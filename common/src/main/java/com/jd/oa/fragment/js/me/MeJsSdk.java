package com.jd.oa.fragment.js.me;


import static android.app.Activity.RESULT_OK;
import static com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_TV;
import static com.jd.oa.router.DeepLink.JDME;
import static com.jd.oa.router.DeepLink.OPEN;

import android.Manifest;
import android.annotation.TargetApi;
import android.app.Activity;
import android.content.ClipData;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Base64;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.jd.jdvideoplayer.live.SmallTV;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.setting.FontScaleUtils;
import com.jd.oa.cache.FileCache;
import com.jd.oa.fragment.H5App;
import com.jd.oa.fragment.WebFragment;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.melib.utils.PermissionUtils;
import com.jd.oa.multitask.SmallTvWindowManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.prefile.OpenFileUtil;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.BitmapUtil;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.CompressType;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.ImageCompressUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.MEScanUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ThreadExtKt;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;
import com.yu.bundles.album.MaeAlbum;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.ShareParam;
import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * JavaScript调用Native
 */
@SuppressWarnings({"unused", "ResultOfMethodCallIgnored"})
public class MeJsSdk {
    public static final int IMAGE_COMPRESS_SIZE = 1024 * 1024;
    public static final int IMAGE_MAX_WIDTH = 3072;
    public static final int IMAGE_MAX_HEIGHT = 3072;
    public static final int IMAGE_COMPRESS_QUALITY = 70;
    /**
     * 扫描结果返回
     */
    private static final int REQUEST_CODE_FOR_SCAN = 88;
    /**
     * 系统相机拍照
     */
    private static final int REQUEST_CODE_FOR_CAPTURE = 92;
    private static final int REQUEST_CODE_FOR_CAPTURE_VIDEO = 922;
    private static final int REQUEST_CODE_FOR_CAPTURE_VIDEO_CUSTOM = 923;
    private static final int REQUEST_CODE_FOR_GALLERY = 93;
    /**
     * 扫描结果返回
     */
    private static final int REQUEST_CODE_FOR_THIRD = 89;
    private static final int REQUEST_CODE_FOR_TAKE_PHOTO_INVOICE = 91;
    /**
     * 打开相机
     */
    private static final int REQUEST_CODE_FOR_TAKE_PHOTO = 90;
    /**
     * 扫描回调JS方法
     */
    private static final int REQUEST_CODE_FOR_THIRD_CALLBACK = 94;
    /**
     * joymeeting
     */
    private static final int REQUEST_CODE_FOR_JOY_MEETING = 95;
    private static final int REQUEST_CODE_FOR_GALLERY_MULTIPLE = 96;
    private static final int PERMISSION_CAMERA_TAKE_PHOTO = 1;
    private static final int PERMISSION_CAMERA_INVOICE = 2;
    private static final int PERMISSION_CAPTURE = 3;
    private static final int PERMISSION_GALLERY = 4;
    /**
     * 兼容android4.4选择文件上传
     */
    private final int KITKAT_RESULTCODE = 58;
    /**
     * 兼容android5 以上棒棒糖
     */
    private final int FOR_LOLLIPOP_RESULTCODE = 60;
    private final int FILECHOOSER_RESULTCODE = 55;
    /**
     * 定位服务
     */
    private SosoLocationService sosoLocationService;
    /**
     * webview 上传文件回调《以支持android4.4，4.4 特殊接入，需参考文档》
     */
    private ValueCallback<Uri> mUploadMessage;
    /**
     * for android 5.0文件上传
     */
    private ValueCallback<Uri[]> android5Upload;
    /**
     * 拍照图片路径
     */
    private String mCameraFilePath;
    private Uri mCaptureUri;
    private Uri mCaptureUriVideo;//
    private Handler mainHandler = new Handler(Looper.getMainLooper());
    /***
     * 是否合法的第三方应用，不合法，敏感的js调用原始方法均不能使用
     */
    private boolean mIsVerifyThirdApp;
    private int mTakePhotoType = 0;
    private String mOpenInvoiceCameraParam;
    /**
     * 拍照图片文件
     */
    private File mCaptureFile;
    private File mCaptureVideoFile;//
    private File mCompressedFile;
    /**
     * 临时参数
     */
    private String mTmpTransParam;

    private String mTmpFuncionName;

    private final Activity activity;
    private H5App h5App;

    private IWebPage webPage;

    public MeJsSdk(WebFragment webFragment) {
        mIsVerifyThirdApp = webFragment.isThirdApp();
        activity = AppBase.getTopActivity();
        h5App = webFragment;
        init();
    }

    public MeJsSdk(WebFragment2 webFragment) {
        this.webPage = webFragment;
        mIsVerifyThirdApp = webFragment.isThirdApp();
        activity = AppBase.getTopActivity();
        h5App = webFragment;
        init();
    }

    public MeJsSdk(android.webkit.WebView webView) { //RnWebView专用
        activity = AppBase.getTopActivity();
        init();
    }

    @TargetApi(Build.VERSION_CODES.KITKAT)
    public static String getPath(final Context context, final Uri uri) {
        final boolean isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;
        // DocumentProvider
        if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
            // ExternalStorageProvider
            if (isExternalStorageDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                final String type = split[0];
                if ("primary".equalsIgnoreCase(type)) {
                    return Environment.getExternalStorageDirectory() + "/" + split[1];
                }
            }
            // DownloadsProvider
            else if (isDownloadsDocument(uri)) {
                final String id = DocumentsContract.getDocumentId(uri);
                final Uri contentUri = ContentUris.withAppendedId(
                        Uri.parse("content://downloads/public_downloads"), Long.valueOf(id));

                return getDataColumn(context, contentUri, null, null);
            }
            // MediaProvider
            else if (isMediaDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                final String type = split[0];

                Uri contentUri = null;
                if ("image".equals(type)) {
                    contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
                } else if ("video".equals(type)) {
                    contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
                } else if ("audio".equals(type)) {
                    contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
                }

                final String selection = "_id=?";
                final String[] selectionArgs = new String[]{
                        split[1]
                };

                return getDataColumn(context, contentUri, selection, selectionArgs);
            }
        }
        // MediaStore (and general)
        else if ("content".equalsIgnoreCase(uri.getScheme())) {
            return getDataColumn(context, uri, null, null);
        }
        // File
        else if ("file".equalsIgnoreCase(uri.getScheme())) {
            return uri.getPath();
        }

        return null;
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is MediaProvider.
     * http://stackoverflow.com/a/20559175
     */
    private static boolean isMediaDocument(Uri uri) {
        return "com.android.providers.media.documents".equals(uri.getAuthority());
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is ExternalStorageProvider.
     */
    private static boolean isExternalStorageDocument(Uri uri) {
        return "com.android.externalstorage.documents".equals(uri.getAuthority());
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is DownloadsProvider.
     */
    private static boolean isDownloadsDocument(Uri uri) {
        return "com.android.providers.downloads.documents".equals(uri.getAuthority());
    }

    /**
     * Get the value of the data column for this Uri. This is useful for
     * MediaStore Uris, and other file-based ContentProviders.
     *
     * @param context       The context.
     * @param uri           The Uri to query.
     * @param selection     (Optional) Filter used in the query.
     * @param selectionArgs (Optional) Selection arguments used in the query.
     * @return The value of the _data column, which is typically a file path.
     * http://stackoverflow.com/a/20559175
     */
    private static String getDataColumn(Context context, Uri uri, String selection,
                                        String[] selectionArgs) {
        Cursor cursor = null;
        final String column = "_data";
        final String[] projection = {column};
        try {
            cursor = context.getContentResolver().query(uri, projection, selection, selectionArgs, null);
            if (cursor != null && cursor.moveToFirst()) {
                final int column_index = cursor.getColumnIndexOrThrow(column);
                return cursor.getString(column_index);
            }
        } finally {
            if (cursor != null)
                cursor.close();
        }
        return null;
    }

    public void setIsVerifyThirdApp(boolean isVerifyThirdApp) {
        mIsVerifyThirdApp = isVerifyThirdApp;
    }

    private void init() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        mCaptureFile = new File(FileCache.getInstance().getImageCacheFile(), "capture.jpg");
        mCaptureVideoFile = new File(FileCache.getInstance().getImageCacheFile(), "video.3pg");//
        mCompressedFile = new File(FileCache.getInstance().getImageCacheFile(), "compressed.jpg");
        mCaptureUri = CategoriesKt.getFileUri(activity, mCaptureFile);
        mCaptureUriVideo = CategoriesKt.getFileUri(activity, mCaptureVideoFile);//
    }

    private void clickData(String method, Map<String, String> params) {
        try {
            HashMap<String, String> param = new HashMap<>();
            String appId = null;
            if (h5App != null) {
                appId = h5App.getCurrentAppId();
            }
            if (appId == null) {
                appId = "";
            }
            param.put("appId", appId);
            String url = null;
            if (webPage != null) {
                url = webPage.getUrl();
            }
            if (url == null) {
                url = "";
            }
            param.put("url", url);
            if (params == null) {
                params = new HashMap<>();
            }
            String jsonStr = "";
            try {
                Gson gson = new Gson();
                jsonStr = gson.toJson(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            param.put("params", jsonStr);
            param.put("erp", PreferenceManager.UserInfo.getUserName());
            if (method == null) {
                method = "";
            }
            param.put("method", method);
            JDMAUtils.clickEvent("", JDMAConstants.js_sdk_old_call_native, param);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 获取应用版本信息 返回格式：

    @JavascriptInterface
    public String isInner() {
        clickData("isInner", null);
        if (MyPlatform.sIsInner) {
            return "1";
        } else {
            return "0";
        }
    }

    @JavascriptInterface
    public void share(String title, String h5Url, String imgUrl, String content) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("title", title);
        params.put("h5Url", h5Url);
        params.put("imgUrl", imgUrl);
        params.put("content", content);
        clickData("share", params);
        if (StringUtils.isEmptyWithTrim(title)) {
            if (webPage != null) {
                title = webPage.getTitle();
            }
        }
        if (StringUtils.isEmptyWithTrim(h5Url)) {
            if (webPage != null) {
                h5Url = webPage.getUrl();
            }
        }
        h5Url = h5Url.startsWith("http") ? h5Url : "http://" + h5Url;
        if (StringUtils.isEmptyWithTrim(content)) {
            content = "";
        }
        Sharing.from(activity)
                .params(new ShareParam.Builder()
                        .title(title)
                        .content(content)
                        .url(h5Url).build())
                .show();
    }

    @JavascriptInterface
    public void goBack() {
        // 回退
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("goBack", null);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (activity != null && !activity.isFinishing() && !activity.isFinishing()) {
                    activity.onBackPressed();      // 避免activity状态丢失
                }
            }
        });
    }

    @JavascriptInterface
    public String getUserName() {
//        if (mIsVerifyThirdApp) {
        clickData("getUserName", null);
        return PreferenceManager.UserInfo.getUserName();
//        }
//        return "";
    }

    // 针对4.4图片上传
    @JavascriptInterface
    public void showPicker() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("showPicker", null);
        if (mIsVerifyThirdApp) {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    activity.startActivityForResult(createDefaultOpenableIntent(), KITKAT_RESULTCODE);
                }
            });
        }
    }

    /**
     * @return {"versionCode" : "19", "versionName" : "1.3.2", "navHeight" : "96"}
     */
    @JavascriptInterface
    public String getAppVersionInfo() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return "{}";
        }
        clickData("getAppVersionInfo", null);
        if (mIsVerifyThirdApp && activity != null) {
            int versinCode = DeviceUtil.getLocalVersionCode(activity);
            String versionName = DeviceUtil.getLocalVersionName(activity);
            int titleHeight = DeviceUtil.getActionBarHeight(activity);
            JSONObject json = new JSONObject();
            try {
                json.put("versionCode", versinCode + "");
                json.put("versionName", versionName + "");
                json.put("navHeight", titleHeight + "");
                json.put("version", versionName);
            } catch (Exception e) {
                return "{}";
            }
            return json.toString();
        }
        return "{}";
    }

    /**
     * 根据包名判断app是否安装
     *
     * @return {"result" : "0" | "1"} 0 表示没有安装，1表示安装了
     */
    @JavascriptInterface
    public String isApkInstalled(String packageName) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return "{}";
        }
        Map<String, String> params = new HashMap<>();
        params.put("packageName", packageName);
        clickData("isApkInstalled", params);
        if (mIsVerifyThirdApp) {
            JSONObject json = new JSONObject();
            try {
                if (DeviceUtil.isApkInstalled(activity, packageName)) {
                    json.put("result", "1");
                } else {
                    json.put("result", "0");
                }
            } catch (Exception e) {
                return "{}";
            }
            return json.toString();
        }
        return "{}";
    }

    /**
     * 重新载入
     */
    @JavascriptInterface
    public void reload() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (webPage == null) return;
        clickData("reload", null);
        if (ThreadExtKt.isMainThread()) {
            webPage.initData(webPage.getUrl());
            webPage.clearHistory();
        } else {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (activity == null || webPage == null) return;
                    webPage.initData(webPage.getUrl());
                    webPage.clearHistory();
                }
            });
        }
    }

    @JavascriptInterface
    public void qrScan() {
        final Activity top = AppBase.getTopActivity();
        if (top == null || top.isFinishing() || top.isDestroyed()) {
            return;
        }
        clickData("qrScan", null);
        if (mIsVerifyThirdApp) {
            PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_camera_scan), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
                    top.startActivityForResult(intent, REQUEST_CODE_FOR_SCAN);
                }

                @Override
                public void denied(List<String> deniedList) {
                }
            }, Manifest.permission.CAMERA);
        }
    }

    /**
     * 使用原生的方式来定位，获取火星坐标
     */
    @JavascriptInterface
    public String reqHuoxingLocation() {
        clickData("reqHuoxingLocation", null);
        final JSONObject json = new JSONObject();
        final CountDownLatch latch = new CountDownLatch(1);// 等待回调执行完成，会有界面卡顿
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                reqLocation(json, latch, false);
            }
        });
        try {
            if (!latch.await(5, TimeUnit.SECONDS)) {// 规定时间内没有完成,停止定位
                if (sosoLocationService != null) {
                    sosoLocationService.stopLocation();
                    sosoLocationService = null;
                }
            }
        } catch (Exception ignored) {
        }
        return json.toString();
    }

    /**
     * 获取地球坐标
     */
    @JavascriptInterface
    public String reqEarthLocation() {
        clickData("reqEarthLocation", null);
        final JSONObject json = new JSONObject();
        final CountDownLatch latch = new CountDownLatch(1);// 等待回调执行完成
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                reqLocation(json, latch, true);
            }
        });
        try {
            if (!latch.await(5, TimeUnit.SECONDS)) {// 规定时间内没有完成,停止定位
                if (sosoLocationService != null) {
                    sosoLocationService.stopLocation();
                    sosoLocationService = null;
                }
            }
        } catch (Exception ignored) {
        }
        return json.toString();
    }

    @JavascriptInterface // h5全屏的
    public void notifyVideoEnd() {
        clickData("notifyVideoEnd", null);
        if (webPage != null) {
            webPage.fullScreen();
        }
    }

    /**
     * @return "1"是wifi，"0"不是wifi
     */
    @JavascriptInterface
    public String isWifi() {
        clickData("isWifi", null);
        final Activity top = AppBase.getTopActivity();
        if (top != null && !top.isFinishing() && !top.isDestroyed()) {
            ConnectivityManager connectivityManager = (ConnectivityManager) top
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetInfo = null;
            if (connectivityManager != null) {
                activeNetInfo = connectivityManager.getActiveNetworkInfo();
            }
            if (activeNetInfo != null
                    && activeNetInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                return "1";
            }
        }
        return "0";
    }

    // 打开咚咚聊天
    @JavascriptInterface
    public void openTimline(final String erp) {
        Map<String, String> params = new HashMap<>();
        params.put("erp", erp);
        clickData("openTimline", params);
        final Activity top = AppBase.getTopActivity();
        if (top != null && !top.isFinishing() && !top.isDestroyed() && StringUtils.isNotEmptyWithTrim(erp)) {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    AppBase.iAppBase.showChattingActivity(top, erp);
                }
            });
        }
    }

    //没有推荐账号时，绑定京东钱包成功
    @JavascriptInterface
    public void onWalletBindSuccess() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("onWalletBindSuccess", null);
        activity.setResult(RESULT_OK);
        activity.finish();
    }

    /**
     * 调用内部模块 2017/01/09 11:18
     *
     * @param appId      应用Id
     * @param appAddress 应用地址
     * @param appType    类型
     * @param appName    app名称
     * @param bizParam   透传参数
     */
    @JavascriptInterface
    public void openModule(String appId, String appAddress, String appType, String appName, String bizParam) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (!checkSmallTVAvailable(appId)) return;
        Map<String, String> params = new HashMap<>();
        params.put("appId", appId);
        params.put("appAddress", appAddress);
        params.put("appType", appType);
        params.put("appName", appName);
        params.put("bizParam", bizParam);
        clickData("openModule", params);
        if (activity.getIntent().hasExtra("modleIsOpen")) {
            Intent data = new Intent();
            data.putExtra("bizParam", bizParam);
            activity.setResult(RESULT_OK, data);
            activity.finish();
        } else {
            AppUtils.openFunctionByPlugIn(activity,
                    appId,
                    appName,
                    appType,
                    appAddress,
                    "", null,
                    "", "", null,
                    bizParam, null, null, null, null, null, null);//打开应用多添加了isnativehead字段
        }
    }

    public static boolean checkSmallTVAvailable(String appId) {
        //201705190031 点播  10046直播
        if (appId.equals("10046") || appId.equals("201705190031")) {
            //如果打开小MEtv播放页时小窗在播放先关闭小窗
            if (SmallTvWindowManager.getInstance(AppBase.getAppContext()).isShowing()) {
                SmallTvWindowManager.getInstance(AppBase.getAppContext()).close(null);
                SmallTvWindowManager.getInstance(AppBase.getAppContext()).setClosed(true);
//                return false;
            }
//            if (JMAudioCategoryManager.getInstance().canSetAudioCategory(JME_AUDIO_CATEGORY_ME_TV)) {
            final JMAudioCategoryManager.JMEAudioCategorySet jmeAudioCategorySet = JMAudioCategoryManager.getInstance().setAudioCategory(JME_AUDIO_CATEGORY_ME_TV);
            if (jmeAudioCategorySet.available) {
                SmallTV.getInstance().unRegister();
                SmallTV.getInstance().register(new SmallTV.OnSmallTvPlayerListener() {
                    @Override
                    public void onPlayFinished(boolean isShowFloatView) {
                        if (JMAudioCategoryManager.getInstance().getCurrentAudioCategory() == JME_AUDIO_CATEGORY_ME_TV) {
                            if (!isShowFloatView) {//正常关闭播放页  不是缩小窗
                                JMAudioCategoryManager.getInstance().releaseSmallTv();
                                if (AppBase.getAppContext() != null) {//如果小窗还在占用音频 就释放
                                    String floatWindowSecret = SmallTvWindowManager.getInstance(AppBase.getAppContext()).getAudioSecret();
                                    if (!TextUtils.isEmpty(floatWindowSecret)) {
//                                        JMAudioCategoryManager.getInstance().releaseSmallTv();
                                        SmallTvWindowManager.getInstance(AppBase.getAppContext()).setAudioSecret("");
                                    }
                                }
                            } else if (AppBase.getAppContext() != null) {//缩小窗时传secret 给小窗
                                SmallTvWindowManager.getInstance(AppBase.getAppContext()).setAudioSecret(jmeAudioCategorySet.secret);
                            }
                        }
                    }
                });
            }
            return jmeAudioCategorySet.available;
//            } else return false;
        }
        return true;
    }

    @JavascriptInterface
    public String getFile(final String path) {
        Map<String, String> params = new HashMap<>();
        params.put("path", path);
        clickData("getFile", params);
        try {
            File outFile = new File(path);
            FileInputStream inputFile;
            inputFile = new FileInputStream(outFile);
            byte[] buffer = new byte[(int) outFile.length()];
            inputFile.read(buffer);
            inputFile.close();
            String encodedString = Base64.encodeToString(buffer, Base64.DEFAULT);
            return "data:image/png;base64," + encodedString + "";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @JavascriptInterface
    public void getFile2(final String path) {
        Map<String, String> params = new HashMap<>();
        params.put("path", path);
        clickData("getFile2", params);
        Observable.create(new ObservableOnSubscribe<String>() {

                    @Override
                    public void subscribe(ObservableEmitter<String> e) throws Exception {
                        String base64 = getFile(path);
                        e.onNext(base64);
                        e.onComplete();
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<String>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                    }

                    @Override
                    public void onNext(String s) {
                        tryLoadJs("javascript:callBackGetFile2('" + s + "')");
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                    }
                });
    }

    @JavascriptInterface
    public void takePhoto() {
        clickData("takePhoto", null);
        checkPermissionAndTakePhoto(AppBase.TYPE_CAMERA_END);
    }

    @JavascriptInterface
    public void takePhotoFront() {
        clickData("takePhotoFront", null);
        checkPermissionAndTakePhoto(AppBase.TYPE_CAMERA_FRONT);
    }

    /**
     * 第三方通用扫一扫 2017/01/09 11:18
     *
     * @param transParam 透传参数
     */
    @JavascriptInterface
    public void openScanForThirdParty(final String transParam) {
        if (mIsVerifyThirdApp) {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            Map<String, String> params = new HashMap<>();
            params.put("transParam", transParam);
            clickData("openScanForThirdParty", params);
            PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_camera_scan), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    mTmpTransParam = transParam;
                    Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
                    activity.startActivityForResult(intent, REQUEST_CODE_FOR_THIRD);
                }

                @Override
                public void denied(List<String> deniedList) {
                }
            }, Manifest.permission.CAMERA);
        }
    }

    /**
     * 第三方通用扫一扫 JS回调 2019/02/15 17:17
     *
     * @param callbackFunName
     * @param transParam      透传参数
     * @param
     */
    @JavascriptInterface
    public void openScanForThirdPartyJsCallback(final String callbackFunName, final String transParam) {
        if (mIsVerifyThirdApp) {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            Map<String, String> params = new HashMap<>();
            params.put("callbackFunName", callbackFunName);
            params.put("transParam", transParam);
            clickData("openScanForThirdPartyJsCallback", params);
            PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_camera_scan), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    mTmpTransParam = transParam;
                    mTmpFuncionName = callbackFunName;
                    Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
                    activity.startActivityForResult(intent, REQUEST_CODE_FOR_THIRD_CALLBACK);
                }

                @Override
                public void denied(List<String> deniedList) {
                }
            }, Manifest.permission.CAMERA);
        }
    }

    /**
     * 发票拍照界面
     *
     * @param params 界面选项
     *               {
     *               showNoInvoice : true,        //是否显示无发票按钮
     *               showTicketHolder : true     //是否显示票价按钮
     *               }
     */
    @JavascriptInterface
    public void openInvoiceCamera(final String params) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("params", params);
        clickData("openInvoiceCamera", paramMap);
        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        openInvoiceCamera2(params);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                }, Manifest.permission.CAMERA);
            }
            mOpenInvoiceCameraParam = params;
        } else {
            openInvoiceCamera2(params);
        }
    }

    /**
     * 跳转Deeplink
     *
     * @param deepLink deeplink链接
     * @param title    不知道干啥的，不是我定义的
     */
    @JavascriptInterface
    public void openDPModule(String deepLink, String title) {
        if (TextUtils.isEmpty(deepLink)) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("deepLink", deepLink);
        paramMap.put("title", title);
        clickData("openDPModule", paramMap);
        Router.build(deepLink).go(activity, new RouteNotFoundCallback(activity));
    }

    @JavascriptInterface
    public void openCamera() {//调用相机拍照的功能
        //检测拍照权限
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("openCamera", null);
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        openCamera2();
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
        } else {
            openCamera2();
        }
    }

    @JavascriptInterface
    public void openCameraVideo() {//调用相机录像的功能
        //检测权限
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("openCameraVideo", null);
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_video), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        openCameraVideo2();
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.RECORD_AUDIO);
            }
        } else {
            openCameraVideo2();

        }
    }

    //传参数的自定义调用录像功能
    @JavascriptInterface
    public void openCameraVideoCustom(final int cameraType, final int videoQuality, final int durationLimit) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("cameraType", String.valueOf(cameraType));
        paramMap.put("videoQuality", String.valueOf(videoQuality));
        paramMap.put("durationLimit", String.valueOf(durationLimit));
        clickData("openCameraVideoCustom", paramMap);
        //检测权限
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_video), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        openCameraVideoCustom2(cameraType, videoQuality, durationLimit);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.RECORD_AUDIO);
            }
        } else {
            openCameraVideoCustom2(cameraType, videoQuality, durationLimit);
        }
    }

    @JavascriptInterface
    public void openGallery() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("openGallery", null);
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_read_storage_gallery), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        openGallery2();
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                }, Manifest.permission.READ_EXTERNAL_STORAGE);
            }
        } else {
            openGallery2();
        }
    }

    @JavascriptInterface
    public void openGalleryMultiple(final String maxNum) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("maxNum", maxNum);
        clickData("openGalleryMultiple", paramMap);
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_read_storage_gallery), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        openGalleryMultiple2(maxNum);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                }, Manifest.permission.READ_EXTERNAL_STORAGE);
            }
        } else {
            openGalleryMultiple2(maxNum);
        }
    }

    @JavascriptInterface
    public void scanQRCodeClose() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("scanQRCodeClose", null);
        activity.finish();
    }

    @JavascriptInterface
    public void landscape() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("landscape", null);
        if (activity.getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
    }

    @JavascriptInterface
    public void portrait() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        clickData("portrait", null);
        if (activity.getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    @JavascriptInterface
    public void getJDPinToken(String url) {
        if (webPage == null) {
            return;
        }
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("url", url);
        clickData("getJDPinToken", paramMap);
        if (TextUtils.isEmpty(url)) {
            url = webPage.getUrl();
        }

        NetWorkManager.getJDPinToken(null, new SimpleRequestCallback<String>(activity, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                tryLoadJs("javascript:callBackGetJDPinToken('','0')");
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String result = info.result;
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    String redirectUrl = jsonObject.optJSONObject("content").optString("url");
                    String isBind = jsonObject.optJSONObject("content").optString("isBind");
                    if (TextUtils.isEmpty(isBind)) {
                        isBind = "0";
                    }
                    //callback不进行base64，urlencode后的值也会被urldecode一遍
                    String base64 = Base64.encodeToString(redirectUrl.getBytes(), Base64.DEFAULT);
                    tryLoadJs("javascript:callBackGetJDPinToken('" + base64 + "','" + isBind + "')");
                } catch (Exception e) {
                    e.printStackTrace();
                    tryLoadJs("javascript:callBackGetJDPinToken('','0')");
                }
            }
        }, url);
    }

    @JavascriptInterface
    public String getFontScale() {
        clickData("getFontScale", null);
        return String.valueOf(FontScaleUtils.getScaleIndex());
    }

    @JavascriptInterface
    public void updatestatusbatch(String openid, final String status, final String invoiceList) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("openid", openid);
        paramMap.put("status", status);
        paramMap.put("invoiceList", invoiceList);
        clickData("updatestatusbatch", paramMap);

        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("openid", openid);
        hashMap.put("invoice_list", invoiceList);
        hashMap.put("reimburse_status", status);
        NetWorkManager.updateInvoiceStatus(null, new SimpleRequestCallback<String>(activity, true) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                String js = String.format("javascript:CallbackUpdatestatusbatch('1','%s', '%s')", status, invoiceList);
                tryLoadJs(js);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    String js = String.format("javascript:CallbackUpdatestatusbatch('0', '%s', '%s')", status, invoiceList);
                    tryLoadJs(js);
                } else {
                    String js = String.format("javascript:CallbackUpdatestatusbatch('1','%s', '%s')", status, invoiceList);
                    tryLoadJs(js);
                }
            }
        }, hashMap);
    }

    /**
     * 发票票夹选择完成后回调
     *
     * @param invoiceList
     */
    @JavascriptInterface
    public void selectInvoiceList(String invoiceList) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("invoiceList", invoiceList);
        clickData("selectInvoiceList", paramMap);
        Intent intent = new Intent();
        intent.putExtra("invoiceList", invoiceList);
        activity.setResult(RESULT_OK, intent);
        activity.finish();
    }

    private Intent createDefaultOpenableIntent() {
        Intent innerIntent = new Intent(Intent.ACTION_GET_CONTENT); // "android.intent.action.GET_CONTENT"
        String IMAGE_UNSPECIFIED = "image/*";
        innerIntent.setType(IMAGE_UNSPECIFIED); // 查看类型
        return Intent.createChooser(innerIntent, null);
    }

    /**
     * 获取定位结果
     *
     * @param isEarth 是否地理坐标
     */
    private void reqLocation(final JSONObject json, final CountDownLatch latch, boolean isEarth) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (sosoLocationService != null) {
            sosoLocationService.stopLocation();
            sosoLocationService = null;
        }

        sosoLocationService = new SosoLocationService(activity);
        sosoLocationService.setCoordinateType(isEarth);
        sosoLocationService.startLocationWithCheck();  // 定位
        sosoLocationService.setLocationChangedListener(new SosoLocationChangeInterface() {
            @Override
            public void onLocated(String lat, String lng, String name, String cityName) {
                try {
                    json.put("lat", lat);
                    json.put("lng", lng);
                    json.put("name", name);
                } catch (Exception ignored) {
                } finally {
                    latch.countDown();
                }
            }

            @Override
            public void onFailed() {
                ToastUtils.showInfoToast(R.string.me_locatin_fail);
                latch.countDown();
            }
        });
    }

    private void checkPermissionAndTakePhoto(final int type) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            mTakePhotoType = type;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        takePhoto2(type);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                }, Manifest.permission.CAMERA);
            }
        } else {
            takePhoto2(type);
        }
    }

    /**
     * 打开自定义相机
     */
    private void takePhoto2(int type) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Camera).getIntent(activity);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        intent.putExtra(AppBase.KEY_TYPE_CAMERA, type);
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_TAKE_PHOTO);
    }

    /**
     * 打开发票拍照界面
     */
    private void openInvoiceCamera2(String params) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        JSONObject obj = null;
        try {
            if (params != null) {
                obj = new JSONObject(params);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_ReimbursementCreate).getIntent(activity);
        if (obj != null) {
            intent.putExtra(AppBase.ARG_SHOW_NO_INVOICE, obj.optBoolean("showNoInvoice", true));
            intent.putExtra(AppBase.ARG_SHOW_TICKET_HOLDER, obj.optBoolean("showTicketHolder", true));
            intent.putExtra(AppBase.ARG_SHOW_ELECTRONIC_TICKET_HOLDER, obj.optBoolean("showElectronicTicketHolder", false));
        }
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_TAKE_PHOTO_INVOICE);
    }

    /**
     * 打开系统相机
     */
    private void openCamera2() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUri);
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_CAPTURE);
    }

    private void tryLoadJs(String js) {
        if (webPage != null && !TextUtils.isEmpty(js)) {
            webPage.loadUrl(js);
        }
    }

    /**
     * 打开系统相机录像
     */
    private void openCameraVideo2() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUriVideo);

//        //根据不同版本打开前置摄像头
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && Build.VERSION.SDK_INT < Build.VERSION_CODES.O){
//            intent.putExtra("android.intent.extras.CAMERA_FACING", CameraCharacteristics.LENS_FACING_FRONT);
//        }else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
//            intent.putExtra("android.intent.extras.CAMERA_FACING", CameraCharacteristics.LENS_FACING_FRONT);
//            intent.putExtra("android.intent.extra.USE_FRONT_CAMERA", true);
//        }else{
//            intent.putExtra("android.intent.extras.CAMERA_FACING", 1);
//        }

        //设置优先打开前置还是后置
        intent.putExtra("android.intent.extras.CAMERA_FACING", 1);
        //设置视频录制的最长时间
        intent.putExtra(MediaStore.EXTRA_DURATION_LIMIT, 60);
        //设置视频录制的画质
        intent.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 1);

        activity.startActivityForResult(intent, REQUEST_CODE_FOR_CAPTURE_VIDEO);
    }

    /**
     * 打开系统相机录像，可传参数
     */
    private void openCameraVideoCustom2(int cameraType, int videoQuality, int durationLimit) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
        intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUriVideo);
//            默认参数
        int cameraTypeDefault = 1;
        int videoQualityDefault = 1;
        int durationLimitDefault = 60;
        if (cameraType == 0) {
            cameraTypeDefault = cameraType;
        }
        if (videoQuality >= 0 & videoQuality < 1) {
            videoQualityDefault = videoQuality;
        }
        if (durationLimit >= 0) {
            durationLimitDefault = durationLimit;
        }
        //设置优先打开前置还是后置
        intent.putExtra("android.intent.extras.CAMERA_FACING", cameraTypeDefault);
        //设置视频录制的最长时间
        intent.putExtra(MediaStore.EXTRA_DURATION_LIMIT, durationLimitDefault);
        //设置视频录制的画质
        intent.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, videoQualityDefault);
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_CAPTURE_VIDEO_CUSTOM);
    }


    /**
     * 打开相册
     */
    private void openGallery2() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_GALLERY);
    }

    /**
     * 打开相册
     */
    private void openGalleryMultiple2(String maxNum) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            GalleryProvider mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
            mGalleryProvider.openGallery(activity, Integer.parseInt(maxNum), REQUEST_CODE_FOR_GALLERY_MULTIPLE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (requestCode == PERMISSION_CAMERA_TAKE_PHOTO) {
            if (grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                takePhoto2(mTakePhotoType);
            } else {
                ToastUtils.showToast(R.string.me_camera_permission);
            }
        } else if (requestCode == PERMISSION_CAMERA_INVOICE) {
            if (grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                openInvoiceCamera(mOpenInvoiceCameraParam);
            } else {
                ToastUtils.showToast(R.string.me_camera_permission);
            }
        } else if (requestCode == PERMISSION_CAPTURE) {
            if (grantResults.length == 2 && grantResults[0] == PackageManager.PERMISSION_GRANTED && grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                openCamera2();
            } else {
                ToastUtils.showToast(R.string.me_camera_permission);
            }
        } else if (requestCode == PERMISSION_GALLERY) {
            if (grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                openGallery2();
            }
        } else {
            PermissionUtils.requestResult(requestCode, permissions, grantResults, new Runnable() {
                @Override
                public void run() {
                    Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
                    activity.startActivityForResult(intent, REQUEST_CODE_FOR_SCAN);
                }
            }, null);
        }
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (requestCode == FILECHOOSER_RESULTCODE) {
            if (null == mUploadMessage)
                return;
            Uri result = data == null || resultCode != RESULT_OK ? null : data.getData();
            if (result == null && data == null
                    && resultCode == RESULT_OK) {
                File cameraFile = new File(mCameraFilePath);
                if (cameraFile.exists()) {
//                    result = Uri.fromFile(cameraFile);
                    result = OpenFileUtil.getUri(activity, cameraFile);
                    // Broadcast to the media scanner that we have a new photo
                    // so it will be added into the gallery for the user.
                    if (activity != null)
                        activity.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, result));
                }
            }
            mUploadMessage.onReceiveValue(result);
            mUploadMessage = null;
        } else if (requestCode == KITKAT_RESULTCODE && data != null) {
            // 针对android4.4手动上传文件
            Uri result = data.getData();
            String path = getPath(activity, result);
            assert path != null;
            File selectedFile = new File(path);
            uploadFile(selectedFile);
        } else if (requestCode == FOR_LOLLIPOP_RESULTCODE && null != android5Upload) {
            try {
                Uri[] results = null;
                if (resultCode == RESULT_OK && data != null) {
                    String dataString = data.getDataString();
                    ClipData clipData = data.getClipData();

                    if (clipData != null) {
                        results = new Uri[clipData.getItemCount()];
                        for (int i = 0; i < clipData.getItemCount(); i++) {
                            ClipData.Item item = clipData.getItemAt(i);
                            results[i] = item.getUri();
                        }
                    }
                    if (dataString != null) {
                        results = new Uri[]{Uri.parse(dataString)};
                    }
                }

                android5Upload.onReceiveValue(results);
                // for android 5.0
                android5Upload = null;
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (REQUEST_CODE_FOR_SCAN == requestCode) {
            // 扫描返回
            if (null != data) {
                String result = data.getStringExtra("result");
                if (!TextUtils.isEmpty(result)) {
                    analyseResult(result);
                }
            }
        } else if (REQUEST_CODE_FOR_THIRD == requestCode) { // 第三方扫码回调
            if (null != data) {
                String result = data.getStringExtra("result");
                if (!TextUtils.isEmpty(result)) {
                    tryLoadJs("javascript:thirdPartyScanCallBack('" + result + "','" + mTmpTransParam + "')");
                }
            }
        } else if (REQUEST_CODE_FOR_THIRD_CALLBACK == requestCode) { // 第三方扫码回调JS方法
            if (null != data) {
                String result = data.getStringExtra("result");
                if (!TextUtils.isEmpty(result)) {
                    tryLoadJs("javascript:" + mTmpFuncionName + "('" + result + "','" + mTmpTransParam + "')");
                }

            }
        } else if (REQUEST_CODE_FOR_TAKE_PHOTO == requestCode) {
            if (null != data) {
                String result = data.getStringExtra("result");
                if (!TextUtils.isEmpty(result)) {
                    tryLoadJs("javascript:callBackOpenCamera('" + result + "')");
                }

            }
        } else if (REQUEST_CODE_FOR_TAKE_PHOTO_INVOICE == requestCode) {
            if (resultCode == RESULT_OK && data != null) {
                String result = data.getStringExtra(AppBase.RESULT_PICTURE);
                String tickets = data.getStringExtra("tickets");
                if (!TextUtils.isEmpty(result)) {
                    tryLoadJs("javascript:callbackOpenInvoiceCamera('" + result + "')");
                } else if (!TextUtils.isEmpty(tickets)) {
                    tryLoadJs("javascript:callBackInvoicebatchList('" + tickets + "')");
                }
            }
        } else if (REQUEST_CODE_FOR_CAPTURE == requestCode) {
            if (resultCode == RESULT_OK) {
                BitmapUtil.compress(mCaptureFile, mCompressedFile, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT, IMAGE_COMPRESS_QUALITY);
                try {
                    int[] size = new int[2];
                    BitmapUtil.getImageSize(mCompressedFile, size);
                    if (size[0] > size[1]) {
                        Bitmap rotateBitmap = BitmapUtil.rotateBitmapByDegree(BitmapFactory.decodeFile(mCompressedFile.getPath()), 90);
                        BitmapUtil.save(rotateBitmap, mCompressedFile);
                    }
                } catch (IOException | OutOfMemoryError e) {
                    e.printStackTrace();
                }
                tryLoadJs("javascript:callBackOpenCamera('" + mCompressedFile.getPath() + "')");
            }
        } else if (REQUEST_CODE_FOR_CAPTURE_VIDEO == requestCode) {//录像
            if (resultCode == RESULT_OK) {
                tryLoadJs("javascript:callBackOpenCameraVideo('" + mCaptureVideoFile.getPath() + "')");
            }
        } else if (REQUEST_CODE_FOR_CAPTURE_VIDEO_CUSTOM == requestCode) {//录像自定义
            if (resultCode == RESULT_OK) {
                tryLoadJs("javascript:callBackOpenCameraVideoCustom('" + mCaptureVideoFile.getPath() + "')");
            }
        } else if (REQUEST_CODE_FOR_GALLERY == requestCode) {
            if (resultCode == RESULT_OK) {
                Uri selected = data.getData();
                String path = getPath(activity, selected);
                tryLoadJs("javascript:callBackOpenCamera('" + path + "')");
            }
        } else if (REQUEST_CODE_FOR_JOY_MEETING == requestCode) {
            AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
                @Override
                public void run() {
                    activity.finish();
                }
            }, 500, TimeUnit.MILLISECONDS);
        } else if (REQUEST_CODE_FOR_GALLERY_MULTIPLE == requestCode) {
            if (resultCode == RESULT_OK) {
                final List<String> pList = MaeAlbum.obtainPathResult(data);
                if (CollectionUtil.notNullOrEmpty(pList)) {
                    final ImageCompressUtils mGallery = ImageCompressUtils.INSTANCE;
                    mGallery.reset();
                    mGallery.setType(CompressType.SIZE);
                    mGallery.setSizeValue(IMAGE_COMPRESS_SIZE);
                    final List<Uri> result = new ArrayList<>();
                    File parent = new File(activity.getExternalCacheDir() + "/web");
                    DialogUtils.showLoadDialog((FragmentActivity) activity, activity.getResources().getString(R.string.me_rn_compress_image));
                    for (String p : pList) {
                        mGallery.compressAsync(p, new File(parent, System.currentTimeMillis() + ".jpg").getAbsolutePath(), new Function1<String, Unit>() {
                            @Override
                            public Unit invoke(String s) {
                                result.add(Uri.parse(s));
                                if (result.size() >= pList.size()) {
                                    JSONArray jsonArray = new JSONArray();
                                    for (Uri path : result) {
                                        jsonArray.put(path.toString());
                                    }
                                    tryLoadJs("javascript:callBackOpenGalleryMultiple('" + jsonArray.toString() + "')");
                                    DialogUtils.removeLoadDialog((FragmentActivity) activity);
                                    mGallery.reset();
                                }
                                return null;
                            }
                        });
                    }
                }
            }
        }
    }

    private void uploadFile(File selectedFile) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("upload", selectedFile);
        HttpManager.legacy().upload("http://m.wenba.jd.com/uploader", new HashMap<String, Object>(), fileMap, new SimpleRequestCallback<String>(activity, R.string.me_uploading) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                if (!TextUtils.isEmpty(json)) {
                    tryLoadJs("javascript:androidCallBack('" + json + "')");
                } else {
                    showUploadError();
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                showUploadError();
            }
        });
    }

    private void showUploadError() {
        ToastUtils.showInfoToast(R.string.me_img_uploading_failed);
    }

    // For Android 3.0+
    public void openFileChooser(ValueCallback<Uri> uploadMsg,
                                String acceptType) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (mUploadMessage != null)
            return;
        mUploadMessage = uploadMsg;
        activity.startActivityForResult(createDefaultOpenableIntent(), FILECHOOSER_RESULTCODE);
    }

    //TODO 这里需要测试一下，两个不同内核
    public boolean onShowFileChooser(ValueCallback<Uri[]> filePathCallback,
                                     Intent intent) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return false;
        }
        if (android5Upload != null) {
            android5Upload.onReceiveValue(null);
            android5Upload = null;
        }
        android5Upload = filePathCallback;

        if (intent == null) {
            return false;
        }
        activity.startActivityForResult(intent, FOR_LOLLIPOP_RESULTCODE);
        return true;     // 自己来处理
    }

    /**
     * 将扫码的结果发送到后端
     */
    private void analyseResult(String result) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Map<Integer, String> map = MEScanUtils.checkScanContent(result);
        if (map == null && null != webPage) {
            webPage.initData(result);
            return;
        }

        NetWorkManager.sendScanResult(this, new SimpleRequestCallback<String>(activity, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                if (!TextUtils.isEmpty(json)) {
                    ResponseParser parser = new ResponseParser(json, activity);
                    parser.parse(new ResponseParser.ParseCallbackAdapter() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            super.parseObject(jsonObject);
                            try {
                                String type = jsonObject.getString("type");
                                String msg = jsonObject.getString("msg");
                                if ("0".equals(type)) {        // 弹出消息
                                    ToastUtils.showInfoToast(msg);
                                } else {
                                    if (null != webPage) {
                                        webPage.initData(msg);
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        }, result);
    }

    public void stopLocationService() {
        if (sosoLocationService != null) {
            sosoLocationService.stopLocation();
            sosoLocationService = null;
        }
    }

    public boolean shouldOverrideUrlLoading(String url) {
        Uri uri = Uri.parse(url);
        if (uri.getScheme() != null && uri.getScheme().startsWith("http")) {
            return false;
        } else if (uri.getScheme() != null && uri.getScheme().startsWith("file")) {
            return false;
        } else {
            try {
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return false;
                }
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                if (url.startsWith(DeepLink.OPEN_JOY_MEETING_OLD)) {
                    activity.startActivityForResult(intent, REQUEST_CODE_FOR_JOY_MEETING);
                } else {
                    if (url.startsWith(OPEN) || url.startsWith(JDME)) {
                        Router.build(url).go(activity);
                    } else {
                        activity.startActivity(intent);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        }
    }


    private interface IBase64CallBack {
        void finish(String s);
    }
}