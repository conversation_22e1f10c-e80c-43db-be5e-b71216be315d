package com.jd.oa.fragment;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jme.common.R;
import com.jd.oa.utils.Logger;


import java.lang.reflect.Method;

/**
 * 自带进度,空布局提示,错误信息提示的fragment
 */
public abstract class ProgressFragment extends BaseFragment {

    private final static int PROGRESS_ITEM = 0;
    private final static int EMPTY_ITEM = 1;
    private final static int ERROR_ITEM = 2;
    private final static int REPEAT_ITEM = 3;
    private final static int CONTAINER_ITEM = 4;
    private static final int DELAY_MILLIS = 1000;
    /**
     * Fragment内容
     */
    protected View viewContainer = null;
    /**
     * 没有数据
     */
    private TextView emptyView = null;
    /**
     * 加载错误-- 网络提示
     */
    private TextView errorView = null;
    /**
     * 加载重试
     */
    private LinearLayout repeatLayout;// 重试
    /**
     * 加载中
     */
    private TextView loadInfo;
    private FrameLayout root = null;
    // 当前布局显示状态
    private int lastItem;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (null == viewContainer) {
            throw new NullPointerException("the Container is Null!!!");
        }

        root = (FrameLayout) inflater.inflate(R.layout.jdme_fragment_progress, container, false);
        root.setClickable(true);        // 避免 Fragment事件穿透

        emptyView = (TextView) root.findViewById(R.id.empty_container);
        errorView = (TextView) root.findViewById(R.id.error_container);
        repeatLayout = (LinearLayout) root.findViewById(R.id.repeat_container);
        loadInfo = (TextView) root.findViewById(R.id.load_info);

        // 添加布局对象，添加原来的fragment内容
        root.addView(viewContainer, lastItem = CONTAINER_ITEM,
                new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT));
        return root;
    }

    @Override
    public void onDestroyView() {
        emptyView = null;
        errorView = null;
        repeatLayout = null;
        viewContainer = null;
        super.onDestroyView();
    }

    /**
     * 设置空布局信息
     *
     * @param info
     */
    protected void setEmptyInfo(String info) {
        if (null != emptyView) {
            emptyView.setText(info);
        }
    }

    protected void setEmptyInfo(int resId) {
        if (null != emptyView) {
            emptyView.setText(resId);
        }
    }

    protected void setEmptyIcon(Drawable drawable) {
        if (null != emptyView) {
            emptyView.setCompoundDrawablesWithIntrinsicBounds(null, drawable,
                    null, null);
        }
    }

    protected void setEmptyIcon(int resId) {
        if (null != emptyView) {
            emptyView.setCompoundDrawablesWithIntrinsicBounds(0, resId, 0, 0);
        }
    }

    /**
     * 设置空布局信息
     *
     * @param info
     */
    protected void setErrorInfo(String info) {
        if (null != errorView) {
            errorView.setText(info);
        }
    }

    private void setErrorInfo(int resId) {
        if (null != errorView) {
            errorView.setText(resId);
        }
    }

    protected void setErrorIcon(Drawable drawable) {
        if (null != errorView) {
            errorView.setCompoundDrawablesWithIntrinsicBounds(null, drawable,
                    null, null);
        }
    }

    protected void setErrorIcon(int resId) {
        if (null != errorView) {
            errorView.setCompoundDrawablesWithIntrinsicBounds(0, resId, 0, 0);
        }
    }

    protected void setLoadInfo(int resId) {
        if (null != loadInfo) {
            loadInfo.setText(resId);
        }
    }

    /**
     * 设置显示空布局体
     *
     * @param show true:显示 false:隐藏
     */
    protected void setEmptyShown(boolean animate) {
        setViewShown(EMPTY_ITEM, animate);
    }

    /**
     * 延迟设置空布局体
     *
     * @param animate
     */
    protected void delayShowEmpty(final boolean animate) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                setEmptyShown(animate);
            }
        }, DELAY_MILLIS);
    }

    /**
     * 设置进度装载显示
     */
    protected void setProgressShown(boolean animate) {
        setViewShown(PROGRESS_ITEM, animate);
    }

    /**
     * 设置重试布局是否显示
     *
     * @param show true:显示 false:隐藏
     */
    private void setRepeatShown(boolean animate) {
        setViewShown(REPEAT_ITEM, animate);
    }

    /**
     * 设置指定透明度的进度条
     *
     * @param animate
     */
    protected void setScaleProgressShown(boolean animate) {
        int item = PROGRESS_ITEM;
        lastItem = CONTAINER_ITEM;
        if (null != viewContainer) {
            View showView = root.getChildAt(item);
            View closeView = root.getChildAt(lastItem);
            showView.setVisibility(View.VISIBLE);
            closeView.setVisibility(View.VISIBLE);
            if (animate) {
                showView.animate().setDuration(1000)
                        .setInterpolator(new AccelerateInterpolator())
                        .alpha(0.4f);
            } else {
                showView.clearAnimation();
            }
            lastItem = item;
        }
    }

    /**
     * 延迟设置进度体
     *
     * @param animate
     */
    protected void delayShowProgress(final boolean animate) {
        root.postDelayed(new Runnable() {
            @Override
            public void run() {
                setProgressShown(animate);
            }
        }, DELAY_MILLIS);
    }

    /**
     * 延迟设置进度体
     *
     * @param animate
     */
    protected void delayShowRepeat(final boolean animate) {
        root.postDelayed(new Runnable() {
            @Override
            public void run() {
                setRepeatShown(animate);
            }
        }, DELAY_MILLIS);
    }

    /**
     * 设置错误布局体显示
     *
     * @param show true:显示 false:隐藏
     */
    private void setErrorShown(boolean animate) {
        setViewShown(ERROR_ITEM, animate);
    }

    /**
     * 延迟设置错误布局体
     *
     * @param animate
     */
    protected void delayShowError(final boolean animate) {
        root.postDelayed(new Runnable() {
            @Override
            public void run() {
                setErrorShown(animate);
            }
        }, DELAY_MILLIS);
    }

    /**
     * 设置布局体显示
     *
     * @param show true:显示 false:隐藏
     */
    protected void setContainerShown(boolean animate) {
        setViewShown(CONTAINER_ITEM, animate);
    }

    /**
     * 延迟设置布局体
     *
     * @param animate
     */
    protected void delayShowContainer(final boolean animate) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                setContainerShown(animate);
            }
        }, DELAY_MILLIS);
    }

    protected ViewGroup getRootView() {
        return root;
    }

    /**
     * 设置空布局点击事件
     *
     * @param listener
     */
    protected void setEmptyListener(OnClickListener listener) {
        if (null != emptyView) {
            emptyView.setOnClickListener(listener);
        }
    }

    /**
     * 设置异常局点击事件
     *
     * @param listener
     */
    public void setErrorListener(OnClickListener listener) {
        if (null != errorView) {
            errorView.setOnClickListener(listener);
        }
    }

    protected void setRepeatListener(OnClickListener listener) {
        if (null != repeatLayout) {
            repeatLayout.setOnClickListener(listener);
        }
    }

    public boolean isErrorStatus(int status) {
        return lastItem == status;
    }

    private void setViewShown(int item, boolean animate) {
        try {
            if (null != viewContainer && item != lastItem) {
                View showView = root.getChildAt(item);      // 可能为 null ，catch异常。
                if (showView.getVisibility() == View.VISIBLE) {
                    return;
                }

                View closeView = root.getChildAt(lastItem);
                if (animate) {
                    Animation loadAnimation = AnimationUtils.loadAnimation(getActivity(), android.R.anim.fade_in);
                    showView.startAnimation(loadAnimation);
                } else {
                    showView.clearAnimation();
                }
                closeView.setVisibility(View.GONE);
                showView.setVisibility(View.VISIBLE);
                lastItem = item;
            }
        } catch (Exception e) {

        }
    }

    /** ======================================================
     * ====================================
     * [2015-03-13] better 新想法，执行method方法  start
     * =====================================
     * =======================================================*/
    /**
     * 数据为空时，显示空布局信息
     *
     * @param t        泛型类，指子类
     * @param emptyMsg 界面提示信息
     * @param method   点击空布局执行方法，null 为 不执行
     * @param args     方法参数
     */
    protected <T> void setEmptyViewClickMethod(final T t, Object emptyMsg, final Method method, final Object... args) {
        if (null != emptyView) {
            setEmptyShown(false);
            if (emptyMsg instanceof Integer) {
                emptyView.setText(Integer.valueOf(emptyMsg.toString()));        // 空白信息提示
            } else {
                emptyView.setText(String.valueOf(emptyMsg));
            }

            if (null != method) {
                emptyView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        setProgressShown(false);
                        try {
                            method.setAccessible(true);
                            method.invoke(t, args);
                        } catch (Exception e) {
                            Logger.e(TAG, e.toString());
                            setEmptyShown(false);
                        }
                    }
                });
            }
        }
    }

    /**
     * 无网络时，显示的信息
     *
     * @param t        泛型类，指子类
     * @param emptyMsg 界面提示信息
     * @param method   点击空布局执行方法，null 为 不执行
     * @param args     方法参数
     */
    public <T> void setNoNetViewClickMethod(final T t, int msg, final Method method, final Object... args) {
        if (null != emptyView) {
            setErrorShown(false);
            setErrorInfo(msg);

            if (null != method) {
                errorView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        setProgressShown(false);
                        try {
                            method.setAccessible(true);
                            method.invoke(t, args);
                        } catch (Exception e) {
                            Logger.e(TAG, e.toString());
                            setErrorShown(false);
                        }
                    }
                });
            }
        }
    }

    /**
     * 重复加载设置 (出错了。可调用此方法，重复加载一次)
     *
     * @param t      泛型类，指子类
     * @param method 点击空布局执行方法，null 为 不执行
     * @param args   方法参数
     */
    public <T> void setRepeatViewClickMethod(final T t, final Method method, final Object... args) {
        if (null != repeatLayout) {
            setRepeatShown(false);
            if (null != method) {
                repeatLayout.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        setProgressShown(false);
                        try {
                            method.setAccessible(true);
                            method.invoke(t, args);
                        } catch (Exception e) {
                            Logger.e(TAG, e.toString());
                            setRepeatShown(false);
                        }
                    }
                });
            }
        }
    }

    /** ======================================================
     * ====================================
     * [2015-03-13] better 新想法，执行method方法  end
     * =====================================
     * =======================================================*/
}
