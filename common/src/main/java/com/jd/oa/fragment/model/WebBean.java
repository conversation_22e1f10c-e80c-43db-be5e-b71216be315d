package com.jd.oa.fragment.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.fragment.web.WebConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * 加载webFragment传递的参数bean
 *
 * <AUTHOR>
 */
public class WebBean implements Parcelable {

    /**
     * 标题
     */
    private String title;

    /**
     * 加载的url
     */
    private String url;

    /**
     * 是否显示导航, 0 不显示，1 显示 （原生头）
     */
    private String showNav = WebConfig.H5_NATIVE_HEAD_SHOW;

    /**
     * 页面主题dark light 默认light
     */
    private String theme = WebConfig.THEME_LIGHT;
    /**
     * 原生返回/关闭/更多按钮是否悬浮  0不沉浸 1 沉浸
     */
    private String immersive = WebConfig.H5_IMMERSIVE_NO;
    /**
     * 是否显示分享
     */
    private String share = WebConfig.H5_SHARE_SHOW;
    /**
     * 是否显示分享
     */
    private String orientation = WebConfig.H5_ORIENTATION_PORTRAIT;
    /**
     * 分享标题
     */
    private String shareTitle;
    /**
     * 分享Url
     */
    private String shareUrl;
    /**
     * 分享图标
     */
    private String shareIconUrl;
    /**
     * 分享内容
     */
    private String shareContent;
    /**
     * 写入cookie标记
     */
    private int writeCookie = 0;
    /**
     * cookieMap
     */
    private HashMap<String, String> cookieMapInfo;

    /**
     * requestFocus
     */
    private boolean requestFocus = true;

    /**
     * 是否是用来显示百科词条
     */
    private boolean isFromJoySpacePedia = false;
    /**
     * 是否通过https: ip 的 ssl验证
     */
    public boolean isPassSsl;
    private Map<String, String> formPairs;

    public Map<String, String> getFormPairs() {
        return formPairs;
    }

    public void setFormPairs(Map<String, String> formPairs) {
        this.formPairs = formPairs;
    }

    public WebBean() {

    }

    public WebBean(String url, String showNav) {
        super();
        this.url = url;
        this.showNav = WebConfig.validateShowNav(showNav);
    }

    public WebBean(String url, String showNav, String share) {
        super();
        this.url = url;
        this.showNav = WebConfig.validateShowNav(showNav);
        this.share = WebConfig.validateShare(share);
    }

    public WebBean(String url, String showNav, String theme, String immersive, String share) {
        super();
        this.url = url;
        this.showNav = WebConfig.validateShowNav(showNav);
        this.theme = theme;
        this.immersive = WebConfig.validateImmersive(immersive);
        this.share = WebConfig.validateShare(share);
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getShowNav() {
        return showNav;
    }

    public void setShowNav(String showNav) {
        this.showNav = WebConfig.validateShowNav(showNav);
    }

    public String getShare() {
        return share;
    }

    public void setShare(String share) {
        this.share = WebConfig.validateShare(share);
    }

    public void setShareTitle(String shareTitle) {
        this.shareTitle = shareTitle;
    }

    public String getShareTitle() {
        return shareTitle;
    }

    public void setShareIconUrl(String shareIconUrl) {
        this.shareIconUrl = shareIconUrl;
    }

    public String getShareIconUrl() {
        return shareIconUrl;
    }

    public void setShareContent(String shareContent) {
        this.shareContent = shareContent;
    }

    public String getShareContent() {
        return shareContent;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setWriteCookie(int writeCookie) {
        this.writeCookie = writeCookie;
    }

    public boolean getRequestFocus() {
        return requestFocus;
    }

    public void disableRequestFocus() {
        this.requestFocus = false;
    }

    public int getWriteCookie() {
        return writeCookie;
    }

    public void setCookieMapInfo(HashMap<String, String> cookieMapInfo) {
        this.cookieMapInfo = cookieMapInfo;
    }

    public boolean isFromJoySpacePedia() {
        return isFromJoySpacePedia;
    }

    public void setFromJoySpacePedia(boolean fromJoySpacePedia) {
        isFromJoySpacePedia = fromJoySpacePedia;
    }

    public HashMap<String, String> getCookieMapInfo() {
        return cookieMapInfo;
    }

    public String getTheme() {
        return theme == null ? "" : theme;
    }

    public void setTheme(String theme) {
        this.theme = theme == null ? "" : theme;
    }

    public String getImmersive() {
        return immersive;
    }

    public void setImmersive(String immersive) {
        this.immersive = WebConfig.validateImmersive(immersive);
    }

    public void setOrientation(String orientation) {
        this.orientation = orientation;
    }

    public String getOrientation() {
        return orientation;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.title);
        dest.writeString(this.url);
        dest.writeString(this.showNav);
        dest.writeString(this.immersive);
        dest.writeString(this.share);
        dest.writeString(this.theme);
        dest.writeString(this.orientation);
        dest.writeString(this.shareTitle);
        dest.writeString(this.shareUrl);
        dest.writeString(this.shareIconUrl);
        dest.writeString(this.shareContent);
        dest.writeInt(this.writeCookie);
        dest.writeSerializable(this.cookieMapInfo);
        dest.writeByte(this.isPassSsl ? (byte) 1 : (byte) 0);
        dest.writeByte((byte) (isFromJoySpacePedia ? 1 : 0));
        if (null == this.formPairs) {
            dest.writeInt(0);
        } else {
            dest.writeInt(this.formPairs.size());
            for (Map.Entry<String, String> entry : this.formPairs.entrySet()) {
                dest.writeString(entry.getKey());
                dest.writeString(entry.getValue());
            }
        }
    }

    protected WebBean(Parcel in) {
        this.title = in.readString();
        this.url = in.readString();
        this.showNav = in.readString();
        this.immersive = in.readString();
        this.share = in.readString();
        this.theme = in.readString();
        this.orientation = in.readString();
        this.shareTitle = in.readString();
        this.shareUrl = in.readString();
        this.shareIconUrl = in.readString();
        this.shareContent = in.readString();
        this.writeCookie = in.readInt();
        this.cookieMapInfo = (HashMap<String, String>) in.readSerializable();
        this.isPassSsl = in.readByte() != 0;
        this.isFromJoySpacePedia = in.readByte() != 0;
        int formPairsSize = in.readInt();
        if (formPairsSize > 0)
            this.formPairs = new HashMap<String, String>(formPairsSize);
        for (int i = 0; i < formPairsSize; i++) {
            String key = in.readString();
            String value = in.readString();
            this.formPairs.put(key, value);
        }
    }

    public static final Creator<WebBean> CREATOR = new Creator<WebBean>() {
        @Override
        public WebBean createFromParcel(Parcel source) {
            return new WebBean(source);
        }

        @Override
        public WebBean[] newArray(int size) {
            return new WebBean[size];
        }
    };
}
