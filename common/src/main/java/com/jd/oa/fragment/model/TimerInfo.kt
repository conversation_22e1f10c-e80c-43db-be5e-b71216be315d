package com.jd.oa.fragment.model

import java.io.Serializable

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/4
 */

data class TimerInfo(
    val startTime: Long? = null,
    val endTime: Long? = null,
    val remindStr: String,
    val cycle: Int
): Serializable {
    override fun toString(): String {
        return "TimerInfo(startTime=$startTime, endTime=$endTime, remindStr='$remindStr', cycle=$cycle)"
    }
}
