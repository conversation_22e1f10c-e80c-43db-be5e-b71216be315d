package com.jd.oa.fragment.js.hybrid.bean;

public class DeviceInfo {
    private int screenWidth;
    private String system;
    private int screenHeight;
    private String model;
    private String modelType;
    private String brand;
    private String version;
    private String platform;
    private String deviceToken;
    private String language;
    private SafeAreaInsets safeAreaInsets;
    private String fp;


    public int getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(int screenWidth) {
        this.screenWidth = screenWidth;
    }

    public String getSystem() {
        return system == null ? "" : system;
    }

    public void setSystem(String system) {
        this.system = system == null ? "" : system;
    }

    public int getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(int screenHeight) {
        this.screenHeight = screenHeight;
    }

    public String getModel() {
        return model == null ? "" : model;
    }

    public void setModel(String model) {
        this.model = model == null ? "" : model;
    }

    public String getModelType() {
        return modelType == null ? "" : modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType == null ? "" : modelType;
    }

    public String getBrand() {
        return brand == null ? "" : brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? "" : brand;
    }

    public String getVersion() {
        return version == null ? "" : version;
    }

    public void setVersion(String version) {
        this.version = version == null ? "" : version;
    }

    public String getPlatform() {
        return platform == null ? "" : platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform == null ? "" : platform;
    }

    public String getDeviceToken() {
        return deviceToken == null ? "" : deviceToken;
    }

    public void setDeviceToken(String deviceToken) {
        this.deviceToken = deviceToken == null ? "" : deviceToken;
    }

    public String getLanguage() {
        return language == null ? "" : language;
    }

    public void setLanguage(String language) {
        this.language = language == null ? "" : language;
    }

    public SafeAreaInsets getSafeAreaInsets() {
        return safeAreaInsets;
    }

    public void setSafeAreaInsets(SafeAreaInsets safeAreaInsets) {
        this.safeAreaInsets = safeAreaInsets;
    }

    public void setFp(String fp) {
        this.fp = fp;
    }

    public String getFp() {
        return fp == null ? "" : fp;
    }

    public static class SafeAreaInsets {
        private int left;
        private int top;
        private int right;
        private int bottom;

        public int getLeft() {
            return left;
        }

        public void setLeft(int left) {
            this.left = left;
        }

        public int getTop() {
            return top;
        }

        public void setTop(int top) {
            this.top = top;
        }

        public int getRight() {
            return right;
        }

        public void setRight(int right) {
            this.right = right;
        }

        public int getBottom() {
            return bottom;
        }

        public void setBottom(int bottom) {
            this.bottom = bottom;
        }
    }
}
