package com.jd.oa.fragment.js.hybrid

import android.content.Context
import com.jd.oa.eventbus.JmEventDispatcher
import com.jd.oa.eventbus.JmEventVoidReturnProcessor
import com.jd.oa.fragment.js.hybrid.events.EditCalendarEventProcessor
import wendu.dsbridge.CompletionHandler

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/2 00:28
 */

class JsEventExt {

    private val hashCode = hashCode()

    private val editCalendarEventProcessor = EditCalendarEventProcessor()

    init {
        JmEventDispatcher.registerProcessor(editCalendarEventProcessor)
    }

    fun onNativeEvent(eventName: String?, handler: CompletionHandler<Any?>?) {
        if (eventName.isNullOrEmpty()) return
        JmEventDispatcher.registerProcessor(JsEventProcessor(hashCode, handler, eventName))
    }

    fun offNativeEvent(eventName: String?) {
        if (eventName.isNullOrEmpty()) return
        JmEventDispatcher.unregisterAll { processor ->
            if (processor is JsEventProcessor)
                processor.hashCode == hashCode && processor.hasEvent(eventName)
            else
                false
        }
    }

    fun onDestroy() {
        JmEventDispatcher.unregisterAll { processor ->
            processor is JsEventProcessor && processor.hashCode == hashCode
        }
        JmEventDispatcher.unregisterProcessor(editCalendarEventProcessor)
    }

    private class JsEventProcessor(
        val hashCode: Int, val handler: CompletionHandler<Any?>?, vararg events: String
    ) : JmEventVoidReturnProcessor<Any>(*events) {

        //发送到h5
        override fun processEvent(
            context: Context,
            event: String,
            args: Any?,
        ) {
            handler?.setProgressData(args)
        }
    }

}