package com.jd.oa.fragment.js.hybrid.utils

class HandlerContainer<T> {
    private val handlerMap = HashMap<String, T?>()
    private val indexList = ArrayList<String>()

    fun update(mediaId: String, handler: T?) {
        if (handlerMap.containsKey(mediaId)) {
            // 如果已经存在，移动到列表末尾
            indexList.remove(mediaId)
        }
        // 添加到列表末尾
        indexList.add(mediaId)
        handlerMap[mediaId] = handler

        // 保证只保留两个元素
        if (indexList.size > 2) {
            val oldestMediaId = indexList.removeAt(0) // 移除最左边的元素
            handlerMap.remove(oldestMediaId)
        }
    }

    fun getHandler(mediaId: String): T? {
        return handlerMap[mediaId]
    }

    fun clear() {
        handlerMap.clear()
        indexList.clear()
    }
}