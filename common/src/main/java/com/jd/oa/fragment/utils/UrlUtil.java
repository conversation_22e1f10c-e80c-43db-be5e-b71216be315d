package com.jd.oa.fragment.utils;

import android.text.TextUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;

/*
 * Time: 2023/4/3
 * Author: qudongshi
 * Description:
 */
public class UrlUtil {

    public static boolean schemeInBlackList(String scheme, List<String> blackList) {
        if (blackList == null || blackList.size() == 0) {
            return false;
        }
        if (TextUtils.isEmpty(scheme)) {
            return false;
        }
        for (String s : blackList) {
            if (s.startsWith(scheme)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isValidUrl(String url) {
        try {
            if (TextUtils.isEmpty(url)) return false;
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }

}
