package com.jd.oa.fragment.js.hybrid

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.session.PlaybackState
import android.text.TextUtils
import android.webkit.JavascriptInterface
import androidx.fragment.app.FragmentActivity
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.common.me_audio_player.AudioPlayerManager
import com.jd.oa.fragment.js.JSErrCode
import com.jd.oa.fragment.js.JSTools
import com.jd.oa.fragment.js.hybrid.utils.HandlerContainer
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.fragment.web.IWebPage
import com.jd.oa.multitask.AudioPlaybackSSEHandler
import com.jd.oa.multitask.FloatItemInfo
import com.jd.oa.multitask.MultiTaskManager
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2025/1/7 18:34
 */
class JsMedia(
    private val webPage: IWebPage? = null,
    private val webContainer: IWebContainer? = null
) :
    JsInterface {

    companion object {
        const val DOMAIN = "media"
        const val TAG = "JsMedia"
    }

    //JMAudioCategoryManager生产的
    var secret: String? = null
    private val activity by lazy { AppBase.getTopActivity() }
    private var playbackStateChangeReceiver: BroadcastReceiver? = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent != null) {
                val state = intent.getIntExtra("playback_state", -1)
                val mediaId = intent.getStringExtra("mediaId")
                mediaId?.let {
                    val handler = mapHandler.getHandler(mediaId)
                    handler?.let {
                        val playInfo = JSONObject().apply {
                            put("identifier", mediaId)
                            put("playStatus", when (state) {
                                PlaybackState.STATE_PLAYING -> "running"
                                PlaybackState.STATE_PAUSED -> "pause"
                                PlaybackState.STATE_STOPPED -> "end"
                                else -> "unknown" // 添加一个默认情况，防止遗漏其他状态
                            })
                        }
                        val data = JSONObject().apply {
                            put("data", playInfo)
                        }
                        it.setProgressData(JSTools.success(data))
                    }
                }
            }
        }
    }

    //保存两个callback以防止新的更新callback后老callback的收不到对应后到的广播
    private var mapHandler = HandlerContainer<CompletionHandler<Any?>>()

    init {
        activity?.let {
            playbackStateChangeReceiver?.let { receiver ->
                MELogUtil.localD(TAG, "register receiver")
                //只注册一次广播
                LocalBroadcastManager.getInstance(it).registerReceiver(receiver, IntentFilter(AudioPlayerManager.EVENT_STATE_CHANGE))
            }
        }
    }

    @JavascriptInterface
    fun startSSEAudioPlayer(args: Any?, handler: CompletionHandler<Any?>?) {
        val json = args as? JSONObject ?: return
        val isShowPanel = json.optBoolean("isShowPanel", false)
        val deeplink = json.optString("deeplink")

        val (url, params, headers) = json.optJSONObject("sseReqestInfo")?.let { sseRequestInfo ->
            val url = sseRequestInfo.optString("url")
            val params = sseRequestInfo.optJSONObject("params")?.apply {
                remove("audioType") // 暂不支持MP3格式
            }
            val headers = sseRequestInfo.optJSONObject("headers")
            Triple(url, params, headers)
        } ?: Triple("", null, null)

        val (title, logoUrl, identifier) = json.optJSONObject("playInfo")?.let { playInfo ->
            val title = playInfo.optString("title")
            val logoUrl = playInfo.optString("logoUrl")
            val identifier = playInfo.optString("identifier")
            Triple(title, logoUrl, identifier)
        } ?: Triple("", "", "")

        activity?.let {
            mapHandler.update(identifier, handler)
            AudioPlayerManager.getInstance()
                .playStream(AppBase.getAppContext(), AudioPlaybackSSEHandler(), identifier, title, logoUrl, url, params, headers, null)
            if (isShowPanel) {
                val floatItemInfo = FloatItemInfo(
                    it,
                    identifier,
                    title,
                    "",
                    logoUrl,
                    deeplink,
                    FloatItemInfo.FLOAT_TYPE_MEDIA,
                    url,
                    headers.toString(),
                    params.toString()
                )
                if (it is FragmentActivity) {
                    MultiTaskManager.getInstance().addFlowList(it, floatItemInfo)
                }
            }
        }
    }

    @JavascriptInterface
    fun pauseSSEAudioPlayer(args: Any?, handler: CompletionHandler<Any?>?) {
        val json = args as? JSONObject ?: return
        val identifier = json.optString("identifier")
        if (TextUtils.isEmpty(identifier)) {
            handler?.complete(JSTools.error(JSErrCode.ERROR_104))
            return
        }
        AudioPlayerManager.getInstance().pause()
        handler?.complete(JSTools.success())
    }

    @JavascriptInterface
    fun stopSSEAudioPlayer(args: Any?, handler: CompletionHandler<Any?>?) {
        val json = args as? JSONObject ?: return
        val identifier = json.optString("identifier")
        if (TextUtils.isEmpty(identifier)) {
            handler?.complete(JSTools.error(JSErrCode.ERROR_104))
            return
        }
        activity?.let {
            AudioPlayerManager.getInstance().stop(it)
        }
        handler?.complete(JSTools.success())
    }

    @JavascriptInterface
    fun setMaxAudioSession(args: Any?, handler: CompletionHandler<Any?>?) {
        val json = args as? JSONObject ?: return
        val isIdle = json.optBoolean("isIdle", false)
        if (isIdle) {
            //释放占用的声道
            releaseAudioIfNeed()
            handler?.complete(JSTools.success())
        } else {
            //申请占用声道
            val needToast = json.optBoolean("needToast", false)
            val setResult = JMAudioCategoryManager.getInstance()
                .setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_WEB_APP, needToast)
            val result = JSONObject()
            result.put("curAudioCategory", setResult.currentAudioCategory)
            if (setResult.available) {
                secret = setResult.secret
                handler?.complete(JSTools.success(result))
            } else {
                result.put("errCode", JSErrCode.ERROR_1006005)
                result.put("errMsg", JSErrCode.codeToEnglishMessage(JSErrCode.ERROR_1006005))
                handler?.complete(JSTools.error(result))
            }
        }
    }

    private fun releaseAudioIfNeed() {
        if (secret.isNullOrEmpty()) return
        JMAudioCategoryManager.getInstance().releaseAudio(secret)
        secret = null
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseAudioIfNeed()
        mapHandler.clear()
        activity?.let {
            playbackStateChangeReceiver?.let { receiver ->
                MELogUtil.localD(TAG, "unregisterReceiver")
                LocalBroadcastManager.getInstance(it).unregisterReceiver(receiver)
            }
        }
    }
}