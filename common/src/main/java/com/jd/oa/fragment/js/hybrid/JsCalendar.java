package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.JDFlutterService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.dialog.BottomSheetActionDialog;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import wendu.dsbridge.CompletionHandler;

public class JsCalendar {

    public static final String DOMAIN = "calendar";
    private final JsSdkKit jsSdkKit;
    private final Activity activity;

    private static final String ACTION_ADD = "add";
    private static final String ACTION_SELECT = "select";
    public static final int REQUEST_CODE_CALENDAR_SELECT = 621;
    public static final int REQUEST_CODE_CALENDAR_ADD = 622;

    public static final int REQUEST_CODE_CALENDAR_ARRANGE_TIME = 623;

    private IWebPage webPage;

    public JsCalendar(JsSdkKit jsSdkKit) {
        this.jsSdkKit = jsSdkKit;
        this.activity = AppBase.getTopActivity();
    }

    public JsCalendar(JsSdkKit jsSdkKit, IWebPage webPage) {
        this(jsSdkKit);
        this.webPage = webPage;
    }

    /**
     * 获取一个日程，可以通过打开新建/选择日程弹窗，或者直接打开新建/日程选择界面
     * @param args
     * @param handler
     */

    @JavascriptInterface
    public void getAppointment(final Object args, final CompletionHandler<Object> handler) {
        if(webPage != null){
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "getAppointment", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        getAppointmentInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_106));
                        }
                    }
                }
            });
        } else {
            getAppointmentInner(args, handler);
        }
    }


    public void getAppointmentInner(final Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            JSONArray action = jsonObject.optJSONArray("action");
            String params = jsonObject.optString("params", "{}");
            String from = jsonObject.optString("from", "");
            String selectorConfig = jsonObject.optString("selectorConfig");
            HashMap<String, Object> data = JSON.parseObject(params, new TypeReference<HashMap<String, Object>>() {});
            if (action == null || action.length() >= 2 || action.length() == 0) {
                // 创建日程：add，选择日程：select，两个都传或者为空时显示弹窗
                showSelectDialog(handler, data, from, params, selectorConfig);
            } else {
                switch (action.getString(0)) {
                    case ACTION_ADD:
                        calenderAdd(handler, data, from);
                        break;
                    case ACTION_SELECT:
                        calenderSelect(selectorConfig, params, from, handler);
                        break;
                    default:
                        break;
                }
            }
            JDMAUtils.clickEvent("","joyspace_1671178113159|3", null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showSelectDialog(final CompletionHandler<Object> handler, final HashMap<String, Object> data, final String from, String params, final String selectorConfig) {
        if (AppBase.getTopActivity() == null || AppBase.getTopActivity().isFinishing()) {
            MELogUtil.localI(MELogUtil.TAG_JS, "JsCalendar showSelectDialog activity is null");
            handler.complete(JSTools.error());
            return;
        }
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                List<String> list = Arrays.asList(AppBase.getAppContext().getString(R.string.me_calendar_js_select)
                        , AppBase.getAppContext().getString(R.string.me_calendar_js_add));
                final BottomSheetActionDialog dialog = new BottomSheetActionDialog(AppBase.getTopActivity(), list);
                dialog.setOnActionClickListener(new BottomSheetActionDialog.OnActionClickListener() {
                    @Override
                    public void onActionClick(int position) {
                        dialog.cancel();
                        if (position == 0) {
                            calenderSelect(selectorConfig, params, from, handler);
                        } else if (position == 1) {
                            calenderAdd(handler, data, from);
                        }
                        MELogUtil.localI(MELogUtil.TAG_JS, "JsCalendar showSelectDialog OnActionClickListener, position: " + position);
                    }
                });
                dialog.setOnCancelClickListener(new BottomSheetActionDialog.OnCancelClickListener() {
                    @Override
                    public void onCancelClick() {
                        dialog.cancel();
                        try {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put(JsSdkKit.STATUS_CODE, JsSdkKit.CANCEL_CODE);
                            handler.complete(jsonObject);
                            MELogUtil.localI(MELogUtil.TAG_JS, "JsCalendar showSelectDialog OnCancelClickListener");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
                dialog.show();
            }
        });
    }

    /**
     * 插入（选择）日程
     *
     * @param handler
     */
    public void calenderSelect(String config, String params, String from, final CompletionHandler<Object> handler) {
        jsSdkKit.addHandler(REQUEST_CODE_CALENDAR_SELECT, handler);
        Uri.Builder builder = Uri.parse(DeepLink.CALENDAR_SCHEDULE_SELECT)
                .buildUpon();
        if (!TextUtils.isEmpty(config)) {
            builder.appendQueryParameter("mparam", config);
        }
        if (!TextUtils.isEmpty(params)) {
            builder.appendQueryParameter("params", params);
        }
        if (!TextUtils.isEmpty(from)) {
            builder.appendQueryParameter("from", from);
        }
        Intent intent = Router.build(builder.build()).getIntent(AppBase.getAppContext());
        activity.startActivityForResult(intent, REQUEST_CODE_CALENDAR_SELECT);
        JDMAUtils.clickEvent("","joyspace_1671178113159|5", null);
    }

    /**
     * 创建日程
     *
     * @param handler
     * @param data
     */
    public void calenderAdd(final CompletionHandler<Object> handler, final HashMap<String, Object> data, String from) {
        jsSdkKit.addHandler(REQUEST_CODE_CALENDAR_ADD, handler);
        HashMap<String, Object> mparam = new HashMap<>();
        mparam.put("mparam", data);
        mparam.put("from", from);
        JDFlutterService jdFlutterService = AppJoint.service(JDFlutterService.class);
        jdFlutterService.openFlutterPage(AppBase.getTopActivity(), "schedule_create_page", mparam, REQUEST_CODE_CALENDAR_ADD);
        JDMAUtils.clickEvent("","joyspace_1671178113159|4", null);
    }

    @JavascriptInterface
    public void arrangeTime(final Object args, final CompletionHandler<Object> handler) {
        jsSdkKit.addHandler(REQUEST_CODE_CALENDAR_ARRANGE_TIME, handler);
        JSONObject jsonObject = (JSONObject) args;
        HashMap<String, Object> mparam = JSON.parseObject(jsonObject.toString(), new TypeReference<HashMap<String,Object>>(){}.getType());
        HashMap<String,Object> param = new HashMap<>();
        param.put("mparam", mparam);

        JDFlutterService jdFlutterService = AppJoint.service(JDFlutterService.class);
        jdFlutterService.openFlutterPage(AppBase.getTopActivity(), "time_arrangement_page", param, REQUEST_CODE_CALENDAR_ARRANGE_TIME);
    }
}
