package com.jd.oa.fragment;

import static com.jd.oa.fragment.WebFragment2.EXTRA_MANIPULATE_ACTIONBAR;
import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.fragment.js.hybrid.JsJoySpace.REQUEST_CODE_CALL_JOYSPACE_PEDIA_VOTE;
import static com.jd.oa.fragment.js.hybrid.JsPediaBrowser.REQUEST_CODE_CALL_JOYSPACE_PEDIA_BACK;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Outline;
import android.graphics.Point;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.fragment.adapter.PediaContributorsAdapter;
import com.jd.oa.fragment.adapter.PediaUnlikeAdapter;
import com.jd.oa.fragment.js.hybrid.JsJoySpace;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.model.JoyPediaItemInfo;
import com.jd.oa.fragment.model.PediaUnlikeBean;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.listener.OnJoySpaceListener;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jme.common.BuildConfig;
import com.jme.common.R;
import com.tencent.smtt.sdk.CookieManager;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import wendu.dsbridge.CompletionHandler;

@Route(DeepLink.JOYSPACE_PEDIA_CARD)
public class JoySpaceDialogFragment extends BottomSheetDialogFragment {
    /**
     * 顶部向下偏移量  距离顶部距离
     */
    private int topOffset = 120;
    private BottomSheetBehavior<FrameLayout> behavior;
    private NestedScrollView sv;
    private RelativeLayout rlTop;
    private RelativeLayout rlRoot;
    private TextView tvLikeCount;
    //    private TextView tvUnLikeCount;
    private IconFontView tvUnLike;
    private IconFontView tvLike;
    private RecyclerView rvContributors;
    private IconFontView tvBack;
    private String TAG = "JoySpaceDialogFragment";
    private TextView tvTitle;
    private WebFragment2 webFragment2;
    private LinearLayout rlEmpty;
    private String url = "";
    private JsSdkKit jsSdkKit = new JsSdkKit();
    private boolean isDebug = BuildConfig.DEBUG;
    private RelativeLayout rlBottom;
    private TextView tvContributors;
    private IconFontView tvMore;

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
//        Log.e(TAG, "onCreateDialog: ");
        return super.onCreateDialog(savedInstanceState);
    }

    public JoySpaceDialogFragment() {
        super();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(STYLE_NORMAL, R.style.AppBottomSheet);
        if (getArguments() != null)
            url = getArguments().getString("url", url);

        if (isDebug && TextUtils.isEmpty(url)) {//测试时使用默认地址
            //测试
//   this.url = "http://joyspace-test2.jd.com/knowledges/item/%E8%BE%B9%E7%95%8C%E9%AA%8C%E8%AF%81?popup=true";
            //预发
            this.url = "https://joyspace-pre2.jd.com/knowledges/item/%E6%99%BA%E8%83%BDUI?popup=true";

        }
    }

    public void initView(View view) {
        sv = view.findViewById(R.id.sv);
        rlTop = view.findViewById(R.id.rl_top);
        rlRoot = view.findViewById(R.id.rl_root);
        tvBack = view.findViewById(R.id.tv_back);
        tvTitle = view.findViewById(R.id.tv_title);
        rlEmpty = view.findViewById(R.id.empty_view);
        tvMore = view.findViewById(R.id.tv_more);
        sv.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
//                Log.e("666666", "onScrollChange: " + scrollY + " " + oldScrollY);
                //设置头部渐变效果  开始渐变的滑动距离
                float topHeight = 130f;
                float maxHeight = 160f;
                if (scrollY < topHeight) {
                    rlTop.setAlpha(0);
                } else if (scrollY < maxHeight) {
                    rlTop.setAlpha((((float) scrollY - topHeight) / (maxHeight - topHeight)));
                } else if (rlTop.getAlpha() != 1) {
                    rlTop.setAlpha(1);
                }
                rlTop.setClickable(rlTop.getAlpha() >= 0.8);
                tvBack.setClickable(rlTop.getAlpha() >= 0.8);
                tvMore.setClickable(rlTop.getAlpha() >= 0.8);
            }
        });

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //  Log.e(TAG, "onViewCreated: ");
        try {
            BottomSheetDialog dialog = (BottomSheetDialog) getDialog();
            FrameLayout flRoot = dialog.getDelegate().findViewById(R.id.container);
            View inflate = View.inflate(getActivity(), R.layout.jdme_content_bottom_joyspace_pedia_out, null);
            ViewGroup.LayoutParams layoutParams1 = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            flRoot.addView(inflate, layoutParams1);
            rlBottom = inflate.findViewById(R.id.rl_bottom);
            tvLike = inflate.findViewById(R.id.tv_like);
            tvUnLike = inflate.findViewById(R.id.tv_unlike);
            tvLikeCount = inflate.findViewById(R.id.tv_like_count);
//        tvUnLikeCount = inflate.findViewById(R.id.tv_unlike_count);
            tvContributors = inflate.findViewById(R.id.tv_contributors);
            rvContributors = inflate.findViewById(R.id.rv_contributors);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
//        Log.e(TAG, "onCreateView: ");
//        View view = LayoutInflater.from(getActivity()).inflate(R.layout.content_bottom_sheet_dialog, null);
        View view = inflater.inflate(R.layout.content_bottom_sheet_dialog, container, false);
        initView(view);
        setClipViewCornerRadius(view, 40);
        if (webFragment2 == null) {
            webFragment2 = new WebFragment2();
        }
        webFragment2.setOnJoySpaceListener(new OnJoySpaceListener() {
            @Override
            public void onWebViewCreate(JMEWebview webview) {
                try {
                    // 写入cookie
                    //  cookies.domainList  取ducc 配置
                    String domain = getDomain();
                    if (!TextUtils.isEmpty(domain)) {
                        CookieManager cookieManager = CookieManager.getInstance();
                        cookieManager.setCookie(domain, "focus-token=" + TokenManager.getInstance().getAccessToken());
                        cookieManager.setCookie(domain, "focus-team-id=" + PreferenceManager.UserInfo.getTenantCode());
                        cookieManager.setCookie(domain, "focus-client=Android");
                        cookieManager.setCookie(domain, "focus-lang=" + LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
                        MELogUtil.info("baike", "onWebViewCreate url=" + url + " domain=" + domain
                                + " focus-token=" + TokenManager.getInstance().getAccessToken()
                                + " focus-team-id=" + PreferenceManager.UserInfo.getTenantCode()
                                + " focus-client=Android"
                                + " focus-lang=" + LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
                        CookieManager.getInstance().flush();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                initWebView(webview);
            }

            @Override
            public Map<String, String> getJoyHeaders() {
                if (TextUtils.isEmpty(getDomain())) {
                    return null;
                }
                Map<String, String> additionalHeaders = new HashMap<>();
                additionalHeaders.put("focus-token", TokenManager.getInstance().getAccessToken());
                additionalHeaders.put("focus-team-id", PreferenceManager.UserInfo.getTenantCode());
                additionalHeaders.put("focus-client", "Android");
                additionalHeaders.put("focus-lang", LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
                return additionalHeaders;
            }

            @Override
            public JoySpaceDialogFragment getJoySpaceDialogFragment() {
                return JoySpaceDialogFragment.this;
            }
        });
        WebBean bean = new WebBean();
        bean.setUrl(url);
        bean.setShowNav(WebConfig.H5_NATIVE_HEAD_HIDE);
        bean.setWriteCookie(1);
        bean.setFromJoySpacePedia(true);
        Bundle args = new Bundle();
        args.putParcelable(EXTRA_WEB_BEAN, bean);
        args.putBoolean(EXTRA_MANIPULATE_ACTIONBAR, false);
        webFragment2.setArguments(args);
        getChildFragmentManager().beginTransaction().replace(R.id.fl_container_web, webFragment2, "webFragment2").commitAllowingStateLoss();
        return view;
    }

    private void setClipViewCornerRadius(View view, final int radius) {
        if (view == null) return;
        if (radius <= 0) {
            return;
        }
        view.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight() + radius * 2, radius);
            }
        });
        view.setClipToOutline(true);
    }

    @Override
    public void onStart() {
        super.onStart();
        // 设置软键盘不自动弹出
        if (getActivity() != null) {
            getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING | WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        }
        final BottomSheetDialog dialog = (BottomSheetDialog) getDialog();
        FrameLayout bottomSheet = dialog.getDelegate().findViewById(R.id.design_bottom_sheet);
        if (bottomSheet != null) {
            CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) bottomSheet.getLayoutParams();
            layoutParams.height = getHeight();
//            layoutParams.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
            behavior = BottomSheetBehavior.from(bottomSheet);
            if (getActivity() != null) {
                behavior.setPeekHeight((getActivity().getWindow().getDecorView().getHeight() - topOffset) / 2);
                behavior.setMaxWidth(-1);
            }
            bottomSheet.setLayoutParams(layoutParams);
            // 初始为展开状态
//            behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        }
        behavior.setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            int lastStableState;

            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                switch (newState) {
                    case BottomSheetBehavior.STATE_COLLAPSED://半开
                        lastStableState = BottomSheetBehavior.STATE_COLLAPSED;
//                        rlRoot.getLayoutParams().height = behavior.getPeekHeight();
//                        Log.e("Bottom Sheet Behaviour", "STATE_COLLAPSED 半开");
                        break;
                    case BottomSheetBehavior.STATE_DRAGGING:
//                        Log.e("Bottom Sheet Behaviour", "STATE_DRAGGING 滑动中");
                        break;
                    case BottomSheetBehavior.STATE_EXPANDED:   //完全展开
                        lastStableState = BottomSheetBehavior.STATE_EXPANDED;
//                        rlRoot.getLayoutParams().height = getHeight();
//                        Log.e("Bottom Sheet Behaviour", "STATE_EXPANDED 完全展开");
                        break;
                    case BottomSheetBehavior.STATE_HIDDEN:     //完全收回
                        lastStableState = BottomSheetBehavior.STATE_HIDDEN;
                        dialog.dismiss();
//                        Log.e("Bottom Sheet Behaviour", "STATE_HIDDEN 完全收回");
                        break;
                    case BottomSheetBehavior.STATE_SETTLING:
//                        Log.e("Bottom Sheet Behaviour", "STATE_SETTLING");
                        break;
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
//                Log.e(TAG, "onSlide: " + slideOffset + "  state=" + behavior.getState() + " lastStableState" + lastStableState);
                //到顶后下拉直接收回
                if (lastStableState == 3 && slideOffset < 0.1f) {
                    behavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                    dialog.dismiss();
                }
            }
        });

    }


    private void initWebView(JMEWebview webView) {
        if (jsSdkKit == null) jsSdkKit = new JsSdkKit();
//        webView.addJavascriptObject(new JsPediaBrowser(webView, jsSdkKit, this), JsBrowser.DOMAIN);
        webView.addJavascriptObject(new JsJoySpace(this), JsJoySpace.DOMAIN);
//        webView.addJavascriptObject(new JsBrowser(webView, new JsSdkKit(), fragment), JsBrowser.DOMAIN);
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView webView, String s) {
                super.onPageFinished(webView, s);
                rlEmpty.setVisibility(View.GONE);

            }

            @Override
            public void onPageStarted(WebView webView, String s, Bitmap bitmap) {
                super.onPageStarted(webView, s, bitmap);
//                ToastUtils.showToast("load start");
            }

//            @Override
//            public boolean shouldOverrideUrlLoading(WebView webView, String s) {
//                webView.loadUrl(s);
//                return true;
//            }
        });

    }


    @Override
    public void onStop() {
        super.onStop();
    }

    /**
     * 获取屏幕高度
     *
     * @return height
     */
    private int getHeight() {
        int height = 1920;
//        int height = getScreenHeight(getActivity());
        if (getContext() != null) {
            WindowManager wm = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
            Point point = new Point();
            if (wm != null) {
                // 使用Point已经减去了距离顶部的offset
                wm.getDefaultDisplay().getSize(point);
                height = point.y - getTopOffset();
            }
        }
        return height;
    }

    public int getTopOffset() {
        return topOffset;
    }

    public void setTopOffset(int topOffset) {
        this.topOffset = topOffset;
    }

    public BottomSheetBehavior<FrameLayout> getBehavior() {
        return behavior;
    }

    public void scrollToTop(final boolean isSmooth) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (isSmooth)
                        sv.smoothScrollTo(0, 0);
                    else
                        sv.scrollTo(0, 0);
                }
            });
        }
    }


    public void setGoBackDisabled(final boolean disabled, final CompletionHandler<Object> handler) {
        if (handler == null || getActivity() == null) return;
        if (jsSdkKit != null) {
            jsSdkKit.addHandler(REQUEST_CODE_CALL_JOYSPACE_PEDIA_BACK, handler);
        }
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                tvBack.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (disabled) {
                            jsSdkKit.joySpacePediaBack();
                        } else if (webFragment2 != null && webFragment2.getWebView() != null) {
                            if (webFragment2.getWebView().canGoBack()) {
                                webFragment2.getWebView().goBack();
                            }
                        }
                    }
                });
            }
        });
    }

    public void showBack(final boolean hidden) {
        if (getActivity() == null) return;
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                tvBack.setVisibility(hidden ? View.GONE : View.VISIBLE);
                tvBack.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (webFragment2 != null) {
                            webFragment2.goBack();
                        }
                    }
                });
            }
        });

    }

    @SuppressLint("SetTextI18n")
    public void initBottom(final JoyPediaItemInfo info, final CompletionHandler<Object> handler) {
        if (getActivity() == null || info == null || rlBottom == null) {
            return;
        }
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (info.getId() == -1) {
                    rlBottom.setVisibility(View.GONE);
                    return;
                } else {
                    rlBottom.setVisibility(View.VISIBLE);
                }
                if (getActivity() == null) return;
                tvTitle.setText(info.getName());
                if (info.getUpvoteCount() == 0) {
                    tvLikeCount.setVisibility(View.INVISIBLE);
                }
                String upVoteCount = String.valueOf(info.getUpvoteCount());
                if (info.getUpvoteCount() > 1000) {
                    int showNum = info.getUpvoteCount() / 1000;
                    upVoteCount = String.valueOf(showNum) + "k";
                }
                tvLikeCount.setText(upVoteCount);
                tvLikeCount.setTextColor(info.isUserUpVoted() ? getActivity().getResources().getColor(R.color.color_FFA53D)
                        : getActivity().getResources().getColor(R.color.color_FF666666));
//        tvUnLikeCount.setText("" + info.getDownvoteCount());
                tvLike.setText(info.isUserUpVoted() ? R.string.icon_padding_like : R.string.icon_general_like);
                tvLike.setTextColor(info.isUserUpVoted() ? getActivity().getResources().getColor(R.color.color_FFCF33)
                        : getActivity().getResources().getColor(R.color.color_FF666666));
                tvUnLike.setText(info.isUserDownVoted() ? R.string.icon_padding_like2 : R.string.icon_general_like1);
                tvUnLike.setTextColor(info.isUserDownVoted() ? getActivity().getResources().getColor(R.color.color_FF666666)
                        : getActivity().getResources().getColor(R.color.color_FF666666));

                if (info.getContributors() != null && info.getContributors().size() > 0) {
                    tvContributors.setVisibility(View.VISIBLE);
                    tvContributors.setText("贡献人");
                    rvContributors.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false));
                    PediaContributorsAdapter pediaContributorsAdapter = new PediaContributorsAdapter(getActivity(), info.getContributors());
                    rvContributors.setAdapter(pediaContributorsAdapter);
                    rvContributors.setVisibility(View.VISIBLE);
                } else {
                    tvContributors.setVisibility(View.GONE);
                    rvContributors.setVisibility(View.GONE);
                }
                jsSdkKit.addHandler(REQUEST_CODE_CALL_JOYSPACE_PEDIA_VOTE, handler);
                tvUnLike.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        showUnLikePopUpWindow(info);
                    }
                });
                tvLike.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
//                        ToastUtils.showToast(info.isUserUpVoted() ?"取消成功":"点赞成功");
                        if (jsSdkKit != null) {
                            int voteType = info.isUserUpVoted() ? 2 : 1;
                            jsSdkKit.joySpacePediaVote(info.getId(), "", voteType, info.getUpvoteCount(), info.isUserUpVoted());
                        }
                    }
                });
                tvMore.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        showMorePopUpWindow(String.valueOf(info.getId()));
                    }
                });
                if (rlTop != null) {
                    tvMore.setClickable(rlTop.isClickable());
                }
            }
        });
    }

    /**
     * 显示踩的弹窗
     */
    public void showUnLikePopUpWindow(final JoyPediaItemInfo info) {
        if (getActivity() == null) return;
        View view = LayoutInflater.from(getActivity()).inflate(R.layout.jdme_content_joyspace_pedia_unlike, null);
        final PopupWindow popupWindow = new PopupWindow(view, LinearLayout.LayoutParams.WRAP_CONTENT
                , LinearLayout.LayoutParams.WRAP_CONTENT, true);
        RecyclerView rvUnlike = view.findViewById(R.id.rv_unlike);
        rvUnlike.setLayoutManager(new GridLayoutManager(getActivity(), 3, GridLayoutManager.HORIZONTAL, false));
        ArrayList<PediaUnlikeBean> list = new ArrayList<>();
        list.add(new PediaUnlikeBean("释义不清晰", R.drawable.jdme_icon_pedia_unlike1));
        list.add(new PediaUnlikeBean("分类不准确", R.drawable.jdme_icon_pedia_unlike3));
        list.add(new PediaUnlikeBean("文档无权限", R.drawable.jdme_icon_pedia_unlike5));
        list.add(new PediaUnlikeBean("内容有误", R.drawable.jdme_icon_pedia_unlike2));
        list.add(new PediaUnlikeBean("缺少联系人", R.drawable.jdme_icon_pedia_unlike4));
        list.add(new PediaUnlikeBean("缺少相关词条", R.drawable.jdme_icon_pedia_unlike6));
        PediaUnlikeAdapter adapter = new PediaUnlikeAdapter(getActivity(), list);
        adapter.setOnItemClickListener(new PediaUnlikeAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(PediaUnlikeBean bean) {
                if (jsSdkKit != null) {
                    int voteType = 0;
                    jsSdkKit.joySpacePediaVote(info.getId(), bean.getTitle(), voteType
                            , info.getUpvoteCount(), info.isUserUpVoted());
                }
                popupWindow.dismiss();
            }
        });
        rvUnlike.setAdapter(adapter);
        popupWindow.setOutsideTouchable(true);
        int[] location = new int[2];
        tvUnLike.getLocationInWindow(location);
        view.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        int popupWidth = view.getMeasuredWidth();
        int popupHeight = view.getMeasuredHeight();
        popupWindow.showAtLocation(tvUnLike, Gravity.NO_GRAVITY, location[0] + tvUnLike.getWidth() - popupWidth
                , location[1] - popupHeight - CommonUtils.dp2px(10));
    }

    /**
     * 显示创建、修改词条弹窗
     *
     * @param id
     */
    public void showMorePopUpWindow(final String id) {
        if (getActivity() == null) return;
        View view = LayoutInflater.from(getActivity()).inflate(R.layout.jdme_content_joyspace_pedia_more, null);
        final PopupWindow popupWindow = new PopupWindow(view, CommonUtils.dp2px(160)
                , CommonUtils.dp2px(70), true);
        LinearLayout llNew = view.findViewById(R.id.ll_new_pedia);
        LinearLayout llUpdate = view.findViewById(R.id.ll_update_pedia);
        llNew.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(DeepLink.rnOld("201909020601","routeTag=pedia_edit&rnStandalone=2&mode=new&permissionType=2")).go(getActivity());
                popupWindow.dismiss();
            }
        });
        llUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(DeepLink.rnOld("201909020601","routeTag=pedia_edit&rnStandalone=2&entryId="+id+"&mode=update&permissionType=2")).go(getActivity());
                popupWindow.dismiss();
            }
        });
        popupWindow.setOutsideTouchable(true);
        popupWindow.showAsDropDown(tvMore, -popupWindow.getWidth() + tvMore.getWidth(), 0);
    }


    /**
     * 读取配置下发下来的域名
     */
    private static List<String> readConfigs(String key) {
        List<String> list = new ArrayList<>();
        try {
            String domain = ConfigurationManager.get().getEntry(key, "");
            if (!TextUtils.isEmpty(domain)) {
                list = JSON.<List<String>>parseObject(domain, List.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 检查当前url是否需要种cookies  如果ducc配置了 用ducc的   没有配就用默认的
     *
     * @return
     */
    public String getDomain() {
        try {
            List<String> duccDomain = readConfigs("cookies.domainList");
            List<String> defaultDomain = new ArrayList<>();
            defaultDomain.add("joyspace-test.jd.com");
            defaultDomain.add("joyspace-test2.jd.com");
            defaultDomain.add("joyspace-pre2.jd.com");
            defaultDomain.add("joyspace.jd.com");
            URL mUrl = new URL(url);
            String host = mUrl.getHost();
            MELogUtil.onlineI("baike", "getHost:" + host);
            if (duccDomain != null && duccDomain.size() > 0) {//ducc取到值
                MELogUtil.onlineI("baike", duccDomain.toString());
                if (duccDomain.contains(host)) {//且包含此url的域名
                    MELogUtil.onlineI("baike", "host:" + host + "type=1");
                    return host;
                } else {  //ducc取到值 但此url的域名不是ducc配置的 不种cookies
                    MELogUtil.onlineI("baike", "host=\"\"" + "type=2");
                    return "";
                }
            } else if (defaultDomain.contains(host)) {//ducc没取到值  看是否在默认的里面
                MELogUtil.onlineI("baike", "host:" + host + "type=3");
                return host;
            }
            MELogUtil.onlineI("baike", "host=\"\"" + "type=4");
            return "";
        } catch (Exception e) {
            MELogUtil.onlineI("baike", "host=\"\"" + "type=5");
            return "";
        }
    }
}