package com.jd.oa.fragment.model;

import android.os.Parcel;
import android.os.Parcelable;

public class PhotoInfo implements Parcelable {

    public String fileName = "";
    public String path = "";
    public String thumbPath = "";
    public String url = "";

    public PhotoInfo(String fileName, String path, String thumbPath, String url) {
        this.fileName = fileName;
        this.path = path;
        this.thumbPath = thumbPath;
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getThumbPath() {
        return thumbPath;
    }

    public void setThumbPath(String thumbPath) {
        this.thumbPath = thumbPath;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    protected PhotoInfo(Parcel in) {
        fileName = in.readString();
        path = in.readString();
        thumbPath = in.readString();
        url = in.readString();
    }

    public static final Creator<PhotoInfo> CREATOR = new Creator<PhotoInfo>() {
        @Override
        public PhotoInfo createFromParcel(Parcel in) {
            return new PhotoInfo(in);
        }

        @Override
        public PhotoInfo[] newArray(int size) {
            return new PhotoInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(fileName);
        dest.writeString(path);
        dest.writeString(thumbPath);
        dest.writeString(url);
    }
}
