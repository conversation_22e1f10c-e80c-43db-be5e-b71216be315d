package com.jd.oa.fragment.js.hybrid

import com.jd.oa.abilities.api.ApiAuth
import com.jd.oa.abilities.model.CHECK_AUTHORIZE_RESULT_ACCEPT
import com.jd.oa.abilities.model.CHECK_AUTHORIZE_RESULT_CANCELED
import com.jd.oa.abilities.model.CheckAuthorizeResult
import com.jd.oa.ext.toListOfType
import com.jd.oa.fragment.js.JSErrCode
import com.jd.oa.fragment.js.JSTools
import com.jd.oa.fragment.web.IWebPage
import com.jd.oa.model.service.IServiceCallback
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/18 14:27
 */
class JsLoginExt(private val webPage: IWebPage?) {

    fun requestAccess(args: Any?, handler: CompletionHandler<Any?>?) {
        if (handler == null) return
        val jsonData = args as? JSONObject
        if (jsonData == null) {
            handler.complete(JSTools.error(JSErrCode.ERROR_104))
            return
        }
        val appKey = jsonData.optString("appKey")
        if (appKey.isNullOrEmpty()) {
            handler.complete(JSTools.error(JSErrCode.ERROR_104))
            return
        }
        val scopeList = jsonData.optJSONArray("scopeList")?.toListOfType<String>()
        if (scopeList.isNullOrEmpty()) {
            handler.complete(JSTools.error(JSErrCode.ERROR_104))
            return
        }
        ApiAuth.requestAccess(
            webPage?.webContainer()?.getContext(),
            appKey,
            scopeList,
            webPage?.getAuthApp(),
            object : IServiceCallback<CheckAuthorizeResult> {
                //false 网络失败一类
                //true 校验result
                override fun onResult(success: Boolean, t: CheckAuthorizeResult?, error: String?) {
                    if (!success || t == null) {
                        handler.complete(JSTools.error(JSErrCode.ERROR_305))
                    } else {
                        when (t.status) {
                            CHECK_AUTHORIZE_RESULT_ACCEPT -> {//成功
                                val result = JSONObject().apply {
                                    put("code", t.code)
                                    put("expireIn", t.expireIn)
                                }
                                handler.complete(JSTools.success(result))
                            }
                            CHECK_AUTHORIZE_RESULT_CANCELED -> {
                                handler.complete(JSTools.error(JSErrCode.ERROR_109))
                            }
                            else -> {
                                handler.complete(JSTools.error(JSErrCode.ERROR_107))
                            }
                        }
                    }
                }
            })
    }

    fun onDestroy() {
    }

}