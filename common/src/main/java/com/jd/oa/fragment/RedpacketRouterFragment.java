package com.jd.oa.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.router.DeepLink;
import com.jme.common.R;

@Route(DeepLink.RED_PACKAGE)
@Navigation(hidden = false, displayHome = true)
public class RedpacketRouterFragment extends BaseFragment {


    private View mRootView;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_fragment_empty, container, false);
        return mRootView;
    }

}
