package com.jd.oa.fragment.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatDialog;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.adapter.ChooseItemAdapter;
import com.jd.oa.fragment.model.ChooseItemInfo;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

public class SaveFileDialog extends AppCompatDialog {

    private View mContentView;
    private Context mContext;

    private TextView mTvCancel;
    private RecyclerView mRvSaveContent;
    private ChooseItemAdapter mSaveAdaper;

    private RecyclerView mRvForwardContent;
    private ChooseItemAdapter mForwardAdaper;

    private ChooseFileDialog.IChooseFileCallback mCallback;

    public SaveFileDialog(Context context, ChooseFileDialog.IChooseFileCallback callback) {
        this(context, 0);
        this.mCallback = callback;
    }

    public SaveFileDialog(Context context, int theme) {
        super(context, R.style.BottomDialogStyle);
        mContext = context;
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_save_file, null);
        setContentView(mContentView);

        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        mTvCancel = mContentView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCallback.chooseType(ChooseItemInfo.ItemType.cancel);
                try {
                    dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
            }
        });
        // 保存
        mSaveAdaper = new ChooseItemAdapter(getContext(), getSaveChooseItems());
        mSaveAdaper.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                ChooseItemInfo info = mSaveAdaper.getItem(position);
                mCallback.chooseType(info.type);
                dismiss();
            }
        });
        mRvSaveContent = mContentView.findViewById(R.id.rv_save_content);
        mRvSaveContent.setAdapter(mSaveAdaper);
        RecyclerView.LayoutManager layoutManager = new GridLayoutManager(getContext(), 4);
        mRvSaveContent.setLayoutManager(layoutManager);
        // 转发
        mForwardAdaper = new ChooseItemAdapter(getContext(), getForwardChooseItems());
        mForwardAdaper.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                ChooseItemInfo info = mForwardAdaper.getItem(position);
                mCallback.chooseType(info.type);
                dismiss();
            }
        });
        mRvForwardContent = mContentView.findViewById(R.id.rv_forward_content);
        mRvForwardContent.setAdapter(mForwardAdaper);
        RecyclerView.LayoutManager layoutManager1 = new GridLayoutManager(getContext(), 4);
        mRvForwardContent.setLayoutManager(layoutManager1);
    }

    private List<ChooseItemInfo> getSaveChooseItems() {
        List<ChooseItemInfo> res = new ArrayList<>();
        res.add(new ChooseItemInfo(R.string.me_web_item_local, R.drawable.jdme_icon_web_local, ChooseItemInfo.ItemType.local));
        res.add(new ChooseItemInfo(R.string.me_web_item_pan, R.drawable.jdme_icon_web_pan, ChooseItemInfo.ItemType.pan));
        return res;
    }

    private List<ChooseItemInfo> getForwardChooseItems() {
        List<ChooseItemInfo> res = new ArrayList<>();
        res.add(new ChooseItemInfo(R.string.me_web_item_mail, R.drawable.jdme_icon_web_mail, ChooseItemInfo.ItemType.mail));
        res.add(new ChooseItemInfo(R.string.me_web_item_timline, R.drawable.jdme_icon_web_timline, ChooseItemInfo.ItemType.timline));
        res.add(new ChooseItemInfo(R.string.me_web_item_other, R.drawable.jdme_icon_web_other, ChooseItemInfo.ItemType.other));
        return res;
    }
}
