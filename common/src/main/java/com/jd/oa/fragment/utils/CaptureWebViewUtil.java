package com.jd.oa.fragment.utils;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Picture;
import android.os.Environment;
import android.view.View;

import com.tencent.smtt.sdk.WebView;

import java.io.File;
import java.io.FileOutputStream;

public class CaptureWebViewUtil {

    public static Bitmap captureWebView(WebView webView) {
        Picture picture = webView.capturePicture();
        int width = picture.getWidth();
        int height = picture.getHeight();
        if (width > 0 && height > 0) {
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
            Canvas canvas = new Canvas(bitmap);
            picture.draw(canvas);
            return bitmap;
        }
        return null;
    }


    /**
     * 获取控件截图（黑色背景）
     *
     * @param view view
     * @return Bitmap
     */
    public static Bitmap getViewBitmapNoBg(View view) {
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache(true);
        Bitmap drawingCache = view.getDrawingCache();
        Bitmap bitmap = Bitmap.createBitmap(drawingCache, 0, 0, drawingCache.getWidth(), drawingCache.getWidth() * 3 / 4);
        // clear drawing cache
        view.setDrawingCacheEnabled(false);
        drawingCache.recycle();
        return bitmap;
    }

    public static Bitmap getViewBitmapNoBg2(View view) {
        try {
            view.setDrawingCacheEnabled(true);
            view.buildDrawingCache(true);
            Bitmap drawingCache = view.getDrawingCache();
            Bitmap bitmap = Bitmap.createBitmap(drawingCache, 0, 0, drawingCache.getWidth(), drawingCache.getHeight());
            // clear drawing cache
            view.setDrawingCacheEnabled(false);
            drawingCache.recycle();
            return bitmap;
        } catch (Exception e) {
            return null;
        }
    }

    public static File saveBitmap(Bitmap bitmap) {
        File file = new File(Environment.getExternalStorageDirectory(), System.currentTimeMillis() + ".jpg");
        try {
            FileOutputStream fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
            fos.flush();
            fos.close();
        } catch (java.io.IOException e) {
            e.printStackTrace();
        } finally {
            bitmap.recycle();
        }
        return file;
    }

    /**
     * 清除缓存文件
     *
     * @param file
     */
    public static void deleteTempFile(File file) {
        if (file.exists()) {
            file.delete();
        }
    }


    public File getCaptureFile(WebView webView) {
        Bitmap bitmap = webView.getDrawingCache();
        try {
            String fileName = Environment.getExternalStorageDirectory().getPath() + "/webview_cap.jpg";
            FileOutputStream fos = new FileOutputStream(fileName);
            //压缩bitmap到输出流中
            bitmap.compress(Bitmap.CompressFormat.JPEG, 70, fos);
//            bitmap.recycle();
            fos.close();
            return new File(Environment.getExternalStorageDirectory().getPath() + "/webview_cap.jpg");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            bitmap.recycle();
        }
        return null;
    }

}
