package com.jd.oa.fragment.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JoyPediaItemInfo {


    @SerializedName("id")
    private int id = -1;
    @SerializedName("name")
    private String name;
    @SerializedName("aliases")
    private List<String> aliases;
    @SerializedName("explanation")
    private String explanation;
    @SerializedName("previewText")
    private String previewText;
    @SerializedName("imgUrls")
    private List<?> imgUrls;
    @SerializedName("files")
    private List<?> files;
    @SerializedName("status")
    private int status;
    @SerializedName("source")
    private int source;
    @SerializedName("ext")
    private Ext ext;
    @SerializedName("createdBy")
    private String createdBy;
    @SerializedName("updatedBy")
    private String updatedBy;
    @SerializedName("createdAt")
    private String createdAt;
    @SerializedName("updatedAt")
    private String updatedAt;
    @SerializedName("pages")
    private List<Pages> pages;
    @SerializedName("categories")
    private List<Categories> categories;
    @SerializedName("contributors")
    private List<Contributors> contributors;
    @SerializedName("upvoteCount")
    private int upvoteCount;
    @SerializedName("downvoteCount")
    private int downvoteCount;
    @SerializedName("userUpVoted")
    private boolean userUpVoted = false;
    @SerializedName("userDownVoted")
    private boolean userDownVoted = false;
    @SerializedName("createUserInfo")
    private CreateUserInfo createUserInfo;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getAliases() {
        return aliases;
    }

    public void setAliases(List<String> aliases) {
        this.aliases = aliases;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public String getPreviewText() {
        return previewText;
    }

    public void setPreviewText(String previewText) {
        this.previewText = previewText;
    }

    public List<?> getImgUrls() {
        return imgUrls;
    }

    public void setImgUrls(List<?> imgUrls) {
        this.imgUrls = imgUrls;
    }

    public List<?> getFiles() {
        return files;
    }

    public void setFiles(List<?> files) {
        this.files = files;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getSource() {
        return source;
    }

    public void setSource(int source) {
        this.source = source;
    }

    public Ext getExt() {
        return ext;
    }

    public void setExt(Ext ext) {
        this.ext = ext;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<Pages> getPages() {
        return pages;
    }

    public void setPages(List<Pages> pages) {
        this.pages = pages;
    }

    public List<Categories> getCategories() {
        return categories;
    }

    public void setCategories(List<Categories> categories) {
        this.categories = categories;
    }

    public List<Contributors> getContributors() {
        return contributors;
    }

    public void setContributors(List<Contributors> contributors) {
        this.contributors = contributors;
    }

    public int getUpvoteCount() {
        return upvoteCount;
    }

    public void setUpvoteCount(int upvoteCount) {
        this.upvoteCount = upvoteCount;
    }

    public int getDownvoteCount() {
        return downvoteCount;
    }

    public void setDownvoteCount(int downvoteCount) {
        this.downvoteCount = downvoteCount;
    }

    public boolean isUserUpVoted() {
        return userUpVoted;
    }

    public void setUserUpVoted(boolean userUpVoted) {
        this.userUpVoted = userUpVoted;
    }

    public boolean isUserDownVoted() {
        return userDownVoted;
    }

    public void setUserDownVoted(boolean userDownVoted) {
        this.userDownVoted = userDownVoted;
    }

    public CreateUserInfo getCreateUserInfo() {
        return createUserInfo;
    }

    public void setCreateUserInfo(CreateUserInfo createUserInfo) {
        this.createUserInfo = createUserInfo;
    }

    public static class Ext {
        @SerializedName("relatedEntries")
        private List<RelatedEntries> relatedEntries;
        @SerializedName("contacts")
        private List<Contacts> contacts;
        @SerializedName("links")
        private List<Links> links;

        public List<RelatedEntries> getRelatedEntries() {
            return relatedEntries;
        }

        public void setRelatedEntries(List<RelatedEntries> relatedEntries) {
            this.relatedEntries = relatedEntries;
        }

        public List<Contacts> getContacts() {
            return contacts;
        }

        public void setContacts(List<Contacts> contacts) {
            this.contacts = contacts;
        }

        public List<Links> getLinks() {
            return links;
        }

        public void setLinks(List<Links> links) {
            this.links = links;
        }

        public static class RelatedEntries {
            @SerializedName("id")
            private int id;
            @SerializedName("name")
            private String name;

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }

        public static class Contacts {
            @SerializedName("userInfo")
            private UserInfo userInfo;
            @SerializedName("desc")
            private String desc;

            public UserInfo getUserInfo() {
                return userInfo;
            }

            public void setUserInfo(UserInfo userInfo) {
                this.userInfo = userInfo;
            }

            public String getDesc() {
                return desc;
            }

            public void setDesc(String desc) {
                this.desc = desc;
            }

            public static class UserInfo {
                @SerializedName("accountUserId")
                private String accountUserId;
                @SerializedName("joySpaceUserId")
                private String joySpaceUserId;
                @SerializedName("tenantId")
                private String tenantId;
                @SerializedName("username")
                private String username;
                @SerializedName("name")
                private String name;
                @SerializedName("avatarUrl")
                private String avatarUrl;
                @SerializedName("positionName")
                private String positionName;
                @SerializedName("orgName")
                private String orgName;
                @SerializedName("orgCode")
                private String orgCode;
                @SerializedName("fullOrgName")
                private String fullOrgName;
                @SerializedName("fullOrgPath")
                private String fullOrgPath;
                @SerializedName("userType")
                private int userType;
                @SerializedName("valid")
                private int valid;

                public String getAccountUserId() {
                    return accountUserId;
                }

                public void setAccountUserId(String accountUserId) {
                    this.accountUserId = accountUserId;
                }

                public String getJoySpaceUserId() {
                    return joySpaceUserId;
                }

                public void setJoySpaceUserId(String joySpaceUserId) {
                    this.joySpaceUserId = joySpaceUserId;
                }

                public String getTenantId() {
                    return tenantId;
                }

                public void setTenantId(String tenantId) {
                    this.tenantId = tenantId;
                }

                public String getUsername() {
                    return username;
                }

                public void setUsername(String username) {
                    this.username = username;
                }

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public String getAvatarUrl() {
                    return avatarUrl;
                }

                public void setAvatarUrl(String avatarUrl) {
                    this.avatarUrl = avatarUrl;
                }

                public String getPositionName() {
                    return positionName;
                }

                public void setPositionName(String positionName) {
                    this.positionName = positionName;
                }

                public String getOrgName() {
                    return orgName;
                }

                public void setOrgName(String orgName) {
                    this.orgName = orgName;
                }

                public String getOrgCode() {
                    return orgCode;
                }

                public void setOrgCode(String orgCode) {
                    this.orgCode = orgCode;
                }

                public String getFullOrgName() {
                    return fullOrgName;
                }

                public void setFullOrgName(String fullOrgName) {
                    this.fullOrgName = fullOrgName;
                }

                public String getFullOrgPath() {
                    return fullOrgPath;
                }

                public void setFullOrgPath(String fullOrgPath) {
                    this.fullOrgPath = fullOrgPath;
                }

                public int getUserType() {
                    return userType;
                }

                public void setUserType(int userType) {
                    this.userType = userType;
                }

                public int getValid() {
                    return valid;
                }

                public void setValid(int valid) {
                    this.valid = valid;
                }
            }
        }

        public static class Links {
            @SerializedName("link")
            private String link;
            @SerializedName("name")
            private String name;

            public String getLink() {
                return link;
            }

            public void setLink(String link) {
                this.link = link;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }
    }

    public static class CreateUserInfo {
        @SerializedName("accountUserId")
        private String accountUserId;
        @SerializedName("joySpaceUserId")
        private String joySpaceUserId;
        @SerializedName("tenantId")
        private String tenantId;
        @SerializedName("username")
        private String username;
        @SerializedName("name")
        private String name;
        @SerializedName("avatarUrl")
        private String avatarUrl;
        @SerializedName("positionName")
        private String positionName;
        @SerializedName("orgName")
        private String orgName;
        @SerializedName("orgCode")
        private String orgCode;
        @SerializedName("fullOrgName")
        private String fullOrgName;
        @SerializedName("fullOrgPath")
        private String fullOrgPath;
        @SerializedName("userType")
        private int userType;
        @SerializedName("valid")
        private int valid;

        public String getAccountUserId() {
            return accountUserId;
        }

        public void setAccountUserId(String accountUserId) {
            this.accountUserId = accountUserId;
        }

        public String getJoySpaceUserId() {
            return joySpaceUserId;
        }

        public void setJoySpaceUserId(String joySpaceUserId) {
            this.joySpaceUserId = joySpaceUserId;
        }

        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getPositionName() {
            return positionName;
        }

        public void setPositionName(String positionName) {
            this.positionName = positionName;
        }

        public String getOrgName() {
            return orgName;
        }

        public void setOrgName(String orgName) {
            this.orgName = orgName;
        }

        public String getOrgCode() {
            return orgCode;
        }

        public void setOrgCode(String orgCode) {
            this.orgCode = orgCode;
        }

        public String getFullOrgName() {
            return fullOrgName;
        }

        public void setFullOrgName(String fullOrgName) {
            this.fullOrgName = fullOrgName;
        }

        public String getFullOrgPath() {
            return fullOrgPath;
        }

        public void setFullOrgPath(String fullOrgPath) {
            this.fullOrgPath = fullOrgPath;
        }

        public int getUserType() {
            return userType;
        }

        public void setUserType(int userType) {
            this.userType = userType;
        }

        public int getValid() {
            return valid;
        }

        public void setValid(int valid) {
            this.valid = valid;
        }
    }

    public static class Pages {
        @SerializedName("id")
        private String id;
        @SerializedName("name")
        private String name;
        @SerializedName("status")
        private int status;
        @SerializedName("pageType")
        private int pageType;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getPageType() {
            return pageType;
        }

        public void setPageType(int pageType) {
            this.pageType = pageType;
        }
    }

    public static class Categories {
        @SerializedName("id")
        private int id;
        @SerializedName("name")
        private String name;
        @SerializedName("level")
        private int level;
        @SerializedName("parentCategoryId")
        private int parentCategoryId;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getParentCategoryId() {
            return parentCategoryId;
        }

        public void setParentCategoryId(int parentCategoryId) {
            this.parentCategoryId = parentCategoryId;
        }
    }

    public static class Contributors {
        @SerializedName("accountUserId")
        private String accountUserId;
        @SerializedName("joySpaceUserId")
        private String joySpaceUserId;
        @SerializedName("tenantId")
        private String tenantId;
        @SerializedName("username")
        private String username;
        @SerializedName("name")
        private String name;
        @SerializedName("avatarUrl")
        private String avatarUrl;
        @SerializedName("positionName")
        private String positionName;
        @SerializedName("orgName")
        private String orgName;
        @SerializedName("orgCode")
        private String orgCode;
        @SerializedName("fullOrgName")
        private String fullOrgName;
        @SerializedName("fullOrgPath")
        private String fullOrgPath;
        @SerializedName("userType")
        private int userType;
        @SerializedName("valid")
        private int valid;

        public String getAccountUserId() {
            return accountUserId;
        }

        public void setAccountUserId(String accountUserId) {
            this.accountUserId = accountUserId;
        }

        public String getJoySpaceUserId() {
            return joySpaceUserId;
        }

        public void setJoySpaceUserId(String joySpaceUserId) {
            this.joySpaceUserId = joySpaceUserId;
        }

        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getPositionName() {
            return positionName;
        }

        public void setPositionName(String positionName) {
            this.positionName = positionName;
        }

        public String getOrgName() {
            return orgName;
        }

        public void setOrgName(String orgName) {
            this.orgName = orgName;
        }

        public String getOrgCode() {
            return orgCode;
        }

        public void setOrgCode(String orgCode) {
            this.orgCode = orgCode;
        }

        public String getFullOrgName() {
            return fullOrgName;
        }

        public void setFullOrgName(String fullOrgName) {
            this.fullOrgName = fullOrgName;
        }

        public String getFullOrgPath() {
            return fullOrgPath;
        }

        public void setFullOrgPath(String fullOrgPath) {
            this.fullOrgPath = fullOrgPath;
        }

        public int getUserType() {
            return userType;
        }

        public void setUserType(int userType) {
            this.userType = userType;
        }

        public int getValid() {
            return valid;
        }

        public void setValid(int valid) {
            this.valid = valid;
        }
    }
}
