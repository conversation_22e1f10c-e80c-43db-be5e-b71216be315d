package com.jd.oa.fragment.utils;

import android.app.Activity;

import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.fragment.js.hybrid.utils.ShareUtils;
import com.jd.oa.utils.Logger;

import org.json.JSONObject;

public class WebMemuUtil {

    private static final String SHARE_DATA = "shareData";
    private static final String TYPE_LIST = "typeList";
    private static final String IMAGE = "image";

    public static void showMenu(Activity activity, Object obj, String currentAppId) {
        if (obj == null) {
            showMoreMenu(activity, currentAppId, "");
        } else {
            try {
                JSONObject jsonObject = (JSONObject) obj;
                JSONObject params = jsonObject.optJSONObject(SHARE_DATA);
                String typeList = jsonObject.optString(TYPE_LIST);
                if (typeList.equals("[\"JDMESession\"]")) {
                    JSONObject jsonParams = new JSONObject();
                    jsonParams.put("shareInfo", params);
                    showMoreMenu(activity, currentAppId, jsonParams.toString());
                } else {
                    ShareUtils.share(activity, params, typeList, null, 2, currentAppId);
                }
            } catch (Exception e) {
                Logger.e("WebMenuUtil", "failed");
            }
        }
    }

    public static void showMoreMenu(Activity activity, String currentAppId, String params) {
        OpennessApi.shareOnlyExpand(activity, new AbsOpennessCallback() {
            @Override
            public void done(int code, String msg) {

            }
        }, currentAppId, params);
    }
}
