package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.ImBasic;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.dynamic.listener.ContactCallback;
import com.jd.oa.filetransfer.upload2.TransformFileUploadCache;
import com.jd.oa.filetransfer.upload2.TransformUploadFileType;
import com.jd.oa.filetransfer.upload2.TransformUploader;
import com.jd.oa.filetransfer.upload2.TransformUploaderListener;
import com.jd.oa.filetransfer.upload2.model.TransformUploadRequest;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.model.ImMsgBean;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.entity.UploadEntry;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd;
import com.jd.oa.network.utils.Utils;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StringUtils;
import com.jme.common.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import wendu.dsbridge.CompletionHandler;

import static android.app.Activity.RESULT_CANCELED;
import static android.app.Activity.RESULT_OK;
import static com.jd.oa.dynamic.listener.DynamicOperatorListener.ACTION_DISMISS_LOADING;
import static com.jd.oa.dynamic.listener.DynamicOperatorListener.ACTION_SHOW_LOADING;
import static com.jd.oa.dynamic.listener.DynamicOperatorListener.KEY_ACTION;
import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.sendCancel;
import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;
import static com.jd.oa.router.DeepLink.CONTACTS;


@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsIm implements JsInterface {

    public static final String DOMAIN = "im";
    /**
     * 扫描回调JS方法
     */
    public static final int REQUEST_CODE_SELECT_CONTACT = 597;

    public static final int REQUEST_CODE_SELECT_CONVERSATION = 598;

    private final JsSdkKit jsSdkKit;
    private final Activity activity;
    private final IWebPage webPage;
    private final JmImExt imExt;

    public JsIm(JsSdkKit jsSdkKit, IWebPage webPage) {
        this.jsSdkKit = jsSdkKit;
        this.webPage = webPage;
        imExt = new JmImExt(webPage);
        activity = AppBase.getTopActivity();
    }


    // 此方法仅用在 JDMEIMModule 中，保证不会使用到 jsSdkKit
    public JsIm(Activity activity) {
        jsSdkKit = null;
        this.activity = activity;
        this.webPage = null;
        imExt = new JmImExt(null);
    }

    @JavascriptInterface
    public void openContactsSelector(Object erpString, final CompletionHandler<Object> handler) {
        openContactSelector(erpString, handler);
    }

    @JavascriptInterface
    public void chooseContacts(Object params, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_102));
            return;
        }
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        JSONObject object = (JSONObject) params;
        int max = object.optInt("max");
        if (max == 0) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        Map<String, Object> map = new Gson().fromJson(params.toString(), new TypeToken<HashMap<String, Object>>() {
        }.getType());
        ImBasic.openContactSelector(activity, map, (data, resultCode) -> {
            if (resultCode == RESULT_OK && data != null) {
                try {
                    @SuppressWarnings("unchecked")
                    ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                    JSONArray result = new JSONArray();
                    if (selected != null) {
                        for (int i = 0; i < selected.size(); i++) {
                            MemberEntityJd entity = selected.get(i);
                            JSONObject member = new JSONObject();
//                            member.put("erp", entity.mId);
                            member.put("userId", entity.mId);
                            member.put("avatar", entity.mAvatar);
                            member.put("appId", entity.mApp);
                            member.put("userName", entity.mName);
                            member.put("email", entity.mEmail);
                            result.put(member);
                        }
                    }
                    JSONObject contacts = new JSONObject();
                    contacts.put("contacts", result);
                    handler.complete(JSTools.success(contacts));
                } catch (Throwable e) {
                    handler.complete(JSTools.error(new JSONObject()));
                    e.printStackTrace();
                }
            } else {
                handler.complete(JSTools.error(new JSONObject()));
            }
        });
    }

    /**
     * @param erpString {
     *                  "selected" : [
     *                  {
     *                  "erp" : "chenqizheng1",
     *                  "appId" : "ee"
     *                  },
     *                  {
     *                  "erp" : "chenjie206",
     *                  "appId" : "ee"
     *                  }
     *                  ],
     *                  "title" : "参与人",
     *                  "maxNum" : 50
     *                  }
     */
    @JavascriptInterface
    public void openContactSelector(Object erpString, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        if (erpString == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        if (!useOldInterface("contactsSelector")) {
            JSONObject jo = (JSONObject) erpString;
            Map<String, Object> map = new Gson().fromJson(
                    jo.toString(), new TypeToken<HashMap<String, Object>>() {}.getType());

            ImBasic.openContactSelector(activity, map, (data, resultCode) -> {
                if (resultCode == RESULT_OK && data != null) {
                    try {
                        @SuppressWarnings("unchecked")
                        ArrayList<MemberEntityJd> selected =
                                (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                        if (selected == null) {
                            sendCancel(handler);
                            return;
                        }
                        ImBasic.contactInfoDialogController(activity,
                                new HashMap<String, Object>() {
                                    {
                                        put(KEY_ACTION, ACTION_SHOW_LOADING);
                                    }
                                });
                        getJsonArray(selected, result -> {
                            ImBasic.contactInfoDialogController(activity,
                                    new HashMap<String, Object>() {
                                        {
                                            put(KEY_ACTION, ACTION_DISMISS_LOADING);
                                        }
                                    });
                            handler.complete(result);
                        });
                    } catch (Throwable e) {
                        sendCancel(handler);
                        e.printStackTrace();
                    }
                } else {
                    sendCancel(handler);
                }
            });
            return;
        }

        ArrayList<MemberEntityJd> erpList = new ArrayList<>();
        String title = activity.getResources().getString(R.string.me_cmn_h5_select_contact_default_title);
        int maxNum = Integer.MAX_VALUE;
        JSONArray excludeAppIds = null;

        try {
            JSONObject jo = (JSONObject) erpString;
            JSONArray arr = jo.getJSONArray("selected");
            excludeAppIds = jo.optJSONArray("excludeAppIds");
            for (int x = 0; x < arr.length(); x++) {
                JSONObject object = arr.getJSONObject(x);
                String erp = object.getString("erp");
                MemberEntityJd jd = new MemberEntityJd();
                jd.mId = erp;
                if (object.has("appId")) {
                    jd.mApp = object.getString("appId");
                } else {
                    jd.mApp = AppBase.iAppBase.getTimlineAppId();
                }
                erpList.add(jd);
            }
            if (jo.has("maxNum")) {
                maxNum = Math.max(jo.getInt("maxNum"), 0);
            }
            title = jo.getString("title");
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(MELogUtil.TAG_JS, "JsIm openContactSelector error", e);
        }

        try {
            MELogUtil.localI(MELogUtil.TAG_JS, "JsIm openContactSelector params: " + erpString.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        Intent intent = Router.build(CONTACTS).getIntent(activity);
        intent.putExtra("extra_contact", erpList);
        intent.putExtra("title", title);
        intent.putExtra("max", maxNum);
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        intent.putExtra("extra_specify_appId", new ArrayList<>(TenantConfigBiz.INSTANCE.getCollaborativelyApps()));
        intent.putExtra("extra_exclude_appId", ImBasic.getExcludeAppIds(excludeAppIds));
        jsSdkKit.addHandler(REQUEST_CODE_SELECT_CONTACT, handler);
        activity.startActivityForResult(intent, REQUEST_CODE_SELECT_CONTACT);
    }

    private static void getJsonArray(ArrayList<MemberEntityJd> selected, ContactCallback callback) {
        if (selected == null) {
            return;
        }
        if (selected.isEmpty()) {
            callback.call(new JSONArray());
            return;
        }
        AtomicInteger completeEntityNum = new AtomicInteger();
        JSONArray result = new JSONArray();
        for (int i = 0; i < selected.size(); i++) {
            MemberEntityJd entity = selected.get(i);
            JSONObject object = new JSONObject();
            // 无论联系人信息中是否有 email，都走云端接口请求联系人的完整信息，
            // 联系人的邮箱存在修改的情况（端缓存的 email 和云端修改后的 email 可能不一致）。
            AppJoint.service(ImDdService.class)
                    .getContactInfoFromNet(entity.getApp(), entity.getId(), new Callback<MemberEntityJd>() {
                        @Override
                        public void onSuccess(MemberEntityJd bean) {
                            try {
                                object.put("erp", bean.mId);
                                object.put("avatar", bean.mAvatar);
                                object.put("appId", bean.mApp);
                                object.put("name", bean.mName);
                                object.put("email", bean.mEmail);
                            } catch (JSONException e) {
                                onFail();
                                e.printStackTrace();
                            }
                            result.put(object);
                            completeEntityNum.getAndIncrement();
                            if (completeEntityNum.get() == selected.size()) {
                                callback.call(result);
                            }
                        }

                        @Override
                        public void onFail() {
                            completeEntityNum.getAndDecrement();
                        }
                    });
        }
    }

    @JavascriptInterface
    public void sendShareCardMessage(Object json) {
        if(webPage != null){
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "sendShareCardMessage", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer integer, @Nullable String error) {
                    if (success) {
                        sendShareCardMessageInner(json);
                    }
                }
            });
        } else {
            sendShareCardMessageInner(json);
        }
    }

    public void sendShareCardMessageInner(Object json) {
        String type = "0";
        String pin = "";
        String app = "";
        String sessionTypeStr = "";

        try {
            JSONObject jsonParam = (JSONObject) json;
            if (jsonParam.has("type")) {
                type = jsonParam.optString("type");
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsIm sendShareCardMessage params: " + jsonParam.toString());
            if ("0".equals(type)) {
                JSONObject jsonObject = ((JSONObject) json).optJSONObject("session");
                if (jsonObject == null) {
                    return;
                }
                String sessionKey = jsonObject.optString("sessionKey");
                if (jsonObject.has("pin")) {
                    pin = jsonObject.optString("pin");
                }
                if (jsonObject.has("app")) {
                    app = jsonObject.optString("app");
                }
                if (jsonObject.has("sessionType")) {
                    sessionTypeStr = jsonObject.optString("sessionType");
                }
                AppJoint.service(ImDdService.class).sendShareLinkMsg(sessionKey, pin, app, Integer.parseInt(sessionTypeStr),
                        jsonParam.optString("url"),
                        jsonParam.optString("title"),
                        jsonParam.optString("content"),
                        jsonParam.optString("icon"),
                        jsonParam.optString("source"),
                        jsonParam.optString("sourceIcon"),
                        jsonParam.optString("category"));
            } else {
                String icon = "";
                String url = "";
                String title = "";
                String content = "";
                if (jsonParam.has("icon")) {
                    icon = jsonParam.optString("icon");
                }
                if (jsonParam.has("title")) {
                    title = jsonParam.optString("title");
                }
                if (jsonParam.has("content")) {
                    content = jsonParam.optString("content");
                }
                if (jsonParam.has("url")) {
                    url = jsonParam.optString("url");
                }

                AppJoint.service(ImDdService.class).share(
                        activity,
                        title,
                        content,
                        url,
                        icon,
                        "jdim_share_link");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void openContactsCard(Object options, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_102));
            return;
        }
        if (JSTools.isEmpty(options)) {
            if (handler != null) {
                handler.complete(JSTools.paramsError(new JSONObject()));
            }
            return;
        }
        JSONObject jsonObject = (JSONObject) options;
        MELogUtil.localI(MELogUtil.TAG_JS, "JsIm openContactsCard params: " + jsonObject.toString());
        try {
//            "appId", "userId", "teamId"
            JSONObject keyJson = (JSONObject) options;
            String erp = keyJson.optString("userId");
            if (StringUtils.isEmpty(erp)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            String appId = keyJson.optString("appId", ImDdService.APP_ID_JDME_CHINA);
            if (useOldInterface("openUserCard")) {
                ImDdService imDdService = AppJoint.service(ImDdService.class);
                imDdService.showContactDetailInfo(activity, erp);//仿照跳转聊天页面
            } else {
                if (TextUtils.isEmpty(appId)) {
                    appId = ImDdService.APP_ID_JDME_CHINA;
                }
                ImBasic.openContactInfo(activity, appId, erp);
            }
            handler.complete(JSTools.success(new JSONObject()));
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_102));
            MELogUtil.localI(MELogUtil.TAG_JS, "JsIm openContactsCard error params: " + jsonObject.toString(), e);
        }
    }

    @JavascriptInterface
    public void openGroupChat(Object options, final CompletionHandler<Map<String, Object>> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new HashMap<>()));
            return;
        }
        if (options == null) {
            handler.complete(JSTools.paramsError(new HashMap<>()));
            return;
        }
        final ImDdService ddService = AppJoint.service(ImDdService.class);

        JSONObject jsonObject = (JSONObject) options;
        final String groupId = jsonObject.optString("groupId");
        if (!TextUtils.isEmpty(groupId)) {
            if (useOldInterface("openGroupChat")) {
                ddService.openChat(activity, null, null, groupId, new LoadDataCallback<Void>() {
                    @Override
                    public void onDataLoaded(Void aVoid) {
                        handler.complete(JSTools.success(OpenGroupResult.success(groupId)));
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        Map<String, Object> result = OpenGroupResult.failure(i, s);
                        if (i == 1) {
                            result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002014);
                        } else if (i == 2) {
                            result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002015);
                        } else {
                            result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_102);
                        }
                        handler.complete(result);
                    }
                });
            } else {
                ImBasic.openGroupChat(activity, groupId, (data, resultCode) -> {
                    Map<String, Object> result = new HashMap<>();
                    try {
                        if (resultCode == RESULT_OK) {
                            result.put("statusCode", 0);
                            result = JSTools.success(result);
                        } else if (resultCode == RESULT_CANCELED) {
                            result.put("statusCode", 1);
                            if (data.hasExtra("msg")) {
                                result.put("message", data.getStringExtra("msg"));
                            }
                            if (data.hasExtra("statusCode")) {
                                result.put("statusCode", data.getIntExtra("statusCode", 1));
                                resultCode = (int) result.get("statusCode");
                            }

                            if (resultCode == 1) {
                                result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002014);
                            } else if (resultCode == 2) {
                                result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002015);
                            } else if (resultCode == 3) {
                                result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_302);
                            } else {
                                result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_102);
                            }
                        }
                        handler.complete(result);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
            return;
        }

        try {
            String appId = jsonObject.optString("appId");
            String sourceId = jsonObject.optString("sourceID");
            String key = jsonObject.optString("rKey");
            String teamId = jsonObject.optString("teamId");
            String groupName = jsonObject.optString("groupName");
            int groupType = jsonObject.optInt("groupType", 0);
            boolean secret = jsonObject.optBoolean("secret");
            boolean canSearch = jsonObject.optBoolean("canSearch");
            boolean canEdit = jsonObject.optBoolean("canEdit");
            JSONArray members = jsonObject.optJSONArray("members");
            final boolean open = jsonObject.optBoolean("open", true);

            if (TextUtils.isEmpty(sourceId)) {
                handler.complete(JSTools.paramsError(OpenGroupResult.failure(1, "sourceId is empty")));
                return;
            }

            if (TextUtils.isEmpty(key)) {
                handler.complete(JSTools.paramsError(OpenGroupResult.failure(1, "rKey is empty")));
                return;
            }

            if (members == null || members.length() == 0) {
                handler.complete(JSTools.paramsError(OpenGroupResult.failure(1, "members is empty")));
                return;
            }
            ArrayList<MemberEntityJd> list = new ArrayList<>();
            for (int i = 0; i < members.length(); i++) {
                JSONObject object = members.getJSONObject(i);
                MemberEntityJd entity = new MemberEntityJd();
                entity.mName = object.optString("name");
                entity.mApp = object.optString("appId");
                // 兼容新字段app
                if (TextUtils.isEmpty(entity.mApp)) {
                    entity.mApp = object.optString("app", AppBase.iAppBase.getTimlineAppId());
                }
                entity.mId = object.optString("userId");
                // 兼容新字段erp
                if (TextUtils.isEmpty(entity.mId)) {
                    entity.mId = object.optString("erp");
                }
                entity.mAvatar = object.optString("avatar");
                list.add(entity);
            }

            ddService.createGroup(activity, sourceId, key, list, groupType, groupName, canSearch, canEdit, new LoadDataCallback<String>() {
                @Override
                public void onDataLoaded(final String gid) {
                    if (!open) {
                        handler.complete(JSTools.success(OpenGroupResult.success(gid)));
                    } else {
                        ddService.openChat(activity, null, null, gid, new LoadDataCallback<Void>() {
                            @Override
                            public void onDataLoaded(Void aVoid) {
                                handler.complete(JSTools.success(OpenGroupResult.success(gid)));
                            }

                            @Override
                            public void onDataNotAvailable(String s, int resultCode) {
                                Map<String, Object> result = OpenGroupResult.failure(resultCode, s);
                                if (resultCode == 1) {
                                    result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002014);
                                } else if (resultCode == 2) {
                                    result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002015);
                                } else if (resultCode == 3) {
                                    result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_302);
                                } else {
                                    result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_102);
                                }
                                handler.complete(result);
                            }
                        });
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int resultCode) {
                    Map<String, Object> result = OpenGroupResult.failure(resultCode, s);
                    if (resultCode == 1) {
                        result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002014);
                    } else if (resultCode == 2) {
                        result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_1002015);
                    } else if (resultCode == 3) {
                        result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_302);
                    } else {
                        result = JSTools.appendErrCodeAndMsg(result, JSErrCode.ERROR_102);
                    }
                    handler.complete(result);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(OpenGroupResult.failure(1, e.getMessage())));
        }
    }

    @JavascriptInterface
    public void openSingleChat(Object options, final CompletionHandler<Map<String, Object>> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new HashMap<>()));
            return;
        }
        if (options == null) {
            handler.complete(JSTools.paramsError());
            return;
        }

        JSONObject jsonObject = (JSONObject) options;
        String appId = jsonObject.optString("appId", ImDdService.APP_ID_JDME_CHINA);
        String teamId = jsonObject.optString("teamId");
        String userId = jsonObject.optString("userId");
        //boolean secret = jsonObject.optBoolean("secret", false);

        if (TextUtils.isEmpty(appId)) {
            Map<String, Object> result = new HashMap<>();
            result.put("statusCode", 1);
            result.put("message", "appId is empty");
            handler.complete(JSTools.paramsError(result));
            return;
        }

        if (TextUtils.isEmpty(userId)) {
            Map<String, Object> result = new HashMap<>();
            result.put("statusCode", 1);
            result.put("message", "userId is empty");
            handler.complete(JSTools.paramsError(result));
            return;
        }

        if (useOldInterface("openSingleChat")) {
            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.openChat(activity, appId, userId, null, new LoadDataCallback<Void>() {
                @Override
                public void onDataLoaded(Void aVoid) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("statusCode", 0);
                    handler.complete(JSTools.success(result));
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("statusCode", 1);
                    result.put("message", s);
                    handler.complete(JSTools.error(result));
                }
            });
        } else {
            ImBasic.openSingleChat(activity, appId, userId, (data, resultCode) -> {
//                Map<String, Object> result = new HashMap<>();
//                if (resultCode == RESULT_OK) {
//                    result.put("statusCode", 0);
//                    result = JSTools.success(result);
//                } else if (resultCode == RESULT_CANCELED) {
//                    result.put("statusCode", 1);
//                    if (data.hasExtra("msg")) {
//                        result.put("message", data.getStringExtra("msg"));
//                    }
//                    result = JSTools.error(result);
//                } else {
//                    result = JSTools.error(result);
//                }
//                handler.complete(result);
            });
            handler.complete(JSTools.success(new HashMap<>()));
        }
    }


    @JavascriptInterface
    public void joinGroup(Object options, final CompletionHandler<Map<String, Object>> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (options == null) {
            return;
        }
        JSONObject jsonObject = (JSONObject) options;
        String groupId = jsonObject.optString("groupId");
        String code = jsonObject.optString("code");
        ImDdService ddService = AppJoint.service(ImDdService.class);
        ddService.joinGroup(groupId, code, new LoadDataCallback<Void>() {
            @Override
            public void onDataLoaded(Void aVoid) {
                Map<String, Object> result = new HashMap<>();
                result.put("statusCode", 0);
                handler.complete(result);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Map<String, Object> result = new HashMap<>();
                result.put("statusCode", i);
                result.put("message", s);
                handler.complete(result);
            }
        });

        String appId = "unknown";
        if (activity instanceof FunctionActivity) {
            Fragment fragment = ((FunctionActivity) activity).getFragment();
            if (fragment instanceof WebFragment2) {
                appId = ((WebFragment2) fragment).getCurrentAppId();
            }
        }
        Map<String, String> params = new HashMap<>();
        params.put("app", PreferenceManager.UserInfo.getTimlineAppID());
        params.put("user", PreferenceManager.UserInfo.getUserName());
        params.put("team", PreferenceManager.UserInfo.getTenantCode());
        params.put("groupid", groupId);
        params.put("source", "h5");
        params.put("appid", appId);
        JDMAUtils.clickEvent(JDMAConstants.Mobile_Page_OpenAPI_IMApi, JDMAConstants.Mobile_Event_OpenAPI_IMApi_JoinGroup, params);
    }

    @JavascriptInterface
    public void sendMessageCard(Object options, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(createResponse(101, "activity exception")));
            return;
        }
        if (options == null) {
            handler.complete(JSTools.paramsError(createResponse(104, "params options is null")));
            return;
        }
        try {
            JSONObject jsonMsgData = (JSONObject) options;
            JSONObject jsonObject = jsonMsgData.optJSONObject("messageData");
            if (!jsonObject.has("type") || !jsonObject.has("data")) {
                handler.complete(JSTools.paramsError(createResponse(104, "params type or data exception")));
                return;
            }
            int type = jsonObject.optInt("type", 0);
            if (type == 0 || type > 3) {
                handler.complete(JSTools.paramsError(createResponse(104, "params type exception")));
                return;
            }
            //add 7.9.10 解决返回格式不对，导致前端读取不到errCode
            String val = ConfigurationManager.get().getEntry("android.sendMessageCard.old.return", "0");
            boolean isOldReturn = "1".equals(val);
            JSONObject data = jsonObject.optJSONObject("data");
            ImDdService ddService = AppJoint.service(ImDdService.class);
            LoadDataCallback loadDataCallback = new LoadDataCallback<Void>() {
                @Override
                public void onDataLoaded(Void aVoid) {
                    if (isOldReturn) {
                        Map<String, Object> result = new HashMap<>();
                        try {
                            result.put("statusCode", 0);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        handler.complete(JSTools.success(result));
                    } else {
                        handler.complete(JSTools.success());
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    if (isOldReturn) {
                        Map<String, Object> result = new HashMap<>();
                        try {
                            result.put("statusCode", 1);
                            result.put("message", s);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        switch (i) {
                            case -1:
                                handler.complete(JSTools.error(result));
                                break;
                            case -2:
                                handler.complete(JSTools.cancel(result));
                                break;
                        }
                    } else {
                        try {
                            JSONObject resp = new JSONObject();
                            resp.put("errCode", 1);
                            resp.put("errMsg", s);
                            handler.complete(resp);
                        } catch (JSONException e) {
                            handler.complete(JSTools.error());
                        }
                    }
                }
            };
            //添加参数
            if (jsonMsgData.has("customPage")) {
                JSONObject customPage = jsonMsgData.optJSONObject("customPage");
                if (customPage != null) {
                    String customPageAppId = customPage.optString("appId", "");
                    String customPageUrl = customPage.optString("url", "");
                    if (!TextUtils.isEmpty(customPageAppId) && data != null) { //appId为必填项
                        data.put("customPageAppId", customPageAppId);
                        data.put("customPageUrl", customPageUrl);
                    }
                }
            }
            switch (type) {
                case 1: // 文本类型
                    ddService.sendTextCard(data.toString(), loadDataCallback);
                    break;
                case 2: // 链接类型
                    ddService.sendShareLink(data.toString(), loadDataCallback);
                    break;
                default: // jue卡片
                    ddService.sendJueCard(data.toString(), loadDataCallback);
                    break;
            }
            Map<String, String> params = new HashMap<>();
            params.put("options", jsonObject.toString());
            JDMAUtils.clickEvent(JDMAConstants.Mobile_Page_OpenAPI_IMApi, JDMAConstants.Mobile_Event_OpenAPI_IMApi_SendMessageCard, params);
        } catch (Exception e) {
            handler.complete(JSTools.error(createResponse(104, "params exception")));
        }
    }

    @JavascriptInterface
    public void sendMessageCardToGroup(Object options, final CompletionHandler<Object> handler) {
        if(webPage != null){
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "sendMessageCardToGroup", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        imExt.sendMessageCardToGroup(options, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_106));
                        }
                    }
                }
            });
        } else {
            imExt.sendMessageCardToGroup(options, handler);
        }
    }

    @JavascriptInterface
    public void openProfile(Object params, final CompletionHandler<Object> handler) {
        openContactsCard(params, handler);
    }

    @JavascriptInterface
    public void openChat(Object params, final CompletionHandler<Object> handler) {
        openSingleChat(params, new CompletionHandler<Map<String, Object>>() {

            @Override
            public void complete(Map<String, Object> retValue) {
                JSONObject object = new JSONObject();
                try {
                    for (Map.Entry<String, Object> entry : retValue.entrySet()) {
                        String key = entry.getKey();
                        Object value = entry.getValue();
                        object.put(key, value);
                    }
                    handler.complete(object);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void complete() {

            }

            @Override
            public void setProgressData(Map<String, Object> value) {

            }
        });
    }

    @JavascriptInterface
    public void chooseChat(Object params, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        ImDdService ddService = AppJoint.service(ImDdService.class);
        JSONObject object = (JSONObject) params;
        int max = object.optInt("max");
        boolean multiSelect = object.optBoolean("multiSelect");
        //以下参数接口暂不支持
        String title = object.optString("title");//TODO:原生IM接口待支持
        boolean showConfirm = object.optBoolean("showConfirm");//TODO:原生IM接口待支持
        String confirmTitle = object.optString("confirmTitle");//TODO:原生IM接口待支持
        String confirmDesc = object.optString("confirmDesc");//TODO:原生IM接口待支持
        String confirmText = object.optString("confirmText");//TODO:原生IM接口待支持
        String confirmCancelText = object.optString("confirmCancelText");//TODO:原生IM接口待支持

        MemberListEntityJd entityJd = new MemberListEntityJd();
        entityJd.setSelectMode(multiSelect ? MemberListEntityJd.SELECT_MODE_MULTI : MemberListEntityJd.SELECT_MODE_SINGLE);
        entityJd.setFrom(UIHelperConstantJd.TYPE_SHARE)
                .setShowConstantFilter(false)
                .setShowSelf(true)
                .setShowOptionalFilter(false)
                .setMaxNum(max);
        ddService.gotoMemberList(activity, REQUEST_CODE_SELECT_CONVERSATION, entityJd, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> bean) {
                JSONArray members = new JSONArray();
                try {
                    for (int i = 0; i < bean.size(); i++) {
                        JSONObject item = new JSONObject();
                        MemberEntityJd member = bean.get(i);
                        String id = member.getId();
                        String appId = member.getApp();
                        boolean isGroup = member.isGroup();
                        String groupId = member.getId();
                        String name = member.getName();
                        String avatar = member.getAvatar();
                        if (isGroup) {
                            item.put("groupId", id == null ? "" : id);
                            item.put("sessionType", 1);
                            item.put("name", name == null ? "" : name);
                            item.put("avatar", avatar == null ? "" : avatar);
                        } else {
                            item.put("userId", id == null ? "" : id);
                            item.put("sessionType", 0);
                            item.put("appId", appId == null ? "" : appId);
                            item.put("name", name == null ? "" : name);
                            item.put("avatar", avatar == null ? "" : avatar);
                        }
                        members.put(item);
                    }
                    JSONObject data = new JSONObject();
                    data.put("data", members);
                    handler.complete(JSTools.success(data));
                } catch (Exception e) {
                    e.printStackTrace();
                    handler.complete(JSTools.error(new JSONObject()));
                }
            }

            @Override
            public void onFail() {
                handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_1003003));
            }
        });
    }

    @JavascriptInterface
    public void getShortcutData(Object args, CompletionHandler<Object> handler) {
        ImDdService ddService = AppJoint.service(ImDdService.class);
        String selectedMessage = ddService.getQuickMenuSelectedMessage();
        try {
            JSONObject result = new JSONObject();
            ImMsgBean imMsgBean = new Gson().fromJson(selectedMessage, ImMsgBean.class);
            result.put("data", ImBasic.convertImMessage(imMsgBean));
            handler.complete(JSTools.success(result));
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void getShortcutDataByActionId(Object args, CompletionHandler<Object> handler) {
        if (args == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        try {
            JSONObject params = (JSONObject) args;
            String actionId = params.getString("actionId");
            ImDdService ddService = AppJoint.service(ImDdService.class);
            String selectedMessage = ddService.getQuickMenuSelectedMessage(actionId);
            JSONObject result = new JSONObject();
            JSONObject messageObject = new JSONObject(selectedMessage);
            result.put("data", messageObject);
            handler.complete(JSTools.success(result));
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void getShortcutFile(Object args, final CompletionHandler<Object> handler) {
        if (args == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        try {
            JSONObject params = (JSONObject) args;
            String fileUUID = params.getString("fileUUID");
            if (StringUtils.isEmptyWithTrim(fileUUID)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (!Utils.isNetworkAvailable(AppBase.getTopActivity())) {
                if (handler != null) {
                    handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_303));
                }
            }
            TransformFileUploadCache localCache = TransformFileUploadCache.getInstance();
            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.getUploadFilePathOrUrl(fileUUID, new LoadDataCallback<UploadEntry>() {
                @Override
                public void onDataLoaded(UploadEntry uploadEntry) {
                    String appKey = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.IM);
                    String taskId = AppBase.iAppBase.getTimlineAppId() + fileUUID;
                    String fileName = uploadEntry.getName();
                    String fileType = uploadEntry.getFileType();
                    if ("img".equals(fileType)) {
                        fileName = fileUUID + "";
                    }
                    TransformUploaderListener listener = new TransformUploaderListener(taskId, appKey, fileUUID) {
                        @Override
                        public void callback(boolean isSuccess, JSONObject jsonObject) {
                            if (isSuccess) {
                                handler.complete(JSTools.success(jsonObject));
                            } else {
                                handler.complete(JSTools.error(jsonObject));
                            }
                        }
                    };
                    if (uploadEntry.getType() == TransformUploadFileType.LOCAL_FILE) {
                        TransformUploadRequest uploadRequest = TransformUploader.getInstance().createLocalFileRequest(appKey, taskId, uploadEntry.getPath());
                        uploadRequest.setTempPath(activity.getExternalCacheDir() + File.separator + taskId);
                        TransformUploader.initUpload(uploadRequest, listener);
                    } else if (uploadEntry.getType() == TransformUploadFileType.CLOUD_FILE) {
                        TransformUploadRequest uploadRequest = TransformUploader.getInstance().createCloudFileRequest(appKey, taskId, uploadEntry.getUrl(), fileName);
                        TransformUploader.initUpload(uploadRequest, listener);
                    } else {
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_215));
                }
            });
        } catch (Exception e) {
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void getShortcutFileByActionId(Object args, final CompletionHandler<Object> handler) {
        if (args == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        try {
            JSONObject params = (JSONObject) args;
            String actionId = params.getString("actionId");
            String fileUUID = params.getString("fileUUID");
            if (StringUtils.isEmptyWithTrim(fileUUID)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (!Utils.isNetworkAvailable(AppBase.getTopActivity())) {
                if (handler != null) {
                    handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_303));
                }
            }
            TransformFileUploadCache localCache = TransformFileUploadCache.getInstance();
            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.getUploadFilePathOrUrl(actionId, fileUUID, new LoadDataCallback<UploadEntry>() {
                @Override
                public void onDataLoaded(UploadEntry uploadEntry) {
                    String appKey = "UCAL6USOcjZGwm48nRmyqC9yB";
                    String taskId = AppBase.iAppBase.getTimlineAppId() + fileUUID;
                    String fileName = uploadEntry.getName();
                    String fileType = uploadEntry.getFileType();
                    if ("img".equals(fileType)) {
                        fileName = fileUUID + "";
                    }
                    TransformUploaderListener listener = new TransformUploaderListener(taskId, appKey, fileUUID) {
                        @Override
                        public void callback(boolean isSuccess, JSONObject jsonObject) {
                            if (isSuccess) {
                                handler.complete(JSTools.success(jsonObject));
                            } else {
                                handler.complete(JSTools.error(jsonObject));
                            }
                        }
                    };
                    if (uploadEntry.getType() == TransformUploadFileType.LOCAL_FILE) {
                        TransformUploadRequest uploadRequest = TransformUploader.getInstance().createLocalFileRequest(appKey, taskId, uploadEntry.getPath());
                        uploadRequest.setTempPath(activity.getExternalCacheDir() + File.separator + taskId);
                        TransformUploader.initUpload(uploadRequest, listener);
                    } else if (uploadEntry.getType() == TransformUploadFileType.CLOUD_FILE) {
                        TransformUploadRequest uploadRequest = TransformUploader.getInstance().createCloudFileRequest(appKey, taskId, uploadEntry.getUrl(), fileName);
                        TransformUploader.initUpload(uploadRequest, listener);
                    } else {
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_215));
                }
            });
        } catch (Exception e) {
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void getShortcutFileProgress(Object args, final CompletionHandler<Object> handler) {
        if (args == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        try {
            JSONObject params = (JSONObject) args;
            String fileUUID = params.optString("fileUUID");
            if (StringUtils.isEmptyWithTrim(fileUUID)) {
                fileUUID = params.optString("uploadId");
            }
            if (StringUtils.isEmptyWithTrim(fileUUID)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (!Utils.isNetworkAvailable(AppBase.getTopActivity())) {
                if (handler != null) {
                    handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_303));
                    return;
                }
            }
            String taskId = AppBase.iAppBase.getTimlineAppId() + fileUUID;
            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.getUploadFilePathOrUrl(fileUUID, new LoadDataCallback<UploadEntry>() {
                @Override
                public void onDataLoaded(UploadEntry uploadEntry) {
                    TransformFileUploadCache uploadCache = TransformFileUploadCache.getInstance();
                    String url = TransformFileUploadCache.getInstance().findUrlByTaskId(taskId);
                    JSONObject cache = uploadCache.findUrlByUUID(taskId);

                    boolean needQueryApi = false;
                    if (TransformFileUploadCache.getInstance().contains(taskId)) {
                        int findType = TransformFileUploadCache.getInstance().findTypeByUUID(taskId);
                        String uploadId = TransformFileUploadCache.getInstance().findUploadIdByUUID(taskId);
                        switch (findType) {
                            case 0:
                                //分片上传
                                String fileUrl = cache.optString("fileUrl");
                                boolean isUploading = cache.optBoolean("isUploading");
                                int progress = cache.optInt("progress");
                                try {
                                    JSONObject response = new JSONObject();
                                    response.put("fileUrl", fileUrl);
                                    response.put("isUploading", isUploading);
                                    if (isUploading) {
                                        response.put("progress", progress);
                                    }
                                    handler.complete(JSTools.success(response));
                                } catch (Exception e) {
                                    handler.complete(JSTools.error(new JSONObject()));
                                }
                                //恢复任务
                                TransformUploader.restoreAllTask(null);
//                                needQueryApi = true;
                                break;
                            case 1:
                                //秒传上传 取本地结果  本地文件结果过期 不拦截
                                if (!"".equals(url) && !uploadCache.isExpired(taskId)) {
                                    JSONObject response = new JSONObject();
                                    try {
                                        response.put("fileUrl", cache.optString("fileUrl"));
                                        response.put("isUploading", cache.optBoolean("isUploading"));
                                        if (cache.optBoolean("isUploading")) {
                                            response.put("progress", cache.optInt("progress"));
                                        }
                                        handler.complete(JSTools.success(response));
                                    } catch (JSONException e) {
                                        throw new RuntimeException(e);
                                    }
                                } else {
                                    needQueryApi = true;
                                }
                                break;
                            case 2:
                                //异步上传 直接走查询接口 如果本地缓存不为空 且缓存事件不超过 不走查询接口
                                needQueryApi = true;
                                break;
                        }
                    }
                    if (needQueryApi) {
                        String appKey = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.IM);
                        String uploadId = uploadCache.findUploadIdByUUID(taskId);
                        TransformUploader.getInstance().queryResult(taskId, appKey, uploadId, new TransformUploader.OnTransformResultListener() {
                            @Override
                            public void onSuccess(boolean isUploading, int progress, String url) {
                                try {
                                    JSONObject response = new JSONObject();
                                    response.put("isUploading", isUploading);
                                    response.put("fileUrl", url);
                                    if (isUploading) {
                                        response.put("progress", progress);
                                    }
                                    handler.complete(JSTools.success(response));
                                } catch (Exception e) {
                                    handler.complete(JSTools.error(new JSONObject()));
                                }
                            }

                            @Override
                            public void onFail(int code, String message) {
                                JSONObject jsonObject = new JSONObject();
                                switch (code) {
                                    case -303:
                                        handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_303));
                                        break;
                                    case -305:
                                        handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_305));
                                        break;
                                    default:
                                        handler.complete(JSTools.error(new JSONObject()));
                                        break;
                                }
                            }
                        });
                    } else {
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    handler.complete(JSTools.error(new JSONObject()));
                }
            });
        } catch (Exception e) {
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void sendAISessionInfo(Object args, final CompletionHandler<Object> handler) {
        if(webPage != null){
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "sendAISessionInfo", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        sendAISessionInfoInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_106));
                        }
                    }
                }
            });
        } else {
            sendAISessionInfoInner(args, handler);
        }
    }

    public void sendAISessionInfoInner(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject params = (JSONObject) args;
            String sessionId = params.optString("sessionId");
            String reqId = params.optString("reqId");
            long time = params.optLong("time");
            int sessionType = params.optInt("sessionType");
            String traceId = params.optString("traceId");
            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.sendAISessionInfo(sessionId, reqId, time, sessionType, traceId, new Callback<String>() {
                @Override
                public void onSuccess(String bean) {
                    handler.complete(JSTools.success(new JSONObject()));
                }

                @Override
                public void onFail() {
                    handler.complete(JSTools.error(new JSONObject()));
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void callback(CompletionHandler<Map<String, Object>> handler, int code, String message) {
        if (handler == null) {
            return;
        }
        String STATUS_CODE = "statusCode";
        String MESSAGE = "message";
        Map<String, Object> map = new HashMap<>();
        map.put(STATUS_CODE, code);
        map.put(MESSAGE, message);
        handler.complete(map);
    }

    public Map<String, Object> createResponse(int code, String message) {
        Map<String, Object> response = new HashMap<>();
        try {
            response.put("statusCode", code);
            response.put("message", message);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    public static class OpenGroupResult {
        public static final String STATUS_CODE = "statusCode";
        private static final String GROUP_ID = "groupId";
        private static final String MESSAGE = "message";

        static Map<String, Object> success(String groupId) {
            Map<String, Object> map = new HashMap<>();
            try {
                map.put(STATUS_CODE, 0);
                map.put(GROUP_ID, groupId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return map;
        }

        static Map<String, Object> failure(int code, String message) {
            Map<String, Object> map = new HashMap<>();
            try {
                map.put(STATUS_CODE, code);
                map.put(MESSAGE, message);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return map;
        }

        public static Map<String, Object> successMap(String groupId) {
            Map<String, Object> map = new HashMap<>();
            map.put(STATUS_CODE, 0);
            map.put(GROUP_ID, groupId);
            return map;
        }

        public static Map<String, Object> failureMap(int code, String message) {
            Map<String, Object> map = new HashMap<>();
            map.put(STATUS_CODE, code);
            map.put(MESSAGE, message);
            return map;
        }
    }
}
