package com.jd.oa.fragment.js.hybrid

import android.content.Context
import android.net.Uri
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import com.jd.oa.router.DeepLink.DEEPLINK_PARAM
import org.json.JSONObject

/**
 * create by huf<PERSON> on 2020-01-08
 */

object DongdongDeeplinkHelper {

    fun handle(url: String, context: Context?): Boolean {
        if (context == null)
            return false
        if (url.startsWith(DeepLink.DD_USER_INFO)) {
            val mparam = Uri.parse(url).getQueryParameter(DEEPLINK_PARAM)
            if (mparam.isNullOrBlank()) {
                return false
            }
            val erp = JSONObject(mparam).getString("erp")
            if (erp.isNullOrBlank()) {
                return false
            }
            AppJoint.service(ImDdService::class.java).showContactDetailInfo(context, erp)
            return true
        }
        return false
    }
}