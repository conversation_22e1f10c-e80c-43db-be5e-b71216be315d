package com.jd.oa.fragment;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jme.common.R;

import java.util.Map;

/**
 * 校验密码
 */
@FontScalable(scaleable = true)
@Navigation(hidden = false, displayHome = true)
public class CheckPasswordActivity extends BaseActivity implements View.OnClickListener {

    private static final String TAG = "CheckPasswordActivity";

    private TextView mCetUsername;
    private ClearableEditTxt mCetPwd;

    private Button mBtnBind;
    private CheckBox cbPwd;

    private String sUserName;
    private String sUserPwd;
    private String sid;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.jdme_fragment_account_check);
        initView();
    }

    private void initView() {
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_check_pass_title);
        mCetUsername = findViewById(R.id.bind_jd_username);
        mCetUsername.setText(PreferenceManager.UserInfo.getUserName());
        mCetPwd = findViewById(R.id.bind_jd_pwd);
        mBtnBind = findViewById(R.id.bind_jd_btn);
        cbPwd = findViewById(R.id.cbPwd);
        addListener();
    }

    @Override
    public void onClick(View v) {
        if (R.id.bind_jd_btn == v.getId()) {
            sUserName = mCetUsername.getText().toString();
            sUserPwd = mCetPwd.getText().toString().trim();
            bind();
        }
    }

    /**
     * 绑定京东账号
     */
    private void bind() {
        NetWorkManagerLogin.checkPassword(sUserPwd,
                new SimpleRequestCallback<String>(this, true) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                        }.getType());
                        if (!response.isSuccessful()) {
                            ToastUtils.showToast(response.getErrorMessage());
                            return;
                        }

                        if ("1".equals(response.getData().get("checkPassword"))) {
                            if (!TextUtils.isEmpty(sUserPwd)) {
                                String encryptString = JdmeEncryptUtil.getEncryptString(sUserPwd);
                                PreferenceManager.UserInfo.setEmailPwd(encryptString);
                            }
                            setResult(1);
                            finish();
                            // 成功
                        } else {
                            ToastUtils.showToast(R.string.me_check_pass_error);
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                    }
                });

    }

    private void addListener() {
        mCetPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                setViewEnabled(mBtnBind, mCetUsername.getText().toString(), mCetPwd.getText().toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        //显示或隐藏密码
        StringUtils.showPwd(cbPwd, mCetPwd);

        mBtnBind.setOnClickListener(this);
    }

    /***
     * 根据input来 enable View
     *
     * @param input 输入值
     * @param view  操作的view对象
     */
    private void setViewEnabled(View view, String... input) {
        if (input != null) {
            for (String str : input) {
                if (StringUtils.isEmptyWithTrim(str)) {
                    view.setEnabled(false);
                    break;
                }
                view.setEnabled(true);
            }
        } else {
            view.setEnabled(false);
        }
    }

    @Override
    public void onBackPressed() {
        setResult(-1);
        super.onBackPressed();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (android.R.id.home == item.getItemId()) {// actionbar 返回
            setResult(-1);
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

}
