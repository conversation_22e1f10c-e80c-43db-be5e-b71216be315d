package com.jd.oa.fragment.model;

public class ShareCardBean {
    public String summary;
    public String titleText;
    public String titleIcon;
    public String contentText;
    public String contentImage;
    public LinkInfo linkInfo;

    public ShareCardBean() {
    }

    public ShareCardBean(String summary, String titleText, String titleIcon, String contentText, String contentImage, LinkInfo linkInfo) {
        this.summary = summary;
        this.titleText = titleText;
        this.titleIcon = titleIcon;
        this.contentText = contentText;
        this.contentImage = contentImage;
        this.linkInfo = linkInfo;
    }

    public static class LinkInfo {
        public String defaultUrl;
        public String pc;
        public String mobile;
        public String mac;
        public String ipad;

        public LinkInfo() {
        }

        public LinkInfo(String defaultUrl, String pc, String mobile, String mac, String ipad) {
            this.defaultUrl = defaultUrl;
            this.pc = pc;
            this.mobile = mobile;
            this.mac = mac;
            this.ipad = ipad;
        }
    }


}
