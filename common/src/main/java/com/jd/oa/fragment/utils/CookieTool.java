package com.jd.oa.fragment.utils;

import android.net.Uri;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.tencent.smtt.sdk.CookieManager;
import com.tencent.smtt.sdk.CookieSyncManager;
import com.tencent.smtt.sdk.ValueCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/*
**********************************************************************************************************
 6.21.0 新增处理逻辑
 1.按照之前商定, 只在打开应用时执行清理
 2.代码内置默认清除配置如下:
 hosts :  ".jd.com",  ".jdl.cn",  "jd.com",    "jdl.cn" ,  "jdl.com" ,  ".jdl.com" ,
 cookies:   "third_name","third_token","third_language","third_safetyToken","third_timestamp","third_tenantCodeList","third_tenantCode"
 3.读取不到ducc配置,  清除上面默认的hosts下对应的cookies
 4.读取到ducc下, turnOff为'1'时, 不再清理默认的这些cookie, 两端各自恢复到自己6.20.0版本的逻辑(打开应用时, android清除全部cookies, iOS只清除应用自己domain下的cookie)
 5.读取到ducc下 turnOff不为'1', 若hosts/cookies字段下有值, 将ducc下配置的值追加到默认清理项下, 然后执行清理, 没有值, 执行默认清理
 *******************************************************************************************************
 */
/*
下发的例子
{
    "ios":{
        "turnOff":"0",
        "hosts":[
        "test.com"
        ],
        "cookies":[
        ]
    },
    "android":{
        "turnOff":"0",
        "hosts":[
        "test.com"
        ],
        "cookies":[
        ]
    }
}
*/

public class CookieTool {
    private static final Set<String> URL_SET = new HashSet<>();
    private static final List<String> HOSTS = new ArrayList<>(Arrays.asList(
            ".jd.com", ".jdl.cn", "jd.com", "jdl.cn", "jdl.com", ".jdl.com"));
    private static final List<String> COOKIE_LIST = new ArrayList<>(Arrays.asList(
            "third_name", "third_token", "third_language", "third_safetyToken", "third_timestamp", "third_tenantCodeList", "third_tenantCode"));

    public static void cleanCookies() {
        try {
            CookieManager.getInstance().removeAllCookies(new ValueCallback<Boolean>() {
                @Override
                public void onReceiveValue(Boolean value) {
                    System.out.println("removeAllCookies=" + value);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void addUrl(String url) {
        try {
            Uri uri = Uri.parse(url);
            URL_SET.add(uri.getHost());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void uploadCookie() {
        try {
            CookieManager cookieManager = CookieManager.getInstance();
            for (String url : URL_SET) {
                MELogUtil.info("WEB", "uploadCookie: url=" + url + " cookie=" + cookieManager.getCookie(url));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void delCookieFromConfiguration() {
        try {
            String cleanStr = ConfigurationManager.get().getEntry("cookies.clean", "");
            if (cleanStr != null && cleanStr.length() > 0) {
                JSONObject cleanJson = new JSONObject(cleanStr);
                JSONObject androidJson = cleanJson.getJSONObject("android");
                String turnOff = androidJson.optString("turnOff");
                if ("1".equals(turnOff)) {//使用旧逻辑
                    try {
                        CookieSyncManager.createInstance(AppBase.getTopActivity());
                        CookieManager.getInstance().removeAllCookie();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return;
                }
                JSONArray hostArray = androidJson.optJSONArray("hosts");
                if (hostArray != null && hostArray.length() > 0) {
                    for (int a = 0; a < hostArray.length(); a++) {
                        String host = hostArray.getString(a);
                        if (!HOSTS.contains(host)) {
                            HOSTS.add(host);
                        }
                    }
                }
                JSONArray cookieArray = androidJson.optJSONArray("cookies");
                if (cookieArray != null && cookieArray.length() > 0) {
                    for (int a = 0; a < cookieArray.length(); a++) {
                        String cookie = cookieArray.getString(a);
                        if (!COOKIE_LIST.contains(cookie)) {
                            COOKIE_LIST.add(cookie);
                        }
                    }
                }
            }
            CookieManager cookieManager = CookieManager.getInstance();
            if (HOSTS.size() > 0) {
                for (int a = 0; a < HOSTS.size(); a++) {
                    String domain = HOSTS.get(a);
                    if (domain != null && domain.length() > 0 && COOKIE_LIST.size() > 0) {
                        for (int b = 0; b < COOKIE_LIST.size(); b++) {
                            cookieManager.setCookie(domain, COOKIE_LIST.get(b) + "=; Expires=Wed, 31 Dec 2010 23:59:59 GMT");
                        }
                    }
                }
            }
            cookieManager.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
