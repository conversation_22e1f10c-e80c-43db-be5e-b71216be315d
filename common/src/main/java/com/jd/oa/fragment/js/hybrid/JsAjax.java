package com.jd.oa.fragment.js.hybrid;

import android.content.Context;
import android.webkit.JavascriptInterface;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.js.hybrid.utils.AjaxHandler;

import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsAjax {

    private Context mContext;

    public JsAjax(Context context) {
        this.mContext = context;
    }

    public JsAjax() {
        this.mContext = null;
    }

    @JavascriptInterface
    public void onAjaxRequest(Object requestData, CompletionHandler handler) {
        AjaxHandler.onAjaxRequest(mContext, (JSONObject) requestData, handler);
        MELogUtil.localI(MELogUtil.TAG_JS, "JsAjax onAjaxRequest requestData: " + requestData.toString());
    }
}
