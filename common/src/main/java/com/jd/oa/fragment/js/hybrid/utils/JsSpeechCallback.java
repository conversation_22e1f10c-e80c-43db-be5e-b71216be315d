package com.jd.oa.fragment.js.hybrid.utils;

import android.text.TextUtils;

import com.jd.oa.speech.SpeechRecognitionDialog;

import org.json.JSONException;
import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.CONTENT;
import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.FAILURE;
import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.STATUS;
import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.SUCCESS;

/**
 * create by hufeng on 2019-07-15
 */
public class JsSpeechCallback implements SpeechRecognitionDialog.SpeechRecognitionCallback {
    private CompletionHandler<Object> handler;

    public JsSpeechCallback(CompletionHandler<Object> handler) {
        this.handler = handler;
    }

    @Override
    public void onCancel() {
        JsSdkKit.sendCancel(handler);
    }

    @Override
    public void onFinish() {

    }

    @Override
    public void onResult(String s) {
        try {
            if (handler == null) {
                return;
            }
            JSONObject jsonObject = new JSONObject();
            if (TextUtils.isEmpty(s)) {
                jsonObject.put(STATUS, FAILURE);
            } else {
                jsonObject.put(STATUS, SUCCESS);
                jsonObject.put(CONTENT, s);
            }
//        mJsCommonWebView.callJsMethod("speechRecognitionResult", s);
            handler.complete(jsonObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDismiss() {

    }

    @Override
    public void onEnd() {

    }
}
