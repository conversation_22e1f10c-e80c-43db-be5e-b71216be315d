package com.jd.oa.fragment

import com.jd.oa.abilities.api.ApiLogin
import com.jd.oa.abilities.api.ApiLogin.TAG
import com.jd.oa.abilities.api.ApiLogin.ccoLink
import com.jd.oa.abilities.api.ApiLogin.meSaasLink
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.utils.Logger

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/3/26 18:03
 */
class SaasUrlInterceptor {


    private val attempts = mutableMapOf<String, AttemptRecord>()

    companion object {
        private const val THRESHOLD_FREQUENCY = 5 * 1000L
        private const val THRESHOLD_TIMES = 3
    }

    fun shouldOverrideUrlLoading(
        url: String,
        onCheckValidate: (String) -> Boolean,
        onAppendAuthUrl: (String?) -> Unit,
    ): Boolean {
        val checkResult = ApiLogin.checkAuthType(url)
        return if (checkResult.authType == ApiLogin.AUTH_NO) {
            false
        } else {
            val authType = checkResult.authType
            val authUrl = checkResult.authUrl
            val host = checkResult.host
            if (onCheckValidate(host)) {
                Logger.e(TAG, "SaasUrlInterceptor false onCheckValidate = true")
                attempts.remove(host)
                return false
            }
            //看上次请求时间以及允许的次数，防止死锁
            val record = attempts[host]
            val now = System.currentTimeMillis()
            if (record != null) {
                if (now - record.last < THRESHOLD_FREQUENCY && record.times >= THRESHOLD_TIMES) {
                    Logger.e(TAG, "SaasUrlInterceptor false record = $record")
                    return false
                }
                attempts[host] = AttemptRecord(now, record.times + 1)
            } else {
                attempts[host] = AttemptRecord(now, 1)
            }
            if (ApiLogin.AUTH_CCO == authType) {
                ccoLink(url, object : IServiceCallback<String> {
                    override fun onResult(success: Boolean, t: String?, error: String?) {
                        Logger.e(TAG, "SaasUrlInterceptor true AUTH_CCO")
                        onAppendAuthUrl.invoke(t)
                    }
                })
            } else if (ApiLogin.AUTH_ME_SAAS == authType) {
                meSaasLink(authUrl, url, object : IServiceCallback<String> {
                    override fun onResult(success: Boolean, s: String?, error: String?) {
                        Logger.e(TAG, "SaasUrlInterceptor true AUTH_ME_SAAS")
                        onAppendAuthUrl.invoke(s)
                    }
                })
            }
            true
        }
    }

    private data class AttemptRecord(val last: Long, val times: Int)
}