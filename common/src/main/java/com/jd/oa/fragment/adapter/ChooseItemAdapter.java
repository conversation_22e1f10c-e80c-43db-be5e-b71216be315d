package com.jd.oa.fragment.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.fragment.model.ChooseItemInfo;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jme.common.R;

import java.util.List;


public class ChooseItemAdapter extends BaseRecyclerAdapter<ChooseItemInfo, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;
    private boolean mShowMore = false;

    public ChooseItemAdapter(Context context, List<ChooseItemInfo> data) {
        super(context, data);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_choose, viewGroup, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ViewHolder holder = (ViewHolder) viewHolder;
        ChooseItemInfo info = getItem(i);
        holder.image.setImageResource(info.iconRes);
        holder.name.setText(info.labelRes);
    }


    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }


    @Override
    public int getItemCount() {
        return mShowMore ? super.getItemCount() + 1 : super.getItemCount();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        //ViewGroup container;
        ImageView image;
        TextView name;
        TextView tips;

        public ViewHolder(View itemView) {
            super(itemView);
            //container = itemView.findViewById(R.id.layout_container);
            image = itemView.findViewById(R.id.iv_image);
            name = itemView.findViewById(R.id.tv_name);
            tips = itemView.findViewById(R.id.tv_tips);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        int position = getAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) {
                            return;
                        }
                        mOnItemClickListener.onItemClick(ChooseItemAdapter.this, v, position);
                    }
                }
            });
        }
    }
}
