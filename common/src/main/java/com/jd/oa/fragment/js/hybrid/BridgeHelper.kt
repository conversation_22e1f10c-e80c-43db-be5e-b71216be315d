package com.jd.oa.fragment.js.hybrid

import android.net.Uri
import com.jd.oa.fragment.web.IWebPage

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/1/7 17:46
 */

internal fun source(webPage: IWebPage?, default: String = "Js"): String {
    return runCatching {
        if (webPage?.getAppId().isNullOrEmpty()) {
            val url = webPage?.getUrl()
            Uri.parse(url).host
        } else {
            webPage?.getAppId()
        }
    }.getOrNull() ?: default
}