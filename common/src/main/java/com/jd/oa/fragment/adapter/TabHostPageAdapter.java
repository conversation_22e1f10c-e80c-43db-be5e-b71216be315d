package com.jd.oa.fragment.adapter;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.jd.oa.fragment.TabHostInterface;

import java.util.ArrayList;

public class TabHostPageAdapter extends FragmentPagerAdapter {

    private ArrayList<String> mTypes;
    private ArrayList<Bundle> mBundles;
    private Class<? extends Fragment> mFragmentName;
    private Fragment[] mFragments;
    private TabHostInterface.OnTabTitleChangeListener mOnTabTitleChangeListener;

    public TabHostPageAdapter(FragmentManager fm, ArrayList<String> types, ArrayList<Bundle> bundles, Class<? extends Fragment> fragmentName) {
        super(fm);
        mTypes = types;
        mBundles = bundles;
        mFragmentName = fragmentName;
        mFragments = new Fragment[mTypes.size()];
    }

    public void setOnTabTitleChangeListener(TabHostInterface.OnTabTitleChangeListener onTabTitleChangeListener) {
        mOnTabTitleChangeListener = onTabTitleChangeListener;
    }

    @Override
    public Fragment getItem(int position) {
        Fragment fragment = mFragments[position];
        if (fragment == null) {
            try {
                fragment = mFragmentName.newInstance();
                if (fragment instanceof TabHostInterface.CustomTitle) {
                    ((TabHostInterface.CustomTitle) fragment).setOnTabTitleChangeListener(mOnTabTitleChangeListener);
                }
                if (mBundles != null) {
                    Bundle bundle = mBundles.get(position);
                    fragment.setArguments(bundle);
                }
            } catch (InstantiationException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            mFragments[position] = fragment;
        }
        return fragment;
    }

    @Override
    public int getCount() {
        if (mTypes == null) {
            return 0;
        }
        return mTypes.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        if (mFragments[position] != null && mFragments[position] instanceof TabHostInterface.CustomTitle) {
            return ((TabHostInterface.CustomTitle) mFragments[position]).getCustomTitle();
        }
        return mTypes.get(position);
    }
}
