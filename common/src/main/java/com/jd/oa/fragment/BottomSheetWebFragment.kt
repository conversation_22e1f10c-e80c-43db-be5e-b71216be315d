package com.jd.oa.fragment

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.jd.oa.ext.getParentView
import com.jd.oa.ext.getParentViewModel
import com.jd.oa.fragment.js.hybrid.JsBottomSheetBrowser
import com.jd.oa.fragment.js.hybrid.JsBrowser
import com.jd.oa.fragment.model.WebMenu
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_SCREEN_SCALE
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_MOST
import com.jd.oa.fragment.web.WebHelper
import com.jd.oa.ui.MyProgressWebView2
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import com.jd.oa.viewmodel.BottomSheetContainerModel
import com.jd.oa.viewmodel.BottomSheetWebModel
import com.jme.common.R

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/3 00:18
 */
class BottomSheetWebFragment : WebFragment2() {

    private val webViewModel: BottomSheetWebModel by viewModels()

    private val parentViewModel: BottomSheetContainerModel by lazy {
        getParentViewModel<BottomSheetContainerModel>()
    }

    override fun configLayout(root: FrameLayout?, immersive: String?, theme: String?) {
        super.configLayout(root, immersive, theme)
        root?.runCatching {
            val titleBar: View? = this.findViewById(R.id.titleBar)
            titleBar?.gone()
            val progressBar: MyProgressWebView2? = this.findViewById(R.id.web_view_process)
            progressBar?.notShowProgress()
        }
    }

    override fun registerJSNativeKit() {
        super.registerJSNativeKit()
        val initScale =
            arguments?.getString(KEY_SCREEN_SCALE) ?: SCREEN_SCALE_MOST
        jsBrowser =
            JsBottomSheetBrowser(
                webView,
                jsSdkKit,
                this,
                requireActivity(),
                webViewModel,
                parentViewModel,
                initScale,
                webContainer
            )
        webView.addJavascriptObject(jsBrowser, JsBrowser.DOMAIN)
        initViewModels(jsBrowser as JsBottomSheetBrowser)
    }

    private fun initViewModels(jsBrowser: JsBottomSheetBrowser) {
        runCatching {
            webViewModel.actionMenus.observe(viewLifecycleOwner) { list ->
                val menuBtn = getParentView<TextView>(R.id.tv_menu)
                if (list.isNullOrEmpty()) {
                    menuBtn.gone()
                } else {
                    menuBtn.visible()
                    if (list.size == 1) {
                        val menuItem = list[0]
                        menuBtn.setOnClickListener {
                            jsBrowser.menuClick(menuItem.key)
                        }
                    } else {
                        menuBtn.setOnClickListener {
                            showMenus(requireContext(), menuBtn, list) { key ->
                                jsBrowser.menuClick(key)
                            }
                        }
                    }
                }
            }

            webViewModel.buttonStates.observe(viewLifecycleOwner) { map ->
                if (map == null) {
                    return@observe
                }
                val close = getParentView<TextView>(R.id.tv_close)
                val menu = getParentView<TextView>(R.id.tv_menu)
                val expand = getParentView<TextView>(R.id.tv_expand)
                WebHelper.setNavigationButtonsVisible(map, object : WebHelper.StateViewGetter() {
                    override fun getClose(): View = close

                    override fun getExpand(): View = expand

                    override fun getMore(): View = menu
                })
            }


            parentViewModel.crtScaleValue.observe(viewLifecycleOwner) {
                jsBrowser.screenScaleChanged(it)
            }
        }
    }

    private fun showMenus(
        context: Context,
        anchorView: View,
        items: List<WebMenu>,
        onItemClickListener: (String) -> Unit
    ) {
        val contentView: View =
            LayoutInflater.from(context).inflate(R.layout.ai_bottom_sheet_menu, null)
        val recyclerView = contentView.findViewById<RecyclerView>(R.id.recycle)
        val popupWindow = PopupWindow(anchorView.context)
        popupWindow.contentView = contentView
        popupWindow.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // setOutsideTouchable设置生效的前提是setTouchable(true)和setFocusable(false)
        popupWindow.isTouchable = true
        popupWindow.isFocusable = true
        popupWindow.isOutsideTouchable = true
        popupWindow.showAsDropDown(anchorView, 0, 0)
        recyclerView.layoutManager = LinearLayoutManager(activity)

        recyclerView.adapter = MenuAdapter(context, items) {
            popupWindow.dismiss()
            onItemClickListener.invoke(it)
        }
//            contentView.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
//                override fun onGlobalLayout() {
//                    autoAdjustArrowPos(contentView, anchorView)
//                    contentView.viewTreeObserver.removeOnGlobalLayoutListener(this)
//                }
//            })
    }

    internal class MenuAdapter(
        private val context: Context,
        private val items: List<WebMenu>,
        private val onItemClickListener: (String) -> Unit
    ) : RecyclerView.Adapter<MenuVH>() {


        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuVH {
            val view: View =
                LayoutInflater.from(context).inflate(
                    R.layout.ai_bottom_sheet_menu_item,
                    parent, false
                )
            return MenuVH(view)
        }

        override fun getItemCount(): Int = items.size

        override fun onBindViewHolder(holder: MenuVH, position: Int) {
            val item = items[position]
            Glide.with(holder.icon)
                .load(item.icon)
                .placeholder(R.drawable.prompt_circle)
                .into(holder.icon)
            holder.title.apply {
                text = item.title
            }
            holder.itemView.setOnClickListener {
                onItemClickListener.invoke(item.key)
            }
        }

    }

    class MenuVH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var icon: ImageView = itemView.findViewById(R.id.menu_icon)
        var title: TextView = itemView.findViewById(R.id.menu_text)
    }
}