package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import org.json.JSONException;
import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

public class JsMeeting {

    public static final String DOMAIN = "meeting";

    public static final String TAG = "JsMeeting";


    @JavascriptInterface
    public void joinConference(final Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonObject = (JSONObject) args;
        String meetingId = jsonObject.optString("meetingId", null);
        String meetingCode = jsonObject.optString("meetingCode");
        String password = jsonObject.optString("password");
        String source = jsonObject.optString("source");

        try {
            MELogUtil.localI(TAG, "joinConference, " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (TextUtils.isEmpty(meetingCode) && TextUtils.isEmpty(meetingId)) {
            JSONObject error = new JSONObject();
            try {
                error.put("errMsg", "meetingCode and meetingId is empty");
                handler.complete(JSTools.error(error, JSErrCode.ERROR_104));
            } catch (JSONException e) {
                MELogUtil.localE(TAG, e.getMessage(), e);
                handler.complete(JSTools.error());
            }
            return;
        }

        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing()) {
            handler.complete(JSTools.error());
        }
        JdMeetingService jdMeetingService = AppJoint.service(JdMeetingService.class);

        Long code = null;
        if (!TextUtils.isEmpty(meetingCode) && TextUtils.isDigitsOnly(meetingCode)) {
            try {
                code = Long.parseLong(meetingCode);
            } catch (Exception e) {
                handler.complete(JSTools.error());
                MELogUtil.localE(TAG, e.getMessage(), e);
                return;
            }
        }

        if (TextUtils.isEmpty(meetingId) && code == null) {
            JSONObject error = new JSONObject();
            try {
                error.put("errMsg", "meetingCode and meetingId is empty");
                handler.complete(JSTools.error(error, JSErrCode.ERROR_104));
            } catch (JSONException e) {
                MELogUtil.localE(TAG, e.getMessage(), e);
                handler.complete(JSTools.error());
            }
            return;
        }

        jdMeetingService.joinMeeting(activity, meetingId, code, password, source, new JdMeetingService.JoinMeetingCallback() {
            @Override
            public void onSuccess() {
                handler.complete(JSTools.success(new JSONObject()));
            }

            @Override
            public void onFail(String errorCode, String message) {
                try {
                    handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_100));
                } catch (Exception e) {
                    handler.complete(JSTools.error());
                    MELogUtil.localE(TAG, e.getMessage(), e);
                }
            }
        });
    }
}