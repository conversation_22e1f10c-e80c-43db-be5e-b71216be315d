package com.jd.oa.fragment.model;

public class ChooseItemInfo {

    public ChooseItemInfo(int label, int res, ItemType itemType) {
        this.labelRes = label;
        this.iconRes = res;
        this.type = itemType;
    }

    public int labelRes;
    public int iconRes;
    public ItemType type;

    public enum ItemType {
        camera, gallery, local, pan, cancel, mail, timline, other, joyspace, imFile
    }

}
