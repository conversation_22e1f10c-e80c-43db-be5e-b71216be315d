package com.jd.oa.fragment.js.hybrid;

import android.os.Handler;
import android.os.Looper;
import android.webkit.JavascriptInterface;

import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsPopup2 {
    public static final String DOMAIN = "popup";
    private final Handler main = new Handler(Looper.getMainLooper());

    @JavascriptInterface
    public void toast(final Object args, final CompletionHandler<Object> handler) {
        main.post(new Runnable() {
            @Override
            public void run() {
                try {
                    String message = ((JSONObject) args).optString("message");
                    ToastUtils.showToast(message);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }


}
