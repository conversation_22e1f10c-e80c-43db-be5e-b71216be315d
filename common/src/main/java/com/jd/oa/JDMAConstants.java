package com.jd.oa;

public class JDMAConstants {

    public final static String mobile_Mine_myHoliday_click = "mobile_Mine_myHoliday_click";//我的_点击我的假期
    public final static String mobile_Mine_myAttendance_click = "mobile_Mine_myAttendance_click";//我的_点击我的考勤
    public final static String mobile_myAttendance_FAQIcon_click = "mobile_myAttendance_FAQIcon_click";//我的_我的考勤_点击常见问题说明符号
    public final static String mobile_Mine_myHoliday_holidayApply_click = "mobile_Mine_myHoliday_holidayApply_click";//我的_我的假期_点击假期申请
    public final static String mobile_myAttendance_holidayApply_click = "mobile_myAttendance_holidayApply_click";//我的考勤_点击休假及外出申请
    public final static String mobile_myAttendance_unusualAttendanceApply_click = "mobile_myAttendance_unusualAttendanceApply_click";//我的考勤_点击考勤异常申请
    public final static String mobile_myAttendance_overtimeApply_click = "mobile_myAttendance_overtimeApply_click";//我的考勤_点击加班申请
    public final static String mobile_Mine_headIcon_click = "mobile_Mine_headIcon_click";//我的_点击头像
    public final static String mobile_Mine_personInfo_headIcon_click = "mobile_Mine_personInfo_headIcon_click";//我的_头像_点击个人信息页头像
    public final static String mobile_Mine_personInfo_hr_click = "mobile_Mine_personInfo_hr_click";//我的_头像_点击个人信息页人力资源联系人
    public final static String mobile_Mine_IntegralInfo_click = "mobile_Mine_IntegralInfo_click";//我的_点击福利积分
    public final static String mobile_Mine_QRCode_click = "mobile_Mine_QRCode_click";//我的_点击二维码
    public final static String mobile_Mine_addSignature_click = "mobile_Mine_addSignature_click";//我的_点击添加工作签名

    public final static String mobile_employeeTravel_useCarType_click = "mobile_employeeTravel_useCarType_click";//员工出行_点击用车类型
    public final static String mobile_employeeTravel_overTime_click = "mobile_employeeTravel_overTime_click";//员工出行_点击工作加班
    public final static String mobile_employeeTravel_leaveBusiness_click = "mobile_employeeTravel_leaveBusiness_click";//员工出行_点击因公外出
    public final static String mobile_employeeTravel_commonAddress_click = "mobile_employeeTravel_commonAddress_click";//员工出行_点击管理常用地址
    public final static String mobile_employeeTravel_overTime_leaveTime_click = "mobile_employeeTravel_overTime_leaveTime_click";//员工出行_工作加班_点击出发时间
    public final static String mobile_employeeTravel_leaveBusiness_leaveTime_click = "mobile_employeeTravel_leaveBusiness_leaveTime_click";//员工出行_因公外出_点击出发时间
    public final static String mobile_employeeTravel_overTime_isHasTravelPartnerOpen_click = "mobile_employeeTravel_overTime_isHasTravelPartnerOpen_click";//员工出行_工作加班_点击是否有同行人打开
    public final static String mobile_employeeTravel_overTime_isHasTravelPartnerClose_click = "mobile_employeeTravel_overTime_isHasTravelPartnerClose_click";//员工出行_工作加班_点击是否有同行人关闭
    public final static String mobile_employeeTravel_overTime_callCar_click = "mobile_employeeTravel_overTime_callCar_click";//员工出行_工作加班_点击叫车
    public final static String mobile_employeeTravel_leaveBusiness_callCar_click = "mobile_employeeTravel_leaveBusiness_callCar_click";//员工出行_因公外出_点击叫车
    public final static String mobile_employeeTravel_leaveBusiness_dailyCallCar_click = "mobile_employeeTravel_leaveBusiness_dailyCallCar_click";//员工出行_因公外出_点击日常用车
    public final static String mobile_employeeTravel_leaveBusiness_travelCallCar_click = "mobile_employeeTravel_leaveBusiness_travelCallCar_click";//员工出行_因公外出_点击差旅用车

    public final static String mobile_employeeTravel_waitResponse_cancel_click = "mobile_employeeTravel_waitResponse_cancel_click";//员工出行_等待应答_点击取消
    public final static String mobile_employeeTravel_waitPickUp_cancel_click = "mobile_employeeTravel_waitPickUp_cancel_click";//员工出行_等待接驾_点击取消用车
    public final static String mobile_employeeTravel_waitPickUp_complaint_click = "mobile_employeeTravel_waitPickUp_complaint_click";//员工出行_等待接驾_点击投诉
    public final static String mobile_employeeTravel_waitPickUp_complaint_submit_click = "mobile_employeeTravel_waitPickUp_complaint_submit_click";//员工出行_等待接驾_投诉_点击提交
    public final static String mobile_employeeTravel_waitPickUp_complaint_customerTel_click = "mobile_employeeTravel_waitPickUp_complaint_customerTel_click";//员工出行_等待接驾_投诉_点击客服电话
    public final static String mobile_employeeTravel_waitPickUp_travelShare_click = "mobile_employeeTravel_waitPickUp_travelShare_click";//员工出行_等待接驾_点击行程分享
    public final static String mobile_employeeTravel_waitPickUp_travelShare_JDMShare_click = "mobile_employeeTravel_waitPickUp_travelShare_JDMShare_click";//员工出行_等待接驾_行程分享_分享京ME
    public final static String mobile_employeeTravel_waitPickUp_travelShare_wechatShare_click = "mobile_employeeTravel_waitPickUp_travelShare_wechatShare_click";//员工出行_等待接驾_行程分享_分享微信
    public final static String mobile_employeeTravel_waitPickUp_phoneDriver_click = "mobile_employeeTravel_waitPickUp_phoneDriver_click";//员工出行_等待接驾_点击拨打司机电话
    public final static String mobile_employeeTravel_traveling_help_click = "mobile_employeeTravel_traveling_help_click";//员工出行_行程中_点击急救
    public final static String mobile_employeeTravel_traveling_complaint_click = "mobile_employeeTravel_traveling_complaint_click";//员工出行_行程中_点击投诉
    public final static String mobile_employeeTravel_traveling_complaint_submit_click = "mobile_employeeTravel_traveling_complaint_submit_click";//员工出行_行程中_投诉_点击提交
    public final static String mobile_employeeTravel_traveling_complaint_customerTel_click = "mobile_employeeTravel_traveling_complaint_customerTel_click";//员工出行_行程中_投诉_点击客服电话
    public final static String mobile_employeeTravel_traveling_travelShare_click = "mobile_employeeTravel_traveling_travelShare_click";//员工出行_行程中_点击行程分享
    public final static String mobile_employeeTravel_traveling_travelShare_JDMShare_click = "mobile_employeeTravel_traveling_travelShare_JDMShare_click";//员工出行_行程中_行程分享_分享京ME
    public final static String mobile_employeeTravel_traveling_travelShare_wechatShare_click = "mobile_employeeTravel_traveling_travelShare_wechatShare_click";//员工出行_行程中_行程分享_分享微信
    public final static String mobile_employeeTravel_traveling_phoneDriver_click = "mobile_employeeTravel_traveling_phoneDriver_click";//员工出行_行程中_点击司机拨打电话
    public final static String mobile_employeeTravel_traveDetail_phoneDriver_click = "mobile_employeeTravel_traveDetail_phoneDriver_click";//员工出行_行程详情_点击司机拨打电话
    public final static String mobile_employeeTravel_traveDetail_pay_click = "mobile_employeeTravel_traveDetail_pay_click";//员工出行_行程详情_点击立即支付
    public final static String mobile_employeeTravel_traveDetail_payDelay_click = "mobile_employeeTravel_traveDetail_payDelay_click";//员工出行_行程详情_点击费用有误延缓支付
    public final static String mobile_employeeTravel_traveDetail_complaint_click = "mobile_employeeTravel_traveDetail_complaint_click";//员工出行_行程详情_点击投诉
    public final static String mobile_employeeTravel_traveDetail_help_click = "mobile_employeeTravel_traveDetail_help_click";//员工出行_行程详情_点击急救
    public final static String mobile_employeeTravel_traveDetail_pay_evaluate_click = "mobile_employeeTravel_traveDetail_pay_evaluate_click";//员工出行_行程详情_支付_点击评价
    public final static String mobile_employeeTravel_traveDetail_payDelay_submit_click = "mobile_employeeTravel_traveDetail_payDelay_submit_click";//员工出行_行程详情_费用有误延缓支付_点击提交
    public final static String mobile_employeeTravel_traveDetail_payDelay_customerTel_click = "mobile_employeeTravel_traveDetail_payDelay_customerTel_click";//员工出行_行程详情_费用有误延缓支付_点击客服电话

    public final static String mobile_holiday = "mobile_holiday";//我的假期页面
    public final static String mobile_myAttendance = "mobile_myAttendance";//我的考勤页面

    public final static String mobile_workbench_all_click = "mobile_workbench_all_click";//工作台_我的考勤_全部点击
    public final static String mobile_flowCenter_leave_out_apply_click = "mobile_flowCenter_leave_out_apply_click";//流程中心_休假及外出申请点击
    public final static String mobile_flowCenter_exception_apply_click = "mobile_flowCenter_exception_apply_click";//流程中心_考勤异常申请点击
    public final static String mobile_flowCenter_overtime_apply_click = "mobile_flowCenter_overtime_apply_click";//流程中心_加班申请点击
    public final static String mobile_toolSearch_myAttendance_click = "mobile_toolSearch_myAttendance_click";//工具搜索_我的考勤点击

    public final static String mobile_exception_apply = "mobile_exception_apply";//考勤异常页面
    public final static String mobile_overtime_apply = "mobile_overtime_apply";//加班申请页面
    public final static String mobile_leave_out_apply = "mobile_leave_out_apply";//休假及外出申请页面
    public final static String mobile_employeeTravel = "mobile_employeeTravel";//员工出行页面

    public final static String mobile_exp_click = "EXP_Main_Tab";//点击个人中心
    public final static String mobile_mine_click = "mobile_mine_click";//点击我
    public final static String mobile_timline_click = "mobile_timline_click";//点击消息
    public final static String mobile_contacts_click = "mobile_contacts_click";//点击通讯录
    public final static String mobile_mail_click = "mobile_mail_click";//点击邮箱
    public final static String mobile_task_click = "mobile_task_click";//点击任务
    public final static String mobile_joyspace_click = "mobile_joyspace_click";//点击云文档
    public final static String mobile_workbench_click = "mobile_workbench_click";//点击工作台
    public final static String mobile_joyday_click = "mobile_joyday_click";//点击日历

    public final static String mobile_tabar_edit_click = "mobile_tabar_edit_click";//点击导航栏编辑
    public final static String mobile_tabar_save_click = "mobile_tabar_save_click";//点击导航栏保存

    public final static String mobile_tabar_v2_edit_click = "Click_NavigationEdit";//V2点击导航栏编辑
    public final static String mobile_tabar_v2_more_click = "Click_NavigationMore";//V2点击更多
    public final static String mobile_tabar_v2_edit_save_click = "Click_EditNavigationDone";//V2点击编辑保存
    public final static String mobile_tabar_v2_edit_swipe_up = "Click_SwipeupNavigation";//V2向上划动打开
    public final static String mobile_tabar_v2_edit_swipe_down = "Click_SwipedownNavigation";//V2向下划动关闭
    public final static String mobile_tabar_v2_edit_cancel = "Click_EditNavigationCancel";//V2点击取消编辑
    public final static String mobile_event_platform_tabbar_click = "Mobile_Event_Platform_tabbar_click";// tabar点击

    public final static String mobile_timline_head_qrScan_click = "mobile_timline_head_qrScan_click";//消息_头部_点击扫一扫
    //    public final static String mobile_timline_head_add_friend_click = "mobile_timline_head_add_friend_click";//消息_头部_点击添加好友
//    public final static String mobile_timline_head_create_groupChat_click = "mobile_timline_head_create_groupChat_click";//消息_头部_点击发起群聊
    public final static String mobile_timline_head_create_word_click = "mobile_timline_head_create_word_click";//消息_头部_点击新建文档
    public final static String mobile_timline_head_new_meeting_invite_click = "mobile_timline_head_new_meeting_invite_click";//消息_头部_点击新建会邀
//    public final static String mobile_timline_save_to_netdisk_click = "mobile_timline_save_to_netdisk_click";//消息_文件_存鲸盘

    public final static String mobile_timline_notification_approve_click = "mobile_timline_notification_approve_click";//消息_审批通知_点击审批通知栏

    public final static String mobile_workbench_clock_in_click = "mobile_workbench_clock_in_click";//工作台_打卡
    public final static String mobile_workbench_myApply_all_click = "mobile_workbench_myApply_all_click";//工作台_我的申请_全部
    public final static String mobile_workbench_myApply_urge_click = "mobile_workbench_myApply_urge_click";//工作台_我的申请_催办
    public final static String mobile_workbench_myApply_cancel_apply_click = "mobile_workbench_myApply_cancel_apply_click";//工作台_我的申请_取消申请
    public final static String mobile_workbench_myApprove_all_click = "mobile_workbench_myApprove_all_click";//工作台_我的审批_全部
    public final static String mobile_workbench_myApprove_approve_click = "mobile_workbench_myApprove_approve_click";//工作台_我的审批_批准
    public final static String mobile_workbench_myApprove_reject_click = "mobile_workbench_myApprove_reject_click";//工作台_我的审批_驳回
    public final static String mobile_workbench_myApprove_detail_click = "mobile_workbench_myApprove_detail_click";//工作台_我的审批_详情

    public final static String mobile_flowCencter_myApply_click = "mobile_flowCencter_myApply_click";//流程中心_点击我的申请
    public final static String mobile_flowCencter_myApprove_click = "mobile_flowCencter_myApprove_click";//流程中心_点击我的审批
    public final static String mobile_flowCencter = "mobile_flowCencter";//流程中心
    public final static String mobile_flowCencter_myApprove_time_tab_click = "mobile_flowCencter_myApprove_time_tab_click";//流程中心_我的审批_点击时间tab页
    public final static String mobile_flowCencter_myApprove_type_tab_click = "mobile_flowCencter_myApprove_type_tab_click";//流程中心_我的审批_点击类别tab页
    public final static String mobile_flowCencter_myApprove_approve_history_click = "mobile_flowCencter_myApprove_approve_history_click";//流程中心_我的审批_点击审批历史
    public final static String mobile_flowCencter_myApprove_approve_click = "mobile_flowCencter_myApprove_approve_click";//流程中心_我的审批_点击批准
    public final static String mobile_login_faceLogin_login_click = "mobile_login_faceLogin_login_click";//登录页_刷脸登录_点击登录
    public final static String mobile_login_accountLogin_login_click = "mobile_login_accountLogin_login_click";//登录页_账号密码登录_点击登录
    public final static String mobile_login_cant_receive_authCode = "mobile_login_cant_receive_authCode";//登录页_收不到验证码
    public final static String mobile_login_phone_number_error = "mobile_login_phone_number_error";//登录页_手机号有误
    public final static String mobile_login_send_authCode_to_mail = "mobile_login_send_authCode_to_mail";//登录页_发送验证码至邮箱

    public final static String mobile_myWallet = "mobile_myWallet";//我的钱包
    public final static String mobile_myWallet_click = "mobile_myWallet_click";//点击我的钱包
    public final static String mobile_myWallet_balance_click = "mobile_myWallet_balance_click";//我的钱包_点击钱包余额
    public final static String mobile_myWallet_notice_click = "mobile_myWallet_notice_click";//我的钱包_点击钱包须知
    public final static String mobile_myWallet_bind_jd_account_click = "mobile_myWallet_bind_jd_account_click";//我的钱包_点击绑定京东钱包
    public final static String mobile_myWallet_staffCard_click = "mobile_myWallet_staffCard_click";//我的钱包_点击员工卡
    public final static String mobile_myWallet_redPacket_click = "mobile_myWallet_redPacket_click";//我的钱包_点击红包
    public final static String mobile_myWallet_mealCoupon_click = "mobile_myWallet_mealCoupon_click";//我的钱包_点击餐券
    public final static String mobile_myWallet_staff_money_manage_click = "mobile_myWallet_staff_money_manage_click";//我的钱包_点击员工理财
    public final static String mobile_myWallet_staff_insurance_click = "mobile_myWallet_staff_insurance_click";//我的钱包_点击员工保险
    public final static String mobile_myWallet_staff_borrow_money_click = "mobile_myWallet_staff_borrow_money_click";//我的钱包_点击员工借钱

    public final static String mobile_live_video = "mobile_live_video";//视频直播
    public final static String mobile_video_on_demand = "mobile_video_on_demand";//视频点播
    public final static String mobile_joymeeting = "mobile_joymeeting";//joymeeting
    public final static String mobile_welfare_stamps = "mobile_welfare_stamps";//福利券
    public final static String mobile_welfare_stamps_click = "mobile_welfare_stamps_click";//福利券dianji
    public final static String mobile_around = "mobile_around";//身边

    public final static String mobile_employeeTravel_carpooling_passenger_click = "mobile_employeeTravel_carpooling_passenger_click";//员工出行_一起拼车_点击乘客
    public final static String mobile_employeeTravel_carpooling_carOwner_click = "mobile_employeeTravel_carpooling_carOwner_click";//员工出行_一起拼车_点击车主
    public final static String mobile_employeeTravel_carpooling_carOwnerInfo_click = "mobile_employeeTravel_carpooling_carOwnerInfo_click";//员工出行_一起拼车_点击车主信息
    public final static String mobile_employeeTravel_carpooling_carOwnerInfoModify_click = "mobile_employeeTravel_carpooling_carOwnerInfoModify_click";//员工出行_一起拼车_点击车主修改信息
    public final static String mobile_employeeTravel_carpooling_passenger_release_click = "mobile_employeeTravel_carpooling_passenger_release_click";//员工出行_一起拼车_乘客_点击确认发布
    public final static String mobile_employeeTravel_carpooling_carOwner_release_click = "mobile_employeeTravel_carpooling_carOwner_release_click";//员工出行_一起拼车_车主_点击确认发布
    public final static String mobile_employeeTravel_carpooling_passenger_cancel_click = "mobile_employeeTravel_carpooling_passenger_cancel_click";//员工出行_一起拼车_乘客_点击取消
    public final static String mobile_employeeTravel_carpooling_carOwner_cancel_click = "mobile_employeeTravel_carpooling_carOwner_cancel_click";//员工出行_一起拼车_车主_点击取消
    public final static String mobile_employeeTravel_carpooling_carOwner_OrderReceive_click = "mobile_employeeTravel_carpooling_carOwner_OrderReceive_click";//员工出行_一起拼车_车主_点击接单
    public final static String mobile_employeeTravel_carpooling_carOwner_arrive_click = "mobile_employeeTravel_carpooling_carOwner_arrive_click";//员工出行_一起拼车_车主_点击确认送达乘客
    public final static String mobile_employeeTravel_carpooling_carOwner_reasonSelectionCancel_click = "mobile_employeeTravel_carpooling_carOwner_reasonSelectionCancel_click";//员工出行_一起拼车_车主_取消原因选择
    public final static String mobile_employeeTravel_carpooling_passenger_Rereleaseclick = "mobile_employeeTravel_carpooling_passenger_Rereleaseclick";//员工出行_一起拼车_乘客_点击重新发布
    public final static String mobile_employeeTravel_carpooling_passenger_travelConfirm_click = "mobile_employeeTravel_carpooling_passenger_travelConfirm_click";//员工出行_一起拼车_乘客_点击确认行程
    public final static String mobile_employeeTravel_carpooling_passenger_reasonSelectionCancel_click = "mobile_employeeTravel_carpooling_passenger_reasonSelectionCancel_click";//员工出行_一起拼车_乘客_取消原因选择
    public final static String mobile_employeeTravel_traveDetail_repayment = "mobile_employeeTravel_traveDetail_repayment";//员工出行 一键还款


    public final static String mobile_mine_myNetdisk_click = "mobile_mine_myNetdisk_click";//我的_我的鲸盘点击
    public final static String mobile_myNetdisk_send_click = "mobile_myNetdisk_send_click";//我的鲸盘_点击发送
    public final static String mobile_timline_file_saveToNetDisk_click = "mobile_timline_file_saveToNetDisk_click";//我的鲸盘_点击发送
    public final static String mobile_myNetdisk_move_click = "mobile_myNetdisk_move_click";//我的鲸盘_点击移动
    public final static String mobile_myNetdisk_delete_click = "mobile_myNetdisk_delete_click";//我的鲸盘_点击删除
    public final static String mobile_myNetdisk_print_click = "mobile_myNetdisk_print_click";//我的鲸盘_点击打印
    public final static String mobile_myNetdisk_rename_click = "mobile_myNetdisk_rename_click";//我的鲸盘_点击重命名
    public final static String mobile_myNetdisk_netdisk_click = "mobile_myNetdisk_netdisk_click";//我的鲸盘_点击我的鲸盘
    public final static String mobile_myNetdisk_transfer_list_click = "mobile_myNetdisk_transfer_list_click";//我的鲸盘_点击传输列表
    public final static String mobile_myNetdisk_add_click = "mobile_myNetdisk_add_click";//我的鲸盘_点击+号
    public final static String mobile_myNetdisk_upload_file_click = "mobile_myNetdisk_upload_file_click";//我的鲸盘_上传文件

    public final static String mobile_myNetdisk_new_add_click = "joybox__1653665519310|1";//鲸盘移动端_右上角新建按钮
    public final static String mobile_myNetdisk_new_file_request_click = "joybox__1653665519310|4";//鲸盘移动端_新建文件请求
    public final static String mobile_myNetdisk_select_file_path_click = "joybox__1653665519310|5";//鲸盘移动端_选择存储位置
    public final static String mobile_myNetdisk_select_validity_click = "joybox__1653665519310|6";//鲸盘移动端_选择有效期
    public final static String mobile_myNetdisk_select_file_path_new_folder_click = "joybox__1653665519310|7";//鲸盘移动端_选择存储地址_新建文件夹
    public final static String mobile_myNetdisk_share_copy_link = "joybox__1653665519310|8";//点击事件鲸盘移动端_创建文件请求_复制链接


    public final static String mobile_eval_alert_msg_title_click = "mobile_eval_alert_msg_title_click";//调研弹屏_点击飘带
    public final static String mobile_eval_alert_click = "mobile_eval_alert_click";//调研弹屏
    public final static String mobile_eval_alert_later_click = "mobile_eval_alert_later_click";//调研弹屏_点击稍后作答
    public final static String mobile_eval_alert_protocol_click = "mobile_eval_alert_protocol_click";//调研弹屏_点击保密协议
    public final static String mobile_eval_alert_up_click = "mobile_eval_alert_up_click";//调研弹屏_点击赞
    public final static String mobile_eval_alert_down_click = "mobile_eval_alert_down_click";//调研弹屏_点击踩
    public final static String mobile_eval_alert_comment_click = "mobile_eval_alert_comment_click";//调研弹屏_点击评论
    public final static String mobile_eval_alert_dont_join_click = "mobile_eval_alert_dont_join_click";//调研弹屏_点击我不愿作答
    public final static String mobile_eval_alert_commit_click = "mobile_eval_alert_commit_click";//调研弹屏_点击提交
    public final static String mobile_eval_alert_float_click = "mobile_eval_alert_float_click";//调研弹屏_点击悬浮窗
    public final static String mobile_eval_alert_readed_click = "mobile_eval_alert_readed_click";//调研弹屏_点我已阅读

    public final static String mobile_timline_cloud_print = "mobile_timline_cloud_print";//消息_云打印

    public final static String mobile_mine_setting_general_wifi_auto_download_package = "mobile_mine_setting_general_wifi_auto_download_package";//我的_设置_通用_WIFI下自动下载安装包
    public final static String mobile_mine_setting_general_fontSize_normal = "mobile_mine_setting_general_fontSize_normal";//我的_设置_通用_字号调整_标准
    public final static String mobile_mine_setting_general_fontSize_big = "mobile_mine_setting_general_fontSize_big";//我的_设置_通用_字号调整_较大
    public final static String mobile_mine_setting_general_fontSize_bigger = "mobile_mine_setting_general_fontSize_bigger";//我的_设置_通用_字号调整_超大
    public final static String mobile_mine_setting_general_fontSize_biggest = "mobile_mine_setting_general_fontSize_biggest";//我的_设置_通用_字号调整_最大
    public final static String mobile_mine_setting_general_typeface_normal = "mobile_mine_setting_general_typeface_normal";//我的_设置_通用_字体选择_默认字体
    public final static String mobile_mine_setting_general_typeface_jdLangzheng = "mobile_mine_setting_general_typeface_jdLangzheng";//我的_设置_通用_字体选择_京东朗正体
    public final static String mobile_mine_setting_general_me_lab_click = "mobile_mine_setting_general_me_lab_click";//我的_设置_点击小ME实验室

    public final static String mobile_timline_add_task_click = "mobile_timline_add_task_click";//消息_+_点击任务
    public final static String mobile_timline_add_vote_click = "mobile_timline_add_vote_click";//消息_+_点击投票
    public final static String mobile_timline_longClick_schedule_click = "mobile_timline_longClick_schedule_click";//消息_长按聊天记录_点击日程
    public final static String mobile_timline_add_schedule_click = "mobile_timline_add_schedule_click";//消息_+_点击日程
    public final static String mobile_timline_add_file_click = "mobile_timline_add_file_click";//消息_+_点击文件
    public final static String mobile_timline_add_joyspace_click = "mobile_timline_add_joyspace_click";//消息_+_点击云文档
    public final static String mobile_timline_mail_icon_click = "mobile_timline_mail_icon_click";//消息_点击邮箱图标

    public final static String mobile_around_make_friends_click = "mobile_around_make_friends_click";//身边_点击婚恋交友
    public final static String mobile_around_party_click = "mobile_around_party_click";//身边_点击聚会活动
    public final static String mobile_around_help_each_other_click = "mobile_around_help_each_other_click";//身边_点击互帮互助
    public final static String mobile_around_secondary_market_click = "mobile_around_secondary_market_click";//身边_点击二手市场
    public final static String mobile_around_buy_rent_house_click = "mobile_around_buy_rent_house_click";//身边_点击买房租房
    public final static String mobile_timline_notification_attendance_exception_click = "mobile_timline_notification_attendance_exception_click";//消息_通知_考勤异常
    public final static String mobile_timline_notification_vacation_out_of_date_click = "mobile_timline_notification_vacation_out_of_date_click";//消息_通知_休假过期

    public final static String mobile_me_icon_clock_in_click = "mobile_me_icon_clock_in_click";//ME图标_打卡
    public final static String mobile_me_icon_approve_click = "mobile_me_icon_approve_click";//ME图标_审批
    public final static String mobile_me_icon_scan_click = "mobile_me_icon_scan_click";//ME图标_扫一扫
    public final static String mobile_me_icon_staff_card_click = "mobile_me_icon_staff_card_click";//ME图标_员工卡
    public final static String mobile_me_icon_access_card_click = "mobile_me_icon_access_card_click";//ME图标_门禁卡


    public final static String mobile_timline_system_msg_click = "mobile_timline_system_msg_click";//消息_系统消息
    public final static String mobile_timline_apply_click = "mobile_timline_apply_click";//消息_申请
    public final static String mobile_timline_approve_click = "mobile_timline_approve_click";//消息_审批
    public final static String mobile_timline_sign_click = "mobile_timline_sign_click";//消息_打卡
    public final static String mobile_timline_jumpH5_click = "mobile_timline_jumpH5_click";//消息_跳转到h5
    public final static String mobile_timline_employeeTravel_click = "mobile_timline_employeeTravel_click";//消息_员工用车
    public final static String mobile_LBS_click = "mobile_LBS_click";//6_LBS  定位
    public final static String mobile_resignationData_click = "mobile_resignationData_click";//6_LBS
    public final static String mobile_timline_joyMeeting_detail_click = "mobile_timline_joyMeeting_detail_click";//4_消息_JoyMeeting_查看详情

    public final static String mobile_workbench_remind_click = "mobile_workbench_remind_click";//工作台_任务_提醒一下
    public final static String mobile_workbench_priority_click = "mobile_workbench_priority_click";//工作台_任务_优先级
    public final static String mobile_workbench_sponsor_click = "mobile_workbench_sponsor_click";//工作台_任务_发起人

    public final static String mobile_login_faceLogin_set_click = "mobile_login_faceLogin_set_click";//我-设置-安全-刷脸设置按钮点击事件

    public final static String mobile_event_deeplink_click = "mobile_event_deeplink_click";//Event_deeplink
    public final static String mobile_event_deeplink_error_click = "mobile_event_deeplink_error_click";//Event_deeplink_error

    public final static String mobile_timline_notification_conferenceRoom_reserve_click = "mobile_timline_notification_conferenceRoom_reserve_click";//消息_通知_会议室预定

    public final static String mobile_workbench_banner_click_1647488271311 = "mobile_workbench_banner_click_1647488271311";//消息_通知_会议室预定

    public final static String mobile_Startpage = "mobile_Startpage";//生日、广告曝光 参数：start_page_type：1-生日；2-广告， start_page_ad_id：
    public final static String mobile_Popup = "mobile_Popup";//升级弹窗曝光
    public final static String mobile_Startpage_click = "mobile_Startpage_click";//生日、广告点击  start_page_type：1-生日；2-广告 start_page_ad_id：
    public final static String mobile_Popup_clickclose = "mobile_Popup_clickclose";//升级弹窗关闭
    public final static String mobile_Popup_clickupdate = "mobile_Popup_clickupdate";//升级弹窗更新
    public final static String mobile_punch_wuhen = "mobile_punch_wuhen";//无痕打卡
    public final static String mobile_mobile_event_platform_app_update_alert_click = "Mobile_Event_Platform_App_Update_Alert_Click";// 点击立即升级

    public static final String timline_video_create_schedule = "TimlineVideo_createschedule_1652943937507|1";   //咚咚视频会议
    public static final String mobile_location_quick_daka = "mobile_location_1654656097460|1";   //无痕打卡
    public static final String mobile_location_daka = "mobile_location_1654656097460|2";   //手动打卡
    public static final String mobile_location_use_car = "mobile_location_1654656097460|3";   //用车定位

    public static final String mobile_flowCenter_myApprove_overtimeRecord_click = "mobile_flowCenter_myApprove_overtimeRecord_click";   //审批详情—点击加班事项
    public static final String mobile_flowCenter_myApprove_overtimeDetail_click = "mobile_flowCenter_myApprove_overtimeDetail_click";   //审批详情_点击展开加班明细

    public static final String mobile_joyday_group_calendar = "mobile_calendar_1655370577942|1";    //群日历入口
    public static final String mobile_joyday_contact_calendar = "mobile_joyday__1652859340650|1";    //个人名片日历入口

    public static final String mobile_employeeTravel_traveDetail_application = "mobile_employeeTravel_traveDetail_application";//员工出行_待还款_重新发起申请
    public static final String mobile_employeeTravel_traveDetail_application_Oversubmission = "mobile_employeeTravel_traveDetail_application_Oversubmission";//员工出行_待还款_重新发起申请_超出费用提交
    public static final String mobile_employeeTravel_traveDetail_application_Othersubmission = "mobile_employeeTravel_traveDetail_application_Othersubmission";//员工出行_待还款_重新发起申请_其他驳回提交

    public static final String click_network_diagnosis = "Click_NetworkDiagnosis";//网络诊断
    public static final String click_start_diagnosis = "Click_StartDiagnosis";//开始诊断
    public static final String click_cancel_diagnosis = "Click_CancelDiagnosis";//取消诊断
    public static final String click_diagnosis_again = "Click_DiagnosisAgain";//重新诊断
    public static final String click_share_log = "Click_ShareLog";//分享结果
    public static final String mobile_event_im_pd_success = "mobile_event_im_pd_success";//分享成功埋点
    public static final String mobile_event_im_pd_fail = "mobile_event_im_pd_fail";//分享失败埋点

    public static final String mobile_mine_setting_messageNotification_mandatoryNotification = "mobile_mine_setting_messageNotification_mandatoryNotification";//我的-设置-消息通知-霸屏提醒
    public static final String mobile_mine_setting_messageNotification_mandatoryNotification_calendarHelper = "mobile_mine_setting_messageNotification_mandatoryNotification_calendarHelper";//我的-设置-消息通知-霸屏提醒-日程助手

    /**
     * 多任务窗口
     */
    public static final String mobile_Click_FloatingWindow_Add = "Click_FloatingWindow_Add";//添加悬浮

    public static final String mobile_Click_FloatingWindow_Cancel = "Click_FloatingWindow_Cancel";//取消悬浮

    public static final String mobile_Click_FloatingWindow_Isee = "Click_FloatingWindow_Isee";//点击引导的 "我知道了"

    public static final String mobile_Click_FloatingWindow_ClearAll = "Click_FloatingWindow_ClearAll";// 点击 "清除全部"

    public static final String mobile_Click_FloatingWindow_Close = "Click_FloatingWindow_Close";//点击关闭

    /**
     * im banner
     */
    public static final String timline_banner_click = "timline_banner_click";//消息_banner_查看
    public static final String timline_banner_close_click = "timline_banner_close_click";//消息_banner_关闭

    public static final String mobile_create_optional_attendees = "mobile_create_1653382119144|2";// 可选参会人
    public static final String mobile__JoyWork_joyworkmessage_setting_turnoffreminder_click = "mobile__JoyWork_joyworkmessage_setting_turnoffreminder_click";//在消息列表中点击关闭/开启消息通知


    public static final String MOBILE_WORKBENCH_TOOLS_ALL = "mobile_workbench_tools_all";//工作台_我的工具_全部
    public static final String MOBILE_WORKBENCH_TOOLS_RECENTLY_USE = "mobile_workbench_tools_recentlyuse";//工作台_我的工具_最近使用

    public static final String MOBILE_FILE_OPEN = "mobile_file_open";//文档预览
    public static final String JDME_MAIN_THEME_SAVE = "EXP_Main_Theme_save";//个人中心-换肤保存

    public static final String mobile_3rdApp_start = "mobile_3rdApp_start_";//三方应用-启动耗时
    public static final String mobile_app_ask_info = "mobile_3rdApp_askInfo_";//仅用来统计askinfo调用次数
    public static final String mobile_3rdApp_use = "mobile_3rdApp_use_";//三方应用-使用统计
    public static final String js_sdk_call_native = "JSSDK_CallNative";
    public static final String js_sdk_old_call_native = "JSSDK_Old_CallNative";


    public static String EXP_MAIN_THEME_GLOBAL_PREVIEW = "EXP_Main_Theme_global_preview";//个人中心-主题换肤-全局皮肤-预览
    public static String EXP_MAIN_THEME_GLOBAL_USE = "EXP_Main_Theme_global_use";//个人中心-主题换肤-全局皮肤-使用
    public static String Mobile_Event_Platform_TimeCalculation_AppClick = "Mobile_Event_Platform_TimeCalculation_AppClick"; //活动时长


    /**
     * 安全需求 2023/10
     */
    public static String Mobile_Event_PlatformSafety_AnyPage_ScreenShot = "Mobile_Event_PlatformSafety_AnyPage_ScreenShot";// 截屏事件
    public static String Mobile_Event_PlatformSafety_AnyPage_ScreenShotUpload = "Mobile_Event_PlatformSafety_AnyPage_ScreenShotUpload";// 截屏图片上传成功
    public static String Mobile_Event_PlatformSafety_AnyPage_ScreenRecordShotUpload = "Mobile_Event_PlatformSafety_AnyPage_ScreenRecordShotUpload";// 截屏图片上传成功
    public static String Mobile_Event_platformSafety_AnyPage_CopyAndEnterBackground = "Mobile_Event_platformSafety_AnyPage_CopyAndEnterBackground";//复制内容并切换App到后台
    public static String Mobile_Event_PlatformSafety_AnyPage_Copy = "Mobile_Event_PlatformSafety_AnyPage_Copy";//复制

    public static String Mobile_Event_PlatformSafety_AnyPage_FilePreview = "Mobile_Event_PlatformSafety_AnyPage_FilePreview";//文件预览

    /**
     * 慧记详情页埋点
     */
    public static String Mobile_Event_Minute_Textrecord = "Mobile_Event_Minute_Textrecord";//慧记详情页-文字记录
    public static String Mobile_Event_Minute_Keyword = "Mobile_Event_Minute_Keyword";//慧记详情页-关键词
    public static String Mobile_Event_Minute_Basicinfo = "Mobile_Event_Minute_Basicinfo";//慧记详情页-基础信息
    public static String Mobile_Event_Minute_Search = "Mobile_Event_Minute_Search";//慧记详情页-搜索
    public static String Mobile_event_Minutes_DetailHome_AISummary = "Mobile_event_Minutes_DetailHome_AISummary";   //慧记详情页-AI总结
    /**
     * 慧记列表页埋点
     */
    public static String Mobile_Event_Minute_Mycontent = "Mobile_Event_Minute_Mycontent";//慧记列表页-我创建的
    public static String Mobile_Event_Minute_Recieved = "Mobile_Event_Minute_Recieved";//慧记列表页-我收到的
    public static String Mobile_Event_Minute_Recentadded = "Mobile_Event_Minute_Recentadded";//慧记列表页-最近新增
    public static String Mobile_Event_Minute_Recentaddedcreated = "Mobile_Event_Minute_Recentaddedcreated";//慧记列表页-最近新增-我创建的
    public static String Mobile_Event_Minute_Recentaddedcreator = "Mobile_Event_Minute_Recentaddedcreator";//慧记列表页-最近新增-创建人
    public static String Mobile_Event_Minute_Recentaddedsender = "Mobile_Event_Minute_Recentaddedsender";//慧记列表页-我收到的-发送人
    public static String Mobile_Event_Minute_Recentlyopen = "Mobile_Event_Minute_Recentlyopen";//慧记列表页-最近打开
    public static String Mobile_Event_Minute_More = "Mobile_Event_Minute_More";//慧记列表页-更多菜单
    public static String Mobile_Event_Minute_Copylink = "Mobile_Event_Minute_Copylink";//慧记列表页-更多-复制链接
    public static String Mobile_Event_Minute_Share = "Mobile_Event_Minute_Share";//慧记列表页-更多-分享
    public static String Mobile_Event_Minute_Rename = "Mobile_Event_Minute_Rename";//慧记列表页-更多-重命名
    public static String Mobile_Event_Tabbar_MainTab_Clicked = "Mobile_Event_Tabbar_MainTab_Clicked";//tabbar Tab页的点击事件
    public static String Mobile_Event_Platform_RedDot_Clicked = "Mobile_Event_Platform_RedDot_Clicked";//红点点击
    public static String Mobile_Event_Platform_Permission_Getting = "Mobile_Event_Platform_Permission_Getting";//获取系统通用权限
    public static String Mobile_Event_Platform_AppLifeCircle_Triggered = "Mobile_Event_Platform_AppLifeCircle_Triggered";//App生命周期事件
    // 设备Root上报
    public static String MOBILE_EVENT_PLATFORM_SAFETY_JAILBREAK_REPORT = "Mobile_Event_PlatformSafety_Jailbreak_Report";
    // 详情页点击「发言人」tab
    public static String MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB = "Mobile_event_Minutes_DetailHome_Spoketab";
    // 打开「发言片段」二级列表
    public static String MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB_SPEECH_CLIPS = "Mobile_event_Minutes_DetailHome_Spoketab_SpeechClips";
    // 点击「发言片段」二级列表的片段
    public static String MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB_SPEECH_CLIPS_CLIP = "Mobile_event_Minutes_DetailHome_Spoketab_SpeechClips_Clip";
    // 加载失败，点击刷新
    public static String MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB_REFRESH = "Mobile_event_Minutes_DetailHome_Spoketab_Refresh";
    // 慧记-与我相关 Tab
    public static String MOBILE_EVENT_MINUTES_MENTIONS = "Mobile_event_Minutes_DetailHome_Mentions";
    // 慧记-与我相关-「查看上下文」按钮
    public static String MOBILE_EVENT_MINUTES_DETAIL_HOME_MENTIONS_VIEW_CONTEXT = "Mobile_event_Minutes_DetailHome_Mentions_ViewContext";
    // 慧记-「与我相关-文字」跳转播放
    public static String MOBILE_EVENT_MINUTES_DETAIL_HOME_MENTIONS_CLICK_SPEECH = "Mobile_event_Minutes_DetailHome_Mentions_ClickSpeech";
    // 慧记-「与我相关」搜索
    public static String MOBILE_EVENT_MINUTES_DETAIL_HOME_MENTIONS_SEARCH = "Mobile_event_Minutes_DetailHome_Mentions_Search";

    public static final String Mobile_Page_OpenAPI_IMApi = "Mobile_Page_OpenAPI_IMApi";
    public static final String Mobile_Event_OpenAPI_IMApi_GetGroupInfo = "Mobile_Event_OpenAPI_IMApi_GetGroupInfo";
    public static final String Mobile_Event_OpenAPI_IMApi_JoinGroup = "Mobile_Event_OpenAPI_IMApi_JoinGroup";
    public static final String Mobile_Event_OpenAPI_IMApi_SendMessageCard = "Mobile_Event_OpenAPI_IMApi_SendMessageCard";

    public static final String Mobile_Event_MEAI_IM_Main_ck = "Mobile_Event_MEAI_IM_Main_ck";

    /**
     * 聊天横幅，半屏弹窗埋点
     */
    public static final String mobile_event_im_chatwindow_turnon = "mobile_event_im_chatwindow_turnon";
    public static final String mobile_event_im_chatwindow_turnoff = "mobile_event_im_chatwindow_turnoff";
    public static final String mobile_event_im_chatwindow_content = "mobile_event_im_chatwindow_content";
    public static final String mobile_event_im_chatwindow_session = "mobile_event_im_chatwindow_session";
    public static final String mobile_event_im_chatwindow_notice = "mobile_event_im_chatwindow_notice";
    public static final String mobile_event_im_chatwindow_popup = "mobile_event_im_chatwindow_popup";
    public static final String mobile_event_im_chatwindow_openchat = "mobile_event_im_chatwindow_openchat";
    public static final String mobile_event_im_chatwindow_fullscreen = "mobile_event_im_chatwindow_fullscreen";
    public static final String mobile_event_im_chatwindow_latter = "mobile_event_im_chatwindow_latter";
    public static final String mobile_event_im_chatwindow_30mins = "mobile_event_im_chatwindow_30mins";
    public static final String mobile_event_im_chatwindow_1hour = "mobile_event_im_chatwindow_1hour";
    public static final String mobile_event_im_chatwindow_2hours = "mobile_event_im_chatwindow_2hours";
    public static final String mobile_event_im_chatwindow_view = "mobile_event_im_chatwindow_view";

    public static final String mobile_event_im_quickapplications = "Mobile_Event_IM_QuickApplications";

    public static final String mobie_platform_floating_slide = "mobie_platform_floating_slide";
    public static final String mobie_platform_floating_open = "mobie_platform_floating_open";
    public static final String mobie_platform_floating_allin = "mobie_platform_floating_allin";

    // MEAI埋点
    public static final String Mobile_Page_MEAI_Main_Home = "Mobile_Page_MEAI_Main_Home";
    public static final String Mobile_Event_MEAI_Search_Main_QA_ck = "Mobile_Event_MEAI_Search_Main_QA_ck";
    public static final String Mobile_Event_MEAI_Search_Tab_ck = "Mobile_Event_MEAI_Search_Tab_ck";
    public static final String Mobile_Event_MEAI_Search_More_ck = "Mobile_Event_MEAI_Search_More_ck";

    public static final String Mobile_Event_Platform_ScanContent = "Mobile_Event_Platform_ScanContent";
    public static final String Mobile_Event_Platform_Mini_Start = "Mobile_Event_Platform_Mini_Start";

    //文件预览
    public static String Mobile_Event_PlatformSafety_AnyPage_FilePreviewOpenOption = "Mobile_Event_PlatformSafety_AnyPage_FilePreviewOpenOption";

    //主搜埋点
    public static final String Mobile_Event_UnifiedSearch_Discovery_ck = "Mobile_Event_UnifiedSearch_Discovery_ck";
    public static final String Mobile_Event_UnifiedSearch_Discovery_Hide_ck = "Mobile_Event_UnifiedSearch_Discovery_Hide_ck";
    public static final String Mobile_Event_UnifiedSearch_Discovery_Display_ck = "Mobile_Event_UnifiedSearch_Discovery_Display_ck";
    public static final String Mobile_Event_UnifiedSearch_Discovery_exposure = "Mobile_Event_UnifiedSearch_Discovery_exposure";

    public static final String Mobile_Event_MEAI_docSearch_docFilterType_ck = "Mobile_Event_MEAI_docSearch_docFilterType_ck";


    public static final String mobie_filepreview_view = "mobie_filepreview_view";
    public static final String mobie_filepreview_download = "mobie_filepreview_download";
    public static final String mobie_filepreview_downloadcancel = "mobie_filepreview_downloadcancel";
    public static final String mobie_filepreview_appopen = "mobie_filepreview_appopen";


    //流程
    public static final String Mobile_Event_UnifiedSearch_Process_exposure = "Mobile_Event_UnifiedSearch_Process_exposure";
    public static final String Mobile_Event_UnifiedSearch_Process_Tab_ck = "Mobile_Event_UnifiedSearch_Process_Tab_ck";
    public static final String Mobile_Event_UnifiedSearch_Process_ck = "Mobile_Event_UnifiedSearch_Process_ck";
    //审批
    public static final String Mobile_Event_UnifiedSearch_Approval_exposure = "Mobile_Event_UnifiedSearch_Approval_exposure";
    public static final String Mobile_Event_UnifiedSearch_Approval_Tab_ck = "Mobile_Event_UnifiedSearch_Approval_Tab_ck";
    public static final String Mobile_Event_UnifiedSearch_Approval_ck = "Mobile_Event_UnifiedSearch_Approval_ck";

    //开放能力埋点
    public static final String Mobile_Event_OpenAPI_Call = "Mobile_Event_OpenAPI_Call";

    public static final String Mobile_Event_Web_Url_diff = "Mobile_Event_Web_Url_diff";

    //邮箱埋点
    public static final String Mobile_Page_Mail_Account = "Mobile_Page_Mail_Account";
    public static final String Mobile_Event_Mail_Account_CancelBind = "Mobile_Event_Mail_Account_CancelBind";
    public static final String Mobile_Event_Mail_Account_Unbind = "Mobile_Event_Mail_Account_Unbind";
    public static final String Mobile_Event_Mail_Account_Bind = "Mobile_Event_Mail_Account_Bind";
    public static final String Mobile_Event_Mail_Account_ConfirmBind = "Mobile_Event_Mail_Account_ConfirmBind";

    public static final String Mobile_Event_Appdirectory_entrance_click = "Mobile_Event_Appdirectory_entrance_click";
    //自动翻译埋点
    public static final String Mobile_Event_AutoTranslate = "Mobile_Event_Platform_topME_click";
    public static final String Mobile_Event_AutoTranslate_Click_Param = "jdme_Tabbar_click_parameters";
    public static final String Mobile_Event_AutoTranslate_Setting_Status_Open = "topme_settings_AutoTranslationOpen";
    public static final String Mobile_Event_AutoTranslate_Setting_Status_Off = "topme_settings_AutoTranslationOff";
    // 点击「实时翻译-独立弹出」按钮
    public static final String MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_DETACH = "Mobile_event_Minutes_RealtimeTranslation_Detach";
    // 切换翻译语言
    public static final String MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_TRANSLATE = "Mobile_event_Minutes_RealtimeTranslation_Translate";
    // 选择双语展示
    public static final String MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_SHUANGYU = "Mobile_event_Minutes_RealtimeTranslation_Shuangyu";
    // 选择仅展示译文
    public static final String MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_YIWEN = "Mobile_event_Minutes_RealtimeTranslation_Yiwen";

    //新版个人中心埋点
    public static final String mobile_EXP_Main_HRAI_click = "mobile_EXP_Main_HRAI_click";
    public static final String mobile_EXP_Main_JoyHR = "mobile_EXP_Main_JoyHR";
    public static final String mobile_EXP_Main_JoyHR_apps = "mobile_EXP_Main_JoyHR_apps";
    public static final String EXP_Main_Activity_header = "EXP_Main_Activity_header";
    public static final String EXP_Main_Activity_body_item = "EXP_Main_Activity_body_item";
    public static final String mobile_EXP_Main_Service = "mobile_EXP_Main_Service";
    public static final String mobile_EXP_Main_Service_HRClick = "mobile_EXP_Main_Service_HRClick";
    public static final String mobile_EXP_Main_Welfare = "mobile_EXP_Main_Welfare";
    public static final String mobile_EXP_Main_Welfare_HRClick = "mobile_EXP_Main_Welfare_HRClick";
    public static final String mobile_EXP_Main_Course = "mobile_EXP_Main_Course";
    public static final String mobile_EXP_Main_Course_click = "mobile_EXP_Main_Course_click";
    public static final String mobile_EXP_Main_Moments = "mobile_EXP_Main_Moments";
    public static final String mobile_EXP_Main_JoyHR_search = "mobile_EXP_Main_JoyHR_search";

    public static final String Main_JoyHR_FORMAT = "mobile_EXP_Main_JoyHR_";
}
