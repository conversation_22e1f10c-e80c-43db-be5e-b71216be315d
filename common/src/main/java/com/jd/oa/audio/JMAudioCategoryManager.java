package com.jd.oa.audio;

import android.view.Gravity;

import androidx.annotation.NonNull;

import com.jd.jdvideoplayer.live.SmallTV;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.multitask.SmallTvWindowManager;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JMAudioCategoryManager {
    //高优先级
    public static final int JME_AUDIO_CATEGORY_VIDEO_MEETING = 1; //咚咚视频会议(高优先级)
    public static final int JME_AUDIO_CATEGORY_VOIP = 2; //咚咚VoIP语音会议(高优先级)
    public static final int JME_AUDIO_CATEGORY_JOY_MEETING = 3; //JoyMeeting会议(高优先级)
    public static final int JME_AUDIO_CATEGORY_ME_MEETING = 4; //JBMeeting会议(高优先级)

    //中优先级
    public static final int JME_AUDIO_CATEGORY_JOY_NOTE = 201; //慧记语音录制(中优先级)
    public static final int JME_AUDIO_CATEGORY_ME_TV = 202; //小ME TV(中优先级)
    public static final int JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY = 203; //慧记视频播放(中优先级)
    public static final int JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY = 204; //慧记音频播放(中优先级)


    //低优先级
    public static final int JME_AUDIO_CATEGORY_VIDEO_PLAY = 100; //播放咚咚视频(低优先级)
    public static final int JME_AUDIO_CATEGORY_VIDEO_RECORD = 101; //录制咚咚视频(低优先级)
    public static final int JME_AUDIO_CATEGORY_VOICE_PLAY = 102; //播放咚咚语音(低优先级)
    public static final int JME_AUDIO_CATEGORY_VOICE_RECORD = 103; //录制咚咚语音(低优先级)
    public static final int JME_AUDIO_CATEGORY_MEMO_PLAY = 104; //播放小记语音(低优先级)
    public static final int JME_AUDIO_CATEGORY_MEMO_RECORD = 105; //录制小记语音(低优先级)
    public static final int JME_AUDIO_CATEGORY_WEB_APP = 106; // WEB声道占用(低优先级)
    public static final int JME_AUDIO_CATEGORY_AUDIO_PLAYER = 107; //播放器(低优先级)

    //其他优先级
    public static final int JME_AUDIO_CATEGORY_IDLE = 0; // 默认空闲状态
    public static final int JME_AUDIO_CATEGORY_ME_OTHER = 400; //其他音频(低优先级)

    public static final String AUDIO_MANAGER = MELogUtil.TAG_AUDIO;
    private int currentAudioCategory = JME_AUDIO_CATEGORY_IDLE;
    private String currentSecret;
    private final Map<Integer, Runnable> audioCategoryMap = new HashMap<>();

    public void setOutCall(boolean outCall) {
        this.outCall = outCall;
    }

    private boolean outCall;

    private JMAudioCategoryManager() {
    }

    public static JMAudioCategoryManager getInstance() {
        return Inner.instance;
    }

    private static class Inner {
        private static final JMAudioCategoryManager instance = new JMAudioCategoryManager();
    }

    public static boolean isHighPriority(int category) {
        switch (category) {
            case JME_AUDIO_CATEGORY_VIDEO_MEETING:
            case JME_AUDIO_CATEGORY_VOIP:
            case JME_AUDIO_CATEGORY_JOY_MEETING:
            case JME_AUDIO_CATEGORY_ME_MEETING:
                return true;
            default:
                return false;
        }
    }

    public static boolean isMediumPriority(int category) {
        switch (category) {
            case JME_AUDIO_CATEGORY_JOY_NOTE:
            case JME_AUDIO_CATEGORY_ME_TV:
            case JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY:
            case JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY:
                return true;
            default:
                return false;
        }
    }

    public static boolean isLowPriority(int category) {
        switch (category) {
            case JME_AUDIO_CATEGORY_VIDEO_PLAY:
            case JME_AUDIO_CATEGORY_VIDEO_RECORD:
            case JME_AUDIO_CATEGORY_VOICE_PLAY:
            case JME_AUDIO_CATEGORY_VOICE_RECORD:
            case JME_AUDIO_CATEGORY_MEMO_PLAY:
            case JME_AUDIO_CATEGORY_MEMO_RECORD:
            case JME_AUDIO_CATEGORY_WEB_APP:
            case JME_AUDIO_CATEGORY_AUDIO_PLAYER:
                return true;
            default:
                return false;
        }
    }

    /**
     * 判断当时是否可以设置成功，并不真正去设置
     *
     * @return {@code true}: 可以设置 <br>{@code false}: 不可以设置
     */
    public boolean canSetAudioCategory(int jMAudioCategory) {
        MELogUtil.localV(AUDIO_MANAGER, "canSetAudioCategory---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory);
        MELogUtil.onlineV(AUDIO_MANAGER, "canSetAudioCategory---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory);
        synchronized (JMAudioCategoryManager.class) {
            JMEAudioCategorySet jmeAudioCategorySet = setAudioCategory(jMAudioCategory, true, true);
            MELogUtil.localV(AUDIO_MANAGER, "canSetAudioCategory---jmeAudioCategorySet.available=" + jmeAudioCategorySet.available);
            MELogUtil.onlineV(AUDIO_MANAGER, "canSetAudioCategory---jmeAudioCategorySet.available=" + jmeAudioCategorySet.available);
            return jmeAudioCategorySet.available;
        }
    }

    /**
     * 设置将要使用Audio的业务，默认显示错误提示，暂时只支持在主进程使用。
     * 高优先级会踢掉低优先级的，设置成功请保存返回值JMEAudioCategorySet的secret。
     * 每次使用结束以后一定要成对调用{@link #releaseAudio(String)}
     *
     * @param jMAudioCategory Audio类型
     * @return 和iOS统一返回对象为：{@link JMEAudioCategorySet}
     */
    public @NonNull
    JMEAudioCategorySet setAudioCategory(int jMAudioCategory) {
        return setAudioCategory(jMAudioCategory, false, true);
    }

    public @NonNull
    JMEAudioCategorySet setAudioCategory(int jMAudioCategory, Runnable pauseCurrent) {
        audioCategoryMap.put(jMAudioCategory, pauseCurrent);
        return setAudioCategory(jMAudioCategory, false, true);
    }

    public @NonNull
    JMEAudioCategorySet setAudioCategory(int jMAudioCategory, boolean needToast) {
        return setAudioCategory(jMAudioCategory, false, needToast);
    }

    private @NonNull
    JMEAudioCategorySet setAudioCategory(int jMAudioCategory, boolean justCheck, boolean needToast) {
        MELogUtil.localV(AUDIO_MANAGER, "setAudioCategory---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory + "  justCheck=" + justCheck);
        MELogUtil.onlineV(AUDIO_MANAGER, "setAudioCategory---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory + "  justCheck=" + justCheck);
        synchronized (JMAudioCategoryManager.class) {
            //如果声道正在被高优先级占用，显示toast
            String tips = null;
            switch (currentAudioCategory) {
                case JME_AUDIO_CATEGORY_VIDEO_MEETING:
                case JME_AUDIO_CATEGORY_ME_MEETING:
                    tips = AppBase.getAppContext().getString(R.string.video_call_is_on);
                    break;
                case JME_AUDIO_CATEGORY_VOIP:
                    tips = AppBase.getAppContext().getString(R.string.voice_call_is_on);
                    break;
                case JME_AUDIO_CATEGORY_JOY_MEETING:
                    tips = AppBase.getAppContext().getString(R.string.joy_meeting_is_on);
                    break;
                default:
                    break;
            }
            if (tips != null && !justCheck) {
                MELogUtil.localV(AUDIO_MANAGER, "showToast---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory + "  justCheck=" + justCheck);
                MELogUtil.onlineV(AUDIO_MANAGER, "showToast---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory + "  justCheck=" + justCheck);
                if (needToast) {
                    ToastUtils.showToast(tips,Gravity.CENTER);
                }
            }

            //声道优先级判断
            //当前声道状态为空闲
            if (currentAudioCategory == JME_AUDIO_CATEGORY_IDLE) {
                if (outCall && jMAudioCategory == JME_AUDIO_CATEGORY_JOY_NOTE) {
                    if (needToast) {
                        ToastUtils.showToast(AppBase.getAppContext().getString(R.string.joynote_audio_is_out_tips), Gravity.CENTER);
                    }
                    return setResult(false, null);
                }
                MELogUtil.localV(AUDIO_MANAGER, "setAudioCategory---2");
                MELogUtil.onlineV(AUDIO_MANAGER, "setAudioCategory---2");
                return setSuccess(jMAudioCategory, justCheck);
            }

            //当前声道被占用
            if (jMAudioCategory == JME_AUDIO_CATEGORY_ME_OTHER) { //其他优先级不覆盖，比如打卡音效等
                return setResult(false, null);
            }

            if (isHighPriority(currentAudioCategory)) { //当前声道正在被高优先级应用占用
                //高优先级不能被任何优先级覆盖
                MELogUtil.localV(AUDIO_MANAGER, "setAudioCategory---1");
                MELogUtil.onlineV(AUDIO_MANAGER, "setAudioCategory---1");
                return setResult(false, null);
            } else if (isMediumPriority(currentAudioCategory)) { //当前声道正在被中优先级应用占用
                if (isHighPriority(jMAudioCategory)) { //被高优先级占用，通知声道被占用
                    if (!justCheck) {
                        switch (currentAudioCategory) {
                            case JME_AUDIO_CATEGORY_ME_TV:
                                SmallTV.getInstance().notifyFinishPlayer();
                                if (AppBase.getAppContext() != null) {
                                    SmallTvWindowManager.getInstance(AppBase.getAppContext()).close(null);
                                    SmallTvWindowManager.getInstance(AppBase.getAppContext()).setClosed(true);
                                }
                                break;
                            case JME_AUDIO_CATEGORY_JOY_NOTE:
                            case JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY:
                            case JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY:
                                executeRunnableForCategory(currentAudioCategory);
                                break;
                            default:
                                break;
                        }
                    }
                    return setSuccess(jMAudioCategory, justCheck);
                } else { //中低优先级无法覆盖
                    if (needToast && !justCheck) {
                        if (currentAudioCategory == JME_AUDIO_CATEGORY_ME_TV
                                && jMAudioCategory != JME_AUDIO_CATEGORY_ME_TV
                        ) {
                            ToastUtils.showToast(AppBase.getAppContext().getString(R.string.small_tv_is_on), Gravity.CENTER);
                        }
                        if (currentAudioCategory == JME_AUDIO_CATEGORY_JOY_NOTE
                                && jMAudioCategory != JME_AUDIO_CATEGORY_JOY_NOTE
                        ) {
                            ToastUtils.showToast(AppBase.getAppContext().getString(R.string.joynote_audio_is_playing_tips), Gravity.CENTER);
                        }
                        if (currentAudioCategory == JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY
                                && jMAudioCategory != JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY
                        ) {
                            ToastUtils.showToast(AppBase.getAppContext().getString(R.string.joynote_is_playing_tips), Gravity.CENTER);
                        }
                        if (currentAudioCategory == JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY
                                && jMAudioCategory != JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY
                        ) {
                            ToastUtils.showToast(AppBase.getAppContext().getString(R.string.joynote_is_playing_tips), Gravity.CENTER);
                        }
                    }
                    MELogUtil.localV(AUDIO_MANAGER, "setAudioCategory---4");
                    MELogUtil.onlineV(AUDIO_MANAGER, "setAudioCategory---4");
                    return setResult(false, null);
                }
            } else { //当前声道正在被低优先级应用占用
                //低优先级直接被覆盖，通知各类应用声道被占用
                if (currentAudioCategory == JME_AUDIO_CATEGORY_WEB_APP &&
                        jMAudioCategory != JME_AUDIO_CATEGORY_WEB_APP && !justCheck) { //h5声道被抢占
                    //通知h5声道被抢占
                    try {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("eventName", "AudioCategoryDidChangedNotification");
                        JSONObject eventParams = new JSONObject();
                        eventParams.put("JMEAudioCategoryNotificationNewValue", jMAudioCategory);
                        eventParams.put("JMEAudioCategoryNotificationOldValue", currentAudioCategory);
                        eventParams.put("JMEAudioCategoryNotificationIsHighPrioInterrupt", 1);
                        jsonObject.put("eventParams", eventParams);
                        JmEventDispatcher.dispatchEvent(AppBase.getAppContext(), "AudioCategoryDidChangedNotification", jsonObject);
                    } catch (Exception e) {
                        MELogUtil.localE(AUDIO_MANAGER, e.getMessage());
                    }
                }
                MELogUtil.localV(AUDIO_MANAGER, "setAudioCategory---3");
                MELogUtil.onlineV(AUDIO_MANAGER, "setAudioCategory---3");
                return setSuccess(jMAudioCategory, justCheck);
            }
        }
    }

    @NotNull
    private JMEAudioCategorySet setResult(boolean success, String secret) {
        MELogUtil.localV(AUDIO_MANAGER, "setResult---success=" + success + "  secret=" + secret + "  currentSecret=" + currentSecret);
        MELogUtil.onlineV(AUDIO_MANAGER, "setResult---success=" + success + "  secret=" + secret + "  currentSecret=" + currentSecret);
        return new JMEAudioCategorySet(success, currentAudioCategory, secret);
    }

    @NotNull
    private JMEAudioCategorySet setSuccess(int jMAudioCategory, boolean justCheck) {
        MELogUtil.localV(AUDIO_MANAGER, "setSuccess---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory + "  justCheck=" + justCheck);
        MELogUtil.onlineV(AUDIO_MANAGER, "setSuccess---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  jMAudioCategory=" + jMAudioCategory + "  justCheck=" + justCheck);
        synchronized (JMAudioCategoryManager.class) {
            if (justCheck) {
                MELogUtil.localV(AUDIO_MANAGER, "setSuccess---1");
                MELogUtil.onlineV(AUDIO_MANAGER, "setSuccess---1");
                return setResult(true, null);
            }
            resetSecret();
            currentAudioCategory = jMAudioCategory;
            MELogUtil.localV(AUDIO_MANAGER, "setSuccess---2");
            MELogUtil.onlineV(AUDIO_MANAGER, "setSuccess---2");
            return setResult(true, currentSecret);
        }
    }

    private void executeRunnableForCategory(int audioCategory) {
        Runnable action = audioCategoryMap.get(audioCategory);
        if (action != null) {
            action.run();
        }
    }

    /**
     * 释放Audio占用，必须和{@link #setAudioCategory(int)}成对使用
     *
     * @param secret 之前获取的密钥，如果不传可能会release失败
     * @return 成功返回true，失败返回false
     */
    public boolean releaseAudio(String secret) {
        MELogUtil.localV(AUDIO_MANAGER, "releaseAudio---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  secret=" + secret);
        MELogUtil.onlineV(AUDIO_MANAGER, "releaseAudio---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret + "  secret=" + secret);
        synchronized (JMAudioCategoryManager.class) {
            if (secret != null) {
                if (currentSecret == null) {
                    currentAudioCategory = JME_AUDIO_CATEGORY_IDLE;
                    MELogUtil.localV(AUDIO_MANAGER, "releaseAudio---1--currentAudioCategory=" + currentAudioCategory);
                    MELogUtil.onlineV(AUDIO_MANAGER, "releaseAudio---1--currentAudioCategory=" + currentAudioCategory);
                    return true;
                }
                if (secret.equals(currentSecret)) {
                    currentAudioCategory = JME_AUDIO_CATEGORY_IDLE;
                    currentSecret = null;
                    MELogUtil.localV(AUDIO_MANAGER, "releaseAudio---2--currentAudioCategory=" + currentAudioCategory);
                    MELogUtil.onlineV(AUDIO_MANAGER, "releaseAudio---2--currentAudioCategory=" + currentAudioCategory);
                    return true;
                }
            } else {
                if (currentSecret == null) {
                    currentAudioCategory = JME_AUDIO_CATEGORY_IDLE;
                    MELogUtil.localV(AUDIO_MANAGER, "releaseAudio---3--currentAudioCategory=" + currentAudioCategory);
                    MELogUtil.onlineV(AUDIO_MANAGER, "releaseAudio---3--currentAudioCategory=" + currentAudioCategory);
                    return true;
                }
            }
            MELogUtil.localV(AUDIO_MANAGER, "releaseAudio---4--currentAudioCategory=" + currentAudioCategory);
            MELogUtil.onlineV(AUDIO_MANAGER, "releaseAudio---4--currentAudioCategory=" + currentAudioCategory);
            return false;
        }
    }

    private void resetSecret() {
        MELogUtil.localV(AUDIO_MANAGER, "resetSecret---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret);
        MELogUtil.onlineV(AUDIO_MANAGER, "resetSecret---0--currentAudioCategory=" + currentAudioCategory + "  currentSecret=" + currentSecret);
        if (currentSecret != null) {
            return;
        }
        currentSecret = UUID.randomUUID().toString().replace("-", "");
        MELogUtil.localV(AUDIO_MANAGER, "resetSecret---1");
        MELogUtil.onlineV(AUDIO_MANAGER, "resetSecret---1");
    }

    /**
     * 内部使用，第三方不使用
     */
    public void releaseJoyMeeting() {
        MELogUtil.localV(AUDIO_MANAGER, "releaseJoyMeeting---0--currentSecret=" + currentSecret);
        MELogUtil.onlineV(AUDIO_MANAGER, "releaseJoyMeeting---0--currentSecret=" + currentSecret);
        releaseAudio(currentSecret);
    }

    public void releaseSmallTv() {
        if (currentAudioCategory == JME_AUDIO_CATEGORY_ME_TV) {
            releaseAudio(currentSecret);
        }
    }

    public void releaseAsr() {
        if (currentAudioCategory == JME_AUDIO_CATEGORY_MEMO_RECORD) {
            releaseAudio(currentSecret);
        }
    }

    public void releaseJoyNote() {
        if (currentAudioCategory == JME_AUDIO_CATEGORY_JOY_NOTE) {
            releaseAudio(currentSecret);
        }
        audioCategoryMap.remove(JME_AUDIO_CATEGORY_JOY_NOTE);
    }

    public void releaseJoyNoteAudio() {
        if (currentAudioCategory == JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY) {
            releaseAudio(currentSecret);
        }
        audioCategoryMap.remove(JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY);
    }

    public void releaseJoyNoteVideo() {
        if (currentAudioCategory == JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY) {
            releaseAudio(currentSecret);
        }
        audioCategoryMap.remove(JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY);
    }

    public int getCurrentAudioCategory() {
        return currentAudioCategory;
    }


    /**
     * 内部使用，第三方不使用
     */
    public void init() {
        MELogUtil.localV(AUDIO_MANAGER, "init---0--currentSecret=" + currentSecret);
        MELogUtil.onlineV(AUDIO_MANAGER, "init---0--currentSecret=" + currentSecret);
        releaseAudio(currentSecret);
    }

    /**
     * 和iOS统一的Audio状态返回对象
     */
    public static class JMEAudioCategorySet {
        /**
         * 如果为true则Audio类型修改成功
         */
        public boolean available;
        /**
         * 当前的Audio类型
         */
        public int currentAudioCategory;
        /**
         * 如果有值，使用者需要保存此值，release的时候需要使用。如果此值为null，则不用保存
         */
        public String secret;

        public JMEAudioCategorySet(boolean available, int currentAudioCategory, String secret) {
            this.available = available;
            this.currentAudioCategory = currentAudioCategory;
            this.secret = secret;
        }
    }
}
