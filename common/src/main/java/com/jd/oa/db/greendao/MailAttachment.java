package com.jd.oa.db.greendao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table MAIL_ATTACHMENT.
 */
public class MailAttachment {

    private String mailId;
    private Integer index;
    private String localPath;
    private String name;
    private Integer size;
    private Boolean isInline;
    private String contentId;
    private Long id;
    private Long timestamp;

    public MailAttachment() {
    }

    public MailAttachment(Long id) {
        this.id = id;
    }

    public MailAttachment(String mailId, Integer index, String localPath, String name, Integer size, Boolean isInline, String contentId, Long id, Long timestamp) {
        this.mailId = mailId;
        this.index = index;
        this.localPath = localPath;
        this.name = name;
        this.size = size;
        this.isInline = isInline;
        this.contentId = contentId;
        this.id = id;
        this.timestamp = timestamp;
    }

    public String getMailId() {
        return mailId;
    }

    public void setMailId(String mailId) {
        this.mailId = mailId;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Boolean getIsInline() {
        return isInline;
    }

    public void setIsInline(Boolean isInline) {
        this.isInline = isInline;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

}
