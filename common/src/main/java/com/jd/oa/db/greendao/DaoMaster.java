package com.jd.oa.db.greendao;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteDatabase.CursorFactory;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;
import de.greenrobot.dao.AbstractDaoMaster;
import de.greenrobot.dao.identityscope.IdentityScopeType;

import com.jd.oa.db.greendao.HomePageDBDao;
import com.jd.oa.db.greendao.MorePageDBDao;
import com.jd.oa.db.greendao.WorkPlaceDBDao;
import com.jd.oa.db.greendao.ChineseHolidayDBDao;
import com.jd.oa.db.greendao.DidiOrderDBDao;
import com.jd.oa.db.greendao.DidiAddressHistoryDBDao;
import com.jd.oa.db.greendao.TimlineSearchHistoryDao;
import com.jd.oa.db.greendao.ImageCacheDao;
import com.jd.oa.db.greendao.TravelSearchHistoryDBDao;
import com.jd.oa.db.greendao.ReimburseDeptDBDao;
import com.jd.oa.db.greendao.ReimburseProjectDBDao;
import com.jd.oa.db.greendao.ResponseCacheDao;
import com.jd.oa.db.greendao.PushMessageDao;
import com.jd.oa.db.greendao.DeletedMessageDao;
import com.jd.oa.db.greendao.MailCertDao;
import com.jd.oa.db.greendao.MailInfoDao;
import com.jd.oa.db.greendao.MailAttachmentDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * Master of DAO (schema version 1030): knows all DAOs.
*/
public class DaoMaster extends AbstractDaoMaster {
    public static final int SCHEMA_VERSION = 1031;//改数据库版本

    /** Creates underlying database table using DAOs. */
    public static void createAllTables(SQLiteDatabase db, boolean ifNotExists) {
        HomePageDBDao.createTable(db, ifNotExists);
        MorePageDBDao.createTable(db, ifNotExists);
        WorkPlaceDBDao.createTable(db, ifNotExists);
        ChineseHolidayDBDao.createTable(db, ifNotExists);
        DidiOrderDBDao.createTable(db, ifNotExists);
        DidiAddressHistoryDBDao.createTable(db, ifNotExists);
        TimlineSearchHistoryDao.createTable(db, ifNotExists);
        ImageCacheDao.createTable(db, ifNotExists);
        TravelSearchHistoryDBDao.createTable(db, ifNotExists);
        ReimburseDeptDBDao.createTable(db, ifNotExists);
        ReimburseProjectDBDao.createTable(db, ifNotExists);
        ResponseCacheDao.createTable(db, ifNotExists);
        PushMessageDao.createTable(db, ifNotExists);
        DeletedMessageDao.createTable(db, ifNotExists);
        MailCertDao.createTable(db, ifNotExists);
        MailInfoDao.createTable(db, ifNotExists);
        MailAttachmentDao.createTable(db, ifNotExists);
    }
    
    /** Drops underlying database table using DAOs. */
    public static void dropAllTables(SQLiteDatabase db, boolean ifExists) {
        HomePageDBDao.dropTable(db, ifExists);
        MorePageDBDao.dropTable(db, ifExists);
        WorkPlaceDBDao.dropTable(db, ifExists);
        ChineseHolidayDBDao.dropTable(db, ifExists);
        DidiOrderDBDao.dropTable(db, ifExists);
        DidiAddressHistoryDBDao.dropTable(db, ifExists);
        TimlineSearchHistoryDao.dropTable(db, ifExists);
        ImageCacheDao.dropTable(db, ifExists);
        TravelSearchHistoryDBDao.dropTable(db, ifExists);
        ReimburseDeptDBDao.dropTable(db, ifExists);
        ReimburseProjectDBDao.dropTable(db, ifExists);
        ResponseCacheDao.dropTable(db, ifExists);
        PushMessageDao.dropTable(db, ifExists);
        DeletedMessageDao.dropTable(db, ifExists);
        MailCertDao.dropTable(db, ifExists);
        MailInfoDao.dropTable(db, ifExists);
        MailAttachmentDao.dropTable(db, ifExists);
    }
    
    public static abstract class OpenHelper extends SQLiteOpenHelper {

        public OpenHelper(Context context, String name, CursorFactory factory) {
            super(context, name, factory, SCHEMA_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            Log.i("greenDAO", "Creating tables for schema version " + SCHEMA_VERSION);
            createAllTables(db, false);
        }
    }
    
    /** WARNING: Drops all table on Upgrade! Use only during development. */
    public static class DevOpenHelper extends OpenHelper {
        public DevOpenHelper(Context context, String name, CursorFactory factory) {
            super(context, name, factory);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.i("greenDAO", "Upgrading schema from version " + oldVersion + " to " + newVersion + " by dropping all tables");
            dropAllTables(db, true);
            onCreate(db);
        }
    }

    public DaoMaster(SQLiteDatabase db) {
        super(db, SCHEMA_VERSION);
        registerDaoClass(HomePageDBDao.class);
        registerDaoClass(MorePageDBDao.class);
        registerDaoClass(WorkPlaceDBDao.class);
        registerDaoClass(ChineseHolidayDBDao.class);
        registerDaoClass(DidiOrderDBDao.class);
        registerDaoClass(DidiAddressHistoryDBDao.class);
        registerDaoClass(TimlineSearchHistoryDao.class);
        registerDaoClass(ImageCacheDao.class);
        registerDaoClass(TravelSearchHistoryDBDao.class);
        registerDaoClass(ReimburseDeptDBDao.class);
        registerDaoClass(ReimburseProjectDBDao.class);
        registerDaoClass(ResponseCacheDao.class);
        registerDaoClass(PushMessageDao.class);
        registerDaoClass(DeletedMessageDao.class);
        registerDaoClass(MailCertDao.class);
        registerDaoClass(MailInfoDao.class);
        registerDaoClass(MailAttachmentDao.class);
    }
    
    public DaoSession newSession() {
        return new DaoSession(db, IdentityScopeType.Session, daoConfigMap);
    }
    
    public DaoSession newSession(IdentityScopeType type) {
        return new DaoSession(db, type, daoConfigMap);
    }
    
}
