package com.jd.oa.db.greendao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table REIMBURSE_PROJECT_DB.
 */
public class ReimburseProjectDB {

    private String projectName;
    private String projectCode;
    private String companyCode;
    private Long id;

    public ReimburseProjectDB() {
    }

    public ReimburseProjectDB(Long id) {
        this.id = id;
    }

    public ReimburseProjectDB(String projectName, String projectCode, String companyCode, Long id) {
        this.projectName = projectName;
        this.projectCode = projectCode;
        this.companyCode = companyCode;
        this.id = id;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

}
