package com.jd.oa.db.model;


/**
 * 中国传统节假日
 *
 * <AUTHOR>
 */
public class ChineseHolidayBean {

    /**
     * 格式：yyyy-MM-dd
     */
    private String dateStr;

    /**
     * 是否上班
     */
    private int onWork;


    public ChineseHolidayBean() {
    }

    public ChineseHolidayBean(String dateStr) {
        this.dateStr = dateStr;
    }

    public ChineseHolidayBean(String dateStr, int onWork) {
        this.dateStr = dateStr;
        this.onWork = onWork;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public int getOnWork() {
        return onWork;
    }

    public void setOnWork(int onWork) {
        this.onWork = onWork;
    }

    @Override
    public String toString() {
        return dateStr;
    }
}
