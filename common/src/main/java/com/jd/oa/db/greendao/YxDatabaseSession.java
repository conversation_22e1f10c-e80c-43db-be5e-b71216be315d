
package com.jd.oa.db.greendao;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

import com.jd.oa.AppBase;
import com.jd.oa.db.ReleaseOpenHelper;
import com.jd.oa.storage.StorageHelper;

public class YxDatabaseSession {
    private static final String DB_NAME = "db_jdme";
    private static SQLiteDatabase database = null;
    private static DaoMaster daoMaster;
    private static DaoSession instance;

    private YxDatabaseSession() {

    }

    public static DaoSession getInstance(Context context) {
        if (instance == null) {
            DaoMaster.OpenHelper helper;
            helper = new ReleaseOpenHelper(StorageHelper.getInstance(context).getCustomerContext(), DB_NAME, null);
            database = helper.getWritableDatabase();
            daoMaster = new DaoMaster(database);
            instance = daoMaster.newSession();
        }
        return instance;
    }

    public static DidiAddressHistoryDBDao getDidiAddressHistoryDBDao() {
        return getInstance(AppBase.getAppContext()).getDidiAddressHistoryDBDao();
    }

    public static DidiOrderDBDao getDidiOrderDBDao() {
        return getInstance(AppBase.getAppContext()).getDidiOrderDBDao();
    }

    public static void release() {
        instance = null;
    }
}
