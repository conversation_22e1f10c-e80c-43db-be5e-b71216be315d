package com.jd.oa.db.greendao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table IMAGE_CACHE.
 */
public class ImageCache {

    private String name;
    private String cacheType;
    private String imageURL;
    private String startTime;
    private String endTime;
    private String filePath;

    public ImageCache() {
    }

    public ImageCache(String imageURL) {
        this.imageURL = imageURL;
    }

    public ImageCache(String name, String cacheType, String imageURL, String startTime, String endTime, String filePath) {
        this.name = name;
        this.cacheType = cacheType;
        this.imageURL = imageURL;
        this.startTime = startTime;
        this.endTime = endTime;
        this.filePath = filePath;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCacheType() {
        return cacheType;
    }

    public void setCacheType(String cacheType) {
        this.cacheType = cacheType;
    }

    public String getImageURL() {
        return imageURL;
    }

    public void setImageURL(String imageURL) {
        this.imageURL = imageURL;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

}
