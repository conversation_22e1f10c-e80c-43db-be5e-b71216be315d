package com.jd.oa.badge;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by gzf on 2022/5/26.
 *
 */
public class AppLinkParams {
    private String className;

    private String jsonStr;
    private Map<String, String> params = new HashMap<>();

    public AppLinkParams(String className, Map<String, String> params) {
        this.className = className;
        this.params.clear();
        this.params.putAll(params);
    }

    public AppLinkParams(String className, String jsonStr) {
        this.className = className;
        this.jsonStr = jsonStr;
    }

    public AppLinkParams(String className) {
        this(className, new HashMap<String, String>());
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }

    public String getClassName() {
        return className;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public String getJsonStr() {
        return jsonStr;
    }
}

