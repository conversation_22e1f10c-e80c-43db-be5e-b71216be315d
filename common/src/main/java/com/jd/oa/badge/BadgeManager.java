package com.jd.oa.badge;

import android.content.Context;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.badge.link.AppUpdate;
import com.jd.oa.badge.link.BadgeTabCollection;
import com.jd.oa.badge.link.BadgeTabMeeting;
import com.jd.oa.badge.link.UserCenter;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.BadgeTreeModel;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.StringUtils;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

public class BadgeManager {
    public static final String BADGE_SCHEME = "jdme-badge://";
    public static final String BADGE_APP_UPDATE = BADGE_SCHEME + AppUpdate.class.getSimpleName();
    public static final String BADGE_TAB_BAR_MEETING = BADGE_SCHEME + BadgeTabMeeting.class.getSimpleName();
    public static final String BADGE_TAB_BAR_COLLECTION = BADGE_SCHEME + BadgeTabCollection.class.getSimpleName();
    public static final String BADGE_USER_CENTER = BADGE_SCHEME + UserCenter.class.getSimpleName();

    //红点树形结构的节点名称
    public static final String BADGE_TAB_INSIGHT = "tab_insight";//个人中心&我的
    public static final String BADGE_INS_SETTING = "ins_setting";//个人中心-设置
    public static final String BADGE_INS_ST_ABOUT = "ins_st_about";//个人中心-设置-关于京东ME
    public static final String BADGE_INS_ST_AB_UPDATE = "ins_st_ab_update";//个人中心-设置-关于京东ME-版本更新
    public static final String BADGE_TAB_MEETING = "tab_meeting";//主页tab 会议
    public static final String BADGE_TAB_COLLECTION = "tab_collection";//主页tab 收藏

    public static Map<String, String> badgeLinks = new HashMap<>();

    public static void init(Context context) {
        AppLinkUtils.setup("com.jd.oa.badge.link", R.drawable.jdme_app_icon, "jdme-badge");
        PushNotificationReceiver.register(context, new PushNotificationReceiver() {
            @Override
            public int getSmallIcon(@NonNull Context context) {
                return R.drawable.jdme_app_icon;
            }

            @Override
            public String getAccount(@NonNull Context context) {
                return PreferenceManager.UserInfo.getUserName();
            }
        });
        badgeLinks.put(BADGE_INS_ST_AB_UPDATE, BADGE_APP_UPDATE);
        badgeLinks.put(BADGE_TAB_MEETING, BADGE_TAB_BAR_MEETING);
        badgeLinks.put(BADGE_TAB_COLLECTION, BADGE_TAB_BAR_COLLECTION);
    }

    private static BadgeTreeModel getBadge(String name) {
        BadgeTreeModel badgeTree = LocalConfigHelper.getInstance(AppBase.getAppContext()).getBadgeTreeConfig();
        if (badgeTree == null) {
            return null;
        }
        Stack<BadgeTreeModel> stack = new Stack<>();
        stack.push(badgeTree);
        while (!stack.isEmpty()) {
            BadgeTreeModel model = stack.pop();
            if (model.children.size() > 0) {
                for (BadgeTreeModel child : model.children) {
                    stack.push(child);
                }
            }
            if (model.name.equals(name)) {
                return model;
            }
        }
        return badgeTree;
    }

    public static List<String> getBadgeLinks(String name) {
        List<String> badges = new ArrayList<>();
        BadgeTreeModel treeModel = getBadge(name);
        if (treeModel == null) {
            return badges;
        }
        Stack<BadgeTreeModel> stack = new Stack<>();
        stack.push(treeModel);
        while (!stack.isEmpty()) {
            BadgeTreeModel pop = stack.pop();
            if (pop.children.size() > 0) {
                for (BadgeTreeModel child : pop.children) {
                    stack.push(child);
                }
            } else {
                if (badgeLinks.containsKey(pop.name)) {
                    badges.add(badgeLinks.get(pop.name));
                }
            }
        }
        return badges;
    }

    public static void showBadge(Context context, String badge) {
        if (StringUtils.isNotEmptyWithTrim(badge)) {
            AppLinkUtils.pushAppLink(context, badge);
        }
    }

    public static void hideBadge(Context context, String badge) {
        if (StringUtils.isNotEmptyWithTrim(badge)) {
            PushMessageService.getInstance(context).delete(badge, PreferenceManager.UserInfo.getUserName());
            AppLinkUtils.refreshAppLink(context, badge);
        }
    }

    public static void hideAllBadge(Context context) {
        PushMessageService.getInstance(context).deleteAll();
    }
}