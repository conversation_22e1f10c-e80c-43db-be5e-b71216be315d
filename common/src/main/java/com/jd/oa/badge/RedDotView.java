package com.jd.oa.badge;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;

import com.jd.oa.preference.PreferenceManager;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by gzf on 2022/5/26.
 */
public class RedDotView extends AppCompatImageView {
    private String mAccount;

    private List<String> mAppLinks = new ArrayList<>();

    private boolean mEnable = true;

    public boolean isSpecial = false;

    private BadgeVisibleCallback visibleCallback;

    private PushContentReceiver mContentReceiver = new PushContentReceiver() {

        @Override
        public String getAccount(@NonNull Context context) {
            return mAccount;
        }

        @Override
        public List<String> getAppLinks() {
            return mAppLinks;
        }

        @Override
        public boolean onReceive(@NonNull Context context, @NonNull AppLink appLink) {
            Log.d("DotView", "onReceive appLink: " + appLink);

            if (!mEnable || mAppLinks == null || mAppLinks.size() == 0) {
                return false;
            }

            boolean haveUnReadMsg = false;
            for (String item : mAppLinks) {
                if (PushMessageService.getInstance(getContext()).haveUnread(item, getAccount(context))) {
                    haveUnReadMsg = true;
                    break;
                }
            }

            setVisibility(haveUnReadMsg ? VISIBLE : GONE);
            if (null != visibleCallback) {
                visibleCallback.onVisibleChanged(haveUnReadMsg);
            }
            return false;
        }
    };

    public RedDotView(@NonNull Context context) {
        this(context, null);
    }

    public RedDotView(@NonNull Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RedDotView(@NonNull Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        // 增加属性
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.dotView);
        isSpecial = ta.getBoolean(R.styleable.dotView_dot_special, false);
        mEnable = ta.getBoolean(R.styleable.dotView_dot_default, true);

        setImageResource(R.drawable.red_dot_color_red);
        if (!isInEditMode()) {
            mAccount = PreferenceManager.UserInfo.getUserName();
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (showAsSimpleImgView) {
            return;
        }
        if (isSpecial) {
            setVisibility(mEnable ? VISIBLE : GONE);
            return;
        }
        if (mEnable) {
            PushContentReceiver.register(getContext(), mContentReceiver, false);
            checkVisible();
        } else {
            setVisibility(GONE);
            if (null != visibleCallback) {
                visibleCallback.onVisibleChanged(false);
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        try {
            if (mEnable) {
                PushContentReceiver.unregister(getContext(), mContentReceiver);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * For some case, push message is private, so need to provide account.
     *
     * @param account account maybe phone number or account id
     */
    public void setAccount(String account) {
        mAccount = account;
    }

    /**
     * Bind push appLink with red dot view, so that it can receive push message automatically.
     */
    public void setAppLinks(String... appLinks) {
        mAppLinks.clear();
        mAppLinks.addAll(Arrays.asList(appLinks));
    }

    public List<String> getAppLinks() {
        return mAppLinks;
    }

    //普通ImageView，显隐外界控制
    boolean showAsSimpleImgView;

    public void setEnable(boolean enable) {
        mEnable = enable;
        showAsSimpleImgView = !enable;
        if (!enable) {
            setVisibility(GONE);
        }
    }

    /**
     * Remove red dot
     */
    public void remove() {
        if (!mEnable || mAppLinks == null || mAppLinks.size() == 0) {
            return;
        }

        // remove message when clicked
        for (String appLink : mAppLinks) {
            PushMessageService.getInstance(getContext()).delete(appLink, mAccount);
            AppLinkUtils.refreshAppLink(getContext(), appLink);
        }
    }


    public void checkVisible() {
        // 添加特例
        if ((mAppLinks == null || mAppLinks.size() == 0) && isSpecial) {
            setVisibility(mEnable ? View.VISIBLE : GONE);
            return;
        }

        if (mAppLinks == null || mAppLinks.size() == 0) {
            return;
        }

        if (mEnable) {
            boolean exist = PushMessageService.getInstance(getContext()).haveUnread(
                    mAppLinks, mAccount);
            setVisibility(exist ? View.VISIBLE : GONE);
            if (null != visibleCallback) {
                visibleCallback.onVisibleChanged(exist);
            }
        } else {
            setVisibility(GONE);
            if (null != visibleCallback) {
                visibleCallback.onVisibleChanged(false);
            }
        }
    }

    public void setVisibleCallback(BadgeVisibleCallback callback) {
        this.visibleCallback = callback;
    }
}