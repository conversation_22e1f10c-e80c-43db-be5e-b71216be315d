package com.jd.oa.badge

import android.content.Context
import androidx.annotation.Keep
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.gson.JsonObject
import com.jd.oa.listener.TimlineMessageListener
import com.jd.oa.network.post
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.withContext
import java.util.Collections

/**
 *
 * @authro zhengyunguang
 * @date 24/08/20
 *
 */
object AppBadgeCenter : TimlineMessageListener {

    private val listeners = Collections.synchronizedMap<String, IAppBadgeListener>(mutableMapOf())

    //上次更新时间，防止接口返回数据为脏数据
    private val lastTimestamps = mutableMapOf<String, Long>()

    private val tempData = mutableMapOf<String, BadgeData>()

    /**
     * id = 202104251433 link = jdme://jm/biz/joyspace
     * id = 202104251439 link = jdme://jm/biz/meeting
     * id = 202104251441 link = jdme://jm/biz/collection
     * id = 202104251440 link = jdme://jm/biz/joyNote
     * id = 202104251437 link = jdme://jm/sys/browser
     * id = 202104251442 link = jdme://jm/biz/appcenter/202109081097
     * id = 202104251434 link = jdme://jm/biz/workbench
     * id = 202106211630 link = jdme://jm/biz/joywork
     * id = 202104251435 link = jdme://jm/biz/calendar
     */


    override fun onMessageReceiver(type: String?, message: String?) {
        message?.runCatching {

            val jsonObj = JsonUtils.getGson().fromJson(this, JsonObject::class.java)
            val app = JsonUtils.getGson().fromJson(jsonObj.get("data"), BadgeData::class.java)
            when (app.type) {
                //直接覆盖更新
                1 -> {
                    onReceived(app)
                }
                //调http接口重新同步
                2 -> {
                    query(null, app.appCode)
                }

                else -> {
                    query(null, app.appCode)
                }
            }
        }
    }

    @JvmStatic
    fun onReceived(vararg apps: BadgeData) {
        apps.forEach { app ->
            val target = listeners.firstNotNullOfOrNull {
                if (it.key == app.appCode) it.value else null
            }
            if (target == null) {
                val data = tempData[app.appCode]
                if (data == null || data.timestamp < app.timestamp) {
                    tempData[app.appCode] = app
                }
            } else {
                val last = lastTimestamps[app.appCode] ?: 0
                if (last < app.timestamp) {
                    lastTimestamps[app.appCode] = app.timestamp
                    target.onBadgeDataReceived(app)
                }
            }

        }
    }


    @JvmStatic
    @OptIn(DelicateCoroutinesApi::class)
    fun query(context: Context? = null, vararg appCodes: String) {
        val coroutineScope =
            if (context is AppCompatActivity) context.lifecycleScope else GlobalScope
        coroutineScope.safeLaunch(Dispatchers.Default) {
            val response = post<String>(action = "jdme.appstore.app.getAppBadge") {
                mutableMapOf(
                    Pair("appCodes", appCodes),
                )
            }
            val apps = withContext(Dispatchers.Default) {
                if (response.isSuccessful) {
                    val jsonObj =
                        JsonUtils.getGson().fromJson(response.data, JsonObject::class.java)
                    jsonObj.getAsJsonArray("apps")
                        ?.map { JsonUtils.getGson().fromJson(it, BadgeData::class.java) }
                        ?: emptyList()
                } else {
                    emptyList()
                }
            }
            withContext(Dispatchers.Main) {
                onReceived(*apps.toTypedArray())
            }
        }

    }

    @JvmStatic
    fun registerListener(appCode: String, appBadgeListener: IAppBadgeListener) {
        listeners[appCode] = appBadgeListener
        lastTimestamps.remove(appCode)
        val data = tempData.remove(appCode)
        data?.run {
            appBadgeListener.onBadgeDataReceived(data, true)
        }
    }

    @JvmStatic
    fun unregisterListener(appCode: String): IAppBadgeListener? {
        lastTimestamps.remove(appCode)
        return listeners.remove(appCode)
    }

    @JvmStatic
    fun unregisterListenerByType(type: Class<*>) {
        val subMap = listeners.filterNot {
            it.value::class.java == type
        }
        listeners.clear()
        listeners.putAll(subMap)
        lastTimestamps.clear()
    }

}

@Keep
data class BadgeData(val appCode: String) {
    var reddot: Boolean = false
    var badge: Int = 0
    var type: Int? = null
    var timestamp: Long = -1
}

interface IAppBadgeListener {
    /**
     * isSilent 只更新数据
     */
    fun onBadgeDataReceived(app: BadgeData, isSilent: Boolean = false)
}