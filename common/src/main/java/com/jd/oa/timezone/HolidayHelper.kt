package com.jd.oa.timezone

import android.annotation.SuppressLint
import androidx.appcompat.app.AppCompatDialog
import androidx.lifecycle.lifecycleScope
import com.jd.me.datetime.picker.DatePickerDialog
import com.jd.me.datetime.picker.DatetimePickerDialog
import com.jd.me.datetime.picker.PickerConfig
import com.jd.me.datetime.picker.PickerInterface
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.model.Holiday
import com.jd.oa.model.Holidays
import com.jd.oa.network.post
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.TimeZone

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/17 16:05
 */

fun DatePickerDialog.holidayFetcher() {
    if (!LocaleUtils.isBJTimeZone()) return
    setHolidayFetcher { onHolidayLoadListener ->
        holidayFetch(this@holidayFetcher, onHolidayLoadListener)
    }
}

fun DatetimePickerDialog.holidayFetcher() {
    if (!LocaleUtils.isBJTimeZone()) return
    setHolidayFetcher { onHolidayLoadListener ->
        holidayFetch(this@holidayFetcher, onHolidayLoadListener)
    }
}

private fun holidayFetch(
    dialog: AppCompatDialog,
    onLoadListener: PickerInterface.HolidayLoadListener
) {
    dialog.lifecycleScope.safeLaunch {
        val holidaysResult = HolidayHelper.queryHolidays()
        if (dialog.isShowing && holidaysResult.isNotEmpty()) {
            val holidayMap = mutableMapOf<String, com.haibin.calendarview.Calendar>()
            holidaysResult.forEach { map ->
                val holidays = map.value
                holidayMap += holidays.associate { holiday ->
                    val format = SimpleDateFormat("yyyy-MM-dd")
                    val schemeConfig = if (holiday.isOffDay == true) HolidayScheme() else WeekDayScheme()
                    val date = format.parse(holiday.date!!)
                    val cal = Calendar.getInstance()
                    cal.timeInMillis = date?.time ?: 0
                    val calendar = com.haibin.calendarview.Calendar()
                    calendar.year = cal.get(Calendar.YEAR)
                    calendar.month = cal.get(Calendar.MONTH) + 1
                    calendar.day = cal.get(Calendar.DATE)
                    val scheme = com.haibin.calendarview.Calendar.Scheme()
                    scheme.type =
                        if (schemeConfig is HolidayScheme)
                            PickerConfig.SCHEME_TYPE_HOLIDAY
                        else
                            PickerConfig.SCHEME_TYPE_WORKDAY
                    scheme.scheme = schemeConfig.getSchemeText(dialog.context)
                    scheme.shcemeColor = schemeConfig.getSchemeColor()
                    calendar.addScheme(scheme)
                    calendar.toString() to calendar
                }
            }
            onLoadListener.onDataLoad(holidayMap)
        }
    }
}

object HolidayHelper {

    private val holidays = mutableMapOf<String, List<Holiday>>()

    @JvmStatic
    suspend fun queryHolidays(
        years: List<String> = run {
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = Date().time
            val year = calendar.get(Calendar.YEAR)
            mutableListOf(
                (year - 1).toString(),
                year.toString(),
                (year + 1).toString()
            )
        },
        timeZoneId: String = TimeZone.getDefault().id
    ): Map<String, List<Holiday>> = coroutineScope {
        if (!LocaleUtils.isBJTimeZone(timeZoneId)) {
            return@coroutineScope holidays
        }
        val containsAll = years.all {
            holidays.contains(it)
        }
        if (containsAll) {
            safeLaunch {
                val result = queryHolidaysServer(years, timeZoneId)
                holidays.putAll(result)
            }
            holidays
        } else {
            val result = async { queryHolidaysServer(years, timeZoneId) }
            holidays.putAll(result.await())
            holidays
        }
    }

    @SuppressLint("SimpleDateFormat")
    suspend fun queryHolidaysServer(
        years: List<String>,
        timeZoneId: String,
    ): Map<String, List<Holiday>> {
        val result = post<Holidays>("joyday.calendar.getHolidays") {
            mutableMapOf(
                Pair("years", years),
                Pair("timeZone", timeZoneId),
            )
        }
        if (!result.isSuccessful || result.data == null) return emptyMap()
        val all = result.data.day
        if (all.isEmpty()) return emptyMap()
        val holidays = mutableMapOf<String, MutableList<Holiday>>()
        val format = SimpleDateFormat("yyyy-MM-dd")
        all.forEach {
            try {
                val date = format.parse(it.date!!)
                val cal = Calendar.getInstance()
                cal.timeInMillis = date?.time ?: 0
                val year = cal.get(Calendar.YEAR)
                val listOfYear = holidays[year.toString()] ?: mutableListOf()
                listOfYear.add(it)
                holidays[year.toString()] = listOfYear
            } catch (e: Exception) {
                MELogUtil.localE("queryHolidaysServer", "format")
            }
        }
        return holidays
    }
}


