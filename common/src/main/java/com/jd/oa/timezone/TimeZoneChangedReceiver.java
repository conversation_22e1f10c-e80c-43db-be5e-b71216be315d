package com.jd.oa.timezone;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.jd.oa.abilities.utils.MELogUtil;

public class TimeZoneChangedReceiver extends BroadcastReceiver {
    private static final String TAG = "TimeZoneChangedReceiver";
    private static final String EXTRA_TIMEZONE = "time-zone";
    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            String timeZone = intent.getStringExtra(EXTRA_TIMEZONE);
            MELogUtil.localI(MELogUtil.TAG_TIME, "TimeZoneChangedReceiver, timeZone: " + timeZone);
            TimeZoneChangeNotifier.getInstance().onTimeZoneChanged(timeZone);
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_TIME, "TimeZoneChangedReceiver, error: " + e.getMessage(), e);
        }
    }
}