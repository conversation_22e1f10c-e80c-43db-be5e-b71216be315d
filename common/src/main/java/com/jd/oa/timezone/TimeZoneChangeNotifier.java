package com.jd.oa.timezone;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;


public class TimeZoneChangeNotifier {

    private static volatile TimeZoneChangeNotifier sInstance;

    MutableLiveData<String> mTimeZoneLiveData;

    public static TimeZoneChangeNotifier getInstance() {
        if (sInstance == null) {
            synchronized (TimeZoneChangeNotifier.class) {
                if (sInstance == null) {
                    sInstance = new TimeZoneChangeNotifier();
                }
            }
        }
        return sInstance;
    }

    public TimeZoneChangeNotifier() {
        mTimeZoneLiveData = new MutableLiveData<>();
    }

    public LiveData<String> getTimeZoneLiveData() {
        return mTimeZoneLiveData;
    }

    protected void onTimeZoneChanged(String timeZone) {
        mTimeZoneLiveData.postValue(timeZone);
    }
}
