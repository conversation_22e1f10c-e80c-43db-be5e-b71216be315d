package com.jd.oa.download;

import android.app.DownloadManager;
import android.content.Context;
import android.net.Uri;
import android.os.Environment;
import android.util.Log;

import java.io.IOException;

/**
 * Created by peidongbiao on 2019/4/29
 */
public class SystemDownloader extends Downloader {
    private static final String TAG = "SystemDownloader";

    private Context mContext;
    DownloadManager mDownloadManager;

    public SystemDownloader(Context context) {
        mContext = context;
        mDownloadManager = (DownloadManager) mContext.getSystemService(Context.DOWNLOAD_SERVICE);
    }

    @Override
    DownloadResult download(DownloadRequest req) throws IOException {
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(req.getUrl()));
        request.setDestinationInExternalFilesDir(mContext, Environment.DIRECTORY_DOWNLOADS, "jdme.apk");
        request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI);
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE);
        request.setTitle("京东ME");
        request.setDescription("正在下载");
        long reuqestId = mDownloadManager.enqueue(request);
        Log.d(TAG, "download: " + reuqestId);
        return null;
    }

    @Override
    void stop() {

    }
}
