package com.jd.oa.download;

import androidx.annotation.Keep;

/**
 * Created by peidongbiao on 2019/4/28
 */
@Keep
public class DownloadRequest {

    private String url;
    private String target;
    private String messageDigest;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getMessageDigest() {
        return messageDigest;
    }

    public void setMessageDigest(String messageDigest) {
        this.messageDigest = messageDigest;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DownloadRequest request = (DownloadRequest) o;

        if (getUrl() != null ? !getUrl().equals(request.getUrl()) : request.getUrl() != null)
            return false;
        return getTarget() != null ? getTarget().equals(request.getTarget()) : request.getTarget() == null;
    }

    @Override
    public int hashCode() {
        int result = getUrl() != null ? getUrl().hashCode() : 0;
        result = 31 * result + (getTarget() != null ? getTarget().hashCode() : 0);
        return result;
    }
}
