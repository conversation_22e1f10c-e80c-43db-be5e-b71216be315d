package com.jd.oa.download;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.TextUtils;
import android.text.format.Formatter;
import android.util.Log;
import android.widget.RemoteViews;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.AppUpdateCache;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.NamedThreadFactory;
import com.jd.oa.utils.NotificationUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import java.io.File;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by peidongbiao on 2019/4/29
 */
public class DownloadService extends Service {
    private static final String TAG = "DownloadService";
    public static final String ARG_URL = "arg.url";
    public static final String ARG_TARGET = "arg.target";
    public static final String ARG_FILE_NAME = "arg.file.name";
    public static final String ARG_SHOW_NOTIFICATION = "arg.show.notification";
    public static final String ARG_AUTO_INSTALL = "arg.auto.install";
    public static final String ARG_FILE_MD5 = "arg.file.md5";

    public static final String APK_DOWNLOAD = "apk_download";
    public static final String ACTION_START = "start";
    public static final String ACTION_PROGRESS = "progress";
    public static final String ACTION_PAUSED = "paused";
    public static final String ACTION_SUCCESS = "success";
    public static final String ACTION_FAILURE = "failure";

    private static final String ACTION = "action.notification.click";
    private static final String CHANNEL_ID = "Download";
    private static final String CHANNEL_NAME = "Download";
    private static final String CHANNEL_DESCRIPTION = "Download Notification";

    private static final int STATUS_NONE = 0;
    private static final int STATUS_DOWNLOADING = 1;
    private static final int STATUS_PAUSED = 2;
    private static final int STATUS_FINISHED = 3;
    private static final int STATUS_ERROR = 4;
    private static final int STATUS_CHECKING = 5;

    public static final int NOTIFICATION_ID = 1;

    private String mUrl;
    private String mTarget;
    private String mFileName;
    private boolean mShowNotification;
    private boolean mAutoInstall;
    private String mFileMd5;
    private NotificationManager mNotificationManager;
    private Notification mNotification;
    private RemoteViews mRemoteViews;
    private DownloadRequest mRequest;
    private DownloadTask mDownloadTask;
    private int mStatus = STATUS_NONE;
    private File mDownloadFile;
    private ExecutorService mDownloadExecutor;
    private boolean mScreenLocked = false;

    private final BroadcastReceiver mClickReceiver = new BroadcastReceiver() {
        private static final int INTERVAL_TIME = 500;
        private long mLastReceiveTime = 0;
        @Override
        public void onReceive(Context context, Intent intent) {
            long current = System.currentTimeMillis();
            if (current - mLastReceiveTime < INTERVAL_TIME) return;

            MELogUtil.localI(MELogUtil.TAG_AUP, "mClickReceiver.onReceive, mStatus: " + mStatus);
            mLastReceiveTime = current;
            switch (mStatus) {
                case STATUS_DOWNLOADING: {
                    //暂停下载
                    if (mDownloadTask != null) {
                        mDownloadTask.stop();
                    }
                    break;
                }
                case STATUS_PAUSED: {
                    //继续下载
                    if (mRequest != null) {
                        download(mRequest);
                    }
                    break;
                }
                case STATUS_FINISHED: {
                    //安装
                    if (mDownloadFile != null) {
                        ImDdService imDdService = AppJoint.service(ImDdService.class);
                        if(null != imDdService && mDownloadFile.exists()) {
                            imDdService.onNotifyIMInstallApk();
                        }
                        CommonUtils.installApk(DownloadService.this, mDownloadFile);
                        mNotificationManager.cancel(NOTIFICATION_ID);
                        stopSelf();
                    }
                    break;
                }
                case STATUS_ERROR: {
                    //删除原文件，重新下载
                    if (mRequest != null) {
                        FileUtils.deleteFile(mRequest.getTarget());
                        download(mRequest);
                    }
                    break;
                }
            }
        }
    };

    private final BroadcastReceiver mNetworkChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (!CommonUtils.isWiFi(context) && mStatus == STATUS_DOWNLOADING && mDownloadTask != null) {
                //mDownloadTask.stop();
            }
        }
    };

    private final BroadcastReceiver mScreenLockReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                String action = intent.getAction();
                if (Intent.ACTION_SCREEN_ON.equals(action)) {
                    mScreenLocked = false;
                } else if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                    mScreenLocked = true;
                } else if (Intent.ACTION_USER_PRESENT.equals(action)) {
                    Log.d(TAG, "onReceive: ACTION_USER_PRESENT");
                }
            }
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        mNotificationManager  = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        registerReceiver(mClickReceiver, new IntentFilter(ACTION));
        registerReceiver(mNetworkChangeReceiver, new IntentFilter("android.net.conn.CONNECTIVITY_CHANGE"));
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_SCREEN_OFF);
        filter.addAction(Intent.ACTION_SCREEN_ON);
        filter.addAction(Intent.ACTION_USER_PRESENT);
        registerReceiver(mScreenLockReceiver, filter);
        mDownloadExecutor = Executors.newSingleThreadExecutor(new NamedThreadFactory(this.getClass().getName()));
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int result = super.onStartCommand(intent, flags, startId);
        if (intent == null) return result;
        //stop previous
        if (mDownloadTask != null) {
            mDownloadTask.stop();
            mDownloadFile = null;
            mDownloadTask = null;
            mStatus = STATUS_NONE;
            mNotificationManager.cancel(NOTIFICATION_ID);
            //return result;
        }
        mUrl = intent.getStringExtra(ARG_URL);
        mTarget = intent.getStringExtra(ARG_TARGET);
        mFileName = intent.getStringExtra(ARG_FILE_NAME);
        mShowNotification = intent.getBooleanExtra(ARG_SHOW_NOTIFICATION, false);
        mAutoInstall = intent.getBooleanExtra(ARG_AUTO_INSTALL, false);
        mFileMd5 = intent.getStringExtra(ARG_FILE_MD5);
        mRequest = new DownloadRequest();
        mRequest.setUrl(mUrl);
        mRequest.setTarget(mTarget);
        mRequest.setMessageDigest(mFileMd5);
        if (TextUtils.isEmpty(mFileName)) {
            mFileName = getFileName(mTarget);
        }
        if (TextUtils.isEmpty(mUrl) || TextUtils.isEmpty(mTarget) || TextUtils.isEmpty(mFileName)) {
            MELogUtil.localI(MELogUtil.TAG_AUP, "onStartCommand, illegal params: url: " + mUrl + ", target: " + mTarget + ", fileName: " + mFileName);
            return result;
        }
        download(mRequest);
        return result;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mDownloadExecutor.shutdown();
        unregisterReceiver(mClickReceiver);
        unregisterReceiver(mNetworkChangeReceiver);
        unregisterReceiver(mScreenLockReceiver);
    }

    private final LocalBroadcastManager broadcastManager = LocalBroadcastManager.getInstance(this);

    private DownloadTask download(DownloadRequest request) {
        if (mDownloadTask != null && !mDownloadTask.isCancelled()) {
            mDownloadTask.stop();
            mDownloadTask.cancel(false);
        }
        mDownloadTask = new DownloadTask(new OkHttpDownloader(), new SimpleDownloadListener() {
            @Override
            public void onStart() {
                if (mShowNotification) {
                    showNotification();
                    sendLocalBroadcast(ACTION_START, "");
                }
                mStatus = STATUS_DOWNLOADING;
                MELogUtil.localI(MELogUtil.TAG_AUP, "DownloadService.download, onStart");
            }

            @Override
            public void onProgressUpdate(long total, long current, long progress) {
                Log.d(TAG, "onProgressUpdate, total: " + total + ", current: " + current + ", progress: " + progress);
                if (mShowNotification) {
                    if (progress >= 0) {
                        if (!mScreenLocked) {
                            try {
                                setNotificationProgress(total, current, progress);
                            } catch (Throwable e) {
                                e.printStackTrace();
                            }
                        }
                        mStatus = STATUS_DOWNLOADING;
                    } else if (progress == DownloadTask.PROGRESS_CHECKING) {
                        setNotificationChecking();
                        mStatus = STATUS_CHECKING;
                    }
                    sendLocalBroadcast(ACTION_PROGRESS, Long.toString(progress));
                    AppUpdateCache.INSTANCE.setUpdateStatus(ACTION_PROGRESS);
                    AppUpdateCache.INSTANCE.setUpdateProgress(progress == -1L ? "" : Long.toString(progress));
                }
            }

            @Override
            public void onPaused(File file) {
                if (mShowNotification) {
                    setNotificationPaused();
                    sendLocalBroadcast(ACTION_PAUSED, "");
                    AppUpdateCache.INSTANCE.setUpdateStatus(ACTION_PAUSED);
                }
                mStatus = STATUS_PAUSED;
                MELogUtil.localI(MELogUtil.TAG_AUP, "DownloadService.download, onPaused");
            }

            @Override
            public void onSuccess(File file) {
                MELogUtil.localI(MELogUtil.TAG_AUP, "DownloadService.download, onSuccess, mShowNotification: "
                        + mShowNotification
                        + ", mAutoInstall"
                        + mAutoInstall
                );
                if (mShowNotification) {
                    setNotificationComplete(file.length());
                    AppUpdateCache.INSTANCE.setUpdateStatus(ACTION_SUCCESS);
                    AppUpdateCache.INSTANCE.setUpdateProgress("0");
                    sendLocalBroadcast(ACTION_SUCCESS, "");
                }
                mStatus = STATUS_FINISHED;
                mDownloadFile = file;
                if (mAutoInstall) {
                    //下载完成后安装
                    Context context = AppBase.getTopActivity() == null ? AppBase.getAppContext() : AppBase.getTopActivity();
                    ImDdService imDdService = AppJoint.service(ImDdService.class);
                    if(null != imDdService && file.exists()) {
                        imDdService.onNotifyIMInstallApk();
                    }
                    CommonUtils.installApk(context, file);
                    mNotificationManager.cancel(NOTIFICATION_ID);
                    stopSelf();
                }
            }

            @Override
            public void onFailure(int code, Exception e) {
                MELogUtil.localI(MELogUtil.TAG_AUP, "DownloadService.download, onFailure, code: " + code, e);
                if (mShowNotification) {
                    if (DownloadResult.ERROR_FILE_DIGEST_INCORRECT == code) {
                        setNotificationError(getString(R.string.me_update_check_digest_failed_retry));
                        ToastUtils.showToast(R.string.me_update_check_digest_failed);
                    } else {
                        setNotificationError(getString(R.string.me_update_exception));
                    }
                    sendLocalBroadcast(ACTION_FAILURE, DownloadResult.ERROR_FILE_DIGEST_INCORRECT == code ? String.valueOf(DownloadResult.ERROR_FILE_DIGEST_INCORRECT): "");
                    AppUpdateCache.INSTANCE.setUpdateStatus(ACTION_FAILURE);
                }
                mStatus = STATUS_ERROR;
            }
        });
        //mDownloadTask.execute(request);
        mDownloadTask.executeOnExecutor(mDownloadExecutor, request);
        return mDownloadTask;
    }

    private void showNotification() {
        mRemoteViews = new RemoteViews(getPackageName(), R.layout.jdme_layout_download_notification);
        mRemoteViews.setTextViewText(R.id.tv_name, mFileName);
        mRemoteViews.setOnClickPendingIntent(R.id.layout_notification, getClickPendingIntent());

        NotificationCompat.Builder builder;
        if (Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.O && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            builder = new NotificationCompat.Builder(this);
            builder.setContent(mRemoteViews);
            builder.setPriority(NotificationCompat.PRIORITY_DEFAULT);
            builder.setSmallIcon(R.drawable.jdme_app_icon_small);
            builder.setLargeIcon(BitmapFactory.decodeResource(AppBase.getAppContext().getResources(), R.drawable.jdme_app_icon));
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                builder.setVisibility(NotificationCompat.VISIBILITY_SECRET);
            }
            mNotification  = builder.build();
        } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            int importance = NotificationManager.IMPORTANCE_LOW;
            NotificationChannel mChannel = new NotificationChannel(NotificationUtils.CHANNEL_ID_PROGRESS, NotificationUtils.CHANNEL_NAME_PROGRESS, importance);
            mChannel.setDescription(NotificationUtils.CHANNEL_DESCRIPTION_PROGRESS);
            mChannel.enableLights(true);
            mChannel.setLightColor(Color.RED);
            mChannel.setShowBadge(false);
            mChannel.setVibrationPattern(new long[]{0, 0, 0, 0, 0, 0, 0, 0, 0});
            mNotificationManager.createNotificationChannel(mChannel);

            builder = new NotificationCompat.Builder(this, "me_app_update");
            builder.setContent(mRemoteViews);
            builder.setPriority(NotificationCompat.PRIORITY_DEFAULT);
            builder.setSmallIcon(R.drawable.jdme_app_icon_small);
            builder.setLargeIcon(BitmapFactory.decodeResource(AppBase.getAppContext().getResources(), R.drawable.jdme_app_icon));
            builder.setVisibility(NotificationCompat.VISIBILITY_SECRET);
            mNotification  = builder.build();
        }
        mNotificationManager.notify(NOTIFICATION_ID, mNotification);
    }

    private void setNotificationComplete(long fileSize) {
        mRemoteViews.setTextViewText(R.id.tv_name, mFileName);
        String size = Formatter.formatFileSize(this, fileSize);
        mRemoteViews.setTextViewText(R.id.tv_size, String.format(Locale.getDefault(), "%s/%s", size, size));
        mRemoteViews.setProgressBar(R.id.pb_progress, 100, 100, false);
        mRemoteViews.setTextViewText(R.id.tv_action, getString(R.string.me_update_completed));
        mRemoteViews.setTextViewText(R.id.tv_percent, String.format(Locale.getDefault(), "%d%%", 100));
        mNotificationManager.notify(NOTIFICATION_ID, mNotification);
    }

    private void setNotificationPaused() {
        mRemoteViews.setTextViewText(R.id.tv_name, mFileName);
        mRemoteViews.setTextViewText(R.id.tv_action, getString(R.string.me_update_download_paused));
        mNotificationManager.notify(NOTIFICATION_ID, mNotification);
    }

    private void setNotificationError(String msg) {
        mRemoteViews.setTextViewText(R.id.tv_name, mFileName);
        mRemoteViews.setTextViewText(R.id.tv_size, null);
        mRemoteViews.setTextViewText(R.id.tv_action, msg);
        mNotificationManager.notify(NOTIFICATION_ID, mNotification);
    }

    private void setNotificationChecking() {
        mRemoteViews.setTextViewText(R.id.tv_action, getString(R.string.me_update_checking));
        mNotificationManager.notify(NOTIFICATION_ID, mNotification);
    }

    private void setNotificationProgress(long total, long current, long progress) {
        mRemoteViews.setTextViewText(R.id.tv_name, mFileName);
        mRemoteViews.setTextViewText(R.id.tv_size, String.format(Locale.getDefault(), "%s/%s", Formatter.formatFileSize(this, current), Formatter.formatFileSize(this, total)));
        mRemoteViews.setProgressBar(R.id.pb_progress, 100, (int) progress, false);
        mRemoteViews.setTextViewText(R.id.tv_action, getString(R.string.me_update_downloading));
        mRemoteViews.setTextViewText(R.id.tv_percent, String.format(Locale.getDefault(), "%d%%", progress));
        mNotificationManager.notify(NOTIFICATION_ID, mNotification);
    }

    private PendingIntent getClickPendingIntent() {
        Intent intent = new Intent(ACTION);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(this, 1, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        return pendingIntent;
    }

    private String getFileName(String filePath) {
        int index = filePath.lastIndexOf("/");
        if (index == -1) return null;
        return filePath.substring(index + 1);
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_DEFAULT);
            channel.setDescription(CHANNEL_DESCRIPTION);
            mNotificationManager.createNotificationChannel(channel);
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void sendLocalBroadcast(String action, String progress) {
        Intent intent = new Intent(APK_DOWNLOAD);
        intent.putExtra("action", action);
        if (ACTION_PROGRESS.equals(action) || ACTION_FAILURE.equals(action)) {
            intent.putExtra(action, progress);
        }
        if (broadcastManager != null) {
            broadcastManager.sendBroadcastSync(intent);
        }
    }
}