package com.jd.oa.download;

import java.io.File;

/**
 * Created by peidongbiao on 2019/4/28
 */
public abstract class SimpleDownloadListener implements DownloadListener {

    @Override
    public void onStart() {

    }

    @Override
    public void onProgressUpdate(long total, long current, long progress) {

    }

    @Override
    public void onPaused(File file) {

    }

    @Override
    public void onSuccess(File file) {

    }

    @Override
    public void onFailure(int code, Exception e) {

    }
}
