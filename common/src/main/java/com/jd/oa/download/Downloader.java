package com.jd.oa.download;

import java.io.IOException;

/**
 * Created by peidongbiao on 2019/4/28
 */
public abstract class Downloader {

    public static final String DOWNLOADING_SUFFIX = "-dld";
    protected ProgressUpdateListener mProgressUpdateListener;

    abstract DownloadResult download(DownloadRequest request) throws IOException;

    abstract void stop();

    public void setProgressUpdateListener(ProgressUpdateListener progressUpdateListener) {
        mProgressUpdateListener = progressUpdateListener;
    }
}