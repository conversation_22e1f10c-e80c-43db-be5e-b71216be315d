package com.jd.oa.download;

import androidx.annotation.Keep;

import java.io.File;

/**
 * Created by <PERSON>ei<PERSON>biao on 2019/4/29
 */
@Keep
public class DownloadResult {
    public static final int ERROR_FILE_DIGEST_INCORRECT = 1;

    private boolean successful;
    private boolean completed;
    private DownloadRequest request;
    private File file;
    private Exception error;
    private int errorCode;

    public boolean isSuccessful() {
        return successful;
    }

    public void setSuccessful(boolean successful) {
        this.successful = successful;
    }

    public boolean isCompleted() {
        return completed;
    }

    public void setCompleted(boolean completed) {
        this.completed = completed;
    }

    public DownloadRequest getRequest() {
        return request;
    }

    public void setRequest(DownloadRequest request) {
        this.request = request;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public Exception getError() {
        return error;
    }

    public void setError(Exception error) {
        this.error = error;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }
}
