package com.jd.oa.download;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;

import com.jd.oa.cache.FileCache;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.encrypt.MD5Utils;

import java.io.File;

/**
 * Created by peidongbiao on 2019/4/28
 */
public class DownloadManager implements IUpdateManager {
    private static final String TAG = "DownloadManager";
    private static final String DIR_NAME = "download";
    private static DownloadManager sInstance;
    private Context mContext;
    private String mDefaultDirectory;

    public static DownloadManager get(Context context) {
        if (sInstance == null) {
            synchronized (DownloadManager.class) {
                if (sInstance == null) {
                    sInstance = new DownloadManager(context);
                }
            }
        }
        return sInstance;
    }

    private DownloadManager(Context context) {
        mContext = context.getApplicationContext();
        mDefaultDirectory = FileCache.getInstance().getAppFile().getAbsolutePath() + File.separator + DIR_NAME;
    }

    public void download(String url) {
        String target = mDefaultDirectory + File.separator + getFileName(url);
        download(url, target);
    }

    public void download(String url, String target) {
        download(url, target, null);
    }

    public void download(String url, String targetDir, String fileName) {
        download(url, targetDir, fileName, true, false, null);
    }

    public void download(String url, String target, String fileName, boolean showNotification, boolean autoInstall, String md5) {
        clearOldInstallationFiles();
        Intent intent = new Intent(mContext, DownloadService.class);
        intent.putExtra(DownloadService.ARG_URL, url);
        intent.putExtra(DownloadService.ARG_TARGET, target);
        intent.putExtra(DownloadService.ARG_FILE_NAME, fileName);
        intent.putExtra(DownloadService.ARG_SHOW_NOTIFICATION, showNotification);
        intent.putExtra(DownloadService.ARG_AUTO_INSTALL, autoInstall);
        intent.putExtra(DownloadService.ARG_FILE_MD5, md5);
        mContext.startService(intent);
    }

    public String getDefaultDownloadDirectory() {
        return mDefaultDirectory;
    }

    private String getFileName(String url) {
        String lastSegment = Uri.parse(url).getLastPathSegment();
        if (!TextUtils.isEmpty(lastSegment)) {
            return lastSegment;
        }
        return MD5Utils.getMD5(url);
    }

    private void clearOldInstallationFiles() {
        File file = new File(mDefaultDirectory);
        if (!file.exists()) return;
        File[] files = file.listFiles();
        if (files == null || files.length == 0) return;
        PackageManager pm = mContext.getPackageManager();
        for (int i = 0; i < files.length; i++) {
            PackageInfo packageInfo = pm.getPackageArchiveInfo(files[i].getPath(), PackageManager.GET_ACTIVITIES);
            if (packageInfo == null) continue;
            String filePackageName = packageInfo.applicationInfo.packageName;
            String fileVersionName = packageInfo.versionName;
            if (!mContext.getPackageName().equals(filePackageName)) continue;
            if (CommonUtils.compareVersion(fileVersionName, CommonUtils.getVersionName(mContext)) == -1) {
                files[i].delete();
            }
        }
    }
}