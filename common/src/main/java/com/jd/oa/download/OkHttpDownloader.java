package com.jd.oa.download;

import android.text.TextUtils;
import android.util.Log;


import com.jd.oa.network.httpmanager.interceptors.CatchExceptionInterceptor;

import java.io.BufferedInputStream;
import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Created by peidongbiao on 2019/4/28
 */
public class OkHttpDownloader extends Downloader {
    private static final String TAG = "OkHttpDownloader";
    private static final int TIMEOUT = 20;
    private static OkHttpClient sOkHttpClient;

    private AtomicBoolean mStopped = new AtomicBoolean(false);

    @Override
    public DownloadResult download(DownloadRequest request) throws IOException {
        DownloadResult result = new DownloadResult();
        result.setRequest(request);
        File file = new File(request.getTarget());
        if (file.isDirectory()) {
            result.setSuccessful(false);
            result.setError(new IllegalArgumentException("target cant be directory"));
            return result;
        }
        File parent = file.getParentFile();
        File downloadingFile = new File(file.getAbsolutePath() + DOWNLOADING_SUFFIX);
        long contentLength = getContentLength(request.getUrl());
        if (!parent.exists()) {
            parent.mkdirs();
        }
        if (file.exists() && file.length() == contentLength) {
            result.setFile(file);
            result.setSuccessful(true);
            result.setCompleted(true);
            return result;
        } else if (!downloadingFile.exists()) {
            downloadingFile.createNewFile();
        }
        file.delete();
        long downloadedLength = downloadingFile.length();
        OkHttpClient client = getOkHttpClient();
        if (downloadedLength == contentLength) {
            downloadingFile.renameTo(file);
            result.setFile(file);
            result.setSuccessful(true);
            result.setCompleted(true);
            return result;
        } else if (downloadedLength > contentLength) {
            downloadedLength = 0;
        }
        Request req = new Request.Builder()
                .url(request.getUrl())
                .addHeader("RANGE", "bytes=" + downloadedLength + "-")
                .build();
        Response response = null;
        BufferedInputStream in = null;
        RandomAccessFile raf = null;
        try {
            response = client.newCall(req).execute();
            in = new BufferedInputStream(response.body().byteStream());
            raf = new RandomAccessFile(downloadingFile, "rwd");
            if (!TextUtils.isEmpty(response.header("content-range"))) {
                raf.seek(downloadedLength);
            }
            int percent = 0;
            int currentPercent = 0;
            int readLength;
            byte[] buffer = new byte[2048];
            while ((readLength = in.read(buffer)) != -1) {
                downloadedLength += readLength;
                raf.write(buffer, 0, readLength);
                if (contentLength == 0) continue;
                currentPercent = (int) (downloadedLength * 100 / contentLength);
                if (currentPercent - percent >= 1) {
                    percent = currentPercent;
                    if (mProgressUpdateListener != null) {
                        mProgressUpdateListener.onProgressUpdate(contentLength, downloadedLength, percent);
                    }
                }
                if (mStopped.get()) {
                    //Log.d(TAG, "download stopped");
                    break;
                }
            }
            result.setSuccessful(true);
            boolean complete = downloadedLength == contentLength;
            if (complete) {
                downloadingFile.renameTo(file);
                result.setFile(file);
            } else {
                result.setFile(downloadingFile);
            }
            result.setCompleted(complete);
        } finally {
            closeQuietly(response);
            closeQuietly(in);
            closeQuietly(raf);
        }
        return result;
    }

    @Override
    void stop() {
        mStopped.compareAndSet(false, true);
    }

    private OkHttpClient getOkHttpClient() {
        return OkHttpClientHolder.sOkHttpClient;
    }

    /**
     * 获取URL内容的长度
     *
     * @param url
     * @return
     * @throws IOException
     */
    private long getContentLength(String url) throws IOException {
        long length = 0;
        Request request = new Request.Builder().url(url).get().build();
        Response response = null;
        try {
            response = getOkHttpClient().newCall(request).execute();
            if (response.isSuccessful()) {
                length = response.body().contentLength();
            }
        } finally {
            closeQuietly(response);
        }
        return length;
    }

    private void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static class OkHttpClientHolder {
        static OkHttpClient sOkHttpClient;

        static {
            Log.d("OkHttpClientHolder", "static initializer: ");
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .addInterceptor(new CatchExceptionInterceptor())
                    .protocols(Collections.unmodifiableList(Arrays.asList(Protocol.HTTP_1_1)))
                    //.retryOnConnectionFailure(true)
                    //.addInterceptor(new LogInterceptor())
                    .build();
            sOkHttpClient = client;
        }
    }
}