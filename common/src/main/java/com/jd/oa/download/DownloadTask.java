package com.jd.oa.download;

import android.os.AsyncTask;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.encrypt.MD5Utils;

import java.io.File;

/**
 * 下载任务
 * Created by peidongbiao on 2018/6/20.
 */

public class DownloadTask extends AsyncTask<DownloadRequest, Long, DownloadResult> {
    private static final String TAG = "DownloadTask";

    public static final long PROGRESS_CHECKING = -1L;
    private DownloadRequest mRequest;
    private Downloader mDownloader;
    private DownloadListener mDownloadListener;

    public DownloadTask(Downloader downloader, DownloadListener downloadListener) {
        mDownloader = downloader;
        mDownloadListener = downloadListener;
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();
        mDownloadListener.onStart();
    }

    @Override
    protected DownloadResult doInBackground(DownloadRequest... requests) {
        mRequest = requests[0];
        DownloadResult result = download(requests[0]);
        //校验MD5
        if (!TextUtils.isEmpty(mRequest.getMessageDigest()) && result.isSuccessful() && result.isCompleted()) {
            publishProgress( 0L, 0L, PROGRESS_CHECKING);
            boolean checkResult = check(mRequest, result.getFile());
            if (!checkResult) {
                result.setSuccessful(false);
                result.setError(new IllegalStateException("check file's digest failed"));
                result.setErrorCode(DownloadResult.ERROR_FILE_DIGEST_INCORRECT);
                FileUtils.deleteFile(result.getFile());
                MELogUtil.localI(MELogUtil.TAG_AUP, "check md5 failed, md5: " + mRequest.getMessageDigest());
            }
        }
        MELogUtil.localI(MELogUtil.TAG_AUP, "download, req: " + JSON.toJSONString(mRequest)
                + ", errorCode: " + result.getErrorCode()
                + ", isSuccessful" + result.isSuccessful()
                + ", isCompleted" + result.isCompleted(),
                result.getError());
        return result;
    }

    @Override
    protected void onProgressUpdate(Long... values) {
        mDownloadListener.onProgressUpdate(values[0], values[1], values[2]);
    }

    @Override
    protected void onPostExecute(DownloadResult result) {
        if (result.isSuccessful()) {
            if (result.isCompleted()) {
                mDownloadListener.onSuccess(result.getFile());
            } else {
                mDownloadListener.onPaused(result.getFile());
            }
        } else {
            mDownloadListener.onFailure(result.getErrorCode(), result.getError());
        }
    }

    protected DownloadResult download(DownloadRequest request) {
        mDownloader.setProgressUpdateListener(new ProgressUpdateListener() {
            @Override
            public void onProgressUpdate(long total, long current, long progress) {
                publishProgress(total, current, progress);
            }
        });
        try {
            DownloadResult result = mDownloader.download(request);
            return result;
        } catch (Exception e) {
            Log.e(TAG, "download exception " + this.toString(), e);
            DownloadResult result = new DownloadResult();
            result.setSuccessful(false);
            result.setCompleted(false);
            result.setRequest(request);
            result.setError(e);
            return result;
        }
    }

    private boolean check(DownloadRequest request, File file) {
        if (TextUtils.isEmpty(request.getMessageDigest())) return true;
        String fileMd5 = MD5Utils.getFileMD5(file);
        return request.getMessageDigest().equalsIgnoreCase(fileMd5);
    }

    public void stop() {
        mDownloader.stop();
    }
}