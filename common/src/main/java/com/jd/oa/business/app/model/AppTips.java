package com.jd.oa.business.app.model;

import androidx.annotation.Keep;

/**应用气泡提示
 * Created by peidongbiao on 2018/8/14.
 */
@Keep
public class AppTips {
    private String appId; // 其它的是appID
    private String count;

    public String getAppId() {
        if (appId == null)
            return "";

        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }
}