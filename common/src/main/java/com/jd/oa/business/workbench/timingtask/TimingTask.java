package com.jd.oa.business.workbench.timingtask;

import android.content.Context;
import android.content.Intent;
import android.os.Parcelable;

/**
 * Created by <PERSON> on 2017/8/16.
 */

public abstract class TimingTask implements Parcelable {

    public static final int DEFAULT_TASK_TYPE = 1;

    public static final int TASK_TYPE_TASK_NOTITFY = 2;
    public static final int TASK_TYPE_DAKA_USE_CAR = 3;

    public static final int MAX_TASK_ID = 10000000;

    public final int getRequestCode() {
        return getTimingTaskType() * MAX_TASK_ID + getTimingTaskId();
    }

    public abstract void executor(Context context, Intent intent);

    public abstract int getTimingTaskType();

    public abstract int getTimingTaskId();

}
