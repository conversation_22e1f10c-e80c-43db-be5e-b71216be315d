package com.jd.oa.business.smime;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.smime.utils.SmimeUtil;
import com.jd.oa.db.greendao.MailCert;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.ActionBarHelper;
import com.jme.common.R;

public class SmimeDemoFragment extends BaseFragment {

    View mRootView;

    TextView mTvGeneral;
    TextView mTvSign;
    TextView mTvEncrypt;

    TextView mTvPaseEml;

    TextView tv_test_getCert;
    TextView tv_test_getPfx;

    private SmimeHelper helper;

    private String emlPath;
    private String p7mPath;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_fragment_test_sime, container, false);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle("test");
        initView();
        return mRootView;
    }

    private void initView() {

        helper = new SmimeHelper(getActivity());

        mTvGeneral = mRootView.findViewById(R.id.tv_test_general);
        mTvGeneral.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                general();
            }
        });

        mTvSign = mRootView.findViewById(R.id.tv_test_sign);
        mTvSign.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sign();
            }
        });

        mTvEncrypt = mRootView.findViewById(R.id.tv_test_encrypt);
        mTvEncrypt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                String testFilepath = FileCache.getInstance().getMailFile() + "/1587628303417.eml";
//                helper.decrypt(testFilepath, "<EMAIL>", new ISmimeCallback() {
//                    @Override
//                    public void done(String var1) {
//                        String aa = "";
//                    }
//
//                    @Override
//                    public void fail(String var1) {
//                        String aa = "";
//                    }
//                });
            }
        });

        mTvPaseEml = mRootView.findViewById(R.id.tv_test_parseEml);
//        mTvPaseEml.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                String emlPath = FileCache.getInstance().getMailFile() + "/test0000.eml";
//                helper.parseEml(emlPath, new ISmimeCallback() {
//                    @Override
//                    public void done(String var1) {
//
//                    }
//
//                    @Override
//                    public void fail(String var1) {
//
//                    }
//                });
//            }
//        });

        String eMail = PreferenceManager.UserInfo.getEmailAddress();
        if (!TextUtils.isEmpty(PreferenceManager.UserInfo.getBindEmailAddress())) {
            eMail = PreferenceManager.UserInfo.getBindEmailAddress();
        }

        tv_test_getCert = mRootView.findViewById(R.id.tv_test_getCert);
        final String finalEMail = eMail;

        tv_test_getCert.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

//                SmimeUtil.getCertificate("<EMAIL>", "2", new LoadDataCallback<MailCert>() {
//                    @Override
//                    public void onDataLoaded(MailCert certInfo) {
//                        String aa = "";
//                    }
//
//                    @Override
//                    public void onDataNotAvailable(String s, int i) {
//
//                    }
//                });
            }
        });

        tv_test_getPfx = mRootView.findViewById(R.id.tv_test_getPfx);
        final String finalEMail1 = eMail;
        tv_test_getPfx.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SmimeUtil.getCertificate(finalEMail1, "1", new LoadDataCallback<MailCert>() {
                    @Override
                    public void onDataLoaded(MailCert certInfo) {
                        String aa = "";
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {

                    }
                });
            }
        });

    }

    private void sign() {
//        String testStr = "{\"uploadUrl\":\"\",\"recipients\":[\"<EMAIL>\",\"<EMAIL>\"],\"htmlbody\":\"里了里里里里里里里了里里里里里里\",\"attachList\":[{\"name\":\"2.docx\",\"localPath\":\"/storage/emulated/0/Download/2.docx\",\"isInline\":false,\"contentId\":\"2\"},{\"name\":\"Timline地图v2.1.docx\",\"localPath\":\"/storage/emulated/0/Download/Timline地图需求v2.1.docx\",\"isInline\":false,\"contentId\":\"Timline地图v2.1\"},{\"name\":\"ccfeipzreiw9470459.gif\",\"localPath\":\"/storage/emulated/0/Download/ccfe-ipzreiw9470459.gif\",\"isInline\":true,\"contentId\":\"ccfe-ipzreiw9470459.gif\"}]}";
//        EmailInfo info = new Gson().fromJson(testStr, EmailInfo.class);

//        helper.encryptAndSignEml(info, emlPath, "<EMAIL>", new ISmimeCallback() {
//            @Override
//            public void done(String var1) {
//                p7mPath = var1;
//            }
//
//            @Override
//            public void fail(String var1) {
//
//            }
//        });
    }

    private void general() {

//        String testStr = "{\"uploadUrl\":\"\",\"recipients\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"body\":\"里了里里里里里里里了里里里里里里\",\"attachList\":[{\"name\":\"2.docx\",\"localPath\":\"/storage/emulated/0/Download/2.docx\",\"isInline\":false,\"contentId\":\"2\"},{\"name\":\"Timline地图v2.1.docx\",\"localPath\":\"/storage/emulated/0/Download/Timline地图需求v2.1.docx\",\"isInline\":false,\"contentId\":\"Timline地图v2.1\"},{\"name\":\"ccfeipzreiw9470459.gif\",\"localPath\":\"/storage/emulated/0/Download/ccfe-ipzreiw9470459.gif\",\"isInline\":true,\"contentId\":\"ccfe-ipzreiw9470459.gif\"}]}";
//        listAtta.add("/storage/emulated/0/Download/2.docx|attachment|id0");
//        listAtta.add("/storage/emulated/0/Download/ccfe-ipzreiw9470459.gif|inline|ccfe-ipzreiw9470459.gif@2020");
//        listAtta.add("/storage/emulated/0/Download/Timline地图需求v2.1.docx|attachment|id1");

//        final EmailInfo info = new Gson().fromJson(testStr, EmailInfo.class);
//
//        helper.createEml(info, new ISmimeCallback() {
//            @Override
//            public void done(String var1) {
//                p7mPath = var1;
//
//            }
//
//            @Override
//            public void fail(String var1) {
//
//            }
//        });
    }

}
