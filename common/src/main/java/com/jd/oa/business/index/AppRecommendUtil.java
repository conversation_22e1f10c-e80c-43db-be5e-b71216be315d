package com.jd.oa.business.index;

import android.content.Intent;
import android.text.TextUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: qudongshi
 * @date: 2025/2/10
 */
public class AppRecommendUtil {

    public static final String APP_MENU_ADD_RECOMMEND_URL_KEY = "AppMenuRecommend";

    public static List<AppInfo> getFavoriteAppsCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_APP_GET_FAVORITE, null);
        if (cache == null || cache.getResponse() == null) return null;
        List<AppInfo> list = JsonUtils.getGson().fromJson(cache.getResponse(), new TypeToken<ArrayList<AppInfo>>() {
        }.getType());
        return list;
    }

    public static void addFavoriteAppsToCache(List<AppInfo> list) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_APP_GET_FAVORITE, null, JsonUtils.getGson().toJson(list));
    }

    public static void removeFavoriteApp(AppInfo appInfo) {
        if (appInfo == null) {
            return;
        }
        NetWorkManagerAppCenter.removeFromFavorite(appInfo.getAppID(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ToastUtils.showToast(R.string.me_app_remove_favorite_success);
                // 通知工作台刷新
                LocalBroadcastManager.getInstance(AppBase.getTopActivity()).sendBroadcast(new Intent(AppUtils.ACTION_REFRESH_APP));
                List<AppInfo> appFavoriteApps = getFavoriteAppsCache();
                if (appFavoriteApps == null) {
                    return;
                }
                AppInfo tempAppInfo = null;
                for (AppInfo tmpInfo : appFavoriteApps) {
                    if (TextUtils.equals(tmpInfo.getAppID(), appInfo.getAppID())) {
                        tempAppInfo = tmpInfo;
                        break;
                    }
                }
                if (tempAppInfo != null) {
                    appFavoriteApps.remove(tempAppInfo);
                    addFavoriteAppsToCache(appFavoriteApps);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_app_remove_favorite_fail);
            }
        });
    }

    public static void addFavoriteApp(AppInfo appInfo) {
        if (appInfo == null) {
            return;
        }
        List<AppInfo> appInfos = getFavoriteAppsCache();
        if (appInfos != null && getFavoriteAppsCache().size() >= 24) {
            ToastUtils.showToast(R.string.me_appcenter_market_max_count_toast);
            return;
        }
        NetWorkManagerAppCenter.addToFavorite(appInfo.getAppID(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    // 通知工作台刷新
                    LocalBroadcastManager.getInstance(AppBase.getTopActivity()).sendBroadcast(new Intent(AppUtils.ACTION_REFRESH_APP));
//                    callback.onDataLoaded(response);
                    if (appInfos != null) {
                        appInfos.add(appInfo);
                        addFavoriteAppsToCache(appInfos);
                    }
                    ToastUtils.showToast(R.string.me_app_add_favorite_success);
                } else {
                    int errorCode = -1000;
                    try {
                        errorCode = Integer.parseInt(response.getErrorCode());
                        if (errorCode == 1 || errorCode == 1050103) {
                            //常用应用已经达到上限
                            ToastUtils.showToast(R.string.me_appcenter_market_max_count_toast);
                        } else if (errorCode == 1050102) {
                            //点击频繁
                            ToastUtils.showToast(R.string.me_appcenter_market_max_count_toast);
                            ToastUtils.showToast(R.string.me_app_add_favorite_fail);
                        } else {
                            ToastUtils.showToast(R.string.me_app_add_favorite_fail);
                        }
                    } catch (Exception e) {

                    }
                    ToastUtils.showToast(R.string.me_app_remove_favorite_fail);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_app_remove_favorite_fail);
            }
        });
    }

    public static boolean innerFavoriteApp(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return false;
        }
        List<AppInfo> apps = getFavoriteAppsCache();
        if (apps == null) {
            return false;
        }
        for (AppInfo item : apps) {
            if (item.getAppID().equals(appId)) {
                return true;
            }
        }
        return false;
    }
}
