package com.jd.oa.business.workbench.model;

import android.content.Context;
import android.content.Intent;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import com.jd.oa.business.index.FunctionActivity;

/**
 * Created by <PERSON> on 2017/9/5.
 */

public class ToDoAppData implements Parcelable {
    public static final String MEET_BOOK = "MEET_BOOK";
    public static final String TODO_TASK = "TODO_TASK";
    public static final String MEET_BOOK_SINGIN = "1";
    public static final String MEET_BOOK_COMFIRM = "2";
    private String appAddress;
    private String isSignIn;

    public String getAppAddress() {
        return appAddress;
    }

    public void setAppAddress(String appAddress) {
        this.appAddress = appAddress;
    }

    public String getIsSignIn() {
        return isSignIn;
    }

    public void setIsSignIn(String isSignIn) {
        this.isSignIn = isSignIn;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.appAddress);
        dest.writeString(this.isSignIn);
    }

    public ToDoAppData() {
    }

    protected ToDoAppData(Parcel in) {
        this.appAddress = in.readString();
        this.isSignIn = in.readString();
    }

    public static final Parcelable.Creator<ToDoAppData> CREATOR = new Parcelable.Creator<ToDoAppData>() {
        @Override
        public ToDoAppData createFromParcel(Parcel source) {
            return new ToDoAppData(source);
        }

        @Override
        public ToDoAppData[] newArray(int size) {
            return new ToDoAppData[size];
        }
    };

    public void open(Context context) {
        if (TextUtils.equals(MEET_BOOK, appAddress)) {
            Intent intent = new Intent(context, FunctionActivity.class);
//            intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyReservationFragment.class.getName());
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.conference.MyReservationFragment");
            context.startActivity(intent);
        } else if(TextUtils.equals(TODO_TASK,appAddress)) {
            Intent intent = new Intent(context, FunctionActivity.class);
//            intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveFragment.class.getName());
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.conference.MyApproveFragment");
            context.startActivity(intent);
        }
    }
}
