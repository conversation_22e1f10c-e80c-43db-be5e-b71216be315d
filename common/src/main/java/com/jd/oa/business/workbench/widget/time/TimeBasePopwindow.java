package com.jd.oa.business.workbench.widget.time;

import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;

import java.util.Date;

/**
 * Created by liyao8 on 2017/8/24.
 * 任务选择开始和结束时间的popwindow的基类
 */

public abstract class TimeBasePopwindow extends PopupWindow {
    protected long startTimeMillis = 0l;
    protected long endTimeMillis = 0l;
    protected int minStep = 1;
    protected long selectedMillis = new Date().getTime();//默认选中当前时间
    protected boolean setBackgroundAlpha = true;
    protected IPopwindowCallback mCallBack;
    protected Context context;

    public interface IPopwindowCallback {

        void onConfirmCallback(String day, String time);

        void onCancel();
    }

    public TimeBasePopwindow(Context context, IPopwindowCallback mCallBack) {
        super(context);
        this.mCallBack = mCallBack;
        this.context = context;
    }

    public abstract void init();

    /**
     * 设置开始时间
     *
     * @param startTimeMillis 毫秒值
     * @return
     */
    public TimeBasePopwindow setStartTimeMillis(long startTimeMillis) {
        this.startTimeMillis = startTimeMillis;
        return this;
    }

    /**
     * 设置结束时间
     *
     * @param endTimeMillis 毫秒值
     * @return
     */
    public TimeBasePopwindow setEndimeMillis(long endTimeMillis) {
        this.endTimeMillis = endTimeMillis;
        return this;
    }

    /**
     * 设置初始化显示时 被选中的时间
     *
     * @param selectedMillis 毫秒值
     * @return
     */
    public TimeBasePopwindow setInitSelectedMillis(long selectedMillis) {
        this.selectedMillis = selectedMillis;
        return this;
    }

    public TimeBasePopwindow setMinStep(int minStep) {
        this.minStep = minStep;
        return this;
    }

    public void show(View rootView) {
        this.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        this.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (setBackgroundAlpha)
                    backgroundAlpha((Activity) context, 1f);
            }
        });
        this.showAtLocation(rootView, Gravity.BOTTOM, 0, 0);
        if (setBackgroundAlpha)
            backgroundAlpha((Activity) context, 0.5f);
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private static void backgroundAlpha(Activity activity, float bgAlpha) {
        WindowManager.LayoutParams lp = activity.getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        activity.getWindow().setAttributes(lp);
    }

}
