package com.jd.oa.business.workbench;

import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.business.workbench.model.ToDo;
import com.jd.oa.business.workbench.timingtask.ScheduleNotifyTimingTask;
import com.jd.oa.business.workbench.timingtask.TimingTask;
import com.jd.oa.business.workbench.timingtask.TimingTaskUtils;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;

public class TodoUtils {

    public static void addRemind(ArrayList<ToDo> todoList) {
        //改为服务器推送
        for (ToDo toDo : todoList) {
            toDo.setUserName(PreferenceManager.UserInfo.getUserName());
            TimingTask timingTask = new ScheduleNotifyTimingTask(toDo);
            if (!TextUtils.equals(toDo.getRemindTime(), "0") && !toDo.isCustom()) {
                long remind = toDo.getRemindTimeInMillis();
                if (remind > System.currentTimeMillis()) {
                    TimingTaskUtils.set(AppBase.getAppContext(), remind, timingTask);
                }
            }
        }
    }
}
