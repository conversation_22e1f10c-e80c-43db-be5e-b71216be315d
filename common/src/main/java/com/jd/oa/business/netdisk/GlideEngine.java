package com.jd.oa.business.netdisk;

import static com.jd.oa.utils.BitmapUtil.calculateInSampleSize;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.jd.oa.bundles.netdisk.transfer.TransferDownloadFile;
import com.jd.oa.bundles.netdisk.transfer.TransferService;
import com.jd.oa.bundles.netdisk.transfer.manager.TransferManager;
import com.yu.bundles.album.image.ImageEngine;
import com.yu.bundles.album.utils.MethodUtils;

import java.io.File;

/**
 * Created by liyu20 on 2017/9/30.
 */

public class GlideEngine implements ImageEngine {

    @Override
    public void loadImg(final Context context, Object path, final ImageView imageView, boolean isNeedPlaceHolder, final AlbumEngineLoadListener... listeners) {
        if(path instanceof TransferManager.DownloadTask){
            final TransferManager.DownloadTask downloadTask = (TransferManager.DownloadTask) path;
            TransferService.addListener(new TransferManager.TransferServiceCallback() {
                @Override
                public void onReceiveMessage(TransferManager.TransferMessage transferMessage, Object... params) {
                    if(transferMessage != TransferManager.TransferMessage.DOWNLOAD_SUCCESS){
                        return;
                    }
                    Activity activity = (Activity) context;
                    TransferDownloadFile transferDownloadFile = (TransferDownloadFile) params[0];
                    if (transferDownloadFile.getDownloadFileInfo().uuid.equals(downloadTask.bean.uuid) &&
                            context != null && !activity.isFinishing()) {
                        if (transferDownloadFile.getSaveFilePath().endsWith(".gif") || transferDownloadFile.getSaveFilePath().endsWith(".GIF")) {
                            RequestOptions options = new RequestOptions()
                                    .centerCrop()
                                    .error(android.R.drawable.stat_notify_error)
                                    .priority(Priority.HIGH)
                                    .error(com.yu.bundles.album.R.mipmap.mae_album_img_default)
                                    .diskCacheStrategy(DiskCacheStrategy.NONE);
                            Glide.with(context)
                                    .load(transferDownloadFile.getSaveFilePath()).apply(options).into(imageView);
                        } else {
                            RequestOptions options = new RequestOptions()
                                    .error(com.yu.bundles.album.R.mipmap.mae_album_img_default);
                            Glide.with(context).asBitmap()
                                    .load(transferDownloadFile.getSaveFilePath())
                                    .apply(options)
                                    .listener(new RequestListener<Bitmap>() {
                                        @Override
                                        public boolean onLoadFailed(@Nullable GlideException e, Object o, Target<Bitmap> target, boolean b) {
                                            try {
                                                String filePath = transferDownloadFile.getSaveFilePath();
                                                if (filePath != null) {
                                                    BitmapFactory.Options options = new BitmapFactory.Options();
                                                    options.inJustDecodeBounds = true;
                                                    BitmapFactory.decodeFile(filePath, options);
                                                    BitmapFactory.Options op = new BitmapFactory.Options();
                                                    op.inJustDecodeBounds = false;
                                                    op.inSampleSize = calculateInSampleSize(options, 2000, 2000);
                                                    imageView.setImageBitmap(BitmapFactory.decodeFile(filePath, op));
                                                    return true;
                                                }
                                            } catch (Exception ex) {
                                                ex.printStackTrace();
                                            }
                                            return false;
                                        }

                                        @Override
                                        public boolean onResourceReady(Bitmap drawable, Object o, Target<Bitmap> target, DataSource dataSource, boolean b) {
                                            return false;
                                        }
                                    })
                                    .into(imageView);
                        }

                        if (listeners != null && listeners.length > 0) {
                            listeners[0].onLoadComplete();
                        }
                        TransferService.removeListener(this);
                    }
                }
            });
            TransferService.addDownloadTask(downloadTask);
        } else {
            RequestOptions options = new RequestOptions().error(com.yu.bundles.album.R.mipmap.mae_album_img_default);
            RequestBuilder builder = Glide.with(context).asBitmap().load(path);
            if (isNeedPlaceHolder) {
                options.placeholder(com.yu.bundles.album.R.mipmap.mae_album_img_default);
            }
            if (MethodUtils.isNull(listeners)) {
                builder.into(imageView);
            } else {
                builder.listener(new GlideRequestListener(listeners[0])).into(imageView);
            }
        }
    }

    @Override
    public void onOuterPreviewPageSelected(int position, Object path, boolean loadImgSuccess) {
        if(!loadImgSuccess && path instanceof TransferManager.DownloadTask){
            TransferService.addDownloadTask((TransferManager.DownloadTask) path);
        }
    }

    @Override
    public void loadGifImg(Context context, Object path, ImageView imageView, boolean isNeedPlaceHolder, final AlbumEngineLoadListener... listeners) {
        RequestOptions options = new RequestOptions()
                .centerCrop()
                .error(android.R.drawable.stat_notify_error)
                .priority(Priority.HIGH)
                .error(com.yu.bundles.album.R.mipmap.mae_album_img_default)
                .diskCacheStrategy(DiskCacheStrategy.NONE);
        if (isNeedPlaceHolder) {
            options.placeholder(com.yu.bundles.album.R.mipmap.mae_album_img_default);
        }
        if (MethodUtils.isNull(listeners)) {
            Glide.with(context).load(path).apply(options).into(imageView);
        } else {
            Glide.with(context).load(path).apply(options).listener(new GlideRequestListener(listeners[0])).into(imageView);
        }
    }

    private class GlideRequestListener implements RequestListener{
        private AlbumEngineLoadListener listener;

        private GlideRequestListener(AlbumEngineLoadListener listener) {
            this.listener = listener;
        }

        @Override
        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target target, boolean isFirstResource) {
            listener.onLoadComplete();
            return false;
        }

        @Override
        public boolean onResourceReady(Object resource, Object model, Target target, DataSource dataSource, boolean isFirstResource) {
            listener.onLoadComplete();
            return false;
        }
    }

    @Override
    public File downloadFile(Context context, Object url) {
        try {
            return Glide.with(context)
                    .load(url)
                    .downloadOnly(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL)
                    .get();
        } catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}