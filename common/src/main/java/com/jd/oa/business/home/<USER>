package com.jd.oa.business.home;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.Window;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

import java.lang.ref.WeakReference;
@Route({ DeepLink.SMALL_TV_LIVE, DeepLink.SMALL_TV_VIDEO})
public class EmptyActivity extends BaseActivity {
    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        super.onCreate(savedInstanceState);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();//隐藏头
        }
        setContentView(R.layout.jdme_activity_empty);
        if (!TabletUtil.mMainActivityExist) {
            finish();
        }
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                TabletUtil.uploadSplitEnable();
            }
        }, 1000);
    }

    @Override
    protected void onResume() {
        super.onResume();
        AppBase.padEmptyActivity = new WeakReference<>(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        AppBase.padEmptyActivity = null;
    }
}
