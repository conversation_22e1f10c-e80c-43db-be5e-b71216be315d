package com.jd.oa.business.app.adapter;

import android.content.Context;
import androidx.viewpager.widget.PagerAdapter;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2018/8/10.
 */

public class FavoritePagerAdapter extends PagerAdapter{

    private Context mContext;
    private List<List<AppInfo>> mData;
    private OnItemClickListener mOnItemClickListener;
    private OnItemLongClickListener mOnItemLongClickListener;

    public static final int FAVORITE_PAGE_NUM = 8;
    public static final int FAVORITE_PAGE_COLUMN = 4;

    private List<AppRecyclerAdapter> mAdapters;

    public FavoritePagerAdapter(Context context) {
        mContext = context;
        mData = new ArrayList<>();
        mAdapters = new ArrayList<>();
    }

    @Override
    public int getItemPosition(Object object) {
        return POSITION_NONE;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object o) {
        return view == o;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_pager_app, container, false);
        RecyclerView recyclerView = view.findViewById(R.id.recycler_view);
        RecyclerView.LayoutManager layoutManager = new GridLayoutManager(mContext, FAVORITE_PAGE_COLUMN);
        recyclerView.setLayoutManager(layoutManager);
        List<AppInfo> list =  mData.get(position);
        final AppRecyclerAdapter adapter = new AppRecyclerAdapter(mContext, list);
//        adapter.setShowMore(list.size() < FAVORITE_PAGE_NUM);
        adapter.setShowMore(false);
        adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter baseRecyclerAdapter, View view, int position) {
                if (mOnItemClickListener != null) {
                    if (adapter.isMore(position)) {
                        mOnItemClickListener.onMoreClick();
                    } else {
                        AppInfo info = (AppInfo) baseRecyclerAdapter.getItem(position);
                        mOnItemClickListener.onItemClick(info);
                    }
                }
            }
        });
        adapter.setOnItemLongClickListener(new BaseRecyclerAdapter.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(BaseRecyclerAdapter baseRecyclerAdapter, View view, int position) {
                if (mOnItemLongClickListener != null && !adapter.isMore(position)) {
                    AppInfo info = (AppInfo) adapter.getItem(position);
                    return mOnItemLongClickListener.onItemLongClick(info);
                }
                return true;
            }
        });
        recyclerView.setAdapter(adapter);
        container.addView(view);
        mAdapters.add(adapter);
        view.setTag(adapter);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        AppRecyclerAdapter adapter = (AppRecyclerAdapter) view.getTag();
        mAdapters.remove(adapter);
        container.removeView(view);
    }

    public void refresh(List<List<AppInfo>> list){
        mData.clear();
        mData.addAll(list);
        this.notifyDataSetChanged();
    }

    public void updateTips(List<AppTips> list){
        if (list == null)
            return;

        for (List<AppInfo> datum : mData) {
            for (AppInfo info : datum) {
                for (AppTips appTips : list) {
                    if(appTips.getAppId().equals(info.getAppID())){
                        info.setNumTip(appTips.getCount());
                    }
                }
            }
        }
        for (AppRecyclerAdapter adapter : mAdapters) {
            adapter.updateTips(list);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setOnItemLongClickListener(OnItemLongClickListener onItemLongClickListener) {
        mOnItemLongClickListener = onItemLongClickListener;
    }

    public interface OnItemClickListener{
        void onItemClick(AppInfo info);

        void onMoreClick();
    }

    public interface OnItemLongClickListener{
        boolean onItemLongClick(AppInfo info);
    }
}