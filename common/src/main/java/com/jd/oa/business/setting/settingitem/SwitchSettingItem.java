package com.jd.oa.business.setting.settingitem;

import android.content.Context;

import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import android.util.AttributeSet;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jme.common.R;

/**切换设置项
 * Created by peidongbiao on 2018/7/13.
 */

public class SwitchSettingItem extends LinearLayout {
    private TextView mTvName;
    private SwitchCompat mSwitch;
    private TextView mTvDescription;
    private View mViewDivider;
    private ViewGroup mLayoutItem;

    private String mName;
    private String mDesc;
    private boolean mSwitchChecked;
    private boolean mShowDivider;
    private int mItemCorner;

    public SwitchSettingItem(Context context) {
        this(context, null);
    }

    public SwitchSettingItem(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SwitchSettingItem(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setOrientation(LinearLayout.VERTICAL);
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_widget_switch_setting_item,this);
        mTvName = findViewById(R.id.tv_name);
        mSwitch = findViewById(R.id.sw);
        mTvDescription = findViewById(R.id.tv_description);
        mViewDivider = findViewById(R.id.view_divider);
        mLayoutItem = findViewById(R.id.layout_item);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, @Nullable AttributeSet attrs, int defStyleAttr){
        TypedArray typedArray = context.obtainStyledAttributes(attrs,R.styleable.SwitchSettingItem);
        mName = typedArray.getString(R.styleable.SwitchSettingItem_setting_name);
        mSwitchChecked = typedArray.getInteger(R.styleable.SwitchSettingItem_setting_switch_status,0) == 1;
        mDesc = typedArray.getString(R.styleable.SwitchSettingItem_setting_description);
        mShowDivider = typedArray.getBoolean(R.styleable.SwitchSettingItem_setting_show_divider,false);
        mItemCorner = typedArray.getInteger(R.styleable.SwitchSettingItem_setting_item_corner, 0);
        typedArray.recycle();

        mTvName.setText(mName);
        mSwitch.setChecked(mSwitchChecked);
        mTvDescription.setText(mDesc);
        mTvDescription.setVisibility(TextUtils.isEmpty(mDesc)? GONE : VISIBLE);
        mViewDivider.setVisibility(mShowDivider ? VISIBLE : GONE);
        setItemBackground(mItemCorner);
    }

    public void setDividerVisibility(boolean isVisible) {
        mViewDivider.setVisibility(isVisible ? VISIBLE : GONE);
    }

    public void setItemBackground(int type) {
        mLayoutItem.setBackgroundResource(getItemBackgroundRes(type));
    }

    private @DrawableRes int getItemBackgroundRes(int type) {
        mItemCorner = type;
        if (type == 1) {
            return R.drawable.jdme_ripple_white_top_corner8;
        } else if (type == 2) {
            return R.drawable.jdme_ripple_white_bottom_corner8;
        } else if (type == 3) {
            return R.drawable.jdme_ripple_white;
        } else {
            return R.drawable.jdme_ripple_white_corner8;
        }
    }

    public SwitchCompat getSwitchView() {
        return mSwitch;
    }

    public void setOnSwitchCheckedChangeListener(CompoundButton.OnCheckedChangeListener listener) {
        mSwitch.setOnCheckedChangeListener(listener);
    }

    public void setSwitchChecked(boolean checked) {
        mSwitch.setChecked(checked);
    }

    public boolean isSwitchChecked() {
        return mSwitch.isChecked();
    }

    public void setName(String name) {
        mName = name;
        mTvName.setText(mName);
    }

    public void setDescription(String desc) {
        mDesc = desc;
        mTvDescription.setText(mDesc);
    }
}
