package com.jd.oa.business.smime.utils;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.jd.oa.business.smime.model.UploadInfo;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.cache.FileCache;
import com.jd.oa.db.greendao.MailCert;
import com.jd.oa.fragment.utils.DownloadUtil;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jd.oa.utils.encrypt.RSAUtil;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.io.UnsupportedEncodingException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import ipworkssmime.Certificate;
import ipworkssmime.IPWorksSMIMEException;

public class SmimeUtil {

    public static final String TYPE_PRK = "1";  //私钥

    public static final String TYPE_PUK = "2"; //公钥

    public static byte[] readFile(File file) {
        RandomAccessFile rf = null;
        byte[] data = null;
        try {
            rf = new RandomAccessFile(file, "r");
            data = new byte[(int) rf.length()];
            rf.readFully(data);
        } catch (Exception exception) {
            exception.printStackTrace();
        } finally {
            closeQuietly(rf);
        }
        return data;
    }

    //关闭读取file
    private static void closeQuietly(Closeable closeable) {
        try {
            if (closeable != null) {
                closeable.close();
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    public static byte[] byteMerger(byte[] byte_1, byte[] byte_2) {
        byte[] byte_3 = new byte[byte_1.length + byte_2.length];
        System.arraycopy(byte_1, 0, byte_3, 0, byte_1.length);
        System.arraycopy(byte_2, 0, byte_3, byte_1.length, byte_2.length);
        return byte_3;
    }

    public static String streamToString(InputStream inputStream) {
        try {
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }
            return result.toString("UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getRealFileName(String path) {
        int start = path.lastIndexOf("/");
        int end = path.lastIndexOf(".");
        if (start != -1 && end != -1) {
            return path.substring(start + 1, end);
        } else {
            return null;
        }
    }


    /**
     * 获取证书
     *
     * @param email    邮箱地址
     * @param type     证书类型，1：私钥 2：公钥
     * @param callback 回调
     */
    public static void getCertificate(String email, final String type, final LoadDataCallback<MailCert> callback) {

        String key = RSAUtil.encrypt(KeyManager.getInstance().getServerPublicKey(), PreferenceManager.UserInfo.getEmailPwd());
        Map<String, Object> param = new HashMap<>();
        param.put("userName", PreferenceManager.UserInfo.getUserName());
        param.put("mailAccount", email);
        param.put("encryption", key);
        param.put("type", type);

        try {
            MailCert mailCert = MailCertDaoHelper.getCert(email, type);
            if (null != mailCert) {
                if (null != mailCert.getEnd() && mailCert.getEnd() > System.currentTimeMillis()) { // 如果没有有效期、失效重新获取
                    if (!TextUtils.isEmpty(mailCert.getPwd())) {
                        String val = JdmeEncryptUtil.getDecryptString(mailCert.getPwd());
                        mailCert.setPwd(val);
                    }
                    callback.onDataLoaded(mailCert);
                    return;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        NetWorkManager.getMailCertificate(null, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<MailCert> response = ApiResponse.parse(info.result, MailCert.class);
                MailCert res = response.getData();
                if (null == res.getUser()) {
                    callback.onDataNotAvailable("null", -1);
                    return;
                }
                res.setType(type);
                res.setTimestamp(System.currentTimeMillis());
                if (type.equals(TYPE_PRK)) {
                    getPKDate(res);
                }
                if (type.equals(TYPE_PUK)) {
                    getPUDate(res);
                }
                if (!TextUtils.isEmpty(res.getPwd())) {
                    String encryptString = JdmeEncryptUtil.getEncryptString(res.getPwd());
                    res.setPwd(encryptString);
                }
                MailCertDaoHelper.insertData(res);
                if (!TextUtils.isEmpty(res.getPwd())) {
                    String decryptString = JdmeEncryptUtil.getDecryptString(res.getPwd());
                    res.setPwd(decryptString);
                }
                callback.onDataLoaded(res);
            }
        }, param);
    }

    /**
     * 获取证书
     *
     * @param url      上传地址
     * @param filePath 文件路径
     * @param callback 回调
     */
    public static void uploadFile(String url, final String filePath, final LoadDataCallback<UploadInfo> callback) {
        Map<String,File> fileMap = new HashMap<>();
        fileMap.put("file", new File(filePath));
        HttpManager.legacy().upload(url, null, fileMap, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                UploadInfo uploadInfo = new Gson().fromJson(info.result, UploadInfo.class);
                callback.onDataLoaded(uploadInfo);
            }
        });
    }

    public static void downloadEml(String url, final LoadDataCallback<String> callback) {
        final String filePath = FileCache.getInstance().getMailFile().getAbsolutePath() + "/" + System.currentTimeMillis() + ".eml";
        DownloadUtil.get().download(url, filePath, new HashMap<String, String>(), "", new DownloadUtil.OnDownloadListener() {
            @Override
            public void onDownloadSuccess() {
                callback.onDataLoaded(filePath);
            }

            @Override
            public void onDownloading(int progress) {

            }

            @Override
            public void onDownloadFailed() {
                callback.onDataNotAvailable("", -1);
            }
        });
//        OkHttpManager.down(url, filePath, false, false, new SimpleRequestCallback<File>() {
//            @Override
//            public void onSuccess(ResponseInfo<File> responseInfo) {
//
//            }
//
//            @Override
//            public void onLoading(long total, long current, boolean isUploading) {
//                super.onLoading(total, current, isUploading);
//            }
//
//            @Override
//            public void onFailure(HttpException e, String s) {
//
//            }
//        });
    }

    public static String getEmailAddress() {
        String emailAddress = PreferenceManager.UserInfo.getBindEmailAddress();
        if (TextUtils.isEmpty(emailAddress)) {
            emailAddress = PreferenceManager.UserInfo.getEmailAddress();
        }
        return emailAddress;
    }

    private static void getPKDate(MailCert cert) {
        Certificate certificate = null;
        try {
            certificate = new Certificate(Certificate.cstPFXBlob, cert.getCert().getBytes("utf-8"), cert.getPwd(), "*");
            cert.setBegin(getTimeMillis(certificate.getEffectiveDate()));
            cert.setEnd(getTimeMillis(certificate.getExpirationDate()));
        } catch (IPWorksSMIMEException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    private static void getPUDate(MailCert cert) {
        Certificate certificate = null;
        try {
            certificate = new Certificate(cert.getCert().getBytes("utf-8"));
            cert.setBegin(getTimeMillis(certificate.getEffectiveDate()));
            cert.setEnd(getTimeMillis(certificate.getExpirationDate()));
        } catch (IPWorksSMIMEException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    private static final String DATE_FORMAT_LONG = "dd-MMM-yyyy HH:mm:ss";

    private static long getTimeMillis(String dateStr) {
        DateFormat format1 = new SimpleDateFormat(DATE_FORMAT_LONG, Locale.ENGLISH);
        try {
            Date date = format1.parse(dateStr);
            return date.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return -1;
    }

    private static final Pattern charsetPattern = Pattern.compile("(?i)\\bcharset=\\s*\"?([^\\s;\"]*)");

    public static String getCharset(String str) {
        if (TextUtils.isEmpty(str)) {
            return "";
        }
        Matcher m = charsetPattern.matcher(str);
        if (m.find()) {
            return m.group(1).trim().toUpperCase();
        }
        return null;

    }
}