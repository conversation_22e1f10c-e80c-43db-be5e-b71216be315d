package com.jd.oa.business.setting.settingitem;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.jme.common.R;

/**设置项
 * Created by peidongbiao on 2018/7/12.
 */

public class SettingItem extends LinearLayout {

    private ImageView mIvIcon;
    private TextView mTvName;
    private TextView mTvBadge;
    private TextView mTvTips;
    private ImageView mIvArrow;
    private TextView mTvDescription;
    private View mViewDivider;
    private ViewGroup mLayoutItem;
    private ProgressBar mPbLoading;

    private String mName;
    private String mBadge;
    private String mTips;
    private int mTipsColor;
    private String mDesc;
    private boolean mShowDivider;
    private int mDividerMarginStart;
    private int mDividerMarginEnd;
    private Drawable mIconDrawable;
    private boolean mShowArrow;

    private View.OnClickListener mClickListener;

    public SettingItem(Context context) {
        this(context, null);
    }

    public SettingItem(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SettingItem(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setOrientation(LinearLayout.VERTICAL);
        setClickable(true);
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_widget_setting_item,this);
        mIvIcon = findViewById(R.id.iv_icon);
        mTvName = findViewById(R.id.tv_name);
        mTvBadge = findViewById(R.id.tv_badge);
        mTvTips = findViewById(R.id.tv_tips);
        mIvArrow = findViewById(R.id.iv_arrow);
        mTvDescription = findViewById(R.id.tv_description);
        mViewDivider = findViewById(R.id.view_divider);
        mLayoutItem = findViewById(R.id.layout_item);
        mPbLoading = findViewById(R.id.pb_loading);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, @Nullable AttributeSet attrs, int defStyleAttr){
        TypedArray typedArray = context.obtainStyledAttributes(attrs,R.styleable.SettingItem);
        mName = typedArray.getString(R.styleable.SettingItem_setting_name);
        mBadge = typedArray.getString(R.styleable.SettingItem_setting_badge);
        mTips = typedArray.getString(R.styleable.SettingItem_setting_tips);
        mDesc = typedArray.getString(R.styleable.SettingItem_setting_description);
        mShowDivider = typedArray.getBoolean(R.styleable.SettingItem_setting_show_divider,false);
        mDividerMarginStart = typedArray.getDimensionPixelOffset(R.styleable.SettingItem_setting_divider_margin_start, 0);
        mDividerMarginEnd = typedArray.getDimensionPixelOffset(R.styleable.SettingItem_setting_divider_margin_end, 0);
        mIconDrawable = typedArray.getDrawable(R.styleable.SettingItem_setting_icon);
        mShowArrow = typedArray.getBoolean(R.styleable.SettingItem_setting_show_arrow, true);
        typedArray.recycle();

        if(mIconDrawable != null){
            mIvIcon.setVisibility(VISIBLE);
            mIvIcon.setImageDrawable(mIconDrawable);
        }
        mTvName.setText(mName);
        mTvBadge.setText(mBadge);
        mTvTips.setText(mTips);
        mTvDescription.setText(mDesc);
        mTvDescription.setVisibility(TextUtils.isEmpty(mDesc)? GONE : VISIBLE);
        mViewDivider.setVisibility(mShowDivider ? VISIBLE : GONE);
        mIvArrow.setVisibility(mShowArrow ? VISIBLE : GONE);

        mLayoutItem.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mClickListener != null){
                    mClickListener.onClick(SettingItem.this);
                }
            }
        });
    }

    public void setLoading(boolean loading){
        mTvTips.setVisibility(loading ? GONE : VISIBLE);
        mIvArrow.setVisibility(loading ? GONE : VISIBLE);
        mPbLoading.setVisibility(loading ? VISIBLE : GONE);
    }

    public void setName(String name){
        mName = name;
        mTvName.setText(mName);
    }

    public void setBadge(String badge) {
        mBadge = badge;
        mTvBadge.setText(mBadge);
    }

    public void setTips(String tips){
        mTips = tips;
        mTvTips.setText(mTips);
    }

    public void setTips(int resId){
        mTvTips.setText(resId);
    }

    public void setTipsColor(int resColor){
        mTipsColor = resColor;
        mTvTips.setTextColor(resColor);
    }

    public void setDescription(String description){
        mDesc = description;
        mTvDescription.setText(mDesc);
    }

    public void setIcon(@DrawableRes int icon){
        mIconDrawable = ContextCompat.getDrawable(getContext(), icon);
        mIvIcon.setImageDrawable(mIconDrawable);
    }

    public void setShowDivider(boolean showDivider){
        mShowDivider = showDivider;
        mViewDivider.setVisibility(mShowDivider ? VISIBLE : GONE);
    }

    public void setShowIcon(boolean showIcon){
        mIvIcon.setVisibility(showIcon ? VISIBLE : GONE);
    }

    public ImageView getIconImage(){
        return mIvIcon;
    }

    public String getBadge() {
        return mBadge;
    }

    public void setOnSettingClickListener(View.OnClickListener listener){
        mClickListener = listener;
    }
}