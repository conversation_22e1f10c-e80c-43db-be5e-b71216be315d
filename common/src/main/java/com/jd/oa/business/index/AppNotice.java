package com.jd.oa.business.index;

import android.text.TextUtils;

import com.jd.oa.business.index.model.AppNoticeBean;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.io.Serializable;

/**
 * Created by qudo<PERSON><PERSON> on 2017/6/26.
 */

public class AppNotice implements Serializable {

    private static AppNotice appNotice;

    private AppNoticeBean mData;

    private boolean initFlag = false;

    private AppNotice() {

    }

    public static AppNotice getInstant() {
        if (null == appNotice) {
            appNotice = new AppNotice();
        }
        return appNotice;
    }

    /**
     * 更新数据
     */
    public void updateData() {
        // 未登录不需要调用
        if(!PreferenceManager.UserInfo.getLogin()){
            return;
        }
        NetWorkManagerAppCenter.getAppNoticeList(new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);

                ApiResponse<AppNoticeBean> response = ApiResponse.parse(info.result, AppNoticeBean.class);
                if (response.isSuccessful()) {
                    mData = response.getData();
                    initFlag = true;
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

        });
    }

    public AppNoticeBean.NoticeBean getOutNotice(String appId) {
        AppNoticeBean.NoticeBean noticeBean = null;
        if (initFlag) {
            if (null != mData && null != mData.outNoticeList) {
                for (AppNoticeBean.NoticeBean bean : mData.outNoticeList) {
                    if (TextUtils.equals(appId,bean.appId)) {
                        noticeBean = bean;
                    }
                }
            }
        }
        return noticeBean;
    }

    public AppNoticeBean.NoticeBean getInsideNotice(String appId) {
        AppNoticeBean.NoticeBean noticeBean = null;
        if (initFlag) {
            if (null != mData && null != mData.insideNoticeList) {
                for (AppNoticeBean.NoticeBean bean : mData.insideNoticeList) {
                    if (TextUtils.equals(appId,bean.appId)) {
                        noticeBean = bean;
                    }
                }
            }
        }
        return noticeBean;
    }

}
