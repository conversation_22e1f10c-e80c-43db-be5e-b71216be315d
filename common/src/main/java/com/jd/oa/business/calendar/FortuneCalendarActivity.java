package com.jd.oa.business.calendar;

import android.graphics.Color;
import android.graphics.Paint;
import android.os.Bundle;
import androidx.appcompat.app.ActionBar;
import android.text.TextPaint;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.business.calendar.model.FortuneCalendar;
import com.jd.oa.ui.QMUIVerticalTextView;
import com.jd.oa.utils.AnimationListenerAdapter;
import com.jd.oa.utils.CharacterHelper;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.Logger;
import com.jme.common.R;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;


/**农历、运势日历
 * Created by peidongbiao on 2018/1/4.
 */

public class FortuneCalendarActivity extends BaseActivity {
    private static final String TAG = "FortuneCalendarActivity";

    public static final String ARG_CIRCLE_LOCATION_X = "arg.reveal.location.x";
    public static final String ARG_CIRCLE_LOCATION_Y = "arg.reveal.location.y";

    private FrameLayout mLayoutContainer;
    private ViewGroup mLayoutContentTop;
    private View mViewReveal;
    private ImageView mIvBackground;
    private LinearLayout mContentLayout;
    private TextView mTvDate;
    private QMUIVerticalTextView mTvLunarDate;
    private QMUIVerticalTextView mTvLunarDate2;
    private TextView mTvDayOfMonth;
    private TextView mTvTaboo;
    private TextView mTvPrecept;
    private TextView mTvAuthor;
    private TextView mTvSource;
    private FrameLayout mLayoutClose;
    private ImageView mIvClose;
    private ProgressBar mPbProgress;
    private boolean mClosing;

    private int mCircleLocationX;
    private int mCircleLocationY;

    private CalendarRepo mCalendarRepo;
    private FortuneCalendar mCalendar;
    private boolean mRequesting;

    @Override
    protected void configTimlineTheme() {
        setTheme(R.style.MEWhiteTransparentTheme);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        mCircleLocationX = getIntent().getIntExtra(ARG_CIRCLE_LOCATION_X, -1);
        mCircleLocationY = getIntent().getIntExtra(ARG_CIRCLE_LOCATION_Y, -1);
        if (mCircleLocationY != -1) {
            mCircleLocationY -= DisplayUtil.getStatusBarHeight(this);
        }
        setContentView(R.layout.jdme_activity_fortune_calendar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }

        mCalendarRepo = CalendarRepo.get();

        mLayoutContainer = findViewById(R.id.layout_container);
        mLayoutContentTop = findViewById(R.id.layout_content_top);
        mContentLayout = findViewById(R.id.layout_content);
        mViewReveal = findViewById(R.id.view_reveal);
        mIvBackground = findViewById(R.id.iv_background);
        mLayoutClose = findViewById(R.id.layout_close);
        mIvClose = findViewById(R.id.iv_close);
        mTvDate = findViewById(R.id.tv_date);
        mTvLunarDate = findViewById(R.id.tv_lunar_date);
        mTvLunarDate2 = findViewById(R.id.tv_lunar_date2);
        mTvDayOfMonth = findViewById(R.id.tv_day_of_month);
        mTvTaboo = findViewById(R.id.tv_taboo);
        mTvPrecept = findViewById(R.id.tv_precept);
        mTvAuthor = findViewById(R.id.tv_author);
        mTvSource = findViewById(R.id.tv_source);
        mPbProgress = findViewById(R.id.pb_progress);

        setRevealCircleLocation(mCircleLocationX, mCircleLocationY);

        mContentLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //startAnimation(mContentLayout);
            }
        });

        Animation revealAnimation = AnimationUtils.loadAnimation(this, R.anim.jdme_circle_reveal);
        revealAnimation.setAnimationListener(new AnimationListenerAdapter() {
            @Override
            public void onAnimationEnd(Animation animation) {
                Animation anim = AnimationUtils.loadAnimation(FortuneCalendarActivity.this, R.anim.jdme_calendar_content_show);
                mContentLayout.setAnimation(anim);
                mContentLayout.setVisibility(View.VISIBLE);
                mLayoutClose.setVisibility(View.VISIBLE);
                mIvBackground.setVisibility(View.VISIBLE);
                mLayoutContainer.setBackgroundColor(Color.parseColor("#f92f39"));
                mPbProgress.setVisibility(mRequesting ? View.VISIBLE : View.GONE);
            }
        });
        mViewReveal.startAnimation(revealAnimation);

        mIvClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        //fill(mCalendar);
        getCalendar();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(0, R.anim.jdme_anim_fade);
    }

    private void getCalendar() {
        mCalendarRepo.getCalendar()
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe(new Consumer<Disposable>() {
                    @Override
                    public void accept(@NonNull Disposable disposable) throws Exception {
                        mRequesting = true;
                    }
                })
                .doOnTerminate(new Action() {
                    @Override
                    public void run() throws Exception {
                        mRequesting = false;
                        mPbProgress.setVisibility(View.GONE);
                    }
                })
                .subscribe(new Consumer<FortuneCalendar>() {
                    @Override
                    public void accept(@NonNull FortuneCalendar fortuneCalendar) throws Exception {
                        fill(fortuneCalendar);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(@NonNull Throwable throwable) throws Exception {
                        Logger.e(TAG, throwable.getMessage(), throwable);
                        showDateOnError();
                    }
                });

    }

    private void fill(FortuneCalendar calendar) {
        if (calendar == null) {
            return;
        }
        mTvDate.setText(calendar.getDate());
        mTvDate.append(" ");
        mTvDate.append(calendar.getDayOfWeek());
        setLunarDate(calendar);
        //mTvDayOfMonth.setText(calendar.getDayOfMonth());
        //mTvTaboo.setText(calendar.getTaboo());
        mTvPrecept.setText(calendar.getPrecept());
        mTvAuthor.setText(calendar.getAuthor());
        mTvSource.setText(calendar.getSource());
    }

    private void showDateOnError() {
        Calendar calendar = Calendar.getInstance();
        String date = new SimpleDateFormat("yyyy.MM.dd", Locale.getDefault()).format(calendar.getTime());
        String week = getResources().getStringArray(R.array.me_week_day_calendar)[calendar.get(Calendar.DAY_OF_WEEK) - 1];
        mTvDate.setText(String.format(Locale.getDefault(), "%s %s", date, week));
        mTvDayOfMonth.setText(String.valueOf(calendar.get(Calendar.DAY_OF_MONTH)));
        mTvTaboo.setText(R.string.me_calendar_default_allow);
        mTvPrecept.setText(R.string.me_calendar_default_precept);
        mTvAuthor.setText(R.string.me_calendar_default_author);
        mTvSource.setText(R.string.me_calendar_default_source);
    }

    /**
     * 设置打开时扩展波纹的位置
     * @param x
     * @param y
     */
    private void setRevealCircleLocation(int x, int y) {
        if (x == -1 || y == -1) {
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mViewReveal.getLayoutParams();
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            mViewReveal.setLayoutParams(layoutParams);
        } else {
            int measureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            mViewReveal.measure(measureSpec, measureSpec);
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mViewReveal.getLayoutParams();
            layoutParams.leftMargin = x - mViewReveal.getMeasuredWidth() / 2;
            layoutParams.topMargin = y - mViewReveal.getMeasuredHeight() / 2;
            mViewReveal.setLayoutParams(layoutParams);
        }
    }

    /**
     * 设置农历日期。根据字数和空格决定是否换行
     * @param calendar
     */
    private void setLunarDate(final FortuneCalendar calendar) {
        final String date = calendar.getLunarDate();
        mLayoutContentTop.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                mLayoutContentTop.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                mTvDayOfMonth.setText(calendar.getDayOfMonth());
                mTvTaboo.setText(calendar.getTaboo());
                try {
                    ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mTvDate.getLayoutParams();
                    int maxHeight = mLayoutContentTop.getHeight() - layoutParams.topMargin - layoutParams.bottomMargin;
                    float textHeight = getTextHeight(mTvLunarDate.getPaint(), date);
                    if (textHeight > maxHeight * 2) {
                        mTvLunarDate2.setVisibility(View.GONE);
                        mTvLunarDate.setText(date);
                        return;
                    }
                    int index = getBreakingIndex(mTvLunarDate.getPaint(), date, maxHeight);
                    Log.d(TAG, "breaking index: " + index);
                    if (index == -1) {
                        mTvLunarDate.setText(date);
                        return;
                    }
                    while (index > 0 && date.charAt(index) != ' ' && date.charAt(index - 1) != ' ') {
                        index--;
                    }
                    if (index == 0) {
                        mTvLunarDate.setText(date);
                        mTvLunarDate2.setVisibility(View.GONE);
                        return;
                    }
                    String firstLine = date.substring(0, index);
                    String secondLine = date.substring(index, date.length());
                    Log.d(TAG, "first line:" + firstLine + " second line:" + secondLine);
                    mTvLunarDate.setText(firstLine.trim());
                    mTvLunarDate2.setVisibility(View.VISIBLE);
                    mTvLunarDate2.setText(secondLine.trim());
                } catch (Exception e) {
                    Logger.e(TAG, e);
                    mTvLunarDate.setText(calendar.getLunarDate());
                    mTvLunarDate2.setVisibility(View.GONE);
                }
            }
        });
    }


    /**
     * 计算换行的位置
     * @see QMUIVerticalTextView#
     * @param paint
     * @param string
     * @param maxHeight
     * @return
     */
    private int getBreakingIndex(TextPaint paint, String string, int maxHeight) {
        Log.d(TAG, "string length: " + string.length());
        int breakingIndex = -1;
        Paint.FontMetrics fontMetrics = paint.getFontMetrics();
        char[] chars = string.toCharArray();
        float height = 0;
        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            boolean isNeedRotate = !CharacterHelper.isCJKCharacter(c);
            float charHeight = isNeedRotate ? paint.measureText(chars, i, 1) : fontMetrics.descent - fontMetrics.ascent;
            height += charHeight;
            if (height > maxHeight) {
                breakingIndex = i;
                break;
            }
        }
        return breakingIndex;
    }

    /**
     * 计算文本的高度
     * @param paint
     * @param string
     * @return
     */
    private float getTextHeight(TextPaint paint, String string) {
        Paint.FontMetrics fontMetrics = paint.getFontMetrics();
        char[] chars = string.toCharArray();
        float height = 0;
        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            boolean isNeedRotate = !CharacterHelper.isCJKCharacter(c);
            float charHeight = isNeedRotate ? paint.measureText(chars, i, 1) : fontMetrics.descent - fontMetrics.ascent;
            height += charHeight;
        }
        return height;
    }
}