package com.jd.oa.business.workbench2.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.jme.common.R;

/** 工作台-我的申请容器
 * Created by peidongbiao on 2018/8/27.
 */

public class ApplyNodeLayout extends LinearLayout {
    private static final String TAG = "ApplyNodeLayout";
    private int mLineOffset;

    public ApplyNodeLayout(Context context) {
        this(context, null);
    }

    public ApplyNodeLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ApplyNodeLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ApplyNodeLayout);
        mLineOffset = typedArray.getDimensionPixelOffset(R.styleable.ApplyNodeLayout_line_offset, 0);
        typedArray.recycle();
        setWillNotDraw(false);
        setOrientation(LinearLayout.HORIZONTAL);
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        int childCount = getChildCount();
        int lineStart = 0;
        int lineEnd = 0;
        for (int i = 0; i < childCount; i++) {
            View view = getChildAt(i);
            lineStart = lineEnd;
            lineEnd = view.getLeft() + (view.getRight() - view.getLeft()) / 2;
            LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
            Drawable drawable = layoutParams.line;
            if(drawable == null) continue;
            int lineHeight = 0;
            if(layoutParams.lineHeight != -1){
                lineHeight = layoutParams.lineHeight;
            }else if(drawable.getIntrinsicHeight() != -1){
                lineHeight = drawable.getIntrinsicHeight();
            }
            drawable.setBounds(lineStart, mLineOffset, lineEnd, mLineOffset + lineHeight);
            drawable.draw(canvas);
        }
        super.onDraw(canvas);
    }

    @Override
    public LinearLayout.LayoutParams generateLayoutParams(AttributeSet attrs) {
        LayoutParams layoutParams = new LayoutParams(getContext(), attrs);
        return layoutParams;
    }

    public static class LayoutParams extends LinearLayout.LayoutParams{
        private Drawable line;
        public int lineHeight;

        public LayoutParams(Context c, AttributeSet attrs) {
            super(c, attrs);
            TypedArray typedArray = c.obtainStyledAttributes(attrs, R.styleable.ApplyNodeLayout_Layout);
            int drawableRes = typedArray.getResourceId(R.styleable.ApplyNodeLayout_Layout_layout_node_line, -1);
            lineHeight = typedArray.getDimensionPixelOffset(R.styleable.ApplyNodeLayout_Layout_layout_node_line_height, -1);
            typedArray.recycle();
            if(drawableRes != -1){
                line = ContextCompat.getDrawable(c, drawableRes);
            }
        }

        public LayoutParams(int width, int height) {
            super(width, height);
        }

        public LayoutParams(int width, int height, float weight) {
            super(width, height, weight);
        }

        public LayoutParams(ViewGroup.LayoutParams p) {
            super(p);
        }

        public LayoutParams(MarginLayoutParams source) {
            super(source);
        }

        public LayoutParams(LinearLayout.LayoutParams source) {
            super(Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT ? (MarginLayoutParams)source : source);
        }

        public void setLineDrawable(Drawable drawable){
            line = drawable;
        }
    }
}
