package com.jd.oa.business.index;

import android.util.Pair;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.subjects.BehaviorSubject;

/**
 * Created by peidongbiao on 2018/12/14
 */
public class ShowAnimationSubject {

    private static BehaviorSubject<Pair<Boolean,String>> sSubject;

    private static void ensureSubject(){
        if(sSubject == null){
            sSubject = BehaviorSubject.createDefault(Pair.create(false, ""));
        }
    }

    public static void publish(Pair<Boolean,String> pair){
        ensureSubject();
        sSubject.onNext(pair);
    }

    public static Disposable subscribe(Consumer<Pair<Boolean,String>> consumer){
        ensureSubject();
        Disposable disposable = sSubject.subscribe(consumer);
        return disposable;
    }
}
