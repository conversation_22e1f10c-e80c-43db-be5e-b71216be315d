package com.jd.oa.business.workbench.model;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

@Keep
public class AttendeesInfo implements Parcelable {
    private String mail;
    private String realName;
    private String curStatus;

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCurStatus() {
        return curStatus;
    }

    public void setCurStatus(String curStatus) {
        this.curStatus = curStatus;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.mail);
        dest.writeString(this.realName);
        dest.writeString(this.curStatus);
    }

    public AttendeesInfo() {
    }

    protected AttendeesInfo(Parcel in) {
        this.mail = in.readString();
        this.realName = in.readString();
        this.curStatus = in.readString();
    }

    public static final Parcelable.Creator<AttendeesInfo> CREATOR = new Parcelable.Creator<AttendeesInfo>() {
        @Override
        public AttendeesInfo createFromParcel(Parcel source) {
            return new AttendeesInfo(source);
        }

        @Override
        public AttendeesInfo[] newArray(int size) {
            return new AttendeesInfo[size];
        }
    };
}
