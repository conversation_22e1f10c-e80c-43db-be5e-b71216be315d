package com.jd.oa.business.setting.settingitem;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.jd.oa.badge.RedDotView;
import com.jme.common.R;

/**
 * 设置项
 * Created by gaozhanfeng on 2022-10-31.
 */

public class SettingItem2 extends LinearLayout {
    public static final int ITEM_CORNER_ALL = 0;
    public static final int ITEM_CORNER_TOP = 1;
    public static final int ITEM_CORNER_BOTTOM = 2;
    public static final int ITEM_CORNER_NONE = 3;

    private ImageView mIvIcon;
    private TextView mTvName;
    private RedDotView mBadgeView;
    private TextView mTvTips;
    private ImageView mIvArrow;
    private TextView mTvDescription;
    private View mViewDivider;
    private ViewGroup mLayoutItem;
    private ProgressBar mPbLoading;

    private String mName;
    private String mBadgeLink;
    private String mTips;
    private int mTipsColor;
    private String mDesc;
    private boolean mShowDivider;
    private int mDividerMarginStart;
    private int mDividerMarginEnd;
    private Drawable mIconDrawable;
    private boolean mShowArrow;
    private boolean mDisableClick = false;

    private OnClickListener mClickListener;
    private int mItemCorner;

    public SettingItem2(Context context) {
        this(context, null);
    }

    public SettingItem2(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SettingItem2(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setOrientation(LinearLayout.VERTICAL);
        setClickable(true);
        View.inflate(context, R.layout.jdme_widget_setting_item2, this);
        mIvIcon = findViewById(R.id.iv_icon);
        mTvName = findViewById(R.id.tv_name);
        mBadgeView = findViewById(R.id.badge);
        mBadgeView.setEnable(false);
        mTvTips = findViewById(R.id.tv_tips);
        mIvArrow = findViewById(R.id.iv_arrow);
        mTvDescription = findViewById(R.id.tv_description);
        mViewDivider = findViewById(R.id.view_divider);
        mLayoutItem = findViewById(R.id.layout_item);
        mPbLoading = findViewById(R.id.pb_loading);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SettingItem2);
        mName = typedArray.getString(R.styleable.SettingItem2_setting_name);
        mTips = typedArray.getString(R.styleable.SettingItem2_setting_tips);
        mDesc = typedArray.getString(R.styleable.SettingItem2_setting_description);
        mShowDivider = typedArray.getBoolean(R.styleable.SettingItem2_setting_show_divider, false);
        mDividerMarginStart = typedArray.getDimensionPixelOffset(R.styleable.SettingItem2_setting_divider_margin_start, 0);
        mDividerMarginEnd = typedArray.getDimensionPixelOffset(R.styleable.SettingItem2_setting_divider_margin_end, 0);
        mIconDrawable = typedArray.getDrawable(R.styleable.SettingItem2_setting_icon);
        mShowArrow = typedArray.getBoolean(R.styleable.SettingItem2_setting_show_arrow, true);
        mItemCorner = typedArray.getInteger(R.styleable.SettingItem2_setting_item_corner, 0);
        typedArray.recycle();

        setItemBackground(mItemCorner);
        if (mIconDrawable != null) {
            mIvIcon.setVisibility(VISIBLE);
            mIvIcon.setImageDrawable(mIconDrawable);
        }
        mTvName.setText(mName);
        mTvTips.setText(mTips);
        mTvDescription.setText(mDesc);
        mTvDescription.setVisibility(TextUtils.isEmpty(mDesc) ? GONE : VISIBLE);
        mViewDivider.setVisibility(mShowDivider ? VISIBLE : GONE);
        mIvArrow.setVisibility(mShowArrow ? VISIBLE : GONE);

        mLayoutItem.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClickListener != null && !mDisableClick) {
                    mClickListener.onClick(SettingItem2.this);
                }
            }
        });
    }

    public void setLoading(boolean loading) {
        mTvTips.setVisibility(loading ? GONE : VISIBLE);
        mIvArrow.setVisibility(loading ? GONE : VISIBLE);
        mPbLoading.setVisibility(loading ? VISIBLE : GONE);
    }

    public void setItemBackground(int type) {
        mLayoutItem.setBackgroundResource(getItemBackgroundRes(type));
    }

    private @DrawableRes int getItemBackgroundRes(int type) {
        mItemCorner = type;
        if (type == ITEM_CORNER_TOP) {
            return R.drawable.jdme_ripple_white_top_corner8;
        } else if (type == ITEM_CORNER_BOTTOM) {
            return R.drawable.jdme_ripple_white_bottom_corner8;
        } else if (type == ITEM_CORNER_NONE) {
            return R.drawable.jdme_ripple_white;
        } else {
            return R.drawable.jdme_ripple_white_corner8;
        }
    }

    public void setName(String name) {
        mName = name;
        mTvName.setText(mName);
    }

    /**
     * 设置红点BadgeLink
     *
     * @param badgeLink 红点link
     */
    public void setBadge(String badgeLink) {
        mBadgeLink = badgeLink;
        mBadgeView.setEnable(true);
        mBadgeView.setAppLinks(badgeLink);
    }

    public void setTips(String tips) {
        mTips = tips;
        mTvTips.setText(mTips);
    }

    public void setTips(int resId) {
        mTvTips.setText(resId);
    }

    public void setTipsColor(int resColor) {
        mTipsColor = resColor;
        mTvTips.setTextColor(resColor);
    }

    public TextView getDescriptionView() {
        mTvDescription.setVisibility(VISIBLE);
        return mTvDescription;
    }

    public void setDescription(String description) {
        mDesc = description;
        mTvDescription.setText(mDesc);
    }

    public void setIcon(@DrawableRes int icon) {
        mIconDrawable = ContextCompat.getDrawable(getContext(), icon);
        mIvIcon.setImageDrawable(mIconDrawable);
    }

    public void setShowDivider(boolean showDivider) {
        mShowDivider = showDivider;
        mViewDivider.setVisibility(mShowDivider ? VISIBLE : GONE);
    }

    public void setShowIcon(boolean showIcon) {
        mIvIcon.setVisibility(showIcon ? VISIBLE : GONE);
    }

    public ImageView getIconImage() {
        return mIvIcon;
    }

    public RedDotView getBadge() {
        return mBadgeView;
    }

    public void removeBadge() {
        mBadgeView.remove();
    }

    public int getBadgeVisibility() {
        return mBadgeView.getVisibility();
    }

    public void setOnSettingClickListener(OnClickListener listener) {
        mClickListener = listener;
    }

    public void setDisableClick(boolean disableClick) {
        this.mDisableClick = disableClick;
    }

    public boolean getDisableClick() {
        return mDisableClick;
    }
}