package com.jd.oa.business.login.model;

import com.jd.oa.MyPlatform;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.utils.ConvertUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;

/**
 * 用户实体类
 *
 * <AUTHOR>
 */
public class UserEntity {

    private String userName; // ERP账号
    private String realName; // 中文名
    private String userIcon; // 用户图片url
    private String jdAccount; // 京东账号

    private String attendance; // 是否需要打卡，T4及以上不显示打卡界面

    private String sex;

    private String tenantCode;//租户code
    private String tenantCodeList;//租户list
    private String appId; //me是ee/th.ee，saas是teamId
    private String pin; //me是erp，saas是userId

    private String birthday;  // 生日

    private String teamId; // 团队Id

    private String userId;

    private String teamName;

    private String wjLoginPin;

    public UserEntity() {

    }

    public String getAttendance() {
        return attendance;
    }

    public UserEntity(String userName, String realName, String userIcon, String jdAccount, String attendance, String sex) {
        this.userName = userName;
        this.realName = realName;
        this.userIcon = userIcon;
        this.jdAccount = jdAccount;
        this.attendance = attendance;
        this.sex = sex;
    }

    public void setAttendance(String attendance) {
        this.attendance = attendance;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    /**
     * 级别
     */
    private String levelDesc;
    private String mobile;
    /**
     * 部门
     */
    private String organizationName;
    /**
     * 岗位
     */
    private String positionName;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 加密后的erp
     */
    private String encryptErp;

    /**
     * 是否有薪资查询权限
     */
    private boolean hasSalaryAuthority;


    public String getEncryptErp() {
        if (StringUtils.isEmptyWithTrim(encryptErp)) {
            if (StringUtils.isNotEmptyWithTrim(MyPlatform.getCurrentUser().getUserName())) {
                encryptErp = JdmeEncryptUtil.getEncryptString(MyPlatform.getCurrentUser().getUserName().trim());
            }
        }

        // 刷脸登录保存的
        String userName = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME);
        if (StringUtils.isEmptyWithTrim(encryptErp) && StringUtils.isNotEmptyWithTrim(userName)) {
            encryptErp = JdmeEncryptUtil.getEncryptString(userName);
        }
        return ConvertUtils.toString(encryptErp);
    }

    public void setEncryptErp(String encryptErp) {
        this.encryptErp = encryptErp;
    }


    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getUserIcon() {
        return userIcon;
    }

    public void setUserIcon(String userIcon) {
        this.userIcon = userIcon;
    }

    public String getJdAccount() {
        return jdAccount;
    }

    public void setJdAccount(String jdAccount) {
        this.jdAccount = jdAccount;
    }

    public String getLevelDesc() {
        return levelDesc;
    }

    public void setLevelDesc(String levelDesc) {
        this.levelDesc = levelDesc;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isHasSalaryAuthority() {
        return hasSalaryAuthority;
    }

    public void setHasSalaryAuthority(boolean hasSalaryAuthority) {
        this.hasSalaryAuthority = hasSalaryAuthority;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getTenantCodeList() {
        return tenantCodeList;
    }

    public void setTenantCodeList(String tenantCodeList) {
        this.tenantCodeList = tenantCodeList;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getWjLoginPin(){
        return wjLoginPin;
    }

    public void setWjLoginPin(String wjLoginPin){
        this.wjLoginPin = wjLoginPin;
    }
}
