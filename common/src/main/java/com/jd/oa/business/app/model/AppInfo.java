package com.jd.oa.business.app.model;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.Keep;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Keep
public class AppInfo implements Parcelable {
    public static final String IS_APP_DETAIL = "0";
    // 附加字段
    public String callbackInfo;
    //appdetail接口返回的是这个字段，兼容一下
    public String callBackInfo;

    // 应用Id
    protected String appID;

    // 应用名称
    protected String appName;

    // 应用类型 1：原生 2：H5 3 APP跳转 4 sdk (废弃)\r\n 5 sdk (需token校验)； 6 sdk (无需验证)  7 WebApp类型 8 Rn应用    9Webapp',
    protected String appType;

    // 应用地址
    protected String appAddress;

    // 简介
    protected String appSubName;

    // 中文名
    protected String appCName;

    // 安卓打开应用的browser类型；1 默认 2 打开h5的新浏览器 3 webapp浏览类型
    private String anBrowserType;

    // 安卓h5|Webapp包下载地址
    private String anwebappDownloadUrl;

    // 图标
    protected String photoKey;

    // 应用下载地址
    protected String appDUrl;

    // 角标图片地址
    protected String cormarkUrl;

    // 安装数量
    protected String installCount;

    // 用户是否安装（0：未安装、1：已安装）
    protected String isInstall;

    // 是否插件
    protected String isPlugin;

    //
    protected String isCommon;

    //是否固定不可删除，员工论坛
    protected String isFixed;

    // 是否限制内网访问 是否支持内网  1：内网  2 ：外网
    protected String isInnerOnly;

    // 是否原生头 1：是，0否
    protected String isNativeHead;

    //是否请求数量角标信息 0：请求 1：不请求
    protected String isAppDetail;

    protected String modifyTime;

    // 数量角标
    private String numTip;

    // 信息角标
    protected String appMessage;

    // 版本
    protected String version;

    // DeepLink地址
    protected String deeplink;

    // 整合应用ID
    protected String interateAppID;

    //是否是应用整合的父应用;0:不是 1:是'
    protected String isInterateParentApp;

    private List<SonApp> sonAppList;

    private String isMultiTask;

    // Cookie信息
    private HashMap<String, String> cookie = new HashMap<>();

    // 传递给应用的参数
    private Map bizParam;

    // jdme:/biz/appcenter/202104120993?mparam=%7B%22browser%22%3A%221%22%2C%22isNativeHead%22%3A%220%22%2C%22url%22%3A%22https%3A%2F%2Fmgr-dashboard.jd.com%2F%23%2Fmodule-manage%22%7D
    private String mParam; //这里转化比较繁琐，直接传递mParam了%221%22%2C%22isNativeHead%22%3A%220%22%2C%22url%22%3A%22https%3A%2F%2Fmgr-dashboard.jd.com%2F%23%2Fmodule-manage%22%7D

    public String getBizParamStr() {
        return bizParamStr;
    }

    public void setBizParamStr(String bizParamStr) {
        this.bizParamStr = bizParamStr;
    }

    private String bizParamStr; //这个是h5应用专用的，传递给url的

    private String androidMinVersion;

    // 应用图标的badge（new-1  beta-2  推荐-3 专属-4 热门-5 ）
    protected String metaTag;

    public String getMetaTag() {
        return metaTag;
    }

    public void setMetaTag(String metaTag) {
        this.metaTag = metaTag;
    }


    public String getAndroidMinVersion() {
        return androidMinVersion;
    }

    public void setAndroidMinVersion(String androidMinVersion) {
        this.androidMinVersion = androidMinVersion;
    }

    public HashMap<String, String> getCookie() {
        return cookie;
    }

    public void setCookie(HashMap<String, String> cookie) {
        this.cookie = cookie;
    }

    public String getParam() {
        if (mParam != null) {
            return mParam;
        }
        if (bizParam == null) {
            return new JSONObject().toString();
        } else {
            return new JSONObject(bizParam).toString();
        }
    }

    public void setParam(Map param) {
        this.bizParam = param;
    }

    public void setParam(String mParam) {
        this.mParam = mParam;
    }


    public String getIsNativeHead() {
        return isNativeHead;
    }

    public void setIsNativeHead(String isNativeHead) {
        this.isNativeHead = isNativeHead;
    }

    public String getIsInnerOnly() {
        return isInnerOnly;
    }

    public void setIsInnerOnly(String isInnerOnly) {
        this.isInnerOnly = isInnerOnly;
    }

    public boolean isInterateParentApp() {
        return "1".equals(isInterateParentApp);
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getAppCName() {
        return appCName;
    }

    public void setAppCName(String appCName) {
        this.appCName = appCName;
    }

    public String getAppSubName() {
        return appSubName;
    }

    public void setAppSubName(String appSubName) {
        this.appSubName = appSubName;
    }

    public String getPhotoKey() {
        return photoKey;
    }

    public void setPhotoKey(String photoKey) {
        this.photoKey = photoKey;
    }

    public String getAppDUrl() {
        return appDUrl;
    }

    public void setAppDUrl(String appDUrl) {
        this.appDUrl = appDUrl;
    }

    public String getIsInstall() {
        return isInstall;
    }

    public void setIsInstall(String isInstall) {
        this.isInstall = isInstall;
    }

    public String getInstallCount() {
        return installCount;
    }

    public void setInstallCount(String installCount) {
        this.installCount = installCount;
    }

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getAppAddress() {
        return appAddress == null ? "" : appAddress;
    }

    public void setAppAddress(String appAddress) {
        this.appAddress = appAddress;
    }

    public String getIsPlugin() {
        return isPlugin;
    }

    public void setIsPlugin(String isPlugin) {
        this.isPlugin = isPlugin;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getIsAppDetail() {
        return isAppDetail;
    }

    public void setIsAppDetail(String isAppDetail) {
        this.isAppDetail = isAppDetail;
    }

    public String getNumTip() {
        return numTip;
    }

    public void setNumTip(String numTip) {
        this.numTip = numTip;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppMessage() {
        return appMessage;
    }

    public void setAppMessage(String appMessage) {
        this.appMessage = appMessage;
    }

    public String getAnwebappDownloadUrl() {
        return anwebappDownloadUrl;
    }

    public void setAnwebappDownloadUrl(String anwebappDownloadUrl) {
        this.anwebappDownloadUrl = anwebappDownloadUrl;
    }

    public String getAnBrowserType() {
        return anBrowserType;
    }

    public void setAnBrowserType(String anBrowserType) {
        this.anBrowserType = anBrowserType;
    }

    public String getCormarkUrl() {
        return cormarkUrl;
    }

    public void setCormarkUrl(String cormarkUrl) {
        this.cormarkUrl = cormarkUrl;
    }

    public String getIsCommon() {
        return isCommon;
    }

    public void setIsCommon(String isCommon) {
        this.isCommon = isCommon;
    }

    public String getIsFixed() {
        return isFixed;
    }

    public void setIsFixed(String isFixed) {
        this.isFixed = isFixed;
    }

    public void setCallBackInfo(String callBackInfo) {
        this.callBackInfo = callBackInfo;
    }

    public String getCallBackInfo() {
        return callBackInfo;
    }

    public boolean isMultiTaskApp() {
        if(TextUtils.isEmpty(isMultiTask)){
            return false;
        }
        return "1".equals(isMultiTask);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AppInfo appInfo = (AppInfo) o;

        if (getAppSubName() != null ? !getAppSubName().equals(appInfo.getAppSubName()) : appInfo.getAppSubName() != null)
            return false;
        if (getPhotoKey() != null ? !getPhotoKey().equals(appInfo.getPhotoKey()) : appInfo.getPhotoKey() != null)
            return false;
        if (getAppDUrl() != null ? !getAppDUrl().equals(appInfo.getAppDUrl()) : appInfo.getAppDUrl() != null)
            return false;
        if (getIsInstall() != null ? !getIsInstall().equals(appInfo.getIsInstall()) : appInfo.getIsInstall() != null)
            return false;
        if (getInstallCount() != null ? !getInstallCount().equals(appInfo.getInstallCount()) : appInfo.getInstallCount() != null)
            return false;
        if (getAppID() != null ? !getAppID().equals(appInfo.getAppID()) : appInfo.getAppID() != null)
            return false;
        if (getAppName() != null ? !getAppName().equals(appInfo.getAppName()) : appInfo.getAppName() != null)
            return false;
        if (getAppType() != null ? !getAppType().equals(appInfo.getAppType()) : appInfo.getAppType() != null)
            return false;
        if (getAppAddress() != null ? !getAppAddress().equals(appInfo.getAppAddress()) : appInfo.getAppAddress() != null)
            return false;
        if (getIsPlugin() != null ? !getIsPlugin().equals(appInfo.getIsPlugin()) : appInfo.getIsPlugin() != null)
            return false;
        if (getModifyTime() != null ? !getModifyTime().equals(appInfo.getModifyTime()) : appInfo.getModifyTime() != null)
            return false;
        if (getIsAppDetail() != null ? !getIsAppDetail().equals(appInfo.getIsAppDetail()) : appInfo.getIsAppDetail() != null)
            return false;
        if (getNumTip() != null ? !getNumTip().equals(appInfo.getNumTip()) : appInfo.getNumTip() != null)
            return false;
        if (getAppMessage() != null ? !getAppMessage().equals(appInfo.getAppMessage()) : appInfo.getAppMessage() != null)
            return false;
        if (getVersion() != null ? !getVersion().equals(appInfo.getVersion()) : appInfo.getVersion() != null)
            return false;
        if (getDeeplink() != null ? !getDeeplink().equals(appInfo.getDeeplink()) : appInfo.getDeeplink() != null)
            return false;
        return getAppCName() != null ? getAppCName().equals(appInfo.getAppCName()) : appInfo.getAppCName() == null;
    }

    @Override
    public int hashCode() {
        int result = getAppSubName() != null ? getAppSubName().hashCode() : 0;
        result = 31 * result + (getPhotoKey() != null ? getPhotoKey().hashCode() : 0);
        result = 31 * result + (getAppDUrl() != null ? getAppDUrl().hashCode() : 0);
        result = 31 * result + (getIsInstall() != null ? getIsInstall().hashCode() : 0);
        result = 31 * result + (getInstallCount() != null ? getInstallCount().hashCode() : 0);
        result = 31 * result + (getAppID() != null ? getAppID().hashCode() : 0);
        result = 31 * result + (getAppName() != null ? getAppName().hashCode() : 0);
        result = 31 * result + (getAppType() != null ? getAppType().hashCode() : 0);
        result = 31 * result + (getAppAddress() != null ? getAppAddress().hashCode() : 0);
        result = 31 * result + (getIsPlugin() != null ? getIsPlugin().hashCode() : 0);
        result = 31 * result + (getModifyTime() != null ? getModifyTime().hashCode() : 0);
        result = 31 * result + (getIsAppDetail() != null ? getIsAppDetail().hashCode() : 0);
        result = 31 * result + (getNumTip() != null ? getNumTip().hashCode() : 0);
        result = 31 * result + (getAppMessage() != null ? getAppMessage().hashCode() : 0);
        result = 31 * result + (getVersion() != null ? getVersion().hashCode() : 0);
        result = 31 * result + (getDeeplink() != null ? getDeeplink().hashCode() : 0);
        result = 31 * result + (getAppCName() != null ? getAppCName().hashCode() : 0);
        result = 31 * result + (getAndroidMinVersion() != null ? getAndroidMinVersion().hashCode() : 0);
        return result;
    }

    public String getInterateAppID() {
        return interateAppID;
    }

    public void setInterateAppID(String interateAppID) {
        this.interateAppID = interateAppID;
    }

    public String getIsInterateParentApp() {
        return isInterateParentApp == null ? "" : isInterateParentApp;
    }

    public void setIsInterateParentApp(String isInterateParentApp) {
        this.isInterateParentApp = isInterateParentApp;
    }

    public List<SonApp> getSonAppList() {
        return sonAppList;
    }

    public void setSonAppList(List<SonApp> sonAppList) {
        this.sonAppList = sonAppList;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.appSubName);
        dest.writeString(this.anwebappDownloadUrl);
        dest.writeString(this.anBrowserType);
        dest.writeString(this.photoKey);
        dest.writeString(this.appDUrl);
        dest.writeString(this.isInstall);
        dest.writeString(this.installCount);
        dest.writeString(this.appID);
        dest.writeString(this.appName);
        dest.writeString(this.appType);
        dest.writeString(this.appAddress);
        dest.writeString(this.isPlugin);
        dest.writeString(this.modifyTime);
        dest.writeString(this.isAppDetail);
        dest.writeString(this.numTip);
        dest.writeString(this.appMessage);
        dest.writeString(this.version);
        dest.writeString(this.deeplink);
        dest.writeString(this.appCName);
        dest.writeString(this.interateAppID);
        dest.writeString(this.isInterateParentApp);
        dest.writeList(this.sonAppList);
        dest.writeString(this.cormarkUrl);
        dest.writeString(this.isNativeHead);
        dest.writeString(this.isInnerOnly);
        dest.writeString(this.isCommon);
        dest.writeString(this.isFixed);
        dest.writeMap(this.cookie);
        dest.writeMap(this.bizParam);
        dest.writeString(this.mParam);
        dest.writeString(this.bizParamStr);
        dest.writeString(this.callbackInfo);
        dest.writeString(this.androidMinVersion);
        dest.writeString(this.metaTag);
    }

    public AppInfo() {
    }

    protected AppInfo(Parcel in) {
        this.appSubName = in.readString();
        this.anwebappDownloadUrl = in.readString();
        this.anBrowserType = in.readString();
        this.photoKey = in.readString();
        this.appDUrl = in.readString();
        this.isInstall = in.readString();
        this.installCount = in.readString();
        this.appID = in.readString();
        this.appName = in.readString();
        this.appType = in.readString();
        this.appAddress = in.readString();
        this.isPlugin = in.readString();
        this.modifyTime = in.readString();
        this.isAppDetail = in.readString();
        this.numTip = in.readString();
        this.appMessage = in.readString();
        this.version = in.readString();
        this.deeplink = in.readString();
        this.appCName = in.readString();
        this.interateAppID = in.readString();
        this.isInterateParentApp = in.readString();
        this.sonAppList = new ArrayList<SonApp>();
        in.readList(this.sonAppList, SonApp.class.getClassLoader());
        this.cormarkUrl = in.readString();
        this.isNativeHead = in.readString();
        this.isInnerOnly = in.readString();
        this.isCommon = in.readString();
        this.isFixed = in.readString();
        this.cookie = in.readHashMap(HashMap.class.getClassLoader());
        this.bizParam = in.readHashMap(HashMap.class.getClassLoader());
        this.mParam = in.readString();
        this.bizParamStr = in.readString();
        this.callbackInfo = in.readString();
        this.androidMinVersion = in.readString();
        this.metaTag = in.readString();
    }

    public static final Creator<AppInfo> CREATOR = new Creator<AppInfo>() {
        @Override
        public AppInfo createFromParcel(Parcel source) {
            return new AppInfo(source);
        }

        @Override
        public AppInfo[] newArray(int size) {
            return new AppInfo[size];
        }
    };
}
