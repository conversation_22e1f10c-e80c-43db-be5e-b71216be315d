package com.jd.oa.business.myapply.model;


import java.io.Serializable;

/**
 * 我的申请bean
 *
 * <AUTHOR>
 */
public class MyTaskApply implements Serializable {

    public static final String STATUS_DOING = "1";
    public static final String STATUS_CANCELED = "2";
    public static final String STATUS_FINISHED = "3";

    public String reqName;    // 任务名称
    public String reqTime;    // 任务时间
    public String reqId;    //申请单ID
    public String status;    // String	申请单状态
    public String statusText;    // String	申请单状态文本
    public String currentTaskAssigneerName; //	String	当前审批人
    public String currentTaskAssigneerErp;    // String	当前审批人ERP
    public String isUrge;//是否催办过：1催办过 0未催办过
    public String allowCancel;//是否允许取消："true"可以取消 "false"不可取消

    public String meViewUrl;
    public String viewType;
}
