package com.jd.oa.business.setting;

import android.content.Context;
import android.content.res.Configuration;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.Menu;
import android.view.MenuItem;

import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.utils.LocaleUtils;

import java.util.Locale;

/**
 * 字号调整工具类
 * <p>
 * 需要字号调整的Activity或者由FunctionActivity承载的Fragment，可增加FontScalable的注解，BaseActivity和FunctionActivity会处理
 *
 * <AUTHOR>
 */
public class FontScaleUtils {
    private static float[] scaleArray = new float[]{
            1, 1.2f, 1.3f, 1.6f
    };

    public static float[] getScaleArray() {
        return scaleArray;
    }

    public static float getCurrentScale() {
        return JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_SCALE);
    }


    public static void saveScale(float currentScale) {
        JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_JDME_FONT_SCALE,currentScale);
    }

    public static int getScaleIndex() {
        float currentScale = getCurrentScale();
        for (int i = 0; i < scaleArray.length; i++) {
            if (currentScale == scaleArray[i]) {
                return i;
            }
        }
        return 0;
    }

    public static void fixOptionMenuTextSizeInFragment(Menu menu) {
        for (int i = 0; i < menu.size(); i++) {
            MenuItem item = menu.getItem(i);
            SpannableString spanString = new SpannableString(menu.getItem(i).getTitle().toString());
            int end = spanString.length();
            spanString.setSpan(new AbsoluteSizeSpan(16, true), 0, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            item.setTitle(spanString);
        }
    }

    public static boolean resetFontScaleWhenSystemLanguage(Context context, Configuration configuration) {
        try {
            // 本地有设置语言不跟随
            if (LocaleUtils.getUserSetLocale(context) != null) {
                return false;
            }
            Locale newLocale = LocaleUtils.getLocaleByConfig(configuration);
            if (newLocale != null && TextUtils.equals(newLocale.getLanguage(), "en")) {
                FontScaleUtils.saveScale(1);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
