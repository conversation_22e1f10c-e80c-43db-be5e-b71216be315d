package com.jd.oa.business.mine;

import com.jd.oa.AppBase;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jme.common.R;
import com.jd.oa.melib.reponse.Response;


import java.util.List;

/**
 * Created by h<PERSON><PERSON> on 2016/7/12
 */
public abstract class AbsReqCallback<T>  {
    protected Class<T> clazz;

    public void onLoading(long total, long current, boolean isUploading) {
    }

    public void onCancelled() {
    }

    public void onStart() {
    }

    /**
     * {@link #onFailure(String, int)}时错误代码
     */
    public interface ErrorCode {
        /**
         * 网络错误，如无网络之类
         */
        int CODE_NET_ERROR = 100;
        /**
         * 服务器响应超时
         */
        int CODE_SERVER_TIMEOUT = 200;
        /**
         * 服务器内部错误
         */
        int CODE_SERVER_INTERNET_ERROR = 300;
        /**
         * 找不到接口
         */
        int CODE_NO_INTERFACE = 400;
        /**
         * 解析时出错
         */
        int CODE_PARSE_ERROR = 500;
        /**
         * 服务器返回的数据中errorCode是0
         */
        int CODE_RETURN_ERROR = 600;
        /**
         * 其它错误
         */
        int CODE_OTHER_ERROR = 700;
    }

    protected AbsReqCallback(Class<T> clazz) {
        this.clazz = clazz;
    }


    public final void onFailure(HttpException exception, String s) {
        if (exception == null) {
            return;
        }
        int code;
        int statusCode = exception.getExceptionCode();
        int msgRes;
        switch (statusCode) {
            case -100:
                msgRes = R.string.me_no_network;
                code = ErrorCode.CODE_NET_ERROR;
                break;
            case 0: // 服务器响应超时
                msgRes = R.string.me_server_time_out;
                code = ErrorCode.CODE_SERVER_TIMEOUT;
                break;
            case 500:
                msgRes = R.string.me_server_error;
                code = ErrorCode.CODE_SERVER_INTERNET_ERROR;
                break;
            case 404:
                msgRes = R.string.me_server_not_found;
                code = ErrorCode.CODE_NO_INTERFACE;
                break;
            default:
                code = ErrorCode.CODE_OTHER_ERROR;
                msgRes = R.string.me_request_fail;
                break;
        }
        onFailure(AppBase.getAppContext().getResources().getString(msgRes));
        onFailure(AppBase.getAppContext().getResources().getString(msgRes), code);
    }

    /**
     * 服务端返回错误，以及接口中返回错误都会走该回调。<br/>
     * 有以下三种情况会走该回调:
     * <ol>
     * <li>无网络，服务器返回404等错误时，此时msg由客户端指定</li
     * <li>服务器正常返回，但code为1。</li>
     * <li>解析数据出现错误也走该回调</li>
     * </ol>
     *
     * @param errorMsg：返回错误时的提示信息
     */
    public void onFailure(String errorMsg) {
        //empty
    }

    /**
     * @param code 参考{@link ErrorCode}
     */
    public void onFailure(String errorMsg, int code) {
        //empty
    }


    public void onSuccess(ResponseInfo<String> responseInfo) {
        try {

//            Logger.e("server", responseInfo.result);
            Response<T> response = Response.getResponse(clazz, responseInfo.result);
            T obj = response.getDataObj();
            List<T> list = response.getDataList();
            if (response.isOk()) {
                onSuccess(obj, list);
                onSuccess(obj, list, responseInfo.result);
            } else {
                onFailure(response.getMessage());
                onFailure(response.getMessage(), ErrorCode.CODE_RETURN_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            onFailure(AppBase.getAppContext().getString(R.string.me_data_parse_error));
            onFailure(AppBase.getAppContext().getString(R.string.me_data_parse_error), ErrorCode.CODE_PARSE_ERROR);
        }
    }

    /**
     * @param rawData 后台返回的原始数据
     */
    protected void onSuccess(T t, List<T> tArray, String rawData) {
        //empty
    }

    /**
     * 已解析过后的回调。若想拿到后端返回的数据，可重写{@link #onSuccess(Object, List, String)}
     *
     * @param t：如果返回的是一个Object，t不为null，而tArray为null。
     * @param tArray：如果返回的是一个Array,tArray不为null，t为null
     */
    protected void onSuccess(T t, List<T> tArray) {
    }

}
