package com.jd.oa.business.didi;

import com.jd.oa.AppBase;
import com.jd.oa.db.greendao.DidiOrderDB;
import com.jd.oa.db.greendao.DidiOrderDBDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.List;

public class DidiOrderListDaoHelper {

    public static void insertData(DidiOrderDB db) {
        getDao().insert(db);
    }

    public static List<DidiOrderDB> loadAllData() {
        return getDao().loadAll();
    }

    public static void deleteAll() {
        getDao().deleteAll();
    }

    public static void updateData(List<DidiOrderDB> list) {
        if (list != null && list.size() > 0) {
            getDao().insertInTx(list);
        }
    }

    private static DidiOrderDBDao getDao() {
        return YxDatabaseSession.getDidiOrderDBDao(AppBase.getAppContext());
    }

}
