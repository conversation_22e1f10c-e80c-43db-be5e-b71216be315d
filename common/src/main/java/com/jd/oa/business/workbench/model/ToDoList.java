package com.jd.oa.business.workbench.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;

/**
 * Created by <PERSON> on 2017/8/24.
 */

public class ToDoList implements Parcelable {
    private ArrayList<ToDo> todoList;
    private String mailPwdError;
    private String placeholderMsg;
    private ArrayList<ToDo> cancelList;
    private String dateStamp;

    public String getPlaceholderMsg() {
        return placeholderMsg;
    }

    public void setPlaceholderMsg(String placeholderMsg) {
        this.placeholderMsg = placeholderMsg;
    }

    public static Creator<ToDoList> getCREATOR() {
        return CREATOR;
    }

    public ArrayList<ToDo> getTodoList() {
        return todoList;
    }

    public void setTodoList(ArrayList<ToDo> todoList) {
        this.todoList = todoList;
    }

    public String getMailPwdError() {
        return mailPwdError;
    }

    public boolean isMaillPwdError() {
        return "1".equals(mailPwdError);
    }

    public void setMailPwdError(String mailPwdError) {
        this.mailPwdError = mailPwdError;
    }


    public ArrayList<ToDo> getCancelList() {
        return cancelList;
    }

    public void setCancelList(ArrayList<ToDo> cancelList) {
        this.cancelList = cancelList;
    }

    public String getDateStamp() {
        return dateStamp;
    }

    public void setDateStamp(String dateStamp) {
        this.dateStamp = dateStamp;
    }

    public ToDoList() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.todoList);
        dest.writeString(this.mailPwdError);
        dest.writeString(this.placeholderMsg);
        dest.writeTypedList(this.cancelList);
    }

    protected ToDoList(Parcel in) {
        this.todoList = in.createTypedArrayList(ToDo.CREATOR);
        this.mailPwdError = in.readString();
        this.placeholderMsg = in.readString();
        this.cancelList = in.createTypedArrayList(ToDo.CREATOR);
    }

    public static final Creator<ToDoList> CREATOR = new Creator<ToDoList>() {
        @Override
        public ToDoList createFromParcel(Parcel source) {
            return new ToDoList(source);
        }

        @Override
        public ToDoList[] newArray(int size) {
            return new ToDoList[size];
        }
    };
}
