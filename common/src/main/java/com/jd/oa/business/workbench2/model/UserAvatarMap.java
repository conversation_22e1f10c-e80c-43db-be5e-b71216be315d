package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

@Keep
public class UserAvatarMap {

    @SerializedName("userName")
    private String user;
    @SerializedName("headImage")
    private String avatar;

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
}