package com.jd.oa.business.workbench.timingtask;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Parcel;

import com.jd.oa.business.workbench.model.ToDo;
//import com.jd.oa.business.workbench.schedule.ScheduleDetailActivity;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.cache.LruCacheConfig;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.cache.Logger;
import com.jd.oa.utils.NotificationUtils;
import com.jme.common.R;

import java.util.UUID;

/**
 * 待办的提醒定时任务
 * Created by <PERSON> on 2017/8/17.
 */
public class ScheduleNotifyTimingTask extends TimingTask {

    private ToDo mToDo;

    private static final String SCHEDULE_CHANNEL_ID = "me_schedule";
    private static final String SCHEDULE_CHANNEL_NAME = "schedule";


    public ScheduleNotifyTimingTask(ToDo toDo) {
        mToDo = toDo;
    }

    @Override
    public void executor(Context context, Intent intent) {
        Logger.save("ScheduleNotifyTimingTask executor");

        if (mToDo == null) {
            return;
        }
        String key = UUID.randomUUID().toString();
        LruCacheConfig.getInstance().addObjReference(key, mToDo);


        //跳转到RN日程
        Intent resultIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(DeepLink.rnOld("************", "routeTag=detail&taskId="
                + mToDo.getCode() + "&date=" + mToDo.getStartTime() + "&cacheKey=" + key)));
        PendingIntent resultPendingIntent =
                PendingIntent.getActivity(context,
                        getRequestCode(),
                        resultIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
        Logger.save("ScheduleNotifyTimingTask mToDo = " + mToDo.toString());
        boolean needNotify = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_USER_WORKBENCH_MAIL_RECEIVE);
        if (mToDo != null) {
            // 设置中 会议提醒开关关闭就不提醒了
            if (mToDo.isEmail() && !needNotify) {
                return;
            }
            // 写入alarmmanager时 和 当前登录的用户名字不一致就不提醒
            if (!PreferenceManager.UserInfo.getUserName().equalsIgnoreCase(mToDo.getUserName())) {
                return;
            }
            NotificationUtils.sendNotificaiton(context, getRequestCode(), context.getString(R.string.me_workbench_todo_notify_title), context.getString(R.string.me_workbench_schedule_notify_pre) + mToDo.getContent(), resultPendingIntent,NotificationUtils.CHANNEL_ID_OTHER,NotificationUtils.CHANNEL_NAME_OTHER);
        }

    }

    public ToDo getToDo() {
        return mToDo;
    }

    public void setToDo(ToDo toDo) {
        mToDo = toDo;
    }

    @Override
    public int getTimingTaskType() {
        return TimingTask.TASK_TYPE_TASK_NOTITFY;
    }

    @Override
    public int getTimingTaskId() {
        if (mToDo != null) {
            try {
                if (mToDo.getTodoType().equals(ToDo.TODO_TYPE_MAIL_FLAG)) {
                    String unicodeStr = mToDo.getStartTime() + mToDo.getContent();
                    return unicodeStr.hashCode();
                } else {
                    return Integer.parseInt(mToDo.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(this.mToDo, flags);
    }

    public ScheduleNotifyTimingTask() {
    }

    protected ScheduleNotifyTimingTask(Parcel in) {
        this.mToDo = in.readParcelable(ToDo.class.getClassLoader());
    }

    public static final Creator<ScheduleNotifyTimingTask> CREATOR = new Creator<ScheduleNotifyTimingTask>() {
        @Override
        public ScheduleNotifyTimingTask createFromParcel(Parcel source) {
            return new ScheduleNotifyTimingTask(source);
        }

        @Override
        public ScheduleNotifyTimingTask[] newArray(int size) {
            return new ScheduleNotifyTimingTask[size];
        }
    };
}
