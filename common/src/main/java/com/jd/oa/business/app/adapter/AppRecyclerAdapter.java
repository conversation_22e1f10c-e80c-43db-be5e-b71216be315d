package com.jd.oa.business.app.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jme.common.R;

import java.util.List;

/**
 * Created by peidongbiao on 2018/8/10.
 */

public class AppRecyclerAdapter extends BaseRecyclerAdapter<AppInfo, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;
    private OnItemLongClickListener mOnItemLongClickListener;
    private boolean mShowMore = false;

    public AppRecyclerAdapter(Context context) {
        super(context);
    }

    public AppRecyclerAdapter(Context context, List<AppInfo> data) {
        super(context, data);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_app, viewGroup, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ViewHolder holder = (ViewHolder) viewHolder;
        if (isMore(i)) {
            holder.badge.setVisibility(View.GONE);
            holder.name.setText(R.string.me_app_more);
            holder.image.setImageResource(R.drawable.jdme_icon_app_more);
        } else {
            AppInfo info = getItem(i);
            RequestOptions options = new RequestOptions()
                    .transform(new RoundedCorners(6))//补充工作台图标圆角
                    .placeholder(R.drawable.jdme_ic_app_default);
            Glide.with(getContext()).load(info.getPhotoKey()).apply(options).into(holder.image);
//            if (!TextUtils.isEmpty(info.getNumTip())) {
//                holder.tips.setVisibility(View.VISIBLE);
//                holder.tips.setText(info.getNumTip());
//            } else if (!TextUtils.isEmpty(info.getAppMessage())) {
//                holder.tips.setVisibility(View.VISIBLE);
//                holder.tips.setText(info.getAppMessage());
//            } else {
//                holder.tips.setVisibility(View.GONE);
//            }
            setIconBadge(holder.badge, info);

            holder.name.setText(info.getAppName());
        }
    }

    public static void setIconBadge(ImageView badge, AppInfo info) {
//        int r = Random.Default.nextInt() % 7;
        String metaTag = info.getMetaTag();
//        if (r >= 1 && r <= 5) {
//            metaTag = String.valueOf(r);
//        }
        if (!TextUtils.isEmpty(metaTag)) {
            badge.setVisibility(View.VISIBLE);
            switch (metaTag) {
                case "1":
                    badge.setImageResource(R.drawable.jdme_app_badge_new);
                    break;
                case "2":
                    badge.setImageResource(R.drawable.jdme_app_badge_beta);
                    break;
                case "3":
                    badge.setImageResource(R.drawable.jdme_app_badge_commend);
                    break;
                case "4":
                    badge.setImageResource(R.drawable.jdme_app_badge_special);
                    break;
                case "5":
                    badge.setImageResource(R.drawable.jdme_app_badge_hot);
                    break;
                default:
                    badge.setVisibility(View.GONE);
                    break;
            }
        } else {
            badge.setVisibility(View.GONE);
        }
    }


    public static void setAppTag(TextView tvTag, AppInfo info) {
        if (tvTag == null || info == null) return;
        String metaTag = info.getMetaTag();
        if (!TextUtils.isEmpty(metaTag)) {
            tvTag.setVisibility(View.VISIBLE);
            switch (metaTag) {
                case "1":
                    tvTag.setText(R.string.me_web_tag_new);
                    tvTag.setTextColor(Color.parseColor("#FE3E33"));
                    tvTag.setBackgroundResource(R.drawable.jdme_bg_app_tag_red);
                    break;
                case "2":
                    tvTag.setText(R.string.me_web_tag_beta);
                    tvTag.setTextColor(Color.parseColor("#999999"));
                    tvTag.setBackgroundResource(R.drawable.jdme_bg_app_tag_gray);
                    break;
                case "3":
                    tvTag.setText(R.string.me_web_tag_recommend);
                    tvTag.setTextColor(Color.parseColor("#FE3E33"));
                    tvTag.setBackgroundResource(R.drawable.jdme_bg_app_tag_red);
                    break;
                case "4":
                    tvTag.setText(R.string.me_web_tag_private);
                    tvTag.setTextColor(Color.parseColor("#4C7CFF"));
                    tvTag.setBackgroundResource(R.drawable.jdme_bg_app_tag_blue);
                    break;
                case "5":
                    tvTag.setText(R.string.me_web_tag_hot);
                    tvTag.setTextColor(Color.parseColor("#FE3E33"));
                    tvTag.setBackgroundResource(R.drawable.jdme_bg_app_tag_red);
                    break;
                default:
                    tvTag.setVisibility(View.GONE);
                    break;
            }
        } else {
            tvTag.setVisibility(View.GONE);
        }
    }

    public boolean isMore(int position) {
        if (!mShowMore) {
            return false;
        } else if (position == getItemCount() - 1) {
            return true;
        } else {
            return false;
        }
    }

    public void updateTips(List<AppTips> tipsList) {
        List<AppInfo> list = getData();
        boolean updated = false;
        for (int i = 0; i < list.size(); i++) {
            AppInfo info = list.get(i);
            for (int j = 0; j < tipsList.size(); j++) {
                AppTips tips = tipsList.get(j);
                if (tips.getAppId().equals(info.getAppID())) {
                    info.setNumTip(tips.getCount());
                    updated = true;
                }
            }
        }
        if (updated) {
            this.notifyDataSetChanged();
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setOnItemLongClickListener(OnItemLongClickListener onItemLongClickListener) {
        mOnItemLongClickListener = onItemLongClickListener;
    }

    public void setShowMore(boolean showMore) {
        mShowMore = showMore;
    }

    @Override
    public int getItemCount() {
        return mShowMore ? super.getItemCount() + 1 : super.getItemCount();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        //ViewGroup container;
        ImageView image;
        TextView name;
        ImageView badge;

        public ViewHolder(View itemView) {
            super(itemView);
            //container = itemView.findViewById(R.id.layout_container);
            image = itemView.findViewById(R.id.iv_image);
            name = itemView.findViewById(R.id.tv_name);
            badge = itemView.findViewById(R.id.app_icon_badge);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        int position = getAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) {
                            return;
                        }
                        mOnItemClickListener.onItemClick(AppRecyclerAdapter.this, v, position);
                    }
                }
            });

            itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    if (mOnItemLongClickListener != null) {
                        int position = getAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) {
                            return false;
                        }
                        return mOnItemLongClickListener.onItemLongClick(AppRecyclerAdapter.this, v, position);
                    }
                    return false;
                }
            });
        }
    }
}
