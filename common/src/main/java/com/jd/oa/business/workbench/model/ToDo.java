package com.jd.oa.business.workbench.model;

import android.content.Context;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务
 * Created by <PERSON> on 2017/8/17.
 */

public class ToDo implements Parcelable {

    private static final String COMPLETEED_FLAG = "1";
    private static final String EXEED_FLAG = "1";
    private static final String DELAY_FLAG = "1";
    private static final String TOP_FLAG = "1";
    public static final String TODO_TYPE_CUSTOM_FLAG = "1";
    public static final String TODO_TYPE_SYSTEM_FLAG = "3";
    public static final String TODO_TYPE_MAIL_FLAG = "2";
    public static final String TODO_TYPE_APP_FLAG = "4";

    //注意邮件没有id！！！！
    private String id;
    private String code;
    private String content;//todotype为 mail时 content表示邮件的标题
    private String todoType;
    private String startTime;
    private String endTime;
    private String remindTime;
    private String appAddress;
    private String isTop;
    private String isCompleteed;
    private String isExceed;
    private ToDoAppData appData;
    private String body;//邮件会议的body
    private String organizer;//会议组织者
    private String location;//会议地点
    private ArrayList<String> attendees;//会议参加者待确定可能是数组
    private String userName;
    private String deepLink;
    private String buttonName;
    @SerializedName("isAdd")
    private String isCardAdded;
    private String isDeleted;
    private String fromMail;
    private List<InviteModel> invitee;
    private String creatorRealName;     //创建人真实姓名
    private String creator;           //创建人erp
    private int total;//邀请人总数
    private int isInvited;      // 1已经被添加，0是未添加
    private String meetingLoc; //新版日程增加的地点字段

    private ArrayList<AttendeesInfo> attendeesInfo;


    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public ArrayList<String> getAttendees() {
        return attendees;
    }

    public void setAttendees(ArrayList<String> attendees) {
        this.attendees = attendees;
    }

    public static String getCompleteedFlag() {
        return COMPLETEED_FLAG;
    }

    public String getOrganizer() {
        return organizer;
    }

    public void setOrganizer(String organizer) {
        this.organizer = organizer;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public ToDoAppData getAppData() {
        return appData;
    }

    public void setAppData(ToDoAppData appData) {
        this.appData = appData;
    }

    public String getIsExceed() {
        return isExceed;
    }

    public void setIsExceed(String isExceed) {
        this.isExceed = isExceed;
    }

    public boolean isExceed() {
        return TextUtils.equals(EXEED_FLAG, isExceed);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTodoType() {
        return todoType;
    }

    public void setTodoType(String todoType) {
        this.todoType = todoType;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getRemindTime() {
        return remindTime;
    }

    public void setRemindTime(String remindTime) {
        this.remindTime = remindTime;
    }

    public String getAppAddress() {
        return appAddress;
    }

    public void setAppAddress(String appAddress) {
        this.appAddress = appAddress;
    }

    public String getIsTop() {
        return isTop;
    }

    public boolean isTop() {
        return TextUtils.equals(TOP_FLAG, isTop);
    }

    public void setIsTop(String isTop) {
        this.isTop = isTop;
    }

    public String getIsCompleteed() {
        return isCompleteed;
    }

    public boolean isCompleteed() {
        return TextUtils.equals(COMPLETEED_FLAG, isCompleteed);
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    /**
     * 是否自己创建的任务
     *
     * @return
     */
    public boolean isCustom() {
        return TextUtils.equals(TODO_TYPE_CUSTOM_FLAG, todoType);
    }

    public boolean isSystem() {
        return TextUtils.equals(TODO_TYPE_SYSTEM_FLAG, todoType);
    }

    public boolean isFromApp() {
        return TextUtils.equals(ToDo.TODO_TYPE_APP_FLAG, todoType);
    }

    public boolean isEmail() {
        return TextUtils.equals(TODO_TYPE_MAIL_FLAG, todoType);
    }

    public String getDeepLink() {
        return deepLink;
    }

    public void setDeepLink(String deepLink) {
        this.deepLink = deepLink;
    }

    public String getButtonName() {
        return buttonName;
    }

    public void setButtonName(String buttonName) {
        this.buttonName = buttonName;
    }

    public void setCardAdded(boolean added) {
        isCardAdded = added ? "1" : "0";
    }

    public boolean isCardAdded() {
        return "1".equals(isCardAdded);
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public boolean isDeleted() {
        return "1".equals(isDeleted);
    }

    public String getFromMail() {
        return fromMail;
    }

    public boolean isFromMail() {
        return "1".equals(fromMail);
    }

    public List<InviteModel> getInvitee() {
        return invitee;
    }

    public void setInvitee(List<InviteModel> invitee) {
        this.invitee = invitee;
    }

    public String getActionTitle(Context context) {
        String finish = context.getString(R.string.me_todo_list_finish_btn);
        if (TextUtils.isEmpty(todoType)) {
            return finish;
        }
        switch (todoType) {
            case TODO_TYPE_SYSTEM_FLAG:
                return getSystemActionTitle(context);
            default:
                return finish;
        }
    }

    private String getSystemActionTitle(Context context) {
        String finish = context.getString(R.string.me_todo_list_finish_btn);
        if (appData != null) {
            if (TextUtils.equals(appData.getAppAddress(), ToDoAppData.MEET_BOOK)) {
                if (TextUtils.equals(appData.getIsSignIn(), ToDoAppData.MEET_BOOK_SINGIN)) {
                    return context.getString(R.string.me_workbench_todo_meeting_sigin);
                } else {
                    return context.getString(R.string.me_workbench_todo_meeting_subscribe);
                }
            } else if (TextUtils.equals(appData.getAppAddress(), ToDoAppData.TODO_TASK)) {
                return context.getString(R.string.me_workbench_todo_task);
            }
        }
        return finish;
    }

    public long getRemindTimeInMillis() {
        if (!TextUtils.isEmpty(remindTime)) {
            try {
                if (remindTime.equals("-0") && !TextUtils.isEmpty(startTime)) {
                    return Long.parseLong(startTime);
                }
                int remindTimeInt = Integer.parseInt(remindTime);
                if (remindTimeInt != 0 && !TextUtils.isEmpty(startTime)) {
                    //remindTime是负值
                    return Long.parseLong(startTime) + remindTimeInt * 60 * 1000;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return -1;
    }

    public boolean isDelay() {
        return TextUtils.equals(DELAY_FLAG, isExceed);
    }

    public String formatContent() {
        if (content != null) {
            if (content.length() > 15) {
                return content.substring(0, 14) + "…";
            } else {
                return content;
            }
        } else {
            return "";
        }
    }


    public ToDo() {
    }

    public String getCreatorRealName() {
        return creatorRealName;
    }

    public void setCreatorRealName(String creatorRealName) {
        this.creatorRealName = creatorRealName;
    }


    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getIsInvited() {
        return isInvited;
    }

    public void setIsInvited(int isInvited) {
        this.isInvited = isInvited;
    }

    public ArrayList<AttendeesInfo> getAttendeesInfo() {
        return attendeesInfo;
    }

    public void setAttendeesInfo(ArrayList<AttendeesInfo> attendeesInfo) {
        this.attendeesInfo = attendeesInfo;
    }

    public String getMeetingLoc() {
        return meetingLoc;
    }

    public void setMeetingLoc(String meetingLoc) {
        this.meetingLoc = meetingLoc;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.id);
        dest.writeString(this.code);
        dest.writeString(this.content);
        dest.writeString(this.todoType);
        dest.writeString(this.startTime);
        dest.writeString(this.endTime);
        dest.writeString(this.remindTime);
        dest.writeString(this.appAddress);
        dest.writeString(this.isTop);
        dest.writeString(this.isCompleteed);
        dest.writeString(this.isExceed);
        dest.writeParcelable(this.appData, flags);
        dest.writeString(this.body);
        dest.writeString(this.organizer);
        dest.writeString(this.location);
        dest.writeStringList(this.attendees);
        dest.writeString(this.userName);
        dest.writeString(this.deepLink);
        dest.writeString(this.buttonName);
        dest.writeString(this.isCardAdded);
        dest.writeString(this.isDeleted);
        dest.writeString(this.fromMail);
        dest.writeTypedList(this.invitee);
        dest.writeString(this.creatorRealName);
        dest.writeString(this.creator);
        dest.writeInt(this.total);
        dest.writeInt(this.isInvited);
        dest.writeString(this.meetingLoc);
        dest.writeTypedList(this.attendeesInfo);
    }

    protected ToDo(Parcel in) {
        this.id = in.readString();
        this.code = in.readString();
        this.content = in.readString();
        this.todoType = in.readString();
        this.startTime = in.readString();
        this.endTime = in.readString();
        this.remindTime = in.readString();
        this.appAddress = in.readString();
        this.isTop = in.readString();
        this.isCompleteed = in.readString();
        this.isExceed = in.readString();
        this.appData = in.readParcelable(ToDoAppData.class.getClassLoader());
        this.body = in.readString();
        this.organizer = in.readString();
        this.location = in.readString();
        this.attendees = in.createStringArrayList();
        this.userName = in.readString();
        this.deepLink = in.readString();
        this.buttonName = in.readString();
        this.isCardAdded = in.readString();
        this.isDeleted = in.readString();
        this.fromMail = in.readString();
        this.invitee = in.createTypedArrayList(InviteModel.CREATOR);
        this.creatorRealName = in.readString();
        this.creator = in.readString();
        this.total = in.readInt();
        this.isInvited = in.readInt();
        this.meetingLoc = in.readString();
        this.attendeesInfo = in.createTypedArrayList(AttendeesInfo.CREATOR);
    }

    public static final Creator<ToDo> CREATOR = new Creator<ToDo>() {
        @Override
        public ToDo createFromParcel(Parcel source) {
            return new ToDo(source);
        }

        @Override
        public ToDo[] newArray(int size) {
            return new ToDo[size];
        }
    };
}