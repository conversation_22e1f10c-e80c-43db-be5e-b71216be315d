package com.jd.oa.business.travel.modle;

import com.jd.oa.AppBase;
import com.jd.oa.db.greendao.TravelSearchHistoryDB;
import com.jd.oa.db.greendao.TravelSearchHistoryDBDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.List;

import de.greenrobot.dao.query.Query;

public class TravelSearchHistoryDaoHelper {

    public static void insertData(TravelSearchHistoryDB db) {
        Query query = getDao().queryBuilder().where(TravelSearchHistoryDBDao.Properties.SerchContent.eq(db.getSerchContent())).build();
        getDao().deleteInTx(query.list());
        getDao().insertInTx(db);
    }

    public static List<TravelSearchHistoryDB> loadAllData() {
        Query query = getDao().queryBuilder().orderDesc(TravelSearchHistoryDBDao.Properties.Id).limit(10).build();
        List<TravelSearchHistoryDB> listDb = query.list();
        return listDb;
    }

    public static void deleleAll() {
        getDao().deleteAll();
    }

    private static TravelSearchHistoryDBDao getDao() {
        return YxDatabaseSession.getInstance(AppBase.getAppContext()).getTravelSearchHistoryDBDao();
    }
}
