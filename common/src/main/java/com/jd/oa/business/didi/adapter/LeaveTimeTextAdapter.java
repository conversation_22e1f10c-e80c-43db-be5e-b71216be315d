package com.jd.oa.business.didi.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.ui.wheel.adapter.AbstractWheelTextAdapter;
import com.jme.common.R;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/1/15.
 */
public class LeaveTimeTextAdapter extends AbstractWheelTextAdapter {
    List<String> list;

    public LeaveTimeTextAdapter(Context context, List<String> list, int currentItem, int maxsize, int minsize) {
        super(context, R.layout.jdme_item_reserve, NO_RESOURCE, currentItem, maxsize, minsize);
        this.list = list;
        setItemTextResource(R.id.tv_val);
    }

    @Override
    public View getItem(int index, View cachedView, ViewGroup parent) {
        View view = super.getItem(index, cachedView, parent);
        return view;
    }

    @Override
    public int getItemsCount() {
        return list.size();
    }

    @Override
    public CharSequence getItemText(int index) {
        if(list == null || index > list.size() - 1) {      // indexOfBounds
            return "";
        }

        return list.get(index) + "";
    }
}