package com.jd.oa.business.setting;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import androidx.fragment.app.DialogFragment;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jme.common.R;

//Created by zhangjie14 on 2016/3/11.
public class VersionUpdateFragment extends DialogFragment {
    private final String TAG = "VersionUpdateFragment";
    private static final String ARG_TITLE = "arg_title";
    private static final String ARG_MESSAGE = "arg_message";
    private static final String ARG_CONFIRM = "arg_confirm";
    private static final String ARG_SHOW_CANCEL = "arg_show_cancel";

    private Button mBtnConfirm;
    private DialogClickListener mListener;

    private String mConfirmText;

    public static VersionUpdateFragment newInstance(String title,
                                                    String message,
                                                    boolean showCancel,
                                                    DialogClickListener listener) {
        return newInstance(title, message, null, showCancel, listener);
    }

    /**
     * @param title      标题资源，-1不显示标题，0显示默认标题（应用名称）
     * @param message    提示资源
     * @param showCancel false，只有一个按钮， true 2个按钮，一个确认，一个取消
     * @return
     */
    public static VersionUpdateFragment newInstance(String title,
                                                    String message,
                                                    String confirm,
                                                    boolean showCancel,
                                                    DialogClickListener listener) {
        VersionUpdateFragment fragment = new VersionUpdateFragment();
        Bundle args = new Bundle();
        args.putString(ARG_TITLE, title);
        args.putString(ARG_MESSAGE, message);
        args.putString(ARG_CONFIRM, confirm);
        args.putBoolean(ARG_SHOW_CANCEL, showCancel);
        fragment.setArguments(args);
        fragment.mListener = listener;
        fragment.setCancelable(false);
        return fragment;
    }

    @Override
    public Dialog onCreateDialog(Bundle saveInstanceState) {
        String title = getArguments().getString(ARG_TITLE);
        String message = getArguments().getString(ARG_MESSAGE);
        Boolean showCancel = getArguments().getBoolean(ARG_SHOW_CANCEL);
        mConfirmText = getArguments().getString(ARG_CONFIRM);

        final Dialog dialog = new Dialog(getActivity(), R.style.me_ShareDialogStyle);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(R.color.transparent);
        }
        View view = LayoutInflater.from(getActivity()).inflate(R.layout.jdme_fragment_update_dialog, null, false);
        TextView tvTitle = view.findViewById(R.id.tv_update_subject);
        if (TextUtils.isEmpty(title)) {
            tvTitle.setVisibility(View.GONE);
        } else {
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(title);
        }

        TextView tvMessage = view.findViewById(R.id.tv_update_summary);
        if (!TextUtils.isEmpty(message)) {
            tvMessage.setText(message);
        }
        View cancel = view.findViewById(R.id.iv_update_cancel);

        dialog.setCancelable(showCancel);
        cancel.setVisibility(showCancel ? View.VISIBLE : View.GONE);

        mBtnConfirm = view.findViewById(R.id.btn_update_ok);
        if (!TextUtils.isEmpty(mConfirmText)) {
            mBtnConfirm.setText(mConfirmText);
        }
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_AUP, TAG + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_AUP, TAG + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
                if (mListener != null) {
                    mListener.doPositiveClick();
                }
                MELogUtil.localI(MELogUtil.TAG_AUP, TAG + " click update_ok button");
                MELogUtil.onlineI(MELogUtil.TAG_AUP, TAG + " click update_ok button");
            }
        });

        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_AUP, TAG + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_AUP, TAG + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
                if (mListener != null) {
                    mListener.doNegativeClick();
                }
                MELogUtil.localI(MELogUtil.TAG_AUP, TAG + " click cancel button");
                MELogUtil.onlineI(MELogUtil.TAG_AUP, TAG + " click cancel button");
            }
        });
        dialog.setContentView(view);
        MELogUtil.localI(MELogUtil.TAG_AUP, TAG + " onCreateDialog success " + getArguments().toString());
        MELogUtil.onlineI(MELogUtil.TAG_AUP, TAG + " onCreateDialog success " + getArguments().toString());
        return dialog;
    }

    public interface DialogClickListener {
        void doNegativeClick();

        void doPositiveClick();
    }
}