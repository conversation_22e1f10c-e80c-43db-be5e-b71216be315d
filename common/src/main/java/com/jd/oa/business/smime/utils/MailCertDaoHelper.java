package com.jd.oa.business.smime.utils;

import com.jd.oa.AppBase;
import com.jd.oa.db.greendao.MailCert;
import com.jd.oa.db.greendao.MailCertDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.List;

import de.greenrobot.dao.query.Query;

// 邮件证书
public class MailCertDaoHelper {

    public static void insertData(MailCert db) {
        Query query = getDao().queryBuilder().where(MailCertDao.Properties.User.eq(db.getUser())).where(MailCertDao.Properties.Type.eq(db.getType())).build();
        getDao().deleteInTx(query.list());
        getDao().insertInTx(db);
    }

    public static MailCert getCert(String mail, String type) {
        Query query = getDao().queryBuilder().where(MailCertDao.Properties.User.eq(mail), MailCertDao.Properties.Type.eq(type)).build();
        List<MailCert> listDb = query.list();
        if (null != listDb && listDb.size() > 0) {
            return listDb.get(0);
        }
        return null;
    }

    public static List<MailCert> getListCert(List<String> listMail, String type) {
        Query query = getDao().queryBuilder().where(MailCertDao.Properties.User.in(listMail.toArray()), MailCertDao.Properties.Type.eq(type)).build();
        List<MailCert> listDb = query.list();
        if (null != listDb && listDb.size() > 0) {
            return listDb;
        }
        return null;
    }

    public static void deleleAll() {
        getDao().deleteAll();
    }

    private static MailCertDao getDao() {
        return YxDatabaseSession.getInstance(AppBase.getAppContext()).getMailCertDao();
    }
}
