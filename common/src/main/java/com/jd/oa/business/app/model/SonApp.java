package com.jd.oa.business.app.model;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

@Keep
public class SonApp implements Parcelable {
    private String appID;
    private String appName;
    private String appDeeplink;

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppDeeplink() {
        return appDeeplink;
    }

    public void setAppDeeplink(String appDeeplink) {
        this.appDeeplink = appDeeplink;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.appID);
        dest.writeString(this.appName);
        dest.writeString(this.appDeeplink);
    }

    public SonApp() {
    }

    protected SonApp(Parcel in) {
        this.appID = in.readString();
        this.appName = in.readString();
        this.appDeeplink = in.readString();
    }

    public static final Parcelable.Creator<SonApp> CREATOR = new Parcelable.Creator<SonApp>() {
        @Override
        public SonApp createFromParcel(Parcel source) {
            return new SonApp(source);
        }

        @Override
        public SonApp[] newArray(int size) {
            return new SonApp[size];
        }
    };
}
