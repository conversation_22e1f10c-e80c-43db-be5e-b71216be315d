package com.jd.oa.business.smime;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.utils.FileUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.business.smime.callback.ISmimeCallback;
import com.jd.oa.business.smime.model.EmailInfo;
import com.jd.oa.business.smime.utils.MailCertDaoHelper;
import com.jd.oa.business.smime.utils.SmimeUtil;
import com.jd.oa.cache.FileCache;
import com.jd.oa.db.greendao.MailCert;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jd.oa.utils.encrypt.Base64Encoder;
import ipworkssmime.Certificate;
import ipworkssmime.Certmgr;
import ipworkssmime.Header;
import ipworkssmime.IPWorksSMIMEException;
import ipworkssmime.MIMEPart;
import ipworkssmime.Mime;
import ipworkssmime.Smime;

public class SmimeHelper {

    private Context mContext;
    private Mime mime;
    private Smime mSmime;
    private Certmgr mCertmgr;

//    private static SmimeHelper helper;

    private final String license = "314D47465642364D30464D4E39573131524445564531305045525343304D00000000000000000000585447465343534500004E303658444A59415A344E320000";

    private LogRecorder mLogRecorder;

    public SmimeHelper(Context context) {
        mContext = context;
        mSmime = new Smime(mContext);
        mSmime.setRuntimeLicense(license);
        mime = new Mime(mContext);
        mime.setRuntimeLicense(license);

        mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());
    }

//    public synchronized static SmimeHelper getInstance(Context context) {
//        if (helper == null) {
//            helper = new SmimeHelper(context);
//        }
//        return helper;
//    }

    /**
     * 生成Eml文件
     *
     * @param emailInfo 邮件信息
     * @param callback  回调
     */
    public void createEml(final EmailInfo emailInfo, final ISmimeCallback callback) {
        SmimeUtil.getCertificate(SmimeUtil.getEmailAddress(), SmimeUtil.TYPE_PRK, new LoadDataCallback<MailCert>() {
            @Override
            public void onDataLoaded(MailCert cert) {
                try {
                    mime.reset();
                    // 测试header
//                    mime.getMessageHeaders().add(new Header("From", "<EMAIL>"));
//                    mime.getMessageHeaders().add(new Header("To", "<EMAIL>"));
//                    mime.getMessageHeaders().add(new Header("Bcc", "<EMAIL>"));
                    // Body
                    MIMEPart mimeBody = new MIMEPart();
                    mimeBody.setContentType("text/html;charset=utf-8");
                    InputStream isBody = new ByteArrayInputStream(emailInfo.body.getBytes("utf-8"));
                    mimeBody.setPartInputStream(isBody);
                    List<MIMEPart> listpart = new ArrayList<>();
                    listpart.add(mimeBody);
                    mime.getParts().addAll(listpart);
                    // mime.getMessageHeaders().add(new Header("Subject", "=?utf-8?B?" + Base64Encoder.encode(emailInfo.Subject) + "?="));
                    // Attachments
                    if (null != emailInfo.attachmentList) { // 为空，无附件
                        List<MIMEPart> listAttachPart = new ArrayList<>();
                        for (EmailInfo.AttachmentInfo attr : emailInfo.attachmentList) {
                            File tmpFile = new File(attr.filePath);
                            if (!tmpFile.exists()) {
                                continue;
                            }
                            MIMEPart tmpMimePart = new MIMEPart();
                            tmpMimePart.setContentDisposition(attr.isInline ? "inline" : "attachment");
                            tmpMimePart.setContentId(attr.isInline ? "<" + attr.contentId + ">" : attr.contentId);
                            tmpMimePart.setName("=?UTF-8?B?" + Base64Encoder.encode(attr.name) + "?=");
                            tmpMimePart.setFilename("=?UTF-8?B?" + Base64Encoder.encode(attr.name) + "?=");
                            tmpMimePart.setDecodedString(SmimeUtil.readFile(tmpFile));
                            tmpMimePart.setEncoding(MIMEPart.peBase64);
                            listAttachPart.add(tmpMimePart);
                        }
                        mime.getParts().addAll(listAttachPart);
                    }
                    //编码
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    mime.encodeToStream(outputStream);
                    byte[] bMsg = outputStream.toByteArray();

                    List<Header> listHeader = new ArrayList<>();
                    listHeader.add(new Header("Content-Type", mime.getContentType() + ";" + mime.getContentTypeAttr() + ""));
                    mime.getMessageHeaders().addAll(listHeader);

                    boolean flag = false;
                    if (null != emailInfo.recipientList && emailInfo.recipientList.size() > 0) { // null 无收件人
                        flag = true;
                    }

                    switch (emailInfo.encryptionOptions) {
                        case 1:
                            sign(emailInfo, bMsg, mime.getMessageHeadersString(), false, callback, cert);
                            break;
                        case 2:
                            encrypt(emailInfo, bMsg, mime.getMessageHeadersString(), callback);
                            break;
                        default:
                            sign(emailInfo, bMsg, mime.getMessageHeadersString(), flag, callback, cert);
                            break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    callback.fail("001");
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                callback.fail("001");
            }
        });
    }

    // 签名
    private void sign(final EmailInfo info, final byte[] data, final String header, final boolean encryptFlag, final ISmimeCallback callback, MailCert mailCert) {

        try {
            if (mCertmgr == null) {
                initCertmgr(mailCert.getCert(), mailCert.getPwd());
            }
            mSmime.reset();
            mSmime.setCertificate(mCertmgr.getCert());
            mSmime.setInputMessageHeadersString(header);
            mSmime.setInputMessage(data);
            mSmime.setDetachedSignature(false);
            mSmime.config("ApplyB64Encoding=1");

            mSmime.sign();

            if (encryptFlag) {
                encrypt(info, mSmime.getOutputMessage(), mSmime.getOutputMessageHeadersString(), callback);
            } else {
                String sHeader = mSmime.getOutputMessageHeadersString() + "\r\n";
                byte[] tmpbyte = SmimeUtil.byteMerger(sHeader.getBytes("utf-8"), mSmime.getOutputMessage());
                File fSign = FileUtils.saveFile(tmpbyte, FileCache.getInstance().getMailFile(), System.currentTimeMillis() + ".eml");
                callback.done(fSign.getAbsolutePath());
            }

        } catch (Exception e) {
            callback.fail("002");
        }

    }

    // 加密
    private void encrypt(final EmailInfo info, final byte[] data, final String header, final ISmimeCallback callback) {
        try {
            mSmime.reset();
            mSmime.setInputMessageHeadersString(header);
            mSmime.setInputMessage(data);
            mSmime.setDetachedSignature(false);
            mSmime.config("ApplyB64Encoding=1");

            List<MailCert> listData = MailCertDaoHelper.getListCert(info.recipientList, SmimeUtil.TYPE_PUK);
            if (null != listData) {
                for (MailCert cert : listData) {
                    mSmime.addRecipientCert(cert.getCert().getBytes("utf-8"));
                }
            }
            mSmime.encrypt();

            String sHeader = mSmime.getOutputMessageHeadersString() + "\r\n";

            byte[] tmpbyte = SmimeUtil.byteMerger(sHeader.getBytes("utf-8"), mSmime.getOutputMessage());
            File fSign = FileUtils.saveFile(tmpbyte, FileCache.getInstance().getMailFile(), System.currentTimeMillis() + ".eml");
            callback.done(fSign.getAbsolutePath());
        } catch (Exception e) {
            callback.fail("003");
        }

    }


    /**
     * 解析Eml To Json
     *
     * @param filePath 文件路径
     * @param callback 回调
     */
    public void parseEml(final String filePath, final ISmimeCallback callback, JSONObject mainObj) {
        try {
            long paseStart = System.currentTimeMillis();
            mLogRecorder.record("smime", "--------parseEml start " + paseStart);

            FileInputStream fileInputStream = new FileInputStream(filePath);
            InputStream inputStream = FileUtils.getInputStream(fileInputStream);
            mime.reset();
            mime.decodeFromStream(inputStream);

            JSONObject jsonObject = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            StringBuffer bodyBuffer = new StringBuffer();
            // Body  Attachments
            for (int i = 0; i < mime.getParts().size(); i++) {
                MIMEPart part = mime.getParts().item(i);
                String tmpContentType = part.getContentType();
                if (tmpContentType.indexOf("multipart/") >= 0) {
                    parse(part.getDecodedString(), part.getHeaders(), jsonArray, bodyBuffer, filePath);
                } else if (tmpContentType.indexOf("text/plain") >= 0 && bodyBuffer.length() == 0) {
                    String charset = SmimeUtil.getCharset(part.getHeaders());
                    bodyBuffer.append(new String(part.getDecodedString(), charset));
                } else if (tmpContentType.indexOf("text/html") >= 0) {
                    bodyBuffer.setLength(0);
                    String charset = SmimeUtil.getCharset(part.getHeaders());
                    bodyBuffer.append(new String(part.getDecodedString(), charset));
                } else {
                    String folder = FileCache.getInstance().getMailFile().getAbsolutePath() + "/" + SmimeUtil.getRealFileName(filePath);
                    File fileFolder = new File(folder);
                    if (!fileFolder.exists()) {
                        fileFolder.mkdirs();
                    }
                    JSONObject obj = new JSONObject();
                    String name = !TextUtils.isEmpty(part.getName()) ? part.getName() : part.getFilename();
                    String contentId = part.getContentId();
                    obj.put("size", part.getSize());
                    obj.put("name", name);
                    obj.put("contentId", contentId);
                    boolean isInline = false;
                    if (!part.getContentDisposition().equals("attachment")) {
                        isInline = true;
                    }
                    obj.put("isInline", isInline);
                    File attr = FileUtils.saveFile(part.getDecodedString(), fileFolder, System.currentTimeMillis() + "-" + name);
                    obj.put("localPath", "https://localhst/file:///" + attr.getAbsolutePath());
                    jsonArray.put(obj);
                }
            }
            jsonObject.put("attachmentList", jsonArray);
            jsonObject.put("body", bodyBuffer.toString());
            mainObj.put("mail", jsonObject);

            long parseEnd = System.currentTimeMillis();
            mLogRecorder.record("smime", "--------timer =  " + (parseEnd - paseStart));
            mLogRecorder.record("smime", "--------parseEml end " + parseEnd);

            callback.done(mainObj.toString());

        } catch (Exception e) {
            callback.fail("004");
        }
    }

    // 递归解析
    private void parse(final byte[] data, final String header, final JSONArray attrArray, final StringBuffer body, final String filePath) throws Exception {
        Mime subMime = new Mime(mContext);
        subMime.setRuntimeLicense(license);
        subMime.setMessage(data);
        subMime.setMessageHeadersString(header);
        subMime.decodeFromString();
        for (int i = 0; i < subMime.getParts().size(); i++) {
            MIMEPart subpart = subMime.getParts().item(i);
            String tmpContentType = subpart.getContentType();
            if (tmpContentType.indexOf("multipart/") >= 0) {
                parse(subpart.getDecodedString(), subpart.getHeaders(), attrArray, body, filePath);
            } else if (tmpContentType.indexOf("text/plain") >= 0 && body.length() == 0) {
                String charset = SmimeUtil.getCharset(subpart.getHeaders());
                body.append(new String(subpart.getDecodedString(), charset));
            } else if (tmpContentType.indexOf("text/html") >= 0) {
                body.setLength(0);
                String charset = SmimeUtil.getCharset(subpart.getHeaders());
                body.append(new String(subpart.getDecodedString(), charset));
            } else {
                String folder = FileCache.getInstance().getMailFile().getAbsolutePath() + "/" + SmimeUtil.getRealFileName(filePath);
                File fileFolder = new File(folder);
                if (!fileFolder.exists()) {
                    fileFolder.mkdirs();
                }
                JSONObject obj = new JSONObject();
                String name = !TextUtils.isEmpty(subpart.getName()) ? subpart.getName() : subpart.getFilename();
                String contentId = subpart.getContentId();
                obj.put("size", subpart.getSize());
                obj.put("name", name);
                obj.put("contentId", subpart.getContentId());
                boolean isInline = false;
                if (!subpart.getContentDisposition().equals("attachment")) {
                    isInline = true;
                }
                obj.put("isInline", isInline);
                File attr = FileUtils.saveFile(subpart.getDecodedString(), fileFolder, System.currentTimeMillis() + "-" + name);
                obj.put("localPath", "https://localhst/file:///" + attr.getAbsolutePath());
                attrArray.put(obj);
            }
        }
    }

    // 解密并验签
    public void decryptAndVerifySignature(final String filePath, final String mEmail, final ISmimeCallback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                SmimeUtil.getCertificate(mEmail, SmimeUtil.TYPE_PRK, new LoadDataCallback<MailCert>() {
                    @Override
                    public void onDataLoaded(MailCert mailCert) {
                        try {
                            long decryptAndVerifySignatureStart = System.currentTimeMillis();
                            mLogRecorder.record("smime", "--------decryptAndVerifySignature start " + decryptAndVerifySignatureStart);

                            mime.reset();
                            FileInputStream fileInputStream = new FileInputStream(filePath);
                            InputStream inputStream = FileUtils.getInputStream(fileInputStream);

                            mime.decodeFromStream(inputStream);
                            boolean isEncrypt = true;
                            boolean isSign = true;
                            MIMEPart mimePart = mime.getParts().item(0);
                            mLogRecorder.record("smime", "decryptAndVerifySignature 31");
                            mSmime.setInputFile(mimePart.getDecodedFile());
                            mLogRecorder.record("smime", "decryptAndVerifySignature 32");
                            mSmime.setInputMessageHeadersString(mime.getParts().item(0).getHeaders());
                            mLogRecorder.record("smime", "decryptAndVerifySignature 33");
                            mSmime.setCertificate(mCertmgr.getCert());
                            mSmime.decryptAndVerifySignature();

                            JSONArray signArray = new JSONArray();
                            if (null != mSmime.getSignerCert()) {
                                String subject = mSmime.getSignerCert().getSubject();
                                String val = getSinger(subject);
                                JSONObject obj = new JSONObject();
                                obj.put("signer", val);
                                obj.put("signTime", "");
                                signArray.put(obj);
                            }

                            JSONObject jsonObjSmime = new JSONObject();
                            jsonObjSmime.put("isEncrypt", isEncrypt);
                            jsonObjSmime.put("isSign", isSign);
                            jsonObjSmime.put("signInfoList", signArray);

                            JSONObject mainJsonObj = new JSONObject();
                            mainJsonObj.put("statusCode", 0);
                            mainJsonObj.put("smime", jsonObjSmime);

                            String sHeader = mSmime.getOutputMessageHeadersString() + "\r\n";
                            byte[] tmpbyte = SmimeUtil.byteMerger(sHeader.getBytes("utf-8"), mSmime.getOutputMessage());
                            File fSign = FileUtils.saveFile(tmpbyte, FileCache.getInstance().getMailFile(), System.currentTimeMillis() + ".eml");

                            long decryptAndVerifySignatureEnd = System.currentTimeMillis();
                            mLogRecorder.record("smime", "--------timer =  " + (decryptAndVerifySignatureEnd - decryptAndVerifySignatureStart));
                            mLogRecorder.record("smime", "--------decryptAndVerifySignature end " + decryptAndVerifySignatureEnd);

                            parseEml(fSign.getAbsolutePath(), callback, mainJsonObj);
                        } catch (Exception e) {
                            callback.fail("005");
                        }

                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        callback.fail("005");
                    }
                });
            }
        }).start();


    }

    // 解密
    public void decrypt(final String filePath, final String mEmail, final ISmimeCallback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {

                SmimeUtil.getCertificate(mEmail, SmimeUtil.TYPE_PRK, new LoadDataCallback<MailCert>() {
                    @Override
                    public void onDataLoaded(final MailCert mailCert) {

                        try {
                            long decryptAndVerifySignatureStart = System.currentTimeMillis();
                            mLogRecorder.record("smime", "--------decrypt start " + decryptAndVerifySignatureStart);

                            mime.reset();
                            FileInputStream fileInputStream = new FileInputStream(filePath);
                            InputStream tmpInputstream = FileUtils.getInputStream(fileInputStream);

                            mime.decodeFromStream(tmpInputstream);

                            boolean isEncrypt = false;
                            boolean isSign = false;
                            String contentType = mime.getContentType();
                            String contentTypeAttr = mime.getContentTypeAttr();

                            if (contentType.indexOf("application/pkcs7-mime") >= 0) {
                                isEncrypt = contentTypeAttr.indexOf("enveloped-data") >= 0;
                                isSign = contentTypeAttr.indexOf("signed-data") >= 0;
                            } else if (contentType.indexOf("signed") >= 0) {
                                isSign = true;
                            }

                            if (isEncrypt) {
                                mSmime.reset();
                                if (mCertmgr == null) {
                                    initCertmgr(mailCert.getCert(), mailCert.getPwd());
                                }
                                MIMEPart mimePart = mime.getParts().item(0);
                                mLogRecorder.record("smime", "--------decrypt 21 ");
                                String tmpFilePath = mimePart.getDecodedFile();
                                mLogRecorder.record("smime", "--------decrypt 22 ");
                                mSmime.setInputFile(tmpFilePath);
                                mLogRecorder.record("smime", "--------decrypt 23 ");
                                mSmime.setInputMessageHeadersString(mimePart.getHeaders());
                                mLogRecorder.record("smime", "--------decrypt 24 ");
                                mSmime.setCertificate(mCertmgr.getCert());
                                mSmime.decrypt();

                                mime.reset();
                                mime.setMessageHeadersString(mSmime.getOutputMessageHeadersString());
                                mime.setMessage(mSmime.getOutputMessage());
                                mime.decodeFromString();
                                if ("application/pkcs7-mime".equals(mime.getContentType())) {
                                    isSign = mime.getContentTypeAttr().indexOf("signed-data") >= 0;
                                }

                            }

                            byte[] smimeData = mSmime.getOutputMessage();
                            String smimeHeader = mSmime.getOutputMessageHeadersString();
                            JSONArray signArray = new JSONArray();
                            if (isSign) {
                                mSmime.reset();
                                if (isEncrypt) {
                                    mSmime.setInputMessage(smimeData);
                                    mSmime.setInputMessageHeadersString(smimeHeader);
                                } else {
                                    MIMEPart mimePart = mime.getParts().item(0);
                                    String tmpFilePath = mimePart.getDecodedFile();
                                    mSmime.setInputFile(tmpFilePath);
                                    mSmime.setInputMessageHeadersString(mimePart.getHeaders());
                                }
                                mSmime.verifySignature();
                                if (null != mSmime.getSignerCert()) {
                                    String subject = mSmime.getSignerCert().getSubject();
                                    String val = getSinger(subject);
                                    JSONObject obj = new JSONObject();
                                    obj.put("signer", val);
                                    obj.put("signTime", "");
                                    signArray.put(obj);
                                }
                            }
                            String sHeader = mSmime.getOutputMessageHeadersString() + "\r\n";
                            byte[] tmpbyte = SmimeUtil.byteMerger(sHeader.getBytes("utf-8"), mSmime.getOutputMessage());
                            File fSign = FileUtils.saveFile(tmpbyte, FileCache.getInstance().getMailFile(), System.currentTimeMillis() + ".eml");
//                    callback.done(fSign.getAbsolutePath());

                            JSONObject jsonObjSmime = new JSONObject();
                            jsonObjSmime.put("isEncrypt", isEncrypt);
                            jsonObjSmime.put("isSign", isSign);
                            if (isSign) {
                                jsonObjSmime.put("signInfoList", signArray);
                            }

                            JSONObject mainJsonObj = new JSONObject();
                            mainJsonObj.put("statusCode", 0);
                            mainJsonObj.put("smime", jsonObjSmime);
                            long decryptAndVerifySignatureEnd = System.currentTimeMillis();
                            mLogRecorder.record("smime", "--------timer =  " + (decryptAndVerifySignatureEnd - decryptAndVerifySignatureStart));
                            mLogRecorder.record("smime", "--------decrypt end " + decryptAndVerifySignatureEnd);
                            parseEml(fSign.getAbsolutePath(), callback, mainJsonObj);

                        } catch (Exception e) {
                            callback.fail("006");
                            e.printStackTrace();
                        }


                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        callback.fail("006");
                    }
                });

            }
        }).start();


    }

    private String getSinger(String subject) throws Exception {
        subject = subject.replaceAll("\\s*", "");
        String[] s = subject.split(",");
        Map<String, String> map = new HashMap<>();
        for (String tmps : s) {
            String[] tmpss = tmps.split("=");
            map.put(tmpss[0], tmpss[1]);
        }
        return map.get("CN");
    }

    private void initCertmgr(String pfx, String pwd) {
        try {
            mCertmgr = new Certmgr(mContext);
            mCertmgr.setRuntimeLicense(license);
            Certificate certificate = new Certificate(Certificate.cstPFXBlob, pfx.getBytes("utf-8"), pwd, "*");
            mCertmgr.setCert(certificate);
        } catch (IPWorksSMIMEException | UnsupportedEncodingException e) {
            e.printStackTrace();
            mCertmgr = null;
        }
    }

}
