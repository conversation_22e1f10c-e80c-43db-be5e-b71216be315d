package com.jd.oa.business.didi.model;

import java.io.Serializable;

/**
 * 获取出发地或者目的地的职场地址Bean
 */
public class DidiAddressBean implements Serializable {

    public String displayName; //"京东总部"
    public String address;  //"详细地址"
    public String cityName; // "北京市",//城市名称
    public String cityCode; //城市ID
    public String lat;     //"1.3" //地址纬度
    public String lng;     //"2.6" //地址经度
    public String hypothetical;
    //部分变颜色String的启示位置
    public int span_start;
    //部分变颜色String的结束位置
    public int span_end;

    @Override
    public String toString() {
        return "DidiAddressBean{" +
                "displayName='" + displayName + '\'' +
                ", address='" + address + '\'' +
                ", cityName='" + cityName + '\'' +
                ", cityCode='" + cityCode + '\'' +
                ", lat='" + lat + '\'' +
                ", lng='" + lng + '\'' +
                ", hypothetical='" + hypothetical + '\'' +
                ", span_start=" + span_start +
                ", span_end=" + span_end +
                '}';
    }
}