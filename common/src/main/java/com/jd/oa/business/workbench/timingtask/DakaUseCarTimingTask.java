package com.jd.oa.business.workbench.timingtask;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Parcel;

import com.chenenyu.router.Router;
import com.jd.oa.Constant;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.router.DeepLink;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.utils.NotificationUtils;
import com.jme.common.R;

import java.util.HashMap;
import java.util.List;

/**
 * Created by chenqizheng on 2018/3/15.
 */

public class DakaUseCarTimingTask extends TimingTask implements SosoLocationChangeInterface {
    private SosoLocationService mLocationService;
    private PendingIntent mResultPendingIntent;
    private Context mContext;

    @Override
    public void executor(Context context, Intent intent) {
        mContext = context;
        Intent resultIntent = Router.build(DeepLink.WORKBENCH_OLD).getIntent(context);
        mResultPendingIntent =
                PendingIntent.getActivity(context,
                        getRequestCode(),
                        resultIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
        mLocationService = new SosoLocationService(context);
        mLocationService.startLocation();
        mLocationService.setLocationChangedListener(this);
    }

    @Override
    public int getTimingTaskType() {
        return TASK_TYPE_DAKA_USE_CAR;
    }

    @Override
    public int getTimingTaskId() {
        return 1;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
    }

    public DakaUseCarTimingTask() {
    }

    protected DakaUseCarTimingTask(Parcel in) {
    }

    public static final Creator<DakaUseCarTimingTask> CREATOR = new Creator<DakaUseCarTimingTask>() {
        @Override
        public DakaUseCarTimingTask createFromParcel(Parcel source) {
            return new DakaUseCarTimingTask(source);
        }

        @Override
        public DakaUseCarTimingTask[] newArray(int size) {
            return new DakaUseCarTimingTask[size];
        }
    };

    @Override
    public void onLocated(String lat, String lng, String name, String cityName) {
        //定位成功去给后台经纬度校验在不在这个职场。
        mLocationService.stopLocation();
        checkLocation(lat, lng);

    }

    private void checkLocation(String lat, String lng) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("lat", lat);
        hashMap.put("lng", lng);
        NetWorkManager.request(null, NetworkConstant.API_CHECK_LOCATION, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(Object map, List<Object> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                NotificationUtils.sendNotificaiton(mContext, getRequestCode(), mContext.getString(R.string.me_punch_notification), mContext.getString(R.string.me_daka_use_car), mResultPendingIntent,NotificationUtils.CHANNEL_ID_OTHER,NotificationUtils.CHANNEL_NAME_OTHER);
            }
        }), hashMap);
    }

    @Override
    public void onFailed() {
        mLocationService.stopLocation();
    }
}
