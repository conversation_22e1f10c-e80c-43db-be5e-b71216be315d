package com.jd.oa.business.didi.model;

import org.json.JSONArray;

import java.util.List;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2016/1/25.
 */
public class DidiEstimatePriceBean {
    public String price = "0"; // 价格
    public String distance = "--"; // 里程
    public String duration = "--"; // 时间

    public String dynaPrice = "0";  //(动调溢价)
    public String dynaMD5 = "";  //(动调md5,调用叫车接口时需传入)
    public String multiple = "0";  //((动调倍数))
    public String startPrice = "0";  //(起步价)
    public String unitPrice = "0";  //(每公里单价)

    public String serviceType = ""; //  01 滴滴 02 首汽

    //常用地址校验新增
    public String type;
    public String addressOrder;
    public String showMsg;
    public String isCanUse;

    public String isTempAddress;

    public String stationId;

    public JSONArray callbackParams;

    public List<DidiEstimatePriceListBean> estimatePriceList;

    public boolean canUse() {
        return "1".equals(isCanUse);
    }

    public boolean isFrom() {
        return "from".equals(type);
    }

    public boolean isTempAddress() {
        return "1".equals(isTempAddress);
    }

    public static class DidiEstimatePriceListBean {
        public String serviceName; //运营商名称(滴滴出行、曹操出行)
        public String price;// 预估价格
        public String icon;//运营商logo
        public String stationId;//拼车订单专用id
        public String distance = "--"; // 里程
        public String duration = "--"; // 时间
        public String startPrice = "";
        public String unitPrice = "";
        public String serviceType = "";//运营商类型 01:滴滴 05:曹操
        public double originalPrice;
        public String priceKey = "";
        public String dynaMD5 = "";
        public String dynaPrice = "";
        public String multiple = "1.0";
    }
}