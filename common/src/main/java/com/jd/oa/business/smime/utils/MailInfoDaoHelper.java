package com.jd.oa.business.smime.utils;

import com.jd.oa.AppBase;
import com.jd.oa.db.greendao.MailAttachment;
import com.jd.oa.db.greendao.MailAttachmentDao;
import com.jd.oa.db.greendao.MailInfo;
import com.jd.oa.db.greendao.MailInfoDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.Query;

public class MailInfoDaoHelper {

    public static void insertData(String mailId, String localPath, JSONObject jsonObject) {
        Long timestamp = System.currentTimeMillis();
        // 清空数据
        Query query = getMailInfoDao().queryBuilder().where(MailInfoDao.Properties.MailId.eq(mailId)).build();
        getMailInfoDao().deleteInTx(query.list());
        Query queryDetail = getMailAttachmentDao().queryBuilder().where(MailAttachmentDao.Properties.MailId.eq(mailId)).build();
        getMailAttachmentDao().deleteInTx(queryDetail.list());
        // 组织数据
        try {
            JSONObject jsonObjSmime = jsonObject.getJSONObject("smime");
            JSONObject jsonObjMail = jsonObject.getJSONObject("mail");
            MailInfo mailInfo = new MailInfo();
            // 邮件ID
            mailInfo.setMailId(mailId);
            // 创建时间
            mailInfo.setTimestamp(timestamp);
            // 本地路径
            mailInfo.setLocalPath(localPath);
            // body
            mailInfo.setBody(jsonObjMail.getString("body"));
            // 是否加密
            mailInfo.setIsEncrypt(jsonObjSmime.getBoolean("isEncrypt"));
            // 是否签名
            mailInfo.setIsSign(jsonObjSmime.getBoolean("isSign"));
            // 签名列表
            if (jsonObjSmime.getBoolean("isSign") && jsonObjSmime.has("signInfoList")) {
                JSONArray singInfo = jsonObjSmime.getJSONArray("signInfoList");
                mailInfo.setSignInfoList(singInfo.toString());
            }
            // 附件
            JSONArray attachmentList = jsonObjMail.getJSONArray("attachmentList");
            List<MailAttachment> listAttachment = new ArrayList<>();
            for (int i = 0; i < attachmentList.length(); i++) {
                JSONObject tmpObj = attachmentList.getJSONObject(i);
                MailAttachment tmp = new MailAttachment();
                // 邮件ID
                tmp.setMailId(mailId);
                tmp.setContentId(tmpObj.getString("contentId"));
                tmp.setName(tmpObj.getString("name"));
                tmp.setSize(tmpObj.getInt("size"));
                tmp.setIsInline(tmpObj.getBoolean("isInline"));
                tmp.setLocalPath(tmpObj.getString("localPath"));
                tmp.setIndex(i);
                tmp.setTimestamp(timestamp);
                listAttachment.add(tmp);
            }

            // 插入数据
            getMailInfoDao().insertInTx(mailInfo);
            getMailAttachmentDao().insertInTx(listAttachment);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static JSONObject getMailInfo(String mailId) {
        Query query = getMailInfoDao().queryBuilder().where(MailInfoDao.Properties.MailId.eq(mailId)).build();
        Query queryDetail = getMailAttachmentDao().queryBuilder().where(MailAttachmentDao.Properties.MailId.eq(mailId)).orderAsc(MailAttachmentDao.Properties.Index).build();
        if (null != query.list() && query.list().size() == 0) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        JSONObject jsonObjSmime = new JSONObject();
        JSONObject jsonObjMail = new JSONObject();
        try {
            jsonObject.put("statusCode", -1);
            // 组织数据
            MailInfo info = (MailInfo) query.list().get(0);
            jsonObjMail.put("body", info.getBody());
            jsonObjSmime.put("isEncrypt", info.getIsEncrypt());
            jsonObjSmime.put("isSign", info.getIsSign());
            jsonObjSmime.put("signInfoList", info.getSignInfoList());
            // 附件
            List<MailAttachment> listAttachment = queryDetail.list();
            if (null != listAttachment && listAttachment.size() > 0) {
                JSONArray attachment = new JSONArray();
                for (MailAttachment mailAttachment : listAttachment) {
                    JSONObject tmpObj = new JSONObject();
                    tmpObj.put("contentId", mailAttachment.getContentId());
                    tmpObj.put("isInline", mailAttachment.getIsInline());
                    tmpObj.put("size", mailAttachment.getSize());
                    tmpObj.put("localPath", mailAttachment.getLocalPath());
                    tmpObj.put("name", mailAttachment.getName());
                    attachment.put(tmpObj);
                }
                jsonObjMail.put("attachmentList", attachment);
            }
            jsonObject.put("mail", jsonObjMail);
            jsonObject.put("smime", jsonObjSmime);
            jsonObject.put("statusCode", 0);
        } catch (Exception e) {
            e.printStackTrace();
        }


        return jsonObject;

    }

    public static MailAttachment getAttachment(String mailId, int index) {
        Query query = getMailAttachmentDao().queryBuilder().where(MailAttachmentDao.Properties.MailId.eq(mailId), MailAttachmentDao.Properties.Index.eq(index)).build();
        List<MailAttachment> listDb = query.list();
        if (listDb.size() > 0) {
            return listDb.get(0);
        }
        return null;
    }

    public static void deleleAll() {
        getMailInfoDao().deleteAll();
        getMailAttachmentDao().deleteAll();
    }

    private static MailInfoDao getMailInfoDao() {
        return YxDatabaseSession.getInstance(AppBase.getAppContext()).getMailInfoDao();
    }

    private static MailAttachmentDao getMailAttachmentDao() {
        return YxDatabaseSession.getInstance(AppBase.getAppContext()).getMailAttachmentDao();
    }

}
