package com.jd.oa.business.netdisk;

import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;
import java.util.Map;

public class NetworkUtils {

    private static final String GET_PREVIEW_URL = "jmeMobile/common/file/preview";

    public static void getPreviewUrl(String fileUrl, String filename, SimpleRequestCallback callback) {
        Map<String, Object> param = new HashMap<>();
        try {
            param.put("docUrl", fileUrl);
            param.put("fileName", filename);
        } catch (Exception e) {
        }
        NetWorkManager.request(null, GET_PREVIEW_URL, callback, param);
    }
}
