package com.jd.oa.business.index;

import static com.jd.oa.fragment.WebFragment2.BIZ_PARAM;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_DETAIL_URL;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ICON_KEY;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_NAME;
import static com.jd.oa.fragment.WebFragment2.EXTRA_NAV;
import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.router.DeepLink.DEEPLINK_PARAM;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.INotProguard;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.model.AppNoticeBean;
import com.jd.oa.business.index.model.AppSdkTokenBean;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.setting.VersionUpdateFragment;
import com.jd.oa.fragment.BottomSheetWebContainer;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.utils.MiniAppUtil;
import com.jd.oa.fragment.utils.WebAppUtil;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.model.AppInfoBean;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.open.TokenInfoBean;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.Utils;
import com.jd.oa.utils.Utils2String;
import com.jd.oa.utils.VersionUpdateUtil;
import com.jd.oa.utils.WebViewUtils;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;

/**
 * Created by qudongshi on 2016/8/11.
 */
public class AppUtils implements INotProguard {
    public static final String X5 = "2";
    private static final String TAG = "AppUtils";

    public static String ACTION_REFRESH_APP = "intent.filter.action.refresh.app";


    /**
     * 打开应用
     * jmScene add 7.9.10增加启动场景参数
     */
    public static void openFunctionByPlugIn(Activity activity, AppInfo app, String jmScene) {
        openFunctionByPlugIn(activity, app, null, jmScene);
    }

    /**
     * 打开应用
     */
    public static void openFunctionByPlugIn(Activity activity, AppInfo app) {
        openFunctionByPlugIn(activity, app, null, null);
    }

    /**
     * 打开应用包含source，jdme://web/201905050466?url=http%3a%2f%2fwww.baidu.com
     */
    public static void openFunctionByPlugIn(Activity activity, AppInfo app, Uri source, String jmScene) {

        // 不是整合应用，其整合应用ID需要设置为空
        if (app.getIsInterateParentApp().equals("0")) {
            app.setInterateAppID("");
        }
        String callbackInfo = app.callbackInfo;
        if (callbackInfo == null) {
            callbackInfo = app.getCallBackInfo();
        }
        openFunctionByPlugIn(activity, app.getAppID(), app.getAppName(), app.getAppType(), app.getAppAddress(), app.getAppCName(),
                app.getCookie(), app.getVersion(), app.getInterateAppID(), app.getAnBrowserType(), app.getParam(), source,
                app.getAndroidMinVersion(), app.getIsNativeHead(), callbackInfo, app.getPhotoKey(),
                app.getBizParamStr(), jmScene);//这里做过修改，把是否是原生头的标记传过去IsNativeHead()
    }


    /**
     * 根据插件打开功能，添加附带bundle
     */
    public static void openFunctionByPlugIn(final Activity activity, final String appId, String appName, String appType, final String appAddress, final String appCName,
                                            HashMap<String, String> appCookies, String version, String interateAppID, String browserType, final String param, Uri source,
                                            String androidMinVersion, String isNativeHead, String callbackInfo, String iconUrl,
                                            final String bizParam) {//这里做过修改，把是否是原生头的标记传过去IsNativeHead()
      openFunctionByPlugIn(activity, appId, appName, appType, appAddress, appCName, appCookies, version, interateAppID, browserType, param, source, androidMinVersion, isNativeHead, callbackInfo, iconUrl, bizParam, null);
    }

    /**
     * 根据插件打开功能，添加附带bundle
     * add jmScene 7.9.10 小程序需要感知来源
     */
    public static void openFunctionByPlugIn(final Activity activity, final String appId, String appName, String appType, final String appAddress, final String appCName,
                                            HashMap<String, String> appCookies, String version, String interateAppID, String browserType, final String param, Uri source,
                                            String androidMinVersion, String isNativeHead, String callbackInfo, String iconUrl,
                                            final String bizParam, String jmScene) {//这里做过修改，把是否是原生头的标记传过去IsNativeHead()
        if (null != AppNotice.getInstant().getOutNotice(appId)) {
            AppNoticeBean.NoticeBean noticeBean = AppNotice.getInstant().getOutNotice(appId);
            showDialog(activity, noticeBean);
            return;
        }

        if (activity == null) return;

        Logger.d(TAG, "openFunctionByPlugIn  appName:" + appName + ",appId:" + appId + ",appType:" + appType);

        // 判断版本
        String appVersion = DeviceUtil.getLocalVersionName(activity);
        boolean needUpdate = needUpdate(appVersion, androidMinVersion);
        if (needUpdate) {
            showDialog(activity);
            return;
        }

        if (TextUtils.isEmpty(appType)) {
            return;
        }

        Intent intent = new Intent(AppBase.getAppContext(),
                FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_THEME, "0");
        intent.putExtra(FunctionActivity.FLAG_WINDOW_FEATURE, -1);
        try {
            if (source != null) {
                String url = source.getQueryParameter(EXTRA_APP_DETAIL_URL);
                if (Utils2String.isNotEmptyWithTrim(url)) {
                    intent.putExtra(EXTRA_APP_DETAIL_URL, url);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //埋点用于5.0应用推荐
        addVisits(appId, appCName);
        switch (appType) {
            case "1": // fragment 类型
                if (appAddress.contains("Activity")) {
                    //是activity类型，特殊处理
                    openFunWithActivity(activity, appName, appAddress, appId, appCName, param);
                } else if (appAddress.startsWith(DeepLink.JDME)) {
                    //deeplink类型
                    Router.build(appAddress).go(activity, new RouteNotFoundCallback(activity));
                } else {//包含"Fragment"的处理方法
                    try {
                        // 福利券 aura组件
                        if ("8".equals(appId)) {
                            Intent t = new Intent();
                            t.setComponent(new ComponentName(activity, "com.jd.oa.mae.aura.welfare.WelfareMainActivity"));
                            activity.startActivity(t);
                        } else {
                            intent.putExtra("appId", appId);
                            intent.putExtra(FunctionActivity.FLAG_FUNCTION, appAddress);
                            if (param != null) {
                                intent.putExtra("param", param); //通过Intent携带传递param
                            }
                            activity.startActivity(intent);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        showUpdatePopup(activity);
                    }
                }
                break;
            case "9": // 本地H5 类型
                intent.putExtra(WebFragment2.HYBRID_APP, true);
            case "2": // web url 类型
                if (AppBase.isMultiTask()) {
                    WebAppUtil.openH5App(activity, appId, param, bizParam, true, false);
                    break;
                }
                //下方的逻辑可以在AppBase.isMultiTask()成熟以后去掉。
                intent.putExtra(EXTRA_APP_ID, appId);
                intent.putExtra(EXTRA_APP_NAME, appName);
                if (iconUrl != null) {
                    intent.putExtra(EXTRA_APP_ICON_KEY, iconUrl);
                }
                intent.putExtra(EXTRA_NAV, isNativeHead);//这里做过修改，把是否是原生头的标记也传过去
                intent.putExtra("appTrackName", appCName);
                if (X5.equals(browserType) || appType.equals("9")) {
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
                } else {
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getOldName());
                }
                if (param != null) {
                    intent.putExtra("param", param); //通过Intent携带传递param
                    intent.putExtra(DEEPLINK_PARAM, param);
                }
                if (bizParam != null) {
                    intent.putExtra(BIZ_PARAM, bizParam);
                }
                BottomSheetWebContainer.ScreenParam screenParam = WebAppUtil.checkScreenSize(param);
                if (screenParam.getUseBottomSheet()) {
                    BottomSheetWebContainer.show(activity, intent.getExtras(), screenParam);
                } else {
                    activity.startActivity(intent);
                }
                break;
            case "3":  // SDK类型,如网银钱包
                openFunWithActivity(activity, appName, appAddress, appId, appCName);
                break;
            case "4":
                //室内导航
                try {
                    Bundle data = new Bundle();
                    data.putString("erpCode", MyPlatform.getCurrentUser().getUserName());
                    data.putString("userName", MyPlatform.getCurrentUser().getRealName());
                    data.putString("userDepartment", MyPlatform.getCurrentUser().getOrganizationName());
                    data.putString("userHeadImg", MyPlatform.getCurrentUser().getUserIcon());
                    data.putString(FunctionActivity.FLAG_THEME, "0");
                    Intent intent1 = new Intent();
                    ComponentName comp = new ComponentName(activity, appAddress);
                    intent1.setComponent(comp);
                    intent1.putExtras(data);
                    activity.startActivity(intent1);
                } catch (Exception e) {
                    Logger.e(TAG, String.format("%s 应用打开失败, 原因：%s", appName,
                            e.toString()));
                    showUpdatePopup(activity);
                }
                break;
            case "5":
                // 如果是deeplink
                if (StringUtils.isNotEmptyWithTrim(appAddress) && appAddress.startsWith(DeepLink.JDME)) {
                    gainTokenAndGoPlugin(appAddress, appId);
                } else {
                    getSdkToken(activity, appId, new AppSdkTokenBean.IGetTokenCallback() {
                        @Override
                        public void callback(String token) {
                            try {
                                Intent intentSdk = new Intent();
                                ComponentName cn = new ComponentName(activity, appAddress);
                                intentSdk.setComponent(cn);
                                intentSdk.putExtra("token", token); //通过Intent携带传递Token
                                if (param != null) {
                                    intentSdk.putExtra("param", param); //通过Intent携带传递param
                                }
                                activity.startActivity(intentSdk);
                            } catch (Exception e) {
                                showUpdatePopup(activity);
                            }
                        }
                    });
                }
                break;
            case "7": //打开网页子页
                if (appCookies != null) {
                    intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                    WebBean bean = new WebBean(appAddress, WebConfig.H5_NATIVE_HEAD_SHOW);
                    bean.setWriteCookie(1);
                    bean.setCookieMapInfo(appCookies);
                    intent.putExtra(EXTRA_WEB_BEAN, bean);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
                    activity.startActivity(intent);
                }
                break;
            case "8": //RN类型
                Uri.Builder realUriBuild = new Uri.Builder();
                realUriBuild.scheme(DeepLink.JDME_SCHEME);
                realUriBuild.authority("rn");
                if (TextUtils.isEmpty(interateAppID)) {
                    realUriBuild.path(appId);
                    realUriBuild.appendQueryParameter("appId", appId);
                } else {
                    realUriBuild.path(interateAppID);
                    realUriBuild.appendQueryParameter("parentAppid", appId);
                }
                if (!TextUtils.isEmpty(version)) {
                    realUriBuild.appendQueryParameter("version", version);
                }
                Router.build(realUriBuild.build()).go(activity);

                break;
            case "10": // JD小程序 类型
                if (callbackInfo != null) {
                    MiniAppUtil.openMiniApp(activity, appId, callbackInfo, param, jmScene);
                }
                break;
            default:
                break;
        }
    }


    /**
     * deeplink 跳转 第三方sdk 获取 token，在这里直跳转了；
     */
    public static void gainTokenAndGoPlugin(final String deeplink, String appId) {

        // 返回的是字符串：{"content":{"third_name":"","jdPin":"","third_timesntamp":"","userCode":"","third_token":"","realName":"赵宇"},
        // "errorCode":"0","errorMsg":""}
        NetWorkManagerAppCenter.gainToken(null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            protected void onSuccess(JSONObject str, List<JSONObject> strs, String raw) {
                super.onSuccess(str, strs, raw);
                TokenInfoBean infoBean = new Gson().fromJson(raw, TokenInfoBean.class);
                if (infoBean != null && infoBean.isTokenSuccess()) {
                    final Uri originUri = Uri.parse(deeplink);
                    String realDeepLink = deeplink + (originUri.getQueryParameterNames().size() > 0 ? "&" : "?")
                            + "param=" + Uri.encode(infoBean.contentToJSONStr());
                    Router.build(realDeepLink).go(AppBase.getAppContext(), new RouteNotFoundCallback(AppBase.getAppContext()));
                } else {
                    com.jd.oa.melib.ToastUtils.showInfoToast(AppBase.getAppContext(), "" + infoBean.errorMsg);
                }
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                //ToastUtils.showInfoToast("" + errorMsg + ": " + code);
                Uri originUri = Uri.parse(deeplink)
                        .buildUpon().appendQueryParameter("authFailed", "true")
                        .build();
                Router.build(originUri.toString()).go(AppBase.getAppContext(), new RouteNotFoundCallback(AppBase.getAppContext()));
            }
        }), appId);
    }


    private static void addVisits(String appSN, String appName) {
        NetWorkManagerAppCenter.addVisits(appSN, appName);
    }


    /**
     * 如果是 activity，需要区分 内部 or 外部应用的activity
     */
    private static void openFunWithActivity(final Activity activity, String appName, String address, String appId, String appCName, String... args) {
        Logger.d(TAG, "openFunWithActivity plugIn Name=" + appName);
        // 1.先判断 此功能 是否为外部 apk
        Logger.d(TAG, "func.getItemLink=" + address);
        String[] split = address.split("\\|\\|");
        if (split.length == 3) { //暂时为网银钱包
            Utils.openPlugInActivity(activity, address, new Utils.ApkNotInstallCallBack() {
                @Override
                public void onCallBack(final AppInfoBean bean) {
                    {
                        if (bean != null) {
                            PromptUtils.showConfrimDialog(activity,
                                    R.string.me_purse_account, bean.getInfo(),
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            // 打开网页
                                            Intent intent = new Intent();
                                            intent.setAction("android.intent.action.VIEW");
                                            Uri content_url = Uri.parse(bean.getDownUrl());
                                            intent.setData(content_url);
                                            activity.startActivity(intent);
                                        }
                                    });
                        }
                    }
                }
            });
        } else {
            // 内部activity
            try {
                Intent intent = new Intent(AppBase.getAppContext(), Class.forName(address));
                if (null != args && args.length > 0) {
                    intent.putExtra("param", args[0]); //通过Intent携带传递param
                }
                intent.putExtra("appId", appId);
                intent.putExtra("appName", appName);
                //针对插件应用，通知直接传到应用内
                final AppNoticeBean.NoticeBean noticeBean = AppNotice.getInstant().getInsideNotice(appId);
                if (noticeBean != null) {
                    intent.putExtra("appNotification", new Gson().toJson(noticeBean));
                }
                activity.startActivity(intent);
            } catch (Exception e) {
                Logger.e(TAG, String.format("%s 应用打开失败, 原因：%s", appName,
                        e.getMessage()));
                showUpdatePopup(activity);
            }
        }
    }

    private static void showUpdatePopup(final Activity activity) {
        new IosAlertDialog(activity).builder()
                .setTitle(activity.getString(R.string.me_update_for_open_fail_info))
                .setCancelable(false)
                .setPositiveButton(activity.getString(R.string.me_go_update), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        VersionUpdateUtil.checkVersion(activity, false);
                    }
                })
                .setNegativeButton(activity.getString(R.string.me_cancel), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                    }
                }).show();
    }

    public static void getSdkToken(final Context context, String appId, final AppSdkTokenBean.IGetTokenCallback callback) {

        NetWorkManagerAppCenter.gainToken(context, new SimpleRequestCallback<String>(context) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                Logger.d(TAG, info.toString());
                callback.callback(info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

        }, appId);
    }

    private static void showDialog(final Activity activity, final AppNoticeBean.NoticeBean noticeBean) {
        String confirmText = noticeBean.appType.equals("9") ? activity.getString(R.string.me_tip_iknow) : null;
        VersionUpdateFragment fragment = VersionUpdateFragment.newInstance(null, noticeBean.clickNotice, confirmText, true, new VersionUpdateFragment.DialogClickListener() {
            @Override
            public void doNegativeClick() {

            }

            @Override
            public void doPositiveClick() {
                if (noticeBean.appType.equals("9")) return;
                VersionUpdateUtil.checkVersion(activity, true);
            }
        });
        fragment.show(((AppCompatActivity) activity).getSupportFragmentManager(), "update");
    }

    public static void showDialog(final Activity activity) {
        String confirmText = activity.getString(R.string.me_upgrade_now);
        VersionUpdateFragment fragment = VersionUpdateFragment.newInstance(null, activity.getString(R.string.me_app_need_update), confirmText, true, new VersionUpdateFragment.DialogClickListener() {
            @Override
            public void doNegativeClick() {

            }

            @Override
            public void doPositiveClick() {
                VersionUpdateUtil.checkVersion2(activity);
            }
        });
        fragment.show(((AppCompatActivity) activity).getSupportFragmentManager(), "update");
    }

    public static String getHasTipsAppIds(List<AppInfo> list) {
        if (list == null || list.isEmpty()) return null;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            AppInfo info = list.get(i);
            if (AppInfo.IS_APP_DETAIL.equals(info.getIsAppDetail())) {
                sb.append(info.getAppID());
                if (i != list.size() - 1) {
                    sb.append(",");
                }
            }
        }
        return sb.toString();
    }

    public static boolean needUpdate(String localVersion, String minAppVersion) {
        if (TextUtils.isEmpty(localVersion) || TextUtils.isEmpty(minAppVersion)) {
            return false;
        }
        String[] localVersionArray = localVersion.split("\\.");
        String[] minVersionArray = minAppVersion.split("\\.");
        if (localVersionArray.length < minVersionArray.length) {
            int cha = minVersionArray.length - localVersionArray.length;
            for (int i = 0; i < cha; i++) {
                localVersion = localVersion + ".0";
            }
            localVersionArray = localVersion.split("\\.");
        }
        if (localVersionArray.length > minVersionArray.length) {
            int cha = localVersionArray.length - minVersionArray.length;
            for (int i = 0; i < cha; i++) {
                minAppVersion = minAppVersion + ".0";
            }
            minVersionArray = minAppVersion.split("\\.");
        }
        for (int i = 0; i < minVersionArray.length; i++) {
            int min = StringUtils.convertToInt(minVersionArray[i]);
            int local = StringUtils.convertToInt(localVersionArray[i]);
            if (min > local) {
                return true;
            } else if (min < local) {
                return false;
            }
        }
        return false;
    }

}