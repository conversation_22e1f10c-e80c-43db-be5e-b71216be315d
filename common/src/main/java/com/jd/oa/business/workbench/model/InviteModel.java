package com.jd.oa.business.workbench.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class InviteModel implements Parcelable {

    @SerializedName("realName")
    private String name;
    @SerializedName("userName")
    private String uid;
    private String icon;

    public InviteModel() {

    }

    private InviteModel(Parcel in) {
        name = in.readString();
        uid = in.readString();
        icon = in.readString();
    }

    public static final Creator<InviteModel> CREATOR = new Creator<InviteModel>() {
        @Override
        public InviteModel createFromParcel(Parcel in) {
            return new InviteModel(in);
        }

        @Override
        public InviteModel[] newArray(int size) {
            return new InviteModel[size];
        }
    };

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(name);
        dest.writeString(uid);
        dest.writeString(icon);
    }
}
