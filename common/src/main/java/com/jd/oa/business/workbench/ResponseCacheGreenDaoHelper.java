package com.jd.oa.business.workbench;

import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.db.greendao.ResponseCacheDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import de.greenrobot.dao.query.QueryBuilder;

/**
 * Created by Chen on 2017/11/10.
 */

public class ResponseCacheGreenDaoHelper {
    public synchronized static void deleteAll() {
        YxDatabaseSession.getInstance(AppBase.getAppContext()).getResponseCacheDao().deleteAll();
    }

    public synchronized static void delete(String username, String url, HashMap<String, String> params) {
        ResponseCache oldCache = loadCache(username, url, params);
        if (oldCache != null) {
            YxDatabaseSession.getInstance(AppBase.getAppContext()).getResponseCacheDao().delete(oldCache);
        }
    }

    public static synchronized ResponseCache loadCache(String username, String url, HashMap<String, String> params) {

        QueryBuilder<ResponseCache> queryBuilder = YxDatabaseSession.getInstance(AppBase.getAppContext()).getResponseCacheDao().queryBuilder();
        queryBuilder.where(ResponseCacheDao.Properties.UserName.eq(username), ResponseCacheDao.Properties.Params.eq(getHashMapSortStr(params)), ResponseCacheDao.Properties.Url.eq(url.toString()));
        List<ResponseCache> list = queryBuilder.list();
        if (list != null && list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    public static synchronized void addCache(String username, String url, HashMap<String, String> params, String content) {
        ResponseCache oldCache = loadCache(username, url, params);
        ResponseCache cache = new ResponseCache();
        cache.setParams(getHashMapSortStr(params));
        cache.setResponse(content);
        cache.setUrl(url);
        cache.setUserName(username);
        if (oldCache != null) {
            YxDatabaseSession.getInstance(AppBase.getAppContext()).getResponseCacheDao().delete(oldCache);
        }
        YxDatabaseSession.getInstance(AppBase.getAppContext()).getResponseCacheDao().insert(cache);
    }

    public static String getHashMapSortStr(HashMap<String, String> hashMap) {
        if (hashMap == null) {
            return "";
        }
        List<Map.Entry<String, String>> list =
                new ArrayList<Map.Entry<String, String>>(hashMap.entrySet());

        Collections.sort(list, new Comparator<Map.Entry<String, String>>() {
            public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                //return (o2.getValue() - o1.getValue());
                return (o1.getKey()).toString().compareTo(o2.getKey());
            }
        });
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : list
                ) {

            sb.append(entry.getKey() + "=" + entry.getValue());
            sb.append(",");
        }
        return sb.toString();
    }

    public static boolean isCacheVaild(ResponseCache cache) {
        if (cache != null && !TextUtils.isEmpty(cache.getResponse())) {
            return true;
        }
        return false;
    }
}
