package com.jd.oa.business.smime.utils;

import com.jd.oa.cache.FileCache;

import java.io.File;

public class MailDataClean {

    private static long FILE_SAVE_TIME = 1 * 60 * 60 * 1000;

    private static long DATA_SAVE_TIME = 3 * 60 * 60 * 1000;

    public static void start() {
        File file = FileCache.getInstance().getMailFile();
        if (file.exists()) {
            clean(file);
        }
    }

    private static void clean(final File file) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    File[] files = file.listFiles();
                    for (File tmpFile : files) {
                        if (tmpFile.isDirectory()) {
                            continue;
                        }
                        if (System.currentTimeMillis() - tmpFile.lastModified() > FILE_SAVE_TIME) {
                            tmpFile.delete();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }).start();
    }
}
