package com.jd.oa.business.setting;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.DialogFragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.download.DownloadResult;
import com.jd.oa.download.DownloadService;
import com.jd.oa.ui.GradientButton;
import com.jd.oa.utils.AppUpdateCache;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StringUtils;
import com.jme.common.R;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

public class VersionUpdateDialog extends DialogFragment {
    private static final String ARG_TITLE = "arg_title";
    private static final String ARG_MESSAGE = "arg_message";
    private static final String ARG_CONFIRM = "arg_confirm";
    private static final String ARG_SHOW_CANCEL = "arg_show_cancel";
    private static final String ARG_UPDATE_TEXT = "arg_update_text";
    private static final String ARG_UPDATE_NUM = "arg_update_num";
    private static final String ARG_DEEP_LINK = "arg_deep_link";

    private DialogClickListener mListener;

    private String currentDownloadStatus = AppUpdateCache.INSTANCE.getUpdateStatus();

    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (DownloadService.APK_DOWNLOAD.equals(intent.getAction())) {
                String action = intent.getStringExtra("action");
                if (action != null) {
                    currentDownloadStatus = action;
                    switch (action) {
                        case DownloadService.ACTION_START:
                            onDownloadStart();
                            break;
                        case DownloadService.ACTION_PROGRESS:
                            String progress = intent.getStringExtra(DownloadService.ACTION_PROGRESS);
                            if (progress != null) {
                                onDownloadProgress(progress);
                            }
                            break;
                        case DownloadService.ACTION_PAUSED:
                            onDownloadPaused();
                            break;
                        case DownloadService.ACTION_SUCCESS:
                            onDownloadFinish();
                            break;
                        case DownloadService.ACTION_FAILURE:
                            String errCode = intent.getStringExtra(DownloadService.ACTION_FAILURE);
                            onDownloadFailed(errCode);
                            break;
                    }
                }
            }
        }
    };
    private LinearLayout ll_progress;
    private LinearLayout ll_update_content;
    private LinearLayout ll_failed;
    private ProgressBar progress_bar;
    private TextView tv_currentProgress;
    private boolean showCancel;
    private String mConfirmText;
    private GradientButton mJumpBtn;
    private GradientButton mBtnConfirm;
    private LinearLayout ll_buttons;
    private View cancel;
    private TextView tv_download_status;
    private boolean checkDownload = false;
    private String currentProgress = "0";
    private TextView tv_failed;

    @SuppressLint("SetTextI18n")
    private void onDownloadStart() {
        MELogUtil.info(MELogUtil.TAG_AUP, "onDownloadStart");
        ll_update_content.setVisibility(View.GONE);
        ll_progress.setVisibility(View.VISIBLE);
        ll_failed.setVisibility(View.GONE);
        progress_bar.setProgress(Integer.parseInt(currentProgress));
        tv_currentProgress.setText(currentProgress + "%");
        cancel.setVisibility(View.INVISIBLE);
        tv_download_status.setText(R.string.jdme_update_download_status);
        if (showCancel) {
            mJumpBtn.setVisibility(View.VISIBLE);
            mBtnConfirm.setVisibility(View.GONE);
            mJumpBtn.setButtonText(R.string.jdme_update_background);
        } else {
            ll_buttons.setVisibility(View.GONE);
        }
    }

    @SuppressLint("SetTextI18n")
    private void onDownloadProgress(String progress) {
        MELogUtil.info(MELogUtil.TAG_AUP, "onDownloadProgress " + progress);
        if ("-1".equals(progress)) {
            tv_download_status.setText(R.string.me_update_checking);
            return;
        }
        currentProgress = progress;
        ll_update_content.setVisibility(View.GONE);
        ll_progress.setVisibility(View.VISIBLE);
        ll_failed.setVisibility(View.GONE);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                progress_bar.setProgress(Integer.parseInt(progress), true);
            } else {
                progress_bar.setProgress(Integer.parseInt(progress));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        tv_currentProgress.setText(progress + "%");
        tv_download_status.setText(R.string.jdme_update_download_status);
        if (showCancel) {
            mJumpBtn.setVisibility(View.VISIBLE);
            mBtnConfirm.setVisibility(View.GONE);
            mJumpBtn.setButtonText(R.string.jdme_update_background);
        } else {
            ll_buttons.setVisibility(View.GONE);
        }
    }

    private void onDownloadPaused() {
        MELogUtil.info(MELogUtil.TAG_AUP, "onDownloadPaused");
        mBtnConfirm.setVisibility(View.VISIBLE);
        mJumpBtn.setVisibility(View.GONE);
        ll_buttons.setVisibility(View.VISIBLE);
        tv_download_status.setText(R.string.me_update_download_paused);
        mBtnConfirm.setButtonText(R.string.jdme_update_continue);
        if (showCancel) {
            mJumpBtn.setVisibility(View.VISIBLE);
            mJumpBtn.setButtonText(R.string.jdme_update_later);
        }
    }

    private void onDownloadFinish() {
        MELogUtil.info(MELogUtil.TAG_AUP, "onDownloadFinish");
        checkDownload = true;
        mBtnConfirm.setVisibility(View.VISIBLE);
        mJumpBtn.setVisibility(View.GONE);
        ll_buttons.setVisibility(View.VISIBLE);
        tv_download_status.setText(R.string.me_update_completed);
        mBtnConfirm.setButtonText(R.string.me_update_install_now);
        if (showCancel) {
            cancel.setVisibility(View.VISIBLE);
        }
    }

    private void onDownloadFailed(String errCode) {
        MELogUtil.info(MELogUtil.TAG_AUP, "onDownloadFailed");
        ll_update_content.setVisibility(View.GONE);
        ll_progress.setVisibility(View.GONE);
        ll_failed.setVisibility(View.VISIBLE);
        if (String.valueOf(DownloadResult.ERROR_FILE_DIGEST_INCORRECT).equals(errCode)) {
            tv_failed.setText(R.string.jdme_update_md5_fail);
        } else {
            tv_failed.setText(R.string.jdme_update_fail);
        }
        mBtnConfirm.setVisibility(View.VISIBLE);
        mBtnConfirm.setButtonText(R.string.jdme_update_restart);
        if (showCancel) {
            mJumpBtn.setVisibility(View.VISIBLE);
            mJumpBtn.setButtonText(R.string.jdme_update_later);
        } else {
            mJumpBtn.setVisibility(View.GONE);
        }
        ll_buttons.setVisibility(View.VISIBLE);
    }


    /**
     * @param title      标题资源，-1不显示标题，0显示默认标题（应用名称）
     * @param message    提示资源
     * @param showCancel false，只有一个按钮， true 2个按钮，一个确认，一个取消
     * @return VersionUpdateDialog
     */
    public static VersionUpdateDialog newInstance(String title,
                                                  String message,
                                                  boolean showCancel,
                                                  String deeplink,
                                                  String confirm,
                                                  String updateText,
                                                  String updateNum,
                                                  DialogClickListener listener) {
        VersionUpdateDialog fragment = new VersionUpdateDialog();
        Bundle args = new Bundle();
        args.putString(ARG_TITLE, title);
        args.putString(ARG_MESSAGE, message);
        args.putString(ARG_CONFIRM, confirm);
        args.putBoolean(ARG_SHOW_CANCEL, showCancel);
        args.putString(ARG_DEEP_LINK, deeplink);
        args.putString(ARG_UPDATE_TEXT, updateText);
        args.putString(ARG_UPDATE_NUM, updateNum);
        fragment.setArguments(args);
        fragment.mListener = listener;
        fragment.setCancelable(false);
        return fragment;
    }

    @NotNull
    @Override
    public Dialog onCreateDialog(Bundle saveInstanceState) {
        IntentFilter filter = new IntentFilter(DownloadService.APK_DOWNLOAD);
        LocalBroadcastManager.getInstance(requireActivity()).registerReceiver(receiver, filter);
        String title = getArguments().getString(ARG_TITLE);
        String message = getArguments().getString(ARG_MESSAGE);
        showCancel = getArguments().getBoolean(ARG_SHOW_CANCEL);
        mConfirmText = getArguments().getString(ARG_CONFIRM);
        String updateText = getArguments().getString(ARG_UPDATE_TEXT);
        String updateNum = getArguments().getString(ARG_UPDATE_NUM);
        final String jumpUrl = getArguments().getString(ARG_DEEP_LINK);

        final Dialog dialog = new Dialog(requireActivity(), R.style.me_ShareDialogStyle);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(R.color.transparent);
        }
        View view = LayoutInflater.from(getActivity()).inflate(R.layout.jdme_fragment_version_update_dialog, null, false);
        TextView tvTitle = view.findViewById(R.id.tv_update_subject);
        ll_progress = view.findViewById(R.id.ll_progress);
        ll_update_content = view.findViewById(R.id.ll_update_content);
        ll_failed = view.findViewById(R.id.ll_failed);
        tv_failed = view.findViewById(R.id.tv_failed);
        ll_buttons = view.findViewById(R.id.ll_buttons);
        progress_bar = view.findViewById(R.id.progress_bar);
        tv_currentProgress = view.findViewById(R.id.tv_currentProgress);
        tv_download_status = view.findViewById(R.id.tv_download_status);
        if (TextUtils.isEmpty(title)) {
            tvTitle.setVisibility(View.GONE);
        } else {
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(title);
        }

        TextView tv_update_text = view.findViewById(R.id.tv_update_text);
        if (TextUtils.isEmpty(updateText)) {
            tv_update_text.setVisibility(View.GONE);
        } else {
            tv_update_text.setVisibility(View.VISIBLE);
            tv_update_text.setText(updateText);
        }

        TextView tvMessage = view.findViewById(R.id.tv_update_summary);
        if (!TextUtils.isEmpty(message)) {
            tvMessage.setText(message);
        }

        TextView tv_update_num = view.findViewById(R.id.tv_update_num);
        if (!TextUtils.isEmpty(updateNum)) {
            tv_update_num.setText(updateNum);
        }

        cancel = view.findViewById(R.id.iv_update_cancel);

        dialog.setCancelable(showCancel);
        cancel.setVisibility(showCancel ? View.VISIBLE : View.INVISIBLE);

        mJumpBtn = view.findViewById(R.id.btn_jump);
        mJumpBtn.getButton().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
        mJumpBtn.setVisibility(StringUtils.isNotEmptyWithTrim(jumpUrl) ? View.VISIBLE : View.GONE);
        mBtnConfirm = view.findViewById(R.id.btn_update_ok);
        mBtnConfirm.getButton().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
        if (!TextUtils.isEmpty(mConfirmText)) {
            mBtnConfirm.setButtonText(mConfirmText);
        }
        mBtnConfirm.getButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                try {
//                    if (showCancel) {//强制更新点击升级不关闭dialog
//                        dialog.dismiss();
//                    }
//                } catch (Exception e) {
//                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
//                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
//                    e.printStackTrace();
//                }
                if (mListener != null) {
                    mListener.doPositiveClick(checkDownload);
                }

                Map<String, String> param = new HashMap<>();
                param.put("event_name", mBtnConfirm.getButton().getText().toString());
                JDMAUtils.clickEvent("", JDMAConstants.mobile_mobile_event_platform_app_update_alert_click, param);

                MELogUtil.localD(MELogUtil.TAG_AUP, "event_name : " + mBtnConfirm.getButton().getText().toString());
                MELogUtil.onlineD(MELogUtil.TAG_AUP, "event_name : " + mBtnConfirm.getButton().getText().toString());
            }
        });

        mJumpBtn.getButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (DownloadService.ACTION_PROGRESS.equals(currentDownloadStatus) ||
                        DownloadService.ACTION_FAILURE.equals(currentDownloadStatus) ||
                        DownloadService.ACTION_PAUSED.equals(currentDownloadStatus) ||
                        DownloadService.ACTION_START.equals(currentDownloadStatus)) {
                    try {
                        dialog.dismiss();
                    } catch (Exception e) {
                        MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                        MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                        e.printStackTrace();
                    }
                } else {
                    if (StringUtils.isNotEmptyWithTrim(jumpUrl) && null != mListener) {
                        mListener.doNegativeClick();
                    }
                }
            }
        });

        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
                if (mListener != null) {
                    mListener.doCloseClick();
                }
            }
        });

        String downloadStatus = AppUpdateCache.INSTANCE.getUpdateStatus();
        String progress = AppUpdateCache.INSTANCE.getUpdateProgress();
        if (DownloadService.ACTION_PAUSED.equals(downloadStatus)) {
            onDownloadStart();
            onDownloadProgress(progress);
            onDownloadPaused();
        } else if (DownloadService.ACTION_PROGRESS.equals(downloadStatus)) {
            onDownloadStart();
            onDownloadProgress(progress);
        }
        dialog.setContentView(view);
        return dialog;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        try {
            LocalBroadcastManager.getInstance(requireActivity()).unregisterReceiver(receiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (mListener != null) {
            mListener.onDismiss();
        }
    }

    public interface DialogClickListener {
        void doCloseClick();

        void doNegativeClick();

        void doPositiveClick(boolean checkDownload);

        void onDismiss();
    }
}