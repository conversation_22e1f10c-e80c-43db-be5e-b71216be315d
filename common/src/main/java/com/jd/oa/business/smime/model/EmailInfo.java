package com.jd.oa.business.smime.model;

import java.io.Serializable;
import java.util.ArrayList;

public class EmailInfo implements Serializable {

    public String uploadUrl;

    public ArrayList<String> recipientList;

    public ArrayList<AttachmentInfo> attachmentList;

    public String body;

    // 加密类型选项（1,签名；2,加密；3,签名&加密）
    public int encryptionOptions;

    public class AttachmentInfo {

        public String name;

        public boolean isInline;

        public String contentId;

        public String src;

        public String filePath;
    }

}
