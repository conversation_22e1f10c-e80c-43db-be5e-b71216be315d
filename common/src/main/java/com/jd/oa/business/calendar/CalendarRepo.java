package com.jd.oa.business.calendar;

import com.jd.oa.business.calendar.model.FortuneCalendar;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.exception.NetException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.annotations.NonNull;

/**
 * Created by peidongbiao on 2018/1/8.
 */

public class CalendarRepo {
    private static CalendarRepo sInstance;

    public static CalendarRepo get() {
        if (sInstance == null) {
            synchronized (CalendarRepo.class) {
                if (sInstance == null) {
                    sInstance = new CalendarRepo();
                }
            }
        }
        return sInstance;
    }

    private CalendarRepo() {

    }

    /**
     * 获取日历
     *
     * @return
     */
    public Observable<FortuneCalendar> getCalendar() {
        final Map<String, Object> params = new HashMap<>();
        params.put("searchDate", getCurrentTime());
        return Observable.create(new ObservableOnSubscribe<FortuneCalendar>() {
            @Override
            public void subscribe(@NonNull final ObservableEmitter<FortuneCalendar> e) throws Exception {
                NetWorkManager.request(null, NetworkConstant.API_GET_FORTURN_CALENDAR, new SimpleReqCallbackAdapter<>(new AbsReqCallback<FortuneCalendar>(FortuneCalendar.class) {
                    @Override
                    protected void onSuccess(FortuneCalendar calendar, List<FortuneCalendar> tArray, String rawData) {
                        super.onSuccess(calendar, tArray, rawData);
                        if (!e.isDisposed()) {
                            e.onNext(calendar);
                            e.onComplete();
                        }
                    }

                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        if (!e.isDisposed()) {
                            e.onError(new NetException(errorMsg));
                        }
                    }
                }), params);
            }
        });
    }

    private String getCurrentTime() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return dateFormat.format(new Date());
    }
}