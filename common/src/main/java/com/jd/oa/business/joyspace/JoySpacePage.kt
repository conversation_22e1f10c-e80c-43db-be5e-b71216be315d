package com.jd.oa.business.joyspace

import androidx.annotation.Keep

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/16 23:47
 */
@Keep
class JoySpacePage {
    /**
     * 是否有效
     * 1：有效
     * 0：无效
     */
    var valid: Int? = null

    /**
     * 资源ID（链接里拼的id）
     */
    var id: String? = null

    var title: String? = null

    /**
     * 如果是软链，是原始文档的id，否则同资源id
     */
    var bizId: String? = null


    /**
     * 资源类型
     * 1：文档
     * 4：文件夹
     */
    var bizType: Int? = null

    /**
     * 文档类型
     * DOCUMENT（1，"普通文档"）
     * REPORTING（2，"日报周报"），
     * NOTE（3，"鲸盘笔记"），
     * SHEET（4，"普通表格"），
     * LINK（5，"软链"），
     * FILE（6，"文件"），
     * WEBSHEET（7，"葡萄城旧表格类型"），
     * SPREADSHEET（8，"葡萄城新表格类型"），
     * SHIMO_SHEET（10，"石墨表格"），
     * SHIMO_PPT（11，"石墨幻灯片"），
     * SHIMO_WORD（12，"石墨文档"），
     * FOLDER（Q，"文件夹"），
     * TEAM_HOME_PAGE（9，"团队空间主页"），
     * NEW_EDITOR（13，"新文档编辑器"），
     * PERSONAL_INSTRUCTION （14，“个人说明书"），
     * MEETING（15，"会议文档"），
     * MIND（16，"思维导图"），
     * WPS_DOCUMENTPRO（17，"WPS文档"），
     * WPS_TABLE（18，"WPS表格"），
     * WPS_PPT（19，"WPS--PPT"），
     * WPS_MTTABLE（21，"WPS--多维表格"），
     * BOARDMIX（25，"博思白板"）；
     */
    var pageType: Int? = null

    /**
     * 空间ID
     */
    var teamId: String? = null

    /**
     * 用户对该资源权限
     * 0：所有者
     * 1：可编辑
     * 2：可阅读
     * 3：无权限
     */
    var permissionType: Int? = null

    /**
     * 权限枚举值Set，目前用不上，可不新增该字段
     */
    var permissions: Set<String>? = null

    /**
     * 移动端图标
     */
    var mobileIcon: String? = null
}