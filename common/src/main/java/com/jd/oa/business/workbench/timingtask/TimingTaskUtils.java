package com.jd.oa.business.workbench.timingtask;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;

import com.jd.oa.business.workbench.model.ToDo;

import java.util.List;

/**
 * Created by <PERSON> on 2017/8/16.
 */

public class TimingTaskUtils {


    public static void cancel(Context context, TimingTask timingTask) {
        PendingIntent pendingIntent = getPendingIntent(context, timingTask);
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null)
            alarmManager.cancel(pendingIntent);
    }

    public static void set(Context context, long time, TimingTask timingTask) {
        if (context == null || timingTask == null) {
            throw new RuntimeException("context == null or timingTask == null");
        }
        // 设置精确闹钟需要权限，升级target31优化
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    PendingIntent sender = getPendingIntent(context, timingTask);
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, time, sender);
                }
            }
        }
    }

    /***
     * 设置重复的任务
     * @param context
     * @param time
     * @param intervalMillis interval in milliseconds between subsequent repeats
     * of the alarm.  Prior to API 19, if this is one of INTERVAL_FIFTEEN_MINUTES,
     * INTERVAL_HALF_HOUR, INTERVAL_HOUR, INTERVAL_HALF_DAY, or INTERVAL_DAY
     * then the alarm will be phase-aligned with other alarms to reduce the
     * number of wakeups.  Otherwise, the alarm will be set as though the
     * application had called.  As of API 19, all repeating
     * alarms will be inexact and subject to batching with other alarms regardless
     * of their stated repeat interval.
     * @param timingTask
     */
    public static void setInexactRepeating(Context context, long time, long intervalMillis, TimingTask timingTask) {
        if (context == null || timingTask == null) {
            throw new RuntimeException("context == null or timingTask == null");
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.setInexactRepeating(AlarmManager.RTC_WAKEUP, time, intervalMillis, getPendingIntent(context, timingTask));

    }

    private static PendingIntent getPendingIntent(Context context, TimingTask timingTask) {
        Intent intent = new Intent(context, TimingTaskReceiver.class);
        intent.setAction(TimingTaskReceiver.ACTION);
        // 放bundle的原因是因为直接put到intent会出现classnofound
        // see: https://issuetracker.google.com/issues/36914697
        Bundle bundle = new Bundle();
        bundle.putParcelable(TimingTaskReceiver.EXTRA_DATA, timingTask);
        intent.putExtra(TimingTaskReceiver.EXTRA_BUNDLE, bundle);
        PendingIntent alarmIntent = PendingIntent.getBroadcast(context, timingTask.getRequestCode(), intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        return alarmIntent;
    }

    public static void cancel(Context context, List<ToDo> cancelList) {
        if (null == cancelList)
            return;
        for (ToDo toDo : cancelList) {
            ScheduleNotifyTimingTask timingTask = new ScheduleNotifyTimingTask(toDo);
            cancel(context, timingTask);
        }
    }

}
