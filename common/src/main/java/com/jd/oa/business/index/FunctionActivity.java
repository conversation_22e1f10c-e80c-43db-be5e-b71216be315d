package com.jd.oa.business.index;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LifecycleOwner;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.multitask.sliding.ISlidingListener;
import com.jd.oa.multitask.sliding.SlidingCloseHelper;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.StatusBarUtil;
import com.jd.oa.utils.Utils2String;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

/**
 * 功能Activity
 *
 * <AUTHOR>
 */
@Route(DeepLink.ACTIVITY_URI_Function)
public class FunctionActivity extends BaseActivity implements OperatingListener, LifecycleOwner,
        DynamicOperatorListener, ISlidingListener {

    /**
     * window 属性 （类型：long）
     */
    public static final String FLAG_WINDOW_FEATURE = "windowFeature";

    /**
     * 设置旋转屏幕状态（横竖屏）
     */
    public static final String FLAG_SCREEN_ORIENTATION = "screen_orientation";

    /**
     * 打开的Fragment（类型：Class字节码）.class.getName()
     */
    public static final String FLAG_FUNCTION = "function";

    public static final String SHOW_ACTION = "show_action";
    /**
     * 通过路由打开fragment
     */
    public static final String FLAG_ROUTER = "router";

    /**
     * 传递的bean数据
     */
    public static final String FLAG_BEAN = "bean";
    /**
     * 新接口传递的参数
     */
    public static final String FLAG_M_PARAM = "mparam";

    /**
     * 主题
     */
    public static final String FLAG_THEME = "theme";

    private Fragment mFragment;

    private FontScalable fontScalable;
    private boolean isFontSizeScaleableInit = false;
    private List<Integer> ids = new ArrayList<>();
    private LoadingDialog loadingDialog;

    @SuppressLint("MissingSuperCall")
    @SuppressWarnings("unchecked")
    @Override
    protected void onCreate(Bundle b) {
        boolean superCalled = false;
        try {
            String clazzName = getIntent().getStringExtra(FLAG_FUNCTION);
            // 设置window属性
            int windowFeature = getIntent().getIntExtra(FLAG_WINDOW_FEATURE, -1);
            //只能在这里搞了-.-
            if ("com.jd.oa.business.mine.HolidayBankFragment".equals(clazzName)
                    || "com.jd.oa.business.mine.MinePortraitFragment".equals(clazzName)
                    || "com.jd.oa.business.mine.card.MySaasCardFragment".equals(clazzName)) {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//                getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//                getWindow().setStatusBarColor(getColor(R.color.transparent));
//            }
                StatusBarUtil.setTranslucentForImageViewInFragment(this, 10, null);
                StatusBarUtil.setLightMode(this);
            }
//        if (AppBase.isMultiTask()) {
//            setTheme(R.style.MultiTaskTheme);
//        }
            //android O fix bug orientation
            if (android.os.Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
            //try fix https://stackoverflow.com/questions/64709699/application-crashes-sometimes-on-androidx-appcompat-widget-contentframelayout-se
//            getWindow().getDecorView();
            super.onCreate(b);
            superCalled = true;
            if (windowFeature != -1) {
                supportRequestWindowFeature(windowFeature);
            }

            // 设置屏幕横竖屏状态
            int screenOrientation = getIntent().getIntExtra(FLAG_SCREEN_ORIENTATION, -1);
            if (screenOrientation != -1) {
                setRequestedOrientation(screenOrientation);
            }
            setContentView(R.layout.jdme_activity_function);

            Class<? extends Fragment> clazz = null;

            // fragment 会自动保存实例
            if (b == null) {
                ActionBarHelper.init(this);
                boolean show = getIntent().getBooleanExtra(SHOW_ACTION, true);
                if (!show) {
                    ActionBar bar = ActionBarHelper.getActionBar(this);
                    if (bar != null) {
                        bar.hide();
                    }
                }
                String router = getIntent().getStringExtra(FLAG_ROUTER);
                if (!Utils2String.isEmptyWithTrim(router)) {
                    mFragment = (Fragment) Router.build(router).getFragment(this);
                    if (mFragment != null) {
                        mFragment.setArguments(getIntent().getExtras());
                        getSupportFragmentManager().beginTransaction().add(R.id.me_fragment_content, mFragment).commit();
                    }
                } else {
                    try {
                        clazz = (Class<? extends Fragment>) Class.forName(clazzName);
                    } catch (Exception e) {
                        Logger.e("fucntionActivity", e.getMessage());
                    }
                    if (null != clazz) {
                        // 参数继续传给 碎片
                        mFragment = FragmentUtils.replaceWithCommit(this, clazz,
                                R.id.me_fragment_content, false, getIntent().getExtras(),
                                false);
                    }
                }
                if (mFragment instanceof WebFragment2) {
                    WebFragment2 fragment = (WebFragment2) mFragment;
                    fragment.setWebContainer(new FunctionWebContainer(this));
                }
            }
            if (slidingCloseEnable()) {
                SlidingCloseHelper.getInstance(AppBase.getAppContext()).transferStatusBar(this);
            }
        } catch (Exception e) {
            if (!superCalled) {
                super.onCreate(b);
            }
            Logger.e("FunctionActivity", "onCreate Error = " + e.getMessage());
        }
    }

    @Override
    protected Dialog onCreateDialog(int id) {
        ids.add(id);
        return super.onCreateDialog(id);
    }

    public void setBarHide() {
        ActionBar bar = ActionBarHelper.getActionBar(this);
        if (bar != null) {
            bar.hide();
        }
    }

    public void hideApp() {
        if (mFragment != null && mFragment instanceof WebFragment2) {
            WebFragment2 webFragment2 = (WebFragment2) mFragment;
            webFragment2.onMultiTaskButtonClick();
        }
    }

    /**
     * 返回键处理
     */
    @Override
    public void onBackPressed() {
        boolean useSupper = false; // 使用父类来处理返回键
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        if (null != fragments && fragments.size() > 0) {
            for (Fragment f : fragments) {
                // 模拟fragment，只有fragment在前台时，返回键才生效
                if (f instanceof OperatingListener && f.isVisible() && f.isResumed()) {
                    useSupper = ((OperatingListener) f).onOperate(OperatingListener.OPERATE_BACK_PRESS, null);
                }
            }
        }

        // fragment 可能状态丢失，导致 onsaveinstance 异常，这里加入 try.. catch
        try {
            if (!useSupper) {
                super.onBackPressed();
            }
        } catch (Exception e) {

        }
    }

    /**
     * 修改背景
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OperatingListener.OPERATE_CHANGE_SKIN == optionFlag && null != args) {
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        if (ids.size() > 0) {
            for (Integer id : ids) {
                try {
                    dismissDialog(id);
                } catch (Exception e) {
                }
            }
            ids.clear();
        }
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mFragment != null) {
            mFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping.get(requestCode).call(data, resultCode);
        }
    }

    @Override
    public Resources getResources() {
        try {
            String clazzName = getIntent().getStringExtra(FLAG_FUNCTION);
            Class clazz = Class.forName(clazzName);
            if (!isFontSizeScaleableInit) {
                fontScalable = (FontScalable) clazz.getAnnotation(FontScalable.class);
                isFontSizeScaleableInit = true;
            }
            if (fontScalable != null && fontScalable.scaleable()) {
                return setFontScaleFromSettings();
            } else {
                resetFontScale();
            }
        } catch (Exception e) {
        }
        return super.getResources();
    }


    Map<Integer, DynamicCallback> callbackMapping = new HashMap<>();

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        callbackMapping.put(requestCode, callBack);

    }

    @Override
    public void operator(Map<String, Object> param) {
        String action = (String) param.get(KEY_ACTION);
        if (TextUtils.isEmpty(action)) {
            return;
        }
        switch (action) {
            case ACTION_SHOW_LOADING:
                if (loadingDialog == null) {
                    loadingDialog = new LoadingDialog(this);
                }
                loadingDialog.show();
                break;
            case ACTION_DISMISS_LOADING:
                if (loadingDialog != null && loadingDialog.isShowing()) {
                    loadingDialog.hide();
                }
                break;
            default:
                break;
        }
    }

    public Fragment getFragment() {
        return mFragment;
    }

    @Override
    public boolean slidingCloseEnable() {
        if (!AppBase.isMultiTask() || MultiTaskManager.isPad()) {
            return false;
        }
        if (SlidingCloseHelper.slidingWebDisable()) {
            return false;
        }
        if (mFragment == null) {
            return true;
        }
        if (mFragment instanceof WebFragment2) {
            return true;
        }
        return false;
    }

    @Override
    public boolean addFloatActionEnable() {
        return true;
    }

    @Override
    public String getMultiTaskId() {
        return null;
    }

    @Override
    public boolean isRn() {
        return false;
    }
}
