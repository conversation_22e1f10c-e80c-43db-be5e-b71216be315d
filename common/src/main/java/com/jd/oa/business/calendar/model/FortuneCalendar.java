package com.jd.oa.business.calendar.model;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

/**运势日历
 * Created by peidongbiao on 2018/1/5.
 */
@Keep
public class FortuneCalendar implements Parcelable {
    public static final String FORTUNE_ALLOW = "1";
    public static final String FORTUNE_TABOO = "2";

    private String date;
    private String dayOfWeek;
    private String lunarDate;
    private String dayOfMonth;
    private String taboo;
    private String precept;
    private String author;
    private String source;
    private int fortune;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public String getLunarDate() {
        return lunarDate;
    }

    public void setLunarDate(String lunarDate) {
        this.lunarDate = lunarDate;
    }

    public String getDayOfMonth() {
        return dayOfMonth;
    }

    public void setDayOfMonth(String dayOfMonth) {
        this.dayOfMonth = dayOfMonth;
    }

    public String getTaboo() {
        return taboo;
    }

    public void setTaboo(String taboo) {
        this.taboo = taboo;
    }

    public String getPrecept() {
        return precept;
    }

    public void setPrecept(String precept) {
        this.precept = precept;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public int getFortune() {
        return fortune;
    }

    public void setFortune(int fortune) {
        this.fortune = fortune;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.date);
        dest.writeString(this.dayOfWeek);
        dest.writeString(this.lunarDate);
        dest.writeString(this.dayOfMonth);
        dest.writeString(this.taboo);
        dest.writeString(this.precept);
        dest.writeString(this.author);
        dest.writeString(this.source);
        dest.writeInt(this.fortune);
    }

    public FortuneCalendar() {
    }

    protected FortuneCalendar(Parcel in) {
        this.date = in.readString();
        this.dayOfWeek = in.readString();
        this.lunarDate = in.readString();
        this.dayOfMonth = in.readString();
        this.taboo = in.readString();
        this.precept = in.readString();
        this.author = in.readString();
        this.source = in.readString();
        this.fortune = in.readInt();
    }

    public static final Creator<FortuneCalendar> CREATOR = new Creator<FortuneCalendar>() {
        @Override
        public FortuneCalendar createFromParcel(Parcel source) {
            return new FortuneCalendar(source);
        }

        @Override
        public FortuneCalendar[] newArray(int size) {
            return new FortuneCalendar[size];
        }
    };
}
