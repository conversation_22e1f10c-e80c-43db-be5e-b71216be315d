package com.jd.oa.business.smime.utils;

import android.text.TextUtils;

import com.jd.oa.business.smime.model.EmailInfo;
import com.jd.oa.network.FileDownloadListenerAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.cache.FileCache;
import com.liulishuo.filedownloader.FileDownloader;

import java.io.File;
import java.util.ArrayList;

public class AttachDownloadUtil {

    private ArrayList<EmailInfo.AttachmentInfo> mInfos;
    private IAttachCallback mCallback;

    public AttachDownloadUtil(ArrayList<EmailInfo.AttachmentInfo> infos, IAttachCallback callback) {
        mInfos = infos;
        mCallback = callback;
        downloadAttach();
    }

    private int countFlag = 0;
    private boolean failedFlag = false;

    private void downloadAttach() {

        for (int i = 0; i < mInfos.size(); i++) {
            if (!TextUtils.isEmpty(mInfos.get(i).filePath)) {
                mInfos.get(i).filePath = mInfos.get(i).filePath.replace("https://localhst/file:///", "");
                continue;
            }
            if (!TextUtils.isEmpty(mInfos.get(i).src)) {
                countFlag++;
                downloadFile(i, mInfos.get(i).src, mInfos.get(i).name, new IDownloadCallback() {
                    @Override
                    public void done(int index, String filePath) {
                        mInfos.get(index).filePath = filePath;
                        countFlag--;
                        if (countFlag == 0) {
                            finish();
                        }
                    }

                    @Override
                    public void fail(int index, String msg) {
                        countFlag--;
                        failedFlag = true;
                        if (countFlag == 0) {
                            finish();
                        }
                    }
                });
            }
        }
        if (countFlag == 0) {
            finish();
        }
    }

    private synchronized void downloadFile(final int index, String url, String filename, final IDownloadCallback callback) {
        String filePath = FileCache.getInstance().getMailFile().getAbsolutePath() + "/" + System.currentTimeMillis() + "-" + filename;
        FileDownloader.getImpl().create(url)
                .setForceReDownload(true)
                .setPath(filePath)
                .setListener(new FileDownloadListenerAdapter(new SimpleRequestCallback<File>() {
                    @Override
                    public void onSuccess(ResponseInfo<File> responseInfo) {
                        callback.done(index, responseInfo.result.getAbsolutePath());
                    }

                    @Override
                    public void onLoading(long total, long current, boolean isUploading) {
                        super.onLoading(total, current, isUploading);
                    }

                    @Override
                    public void onFailure(HttpException e, String s) {
                        callback.fail(index, s);
                    }
                })).start();
    }

    private void finish() {
        if (!failedFlag) {
            mCallback.done(mInfos);
        } else {
            mCallback.failed("download attach failed");
        }

    }


    public interface IAttachCallback {

        void done(ArrayList<EmailInfo.AttachmentInfo> infos);

        void failed(String msg);
    }

    interface IDownloadCallback {
        void done(int index, String filePath);

        void fail(int index, String msg);
    }

}
