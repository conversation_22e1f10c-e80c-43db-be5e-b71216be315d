package com.jd.oa.business.didi;

import com.jd.oa.AppBase;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.db.greendao.DidiAddressHistoryDB;
import com.jd.oa.db.greendao.DidiAddressHistoryDBDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.Query;

public class DidiAddressHistoryDaoHelper {

    public static void insertData(DidiAddressBean bean) {
        Query query = getDao().queryBuilder().where(DidiAddressHistoryDBDao.Properties.DisplayName.eq(bean.displayName)).build();
        getDao().deleteInTx(query.list());
        getDao().insertInTx(beanToDb(bean));
    }

    public static List<DidiAddressBean> loadAllData() {
        Query query = getDao().queryBuilder().orderDesc(DidiAddressHistoryDBDao.Properties.Id).limit(10).build();
        List<DidiAddressHistoryDB> listDb = query.list();
        List<DidiAddressBean> listBean = new ArrayList<>();
        for (DidiAddressHistoryDB didiAddressHistoryDB : listDb) {
            listBean.add(dbToBean(didiAddressHistoryDB));
        }
        return listBean;
    }

    public static void deleteAll() {
        getDao().deleteAll();
    }

    private static DidiAddressHistoryDBDao getDao() {
        return YxDatabaseSession.getDidiAddressHistoryDBDao(AppBase.getAppContext());
    }

    private static DidiAddressBean dbToBean(DidiAddressHistoryDB db) {
        DidiAddressBean didiAddressBean = new DidiAddressBean();
        didiAddressBean.displayName = db.getDisplayName();
        didiAddressBean.address = db.getAddress();
        didiAddressBean.cityName = db.getCityName();
        didiAddressBean.cityCode = db.getCityCode();
        didiAddressBean.lat = db.getLat();
        didiAddressBean.lng = db.getLng();
        didiAddressBean.hypothetical= db.getHypothetical();
        return didiAddressBean;
    }

    private static DidiAddressHistoryDB beanToDb(DidiAddressBean bean) {
//        return new DidiAddressHistoryDB(bean.displayName, bean.address, bean.cityName, bean.cityCode, bean.lat, bean.lng, null);
        return new DidiAddressHistoryDB(bean.displayName, bean.address, bean.cityName, bean.cityCode, bean.lat, bean.lng, null, bean.hypothetical);
    }

}
