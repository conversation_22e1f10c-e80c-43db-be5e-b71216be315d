package com.jd.oa.business.index

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import androidx.activity.OnBackPressedDispatcher
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.screen.ScreenController
import com.jd.oa.fragment.web.WebConfig
import com.qmuiteam.qmui.util.QMUIStatusBarHelper

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/26 14:15
 */
class FunctionWebContainer(val activity: FunctionActivity) : IWebContainer {

    private var screenController = ScreenController(activity)

    override fun getContext(): Context = activity

    override fun onThemeChanged(immersive: Boolean, theme: String) {
        runCatching {
            if (TextUtils.isEmpty(theme)) {
                return
            }
            if (WebConfig.THEME_DARK == theme) {
                QMUIStatusBarHelper.setStatusBarDarkMode(activity)
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(activity)
            }
            if (!immersive) {
                if (WebConfig.THEME_DARK == theme) {
                    activity.window.statusBarColor = Color.BLACK
                } else {
                    activity.window.statusBarColor = Color.WHITE
                }
            }
        }
    }

    override fun setImmersiveMode() {
        QMUIStatusBarHelper.translucent(activity)
    }

    override fun configLayout(root: View?, immersive: String, theme: String) {
    }

    override fun onBackPressDispatcher(): OnBackPressedDispatcher = activity.onBackPressedDispatcher

    override fun close() {
        kotlin.runCatching {
            activity.finish()
        }
    }

    //当加载失败时是否展示title
    override fun showTitleWhenFailed(): Boolean = true

    override fun setOrientation(orientation: String, autorotate: Boolean) {
        super.setOrientation(orientation, autorotate)
        activity.runOnUiThread {
            screenController.setOrientation(orientation, autorotate)
        }
    }

    override fun destroy() {
        super.destroy()
        screenController.destroy()
    }
}