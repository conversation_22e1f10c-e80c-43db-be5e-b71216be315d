package com.jd.oa.business.joyspace

import com.jd.oa.network.ResultWrapper
import com.jd.oa.network.post

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/17 00:05
 */


/**
 * 查询JoySpace地址的信息
 */
suspend fun queryPageInfo(
    link: String,
    scene: String,
    returnPermission: Boolean = false
): ResultWrapper<JoySpacePage> {
    val result = post<JoySpacePage>("joyspace.third.identify") {
        mutableMapOf(
            Pair("link", link),
            Pair("scene", scene),
            Pair("returnPermission", returnPermission),
        )
    }
    if (result.isSuccessful) {
        return ResultWrapper(true, result.data)
    }
    return ResultWrapper(false, null, result.errorMessage)

}