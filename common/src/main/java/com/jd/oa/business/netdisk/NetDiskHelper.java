package com.jd.oa.business.netdisk;

import static com.jd.oa.JDMAConstants.Mobile_Event_PlatformSafety_AnyPage_FilePreview;
import static com.jd.oa.bundles.netdisk.utils.NetDiskBuryPoint.PointEvent.PREVIEW_FILE;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.fragment.app.FragmentActivity;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.JDMAPages;
import com.jd.oa.bundles.netdisk.NetDiskAlbumInterface;
import com.jd.oa.bundles.netdisk.NetDiskBuryPointInterface;
import com.jd.oa.bundles.netdisk.NetDiskInterface;
import com.jd.oa.bundles.netdisk.NetDiskPermissionInterface;
import com.jd.oa.bundles.netdisk.NetDiskSdk;
import com.jd.oa.bundles.netdisk.NetDiskTokenInterface;
import com.jd.oa.bundles.netdisk.NetDiskWebViewInterface;
import com.jd.oa.bundles.netdisk.base.LoadDataCallback;
import com.jd.oa.bundles.netdisk.myfile.bean.SendFileInfo;
import com.jd.oa.bundles.netdisk.opt.FileOptActivity;
import com.jd.oa.bundles.netdisk.utils.NetDiskBuryPoint;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;
import com.yu.bundles.album.AlbumListener;
import com.yu.bundles.album.ConfigBuilder;
import com.yu.bundles.album.MaeAlbum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网盘相关接口
 * Created by peidongbiao on 2018/6/27.
 */

public class NetDiskHelper {

    /**
     * 网盘发送文件到咚咚的接口
     */
    private static NetDiskInterface sSendFileInterface;

    /**
     * 网盘刷新token接口
     */
    private static NetDiskTokenInterface sNetDiskTokenInterface;

    /**
     * 网盘埋点接口
     */
    private static NetDiskBuryPointInterface sNetDiskBuryPointInterface;


    // web接口
    private static NetDiskWebViewInterface sNetDiskWebViewInterface;

    static {

        final ImDdService imDdService = AppJoint.service(ImDdService.class);
        MaeAlbum.setStyle(R.style.MeAlum);
        MaeAlbum.setImageEngine(new GlideEngine());
        sSendFileInterface = new NetDiskInterface() {

            Gson gson = new Gson();

            @Override
            public boolean sendFile(Activity activity, List<SendFileInfo> list) {
                if (list == null || list.isEmpty()) return false;
                try {
                    if (list.size() == 1) {
                        sendSingleFile(activity, list.get(0));
                    } else {
                        sendMultiFile(activity, list);
                    }
                    return true;
                } catch (Exception e) {
                    return false;
                }
            }

            private void sendSingleFile(Activity activity, SendFileInfo sendFileInfo) {
                Map<String, Object> map = new HashMap<>();
                map.put("url", sendFileInfo.downloadUrl);
                map.put("fileName", sendFileInfo.resourceName);
                map.put("size", sendFileInfo.resourceSize);
                map.put("ext", deletePointFromSuffix(sendFileInfo.suffix));
                imDdService.sendSingleFile(activity, gson.toJson(map));
            }

            private void sendMultiFile(Activity activity, List<SendFileInfo> fileInfos) {
                List<Map<String, Object>> list = new ArrayList<>();
                for (SendFileInfo sendFileInfo : fileInfos) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("url", sendFileInfo.downloadUrl);
                    map.put("fileName", sendFileInfo.resourceName);
                    map.put("size", sendFileInfo.resourceSize);
                    map.put("ext", deletePointFromSuffix(sendFileInfo.suffix));
                    list.add(map);
                }
                imDdService.sendMultiFile(activity, gson.toJson(list));
            }

            private String deletePointFromSuffix(String extName) {
                if (!TextUtils.isEmpty(extName) && extName.startsWith(".")) {
                    extName = extName.substring(1);
                }
                return extName;
            }

            @Override
            public boolean printFile(Activity activity, List<SendFileInfo> list) {
                if (CollectionUtil.isEmptyOrNull(list)) {
                    return false;
                }
                SendFileInfo fileInfo = list.get(0);
                return AppJoint.service(AppService.class).onJDCloudPrint(fileInfo.downloadUrl, fileInfo.resourceName, fileInfo.resourceSize, fileInfo.suffix);
            }

            @Override
            public void getPreUrl(final Activity activity, String fileUrl, String fileName, final LoadDataCallback<Map<String, String>> callback) {
                NetworkUtils.getPreviewUrl(fileUrl, fileName, new SimpleRequestCallback<String>(null, false) {

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        callback.onDataNotAvailable(info, info);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        try {
                            ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                            }.getType());
                            Map<String, String> map = response.getData();
                            if (!map.containsKey("docPreviewUrl")) {
                                ToastUtils.showToast(activity, "load fileurl failed");
                            } else {
                                Map<String, String> mapResult = new HashMap<>();
                                mapResult.put("url", map.get("docPreviewUrl"));
                                mapResult.put("title", map.get("fileName"));
                                callback.onDataLoaded(mapResult);
                            }
                        } catch (Exception e) {
                            ToastUtils.showToast(activity, "load fileurl failed");
                            callback.onDataNotAvailable("exception", "load fileurl failed");
                        }

                    }
                });
            }

            @Override
            public boolean isOpenDocPreview() {
                //TODO 开关
                String preView = ConfigurationManager.get().getEntry("doc.preview", "0");
                return "1".equals(preView);
            }
        };

        sNetDiskTokenInterface = new NetDiskTokenInterface() {
            @Override
            public void refreshTokenRequest(final NetDiskTokenCallBack netDiskTokenCallBack) {
                NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(null, false) {

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                        }.getType());
                        Map<String, String> map = response.getData();
                        netDiskTokenCallBack.callback(map.get("third_token"));
                    }
                });
            }
        };
        //埋点
        sNetDiskBuryPointInterface = new NetDiskBuryPointInterface() {

            @Override
            public void buryEventPoint(NetDiskBuryPoint.PointEvent pointEvent, Long aLong, String s) {
                String event = null;
                switch (pointEvent) {
//                    case OPEN_CLICK:
//                        event = PageEventUtil.EVENT_NETDISK_OPEN;
//                        break;
                    case FILE_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_netdisk_click;
                        break;
                    case MOVE_CLICK:
//                        event = PageEventUtil.EVENT_NETDISK_MOVE;
                        event = JDMAConstants.mobile_myNetdisk_move_click;
                        break;
                    case SEND_CLICK:
//                        event = PageEventUtil.EVENT_NETDISK_SEND;
                        event = JDMAConstants.mobile_myNetdisk_send_click;
                        break;
                    case DELETE_CLICK:
//                        event = PageEventUtil.EVENT_NETDISK_DELETE;
                        event = JDMAConstants.mobile_myNetdisk_delete_click;
                        break;
                    case SAVE_2_NET_DISK_CLICK:
//                        event = PageEventUtil.EVENT_NETDISK_SAVE;
                        event = JDMAConstants.mobile_timline_file_saveToNetDisk_click;
                        break;
                    case UPLOAD_CLICK:
//                        event = PageEventUtil.EVENT_NETDISK_UPLOAD_FILE;
                        event = JDMAConstants.mobile_myNetdisk_upload_file_click;
                        break;
                    case PRINT_CLICK:
//                        event = PageEventUtil.EVENT_PRINT_FROM_NETDISK;
                        event = JDMAConstants.mobile_myNetdisk_print_click;
                        break;
                    case TRANSFER_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_transfer_list_click;
                        break;
                    case RENAME_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_rename_click;
                        break;
                    case ADD_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_new_add_click;
                        break;
                    case NEW_FILE_REQUEST_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_new_file_request_click;
                        break;
                    case SELECT_FILE_PATH_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_select_file_path_click;
                        break;
                    case SELECT_VALIDITY_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_select_validity_click;
                        break;
                    case SELECT_FILE_PATH_NEW_FOLDER_CLICK:
                        event = JDMAConstants.mobile_myNetdisk_select_file_path_new_folder_click;
                        break;
                    case SHARE_COPY_LINK:
                        event = JDMAConstants.mobile_myNetdisk_share_copy_link;
                        break;
                }
                if (event == null) {
                    return;
                }
                JDMAUtils.onEventClick(event, event);
//                PageEventUtil.onEvent(Apps.getAppContext(), event);
            }

            @Override
            public void clickEvent(NetDiskBuryPoint.PointEvent pointEvent, Map<String, String> map) {
                if (pointEvent == PREVIEW_FILE) {
                    Map<String, String> param1 = new HashMap<>();
                    param1.put("appId", "201806210262");
                    param1.put("from_mobule", "jpan");
                    if (map != null) {
                        param1.putAll(map);
                    }
                    JDMAUtils.clickEvent(JDMAPages.Mobile_Page_PlatfromSafety_AnyPage, Mobile_Event_PlatformSafety_AnyPage_FilePreview, param1);

                }
            }
        };


        // 开网页
        sNetDiskWebViewInterface = new NetDiskWebViewInterface() {
            @Override
            public void open(String url, String title) {
                WebBean webBean = new WebBean(url, WebConfig.H5_NATIVE_HEAD_SHOW);
                webBean.setTitle(title);
                Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
                intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean);
                AppBase.getAppContext().startActivity(intent);
            }
        };
    }

    public static void saveFileToNetDisk(Activity activity, ToNetDiskBean bean, int requestCode) {
        Map<String, String> params = new HashMap<>();
        params.put("token", bean.getToken());
        params.put("userCode", bean.getUserCode());
        params.put("thirdTimeStamp", bean.getThirdTimestamp());
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("file_name", bean.getFileName());
        jsonObject.addProperty("file_url", bean.getFileUrl());
        jsonObject.addProperty("file_size", bean.getFileSize());
        jsonObject.addProperty("file_suffix", bean.getFileSuffix());
        jsonObject.addProperty("erp", bean.getUserName());
        if (!TextUtils.isEmpty(bean.getSource())) {
            jsonObject.addProperty("source", bean.getSource());
        } else {
            jsonObject.addProperty("source", "");
        }

        NetDiskSdk.with(activity)
                .setStyle(R.style.NetDiskStyle)
                .setParam(params)
                .goForward(requestCode, jsonObject.toString());
    }

    public static void openNetDisk(final Activity activity, ToNetDiskBean bean) {
        NetDiskPermissionInterface netDiskPermissionInterface = new NetDiskPermissionInterface() {
            @Override
            public void requestPermission(final OnPermissionCallBack onPermissionCallBack, String... permissions) {
                if (AppBase.getTopActivity() != null)
                    PermissionHelper.requestPermissions(AppBase.getTopActivity()
                            , AppBase.getTopActivity().getResources().getString(R.string.me_request_permission_title_normal)
                            , AppBase.getTopActivity().getResources().getString(R.string.me_request_permission_storage_normal)
                            , new RequestPermissionCallback() {
                                @Override
                                public void allGranted() {
                                    onPermissionCallBack.allGranted();
                                }

                                @Override
                                public void denied(List<String> deniedList) {
                                    onPermissionCallBack.denied(deniedList);
                                }
                            }, permissions);
            }
        };
        NetDiskAlbumInterface netDiskAlbumInterface = new NetDiskAlbumInterface() {

            @Override
            public void startPreview(Context context, ArrayList arrayList, int i) {
                MaeAlbum.startPreview(context, arrayList, i, false, false, false);
            }

            @Override
            public void openAlbum(Activity activity, NetDiskAlbumOpenFlag openFlag, int maxNum, final NetDiskAlbumListener listener) {
                MaeAlbum maeAlbum = MaeAlbum.from(activity)
                        .column(3)
                        .setIsShowCapture(true);
                switch (openFlag) {
                    case IMAGE:
                        maeAlbum.fileType(ConfigBuilder.FILE_TYPE.IMAGE);
                        break;
                    case VIDEO:
                        maeAlbum.fileType(ConfigBuilder.FILE_TYPE.VIDEO);
                        break;
                    case IMAGE_VIDEO:
                        maeAlbum.fileType(ConfigBuilder.FILE_TYPE.IMAGE_AND_VIDEO);
                        break;
                }
                maeAlbum.maxSize(maxNum);
                maeAlbum.forResult(new AlbumListener() {
                    @Override
                    public void onSelected(List<String> list) {
                        listener.onSelected(list);
                    }

                    @Override
                    public void onFull(List<String> list, String s) {
                        listener.onFull(list, s);
                    }
                });
            }
        };
        String maxNum = ConfigurationManager.get().getEntry("jdpan.album.checked.max", "9");
        int val = StringUtils.convertToInt(maxNum, 9);
        Map<String, String> params = new HashMap<>();
        params.put("token", bean.getToken());
        params.put("userCode", bean.getUserCode());
        params.put("thirdTimeStamp", bean.getThirdTimestamp());
        NetDiskSdk.with(activity)
                .setStyle(R.style.NetDiskStyle)
                .setAlbum(netDiskAlbumInterface)
                .setParam(params)
                .setMaxSelectNum(val)
                .setPermissionInterface(netDiskPermissionInterface)
                .setLocale(LocaleUtils.getUserSetLocale(activity))
                .go();
    }

    public static void optFileFromNetdisk(Activity activity, ToNetDiskBean bean, int requestCode, int optMaxSize) {
        Map<String, String> params = new HashMap<>();
        params.put("token", bean.getToken());
        params.put("userCode", bean.getUserCode());
        params.put("thirdTimeStamp", bean.getThirdTimestamp());
        String local = "en";
        if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).indexOf("zh") >= 0) {
            local = "zh";
        }
        NetDiskSdk.with(activity)
                .setStyle(R.style.NetDiskStyle)
                .setParam(params)
                .goOptFile(requestCode, local, optMaxSize);
    }

    public static void optFileFromNetdisk(FragmentActivity activity, ToNetDiskBean bean, int optMaxSize, AutoUnregisterResultCallback<ActivityResult> callback) {
        Map<String, String> params = new HashMap<>();
        params.put("token", bean.getToken());
        params.put("userCode", bean.getUserCode());
        params.put("thirdTimeStamp", bean.getThirdTimestamp());
        String local = "en";
        if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).contains("zh")) {
            local = "zh";
        }
        NetDiskSdk.with(activity)
                .setStyle(R.style.NetDiskStyle)
                .setParam(params);

        Intent intent = new Intent(activity, FileOptActivity.class);
        intent.putExtra("local", local);
        intent.putExtra("max", optMaxSize);
        String key = "optFileFromNetdisk_" + System.currentTimeMillis();
        ActivityResultLauncher<Intent> register = activity.getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), callback);
        callback.setLauncher(register);
        register.launch(intent);
//        activity.startActivityForResult(intent, reqCode);
    }


    public static NetDiskInterface getSendFileInterface() {
        return sSendFileInterface;
    }

    public static NetDiskTokenInterface getNetDiskTokenInterface() {
        return sNetDiskTokenInterface;
    }

    public static NetDiskBuryPointInterface getNetDiskBuryPointInterface() {
        return sNetDiskBuryPointInterface;
    }

    public static NetDiskWebViewInterface getNetDiskWebViewInterfac() {
        return sNetDiskWebViewInterface;
    }
}