package com.jd.oa.business.workbench.timingtask;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.jd.oa.cache.Logger;

/**
 * Created by <PERSON> on 2017/8/16.
 */

public class TimingTaskReceiver extends BroadcastReceiver {
    public static final String ACTION = "com.jd.oa.business.workbench.timingtask.TimingTaskAlarmReceiver";
    public static final String EXTRA_DATA = "data";
    public static final String EXTRA_BUNDLE = "bundle";

    @Override
    public void onReceive(Context context, Intent intent) {
        Logger.save("TimingTaskReceiver onReceive");
        intent.setExtrasClassLoader(getClass().getClassLoader());
        Bundle bundle = intent.getParcelableExtra(EXTRA_BUNDLE);
        TimingTask task = bundle.getParcelable(EXTRA_DATA);
        task.executor(context, intent);
    }
}
