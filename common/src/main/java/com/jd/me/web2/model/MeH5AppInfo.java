package com.jd.me.web2.model;


import com.google.gson.annotations.SerializedName;

/*
*目前服务器返回的可能json
* 1.如果需要更新
* {
    "bundleType":"h5",
    "moduleId":"20190329",
    "bundleVersion":10,
    "processFlag":-1,
    "filePath":"/storage/emulated/0/Android/data/package/cache/MaeBundleManager/5.4.0/h5/20190329/0.zip",
    "unzipFilePath":"",
    "attachInfo":""
}
*
*2.如果不需要更新
{
    "bundleType":"h5",
    "moduleId":"20190329",
    "bundleVersion":10,
    "processFlag":-3,
    "filePath":"",
    "unzipFilePath":"",
    "attachInfo":""
}
*
* */
@SuppressWarnings({"unused", "WeakerAccess"})
public class MeH5AppInfo {
    /**
     * -2: 关闭，删除本地组件；
     * -1: 有新组件，从newBundleInfo中解压使用；
     * 0: 回滚到上一版本，bundleManager会将
     * >0: 表示回滚到指定的版本号，回滚时从rollbackBundle中拿到回滚地址。
     */
    public static final int PROCESS_FLAG_NEW = -1;
    //    public static final int PROCESSFLAG_CLOSE = -2;
    public static final int PROCESS_FLAG_DO_NOTHING = -3;
    //    public static final int PROCESSFLAG_ROLL = 0;
    @SerializedName("moduleId")
    private String appId;
    private String appName;
    private String appShowName;
    private String appIcon;
    @SerializedName("attachInfo")
    private String appDes;
    @SerializedName("bundleType")
    private Type appType;
    private String appVersion;
    @SerializedName("bundleVersion")
    private int appBuildVersion;
    private String Token;
    @SerializedName("filePath")
    private String path;
    private String appKey;
    private int processFlag;

    public MeH5AppInfo() {
    }

    public MeH5AppInfo(String appId) {
        this(appId, null);
    }

    public MeH5AppInfo(String appId, String path) {
        this.appId = appId;
        this.path = path;
        setAppBuildVersion(0);
        setAppType(Type.H5);
    }

    public int getProcessFlag() {
        return processFlag;
    }

    public void setProcessFlag(int processFlag) {
        this.processFlag = processFlag;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppShowName() {
        return appShowName;
    }

    public void setAppShowName(String appShowName) {
        this.appShowName = appShowName;
    }

    public String getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(String appIcon) {
        this.appIcon = appIcon;
    }

    public String getAppDes() {
        return appDes;
    }

    public void setAppDes(String appDes) {
        this.appDes = appDes;
    }

    public Type getAppType() {
        return appType;
    }

    public void setAppType(Type appType) {
        this.appType = appType;
    }

    public void setAppType(String appType) {
        switch (appType.toLowerCase()) {
            case "rn":
                this.appType = Type.RN;
                break;
            case "h5":
                this.appType = Type.H5;
                break;
            default:
                this.appType = Type.OTHER;
                break;
        }
    }

    public String getAppTypeStr() {
        switch (appType) {
            case RN:
                return "rn";
            case H5:
                return "h5";
        }
        return "other";
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public int getAppBuildVersion() {
        return appBuildVersion;
    }

    public void setAppBuildVersion(int appBuildVersion) {
        this.appBuildVersion = appBuildVersion;
    }

    public String getToken() {
        return Token;
    }

    public void setToken(String token) {
        Token = token;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public enum Type {H5, RN, OTHER}
}
