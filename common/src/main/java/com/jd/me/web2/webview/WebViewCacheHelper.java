package com.jd.me.web2.webview;

import android.app.Activity;
import android.content.Context;
import android.content.MutableContextWrapper;
import android.os.Handler;
import android.os.Looper;
import android.os.MessageQueue;
import android.view.ViewGroup;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.ConfigurationManager;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;

import java.lang.ref.SoftReference;
import java.util.HashMap;
import java.util.Map;
import java.util.Stack;

import cn.com.libdsbridge.webviewsdk.LibWebCoreHelper;


/*
 * webview缓存
 *
 * Time: 2024/6/12
 * Author: qudongshi
 * Description:
 */
public class WebViewCacheHelper {

    private static final String TAG = "WebViewCacheHelper";

    private static volatile WebViewCacheHelper instance;

    private int MAX_SIZE = 1;

    private Stack<JMEWebview> webViews;

    private Handler handler;

    /**
     * 避免被直接实例
     */
    private WebViewCacheHelper() {
        handler = new Handler(Looper.getMainLooper());
        preload();
    }

    /**
     * 获取cache实例
     *
     * @return
     */
    public static WebViewCacheHelper getInstance() {
        MELogUtil.localD(TAG, "getInstance");
        if (instance == null) {
            synchronized (WebViewCacheHelper.class) {
                if (instance == null) {
                    instance = new WebViewCacheHelper();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化
     */
    public void init() {
        if (AppBase.getAppContext() == null) {
            MELogUtil.localW(TAG, "init context is null");
            return;
        }
        MELogUtil.localD(TAG, "init");
        webViews = new Stack<>();
        prepare();
    }


    /**
     * 准备
     */
    private void prepare() {
        if (!isPrepareEnable()) {
            return;
        }
        MELogUtil.localD(TAG, "prepare cache size " + webViews.size());
        if (webViews.size() < MAX_SIZE) {
            Looper.getMainLooper().getQueue().addIdleHandler(new MessageQueue.IdleHandler() {
                @Override
                public boolean queueIdle() {
                    MELogUtil.localD(TAG, "prepare queueIdle cache size " + webViews.size());
                    if (webViews.size() < MAX_SIZE) {
                        JMEWebview webview = create();
                        if (webview != null) {
                            webViews.push(webview);
                        }
                        prepare();
                    }
                    return false;
                }
            });
        } else {
            MELogUtil.localD(TAG, "prepare --> fully cache size " + webViews.size());
        }
    }

    /**
     * 获取webview & 替换context
     *
     * @return JMEWebview
     */
    public JMEWebview getWebView(Context context) {
        JMEWebview webview = fetchWebView(context);
        if (webview != null) {
            MELogUtil.localD(TAG, "getWebView --> replace context ");
            Context cxt = webview.getContext();
            if (cxt instanceof MutableContextWrapper) {
                ((MutableContextWrapper) cxt).setBaseContext(context);
            }
        } else {
            webview = create(context);
        }
        return webview;
    }

    /**
     * 获取webview
     *
     * @param context
     * @return
     */
    private JMEWebview fetchWebView(Context context) {
        if (webViews == null || webViews.size() == 0) {
            MELogUtil.localW(TAG, "getWebView --> no cache");
            return create(context);
        }
        if (webViews.size() > 0) {
            JMEWebview jmeWebview = webViews.pop();
            prepare();
            if (jmeWebview != null) {
                MELogUtil.localD(TAG, "fetchWebView webview hash code = " + jmeWebview.hashCode());
                return jmeWebview;
            }
        }
        return create(context);
    }

    /**
     * 创建webview
     *
     * @return
     */
    private JMEWebview create() {
        MELogUtil.localD(TAG, "create");
        boolean hasInitialized = LibWebCoreHelper.hasInitialized;
//        MELogUtil.localE("JDHybrid", "hasInitialized = " + hasInitialized);
//        MELogUtil.onlineE("JDHybrid", "hasInitialized = " + hasInitialized);
        if (hasInitialized) {
            JMEWebview webView = new JMEWebview(new MutableContextWrapper(AppBase.getAppContext()));
            return webView;
        }
        return null;
    }


    /**
     * 创建webview
     *
     * @param context
     * @return
     */
    private JMEWebview create(Context context) {
        MELogUtil.localD(TAG, "create need context");
        return new JMEWebview(context);
    }

    /**
     * 清空
     */
    public void clear() {
        MELogUtil.localD(TAG, "clear");
        webViews.clear();
        handler = null;
        instance = null;
    }


    /**
     * 预加载页面
     */
    private void preload() {
        if (!isPreloadEnable()) {
            return;
        }
        MELogUtil.localD(TAG, "preloadWebView");
        handler.post(new Runnable() {
            @Override
            public void run() {
                Looper.getMainLooper().getQueue().addIdleHandler(new MessageQueue.IdleHandler() {
                    @Override
                    public boolean queueIdle() {
                        MELogUtil.localD(TAG, "preload local html");
                        WebView webView = create();
                        if (webView != null) {
                            webView.getSettings().setJavaScriptEnabled(true);
                            webView.setWebViewClient(new WebViewClient());
                            webView.loadUrl("file:///android_asset/error.html");
                            webView.destroy();
                        }
                        return false;
                    }
                });
            }
        });
    }

    final String ANDROID_WEBVIEW_PREPARE_ENABLE = "android.webview.prepare.enable";
    final String ANDROID_WEBVIEW_PRELOAD_ENABLE = "android.webview.preload.enable";
    final String ANDROID_WEBVIEW_FLOATING_CACHE_DISABLE = "android.webview.floating.cache.disable";

    private boolean isPrepareEnable() {
        if (!LibWebCoreHelper.hasInitialized) {
            return false;
        }
        String val = ABTestManager.getInstance().getConfigByKey(ANDROID_WEBVIEW_PREPARE_ENABLE, "0");
        if ("1".equals(val)) {
            return true;
        }
        return false;
    }

    private boolean isPreloadEnable() {
        if (!LibWebCoreHelper.hasInitialized) {
            return false;
        }
        String val = ABTestManager.getInstance().getConfigByKey(ANDROID_WEBVIEW_PRELOAD_ENABLE, "0");
        if ("1".equals(val)) {
            return true;
        }
        return false;
    }

    private Map<String, SoftReference<JMEWebview>> floatWebViews = new HashMap<>();
    private Map<String, Integer> floatWebViewMapping = new HashMap<>();
    private Map<String, String> floatTitles = new HashMap<>();

    /**
     * 缓存悬浮窗中的webview
     *
     * @param key
     * @param jmeWebview
     */
    public boolean saveFloatWebView(String key, JMEWebview jmeWebview, String title) {
        if (floatWebViews == null) {
            return false;
        }
        if (floatWebViewMapping.containsKey(key)) {
            // 使用中
            if (floatWebViewMapping.get(key) != jmeWebview.hashCode()) {
                // 非同一个
                return false;
            }
        }
        unbindWebView(jmeWebview);
        SoftReference<JMEWebview> val = new SoftReference<>(jmeWebview);
        floatWebViews.put(key, val);
        floatTitles.put(key, title);
        floatWebViewMapping.remove(key);
        return true;
    }

    /**
     * 删除缓存
     *
     * @param key
     */
    public void removeFloatWebView(String key) {
        if (floatWebViews != null && floatWebViews.containsKey(key)) {
            floatWebViews.remove(key);
            floatTitles.remove(key);
            floatWebViewMapping.remove(key);
        }
    }

    /**
     * 获取悬浮窗中的 webview
     *
     * @param key
     * @param activity
     * @return
     */
    public JMEWebview getFloatWebView(String key, Activity activity) {
        if (floatWebViewMapping.containsKey(key)) {
            // 使用中
            return null;
        }
        SoftReference<JMEWebview> val = floatWebViews.get(key);
        if (val != null && val.get() != null) {
            bindWebView(val.get(), activity);
            floatWebViewMapping.put(key, val.get().hashCode());
            return val.get();
        } else if (val != null && val.get() == null) {
            removeFloatWebView(key);
        }
        return null;
    }

    /**
     * 获取标题
     *
     * @param key
     * @return
     */
    public String getFloatTitle(String key) {
        if (floatTitles.containsKey(key)) {
            return floatTitles.get(key);
        }
        return "";
    }

    /**
     * @param jmeWebview
     */
    private void unbindWebView(JMEWebview jmeWebview) {
        ViewGroup parentView = (ViewGroup) jmeWebview.getParent();
        if (parentView != null) {
            parentView.removeView(jmeWebview);
        }
        Context cxt = jmeWebview.getContext();
        if (cxt instanceof MutableContextWrapper) {
            ((MutableContextWrapper) cxt).setBaseContext(cxt.getApplicationContext());
        }
    }

    public void clearCache() {
        if (floatWebViews != null) {
            floatWebViews.clear();
        }
        if (floatWebViewMapping != null) {
            floatWebViewMapping.clear();
        }
        if (floatTitles != null) {
            floatTitles.clear();
        }
    }

    /**
     * 绑定webview
     *
     * @param jmeWebview
     * @param activity
     */
    private void bindWebView(JMEWebview jmeWebview, Activity activity) {
        Context cxt = jmeWebview.getContext();
        if (cxt instanceof MutableContextWrapper) {
            ((MutableContextWrapper) cxt).setBaseContext(activity);
        }
    }

    /**
     * 是否关闭web 缓存
     *
     * @return
     */
    public boolean floatCacheDisable() {
        String val = ConfigurationManager.get().getEntry(ANDROID_WEBVIEW_FLOATING_CACHE_DISABLE, "0");
        if ("1".equals(val)) {
            return true;
        }
        return false;
    }

}
