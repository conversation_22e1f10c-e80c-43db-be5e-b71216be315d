package com.jd.me.web2.model;

@SuppressWarnings("unused")
public class MeH5AppToken {
    private String deviceId;
    private String nativeVersion;
    private String token;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getNativeVersion() {
        return nativeVersion;
    }

    public void setNativeVersion(String nativeVersion) {
        this.nativeVersion = nativeVersion;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
