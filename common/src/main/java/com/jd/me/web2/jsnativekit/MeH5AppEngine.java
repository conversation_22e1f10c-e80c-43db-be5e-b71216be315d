package com.jd.me.web2.jsnativekit;

import android.app.Application;
import android.content.Context;

import com.google.gson.Gson;
import com.jd.me.web2.model.MeH5AppInfo;
import com.jd.me.web2.model.MeH5AppToken;
import com.jd.oa.AppBase;
import com.jd.oa.utils.Utils2File;
import com.jd.oa.utils.Utils2String;
import com.jd.oa.utils.Utils2Zip;

import java.io.File;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.com.libbundlemanager.bean.BundleManInitInfo;
import cn.com.libbundlemanager.bean.BundleTypeInfo;
import cn.com.libbundlemanager.manager.BundleManager;

import static com.jd.me.web2.model.MeH5AppInfo.PROCESS_FLAG_DO_NOTHING;
import static com.jd.me.web2.model.MeH5AppInfo.PROCESS_FLAG_NEW;
import static com.jd.oa.utils.Utils2File.copyFile;
import static com.jd.oa.utils.Utils2File.createFileByDeleteOldFile;
import static com.jd.oa.utils.Utils2File.moveFile;
import static com.jd.oa.utils.Utils2IO.readFile2String;
import static com.jd.oa.utils.Utils2IO.writeFileFromString;


@SuppressWarnings({"WeakerAccess"})
public class MeH5AppEngine {
    public static final String TAG = "MeH5AppEngine";

    public static final int ERR_MOVE_FILE_FAIL = -1;
    public static final int ERR_WRITE_INFO_FAIL = -2;
    public static final int ERR_DOWNLOAD_FAIL = -3;
    public static final int ERR_WRITE_NO_INFO = -4;
    public static final int ERR_APP_INFO = -5;
//    public static final int ERR_WRITE_CACHE = -6;
    public static final int ERR_INSTALL_FAIL = -7;
    public static final int ERR_APP_FILE = -8;
    //正式网址
    private static final String BUNDLE_MANAGE_URL_PRE = "https://chopin.jd.com/api/bundle/v1/findBundleInfo";
    //TODO appid硬编码
    private static final String BUNDLE_MANAGE_APP_ID_PRE = "DFCD672A8BFE6688F5BFA0B88F1080C8";
    //测试网址
    @SuppressWarnings("SpellCheckingInspection")
//    private static final String BUNDLE_MANAGE_URL_PRE = "http://chopin-test.tripitaka.svc.hcyf.n.jd.local/api/bundle/v1/findBundleInfo";
//    private static final String BUNDLE_MANAGE_APP_ID_PRE = "C4CA4238A0B923820DCC509A6F75849B";

    private static final String DEL = ".del";
    private static final String INFO = ".info";
    private static final String H_5 = "h5";
    private static final String UTF_8 = "utf-8";
    private static final String INDEX_HTML = "index.html";

    private static final String BUNDLE = ".bundle";
    private static final String FILE = "file" + File.pathSeparatorChar + File.separatorChar + File.separatorChar;
    private static final ExecutorService executor = Executors.newSingleThreadExecutor();
    private volatile static MeH5AppEngine sInstance;
    private File mCacheDir;
    private Application application;

    private MeH5AppEngine() {
        if (mCacheDir == null) {
            initCacheDir(AppBase.getAppContext());
        }
    }

    public static MeH5AppEngine getInstance() {
        if (sInstance == null) {
            synchronized (MeH5AppEngine.class) {
                if (sInstance == null) {
                    sInstance = new MeH5AppEngine();
                }
            }
        }
        return sInstance;
    }

    public void openApp(final Context context, final MeH5AppInfo openInfo, final MeH5AppToken token, final MeH5AppOpenListener meH5AppOpenListener) {
        executor.execute(new Runnable() {
            @Override
            public void run() {
//                Log.d("TH1111", "-------------1-------------");
                openAppInternal(context, openInfo, token, meH5AppOpenListener);
//                Log.d("TH1111", "-------------2-------------");
            }
        });
    }

    private void openAppInternal(final Context context, final MeH5AppInfo openInfo, MeH5AppToken token, final MeH5AppOpenListener meH5AppOpenListener) {
        final CountDownLatch latch = new CountDownLatch(1);
        String appId = openInfo.getAppId(); //至少需要有AppId
        //TODO 目前没有考虑国际化、自定义安装包路径
        if (Utils2String.isEmptyWithTrim(appId)) {
            if (meH5AppOpenListener != null) {
                meH5AppOpenListener.onFail(ERR_APP_INFO);
            }
            return;
        }
        final File appInternalCacheDir = new File(getAppInternalCacheAppDir(appId)); //检查内部缓存是否已经安装
        File cacheInfoFile = new File(getCacheInfoFilePath(appId));
        final MeH5AppInfo appCacheInfo = getAppInfo(cacheInfoFile); //检查内部缓存是否有info文件
        File delFlagFile = new File(getCacheDelFlagFile(appId)); //有"更新失败"标志，需要删除残留缓存，重新解压安装
        final MeH5AppInfo localInfo = getAppInfo(new File(getInfoFilePath(appId)));  //检查内部缓存是否有info文件

        //----=== 1.判断是否需要清空内部缓存（没有info文件，有del标志，没有解压文件夹，版本不一致，都要删除）
        if (localInfo == null || appCacheInfo == null || delFlagFile.exists() || !appInternalCacheDir.exists()
                || (appCacheInfo.getAppBuildVersion() != localInfo.getAppBuildVersion())) {
            //删除全部缓存，准备重新安装
            boolean result = createFileByDeleteOldFile(delFlagFile);
            if (result) {
                result = Utils2File.deleteAllInDir(appInternalCacheDir);
                if (result) {
                    result = Utils2File.delete(cacheInfoFile);
                    if (result) {
                        Utils2File.delete(delFlagFile);
                    }
                }
            }
        } else {
            //缓存文件版本正常，可以打开APP
            openByPath(context, openInfo.getAppId(), appInternalCacheDir.getAbsolutePath(), meH5AppOpenListener);
            return;
        }
        //----=== 2.判断外部缓存是否有安装包，有的话解压到内部缓存，正常打开
        if (localInfo != null) {
            File bundleFile = new File(getAppFilePathByVersion(localInfo.getAppId(), localInfo.getAppBuildVersion()));
            if (bundleFile.exists()) {
                tryInstallAndOpen(context, localInfo, appInternalCacheDir, meH5AppOpenListener);
                return;
            }
        }
        //----=== 3.外部缓存没有，走联网重新下载
        updateAppInternal(context, openInfo, token, new MeH5AppUpdateListener() {
            @Override
            public void onSuccess(MeH5AppInfo meH5AppInfo, boolean needUpdate) {
                if (meH5AppInfo != null) {
                    tryInstallAndOpen(context, meH5AppInfo, appInternalCacheDir, meH5AppOpenListener);
                }
                latch.countDown();
            }

            @Override
            public void onFail(int errCode) {
                if (meH5AppOpenListener != null) {
                    meH5AppOpenListener.onFail(errCode);
                }
                latch.countDown();
            }

            @Override
            public void onProgress(long contentLength, long bytesRead) {
                if (meH5AppOpenListener != null) {
                    meH5AppOpenListener.onProgress(contentLength, bytesRead);
                }
            }
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void closeApp(Context context, MeH5AppInfo meH5AppInfo) {
    }

    public void updateApp(final Context context, final MeH5AppInfo updateInfo, final MeH5AppToken token, final MeH5AppUpdateListener meH5AppUpdateListener) {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                updateAppInternal(context, updateInfo, token, meH5AppUpdateListener);
            }
        });
    }

    private void updateAppInternal(final Context context, final MeH5AppInfo updateInfo, final MeH5AppToken token, final MeH5AppUpdateListener meH5AppUpdateListener) {
//        Log.d("TH1111", "-------------AAA-------------");
        final CountDownLatch latch = new CountDownLatch(1);
        String appId = updateInfo.getAppId();
        BundleManager bundleManager = new BundleManager(context, new BundleManInitInfo(BUNDLE_MANAGE_URL_PRE, BUNDLE_MANAGE_APP_ID_PRE,
                token.getDeviceId(), token.getNativeVersion(), token.getToken(), mCacheDir.getAbsolutePath()));
        MeH5AppInfo oldInfo = getAppInfo(appId);
        if (oldInfo == null) {
            oldInfo = new MeH5AppInfo(appId);
        } else { // 有info文件，但是没有对应的bundle安装包，将版本设置为0。
            File bundleFile = new File(getAppFilePathByVersion(appId, oldInfo.getAppBuildVersion()));
            if (!bundleFile.exists()) {
                oldInfo.setAppBuildVersion(0);
            }
        }
        bundleManager.addMsgListener(new BundleManager.OnBundleMsgListener() {
            @Override
            public void onReceiveMsg(final String msg) {
                handleMsg(msg);
                latch.countDown();
            }

            private void handleMsg(String msg) {
                MeH5AppInfo msgInfo = new Gson().fromJson(msg, MeH5AppInfo.class);
                if (msgInfo == null || mCacheDir == null) {
                    return;
                }
                if (msgInfo.getProcessFlag() == PROCESS_FLAG_NEW) {
                    //-----=== 第1步，移动下载的H5包到外部缓存中
                    //比方从"/storage/emulated/0/Android/data/package/cache/MaeBundleManager/5.4.0/h5/20190329/5.zip"
                    // 到"/storage/emulated/0/Android/data/package/cache/h5/20190329/5.bundle"
                    boolean result = moveFile(msgInfo.getPath(), getAppFilePathByVersion(msgInfo.getAppId(), msgInfo.getAppBuildVersion()));
                    if (!result) {
                        if (meH5AppUpdateListener != null) {
                            meH5AppUpdateListener.onFail(ERR_MOVE_FILE_FAIL);
                        }
                        return;
                    }
                    //------=== 第2步，将本次联网下载获得的app版本信息保存在app.info文件中
                    // 比方"/storage/emulated/0/Android/data/package/cache/h5/20190329/app.info"
                    File info = new File(getInfoFilePath(msgInfo.getAppId()));
                    result = writeFileFromString(info, msg);
                    if (!result) {
                        if (meH5AppUpdateListener != null) {
                            meH5AppUpdateListener.onFail(ERR_WRITE_INFO_FAIL);
                        }
                        return;
                    }
                    //-----=== 第3步，回调通知结果。
                    if (meH5AppUpdateListener != null) {
                        meH5AppUpdateListener.onSuccess(getAppInfo(msgInfo.getAppId()), true);
                    }
                } else if (msgInfo.getProcessFlag() == PROCESS_FLAG_DO_NOTHING) {
                    if (meH5AppUpdateListener != null) {
                        MeH5AppInfo appInfo = getAppInfo(msgInfo.getAppId());
                        if (appInfo == null) { //appId错误会到这里
                            meH5AppUpdateListener.onFail(ERR_WRITE_NO_INFO);
                        } else {
                            meH5AppUpdateListener.onSuccess(appInfo, false);
                        }
                    }
                }
            }

            @Override
            public void onError(int errCode, String errMsg, BundleTypeInfo... typeInfo) {
                if (meH5AppUpdateListener != null) {
                    meH5AppUpdateListener.onFail(ERR_DOWNLOAD_FAIL);
                }
                latch.countDown();
            }

            @Override
            public void onQuerySuccess(Map<String, BundleTypeInfo> downloadMap) {
                if (meH5AppUpdateListener != null) { // 暂时不用这个
                    meH5AppUpdateListener.onSuccess(null, false);
                }
                latch.countDown();
            }

            @Override
            public void onDownloadProgress(BundleTypeInfo typeInfo, final long contentLength, final long bytesRead, boolean done) {
                if (meH5AppUpdateListener != null) {
                    meH5AppUpdateListener.onProgress(contentLength, bytesRead);
                }
            }
        });
        bundleManager.startCheck(BundleManager.OperateFlag.INFO_POINT_DOWNLOAD, true,
                new BundleTypeInfo(BundleTypeInfo.BundleType.H5, oldInfo.getAppId(), oldInfo.getAppBuildVersion()));
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
//        Log.d("TH1111", "-------------BBB-------------");
    }

    public MeH5AppInfo getAppInfo(String appId) {
        if (Utils2String.isEmptyWithTrim(appId)) {
            return null;
        }
        return getAppInfo(new File(getInfoFilePath(appId)));
    }

    private MeH5AppInfo getAppInfo(File infoFile) {
        if (infoFile == null || !infoFile.exists()) {
            return null;
        }
        return new Gson().fromJson(readFile2String(infoFile, UTF_8), MeH5AppInfo.class);
    }

    private void tryInstallAndOpen(Context context, MeH5AppInfo openInfo, File appInternalCacheDir, final MeH5AppOpenListener meH5AppOpenListener) {
        try { //如果没有则从外部缓存解压安装
            File delFlagFile = new File(getCacheDelFlagFile(openInfo.getAppId()));
            boolean result = createFileByDeleteOldFile(delFlagFile);
            Utils2Zip.unzipFile(getAppFilePathByVersion(openInfo.getAppId(), openInfo.getAppBuildVersion()), appInternalCacheDir.getAbsolutePath());
            copyFile(getInfoFilePath(openInfo.getAppId()), getCacheInfoFilePath(openInfo.getAppId()));
            Utils2File.delete(delFlagFile);
        } catch (Exception e) {
            if (meH5AppOpenListener != null) {
                meH5AppOpenListener.onFail(ERR_INSTALL_FAIL);
            }
            return;
        }
        openByPath(context, openInfo.getAppId(), appInternalCacheDir.getAbsolutePath(), meH5AppOpenListener);
    }

    private void openByPath(Context context, String appId, String appPath, final MeH5AppOpenListener meH5AppOpenListener) {
        if (meH5AppOpenListener != null) {
            String path = appPath + File.separatorChar + INDEX_HTML;
            File indexFile = new File(path);
            if (indexFile.exists()) {
                meH5AppOpenListener.onSuccess(new MeH5AppInfo(appId, FILE + path));
            } else {
                meH5AppOpenListener.onFail(ERR_APP_FILE);
            }
        }
    }

    private void initCacheDir(Application context) {
        application = context;
        setCacheDir(application.getExternalCacheDir());// TODO 如果sd卡被拔出？
    }

    private void setCacheDir(File file) {
        mCacheDir = file;
    }

    private String getInfoFilePath(String appId) {
        return getCacheDirPath(appId) + appId + INFO;
    }

    private String getCacheInfoFilePath(String appId) {
        return getAppInternalCacheDir(appId) + appId + INFO;
    }

    private String getAppFilePathByVersion(String appId, int version) {
        return getCacheDirPath(appId) + version + BUNDLE;
    }

    private String getCacheDirPath(String appId) {
        return mCacheDir.getAbsolutePath() + File.separatorChar
                + H_5 + File.separatorChar
                + appId + File.separatorChar;
    }

    private String getCacheDelFlagFile(String appId) {
        return getAppInternalCacheDir(appId) + appId + DEL;
    }

    private String getAppInternalCacheAppDir(String appId) {
        if (Utils2String.isEmptyWithTrim(appId)) {
            return null;
        }
        return getAppInternalCacheDir(appId) + appId + File.separatorChar;
    }

    private String getAppInternalCacheDir(String appId) {
        if (Utils2String.isEmptyWithTrim(appId)) {
            return null;
        }
        return application.getCacheDir().getAbsolutePath() + File.separatorChar + H_5 + File.separatorChar;
    }


//    public void testAddDel(Context context, MeH5AppInfo meH5AppInfo) {
//        File delFlagFile = new File(getCacheDelFlagFile(meH5AppInfo.getAppId()));
//        boolean result = createFileByDeleteOldFile(delFlagFile);
//    }

}
