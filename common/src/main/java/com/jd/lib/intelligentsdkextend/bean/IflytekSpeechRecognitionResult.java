package com.jd.lib.intelligentsdkextend.bean;

import android.text.TextUtils;

/**
 * 科大讯飞语音识别结果
 */
public class IflytekSpeechRecognitionResult {
    /**
     * 日志TAG.
     */
    public static final String TAG = IflytekSpeechRecognitionResult.class.getSimpleName();
    /**
     * 识别结果文本缓冲区
     */
    private static StringBuffer resultTextStringBuffer;

    /**
     * 静态工具类屏蔽构造方法
     */
    private IflytekSpeechRecognitionResult() {
    }

    /**
     * 解析科大讯飞识别结果
     *
     * @param iflytekRecognitionResult 科大讯飞识别结果
     * @param space                    识别结果是否保留空格
     */
    public static void parseIflytekRecognitionResult(String iflytekRecognitionResult, boolean space) {
        if (null == resultTextStringBuffer) {
            resultTextStringBuffer = new StringBuffer();
        }
        resultTextStringBuffer.append(iflytekRecognitionResult.replace(" ", ""));
        //当开启空格分词配置和识别的字符经过过滤空格后不是空字符串时才追加空格
        if (space && !TextUtils.isEmpty(iflytekRecognitionResult)) {
            resultTextStringBuffer.append(" ");
        }
    }

    /**
     * 获取解析结果
     *
     * @return 解析结果
     */
    public static String getResult() {
        String resultText = resultTextStringBuffer.toString();
        resultTextStringBuffer.setLength(0);
        return resultText;
    }
}
