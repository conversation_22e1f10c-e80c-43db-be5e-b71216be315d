package com.jd.lib.intelligentsdkextend;


import android.app.Application;
import android.text.TextUtils;

import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechRecognizer;
import com.iflytek.cloud.SpeechUtility;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.wireless.sdk.intelligent.assistant.ThirdPartySpeechRecognitionCallInterface;
import com.jd.wireless.sdk.intelligent.assistant.ThirdPartySpeechRecognitionConfig;
import com.jd.wireless.sdk.intelligent.assistant.ThirdPartySpeechRecognizerListener;

/**
 * 第三方语音识别调用
 */
public class ThirdPartySpeechRecognitionCall implements ThirdPartySpeechRecognitionCallInterface {
    /**
     * 第三方语音识别调用类实例
     */
    private volatile static ThirdPartySpeechRecognitionCall instance;
    /**
     * 上下文实体
     */
    private Application context;
    /**
     * 讯飞语音回调
     */
    private IflytekRecognizerListener iflytekRecognizerListener;
    /**
     * 讯飞语音识别引擎
     */
    private com.iflytek.cloud.SpeechRecognizer iflytekSpeechRecognizer;
    /**
     * 第三方语音识别配置
     */
    private ThirdPartySpeechRecognitionConfig thirdPartySpeechRecognitionConfig;

    /**
     * 构造方法
     */
    private ThirdPartySpeechRecognitionCall() {

    }

    public static synchronized ThirdPartySpeechRecognitionCall getInstance() {
        if (instance == null) {
            instance = new ThirdPartySpeechRecognitionCall();
        }
        return instance;
    }

    /**
     * 销毁单例对象
     */
    public static void destroyInstance() {
        if (instance != null) {
            instance.destroyThirdPartySpeechRecognitionEngine();
        }
        instance = null;
    }

    /**
     * 设置上下文实体
     *
     * @param context 上下文实体
     */
    public void setContext(Application context) {
        this.context = context;
    }

    /**
     * 初始化科大讯飞语音识别组件
     */
    private synchronized void initIflytekSdk() {
        String appId = ThirdPartyConfigHelper.getInstance(context).getIflytekAppId();
        SpeechUtility.createUtility(context, SpeechConstant.APPID + "="+appId);
        iflytekSpeechRecognizer = SpeechRecognizer.createRecognizer(context, null);
        iflytekRecognizerListener = new IflytekRecognizerListener();

    }

    /**
     * 用默认配置初始化科大讯飞语音识别组件
     */
    private void initIflytekSdkForDefaultConfig() {
        initIflytekSdk();
        if (iflytekSpeechRecognizer != null) {
//      此处engineType为“cloud”
//      mIat.setParameter( SpeechConstant.ENGINE_TYPE, engineType );

            //设置语法ID和 SUBJECT 为空，以免因之前有语法调用而设置了此参数；或直接清空所有参数，具体可参考 DEMO 的示例。
            iflytekSpeechRecognizer.setParameter(SpeechConstant.CLOUD_GRAMMAR, null);
            iflytekSpeechRecognizer.setParameter(SpeechConstant.SUBJECT, null);
            // 设置语音前端点:静音超时时间，单位ms，即用户多长时间不说话则当做超时处理取值范围{1000～10000}
            iflytekSpeechRecognizer.setParameter(SpeechConstant.VAD_BOS, "10000");
            //设置语音后端点:后端点静音检测时间，单位ms，即用户停止说话多长时间内即认为不再输入，自动停止录音，范围{0~10000}
            iflytekSpeechRecognizer.setParameter(SpeechConstant.VAD_EOS, "10000");
            iflytekSpeechRecognizer.setParameter(SpeechConstant.ASR_PTT, "1");
            //设置返回结果格式，目前支持json,xml以及plain 三种格式，其中plain为纯听写文本内容
            iflytekSpeechRecognizer.setParameter(SpeechConstant.RESULT_TYPE, "plain");
            iflytekSpeechRecognizer.setParameter(SpeechConstant.DOMAIN, "iat");
            //设置语音输入语言，zh_cn为简体中文
            iflytekSpeechRecognizer.setParameter(SpeechConstant.LANGUAGE, "zh_cn");
            //设置结果返回语言
            iflytekSpeechRecognizer.setParameter(SpeechConstant.ACCENT, "mandrain");
            //设置标点符号,设置为"0"返回结果无标点,设置为"1"返回结果有标点
            iflytekSpeechRecognizer.setParameter(SpeechConstant.ASR_PTT, "1");
            iflytekSpeechRecognizer.setParameter(SpeechConstant.AUDIO_FORMAT, "wav");
        }
        if (iflytekRecognizerListener != null) {
            iflytekRecognizerListener.setReserveSpaceEnable(false);
        }
    }

    /**
     * 用线上配置初始化科大讯飞语音识别组件
     */
    private void initIflytekSdkForOnLineConfig(ThirdPartySpeechRecognitionConfig thirdPartySpeechRecognitionConfig) {
        initIflytekSdk();
        if (iflytekSpeechRecognizer != null && thirdPartySpeechRecognitionConfig != null && iflytekRecognizerListener
                != null) {
            if (thirdPartySpeechRecognitionConfig.isPunctuationEnable()) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.ASR_PTT, "1");
            } else {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.ASR_PTT, "0");
            }
            iflytekSpeechRecognizer.setParameter(SpeechConstant.RESULT_TYPE, "plain");
            iflytekRecognizerListener.setReserveSpaceEnable(thirdPartySpeechRecognitionConfig.isReserveSpaceEnable());
            iflytekRecognizerListener.setResultsReturnModel(thirdPartySpeechRecognitionConfig.getResultsReturnModel());
            //前置静音时长
            String vadBos = thirdPartySpeechRecognitionConfig.getPrepositionSilenceMaxDuration();
            //后置静音时长
            String vadEos = thirdPartySpeechRecognitionConfig.getPostpositionSilenceMaxDuration();
            //语音输入最长时间
            String SpeechMaxDuration = thirdPartySpeechRecognitionConfig.getSpeechMaxDuration();
            //时长为整数最小值时说明服务器未下发该配置，不配置走第三方Sdk默认配置
            if (!vadBos.equals(String.valueOf(Integer.MIN_VALUE))) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.VAD_BOS, vadBos);
            }
            if (!vadEos.equals(String.valueOf(Integer.MIN_VALUE))) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.VAD_EOS, vadEos);
            }
            if (!SpeechMaxDuration.equals(String.valueOf(Integer.MIN_VALUE))) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.KEY_SPEECH_TIMEOUT, SpeechMaxDuration);
            }
            //应用领域
            String domain = thirdPartySpeechRecognitionConfig.getDomain();
            //语言区域
            String language = thirdPartySpeechRecognitionConfig.getLanguage();
            //方言
            String accent = thirdPartySpeechRecognitionConfig.getAccent();
            //音频采样率
            String sampleRate = thirdPartySpeechRecognitionConfig.getSamplingRate();
            //以下配置项当值为null或空字符串时，不进行配置走第三方Sdk默认配置
            if (!TextUtils.isEmpty(domain)) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.DOMAIN, domain);
            }
            if (!TextUtils.isEmpty(language)) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.LANGUAGE, language);
            }
            if (!TextUtils.isEmpty(accent)) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.ACCENT, accent);
            }
            if (!TextUtils.isEmpty(sampleRate)) {
                iflytekSpeechRecognizer.setParameter(SpeechConstant.SAMPLE_RATE, sampleRate);
            }
            iflytekSpeechRecognizer.setParameter(SpeechConstant.AUDIO_FORMAT, "wav");
        }
    }

    @Override
    public void initThirdPartySpeechRecognitionEngine(ThirdPartySpeechRecognitionConfig pThirdPartySpeechRecognitionConfig) {
        this.thirdPartySpeechRecognitionConfig = pThirdPartySpeechRecognitionConfig;
        if (thirdPartySpeechRecognitionConfig == null) {
            initIflytekSdkForDefaultConfig();
        } else {
            if (thirdPartySpeechRecognitionConfig.getSpeechRecognitionServeProvider() == ThirdPartySpeechRecognitionConfig.SERVICE_PROVIDER_IFLYTEK) {
                initIflytekSdkForOnLineConfig(thirdPartySpeechRecognitionConfig);
            }
        }
    }

    @Override
    public boolean isListening() {
        return iflytekSpeechRecognizer != null && iflytekSpeechRecognizer.isListening();
    }

    /**
     * 销毁第三方语音引擎
     */
    @Override
    public void destroyThirdPartySpeechRecognitionEngine() {
        if (iflytekSpeechRecognizer != null) {
            //如果正在交互先调用取消方法
            if (iflytekSpeechRecognizer.isListening()) {
                iflytekSpeechRecognizer.cancel();
            }
            iflytekSpeechRecognizer.destroy();
        }
    }

    @Override
    public String getVoiceSampleFormat() {
        if (iflytekSpeechRecognizer != null) {
            return iflytekSpeechRecognizer.getParameter(SpeechConstant.AUDIO_FORMAT);
        } else {
            return null;
        }
    }

    /**
     * 开始说话
     */
    @Override
    public void startSpeak() {
        if (iflytekSpeechRecognizer != null) {
            iflytekSpeechRecognizer.startListening(iflytekRecognizerListener);
        }
    }

    /**
     * 停止说话
     */
    @Override
    public void stopSpeak() {
        if (iflytekSpeechRecognizer != null) {
            iflytekSpeechRecognizer.stopListening();
        }
    }

    /**
     * 取消说话（暂时只支持讯飞识别引擎）
     */
    @Override
    public void cancelSpeak() {
        if (iflytekSpeechRecognizer != null) {
            iflytekSpeechRecognizer.cancel();
        }
    }

    /**
     * 使用 setThirdPartySpeechRecognizerListener2
     */
    @Override
    @Deprecated
    public void setThirdPartySpeechRecognizerListener(ThirdPartySpeechRecognizerListener pThirdPartySpeechRecognizerListener) {
//        if (iflytekRecognizerListener != null) {
//            iflytekRecognizerListener.setThirdPartySpeechRecognizerListener(pThirdPartySpeechRecognizerListener);
//        }
    }

    public void setThirdPartySpeechRecognizerListener2(ThirdPartySpeechRecognizerListener2 pThirdPartySpeechRecognizerListener) {
        if (iflytekRecognizerListener != null) {
            iflytekRecognizerListener.setThirdPartySpeechRecognizerListener(pThirdPartySpeechRecognizerListener);
        }
    }

    @Override
    public void setVoiceSavePath(String s) {
        if (iflytekSpeechRecognizer != null) {
            iflytekSpeechRecognizer.setParameter(SpeechConstant.ASR_AUDIO_PATH, s);
        }
    }
}
