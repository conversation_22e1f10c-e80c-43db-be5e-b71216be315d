package com.jd.lib.intelligentsdkextend;

import android.os.Bundle;
import android.util.Log;

import com.iflytek.cloud.RecognizerListener;
import com.iflytek.cloud.RecognizerResult;
import com.iflytek.cloud.SpeechError;
import com.jd.lib.intelligentsdkextend.bean.IflytekSpeechRecognitionResult;
import com.jd.wireless.sdk.intelligent.assistant.ThirdPartySpeechRecognitionConfig;
import com.jme.common.BuildConfig;

/**
 * 科大讯飞语音识别回调
 */
class IflytekRecognizerListener implements RecognizerListener {
    private static final String TAG = "RecognizerListener";
    /**
     * 结果返回模式
     */
    private byte resultsReturnModel;
    /**
     * 第三方语音识别回调
     */
    private ThirdPartySpeechRecognizerListener2 thirdPartySpeechRecognizerListener;
    /**
     * 语音识别词组间保留空格开关状态
     */
    private boolean reserveSpaceEnable;

    /**
     * 设置结果返回模式
     *
     * @param pResultsReturnModel 结果返回模式
     */
    public void setResultsReturnModel(byte pResultsReturnModel) {
        resultsReturnModel = pResultsReturnModel;
    }

    /**
     * 设置第三方语音识别回调
     *
     * @param thirdPartySpeechRecognizerListener 第三方语音识别回调
     */
    public void setThirdPartySpeechRecognizerListener(ThirdPartySpeechRecognizerListener2 thirdPartySpeechRecognizerListener) {
        this.thirdPartySpeechRecognizerListener = thirdPartySpeechRecognizerListener;
    }

    /**
     * 设置语音识别词组间保留空格开关状态
     *
     * @param reserveSpaceEnable 语音识别词组间保留空格开关状态,true:保留，false：不保留
     */
    public void setReserveSpaceEnable(boolean reserveSpaceEnable) {
        this.reserveSpaceEnable = reserveSpaceEnable;
    }

    @Override
    public void onVolumeChanged(int i, byte[] bytes) {
        if (thirdPartySpeechRecognizerListener != null) {
            thirdPartySpeechRecognizerListener.onRmsChanged(i);
        }
    }

    @Override
    public void onBeginOfSpeech() {
        if (thirdPartySpeechRecognizerListener != null) {
            thirdPartySpeechRecognizerListener.onStart();
        }
    }

    @Override
    public void onEndOfSpeech() {
        if (thirdPartySpeechRecognizerListener != null) {
            thirdPartySpeechRecognizerListener.onEndOfSpeech();
        }
    }

    @Override
    public void onResult(RecognizerResult recognizerResult, boolean isLast) {
        //解析识别结果
        IflytekSpeechRecognitionResult.parseIflytekRecognitionResult(recognizerResult.getResultString(), reserveSpaceEnable);
        //如果结果返回类型为即时返回则立即发送结果。否则在识别至最后一句话时才发送
        if (resultsReturnModel == ThirdPartySpeechRecognitionConfig.RESULTS_RETURN_MODEL_INSTANT) {
            sendResult(IflytekSpeechRecognitionResult.getResult(), isLast);
        } else {
            //是否为最后一句
            if (isLast) {
                sendResult(IflytekSpeechRecognitionResult.getResult(), isLast);
            }
        }
    }

    /**
     * 发送结果
     *
     * @param result 结果文本
     */
    private void sendResult(String result, boolean isLast) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "sendResult: " + result);
        }
        if (thirdPartySpeechRecognizerListener != null) {
            thirdPartySpeechRecognizerListener.onResult(result, isLast);
        }
    }

    @Override
    public void onError(SpeechError speechError) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "onError: " + speechError.getErrorCode());
        }
        if (thirdPartySpeechRecognizerListener != null) {
            thirdPartySpeechRecognizerListener.onError(speechError.getErrorCode());
        }
    }

    @Override
    public void onEvent(int i, int i1, int i2, Bundle bundle) {

    }
}
