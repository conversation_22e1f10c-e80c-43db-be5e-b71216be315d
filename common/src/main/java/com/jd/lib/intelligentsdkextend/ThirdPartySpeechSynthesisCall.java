package com.jd.lib.intelligentsdkextend;

import android.app.Application;
import android.os.Bundle;
import android.util.Log;

import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechError;
import com.iflytek.cloud.SpeechSynthesizer;
import com.iflytek.cloud.SpeechUtility;
import com.iflytek.cloud.SynthesizerListener;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.wireless.sdk.intelligent.assistant.ThirdPartySpeechSynthesizerCallInterface;
import com.jd.wireless.sdk.intelligent.assistant.ThirdPartySpeechSynthesizerListener;
import com.jme.common.BuildConfig;

import java.util.HashSet;

/**
 * 第三方语音合成调用
 */
public class ThirdPartySpeechSynthesisCall implements ThirdPartySpeechSynthesizerCallInterface {
    /**
     * 语音合成人默认值
     */
    private static final String DEFAULT_VOICE_NAME = "xiaoyan";
    /**
     * 单例实例
     */
    private volatile static ThirdPartySpeechSynthesisCall instance;
    /**
     * 语音合成发音人支持列表
     */
    private static HashSet<String> names;

    /**
     *初始化发音人列表
     */
    static {
        names = new HashSet<>();
        names.add("xiaoyan");
        names.add("xiaofeng");
        names.add("xiaoqi");
        names.add("vinn");
        names.add("vils");
        names.add("aisjiuxu");
        names.add("aisxping");
        names.add("aisjying");
        names.add("aisbabyxu");
        names.add("aisjinger");
        names.add("yefang");
        names.add("aisduck");
        names.add("aisxmeng");
        names.add("aismengchun");
        names.add("ziqi");
        names.add("aisduoxu");
        names.add("xiaoxin");
        names.add("xiaowanzi");
    }

    /**
     * 上下文实体
     */
    private Application context;
    /**
     * 科大讯飞语音合成引擎实例
     */
    private SpeechSynthesizer speechSynthesizer;
    /**
     * 第三方语音合成调用侦听
     */
    private ThirdPartySpeechSynthesizerListener thirdPartySpeechSynthesizerListener;
    /**
     * 语音合成侦听
     */
    private SynthesizerListener synthesizerListener;

    /**
     * 构造方法
     */
    private ThirdPartySpeechSynthesisCall() {
        synthesizerListener = new SynthesizerListener() {
            @Override
            public void onSpeakBegin() {

            }

            @Override
            public void onBufferProgress(int i, int i1, int i2, String s) {

            }

            @Override
            public void onSpeakPaused() {

            }

            @Override
            public void onSpeakResumed() {

            }

            @Override
            public void onSpeakProgress(int i, int i1, int i2) {

            }

            @Override
            public void onCompleted(SpeechError speechError) {
                if (null == speechError) {
                    thirdPartySpeechSynthesizerListener.onCompleted(ThirdPartySpeechSynthesizerListener.SPEECH_SYNTHESIS_ERROR_CODE_NO_ERROR);
                } else {
                    if (BuildConfig.DEBUG) {
                        Log.d("SynthesisCompleted", "speechErrorCode:" + speechError.getErrorCode());
                    }
                    thirdPartySpeechSynthesizerListener.onCompleted(ThirdPartySpeechSynthesizerListener.SPEECH_SYNTHESIS_ERROR_CODE_UNKNOWN_ERROR);
                }
            }

            @Override
            public void onEvent(int i, int i1, int i2, Bundle bundle) {

            }
        };
    }

    /**
     * 获取单例实例
     */
    public static synchronized ThirdPartySpeechSynthesisCall getInstance() {
        if (instance == null) {
            instance = new ThirdPartySpeechSynthesisCall();
        }
        return instance;
    }

    public static void destroyInstance() {
        if (instance != null) {
            instance.destroyThirdPartyVoiceEngine();
        }
        instance = null;
    }

    /**
     * 设置上下文实体
     *
     * @param pContext 上下文实体
     */
    public void setContext(Application pContext) {
        this.context = pContext;
    }

    /**
     * 销毁第三方语音引擎
     */
    public void destroyThirdPartyVoiceEngine() {
        if (speechSynthesizer != null) {
            speechSynthesizer.destroy();
        }
    }

    @Override
    public void initThirdPartySpeechSynthesisEngine() {
        String appId = ThirdPartyConfigHelper.getInstance(context).getIflytekAppId();
        SpeechUtility.createUtility(context, SpeechConstant.APPID + "="+appId);
        speechSynthesizer = SpeechSynthesizer.createSynthesizer(context, null);
        //初始化异常时的不在进行参数配置
        if (speechSynthesizer != null) {
            //后续改为配置下发
            speechSynthesizer.setParameter(SpeechConstant.VOICE_NAME, DEFAULT_VOICE_NAME);
            speechSynthesizer.setParameter(SpeechConstant.SPEED, "50");
            speechSynthesizer.setParameter(SpeechConstant.VOLUME, "80");
            speechSynthesizer.setParameter(SpeechConstant.ENGINE_TYPE, SpeechConstant.TYPE_CLOUD);
        }
    }

    @Override
    public void cancelSpeechSynthesizer() {
        if (speechSynthesizer != null) {
            speechSynthesizer.stopSpeaking();
        }
    }

    @Override
    public void setVoiceName(String s) {
        if (names.contains(s)) {
            if (speechSynthesizer != null) {
                speechSynthesizer.setParameter(SpeechConstant.VOICE_NAME, s);
            }
        }

    }

    @Override
    public void startSpeechSynthesizer(String s) {
        s = s.replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", "");
        if (speechSynthesizer != null) {
            speechSynthesizer.startSpeaking(s, synthesizerListener);
        }
    }

    @Override
    public void setThirdPartySpeechSynthesizerListener(ThirdPartySpeechSynthesizerListener pThirdPartySpeechSynthesizerListener) {
        thirdPartySpeechSynthesizerListener = pThirdPartySpeechSynthesizerListener;
    }
}
