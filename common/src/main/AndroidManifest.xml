<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />

    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <application xmlns:tools="http://schemas.android.com/tools">
        <activity
            android:name="com.jd.oa.PhotoPreviewActivity"
            android:exported="true"
            android:launchMode="singleTask" />
        <activity
            android:name="com.jd.oa.theme.view.SelectThemeActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.home.EmptyActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" /> <!-- 功能 -->
        <activity
            android:name="com.jd.oa.business.index.FunctionActivity"
            android:maxRecents="4"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.Slide"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize" /> <!-- 功能 -->
        <activity
            android:name="com.jd.oa.TextDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" /> <!-- 打卡提醒内容提供者 -->
        <provider
            android:name="com.jd.oa.provider.PunchNotifyProvider"
            android:authorities="${applicationId}.PunchNotifyProvider"
            android:exported="false" />

        <service
            android:name="com.jd.oa.download.DownloadService"
            android:exported="false"></service>

        <activity
            android:name="com.jd.oa.mask.MaskActivity"
            android:exported="false"
            android:theme="@style/MaskTheme" />

        <activity
            android:name="com.jd.oa.fragment.CheckPasswordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <service
            android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService"
            android:label="dexopt"
            android:process=":dexopt"></service>

        <activity
            android:name="com.jd.oa.tablet.TabletPlaceHolderActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:theme="@style/TabletPlaceHolderTheme" />
        <activity
            android:name="com.jd.oa.tablet.PermissionPlaceHolderActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:theme="@style/TabletPlaceHolderTheme" />
        <activity
            android:name="com.jd.oa.tablet.FoldPlaceHolderActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:theme="@style/TabletPlaceHolderTheme" />
        <activity android:name="com.jd.oa.JDMAHelperActivity" />
        <activity
            android:name="com.jd.oa.cool.FloatingActivity"
            android:configChanges="keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:theme="@style/FloatingActivityStyle"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <service
            android:name="com.jd.oa.multitask.MultiTaskService"
            android:exported="false">
        </service>
        <activity android:name="com.jd.oa.notification.HalfScreenProxyActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.TransparentActivity"/>

        <activity android:name="com.jd.oa.test.samelayer.WebTestActivity"/>

        <activity android:name="com.jd.oa.fragment.BottomSheetWebContainer"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan"
            android:launchMode="singleTop"
            android:theme="@style/AppTheme.TransparentActivity"/>

        <activity android:name="com.jd.oa.fragment.FileViewer"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan"/>

        <activity android:name="com.jd.oa.fragment.ImFileChooser"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan"/>


        <receiver android:name="com.jd.oa.timezone.TimeZoneChangedReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.TIMEZONE_CHANGED"/>
            </intent-filter>
        </receiver>
        <activity
            android:name="com.jd.oa.TransferEmptyActivity"
            android:enabled="true"
            android:exported="false"
            android:theme="@style/AppTheme.TransparentActivity">
            <intent-filter>
                <action android:name="${applicationId}.open.transfer" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <provider
            android:name="com.jd.oa.provider.MiniMoreMenuProvider"
            android:authorities="${applicationId}.MiniMoreMenuProvider"
            android:exported="false" />

    </application>

</manifest>