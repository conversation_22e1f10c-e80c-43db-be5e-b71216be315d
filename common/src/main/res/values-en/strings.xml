<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <string name="me_app_name">JoyME</string>
    <string name="me_app_name_test">JoyME(TEST)</string>
    <string name="me_setting">Settings</string>
    <string name="me_title_item">Title</string>
    <string name="me_info_title">Hint</string>
    <string name="me_content_sign">Contents</string>
    <string name="me_message">Messages</string>

    <string name="me_ok">OK</string>
    <string name="me_cancel">Cancel</string>
    <string name="me_confirm">Confirm</string>

    <string name="me_input_error">Input Error</string>
    <string name="me_setting_next_step">Next Step</string>
    <string name="me_delete">Delete</string>
    <string name="me_submit">Submit</string>

    <string name="me_yes">Yes</string>
    <string name="me_no">No</string>

    <string name="me_has_no_icon">ME has no profile photo</string>
    <string name="me_image_load_fail">Image Load Fails</string>
    <string name="me_agree_liveness_protocol">Enabling is Deemed as Agreeing on Face Use Protocol </string>

    <string name="me_punch_history">Clock History</string>
    <string name="me_join_meeting">Join in</string>

    <string name="me_api_response_unusual">Interface call unusual, please try again</string>

    <string name="me_no_data">No Data</string>
    <string name="me_dialog_with">Dialog Box</string>

    <string name="me_choose_unbind_reason">Please select unbind reason and submit it:</string>
    <string name="me_bind_wrong_account">Bind a Wrong Account</string>
    <string name="me_account_lost">Account Lost</string>
    <string name="me_other">Other</string>
    <string name="me_submit_and_wait">Submit for Check</string>
    <string name="me_eval_request_author">The permission of floating window is needed</string>


    <string name="me_load_failed">Loading failed</string>
    <string name="me_data_comm_fail">Data Communication Fails</string>
    <string name="me_face_upload_success">Upload Success</string>


    <!-- 界面切换时，如果参数不对，弹出此提示信息 -->
    <string name="me_params_error">Parameter Errors</string>
    <string name="me_empty_repeat">No Data, Click to Try Again</string>
    <string name="me_error_repeat">Load Unusual, Click to Try Again</string>
    <string name="me_error_net_repeat">Network Unusual, Click to Try Again</string>

    <string name="me_data_uploading">Loading...</string>
    <string name="me_data_compressing">Compressing...</string>

    <string name="me_not_install_jd_pay">Not installed JD wallet or it is not the latest version, click OK to download </string>

    <string name="me_token_refresh_token_expired">Token expired, please login again</string>

    <string name="me_sure_exit">Are you sure to log out?</string>

    <string name="me_load_fail_retry"> Load Fails, Click to Try Again</string>
    <string name="me_choose_reason_one">Choose an Unbind Reason</string>

    <string name="me_are_you_sure">Are You Sure?</string>

    <string name="me_camera_permission">Camera Permission Required</string>
    <string name="me_img_uploading_failed">Image Loading Fails</string>

    <!--分享
    <string name="me_share">分享</string>
    <string name="me_share_wechat_friend">分享到微信</string>
    <string name="me_share_wechat_moments">分享到朋友圈</string>
    <string name="me_share_wechat_content">我发现了一个不错的链接，分享给你看一下。</string>
    <string name="me_share_message">信息</string>
    <string name="me_share_copy_link">复制链接</string>
    <string name="me_share_open_with_browser">在浏览器中打开</string>
    <string name="me_share_qq_friend">QQ好友</string>
    <string name="me_share_qq_zone">QQ空间</string>
    <string name="me_share_sina_weibo">新浪微博</string>
    <string name="me_share_qq_not_installed">QQ未安装</string>
    <string name="me_share_timline">发送给朋友</string>


    <string name="me_web_decode_image_qrcode">识别图中二维码</string>
    <string name="me_web_view_image">查看图片</string>
    <string name="me_qrcode_result">扫描结果</string>
    <string name="me_qrcode_decode_fail">解析结果失败</string>
    <string name="me_qrcode_server_error">访问服务器异常，请稍后再试</string>
    <string name="me_save_to_netdisk_fail">保存失败，请稍后再试</string>
-->

    <string name="me_share_timline">JoyME</string>

    <string name="me_locatin_fail">Positioning Fails</string>
    <string name="me_need_camera_permission">Camera Permission Required for Scanning</string>
    <string name="me_uploading">Loading</string>

    <string name="me_allow">Allow</string>
    <string name="me_not_allow">Not Allow</string>
    <string name="me_allow_visist_pos">Allow "JoyME" to visit your position when using the APP?</string>
    <string name="me_open_loc_for_use">Enable the "positioning service" to allow "JoyME" to confirm your position</string>

    <string name="me_only_open_location">Only by enabling locating can the current position be detected</string>

    <string name="me_share_title">Share</string>
    <string name="me_load_more_data">Load More</string>
    <string name="me_no_more_data">No More Data </string>
    <string name="me_no_more">No More</string>

    <string name="me_feedback_tab_processing">Processing</string>
    <string name="me_parse_image_not_support">Invalid QR Code/Bar Code</string>

    <string name="me_purse_account">Me Wallet Account</string>
    <string name="me_tip_iknow">Got it</string>
    <string name="me_upgrade_now">Upgrade Now</string>
    <string name="me_upgrade">Upgrade</string>
    <string name="me_update_for_open_fail_info">Update Me to Experience! Go and Update</string>
    <string name="me_go_update">Update</string>
    <string name="me_app_version_latest">Latest Version Already</string>
    <string name="me_app_version_latest2">Latest Version</string>
    <string name="me_download_title">Downloading</string>
    <string name="me_waiting">Waiting...</string>
    <string name="me_dow_fail_retry">Download Fails, Try Again OR Download by Scanning</string>


    <string name="me_rn_compress_image">Processing...</string>
    <string name="me_hybrid_open_error">Error:</string>

    <!--下载-->
    <string name="me_update_preparing_download">Ready to download</string>
    <string name="me_update_downloading">Downloading</string>
    <string name="me_update_download_paused">Pause</string>
    <string name="me_update_completed">Download complete</string>
    <string name="me_update_exception">Download failed, click Retry</string>
    <string name="me_update_auto_download_in_wifi">Auto update on WiFi</string>
    <string name="me_update_downloading_apk">Downloading the new version</string>
    <string name="me_update_install_now">Install now</string>
    <string name="me_update_install_now2">Install</string>
    <string name="me_update_checking">Verifying the file</string>
    <string name="me_update_check_digest_failed">File failed to validate</string>
    <string name="me_update_check_digest_failed_retry">File failed to validate, click Retry</string>

    <string name="jdme_str_net_error">Network Loading Fails~</string>
    <string name="me_punch_notification">Clock Notification</string>
    <string name="me_tasks_notification">Tasks</string>
    <string name="me_daily_tasks_notification">Daily notification</string>

    <string name="me_todo_list_finish_btn">Complete</string>
    <string name="me_workbench_todo_meeting_sigin">Confirm</string>
    <string name="me_workbench_todo_meeting_subscribe">Confirm</string>
    <string name="me_workbench_todo_task">Approval</string>

    <string name="me_workbench_todo_notify_title">To do today</string>

    <string name="me_save">Save</string>
    <string name="me_cancel_ok">Canceled Successful</string>
    <string name="me_storage_permission">Read Storage Permission Required</string>
    <string name="me_camera_name">Camera</string>
    <string name="me_write_storage_name">Write Storage</string>

    <string name="me_agreement">Privacy Policy</string>

    <string name="me_week_seven">Sun</string>
    <string name="me_week_one">Mon</string>
    <string name="me_week_two">Tue</string>
    <string name="me_week_three">Wed</string>
    <string name="me_week_four">Thu</string>
    <string name="me_week_five">Fri</string>
    <string name="me_week_six">Sat</string>

    <string name="me_punch">Clock</string>
    <string name="me_finish">Finish</string>
    <string name="me_edit">Edit</string>

    <string name="me_punch_success">Clock Successfully!</string>
    <string name="me_punched_on_work">Clocked in: <xliff:g id="text">%s</xliff:g></string>
    <string name="me_punch_on_work">Not Clocked in work: &#8212;&#8212;:&#8212;&#8212;</string>
    <string name="me_punched_off_work">Clocked out work: <xliff:g id="text">%s</xliff:g></string>
    <string name="me_punch_off_work">Not Clocked out work: &#8212;&#8212;:&#8212;&#8212;</string>


    <!--mail chenqizheng start-->
    <string name="me_email_account">Email</string>
    <string name="me_bind_jd_email_summary">After binding your JD email, it can synchronize the meeting schedule</string>
    <string name="me_bind_jd_email_summary2">If you need to unlock, click on the account to unlock it</string>
    <string name="me_bind_email_account">Bind a JD email</string>
    <string name="me_hint_email_account">Input E-mail Address</string>
    <string name="me_hint_email_pwd">Input Password</string>
    <string name="me_bt_bind_email">Bind</string>
    <string name="me_at_jd_com">\@jd.com</string>
    <string name="me_bind_email_dialog">After binding your JD email, it can synchronize the meeting schedule</string>
    <string name="me_bind_email_out_time">Your email password is expired and cannot synchronize the schedule, please bind your email again.</string>
    <string name="me_bind_email_unbind_dialog">ME will not synchronize your schedules after unbinding your email. Are you sure to unbind？</string>
    <string name="me_bind_email_go">To bind</string>
    <string name="me_bind_email_account_check_notice">Password error, please input again!</string>
    <string name="me_bind_email_success_toast">Bind success</string>
    <string name="me_bind_email_undo_success_toast">Succeed</string>
    <string name="me_auto_bind_email_account">Email account automatic binding</string>
    <string name="me_auto_bind_email_account_summary">After closing, it will not automatically bind when re-login.</string>
    <string name="me_auto_bind_email_account_alert">After closing, the mailbox account will not be automatically bound when you log in again.</string>


    <string name="me_quick_daka">Easy Clock </string>
    <string name="me_quick_daka_settings">Easy Clock</string>

    <string name="me_todo_not_approve_prompt">You need to fill in the required items to complete the process, please log in ERP-flow center on a PC to complete examination and approval</string>
    <string name="me_todo_must_approve_in_detail">The process of this type does not support batch approval, enter Details for examination and approval separately</string>

    <string name="me_not_null">Input reason</string>
    <string name="me_net_error">Failed to load</string>

    <string name="me_email_pwd_error_tip">The email password does not match, please re-verify.</string>
    <string name="me_email_pwd_error_input_hit">Please enter your email password</string>
    <string name="me_email_pwd_error">The password is incorrect, please re-enter</string>

    <string name="me_permission_denied_camera">Camera permission denied</string>
    <string name="me_permission_request_camera_info">Get camera permission for photo taking and album visiting</string>

    <string name="me_print_file_expired">The file has expired or has been cleaned up</string>
    <string name="me_print_access_file_failed">File access failed</string>

    <string name="me_check_all">Check All</string>

    <string name="libui_loading">Loading</string>
    <string name="libui_request_error">Failed to request data</string>
    <string name="libui_reload_retry">Reload or retry</string>
    <string name="libui_net_error">Network request error</string>
    <string name="libui_checknet_reload">Please check your network to reload it.</string>
    <string name="libui_content_empty">No content</string>
    <string name="confirm">Confirm</string>
    <string name="cancel">Cancel</string>
    <string name="libui_header_normal">Try to refresh</string>
    <string name="libui_header_pulling">Pull down to refresh</string>
    <string name="libui_header_pull_over">Release refresh</string>
    <string name="libui_header_refreshing">Refreshing</string>
    <string name="libui_header_header_success">Refresh successfully</string>
    <string name="libui_header_header_fail">failed to refresh</string>
    <string name="libui_header_header_net_error">Network Error</string>
    <string name="libui_footer_normal">load more</string>
    <string name="libui_footer_loading">loading</string>
    <string name="libui_footer_load_success">Loaded successfully</string>
    <string name="libui_footer_load_fail">Failed to load</string>
    <string name="libui_footer_net_error">Network Error</string>
    <string name="libui_footer_no_more">No more</string>


    <string name="me_cmn_speech_recognition_explain">The recording function is necessary. Please allow the recording and storage permissions in settings.</string>
    <string name="me_cmn_speech_recognition_ing">Enabling/Recognizing</string>
    <string name="me_cmn_speech_recognition_error">ME didn\'t catch your voice, please try again.</string>
    <string name="me_cmn_h5_select_contact_failure">Failure to select members</string>
    <string name="me_cmn_h5_select_contact_default_title">Select Members</string>

    <string name="me_i_agree_protocol">I agree to the above agreement</string>

    <string name="me_dynamic_loading_failed">age loading failed. Click to reload</string>
    <string name="me_dymamic_retry_button">Retry</string>

    <string name="me_web_loading">Loading···</string>
    <string name="me_web_loading_failed">Page loading failed\nClick to reload</string>
    <string name="me_web_check_network_and_proxy">Check the network environment and proxy server</string>
    <string name="me_web_check_network">Please check the network connection\nClick to reload the page</string>
    <string name="me_text_detail_copy">Copy Success</string>

    <string name="me_web_tag_new">new</string>
    <string name="me_web_tag_beta">beta</string>
    <string name="me_web_tag_private">专属</string>
    <string name="me_web_tag_recommend">推荐</string>
    <string name="me_web_tag_hot">热门</string>

    <string name="me_scan_fun">Scan</string>
    <string name="me_joyspace_new">New Doc</string>
    <string name="me_schedule_new">Appointment</string>
    <string name="me_real_translate">Real-time translation</string>

    <string name="me_add_sigin">Insert task</string>
    <string name="me_add_sigin_hold_on">Pending</string>
    <string name="me_app_more">More</string>
    <string name="me_app_need_update">Please use after upgrading the new version～</string>

    <string name="me_download_failed">File Download Fails</string>

    <string name="me_check_account">Account:</string>
    <string name="me_check_email_account_phone_number">Email/User Name/Phone Number Verified</string>
    <string name="me_check_password">Password:</string>
    <string name="me_check_input_password">Input ERP Password</string>
    <string name="me_bind">Bind</string>
    <string name="me_check_pass_error">Password Error</string>
    <string name="me_check_pass_title">Bind JD Email Address</string>
    <string name="me_check_mail_tips">Timline Service Group ID：********</string>


    <string name="me_web_file_save">Save to</string>
    <string name="me_web_file_forward">Send to</string>
    <string name="me_web_item_local">JoyME</string>
    <string name="me_web_item_local_new">Storage</string>
    <string name="me_web_item_camera">Camera</string>
    <string name="me_web_item_gallery">Album</string>
    <string name="me_web_item_timline">Messages</string>
    <string name="me_web_item_other">other</string>
    <string name="me_web_item_mail">JoyMail</string>
    <string name="me_web_item_pan">JoyBox</string>
    <string name="me_web_item_joyspace">JoySpace</string>
    <string name="me_web_item_im_file">Download</string>

    <string name="me_web_decode_image_qrcode">Extract QR Code</string>
    <string name="me_web_view_image">view</string>
    <string name="me_web_view_image_save">Save</string>
    <string name="me_web_view_image_send">Send to Chat</string>
    <string name="me_qrcode_result">Scaning Result</string>
    <string name="me_qrcode_decode_fail">Fail to Extract</string>
    <string name="me_qrcode_server_error">Error,Please Try Again Later</string>
    <string name="me_save_to_netdisk_fail">Failed,Please Try Again Later</string>

    <string name="me_save_success">Success</string>
    <string name="me_save_failed">Fails</string>
    <string name="me_send_failed">Fails</string>

    <string name="me_file_use_other_app_open">Open in Other App</string>
    <string name="me_file_reload">Re-download</string>
    <string name="me_file_tip_0">This file type cannot ben opened</string>
    <string name="me_file_tip_1">Use another app to open and preview file</string>

    <string name="me_scaning">Scanning...</string>
    <string name="me_pick_file_failed">pick file failed</string>

    <string name="me_decrypt_failed">decrypt failed</string>

    <string name="date_format_type">MM/dd/yyyy</string>
    <string name="me_app_search_empty">No Result</string>
    <string name="quick_daka_failed_tip">Failed to check in without trace, please check whether the network or GPS is turned on</string>
    <string name="search_history">search history</string>
    <string name="go_to_set_up">set up</string>

    <string name="fb_share_title">Feedback</string>
    <string name="fb_share_evalute">Rate</string>
    <string name="fb_share_about">About</string>

    <string name="me_joywork_level">high</string>
    <string name="me_joywork_new">New Task</string>
    <string name="me_joywork_from">from: %s</string>
    <!--    以下四个合在一起才显示正确-->
    <string name="me_joywork_deadline_today">Due %s</string>
    <string name="me_joywork_deadline_yesterday">Due %s</string>
    <string name="me_joywork_deadline_tomorrow">Due %s</string>
    <string name="me_joywork_deadline_format">MM/dd HH:mm</string>

    <string name="me_joywork_deadline_normal">%s Due</string>
    <string name="me_joywork_deadline_normal2">%s Due</string>

    <string name="joywork_plan_alert_screen">Filter</string>
    <string name="joywork_plan_alert_title">Mark for</string>
    <string name="joywork_plan_alert_reset">Reset</string>

    <string name="me_js_cut">cut</string>
    <string name="me_js_copy">copy</string>
    <string name="me_js_paste">paste</string>
    <string name="me_js_select">select</string>
    <string name="me_js_selectAll">select all</string>
    <string name="me_js_delete">delete</string>
    <string name="me_js_search">search</string>
    <string name="me_js_comment">Comment</string>
    <string name="me_js_insertLink">link</string>

    <string name="me_not_support">not support</string>

    <string name="joywork_plan_alert_done">Done</string>
    <string name="joywork_plan_alert_plan">Plan</string>
    <string name="joywork_plan_alert_today">Today</string>
    <string name="joywork_plan_alert_one_day">Someday</string>

    <string name="joywork_screen_all">All</string>
    <string name="joywork_screen_unfinish">Incomplete</string>
    <string name="joywork_screen_finish">Completed</string>
    <string name="joywork_screen_deleted">Deleted</string>



    <string name="me_apply_urge">Urgent</string>

    <string name="xcx_bind_pin">Without binding a JD account, automatic login failed</string>
    <string name="me_joywork_unfinish">Incomplete</string>
    <string name="daka_tip_error">Check in failed</string>
    <string name="daka_tip_not_inner">Please confirm whether it is connected to the intranet</string>
    <string name="daka_tip_net_error">Check in failed, please try again later</string>
    <string name="daka_tip_gps_not_open">Please confirm whether to enable location permission</string>
    <string name="daka_tip_location_not_open">Please confirm whether to enable location service</string>
    <string name="daka_tip_gps_location_error">Failed to get location, please try again later</string>
    <string name="jdme_pay_code">Pay Code</string>
    <string name="jdme_pass_code">Pass Code</string>
    <string name="jdme_string_approval">Approval</string>
    <string name="video_call_is_on">Video call is on. Try again later.</string>
    <string name="voice_call_is_on">Voice call is on. Try again later.</string>
    <string name="joy_meeting_is_on">JoyMeeting is on. Try again later.</string>
    <string name="small_tv_is_on">ME TV is on. Try again later.</string>
    <string name="joynote_audio_is_playing_tips">You have a recording in progress. Please  retry later.</string>
    <string name="joynote_audio_is_out_tips">The recording was interrupted, please try again later.</string>
    <string name="joynote_audio_asr_is_playing_tips">The asr service recording was interrupted, please try again later.</string>

    <string name="me_request_permission_title_normal">JoyME needs to apply permission</string>
    <string name="me_request_permission_location_punch">JoyME needs to apply for your geographic location permission so that you can use the geographic location-based check-in function.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_location_normal">JoyME needs to apply for your geographic location permission so that you can use geographic location-based functions.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_location_travel">JoyME needs to apply for your geographic location permission so that you can use the feature of using a car based on geographic location.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_storage_normal">JoyME needs to apply for read storage permission so that you can get the functions of file viewing and downloading.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_normal">JoyME needs to apply for your camera permission so that you can use the function of taking pictures or uploading pictures. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_liveness">JoyME needs to apply for your camera shooting permission so that you can realize face collection and authentication services through face recognition. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_scan">JoyME needs to apply for your camera permission so that you can scan the QR code for asset inventory, login authentication, and sign-in authentication services.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_video">JoyME needs to apply for your camera permission so that you can use the function of taking photos or videos to upload pictures or videos. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_read_storage_gallery">JoyME needs to apply for your read and storage permission so that you can use the function of taking photos or videos to upload pictures or videos.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_audio_normal">JoyME needs to apply for microphone permission so that you can use the voice or recording function normally.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_phone_normal">JoyME needs to apply for phone permission so that you can use the call phone normally.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="input_baby_name">input baby name</string>
    <string name="please_input_baby_name">Please input baby name!</string>

    <string name="pull_header_text_loading">Loading…</string>
    <string name="pull_header_text_refresh">Pull down to refresh</string>
    <string name="load_more_text_loading">Next page</string>
    <string name="me_new_features">New Features</string>
    <string name="joywork_risk">Risks</string>

    <string name="me_title_stream_checker">stream checker</string>

    <string name="me_camera">Take a Photo</string>
    <string name="me_mine_no_pig_msg">Want to see your wallet information\n first bind your JD account~</string>
    <string name="me_mine_no_pig_cancel">Think Again</string>
    <string name="me_mine_no_pig_ok">To Bind</string>
    <string name="jdme_problem_fixed">Click OK and your problem will be fixed</string>
    <string name="me_setting_message">Message</string>
    <string name="me_setting_docs">Docs</string>
    <string name="me_setting_calendar_helper">Calendar Helper</string>
    <string name="me_setting_wiki_desc">Tap a supported word to see its Teampedia entry</string>
    <string name="me_setting_mandatory_desc">After enabling, the new message notification will appear at the top of the inbox page in the form of a card, so that important information will no longer be missed.</string>
    <string name="jdme_rsa_decode">RSA Decrypt</string>
    <string name="jdme_anr_test">ANR test</string>
    <string name="me_diagnosis_basic_info">Basic information</string>
    <string name="me_setting_os_version">System Version：%s</string>
    <string name="me_setting_current_version">Current Version：%s</string>
    <string name="me_diagnosis_net_status">Network status</string>
    <string name="me_diagnosis_net_desc_default">Check network connection and network connection certificate</string>
    <string name="me_diagnosis_net_desc_success">This device is connected to the internet.</string>
    <string name="me_diagnosis_net_desc_warning">This device is not connected to any available network.Please check your internet connection.</string>
    <string name="me_diagnosis_agent_desc_default">Check network proxy</string>
    <string name="me_diagnosis_agent_desc_success">No abnormality detected</string>
    <string name="me_diagnosis_agent_desc_warning">The proxy is detected to be enabled</string>
    <string name="me_diagnosis_dns_desc_default">If you can log in to JoyME but can\'t open websites，check DNS hosting service.</string>
    <string name="me_diagnosis_dns_desc_success">Normal</string>
    <string name="me_diagnosis_dns_desc_warning">Abnormal</string>
    <string name="me_diagnosis_service_desc_default">Check service stability and connection to JoyME</string>
    <string name="me_diagnosis_service_desc_success">Stable</string>
    <string name="me_diagnosis_service_desc_warning">Unstable</string>
    <string name="me_diagnosis_cancel">Cancel Diagnosis</string>
    <string name="me_diagnosis_again">Diagnosis Again</string>
    <string name="me_diagnosis_share_result">Share Log</string>
    <string name="me_diagnosis_start">Start Diagnosis</string>
    <string name="me_setting_net_diagnosis">Network Diagnosis</string>
    <string name="me_diagnosis_network_proxy">Network proxy</string>
    <string name="me_diagnosis_dns_hosting_service">DNS hosting service</string>
    <string name="me_diagnosis_service_stability">Service stability</string>
    <string name="me_diagnosis_innser_service_connected">Connect Intranet (Attendance)</string>
    <string name="me_diagnosis_innser_service_connected_desc_default">Check network connection to intranet</string>
    <string name="me_diagnosis_innser_service_connected_success">Connected</string>
    <string name="me_diagnosis_innser_service_connected_warning">Not connected</string>
    <string name="me_i_kown">I Have Known</string>

    <string name="me_calendar_js_add">Create meeting</string>
    <string name="me_calendar_js_select">Insert meeting</string>

    <string name="me_multitask_evalution_title">JD Communications</string>
    <string name="me_multitask_dialog_title">Floating window （%d）</string>
    <string name="me_multitask_add">Add to floating window</string>
    <string name="me_multitask_cancel">Cancel floating window</string>
    <string name="me_multitask_first_tip_content">Support adding tools to floating windows, fast switching, and improve office efficiency</string>
    <string name="me_multitask_first_tip_btn">I see</string>
    <string name="me_multitask_clear_when_down">Clear when down</string>
    <string name="me_multitask_max_size_tip_for_add">The number of  floating windows is full，please delete and add</string>
    <string name="me_multitask_clear">Clear</string>
    <string name="me_multitask_clear_all">Clear all</string>


    <string name="me_feedback_first_tip_content">Providing you with convenient system fault reporting and product use consultation.</string>

    <string name="exp_theme_skin">Theme Skin</string>
    <string name="exp_theme_forever">Free</string>
    <string name="exp_theme_limited_use">Limited</string>
    <string name="exp_theme_before">Before %s</string>
    <string name="exp_theme_using">Using</string>
    <string name="exp_theme_to_use">To Use</string>
    <string name="exp_theme_preview">Preview</string>
    <string name="exp_theme_preview_use">To Use</string>
    <string name="exp_theme_preview_cancel">Cancel</string>
    <string name="exp_theme_global">Global</string>
    <string name="exp_theme_setup_succeeded">Skin Setup Succeeded</string>
    <string name="exp_theme_setup_failed">Skin Setup Failed</string>
    <string name="exp_theme_obtain_before">Before %s Download</string>
    <string name="exp_theme_obtain">Download</string>
    <string name="exp_theme_skin_able">Change theme skin</string>
    <string name="exp_theme_over_obtain_due">Off shelves</string>
    <string name="exp_theme_over_use_due">Expired</string>
    <string name="jdme_feedback">Feedback</string>
    <string name="jdme_update_later">Update later</string>
    <string name="jdme_update_restart">Re-download</string>
    <string name="jdme_update_background">Background download</string>
    <string name="jdme_update_download_status">Trying to download…</string>
    <string name="jdme_update_continue">Continue</string>
    <string name="jdme_update_fail">There is something wrong with your network</string>
    <string name="jdme_update_md5_fail">File verification failed, please download again</string>

    <!--  科技音视频会议  -->
    <string name="meeting_start_conference_conference_busy">You‘re already in a meeting.</string>
    <string name="meeting_video_conference_rejected_in_other_device">You have operated on another terminal. This call has ended.</string>
    <string name="meeting_video_conference_hang_up">The invitee has hung up.The call is over.</string>
    <string name="meeting_video_conference_joined_in_other_device">You have joined this meeting on another terminal.</string>
    <string name="meeting_chat_bottom_voip_single">TimLine Call</string>
    <string name="meeting_video_conference_title">TimLine Video</string>
    <string name="meeting_notify_videoconference_single">Invites you to a video call</string>
    <string name="meeting_notify_videoconference_group">Invites you to join a video conference</string>
    <string name="meeting_voice_conference_title">TimLine Voice</string>
    <string name="meeting_video_conference_reserve1">Schedule</string>
    <string name="meeting_video_conference1">Create</string>
    <string name="meeting_join_video_conference1">Join</string>
    <string name="meeting_forward_msg_to">Send to:</string>
    <string name="meeting_send_count">Send (%d)</string>
    <string name="meeting_permission_conference_name">Meeting</string>

    <string name="me_multi_selected_reset_default">Reset</string>
    <string name="me_camera_permission_tips">JoyME needs to apply for your permission to make calls so that you can quickly use the function of making calls.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_virtual_number_title">Call tips</string>
    <string name="me_virtual_number_msg">To protect privacy and security, the real phone number will not be displayed. Please dial the following temporary number within %s minutes:</string>
    <string name="me_virtual_number_action_got">Close</string>
    <string name="me_im_dd_deeplink_not_support">Not supported</string>
    <string name="me_virtual_number_action_dial">Call now</string>
    <string name="notification_banner_auth_require">You have received a new message. Grant permission to show messages in banner while reading doc or having meetings.</string>

    <string name="me_float_add">Floating</string>
    <string name="me_float_add_success">Success</string>
    <string name="me_float_app_disable">Floating not available</string>
    <string name="no_reminder">No reminder</string>
    <string name="no_reminder_for_thirty">No reminder for 30 minutes</string>
    <string name="no_reminder_for_one_hour">No reminder for 1 hour</string>
    <string name="no_reminder_for_two_hours">No reminder for 2 hours</string>
    <string name="turn_off_notification">Close</string>
    <string name="in_app_banner_notification">JoyME show message in banner</string>
    <string name="me_setting_resource_subscription_settings">Resource Subscription Settings</string>
    <string name="me_setting_later_msg_create_task_automatically">The later msg create task automatically</string>
    <string name="me_setting_later_msg_create_task_automatically_description">After checking, they will be automatically converted to the tasks and completed.</string>
    <string name="me_setting_custom_view_settings">Customize View Settings</string>
    <string name="me_setting_filter_settings_description">Tasks will automatically aggregate according to its source. After unchecking, it will no longer be displayed by default, but the task will keep in your list.</string>
    <string name="you_have_received_one_message">You\'re received a message</string>
    <string name="sent_a_message_to_you">sent 1 message(s)</string>

    <string name="jdme_expand_abort">About</string>
    <string name="jdme_expand_evalute">Rate</string>
    <string name="jdme_expand_feedback">Feedback</string>
    <string name="jdme_expand_float_add">Add Floating</string>
    <string name="jdme_expand_float_cancel">Cancel Floating</string>
    <string name="jdme_expand_restart">Restart</string>
    <string name="jdme_expand_robot">Robot</string>
    <string name="jdme_expand_share">Send to chat</string>
    <string name="jdme_expand_recommend_add">Add to \n Recently Used</string>
    <string name="jdme_expand_recommend_cancel">Remove from \n Recently Used</string>

    <string name="me_app_add_favorite_success">Added to My App</string>
    <string name="me_app_add_favorite_fail">Failed</string>
    <string name="me_app_remove_favorite_success">Removed</string>
    <string name="me_app_remove_favorite_fail">Failed</string>
    <string name="me_appcenter_market_max_count_toast">24 Tools At Most</string>

    <string name="jdme_multi_tips">The current floating window is full,\n you can continue to add it after</string>
    <string name="jdme_multi_manager">Manage</string>

    <string name="me_face_setting_i_know">OK</string>
    <string name="me_face_setting_dialog_tip">The face recognition data and functions are stored and used only within the territory of mainland China，Hongkong，Macao and Taiwan.</string>

    <string name="me_guide_next">Next 1/2</string>
    <string name="me_guide_skip">Skip</string>
    <string name="me_guide_start">Start Now</string>

    <string name="refresh">Refresh</string>
    <string name="download">Download</string>
    <string name="downloading">Downloading</string>
    <string name="open_file_err">view file error</string>
    <string name="file_type_preview_error">file type cannot preview</string>
    <string name="download_success">Download successful</string>
    <string name="download_fail">Download failed</string>

    <string name="file_expired">The file has expired or been recalled</string>
    <string name="file_exceed_limit">The file size exceeds the limit and cannot be viewed</string>
    <string name="file_visit_too_more">Too many people viewing, please try again later</string>
    <string name="file_net_error">Network error, please try again later</string>
    <string name="file_view_error">File preview failed, please try again later</string>
    <string name="jdme_placeholder_slogan">JoyME, Create It Together.</string>

    <string name="incompatible_file_format">Incompatible file format</string>

    <string name="jdme_guide_home_mail_title">New Mail</string>
    <string name="jdme_guide_home_mail_sub_title">Integrate messages, docs, etc. Make emails more secure and efficient</string>
    <string name="jdme_guide_home_process_title">New Process Search</string>
    <string name="jdme_guide_home_process_sub_title">Support keywords search for processes</string>
    <string name="jdme_guide_home_apply_title">New Approval</string>
    <string name="jdme_guide_home_apply_sub_title">Faster approval in JoyME</string>

    <string name="jdme_guide_home_try">Try Now</string>
    <string name="jdme_guide_home_iknow">Go it</string>
    <string name="jdme_guide_home_next">Next %s/%s</string>
    <string name="joynote_is_playing_tips">You have a playing media in progress. Please retry later.</string>

    <string name="jdme_api_auth_title">Permissions Request</string>
    <string name="jdme_calendar_scheme_off">Off</string>
    <string name="jdme_calendar_scheme_work">Work</string>

    <string name="jdme_copy_link_success">Copy link successful</string>
    <string name="jdme_share_failed">Share failed. Please try again later</string>

    <string name="jdme_auth_start_biometric_authentication">Allow the app to perform biometric authentication.</string>
    <string name="jdme_auth_start_speech_recognition">Allow applications to obtain text information after user voice recognition.</string>
    <string name="jdme_auth_choose_photo">Allows App to select an image from the system photo album or capture a photo using the camera.</string>
    <string name="jdme_auth_choose_file">Allows App to read local file contents.</string>
    <string name="multitask_media_playlist_title">Media Tasks</string>
    <string name="multitask_media_readinglist_title">Reading Tasks</string>

    <string name="jdme_open_in_browser">Leaving JoyME and opening in browser</string>
    <string name="jdme_link_blocked_tip">The link has been prohibited due to security risks such as guiding users to external sites.</string>

    <string name="me_guide_click_open">Expand by clicking</string>
    <string name="jdme_im_files_title">Download</string>
    <string name="jdme_no_im_files">None download messages files</string>
    <string name="jdme_im_selected_files">Selected: </string>
    <string name="jdme_im_not_support">Do not support this file type</string>
    <string name="jdme_im_n_files">%d files</string>
    <string name="jdme_im_exceed_size">Exceed size limit</string>
    <string name="jdme_im_exceed_count">Exceed count limit</string>
</resources>
