<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--网盘样式-->
    <style name="NetDiskStyle" parent="mae_net_disk_base_theme">
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="mae_net_disk_bar_text_color">@android:color/black</item>
        <item name="mae_net_disk_major_color">#E4393C</item>
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>
    <!--多任务窗口切换专用-->
    <!--继承Theme.AppCompat.NoActionBar，不显示标题栏-->
    <style name="MultiTaskTheme" parent="Theme.AppCompat.NoActionBar">
        <!--不设置activity进入和退出动画样式-->
        <item name="android:windowAnimationStyle">@null</item>
        <!--设置窗口的背景为透明，设置透明背景必须要设置此项-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--设置窗口的背景是否为半透明，设置透明背景必须要设置此项-->
        <item name="android:windowIsTranslucent">true</item>
        <!--设置状态栏的背景为半透明-->
        <item name="android:windowTranslucentStatus">true</item>

        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
    </style>

</resources>