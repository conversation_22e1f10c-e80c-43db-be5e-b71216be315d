<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/ripple_material_light">

    <!-- Ripple mask - applied to all selector states -->
    <item android:id="@android:id/mask">
        <color android:color="@color/black_divider" />
    </item>

    <!--<item>-->
    <!--<selector>-->
    <!--<item android:state_pressed="true">-->
    <!--<solid android:color="@color/ripple_material_light" />-->
    <!--</item>-->
    <!--</selector>-->
    <!--</item>-->

    <item>
        <selector>
            <item>
                <shape>
                    <solid android:color="@color/transparent" />
                </shape>
            </item>
        </selector>
    </item>

</ripple>