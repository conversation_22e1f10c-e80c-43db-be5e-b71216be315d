<?xml version="1.0" encoding="utf-8"?><!-- 指定动画匀速改变 -->
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:interpolator="@android:anim/linear_interpolator">
    <!-- 定义缩放变换 -->
    <!--<scale android:fromXScale="1.0"
        android:toXScale="0.01"
        android:fromYScale="1.0"
        android:toYScale="0.01"
        android:pivotX="50%"
        android:pivotY="50%"
        android:fillAfter="true"
        android:duration="3000"/> -->
    <!-- 定义透明度的变换 -->
    <alpha
        android:duration="500"
        android:fromAlpha="1"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:toAlpha="0.05" />
    <!-- 定义旋转变换 -->
    <!--<rotate
        android:fromDegrees="0"
        android:toDegrees="90"
        android:pivotX="0%"
        android:pivotY="0%"
        android:duration="3000"/>-->
</set>