<?xml version="1.0" encoding="utf-8"?><!-- 更多界面添加or移除按钮样式 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下去样式 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/black_light" />
            <corners android:radius="2dip" />
            <stroke android:width="@dimen/me_divide_height_min" android:color="@color/black_divider" />
        </shape>
    </item>

    <!-- 不可用样式 
    <item android:state_enabled="false">
    	<shape>
             <solid android:color="@color/black_transparent_20" />
            <corners android:radius="2dip" />
            <stroke android:width="@dimen/divide_height_min" android:color="@color/black_divider" />
        </shape>
    </item>-->
    <!-- 默认 -->
    <item>
        <shape>
            <solid android:color="@color/white_light_gray_f8f8f8" />
            <corners android:radius="2dip" />
            <stroke android:width="@dimen/me_divide_height_min" android:color="@color/black_divider" />
        </shape>
    </item>

</selector>