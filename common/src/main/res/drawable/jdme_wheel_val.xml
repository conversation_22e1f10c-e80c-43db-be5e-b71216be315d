<?xml version="1.0" encoding="utf-8"?>

<!-- 
    Android Wheel Control.
    http://android-devblog.blogspot.com/2010/05/wheel-ui-contol.html
   
    Copyright 2010 <PERSON>s
    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:top="-35dp">
        <shape xmlns:android="http://schemas.android.com/apk/res/android"
            android:shape="line">
            <stroke
                android:width="1dp"
                android:color="#C5CEDA" />
        </shape>
    </item>
    <item android:top="35dp">
        <shape xmlns:android="http://schemas.android.com/apk/res/android"
            android:shape="line">
            <stroke
                android:width="1dp"
                android:color="#C5CEDA" />
        </shape>
    </item>
</layer-list>