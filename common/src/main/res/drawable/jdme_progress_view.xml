<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!--设置progress背景颜色-->
    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="10dp" />
            <solid android:color="#E1E3E6" />
        </shape>
    </item>
    <!--设置progress进度条颜色-->
    <item android:id="@android:id/progress">
<!--        <clip android:clipOrientation="horizontal">-->
<!--            <shape>-->
<!--                <corners android:radius="10dp" />-->
<!--                <gradient-->
<!--                    android:endColor="#FF7B5E"-->
<!--                    android:startColor="#FF472F" />-->
<!--            </shape>-->
<!--        </clip>-->
        <scale android:scaleWidth="100%">
            <shape>
                <corners android:radius="10dp" />
                <gradient
                    android:endColor="#FF7B5E"
                    android:startColor="#FF472F" />
            </shape>
        </scale>
    </item>
</layer-list>
