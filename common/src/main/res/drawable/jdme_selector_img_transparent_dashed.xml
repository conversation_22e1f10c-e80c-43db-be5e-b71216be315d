<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下去样式 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/black_transparent_12"/>
            <stroke android:width="@dimen/me_divide_height_min"
                    android:color="@color/conference_black_color"
                    android:dashGap="3dp"
                    android:dashWidth="3dp"/>
        </shape>
    </item>

    <!-- 不可用样式 -->
    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/black_transparent_12"/>
            <stroke android:width="@dimen/me_divide_height_min"
                    android:color="@color/conference_black_color"
                    android:dashGap="3dp"
                    android:dashWidth="3dp"/>
        </shape>
    </item>

    <!-- 默认 -->
    <item>
        <shape>
            <solid android:color="@color/transparent"/>
            <stroke android:width="@dimen/me_divide_height_min"
                    android:color="@color/conference_black_color"
                    android:dashGap="3dp"
                    android:dashWidth="3dp"/>
        </shape>
    </item>

</selector>