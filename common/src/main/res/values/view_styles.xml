<resources xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">

    <!-- radionbutton tab tabhost -->
    <style name="tab_rb_style">
        <item name="android:paddingTop">5dip</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:button">@null</item>
        <item name="android:textColor">@color/selector_tab_tv_color</item>
        <item name="android:gravity">center_horizontal|bottom</item>
        <item name="android:layout_gravity">bottom</item>
    </style>

    <!-- ## 设置容器项目		start  ################################ -->
    <!-- 设置项 - 左 -->
    <style name="set_title_left">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_marginLeft">12dip</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#848484</item>
    </style>

    <!-- 设置项 - 右 -->
    <style name="set_title_right" parent="@style/set_title_left">
        <item name="android:layout_marginRight">12dip</item>
        <item name="android:textColor">@color/black_first</item>
        <item name="android:gravity">right</item>
    </style>

    <!-- 设置项容器 - 不显示线条 -->
    <style name="set_container_no_divide">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:background">@drawable/jdme_selector_set_no_divide_item</item>
        <item name="android:paddingLeft">8dip</item>
        <item name="android:paddingRight">8dip</item>
        <item name="android:clickable">true</item>
    </style>

    <!-- 设置项容器 - 显示底部线条 -->
    <style name="set_container_bottom" parent="@style/set_container_no_divide"></style>

    <!-- ## 设置容器项目		end  ################################ -->


    <style name="AnimBottom" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/jdme_push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/jdme_push_bottom_out</item>
    </style>


    <style name="DialogLightDatePicker" parent="Theme.AppCompat.Light.Dialog">
        <!-- 修改 button 文字颜色 -->
        <item name="colorAccent">?attr/me_theme_major_color</item>
        <!-- 标题文字颜色 -->
        <item name="android:textColorPrimary">@color/black_main_title</item>
    </style>

    <!-- for 日期 时间  低于 api 5.0 -->
    <style name="lightDialog_for_date_dialog" parent="DialogLightDatePicker">
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <!-- 启动界面全屏样式，跟随主题颜色走Theme.Translucent.NoTitleBar.Fullscreen Theme.NoTitleBar.Fullscreen-->
    <style name="startUpTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColor">#E4393C</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <!--使用windowBackground，图片被导航条挡住，将图标向上移动45dp-->
        <!--使用background，不会被导航条挡住，但在android 12以后会显示白页，android 12提供了splash screen接口，只对windowBackground兼容-->
        <!--android 12提供的splash screen接口比较死板，底部的brandingImage存在图片尺寸限制，图片显示不全，暂不考虑使用该接口-->
        <item name="android:windowBackground">@drawable/jdme_launch_bg</item>

    </style>

    <style name="TabletPlaceHolderTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@color/translucent</item>
        <item name="android:background">@color/translucent</item>
        <item name="android:statusBarColor">@android:color/white</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@android:color/white</item>
    </style>

    <!--StartupActivity启动后切换透明背景，否则生日显示不正常，另一种解法是在startUpTheme的windowBackground设置背景，但是需要计算导航条高度-->
    <style name="startUpTheme2" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColor">#E4393C</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:statusBarColor">@android:color/white</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@android:color/white</item>
        <item name="android:background">@color/translucent</item>
    </style>

    <!-- Dialog 淡入淡出动画 -->
    <style name="animation_fade" parent="android:Animation">
        <item name="@android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="@android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <!-- 分割线 -->
    <style name="line_divide_thin">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/me_divide_height_min</item>
        <item name="android:background">@color/black_divider</item>
    </style>

    <style name="line_divide_middle" parent="@style/line_divide_thin">
        <item name="android:layout_height">@dimen/me_divide_height_middle</item>
    </style>

    <!-- 小文本输入框 -->
    <style name="my_small_edittext">
        <item name="android:layout_height">28dip</item>
        <item name="android:layout_width">56dip</item>
        <item name="android:background">@drawable/jdme_bg_edittext</item>
        <item name="android:textSize">@dimen/me_text_size_middle</item>
        <item name="android:textColor">@color/black_assist</item>
        <item name="android:textCursorDrawable" tools:ignore="NewApi">@drawable/jdme_edit_cursor
        </item>
    </style>

    <!-- 小按钮 灰白色： 高度 32dip -->
    <style name="my_less_white_gray_btn">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minHeight">32dip</item>
        <item name="android:minWidth">56dip</item>
        <item name="android:textSize">14sp</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/jdme_selector_small_gray_white_button</item>
        <item name="android:textColor">@color/selector_small_gray_white_button_color</item>
    </style>

    <!-- viewpager 标题 -->
    <style name="tv_viewpager_title">
        <item name="android:textColor">@color/black_main_title</item>
        <item name="android:textSize">@dimen/me_text_size_larger</item>
        <item name="android:layout_width">0dip</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- 开关 -->
    <style name="SwitchStyle_Custom">
        <!-- 跟随主题颜色走 -->
        <item name="colorControlActivated">#E4393C</item>
        <!-- Inactive thumb color -->
        <item name="colorSwitchThumbNormal">@color/light_gray</item>
        <!-- Inactive track color(30% transparency) -->
        <item name="android:colorForeground">@color/light_gray</item>
    </style>

    <style name="SwitchStyle">
        <item name="android:thumb">@drawable/jdme_switch_thumb_selector</item>
        <item name="track">@drawable/jdme_switch_track_selector</item>
    </style>

    <!-- 红包样式 -->
    <style name="ME_red_packet_open_anim">
        <item name="android:windowEnterAnimation">@anim/jdme_red_packet_open</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>

    <style name="ME_red_packet_dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/ME_red_packet_dialog_anim</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- 界面样式： 界面切换动画 -->
    <style name="ME_red_packet_dialog_anim" parent="@android:style/Animation.Translucent">
        <item name="android:windowEnterAnimation">@anim/jdme_activity_open_enter</item>
        <item name="android:windowExitAnimation">@anim/jdme_activity_close_exit</item>
    </style>

    <!-- 去掉dialog背景，标题 -->
    <style name="myDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!-- 亮的dialog样式 -->
    <style name="lightDialog" parent="Theme.AppCompat.Light.Dialog.Alert">
        <!-- 修改 button 文字颜色 -->
        <item name="colorAccent">@color/skin_color_default</item>
        <!-- 标题文字颜色 -->
        <item name="android:textColorPrimary">@color/black_main_title</item>
        <!-- jdme_list dialog 时显示条目线条
        <item name="android:listDivider">@drawable/list_divider</item>
        <item name="listDividerAlertDialog">@drawable/list_divider</item>-->
    </style>


</resources>