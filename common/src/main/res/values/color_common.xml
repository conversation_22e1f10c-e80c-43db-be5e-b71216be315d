<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="comm_background">@color/comm_most_slight_gray</color>
    <color name="comm_background_transparent">#7f000000</color>
    <color name="comm_black">#000000</color>
    <color name="comm_blue">#0000ff</color>
    <color name="comm_btn_bg_nor">@color/comm_blue</color>
    <color name="comm_btn_bg_sel">@color/comm_blue</color>
    <color name="comm_button_divider">@color/comm_sliver</color>
    <color name="comm_dark_gray">#2e2d2d</color>
    <color name="comm_divider">@color/comm_light_gray</color>
    <color name="comm_gray">#848484</color>
    <color name="comm_green">#00ff00</color>
    <color name="comm_grey">#7b7d83</color>
    <color name="comm_light_gray">#eef1f4</color>
    <color name="comm_light_red">#f0250f</color>
    <color name="comm_most_slight_gray">#f5f8fc</color>
    <color name="comm_navigation_background">@color/comm_white</color>
    <color name="comm_orange">#f86e21</color>
    <color name="comm_red">#ff0000</color>
    <color name="comm_slight_gray">#bbbbbb</color>
    <color name="comm_sliver">#d6dbe1</color>
    <color name="comm_text_black">@color/comm_black</color>
    <color name="comm_text_nor">@color/comm_dark_gray</color>
    <color name="comm_text_normal">@color/comm_gray</color>
    <color name="comm_text_red">@color/comm_light_red</color>
    <color name="comm_text_secondary">@color/comm_slight_gray</color>
    <color name="comm_text_title">@color/comm_dark_gray</color>
    <color name="comm_text_warn">@color/comm_orange</color>
    <color name="comm_text_white">@color/comm_white</color>
    <color name="comm_theme">@color/comm_blue</color>
    <color name="comm_transparent">#00000000</color>
    <color name="comm_white">#ffffff</color>
    <color name="refresh_text">@color/comm_grey</color>
    <color name="common_ripple">#14000000</color>
    <dimen name="comm_divider_height">0.8dp</dimen>
    <dimen name="comm_font_example">6sp</dimen>
    <dimen name="comm_navigation_height">45dp</dimen>
    <dimen name="comm_spacing_horizontal">12dp</dimen>
    <dimen name="comm_text_normal">14sp</dimen>
    <dimen name="comm_text_normal_large">15sp</dimen>
    <dimen name="comm_text_normal_xlarge">16sp</dimen>
    <dimen name="comm_text_secondary">13sp</dimen>
    <dimen name="comm_text_small">12sp</dimen>
    <dimen name="comm_text_title">18sp</dimen>
    <dimen name="comm_text_title_large">19sp</dimen>
    <dimen name="comm_text_xsmall">11sp</dimen>


    <!-- 通用颜色 -->
    <!--<color name="comm_transparent">#00000000</color>-->

    <!-- 白色 -->
    <!--<color name="comm_white">#ffffff</color>-->

    <!-- 黑色 -->
    <!--<color name="comm_black">#000000</color>-->

    <!-- 红色 -->
    <!--<color name="comm_red">#ff0000</color>-->

    <!-- 黄色 -->
    <color name="comm_yellow">#fdbe2c</color>                                  <!-- 按钮黄 -->

    <!-- 蓝色 -->
    <!--<color name="comm_blue">#0000ff</color>-->

    <!-- 绿色 -->
    <!--<color name="comm_green">#00ff00</color>-->

    <!-- 灰色 -->
    <!--<color name="comm_grey">#7b7d83</color>-->

    <!-- 请尽量不要直接使用上面的comm_xxx颜色值，使用如下定义的颜色，如果缺少对应颜色的，请询问comm_color的owner -->

    <!-- 通用页面颜色 -->
    <!--<color name="comm_background">@color/comm_grey</color>                     &lt;!&ndash; 背景颜色 &ndash;&gt;-->
    <!--<color name="comm_theme">@color/comm_blue</color>                          &lt;!&ndash; 主题颜色 &ndash;&gt;-->
    <color name="comm_devider_line">#e1e2e5</color>                            <!-- 分割线的颜色 -->

    <!-- 通用控件颜色 -->
    <!--<color name="comm_btn_bg_nor">@color/comm_blue</color>-->
    <!--<color name="comm_btn_bg_sel">@color/comm_blue</color>-->
    <color name="comm_btn_bg_disable">@color/comm_grey</color>

    <!-- 通用字体颜色 -->
    <!--<color name="comm_text_nor">@color/comm_black</color>                      &lt;!&ndash;  文案主色 &ndash;&gt;-->
    <color name="comm_text_desc">@color/comm_grey
    </color>                      <!--  文案辅色（辅助说明问题） -->
    <color name="comm_text_desc1">@color/comm_yellow</color>                   <!--  文案橙色 -->
    <color name="comm_text_hint">@color/comm_grey</color>                      <!--  文案 输入框内，提示灰-->
    <color name="comm_text_title_grey">@color/comm_grey</color>                <!--  文案 输入框内，提示灰-->

</resources>
