<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CornerRadius">
        <attr name="tl" format="dimension" />
        <attr name="tr" format="dimension" />
        <attr name="br" format="dimension" />
        <attr name="bl" format="dimension" />
    </declare-styleable>
    <!-- Banner图 -->
    <declare-styleable name="Banner">
        <attr name="delay_time" format="integer" />
        <attr name="scroll_time" format="integer" />
        <attr name="is_auto_play" format="boolean" />
        <attr name="title_background" format="color|reference" />
        <attr name="title_textcolor" format="color" />
        <attr name="title_textsize" format="dimension" />
        <attr name="title_height" format="dimension" />
        <attr name="banner_layout" format="reference" />
        <attr name="banner_default_image" format="reference" />
        <attr name="indicator_width" format="dimension" />
        <attr name="indicator_height" format="dimension" />
        <attr name="indicator_margin" format="dimension" />
        <attr name="indicator_drawable_selected" format="reference" />
        <attr name="indicator_drawable_unselected" format="reference" />
        <attr name="layout_id" format="reference" />
        <attr name="image_scale_type" format="enum">
            <enum name="center" value="0" />
            <enum name="center_crop" value="1" />
            <enum name="center_inside" value="2" />
            <enum name="fit_center" value="3" />
            <enum name="fit_end" value="4" />
            <enum name="fit_start" value="5" />
            <enum name="fit_xy" value="6" />
            <enum name="matrix" value="7" />
        </attr>

        <!--<attr name="indicator_animator" format="reference" />-->
        <!--<attr name="indicator_animator_reverse" format="reference" />-->
    </declare-styleable>

    <declare-styleable name="PullRefreshLayout">
        <attr name="header_extra_height" format="dimension" />
        <!--<attr name="header_default_background" format="color"/>
        <attr name="footer_background" format="color"/>-->
    </declare-styleable>

    <declare-styleable name="PullToRefreshLayout">
        <attr name="view_error" format="reference" />
        <attr name="view_empty" format="reference" />
        <attr name="view_loading" format="reference" />
    </declare-styleable>

    <!-- RefreshLayout 下拉刷新，上拉加载控件 -->
    <declare-styleable name="RefreshRecyclerLayout">
        <attr name="me_load_manual" format="boolean" />
        <attr name="me_refresh_scheme_color" format="reference" />
        <attr name="me_refresh_enable" format="boolean" />
    </declare-styleable>
    <attr name="me_border_width" format="dimension" />
    <attr name="me_border_color" format="color" />
    <!-- 圆图 -->
    <declare-styleable name="CircleImageView">
        <attr name="me_border_width" />
        <attr name="me_border_color" />
    </declare-styleable>
    <declare-styleable name="CircleImageViewWithGap">
        <attr name="me_border_width" />
        <attr name="me_border_color" />
        <attr name="me_border_gap" format="dimension" />
    </declare-styleable>
    <!-- 9宫格手势解锁 -->
    <declare-styleable name="LockPatternView">
        <attr name="me_aspect" format="string" />
        <!--连线的颜色，手指还在屏幕上移动时的颜色-->
        <attr name="me_lineProgressColor" format="color" />
        <!--连线的颜色，图案绘制正确时的颜色。默认时与me_lineProgressColor一致-->
        <attr name="me_lineCorrectColor" format="color" />
        <!--连线的颜色，图案绘制错误时的颜色-->
        <attr name="me_lineWrongColor" format="color" />
        <!--连线的粗细-->
        <attr name="me_diameterFactor" format="float" />
        <!--图标的相对大小，可以设置每一个图标占整体View的宽高百分比-->
        <attr name="me_circleRelativeFactor" format="float" />
    </declare-styleable>

    <!-- XListView -->
    <declare-styleable name="my_list_view">
        <attr name="me_always_scroll" format="boolean" />
    </declare-styleable>


    <!-- 主题相关 -->
    <declare-styleable name="Theme">
        <attr name="me_text_color" format="color" />
        <attr name="me_text_size" format="dimension" />
        <attr name="me_text_res" format="reference" />
        <attr name="me_app_bg" format="color|reference" />
        <attr name="me_menu_text_color" format="color|reference" />

        <!-- ME Style -->
        <!-- 主题颜色 -->
        <attr name="me_theme_major_color" format="color|reference" />
        <attr name="me_windowBackground" format="color|reference" />
        <attr name="me_actionbar_bg" format="color|reference" />
        <attr name="me_icon_user" format="reference" />
        <attr name="me_icon_pwd" format="reference" />
        <attr name="me_icon_detail_arrow" format="reference" />
        <attr name="me_line_color" format="color|reference" />
        <attr name="me_btn_selector" format="reference" />
        <attr name="me_btn_selector_disable_gray" format="reference" />
        <attr name="me_btn_borderness_selector" format="reference" />
        <attr name="me_borderness_btn_color" format="color|reference" />
        <attr name="me_check_box_selector" format="reference" />
        <attr name="me_feedback_tab_selector" format="color|reference" />
        <attr name="me_btn_black_border_selector" format="reference" />
    </declare-styleable>
    <declare-styleable name="CircleTextView">
        <attr name="me_text" format="string" />
    </declare-styleable>

    <!-- 布局加载 -->
    <declare-styleable name="FrameView">
        <attr name="me_fv_progressInfo" format="string" />
        <attr name="me_fv_emptyIcon" format="reference" />
        <attr name="me_fv_emptyInfo" format="string" />
        <attr name="me_fv_errorIcon" format="reference" />
        <attr name="me_fv_errorInfo" format="string" />
        <attr name="me_fv_repeatIcon" format="reference" />
        <attr name="me_fv_repeatInfo" format="string" />
        <attr name="me_fv_customLayout" format="reference" />
        <attr name="me_fv_frame">
            <flag name="container" value="0x0" />
            <flag name="progress" value="0x1" />
            <flag name="empty" value="0x2" />
            <flag name="error" value="0x3" />
            <flag name="repeat" value="0x4" />
        </attr>
    </declare-styleable>


    <declare-styleable name="PagerSlidingTabStrip">
        <attr name="me_pstsIndicatorColor" format="color" />
        <attr name="me_pstsUnderlineColor" format="color" />
        <attr name="me_pstsDividerColor" format="color" />
        <attr name="me_pstsIndicatorHeight" format="dimension" />
        <attr name="me_pstsUnderlineHeight" format="dimension" />
        <attr name="me_pstsDividerPadding" format="dimension" />
        <attr name="me_pstsTabPaddingLeftRight" format="dimension" />
        <attr name="me_pstsScrollOffset" format="dimension" />
        <attr name="me_pstsTabBackground" format="reference" />
        <attr name="me_pstsShouldExpand" format="boolean" />
        <attr name="me_pstsTextAllCaps" format="boolean" />
        <attr name="me_zmsTabTextSize" format="dimension" />
        <attr name="me_zmsTabTextColor" format="color" />
        <attr name="me_zmsSelectedTabTextSize" format="dimension" />
        <attr name="me_zmsSelectedTabTextColor" format="color" />
    </declare-styleable>

    <!-- ******************************** 进度circle for 京东用车 Start ******************************  -->
    <!-- 外环宽 -->
    <attr name="me_tv_circleWidth" format="dimension" />
    <!-- 外环颜色 -->
    <attr name="me_tv_circleColor" format="color" />
    <!-- 滚动小圆半径 -->
    <attr name="me_tv_ballRadius" format="dimension" />
    <!-- 圆填充颜色 -->
    <attr name="me_tv_fillColor" format="color" />
    <!-- 小圆转动一周花费时间 毫秒 -->
    <attr name="me_tv_shotTime" format="integer" />
    <!-- 显示波浪 -->
    <attr name="me_tv_showWave" format="boolean" />
    <!-- 显示小球 -->
    <attr name="me_tv_showBall" format="boolean" />
    <!-- 开始计数 -->
    <attr name="me_tv_startNum" format="integer" />
    <!-- 最大计数 -->
    <attr name="me_tv_maxNum" format="integer" />
    <!-- 文字大小 -->
    <attr name="me_tv_texSize" format="dimension" />

    <declare-styleable name="CircleProgressRefreshView">
        <attr name="me_tv_circleWidth" />
        <attr name="me_tv_circleColor" />
        <attr name="me_tv_ballRadius" />
        <attr name="me_tv_fillColor" />
        <attr name="me_tv_shotTime" />
        <attr name="me_tv_showWave" />
        <attr name="me_tv_showBall" />
    </declare-styleable>

    <!-- 应答中 计时器circleView -->
    <declare-styleable name="CircleProgressTimerView">
        <attr name="me_tv_startNum" />
        <attr name="me_tv_maxNum" />
        <attr name="me_tv_texSize" />
    </declare-styleable>

    <!-- 赶来中 circleView -->
    <declare-styleable name="CircleProgressCameView">
        <attr name="me_tv_texSize" />
        <!-- 距离，米为单位 -->
        <attr name="me_tv_distance" format="integer" />
    </declare-styleable>

    <!-- 车子运行中 circleView -->
    <declare-styleable name="CircleProgressRunningView">
        <attr name="me_tv_texSize" />
        <attr name="me_tv_img" format="reference" />
    </declare-styleable>
    <!-- ******************************** 进度circle for 京东用车 End ******************************  -->

    <declare-styleable name="CycleProgressView">

        <!-- 背景颜色 -->
        <attr name="me_cycle_background" format="color" />
        <!-- 进度颜色 -->
        <attr name="me_progress_color" format="color" />
        <!-- 宽度 -->
        <attr name="me_cycle_width" format="dimension" />
        <!-- 字体颜色 -->
        <attr name="textColor" format="color" />
        <!-- 字体大小 -->
        <attr name="textSize" format="dimension" />
        <!-- 最大进度 -->
        <attr name="me_max_progress" format="integer" />
        <!-- 进度 -->
        <attr name="me_progress" format="integer" />
        <!-- 进度点 -->
        <attr name="me_thumb" format="reference" />
        <!-- 进度点宽高 -->
        <attr name="me_thumbSize" format="integer" />
    </declare-styleable>

    <declare-styleable name="ArcProgress">
        <!-- 背景颜色 -->
        <attr name="me_progressBackground" format="color" />
        <!-- 进度颜色 -->
        <attr name="me_progressColor" format="color" />
        <!-- 宽度 -->
        <attr name="me_progressWidth" format="dimension" />
        <!-- 最大进度 -->
        <attr name="me_maxProgress" format="integer" />
        <!-- 进度点 -->
        <attr name="me_progressPointColor" format="color" />
        <!-- 进度点宽高 -->
        <attr name="me_progressPointSize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CircleBgTextView">
        <attr name="tvStyle" format="enum">
            <enum name="stroke" value="1" />
            <enum name="fill" value="2" />
        </attr>
        <attr name="bgColor" format="color" />
        <attr name="strokeWidth" format="dimension" />
    </declare-styleable>

    <!-- 新版流程中心  -->
    <declare-styleable name="FlowMainItemView">
        <attr name="fv_top_bg_start_color" format="color|reference" />
        <attr name="fv_top_bg_end_color" format="color|reference" />
        <attr name="fv_bottom_bg_color" format="color|reference" />
        <attr name="fv_iconDrawable" format="reference" />
        <attr name="fv_title" format="string" />
        <attr name="fv_tips_num" format="string" />
        <attr name="fv_tips_bg_color" format="color|reference" />
    </declare-styleable>
    <declare-styleable name="RiseNumberTextView">
        <attr name="decimal_places" format="integer" />
    </declare-styleable>

    <declare-styleable name="FlowLayout">
        <attr name="fl_horizontal_space" format="dimension" />
        <attr name="fl_vertical_space" format="dimension" />
        <attr name="fl_gravity" format="enum">
            <enum name="LEFT" value="0" />
            <enum name="HORIZONTAL_CENTER" value="1" />
            <enum name="RIGHT" value="2" />
            <enum name="BOTTOM" value="3" />
        </attr>
    </declare-styleable>

    <!-- SelectFlowLayout -->
    <declare-styleable name="SelectFlowLayout">
        <attr name="fl_item_selector" format="reference" />
    </declare-styleable>

    <!--拍照遮罩-->
    <declare-styleable name="RectMaskView">
        <attr name="rect_mask_color" format="color" />
        <attr name="rect_mask_left" format="dimension" />
        <attr name="rect_mask_top" format="dimension" />
        <attr name="rect_mask_right" format="dimension" />
        <attr name="rect_mask_bottom" format="dimension" />
        <attr name="rect_mask_corner_color" format="color" />
        <attr name="rect_mask_corner_width" format="dimension" />
        <attr name="rect_mask_corner_length" format="dimension" />
    </declare-styleable>

    <!--相机对焦-->
    <declare-styleable name="CircleFocusView">
        <attr name="focus_circle_radius" format="dimension" />
        <attr name="focus_circle_width" format="dimension" />
        <attr name="focus_circle_color" format="color" />
    </declare-styleable>

    <declare-styleable name="SimpleRoundImageView">
        <attr name="round_radius" format="dimension" />
    </declare-styleable>

    <!--设置项-->
    <attr name="setting_name" format="string" />
    <attr name="setting_badge" format="string" />
    <attr name="setting_tips" format="string" />
    <attr name="setting_description" format="string" />
    <attr name="setting_show_divider" format="boolean" />
    <attr name="setting_divider_margin_start" format="dimension" />
    <attr name="setting_divider_margin_end" format="dimension" />
    <attr name="setting_icon" format="reference" />
    <attr name="setting_switch_status" format="enum">
        <enum name="close" value="0" />
        <enum name="open" value="1" />
    </attr>
    <attr name="setting_show_arrow" format="boolean" />
    <declare-styleable name="SettingItem">
        <attr name="setting_name" />
        <attr name="setting_badge" />
        <attr name="setting_tips" />
        <attr name="setting_description" />
        <attr name="setting_show_divider" />
        <attr name="setting_divider_margin_start" />
        <attr name="setting_divider_margin_end" />
        <attr name="setting_icon" />
        <attr name="setting_show_arrow" />
    </declare-styleable>
    <attr name="setting_item_corner" format="enum">
        <enum name="all" value="0" />
        <enum name="top" value="1" />
        <enum name="bottom" value="2" />
        <enum name="none" value="3" />
    </attr>
    <declare-styleable name="SettingItem2">
        <attr name="setting_name" />
        <attr name="setting_tips" />
        <attr name="setting_description" />
        <attr name="setting_show_divider" />
        <attr name="setting_divider_margin_start" />
        <attr name="setting_divider_margin_end" />
        <attr name="setting_icon" />
        <attr name="setting_show_arrow" />
        <attr name="setting_item_corner" />
    </declare-styleable>
    <declare-styleable name="SwitchSettingItem">
        <attr name="setting_name" />
        <attr name="setting_description" />
        <attr name="setting_show_divider" />
        <attr name="setting_switch_status" />
        <attr name="setting_item_corner" />
    </declare-styleable>

    <declare-styleable name="ApplyNodeLayout">
        <attr name="line_offset" format="dimension" />
    </declare-styleable>
    <declare-styleable name="ApplyNodeLayout_Layout">
        <attr name="layout_node_line" format="reference" />
        <attr name="layout_node_line_height" format="dimension" />
    </declare-styleable>

    <declare-styleable name="TDTextView">
        <attr name="td_textColor" format="color" />
        <attr name="td_text" format="string" />
        <attr name="td_textSize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="RoundBackgroundTextView">
        <attr name="roundBackgroundHorizontalPadding" format="dimension" />
        <attr name="roundBackgroundVerticalPadding" format="dimension" />
        <attr name="roundBackground" format="reference" />
        <attr name="roundBackgroundStart" format="reference" />
        <attr name="roundBackgroundMiddle" format="reference" />
        <attr name="roundBackgroundEnd" format="reference" />
    </declare-styleable>
    <!--签名控件-->
    <declare-styleable name="SignaturePad">
        <attr name="penMinWidth" format="dimension" />
        <attr name="penMaxWidth" format="dimension" />
        <attr name="penColor" format="color" />
        <attr name="velocityFilterWeight" format="float" />
        <attr name="clearOnDoubleClick" format="boolean" />
    </declare-styleable>

    <declare-styleable name="MESeekBarStyle">
        <attr name="drawable" format="reference" />
        <attr name="lineColor" format="color" />
        <attr name="lineWidth" format="dimension" />
        <attr name="verticalLine" format="dimension" />
    </declare-styleable>


    <declare-styleable name="WheelPicker">
        <attr name="wheel_data" format="reference" />
        <attr name="wheel_selected_item_position" format="integer" />
        <attr name="wheel_item_text_size" format="dimension" />
        <attr name="wheel_item_text_color" format="color" />
        <attr name="wheel_selected_item_text_color" format="color" />
        <attr name="wheel_same_width" format="boolean" />
        <attr name="wheel_maximum_width_text" format="string" />
        <attr name="wheel_maximum_width_text_position" format="integer" />
        <attr name="wheel_visible_item_count" format="integer" />
        <attr name="wheel_item_space" format="dimension" />
        <attr name="wheel_cyclic" format="boolean" />
        <attr name="wheel_indicator" format="boolean" />
        <attr name="wheel_indicator_color" format="color" />
        <attr name="wheel_indicator_size" format="dimension" />
        <attr name="wheel_curtain" format="boolean" />
        <attr name="wheel_curtain_color" format="color" />
        <attr name="wheel_atmospheric" format="boolean" />
        <attr name="wheel_curved" format="boolean" />
        <attr name="wheel_item_align" format="enum">
            <enum name="center" value="0" />
            <enum name="left" value="1" />
            <enum name="right" value="2" />
        </attr>
        <attr name="wheel_font_path" format="string" />
    </declare-styleable>

    <!--底部弹出列表 dialog 中文字的 text appearance-->
    <attr name="libui_bottom_sheet_list_item_text_appearance" format="reference" />
    <!--底部弹出列表 dialog 中 item 样式。目前仅支持 background 设置-->
    <attr name="libui_bottom_sheet_list_item_style" format="reference" />
    <!--底部弹出列表 dialog 中 cancel 样式-->
    <attr name="libui_bottom_sheet_list_cancel_style" format="reference" />

    <!--################################# custom dialog 开始 ###########################################-->
    <!--用于设置 dialog 整体样式，如背景、padding 等。-->
    <attr name="libui_dialog_style" format="reference" />
    <!--dialog 的整体样式-->
    <declare-styleable name="DialogWrapperStyleDef">
        <attr name="android:background" />
        <attr name="android:layout_width" />
        <attr name="android:paddingBottom" />
        <attr name="android:paddingLeft" />
        <attr name="android:paddingTop" />
        <attr name="android:paddingRight" />
        <attr name="android:maxWidth" />
        <!--设置最大宽度占屏幕的百分比，从0 -1。在指定有 maxWidth 时，该属性无效 -->
        <attr name="maxWidthFraction" format="float" />
        <!--设置宽度占屏幕的百分比，从0 -1。在指定有 layout_width 时，该属性无效 -->
        <attr name="widthFraction" format="float" />
    </declare-styleable>

    <!--定义 dialog action 的外层。可以配置在 theme中。-->
    <attr name="libui_dialog_action_container_style" format="reference" />
    <!--定义 dialog action 的外层属性 -->
    <declare-styleable name="DialogActionContainerDef">
        <attr name="dialog_action_container_justify_content" format="enum">
            <enum name="start" value="0" />
            <enum name="end" value="1" />
            <enum name="stretch" value="2" />
            <enum name="custom" value="3" />
        </attr>
        <attr name="dialog_action_container_custom_space_index" format="integer" />
        <attr name="dialog_action_height" format="dimension" />
        <attr name="dialog_action_space" format="dimension" />
    </declare-styleable>

    <!--定义 dialog action 的属性。可以配置在 theme中。-->
    <attr name="libui_dialog_action_style" format="reference" />
    <!--dialog action 可以定义的属性-->
    <declare-styleable name="DialogActionStyleDef">
        <attr name="android:textColor" />
        <attr name="android:textSize" />
        <attr name="android:background" />
        <attr name="android:gravity" />
        <attr name="dialog_action_button_padding_horizontal" format="dimension" />
        <attr name="dialog_action_button_padding_vertical" format="dimension" />
        <attr name="dialog_positive_action_text_color" format="color|reference" />
        <attr name="dialog_negative_action_text_color" format="color|reference" />
    </declare-styleable>

    <!--定义 dialog title 的属性。可以配置在 theme中。在解析该属性时，会解析 DialogTextStyle 中定义的属性值 -->
    <attr name="libui_dialog_title_style" format="reference" />

    <!--使用 MessageDialogBuilder 时，设置 message 的样式。在解析该属性时，会解析 DialogTextStyle 中定义的属性值-->
    <attr name="libui_dialog_message_content_style" format="reference" />

    <!--使用 MenuDialogBuilder 时，content 的整体属性。在解析该属性时，会解析 DialogMenuContainerStyleDef 中定义的属性-->
    <attr name="libui_dialog_menu_container_style" format="reference" />
    <!--使用 MenuDialogBuilder 时，item 的属性。在解析该属性时，会解析 DialogTextStyle 中定义的属性-->
    <attr name="libui_dialog_menu_item_style" format="reference" />
    <!--MenuDialogBuilder 中 content 外层属性-->
    <declare-styleable name="DialogMenuContainerStyleDef">
        <attr name="android:paddingTop" />
        <attr name="android:paddingBottom" />
        <attr name="dialog_menu_container_single_padding_vertical" format="dimension" />
        <attr name="dialog_menu_container_padding_top_when_title_exist" format="dimension" />
        <attr name="dialog_menu_container_padding_bottom_when_action_exist" format="dimension" />
        <attr name="dialog_menu_item_height" format="dimension" />
    </declare-styleable>


    <!--dialog 文字的样式 中可支持的属性-->
    <declare-styleable name="DialogTextStyle">
        <attr name="android:textColor" />
        <attr name="android:textSize" />
        <attr name="android:background" />
        <attr name="android:gravity" />
        <attr name="android:paddingTop" />
        <attr name="android:paddingLeft" />
        <attr name="android:paddingRight" />
        <attr name="android:paddingBottom" />
        <attr name="android:singleLine" />
        <attr name="android:ellipsize" />
        <attr name="android:maxLines" />
        <attr name="android:lineSpacingExtra" />
        <attr name="android:drawablePadding" />
        <attr name="android:textColorHint" />
    </declare-styleable>
    <!--################################# custom dialog 结束 ###########################################-->


    <!--################################# loading 开始 ###########################################-->
    <declare-styleable name="LoadingView">
        <attr name="loading_view_size" format="dimension" />
        <attr name="android:color" />
    </declare-styleable>
    <attr name="LoadingViewStyle" format="reference" />
    <!--################################# loading 结束 ###########################################-->

    <!--################################# TagFlowLayout 开始 ###########################################-->
    <declare-styleable name="TagFlowLayout">
        <attr name="titleText" format="reference|string" />
        <attr name="textTitleColor" format="reference|color" />
        <attr name="titleTextSize" format="dimension" />

        <attr name="backGroundColor" format="reference|color" />

        <attr name="tagsTextSize" format="dimension" />
        <attr name="tagsTextColor" format="reference|color" />
        <attr name="tagsBackgroundColor" format="reference|color" />
        <attr name="tagsBackgroundPressedColor" format="reference|color" />
        <attr name="tagsBackgroundCorners" format="dimension" />
        <attr name="tagsHorizontalSpace" format="dimension" />
        <attr name="tagsVerticalSpace" format="dimension" />
        <attr name="randomBackground" format="boolean" />

        <attr name="foldHint" format="reference|string" />
        <attr name="expandHint" format="reference|string" />
        <attr name="hintTextColor" format="reference|color" />
        <attr name="hintTextSize" format="dimension" />
        <attr name="dividerColor" format="reference|color" />

        <attr name="indicateImage" format="reference" />
        <attr name="minVisibleHeight" format="dimension" />
        <attr name="maxVisibleHeight" format="dimension" />

        <attr name="animationDuration" format="integer" />
        <!--################################# TagFlowLayout 结束 ###########################################-->
    </declare-styleable>

    <declare-styleable name="CircleProgressBar">
        <attr name="min" format="integer" />
        <attr name="max" format="integer" />
        <attr name="progress" format="integer" />
        <attr name="secondaryColor" format="color" />
        <attr name="progressbarColor" format="color" />
        <attr name="progressBarThickness" format="dimension" />
    </declare-styleable>

    <declare-styleable name="iconfont">
        <attr name="icon_font_path" format="string" />
    </declare-styleable>

    <declare-styleable name="OffsetLayout">
        <attr name="offset" format="dimension" />
        <attr name="reverseDrawingOrder" format="boolean" />
    </declare-styleable>

    <declare-styleable name="GradientButton">
        <attr name="button_text" format="string" />
        <attr name="button_size" format="dimension" />
        <attr name="button_text_color" format="color" />
        <attr name="button_stroke_color" format="color" />
        <attr name="button_start_color" format="color" />
        <attr name="button_end_color" format="color" />
        <attr name="button_press_start_color" format="color" />
        <attr name="button_press_end_color" format="color" />
        <attr name="button_gradient_orientation">
            <enum name="TOP_BOTTOM" value="0" />
            <enum name="TR_BL" value="1" />
            <enum name="RIGHT_LEFT" value="2" />
            <enum name="BR_TL" value="3" />
            <enum name="BOTTOM_TOP" value="4" />
            <enum name="BL_TR" value="5" />
            <enum name="LEFT_RIGHT" value="6" />
            <enum name="TL_BR" value="7" />
        </attr>
        <attr name="button_radius" format="dimension" />
        <attr name="button_shadow_distance" format="dimension" />
        <attr name="button_shadow_angle" format="integer" />
        <attr name="button_shadow_color" format="color" />
        <attr name="button_is_shadowed" format="boolean" />
        <attr name="button_shadow_radius" format="dimension" />
        <attr name="button_stroke_width" format="dimension" />
    </declare-styleable>

    <declare-styleable name="dotView">
        <attr name="dot_special" format="boolean" />
        <attr name="dot_default" format="boolean" />
    </declare-styleable>

    <declare-styleable name="circleLayoutStyle">
        <attr name="radium" format="dimension" />
        <attr name="changeCorner" format="float" />
    </declare-styleable>

    <declare-styleable name="CircleLayout">
        <!-- (Optional) The ID of the child to show at the center of the layout. -->
        <attr name="cl_centerView" format="reference" />

        <!-- (Optional) A fixed angle between views. -->
        <attr name="cl_angle" format="float" />

        <!-- The initial angle of the layout pass. A value of 0 will start laying out from the horizontal axis. Defaults to 0. -->
        <attr name="cl_angleOffset" format="float" />

        <!-- The radius of the circle. Use a dimension, {@code fitsSmallestChild}, or {@code fitsLargestChild}. Defaults to {@code fitsLargestChild}. -->
        <attr name="cl_radius" format="dimension">
            <!-- Will adjust the radius to make the smallest child fit in the layout and larger children will bleed outside the radius. -->
            <enum name="fitsSmallestChild" value="-1" />

            <!-- Will adjust the radius to make the largest child fit in the layout. -->
            <enum name="fitsLargestChild" value="-2" />
        </attr>

        <!-- The layout direction. Defaults to {@code counterClockwise}. -->
        <attr name="cl_direction">
            <enum name="clockwise" value="-1" />
            <enum name="counterClockwise" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="VoiceWaveView">
        <attr name="waveLineSpace" format="dimension"/>
        <attr name="waveLineWidth" format="dimension"/>
        <attr name="duration" format="integer"/>

        <attr name="waveMode" format="enum">
            <enum name="up_down" value="0"/>
            <enum name="left_right" value="1"/>
            <enum name="right_left" value="2"/>
            <enum name="right_left_no_repeat" value="3"/>
        </attr>

        <attr name="lineType" format="enum">
            <enum name="bar_chart" value="0"/>
            <enum name="line_graph" value="1"/>
        </attr>

        <attr name="waveLineColor" format="color"/>
        <attr name="android:gravity"/>
    </declare-styleable>

    <declare-styleable name="CustomRadiusLayout">
        <!-- 圆角类型：
             none   → 无圆角
             all    → 全部圆角
             top    → 仅顶部圆角
             bottom → 仅底部圆角
             left   → 仅左边圆角（左上、左下）
             right  → 仅右边圆角（右上、右下）
        -->
        <attr name="cornerType" format="enum">
            <enum name="none" value="-1"/>
            <enum name="all" value="0"/>
            <enum name="top" value="1"/>
            <enum name="bottom" value="2"/>
            <enum name="left" value="3"/>
            <enum name="right" value="4"/>
        </attr>
    </declare-styleable>

    <declare-styleable name="JoyCheckBox">
        <attr name="text" format="string" />
        <attr name="checked" format="boolean" />
        <attr name="text_color" format="color" />
        <attr name="selectBackground" format="reference" />
        <attr name="unselectBackground" format="reference" />
    </declare-styleable>

    <declare-styleable name="JoyListEmptyView">
        <!-- Image resource -->
        <attr name="emptyImage" format="reference" />
        <!-- Image dimensions -->
        <attr name="emptyImageWidth" format="dimension" />
        <attr name="emptyImageHeight" format="dimension" />

        <!-- Text content -->
        <attr name="emptyText" format="string" />
        <!-- Text appearance -->
        <attr name="emptyTextColor" format="color" />
        <attr name="emptyTextSize" format="dimension" />
    </declare-styleable>
</resources>