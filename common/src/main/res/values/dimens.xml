<resources>

    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="libui_0dp">0dp</dimen>

    <!-- TitleBar -->
    <dimen name="libui_titleBar_height">48dp</dimen>
    <dimen name="libui_titleBar_back_btn_width">34dp</dimen>

    <!-- 字体 -->
    <dimen name="libui_font_60pt_20sp">20sp</dimen>
    <dimen name="libui_font_55pt_18sp">18sp</dimen>
    <dimen name="libui_font_40pt_14sp">14sp</dimen>

    <!-- icon_font 大小统一，同时和iOS命名方式统一 -->
    <dimen name="JMEIcon_08">8dp</dimen>
    <dimen name="JMEIcon_10">10dp</dimen>
    <dimen name="JMEIcon_12">12dp</dimen>
    <dimen name="JMEIcon_14">14dp</dimen>
    <dimen name="JMEIcon_16">16dp</dimen>
    <dimen name="JMEIcon_18">18dp</dimen>
    <dimen name="JMEIcon_20">20dp</dimen>
    <dimen name="JMEIcon_22">22dp</dimen>
    <dimen name="JMEIcon_24">24dp</dimen>
    <dimen name="JMEIcon_26">26dp</dimen>
    <dimen name="JMEIcon_28">28dp</dimen>

    <!-- LoadingDialog -->
    <dimen name="libui_dialog_content_height">85dp</dimen>
    <dimen name="libui_Loading_dialog_height">100dp</dimen>
    <dimen name="libui_Loading_dialog_width">135dp</dimen>
    <dimen name="libui_Loading_dialog_padding">20dp</dimen>
    <dimen name="libui_Loading_dialog_progressbar_sides">40dp</dimen>
    <dimen name="libui_Loading_dialog_margin">5dp</dimen>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="me_activity_horizontal_margin">16dp</dimen>
    <dimen name="me_activity_vertical_margin">16dp</dimen>

    <!-- 字体大小 -->
    <dimen name="me_text_size_10">10sp</dimen>
    <dimen name="me_text_size_small">12sp</dimen>
    <dimen name="me_text_size_small_plus">12.5sp</dimen>
    <dimen name="me_text_size_14">14sp</dimen>
    <dimen name="me_text_size_middle">16sp</dimen>
    <dimen name="me_text_size_larger">18sp</dimen>
    <dimen name="me_text_size_xlarger">20sp</dimen>
    <dimen name="me_text_size_xxlarger">24sp</dimen>
    <dimen name="me_text_size_xxxlarger">26sp</dimen>
    <!-- 导航栏标题字体大小 -->
    <dimen name="me_text_size_navigation_title">18sp</dimen>
    <!-- 底部导航条高度 -->
    <dimen name="me_bottom_bar_height">50dip</dimen>
    <!-- 手势锁圈的大小 -->
    <dimen name="me_gesture_lock_circle">60dip</dimen>

    <!-- 自定义标题栏高度，与actionbar高度保持一致 -->
    <dimen name="me_navigation_height">48dip</dimen>
    <!-- 自定义actionbar标题大小 -->
    <dimen name="me_navigation_text_size">18sp</dimen>
    <!-- 分割线高度 -->
    <dimen name="me_divide_height_min">1px</dimen>
    <dimen name="me_divide_height_middle">1dip</dimen>
    <dimen name="me_circle_width">5px</dimen>
    <dimen name="me_warn_button_height">48dip</dimen>
    <dimen name="me_edit_height">46dip</dimen>

    <!-- 列表项左侧图片宽高 -->
    <dimen name="me_icon_left_width">40dip</dimen>
    <!-- 按钮高度 -->
    <dimen name="me_btn_height">36dip</dimen>
    <!-- 按钮最小宽 -->
    <dimen name="me_btn_min_width">88dip</dimen>

    <!-- 首页头像区域高度 -->
    <dimen name="me_home_user_icon_height">108dip</dimen>

    <dimen name="me_home_header_height_with_action_bar">178dip</dimen>

    <dimen name="me_home_header_height_with_toolbar">178dip</dimen>

    <!-- 首页 icon 的宽 -->
    <dimen name="me_home_icon_item_fun_width">40dp</dimen>

    <!-- 首页 item 的宽 -->
    <dimen name="me_home_item_fun_height">100dip</dimen>

    <!-- 首页 item 右上角new hot的小角标 -->
    <dimen name="me_home_item_fun_tag_size">32dip</dimen>

    <!-- 首页 item 的宽 -->
    <dimen name="me_home_item_fun_width">118dip</dimen>

    <!-- 年会下拉 拉手高度 -->
    <dimen name="me_festive_height">48dip</dimen>

    <!-- 圆角矩形圆角大小 -->
    <dimen name="me_round_rect_corner_size">2dip</dimen>
    <!-- webview 顶部 progress 进度条高度 -->
    <dimen name="me_progress_line_height">3dip</dimen>

    <!-- listview item key value 单行 条目高度 -->
    <dimen name="me_item_single_height">48dip</dimen>
    <!-- table 单元格内容padding的值 -->
    <dimen name="me_table_cell_padding">2dip</dimen>

    <!-- 假期申请 上传图片 的宽 -->
    <dimen name="me_attachment_pic_width">96dip</dimen>

    <!-- 假期申请 上传图片 的宽 -->
    <dimen name="me_attachment_pic_width_sml">88dip</dimen>

    <!-- 打卡半圆形的 progressbar的半径  -->
    <dimen name="me_arcProgressBarDiameters">88dip</dimen>

    <dimen name="abc_action_bar_default_height">55dip</dimen>

    <dimen name="tool_bar_default_height">55dip</dimen>

    <dimen name="tool_bar_padding_top">0dip</dimen>

    <!-- IOS Alert Dialog  -->
    <dimen name="me_alert_dialog_msg_padding">20dip</dimen>

    <!-- listItem 条目高度 -->
    <dimen name="me_list_item_height_for_dialog">48dip</dimen>

    <!-- 会议室 展开框 GridView item的高度 -->
    <dimen name="me_list_item_height_for_expand">36dip</dimen>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="jdreact_progressbar_size">34dp</dimen>

    <dimen name="setting_item_padding_vertical">16dp</dimen>
    <dimen name="setting_item_padding_horizontal">12dp</dimen>
    <dimen name="setting_name_text_size">16sp</dimen>
    <dimen name="setting_tips_text_size">16sp</dimen>

    <dimen name="app_icon_size">40dp</dimen>

    <dimen name="approval_text_background_vertical_padding">2dp</dimen>

    <dimen name="me_action_bar_title_text_size">19dp</dimen>
    <dimen name="me_action_bar_menu_text_size">16sp</dimen>

    <dimen name="me_evaluation_float_view_height">72dp</dimen>
    <dimen name="me_evaluation_float_view_width">72dp</dimen>

    <dimen name="me_home_tab_bar_height">60dip</dimen>
    <dimen name="me_home_tab_bar_item_size">50dip</dimen>
    <dimen name="me_home_behavior_peekHeight">70dip</dimen>
    <dimen name="me_home_behavior_peekHeight2">50dip</dimen>

    <dimen name="joywork_tab_textsize">14dp</dimen>
    <dimen name="floating_top_margin">95dp</dimen>

</resources>