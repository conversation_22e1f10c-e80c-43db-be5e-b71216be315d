<resources xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">


    <style name="AppCompatAlertDialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">?attr/me_theme_major_color</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="me_btn_style" parent="Base.Widget.AppCompat.Button">
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- actionbar 样式 -->
    <style name="MeActionBarStyle" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="android:background">?attr/me_actionbar_bg</item>
        <!-- Support library compatibility -->
        <item name="background">?attr/me_actionbar_bg</item>
    </style>

    <style name="MeWhiteActionBarStyle" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">@drawable/jdme_bg_actionbar_white</item>
        <item name="elevation">0dp</item>
    </style>

    <style name="MeToolbarStyle" parent="ThemeOverlay.AppCompat.ActionBar">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/abc_action_bar_default_height</item>
        <item name="android:background">@drawable/jdme_bg_actionbar_white</item>
    </style>

    <style name="MeWorkbenchToolbarStyle" parent="ThemeOverlay.AppCompat.ActionBar">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">44dip</item>
        <item name="android:background">@color/white</item>
    </style>


    <style name="ActionMenuTextStyle" parent="TextAppearance.AppCompat.Medium">
        <item name="android:textSize">16sp</item>
    </style>


    <!-- 界面样式： 界面切换动画 -->
    <style name="me_activityAnimation" parent="@android:style/Animation.Activity">
        <!--
        activity 切换使用android原生提供的
        <item name="android:activityOpenEnterAnimation">@anim/activity_open_enter</item>
        <item name="android:activityOpenExitAnimation">@anim/activity_open_exit</item>
        <item name="android:activityCloseEnterAnimation">@anim/activity_close_enter</item>
        <item name="android:activityCloseExitAnimation">@anim/jdme_activity_close_exit</item>
        -->
        <!-- 任务task切换，使用本地系统的，测试发现，微信改了 -->
        <!-- fragment 切换 -->
    </style>

    <!-- 自定义Dialog -->
    <style name="me_custom_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">false</item>
        <!--半透明-->
        <item name="android:windowNoTitle">true</item>
        <!--无标题-->
        <item name="android:windowBackground">@color/transparent</item>
        <!--背景透明-->
        <item name="android:backgroundDimEnabled">false</item>
        <!--模糊-->
    </style>

    <!-- 修改手机号Dialog样式 -->
    <style name="me_custom_dialog1" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item>
        <!--半透明-->
        <item name="android:windowNoTitle">true</item>
        <!--无标题-->
        <item name="android:windowBackground">@drawable/libui_custom_dialog_bg</item>
        <!--背景透明-->
        <item name="android:backgroundDimEnabled">true</item>
        <!--模糊-->
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorButtonNormal">@color/colorPrimary</item>
    </style>

    <style name="me_loading_dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>


    <style name="me_dateStyle">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">13sp</item>
    </style>


    <!-- 自定义仿IOS的AlertDialog的样式 -->
    <style name="me_AlertDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>


    <!-- 自定义仿IOS的Toast的样式 -->
    <style name="me_IosToastStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="me_ShareDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="me_DidiYellowStar" parent="@android:style/Widget.RatingBar">
        <item name="android:minHeight">38dp</item>
        <item name="android:maxHeight">38dp</item>
    </style>

    <style name="me_DidiLittleStar" parent="@android:style/Widget.RatingBar">
        <item name="android:minHeight">15dp</item>
        <item name="android:maxHeight">15dp</item>
    </style>

    <style name="JDMEMineMain">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">48dip</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:paddingLeft">15dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:textColor">@color/jdme_color_first</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="JDMEMineArrowContainer">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
    </style>

    <style name="JDMEMineArrow">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_marginRight">25dp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:src">@drawable/jdme_icon_right_arrow</item>
    </style>

    <!-- 半透明Activity -->
    <style name="JDME_ActivityTransparent" parent="jdme_ParentTheme">
        <item name="android:windowIsTranslucent">true</item>
        <!-- <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item> -->
    </style>

    <style name="JDME_StyleProgressBarMini" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:maxHeight">50dip</item>
        <item name="android:minHeight">15dip</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">
            @android:drawable/progress_indeterminate_horizontal
        </item>
        <item name="android:progressDrawable">@drawable/jdme_shape_progressbar_mini</item>
    </style>

    <style name="me_TabLayoutTextStyle">
        <item name="android:textSize">@dimen/me_text_size_middle</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="me_joywork_TabLayoutTextStyle">
        <item name="android:textSize">@dimen/joywork_tab_textsize</item>
    </style>

    <style name="ME_UnbindDetailInfo_Left">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/grey_text_color</item>
    </style>

    <style name="ME_UnbindDetailInfo_Right">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">right|center_vertical</item>
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/grey_text_color</item>
    </style>

    <style name="Transparent">
        <item name="android:windowBackground">@color/transparent</item>
    </style>

    <style name="jdreact_top_rl">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">49dp</item>
        <item name="android:background">@drawable/jdreact_bg_top</item>
    </style>

    <style name="jdreact_top_iv_back">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentLeft">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:paddingRight">20dp</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingTop">15dp</item>
        <item name="android:paddingBottom">15dp</item>
        <item name="android:src">@drawable/common_title_back_selector</item>
    </style>


    <style name="JDME_AppMarketTabText" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="jdme_TimePicker_style" parent="Theme.AppCompat.Light.Dialog">
        <item name="colorAccent">#FF0000</item>
        <item name="android:textColorPrimary">@color/red_warn</item>
    </style>


    <!-- 普通按钮 -->
    <style name="my_button" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minHeight">@dimen/me_btn_height</item>
        <item name="android:minWidth">@dimen/me_btn_min_width</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
    </style>

    <!-- 红色普通按钮 -->
    <style name="my_button_default" parent="@style/my_button">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:background">@drawable/jdme_selector_button_default</item>
    </style>

    <!--Theme.AppCompat.Dialog-->
    <style name="BottomDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@style/BottomDialogAnimation</item>
    </style>
    <!--    JoyWork 专用 dialog style-->
    <style name="JoyWorkDialogStyle" parent="me_ActionSheetDialogStyle">
        <item name="android:backgroundDimAmount">0.25</item>
        <item name="windowNoTitle">true</item>
        <!-- 显示软键盘 -->
        <item name="android:windowSoftInputMode">stateAlwaysVisible</item>
    </style>

    <style name="JoyWorkSubDialogStyle" parent="JoyWorkDialogStyle">
        <item name="android:windowAnimationStyle">@style/me_ActionSheetSubDialogAnimation</item>
    </style>

    <!--    joywork 专用文本输入框设置-->
    <style name="JoyWorkEditTextStyle">
        <item name="android:colorControlActivated">#E4393C</item>
        <item name="android:colorControlNormal">#E4393C</item>
    </style>

    <style name="JoyWorkFilterDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:backgroundDimAmount">0</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="JoyWorkAlertDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:backgroundDimAmount">0.25</item>
    </style>

    <style name="JoyWorkEditDialogStyle" parent="JoyWorkAlertDialogStyle">
        <item name="android:windowSoftInputMode">stateAlwaysVisible</item>
    </style>

    <style name="AutoSoftKeyboardDialogStyle" parent="BottomDialogStyle">
        <item name="android:windowSoftInputMode">stateAlwaysVisible</item><!--显示软件盘-->
    </style>

    <style name="BottomDialogAnimation" parent="Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/jdme_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/jdme_bottom_sheet_slide_out</item>
    </style>

    <!-- ActionSheet进出动画 -->
    <style name="me_ActionSheetDialogAnimation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/jdme_actionsheet_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/jdme_actionsheet_dialog_out</item>
    </style>
    <!-- 二级 dialog 页面进出动画-->
    <style name="me_ActionSheetSubDialogAnimation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/jdme_action_sheet_sub_dialog_left_in</item>
        <item name="android:windowExitAnimation">@anim/jdme_action_sheet_sub_dialog_left_out</item>
    </style>

    <!-- 自定义仿IOS的ActionSheet底部Dialog的样式 ，有模糊效果 -->
    <style name="me_ActionSheetDialogStyle" parent="@android:style/Theme.Dialog">

        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- 浮于Activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- Dialog以外的区域模糊效果 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- Dialog进入及退出动画 -->
        <item name="android:windowAnimationStyle">@style/me_ActionSheetDialogAnimation</item>
    </style>

    <style name="me_ActionSheetDialogStyle_Opaque_Item" parent="@android:style/Theme.Dialog">

        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- 浮于Activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- Dialog以外的区域模糊效果 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">false</item>
        <!-- Dialog进入及退出动画 -->
        <item name="android:windowAnimationStyle">@style/me_ActionSheetDialogAnimation</item>
    </style>

    <style name="MEWhiteTransparentTheme" parent="MEWhiteTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="ActivityThemeTransparent" parent="jdme_AppTheme_Defalut">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <!-- 添加这个主题先让状态栏透明，然后再去activity里面加两行代码，让状态栏白底黑字-->
    <style name="ActivityThemeTransparentTransparent" parent="jdme_AppTheme_Defalut">
        <item name="colorPrimary">@color/transparent</item>
        <item name="colorPrimaryDark">@color/transparent</item>
        <item name="colorAccent">@color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="me_MyDialogStyleBottom" parent="android:Theme.Dialog">
        <item name="android:windowAnimationStyle">@style/AnimBottom</item>
        <item name="android:windowFrame">@null</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 模糊 -->
    </style>

    <style name="me_MyDialogStyle" parent="me_MyDialogStyleBottom">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFrame">@null</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 模糊 -->
    </style>

    <style name="me_BottomDialogAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/jdme_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/jdme_dialog_exit</item>
    </style>

    <!--从底部弹出的activity-->
    <style name="AnimBottomShare" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/jme_activity_push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/jme_activity_push_bottom_out</item>
    </style>

    <style name="MyDialogStyleBottom" parent="android:Theme">
        <item name="android:windowAnimationStyle">@style/AnimBottomShare</item>
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 模糊 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!--全屏-->
        <item name="android:windowFullscreen">true</item>

    </style>

    <style name="me_TravelRadiobox">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/jdme_bg_travel_radiobox_selector</item>
        <item name="android:button">@null</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/me_text_size_larger</item>
        <item name="android:textColor">@color/travel_radiobox_text_selector</item>
    </style>

    <style name="jdme_Translucent" parent="jdme_AppTheme_Defalut">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- 相册样式 -->
    <style name="MeAlum" parent="Mae_Album_Base_theme">
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="mae_album_topBar_text_color">@android:color/black</item>
        <item name="mae_album_unCheckedColorRes">@android:color/white</item>
        <item name="mae_album_checkedColorRes">@color/colorPrimary</item>
    </style>

    <!--网盘样式-->
    <style name="NetDiskStyle" parent="mae_net_disk_base_theme">
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="mae_net_disk_bar_text_color">@android:color/black</item>
        <item name="mae_net_disk_major_color">#E4393C</item>
    </style>

    <style name="MainActivityTheme" parent="MEWhiteTheme">
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>

    <style name="JDMEAppThemeNoActionBar" parent="MEWhiteTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="TimePickerStyle">
        <!--未选中数据项颜色-->
        <item name="wheel_item_text_color">@color/comm_text_secondary</item>
        <!--选中数据项颜色-->
        <item name="wheel_selected_item_text_color">@color/comm_text_title</item>
        <!--设置数据项文字大小-->
        <item name="wheel_item_text_size">18sp</item>
        <!--是否显示空气效果-->
        <item name="wheel_atmospheric">true</item>
        <!--是否显示标线-->
        <item name="wheel_indicator">true</item>
        <!--标线的颜色-->
        <item name="wheel_indicator_color">@color/comm_divider</item>
        <!--标线的大小-->
        <item name="wheel_indicator_size">1dp</item>
    </style>

    <style name="PrintOptionText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/comm_text_normal_xlarge</item>
        <item name="android:textColor">@color/comm_text_title</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
    </style>

    <!--透明无入场动画-->
    <style name="ActivityThemeTransparentNoAnimation" parent="ActivityThemeTransparent">
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="NoAnimTheme" parent="MEWhiteTheme">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="EvalDialogStyle" parent="BottomDialogStyle">
        <item name="android:windowContentOverlay">@null</item>
    </style>


    <!-- ############################# 白色默认主题  ############################# -->
    <style name="jdme_ParentTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <!-- listview recyclerView 分割线 -->
        <item name="android:listDivider">@drawable/jdme_list_divider</item>
        <item name="colorAccent">?attr/me_theme_major_color</item>
        <!-- 窗口背景色 -->
        <item name="android:windowBackground">@android:color/white</item>

        <!-- actionbar 样式 -->
        <item name="actionBarStyle">@style/MeActionBarStyle</item>
        <item name="android:actionBarStyle" tools:ignore="NewApi">@style/MeActionBarStyle</item>


        <!-- button样式修改，使用白色文字 -->
        <item name="android:buttonStyle">@style/me_btn_style</item>
        <item name="buttonStyle">@style/me_btn_style</item>

        <!-- alertDialog 样式 -->
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="alertDialogTheme">@style/AppCompatAlertDialogStyle</item>

        <item name="android:textAllCaps">false</item>
    </style>

    <!-- ############################# 默认主题 当前唯一使用的主题 ############################# -->
    <style name="jdme_AppTheme_Defalut" parent="jdme_ParentTheme">
        <item name="actionMenuTextAppearance">@style/ActionMenuTextStyle</item>

        <!-- 配置activity切换时跳转动画 -->
        <item name="android:windowAnimationStyle">@style/me_activityAnimation</item>
        <item name="android:windowContentTransitions">true</item>

        <!-- 主题：配置Theme属性 -->
        <item name="me_theme_major_color">@color/skin_color_default</item>
        <item name="me_icon_detail_arrow">@drawable/jdme_detail_arrow_default</item>
        <item name="me_btn_selector">@drawable/jdme_selector_button_default</item>
        <item name="me_btn_selector_disable_gray">@drawable/jdme_selector_button_disable_gray</item>
        <item name="me_btn_borderness_selector">@drawable/jdme_selector_button_borderness_default
        </item>
        <item name="me_borderness_btn_color">@color/borderness_btn_color_default</item>
        <item name="me_windowBackground">@drawable/jdme_bg_header_default</item>
        <item name="me_actionbar_bg">@drawable/jdme_bg_title_default</item>
        <item name="me_check_box_selector">@drawable/jdme_checkbox_default_selector</item>
        <item name="me_feedback_tab_selector">@color/tab_text_color_default_selector</item>
        <item name="me_btn_black_border_selector">@drawable/jdme_selector_button_borderness_black
        </item>
    </style>

    <style name="MEWhiteTheme" parent="jdme_AppTheme_Defalut">
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="actionBarStyle">@style/MeWhiteActionBarStyle</item>
    </style>

    <!-- 加载中 -->
    <style name="dialogTheme" parent="android:Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <!-- 除去title -->
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowBackground">@color/comm_transparent</item>
        <!-- 除去背景色 -->
    </style>


    <!-- 自定义对话框 -->
    <style name="CustomDialogViewStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/libui_custom_dialog_view_bg</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!-- 自定义全透明对话框 -->
    <style name="commDialogStyle_no_bg" parent="Theme.AppCompat.Dialog">
        <!-- 上面说过，只要是Dialog，这两个属性必须设置 -->
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>

        <!--设置透明状态栏，适用于SDK19（4.4）及以上版本-->
        <item name="android:windowTranslucentStatus">true</item>
        <!-- 如果你不需要自定义状态栏颜色，下面两个可不要 -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <!-- 对话框是否有遮盖 -->
        <item name="android:windowAnimationStyle">@null</item>
        <!--是否有灰色背景-->
        <item name="android:backgroundDimEnabled">false</item>
        <!-- 透明导航栏 -->
        <item name="android:windowTranslucentNavigation">true</item>
    </style>

    <style name="BtnStyle">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/libui_selector_bg_btn_corner</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:layout_marginLeft">44dp</item>
        <item name="android:layout_marginRight">44dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>


    <style name="BaseDialog" parent="android:Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="BottomSheetDialogStyle" parent="BaseDialog">
        <!-- 为了避免在有 NavigationBar 的手机上 Dialog 从 NavigationBar 底部上来。去掉 Dialog 的动画，使用 View 的动画。-->
        <item name="android:windowAnimationStyle">@null
        </item>
        <item name="android:layout_width">match_parent</item>
    </style>


    <style name="CustomDialog" parent="BaseDialog">
        <!--在使用时可手动修改-->
        <item name="android:backgroundDimAmount">0.6</item>
        <!-- 在中间弹框、背后有遮罩的效果 -->
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="MaskTheme" parent="MEWhiteTheme">
        <item name="android:windowAnimationStyle">@style/MaskAnimation</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="MaskAnimation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>

    <declare-styleable name="HeightLimitedScrollView">
        <attr name="maxHeight" format="dimension" />
    </declare-styleable>

    <style name="AiAppBottomSheet" parent="AppBottomSheet">
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
        <item name="backgroundTint">@color/transparent</item>
        <item name="android:background">@color/transparent</item>
        <!--        <item name="android:windowFullscreen">true</item>-->
        <!--        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>-->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.7</item>
        <item name="android:windowTranslucentStatus">true</item>

        <item name="android:windowIsFloating">false</item>
        <!--        <item name="android:windowFrame">@null</item>-->
    </style>

    <style name="FileViewerBottomSheet" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/bottom_sheet_style</item>
    </style>

    <style name="bottom_sheet_style" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="AppBottomSheet" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppBottomSheetStyle</item>
    </style>

    <style name="AppBottomSheetStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="backgroundTint">@color/transparent</item>
        <item name="android:layout_width">match_parent</item>
    </style>

    <style name="BottomActionDialogStyle" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomActionDialogSheetStyle</item>
    </style>

    <style name="BottomActionDialogSheetStyle" parent="AppBottomSheetStyle">
        <item name="android:maxWidth">@null</item>
    </style>

    <style name="FloatingActivityStyle" parent="jdme_AppTheme_Defalut">
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <!--是否启用标题栏-->
        <item name="android:background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>

    <style name="MeDialog2" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>

    <style name="AppTheme.Slide" parent="Theme.AppCompat.NoActionBar">
        <!--Required-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/AppTheme.Slide.Animation</item>
    </style>

    <style name="AppTheme.Slide.Animation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/jdme_anim_slide_in</item>
        <item name="android:activityOpenExitAnimation">@anim/jdme_anim_slide_out</item>
        <item name="android:activityCloseEnterAnimation">@anim/jdme_anim_slide_in</item>
        <item name="android:activityCloseExitAnimation">@anim/jdme_anim_slide_out</item>
    </style>

    <style name="CustomBottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="CustomBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/jdme_bg_bottomsheet_dialog_corner</item>
    </style>

    <style name="AppTheme.TransparentActivity" parent="Theme.AppCompat.NoActionBar">
        <!--Required-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="GuideDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!--  个人中心-大事记图片四周圆角  -->
    <style name="MeMemorabiliaCorner">
        <item name="cornerFamily">rounded</item>
        <item name="radius">8dp</item>
    </style>

</resources>