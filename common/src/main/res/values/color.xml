<?xml version="1.0" encoding="utf-8"?>
<resources>

    <color name="ddtl_colorWhiteGray">#e5e5e5</color>

    <color name="jdme_color_second">#5d5c5c</color>
    <color name="jdme_color_first">#2d2d2d</color>
    <color name="jdme_color_third">#838383</color>
    <color name="jdme_color_forth">#b9b9b9</color>
    <color name="jdme_color_bottom_bar">#929292</color>
    <color name="jdme_color_divider">#eeeeee</color>
    <color name="jdme_color_blue">#458bff</color>

    <color name="white">#FFFFFF</color>
    <color name="white_light_gray_f8f8f8">#f8f8f8</color>
    <color name="white_light_gray_f8f8f9">#f8f8f9</color>

    <color name="white_transparent_95">#F2FFFFFF</color>
    <color name="white_transparent_80">#C8FFFFFF</color>
    <color name="white_transparent_60">#99FFFFFF</color>
    <color name="white_transparent_50">#80FFFFFF</color>
    <color name="white_transparent_40">#66FFFFFF</color>
    <color name="white_transparent_20">#51FFFFFF</color>
    <color name="edit_cursor">#F48260</color>

    <color name="login_background">#fafafa</color>

    <color name="bottomTaphost_backgroud">@color/white</color>

    <!-- ****************** 黑色系列（颜色由深入浅）	start ****************** -->

    <color name="black">#000000</color>
    <!-- 非常重要的文字信息，如：主标题、类目名称等 -->
    <color name="black_main_title">#333333</color>
    <!-- 大部分文件描述，如：商品详情 -->
    <color name="black_main_summary">#666666</color>
    <!-- 辅助，次要的文字信息，如：副标题, 层次高的分割线 -->
    <color name="black_assist">#848484</color>
    <color name="black_first">#2E2D2D</color>
    <!-- edittext默认显示的提示文字颜色 -->
    <color name="black_edit_hit">#bcbcbc</color>
    <color name="black_edit">#666666</color>
    <color name="black_light">#EBEBF1</color>
    <!-- #e5e5e5 #EAEAEA #FFC2C2C2 #EBEBF1 -->
    <color name="black_divider">#eeeeee</color>
    <!-- %20透明度 1分是2.55 -->
    <color name="black_transparent_20">#51000000</color>
    <color name="black_transparent_30">#4C000000</color>
    <color name="black_transparent_12">#1F000000</color>
    <color name="black_transparent_26">#42000000</color>
    <color name="black_transparent_87">#DE000000</color>
    <color name="black_252525">#252525</color>
    <color name="black_transparent_08">#0F000000</color>


    <!-- ****************** 黑色系列（颜色由深入浅）	start ****************** -->

    <!-- 灰色系列 上面已有部分灰色 -->
    <color name="gray_c7c7c7">#c7c7c7</color>
    <color name="gray_999_10">#19999999</color>
    <color name="gray_fe">#fefefe</color>
    <color name="gray_ef">#efeff4</color>
    <!-- 灰色系列 上面已有部分灰色 -->


    <!-- ****************** 红色系列（颜色由深入浅）	start ****************** -->
    <color name="red">#FF0000</color>
    <color name="red_1">#f1617c</color>
    <!-- 需要强调和突出的文字、按钮，如：退出按钮背景 -->
    <color name="red_warn">#F0250F</color>
    <!-- ****************** 红色系列（颜色由深入浅）	end ****************** -->

    <color name="light_gray">#CCCCCC</color>
    <color name="set_bind">#3ABE00</color>
    <color name="press_set_bind">#BB3ABE00</color>
    <!-- GridView -->
    <color name="list_divider">#20000000</color>
    <color name="blue_light">#00EEEE</color>

    <!-- ****************** 蓝色系列	start ****************** -->
    <color name="blue">#0000ff</color>
    <color name="blue_1">#456bff</color>
    <color name="blue_2">#0284fe</color>
    <color name="me_text_link">#1869F5</color>
    <color name="blue_button_text">#1074FF</color>

    <!-- ****************** 蓝色系列	end ****************** -->


    <!-- ****************** 黄色系列	start ****************** -->
    <!-- 推送消息小圆点背景 -->
    <color name="yellow_push_msg_dot">#ff9c00</color>
    <color name="yellow_1">#f9ba58</color>
    <!-- ****************** 黄色系列	end ****************** -->

    <!-- ***************** 换肤主题颜色系列	start ******* -->
    <color name="skin_color_default">#E4393C</color>
    <color name="skin_color_night">#2C5694</color>
    <color name="skin_color_fresh">#1299E6</color>
    <color name="skin_color_colorful_purity">#FF72A1</color>
    <!-- ***************** 换肤主题颜色系列	end ******* -->

    <!-- 资源颜色 -->
    <color name="title_color">#FFFFFF</color>

    <color name="green">#228B22</color>
    <!-- zxing -->
    <color name="contents_text">#ff000000</color>
    <color name="encode_view">#ffffffff</color>
    <color name="possible_result_points">#c0ffbd21</color>
    <!-- Android standard ICS color -->
    <color name="result_minor_text">#ffc0c0c0</color>
    <color name="result_points">#c099cc00</color>
    <!-- Android standard ICS color -->
    <color name="result_text">#ffffffff</color>
    <color name="result_view">#b0000000</color>
    <color name="status_text">#ffffffff</color>
    <color name="viewfinder_laser">#ffcc0000</color>
    <!-- Android standard ICS color -->
    <color name="viewfinder_mask">#c9141414</color>
    <color name="capture_text_cover_bg">#99535353</color>
    <color name="seek_bar_text">#fff6f6f6</color>

    <color name="setting_devide_line">#f0f0f0</color>

    <color name="more_page_black_color">#666666</color>
    <color name="more_page_bg_color">#ededee</color>

    <color name="tab_text_color">#666666</color>
    <color name="tab_text_color_default">#e4393c</color>
    <color name="tab_text_color_star">#345897</color>
    <color name="tab_text_color_girl">#ff62a5</color>
    <color name="tab_text_color_boy">#169ce6</color>

    <color name="focus_circle_bg">#F24949</color>
    <color name="date_1">#F24949</color>
    <color name="date_2">#848484</color>

    <color name="grey_text_color">#999999</color>
    <color name="grey_text_color_disable">#d9d9d9</color>
    <color name="green_text_color">#00cc96</color>
    <color name="black_month_color">#666666</color>
    <color name="circle_orange_color">#F24949</color>
    <color name="circle_orange_color_disable">#e4393c</color>
    <color name="circle_grey_color">#999999</color>
    <color name="black_grey_color">#cccccc</color>
    <color name="white_text_color">#fffffe</color>
    <color name="calendar_bg_color">#f9f9f9</color>
    <color name="actionsheet_blue">#037BFF</color>
    <color name="actionsheet_red">#FD4A2E</color>
    <color name="actionsheet_gray">#929292</color>

    <!-- 会议室的颜色 -->
    <color name="conference_white_bg">#f2f2f2</color>
    <color name="conference_expand_white_bg">#dfdfdf</color>
    <color name="conference_white_2">#f0f0f0</color>
    <color name="conference_blue_color">#179de7</color>
    <color name="alertdialog_line">#c6c6c6</color>
    <color name="conference_black_color">#808080</color>
    <color name="conference_black_color2">#4c4c4c</color>
    <color name="conference_black_color3">#888888</color>
    <color name="conference_black_color4">#b3b3b3</color>
    <color name="conference_gry_color">#d8d8d8</color>
    <color name="reserve_popup_header_bg">#f5f5f5</color>
    <color name="md__defaultBackground">#FF555555</color>
    <color name="conference_filter_bg">#fafafa</color>
    <color name="conference_reserve_bg">#fbfbfb</color>

    <color name="background_tab_pressed">#6633B5E5</color>
    <!--员工用车-->
    <color name="didi_warn_bg">#96ffe1e1</color>

    <color name="didi_page_bg">#F4F4F4</color>
    <color name="feedback_line_bg">#E5E5E5</color>

    <color name="me_app_background">#f3f3f3</color>

    <color name="jdme_color_myapply_cancel">#F0250F</color>
    <color name="jdme_color_myapply_cancel_light">#fff0f0</color>
    <color name="jdme_color_myapply_finished">#57B987</color>
    <color name="jdme_color_myapply_finished_light">#e1faef</color>
    <color name="jdme_color_myapply_doing">#62A9FF</color>
    <color name="jdme_color_myapply_doing_light">#e8f4ff</color>
    <color name="jdme_color_myapply_future">#a0a0a0</color>
    <color name="jdme_color_myapply_future_background">#eeeeee</color>
    <color name="jdme_color_myapply_line">#d2d2d2</color>

    <color name="jdme_color_workbench_white_deliver">#4DFFFFFF</color>
    <color name="jdme_color_workbench_black_deliver">#4D2d2d2d</color>
    <color name="jdme_color_todo_no_data_tip_color">#808080</color>
    <color name="jdme_color_todo_finish_btn_color">#5c5c5c</color>
    <color name="jdme_color_todo_top_text_color">#ffb600</color>
    <color name="jdme_color_todo_todo_finish_color">#00cd95</color>

    <color name="jdme_color_reimbursement_approval">#2977FF</color>
    <color name="jdme_color_reimbursement_finish">#00CD95</color>
    <color name="jdme_color_reimbursement_cancel">#F23030</color>
    <color name="jdme_color_reimbursement_recall">#808080</color>

    <color name="jdme_color_didi_red_text">#f23030</color>
    <color name="jdme_color_didi_red_bg">#ffe7e7</color>

    <color name="jdme_color_ripple_background">#14000000</color>

    <color name="jdme_color_warning">#ff3153</color>
    <color name="jdme_color_success">#333</color>

    <color name="jdme_color_agreement_bg">#f6f6fa</color>

    <color name="jdme_color_daka_quick_content">#808080</color>
    <color name="jdme_color_daka_settings_line">#ebebeb</color>

    <color name="jdreact_common_textview_bg_color">#eaedf1</color>
    <color name="jdreact_common_title_text_color">#232326</color>
    <color name="c_F0F2F5">#F0F2F5</color>
    <color name="jdme_color_orange">#ff7e00</color>

    <color name="jdme_search_highlight_color">#3EA1E8</color>

    <color name="me_setting_background">#f7f7f9</color>
    <color name="me_setting_foreground">#2e2d2d</color>
    <color name="me_setting_foreground_light">#848484</color>
    <color name="me_setting_foreground_red">#F0250F</color>
    <color name="me_setting_divider">#eef1f4</color>

    <color name="me_app_market_text">#2E2D2D</color>
    <color name="me_app_market_background">#f7f7f9</color>
    <color name="me_app_market_tab_no_select">#C7C7C7</color>
    <color name="me_app_market_tab_no_select_bg">#E6E6E6</color>
    <color name="me_app_market_tab_select">#EE5A55</color>
    <color name="me_app_market_tab_text_select">#F0250F</color>

    <color name="me_app_workbench_approval_text">#2E2D2D</color>
    <color name="me_app_workbench_approval_light_text">#BBBBBB</color>
    <color name="me_app_workbench_approval_wait_text">#F0250F</color>
    <color name="me_app_workbench_approval_line">#D6DBE1</color>
    <color name="me_color_myapply_detail">#80f23030</color>
    <color name="me_app_workbench_schedule_todo_red">#F0250F</color>
    <color name="me_app_workbench_schedule_todo_gray">#848484</color>

    <color name="me_approval_desc_background">#f2f2f2</color>

    <!-- 流程中心优化 -->
    <color name="me_flowcenter_title_red_tip">#EE5A55</color>
    <color name="me_flowcenter_title_text">#2E2D2D</color>

    <color name="me_color_green">#4EBF66</color>
    <color name="me_color_yellow">#FCA600</color>
    <color name="me_color_red">#FE3931</color>

    <color name="divider">#eef1f4</color>

    <color name="tabbar_text_color_normal">#4A4A4A</color>
    <color name="tabbar_text_color_selected">#EE5A55</color>

    <!-- ====================== 控件颜色 ========================-->

    <!-- Titlebar 的颜色设置 -->
    <color name="libui_titlebar_bg">@color/comm_blue
    </color>                  <!--titleBar的背景颜色 色值-->
    <color name="libui_titlebar_title_textColor">@color/comm_white
    </color>    <!--titleBar的文字颜色 色值-->

    <!-- Emptyview 的颜色设置 -->
    <color name="libui_emptyview_text_dark">#444444</color>
    <color name="libui_emptyview_text_light">#929292</color>

    <!-- ErrorView 的颜色设置 -->
    <color name="libui_errorview_bg">@color/comm_grey</color>                 <!-- 背景颜色 -->

    <!-- CustomDialogView 的颜色设置 -->
    <color name="libui_custom_dialog_bg">@color/comm_white</color>            <!--  dialog 背景色 -->
    <color name="libui_custom_dialog_btn_press">#eaeaea
    </color>               <!--  dialog 按钮按下颜色 -->

    <!-- Pull2Refresh 的颜色设置 -->
    <color name="libui_header_progress_default_color">#FF4081</color>
    <color name="libui_default_header_background_color">#f3f3f3</color>
    <color name="libui_default_text_color">#2d2d2d</color>
    <color name="libui_footer_background_color">#f3f3f3</color>

    <color name="color_aaaaaa">#AAAAAA</color>
    <color name="color_fd635b">#FD635B</color>
    <color name="color_2ece39">#2ECE39</color>
    <color name="color_f7f7f9">#F7F7F9</color>
    <color name="color_de0e3">#DEE0E3</color>
    <color name="color_2e2d2d">#2E2D2D</color>
    <color name="color_232930">#232930</color>
    <color name="color_62656D">#62656D</color>

    <color name="color_4bf0250f">#4BF0250F</color>
    <color name="color_bfc1c4">#BFC1C4</color>
    <color name="color_8f959e">#8F959E</color>
    <color name="color_4c7cff">#4C7CFF</color>
    <color name="color_dee0e3">#DEE0E3</color>
    <color name="color_fe3b30">#FE3B30</color>
    <color name="color_fe3b30_50">#7FFE3B30</color>


    <color name="joywork_red">#FE3B30</color>
    <color name="joywork_red_transparent">#7FFE3B30</color>
    <color name="color_1b96fe">#1B96FE</color>
    <color name="color_f8f9fa">#F8F9FA</color>
    <color name="color_0AFE3B30">#0AFE3B30</color>
    <color name="color_FE3B30">#FE3B30</color>


    <color name="color_tag_gray">#666666</color>
    <color name="color_tag_blue">#4C7CFF</color>
    <color name="color_f2f3f5">#F2F3F5</color>
    <color name="color_FFCF33">#FFCF33</color>
    <color name="color_FFA53D">#FFA53D</color>

    <color name="color_F63218">#F63218</color>
    <color name="color_1B1B1B">#1B1B1B</color>
    <color name="color_9D9D9D">#9D9D9D</color>
    <color name="color_CECECE">#CECECE</color>
    <color name="color_FFF63218">#FFF63218</color>
    <color name="color_F8F8F9">#F8F8F9</color>
    <color name="color_6A6A6A">#6A6A6A</color>
    <color name="color_FFB416">#FFB416</color>
    <color name="color_FBB731">#FBB731</color>
    <color name="color_F0F1F2">#F0F1F2</color>
    <color name="color_F85B46">#F85B46</color>
    <color name="color_F4F5F6">#F4F5F6</color>

<!--    <color name="color_fe3b30">#FE3B30</color>-->
</resources>