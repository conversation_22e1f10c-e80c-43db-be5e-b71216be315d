<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <LinearLayout
        android:id="@+id/layout_container"
        android:layout_width="270dp"
        android:layout_height="310dp"
        android:layout_marginTop="40dp"
        android:orientation="vertical"
        android:background="@drawable/jdme_update">
        <TextView
            android:id="@+id/tv_update_subject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:gravity="center_horizontal"
            android:lineSpacingExtra="2dp"
            android:textColor="@color/black_main_title"
            android:textSize="16sp"
            tools:text="标题标题标题标"/>
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="8dp">
            <TextView
                android:id="@+id/tv_update_summary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="start"
                android:lineSpacingExtra="6dp"
                android:textColor="@color/black_main_summary"
                android:textSize="14sp"
                tools:text="1、更新内容更新内容更新内容更新内容更新内容更新内容\n1、更新内容更新内容\n1、更新内容更内容\n1、更新内容更新内容"/>
        </ScrollView>
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_update_ok"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/jdme_btn_update"
            android:text="@string/me_upgrade_now"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:src="@drawable/jdme_rice" />
    <ImageView
        android:id="@+id/iv_update_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/layout_container"
        android:layout_alignEnd="@id/layout_container"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="2dp"
        android:padding="12dp"
        android:src="@drawable/jdme_ic_close" />
</RelativeLayout>