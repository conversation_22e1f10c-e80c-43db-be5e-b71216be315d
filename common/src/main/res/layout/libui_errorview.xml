<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/libui_errorview"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/comm_background"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:background="@color/comm_background"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/libui_errorview_image"
            android:layout_width="135dp"
            android:layout_height="135dp"
            android:background="@drawable/libui_request_error_image"
            android:visibility="visible" />

        <TextView
            android:id="@+id/libui_errorview_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/libui_request_error"
            android:textColor="@color/comm_text_desc"
            android:textSize="18dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/libui_tips_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:textColor="@color/comm_text_nor"
            android:text="@string/libui_reload_retry"
            android:textSize="16dp" />

        <Button
            android:id="@+id/libui_error_button"
            android:layout_width="105dp"
            android:layout_height="41dp"
            android:layout_marginTop="22dp"
            android:background="@drawable/libui_reload_button" />
    </LinearLayout>

</LinearLayout>