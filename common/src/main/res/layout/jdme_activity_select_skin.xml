<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#FAFAFA">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="#FFFFFF">
        <RelativeLayout
            android:id="@+id/back"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true">
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/icon_direction_left"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_20"/>
        </RelativeLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#2E2D2D"
            android:textStyle="bold"
            android:textSize="18dp"
            android:text="@string/exp_theme_skin"
            android:textAlignment="center"
            android:includeFontPadding="false"/>
    </RelativeLayout>

    <com.jd.oa.pulltorefresh.PullToRefreshLayout
        android:id="@+id/skin_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/skin_recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="4dp"/>
    </com.jd.oa.pulltorefresh.PullToRefreshLayout>

</LinearLayout>