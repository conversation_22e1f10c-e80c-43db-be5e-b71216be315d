<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_bg_toast"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="0dp">

    <TextView
        android:id="@+id/toast_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:layout_marginEnd="38dp"
        android:layout_marginLeft="38dp"
        android:layout_marginRight="38dp"
        android:layout_marginStart="38dp"
        android:layout_marginTop="20dp"
        android:drawablePadding="10dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="15dp"
        tools:text="@string/exp_theme_setup_succeeded"/>
</LinearLayout>