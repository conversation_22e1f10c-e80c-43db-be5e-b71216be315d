<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:padding="5dp">

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_image"
        android:layout_marginRight="10dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:textColor="@color/me_setting_foreground"
        android:textSize="16dp"
        tools:text="infoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfoinfo" />

    <TextView
        android:id="@+id/tv_option_drop"
        style="@style/my_button_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="删除"
        android:textSize="16dp" />
</LinearLayout>