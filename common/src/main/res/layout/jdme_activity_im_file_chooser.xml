<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f4f5f6">


    <LinearLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="12dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:includeFontPadding="false"
            android:paddingHorizontal="15dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#1b1b1b"
            android:textSize="18dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/more"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:includeFontPadding="false"
            android:paddingHorizontal="15dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"
            android:visibility="invisible" />

    </LinearLayout>


    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refreshView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/title">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    app:layout_constraintBottom_toTopOf="@id/bottom_bar"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                </androidx.recyclerview.widget.RecyclerView>

                <RelativeLayout
                    android:id="@+id/bottom_bar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:background="@color/white"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="8dp"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <TextView
                        android:id="@+id/pre_selected_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/jdme_im_selected_files"
                        android:textColor="#6a6a6a"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/selected_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_toRightOf="@id/pre_selected_count"
                        android:textColor="#1b1b1b"
                        android:textSize="14dp"
                        tools:text="6个附件" />

                    <TextView
                        android:id="@+id/send"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/jdme_file_viewer_btn_sel"
                        android:enabled="false"
                        android:paddingHorizontal="28dp"
                        android:paddingVertical="8dp"
                        android:textColor="@color/white"
                        android:textSize="14dp"
                        tools:text="发送" />

                </RelativeLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/ll_emptyView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="100dp"
                    android:background="@drawable/jdme_list_empty" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/jdme_no_im_files" />
            </LinearLayout>
        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</RelativeLayout>