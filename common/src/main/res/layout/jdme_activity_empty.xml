<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:id="@+id/layout_empty">
    <ImageView
        android:id="@+id/iv_empty"
        android:layout_width="160dp"
        android:layout_height="100dp"
        android:layout_centerInParent="true"
        android:src="@drawable/cmn_split_detail_default" />

    <TextView
        android:layout_alignStart="@id/iv_empty"
        android:layout_alignEnd="@id/iv_empty"
        android:layout_below="@id/iv_empty"
        android:layout_marginTop="16dp"
        android:gravity="center_horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="#6A6A6A"
        android:text="@string/jdme_placeholder_slogan"/>
</RelativeLayout>