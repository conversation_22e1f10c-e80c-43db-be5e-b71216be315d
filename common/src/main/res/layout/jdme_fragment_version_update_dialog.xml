<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="304dp"
        android:layout_height="wrap_content"
        tools:ignore="UselessParent">

        <LinearLayout
            android:id="@+id/layout_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/ll_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/image"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_version_update_bg" />

            <View
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/jdme_version_update_bottom" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="198dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_update_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    tools:visibility="gone">

                    <TextView
                        android:id="@+id/tv_update_subject"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:ellipsize="end"
                        android:gravity="center_horizontal"
                        android:lineSpacingExtra="2dp"
                        android:maxLines="1"
                        android:textColor="@color/black_main_title"
                        android:textSize="16dp"
                        android:textStyle="bold"
                        tools:text="标题标题标题标" />

                    <TextView
                        android:id="@+id/tv_update_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="#BFBFBF"
                        android:textSize="14dp"
                        tools:text="wifi环境下更新不到30秒哦～" />

                    <com.jd.oa.ui.HeightLimitedScrollView
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/jdme_version_update_content_text_bg"
                        android:minHeight="66dp"
                        android:overScrollMode="never"
                        android:paddingStart="4dp"
                        android:paddingTop="10dp"
                        android:paddingEnd="4dp"
                        android:paddingBottom="10dp"
                        app:maxHeight="136dp">

                        <TextView
                            android:id="@+id/tv_update_summary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:gravity="start"
                            android:lineSpacingExtra="6dp"
                            android:paddingStart="21dp"
                            android:paddingEnd="21dp"
                            android:textColor="@color/black_main_summary"
                            android:textSize="14dp"
                            tools:text="1、更新内容更新内容更新内容更新内容更新内容更新内容\n1、更新内容更新内容\n1、更新内容更内容\n1、更新内容更新内容\n1、更新内容更新内容\n1、更新内容更新内容\n1、更新内容更新内容\n1、更新内容更新内容" />
                    </com.jd.oa.ui.HeightLimitedScrollView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_download_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="50dp"
                        android:text="@string/jdme_update_download_status"
                        android:textColor="#6A6A6A"
                        android:textSize="14dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="35dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="65dp"
                        android:gravity="center_vertical">

                        <ProgressBar
                            android:id="@+id/progress_bar"
                            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                            android:layout_width="0dp"
                            android:layout_height="10dp"
                            android:layout_weight="1"
                            android:max="100"
                            android:progress="0"
                            android:progressDrawable="@drawable/jdme_progress_view"
                            tools:progress="30" />

                        <TextView
                            android:id="@+id/tv_currentProgress"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:textColor="#6A6A6A"
                            android:textSize="14dp"
                            tools:text="99%" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_failed"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="gone">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="23dp"
                        android:src="@drawable/jdme_update_error" />

                    <TextView
                        android:id="@+id/tv_failed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="28dp"
                        android:text="@string/jdme_update_fail"
                        android:textColor="#6A6A6A"
                        android:textSize="12dp" />

                </LinearLayout>

            </FrameLayout>

            <LinearLayout
                android:id="@+id/ll_buttons"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginStart="10dp"
                android:layout_marginTop="2dp"
                android:layout_marginEnd="10dp">

                <com.jd.oa.ui.GradientButton
                    android:id="@+id/btn_jump"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="2dp"
                    android:layout_weight="1"
                    app:button_end_color="@color/transparent"
                    app:button_is_shadowed="true"
                    app:button_press_end_color="#20000000"
                    app:button_press_start_color="#20000000"
                    app:button_radius="20dp"
                    app:button_shadow_angle="0"
                    app:button_shadow_color="#47F33A2C"
                    app:button_shadow_distance="0dp"
                    app:button_shadow_radius="6dp"
                    app:button_size="7dp"
                    app:button_start_color="@color/transparent"
                    app:button_stroke_color="#FE3E33"
                    app:button_stroke_width="1dp"
                    app:button_text="@string/me_new_features"
                    app:button_text_color="#FE3E33" />

                <com.jd.oa.ui.GradientButton
                    android:id="@+id/btn_update_ok"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:button_end_color="#E83725"
                    app:button_gradient_orientation="TOP_BOTTOM"
                    app:button_is_shadowed="true"
                    app:button_press_end_color="#E83725"
                    app:button_press_start_color="#E83725"
                    app:button_radius="20dp"
                    app:button_shadow_angle="0"
                    app:button_shadow_color="#47F33A2C"
                    app:button_shadow_distance="0dp"
                    app:button_shadow_radius="6dp"
                    app:button_size="7dp"
                    app:button_start_color="#FF3D33"
                    app:button_text="@string/me_upgrade"
                    app:button_text_color="@color/white" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_update_num"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#666666"
                android:textSize="14dp"
                tools:text="80%的JDer已升级" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_update_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/layout_container"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="35dp"
            android:padding="5dp"
            android:src="@drawable/jdme_icon_dialog_close" />

    </RelativeLayout>

</FrameLayout>