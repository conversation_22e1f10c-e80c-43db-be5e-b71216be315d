<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/file_icon"
        android:layout_width="110dp"
        android:layout_height="130dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="80dp"
        android:src="@drawable/mae_netdisk_file" />

    <TextView
        android:id="@+id/file_name"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="@dimen/me_text_size_middle"
        tools:text="ccccccccccccccccccccccccdddddddddddc" />

    <ProgressBar
        android:id="@+id/pb_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:layout_margin="20dp"
        android:background="@drawable/jdme_bg_file_progress"
        android:max="100" />

    <Button
        android:id="@+id/btn_open"
        style="@style/my_button"
        android:layout_width="200dp"
        android:layout_height="42dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:background="@drawable/jdme_btn_red"
        android:text="@string/me_file_use_other_app_open"
        android:textColor="@color/white"
        android:textSize="@dimen/comm_text_normal_large"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="vertical"
        android:visibility="invisible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:ellipsize="middle"
            android:singleLine="true"
            android:text="@string/me_file_tip_0"
            android:textColor="@color/gray_c7c7c7"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:ellipsize="middle"
            android:singleLine="true"
            android:text="@string/me_file_tip_1"
            android:textColor="@color/gray_c7c7c7"
            android:textSize="@dimen/me_text_size_14" />
    </LinearLayout>

</LinearLayout>