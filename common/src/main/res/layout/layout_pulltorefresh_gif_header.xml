<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical">

    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/iv_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="1dp"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:scaleType="fitXY"
        android:src="@drawable/pull_to_refresh_header"/>

    <TextView
        android:id="@+id/loading_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_above="@id/iv_header"
        android:layout_centerHorizontal="true"
        android:maxLines="1"
        android:textColor="#000000"
        android:textSize="12dp"
        android:text="@string/pull_header_text_refresh"
        android:textAlignment="center"
        android:includeFontPadding="false"
        />

</RelativeLayout>