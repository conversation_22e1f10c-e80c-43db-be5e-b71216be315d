<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="44dp">

    <TextView
        android:id="@+id/tv_tab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:textColor="@color/me_app_market_text"
        android:textSize="14sp"
        tools:text="办公助理" />

    <View
        android:id="@+id/view_indicator"
        android:layout_width="28dp"
        android:layout_height="3dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/jdme_shape_app_market_tab_indicator_bg"
        android:visibility="gone"
        tools:visibility="visible" />
</RelativeLayout>