<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_pedia_popup_more"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_shape_bg_joy_pedia_unlike_card">

    <LinearLayout
        android:id="@+id/ll_new_pedia"
        android:layout_width="160dp"
        android:layout_height="35dp"
        android:gravity="center_vertical">

        <com.jd.oa.ui.IconFontView
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="15dp"
            android:textSize="@dimen/JMEIcon_18"            android:text="@string/icon_edit_newentry" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="创建新词条"
            android:textColor="#333" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/ll_update_pedia"
        android:layout_width="160dp"
        android:layout_height="35dp"
        android:layout_below="@id/ll_new_pedia"
        android:gravity="center_vertical"
        android:padding="1dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="15dp"
            android:textSize="@dimen/JMEIcon_18"            android:text="@string/icon_edit_updateentry" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="更新该词条"
            android:textColor="#333" />
    </LinearLayout>
</RelativeLayout>