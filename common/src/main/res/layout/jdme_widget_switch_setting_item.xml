<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:orientation="vertical"
    tools:parentTag="android.widget.LinearLayout">

    <LinearLayout
        android:id="@+id/layout_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_ripple_white_corner8"
        android:orientation="vertical"
        android:padding="16dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#2e2d2d"
                android:textSize="16dp"
                tools:text="功能名称" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/sw"
                style="@style/SwitchStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textSize="14dp"
            tools:text="功能描述文字，功能描述问文字" />
    </LinearLayout>


    <FrameLayout
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="16dp"
            android:background="#E6E6E6" />
    </FrameLayout>
</merge>