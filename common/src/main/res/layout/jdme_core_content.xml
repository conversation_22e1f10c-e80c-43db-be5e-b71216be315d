<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/half_screen_chat_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/half_screen_tool_bar"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.jd.oa.ui.IconFontView
            android:text="@string/icon_prompt_close"
            android:textSize="@dimen/JMEIcon_24"
            android:id="@+id/button_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="16dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textSize="16sp"
            android:textColor="#1B1B1B"
            android:text="测试"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/button_unread"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_edit_historyright"
            android:textSize="@dimen/JMEIcon_24"
            app:layout_constraintRight_toLeftOf="@id/button_expand"
            android:layout_marginEnd="20dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/button_expand"
            android:text="@string/icon_general_anobliquearrow"
            android:textSize="@dimen/JMEIcon_24"
            android:layout_marginEnd="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/half_screen_tool_bar"
        app:layout_constraintBottom_toTopOf="@id/dialog_edittext">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:text="1"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:text="2"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:text="3"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <EditText
        android:id="@+id/dialog_edittext"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>