<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/header_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/libui_default_text_color"
        android:text="@string/libui_header_normal"
        android:layout_centerInParent="true" />

    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/header_gif"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:scaleType="fitCenter"
        android:layout_marginRight="30dp"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/header_text" />

</RelativeLayout>