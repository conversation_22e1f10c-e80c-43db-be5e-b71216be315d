<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_pedia_popup"
    android:layout_width="130dp"
    android:layout_height="44dp"
    android:padding="5dp">

    <LinearLayout
        android:layout_width="120dp"
        android:layout_height="34dp"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_bg_pedia_unlike_unselect"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp">

        <ImageView
            android:id="@+id/iv"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_gravity="center_vertical" />

        <TextView
            android:id="@+id/tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:text="111111"
            android:textColor="#333333"
            android:textSize="12dp" />

    </LinearLayout>
</RelativeLayout>