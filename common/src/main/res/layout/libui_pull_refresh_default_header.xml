<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="60dp">

    <TextView
        android:id="@+id/header_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/libui_default_text_color"
        android:text="@string/libui_header_normal"
        android:layout_centerInParent="true" />

    <ImageView
        android:id="@+id/header_stop_icon"
        android:visibility="invisible"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginRight="20dp"
        android:layout_centerInParent="true"
        android:layout_toLeftOf="@+id/header_text"
        android:tint="@color/libui_header_progress_default_color" />

    <ImageView
        android:id="@+id/header_arrow_icon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:scaleType="fitCenter"
        android:src="@drawable/libui_pull_refresh_pull"
        android:tint="@color/libui_header_progress_default_color"
        android:layout_marginRight="30dp"
        android:layout_centerInParent="true"
        android:layout_toLeftOf="@id/header_text" />

    <ProgressBar
        android:id="@+id/header_progress"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:indeterminateTint="@color/libui_header_progress_default_color"
        android:indeterminateTintMode="src_atop"
        android:visibility="gone"
        android:layout_marginRight="20dp"
        android:layout_centerInParent="true"
        android:layout_toLeftOf="@id/header_text" />

</RelativeLayout>