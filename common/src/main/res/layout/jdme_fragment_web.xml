<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- View that will be hidden when video goes fullscreen -->
    <LinearLayout
        android:id="@+id/nonVideoLayout"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 使用 toolbar 定制 actionbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/abc_action_bar_default_height"
            android:background="@drawable/jdme_bg_actionbar_white"
            app:contentInsetStart="0dp"
            app:contentInsetStartWithNavigation="0dp">

            <ImageButton
                android:id="@+id/btn_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|center"
                android:layout_marginEnd="8dip"
                android:background="@color/transparent"
                android:src="@drawable/jdme_abc_ic_clear_mtrl_alpha"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|center"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="18sp" />
        </androidx.appcompat.widget.Toolbar>

        <com.jd.oa.ui.MyProgressWebView
            android:id="@+id/web_view_process"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true">

        </com.jd.oa.ui.MyProgressWebView>
    </LinearLayout>

    <!-- View where the video will be shown when video goes fullscreen -->
    <RelativeLayout
        android:id="@+id/videoLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible">
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/layout_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">
        <pl.droidsonroids.gif.GifImageView
            android:layout_width="125dp"
            android:layout_height="125dp"
            android:scaleType="fitCenter"
            android:src="@drawable/jdme_web_loading"/>
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_failed"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/abc_action_bar_default_height"
        android:visibility="gone"
        tools:visibility="visible">
        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/iv_failed"
            android:layout_width="125dp"
            android:layout_height="125dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginBottom="55dp"
            android:scaleType="fitCenter"
            android:src="@drawable/jdme_web_failed"/>
        <TextView
            android:id="@+id/tv_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_failed"
            android:layout_marginTop="12dp"
            android:textSize="16sp"
            android:textColor="@color/comm_text_normal"
            android:lineSpacingMultiplier="1.2"
            android:text="@string/me_web_loading_failed"/>
        <TextView
            android:id="@+id/tv_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="48dp"
            android:textSize="16sp"
            android:textColor="@color/comm_text_normal"
            android:text="@string/me_web_check_network_and_proxy"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>


