<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="fill_parent"
              android:layout_height="wrap_content"
              android:gravity="center_vertical"
              android:orientation="vertical">

    <TextView
        android:id="@+id/catalog"
        android:layout_width="16dp"
        android:layout_height="18dp"
        android:layout_marginBottom="6dp"
        android:layout_marginLeft="9dp"
        android:layout_marginTop="6dp"
        android:background="#EDEDED"
        android:gravity="center"
        android:text="A"
        android:textColor="#666666"
        android:textSize="12sp"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="fill_parent"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:background="@drawable/jdme_selector_conference_search_list"
        android:gravity="center_vertical"
        android:paddingLeft="18dp"
        android:textColor="#666666"
        android:textSize="14sp"
        />

</LinearLayout>