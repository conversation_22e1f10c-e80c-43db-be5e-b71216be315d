<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="12dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:includeFontPadding="false"
            android:paddingHorizontal="15dp"
            android:text="@string/icon_direction_left"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#1b1b1b"
            android:textSize="18dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/more"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="15dp"
            android:text="@string/icon_tabbar_more"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_20" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/web_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/title" />

    <FrameLayout
        android:id="@+id/state_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="#F8F8F9"
        android:layout_below="@id/title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginHorizontal="60dp"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_state"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/jdme_icon_send" />

            <TextView
                android:id="@+id/tv_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="#1b1b1b"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/tv_state_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="#6a6a6a"
                android:textSize="12dp" />

            <Button
                android:id="@+id/btn_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:background="@drawable/jdme_file_viewer_btn_sel"
                android:paddingHorizontal="12dp"
                android:paddingVertical="8dp"
                android:textColor="@color/white" />
        </LinearLayout>
    </FrameLayout>

    <RelativeLayout
        android:id="@+id/download_state"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:elevation="30dp"
        android:paddingHorizontal="24dp"
        android:paddingVertical="15dp">

        <TextView
            android:id="@+id/tv_download"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="正在下载"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/cancel_download"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="15dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#6a6a6a"
            android:textSize="@dimen/JMEIcon_20" />

        <ProgressBar
            android:id="@+id/progress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:progress="80"
            android:progressDrawable="@drawable/jdme_progress_green"
            android:layout_below="@id/tv_download"
            android:layout_marginTop="8dp"
            android:layout_toLeftOf="@id/cancel_download" />

    </RelativeLayout>

</RelativeLayout>