<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinator"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false">

        <View
            android:id="@+id/touch_outside"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:importantForAccessibility="no"
            android:soundEffectsEnabled="false"
            tools:ignore="UnusedAttribute" />

        <FrameLayout
            android:id="@+id/design_bottom_sheet"
            style="?attr/bottomSheetStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#ffffff"
            android:layout_gravity="center_horizontal|top"
            app:layout_behavior="com.jd.oa.cool.FloatingBehavior">

            <!--            <androidx.core.widget.NestedScrollView-->
            <!--                android:id="@+id/design_bottom_sheet"-->
            <!--                app:layout_behavior="com.jd.oa.cool.TestBehavior"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:fillViewport="true"-->
            <!--                android:layout_height="wrap_content">-->

            <FrameLayout
                android:id="@+id/frg_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#ffffff" />
            <!--            </androidx.core.widget.NestedScrollView>-->
        </FrameLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</FrameLayout>
