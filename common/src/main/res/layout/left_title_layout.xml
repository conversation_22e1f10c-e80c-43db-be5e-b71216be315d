<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="24dp" />
    <!--标题-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <!--左标题-->
        <LinearLayout
            android:id="@+id/left_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="15dp">

            <ImageView
                android:id="@+id/left_back"
                android:layout_width="21dp"
                android:layout_height="22dp"
                android:layout_gravity="center_vertical"
                android:scaleType="centerInside"
                android:src="@drawable/ic_back4" />

            <TextView
                android:id="@+id/left_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="10dp"

                android:text=""
                android:textColor="@color/tt_33333"
                android:textSize="@dimen/text_size_14" />
        </LinearLayout>

        <TextView
            android:layout_toEndOf="@id/left_ll"
            android:layout_toStartOf="@id/right_ll"
            android:id="@+id/middle_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/tt_33333"
            android:textSize="@dimen/text_size_18"
            android:textStyle="bold" />

        <RelativeLayout
            android:id="@+id/right_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:padding="15dp">

            <TextView
                android:id="@+id/right_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center_vertical"
                android:text="提交"
                android:textColor="@color/tt_33333"
                android:textSize="@dimen/text_size_14"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/right_title_iv"
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center_vertical"
                android:scaleType="fitXY"
                android:visibility="gone" />
        </RelativeLayout>


    </RelativeLayout>

</LinearLayout>
