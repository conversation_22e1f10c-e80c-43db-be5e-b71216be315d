<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/layout"
    android:layout_width="290dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- title msg -->
    <RelativeLayout
        android:id="@+id/title_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:visibility="gone">

        <!--title信息-->
        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:gravity="center_horizontal"
            android:text="我们我们我们"
            android:textColor="@color/comm_text_nor"
            android:textSize="16sp"
            android:visibility="gone" />

        <!--右上角的关闭按钮-->
        <RelativeLayout
            android:id="@+id/closeBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:paddingRight="10dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:scaleType="center"
                android:src="@drawable/libui_custom_dialog_view_fork_icon" />

        </RelativeLayout>

    </RelativeLayout>

    <!--分割线-->
    <View
        android:id="@+id/title_message_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#D1D1D1"
        android:visibility="gone" />

    <!--message信息-->
    <TextView
        android:id="@+id/message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="32dp"
        android:maxHeight="250dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="18dp"
        android:layout_marginLeft="29dp"
        android:layout_marginRight="29dp"
        android:scrollbars="vertical"
        android:textColor="@color/comm_text_nor"
        android:textSize="14sp"
        android:layout_gravity="center"
        android:visibility="gone" />

    <!--分割线-->
    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#dddddd" />

    <!-- Button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal">

        <!--左侧按钮-->
        <Button
            android:id="@+id/left_btn"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/libui_custom_dialog_view_left_btn_select"
            android:text="@string/cancel"
            android:textColor="#8199f6"
            android:textSize="16sp" />

        <!--分割线-->
        <View
            android:id="@+id/single_line"
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="#dddddd" />

        <!--右侧按钮-->
        <Button
            android:id="@+id/right_btn"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/libui_custom_dialog_view_right_btn_select"
            android:text="@string/confirm"
            android:textColor="#8199f6"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>
