<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="6dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/jdme_bg_multi_task_button_right_square"
    android:gravity="center">

    <com.jd.oa.multitask.CircleLayout
        android:id="@+id/circleLayout"
        android:layout_width="47dp"
        android:layout_height="47dp"
        android:layout_centerHorizontal="true" />

    <androidx.cardview.widget.CardView
        android:layout_below="@id/circleLayout"
        android:id="@+id/cv_media_control"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/iv_audio_cover"
                android:layout_width="82dp"
                android:layout_height="82dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>
            <com.jd.oa.common.me_audio_player.PlayControlButton
                android:id="@+id/btn_audio_play_pause"
                android:layout_marginVertical="8dp"
                android:layout_marginStart="9dp"
                android:layout_width="28dp"
                android:layout_height="24dp"
                app:buttonColor="#F85B46"
                app:cornerRadius="27dp"
                app:playIcon="@drawable/jdme_ic_audio_pause"
                app:pauseIcon="@drawable/jdme_ic_audio_play"
                app:progressColor="@color/white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_audio_cover"
                app:layout_constraintBottom_toBottomOf="parent"/>
            <ImageButton
                android:id="@+id/btn_close"
                android:background="@drawable/jdme_ic_close_gray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_audio_cover"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginVertical="8dp"
                android:layout_marginEnd="9dp"
                android:layout_width="28dp"
                android:layout_height="24dp"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</RelativeLayout>
