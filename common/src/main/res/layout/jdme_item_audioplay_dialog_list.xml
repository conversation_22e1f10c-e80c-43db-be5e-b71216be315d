<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_media_cover_container"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="6dp"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <ImageView
            android:id="@+id/iv_media_cover_image"
            android:scaleType="centerCrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_media_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="#1B1B1B"
        android:ellipsize="end"
        android:maxLines="1"
        android:textStyle="bold"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintStart_toEndOf="@id/cv_media_cover_container"
        app:layout_constraintEnd_toStartOf="@id/iv_media_play_pause"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="法律风险管理一体化项目立项申请报告…"/>
    
    <ImageView
        android:id="@+id/iv_media_play_pause"
        android:layout_width="28dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/jdme_ic_audio_pause"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_media_cancel"/>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_media_cancel"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="9dp"
        android:gravity="center"
        android:text="@string/icon_prompt_close"
        android:textColor="#CDCDCD"
        android:textSize="@dimen/JMEIcon_14"/>
</androidx.constraintlayout.widget.ConstraintLayout>