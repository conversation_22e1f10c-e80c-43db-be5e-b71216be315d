<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/translucent"
    android:orientation="vertical"
    android:paddingHorizontal="12dp">

    <LinearLayout
        android:id="@+id/options"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_shape_white_radius_bg"
        android:orientation="vertical">

        <TextView
            android:id="@+id/opt_open"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="12dp"
            android:text="@string/me_file_use_other_app_open"
            android:textColor="#1b1b1b"
            android:textSize="16dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:background="@drawable/jdme_shape_white_radius_bg"
        android:gravity="center"
        android:paddingVertical="12dp"
        android:text="@string/cancel"
        android:textColor="#1b1b1b"
        android:textSize="16dp" />

</LinearLayout>