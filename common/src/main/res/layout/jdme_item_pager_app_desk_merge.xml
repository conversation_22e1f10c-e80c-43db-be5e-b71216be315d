<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="14dp"
    android:paddingTop="6dp"
    android:paddingBottom="6dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/cb_task"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:gravity="center"
        android:padding="3dp"
        android:textSize="@dimen/JMEIcon_16"
        app:buttonTint="@color/me_app_market_tab_select"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/top"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/cb_task"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cb_task"
        app:layout_constraintTop_toTopOf="@id/cb_task">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="@color/color_2e2d2d"
                android:textSize="14sp" />

            <com.jd.oa.ui.TDTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:td_text="aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                app:td_textColor="@color/color_2e2d2d"
                app:td_textSize="14sp" />
        </FrameLayout>

        <LinearLayout
            android:id="@+id/extend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal" />

        <com.jd.oa.ui.CircleProgressBar
            android:id="@+id/pb"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="14dp"
            android:indeterminate="false"
            android:indeterminateOnly="false"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/cb_task"
            app:max="100"
            app:progressBarThickness="2dp"
            app:progressbarColor="#26A7FD"
            app:secondaryColor="#DEE0E3" />

        <ImageView
            android:id="@+id/finish_ic"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="14dp"
            android:indeterminate="false"
            android:indeterminateOnly="false"
            android:src="@drawable/jdme_ic_joywork_complete"
            android:visibility="gone" />
    </LinearLayout>

    <View
        android:id="@+id/seat"
        android:layout_width="wrap_content"
        android:layout_height="35dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cb_task"
        tools:ignore="MissingConstraints"
        tools:layout_editor_absoluteX="14dp" />


    <!--    <TextView-->
    <!--        android:id="@+id/from"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="9dp"-->
    <!--        android:layout_marginEnd="12dp"-->
    <!--        android:ellipsize="end"-->
    <!--        android:maxLength="8"-->
    <!--        android:maxLines="1"-->
    <!--        android:text="@string/me_joywork_from"-->
    <!--        android:textColor="#379AFF"-->
    <!--        android:textSize="14sp"-->
    <!--        app:layout_constraintStart_toStartOf="@id/top"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/top" />-->

    <!--    <LinearLayout-->
    <!--        android:id="@+id/divider"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="20dp"-->
    <!--        android:layout_marginTop="9dp"-->
    <!--        android:gravity="center_vertical"-->
    <!--        android:paddingStart="12dp"-->
    <!--        android:paddingEnd="12dp"-->
    <!--        app:layout_constraintStart_toEndOf="@id/from"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/top">-->

    <!--        <View-->
    <!--            android:layout_width="1dp"-->
    <!--            android:layout_height="10dp"-->
    <!--            android:background="#BFC1C4" />-->
    <!--    </LinearLayout>-->


    <!--    <TextView-->
    <!--        android:id="@+id/deadline"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="9dp"-->
    <!--        android:ellipsize="end"-->
    <!--        android:maxLines="1"-->
    <!--        android:text="@string/me_joywork_deadline_format"-->
    <!--        android:textColor="#8F959E"-->
    <!--        android:textSize="14sp"-->
    <!--        app:layout_constraintStart_toEndOf="@id/divider"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/top" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/priority"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="9dp"-->
    <!--        android:layout_marginEnd="14dp"-->
    <!--        android:padding="4dp"-->
    <!--        android:text="@string/me_joywork_level"-->
    <!--        android:textColor="#EB5B56"-->
    <!--        android:textSize="10dp"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/top" />-->

    <!--    <View-->
    <!--        android:id="@+id/divider_bottom"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="1dp"-->
    <!--        android:layout_marginTop="14dp"-->
    <!--        android:background="#F0F3F3"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="@id/from"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/deadline" />-->
</androidx.constraintlayout.widget.ConstraintLayout>