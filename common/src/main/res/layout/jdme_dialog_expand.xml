<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/jdme_expand_dialog_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/expend_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginLeft="16dp" />

            <TextView
                android:id="@+id/expand_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textStyle="bold"
                android:textColor="#000000"
                android:textSize="14dp"
                tools:text="应用名称应用名称应用名称应用名称应用名称应用名称应用名称应用名称应用名称应用名称应用名称应用名称" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="3dp"
            android:background="#DCDEE0" />

        <com.jd.oa.ui.WrapContentViewPager
            android:id="@+id/expand_view_pager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.viewpagerindicator.CirclePageIndicator
            android:id="@+id/expand_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            app:centered="true"
            app:circleSpacing="6dp"
            app:fillColor="#FFF63218"
            app:pageColor="#20000000"
            app:radius="2.5dp"
            app:strokeWidth="0dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="#DCDEE0" />

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#F4F5F6"
            android:gravity="center"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingTop="14dp"
            android:paddingBottom="14dp"
            android:textAllCaps="false"
            android:text="@string/libshare_cancel"
            android:textColor="#1B1B1B"
            android:textSize="16dp" />
    </LinearLayout>
</FrameLayout>