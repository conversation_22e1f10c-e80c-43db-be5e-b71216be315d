<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/header_arrow"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_centerVertical="true"
        android:layout_marginRight="6dp"
        android:layout_toLeftOf="@+id/header_tv"/>

    <ProgressBar
        android:id="@+id/header_progress"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_centerVertical="true"
        android:layout_marginRight="6dp"
        android:layout_toLeftOf="@+id/header_tv"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/header_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:padding="16dp"
        android:text="下拉刷新"
        android:textSize="14sp"
        android:layout_gravity="center"
        />

</RelativeLayout>