<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/libui_titlebar_rl"
    android:layout_width="match_parent"
    android:layout_height="@dimen/libui_titleBar_height"
    android:background="@color/libui_titlebar_bg"
    android:paddingLeft="8dp"
    android:paddingRight="8dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!--返回按钮-->
        <ImageView
            android:id="@+id/libui_titlebar_back_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:scaleType="center"
            android:src="@drawable/libui_title_back_btn_selector"
            android:visibility="gone" />

        <!--左侧图片按钮_1-->
        <ImageView
            android:id="@+id/libui_titlebar_left_btn_1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:scaleType="center"
            android:visibility="gone" />

        <!--左侧文字_左侧文字两边的图片 布局-->
        <LinearLayout
            android:id="@+id/libui_titlebar_leftText_rl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:visibility="gone">

            <!--左侧文字_左侧的按钮-->
            <ImageView
                android:id="@+id/libui_titlebar_leftText_left_icon"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingRight="5dp"
                android:scaleType="center"
                android:visibility="gone" />

            <!--左侧文字-->
            <TextView
                android:id="@+id/libui_titlebar_leftText_text"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:singleLine="true"
                android:text="右侧文字"
                android:textColor="@color/libui_titlebar_title_textColor"
                android:textSize="12sp" />

            <!--左侧文字_右侧的按钮-->
            <ImageView
                android:id="@+id/libui_titlebar_leftText_right_icon"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="5dp"
                android:scaleType="center"
                android:visibility="gone" />
        </LinearLayout>

        <!--左侧图片按钮_2-->
        <ImageView
            android:id="@+id/libui_titlebar_left_btn_2"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:scaleType="center"
            android:visibility="gone" />
    </LinearLayout>

    <!--标题_副标题_标题左侧右侧图片 全部布局-->
    <LinearLayout
        android:id="@+id/libui_titlebar_titleText_ll"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_marginLeft="55dp"
        android:layout_marginRight="55dp"
        android:orientation="horizontal">

        <!--标题文字 左侧的按钮-->
        <ImageView
            android:id="@+id/libui_titlebar_titleText_left_icon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:scaleType="center"
            android:visibility="gone" />

        <!--标题 双行标题 布局-->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <!--主标题文字-->
            <TextView
                android:id="@+id/libui_titlebar_titleText_mainTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:singleLine="true"
                android:text="主标题文字"
                android:textColor="@color/libui_titlebar_title_textColor"
                android:textSize="18sp"
                android:visibility="gone" />

            <!--副标题文字-->
            <TextView
                android:id="@+id/libui_titlebar_titleText_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/libui_titlebar_titleText_mainTitle"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="2dp"
                android:singleLine="true"
                android:text="副标题文字"
                android:textColor="@color/libui_titlebar_title_textColor"
                android:textSize="10sp"
                android:visibility="gone" />
        </RelativeLayout>

        <!--标题文字 右侧的按钮-->
        <ImageView
            android:id="@+id/libui_titlebar_titleText_right_icon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:scaleType="center"
            android:visibility="gone" />
    </LinearLayout>

    <!-- titleBar,标题文案右侧按钮布局-->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!--右侧文字按钮_2-->
        <ImageView
            android:id="@+id/libui_titlebar_right_btn_2"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:scaleType="center"
            android:visibility="gone" />

        <!--右侧文字_右侧文字两边的图片 布局-->
        <LinearLayout
            android:id="@+id/libui_titlebar_rightText_rl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:visibility="gone">

            <!--右侧文字 左侧的按钮-->
            <ImageView
                android:id="@+id/libui_titlebar_rightText_left_icon"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="center"
                android:paddingRight="5dp"
                android:visibility="gone" />

            <!--右侧文字-->
            <TextView
                android:id="@+id/libui_titlebar_rightText_text"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:singleLine="true"
                android:text="右侧文字"
                android:textColor="@color/libui_titlebar_title_textColor"
                android:textSize="12sp" />

            <!--右侧文字_右侧的按钮-->
            <ImageView
                android:id="@+id/libui_titlebar_rightText_right_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:paddingLeft="5dp"
                android:visibility="gone" />
        </LinearLayout>

        <!--右侧文字按钮_1-->
        <ImageView
            android:id="@+id/libui_titlebar_right_btn_1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:scaleType="center"
            android:visibility="gone" />

        <!--消息按钮-->
        <RelativeLayout
            android:id="@+id/libui_titlebar_message_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:visibility="gone">

            <!--消息按钮-->
            <ImageView
                android:id="@+id/libui_titlebar_message_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:scaleType="center" />

            <!-- 提示信息 -->
            <ImageView
                android:id="@+id/libui_titlebar_message_info"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_alignRight="@id/libui_titlebar_message_icon"
                android:layout_alignTop="@id/libui_titlebar_message_icon"
                android:layout_marginRight="1dp"
                android:layout_marginTop="-2dp"
                android:scaleType="fitXY" />
        </RelativeLayout>
    </LinearLayout>
</RelativeLayout>