<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tv_test_general"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dip"
        android:layout_marginBottom="5dp"
        android:text="General eml"
        android:textSize="17sp" />

    <TextView
        android:id="@+id/tv_test_sign"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dip"
        android:layout_marginBottom="5dp"
        android:text="EncryptAndSignEml"
        android:textSize="17sp" />


    <TextView
        android:id="@+id/tv_test_encrypt"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dip"
        android:layout_marginBottom="5dp"
        android:text="Encrypt Eml"
        android:textSize="17sp" />


    <TextView
        android:id="@+id/tv_test_parseEml"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dip"
        android:layout_marginBottom="5dp"
        android:text="ParseEml"
        android:textSize="17sp" />

    <TextView
        android:id="@+id/tv_test_getCert"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dip"
        android:layout_marginBottom="5dp"
        android:text="GetCer"
        android:textSize="17sp" />


    <TextView
        android:id="@+id/tv_test_getPfx"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dip"
        android:layout_marginBottom="5dp"
        android:text="GetPfx"
        android:textSize="17sp" />

</LinearLayout>