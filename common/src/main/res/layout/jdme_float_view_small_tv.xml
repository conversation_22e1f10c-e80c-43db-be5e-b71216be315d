<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="160dp"
    android:layout_height="90dp"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardMaxElevation="8dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/fl_video"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:id="@+id/v_touch"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/jdme_bg_shape_gradient_trans2black"
            android:gravity="center_vertical"
            android:paddingHorizontal="2dp">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/iv_play_pause"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:gravity="center"
                android:text="@string/icon_float_pause"
                android:textColor="@color/white"
                android:textSize="@dimen/JMEIcon_12"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/iv_close"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:gravity="center"
                android:text="@string/icon_float_close"
                android:textColor="@color/white"
                android:textSize="@dimen/JMEIcon_12"/>
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>