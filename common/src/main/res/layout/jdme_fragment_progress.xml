<?xml version="1.0" encoding="utf-8"?>
<!-- fragment 加载时，显示的 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
    android:background="@color/white"
             android:clickable="true">

    <!-- 加载框 大 -->

    <include
        android:id="@+id/progress_container"
        layout="@layout/jdme_loading_view"
        android:visibility="gone"/>

    <!-- 没有数据的提示 -->

    <TextView
        android:id="@+id/empty_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:drawablePadding="8dip"
        android:drawableTop="@drawable/jdme_mi_blank_page"
        android:gravity="center"
        android:text="@string/me_no_data"
        android:textColor="@color/black_main_summary"
        android:textSize="14sp"
        android:visibility="gone"/>

    <!-- 加载错误提示：没有网络 -->

    <TextView
        android:id="@+id/error_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:drawablePadding="8dip"
        android:drawableTop="@drawable/jdme_mi_network_error"
        android:gravity="center"
        android:text="@string/me_no_network"
        android:textColor="@color/black_main_summary"
        android:textSize="14sp"
        android:visibility="gone"/>

    <!-- 重新加载 ： 点击重试 -->

    <LinearLayout
        android:id="@+id/repeat_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_repeat_pic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitCenter"
            android:src="@drawable/jdme_mi_network_error"
            android:contentDescription="" />

        <TextView
            android:id="@+id/tv_repeat_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dip"
            android:text="@string/me_load_fail_retry"
            android:textColor="@color/black_main_summary"
            android:textSize="14sp"/>
    </LinearLayout>

</FrameLayout>