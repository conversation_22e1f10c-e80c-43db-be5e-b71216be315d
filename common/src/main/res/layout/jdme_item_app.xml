<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_image"
        android:layout_width="@dimen/app_icon_size"
        android:layout_height="@dimen/app_icon_size"
        android:layout_marginTop="15dp"
        android:layout_centerHorizontal="true"
        android:scaleType="fitCenter" />

    <ImageView
        android:id="@+id/app_icon_badge"
        android:layout_width="25dp"
        android:layout_height="14dp"
        android:layout_marginStart="-15dp"
        android:layout_marginTop="-4dp"
        android:layout_toEndOf="@id/iv_image"
        android:layout_alignTop="@id/iv_image"
        android:src="@drawable/jdme_app_badge_new"
        android:visibility="gone"
        tools:ignore="ContentDescription"
        tools:visibility="visible" />
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_image"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="2dp"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="11dp"
        android:textColor="@color/me_setting_foreground"
        tools:text="应用名称名称"/>
</RelativeLayout>