<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <FrameLayout
            android:id="@+id/text_msg_parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_text_msg_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:lineSpacingExtra="4dp"
                android:paddingLeft="15dp"
                android:paddingTop="18dp"
                android:paddingRight="15dp"
                android:paddingBottom="18dp"
                android:textColor="#d9000000"
                android:textColorLink="#D8000000"
                android:textIsSelectable="false"
                android:textSize="24sp" />

        </FrameLayout>

    </ScrollView>

</RelativeLayout>