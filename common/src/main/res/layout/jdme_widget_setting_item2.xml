<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:orientation="vertical"
    tools:parentTag="android.widget.LinearLayout">

    <LinearLayout
        android:id="@+id/layout_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_ripple_white_corner8"
        android:orientation="vertical"
        android:padding="16dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="12dp"
                android:scaleType="fitCenter"
                android:visibility="gone"
                tools:src="@drawable/jdme_ic_noti_punch"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/ll_tips"
                android:layout_toEndOf="@id/iv_icon"
                android:ellipsize="end"
                android:gravity="left"
                android:lines="1"
                android:textColor="#333333"
                android:textSize="16dp"
                tools:text="功能名称" />

            <LinearLayout
                android:id="@+id/ll_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#999999"
                    android:textSize="14dp"
                    android:maxEms="8"
                    android:singleLine="true"
                    tools:text="状态展示" />

                <ImageView
                    android:id="@+id/iv_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/jdme_icon_arrow_right_gray" />
            </LinearLayout>

            <com.jd.oa.badge.RedDotView
                android:id="@+id/badge"
                android:layout_width="4dp"
                android:layout_height="4dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="10dp"
                tools:visibility="visible" />

            <ProgressBar
                android:id="@+id/pb_loading"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="16dp"
                android:visibility="gone"
                tools:visibility="visible" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textColor="#999999"
            android:textSize="14dp"
            tools:text="功能描述文字，功能描述文字" />

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="16dp"
            android:background="#E6E6E6" />
    </FrameLayout>
</merge>