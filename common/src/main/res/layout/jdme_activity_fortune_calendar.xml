<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white">
    <View
        android:id="@+id/view_reveal"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:background="@drawable/jdme_circle_reveal"></View>
    <ImageView
        android:id="@+id/iv_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/jdme_img_cal_bkg"
        android:visibility="invisible"
        tools:visibility="visible" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp">
        <LinearLayout
            android:id="@+id/layout_content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="46dp"
            android:orientation="vertical"
            android:visibility="invisible"
            tools:visibility="visible">
            <RelativeLayout
                android:id="@+id/layout_content_top"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingBottom="12dp"
                android:background="@drawable/jdme_img_cal_bkg_top">
                <com.jd.oa.ui.QMUIVerticalTextView
                    android:id="@+id/tv_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginStart="20dp"
                    android:textSize="12sp"
                    android:textColor="#000"
                    tools:text="2017.12.28 周一" />
                <View
                    android:id="@+id/view_date_line"
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="4dp"
                    android:layout_marginLeft="4dp"
                    android:layout_alignTop="@id/tv_date"
                    android:layout_alignBottom="@id/tv_date"
                    android:layout_toEndOf="@id/tv_date"
                    android:layout_toRightOf="@id/tv_date"
                    android:background="#000" />
                <LinearLayout
                    android:id="@+id/layout_lunar_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginLeft="4dp"
                    android:layout_alignTop="@id/view_date_line"
                    android:layout_toEndOf="@id/view_date_line"
                    android:layout_toRightOf="@id/view_date_line"
                    android:orientation="horizontal">
                    <com.jd.oa.ui.QMUIVerticalTextView
                        android:id="@+id/tv_lunar_date2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="#000"
                        android:visibility="gone"
                        tools:text="第二个" />
                    <com.jd.oa.ui.QMUIVerticalTextView
                        android:id="@+id/tv_lunar_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="#000"
                        tools:text="十月二十七日 丁酉年 鸡年 壬子月 壬申时" />
                </LinearLayout>
                <View
                    android:id="@+id/view_lunar_date_line"
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="4dp"
                    android:layout_marginLeft="4dp"
                    android:layout_alignTop="@id/layout_lunar_date"
                    android:layout_alignBottom="@id/layout_lunar_date"
                    android:layout_toEndOf="@id/layout_lunar_date"
                    android:layout_toRightOf="@id/layout_lunar_date"
                    android:background="#000" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:layout_marginRight="8dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentRight="true"
                    android:src="@drawable/jdme_img_cal_cloud"
                    android:layout_marginEnd="8dp" />
                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_toRightOf="@id/view_lunar_date_line"
                    android:layout_toEndOf="@id/view_lunar_date_line"
                    android:layout_alignParentRight="true"
                    android:layout_alignParentEnd="true">
                    <TextView
                        android:id="@+id/tv_day_of_month"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:textColor="#000"
                        android:textSize="120sp"
                        android:typeface="serif"
                        android:fontFamily="serif"
                        tools:text="14" />
                    <TextView
                        android:id="@+id/tv_taboo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_alignParentBottom="true"
                        android:textColor="#000"
                        android:textSize="24sp"
                        tools:text="不宜不宜" />
                </RelativeLayout>
            </RelativeLayout>
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:orientation="horizontal">
                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/jdme_img_cal_bkg_mid" />
                <View
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:layerType="software"
                    android:background="@drawable/jdme_dash_line" />
            </FrameLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/jdme_img_cal_bkg_bottom">
                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_begin="20dp" />
                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_end="20dp" />
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toRightOf="@id/guide_left"
                    app:layout_constraintRight_toRightOf="@id/guide_right"
                    app:layout_constraintBottom_toTopOf="@+id/tv_author"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp">
                    <com.jd.oa.ui.widget.JustifyTextView
                        android:id="@+id/tv_precept"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:layout_gravity="center"
                        android:textSize="20sp"
                        android:textColor="#c0a141"
                        tools:text="遗忘就像一扇闸门，通过这道闸门我们排出关于过去的繁重而讨厌的思绪。" />
                </FrameLayout>
                <TextView
                    android:id="@+id/tv_author"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginStart="24dp"
                    android:layout_marginLeft="24dp"
                    android:layout_marginBottom="16dp"
                    android:textSize="12sp"
                    tools:text="米哈依儿 肖洛霍夫" />
                <TextView
                    android:id="@+id/tv_source"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginRight="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="16dp"
                    android:textSize="12sp"
                    tools:text="《静静的顿河》" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
        <FrameLayout
            android:id="@+id/layout_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/jdme_img_point_circle"
            android:visibility="invisible"
            tools:visibility="visible">
            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/jdme_icon_cal_close" />
        </FrameLayout>
    </LinearLayout>
    <ProgressBar
        android:id="@+id/pb_progress"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />
</FrameLayout>