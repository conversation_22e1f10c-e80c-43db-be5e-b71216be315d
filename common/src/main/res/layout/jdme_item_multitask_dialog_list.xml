<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingStart="16dp">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginVertical="12dp"
        android:layout_centerVertical="true"
        tools:ignore="ContentDescription"
        tools:src="@drawable/jdme_flow_ht" />


    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_centerVertical="true"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_toStartOf="@id/iv_delete"
        android:layout_toEndOf="@id/iv_icon"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#FF232930"
                android:textSize="14dp"
                android:textStyle="bold"
                tools:text="京东互通" />

            <TextView
                android:id="@+id/tv_ht_tag"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/jdme_bg_shape_multitask_ht_tag"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="8dp"
                android:text="@string/me_multitask_clear_when_down"
                android:textColor="#1869F5"
                android:textSize="12dp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#FF999999"
            android:textSize="11dp"
            tools:text="周一 18:00 由宋瑞进发送"
            tools:visibility="gone" />
    </LinearLayout>


    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_delete"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="9dp"
        android:gravity="center"
        android:text="@string/icon_prompt_close"
        android:textColor="#CDCDCD"
        android:textSize="@dimen/JMEIcon_14"/>

</RelativeLayout>