<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingHorizontal="5dp"
    android:paddingVertical="9dp">

    <ImageView
        android:id="@+id/menu_icon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:scaleType="fitCenter"
        android:src="@drawable/prompt_circle"/>

    <TextView
        android:id="@+id/menu_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_toRightOf="@id/menu_icon"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#1b1b1b"
        android:textSize="14dp" />

</RelativeLayout>