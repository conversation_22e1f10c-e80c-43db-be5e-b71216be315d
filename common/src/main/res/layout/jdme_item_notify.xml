<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#fff6c1">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:src="@drawable/jdme_app_icon_notify_speaker" />

        <TextView
            android:id="@+id/tv_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/iv_close"
            android:layout_toRightOf="@+id/iv_speaker"
            android:ellipsize="marquee"
            android:padding="5dp"
            android:singleLine="true"
            android:text="Hello World!"
            android:textColor="#f29e0c"
            android:textSize="@dimen/me_text_size_14" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingRight="10dp"
            android:paddingEnd="10dp"
            android:src="@drawable/jdme_app_icon_notify_close" />
    </RelativeLayout>


</LinearLayout>
