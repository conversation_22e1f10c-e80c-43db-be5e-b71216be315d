<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/view_top_msg_parent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/chat_info_banner_container"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:layout_marginTop="26dp"
        android:layout_marginBottom="10dp"
        android:layout_marginHorizontal="15.5dp"
        app:cardElevation="4dp"
        app:cardMaxElevation="4dp"
        android:translationZ="5dp"
        app:cardCornerRadius="10dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/chat_info_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.jd.oa.elliptical.SuperEllipticalImageView
                android:id="@+id/banner_avatar"
                android:src="@drawable/vf_avatar_default"
                android:layout_marginStart="12dp"
                android:layout_marginVertical="7dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:id="@+id/banner_name"
                tool:text="周大白"
                android:textSize="14sp"
                android:textColor="#1B1B1B"
                android:layout_marginStart="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:maxEms="15"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="@id/banner_avatar"
                app:layout_constraintLeft_toRightOf="@id/banner_avatar"/>

            <TextView
                android:id="@+id/banner_detail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:layout_marginStart="10dp"
                android:maxEms="15"
                android:textColor="#6A6A6A"
                android:singleLine="true"
                tool:text="好的，我这边要确认一下具体内容，下午给你答复可以吗？"
                app:layout_constraintLeft_toRightOf="@id/banner_avatar"
                app:layout_constraintTop_toBottomOf="@id/banner_name" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/chat_notification"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:layout_marginVertical="16dp"
            android:layout_marginHorizontal="12dp"
            android:text="@string/you_have_received_one_message"
            android:textStyle="bold"
            android:singleLine="true"
            android:visibility="gone"
            android:textColor="#1B1B1B"
            android:textSize="14sp" />

        <LinearLayout
            android:id="@+id/chat_notification_plus"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/chat_notification_plus_name"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:layout_marginVertical="16dp"
                android:layout_marginStart="12dp"
                tool:text="周大白"
                android:textStyle="bold"
                android:singleLine="true"
                android:textColor="#1B1B1B"
                android:maxEms="13"
                android:textSize="14sp" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:layout_marginVertical="16dp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="12dp"
                android:text="@string/sent_a_message_to_you"
                android:textStyle="bold"
                android:singleLine="true"
                android:textColor="#1B1B1B"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/button_settings"
        android:layout_width="wrap_content"
        android:layout_height="58dp"
        android:layout_marginTop="26dp"
        android:layout_marginBottom="10dp"
        android:layout_gravity="end"
        android:layout_marginHorizontal="15.5dp"
        app:cardElevation="4dp"
        app:cardMaxElevation="4dp"
        android:translationZ="4dp"
        android:background="#FFFFFF"
        app:cardCornerRadius="10dp"
        android:alpha="0">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingVertical="16dp"
            android:paddingHorizontal="12dp"
            android:layout_marginHorizontal="15.5dp"
            android:text="@string/turn_off_notification"
            android:textColor="#1B1B1B"
            android:textSize="14sp"
            android:alpha="1"/>
    </androidx.cardview.widget.CardView>
</FrameLayout>