<?xml version="1.0" encoding="utf-8"?>
<!-- 加载中布局 -->
<LinearLayout
    android:id="@+id/loading_view"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical"
    android:visibility="visible">

    <ProgressBar
        android:id="@+id/progress"
        style="?android:attr/progressBarStyleInverse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/load_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dip"
        android:text="@string/me_loading"
        android:textColor="@color/black_main_summary"
        android:textSize="14sp"/>

</LinearLayout>