<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="160dp"
    android:layout_height="160dp">

    <ImageView
        android:id="@+id/action_view_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom|right"
        android:scaleType="fitXY"
        android:src="@drawable/me_float_action_bg_blue"
        android:visibility="gone"
        tools:background="#20CC0000" />

    <ImageView
        android:id="@+id/action_view_warning"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom|right"
        android:scaleType="fitXY"
        android:src="@drawable/me_float_action_bg_red"
        android:visibility="gone"
        tools:background="#20CC0000" />

    <ImageView
        android:id="@+id/action_view_normal"
        android:layout_width="136dp"
        android:layout_height="136dp"
        android:layout_gravity="bottom|right"
        android:scaleType="fitXY"
        android:src="@drawable/me_float_action_bg_gray"
        tools:background="#20000000" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginLeft="18dp"
        android:layout_marginBottom="24dp"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_marginRight="6dp"
                android:src="@drawable/me_float_action_point" />

            <ImageView
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:src="@drawable/me_float_action_point" />
        </LinearLayout>

        <TextView
            android:id="@+id/action_view_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/me_float_add"
            android:textColor="@android:color/white"
            android:textSize="10dp"
            tools:text="该应用不支持移入收起" />

    </LinearLayout>


</FrameLayout>