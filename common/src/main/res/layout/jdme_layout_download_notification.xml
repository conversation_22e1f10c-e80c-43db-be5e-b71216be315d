<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:id="@+id/layout_notification"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="64dp"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    android:paddingStart="12dp"
    android:paddingEnd="12dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/jdme_app_icon"/>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginStart="10dp"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/tv_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:layout_constraintLeft_toRightOf="@id/iv_logo"
                app:layout_constraintRight_toLeftOf="@+id/tv_size"
                app:layout_constraintTop_toTopOf="@id/iv_logo"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="@dimen/comm_text_secondary"
                android:textColor="#848484"
                tools:text="JDME_5.4.2.apk"/>
            <TextView
                android:id="@+id/tv_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="@id/tv_name"
                app:layout_constraintRight_toRightOf="parent"
                android:textSize="@dimen/comm_text_secondary"
                android:textColor="#848484"
                tools:text="88mb"/>
        </LinearLayout>

        <ProgressBar
            android:id="@+id/pb_progress"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="1.5dp"
            app:layout_constraintLeft_toLeftOf="@id/tv_name"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            app:layout_constraintBottom_toBottomOf="@id/iv_logo"
            android:layout_marginTop="1dp"
            android:layout_marginBottom="1dp"
            android:progressDrawable="@drawable/jdme_progress_download"
            android:max="100"
            tools:progress="50"/>
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp">
            <TextView
                android:id="@+id/tv_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|center_vertical"
                android:textSize="@dimen/comm_text_xsmall"
                android:textColor="#848484"
                android:text="@string/me_update_preparing_download" />
            <TextView
                android:id="@+id/tv_percent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:textSize="@dimen/comm_text_xsmall"
                android:textColor="#4C94EA"
                android:text="0%"
                tools:text="80%"/>
        </FrameLayout>
    </LinearLayout>
</LinearLayout>