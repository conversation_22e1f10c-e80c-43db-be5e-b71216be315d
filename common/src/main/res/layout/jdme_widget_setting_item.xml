<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="android.widget.LinearLayout"
    tools:orientation="vertical">
    <RelativeLayout
        android:id="@+id/layout_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:background="@drawable/jdme_ripple_white">
        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentStart="true"
            android:layout_marginStart="12dp"
            android:layout_centerVertical="true"
            android:scaleType="fitCenter"
            android:visibility="gone"
            tools:src="@drawable/jdme_ic_noti_punch"
            tools:visibility="visible"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="12dp"
            android:layout_toRightOf="@id/iv_icon"
            android:layout_toLeftOf="@id/tv_badge"
            android:gravity="left"
            android:textColor="#2e2d2d"
            android:textSize="16sp"
            tools:text="功能名称" />
        <TextView
            android:id="@+id/tv_badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/ll_tips"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:textSize="16sp"
            android:textColor="#EF5450"
            tools:text="new"/>
        <LinearLayout
            android:id="@+id/ll_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="#848484"
                tools:text="状态展示"/>
            <ImageView
                android:id="@+id/iv_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:src="@drawable/jdme_icon_arrow_right_gray"/>
        </LinearLayout>
        <ProgressBar
            android:id="@+id/pb_loading"
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:visibility="gone"/>
    </RelativeLayout>
    <TextView
        android:id="@+id/tv_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        tools:text="功能描述文字，功能描述问文字"/>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">
        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="12dp"
            android:background="#eef1f4" />
    </FrameLayout>
</merge>