<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingVertical="12dp"
        android:paddingEnd="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/fl_cb_selected"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:paddingStart="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <CheckBox
                android:id="@+id/cb_selected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@drawable/jdme_ripple_white"
                android:button="@null"
                android:drawableStart="@drawable/jdme_file_selector_checkbox"
                android:drawablePadding="13dp" />
        </FrameLayout>

        <ImageView
            android:id="@+id/iv_file_icon"
            android:layout_width="37dp"
            android:layout_height="42dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/fl_cb_selected"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_file_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:ellipsize="middle"
            android:lines="1"
            android:textColor="#1b1b1b"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_file_icon"
            app:layout_constraintTop_toTopOf="@id/iv_file_icon"
            tools:text="测试的文件测试的文件测试的文件测试的文件测试的文件测试的文件测试的文件.txt" />

        <TextView
            android:id="@+id/tv_file_size"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#9d9d9d"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_file_icon"
            app:layout_constraintEnd_toStartOf="@id/ll_file_tip"
            app:layout_constraintStart_toEndOf="@id/iv_file_icon"
            tools:text="1.2M2M2M2M2M2M2M2M2M2M2M2M2M2M" />

        <LinearLayout
            android:id="@+id/ll_file_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="6dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_file_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_goneMarginStart="0dp">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:text="@string/icon_general_information"
                android:textColor="#6a6a6a"
                android:textSize="@dimen/JMEIcon_12" />

            <TextView
                android:id="@+id/tv_file_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/jdme_progress_attendance"
                android:ellipsize="middle"
                android:lines="1"
                android:textColor="#6a6a6a"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@id/iv_file_icon"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="该文件类型不支持" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_mask"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0A000000"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
