<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rl_bottom"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="16dp"
            android:background="#F5F5F5" />

        <TextView
            android:id="@+id/tv_contributors"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="贡献人" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_contributors"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/tv_contributors"
            android:orientation="horizontal" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_like"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="2dp"
                android:gravity="bottom"
                android:text="@string/icon_general_like"
                android:textColor="@color/black"
                android:textSize="@dimen/JMEIcon_18"
                tools:textSize="@dimen/JMEIcon_18" />

            <TextView
                android:id="@+id/tv_like_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="start|bottom"
                android:minWidth="20dp"
                android:textSize="14dp"
                tools:text="111" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_unlike"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="20dp"
                android:gravity="bottom"
                android:text="@string/icon_general_like1"
                android:textColor="@color/black"
                android:textSize="@dimen/JMEIcon_18"/>

            <!--   <TextView
                   android:id="@+id/tv_unlike_count"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:layout_gravity="center_vertical"
                   android:layout_marginEnd="20dp"
                   tools:text="111" />-->

        </LinearLayout>

    </RelativeLayout>
</RelativeLayout>