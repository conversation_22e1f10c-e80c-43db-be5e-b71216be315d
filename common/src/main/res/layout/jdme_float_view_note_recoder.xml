<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_note_bg_audio_record"
    app:cardCornerRadius="20dp"
    app:cardElevation="6dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true">

    <LinearLayout
        android:layout_width="129dp"
        android:layout_height="40dp"
        android:background="@drawable/jdme_note_bg_audio_record"
        android:gravity="center">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/exception_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/icon_padding_infocirclecircle"
            android:textColor="#FBB731"
            android:visibility="gone"
            android:textSize="@dimen/JMEIcon_24" />

        <LinearLayout
            android:id="@+id/voice_wave_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.jd.oa.ui.voice.VoiceWaveView
                android:id="@+id/float_wave1"
                android:layout_width="4dp"
                android:layout_height="30dp"
                android:gravity="center"
                app:duration="310"
                app:waveLineColor="#4C7CFF"
                app:waveLineWidth="2dp"
                app:waveMode="up_down" />

            <com.jd.oa.ui.voice.VoiceWaveView
                android:id="@+id/float_wave2"
                android:layout_width="4dp"
                android:layout_height="30dp"
                android:gravity="center"
                app:duration="300"
                app:waveLineColor="#4C7CFF"
                app:waveLineWidth="2dp"
                app:waveMode="up_down" />

            <com.jd.oa.ui.voice.VoiceWaveView
                android:id="@+id/float_wave3"
                android:layout_width="4dp"
                android:layout_height="30dp"
                android:gravity="center"
                app:duration="320"
                app:waveLineColor="#4C7CFF"
                app:waveLineWidth="2dp"
                app:waveMode="up_down" />

            <com.jd.oa.ui.voice.VoiceWaveView
                android:id="@+id/float_wave4"
                android:layout_width="4dp"
                android:layout_height="30dp"
                android:gravity="center"
                app:duration="290"
                app:waveLineColor="#4C7CFF"
                app:waveLineWidth="2dp"
                app:waveMode="up_down" />

            <com.jd.oa.ui.voice.VoiceWaveView
                android:id="@+id/float_wave5"
                android:layout_width="4dp"
                android:layout_height="30dp"
                android:gravity="center"
                app:duration="330"
                app:waveLineColor="#4C7CFF"
                app:waveLineWidth="2dp"
                app:waveMode="up_down" />

            <com.jd.oa.ui.voice.VoiceWaveView
                android:id="@+id/float_wave6"
                android:layout_width="4dp"
                android:layout_height="30dp"
                android:gravity="center"
                app:duration="290"
                app:waveLineColor="#4C7CFF"
                app:waveLineWidth="2dp"
                app:waveMode="up_down" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/color_666666"
            android:textSize="12dp"
            tools:text="00:00:08" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iftv_control"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="6dp"
            android:background="@drawable/jdme_note_bg_audio_paused"
            android:gravity="center"
            android:text="@string/icon_edit_voice3"
            android:textColor="@color/white"
            android:textSize="@dimen/JMEIcon_16"
            tools:ignore="SpUsage"
            tools:text="@string/icon_float_pause" />

    </LinearLayout>
</androidx.cardview.widget.CardView>