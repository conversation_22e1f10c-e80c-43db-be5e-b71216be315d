<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="2dp"
    android:layout_marginEnd="2dp"
    android:layout_marginTop="1dp"
    android:layout_marginBottom="3dp"
    android:background="@drawable/jdme_exp_skin_card_shadow">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="150dp">
                <ImageView
                    android:id="@+id/skin_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    tools:ignore="ContentDescription" />
                <TextView
                    android:id="@+id/skin_theme_icon"
                    android:layout_width="64dp"
                    android:layout_height="20dp"
                    android:layout_gravity="bottom|end"
                    android:background="@drawable/jdme_skin_global_label"
                    android:gravity="center"
                    android:text="@string/exp_theme_global"
                    android:textColor="@color/white"
                    android:textSize="12dp"
                    android:visibility="gone"
                    tools:ignore="ContentDescription,SpUsage"
                    tools:visibility="visible" />
            </FrameLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="63dp"
                android:orientation="horizontal"
                android:background="#FFFFFF"
                android:baselineAligned="false">
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights">
                    <TextView
                        android:id="@+id/skin_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="7dp"
                        android:textSize="16dp"
                        android:textColor="#333333"
                        android:textStyle="bold"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textAlignment="center"
                        android:includeFontPadding="false"/>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <FrameLayout
                            android:id="@+id/skin_label"
                            android:layout_width="wrap_content"
                            android:layout_height="18dp"
                            android:layout_marginEnd="8dp"
                            android:background="@drawable/jdme_exp_skin_label_bg"
                            android:visibility="gone">
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="5dp"
                                android:layout_marginEnd="5dp"
                                android:textSize="12dp"
                                android:textColor="#FE3E33"
                                android:text="@string/exp_theme_limited_use"
                                android:includeFontPadding="false"/>
                        </FrameLayout>
                        <TextView
                            android:id="@+id/skin_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:textSize="12dp"
                            android:textColor="#999999"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:includeFontPadding="false"/>
                    </LinearLayout>
                </LinearLayout>
                <FrameLayout
                    android:id="@+id/skin_button"
                    android:layout_width="78dp"
                    android:layout_height="27dp"
                    android:layout_marginEnd="16dp"
                    android:layout_gravity="center_vertical">
                    <TextView
                        android:id="@+id/skin_button_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:textSize="14dp"
                        android:textColor="#FE3E33"
                        android:textAlignment="center"
                        android:includeFontPadding="false"
                        android:background="@drawable/shape_bg_exp_skin_button"
                        android:enabled="false"/>
                    <ProgressBar
                        android:id="@+id/skin_button_loading"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingTop="6dp"
                        android:paddingBottom="6dp"
                        android:gravity="center"
                        android:background="@drawable/shape_bg_exp_skin_loading"
                        android:indeterminate="true"
                        android:indeterminateDrawable="@drawable/jdme_exp_skin_button_loading"/>
                </FrameLayout>
            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/skin_new"
            android:layout_width="47dp"
            android:layout_height="20dp"
            android:src="@drawable/jdme_skin_new_icon"
            android:visibility="gone"/>

        <com.jd.oa.ui.LightAnimView
            android:id="@+id/skin_new_anim"
            android:layout_width="47dp"
            android:layout_height="20dp"
            app:tl="12dp"
            app:br="7.5dp"
            android:visibility="gone"/>

        <View
            android:id="@+id/skin_select"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:background="@drawable/jdme_exp_skin_select_bg"/>
    </androidx.cardview.widget.CardView>
</FrameLayout>