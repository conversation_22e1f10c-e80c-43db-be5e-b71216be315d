<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_top"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:layout_alignParentTop="true"
    android:alpha="0"
    android:background="@color/white"
    tools:alpha="1">

    <View
        android:layout_width="50dp"
        android:layout_height="4dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="6dp"
        android:background="@drawable/jdme_shape_drawer_handle" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_back"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:text="@string/icon_direction_left"
        android:textSize="@dimen/JMEIcon_18"        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:layout_toEndOf="@id/tv_back"
        android:textColor="#333333"
        android:textSize="20dp"
        android:textStyle="bold"
        tools:text="词条标题" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_more"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="16dp"
        android:background="@drawable/jdme_selector_common_ripple_effect"
        android:gravity="center"
        android:text="@string/icon_tabbar_more"
        android:textColor="#333"
        android:textSize="@dimen/JMEIcon_18"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="16dp"
        android:background="#F5F5F5" />

</RelativeLayout>