<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="60dp">

    <TextView
        android:id="@+id/footer_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/libui_default_text_color"
        android:text="@string/libui_footer_normal"
        android:layout_centerInParent="true" />

    <ProgressBar
        android:id="@+id/footer_progress"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:scaleType="fitCenter"
        android:indeterminateTint="@color/libui_header_progress_default_color"
        android:indeterminateTintMode="src_atop"
        android:layout_marginRight="10dp"
        android:layout_centerInParent="true"
        android:layout_toLeftOf="@id/footer_text" />

</RelativeLayout>