<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/preview_card"
        android:layout_width="280dp"
        android:layout_height="544dp"
        android:layout_centerInParent="true"
        app:cardBackgroundColor="@color/translucent"
        app:cardCornerRadius="24dp"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/preview_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            tools:ignore="ContentDescription" />
    </androidx.cardview.widget.CardView>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@id/preview_card"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="126dp">

        <FrameLayout
            android:id="@+id/cancel_button"
            android:layout_width="100dp"
            android:layout_height="34dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="8dp">

            <TextView
                android:id="@+id/cancel_button_text"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_bg_exp_skin_button"
                android:enabled="false"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/exp_theme_preview_cancel"
                android:textAlignment="center"
                android:textColor="#FE3E33"
                android:textSize="14dp"
                tools:ignore="SpUsage" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/use_button"
            android:layout_width="100dp"
            android:layout_height="34dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp">

            <TextView
                android:id="@+id/use_button_text"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_bg_exp_skin_button_red"
                android:enabled="false"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/exp_theme_preview_use"
                android:textAlignment="center"
                android:textColor="#FFFFFF"
                android:textSize="14dp"
                tools:ignore="SpUsage" />

            <ProgressBar
                android:id="@+id/use_button_loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_bg_exp_skin_loading"
                android:gravity="center"
                android:indeterminate="true"
                android:indeterminateDrawable="@drawable/jdme_exp_skin_button_loading_white"
                android:paddingTop="6dp"
                android:paddingBottom="6dp"
                android:visibility="gone" />
        </FrameLayout>
    </LinearLayout>
</RelativeLayout>