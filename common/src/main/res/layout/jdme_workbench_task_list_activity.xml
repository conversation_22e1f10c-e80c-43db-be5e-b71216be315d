<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/option_item_trigger"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <TextView
            android:id="@+id/option_item_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/jdme_workbench_ic_task_filter_arrow_down"
            android:drawablePadding="4dp"
            android:text=""
            android:textColor="#2E2D2D"
            android:textSize="14sp" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/vp_task"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/task_list_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <FrameLayout
            android:id="@+id/filter_items_list_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>
</LinearLayout>