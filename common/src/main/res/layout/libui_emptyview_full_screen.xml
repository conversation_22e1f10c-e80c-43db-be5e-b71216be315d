<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_nodata_layout"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/comm_background"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingBottom="87dp"
    android:paddingTop="87dp">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:background="@color/comm_background"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/libui_emptyview_iv"
            android:layout_width="135dp"
            android:layout_height="135dp"
            android:background="@drawable/libui_empty_image"
            android:visibility="visible" />

        <TextView
            android:id="@+id/libui_emptyview_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textColor="@color/comm_text_nor"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/libui_emptyview_tips_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:textColor="@color/libui_emptyview_text_light"
            android:textSize="16dp" />

        <Button
            android:id="@+id/libui_emptyview_go_find"
            android:layout_width="105dp"
            android:layout_height="41dp"
            android:layout_marginTop="52dp"
            android:background="@drawable/libui_go_find"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>