<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/abc_action_bar_default_height">

        <com.jd.oa.ui.MyProgressWebView2
            android:id="@+id/web_view_process"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true" />

        <LinearLayout
            android:id="@+id/layout_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            tools:visibility="visible">

            <pl.droidsonroids.gif.GifImageView
                android:layout_width="125dp"
                android:layout_height="125dp"
                android:scaleType="fitCenter"
                android:src="@drawable/jdme_web_loading" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_failed"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/me_web_loading_failed"
                android:textColor="@color/comm_text_normal"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_failed" />

            <TextView
                android:id="@+id/tv_diagnosis"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="36dp"
                android:background="@drawable/jdme_diagnosis_btn_stroke"
                android:paddingStart="32dp"
                android:paddingTop="9dp"
                android:paddingEnd="32dp"
                android:paddingBottom="9dp"
                android:text="@string/me_setting_net_diagnosis"
                android:textColor="@color/color_232930"
                android:textSize="16sp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_error"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="48dp"
                android:text="@string/me_web_check_network_and_proxy"
                android:textColor="@color/comm_text_normal"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <pl.droidsonroids.gif.GifImageView
                android:id="@+id/iv_failed"
                android:layout_width="125dp"
                android:layout_height="125dp"
                android:layout_marginBottom="55dp"
                android:scaleType="fitCenter"
                android:src="@drawable/jdme_web_failed"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <com.jd.oa.ui.h5.H5TitleBar
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</FrameLayout>


