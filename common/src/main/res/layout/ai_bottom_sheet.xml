<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/status_bar_holder"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_bar_holder" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="22dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="@string/icon_prompt_close"
        android:textColor="@color/black"
        android:textSize="@dimen/JMEIcon_20"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guide_line" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="18dp"
        android:text="@string/icon_tabbar_more"
        android:visibility="gone"
        android:textColor="@color/black"
        android:textSize="@dimen/JMEIcon_20"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guide_line" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_expand"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="18dp"
        android:text="@string/icon_general_anobliquearrow"
        android:visibility="gone"
        android:textColor="@color/black"
        android:textSize="@dimen/JMEIcon_20"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/tv_menu"
        app:layout_constraintTop_toBottomOf="@id/guide_line" />

</androidx.constraintlayout.widget.ConstraintLayout>