<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="10dp">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:scrollbars="vertical">

        <LinearLayout
            android:id="@+id/ll_info_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical" />
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_option_offline_info"
            style="@style/my_button_default"
            android:layout_width="0dp"
            android:layout_height="44dip"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="5dp"
            android:layout_weight="1"
            android:text="重新获取信息"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_option_drop_all"
            style="@style/my_button_default"
            android:layout_width="0dp"
            android:layout_height="44dip"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:layout_weight="1"
            android:text="删除全部数据"
            android:textSize="17sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_option_log"
        style="@style/my_button_default"
        android:layout_width="wrap_content"
        android:layout_height="44dip"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:text="查看日志"
        android:textSize="17sp" />

</LinearLayout>