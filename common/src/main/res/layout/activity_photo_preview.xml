<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context="com.jd.oa.PhotoPreviewActivity">


    <!--    <com.jingdong.common.ui.PhotoView-->
    <!--        android:id="@+id/pv"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_centerInParent="true"-->
    <!--        android:scaleType="fitCenter"-->
    <!--        tools:src="@drawable/libui_banner_no_banner" />-->

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <TextView
        android:id="@+id/tv_page"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/jdme_bg_gray_corner"
        android:backgroundTint="#FF62656D"
        android:gravity="center"
        android:paddingHorizontal="4dp"
        android:textColor="@color/white"
        android:textSize="14dp"
        tools:text="1/2" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_download"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:background="@drawable/jdme_bg_gray_corner"
        android:backgroundTint="#FF62656D"
        android:gravity="center"
        android:text="@string/icon_direction_download"
        android:textColor="@color/white"
        android:textSize="@dimen/JMEIcon_18"/>

</RelativeLayout>