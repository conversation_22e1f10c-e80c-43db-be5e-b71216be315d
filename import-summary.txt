ECLIPSE ANDROID PROJECT IMPORT SUMMARY
======================================

Manifest Merging:
-----------------
Your project uses libraries that provide manifests, and your Eclipse
project did not explicitly turn on manifest merging. In Android Gradle
projects, manifests are always merged (meaning that contents from your
libraries' manifests will be merged into the app manifest. If you had
manually copied contents from library manifests into your app manifest
you may need to remove these for the app to build correctly.

Ignored Files:
--------------
The following files were *not* copied into the new Gradle project; you
should evaluate whether these are still needed in your project and if
so manually move them:

* proguard-project.txt
* test\
* test\db\
* test\db\DBTest.java
* test\db\DeviceTest.java
* test\db\HttpTest.java
* test\db\JsonTest.java
* test\encrypt\
* test\encrypt\EncryTest.java
* test\encrypt\ThemeTest.java
* test\json\
* test\json\JsonTest.java
* test\json\TestResponseJson.java
* test\sys\
* test\sys\DeviceTest.java
* test\sys\GetMemoryInfo.java

Replaced Jars with Dependencies:
--------------------------------
The importer recognized the following .jar files as third party
libraries and replaced them with Gradle dependencies instead. This has
the advantage that more explicit version information is known, and the
libraries can be updated automatically. However, it is possible that
the .jar file in your project was of an older version than the
dependency we picked, which could render the project not compileable.
You can disable the jar replacement in the import wizard and try again:

gson-2.2.4.jar => com.google.code.gson:gson:2.2.4
android-support-v4.jar => com.android.support:support-v4:21.0.3
android-support-v7-appcompat.jar => com.android.support:appcompat-v7:21.0.3
gson-2.2.4.jar => com.google.code.gson:gson:2.2.4

Potentially Missing Dependency:
-------------------------------
When we replaced the following .jar files with a Gradle dependency, we
inferred the dependency version number from the filename. This
specific version may not actually be available from the repository.
If you get a build error stating that the dependency is missing, edit
the version number to for example "+" to pick up the latest version
instead. (This may require you to update your code if the library APIs
have changed.)

gson-2.2.4.jar => version 2.2.4 in com.google.code.gson:gson:2.2.4
gson-2.2.4.jar => version 2.2.4 in com.google.code.gson:gson:2.2.4

Replaced Libraries with Dependencies:
-------------------------------------
The importer recognized the following library projects as third party
libraries and replaced them with Gradle dependencies instead. This has
the advantage that more explicit version information is known, and the
libraries can be updated automatically. However, it is possible that
the source files in your project were of an older version than the
dependency we picked, which could render the project not compileable.
You can disable the library replacement in the import wizard and try
again:

android-support-v7-appcompat => [com.android.support:appcompat-v7:21.0.3]

Moved Files:
------------
Android Gradle projects use a different directory structure than ADT
Eclipse projects. Here's how the projects were restructured:

* D:\workspace\JDME\libs\gson-2.2.4.jar => app\libs\gson-2.2.4.jar
* D:\workspace\JDME\res\ => app\src\androidTest\res\
* D:\workspace\JDME\src\ => app\src\androidTest\java\
* AndroidManifest.xml => app\src\main\AndroidManifest.xml
* assets\ => app\src\main\assets\
* libs\armeabi-v7a\libjpush173.so => app\src\main\jniLibs\armeabi-v7a\libjpush173.so
* libs\armeabi\libjpush173.so => app\src\main\jniLibs\armeabi\libjpush173.so
* libs\greendao-1.3.7.jar => app\libs\greendao-1.3.7.jar
* libs\jaa_sdk.jar => app\libs\jaa_sdk.jar
* libs\jdmap.jar => app\libs\jdmap.jar
* libs\jpush-sdk-release1.7.3.jar => app\libs\jpush-sdk-release1.7.3.jar
* libs\nineoldandroids-2.4.0.jar => app\libs\nineoldandroids-2.4.0.jar
* libs\xUtils_2.6.14_update.jar => app\libs\xUtils_2.6.14_update.jar
* libs\zxing-core-3.1.1.jar => app\libs\zxing-core-3.1.1.jar
* res\ => app\src\main\res\
* src\ => app\src\main\java\
* src\com\jd\oa\readMe.txt => app\src\main\resources\com\jd\oa\readMe.txt

Next Steps:
-----------
You can now build the project. The Gradle project needs network
connectivity to download dependencies.

Bugs:
-----
If for some reason your project does not build, and you determine that
it is due to a bug or limitation of the Eclipse to Gradle importer,
please file a bug at http://b.android.com with category
Component-Tools.

(This import summary is for your information only, and can be deleted
after import once you are satisfied with the results.)
