# 京东ME Andriod

### 开发环境
1. IDE： Android Studio 2024+
2. 开发语言: Java 11/1.8(compatibility) / Kotlin 1.6.21
3. 构建工具: [Gradle: 7.3.3; Gradle Plugin for Android: 7.2](https://developer.android.google.cn/build/releases/past-releases/agp-7-2-0-release-notes?hl=en)
4. Target Version: 31
5. Compile Sdk Version: 33
6. Mini Sdk Version: 23

### 常用命令
```shell
# 清理
./gradlew clean
# 编译
./gradlew build --info
# 构建并生成apk
./gradlew assembleRelease
# 显示系统的代理配置信息
scutil --proxy
echo $http_proxy
echo $https_proxy
# 依赖分析
# 打印依赖到控制台
./gradlew :app:printAllModulesDependencies
# 生成 JSON 格式
./gradlew :app:printAllModulesDependenciesJson
# 生成 PlantUML 格式
./gradlew :app:printAllModulesDependenciesPlantUML
# 生成 Mermaid 格式  
./gradlew :app:printAllModulesDependenciesMermaid
# 从京东内部Git仓库拉取JSON5配置文件
./gradlew fetchProfileConfigFromTag -PtagName=1.1.1

```

### 常用文档
- [本项目GIT地址](http://xingyun.jd.com/codingRoot/jme/jdme_android_project/)
- [项目文档空间](https://joyspace.jd.com/teams/mOfQuX8EUYJD2kSayY_B/root)
- [GIT分支管理](https://joyspace.jd.com/file/hm16Y7Bbq2EV1jSqzgfb)
- [行云团队空间](http://jagile.jd.com/teamspace/scrum/_ME_/workitems/card?sprintId=145970)


### 自研子项目
- [跨平台的文件、文档等维护](http://xingyun.jd.com/codingRoot/jme/jme_public/)
- [京东ME身边模块](http://xingyun.jd.com/codingRoot/jme/around/)
- [鲸盘sdk](http://xingyun.jd.com/codingRoot/jme/netdisk/)
- 

### 上线
- 上线评审记录：https://joyspace.jd.com/teams/bnbfteZ18Jf0eykbw1tm/YMvcr112ADSS9zYKbuNS
- 

### 构建
- 打包流水线：http://jagile.jd.com/upipe/pipelines/637506/buildList/2221139


### 质量（数据分析&问题排查）
- Bugly ：[https://bugly.tds.qq.com/v2/workbench/apps](https://bugly.tds.qq.com/v2/workbench/apps)
  - 解密erp方案： [https://joyspace.jd.com/pages/dNr1f7kijT81yu8voKav](https://joyspace.jd.com/pages/dNr1f7kijT81yu8voKav)
- 京ME看板（其他的数据需要的测试写sql拉）：http://qa.ada.jd.com/#/analysisview/index?Y3R0eXBlPXZpZXcmYXBpcGFyYW10eXBlPXZpZXcmcmVwb3J0aWQ9Yjg2NWpNcGo0YU82YVVTejVlOWx1WEpxTEJjWFNGJnByb2plY3Rjb2RlPW9uZWRhdGFfcWE%3D
- Bug管理：http://jagile.jd.com/test-manage/jbug
- SGM查数：http://sgm-mobile.jd.com/homepage


### 跨平台/开放平台
- [ROMA | ROMA](https://roma-design.jd.com/docs/)
- 京ME消息卡片组件介绍 (https://joyspace.jd.com/pages/4gMljjVyhX8NEaIztKfW)
- JS SDK-Hybrid 接入文档（老的）：(https://joyspace.jd.com/pages/69Mb6hM6F7CqmXNfwU5f)
- JS SDK-Hybrid 接入文档（新的）(https://joyspace.jd.com/pages/7hU7dThkJL06OQGZ5RED)
- 京ME开发平台-应用查询 https://me.jd.com/openplatform/Administrator?SummaryList

### GIT提交信息规范（简版）
git commit 格式 如下：

`<type>(<scope>): <subject>`
各个部分的说明如下：

- type 类型，提交的类别

> feat: 新功能
> fix: 修复 bug
> docs: 文档变动
> style: 格式调整，对代码实际运行没有改动，例如添加空行、格式化等
> refactor: bug 修复和添加新功能之外的代码改动
> perf: 提升性能的改动
> test: 添加或修正测试代码
> chore: 构建过程或辅助工具和库（如文档生成）的更改

- scope 修改范围

主要是这次修改涉及到的部分，简单概括，例如 login、train-order

- subject 修改的描述

具体的修改描述信息

- 范例

feat(detail): 详情页修改样式
fix(login): 登录页面错误处理
test(list): 列表页添加测试代码

**这里对提交规范加几点说明：**

> type + scope 能够控制每笔提交改动的文件尽可能少且集中，避免一次很多文件改动或者多个改动合成一笔。
> subject 对于大部分国内项目而已，如果团队整体英文不是较高水平，比较推荐使用中文，方便阅读和检索。
> 避免重复的提交信息，如果发现上一笔提交没改完整，可以使用 git commit --amend 指令追加改动，尽量避免重复的提交信息。



## 其他

### DeepLink

[DeepLink文档](doc/DeepLink.md)

### 数据库升级

[GreenDao数据库](./daogenerator/README.md)

### 历史版本

[历史版本](doc/VERSIONS.md)