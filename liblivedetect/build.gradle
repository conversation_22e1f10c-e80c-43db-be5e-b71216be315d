apply plugin: 'com.android.library'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION
    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        multiDexEnabled true

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            jniLibs.srcDir 'src/main/libs'
        }
    }
    namespace 'ejoy.livedetector'
    lint {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation "androidx.constraintlayout:constraintlayout:$constraintlayoutVersion"

//    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'

    api libs.lib.permission
    api project(path: ':common')
}
