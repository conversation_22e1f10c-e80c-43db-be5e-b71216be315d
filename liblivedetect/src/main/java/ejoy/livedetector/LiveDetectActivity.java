package ejoy.livedetector;

import android.Manifest;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.hardware.Camera;
import android.hardware.SensorManager;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Base64;
import android.util.Log;
import android.view.OrientationEventListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.AccelerateInterpolator;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.JDMAUtils;

import org.json.JSONObject;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import ejoy.livedetector.camera.CameraInterface;
import ejoy.livedetector.camera.CameraSurfaceView;
import ejoy.livedetector.util.DetectResultUtil;
import ejoy.livedetector.util.DisplayUtil;
import ejoy.livedetector.util.EventUtil;
import ejoy.livedetector.util.FileUtil;
import ejoy.livedetector.util.LogUtil;

@SuppressWarnings({"JavaDoc", "unused", "unchecked", "SynchronizeOnNonFinalField", "NullableProblems", "ResultOfMethodCallIgnored", "UnnecessaryReturnStatement", "UnusedAssignment", "CStyleArrayDeclaration", "StatementWithEmptyBody", "FieldCanBeLocal"})
public abstract class LiveDetectActivity extends AppCompatActivity {

    static {
        System.loadLibrary("JniDetectInterfaces");
    }

    private String TAG = "LiveDetectActivity";
    private int mUploadModelImageNum = 5;
    private LinkedList<byte[]> mLiveDetectResult = new LinkedList<>();
    private byte[] mPreDetectResult;

    /**
     * 检测结果(超时返回FAILED)
     */
    public enum DetectStatus {
        SUCCESS, FAILED, NO_PERMISSION, CANCEL
    }

    public enum ActionType {

        LEFT_RIGHT(0, "左右摇头"),
        UP_DOWN(1, "上下摇头"),
        MOUTH(2, "张嘴"),
        BLINK(3, "眨眼");


        private int code;

        ActionType(int code, final String title) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }

    }

    /**
     * 检测回调
     */
    public interface DetectCallback {

        // 失败完成
        void onDetectFinish(DetectStatus result);

        // 打开相机失败
        void onOpenCameraFailed();

        void onResumeMethod();

        boolean isSplitMode(Configuration configuration);

        boolean isXiaomiTablet();

    }

    /**
     * 预检回调
     */
    public interface PrestartCallback {

//        void onPrestartSuccess(ejoy.livedetector.model.DetectResult var1);

        void onPrestartFail(int var1, int var2);
    }

    /**
     * 识别回调
     */
    public interface LivenessCallback {
        void onLivenessSuccess(List<byte[]> imgList);

        void onLivenessFail(int var1, int var2);
    }

    public static final String PARAM_SNAP = "snap";
    // 最大超时时间(秒)
    private static int MAX_DETECT_SECOND = 10;
    // 最小超时时间
    private static final int MIN_DETECT_SECOND = 2;
    // 最大检测次数
    private static final int MAX_REDETECT = 50;

    private static final int MAX_PRESTART = 10;

    //    private TextView mMask;
    CameraSurfaceView surfaceView = null;//摄像画面
    private int mSurfaceOrgWidth;
    private int mSurfaceOrgHeight;

    float previewRate = -1f;
    final private MainHandler mMainHandler = new MainHandler();

    private int mCameraId = Camera.CameraInfo.CAMERA_FACING_FRONT;
    private int mPreviewFormat = ImageFormat.NV21;

    String detectFilePath;
    String landMarkFilePath;

    // 识别回调
    private DetectCallback mDetectCallback;

    private PrestartCallback mPrestartCallback;

    private LivenessCallback mLivenessCallback;

    // 超时时间
    private int mTimeout;
    // 开始识别的时间
    private long mStartTime;

    private ActionType action;

    protected TextView mtip;
    protected TextView detectTip;
    //    private TextView prestartTip;
    //    private ImageView faceView;
//    private ScanFaceView mScanFaceView;
//    private ImageView mSound;
    private View cameraRing, detectLine, detectCover;

    volatile private Boolean isPrestartOk;

    public int x, y, w, h;
    public byte[] actArrays = {0, 0, 0, 1};
    int position = 0;
    int lastStep = -1;
    int failed = 0;
    boolean isOnPause = false;
    private HashMap<String, String> map = new HashMap<String, String>();
    private LinkedList<Object> picList = new LinkedList<>();
    public int degress = 90;

    String erp;
    String requestAction;
    protected String snap;

    private CameraInterface.CamOpenOverCallback camOpenOverCallback;

    OrientationEventListener mOrientationListener;
    int mOrientation = 0;

    Context mContext;

    ObjectAnimator animator;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = this;

        if (BuildConfig.DEBUG) {
            MAX_DETECT_SECOND = 100;
        }
        // 权限处理
        PermissionHelper.requestPermission(this, getResources().getString(R.string.me_request_permission_camera_liveness), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                initCmdMap(snap);//初始化后台配置的活体检测类型
            }

            @Override
            public void denied(List<String> deniedList) {
                detectFinish(DetectStatus.NO_PERMISSION, null);
            }
        }, Manifest.permission.CAMERA);


        erp = getIntent().getStringExtra("erp");
        requestAction = getIntent().getStringExtra("action");

        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);//竖屏
        //        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
        setContentView(R.layout.detect_activity_live_detect);

//        mMask = (TextView) findViewById(R.id.mask);
        surfaceView = (CameraSurfaceView) findViewById(R.id.camera_surface_view);//摄像画面
        surfaceView.setHandler(mMainHandler);

//        faceView = (ImageView) findViewById(R.id.face_view);
//        mScanFaceView = (ScanFaceView) findViewById(R.id.scan_face_view);


        mtip = (TextView) findViewById(R.id.tv_action_tip);
        detectTip = (TextView) findViewById(R.id.tv_detect_tip);
//        mSound = (ImageView) findViewById(R.id.iv_sound);
        cameraRing = findViewById(R.id.me_detect_camera_ring);
        detectLine = findViewById(R.id.me_detect_scan_line);
        detectCover = findViewById(R.id.me_detect_scan_cover);//摄像头准备前的默认遮挡画面/**/
        camOpenOverCallback = new CameraInterface.CamOpenOverCallback() {
            @Override
            public void cameraHasOpened() {
                if (detectCover != null) {
                    detectCover.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (detectCover.getVisibility() == View.VISIBLE || (animator != null && !animator.isRunning())) {
//                                Animation animation1 = AnimationUtils.loadAnimation(LiveDetectActivity.this, R.anim.me_detect_line_tramslate);
//                                detectLine.startAnimation(animation1);
                                if (animator == null) {
                                    PropertyValuesHolder pvhY = PropertyValuesHolder.ofFloat("translationY", cameraRing.getTop(), cameraRing.getBottom());
                                    animator = ObjectAnimator.ofPropertyValuesHolder(detectLine, pvhY);
                                    animator.setDuration(3000);
                                    animator.setRepeatCount(-1);
                                    animator.setRepeatMode(ValueAnimator.RESTART);
                                    animator.setInterpolator(new AccelerateInterpolator());
                                }
                                animator.start();
                                if (detectLine.getVisibility() == View.GONE) {
                                    detectLine.setVisibility(View.VISIBLE);
                                }
                            }
                            if (detectCover.getVisibility() == View.VISIBLE) {
                                detectCover.setVisibility(View.GONE);
                            }
                        }
                    }, 20);
                }
            }
        };

        setTimeout(MAX_DETECT_SECOND);

        mOrientationListener = new OrientationEventListener(this, SensorManager.SENSOR_DELAY_UI) {

            @Override
            public void onOrientationChanged(int orientation) {

                if (orientation == OrientationEventListener.ORIENTATION_UNKNOWN) {
                    return;  //手机平放时，检测不到有效的角度
                }
                //只检测是否有四个角度的改变
                if (orientation > 350 || orientation < 10) { //0度
                    mOrientation = 0;
                } else if (orientation > 80 && orientation < 100) { //90度
                    mOrientation = 90;
                } else if (orientation > 170 && orientation < 190) { //180度
                    mOrientation = 180;
                } else if (orientation > 260 && orientation < 280) { //270度
                    mOrientation = 270;
                }

                // Log.d(TAG, "Orientation changed to " + mOrientation);
            }
        };

        if (mOrientationListener.canDetectOrientation()) {
            Log.v(TAG, "Can detect orientation");
            mOrientationListener.enable();
        } else {
            Log.v(TAG, "Cannot detect orientation");
            mOrientationListener.disable();
        }
    }


    public void setUploadModelImgNum(int size) {
        if (size > 0) {
            mUploadModelImageNum = size;
        }

    }


    // 后台配置的活体检测类型
    // 1, "眨眼", 2, "张嘴", 3, "上下点头", 4, "左右摇头"
    void initCmdMap(String snapInitParam) {
        byte[] mActionBytes = new byte[]{0, 0, 0, 1};
        if (snapInitParam != null) {
            try {
                Map<String, Integer> cmdMapOriginal = new HashMap<>();
                cmdMapOriginal.put("1", LiveDetectActivity.ActionType.BLINK.getCode());
                cmdMapOriginal.put("2", LiveDetectActivity.ActionType.MOUTH.getCode());
                cmdMapOriginal.put("3", LiveDetectActivity.ActionType.UP_DOWN.getCode());
                cmdMapOriginal.put("4", LiveDetectActivity.ActionType.LEFT_RIGHT.getCode());
                String[] dictValue = snapInitParam.split(",");
                for (String s : dictValue) {
                    //noinspection ConstantConditions
                    mActionBytes[cmdMapOriginal.get(s)] = 1;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        initDetect(mActionBytes);//初始化检测
    }

    private void setPrestartFailedTip(int n) {
        mtip.setText(map.get(String.valueOf(n)));
        detectTip.setText(map.get(String.valueOf(n)));
        mMainHandler.sendEmptyMessageDelayed(EventUtil.ACTION_FAILED, 0);
    }

    private void setTipText() {
        try {
            synchronized (action) {
                Drawable drawable;
                LogUtil.w(TAG, "code-->" + action.getCode());

                switch (action.getCode()) {
                    case 0:
                        mtip.setText(getResources().getString(R.string.detect_tip1));
                        detectTip.setText(getResources().getString(R.string.detect_tip1));
//                        SoundManager.getManager().play(SoundManager.SM_LIVENESS_YAW_LEFT);
                        break;
                    case 1:
                        mtip.setText(getResources().getString(R.string.detect_tip2));
                        detectTip.setText(getResources().getString(R.string.detect_tip2));
//                        SoundManager.getManager().play(SoundManager.SM_LIVENESS_YAW_RIGHT);
                        break;
                    case 2:
                        mtip.setText(getResources().getString(R.string.detect_tip3));
                        detectTip.setText(getResources().getString(R.string.detect_tip3));
//                        SoundManager.getManager().play(SoundManager.SM_LIVENESS_MOUTH_OPEN);
                        break;
                    case 3:
                        mtip.setText(getResources().getString(R.string.detect_tip4));
                        detectTip.setText(getResources().getString(R.string.detect_tip4));
//                        SoundManager.getManager().play(SoundManager.SM_LIVENESS_EYE_BLINK);
                        break;

                }
            }
        } catch (Exception e) {
            mtip.setText(getResources().getString(R.string.detect_prestart_tip));
        }

    }


    /**
     * 初始化检测
     */
    private void initDetect() {

        //复制模型到文件系统
        copyModelsToFileSystem();

        previewRate = DisplayUtil.getScreenRate(this);

        // 活体识别初始化
        NdkJniUtils.initialization(getAssets(), detectFilePath, landMarkFilePath, "", Constants.TOO_LARGE, Constants.TOO_SMALL, Constants.TOO_RIGHT, Constants.TOO_LEFT, Constants.TOO_TOP, Constants.TOO_BOTTOM);

        // 先layout SurfaceView，然后再打开camera
        mMainHandler.sendEmptyMessage(EventUtil.CHANGE_SURFACE_VIEW_LAYOUT);
    }

    private void initDetect(byte[] act) {
        actArrays = act;
        for (int i = actArrays.length - 1; i >= 0; i--) {
            if (actArrays[i] == 1) {
                lastStep = i;
                break;
            }
        }

        initDetect();
    }

//    // sdk语音开关
//    public void setSoundEnabled(boolean enabled) {
//        SoundManager.getManager().setSoundEnabled(enabled);
//    }

    public void initStartStatus() {
        isPrestartOk = false;
        failed = 0;
        position = 0;
    }

    public void initStopStatus() {
        isPrestartOk = false;
        failed = 0;
        position = 0;
        mStartTime = 0;
    }

    /**
     * 设置超时时间
     *
     * @param timeout
     */
    private void setTimeout(int timeout) {
        // 不能超过最大和最小超时时间
        if (timeout <= 0)
            timeout = MIN_DETECT_SECOND;

        if (timeout > MAX_DETECT_SECOND)
            timeout = MAX_DETECT_SECOND;

        // 毫秒变秒
        mTimeout = timeout * 1000;
    }

//    /**
//     * 权限回调
//     *
//     * @param requestCode
//     * @param permissions
//     * @param results
//     */
//    @Override
//    public void onRequestPermissionsResult(int requestCode, String permissions[], int[] results) {
//        if (requestCode == PermissionUtils.REQ_CODE_PERM_CAMERA) {
//            if (results.length > 0 && results[0] == PackageManager.PERMISSION_GRANTED) {
//                initCmdMap(snap);
//            } else {
//                detectFinish(DetectStatus.NO_PERMISSION);//去检测
//            }
//        }
//    }

    /**
     * 设置检测回调
     *
     * @param detectCallback
     */
    public void setDetectCallback(DetectCallback detectCallback) {
        mDetectCallback = detectCallback;
    }

    /**
     * 预检回调
     *
     * @param callback
     */
    public void setPreviewCallback(PrestartCallback callback) {
        mPrestartCallback = callback;
    }

    /**
     * 识别回调
     *
     * @param callback
     */
    public void setLivenessCallback(LivenessCallback callback) {
        mLivenessCallback = callback;
    }


    /**
     * 检测完成
     */
    private void detectFinish(DetectStatus status, byte[] result) {
        if (result != null) {
            Log.d(TAG, "status = " + status + "-----" + result[0] + "-----" + result[1]);
        }
        if (status == DetectStatus.SUCCESS) {

            Message message = new Message();
            int feedBackCode = result[0]; //预检测返回码
            int livenessOK = result[1];   //0:活体检测通过 -1:未通过

            // TODO 刷提示
            if (!isPrestartOk) {
                if (feedBackCode == 0) {
                    isPrestartOk = true;

                    //预检成功
//                    mPrestartCallback.onPrestartSuccess(new ejoy.livedetector.model.DetectResult(result));
                    mPreDetectResult = result;
                    failed = 0;
                    doNext();
                } else {
                    //预检失败
                    failed++;
                    if (failed >= MAX_PRESTART) {
                        LogUtil.w(TAG, "predetect-->" + failed);
                        failed = 0;
                        setPrestartFailedTip(feedBackCode);
                        if (mPreDetectResult != null) {
                            mPrestartCallback.onPrestartFail(feedBackCode, livenessOK);
                        }
                    }
                }
            } else {

                if (livenessOK == 0) {
                    failed = 0;
                    if (lastStep > position) {
                        position++;
                        doNext();
                    } else {
                        //活检成功
                        stopLiveDetect();

                        ArrayList<byte[]> imgList = new ArrayList<>();
                        imgList.add(DetectResultUtil.getImage(result));

                        if (mLivenessCallback != null && result.length > 2) {
                            mLivenessCallback.onLivenessSuccess(imgList);//检测完成，识别成就把携带图片集合的imgList返回去
                        }
                    }
                } else if (livenessOK == -1) {
                    //活检失败
                    failed++;

                    if (failed >= MAX_REDETECT) {
                        stopLiveDetect();
                        if (mLivenessCallback != null) {
                            mLivenessCallback.onLivenessFail(feedBackCode, livenessOK);
                        }

                    }
                } else {
                    // 过度状态
                }
            }
        } else {
            mDetectCallback.onDetectFinish(status);//检测完成，把检测的状态返回去
        }
    }

    /**
     * 检测识别，开始上传或者识别
     *
     * @param status
     * @param imgData
     * @param isUpload 上传为true，校验为false
     */
    @SuppressLint("StaticFieldLeak")
    private void updateOrCheckImage(DetectStatus status, final byte[] imgData, final boolean isUpload) {
        if (status == DetectStatus.SUCCESS) {
            new AsyncTask() {
                @Override
                protected String doInBackground(Object[] objects) {
                    return doIdentify(imgData, isUpload);
                }

                @Override
                protected void onPostExecute(Object ret) {
                    Toast.makeText(LiveDetectActivity.this, "识别返回：" + ret, Toast.LENGTH_SHORT).show();
                }
            }.execute(1000);
        }
    }

    /**
     * 识别
     *
     * @param imgData 内部存储的文件名
     */
    private String doIdentify(byte[] imgData, boolean isUpload) {
        String url = "http://vision.jd.com/FaceDetect/";
        // 上传或者识别
        if (isUpload)
            url += "upload.action";
        else
            url += "verifyErp.action";

        return postImage(url, Base64.encodeToString(imgData, Base64.DEFAULT));
    }

    /**
     * POST请求
     */
    public String postImage(String postUrl, String imgBase64) {
        InputStream in = null;
        try {
            //  Log.e("postImage", postUrl);
            URL url = new URL(postUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            //conn.setRequestProperty("Content-Length", String.valueOf(entity.length));

            JSONObject json = new JSONObject();
            try {
                json.put("imgBase64", imgBase64);
                json.put("erp", "liudengyong");
            } catch (Exception e) {
                //  Log.e("postImage", "params--error=" + e.getMessage());
            }

            OutputStream out = conn.getOutputStream();//输出流，用来发送请求，http请求实际上直到这个函数里面才正式发送出去
            BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(out));//创建字符流对象并用高效缓冲流包装它，便获得最高的效率,发送的是字符串推荐用字符流，其它数据就用字节流
            bw.write(json.toString());//把json字符串写入缓冲区中
            bw.flush();//刷新缓冲区，把数据发送出去，这步很重要
            out.close();
            bw.close();

            //  Log.e("postImage", "conn.getResponseCode() = " + conn.getResponseCode());
            in = conn.getInputStream();
            String result = readStrFromStream(in);
            //  Log.e("postImage", "result = " + result);

            if (conn.getResponseCode() == 200) {
                in = conn.getInputStream();
                return readStrFromStream(in);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * 读取输出
     */
    private String readStrFromStream(InputStream in) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = in.read(buffer)) != -1) {
            out.write(buffer, 0, len);
        }
        String ret = null;
        byte[] data = out.toByteArray();
        if (data != null) {
            ret = new String(data);
        }
        out.close();
        return ret;
    }

    //复制模型到文件系统
    public void copyModelsToFileSystem() {

        //String dir = "/sdcard/faceid";
        String dir = "/data/data/" + this.getPackageName();
        File dirFile = new File(dir);
        if (!dirFile.exists()) {
            dirFile.mkdir();
        }

        detectFilePath = dir + "/" + "det1.bin";
        landMarkFilePath = dir + "/" + "landmark.caffemodel";

        Map<String, Integer> fileSizeMap = new HashMap<>();
//        fileSizeMap.put( "det1.bin", 26548);
//        fileSizeMap.put("det2.bin", 400736);
//        fileSizeMap.put("det3.bin", 1556192);
//        fileSizeMap.put("det1.param", 957);
//        fileSizeMap.put("det2.param", 1139);
//        fileSizeMap.put("det3.param", 1544);

        fileSizeMap.put("landmark.caffemodel", 3297959);
        fileSizeMap.put("landmark_deploy.prototxt", 4421);

        for (Map.Entry<String, Integer> entry : fileSizeMap.entrySet()) {
            String fileName = entry.getKey();
            Integer size = entry.getValue();
            String filePath = dir + "/" + fileName;
            if (FileUtil.getFileSize(filePath) != size) {
                releaseAsset(dir, fileName);
            }
        }
    }


    public void onClickBack(View view) {
        detectFinish(DetectStatus.CANCEL, null);
        onBackPressed();
    }

//    public void onClickSound(View view) {
//        boolean soundEnabled = (boolean) SharedPreferencesUtils.get(this, SharedPreferencesUtils.PREF_NAME, SharedPreferencesUtils.SP_SOUND, true);
//        if (soundEnabled) {
//            SharedPreferencesUtils.put(this, SharedPreferencesUtils.PREF_NAME, SharedPreferencesUtils.SP_SOUND, false);
//        } else {
//            SharedPreferencesUtils.put(this, SharedPreferencesUtils.PREF_NAME, SharedPreferencesUtils.SP_SOUND, true);
//        }
//        setHornDrawable();
//    }

//    public void setHornDrawable() {
//        boolean soundEnabled = (boolean) SharedPreferencesUtils.get(this, SharedPreferencesUtils.PREF_NAME, SharedPreferencesUtils.SP_SOUND, true);
//        if (soundEnabled) {
//            mSound.setImageResource(R.drawable.detect_horn);
//        } else {
//            mSound.setImageResource(R.drawable.detect_horn_no);
//        }
//    }


    public class MainHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            // TODO 处理返回值
            LogUtil.d(null, "handleMessage " + msg.what);
            Bundle bundle;
            switch (msg.what) {
                case EventUtil.CAMERA_HAS_STARTED_PREVIEW:
                    startLiveDetect();
                    if (camOpenOverCallback != null) {
                        camOpenOverCallback.cameraHasOpened();
                    }
                    break;
                case EventUtil.TURN_TO_FRONT_CAMERA:
                    CameraInterface.getInstance().doStopCamera();
                    try {
                        CameraInterface.getInstance().doOpenCamera();
                        CameraInterface.getInstance().doStartPreview(mContext, surfaceView.getSurfaceHolder(), surfaceView.getWidth(), surfaceView.getHeight(), null);
                        resetPreCallback();
                        mMainHandler.sendEmptyMessageDelayed(EventUtil.CAMERA_HAS_STARTED_PREVIEW, 0);
                    } catch (Exception e) {
                        CameraInterface.getInstance().setCameraDevice(null);
                    }
                    break;
                case EventUtil.CHANGE_SURFACE_VIEW_LAYOUT:
                    onChangeSurfaceViewLayout();
                    break;

                case EventUtil.SET_TIPS:
                    setTipText();
                    break;
                case EventUtil.PRESTART_SUCCESS:
                    bundle = msg.getData();
//                    mPrestartCallback.onPrestartSuccess(new ejoy.livedetector.model.DetectResult(bundle.getByteArray("result")));
                    break;
                case EventUtil.PRESTART_FAILED:
                    mPrestartCallback.onPrestartFail(msg.arg1, msg.arg2);
                    break;
                case EventUtil.ACTION_SUCCESS:
                    break;
                case EventUtil.LIVENESS_SUCCESS:
                    bundle = msg.getData();
//                    mLivenessCallback.onLivenessSuccess(new ejoy.livedetector.model.DetectResult(bundle.getByteArray("result")));
                    break;
                case EventUtil.LIVENESS_FAILED:
                    mLivenessCallback.onLivenessFail(msg.arg1, msg.arg2);
                    break;
                case EventUtil.PRESTART:
                    mtip.setText(getResources().getString(R.string.detect_prestart_tip));
                    break;
                case EventUtil.DETECT_FINISH:
                    bundle = msg.getData();
                    detectFinish(DetectStatus.SUCCESS, bundle.getByteArray("result"));//成功
                    break;
                case EventUtil.ACTION_FAILED:
//                    prestartTip.setText("");
//                    prestartTip.setCompoundDrawables(null,null,null,null);
//                    mtip.setText("");
//                    mtip.setCompoundDrawables(null, null, null, null);
                    break;
                case EventUtil.RESUME:
                    CameraInterface.getInstance().doStopCamera();
                    try {
                        CameraInterface.getInstance().doOpenCamera();
                        CameraInterface.getInstance().doStartPreview(LiveDetectActivity.this, surfaceView.getSurfaceHolder(), surfaceView.getWidth(), surfaceView.getHeight(), camOpenOverCallback);
                        resetPreCallback();
                    } catch (Exception e) {
                        Log.e(TAG, e.toString());
                        CameraInterface.getInstance().setCameraDevice(null);
                    }
                    break;

                default:
                    break;
            }
            super.handleMessage(msg);
        }
    }

    private void onChangeSurfaceViewLayout() {
        CameraInterface.getInstance().doOpenCamera();
        boolean isSquare = CameraInterface.getInstance().checkCameraHasSquare();

        ViewGroup.LayoutParams params = surfaceView.getLayoutParams();
        mSurfaceOrgWidth = params.width;
        if (isSquare) {
            mSurfaceOrgHeight = params.height;
        } else {
            mSurfaceOrgHeight = DisplayUtil.dip2px(mContext, 312);
        }
        changeSurfaceViewLayout(mDetectCallback != null && mDetectCallback.isSplitMode(getResources().getConfiguration()));

        mMainHandler.sendEmptyMessage(EventUtil.TURN_TO_FRONT_CAMERA);
    }

    /**
     * 开始检测
     */
    public void startLiveDetect() {
        LogUtil.w(TAG, "action--> " + actArrays[0] + "," + actArrays[1] + "," + actArrays[2] + "," + actArrays[3]);

//        SoundManager.getManager().play(SoundManager.SM_LIVENESS_BEGIN);

        // 显示扫描线
//        mScanFaceView.setScanLineVisible(true);

        {
            map.clear();
            map.put("0", getResources().getString(R.string.detect_feedBackCode0));
            map.put("10", getResources().getString(R.string.detect_feedBackCode10));
            map.put("11", getResources().getString(R.string.detect_feedBackCode11));
            map.put("12", getResources().getString(R.string.detect_feedBackCode12));
            map.put("13", getResources().getString(R.string.detect_feedBackCode13));
            map.put("14", getResources().getString(R.string.detect_feedBackCode14));
            map.put("15", getResources().getString(R.string.detect_feedBackCode15));
            map.put("16", getResources().getString(R.string.detect_feedBackCode16));
        }
        initStartStatus();
        mtip.setText(getResources().getString(R.string.detect_prestart_tip));
        resetPreCallback();
    }

    // 重新设置预览回调
    private void resetPreCallback() {
        Camera camera = CameraInterface.getInstance().getCameraDevice();
        if (camera == null) {
            mDetectCallback.onOpenCameraFailed();
            return;
        }

        try {
            mPreviewFormat = CameraInterface.getInstance().getCameraParams().getPreviewFormat();
        } catch (Exception e) {
            MELogUtil.localE(TAG, "resetPreCallback exception ", e);
            return;
        }
        camera.setPreviewCallback(new Camera.PreviewCallback() {
            @Override
            public void onPreviewFrame(byte[] data, Camera camera) {

                if (data == null) {
                    return;
                }
                try {
                    Camera.Size size = camera.getParameters().getPreviewSize();
                    sendPictureFrame(data, size.width, size.height);

                    if (mStartTime == 0) {
                        mStartTime = System.currentTimeMillis();
                    } else if (System.currentTimeMillis() - mStartTime >= mTimeout && isPrestartOk) { // 预检测成功 10s超时
                        LogUtil.d(LiveDetectActivity.this, "time out");
                        stopLiveDetect();
                        detectFinish(DetectStatus.FAILED, null);
                        return;
                    } else if (System.currentTimeMillis() - mStartTime >= mTimeout + 10) { // 预检未成功20s超时
                        LogUtil.d(LiveDetectActivity.this, "time out");
                        stopLiveDetect();
                        detectFinish(DetectStatus.FAILED, null);
                        return;
                    }
                } catch (Exception e) {
                    MELogUtil.onlineE(TAG, "resetPreCallback exception ", e);
                }

            }
        });
    }


    //发送图片画面
    private void sendPictureFrame(byte[] data, int width, int height) {
        byte[] result = null;

        degress = CameraInterface.getInstance().getDegress();

        if (!isPrestartOk) {
            //预检
            result = NdkJniUtils.resultFromJNI(data, width, height, -1, mPreviewFormat, mOrientation);
            if (result != null) {
                LogUtil.d(null, "预检 " + result[0] + "------" + result[1]);
            }
        } else {
            //活检
            result = NdkJniUtils.resultFromJNI(data, width, height, action.getCode(), mPreviewFormat, mOrientation);
            if (result != null) {
                LogUtil.d(null, "活检 " + result[0] + "------" + result[1]);
            }
        }

        if (result == null || result.length == 0) {
            return;
        }

        // 识别成功
        Bundle bundle = new Bundle();
        bundle.putByteArray("result", result);
        Message message = new Message();
        message.setData(bundle);
        message.what = EventUtil.DETECT_FINISH;
        mMainHandler.sendMessage(message);
    }


//    /**
//     * 存储预览图片
//     *
//     * @param data
//     * @param camera
//     */
//    private void savePreviewBitmap(byte[] data, Camera camera) {
//
//        byte[] rawImage;
//        Bitmap bitmap;
//        Camera.Size previewSize = camera.getParameters().getPreviewSize();//获取尺寸,格式转换的时候要用到
//        BitmapFactory.Options newOpts = new BitmapFactory.Options();
//        newOpts.inJustDecodeBounds = true;
//        YuvImage yuvimage = new YuvImage(
//                data,
//                ImageFormat.NV21,
//                previewSize.width,
//                previewSize.height,
//                null);
//        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//        yuvimage.compressToJpeg(new Rect(0, 0, previewSize.width, previewSize.height), 100, baos);// 80--JPG图片的质量[0-100],100最高
//        rawImage = baos.toByteArray();
//        //将rawImage转换成bitmap
//        BitmapFactory.Options options = new BitmapFactory.Options();
//        options.inPreferredConfig = Bitmap.Config.RGB_565;
//        bitmap = BitmapFactory.decodeByteArray(rawImage, 0, rawImage.length, options);
//
//        if (bitmap != null) {
//
//            try {
//                FileOutputStream out = new FileOutputStream("/sdcard/Download/out/face.png");
//                bitmap.compress(Bitmap.CompressFormat.PNG, 90, out);
//                out.flush();
//                out.close();
//            } catch (FileNotFoundException e) {
//                e.printStackTrace();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }

    /**
     * 停止检测
     */
    public void stopLiveDetect() {
        LogUtil.d(null, "stopLiveDetect");
        initStopStatus();

//        mScanFaceView.setScanLineVisible(false);
        try {
//            CameraInterface.getInstance().getCameraDevice().setPreviewCallbackWithBuffer(null);
            CameraInterface.getInstance().doStopCamera();
            if (animator != null && animator.isRunning()) {
                detectLine.setVisibility(View.GONE);
                animator.cancel();
            }
        } catch (Throwable e) {
//            e.printStackTrace();
            LogUtil.e(LiveDetectActivity.this, "stopLiveDetect exception", e);
        }
    }

    /**
     * 保存照片
     */
    private String saveFacePicture(byte[] data) {
        String imgFileName = "face.png";
        FileOutputStream outputStream;
        try {
            File file = new File("/sdcard/" + imgFileName);
            if (file.exists())
                return null;
            outputStream = new FileOutputStream(file);
            outputStream.write(data);
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return imgFileName;
    }

//    /**
//     * 点击切换镜头
//     *
//     * @param view
//     */
//    public void onSwitchCamera(View view) {
//        stopLiveDetect();
//        mCameraId = (CameraInterface.getInstance().getCameraId() + 1) % 2;
//        mMainHandler.sendEmptyMessageDelayed(EventUtil.TURN_TO_FRONT_CAMERA, 500);
//    }


    public void doNext() {
        for (int i = position; i < actArrays.length; i++) {
            if (actArrays[i] == 1) {
                position = i;
                try {
                    synchronized (action) {
                        switch (position) {
                            case 0:
                                action = ActionType.LEFT_RIGHT;
                                break;
                            case 1:
                                action = ActionType.UP_DOWN;
                                break;
                            case 2:
                                action = ActionType.MOUTH;
                                break;
                            case 3:
                                action = ActionType.BLINK;
                                break;
                        }
                        break;
                    }
                } catch (Exception e) {
                    switch (position) {
                        case 0:
                            action = ActionType.LEFT_RIGHT;
                            break;
                        case 1:
                            action = ActionType.UP_DOWN;
                            break;
                        case 2:
                            action = ActionType.MOUTH;
                            break;
                        case 3:
                            action = ActionType.BLINK;
                            break;
                    }
                    break;
                }
            }
        }
        mMainHandler.sendEmptyMessage(EventUtil.SET_TIPS);

    }

    @Override
    protected void onPause() {
        super.onPause();

    }

    @Override
    protected void onStart() {
        super.onStart();
        if (isOnPause) {
            mtip.post(new Runnable() {
                @Override
                public void run() {
                    mMainHandler.sendEmptyMessageDelayed(EventUtil.RESUME, 0);
                    mDetectCallback.onResumeMethod();
                }
            });
        }

        isOnPause = false;
    }

    @Override
    protected void onStop() {
        super.onStop();
        stopLiveDetect();
        CameraInterface.getInstance().doStopCamera();
        isOnPause = true;
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 增加埋点
        JDMAUtils.onEventPagePV(AppBase.getAppContext(), "livedetect", "livedetect");
    }

    @Override
    protected void onDestroy() {
        stopLiveDetect();
        super.onDestroy();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.d(TAG, "" + newConfig.orientation);

        if (mDetectCallback != null && mDetectCallback.isXiaomiTablet() && CameraInterface.getDegress() != 0) {
        } else {
            changeSurfaceViewLayout(mDetectCallback != null && mDetectCallback.isSplitMode(newConfig));
            CameraInterface.getInstance().resetCameraOrientation(LiveDetectActivity.this);
        }
    }

    private void changeSurfaceViewLayout(boolean splitMode) {
        ViewGroup.LayoutParams params = surfaceView.getLayoutParams();
        params.width = splitMode ? mSurfaceOrgHeight : mSurfaceOrgWidth;
        params.height = splitMode ? mSurfaceOrgWidth : mSurfaceOrgHeight;
        surfaceView.setLayoutParams(params);
    }

    private String releaseAsset(String dir, String name) {

        try {
            InputStream is = this.getResources().getAssets().open(name);
            OutputStream os = FileUtil.fileOutputStream(dir + "/" + name);
            FileUtil.write(os, is);

        } catch (IOException e) {
            return null;
        }

        return dir + "/" + name;
    }
}
