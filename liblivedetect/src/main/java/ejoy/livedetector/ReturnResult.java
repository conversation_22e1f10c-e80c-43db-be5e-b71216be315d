package ejoy.livedetector;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/1/19.
 * God loves all
 */
public class ReturnResult {

    public Boolean succeed = false;
    public String result;

//    public  ReturnResult(Boolean succeed, String result){
//        this.succeed=succeed;
//        this.result=result;
//    }

    public Boolean getSucceed() {
        return succeed;
    }

    public void setSucceed(<PERSON><PERSON><PERSON> succeed) {
        this.succeed = succeed;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
