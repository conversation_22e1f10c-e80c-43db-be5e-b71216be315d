package ejoy.livedetector;

import android.os.AsyncTask;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * Created by xinchunpeng on 2017/1/18.
 * God loves all
 */
public class UploadAndCheck {
    public FaceRecognitionInterface.CheckCallBack callBack;

    public UploadAndCheck(FaceRecognitionInterface.CheckCallBack callBack) {
        this.callBack = callBack;
    }

    /**
     * 检测识别，开始上传或者识别
     *
     * @param imgData
     * @param isUpload 上传为true，校验为false
     */
    public void updateOrCheckImage(final String erp, final String imgBase64, final String imgJsonArray, final String key, final boolean isUpload) {

        new AsyncTask() {
            @Override
            protected ReturnResult doInBackground(Object[] objects) {
                return doIdentify(erp, imgBase64, imgJsonArray, key, isUpload);
            }

            @Override
            protected void onPostExecute(Object ret) {
                ReturnResult result = (ReturnResult) ret;

                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(result.getResult());

                    if (isUpload) {
                        if (jsonObject.has("dataValue") && jsonObject.getString("dataValue").equals("true"))
                            callBack.onDetechSuccess();
                        else
                            callBack.onDetechFailure(result.getResult());
                    } else {
                        if (jsonObject.has("dataValue") && jsonObject.getString("dataValue").equals("1.0")) {
                            callBack.onDetechSuccess();
                        } else {
                            callBack.onDetechFailure(result.getResult());
                        }
                    }
                    Log.i("UploadAndCheck", "识别返回：" + ret);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        }.execute(1000);
    }


    private ReturnResult doIdentify(String erp, String imgBase64, String imgJsonArray, String key, boolean isUpload) {
//        String url = "http://vision.jd.com/FaceDetect/";
        String url = "http://vision.jd.com/image/";
        String token = "";
        // 上传或者识别
        if (isUpload) {
//            url += "upload.action";
            url += "erpImgUpload.action";
            token = "942f02b8-3445-48ef-b424-fb95ee7f9a71";
        } else {
//            url += "verifyErp.action";
            url += "erpImgVerify.action";
            token = "070abb8e-867f-4c7e-89ce-5cc7a5ce8bd3";
        }


//        return postImage(erp,url, Base64.encodeToString(imgData, Base64.DEFAULT));

        return postImage(erp, url, imgBase64, imgJsonArray, token, key, isUpload);
    }

    /**
     * POST请求
     */
    public ReturnResult postImage(String erp, String postUrl, String imgBase64, String imgJsonArray, String token, String key, boolean isUpload) {
        ReturnResult returnResult = new ReturnResult();
        InputStream in = null;
        try {
            Log.e("postImage", postUrl);
            URL url = new URL(postUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            //conn.setRequestProperty("Content-Length", String.valueOf(entity.length));


            JSONObject json = new JSONObject();
            try {

                JSONObject imgarray = new JSONObject(imgJsonArray);

                if (isUpload) {
                    json.put("imgJsonArray", imgarray.get("img"));
                } else {
                    json.put("imgBase64", imgBase64);
                }

                json.put("erp", erp);

                json.put("token", token);
                json.put("key", key);
            } catch (Exception e) {
                Log.e("postImage", "params--error=" + e.getMessage());
            }


            OutputStream out = conn.getOutputStream();//输出流，用来发送请求，http请求实际上直到这个函数里面才正式发送出去
            BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(out));//创建字符流对象并用高效缓冲流包装它，便获得最高的效率,发送的是字符串推荐用字符流，其它数据就用字节流
            bw.write(json.toString());//把json字符串写入缓冲区中
            bw.flush();//刷新缓冲区，把数据发送出去，这步很重要
            out.close();
            bw.close();

            int code = conn.getResponseCode();
            in = conn.getInputStream();
            String result = readStrFromStream(in);
            Log.e("postImage", "result = " + result);

            returnResult.setResult(result);
            if (code == 1) {
                returnResult.setSucceed(true);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return returnResult;
    }

    /**
     * 读取输出
     */
    private String readStrFromStream(InputStream in) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = in.read(buffer)) != -1) {
            out.write(buffer, 0, len);
        }
        String ret = null;
        byte[] data = out.toByteArray();
        if (data != null) {
            ret = new String(data);
        }
        out.close();
        return ret;
    }
}
