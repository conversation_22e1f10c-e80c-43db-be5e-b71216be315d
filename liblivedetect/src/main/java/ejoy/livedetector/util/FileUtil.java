package ejoy.livedetector.util;

/**
 * Created by f<PERSON><PERSON><PERSON> on 2016/12/19.
 */

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.Toast;


public class FileUtil {
    private static final String TAG = "FileUtil";
    private static final File parentPath = Environment.getExternalStorageDirectory();
    private static String storagePath = "";
    private static final String DST_FOLDER_NAME = "PlayCamera";

    private static String initPath() {
        if (storagePath.equals("")) {
            storagePath = parentPath.getAbsolutePath() + "/" + DST_FOLDER_NAME;
            File f = new File(storagePath);
            if (!f.exists()) {
                f.mkdir();
            }
        }
        return storagePath;
    }

    public static void saveBitmap(Bitmap b) {

        String path = initPath();
        long dataTake = System.currentTimeMillis();
        String jpegName = path + "/" + dataTake + ".jpg";
        Log.i(TAG, "saveBitmap:jpegName = " + jpegName);
        try {
            FileOutputStream fout = new FileOutputStream(jpegName);
            BufferedOutputStream bos = new BufferedOutputStream(fout);
            b.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            bos.flush();
            bos.close();
            Log.i(TAG, "saveBitmap�ɹ�");
        } catch (IOException e) {
            Log.i(TAG, "saveBitmap:ʧ��");
            e.printStackTrace();
        }
    }

    public static InputStream fileInputStream(String filePath) throws IOException {
        File file = new File(filePath);
        if (file.exists()) {
            return new FileInputStream(file);
        } else {
            return null;
        }
    }

    public static OutputStream fileOutputStream(String filePath) throws IOException {
        File file = new File(filePath);
        String tempPath = file.getAbsolutePath();
        return new FileOutputStream(tempPath);
    }

    public static String read(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buf = new byte[1024];
        int len = -1;
        while ((len = inputStream.read(buf)) != -1) {
            outStream.write(buf, 0, len);
        }

        byte[] data = outStream.toByteArray();
        outStream.close();
        inputStream.close();
        return new String(data, "UTF-8");
    }

    public static boolean write(OutputStream outputStream, byte[] content, int offset, int size) throws IOException {
        outputStream.write(content, offset, size);
        outputStream.close();
        return true;
    }

    public static boolean write(OutputStream outputStream, String content) throws IOException {
        outputStream.write(content.getBytes("UTF-8"));
        outputStream.close();
        return true;
    }

    public static boolean write(OutputStream os, InputStream is) throws IOException {
        byte buf[] = new byte[1024];
        int len = -1;
        while ((len = is.read(buf)) != -1) {
            os.write(buf, 0, len);
        }
        is.close();
        os.close();

        return true;
    }

    public static boolean exists(String pathString) {
        File f = new File(pathString);
        if (!f.exists()) {
            return false;
        } else {
            return true;
        }
    }

    public static void writeImage(Bitmap bitmap, OutputStream fileOutputStream) {
        bitmap.compress(Bitmap.CompressFormat.JPEG, 50, fileOutputStream);
        try {
            fileOutputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static Bitmap readBitmap(String path) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inScaled = false;
        return BitmapFactory.decodeFile(path, options);
    }

    public static Bitmap readBitmap(byte[] bytes) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inScaled = false;
        return BitmapFactory.decodeByteArray(bytes, 0, bytes.length, options);
    }

    public static boolean unzip(String src, String dest) {
        ZipInputStream inZip = null;
        try {
            inZip = new ZipInputStream(new FileInputStream(src));
            ZipEntry zipEntry;
            String szName = "";
            while ((zipEntry = inZip.getNextEntry()) != null) {
                szName = zipEntry.getName();
                if (zipEntry.isDirectory()) {
                    szName = szName.substring(0, szName.length() - 1);
                    File folder = new File(dest + "/" + szName);
                    folder.mkdirs();
                } else {

                    File file = new File(dest + "/" + szName);
                    file.createNewFile();

                    FileOutputStream out = new FileOutputStream(file);
                    int len;
                    byte[] buffer = new byte[1024];

                    while ((len = inZip.read(buffer)) != -1) {
                        out.write(buffer, 0, len);
                        out.flush();
                    }

                    out.close();
                }
            }
            inZip.close();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public static boolean renameFile(File oldfile, File newfile) {
        boolean result = false;
        if (oldfile == null || newfile == null || !oldfile.exists() || oldfile.isDirectory())
            return result;
        if (newfile.exists())
            return result;
        result = oldfile.renameTo(newfile);
        return result;
    }

    public static byte[] getBytes(InputStream inputStream) throws Exception {
        byte[] b = new byte[1024];
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int len = -1;
        while ((len = inputStream.read(b)) != -1) {
            byteArrayOutputStream.write(b, 0, len);
        }
        byteArrayOutputStream.close();
        inputStream.close();
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 将Bitmap图片保存到本地相册
     */
    public static void savePhotoToGallery(final Context context, final Bitmap bitmap) {

        if (bitmap == null) {
            Toast.makeText(context, "未获取到图片", Toast.LENGTH_SHORT ).show();
            return;
        }

        new Thread(new Runnable() {
            @Override
            public void run() {

                try {
                    String fileName = "face_" + System.currentTimeMillis() + ".jpg";
                    MediaStore.Images.Media.insertImage(context.getContentResolver(), bitmap,
                            fileName, "人脸检测"); //
                    showToast(context, "图片保存至相册");
                } catch (Exception e) {
                    showToast(context, "图片保存失败");
                    Log.e(TAG, "图片保存异常：" + e);
                }
            }
        }).start();
    }

    public static long getFileSize(String filePath)  {
        long size = 0;

        try {
            File file = new File(filePath);

            if (file.exists()) {
                FileInputStream fis = new FileInputStream(file);
                size = fis.available();
            }
        } catch (Exception e) {

        }

        return size;
    }


    public static void showToast(final Context context, String text) {
        ((Activity) context).runOnUiThread(new Runnable() {
            @Override
            public void run() {
//                Toast.makeText(context,"图片保存至相册", Toast.LENGTH_SHORT ).show();
            }
        });
    }

}
