package ejoy.livedetector.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.hardware.Camera;
import android.os.Environment;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.LinkedList;
import java.util.UUID;

import ejoy.livedetector.camera.CameraInterface;


public class ImagesUtil {
    public static LinkedList<Object> byteList;
    public static byte[] firstImg;

    public static LinkedList<Object> getByteList() {
        return byteList;
    }

    public static void setByteList(LinkedList<Object> byteList) {
        ImagesUtil.byteList = byteList;
    }

    public static byte[] getFirstImg() {
        return firstImg;
    }

    public static void setFirstImg(byte[] img) {
        firstImg = new byte[img.length - 16];
        for (int i = 0; i < firstImg.length; i++) {
            firstImg[i] = img[i + 16];
        }
    }

    public static String[] getImgArrays(String keyStr) {
        // 处理多张的情况
        ImagesUtil.YUV2JPEG();
        LinkedList<Object> list = ImagesUtil.getByteList();
        if (list == null) return null;

        String[] imgArrays = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            byte[] data = (byte[]) list.get(i);
            try {
                imgArrays[i] = RSAAESEncryption.getImgBase64EncryptionStr(keyStr, data);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return imgArrays;
    }

    public static String getImgJsonArray(String keyStr) {
        // 处理多张的情况
        ImagesUtil.YUV2JPEG();
        LinkedList<Object> list = ImagesUtil.getByteList();
        if (list == null) return "";

        JSONObject jsonObject = new JSONObject();

        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            byte[] data = (byte[]) list.get(i);
            try {
                jsonArray.put(i, RSAAESEncryption.getImgBase64EncryptionStr(keyStr, data));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            jsonObject.put("img", jsonArray);
        } catch (JSONException e) {
            e.printStackTrace();
        }

//        return jsonArray.toString();
        return jsonObject.toString();
    }

    public static void YUV2JPEG() {
        if (byteList != null) {
            int length = byteList.size();
            for (int i = 0; i < length; i++) {
                Object[] obj = (Object[]) byteList.getFirst();
                int width = (int) obj[1];
                int height = (int) obj[2];
                byte[] data = (byte[]) obj[0];


                ByteArrayOutputStream baos = new ByteArrayOutputStream();

                YuvImage yuvimage = new YuvImage(
                        data,
                        ImageFormat.NV21,
                        width,
                        height,
                        null);
                yuvimage.compressToJpeg(new Rect(0, 0, width, height), 80, baos);// 80--JPG图片的质量[0-100],100最高
                data = baos.toByteArray();


                // 角度-------------------------待优化
                Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length);
                Matrix matrix = new Matrix();
                int degree = CameraInterface.getDegress();
                matrix.preRotate(360 - degree);
                bitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);

                baos = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 80, baos);
                data = baos.toByteArray();
                bitmap.recycle();

                //------------------------------------

                // 存sd
//                if(data.length>18) {
//                    Bitmap bp = BitmapFactory.decodeByteArray(data, 0, data.length);
//                    FileUtil.saveBitmap(bp);
//                    bp.recycle();
//                }

                byteList.removeFirst();
                byteList.add(data);
            }
        }
    }

    private static byte[] rotateYUV420Degree90(byte[] data, int imageWidth, int imageHeight) {
        byte[] yuv = new byte[imageWidth * imageHeight * 3 / 2];
        int i = 0;
        for (int x = 0; x < imageWidth; x++) {
            for (int y = imageHeight - 1; y >= 0; y--) {
                yuv[i] = data[y * imageWidth + x];
                i++;
            }
        }
        i = imageWidth * imageHeight * 3 / 2 - 1;
        for (int x = imageWidth - 1; x > 0; x = x - 2) {
            for (int y = 0; y < imageHeight / 2; y++) {
                yuv[i] = data[(imageWidth * imageHeight) + (y * imageWidth) + x];
                i--;
                yuv[i] = data[(imageWidth * imageHeight) + (y * imageWidth)
                        + (x - 1)];
                i--;
            }
        }
        return yuv;
    }

    private static byte[] rotateYUV420Degree180(byte[] data, int imageWidth, int imageHeight) {
        byte[] yuv = new byte[imageWidth * imageHeight * 3 / 2];
        int i = 0;
        int count = 0;
        for (i = imageWidth * imageHeight - 1; i >= 0; i--) {
            yuv[count] = data[i];
            count++;
        }
        i = imageWidth * imageHeight * 3 / 2 - 1;
        for (i = imageWidth * imageHeight * 3 / 2 - 1; i >= imageWidth
                * imageHeight; i -= 2) {
            yuv[count++] = data[i - 1];
            yuv[count++] = data[i];
        }
        return yuv;
    }

    private static byte[] rotateYUV420Degree270(byte[] data, int imageWidth,
                                               int imageHeight) {
        byte[] yuv = new byte[imageWidth * imageHeight * 3 / 2];
        int nWidth = 0, nHeight = 0;
        int wh = 0;
        int uvHeight = 0;
        if (imageWidth != nWidth || imageHeight != nHeight) {
            nWidth = imageWidth;
            nHeight = imageHeight;
            wh = imageWidth * imageHeight;
            uvHeight = imageHeight >> 1;// uvHeight = height / 2
        }

        int k = 0;
        for (int i = 0; i < imageWidth; i++) {
            int nPos = 0;
            for (int j = 0; j < imageHeight; j++) {
                yuv[k] = data[nPos + i];
                k++;
                nPos += imageWidth;
            }
        }
        for (int i = 0; i < imageWidth; i += 2) {
            int nPos = wh;
            for (int j = 0; j < uvHeight; j++) {
                yuv[k] = data[nPos + i];
                yuv[k + 1] = data[nPos + i + 1];
                k += 2;
                nPos += imageWidth;
            }
        }
        return rotateYUV420Degree180(rotateYUV420Degree90(data, imageWidth, imageHeight), imageWidth, imageHeight);
    }

    public static byte[] rotateYUV420(int degree, byte[] data, int imageWidth, int imageHeight) {
        switch (degree) {
            case 90:
                return data;
            case 0:
                return rotateYUV420Degree90(data, imageWidth, imageHeight);
            case 180:
                return rotateYUV420Degree270(data, imageWidth, imageHeight);
            case 270:
                return rotateYUV420Degree180(data, imageWidth, imageHeight);
        }
        return data;
    }


    private static final String SD_PATH = "/sdcard/aaaa/";
    private static final String IN_PATH = "/aaaa/";

    // 分析1：存到手机文件夹
    public static String saveBitmap(Context context, Bitmap mBitmap) {
        String savePath;
        File filePic;
        if (Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED)) {
            savePath = SD_PATH;
        } else {
            savePath = context.getApplicationContext().getFilesDir()
                    .getAbsolutePath()
                    + IN_PATH;
        }
        try {
//            filePic = new File(savePath + generateFileName() + ".jpg");
            filePic = new File(savePath + "photo.jpg");
            if (!filePic.exists()) {
                filePic.getParentFile().mkdirs();
                filePic.createNewFile();
            } else {
                return null;
            }
            FileOutputStream fos = new FileOutputStream(filePic);
            mBitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            fos.flush();
            fos.close();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        }
        System.out.println("filePic.getAbsolutePath()=" + filePic.getAbsolutePath());

        return filePic.getAbsolutePath();
    }

    private static String generateFileName() {
        return UUID.randomUUID().toString();
    }

    public static void saveData(final Context context, final byte[] data2, final int w, final int h) {
        Thread thread = new Thread
                (new Runnable() {
                    @Override
                    public void run() {

                        // b. 获得摄像头预览Size
//                        Camera.Size size = camera.getParameters().getPreviewSize();
                        try {
                            // c. 创建YUV对象
                            YuvImage image = new YuvImage(data2, ImageFormat.NV21, w, h, null);

                            // d. 存为BitMap对象
                            ByteArrayOutputStream stream = new ByteArrayOutputStream();
                            image.compressToJpeg(new Rect(0, 0, w, h), 80, stream);
                            Bitmap bmp = BitmapFactory.decodeByteArray(stream.toByteArray(), 0, stream.size());

                            // e. 保存到文件 - 下面分析1
                            saveBitmap(context, bmp);
                            stream.close();
                        } catch (Exception ex) {
                            Log.e("carson", "Error:" + ex.getMessage());
                        }
                    }

                });
        thread.start();
    }
}