package ejoy.livedetector.util;

import android.util.Base64;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;


/**
 * Created by liuqianginfo on 2017/5/10.
 */
public class RSAAESEncryption {

    /**
     * 加密算法步骤：
     * 1、ios、android、pc客户端打印时间戳生成13位的数字然后加上随机3个字符凑成16位字符串（初步设定16位），该字符串作为AES加密算法的key。
     * 2、对上述生成的key进行RSA加密，首先把key进行base64字符串加密，通过encryptByPublicKey(data,rsaKey)进行加密，data是key.getBytes(),rsaKey是RSAPublicKey，加密后生成base64字符串该串作为AES的加密算法key
     * 3、对图片进行base64编码生成字符串
     * 4、用AES对图片base64进行加密
     */

    /**
     * 定义加密方式
     */
    private final static String KEY_RSA = "RSA";

    /**
     * RSA公钥
     */
    public static String RSAPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNvJiXAzupsHrjF+mfxgBBD4fHYfeB+0nhewYZRpygNFkGIak/siInAk+ZsH6t/PWNltfLMXh25f/7D8A74wjhlVInKsM+V+QYTTEX0t8e3F6F+mqSMLuxK+OXPTHEx7Nq4gOZMDFLt3i3KTIMcPFS36lckyajXCejhKh71j3S0QIDAQAB";


    /**
     *
     */
    private static final String IV_STRING = "3bCdEfGhiJZXCVB#";

    private static final int BASE64_FLAG = Base64.DEFAULT;

    /**
     * 图片加密
     *
     * @param sixteenStringsRSAKey 16位字符串=时间戳+3位随意字符串
     * @param imgByte
     */
    public static String getImgBase64EncryptionStr(String sixteenStringsRSAKey, byte[] imgByte) throws Exception {
        //对图片二进制进行base64编码处理
        String imgBase64Str = encryptBase64(imgByte);
        //对图片base64进行AES进行加密
        return aesEncryptString(imgBase64Str, sixteenStringsRSAKey);
    }

    /**
     * @param key 16位字符串=时间戳+3位随意字符串
     */
    public static String encryptionAESKey(String key) throws Exception {
        return encryptByPublicKey(key.getBytes(), RSAPublicKey);
    }

    /**
     * AES加密
     *
     * @param content
     * @param key
     * @return
     * @throws IOException
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws InvalidAlgorithmParameterException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws UnsupportedEncodingException
     */
    public static String aesEncryptString(String content, String key) throws IOException, InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        //aes加密内容和key进行UTF-8转码
        byte[] contentBytes = content.getBytes("UTF-8");
        byte[] keyBytes = key.getBytes("UTF-8");
        SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
        //iv设置
        byte[] initParam = IV_STRING.getBytes("UTF-8");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
        //加密类型设置
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);
        //加密
        byte[] encryptedBytes = cipher.doFinal(contentBytes);
        //base64转码
        String encryptedStr = Base64.encodeToString(encryptedBytes, BASE64_FLAG);
        return encryptedStr;
    }

    /**
     * RSA公钥加密
     *
     * @param data 待加密数据
     * @param key  公钥
     * @return
     */
    public static String encryptByPublicKey(byte[] data, String key) throws Exception {
        byte[] bytes = decryptBase64(key);
        // 取得公钥
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(bytes);
        KeyFactory factory = KeyFactory.getInstance(KEY_RSA);
        PublicKey publicKey = factory.generatePublic(keySpec);
        // 对数据加密
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");//factory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] resultByte = cipher.doFinal(data);
        String baseKey = encryptBase64(resultByte);
        return baseKey;
    }

    /**
     * BASE64 解密
     *
     * @param key 需要解密的字符串
     * @return 字节数组
     * @throws Exception
     */
    public static byte[] decryptBase64(String key) throws Exception {
        return Base64.decode(key, BASE64_FLAG);
    }

    /**
     * BASE64 加密
     *
     * @param key 需要加密的字节数组
     * @return 字符串
     * @throws Exception
     */
    public static String encryptBase64(byte[] key) throws Exception {

        return Base64.encodeToString(key, BASE64_FLAG);
    }
}
