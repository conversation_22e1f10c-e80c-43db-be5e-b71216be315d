package ejoy.livedetector.util;

import android.os.SystemClock;
import android.util.Log;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/2/6.
 */

public class LogUtil {
    public static boolean ENABLE_LOG = true;
    public static boolean ENABLE_LOG_PROFILE = false;
    public static boolean ENABLE_LOG_FILE = false;
    public static int MIN_LOG_LEVEL = -1;
    public static final int VERBOSE = 0;
    public static final int DEBUG = 1;
    public static final int INFO = 2;
    public static final int WARNING = 3;
    public static final int ERROR = 4;
    public static final String TAG = "Utility";
    public static final String PROFILE_TAG = ":Profile";
    private static long mLastTime;
    private static long mInitTime;

    public LogUtil() {
    }

    public static int i(Object o, String msg) {
        if (ENABLE_LOG && MIN_LOG_LEVEL <= 2) {
            String tag = getTag(o);
            if (msg.contains("BEGIN")) {
                initTime();
            } else if (msg.contains("END")) {
                msg = genenateLogPrefix(tag) + msg;
                long currentTime = SystemClock.currentThreadTimeMillis();
                return Log.i(tag, msg + " -> cost time = " + (currentTime - mLastTime) + ", total time = " + (currentTime - mInitTime));
            }

            msg = genenateLogPrefix(tag) + msg;
            return Log.i(tag, msg);
        } else {
            return 0;
        }
    }

    public static int v(Object o, String msg) {
        if (ENABLE_LOG && MIN_LOG_LEVEL <= 0) {
            String tag = getTag(o);
            msg = genenateLogPrefix(tag) + msg;
            return Log.v(tag, msg);
        } else {
            return 0;
        }
    }

    public static int d(Object o, String msg) {
        if (ENABLE_LOG && MIN_LOG_LEVEL <= 1) {
            String tag = getTag(o);
            msg = genenateLogPrefix(tag) + msg;
            return Log.d(tag, msg);
        } else {
            return 0;
        }
    }

    public static int w(Object o, String msg) {
        if (ENABLE_LOG && MIN_LOG_LEVEL <= 3) {
            String tag = getTag(o);
            msg = genenateLogPrefix(tag) + msg;
            return Log.w(tag, msg);
        } else {
            return 0;
        }
    }

    public static int e(Object o, String msg) {
        if (ENABLE_LOG && MIN_LOG_LEVEL <= 4) {
            String tag = getTag(o);
            msg = genenateLogPrefix(tag) + msg;
            return Log.e(tag, msg);
        } else {
            return 0;
        }
    }

    public static int e(Object o, String msg, Throwable tr) {
        if (ENABLE_LOG && MIN_LOG_LEVEL <= 4) {
            String tag = getTag(o);
            msg = genenateLogPrefix(tag) + msg;
            return Log.e(tag, msg, tr);
        } else {
            return 0;
        }
    }

    public static int wtf(Object o, String msg) {
        String tag = getTag(o);
        msg = genenateLogPrefix(tag) + msg;
        return Log.wtf(tag, msg);
    }

    public static String getTag(Object o) {
        return o == null ? "Utility" : (o instanceof String ? (String) o : o.getClass().getSimpleName());
    }

    public static long initTime() {
        if (ENABLE_LOG && ENABLE_LOG_PROFILE) {
            mLastTime = SystemClock.currentThreadTimeMillis();
            mInitTime = mLastTime;
            return mLastTime;
        } else {
            return 0L;
        }
    }

    public static void printTime(Object o, String msg) {
        if (ENABLE_LOG && ENABLE_LOG_PROFILE) {
            long currentTime = SystemClock.currentThreadTimeMillis();
            String tag = getTag(o);
            i(tag, msg + " -> cost time = " + (currentTime - mLastTime) + ", total time = " + (currentTime - mInitTime));
            mLastTime = SystemClock.currentThreadTimeMillis();
        }

    }

    private static String genenateLogPrefix(String simpleClassName) {
        StackTraceElement[] sts = Thread.currentThread().getStackTrace();
        if (sts == null) {
            return "";
        } else {
            StackTraceElement[] var2 = sts;
            int var3 = sts.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                StackTraceElement st = var2[var4];
                if (!st.isNativeMethod() && !st.getClassName().equals(Thread.class.getName()) && st.getClassName().endsWith(simpleClassName)) {
                    return "[" + Thread.currentThread().getName() + "][" + st.getFileName() + ":" + st.getLineNumber() + "][" + st.getMethodName() + "] ";
                }
            }

            return "";
        }
    }

    public static int writeLog(Object o, String msg) {
        if (ENABLE_LOG && ENABLE_LOG_PROFILE && ENABLE_LOG_FILE) {
            String tag = getTag(o);

            try {
                msg = msg + "\n";
                long e = System.currentTimeMillis();
                FileOutputStream file = new FileOutputStream("/mnt/sdcard/log.txt", true);
                String time = e + "--\t";
                file.write(time.getBytes());
                file.write(tag.getBytes());
                file.write((new String("\t")).getBytes());
                file.write(msg.getBytes());
                file.flush();
                file.close();
            } catch (FileNotFoundException var7) {
                var7.printStackTrace();
            } catch (IOException var8) {
                var8.printStackTrace();
            }
        }

        return 0;
    }
}
