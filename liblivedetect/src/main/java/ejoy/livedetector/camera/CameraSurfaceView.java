package ejoy.livedetector.camera;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/12/19.
 */

import android.Manifest;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;

import com.jd.oa.AppBase;
import com.jd.oa.permission.PermissionHelper;

import ejoy.livedetector.util.EventUtil;

public class CameraSurfaceView extends SurfaceView implements SurfaceHolder.Callback {

    private String TAG = "LiveDetectActivity";
    SurfaceHolder mSurfaceHolder;
    Context mContext;
    Handler mHandler;

    public CameraSurfaceView(Context context, AttributeSet attrs) {
        super(context, attrs);

        mContext = context;

        mSurfaceHolder = getHolder();
        mSurfaceHolder.setFormat(PixelFormat.TRANSPARENT);
        mSurfaceHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        mSurfaceHolder.addCallback(this);
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
//        Log.d(TAG, "width:" + width + ",height:" + height);
        if(!PermissionHelper.isGranted(AppBase.getAppContext(), Manifest.permission.CAMERA)){
            return;
        }
        CameraInterface.getInstance().doStopCamera();
        try {
            CameraInterface.getInstance().doOpenCamera();
            CameraInterface.getInstance().doStartPreview(mContext, this.getSurfaceHolder(), width, height, null);
            mHandler.sendEmptyMessageDelayed(EventUtil.CAMERA_HAS_STARTED_PREVIEW, 0);
        } catch (Exception e) {
            CameraInterface.getInstance().setCameraDevice(null);
        }
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        CameraInterface.getInstance().doStopCamera();
    }

    public SurfaceHolder getSurfaceHolder() {
        return mSurfaceHolder;
    }

    public void setHandler(Handler handler) {
        mHandler = handler;
    }
}
