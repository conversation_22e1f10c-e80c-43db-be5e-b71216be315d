package ejoy.livedetector.camera;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/12/19.
 */

import static java.lang.Math.abs;
import static ejoy.livedetector.util.LogUtil.TAG;

import android.app.Activity;
import android.content.Context;
import android.hardware.Camera;
import android.hardware.Camera.Size;
import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.WindowManager;

import com.jd.oa.abilities.utils.MELogUtil;

import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class CameraInterface {

    private Camera mCamera;
    private Camera.Parameters mParams;
    private boolean isPreviewing = false;
    private int mCameraId = Camera.CameraInfo.CAMERA_FACING_FRONT;
    ;
    private static CameraInterface mCameraInterface;

    public interface CamOpenOverCallback {
        void cameraHasOpened();
    }

    public static synchronized CameraInterface getInstance() {
        if (mCameraInterface == null) {
            mCameraInterface = new CameraInterface();
        }
        return mCameraInterface;
    }

    public void doOpenCamera() {
        Log.i(TAG, "doOpenCamera: 开启相机");
        mCamera = getCameraDevice();
        if(mCamera == null){
            return;
        }
        mCamera.setPreviewCallbackWithBuffer(null);
    }

    public void doOpenCamera(CamOpenOverCallback callback, int cameraId) {
        try {
            mCamera = Camera.open(cameraId);
            mCameraId = cameraId;
            mCamera.setPreviewCallbackWithBuffer(null);
            if (callback != null) {
                callback.cameraHasOpened();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void doStartPreview(Context context, SurfaceHolder holder, int w, int h, CamOpenOverCallback callback) {

        Log.i(TAG, "doStartPreview: 开始预览");
        if (isPreviewing) {
            mCamera.stopPreview();
            return;
        }
        if (mCamera != null) {
            mParams = mCamera.getParameters();

            List<Size> previewSizes = mParams.getSupportedPreviewSizes();
            Size size = null;
            if (w == h) {
                size = getSizeBySquare(previewSizes, w);
            } else {
                size = getSize(previewSizes, w);
            }
            mParams.setPreviewSize(size.width, size.height);

            mParams.setJpegQuality(60);
            mParams.setRecordingHint(true);
            mParams.set("orientation", "portrait");
            mCamera.setDisplayOrientation(setCameraDisplayOrientation(context, mCameraId));
            try {
                mCamera.setParameters(mParams);
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                mCamera.setPreviewDisplay(holder);
                mCamera.startPreview();
            } catch (IOException e) {

            }
            isPreviewing = true;

            mParams = mCamera.getParameters();

            if (callback != null) {
                callback.cameraHasOpened();
            }
        }
    }

    public boolean checkCameraHasSquare(){
        if(mCamera == null){
            return false;
        }
        Size sizeReturn = null;
        try {
            mParams = mCamera.getParameters();
            List<Size> sizeList = mParams.getSupportedPreviewSizes();

            Collections.reverse(sizeList);
            for (Size size : sizeList) {
                Log.d(TAG, "getSize width:" + size.width + " x" + size.height);

                if (size.width < 640) {
                    continue;
                }

                float ratio = (float) size.width / size.height;
                if (abs(ratio - 1.0) < 0.1) {
                    sizeReturn = size;
                    Log.d(TAG, "getSize width:" + sizeReturn.width + " x" + sizeReturn.height);
                }
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "checkCameraHasSquare exception ", e);
            return false;
        }

        if(sizeReturn == null){
            return false;
        }
        return  true;
    }



    private Size getSize(List<Size> sizeList, int widthView) {
        Size sizeReturn = null;
        if (widthView > 720) {
            widthView = 720;
        }
        if (sizeList == null || sizeList.size() == 0) {
            return mCamera.new Size(240, 320);
        }
        int gapLast = Integer.MAX_VALUE;
        for (Size size : sizeList) {
            float ratio = (float) size.height / size.width;
            if (ratio == 0.75f) {
                int gap = size.width - widthView;
                if (gap < gapLast && gap > 0) {
                    gapLast = gap;
                    sizeReturn = size;
                }
            }
        }
        return sizeReturn;
    }

    private Size getSizeBySquare(List<Size> sizeList, int widthView) {
        Size sizeReturn = null;
        if (sizeList == null || sizeList.size() == 0) {
            return mCamera.new Size(640, 640);
        }
        Collections.sort(sizeList, new Comparator<Size>() {
            @Override
            public int compare(Size o1, Size o2) {
                if (o1.width == o2.width) {
                    return 0;
                } else if (o1.width > o2.width) {
                    return 1;
                } else {
                    return -1;
                }
            }
        });
        for (Size size : sizeList) {
            Log.d(TAG, "getSize width:" + size.width + " x" + size.height);

            if (size.width < 640) {
                continue;
            }

            float ratio = (float) size.width / size.height;
            if (abs(ratio - 1.0) < 0.1) {
                sizeReturn = size;
                Log.d(TAG, "getSize width:" + sizeReturn.width + " x" + sizeReturn.height);
                return sizeReturn;
            }
        }

        if (sizeReturn == null) {
            return mCamera.new Size(640, 480);
        }
        return sizeReturn;
    }

    public int setCameraDisplayOrientation(Context context, int cameraId) {
        Camera.CameraInfo info = new Camera.CameraInfo();
        Camera.getCameraInfo(cameraId, info);

        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        int rotation = wm.getDefaultDisplay().getRotation();
        int degrees = 0;
        switch (rotation) {
            case Surface.ROTATION_0:
                degrees = 0;
                break;
            case Surface.ROTATION_90:
                degrees = 90;
                break;
            case Surface.ROTATION_180:
                degrees = 180;
                break;
            case Surface.ROTATION_270:
                degrees = 270;
                break;
        }

        int result;
        if (info.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            result = (info.orientation + degrees) % 360;
            result = (360 - result) % 360;  // compensate the mirror
        } else {  // back-facing
            result = (info.orientation - degrees + 360) % 360;
        }
        setDegress(result);

        Log.i(TAG, "rotation:" + rotation + " degrees:" + result);

        return result;
    }

    public void doStopCamera() {
        if (null != mCamera) {
            mCamera.setPreviewCallbackWithBuffer(null);
            mCamera.stopPreview();
            isPreviewing = false;
            mCamera.release();
            mCamera = null;
        }
    }

    public Camera.Parameters getCameraParams() {
        if (mCamera != null) {
            mParams = mCamera.getParameters();
            return mParams;
        }
        return null;
    }

//    public Camera getCameraDevice() {
//        return mCamera;
//    }

    public Camera getCameraDevice() {
        if (mCamera == null) {
            try {
                mCamera = Camera.open(mCameraId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return mCamera;
    }

    public int getCameraId() {
        return mCameraId;
    }

    public static int degress = 0;

    public static int getDegress() {
        return degress;
    }

    public static void setDegress(int degress) {
        CameraInterface.degress = degress;
    }


    public void setCameraDevice(Camera camera) {
        mCamera = camera;
    }

    public void resetCameraOrientation(Activity activity) {
        if (mCamera != null) {
            mCamera.setDisplayOrientation(setCameraDisplayOrientation(activity, mCameraId));
        }
    }
}
