package ejoy.livedetector;

import android.content.res.AssetManager;

/**
 * Created by fengyijun on 2016/11/3.
 */

public class NdkJniUtils {

    // 活体识别初始化
    public static native void initialization(AssetManager ass, String detectFilePath, String landMarkFilePath, String modal, double tooLarge, double tooSmall, double tooRight, double tooLeft, double tooTop, double tooBottom);

    // 识别结果
    public static native byte[] liveDetect(byte[] imageBuff, int width, int height);

    public static native byte[] resultFromJNI(byte[] imageBuff, int width, int height, int action, int format, int degerss);

}
