package ejoy.livedetector;

import android.util.Log;

import java.io.Serializable;

/**
 * Created by x<PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/1/18.
 * God loves all
 */
public class FaceRecognitionInterface implements FaceRecognition {

    /**
     * 图片验证
     *
     * @param erp
     * @param imgData  图片数据
     * @param callBack 返回结果回调
     */
    @Override
    public void checkLogin(String erp, String imgBase64, String imgJsonArray, String key, CheckCallBack callBack) {

        checkLoginOrUpload(0, erp, imgBase64, imgJsonArray, key, callBack);

    }

    /**
     * 图片上传
     *
     * @param erp
     * @param imgData  图片数据
     * @param callBack 返回结果回调
     */
    @Override
    public void checkUpload(String erp, String imgBase64, String imgJsonArray, String key, CheckCallBack callBack) {
        checkLoginOrUpload(1, erp, imgBase64, imgJsonArray, key, callBack);
    }


    /**
     * @param code     0:登陆   1：上传
     * @param erp
     * @param imgData
     * @param callBack
     */
    private void checkLoginOrUpload(int code, String erp, String imgBase64, String imgJsonArray, String key, CheckCallBack callBack) {

        if (null == erp || erp.length() == 0) {

            Log.e("FaceRecognition", "erp is null or empty");
            return;
        }

        switch (code) {
            case 0:
                new UploadAndCheck(callBack).updateOrCheckImage(erp, imgBase64, imgJsonArray, key, false);
                break;
            case 1:
                new UploadAndCheck(callBack).updateOrCheckImage(erp, imgBase64, imgJsonArray, key, true);
                break;
        }

    }

    public interface CheckCallBack extends Serializable {
        void onDetechSuccess();

        void onDetechFailure(String result);
    }
}
