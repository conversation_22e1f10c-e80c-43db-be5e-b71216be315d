package ejoy.livedetector.model;

import java.util.UUID;

import ejoy.livedetector.util.ImagesUtil;
import ejoy.livedetector.util.RSAAESEncryption;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>yang on 2017/2/7.
 */

public class DetectResult {

    int FeedBackCode;
    int livenessOK; //0:活体检测通过 -1:未通过
    byte[] data;//图像二进制流


    public String imgBase64;    // 一张情况
    public String key;

    public String[] imgArrays; // 多张情况


    public DetectResult(byte[] bytes) {
        this.FeedBackCode = bytes[0];
        this.livenessOK = bytes[1];
        this.data = new byte[bytes.length - 2];
        int length = bytes.length;
        for (int i = 0; i < length - 2; i++) {
            this.data[i] = bytes[i + 2];
        }

//        String keyStr = System.currentTimeMillis() + "abc";
        String keyStr = UUID.randomUUID().toString();

        try {
            imgBase64 = RSAAESEncryption.getImgBase64EncryptionStr(keyStr, data);
            key = RSAAESEncryption.encryptionAESKey(keyStr);
        } catch (Exception e) {
            e.printStackTrace();
        }


        imgArrays = ImagesUtil.getImgArrays(keyStr);

    }

    public String[] getImgArrays() {
        return imgArrays;
    }


    public int getFeedBackCode() {
        return FeedBackCode;
    }

    public int getLivenessOK() {
        return livenessOK;
    }

    public String getToken() {
        return "b8c146ff-3afc-41ac-b7cf-9715cce73270";
    }


    public byte[] getData() {
        return data;
    }

    public String getImgBase64() {
        return imgBase64;
    }

    public String getKey() {
        return key;
    }
}
