<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".LiveDetectActivity">

    <View
        android:id="@+id/me_detect_top_bg"
        android:layout_width="wrap_content"
        android:layout_height="100dp"
        android:background="@color/detect_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/me_detect_scan"
        android:layout_width="282dp"
        android:layout_height="282dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/me_detect_top_bg">
        <!--摄像画面-->
        <ejoy.livedetector.camera.CameraSurfaceView
            android:id="@+id/camera_surface_view"
            android:layout_width="234dp"
            android:layout_height="234dp"
            android:layout_gravity="center" />
        <!--摄像头准备前遮挡-->
<!--        <View-->
<!--            android:id="@+id/me_detect_scan_cover"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:background="@drawable/jdme_liveness_face" />-->
        <View
            android:id="@+id/me_detect_scan_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white" />
        <!--动态扫描线-->
        <View
            android:id="@+id/me_detect_scan_line"
            android:layout_width="match_parent"
            android:layout_height="15dp"
            android:background="@drawable/detect_scan_line" />
        <!--说明文字-->
        <TextView
            android:id="@+id/tv_detect_tip"
            android:layout_width="234dp"
            android:layout_height="80dp"
            android:layout_gravity="center_horizontal|top"
            android:background="@color/camera_title"
            android:gravity="center_horizontal|bottom"
            android:paddingBottom="5dp"
            android:text="@string/me_login_scan_opening"
            android:textColor="@color/detect_white"
            android:textSize="16sp" />
        <!--白色遮罩-->
        <View
            android:id="@+id/me_detect_camera_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/detect_face_frame_bg" />
        <!--旋转圆环-->
        <View
            android:id="@+id/me_detect_camera_ring"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/detect_face_frame" />
    </FrameLayout>

    <View
        android:id="@+id/me_detect_start_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/detect_white"
        app:layout_constraintBottom_toTopOf="@+id/tv_action_tip"
        app:layout_constraintEnd_toStartOf="@id/me_detect_scan"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/me_detect_scan" />

    <View
        android:id="@+id/me_detect_right_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/detect_white"
        app:layout_constraintBottom_toTopOf="@+id/tv_action_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/me_detect_scan"
        app:layout_constraintTop_toTopOf="@id/me_detect_scan" />
    <!--外层说明文字-->

    <!--<include layout="@layout/detect_camera_overlay" />-->

    <TextView
        android:id="@+id/tv_action_tip"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/detect_white"
        android:gravity="center_horizontal"
        android:paddingTop="100dp"
        android:text="@string/detect_prestart_tip"
        android:textColor="@color/article_title"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/me_detect_scan" />


</androidx.constraintlayout.widget.ConstraintLayout>
