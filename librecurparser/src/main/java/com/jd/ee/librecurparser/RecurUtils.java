package com.jd.ee.librecurparser;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

public class RecurUtils {

    private static final List<String> availableIds = Arrays.asList(TimeZone.getAvailableIDs());

    static boolean isSameDay(long date1, long date2) {
        Calendar c1 = Calendar.getInstance();
        c1.setTimeInMillis(date1);
        Calendar c2 = Calendar.getInstance();
        c2.setTimeInMillis(date2);
        return isSameDay(c1, c2);
    }

    static boolean isSameDay(Calendar date1, Calendar date2) {
        if(date1.get(Calendar.YEAR) == date2.get(Calendar.YEAR) &&
                date1.get(Calendar.MONTH) == date2.get(Calendar.MONTH)) {
            int day1 = date1.get(Calendar.DAY_OF_MONTH);
            int day2 = date2.get(Calendar.DAY_OF_MONTH);
            return day1 == day2;
        }
        return false;
    }

    static List<Date> getDateBetween(long beginTime, long endTime, TimeZone timeZone) {
        List<Date> mDateList = new ArrayList<>();
        //记录往后每一天的时间，用来和最后一天到做对比。这样就能知道什么时候停止了。
        long allTimeEnd = 0;
        //记录当前到个数(相当于天数)
        long currentFlag = 0;

        while (endTime > allTimeEnd) {
            allTimeEnd = beginTime + currentFlag * 24 * 60 * 60 * 1000;
            Date dateTime = new Date(beginTime + currentFlag * 24 * 60 * 60 * 1000);
            long newEndTime = makeNewTimeStamp(dateTime.getTime(), endTime, timeZone);
            if (newEndTime < endTime) {
                mDateList.add(dateTime);
            }
            currentFlag++;
        }
        return mDateList;
    }

    public static long makeNewTimeStamp(long day, long time, TimeZone timeZone) {
        Calendar date = Calendar.getInstance(timeZone);
        date.setTimeInMillis(day);

        Calendar timeCalendar = Calendar.getInstance(timeZone);
        timeCalendar.setTimeInMillis(time);

        date.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
        date.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
        date.set(Calendar.SECOND, timeCalendar.get(Calendar.SECOND));
        date.set(Calendar.MILLISECOND, timeCalendar.get(Calendar.MILLISECOND));

        return date.getTimeInMillis();
    }

    public static Calendar startOfDay(Date date) {
        Calendar dateCal = Calendar.getInstance();
        dateCal.setTime(date);

        Calendar cal = Calendar.getInstance();
        cal.set(dateCal.get(Calendar.YEAR), dateCal.get(Calendar.MONTH), dateCal.get(Calendar.DAY_OF_MONTH), 0, 0,0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal;
    }

    public static Calendar endOfDay(Date date) {
        Calendar dateCal = Calendar.getInstance();
        dateCal.setTime(date);

        Calendar cal = Calendar.getInstance();
        cal.set(dateCal.get(Calendar.YEAR), dateCal.get(Calendar.MONTH), dateCal.get(Calendar.DAY_OF_MONTH), 24, 0,0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal;
    }

    public static <T extends BaseAppointment> List<T> splitMultiDayAppointment(T appointment) {
        if (RecurUtils.isSameDay(appointment.start, appointment.end)) return Collections.emptyList();
        List<T> result = new ArrayList<>();
        List<Date> dates = RecurUtils.getDateBetween(appointment.start, appointment.end, getTimeZone(appointment.getStartTimeZone(), TimeZone.getDefault()));

        for (int index = 0; index < dates.size(); index++) {
            Date date = dates.get(index);
            T newApp = (T) appointment.clone();
            if (index == 0) {
                newApp.endTime = RecurUtils.endOfDay(date).getTimeInMillis();
            } else if (index == dates.size() - 1) {
                newApp.beginTime = RecurUtils.startOfDay(date).getTimeInMillis();
            } else {
                newApp.beginTime = RecurUtils.startOfDay(date).getTimeInMillis();
                newApp.endTime = RecurUtils.endOfDay(date).getTimeInMillis();
            }
            result.add(newApp);
        }

        return result;
    }

    public static TimeZone getTimeZone(String timeZoneId, TimeZone fallback) {
        if (timeZoneId != null) {
            try {
                if (availableIds.contains(timeZoneId)) {
                    return TimeZone.getTimeZone(timeZoneId);
                } else {
                    return fallback;
                }
            } catch (Exception e) {
                return fallback;
            }
        } else {
            return fallback;
        }
    }
}