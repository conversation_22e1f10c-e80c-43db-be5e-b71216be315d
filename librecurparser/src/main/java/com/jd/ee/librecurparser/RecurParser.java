package com.jd.ee.librecurparser;

import static com.jd.ee.librecurparser.RecurUtils.getTimeZone;

import android.text.TextUtils;

import org.dmfs.rfc5545.DateTime;
import org.dmfs.rfc5545.Duration;
import org.dmfs.rfc5545.recur.RecurrenceRule;
import org.dmfs.rfc5545.recur.RecurrenceRuleIterator;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;


public class RecurParser<T extends BaseAppointment> {

    public List<Long> parseRecurrenceRule(RecurRule rule) throws RecurrenceParseException {
        List<Long> list = new ArrayList<>();
        try {
            RecurrenceRule rrule = new RecurrenceRule(rule.getRrule());

            TimeZone defaultTimeZone = TimeZone.getDefault();
            TimeZone timeZone = getTimeZone(rule.getTimeZoneId(), defaultTimeZone);

            DateTime limitStart = new DateTime(timeZone, rule.getLimitStart());
            DateTime limitEnd = new DateTime(timeZone, rule.getLimitEnd());

            Integer count = rrule.getCount();
            if (count == null) {
                DateTime until = rrule.getUntil();

                if (until == null && rule.getRepeatEnd() != null && rule.getRepeatEnd() != 0) {
                    until = new DateTime(timeZone, rule.getRepeatEnd());
                }
                if (until == null || limitEnd.before(until)) {
                    until = limitEnd;
                }
                rrule.setUntil(until);

                if (rrule.isInfinite()) {
                    until = limitStart.addDuration(new Duration(1, 30 * 6, 0));
                    rrule.setUntil(until);
                }
            }

            DateTime start = new DateTime(timeZone, rule.getStart());

            RecurrenceRuleIterator iterator = rrule.iterator(start);
            iterator.fastForward(limitStart);

            while (iterator.hasNext()) {
                long dateTime = iterator.nextMillis();
                list.add(dateTime);
            }
        } catch (Exception e) {
            throw new RecurrenceParseException(e);
        }
        return list;
    }

    public List<Long> parseRecurrencesByCount(RecurRule rule, int count) throws RecurrenceParseException {
        List<Long> list = new ArrayList<>();
        try {
            RecurrenceRule rrule = new RecurrenceRule(rule.getRrule());
            rrule.setCount(count);

            TimeZone defaultTimeZone = TimeZone.getDefault();
            TimeZone timeZone = getTimeZone(rule.getTimeZoneId(), defaultTimeZone);

            DateTime repeatStartTime = new DateTime(timeZone, rule.getStart());
            RecurrenceRuleIterator iterator = rrule.iterator(repeatStartTime);

            while (iterator.hasNext()) {
                long dateTime = iterator.nextMillis();
                list.add(dateTime);
            }
        } catch (Exception e) {
            throw new RecurrenceParseException(e);
        }
        return list;
    }

    public List<T> parseAppointmentList(List<T> appointmentList, Long beginTime, Long endTime, AppointmentCreator<T> creator) {
        List<T> result = new ArrayList<>();
        List<T> removed = new ArrayList<>();
        // 处理重复和单次数据
        for (T appointment : appointmentList) {
            if (BaseAppointment.APPOINTMENT_TYPE_OCCURRENCE.equals(appointment.appointmentType) ||
                    BaseAppointment.APPOINTMENT_TYPE_EXCEPTION.equals(appointment.appointmentType)) {
                removed.add(appointment);
                continue;
            }

            if (appointment.isDeleted()) continue;

            if (BaseAppointment.APPOINTMENT_TYPE_MASTER.equals(appointment.appointmentType) && appointment.rrule != null) {
                if (appointment.repeatStart == appointment.repeatEnd) continue;
                List<T> list = parseRecurringMaster(appointment, beginTime, endTime, creator);
                result.addAll(list);
            } else {
                result.add(appointment);
            }
        }

        // 处理修改删除对冲数据
        for (T appointment : removed) {
            if (BaseAppointment.APPOINTMENT_TYPE_OCCURRENCE.equals(appointment.appointmentType)) {
                T item = findAppointmentItem(result, appointment.scheduleId, appointment.start, appointment.getEnd());
                if (item != null) {
                    result.remove(item);
                }
            } else if (BaseAppointment.APPOINTMENT_TYPE_EXCEPTION.equals(appointment.appointmentType)) {
                T item = findAppointmentItem(result, appointment.scheduleId, appointment.originalStart, appointment.getEnd());
                if (item != null) {
                    result.remove(item);
                }
                result.add(appointment);
            }
        }

        Collections.sort(result);

        return result;
    }

    private List<T> parseRecurringMaster(T recurringMaster, Long beginTime, Long endTime, AppointmentCreator<T> creator) {
        List<T> list = new ArrayList<>();
        RecurRule rule = new RecurRule(recurringMaster.rrule, recurringMaster.repeatStart, recurringMaster.start);
        rule.setTimeZoneId(recurringMaster.getStartTimeZone());
        rule.setLimitStart(beginTime);
        rule.setLimitEnd(endTime);
        rule.setRepeatEnd(recurringMaster.getRepeatEnd());

        try {
            List<Long> dates = parseRecurrenceRule(rule);
            for (Long date : dates) {
                TimeZone timeZone = getTimeZone(recurringMaster.getStartTimeZone(), TimeZone.getDefault());
                T appointment = createNewAppointment(recurringMaster, date, creator, timeZone);
                list.add(appointment);
            }
        } catch (RecurrenceParseException e) {
            e.printStackTrace();
        }
        return list;
    }

    private T createNewAppointment(T recurringMaster, long date, AppointmentCreator<T> creator, TimeZone timeZone) {
        T appointment = creator.create(recurringMaster);

        long duration = recurringMaster.getEnd() - recurringMaster.getStart();
        long start = date; //RecurUtils.makeNewTimeStamp(date, appointment.getStart(), timeZone);

        appointment.setStart(start);
        appointment.setEnd(appointment.getStart() + duration);
        appointment.setOriginalStart(start);
        appointment.setRelatedDate(start);

        return appointment;
    }

    private T findAppointmentItem(List<T> list, String id, long beginTime, long endTime) {
        T ret = null;
        for (T item : list) {
            if (beginTime != 0) {
                if (TextUtils.equals(item.scheduleId, id) && item.getStart() == beginTime /* && item.endTime == endTime */) {
                    ret = item;
                    break;
                }
            } else {
                if (TextUtils.equals(item.scheduleId, id)) {
                    ret = item;
                    break;
                }
            }
        }
        return ret;
    }

    public interface AppointmentCreator<T extends BaseAppointment> {
        T create(T recurrenceAppointment);
    }
}