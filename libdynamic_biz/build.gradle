apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

//    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    namespace 'com.jd.oa.dynamic.biz'
}

dependencies {
//    implementation ('com.jd.jrapp.bm.common:jsdynamic:jrapp-0.9.16-202208090033-SNAPSHOT'){
//        force true
//        exclude group: "com.eclipsesource.v8"
//    }

//    implementation 'com.chenenyu.router:router:1.5.2'
//    kapt 'com.chenenyu.router:compiler:1.5.1'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation COMPILE_SUPPORT.appcompat
    implementation COMPILE_SUPPORT.design
    implementation COMPILE_SUPPORT.annotations
    implementation COMPILE_SUPPORT.recyclerview
    implementation COMPILE_COMMON.gson
    implementation "com.github.bumptech.glide:glide:$glideVersion"
    implementation libs.lib.storage
    implementation project(path: ':libanalyze')
    implementation project(path: ':common')
}