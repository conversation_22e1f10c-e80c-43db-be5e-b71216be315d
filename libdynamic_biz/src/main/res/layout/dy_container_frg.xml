<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/mTitleContainer"
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView
                android:id="@+id/btn_cancel"
                android:layout_width="46dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:paddingStart="10dp"
                android:paddingTop="10dp"
                android:paddingEnd="16dp"
                android:paddingBottom="10dp"
                android:src="@drawable/dy_biz_back" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/comm_text_title"
                android:textSize="@dimen/comm_text_title_large"
                android:textStyle="bold"
                tools:text="标题" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_dynamic_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_dynamic_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="200dp"
        android:layout_centerHorizontal="true"
        android:visibility="gone"
        tools:visibility="gone">

        <androidx.core.widget.ContentLoadingProgressBar
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="30dp"
            android:layout_height="30dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_dynamic_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="200dp"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/layout_error_png"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/jdme_dynamic_error" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:text="@string/me_dynamic_loading_failed" />

        <TextView
            android:id="@+id/retry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:text="@string/me_dymamic_retry_button"
            android:textColor="@color/actionsheet_blue" />
    </LinearLayout>
</RelativeLayout>