package com.jd.oa.dynamic.biz.module;

import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.dynamic.biz.DyCommonKt;
import com.jd.oa.dynamic.biz.DynamicCrossPlatformResultListener;
import com.jd.oa.dynamic.module.MEPan;

import java.util.HashMap;

public class MEPanImpl extends MEPan {
    @Override
    public void downloadFile(HashMap<String, Object> params, JsCallBack callback) {
        CrossPlatformPan.downloadFile(AppBase.getTopActivity(), DyCommonKt.toSS(params), new DynamicCrossPlatformResultListener<String>(callback, "MEPan::downloadFile") {
            @Override
            public void success(String domain, String... t) {
                HashMap<String, String> map = new HashMap<>();
                if (t != null && t.length > 0) {
                    map.put("filePath", t[0]);
                }
                DyCommonKt.success(callback, map, domain);
            }
        });
    }

    @Override
    public void openFile(HashMap<String, Object> params, JsCallBack callback) {
        CrossPlatformPan.openFile(AppBase.getTopActivity(), DyCommonKt.toSS(params), new DynamicCrossPlatformResultListener<String>(callback, "MEPan::openFile") {
            @Override
            public void success(String domain, String... t) {
                HashMap<String, String> map = new HashMap<>();
                if (t != null && t.length > 0) {
                    map.put("filePath", t[0]);
                }
                DyCommonKt.success(callback, map, domain);
            }
        });
    }
}
