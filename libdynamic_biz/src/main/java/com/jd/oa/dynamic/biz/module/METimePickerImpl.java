package com.jd.oa.dynamic.biz.module;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.basic.DateSelectBasic;
import com.jd.oa.dynamic.module.METimePicker;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class METimePickerImpl extends METimePicker {

    private void failure(JsCallBack callBack) {
        List<Object> ans = new ArrayList<>();
        Map<String, Object> m = new HashMap<>();
        m.put("statusCode", 1);
        ans.add(m);
        callBack.call(ans);
    }

    @Override
    public void selectDate(@NonNull Map<String, Object> map, @NonNull final JsCallBack callBack) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            failure(callBack);
            return;
        }
        activity.runOnUiThread(() -> DateSelectBasic.selectDate(activity, map, result -> {
            if (result == null) {
                failure(callBack);
            } else {
                List<Object> ans = new ArrayList<>();
                try {
                    //noinspection unchecked
                    ans.add((Map<String, Object>) result);
                } catch (Exception e) {
                    failure(callBack);
                    return;
                }
                callBack.call(ans);
            }
        }));
    }


    public void selectDateTime(@NonNull Map<String, Object> map,
                               @NonNull final JsCallBack callBack) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            failure(callBack);
            return;
        }
        activity.runOnUiThread(() -> DateSelectBasic.selectTime(activity, map, result -> {
            if (result == null) {
                failure(callBack);
            } else {
                List<Object> ans = new ArrayList<>();
                try {
                    //noinspection unchecked
                    ans.add((Map<String, Object>) result);
                } catch (Exception e) {
                    failure(callBack);
                    return;
                }
                callBack.call(ans);
            }
        }));
    }

}
