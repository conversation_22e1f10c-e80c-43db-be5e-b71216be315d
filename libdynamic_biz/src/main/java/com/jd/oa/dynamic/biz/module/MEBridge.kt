package com.jd.oa.dynamic.biz.module

import com.jd.jrapp.dy.annotation.JSFunction
import com.jd.jrapp.dy.api.JsModule
import com.jd.oa.dynamic.MEDynamic
import com.jd.oa.dynamic.listener.DynamicOperatorListener

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/19 15:54
 */
class MEBridge : JsModule() {

    @JSFunction
    fun setTitle(title: String) {
        val param: MutableMap<String, Any> = HashMap()
        param[DynamicOperatorListener.KEY_ACTION] = DynamicOperatorListener.ACTION_SET_TITLE
        param["title"] = title
        MEDynamic.getInstance().opratorCallback(super.instanceId, param)
    }

    @JSFunction
    fun updateSuccess(dynamicId: String) {
        val param: MutableMap<String, Any> = HashMap()
        param[DynamicOperatorListener.KEY_ACTION] = DynamicOperatorListener.ACTION_RELOAD
        param["dynamicId"] = dynamicId
        MEDynamic.getInstance().opratorCallback(super.instanceId, param)
    }

    @JSFunction
    fun sendOperationResult(params: Map<String?, Any?>?) {
        val p = mutableMapOf<String, Any>()
        p[DynamicOperatorListener.KEY_ACTION] = DynamicOperatorListener.ACTION_SEND_RESULT
        params?.runCatching {
            params.forEach { (key, value) ->
                if (!key.isNullOrEmpty() && value != null) {
                    p[key] = value
                }
            }
        }
        MEDynamic.getInstance().opratorCallback(super.instanceId, p)
    }

    override fun release() {
        super.release()
    }

}