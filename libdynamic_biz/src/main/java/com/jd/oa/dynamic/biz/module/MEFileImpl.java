package com.jd.oa.dynamic.biz.module;

import android.app.Activity;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiFile;
import com.jd.oa.abilities.api.FileChooserBuilder;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.dynamic.biz.DyCommonKt;
import com.jd.oa.dynamic.biz.DynamicCrossPlatformResultListener;
import com.jd.oa.dynamic.module.MeFile;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MEFileImpl extends MeFile {
    @Override
    public void chooseFile(Map<String, Object> options, JsCallBack callback) {
//        CrossPlatformFile.chooseFile(AppBase.getTopActivity(), DyCommonKt.toSS(options), new DynamicCrossPlatformResultListener<JSONArray>(callback, "MEFile::chooseFile") {
//            @Override
//            public void success(String domain, JSONArray... t) {
//                if (t == null || t.length == 0) {
//                    failure("-1", "The user choose nothing");
//                } else {
//                    try {
//                        JSONArray array = t[0];
//                        Map<String, Object> result = new HashMap<>();
//
//                        List<Map<String, Object>> ans = new ArrayList<>();
//                        result.put("fileList", ans);
//                        Gson gson = new Gson();
//                        for (int i = 0; i < array.length(); i++) {
//                            Object o = array.get(i);
//                            if (o instanceof JSONObject) {
//                                Type type = new TypeToken<Map<String, Object>>() {
//                                }.getType();
//                                Map<String, Object> map = gson.fromJson(((JSONObject) o).toString(), type);
//                                ans.add(map);
//                            }
//                        }
//                        DyCommonKt.success(callback, result, domain);
//                    } catch (Exception e) {
//                        failure("-1", "Local exception: " + e.getMessage());
//                    }
//                }
//            }
//        });

        FileChooserBuilder builder = new FileChooserBuilder();
        try {
            Map<String, String> ssMap = DyCommonKt.toSS(options);
            String count = ssMap.get("count");
            if (count != null) {
                builder.count(StringUtils.convertToInt(count));
            }
            String localFileDisable = ssMap.get("localFileDisable");
            if (localFileDisable != null) {
                builder.localFileDisable(Boolean.parseBoolean(localFileDisable));
            }
            String joyBoxDisable = ssMap.get("joyBoxDisable");
            if (joyBoxDisable != null) {
                builder.joyBoxDisable(Boolean.parseBoolean(joyBoxDisable));
            }
            String joySpaceEnable = ssMap.get("joySpaceEnable");
            if (joySpaceEnable != null) {
                builder.joySpaceEnable(Boolean.parseBoolean(joySpaceEnable));
            }
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_JS, "JsFile chooseFile error", e);
        }

        Activity activity = AppBase.getTopActivity();
        if (activity instanceof FragmentActivity) {
            FragmentActivity fragmentActivity = (FragmentActivity) activity;
            ApiFile.chooseFile(fragmentActivity, builder, new IServiceCallback<JSONArray>() {
                @Override
                public void onResult(boolean success, @Nullable JSONArray paths, @Nullable String error) {

                    String domain = "MEFile::chooseFile";

                    DynamicCrossPlatformResultListener<JSONArray> resultListener = new DynamicCrossPlatformResultListener<>(callback, domain);

                    try {
                        if (success && paths != null && paths.length() > 0) {
                            try {
                                Map<String, Object> result = new HashMap<>();
                                List<Map<String, Object>> ans = new ArrayList<>();
                                result.put("fileList", ans);
                                Gson gson = new Gson();
                                for (int i = 0; i < paths.length(); i++) {
                                    Object o = paths.get(i);
                                    if (o instanceof JSONObject) {
                                        Type type = new TypeToken<Map<String, Object>>() {
                                        }.getType();
                                        Map<String, Object> map = gson.fromJson(((JSONObject) o).toString(), type);
                                        ans.add(map);
                                    }
                                }
                                DyCommonKt.success(callback, result, domain);
                            } catch (Exception e) {
                                resultListener.failure("-1", "Local exception: " + e.getMessage());
                            }
                        } else {
                            resultListener.failure("-1", "The user choose nothing");
                        }
                    } catch (Exception e) {
                        resultListener.failure("-1", "Local exception: " + e.getMessage());
                    }
                }
            });
        }
    }

}
