package com.jd.oa.dynamic.biz

import com.jd.jrapp.dy.api.JsCallBack
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.utils.JsonUtils

val TAG = "dynamic_biz"

fun Map<String, Any?>.toSS(): HashMap<String, String?> {
    val ans = HashMap<String, String?>()
    forEach {
        ans[it.key] = it.value?.toString()
    }
    return ans
}


@JvmOverloads
fun JsCallBack.failure(code: String, message: String, domain: String? = null) {
    val ans: MutableList<Any> = ArrayList()
    val m: MutableMap<String, Any> = HashMap()
    m["statusCode"] = code
    m["errorMessage"] = message
    ans.add(m)
    call(ans)
    if (domain != null) {
        MELogUtil.localW(TAG, "$domain failure: code = $code message = $message")
    }
}

@JvmOverloads
fun JsCallBack.success(result: MutableMap<String, out Any>? = null, domain: String? = null) {
    val ans: MutableList<Any> = ArrayList()
    val m = HashMap<String, Any?>()
    m["statusCode"] = "0"
    result?.entries?.forEach {
        m[it.key] = it.value
    }
    ans.add(m)
    call(ans)
    if (domain != null) {
        MELogUtil.localI(TAG, "$domain success: result = ${JsonUtils.getJsonString(m)}")
    }
}