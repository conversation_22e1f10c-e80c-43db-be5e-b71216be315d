package com.jd.oa.dynamic.biz.module

import android.content.Context
import com.jd.jrapp.dy.annotation.JSFunction
import com.jd.jrapp.dy.api.JsCallBack
import com.jd.jrapp.dy.api.JsModule
import com.jd.oa.dynamic.biz.event.JueEventCallback
import com.jd.oa.dynamic.biz.failure
import com.jd.oa.dynamic.biz.success
import com.jd.oa.eventbus.JmEventDispatcher
import com.jd.oa.eventbus.JmEventVoidReturnProcessor
import com.jd.oa.ext.toMap
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/1 15:01
 */
class MEEventImpl : JsModule() {

    private val uniqueId = hashCode()

    companion object {
        const val KEY_EVENT_NAME = "eventName"
        const val KEY_EVENT_PARAMS = "eventParams"
    }

    @JSFunction
    fun sendEvent(params: Map<String?, Any?>?, jsCallBack: JsCallBack?) {
        if (params == null) return
        val eventName = params[KEY_EVENT_NAME] as? String?
        if (eventName.isNullOrEmpty()) return
        val eventParams = params[KEY_EVENT_PARAMS]
        sendEventInner(eventName, eventParams, jsCallBack)
    }

    /**
     *
     * 普通事件发出去即可，可根据eventName具化返回类型，也可以用通用的流程
     *
     */
    private fun sendEventInner(eventName: String, eventParams: Any?, jsCallBack: JsCallBack?) {
        JmEventDispatcher.dispatchEvent(
            context,
            eventName,
            eventParams,
            JueEventCallback(jsCallBack)
        )
        jsCallBack?.success()
    }


    @JSFunction
    fun onNativeEvent(params: Map<String?, Any?>?, jsCallBack: JsCallBack?) {
        if (jsCallBack == null) return
        if (params == null) {
            jsCallBack.failure("-1", "param is required")
            return
        }
        val eventName = params[KEY_EVENT_NAME] as? String?
        if (eventName.isNullOrEmpty()) {
            jsCallBack.failure("-1", "eventName is required")
            return
        }
        JmEventDispatcher.registerProcessor(
            JueEventProcessor(uniqueId, jsCallBack, eventName)
        )
        //注册成功
        jsCallBack.success()
    }

    @JSFunction
    fun offNativeEvent(params: Map<String?, Any?>?, jsCallBack: JsCallBack?) {
        if (params == null) return
        val eventName = params[KEY_EVENT_NAME] as? String?
        if (eventName.isNullOrEmpty()) return
        JmEventDispatcher.unregisterAll { processor ->
            if (processor is JueEventProcessor)
                processor.uniqueId == uniqueId && processor.hasEvent(eventName)
            else
                false
        }
        //注销成功
        jsCallBack?.success()
    }


    override fun release() {
        JmEventDispatcher.unregisterAll { processor ->
            if (processor is JueEventProcessor)
                processor.uniqueId == uniqueId
            else
                false
        }
    }

    private class JueEventProcessor(
        val uniqueId: Int, val jsCallBack: JsCallBack?, vararg events: String
    ) : JmEventVoidReturnProcessor<Any>(*events) {

        override fun processEvent(
            context: Context,
            event: String,
            args: Any?,
        ) {
            jsCallBack?.runCatching {
                val result = mutableMapOf<String, Any>()
                args?.run {
                    //callback参数基本数据类型 、List、Map，其他java 类不支持
                    if (args is JSONObject) {
                        result += Pair("eventParams", args.toMap())
                    } else {
                        result += Pair("eventParams", args)
                    }
                }
                success(result)
            }
        }
    }
}