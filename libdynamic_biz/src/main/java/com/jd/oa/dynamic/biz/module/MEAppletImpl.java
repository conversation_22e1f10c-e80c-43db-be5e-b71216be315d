package com.jd.oa.dynamic.biz.module;

import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.crossplatform.CrossPlatformApplet;
import com.jd.oa.dynamic.biz.DyCommonKt;
import com.jd.oa.dynamic.biz.DynamicCrossPlatformResultListener;
import com.jd.oa.dynamic.module.MEApplet;

import java.util.HashMap;

public class MEAppletImpl extends MEApplet {
    @Override
    public void openUrl(HashMap<String, Object> params, JsCallBack callback) {
        CrossPlatformApplet.openUrl(AppBase.getTopActivity(), DyCommonKt.toSS(params), new DynamicCrossPlatformResultListener<>(callback, "MEApplet::openUrl"));
    }
}
