package com.jd.oa.dynamic.biz.module;

import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.crossplatform.CrossPlatformShare;
import com.jd.oa.dynamic.biz.DyCommonKt;
import com.jd.oa.dynamic.biz.DynamicCrossPlatformResultListener;
import com.jd.oa.dynamic.module.MEShare;

import java.util.HashMap;

public class MEShareImpl extends MEShare {
    @Override
    public void shareNormal(HashMap<String, Object> args, final JsCallBack callback) {
        CrossPlatformShare.shareNormal(AppBase.getTopActivity(), DyCommonKt.toSS(args)
                , new DynamicCrossPlatformResultListener<HashMap<String, String>>(callback, "MEShare::shareNormal") {
                    @Override
                    public void success(String domain, HashMap<String, String>... t) {
                        if (t == null || t.length == 0) {
                            failure("-1", "Local error:");
                        } else {
                            DyCommonKt.success(callback, t[0], domain);
                        }
                    }
                });
    }

    @Override
    public void shareSystem(HashMap<String, Object> args, final JsCallBack callback) {
        CrossPlatformShare.shareSystem(AppBase.getTopActivity(), DyCommonKt.toSS(args)
                , new DynamicCrossPlatformResultListener<HashMap<String, String>>(callback, "MEShare::shareSystem") {
                    @Override
                    public void success(String domain, HashMap<String, String>... t) {
                        if (t == null || t.length == 0) {
                            failure("-1", "Local error:");
                        } else {
                            DyCommonKt.success(callback, t[0], domain);
                        }
                    }
                });
    }
}
