package com.jd.oa.dynamic.biz.module;

import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.crossplatform.CrossPlatformLocation;
import com.jd.oa.crossplatform.ErrorCode;
import com.jd.oa.dynamic.biz.DyCommonKt;
import com.jd.oa.dynamic.biz.DynamicCrossPlatformResultListener;
import com.jd.oa.dynamic.module.MELocation;

import java.util.HashMap;

public class MELocationImpl extends MELocation {
    @Override
    public void getLocation(HashMap<String, Object> args, JsCallBack handler) {
        CrossPlatformLocation.getLocation(AppBase.getTopActivity(), new DynamicCrossPlatformResultListener<Double>(handler, "MELocation::getLocation") {
            @Override
            public void success(String domain, Double... t) {
                try {
                    HashMap<String, Double> ans = new HashMap<>();
                    ans.put("latitude", t[0]);
                    ans.put("longitude", t[1]);
                    DyCommonKt.success(handler, ans, domain);
                } catch (Throwable throwable) {
                    DyCommonKt.failure(handler, ErrorCode.LOCAL_ERROR.getCode(), "Local exception: " + throwable.getMessage(), domain);
                }
            }
        });
    }
}
