package com.jd.oa.dynamic.biz;

import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.crossplatform.CrossPlatformResultListener;

public class DynamicCrossPlatformResultListener<T> implements CrossPlatformResultListener<T> {

    private final JsCallBack mCallBack;
    private final String mDomain;

    public DynamicCrossPlatformResultListener(JsCallBack callBack, String domain) {
        this.mCallBack = callBack;
        this.mDomain = domain;
    }

    @Override
    public final void failure(String code, String message) {
        this.failure(code, message, this.mDomain);
    }

    @Override
    public final void success(T... t) {
        this.success(this.mDomain, t);
    }

    public void failure(String code, String message, String domain) {
        if (mCallBack == null) return;
        DyCommonKt.failure(mCallBack, code, message, domain);
    }

    public void success(String domain, T... t) {
        if (mCallBack == null) {
            return;
        }
        DyCommonKt.success(mCallBack, null, domain);
    }
}
