package com.jd.oa.dynamic.biz;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;

import com.chenenyu.router.annotation.Route;
import com.jd.jrapp.dy.api.JRDyEngineManager;
import com.jd.jrapp.dy.api.JRDynamicView;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DownloadListener;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.dynamic.utils.DUtil;
import com.jd.oa.eventbus.DeeplinkCallbackProcessor;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.js.hybrid.utils.JsResultCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.MainHandler;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

// jdme://jm/sys/coolApp?mparam=%7B%22pageName%22%3A%22templateTaskCard%22%2C%22title%22%3A%22%E6%B5%8B%E8%AF%95%E6%A0%87%E9%A2%98%E6%A0%8F%22%7D
@Route(DeepLink.DYNAMIC_CONTAINER_FRG)
public class MEDynamicContainerFragment extends BaseFragment implements DynamicOperatorListener {
    private final Runnable mShowDynamicUI = new Runnable() {
        @Override
        public void run() {
            if (mInitialized && MEDynamic.getInstance().existJue(getPageName())) {
                showDynamicUI();
            }
        }
    };

    private LinearLayout mContainer;
    private View mErrorContainer;
    private ViewGroup mTitleContainer;
    private TextView mTitleView;
    private JRDynamicView dynamicView;
    private LinearLayout mLoading;
    private volatile boolean mInitialized = false;
    private boolean isDestroy = false;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            actionBar.hide();
        }
        return inflater.inflate(R.layout.dy_container_frg, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
    }

    private String getPageName() {
        Bundle bundle = getArguments();
        return DeeplinkExKt.getParamsKey(bundle, "pageName", "");
    }

    private String getDeepLinkCallbackId() {
        Bundle bundle = getArguments();
        return DeeplinkExKt.getParamsKey(bundle, DeeplinkCallbackProcessor.KEY_CALLBACK_ID, "");
    }

    private void initView(View view) {
        Bundle bundle = getArguments();
        String pageName = getPageName();
        if (pageName.trim().isEmpty()) {
            MELogUtil.localW(DyCommonKt.getTAG(), "Cannot get the name of the template, the page will be closed");
            MELogUtil.onlineW(DyCommonKt.getTAG(), "Cannot get the name of the template, the page will be closed");
            Activity activity = getActivity();
            if (activity != null) {
                activity.finish();
            }
            return;
        }
        mTitleContainer = view.findViewById(R.id.mTitleContainer);
        mTitleView = view.findViewById(R.id.tv_title);
        view.findViewById(R.id.btn_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Activity a = getActivity();
                if (a != null) {
                    a.finish();
                }
            }
        });
        String title = DeeplinkExKt.getParamsKey(bundle, "title", "");
        processTitle(title);
        mLoading = view.findViewById(R.id.ll_dynamic_loading);
        mErrorContainer = view.findViewById(R.id.ll_dynamic_error);
        mContainer = view.findViewById(R.id.ll_dynamic_container);
        view.findViewById(R.id.retry).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initAndCheck(getPageName());
            }
        });
        initAndCheck(pageName);
//        MEDynamic.getInstance().loadDynamicCard(view.getContext(), pageName, mContainer);
    }

    private void initAndCheck(String pageName) {
        MELogUtil.localW(DyCommonKt.getTAG(), pageName + " initAndCheck: " + MEDynamic.getInstance().existJue(pageName));
        MELogUtil.onlineW(DyCommonKt.getTAG(), pageName + " initAndCheck: " + MEDynamic.getInstance().existJue(pageName));
        if (JRDyEngineManager.instance().existJue(pageName)) {
            mErrorContainer.setVisibility(View.GONE);
            mLoading.setVisibility(View.GONE);
            dynamicView = new JRDynamicView(getContext());
            dynamicView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            dynamicView.initJue(pageName, false, null);
            mContainer.removeAllViews();
            mContainer.addView(dynamicView);
            MEDynamic.getInstance().addDynamicOperatorListener(this);
            mInitialized = true;
        } else {
            mLoading.setVisibility(View.VISIBLE);
            mErrorContainer.setVisibility(View.GONE);
            MEDynamic.getInstance().downloadCardById(pageName, new DownloadListener() {
                @Override
                public void success() {
                    if (isDestroy) {
                        return;
                    }
                    MELogUtil.localI(DyCommonKt.getTAG(), pageName + " downloadCardById: success " + MEDynamic.getInstance().existJue(pageName));
                    MELogUtil.onlineI(DyCommonKt.getTAG(), pageName + " downloadCardById: success " + MEDynamic.getInstance().existJue(pageName));
                    mErrorContainer.setVisibility(View.GONE);
                    initAndCheck(pageName);
                    mContainer.post(mShowDynamicUI);
                }

                @Override
                public void failed() {
                    if (isDestroy) {
                        return;
                    }
                    MELogUtil.localI(DyCommonKt.getTAG(), pageName + " downloadCardById: failed " + MEDynamic.getInstance().existJue(pageName));
                    MELogUtil.onlineI(DyCommonKt.getTAG(), pageName + " downloadCardById: failed " + MEDynamic.getInstance().existJue(pageName));
                    mErrorContainer.setVisibility(View.VISIBLE);
                    mLoading.setVisibility(View.GONE);
                }
            });
        }
    }

    private void processTitle(String title) {
        if (title.trim().isEmpty()) {
            if (mTitleContainer != null) {
                mTitleContainer.setVisibility(View.GONE);
            }
        } else {
            if (mTitleContainer != null) {
                mTitleContainer.setVisibility(View.VISIBLE);
                mTitleView.setText(title);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        mContainer.post(mShowDynamicUI);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        isDestroy = true;
        MainHandler.INSTANCE.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mContainer != null) {
                    MEDynamic.getInstance().removeDynamicOperatorListener(MEDynamicContainerFragment.this);
                }
            }
        }, 0);

    }

    private void showDynamicUI() {
        Context c = getContext();
        if (c != null) {
            int width = DUtil.px2dp(c, mContainer.getWidth());
            int height = DUtil.px2dp(c, mContainer.getHeight());
            JSONObject object = new JSONObject();
            try {
                object.put("width", width);
                object.put("height", height);

                JSONObject ans = new JSONObject();
                ans.put("viewSize", object.toString());
                Locale locale = LocaleUtils.getUserSetLocale(c);
                if (locale == null) {
                    locale = LocaleUtils.getSystemLocale();
                }
                String language = locale != null && locale.getLanguage().contains("zh") ? "zh" : "en";
                ans.put("language", language);
                Bundle bundle = getArguments();
                if (bundle != null) {
                    try {
                        String options = DeeplinkExKt.getParamsKey(bundle, "options", "");
                        JSONObject oo = new JSONObject(options);
                        Iterator<String> keys = oo.keys();
                        while (keys.hasNext()) {
                            String key = keys.next();
                            ans.put(key, oo.get(key));
                        }
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                }
                MELogUtil.localI(DyCommonKt.getTAG(), "send to jue params: " + ans);
                MELogUtil.onlineI(DyCommonKt.getTAG(), "send to jue params: " + ans);
                dynamicView.loadJue(ans.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            // height 不需要
//            MEDynamic.getInstance().loadData(mContainer, data);
        }
    }

    @Override
    public void operator(Map<String, Object> param) {
        Object actionObj = param.get(KEY_ACTION);
        if (actionObj == null) {
            return;
        }
        String action = actionObj.toString();
        if (TextUtils.isEmpty(action)) {
            return;
        }
        //getActivity会空，导致流程断掉，ACTION_SEND_RESULT不强依赖context
        if (Objects.equals(action, ACTION_SEND_RESULT)) {
            try {
                String callbackId = DeeplinkExKt.getParamsKey(getArguments(), DeeplinkCallbackProcessor.KEY_CALLBACK_ID, "");
                if (TextUtils.isEmpty(callbackId)) return;
                Map<String, Object> options = (Map<String, Object>) param.get("options");
                DeeplinkCallbackProcessor.notify(getContext(), options, callbackId);
            } catch (Exception e) {
                MELogUtil.localD("MeDynamic", "operator-->sendResult exception: " + e);
            }
            return;
        }
        Activity a = getActivity();
        if (a == null) {
            return;
        }
        if (ACTION_CLOSE_PAGE.equals(action)) {
            a.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    param.remove(KEY_ACTION);
                    if (!param.isEmpty()) {
                        try {
                            String uri = getArguments().getString("raw_uri");
                            JsResultCallback.notifyCallback(uri, param);
                        } catch (Exception e) {
                            MELogUtil.localD("MeDynamic", "reply to jue exception: " + e);
                        }
                    }
                    a.finish();
                }
            });
        } else if (Objects.equals(action, ACTION_SET_TITLE)) {
            a.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    String title;
                    try {
                        title = param.get("title").toString();
                    } catch (Exception e) {
                        title = "";
                    }
                    processTitle(title);
                }
            });
        } else if (Objects.equals(action, ACTION_SHOW_LOADING)) {
            a.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    mLoading.setVisibility(View.VISIBLE);
                }
            });
        } else if (Objects.equals(action, ACTION_DISMISS_LOADING)) {
            a.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    mLoading.setVisibility(View.GONE);
                }
            });
        }
    }

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {

    }
}
