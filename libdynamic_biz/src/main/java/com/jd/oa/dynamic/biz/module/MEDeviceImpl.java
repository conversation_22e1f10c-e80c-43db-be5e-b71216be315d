package com.jd.oa.dynamic.biz.module;

import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.crossplatform.CrossPlatformMedia;
import com.jd.oa.dynamic.biz.DyCommonKt;
import com.jd.oa.dynamic.biz.DynamicCrossPlatformResultListener;
import com.jd.oa.dynamic.module.MEDevice;

import java.util.HashMap;
import java.util.Map;

public class MEDeviceImpl extends MEDevice {

    @Override
    public void saveToPhotoAlbum(Map<String, Object> options, JsCallBack callback) {
        CrossPlatformMedia.saveToPhotoAlbum(AppBase.getTopActivity(), DyCommonKt.toSS(options), new DynamicCrossPlatformResultListener<>(callback, "MEDevice::saveToPhotoAlbum"));
    }

    @Override
    public void startBiometricAuthentication(Map<String, Object> options, JsCallBack callback) {
        CrossPlatformMedia.startBiometricAuthentication(AppBase.getTopActivity(), options, new DynamicCrossPlatformResultListener<Integer>(callback, "MEDevice::startBiometricAuthentication") {
            @Override
            public void success(String domain, Integer... t) {

                HashMap<String, String> map = new HashMap<>();
                if (t != null && t.length > 0) {
                    map.put("authMode", t[0] + "");
                }
                DyCommonKt.success(callback, map, domain);
            }
        });
    }
}
