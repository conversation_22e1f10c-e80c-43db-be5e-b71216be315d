package com.jd.oa.dynamic.biz.event

import com.jd.jrapp.dy.api.JsCallBack
import com.jd.oa.eventbus.JmEventProcessCallback
import wendu.dsbridge.CompletionHandler

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/2 22:55
 */
class JueEventCallback(val callBack: JsCallBack?) : JmEventProcessCallback<Unit> {

    override fun onResult(result: Unit?) {
    }
}