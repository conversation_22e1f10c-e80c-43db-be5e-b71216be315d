package com.jd.oa.dynamic.biz.module;

import android.app.Activity;
import android.content.Intent;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.AppBase;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.crossplatform.Checker;
import com.jd.oa.crossplatform.CrossPlatformResultListener;
import com.jd.oa.fragment.DownloadFragment;

import java.util.HashMap;

public class CrossPlatformPan {
    public static void downloadFile(Activity activity, HashMap<String, String> params, final CrossPlatformResultListener<String> handler) {
        if (Checker.INSTANCE.checkFragmentActivity(activity, handler)) {
            return;
        }
        try {
            String downloadUrl = Checker.INSTANCE.getDefault(params, "url", "");
            if (Checker.INSTANCE.missParameters("url", downloadUrl, handler)) {
                return;
            }
            String accessKey = "";
            if (params.containsKey("accessKey")) {
                accessKey = Checker.INSTANCE.getDefault(params, "accessKey", "");
            }
            String name = Checker.INSTANCE.getDefault(params, "portalName", "");
            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent.putExtra("downloadUrl", downloadUrl);
            intent.putExtra("fileName", name);
            intent.putExtra("accessKey", accessKey);
            intent.putExtra("donwloadType", "netdisk");
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
            AutoUnregisterResultCallback<ActivityResult> callback = new AutoUnregisterResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult o, boolean u) {
                    if (o.getResultCode() == 200 && o.getData() != null) {
                        Intent i = o.getData();
                        if (i.hasExtra("filePath")) {
                            handler.success(i.getStringExtra("filePath"));
                        } else {
                            Checker.INSTANCE.localError(handler, "Local error: cannot get result after choosing file");
                        }
                    } else {
                        handler.failure("-1", "Biz error: download failure");
                    }
                }
            };
            String key = "CrossPlatformPan_downloadFile" + System.currentTimeMillis();
            ActivityResultLauncher<Intent> register = ((FragmentActivity) activity).getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), callback);
            callback.setLauncher(register);
            register.launch(intent);
        } catch (Throwable e) {
            e.printStackTrace();
            Checker.INSTANCE.localError(handler, "Local error: " + e.getMessage());
        }
    }

    public static void openFile(Activity activity, HashMap<String, String> params, final CrossPlatformResultListener<String> handler) {
        try {
            if (Checker.INSTANCE.checkFragmentActivity(activity, handler)) {
                return;
            }
            String downloadUrl = Checker.INSTANCE.getDefault(params, "url", "");
            String accessKey = "";
            if (params.containsKey("accessKey")) {
                accessKey = Checker.INSTANCE.getAsString(params, "accessKey", "");
            }
            String name = Checker.INSTANCE.getDefault(params, "portalName", "");
            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent.putExtra("downloadUrl", downloadUrl);
            intent.putExtra("fileName", name);
            intent.putExtra("accessKey", accessKey);
            intent.putExtra("donwloadType", "netdisk");
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
            AutoUnregisterResultCallback<ActivityResult> callback = new AutoUnregisterResultCallback<ActivityResult>() {
                @Override
                protected void onActivityResult(ActivityResult o, boolean unused) {
                    if (o.getResultCode() == 200 && o.getData() != null) {
                        Intent i = o.getData();
                        if (i.hasExtra("filePath")) {
                            handler.success(i.getStringExtra("filePath"));
                        } else {
                            Checker.INSTANCE.localError(handler, "Local error: cannot get result after choosing file");
                        }
                    } else {
                        handler.failure("-1", "User error: the user has cancelled");
                    }
                }
            };
            String key = "CrossPlatformPan_openFile" + System.currentTimeMillis();
            ActivityResultLauncher<Intent> register = ((FragmentActivity) activity).getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), callback);
            callback.setLauncher(register);
            register.launch(intent);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            Checker.INSTANCE.localError(handler, "Local error: " + throwable.getMessage());
        }
    }
}
