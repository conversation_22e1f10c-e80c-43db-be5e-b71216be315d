package com.jd.oa.dynamic.biz

import android.content.Intent
import android.os.Bundle
import org.json.JSONObject


fun Intent?.getParamsStr(): String? {
    return this?.getStringExtra("mparam")
}

fun Intent?.getDeeplinkRawString(): String? {
    return this?.getStringExtra("raw_uri")
}

fun Intent.getParamsKey(key: String, dv: String = ""): String {
    try {
        val mparam = getStringExtra("mparam")
        return JSONObject(mparam).getString(key) ?: dv
    } catch (e: Exception) {
        return dv
    }
}

fun Bundle?.getParamsKey(key: String, dv: String = ""): String {
    if (this == null)
        return dv
    try {
        return JSONObject(getString("mparam")).getString(key) ?: dv
    } catch (e: Exception) {
        return dv
    }
}