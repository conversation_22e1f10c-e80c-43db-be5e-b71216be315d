package com.jd.oa.dynamic.biz.module;

import android.app.Activity;
import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.crossplatform.CrossPlatformAuthentication;
import com.jd.oa.dynamic.biz.DyCommonKt;
import com.jd.oa.dynamic.biz.DynamicCrossPlatformResultListener;
import com.jd.oa.dynamic.module.MEAuthentication;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

public class MEAuthenticationImpl extends MEAuthentication {
    private WeakReference<MEAuthentication> mAuthenticationWeakReference;

    @Override
    protected void saveOrigin(MEAuthentication origin) {
        mAuthenticationWeakReference = new WeakReference<>(origin);
    }

    @Override
    public void facialAuthentication(HashMap<String, Object> args, final JsCallBack callback) {
        Context context = (mAuthenticationWeakReference != null && mAuthenticationWeakReference.get() != null)
                ? mAuthenticationWeakReference.get().getContext()
                : null;
        Activity activity = context instanceof Activity ? (Activity) context : null;
        mAuthenticationWeakReference = null;
        CrossPlatformAuthentication.facialAuthentication(activity == null ? AppBase.getTopActivity() : activity, DyCommonKt.toSS(args), new DynamicCrossPlatformResultListener<JSONObject>(callback, "MEAuthentication::facialAuthentication") {
            @Override
            public void success(String domain, JSONObject... t) {
                if (t == null || t.length == 0) {
                    failure("-1", "Local error");
                } else {
                    Type type = new TypeToken<Map<String, Object>>() {
                    }.getType();
                    Gson gson = new Gson();
                    Map<String, Object> map = gson.fromJson(((JSONObject) t[0]).toString(), type);
                    DyCommonKt.success(callback, map, domain);
                }
            }
        });
    }
}
