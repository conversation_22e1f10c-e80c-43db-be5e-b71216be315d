#!/bin/bash

buildFlutter="$1"
flutterProjectBranch="$2"
commitFlutterUpdate="$3"
commitMessage="$4"
pushBranch="$5"
jenkinsUser="$6"
buildFlutterDebug="$7"

echo "buildFlutter: ${buildFlutter}, flutterProjectBranch: ${flutterProjectBranch}, commitFlutterUpdate: ${commitFlutterUpdate}, commitMessage: ${commitMessage}, pushBranch: ${pushBranch}, jenkinsUser: ${jenkinsUser}, buildFlutterDebug: ${buildFlutterDebug}"

if [[ ${buildFlutter} == false ]]
then
    echo "no need to build flutter"
    exit 0
fi

if [[ ${flutterProjectBranch} == "" ]]
then
    flutterProjectBranch=dev
fi

if [[ ${commitFlutterUpdate} == "" ]]
then
    commitFlutterUpdate=false
fi


if [[ ${pushBranch} == "" ]]
then
    pushBranch=dev
else
    #去掉origin/
    pushBranch=${pushBranch:7}
fi

if [[ ${jenkinsUser} == "" ]]
then
    jenkinsUser=jenkins
fi

if [[ ${buildFlutterDebug} == "" ]]
then
    buildFlutterDebug=false
fi

curDir=$(pwd)
flutterDir=../jdf_jdme_test_module/

#先切下分支
if [[ ${commitFlutterUpdate} == true && ${pushBranch} != "" ]]
then
        if [[ $(git branch --list ${pushBranch}) == "" ]]
        then
            git lfs fetch
            git branch ${pushBranch} origin/${pushBranch}
            git branch --set-upstream-to=origin/${pushBranch} ${pushBranch}
        else
           echo "${pushBranch} branch already exists"
        fi
        git status
        git checkout ${pushBranch}
        git lfs fetch
        git reset --hard origin/${pushBranch}
        #git pull origin ${pushBranch} -s recursive -X thiers
fi

cd ${flutterDir}

branch=`git branch | grep "*"`
curBranch=${branch:2}
echo "current branch: ${curBranch}"

#git reset --hard HEAD
git fetch
if [[ ${curBranch} != ${flutterProjectBranch} ]]
then
    git branch ${flutterProjectBranch} origin/${flutterProjectBranch}
    git checkout ${flutterProjectBranch}
fi
git reset --hard origin/${flutterProjectBranch}
#git pull origin ${flutterProjectBranch}
git log -3

flutterCommit=`git log --oneline -1`
commitAuthorName=`git show -s --format=%an`
commitAuthorDate=`git show -s --format=%ad`
echo "flutterCommit: ${flutterCommit}, commitAuthorName: ${commitAuthorName}, commitAuthorDate: ${commitAuthorDate}"

flutter doctor

if [[ ${buildFlutterDebug} == true ]]
then
  bash -x ./build_debug_aar.sh JDME_Android
else
  bash -x ./build_release_aar.sh JDME_Android
fi

cd -

if [[ ${commitFlutterUpdate} == true ]]
then
    commitMessage="feat(flutter): ${commitMessage}commit: ${flutterCommit}, author: ${commitAuthorName} ${commitAuthorDate} [${jenkinsUser}]"

    if [[ ${pushBranch} != "" ]]
    then
        git checkout ${pushBranch}
        git add ./lib_me_flutter/flutter_release
        git add ./lib_me_flutter/flutter_debug
        git commit -m "${commitMessage}"
        git lfs pull origin ${pushBranch} -s recursive -X ours
        git push origin ${pushBranch}
    fi
fi