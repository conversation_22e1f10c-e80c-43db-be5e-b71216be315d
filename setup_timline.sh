#!/bin/bash
CURRENT_DIR=$(pwd)
if [[ ! -d "../jd-icsp" ]]; then
  echo "please clone jd-icsp in ../"
  exit
fi
cd ../jd-icsp
if [[ ! -f "./gradlew" ]]
then
	echo "please open jd-icsp with studio first"
	exit
fi
SDK_DIR=$(pwd)
echo "CURRENT_DIR: $CURRENT_DIR"
echo "SDK_DIR $SDK_DIR"
echo "current dir = $(pwd)"
branch="develop/202103"
log=$(git show ${branch})
if [[ ${log} != commit* ]]; then
	# 错误
	git pull
	git checkout -b ${branch} origin/${branch}
fi
git checkout ${branch}
git pull
chmod 777 ./gradlew
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMAudio:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMBase:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMCore:assembleTimlineDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMGallery:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMGlide:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMMap:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMOkhttp:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMSdk:assembleTimlineDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMSmiley:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMUi:assembleTimlineDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMWidget:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon jimutils:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon JIMDownloadUpload:assembleDebug && cd "${CURRENT_DIR}"
cd "${SDK_DIR}" && ./gradlew --no-daemon signalvariant:assembleDebug && cd "${CURRENT_DIR}"
mkdir -p "${CURRENT_DIR}"/app/libs

function customCp(){
    if [[ ! -f "${SDK_DIR}"/"${1}"/build/outputs/aar/"${2}" ]]; then # 不存在
        cp -v "${SDK_DIR}"/"${1}"/build/outputs/aar/"${1}".aar "${CURRENT_DIR}"/im_dd/libs/"${3}"
    else
        cp -v "${SDK_DIR}"/"${1}"/build/outputs/aar/"${2}" "${CURRENT_DIR}"/im_dd/libs/"${3}"
    fi
}
customCp JIMAudio jimaudio-v1.0.0.aar jimaudio-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMAudio/build/outputs/aar/jimaudio-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimaudio-v1.0.0.aar
customCp JIMBase jimbase-v1.0.0.aar jimbase-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMBase/build/outputs/aar/jimbase-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimbase-v1.0.0.aar
customCp JIMCore timline-jimcore-v1.0.0.aar jimcore-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMCore/build/outputs/aar/timline-jimcore-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimcore-v1.0.0.aar
customCp JIMGallery jimgallery-v1.0.0.aar jimgallery-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMGallery/build/outputs/aar/jimgallery-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimgallery-v1.0.0.aar
customCp JIMGlide jimglide-v1.0.0.aar jimglide-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMGlide/build/outputs/aar/jimglide-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimglide-v1.0.0.aar
customCp JIMMap jimmap-v1.0.0.aar jimmap-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMMap/build/outputs/aar/jimmap-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimmap-v1.0.0.aar
customCp JIMOkhttp jimokhttp-v1.0.0.aar jimokhttp-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMOkhttp/build/outputs/aar/jimokhttp-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimokhttp-v1.0.0.aar
customCp JIMSdk timline-jimsdk-v1.0.0.aar jimsdk-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMSdk/build/outputs/aar/timline-jimsdk-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimsdk-v1.0.0.aar
customCp JIMSmiley jimsmiley-v1.0.0.aar jimsmiley-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMSmiley/build/outputs/aar/jimsmiley-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimsmiley-v1.0.0.aar
customCp JIMUi timline-jimui-v1.0.0.aar jimui-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMUi/build/outputs/aar/timline-jimui-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimui-v1.0.0.aar
customCp JIMWidget jimwidget-v1.0.0.aar jimwidget-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMWidget/build/outputs/aar/jimwidget-v1.0.0.aar "${CURRENT_DIR}"/im_dd/libs/jimwidget-v1.0.0.aar
customCp jimutils jimutils-v1.0.0.aar jimutils-v1.0.0.aar
# cp -v "${SDK_DIR}"/jimutils/build/outputs/aar/jimutils-debug.aar "${CURRENT_DIR}"/im_dd/libs/jimutils-v1.0.0.aar
customCp JIMDownloadUpload JIMDownloadUpload-debug.aar JIMDownloadUpload-v1.0.0.aar
# cp -v "${SDK_DIR}"/JIMDownloadUpload/build/outputs/aar/JIMDownloadUpload-debug.aar "${CURRENT_DIR}"/im_dd/libs/JIMDownloadUpload-v1.0.0.aar
customCp signalvariant jimsignalvariant-v1.0.0.aar jimsignalvariant-v1.0.0.aar
# cp -v "${SDK_DIR}"/signalvariant/build/outputs/aar/signalvariant-v1.1.aar "${CURRENT_DIR}"/im_dd/libs/signalvariant-v1.1.aar
echo "build timline aar finish"
