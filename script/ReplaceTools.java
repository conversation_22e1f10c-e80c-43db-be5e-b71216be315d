package script;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;

/*
 * 替换文件（如果该文件含有子目录，则包括子目录所有文件）中某个字符串并写入新内容（Java代码实现）.
 *
 *原理：逐行读取源文件的内容，一边读取一边同时写一个*.tmp的文件。
 *当读取的行中发现有需要被替换和改写的目标内容‘行’时候，用新的内容‘行’替换之。
 *最终，删掉源文件，把*.tmp的文件重命名为源文件名字。
 *
 *注意！代码功能是逐行读取一个字符串，然后检测该字符串‘行’中是否含有替换的内容，有则用新的字符串‘行’替换源文件中该处整个字符串‘行’。没有则继续读。
 *注意！替换是基于‘行’，逐行逐行的替换！
 *
 * */
@SuppressWarnings("ResultOfMethodCallIgnored")
public class ReplaceTools {

    private final String path;
    private final String target;
    private final String newContent;
    private final static String ICON_VIEW_START = "<com.jd.oa.ui.IconFontText" + "View";
    private final static String ICON_VIEW_START_NEW = "<com.jd.oa.ui.IconFontView";
    private final static String ICON_VIEW_END = "/>";
    private final static String ICON_VIEW_WIDTH = "android:layout_width";
//    private final static String ICON_VIEW_WIDTH_NEW = "android:layout_width=\"wrap_content\"";
    private final static String ICON_VIEW_HEIGHT = "android:layout_height";
//    private final static String ICON_VIEW_HEIGHT_NEW = "android:layout_height=\"wrap_content\"";
    private final static String ICON_VIEW_SIZE = "android:textSize";

    public ReplaceTools(String path, String target, String newContent) {
        // 操作目录。从该目录开始。该文件目录下及其所有子目录的文件都将被替换。
        this.path = path;
        // target:需要被替换、改写的内容。
        this.target = target;
        // newContent:需要新写入的内容。
        this.newContent = newContent;

        operation();
    }

    private void operation() {
        File file = new File(path);
        operationDirectory(file);
    }

    public void operationDirectory(File file) {
        if (file.isFile()) {
            operatFile(file);
            return;
        }
        File[] files = file.listFiles();
        if (files == null) {
            return;
        }
        for (File f : files) {
            if (f.isDirectory()) {
                // 如果是目录，则递归。
                operationDirectory(f);
            } else {
                operatFile(f);
            }
        }
    }

    private void operatFile(File f) {
        if (f.isFile()) {
//            System.out.println("file=" + f.getAbsolutePath());
            if (f.getAbsolutePath().endsWith(".java")) {
                operationFile(f);
            } else if (f.getAbsolutePath().endsWith(".xml")) {
                operationXmlFile(f);
            } else if (f.getAbsolutePath().endsWith(".kt")) {
                operationFile(f);
            }
        }
    }

    public void operationXmlFile(File file) {
        try {
            InputStream is = Files.newInputStream(file.toPath());
            BufferedReader reader = new BufferedReader(new InputStreamReader(is));

            String filename = file.getName();
            // tmp file为缓存文件，代码运行完毕后此文件将重命名为源文件名字。
            File tmpfile = new File(file.getParentFile().getAbsolutePath() + "\\" + filename + ".tmp");

            BufferedWriter writer = new BufferedWriter(new FileWriter(tmpfile));

            boolean flag = false;
            boolean flagIconViewStart = false;
            String str;
            int containerSize = -1;
            boolean containerSizeTab = false;
            String blankStart = null;
            boolean first = true;
            while (true) {
                str = reader.readLine();

                if (str == null) {
                    break;
                }
                if (!first) {
                    writer.write("\n");
                }
                first = false;
                if (flagIconViewStart) {
                    if (blankStart == null) {
                        if (str.contains("android:")) {
                            blankStart = str.substring(0, str.indexOf("android:"));
                        }
                    }
                    if (str.contains(ICON_VIEW_WIDTH)) {
                        if (str.toLowerCase().contains("dp")) {
                            containerSize = getSize(str);
//                            writer.write(blankStart + ICON_VIEW_WIDTH_NEW);
//                        } else {
//                            writer.write(str);
                        }
                        writer.write(str);
                    } else if (str.contains(ICON_VIEW_HEIGHT)) {
                        if (str.toLowerCase().contains("dp")) {
                            containerSize = getSize(str);
//                            writer.write(blankStart + ICON_VIEW_HEIGHT_NEW);
//                        } else {
//                            writer.write(str);
//
                        }
                        writer.write(str);
                    } else if (str.contains(ICON_VIEW_SIZE)) {
                        containerSizeTab = true;
                        int sizeOld = getSize(str);
                        boolean end = str.contains(ICON_VIEW_END);
                        if (sizeOld > 0) {
                            writer.write(blankStart + "android:textSize=\"@dimen/JMEIcon_" + (sizeOld + 2) + "\"" + (end ? ICON_VIEW_END : ""));
                        } else {
                            writer.write(str);
                        }
                        if (sizeOld > 0 && containerSize > 0) {
                            if (containerSize <= sizeOld + 2) {
                                System.out.println(" sizeOld=" + sizeOld + "  containerSize=" + containerSize + "  file=" + file.getAbsolutePath());
                            }
                        }
                        if (end) {
                            //reset flags
                            flagIconViewStart = false;
                            containerSize = -1;
                            blankStart = null;
                            containerSizeTab = false;
                        }
                    } else if (str.contains(ICON_VIEW_END)) {
                        if (!containerSizeTab && containerSize > 0) {
                            writer.write(blankStart + "android:textSize=\"@dimen/JMEIcon_" + (containerSize + 2) + "\"");
                        }
                        writer.write(str);
                        //reset flags
                        flagIconViewStart = false;
                        containerSize = -1;
                        blankStart = null;
                        containerSizeTab = false;
                    } else {
                        writer.write(str);
                    }
                } else {
                    if (str.contains(ICON_VIEW_START)) {
                        flag = true;
                        flagIconViewStart = true;
                        str = str.replace(ICON_VIEW_START, ICON_VIEW_START_NEW);
                        writer.write(str);
                    } else {
                        writer.write(str);
                    }
                }
            }

            is.close();

            writer.flush();
            writer.close();

            if (flag) {
                file.delete();
                tmpfile.renameTo(new File(file.getAbsolutePath()));
            } else {
                tmpfile.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void operationFile(File file) {
        try {
            InputStream is = Files.newInputStream(file.toPath());
            BufferedReader reader = new BufferedReader(new InputStreamReader(is));

            String filename = file.getName();
            // tmp file为缓存文件，代码运行完毕后此文件将重命名为源文件名字。
            File tmpfile = new File(file.getParentFile().getAbsolutePath() + "\\" + filename + ".tmp");

            BufferedWriter writer = new BufferedWriter(new FileWriter(tmpfile));

            boolean flag = false;
            String str;
            boolean first = true;
            while (true) {
                str = reader.readLine();

                if (str == null) {
                    break;
                }
                if (!first) {
                    writer.write("\n");
                }
                first = false;
                if (str.contains(target)) {
                    str = str.replace(target, newContent);
                    flag = true;
                }
                writer.write(str);
            }

            is.close();

            writer.flush();
            writer.close();

            if (flag) {
                file.delete();
                tmpfile.renameTo(new File(file.getAbsolutePath()));
            } else {
                tmpfile.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getSize(String lineStr) {
        try {
            if (lineStr == null || (!lineStr.toLowerCase().contains("dp") && !lineStr.toLowerCase().contains("sp"))) {
                return -1;
            }
            lineStr = lineStr.toLowerCase();
            int start = lineStr.indexOf("\"");
            if (start < 0) {
                return -1;
            }
            int endDp = lineStr.indexOf("dp", start);
            int endSp = lineStr.indexOf("sp", start);
            if (endDp > 0) {
                int r = Integer.parseInt(lineStr.substring(start + 1, endDp));
                if (r % 2 != 0) {
                    return r + 1;
                } else {
                    return r;
                }
            } else if (endSp > 0) {
                int r = Integer.parseInt(lineStr.substring(start + 1, endSp));
                if (r % 2 != 0) {
                    return r + 1;
                } else {
                    return r;
                }
            } else {
                return -1;
            }
        } catch (NumberFormatException e) {
//            e.printStackTrace();
            return -1;
        }
    }

    public static void main(String[] args) {
        //代码测试：假设有一个test文件夹，test文件夹下含有若干文件或者若干子目录，子目录下可能也含有若干文件或者若干子目录（意味着可以递归操作）。
        //把test目录下以及所有子目录下（如果有）中文件含有"hi"的字符串行替换成新的"hello,world!"字符串行。
//        new script.ReplaceTools("./app/src/main/res/layout/jdme_activity_search_main_header.xml", "hi", "hello,world!");
//        new scrpt.ReplaceTools("./app/src/main/java/com/jd/oa/business/search/SearchActivity.kt", "IconFontText" +
//                "View", "IconFontView");
//
//
        new ReplaceTools("./", "IconFontText" + "View", "IconFontView");


    }
}