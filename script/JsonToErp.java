package script;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.InputStreamReader;

public class JsonToErp {
    public static void main(String[] args) {
        try {
            /* 读入TXT文件 */
            String pathname = "input.txt"; // 绝对路径或相对路径都可以，这里是绝对路径，写入文件时演示相对路径
            File filename = new File(pathname); // 要读取以上路径的input。txt文件
            if (!filename.exists()) {
                return;
            }
            InputStreamReader reader = new InputStreamReader(
                    new FileInputStream(filename)); // 建立一个输入流对象reader
            BufferedReader br = new BufferedReader(reader); // 建立一个对象，它把文件内容转成计算机能读懂的语言
            String line = "";
            line = br.readLine();
            String start = "\"key\": \"";
            String end = "\",";
            String erp = null;
//            StringBuilder outStr= new StringBuilder();

            /* 写入Txt文件 */
            File writeName = new File("output.txt"); // 相对路径，如果没有则要建立一个新的output。txt文件
            if (!filename.exists()) {
                writeName.createNewFile(); // 创建新文件
            }
            BufferedWriter out = new BufferedWriter(new FileWriter(writeName));
            while (line != null) {
                int s = line.indexOf(start);
                if (s >= 0) {
                    int e = line.indexOf(end, s);
                    if (e >= 0) {
                        erp = line.substring(s + start.length(), e);
//                        outStr.append(outStr).append(erp).append(",");
                        out.write(erp + ","); // \r\n即为换行
                    }
                }
                line = br.readLine(); // 一次读入一行数据
            }
            out.flush(); // 把缓存区内容压入文件
            out.close(); // 最后记得关闭文件

        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("Hello, World!");
    }
}
