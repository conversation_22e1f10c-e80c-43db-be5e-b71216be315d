apply plugin: 'com.android.application'
apply plugin: 'com.chenenyu.router'

android {

    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILD_TOOLS_VERSION

    defaultConfig {
        applicationId "com.jd.oa.workbench"

        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 107
        versionName VERSION_NAME

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

        multiDexEnabled true

        ndk {
            abiFilters "arm64-v8a"//, "armeabi", "armeabi-v7a", "x86"
        }

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        buildConfigField 'String','buiding_no','"ffffffffff"'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'

    implementation COMPILE_SUPPORT.design
    implementation "com.android.support.constraint:constraint-layout:${constraintVersion}"

    implementation('com.chenenyu.router:router:1.5.2') {
//        force = true
    }

    implementation 'com.android.support:multidex:1.0.3'

    implementation(libs.lib.utils) { dep ->
        ['com.jd.oa', 'com.squareup.okhttp3', 'com.github.lib'].each { group -> dep.exclude group: group }
        exclude module: 'mae-bundles-widget'
    }

    //zxing
//    implementation 'com.google.zxing:core:3.3.0'


    implementation project(':common')
    implementation project(':login')
    implementation project(':workbench')
}
