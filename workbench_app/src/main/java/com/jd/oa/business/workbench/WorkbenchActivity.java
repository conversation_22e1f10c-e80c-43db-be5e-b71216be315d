package com.jd.oa.business.workbench;

import android.os.Bundle;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import androidx.appcompat.app.AppCompatActivity;
import android.view.View;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.business.workbench2.fragment.WorkbenchFragment;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.RouterConstant;
import com.jd.oa.utils.FragmentUtils;


@Route(RouterConstant.ACTIVITY_URI_WORKENCH)
public class WorkbenchActivity extends AppCompatActivity {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(com.jd.oa.workbench.R.layout.jdme_activity_workbench_main);

        FragmentUtils.addWithCommit(this, WorkbenchFragment.class, R.id.container, false, false, null);
        FloatingActionButton floatingActionButton = findViewById(com.jd.oa.workbench.R.id.fab);
        floatingActionButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 移除本地信息
                PreferenceManager.UserInfo.removeAll(); // 移除当前用户配置信息
                finish();
            }
        });
    }
}
