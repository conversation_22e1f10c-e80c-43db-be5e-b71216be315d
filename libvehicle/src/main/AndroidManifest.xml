<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->
    <uses-permission android:name="android.permission.CALL_PHONE" />

    <application>

        <activity
            android:name=".caraddress.CarAddressActivity"
            android:label="@string/me_car_address_title"
            android:screenOrientation="portrait" />
        <activity
            android:name=".caraddress.CarAddressModifyActivity"
            android:label="@string/me_car_address_add_title"
            android:screenOrientation="portrait" />

        <service android:name=".didi.DidiPollingService" />

        <activity
            android:name=".didi.DidiChangeCityActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".didi.DidiAddressSearchActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".didi.DidiCancelOrderResonActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat"
            android:windowSoftInputMode="stateAlwaysHidden" />

        <activity
            android:name=".didi.DidiColleaguesSearchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".didi.DidiColleaguesSearchResultActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".didi.DidiActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat"
            android:windowSoftInputMode="stateAlwaysHidden" />
    </application>
</manifest>
