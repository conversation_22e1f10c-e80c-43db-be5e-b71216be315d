<?xml version="1.0" encoding="utf-8"?>


<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape xmlns:android="http://schemas.android.com/apk/res/android">
            <solid android:color="@color/black_divider" />
            <corners android:bottomLeftRadius="4dip"
                android:bottomRightRadius="4dip"
                android:topLeftRadius="4dip"
                android:topRightRadius="4dip">
            </corners>
        </shape>
    </item>

    <item>
        <shape xmlns:android="http://schemas.android.com/apk/res/android">
            <solid android:color="@color/white" />
            <corners android:bottomLeftRadius="4dip"
                android:bottomRightRadius="4dip"
                android:topLeftRadius="4dip"
                android:topRightRadius="4dip">
            </corners>
        </shape>
    </item>

</selector>