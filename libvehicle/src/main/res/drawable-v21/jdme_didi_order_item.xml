<?xml version="1.0" encoding="utf-8"?>

<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/ripple_material_light">
    <item>
        <selector>
            <item android:state_pressed="true">
                <shape xmlns:android="http://schemas.android.com/apk/res/android">
                    <solid android:color="@color/white" />
                    <corners
                        android:bottomLeftRadius="4dip"
                        android:bottomRightRadius="4dip"
                        android:topLeftRadius="4dip"
                        android:topRightRadius="4dip"/>
                </shape>
            </item>

            <item>
                <shape xmlns:android="http://schemas.android.com/apk/res/android">
                    <solid android:color="@color/white" />
                    <corners
                        android:bottomLeftRadius="4dip"
                        android:bottomRightRadius="4dip"
                        android:topLeftRadius="4dip"
                        android:topRightRadius="4dip"/>
                </shape>
            </item>

        </selector>
    </item>
</ripple>