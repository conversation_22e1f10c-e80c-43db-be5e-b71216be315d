<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:scrollbars="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="27dp"
            android:layout_marginRight="27dp"
            android:layout_marginTop="14dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:text="@string/me_didi_title_question"
                android:textColor="@color/conference_black_color"
                android:textSize="13dp" />

            <TextView
                android:id="@+id/tv_consumer_hotline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:drawableLeft="@drawable/jdme_didi_icon_consumer_hotline"
                android:drawablePadding="10dp"
                android:text="@string/me_didi_consumer_hotline"
                android:textColor="@color/conference_black_color"
                android:textSize="13dp" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:orientation="vertical" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/et_msg"
                android:layout_width="fill_parent"
                android:layout_height="60dp"
                android:layout_marginLeft="25dp"
                android:layout_marginRight="25dp"
                android:layout_marginTop="2dp"
                android:background="@drawable/jdme_bg_didi_textview_border_omplaints"
                android:gravity="top"
                android:hint="@string/ne_didi_hint_omplaints"
                android:maxLength="40"
                android:paddingBottom="20dp"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="7dp"
                android:textColor="@color/black_main_summary"
                android:textColorHint="@color/light_gray"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_alignParentRight="true"
                android:layout_marginBottom="20dp"
                android:layout_marginRight="35dp"
                android:text="40"
                android:textColor="@color/light_gray"
                android:textSize="13dp" />

        </RelativeLayout>

        <Button
            android:id="@+id/btn_submit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="40dp"
            android:layout_marginLeft="60dp"
            android:layout_marginRight="60dp"
            android:layout_marginTop="14dp"
            android:background="?attr/me_btn_selector"
            android:text="@string/me_submit_not_translate"
            android:textSize="@dimen/me_text_size_middle" />

    </LinearLayout>

</ScrollView>