<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="12dp"
        android:layout_marginStart="64dp"
        android:scaleType="centerCrop"
        tools:src="@drawable/jdme_app_icon"/>
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        app:layout_constraintLeft_toRightOf="@id/iv_avatar"
        app:layout_constraintRight_toLeftOf="@+id/tv_status"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="12dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#d9000000"
        android:textSize="@dimen/comm_text_normal_xlarge"
        tools:text="葛姝悦葛姝悦葛姝悦葛姝悦"/>
    <TextView
        android:id="@+id/tv_erp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        app:layout_constraintLeft_toRightOf="@id/iv_avatar"
        app:layout_constraintRight_toLeftOf="@+id/tv_status"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="12dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#ff3f3f3f"
        android:textSize="@dimen/comm_text_normal_xlarge"
        tools:text="geshuyue1geshuyue1"/>

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginEnd="38dp"
        android:textSize="@dimen/comm_text_normal_large"
        android:textColor="@color/comm_text_normal"
        android:drawableStart="@drawable/jdme_ic_car_wait_for_confirm"
        android:drawablePadding="10dp"
        android:text="@string/me_car_travel_invite_unconfirmed"/>
    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="15dp"
        android:layout_marginStart="12dp"
        android:background="@color/comm_background"/>
</androidx.constraintlayout.widget.ConstraintLayout>