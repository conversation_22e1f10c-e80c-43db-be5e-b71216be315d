<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_didi_order_item"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="25dp">

        <TextView
            android:id="@+id/tv_didi_order_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:singleLine="true"
            android:text="@string/me_car_default_order_time"
            android:textColor="@color/black_252525"
            android:textSize="12sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/tv_didi_order_date"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_car_appointment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/jdme_shape_car_appointment_bg"
                android:text="@string/appointment"
                android:textColor="@color/joywork_red"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/tv_car_pool"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/jdme_shape_car_pool_bg"
                android:text="@string/pool"
                android:textColor="@color/me_color_yellow"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/tv_car_upgrade"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/jdme_shape_car_upgrade_bg"
                android:text="@string/call_car_upgrade"
                android:textColor="#4C7CFF"
                android:textSize="10sp" />
        </LinearLayout>


        <TextView
            android:id="@+id/tv_didi_order_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:text="@string/me_car_default_cost"
            android:textColor="@color/black_252525"
            android:textSize="12sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5px"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:background="@color/didi_page_bg" />

    <RelativeLayout
        android:id="@+id/rl_didi_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="20dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_didi_start_place"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/jdme_icon_loc_from"
                android:drawablePadding="5dp"
                android:text="@string/me_car_default_origin"
                android:textColor="@color/black_assist"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_didi_end_place"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="14dp"
                android:drawableLeft="@drawable/jdme_icon_loc_to"
                android:drawablePadding="5dp"
                android:text="@string/me_car_default_target"
                android:textColor="@color/black_assist"
                android:textSize="12sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_didi_order_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
            android:drawablePadding="5dp"
            android:text="@string/me_car_driving"
            android:textColor="@color/black_main_summary"
            android:textSize="14sp" />
    </RelativeLayout>
</LinearLayout>