<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_tip_approval"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/didi_warn_bg"
                android:drawableLeft="@drawable/jdme_alert_icon"
                android:drawablePadding="5dp"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:paddingLeft="8dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="@string/me_car_pool_call_tips"
                android:textColor="@color/red_warn"
                android:textSize="@dimen/me_text_size_small"
                android:visibility="visible" />

            <LinearLayout
                android:id="@+id/ll_reserve"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/me_didi_go_time"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_small" />

                <TextView
                    android:id="@+id/tv_leave_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="6dp"
                    android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
                    android:drawablePadding="5dp"
                    android:text="@string/me_didi_now"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_small" />

            </LinearLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/comm_divider" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="11dp"
                    android:layout_marginBottom="11dp"
                    android:paddingLeft="10dip"
                    android:src="@drawable/jdme_didi_icon_loc_from" />

                <TextView
                    android:id="@+id/tv_from"
                    android:layout_width="0dp"
                    android:layout_height="38dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:hint="@string/me_didi_hint_from"
                    android:paddingLeft="8dp"
                    android:singleLine="true"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_from_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="8dp"
                    android:background="@drawable/jdme_bg_corner_rectangle_red"
                    android:textColor="#ff0000"
                    android:textSize="14sp"
                    android:visibility="gone" />


            </LinearLayout>

            <ViewStub
                android:id="@+id/vs_from"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="5dp"
                android:layout="@layout/jdme_viewstub_didi_calltaxi_overtime_address_tip" />

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/comm_divider" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="11dp"
                    android:layout_marginBottom="11dp"
                    android:paddingLeft="10dip"
                    android:src="@drawable/jdme_didi_icon_loc_to" />

                <TextView
                    android:id="@+id/tv_to"
                    android:layout_width="0dp"
                    android:layout_height="38dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:hint="@string/me_didi_hint_to"
                    android:paddingLeft="8dip"
                    android:singleLine="true"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_to_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="8dp"
                    android:background="@drawable/jdme_bg_corner_rectangle_red"
                    android:textColor="#ff0000"
                    android:textSize="14sp"
                    android:visibility="gone" />


            </LinearLayout>

            <ViewStub
                android:id="@+id/vs_to"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="5dp"
                android:layout="@layout/jdme_viewstub_didi_calltaxi_overtime_address_tip" />

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/comm_divider" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp">

                <ImageView
                    android:id="@+id/iv_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="12dip"
                    android:src="@drawable/jdme_didi_icon_phone" />

                <com.jd.oa.ui.ClearableEditTxt
                    android:id="@+id/cet_telephone"
                    android:layout_width="fill_parent"
                    android:layout_height="42dp"
                    android:layout_toRightOf="@id/iv_phone"
                    android:background="@null"
                    android:hint="@string/me_didi_hint_phone_number"
                    android:maxLength="11"
                    android:numeric="integer"
                    android:paddingStart="10dp"
                    android:singleLine="true"
                    android:textSize="@dimen/me_text_size_14" />
            </RelativeLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="@dimen/comm_divider_height"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/comm_divider" />


            <LinearLayout
                android:id="@+id/layout_carpool"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="10dp"
                    android:src="@drawable/jdme_ic_car_colleague" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="@string/me_car_has_colleague"
                    android:textColor="@color/comm_text_title"
                    android:textSize="@dimen/comm_text_normal" />

                <Space
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_colleague"
                    style="@style/SwitchStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/setting_item_padding_horizontal"
                    android:checked="false" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/layout_colleague"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:layout_marginBottom="8dp"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="36dp"
                    android:text="@string/me_car_travel_colleague_num_tips"
                    android:textColor="@color/comm_text_red" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_colleague_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="34dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="12dp" />
            </LinearLayout>

            <com.jd.oa.ui.ClearableEditTxt
                android:id="@+id/cet_reason"
                android:layout_width="fill_parent"
                android:layout_height="42dp"
                android:layout_marginLeft="11dp"
                android:layout_marginRight="11dp"
                android:background="@null"
                android:hint="@string/me_didi_hint_remark1"
                android:maxLength="20"
                android:paddingLeft="15dp"
                android:singleLine="true"
                android:textColorHint="#dddddd"
                android:textSize="@dimen/me_text_size_14"
                android:visibility="gone"
                tools:visibility="visible" />

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/comm_divider" />

            <com.jd.oa.business.didi.widget.EstimateInfoView
                android:id="@+id/estimateView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include layout="@layout/jdme_didi_call_taxi_buttons" />
</LinearLayout>