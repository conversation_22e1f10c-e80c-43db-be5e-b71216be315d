<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_countdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="30dp"
        android:textSize="30sp"
        android:textColor="@color/comm_text_title"
        tools:text="03：00"/>
    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_countdown"
        android:layout_marginTop="16dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:gravity="center"
        android:textColor="@color/comm_text_title"
        android:textSize="@dimen/comm_text_normal_large"
        android:text="@string/me_car_travel_invitation_tips"
        tools:text="请在%s内确认本次邀请，确认后您无法取消订单请在%s内确认本次邀请，确认后您无法取消订单"/>

    <TextView
        android:id="@+id/tv_title_call_car"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_tips"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="46dp"
        android:layout_marginStart="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_invitation_title_call_car"/>
    <TextView
        android:id="@+id/tv_call_car"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_title_call_car"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="葛姝悦(geshuyue)"/>
    <View
        android:id="@+id/view_divider_call_car"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_call_car"
        android:layout_marginTop="12dp"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_title_start_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider_call_car"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="12dp"
        android:layout_marginStart="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_invitation_title_start_time"/>
    <TextView
        android:id="@+id/tv_start_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_title_start_time"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        android:text="@string/me_car_pool_start_time_now" />
    <View
        android:id="@+id/view_divider_start_time"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_start_time"
        android:layout_marginTop="12dp"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_title_from"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider_start_time"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="12dp"
        android:layout_marginStart="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_invitation_title_from"/>
    <TextView
        android:id="@+id/tv_from"
        android:layout_width="0dp"
        android:gravity="end"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_title_from"
        app:layout_constraintStart_toEndOf="@id/tv_title_from"
        android:layout_marginStart="30dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="明基广场"/>
    <View
        android:id="@+id/view_divider_from"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_from"
        android:layout_marginTop="12dp"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_title_to"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider_from"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="12dp"
        android:layout_marginStart="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_invitation_title_to"/>
    <TextView
        android:id="@+id/tv_to"
        android:layout_width="0dp"
        android:gravity="end"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_title_to"
        app:layout_constraintStart_toEndOf="@id/tv_title_to"
        android:layout_marginStart="30dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="中山公园地铁站"/>
    <View
        android:id="@+id/view_divider_to"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_to"
        android:layout_marginTop="12dp"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_colleague"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/view_divider_to"
        app:layout_constraintLeft_toRightOf="@+id/tv_title_colleague"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constrainedWidth="true"/>

    <TextView
        android:id="@+id/tv_title_colleague"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/recycler_colleague"
        app:layout_constraintBottom_toBottomOf="@id/recycler_colleague"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:layout_marginStart="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_passenger"/>
    <View
        android:id="@+id/view_divider_colleague"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recycler_colleague"
        android:layout_marginTop="8dp"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider_colleague"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/me_setting_background"/>

    <TextView
        android:id="@+id/tv_cost_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider_colleague"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="10dp"
        android:layout_marginStart="12dp"
        android:textColor="@color/comm_text_red"
        android:text="@string/me_car_pool_invitation_cost_tips"/>

    <Button
        android:id="@+id/btn_reject"
        android:minHeight="0dp"
        android:minWidth="0dp"
        android:layout_width="140dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toBottomOf="@id/tv_cost_tips"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_confirm"
        android:layout_marginTop="36dp"
        android:textColor="@color/selector_didi_text_black"
        android:background="@drawable/jdme_btn_round_gray"
        android:enabled="false"
        android:text="@string/me_car_pool_reject"/>
    <Button
        android:id="@+id/btn_confirm"
        android:minHeight="0dp"
        android:minWidth="0dp"
        android:layout_width="140dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="@id/btn_reject"
        app:layout_constraintLeft_toRightOf="@id/btn_reject"
        app:layout_constraintRight_toRightOf="parent"
        android:textColor="@color/comm_text_white"
        android:background="@drawable/jdme_btn_red"
        android:enabled="false"
        android:text="@string/me_car_pool_confirm"/>
</androidx.constraintlayout.widget.ConstraintLayout>