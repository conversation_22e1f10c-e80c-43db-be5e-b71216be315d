<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/id_edittext_container"
        android:layout_width="fill_parent"
        android:layout_height="50dp"
        android:background="@color/didi_page_bg"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_city"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:drawableRight="@drawable/jdme_didi_icon_arrow"
            android:drawablePadding="2dp"
            android:onClick="onClick"
            android:text="@string/me_positioning"
            android:textColor="@color/conference_black_color2"
            android:textSize="@dimen/me_text_size_middle" />

        <View
            android:layout_width="1dp"
            android:layout_height="30dp"
            android:layout_gravity="center_vertical"
            android:background="#E0E0E0" />

        <EditText
            android:id="@+id/id_workplace_search_et"
            style="@android:style/TextAppearance.Widget.EditText"
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_marginLeft="11dp"
            android:layout_weight="1"
            android:background="@color/didi_page_bg"
            android:cursorVisible="true"
            android:drawablePadding="8dp"
            android:gravity="left|center_vertical"
            android:hint="@string/me_didi_hint_input_address"
            android:paddingLeft="5dp"
            android:singleLine="true"
            android:textColor="@color/conference_black_color2"
            android:textColorHint="@color/conference_black_color"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:gravity="center_vertical"
            android:text="@string/me_cancel_not_translate"
            android:onClick="onClick"
            android:textColor="?me_theme_major_color"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_tab"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:orientation="horizontal">


        <LinearLayout
            android:id="@+id/ll_history"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:onClick="onClick">

            <TextView
                android:id="@+id/tv_history"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@drawable/jdme_didi_icon_address_history_checked_default"
                android:drawablePadding="5dp"
                android:gravity="center_horizontal"
                android:text="@string/me_didi_address_history"
                android:textColor="?me_theme_major_color"
                android:textSize="@dimen/me_text_size_middle" />
        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:background="#E0E0E0" />

        <LinearLayout
            android:id="@+id/ll_business"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center" android:onClick="onClick">

            <TextView
                android:id="@+id/tv_business"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@drawable/jdme_didi_icon_address_location_normal"
                android:drawablePadding="5dp"
                android:text="@string/me_didi_address_business"
                android:textColor="@color/conference_black_color"
                android:textSize="@dimen/me_text_size_middle" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:background="#E0E0E0" />

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/me_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:cacheColorHint="@color/transparent"
            android:divider="@color/black_divider"
            android:dividerHeight="@dimen/me_divide_height_min"
            android:listSelector="@drawable/jdme_selector_my_actionbar" />
    </com.jd.oa.ui.FrameView>

</LinearLayout>