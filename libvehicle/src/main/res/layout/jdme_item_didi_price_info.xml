<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="fill_parent"
              android:layout_height="wrap_content"
              android:gravity="center_vertical"
              android:orientation="horizontal"
              android:paddingTop="15dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/me_didi_other"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14"/>

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:layout_weight="1"
        android:background="@drawable/jdme_didi_dotted"/>

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/me_didi_yuan"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14"/>

</LinearLayout>