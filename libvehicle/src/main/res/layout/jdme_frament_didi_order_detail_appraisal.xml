<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <include layout="@layout/jdme_item_driver_info" />

            <LinearLayout
                android:id="@+id/ll_price_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:background="?attr/me_theme_major_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_didi_pay_succsess"
                        android:textColor="?attr/me_theme_major_color"
                        android:textSize="@dimen/me_text_size_small" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:background="?attr/me_theme_major_color" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10dp"
                    android:gravity="center_horizontal">

                    <TextView
                        android:id="@+id/tv_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="10dp"
                        android:singleLine="true"
                        android:text="25"
                        android:textColor="?attr/me_theme_major_color"
                        android:textSize="36sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@string/me_didi_yuan"
                        android:textColor="?attr/me_theme_major_color"
                        android:textSize="@dimen/me_text_size_larger" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_query_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:drawableRight="@drawable/jdme_didi_icon_arrow_down"
                    android:drawablePadding="6dp"
                    android:text="@string/ne_didi_query_detail"
                    android:textColor="@color/conference_black_color"
                    android:textSize="15dp" />

                <LinearLayout
                    android:id="@+id/ll_price_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_order_time_range"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="15dp"
                        android:singleLine="true"
                        android:textColor="@color/black_assist"
                        android:textSize="@dimen/me_text_size_small" />

                    <LinearLayout
                        android:id="@+id/ll_price_container"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="40dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="40dp"
                        android:gravity="center_vertical"
                        android:orientation="vertical" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:gravity="center_vertical">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ne_didi_appraisal_anonymous"
                    android:textColor="@color/conference_black_color"
                    android:textSize="15dp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />
            </LinearLayout>

            <RatingBar
                android:id="@+id/rab_yellow_star"
                style="@style/me_DidiYellowStar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:isIndicator="false"
                android:numStars="5"
                android:progressDrawable="@drawable/jdme_didi_star_yellow"
                android:rating="0"
                android:stepSize="1" />

            <LinearLayout
                android:id="@+id/ll_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="50dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="50dp"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_content_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:textColor="#ff8903"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="@string/ne_didi_titile_please_sel_label"
                    android:textColor="@color/conference_black_color"
                    android:textSize="@dimen/me_text_size_14" />

                <com.jd.oa.feedback.widget.FlowRadioGroup
                    android:id="@+id/rg_content_option"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <EditText
                        android:id="@+id/et_msg"
                        android:layout_width="fill_parent"
                        android:layout_height="75dp"
                        android:layout_marginTop="2dp"
                        android:background="@drawable/jdme_bg_didi_textview_border"
                        android:gravity="top"
                        android:hint="@string/ne_didi_hint_appraisal"
                        android:maxLength="40"
                        android:paddingLeft="6dp"
                        android:paddingTop="7dp"
                        android:textColor="@color/black_main_summary"
                        android:textColorHint="@color/conference_black_color4"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/tv_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginRight="5dp"
                        android:layout_marginBottom="20dp"
                        android:text="40"
                        android:textColor="@color/light_gray"
                        android:textSize="13dp" />

                </RelativeLayout>

                <Button
                    android:id="@+id/btn_submit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:background="?attr/me_btn_selector"
                    android:text="@string/ne_didi_submit_appraisal"
                    android:textSize="@dimen/me_text_size_middle" />

            </LinearLayout>

            <Button
                android:id="@+id/btn_show_colleague"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="30dp"
                android:layout_marginBottom="8dp"
                android:background="?attr/me_btn_selector"
                android:text="@string/me_car_pool_show_colleague" />

            <TextView
                android:id="@+id/tv_tip_approval"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:textColor="@color/tab_text_color_default"
                android:textSize="@dimen/me_text_size_small"
                android:visibility="gone"
                tools:text="tipstipstipstipstipstips"
                tools:visibility="visible" />


            <TextView
                android:id="@+id/tv_reimbursement"
                android:layout_width="220dp"
                android:layout_height="44dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="48dp"
                android:layout_marginBottom="100dp"
                android:background="@drawable/jdme_bg_corner_red"
                android:gravity="center"
                android:text="@string/me_didi_reimbursement"
                android:textColor="#FFF"
                android:textSize="16sp"
                android:visibility="gone" />

            <include layout="@layout/jdme_layout_didi_reimbursement_confirm" />

            <LinearLayout
                android:id="@+id/ll_btn_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="15dp"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/ibtn_omplaints"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_didi_icon_complaints_default" />
            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <Button
        android:id="@+id/btn_view_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="14dp"
        android:layout_marginBottom="40dp"
        android:background="?attr/me_btn_selector"
        android:text="@string/me_car_view_detail"
        android:textColor="@color/white"
        android:textSize="18dp"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>