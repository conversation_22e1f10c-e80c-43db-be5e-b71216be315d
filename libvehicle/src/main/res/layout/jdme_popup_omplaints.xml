<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:background="@color/more_page_bg_color">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/me_didi_pop_tip"
            android:textColor="#9C9C9C"
            android:textSize="13sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white">

        <TextView
            android:id="@+id/me_pop_tel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/me_didi_pop_tel"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#8f9296" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white">

        <TextView
            android:id="@+id/me_pop_cancel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/me_didi_pop_cancel"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>
</LinearLayout>