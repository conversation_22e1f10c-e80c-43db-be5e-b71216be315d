<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/comm_divider" />

    <LinearLayout
        android:id="@+id/ll_expend_disable_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:gravity="center_vertical"
        android:visibility="gone">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_padding_infocirclecircle"
            android:textColor="#CDCDCD"
            android:textSize="@dimen/JMEIcon_14"/>

        <TextView
            android:id="@+id/tv_upgrade_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_expend_disable_tip"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="25dp">

        <Button
            android:id="@+id/btn_call_taxi_upgrade"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="11dp"
            android:layout_weight="1"
            android:background="?attr/me_btn_black_border_selector"
            android:enabled="false"
            android:text="@string/me_didi_call_taxi_upgrade"
            android:textColor="@color/jdme_text_btn_color_black_border"
            android:textSize="18sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/btn_call_taxi"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="?attr/me_btn_selector_disable_gray"
            android:enabled="false"
            android:text="@string/me_didi_call_taxi"
            android:textSize="18sp" />
    </LinearLayout>
</RelativeLayout>