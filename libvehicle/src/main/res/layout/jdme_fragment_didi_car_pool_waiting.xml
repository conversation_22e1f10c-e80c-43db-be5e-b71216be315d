<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_countdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="32dp"
        android:textSize="30sp"
        android:textColor="@color/comm_text_title"
        tools:text="01：29"/>
    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_countdown"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="16dp"
        android:textSize="@dimen/comm_text_normal_large"
        android:textColor="@color/comm_text_title"
        android:text="@string/me_car_request_sent"/>
    <View
        android:id="@+id/view_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        android:background="@color/comm_divider"
        app:layout_constraintTop_toBottomOf="@id/tv_tips"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="15dp"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recycler"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/me_setting_background"/>
    
    <Button
        android:id="@+id/btn_cancel"
        android:minHeight="0dp"
        android:minWidth="0dp"
        android:layout_width="140dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toBottomOf="@id/recycler"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_call"
        android:layout_marginTop="48dp"
        android:textSize="@dimen/comm_text_normal_large"
        android:textColor="@color/selector_didi_text_black"
        android:text="@string/me_didi_menu_cancel"
        android:background="@drawable/jdme_btn_round_gray"
        android:enabled="false"/>
    <Button
        android:id="@+id/btn_call"
        android:minHeight="0dp"
        android:minWidth="0dp"
        android:layout_width="140dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="@id/btn_cancel"
        app:layout_constraintLeft_toRightOf="@id/btn_cancel"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="38dp"
        android:paddingEnd="38dp"
        android:textSize="@dimen/comm_text_normal_large"
        android:textColor="@color/comm_text_white"
        android:background="@drawable/jdme_btn_red"
        android:text="@string/me_didi_call_taxi"
        android:enabled="false"/>

</androidx.constraintlayout.widget.ConstraintLayout>