<?xml version="1.0" encoding="utf-8"?><!-- 我的假期 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">
    <FrameLayout
        android:id="@+id/ll_normal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="visible">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_margin="4dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:textColor="#252525"
            android:textSize="14sp"
            android:background="@drawable/jdme_bg_corner_didi_colleagues_del_gray"
            tools:text="geshuyue1"/>

        <ImageView
            android:id="@+id/iv_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="-6dp"
            android:layout_marginTop="-6dp"
            android:padding="6dp"
            android:layout_gravity="top|end"
            android:src="@drawable/jdme_ic_didi_icon_colleagues_delete" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/ll_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_margin="4dp"
        android:paddingTop="2dp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/jdme_didi_icon_colleagues_add_gray" />
    </LinearLayout>
</LinearLayout>