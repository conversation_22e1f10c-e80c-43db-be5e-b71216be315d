<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_shape_white_radius_bg"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:scaleType="center"
        android:src="@drawable/jdme_daka_use_car_close" />

    <TextView
        android:id="@+id/tv_set_car_address_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="20dp"
        android:layout_marginTop="20dp"
        android:text="@string/me_car_address_use_car_tip"
        android:textColor="@color/jdme_color_first"
        android:textSize="16sp" />

    <View
        android:id="@+id/view_space"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/tv_set_car_address_title"
        android:layout_marginBottom="10dp"
        android:background="#f5f5f5" />

    <TextView
        android:id="@+id/tv_set_car_address_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/view_space"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="24dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:text="@string/me_car_address_use_car_tip_desc"
        android:textColor="@color/jdme_color_first"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_add"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_below="@+id/tv_set_car_address_desc"
        android:background="@drawable/jdme_shape_bottom_radius_red_btn_bg"
        android:gravity="center"
        android:text="@string/me_car_address_use_car_tip_btn"
        android:textColor="@color/white"
        android:textSize="16dp" />


</RelativeLayout>
