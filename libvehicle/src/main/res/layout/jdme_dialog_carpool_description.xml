<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/jdme_dialog_bg_round_corners"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#f4f4f4">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_popup_title_round_corners"
            android:gravity="center"
            android:text="@string/me_car_pool_description_title"
            android:textColor="@color/black_main_title"
            android:textSize="15sp"/>
    </RelativeLayout>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white">
        <FrameLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="12dp"/>
        <TextView
            android:id="@+id/tv_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textSize="@dimen/comm_text_normal_large"
            android:textColor="@color/comm_text_normal"
            android:text="@string/me_didi_web_loading"
            android:visibility="invisible"
            tools:visibility="visible"/>
    </FrameLayout>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/comm_white">
        <Button
            style="@style/Widget.AppCompat.Button.Borderless"
            android:id="@+id/btn_confirm"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:layout_marginStart="36dp"
            android:layout_marginEnd="36dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/jdme_btn_red"
            android:text="@string/me_didi_tip_iknow_not_translate_n"/>
    </FrameLayout>
</LinearLayout>