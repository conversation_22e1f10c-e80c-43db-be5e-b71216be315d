<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="fill_parent"
              android:layout_height="wrap_content"
              android:background="@color/white"
              android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="10dp"
        android:paddingLeft="15dp"
        android:paddingTop="10dp"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14"/>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:background="@color/black_transparent_12"/>
</LinearLayout>