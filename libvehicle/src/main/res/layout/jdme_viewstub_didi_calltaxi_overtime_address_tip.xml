<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:src="@drawable/jdme_icon_didi_car_address_arrow_top" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_bg_circle_gray"
        android:paddingLeft="4dp"
        android:paddingRight="4dp">


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_set_car_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:button="@drawable/jdme_selector_didi_checkbox" />

        <TextView
            android:id="@+id/tv_set_car_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/cb_set_car_address"
            android:layout_toRightOf="@+id/cb_set_car_address"
            android:text="@string/me_car_address_set_checkbox_tip"
            android:textColor="@color/jdme_color_first"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/tv_set_car_address"
            android:layout_below="@+id/tv_set_car_address"
            android:layout_marginBottom="6dp"
            android:layout_marginRight="26dp"
            android:layout_marginTop="6dp" />
    </RelativeLayout>
</LinearLayout>