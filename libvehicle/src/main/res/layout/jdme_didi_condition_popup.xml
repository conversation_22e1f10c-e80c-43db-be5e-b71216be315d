<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_popup_title_round_corners"
            android:gravity="center"
            android:text="@string/me_didi_description_page_title"
            android:textColor="@color/black_main_title"
            android:textSize="15sp"/>


        <ImageButton
            android:id="@+id/img_btn_didi_close_poup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="right"
            android:layout_marginRight="8dp"
            android:background="@drawable/jdme_del_icon_red_pressed"/>

    </RelativeLayout>


    <WebView
        android:id="@+id/webview_didi_condition_popup"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white"/>

    <!-- 这个view只是为了显示成圆角 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="7dp"
        android:layout_below="@+id/webview_didi_condition_popup"
        android:background="@drawable/jdme_popup_body_round_corners"/>

    <LinearLayout
        android:id="@+id/ll_didi_condition_popup_summary"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_margin="8dp"
        android:background="@drawable/jdme_selector_img_transparent_dashed"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/me_friendly_prompt"
            android:textColor="@color/black_assist"
            android:textSize="14dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center_horizontal"
            android:text="@string/me_car_use_car_not_allow"
            android:textColor="@color/black_main_summary"
            android:textSize="12dp"/>
    </LinearLayout>
</LinearLayout>