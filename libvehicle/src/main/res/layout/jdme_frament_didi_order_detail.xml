<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include layout="@layout/jdme_item_driver_info" />
            <!--订单价格异常提示信息-->
            <RelativeLayout
                android:id="@+id/rlWarning"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:background="@color/jdme_color_didi_red_bg"
                android:paddingLeft="18dp"
                android:paddingRight="18dp">

                <TextView
                    android:id="@+id/tvWarning"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:drawableStart="@drawable/jdme_icon_didi_notice"
                    android:drawableLeft="@drawable/jdme_icon_didi_notice"
                    android:drawablePadding="11dp"
                    android:gravity="left"
                    android:textColor="@color/jdme_color_didi_red_text" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:gravity="center_vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="?attr/me_theme_major_color" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_pay_succsess"
                    android:textColor="?attr/me_theme_major_color"
                    android:textSize="@dimen/me_text_size_small" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="?attr/me_theme_major_color" />
            </LinearLayout>
            <!--订单金额-->
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center_vertical">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="48dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_order_sum_money"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_small" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="48dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:gravity="center_horizontal">

                <TextView
                    android:id="@+id/tv_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:singleLine="true"
                    android:textColor="?attr/me_theme_major_color"
                    android:textSize="36sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="@string/me_didi_yuan"
                    android:textColor="?attr/me_theme_major_color"
                    android:textSize="@dimen/me_text_size_larger" />

            </LinearLayout>
            <!--预估金额-->
            <TextView
                android:id="@+id/tvEstimatePrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:text="@string/me_didi_order_guess_money"
                android:textColor="@color/black_252525"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center_vertical">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="48dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_order_detail"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_small" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="48dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_order_time_range"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="15dp"
                android:singleLine="true"
                android:textColor="@color/black_assist"
                android:textSize="@dimen/me_text_size_small" />

            <LinearLayout
                android:id="@+id/ll_price_container"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="40dp"
                android:layout_marginBottom="30dp"
                android:gravity="center_vertical"
                android:orientation="vertical" />

            <!--确认支付、延缓支付按钮-->
            <LinearLayout
                android:id="@+id/ll_confirm_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btn_confirm_stroke"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="18dp"
                        android:layout_marginRight="18dp"
                        android:layout_weight="1"
                        android:background="@drawable/jdme_selector_button_fee_default"
                        android:text="@string/me_didi_confirm_stroke"
                        android:textColor="@drawable/jdme_selector_didi_btn_fee"
                        android:textSize="@dimen/me_text_size_middle" />

                    <Button
                        android:id="@+id/btn_confirm_fee"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="18dp"
                        android:layout_marginRight="18dp"
                        android:layout_weight="1"
                        android:background="?attr/me_btn_selector"
                        android:text="@string/me_didi_confirm_fee"
                        android:textSize="@dimen/me_text_size_middle" />
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center"
                    android:text="@string/me_didi_order_detail_remark2"
                    android:textColor="@color/grey_text_color"
                    android:textSize="13sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_tip_approval"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="30dp"
                android:layout_marginRight="20dp"
                android:textColor="@color/tab_text_color_default"
                android:textSize="@dimen/me_text_size_small"
                android:visibility="gone"
                tools:text="需要审批" />

            <TextView
                android:id="@+id/tv_reimbursement"
                android:layout_width="220dp"
                android:layout_height="44dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="48dp"
                android:layout_marginBottom="100dp"
                android:background="@drawable/jdme_bg_corner_red"
                android:gravity="center"
                android:text="@string/me_didi_reimbursement"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:visibility="gone" />

            <include layout="@layout/jdme_layout_didi_reimbursement_confirm" />

            <Button
                android:id="@+id/btn_show_colleague"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="30dp"
                android:layout_marginEnd="30dp"
                android:layout_marginBottom="15dp"
                android:background="?attr/me_btn_selector"
                android:text="@string/me_car_pool_show_colleague" />

            <LinearLayout
                android:id="@+id/ll_btn_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="15dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageButton
                    android:id="@+id/ibtn_appraisal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:background="@drawable/jdme_didi_icon_appraisal_default" />

                <ImageButton
                    android:id="@+id/ibtn_omplaints"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:background="@drawable/jdme_didi_icon_complaints_default" />

                <ImageButton
                    android:id="@+id/ib_help"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:background="@color/transparent"
                    android:src="@drawable/jdme_didi_icon_urgent_help" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/btn_view_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="14dp"
        android:layout_marginBottom="40dp"
        android:background="?attr/me_btn_selector"
        android:text="@string/me_car_view_detail"
        android:textColor="@color/white"
        android:textSize="18dp"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>