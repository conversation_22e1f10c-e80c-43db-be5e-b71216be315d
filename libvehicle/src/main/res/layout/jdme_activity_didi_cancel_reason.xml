<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/jdme_bg_actionbar_white"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_didi_title_cancel_reson"
            android:textColor="@color/comm_text_title"
            android:textSize="@dimen/me_text_size_xlarger" />
    </LinearLayout>

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_frame"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:orientation="vertical" />

                <Button
                    android:id="@+id/btn_submit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="60dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginRight="60dp"
                    android:layout_marginBottom="40dp"
                    android:background="?attr/me_btn_selector"
                    android:text="@string/me_submit_not_translate"
                    android:textSize="@dimen/me_text_size_middle" />
            </LinearLayout>
        </ScrollView>
    </com.jd.oa.ui.FrameView>
</LinearLayout>
