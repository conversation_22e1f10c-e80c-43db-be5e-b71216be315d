<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg">

    <com.tencent.tencentmap.mapsdk.map.MapView
        android:id="@+id/mapView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <LinearLayout
        android:id="@+id/ll_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="78dp"
        android:orientation="horizontal"
        android:background="#fff4e6"
        android:padding="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:visibility="gone"
        tools:visibility="visible">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_car_notice"
            android:layout_marginRight="10dp"
            android:layout_marginEnd="10dp"
            android:layout_gravity="center_vertical" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:layout_gravity="center_vertical"
            android:textColor="#f97c0e"
            android:textSize="@dimen/me_text_size_14"
            tools:text="@string/me_didi_waitcar_tip3"/>

    </LinearLayout>
    <FrameLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="22dp">
        <LinearLayout
            android:id="@+id/ll_cancel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:background="@drawable/jdme_selector_common_ripple_effect"
            android:padding="12dp"
            android:gravity="center"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:visibility="visible">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_car_cancel"
                android:layout_marginRight="10dp"
                android:layout_marginEnd="10dp" />
            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:layout_gravity="center_vertical"
                android:text="@string/me_car_cancel"
                android:textColor="#7a7a7a"
                android:textSize="@dimen/me_text_size_14" />
        </LinearLayout>
        <include
            android:id="@+id/layout_cant_cancel"
            layout="@layout/jdme_include_didi_cant_cancel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:visibility="gone" />
    </FrameLayout>

</RelativeLayout>