<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="15dp"
        android:paddingLeft="20dp"
        android:paddingTop="15dp"
        android:text="@string/me_car_address_cooleagues_search"
        android:textColor="#808080"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/et_key"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/comm_white"
        android:drawableLeft="@drawable/jdme_didi_icon_colleagues_search"
        android:drawablePadding="2dp"
        android:hint="@string/me_car_address_cooleagues_search_tip"
        android:imeOptions="actionSearch"
        android:inputType="text"
        android:paddingLeft="20dp"
        android:singleLine="true"
        android:textColor="#727272"
        android:textSize="14sp" />
</LinearLayout>