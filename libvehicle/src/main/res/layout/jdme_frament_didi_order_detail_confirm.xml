<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="108dip"
                android:background="#FFE7E7">

                <ImageView
                    android:id="@+id/iv_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/jdme_icon_didi_confirm_tip" />

                <TextView
                    android:id="@+id/tv_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="80dp"
                    android:layout_toRightOf="@+id/iv_tip"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_larger" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:gravity="center_vertical">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_split_order_detail"
                    android:textColor="@color/conference_black_color"
                    android:textSize="@dimen/me_text_size_small" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="50dp"
                android:layout_marginTop="25dp"
                android:layout_marginRight="40dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_didi_order_title_departure_time"
                        android:textColor="@color/conference_black_color"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_departure_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="@string/me_didi_order_title_from"
                        android:textColor="@color/conference_black_color"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_from"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="@string/me_didi_order_title_to"
                        android:textColor="@color/conference_black_color"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_to"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_14" />
                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_title_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_didi_order_title_fee_count"
                        android:textColor="@color/conference_black_color"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_toRightOf="@+id/tv_title_count"
                        android:text="@string/me_didi_pay_price"
                        android:textColor="@color/jdme_color_myapply_cancel"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_opt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="5dp"
                        android:layout_toLeftOf="@+id/iv_btn_opt"
                        android:text="@string/me_didi_order_title_opt_up"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_14" />

                    <ImageView
                        android:id="@+id/iv_btn_opt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/jdme_icon_didi_confirm_up" />
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_price_container"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="40dp"
                android:gravity="center_vertical"
                android:orientation="vertical" />

            <LinearLayout
                android:id="@+id/ll_confirm_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="50dp"
                android:orientation="vertical">

                <Button
                    android:id="@+id/btn_confirm_stroke"
                    android:layout_width="fill_parent"
                    android:layout_height="40dp"
                    android:layout_marginLeft="50dp"
                    android:layout_marginTop="60dp"
                    android:layout_marginRight="50dp"
                    android:background="?attr/me_btn_selector"
                    android:text="@string/me_didi_order_confirm_fee_exception"
                    android:textSize="@dimen/me_text_size_middle" />

                <Button
                    android:id="@+id/btn_confirm_fee"
                    android:layout_width="fill_parent"
                    android:layout_height="40dp"
                    android:layout_marginLeft="50dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginRight="50dp"
                    android:background="@drawable/jdme_selector_button_fee_default"
                    android:text="@string/me_didi_order_confirm_fee"
                    android:textColor="@drawable/jdme_selector_didi_btn_fee"
                    android:textSize="@dimen/me_text_size_middle" />

            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/btn_view_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="14dp"
        android:layout_marginBottom="40dp"
        android:background="?attr/me_btn_selector"
        android:text="@string/me_car_view_detail"
        android:textColor="@color/white"
        android:textSize="18dp"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>