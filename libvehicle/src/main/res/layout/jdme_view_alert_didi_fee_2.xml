<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lLayout_bg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_didi_fee_alert_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txt_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="25dp"
        android:lineSpacingExtra="6dp"
        android:textColor="@color/jdme_color_first"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/txt_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:textColor="@color/jdme_color_first"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/txt_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="17dp"
        android:gravity="center"
        android:textColor="@color/jdme_color_myapply_cancel"
        android:textSize="30sp"
        android:textStyle="bold" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="30dp"
        android:background="@color/alertdialog_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_neg"
            android:layout_width="wrap_content"
            android:layout_height="43dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_alertdialog_left_selector"
            android:gravity="center"
            android:text="@string/me_cancel"
            android:textColor="@color/jdme_color_second"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/img_line"
            android:layout_width="0.5dp"
            android:layout_height="43dp"
            android:background="@color/alertdialog_line" />

        <Button
            android:id="@+id/btn_pos"
            android:layout_width="wrap_content"
            android:layout_height="43dp"
            android:layout_weight="1"
            android:background="?attr/me_btn_selector"
            android:gravity="center"
            android:text="@string/me_ok"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>