<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_address_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"></androidx.recyclerview.widget.RecyclerView>

    <TextView
        android:id="@+id/tv_address_add"
        style="@style/my_button_default"
        android:layout_gravity="center"
        android:layout_marginBottom="20dp"
        android:layout_marginLeft="44dp"
        android:layout_marginRight="44dp"
        android:layout_marginTop="20dp"
        android:text="@string/me_car_address_button_add" />

    <TextView
        android:id="@+id/tv_address_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        android:text=""
        android:textColor="@color/black_252525"
        android:textSize="12sp" />
</LinearLayout>