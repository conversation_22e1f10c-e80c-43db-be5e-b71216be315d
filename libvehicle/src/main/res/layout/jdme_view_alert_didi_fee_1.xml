<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_didi_fee_alert_bg"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="20dp"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_didi_icon_fee_tip" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:text="@string/me_didi_tip_fee_success"
            android:textColor="@color/jdme_color_first"
            android:textSize="16sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_tip_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ll_tip"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:text="@string/me_didi_tip_fee"
        android:textColor="@color/actionsheet_gray"
        android:textSize="14sp" />

    <View
        android:id="@+id/v_split"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="20dp"
        android:layout_below="@+id/tv_tip_1"
        android:background="#E3E5E9" />

    <TextView
        android:id="@+id/tv_ikonw"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/v_split"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="15dp"
        android:layout_marginTop="15dp"
        android:gravity="center_horizontal"
        android:text="@string/me_didi_tip_iknow_not_translate_n"
        android:textColor="#F23030"
        android:textSize="16sp" />
</RelativeLayout>
