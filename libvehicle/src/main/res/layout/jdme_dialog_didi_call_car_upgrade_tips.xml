<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/me_didi_call_car_upgrade"
        android:textColor="#232930"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="64dp"
        android:layout_marginEnd="24dp"
        android:lineSpacingMultiplier="1.2"
        android:textColor="#666666"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="企业最终支付金额与预估价一致，路途中有变化造成的费用增加由员工个人承担\n\n如有费用异议联系：4000000777" />

    <CheckBox
        android:id="@+id/cb_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:button="@drawable/jdme_selector_checkbox_car"
        android:gravity="center_vertical"
        android:paddingStart="4dp"
        android:text="@string/me_car_pool_dont_remind"
        android:textColor="#999999"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@+id/tv_tips"
        app:layout_constraintLeft_toLeftOf="@id/tv_tips"
        app:layout_constraintStart_toStartOf="@+id/tv_tips"
        app:layout_constraintTop_toBottomOf="@id/tv_tips" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginTop="16dp"
        android:background="@color/comm_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cb_check" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_ripple"
        android:gravity="center"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:text="@string/me_cancel"
        android:textColor="#999999"
        android:textSize="@dimen/comm_text_normal_xlarge"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/view_divider_button"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />

    <View
        android:id="@+id/view_divider_button"
        android:layout_width="@dimen/comm_divider_height"
        android:layout_height="0dp"
        android:background="@color/comm_divider"
        app:layout_constraintBottom_toBottomOf="@id/tv_cancel"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_cancel" />

    <TextView
        android:id="@+id/tv_call"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_ripple"
        android:gravity="center"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:text="@string/me_ok"
        android:textColor="#292929"
        android:textSize="@dimen/comm_text_normal_xlarge"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_cancel"
        app:layout_constraintLeft_toRightOf="@id/view_divider_button"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>