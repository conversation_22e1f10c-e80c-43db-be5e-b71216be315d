<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/guide_line"
        android:paddingBottom="8dp"
        android:background="#ff4b4a5b"/>

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="14dp"
        android:scaleType="centerInside"
        android:src="@drawable/jdme_ic_car_pool_colleague_default_icon"/>

    <TextView
        android:id="@+id/tv_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar"
        android:layout_marginTop="10dp"
        android:textSize="@dimen/comm_text_title"
        android:textColor="@color/comm_white"
        android:visibility="gone"
        tools:text="139****1111"/>
    <View
        android:id="@+id/guide_line"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_phone"
        android:layout_marginTop="14dp"/>

    <TextView
        android:id="@+id/tv_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/guide_line"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="24dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:drawableStart="@drawable/jdme_ic_car_pool_location"
        android:drawablePadding="8dp"
        android:textColor="@color/comm_text_title"
        android:textSize="@dimen/comm_text_normal"
        tools:text="朝阳区望京新城102号楼1单元503朝阳区望京新城102号楼1单元503"/>
    <View
        android:id="@+id/view_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_address"
        android:layout_marginTop="24dp"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_close"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:gravity="center_horizontal"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_red"
        android:text="@string/me_car_pool_close"
        android:background="@drawable/jdme_ripple"/>
</androidx.constraintlayout.widget.ConstraintLayout>