<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <include layout="@layout/jdme_item_driver_info"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/ll_order_detail"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:gravity="center_vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"
            android:background="@color/black_assist" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_didi_order_detail"
            android:textColor="@color/black_assist"
            android:textSize="@dimen/me_text_size_small" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"
            android:background="@color/black_assist" />
    </LinearLayout>

    <ImageView
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="88dp"
        android:src="@drawable/jdme_didi_order_notice" />

    <TextView
        android:id="@+id/tv_notice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="@string/me_didi_order_cancel"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_middle" />

    <LinearLayout
        android:id="@+id/ll_btn_group"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:gravity="bottom|center_horizontal"
        android:orientation="horizontal"
        android:visibility="visible">

        <ImageButton
            android:id="@+id/ibtn_omplaints"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_didi_icon_complaints_default" />
    </LinearLayout>

    <Button
        android:id="@+id/btn_view_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="14dp"
        android:layout_marginBottom="40dp"
        android:background="?attr/me_btn_selector"
        android:text="@string/me_car_view_detail"
        android:textColor="@color/white"
        android:textSize="18dp"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>