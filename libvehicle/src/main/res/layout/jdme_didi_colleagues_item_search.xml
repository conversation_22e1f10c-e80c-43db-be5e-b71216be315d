<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:background="@drawable/ddtl_selector_message_list"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <!--头像与默认图标区域-->
    <FrameLayout
        android:id="@+id/me_container_left"
        android:layout_width="56dp"
        android:layout_height="match_parent">

        <com.jd.oa.mae.bundles.widget.CircleImageView
            android:id="@+id/jdme_contact_avatar"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ddtl_avatar_personal_normal_blue" />
    </FrameLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:layout_toRightOf="@+id/me_container_left"
        android:orientation="vertical">

        <TextView
            android:id="@+id/jdme_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/jdme_color_first"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/jdme_uid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/jdme_name"
            android:ellipsize="end"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/jdme_color_third"
            android:textSize="15sp" />


        <TextView
            android:id="@+id/jdme_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/jdme_name"
            android:layout_marginTop="10dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/jdme_color_bottom_bar"
            android:textSize="12sp" />

    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="56dp"
        android:background="@color/jdme_color_divider" />

    <TextView
        android:id="@+id/tv_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="10dp"
        android:background="@drawable/jdme_bg_corner_didi_colleagues_add"
        android:padding="4dp"
        android:text="@string/me_car_address_colleagues_add"
        android:textColor="#ff0000"
        android:textSize="14sp" />
</RelativeLayout>