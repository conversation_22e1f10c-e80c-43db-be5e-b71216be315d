<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="84dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="84dp"
        android:background="@color/comm_white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:textColor="@color/black_252525"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_title"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:textColor="@color/jdme_color_second"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="18dp"
            android:layout_toRightOf="@+id/tv_title"
            android:background="@drawable/jdme_bg_corner_rectangle_red"
            android:textColor="#ff0000"
            android:textSize="14sp" />


        <TextView
            android:id="@+id/tv_flow_flag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:text="@string/me_car_address_approving"
            android:textColor="#ff0000"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/im_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:src="@drawable/me_car_address_edit_ic" />
    </RelativeLayout>

</LinearLayout>