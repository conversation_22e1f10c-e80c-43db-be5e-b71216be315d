<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg">

    <TextView
        android:id="@+id/tv_msg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:lineSpacingMultiplier="1.1"
        android:background="#fff4e6"
        android:textSize="@dimen/comm_text_normal"
        android:textColor="#f86e21"
        android:visibility="invisible"
        tools:visibility="visible"
        tools:text="文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示"/>

    <com.jd.oa.business.didi.widget.CircleProgressRunningView
        android:id="@+id/cprv_driving"
        android:layout_width="180dp"
        android:layout_height="180dp"
        android:layout_marginTop="50dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/layout_bottom"
        app:layout_constraintVertical_bias="0.2"
        app:me_tv_circleColor="?me_theme_major_color"
        app:me_tv_img="@drawable/jdme_icon_didi_car_default"
        app:me_tv_texSize="15sp" />

    <LinearLayout
        android:id="@+id/ll_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal"
        android:background="#fff4e6"
        android:padding="8dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:visibility="invisible"
        tools:visibility="visible">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_car_notice"
            android:layout_marginRight="10dp"
            android:layout_marginEnd="10dp"
            android:layout_gravity="center_vertical"/>

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:layout_gravity="center_vertical"
            android:textColor="#f97c0e"
            android:lineSpacingMultiplier="1.1"
            android:textSize="@dimen/me_text_size_14"
            tools:text="@string/me_travel_help_tips"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_bottom"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:orientation="vertical">
        <include
            android:id="@+id/ll_driver_info"
            layout="@layout/jdme_item_driver_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            app:layout_constraintTop_toBottomOf="@id/ll_driver_info"
            android:background="#eeeeee"/>
        <LinearLayout
            android:id="@+id/layout_colleague_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/view_divider"
            android:orientation="vertical"
            android:visibility="gone"
            android:background="@color/white"
            tools:visibility="visible">
            <LinearLayout
                android:id="@+id/layout_colleague_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#eeeeee"/>
        </LinearLayout>
        <FrameLayout
            android:id="@+id/ll_btn_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal"
                android:background="#ffffff"
                android:gravity="center">
                <LinearLayout
                    android:id="@+id/ll_help"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:gravity="center">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:src="@drawable/jdme_ic_didi_urgent_help"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_travel_help"
                        android:textColor="#7a7a7a"
                        android:textSize="@dimen/me_text_size_14"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_complain"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:gravity="center">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:src="@drawable/ic_car_complain"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp" />
                    <TextView
                        android:id="@+id/tvComplain"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_didi_title_order_omplaints"
                        android:textColor="#7a7a7a"
                        android:textSize="@dimen/me_text_size_14"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_share"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:gravity="center">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:src="@drawable/ic_car_share"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp" />
                    <TextView
                        android:id="@+id/tvShare"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_didi_title_order_share"
                        android:textColor="#7a7a7a"
                        android:textSize="@dimen/me_text_size_14"/>

                </LinearLayout>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
