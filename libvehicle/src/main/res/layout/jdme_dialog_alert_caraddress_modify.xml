<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/jdme_bg_circle_white"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="8dp"
                android:src="@drawable/jdme_icon_red_close_caraddress_modify" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_title_msg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:gravity="center"
            android:text=""
            android:textColor="#FF232930"
            android:textSize="16sp" />

        <EditText
            android:maxLength="20"
            android:id="@+id/ed_approval"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="24dp"
            android:layout_marginBottom="16dp"
            android:background="#F8F9FA"
            android:gravity="left"
            android:hint="@string/please_enter_reason"
            android:maxHeight="120dp"
            android:minHeight="120dp"
            android:padding="12dp"
            android:textColor="#FF232930"
            android:textColorHint="#8F959E"
            android:textSize="14sp" />


        <Button
            android:id="@+id/bt_submit"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="24dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/jdme_bg_circle_red"
            android:gravity="center"
            android:text="@string/me_car_address_submit"
            android:textColor="#FFFFFFFF"
            android:textSize="@dimen/libui_font_40pt_14sp" />
    </LinearLayout>

</LinearLayout>