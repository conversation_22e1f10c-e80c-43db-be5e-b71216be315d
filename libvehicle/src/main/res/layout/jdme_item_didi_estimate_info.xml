<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_price"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="9dp"
        android:layout_marginRight="9dp"
        android:hardwareAccelerated="true"
        app:cardCornerRadius="8dp"
        app:cardElevation="5dp"
        app:cardPreventCornerOverlap="true"
        app:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="98dp"
            android:gravity="center_vertical">

            <LinearLayout
                android:id="@+id/linearLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/view"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_title_estimate_time"
                    android:textColor="#292929"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="#292929"
                    android:textSize="24sp"
                    tools:text="29分钟" />

            </LinearLayout>

            <View
                android:id="@+id/view"
                android:layout_width="1dp"
                android:layout_height="24dp"
                android:background="#D4D8DC"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/view"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_title_estimate_distance"
                    android:textColor="#292929"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_distance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textColor="#292929"
                    android:textSize="24sp"
                    tools:text="24.9公里" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_price_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="9dp"
        android:layout_marginRight="9dp"
        android:hardwareAccelerated="true"
        app:cardCornerRadius="4dp"
        app:cardElevation="6dp"
        app:cardPreventCornerOverlap="true"
        app:cardUseCompatPadding="true">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            tools:visibility="gone" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/icon_list"
                android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="12dp" />

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/icon_list"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="24dp"
                android:textColor="#292929"
                android:textSize="24dp"
                tools:text="27.8~39.1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/tv_price"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="3dp"
                android:layout_toStartOf="@id/tv_price"
                android:text="@string/me_didi_estimate"
                android:textColor="#6F6F6F"
                android:textSize="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/tv_price"
                android:layout_marginStart="8dp"
                android:layout_marginBottom="3dp"
                android:layout_toEndOf="@id/tv_price"
                android:text="@string/me_didi_yuan"
                android:textColor="#6F6F6F"
                android:textSize="12dp" />
        </RelativeLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>