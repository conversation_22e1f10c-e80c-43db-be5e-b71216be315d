<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_didi_fee_alert_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="30dp"
        android:gravity="center_horizontal"
        android:textColor="@color/jdme_color_first"
        android:textSize="14sp" />

    <View
        android:id="@+id/v_split"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/tv_tip"
        android:background="#E3E5E9" />

    <TextView
        android:id="@+id/tv_ikonw"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/v_split"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="15dp"
        android:layout_marginTop="15dp"
        android:gravity="center_horizontal"
        android:text="@string/me_didi_tip_iknow_not_translate_n"
        android:textColor="#F23030"
        android:textSize="14sp" />
</RelativeLayout>
