<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">
    <com.tencent.tencentmap.mapsdk.map.MapView
        android:id="@+id/mapView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    <TextView
        android:id="@+id/tv_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:lineSpacingMultiplier="1.1"
        android:background="#fff4e6"
        android:textSize="@dimen/comm_text_normal"
        android:textColor="#f86e21"
        android:visibility="invisible"
        tools:visibility="visible"
        tools:text="文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:layout_marginBottom="10dp"
        android:layout_alignParentBottom="true">

        <LinearLayout
            android:id="@+id/ll_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:orientation="horizontal"
            android:background="#fff4e6"
            android:padding="10dp"
            android:visibility="gone"
            tools:visibility="visible">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_car_notice"
                android:layout_marginEnd="10dp"
                android:layout_marginRight="10dp" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:layout_gravity="center_vertical"
                android:textColor="#f97c0e"
                android:textSize="@dimen/me_text_size_14"
                tools:text="@string/me_didi_waitcar_tip3"/>

        </LinearLayout>
        <include layout="@layout/jdme_item_driver_info" />
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#eeeeee"/>
        <LinearLayout
            android:id="@+id/layout_colleague_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            android:background="@color/white"
            tools:visibility="visible">
            <LinearLayout
                android:id="@+id/layout_colleague_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#eeeeee"/>
        </LinearLayout>
        <FrameLayout
            android:id="@+id/layout_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:background="#ffffff"
                android:gravity="center">
                <LinearLayout
                    android:id="@+id/ll_cancel"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:gravity="center">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:src="@drawable/ic_car_cancel"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_car_cancel"
                        android:textColor="#7a7a7a"
                        android:textSize="@dimen/me_text_size_14"/>

                </LinearLayout>

<!--                <View-->
<!--                    android:layout_width="0.5dp"-->
<!--                    android:layout_height="25dp"-->
<!--                    android:background="#eeeeee"/>-->
                <LinearLayout
                    android:id="@+id/ll_complain"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:gravity="center">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:src="@drawable/ic_car_complain"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp" />
                    <TextView
                        android:id="@+id/tvComplain"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_didi_title_order_omplaints"
                        android:textColor="#7a7a7a"
                        android:textSize="@dimen/me_text_size_14"/>

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_share"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:gravity="center">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:src="@drawable/ic_car_share"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp" />
                    <TextView
                        android:id="@+id/tvShare"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_didi_title_order_share"
                        android:textColor="#7a7a7a"
                        android:textSize="@dimen/me_text_size_14"/>

                </LinearLayout>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</RelativeLayout>