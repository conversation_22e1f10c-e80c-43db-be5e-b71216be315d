<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_driver_info"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_driver_pic"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:src="@drawable/jdme_icon_driver" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/iv_driver_pic">

        <LinearLayout
            android:id="@+id/ll_name"
            android:layout_width="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_driver_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black_252525"
                android:textSize="18sp"
                tools:text="京师傅" />

            <TextView
                android:id="@+id/tv_car_no"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_toRightOf="@+id/tv_driver_name"
                android:textColor="#62656D"
                android:textSize="14sp"
                tools:text="京 GUH869" />

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="8dp"
                android:layout_toRightOf="@id/tv_car_no"
                tools:src="@drawable/img_default" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_car_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/ll_name"
            android:layout_marginTop="7dp"
            android:textColor="#62656D"
            tools:text="白色　长安欧尚X7"
            android:textSize="@dimen/me_text_size_14" />

        <LinearLayout
            android:id="@+id/ll_star_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_car_type"
            android:layout_marginTop="6dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <RatingBar
                android:id="@+id/rab_star"
                style="@style/me_DidiLittleStar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:isIndicator="true"
                android:progressDrawable="@drawable/jdme_didi_star_little_default"
                android:stepSize="0.1" />

            <TextView
                android:id="@+id/tv_fraction"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:background="@drawable/jdme_bg_didi_driverinfo_fraction_default"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/tv_order"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:textColor="@color/black_assist"
                android:textSize="@dimen/me_text_size_small"
                android:visibility="gone" />
        </LinearLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_telephone"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="15dp"
        android:src="@drawable/jdme_icon_didi_phone_default" />

</RelativeLayout>