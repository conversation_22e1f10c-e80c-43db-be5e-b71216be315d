<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="32dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        android:lineSpacingMultiplier="1.2"
        android:text="@string/me_car_pool_tips" />
    <CheckBox
        android:id="@+id/cb_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/tv_tips"
        app:layout_constraintTop_toBottomOf="@id/tv_tips"
        android:layout_marginTop="16dp"
        android:button="@drawable/jdme_selector_checkbox_car"/>
    <TextView
        android:id="@+id/tv_dont_remind"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/cb_check"
        app:layout_constraintTop_toTopOf="@id/cb_check"
        app:layout_constraintBottom_toBottomOf="@id/cb_check"
        android:layout_marginStart="6dp"
        android:textSize="@dimen/comm_text_normal"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_dont_remind"/>
    <View
        android:id="@+id/view_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cb_check"
        android:layout_marginTop="16dp"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/view_divider_button"
        app:layout_constraintHorizontal_chainStyle="spread"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:gravity="center"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        android:background="@drawable/jdme_ripple"
        android:text="@string/me_car_pool_cancel_call"/>
    <View
        android:id="@+id/view_divider_button"
        android:layout_width="@dimen/comm_divider_height"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/tv_cancel"
        app:layout_constraintBottom_toBottomOf="@id/tv_cancel"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_call"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_cancel"
        app:layout_constraintLeft_toRightOf="@id/view_divider_button"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:gravity="center"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple"
        android:text="@string/me_car_pool_call"/>
</androidx.constraintlayout.widget.ConstraintLayout>