<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_car_pop"
    android:gravity="center"
    android:orientation="horizontal">

<LinearLayout
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <TextView
        android:id="@+id/tvContent2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:text="@string/me_didi_car_wait"
        android:textColor="#999999"
        android:textSize="12sp"/>
    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#ff7e00"
        android:textStyle="bold"
        android:textSize="14sp"
        tools:text="00:23"/>
</LinearLayout>
    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="#7e7e7e"
        android:layout_margin="5dp"/>

    <TextView
        android:id="@+id/tvContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="200dp"
        android:layout_gravity="center_vertical"
        android:text="@string/me_didi_car_searching"
        android:textColor="#333333"
        android:textSize="13sp"
        />



</LinearLayout>