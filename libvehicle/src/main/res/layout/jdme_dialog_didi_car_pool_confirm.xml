<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="280dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="18dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        android:text="@string/me_car_pool_confirm_dialog_title"/>
    <TextView
        android:id="@+id/tv_title_from"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="18dp"
        android:layout_marginStart="16dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_invitation_title_from"/>
    <TextView
        android:id="@+id/tv_from"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_title_from"
        app:layout_constraintLeft_toRightOf="@id/tv_title_from"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="明基商务广场明基商务广场"/>
    <TextView
        android:id="@+id/tv_title_to"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_title_from"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="18dp"
        android:layout_marginStart="16dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_invitation_title_to"/>
    <TextView
        android:id="@+id/tv_to"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tv_title_to"
        app:layout_constraintLeft_toRightOf="@id/tv_title_to"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="中央公园地铁站"/>
    <TextView
        android:id="@+id/tv_title_colleague"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_title_to"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="18dp"
        android:layout_marginStart="16dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_car_pool_passenger"/>
    <HorizontalScrollView
        android:id="@+id/layout_scroll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_title_colleague"
        app:layout_constraintLeft_toRightOf="@id/tv_title_colleague"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:scrollbars="none">
        <TextView
            android:id="@+id/tv_colleague"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/comm_text_normal_xlarge"
            android:textColor="@color/comm_text_title"
            tools:text="1234567890abcdefghigklmnopqrstuvwxyz"/>
    </HorizontalScrollView>

    <View
        android:id="@+id/view_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintTop_toBottomOf="@id/layout_scroll"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="18dp"
        android:background="@color/comm_background"/>
    <Button
        android:id="@+id/btn_cancel"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/view_divider_button"
        android:background="@drawable/jdme_ripple_white"
        android:textColor="@color/comm_text_title"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:text="@string/me_car_pool_cancel_call"/>
    <View
        android:id="@+id/view_divider_button"
        android:layout_width="@dimen/comm_divider_height"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/btn_cancel"
        app:layout_constraintBottom_toBottomOf="@id/btn_cancel"
        app:layout_constraintLeft_toRightOf="@id/btn_cancel"
        app:layout_constraintRight_toLeftOf="@+id/btn_call"
        android:background="@color/comm_divider" />
    <Button
        android:id="@+id/btn_call"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/view_divider_button"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/btn_cancel"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple_white"
        android:text="@string/me_car_pool_call"/>
</androidx.constraintlayout.widget.ConstraintLayout>