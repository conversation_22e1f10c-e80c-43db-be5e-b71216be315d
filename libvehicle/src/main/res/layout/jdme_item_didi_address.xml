<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dip"
    android:gravity="center_vertical">

    <TextView
        android:id="@+id/tv_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="25dp"
        android:gravity="center_vertical"
        android:textColor="@color/black_assist"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_item3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dp"
        android:layout_toRightOf="@+id/tv_item"
        android:background="#33FFB416"
        android:textColor="#FFB416"
        android:padding="1dp"
        android:gravity="center_vertical"
        android:text="临时站点"
        android:textSize="12sp"
        android:visibility="invisible"
        />


    <TextView
        android:id="@+id/tv_item2"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_item"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical"
        android:textColor="#dddddd"
        android:textSize="11sp" />

</RelativeLayout>