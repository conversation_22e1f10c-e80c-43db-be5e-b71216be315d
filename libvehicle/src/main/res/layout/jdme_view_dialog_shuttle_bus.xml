<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_shuttle_bus_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="36dp"
        android:layout_marginTop="28dp"
        android:layout_marginRight="36dp"
        android:layout_marginBottom="29dp"
        android:text="您前面一共40人，预计等待时间1小时10分钟，为节省您的等待时间，建议您乘坐 XX 停车场摆渡车到其他地点打车。"
        android:textColor="#FF2E2D2D"
        android:textSize="16sp"
        android:maxLines="5"
        android:ellipsize="end"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#FFD6DBE1"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_shuttle_bus_cancel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="#FF2B3138"
            android:textSize="16sp" />

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="#FFD6DBE1" />

        <TextView
            android:id="@+id/tv_shuttle_bus_check"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="查看摆渡车"
            android:textColor="#FFF0250F"
            android:textSize="16sp" />
    </LinearLayout>


</LinearLayout>