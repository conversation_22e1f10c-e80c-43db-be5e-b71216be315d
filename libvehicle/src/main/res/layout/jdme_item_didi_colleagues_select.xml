<?xml version="1.0" encoding="utf-8"?><!-- 我的假期 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    android:orientation="horizontal">


    <LinearLayout
        android:id="@+id/ll_normal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:background="@drawable/jdme_bg_corner_didi_colleagues_del"
        android:orientation="horizontal"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:visibility="visible">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:padding="2dp"
            android:textColor="@color/comm_text_red"
            android:textSize="14sp"
            android:text="geshuyue1"/>

        <ImageView
            android:id="@+id/iv_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/jdme_ic_didi_icon_colleagues_delete" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/ll_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:background="@drawable/jdme_bg_corner_didi_colleagues_add"
        android:orientation="horizontal"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/jdme_didi_icon_colleagues_add" />

        <TextView
            android:id="@+id/tv_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:padding="2dp"
            android:text="@string/me_car_address_colleagues_add"
            android:textColor="#ff0000"
            android:textSize="14sp" />

    </LinearLayout>
</LinearLayout>