<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical"
    android:scrollbars="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_tip_approval"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/didi_warn_bg"
                android:drawableLeft="@drawable/jdme_alert_icon"
                android:drawablePadding="5dp"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:paddingLeft="8dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="@string/me_didi_tip_approval_bussiness"
                android:textColor="@color/red_warn"
                android:textSize="@dimen/me_text_size_small" />

            <LinearLayout
                android:id="@+id/ll_reserve"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/me_didi_go_time"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_small" />

                <TextView
                    android:id="@+id/tv_leave_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="6dp"
                    android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
                    android:drawablePadding="5dp"
                    android:text="@string/me_didi_now"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_small" />

            </LinearLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/black_transparent_12" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="38dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="11dp"
                    android:layout_marginBottom="11dp"
                    android:paddingLeft="10dip"
                    android:src="@drawable/jdme_didi_icon_loc_from" />

                <TextView
                    android:id="@+id/tv_from"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/me_didi_hint_from"
                    android:paddingLeft="8dp"
                    android:singleLine="true"
                    android:textSize="@dimen/me_text_size_14" />
            </LinearLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/black_transparent_12" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="38dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="11dp"
                    android:layout_marginBottom="11dp"
                    android:paddingLeft="10dip"
                    android:src="@drawable/jdme_didi_icon_loc_to" />

                <TextView
                    android:id="@+id/tv_to"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/me_didi_hint_to"
                    android:paddingLeft="8dip"
                    android:singleLine="true"
                    android:textSize="@dimen/me_text_size_14" />
            </LinearLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/black_transparent_12" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="28dp"
                    android:src="@drawable/jdme_didi_icon_phone" />

                <com.jd.oa.ui.ClearableEditTxt
                    android:id="@+id/cet_telephone"
                    android:layout_width="fill_parent"
                    android:layout_height="42dp"
                    android:layout_marginLeft="11dp"
                    android:layout_marginRight="11dp"
                    android:background="@null"
                    android:hint="@string/me_didi_hint_phone_number"
                    android:maxLength="11"
                    android:numeric="integer"
                    android:paddingLeft="42dp"
                    android:singleLine="true"
                    android:textSize="@dimen/me_text_size_14" />
            </RelativeLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/black_transparent_12" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ll_daily"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_didi_bg_business_reason_gray"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_daily"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="4dp"
                        android:text="@string/me_didi_business_daily"
                        android:textColor="@color/conference_black_color" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_travel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="7dp"
                    android:background="@drawable/jdme_didi_bg_business_reason_gray"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_travel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="4dp"
                        android:text="@string/me_didi_business_travel"
                        android:textColor="@color/conference_black_color" />
                </LinearLayout>

            </LinearLayout>

            <com.jd.oa.business.didi.widget.ClearableAutoCompleteTextView
                android:id="@+id/cet_reason"
                android:layout_width="fill_parent"
                android:layout_height="42dp"
                android:layout_marginLeft="11dp"
                android:layout_marginRight="11dp"
                android:background="@null"
                android:completionThreshold="1"
                android:hint="@string/me_didi_hint_remark1"
                android:maxLength="20"
                android:paddingLeft="15dp"
                android:singleLine="true"
                android:textColorHint="#dddddd"
                android:textSize="@dimen/me_text_size_14" />

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:background="@color/black_transparent_12" />

            <com.jd.oa.business.didi.widget.EstimateInfoView
                android:id="@+id/estimateView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include layout="@layout/jdme_didi_call_taxi_buttons" />

</LinearLayout>