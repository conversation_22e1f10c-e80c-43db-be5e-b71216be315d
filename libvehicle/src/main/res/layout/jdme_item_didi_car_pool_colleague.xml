<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="18dp"
        android:layout_marginStart="12dp"
        android:textColor="@color/comm_text_title"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:text="@string/me_car_pool_colleague"/>
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        android:layout_marginEnd="12dp"
        android:layout_marginStart="12dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="end"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="葛姝悦(geshuyue)"/>
    <View
        android:id="@+id/view_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_marginTop="18dp"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
</androidx.constraintlayout.widget.ConstraintLayout>