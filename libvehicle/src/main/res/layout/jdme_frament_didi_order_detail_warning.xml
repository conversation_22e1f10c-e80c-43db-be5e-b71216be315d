<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:scrollbars="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <!--订单价格异常提示信息-->
            <RelativeLayout
                android:id="@+id/rlWarning"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:background="@color/jdme_color_didi_red_bg"
                android:paddingLeft="18dp"
                android:paddingRight="18dp">

                <TextView
                    android:id="@+id/tvWarning"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:drawableStart="@drawable/jdme_icon_didi_notice"
                    android:drawableLeft="@drawable/jdme_icon_didi_notice"
                    android:drawablePadding="11dp"
                    android:gravity="left"
                    android:textColor="@color/jdme_color_didi_red_text" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:gravity="center_vertical"
                android:visibility="gone">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="?attr/me_theme_major_color" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_pay_succsess"
                    android:textColor="?attr/me_theme_major_color"
                    android:textSize="@dimen/me_text_size_small" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="?attr/me_theme_major_color" />
            </LinearLayout>
            <!--订单核对-->
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center_vertical">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="48dp"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_didi_order_money_check"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_small" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="48dp"
                    android:layout_weight="1"
                    android:background="#dddddd" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="18dp"
                android:paddingTop="18dp"
                android:paddingRight="18dp">
                <!--预估金额-->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="13dp"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_didi_delay_pay_guess_price"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="20dp"
                        android:text="@string/me_didi_order_guess_price"
                        android:textSize="16sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvEstimatePrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="10dp"
                            android:singleLine="true"
                            android:textColor="@color/black_252525"
                            android:textSize="32sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="20dp"
                            android:singleLine="true"
                            android:text="@string/me_didi_yuan"
                            android:textColor="@color/black_252525"
                            android:textSize="@dimen/me_text_size_larger" />

                    </LinearLayout>
                </LinearLayout>
                <!--实际金额-->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="13dp"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_didi_delay_pay_price"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="20dp"
                        android:text="@string/me_didi_order_true_price"
                        android:textSize="16sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="10dp"
                            android:singleLine="true"
                            android:textColor="?attr/me_theme_major_color"
                            android:textSize="32sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="20dp"
                            android:singleLine="true"
                            android:text="@string/me_didi_yuan"
                            android:textColor="?attr/me_theme_major_color"
                            android:textSize="@dimen/me_text_size_larger" />

                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:paddingLeft="18dp"
                android:paddingRight="18dp"
                android:text="@string/me_didi_order_pay_notice"
                android:textColor="#666666"
                android:textSize="14sp" />

            <FrameLayout
                android:id="@+id/fl_colleagues"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="140dp"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="18dp">

            <TextView
                android:id="@+id/tv_reason_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/me_didi_order_reason_title"
                android:textColor="@color/jdme_color_first"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/etReason"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/tv_reason_title"
                android:layout_marginTop="5dp"
                android:background="@drawable/jdme_bg_didi_delay_pay_edit"
                android:gravity="top|left"
                android:hint="@string/me_didi_order_pay_notice_hint"
                android:padding="12dp"
                android:textColorHint="#cccccc"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvReasonCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="10dp"
                android:text="0/100" />
        </RelativeLayout>


        <!--确认支付、延缓支付按钮-->


        <LinearLayout
            android:id="@+id/ll_pay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_confirm_pay"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_selector_button_fee_default"
                android:text="@string/me_didi_confirm_stroke"
                android:textColor="@drawable/jdme_selector_didi_btn_fee"
                android:textSize="@dimen/me_text_size_middle" />

            <Button
                android:id="@+id/btn_confirm_delay_pay"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:layout_weight="1"
                android:background="?attr/me_btn_selector"
                android:text="@string/me_didi_confirm_fee"
                android:textSize="@dimen/me_text_size_middle" />
        </LinearLayout>

        <Button
            android:id="@+id/btn_submit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:background="?attr/me_btn_selector"
            android:paddingLeft="50dp"
            android:paddingRight="50dp"
            android:text="@string/me_submit_not_translate"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>


</ScrollView>