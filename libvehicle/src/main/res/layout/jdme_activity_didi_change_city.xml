<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/id_edittext_container"
        android:layout_width="fill_parent"
        android:layout_height="50dp"
        android:background="@color/didi_page_bg"
        android:orientation="horizontal"
        android:paddingLeft="16dp">

        <EditText
            android:id="@+id/et_city"
            style="@android:style/TextAppearance.Widget.EditText"
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_weight="1"
            android:background="@color/didi_page_bg"
            android:cursorVisible="true"
            android:drawablePadding="8dp"
            android:gravity="left|center_vertical"
            android:hint="@string/me_didi_hint_msg_city"
            android:paddingLeft="5dp"
            android:singleLine="true"
            android:textColor="@color/conference_black_color2"
            android:textColorHint="@color/conference_black_color"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="@dimen/me_text_size_middle" />

        <View
            android:layout_width="1dp"
            android:layout_height="30dp"
            android:layout_gravity="center_vertical"
            android:background="#E0E0E0" />

        <TextView
            android:id="@+id/tv_input"
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_marginLeft="11dp"
            android:layout_weight="1"
            android:background="@color/didi_page_bg"
            android:drawablePadding="8dp"
            android:gravity="left|center_vertical"
            android:paddingLeft="5dp"
            android:singleLine="true"
            android:text="@string/me_didi_hint_msg_input"
            android:textColor="@color/conference_black_color"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:gravity="center_vertical"
            android:text="@string/me_cancel_not_translate"
            android:textColor="@color/conference_blue_color"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="43dp"
        android:layout_gravity="center_vertical"
        android:paddingLeft="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/me_didi_title_current_city"
            android:textColor="#262626"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_city_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textColor="#262626"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/feedback_line_bg" />

    <FrameLayout
        android:id="@+id/fl_sidebarList"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_marginRight="4dp"
        android:layout_weight="1"
        android:visibility="visible">
        <!-- ListView 展示区 -->
        <ListView
            android:id="@+id/lv_conference_room_search_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:cacheColorHint="@color/transparent"
            android:divider="@color/black_divider"
            android:dividerHeight="@dimen/me_divide_height_min"
            android:listSelector="@color/transparent"
            android:scrollbars="none" />

        <TextView
            android:id="@+id/dialog"
            android:layout_width="80.0dip"
            android:layout_height="80.0dip"
            android:layout_gravity="center"
            android:background="@drawable/jdme_slidebar_dialog"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="30.0dip"
            android:visibility="gone" />

        <com.jd.oa.ui.sortlistview.SideBar
            android:id="@+id/sidebar"
            android:layout_width="26dip"
            android:layout_height="fill_parent"
            android:layout_gravity="right"
            android:layout_marginBottom="20dp"
            android:layout_marginTop="20dp" />
    </FrameLayout>

</LinearLayout>
