<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        tools:src="@drawable/img_default" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_weight="1"
        android:textColor="#292929"
        android:textSize="16sp"
        tools:text="滴滴出行" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="bottom">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_didi_order_guess"
            android:textColor="#6F6F6F"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:textColor="#292929"
            android:textSize="24sp"
            android:textStyle="bold"
            tools:text="888" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_didi_yuan"
            android:textColor="#6F6F6F"
            android:textSize="12sp" />

    </LinearLayout>
</LinearLayout>