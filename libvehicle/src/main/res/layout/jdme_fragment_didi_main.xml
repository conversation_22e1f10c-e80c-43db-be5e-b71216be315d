<?xml version="1.0" encoding="utf-8"?><!-- 我的假期 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg">

    <TextView
        android:id="@+id/tv_didi_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="10dp"
        android:drawablePadding="5dp"
        android:drawableRight="@drawable/jdme_alert_icon1"
        android:text="@string/me_car_use_type"
        android:textColor="@color/black_252525"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/tv_didi_main_condition"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:layout_marginTop="5dp"
        android:drawableRight="@drawable/me_red_right_arrow_ic"
        android:gravity="center_vertical"
        android:paddingBottom="5dp"
        android:paddingTop="5dp"
        android:text="@string/me_car_manage_address"
        android:textColor="@color/red_warn"
        android:textSize="12sp" />


    <LinearLayout
        android:id="@+id/ll_didi_main_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_didi_title"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/rl_didi_overtime"
            android:layout_width="0dp"
            android:layout_height="65dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="7dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_selector_didi_overtime">

            <ImageView
                android:id="@+id/iv_didi_overtime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17dp"
                android:background="@drawable/jdme_didi_overtime_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/iv_didi_overtime"
                android:drawablePadding="5dp"
                android:drawableRight="@drawable/jdme_didi_arrow"
                android:text="@string/me_car_overtime_work"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_didi_business"
            android:layout_width="0dp"
            android:layout_height="65dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_selector_didi_business">

            <ImageView
                android:id="@+id/iv_didi_business"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17dp"
                android:background="@drawable/jdme_didi_business_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/iv_didi_business"
                android:drawablePadding="5dp"
                android:drawableRight="@drawable/jdme_didi_arrow"
                android:text="@string/me_car_by_travel"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </RelativeLayout>
    </LinearLayout>


    <RelativeLayout
        android:id="@+id/ll_didi_main_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ll_didi_main_buttons"
        android:layout_marginRight="20dp"
        android:layout_marginTop="15dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:padding="10dp"
            android:text="@string/me_order_list_late"
            android:textColor="@color/black_252525"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_didi_main_all_order_list_entrance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
            android:padding="10dp"
            android:text="@string/me_order_all"
            android:textColor="@color/grey_text_color"
            android:textSize="12sp" />
    </RelativeLayout>

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/ll_didi_main_subtitle"
        android:layout_margin="8dp">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout_didi_main"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_didi_order_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:cacheColorHint="@color/transparent"
                android:divider="@color/black_divider"
                android:dividerHeight="5dp"
                android:listSelector="@color/transparent" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </com.jd.oa.ui.FrameView>
</RelativeLayout>