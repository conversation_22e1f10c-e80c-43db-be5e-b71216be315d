<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:paddingLeft="20dp"
        android:text="@string/me_car_address_add_tip"
        android:textColor="@color/black_252525"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:background="@color/comm_white"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_city"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:drawableRight="@drawable/jdme_didi_icon_arrow"
            android:drawablePadding="2dp"
            android:text="@string/me_positioning"
            android:textColor="@color/conference_black_color2"
            android:textSize="@dimen/me_text_size_middle" />

        <View
            android:layout_width="1dp"
            android:layout_height="30dp"
            android:layout_gravity="center_vertical"
            android:background="#E0E0E0" />

        <EditText
            android:id="@+id/id_workplace_search_et"
            style="@android:style/TextAppearance.Widget.EditText"
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_marginLeft="11dp"
            android:layout_weight="1"
            android:cursorVisible="true"
            android:drawablePadding="8dp"
            android:gravity="left|center_vertical"
            android:hint="@string/me_car_address_edit_tip"
            android:paddingLeft="5dp"
            android:singleLine="true"
            android:textColor="@color/conference_black_color2"
            android:textColorHint="@color/conference_black_color"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>


    <ListView
        android:id="@+id/me_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:cacheColorHint="@color/transparent"
        android:divider="@color/black_divider"
        android:dividerHeight="@dimen/me_divide_height_min"
        android:listSelector="@drawable/jdme_selector_my_actionbar" />


    <com.jd.oa.business.didi.widget.ClearableAutoCompleteTextView
        android:id="@+id/cet_reason"
        android:layout_width="fill_parent"
        android:layout_height="42dp"
        android:paddingRight="11dp"
        android:layout_marginTop="20dp"
        android:background="@color/white"
        android:completionThreshold="1"
        android:hint="@string/me_didi_hint_remark1"
        android:maxLength="20"
        android:paddingLeft="16dp"
        android:singleLine="true"
        android:textColorHint="#dddddd"
        android:textSize="@dimen/me_text_size_14"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_action"
        style="@style/my_button_default"
        android:layout_gravity="center"
        android:layout_marginLeft="44dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="44dp"
        android:layout_marginBottom="20dp"
        android:text="@string/me_car_address_button_add" />
</LinearLayout>