<?xml version="1.0" encoding="utf-8"?><!-- 我的假期 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg">

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout_didi_all_order"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="10dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_didi_all_order_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:cacheColorHint="@color/transparent"
                android:divider="@color/black_divider"
                android:dividerHeight="5dp"
                android:listSelector="@color/transparent" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </com.jd.oa.ui.FrameView>
</RelativeLayout>