<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="240dp"
    android:background="@color/comm_white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        android:text="@string/me_car_pool_passenger"/>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        android:padding="20dp"
        android:src="@drawable/jdme_ic_didi_car_pool_close"/>
    <View
        android:id="@+id/view_divider"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:background="#eef1f4"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        android:overScrollMode="never"/>
</androidx.constraintlayout.widget.ConstraintLayout>