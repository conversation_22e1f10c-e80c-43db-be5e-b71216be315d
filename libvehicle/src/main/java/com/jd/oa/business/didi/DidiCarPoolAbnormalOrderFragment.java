package com.jd.oa.business.didi;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;

/**
 * Created by peidongbiao on 2019/4/7
 */
@Navigation(hidden = false,  displayHome = true)
public class DidiCarPoolAbnormalOrderFragment extends BaseFragment {

    private TextView mTvStatus;

    private DidiOrderDetailBean mDetailBean;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if(getArguments() == null || !getArguments().containsKey("orderDetailBean")) {
            getActivity().finish();
            return super.onCreateView(inflater, container, savedInstanceState);
        }
        mDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        View view = inflater.inflate(R.layout.jdme_fragment_didi_car_pool_abnormal_order_status, container, false);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_order_detail);
        mTvStatus = view.findViewById(R.id.tv_status);
        showStatus(mDetailBean);
        return view;
    }


    private void showStatus(DidiOrderDetailBean detailBean) {
        if(detailBean == null) return;
        String status = DidiUtils.getCarPoolOrderStatus(detailBean.carPoolUser);
        if(status == null) return;
        if(DidiUtils.CAR_POOL_STATE_EXPIRED.equals(status)) {
            mTvStatus.setText(R.string.me_car_pool_invitation_expired);
        }else if(DidiUtils.CAR_POOL_STATE_REJECTED.equals(status)) {
            mTvStatus.setText(R.string.me_car_pool_invitation_rejected);
        }else if(DidiUtils.CAR_POOL_STATE_CANCELED.equals(status)) {
            mTvStatus.setText(R.string.me_car_pool_invitation_canceled);
        }
    }
}