package com.jd.oa.business.didi;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.StringUtils;

import java.util.List;


/**
 * Created by qudongshi on 2016/1/15.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderDetailOrderCancelFragment extends DidiBaseFragment implements OperatingListener {

    private View mRootView;

    private DidiOrderDetailBean mDidiOrderDetailBean;

    private RelativeLayout mRlDriverInfo;
    private CircleImageView mIvDriverPic;
    private TextView mTvDriverName;
    private TextView mTvCarType;
    private TextView mTvCarNo;
    private LinearLayout mLlOrderDetail;

    private LinearLayout mLlStarContent;
    private RatingBar mRbStar;
    private TextView mTvFraction;
    private TextView mTvOrder;

    private ImageButton mIbtnOmplaints; // 投诉
    private TextView mTvTips;
    private ImageView mIvTelephone;
    private ImageView mIvService;
    private Button btn_view_detail;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_cancel, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_order_detail);

        mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        initView();
        return mRootView;
    }

    private void initView() {

        mRlDriverInfo = mRootView.findViewById(R.id.ll_driver_info);
        mIvDriverPic = mRootView.findViewById(R.id.iv_driver_pic);
        mTvDriverName = mRootView.findViewById(R.id.tv_driver_name);
        mTvCarType = mRootView.findViewById(R.id.tv_car_type);
        mTvCarNo = mRootView.findViewById(R.id.tv_car_no);
        mIvService = mRootView.findViewById(R.id.iv_icon);
        mLlOrderDetail = mRootView.findViewById(R.id.ll_order_detail);

        mLlStarContent = mRootView.findViewById(R.id.ll_star_content);
        mRbStar = mRootView.findViewById(R.id.rab_star);
        mTvFraction = mRootView.findViewById(R.id.tv_fraction);
        mTvOrder = mRootView.findViewById(R.id.tv_order);

        mIbtnOmplaints = mRootView.findViewById(R.id.ibtn_omplaints); // 投诉
        mTvTips = mRootView.findViewById(R.id.tv_notice);
        mIvTelephone = mRootView.findViewById(R.id.iv_telephone);
        btn_view_detail = mRootView.findViewById(R.id.btn_view_detail);
        btn_view_detail.setVisibility(TextUtils.isEmpty(mDidiOrderDetailBean.h5url) ? View.GONE : View.VISIBLE);

        btn_view_detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (mDidiOrderDetailBean != null && !TextUtils.isEmpty(mDidiOrderDetailBean.h5url)) {
                    Router.build(mDidiOrderDetailBean.h5url).go(getActivity());
                }
            }
        });

        mIbtnOmplaints.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 投诉
                if ("1".equals(mDidiOrderDetailBean.order.isComplainted))
                    return;
                Bundle bundle1 = new Bundle();
                bundle1.putString("orderId", mDidiOrderDetailBean.order.orderId);
                bundle1.putInt("flag", 1);
                bundle1.putString("serviceType", mDidiOrderDetailBean.order.serviceType);
                FragmentUtils.replaceWithCommit(getActivity(), DidiOrderDetailOmplaintsFragment.class, R.id.me_fragment_content, true, bundle1, false);
            }
        });

        mIvTelephone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mDidiOrderDetailBean.order.driverPhone);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.driverPhone);
                }
            }
        });

        if (TextUtils.isEmpty(mDidiOrderDetailBean.order.driverName) && TextUtils.isEmpty(mDidiOrderDetailBean.order.driverPhone)) {
            mRlDriverInfo.setVisibility(View.GONE);
            mLlOrderDetail.setVisibility(View.GONE);
            mIbtnOmplaints.setVisibility(View.GONE);
        } else {
            mTvDriverName.setText(mDidiOrderDetailBean.order.driverName);
            ImageLoader.load(getActivity(), mIvService, mDidiOrderDetailBean.icon, false, R.drawable.jdme_icon_use_car_service_default, R.drawable.jdme_icon_use_car_service_default);
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.driverLevel)) {
                try {
                    DidiUtils.showDriverLevel(Float.parseFloat(mDidiOrderDetailBean.order.driverLevel), mRbStar);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                mTvFraction.setText(mDidiOrderDetailBean.order.driverLevel + mTvFraction.getContext().getString(R.string.me_didi_minute));
                mTvOrder.setText(mDidiOrderDetailBean.order.driverOrderCount + mTvFraction.getContext().getString(R.string.me_didi_unit_dan));
            } else {
                mLlStarContent.setVisibility(View.GONE);
            }
            mTvCarNo.setText(mDidiOrderDetailBean.order.driverCard);
            mTvCarType.setText(StringUtils.getSubStringFromStart(mDidiOrderDetailBean.order.driverCarType, 12));
            DidiUtils.getDriverImg(mDidiOrderDetailBean.order.driverAvatar, mIvDriverPic, getActivity());
        }
        // 初始化
        if ("1".equals(mDidiOrderDetailBean.order.isComplainted)) {
            mIbtnOmplaints.setBackgroundResource(R.drawable.jdme_didi_icon_complaints_disable);
        }
        if (!"".equals(mDidiOrderDetailBean.order.showTip)) {
            // 修改提示语
            mTvTips.setText(mDidiOrderDetailBean.order.showTip);
        }

        if (!DidiUtils.isCallCarUser(mDidiOrderDetailBean)) {
            //非拼车发起人
            mIbtnOmplaints.setVisibility(View.GONE);
            mIvTelephone.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.h5url)) {
            mIbtnOmplaints.setVisibility(View.GONE);
        }
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OPERATE_GO_DIDI == optionFlag) {
            if (args != null) {
                String isAgain = args.getString("isAgain");
                if ("0".equals(isAgain)) {
                    mIbtnOmplaints.setBackgroundResource(R.drawable.jdme_didi_icon_complaints_disable);
                    mDidiOrderDetailBean.order.isComplainted = "1";
                }
            }
            return true;
        }
        return false;
    }
}
