package com.jd.oa.business.didi.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.business.caraddress.bean.CarAddress;

import java.util.ArrayList;

public class UserCommonAddress implements Parcelable {
    private CarAddress defaultAddress;
    private ArrayList<CarAddress> addressList;
    private String addressSize;


    public CarAddress getDefaultAddress() {
        return defaultAddress;
    }

    public void setDefaultAddress(CarAddress defaultAddress) {
        this.defaultAddress = defaultAddress;
    }

    public ArrayList<CarAddress> getAddressList() {
        return addressList;
    }

    public void setAddressList(ArrayList<CarAddress> addressList) {
        this.addressList = addressList;
    }

    public String getAddressSize() {
        return addressSize;
    }

    public void setAddressSize(String addressSize) {
        this.addressSize = addressSize;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(this.defaultAddress, flags);
        dest.writeTypedList(this.addressList);
        dest.writeString(this.addressSize);
    }

    public UserCommonAddress() {
    }

    protected UserCommonAddress(Parcel in) {
        this.defaultAddress = in.readParcelable(CarAddress.class.getClassLoader());
        this.addressList = in.createTypedArrayList(CarAddress.CREATOR);
        this.addressSize = in.readString();
    }

    public static final Creator<UserCommonAddress> CREATOR = new Creator<UserCommonAddress>() {
        @Override
        public UserCommonAddress createFromParcel(Parcel source) {
            return new UserCommonAddress(source);
        }

        @Override
        public UserCommonAddress[] newArray(int size) {
            return new UserCommonAddress[size];
        }
    };
}
