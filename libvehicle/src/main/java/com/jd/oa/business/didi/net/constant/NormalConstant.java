package com.jd.oa.business.didi.net.constant;

public class NormalConstant extends Constant {

    public NormalConstant(){
        init();
    }

    private void init() {
        //提交费用异议
        API_ME_VEHICLE_SUBMITFEEOBJECTION = "jmeMobile/vehicle/submitFeeObjection";
        //用车急救
        API_CAR_CALL_HELP = "jmeMobile/vehicle/callHelp";
        // 协议状态
        API_GETAGREEMENTSTATUS = "jmeMobile/vehicle/getAgreementStatus";
        // 用车协议
        API_PLUSAGREEMENTSTATUS = "jmeMobile/vehicle/getVehiclePlusAgreementStatus";
        // 签署协议
        API_SIGNAGREEMENT = "jmeMobile/vehicle/signAgreement";

        API_CHECKCHOSECARTYPE = "jmeMobile/vehicle/checkChoseCarType";

        API_GETJOBADDRESSBYLOCAL = "jmeMobile/vehicle/getJobAddressByLocal";

        API_GETADDRESSBYINPUT = "jmeMobile/vehicle/getAddressByInput";
        // 获取预估计
        API_GETESTIMATEPRICE = "jmeMobile/vehicle/getEstimatePrice";

        API_CHECKCARPOOLUSER = "jmeMobile/vehicle/checkCarPoolUser";

        API_CALLCARORDER = "jmeMobile/vehicle/callCarOrder";

        API_PRECALLCARORDERFORPOOL = "jmeMobile/vehicle/preCallCarOrderForPool";

        API_AFTERCALLCARORDERFORPOOL = "jmeMobile/vehicle/afterCallCarOrderForPool";

        API_GETCARPOOLORDERSTATUS = "jmeMobile/vehicle/getCarPoolOrderStatus";

        API_GETORDERLIST = "jmeMobile/vehicle/getOrderList";
        //  重叫
        API_RECALLCARORDER = "jmeMobile/vehicle/reCallCarOrder";
        // 获取订单详情
        API_GETORDERDETAIL = "jmeMobile/vehicle/getOrderDetail";
        // 取消订单
        API_CANCELORDER = "jmeMobile/vehicle/cancelOrder";

        API_GETCARPOOLUSERSTATUS = "jmeMobile/vehicle/getCarPoolUserStatus";

        API_CONFIRMFORORDER = "jmeMobile/vehicle/confirmForOrder";
        // 检查城市是否可用
        API_CHECKCITY = "jmeMobile/vehicle/checkCity";
        // 确认行程
        API_AGREEPAY = "jmeMobile/vehicle/agreePay";
        // 带有异常原因的确认行程
        API_AGREECONFIRMPAY = "jmeMobile/vehicle/agreeConfirmPay";
        //取消原因列表
        API_GETCANCELORDERREASONLIST = "jmeMobile/vehicle/getCancelOrderReasonList";
        //保存取消原因
        API_SAVECANCELORDERREASON = "jmeMobile/vehicle/saveCancelOrderReason";
        // 投诉选项列表
        API_GETCOMPLAINTREASONLIST = "jmeMobile/vehicle/getComplaintReasonList";
        // 提交投诉
        API_SUBMITCOMPLAINT = "jmeMobile/vehicle/submitComplaint";
        // 评价标签
        API_GETCOMMENTLABEL = "jmeMobile/vehicle/getCommentLabel";
        // 提交评价
        API_SUBMITCOMMENT = "jmeMobile/vehicle/submitComment";
        // 延缓支付
        ACTION_COMMIT_FEE = "jmeMobile/vehicle/dissentPay";

        API_GETADDRESSBYCURLOCATION = "jmeMobile/vehicle/getAddressByCurLocation";

        /**
         * 常用地址
         */
        API_GET_USER_ADDRESS_LIST = "jmeMobile/vehicle/getUserAddressList";
        API_ADD_USER_ADDRESS = "jmeMobile/vehicle/addUserAddress";
//        API_MODIFY_USER_ADDRESS = "jmeMobile/vehicle/modifyUserAddress";
        API_CHECK_CITY = "jmeMobile/vehicle/checkCity";
        API_CHECK_USER_COMMOM_ADDRESS = "jmeMobile/vehicle/checkUserCommonAddress";
        API_CHECK_USER_COMMOM_ADDRESS = "jmeMobile/vehicle/checkUserCommonAddress";
        API_CHECK_USER_COMMOM_ADDRESS = "jmeMobile/vehicle/checkUserCommonAddress";
        API_CHECK_USER_COMMOM_ADDRESS = "jmeMobile/vehicle/checkUserCommonAddress";
    }

}
