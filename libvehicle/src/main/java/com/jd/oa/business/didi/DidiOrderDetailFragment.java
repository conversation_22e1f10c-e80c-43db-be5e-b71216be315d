package com.jd.oa.business.didi;

import static com.jd.oa.business.didi.DidiOrderDetailOmplaintsFeeFragment.FEE_COMPLAINS_SUCCESS;
import static com.jd.oa.business.didi.DidiOrderWarningFragment.APPLY_AGAIN_RESULT_CODE;
import static com.jd.oa.business.didi.DidiOrderWarningFragment.FROM_FLAG_APPLY_AGAIN;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.dialog.ColleagueListDialog;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.widget.OmplaintsPopwindow;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

/**
 * Created by qudongshi on 2016/1/15.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderDetailFragment extends DidiBaseFragment implements
        FragmentOperatingListener, OperatingListener {

    LinearLayout mLlPriceContiner;
    String mPrice; // 费用
    String mOrderTimeRange; // 时间区间
    private View mRootView;

    private TextView mTvPrice;  // 价格
    private TextView mTvTimeRange;
    private TextView mTvApproval;
    // 司机信息
    private RelativeLayout mRlDriverInfo;
    private CircleImageView mIvDriverPic;
    private TextView mTvDriverName;
    private TextView mTvCarType;
    private TextView mTvCarNo;
    private ImageView mIvService;
    private DidiOrderDetailBean mDidiOrderDetailBean;

    private LinearLayout mLlStarContent;
    private RatingBar mRbStar;
    private TextView mTvFraction;
    private TextView mTvOrder;

    private LinearLayout mLlBtnGroup;
    private LinearLayout mLlConfirmGroup;
    private ImageButton mIbtnAppraisal;  // 评价
    private ImageButton mIbtnOmplaints; // 投诉
    private ImageButton mIbHelp;    //急救

    private boolean mOmplaintsSuccess = false;
    private boolean mAppraisalSuccess = false;

    private Button mBtnConfirm;

    private Button mBtnConfirmFee; // 费用延误
    private RelativeLayout rlWarning; // 异常提示
    private TextView tvWarning; // 异常提示text
    private TextView tvEstimatePrice; // 预估金额
    private Button btnShowColleague;
    private ImageView mIvTelephone;
    private String mOrderId;

    private boolean showDialogFlag = true;
    private BroadcastReceiver complainSuccessReceiver;
    private BroadcastReceiver orderComfirmSuccessReceiver;
    private boolean isCallCarUser;
    private TextView mTvReimbursement;

    private boolean isToReimbursement = false;

    private LinearLayout mLlConfirm;
    private Button mTvConfirm;
    private Button mTvApplyAgain;
    private Button btn_view_detail;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (null == mRootView) {
            mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail, container, false);
            mOrderId = getArguments().getString("orderId");
            init();
            initData();
        }
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_order_detail);
        initComplainSuccesReceiver();
        initOrderConfirmSuccessRe();
        return mRootView;
    }

    private void init() {
        mLlPriceContiner = mRootView.findViewById(R.id.ll_price_container);
        mTvPrice = mRootView.findViewById(R.id.tv_price);  // 价格
        mTvTimeRange = mRootView.findViewById(R.id.tv_order_time_range);
        mTvApproval = mRootView.findViewById(R.id.tv_tip_approval);
        // 司机信息
        mRlDriverInfo = mRootView.findViewById(R.id.ll_driver_info);
        mIvDriverPic = mRootView.findViewById(R.id.iv_driver_pic);
        mTvDriverName = mRootView.findViewById(R.id.tv_driver_name);
        mTvCarType = mRootView.findViewById(R.id.tv_car_type);
        mTvCarNo = mRootView.findViewById(R.id.tv_car_no);
        mIvService = mRootView.findViewById(R.id.iv_icon);

        mLlStarContent = mRootView.findViewById(R.id.ll_star_content);
        mRbStar = mRootView.findViewById(R.id.rab_star);
        mTvFraction = mRootView.findViewById(R.id.tv_fraction);
        mTvOrder = mRootView.findViewById(R.id.tv_order);

        mLlBtnGroup = mRootView.findViewById(R.id.ll_btn_group);
        mLlConfirmGroup = mRootView.findViewById(R.id.ll_confirm_group);
        mIbtnAppraisal = mRootView.findViewById(R.id.ibtn_appraisal);  // 评价
        mIbtnOmplaints = mRootView.findViewById(R.id.ibtn_omplaints); // 投诉
        mIbHelp = mRootView.findViewById(R.id.ib_help);    //急救

        mBtnConfirm = mRootView.findViewById(R.id.btn_confirm_stroke);

        mBtnConfirmFee = mRootView.findViewById(R.id.btn_confirm_fee); // 费用延误
        rlWarning = mRootView.findViewById(R.id.rlWarning); // 异常提示
        tvWarning = mRootView.findViewById(R.id.tvWarning); // 异常提示text
        tvEstimatePrice = mRootView.findViewById(R.id.tvEstimatePrice); // 预估金额
        btnShowColleague = mRootView.findViewById(R.id.btn_show_colleague);
        mIvTelephone = mRootView.findViewById(R.id.iv_telephone);
        mTvReimbursement = mRootView.findViewById(R.id.tv_reimbursement);

        mLlConfirm = mRootView.findViewById(R.id.ll_confirm);
        mTvConfirm = mRootView.findViewById(R.id.tv_reimbursement_confirm);
        mTvApplyAgain = mRootView.findViewById(R.id.tv_apply_again);
        btn_view_detail = mRootView.findViewById(R.id.btn_view_detail);

        btn_view_detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (mDidiOrderDetailBean != null && !TextUtils.isEmpty(mDidiOrderDetailBean.h5url)) {
                    Router.build(mDidiOrderDetailBean.h5url).go(getActivity());
                }
            }
        });

        mIvTelephone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_phoneDriver_click, JDMAConstants.mobile_employeeTravel_traveDetail_phoneDriver_click);
                if (mDidiOrderDetailBean != null) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && null != getActivity() && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                        PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.driverPhone);
                            }

                            @Override
                            public void denied(List<String> deniedList) {

                            }
                        }, Manifest.permission.CALL_PHONE);
                    } else {
                        if (null != getActivity()) {
                            DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.driverPhone);
                        }
                    }
                }
            }
        });

        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(requireActivity(), PageEventUtil.EVENT_USER_IMMEDIATE_PAY);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_pay_click, JDMAConstants.mobile_employeeTravel_traveDetail_pay_click);

                if (mDidiOrderDetailBean != null && mDidiOrderDetailBean.order != null
                        && "1".equals(mDidiOrderDetailBean.order.getIsWarning()) && null != getActivity()) {
                    //有异常的情况下 需要跳转到二次确认页
                    Intent intent = new Intent(getActivity(), FunctionActivity.class);
                    intent.putExtra("function", DidiOrderWarningFragment.class.getName());
                    intent.putExtra("orderDetailBean", mDidiOrderDetailBean);
                    startActivity(intent);
                } else {
                    orderConfirm();
                }
            }
        });

        mIbtnAppraisal.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_pay_evaluate_click, JDMAConstants.mobile_employeeTravel_traveDetail_pay_evaluate_click);
                if ("1".equals(mDidiOrderDetailBean.order.isCommented) || TextUtils.isEmpty(mDidiOrderDetailBean.order.isCommented))
                    return;
                Bundle bundle = new Bundle();
                bundle.putSerializable("orderDetailBean", mDidiOrderDetailBean);
                if (null != getActivity()) {
                    FragmentUtils.replaceWithCommit(getActivity(), DidiOrderDetailAppraisalFragment.class, R.id.me_fragment_content, true, bundle, false);
                }
            }
        });

        mIbtnOmplaints.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_complaint_click, JDMAConstants.mobile_employeeTravel_traveDetail_complaint_click);
                if ("1".equals(mDidiOrderDetailBean.order.isComplainted) && null != getActivity()) {
                    OmplaintsPopwindow mPopupWindow = new OmplaintsPopwindow(getActivity(), mDidiOrderDetailBean.order.servicePhone, new OmplaintsPopwindow.ITelCallback() {
                        @Override
                        public void onTelCallback() {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                                PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.servicePhone.replaceAll("-", ""));
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {

                                    }
                                }, Manifest.permission.CALL_PHONE);
                            } else {
                                DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.servicePhone.replaceAll("-", ""));
                            }
                        }
                    });
                    mPopupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
                    mPopupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                        @Override
                        public void onDismiss() {
                            backgroundAlpha(1f);
                        }
                    });

                    mPopupWindow.showAtLocation(mRootView, Gravity.BOTTOM, 0, 0);
                    backgroundAlpha(0.5f);
                } else {
                    Bundle bundle1 = new Bundle();
                    bundle1.putString("orderId", mDidiOrderDetailBean.order.orderId);
                    bundle1.putString("serviceType", mDidiOrderDetailBean.order.serviceType);
                    bundle1.putInt("flag", 3);
                    if (null != getActivity()) {
                        FragmentUtils.replaceWithCommit(getActivity(), DidiOrderDetailOmplaintsFragment.class, R.id.me_fragment_content, true, bundle1, false);
                    }
                }
            }
        });

        mBtnConfirmFee.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_payDelay_click, JDMAConstants.mobile_employeeTravel_traveDetail_payDelay_click);
                if ("1".equals(mDidiOrderDetailBean.order.isFeeComplainted) || null == getActivity())
                    return;
//                Bundle bundle2 = new Bundle();
//                bundle2.putString("orderId", mDidiOrderDetailBean.order.orderId);
//                bundle2.putString("serviceType", mDidiOrderDetailBean.order.serviceType);
//                FragmentUtils.replaceWithCommit(getActivity(), DidiOrderDetailOmplaintsFeeFragment.class, R.id.me_fragment_content, true, bundle2, false);
                // 更换为打电话逻辑
                DidiUtils.callService(getActivity(),mDidiOrderDetailBean);
            }

        });

        mIbHelp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_help_click, JDMAConstants.mobile_employeeTravel_traveDetail_help_click);
                if (null == getActivity()) {
                    return;
                }
                PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_travel_help_confirmation, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_DENIED
                                || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_DENIED
                                || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_DENIED) {
                            PermissionHelper.requestPermissions(getActivity(), getResources().getString(R.string.me_request_permission_title_normal), getResources().getString(R.string.me_request_permission_location_travel),
                                    new RequestPermissionCallback() {
                                        @Override
                                        public void allGranted() {
                                            DidiUtils.urgentHelp(getActivity(), mDidiOrderDetailBean.order.orderId);
                                        }

                                        @Override
                                        public void denied(List<String> deniedList) {
                                            DidiUtils.urgentHelp(getActivity(), mDidiOrderDetailBean.order.orderId);
                                        }
                                    }, Manifest.permission.ACCESS_COARSE_LOCATION,
                                    Manifest.permission.ACCESS_FINE_LOCATION,
                                    Manifest.permission.WRITE_EXTERNAL_STORAGE);
                        } else {
                            DidiUtils.urgentHelp(getActivity(), mDidiOrderDetailBean.order.orderId);
                        }
                    }
                });
            }
        });
        mTvReimbursement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.paymentDeepLink) && null != getActivity()){
                    Router.build(mDidiOrderDetailBean.order.paymentDeepLink).go(getActivity());
                    JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_repayment,JDMAConstants.mobile_employeeTravel_traveDetail_repayment);
                    isToReimbursement = true;
                }
            }
        });
        mTvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() == null) {
                    return;
                }
                DidiUtils.orderUserValidation(getActivity(), mDidiOrderDetailBean.order.orderId, new DidiUtils.ICommonCallback() {
                    @Override
                    public void onSuccess() {
                        DidiUtils.getOrderDetail(getActivity(), mDidiOrderDetailBean.order.orderId, "", new DidiUtils.IDetailCallback() {
                            @Override
                            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                                mDidiOrderDetailBean = didiOrderDetailBean;
                                initView(getActivity());
                            }
                        });
                    }

                    @Override
                    public void onFailure() {
                    }
                });
            }
        });
        mTvApplyAgain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Intent intent = new Intent(getActivity(), FunctionActivity.class);
//                intent.putExtra("function", DidiOrderDetailApplySubmitFragment.class.getName());
//                intent.putExtra("orderId", mDidiOrderDetailBean.order.orderId);
//                startActivityForResult(intent, APPLY_AGAIN_RESULT_CODE);

                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DidiOrderWarningFragment.class.getName());
                intent.putExtra("orderDetailBean", mDidiOrderDetailBean);
                intent.putExtra("from", FROM_FLAG_APPLY_AGAIN);
                startActivityForResult(intent, APPLY_AGAIN_RESULT_CODE);
                JDMAUtils.clickEvent("", JDMAConstants.mobile_employeeTravel_traveDetail_application, null);
            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == APPLY_AGAIN_RESULT_CODE) {
            if (getActivity() != null) {
                getActivity().finish();
            }
        }
    }

    private void initData() {
        if (null == getActivity()) {
            return;
        }
        if (TextUtils.isEmpty(mOrderId)) {
            mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
            initView(getActivity());
        } else {
                DidiUtils.getOrderDetail(getActivity(), mOrderId, "", new DidiUtils.IDetailCallback() {
                    @Override
                    public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                        mDidiOrderDetailBean = didiOrderDetailBean;
                        initView(getActivity());
                    }
                });
        }
    }

    private void initView(Context context) {
        isCallCarUser = DidiUtils.isCallCarUser(mDidiOrderDetailBean);
        if (TextUtils.isEmpty(mDidiOrderDetailBean.order.driverName) && TextUtils.isEmpty(mDidiOrderDetailBean.order.driverPhone)) {
            mRlDriverInfo.setVisibility(View.GONE);
        } else {
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.driverLevel)) {
                try {
                    DidiUtils.showDriverLevel(Float.parseFloat(mDidiOrderDetailBean.order.driverLevel), mRbStar);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                mTvFraction.setText(mDidiOrderDetailBean.order.driverLevel + mTvFraction.getContext().getString(R.string.me_didi_minute));
                mTvOrder.setText(mDidiOrderDetailBean.order.driverOrderCount + mTvFraction.getContext().getString(R.string.me_didi_unit_dan));
            } else {
                mLlStarContent.setVisibility(View.GONE);
            }
            mTvDriverName.setText(mDidiOrderDetailBean.order.driverName);
            if (null != getActivity() && !getActivity().isFinishing() && !getActivity().isDestroyed()) {
                ImageLoader.load(getActivity(), mIvService, mDidiOrderDetailBean.icon, false,0, 0);
            }
            mTvCarNo.setText(mDidiOrderDetailBean.order.driverCard);
            mTvCarType.setText(StringUtils.getSubStringFromStart(mDidiOrderDetailBean.order.driverCarType, 12));
            DidiUtils.getDriverImg(mDidiOrderDetailBean.order.driverAvatar, mIvDriverPic, context);
        }

        // 设置提示
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.showTip)) {
            mTvApproval.setVisibility(View.VISIBLE);
            mTvApproval.setText(mDidiOrderDetailBean.order.showTip);
        }
        if (mDidiOrderDetailBean != null && mDidiOrderDetailBean.order != null
                && mDidiOrderDetailBean.order.orderStatus.equals(DidiUtils.STATE_DEBT) && isCallCarUser) {
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.paymentDeepLink)) {
                mTvReimbursement.setVisibility(View.VISIBLE);
            } else {
                mLlConfirm.setVisibility(mDidiOrderDetailBean.order.isConfirm() ? View.GONE : View.VISIBLE);
            }
        } else {
            mTvReimbursement.setVisibility(View.GONE);
            mLlConfirm.setVisibility(View.GONE);
        }

        // 初始值
        mPrice = context.getResources().getString(R.string.me_didi_pay_price);
        mOrderTimeRange = context.getResources().getString(R.string.me_didi_order_time_range);

        // 赋值
        mTvPrice.setText(mDidiOrderDetailBean.price.totalPrice);  // 总价
        mTvTimeRange.setText(String.format(mOrderTimeRange, mDidiOrderDetailBean.order.beginChargeTime, mDidiOrderDetailBean.order.finishTime)); // 打车时间区间
        mLlPriceContiner.removeAllViews();
        for (DidiOrderDetailBean.PriceBean.model bean : mDidiOrderDetailBean.price.detail) {
            LinearLayout mTmp = (LinearLayout) LayoutInflater.from(context).inflate(R.layout.jdme_item_didi_price_info, null);
            TextView mTmpTitle = (TextView) mTmp.findViewById(R.id.tv_title);
            TextView mTmpPrice = (TextView) mTmp.findViewById(R.id.tv_price);
            mTmpTitle.setText(bean.name);
            mTmpPrice.setText(mPrice.replace("--", bean.amount));
            mLlPriceContiner.addView(mTmp);
        }

        if ("0".equals(mDidiOrderDetailBean.order.isFeeComplainted)) {
            mBtnConfirmFee.setEnabled(true);
        } else {
            mBtnConfirmFee.setEnabled(false);
        }
        showDialogFlag = false;

        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.getConfirmInfo())) {
            rlWarning.setVisibility(View.VISIBLE);
            tvWarning.setText(mDidiOrderDetailBean.order.getConfirmInfo());
        } else {
            rlWarning.setVisibility(View.GONE);
        }
        tvEstimatePrice.setText(context.getResources().getString(R.string.me_didi_order_guess_money, mDidiOrderDetailBean.order.getEstimatePrice()));

        //进行中和待支付状态的订单显示急救按钮
        if (DidiUtils.STATE_ONGOING.equals(mDidiOrderDetailBean.order.orderStatus) || DidiUtils.STATE_HOLD_CONFIRM.equals(mDidiOrderDetailBean.order.orderStatus)) {
            mIbHelp.setVisibility(View.VISIBLE);
        } else {
            mIbHelp.setVisibility(View.GONE);
        }

        //拼车订单显示按钮
        boolean showColleague = "1".equals(mDidiOrderDetailBean.isCarPool);
        btnShowColleague.setVisibility(showColleague ? View.VISIBLE : View.GONE);
        btnShowColleague.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mDidiOrderDetailBean == null || mDidiOrderDetailBean.callCarUser == null)
                    return;
                showColleagueDialog(mDidiOrderDetailBean);
            }
        });

        if (!isCallCarUser) {
            //非拼车发起人隐藏按钮
            mLlConfirmGroup.setVisibility(View.GONE);
            mLlBtnGroup.setVisibility(View.GONE);
            mIvTelephone.setVisibility(View.GONE);
        } else {
            mLlConfirmGroup.setVisibility(View.VISIBLE);
            mLlBtnGroup.setVisibility(View.VISIBLE);
            mIvTelephone.setVisibility(View.VISIBLE);
        }
        changeStatus(mDidiOrderDetailBean);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.h5url)) {
            mLlBtnGroup.setVisibility(View.GONE);
        }
        btn_view_detail.setVisibility(TextUtils.isEmpty(mDidiOrderDetailBean.h5url) ? View.GONE : View.VISIBLE);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (null != getActivity()) {
            getActivity().unregisterReceiver(complainSuccessReceiver);
            getActivity().unregisterReceiver(orderComfirmSuccessReceiver);
        }
    }

    private void initOrderConfirmSuccessRe() {
        orderComfirmSuccessReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (getActivity() != null) {
                    getActivity().finish();
                }
            }
        };
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("ORDER_CONFIRM_SUCCESS");
        if (null != getActivity()) {
            getActivity().registerReceiver(orderComfirmSuccessReceiver, intentFilter);
        }
    }

    private void initComplainSuccesReceiver() {
        complainSuccessReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                // 投诉完成后刷新此界面,暂时不访问网络，本地刷新

                mDidiOrderDetailBean.order.setConfirmInfo("");
                mDidiOrderDetailBean.order.setIsWarning("0");
                mDidiOrderDetailBean.order.isFeeComplainted = "1";
                initView(context);
                ToastUtils.showInfoToast(R.string.me_didi_order_fee_complain_success);
            }
        };
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(FEE_COMPLAINS_SUCCESS);
        if (null != getActivity()) {
            getActivity().registerReceiver(complainSuccessReceiver, intentFilter);
        }
    }

    private void changeStatus(DidiOrderDetailBean orderDetailBean) {
        switch (orderDetailBean.order.orderStatus) {
            case DidiUtils.STATE_HOLD_CONFIRM: // 同状态
                if (isCallCarUser) {
                    mLlConfirmGroup.setVisibility(View.VISIBLE);
                    mLlBtnGroup.setVisibility(View.VISIBLE);
                }
                mIbtnAppraisal.setVisibility(View.GONE);
                if (!TextUtils.isEmpty(orderDetailBean.order.alarm) && showDialogFlag) {
                    if (null == getActivity()) {
                        return;
                    }
                    RelativeLayout mLayout = (RelativeLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_view_alert_didi_fee, null);
                    TextView mTvMsg = (TextView) mLayout.findViewById(R.id.tv_tip);
                    mTvMsg.setText(orderDetailBean.order.alarm);
                    AlertDialog.Builder mBuilder = new AlertDialog.Builder(getActivity());
                    mBuilder.setView(mLayout);
                    final AlertDialog alertDialog = mBuilder.create();
                    alertDialog.show();
                    DisplayMetrics dm = new DisplayMetrics();
                    //取得窗口属性
                    getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
                    int width = (int) (dm.widthPixels * 0.9);
                    alertDialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
                    TextView mTvIknow = (TextView) mLayout.findViewById(R.id.tv_ikonw);
                    mTvIknow.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            alertDialog.dismiss();
                        }
                    });
                }
                break;
            case DidiUtils.STATE_FINISHED:
            case DidiUtils.STATE_PENDING:
            case DidiUtils.STATE_DEBT:
                mLlBtnGroup.setVisibility(isCallCarUser ? View.VISIBLE : View.GONE);
                mLlConfirmGroup.setVisibility(View.GONE);
                if ("1".equals(orderDetailBean.order.isCommented) || mAppraisalSuccess) {
                    mIbtnAppraisal.setBackgroundResource(R.drawable.jdme_didi_icon_appraisal_disable);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private void backgroundAlpha(float bgAlpha) {
        if (null != getActivity()) {
            WindowManager.LayoutParams lp = getActivity().getWindow().getAttributes();
            lp.alpha = bgAlpha; //0.0-1.0
            getActivity().getWindow().setAttributes(lp);
        }
    }


    // 确认行程
    private void orderConfirm() {
        if (NClick.isFastDoubleClick())
            return;
        mBtnConfirm.setEnabled(false);
        if (null != getActivity()) {
            SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) {

                @Override
                public void onSuccess(final ResponseInfo<String> request) {
                    super.onSuccess(request);
                    ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                    parser.parse(new ResponseParser.ParseCallback() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            DidiUtils.getOrderDetail(getActivity(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, new DidiUtils.IDetailCallback() {
                                @Override
                                public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                                    if (null == didiOrderDetailBean) {
                                        ToastUtils.showToast(R.string.me_exception_order);
                                    } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
                                        ToastUtils.showToast(R.string.me_exception_order);
                                    } else {
                                        String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
                                        if (TextUtils.isEmpty(className)) {
                                            ToastUtils.showToast(R.string.me_exception_order_state);
                                        } else {
                                            Bundle bundle = new Bundle();
                                            bundle.putSerializable("orderDetailBean", didiOrderDetailBean);
                                            try {
                                                FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content, false, bundle, false);
                                            } catch (ClassNotFoundException e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                    new Handler().postDelayed(new Runnable() {
                                        public void run() {
                                            mBtnConfirm.setEnabled(true);
                                        }
                                    }, 500);
                                }
                            });
                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {
                        }

                        @Override
                        public void parseError(String errorMsg) {
                            mBtnConfirm.setEnabled(true);
                            ToastUtils.showToast(errorMsg);
                        }
                    });
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    if (!TextUtils.isEmpty(info)) {
                        ToastUtils.showToast(info);
                    }
                    mBtnConfirm.setEnabled(true);
                }
            };
            callback.setNeedTranslate(false);
            NetWorkManager.confirmOrder(this, callback, mDidiOrderDetailBean.order.orderId);
        }
    }

//    @Override
//    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        if (requestCode == DidiUtils.REQUEST_CODE_PERMISSION_LOCATION) {
//            //申请权限成功失败都会请求
//            DidiUtils.urgentHelp(requireContext(), mDidiOrderDetailBean.order.orderId);
//        }
//    }

    public void onEventMainThread(DidiOrderDetailBean orderDetailBean) {
        // 跳转逻辑

    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
        if (bundle != null) {
            if (bundle.getBoolean("isOmplaints"))
                mOmplaintsSuccess = bundle.getBoolean("isSuccsess");
            if (bundle.getBoolean("isAppraisal"))
                mAppraisalSuccess = bundle.getBoolean("isSuccsess");
        }

    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OPERATE_GO_DIDI == optionFlag) {
            if (args != null) {
                String isAgain = args.getString("isAgain");
                if ("0".equals(isAgain)) {
//                    mIbtnOmplaints.setBackgroundResource(R.drawable.jdme_didi_icon_complaints_disable);
                    mDidiOrderDetailBean.order.isComplainted = "1";
                }
                String isFeeSuccess = args.getString("isFeeSuccess");
                if ("1".equals(isFeeSuccess) && null != getActivity()) {
                    mBtnConfirmFee.setEnabled(false);
                    getActivity().onBackPressed();
                }
            }
            return true;
        }
        return false;
    }


    @Override
    public void onStart() {
        super.onStart();
        if(isToReimbursement){
            //还款页面回来刷一遍页面
            if (mDidiOrderDetailBean != null && mDidiOrderDetailBean.order != null
                    && mDidiOrderDetailBean.order.orderStatus.equals(DidiUtils.STATE_DEBT)
                    && !TextUtils.isEmpty(mDidiOrderDetailBean.order.paymentDeepLink)) {
                initData();
            }
        }
    }

    private void showColleagueDialog(DidiOrderDetailBean detailBean) {
        if (detailBean == null || null == getContext()) return;
        ColleagueListDialog dialog = new ColleagueListDialog(getContext(), detailBean);
        dialog.show();
    }
}