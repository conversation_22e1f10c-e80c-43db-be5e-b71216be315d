package com.jd.oa.business.didi.widget;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.RadialGradient;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.jd.oa.business.R;

/**
 * 动态图，for 打车
 * Created by <PERSON><PERSON><PERSON> on 16/1/13.
 */
public class CircleProgressRefreshView extends View {

    protected int mCircleColor;
    private int mCircleWidth;
    private int mBallRadius;
    private int mFillColor;
    /**
     * 旋转一周花费时间，毫秒
     */
    private int mShotTime;

    /**
     * 显示波浪效果
     */
    private boolean mShowWave;
    /**
     * 显示小球
     */
    private boolean mShowBall;

    /**
     * 光晕宽
     */
    private int mHaloWidth;
    private int mHaloColor;
    private int mGradientEndColor;    // 渐变边缘颜色

    private Paint mPaint;

    private Bitmap mRollBitmap;
    private Matrix mMatrix;
    private int mDegree;

    private float mAnimRatio = 0.0f;

    private ValueAnimator mValueAnimator;

    public CircleProgressRefreshView(Context context) {
        this(context, null);
    }

    public CircleProgressRefreshView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CircleProgressRefreshView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();

        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CircleProgressRefreshView, defStyleAttr, 0);
        mCircleWidth = a.getDimensionPixelSize(R.styleable.CircleProgressRefreshView_me_tv_circleWidth,
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 1, displayMetrics));
        mBallRadius = a.getDimensionPixelOffset(R.styleable.CircleProgressRefreshView_me_tv_ballRadius,
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8, displayMetrics));
        mFillColor = a.getColor(R.styleable.CircleProgressRefreshView_me_tv_fillColor, Color.WHITE);
        mShotTime = a.getInt(R.styleable.CircleProgressRefreshView_me_tv_shotTime, 1000);
        mCircleColor = a.getColor(R.styleable.CircleProgressRefreshView_me_tv_circleColor, Color.RED);
        mShowWave = a.getBoolean(R.styleable.CircleProgressRefreshView_me_tv_showWave, true);
        mShowBall = a.getBoolean(R.styleable.CircleProgressRefreshView_me_tv_showBall, true);
        a.recycle();


        mHaloWidth = mBallRadius * 2;
        mHaloColor = getLightColor(mCircleColor, 127);
        mGradientEndColor = getLightColor(mCircleColor, 2);

        // ==== 创建滚动小球 ====
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(mCircleColor);

        Bitmap.Config conf = Bitmap.Config.ARGB_8888;
        mRollBitmap = Bitmap.createBitmap(mBallRadius * 2, mBallRadius * 2, conf);
        Canvas canvas = new Canvas(mRollBitmap);
        canvas.drawCircle(mBallRadius, mBallRadius, mBallRadius, mPaint);

        mMatrix = new Matrix();
    }

    @Override
    protected void onDetachedFromWindow() {
        if (null != mRollBitmap && !mRollBitmap.isRecycled()) {
            mRollBitmap.recycle();
            mRollBitmap = null;
        }
        super.onDetachedFromWindow();
    }

    private void setDegree(int degree) {
        this.mDegree = degree;
        if (hasWindowFocus()) {
            invalidate();
        }
    }

    /**
     * 启动
     */
    public void start() {
        if (mValueAnimator != null) {
            mValueAnimator.cancel();
            mValueAnimator = null;
        }

        mValueAnimator = ObjectAnimator.ofInt(360);
        mValueAnimator.setDuration(mShotTime);
        mValueAnimator.setRepeatCount(-1);
        mValueAnimator.setInterpolator(new LinearInterpolator());
        mValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animator) {
                mAnimRatio = animator.getAnimatedFraction();
                setDegree((int) (360 * animator.getAnimatedFraction()));
            }
        });

        mValueAnimator.start();
    }

    /**
     * 停止
     */
    public void stop() {
        setShowBall(false);
        setShowWave(false);
        if (mValueAnimator != null) {
            mValueAnimator.cancel();
        }
    }

    public void setShowWave(boolean showWave) {
        this.mShowWave = showWave;
        invalidate();
    }

    public void setShowBall(boolean showBall) {
        this.mShowBall = showBall;
        invalidate();
    }

    /**
     * 直接画
     *
     * @param canvas
     */
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        // ==== 中心点 ====
        int center = getWidth() / 2;
        // ==== 背景光晕 ====
        drawHaloBg(canvas, center);
        // ==== 波动效果的光晕 ====
        drawWaveHalo(canvas, center);
        // ==== 轨迹圆 ====
        drawCircleTrajectory(canvas, center);

        // 小球走动
        drawBall(canvas, mDegree);
    }

    /**
     * 水波纹光晕效果
     * stroke 逐渐变宽，逐渐透明
     */
    private void drawWaveHalo(Canvas canvas, int center) {
        if (!mShowWave) {
            return;
        }

        mPaint.setShader(null);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(mHaloColor);
        mPaint.setAlpha(255 - (int) (mAnimRatio * 255));

        int radius = getWidth() / 2 - (int) (mHaloWidth - mAnimRatio * mHaloWidth);
        canvas.drawCircle(center, center, radius, mPaint);
    }

    /**
     * 光晕背景
     *
     * @param canvas
     * @param center
     */
    private void drawHaloBg(Canvas canvas, int center) {
        int radius = getWidth() / 2;
        mPaint.setStyle(Paint.Style.FILL);
        RadialGradient gradient = new RadialGradient(center, center, radius,
                new int[]{mCircleColor, mGradientEndColor}, null, Shader.TileMode.MIRROR);
        mPaint.setShader(gradient);
        canvas.drawCircle(center, center, radius, mPaint);
    }

    /**
     * 画轨迹圆
     *
     * @param canvas
     * @param center
     */
    private void drawCircleTrajectory(Canvas canvas, int center) {
        int radius = (getWidth() - mCircleWidth) / 2 - mBallRadius - mHaloWidth / 2;
        Paint paint = mPaint;
        paint.setStyle(Paint.Style.STROKE);
        paint.setColor(mCircleColor);
        paint.setStrokeWidth(mCircleWidth);
        paint.setShader(null);
        canvas.drawCircle(center, center, radius, paint);

        // 填充颜色
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(mFillColor);
        canvas.drawCircle(center, center, (radius - mCircleWidth * 1.0f / 2), paint);
    }

    private void drawBall(Canvas canvas, int degree) {
        if (!mShowBall) {
            return;
        }
        int ballWidth = mRollBitmap.getWidth();
        int ballHeight = mRollBitmap.getHeight();
        int ballSize = Math.min(ballWidth, ballHeight);
        int radius = Math.min(getWidth(), getHeight()) / 2 - ballSize / 2;
        int dx = (int) (radius + Math.cos((float) (90 - degree) / 360f * 2 * Math.PI) * (radius - ballSize / 2));
        int dy = (int) (radius - Math.sin((float) (90 - degree) / 360f * 2 * Math.PI) * (radius - ballSize / 2));
        mMatrix.reset();
        mMatrix.postTranslate(dx, dy);
        canvas.drawBitmap(mRollBitmap, mMatrix, null);
    }


    /**
     * 获取颜色的浅色形式
     *
     * @param color 颜色
     * @param alpha 透明度
     * @return
     */
    private int getLightColor(int color, int alpha) {
        int r = Color.red(color);
        int g = Color.green(color);
        int b = Color.blue(color);
        int a = Color.alpha(color);
        int ta = 255 - alpha;
        a = a - ta < 0 ? a / 2 + ta / 3 : a - ta;
        return Color.argb(a, r, g, b);
    }

    /**
     * 画移动小圆点方法一
     *
     * @param canvas
     * @param center
     * @param radius
     */
    @Deprecated
    private void drawRollCircle(Canvas canvas, int center, int radius) {
        Paint paint = mPaint;
        paint.setStyle(Paint.Style.STROKE);
        paint.setColor(mCircleColor);
        paint.setStrokeWidth(mBallRadius * 2);
        paint.setStrokeCap(Paint.Cap.ROUND);
        paint.setStrokeJoin(Paint.Join.ROUND);
        RectF rectF = new RectF(center - radius,
                center - radius,
                center + radius,
                center + radius);

        // 画弧度，0.05f,画笔宽，就是圆点，感觉这种方式怪
        canvas.drawArc(rectF, -180, 0.05f, false, mPaint);
    }
}
