package com.jd.oa.business.caraddress;

import com.jd.oa.business.caraddress.bean.CarAddressListWrapper;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

public class CarAddressListPresenter extends AbsM<PERSON>Presenter<CarAddressContract.ICarAddressListView> implements CarAddressContract.ICarAddressListPresenter {

    private CarAddressContract.ICarAddressRepo mRepo;

    public CarAddressListPresenter(CarAddressContract.ICarAddressListView view) {
        super(view);
        mRepo = new CarAddressRepoImpl();
    }

    @Override
    public void loadCarAddress() {
        view.showLoading(null);
        mRepo.getCarAddressList(new LoadDataCallback<CarAddressListWrapper>() {
            @Override
            public void onDataLoaded(CarAddressListWrapper carAddressListWrapper) {
//                view.showCarAddressList(carAddressListWrapper.getAddressList());
                view.showCarAddressList(carAddressListWrapper);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        });
    }

    @Override
    public void onDestroy() {

    }
}
