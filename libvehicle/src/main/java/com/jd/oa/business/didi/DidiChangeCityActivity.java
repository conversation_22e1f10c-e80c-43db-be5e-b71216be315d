package com.jd.oa.business.didi;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.adapter.DidiCitySortAdapter;
import com.jd.oa.business.didi.model.DidiCityInfoBean;
import com.jd.oa.business.didi.model.DidiCityListInfoBean;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.ui.sortlistview.SideBar;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class DidiChangeCityActivity extends BaseActivity {

    private Context mContext;

    private View mRootView; // 根View

    private EditText mEtAddress;
    private TextView mTvCityName;

    private String mCityName;

    private DidiCityListInfoBean mDidiCityListInfoBean;

    private ListView mListView;
    private SideBar sideBar;
    private TextView dialog;

    private List<DidiCityInfoBean> mListDidiCityInfoBean;
    private DidiCitySortAdapter mAdapter;

    private TextView mTvCancel;
    private TextView mTvInput;

    private View.OnClickListener mOnclicklistener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
//            DidiUtils.closeBoard(mContext, mEtAddress);
            finish();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mRootView = getLayoutInflater().inflate(R.layout.jdme_activity_didi_change_city, null, false);
        setContentView(mRootView);
        mContext = this;
        getSupportActionBar().hide();
        init();
    }

    private void init() {

        mEtAddress = findViewById(R.id.et_city);
        mTvCityName = findViewById(R.id.tv_city_name);
        mListView = findViewById(R.id.lv_conference_room_search_list);
        sideBar = findViewById(R.id.sidebar);
        dialog = findViewById(R.id.dialog);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvInput = findViewById(R.id.tv_input);
        mTvCancel.setOnClickListener(mOnclicklistener);
        mTvInput.setOnClickListener(mOnclicklistener);

        mListDidiCityInfoBean = new ArrayList<>();

        mCityName = getIntent().getStringExtra("cityName");
        mDidiCityListInfoBean = (DidiCityListInfoBean) getIntent().getSerializableExtra("entity");
        mTvCityName.setText(mCityName);

        mEtAddress.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                filterCityInfo();
            }

        });

        sideBar.setTextView(dialog);
        sideBar.setOnTouchingLetterChangedListener(new SideBar.OnTouchingLetterChangedListener() {
            @Override
            public void onTouchingLetterChanged(String s) {
                //该字母首次出现的位置
                int position = mAdapter.getPositionForSection(s.charAt(0));
                if (position != -1) {
                    mListView.setSelection(position);
                }
            }
        });
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                DidiCityInfoBean mBean = mAdapter.getItem(position);
                Intent intent = new Intent();
                intent.putExtra("cityinfo", mBean);
                setResult(100, intent);
                DidiUtils.closeBoard(mContext, mEtAddress);
                finish();
            }
        });

        try {
            for (char ch = 'A'; ch < 'A' + 26; ch++) {
                Field tmp = mDidiCityListInfoBean.getClass().getDeclaredField(ch + "");
                DidiCityInfoBean[] mBean = (DidiCityInfoBean[]) tmp.get(mDidiCityListInfoBean);
                if (mBean != null)
                    for (DidiCityInfoBean tmpBean : mBean) {
                        tmpBean.sortLetters = ch + "";
                        mListDidiCityInfoBean.add(tmpBean);
                    }
            }
            mAdapter = new DidiCitySortAdapter(this, mListDidiCityInfoBean);
            mListView.setAdapter(mAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPressed() {
        setResult(400);
        DidiUtils.closeBoard(mContext, mEtAddress);
        super.onBackPressed();
    }

    private void filterCityInfo() {
        String val = mEtAddress.getText().toString();
        List<DidiCityInfoBean> tmpListBean = new ArrayList<>();
        for (DidiCityInfoBean bean : mListDidiCityInfoBean) {
            if (bean.cityName.indexOf(val) >= 0 || bean.cityPinyin.indexOf(val) >= 0)
                tmpListBean.add(bean);
        }
        mAdapter.updateListView(tmpListBean);
    }
}
