package com.jd.oa.business.didi.dialog;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatDialog;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.utils.DisplayUtil;

/**
 * Created by peidongbiao on 2019/4/12
 */
public class CarPoolDescriptionDialog extends AppCompatDialog {
    private static final String URL = "http://jdmegy.jd.com/vehicle/carPoolDesc.html";
    private FrameLayout mContainer;
    private Button mBtnConfirm;
    private WebView mWebView;
    private TextView mTvError;

    private OnConfirmClickListener mOnConfirmClickListener;

    public CarPoolDescriptionDialog(Context context) {
        super(context);
    }

    public CarPoolDescriptionDialog(Context context, int theme) {
        super(context, theme);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.jdme_dialog_carpool_description);
        mContainer = findViewById(R.id.container);
        mBtnConfirm = findViewById(R.id.btn_confirm);
        mTvError = findViewById(R.id.tv_error);
        mWebView = createWebView();
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes(); // 获取对话框当前的参数值
        layoutParams.height = (int) (DisplayUtil.getScreenHeight(getContext()) * 0.7); // 高度设置为屏幕的比例
        layoutParams.width = (int) (DisplayUtil.getScreenWidth(getContext()) * 0.9); // 宽度设置为屏幕的比例
        getWindow().setAttributes(layoutParams); // 设置生效
        getWindow().setBackgroundDrawableResource(R.drawable.jdme_dialog_bg_round_corners);
        mWebView.setWebViewClient(new WebViewClient(){
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @TargetApi(Build.VERSION_CODES.LOLLIPOP)
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(request.getUrl().toString());
                return true;
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                mTvError.setVisibility(View.VISIBLE);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                mTvError.setVisibility(View.VISIBLE);
            }
        });

        mWebView.setWebChromeClient(new WebChromeClient(){

        });
        mContainer.addView(mWebView, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));

        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                mWebView.stopLoading();
                mWebView.destroy();
                mWebView = null;
            }
        });

        mWebView.loadUrl(URL);
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancel();
                if (mOnConfirmClickListener != null) {
                    mOnConfirmClickListener.onClick();
                }
            }
        });
    }

    private WebView createWebView() {
        WebView webView = new WebView(getContext().getApplicationContext());
        WebSettings settings = (webView).getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setLoadsImagesAutomatically(true);
        settings.setAllowFileAccessFromFileURLs(false);
        settings.setAllowUniversalAccessFromFileURLs(false);
        return webView;
    }

    public void setOnConfirmClickListener(OnConfirmClickListener onConfirmClickListener) {
        mOnConfirmClickListener = onConfirmClickListener;
    }

    public interface OnConfirmClickListener {
        void onClick();
    }
}
