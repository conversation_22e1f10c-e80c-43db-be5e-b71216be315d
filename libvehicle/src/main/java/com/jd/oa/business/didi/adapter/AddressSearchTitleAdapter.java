package com.jd.oa.business.didi.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.ui.recycler.TypeAdapter;

public class AddressSearchTitleAdapter extends TypeAdapter<String, AddressSearchTitleAdapter.VH> {

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        View view = inflater.inflate(R.layout.jdme_item_didi_address_title, viewGroup, false);
        return new VH(view);
    }

    @Override
    protected void onBindViewHolder(String bean, VH vh, int position) {
        ((TextView) vh.itemView).setText(bean);
    }

    public static class VH extends RecyclerView.ViewHolder {
        public VH(View itemView) {
            super(itemView);
        }
    }
}