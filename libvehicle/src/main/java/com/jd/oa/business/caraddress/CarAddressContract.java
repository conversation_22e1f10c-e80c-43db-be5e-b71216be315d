package com.jd.oa.business.caraddress;

import com.google.gson.JsonObject;
import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.business.caraddress.bean.CarAddressListWrapper;
import com.jd.oa.business.didi.model.DidiCityBean;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONObject;

import java.util.List;

public class CarAddressContract {
    public interface ICarAddressListView extends IMVPView {
        void showCarAddressList(CarAddressListWrapper carAddressListWrapper);
    }

    public interface ICarAddressModifyView extends IMVPView {
        void onCheckCityResult(DidiCityBean bean);

        void onModifyCarAddressSuccess(JSONObject jsonObject);
    }

    public interface ICarAddressListPresenter extends IMVPPresenter {
        void loadCarAddress();
    }

    public interface ICarAddressModifyPresenter extends IMVPPresenter {
        void addCarAddress(CarAddress carAddress);

        void modifyCarAddress(CarAddress carAddress);

        void getCityList(String cityName);
    }

    public interface ICarAddressRepo extends IMVPRepo {

        void getCarAddressList(LoadDataCallback<CarAddressListWrapper> callback);

        void addCarAddress(CarAddress carAddress, LoadDataCallback<JSONObject> callback);

        void modifyCarAddress(CarAddress carAddress, LoadDataCallback<JSONObject> callback);

        void checkCity(String cityName, LoadDataCallback<DidiCityBean> callback);
    }
}
