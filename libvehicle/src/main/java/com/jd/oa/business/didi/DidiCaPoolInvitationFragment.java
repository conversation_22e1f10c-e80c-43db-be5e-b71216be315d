package com.jd.oa.business.didi;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.adapter.CarPoolInvitationColleagueAdapter;
import com.jd.oa.business.didi.model.CarPoolInvitationStatus;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ToastUtils;


import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * Created by peidongbiao on 2019/4/2
 */
@Navigation(hidden = false,  displayHome = true)
public class DidiCaPoolInvitationFragment extends BaseFragment {
    private static final String ABLE_TO_USE_CAR = "0";  //可以用车
    private static final String NEED_TO_PUNCH = "1";    //需要打卡
    private static final String OVER_TIME_NOT_ENOUGH  = "2";    //加班时长不够

    private TextView mTvTips;
    private TextView mTvCountdown;
    private TextView mTvCallCar;
    private TextView mTvStartTime;
    private TextView mTvFrom;
    private TextView mTvTo;
    private Button mBtnReject;
    private Button mBtnConfirm;
    private RecyclerView mRecyclerView;
    private CarPoolInvitationColleagueAdapter mColleagueAdapter;

    private DidiOrderDetailBean mDetailBean;
    private String mOrderId;

    private Disposable mCountdownDisposable;
    private CarPoolInvitationStatus mStatus;
    private boolean mCountdownPaused;
    private long mTimeDifference;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if(getArguments() == null || !getArguments().containsKey("orderDetailBean")) {
            getActivity().finish();
            return super.onCreateView(inflater, container, savedInstanceState);
        }
        View view = inflater.inflate(R.layout.jdme_fragment_didi_car_pool_invitation, container, false);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_car_pool_invitation);

        mDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        mOrderId = mDetailBean.order.orderId;

        mTvTips = view.findViewById(R.id.tv_tips);
        mTvCountdown = view.findViewById(R.id.tv_countdown);
        mTvCallCar = view.findViewById(R.id.tv_call_car);
        mTvStartTime = view.findViewById(R.id.tv_start_time);
        mTvFrom = view.findViewById(R.id.tv_from);
        mTvTo = view.findViewById(R.id.tv_to);
        mBtnReject = view.findViewById(R.id.btn_reject);
        mBtnConfirm = view.findViewById(R.id.btn_confirm);
        mRecyclerView = view.findViewById(R.id.recycler_colleague);

        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mColleagueAdapter = new CarPoolInvitationColleagueAdapter(getContext());
        mRecyclerView.setAdapter(mColleagueAdapter);

        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                carPoolUserConfirm();
            }
        });

        mBtnReject.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new IosAlertDialog(getContext())
                        .builder()
                        .setMsg(getContext().getString(R.string.me_car_pool_reject_confirm, mStatus.getCallCarRealName(), mStatus.getCallCarUserName()))
                        .setNegativeButton(getString(R.string.me_car_pool_let_me_think), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {

                            }
                        })
                        .setPositiveButton(getString(R.string.me_car_pool_reject), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                carPoolUserReject();
                            }
                        }).show();
            }
        });

        getCarPoolOrderStatus();
        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if(mCountdownDisposable != null) {
            mCountdownDisposable.dispose();
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if(mCountdownPaused) {
            countdown(mStatus);
            mCountdownPaused = false;
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mCountdownDisposable != null && !mCountdownDisposable.isDisposed()) {
            mCountdownDisposable.dispose();
            mCountdownPaused = true;
        }
    }

    private void show(CarPoolInvitationStatus status) {
        mTvTips.setText(getString(R.string.me_car_travel_invitation_tips, getLimitTime(status.getConfirmStopSecond())));
        if(!TextUtils.isEmpty(status.getCallCarUserName()) && !TextUtils.isEmpty(status.getCallCarRealName())) {
            mTvCallCar.setText(String.format(Locale.getDefault(), "%s(%s)", status.getCallCarRealName(), status.getCallCarUserName()));
        }
        if("0".equals(mDetailBean.order.getOrderTimeType())) {
            mTvStartTime.setText(getString(R.string.me_car_pool_start_time_now));
        }else {
            mTvStartTime.setText(mDetailBean.order.getStartTime());
        }
        mTvFrom.setText(status.getStartName());
        mTvTo.setText(status.getEndName());
        List<CarPoolUserStatus> list = new ArrayList<>();
        CarPoolUserStatus callCarUser = DidiUtils.getCallCarUser(mDetailBean);
        if(callCarUser != null) {
            list.add(callCarUser);
        }
        if(CollectionUtil.notNullOrEmpty(status.getCarPoolUsers())) {
            list.addAll(status.getCarPoolUsers());
        }
        mColleagueAdapter.refresh(list);
    }

    private void countdown(CarPoolInvitationStatus status) {
        if(status == null) return;
        long start = Long.parseLong(status.getStartTimestamp());
        //long current = Long.parseLong(status.getCurTimestamp());
        long current = (System.currentTimeMillis() / 1000) + mTimeDifference;
        long duration = Long.parseLong(status.getConfirmStopSecond());
        long end = start + duration;
        final long remain = (end - current);
        mTvCountdown.setText(DidiUtils.secToTime((int) remain));
        if (mCountdownDisposable != null) {
            mCountdownDisposable.dispose();
        }
        mCountdownDisposable = Observable.interval(0,1, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                        MELogUtil.localI(MELogUtil.TAG_TAX, "countdown: " + aLong);
                        int time = (int)(remain - aLong);
                        if(time <= 0) {
                            time = 0;
                            mCountdownDisposable.dispose();
                            mCountdownPaused = false;
                            expiredCarPoolUserStatus(mOrderId);
                            showExpiredDialog();
                        }
                        mTvCountdown.setText(DidiUtils.secToTime(time));
                    }
                });
    }

    private void getCarPoolOrderStatus() {
        NetWorkManager.getCarPoolUserStatus(mOrderId, new SimpleRequestCallback<String>(getContext(), true) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if(!isAlive()) return;
                ApiResponse<CarPoolInvitationStatus> response = ApiResponse.parse(info.result, CarPoolInvitationStatus.class);
                if(!response.isSuccessful()) {
                    ToastUtils.showToast(response.getErrorMessage());
                    return;
                }
                mStatus = response.getData();
                mTimeDifference = getTimeDifference(mStatus);
                show(mStatus);
                boolean isExpired = isExpired(mStatus);
                if (!isExpired) {
                    countdown(mStatus);
                    mBtnReject.setEnabled(true);
                    mBtnConfirm.setEnabled(true);
                }else {
                    mTvCountdown.setText("00:00");
                    expiredCarPoolUserStatus(mOrderId);
                    showExpiredDialog();
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    /**
     * 确定同乘
     */
    private void carPoolUserConfirm() {
        NetWorkManager.confirmCarPoolUserStatus(mOrderId, new SimpleRequestCallback<String>(getContext(), true) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if(!isAlive()) return;
                ApiResponse<Map<String,String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String,String>>(){}.getType());
                if(!response.isSuccessful()) {
                    ToastUtils.showToast(response.getErrorMessage());
                    return;
                }
                String status = response.getData().get("dakaStatus");
                String showTip = response.getData().get("showTip");
                if (checkPunchStatus(status, showTip)) {
                    //刷新订单状态，并且跳页
                    DidiUtils.getOrderDetailAndJump(getActivity(), mDetailBean);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    /**
     * 取消同乘
     */
    private void carPoolUserReject() {
        NetWorkManager.rejectCarPoolUserStatus(mOrderId, new SimpleRequestCallback<String>(getContext(),true) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if(!isAlive()) return;
                ApiResponse<Map> response = ApiResponse.parse(info.result, Map.class);
                if(!response.isSuccessful()) {
                    ToastUtils.showToast(response.getErrorMessage());
                    return;
                }
                getActivity().finish();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    /**
     * 设置订单状态为过期
     */
    private void expiredCarPoolUserStatus(String orderId) {
        NetWorkManager.expiredCarPoolUserStatus(orderId, new SimpleRequestCallback<String>(getContext()) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if(!isAlive()) return;
                ApiResponse<Map<String,String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String,String>>(){}.getType());
                if(!response.isSuccessful())return;
                mTvCountdown.setText("00:00");
                mBtnReject.setEnabled(false);
                mBtnConfirm.setEnabled(false);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    private void showExpiredDialog() {
        IosAlertDialog dialog = new IosAlertDialog(getContext()).builder()
                .setMsg(getString(R.string.me_car_pool_invitation_expired))
                .setPositiveButton(getString(R.string.me_car_pool_return), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        getActivity().finish();
                    }
                });
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        dialog.show();
    }

    private boolean isExpired(CarPoolInvitationStatus status) {
        long start = Long.parseLong(status.getStartTimestamp());
        long current = Long.parseLong(status.getCurTimestamp());
        long duration = Long.parseLong(status.getConfirmStopSecond());
        long end = start + duration;
        boolean isExpired = current > end;
        return isExpired;
    }

    private String getLimitTime(String limitTime) {
        if (TextUtils.isEmpty(limitTime) || !TextUtils.isDigitsOnly(limitTime)) return "0";
        int time = Integer.parseInt(limitTime);
        int hour = 0;
        int minute = 0;
        int second = 0;
        if (time <= 0) {
            return "0" + getString(R.string.me_car_pool_second);
        } else {
            if (time >= 3600) {
                hour = time / 3600;
                time = time % 3600;
            }
            if (time >= 60) {
                minute = time / 60;
                second = time % 60;
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (hour != 0) {
           stringBuilder.append(hour);
           stringBuilder.append(getString(R.string.me_car_pool_hour));
        }
        if(minute != 0) {
            stringBuilder.append(minute);
            stringBuilder.append(getString(R.string.me_car_pool_minute));
        }
        if(second !=0 ){
            stringBuilder.append(second);
            stringBuilder.append(getString(R.string.me_car_pool_second));
        }
        return stringBuilder.toString();
    }

    private long getTimeDifference(CarPoolInvitationStatus status) {
        if(status == null) return 0;
        long client = System.currentTimeMillis() / 1000;
        long server = Long.parseLong(status.getCurTimestamp());
        return server - client;
    }

    /**
     * 确认同乘后，检查返回结果
     * @param status
     * @return
     */
    private boolean checkPunchStatus(String status, String message) {
        if (ABLE_TO_USE_CAR.equals(status)) {
            return true;
        } else if (NEED_TO_PUNCH.equals(status)) {
            IosAlertDialog dialog = new IosAlertDialog(getContext()).builder()
                    .setMsg(message)
                    .setPositiveButton(getString(R.string.me_car_pool_to_punch), new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            Router.build(DeepLink.WORKBENCH_OLD).go(getContext());
                        }
                    });
            dialog.show();
            return false;
        } else if (OVER_TIME_NOT_ENOUGH.equals(status)) {
            IosAlertDialog dialog = new IosAlertDialog(getContext()).builder()
                    .setMsg(message)
                    .setNegativeButton(getString(R.string.me_car_pool_to_punch), new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            Router.build(DeepLink.WORKBENCH_OLD).go(getContext());
                        }
                    })
                    .setPositiveButton(getString(R.string.me_car_pool_reject_invitation), new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            carPoolUserReject();
                        }
                    });
            dialog.show();
            return false;
        } else {
            return true;
        }
    }
}