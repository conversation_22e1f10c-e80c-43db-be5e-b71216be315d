package com.jd.oa.business.didi;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;

import com.chenenyu.router.Router;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ResponseParser;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 确认费用
 * Created by qudongshi on 2017/1/15 新增支付阻断.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderDetailConfirmFragment extends DidiBaseFragment implements
        FragmentOperatingListener, OperatingListener {

    LinearLayout mLlPriceContiner;

    private View mRootView;

    private TextView mTvTip; // 提示信息
    private TextView mTvDepartureTime; // 上车时间
    private TextView mTvFrom; // 出发地
    private TextView mTvTo; // 目的地
    private TextView mTvCount; // 合计金额
    private TextView mTvOpt; // 操作
    private ImageView mIvBtnOpt; // 展开/收起

    private Button mBtnConfirm; //司机乱收附加费

    private Button mBtnConfirmFee; // 费用正常

    private String mOrderId;

    private DidiOrderDetailBean mDidiOrderDetailBean;

    private String mPrice;

    private boolean showDialogFlag = true;
    private Button btn_view_detail;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_confirm, container, false);

        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_order_detail_confirm);

        mOrderId = getArguments().getString("orderId");
        if (TextUtils.isEmpty(mOrderId)) {
            mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
            initView();
        } else {
            DidiUtils.getOrderDetail(getActivity(), mOrderId, "", new DidiUtils.IDetailCallback() {
                @Override
                public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                    mDidiOrderDetailBean = didiOrderDetailBean;
                    initView();
                }
            });
        }
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    private void initView() {
        mLlPriceContiner = mRootView.findViewById(R.id.ll_price_container);

        mTvTip = mRootView.findViewById(R.id.tv_tip); // 提示信息
        mTvDepartureTime = mRootView.findViewById(R.id.tv_departure_time); // 上车时间
        mTvFrom = mRootView.findViewById(R.id.tv_from); // 出发地
        mTvTo = mRootView.findViewById(R.id.tv_to); // 目的地
        mTvCount = mRootView.findViewById(R.id.tv_count); // 合计金额
        mTvOpt = mRootView.findViewById(R.id.tv_opt); // 操作
        mIvBtnOpt = mRootView.findViewById(R.id.iv_btn_opt); // 展开/收起

        mBtnConfirm = mRootView.findViewById(R.id.btn_confirm_stroke); //司机乱收附加费

        mBtnConfirmFee = mRootView.findViewById(R.id.btn_confirm_fee); // 费用正常
        btn_view_detail = mRootView.findViewById(R.id.btn_view_detail); // 自费升舱H5详情
        btn_view_detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (mDidiOrderDetailBean != null && !TextUtils.isEmpty(mDidiOrderDetailBean.h5url)) {
                    Router.build(mDidiOrderDetailBean.h5url).go(getActivity());
                }
            }
        });

        mBtnConfirmFee.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pageRedirect(mDidiOrderDetailBean, 0);
            }
        });

        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDialog();
            }
        });

        mIvBtnOpt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (View.GONE == mLlPriceContiner.getVisibility()) {
                    mLlPriceContiner.setVisibility(View.VISIBLE);
                    mIvBtnOpt.setImageResource(R.drawable.jdme_icon_didi_confirm_up);
                    mTvOpt.setText(R.string.me_didi_order_title_opt_up);
                } else {
                    mLlPriceContiner.setVisibility(View.GONE);
                    mIvBtnOpt.setImageResource(R.drawable.jdme_icon_didi_confirm_down);
                    mTvOpt.setText(R.string.me_didi_order_title_opt_down);
                }
            }
        });

        // 初始值
        mPrice = getResources().getString(R.string.me_didi_pay_price);

        // 提示标题
        StringBuffer mStr = new StringBuffer();
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.price.feeObjection.objectionTitle))
            mStr.append(mDidiOrderDetailBean.price.feeObjection.objectionTitle);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.price.feeObjection.objectionMsg))
            mStr.append(mDidiOrderDetailBean.price.feeObjection.objectionMsg);
        mTvTip.setText(mStr.toString());
        //  内容体
        mTvDepartureTime.setText(mDidiOrderDetailBean.order.beginChargeTime); // 上车时间
        mTvFrom.setText(mDidiOrderDetailBean.order.startName);
        mTvTo.setText(mDidiOrderDetailBean.order.endName);
        mTvCount.setText(mDidiOrderDetailBean.price.totalPrice);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.startName))
            mTvFrom.setText(mDidiOrderDetailBean.order.startName);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.endName))
            mTvTo.setText(mDidiOrderDetailBean.order.endName);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.price.feeObjection.errorBtnText))
            mBtnConfirm.setText(mDidiOrderDetailBean.price.feeObjection.errorBtnText);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.price.feeObjection.okBtnText))
            mBtnConfirmFee.setText(mDidiOrderDetailBean.price.feeObjection.okBtnText);


        for (DidiOrderDetailBean.PriceBean.model bean : mDidiOrderDetailBean.price.detail) {
            LinearLayout mTmp = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_price_info, null);
            TextView mTmpTitle = (TextView) mTmp.findViewById(R.id.tv_title);
            TextView mTmpPrice = (TextView) mTmp.findViewById(R.id.tv_price);
            mTmpTitle.setText(bean.name);
            mTmpPrice.setText(mPrice.replace("--", bean.amount));
            mLlPriceContiner.addView(mTmp);
        }

        showDialogFlag = false;
        btn_view_detail.setVisibility(TextUtils.isEmpty(mDidiOrderDetailBean.h5url) ? View.GONE : View.VISIBLE);
    }

    private void showDialog() {
        LinearLayout mLayout = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_view_alert_didi_fee_2, null);
        TextView mTvTitle = (TextView) mLayout.findViewById(R.id.txt_title);
        TextView mTvTip = (TextView) mLayout.findViewById(R.id.txt_tip);
        TextView mTvMsg = (TextView) mLayout.findViewById(R.id.txt_msg);
        Button mBtnConfirm = (Button) mLayout.findViewById(R.id.btn_pos);
        Button mBtnCancel = (Button) mLayout.findViewById(R.id.btn_neg);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.price.feeObjection.confirmMsg))
            mTvTitle.setText(mDidiOrderDetailBean.price.feeObjection.confirmMsg);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.price.feeObjection.confirmTips))
            mTvTip.setText(mDidiOrderDetailBean.price.feeObjection.confirmTips);
        else
            mTvTip.setVisibility(View.GONE);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.price.feeObjection.confirmFee))
            mTvMsg.setText(mPrice.replace("--", mDidiOrderDetailBean.price.feeObjection.confirmFee));
        else
            mTvMsg.setVisibility(View.GONE);

        AlertDialog.Builder mBuilder = new AlertDialog.Builder(getActivity());
        mBuilder.setView(mLayout);

        final AlertDialog alertDialog = mBuilder.create();
        alertDialog.show();
        DisplayMetrics dm = new DisplayMetrics();
        //取得窗口属性
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.9);
        alertDialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
        TextView mTvIknow = (TextView) mLayout.findViewById(R.id.tv_ikonw);
        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialog.dismiss();
            }
        });
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                orderConfirmFeeObjection(alertDialog);
            }
        });
    }

    private void pageRedirect(DidiOrderDetailBean orderDetailBean, int type) {
        Bundle bundle = new Bundle();
        if (0 == type)
            bundle.putSerializable("orderDetailBean", orderDetailBean);
        else
            bundle.putSerializable("orderId", orderDetailBean.order.orderId);
        FragmentUtils.replaceWithCommit(getActivity(), DidiOrderDetailFragment.class, R.id.me_fragment_content, false, bundle, false);
    }

    // 确认行程
    private void orderConfirmFeeObjection(final AlertDialog alertDialog) {
        if (NClick.isFastDoubleClick())
            return;
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", mDidiOrderDetailBean.order.orderId);
        params.put("feeType", "01");
        NetWorkManager.request(this, Constant.getConstant(NetWorkManager.mReqeustType).API_ME_VEHICLE_SUBMITFEEOBJECTION, new SimpleRequestCallback<String>(getActivity(), false, false) {
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        pageRedirect(mDidiOrderDetailBean, 1);
                        alertDialog.dismiss();
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, params);
    }

    public void onEventMainThread(DidiOrderDetailBean orderDetailBean) {
        // 跳转逻辑

    }

    @Override
    public void onFragmentHandle(Bundle bundle) {

    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {

        return false;
    }
}
