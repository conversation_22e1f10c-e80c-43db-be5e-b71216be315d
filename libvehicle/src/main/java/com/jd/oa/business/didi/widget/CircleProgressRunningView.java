package com.jd.oa.business.didi.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;

import com.jd.oa.business.R;
import com.jd.oa.utils.NamedThreadFactory;

import java.util.Locale;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 车子运行中
 * Created by z<PERSON><PERSON> on 16/1/15.
 */
public class CircleProgressRunningView extends CircleProgressRefreshView {

    public final int MINUTES = 60 * 1000;// 分毫秒值
    public final int HOUR = 60 * MINUTES;// 小时毫秒值
    private final String TIME_PATTERN = "00:00:00";
    private int mTextSize;
    private Bitmap mImage;

    private Paint mPaint;
    private Rect mTextBound;
    private Rect mImageBound;


    /**
     * 行程开始时间
     */
    private int mStartTime;

    /**
     * 间隙
     */
    private int mGap;

    /**
     * 计时器
     */
    private ScheduledExecutorService mExecutor;
    private TimerTask mTimerTask;


    public CircleProgressRunningView(Context context) {
        this(context, null);
    }

    public CircleProgressRunningView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CircleProgressRunningView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();

        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CircleProgressRunningView, defStyleAttr, 0);
        mTextSize = a.getDimensionPixelSize(R.styleable.CircleProgressRunningView_me_tv_texSize,
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, 12, displayMetrics));
        mImage = BitmapFactory.decodeResource(getResources(), a.getResourceId(R.styleable.CircleProgressRunningView_me_tv_img, 0));
        a.recycle();


        // 不显示动态效果
        setShowBall(false);
        setShowWave(false);

        mStartTime = 0;
        mGap = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8, displayMetrics);


        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setTextSize(mTextSize);
        mPaint.setColor(mCircleColor);

        mTextBound = new Rect();
        mPaint.getTextBounds(TIME_PATTERN, 0, TIME_PATTERN.length(), mTextBound);

        mImageBound = new Rect();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int txtHeight = mTextBound.height();
        int txtWidth = mTextBound.width();

        int imgWidth = 0;
        int imgHeight = 0;
        if (mImage != null) {
            imgWidth = mImage.getWidth();
            imgHeight = mImage.getHeight();

            /* 设置图片的宽高范围 ,暂不用
        float ratio = imgWidth * 1.0f / getWidth();
		if(ratio > 1.0f) {
			ratio = 0.8f;

			imgWidth = (int) (imgWidth * ratio);
			imgHeight = (int) (imgHeight * ratio);
		} else {
			ratio = 0.8f + 0.8f - ratio;
			imgWidth = (int) (imgWidth * ratio);
			imgHeight = (int) (imgHeight * ratio);
		}*/

            mImageBound.left = (getWidth() - imgWidth) / 2;
            mImageBound.right = mImageBound.left + imgWidth;
            mImageBound.top = (getHeight() - imgHeight) / 2 - txtHeight - mGap;
            mImageBound.bottom = mImageBound.top + imgHeight;

            // 画图片

//#1070138 java.lang.RuntimeException
//todo Canvas: trying to use a recycled bitmap android.graphics.Bitmap@fab6136
            if (mImage.isRecycled()) {
                return;
            }
            canvas.drawBitmap(mImage, null, mImageBound, mPaint);
        } else {
            // 没有图片时，文字居中显示
            mImageBound.bottom = (getHeight() - txtHeight) / 2;
        }

        // 画时间
        String fmtTime = formatTimeMills(mStartTime);
        canvas.drawText(fmtTime, (getWidth() - txtWidth) / 2, mImageBound.bottom + mGap + txtHeight, mPaint);
    }

    /**
     * 设置行程开始时间，默认为 0
     *
     * @param startTime
     */
    public void setStartTime(int startTime) {
        this.mStartTime = startTime;
    }

    /**
     * 开始计数
     */
    @Override
    public void start() {
        super.start();
        releaseTimerTask();

        mExecutor = Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory(this.getClass().getName()));
        mTimerTask = new TimerTask() {
            @Override
            public void run() {
                mStartTime += 1000;
            }
        };
        mExecutor.scheduleWithFixedDelay(mTimerTask, 0, 1, TimeUnit.SECONDS);
    }

    /**
     * 停止计数
     */
    public void stop() {
        super.stop();
        releaseTimerTask();
        mStartTime = 0;
    }


    private void releaseTimerTask() {
        if (null != mTimerTask) {
            mTimerTask.cancel();
            mTimerTask = null;
        }

        if (null != mExecutor && !mExecutor.isShutdown()) {
            mExecutor.shutdown();
            mExecutor = null;
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        releaseTimerTask();
        if (null != mImage && !mImage.isRecycled()) {
            mImage.recycle();
            mImage = null;
        }

        super.onDetachedFromWindow();
    }

    /**
     * 返回格式化秒的字符串形式,如：23:05:22
     *
     * @return
     */
    private String formatTimeMills(int timeMills) {
        if (timeMills > 0) {
            // 取时分秒
            int hour = timeMills / HOUR;
            int minute = (timeMills - hour * HOUR) / MINUTES;
            int second = timeMills / 1000 % 60;
            return String.format(Locale.CHINESE, "%02d:%02d:%02d", hour, minute, second);
        }
        return TIME_PATTERN;
    }
}
