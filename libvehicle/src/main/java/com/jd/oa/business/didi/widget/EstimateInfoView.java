package com.jd.oa.business.didi.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiEstimatePriceBean;
import com.jd.oa.utils.ImageLoader;

import java.util.ArrayList;
import java.util.List;

public class EstimateInfoView extends LinearLayout {

    private TextView tv_duration;
    private TextView tv_distance;
    private TextView tv_price;
    private RecyclerView icon_list;

    private String maxPrice;
    private String minPrice;
    private String price;

    private boolean hasDidiEstimate = false;

    public EstimateInfoView(Context context) {
        this(context, null);
    }

    public EstimateInfoView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EstimateInfoView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View.inflate(context, R.layout.jdme_item_didi_estimate_info, this);
        icon_list = findViewById(R.id.icon_list);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL, false);
        icon_list.setLayoutManager(layoutManager);
        tv_duration = findViewById(R.id.tv_duration);
        tv_distance = findViewById(R.id.tv_distance);
        tv_price = findViewById(R.id.tv_price);
    }

    @SuppressLint("SetTextI18n")
    public void setData(List<DidiEstimatePriceBean.DidiEstimatePriceListBean> dataList) {
        IconAdapter iconAdapter = new IconAdapter(dataList);
        icon_list.setAdapter(iconAdapter);
        String distance = "-";
        String duration = "-";
        if (dataList != null) {
            for (int i = 0; i < dataList.size(); i++) {
                DidiEstimatePriceBean.DidiEstimatePriceListBean bean = dataList.get(i);
                if (i == 0) {
                    maxPrice = bean.price;
                    minPrice = bean.price;
                    distance = bean.distance;
                    duration = bean.duration;
                } else {
                    if (Double.parseDouble(maxPrice) < Double.parseDouble(bean.price)) {
                        maxPrice = bean.price;
                    }
                    if (Double.parseDouble(minPrice) > Double.parseDouble(bean.price)) {
                        minPrice = bean.price;
                    }
                }
                if ("01".equals(bean.serviceType)) {
                    hasDidiEstimate = true;
                }
            }
            if (TextUtils.isEmpty(minPrice) && TextUtils.isEmpty(maxPrice)) {
                tv_price.setText("--");
            } else if (Double.parseDouble(maxPrice) - Double.parseDouble(minPrice) > 1) {
                tv_price.setText(minPrice + "~" + maxPrice);
            } else {
                tv_price.setText(minPrice);
            }
        }
        tv_duration.setText(getResources().getString(R.string.ne_didi_estimate_duration).replaceAll("%s", duration));
        tv_distance.setText(getResources().getString(R.string.ne_didi_estimate_distance).replaceAll("%s", distance));
    }

    public boolean hasDidiEstimate() {
        return hasDidiEstimate;
    }

    public void setHasDidiEstimate(boolean has) {
        hasDidiEstimate = has;
    }

    class IconAdapter extends RecyclerView.Adapter<IconHolder> {
        private final List<DidiEstimatePriceBean.DidiEstimatePriceListBean> dataList;

        public IconAdapter(List<DidiEstimatePriceBean.DidiEstimatePriceListBean> dataList) {
            this.dataList = dataList != null ? dataList : new ArrayList<DidiEstimatePriceBean.DidiEstimatePriceListBean>();
        }

        @NonNull
        @Override
        public IconHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
            View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_didi_estimate_icon_item, viewGroup, false);
            return new IconHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull IconHolder iconHolder, int index) {
            ImageLoader.load(getContext(), iconHolder.icon, dataList.get(index).icon);
        }

        @Override
        public int getItemCount() {
            return dataList.size();
        }
    }

    private static class IconHolder extends RecyclerView.ViewHolder {

        public ImageView icon;

        public IconHolder(@NonNull View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.iv_icon);
        }
    }
}