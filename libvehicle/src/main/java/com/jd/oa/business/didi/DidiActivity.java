package com.jd.oa.business.didi;

import android.os.Bundle;

import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ResponseParser;

import org.json.JSONArray;
import org.json.JSONObject;

public class DidiActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_function);
        // 隐藏标题
//        getSupportActionBar().hide();
        getSupportActionBar().setTitle(R.string.me_didi_main_title);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        getAgreementStatus();
    }

    private void getAgreementStatus() {
        NetWorkManager.getVehiclePlusAgreementStatus(this, new SimpleRequestCallback<String>(this, false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if (DidiActivity.this != null && !DidiActivity.this.isFinishing())
                    FragmentUtils.replaceWithCommit(DidiActivity.this,
                            DidiMainFragment.class, R.id.me_fragment_content, false, null, false);
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                FragmentUtils.replaceWithCommit(DidiActivity.this,
                        DidiMainFragment.class, R.id.me_fragment_content, false, null, false);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, DidiActivity.this);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String isAgree = jsonObject.optString("isAgree");
                            if ("1".equals(isAgree)) {
                                FragmentUtils.replaceWithCommit(DidiActivity.this,
                                        DidiMainFragment.class, R.id.me_fragment_content, false, null, false);
                            } else {
                                String url = jsonObject.optString("agreementUrl");
                                String type = jsonObject.optString("type");
                                Bundle argument = new Bundle();
                                argument.putString(DidiDescriptionFragment.ARG_AGREEMENT_TYPE, type);
                                argument.putString(DidiDescriptionFragment.ARG_AGREEMENT_URL, url);
                                FragmentUtils.replaceWithCommit(DidiActivity.this,
                                        DidiDescriptionFragment.class, R.id.me_fragment_content, false, argument, false);
                            }
                        } catch (Exception e) {
                            Logger.e(MELogUtil.TAG_TAX, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        FragmentUtils.replaceWithCommit(DidiActivity.this,
                                DidiDescriptionFragment.class, R.id.me_fragment_content, false, null, false);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        });
    }
}
