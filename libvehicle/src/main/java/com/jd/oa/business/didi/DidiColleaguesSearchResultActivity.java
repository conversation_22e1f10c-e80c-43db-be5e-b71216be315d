package com.jd.oa.business.didi;

import android.content.Context;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.utils.ActionBarHelper;

//import jd.cdyjy.jimcore.db.dbtable.TbChatGroups;
//import jd.cdyjy.jimcore.db.dbtable.TbContactInfo;
//import jd.cdyjy.timline.util.ImageLoader;
//import jd.cdyjy.timline.wapper.TimlineWapper;

@Navigation(hidden = false,  displayHome = true)
public class DidiColleaguesSearchResultActivity extends BaseActivity {

    public static final String KEY_EXTRA_LIST = "list";
    public static final String KEY_EXTRA_CONTACT = "contact";
//    private ArrayList<TbContactInfo> mSelectList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_didi_colleagues_search_result);
        //TODO Timline
//        mSelectList = (ArrayList<TbContactInfo>) getIntent().getSerializableExtra(DidiColleaguesSearchActivity.KEY_EXTRA_SELECT_LIST);
        RecyclerView recyclerView = findViewById(R.id.rv_list);
        recyclerView.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
//        ArrayList<TbContactInfo> list = (ArrayList<TbContactInfo>) getIntent().getSerializableExtra(KEY_EXTRA_LIST);
//        SearchResultAdapter adapter = new SearchResultAdapter(list);
//        recyclerView.setAdapter(adapter);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_car_address_collegues_search_result_title);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private class SearchResultAdapter extends RecyclerView.Adapter<SearchResultAdapter.SearchResultVH> {

//        private List<TbContactInfo> mList;
        private Context mContext;

//        public SearchResultAdapter(List<TbContactInfo> mList) {
//            this.mList = mList;
//        }

        @Override
        public SearchResultVH onCreateViewHolder(ViewGroup viewGroup, int i) {
            mContext = viewGroup.getContext();
            View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_didi_colleagues_item_search, viewGroup, false);
            return new SearchResultVH(view);
        }

        @Override
        public void onBindViewHolder(SearchResultVH holder, final int i) {
//            final TbContactInfo info = mList.get(i);
//            holder.head.setTag(info);
//            ImageLoader.getInstance().displaySearchHeadImage(mContext, info.uid, holder.head, info.avatar, jd.cdyjy.timline.R.drawable.ddtl_avatar_personal_normal, true);
//            holder.name.setText(info.realname);
//            holder.value.setText(getUserDes(info));
//            if (hasSelect(info) || isSelf(info)) {
//                holder.add.setVisibility(View.GONE);
//            } else {
//                holder.add.setVisibility(View.VISIBLE);
//            }
//            holder.add.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    Intent intent = new Intent();
//                    intent.putExtra(KEY_EXTRA_CONTACT, info);
//                    setResult(RESULT_OK, intent);
//                    finish();
//                }
//            });
//            holder.uid.setText(getString(R.string.me_car_address_colleagues_search_result_uid, info.uid));
        }
//
//        private boolean isSelf(TbContactInfo info) {
//            return TextUtils.equals(info.uid, PreferenceManager.UserInfo.getUserName());
//        }
//
//        private boolean hasSelect(TbContactInfo info) {
//            if (mSelectList == null) {
//                return false;
//            }
//            for (TbContactInfo item : mSelectList) {
//                if (TextUtils.equals(item.uid, info.uid)) {
//                    return true;
//                }
//            }
//            return false;
//        }
//
//        private String getUserDes(TbContactInfo info) {
//            StringBuffer sb = new StringBuffer();
//            if (!TextUtils.isEmpty(info.orgName)) {
//                sb.append(info.orgName).append(" ");
//                if (!TextUtils.isEmpty(info.position)) {
//                    sb.append(info.position);
//                }
//            } else {
//                if (!TextUtils.isEmpty(info.position)) {
//                    sb.append(info.position).append(" ");
//                }
//            }
//            return sb.toString();
//        }

        @Override
        public int getItemCount() {
//            if (mList != null) {
//                return mList.size();
//            } else {
                return 0;
//            }
        }

        public class SearchResultVH extends RecyclerView.ViewHolder {

            ImageView head;
            TextView name;
            TextView value;
            TextView add;
            TextView uid;

            public SearchResultVH(View itemView) {
                super(itemView);
                head = itemView.findViewById(R.id.jdme_contact_avatar);
                name = itemView.findViewById(R.id.jdme_name);
                value = itemView.findViewById(R.id.jdme_value);
                add = itemView.findViewById(R.id.tv_add);
                uid = itemView.findViewById(R.id.jdme_uid);
            }
        }

    }
}
