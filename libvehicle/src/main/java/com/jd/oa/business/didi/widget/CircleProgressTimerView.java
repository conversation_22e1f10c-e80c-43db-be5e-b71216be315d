package com.jd.oa.business.didi.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.CountDownTimer;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;

import com.jd.oa.business.R;

import java.util.Locale;


/**
 * 累积时间控件，到底最大时间后，停止计时器，
 * 执行回调
 * Created by <PERSON><PERSON><PERSON> on 16/1/14.
 */
public class CircleProgressTimerView extends CircleProgressRefreshView {

    public static final int MINUTES = 60 * 1000;// 分毫秒值
    public static final int HOUR = 60 * MINUTES;// 小时毫秒值
    private final int DEFAULT_TEXT_SIZE = 20;
    private static final String TIME_PATTERN = "00:00";
    /**
     * 开始计数，数值，默认从0开始
     */
    private int mStartNum;
    /**
     * 最大计数，数值，单位 毫秒
     */
    private int mMaxNum;
    private int mTextSize;

    private Paint mPaint;
    private Rect mBound;

    private CountDownTimer mTimer;
    private TimeOverListener mListener;

    public CircleProgressTimerView(Context context) {
        this(context, null);
    }

    public CircleProgressTimerView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CircleProgressTimerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();

        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CircleProgressTimerView, defStyleAttr, 0);
        mStartNum = a.getInt(R.styleable.CircleProgressTimerView_me_tv_startNum, 0);
        mMaxNum = a.getInt(R.styleable.CircleProgressTimerView_me_tv_maxNum, 0);
        mTextSize = a.getDimensionPixelSize(R.styleable.CircleProgressTimerView_me_tv_texSize,
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, DEFAULT_TEXT_SIZE, displayMetrics));
        a.recycle();

        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setColor(mCircleColor);
        mPaint.setTextSize(mTextSize);
        mBound = new Rect();
        mPaint.getTextBounds(TIME_PATTERN, 0, TIME_PATTERN.length(), mBound);
    }

    /**
     * 设置开始计数时间
     *
     * @param startTime
     */
    public void setStartNum(int startTime) {
        this.mStartNum = startTime;
    }

    /**
     * 设置最大计数时间
     */
    public void setMaxNum(int maxNum) {
        this.mMaxNum = maxNum;
    }

    @Override
    public void start() {
        super.start();

        if (mTimer != null) {
            mTimer.cancel();
        }

        mTimer = new CountDownTimer(mMaxNum - mStartNum + 200, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                mStartNum += 1000;
            }

            @Override
            public void onFinish() {
                mStartNum = mMaxNum;
                if (mListener != null) {
                    mListener.onTimeOver();
                }
            }
        };

        mTimer.start();
    }

    @Override
    public void stop() {
        super.stop();
        if (mTimer != null) {
            mTimer.cancel();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mTimer != null) {
            mTimer.cancel();
        }
    }


    /**
     * 设置最大运行时常
     *
     * @param maxTime
     */
    public void setMaxTime(int maxTime) {
        this.mMaxNum = maxTime;
        invalidate();
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        String fmtTime = formatTimeMills(mStartNum);
        // 绘制
        canvas.drawText(fmtTime, (getWidth() - mBound.width()) / 2,
                (getHeight() + mBound.height()) / 2, mPaint);
    }

    /**
     * 返回格式化秒的字符串形式,如：05:22
     *
     * @return
     */
    public static String formatTimeMills(long timeMills) {
        if (timeMills > 0) {
            // 取时分秒
            long hour = timeMills / HOUR;
            long minute = (timeMills - hour * HOUR) / MINUTES;
            long second = timeMills / 1000 % 60;
            return String.format(Locale.CHINESE,"%02d:%02d", minute, second);
        }
        return TIME_PATTERN;
    }

    public void setOnTimeOverListener(TimeOverListener listener) {
        this.mListener = listener;
    }

    public interface TimeOverListener {
        void onTimeOver();
    }
}
