package com.jd.oa.business.didi.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;

import java.util.List;

/**
 * Created by peidongbiao on 2019/4/11
 */
public class CarPoolInvitationColleagueAdapter extends BaseRecyclerAdapter<CarPoolUserStatus, RecyclerView.ViewHolder> {

    public CarPoolInvitationColleagueAdapter(Context context) {
        super(context);
    }

    public CarPoolInvitationColleagueAdapter(Context context, List data) {
        super(context, data);
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_didi_carpool_colleague_bg, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        CarPoolUserStatus status = getItem(position);
        viewHolder.textView.setText(status.getRealName());
    }

    private static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textView;
        public ViewHolder(View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.tv_name);
        }
    }
}
