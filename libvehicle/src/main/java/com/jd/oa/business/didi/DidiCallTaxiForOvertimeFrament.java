package com.jd.oa.business.didi;

import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;

import android.Manifest;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.SwitchCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.caraddress.CarAddressActivity;
import com.jd.oa.business.caraddress.CarAddressFragment;
import com.jd.oa.business.didi.dialog.CallCarUpgradeTipsDialog;
import com.jd.oa.business.didi.dialog.CarPoolTipsDialog;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.didi.model.DidiCallTaxiBean;
import com.jd.oa.business.didi.model.DidiCallTaxiUpgradeBean;
import com.jd.oa.business.didi.model.DidiEstimatePriceBean;
import com.jd.oa.business.didi.model.DidiLocationAddressBean;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.model.UserCommonAddress;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.business.didi.utils.CheckCoordinatesUtil;
import com.jd.oa.business.didi.utils.ConvertUtils;
import com.jd.oa.business.didi.widget.EstimateInfoView;
import com.jd.oa.business.didi.widget.ReservationPopwindow;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Created by qudongshi on 2016/1/11.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiCallTaxiForOvertimeFrament extends DidiBaseFragment implements SosoLocationChangeInterface {
    private static final int MAX_COLLEAGUES_COUNT = 3;
    private static final int REQUEST_CODE_ADD_CAR_ADDRESS = 400;
    private static final int REQUEST_CODE_ADD = 401;
    public Bundle mBundel;

    private View mRootView;

    private TextView mTvFrom; // 出发地
    private TextView mTvTo; // 目的地
    private ClearableEditTxt mCetPhoneNumber;
    private TextView mTvLeaveTime; // 出发时间
    private ClearableEditTxt mCetReason; // 加班原因
    private TextView mTvTipApproval; // 审批提示
    private Button mBtnCallTaxi;
    private LinearLayout mLCarpool;
    private SwitchCompat mSwColleague;
    private ViewGroup mLayoutColleague;
    private RecyclerView mRvColleague;
    private TextView mFromDescText;
    private TextView mToDescText;
    private LinearLayout mLl_reserve;

    private ViewStub mFromMsgViewStub;
    private View mFromMsgLayout;

    private ViewStub mToMsgViewStub;
    private View mToMsgLayout;
    private int mCarAddressSize = 0;

    private boolean isNeedReason = false;//是否临时地址
    private String mIsAppropve; // 0 不需要审批;1 需要审批
    private String mWorkDateType; // 0 工作日加班;1 假日加班第一次;2 假日加班第二次;3 因公外出; 4  无考勤人员
    private String mPhoneNumber; // 电话号码

    private DidiAddressBean mFromAddressBean; // 出发地
    private DidiAddressBean mToAddressBean; // 目的地

    private String mDepartureTime = "";
    private String mDepartureDay = "";

    private boolean mCarAddressChecked = false;

    private ProgressDialog progressDlg;

    private SosoLocationService locationService;

    private DidiEstimatePriceBean priceBean = new DidiEstimatePriceBean();

    private UserCommonAddress mUserCommonAddress;
    private ArrayList<MemberEntityJd> mSelectedColleague = new ArrayList<>();
    //用户当前经纬度
    private String mClat;
    private String mClng;
    private boolean mIsCarPool = false;

    private boolean mOpenCarPool;
    private String mConfirmText;// 自费升舱提示文案

    private String expenseDisableText; // 自费升舱提示文案2
    private DidiColleaguesSelectAdapter mColleagueAdapter;

    private Intent intent;

    private CarPoolTipsDialog mCarPoolTipsDialog;
    private Button mBtnCallTaxiUpgrade;
    private boolean upgradeEnable;
    private EstimateInfoView estimateView;//预估价部分UI
    private boolean needClose = false;
    private LinearLayout ll_expend_disable_tip;

    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_calltaxi_overtime, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_overtime);
        upgradeEnable = DidiUtils.callCarUpgradeEnable();

        // 获取传递的参数
        mBundel = getArguments();
        if (null != mBundel) {
            mIsAppropve = mBundel.getString("isApprove");
            mWorkDateType = mBundel.getString("workDateType");
            mPhoneNumber = mBundel.getString("phoneNumber");
            mOpenCarPool = mBundel.getBoolean("openCarPool");
            mConfirmText = mBundel.getString("confirmText");
            expenseDisableText = mBundel.getString("expenseDisableText");
        }
        initView(mRootView);
        return mRootView;
    }

    private void showUseCarTipDialog() {
        RelativeLayout mLayout = (RelativeLayout) LayoutInflater.from(getContext()).inflate(R.layout.jdme_view_alert_didi_set_car_address, null);
        final Dialog dialog = new Dialog(getContext());
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(mLayout);
        ImageView close = mLayout.findViewById(R.id.iv_close);
        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getEstimate(true, mIsCarPool);
                dialog.dismiss();
            }
        });
        TextView addBtn = mLayout.findViewById(R.id.tv_add);
        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(getActivity(), CarAddressActivity.class);
                startActivityForResult(intent, REQUEST_CODE_ADD_CAR_ADDRESS);
                dialog.dismiss();
            }
        });
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(android.graphics.Color.TRANSPARENT));
        }
        dialog.show();
        DisplayMetrics dm = new DisplayMetrics();
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.8);
        dialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    @Override
    public void onResume() {
        super.onResume();
        EventBusMgr.getInstance().register(this);
    }

    @Override
    public void onPause() {
        super.onPause();
        EventBusMgr.getInstance().unregister(this);//反注册EventBus
        if (needClose && getActivity() != null) {
            getActivity().finish();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        locationService.stopLocation();
        DidiUtils.closeBoard(getActivity(), mCetPhoneNumber);
        DidiUtils.closeBoard(getActivity(), mCetReason);
    }

    /**
     * @param id from/to
     * @return 0 办公地 1 普通
     */
    private int getSelAddressType(int id) {
        int addressType = DidiAddressSearchActivity.ADDRESS_TYPE_BUSSINESS;
        // 目的地、工作日加班 ||  出发地、假日加班第一次 || 目的地、假日加班第二次
        if ((id == R.id.tv_to && "0".equals(mWorkDateType)) || "1".equals(mWorkDateType) || "2".equals(mWorkDateType) || "4".equals(mWorkDateType))
            addressType = DidiAddressSearchActivity.ADDRESS_TYPE_NORMAL;
        return addressType;
    }

    /**
     * 叫车
     */
    private void callTaxi(boolean showTipDialog, final boolean isUpgrade) {
        if (showTipDialog && NClick.isFastDoubleClick()) {
            return;
        }
        final String mTelephone = mCetPhoneNumber.getText().toString();
        if (("1".equals(mIsAppropve) || isNeedReason) && TextUtils.isEmpty(mCetReason.getText().toString().trim())) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_reason));
            return;
        } else if (null == mFromAddressBean) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_from_address));
            return;
        } else if (null == mToAddressBean) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_to_address));
            return;
        } else if (TextUtils.isEmpty(mTelephone) || mTelephone.length() != 11) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_phone_number));
            return;
        } else if (mIsCarPool && CollectionUtil.isEmptyOrNull(mSelectedColleague)) {
            ToastUtils.showToast(R.string.me_car_travel_plz_select_colleague);
            return;
        }


        String addCommonAddress = "0";
        if (mCarAddressChecked) {
            addCommonAddress = String.valueOf(mCarAddressSize + 1);
        } else if (mCarAddressSize == 0) {
            PromptUtils.showConfirmDialog(getActivity(), R.string.me_car_address_use_car_tip_set_car_address, R.string.me_cancel_not_translate, R.string.me_car_address_use_car_tip_set_car_address_btn, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    Intent intent = new Intent(getActivity(), CarAddressActivity.class);
                    startActivityForResult(intent, REQUEST_CODE_ADD_CAR_ADDRESS);
                }
            });
            return;
        }

        String reason = "";
        String tempReason = "";
        if (isNeedReason) {
            tempReason = mCetReason.getText().toString().trim();
        } else {
            reason = mCetReason.getText().toString().trim();
        }
        setCallCarEnable(false);
        if (isUpgrade) {
            SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, true) {
                @Override
                public void onSuccess(final ResponseInfo<String> info) {
                    super.onSuccess(info);
                    ResponseParser parser = new ResponseParser(info.result, getActivity(), false);
                    parser.parse(new ResponseParser.ParseCallback() {

                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            if (TextUtils.isEmpty(jsonObject.toString()))
                                return;
                            DidiCallTaxiUpgradeBean upgradeBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCallTaxiUpgradeBean.class);
                            if (!TextUtils.isEmpty(upgradeBean.didiurl)) {
                                Router.build(upgradeBean.didiurl).go(getActivity());
                                new Handler().postDelayed(new Runnable() {
                                    public void run() {
                                        setCallCarEnable(true);
                                    }
                                }, 500);
                                return;
                            }
                            if (!TextUtils.isEmpty(upgradeBean.url)) {
                                if (getActivity() != null) {
                                    Router.build(upgradeBean.url).go(getActivity());
                                    needClose = true;
                                }
                            }
                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {
                        }

                        @Override
                        public void parseError(String errorMsg) {
                            handleResultParseError(info);
                        }
                    });
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                }
            };
            callback.setNeedTranslate(true);
            NetWorkManager.callCarOrderUpgrade(this, callback, mFromAddressBean.lat, mFromAddressBean.lng, mToAddressBean.lat, mToAddressBean.lng,
                    "", "", mFromAddressBean.displayName, mFromAddressBean.address, mToAddressBean.displayName, mToAddressBean.address,
                    mFromAddressBean.cityCode, mToAddressBean.cityCode, "", mDepartureTime, mDepartureDay, mTelephone, reason,
                    "1", "", priceBean, addCommonAddress, tempReason, priceBean.stationId, mFromAddressBean.hypothetical);
            return;
        }

        //不显示提示框
        if (showTipDialog
                && (priceBean != null && !TextUtils.isEmpty(priceBean.stationId))
                && !
//                PreferenceManager.UserInfo.isDontShowCarPollTipsDialog()
                TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_DONT_SHOW_CAR_POOL_TIPS)
        ) {

            if (mCarPoolTipsDialog == null) {
                mCarPoolTipsDialog = new CarPoolTipsDialog(getContext());
                mCarPoolTipsDialog.setOnCallClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        callTaxi(false, isUpgrade);
                        mCarPoolTipsDialog.dismiss();
                    }
                });
            }
            mCarPoolTipsDialog.show();
            return;
        }

        final SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, true) { // 叫车
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        ToastUtils.showToast(R.string.me_didi_msg_order_send_succsess);
                        DidiCallTaxiBean didiCallTaxiBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCallTaxiBean.class);
                        if (!TextUtils.isEmpty(didiCallTaxiBean.orderId)) {
                            DidiUtils.getOrderDetail(getActivity(), didiCallTaxiBean.orderId, mTelephone, new DidiUtils.IDetailCallback() {
                                @Override
                                public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                                    if (null == didiOrderDetailBean) {
                                        ToastUtils.showToast(R.string.me_exception_order);
                                    } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
                                        ToastUtils.showToast(R.string.me_exception_order);
                                    } else {
                                        String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
                                        if (TextUtils.isEmpty(className)) {
                                            ToastUtils.showToast(R.string.me_exception_order_state);
                                        } else {
                                            Intent intent = new Intent(getActivity(), FunctionActivity.class);
                                            intent.putExtra("function", className);
                                            intent.putExtra("orderDetailBean", didiOrderDetailBean);
                                            startActivityForResult(intent, 300);
                                            getActivity().finish();
                                        }
                                    }
                                    new Handler().postDelayed(new Runnable() {
                                        public void run() {
                                            setCallCarEnable(true);
                                        }
                                    }, 500);
                                    progressDlg.dismiss();
                                }
                            });
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        handleResultParseError(request);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                setCallCarEnable(true);
            }
        };
        callback.setNeedTranslate(false);

        if (!mIsCarPool) {
            if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(mToAddressBean.lat, mToAddressBean.lng)) {
                //目的地地址有误
                MELogUtil.localE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament---- before callCarOrder,mIsCarPool =false, mToAddressBean:" + mToAddressBean.toString());
                MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament---- before callCarOrder,mIsCarPool =false, mToAddressBean:" + mToAddressBean.toString());
            }

            NetWorkManager.callCarOrder(this, callback, mFromAddressBean.lat, mFromAddressBean.lng, mToAddressBean.lat, mToAddressBean.lng,
                    "", "", mFromAddressBean.displayName, mFromAddressBean.address, mToAddressBean.displayName, mToAddressBean.address,
                    mFromAddressBean.cityCode, mToAddressBean.cityCode, "", mDepartureTime, mDepartureDay, mTelephone, reason,
                    "1", "", priceBean, addCommonAddress, tempReason, priceBean.stationId, mFromAddressBean.hypothetical);
            MELogUtil.onlineI(MELogUtil.TAG_TAX, "callTaxi: calCarOrder");
            MELogUtil.localI(MELogUtil.TAG_TAX, "callTaxi: calCarOrder");
            return;
        } else {
            final String users = getCarPoolUsers();
            final String finalAddCommonAddress = addCommonAddress;
            final String finalTempReason = tempReason;
            final String finalReason = reason;
            //检查所选用户是否都有权限
            NetWorkManager.checkCarPoolUser("1", users, new SimpleRequestCallback<String>(getContext()) {

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    if (!isAlive()) return;
                    try {
                        JSONObject object = new JSONObject(info.result);
                        String errorCode = object.optString("errorCode");
                        if ("1".equals(errorCode)) {
                            String errorMsg = object.optString("errorMsg");
                            ToastUtils.showToast(errorMsg);
                            return;
                        }
                        JSONObject content = object.optJSONObject("content");
                        boolean allOk = "1".equals(content.optString("nextStep"));
                        if (!allOk) {
                            //将不符合条件的用户删除。
                            String forbiddenUser = content.optString("forbUser");
                            String forbUserNames = content.optString("forbUserNames");
                            if (!TextUtils.isEmpty(forbiddenUser)) {
                                ToastUtils.showToast(getString(R.string.me_car_pool_remove_forbidden_user, forbiddenUser));
                            }
                            removeForbiddenUsers(forbUserNames);
                            return;
                        }

                        if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(mToAddressBean.lat, mToAddressBean.lng)) {
                            //目的地地址有误
                            MELogUtil.localE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament---- before preCallCarOrderForPool,mIsCarPool =true, mToAddressBean:" + mToAddressBean.toString());
                            MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament---- before preCallCarOrderForPool,mIsCarPool =true, mToAddressBean:" + mToAddressBean.toString());
                            mToAddressBean = CheckCoordinatesUtil.reverseAddressCoordinates(mToAddressBean);
                        }

                        //全部符合用车条件，开始叫车
                        NetWorkManager.preCallCarOrderForPool(this, callback, mFromAddressBean.lat, mFromAddressBean.lng, mToAddressBean.lat, mToAddressBean.lng,
                                "", "", mFromAddressBean.displayName, mFromAddressBean.address, mToAddressBean.displayName, mToAddressBean.address,
                                mFromAddressBean.cityCode, mToAddressBean.cityCode, "", mDepartureTime, mDepartureDay, mTelephone, finalReason,
                                "1", "", priceBean, finalAddCommonAddress, finalTempReason, users);
                    } catch (JSONException e) {
                        e.printStackTrace();
                        ToastUtils.showToast(R.string.me_car_pool_check_user_status_failed);
                    } finally {
                        setCallCarEnable(true);
                    }
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    if (!isAlive()) return;
                    setCallCarEnable(true);
                }
            });
        }
    }

    private void handleResultParseError(ResponseInfo<String> request) {
        try {
            JSONObject jsonObj = new JSONObject(request.result);
            String errorCode = jsonObj.getString("errorCode");
            if ("1".equals(errorCode)) {
                String msg = jsonObj.getString("errorMsg");
                new IosAlertDialog(getContext()).builder()
                        .setMsg(msg)
                        .setPositiveButton(getString(R.string.me_car_travel_i_know), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {

                            }
                        })
                        .show();
                setCallCarEnable(true);
            } else {
                JSONObject contentObj = jsonObj.optJSONObject("content");
                if (contentObj != null) {
                    String isRefresh = contentObj.optString("isRefresh");
                    if (!TextUtils.isEmpty(isRefresh) && "1".equals(isRefresh)) {
                        getEstimate(true, mIsCarPool);
                    }
                }
            }
        } catch (Exception e) {
            setCallCarEnable(true);
        }
    }

    private String getCarPoolUsers() {
        if (CollectionUtil.isEmptyOrNull(mSelectedColleague)) return null;
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < mSelectedColleague.size(); i++) {
            MemberEntityJd memberEntity = mSelectedColleague.get(i);
            stringBuilder.append(memberEntity.mId);
            stringBuilder.append("_");
            stringBuilder.append(memberEntity.mName);
            if (i < mSelectedColleague.size() - 1) {
                stringBuilder.append(",");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 移出不符合条件的用户
     *
     * @param users "fengtingfan,fengtingfan"
     */
    private void removeForbiddenUsers(String users) {
        if (TextUtils.isEmpty(users)) return;
        String[] user = users.split(",");
        if (user.length == 0) return;
        Iterator<MemberEntityJd> iterator = mSelectedColleague.iterator();
        while (iterator.hasNext()) {
            MemberEntityJd entity = iterator.next();
            for (int i = 0; i < user.length; i++) {
                if (user[i].equals(entity.mId)) {
                    iterator.remove();
                    break;
                }
            }
        }
        mColleagueAdapter.notifyDataSetChanged();

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == 200) {
            if (requestCode == 100) {   // from
                mFromAddressBean = (DidiAddressBean) data.getSerializableExtra(DidiAddressSearchActivity.KEY_ADDRESS);
                mTvFrom.setText(mFromAddressBean.displayName);
                mTvFrom.setTextColor(getResources().getColor(R.color.black_252525));
            } else if (requestCode == 200) {    //to
                mToAddressBean = (DidiAddressBean) data.getSerializableExtra(DidiAddressSearchActivity.KEY_ADDRESS);
                if (mToAddressBean != null && !CheckCoordinatesUtil.checkAddressCoordinatesValidity(mToAddressBean.lat, mToAddressBean.lng)) {
                    //目的地地址有误
//                    mToAddressBean = CheckCoordinatesUtil.reverseAddressCoordinates(mToAddressBean);
                    MELogUtil.localE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament---- get mToAddressBean from DidiAddressSearchActivity, mToAddressBean:" + mToAddressBean.toString());
                    MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament---- get mToAddressBean from DidiAddressSearchActivity, mToAddressBean:" + mToAddressBean.toString());
                }

                mTvTo.setText(mToAddressBean.displayName);
                mTvTo.setTextColor(getResources().getColor(R.color.black_252525));
            }
            getEstimate(true, mIsCarPool);
        } else if (requestCode == REQUEST_CODE_ADD_CAR_ADDRESS) {
            checkUserCommonAddress(true, true);
        } else if (requestCode == REQUEST_CODE_ADD) {
            MELogUtil.localI(MELogUtil.TAG_TAX, "onActivityResult: ");
        }
//        else if (resultCode == 0) {
//            getActivity().finish();
//        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 初始化
     *
     * @param mRootView
     */
    private void initView(View mRootView) {

        mTvFrom = mRootView.findViewById(R.id.tv_from); // 出发地
        mTvTo = mRootView.findViewById(R.id.tv_to); // 目的地
        mCetPhoneNumber = mRootView.findViewById(R.id.cet_telephone);
        mTvLeaveTime = mRootView.findViewById(R.id.tv_leave_time); // 出发时间
        mCetReason = mRootView.findViewById(R.id.cet_reason); // 加班原因
        mTvTipApproval = mRootView.findViewById(R.id.tv_tip_approval); // 审批提示
        TextView tv_upgrade_tip = mRootView.findViewById(R.id.tv_upgrade_tip); //自费升舱不可用提示文案
        tv_upgrade_tip.setText(expenseDisableText);
        ll_expend_disable_tip = mRootView.findViewById(R.id.ll_expend_disable_tip);
        mBtnCallTaxi = mRootView.findViewById(R.id.btn_call_taxi);
        mBtnCallTaxiUpgrade = mRootView.findViewById(R.id.btn_call_taxi_upgrade);
        mBtnCallTaxiUpgrade.setVisibility(upgradeEnable ? View.VISIBLE : View.GONE);
        mLCarpool = mRootView.findViewById(R.id.layout_carpool);
        mSwColleague = mRootView.findViewById(R.id.switch_colleague);
        mLayoutColleague = mRootView.findViewById(R.id.layout_colleague);
        mRvColleague = mRootView.findViewById(R.id.rv_colleague_list);
        mLl_reserve = mRootView.findViewById(R.id.ll_reserve);
        estimateView = mRootView.findViewById(R.id.estimateView);

        mTvFrom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), DidiAddressSearchActivity.class);
                intent.putExtra(DidiAddressSearchActivity.KEY_ADDRESS_TPYE, getSelAddressType(R.id.tv_from));
                intent.putExtra(DidiAddressSearchActivity.KEY_REQUEST_CODE, 100);
                intent.putExtra(DidiAddressSearchActivity.KEY_PHONE_NUMBER, mPhoneNumber);
                if (mUserCommonAddress != null && mUserCommonAddress.getAddressList() != null) {
                    intent.putExtra(DidiAddressSearchActivity.KEY_CAR_ADDRESS, mUserCommonAddress.getAddressList());
                }
                startActivityForResult(intent, 100);
            }
        });

        mTvTo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), DidiAddressSearchActivity.class);
                intent.putExtra(DidiAddressSearchActivity.KEY_ADDRESS_TPYE, getSelAddressType(R.id.tv_to));
                intent.putExtra(DidiAddressSearchActivity.KEY_REQUEST_CODE, 200);
                intent.putExtra(DidiAddressSearchActivity.KEY_PHONE_NUMBER, mPhoneNumber);
                if (mUserCommonAddress != null && mUserCommonAddress.getAddressList() != null) {
                    intent.putExtra(DidiAddressSearchActivity.KEY_CAR_ADDRESS, mUserCommonAddress.getAddressList());
                }
                startActivityForResult(intent, 200);
            }
        });

        mBtnCallTaxi.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                callTaxi(true, false);
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_CAR_OVERTIME_CALL);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_overTime_callCar_click, JDMAConstants.mobile_employeeTravel_overTime_callCar_click);
            }
        });

        mBtnCallTaxiUpgrade.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_JDME_CALL_CAR_UPGRADE_TIPS_NO_MORE)) {
                    new CallCarUpgradeTipsDialog(getActivity())
                            .setOnCallClickListener(new CallCarUpgradeTipsDialog.OnConfirmClickListener() {
                                @Override
                                public void onConfirmClick() {
                                    callTaxi(true, true);
                                }
                            }).setTipsContent(mConfirmText).show();
                } else {
                    callTaxi(true, true);
                }
            }
        });

        mLl_reserve.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DidiUtils.closeBoard(getActivity(), mCetPhoneNumber);
                DidiUtils.closeBoard(getActivity(), mCetReason);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_overTime_leaveTime_click, JDMAConstants.mobile_employeeTravel_overTime_leaveTime_click);
                showPopwindow();
            }
        });

        mFromMsgViewStub = mRootView.findViewById(R.id.vs_from);
        mToMsgViewStub = mRootView.findViewById(R.id.vs_to);
        mFromDescText = mRootView.findViewById(R.id.tv_from_desc);
        mToDescText = mRootView.findViewById(R.id.tv_to_desc);
        progressDlg = new ProgressDialog(getActivity());
        progressDlg.setCanceledOnTouchOutside(false);
        progressDlg.setMessage(getActivity().getResources().getString(R.string.me_loading_not_translate));
        locationService = new SosoLocationService(getActivity());

        new Handler().postDelayed(new Runnable() {
            public void run() {
                if (getContext() == null || getActivity() == null) {
                    return;
                }
                PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_location_travel), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        locationService.startLocationWithCheck();
                        locationService.setLocationChangedListener(DidiCallTaxiForOvertimeFrament.this);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        locationService.startLocationWithCheck();
                        locationService.setLocationChangedListener(DidiCallTaxiForOvertimeFrament.this);
                    }
                }, Manifest.permission.ACCESS_FINE_LOCATION);

            }
        }, 500);
        if (!TextUtils.isEmpty(mIsAppropve) && "1".equals(mIsAppropve)) { // 是否需要审批
            mCetReason.setVisibility(View.VISIBLE);
            mTvTipApproval.setText(R.string.me_didi_tip_approval);
        }
        // 手机号
        if (!TextUtils.isEmpty(mPhoneNumber)) {
            mCetPhoneNumber.setText(mPhoneNumber);
            mCetPhoneNumber.setSelection(mCetPhoneNumber.getText().length());
        }

        mCetPhoneNumber.setOnFocusChangeListener(new android.view.View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                } else {
//                    if (TextUtils.isEmpty(mTvEstimatePrice.getText().toString()))
                    getEstimate(true, mIsCarPool);
                }
            }
        });

        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false);
        mColleagueAdapter = new DidiColleaguesSelectAdapter(mSelectedColleague);
        mColleagueAdapter.setLayoutId(R.layout.jdme_item_didi_colleagues_select_call);
        mRvColleague.setLayoutManager(layoutManager);
        mRvColleague.setAdapter(mColleagueAdapter);
        mColleagueAdapter.setAction(new DidiColleaguesSelectAdapter.Action() {
            @Override
            public void onAddBtnClick() {
                MemberListEntityJd entity = new MemberListEntityJd();
                entity.setFrom(TYPE_ADD_MEMBER).setShowConstantFilter(true).setOptionalFilter(mSelectedColleague).setShowSelf(false);
                AppBase.iAppBase.gotoMemberList(getActivity(), REQUEST_CODE_ADD, entity, new Callback<ArrayList<MemberEntityJd>>() {
                    @Override
                    public void onSuccess(final ArrayList<MemberEntityJd> list) {
                        Log.d(TAG, "gotoMemberList onSuccess: ");
                        if (CollectionUtil.isEmptyOrNull(list)) {
                            return;
                        }

                        //不能选择自己
                        Iterator<MemberEntityJd> iterator = list.iterator();
                        while (iterator.hasNext()) {
                            MemberEntityJd memberEntity = iterator.next();
                            if (PreferenceManager.UserInfo.getUserName().equals(memberEntity.mId)) {
                                iterator.remove();
                            }
                        }

                        //只能添加3人
                        mSelectedColleague.clear();
                        if (list.size() > MAX_COLLEAGUES_COUNT) {
                            mSelectedColleague.addAll(list.subList(0, MAX_COLLEAGUES_COUNT));
                            ToastUtils.showToast(R.string.me_car_travel_colleague_exceed_limit_tips);
                        } else {
                            mSelectedColleague.addAll(list);
                        }
                        mColleagueAdapter.notifyDataSetChanged();
                    }

                    @Override
                    public void onFail() {
                    }

                });
            }
        });

        mSwColleague.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                mIsCarPool = isChecked;
                mLayoutColleague.setVisibility(isChecked ? View.VISIBLE : View.GONE);
                getEstimate(true, mIsCarPool);
                if (isChecked) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_overTime_isHasTravelPartnerOpen_click, JDMAConstants.mobile_employeeTravel_overTime_isHasTravelPartnerOpen_click);
                } else {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_overTime_isHasTravelPartnerClose_click, JDMAConstants.mobile_employeeTravel_overTime_isHasTravelPartnerClose_click);
                }
            }
        });

        //是否显示拼车按钮
        mLCarpool.setVisibility(mOpenCarPool ? View.VISIBLE : View.GONE);
    }

    // 选择出发时间
    private void showPopwindow() {
        ReservationPopwindow mPop = new ReservationPopwindow(getActivity(), new ReservationPopwindow.IReserveCallback() {
            @Override
            public void onConfirmCallback(String day, String time) {
                if ("现在".contains(time)) {
                    mTvLeaveTime.setText(R.string.me_didi_now);
                    mDepartureTime = "";
                    mDepartureDay = "";
                } else if ("今天".equals(day)) {
                    mTvLeaveTime.setText(time);
                    mDepartureDay = "01";
                    mDepartureTime = time + ":00";
                    ToastUtils.showToastLong(R.string.me_didi_tip_reserved_order);

                } else {
                    mTvLeaveTime.setText(day + " " + time);
                    mDepartureDay = "02";
                    mDepartureTime = time + ":00";
                    ToastUtils.showToastLong(R.string.me_didi_tip_reserved_order);

                }
                getEstimate(true, mIsCarPool);
            }
        }, ReservationPopwindow.TYPE_OVERTIME);
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
//                backgroundAlpha(1f);
            }
        });

        mPop.showAtLocation(mRootView, Gravity.BOTTOM, 0, 0);
//        backgroundAlpha(0.5f);
    }

//    PopWindow 不修改透明度
//    /**
//     * 设置添加屏幕的背景透明度
//     *
//     * @param bgAlpha
//     */
//    private void backgroundAlpha(float bgAlpha) {
//        WindowManager.LayoutParams lp = getActivity().getWindow().getAttributes();
//        lp.alpha = bgAlpha; //0.0-1.0
//        getActivity().getWindow().setAttributes(lp);
//    }

    @Override
    public void onLocated(String lat, String lng, String name, String cityName) {
        mClat = lat;
        mClng = lng;
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), false, false) { // 获取职场地址
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString())) {
                            return;
                        }
                        DidiLocationAddressBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiLocationAddressBean.class);
                        if (!TextUtils.isEmpty(didiAddressBean.currentAddress.displayName)) {
                            mFromAddressBean = didiAddressBean.currentAddress;
                            mTvFrom.setText(mFromAddressBean.displayName);
                            mTvFrom.setTextColor(getResources().getColor(R.color.black_252525));
                            //获取用户默认地址
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
                checkUserCommonAddress(false, true);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                checkUserCommonAddress(false, true);
            }
        };
        callback.setNeedTranslate(false);
        PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_loading_message_not_translate));
        NetWorkManager.getJobAddressByLocal(this, callback, lat, lng, "");
    }

    @Override
    public void onFailed() {
        ToastUtils.showToast(R.string.me_didi_fail_get_location);
        checkUserCommonAddress(true, true);
    }

    /**
     * 获取预估价
     */
    private void getEstimate(boolean showProgress, boolean isCarPool) {
        String mTelephone = mCetPhoneNumber.getText().toString();
        if (TextUtils.isEmpty(mTelephone) || mTelephone.length() != 11) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_phone_number));
            PromptUtils.removeLoadDialog(getActivity());
            return;
        }
        if (null == mFromAddressBean || null == mToAddressBean || TextUtils.isEmpty(mTelephone)) {
            PromptUtils.removeLoadDialog(getActivity());
            return;
        }
        setCallCarEnable(false);
        if (showProgress) {
            PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_loading_message_not_translate));
        }
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), false, false) { // 获取预估价
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                PromptUtils.removeLoadDialog(getActivity());
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        priceBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiEstimatePriceBean.class);
                        JSONArray estimatePriceList = jsonObject.optJSONArray("estimatePriceList");
                        priceBean.callbackParams = estimatePriceList != null ? estimatePriceList : new JSONArray();
                        setCarAddressMsg(priceBean);
                        if (priceBean.estimatePriceList == null || priceBean.estimatePriceList.isEmpty()) {
                            estimateView.setVisibility(View.GONE);
                            estimateView.setHasDidiEstimate(false);
                            setCallCarEnable(false);
                        } else {
                            estimateView.setVisibility(View.VISIBLE);
                            estimateView.setData(priceBean.estimatePriceList);
                            setCallCarEnable(true);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        estimatePriceFailedUI();
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                estimatePriceFailedUI();
                PromptUtils.removeLoadDialog(getActivity());
            }
        };
        callback.setNeedTranslate(false);
        if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(mToAddressBean.lat, mToAddressBean.lng)) {
            MELogUtil.localE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament ---before jmeMobile/vehicle/getEstimatePrice, mToAddressBean:" + mToAddressBean);
            MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament ---before jmeMobile/vehicle/getEstimatePrice, mToAddressBean:" + mToAddressBean);
            mToAddressBean = CheckCoordinatesUtil.reverseAddressCoordinates(mToAddressBean);
        }
        if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(mFromAddressBean.lat, mFromAddressBean.lng)) {
            MELogUtil.localE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament ---before jmeMobile/vehicle/getEstimatePrice, mFromAddressBean:" + mFromAddressBean);
            MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament ---before jmeMobile/vehicle/getEstimatePrice, mFromAddressBean:" + mFromAddressBean);
            mFromAddressBean = CheckCoordinatesUtil.reverseAddressCoordinates(mFromAddressBean);
        }
        NetWorkManager.getEstimatePrice(null, callback, mFromAddressBean.lat, mFromAddressBean.lng,
                mToAddressBean.lat, mToAddressBean.lng, mFromAddressBean.cityCode, "",
                mDepartureTime, mDepartureDay, mTelephone, mToAddressBean.cityCode, "1",
                isCarPool ? "1" : "0", mClat, mClng);
    }

    /**
     * 获取预估价失败隐藏预估价展示区域 叫车按钮置灰
     */
    private void estimatePriceFailedUI() {
        estimateView.setVisibility(View.GONE);
        setCallCarEnable(false);
    }

    public void checkUserCommonAddress(boolean showProgress, final boolean needRequestEstimate) {
        if (showProgress && isAdded()) {
            String tip = getString(R.string.me_loading_message_not_translate);
            PromptUtils.showLoadDialog(getActivity(), tip);
        }
        NetWorkManager.request(null, Constant.getConstant(NetWorkManager.mReqeustType).API_CHECK_USER_COMMOM_ADDRESS, new SimpleReqCallbackAdapter<>(new AbsReqCallback<UserCommonAddress>(UserCommonAddress.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                ToastUtils.showToast(errorMsg);
                PromptUtils.removeLoadDialog(getActivity());
            }

            @Override
            protected void onSuccess(UserCommonAddress bean, List<UserCommonAddress> tArray, String rawData) {
                super.onSuccess(bean, tArray, rawData);
                initUserCommonAddress(bean, needRequestEstimate);

                if (bean != null && !CheckCoordinatesUtil.checkAddressCoordinatesValidity(bean.getDefaultAddress().getLat(), bean.getDefaultAddress().getLng())) {
                    MELogUtil.localE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament: after jmeMobile/vehicle/checkUserCommonAddress,UserCommonAddress:" + bean.getDefaultAddress().toString());
                    MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "DidiCallTaxiForOvertimeFrament: after jmeMobile/vehicle/checkUserCommonAddress,UserCommonAddress:" + bean.getDefaultAddress().toString());
                }
            }
        }), null);
    }

    private void initUserCommonAddress(UserCommonAddress bean, boolean needRequestEstimate) {
        if (bean != null) {
            mUserCommonAddress = bean;
            DidiAddressBean addressBean = ConvertUtils.convertAddressBean(bean.getDefaultAddress());
            try {
                mCarAddressSize = Integer.parseInt(bean.getAddressSize());
            } catch (NumberFormatException e) {
                mCarAddressSize = 0;
            }
            if (addressBean != null) {
                if (!("1".equals(mWorkDateType) || "2".equals(mWorkDateType))) {
                    //周末第一次加班
                    mToAddressBean = addressBean;
                    mTvTo.setText(mToAddressBean.displayName);
                    mTvTo.setTextColor(getResources().getColor(R.color.black_252525));
                    needRequestEstimate = true;
                }
            }
            if (mCarAddressSize == 0 && !
//                    PreferenceManager.UserInfo.getUseCarSetCarAddressTip()
                    TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_SET_CAR_ADDRESS_TIP)
            ) {
//                PreferenceManager.UserInfo.setUseCarSetCarAddressTip(true);
                TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_SET_CAR_ADDRESS_TIP, true);
                showUseCarTipDialog();
                needRequestEstimate = false;
            }
            if (needRequestEstimate) {
                //获取预估价
                getEstimate(false, mIsCarPool);
            } else {
                PromptUtils.removeLoadDialog(getActivity());
            }
        } else {
            PromptUtils.removeLoadDialog(getActivity());
        }
    }

    public void onEventMainThread(DidiOrderDetailBean orderDetailBean) {
        // 跳转逻辑
        if (null == orderDetailBean) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else if (TextUtils.isEmpty(orderDetailBean.order.orderId)) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else {
            String className = DidiUtils.getRedirectFragmentClassname(orderDetailBean);
            if (TextUtils.isEmpty(className)) {
                ToastUtils.showToast(R.string.me_exception_order_state);
            } else {
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", className);
                intent.putExtra("orderDetailBean", orderDetailBean);
                startActivityForResult(intent, 300);
            }
        }
        DidiPollingUtils.stopPollingService(getActivity());
        progressDlg.dismiss();
        new Handler().postDelayed(new Runnable() {
            public void run() {
                setCallCarEnable(true);
            }
        }, 500);
    }

    public void setCarAddressMsg(final DidiEstimatePriceBean bean) {
        mCarAddressChecked = false;
        if (mFromMsgLayout == null) {
            mFromMsgLayout = mFromMsgViewStub.inflate();
        }
        if (mToMsgLayout == null) {
            mToMsgLayout = mToMsgViewStub.inflate();
        }
        setCallCarEnable(bean.canUse());
        AppCompatCheckBox checkBox;
        TextView mSetCarAddressTextView;
        if (!bean.canUse() || bean.isTempAddress()) {
            mFromDescText.setVisibility(View.GONE);
            mToDescText.setVisibility(View.GONE);
            isNeedReason = true;
            mCetReason.setVisibility(View.VISIBLE);
            if (bean.isFrom()) {
                mFromMsgLayout.setVisibility(View.VISIBLE);
                mToMsgLayout.setVisibility(View.GONE);
                TextView msgTextView = mFromMsgLayout.findViewById(R.id.tv_msg);
                checkBox = mFromMsgLayout.findViewById(R.id.cb_set_car_address);
                mSetCarAddressTextView = mFromMsgLayout.findViewById(R.id.tv_set_car_address);
                msgTextView.setText(bean.showMsg);
            } else {
                mFromMsgLayout.setVisibility(View.GONE);
                mToMsgLayout.setVisibility(View.VISIBLE);
                TextView msgTextView = mToMsgLayout.findViewById(R.id.tv_msg);
                checkBox = mToMsgLayout.findViewById(R.id.cb_set_car_address);
                mSetCarAddressTextView = mToMsgLayout.findViewById(R.id.tv_set_car_address);
                msgTextView.setText(bean.showMsg);
            }
            if (mCarAddressSize >= CarAddressFragment.MAX_ADDRESS_SIZE) {
                mSetCarAddressTextView.setVisibility(View.GONE);
                checkBox.setVisibility(View.GONE);
            } else {
                if (bean.canUse()) {
                    mSetCarAddressTextView.setVisibility(View.VISIBLE);
                    checkBox.setVisibility(View.VISIBLE);
                    mSetCarAddressTextView.setText(getString(R.string.me_car_address_set_checkbox_tip) + (mCarAddressSize + 1));
                } else {
                    mSetCarAddressTextView.setVisibility(View.GONE);
                    checkBox.setVisibility(View.GONE);
                }

            }
            checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    mCarAddressChecked = isChecked;
                    if (!bean.canUse()) {
                        setCallCarEnable(mCarAddressChecked);
                    }
                }
            });
        } else if ("1".equals(mIsAppropve)) {
            mFromMsgLayout.setVisibility(View.GONE);
            mToMsgLayout.setVisibility(View.GONE);
            if (bean.isFrom()) {
                mFromDescText.setVisibility(View.VISIBLE);
                mFromDescText.setText(getString(R.string.me_car_address_title) + bean.addressOrder);
                mToDescText.setVisibility(View.GONE);
            } else {
                mFromDescText.setVisibility(View.GONE);
                mToDescText.setVisibility(View.VISIBLE);
                mToDescText.setText(getString(R.string.me_car_address_title) + bean.addressOrder);
            }
        } else {
            isNeedReason = false;
            mCetReason.setVisibility(View.GONE);
            mFromMsgLayout.setVisibility(View.GONE);
            mToMsgLayout.setVisibility(View.GONE);
            if (bean.isFrom()) {
                mFromDescText.setVisibility(View.VISIBLE);
                mFromDescText.setText(getString(R.string.me_car_address_title) + bean.addressOrder);
                mToDescText.setVisibility(View.GONE);
            } else {
                mFromDescText.setVisibility(View.GONE);
                mToDescText.setVisibility(View.VISIBLE);
                mToDescText.setText(getString(R.string.me_car_address_title) + bean.addressOrder);
            }
        }
    }

    public void setCallCarEnable(boolean enable) {
        mBtnCallTaxi.setEnabled(enable);
        if (upgradeEnable) {
            //无滴滴预估价或同行订单时 显示底部升舱不可用提示文案
            boolean estimateFlag = !estimateView.hasDidiEstimate() && View.VISIBLE == estimateView.getVisibility();
            if (mSwColleague.isChecked() || estimateFlag) {
                mBtnCallTaxiUpgrade.setEnabled(false);
                ll_expend_disable_tip.setVisibility(View.VISIBLE);
            } else {
                mBtnCallTaxiUpgrade.setEnabled(enable);
                ll_expend_disable_tip.setVisibility(View.GONE);
            }
        }
    }
}