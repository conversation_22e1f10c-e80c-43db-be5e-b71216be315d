package com.jd.oa.business.didi.adapter;

import android.graphics.Color;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.ui.recycler.TypeAdapter;

public class AddressSearchResultTypeAdapter extends TypeAdapter<DidiAddressBean, AddressSearchResultTypeAdapter.VH> {

    private OnItemClickListener<DidiAddressBean> mOnItemClickListener;


    public AddressSearchResultTypeAdapter(OnItemClickListener<DidiAddressBean> onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener<DidiAddressBean> onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        View view = inflater.inflate(R.layout.jdme_item_didi_address, viewGroup, false);
        return new VH(view);
    }

    @Override
    protected void onBindViewHolder(final DidiAddressBean bean, VH holder, final int position) {

        SpannableString name = new SpannableString(bean.displayName);
        ForegroundColorSpan span = new ForegroundColorSpan(Color.BLUE);
        holder.span_end = bean.span_end;
        holder.span_start = bean.span_start;
        name.setSpan(span, holder.span_start, holder.span_end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        holder.tv_item.setText(name);
        holder.tv_item2.setText(bean.address);
        String hypothetical = bean.hypothetical;
        if ("1".equals(hypothetical)){
            holder.tv_item3.setVisibility(View.VISIBLE);
        }else{
            holder.tv_item3.setVisibility(View.GONE);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(bean, position);
                }
            }
        });
    }

    public static class VH extends RecyclerView.ViewHolder {

        public TextView tv_item;
        public TextView tv_item2;
        public TextView tv_item3;

        public int span_start;
        public int span_end;

        public VH(View itemView) {
            super(itemView);
            tv_item = itemView.findViewById(R.id.tv_item);
            tv_item2 = itemView.findViewById(R.id.tv_item2);
            tv_item3 = itemView.findViewById(R.id.tv_item3);
        }
    }

}
