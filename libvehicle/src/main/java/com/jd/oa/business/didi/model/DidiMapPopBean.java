package com.jd.oa.business.didi.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by liyao8 on 2018/3/16.
 * 地图上展示windowInfo中的信息
 */

public class DidiMapPopBean implements Parcelable {
    private String type;
    private String content;
    private String content2;
    private String time;

    public DidiMapPopBean(String type, String content, String content2, String time) {
        this.type = type;
        this.content = content;
        this.content2 = content2;
        this.time = time;
    }

    public DidiMapPopBean(String type, String content, String content2) {
        this.type = type;
        this.content = content;
        this.content2 = content2;
    }
    public DidiMapPopBean(String type) {
        this.type = type;
    }
    public DidiMapPopBean(String type, String time) {
        this.type = type;
        this.time = time;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent2() {
        return content2;
    }

    public void setContent2(String content2) {
        this.content2 = content2;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.type);
        dest.writeString(this.content);
        dest.writeString(this.content2);
        dest.writeString(this.time);
    }

    public DidiMapPopBean() {
    }

    protected DidiMapPopBean(Parcel in) {
        this.type = in.readString();
        this.content = in.readString();
        this.content2 = in.readString();
        this.time = in.readString();
    }

    public static final Parcelable.Creator<DidiMapPopBean> CREATOR = new Parcelable.Creator<DidiMapPopBean>() {
        @Override
        public DidiMapPopBean createFromParcel(Parcel source) {
            return new DidiMapPopBean(source);
        }

        @Override
        public DidiMapPopBean[] newArray(int size) {
            return new DidiMapPopBean[size];
        }
    };
}
