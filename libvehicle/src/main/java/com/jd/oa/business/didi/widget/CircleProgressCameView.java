package com.jd.oa.business.didi.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;

import com.jd.oa.business.R;


/**
 * 接驾中 View
 * Created by <PERSON><PERSON><PERSON> on 16/1/15.
 */
public class CircleProgressCameView extends CircleProgressRefreshView {

	private  String UNIT = "米";

	private int mTextSize;
	private int mDistance;

	private Paint mPaint;
	private Rect mBound;

	private Paint mUnitPaint;

	public CircleProgressCameView(Context context) {
		this(context, null);
	}

	public CircleProgressCameView(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}

	public CircleProgressCameView(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();

		TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CircleProgressCameView, defStyleAttr, 0);
		mTextSize = a.getDimensionPixelSize(R.styleable.CircleProgressCameView_me_tv_texSize,
				(int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, 20, displayMetrics));
		mDistance = a.getInt(R.styleable.CircleProgressCameView_me_tv_distance, 0);

		a.recycle();

		// 不显示球与波纹效果
		setShowWave(false);
		setShowBall(false);
		mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
		mPaint.setColor(mCircleColor);
		mPaint.setTextSize(mTextSize);
		mBound = new Rect();

		mUnitPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
		mUnitPaint.setColor(mCircleColor);
		UNIT = getContext().getString(R.string.me_metre_distance);
	}

	/**
	 * 设置距离
	 *
	 * @param distance
	 */
	public void setDistance(int distance) {
		this.mDistance = distance;
		invalidate();
	}

	@Override
	protected void onDraw(Canvas canvas) {
		super.onDraw(canvas);

		String distance = String.valueOf(mDistance);
		mPaint.getTextBounds(distance, 0, distance.length(), mBound);

		int disWidth = mBound.width();
		int disHeight = mBound.height();

		int unitTextSize = mTextSize / 2 + 2;

		mPaint.setTextSize(unitTextSize);
		float unitWidth = mPaint.measureText(UNIT);

		mPaint.setTextSize(mTextSize);

		// 画距离文字
		canvas.drawText(distance, (getWidth() - disWidth - unitWidth) / 2, (getHeight() + disHeight) / 2, mPaint);

		// 画单位
		mUnitPaint.setTextSize(unitTextSize);
		canvas.drawText(UNIT, (getWidth() + disWidth - unitWidth / 2) / 2, (getHeight() + disHeight) / 2, mUnitPaint);
	}
}
