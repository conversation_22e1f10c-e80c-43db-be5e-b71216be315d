
package com.jd.oa.business.didi.widget;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.AutoCompleteTextView;

import com.jd.oa.business.R;


/**
 * 设置清除按钮样式,修改 imgDisable imgEnable
 */
public class ClearableAutoCompleteTextView extends AutoCompleteTextView {

    private final Context mContext;
    private Drawable imgDisable;
    private Drawable imgEnable;

    public ClearableAutoCompleteTextView(Context context) {
        this(context, null);
    }

    public ClearableAutoCompleteTextView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mContext = context;
        init();
    }

    public ClearableAutoCompleteTextView(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.editTextStyle);
    }

    private void init() {

        imgDisable = null;// mContext.getResources().getDrawable(R.drawable.pop_clear);
        imgEnable = mContext.getResources().getDrawable(R.drawable.jdme_icon_delete);

        addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                setDrawable();
            }
        });
        setDrawable();
    }

    @Override
    protected void onFocusChanged(boolean focused, int direction, Rect previouslyFocusedRect) {
        setDrawable();
        super.onFocusChanged(focused, direction, previouslyFocusedRect);
    }

    // 设置删除图片
    private void setDrawable() {
        if (length() < 1 || !isFocused()) {
            setCompoundDrawablesWithIntrinsicBounds(null, null, imgDisable, null);
        } else {
            setCompoundDrawablesWithIntrinsicBounds(null, null, imgEnable, null);
        }
    }

    // 处理删除事件
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (imgEnable != null && event.getAction() == MotionEvent.ACTION_UP) {

            int eventX = (int) event.getX();
            int eventY = (int) event.getY();

            Rect editRect = new Rect();
            editRect.top = 0;
            editRect.bottom = getHeight();
            editRect.right = getWidth();
            editRect.left = editRect.right - getPaddingRight() - imgEnable.getIntrinsicWidth();

            if (editRect.contains(eventX, eventY)) {
                setText("");
            }
        }
        return super.onTouchEvent(event);
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
    }

}
