package com.jd.oa.business.didi;

import android.animation.ValueAnimator;
import android.app.ProgressDialog;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiMapPopBean;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.widget.CircleProgressTimerView;
import com.jd.oa.business.didi.widget.MyInfoWindowAdapter;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2App;
import com.tencent.map.geolocation.TencentLocation;
import com.tencent.map.geolocation.TencentLocationListener;
import com.tencent.map.geolocation.TencentLocationManager;
import com.tencent.map.geolocation.TencentLocationRequest;
import com.tencent.mapsdk.raster.model.BitmapDescriptorFactory;
import com.tencent.mapsdk.raster.model.Circle;
import com.tencent.mapsdk.raster.model.CircleOptions;
import com.tencent.mapsdk.raster.model.LatLng;
import com.tencent.mapsdk.raster.model.Marker;
import com.tencent.mapsdk.raster.model.MarkerOptions;
import com.tencent.tencentmap.mapsdk.map.CameraUpdateFactory;
import com.tencent.tencentmap.mapsdk.map.MapView;
import com.tencent.tencentmap.mapsdk.map.TencentMap;
import com.tuyenmonkey.textdecorator.TextDecorator;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Created by qudongshi on 2016/1/15.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderDetailWaitReplyFragment extends DidiBaseFragment implements TencentLocationListener, TencentMap.OnMarkerClickListener {

    private View mRootView;

    private MapView mapView;
    private TextView mTvNotice;
    private TextView mTvCancel;
    private LinearLayout mLayoutCancel;
    private FrameLayout mLayoutCantCancel;
    private LinearLayout mLayoutTip;
    private TextView mTvTips;

    private String mNotice;
    private int pollingTime = 30;

    private DidiOrderDetailBean mDidiOrderDetailBean;

    private IosAlertDialog iosAlertDialogDialog;

    private boolean isCancel = false;
    private boolean isRecall = false;
    private TencentLocationManager locationManager;
    private TencentLocationRequest locationRequest;
    private Marker myLocation;
    private Circle accuracy;
    private TencentMap tencentMap;
    private float lastX;
    private MyOrientationListener myOrientationListener;
    private Marker beginLocation;
    private MyInfoWindowAdapter infoWindowAdapter;
    private long startTime;
    private CountDownTimer countDownTimer;
    private View mDialogShuttleBus;
    private AlertDialog alertDialogShuttleBus;
    private TextView shuttleHint;
    private String shuttleBusTip = "客户端写死测试：您前面一共340人，预计等待时间1小时10分钟，为节省您的等待时间，建议您乘坐 XX 停车场摆渡车到其他地点打车。";
    private String shuttleBusLink = DeepLink.rnOld("201705230039", "params={\"path\":\"virtual-bus-list\",\"flat\": \"39.78772\",\"flng\": \"116.56225\"}");
    private String isShow;//是否弹出去乘坐摆渡车的提示框，1弹出，0不弹出

    private ProgressDialog progressDialog;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_wait_reply, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_wait_reply);

        mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        initView();
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
        mapView.onResume();
        checkOrderStatus(false);
//todo 可能会造成内存泄漏
        countDownTimer = new CountDownTimer(Long.MAX_VALUE, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                startTime += 1000;
                if (beginLocation != null) {
                    setWaitTime(startTime);
                }
            }

            @Override
            public void onFinish() {
                //startTime = 0;
            }
        };
        countDownTimer.start();
        EventBusMgr.getInstance().register(this);
        // 启动轮询
        DidiPollingUtils.startPollingService(AppBase.getAppContext(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, pollingTime, true);
        myOrientationListener.start();
//        tencentMap.setZoom(16);

    }

    @Override
    public void onPause() {
        super.onPause();
        mapView.onPause();
        EventBusMgr.getInstance().unregister(this);//反注册EventBus

    }

    @Override
    public void onStop() {
        super.onStop();
        mapView.onStop();
        myOrientationListener.stop();
        countDownTimer.cancel();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mapView.onDestroyView();
        DidiPollingUtils.stopPollingService(getActivity());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mapView.onDestroy();
    }

    /**
     * 设置等待时间
     *
     * @param mills
     */
    private void setWaitTime(long mills) {
        if (myLocation != null) {
            TextView tvTime = infoWindowAdapter.getInfoWindow(beginLocation).findViewById(R.id.tvTime);
            tvTime.setText(CircleProgressTimerView.formatTimeMills(mills));
        }

    }

    /**
     * 设置订单pop的提示文字
     */
    private void setPopContent() {
        //排队订单
        if (mDidiOrderDetailBean != null && myLocation != null) {
            TextView tvContent = infoWindowAdapter.getInfoWindow(beginLocation).findViewById(R.id.tvContent);
            //todo 'boolean java.lang.String.equals(java.lang.Object)' on a null object reference
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.isLineup) && mDidiOrderDetailBean.order.isLineup.equals("1")) {
                //排队订单
//                String str = getString(R.string.me_didi_car_line_up, mDidiOrderDetailBean.order.ranking, mDidiOrderDetailBean.order.waitTime);
                try {
                    if (mDidiOrderDetailBean.order.ranking.equals(mDidiOrderDetailBean.order.waitTime) &&
                            mDidiOrderDetailBean.order.lineTip.contains(mDidiOrderDetailBean.order.ranking)) {
                        int firstIndex = mDidiOrderDetailBean.order.lineTip.indexOf(mDidiOrderDetailBean.order.waitTime);
                        int firstFinishIndex = firstIndex + mDidiOrderDetailBean.order.waitTime.length();
                        int secondIndex = mDidiOrderDetailBean.order.lineTip.lastIndexOf(mDidiOrderDetailBean.order.ranking);
                        int secondFinishIndex = secondIndex + mDidiOrderDetailBean.order.ranking.length();
                        TextDecorator.decorate(tvContent, mDidiOrderDetailBean.order.lineTip)
                                .setTextColor(R.color.jdme_color_orange, firstIndex, firstFinishIndex)
                                .setTextStyle(Typeface.BOLD, firstIndex, firstFinishIndex)
                                .setTextColor(R.color.jdme_color_orange, secondIndex, secondFinishIndex)
                                .setTextStyle(Typeface.BOLD, secondIndex, secondFinishIndex)
                                .build();
                    } else {
                        TextDecorator.decorate(tvContent, mDidiOrderDetailBean.order.lineTip)
                                .setTextColor(R.color.jdme_color_orange, mDidiOrderDetailBean.order.ranking, mDidiOrderDetailBean.order.waitTime)
                                .setTextStyle(Typeface.BOLD, mDidiOrderDetailBean.order.ranking, mDidiOrderDetailBean.order.waitTime)
                                .build();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    tvContent.setText(R.string.me_didi_car_searching);
                }
            } else if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.isLineup) && mDidiOrderDetailBean.order.isLineup.equals("0")) {
                //不排队订单
                tvContent.setText(R.string.me_didi_car_searching);
            } else {
                //isLineup 返回为空时
                tvContent.setText("");
            }
        }

    }

    private void initView() {
        mapView = mRootView.findViewById(R.id.mapView);
        mTvNotice = mRootView.findViewById(R.id.tv_notice);
        mTvCancel = mRootView.findViewById(R.id.tv_cancel);
        mLayoutCancel = mRootView.findViewById(R.id.ll_cancel);
        mLayoutCantCancel = mRootView.findViewById(R.id.layout_cant_cancel);
        mLayoutTip = mRootView.findViewById(R.id.ll_tip);
        mTvTips = mRootView.findViewById(R.id.tv_tips);
        mLayoutCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doCancel();
            }
        });
        String tips = mDidiOrderDetailBean.drivingShowTip;
        mLayoutTip.setVisibility(TextUtils.isEmpty(tips) ? View.GONE : View.VISIBLE);
        mTvTips.setText(tips);
        iosAlertDialogDialog = new IosAlertDialog(getActivity()).builder().setMsg(getString(R.string.me_order_time_out))
                .setPositiveButton(getString(R.string.me_didi_menu_cancel), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        cancelCallTaxi();
                    }
                });
        iosAlertDialogDialog.setCancelable(false);
        //加载摆渡车对话框
        mDialogShuttleBus = LayoutInflater.from(getContext()).inflate(R.layout.jdme_view_dialog_shuttle_bus, null, false);
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setView(mDialogShuttleBus);
        builder.setCancelable(false);
        alertDialogShuttleBus = builder.create();
        mDialogShuttleBus.findViewById(R.id.tv_shuttle_bus_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialogShuttleBus.dismiss();
            }
        });
        mDialogShuttleBus.findViewById(R.id.tv_shuttle_bus_check).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(shuttleBusLink).go(Utils2App.getApp(), new RouteNotFoundCallback(Utils2App.getApp()));
                alertDialogShuttleBus.dismiss();
            }
        });
        shuttleHint = mDialogShuttleBus.findViewById(R.id.tv_shuttle_bus_hint);

        if (mDidiOrderDetailBean.order.didiOrderStatus.equals("300")) {
            if (StringUtils.convertToInt(mDidiOrderDetailBean.order.pollingTime) > 0)
                pollingTime = StringUtils.convertToInt(mDidiOrderDetailBean.order.pollingTime);
        } else if (mDidiOrderDetailBean.order.didiOrderStatus.equals("311")) {
            iosAlertDialogDialog.show();
        }
        mNotice = getResources().getString(R.string.me_didi_wait_reply_notice);

        //初始化地图
        tencentMap = mapView.getMap();
        //设置自定义pop的adapter
        infoWindowAdapter = new MyInfoWindowAdapter();
        tencentMap.setInfoWindowAdapter(infoWindowAdapter);
        tencentMap.setOnMarkerClickListener(this);

        locationManager = TencentLocationManager.getInstance(getActivity());
        locationRequest = TencentLocationRequest.create();
        locationRequest.setRequestLevel(TencentLocationRequest.REQUEST_LEVEL_GEO);
        bindListener();

        //开启方向传感器
        myOrientationListener = new MyOrientationListener(getActivity());
        myOrientationListener.setOnOrientationListener(new MyOrientationListener.OnOrientationListener() {
            @Override
            public void onOrientationChanged(float x) {
                //UI原始箭头图标是和X轴平行的,加上偏移量纠正箭头指向
                lastX = x + 55;
                if (myLocation != null) {
                    myLocation.setRotation(lastX);
                }
            }
        });
        if (mDidiOrderDetailBean.order.isTransfer.equals("1")) {
            ActionBarHelper.getActionBar(getActivity()).setTitle(R.string.me_didi_car_transfer_title);
        } else {
            ActionBarHelper.getActionBar(getActivity()).setTitle(R.string.me_didi_title_wait_reply);
        }
        //取消用车
        if (DidiUtils.isCallCarUser(mDidiOrderDetailBean)) {
            mLayoutCancel.setVisibility(View.VISIBLE);
            mLayoutCantCancel.setVisibility(View.INVISIBLE);
        } else {
            mLayoutCancel.setVisibility(View.INVISIBLE);
            mLayoutCantCancel.setVisibility(View.VISIBLE);
        }
    }

    protected void bindListener() {
        int error = locationManager.requestLocationUpdates(
                locationRequest, this);
    }

    private boolean hasSetCenter = false;

    @Override
    public void onLocationChanged(TencentLocation tencentLocation, int errorCode, String errorMsg) {
        if (errorCode == TencentLocation.ERROR_OK) {
            LatLng latLng = new LatLng(tencentLocation.getLatitude(), tencentLocation.getLongitude());
            LatLng latLngStart = null;
            if (mDidiOrderDetailBean != null &&
                    !TextUtils.isEmpty(mDidiOrderDetailBean.order.flat) &&
                    !TextUtils.isEmpty(mDidiOrderDetailBean.order.flng)) {
                latLngStart = new LatLng(Double.parseDouble(mDidiOrderDetailBean.order.flat), Double.parseDouble(mDidiOrderDetailBean.order.flng));
            }


            if (!hasSetCenter) {
                tencentMap.setCenter(latLng);//设置当前位置为地图中心点
                hasSetCenter = true;
            }
            //添加我的位置
            if (myLocation == null) {
                myLocation = this.tencentMap.addMarker(new MarkerOptions().
                        position(latLng).
                        icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_car_map_me)).
                        anchor(0.5f, 0.5f));
            }
            //添加起点标志
            if (beginLocation == null) {
                beginLocation = tencentMap.addMarker(new MarkerOptions().
                        position(latLngStart == null ? latLng : latLngStart).
                        icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_car_begin)).
                        anchor(0.5f, 1f));
                beginLocation.setTag(new DidiMapPopBean(MyInfoWindowAdapter.TIME_COUNT, ""));
                beginLocation.showInfoWindow();
                setPopContent();
            }

            //添加一个扩大渐变的圆
            if (accuracy == null) {
                accuracy = this.tencentMap.addCircle(new CircleOptions().
                        center(latLng).
//                        radius((double)tencentLocation.getAccuracy()).
        fillColor(0x440000ff).
                                strokeWidth(10f).strokeColor(0x550000ff));
                ValueAnimator valueAnimator = ValueAnimator.ofInt(0x44, 0);
                valueAnimator.setDuration(3000);
                valueAnimator.setRepeatCount(-1);
                valueAnimator.setRepeatMode(ValueAnimator.RESTART);
                valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator valueAnimator) {
                        float newRadius = (mapView.getWidth()) * valueAnimator.getAnimatedFraction();
                        accuracy.setRadius(newRadius);
                        int value = (int) valueAnimator.getAnimatedValue();
                        String hexString = String.valueOf(value) + "0000ff";
                        accuracy.setFillColor(Integer.parseInt(hexString, 16));
                        accuracy.setStrokeWidth(20f);
                        accuracy.setStrokeColor(0x000000);
                    }
                });
                valueAnimator.start();
            }
            //定位改变时同时修改自己和和圆的位置
//            accuracy.setCenter(latLng);
//            myLocation.setPosition(latLng);
            tencentMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 16));
        } else {
            Log.e("location", "location failed:" + errorMsg);
        }
    }


    @Override
    public void onStatusUpdate(String s, int i, String s1) {
        String desc = "";
        switch (i) {
            case STATUS_DENIED:
                desc = "权限被禁止";
                break;
            case STATUS_DISABLED:
                desc = "模块关闭";
                break;
            case STATUS_ENABLED:
                desc = "模块开启";
                break;
            case STATUS_GPS_AVAILABLE:
                desc = "GPS可用，代表GPS开关打开，且搜星定位成功";
                //通过方向传感器获取的值更新箭头的方向
                break;
            case STATUS_GPS_UNAVAILABLE:
                desc = "GPS不可用，可能 gps 权限被禁止或无法成功搜星";
                break;
//            case STATUS_LOCATION_SWITCH_OFF:
//                desc = "位置信息开关关闭，在android M系统中，此时禁止进行wifi扫描";
//                break;
            case STATUS_UNKNOWN:
                break;
        }
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == 300) {
            if (isAlive() && getActivity() != null) {
                //  填写取消订单的原因后 关闭叫车页面
                getActivity().setResult(300);
                getActivity().finish();
            }
        }
    }

    /**
     * 取消叫车
     */
    private void cancelCallTaxi() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) { // 取消叫车
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        Intent intent = new Intent(getActivity(), DidiCancelOrderResonActivity.class);
                        intent.putExtra("orderId", mDidiOrderDetailBean.order.orderId);
                        //这个requestCode 100 我怀疑是随手写的
                        //不用怀疑 这里肯定是随手写的
                        getActivity().startActivityForResult(intent, 100);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.cancelOrder(this, callback, mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, "1");
    }

    private void reCallTaxi() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) { // 重新叫车
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        isRecall = true;
                        DidiPollingUtils.startPollingService(AppBase.getAppContext(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, pollingTime, true);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.reCallCarOrder(this, callback, mDidiOrderDetailBean.order.orderId, "", mDidiOrderDetailBean.order.phoneNumber);
    }

    public void checkOrderStatus(boolean cancel) {
        this.isCancel = cancel;
        if (progressDialog == null) {
            progressDialog = new ProgressDialog(getContext());
            //progressDialog.setMessage(context.getResources().getString(resId));
            progressDialog.setCancelable(true);
            progressDialog.setCanceledOnTouchOutside(true);
        }
        progressDialog.show();
        DidiUtils.getOrderDetail(getActivity(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, new DidiUtils.IDetailCallback() {


            @Override
            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                if (null == didiOrderDetailBean) {
                    ToastUtils.showToast(R.string.me_exception_order);
                } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
                    ToastUtils.showToast(R.string.me_exception_order);
                } else {
                    switch (didiOrderDetailBean.order.didiOrderStatus) {
                        case DidiUtils.DIDI_STATE_WAIT_RESPONSE: // 同状态
//                            mTvNotice.setText(mNotice.replace("--", didiOrderDetailBean.order.driverNum));
                            if (isRecall) {
                                isRecall = false;
                            }
                            mDidiOrderDetailBean = didiOrderDetailBean;
//                                mCptvWaitReply.setStartNum(coverTimerToSecond(didiOrderDetailBean.order.orderIngTime) * 1000);
                            startTime = coverTimerToSecond(didiOrderDetailBean.order.orderIngTime) * 1000;
                            setWaitTime(startTime);
                            setPopContent();

                            if (mDidiOrderDetailBean.shuttleBusTip != null && mDidiOrderDetailBean.shuttleBusTip.length() > 0) {
                                shuttleBusTip = mDidiOrderDetailBean.shuttleBusTip;
                            }
                            if (mDidiOrderDetailBean.shuttleBusLink != null && mDidiOrderDetailBean.shuttleBusLink.length() > 0) {
                                shuttleBusLink = mDidiOrderDetailBean.shuttleBusLink;
                            }

                            if (!isCancel) {
                                if (mDidiOrderDetailBean.isShow != null && mDidiOrderDetailBean.isShow.length() > 0) {
                                    isShow = mDidiOrderDetailBean.isShow;
                                }
                                if ("1".equals(isShow)) {
                                    alertDialogShuttleBus.show();
                                    shuttleHint.setText(shuttleBusTip);
                                }
                            }

                            if (isCancel)
                                cancelCallTaxi();
//                            if ("1".equals(didiOrderDetailBean.order.isComplainted))
//                                mIbtnOmplaints.setBackgroundResource(R.drawable.jdme_didi_icon_complaints_disable);
                            break;
//                        case DidiUtils.DIDI_STATE_OVERTIME:
//                            if (!iosAlertDialogDialog.isShowing())
//                                iosAlertDialogDialog.show();
//                            break;
                        default:
                            String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
                            if (TextUtils.isEmpty(className)) {
                                ToastUtils.showToast(R.string.me_exception_order_state);
                            } else {
                                Bundle bundle = new Bundle();
                                bundle.putBoolean("isCancel", isCancel);
                                bundle.putSerializable("orderDetailBean", didiOrderDetailBean);
                                try {
                                    FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content, false, bundle, false);
                                } catch (ClassNotFoundException e) {
                                    e.printStackTrace();
                                }
                            }
                            break;
                    }
                }
                if (progressDialog != null) {
                    progressDialog.dismiss();
                }
            }

            @Override
            void onFailure(HttpException exception, String info) {
                if (progressDialog != null) {
                    progressDialog.dismiss();
                }
            }
        }, false);
    }

    private void doCancel() {
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_WAIT_REPLY_CANCEL);
        JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_waitResponse_cancel_click, JDMAConstants.mobile_employeeTravel_waitResponse_cancel_click);
        String notice;
//        if (mDidiOrderDetailBean.order.isLineup.equals("1")) {
        if (TextUtils.equals(mDidiOrderDetailBean.order.isLineup, "1")) {
            notice = getString(R.string.me_didi_car_line_up_cancel_notice, mDidiOrderDetailBean.order.ranking);
        } else {
            notice = getString(R.string.me_are_you_cancel_car);
        }
        IosAlertDialog iosAlertDialogDialog = new IosAlertDialog(getActivity()).builder().setMsg(notice)
                .setNegativeButton(getString(R.string.me_car_contiune), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                    }
                })
                .setPositiveButton(getString(R.string.me_didi_menu_cancel), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        checkOrderStatus(true);
                    }
                });
        iosAlertDialogDialog.setCancelable(true);
        iosAlertDialogDialog.show();
    }

    public int coverTimerToSecond(String timer) {
        int second = 0;
        //todo 'int java.lang.String.indexOf(java.lang.String)' on a null object reference
        if (
                !TextUtils.isEmpty(timer) &&
                        timer.indexOf(":") > 0) {
            String[] tmp = timer.split(":");
            second = StringUtils.convertToInt(tmp[0]) * 60 + StringUtils.convertToInt(tmp[1]);
        }
        return second;
    }

    /**
     * eventBus 接收方法 订单状态变化时触发
     *
     * @param orderDetailBean
     */
    public void onEventMainThread(DidiOrderDetailBean orderDetailBean) {
        // 跳转逻辑
        if (null == orderDetailBean) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else if (TextUtils.isEmpty(orderDetailBean.order.orderId)) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else {
            mDidiOrderDetailBean = orderDetailBean;
            if (!TextUtils.isEmpty(orderDetailBean.order.reassignMeOrderNo)) {
                DidiPollingUtils.stopPollingService(getActivity());
                DidiPollingUtils.startPollingService(AppBase.getAppContext(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, pollingTime, true);
                return;
            }
            switch (orderDetailBean.order.didiOrderStatus) {
                case DidiUtils.DIDI_STATE_WAIT_RESPONSE:
                    if (isRecall) {
                        isRecall = false;
                    }
                    // 2018/2/28 leo 从后台获取数据 更新倒计时和排队信息等
                    startTime = coverTimerToSecond(orderDetailBean.order.orderIngTime) * 1000;
                    setWaitTime(startTime);
                    setPopContent();
                    if (isCancel)
                        cancelCallTaxi();
                    break;
                case DidiUtils.DIDI_STATE_OVERTIME:
                    if (!iosAlertDialogDialog.isShowing())
                        iosAlertDialogDialog.show();
                    break;
                default:
                    String className = DidiUtils.getRedirectFragmentClassname(orderDetailBean);
                    if (TextUtils.isEmpty(className)) {
                        ToastUtils.showToast(R.string.me_exception_order_state);
                    } else {
                        Bundle bundle = new Bundle();
                        bundle.putBoolean("isCancel", isCancel);
                        bundle.putSerializable("orderDetailBean", orderDetailBean);
                        try {
                            FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content, false, bundle, false);
                        } catch (ClassNotFoundException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
            }
        }
    }

    @Override
    public boolean onMarkerClick(Marker marker) {
        //屏蔽地图覆盖物的点击时间
        return true;
    }
}
