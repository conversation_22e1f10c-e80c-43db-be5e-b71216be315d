package com.jd.oa.business.didi;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.RequestType;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ResponseParser;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Created by qudongshi on 2017/5/22.
 */
public class DidiFragment extends BaseFragment {

    private View mRootView;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        NetWorkManager.mReqeustType = RequestType.GATEWAY;

        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_activity_function, container, false);
            getAgreementStatus();

        }
        return mRootView;
    }

    private void getAgreementStatus() {
        NetWorkManager.getVehiclePlusAgreementStatus(this, new SimpleRequestCallback<String>(getActivity(), false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                FragmentUtils.replaceWithCommit(getActivity(),
                        DidiMainFragment.class, R.id.me_fragment_content, false, getArguments(), false);
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                FragmentUtils.replaceWithCommit(getActivity(),
                        DidiMainFragment.class, R.id.me_fragment_content, false, getArguments(), false);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String isAgree = jsonObject.optString("isAgree");
                            if ("1".equals(isAgree)) {
                                FragmentUtils.replaceWithCommit(getActivity(),
                                        DidiMainFragment.class, R.id.me_fragment_content, false, getArguments(), false);
                            } else {
                                String url = jsonObject.optString("agreementUrl");
                                String type = jsonObject.optString("type");
                                Bundle argument = new Bundle();
                                argument.putString(DidiDescriptionFragment.ARG_AGREEMENT_TYPE, type);
                                argument.putString(DidiDescriptionFragment.ARG_AGREEMENT_URL, url);
                                FragmentUtils.replaceWithCommit(getActivity(),
                                        DidiDescriptionFragment.class, R.id.me_fragment_content, false, argument, false);
                            }
                        } catch (Exception e) {
                            Logger.d(MELogUtil.TAG_TAX, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        FragmentUtils.replaceWithCommit(getActivity(),
                                DidiDescriptionFragment.class, R.id.me_fragment_content, false, null, false);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        });
    }
}
