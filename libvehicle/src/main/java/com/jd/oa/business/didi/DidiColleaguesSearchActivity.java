package com.jd.oa.business.didi;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;

@Navigation(hidden = false, displayHome = true)
public class DidiColleaguesSearchActivity extends BaseActivity {

    private static final int REQUEST_CODE_SEARCH_RESULT = 100;
    public static final String KEY_EXTRA_SELECT_LIST = "select_list";
    private EditText mKeyEditText;
//    private ArrayList<TbContactInfo> mSelectList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_didi_colleagues_search);
        mKeyEditText = findViewById(R.id.et_key);
//        mSelectList = (ArrayList<TbContactInfo>) getIntent().getSerializableExtra(KEY_EXTRA_SELECT_LIST);
        mKeyEditText.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    performSearch();
                    return true;
                }
                return false;
            }
        });
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_car_address_collegues_search_title);
    }

    private void performSearch() {
        String key = mKeyEditText.getText().toString();
        if (!TextUtils.isEmpty(key)) {
            PromptUtils.showLoadDialog(this, getString(R.string.me_loading_message_not_translate), false);
            //TODO Timline
//            TimlineWapper.searchOnline(key,-1, new TimlineWapper.IOnlineSearchResponseListener() {
//                @Override
//                public void onResponse(List<TbContactInfo> list, List<TbChatGroups> list1) {
//                    PromptUtils.removeLoadDialog(DidiColleaguesSearchActivity.this);
//                    if (list != null && list.size() > 0) {
//                        ArrayList<TbContactInfo> arrayList = new ArrayList<>(list);
//                        Intent intent = new Intent(DidiColleaguesSearchActivity.this, DidiColleaguesSearchResultActivity.class);
//                        intent.putExtra(DidiColleaguesSearchResultActivity.KEY_EXTRA_LIST, arrayList);
//                        intent.putExtra(KEY_EXTRA_SELECT_LIST, mSelectList);
//                        startActivityForResult(intent, REQUEST_CODE_SEARCH_RESULT);
//                    } else {
//                        ToastUtils.showToast(DidiColleaguesSearchActivity.this, R.string.me_car_address_colleagues_search_fail);
//                    }
//                }
//
//                @Override
//                public void onError(int i, String s) {
//                    PromptUtils.removeLoadDialog(DidiColleaguesSearchActivity.this);
//                    ToastUtils.showToast(DidiColleaguesSearchActivity.this, s);
//                }
//            });
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_SEARCH_RESULT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data);
                finish();
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
