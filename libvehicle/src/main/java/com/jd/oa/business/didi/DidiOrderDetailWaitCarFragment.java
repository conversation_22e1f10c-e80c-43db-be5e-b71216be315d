package com.jd.oa.business.didi;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.dialog.ColleagueInfoDialog;
import com.jd.oa.business.didi.model.DidiMapPopBean;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.widget.MyInfoWindowAdapter;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.js.hybrid.utils.ShareUtils;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.tencent.map.geolocation.TencentLocation;
import com.tencent.map.geolocation.TencentLocationListener;
import com.tencent.map.geolocation.TencentLocationManager;
import com.tencent.map.geolocation.TencentLocationRequest;
import com.tencent.mapsdk.raster.model.BitmapDescriptorFactory;
import com.tencent.mapsdk.raster.model.CameraPosition;
import com.tencent.mapsdk.raster.model.LatLng;
import com.tencent.mapsdk.raster.model.LatLngBounds;
import com.tencent.mapsdk.raster.model.Marker;
import com.tencent.mapsdk.raster.model.MarkerOptions;
import com.tencent.tencentmap.mapsdk.map.CameraUpdateFactory;
import com.tencent.tencentmap.mapsdk.map.MapView;
import com.tencent.tencentmap.mapsdk.map.TencentMap;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import cn.com.libsharesdk.item.WechatFriendShare;

/**
 * Created by qudongshi on 2016/1/15.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderDetailWaitCarFragment extends DidiBaseFragment implements OperatingListener, TencentLocationListener, TencentMap.OnMarkerClickListener, TimlineMessageListener {

    private View mRootView;

    private Toolbar mToolbar;

    private MapView mapView;

    private TextView mTvNotice; // 等待接驾通知

    private String mNoticeWaitCar;

    //Driver info
    private RelativeLayout mRlDriverInfo;
    private CircleImageView mIvDriverPic;
    private TextView mTvDriverName;
    private TextView mTvCarType;
    private TextView mTvCarNo;
    private ImageView mIvService;

    private LinearLayout mLlStarContent;
    private RatingBar mRbStar;
    private TextView mTvFraction;
    private TextView mTvOrder;

    private TextView tvComplain;

    private LinearLayout mLlcomplain;
    private LinearLayout mShare;

    private View llCancel; // 取消用车
    private ImageView mIvTelephone;
    private View layout_colleague_list;
    private LinearLayout mLayoutColleagueContainer;
    private LinearLayout mLayoutTips;
    private TextView mTvTips;
    private TextView mTvMsg;

    private DidiOrderDetailBean mDidiOrderDetailBean;

    private int pollingTime = 10;

    private boolean isCancel = false;

    private TencentLocationManager locationManager;
    private TencentLocationRequest locationRequest;
    private Marker myLocation;
    private TencentMap tencentMap;
    private Marker beginLocation;
    private MyInfoWindowAdapter infoWindowAdapter;
    private Marker driverLocation;
    private TextView tvDriverDistance;
    private MyOrientationListener myOrientationListener;
    private IosAlertDialog iosAlertDialog;
    private int zoomLevel = 20;
    private boolean needAutoChangeZoom = true;

    private View.OnClickListener mOnClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            DidiOrderDetailBean.ColleagueInfo info = (DidiOrderDetailBean.ColleagueInfo) v.getTag();
            String phone = info.phoneNumber;
            String address = info.address;
            ColleagueInfoDialog dialog = new ColleagueInfoDialog(getContext());
            dialog.setPhone(phone);
            dialog.setAddress(address);
            dialog.show();
        }
    };

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_wait_car, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_wait_car);

        mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        isCancel = getArguments().getBoolean("isCancel");
        initView();
        AppBase.iAppBase.registerTimlineMessage(MESSAGE_TYPE_CAR_POOL_COLLEAGUE, this);
        showLoading();
        return mRootView;
    }

    /**
     * 初始化
     */
    private void initView() {

        mapView = mRootView.findViewById(R.id.mapView);

        mTvNotice = mRootView.findViewById(R.id.tv_notice); // 等待接驾通知

        //Driver info
        mRlDriverInfo = mRootView.findViewById(R.id.ll_driver_info);
        mIvDriverPic = mRootView.findViewById(R.id.iv_driver_pic);
        mTvDriverName = mRootView.findViewById(R.id.tv_driver_name);
        mTvCarType = mRootView.findViewById(R.id.tv_car_type);
        mTvCarNo = mRootView.findViewById(R.id.tv_car_no);
        mIvService = mRootView.findViewById(R.id.iv_icon);

        mLlStarContent = mRootView.findViewById(R.id.ll_star_content);
        mRbStar = mRootView.findViewById(R.id.rab_star);
        mTvFraction = mRootView.findViewById(R.id.tv_fraction);
        mTvOrder = mRootView.findViewById(R.id.tv_order);

        tvComplain = mRootView.findViewById(R.id.tvComplain);

        llCancel = mRootView.findViewById(R.id.ll_cancel); // 取消用车
        mIvTelephone = mRootView.findViewById(R.id.iv_telephone);
        layout_colleague_list = mRootView.findViewById(R.id.layout_colleague_list);
        mLayoutColleagueContainer = mRootView.findViewById(R.id.layout_colleague_container);
        mLayoutTips = mRootView.findViewById(R.id.ll_tip);
        mTvTips = mRootView.findViewById(R.id.tv_tips);
        mLlcomplain = mRootView.findViewById(R.id.ll_complain);
        mShare = mRootView.findViewById(R.id.ll_share);
        mTvMsg = mRootView.findViewById(R.id.tv_msg);

        mIvTelephone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_waitPickUp_phoneDriver_click,JDMAConstants.mobile_employeeTravel_waitPickUp_phoneDriver_click);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jd.oa.business.R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mDidiOrderDetailBean.order.driverPhone);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.driverPhone);
                }
            }
        });

        llCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_waitPickUp_cancel_click, JDMAConstants.mobile_employeeTravel_waitPickUp_cancel_click);
                new IosAlertDialog(getActivity()).builder().setMsg(getString(R.string.me_are_you_cancel_car))
                        .setNegativeButton(getString(R.string.me_car_contiune), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                            }
                        })
                        .setPositiveButton(getString(R.string.me_car_cancel), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                cancelCallTaxi();
                            }
                        })
                        .show();
            }
        });

        mLlcomplain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_waitPickUp_complaint_click,JDMAConstants.mobile_employeeTravel_waitPickUp_complaint_click);
                // 投诉
                if ("1".equals(mDidiOrderDetailBean.order.isComplainted))
                    return;
                Intent intent = new Intent(requireContext(), FunctionActivity.class);
                intent.putExtra("orderId", mDidiOrderDetailBean.order.orderId);
                intent.putExtra("flag", 0);
                intent.putExtra("serviceType", mDidiOrderDetailBean.order.serviceType);
                intent.putExtra("function", DidiOrderDetailOmplaintsFragment.class.getName());
                requireActivity().startActivity(intent);
            }
        });

        mShare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DidiUtils.shareOrder(requireActivity(), mDidiOrderDetailBean.order.orderId);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_waitPickUp_travelShare_click,JDMAConstants.mobile_employeeTravel_waitPickUp_travelShare_click);
            }
        });

        String tips = mDidiOrderDetailBean.drivingShowTip;
        mLayoutTips.setVisibility(TextUtils.isEmpty(tips) ? View.GONE : View.VISIBLE);
        mTvTips.setText(tips);
        // 司机信息
        mTvDriverName.setText(mDidiOrderDetailBean.order.driverName);
        ImageLoader.load(getActivity(), mIvService, mDidiOrderDetailBean.icon, false, R.drawable.jdme_icon_use_car_service_default, R.drawable.jdme_icon_use_car_service_default);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.driverLevel)) {
            try {
                DidiUtils.showDriverLevel(Float.parseFloat(mDidiOrderDetailBean.order.driverLevel), mRbStar);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mTvFraction.setText(mDidiOrderDetailBean.order.driverLevel + mTvFraction.getContext().getString(R.string.me_didi_minute));
            mTvOrder.setText(mDidiOrderDetailBean.order.driverOrderCount + mTvFraction.getContext().getString(R.string.me_didi_unit_dan));
        } else {
            mLlStarContent.setVisibility(View.GONE);
        }
        mTvCarNo.setText(mDidiOrderDetailBean.order.driverCard);
        mTvCarType.setText(StringUtils.getSubStringFromStart(mDidiOrderDetailBean.order.driverCarType, 12));
        DidiUtils.getDriverImg(mDidiOrderDetailBean.order.driverAvatar, mIvDriverPic, getActivity());

        if (mDidiOrderDetailBean.order.didiOrderStatus.equals(DidiUtils.DIDI_STATE_COMEING))
            mNoticeWaitCar = getResources().getString(R.string.me_didi_car_waiting);
        else
            mNoticeWaitCar = getResources().getString(R.string.me_didi_car_comming);
        if (StringUtils.convertToInt(mDidiOrderDetailBean.order.pollingTime) > 0)
            pollingTime = StringUtils.convertToInt(mDidiOrderDetailBean.order.pollingTime);

        // 取消叫车
        if (isCancel) {
            new IosAlertDialog(getActivity()).builder().setMsg(getString(R.string.me_are_you_cancel_car))
                    .setNegativeButton(getString(R.string.me_car_contiune), new View.OnClickListener() {

                        @Override
                        public void onClick(View v) {

                        }
                    })
                    .setPositiveButton(getString(R.string.me_car_cancel), new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            cancelCallTaxi();
                        }
                    })
                    .show();
        }

        if ("1".equals(mDidiOrderDetailBean.order.isComplainted)) {
            tvComplain.setText(R.string.me_didi_car_has_complain);
        }

        //初始化地图
        tencentMap = mapView.getMap();
        //设置自定义pop的adapter
        infoWindowAdapter = new MyInfoWindowAdapter();
        tencentMap.setInfoWindowAdapter(infoWindowAdapter);
        tencentMap.setOnMarkerClickListener(this);


        //开启定位
        locationManager = TencentLocationManager.getInstance(getActivity());
        locationRequest = TencentLocationRequest.create();
        bindListener();


        //开启方向传感器
        myOrientationListener = new MyOrientationListener(getActivity());
        myOrientationListener.setOnOrientationListener(new MyOrientationListener.OnOrientationListener() {
            @Override
            public void onOrientationChanged(float x) {
                //UI原始箭头图标是和X轴平行的,加上偏移量纠正箭头指向
                if (myLocation != null) {
                    myLocation.setRotation(x + 55);
                }
            }
        });

        //同行人隐藏电话按钮
        boolean isCallCarUser = DidiUtils.isCallCarUser(mDidiOrderDetailBean);
        mIvTelephone.setVisibility(isCallCarUser ? View.VISIBLE : View.GONE);
        llCancel.setVisibility(isCallCarUser ? View.VISIBLE : View.GONE);
        mLlcomplain.setVisibility(isCallCarUser ? View.VISIBLE : View.GONE);
        //同行人信息
        if (mDidiOrderDetailBean.carPoolInfoData != null) {
            mTvMsg.setVisibility(TextUtils.isEmpty(mDidiOrderDetailBean.carPoolInfoData.carPoolMsg) ? View.INVISIBLE : View.VISIBLE);
            mTvMsg.setText(mDidiOrderDetailBean.carPoolInfoData.carPoolMsg);
            showColleagues(mDidiOrderDetailBean.carPoolInfoData.carPoolInfo);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        mapView.onResume();
        EventBusMgr.getInstance().register(this);
        DidiPollingUtils.startPollingService(AppBase.getAppContext(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, pollingTime, true);
        if (myOrientationListener != null) {
            myOrientationListener.start();
        }
    }

    private void showLoading() {
        PromptUtils.showLoadDialog(requireActivity(), getString(R.string.me_didi_web_loading), true);
    }

    private void hideLoading() {
        PromptUtils.removeLoadDialog(requireActivity());
    }

    private void setAutoZoom() {
        //各种办法都试了，只能采用这种最不靠谱的方法，自己记录不同level的比例尺
        final double[] scales = new double[]{1946.0552, 1473.3352, 837.1212, 443.70938, 228.08131, 115.58526, 58.176918, 29.184338, 14.616136, 7.314007, 3.6585367
                , 1.8293655, 0.9167961, 0.45814398, 0.22828709};
        tencentMap.setZoom(zoomLevel);
        //加上这个视图区域改变监听，首次改变camera之后再做自动缩放 否则获取到map高度为0
        tencentMap.setOnMapCameraChangeListener(new TencentMap.OnMapCameraChangeListener() {
            @Override
            public void onCameraChange(CameraPosition cameraPosition) {

            }

            @Override
            public void onCameraChangeFinish(CameraPosition cameraPosition) {
                tencentMap.setOnMapCameraChangeListener(null);
                if (needAutoChangeZoom) {
                    needAutoChangeZoom = false;
                    //取一个合适的能够将司机和起点都展示在屏幕内的像素
                    double maxPiexl = (mapView.getHeight() / 2) * 0.8;
                    //两点的实际距离（米）
                    if (getBeginLocation() != null && getDriverLocation() != null) {
                        double distance = mapView.getProjection().distanceBetween(getBeginLocation(), getDriverLocation());
                        for (int i = scales.length - 1; i >= 0; i--) {
                            if (distance / scales[i] < maxPiexl || i == 0) {
                                zoomLevel = i + 5;
                                tencentMap.setZoom(zoomLevel);
                                break;
                            }
                        }
                    }
                }
                tencentMap.setOnMapCameraChangeListener(this);
            }
        });
    }


    @Override
    public void onPause() {
        super.onPause();
        mapView.onPause();
        EventBusMgr.getInstance().unregister(this);//反注册EventBus
    }

    @Override
    public void onStop() {
        super.onStop();
        mapView.onStop();
        if (myOrientationListener != null) {
            myOrientationListener.stop();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mapView.onDestroyView();
        AppBase.iAppBase.unregisterListener(MESSAGE_TYPE_CAR_POOL_COLLEAGUE, this);
        if (locationManager != null) {
            locationManager.removeUpdates(this);
            locationManager = null;
            locationRequest = null;
        }
        DidiPollingUtils.stopPollingService(getActivity());
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == 300 && getActivity() != null) {
            getActivity().setResult(300);
            getActivity().finish();
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 长连接消息
     *
     * @param type
     * @param message
     */
    @Override
    public void onMessageReceiver(String type, String message) {
        MELogUtil.localD(MELogUtil.TAG_TAX, "onMessageReceiver, type: " + type + ", message: " + message);
        if (TextUtils.isEmpty(message)) return;
        if (MESSAGE_TYPE_CAR_POOL_COLLEAGUE.equals(type)) {
            try {
                JSONObject object = new JSONObject(message);
                String dataStr = object.getString("data");
                JSONObject data = new JSONObject(dataStr);
                String colleagueStr = data.getString("carPoolInfo");
                String msg = data.optString("carPoolMsg");
                List<DidiOrderDetailBean.ColleagueInfo> colleagues = new Gson().fromJson(colleagueStr, new TypeToken<List<DidiOrderDetailBean.ColleagueInfo>>() {
                }.getType());
                mTvMsg.setVisibility(TextUtils.isEmpty(msg) ? View.INVISIBLE : View.VISIBLE);
                mTvMsg.setText(msg);
                showColleagues(colleagues);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private void showColleagues(List<DidiOrderDetailBean.ColleagueInfo> colleagues) {
        mLayoutColleagueContainer.removeAllViews();
        if (CollectionUtil.isEmptyOrNull(colleagues)) {
            layout_colleague_list.setVisibility(View.GONE);
        } else {
            for (int i = 0; i < colleagues.size(); i++) {
                ImageView imageView = new ImageView(getContext());
                imageView.setImageResource(R.drawable.jdme_ic_car_pool_colleague_default_icon_small);
                int padding = DensityUtil.dp2px(getContext(), 12);
                imageView.setPadding(padding, padding, padding, padding);
                imageView.setTag(colleagues.get(i));
                imageView.setOnClickListener(mOnClickListener);
                mLayoutColleagueContainer.addView(imageView);
            }
            layout_colleague_list.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 轮训访问接口的返回值会回调到这里
     *
     * @param orderDetailBean
     */
    public void onEventMainThread(DidiOrderDetailBean orderDetailBean) {
        // 跳转逻辑
        if (null == orderDetailBean) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else if (TextUtils.isEmpty(orderDetailBean.order.orderId)) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else {
            mDidiOrderDetailBean = orderDetailBean;
            if (!TextUtils.isEmpty(orderDetailBean.order.reassignMeOrderNo)) {
                DidiPollingUtils.stopPollingService(getActivity());
                DidiPollingUtils.startPollingService(AppBase.getAppContext(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, pollingTime, true);
                return;
            }

            // 展示改派弹窗的逻辑
            if (mDidiOrderDetailBean.order.isTransfer.equals("1")) {
                //弹出提示后 轮询不停 订单状态如果迅速改变 直接跳转下一状态界面
                showtransferDialog();
            }
            switch (orderDetailBean.order.didiOrderStatus) {
                case DidiUtils.DIDI_STATE_COMEING:
                    mNoticeWaitCar = getResources().getString(R.string.me_didi_car_waiting);
                    //  更新司机到乘客的距离，以及司机的经纬度
                    //todo 'java.lang.String java.lang.String.trim()' on a null object reference
                    double mDistance = 0.0d;
                    if(!TextUtils.isEmpty(mDidiOrderDetailBean.order.distance)){
                        mDistance = Double.parseDouble(mDidiOrderDetailBean.order.distance);
                    }
                    String distance = new DecimalFormat("0.0").format(mDistance / 1000d);
                    if (driverLocation != null) {
                        tvDriverDistance.setText(getString(R.string.me_didi_car_driver_distance, distance));
                        driverLocation.setPosition(getDriverLocation());
                        if (StringUtils.isEmptyWithTrim(mDidiOrderDetailBean.order.distance)) {
                            driverLocation.hideInfoWindow();
                        } else {
                            driverLocation.showInfoWindow();
                        }
                    }
                    break;
                case DidiUtils.DIDI_STATE_ARRIVED:
                    if (driverLocation != null)
                        tvDriverDistance.setText(R.string.me_didi_car_comming);
                    break;
                default:
                    if (iosAlertDialog != null)
                        iosAlertDialog.dismiss();
                    String className = DidiUtils.getRedirectFragmentClassname(orderDetailBean);
                    if (TextUtils.isEmpty(className)) {
                        ToastUtils.showToast(R.string.me_exception_order_state);
                    } else {
                        Bundle bundle = new Bundle();
                        bundle.putSerializable("orderDetailBean", orderDetailBean);
                        try {
                            FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content, false, bundle, false);
                        } catch (ClassNotFoundException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
            }
        }
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OPERATE_GO_DIDI == optionFlag) {
            if (args != null) {
                String isAgain = args.getString("isAgain");
                if ("0".equals(isAgain)) {
                    tvComplain.setText(R.string.me_didi_car_has_complain);
                    mDidiOrderDetailBean.order.isComplainted = isAgain;
                }
            }
            return true;
        }
        return false;
    }

    protected void bindListener() {
        locationManager.requestLocationUpdates(locationRequest, this);
    }

    private boolean hasSetCenter = false;

    @Override
    public void onLocationChanged(TencentLocation tencentLocation, int errorCode, String errorMsg) {
        ArrayList<LatLng> latLngArrayList = new ArrayList<>();
        if (errorCode == TencentLocation.ERROR_OK) {
            LatLng latLng = new LatLng(tencentLocation.getLatitude(), tencentLocation.getLongitude());

            //添加我的位置
            if (myLocation == null) {
                myLocation = this.tencentMap.addMarker(new MarkerOptions().
                        position(latLng).
                        icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_car_map_me)).
                        anchor(0.5f, 0.5f));
                latLngArrayList.add(latLng);
            }
            //添加起点标志
            if (beginLocation == null && getBeginLocation() != null) {
                beginLocation = tencentMap.addMarker(new MarkerOptions().
                        position(getBeginLocation()).
                        icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_car_begin)).
                        anchor(0.5f, 1f));
                latLngArrayList.add(getBeginLocation());
            }
            // 2018/3/19 leo  添加司机标志
            if (driverLocation == null && getDriverLocation() != null) {
                driverLocation = tencentMap.addMarker(new MarkerOptions().
                        position(getDriverLocation()).
                        icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_car_driver)).
                        anchor(0.5f, 1f));
                driverLocation.setTag(new DidiMapPopBean(MyInfoWindowAdapter.NORMAL, ""));
                tvDriverDistance = infoWindowAdapter.getInfoWindow(driverLocation).findViewById(R.id.tvContent);
                StringBuilder distanceSb = new StringBuilder();
                if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.distance)) {
                    String distance = new DecimalFormat("0.0").format(Double.parseDouble(mDidiOrderDetailBean.order.distance) / 1000d);
                    distanceSb.append(distance);
                }
                tvDriverDistance.setText(getString(R.string.me_didi_car_driver_distance, distanceSb.toString()));
                if (StringUtils.isEmptyWithTrim(mDidiOrderDetailBean.order.distance)) {
                    driverLocation.hideInfoWindow();
                } else {
                    driverLocation.showInfoWindow();
                }
                latLngArrayList.add(getDriverLocation());
            }

            myLocation.setPosition(latLng);
            if (!hasSetCenter) {
//                tencentMap.setCenter(latLng);//设置当前位置为地图中心点
                hasSetCenter = true;
                LatLngBounds latLngBounds = new LatLngBounds.Builder().include(latLngArrayList).build();
                tencentMap.moveCamera(CameraUpdateFactory.newLatLngBounds(latLngBounds, 200));
//                setAutoZoom();
            }
        } else {
            Log.e("location", "location failed:" + errorMsg);
        }
        hideLoading();
    }

    @Override
    public void onStatusUpdate(String s, int i, String s1) {

    }

    @Override
    public boolean onMarkerClick(Marker marker) {
        return true;
    }

    /**
     * 改派提示框
     */
    private void showtransferDialog() {
        if (iosAlertDialog == null) {
            iosAlertDialog = new IosAlertDialog(getActivity()).builder();

        }
        if (iosAlertDialog.isShowing()) {
            return;
        }
        iosAlertDialog.setMsg(getString(R.string.me_didi_car_transfer_notice)).setCancelable(false)
                .setPositiveButton(getString(R.string.me_ok), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        DidiUtils.getOrderDetail(getActivity(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, new DidiUtils.IDetailCallback() {
                            @Override
                            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                                checkOrder(didiOrderDetailBean);
                            }
                        });
                    }
                })
                .show();
    }

    private void checkOrder(DidiOrderDetailBean didiOrderDetailBean) {
        if (null == didiOrderDetailBean) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else {
            String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
            if (TextUtils.isEmpty(className)) {
                ToastUtils.showToast(R.string.me_exception_order_state);
            } else {
                Bundle bundle = new Bundle();
                bundle.putBoolean("isCancel", isCancel);
                bundle.putSerializable("orderDetailBean", didiOrderDetailBean);
                try {
                    FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content, false, bundle, false);
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 取消叫车
     */
    private void cancelCallTaxi() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) { // 取消叫车
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        Intent intent = new Intent(requireActivity(), DidiCancelOrderResonActivity.class);
                        intent.putExtra("orderId", mDidiOrderDetailBean.order.orderId);
                        requireActivity().startActivityForResult(intent, 100);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.cancelOrder(this, callback, mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, "1");
    }

    /**
     * 获取司机的经纬度
     *
     * @return
     */
    private LatLng getDriverLocation() {
        LatLng latLng = null;
        if (mDidiOrderDetailBean != null) {
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.dlat) && !TextUtils.isEmpty(mDidiOrderDetailBean.order.dlng)) {
                double lng = Double.parseDouble(mDidiOrderDetailBean.order.dlng);
                double lat = Double.parseDouble(mDidiOrderDetailBean.order.dlat);
                latLng = new LatLng(lat, lng);
            }
        }
        return latLng;
    }

    /**
     * 获取起点的经纬度
     *
     * @return
     */
    private LatLng getBeginLocation() {
        LatLng latLng = null;
        if (mDidiOrderDetailBean != null) {
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.flat) && !TextUtils.isEmpty(mDidiOrderDetailBean.order.flng)) {
                double lng = Double.parseDouble(mDidiOrderDetailBean.order.flng);
                double lat = Double.parseDouble(mDidiOrderDetailBean.order.flat);
                latLng = new LatLng(lat, lng);
            }
        }
        return latLng;
    }
}
