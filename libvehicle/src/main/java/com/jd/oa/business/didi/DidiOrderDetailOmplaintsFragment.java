package com.jd.oa.business.didi;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiComplaintReasonBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;


import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * 投诉
 * Created by qudongshi on 2016/5/21
 */
@Navigation(displayHome = true, title = -1)
public class DidiOrderDetailOmplaintsFragment extends BaseFragment{

    private View mRootView;

    private String orderId;
    private String mPhoneNumber;
    private String mServiceType;

    private LinearLayout mLlContainer;
    private EditText mEtContent;
    private TextView mTvCount;
    private TextView mTvHotline;
    private Button mBtnSubmit;

    private DidiComplaintReasonBean didiComplaintReasonBean;

    private int mMajorThemeColor = R.color.skin_color_default;
    private int mMajorChecked;
    private int mMajorBg;

    private int mCheckedId = -1;
    private String mCheckedContent = "";
    private String mCheckedReasonType = "";

    private boolean isSuccess = false;

    private int flag = 0;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_omplaints, container, false);

        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_order_omplaints);

        init();
        orderId = getArguments().getString("orderId");
        mServiceType = getArguments().getString("serviceType"); // 服务类型
        flag = getArguments().getInt("flag");
        mMajorThemeColor =  R.color.skin_color_default;
        mMajorChecked =  R.drawable.jdme_didi_icon_checked_default;
        mMajorBg =  R.drawable.jdme_bg_didi_omplaints_textview_border_checked_default;
        getComplaintReasonList();
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (isSuccess) {
            Bundle bundle = new Bundle();
            bundle.putBoolean("isOmplaints", true);
            bundle.putBoolean("isSuccsess", true);
            FragmentUtils.removeAndNotifyPrev(getActivity(), this, bundle);
        }
    }

    private void init(){
        mLlContainer = mRootView.findViewById(R.id.ll_container);
        mEtContent = mRootView.findViewById(R.id.et_msg);
        mTvCount = mRootView.findViewById(R.id.tv_count);
        mTvHotline = mRootView.findViewById(R.id.tv_consumer_hotline);
        mTvHotline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switch (flag){
                    case 0:
                        JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_waitPickUp_complaint_customerTel_click,JDMAConstants.mobile_employeeTravel_waitPickUp_complaint_customerTel_click);
                        break;
                    case 1:
                        break;
                    case 2:
                        JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveling_complaint_customerTel_click,JDMAConstants.mobile_employeeTravel_traveling_complaint_customerTel_click);
                        break;
                    default:
                        break;
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mPhoneNumber);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mPhoneNumber);
                }
            }
        });
        mBtnSubmit = mRootView.findViewById(R.id.btn_submit);
        mBtnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switch (flag) {
                    case 0:
//                        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_SUBMIT_OMPLAINTS_WAITCAR);
                        JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_waitPickUp_complaint_submit_click,JDMAConstants.mobile_employeeTravel_waitPickUp_complaint_submit_click);
                        break;
                    case 1:
                        break;
                    case 2:
//                        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_SUBMIT_OMPLAINTS_RUNNING);
                        JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveling_complaint_submit_click,JDMAConstants.mobile_employeeTravel_traveling_complaint_submit_click);
                        break;
                    default:
                }
                if ("02".equals(mServiceType) && "其他".equals(mCheckedContent) && mCheckedId == 7) {
                    //首汽勾选其他，则输入框必须填写
                    if (TextUtils.isEmpty(mEtContent.getText())) {
                        ToastUtils.showInfoToast(R.string.ne_didi_notice_omplaints2);
                        return;
                    }
                }
                submitComplaint();
            }
        });

    }

    private void initView() {
        //首汽的订单修改hint文字
        if ("02".equals(mServiceType)) {
            mEtContent.setHint(R.string.ne_didi_hint_omplaints2);
        }
        mPhoneNumber = didiComplaintReasonBean.servicePhone;

        mEtContent.addTextChangedListener(new TextWatcher() { // 信息修改后更改提字数
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mTvCount.setText(40 - s.length() + "");
            }
        });

        for (DidiComplaintReasonBean.ComplaintReson reason : didiComplaintReasonBean.reasonList) {
            TextView mTmp = (TextView) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_omplains_textview, null);
            mTmp.setText(reason.text);
            mTmp.setTag(reason.type);
            mTmp.setId(StringUtils.convertToInt(reason.id));
            mTmp.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCheckedId == v.getId())
                        return;
                    TextView view = (TextView) v;
                    view.setTextColor(getResources().getColor(mMajorThemeColor));
                    view.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(mMajorChecked), null);
                    view.setBackgroundResource(mMajorBg);
                    if (mCheckedId != -1) {
                        TextView mTvChecked = (TextView) mRootView.findViewById(mCheckedId);
                        mTvChecked.setTextColor(getResources().getColor(R.color.black_main_summary));
                        mTvChecked.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                        mTvChecked.setBackgroundResource(R.drawable.jdme_bg_didi_omplaints_textview_border);
                    }
                    mCheckedId = view.getId();
                    mCheckedContent = view.getText().toString();
                    mCheckedReasonType = (String) view.getTag();
                }
            });
            mLlContainer.addView(mTmp);
        }

    }

    /**
     * 投诉列表
     */
    private void getComplaintReasonList() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        didiComplaintReasonBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiComplaintReasonBean.class);
                        initView();
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getComplaintReasonList(this, callback, "", mServiceType);
    }


    private void submitComplaint() {
        if (mCheckedId == -1) {
            ToastUtils.showToast(R.string.me_didi_msg_please_sel_omplaints_reason);
            return;
        }
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            isSuccess = true;
                            getActivity().onBackPressed();
                            String isAgain = jsonObject.getString("isAgain");
                            Bundle bundle = new Bundle();
                            bundle.putString("isAgain", isAgain);
                            FragmentUtils.updateUI(OperatingListener.OPERATE_GO_DIDI, bundle);
                            ToastUtils.showToast(R.string.me_didi_msg_submit_success_not_translate);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        ToastUtils.showToast(errorMsg);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.submitComplaint(this, callback, orderId, mCheckedId + "", mCheckedContent, mEtContent.getText().toString(), mCheckedReasonType);
    }

}
