package com.jd.oa.business.didi.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatDialog;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.utils.DisplayUtil;

/**
 * Created by peidongbiao on 2019-05-16
 */
public class CarPoolTipsDialog extends AppCompatDialog {

    private CheckBox mCheckBox;
    private TextView mTvDontRemind;
    private TextView mTvCancel;
    private TextView mTvCall;

    public CarPoolTipsDialog(Context context) {
        super(context);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_dialog_didi_car_pool_tips);
        mCheckBox = findViewById(R.id.cb_check);
        mTvDontRemind = findViewById(R.id.tv_dont_remind);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvCall = findViewById(R.id.tv_call);
        if (getWindow() != null) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = (int) (DisplayUtil.getScreenWidth(getContext()) * 0.8);
            getWindow().setAttributes(layoutParams);
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_bg_colleague_tips_dialog);
        }
        setCancelable(false);
        setCanceledOnTouchOutside(false);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
//                PreferenceManager.UserInfo.setDontShowCarPollTipsDialog(isChecked);
                TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_DONT_SHOW_CAR_POOL_TIPS, isChecked);
            }
        });
        mTvDontRemind.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCheckBox.setChecked(!mCheckBox.isChecked());
            }
        });
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    public void setOnCallClickListener(View.OnClickListener onCallClickListener) {
        mTvCall.setOnClickListener(onCallClickListener);
    }
}