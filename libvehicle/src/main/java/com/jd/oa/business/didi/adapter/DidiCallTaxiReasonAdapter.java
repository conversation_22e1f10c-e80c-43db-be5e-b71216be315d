package com.jd.oa.business.didi.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.TextView;

import com.jd.oa.business.R;

import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2016/1/27.
 */
public class DidiCallTaxiReasonAdapter extends BaseAdapter implements Filterable {

    private List<String> mData;
    private LayoutInflater mInflater;
    private InnerFilter mFilter;

    public DidiCallTaxiReasonAdapter(Context context, List<String> data) {
        this.mData = data;
        this.mInflater = LayoutInflater.from(context);
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public String getItem(int position) {
        return mData.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View view;
        TextView text;

        if (convertView == null) {
            view = mInflater.inflate(R.layout.jdme_item_didi_call_taxi_reason, parent, false);
        } else {
            view = convertView;
        }

        text = (TextView) view.findViewById(R.id.tv_title);
        String item = getItem(position);
        text.setText(item);

        return view;
    }

    @Override
    public Filter getFilter() {
        if (mFilter == null) {
            mFilter = new InnerFilter();
        }
        return mFilter;
    }

    private class InnerFilter extends Filter {

        @Override
        protected FilterResults performFiltering(CharSequence constraint) {
            FilterResults filterResults = new FilterResults();
            filterResults.values = mData;
            filterResults.count = mData.size();
            return filterResults;
        }

        @Override
        protected void publishResults(CharSequence constraint, FilterResults results) {
            mData = (List<String>) results.values;
            if (results.count > 0) {
                notifyDataSetChanged();
            } else {
                notifyDataSetInvalidated();
            }
        }
    }
}
