package com.jd.oa.business.didi.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatDialog;

import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.utils.DisplayUtil;

/**
 * Created by peidongbiao on 2019-05-15
 */
public class ColleagueInfoDialog extends AppCompatDialog {


    private TextView mTvPhone;
    private TextView mTvAddress;
    private TextView mTvClose;

    public ColleagueInfoDialog(Context context) {
        super(context);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_dialog_didi_car_pool_colleague_info);

        mTvPhone = findViewById(R.id.tv_phone);
        mTvAddress = findViewById(R.id.tv_address);
        mTvClose = findViewById(R.id.tv_close);

        if (getWindow() != null) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = (int) (DisplayUtil.getScreenWidth(getContext()) * 0.8);
            getWindow().setAttributes(layoutParams);
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_bg_colleague_info_dialog);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mTvClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    public void setPhone(String phone) {
        mTvPhone.setText(phone);
    }

    public void setAddress(String address) {
        mTvAddress.setText(address);
    }
}
