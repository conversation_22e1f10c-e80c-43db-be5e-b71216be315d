package com.jd.oa.business.didi.dialog;

import android.content.Context;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.adapter.CarPoolColleagueAdapter;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.utils.CollectionUtil;

import java.util.Comparator;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Predicate;
//import us.zoom.sdk.InMeetingLiveStreamController;

/**
 * Created by peidongbiao on 2019/4/3
 */
public class ColleagueListDialog extends AppCompatDialog {

    private ImageView mIvClose;
    private RecyclerView mRecyclerView;
    private CarPoolColleagueAdapter mAdapter;
    private DidiOrderDetailBean mDetailBean;
    private List<CarPoolUserStatus> mList;

    public ColleagueListDialog(Context context, DidiOrderDetailBean detailBean) {
        super(context, R.style.BottomDialogStyle);
        setContentView(R.layout.jdme_dialog_didi_car_pool_colleague);
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        mDetailBean = detailBean;
        mList = reorderList(mDetailBean);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mIvClose = findViewById(R.id.iv_close);
        mRecyclerView = findViewById(R.id.recycler);

        mAdapter = new CarPoolColleagueAdapter(getContext(), mList);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getContext());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(mAdapter);

        mIvClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancel();
            }
        });
    }

    private List<CarPoolUserStatus> reorderList(DidiOrderDetailBean detailBean) {
        if (detailBean == null) return null;
        if (CollectionUtil.isEmptyOrNull(detailBean.carPoolUser)) return detailBean.carPoolUser;
        final String callCarUser = detailBean.callCarUser;
        return Observable.fromIterable(detailBean.carPoolUser)
                .doOnNext(new Consumer<CarPoolUserStatus>() {
                    @Override
                    public void accept(CarPoolUserStatus carPoolUserStatus) throws Exception {
                        if (carPoolUserStatus.getUserName().equals(callCarUser)) {
                            carPoolUserStatus.setCallCarUser(true);
                        }
                    }
                })
                .filter(new Predicate<CarPoolUserStatus>() {
                    @Override
                    public boolean test(CarPoolUserStatus carPoolUserStatus) throws Exception {
                        if (DidiUtils.CAR_POOL_STATE_APPROVED.equals(carPoolUserStatus.getFlowFlag())) {
                            return true;
                        }
                        if (DidiUtils.CAR_POOL_STATE_COMPLETE.equals(carPoolUserStatus.getFlowFlag())){
                            return true;
                        }
                        if (carPoolUserStatus.isCallCarUser()) {
                            return true;
                        }
                        return false;
                    }
                })
                .toSortedList(new Comparator<CarPoolUserStatus>() {
                    @Override
                    public int compare(CarPoolUserStatus o1, CarPoolUserStatus o2) {
                        if (o1.isCallCarUser() == o2.isCallCarUser()) {
                            return 0;
                        } else if (o1.isCallCarUser() && !o2.isCallCarUser()) {
                            return -1;
                        } else if (!o1.isCallCarUser() && o2.isCallCarUser()) {
                            return 1;
                        } else {
                            return 0;
                        }
                    }
                })
                .blockingGet();
    }
}