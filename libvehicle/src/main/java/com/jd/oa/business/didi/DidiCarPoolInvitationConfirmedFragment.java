package com.jd.oa.business.didi;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.adapter.TravelInviteAdapter;
import com.jd.oa.business.didi.model.CarPoolOrderDetail;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.business.workbench2.model.UserAvatarMap;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;


import java.util.List;

/**
 * Created by peidongbiao on 2019/4/4
 */
@Navigation(hidden = false,  displayHome = true)
public class DidiCarPoolInvitationConfirmedFragment extends BaseFragment {

    private RecyclerView mRecycler;

    private DidiOrderDetailBean mDetailBean;
    private TravelInviteAdapter mAdapter;
    private String mOrderId;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if(getArguments() == null || !getArguments().containsKey("orderDetailBean")) {
            getActivity().finish();
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_didi_car_pool_inviation_confirmed, container, false);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_car_pool_invitation);

        mRecycler = view.findViewById(R.id.recycler);
        mDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        mOrderId = mDetailBean.order.orderId;
        initView();
        action();
        return view;
    }

    private void initView() {
        mAdapter = new TravelInviteAdapter(getContext());
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        mRecycler.setLayoutManager(layoutManager);
        mRecycler.setAdapter(mAdapter);
        mAdapter.setOnUserClickListener(new TravelInviteAdapter.OnUserClickListener() {
            @Override
            public void onUserClick(View view, int position) {
                CarPoolUserStatus user = mAdapter.getItem(position);
                AppBase.iAppBase.showChattingActivity(getActivity(), user.getUserName());
            }
        });
    }

    private void action() {
        getCarPoolOrderStatus();
    }

    private void show(List<CarPoolUserStatus> list) {
        mAdapter.refresh(list);
    }

    private void getCarPoolOrderStatus() {
        NetWorkManager.getCarPoolOrderStatus(mOrderId,null, null, new SimpleRequestCallback<String>(getContext()) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if(!isAlive()) return;
                ApiResponse<CarPoolOrderDetail> response = ApiResponse.parse(info.result, CarPoolOrderDetail.class);
                if(!response.isSuccessful()) {
                    MELogUtil.localD(MELogUtil.TAG_TAX, "getCarPoolOrderStatus onSuccess: " + response.getErrorMessage());
                    return;
                }
                List<CarPoolUserStatus> colleagues = response.getData().getCarPoolUsers();
                //添加发起人
                CarPoolUserStatus callCarUser = DidiUtils.getCallCarUser(mDetailBean);
                if (callCarUser != null) {
                    colleagues.add(0, callCarUser);
                }
                show(colleagues);
                getAvatars(DidiUtils.getCarPoolUserIds(colleagues));
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    private void getAvatars(String ids) {

        com.jd.oa.network.NetWorkManager.getUserAvatars(ids, new LoadDataCallback<List<UserAvatarMap>>() {
            @Override
            public void onDataLoaded(List<UserAvatarMap> userAvatarMaps) {
                showAvatars(userAvatarMaps);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showAvatars(List<UserAvatarMap> userAvatarMaps) {
        if(CollectionUtil.isEmptyOrNull(mAdapter.getData())) return;
        if(CollectionUtil.isEmptyOrNull(userAvatarMaps)) return;
        List<CarPoolUserStatus> list = mAdapter.getData();
        for (int i = 0; i < userAvatarMaps.size(); i++) {
            UserAvatarMap map = userAvatarMaps.get(i);
            for (int i1 = 0; i1 < list.size(); i1++) {
                CarPoolUserStatus status = list.get(i1);
                if(status.getUserName().equals(map.getUser())) {
                    status.setAvatar(map.getAvatar());
                }
            }
        }
        mAdapter.notifyDataSetChanged();
    }
}