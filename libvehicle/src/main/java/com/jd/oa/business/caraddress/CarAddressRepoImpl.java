package com.jd.oa.business.caraddress;

import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.business.caraddress.bean.CarAddressListWrapper;
import com.jd.oa.business.didi.model.DidiCityBean;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;

public class CarAddressRepoImpl implements CarAddressContract.ICarAddressRepo {
    @Override
    public void getCarAddressList(final LoadDataCallback<CarAddressListWrapper> callback) {
        NetWorkManager.request(null, Constant.getConstant(NetWorkManager.mReqeustType).API_GET_USER_ADDRESS_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<CarAddressListWrapper>(CarAddressListWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(CarAddressListWrapper map, List<CarAddressListWrapper> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), null);
    }

    @Override
    public void addCarAddress(CarAddress carAddress, final LoadDataCallback<JSONObject> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("displayName", carAddress.getDisplayName());
        hashMap.put("address", carAddress.getAddress());
        hashMap.put("cityName", carAddress.getCityName());
        hashMap.put("lat", carAddress.getLat());
        hashMap.put("lng", carAddress.getLng());
        hashMap.put("addressOrder", carAddress.getAddressOrder());
        NetWorkManager.request(null, Constant.getConstant(NetWorkManager.mReqeustType).API_ADD_USER_ADDRESS, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(rawData);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                callback.onDataLoaded(jsonObject);
            }
        }), hashMap);
    }

    @Override
    public void modifyCarAddress(CarAddress carAddress, final LoadDataCallback<JSONObject> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("displayName", carAddress.getDisplayName());
        hashMap.put("address", carAddress.getAddress());
        hashMap.put("cityName", carAddress.getCityName());
        hashMap.put("lat", carAddress.getLat());
        hashMap.put("lng", carAddress.getLng());
        hashMap.put("addressOrder", carAddress.getAddressOrder());
        hashMap.put("remark", carAddress.getRemark());
        hashMap.put("id", carAddress.getId() + "");
        hashMap.put("isNeedApproval",carAddress.getIsNeedApprova());
//        NetWorkManager.request(null, Constant.getConstant(NetWorkManager.mReqeustType).API_MODIFY_USER_ADDRESS, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
//            @Override
//            public void onFailure(String errorMsg, int code) {
//                super.onFailure(errorMsg, code);
//                callback.onDataNotAvailable(errorMsg, code);
//            }
//
//            @Override
//            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
//                super.onSuccess(map, tArray, rawData);
//                callback.onDataLoaded(map);
//            }
//        }), hashMap);

        HttpManager.post(null,hashMap , new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(rawData);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                callback.onDataLoaded(jsonObject);
            }
        }),Constant.getConstant(NetWorkManager.mReqeustType).API_MODIFY_USER_ADDRESS );
    }

    @Override
    public void checkCity(String cityName, final LoadDataCallback<DidiCityBean> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("cityName", cityName);
        NetWorkManager.request(null, Constant.getConstant(NetWorkManager.mReqeustType).API_CHECK_CITY, new SimpleReqCallbackAdapter<>(new AbsReqCallback<DidiCityBean>(DidiCityBean.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(DidiCityBean map, List<DidiCityBean> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void onDestroy() {

    }
}
