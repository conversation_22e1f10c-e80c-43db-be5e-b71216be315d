package com.jd.oa.business.didi;

import android.app.ProgressDialog;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.adapter.DidiCallTaxiReasonAdapter;
import com.jd.oa.business.didi.dialog.CallCarUpgradeTipsDialog;
import com.jd.oa.business.didi.model.DiDiAddressListWrapper;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.didi.model.DidiCallTaxiBean;
import com.jd.oa.business.didi.model.DidiCallTaxiReasonBean;
import com.jd.oa.business.didi.model.DidiCallTaxiUpgradeBean;
import com.jd.oa.business.didi.model.DidiEstimatePriceBean;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.business.didi.utils.CheckCoordinatesUtil;
import com.jd.oa.business.didi.widget.ClearableAutoCompleteTextView;
import com.jd.oa.business.didi.widget.EstimateInfoView;
import com.jd.oa.business.didi.widget.ReservationPopwindow;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qudongshi on 2016/1/11.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiCallTaxiForBussinessFrament extends DidiBaseFragment implements SosoLocationChangeInterface {

    public Bundle mBundel;

    private View mRootView;

    private TextView mTvFrom; // 出发地
    private TextView mTvTo; // 目的地
    private ClearableEditTxt mCetPhoneNumber;
    private TextView mTvLeaveTime; // 出发时间
    private ClearableAutoCompleteTextView mCetReason; // 加班原因
    private TextView mTvTipApproval; // 审批提示
    private Button mBtnCallTaxi;
    private TextView mTvDaily; // 日常用车
    private LinearLayout mLlDaily;
    private TextView mTvTravel; // 差旅用车
    private LinearLayout mLlTravel;
    private LinearLayout mLReserve;

    private String businessType = "00";  // 01 日常用车 02 差旅用车

    private String mIsAppropve; // 0 不需要审批;1 需要审批
    private String mWorkDateType; // 0 工作日加班;1 假日加班第一次;2 假日加班第二次;3 因公外出
    private String mPhoneNumber; // 电话号码
    private String mConfirmText; // 自费升舱提示文案
    private String expenseDisableText; // 自费升舱提示文案2

    private DidiAddressBean mFromAddressBean; // 出发地
    private DidiAddressBean mToAddressBean; // 目的地

    private String mDepartureTime = "";
    private String mDepartureDay = "";

    private ProgressDialog progressDlg;

    private DidiEstimatePriceBean priceBean = new DidiEstimatePriceBean();

    private int mMajorThemeColor;
    private int mMajorChecked;
    private int mMajorBg;

    private Intent intent;
    private Button mBtnCallTaxiUpgrade;
    private boolean upgradeEnable;
    private EstimateInfoView estimateView;//预估价部分UI
    private boolean needClose = false;
    private LinearLayout ll_expend_disable_tip;

    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_calltaxi_bussiness, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_business);
        upgradeEnable = DidiUtils.callCarUpgradeEnable();
        // 获取传递的参数
        mBundel = getArguments();
        if (null != mBundel) {
            mIsAppropve = mBundel.getString("isApprove");
            mWorkDateType = mBundel.getString("workDateType");
            mPhoneNumber = mBundel.getString("phoneNumber");
            mConfirmText = mBundel.getString("confirmText");
            expenseDisableText = mBundel.getString("expenseDisableText");
        }
        initView();
        initLocation();
        getCallTaxiReason();
        return mRootView;
    }

    private void initLocation() {
        SosoLocationService sosoLocationService = new SosoLocationService(getActivity());
        sosoLocationService.setLocationChangedListener(this);
        sosoLocationService.startLocation();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (needClose && getActivity() != null) {
            getActivity().finish();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mCetReason.setOnFocusChangeListener(null);
    }

    /**
     * @param id from/to
     * @return 0 办公地 1 普通
     */
    private int getSelAddressType(int id) {
        return DidiAddressSearchActivity.ADDRESS_TYPE_NORMAL;
    }

    private void getCallTaxiReason() {
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(getActivity(), false, true) { // 用车原因
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiCallTaxiReasonBean tmpBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCallTaxiReasonBean.class);
                        DidiCallTaxiReasonAdapter mAdapter = new DidiCallTaxiReasonAdapter(getActivity(), tmpBean.dictValueList);
                        mCetReason.setAdapter(mAdapter);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        com.jd.oa.network.NetWorkManager.getDictValueList(this, callback, "vehicle_reason");
    }

    /**
     * 叫车
     */
    private void callTaxi(boolean isUpgrade) {
        if (NClick.isFastDoubleClick())
            return;
        final String mTelephone = mCetPhoneNumber.getText().toString();
        if (TextUtils.isEmpty(mCetReason.getText().toString().trim())) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_reason));
            return;
        } else if (null == mFromAddressBean) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_from_address));
            return;
        } else if (null == mToAddressBean) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_to_address));
            return;
        } else if (TextUtils.isEmpty(mTelephone) || mTelephone.length() != 11) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_phone_number));
            return;
        } else if ("00".equals(businessType)) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_business_type));
            return;
        }
        if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(mToAddressBean.lat, mToAddressBean.lng)) {
            //目的地地址有误
            MELogUtil.localE(MELogUtil.TAG_TAX, "DidiCallTaxiForBussinessFrament---- before callCarOrder, mToAddressBean:" + mToAddressBean.toString());
            MELogUtil.onlineE(MELogUtil.TAG_TAX, "DidiCallTaxiForBussinessFrament---- before callCarOrder, mToAddressBean:" + mToAddressBean.toString());
            mToAddressBean = CheckCoordinatesUtil.reverseAddressCoordinates(mToAddressBean);
        }
        if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(mFromAddressBean.lat, mFromAddressBean.lng)) {
            MELogUtil.localE(MELogUtil.TAG_TAX, "DidiCallTaxiForOvertimeFrament ---before jmeMobile/vehicle/getEstimatePrice, mFromAddressBean:" + mFromAddressBean);
            MELogUtil.onlineE(MELogUtil.TAG_TAX, "DidiCallTaxiForOvertimeFrament ---before jmeMobile/vehicle/getEstimatePrice, mFromAddressBean:" + mFromAddressBean);
            mFromAddressBean = CheckCoordinatesUtil.reverseAddressCoordinates(mFromAddressBean);
        }

        setCallCarEnable(false);

        if (isUpgrade) {
            SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, true) {
                @Override
                public void onSuccess(final ResponseInfo<String> info) {
                    super.onSuccess(info);
                    ResponseParser parser = new ResponseParser(info.result, getActivity(), false);
                    parser.parse(new ResponseParser.ParseCallback() {

                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            if (TextUtils.isEmpty(jsonObject.toString()))
                                return;
                            DidiCallTaxiUpgradeBean upgradeBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCallTaxiUpgradeBean.class);
                            if (!TextUtils.isEmpty(upgradeBean.didiurl)) {
                                Router.build(upgradeBean.didiurl).go(getActivity());
                                new Handler().postDelayed(new Runnable() {
                                    public void run() {
                                        setCallCarEnable(true);
                                    }
                                }, 500);
                                return;
                            }
                            if (!TextUtils.isEmpty(upgradeBean.url)) {
                                if (getActivity() != null) {
                                    Router.build(upgradeBean.url).go(getActivity());
                                    needClose = true;
                                }
                            }
                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {
                        }

                        @Override
                        public void parseError(String errorMsg) {
                            handleResultParseError(info);
                        }
                    });
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                }
            };
            callback.setNeedTranslate(true);
            NetWorkManager.callCarOrderUpgrade(this, callback, mFromAddressBean.lat, mFromAddressBean.lng,
                    mToAddressBean.lat, mToAddressBean.lng, "", "", mFromAddressBean.displayName,
                    mFromAddressBean.address, mToAddressBean.displayName, mToAddressBean.address, mFromAddressBean.cityCode,
                    mToAddressBean.cityCode, "", mDepartureTime, mDepartureDay, mTelephone, mCetReason.getText().toString(),
                    "2", businessType, priceBean, "0", "", null, mFromAddressBean.hypothetical);
            return;
        }

        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(getActivity(), true, true) { // 叫车
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        ToastUtils.showToast(R.string.me_didi_msg_order_send_succsess);
                        DidiCallTaxiBean didiCallTaxiBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCallTaxiBean.class);
                        if (!TextUtils.isEmpty(didiCallTaxiBean.orderId)) {
                            DidiUtils.getOrderDetail(getActivity(), didiCallTaxiBean.orderId, mTelephone, new DidiUtils.IDetailCallback() {
                                @Override
                                public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                                    if (null == didiOrderDetailBean) {
                                        ToastUtils.showToast(R.string.me_exception_order);
                                    } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
                                        ToastUtils.showToast(R.string.me_exception_order);
                                    } else {
                                        String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
                                        if (TextUtils.isEmpty(className)) {
                                            ToastUtils.showToast(R.string.me_exception_order_state);
                                        } else {
                                            Intent intent = new Intent(getActivity(), FunctionActivity.class);
                                            intent.putExtra("function", className);
                                            intent.putExtra("orderDetailBean", didiOrderDetailBean);
                                            intent.putExtra("useCarType", "business");
                                            startActivityForResult(intent, 300);
                                            getActivity().finish();
                                        }
                                    }
                                    new Handler().postDelayed(new Runnable() {
                                        public void run() {
                                            setCallCarEnable(true);
                                        }
                                    }, 500);
                                }
                            });
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        handleResultParseError(request);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                setCallCarEnable(true);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.callCarOrder(this, callback, mFromAddressBean.lat, mFromAddressBean.lng,
                mToAddressBean.lat, mToAddressBean.lng, "", "", mFromAddressBean.displayName,
                mFromAddressBean.address, mToAddressBean.displayName, mToAddressBean.address, mFromAddressBean.cityCode,
                mToAddressBean.cityCode, "", mDepartureTime, mDepartureDay, mTelephone, mCetReason.getText().toString(),
                "2", businessType, priceBean, "0", "", null, mFromAddressBean.hypothetical);
    }

    private void handleResultParseError(ResponseInfo<String> request) {
        try {
            JSONObject jsonObj = new JSONObject(request.result);
            JSONObject contentObj = jsonObj.optJSONObject("content");
            String isRefresh = contentObj.getString("isRefresh");
            if (!TextUtils.isEmpty(isRefresh) && "1".equals(isRefresh))
                getEstimate();
        } catch (JSONException e) {
            setCallCarEnable(true);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == 200) {
            if (requestCode == 100) {   // from
                mFromAddressBean = (DidiAddressBean) data.getSerializableExtra(DidiAddressSearchActivity.KEY_ADDRESS);
                mTvFrom.setText(mFromAddressBean.displayName);
                mTvFrom.setTextColor(getResources().getColor(R.color.black_252525));
            } else if (requestCode == 200) {    //to
                mToAddressBean = (DidiAddressBean) data.getSerializableExtra(DidiAddressSearchActivity.KEY_ADDRESS);
                mTvTo.setText(mToAddressBean.displayName);
                mTvTo.setTextColor(getResources().getColor(R.color.black_252525));
            }
            getEstimate();
        } else if (resultCode == 0) {
            getActivity().finish();
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 初始化
     */
    private void initView() {
        mTvFrom = mRootView.findViewById(R.id.tv_from); // 出发地
        mTvTo = mRootView.findViewById(R.id.tv_to); // 目的地

        mCetPhoneNumber = mRootView.findViewById(R.id.cet_telephone);
        mTvLeaveTime = mRootView.findViewById(R.id.tv_leave_time); // 出发时间
        mCetReason = mRootView.findViewById(R.id.cet_reason); // 加班原因
        mTvTipApproval = mRootView.findViewById(R.id.tv_tip_approval); // 审批提示
        TextView tv_upgrade_tip = mRootView.findViewById(R.id.tv_upgrade_tip); //自费升舱不可用提示文案
        tv_upgrade_tip.setText(expenseDisableText);
        ll_expend_disable_tip = mRootView.findViewById(R.id.ll_expend_disable_tip);
        mBtnCallTaxi = mRootView.findViewById(R.id.btn_call_taxi);
        mBtnCallTaxiUpgrade = mRootView.findViewById(R.id.btn_call_taxi_upgrade);
        mBtnCallTaxiUpgrade.setVisibility(upgradeEnable ? View.VISIBLE : View.GONE);
        mTvDaily = mRootView.findViewById(R.id.tv_daily); // 日常用车
        mLlDaily = mRootView.findViewById(R.id.ll_daily);
        mTvTravel = mRootView.findViewById(R.id.tv_travel); // 差旅用车
        mLlTravel = mRootView.findViewById(R.id.ll_travel);

        mLReserve = mRootView.findViewById(R.id.ll_reserve);

        mMajorThemeColor = R.color.skin_color_default;
        mMajorChecked = R.drawable.jdme_didi_bg_business_reason_check_default;
        mMajorBg = R.drawable.jdme_didi_bg_business_reason_default;
        estimateView = mRootView.findViewById(R.id.estimateView);

        progressDlg = new ProgressDialog(getActivity());
        progressDlg.setCanceledOnTouchOutside(false);
        progressDlg.setMessage(getActivity().getResources().getString(R.string.me_loading_not_translate));

        mTvFrom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), DidiAddressSearchActivity.class);
                intent.putExtra(DidiAddressSearchActivity.KEY_ADDRESS_TPYE, getSelAddressType(R.id.tv_from));
                intent.putExtra(DidiAddressSearchActivity.KEY_PHONE_NUMBER, mPhoneNumber);
                intent.putExtra(DidiAddressSearchActivity.KEY_REQUEST_CODE, 100);
                startActivityForResult(intent, 100);
            }
        });

        mTvTo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), DidiAddressSearchActivity.class);
                intent.putExtra(DidiAddressSearchActivity.KEY_ADDRESS_TPYE, getSelAddressType(R.id.tv_to));
                intent.putExtra(DidiAddressSearchActivity.KEY_PHONE_NUMBER, mPhoneNumber);
                intent.putExtra(DidiAddressSearchActivity.KEY_REQUEST_CODE, 200);
                startActivityForResult(intent, 200);
            }
        });

        mBtnCallTaxi.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                callTaxi(false);
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_CAR_BUSSINESS_CALL);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_leaveBusiness_callCar_click, JDMAConstants.mobile_employeeTravel_leaveBusiness_callCar_click);
            }
        });

        mBtnCallTaxiUpgrade.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_JDME_CALL_CAR_UPGRADE_TIPS_NO_MORE)) {
                    new CallCarUpgradeTipsDialog(getActivity())
                            .setOnCallClickListener(new CallCarUpgradeTipsDialog.OnConfirmClickListener() {
                                @Override
                                public void onConfirmClick() {
                                    callTaxi(true);
                                }
                            }).setTipsContent(mConfirmText).show();
                } else {
                    callTaxi(true);
                }
            }
        });

        mCetReason.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCetReason.showDropDown();
            }
        });

        mLlTravel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!"02".equals(businessType)) {
                    mLlTravel.setBackgroundResource(mMajorBg);
                    mTvTravel.setCompoundDrawablesWithIntrinsicBounds(getActivity().getResources().getDrawable(mMajorChecked), null, null, null);
                    mTvTravel.setTextColor(getResources().getColor(mMajorThemeColor));
                    mTvDaily.setCompoundDrawables(null, null, null, null);
                    mTvDaily.setTextColor(getResources().getColor(R.color.conference_black_color));
                    mLlDaily.setBackgroundResource(R.drawable.jdme_didi_bg_business_reason_gray);
                    businessType = "02";
                }
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_leaveBusiness_travelCallCar_click, JDMAConstants.mobile_employeeTravel_leaveBusiness_travelCallCar_click);

            }
        });

        mLlDaily.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!"01".equals(businessType)) {
                    mLlDaily.setBackgroundResource(mMajorBg);
                    mTvDaily.setCompoundDrawablesWithIntrinsicBounds(getActivity().getResources().getDrawable(mMajorChecked), null, null, null);
                    mTvDaily.setTextColor(getResources().getColor(mMajorThemeColor));
                    mLlTravel.setBackgroundResource(R.drawable.jdme_didi_bg_business_reason_gray);
                    mTvTravel.setCompoundDrawables(null, null, null, null);
                    mTvTravel.setTextColor(getResources().getColor(R.color.conference_black_color));
                    businessType = "01";
                }
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_leaveBusiness_dailyCallCar_click, JDMAConstants.mobile_employeeTravel_leaveBusiness_dailyCallCar_click);
            }
        });

        mLReserve.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DidiUtils.closeBoard(getActivity(), mCetPhoneNumber);
                DidiUtils.closeBoard(getActivity(), mCetReason);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_leaveBusiness_leaveTime_click, JDMAConstants.mobile_employeeTravel_leaveBusiness_leaveTime_click);
                showPopwindow();
            }
        });


        if (!TextUtils.isEmpty(mPhoneNumber))  // 手机号
            mCetPhoneNumber.setText(mPhoneNumber);

        mCetReason.setDropDownHeight(400);

        mCetPhoneNumber.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                } else {
//                    if (TextUtils.isEmpty(mTvEstimatePrice.getText().toString()))
                    getEstimate();
                }
            }
        });

        mCetReason.post(new Runnable() {
            @Override
            public void run() {
                mCetReason.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        if (hasFocus && getActivity() != null && !getActivity().isFinishing() && !getActivity().isDestroyed()) {
                            mCetReason.showDropDown();
                        }
                    }
                });
            }
        });
        setCallCarEnable(false);
        mTvTipApproval.setVisibility("1".equals(mIsAppropve) ? View.VISIBLE : View.GONE);


    }

    // 选择出发时间
    private void showPopwindow() {
        ReservationPopwindow mPop = new ReservationPopwindow(getActivity(), new ReservationPopwindow.IReserveCallback() {
            @Override
            public void onConfirmCallback(String day, String time) {
                if ("现在".indexOf(time) >= 0) {
                    mTvLeaveTime.setText(R.string.me_didi_now);
                    mDepartureTime = "";
                    mDepartureDay = "";
                } else if ("今天".equals(day)) {
                    mTvLeaveTime.setText(time);
                    mDepartureDay = "01";
                    mDepartureTime = time + ":00";
                    ToastUtils.showToastLong(R.string.me_didi_tip_reserved_order);
                } else {
                    mTvLeaveTime.setText(day + " " + time);
                    mDepartureDay = "02";
                    mDepartureTime = time + ":00";
                    ToastUtils.showToastLong(R.string.me_didi_tip_reserved_order);

                }
                getEstimate();
            }
        });
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
//                backgroundAlpha(1f);
            }
        });

        mPop.showAtLocation(mRootView, Gravity.BOTTOM, 0, 0);
//        backgroundAlpha(0.5f);
    }


//   PopWindow 不修改透明度
//    /**
//     * 设置添加屏幕的背景透明度
//     *
//     * @param bgAlpha
//     */
//    private void backgroundAlpha(float bgAlpha) {
//        WindowManager.LayoutParams lp = getActivity().getWindow().getAttributes();
//        lp.alpha = bgAlpha; //0.0-1.0
//        getActivity().getWindow().setAttributes(lp);
//    }

    /**
     * 获取预估价
     */
    private void getEstimate() {
        String mTelephone = mCetPhoneNumber.getText().toString();
        if (TextUtils.isEmpty(mTelephone) || mTelephone.length() != 11) {
            ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_phone_number));
            return;
        }
        if (null == mFromAddressBean || null == mToAddressBean || TextUtils.isEmpty(mTelephone)) {
            return;
        }
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(getActivity(), true, false) { // 获取预估价
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString())) {
                            return;
                        }
                        priceBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiEstimatePriceBean.class);
                        JSONArray estimatePriceList = jsonObject.optJSONArray("estimatePriceList");
                        priceBean.callbackParams = estimatePriceList != null ? estimatePriceList : new JSONArray();
                        if (priceBean.estimatePriceList == null || priceBean.estimatePriceList.isEmpty()) {
                            estimateView.setVisibility(View.GONE);
                            estimateView.setHasDidiEstimate(false);
                            setCallCarEnable(false);
                        } else {
                            estimateView.setVisibility(View.VISIBLE);
                            estimateView.setData(priceBean.estimatePriceList);
                            setCallCarEnable(true);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        estimatePriceFailedUI();
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                estimatePriceFailedUI();
            }
        };
        callback.setNeedTranslate(false);
        if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(mToAddressBean.lat, mToAddressBean.lng)) {
            MELogUtil.localE(MELogUtil.TAG_TAX, "DidiCallTaxiForBussinessFrament----before jmeMobile/vehicle/getEstimatePrice, mToAddressBean:" + mToAddressBean);
            MELogUtil.onlineE(MELogUtil.TAG_TAX, "DidiCallTaxiForBussinessFrament----before jmeMobile/vehicle/getEstimatePrice, mToAddressBean:" + mToAddressBean);
            mToAddressBean = CheckCoordinatesUtil.reverseAddressCoordinates(mToAddressBean);
        }
        NetWorkManager.getEstimatePrice(this, callback, mFromAddressBean.lat, mFromAddressBean.lng, mToAddressBean.lat, mToAddressBean.lng, mFromAddressBean.cityCode, "", mDepartureTime, mDepartureDay, mTelephone, mToAddressBean.cityCode, "2");
    }

    /**
     * 获取预估价失败隐藏预估价展示区域 叫车按钮置灰
     */
    private void estimatePriceFailedUI() {
        estimateView.setVisibility(View.GONE);
        setCallCarEnable(false);
    }

    @Override
    public void onLocated(String lat, String lng, String name, String cityName) {
        getLocationName(lat, lng);
    }

    @Override
    public void onFailed() {

    }

    private void getLocationName(String lat, String lng) {
        final Map<String, Object> params = new HashMap<>();
        params.put("lat", lat);
        params.put("lng", lng);
        NetWorkManager.request(null, Constant.getConstant(NetWorkManager.mReqeustType).API_GETADDRESSBYCURLOCATION, new SimpleReqCallbackAdapter<>(new AbsReqCallback<DiDiAddressListWrapper>(DiDiAddressListWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(DiDiAddressListWrapper wrapper, List<DiDiAddressListWrapper> tArray, String rawData) {
                super.onSuccess(wrapper, tArray, rawData);
                if (wrapper != null && wrapper.getPositionList() != null && wrapper.getPositionList().size() > 0) {
                    DidiAddressBean position = wrapper.getPositionList().get(0);
                    if (position != null) {
                        mFromAddressBean = position;
                        mFromAddressBean.cityCode = wrapper.getCityCode();
                        mTvFrom.setText(mFromAddressBean.displayName);
                        mTvFrom.setTextColor(getResources().getColor(R.color.black_252525));
                    }

                }
            }
        }), params);
    }

    private void setCallCarEnable(boolean enable) {
        mBtnCallTaxi.setEnabled(enable);
        if (upgradeEnable) {
            //无滴滴预估价时 显示底部升舱不可用提示文案
            if (!estimateView.hasDidiEstimate() && View.VISIBLE == estimateView.getVisibility()) {
                mBtnCallTaxiUpgrade.setEnabled(false);
                ll_expend_disable_tip.setVisibility(View.VISIBLE);
            } else {
                mBtnCallTaxiUpgrade.setEnabled(enable);
                ll_expend_disable_tip.setVisibility(View.GONE);
            }
        }
    }
}