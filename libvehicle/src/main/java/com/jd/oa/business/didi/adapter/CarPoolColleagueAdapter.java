package com.jd.oa.business.didi.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;

import java.util.List;
import java.util.Locale;

/**
 * Created by peidongbiao on 2019/4/3
 */
public class CarPoolColleagueAdapter extends BaseRecyclerAdapter<CarPoolUserStatus, RecyclerView.ViewHolder> {


    public CarPoolColleagueAdapter(Context context) {
        super(context);
    }

    public CarPoolColleagueAdapter(Context context, List<CarPoolUserStatus> data) {
        super(context, data);
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_didi_car_pool_colleague, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        CarPoolUserStatus status = getItem(position);
        viewHolder.title.setText(status.isCallCarUser()? R.string.me_car_pool_call_car_user : R.string.me_car_pool_colleague);
        viewHolder.name.setText(String.format(Locale.getDefault(), "%s（%s）", status.getRealName(), status.getUserName()));
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        TextView title;
        TextView name;

        public ViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
            name = itemView.findViewById(R.id.tv_name);
        }
    }
}