package com.jd.oa.business.didi;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.chenenyu.router.annotation.Interceptor;
import com.jd.oa.AppBase;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

@Interceptor(value = "CarOrderInterceptor")
public class CarOrderInterceptor implements RouteInterceptor {
//    private Activity mActvity;
    private Context mContext;

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        mContext = chain.getContext();
        getOrderDetail(chain.getRequest().getExtras().getString("orderId"));
        return chain.intercept();
    }

    private void getOrderDetail(String orderId) {
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(mContext, true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, mContext);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiOrderDetailBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiOrderDetailBean.class);
                        DidiUtils.processResult(AppBase.getTopActivity(), didiAddressBean);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_didi_exception_order_state);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getOrderDetail(null, callback, orderId, "");
    }
}