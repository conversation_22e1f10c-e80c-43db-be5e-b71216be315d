package com.jd.oa.business.didi;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.caraddress.CarAddressActivity;
import com.jd.oa.business.didi.adapter.DidiOrderListLoadMoreAdapter;
import com.jd.oa.business.didi.dialog.CarPoolDescriptionDialog;
import com.jd.oa.business.didi.model.DidiOrderBean;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.model.DidiOrderListBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.workbench.timingtask.DakaUseCarTimingTask;
import com.jd.oa.business.workbench.timingtask.TimingTaskUtils;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.LinkedList;
import java.util.List;

// title = R.string.me_didi_main_title

/**
 * 滴滴主页
 *
 * <AUTHOR>
 */
@Navigation(hidden = false, displayHome = true)
public class DidiMainFragment extends BaseFragment implements
        FragmentOperatingListener {

    private static final String TAG = "DidiMainFragment";

    public static final String TYPE = "type";
    public static final String TYPE_OVERTIME = "overTime";
    public static final String TYPE_BUSINESS = "business";
    private DidiOrderListLoadMoreAdapter mRecyclerAdapter;
    private LinkedList<DidiOrderBean> mOrderList = new LinkedList<>();
    private RecyclerViewOnLoadMoreListener loadMoreListener;
    private boolean isShowOnGoingDialog = true;

    private RelativeLayout rl_didi_overtime;

    private RelativeLayout rl_didi_business;

    private RecyclerView mRecyclerView;

    private SwipeRefreshLayout mSwipeRefreshLayout;

    private TextView tv_didi_main_condition;

    private TextView tv_didi_title;

    private FrameView mFrameView;

    private TextView tv_didi_order_list;

    private Intent intent;


    // 加载失败的 runnable
    private final Runnable run = new Runnable() {
        @Override
        public void run() {
            getOrderList();
        }
    };

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View mRootView = inflater.inflate(R.layout.jdme_fragment_didi_main,
                container, false);
//        ActionBarHelper.init(this, mRootView);

        initView(mRootView);

        mRecyclerAdapter = new DidiOrderListLoadMoreAdapter(getActivity(), mOrderList);
        mRecyclerView.setAdapter(mRecyclerAdapter);
        mRecyclerAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<DidiOrderBean>() {
            @Override
            public void onItemClick(View view, int position, DidiOrderBean item) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (mSwipeRefreshLayout.isRefreshing()) {
                    return;
                }
                getOrderDetail(item.getOrderId(), item.phoneNumber,getActivity());
            }

            @Override
            public void onItemLongClick(View view, int position, DidiOrderBean item) {
                //Do nothing
            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));

        loadMoreListener = new RecyclerViewOnLoadMoreListener(mSwipeRefreshLayout, mRecyclerAdapter) {
            @Override
            public void onLoadMore() {
                //首页不需要加载更多的功能
            }
        };

        mRecyclerView.addOnScrollListener(loadMoreListener);

        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));

        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                getOrderList();
            }
        });
        mSwipeRefreshLayout.setColorSchemeResources(R.color.skin_color_default);
        loadMoreListener.loadAllData(true);

        handleUseCarType();
        //同乘说明弹窗
//        boolean shown = PreferenceManager.getBoolean(PreferenceManager.UserInfo.KEY_SHOW_CAR_POOL_DIALOG);
//        boolean shown = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_SHOW_CAR_POOL_DIALOG);
        boolean shown = TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_SHOW_CAR_POOL_DIALOG);
        //暂不显示弹窗
        shown = true;
        if (!shown) {
            //不显示正在进行中弹窗
            final boolean showOnGoingDialog = isShowOnGoingDialog;
            isShowOnGoingDialog = false;
            CarPoolDescriptionDialog dialog = new CarPoolDescriptionDialog(getContext());
            dialog.setOnConfirmClickListener(new CarPoolDescriptionDialog.OnConfirmClickListener() {
                @Override
                public void onClick() {
//                    PreferenceManager.setBoolean(PreferenceManager.UserInfo.KEY_SHOW_CAR_POOL_DIALOG, true);
//                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SHOW_CAR_POOL_DIALOG,true);
                    TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_SHOW_CAR_POOL_DIALOG, true);
                    if (showOnGoingDialog) {
                        isShowOnGoingDialog = true;
                        getOrderList();
                    }
                }
            });
            dialog.show();
        }
        getOrderList();

        return mRootView;
    }

    private void initView(View mRootView) {
        rl_didi_overtime = mRootView.findViewById(R.id.rl_didi_overtime);
        rl_didi_business = mRootView.findViewById(R.id.rl_didi_business);
        mRecyclerView = mRootView.findViewById(R.id.recycler_view_didi_order_list);
        mSwipeRefreshLayout = mRootView.findViewById(R.id.swipe_refresh_layout_didi_main);
        tv_didi_main_condition = mRootView.findViewById(R.id.tv_didi_main_condition);
        tv_didi_title = mRootView.findViewById(R.id.tv_didi_title);
        mFrameView = mRootView.findViewById(R.id.fv_view);
        tv_didi_order_list = mRootView.findViewById(R.id.tv_didi_main_all_order_list_entrance);
        rl_didi_overtime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkChoseCarType("1",getActivity(),mOrderList);
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_CAR_OVERTIME);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_overTime_click, JDMAConstants.mobile_employeeTravel_overTime_click);
            }
        });
        rl_didi_business.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkChoseCarType("2",getActivity(),mOrderList);
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_CAR_BUSSINESS);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_leaveBusiness_click, JDMAConstants.mobile_employeeTravel_leaveBusiness_click);
            }
        });

        tv_didi_main_condition.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(DidiMainFragment.this.getActivity(), CarAddressActivity.class);
                startActivity(intent);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_commonAddress_click, JDMAConstants.mobile_employeeTravel_commonAddress_click);
            }
        });

        tv_didi_title.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                createDescriptionDialog().show();
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_useCarType_click, JDMAConstants.mobile_employeeTravel_useCarType_click);
            }
        });

        tv_didi_order_list.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(DidiMainFragment.this.getActivity(), FunctionActivity.class);
                intent.putExtra("function", DidiAllOrderListFragment.class.getName());
                startActivity(intent);

            }
        });
    }

    private void handleUseCarType() {
        String type = null;
        if (getArguments() != null) {
            type = getArguments().getString(TYPE);
        }
        if (TYPE_OVERTIME.equals(type)) {
            checkChoseCarType("1",getActivity(),mOrderList);
        } else if (TYPE_BUSINESS.equals(type)) {
            checkChoseCarType("2",getActivity(),mOrderList);
        }
    }

    private void getOrderList() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), false, true) {
            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                mSwipeRefreshLayout.setRefreshing(false);
            }

            @Override
            public void onStart() {
                super.onStart();
                mSwipeRefreshLayout.setRefreshing(true);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mSwipeRefreshLayout.setRefreshing(false);
                mFrameView.setRepeatRunnable(run, null);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                mSwipeRefreshLayout.setRefreshing(false);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            DidiOrderListBean bean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiOrderListBean.class);
                            if (bean.orderList.size() <= 0) {
                                showEmpty();
                            } else {
                                mFrameView.setContainerShown(true);
                                mOrderList.clear();
                                mOrderList.addAll(bean.orderList);

                                if (isShowOnGoingDialog) {
                                    checkOnGoingOrder();
                                    isShowOnGoingDialog = false;
                                }
                            }

                            mRecyclerAdapter.notifyDataSetChanged();
                        } catch (Exception e) {
                            Logger.d(MELogUtil.TAG_TAX, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getOrderList(this, callback, "1", "5");
    }


    /**
     * 检查正在进行中的行程，弹出对话框提示跳转
     */
    private void checkOnGoingOrder() {
        final DidiOrderBean bean = getOnGoingOrder(mOrderList);
        if (bean == null) return;
        new IosAlertDialog(getActivity()).builder().setMsg(getString(R.string.me_join_exist_car))
                .setNegativeButton(getString(R.string.me_no_join), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                    }
                })
                .setPositiveButton(getString(R.string.me_join_journey), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        getOrderDetail(bean.getOrderId(), bean.phoneNumber,getActivity());
                    }
                })
                .show();
    }

    /**
     * 获取正在进行中的订单
     *
     * @return
     */
    @Nullable
    private static DidiOrderBean getOnGoingOrder(List<DidiOrderBean> orderList) {
        for (final DidiOrderBean bean : orderList) {
            if (DidiUtils.STATE_ONGOING.equals(bean.getFlowFlag()) ||
                    DidiUtils.CAR_POOL_STATE_CONFIRMING.equals(bean.getFlowFlag()) ||
                    DidiUtils.CAR_POOL_STATE_APPROVED.equals(bean.getFlowFlag()) ||
                    DidiUtils.CAR_POOL_STATE_ONGOING.equals(bean.getFlowFlag())) {
                return bean;
            }
        }
        return null;
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
//        ActionBarHelper.initActionBar(this);
    }

    private static void gotoCallTaxiPage(Bundle bundle,Activity activity) {
        Intent intent = new Intent(activity, FunctionActivity.class);
        String workDateType = bundle.getString("workDateType");  // 0 平日加班 1 假日加班第一次 2 假日加班第二次 3 因公外出
        intent.putExtra("isApprove", bundle.getString("isApprove"));
        intent.putExtra("workDateType", workDateType);
        intent.putExtra("phoneNumber", bundle.getString("phoneNumber"));
        intent.putExtra("confirmText", bundle.getString("confirmText"));
        intent.putExtra("expenseDisableText", bundle.getString("expenseDisableText"));
        if ("3".equals(workDateType)) {
            intent.putExtra("function", DidiCallTaxiForBussinessFrament.class.getName());
        } else {
            intent.putExtra("function", DidiCallTaxiForOvertimeFrament.class.getName());
            intent.putExtra("openCarPool", bundle.getBoolean("openCarPool"));
        }
        activity.startActivity(intent);
    }

    public Dialog createDescriptionDialog() {
        final Dialog dialog = new Dialog(getActivity());
        WindowManager m = getActivity().getWindowManager();
        Display d = m.getDefaultDisplay(); // 为获取屏幕宽、高
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().setBackgroundDrawableResource(R.color.transparent);
        LayoutInflater inflater = (LayoutInflater) getActivity()
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        final View view = inflater.inflate(R.layout.jdme_didi_condition_popup, null);
        WebView webView = (WebView) view.findViewById(R.id.webview_didi_condition_popup);
        webView.getSettings().setAllowFileAccessFromFileURLs(false);
        webView.getSettings().setAllowUniversalAccessFromFileURLs(false);
        webView.setWebViewClient(new WebViewClient() {
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                //  重写此方法表明点击网页里面的链接还是在当前的webview里跳转，不跳到浏览器那边
                view.loadUrl(url);
                return true;
            }
        });
        ImageButton imgbtn = (ImageButton) view.findViewById(R.id.img_btn_didi_close_poup);
        imgbtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setContentView(view);
        WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
        p.height = (int) (d.getHeight() * 0.8); // 高度设置为屏幕的比例
        p.width = (int) (d.getWidth() * 0.9); // 宽度设置为屏幕的比例
        dialog.getWindow().setAttributes(p); // 设置生效
        webView.loadUrl(NetworkConstant.PARAM_SERVER_OUTTER + Constant.API_DIDI_CONDITION);
        return dialog;
    }


    public static void checkChoseCarType(final String useCarType, Activity activity,List<DidiOrderBean> orderList) {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(activity, R.string.me_loading_message_not_translate) {
            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, activity);
                parser.parse(new ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String isCanUse = jsonObject.optString("isCanUse");
                            String isApprove = jsonObject.optString("isApprove");
                            String workDateType = jsonObject.optString("workDateType");
                            final String phoneNumber = jsonObject.optString("phoneNumber");
                            String showMsg = jsonObject.optString("showMsg");
                            final String needDaka = jsonObject.optString("isPunch");
                            String reminderTime = jsonObject.optString("remindTime");
                            String isCheckDetail = jsonObject.optString("isCheckDetail");
                            final String orderId = jsonObject.optString("orderId");
                            boolean openCarPool = "1".equals(jsonObject.optString("openCarPool"));
                            String confirmText = jsonObject.optString("confirmText");
                            String expenseDisableText = jsonObject.optString("expenseDisableText");
                            final Bundle bundle = new Bundle();
                            bundle.putString("isCanUse", isCanUse);
                            bundle.putString("isApprove", isApprove);
                            bundle.putString("workDateType", workDateType);
                            bundle.putString("phoneNumber", phoneNumber);
                            bundle.putString("useCarType", useCarType);
                            bundle.putBoolean("openCarPool", openCarPool);
                            bundle.putString("confirmText", confirmText);
                            bundle.putString("expenseDisableText", expenseDisableText);
                            if ("0".equals(isCanUse)) {// 不符合打车情况
                                if (TextUtils.equals("1", needDaka) || !TextUtils.isEmpty(reminderTime)) {
                                    try {
                                        long tipTimeMillis = System.currentTimeMillis();
                                        if (!TextUtils.isEmpty(reminderTime)) {
                                            int second = Integer.parseInt(reminderTime);
                                            tipTimeMillis = System.currentTimeMillis() + second * 1000;
                                            DakaUseCarTimingTask task = new DakaUseCarTimingTask();
                                            TimingTaskUtils.set(activity, tipTimeMillis, task);
                                        }
//                                        PreferenceManager.UserInfo.setUserDakaUseCarTipDate(tipTimeMillis);
                                        TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE, tipTimeMillis);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }

                                new IosAlertDialog(activity).builder().setMsg(showMsg)
                                        .setPositiveButton(activity.getString(R.string.me_ok_not_translate), new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {
                                                if ("1".equals(needDaka)) {
                                                    Router.build(DeepLink.WORKBENCH_OLD).go(activity);
                                                }
                                            }
                                        })
                                        .show();
                            } else if ("1".equals(isApprove) && !TextUtils.isEmpty(showMsg)) {// 不符合打车情况
                                new IosAlertDialog(activity).builder().setMsg(showMsg)
                                        .setNegativeButton(activity.getString(R.string.me_use_continue_not_translate), new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {
                                                gotoCallTaxiPage(bundle,activity);
                                            }
                                        })
                                        .setPositiveButton(activity.getString(R.string.me_cancel_not_translate), new View.OnClickListener() {

                                            @Override
                                            public void onClick(View v) {

                                            }
                                        }).show();
                            } else if ("1".equals(isCheckDetail) && !TextUtils.isEmpty(showMsg)) {  //正在进行中的订单
                                new IosAlertDialog(activity).builder().setMsg(showMsg)
                                        .setNegativeButton(activity.getString(R.string.me_cancel), new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {

                                            }
                                        })
                                        .setPositiveButton(activity.getString(R.string.me_car_to_order_detail), new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {
                                                if (!TextUtils.isEmpty(orderId) && !TextUtils.isEmpty(phoneNumber)) {
                                                    getOrderDetail(orderId, phoneNumber,activity);
                                                } else {
                                                    DidiOrderBean bean = getOnGoingOrder(orderList);
                                                    if (bean == null) {
                                                        Log.e(TAG, "isCheckDetail, no ongoing order");
                                                        return;
                                                    }
                                                    getOrderDetail(bean.getOrderID(), bean.phoneNumber,activity);
                                                }
                                            }
                                        }).show();

                            } else {
                                gotoCallTaxiPage(bundle,activity);
                            }
                        } catch (Exception e) {
                            Logger.d(activity, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {

                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.checkChoseCarType(activity, callback, useCarType);
    }

    private void showEmpty() {
        mFrameView.setEmptyInfo(mFrameView.getContext().getString(R.string.me_no_order));
        mFrameView.setEmptyShown(true);
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
        getOrderList();
        if (getActivity() != null)
            JDMAUtils.onEventPagePV(getActivity(), JDMAConstants.mobile_employeeTravel, JDMAConstants.mobile_employeeTravel);
    }

    /**
     * 获取订单详情并跳转相应的详情页
     *
     * @param mOrderId     订单编号
     * @param mPhoneNumber 用户手机号
     */
    private static void getOrderDetail(String mOrderId, String mPhoneNumber,Activity activity) {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(activity, true, false) { // 获取职场地址
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, activity);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiOrderDetailBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiOrderDetailBean.class);
                        DidiUtils.processResult(activity, didiAddressBean);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_exception_order_state);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getOrderDetail(null, callback, mOrderId, mPhoneNumber);
    }

}
