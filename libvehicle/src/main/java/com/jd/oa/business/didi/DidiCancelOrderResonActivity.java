package com.jd.oa.business.didi;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiCancelOrderReasonBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

@Route(DeepLink.CAR_ORDER_CANCEL)
public class DidiCancelOrderResonActivity extends BaseActivity {

    private Context mContext;

    private View mRootView; // 根View

    private String orderId;

    private LinearLayout mLlContainer;
    private Button mBtnSubmit;

    private DidiCancelOrderReasonBean didiCancelOrderReasonBean;

    private FrameView mFrameView;

    private int mMajorThemeColor = R.color.skin_color_default;
    private int mMajorChecked;

    private int mCheckedId = -1;

    private Map<String, String> mMapId;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mRootView = getLayoutInflater().inflate(R.layout.jdme_activity_didi_cancel_reason, null, false);
        mLlContainer = mRootView.findViewById(R.id.ll_container);
        mBtnSubmit = mRootView.findViewById(R.id.btn_submit);
        mBtnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveCancelOrderReason();
            }
        });
        setContentView(mRootView);
        mContext = this;
        getSupportActionBar().hide();
        init();
    }

    private void init() {
        mMapId = new HashMap<>();

        mMajorThemeColor =  R.color.skin_color_default;
        mMajorChecked =R.drawable.jdme_didi_icon_checked_default;

        orderId = getIntent().getStringExtra("orderId");
        mFrameView = findViewById(R.id.fv_frame);
        mFrameView.setRepeatRunnable(new Runnable() {
            @Override
            public void run() {
                getCancelOrderReasonList();
            }
        },getString(R.string.me_didi_title_cancel_reson_faile));
        getCancelOrderReasonList();

    }

    private void initView() {
        for (DidiCancelOrderReasonBean.ReasonList reasonList : didiCancelOrderReasonBean.reasonGroupList) {
            for (int i = 0; i < reasonList.reasonList.length; i++) {
                TextView mTmp = (TextView) LayoutInflater.from(mContext).inflate(R.layout.jdme_item_didi_cancel_reason_textview, null);
                mTmp.setText(reasonList.reasonList[i].text);
                mTmp.setId(StringUtils.convertToInt(reasonList.reasonList[i].code));
                mMapId.put(reasonList.reasonList[i].code, reasonList.reasonList[i].id);
                mLlContainer.addView(mTmp);
                View mSplit = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_didi_cancel_reason_split, null);
                mLlContainer.addView(mSplit);
                mTmp.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mCheckedId == v.getId())
                            return;
                        TextView view = (TextView) v;
                        view.setTextColor(getResources().getColor(mMajorThemeColor));
                        view.setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(mMajorChecked), null);
                        if (mCheckedId != -1) {
                            TextView mTvChecked = (TextView) mRootView.findViewById(mCheckedId);
                            mTvChecked.setTextColor(getResources().getColor(R.color.black_main_summary));
                            mTvChecked.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                        }
                        mCheckedId = v.getId();
                    }
                });
            }
            View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_didi_cancel_reason_blank, null);
            mLlContainer.addView(view);
        }
    }

    /**
     * 获取评价标签
     */
    private void getCancelOrderReasonList() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(mContext, true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, mContext, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        // 取消原因
                        didiCancelOrderReasonBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCancelOrderReasonBean.class);
                        initView();
                        mFrameView.setContainerShown(true);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        mFrameView.setRepeatShown(true);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mFrameView.setRepeatShown(true);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getCancelOrderReasonList(this, callback);
    }

    private void saveCancelOrderReason() {
        String resonId = mMapId.get(mCheckedId + "");
        if (TextUtils.isEmpty(resonId)) {
            ToastUtils.showToast(R.string.me_didi_msg_please_sel_reason);
            return;
        }
        if (TextUtils.isEmpty(orderId)) {
            return;
        }
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(mContext, true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, mContext, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        setResult(300);
                        finish();
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        ToastUtils.showToast(R.string.me_didi_msg_submit_failed);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_didi_msg_submit_failed);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.saveCancelOrderReason(this, callback, orderId, resonId);
    }

    @Override
    public void onBackPressed() {
        // return;
    }
}
