package com.jd.oa.business.didi.net.constant;

import com.jd.oa.network.RequestType;

public class Constant {

    /**
     * 员工用车， 用车条件H5地址
     */
    public static String API_DIDI_PROTOCPOL = "/vehicle/protocol.html";
    public static String API_DIDI_CONDITION = "/vehicle/condition.html";

    //提交费用异议
    public String API_ME_VEHICLE_SUBMITFEEOBJECTION = "jmeMobile/vehicle/submitFeeObjection";
    //用车急救
    public String API_CAR_CALL_HELP = "jmeMobile/vehicle/callHelp";
    // 协议状态
    public String API_GETAGREEMENTSTATUS = "jmeMobile/vehicle/getAgreementStatus";
    // 用车协议
    public String API_PLUSAGREEMENTSTATUS = "jmeMobile/vehicle/getVehiclePlusAgreementStatus";
    // 签署协议
    public String API_SIGNAGREEMENT = "jmeMobile/vehicle/signAgreement";

    public String API_CHECKCHOSECARTYPE = "jmeMobile/vehicle/checkChoseCarType";

    public String API_GETJOBADDRESSBYLOCAL = "jmeMobile/vehicle/getJobAddressByLocal";

    public String API_GETADDRESSBYINPUT = "jmeMobile/vehicle/getAddressByInput";
    // 获取预估计
    public String API_GETESTIMATEPRICE = "jmeMobile/vehicle/getEstimatePrice";

    public String API_CHECKCARPOOLUSER = "jmeMobile/vehicle/checkCarPoolUser";

    public String API_CALLCARORDER = "jmeMobile/vehicle/callCarOrder";

    public String API_PRECALLCARORDERFORPOOL = "jmeMobile/vehicle/preCallCarOrderForPool";

    public String API_AFTERCALLCARORDERFORPOOL = "jmeMobile/vehicle/afterCallCarOrderForPool";

    public String API_GETCARPOOLORDERSTATUS = "jmeMobile/vehicle/getCarPoolOrderStatus";

    public String API_GETORDERLIST = "jmeMobile/vehicle/getOrderList";
    //  重叫
    public String API_RECALLCARORDER = "jmeMobile/vehicle/reCallCarOrder";
    // 获取订单详情
    public String API_GETORDERDETAIL = "jmeMobile/vehicle/getOrderDetail";
    // 取消订单
    public String API_CANCELORDER = "jmeMobile/vehicle/cancelOrder";
    //分享订单
    public String API_SHAREORDER = "vehicle.outer.getTripShareDetail";

    public String API_GETCARPOOLUSERSTATUS = "jmeMobile/vehicle/getCarPoolUserStatus";

    public String API_CONFIRMFORORDER = "jmeMobile/vehicle/confirmForOrder";
    // 检查城市是否可用
    public String API_CHECKCITY = "jmeMobile/vehicle/checkCity";
    // 确认行程
    public String API_AGREEPAY = "jmeMobile/vehicle/agreePay";
    // 带有异常原因的确认行程
    public String API_AGREECONFIRMPAY = "jmeMobile/vehicle/agreeConfirmPay";
    //取消原因列表
    public String API_GETCANCELORDERREASONLIST = "jmeMobile/vehicle/getCancelOrderReasonList";
    //保存取消原因
    public String API_SAVECANCELORDERREASON = "jmeMobile/vehicle/saveCancelOrderReason";
    // 投诉选项列表
    public String API_GETCOMPLAINTREASONLIST = "jmeMobile/vehicle/getComplaintReasonList";
    // 提交投诉
    public String API_SUBMITCOMPLAINT = "jmeMobile/vehicle/submitComplaint";
    // 评价标签
    public String API_GETCOMMENTLABEL = "jmeMobile/vehicle/getCommentLabel";
    // 提交评价
    public String API_SUBMITCOMMENT = "jmeMobile/vehicle/submitComment";
    // 延缓支付
    public String ACTION_COMMIT_FEE = "jmeMobile/vehicle/dissentPay";

    public String API_GETADDRESSBYCURLOCATION = "jmeMobile/vehicle/getAddressByCurLocation";

    /**
     * 常用地址
     */
    public String API_GET_USER_ADDRESS_LIST = "jmeMobile/vehicle/getUserAddressList";
    public String API_ADD_USER_ADDRESS = "jmeMobile/vehicle/addUserAddress";
//    public String API_MODIFY_USER_ADDRESS = "jmeMobile/vehicle/modifyUserAddress";
    public String API_MODIFY_USER_ADDRESS = "vehicle.outer.modifyUserCommonAddress";
    public String API_CHECK_CITY = "jmeMobile/vehicle/checkCity";
    public String API_CHECK_USER_COMMOM_ADDRESS = "jmeMobile/vehicle/checkUserCommonAddress";
    public String API_RE_APPROVE = "vehicle.outer.reApprove";//重新发起用车审批流程
    public String API_USER_CONFIRM_PROCESS = "vehicle.outer.userConfirmProcess";//用户确认领导驳回结果

    public String API_CALL_CAR_ORDER_UPGRADE = "vehicle.outer.checkOwnExpense";//自费升舱校验

    public static Constant getConstant(RequestType type) {
        return new GatewayConstant();
    }
}
