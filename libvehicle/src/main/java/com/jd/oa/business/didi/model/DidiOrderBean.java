package com.jd.oa.business.didi.model;

import android.os.Parcel;
import android.os.Parcelable;


/**
 * didi订单Bean
 */

public class DidiOrderBean implements Parcelable {
    private String orderId; // 打车订单ID
    private String createTime; // "2015-08-12 22:42",//创建时间
    private String startName; // 出发位置
    private String endName; // 结束位置
    private String totalPrice; // 订单金额
    private String flowFlag; // 订单状态码,
    private String statusDesc; // 订单状态描述100：进行中，200：待审批， 300：待还款，400：已完成，500：已取消
    public String phoneNumber; //电话号
    public String isCarPool;
    public String userName;
    public String orderType = "";// 预约单:1
    public String appointmentTime;//预约时间
    public String ownExpense;//自费升舱订单标识


    public DidiOrderBean(String endName, String totalPrice, String createTime,
                         String orderID, String startName, String flowFlag, String statusDesc) {
        this.endName = endName;
        this.totalPrice = totalPrice;
        this.createTime = createTime;
        this.orderId = orderID;
        this.startName = startName;
        this.flowFlag = flowFlag;
        this.statusDesc = statusDesc;
    }

    public boolean isOwnExpense() {
        return "1".equals(ownExpense);
    }

    public boolean isAppointment() {
        return "1".equals(orderType);
    }

    public String getFlowFlag() {
        return flowFlag;
    }

    public void setFlowFlag(String flowFlag) {
        this.flowFlag = flowFlag;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public String getEndName() {
        return endName;
    }

    public void setEndName(String endPlace) {
        this.endName = endPlace;
    }

    public void setTotalPrice(String orderAmount) {
        this.totalPrice = orderAmount;
    }

    public String getCreateTime() {
        return isAppointment() ? appointmentTime : createTime;
    }

    public void setCreateTime(String orderDate) {
        this.createTime = orderDate;
    }

    public String getOrderID() {
        return orderId;
    }

    public void setOrderID(String orderID) {
        this.orderId = orderID;
    }

    public String getStartName() {
        return startName;
    }

    public void setStartName(String startPlace) {
        this.startName = startPlace;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(orderId);
        dest.writeString(startName);
        dest.writeString(endName);
        dest.writeString(createTime);
        dest.writeString(totalPrice);
        dest.writeString(flowFlag);
        dest.writeString(statusDesc);
        dest.writeString(orderType);
        dest.writeString(appointmentTime);
        dest.writeString(ownExpense);
    }

    public static final Creator<DidiOrderBean> CREATOR = new Creator<DidiOrderBean>() {
        @Override
        public DidiOrderBean createFromParcel(Parcel in) {
            return new DidiOrderBean(in);
        }

        @Override
        public DidiOrderBean[] newArray(int size) {
            return new DidiOrderBean[size];
        }
    };

    public DidiOrderBean(Parcel in) {
        orderId = in.readString();
        startName = in.readString();
        endName = in.readString();
        createTime = in.readString();
        totalPrice = in.readString();
        flowFlag = in.readString();
        statusDesc = in.readString();
        ownExpense = in.readString();
    }
}

