package com.jd.oa.business.didi;

import androidx.fragment.app.Fragment;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.SingleFragmentActivity;
import com.jd.oa.router.DeepLink;

@Route(value = {DeepLink.CAR_ORDER_ID,DeepLink.CAR_ORDER_ID_NEW}, interceptors = "CarOrderInterceptor")
public class DidiOrderDetailActivity extends SingleFragmentActivity {

    @Override
    protected Fragment createFragment() {
        return new DidiOrderDetailFragment();
    }
}
