package com.jd.oa.business.didi;

import android.annotation.TargetApi;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/1/21.
 */
public class DidiPollingUtils {

    public static void startPollingService(final Context context, String orderId, String phoneNumber, int seconds, boolean isRepeating) {
//        AlarmManager manager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        final Intent intent = new Intent(context, DidiPollingService.class);
        intent.putExtra(DidiPollingService.KEY_ORDER_ID, orderId);
        intent.putExtra(DidiPollingService.KEY_PHONE_NUMBER, phoneNumber);
        intent.putExtra(DidiPollingService.KEY_SLEEP_SECOND, seconds);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    context.startForegroundService(intent);
                }
            });
        } else {
            context.startService(intent);
        }

//        PendingIntent pendingIntent = PendingIntent.getService(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
//        manager.cancel(pendingIntent);
//        long nowTime = SystemClock.elapsedRealtime();
//        if (Build.VERSION.SDK_INT >= 19)
//            setAlarmNewApi(manager, pendingIntent, nowTime + seconds * 1000, (long) seconds * 1000, isRepeating);
//        else
//            setAlarmOldApi(manager, pendingIntent, nowTime + seconds * 1000, (long) seconds * 1000, isRepeating);
    }

    public static void stopPollingService(Context context) {
//        AlarmManager manager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        Intent intent = new Intent(context, DidiPollingService.class);
//        PendingIntent pendingIntent = PendingIntent.getService(context, 0,
//                intent, PendingIntent.FLAG_UPDATE_CURRENT);
//        manager.cancel(pendingIntent);

        context.stopService(intent);

    }

}
