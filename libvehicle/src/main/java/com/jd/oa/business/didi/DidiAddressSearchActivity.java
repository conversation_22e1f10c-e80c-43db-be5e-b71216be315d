package com.jd.oa.business.didi;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.R;
import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.business.didi.adapter.AddressSearchResultTypeAdapter;
import com.jd.oa.business.didi.adapter.AddressSearchTitleAdapter;
import com.jd.oa.business.didi.adapter.CarAddressTypeAdapter;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.didi.model.DidiCityBean;
import com.jd.oa.business.didi.model.DidiCityInfoBean;
import com.jd.oa.business.didi.model.DidiListAddress;
import com.jd.oa.business.didi.model.DidiLocationAddressBean;
import com.jd.oa.business.didi.utils.CheckCoordinatesUtil;
import com.jd.oa.business.didi.utils.ConvertUtils;
import com.jd.oa.melib.utils.PermissionUtils;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.MultiTypeRecyclerAdapter;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * 地址搜索
 */
public class DidiAddressSearchActivity extends BaseActivity implements SosoLocationChangeInterface, View.OnClickListener {

    // 地址类型
    public static final int ADDRESS_TYPE_BUSSINESS = 0; // 办公地
    public static final int ADDRESS_TYPE_NORMAL = 1; // 普通
    public static final String KEY_ADDRESS_TPYE = "addressType";
    public static final String KEY_PHONE_NUMBER = "phoneNumber";
    public static final String KEY_REQUEST_CODE = "requestCode";
    public static final String KEY_ADDRESS = "address";
    public static final String KEY_CAR_ADDRESS = "car_address";
    private Context mContext;
    private RecyclerView mList;
    private TextView mTvCity;

    private EditText mEditText;

    private View mRootView;

    private MultiTypeRecyclerAdapter mAddressSearchAdapter;

    private List<DidiAddressBean> list = new ArrayList<>();

    private TextView mTvBusiness;
    private TextView mTvHistory;
    private LinearLayout mLlTab;

    private FrameView mFrameView;

    private int mCurrentFlag = 0;

    private int mAddressType; // 地址类型
    private String mPhoneNumber; // 电话
    private final TextWatcher textWatcher = new TextWatcher() {

        @Override
        public void onTextChanged(CharSequence s, int start, int before,
                                  int count) {
        }

        @Override
        public void afterTextChanged(Editable editable) {
            getAddress();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

    };
    private int mRequestCode;

    private SosoLocationService locationService;
    private String mCityName = "";
    private String mCityCode = "";

    private List<DidiAddressBean> mListBusinessBean; // 职场列表Bean
    private List<Object> mListHistoryBean; // 常用和历史列表Bean;
    private List<DidiAddressBean> mHistoryList; // 历史列
    private List<DidiAddressBean> mListSearchBean; // 联想列表Bean;
    private List<CarAddress> mCarAddresList; //常用地址列表

    // 城市Bean
    private DidiCityBean didiCityBean;

    private int mMajorThemeColor;
    private int mMajorLocationChecked;
    private int mMajorHistoryChecked;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.mContext = this;
        mRootView = getLayoutInflater().inflate(R.layout.jdme_activity_address_search, null, false);

        setContentView(mRootView);
        initView();
        locationService = new SosoLocationService(this);

        PermissionHelper.requestPermission(this, getResources().getString(R.string.me_request_permission_location_punch), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                PromptUtils.showLoadDialog(DidiAddressSearchActivity.this, getString(R.string.me_loading_message_not_translate), true);
                locationService.startLocationWithCheck();
                locationService.setLocationChangedListener(DidiAddressSearchActivity.this);
            }

            @Override
            public void denied(List<String> deniedList) {
                checkCity();
            }
        },Manifest.permission.ACCESS_FINE_LOCATION);

        mCarAddresList = getIntent().getParcelableArrayListExtra(KEY_CAR_ADDRESS);
        mAddressType = getIntent().getIntExtra(KEY_ADDRESS_TPYE, ADDRESS_TYPE_BUSSINESS);
        mPhoneNumber = getIntent().getStringExtra(KEY_PHONE_NUMBER);
        mRequestCode = getIntent().getIntExtra(KEY_REQUEST_CODE, 0);
        mListHistoryBean = new ArrayList<>();
        mHistoryList = DidiAddressHistoryDaoHelper.loadAllData();

        mAddressSearchAdapter = new MultiTypeRecyclerAdapter();
        mAddressSearchAdapter.addTypeAdapter(DidiAddressBean.class, new AddressSearchResultTypeAdapter(new OnItemClickListener<DidiAddressBean>() {
            @Override
            public void onItemClick(DidiAddressBean bean, int postion) {
                handleItemClick(bean);
            }
        }));
        mAddressSearchAdapter.addTypeAdapter(String.class, new AddressSearchTitleAdapter());
        mAddressSearchAdapter.addTypeAdapter(CarAddress.class, new CarAddressTypeAdapter(new OnItemClickListener<CarAddress>() {
            @Override
            public void onItemClick(CarAddress bean, int postion) {
                handleItemClick(ConvertUtils.convertAddressBean(bean));
            }
        }));
        initHistoryList();
        mFrameView = mRootView.findViewById(R.id.fv_list);
        mFrameView.setContainerShown(true);
        mList.setLayoutManager(new LinearLayoutManager(this));
        mAddressSearchAdapter.setData(mListHistoryBean);
        mList.setAdapter(mAddressSearchAdapter);
        //initSearchView();
        getSupportActionBar().hide();

        //setSearchView();
        mEditText.addTextChangedListener(textWatcher);
        initAddressType();

        mMajorThemeColor = R.color.skin_color_default;
        mMajorLocationChecked = R.drawable.jdme_didi_icon_address_location_checked_default;
        mMajorHistoryChecked = R.drawable.jdme_didi_icon_address_history_checked_default;
    }

    private void initView() {

        mList = findViewById(R.id.me_list);
        mTvCity = findViewById(R.id.tv_city);
        mEditText = findViewById(R.id.id_workplace_search_et);
        mTvBusiness = findViewById(R.id.tv_business);
        mTvHistory = findViewById(R.id.tv_history);
        mLlTab = findViewById(R.id.ll_tab);

    }

    private void handleItemClick(final DidiAddressBean bean) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                Intent intent = new Intent();
                intent.putExtra(KEY_ADDRESS, bean);
                setResult(200, intent);
                DidiUtils.closeBoard(mContext, mEditText);
                DidiAddressSearchActivity.this.finish();
                if (bean != null && !CheckCoordinatesUtil.checkAddressCoordinatesValidity(bean.lat, bean.lng)) {
                    MELogUtil.localE(CheckCoordinatesUtil.TAG, "DidiAddressSearchActivity --- insert error Data :lat=" + bean.lat + ",lng = " + bean.lng);
                    MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "DidiAddressSearchActivity --- insert error Data :lat=" + bean.lat + ",lng = " + bean.lng);
                }
                DidiAddressHistoryDaoHelper.insertData(bean);
            }
        }, 500);
    }

    private void initHistoryList() {
        if (mCarAddresList != null && mCarAddresList.size() > 0) {
            mListHistoryBean.add(getString(R.string.me_car_address_title));
            mListHistoryBean.addAll(mCarAddresList);
        }
        mListHistoryBean.add(getString(R.string.me_didi_address_history));
        mListHistoryBean.addAll(mHistoryList);
    }

    @Override
    public void onBackPressed() {
        setResult(400);
        super.onBackPressed();
    }

    /**
     * 初始化地址类型
     */
    private void initAddressType() {
        if (mAddressType == ADDRESS_TYPE_BUSSINESS) {
            mEditText.setEnabled(false);
            if (mRequestCode == 100)
                mEditText.setHint(R.string.me_didi_overtime_from);
            else if (mRequestCode == 200)
                mEditText.setHint(R.string.me_didi_overtime_to);
            else
                mEditText.setHint(R.string.me_didi_hint_select_address);
        } else {
            if (mRequestCode == 100)
                mEditText.setHint(R.string.me_didi_hint_msg_input);
            else if (mRequestCode == 200)
                mEditText.setHint(R.string.me_didi_hint_input_address_to);
        }
    }

    @Override
    public void onClick(View view) {
        int i = view.getId();
        if (i == R.id.ll_history) {
            if (mCurrentFlag != 0) {
                mTvBusiness.setTextColor(getResources().getColor((R.color.conference_black_color)));
                mTvBusiness.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.jdme_didi_icon_address_location_normal), null, null, null);
                mTvHistory.setTextColor(getResources().getColor((mMajorThemeColor)));
                mTvHistory.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(mMajorHistoryChecked), null, null, null);
                mCurrentFlag = 0;
                mFrameView.setContainerShown(true);
                mAddressSearchAdapter.setData(mListHistoryBean);
                mList.setAdapter(mAddressSearchAdapter);
            }

        } else if (i == R.id.ll_business) {
            if (mCurrentFlag != 1) {
                mTvBusiness.setTextColor(getResources().getColor((mMajorThemeColor)));
                mTvBusiness.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(mMajorLocationChecked), null, null, null);
                mTvHistory.setTextColor(getResources().getColor((R.color.conference_black_color)));
                mTvHistory.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.jdme_didi_icon_address_history_normal), null, null, null);
                mCurrentFlag = 1;
                mFrameView.setContainerShown(true);
                mAddressSearchAdapter.setData(mListBusinessBean);
                mList.setAdapter(mAddressSearchAdapter);
            }

        } else if (i == R.id.tv_city) {
            if (didiCityBean == null) {
                checkCity();
                return;
            }
            Intent intent = new Intent(this, DidiChangeCityActivity.class);
            intent.putExtra("entity", didiCityBean.cityList);
            intent.putExtra("cityName", mCityName);
            startActivityForResult(intent, 100);

        } else if (i == R.id.tv_cancel) {
            setResult(400);
            DidiUtils.closeBoard(mContext, mEditText);
            finish();

        }
    }

    /**
     * 获取办公地址
     */
    private void getBussinessAddress() {
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(this, false, false) { // 获取职场地址

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, mContext, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiLocationAddressBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiLocationAddressBean.class);
                        mListBusinessBean = didiAddressBean.addressList;
                        mAddressSearchAdapter.setData(mListBusinessBean);
                        mAddressSearchAdapter.notifyDataSetChanged();
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getJobAddressByLocal(this, callback, "", "", mCityCode);
    }

    /**
     * 获取地址
     */
    private void getAddress() {
        if (mCurrentFlag != -1) {
            mTvBusiness.setTextColor(getResources().getColor((R.color.conference_black_color)));
            mTvBusiness.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.jdme_didi_icon_address_location_normal), null, null, null);
            mTvHistory.setTextColor(getResources().getColor((R.color.conference_black_color)));
            mTvHistory.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.jdme_didi_icon_address_history_normal), null, null, null);
            mCurrentFlag = -1;
        }
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(this, false, false) { // 获取职场地址

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, mContext, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiListAddress didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiListAddress.class);
                        list = didiAddressBean.placeData;
                        list = findItemsbyName(mEditText.getText().toString());

                        if (list == null || list.size() == 0) {
                            mFrameView.setEmptyInfo(R.string.me_not_found_address);
                        } else {
                            mFrameView.setContainerShown(true);
                        }
                        mAddressSearchAdapter.setData(list);
                        mAddressSearchAdapter.notifyDataSetChanged();
                        mList.setAdapter(mAddressSearchAdapter);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getAddressByInput(this, callback, mEditText.getText().toString(), mPhoneNumber, mCityName);
    }

    /**
     * 获取办公地址
     */
    private void checkCity() {
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(this, false) { // 获取职场地址

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                PromptUtils.removeLoadDialog(DidiAddressSearchActivity.this);
                ResponseParser parser = new ResponseParser(request.result, mContext, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        didiCityBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCityBean.class);
                        if (null != didiCityBean.currentCity && null != didiCityBean.currentCity.cityName && null != didiCityBean.currentCity.cityName) {
                            mCityCode = didiCityBean.currentCity.cityCode;
                            mCityName = didiCityBean.currentCity.cityName;
                        } else if (null != didiCityBean.defaultCity) {
                            mCityCode = didiCityBean.defaultCity.cityCode;
                            mCityName = didiCityBean.defaultCity.cityName;
                        }
                        mTvCity.setText(mCityName);
                        mListBusinessBean = didiCityBean.addressList;
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        PromptUtils.removeLoadDialog(DidiAddressSearchActivity.this);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                PromptUtils.removeLoadDialog(DidiAddressSearchActivity.this);
            }
        };
        callback.setNeedTranslate(false);

        // 增加容错，cityName为空，获取缓存最后一次城市名称
        if (TextUtils.isEmpty(mCityName)) {
            mCityName = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_LOCATION_LAST_CITY);
        }
        NetWorkManager.checkCity(this, callback, mCityName);
    }

    /**
     * 查找联想变色部分
     *
     * @param str 地址
     * @return
     */
    private List<DidiAddressBean> findItemsbyName(String str) {
        List<DidiAddressBean> findList = new ArrayList<>();
        for (DidiAddressBean addressBean : list) {
            DidiAddressBean bean = new DidiAddressBean();
            bean.displayName = addressBean.displayName;
            bean.address = addressBean.address;
            bean.cityCode = addressBean.cityCode;
            bean.cityName = addressBean.cityName;
            bean.lat = addressBean.lat;
            bean.lng = addressBean.lng;
            if (!CheckCoordinatesUtil.checkAddressCoordinatesValidity(bean.lat, bean.lng)) {
                MELogUtil.localE(CheckCoordinatesUtil.TAG, "after:jmeMobile/vehicle/getAddressByInput,  error address: " + bean.toString());
                MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "after:jmeMobile/vehicle/getAddressByInput,  error address: " + bean.toString());
            }
            if (addressBean.displayName.contains(str)) {
                bean.span_start = addressBean.displayName.indexOf(str);
                bean.span_end = bean.span_start + str.length();
                if (bean.span_end < 0)
                    bean.span_end = 0;
            }
            findList.add(bean);
        }
        return findList;
    }


    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == 100) {
            if (requestCode == 100) {
                DidiCityInfoBean bean = (DidiCityInfoBean) data.getSerializableExtra("cityinfo");
                if (!mCityName.equals(bean.cityName)) {
                    mCityName = bean.cityName;
                    mCityCode = bean.cityCode;
                    mTvCity.setText(mCityName);
                    mTvBusiness.setTextColor(getResources().getColor((mMajorThemeColor)));
                    mTvBusiness.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(mMajorLocationChecked), null, null, null);
                    mTvHistory.setTextColor(getResources().getColor((R.color.conference_black_color)));
                    mTvHistory.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.jdme_didi_icon_address_history_normal), null, null, null);
                    mCurrentFlag = 1;
                    getBussinessAddress();
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onLocated(String lat, String lng, String name, String cityName) {
        if (TextUtils.isEmpty(cityName)) {
            return;
        }
        mCityName = cityName.replaceAll("市", "");
        checkCity();
    }

    @Override
    public void onFailed() {
        checkCity();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtils.requestResult(requestCode, permissions, grantResults, new Runnable() {
            @Override
            public void run() {
                if (locationService != null) {
                    locationService.startLocationWithCheck();
                    locationService.setLocationChangedListener(DidiAddressSearchActivity.this);
                }
            }
        }, null);
    }

}
