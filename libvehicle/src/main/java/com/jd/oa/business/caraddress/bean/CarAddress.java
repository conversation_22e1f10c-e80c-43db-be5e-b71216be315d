package com.jd.oa.business.caraddress.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class CarAddress implements Parcelable {
    public static final String FLOWFLAG_NORMAL = "1";
    public static final String FLOWFLAG_APPROVEING = "2";
    public static final String FLOWFLAG_APPREJECT = "3";

    private String addressCode;
    private String displayName;
    private String address;
    private String cityName;
    private String lat;
    private String lng;
    private String addressType;
    private String addressOrder;
    private String flowFlag;
    private String cityCode;
    private String remark;  //新增修改地址原因
    private int id;//常用地址的id
    private String isNeedApprova;
    private String hypothetical;

    public String getIsNeedApprova() {
        return isNeedApprova;
    }

    public void setIsNeedApprova(String isNeedApprova) {
        this.isNeedApprova = isNeedApprova;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAddressCode() {
        return addressCode;
    }

    public void setAddressCode(String addressCode) {
        this.addressCode = addressCode;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getHypothetical() {
        return hypothetical;
    }

    public void setHypothetical(String hypothetical) {
        this.hypothetical = hypothetical;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getAddressType() {
        return addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    public String getAddressOrder() {
        return addressOrder;
    }

    public void setAddressOrder(String addressOrder) {
        this.addressOrder = addressOrder;
    }

    public String getFlowFlag() {
        return flowFlag;
    }

    public void setFlowFlag(String flowFlag) {
        this.flowFlag = flowFlag;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.addressCode);
        dest.writeString(this.displayName);
        dest.writeString(this.address);
        dest.writeString(this.cityName);
        dest.writeString(this.lat);
        dest.writeString(this.lng);
        dest.writeString(this.addressType);
        dest.writeString(this.addressOrder);
        dest.writeString(this.flowFlag);
        dest.writeString(this.cityCode);
        dest.writeString(this.remark);
    }

    public CarAddress() {
    }

    protected CarAddress(Parcel in) {
        this.addressCode = in.readString();
        this.displayName = in.readString();
        this.address = in.readString();
        this.cityName = in.readString();
        this.lat = in.readString();
        this.lng = in.readString();
        this.addressType = in.readString();
        this.addressOrder = in.readString();
        this.flowFlag = in.readString();
        this.cityCode = in.readString();
        this.remark = in.readString();
    }

    public static final Creator<CarAddress> CREATOR = new Creator<CarAddress>() {
        @Override
        public CarAddress createFromParcel(Parcel source) {
            return new CarAddress(source);
        }

        @Override
        public CarAddress[] newArray(int size) {
            return new CarAddress[size];
        }
    };


    @Override
    public String toString() {
        return "CarAddress{" +
                "addressCode='" + addressCode + '\'' +
                ", displayName='" + displayName + '\'' +
                ", address='" + address + '\'' +
                ", cityName='" + cityName + '\'' +
                ", lat='" + lat + '\'' +
                ", lng='" + lng + '\'' +
                ", addressType='" + addressType + '\'' +
                ", addressOrder='" + addressOrder + '\'' +
                ", flowFlag='" + flowFlag + '\'' +
                ", cityCode='" + cityCode + '\'' +
                ", remark='" + remark + '\'' +
                ", id=" + id +
                ", isNeedApprova='" + isNeedApprova + '\'' +
                ", hypothetical='" + hypothetical + '\'' +
                '}';
    }
}
