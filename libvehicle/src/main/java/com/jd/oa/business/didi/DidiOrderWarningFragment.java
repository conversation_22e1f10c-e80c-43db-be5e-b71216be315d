package com.jd.oa.business.didi;

import static com.jd.oa.business.didi.DidiOrderDetailOmplaintsFeeFragment.FEE_COMPLAINS_SUCCESS;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.TextHelper;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;


/**
 * 订单异常的二次确认页面
 *
 * <AUTHOR>
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderWarningFragment extends BaseFragment implements FragmentOperatingListener, OperatingListener {
    public static final String FROM_FLAG_APPLY_AGAIN = "apply_again";
    public static final int APPLY_AGAIN_RESULT_CODE = 100;
    private View mRootView;

    private TextView mTvPrice;  // 价格
    private Button mBtnConfirm;

    private Button mBtnConfirmFee; // 费用延误
    private RelativeLayout rlWarning; // 异常提示
    private TextView tvWarning; // 异常提示text
    private TextView tvEstimatePrice; // 预估金额
    private EditText etReason; // 费用异常原因
    private TextView tvReasonCount; // 原因字数

    private String mOrderId;
    private DidiOrderDetailBean mDidiOrderDetailBean;
    private BroadcastReceiver complainSuccessReceiver;
    private DidiColleaguesSelectFragment mSelectFragment;
    private boolean isApplyAgain = false;
    private LinearLayout llTop;
    private LinearLayout llPay;
    private Button mSubmit;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (getArguments() != null) {
            String from = getArguments().getString("from");
            isApplyAgain = FROM_FLAG_APPLY_AGAIN.equals(from);
        }
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(isApplyAgain ?
                R.string.me_process_application : R.string.me_didi_title_order_warning);

        if (null == mRootView) {
            mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_warning, container, false);

            mOrderId = getArguments().getString("orderId");
            if (TextUtils.isEmpty(mOrderId)) {
                mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
                initView();
            } else {
                DidiUtils.getOrderDetail(getActivity(), mOrderId, "", new DidiUtils.IDetailCallback() {
                    @Override
                    public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                        mDidiOrderDetailBean = didiOrderDetailBean;
                        initView();
                    }
                });
            }
        }
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        return mRootView;
    }

    private void initView() {
        llTop = mRootView.findViewById(R.id.ll_top);
        llPay = mRootView.findViewById(R.id.ll_pay);
        mSubmit = mRootView.findViewById(R.id.btn_submit);

        mTvPrice = mRootView.findViewById(R.id.tv_price);  // 价格
        mBtnConfirm = mRootView.findViewById(R.id.btn_confirm_pay);

        mBtnConfirmFee = mRootView.findViewById(R.id.btn_confirm_delay_pay); // 费用延误
        rlWarning = mRootView.findViewById(R.id.rlWarning); // 异常提示
        tvWarning = mRootView.findViewById(R.id.tvWarning); // 异常提示text
        tvEstimatePrice = mRootView.findViewById(R.id.tvEstimatePrice); // 预估金额
        etReason = mRootView.findViewById(R.id.etReason); // 费用异常原因
        TextHelper.setEditTextInputSpace(etReason);
        tvReasonCount = mRootView.findViewById(R.id.tvReasonCount); // 原因字数

        // 赋值
        mTvPrice.setText(mDidiOrderDetailBean.price.totalPrice);  // 总价

        if ("0".equals(mDidiOrderDetailBean.order.isFeeComplainted)) {
            mBtnConfirmFee.setEnabled(true);
        } else {
            mBtnConfirmFee.setEnabled(false);
        }

        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.getIsWarning())) {
            rlWarning.setVisibility(View.VISIBLE);
            tvWarning.setText(mDidiOrderDetailBean.order.getConfirmInfo());
        }
        tvEstimatePrice.setText(mDidiOrderDetailBean.order.getEstimatePrice());
        etReason.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() <= 100) {
                    tvReasonCount.setText(s.length() + "/100");
                } else {
                    etReason.setText(s.delete(100, s.length()));
                    etReason.setSelection(s.length());
                    ToastUtils.showInfoToast("最多输入100个字");
                }
            }
        });
        //投诉完成后关闭此界面
        complainSuccessReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                //投诉完成后关闭此界面
                getActivity().finish();
            }
        };
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(FEE_COMPLAINS_SUCCESS);
        getActivity().registerReceiver(complainSuccessReceiver, intentFilter);
        mSelectFragment = DidiColleaguesSelectFragment.newInstance(mDidiOrderDetailBean);
        FragmentUtils.addWithCommit(getActivity(), mSelectFragment, R.id.fl_colleagues);

        llTop.setVisibility(mDidiOrderDetailBean.order.isWarning() ? View.VISIBLE : View.GONE);
        llPay.setVisibility(isApplyAgain ? View.GONE : View.VISIBLE);
        mSubmit.setVisibility(isApplyAgain ? View.VISIBLE : View.GONE);

        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 打车费用异常依然付钱 需要领导审批 使用新增的付款接口
                if (TextUtils.isEmpty(etReason.getText().toString().trim())) {
                    ToastUtils.showWarnToast(R.string.me_didi_order_pay_info_null);
                } else {
                    orderConfirm(etReason.getText().toString().trim());
                }
            }
        });

        mBtnConfirmFee.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if ("1".equals(mDidiOrderDetailBean.order.isFeeComplainted)) {
                    return;
                }
//                Intent intent = new Intent(getActivity(), FunctionActivity.class);
//                intent.putExtra("function", DidiOrderDetailOmplaintsFeeFragment.class.getName());
//                intent.putExtra("orderId", mDidiOrderDetailBean.order.orderId);
//                intent.putExtra("serviceType", mDidiOrderDetailBean.order.serviceType);
//                startActivity(intent);
                // 更换为拨打服务电话
                DidiUtils.callService(getActivity(),mDidiOrderDetailBean);
            }
        });
        mSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (TextUtils.isEmpty(etReason.getText().toString().trim())) {
                    ToastUtils.showWarnToast(R.string.me_didi_order_pay_info_null);
                    return;
                }
                if (mDidiOrderDetailBean != null) {
                    String key = mDidiOrderDetailBean.order.isWarning() ? JDMAConstants.mobile_employeeTravel_traveDetail_application_Oversubmission :
                            JDMAConstants.mobile_employeeTravel_traveDetail_application_Othersubmission;
                    JDMAUtils.clickEvent("", key, null);
                }
                submitApply();
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        getActivity().unregisterReceiver(complainSuccessReceiver);
    }

    // 确认行程
    private void orderConfirm(String confirmInfo) {
        if (NClick.isFastDoubleClick()) {
            return;
        }
        String passengersErp = getPassengerErp();
        String passengersName = getPassengersName();
        mBtnConfirm.setEnabled(false);
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        DidiUtils.getOrderDetail(getActivity(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, new DidiUtils.IDetailCallback() {
                            @Override
                            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                                if (null == didiOrderDetailBean) {
                                    ToastUtils.showToast(R.string.me_exception_order);
                                } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
                                    ToastUtils.showToast(R.string.me_exception_order);
                                } else {
                                    String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
                                    if (TextUtils.isEmpty(className)) {
                                        ToastUtils.showToast(R.string.me_exception_order_state);
                                    } else {
                                        Bundle bundle = new Bundle();
                                        bundle.putSerializable("orderDetailBean", didiOrderDetailBean);
                                        try {
                                            FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content, false, bundle, false);
                                            //当前activity与订单详情不是同一个 替换fragment完成后关闭订单详情页保证返回键回到订单列表
                                            getActivity().sendBroadcast(new Intent("ORDER_CONFIRM_SUCCESS"));
                                        } catch (ClassNotFoundException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        mBtnConfirm.setEnabled(true);
                                    }
                                }, 500);
                            }
                        });
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        mBtnConfirm.setEnabled(true);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mBtnConfirm.setEnabled(true);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.confirmOrder(this, callback, mDidiOrderDetailBean.order.orderId, confirmInfo, passengersErp, passengersName);
    }

    private void submitApply() {
        String passengersErp = getPassengerErp();
        String passengersName = getPassengersName();
        NetWorkManager.submitAgain(this, new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getActivity() != null) {
                    getActivity().setResult(APPLY_AGAIN_RESULT_CODE);
                    getActivity().finish();
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, mDidiOrderDetailBean.order.orderId, etReason.getText().toString(), passengersErp, passengersName);
    }

    public void onEventMainThread(DidiOrderDetailBean orderDetailBean) {
        // 跳转逻辑

    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
        if (bundle != null) {

        }
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OPERATE_GO_DIDI == optionFlag) {
            if (args != null) {
                String isAgain = args.getString("isAgain");
                if ("0".equals(isAgain)) {
//                    mIbtnOmplaints.setBackgroundResource(R.drawable.jdme_didi_icon_complaints_disable);
                    mDidiOrderDetailBean.order.isComplainted = "1";
                }
                String isFeeSuccess = args.getString("isFeeSuccess");
                if ("1".equals(isFeeSuccess)) {
                    mBtnConfirmFee.setEnabled(false);
                    getActivity().onBackPressed();
                }
            }
            return true;
        }
        return false;
    }

    public String getPassengerErp() {
        List<MemberEntityJd> list = mSelectFragment.getSelectList();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            MemberEntityJd info = list.get(i);
            sb.append(info.mId);
            if (i != list.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public String getPassengersName() {
        List<MemberEntityJd> list = mSelectFragment.getSelectList();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            MemberEntityJd info = list.get(i);
            sb.append(info.mName);
            if (i != list.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == DidiColleaguesSelectFragment.REQUEST_CODE_ADD) {
            //由于调用的咚咚的选人界面，咚咚用的是Activity的startActivityForActivity,所以需要一步步回传给fragment
            //后期据说会改成EventBus
            mSelectFragment.onActivityResult(requestCode, resultCode, data);
        }
    }
}
