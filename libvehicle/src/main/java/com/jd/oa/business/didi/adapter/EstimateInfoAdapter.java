package com.jd.oa.business.didi.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import com.alibaba.fastjson.support.spring.FastJsonRedisSerializer;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiEstimatePriceBean;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class EstimateInfoAdapter extends BaseRecyclerViewAdapter<DidiEstimatePriceBean.DidiEstimatePriceListBean> {
    public EstimateInfoAdapter(Context context, List<DidiEstimatePriceBean.DidiEstimatePriceListBean> data) {
        super(context, data);
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_item_didi_estimate_info_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, DidiEstimatePriceBean.DidiEstimatePriceListBean item, int position) {
        holder.setText(R.id.tv_name, item.serviceName);
        holder.setText(R.id.tv_price, item.price);
        ImageView icon = holder.getView(R.id.iv_icon);
        ImageLoader.load(mContext, icon, item.icon, false, R.drawable.jdme_icon_use_car_service_default, R.drawable.jdme_icon_use_car_service_default);
    }
}
