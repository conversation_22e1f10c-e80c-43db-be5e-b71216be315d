package com.jd.oa.business.didi.widget;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiMapPopBean;
import com.tencent.mapsdk.raster.model.Marker;
import com.tencent.tencentmap.mapsdk.map.TencentMap;

/**
 * Created by liyao8 on 2018/3/15.
 *
 * 用来修改腾讯地图的infoWindow 根据不同的marker使用不同的layout
 */

public class MyInfoWindowAdapter implements TencentMap.InfoWindowAdapter{
    public static final String TIME_COUNT="TIME_COUNT";
    public static final String NORMAL="NORMAL";
    View view;
    @Override
    public View getInfoWindow(Marker marker) {

        if(marker.getTag() instanceof DidiMapPopBean){
            //这里的逻辑不太好理解，每一个type对应一个marker，不做复用。方便在其他地方去更新infowindow的数据
            switch (((DidiMapPopBean) marker.getTag()).getType()){
                case TIME_COUNT:
                    if(view == null){
                        view=LayoutInflater.from(AppBase.getAppContext()).inflate(R.layout.jdme_view_info_window_time,null,false);
                    }
                    break;
                case NORMAL:
                    if(view == null){
                        view=LayoutInflater.from(AppBase.getAppContext()).inflate(R.layout.jdme_view_info_window_normal,null,false);
                    }
                    break;
                default:
                    view =LayoutInflater.from(AppBase.getAppContext()).inflate(R.layout.jdme_view_info_window_time,null,false);
                    break;
            }

        } else{
            view =LayoutInflater.from(AppBase.getAppContext()).inflate(R.layout.jdme_view_info_window_time,null,false);
        }
            return view;
    }

    @Override
    public void onInfoWindowDettached(Marker marker, View view) {

    }

}
