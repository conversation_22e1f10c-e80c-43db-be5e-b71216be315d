package com.jd.oa.business.didi.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by chenqizheng on 2018/4/9.
 */

public class DiDiAddressListWrapper implements Serializable {
    private String cityCode;
    private List<DidiAddressBean> positionList;

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public List<DidiAddressBean> getPositionList() {
        return positionList;
    }

    public void setPositionList(List<DidiAddressBean> positionList) {
        this.positionList = positionList;
    }

}
