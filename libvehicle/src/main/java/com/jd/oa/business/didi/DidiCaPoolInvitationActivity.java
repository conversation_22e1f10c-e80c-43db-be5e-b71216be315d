package com.jd.oa.business.didi;

import androidx.fragment.app.Fragment;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.SingleFragmentActivity;
import com.jd.oa.router.DeepLink;

/**
 * Created by peidongbiao on 2019/4/9
 */
@Route(value = {DeepLink.TRAVEL_ORDER, DeepLink.CAR_OLD}, interceptors = "CarOrderInterceptor")
public class DidiCaPoolInvitationActivity extends SingleFragmentActivity {

    @Override
    protected Fragment createFragment() {
        return new DidiCaPoolInvitationFragment();
    }
}