package com.jd.oa.business.didi.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * 订单详情Bean
 */
public class DidiOrderDetailBean implements Serializable {

    public OrderDetailBean order;
    public PriceBean price;
    //是否是同行拼车 0: 整车; 1: 自选同事拼车；2：无自选同事，走滴滴拼车
    public String isCarPool;
    //同行拼车发起人
    public String callCarUser;
    //同行人
    public List<CarPoolUserStatus> carPoolUser;
    //同行人电话、状态
    public CarPoolInfoData carPoolInfoData;

    //提示文本
    public String drivingShowTip;

    public String shuttleBusTip;//弹框文案
    public String shuttleBusLink;//弹框摆渡车跳转链接
    public String isShow;//是否弹出去乘坐摆渡车的提示框，1弹出，0不弹出

    public String icon;//运营商logo

    public String h5url;//滴滴订单H5详情页链接

    public class OrderDetailBean implements Serializable {
        public String orderId; //"4880109188406595918"
        public String didiOrderStatus;  //滴滴订单状态
        public String didiOrderStatusDesc; // "等待应答", //见滴滴订单状态描述
        public String orderStatus; //100 //订单状态
        public String orderStatusDesc;     //"进行中", //见订单状态描述
        public String showTip;     //"订单已完成，正在审批，请耐心等待"  //对用户的提示
        public String driverName; //"张师傅", //司机名字
        public String driverPhone;  //"18612249028",//司机手机号
        public String driverNum; //"109",//已通知司机数量
        public String driverCarType; // "日产天籁",//车型
        public String driverCard; //"冀A**G05",//车牌
        public String driverAvatar;     //"http://xxxxx.jpg",//头像
        public String distance;     //"480"//直线距离，单位米
        public String dlng;  //司机经度
        public String dlat;  //司机纬度
        public String flng;  //出发地经度
        public String flat;  //出发地纬度

        public String isLineup;//是否处于排队 1:排队
        public String ranking;//队列中的位置
        public String queueLength;//队列总长度
        public String waitTime;//预估需等待时间
        public String isTransfer;//是否改派 1:是 0: 否
        public String nextOrderId;//改派后订单ID
        public String lineTip;//排队文案
        //一键还款 jdme://jm/rn/202110191131?refundType='refundDetail'&billCode=
        public String paymentDeepLink;
        public String isConfirm = "";//是否正在生成还款单 0：没有生成 1：正在生成

        public String requireLevel; //"100"
        public String beginChargeTime;  //"2015-03-11 15:06:58",//开始计价时间
        public String finishTime; // "2015-03-11 16:06:58",//行程结束时间
        public String normalDistance; //"4.80"//实际行驶公里数

        public String pollingTime; //30 轮循掉详情接口的秒数
        public String driveingTime; // 03:09 乘客上车司机计价开始后与系统时间相比的持续时间
        public String orderIngTime; //03:09 生成订单的时间和系统时间相比的持续时间

        public String phoneNumber; // 叫车手机号

        public String driverLevel; //司机星级
        public String driverOrderCount; //司机单量

        public String isComplainted; // 是否已投诉（0：否；1：是）
        public String isCommented; // 是否已评价（0：否；1：是）
        public String isFeeComplainted; // 是否可以延缓支付（0：否；1：是）

        public String alarm; // 提示费用异常

        public String startName; //出发地
        public String endName; //目的地

        public String serviceType = "";
        public String servicePhone;

        public String reassignMeOrderNo; // 改派订单
        private String isWarning = ""; //0 无需确认，1需确认
        private String confirmInfo;//订单详情页顶部的异常提示信息
        private String estimatePrice;//预估价格
        private String orderTimeType;
        private String startTime;

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public String getIsWarning() {
            return isWarning;
        }

        public void setIsWarning(String isWarning) {
            this.isWarning = isWarning;
        }

        public String getConfirmInfo() {
            return confirmInfo;
        }

        public void setConfirmInfo(String confirmInfo) {
            this.confirmInfo = confirmInfo;
        }

        public String getEstimatePrice() {
            return estimatePrice;
        }

        public void setEstimatePrice(String estimatePrice) {
            this.estimatePrice = estimatePrice;
        }

        public String getOrderTimeType() {
            return orderTimeType;
        }

        public void setOrderTimeType(String orderTimeType) {
            this.orderTimeType = orderTimeType;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public boolean isConfirm() {
            return "1".equals(isConfirm);
        }

        public boolean isWarning(){
            return "1".equals(isWarning);
        }
    }


    public class PriceBean implements Serializable {
        public String totalPrice; //"23.00",//总费用
        public List<model> detail;
        public String isExistObjection; //  0不存在  1存在 拦截页面标识 2017/01/05
        public ObjectionModel feeObjection; // 拦截页面信息


        public class model implements Serializable {
            public String name;
            public String amount;
            public String type;
        }

        public class ObjectionModel implements Serializable {
            public String pageTitle; // 页面标题 暂时不用
            public String objectionTitle; // 标题
            public String objectionMsg; // 消息
            public String errorBtnText;// 按钮1
            public String okBtnText; // 按钮2
            public String confirmTitle = ""; // 标题
            public String confirmMsg = ""; // 消息
            public String confirmTips = ""; // 提示
            public String confirmFee = ""; // 总费用
        }
    }

    public class CarPoolInfoData implements Serializable {
        public String carPoolMsg;
        public List<ColleagueInfo> carPoolInfo;
    }

    /**
     * 同行人推送状态
     */
    public class ColleagueInfo implements Serializable {
        @SerializedName("tailNumber")
        public String phoneNumber;
        @SerializedName("endName")
        public String address;
    }
}