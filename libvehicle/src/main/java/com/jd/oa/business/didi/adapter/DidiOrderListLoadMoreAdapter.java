package com.jd.oa.business.didi.adapter;

import android.content.Context;
import androidx.core.content.ContextCompat;
import android.view.View;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiOrderBean;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;
import com.jd.oa.utils.StringUtils;

import java.util.List;

/**
 * 已确定会议列表
 *
 * <AUTHOR>
 */
public class DidiOrderListLoadMoreAdapter extends BaseRecyclerViewLoadMoreAdapter<DidiOrderBean> {

    public final static String TAG = "MyReserve_ProgressOrderListAdapter";
    private List<DidiOrderBean> mList;
    private Context mContext;


    public DidiOrderListLoadMoreAdapter(Context ctx, List<DidiOrderBean> beans) {
        super(ctx, beans);
        if (beans == null) {
            throw new IllegalArgumentException("the data must not be null");
        }
        this.mContext = ctx;
        mList = beans;
    }

    @Override
    protected int getCurrentItemLayoutId(int viewType) {
        return R.layout.jdme_didi_order_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, DidiOrderBean item, int position) {
        holder.setText(R.id.tv_didi_order_date, mList.get(position).getCreateTime());
        holder.setText(R.id.tv_didi_order_money, StringUtils.isEmptyWithTrim(mList.get(position).getTotalPrice()) ? "￥0.00" : ("￥" + mList.get(position).getTotalPrice()));
        holder.setText(R.id.tv_didi_start_place, mList.get(position).getStartName());
        holder.setText(R.id.tv_didi_end_place, mList.get(position).getEndName());
        holder.setText(R.id.tv_didi_order_state, mList.get(position).getStatusDesc());
        holder.setVisible(R.id.tv_car_pool, "1".equals(item.isCarPool)? View.VISIBLE : View.GONE);
        holder.setVisible(R.id.tv_car_appointment, item.isAppointment() ? View.VISIBLE : View.GONE);
        holder.setVisible(R.id.tv_car_upgrade, item.isOwnExpense() ? View.VISIBLE : View.GONE);
        TextView mTv = holder.getView(R.id.tv_didi_order_state);
        switch (mList.get(position).getStatusDesc()) {
            case "待审批":
            case "待还款":
                mTv.setTextColor(mContext.getResources().getColor(R.color.red_warn));
                break;
            case "进行中":
            case "待确认":
            case "待支付":
                mTv.setTextColor(mContext.getResources().getColor(R.color.skin_color_fresh));
                break;
            default:
                mTv.setTextColor(mContext.getResources().getColor(R.color.tab_text_color));
                break;
        }
        boolean isCarPool = "1".equals(item.isCarPool);
        if (!isCarPool) return;
        String flowFlag = item.getFlowFlag();
        if(DidiUtils.CAR_POOL_STATE_CONFIRMING.equals(flowFlag)) {
            mTv.setTextColor(ContextCompat.getColor(mContext, R.color.me_color_yellow));
        } else if(DidiUtils.CAR_POOL_STATE_EXPIRED.equals(flowFlag)) {
            mTv.setTextColor(ContextCompat.getColor(mContext, R.color.tab_text_color));
        } else if(DidiUtils.CAR_POOL_STATE_APPROVED.equals(flowFlag)) {
            mTv.setTextColor(ContextCompat.getColor(mContext, R.color.me_color_green));
        } else if(DidiUtils.CAR_POOL_STATE_REJECTED.equals(flowFlag)) {
            mTv.setTextColor(ContextCompat.getColor(mContext, R.color.red_warn));
        }
    }
}