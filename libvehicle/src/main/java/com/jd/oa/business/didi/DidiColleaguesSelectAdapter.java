package com.jd.oa.business.didi;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;

import java.util.List;


public class DidiColleaguesSelectAdapter extends RecyclerView.Adapter<DidiColleaguesSelectAdapter.ColleaguesVH> {

    private static final int MAX_COLLEAGUES_COUNT = 3;
    private List<MemberEntityJd> tbContactInfos;
    private Action mAction;
    private int layoutId = R.layout.jdme_item_didi_colleagues_select;

    public DidiColleaguesSelectAdapter(List<MemberEntityJd> tbContactInfos) {
        this.tbContactInfos = tbContactInfos;
    }

    public void setAction(Action mAction) {
        this.mAction = mAction;
    }

    @Override
    public DidiColleaguesSelectAdapter.ColleaguesVH onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(viewGroup.getContext()).inflate(layoutId, null);
        return new ColleaguesVH(view);
    }

    @Override
    public void onBindViewHolder(DidiColleaguesSelectAdapter.ColleaguesVH viewHolder, int position) {
        if (tbContactInfos.size() < MAX_COLLEAGUES_COUNT && position == tbContactInfos.size()) {
            viewHolder.addLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mAction != null) {
                        mAction.onAddBtnClick();
                    }
                }
            });
            viewHolder.addLayout.setVisibility(View.VISIBLE);
            viewHolder.normalLayout.setVisibility(View.GONE);
        } else {
            final MemberEntityJd info = tbContactInfos.get(position);
            viewHolder.name.setText(info.mName);
            viewHolder.del.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    tbContactInfos.remove(info);
                    notifyDataSetChanged();
                }
            });
            viewHolder.addLayout.setVisibility(View.GONE);
            viewHolder.normalLayout.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        if (tbContactInfos != null) {
            if (tbContactInfos.size() >= MAX_COLLEAGUES_COUNT) {
                return MAX_COLLEAGUES_COUNT;
            } else {
                return tbContactInfos.size() + 1;
            }
        } else {
            return 1;
        }
    }

    public void setLayoutId(int layoutId) {
        this.layoutId = layoutId;
    }

    public static class ColleaguesVH extends RecyclerView.ViewHolder {

        private TextView name;
        private ImageView del;
        private View normalLayout;
        private View addLayout;

        public ColleaguesVH(View itemView) {
            super(itemView);
            name = itemView.findViewById(R.id.tv_name);
            del = itemView.findViewById(R.id.iv_del);
            normalLayout = itemView.findViewById(R.id.ll_normal);
            addLayout = itemView.findViewById(R.id.ll_add);
        }
    }

    public interface Action {
        void onAddBtnClick();
    }
}
