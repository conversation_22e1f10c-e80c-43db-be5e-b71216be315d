package com.jd.oa.business.didi.model;

import androidx.annotation.Keep;

import java.io.Serializable;
import java.util.List;

/**
 * Created by peidongbiao on 2019/4/2
 */
@Keep
public class CarPoolOrderDetail implements Serializable {
    /**
     * 初始叫车时间戳
     */
    private String startTimestamp;

    /**
     * 当前服务器时间戳
     */
    private String curTimestamp;

    /**
     * 倒计时最终时间
     */
    private String confirmStopTimestamp;

    /**
     * 用户响应后实际停止时间
     */
    private String finalTimestamp;

    private List<CarPoolUserStatus> carPoolUsers;

    public String getStartTimestamp() {
        return startTimestamp;
    }

    public void setStartTimestamp(String startTimestamp) {
        this.startTimestamp = startTimestamp;
    }

    public String getCurTimestamp() {
        return curTimestamp;
    }

    public void setCurTimestamp(String curTimestamp) {
        this.curTimestamp = curTimestamp;
    }

    public List<CarPoolUserStatus> getCarPoolUsers() {
        return carPoolUsers;
    }

    public void setCarPoolUsers(List<CarPoolUserStatus> carPoolUsers) {
        this.carPoolUsers = carPoolUsers;
    }

    public String getConfirmStopTimestamp() {
        return confirmStopTimestamp;
    }

    public void setConfirmStopTimestamp(String confirmStopTimestamp) {
        this.confirmStopTimestamp = confirmStopTimestamp;
    }

    public String getFinalTimestamp() {
        return finalTimestamp;
    }

    public void setFinalTimestamp(String finalTimestamp) {
        this.finalTimestamp = finalTimestamp;
    }
}