package com.jd.oa.business.didi;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.RatingBar;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.INotProguard;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.js.hybrid.utils.ShareUtils;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.location.LocationManager;
import com.jd.oa.location.TencentNullLocation;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.WebViewUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.tencent.map.geolocation.TencentLocation;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.com.libsharesdk.item.WechatFriendShare;
import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.ObservableSource;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;

/**
 * Created by zhangjie14 on 2016/1/28.
 */


public class DidiUtils implements INotProguard {
    private static final String TAG = "DidiUtils";

    public static final String STATE_ONGOING = "100"; //进行中
    public static final String STATE_PENDING = "200"; //待审批
    public static final String STATE_DEBT = "300"; //待还款
    public static final String STATE_FINISHED = "400"; //已完成
    public static final String STATE_CANCELED = "500"; //已取消
    public static final String STATE_HOLD_CONFIRM = "150"; // 待确认、待支付

    //滴滴公司定义的状态
    public static final String DIDI_STATE_WAIT_RESPONSE = "300"; //等待应答
    public static final String DIDI_STATE_OVERTIME = "311"; //订单超时

    public static final String DIDI_STATE_COMEING = "400"; //等待接驾
    public static final String DIDI_STATE_ARRIVED = "410"; //司机已到达

    public static final String DIDI_STATE_ON_DRIVR = "500"; //行驶中
    public static final String DIDI_STATE_COMPLETE = "600"; //行程结束
    public static final String DIDI_STATE_COMPLETE_EXCEPTION = "610"; //行程异常结束
    public static final String DIDI_STATE_PAID = "700"; //已支付

    public static final int REQUEST_CODE_PERMISSION_CALLPHONE = 100;
    public static final int REQUEST_CODE_PERMISSION_LOCATION = 101;

    public static final String CAR_POOL_STATE_CONFIRMING = "10";    //待确认
    public static final String CAR_POOL_STATE_APPROVED = "20";  //已确认
    public static final String CAR_POOL_STATE_ONGOING = "25";   //进行中
    public static final String CAR_POOL_STATE_REJECTED = "30";  //已拒绝
    public static final String CAR_POOL_STATE_CANCELED = "40";  //已取消
    public static final String CAR_POOL_STATE_EXPIRED = "50";   //已过期
    public static final String CAR_POOL_STATE_COMPLETE = "60";  //已完成
    public static final String DIDI_CALL_CAR_UPGRADE_GRAY = "android.vehicle.ownexpense.enable";//自费升舱 ABTest key

    /**
     * 获取跳转Fragment 名称
     *
     * @param orderDetailBean
     * @return
     */
    public static String getRedirectFragmentClassname(DidiOrderDetailBean orderDetailBean) {
        String resFragment = null;
        boolean isCarPool = isCarPoolOrder(orderDetailBean);    //是否是拼车单
        boolean isCallCarUser = isCallCarUser(orderDetailBean); //是否是拼车发起人
        if (isCarPool && !isCallCarUser) {
            //拼车同行人
            String carPoolStatus = getCarPoolOrderStatus(orderDetailBean.carPoolUser);
            if (CAR_POOL_STATE_CONFIRMING.equals(carPoolStatus)) {
                //待确认
                resFragment = DidiCaPoolInvitationFragment.class.getName();
            } else if (CAR_POOL_STATE_APPROVED.equals(carPoolStatus)) {
                //已确认
                resFragment = DidiCarPoolInvitationConfirmedFragment.class.getName();
            } else if (CAR_POOL_STATE_ONGOING.equals(carPoolStatus)) {
                //进行中
                resFragment = getOnGoingOrderRedirectFragment(orderDetailBean);
            } else if (CAR_POOL_STATE_REJECTED.equals(carPoolStatus) ||
                    CAR_POOL_STATE_CANCELED.equals(carPoolStatus) ||
                    CAR_POOL_STATE_EXPIRED.equals(carPoolStatus)) {
                //拒绝，取消，过期
                resFragment = DidiCarPoolAbnormalOrderFragment.class.getName();
            } else if (CAR_POOL_STATE_COMPLETE.equals(carPoolStatus)) {
                //已完成
                resFragment = DidiOrderDetailFragment.class.getName();
            } else {
                resFragment = DidiOrderDetailFragment.class.getName();
            }
            return resFragment;
        } else {
            //非拼车单，或者是拼车发起人
            switch (orderDetailBean.order.orderStatus) {
                case DidiUtils.STATE_CANCELED:
                    resFragment = DidiOrderDetailOrderCancelFragment.class.getName();
                    break;
                case DidiUtils.STATE_FINISHED:
                case DidiUtils.STATE_PENDING:
                case DidiUtils.STATE_DEBT:
                case DidiUtils.STATE_HOLD_CONFIRM:
                    if ("1".equals(orderDetailBean.price.isExistObjection) && null != orderDetailBean.price.feeObjection) {
                        resFragment = DidiOrderDetailConfirmFragment.class.getName();
                    } else if ("0".equals(orderDetailBean.order.isCommented)) {
                        //评价页面
                        resFragment = DidiOrderDetailAppraisalFragment.class.getName();
                    } else {
                        resFragment = DidiOrderDetailFragment.class.getName();
                    }
                    break;
                case DidiUtils.STATE_ONGOING:
                    resFragment = getOnGoingOrderRedirectFragment(orderDetailBean);
                    if (resFragment == null && "".equals(orderDetailBean.order.didiOrderStatus)) {
                        //同乘单，等待同行人确认
                        resFragment = DidiCarPoolWaitingFragment.class.getName();
                    }
                    break;
                default:
                    break;
            }
        }
        return resFragment;
    }

    /**
     * 进行中的订单判断didiOrderStatus字段
     *
     * @param orderDetailBean
     * @return
     */
    private static String getOnGoingOrderRedirectFragment(DidiOrderDetailBean orderDetailBean) {
        String resFragment = null;
        switch (orderDetailBean.order.didiOrderStatus) {
            case DidiUtils.DIDI_STATE_WAIT_RESPONSE:
                resFragment = DidiOrderDetailWaitReplyFragment.class.getName();
                break;
            case DidiUtils.DIDI_STATE_COMEING:
            case DidiUtils.DIDI_STATE_ARRIVED:
                resFragment = DidiOrderDetailWaitCarFragment.class.getName();
                break;
            case DidiUtils.DIDI_STATE_ON_DRIVR:
                resFragment = DidiOrderDetailDrivingFragment.class.getName();
                break;
            case DidiUtils.DIDI_STATE_PAID:
            case DidiUtils.DIDI_STATE_COMPLETE:
                resFragment = DidiOrderDetailDrivingFragment.class.getName();
                break;
            case DidiUtils.DIDI_STATE_COMPLETE_EXCEPTION:
            case DidiUtils.DIDI_STATE_OVERTIME:
                resFragment = DidiOrderDetailOrderCancelFragment.class.getName();
                break;
            default:
                break;
        }
        return resFragment;
    }

    /**
     * 获取状态
     *
     * @param list
     * @return
     */
    @Nullable
    public static String getCarPoolOrderStatus(List<CarPoolUserStatus> list) {
        if (CollectionUtil.isEmptyOrNull(list)) return null;
        for (int i = 0; i < list.size(); i++) {
            CarPoolUserStatus status = list.get(i);
            if (PreferenceManager.UserInfo.getUserName().equals(status.getUserName())) {
                return status.getFlowFlag();
            }
        }
        return null;
    }

    @Nullable
    public static CarPoolUserStatus getCallCarUser(DidiOrderDetailBean detailBean) {
        if (detailBean == null) return null;
        if (!isCarPoolOrder(detailBean)) return null;
        if (CollectionUtil.isEmptyOrNull(detailBean.carPoolUser)) return null;
        String callCarUser = detailBean.callCarUser;
        if (TextUtils.isEmpty(callCarUser)) return null;
        CarPoolUserStatus user = null;
        for (int i = 0; i < detailBean.carPoolUser.size(); i++) {
            CarPoolUserStatus status = detailBean.carPoolUser.get(i);
            if (callCarUser.equals(status.getUserName())) {
                user = status;
                user.setCallCarUser(true);
                break;
            }
        }
        return user;
    }


    public static String getCarPoolUserIds(List<CarPoolUserStatus> members) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < members.size(); i++) {
            CarPoolUserStatus member = members.get(i);
            stringBuilder.append(member.getUserName());
            if (i != members.size() - 1) {
                stringBuilder.append(",");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 是否是拼车单
     *
     * @param detailBean
     * @return
     */
    public static boolean isCarPoolOrder(DidiOrderDetailBean detailBean) {
        return !"0".equals(detailBean.isCarPool);
    }

    /**
     * 关闭键盘
     *
     * @param context
     */
    public static void closeBoard(Context context, View view) {
        InputMethodManager imm = (InputMethodManager) context
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    /**
     * 获取司机图片
     *
     * @param url     地址
     * @param iv      容器
     * @param context
     */
    public static void getDriverImg(String url, CircleImageView iv, Context context) {
        if (!TextUtils.isEmpty(url) && context != null) {
            DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail(R.drawable.jdme_icon_driver).build();
            ImageLoaderUtils.getInstance().displayImage(url, iv, displayImageOptions);
        } else {
            iv.setImageResource(R.drawable.jdme_icon_driver);
        }
    }


    @SuppressLint("MissingPermission")
    public static void callDriver(Context context, String phoneNumber) {
        Intent intent = new Intent(Intent.ACTION_CALL, Uri.parse("tel:" + phoneNumber));
        context.startActivity(intent);
    }

    /**
     * 刷新订单状态，并且跳页
     *
     * @param activity
     * @param detailBean
     */
    public static void getOrderDetailAndJump(final Activity activity, DidiOrderDetailBean detailBean) {
        DidiUtils.getOrderDetail(activity, detailBean.order.orderId, detailBean.order.phoneNumber, new DidiUtils.IDetailCallback() {
            @Override
            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                if (activity.isFinishing()) return;
                String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
                if (TextUtils.isEmpty(className)) {
                    ToastUtils.showToast(R.string.me_didi_exception_order_state);
                } else {
                    Intent intent = new Intent(activity, FunctionActivity.class);
                    intent.putExtra("function", className);
                    intent.putExtra("orderDetailBean", didiOrderDetailBean);
                    activity.startActivity(intent);
                    activity.finish();
                }
            }
        });
    }

    public static void getOrderDetail(final Context context, String orderId, String phoneNumber, final IDetailCallback callback) {
        getOrderDetail(context, orderId, phoneNumber, callback, true);
    }

    /**
     * 获取订单状态
     *
     * @param context
     * @param orderId
     * @param phoneNumber
     * @param callback
     */
    public static void getOrderDetail(final Context context, String orderId, String phoneNumber, final IDetailCallback callback, boolean showProgress) {
        SimpleRequestCallback simpleRequestCallback = new SimpleRequestCallback<String>(context, showProgress, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, context, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiOrderDetailBean didiOrderDetailBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiOrderDetailBean.class);
                        callback.callBack(didiOrderDetailBean);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_exception_order_state);
                callback.onFailure(exception, info);
            }
        };
        simpleRequestCallback.setNeedTranslate(false);
        NetWorkManager.getOrderDetail(null, simpleRequestCallback, orderId, phoneNumber);
    }

    public static void showDriverLevel(float driverLevel, RatingBar ratingBar) {
        if (driverLevel == 0) {
            ratingBar.setVisibility(View.GONE);
        } else {
            int startNum = (int) Math.ceil(driverLevel);
            int ratingNum = (int) Math.floor(driverLevel);
            ratingBar.setNumStars(startNum);
            ratingBar.setRating(ratingNum);
        }
    }

    /**
     * 急救接口
     */
    public static void urgentHelp(final Context context, final String orderId) {
        //获取位置
        Disposable disposable = LocationManager.get(context)
                .getLocation()
                .onErrorReturnItem(new TencentNullLocation())
                .flatMap(new Function<TencentLocation, ObservableSource<ApiResponse<Map<String, String>>>>() {
                    @Override
                    public ObservableSource<ApiResponse<Map<String, String>>> apply(TencentLocation tencentLocation) throws Exception {
                        StringBuilder location = new StringBuilder();
                        if (!(tencentLocation.getLatitude() == 0d && tencentLocation.getLongitude() == 0d)) {
                            location.append(tencentLocation.getAddress());
                        }
                        return callHelp(context, orderId, location.toString(), String.valueOf(tencentLocation.getLatitude()), String.valueOf(tencentLocation.getLongitude()));
                    }
                })
                .subscribe(new Consumer<ApiResponse<Map<String, String>>>() {
                    @Override
                    public void accept(ApiResponse<Map<String, String>> mapApiResponse) throws Exception {
                        if (mapApiResponse.isSuccessful()) {
                            Map<String, String> result = mapApiResponse.getData();
                            String key = "一键报警";
                            if (result.containsKey(key)) {
                                openWebFragment(context, result.get(key));
                            } else {
                                Toast.makeText(context, R.string.me_travel_help_message_sended, Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            Toast.makeText(context, mapApiResponse.getErrorMessage(), Toast.LENGTH_SHORT).show();
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        MELogUtil.onlineE(MELogUtil.TAG_TAX, TAG + " accept: ", throwable);
                        MELogUtil.localE(MELogUtil.TAG_TAX, TAG + " accept: ", throwable);
                        Toast.makeText(context, R.string.jdme_str_net_error, Toast.LENGTH_SHORT).show();
                    }
                });
    }

    private static Observable<ApiResponse<Map<String, String>>> callHelp(final Context context, String orderId, String location, String lat, String lng) {
        final Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("curLocation", location);
        params.put("passengerClat", lat);
        params.put("passengerClng", lng);
        return Observable.create(new ObservableOnSubscribe<ApiResponse<Map<String, String>>>() {
            @Override
            public void subscribe(final ObservableEmitter<ApiResponse<Map<String, String>>> e) throws Exception {
                NetWorkManager.request(null, Constant.getConstant(NetWorkManager.mReqeustType).API_CAR_CALL_HELP, new SimpleRequestCallback<String>(context) {

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                        }.getType());
                        e.onNext(response);
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        if (exception == null) return;
                        e.onError(exception);
                    }
                }, params);
            }
        });
    }

    private static void openWebFragment(Context context, String url) {
        Intent intent = new Intent(context, FunctionActivity.class);
        WebBean bean = new WebBean(url, WebConfig.H5_NATIVE_HEAD_SHOW);
        intent.putExtra(EXTRA_WEB_BEAN, bean);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
        context.startActivity(intent);
    }

    /**
     * 取消叫车
     */
    public static void cancelCallTaxi(final Activity context, final DidiOrderDetailBean mDidiOrderDetailBean) {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(context, true, false) { // 取消叫车
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, context, true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        Intent intent = new Intent(context, DidiCancelOrderResonActivity.class);
                        intent.putExtra("orderId", mDidiOrderDetailBean.order.orderId);
                        context.startActivityForResult(intent, 100);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.cancelOrder(context, callback, mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, "1");
    }

    public static String secToTime(int time) {
        if (time <= 0) return "00:00";
        StringBuilder stringBuilder = new StringBuilder();
        //Integer hour = time / 3600;
        Integer minute = time / 60 % 60;
        Integer second = time % 60;
        if (minute < 10) {
            stringBuilder.append("0");
        }
        stringBuilder.append(minute);
        stringBuilder.append(":");
        if (second < 10) {
            stringBuilder.append("0");
        }
        stringBuilder.append(second);
        return stringBuilder.toString();
    }

    /**
     * 是否是叫车人
     *
     * @param detailBean
     * @return
     */
    public static boolean isCallCarUser(DidiOrderDetailBean detailBean) {
        if (detailBean == null) return false;
        //非拼车单，是叫车人
        if (!isCarPoolOrder(detailBean)) return true;
        return PreferenceManager.UserInfo.getUserName().equals(detailBean.callCarUser);
    }

    /**
     * 行程分享
     * @param context context
     * @param orderId 订单id
     */
    public static void shareOrder(final Activity context, String orderId) {
//        PageEventUtil.onEvent(context, PageEventUtil.EVENT_TRAVEL_DRIVER_SHARE_ORDER);
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(context, true, true) { // 取消叫车
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, context, true);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        String url = jsonObject.optString("shareUrl");
                        String urlOrigin = jsonObject.optString("shareUrlOrigin");
                        String content = jsonObject.optString("shareContent");
                        String title = jsonObject.optString("shareTitle");
                        String picture = jsonObject.optString("sharePicture");
                        JSONObject params = new JSONObject();
                        JSONArray typeList = new JSONArray();
                        typeList.put(WechatFriendShare.NAME);
                        typeList.put("JDMESession");
                        try {
                            params.put("title", title);
                            params.put("content", content);
                            params.put("url", StringUtils.isEmptyWithTrim(url) ? urlOrigin : url);
                            params.put("icon", picture);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        ShareUtils.share(context, params, typeList.toString(), null, 1, "");
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.shareOrder(context, callback, orderId);
    }

    public static void orderUserValidation(final Activity context, final String orderId, final ICommonCallback callback) {
        ConfirmDialog dialog = new ConfirmDialog(context);
        dialog.setMessage(context.getString(R.string.me_didi_reimbursement_tips));
        dialog.setPositiveButton(context.getString(R.string.me_didi_reimbursement_confirm));
        dialog.setPositiveClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                NetWorkManager.userValidation(context, new SimpleRequestCallback<String>(context, true, true){
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        if (info.isSuccessful()) {
                            callback.onSuccess();
                        } else {
                            callback.onFailure();
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        callback.onFailure();
                    }
                }, orderId);
            }
        });
        dialog.show();
    }

    /**
     * 跳转详情页逻辑
     * @param activity activity
     * @param orderDetailBean data
     */
    public static void processResult(Activity activity, DidiOrderDetailBean orderDetailBean) {
        // 跳转逻辑
        if (null == orderDetailBean) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else if (TextUtils.isEmpty(orderDetailBean.order.orderId)) {
            ToastUtils.showToast(R.string.me_exception_order);
        } else {
            if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
                return;
            }
            if (!TextUtils.isEmpty(orderDetailBean.h5url) &&
                    (STATE_ONGOING.equals(orderDetailBean.order.orderStatus) || STATE_HOLD_CONFIRM.equals(orderDetailBean.order.orderStatus))) {
                Router.build(orderDetailBean.h5url).go(activity);
            } else {
                String className = getRedirectFragmentClassname(orderDetailBean);
                Log.d(TAG, "processResult: " + className);
                if (TextUtils.isEmpty(className)) {
                    ToastUtils.showToast(R.string.me_exception_order_state);
                } else {
                    Intent intent = new Intent(activity, FunctionActivity.class);
                    intent.putExtra("function", className);
                    intent.putExtra("orderDetailBean", orderDetailBean);
                    activity.startActivity(intent);
                }
            }
        }
    }

    public static boolean callCarUpgradeEnable() {
        return "1".equals(ABTestManager.getInstance().getConfigByKey(DidiUtils.DIDI_CALL_CAR_UPGRADE_GRAY, "0"));
    }

    public interface ICommonCallback {
        void onSuccess();

        void onFailure();
    }

    /**
     * 回调
     */
    public abstract static class IDetailCallback {
        protected abstract void callBack(DidiOrderDetailBean didiOrderDetailBean);

        void onFailure(HttpException exception, String info) {};
    }

    public static void callService(Activity activity,DidiOrderDetailBean mDidiOrderDetailBean){
        if (mDidiOrderDetailBean != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && null != activity && ContextCompat.checkSelfPermission(activity, Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                PermissionHelper.requestPermission(activity, activity.getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        DidiUtils.callDriver(activity, mDidiOrderDetailBean.order.servicePhone);
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                }, Manifest.permission.CALL_PHONE);
            } else {
                if (null != activity) {
                    DidiUtils.callDriver(activity, mDidiOrderDetailBean.order.servicePhone);
                }
            }
        }

    }
}
