package com.jd.oa.business.didi.net;

import com.jd.oa.AppBase;
import com.jd.oa.business.didi.model.DidiEstimatePriceBean;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.network.httpmanager.interceptors.DecryptResponseInterceptor;
import com.jd.oa.network.IHttpManager;
import com.jd.oa.network.RequestType;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class NetWorkManager {

    public static RequestType mReqeustType = RequestType.GATEWAY;

    public static Constant getConstant(){
        return Constant.getConstant(mReqeustType);
    }

    public static void post(final Object obj, final Map<String, Object> params, final SimpleRequestCallback<String> callBack, final String action) {
        post(obj, null, params, callBack, action);
    }

    public static void post(final Object obj, Map<String, String> headers, Map<String, Object> params, final SimpleRequestCallback<String> callBack, final String action) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        IHttpManager.getHttpManager(mReqeustType).newPost(obj, headers, params, callBack, action);
    }

    /**
     * @param obj      可以是 fragment，Activity，或其他 UI
     * @param action   请求接口名称
     * @param callBack 回调方法
     * @param params   接口参数 ,没有参数时，可传入 null
     */
    public static void request(final Object obj, final String action, final SimpleRequestCallback<String> callBack, final Map<String, Object> params) {
        post(obj, params, callBack, action);
    }

    /**
     * ==================  滴滴打车 Start   ==================
     **/

    public static void getAgreementStatus(final Object obj, SimpleRequestCallback<String> callBack) {
        Map<String, Object> params = new HashMap<>();
        post(obj, params, callBack, getConstant().API_GETAGREEMENTSTATUS);
    }

    /**
     * 用车协议
     *
     * @param obj
     * @param callBack
     */
    public static void getVehiclePlusAgreementStatus(final Object obj, SimpleRequestCallback<String> callBack) {
        Map<String, Object> params = new HashMap<>();
        post(obj, params, callBack, getConstant().API_PLUSAGREEMENTSTATUS);
    }

    public static void signAgreement(final Object obj, SimpleRequestCallback<String> callBack, String isAgree, String type) {
        Map<String, Object> params = new HashMap<>();
        params.put("isAgree", isAgree);
        params.put("type", type);
        post(obj, params, callBack, getConstant().API_SIGNAGREEMENT);
    }

    public static void checkChoseCarType(final Object obj, SimpleRequestCallback<String> callBack, String useCarType) {
        Map<String, Object> params = new HashMap<>();
        params.put("useCarType", useCarType);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date curDate = new Date(System.currentTimeMillis());//获取当前时间
        if (!AppBase.DEBUG) {// 正式版不使用该参数
            String str = formatter.format(curDate);
            params.put("datetimeStr", str);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put(DecryptResponseInterceptor.HEADER_DECRYPT_KEY, DecryptResponseInterceptor.HEADER_DECRYPT_RSA);
        post(obj, headers, params, callBack, getConstant().API_CHECKCHOSECARTYPE);
    }

    public static void getJobAddressByLocal(final Object obj, SimpleRequestCallback<String> callBack, String lat, String lng, String cityCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("lat", lat);
        params.put("lng", lng);
        params.put("cityCode", cityCode);
        post(obj, params, callBack, getConstant().API_GETJOBADDRESSBYLOCAL);
    }

    public static void getAddressByInput(final Object obj, SimpleRequestCallback<String> callBack, String input, String phoneNumber, String cityName) {
        Map<String, Object> params = new HashMap<>();
        params.put("input", input);
        params.put("phoneNumber", phoneNumber);
        params.put("cityName", cityName);
        post(obj, params, callBack, getConstant().API_GETADDRESSBYINPUT);
    }

    public static void getEstimatePrice(final Object obj, SimpleRequestCallback<String> callBack,
                                        String flat, String flng,
                                        String tlat, String tlng,
                                        String cityCode, String requireLeavel,
                                        String departureTime, String departureDay,
                                        String phoneNumber, String tcityCode,
                                        String useCarType) {
        getEstimatePrice(obj, callBack, flat, flng, tlat, tlng, cityCode, requireLeavel, departureTime, departureDay, phoneNumber, tcityCode, useCarType, "0", null, null);
    }

    public static void getEstimatePrice(final Object obj, SimpleRequestCallback<String> callBack,
                                        String flat, String flng,
                                        String tlat, String tlng,
                                        String cityCode, String requireLeavel,
                                        String departureTime, String departureDay,
                                        String phoneNumber, String tcityCode,
                                        String useCarType, String isCarPool,
                                        String clat, String clng) {
        Map<String, Object> params = new HashMap<>();
        params.put("flat", flat);
        params.put("flng", flng);
        params.put("tlat", tlat);
        params.put("tlng", tlng);
        params.put("cityCode", cityCode);
        params.put("tcityCode", tcityCode);// 新增目的地城市编码
        params.put("requireLeavel", requireLeavel);
        params.put("departureTime", departureTime);
        params.put("departureDay", departureDay);
        params.put("phoneNumber", phoneNumber);
        params.put("useCarType", useCarType);
        params.put("isCarPool", isCarPool);
        if(clat != null && clng != null) {
            params.put("clat", clat);
            params.put("clng", clng);
        }
        post(obj, params, callBack, getConstant().API_GETESTIMATEPRICE);
    }

    /**
     *
     * @param userCarType   1：工作加班，2：因公外出
     * @param carPoolUsers  fengtingfan_冯廷璠,fengtingfan_冯廷璠,fengtingfan_冯廷璠
     * @param callBack
     */
    public static void checkCarPoolUser(String userCarType, String carPoolUsers, SimpleRequestCallback<String> callBack) {
        Map<String, Object> params = new HashMap<>();
        params.put("useCarType", userCarType);
        params.put("carPoolUsers", carPoolUsers);
        post(null, params, callBack, getConstant().API_CHECKCARPOOLUSER);
    }

    public static void callCarOrder(final Object obj, SimpleRequestCallback<String> callBack,
                                    String flat, String flng,
                                    String tlat, String tlng,
                                    String clat, String clng,
                                    String startName, String startAddress,
                                    String endName, String endAddress,
                                    String fCityCode, String tCityCode, String requireLeavel,
                                    String departureTime, String departureDay,
                                    String phoneNumber, String reason, String useCarType, String businessType,
                                    DidiEstimatePriceBean priceBean, String addCommonAddress,
                                    String tempAddress, String stationId,String hypothetical) {
        Map<String, Object> params = new HashMap<>();
        params.put("flat", flat);
        params.put("flng", flng);
        params.put("startName", startName);
        params.put("startAddress", startAddress);
        params.put("tlat", tlat);
        params.put("tlng", tlng);
        params.put("endName", endName);
        params.put("endAddress", endAddress);
        params.put("clat", clat);
        params.put("clng", clng);
        params.put("fCityCode", fCityCode);
        params.put("tCityCode", tCityCode);
        params.put("requireLeavel", requireLeavel);
        params.put("departureTime", departureTime);
        params.put("departureDay", departureDay);
        params.put("phoneNumber", phoneNumber);
        params.put("reason", reason);
        params.put("useCarType", useCarType);
        params.put("businessType", businessType);
        params.put("estimatePrice", priceBean.price);
        params.put("estimateDistance", priceBean.distance);
        params.put("estimateTime", priceBean.duration);
        params.put("dynaPrice", priceBean.dynaPrice);
        params.put("dynaMD5", priceBean.dynaMD5);
        params.put("multiple", priceBean.multiple);
        params.put("startPrice", priceBean.startPrice);
        params.put("unitPrice", priceBean.unitPrice);
        params.put("serviceType", priceBean.serviceType);
        params.put("addCommonAddress", addCommonAddress);
        if ("0".equals(addCommonAddress) && "1".equals(useCarType)) {
            params.put("isTempAddress", priceBean.isTempAddress);
        } else {
            params.put("isTempAddress", "0");
        }
        params.put("tempAddressReason", tempAddress);
        params.put("type", priceBean.type);
        params.put("stationId", stationId);
        params.put("hypothetical", hypothetical);
        params.put("estimatePriceList", priceBean.callbackParams.toString());
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        Map<String, String> headers = new HashMap<>();
        headers.put(DecryptResponseInterceptor.HEADER_DECRYPT_KEY, DecryptResponseInterceptor.HEADER_DECRYPT_RSA);
        post(obj, headers, params, callBack, getConstant().API_CALLCARORDER);
    }

    /**
     * 多人同行叫车确认流程
     */
    public static void preCallCarOrderForPool(final Object obj, SimpleRequestCallback<String> callBack,
                                              String flat, String flng,
                                              String tlat, String tlng,
                                              String clat, String clng,
                                              String startName, String startAddress,
                                              String endName, String endAddress,
                                              String fCityCode, String tCityCode, String requireLeavel,
                                              String departureTime, String departureDay,
                                              String phoneNumber, String reason, String useCarType, String businessType,
                                              DidiEstimatePriceBean priceBean, String addCommonAddress,
                                              String tempAddress, String carPoolUsers) {
        Map<String, Object> params = new HashMap<>();
        params.put("flat", flat);
        params.put("flng", flng);
        params.put("startName", startName);
        params.put("startAddress", startAddress);
        params.put("tlat", tlat);
        params.put("tlng", tlng);
        params.put("endName", endName);
        params.put("endAddress", endAddress);
        params.put("clat", clat);
        params.put("clng", clng);
        params.put("fCityCode", fCityCode);
        params.put("tCityCode", tCityCode);
        params.put("requireLeavel", requireLeavel);
        params.put("departureTime", departureTime);
        params.put("departureDay", departureDay);
        params.put("phoneNumber", phoneNumber);
        params.put("reason", reason);
        params.put("useCarType", useCarType);
        params.put("businessType", businessType);
        params.put("estimatePrice", priceBean.price);
        params.put("estimateDistance", priceBean.distance);
        params.put("estimateTime", priceBean.duration);
        params.put("dynaPrice", priceBean.dynaPrice);
        params.put("dynaMD5", priceBean.dynaMD5);
        params.put("multiple", priceBean.multiple);
        params.put("startPrice", priceBean.startPrice);
        params.put("unitPrice", priceBean.unitPrice);
        params.put("serviceType", priceBean.serviceType);
        params.put("addCommonAddress", addCommonAddress);
        params.put("estimatePriceList", priceBean.callbackParams.toString());
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        if ("0".equals(addCommonAddress) && "1".equals(useCarType)) {
            params.put("isTempAddress", priceBean.isTempAddress);
        } else {
            params.put("isTempAddress", "0");
        }
        params.put("tempAddressReason", tempAddress);
        params.put("type", priceBean.type);
        params.put("carPoolUsers", carPoolUsers);
        post(obj, params, callBack, getConstant().API_PRECALLCARORDERFORPOOL);
    }

    public static void afterCallCarOrderForPool (String orderId, boolean containsColleague, SimpleRequestCallback<String> callBack) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("isCarPool", containsColleague? "1" : "2");
        params.put("callCarForPool", "1");

        post(null, params, callBack, getConstant().API_AFTERCALLCARORDERFORPOOL);

    }

    public static void getCarPoolOrderStatus(String orderId,String expireUserNames, String finalTimestamp, SimpleRequestCallback<String> callBack) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("expireUserNames", expireUserNames);
        params.put("finalTimestamp", finalTimestamp);
        post(null, params, callBack, getConstant().API_GETCARPOOLORDERSTATUS);
    }

    public static void reCallCarOrder(final Object obj, SimpleRequestCallback<String> callBack, String orderId, String requireLevel, String phoneNumber) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("requireLevel", requireLevel);
        params.put("phoneNumber", phoneNumber);
        post(obj, params, callBack, getConstant().API_RECALLCARORDER);
    }

    public static void getOrderList(final Object obj, SimpleRequestCallback<String> callBack, String pageNo, String pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        Map<String, String> headers = new HashMap<>();
        headers.put(DecryptResponseInterceptor.HEADER_DECRYPT_KEY, DecryptResponseInterceptor.HEADER_DECRYPT_RSA);
        post(obj, headers, params, callBack, getConstant().API_GETORDERLIST);
    }


    public static void getOrderDetail(final Object obj, SimpleRequestCallback<String> callBack, String orderId, String phoneNumber) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("phoneNumber", phoneNumber);
        Map<String, String> headers = new HashMap<>();
        headers.put(DecryptResponseInterceptor.HEADER_DECRYPT_KEY, DecryptResponseInterceptor.HEADER_DECRYPT_RSA);
        post(obj, headers, params, callBack, getConstant().API_GETORDERDETAIL);
    }

    public static void cancelOrder(final Object obj, SimpleRequestCallback<String> callBack, String orderId, String phoneNumber, String force) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("phoneNumber", phoneNumber);
        params.put("force", force);
        post(obj, params, callBack, getConstant().API_CANCELORDER);
    }

    public static void shareOrder(final Object obj, SimpleRequestCallback<String> callBack, String orderId) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        post(obj, params, callBack, getConstant().API_SHAREORDER);
    }

    public static void getCarPoolUserStatus(String orderId, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        post(null, params, callback, getConstant().API_GETCARPOOLUSERSTATUS);
    }

    public static void confirmCarPoolUserStatus(String orderId, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("flowFlag", "20");
        post(null, params, callback, getConstant().API_CONFIRMFORORDER);
    }

    public static void rejectCarPoolUserStatus(String orderId, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("flowFlag", "30");
        post(null, params, callback, getConstant().API_CONFIRMFORORDER);
    }

    public static void expiredCarPoolUserStatus(String orderId, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("flowFlag", "50");
        post(null, params, callback, getConstant().API_CONFIRMFORORDER);
    }

    /**
     * ==================  滴滴打车 End   ==================
     **/


    /* 用车二期*/
    // 检查城市是否可用
    public static void checkCity(final Object obj, SimpleRequestCallback<String> callBack, String cityName) {
        Map<String, Object> params = new HashMap<>();
        params.put("cityName", cityName);
        post(obj, params, callBack, getConstant().API_CHECKCITY);
    }

    // 确认行程
    public static void confirmOrder(final Object obj, SimpleRequestCallback<String> callBack, String orderId) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        post(obj, params, callBack, getConstant().API_AGREEPAY);
    }

    // 带有异常原因的确认行程
    public static void confirmOrder(final Object obj, SimpleRequestCallback<String> callBack, String orderId, String confirmPayDesc, String passengersErp, String passengersName) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("confirmPayDesc", confirmPayDesc);
        params.put("passengersErp", passengersErp);
        params.put("passengersName", passengersName);
        post(obj, params, callBack, getConstant().API_AGREECONFIRMPAY);
    }

    // 取消原因列表
    public static void getCancelOrderReasonList(final Object obj, SimpleRequestCallback<String> callBack) {
        post(obj, null, callBack, getConstant().API_GETCANCELORDERREASONLIST);
    }

    // 保存取消原因
    public static void saveCancelOrderReason(final Object obj, SimpleRequestCallback<String> callBack, String orderId, String cancelReasonId) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("cancelReasonId", cancelReasonId);
        post(obj, params, callBack, getConstant().API_SAVECANCELORDERREASON);
    }

    // 投诉选项列表
    public static void getComplaintReasonList(final Object obj, SimpleRequestCallback<String> callBack, String type, String serviceType) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        params.put("serviceType", serviceType);
        post(obj, params, callBack, getConstant().API_GETCOMPLAINTREASONLIST);
    }

    // 提交投诉
    public static void submitComplaint(final Object obj, SimpleRequestCallback<String> callBack, String orderId, String reasonId, String reasonText, String content, String type) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("reasonId", reasonId);
        params.put("reasonText", reasonText);
        params.put("content", content);
        params.put("reasonType", type);
        post(obj, params, callBack, getConstant().API_SUBMITCOMPLAINT);
    }

    // 评价标签
    public static void getCommentLabel(final Object obj, SimpleRequestCallback<String> callBack) {
        post(obj, null, callBack, getConstant().API_GETCOMMENTLABEL);
    }

    // 提交评价
    public static void submitComment(final Object obj, SimpleRequestCallback<String> callBack, String orderId, String level, String comment) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("level", level);
        params.put("comment", comment);
        post(obj, params, callBack, getConstant().API_SUBMITCOMMENT);
    }

    //重新发起用车审批流程
    public static void submitAgain(final Object obj, SimpleRequestCallback<String> callback, String orderId, String reason, String passengersErp, String passengersName) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("remark", reason);
        params.put("passengersErp", passengersErp);
        params.put("passengersName", passengersName);
        post(obj, params, callback, getConstant().API_RE_APPROVE);
    }

    //用户确认领导驳回结果
    public static void userValidation(final Object obj, SimpleRequestCallback<String> callback, String orderId) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        post(obj, params, callback, getConstant().API_USER_CONFIRM_PROCESS);
    }

    public static void callCarOrderUpgrade(final Object obj, SimpleRequestCallback<String> callBack,
                                    String flat, String flng,
                                    String tlat, String tlng,
                                    String clat, String clng,
                                    String startName, String startAddress,
                                    String endName, String endAddress,
                                    String fCityCode, String tCityCode, String requireLeavel,
                                    String departureTime, String departureDay,
                                    String phoneNumber, String reason, String useCarType, String businessType,
                                    DidiEstimatePriceBean priceBean, String addCommonAddress,
                                    String tempAddress, String stationId,String hypothetical) {
        Map<String, Object> params = new HashMap<>();
        params.put("flat", flat);
        params.put("flng", flng);
        params.put("startName", startName);
        params.put("startAddress", startAddress);
        params.put("tlat", tlat);
        params.put("tlng", tlng);
        params.put("endName", endName);
        params.put("endAddress", endAddress);
        params.put("clat", clat);
        params.put("clng", clng);
        params.put("fCityCode", fCityCode);
        params.put("tCityCode", tCityCode);
        params.put("requireLeavel", requireLeavel);
        params.put("departureTime", departureTime);
        params.put("departureDay", departureDay);
        params.put("phoneNumber", phoneNumber);
        params.put("reason", reason);
        params.put("useCarType", useCarType);
        params.put("businessType", businessType);
        params.put("estimatePrice", priceBean.price);
        params.put("estimateDistance", priceBean.distance);
        params.put("estimateTime", priceBean.duration);
        params.put("dynaPrice", priceBean.dynaPrice);
        params.put("dynaMD5", priceBean.dynaMD5);
        params.put("multiple", priceBean.multiple);
        params.put("startPrice", priceBean.startPrice);
        params.put("unitPrice", priceBean.unitPrice);
        params.put("serviceType", priceBean.serviceType);
        params.put("addCommonAddress", addCommonAddress);
        if ("0".equals(addCommonAddress) && "1".equals(useCarType)) {
            params.put("isTempAddress", priceBean.isTempAddress);
        } else {
            params.put("isTempAddress", "0");
        }
        params.put("tempAddressReason", tempAddress);
        params.put("type", priceBean.type);
        params.put("stationId", stationId);
        params.put("hypothetical", hypothetical);
        params.put("estimatePriceList", priceBean.callbackParams.toString());
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        Map<String, String> headers = new HashMap<>();
        headers.put(DecryptResponseInterceptor.HEADER_DECRYPT_KEY, DecryptResponseInterceptor.HEADER_DECRYPT_RSA);
        post(obj, headers, params, callBack, getConstant().API_CALL_CAR_ORDER_UPGRADE);
    }
}
