package com.jd.oa.business.caraddress;

import android.util.Log;

import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.business.didi.model.DidiCityBean;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONObject;

public class CarAddressModifyPresenterImpl extends AbsMVPPresenter<CarAddressContract.ICarAddressModifyView> implements CarAddressContract.ICarAddressModifyPresenter {

    private CarAddressContract.ICarAddressRepo mRepo;

    public CarAddressModifyPresenterImpl(CarAddressContract.ICarAddressModifyView view) {
        super(view);
        mRepo = new CarAddressRepoImpl();
    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void addCarAddress(CarAddress carAddress) {
        view.showLoading(null);
        mRepo.addCarAddress(carAddress, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                view.onModifyCarAddressSuccess(jsonObject);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        });
    }

    @Override
    public void modifyCarAddress(CarAddress carAddress) {
        view.showLoading(null);
        mRepo.modifyCarAddress(carAddress, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                view.onModifyCarAddressSuccess(jsonObject);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        });
    }

    @Override
    public void getCityList(String cityName) {
        mRepo.checkCity(cityName, new LoadDataCallback<DidiCityBean>() {
            @Override
            public void onDataLoaded(DidiCityBean didiCityBean) {
                view.onCheckCityResult(didiCityBean);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        });
    }
}
