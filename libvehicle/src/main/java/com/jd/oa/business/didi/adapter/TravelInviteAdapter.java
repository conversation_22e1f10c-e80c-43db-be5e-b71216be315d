package com.jd.oa.business.didi.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

/**
 * Created by peidongbiao on 2019/3/20
 */
public class TravelInviteAdapter extends BaseRecyclerAdapter<CarPoolUserStatus, RecyclerView.ViewHolder> {

    private OnUserClickListener mOnUserClickListener;

    public TravelInviteAdapter(Context context) {
        super(context);
    }

    public TravelInviteAdapter(Context context, List<CarPoolUserStatus> data) {
        super(context, data);
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_recycler_travel_together, parent, false);
        final ViewHolder holder = new ViewHolder(view);
        holder.avatar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mOnUserClickListener != null) {
                   mOnUserClickListener.onUserClick(v, holder.getAdapterPosition());
                }
            }
        });
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        CarPoolUserStatus data = getItem(position);
        ViewHolder viewHolder = (ViewHolder) holder;
        ImageLoader.load(getContext(), viewHolder.avatar, data.getAvatar(), false, R.drawable.ddtl_avatar_personal_normal);
        viewHolder.name.setText(data.getRealName());
        viewHolder.erp.setText(data.getUserName());
        ImageLoader.load(getContext(), viewHolder.avatar, data.getAvatar());
        if(data.isCallCarUser()) {
            //发起人
            viewHolder.status.setText(R.string.me_car_pool_call_car_user);
            viewHolder.status.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            viewHolder.status.setTextColor(ContextCompat.getColor(getContext(), R.color.comm_text_normal));
        }else {
            if(DidiUtils.CAR_POOL_STATE_CONFIRMING.equals(data.getFlowFlag())) {
                viewHolder.status.setText(R.string.me_car_car_pool_invite_wait_for_confirm);
                viewHolder.status.setCompoundDrawablesWithIntrinsicBounds(R.drawable.jdme_ic_car_wait_for_confirm, 0, 0, 0);
                viewHolder.status.setTextColor(ContextCompat.getColor(getContext(), R.color.comm_text_normal));
            }else if (DidiUtils.CAR_POOL_STATE_EXPIRED.equals(data.getFlowFlag())) {
                viewHolder.status.setText(R.string.me_car_travel_invite_unconfirmed);
                viewHolder.status.setCompoundDrawablesWithIntrinsicBounds(R.drawable.jdme_ic_car_unconfirmed, 0, 0, 0);
                viewHolder.status.setTextColor(ContextCompat.getColor(getContext(), R.color.me_color_yellow));
            } else if (DidiUtils.CAR_POOL_STATE_APPROVED.equals(data.getFlowFlag())) {
                viewHolder.status.setText(R.string.me_car_travel_invite_accepted);
                viewHolder.status.setCompoundDrawablesWithIntrinsicBounds(R.drawable.jdme_ic_car_accepted, 0, 0, 0);
                viewHolder.status.setTextColor(ContextCompat.getColor(getContext(), R.color.me_color_green));
            } else if (DidiUtils.CAR_POOL_STATE_REJECTED.equals(data.getFlowFlag())) {
                viewHolder.status.setText(R.string.me_car_travel_invite_rejected);
                viewHolder.status.setCompoundDrawablesWithIntrinsicBounds(R.drawable.jdme_ic_car_rejected, 0, 0, 0);
                viewHolder.status.setTextColor(ContextCompat.getColor(getContext(), R.color.me_color_red));
            }
        }
    }

    public void setOnUserClickListener(OnUserClickListener onUserClickListener) {
        mOnUserClickListener = onUserClickListener;
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        ImageView avatar;
        TextView name;
        TextView erp;
        TextView status;

        public ViewHolder(View itemView) {
            super(itemView);
            avatar = itemView.findViewById(R.id.iv_avatar);
            name = itemView.findViewById(R.id.tv_name);
            erp = itemView.findViewById(R.id.tv_erp);
            status = itemView.findViewById(R.id.tv_status);
        }
    }

    public interface OnUserClickListener {
        void onUserClick(View view, int position);
    }
}