package com.jd.oa.business.didi.interceptor;

import android.net.Uri;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.didi.DidiMainFragment;
import com.jd.oa.business.didi.model.DidiOrderBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/*
 * 用车拦截器
 * 拦截目标：
 *     jdme://jm/biz/vehicle/overtimeTaxi   加班用车
 *     jdme://jm/biz/vehicle/publicVehicle  因公用车
 *
 */
public class VehicleInterceptor implements RouteInterceptor {
    private static final String TAG = "VehicleInterceptor";

    private final String SCHEME = "jdme";
    private final String HOST = "jm";
    private final String[] PATH = new String[]{"/biz/vehicle/overtimeTaxi", "/biz/vehicle/publicVehicle"};

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost()) || !Arrays.asList(PATH).contains(uri.getPath())) {
                // 放行
                return chain.process();
            }
            MELogUtil.localI(TAG, "intercept: " + uri.toString());
            if (uri.getPath().equals(PATH[0])) {
                go("1");
            } else {
                go("2");
            }
            // 拦截
            return chain.intercept();
        } catch (Exception e) {
            MELogUtil.localE(TAG, "intercept exception:", e);
            return chain.process();
        }
    }

    public void go(String carType) {
        if (AppBase.getTopActivity() != null) {
            NetWorkManager.getOrderList(this, new SimpleRequestCallback<String>(null, false, true) {
                @Override
                public void onNoNetWork() {
                    super.onNoNetWork();
                }

                @Override
                public void onStart() {
                    super.onStart();
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    DidiMainFragment.checkChoseCarType(carType, AppBase.getTopActivity(), new ArrayList<>());
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    try {
                        JSONObject infoObject = new JSONObject(info.result);
                        JSONObject content = infoObject.optJSONObject("content");
                        if (content != null) {
                            JSONArray orderList = content.optJSONArray("orderList");
                            List<DidiOrderBean> listOrderBean = JsonUtils.getGson().fromJson(orderList.toString(), new TypeToken<List<DidiOrderBean>>() {
                            }.getType());
                            DidiMainFragment.checkChoseCarType(carType, AppBase.getTopActivity(), listOrderBean);
                            return;
                        }
                    } catch (Exception e) {
                        MELogUtil.localE(TAG, "go exception:", e);
                    }
                    DidiMainFragment.checkChoseCarType(carType, AppBase.getTopActivity(), new ArrayList<>());
                }
            }, "1", "5");
        }
    }
}
