package com.jd.oa.business.didi;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.AppBase;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;

import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;

public class DidiColleaguesSelectFragment extends BaseFragment {
    public static final String ARG_ORDER = "arg.order";

    public static final int REQUEST_CODE_ADD = 100;
    public static final int MAX_COLLEAGUES_COUNT = 3;
    private RecyclerView mRecyclerView;

    private DidiColleaguesSelectAdapter mAdapter;
    private ArrayList<MemberEntityJd> mList;
    private DidiOrderDetailBean mDidiOrderDetailBean;

    public static DidiColleaguesSelectFragment newInstance() {
        return newInstance(null);
    }

    public static DidiColleaguesSelectFragment newInstance(DidiOrderDetailBean detailBean) {
        DidiColleaguesSelectFragment fragment = new DidiColleaguesSelectFragment();
        Bundle arguments = new Bundle();
        if (detailBean != null) {
            arguments.putSerializable(ARG_ORDER, detailBean);
        }
        fragment.setArguments(arguments);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            if (getArguments().containsKey(ARG_ORDER)) {
                mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable(ARG_ORDER);
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_didi_colleagues_select, container, false);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mRecyclerView = view.findViewById(R.id.rv_list);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false));
        mList = getColleagueList(mDidiOrderDetailBean);
        mAdapter = new DidiColleaguesSelectAdapter(mList);
        mAdapter.setAction(new DidiColleaguesSelectAdapter.Action() {
            @Override
            public void onAddBtnClick() {
                MemberListEntityJd entity = new MemberListEntityJd();
                entity.setFrom(TYPE_ADD_MEMBER).setShowConstantFilter(true).setOptionalFilter(mList).setShowSelf(false);
                AppBase.iAppBase.gotoMemberList(getActivity(), REQUEST_CODE_ADD, entity, new Callback<ArrayList<MemberEntityJd>>() {
                    @Override
                    public void onSuccess(ArrayList<MemberEntityJd> bean) {
                        handleMemberListResult(bean);
                    }

                    @Override
                    public void onFail() {
                    }
                });
            }
        });
        mRecyclerView.setAdapter(mAdapter);
    }

    public ArrayList<MemberEntityJd> getSelectList() {
        return mList;
    }

    private void handleMemberListResult(ArrayList<MemberEntityJd> result) {
        if (result != null) {
            if (result.size() > MAX_COLLEAGUES_COUNT) {
                ToastUtils.showToast(R.string.me_car_address_colleagues_over_tip);
                return;
            }
            mList.clear();
            mList.addAll(result);
        }
        mAdapter.notifyDataSetChanged();
    }

    private ArrayList<MemberEntityJd> getColleagueList(DidiOrderDetailBean detailBean) {
        ArrayList<MemberEntityJd> list = new ArrayList<>();
        if (detailBean == null
                || !DidiUtils.isCarPoolOrder(detailBean)
                || CollectionUtil.isEmptyOrNull(detailBean.carPoolUser)) {
            return list;
        }
        for (CarPoolUserStatus status : detailBean.carPoolUser) {
            if (!status.getUserName().equals(detailBean.callCarUser)
                    && (DidiUtils.CAR_POOL_STATE_APPROVED.equals(status.getFlowFlag()) || DidiUtils.CAR_POOL_STATE_COMPLETE.equals(status.getFlowFlag()))) {
                MemberEntityJd entity = new MemberEntityJd();
                entity.mId = status.getUserName();
                entity.mName = status.getRealName();
                entity.mAvatar = status.getAvatar();
                entity.mApp = AppBase.iAppBase.getTimlineAppId();
                entity.mType = MemberEntityJd.TYPE_CONTACT;
                list.add(entity);
            }
        }
        return list;
    }
}
