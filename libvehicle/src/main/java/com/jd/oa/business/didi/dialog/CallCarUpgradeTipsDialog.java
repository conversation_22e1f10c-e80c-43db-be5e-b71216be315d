package com.jd.oa.business.didi.dialog;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDialog;

import com.jd.oa.business.R;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.utils.DisplayUtil;

/**
 * Created by peidongbiao on 2019-05-16
 */
public class CallCarUpgradeTipsDialog extends AppCompatDialog {

    private CheckBox mCheckBox;
    private TextView mTvCancel;
    private TextView mTvCall;
    private TextView mTvTips;

    public CallCarUpgradeTipsDialog(Context context) {
        super(context);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_dialog_didi_call_car_upgrade_tips);
        mCheckBox = findViewById(R.id.cb_check);
        mTvTips = findViewById(R.id.tv_tips);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvCall = findViewById(R.id.tv_call);
        if (getWindow() != null) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = (int) (DisplayUtil.getScreenWidth(getContext()) * 0.8);
            getWindow().setAttributes(layoutParams);
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_bg_colleague_tips_dialog);
        }
        setCancelable(false);
        setCanceledOnTouchOutside(false);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
//                TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_JDME_CALL_CAR_UPGRADE_TIPS_NO_MORE, isChecked);
            }
        });
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    public CallCarUpgradeTipsDialog setOnCallClickListener(final OnConfirmClickListener onConfirmClickListener) {
        mTvCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_JDME_CALL_CAR_UPGRADE_TIPS_NO_MORE, mCheckBox.isChecked());
                onConfirmClickListener.onConfirmClick();
                dismiss();
            }
        });
        return this;
    }

    public CallCarUpgradeTipsDialog setTipsContent(String content) {
        mTvTips.setText(content);
        return this;
    }

    public interface OnConfirmClickListener {
        void onConfirmClick();
    }
}