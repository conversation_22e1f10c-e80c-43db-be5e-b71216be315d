package com.jd.oa.business.didi.utils;

import android.text.TextUtils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.didi.model.DidiAddressBean;

public class CheckCoordinatesUtil {
    public static final String TAG = MELogUtil.TAG_TAX;

    public static AddressBean checkAddressCoordinates(AddressBean bean) {
        if ((Double.parseDouble(bean.lat) > 90 || Double.parseDouble(bean.lat) < -90) //纬度度超出-90度到90度的范围
                && (Double.parseDouble(bean.lng) <= 90 && Double.parseDouble(bean.lng) >= -90)) {//经度在-90度到90度的范围
            //交换经纬度
            String temp = bean.lat;
            bean.lat = bean.lng;
            bean.lng = temp;
        }
        return bean;
    }


    public static DidiAddressBean reverseAddressCoordinates(DidiAddressBean bean) {
        if (bean == null || TextUtils.isEmpty(bean.lat) || TextUtils.isEmpty(bean.lng
        )) return bean;

        if ((Double.parseDouble(bean.lat) > 90 || Double.parseDouble(bean.lat) < -90) //纬度度超出-90度到90度的范围
                && (Double.parseDouble(bean.lng) <= 90 && Double.parseDouble(bean.lng) >= -90)) {//经度在-90度到90度的范围
            //交换经纬度
            String temp = bean.lat;
            bean.lat = bean.lng;
            bean.lng = temp;
        }
        return bean;
    }

    /**
     * 经纬度是否在中国经纬度的范围
     *
     * @param bean
     * @return
     */
    public static boolean checkAddressCoordinatesInChina(AddressBean bean) {
        return (Double.parseDouble(bean.lat) >= 4 && Double.parseDouble(bean.lat) <= 53.5)
                && (Double.parseDouble(bean.lng) <= 135 && Double.parseDouble(bean.lng) >= 73.5);
    }

    /**
     * 检验经纬度有效性
     *
     * @param bean
     * @return
     */
    public static boolean checkAddressCoordinatesValidity(AddressBean bean) {
        return (Double.parseDouble(bean.lat) >= -90 && Double.parseDouble(bean.lat) <= 90)
                && (Double.parseDouble(bean.lng) <= 180 && Double.parseDouble(bean.lng) >= -180);
    }

    /**
     * 检验经纬度有效性
     *
     * @param lat 纬度
     * @param lng 经度
     * @return
     */
    public static boolean checkAddressCoordinatesValidity(String lat, String lng) {
        if (TextUtils.isEmpty(lat) || TextUtils.isEmpty(lng)) return false;
        return (Double.parseDouble(lat) >= -90 && Double.parseDouble(lat) <= 90)
                && (Double.parseDouble(lng) <= 180 && Double.parseDouble(lng) >= -180);
    }

    public static class AddressBean {
        String lat;
        String lng;

        public AddressBean(String lat, String lng) {
            this.lat = lat;
            this.lng = lng;
        }

        public String getLat() {
            return lat;
        }

        public void setLat(String lat) {
            this.lat = lat;
        }

        public String getLng() {
            return lng;
        }

        public void setLng(String lng) {
            this.lng = lng;
        }
    }


}
