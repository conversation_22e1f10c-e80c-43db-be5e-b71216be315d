package com.jd.oa.business.didi.model;

import androidx.annotation.Keep;

import java.io.Serializable;
import java.util.List;

/**
 * Created by p<PERSON><PERSON><PERSON>o on 2019/4/3
 */
@Keep
public class CarPoolInvitationStatus implements Serializable {

    private String startTimestamp;
    private String curTimestamp;
    private String callCarUserName;
    private String callCarRealName;
    private String startName;
    private String endName;
    private String confirmStopSecond;
    private List<CarPoolUserStatus> carPoolUsers;

    public String getStartTimestamp() {
        return startTimestamp;
    }

    public void setStartTimestamp(String startTimestamp) {
        this.startTimestamp = startTimestamp;
    }

    public String getCurTimestamp() {
        return curTimestamp;
    }

    public void setCurTimestamp(String curTimestamp) {
        this.curTimestamp = curTimestamp;
    }

    public String getCallCarUserName() {
        return callCarUserName;
    }

    public void setCallCarUserName(String callCarUserName) {
        this.callCarUserName = callCarUserName;
    }

    public String getCallCarRealName() {
        return callCarRealName;
    }

    public void setCallCarRealName(String callCarRealName) {
        this.callCarRealName = callCarRealName;
    }

    public String getStartName() {
        return startName;
    }

    public void setStartName(String startName) {
        this.startName = startName;
    }

    public String getEndName() {
        return endName;
    }

    public void setEndName(String endName) {
        this.endName = endName;
    }

    public String getConfirmStopSecond() {
        return confirmStopSecond;
    }

    public void setConfirmStopSecond(String confirmStopSecond) {
        this.confirmStopSecond = confirmStopSecond;
    }

    public List<CarPoolUserStatus> getCarPoolUsers() {
        return carPoolUsers;
    }

    public void setCarPoolUsers(List<CarPoolUserStatus> carPoolUsers) {
        this.carPoolUsers = carPoolUsers;
    }
}