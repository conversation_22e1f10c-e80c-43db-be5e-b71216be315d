package com.jd.oa.business.didi;

import android.Manifest;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.dialog.ColleagueInfoDialog;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.widget.CircleProgressRunningView;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;


import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * Created by qudongshi on 2016/1/15.
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderDetailDrivingFragment extends DidiBaseFragment implements OperatingListener, TimlineMessageListener {


    String mPrice; // 费用
    private View mRootView;
    private CircleProgressRunningView mCprvDriving;
    private RelativeLayout mRlDriverInfo;
    private CircleImageView mIvDriverPic;
    private TextView mTvDriverName;
    private TextView mTvCarType;
    private TextView mTvCarNo;

    private LinearLayout mLlStarContent;
    private RatingBar mRbStar;
    private TextView mTvFraction;
    private TextView mTvOrder;
    private View mLLTip;
    private View mLLButtonGroup;
    private View ll_complain;
    private TextView tv_complain;
    private View ll_help;
    private ImageView mIvTelephone;
    private View layout_colleague_list;
    private LinearLayout layout_colleague_container;
    private TextView mTvMsg;
    private TextView mTvTips;
    private LinearLayout ll_share;

    private DidiOrderDetailBean mDidiOrderDetailBean;

    private int pollingTime = 30;

    private boolean mOmplaintsSuccess = false;

    private View.OnClickListener mOnClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            DidiOrderDetailBean.ColleagueInfo info = (DidiOrderDetailBean.ColleagueInfo) v.getTag();
            String phone = info.phoneNumber;
            String address = info.address;
            ColleagueInfoDialog dialog = new ColleagueInfoDialog(getContext());
            dialog.setPhone(phone);
            dialog.setAddress(address);
            dialog.show();
        }
    };
    private ImageView mIvService;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_driving, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_driving);
        mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        initView();
        AppBase.iAppBase.registerTimlineMessage(MESSAGE_TYPE_CAR_POOL_COLLEAGUE, this);
        return mRootView;
    }

    private void initView() {
        mCprvDriving = mRootView.findViewById(R.id.cprv_driving);
        mRlDriverInfo = mRootView.findViewById(R.id.ll_driver_info);
        mIvDriverPic = mRootView.findViewById(R.id.iv_driver_pic);
        mTvDriverName = mRootView.findViewById(R.id.tv_driver_name);
        mTvCarType = mRootView.findViewById(R.id.tv_car_type);
        mTvCarNo = mRootView.findViewById(R.id.tv_car_no);
        mIvService = mRootView.findViewById(R.id.iv_icon);

        LinearLayout mLlStarContent = mRootView.findViewById(R.id.ll_star_content);
        mRbStar = mRootView.findViewById(R.id.rab_star);
        mTvFraction = mRootView.findViewById(R.id.tv_fraction);
        mTvOrder = mRootView.findViewById(R.id.tv_order);
        mLLTip = mRootView.findViewById(R.id.ll_tip);
        mLLButtonGroup = mRootView.findViewById(R.id.ll_btn_group);
        ll_complain = mRootView.findViewById(R.id.ll_complain);
        ll_share = mRootView.findViewById(R.id.ll_share);
        tv_complain = mRootView.findViewById(R.id.tvComplain);
        ll_help = mRootView.findViewById(R.id.ll_help);
        mIvTelephone = mRootView.findViewById(R.id.iv_telephone);
        layout_colleague_list = mRootView.findViewById(R.id.layout_colleague_list);
        layout_colleague_container = mRootView.findViewById(R.id.layout_colleague_container);
        mTvMsg = mRootView.findViewById(R.id.tv_msg);
        mTvTips = mRootView.findViewById(R.id.tv_tips);

        mIvTelephone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveling_phoneDriver_click,JDMAConstants.mobile_employeeTravel_traveling_phoneDriver_click);

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mDidiOrderDetailBean.order.driverPhone);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.driverPhone);
                }
            }
        });

        ll_help.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveling_help_click,JDMAConstants.mobile_employeeTravel_traveling_help_click);
                PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_travel_help_confirmation, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_DENIED
                                || ContextCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_DENIED
                                || ContextCompat.checkSelfPermission(getContext(), Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_DENIED) {
                            PermissionHelper.requestPermissions(getActivity(), getResources().getString(R.string.me_request_permission_title_normal), getResources().getString(R.string.me_request_permission_location_travel),
                                    new RequestPermissionCallback() {
                                        @Override
                                        public void allGranted() {
                                            DidiUtils.urgentHelp(getContext(), mDidiOrderDetailBean.order.orderId);
                                        }

                                        @Override
                                        public void denied(List<String> deniedList) {
                                            DidiUtils.urgentHelp(getContext(), mDidiOrderDetailBean.order.orderId);
                                        }
                                    }, Manifest.permission.ACCESS_COARSE_LOCATION,
                                    Manifest.permission.ACCESS_FINE_LOCATION,
                                    Manifest.permission.WRITE_EXTERNAL_STORAGE);
                        } else {
                            DidiUtils.urgentHelp(getContext(), mDidiOrderDetailBean.order.orderId);
                        }
                    }
                });
            }
        });

        ll_complain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveling_complaint_click,JDMAConstants.mobile_employeeTravel_traveling_complaint_click);
                if ("1".equals(mDidiOrderDetailBean.order.isComplainted))
                    return;
                Bundle bundle1 = new Bundle();
                bundle1.putString("orderId", mDidiOrderDetailBean.order.orderId);
                bundle1.putString("serviceType", mDidiOrderDetailBean.order.serviceType);
                bundle1.putInt("flag", 2);
                FragmentUtils.replaceWithCommit(getActivity(), DidiOrderDetailOmplaintsFragment.class, R.id.me_fragment_content, true, bundle1, false);
            }
        });

        ll_share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveling_travelShare_click,JDMAConstants.mobile_employeeTravel_traveling_travelShare_click);
                DidiUtils.shareOrder(requireActivity(), mDidiOrderDetailBean.order.orderId);
            }
        });

        String tips = mDidiOrderDetailBean.drivingShowTip;
        mLLTip.setVisibility(TextUtils.isEmpty(tips)? View.GONE : View.VISIBLE);
        mTvTips.setText(tips);

        mTvDriverName.setText(mDidiOrderDetailBean.order.driverName);
        ImageLoader.load(requireActivity(), mIvService, mDidiOrderDetailBean.icon, false, R.drawable.jdme_icon_use_car_service_default, R.drawable.jdme_icon_use_car_service_default);
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.driverLevel)) {
            try {
                DidiUtils.showDriverLevel(Float.parseFloat(mDidiOrderDetailBean.order.driverLevel), mRbStar);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mTvFraction.setText(mDidiOrderDetailBean.order.driverLevel + mTvFraction.getContext().getString(R.string.me_didi_minute));
            mTvOrder.setText(mDidiOrderDetailBean.order.driverOrderCount + mTvFraction.getContext().getString(R.string.me_didi_unit_dan));
        } else {
            mLlStarContent.setVisibility(View.GONE);
        }
        mTvCarNo.setText(mDidiOrderDetailBean.order.driverCard);
        mTvCarType.setText(StringUtils.getSubStringFromStart(mDidiOrderDetailBean.order.driverCarType, 12));
        DidiUtils.getDriverImg(mDidiOrderDetailBean.order.driverAvatar, mIvDriverPic, getActivity());

        mCprvDriving.setStartTime(coverTimerToSecond(mDidiOrderDetailBean.order.driveingTime) * 1000);
        mCprvDriving.start();
        mCprvDriving.setShowWave(true);

        if (StringUtils.convertToInt(mDidiOrderDetailBean.order.pollingTime) > 0) {
            pollingTime = StringUtils.convertToInt(mDidiOrderDetailBean.order.pollingTime);
        }

        if ("1".equals(mDidiOrderDetailBean.order.isComplainted)) {
            tv_complain.setText(R.string.me_didi_title_order_already_omplaints);
        }
        if (!DidiUtils.isCallCarUser(mDidiOrderDetailBean)) {
            //非拼车发起人
            ll_complain.setVisibility(View.GONE);
            ll_help.setVisibility(View.GONE);
            mIvTelephone.setVisibility(View.GONE);
        }

        //同行人信息,提示
        if (mDidiOrderDetailBean.carPoolInfoData != null) {
            mTvMsg.setVisibility(TextUtils.isEmpty(mDidiOrderDetailBean.carPoolInfoData.carPoolMsg)? View.INVISIBLE : View.VISIBLE);
            mTvMsg.setText(mDidiOrderDetailBean.carPoolInfoData.carPoolMsg);
            showColleagues(mDidiOrderDetailBean.carPoolInfoData.carPoolInfo);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        DidiUtils.getOrderDetail(getActivity(), mDidiOrderDetailBean.order.orderId, mDidiOrderDetailBean.order.phoneNumber, new DidiUtils.IDetailCallback() {
            @Override
            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                if (null == didiOrderDetailBean) {
                    ToastUtils.showToast(R.string.me_exception_order);
                } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
                    ToastUtils.showToast(R.string.me_exception_order);
                } else {
                    switch (didiOrderDetailBean.order.orderStatus) {
                        case DidiUtils.STATE_ONGOING: // 同状态
                            if ("1".equals(didiOrderDetailBean.order.isComplainted) || mOmplaintsSuccess) {
                                tv_complain.setText(R.string.me_didi_title_order_already_omplaints);
                            }
                            break;
                        default:
                            String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
                            if (TextUtils.isEmpty(className)) {
                                ToastUtils.showToast(R.string.me_exception_order_state);
                            } else {
                                Bundle bundle = new Bundle();
                                bundle.putSerializable("orderDetailBean", didiOrderDetailBean);
                                try {
                                    FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content, false, bundle, false);
                                } catch (ClassNotFoundException e) {
                                    e.printStackTrace();
                                }
                            }
                            break;
                    }
                }
            }
        });
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mCprvDriving.stop();
        AppBase.iAppBase.unregisterListener(MESSAGE_TYPE_CAR_POOL_COLLEAGUE, this);
    }

    @Override
    public void onMessageReceiver(String type, String message) {
        if (TextUtils.isEmpty(message)) return;
        if (MESSAGE_TYPE_CAR_POOL_COLLEAGUE.equals(type)) {
            try {
                JSONObject object = new JSONObject(message);
                String dataStr = object.getString("data");
                JSONObject data = new JSONObject(dataStr);
                String msg = data.optString("carPoolMsg");
                String colleagueStr = data.getString("carPoolInfo");
                List<DidiOrderDetailBean.ColleagueInfo> colleagues = new Gson().fromJson(colleagueStr, new TypeToken<List<DidiOrderDetailBean.ColleagueInfo>>() {
                }.getType());
                mTvMsg.setVisibility(TextUtils.isEmpty(msg)? View.INVISIBLE : View.VISIBLE);
                mTvMsg.setText(msg);
                showColleagues(colleagues);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private void showColleagues(List<DidiOrderDetailBean.ColleagueInfo> colleagues) {
        layout_colleague_container.removeAllViews();
        if (CollectionUtil.isEmptyOrNull(colleagues)) {
            layout_colleague_list.setVisibility(View.GONE);
        } else {
            for (int i = 0; i < colleagues.size(); i++) {
                ImageView imageView = new ImageView(getContext());
                imageView.setImageResource(R.drawable.jdme_ic_car_pool_colleague_default_icon_small);
                int padding = DensityUtil.dp2px(getContext(), 8);
                imageView.setPadding(padding, padding, padding, padding);
                imageView.setTag(colleagues.get(i));
                imageView.setOnClickListener(mOnClickListener);
                layout_colleague_container.addView(imageView);
            }
            layout_colleague_list.setVisibility(View.VISIBLE);
        }
    }

    public int coverTimerToSecond(String timer) {
        int second = 0;
        try {
            if (!TextUtils.isEmpty(timer) && timer.indexOf(":") > 0) {
                String[] tmp = timer.split(":");
                second = StringUtils.convertToInt(tmp[0]) * 60 + StringUtils.convertToInt(tmp[1]);
            }
        } catch (Exception e) {
            Logger.e(MELogUtil.TAG_TAX, "coverTimerToSecond timer exception");
        }
        return second;
    }

//    @Override
//    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        if (requestCode == DidiUtils.REQUEST_CODE_PERMISSION_LOCATION) {
//            //申请权限成功失败都会请求
//            DidiUtils.urgentHelp(getContext(), mDidiOrderDetailBean.order.orderId);
//        }
//    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OPERATE_GO_DIDI == optionFlag) {
            if (args != null) {
                String isAgain = args.getString("isAgain");
                if ("0".equals(isAgain)) {
                    tv_complain.setText(R.string.me_didi_title_order_already_omplaints);
                    mDidiOrderDetailBean.order.isComplainted = isAgain;
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        if ("03".equals(mDidiOrderDetailBean.order.serviceType)) {//嘀嗒展示取消
            inflater.inflate(R.menu.jdme_menu_cancel_dida, menu);
        }
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        //嘀嗒取消按钮
        if (item.getItemId() == R.id.action_cancel) {
            if ("03".equals(mDidiOrderDetailBean.order.serviceType)) {//嘀嗒展示取消
                new IosAlertDialog(getActivity()).builder().setMsg(getString(R.string.me_are_you_cancel_car_dida))
                        .setNegativeButton(getString(R.string.me_car_goahead_dida), new View.OnClickListener() {

                            @Override
                            public void onClick(View v) {

                            }
                        })
                        .setPositiveButton(getString(R.string.me_car_cancel_dida), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                DidiUtils.cancelCallTaxi(getActivity(), mDidiOrderDetailBean);
                            }
                        })
                        .show();
            }
            return true;
        }
        return super.onOptionsItemSelected(item);
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == 300 && getActivity() != null) {
            getActivity().setResult(300);
            getActivity().finish();
        }
        super.onActivityResult(requestCode, resultCode, data);
    }
}