package com.jd.oa.business.didi;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.adapter.DidiOrderListLoadMoreAdapter;
import com.jd.oa.business.didi.model.DidiOrderBean;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.model.DidiOrderListBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.ThemeUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.LinkedList;

/**
 * 滴滴所有订单页
 *
 * <AUTHOR>
 */
@Route(DeepLink.CAR_ORDER)
@Navigation(hidden = false, displayHome = true)
public class DidiAllOrderListFragment extends BaseFragment {

    private DidiOrderListLoadMoreAdapter mRecyclerAdapter;
    private LinkedList<DidiOrderBean> mOrderList = new LinkedList<>();

    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSwipeRefreshLayout;
    private FrameView mFrameView;

    private RecyclerViewOnLoadMoreListener loadMoreListener = null;

    private int mCurrentPage = 1;
    // 加载失败的 runnable
    private final Runnable run = new Runnable() {
        @Override
        public void run() {
            didiGetOrderList(1);
        }
    };


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View mRootView = inflater.inflate(R.layout.jdme_fragment_didi_all_order_list,
                container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_all_order_page_title);

        mRecyclerView = mRootView.findViewById(R.id.recycler_view_didi_all_order_list);
        mSwipeRefreshLayout = mRootView.findViewById(R.id.swipe_refresh_layout_didi_all_order);
        mFrameView = mRootView.findViewById(R.id.fv_view);

        mRecyclerAdapter = new DidiOrderListLoadMoreAdapter(getActivity(), mOrderList);
        mRecyclerView.setAdapter(mRecyclerAdapter);
        mRecyclerAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<DidiOrderBean>() {
            @Override
            public void onItemClick(View view, int position, DidiOrderBean item) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                getOrderDetail(item.getOrderId(), item.phoneNumber);
            }

            @Override
            public void onItemLongClick(View view, int position, DidiOrderBean item) {
                //Do nothing
            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));
        mSwipeRefreshLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));

        // 设置上拉加载
        loadMoreListener = new RecyclerViewOnLoadMoreListener(mSwipeRefreshLayout, mRecyclerAdapter) {
            @Override
            public void onLoadMore() {
                didiGetOrderList(mCurrentPage);
            }
        };
        mRecyclerView.addOnScrollListener(loadMoreListener);
        didiGetOrderList(1);
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                didiGetOrderList(1);
                mCurrentPage = 1;
                loadMoreListener.reset();
            }
        });

        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    private void didiGetOrderList(final int page) {
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(getActivity(), false, true) {
            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                mSwipeRefreshLayout.setRefreshing(false);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mSwipeRefreshLayout.setRefreshing(false);
                mFrameView.setRepeatRunnable(run, null);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                mSwipeRefreshLayout.setRefreshing(false);
                loadMoreListener.setLoaded();
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            DidiOrderListBean bean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiOrderListBean.class);
                            if (page == 1 && bean.orderList.size() <= 0 && mRecyclerAdapter.getItemCount() <= 1) {
                                showEmpty();
                            } else {
                                mCurrentPage++;
                                mFrameView.setContainerShown(true);
                            }
                            // 是否是第一页 初始化数据
                            if (page == 1) {
                                mSwipeRefreshLayout.setRefreshing(false);
                                mOrderList.clear();
                            }
                            // Adapter数据部分刷新
                            mRecyclerAdapter.addItemsAtLast(bean.orderList);

                            // 设置数据全部加载完毕
                            if (bean.orderList.size() < 20) {
                                loadMoreListener.loadAllData(true);
                            }

                        } catch (Exception e) {
                            Logger.e(MELogUtil.TAG_TAX, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getOrderList(this, callback, page + "", "20");
    }

    private void showEmpty() {
        mFrameView.setEmptyInfo(R.string.me_no_order);
        mFrameView.setEmptyShown(true);
    }

    private void getOrderDetail(String mOrderId, String mPhoneNumber) {
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(getActivity(), true, false) { // 获取职场地址

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiOrderDetailBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiOrderDetailBean.class);
                        DidiUtils.processResult(getActivity(), didiAddressBean);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_didi_exception_order_state);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getOrderDetail(null, callback, mOrderId, mPhoneNumber);
    }
}
