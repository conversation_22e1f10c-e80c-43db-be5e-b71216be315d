package com.jd.oa.business.didi;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.Toast;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.FragmentUtils;


/**
 * 用车协议界面
 *
 * <AUTHOR>
 */
// title = R.string.me_didi_protocol_page_title
@Navigation(hidden = false, displayHome = true)
public class DidiDescriptionFragment extends BaseFragment {
    private static final String TAG = "DidiDescriptionFragment";
    public static final String ARG_AGREEMENT_TYPE = "arg.agreement.type";
    public static final String ARG_AGREEMENT_URL = "arg.agreement.url";

    private LayoutInflater mInflater;

    private WebView webview_didi_condition;
    private androidx.appcompat.widget.AppCompatCheckBox checkbox_didi_condition;
    private Button btn_didi_entrance;

    private String mUrl;
    private String mType;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        this.mInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_fragment_didi_condition, container, false);
//        ActionBarHelper.init(this, view);

        initView(view);

        if (getArguments() != null) {
            mUrl = getArguments().getString(ARG_AGREEMENT_URL);
            mType = getArguments().getString(ARG_AGREEMENT_TYPE);
        }
        mUrl = NetworkConstant.PARAM_SERVER_OUTTER + (TextUtils.isEmpty(mUrl) ? Constant.API_DIDI_PROTOCPOL : mUrl);

        webview_didi_condition.getSettings().setAllowFileAccessFromFileURLs(false);
        webview_didi_condition.getSettings().setAllowUniversalAccessFromFileURLs(false);
        webview_didi_condition.setWebViewClient(new WebViewClient() {
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                //  重写此方法表明点击网页里面的链接还是在当前的webview里跳转，不跳到浏览器那边
                view.loadUrl(url);
                return true;
            }
        });

        webview_didi_condition.loadUrl(mUrl);
        checkbox_didi_condition.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    btn_didi_entrance.setEnabled(true);
                } else {
                    btn_didi_entrance.setEnabled(false);
                }
            }
        });

        return view;
    }

    private void initView(View view) {

        webview_didi_condition = view.findViewById(R.id.webview_didi_condition);

        checkbox_didi_condition = view.findViewById(R.id.checkbox_didi_condition);

        btn_didi_entrance = view.findViewById(R.id.btn_didi_entrance);

        btn_didi_entrance.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                signAgreement("1"); // 1代表同意 传0后端没有处理
            }
        });
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

    }

    private void signAgreement(String isAgree) {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(DidiDescriptionFragment.this.getActivity(), false, true) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                if (getActivity() == null) {
                    return;
                }
                super.onSuccess(info);
                final String json = info.result;
                ApiResponse response = ApiResponse.parse(json, Object.class);
                if (response.isSuccessful()) {
                    FragmentUtils.replaceWithCommit(getActivity(), DidiMainFragment.class, R.id.me_fragment_content, false, null, true);
                } else {
                    Toast.makeText(getContext(), response.getErrorMessage(), Toast.LENGTH_SHORT).show();
                }
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.signAgreement(this, callback, isAgree, mType);
    }
}