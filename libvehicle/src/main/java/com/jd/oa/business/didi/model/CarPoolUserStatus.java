package com.jd.oa.business.didi.model;

import androidx.annotation.Keep;

import java.io.Serializable;

/**
 * Created by peidongbiao on 2019/4/2
 */
@Keep
public class CarPoolUserStatus implements Serializable {
    private String userName;
    private String realName;
    private String flowFlag;
    private String avatar;
    private boolean isCallCarUser;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getFlowFlag() {
        return flowFlag;
    }

    public void setFlowFlag(String flowFlag) {
        this.flowFlag = flowFlag;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public boolean isCallCarUser() {
        return isCallCarUser;
    }

    public void setCallCarUser(boolean callCarUser) {
        isCallCarUser = callCarUser;
    }
}