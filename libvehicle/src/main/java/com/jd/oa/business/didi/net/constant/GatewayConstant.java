package com.jd.oa.business.didi.net.constant;

public class GatewayConstant extends Constant {

    public GatewayConstant(){
        init();
    }

    private void init() {
        //提交费用异议
        API_ME_VEHICLE_SUBMITFEEOBJECTION = "vehicle.outer.submitFeeObjection";
        //用车急救
        API_CAR_CALL_HELP = "vehicle.outer.callHelp";
        // 协议状态
        API_GETAGREEMENTSTATUS = "vehicle.outer.getAgreementStatus";
        // 用车协议
        API_PLUSAGREEMENTSTATUS = "vehicle.outer.getVehiclePlusAgreementStatus";
//        API_PLUSAGREEMENTSTATUS = "vehicle.outer.getVehPlusAgrStatus";
        // 签署协议
        API_SIGNAGREEMENT = "vehicle.outer.signAgreement";

        API_CHECKCHOSECARTYPE = "vehicle.outer.checkChoseCarType";

        API_GETJOBADDRESSBYLOCAL = "vehicle.outer.getJobAddressByLocal";

        API_GETADDRESSBYINPUT = "vehicle.outer.getAddressByInput";

//        API_GETESTIMATEPRICE = "vehicle.outer.getEstimatePrice";
        API_GETESTIMATEPRICE = "vehicle.outer.getEstimatePriceList";

        API_CHECKCARPOOLUSER = "vehicle.outer.checkCarPoolUser";

        API_CALLCARORDER = "vehicle.outer.callCarOrder";

        API_PRECALLCARORDERFORPOOL = "vehicle.outer.preCallCarOrderForPool";

        API_AFTERCALLCARORDERFORPOOL = "vehicle.outer.afterCallCarOrderForPool";

        API_GETCARPOOLORDERSTATUS = "vehicle.outer.getCarPoolOrderStatus";

        API_GETORDERLIST = "vehicle.outer.getOrderList";
        //  重叫
        API_RECALLCARORDER = "vehicle.outer.reCallCarOrder";
        // 获取订单详情
        API_GETORDERDETAIL = "vehicle.outer.getOrderDetail";
        // 取消订单
        API_CANCELORDER = "vehicle.outer.cancelOrder";

        API_GETCARPOOLUSERSTATUS = "vehicle.outer.getCarPoolUserStatus";

        API_CONFIRMFORORDER = "vehicle.outer.confirmForOrder";
        // 检查城市是否可用
        API_CHECKCITY = "vehicle.outer.checkCity";
        // 确认行程
        API_AGREEPAY = "vehicle.outer.agreePay";
        // 带有异常原因的确认行程
        API_AGREECONFIRMPAY = "vehicle.outer.agreeConfirmPay";
        //取消原因列表
        API_GETCANCELORDERREASONLIST = "vehicle.outer.getCancelOrderReasonList";
        //保存取消原因
        API_SAVECANCELORDERREASON = "vehicle.outer.saveCancelOrderReason";
        // 投诉选项列表
        API_GETCOMPLAINTREASONLIST = "vehicle.outer.getComplaintReasonList";
        // 提交投诉
        API_SUBMITCOMPLAINT = "vehicle.outer.submitComplaint";
        // 评价标签
        API_GETCOMMENTLABEL = "vehicle.outer.getCommentLabel";
        // 提交评价
        API_SUBMITCOMMENT = "vehicle.outer.submitComment";
        // 延缓支付
        ACTION_COMMIT_FEE = "vehicle.outer.dissentPay";

        API_GETADDRESSBYCURLOCATION = "vehicle.outer.getAddressByCurLocation";

        /**
         * 常用地址
         */
        API_GET_USER_ADDRESS_LIST = "vehicle.outer.getUserAddressList";
        API_ADD_USER_ADDRESS = "vehicle.outer.addUserAddress";
//        API_MODIFY_USER_ADDRESS = "vehicle.outer.modifyUserAddress";
        API_CHECK_CITY = "vehicle.outer.checkCity";
        API_CHECK_USER_COMMOM_ADDRESS = "vehicle.outer.checkUserCommonAddress";
        API_GETORDERLIST = "vehicle.outer.getOrderList";
    }

}
