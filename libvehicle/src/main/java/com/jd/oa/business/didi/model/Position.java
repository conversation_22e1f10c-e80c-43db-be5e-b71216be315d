package com.jd.oa.business.didi.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by ch<PERSON>qizheng on 2018/4/9.
 */

public class Position implements Parcelable {
    private String locateInfo;
    private String locateDetail;
    private String lng;
    private String lat;
    private String rangeCode;

    public String getLocateInfo() {
        return locateInfo;
    }

    public void setLocateInfo(String locateInfo) {
        this.locateInfo = locateInfo;
    }

    public String getLocateDetail() {
        return locateDetail;
    }

    public void setLocateDetail(String locateDetail) {
        this.locateDetail = locateDetail;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getRangeCode() {
        return rangeCode;
    }

    public void setRangeCode(String rangeCode) {
        this.rangeCode = rangeCode;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.locateInfo);
        dest.writeString(this.locateDetail);
        dest.writeString(this.lng);
        dest.writeString(this.lat);
        dest.writeString(this.rangeCode);
    }

    public Position() {
    }

    protected Position(Parcel in) {
        this.locateInfo = in.readString();
        this.locateDetail = in.readString();
        this.lng = in.readString();
        this.lat = in.readString();
        this.rangeCode = in.readString();
    }

    public static final Parcelable.Creator<Position> CREATOR = new Parcelable.Creator<Position>() {
        @Override
        public Position createFromParcel(Parcel source) {
            return new Position(source);
        }

        @Override
        public Position[] newArray(int size) {
            return new Position[size];
        }
    };
}
