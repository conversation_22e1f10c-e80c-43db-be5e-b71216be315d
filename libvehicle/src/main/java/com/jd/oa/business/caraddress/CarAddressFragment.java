package com.jd.oa.business.caraddress;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.business.caraddress.bean.CarAddressListWrapper;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import static com.jd.oa.business.caraddress.CarAddressModifyActivity.EXTRA_KEY_MODE;
import static com.jd.oa.business.caraddress.CarAddressModifyActivity.EXTRA_KEY_ORDER;

public class CarAddressFragment extends BaseFragment implements CarAddressContract.ICarAddressListView {
    public static final int MAX_ADDRESS_SIZE = 2;
    public static final int REQUEST_CODE_ADD = 100;
    private TextView mBtnAdd;
    private TextView mDescView;
    private RecyclerView mAddressRecyclerView;
    private CarAddressListAdapter mAddressAdapter;
    private CarAddressListPresenter mPresenter;
    private List<CarAddress> mCarAddressList;
    private int mResult = Activity.RESULT_CANCELED;

    public static CarAddressFragment newInstance() {
        CarAddressFragment fragment = new CarAddressFragment();
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View mRootView = inflater.inflate(R.layout.jdme_fragment_car_address,
                container, false);
        initView(mRootView);
        return mRootView;
    }

    private void initView(View mRootView) {
        mCarAddressList = new ArrayList<>();
        mPresenter = new CarAddressListPresenter(this);
        mBtnAdd = mRootView.findViewById(R.id.tv_address_add);
        mAddressRecyclerView = mRootView.findViewById(R.id.rv_address_list);
        mDescView = mRootView.findViewById(R.id.tv_address_desc);
        mAddressRecyclerView.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));
        mAddressAdapter = new CarAddressListAdapter(new CarAddressListAdapter.Action() {
            @Override
            public void onEditClick(CarAddress carAddress) {
                Intent intent = new Intent(getActivity(), CarAddressModifyActivity.class);
                intent.putExtra(EXTRA_KEY_MODE, CarAddressModifyActivity.EXTRE_MODE_MODIFY);
                intent.putExtra(CarAddressModifyActivity.EXTRA_KEY_ORDER, carAddress.getAddressOrder());
                intent.putExtra(CarAddressModifyActivity.EXTRA_KEY_ADDRESS_ID, carAddress.getId());
                startActivityForResult(intent, CarAddressFragment.REQUEST_CODE_ADD);
            }
        });
        mAddressRecyclerView.setAdapter(mAddressAdapter);
        mBtnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), CarAddressModifyActivity.class);
                intent.putExtra(EXTRA_KEY_ORDER, String.valueOf(mCarAddressList.size() + 1));
                startActivityForResult(intent, REQUEST_CODE_ADD);
            }
        });
        mPresenter.loadCarAddress();
    }

    @Override
    public void showCarAddressList(CarAddressListWrapper carAddressListWrapper) {
        PromptUtils.removeLoadDialog(getActivity());
        List<CarAddress> list = carAddressListWrapper.getAddressList();
        mCarAddressList.clear();
        mCarAddressList.addAll(list);
        if (list.size() >= MAX_ADDRESS_SIZE) {
            mBtnAdd.setVisibility(View.GONE);
//            mDescView.setText(R.string.me_car_address_tip_added);
        } else {
            mBtnAdd.setVisibility(View.VISIBLE);
//            mDescView.setText(R.string.me_car_address_tip_can_add);
        }
        List<String> addressTextList = carAddressListWrapper.getAddressText();
        if (addressTextList != null){
            StringBuilder sb = new StringBuilder();
            for (String s:addressTextList){
                sb.append(sb.length()>0?"\n"+s:s);
            }
            mDescView.setText(sb);
        }
        mBtnAdd.setText(getString(R.string.me_car_address_button_add));
        mAddressAdapter.setCarAddressList(mCarAddressList);
        mAddressRecyclerView.setAdapter(mAddressAdapter);
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_loading_message_not_translate));
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(s);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_ADD) {
            if (resultCode == Activity.RESULT_OK && getActivity() != null) {
                mPresenter.loadCarAddress();
                mResult = Activity.RESULT_OK;
                getActivity().setResult(mResult);
            }
        }
    }
}
