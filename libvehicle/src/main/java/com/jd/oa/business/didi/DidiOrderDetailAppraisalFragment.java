package com.jd.oa.business.didi;

import static com.jd.oa.business.didi.DidiOrderWarningFragment.APPLY_AGAIN_RESULT_CODE;
import static com.jd.oa.business.didi.DidiOrderWarningFragment.FROM_FLAG_APPLY_AGAIN;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.dialog.ColleagueListDialog;
import com.jd.oa.business.didi.model.DidiCommentLableBean;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.widget.OmplaintsPopwindow;
import com.jd.oa.feedback.widget.FlowRadioGroup;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评价页面
 * Created by qudongshi on 2016/5/20.
 */
@Navigation(displayHome = true, title = -1)
public class DidiOrderDetailAppraisalFragment extends DidiBaseFragment implements
        FragmentOperatingListener {

    LinearLayout mLlPriceContiner;
    String mPrice; // 费用
    String mOrderTimeRange; // 时间区间
    private View mRootView;
    private TextView mTvPrice;  // 价格
    private TextView mTvTimeRange;
    private TextView mTvApproval;
    // 司机信息
    private RelativeLayout mRlDriverInfo;
    private CircleImageView mIvDriverPic;
    private TextView mTvDriverName;
    private TextView mTvCarType;
    private TextView mTvCarNo;
    private DidiOrderDetailBean mDidiOrderDetailBean;

    private RatingBar mRbStar;
    private TextView mTvFraction;
    private TextView mTvOrder;

    private LinearLayout mLlBtnGroup;
    private ImageButton mIbtnOmplaints; // 投诉

    private TextView mTvQueryDetail; // 查看明细
    private LinearLayout mLlPriceGroup; // 价格组
    private LinearLayout mLlPriceDetail;
    private RatingBar mRabYellowStar;
    private LinearLayout mLlContent;
    // 评价标签
    private LinearLayout mLlStarContent;
    private TextView mTvContentLabel;
    private FlowRadioGroup mFrgLabel;
    private TextView mTvCount;
    private TextView mTvReimbursement;
    private EditText mEtMsg;
    private ImageView mIvTelephone;
    private Button btnShowColleague;

    private Button mBtnSubmit;

    private boolean isToReimbursement = false;

    // 评价相关内容

    // 价格明细显示标记
    private boolean mShowPriceDetailFlag = false;
    // 评价标签
    private DidiCommentLableBean didiCommentLableBean;

    private Map<String, String> mMapCheckedVal;

    private String mContent = "";

    private boolean mOmplaintsSuccess = false;
    private boolean isSuccess = false;

    private int mMaxLenght = 40;
    private ImageView mIvService;
    private LinearLayout mLlConfirm;
    private Button mTvConfirm;
    private Button mTvApplyAgain;
    private Button btn_view_detail;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_appraisal, container, false);

        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_appraisal);

        mDidiOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        initView();
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    private void initView() {
        mLlPriceContiner = mRootView.findViewById(R.id.ll_price_container);
        mTvReimbursement = mRootView.findViewById(R.id.tv_reimbursement);//一键还款
        mLlConfirm = mRootView.findViewById(R.id.ll_confirm);
        mTvConfirm = mRootView.findViewById(R.id.tv_reimbursement_confirm);
        mTvApplyAgain = mRootView.findViewById(R.id.tv_apply_again);
        mTvPrice = mRootView.findViewById(R.id.tv_price);  // 价格
        mTvTimeRange = mRootView.findViewById(R.id.tv_order_time_range);
        mTvApproval = mRootView.findViewById(R.id.tv_tip_approval);
        // 司机信息
        mRlDriverInfo = mRootView.findViewById(R.id.ll_driver_info);
        mIvDriverPic = mRootView.findViewById(R.id.iv_driver_pic);
        mTvDriverName = mRootView.findViewById(R.id.tv_driver_name);
        mTvCarType = mRootView.findViewById(R.id.tv_car_type);
        mTvCarNo = mRootView.findViewById(R.id.tv_car_no);
        mIvService = mRootView.findViewById(R.id.iv_icon);

        mRbStar = mRootView.findViewById(R.id.rab_star);
        mTvFraction = mRootView.findViewById(R.id.tv_fraction);
        mTvOrder = mRootView.findViewById(R.id.tv_order);

        mLlBtnGroup = mRootView.findViewById(R.id.ll_btn_group);
        mIbtnOmplaints = mRootView.findViewById(R.id.ibtn_omplaints); // 投诉

        mTvQueryDetail = mRootView.findViewById(R.id.tv_query_detail); // 查看明细
        mLlPriceGroup = mRootView.findViewById(R.id.ll_price_group); // 价格组
        mLlPriceDetail = mRootView.findViewById(R.id.ll_price_detail);
        mRabYellowStar = mRootView.findViewById(R.id.rab_yellow_star);
        mLlContent = mRootView.findViewById(R.id.ll_content);

        // 评价标签
        mLlStarContent = mRootView.findViewById(R.id.ll_star_content);
        mTvContentLabel = mRootView.findViewById(R.id.tv_content_label);
        mFrgLabel = mRootView.findViewById(R.id.rg_content_option);
        mTvCount = mRootView.findViewById(R.id.tv_count);
        mEtMsg = mRootView.findViewById(R.id.et_msg);
        mIvTelephone = mRootView.findViewById(R.id.iv_telephone);

        mBtnSubmit = mRootView.findViewById(R.id.btn_submit);
        btnShowColleague = mRootView.findViewById(R.id.btn_show_colleague);
        btn_view_detail = mRootView.findViewById(R.id.btn_view_detail);
        btn_view_detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (mDidiOrderDetailBean != null && !TextUtils.isEmpty(mDidiOrderDetailBean.h5url)) {
                    Router.build(mDidiOrderDetailBean.h5url).go(getActivity());
                }
            }
        });

        mMapCheckedVal = new HashMap<>();
        mTvDriverName.setText(mDidiOrderDetailBean.order.driverName);
        ImageLoader.load(requireActivity(), mIvService, mDidiOrderDetailBean.icon, false, R.drawable.jdme_icon_use_car_service_default, R.drawable.jdme_icon_use_car_service_default);
        if (TextUtils.isEmpty(mDidiOrderDetailBean.order.driverName) && TextUtils.isEmpty(mDidiOrderDetailBean.order.driverPhone)) {
            mRlDriverInfo.setVisibility(View.GONE);
        } else {
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.driverLevel)) {
                try {
                    DidiUtils.showDriverLevel(Float.parseFloat(mDidiOrderDetailBean.order.driverLevel), mRbStar);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                mTvFraction.setText(mDidiOrderDetailBean.order.driverLevel + mTvFraction.getContext().getString(R.string.me_didi_minute));
                mTvOrder.setText(mDidiOrderDetailBean.order.driverOrderCount + mTvFraction.getContext().getString(R.string.me_didi_unit_dan));
            } else {
                mLlStarContent.setVisibility(View.GONE);
            }
        }
        mTvCarNo.setText(mDidiOrderDetailBean.order.driverCard);
        mTvCarType.setText(StringUtils.getSubStringFromStart(mDidiOrderDetailBean.order.driverCarType, 12));
        DidiUtils.getDriverImg(mDidiOrderDetailBean.order.driverAvatar, mIvDriverPic, getActivity());

        // 设置提示
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.showTip)) {
            mTvApproval.setVisibility(View.VISIBLE);
            mTvApproval.setText(mDidiOrderDetailBean.order.showTip);
        }
        if (mDidiOrderDetailBean != null && mDidiOrderDetailBean.order != null
                && mDidiOrderDetailBean.order.orderStatus.equals(DidiUtils.STATE_DEBT)) {
            if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.paymentDeepLink)) {
                mTvReimbursement.setVisibility(View.VISIBLE);
            } else {
                mLlConfirm.setVisibility(mDidiOrderDetailBean.order.isConfirm() ? View.GONE : View.VISIBLE);
            }
        } else {
            mTvReimbursement.setVisibility(View.GONE);
            mLlConfirm.setVisibility(View.GONE);
        }
        // 初始值
        mPrice = getResources().getString(R.string.me_didi_pay_price);
        mOrderTimeRange = getResources().getString(R.string.me_didi_order_time_range);

        // 赋值
        mTvPrice.setText(mDidiOrderDetailBean.price.totalPrice);  // 总价
        mTvTimeRange.setText(String.format(mOrderTimeRange, mDidiOrderDetailBean.order.beginChargeTime, mDidiOrderDetailBean.order.finishTime)); // 打车时间区间
        mLlPriceContiner.removeAllViews();
        for (DidiOrderDetailBean.PriceBean.model bean : mDidiOrderDetailBean.price.detail) {
            LinearLayout mTmp = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_price_info, null);
            TextView mTmpTitle = (TextView) mTmp.findViewById(R.id.tv_title);
            TextView mTmpPrice = (TextView) mTmp.findViewById(R.id.tv_price);
            mTmpTitle.setText(bean.name);
            mTmpPrice.setText(mPrice.replace("--", bean.amount));
            mLlPriceContiner.addView(mTmp);
        }

//        if ("1".equals(mDidiOrderDetailBean.order.isComplainted) || mOmplaintsSuccess) // 隐藏投诉按钮
//            mIbtnOmplaints.setBackgroundResource(R.drawable.jdme_didi_icon_complaints_disable);

        getCommonLabel();
        mRabYellowStar.setOnRatingBarChangeListener(new RatingBar.OnRatingBarChangeListener() {
            @Override
            public void onRatingChanged(RatingBar ratingBar, float rating, boolean fromUser) {
                mMapCheckedVal.clear();
                changeContentLenght();
                mContent = "";
                if (rating > 0) {
                    mLlContent.setVisibility(View.VISIBLE);
                    mLlPriceGroup.setVisibility(View.GONE);
                    if (null == didiCommentLableBean)
                        return;
                    // 动态加载值
                    if (null == didiCommentLableBean.labelList || didiCommentLableBean.labelList.length == 0)
                        return;
                    mTvContentLabel.setText(didiCommentLableBean.labelList[(int) rating - 1].text);
                    mFrgLabel.removeAllViews();
                    int index = 0;
                    for (String label : didiCommentLableBean.labelList[(int) rating - 1].labelText) {
                        CheckBox mTmp = (CheckBox) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_label, null);
                        mTmp.setText(label);
                        mTmp.setId(++index);
                        mTmp.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                            @Override
                            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                                if (isChecked)
                                    mMapCheckedVal.put(buttonView.getId() + "", buttonView.getText().toString());
                                else
                                    mMapCheckedVal.remove(buttonView.getId() + "");
                                changeContentLenght();
                            }
                        });
                        mFrgLabel.addView(mTmp);
                    }
                } else {
                    mLlPriceGroup.setVisibility(View.VISIBLE);
                    mLlContent.setVisibility(View.GONE);
                }
            }
        });

        mEtMsg.addTextChangedListener(new TextWatcher() { // 信息修改后更改提字数
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                changeContentLenght();
            }
        });

        if (!DidiUtils.isCallCarUser(mDidiOrderDetailBean)) {
            mIvTelephone.setVisibility(View.GONE);
        }

        mIvTelephone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mDidiOrderDetailBean.order.driverPhone);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    }, Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.driverPhone);
                }
            }
        });

        mBtnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                submitComment();
            }
        });

        mTvReimbursement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(mDidiOrderDetailBean.order.paymentDeepLink)) {
                    Router.build(mDidiOrderDetailBean.order.paymentDeepLink).go(requireActivity());
                    JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_repayment,JDMAConstants.mobile_employeeTravel_traveDetail_repayment);
                    isToReimbursement = true;
                }

            }
        });
        mTvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() == null) {
                    return;
                }
                DidiUtils.orderUserValidation(getActivity(), mDidiOrderDetailBean.order.orderId, new DidiUtils.ICommonCallback() {
                    @Override
                    public void onSuccess() {
                        DidiUtils.getOrderDetail(getActivity(), mDidiOrderDetailBean.order.orderId, "", new DidiUtils.IDetailCallback() {
                            @Override
                            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                                mDidiOrderDetailBean = didiOrderDetailBean;
                                initView();
                            }
                        });
                    }

                    @Override
                    public void onFailure() {
                    }
                });
            }
        });
        mTvApplyAgain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Intent intent = new Intent(getActivity(), FunctionActivity.class);
//                intent.putExtra("function", DidiOrderDetailApplySubmitFragment.class.getName());
//                intent.putExtra("orderId", mDidiOrderDetailBean.order.orderId);
//                startActivityForResult(intent, APPLY_AGAIN_RESULT_CODE);

                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DidiOrderWarningFragment.class.getName());
                intent.putExtra("orderDetailBean", mDidiOrderDetailBean);
                intent.putExtra("from", FROM_FLAG_APPLY_AGAIN);
                startActivityForResult(intent, APPLY_AGAIN_RESULT_CODE);
                JDMAUtils.clickEvent("", JDMAConstants.mobile_employeeTravel_traveDetail_application, null);
            }
        });
        mIbtnOmplaints.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if ("1".equals(mDidiOrderDetailBean.order.isComplainted) || mOmplaintsSuccess) {
                    OmplaintsPopwindow mPopupWindow = new OmplaintsPopwindow(getActivity(), mDidiOrderDetailBean.order.servicePhone, new OmplaintsPopwindow.ITelCallback() {
                        @Override
                        public void onTelCallback() {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                                PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        DidiUtils.callDriver(requireActivity(), mDidiOrderDetailBean.order.servicePhone.replaceAll("-", ""));
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {

                                    }
                                }, Manifest.permission.CALL_PHONE);
                            } else {
                                DidiUtils.callDriver(getActivity(), mDidiOrderDetailBean.order.servicePhone.replaceAll("-", ""));
                            }
                        }
                    });
                    mPopupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
                    mPopupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                        @Override
                        public void onDismiss() {
                            backgroundAlpha(1f);
                        }
                    });

                    mPopupWindow.showAtLocation(mRootView, Gravity.BOTTOM, 0, 0);
                    backgroundAlpha(0.5f);
                } else {
                    Bundle bundle1 = new Bundle();
                    bundle1.putString("orderId", mDidiOrderDetailBean.order.orderId);
                    bundle1.putString("serviceType", mDidiOrderDetailBean.order.serviceType);
                    FragmentUtils.replaceWithCommit(getActivity(), DidiOrderDetailOmplaintsFragment.class, R.id.me_fragment_content, true, bundle1, false);
                }
            }
        });

        mTvQueryDetail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mShowPriceDetailFlag) {
                    mLlPriceDetail.setVisibility(View.GONE);
                    mTvQueryDetail.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_didi_icon_arrow_down), null);
                    mShowPriceDetailFlag = false;
                } else {
                    mLlPriceDetail.setVisibility(View.VISIBLE);
                    mTvQueryDetail.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_didi_icon_arrow_up), null);
                    mShowPriceDetailFlag = true;
                }
            }
        });

        //拼车订单显示按钮
        boolean showColleague = "1".equals(mDidiOrderDetailBean.isCarPool);
        btnShowColleague.setVisibility(showColleague ? View.VISIBLE : View.GONE);
        btnShowColleague.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mDidiOrderDetailBean == null || mDidiOrderDetailBean.callCarUser == null) {
                    return;
                }
                ColleagueListDialog dialog = new ColleagueListDialog(getContext(), mDidiOrderDetailBean);
                dialog.show();
            }
        });
        if (!TextUtils.isEmpty(mDidiOrderDetailBean.h5url)) {
            mIbtnOmplaints.setVisibility(View.GONE);
        }
        btn_view_detail.setVisibility(TextUtils.isEmpty(mDidiOrderDetailBean.h5url) ? View.GONE : View.VISIBLE);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == APPLY_AGAIN_RESULT_CODE) {
            if (getActivity() != null) {
                getActivity().finish();
            }
        }
    }

    /**
     * 获取评价标签
     */
    private void getCommonLabel() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        // 评价列表
                        didiCommentLableBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiCommentLableBean.class);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getCommentLabel(this, callback);
    }

    private void changeContentLenght() {
        String content = "";
        for (Map.Entry<String, String> entry : mMapCheckedVal.entrySet()) {
            content += entry.getValue();
        }
        mEtMsg.setFilters(new InputFilter[]{new InputFilter.LengthFilter(40 - content.length())});
        int lenght = 40 - content.length() - mEtMsg.getText().length();
        mTvCount.setText(lenght + "");
    }

    private void submitComment() {
        mContent = "";

//        if (mMapCheckedVal.size() == 0) {
//            return;
//        }
        if (mMapCheckedVal.size() > 0)
            for (Map.Entry<String, String> entry : mMapCheckedVal.entrySet()) {
                mContent += entry.getValue();
            }

        if (mRabYellowStar.getRating() == 0) {
            ToastUtils.showToast(R.string.me_choose_or_input_evaluate);
            return;
        }

        mContent += mEtMsg.getText().toString();

//        mFrgLabel.get
        String mLevel = (int) mRabYellowStar.getRating() + "";

        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        isSuccess = true;
                        getActivity().onBackPressed();
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        ToastUtils.showToast(R.string.me_didi_msg_submit_failed);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(R.string.me_didi_msg_submit_failed);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.submitComment(this, callback, mDidiOrderDetailBean.order.orderId, mLevel, mContent);
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getActivity().getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        getActivity().getWindow().setAttributes(lp);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (isSuccess) {
            Bundle bundle = new Bundle();
            bundle.putBoolean("isAppraisal", true);
            bundle.putBoolean("isSuccsess", true);
            FragmentUtils.removeAndNotifyPrev(getActivity(), this, bundle);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (isToReimbursement) {
            //还款页面回来刷一遍页面
            if (mDidiOrderDetailBean != null && mDidiOrderDetailBean.order != null
                    && !TextUtils.isEmpty(mDidiOrderDetailBean.order.orderId)
                    && mDidiOrderDetailBean.order.orderStatus.equals(DidiUtils.STATE_DEBT)
                    && !TextUtils.isEmpty(mDidiOrderDetailBean.order.paymentDeepLink)) {
                DidiUtils.getOrderDetail(requireActivity(), mDidiOrderDetailBean.order.orderId, "", new DidiUtils.IDetailCallback() {
                    @Override
                    public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
                        mDidiOrderDetailBean = didiOrderDetailBean;
                        initView();
                    }
                });
            }
        }
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
        if (bundle != null) {
            mOmplaintsSuccess = bundle.getBoolean("isSuccsess");
            getActivity().finish();
        }

    }
}
