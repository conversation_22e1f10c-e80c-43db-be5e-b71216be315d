package com.jd.oa.business.didi;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.adapter.TravelInviteAdapter;
import com.jd.oa.business.didi.dialog.CarPoolConfirmDialog;
import com.jd.oa.business.didi.model.CarPoolOrderDetail;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.workbench2.model.UserAvatarMap;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ToastUtils;


import org.json.JSONObject;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * Created by peidongbiao on 2019/3/20
 */
@Navigation(hidden = false,  displayHome = true)
public class DidiCarPoolWaitingFragment extends BaseFragment implements OperatingListener, TimlineMessageListener {
    private TextView mTvCountDown;
    private TextView mTvTips;
    private RecyclerView mRecycler;
    private Button mBtnCancel;
    private Button mBtnCall;

    private TravelInviteAdapter mAdapter;
    private DidiOrderDetailBean mOrderDetailBean;
    private String mOrderId;
    private CarPoolOrderDetail mCarPoolOrderDetail;
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();
    private Disposable mCountdownDisposable;
    private boolean mIsExpired;
    private long mPastTime;
    private boolean mCountdownPaused;
    private long mTimeDifference;
    private Gson gson;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_didi_car_pool_waiting, container, false);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_car_travel_colleague_invite);
        if (getArguments() == null || !getArguments().containsKey("orderDetailBean")) {
            getActivity().finish();
        }
        mOrderDetailBean = (DidiOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        mOrderId = mOrderDetailBean.order.orderId;
        gson = new Gson();
        findViews(view);
        initViews(view);
        AppBase.iAppBase.registerTimlineMessage(MESSAGE_TYPE_CAR_POOL, this);
        action();
        return view;
    }

    private void findViews(View view) {
        mTvCountDown = view.findViewById(R.id.tv_countdown);
        mTvTips = view.findViewById(R.id.tv_tips);
        mRecycler = view.findViewById(R.id.recycler);
        mBtnCancel = view.findViewById(R.id.btn_cancel);
        mBtnCall = view.findViewById(R.id.btn_call);
    }

    private void initViews(View view) {
        mAdapter = new TravelInviteAdapter(getContext());
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        mRecycler.setLayoutManager(layoutManager);
        mRecycler.setAdapter(mAdapter);
        mAdapter.setOnUserClickListener(new TravelInviteAdapter.OnUserClickListener() {
            @Override
            public void onUserClick(View view, int position) {
                CarPoolUserStatus user = mAdapter.getItem(position);
                AppBase.iAppBase.showChattingActivity(getActivity(), user.getUserName());
            }
        });

        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showCancelConfirmDialog();
            }
        });

        mBtnCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOrderDetailBean == null || mOrderDetailBean.order == null) return;
                if (mCarPoolOrderDetail == null) return;
                final CarPoolConfirmDialog dialog = new CarPoolConfirmDialog(getContext(), mOrderDetailBean, mCarPoolOrderDetail.getCarPoolUsers());
                dialog.setOnCancelClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.cancel();
                        cancel();
                    }
                });
                dialog.setOnConfirmClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.cancel();
                        call();
                    }
                });
                dialog.show();
            }
        });
    }

    private void action() {
        Disposable disposable = getCarPoolOrderStatus(true, null, null)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<ApiResponse<CarPoolOrderDetail>>() {
                    @Override
                    public void accept(ApiResponse<CarPoolOrderDetail> response) throws Exception {
                        if (!response.isSuccessful()) {
                            MELogUtil.localW(MELogUtil.TAG_TAX, "onSuccess: " + response.getErrorMessage());
                            return;
                        }
                        mCarPoolOrderDetail = response.getData();
                        mIsExpired = isExpired(mCarPoolOrderDetail);
                        mTimeDifference = getTimeDifference(mCarPoolOrderDetail);
                        boolean allUserResponsed = isAllUserResponsed(mCarPoolOrderDetail.getCarPoolUsers());
                        if (!mIsExpired && !allUserResponsed) {
                            //倒计时
                            countdown(mCarPoolOrderDetail);
                        } else {
                            if (!TextUtils.isEmpty(mCarPoolOrderDetail.getFinalTimestamp())
                                    && TextUtils.isDigitsOnly(mCarPoolOrderDetail.getFinalTimestamp())) {
                                long start = Long.parseLong(mCarPoolOrderDetail.getStartTimestamp());
                                long stopTime = Long.parseLong(mCarPoolOrderDetail.getFinalTimestamp());
                                long end = Long.parseLong(mCarPoolOrderDetail.getConfirmStopTimestamp());
                                stopTime = (stopTime < end)? stopTime : end;
                                final long duration = end - start;
                                mTvCountDown.setText(DidiUtils.secToTime((int) (duration - (stopTime - start))));
                            } else {
                                mTvCountDown.setText("00:00");
                            }
                            boolean containsApproved = containsStatusColleague(mCarPoolOrderDetail.getCarPoolUsers(), DidiUtils.CAR_POOL_STATE_APPROVED);
                            mTvTips.setText(containsApproved ? R.string.me_car_pool_colleague_confirm : R.string.me_car_pool_no_colleague_confirm);
                            mBtnCancel.setEnabled(true);
                            mBtnCall.setEnabled(true);
                            //刷新同行人状态
                            if (!isAllUserResponsed(mCarPoolOrderDetail.getCarPoolUsers())) {
                                refreshStatusWhenStop(null);
                            }
                        }
                        mAdapter.refresh(mCarPoolOrderDetail.getCarPoolUsers());
                        getAvatars(DidiUtils.getCarPoolUserIds(mCarPoolOrderDetail.getCarPoolUsers()));
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.d(TAG, "accept: ");
                    }
                });
        mCompositeDisposable.add(disposable);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            getActivity().finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mCompositeDisposable.dispose();
        AppBase.iAppBase.unregisterListener(MESSAGE_TYPE_CAR_POOL, this);
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (optionFlag == OperatingListener.OPERATE_BACK_PRESS) {

        }
        return false;
    }

    @Override
    public void onStart() {
        super.onStart();
        if (mCountdownPaused) {
            countdown(mCarPoolOrderDetail);
            mCountdownPaused = false;
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        stopCountdown();
    }

    /**
     * 倒计时
     *
     * @param orderDetail
     */
    private void countdown(CarPoolOrderDetail orderDetail) {
        if (orderDetail == null) return;
        long start = Long.parseLong(orderDetail.getStartTimestamp());
        //long current = Long.parseLong(orderDetail.getCurTimestamp());
        long current = (System.currentTimeMillis() / 1000) + mTimeDifference;
        long end = Long.parseLong(orderDetail.getConfirmStopTimestamp());
        final long remain = end > current ? (end - current) : 0;
        mTvCountDown.setText(DidiUtils.secToTime((int) remain));
        if (mCountdownDisposable != null) mCountdownDisposable.dispose();
        mCountdownDisposable = Observable.interval(0, 1, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                        Log.d(TAG, "countdown: " + aLong);
                        mPastTime = aLong;
                        int time = (int) (remain - aLong);
                        if (time <= 0) {
                            //倒计时结束
                            time = 0;
                            mCountdownDisposable.dispose();
                            mIsExpired = true;
                            mCountdownPaused = false;
                            //结束后再刷一次状态
                            refreshStatusWhenStop(null);
                        }
                        mTvCountDown.setText(DidiUtils.secToTime(time));
                    }
                });
        mCompositeDisposable.add(mCountdownDisposable);
    }

    private void stopCountdown() {
        if (mCountdownDisposable != null && !mCountdownDisposable.isDisposed()) {
            mCountdownDisposable.dispose();
            mCountdownPaused = true;
        }
    }

    /**
     * 保存刷新同行人状态
     */
    private void refreshStatusWhenStop(String finalTimestamp) {
        Disposable disposable = getCarPoolOrderStatus(false, getExpiredUserIds(mCarPoolOrderDetail.getCarPoolUsers()), finalTimestamp)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<ApiResponse<CarPoolOrderDetail>>() {
                    @Override
                    public void accept(ApiResponse<CarPoolOrderDetail> response) throws Exception {
                        if (!response.isSuccessful()) return;
                        mCarPoolOrderDetail = response.getData();
                        onStatusRefreshed(mCarPoolOrderDetail);
                        boolean allAccept = containsStatusColleague(mCarPoolOrderDetail.getCarPoolUsers(), DidiUtils.CAR_POOL_STATE_APPROVED);
                        mTvTips.setText(allAccept ? R.string.me_car_pool_colleague_confirm : R.string.me_car_pool_no_colleague_confirm);
                        mBtnCancel.setEnabled(true);
                        mBtnCall.setEnabled(true);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "accept: ", throwable);
                    }
                });
        mCompositeDisposable.add(disposable);
    }

    private Single<ApiResponse<CarPoolOrderDetail>> getCarPoolOrderStatus(final boolean showProgress, final String expireUserNames, final String finalTimestamp) {
        return Single.create(new SingleOnSubscribe<ApiResponse<CarPoolOrderDetail>>() {
            @Override
            public void subscribe(final SingleEmitter<ApiResponse<CarPoolOrderDetail>> e) throws Exception {
                NetWorkManager.getCarPoolOrderStatus(mOrderId, expireUserNames, finalTimestamp, new SimpleRequestCallback<String>(getContext(), showProgress) {

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<CarPoolOrderDetail> response = ApiResponse.parse(info.result, CarPoolOrderDetail.class);
                        e.onSuccess(response);
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        e.onError(exception);
                    }
                });
            }
        });
    }

    /**
     * 取消叫车
     */
    private void cancel() {
        if (mOrderDetailBean == null) return;
        NetWorkManager.cancelOrder(null, new SimpleRequestCallback<String>(getContext(), true) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (!isAlive()) return;
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                if (response.isSuccessful()) {
                    ToastUtils.showToast(R.string.me_cancel_ok);
                    getActivity().finish();
                } else {
                    ToastUtils.showToast(R.string.me_cancel_not_ok_not_translate);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if (!isAlive()) return;
            }

        }, mOrderDetailBean.order.orderId, mOrderDetailBean.order.phoneNumber, "1");
    }

    private void call() {
        if (mOrderDetailBean == null) return;
        //是否有同行人
        boolean containsColleague = containsStatusColleague(mCarPoolOrderDetail.getCarPoolUsers(), DidiUtils.CAR_POOL_STATE_APPROVED);
        NetWorkManager.afterCallCarOrderForPool(mOrderDetailBean.order.orderId, containsColleague, new SimpleRequestCallback<String>(getContext(), true, true) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (!isAlive()) return;
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                if (response.isSuccessful()) {
                    DidiUtils.getOrderDetailAndJump(getActivity(), mOrderDetailBean);
                } else {
                    ToastUtils.showToast(response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

    private void getAvatars(String ids) {
        com.jd.oa.network.NetWorkManager.getUserAvatars(ids, new LoadDataCallback<List<UserAvatarMap>>() {
            @Override
            public void onDataLoaded(List<UserAvatarMap> userAvatarMaps) {
                showAvatars(userAvatarMaps);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showAvatars(List<UserAvatarMap> userAvatarMaps) {
        if (mCarPoolOrderDetail == null || CollectionUtil.isEmptyOrNull(mCarPoolOrderDetail.getCarPoolUsers()))
            return;
        if (CollectionUtil.isEmptyOrNull(userAvatarMaps)) return;
        List<CarPoolUserStatus> list = mCarPoolOrderDetail.getCarPoolUsers();
        for (int i = 0; i < userAvatarMaps.size(); i++) {
            UserAvatarMap map = userAvatarMaps.get(i);
            for (int i1 = 0; i1 < list.size(); i1++) {
                CarPoolUserStatus status = list.get(i1);
                if (status.getUserName().equals(map.getUser())) {
                    status.setAvatar(map.getAvatar());
                }
            }
        }
        mAdapter.refresh(list);
    }

    private boolean isExpired(CarPoolOrderDetail orderDetail) {
        long start = Long.parseLong(orderDetail.getStartTimestamp());
        long current = Long.parseLong(orderDetail.getCurTimestamp());
        long end = Long.parseLong(orderDetail.getConfirmStopTimestamp());
        boolean isExpired = current > end;
        return isExpired;
    }

    private long getTimeDifference(CarPoolOrderDetail orderDetail) {
        if (orderDetail == null) return 0;
        long client = System.currentTimeMillis() / 1000;
        long server = Long.parseLong(orderDetail.getCurTimestamp());
        return server - client;
    }

    /**
     * 是否全部用户都响应
     *
     * @param statuses
     * @return
     */
    private boolean isAllUserResponsed(List<CarPoolUserStatus> statuses) {
        if (CollectionUtil.isEmptyOrNull(statuses)) return false;
        boolean allResponsed = true;
        for (int i = 0; i < statuses.size(); i++) {
            CarPoolUserStatus status = statuses.get(i);
            if (DidiUtils.CAR_POOL_STATE_CONFIRMING.equals(status.getFlowFlag())) {
                allResponsed = false;
            }
        }
        return allResponsed;
    }

    /**
     * 是否有确认同行的人
     *
     * @param statuses
     * @return
     */
    private boolean containsStatusColleague(List<CarPoolUserStatus> statuses, String status) {
        if (CollectionUtil.isEmptyOrNull(statuses)) return false;
        boolean result = false;
        for (int i = 0; i < statuses.size(); i++) {
            CarPoolUserStatus carPoolUserStatus = statuses.get(i);
            if (status.equals(carPoolUserStatus.getFlowFlag())) {
                result = true;
            }
        }
        return result;
    }

    private void onStatusRefreshed(CarPoolOrderDetail detail) {
        if (detail == null) return;
        List<CarPoolUserStatus> statuses = detail.getCarPoolUsers();
        for (int i = 0; i < mAdapter.getData().size(); i++) {
            CarPoolUserStatus user = mAdapter.getData().get(i);
            for (int j = 0; j < statuses.size(); j++) {
                CarPoolUserStatus userStatus = statuses.get(j);
                if (user.getUserName().equals(userStatus.getUserName())) {
                    user.setFlowFlag(userStatus.getFlowFlag());
                    break;
                }
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    private String getExpiredUserIds(List<CarPoolUserStatus> members) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < members.size(); i++) {
            CarPoolUserStatus member = members.get(i);
            if (DidiUtils.CAR_POOL_STATE_CONFIRMING.equals(member.getFlowFlag())) {
                stringBuilder.append(member.getUserName());
                if (i != members.size() - 1) {
                    stringBuilder.append(",");
                }
            }
        }
        return stringBuilder.toString();
    }

    private void showCancelConfirmDialog() {
        new IosAlertDialog(getContext()).builder()
                .setMsg(getString(R.string.me_car_pool_reject_tips))
                .setPositiveButton(getString(R.string.me_car_pool_confirm_cancel), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        cancel();
                    }
                })
                .setNegativeButton(getString(R.string.me_car_pool_continue_call), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                    }
                }).show();
    }

    @Override
    public void onMessageReceiver(String type, String message) {
        if (TextUtils.isEmpty(message)) {
            return;
        }
        try {
            String repsonseStr = new JSONObject(message).optString("carPoolOrderStatus");
            if(TextUtils.isEmpty(repsonseStr)) {
                return;
            }
            mCarPoolOrderDetail = gson.fromJson(repsonseStr, CarPoolOrderDetail.class);
            onStatusRefreshed(mCarPoolOrderDetail);
            if (isAllUserResponsed(mCarPoolOrderDetail.getCarPoolUsers())) {
                //停止倒计时
                mCountdownDisposable.dispose();
                boolean allAccept = containsStatusColleague(mCarPoolOrderDetail.getCarPoolUsers(), DidiUtils.CAR_POOL_STATE_APPROVED);
                mTvTips.setText(allAccept ? R.string.me_car_pool_colleague_confirm : R.string.me_car_pool_no_colleague_confirm);
                mBtnCancel.setEnabled(true);
                mBtnCall.setEnabled(true);
                //调用一次接口，保存停止时间
                refreshStatusWhenStop(mCarPoolOrderDetail.getFinalTimestamp());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}