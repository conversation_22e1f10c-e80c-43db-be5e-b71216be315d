package com.jd.oa.business.didi.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatDialog;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.CarPoolUserStatus;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;

/**
 * Created by peidongbiao on 2019/4/3
 */
public class CarPoolConfirmDialog extends AppCompatDialog {

    private TextView mTvFrom;
    private TextView mTvTo;
    private TextView mTvColleague;
    private Button mBtnCancel;
    private Button mBtnCall;

    private View.OnClickListener mOnCancelClickListener;
    private View.OnClickListener mOnConfirmClickListener;
    private DidiOrderDetailBean mOrderDetailBean;
    private List<CarPoolUserStatus> mCarPoolUserStatuses;

    public CarPoolConfirmDialog(Context context, DidiOrderDetailBean detailBean, List<CarPoolUserStatus> list) {
        super(context);
        mOrderDetailBean = detailBean;
        mCarPoolUserStatuses = list;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        //不可取消
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        if(getWindow() != null){
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_rec_cornor_shape_dialog);
        }
        setContentView(R.layout.jdme_dialog_didi_car_pool_confirm);
        mTvFrom = findViewById(R.id.tv_from);
        mTvTo = findViewById(R.id.tv_to);
        mTvColleague = findViewById(R.id.tv_colleague);
        mBtnCancel = findViewById(R.id.btn_cancel);
        mBtnCall = findViewById(R.id.btn_call);

        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mOnCancelClickListener != null) {
                    mOnCancelClickListener.onClick(v);
                }
            }
        });

        mBtnCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mOnConfirmClickListener != null) {
                    mOnConfirmClickListener.onClick(v);
                }
            }
        });

        fillView(mOrderDetailBean, mCarPoolUserStatuses);
    }

    private void fillView(DidiOrderDetailBean detailBean, List<CarPoolUserStatus> list) {
        if(detailBean == null || detailBean.order == null) return;
        mTvFrom.setText(detailBean.order.startName);
        mTvTo.setText(detailBean.order.endName);
        StringBuilder stringBuilder = new StringBuilder();
        CarPoolUserStatus userStatus = DidiUtils.getCallCarUser(detailBean);
        stringBuilder.append(userStatus == null? PreferenceManager.UserInfo.getUserRealName() : userStatus.getRealName());
        if(CollectionUtil.notNullOrEmpty(list)) {
            stringBuilder.append(" ");
            for (int i = 0; i < list.size(); i++) {
                CarPoolUserStatus st = list.get(i);
                if (DidiUtils.CAR_POOL_STATE_APPROVED.equals(st.getFlowFlag())
                    || DidiUtils.CAR_POOL_STATE_COMPLETE.equals(st.getFlowFlag())) {
                    stringBuilder.append(st.getRealName());
                    stringBuilder.append(" ");
                }
            }
        }
        mTvColleague.setText(stringBuilder);
    }

    public void setOnCancelClickListener(View.OnClickListener onCancelClickListener) {
        mOnCancelClickListener = onCancelClickListener;
    }

    public void setOnConfirmClickListener(View.OnClickListener onConfirmClickListener) {
        mOnConfirmClickListener = onConfirmClickListener;
    }
}