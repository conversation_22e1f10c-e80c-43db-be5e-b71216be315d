package com.jd.oa.business.didi.adapter;

import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.ui.recycler.TypeAdapter;

public class CarAddressTypeAdapter extends TypeAdapter<CarAddress, CarAddressTypeAdapter.VH> {

    private OnItemClickListener<CarAddress> mOnItemClickListener;

    public CarAddressTypeAdapter(OnItemClickListener<CarAddress> onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener<CarAddress> onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        View view = inflater.inflate(R.layout.jdme_item_didi_address, viewGroup, false);
        return new VH(view);
    }

    @Override
    protected void onBindViewHolder(final CarAddress bean, VH holder, final int position) {
        holder.tv_item.setText(bean.getDisplayName());
        holder.tv_item2.setText(bean.getAddress());
        //在这里判断虚拟站点的标记，如果标记是1，那就把图标显示出来。
        String hypothetical = bean.getHypothetical();
        if ("1".equals(hypothetical)){
            holder.tv_item3.setVisibility(View.VISIBLE);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(bean, position);
                }
            }
        });
    }

    public static class VH extends RecyclerView.ViewHolder {

        public TextView tv_item;
        public TextView tv_item2;
        public TextView tv_item3;

        public VH(View itemView) {
            super(itemView);
            tv_item = itemView.findViewById(R.id.tv_item);
            tv_item2 = itemView.findViewById(R.id.tv_item2);
            tv_item3 = itemView.findViewById(R.id.tv_item3);
        }
    }
}
