package com.jd.oa.business.didi.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.jd.oa.business.R;

/**
 * Created by qudo<PERSON><PERSON> on 2017/2/27.
 */
public class OmplaintsPopwindow extends PopupWindow {

    private Context mContext;

    private View mContentView;

    private TextView tvCancel;
    private TextView tvTel;

    private ITelCallback mCallback;
    private String telNum;

    public OmplaintsPopwindow(Context context,String telNum, ITelCallback callback) {
        super(context);
        mContext = context;
        mCallback = callback;
        this.telNum=telNum;
        initView();
    }

    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_omplaints, null);
        setContentView(mContentView);
        tvCancel = (TextView) mContentView.findViewById(R.id.me_pop_cancel);
        tvTel = (TextView) mContentView.findViewById(R.id.me_pop_tel);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
        tvTel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (null != mCallback)
                    mCallback.onTelCallback();
                dismiss();
            }
        });
        tvTel.setText(telNum);
        this.setWidth(ViewGroup.LayoutParams.FILL_PARENT);
        this.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        super.showAtLocation(parent, gravity, x, y);
    }

    public interface ITelCallback {
        void onTelCallback();
    }
}
