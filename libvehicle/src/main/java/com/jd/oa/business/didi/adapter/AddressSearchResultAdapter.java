package com.jd.oa.business.didi.adapter;

import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiAddressBean;

import java.util.List;

public class AddressSearchResultAdapter extends BaseAdapter {
    private final LayoutInflater mInflater;
    private List<DidiAddressBean> list;

    public AddressSearchResultAdapter(LayoutInflater mInflater) {
        this.mInflater = mInflater;
    }

    public void setList(List<DidiAddressBean> list) {
        this.list = list;
    }

    @Override
    public int getCount() {
        if (null == list)
            return 0;
        return list.size();
    }

    @Override
    public DidiAddressBean getItem(int position) {
        return list.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        // 1. 局部变量声明
        View view;
        ViewHolder holder;
        DidiAddressBean bean = list.get(position);

        // 2.view的重用
        if (convertView != null) {
            view = convertView;
            holder = (ViewHolder) view.getTag();
        } else {
            view = mInflater.inflate(R.layout.jdme_item_didi_address, parent, false);
            holder = new ViewHolder();
            holder.tv_item = view.findViewById(R.id.tv_item);
            holder.tv_item2 = view.findViewById(R.id.tv_item2);
            view.setTag(holder);
        }

        SpannableString name = new SpannableString(bean.displayName);
        //placeName.setSpan(holder.tv_item, holder.span_start, holder.span_end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        ForegroundColorSpan span = new ForegroundColorSpan(Color.BLUE);
        holder.span_end = bean.span_end;
        holder.span_start = bean.span_start;
        name.setSpan(span, holder.span_start, holder.span_end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        holder.tv_item.setText(name);
        holder.tv_item2.setText(bean.address);
        return view;
    }

    class ViewHolder {

        public TextView tv_item;
        public TextView tv_item2;

        public int span_start;
        public int span_end;
    }

}
