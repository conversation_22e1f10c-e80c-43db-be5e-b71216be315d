package com.jd.oa.business.didi;

import android.Manifest;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;

import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.R;
import com.jd.oa.business.didi.model.DidiComplaintReasonBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.didi.net.constant.Constant;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 投诉
 * Created by qudongshi on 2016/5/21
 */
@Navigation(hidden = false, displayHome = true)
public class DidiOrderDetailOmplaintsFeeFragment extends BaseFragment {

    public static final String FEE_COMPLAINS_SUCCESS = "FEE_COMPLAINS_SUCCESS";
    private View mRootView;
    private String orderId;
    private String mPhoneNumber;
    private String mServiceType;
    private LinearLayout mLlContainer;
    private EditText mEtContent;
    private TextView mTvCount;
    private DidiComplaintReasonBean didiComplaintReasonBean;
    private int mMajorThemeColor = R.color.skin_color_default;
    private int mMajorChecked;
    private int mMajorBg;
    private int mCheckedId = -1;
    private String mCheckedContent = "";
    private String mCheckedReasonType = "";

    private Button mBtnSubmit;
    private TextView mTvHotline;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_frament_didi_order_detail_omplaints, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_appraisal_fee);
        init();
        orderId = getArguments().getString("orderId");
        mServiceType = getArguments().getString("serviceType");
        mMajorThemeColor = R.color.skin_color_default;
        mMajorChecked = R.drawable.jdme_didi_icon_checked_default;
        mMajorBg = R.drawable.jdme_bg_didi_omplaints_textview_border_checked_default;
        getComplaintReasonList();
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    private void init() {
        mLlContainer = mRootView.findViewById(R.id.ll_container);
        mEtContent = mRootView.findViewById(R.id.et_msg);
        mTvCount = mRootView.findViewById(R.id.tv_count);

        mBtnSubmit = mRootView.findViewById(R.id.btn_submit);
        mBtnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_USER_DELAY_PAY);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_payDelay_submit_click,JDMAConstants.mobile_employeeTravel_traveDetail_payDelay_submit_click);
                if ("02".equals(mServiceType) && "其他".equals(mCheckedContent) && mCheckedId == 7) {
                    //首汽勾选其他，则输入框必须填写
                    if (TextUtils.isEmpty(mEtContent.getText())) {
                        ToastUtils.showInfoToast(R.string.ne_didi_notice_omplaints2);
                        return;
                    }
                }
                submitComplaint();
            }
        });

        mTvHotline = mRootView.findViewById(R.id.tv_consumer_hotline);
        mTvHotline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_traveDetail_payDelay_customerTel_click,JDMAConstants.mobile_employeeTravel_traveDetail_payDelay_customerTel_click);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jd.oa.business.R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mPhoneNumber);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mPhoneNumber);
                }
            }
        });
    }

    private void initView() {
        //首汽的订单修改hint文字
        if ("02".equals(mServiceType)) {
            mEtContent.setHint(R.string.ne_didi_hint_omplaints2);
        }
        mPhoneNumber = didiComplaintReasonBean.servicePhone;

        mEtContent.addTextChangedListener(new TextWatcher() { // 信息修改后更改提字数
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mTvCount.setText(40 - s.length() + "");
            }
        });

        for (DidiComplaintReasonBean.ComplaintReson reason : didiComplaintReasonBean.reasonList) {
            TextView mTmp = (TextView) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_omplains_textview, null);
            mTmp.setText(reason.text);
            mTmp.setTag(reason.type);
            mTmp.setId(StringUtils.convertToInt(reason.id));
            mTmp.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCheckedId == v.getId())
                        return;
                    TextView view = (TextView) v;
                    view.setTextColor(getResources().getColor(mMajorThemeColor));
                    view.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(mMajorChecked), null);
                    view.setBackgroundResource(mMajorBg);
                    if (mCheckedId != -1) {
                        TextView mTvChecked = (TextView) mRootView.findViewById(mCheckedId);
                        mTvChecked.setTextColor(getResources().getColor(R.color.black_main_summary));
                        mTvChecked.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                        mTvChecked.setBackgroundResource(R.drawable.jdme_bg_didi_omplaints_textview_border);
                    }
                    mCheckedId = view.getId();
                    mCheckedContent = view.getText().toString();
                    mCheckedReasonType = (String) view.getTag();
                }
            });
            mLlContainer.addView(mTmp);
        }

    }

    /**
     * 投诉列表
     */
    private void getComplaintReasonList() {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity(), true, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        didiComplaintReasonBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiComplaintReasonBean.class);
                        initView();
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getComplaintReasonList(this, callback, "01", mServiceType);
    }


    private void submitComplaint() {
        if (mCheckedId == -1) {
            ToastUtils.showToast(R.string.me_didi_msg_please_sel_omplaints_reason);
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("reasonId", mCheckedId + "");
        params.put("reasonText", mCheckedContent);
        params.put("content", mEtContent.getText().toString());
        params.put("reasonType", mCheckedReasonType);

        SimpleRequestCallback callback = new SimpleRequestCallback<String>(getActivity()) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                RelativeLayout mLayout = (RelativeLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_view_alert_didi_fee_1, null);
                TextView mTvIknow = (TextView) mLayout.findViewById(R.id.tv_ikonw);
                AlertDialog.Builder mBuilder = new AlertDialog.Builder(getActivity());
                mBuilder.setView(mLayout);
                final AlertDialog alertDialog = mBuilder.create();
                alertDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        // 投诉成功后 发送广播刷新订单详情页，关闭费用二次确认页面
                        if (getActivity() != null) {
                            Intent intent = new Intent(FEE_COMPLAINS_SUCCESS);
                            intent.putExtra("isFeeComplainted", "1");
                            getActivity().sendBroadcast(intent);

                            getActivity().onBackPressed();
                            Bundle bundle = new Bundle();
                            bundle.putString("isFeeSuccess", "1");
//                        FragmentUtils.updateUI(OperatingListener.OPERATE_GO_DIDI, bundle);
                        }
                    }
                });
                mTvIknow.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        alertDialog.dismiss();
                    }
                });
                alertDialog.show();
                DisplayMetrics dm = new DisplayMetrics();
                //取得窗口属性
                getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
                int width = (int) (dm.widthPixels * 0.9);
                alertDialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showToast(info);
            }

        };
        callback.setNeedTranslate(false);
        NetWorkManager.request(this, Constant.getConstant(NetWorkManager.mReqeustType).ACTION_COMMIT_FEE, callback, params);
    }
}
