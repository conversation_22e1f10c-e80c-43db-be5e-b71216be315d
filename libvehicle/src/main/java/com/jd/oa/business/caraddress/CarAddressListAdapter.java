package com.jd.oa.business.caraddress;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.business.R;
import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.utils.StringUtils;

import java.util.List;

public class CarAddressListAdapter extends RecyclerView.Adapter<CarAddressListAdapter.VH> {

    private List<CarAddress> mCarAddressList;
    private Context mContext;
    private Action mAction;

    public CarAddressListAdapter(Action action) {
        this.mAction = action;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup viewGroup, int i) {
        mContext = viewGroup.getContext();
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_fragment_car_address_list_item, viewGroup, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH vh, int i) {
        final CarAddress carAddress = mCarAddressList.get(i);
        vh.title.setText(StringUtils.getSubStringFromStart(carAddress.getDisplayName(),15));
        vh.edit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mAction != null) {
                    mAction.onEditClick(carAddress);
                }
            }
        });
        vh.addressDesc.setText(mContext.getString(R.string.me_car_address_title) + carAddress.getAddressOrder());
        vh.address.setText(StringUtils.getSubStringFromStart(carAddress.getAddress(),20));
        if (CarAddress.FLOWFLAG_APPROVEING.equals(carAddress.getFlowFlag())) {
            vh.edit.setVisibility(View.GONE);
            vh.flowFlag.setVisibility(View.VISIBLE);
        } else {
            vh.edit.setVisibility(View.VISIBLE);
            vh.flowFlag.setVisibility(View.GONE);
        }
    }

    public void setCarAddressList(List<CarAddress> mCarAddressList) {
        this.mCarAddressList = mCarAddressList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        if (mCarAddressList != null) {
            return mCarAddressList.size();
        } else {
            return 0;
        }
    }

    public static class VH extends RecyclerView.ViewHolder {

        TextView title;
        TextView address;
        ImageView edit;
        TextView addressDesc;
        TextView flowFlag;

        public VH(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
            address = itemView.findViewById(R.id.tv_address);
            edit = itemView.findViewById(R.id.im_edit);
            addressDesc = itemView.findViewById(R.id.tv_desc);
            flowFlag = itemView.findViewById(R.id.tv_flow_flag);
        }
    }

    public interface Action {
        void onEditClick(CarAddress carAddress);
    }
}
