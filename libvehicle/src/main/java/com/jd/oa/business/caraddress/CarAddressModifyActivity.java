package com.jd.oa.business.caraddress;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.business.R;
import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.business.didi.DidiChangeCityActivity;
import com.jd.oa.business.didi.adapter.AddressSearchResultAdapter;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.didi.model.DidiCityBean;
import com.jd.oa.business.didi.model.DidiCityInfoBean;
import com.jd.oa.business.didi.model.DidiListAddress;
import com.jd.oa.business.didi.widget.ClearableAutoCompleteTextView;
import com.jd.oa.melib.utils.PermissionUtils;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class CarAddressModifyActivity extends BaseActivity implements SosoLocationChangeInterface, CarAddressContract.ICarAddressModifyView {
    public static final int EXTRE_MODE_ADD = 1;
    public static final int EXTRE_MODE_MODIFY = 2;

    public static final String EXTRA_KEY_MODE = "mode";
    public static final String EXTRA_KEY_ORDER = "order";
    public static final String EXTRA_KEY_ADDRESS_ID = "addressId";
    public static final int REQUEST_CODE_CHANGE_CITY = 100;

    private TextView mTvCity;
    private EditText mEditText;
    private ListView mListView;
    private TextView mBtn;
    private ClearableAutoCompleteTextView mCetReason; // 加班原因

    private SosoLocationService locationService;
    private String mCityName;
    private String mCityCode;
    private DidiCityBean mDidiCityBean;

    private List<DidiAddressBean> list;
    private CarAddressContract.ICarAddressModifyPresenter mPresenter;
    private AddressSearchResultAdapter mAddressSearchAdapter;
    private DidiAddressBean mCurrentAddressBean;
    private String mCurrentOrder;
    private int mAddressId;
    private int mMode = EXTRE_MODE_ADD;
    private String isNeedApprova = "0";
    private final TextWatcher textWatcher = new TextWatcher() {

        @Override
        public void onTextChanged(CharSequence s, int start, int before,
                                  int count) {
        }

        @Override
        public void afterTextChanged(Editable editable) {
            mListView.setVisibility(View.VISIBLE);
            getAddress();
            mBtn.setEnabled(false);
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_car_address_modify_layout);
        init();
    }

    private void init() {
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setDisplayHomeAsUpEnabled(true);
        mMode = getIntent().getIntExtra(EXTRA_KEY_MODE, EXTRE_MODE_ADD);
        mCurrentOrder = getIntent().getStringExtra(EXTRA_KEY_ORDER);
        mAddressId = getIntent().getIntExtra(EXTRA_KEY_ADDRESS_ID, -1);
        mTvCity = findViewById(R.id.tv_city);
        mBtn = findViewById(R.id.tv_action);
        mCetReason = findViewById(R.id.cet_reason);
        mEditText = findViewById(R.id.id_workplace_search_et);
        mListView = findViewById(R.id.me_list);
        mEditText.addTextChangedListener(textWatcher);
        mAddressSearchAdapter = new AddressSearchResultAdapter(this.getLayoutInflater());
        mListView.setAdapter(mAddressSearchAdapter);
        TextView emptyView = new TextView(this);
        emptyView.setText(getString(R.string.me_not_found_address));
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT);
        emptyView.setLayoutParams(lp);
        emptyView.setGravity(Gravity.CENTER);
        emptyView.setTextColor(getResources().getColor(R.color.black_main_summary));
        emptyView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        ((ViewGroup) mListView.getParent()).addView(emptyView);
        mListView.setEmptyView(emptyView);
        mListView.setVisibility(View.GONE);
        mBtn.setEnabled(false);
        if (EXTRE_MODE_ADD == mMode) {
            mBtn.setText(R.string.me_car_address_ok);
            ActionBarHelper.getActionBar(this).setTitle(R.string.me_car_address_add_title);
        } else {
//            mCetReason.setVisibility(View.VISIBLE);
//            mBtn.setText(R.string.me_car_address_submit);
            mBtn.setText(R.string.me_car_address_ok);
            ActionBarHelper.getActionBar(this).setTitle(R.string.me_car_address_modify_title);
        }
        ActionBarHelper.getActionBar(this).setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);

        mBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mMode == EXTRE_MODE_ADD) {
                    mPresenter.addCarAddress(transformBean(mCurrentAddressBean));
                } else {
                    isNeedApprova = "0";
//                    if (mCetReason.getText().toString().trim().isEmpty()) {
//                        ToastUtils.showToast(getResources().getString(R.string.me_didi_msg_please_input_address_reason));
//                    } else {
//                        PromptUtils.showConfrimDialog(CarAddressModifyActivity.this, R.string.me_car_address_modify_title, getString(R.string.me_car_address_tip_modify), new DialogInterface.OnClickListener() {
//                            @Override
//                            public void onClick(DialogInterface dialog, int which) {
//                                mPresenter.modifyCarAddress(transformBean(mCurrentAddressBean));
//                            }
//                        }, new DialogInterface.OnClickListener() {
//                            @Override
//                            public void onClick(DialogInterface dialog, int which) {
//                                dialog.dismiss();
//                            }
//                        });
//                    }
                    mPresenter.modifyCarAddress(transformBean(mCurrentAddressBean));

                }
            }
        });
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                mCurrentAddressBean = list.get(position);
                mBtn.setEnabled(true);
                mEditText.removeTextChangedListener(textWatcher);
                mEditText.setText(mCurrentAddressBean.displayName);
                mEditText.addTextChangedListener(textWatcher);
                mListView.setVisibility(View.GONE);
            }
        });
        mTvCity.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mDidiCityBean == null) {
                    checkCity();
                    return;
                }
                Intent intent = new Intent(getContext(), DidiChangeCityActivity.class);
                intent.putExtra("entity", mDidiCityBean.cityList);
                intent.putExtra("cityName", mCityName);
                startActivityForResult(intent, REQUEST_CODE_CHANGE_CITY);
            }
        });
        mPresenter = new CarAddressModifyPresenterImpl(this);
        locationService = new SosoLocationService(this);

        PermissionHelper.requestPermission(this, getResources().getString(R.string.me_request_permission_location_travel), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                locationService.startLocationWithCheck();
                locationService.setLocationChangedListener(CarAddressModifyActivity.this);
            }

            @Override
            public void denied(List<String> deniedList) {
                checkCity();
            }
        },Manifest.permission.ACCESS_FINE_LOCATION);
    }


    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 获取办公城市
     */
    private void checkCity() {
        mPresenter.getCityList(mCityName);
    }

    @Override
    public void onLocated(String lat, String lng, String name, String cityName) {
        if (!TextUtils.isEmpty(cityName)) {
            mCityName = cityName.replaceAll("市", "");
        }

        checkCity();
    }

    @Override
    public void onFailed() {
        checkCity();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtils.requestResult(requestCode, permissions, grantResults, new Runnable() {
            @Override
            public void run() {
                if (locationService != null) {
                    locationService.startLocationWithCheck();
                    locationService.setLocationChangedListener(CarAddressModifyActivity.this);
                }
            }
        }, null);
    }

    @Override
    public void onCheckCityResult(DidiCityBean didiCityBean) {
        mDidiCityBean = didiCityBean;
        if (null != didiCityBean.currentCity && null != didiCityBean.currentCity.cityName && null != didiCityBean.currentCity.cityName) {
            mCityCode = didiCityBean.currentCity.cityCode;
            mCityName = didiCityBean.currentCity.cityName;
        } else if (null != didiCityBean.defaultCity) {
            mCityCode = didiCityBean.defaultCity.cityCode;
            mCityName = didiCityBean.defaultCity.cityName;
        }
        mTvCity.setText(mCityName);
    }

    @Override
    public void onModifyCarAddressSuccess(JSONObject jsonObject) {
        PromptUtils.removeLoadDialog(this);
        JSONObject jsonObject1 = jsonObject.optJSONObject("content");
        if (jsonObject1 != null) {
            String isNeedApproval = jsonObject1.optString("isNeedApproval");
            if ("1".equals(isNeedApproval)) {//大于500米  需要添加审批原因
                showDialog(jsonObject.optString("errorMsg"));
                return;
            }
        }
        setResult(RESULT_OK);
        finish();
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(this, getString(R.string.me_loading_message_not_translate));
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(this);
        ToastUtils.showToast(s);
    }

    @Override
    public Context getContext() {
        return this;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == 100) {
            if (REQUEST_CODE_CHANGE_CITY == requestCode) {
                DidiCityInfoBean bean = (DidiCityInfoBean) data.getSerializableExtra("cityinfo");
                if (!mCityName.equals(bean.cityName)) {
                    mCityName = bean.cityName;
                    mCityCode = bean.cityCode;
                    mTvCity.setText(mCityName);
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    private List<DidiAddressBean> findItemsbyName(String str) {
        List<DidiAddressBean> findList = new ArrayList<>();
        for (DidiAddressBean addressBean : list) {
            DidiAddressBean bean = new DidiAddressBean();
            bean.displayName = addressBean.displayName;
            bean.address = addressBean.address;
            bean.cityCode = addressBean.cityCode;
            bean.cityName = addressBean.cityName;
            bean.lat = addressBean.lat;
            bean.lng = addressBean.lng;
            if (addressBean.displayName.contains(str)) {
                bean.span_start = addressBean.displayName.indexOf(str);
                bean.span_end = bean.span_start + str.length();
                if (bean.span_end < 0) {
                    bean.span_end = 0;
                }
            }
            findList.add(bean);
        }
        return findList;
    }

    public void getAddress() {
        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(this, false, false) { // 获取职场地址

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getContext(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString())) {
                            return;
                        }
                        mListView.setVisibility(View.VISIBLE);
                        DidiListAddress didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiListAddress.class);
                        list = didiAddressBean.placeData;
                        list = findItemsbyName(mEditText.getText().toString());
                        mAddressSearchAdapter.setList(list);
                        mAddressSearchAdapter.notifyDataSetChanged();
                        mListView.setAdapter(mAddressSearchAdapter);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
        callback.setNeedTranslate(false);
        NetWorkManager.getAddressByInput(this, callback, mEditText.getText().toString(), "", mCityName);
    }

    private CarAddress transformBean(DidiAddressBean bean) {
        if (bean != null) {
            CarAddress carAddress = new CarAddress();
            carAddress.setAddress(bean.address);
            carAddress.setDisplayName(bean.displayName);
            carAddress.setCityName(bean.cityName);
            carAddress.setLat(bean.lat);
            carAddress.setLng(bean.lng);
            carAddress.setAddressOrder(mCurrentOrder
            );
            carAddress.setIsNeedApprova(isNeedApprova);//isNeedApproval 这个字段 0第一次修改不需要审批   1第二次修改  有审批原因
            if (mMode != EXTRE_MODE_ADD) {
                carAddress.setRemark(mCetReason.getText().toString().trim());
                carAddress.setId(mAddressId);
            }
            return carAddress;
        }
        return null;
    }

    public void showDialog(String titleMsg) {
        LinearLayout mLayout = (LinearLayout) LayoutInflater.from(this).inflate(R.layout.jdme_dialog_alert_caraddress_modify, null);
        TextView mTvMsg = (TextView) mLayout.findViewById(R.id.tv_title_msg);
        mTvMsg.setText(titleMsg);
        EditText editText = mLayout.findViewById(R.id.ed_approval);
        final Button bt_submit = mLayout.findViewById(R.id.bt_submit);
        bt_submit.setEnabled(false);
        bt_submit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.modifyCarAddress(transformBean(mCurrentAddressBean));
            }
        });
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s.toString().trim())) {
                    bt_submit.setEnabled(false);
                    bt_submit.setSelected(false);
                    mCetReason.setText("");
                } else {
                    bt_submit.setSelected(true);
                    bt_submit.setEnabled(true);
                    mCetReason.setText(s.toString().trim());
                    isNeedApprova = "1";
                }
            }
        });
        AlertDialog.Builder mBuilder = new AlertDialog.Builder(this);
        mBuilder.setView(mLayout);
        final AlertDialog alertDialog = mBuilder.create();
        alertDialog.show();
        DisplayMetrics dm = new DisplayMetrics();
        //取得窗口属性
        getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.8);
        alertDialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
        ImageView iv_close = (ImageView) mLayout.findViewById(R.id.iv_close);
        iv_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isNeedApprova = "0";
                mCetReason.setText("");
                alertDialog.dismiss();
            }
        });
    }
}
