package com.jd.oa.business.didi;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import android.text.TextUtils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.didi.model.DidiOrderDetailBean;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.NotificationUtils;
import com.jd.oa.utils.ResponseParser;


import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Created by qudo<PERSON><PERSON> on 2016/1/21.
 */
public class DidiPollingService extends Service {

    public static final String TAG = "DidiPollingService";
    public static final String KEY_ORDER_ID = "orderId";
    public static final String KEY_PHONE_NUMBER = "phoneNumber";

    public static final String KEY_SLEEP_SECOND = "sleepSecond";

    private String mOrderId;
    private String mPhoneNumber;

    private int mSleepSecond = 0;

    private Context mContext;

    private PollingThread mPollingThread;

    private boolean mStopFlag = false;

    @Override
    public void onCreate() {
        super.onCreate();
        mContext = this;
        MELogUtil.localD(MELogUtil.TAG_TAX, TAG + " onCreate");
        startForeground();
    }

    private void startForeground() {
        try {
            NotificationChannel notificationChannel = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                notificationChannel = new NotificationChannel(NotificationUtils.CHANNEL_ID_OTHER,
                        NotificationUtils.CHANNEL_NAME_OTHER, NotificationManager.IMPORTANCE_LOW);
                NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
                assert manager != null;
                manager.createNotificationChannel(notificationChannel);
                startForeground(113, new NotificationCompat.Builder(this, NotificationUtils.CHANNEL_ID_OTHER).build());
                MELogUtil.localD(MELogUtil.TAG_TAX, TAG + " startForeground");
            }
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(MELogUtil.TAG_TAX, TAG + " startForeground");
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        MELogUtil.localD(MELogUtil.TAG_TAX, TAG + " onStartCommand");
        startForeground();
        if (null != intent) {
            mOrderId = intent.getStringExtra(KEY_ORDER_ID);
            mPhoneNumber = intent.getStringExtra(KEY_PHONE_NUMBER);
            mSleepSecond = intent.getIntExtra(KEY_SLEEP_SECOND, 0);
            if (null == mPollingThread){
                mPollingThread = new PollingThread();
            }
            if (null != mOrderId && !mPollingThread.isAlive()){
                mPollingThread.start();
            }
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        MELogUtil.localD(MELogUtil.TAG_TAX, TAG + "onDestory");
        mStopFlag = true;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            stopForeground(true);
        }
        if (null != mPollingThread)
            mPollingThread.interrupt();
        super.onDestroy();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    class PollingThread extends Thread {

        @Override
        public void run() {
            while (!mStopFlag && !isInterrupted()) {
                SimpleRequestCallback callback = new SimpleRequestCallback<String>(mContext, false, false) { // 获取职场地址

                    @Override
                    public void onSuccess(final ResponseInfo<String> request) {
                        super.onSuccess(request);
                        if (isInterrupted())
                            return;
                        ResponseParser parser = new ResponseParser(request.result, mContext, false);
                        parser.parse(new ResponseParser.ParseCallback() {
                            @Override
                            public void parseObject(JSONObject jsonObject) {
                                if (TextUtils.isEmpty(jsonObject.toString()))
                                    return;
                                DidiOrderDetailBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiOrderDetailBean.class);
                                EventBusMgr.getInstance().post(didiAddressBean);
                            }

                            @Override
                            public void parseArray(JSONArray jsonArray) {
                                EventBusMgr.getInstance().post(null);
                            }

                            @Override
                            public void parseError(String errorMsg) {
                                EventBusMgr.getInstance().post(null);
                            }
                        });
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        if (isInterrupted())
                            return;
                        EventBusMgr.getInstance().post(null);
                    }
                };
                callback.setNeedTranslate(false);
                NetWorkManager.getOrderDetail(null, callback, mOrderId, mPhoneNumber);

                try {
                    sleep(mSleepSecond * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
