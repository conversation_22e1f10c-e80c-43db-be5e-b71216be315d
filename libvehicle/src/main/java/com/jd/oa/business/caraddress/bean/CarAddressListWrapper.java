package com.jd.oa.business.caraddress.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class CarAddressListWrapper implements Parcelable {
    private String addressSize;
    private List<String> addressText;
    private List<CarAddress> addressList;

    public List<String> getAddressText() {
        return addressText;
    }

    public void setAddressText(List<String> addressText) {
        this.addressText = addressText;
    }

    public String getAddressSize() {
        return addressSize;
    }

    public void setAddressSize(String addressSize) {
        this.addressSize = addressSize;
    }

    public List<CarAddress> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<CarAddress> addressList) {
        this.addressList = addressList;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.addressSize);
        dest.writeTypedList(this.addressList);
    }

    public CarAddressListWrapper() {
    }

    protected CarAddressListWrapper(Parcel in) {
        this.addressSize = in.readString();
        this.addressList = in.createTypedArrayList(CarAddress.CREATOR);
    }

    public static final Parcelable.Creator<CarAddressListWrapper> CREATOR = new Parcelable.Creator<CarAddressListWrapper>() {
        @Override
        public CarAddressListWrapper createFromParcel(Parcel source) {
            return new CarAddressListWrapper(source);
        }

        @Override
        public CarAddressListWrapper[] newArray(int size) {
            return new CarAddressListWrapper[size];
        }
    };
}
