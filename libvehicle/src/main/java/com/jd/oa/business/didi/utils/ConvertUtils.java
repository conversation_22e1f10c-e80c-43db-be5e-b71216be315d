package com.jd.oa.business.didi.utils;

import com.jd.oa.business.caraddress.bean.CarAddress;
import com.jd.oa.business.didi.model.DidiAddressBean;

public class ConvertUtils {

    public static DidiAddressBean convertAddressBean(CarAddress carAddress) {
        if (carAddress != null) {
            DidiAddressBean bean = new DidiAddressBean();
            bean.address = carAddress.getAddress();
            bean.cityName = carAddress.getCityName();
            bean.displayName = carAddress.getDisplayName();
            bean.lat = carAddress.getLat();
            bean.lng = carAddress.getLng();
            bean.cityCode = carAddress.getCityCode();
            return bean;
        } else {
            return null;
        }
    }

}
