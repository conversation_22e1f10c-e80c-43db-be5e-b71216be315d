package com.jd.oa.business.caraddress;

import android.os.Bundle;
import android.view.MenuItem;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.business.R;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;

/**
 * <AUTHOR>
 * <p>
 * 打车常用地址
 */
@Route(DeepLink.COMMONLY_USED_ADDRESS)
public class CarAddressActivity extends BaseActivity {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_car_address_layout);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setDisplayHomeAsUpEnabled(true);
        ActionBarHelper.getActionBar(this).setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);
        FragmentUtils.addWithCommit(this, CarAddressFragment.newInstance(), R.id.frame_layout);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
