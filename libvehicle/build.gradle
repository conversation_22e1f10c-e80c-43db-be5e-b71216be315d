apply plugin: 'com.android.library'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.jd.oa.business'
    lint {
        abortOnError false
    }


//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['libs']
//        }
//    }
//    repositories {
//        flatDir {
//            dirs 'lib_outer'
//        }
//    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

    implementation COMPILE_SUPPORT.design
    implementation COMPILE_SUPPORT.annotations
    implementation COMPILE_SUPPORT.recyclerview
    implementation COMPILE_COMMON.gson
    implementation COMPILE_SUPPORT.cardview

//    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'

    implementation(libs.lib.utils) { dep ->
        ['com.jd.oa', 'com.squareup.okhttp3', 'com.github.lib'].each { group -> dep.exclude group: group }
        exclude module: 'mae-bundles-widget'
    }

    implementation 'com.githang:status-bar-compat:latest.integration'
    implementation 'androidx.multidex:multidex:2.0.0'
    implementation 'com.tuyenmonkey:text-decorator:1.0.0'
    implementation 'com.jd.oa:mae-bundles-monitorfragment:1.0.2-SNAPSHOT'
    implementation libs.universal.image.loader

//    implementation project(':login')
    implementation project(':common')

//    provided(name: 'jimsdk-v1.0.0', ext: 'aar')

}
