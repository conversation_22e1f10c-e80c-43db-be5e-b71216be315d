#!/bin/bash

# AAR 发布脚本工具
# 用法: ./publish_aar.sh <aar_module_name> [version] [environment] [artifactId]
#
# 参数说明:
# aar_module_name: aar模块名称 (如: aar_jimcore)
# version: 版本号 (默认: 1.0.0-SNAPSHOT)
# environment: 发布环境 snapshot|release (默认: snapshot)
# artifactId: 自定义artifactId (可选，默认使用模块名)

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 <aar_module_name> [version] [environment] [artifactId]"
    echo ""
    echo "参数说明:"
    echo "  aar_module_name: aar模块名称 (如: aar_jimcore, aar_netDisk)"
    echo "  version:         版本号 (默认: 1.0.0-SNAPSHOT)"
    echo "  environment:     发布环境 snapshot|release (默认: snapshot)"
    echo "  artifactId:      自定义artifactId (可选，默认使用模块名)"
    echo ""
    echo "示例:"
    echo "  $0 aar_jimcore"
    echo "  $0 aar_jimcore 1.0.1-SNAPSHOT"
    echo "  $0 aar_DatePicker 1.0.0-SNAPSHOT snapshot jim_datepicker"
    echo "  $0 aar_netDisk 1.5.14 release"
    echo "  $0 aar_jimcore 1.1.0 release jimcore-android"
    echo ""
    echo "可用的 aar 模块:"
    ls aar_module/ | grep aar_ | head -10
    echo "  ... (更多模块请查看 aar_module/ 目录)"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "缺少必要参数"
    show_usage
    exit 1
fi

AAR_MODULE=$1
VERSION=${2:-"1.0.0-SNAPSHOT"}
ENVIRONMENT=${3:-"snapshot"}
ARTIFACT_ID=${4:-$AAR_MODULE}

# 验证环境参数
if [[ "$ENVIRONMENT" != "snapshot" && "$ENVIRONMENT" != "release" ]]; then
    log_error "环境参数只能是 'snapshot' 或 'release'"
    exit 1
fi

# 检查模块是否存在
AAR_MODULE_PATH="aar_module/$AAR_MODULE"
if [ ! -d "$AAR_MODULE_PATH" ]; then
    log_error "aar 模块 '$AAR_MODULE' 不存在"
    log_info "可用模块:"
    ls aar_module/ | grep aar_
    exit 1
fi

log_info "准备发布 aar 模块: $AAR_MODULE"
log_info "版本号: $VERSION"
log_info "发布环境: $ENVIRONMENT"
log_info "ArtifactId: $ARTIFACT_ID"

# 备份原始 build.gradle
ORIGINAL_BUILD_GRADLE="$AAR_MODULE_PATH/build.gradle"
BACKUP_BUILD_GRADLE="$AAR_MODULE_PATH/build.gradle.bak"

if [ -f "$ORIGINAL_BUILD_GRADLE" ]; then
    cp "$ORIGINAL_BUILD_GRADLE" "$BACKUP_BUILD_GRADLE"
    log_info "已备份原始 build.gradle"
fi

# 确定仓库类型和 URL
if [[ "$ENVIRONMENT" == "release" ]]; then
    REPO_KEY="libs-releases-local"
    MAVEN_URL_SUFFIX="libs-releases-local"
    REPO_URL="http://artifactory.jd.com/libs-releases-local/"
    BROWSE_URL="http://artifactory.jd.com/webapp/#/artifacts/browse/tree/General/libs-releases-local/com/jd/oa/${ARTIFACT_ID}"
else
    REPO_KEY="libs-snapshots-local"
    MAVEN_URL_SUFFIX="libs-snapshots-local"
    REPO_URL="http://artifactory.jd.com/libs-snapshots-local/"
    BROWSE_URL="http://artifactory.jd.com/webapp/#/artifacts/browse/tree/General/libs-snapshots-local/com/jd/oa/${ARTIFACT_ID}"
fi

# 生成发布用的 build.gradle
cat > "$ORIGINAL_BUILD_GRADLE" << EOF
apply plugin: 'maven-publish'

configurations.maybeCreate("default")

// 查找 aar 文件
def aarFile = null
def possiblePaths = [
    '../../libjdreact/libs/',
    '../../im_dd/libs/',
    '../../common/libs/',
    '../../wifiauth/libs/',
    '../../libs/',
    './libs/',
    './'
]

def aarPattern = ~/.*\.aar$/

// 解析当前模块的 build.gradle 文件，获取 AAR 文件名
def getAarFileNameFromBuildGradle() {
    def buildGradleFile = new File('./build.gradle.bak')
    if (!buildGradleFile.exists()) {
        println ">> No build.gradle.bak found, using default search pattern"
        return ['${AAR_MODULE}'.replace('aar_', '')]
    }
    
    def aarFileNames = []
    buildGradleFile.eachLine { line ->
        // 匹配 artifacts.add("default", file('path/to/file.aar')) 格式
        def matcher = line =~ /artifacts\.add\s*\(\s*["']default["']\s*,\s*file\s*\(\s*["']([^"']+\.aar)["']\s*\)\s*\)/
        if (matcher) {
            def fullPath = matcher[0][1]
            def fileName = new File(fullPath).name
            def baseName = fileName.replaceAll(/\.aar$/, '')
            aarFileNames.add(baseName)
            println ">> Found AAR reference in build.gradle: \${fileName} -> search pattern: \${baseName}"
        }
    }
    
    if (aarFileNames.isEmpty()) {
        println ">> No AAR reference found in build.gradle, using default search pattern"
        return ['${AAR_MODULE}'.replace('aar_', '')]
    }
    
    return aarFileNames
}

def searchPatterns = getAarFileNameFromBuildGradle()
println ">> Searching for AAR files with patterns: \${searchPatterns} from module: ${AAR_MODULE}"

possiblePaths.each { path ->
    println ">> Searching in path: \${path}"
    if (aarFile == null) {
        fileTree(dir: path, include: '*.aar').each { file ->
            println "Found AAR file: \${file.name} in path: \${path}"
            
            // 检查是否匹配任何一个搜索模式
            def matched = false
            for (pattern in searchPatterns) {
                if (file.name.startsWith(pattern)) {
                    println "✓ Matched AAR file: \${file.name} for module: ${AAR_MODULE} (pattern: '\${pattern}')"
                    aarFile = file
                    matched = true
                    return true
                }
            }
            
            if (!matched) {
                println "✗ AAR file: \${file.name} does not contain any pattern: \${searchPatterns}"
            }
        }
    }
}

if (aarFile == null) {
    // 如果没找到，使用原始配置中的文件
    println "No specific AAR found, searching in current directory for any AAR files"
    fileTree(dir: './', include: '*.aar').each { file ->
        println "Using fallback AAR file: \${file.name}"
        aarFile = file
        return true
    }
}

if (aarFile != null) {
    artifacts.add("default", aarFile)
    println "Found AAR file: \${aarFile.absolutePath}"
} else {
    throw new RuntimeException("Cannot find AAR file for module ${AAR_MODULE}")
}

ext {
    version_name = "${VERSION}"
}

publishing {
    publications {
        aar(MavenPublication) {
            groupId = "com.jd.oa"
            artifactId = "${ARTIFACT_ID}"
            version = "${VERSION}"
            
            if (aarFile != null) {
                artifact(aarFile)
            }
            
            pom {
                name = "${ARTIFACT_ID}"
                description = "AAR module ${AAR_MODULE} -> ${ARTIFACT_ID}"
            }
        }
    }
    
    repositories {
        maven {
            name = "artifactory"
            url = "${REPO_URL}"
            allowInsecureProtocol = true
            credentials {
                username = "fengyiyi"
                password = "AKCpBu4vtVVmecvepyG6Hj8bBk8EfMfQgSyMCG2pm3BSV8qG9skMFQH8qKsEgct3HpEWVowYZ"
            }
        }
    }
}

// 配置 Gradle 上传超时时间
gradle.projectsEvaluated {
    tasks.withType(PublishToMavenRepository) {
        doFirst {
            // 设置较长的超时时间
            System.setProperty("org.gradle.internal.http.socketTimeout", "300000")  // 5分钟
            System.setProperty("org.gradle.internal.http.connectionTimeout", "60000") // 1分钟
        }
    }
}

// 创建一个任务来显示将要发布的信息
task showPublishInfo {
    doLast {
        println "========================================"
        println "发布信息:"
        println "模块名: ${AAR_MODULE}"
        println "版本: ${VERSION}"
        println "GroupId: com.jd.oa"
        println "ArtifactId: ${ARTIFACT_ID}"
        println "仓库: ${REPO_KEY}"
        println "仓库URL: ${REPO_URL}"
        println "浏览链接: ${BROWSE_URL}"
        println "Maven坐标: implementation 'com.jd.oa:${ARTIFACT_ID}:${VERSION}'"
        println "========================================"
    }
}

publishAarPublicationToArtifactoryRepository.dependsOn showPublishInfo
EOF

log_info "已生成发布配置"

# 执行发布
log_info "开始发布..."
cd "$AAR_MODULE_PATH"

# 显示发布信息
../../gradlew showPublishInfo

# 确认发布
echo ""
read -p "确认发布? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "执行发布..."
    ../../gradlew publishAarPublicationToArtifactoryRepository
    
    if [ $? -eq 0 ]; then
        log_info "发布成功!"
        echo ""
        log_info "═══════════════════════════════════════"
        log_info "Maven 依赖坐标:"
        echo "implementation 'com.jd.oa:${ARTIFACT_ID}:${VERSION}'"
        echo ""
        log_info "仓库浏览链接:"
        echo "${BROWSE_URL}"
        echo ""
        log_info "直接下载链接:"
        echo "${REPO_URL}com/jd/oa/${ARTIFACT_ID}/${VERSION}/${ARTIFACT_ID}-${VERSION}.aar"
        log_info "═══════════════════════════════════════"
    else
        log_error "发布失败!"
        exit 1
    fi
else
    log_warn "取消发布"
fi

# 恢复原始 build.gradle
if [ -f "$BACKUP_BUILD_GRADLE" ]; then
    mv "$BACKUP_BUILD_GRADLE" "$ORIGINAL_BUILD_GRADLE"
    log_info "已恢复原始 build.gradle"
fi

cd - > /dev/null

log_info "完成"