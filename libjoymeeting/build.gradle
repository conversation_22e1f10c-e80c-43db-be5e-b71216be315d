apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        vectorDrawables.useSupportLibrary = true
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION


        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    flavorDimensions.addAll(flavor_dimensions)
    productFlavors {
        me {
            dimension 'app'
            matchingFallbacks = ['me', 'timline']
            isDefault = true
        }
        saas {
            dimension 'app'
            matchingFallbacks = ['saas', 'timline']
        }
        official {
            dimension 'channel'
            matchingFallbacks = ['official', 'timline']
            isDefault = true
        }
        store {
            dimension 'channel'
            matchingFallbacks = ['store', 'timline']
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.jd.oa.libjoymeeting'
    lint {
        abortOnError false
    }

    configurations.all {
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.32"
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.32"
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib:1.4.32"
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-common:1.4.32"
    }
}

dependencies {

    api fileTree(dir: 'libs', include: ['*.jar'])

    implementation project(":common")
//    compileOnly project(":libjoymeeting_sdk")
    
    meImplementation 'com.jd.oa:lib_cucloud:1220-SNAPSHOT'
    meImplementation 'com.jd.oa:lib_mobilertc_zoom:0919-SNAPSHOT'
    meImplementation 'com.jd.oa:lib_zoom_commonlib:0823-SNAPSHOT'
    meImplementation 'com.google.android.exoplayer:exoplayer-core:2.16.1'
    meImplementation 'com.google.android.exoplayer:exoplayer-ui:2.16.1'
    meImplementation 'com.airbnb.android:lottie:5.2.0'
    
//    saasImplementation 'com.jd.oa:lib_cucloud:1220-SNAPSHOT'
//    saasImplementation 'com.jd.oa:lib_mobilertc_zoom:0919-SNAPSHOT'
//    saasImplementation 'com.jd.oa:lib_zoom_commonlib:0823-SNAPSHOT'
}