package com.jd.oa.joymeeting;

import android.content.Context;

import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.Map;

/**
 * JoyMeeting SAAS Empty Implementation
 * Created on 2019/4/18
 */
@SuppressWarnings({"WeakerAccess", "unused"})
public class JoyMeetingUtils {
    private static final String TAG = "JoyMeetingUtils";

    public static final String TYPE_CANCEL = "0";
    public static final String TYPE_IMMEDIATE = "1";
    public static final String TYPE_SCHEDULED = "2";
    public static final String TYPE_SCHEDULED_REMAIN = "21";
    public static final String TYPE_PERIODIC = "3";

    public static final String STATUS_ACCEPT = "1";
    public static final String STATUS_REJECT = "2";

    public static final boolean isSaaS = true;

    public static void putDeepLink(Map<String, Class<?>> map) {
        // Empty implementation for SAAS
    }

    public static void updateMeetingStatus(final Context context, final String appId, final String meetingId, final String type, final String status, final LoadDataCallback<Object> callback) {
        // Empty implementation for SAAS
    }
}