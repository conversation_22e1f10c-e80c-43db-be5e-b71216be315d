package com.jd.oa.joymeeting;

import android.content.Context;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.model.AppSdkTokenBean;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.open.TokenInfoBean;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;

import java.util.Map;

import cn.cu.jdmeeting.jme.handler.RequestHandler;
import cn.cu.jdmeeting.jme.net.impl.INetTasksListener;
import cn.cu.jdmeeting.jme.ui.activity.MeetingFunctionActivity;

/**
 * JoyMeeting
 * Created on 2019/4/18
 */
@SuppressWarnings({"WeakerAccess", "unused"})
public class JoyMeetingUtils {
    private static final String TAG = "JoyMeetingUtils";
    private static final String SERVER_POC = "http://59.151.64.101/jdMeeting/api/v4/notice";


    public static final String TYPE_CANCEL = "0";
    //即时会议
    public static final String TYPE_IMMEDIATE = "1";
    //预约会议
    public static final String TYPE_SCHEDULED = "2";
    //预约会议前一分钟提醒
    public static final String TYPE_SCHEDULED_REMAIN = "21";
    //周期会议
    public static final String TYPE_PERIODIC = "3";

    public static final String STATUS_ACCEPT = "1";
    public static final String STATUS_REJECT = "2";

    public static final boolean isSaaS = MultiAppConstant.isSaasFlavor();

    public static void putDeepLink(Map<String, Class<?>> map) {
        if(isSaaS){
            return;
        }
        map.put(DeepLink.AUTH_OLD, MeetingFunctionActivity.class);
        map.put(DeepLink.JOY_MEETING_OLD, MeetingFunctionActivity.class);
    }

    public static void updateMeetingStatus(final Context context, final String appId, final String meetingId, final String type, final String status, final LoadDataCallback<Object> callback) {
        if (isSaaS){
            return;
        }
        AppUtils.getSdkToken(context, appId, new AppSdkTokenBean.IGetTokenCallback() {
            @Override
            public void callback(String result) {
                ApiResponse<TokenInfoBean.Content> response = ApiResponse.parse(result, TokenInfoBean.Content.class);
                if (response.isSuccessful()) {
                    TokenInfoBean.Content content = response.getData();
                    postNoticeData(context, meetingId, type, status, content.third_token, content.third_timestamp, callback);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                }
            }
        });
    }

    private static void postNoticeData(final Context context, final String meetingId, final String type, final String status, final String thirdToken, final String timestamp, final LoadDataCallback<Object> callback) {
        if (isSaaS){
            return;
        }
        try {
            RequestHandler.getHandlerInstance().postNoticeData(context, PreferenceManager.UserInfo.getUserName(), PreferenceManager.UserInfo.getEncryptedTenantCode(), meetingId, type, status, thirdToken, timestamp, new INetTasksListener() {
                @Override
                public void onLoading(long l, long l1) {
                    MELogUtil.localD(TAG, "onLoading: ");
                }

                @Override
                public void onSuccess(Object o, int i) {
                    MELogUtil.localD(TAG, "onSuccess: ");
                    callback.onDataLoaded(o);
                }

                @Override
                public void onFailure(Throwable throwable, String s, int i) {
                    MELogUtil.localD(TAG, "onFailure: ");
                    callback.onDataNotAvailable(s, i);
                }

                @Override
                public void onTaskStart() {
                    MELogUtil.localD(TAG, "onTaskStart: ");
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
