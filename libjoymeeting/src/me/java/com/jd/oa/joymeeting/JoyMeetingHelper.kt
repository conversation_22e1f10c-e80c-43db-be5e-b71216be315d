package com.jd.oa.joymeeting

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.text.TextUtils
import cn.cu.jdmeeting.jme.base.JoyMeetingSDKHelper
import cn.cu.jdmeeting.jme.handler.JoyMAudioCheckCallBack
import cn.cu.jdmeeting.jme.handler.JoyMAudioCheckInterface
import cn.cu.jdmeeting.jme.handler.JumpCalenderSchedulePageListener
import cn.cu.jdmeeting.jme.ui.activity.MeetingDetailActivity
import cn.cu.jdmeeting.jme.ui.activity.MyMeetingActivity
import cn.cu.jdmeeting.jme.utils.PreferencesUtils
import com.chenenyu.router.Router
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.AppBase
import com.jd.oa.abilities.api.OpennessApi
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.configuration.TenantConfigBiz.getCollaborativelyApps
import com.jd.oa.im.listener.Callback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.TabletUtil
import com.zipow.videobox.InMeetingSettingsActivity
import com.zipow.videobox.MeetingInfoActivity
import com.zipow.videobox.PListActivity
import io.reactivex.android.schedulers.AndroidSchedulers
import us.zoom.sdk.MeetingActivity
import java.util.concurrent.TimeUnit

object JoyMeetingHelper {

    val isSaaS = MultiAppConstant.isSaasFlavor()

    fun initYunLian() {
        if(isSaaS){
            return
        }
        try {
            PreferencesUtils.init(AppBase.getAppContext())
            JoyMeetingSDKHelper.getInstance().applicationContext = AppBase.getAppContext()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //JoyMeeting日志输出接口
    fun setCuLogMsgOutputListener() {
        if (isSaaS) {
            return
        }
        JoyMeetingSDKHelper.getInstance().setCuLogMsgOutputListener { s ->
            if (!TextUtils.isEmpty(s)) {
                MELogUtil.localI(MELogUtil.TAG_JMET, "JoyMeeting cuLogMsgOutput $s")
            }
        }
    }

    fun getActivitiesForMultiTask(): List<Class<out Activity?>> {
        val activities: MutableList<Class<out Activity?>> = ArrayList()
        if (isSaaS) {
            return activities
        }
        activities.add(MyMeetingActivity::class.java)
        activities.add(MeetingDetailActivity::class.java)
        activities.add(MeetingActivity::class.java)
        activities.add(MeetingInfoActivity::class.java)
        activities.add(PListActivity::class.java)
        activities.add(InMeetingSettingsActivity::class.java)
        return activities
    }

    fun getActivitiesForSmallTv(): List<Class<out Activity?>> {
        val activities: MutableList<Class<out Activity?>> = ArrayList()
        if (isSaaS) {
            return activities
        }
        activities.add(MyMeetingActivity::class.java)
        activities.add(MeetingDetailActivity::class.java)
        activities.add(MeetingActivity::class.java)
        activities.add(MeetingInfoActivity::class.java)
        activities.add(PListActivity::class.java)
        activities.add(InMeetingSettingsActivity::class.java)
        return activities
    }

    fun showMinimizeMeeting() {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance().showMinimizeMeeting()
    }

    fun setJoyMAudioCheckInterface() {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance()
            .setJoyMAudioCheckInterface(object : JoyMAudioCheckInterface {
                override fun requestOpenMeeting(joyMAudioCheckCallBack: JoyMAudioCheckCallBack) {
                    MELogUtil.localV(JMAudioCategoryManager.AUDIO_MANAGER, "requestOpenMeeting---0")
                    MELogUtil.onlineV(
                        JMAudioCategoryManager.AUDIO_MANAGER,
                        "requestOpenMeeting---0"
                    )
                    val jmAudioCategoryManager = JMAudioCategoryManager.getInstance()
                    val jmeAudioCategorySet =
                        jmAudioCategoryManager.setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_MEETING)
                    if (jmeAudioCategorySet.available) {
                        MELogUtil.localV(
                            JMAudioCategoryManager.AUDIO_MANAGER,
                            "requestOpenMeeting--1---jmeAudioCategorySet=" + jmeAudioCategorySet.secret
                        )
                        MELogUtil.onlineV(
                            JMAudioCategoryManager.AUDIO_MANAGER,
                            "requestOpenMeeting--1---jmeAudioCategorySet=" + jmeAudioCategorySet.secret
                        )
                        joyMAudioCheckCallBack.agreed()
                    } else {
                        MELogUtil.localV(
                            JMAudioCategoryManager.AUDIO_MANAGER,
                            "requestOpenMeeting--2---jmeAudioCategorySet=" + jmeAudioCategorySet.secret
                        )
                        MELogUtil.onlineV(
                            JMAudioCategoryManager.AUDIO_MANAGER,
                            "requestOpenMeeting--2---jmeAudioCategorySet=" + jmeAudioCategorySet.secret
                        )
                        joyMAudioCheckCallBack.reject()
                    }
                }

                override fun meetingReleaseCallBack() {
                    val jmAudioCategoryManager = JMAudioCategoryManager.getInstance()
                    jmAudioCategoryManager.releaseJoyMeeting()
                }
            })
    }

    fun setJumpCalenderSchedulePageListener() {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance().jumpCalenderSchedulePageListener =
            JumpCalenderSchedulePageListener {
                MELogUtil.localI(MELogUtil.TAG_JMET, "jumpCalenderSchedulePageEvent")
                val activity = AppBase.getTopActivity() ?: return@JumpCalenderSchedulePageListener
                val tabDeeplink = Uri.parse(DeepLink.CALENDAR_OLD)
                Router.build(tabDeeplink).go(activity) { routeStatus, uri, s ->
                    AndroidSchedulers.mainThread().scheduleDirect({
                        val deeplink = Uri.parse(DeepLink.CALENDER_SCHEDULE)
                            .buildUpon()
                            .appendQueryParameter("routeTag", "create")
                            .appendQueryParameter("from", "joymeetingBook")
                            .build()
                        Router.build(deeplink).go(activity)
                    }, 500, TimeUnit.MILLISECONDS)
                }
            }
    }

    //设置JoyMeeting语言
    fun setAppLanguage(lan: String) {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance().setAppLanguage(lan)
    }

    fun hideShareButtonInMeeting() {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance()
            .hideShareButtonInMeeting(TabletUtil.isEasyGoEnable() && TabletUtil.isTablet())
    }

    fun setHomeFeedbackCallBackListener() {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance().setHomeFeedbackCallBackListener { activity, appId ->
            MELogUtil.localI(
                MELogUtil.TAG_JMET,
                "feedbackBtnCheckCallBack appId = " + (appId ?: "null")
            )
            if (!TextUtils.isEmpty(appId)) {
                OpennessApi.shareOnlyExpand(activity, null, appId, "")
            }
        }
    }

    fun setJoyMeetingPermissionInterface() {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance()
            .setJoyMeetingPermissionInterface { fragmentActivity, s, s1, joyMPermissionResultCallBack ->
                MELogUtil.localI(MELogUtil.TAG_JMET, "requestPermission s = " + (s ?: "null"))
                PermissionHelper.requestPermission(
                    fragmentActivity,
                    s1,
                    object : RequestPermissionCallback {
                        override fun allGranted() {
                            MELogUtil.localI(MELogUtil.TAG_JMET, "requestPermission allGranted")
                            joyMPermissionResultCallBack.agreed()
                        }

                        override fun denied(deniedList: List<String>) {
                            MELogUtil.localI(MELogUtil.TAG_JMET, "requestPermission denied")
                            joyMPermissionResultCallBack.reject()
                        }
                    },
                    s
                )
            }
    }

    //退出meeting
    fun leaveCurrentMeeting() {
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance().leaveCurrentMeeting()
    }

    /*
    * OpenContactsPageListener实现来自于之前的im_dd这个module下的JdMeetingContactSelectorImpl
    * */
    fun setOpenContactsPageListener(){
        if(isSaaS){
            return
        }
        JoyMeetingSDKHelper.getInstance().setOpenContactsPageListener { json, type/* 0 - 预约邀请，1 - 会议中邀请 */ ->
            MELogUtil.localI(MELogUtil.TAG_JMET, "openContactsPageCallBack type = $type")
            val max = 300
            var selected = Gson().fromJson<java.util.ArrayList<MemberEntityJd?>>(
                json,
                object : TypeToken<List<MemberEntityJd?>?>() {}.type
            )
            if (selected == null) {
                selected = java.util.ArrayList()
            }
            val entity = MemberListEntityJd()
            val appIds = getCollaborativelyApps()
            entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(false)
                .setConstantFilter(null).setSpecifyAppId(appIds)
                .setShowSelf(true).setOptionalFilter(selected).setShowOptionalFilter(true)
                .setMaxNum(max)
            //setConstantFilter(selected).setOptionalFilter(null).setMaxNum(max - selected.size())，需要减去已选数量
            //setConstantFilter(null).setOptionalFilter(selected).setMaxNum(max)，不减已选数量
            //setConstantFilter(selected).setOptionalFilter(null).setMaxNum(max - selected.size())，需要减去已选数量
            //setConstantFilter(null).setOptionalFilter(selected).setMaxNum(max)，不减已选数量
            val imDdService = AppJoint.service(ImDdService::class.java)
            imDdService.gotoMemberList(
                AppBase.getTopActivity(),
                100,
                entity,
                object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                    override fun onSuccess(bean: java.util.ArrayList<MemberEntityJd?>?) {
                        MELogUtil.localI(
                            MELogUtil.TAG_JMET,
                            "setSelectedContactsJsonStr bean size = " + (bean?.size
                                ?: 0) + ";type = " + type
                        )
                        JoyMeetingSDKHelper.getInstance()
                            .setSelectedContactsJsonStr(Gson().toJson(bean), type)
                    }

                    override fun onFail() {}
                })
        }
    }

    fun compressedJoyMeetingAndZoomLogFiles(
        context: Context,
        listener: MyCompressedLogCompleteListener
    ) {
        if(isSaaS){
            return
        }
        try {
            JoyMeetingSDKHelper.getInstance()
                .compressedJoyMeetingAndZoomLogFiles(context) { s ->
                    listener.compressedLogFilesPath(s)
                }
        } catch (ignored: java.lang.Exception) {
        }
    }

    interface MyCompressedLogCompleteListener {
        fun compressedLogFilesPath(var1: String?)
    }

}