package com.jd.oa.joymeeting;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.Nullable;
import androidx.arch.core.util.Function;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.Transformations;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.libjoymeeting.R;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.notification.MeetingWindowedCallback;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.ToastUtils;
import com.jdcloud.mt.me.modle.APPEvent;
import com.jdcloud.mt.me.modle.AttendCallback;
import com.jdcloud.mt.me.modle.CurrentConference;
import com.jdcloud.mt.me.modle.MeSdkCommonCallback;
import com.jdcloud.mt.me.modle.SourceType;
import com.jdcloud.mt.me.modle.SourceTypeKt;
import com.jingdong.conference.conference.model.StartConferenceType;
import com.jingdong.conference.integrate.JDMTMeService;
import com.jingdong.conference.integrate.ServiceHubLazy;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class JdMeetingServiceImpl implements JdMeetingService {
    static String TAG = "MEETING";

    @Override
    public void joinMeeting(Activity activity, String meetingId, Long meetingCode, String password, String source, JoinMeetingCallback callback) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null && canHandleMeeting()) {
            SourceType type = parseSource(source);
            AttendCallback attendCallback = null;
            if (callback != null) {
                attendCallback = new JoinMeetingCallbackAdapter(meetingCode, callback);
            }
            jdmtMeService.attend(null, meetingId, meetingCode, password, null, type, attendCallback);
        } else {
            if (callback != null) {
                callback.onFail(String.valueOf(JSErrCode.ERROR_100), "Already in the meeting");
                MELogUtil.localI(TAG, "joinMeeting fail, meetingCode: " + meetingCode + ", message: Already in the meeting");
            }
        }
        MELogUtil.localI(TAG, "joinMeeting, meetingId:" + meetingId + ", meetingCode: " + meetingCode + CommonUtils.getCallerStack());
    }

    private SourceType parseSource(String source) {
        if (source == null) return  null;
        SourceType type = null;
        if (!TextUtils.isEmpty(source) && TextUtils.isDigitsOnly(source)) {
            type = SourceTypeKt.asSourceType(Integer.parseInt(source));
        } else {
            type = SourceType.UNKNOWN;
        }
        return type;
    }

    @Override
    public void startMeeting(Activity activity) {
        if (canHandleMeeting()) {
            JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
            if (jdmtMeService != null) {
                Bundle bundle = new Bundle();
                jdmtMeService.startConference(
                        activity,
                        StartConferenceType.CREATE,
                        false,
                        false,
                        true,
                        null,
                        bundle
                );
            }
            MELogUtil.localI(TAG, "JdMeetingServiceImpl.startMeeting");
        } else {
            MELogUtil.localI(TAG, "JdMeetingServiceImpl.startMeeting canHandleMeeting false");
        }
    }

    @Override
    public void attendMeeting(Activity activity) {
        if (canHandleMeeting()) {
            JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
            if (jdmtMeService != null) {
                Bundle bundle = new Bundle();
                jdmtMeService.startConference(
                        activity,
                        StartConferenceType.JOIN,
                        false,
                        false,
                        true,
                        null,
                        bundle
                );
            }
            MELogUtil.localI(TAG, "JdMeetingServiceImpl.attendMeeting");
        }
    }

    @Override
    public boolean canHandleMeeting() {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        JDMTMeService jdmtmeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (imDdService.hasVoipCalling()) {
            ToastUtils.showToastLong(R.string.meeting_start_conference_conference_busy);
            return false;
        } else if (jdmtmeService != null && jdmtmeService.getBusy()) {
            ToastUtils.showToastLong(R.string.meeting_start_conference_conference_busy);
            return false;
        } else{
            boolean canSetAudioCategory = JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING);
            if (!canSetAudioCategory) {
                ToastUtils.showToastLong(R.string.meeting_start_conference_conference_busy);
            }
            return canSetAudioCategory;
        }
    }

    @Override
    public Pair<String, Long> getCurrentMeeting() {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            CurrentConference meeting = jdmtMeService.getCurrentMeeting().getValue();
            if (meeting != null) {
                return Pair.create(meeting.getId(), meeting.getCode());
            }
        }
        return null;
    }

    @Override
    public LiveData<Pair<String, Long>> getCurrentMeetingLiveData() {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            return Transformations.map(jdmtMeService.getCurrentMeeting(), new Function<CurrentConference, Pair<String,Long>>() {
                @Override
                public Pair<String, Long> apply(CurrentConference input) {
                    if (input != null) {
                        return Pair.create(input.getId(), input.getCode());
                    } else {
                        return null;
                    }
                }
            });
        }
        return null;
    }

    @Override
    public void signOut() {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.signOut();
            JDMeetingManager.Companion.getInstance().releaseInit();
        }
    }

    @Override
    public void needShowReJoin(FragmentActivity activity, boolean show) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.needShowReJoin(activity, show);
        }
    }

    @Override
    public void joinWithDeeplink(Activity activity, String deeplink) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null && canHandleMeeting()) {
            jdmtMeService.dealDeeplink(activity, deeplink);
        }
        MELogUtil.localI(TAG, "joinWithDeeplink, deeplink:" + deeplink + ", caller: " + CommonUtils.getCallerStack());
    }

    @Override
    public void uploadLog(Activity activity, UploadLogCallback callback) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.uploadLog(activity, new UploadLogCallbackAdapter(callback));
        }
    }

    @Override
    public void onAppStart(Activity context) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.onAppEventCallback(APPEvent.APP_START, context);
        }
    }

    @Override
    public void onAppForeground(Activity context) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.onAppEventCallback(APPEvent.APP_FRONT, context);
        }
    }

    @Override
    public void onAppBackground(Activity context) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.onAppEventCallback(APPEvent.APP_BACKGROUND, context);
        }
    }

    @Override
    public void onMeetingNotificationClick(Activity context, String string) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.handleNotificationClick(string, context);
        }
    }


    @Override
    public boolean isMeetingInFloatMode() {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            return jdmtMeService.isMeetingInFloatMode();
        }
        return false;
    }

    @Override
    public void makeMeetingFloat(MeetingWindowedCallback callback) {
        JDMTMeService jdmtMeService = ServiceHubLazy.INSTANCE.getJdmtmeService();
        if (jdmtMeService != null) {
            jdmtMeService.makeMeetingFloat(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    callback.onWindowMinimized();
                    return null;
                }
            });
        }
    }

    static class UploadLogCallbackAdapter implements MeSdkCommonCallback {

        UploadLogCallback mCallback;
        Handler mHandler;
        UploadLogCallbackAdapter(UploadLogCallback callback) {
            this.mCallback = callback;
            this.mHandler = new Handler(Looper.getMainLooper());
        }

        @Override
        public void onError(@Nullable String code, @Nullable String msg) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mCallback.onFailed(code, msg);
                }
            });
        }

        @Override
        public void onSuccess() {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    mCallback.onSuccess();
                }
            });
        }
    }

    static class JoinMeetingCallbackAdapter implements AttendCallback {
        Long meetingCode;

        JoinMeetingCallback callback;

        public JoinMeetingCallbackAdapter(Long meetingCode, JoinMeetingCallback callback) {
            this.meetingCode = meetingCode;
            this.callback = callback;
        }

        @Override
        public void onFail(long meetingCode, @Nullable String errorCode, @Nullable String message) {
            if (callback == null) return;
            if (this.meetingCode == meetingCode) {
                this.callback.onFail(String.valueOf(JSErrCode.ERROR_100), message);
            } else {
                this.callback.onFail(String.valueOf(JSErrCode.ERROR_100), "meeting code is not match");
            }
            MELogUtil.localI(TAG, "joinMeeting fail, meetingCode: " + meetingCode + ", errorCode: " + errorCode + ", message: " + message);
        }

        @Override
        public void onSuccess(long meetingCode, @Nullable String errorCode, @Nullable String errorMessage) {
            if (callback == null) return;
            if (this.meetingCode == meetingCode) {
                this.callback.onSuccess();
            } else {
                this.callback.onFail(String.valueOf(JSErrCode.ERROR_100), "meeting code is not match");
            }
            MELogUtil.localI(TAG, "joinMeeting success, meetingCode: " + meetingCode + ", errorCode: " + errorCode + ", message: " + errorMessage);
        }
    }
}