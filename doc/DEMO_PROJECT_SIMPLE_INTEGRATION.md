# Demo项目简化集成方案

## 概述

本文档描述了不使用Hilt依赖注入框架的简化集成方案。该方案采用服务定位器模式和降级处理，确保代码的健壮性和易维护性。

## 架构原则

### 单一职责原则
每个组件只负责一个功能：
- `DemoIntegrationExample`: 专门负责Demo项目的集成和使用
- 服务定位器：专门负责依赖的获取和管理
- 降级处理：确保服务不可用时的优雅处理

### DRY原则
- 不重复代码，不重复请求
- 统一的集成接口
- 复用的降级逻辑

### 缓存优先策略
- 充分利用已有数据
- 服务实例的缓存管理

### 优雅降级
- 没有数据时平滑处理
- 服务不可用时的备用方案

## 集成方式

### 1. 基本使用

```java
// 创建集成实例
DemoIntegrationExample demoIntegration = new DemoIntegrationExample(context);

// 启动Demo页面（带自动降级）
demoIntegration.launchDemoPage();

// 启动任务创建器
demoIntegration.launchTaskCreator();

// 获取任务数据流（带空值检查）
Flow<DemoTask> taskFlow = demoIntegration.getTaskUpdates();
if (taskFlow != null) {
    // 处理数据流
}
```

### 2. 快速集成

```java
// 静态方法快速启动Demo页面
DemoIntegrationExample.quickLaunchDemo(context);
```

### 3. 服务定位器扩展（未来支持）

```java
// 当Demo项目提供ServiceLocator时
public DemoIntegrationExample(Context context) {
    this.context = context;
    // 通过Demo项目的服务定位器获取实例
    this.demoNavigator = DemoServiceLocator.getDemoNavigator();
    this.demoService = DemoServiceLocator.getDemoService();
}
```

## 降级处理机制

### 导航服务降级

```java
public void launchDemoPage() {
    if (demoNavigator != null) {
        // 优先使用Demo项目的导航服务
        demoNavigator.navigateToDemo(context);
    } else {
        // 降级到直接启动方式
        quickLaunchDemo(context);
    }
}
```

### 数据服务降级

```java
public Flow<DemoTask> getTaskUpdates() {
    if (demoService != null) {
        // 服务可用时返回真实数据流
        return demoService.getLatestTask();
    } else {
        // 服务不可用时返回null，调用方需处理
        Log.w(TAG, "DemoService不可用，无法获取任务数据流");
        return null;
    }
}
```

## 项目配置

### 1. 主项目配置

在 `settings.gradle` 中：
```gradle
// 独立Demo项目集成
includeBuild '../jdme_demo_project'
```

在 `app/build.gradle` 中：
```gradle
dependencies {
    // Demo项目模块依赖（用于调试）
    implementation 'jdme_demo_project:api-demo'
    implementation 'jdme_demo_project:feature-demo'
}
```

### 2. Demo项目独立性

```
/Users/<USER>/Coding/
├── jdme_android_project/          # 主项目
└── jdme_demo_project/             # 独立Demo项目
    ├── api-demo/                  # Demo API模块
    ├── feature-demo/              # Demo功能实现模块
    └── app/                       # Demo应用模块
```

## 编译验证

验证编译是否成功：
```bash
cd /Users/<USER>/Coding/jdme_android_project
./gradlew :app:compileMeOfficialDebugJavaWithJavac --dry-run
```

## 注意事项

### 代码注释要求
- 每个生成的方法和修改的方法都需要加上详细的注释
- 有改动的类需要说明变更原因和影响

### 最小化改动
- 修改代码时，尽量少改动原本的逻辑
- 保持最小化的改动和项目的稳定性

### 项目可运行性
- 每次改完代码后要保证项目是可运行的
- 需要验证项目的可运行性

### TODO标记
- 需要手动完善的地方请加上 `//TODO:` 注释来说明注意事项

## 常见问题

### Q: 为什么不使用Hilt？
A: 为了减少框架依赖，提高项目的灵活性和可维护性。服务定位器模式更简单直接。

### Q: 如何处理服务不可用的情况？
A: 通过降级处理机制，确保在服务不可用时有备用方案，保证应用的健壮性。

### Q: 如何扩展更多的Demo功能？
A: 在 `DemoIntegrationExample` 中添加新的方法，遵循相同的空值检查和降级处理模式。

---

> **重要提示**: 这是一个演示性的集成方案，实际使用时需要根据具体业务需求进行调整和完善。 