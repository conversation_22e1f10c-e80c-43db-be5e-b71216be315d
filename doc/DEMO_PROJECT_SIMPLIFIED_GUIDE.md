# Demo项目简化双模式依赖指南

## 概述

这是一个简化的Demo项目双模式依赖方案，支持：
- **开发模式**: 使用本地includeBuild，便于实时调试
- **发布模式**: 使用Maven依赖，确保版本稳定

## 核心原理

### 统一依赖方式
app模块始终使用Maven坐标方式依赖：
```gradle
implementation "com.jd.oa.demo:api-demo:1.0.0"
implementation "com.jd.oa.demo:feature-demo:1.0.0"
```

### 自动重定向机制
通过`settings.gradle`中的`USE_LOCAL_DEMO_PROJECT`参数控制：
- `true`: includeBuild自动将Maven依赖重定向到本地项目
- `false`: 使用真实的Maven仓库依赖

## 配置文件

### gradle.properties
```properties
# Demo项目双模式依赖配置
USE_LOCAL_DEMO_PROJECT=true

# Demo项目的Maven坐标
DEMO_PROJECT_GROUP=com.jd.oa.demo
DEMO_PROJECT_VERSION=1.0.0
```

### settings.gradle
```gradle
// 动态includeBuild配置
Properties properties = new Properties()
File propertiesFile = new File(rootDir, 'gradle.properties')
if (propertiesFile.exists()) {
    propertiesFile.withInputStream { properties.load(it) }
}

def useLocalDemoProject = Boolean.parseBoolean(properties.getProperty('USE_LOCAL_DEMO_PROJECT', 'true'))

if (useLocalDemoProject) {
    println "📦 Demo项目: 使用本地includeBuild模式 (开发调试)"
    includeBuild '../jdme_demo_project'
} else {
    println "📦 Demo项目: 使用Maven依赖模式 (生产发布)"
}
```

### app/build.gradle
```gradle
dependencies {
    // Demo项目依赖 - 统一使用Maven方式
    def demoGroup = findProperty('DEMO_PROJECT_GROUP') ?: 'com.jd.oa.demo'
    def demoVersion = findProperty('DEMO_PROJECT_VERSION') ?: '1.0.0'
    
    // 始终使用Maven坐标，settings.gradle控制是否重定向到本地项目
    implementation "${demoGroup}:api-demo:${demoVersion}"
    implementation "${demoGroup}:feature-demo:${demoVersion}"
    
    println "📦 App模块: 依赖 ${demoGroup}:*:${demoVersion}"
}
```

## 使用方法

### 1. 开发模式（推荐用于日常开发）
```bash
./scripts/switch_demo_dependency.sh local
```

**特点：**
- 实时代码同步
- 支持断点调试
- 修改立即生效
- 适合功能开发和调试

### 2. 发布模式（用于正式构建）
```bash
./scripts/switch_demo_dependency.sh maven
# 或指定特定版本
./scripts/switch_demo_dependency.sh maven 1.0.1
```

**特点：**
- 使用稳定版本
- 构建结果一致
- 适合CI/CD流程
- 版本可控可追溯

## 项目结构

```
/Users/<USER>/Coding/
├── jdme_android_project/              # 主项目
│   ├── scripts/
│   │   └── switch_demo_dependency.sh  # 模式切换脚本
│   ├── gradle.properties              # 配置参数
│   ├── settings.gradle                # 动态includeBuild
│   └── app/build.gradle               # 统一Maven依赖
└── jdme_demo_project/                 # 独立Demo项目
    ├── api-demo/                      # API模块
    └── feature-demo/                  # 功能实现模块
```

## 验证方法

### 检查当前模式
查看构建输出中的提示信息：
```
📦 Demo项目: 使用本地includeBuild模式 (开发调试)
📦 App模块: 依赖 com.jd.oa.demo:*:1.0.0
```

### 验证依赖解析
```bash
./gradlew :app:dependencies --configuration implementation | grep demo
```

## 优势

1. **简化配置**: app模块只需要一种依赖方式
2. **透明切换**: 通过配置参数控制，无需修改代码
3. **开发友好**: 本地模式支持实时调试
4. **发布稳定**: Maven模式确保版本一致性
5. **维护简单**: 减少了复杂的条件判断逻辑

## 注意事项

1. 确保Demo项目位于正确路径：`../jdme_demo_project`
2. 切换模式后建议执行clean build
3. Maven模式需要确保对应版本已发布到仓库
4. 团队开发时建议统一使用相同模式

## 故障排除

### 找不到Demo项目
```
错误: 本地Demo项目不存在: ../jdme_demo_project
解决: 检查Demo项目路径是否正确
```

### Maven依赖无法解析
```
错误: Could not find com.jd.oa.demo:api-demo:1.0.0
解决: 确保Maven仓库中存在对应版本，或切换到本地模式
```

### 构建配置验证失败
```
解决: 脚本会自动恢复备份配置，检查错误信息并修复
``` 