# 核心模块负责人：
----
为了保证核心库模块的质量和有人统一管理发布（每个模块由对应的Owner管理）：
<br /> libcommon                ：田横
<br /> libui                    ：振中
<br /> libbase                  ：振中
<br /> libutils                 ：振中
<br /> libdependency            ：振中
<br /> libJni                   ：振中
<br /> libmultiservice          ：振中
<br /> Q1 : Owner的责任是什么？
<br /> A1 : Owner的责任是对核心库质量负责，但并不是说只有Owner才能修改submod库（如果业务需要，其他成员也可以对核心库做补充完善），但是最后必须经过Owner的review后提交。 
<br /> Q2 : 主工程如何使用核心模块库文件？
<br /> A2 : 主工程原则上统一使用Maven库方式依赖（ 某些重要的不公开模块，可以使用本地库文件方式依赖 ）
<br /> Q3 : 如何发布核心模块库文件？
<br /> A3 : 只有Owner可以向Maven仓储发布库文件(每次打包后versioncode加1，并将库文件存档备查，并同时在Tag仓库中存档)。
<br /> 
<br /> 
 

# 资源规范：
----
1 资源命名规范(Android+-+图片资源命名规范)：
[https://cf.jd.com/pages/viewpage.action?pageId=132088002]()

<br /> 
<br /> 

# 开发规范：
----
1 编码Java规范(Android+-+编码Java规范)：
[https://cf.jd.com/pages/viewpage.action?pageId=133463879]()

2 编码Android规范(Android+-+编码Android规范) ：
[https://cf.jd.com/pages/viewpage.action?pageId=133463937]()

4 Git开发流程规范（Git分支命名和使用方法的约定（Android））

5 使用Lint检查无效资源 （安装libCodeCheckPlugin插件）

<br /> 
<br /> 

# 工程目录规范
----
1 主工程目录结构规范

2 基础层工程目录结构规范（submodules, third_party）
<br />【模块名称】: 命名规则libxxxx
<br /> -----目录结构如下----------
<br /> &nbsp;&nbsp;&nbsp;&nbsp; 
doc : 存放notice.md文档，说明模块的基本使用方法，和注意要点，以及重要的更改记录。
<br /> &nbsp;&nbsp;&nbsp;&nbsp; 
libs: 存放模块依赖的lib库
<br /> &nbsp;&nbsp;&nbsp;&nbsp; src : 代码文件
<br /> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cn.com.libxxxx : 自己开发代码的包名（开源代码包名不变）
<br /> 注释1: libxxx跟目录下必须包含 LibConfigs_xxxx.java : 设置模块相关的配置信息
<br /> 注释2: libxxx跟目录下必须包含 LibConst_xxxx.java : 设置模块相关的常量信息

<br />
<br /> 

# Gradle依赖注释规范：
----
1 独立模块中的模块依赖配置、注释规范 ：

```
    compile fileTree(dir: 'libs', include: ['*.jar'])   // 【说明：为了兼容低版本Gradle暂时使用compile】

    /// google sdk dependencies     【说明：系统SDK提供的依赖库，示例如下】
    // support package
    compile THIRDPARTY_COMPILE_SUPPORT.cardview     // 【一般分类XXXX_COMPILE_SUPPORT, XXXX_COMPILE_COMMON,在config_xxxx.gradle中配置】

    /// subbusiness dependencies    【说明：子业务模块依赖，以源码或库形式提供依赖，示例如下】
    compile project(':libmia')      // 语音助手业务库 【说明，每个单行依赖不强制添加注释，注释格式 [模块功能]-[添加原因]-[特殊描述] 】

    /// submodules dependencies     【说明：核心模块提供的依赖库，以源码或库形式提供依赖，示例如下】
    compile project(':libcommon')   // 核心common库-提供核心模块的全部功能

    /// third_party dependencies    【说明：非核心模块提供的依赖库，以源码或库形式提供依赖，示例如下】
    // === auto-value   【说明，多行依赖必须在开始和结束行将添加注释，注释格式： // === [模块功能]-[添加原因]-[特殊描述] 】
    compileOnly "com.google.auto.value:auto-value:1.5"
    apt         "com.google.auto.value:auto-value:1.5"
    // === auto-value

    /// other dependencies
```
<br /> 
<br /> 

# 项目工程规范：
----




<br /> 
<br /> 

# 提交注释规范：
----
```
提交人 : [必填]
修改点 : [必填]
CodeReview: [选择填写]
注释：[选择填写]
```
<br /> 
<br /> 

# Trouble Shooting：
----
1 JDME在部分手机上无法启动，需要关闭Instant Run功能。

<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 
<br /> 

































































    /// google sdk dependencies
    // support package

    /// subbusiness dependencies

    /// submodules dependencies

    /// third_party dependencies

    /// other dependencies
