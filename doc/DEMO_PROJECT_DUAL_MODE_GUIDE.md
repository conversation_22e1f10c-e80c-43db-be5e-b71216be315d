# Demo项目双模式依赖指南

## 概述

本项目支持Demo项目的两种依赖模式，以满足不同场景的需求：

### 🔧 开发模式 (Local Mode)
- 使用`includeBuild`本地项目依赖
- 实时代码同步，便于调试和开发
- 适用于开发阶段的功能迭代

### 📦 发布模式 (Maven Mode)  
- 使用Maven仓库的已发布版本
- 确保构建的一致性和可重现性
- 适用于生产环境和正式发布

## 配置参数

所有配置都在`gradle.properties`文件中：

```properties
# Demo项目依赖配置
USE_LOCAL_DEMO_PROJECT=true           # 是否使用本地项目（true=开发模式，false=发布模式）
DEMO_PROJECT_GROUP=com.jd.oa.demo     # Maven群组ID
DEMO_PROJECT_VERSION=1.0.0            # Maven版本号
```

## 使用方式

### 方式一：手动配置

#### 切换到开发模式
```properties
# gradle.properties
USE_LOCAL_DEMO_PROJECT=true
```

#### 切换到发布模式
```properties
# gradle.properties
USE_LOCAL_DEMO_PROJECT=false
DEMO_PROJECT_VERSION=1.0.1  # 指定要使用的版本
```

### 方式二：使用脚本（推荐）

#### 安装脚本
```bash
# 确保脚本有执行权限
chmod +x scripts/switch_demo_dependency.sh
chmod +x scripts/publish_demo_project.sh
```

#### 切换依赖模式
```bash
# 切换到开发模式
./scripts/switch_demo_dependency.sh local

# 切换到发布模式（使用默认版本）
./scripts/switch_demo_dependency.sh maven

# 切换到发布模式（指定版本）
./scripts/switch_demo_dependency.sh maven 1.0.1
```

## 发布流程

### 1. 开发阶段
使用开发模式进行功能开发和调试：

```bash
# 确保使用开发模式
./scripts/switch_demo_dependency.sh local

# 进行开发和调试
# ... 修改Demo项目代码 ...

# 验证构建
./gradlew build
```

### 2. 发布准备
当Demo项目功能稳定后，发布到Maven仓库：

```bash
# 发布到本地Maven仓库（测试用）
./scripts/publish_demo_project.sh 1.0.1 local

# 发布到远程Maven仓库（正式发布）
./scripts/publish_demo_project.sh 1.0.1 remote
```

### 3. 使用已发布版本
切换到Maven模式使用已发布的稳定版本：

```bash
# 切换到Maven模式
./scripts/switch_demo_dependency.sh maven 1.0.1

# 验证构建
./gradlew build
```

## 构建行为差异

### 开发模式构建
```
📦 Demo项目: 使用本地includeBuild模式 (开发调试)
📦 App模块: 使用本地Demo项目依赖

> Configure project :jdme_demo_project
> Configure project :jdme_demo_project:api-demo  
> Configure project :jdme_demo_project:feature-demo
```

### 发布模式构建
```
📦 Demo项目: 使用Maven依赖模式 (生产发布)
📦 App模块: 使用Maven Demo项目依赖 com.jd.oa.demo:*:1.0.1

> Task :app:downloadDependencies
```

## 项目结构

```
/Users/<USER>/Coding/
├── jdme_android_project/              # 主项目
│   ├── scripts/
│   │   ├── switch_demo_dependency.sh  # 依赖模式切换脚本
│   │   └── publish_demo_project.sh    # Demo项目发布脚本
│   ├── gradle.properties              # 包含依赖配置参数
│   ├── settings.gradle                # 动态includeBuild配置
│   └── app/build.gradle               # 动态依赖配置
└── jdme_demo_project/                 # 独立Demo项目
    ├── build.gradle                   # 包含Maven发布配置
    ├── api-demo/                      # API模块
    └── feature-demo/                  # 功能实现模块
```

## 脚本详细说明

### switch_demo_dependency.sh
**功能**: 在本地开发模式和Maven依赖模式之间切换

**特性**:
- ✅ 自动备份和恢复配置
- ✅ 构建验证和错误回滚
- ✅ 彩色输出和详细日志
- ✅ 路径存在性检查

**使用示例**:
```bash
# 显示帮助
./scripts/switch_demo_dependency.sh --help

# 切换到本地开发模式
./scripts/switch_demo_dependency.sh local

# 切换到Maven模式，使用默认版本
./scripts/switch_demo_dependency.sh maven

# 切换到Maven模式，指定版本
./scripts/switch_demo_dependency.sh maven 1.0.2
```

### publish_demo_project.sh
**功能**: 构建并发布Demo项目到Maven仓库

**特性**:
- ✅ 支持本地和远程仓库发布
- ✅ 自动版本号同步
- ✅ 构建验证和发布确认
- ✅ 详细的错误诊断

**使用示例**:
```bash
# 显示帮助
./scripts/publish_demo_project.sh --help

# 发布到本地仓库，使用默认版本
./scripts/publish_demo_project.sh

# 发布到本地仓库，指定版本
./scripts/publish_demo_project.sh 1.0.1

# 发布到远程仓库
./scripts/publish_demo_project.sh 1.0.1 remote
```

## Maven仓库配置

### 本地仓库
- 路径: `~/.m2/repository`
- 用途: 本地测试和验证
- 无需额外配置

### 远程仓库
- 地址: `http://artifactory.jd.com/libs-releases-local`
- 认证: 使用`gradle.properties`中的`artifactory_user`和`artifactory_password`
- 用途: 正式发布和团队共享

## 最佳实践

### 1. 开发工作流
```bash
# 开始新功能开发
./scripts/switch_demo_dependency.sh local

# 开发和测试
# ... 编码 ...

# 功能完成后发布
./scripts/publish_demo_project.sh 1.0.1 local

# 验证Maven版本
./scripts/switch_demo_dependency.sh maven 1.0.1
./gradlew build

# 正式发布
./scripts/publish_demo_project.sh 1.0.1 remote
```

### 2. CI/CD集成
```yaml
# 示例CI配置
stages:
  - build-dev:
      script: 
        - ./scripts/switch_demo_dependency.sh local
        - ./gradlew build
        
  - publish-demo:
      script:
        - ./scripts/publish_demo_project.sh $VERSION remote
        
  - build-release:
      script:
        - ./scripts/switch_demo_dependency.sh maven $VERSION  
        - ./gradlew assembleRelease
```

### 3. 版本管理
- **开发版本**: 使用`-SNAPSHOT`后缀，如`1.0.1-SNAPSHOT`
- **发布版本**: 使用标准语义化版本，如`1.0.1`
- **热修复版本**: 使用补丁版本，如`1.0.1-hotfix.1`

## 故障排除

### 常见问题

#### 1. 本地Demo项目路径不存在
**错误**: `本地Demo项目不存在: /Users/<USER>/Coding/jdme_demo_project`

**解决**: 
```bash
# 检查Demo项目是否在正确位置
ls -la ../jdme_demo_project

# 如果不存在，请确保Demo项目已正确迁移
```

#### 2. Maven依赖下载失败
**错误**: `Could not resolve com.jd.oa.demo:api-demo:1.0.1`

**解决**:
```bash
# 确认版本是否已发布
./scripts/publish_demo_project.sh 1.0.1 local

# 检查本地仓库
ls ~/.m2/repository/com/jd/oa/demo/
```

#### 3. 权限错误
**错误**: `permission denied: ./scripts/switch_demo_dependency.sh`

**解决**:
```bash
# 添加执行权限
chmod +x scripts/*.sh
```

#### 4. 远程仓库认证失败
**错误**: `authentication failed for artifactory`

**解决**:
```bash
# 检查gradle.properties中的认证配置
grep -E "(artifactory_user|artifactory_password)" gradle.properties

# 确保凭据正确且有权限访问仓库
```

## 注意事项

### ⚠️ 重要提醒

1. **版本一致性**: 确保`gradle.properties`中的`DEMO_PROJECT_VERSION`与实际发布的版本一致

2. **构建缓存**: 切换模式后建议清理构建缓存：
   ```bash
   ./gradlew clean
   rm -rf ~/.gradle/caches/
   ```

3. **团队协作**: 团队成员应使用相同的依赖模式，避免构建环境不一致

4. **发布前验证**: 正式发布前务必在Maven模式下完整测试

5. **备份重要配置**: 脚本会自动备份，但建议手动备份重要的配置文件

### 📋 TODO事项

- [ ] 集成到CI/CD流水线
- [ ] 添加版本号自动递增功能  
- [ ] 支持多环境（dev/staging/prod）配置
- [ ] 添加依赖冲突检测
- [ ] 实现自动化测试验证

---

> **💡 提示**: 这套双模式依赖方案能够很好地平衡开发效率和发布稳定性，建议团队根据实际情况制定相应的使用规范。 