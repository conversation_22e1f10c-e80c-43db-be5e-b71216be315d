# Demo项目独立部署与集成指南

## 概述

Demo项目现在是一个完全独立的Android项目，位于主项目外部的平级目录。这种架构设计带来以下优势：

- **物理隔离**：Demo项目有自己独立的Gradle配置，可以单独构建和测试
- **逻辑集成**：主项目可以通过`includeBuild`机制依赖Demo项目的模块
- **开发独立性**：Demo项目可以独立开发、测试和迭代
- **架构展示**：作为新架构的最佳实践样板

## 项目结构

```
/Users/<USER>/Coding/
├── jdme_android_project/          # 主项目
│   ├── app/
│   ├── lib_common/
│   ├── lib_ui/
│   ├── settings.gradle             # 包含对demo项目的includeBuild
│   └── ...
└── jdme_demo_project/              # 独立Demo项目
    ├── api-demo/                   # Demo API模块
    ├── feature-demo/               # Demo功能实现模块  
    ├── app/                        # Demo应用模块
    ├── settings.gradle
    ├── build.gradle
    └── README.md
```

## Demo项目独立使用

### 1. 直接构建Demo项目

```bash
cd /Users/<USER>/Coding/jdme_demo_project
./gradlew build
```

### 2. 运行Demo应用

```bash
./gradlew :app:installDebug
```

### 3. 运行单元测试

```bash
./gradlew test
```

## 主项目集成Demo项目

### 1. 配置说明

主项目的`settings.gradle`中已配置：

```gradle
// Demo项目现在是独立项目，位于与主项目平级的目录中
// 可以通过includeBuild方式集成用于调试和测试
includeBuild '../jdme_demo_project'
```

### 2. 依赖配置

在主项目的`app/build.gradle`中添加依赖：

```gradle
dependencies {
    // Demo项目模块依赖
    implementation 'jdme_demo_project:api-demo'      // Demo API接口
    implementation 'jdme_demo_project:feature-demo'  // Demo功能实现
    
    // ... 其他依赖
}
```

### 3. 代码集成示例

参考 `app/src/main/java/com/jd/oa/example/DemoIntegrationExample.java`：

```java
public class DemoIntegrationExample {
    
    private final Context context;
    private final DemoNavigator demoNavigator;
    private final DemoService demoService;
    
    public DemoIntegrationExample(Context context) {
        this.context = context;
        // 通过Demo项目的服务定位器获取实例
        // this.demoNavigator = DemoServiceLocator.getDemoNavigator();
        // this.demoService = DemoServiceLocator.getDemoService();
        
        // 当前为演示版本，实际依赖为null
        this.demoNavigator = null;
        this.demoService = null;
    }
    
    // 使用Demo项目的API（带降级处理）
    public void launchDemoPage() {
        if (demoNavigator != null) {
            demoNavigator.navigateToDemo(context);
        } else {
            // 降级到直接启动方式
            quickLaunchDemo(context);
        }
    }
    
    public Flow<DemoTask> getTaskUpdates() {
        if (demoService != null) {
            return demoService.getLatestTask();
        } else {
            return null; // 服务不可用
        }
    }
}
```

## 开发工作流

### 1. Demo项目开发

```bash
# 切换到Demo项目
cd /Users/<USER>/Coding/jdme_demo_project

# 独立开发和测试
./gradlew build
./gradlew :app:installDebug

# 发布新版本
./gradlew publish  # (如果配置了发布任务)
```

### 2. 主项目集成测试

```bash
# 在主项目中测试Demo集成
cd /Users/<USER>/Coding/jdme_android_project

# 验证Demo模块可见性
./gradlew :jdme_demo_project:projects

# 构建主项目(包含Demo依赖)
./gradlew :app:assembleDebug
```

### 3. 并行开发支持

由于Demo项目独立，可以支持：

- **不同团队**并行开发主项目和Demo项目
- **不同版本**的Demo项目用于不同环境
- **独立CI/CD**流水线用于Demo项目

## 依赖注入配置

### Demo项目暴露的服务

通过`api-demo`模块暴露：

```kotlin
// 导航服务接口
interface DemoNavigator {
    fun launchDemoActivity(context: Context)
    fun launchTaskCreator(context: Context)
}

// 数据服务接口  
interface DemoService {
    fun getLatestTask(): Flow<DemoTask?>
}
```

### 主项目使用方式

```java
// 通过服务定位器使用（推荐方式）
DemoIntegrationExample demoIntegration = new DemoIntegrationExample(context);
demoIntegration.launchDemoPage();

// 或通过静态方法快速使用
DemoIntegrationExample.quickLaunchDemo(context);
```

## 版本管理策略

### Demo项目版本

- Demo项目有独立的版本号管理
- 可以独立发布和更新
- 支持语义化版本控制

### 依赖版本锁定

在主项目中可以指定Demo项目的特定版本：

```gradle
// 如果需要锁定版本
implementation 'jdme_demo_project:api-demo:1.0.0'
implementation 'jdme_demo_project:feature-demo:1.0.0'
```

## 注意事项

### 1. 路径依赖

- Demo项目必须位于主项目的上级目录的`jdme_demo_project`文件夹中
- 如果移动位置，需要更新主项目`settings.gradle`中的路径

### 2. 构建顺序

- Gradle会自动处理构建顺序
- Demo项目会在主项目之前构建

### 3. 开发调试

- 可以在IDE中同时打开两个项目
- 支持跨项目的代码跳转和调试
- 修改Demo项目代码后，主项目会自动感知变化

## 架构扩展

这种独立项目的模式可以扩展到其他功能模块：

```
/Users/<USER>/Coding/
├── jdme_android_project/          # 主项目
├── jdme_demo_project/              # Demo独立项目
├── jdme_calendar_project/          # 日历独立项目 (未来)
├── jdme_meeting_project/           # 会议独立项目 (未来)
└── jdme_chat_project/              # 聊天独立项目 (未来)
```

每个独立项目都遵循相同的架构模式，实现：
- 完全的模块化
- 独立的开发和测试
- 统一的架构规范
- 灵活的集成方式 