
# Deep Link实现说明
DeepLink是通过链接启动原生应用的方法。更详细地说是通过映射预定义行为到唯一的链接上，让用户无缝跳转到相关内容页面。

## Deep Link定义

在设计深度链接结构时，最佳做法是通过独特的Scheme 加 路由参数(路径和查询参数)，来代表应用支持的自定义操作， 除非有特殊需求，否则建议使用类如下简单的链接结构：
>scheme://host/path?querystring

* scheme ：协议名称。具体定义时为App唯一的名称，避免与其他应用冲突
* host：主机名字
* path：路径的路径
* query string: 查询参数，类似于http中的GET请求，用&分割参数，用=分割key、value（value必须用url编码）


## 京me Deep Link

Deep Link分为两个部分，外部应用跳转到京ME，和京ME内部跨模块跳转。  
外部跳转的监听修改可以搜索，这里使用的是是系统的标准监听方式，并没有在Google Play中注册，本文不讨论
```
android:scheme="open.jdme"
```
京me的scheme为jdme开头，具体的定义可以参考[Deeplink文档](https://cf.jd.com/pages/viewpage.action?pageId=481851338)  
和iOS通用的逻辑可以参考上面的网址，下面说的都是Android的独有的一些实现细节。  
新的deep link基本上都是/jm开头，否则就是老板本的deep link，为了兼容，只要是出现过的deep link都需要保留支持。  

### 测试需要注意的页面：
1. 升级弹窗页面，详情按钮，打开网页
2. 京东互通主界面，参加调研界面
3. 打开流程中心，我的申请，申请详情，批准，批准详情
4. 主界面打开时自动通过deep link跳转到tab
5. Mia打开网页
6. 我的页面：绑定邮箱，京东绑定，打卡历史，假期，头像挂件，意见反馈，钱包
7. 个人信息页面，组织结构页面
8. 发票拍照
9. 浏览器在线文件预览
10. push模块，通过类型区分，打卡，司龄。还有`jdme://auth/`这种类型的
11. 云打印. 责任安全书
12. 跳转第三方：Mia主界面. 休假申请. 鲸盘. 身边. 数科数据站. 黄金眼. 数科-玲珑
13. 应用201801100184的埋点数据是否正常上传
14. 咚咚中的一些跳转：快速创建待办，投票，打开通知详情，会议，顶部的邮箱按钮，文档，名片，日历
15. 扫一扫：跳转福利页
16. 工作台的一些跳转
17. RN和JS的Sdk的跳转能力
18. 一些模块跳转：登录. 打车. wifi认证. 工作台. 应用中心. 待办
19. 显示用户信息二维码页面

### 内部Deep Link实现原理

整个路由使用的是第三方开源控件[chenenyu/Router](https://github.com/chenenyu/Router)，可以将一个字符串直接映射到一个Activity或者一个Fragment，也可以获取一个Intent备用，具体用法可以参考官网。  
目前使用的版本暂时是1.5.2，所以要注意的是，官网的注册方式不适用，添加新的模块需要参考下文。  
目前直接使用Deep Link的值来做路由的Key，不做任何转化。也就是说，一个Deep Link链接，除去后面的query string参数后，将会和实际要跳转的Activity等一起作为Key-map存入控件自动生成等路由表。表现形式如下：
```java
    map.put("jdme://jm/biz/appcenter", WebFragment2.class);
    map.put("jdme://jm/biz/appcenter/{app_id}", WebFragment2.class);
    map.put("jdme://web", WebFragment2.class);
    map.put("jdme://web/{app_id}", WebFragment2.class);
    map.put("jdme://jm/sys/browser", WebFragment2.class);
```
##### 添加新的支持路由等模块
1. 在新模块的gradle中添加：
```
    implementation('com.chenenyu.router:router:1.5.2')
    kapt 'com.chenenyu.router:compiler:1.5.1'
```
1. 搜索主APP的代码块，在里面添加新的模块：
```
Router.initialize(new Configuration.Builder()
                // 调试模式，开启后会打印log
                .setDebuggable(BuildConfig.DEBUG)
                // 模块名，每个使用Router的module都要在这里注册
                .registerModules("app", "common", "around", "mae-biz-mia", "cucloud",
                        "appcenter", "login", "libscanner", "libvehicle", "libtravel",
                        "libjdreact", "workbench","im_dd", "wifiauth", "lib_me_flutter")
                .build());
```
  
  
##### 添加新的路由页面
可以参考`DeepLink.java`类，所有的路由理论上字符串部分都要在这里定义，主要是为了以后便于管理。而且开头等scheme部分需要单独组合，便于以后统一替换scheme，例子如下：
```
    //MeTaskFlutterActivity
    public static final String JOY_WORK_LIST = JDME + "jm/page_joywork_list";
    public static final String JOY_WORK_CREATE = JDME + "jm/page_joywork_taskcreate";
    public static final String JOY_WORK_DETAIL = JDME + "jm/jm/page_joywork_detail";
    //福利券页面
    public static final String BENEFIT_OLD = JDME + "appcenter/benefit";
```

1. 如果跳转到自己的UI代码，使用注解来处理
```
@Route({DeepLink.APP_CENTER, DeepLink.APP_CENTER_ID, WEB, WEB_ID, BROWSER})
```
1. 如果跳转到第三方AAR库中的UI，使用`CustomRouteTable.java`来添加
  
  
##### 京ME对路由对特殊改造
1. 自定Matcher，RestfulParamsMatcher里面增加了对{ID}这种参数对支持，FragmentMatcher增加了精确匹配优先，比方`jdme://jm/biz/appcenter/{app_id}`和`jdme://jm/biz/appcenter/browser`同时出现，会优先匹配没有动态参数的后者。
```
        Router.registerMatcher(new FragmentMatcher(0x1010));
        Router.registerMatcher(new FunctionActivityMatcher(0x1001));
        Router.registerMatcher(new RestfulParamsMatcher(0x1001));
```
2. 自定RouteInterceptor，如果有特殊逻辑可以在这里面找，AppCenter的先联网获取信息然后才打开对应类型的应用也在这里处理。
```        
           Router.addGlobalInterceptor(new DeepLinkPageEventInterceptor());
           Router.addGlobalInterceptor(new X5Interceptor());
           Router.addGlobalInterceptor(new NetDiskInterceptor());
           Router.addGlobalInterceptor(new MiaInterceptor());
           Router.addGlobalInterceptor(new DongdongDeeplinkInterceptor());
           // 开线程初始化RouteConfig，里面有反射
           //Schedulers.io().scheduleDirect(new RouteConfigInitRunnable(ctx));
           Router.addGlobalInterceptor(new RedpacketInterceptor());
           Router.addGlobalInterceptor(new AppCenterInterceptor());
           Router.handleRouteTable(new CustomRouteTable(ctx));
```
  
  
  
##### PUSH模块的一些逻辑
Push过来的url有以下几种：
1. 有deep link参数，包含auth，如：`jdme://auth/XXX`，走先联网授权然后跳转。
1. 有deep link参数，不包含auth，直接打开。
1. 没有参数，根据类型打开，不过其中的'司龄'类型通过Deep Link打开。

