// 统一的配置在这里

//def myAppId
//if (isAppModule.toBoolean()) {
//    myAppId = 'com.jd.oa'
//} else {
//    myAppId = ""
//}

ext {
    //Tinker基准版本名字
    TINKER_BASE_APK_NAME = project.TINKER_BASE_APK_NAME

    //秘钥配置
    STORE_FILE = project.STORE_FILE
    STORE_PASS = project.STORE_PASS
    KEY_ALIAS = project.KEY_ALIAS
    KEY_PASS = project.KEY_PASS

    // 基础的版本配置
    COMPILE_SDK_VERSION = 33
    BUILD_TOOLS_VERSION = "33.0.2"
    MIN_SDK_VERSION = 24
    TARGET_SDK_VERSION = 31

    flavor_dimensions = ["app", "channel"] // 产品、渠道
    product_flavors = {
        me {
            dimension 'app'
//            if (isAppModule.toBoolean()) {
            applicationId 'com.jd.oa'
//            }

            // 版本
            versionCode ME_VERSION_CODE.toInteger()
            versionName ME_VERSION_NAME

            // 应用名
            resValue "string", "me_app_name", "京东ME"
            resValue "string", "me_app_name_test", "京东ME(测试)"

            manifestPlaceholders = [
                    notificationClickAction: "com.jd.oa_notification_click",
                    noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                    PNAME                  : applicationId ?: "",
                    TencentMapKey         : "FGFBZ-N7GWQ-JCA5A-GYS2L-LASI7-L2FYE",
                    FLAVOR                 : "me",
                    SCHEME                 : "jdme"
            ]

            // 显示服务器选择器
            buildConfigField 'Boolean', 'SHOW_SERVER_SWITCHER', project.SHOW_SERVER_SWITCHER.toLowerCase()
            //渠道号
            buildConfigField 'String', 'CHANNEL_NAME', "\"$project.CHANNEL_NAME\""


//            buildConfigField "String", "versionNumber", "\"${rootProject.ext.APP2_VERSION_NAME}\""
//
//            //第三方SDK的一些配置
//            buildConfigField "int", "IM_APPID", "********" //app2的腾讯IM APPID
//            buildConfigField "String", "IM_ACCOUNTTYPE", "\"app2的腾讯IM accountype\""
//            manifestPlaceholders = [UMENG_APP_KEY      : "app2的友盟 APP KEY",
//                                    UMENG_CHANNEL_VALUE: "app2默认的渠道名",
//                                    XG_ACCESS_ID       : "app2信鸽推送ACCESS_ID",
//                                    XG_ACCESS_KEY      : "app2信鸽推送ACCESS_KEY",
//                                    QQ_APP_ID          : "app2的QQ_APP_ID",
//                                    AMAP_KEY           : "app2的高德地图key",
//                                    APPLICATIONID      : applicationId]
            //签名
//            signingConfig signingConfigs.release
        }
        saas {
            dimension 'app'
//            if (isAppModule.toBoolean()) {
            applicationId 'com.jdme.saas'
//            }


            // 版本
            versionCode SAAS_VERSION_CODE.toInteger()
            versionName SAAS_VERSION_NAME

            // 应用名
            resValue "string", "me_app_name", "京东WE"
            resValue "string", "me_app_name_test", "京东WE(测试)"

            manifestPlaceholders = [
                    notificationClickAction: "com.jd.oa_notification_click",
                    noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                    PNAME                  : applicationId ?: "",
                    TencentMapKey          : "K2FBZ-NWWLN-237FP-SXCWC-M4KQK-57FYN",
                    FLAVOR                 : "saas",
                    SCHEME                 : "jdwe"
            ]

            // 显示服务器选择器
            buildConfigField 'Boolean', 'SHOW_SERVER_SWITCHER', project.SHOW_SERVER_SWITCHER.toLowerCase()
            //渠道号
            buildConfigField 'String', 'CHANNEL_NAME', "\"$project.SAAS_CHANNEL_NAME\""

//            buildConfigField "String", "versionNumber", "\"${rootProject.ext.APP2_VERSION_NAME}\""
//
//            //第三方SDK的一些配置
//            buildConfigField "int", "IM_APPID", "********" //app2的腾讯IM APPID
//            buildConfigField "String", "IM_ACCOUNTTYPE", "\"app2的腾讯IM accountype\""
//            manifestPlaceholders = [UMENG_APP_KEY      : "app2的友盟 APP KEY",
//                                    UMENG_CHANNEL_VALUE: "app2默认的渠道名",
//                                    XG_ACCESS_ID       : "app2信鸽推送ACCESS_ID",
//                                    XG_ACCESS_KEY      : "app2信鸽推送ACCESS_KEY",
//                                    QQ_APP_ID          : "app2的QQ_APP_ID",
//                                    AMAP_KEY           : "app2的高德地图key",
//                                    APPLICATIONID      : applicationId]
            //签名
//            signingConfig signingConfigs.release
        }

        official {
            dimension 'channel'
        }
        store {
            dimension 'channel'
        }
    }

    //组件flavor
    product_flavors_module = {
        me {
            dimension 'app'
            applicationId "com.jd.oa.module"
            // 版本
            versionCode ME_VERSION_CODE.toInteger()
            versionName ME_VERSION_NAME

            manifestPlaceholders = [
                    notificationClickAction: "com.jd.oa_notification_click",
                    noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                    PNAME                  : applicationId ?: "",
                    TencentMapKey         : "FGFBZ-N7GWQ-JCA5A-GYS2L-LASI7-L2FYE",
                    FLAVOR                 : "me",
                    SCHEME                 : "jdme"
            ]
        }
        saas {
            dimension 'app'
            applicationId "com.jdme.saas.module"
            // 版本
            versionCode SAAS_VERSION_CODE.toInteger()
            versionName SAAS_VERSION_NAME

            manifestPlaceholders = [
                    notificationClickAction: "com.jd.oa_notification_click",
                    noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                    PNAME                  : applicationId ?: "",
                    TencentMapKey          : "K2FBZ-NWWLN-237FP-SXCWC-M4KQK-57FYN",
                    FLAVOR                 : "saas",
                    SCHEME                 : "jdwe"
            ]
        }
        official {
            dimension 'channel'
        }
        store {
            dimension 'channel'
        }
    }
}
