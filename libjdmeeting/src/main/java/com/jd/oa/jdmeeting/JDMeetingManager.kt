package com.jd.oa.jdmeeting

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import com.chenenyu.router.Router
import com.google.gson.Gson
import com.jd.oa.AppBase
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.business.index.AppUtils
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.libjdmeeting.R
import com.jd.oa.network.NetWorkManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.ToastUtils
import com.jdcloud.jbmeeting_ui.meeting.adapter.MembersAdapter
import com.jdcloud.jbmeeting_ui.members.NotToAttendCallOutHelper
import com.jdcloud.jbmeeting_ui.model.InvitationListsBean
import com.jdcloud.jbmeeting_ui.model.NotToAttendCallOutCallBackRequest
import com.jdcloud.jbmeeting_ui.model.ParticipantBean
import com.jdcloud.jbmeeting_ui.model.jdme.AudioPermissionEntity
import com.jdcloud.jbmeeting_ui.model.jdme.AudioType
import com.jdcloud.jbmeeting_ui.module.JBMeetingHelper
import com.jdcloud.jbmeeting_ui.module.JBNetCallback
import com.jdcloud.jbmeeting_ui.module.NetCallback
import com.jdcloud.jbmeeting_ui.module.OnRelationShipCallback
import com.jdcloud.jbmeeting_ui.module.bean.JBMeetingInfo
import com.jdcloud.jbmeeting_ui.module.bean.JBPerson
import com.jdcloud.jbmeeting_ui.module.ui.util.AcceptType
import com.jdcloud.jbmeeting_ui.util.DeviceUtils
import com.jdcloud.jbmeeting_ui.util.JBMeetingLog
import com.jdcloud.mt.me.BuildConfig
import com.jdcloud.mt.me.modle.MeetingConfig
import com.jingdong.conference.account.model.User
import com.jingdong.conference.conference.model.Conference
import com.jingdong.conference.conference.model.ConferenceUserImpl
import com.jingdong.conference.conference.model.Page
import com.jingdong.conference.conference.model.ParticipantImpl
import com.jingdong.conference.core.extension.showToast
import com.jingdong.conference.integrate.ServiceHub
import com.jingdong.conference.integrate.ServiceHubLazy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


const val TAG = "JDTCMeeting-"

const val JB_PROD = "jzbmeeting.jdcloud.com/"
const val JB_TEST = "jdmeeting-api-gray.jdcloud.com"  //jrtc-meeting-gray.jdcloud.com/
const val JB_RTC = "jrtc-interiorapi.jdcloud.com"

class JDMeetingManager private constructor() {

    var myInfo: JBPerson? = null

    //初始化标志
    private var hasInitJDTCSDK = false

    private val externallazy = ExternalServiceHelp()

    internal class ExternalServiceHelp : ServiceHub by ServiceHubLazy {}

    companion object {
        const val TAG = "JDMeetingManager"
        val instance: JDMeetingManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            JDMeetingManager()
        }
    }

    init {
        JBMeetingHelper.instance.setContextApplication(AppBase.getAppContext())
    }

    //数正云只有正式环境，预发环境用jb的
    private fun netEnvironment(): String {
        return BuildConfig.API_URI
    }

    fun initJBMeetingInNeed(sdkConfig:String? = null) {
        if (myInfo == null) {
            myInfo = JBPerson()
            if (MultiAppConstant.isSaasFlavor()) {//Saas统一用UserId
                myInfo?.pin = PreferenceManager.UserInfo.getUserId()
            } else {
                //TODO 需要根据京Me的实际情况填写
                myInfo?.pin = PreferenceManager.UserInfo.getEncryptedUseName()
            }
            myInfo?.teamId = PreferenceManager.UserInfo.getTeamId()
            myInfo?.app = PreferenceManager.UserInfo.getTimlineAppID()
            myInfo?.name = PreferenceManager.UserInfo.getUserRealName()
            myInfo?.deptName = PreferenceManager.UserInfo.getOrganizationName()
            myInfo?.avatar = PreferenceManager.UserInfo.getUserCover()
        }
        JBMeetingHelper.instance.setAppIconId(R.drawable.jdme_app_icon)
        JBMeetingHelper.instance.sdkConfig = sdkConfig
        JBMeetingHelper.instance.setLanguage(if (LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()).startsWith("en")) "en" else "zh")
        if (!hasInitJDTCSDK) {
            initParams()
        }
    }

    private fun initParams() {
        JBMeetingHelper.instance.netCallback = netJBCallback
        JBMeetingHelper.instance.imCallback = imCallback
        JBMeetingHelper.instance.initJBMeeting(AppBase.getAppContext(), MeetingConfig.meetingAPIUrl, MeetingConfig.meetingJRTCUrl)
        JBMeetingHelper.instance.setEventReportUrl(MeetingConfig.meetingReportUrl)
        myInfo?.let {
            JBMeetingHelper.instance.putMyUserInfo(it)
        }
        hasInitJDTCSDK = true
        logger("-jrtc-uniqueDeviceId:${DeviceUtils.getUniqueDeviceId()}")

        //子午线埋点
        JBMeetingHelper.instance.registerJdmaCallback(JDMeetingJdmaCallback(AppBase.getAppContext()))
    }

    private val imCallback: JBMeetingHelper.JMIMCallback by lazy {

        object : JBMeetingHelper.JMIMCallback {
            // 跳转到邀请人页面
            override fun inviteContactSelector(arguments: Any?, function: () -> Unit) {
                arguments?.let {
                    if (it !is Map<*, *> || it.isEmpty()) return
                    val inMeeting = it["inMeeting"] as Boolean
                    val groupId = it["groupId"].toString()
                    JBMeetingLog.d("inviteContactSelector", "$inMeeting  $groupId ")
                    val existUsers = it["existUsers"] as MutableList<*>
                    val existUserList = mutableListOf<User>()
                    //val list = mutableListOf<HashMap<String,String>>()
                    if (!existUsers.isEmpty()) {
                        existUsers.forEach {
                            if (it is Map<*, *>) {
                                val userId = it["userId"]?.toString() ?: ""
                                val teamid = it["teamid"]?.toString() ?: ""
                                val appid = it["appid"]?.toString() ?: ""
                                existUserList.add(ParticipantImpl(0).also {
                                    it.pin = userId
                                    it.teamId = teamid
                                    it.appId = appid
                                })
                                JBMeetingLog.d("inviteContactSelector", "$userId  $teamid  $appid")
                            }
                        }
                    }
                    //邀请人员列表
                    existUserList.let {
                        GlobalScope.launch(Dispatchers.Main) {
                            val userList: List<User>? =
                                    externallazy.externalService?.chooseParticipants(
                                            groupId,
                                            existUserList,
                                            Page.CONFERENCE,
                                            false
                                    )?.first
                            JBMeetingLog.d("inviteContactSelector", "2$userList")
                            when (inMeeting) {
                                true -> {
                                    if (userList.isNullOrEmpty()) {
                                        function.invoke()
                                        return@launch
                                    }
                                    JBMeetingLog.d(
                                            "inviteContactSelector",
                                            "3 ${Gson().toJson(it)}"
                                    )
                                    val bean = InvitationListsBean().also {
                                        it.needSendCardMsg = true
                                        it.needPushInviteMsg = true
                                        val data = mutableListOf<ParticipantBean>()
                                        userList.forEach {
                                            data.add(ParticipantBean().apply {
                                                this.mAppId = it.appId ?: ""
                                                this.mAvatar = it.avatar ?: ""
                                                this.mTeamId = it.teamId ?: ""
                                                this.mUserId = it.pin ?: ""
                                                this.mUserName = it.nickname ?: ""
                                                this.orgName = ""
                                            })
                                        }
                                        it.data = data
                                        function.invoke()
                                        NotToAttendCallOutHelper.listOfInvitedParticipants(
                                                Gson().toJson(
                                                        it
                                                )
                                        )
                                    }
                                }

                                false -> {
                                    if (userList.isNullOrEmpty()) {
                                        function.invoke()
                                        return@launch
                                    }
                                    val members = mutableListOf<JBPerson>()
                                    userList.forEach {
                                        members.add(JBPerson().apply {
                                            this.app = it.appId ?: ""
                                            this.avatar = it.avatar ?: ""
                                            this.teamId = it.teamId ?: ""
                                            this.pin = it.pin ?: ""
                                            this.name = it.nickname ?: ""
                                        })
                                    }
                                    JBMeetingHelper.instance.meetingMembers?.addAll(members)
                                    function.invoke()
                                }
                            }

                        }
                    }

                }

//                contactChooserListener?.startContactSelectorPage(list, inMeeting, JoyMeetingManager.SDK_TYPE_JB)
            }

            //会议前发送邀请消息
            override fun sendMessageBeforeMeeting(isP2p: Boolean) {
                logger("--------sendMessageBeforeMeeting")
                val list: MutableList<MeetingParticipant> = arrayListOf()
                JBMeetingHelper.instance.meetingMembers?.map {
                    val m = MeetingParticipant()
                    m.mAppId = it.app
                    m.userId = it.pin
                    m.teamId = it.teamId
                    m.mUserName = it.name
                    list.add(m)
                }
                JBMeetingHelper.instance.meetingMembers?.clear()
            }

            // 未入会邀请/取消邀请
            override fun sendMessageNotJoinMsg(conInfoList: List<NotToAttendCallOutCallBackRequest>) {
                logger("--------sendMessageNotJoinMsg")
                val sendList: MutableList<MeetingParticipant> = arrayListOf()
                for (item in conInfoList) {
                    if (item.callState == MembersAdapter.Companion.CALL_STATUS_CANCLE) {

                        // 取消
                        val meetingInfo = JBMeetingInfo()
                        meetingInfo.meetingTopic = JBMeetingHelper.instance.meetingTopic
                        meetingInfo.owner = myInfo?.name
                        meetingInfo.startTime = "${System.currentTimeMillis()}"
                        meetingInfo.meetingNumber = item.meetingId
                        meetingInfo.meetingPassword = item.password
                        val toUser = JBPerson()
                        toUser.app = myInfo?.app
                        toUser.pin = item.userId
                        toUser.teamId = myInfo?.teamId
                        meetingInfo.fromUser = toUser
                        JBMeetingHelper.instance.sendHandUpMessage(meetingInfo, AcceptType.CANCEL)
                    } else if (item.callState == MembersAdapter.Companion.CALL_STATUS_CALL) {

                        // 呼叫
                        myInfo?.let {
                            val m = MeetingParticipant()
                            m.userId = item.userId
                            m.appId = it.app
                            m.teamId = it.teamId
                            sendList.add(m)
                        }
                    }
                }
            }

            override fun shareToIMCard(infoMaps: Map<String, String>, function: () -> Unit) {
                JBMeetingLog.d("shareToIMCard", "shareToIMCard1 ")
                val meetingId = infoMaps["meetingId"].toString()
                val meetingcode = infoMaps["meetingcode"].toString().toLong()
                val suject = infoMaps["meetingTitle"].toString()
                val startTime = infoMaps["startTime"].toString()
                val meetingPsw = infoMaps["meetingPsw"].toString()
                val participantCount = infoMaps["participantCount"].toString().toInt()
                val hostPin = infoMaps["host_pin"].toString()
                val hostAppid = infoMaps["host_appid"].toString()
                val hostTeamid = infoMaps["host_teamid"].toString()
                val hostName = infoMaps["host_name"].toString()
                val host = ConferenceUserImpl(hostPin, hostTeamid, hostAppid).also {
                    it.nickname = hostName
                }
                val conference = Conference(
                        id = meetingId,
                        code = meetingcode,
                        creatorPeerId = null,
                        host = host,
                        subHost = null,
                        creator = null,
                        subject = suject,
                        password = meetingPsw,
                        type = Conference.TYPE_INSTANT,
                        status = Conference.STATUS_PROCESSING,
                        participantCount = participantCount,
                        startTime = startTime,
                        endTime = null,
                        remindTime = null,
                        setting = null,
                        history = false,
                        duration = null,
                        realJoinTime = null,
                        scheduleStartTime = null,
                        recordStatus = null,
                        recordPeerId = null,
                        businessType = null
                )
                JBMeetingLog.d("shareToIMCard", " 2 ${Gson().toJson(conference)} ")
                GlobalScope.launch(Dispatchers.Main) {
                    JBMeetingLog.d("shareToIMCard", " 3 ")
                    val shareTargets =
                            externallazy.externalService?.onShare(conference, Page.CONFERENCE)
                    shareTargets?.let {
                        JBMeetingLog.d("shareToIMCard", " 4 ${Gson().toJson(shareTargets)} ")
                    }
                    if (!shareTargets.isNullOrEmpty()) {
                        showToast(R.string.conference_detail_share_sent)
                    }
                    function.invoke()

                }
            }

            // 会议结束通知宿主
            override fun meetingFinishNotify() {
//                val service = AppJoint.service(NativeCallFlutterService::class.java)
//                service.jbMeetingFinishNotify()
            }

            override fun releaseAudioPermission(secret: String) {
                JMAudioCategoryManager.getInstance().releaseAudio(secret)
            }

            override fun canSetAudioCategory(audioType: AudioType): Boolean {
                val category: Int = convertAudioTypeTOCategory(audioType)
                return JMAudioCategoryManager.getInstance().canSetAudioCategory(category)
            }

            override fun getCurrentAudioCategory(): AudioType {
                return convertCategoryToAudioType(JMAudioCategoryManager.getInstance().currentAudioCategory)
            }

            override fun setAudioPermission(audioType: AudioType): AudioPermissionEntity {
                val category: Int = convertAudioTypeTOCategory(audioType)
                val set = JMAudioCategoryManager.getInstance().setAudioCategory(category)
                return AudioPermissionEntity(convertCategoryToAudioType(set.currentAudioCategory), set.available, set.secret)
            }

            override fun toPersonCardAct(userId: String, appId: String, teamId: String, function: () -> Unit) {
                val user = ParticipantImpl(0).also {
                    it.pin = userId
                    it.appId = appId
                    it.teamId = teamId
                }
                GlobalScope.launch(Dispatchers.Main) {
                    externallazy.externalService?.onShowUser(user)
                }
            }

            override fun jumpJoyMeeting(act: Activity, deepLink: String) {
                if (act == null || act.isFinishing || act.isDestroyed) {
                    return
                }
                if (deepLink.isEmpty()) {
                    ToastUtils.showToast("会议链接为空~")
                } else {
                    AppUtils.gainTokenAndGoPlugin(deepLink, "201803290218")
                }
            }
             override fun jumpVote(act: Activity, deepLink: String) {
                 if (!TextUtils.isEmpty(deepLink)) {
                     Router.build(deepLink).go(act)
                 }
             }

            override fun jumpWutong(act: Activity, url: String, extras: Bundle?) {
                MELogUtil.localI(TAG, "jumpWutong, url: ${url}, extras:${extras}")
                //showMainActivity(act)
                val appId = extras?.getString("appId")
                val intent = Intent(AppBase.getAppContext(), InterviewRoomActivity::class.java).apply {
                    putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2::class.java.name)
                    putExtra(WebFragment2.EXTRA_NAV, "0")
                    putExtra(WebFragment2.EXTRA_APP_ID, appId)
                    val mparam = Uri.parse(url).getQueryParameter(DeepLink.DEEPLINK_PARAM)
                    putExtra(DeepLink.DEEPLINK_PARAM, mparam)
                    putExtra(InterviewRoomActivity.JUMP_URL, url)
                }
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                AppBase.getAppContext().startActivity(intent)
            }
        }
    }

    private fun showMainActivity(activity: Activity) {
        val packageManager = activity.packageManager
        val intent = packageManager.getLaunchIntentForPackage(activity.packageName)
        activity.startActivity(intent)
    }
    private fun isAppAtTheTop(topActivity: Activity?, url: String, appId: String?): Boolean {
        if (topActivity == null) return false
        if (topActivity is FunctionActivity) {
            val webFragment = topActivity.supportFragmentManager.fragments.firstOrNull { it is WebFragment2 } as? WebFragment2
            val webAppId = webFragment?.currentAppId
            if (webAppId != null && webAppId == appId) return true
        }
        return false
    }

    private fun convertCategoryToAudioType(category: Int): AudioType {
        var audioType: AudioType = AudioType.JME_AUDIO_CATEGORY_IDLE
        when (category) {
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_IDLE -> audioType = AudioType.JME_AUDIO_CATEGORY_IDLE
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING -> audioType = AudioType.JME_AUDIO_CATEGORY_VIDEO_MEETING
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_MEETING -> audioType = AudioType.JME_AUDIO_CATEGORY_ME_MEETING
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOIP -> audioType = AudioType.JME_AUDIO_CATEGORY_VOIP
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_MEETING -> audioType = AudioType.JME_AUDIO_CATEGORY_JOY_MEETING
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_PLAY -> audioType = AudioType.JME_AUDIO_CATEGORY_VIDEO_MSG
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOICE_PLAY -> audioType = AudioType.JME_AUDIO_CATEGORY_VOICE_MSG
            JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_TV -> audioType = AudioType.JME_AUDIO_CATEGORY_ME_TV
        }
        return audioType
    }

    private fun convertAudioTypeTOCategory(audioType: AudioType): Int {
        var category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_IDLE
        when (audioType) {
            AudioType.JME_AUDIO_CATEGORY_IDLE -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_IDLE
            AudioType.JME_AUDIO_CATEGORY_VIDEO_MEETING -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_MEETING
            AudioType.JME_AUDIO_CATEGORY_ME_MEETING -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_MEETING
            AudioType.JME_AUDIO_CATEGORY_VOIP -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOIP
            AudioType.JME_AUDIO_CATEGORY_JOY_MEETING -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_MEETING
            AudioType.JME_AUDIO_CATEGORY_VIDEO_MSG -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VIDEO_PLAY
            AudioType.JME_AUDIO_CATEGORY_VOICE_MSG, AudioType.JME_AUDIO_CATEGORY_VOICE_RECORD_MSG -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_VOICE_PLAY
            AudioType.JME_AUDIO_CATEGORY_ME_TV -> category = JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_TV
        }
        return category
    }

    private val netJBCallback: NetCallback by lazy {
        object : NetCallback {
            override fun requestJRTC(
                apiMethod: String,
                param: Map<String, Any>,
                callBack: JBNetCallback<Any>,
            ) {
                NetWorkManager.getAuthorizationCode(
                        null, object : SimpleRequestCallback<String>(null, false) {
                    override fun onSuccess(info: ResponseInfo<String>) {
                        super.onSuccess(info)
                        try {
                            val result = org.json.JSONObject(info.result)
                            val statusCode = result.optInt("code")
                            if (statusCode == 0) {
                                val resultMap = hashMapOf<String, String?>()
                                val data = result.optJSONObject("data")
                                val meCode = data?.optString("code")
                                if (!meCode.isNullOrEmpty()) {
                                    JBMeetingLog.d("netJBCallback", "${myInfo?.app} ${myInfo?.teamId}")
                                    resultMap["meCode"] = meCode
                                    resultMap["appId"] = PreferenceManager.UserInfo.getTimlineAppID()
                                    resultMap["teamId"] = PreferenceManager.UserInfo.getTeamId()
                                    callBack.success(resultMap)
                                    return
                                }
                            }
                        } catch (e: java.lang.Exception) {
                            e.printStackTrace()
                        }
                        callBack.error(info.errorCode ?: "200", info.errorMessage ?: "", info.result
                                ?: "")
                    }

                    override fun onFailure(exception: HttpException, info: String) {
                        super.onFailure(exception, info)
                        callBack.error(
                                exception.exceptionCode.toString(),
                                exception.message.toString(),
                                info
                        )
                    }
                },
                        param["appKey"] as String?
                )
            }

            override fun getRelationShip(
                appId: String,
                userId: String,
                teamId: String,
                callBack: OnRelationShipCallback,
            ) {

            }

            override fun logJBMeeting(str: String) {
                logger(str)
            }

        }
    }

    private fun logger(str: String) {
        JBMeetingLog.d(TAG, "$str")
    }

    fun releaseInit() {
        hasInitJDTCSDK = false
        myInfo = null
    }
}