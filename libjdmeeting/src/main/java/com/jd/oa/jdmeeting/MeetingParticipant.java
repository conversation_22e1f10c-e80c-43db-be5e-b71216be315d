package com.jd.oa.jdmeeting;

import com.alibaba.fastjson.JSONObject;

import com.jdcloud.jbmeeting_ui.module.bean.JBPerson;

import java.io.Serializable;
import java.util.HashMap;

import java.util.Map;

import androidx.annotation.NonNull;


public class MeetingParticipant implements Serializable {
    public String mUserId;
    public String mTeamId;
    public String mAppId;
    public String mUserName;
    public String mAvatar;
    public String mAid;
    public String orgPath;
    public String orgName;
    public String unitName;

    public String titleName;
    public String deptName;
    public String superiorDeptName;

    public MeetingParticipant() {
        mUserId = "";
        mTeamId = "";
        mAppId = "";
        mUserName = "";
        mAvatar = "";
        mAid = "";
        titleName = "";
        deptName = "";
        superiorDeptName = "";
        unitName="";
    }

    public String getUserId() {return mUserId;}
    public String getTeamId() {return mTeamId;}
    public String getAppId() {return mAppId;}
    public String getUserName() {return mUserName;}
    public String getAvatar() {return mAvatar;}
    public String getAid() {return mAid;}

    public String getOrgPath() {
        return orgPath;
    }

    public String getOrgName() {
        return orgName;
    }
    public String getTitleName() {return titleName;}
    public String getDeptName() {return deptName;}
    public String getSuperiorDeptName() {return superiorDeptName;}

    public void setUserId(String userId) {mUserId = userId;}
    public void setTeamId(String teamId) {mTeamId = teamId;}
    public void setAppId(String appId) {mAppId = appId;}
    public void setUserName(String userName) {mUserName = userName;}
    public void setAvatar(String avatar) {mAvatar = avatar;}
    public void setAid(String aid) {mAid = aid;}

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public void setTitleName(String titleName) {this.titleName = titleName;}
    public void setDeptName(String deptName) {this.deptName = deptName;}
    public void setSuperiorDeptName(String superiorDeptName) {this.superiorDeptName = superiorDeptName;}

    @NonNull
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("userId = ");
        sb.append(mUserId);
        sb.append(",teamId = ");
        sb.append(mTeamId);
        sb.append(",appId = ");
        sb.append(mAppId);
        sb.append(",name = ");
        sb.append(mUserName);
        sb.append(",avatar = ");
        sb.append(mAvatar);
        return sb.toString();
    }

    public static MeetingParticipant fromMap(Map map) {
        MeetingParticipant mp = new MeetingParticipant();
        mp.mUserId = (String) map.get("pin");
        mp.mTeamId = (String) map.get("teamId");
        mp.mAppId = (String) map.get("app");
        mp.mUserName = (String) map.get("name");
        mp.mAvatar = (String) map.get("avatar");
        mp.mAid = (String)map.get("aid");
        return mp;
    }

    public static MeetingParticipant fromOtherMap(Map map) {
        Object value;
        MeetingParticipant mp = new MeetingParticipant();
        value = map.get("userId");
        mp.mUserId = value == null ? null : value.toString();
        value = map.get("teamId");
        mp.mTeamId = value == null ? null : value.toString();
        value = map.get("appId");
        mp.mAppId = value == null ? null : value.toString();
        //voip初始化名字realName
        value = map.get("name");
        if (value == null) {
            value = map.get("realName");
        }
        mp.mUserName = value == null ? null : value.toString();
        value = map.get("avatar");
        ////voip初始化头像userIcon
        if (value == null) {
            value = map.get("userIcon");
        }
        mp.mAvatar = value == null ? null : value.toString();
        return mp;
    }

    public static Map toMap(MeetingParticipant mp) {
        Map<String, String> map = new HashMap<>();
        map.put("pin", mp.mUserId);
        map.put("app", mp.mAppId);
        map.put("teamId", mp.mTeamId);
        map.put("clientType", "android");
        map.put("nickName", mp.mUserName);
        return map;
    }

    public JBPerson toJBUserInfo() {//转换为京东会议用户结构
        JBPerson person = new JBPerson();
        person.setPin(mUserId);
        person.setAvatar(mAvatar);
        person.setName(mUserName);
        person.setApp(mAppId);
        person.setTeamId(mTeamId);
        person.setTitleName(this.titleName);
        person.setDeptName(this.deptName);
        person.setSuperiorDeptName(this.superiorDeptName);
        return person;
    }

/*    public static MeetingParticipant fromParticipant(Participant bean) {
        MeetingParticipant mp = new MeetingParticipant();
        mp.mUserId = bean.getUserId();
        mp.mTeamId = bean.getTeamId();
        mp.mAppId = bean.getAppId();
        mp.mUserName = bean.getName();
        mp.mAvatar = bean.getAvatar();
        return mp;
    }

    public static List<MeetingParticipant> fromParticipantList(List<Participant> list) {
        List<MeetingParticipant> mps = new ArrayList<>();
        for (Participant bean : list) {
            mps.add(fromParticipant(bean));
        }
        return mps;
    }

    public static List<Participant> toParticipantList(List<MeetingParticipant> list) {
        List<Participant> beans = new ArrayList<>();
        for (MeetingParticipant participant : list) {
            beans.add(participant.toParticipant());
        }
        return beans;
    }*/

    @Override
    public boolean equals(Object object) {
        if (object == null) {
            return false;
        } else if (object instanceof MeetingParticipant) {
            MeetingParticipant mp = (MeetingParticipant) object;
            return mp.mTeamId.equals(this.mTeamId) && mp.mUserId.equals(this.mUserId);
        } else {
            return false;
        }
    }

    public static MeetingParticipant fromJson(JSONObject jsonObject) {
        MeetingParticipant mp = new MeetingParticipant();
        mp.mUserId = jsonObject.getString("pin");
        mp.mTeamId = jsonObject.getString("teamId");
        mp.mAppId = jsonObject.getString("app");
        mp.mUserName = jsonObject.getString("name");
//        mp.mAvatar = jsonObject.getString("");
        return mp;
    }


}
