package com.jd.oa.jdmeeting

import android.content.Context
import com.jd.oa.analyze.AnalyzeUtil
import com.jdcloud.jbmeeting_ui.open.IJBMeetingJdmaCallback
import java.util.HashMap

class JDMeetingJdmaCallback(val context: Context) : IJBMeetingJdmaCallback {

    override fun onEventPage(
        pageName: String?,
        pageParam: String?,
        eventId: String?,
        pin: String?,
        paramValue: String?,
        nextPageName: String?,
        shopId: String?,
        pageId: String?,
        params: HashMap<String, String>?
    ) {
        AnalyzeUtil.onEventPage(context, pageName, pageParam, eventId, pin,
            paramValue, nextPageName, shopId, pageId, params)
    }
    override fun onEventClick(
        pageName: String?,
        pageParam: String?,
        eventId: String?,
        pin: String?,
        paramValue: String?,
        nextPageName: String?,
        shopId: String?,
        pageId: String?,
        params: HashMap<String, String>?
    ) {
        AnalyzeUtil.onEventClick(context, pageName, pageParam, eventId, pin,
            paramValue, nextPageName, shopId, pageId, params)
    }
    override fun onEventClickWithLocation(
        pageName: String?,
        pageParam: String?,
        eventId: String?,
        pin: String?,
        paramValue: String?,
        nextPageName: String?,
        shopId: String?,
        pageId: String?,
        lat: String?,
        lon: String?,
        params: HashMap<String, String>?
    ) {
        AnalyzeUtil.onEventClickWithLocation(context, pageName, pageParam, eventId,
            pin, paramValue, nextPageName, shopId, pageId, lat, lon, params)
    }
    override fun onEventPagePV(
        pageName: String?,
        pageId: String?,
        param: HashMap<String, String>?
    ) {
        AnalyzeUtil.onEventPagePV(context, pageName, pageId, param)
    }
    override fun onEventPagePV(
        pageName: String?,
        pageParam: String?,
        pin: String?,
        pageId: String?,
        params: HashMap<String, String>?
    ) {
        AnalyzeUtil.onEventPagePV(context, pageName, pageParam, pin, pageId, params)
    }
    override fun clickPageId(
        pageId: String?,
        eventId: String?,
        pageParam: MutableMap<String, String>?,
        pin: String?,
        eventParams: HashMap<String, String>?
    ) {
        AnalyzeUtil.clickPageId(context, pageId, eventId, pageParam, pin, eventParams)
    }
    override fun eventPV(
        pageId: String?,
        pageParam: HashMap<String, String>?,
        pin: String?
    ) {
        AnalyzeUtil.eventPV(context, pageId, pageParam, pin)
    }
}