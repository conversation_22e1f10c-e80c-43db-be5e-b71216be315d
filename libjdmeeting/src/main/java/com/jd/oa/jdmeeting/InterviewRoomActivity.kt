package com.jd.oa.jdmeeting

import android.content.Intent
import android.text.TextUtils
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.AppBase
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.WebFragment2

class InterviewRoomActivity: FunctionActivity() {

    companion object {
        const val TAG = "InterviewRoomActivity"
        const val JUMP_URL = "jumpUrl"
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val jumpUrl = intent.getStringExtra(JUMP_URL)
        if (!TextUtils.isEmpty(jumpUrl)) {
            notifyAppJumpUrl(jumpUrl)
        }
    }

    private fun notifyAppJumpUrl(url: String?) {
        val intent = Intent(WebFragment2.JS_SEND_EVENT_TO_WEB)
        intent.putExtra(WebFragment2.JS_SEND_EVENT_ID, WebFragment2.NATIVE_EVENT_JUMP_URL)
        val param = HashMap<String, String?>()
        param["url"] = url
        intent.putExtra(WebFragment2.JS_SEND_EVENT_PARAM, param)
        intent.putExtra(WebFragment2.JS_SEND_EVENT_ONLY_TOP, true)
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(intent)
    }
}