apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        vectorDrawables.useSupportLibrary = true
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION


        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.jd.oa.libjdmeeting'
    lint {
        abortOnError false
    }

    configurations.all {
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.32"
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.32"
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib:1.4.32"
        resolutionStrategy.force "org.jetbrains.kotlin:kotlin-stdlib-common:1.4.32"
    }
}

dependencies {

    api fileTree(dir: 'libs', include: ['*.jar'])

//    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'
    implementation project(":common")

    //jdmeeting
    implementation 'com.google.android:flexbox:2.0.1'
    implementation 'com.google.crypto.tink:tink-android:1.5.0'
    implementation 'androidx.security:security-crypto:1.1.0-alpha03'
    implementation 'com.google.android.exoplayer:exoplayer-core:2.16.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.16.1'
    implementation('com.auth0:java-jwt:3.4.0') {
        exclude module: 'commons-codec'
    }

    def version = rootProject.ext.saasMeeting//京we 编译时依赖 implementation 在app中
    compileOnly("com.jingdong.conference:mesdk:$version")
    implementation 'com.chibatching.kotpref:enum-support:2.13.2'
}