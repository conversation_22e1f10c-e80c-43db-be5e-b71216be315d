plugins {
    id 'com.android.library'
}
apply plugin: 'com.jfrog.artifactory'
apply plugin: 'maven-publish'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.jd.oa.network'
//    compileOptions {
//        sourceCompatibility JavaVersion.VERSION_1_7
//        targetCompatibility JavaVersion.VERSION_1_7
//    }
}

dependencies {
    implementation 'com.google.android.material:material:1.3.0'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation COMPILE_COMMON.gson
    implementation "com.alibaba:fastjson:$fastjsonVersion"
    implementation "com.squareup.okhttp3:okhttp:$okhttpVersion"
    api "com.squareup.okhttp3:okhttp-sse:$okhttpVersion"  // SSE 支持库
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttpVersion"
    implementation 'com.tencent:mmkv:1.2.11'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1'
    implementation 'com.jd.jr.risk.sdk:BiometricSDK:8.1.3-0029'
    api project(path: ':libstorage')
//    implementation 'com.jd.oa:libstorage:1.1.0-SNAPSHOT'
}

ext {
    //library版本号
   version_name = '1.11.1-SNAPSHOT'
}

apply from: "../publish_to_artifactory.gradle"