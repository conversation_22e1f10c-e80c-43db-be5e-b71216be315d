package com.jd.oa.filetransfer.upload2;

import com.jd.oa.network.utils.Utils;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

public class FileSplitter {
    public static List<FileSegment> spliteFile(String tempDir,String requestId,String fileUploadUrl,File file, long sliceSize) throws IOException {
        long totalLength=file.length();
        long mod = totalLength % sliceSize;
        long num = totalLength / sliceSize;
        num = mod == 0 ? num : num + 1;

        List<FileSegment> list = new ArrayList<>();
        long currentOffset = 0;
        File tempDirFile=new File(tempDir);
        if(!tempDirFile.exists()){
            tempDirFile.mkdirs();
        }

        for (int i = 0; i < num; i++) {
            long segmentLength = Math.min(totalLength - currentOffset, sliceSize);
            FileSegment segment = new FileSegment();
            segment.setUploadId(requestId);
            segment.setSegmentFileSize(segmentLength);
            segment.setUploadUrl(fileUploadUrl);
            segment.setPartNumber(i);
            segment.setFile(file);
            segment.setTempDir(tempDir);
            segment.setOffset(currentOffset);

            currentOffset += segmentLength;
            File tempFile=writeToTempFile(segment,tempDirFile);
            segment.setSegmentFile(tempFile);
            segment.setMd5(Utils.getFileMd5(tempFile));
            list.add(segment);
        }
        return list;
    }

    private static File writeToTempFile(FileSegment segment, File tempDir) throws IOException {
        File temp = null;
        BufferedOutputStream out = null;
        RandomAccessFile randomAccessFile = null;
        try {
            temp = new File(tempDir, segment.getFile().getName() + "-" + segment.getPartNumber());
            segment.setSegmentPath(temp.getPath());

            if (!temp.getParentFile().exists()) {
                temp.getParentFile().mkdirs();
            }
            if (temp.exists()) {
                temp.delete();
            }
            temp.createNewFile();
            out = new BufferedOutputStream(new FileOutputStream(temp));
            randomAccessFile = new RandomAccessFile(segment.getFile(), "rw");
            randomAccessFile.seek(segment.getOffset());
            byte[] buffer = new byte[4096];
            int length = 0;
            int read;
            while ((read = randomAccessFile.read(buffer)) != -1) {
                if (length + read <= segment.getSegmentFileSize()) {
                    out.write(buffer, 0, read);
                    length += read;
                } else {
                    int writeLength = (int)segment.getSegmentFileSize() - length;
                    out.write(buffer, 0, writeLength);
                    length += writeLength;
                    break;
                }
            }
        } finally {
            if (out != null) {
                out.close();
            }
            if (randomAccessFile != null) {
                randomAccessFile.close();
            }
        }
        return temp;
    }

    public static class FileSegment{
        private File file;
        private File segmentFile;
        private String tempDir;

        private long total;
        private long offset;
        private long segmentFileSize;
        private int partNumber;
        private String uploadId;
        private String md5;
        private String segmentPath;

        private String uploadUrl;

        private boolean isUploaded=false;

        public void setOffset(long offset) {
            this.offset = offset;
        }

        public long getOffset() {
            return offset;
        }

        public long getTotal() {
            return total;
        }

        public void setTotal(long total) {
            this.total = total;
        }

        public long getSegmentFileSize() {
            return segmentFileSize;
        }

        public void setSegmentFileSize(long segmentFileSize) {
            this.segmentFileSize = segmentFileSize;
        }

        public File getFile() {
            return file;
        }

        public void setFile(File file) {
            this.file = file;
        }

        public int getPartNumber() {
            return partNumber;
        }

        public void setPartNumber(int partNumber) {
            this.partNumber = partNumber;
        }

        public String getUploadId() {
            return uploadId == null ? "" : uploadId;
        }

        public void setUploadId(String uploadId) {
            this.uploadId = uploadId == null ? "" : uploadId;
        }

        public String getMd5() {
            return md5 == null ? "" : md5;
        }

        public void setMd5(String md5) {
            this.md5 = md5 == null ? "" : md5;
        }

        public String getUploadUrl() {
            return uploadUrl == null ? "" : uploadUrl;
        }

        public void setUploadUrl(String uploadUrl) {
            this.uploadUrl = uploadUrl == null ? "" : uploadUrl;
        }

        public String getSegmentPath() {
            return segmentPath == null ? "" : segmentPath;
        }

        public void setSegmentPath(String segmentPath) {
            this.segmentPath = segmentPath == null ? "" : segmentPath;
        }

        public String getTempDir() {
            return tempDir == null ? "" : tempDir;
        }

        public void setTempDir(String tempDir) {
            this.tempDir = tempDir == null ? "" : tempDir;
        }

        public File getSegmentFile() {
            return segmentFile;
        }

        public void setSegmentFile(File segmentFile) {
            this.segmentFile = segmentFile;
        }

        public boolean isUploaded() {
            return isUploaded;
        }

        public void setUploaded(boolean uploaded) {
            isUploaded = uploaded;
        }
    }
}