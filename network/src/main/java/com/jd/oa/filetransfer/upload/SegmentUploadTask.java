package com.jd.oa.filetransfer.upload;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Segment;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.db.FileTransferDao;
import com.jd.oa.filetransfer.exception.UploadException;
import com.jd.oa.filetransfer.upload.api.FileService;
import com.jd.oa.filetransfer.upload.model.UploadSegment;
import com.jd.oa.filetransfer.upload.model.UploadSegmentResult;
import com.jd.oa.network.utils.Utils;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.concurrent.CountDownLatch;

import okhttp3.Call;
import okhttp3.OkHttpClient;

/**
 * 上传分片
 * Created by peidongbiao on 2018/6/20.
 */

public class SegmentUploadTask extends Task<Segment> {

    private static final String TAG = "SegmentUploadTask";

    private FileUploadManager mUploadManager;
    private final UploadSegment mSegment;

    private UploadRequest mUploadRequest;

    private final CountDownLatch mCountDownLatch;

    protected OkHttpClient mClient;

    private Call mCall;

    private FileTransferDao mDao;

    private Context mContext;

    private File mTempDir;

    private File mTempFile;

    public SegmentUploadTask(FileUploadManager fileUploadManager, UploadRequest request, UploadSegment segment, File tempDir, CountDownLatch latch) {
        super(fileUploadManager.getOkHttpClient(), fileUploadManager.getTaskDispatcher());
        this.mUploadManager = fileUploadManager;
        this.mUploadRequest = request;
        this.mContext = fileUploadManager.getContext();
        this.mClient = fileUploadManager.getOkHttpClient();
        this.mSegment = segment;
        this.mCountDownLatch = latch;
        this.mDao = FileTransferDao.get(mContext);
        this.mTempDir = tempDir;
    }

    @Override
    public void run() {
        try {
            setStatus(Task.STATUS_RUNNING);
            onStart();

            UploadSegment localUploadSegment = mDao.searchUploadSegment(mSegment.getUploadId(), mSegment.getNumber());
            if (mSegment.equals(localUploadSegment) && localUploadSegment.getStatus() == Segment.STATUS_COMPLETE) {
                //已下载完成
                finishWithLocalSegment(localUploadSegment);
                return;
            }

            File file;
            if (mSegment.getSegmentLength() == mSegment.getTotalLength()) {
                //只有一片,不用写临时文件
                file = mSegment.getFile();
            } else {
                mTempFile = writeToTempFile(mSegment, mTempDir);
                log("segment temp file: " + mTempFile.getName() + ", length:" + mTempFile.length());
                if (getStatus() != Task.STATUS_RUNNING) {
                    doOnFailure(new Exception("Segment upload failed"));
                    return;
                }
                file = mTempFile;
            }

            mSegment.setSegmentPath(file.getPath());
            final String md5 = Utils.getFileMd5(file);
            mSegment.setMd5(md5);

            mDao.insertUploadSegments(mSegment);

            this.doUpload(mSegment, file);
        } catch (Exception e) {
            e.printStackTrace();
            doOnFailure(e);
        } finally {
            mCountDownLatch.countDown();
        }
    }


    /**
     * 数据库状态会在取消网络请求触发的失败回调中更新
     */
    @Override
    public void pause() {
        if (mCall != null && !mCall.isCanceled()) {
            mCall.cancel();
        }
        setStatus(Task.STATUS_PAUSED);
        log("pause");
    }

    /**
     * 数据库状态会在取消网络请求触发的失败回调中更新
     */
    @Override
    public void cancel() {
        if (mCall != null && !mCall.isCanceled()) {
            mCall.cancel();
        }
        setStatus(Task.STATUS_CANCELED);
        log("cancel");
    }

    @Override
    protected void onFailure(@NonNull Exception e) {
        super.onFailure(e);
        log("onFailure " + e.getMessage());
    }

    private void log(String message) {
        if (mUploadManager.isPrintLog()) {
            Log.d(TAG, message);
        }
    }

    private File writeToTempFile(UploadSegment segment, File tempDir) throws IOException {
        File temp = null;
        BufferedOutputStream out = null;
        RandomAccessFile randomAccessFile = null;
        try {
            temp = new File(tempDir, segment.getFile().getName() + "-" + segment.getNumber());
            segment.setSegmentPath(temp.getPath());

            if (!temp.getParentFile().exists()) {
                temp.getParentFile().mkdirs();
            }
            if (temp.exists()) {
                temp.delete();
            }
            temp.createNewFile();
            out = new BufferedOutputStream(new FileOutputStream(temp));
            randomAccessFile = new RandomAccessFile(segment.getFile(), "rw");
            randomAccessFile.seek(segment.getOffset());
            byte[] buffer = new byte[4096];
            int length = 0;
            int read;
            while ((read = randomAccessFile.read(buffer)) != -1 && getStatus() == STATUS_RUNNING) {
                if (length + read <= segment.getSegmentLength()) {
                    out.write(buffer, 0, read);
                    length += read;
                } else {
                    int writeLength = (int)segment.getSegmentLength() - length;
                    out.write(buffer, 0, writeLength);
                    length += writeLength;
                    break;
                }
            }

            if (getStatus() != STATUS_RUNNING) {
                //temp.delete();
            }
            //Log.d(TAG, "writeToTempFile segment " + segment.getNumber() + ", offset: " + segment.getOffset() + ", length: " + segment.getSegmentLength() + ", writeLength: " + length + ", tempFileLength: " + temp.length());
        } finally {
            if (out != null) {
                out.close();
            }
            if (randomAccessFile != null) {
                randomAccessFile.close();
            }
        }
        return temp;
    }

    private void doUpload(final UploadSegment segment, File file) throws IOException {
        FileService api = mUploadManager.getFileService();
        try {
            UploadSegmentResult result = api.doUpload(mClient, segment, file, new UploadTask.SegmentUploadCallback() {
                @Override
                public void onNewCall(Call call) {
                    mCall = call;
                }
                @Override
                public void onProgressUpdate(long totalLength, long currentLength, long update, int percent) {
                    Task.Progress progress = Task.Progress.obtain();
                    progress.setTotal(totalLength);
                    progress.setCurrent(currentLength);
                    progress.setUpdate(update);
                    progress.setPercent(percent);
                    onProgressChanged(progress);
                }
            });
            doOnFinished(result);
        } catch (UploadException e) {
            doOnFailure(e);
        }
    }

    private void doOnFinished(UploadSegmentResult result) {
        deleteTempFile(mTempFile);
        setStatus(Task.STATUS_COMPLETE);
        //update database
        mSegment.setStatus(Segment.STATUS_COMPLETE);
        mDao.updateSegmentStatus(mSegment, Segment.STATUS_COMPLETE);
        onComplete(mSegment);
    }

    private void doOnFailure(Exception e) {
        deleteTempFile(mTempFile);
        setStatus(Task.STATUS_FAILED);
        mSegment.setStatus(Segment.STATUS_FAILED);
        //update database
        mDao.updateSegmentStatus(mSegment, Segment.STATUS_FAILED);
        onFailure(e);
    }

    private void finishWithLocalSegment(UploadSegment uploadSegment) {
        setStatus(Task.STATUS_COMPLETE);
        mSegment.setStatus(Segment.STATUS_COMPLETE);
        //update progress
        Progress progress = Progress.complete(uploadSegment.getSegmentLength());
        onProgressChanged(progress);
        //finish
        onComplete(uploadSegment);
    }

    private void deleteTempFile(File tempFile) {
        if (tempFile == null) return;
        tempFile.delete();
    }
}