package com.jd.oa.network.httpmanager;

import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.jd.oa.network.httpmanager.HttpManager.HEADER_GATEWAY_V2;
import static com.jd.oa.network.httpmanager.HttpManager.HEADER_KEY_GATEWAY_VERSION;

public class GatewayV2RequestBuilder {

    public void post(final Object obj, Map<String, Object> params, final SimpleRequestCallback<String> callBack, String action) {
        post(obj, null, params, callBack, action);
    }

    public void post(final Object obj, Map<String, String> headers, Map<String, Object> params, final SimpleRequestCallback<String> callBack, String action) {

        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put(HEADER_KEY_GATEWAY_VERSION, HEADER_GATEWAY_V2);

        HttpManager.post(obj, headers, params, callBack, action);
    }

    public String postSync(String action, Map<String, Object> params) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_KEY_GATEWAY_VERSION, HEADER_GATEWAY_V2);
        return HttpManager.postSync(action, headers, params);
    }

    public void upload(String action, Map<String, Object> params, Map<String, File> fileParams, SimpleRequestCallback<String> callBack) {
        Map<String,String> headers = new HashMap<>();
        headers.put(HEADER_KEY_GATEWAY_VERSION, HEADER_GATEWAY_V2);
        HttpManager.upload(action, headers, params, fileParams, callBack);
    }
}
