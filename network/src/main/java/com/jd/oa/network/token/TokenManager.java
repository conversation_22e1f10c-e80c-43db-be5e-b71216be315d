package com.jd.oa.network.token;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.R;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.storage.file.FilePathUtil;
import com.tencent.mmkv.MMKV;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicReference;


@SuppressLint("ApplySharedPref")
public class TokenManager {
    public static final String ACTION_NEW_ACCESS_TOKEN = "me.action.new.access.token";

    private static final String TAG = "TokenManager";
    private static final String NAME = "tokens";
    private static final String KEY_ACCESS_TOKEN = "key_access_token";
    private static final String KEY_REFRESH_TOKEN = "key_refresh_token";
    private static final String REFRESH_TOKEN_OVERTIME = "overtime";
    private static final String STATUS_OK = "0";
    private static final String STATUS_REFRESH_TOKEN_INVALID = "1010002";
    private static final String STATUS_EXPIRED = "1010003";
    private static final String STATUS_KICKED_OUT = "1010005";

    public static final String REFRESH_TOKEN = "account.base.refreshToken";

    private HandlerThread mHandlerThread;
    private Handler mRefreshTokenHandler;

//    private RefreshTokenListener refreshTokenListener;

    private volatile boolean mTokenRefreshing = false; // 正在刷新token
    private AtomicReference<String> mAccessToken = new AtomicReference<>();
    private AtomicReference<String> mRefreshToken = new AtomicReference<>();
    private String mEncryptedToken;

    private MMKV mMMKV;

    private ConcurrentLinkedQueue<RefreshTokenCallback> queue;

    private volatile static TokenManager sInstance;

    private TokenPreference preference;

    public static TokenManager getInstance() {
        if (sInstance == null) {
            synchronized (TokenManager.class) {
                if (sInstance == null) {
                    sInstance = new TokenManager();
                }
            }
        }
        return sInstance;
    }

//    public void setRefreshTokenListener(RefreshTokenListener refreshTokenListener) {
//        this.refreshTokenListener = refreshTokenListener;
//    }

    private TokenManager() {
        Context context = HttpManager.getContext();
//        mSharedPreferences = context.getSharedPreferences(KeyManager.PREFERENCE_KEYS_NAME, Context.MODE_PRIVATE);
        preference = TokenPreference.getInstance(context);
        mMMKV = MMKV.mmkvWithID(KeyManager.PREFERENCE_KEYS_NAME, FilePathUtil.getFilesDir(context) + "/mmkv");
        importFromSharedPreferences();

        mHandlerThread = new HandlerThread("RefreshToken");
        mHandlerThread.start();
        mRefreshTokenHandler = new Handler(mHandlerThread.getLooper());

        queue = new ConcurrentLinkedQueue<>();

        HttpManagerConfig.Logger logger = HttpManager.getConfig().getLogger();
        logger.log("TokenManager constructor: ", getMMKVValues());
    }

    private String getMMKVValues() {
        StringBuilder sb = new StringBuilder();
        String[] keys = mMMKV.allKeys();
        if (keys == null) return "[]";
        for (int i = 0; i < keys.length; i++) {
            sb.append(keys[i]);
            sb.append(" : ");
            sb.append(mMMKV.getString(keys[i], null));
            sb.append("\n");
        }
        return sb.toString();
    }

    private void importFromSharedPreferences() {
        if (!mMMKV.containsKey(KEY_REFRESH_TOKEN) || !mMMKV.containsKey(KEY_ACCESS_TOKEN)) {
            String refreshToken = preference.get(preference.KV_ENTITY_REFRESH_TOKEN);
            String accessToken = preference.get(preference.KV_ENTITY_ACCESS_TOKEN);

            mMMKV.encode(KEY_REFRESH_TOKEN, refreshToken);
            mMMKV.encode(KEY_ACCESS_TOKEN, accessToken);

            mAccessToken.set(accessToken);
            mRefreshToken.set(refreshToken);
        }
    }

    public void storeToken(@NonNull String refreshToken, @NonNull String accessToken) {
        mMMKV.encode(KEY_REFRESH_TOKEN, refreshToken);
        mMMKV.encode(KEY_ACCESS_TOKEN, accessToken);
        preference.put(preference.KV_ENTITY_REFRESH_TOKEN,refreshToken);
        preference.put(preference.KV_ENTITY_ACCESS_TOKEN,accessToken);

        mAccessToken.set(accessToken);
        mRefreshToken.set(refreshToken);

        HttpManagerConfig.EncryptUtil encryptUtil = HttpManager.getConfig().getEncryptUtil();
        mEncryptedToken = encryptUtil.desEncrypt(KeyManager.getInstance().getHeaderKey(), accessToken);

        HttpManagerConfig.Logger logger = HttpManager.getConfig().getLogger();
        logger.log("TokenManager", "storeToken");

        Context context = HttpManager.getContext();
        if (context != null) {
            Intent i = new Intent(ACTION_NEW_ACCESS_TOKEN);
            LocalBroadcastManager.getInstance(context).sendBroadcast(i);
        }
    }

    public String getAccessToken() {
        if (mAccessToken.get() == null) {
            mAccessToken.set(mMMKV.getString(KEY_ACCESS_TOKEN, null));
        }
        return mAccessToken.get();
    }

    public String getRefreshToken() {
        if (mRefreshToken.get() == null) {
            mRefreshToken.set(mMMKV.getString(KEY_REFRESH_TOKEN, null));
        }
        return mRefreshToken.get();
    }

    public String getEncryptedAccessToken() {
        if (TextUtils.isEmpty(getAccessToken())) return null;
        if (TextUtils.isEmpty(mEncryptedToken)) {
            HttpManagerConfig.EncryptUtil encryptUtil = HttpManager.getConfig().getEncryptUtil();
            mEncryptedToken = encryptUtil.desEncrypt(KeyManager.getInstance().getHeaderKey(), getAccessToken());
        }
        return mEncryptedToken;
    }

    public void clearTokens() {
        mMMKV.removeValuesForKeys(new String[]{KEY_REFRESH_TOKEN, KEY_ACCESS_TOKEN});
        preference.remove(preference.KV_ENTITY_REFRESH_TOKEN);
        preference.remove(preference.KV_ENTITY_ACCESS_TOKEN);

        mRefreshToken.set(null);
        mAccessToken.set(null);
        mEncryptedToken = null;

        HttpManagerConfig.Logger logger = HttpManager.getConfig().getLogger();
        logger.log("TokenManager", "clearTokens");
    }

    public void refreshAccessToken(RefreshTokenCallback callback) {

        queue.add(callback);

        if (mTokenRefreshing) {
            return;
        }
        mTokenRefreshing = true;

        RefreshTokenTask task = new RefreshTokenTask();
        Message message = Message.obtain(mRefreshTokenHandler, task);
        message.sendToTarget();
//        try {
//            if (refreshTokenListener != null) {
//                refreshTokenListener.refresh();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    public synchronized String refreshAccessToken() {
        String token = null;
        try {
            String oldRefreshToken = TokenManager.getInstance().getRefreshToken();
            if (TextUtils.isEmpty(oldRefreshToken)) return null;
            String result = refreshToken(oldRefreshToken);

            HttpManagerConfig.Logger logger = HttpManager.getConfig().getLogger();
            logger.log("TokenManager", "refreshAccessToken");

            ApiResponse<Map<String, String>> response = ApiResponse.parse(result, Map.class);
            Map<String, String> map = response.getData();

            if (response.isSuccessful()) {
                String status = map.get("authnResponseStatus");
                String message = map.get("authnResponseMsg");

                if (STATUS_OK.equals(status)) {
                    String accessToken = map.get("accessToken");
                    String refreshToken = map.get("refreshToken");

                    if (refreshToken == null || accessToken == null) {
                        throw new NullPointerException("token is null!!");
                    }
                    TokenManager.getInstance().storeToken(refreshToken, accessToken);
                    token = accessToken;
                    logger.log("TokenManager", "refreshAccessToken success");
                } else if (shouldKickItOut(status)) {
                    Log.d(TAG, "refreshToken kickOut: " + message);
                    kickOut(message);
                    return null;
                } else {
                    Log.e(TAG, "refreshToken fail " + message);
                    logger.log("TokenManager refreshAccessToken failed", message);
                }
            } else {
                Log.e(TAG, "refreshToken fail " + response.getErrorMessage());
                logger.log("TokenManager refreshAccessToken failed", response.getErrorMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            HttpManagerConfig.Logger logger = HttpManager.getConfig().getLogger();
            logger.log("TokenManager refreshAccessToken failed", e.getMessage() + ", \n" + Arrays.toString(e.getStackTrace()));
        }
        return token;
    }

    private boolean shouldKickItOut(String status) {
        return STATUS_REFRESH_TOKEN_INVALID.equals(status) || STATUS_EXPIRED.equals(status) || STATUS_KICKED_OUT.equals(status);
    }

    /**
     * //踢出
     *
     * @param message
     */
    private void kickOut(String message) {
        Context context = HttpManager.getContext();
        if (message == null) {
            context.getString(R.string.me_token_refresh_token_expired);
        }
        HttpManagerConfig.Logger logger = HttpManager.getConfig().getLogger();
        logger.log("TokenManager kickOut", "message: " + message);
        HttpManager.getConfig().getEventListener().onKickOut(context, message, null);
    }


    public static String refreshToken(String refreshToken) {
        Map<String, Object> params = new HashMap<>();
        params.put("refreshToken", refreshToken);
        return HttpManager.postSync(REFRESH_TOKEN, null, params);
    }

    private class RefreshTokenTask implements Runnable {
        private static final String TAG = "RefreshTokenTask";

        public RefreshTokenTask() { }

        @Override
        public void run() {

            String accessToken = TokenManager.getInstance().refreshAccessToken();
            if (!TextUtils.isEmpty(accessToken)) {
                while (!queue.isEmpty()) {
                    RefreshTokenCallback callback = queue.poll();
                    if (callback != null)
                        callback.onSuccess();
                }
            } else {
                while (!queue.isEmpty()) {
                    RefreshTokenCallback callback = queue.poll();
                    if (callback != null)
                        callback.onFail();
                }
            }
            mTokenRefreshing = false;
        }
    }

    public interface RefreshTokenCallback {
        void onSuccess();

        void onFail();
    }
}