package com.jd.oa.network.httpmanager.interceptors;

import android.net.Uri;
import android.text.TextUtils;


import com.alibaba.fastjson.JSON;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;

import com.jd.oa.network.token.KeyManager;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import okhttp3.FormBody;
import okhttp3.Interceptor;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 未接网关接口加密body
 */
public class LegacyEncryptInterceptor implements Interceptor {
    private static final String TAG = "LegacyEncryptIntercepto";

    public static final String HEADER_ENCRYPT_KEY = "x-client-encrypt";
    public static final String HEADER_ENCRYPT_NONE = "none";
    public static final String HEADER_ENCRYPT_RSA = "rsa";
    public static final String HEADER_ENCRYPT_DES = "des";

    private HttpManagerConfig.UserInfo mUserInfo;
    private HttpManagerConfig.DeviceInfo mDeviceInfo;
    private HttpManagerConfig.EncryptUtil mEncryptUtil;
    private HttpManagerConfig.Logger mLogger;

    public LegacyEncryptInterceptor() {
        mUserInfo = HttpManager.getConfig().getUserInfo();
        mDeviceInfo = HttpManager.getConfig().getDeviceInfo();
        mEncryptUtil = HttpManager.getConfig().getEncryptUtil();
        mLogger = HttpManager.getConfig().getLogger();
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String gatewayVersion = request.header(HttpManager.HEADER_KEY_GATEWAY_VERSION);
        if (!HttpManager.HEADER_GATEWAY_NONE.equals(gatewayVersion)) {
            return chain.proceed(request);
        }
        request = encryptRequest(chain.request());
        Response response = chain.proceed(request);
        return response;
    }

    private Request encryptRequest(Request request) throws IOException {
        //只处理表单格式body
        if (request.body() instanceof FormBody) {
            return encryptFormBody(request);
        } else if (request.body() instanceof MultipartBody) {
            return encryptMultipartBody(request);
        } else {
            return request;
        }
    }

    @SuppressWarnings("unchecked")
    private Request encryptFormBody(Request request) throws IOException {
        FormBody oldBody = (FormBody) request.body();
        String encryptMode = request.header(HEADER_ENCRYPT_KEY);

        Map<String,String> params = (Map<String, String>) request.tag();
        if (params == null) {
            params = new HashMap<>();
        }

        String userName = mUserInfo.getUserName();
        if (TextUtils.isEmpty(userName) && oldBody != null) {
            userName = params.get("userName");
        }

        if (TextUtils.isEmpty(userName)) {
            userName = "";
            mLogger.log(TAG, "userName is null, " + userName);
        }

        FormBody requestBody;
        params.put("userName", userName.trim());

        if (HEADER_ENCRYPT_NONE.equals(encryptMode)) {
            //明文传输
            requestBody = oldBody;
        } else if (HEADER_ENCRYPT_RSA.equals(encryptMode)) {
            //RSA加密
            String paramString = getParamString(params);
            String encryptString = mEncryptUtil.rsaEncrypt(KeyManager.getInstance().getServerPublicKey(), paramString, KeyManager.ENCRYPT_SEGMENT_SIZE);
            String sign = mEncryptUtil.rsaSign(KeyManager.getInstance().getPrivateKey(), encryptString);

            requestBody = new FormBody.Builder().add("equipNo", mDeviceInfo.getDeviceUniqueId())
                    .add("userName", userName)
                    .add("encrypt", encryptString)
                    .add("sign", sign)
                    .build();
        } else {
            //DES加密
            String[] encryptArray = mEncryptUtil.getEncryptArray(params);
            String encrypt = encryptArray[0];
            String checksum = encryptArray[1];

            if (TextUtils.isEmpty(encrypt)) {
                String msg = "Encrypt is null, url: " + request.url() + ", params: " + JSON.toJSONString(params) + ", key: " + mEncryptUtil.getRandomKey();
                mLogger.log(TAG, msg);
                throw new IOException(msg);
            }

            if (TextUtils.isEmpty(checksum)) {
                String msg = "Checksum is null, url: " + request.url() + ", params: " + JSON.toJSONString(params) + ", key: " + mEncryptUtil.getRandomKey();
                mLogger.log(TAG, msg);
                throw new IOException(msg);
            }

            requestBody = new FormBody.Builder().add("equipNo", mDeviceInfo.getDeviceUniqueId())
                    .add("userName", userName)
                    .add("encrypt", encrypt)
                    .add("checksum", checksum)
                    .build();
        }
        return request.newBuilder().method(request.method(), requestBody).build();
    }

    @SuppressWarnings("unchecked")
    private Request encryptMultipartBody(Request request) {
        MultipartBody multipartBody = (MultipartBody) request.body();
        if (request.tag() == null || !(request.tag() instanceof Map)) return request;
        Map<String,String> params = (Map<String, String>) request.tag();

        // DES加密参数
        String userName = mUserInfo.getUserName();
        if (!TextUtils.isEmpty(userName)) {
            params.put("userName", userName.trim());
        }
        List<MultipartBody.Part> parts = multipartBody.parts();
        MultipartBody.Builder bodyBuilder = new MultipartBody.Builder();
        for (int i = 0; i < parts.size(); i++) {
            bodyBuilder.addPart(parts.get(i));
        }

        String[] encryptArray = mEncryptUtil.getEncryptArray(params);
        bodyBuilder.addFormDataPart("equipNo", mDeviceInfo.getDeviceUniqueId())
                .addFormDataPart("userName", params.get("userName"))
                .addFormDataPart("encrypt", encryptArray[0])
                .addFormDataPart("checksum", encryptArray[1]);

        return request.newBuilder().method(request.method(), bodyBuilder.build()).build();
    }

    private String getParamString(Map<String,String> paramMap) throws IOException {
        if (paramMap == null || paramMap.isEmpty()) return "";
        StringBuilder stringBuilder = new StringBuilder();
        Iterator<Map.Entry<String, String>> iterator = paramMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            if (stringBuilder.length() > 0) {
                stringBuilder.append("&");
            }
            stringBuilder.append(entry.getKey());
            stringBuilder.append("=");
            stringBuilder.append(Uri.encode(entry.getValue()));
        }
        return stringBuilder.toString();
    }
}
