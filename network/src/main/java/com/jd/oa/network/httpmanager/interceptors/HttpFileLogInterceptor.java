package com.jd.oa.network.httpmanager.interceptors;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Environment;
import android.telephony.TelephonyManager;
import android.util.Log;

import androidx.annotation.IntDef;
import androidx.core.content.ContextCompat;

import com.jd.oa.filetransfer.ProgressRequestBody;

import com.google.gson.Gson;
import com.jd.oa.network.httpmanager.HttpManager;

import java.io.EOFException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.net.InetAddress;
import java.net.Proxy;
import java.net.Socket;
import java.net.SocketAddress;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import okhttp3.Connection;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.Route;
import okhttp3.internal.http.HttpHeaders;
import okhttp3.logging.HttpLoggingInterceptor;
import okio.Buffer;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.GzipSource;
import okio.Okio;

/**
 * 记录网络请求日志到文件
 * @see HttpLoggingInterceptor
 */
public class HttpFileLogInterceptor implements Interceptor {
    private static final String TAG = "HttpFileLogInterceptor";

    public static final String HEADER_IGNORE_RESPONSE_BODY = "x-client-log-ignore-body";

    private static final String DIR_NAME = "JDME" + File.separator + "log" + File.separator + "network";
    private static final String LOG_FILE_PREFIX = "jdme-";
    private static final String LOG_FILE_SUFFIX = ".log";
    //日志保存7天
    private static final int LOG_FILE_RETAIN_DAYS = 15;

    private static final Charset UTF8 = StandardCharsets.UTF_8;

    public static final int LEVEL_NONE = 1;
    public static final int LEVEL_BASIC = 2;
    public static final int LEVEL_HEADER = 3;
    public static final int LEVEL_BODY = 4;

    @IntDef({LEVEL_NONE, LEVEL_BASIC, LEVEL_HEADER, LEVEL_BODY})
    @Retention(RetentionPolicy.SOURCE)
    public @interface LogLevel{}

    private File mDir;
    private File mFile;
    private SimpleDateFormat mFormat;
    private BufferedSink mSink;
    private boolean mPrintToConsole;
    private boolean mRecordAllRequestBody;
    ExecutorService mExecutorService;
    private BlockingQueue<String> mBlockingQueue;

    @LogLevel
    private int mLogLevel;

    @SuppressLint("SimpleDateFormat")
    public HttpFileLogInterceptor(Context context, @LogLevel int logLevel, boolean printToConsole, boolean recordAllRequestBody) {
        this.mLogLevel = logLevel;
        this.mPrintToConsole = printToConsole;
        this.mRecordAllRequestBody = recordAllRequestBody;
        mDir = new File(context.getExternalFilesDir(""), DIR_NAME);

        mFormat = new SimpleDateFormat("yyyy-MM-dd");
        mFile = getLogFile(mDir);

        mBlockingQueue = new LinkedBlockingQueue<>();

        //单独线程写日志
        mExecutorService = Executors.newSingleThreadExecutor();
        mExecutorService.submit(new LogFileCleaner());
        //mExecutorService.submit(new WriteLogTask());
    }

    @SuppressWarnings("StringConcatenationInsideStringBufferAppend")
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        if (mLogLevel == LEVEL_NONE) {
            return chain.proceed(request);
        }

        RequestBody requestBody = request.body();
        //避免把body写入日志的时候触发进度更新
        if (requestBody instanceof ProgressRequestBody) {
            requestBody = ((ProgressRequestBody) requestBody).getRequestBody();
        }

        boolean hasRequestBody = requestBody != null;

        boolean logBody = mLogLevel == LEVEL_BODY && !"true".equals(request.header(HEADER_IGNORE_RESPONSE_BODY));
        boolean logHeaders = logBody || mLogLevel == LEVEL_HEADER;

        StringBuilder stringBuilder = new StringBuilder();
        Connection connection = chain.connection();
        stringBuilder.append("--> \n");
        String requestStartMessage =
                request.method()
                + ' ' + request.url()
                + (connection != null ? " " + connection.protocol() : "");
        if (!logHeaders && hasRequestBody) {
            requestStartMessage += " (" + requestBody.contentLength() + "-byte body)";
        }
        stringBuilder.append(requestStartMessage);
        stringBuilder.append("\n");

        if (logHeaders) {
            Headers headers = request.headers();
            for (int i = 0, count = headers.size(); i < count; i++) {
                String name = headers.name(i);
                stringBuilder.append(headers.name(i) + ":" + headers.get(name));
                stringBuilder.append("\n");
            }

            if (!logBody || !hasRequestBody) {
                stringBuilder.append("--> END " + request.method());
            } else if (bodyHasUnknownEncoding(request.headers())) {
                stringBuilder.append("--> END " + request.method() + " (encoded body omitted)");
            } else {
                //request body
                stringBuilder.append("\n");
                if (mRecordAllRequestBody) {
                    stringBuilder.append(logRequestBody(request, requestBody));
                } else {
                    if (requestBody instanceof FormBody ||"json".equalsIgnoreCase(requestBody.contentType().subtype())) {
                        stringBuilder.append(logRequestBody(request, requestBody));
                    } else if (requestBody instanceof MultipartBody) {
                        stringBuilder.append("--> END " + request.method() + " (multipart body omitted)");
                    } else {
                        stringBuilder.append("--> END " + request.method() + " (body omitted)");
                    }
                }

            }
            stringBuilder.append("\n");
        }

        long startNs = System.nanoTime();
        Response response;
        try {
            response = chain.proceed(request);
        } catch (Exception e) {
            stringBuilder.append("<-- HTTP FAILED: " + e);
            stringBuilder.append("\n");
            log(stringBuilder.toString());
            throw e;
        }
        long tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs);

        ResponseBody responseBody = response.body();
        long contentLength = responseBody.contentLength();
        String bodySize = contentLength != -1 ? contentLength + "-byte" : "unknown-length";
        stringBuilder.append("<-- "
                + response.code()
                + (response.message().isEmpty() ? "" : ' ' + response.message())
                + ' ' + response.request().url()
                + " (" + tookMs + "ms" + (!logHeaders ? ", " + bodySize + " body" : "") + ')');
        stringBuilder.append("\n");

        logBody = mLogLevel == LEVEL_BODY;

        if (logHeaders) {
            Headers headers = response.headers();
            for (int i = 0, count = headers.size(); i < count; i++) {
                String name = headers.name(i);
                stringBuilder.append(headers.name(i) + ": " + headers.get(name));
                stringBuilder.append("\n");
            }

            if (!logBody || !HttpHeaders.hasBody(response)) {
                stringBuilder.append("<-- END HTTP");
                stringBuilder.append("\n");
            } else if (bodyHasUnknownEncoding(response.headers())) {
                stringBuilder.append("<-- END HTTP (encoded body omitted)");
                stringBuilder.append("\n");
            } else {
                //response body

                BufferedSource source = responseBody.source();
                source.request(Long.MAX_VALUE); // Buffer the entire body.
                Buffer buffer = source.buffer();

                Long gzippedLength = null;
                if ("gzip".equalsIgnoreCase(headers.get("Content-Encoding"))) {
                    gzippedLength = buffer.size();
                    GzipSource gzippedResponseBody = null;
                    try {
                        gzippedResponseBody = new GzipSource(buffer.clone());
                        buffer = new Buffer();
                        buffer.writeAll(gzippedResponseBody);
                    } finally {
                        if (gzippedResponseBody != null) {
                            gzippedResponseBody.close();
                        }
                    }
                }

                Charset charset = UTF8;
                MediaType contentType = responseBody.contentType();
                if (contentType != null) {
                    charset = contentType.charset(UTF8);
                }

                if (!isPlaintext(buffer)) {
                    stringBuilder.append("");
                    stringBuilder.append("\n");
                    stringBuilder.append("<-- END HTTP (binary " + buffer.size() + "-byte body omitted)");
                    stringBuilder.append("\n");

                    log(stringBuilder.toString());

                    return response;
                }

                if (contentLength != 0) {
                    stringBuilder.append("");
                    stringBuilder.append("\n");
                    stringBuilder.append(buffer.clone().readString(charset));
                    stringBuilder.append("\n");
                }

                if (gzippedLength != null) {
                    stringBuilder.append("<-- END HTTP (" + buffer.size() + "-byte, "
                            + gzippedLength + "-gzipped-byte body)");
                    stringBuilder.append("\n");
                } else {
                    stringBuilder.append("<-- END HTTP (" + buffer.size() + "-byte body)");
                    stringBuilder.append("\n");
                }
            }
        }

        if (!response.isSuccessful()) {
            logException(connection, null);
        }

        log(stringBuilder.toString());

        return response;
    }

    protected void logException(Connection connection, Throwable ex) {
        try {
            String log = buildErrorMsg(connection, ex);
            HttpManager.getConfig().getLogger().logLocal("NET", log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    protected String buildErrorMsg(Connection connection, Throwable e) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("\n");

        Context context = HttpManager.getContext();
        ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = manager.getActiveNetworkInfo();
        if (info != null) {
            stringBuilder.append("type: ").append(info.getType())
                    .append(", typeName:").append(info.getTypeName())
                    .append("\n");
        }

        TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (tm != null) {
            stringBuilder.append("READ_PHONE_STATE PERMISSION: ").append(ContextCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED).append("\n");
            stringBuilder.append("SimOperator: ").append(tm.getSimOperator()).append("\n");
            stringBuilder.append("SimOperatorName: ").append(tm.getSimOperatorName()).append("\n");
            stringBuilder.append("NetworkOperator: ").append(tm.getNetworkOperator()).append("\n");
            stringBuilder.append("NetworkOperatorName: ").append(tm.getNetworkOperatorName()).append("\n");
        }

        if (connection != null) {
            stringBuilder.append("connection: ").append(connection).append("\n");

            Socket socket = connection.socket();
            SocketAddress localAddress = socket.getLocalSocketAddress();
            SocketAddress inetAddress = socket.getRemoteSocketAddress();
            stringBuilder.append("localAddress: ").append(localAddress).append("\n");
            stringBuilder.append("remoteAddress: ").append(inetAddress).append("\n");

            Proxy proxy = connection.route().proxy();
            stringBuilder.append("proxy: ").append(proxy).append("\n");
        }

        if (e != null) {
            stringBuilder.append("exception: ").append(e.getMessage()).append("\n");
            stringBuilder.append("stackTrace: ").append(Log.getStackTraceString(e)).append("\n");
        }

        return stringBuilder.toString();
    }

    private void log(String message) {
        //mBlockingQueue.offer(message);
        //mExecutorService.submit(new WriteLogTask(message));

        if (mPrintToConsole) {
            Log.d("HttpFileLogger", message);
        }
        HttpManager.getConfig().getLogger().logLocal("NET", message);
    }

    private String logRequestBody(Request request, RequestBody requestBody) throws IOException {
        //未接网关的请求requestBody被加密，从tag中取请求参数
        boolean isNoneGateway = HttpManager.HEADER_GATEWAY_NONE.equals(request.header(HttpManager.HEADER_KEY_GATEWAY_VERSION));
        if (isNoneGateway) {
            return logNoneGatewayRequestBody(request);
        }

        StringBuilder stringBuilder = new StringBuilder();
        Buffer buffer = new Buffer();

        requestBody.writeTo(buffer);

        Charset charset = StandardCharsets.UTF_8;
        MediaType contentType = requestBody.contentType();
        if (contentType != null) {
            charset = contentType.charset(UTF8);
        }

        stringBuilder.append("");
        if (isPlaintext(buffer)) {
            stringBuilder.append(buffer.readString(charset));
            stringBuilder.append("\n");
            stringBuilder.append("--> END " + request.method()
                    + " (" + requestBody.contentLength() + "-byte body)");
        } else {
            stringBuilder.append("--> END " + request.method() + " (binary "
                    + requestBody.contentLength() + "-byte body omitted)");
        }
        return stringBuilder.toString();
    }


    private String logNoneGatewayRequestBody(Request request) {
        StringBuilder stringBuilder = new StringBuilder();
        if (request.tag() != null) {
            String body = new Gson().toJson(request.tag());
            stringBuilder.append(body);
        }
        stringBuilder.append("\n");
        stringBuilder.append("--> END " + request.method());
        return stringBuilder.toString();
    }

    public HttpFileLogInterceptor setLogLevel(@LogLevel int logLevel) {
        mLogLevel = logLevel;
        return this;
    }

    private boolean bodyHasUnknownEncoding(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return contentEncoding != null
                && !contentEncoding.equalsIgnoreCase("identity")
                && !contentEncoding.equalsIgnoreCase("gzip");
    }

    private boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }

    private File getLogFile(File parent) {
        return new File(parent, LOG_FILE_PREFIX + mFormat.format(new Date()) + LOG_FILE_SUFFIX);
    }

    public File getLogFile() {
        return mFile;
    }

    private class WriteLogTask implements Runnable {
        SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
        String message;

        public WriteLogTask(String message) {
            this.message = message;
        }

        @Override
        public void run() {
            try {
                if (!mFile.exists()) {
                    mDir.mkdirs();
                    mFile.createNewFile();
                }
                if (mSink == null) {
                    mSink = Okio.buffer(Okio.sink(new FileOutputStream(mFile, true)));
                }

                //while (true) {
                    //String message = mBlockingQueue.poll();
                    mSink.writeString(mSimpleDateFormat.format(new Date()), StandardCharsets.UTF_8);
                    mSink.writeString("\n", StandardCharsets.UTF_8);
                    mSink.writeString(message, StandardCharsets.UTF_8);
                    mSink.writeString("\n", StandardCharsets.UTF_8);
                    mSink.flush();
                //}
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除旧日志文件
     */
    private class LogFileCleaner implements Runnable {

        @Override
        public void run() {
            if (!mDir.exists()) return;

            File[] files = mDir.listFiles();
            if (files == null) return;

            Calendar today = Calendar.getInstance();
            today.setTime(new Date());
            today.set(Calendar.HOUR_OF_DAY, 0);
            today.set(Calendar.MINUTE, 0);
            today.set(Calendar.SECOND, 0);

            for (int i = 0; i < files.length; i++) {
                File file = files[i];
                String name = file.getName();
                name = name.replace(LOG_FILE_PREFIX, "");
                name = name.replace(LOG_FILE_SUFFIX, "");
                try {
                    long date = mFormat.parse(name).getTime();
                    int days = (int) ((today.getTimeInMillis() - date) / (1000 * 60 * 60 * 24));
                    if (days > LOG_FILE_RETAIN_DAYS) {
                        file.delete();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}