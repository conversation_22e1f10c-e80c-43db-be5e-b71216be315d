package com.jd.oa.network.httpmanager.interceptors;

import com.jd.oa.network.httpmanager.HttpManager;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class DefaultGatewayVerInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String gatewayVer = request.header(HttpManager.HEADER_KEY_GATEWAY_VERSION);
        if (gatewayVer == null || gatewayVer.isEmpty()) {
            request = request.newBuilder()
                    .addHeader(HttpManager.HEADER_KEY_GATEWAY_VERSION, HttpManager.HEADER_GATEWAY_V1)
                    .build();
        }
        return chain.proceed(request);
    }
}
