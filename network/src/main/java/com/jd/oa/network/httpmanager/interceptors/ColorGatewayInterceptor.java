package com.jd.oa.network.httpmanager.interceptors;

import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import com.jd.oa.network.color.url.ColorUrlConfig;
import com.jd.oa.network.color.utils.MapUtils;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.network.utils.Utils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.UUID;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * color 网关相关处理
 */
public class ColorGatewayInterceptor implements Interceptor {
    private final HttpManagerConfig.DeviceInfo mDeviceInfo;
    private final HttpManagerConfig.UserInfo mUserInfo;
    private static String APPVersion = null;
    private String deviceId = null;
    private String deviceToken = "";

    public ColorGatewayInterceptor() {
        mDeviceInfo = HttpManager.getConfig().getDeviceInfo();
        mUserInfo = HttpManager.getConfig().getUserInfo();
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String gatewayVersion = request.header(HttpManager.HEADER_KEY_GATEWAY_VERSION);
        if (Objects.equals(gatewayVersion, HttpManager.HEADER_GATEWAY_COLOR)) {
            String action = request.header(HttpManager.HEADER_KEY_ACTION);
            // switch to new action
            action = HttpManager.getConfig().getGatewayConfig().transferAction(action,HttpManager.getContext());
            boolean isGet = Objects.equals(request.header(HttpManager.HEADER_KEY_METHOD_GET), "true");
            HttpUrl newUrl;
            Request.Builder builder = request.newBuilder();
            if (isGet) {
                newUrl = request.url().newBuilder(getColorUrl(action, request)).build();
            } else {
                newUrl = request.url().newBuilder(getPostUrl(action, request)).build();
                Map<String, Object> params = (Map<String, Object>) request.tag();
                params = encryptParams(action, params);
                builder.post(getPostRequestBody(params));
            }
            builder.url(newUrl)
                    .addHeader("Host", newUrl.host());
            processHeader(builder, action);
            request = builder.build();
        }
        return chain.proceed(request);
    }

    private void processHeader(Request.Builder builder, String action) {
        String accessToken = TokenManager.getInstance().getAccessToken();
        if (action != null && !action.isEmpty()) {
            builder.addHeader("functionId", action);
        }
        builder.addHeader("x-language", mDeviceInfo.getLocale());
//        builder.addHeader("lang", mDeviceInfo.getLocale());
        builder.addHeader("x-client", "Android");
        builder.addHeader("x-device-type", mDeviceInfo.getDeviceType());
        if(HttpManager.getConfig().isSaasFlavor()){
            addHeadForSaas(builder,accessToken);
        }else {
            if(!TextUtils.isEmpty(accessToken)){
                builder.addHeader("Cookie", "me_token=" + accessToken);
            }
            if(!TextUtils.isEmpty(mUserInfo.getTeamId())){
                builder.addHeader("x-team-id", mUserInfo.getTeamId());
            }
        }
        if (TextUtils.isEmpty(APPVersion)) {
            APPVersion = mDeviceInfo.getAppVersionName();
        }
//        builder.addHeader("clientVersion", APPVersion);
//        builder.addHeader("openudid", getDeviceId());
//        builder.addHeader("uuid", getDeviceId());
        addUselessHeaders(builder);

        // Clean up the http headers related to code logic
        builder.removeHeader(HttpManager.HEADER_KEY_ACTION)
                .removeHeader(HttpManager.HEADER_KEY_GATEWAY_VERSION)
                .removeHeader(HttpManager.HEADER_KEY_METHOD_GET);
    }

    private String getDeviceId() {
        if (TextUtils.isEmpty(deviceId)) {
            deviceId = mDeviceInfo.getDeviceUniqueId();
        }
        return deviceId;
    }

    private String getDeviceToken() {
        if (TextUtils.isEmpty(deviceToken)) {
            deviceToken = mDeviceInfo.getPushDeviceToken();
        }
        return deviceToken;
    }

    private String getPostUrl(String action, Request request) {
        if (action != null && action.startsWith("http")) {
            return action;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("functionId", action);
        return appendParamsToUrl(ColorUrlConfig.getUrl(), params);
//        return ColorUrlConfig.getUrl();
    }


    private RequestBody getPostRequestBody(Map<String, Object> param) {
        Map<String, String> map = MapUtils.obj2Str(param);
        FormBody.Builder b = new FormBody.Builder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            b.add(entry.getKey(), entry.getValue());
        }
        return b.build();
    }

    private String getColorUrl(String action, Request request) {
        if (action != null && action.startsWith("http")) {
            return action;
        }
        String url = ColorUrlConfig.getUrl();
        Map<String, Object> params = (Map<String, Object>) request.tag();
        params = encryptParams(action, params);
        return appendParamsToUrl(url, params);
    }

    private String appendParamsToUrl(String url, Map<String, Object> params) {
        if (TextUtils.isEmpty(url) || params == null) return url;
        Uri.Builder builder = Uri.parse(url).buildUpon();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            builder.appendQueryParameter(entry.getKey(), entry.getValue().toString());
        }
        return builder.build().toString();
    }


    private Map<String, Object> encryptParams(String action, Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        return gatewayEncrypt(action, params);
    }

    private Map<String, Object> gatewayEncrypt(String action, Map<String, Object> map) {
        Map<String, Object> result = new TreeMap<>();
        addUselessParams(result);
        result.put("functionId", action);
        result.put("uuid", getDeviceId());
        result.put("lang", mDeviceInfo.getLocale());
        String appId = ColorUrlConfig.getAppId();
        result.put("appid", appId);
        String t = System.currentTimeMillis() + "";
        result.put("t", t);
        String body = MapUtils.map2String(map);
        if (body != null) {
            result.put("body", body);
        }

        if (TextUtils.isEmpty(APPVersion)) {
            APPVersion = mDeviceInfo.getAppVersionName();
        }
        result.put("clientVersion", APPVersion); //客户端类型
        result.put("loginType", ColorUrlConfig.getLoginType());

        StringBuilder sb = new StringBuilder();
        for (String key : result.keySet()) {
            if (!sb.toString().isEmpty()) {
                sb.append("&");
            }
            sb.append(result.get(key));
        }
        String data = sb.toString();
        String sign = HMACSHA256(data.getBytes());
        result.put("sign", sign.toLowerCase());
        return result;
    }

    private static String HMACSHA256(byte[] data) {
        try {
            byte[] key = ColorUrlConfig.getSecretKey().getBytes();
            SecretKeySpec signingKey = new SecretKeySpec(key, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(signingKey);
            return byte2hex(mac.doFinal(data));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String byte2hex(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }

    /**
     * Add some xxx for future use.
     * How will them be used in the future? I have no idea. This is determined by the architect.
     */
    private void addUselessHeaders(Request.Builder builder) {
        String nonce = UUID.randomUUID().toString().replace("-", "");
        builder.addHeader("x-os-name", "android")
                .addHeader("x-imei", getDeviceId())
                .addHeader("x-tenant-code", mUserInfo.getTenantCode())
                .addHeader("x-channel", mUserInfo.getChannel())
                .addHeader("x-model", android.os.Build.MODEL)
                .addHeader("x-nonce", nonce);
        String token = getDeviceToken();
        if (!TextUtils.isEmpty(token)) {
            builder.addHeader("x-device-token", token);
        }
    }

    /**
     * see above
     */
    private void addUselessParams(Map<String, Object> map) {
        map.put("appName", "JDME");
        map.put("d_model", Build.MODEL);
        map.put("d_brand", Build.BRAND);
        map.put("networkType", Utils.isWiFi(HttpManager.getContext()) ? "wifi" : "4G");
        map.put("screen", mDeviceInfo.getDeviceWidth() + "*" + mDeviceInfo.getDeviceHeight());
        map.put("osVersion", Build.VERSION.RELEASE);
        map.put("client", mDeviceInfo.getDeviceType());
        map.put("eid", mDeviceInfo.getFp());
    }

    /*Saas 增加的header，需要判断header中是否存在，防止切租户接口传递的header被覆盖*/
    private void addHeadForSaas(Request.Builder builder,String accessToken){
        Headers header = builder.build().headers();
        String token = header.get("x-token");
        String cookie = header.get("Cookie");
        String userId =  header.get("x-user-id");
        String teamId = header.get("x-team-id");
        if(!TextUtils.isEmpty(accessToken)){
            if(TextUtils.isEmpty(token)){
                builder.addHeader("x-token",accessToken);
            }
            if(TextUtils.isEmpty(cookie)){
                builder.addHeader("Cookie", "wskey=" + accessToken);
            }
        }
        if(!TextUtils.isEmpty(mUserInfo.getUserId()) && TextUtils.isEmpty(userId)){
            builder.addHeader("x-user-id",mUserInfo.getUserId());
        }
        if(!TextUtils.isEmpty(mUserInfo.getTeamId()) && TextUtils.isEmpty(teamId)){
            builder.addHeader("x-team-id", mUserInfo.getTeamId());
        }
        builder.addHeader("x-did",mDeviceInfo.getDeviceUniqueId());
    }
}
