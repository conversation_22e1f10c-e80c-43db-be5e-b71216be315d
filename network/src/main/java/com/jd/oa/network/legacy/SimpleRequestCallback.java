package com.jd.oa.network.legacy;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.text.TextUtils;
import android.widget.Toast;

import com.alibaba.fastjson.JSON;
import com.jd.oa.network.R;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.remind.Remind;
import com.jd.oa.network.utils.Utils;

import java.net.URLDecoder;

import okhttp3.Headers;

/**
 * 对请求的方法
 *
 * @param <T>
 */
public class SimpleRequestCallback<T>  {

    /**
     * 是否显示加载对话框
     */
    private final boolean showProgressDialog;
    /**
     * 显示自定义提示语句
     */
    private int resId;
    /**
     * 上下文对象
     */
    private Context context = null;
    /**
     * 加载框
     */
    private ProgressDialog progressDialog;
    /**
     * 是否显示加载失败Toast提示
     */
    private boolean showRequestFaildMsg = false;
    /**
     * 是否允许取消，即：可按返回键取消（默认可以取消）
     */
    private boolean isCancel = true;

    //是否需要翻译，默认为翻译
    //需求是：部分原生应用不需要翻译，部分需要。sb
    private boolean needTranslate = true;

    // 回调是否在主线程执行
    public boolean mainThread = true;

    /**
     * 构造函数
     *
     * @param context             活动
     * @param showProgressDialog  是否显示加载框，默认显示
     * @param resId               加载框提示文字
     * @param showRequestFaildMsg 是否显示请求失败提示
     * @param isCancle            是否允许加载框cancel
     */
    public SimpleRequestCallback(Context context, boolean showProgressDialog,
                                 int resId, boolean showRequestFaildMsg, boolean isCancle) {
        this.context = context;
        this.showProgressDialog = showProgressDialog;
        this.isCancel = isCancle;
        this.showRequestFaildMsg = showRequestFaildMsg;
        this.resId = (-1 == resId ? R.string.me_loading : resId);
    }

    /**
     * 显示加载框（显示加载失败提示Toast）
     *
     * @param context  活动
     * @param resId    加载框提示文字
     * @param isCancle 是否允许加载框cancel
     */
    public SimpleRequestCallback(Context context, int resId, boolean isCancle) {
        this(context, true, resId, false, isCancle);
    }

    /**
     * 显示加载框（显示加载失败提示Toast）
     *
     * @param context 活动
     * @param resId   加载框提示文字
     */
    public SimpleRequestCallback(Context context, int resId) {
        this(context, true, resId, false, true);
    }

    /**
     * 是否显示加载框提示框（显示加载失败提示Toast）
     *
     * @param context
     * @param showProgress true 显示 false 不显示
     */
    public SimpleRequestCallback(Context context, boolean showProgress) {
        this(context, showProgress, -1, false, true);
    }

    /**
     * 不显示加载框（显示加载失败提示Toast）
     *
     * @param context
     */
    public SimpleRequestCallback(Context context) {
        this(context, false, -1, false, true);
    }


    /**
     * 不显示加载框,不显示加载失败提示Toast
     *
     *
     */
    public SimpleRequestCallback() {
        this(null, false, -1, false, true);
    }

    /**
     * 是否显示加载框，是否显示加载错误提示
     *
     * @param context
     * @param showProgress
     * @param showRequestFaildMsg
     */
    public SimpleRequestCallback(Context context, boolean showProgress,
                                 boolean showRequestFaildMsg) {
        this(context, showProgress, -1, showRequestFaildMsg, true);
    }

    /**
     * 是否显示加载框，是否显示加载错误提示，是否 允许取消对象框
     *
     * @param context
     * @param showProgressDialog
     * @param showFailMsg
     * @param isCancel
     */
    public SimpleRequestCallback(Context context, boolean showProgressDialog, boolean showFailMsg, boolean isCancel) {
        this(context, showProgressDialog, -1, showFailMsg, isCancel);
    }

    public void setNeedTranslate(boolean needTranslate) {
        this.needTranslate = needTranslate;
        if (resId == R.string.me_loading) {
            resId = needTranslate ? R.string.me_loading : R.string.me_loading_not_translate;
        }
    }

    public void onStart() {
        if (showProgressDialog && null != context) {
            if (context instanceof Activity) {
                if (((Activity) context).isFinishing()) {
                    return;
                }
                if(HttpManager.getConfig().isSaasFlavor()){
                    JdSaasLoadingDialog.showLoadingDialog(context);
                }else{
                    progressDialog = new ProgressDialog(context);
                    progressDialog.setMessage(context.getResources().getString(resId));
                    progressDialog.setCancelable(isCancel);
                    progressDialog.show();
                }
            }
        }
    }

    public void onFailure(HttpException exception, String info) {
        dismissProgress();
        if (exception == null) {
            return;
        }
        int statusCode = exception.getExceptionCode();
        Integer msgRes = null;
        switch (statusCode) {
            case -100:
                msgRes = needTranslate ? R.string.me_pub_no_network : R.string.me_pub_no_network_not_translate;
                break;
            case 0: // 服务器响应超时
                msgRes = needTranslate ? R.string.me_pub_server_time_out : R.string.me_pub_server_time_out_not_translate;
                break;
            case 500:
                msgRes = needTranslate ? R.string.me_pub_server_error : R.string.me_pub_server_error_not_translate;
                break;
            case 404:
                msgRes = needTranslate ? R.string.me_pub_server_not_found : R.string.me_pub_server_not_found_not_translate;
                break;
            default:
                //msgRes = needTranslate ? R.string.me_server_time_out : R.string.me_server_time_out_not_translate;
                break;
        }
        HttpManagerConfig.Logger logger = HttpManager.getConfig().getLogger();
        boolean netAvailable = Utils.isNetworkAvailable(HttpManager.getContext());
        if (!netAvailable) {
            msgRes = needTranslate ? R.string.me_pub_no_network : R.string.me_pub_no_network_not_translate;
        }
        logger.log("NET", "exception: " + exception.getMessage() + "info: " + (TextUtils.isEmpty(info) ? "null" : info) + "msgRes: " + msgRes + "code: " + statusCode + "netAvailable: " + netAvailable);
        if (showRequestFaildMsg) {
            Context ctx = context;
            if (ctx == null) ctx = HttpManager.getContext();
            if (msgRes == null) return;
            Toast.makeText(ctx, msgRes, Toast.LENGTH_SHORT).show();
        }
    }

    public void success(ResponseInfo<T> info) {
        if (!handleRemindTips(info.body)) {
            onSuccess(info);
        }
    }

    public void onSuccess(ResponseInfo<T> info) {
        dismissProgress();
        handleHeader(info);
    }

    /**
     * 无网络回调接口
     */
    @Deprecated
    public void onNoNetWork() {
        if (showRequestFaildMsg) {
            Context ctx = context;
            if (ctx == null) ctx = HttpManager.getContext();
            Toast.makeText(ctx, needTranslate ? R.string.me_no_network : R.string.me_no_network_not_translate, Toast.LENGTH_SHORT).show();
        }

        onFailure(new HttpException(-100, "当前无网络"), "当前无网络");
    }

    /**
     * 隐藏进度框
     */
    private void dismissProgress() {
        if(showProgressDialog){
            if (context instanceof Activity) {
                if (((Activity) context).isFinishing()) {
                    return;
                }
            }
            if(HttpManager.getConfig().isSaasFlavor()){
                JdSaasLoadingDialog.cancelLoadingDialog(context);
            }else{
                // for 3.1.0 Dialog  not attached to window manager 此异常不要出现了
                if (null != progressDialog) {
                    progressDialog.dismiss();
                    progressDialog = null;
                }
            }
        }
        if (context != null) {
            context = null;
        }
    }

    /**
     * 没什么用
     * @param obj
     */
    @Deprecated
    public void setUserTag(Object obj) {}

    public void onLoading(long total, long current, boolean isUploading) {
    }

    /**
     * 处理返回header头信息
     *
     * @param info
     */
    private void handleHeader(ResponseInfo<T> info) {
        if (info != null) {
            try {
                Headers headers = info.getHeaders();
                String contentHead = headers.get("CONTENT_HEAD");
                String leaveHeader = headers.get("leaveUrl");
                String leaveUrlStr = null;
                //leaveheader不为空  说明当前erp已离职
                if (leaveHeader != null) {
                    leaveUrlStr = URLDecoder.decode(leaveHeader, "UTF-8");
                }
                if (contentHead != null) {
                    String opString = URLDecoder.decode(contentHead, "UTF-8");
                    String[] split = opString.split("#");
                    if (split.length > 1) {
                        String operCode = split[0];
                        String message = split[1];
                        toOperation(operCode, message, leaveUrlStr);
                    }
                }
            } catch (Exception e) {
                // doNothing
            }
        }
    }

    /**
     * 处理返回头操作
     */
    private void toOperation(String operCode, String message, String leaveUrl) {
        // 用户已在其他设备登录，强制退出当前用户
        // 这个code目前并没有什么卵用
        if ("0000".equals(operCode)) {
            HttpManagerConfig.EventListener listener = HttpManager.getConfig().getEventListener();
            listener.onKickOut(HttpManager.getContext(), message, leaveUrl);
        }
    }

    private boolean handleRemindTips(String body) {
        try {
            org.json.JSONObject object = new org.json.JSONObject(body);
            if (!object.has("remind")) return false;
            final Remind remind = JSON.parseObject(object.getString("remind"), Remind.class);
            HttpManager.getConfig().getEventListener().onReceiveRemindTips(remind);
            return remind.isInterceptCallback();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}