package com.jd.oa.network.httpmanager;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.jd.oa.network.httpmanager.interceptors.CatchExceptionInterceptor;
import com.jd.oa.network.httpmanager.interceptors.ChangeBaseUrlInterceptor;
import com.jd.oa.network.httpmanager.interceptors.ColorGatewayInterceptor;
import com.jd.oa.network.httpmanager.interceptors.DecryptResponseInterceptor;
import com.jd.oa.network.httpmanager.interceptors.DefaultGatewayVerInterceptor;
import com.jd.oa.network.httpmanager.interceptors.GatewayHeaderInterceptor;
import com.jd.oa.network.httpmanager.interceptors.GatewayV2HeaderInterceptor;
import com.jd.oa.network.httpmanager.interceptors.HttpFileLogInterceptor;
import com.jd.oa.network.httpmanager.interceptors.LegacyEncryptInterceptor;
import com.jd.oa.network.httpmanager.interceptors.LegacyHeaderInterceptor;
import com.jd.oa.network.httpmanager.interceptors.SwitchGatewayInterceptor;
import com.jd.oa.network.httpmanager.interceptors.TimeoutInterceptor;
import com.jd.oa.network.httpmanager.interceptors.UserAgentInterceptor;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.utils.Utils;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.Cache;
import okhttp3.Call;
import okhttp3.ConnectionPool;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 统一网络请求入口，支持网关V1、V2和未接网关网络请求
 * <p>
 * 1、未接网关请求HttpManager.legacy().post()
 * 2、网关v1请求HttpManager.post()
 * 3、网关V2请求HttpManager.v2().post()
 */
public class HttpManager {
    private static final String TAG = "GatewayHttpManager";

    /**
     * http://docs.avatar.jd.com/j-api/
     */
    public static final String JAPI_MOCK_URL_PREFIX = "http://mocker.jd.com/mocker/zh/";

    public static final String HEADER_KEY_GATEWAY_VERSION = "x-client-gateway-version";
    public static final String HEADER_GATEWAY_NONE = "none";    //不走网关
    public static final String HEADER_GATEWAY_V1 = "v1";        //v1网关
    public static final String HEADER_GATEWAY_V2 = "v2";        //v2网关
    public static final String HEADER_GATEWAY_COLOR = "color";        //color网关

    public static final String HEADER_KEY_ACTION = "x-client-action";
    public static final String HEADER_KEY_INTRANET = "x-client-intranet";  //内网访问接口标示
    public static final String HEADER_KEY_CONNECT_TIMEOUT = "x-client-connect-timeout";
    public static final String HEADER_KEY_WRITE_TIMEOUT = "x-client-write-timeout";
    public static final String HEADER_KEY_READ_TIMEOUT = "x-client-read-timeout";

    public static final String HEADER_KEY_SKIP_INTERCEPTORS = "x-client-skip-interceptors";    //跳过业务拦截器

    //J-API MOCK
    public static final String HEADER_KEY_JAPI_MOCK_ID = "x-client-japi-mock-id";
    public static final String HEADER_KEY_JAPI_MOCK_URL = "x-client-japi-mock-url";

    public static final String HEADER_KEY_METHOD_GET = "true";  //Http get 参数放在url query中

    public static final String HEADER_KEY_DO_NOT_REPORT_ERROR = "x-client-do-not-report-error";

    //并发请求数
    private static final int MAX_REQUESTS = 128;
    private static final int MAX_REQUESTS_PER_HOST = 24;

    //请求加密
    public static final int REQUEST_ENCRYPT_NONE = 1 << 1;
    public static final int REQUEST_ENCRYPT_DES = 1 << 2;
    public static final int REQUEST_ENCRYPT_RSA = 1 << 3;

    //响应解密
    public static final int RESPONSE_DECRYPT_RSA = 1 << 4;

    public static final int TIMEOUT = 20;

    private static OkHttpClient httpClient;

    private static Handler sMainHandler;

    private static Context sContext;
    private static HttpManagerConfig sConfig;
    private static Gson sGson;

    private static boolean sMockEnabled = false;

    public static void init(Context context, HttpManagerConfig config) {
        if (context == null) throw new NullPointerException("Context cant be null");
        if (config == null) throw new NullPointerException("HttpManagerConfig cant be null");

        sContext = context;
        sConfig = config;
        sGson = new Gson();

        HttpFileLogInterceptor logInterceptor = new HttpFileLogInterceptor(
                context,
                config.isRecordFullLogs() ? HttpFileLogInterceptor.LEVEL_BODY : HttpFileLogInterceptor.LEVEL_NONE,
                false,
                false
        );

        File cacheDir = context.getExternalCacheDir();
        int cacheSize = 10 * 1024 * 1024;

        OkHttpClient.Builder okHttpBuilder = new OkHttpClient.Builder();
        okHttpBuilder
                .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
                .cache(new Cache(cacheDir.getAbsoluteFile(), cacheSize))
                .addInterceptor(new CatchExceptionInterceptor())
                .addInterceptor(new SwitchGatewayInterceptor())
                .addInterceptor(new DefaultGatewayVerInterceptor())
                .addInterceptor(new LegacyHeaderInterceptor())
                .addInterceptor(new ColorGatewayInterceptor())
                .addInterceptor(new LegacyEncryptInterceptor())
                .addInterceptor(new GatewayHeaderInterceptor())
                .addInterceptor(new GatewayV2HeaderInterceptor())
                .addInterceptor(new DecryptResponseInterceptor())
                .addInterceptor(new ChangeBaseUrlInterceptor())
                .addInterceptor(new UserAgentInterceptor())
                .addInterceptor(new TimeoutInterceptor())
                .addNetworkInterceptor(logInterceptor)
                .pingInterval(10, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(2, 1, TimeUnit.MINUTES))
                .followRedirects(true);

        if (!config.isEnableHttp2()) {
            okHttpBuilder.protocols(Arrays.asList(Protocol.HTTP_1_1));
        }

        if (config.isRecordErrorLogs()) {
            //okHttpBuilder.eventListener(new NetworkEventListener(new FilePrinter(logInterceptor.getLogFile())));
        }

        httpClient = okHttpBuilder.build();

        httpClient.dispatcher().setMaxRequests(MAX_REQUESTS);
        httpClient.dispatcher().setMaxRequestsPerHost(MAX_REQUESTS_PER_HOST);

        sMainHandler = new Handler(Looper.getMainLooper());
    }

    public static Context getContext() {
        return sContext;
    }

    public static HttpManagerConfig getConfig() {
        return sConfig;
    }

    public static OkHttpClient getHttpClient() {
        return httpClient;
    }

    public static void post(final Object obj, Map<String, Object> params, final SimpleRequestCallback<String> callBack, String action) {
        post(obj, null, params, callBack, action);
    }

    /**
     * 默认为网关V1的网络请求，可以通过header里的参数控制
     * post 请求（无网络时，执行默认toast提示）
     *
     * @param obj
     * @param params   参数
     * @param callBack 网络请求回调
     * @param action   请求path或url
     */
    public static void post(final Object obj, Map<String, String> headers, Map<String, Object> params, final SimpleRequestCallback<String> callBack, String action) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            //非主线程
            Log.w(TAG, "requests in background thread", new IllegalStateException("requests must be posted in main thread!"));
        }

        if (callBack != null) {
            callStartOnMainThread(callBack);
        }

//        if (!Utils.isNetworkAvailable(getContext())) {
//            callNoNetworkOnMainThread(callBack);
//            return;
//        }

        CancelTag cancelTag = null;
        if (obj instanceof CancelTag) {
            cancelTag = (CancelTag) obj;
        }

        Request.Builder requestBuilder = makeRequest(cancelTag, headers, params, action);
        httpClient.newCall(requestBuilder.build()).enqueue(new GatewayResponseCallback(callBack, action, TAG));
    }

    private static Request.Builder makeRequest(CancelTag cancelTag, Map<String, String> headers, Map<String, Object> params, String action) {
        String url = getUrl(action);

        if (params == null) {
            params = new HashMap<>();
        }

        Request.Builder requestBuilder = new Request.Builder();
        String gatewayVersion = null;
        if (headers != null) {
            gatewayVersion = headers.get(HEADER_KEY_GATEWAY_VERSION);
        }
        if (!Objects.equals(gatewayVersion, HttpManager.HEADER_GATEWAY_COLOR)) {
            if (headers != null && HEADER_GATEWAY_NONE.equals(headers.get(HEADER_KEY_GATEWAY_VERSION))) {
                //未接网关用form data
                requestBuilder.post(getFormRequestBody(params));
            } else {
                if (headers != null && "true".equals(headers.get(HEADER_KEY_METHOD_GET))) {
                    requestBuilder.get();
                    url = appendParamsToUrl(url, params);
                } else {
                    //网关的用json
                    requestBuilder.post(getJsonRequestBody(params));
                }
            }
        }

        requestBuilder.addHeader("Content-Type", "application/json;charset=UTF-8")
                .addHeader(HEADER_KEY_ACTION, action)
                .url(url)
                .tag(params);

        //设置取消标记
        if (cancelTag != null) {
            requestBuilder.tag(CancelTag.class, cancelTag);
        }

        if (headers == null) {
            headers = new HashMap<>();
        }
        //设置网关版本，默认V1
        if (!headers.containsKey(HEADER_KEY_GATEWAY_VERSION)) {
            headers.put(HEADER_KEY_GATEWAY_VERSION, HEADER_GATEWAY_V1);
        }

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.header(entry.getKey(), entry.getValue());
        }
        return requestBuilder;
    }

    private static void callStartOnMainThread(final SimpleRequestCallback<String> callBack) {
        sMainHandler.post(new Runnable() {
            @Override
            public void run() {
                try {
                    callBack.onStart();
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                }
            }
        });
    }

    private static void callNoNetworkOnMainThread(final SimpleRequestCallback<String> callBack) {
        sMainHandler.post(new Runnable() {
            @Override
            public void run() {
                try {
                    callBack.onNoNetWork();
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                }
            }
        });
    }

    /**
     * baseUrl会在{@link ChangeBaseUrlInterceptor}中被重新修改
     *
     * @param action
     * @return
     */
    private static String getUrl(String action) {
        if (action != null && action.startsWith("http")) {
            return action;
        }
        GatewayNetEnvironment networkEnv = GatewayNetEnvironment.getCurrentEnv();
        Uri uri = Uri.parse(networkEnv.getBaseUrl())
                .buildUpon()
                .appendQueryParameter("appid", networkEnv.getAppId())
                .appendQueryParameter("api", action)
                .build();
        return uri.toString();
    }

    private static String appendParamsToUrl(String url, Map<String, Object> params) {
        if (TextUtils.isEmpty(url) || params == null) return url;
        Uri.Builder builder = Uri.parse(url).buildUpon();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            builder.appendQueryParameter(entry.getKey(), entry.getValue().toString());
        }
        return builder.build().toString();
    }

    public static RequestBody getJsonRequestBody(Map<String, Object> param) {
        MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
        return RequestBody.create(mediaType, sGson.toJson(param));
    }

    public static RequestBody getFormRequestBody(Map<String, Object> params) {
        FormBody.Builder formBodyBuilder = new FormBody.Builder();
        String key;
        Object value;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            key = entry.getKey();
            value = entry.getValue();
            if (value == null) {
                value = "";
            }
            if (key != null) {
                formBodyBuilder.add(key, String.valueOf(value));
            }
        }
        return formBodyBuilder.build();
    }

    /**
     * 发起未接网关网络请求
     */
    public static LegacyRequestBuilder legacy() {
        return new LegacyRequestBuilder();
    }

    public static ColorRequestBuilder color() {
        return new ColorRequestBuilder();
    }


    /**
     * 发起网关V2网络请求
     */
    public static GatewayV2RequestBuilder v2() {
        return new GatewayV2RequestBuilder();
    }

    /**
     * 取消网络请求
     *
     * @param tag
     */
    public static void cancel(CancelTag tag) {
        if (tag == null) return;

        for (Call call : httpClient.dispatcher().queuedCalls()) {
            if (tag.equals(call.request().tag(CancelTag.class)))
                call.cancel();
        }

        for (Call call : httpClient.dispatcher().runningCalls()) {
            if (tag.equals(call.request().tag(CancelTag.class)))
                call.cancel();
        }
    }

    public static String postSync(String action, Map<String, String> headers, Map<String, Object> params) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            Log.e(TAG, "requests in main thread", new IllegalStateException("requests must be posted in background thread!"));
        }

        Request.Builder requestBuilder = makeRequest(null, headers, params, action);

        final String url = getUrl(action);
        requestBuilder.url(url);

        Call call = httpClient.newCall(requestBuilder.build());
        String ret = "";
        long time = 0;
        try {
            Response response = call.execute();
            ret = response.body().string();
            time = response.receivedResponseAtMillis() - response.sentRequestAtMillis();
        } catch (IOException e) {
            Log.e(TAG, e.toString());
        }
        Log.d(TAG, action);
        Log.d(TAG, time + ret);

        return ret;
    }

    public static String postExecute(final Object obj, Map<String, String> headers, Map<String, Object> params, String action) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            //非主线程
            Log.w(TAG, "requests in background thread", new IllegalStateException("requests must be posted in main thread!"));
        }
        CancelTag cancelTag = null;
        if (obj instanceof CancelTag) {
            cancelTag = (CancelTag) obj;
        }

        Request.Builder requestBuilder = makeRequest(cancelTag, headers, params, action);
        try {
            Response response = httpClient.newCall(requestBuilder.build()).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void upload(String action, Map<String, String> headers, Map<String, Object> params, Map<String, File> fileParams, SimpleRequestCallback<String> callBack) {

        final String url = getUrl(action);

        if (params == null) {
            params = new HashMap<>();
        }

        MultipartBody.Builder bodybuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            bodybuilder.addFormDataPart(entry.getKey(), String.valueOf(entry.getValue()));
        }

        if (fileParams == null) {
            fileParams = new HashMap<>();
        }
        for (Map.Entry<String, File> entry : fileParams.entrySet()) {
            File file = entry.getValue();
            if (file == null) {
                callBack.onFailure(new HttpException(), "file is null");
                break;
            }
            bodybuilder.addFormDataPart(entry.getKey(), file.getName(), RequestBody.create(MediaType.parse(Utils.getMimeType(file)), file));
        }

        RequestBody requestBody = bodybuilder.build();
        String deviceToken = getConfig().getDeviceInfo().getPushDeviceToken();
        final Request.Builder requestBuilder = new Request.Builder()
                .addHeader("x-device-token", deviceToken)
                .addHeader(HEADER_KEY_ACTION, action)
                .addHeader(HEADER_KEY_CONNECT_TIMEOUT, "60")
                .addHeader(HEADER_KEY_WRITE_TIMEOUT, "60")
                .addHeader(HEADER_KEY_READ_TIMEOUT, "60")
                .url(url)
                .post(requestBody)
                .tag(params);


        if (headers == null) {
            headers = new HashMap<>();
        }
        //设置网关版本，默认V1
        if (!headers.containsKey(HEADER_KEY_GATEWAY_VERSION)) {
            headers.put(HEADER_KEY_GATEWAY_VERSION, HEADER_GATEWAY_V1);
        }

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }

        httpClient.newCall(requestBuilder.build()).enqueue(new GatewayResponseCallback(callBack, action, TAG));
    }

    public static void reset() {
        LegacyHeaderInterceptor.reset();
    }

    public static void resetLocation() {
        LegacyHeaderInterceptor.resetLocation();
    }

    public static void setMockEnabled(boolean mockEnabled) {
        sMockEnabled = mockEnabled;
    }

    public static boolean isMockEnabled() {
        return sMockEnabled;
    }
}