package com.jd.oa.elliptical;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

public class SuperEllipticalImageView extends AppCompatImageView {

    private PorterDuffXfermode DST_OUT = new PorterDuffXfermode(PorterDuff.Mode.DST_OUT);

    private Path mPath;
    private Paint mPathPaint;

    private SuperEllipsizedDrawer mEllipsizedDrawer;

    public SuperEllipticalImageView(@NonNull Context context) {
        super(context);
        init();
    }

    public SuperEllipticalImageView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SuperEllipticalImageView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }
        mPathPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPathPaint.setFilterBitmap(true);
        mPathPaint.setColor(Color.BLACK);
        mPathPaint.setXfermode(DST_OUT);

        mPath = new Path();

        mEllipsizedDrawer = new SuperEllipsizedDrawer();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (changed) {
            int width = getMeasuredWidth();
            int height = getMeasuredHeight();
            mEllipsizedDrawer.setSize(width, height);
            mEllipsizedDrawer.drawPath(mPath);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        try {
            int saveLayers = canvas.saveLayer(0, 0, getMeasuredWidth(), getMeasuredHeight(), null, Canvas.ALL_SAVE_FLAG);

            super.onDraw(canvas);

            canvas.drawPath(mPath, mPathPaint);
            canvas.restoreToCount(saveLayers);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }
}
