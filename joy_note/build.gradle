plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.chenenyu.router'
    id 'kotlin-parcelize'
}
android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled = true
    }
    viewBinding {
        enabled = true
    }
    namespace 'com.jd.oa.joy.note'
    lint {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
//    implementation 'androidx.appcompat:appcompat:1.6.1'

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

    implementation COMPILE_SUPPORT.design
    implementation COMPILE_SUPPORT.annotations
    implementation COMPILE_SUPPORT.recyclerview
    implementation COMPILE_COMMON.gson
    implementation COMPILE_SUPPORT.cardview

//    implementation 'com.chenenyu.router:router:1.5.2'
//    implementation 'com.chenenyu.router:compiler:1.5.1'

    implementation libs.lib.utils
    implementation project(':common')
    implementation libs.lib.asr

    implementation "com.github.bumptech.glide:glide:$glideVersion"
    implementation 'co.infinum:materialdatetimepicker-support:3.2.2'

    implementation 'com.hyman:flowlayout-lib:1.1.2'
    implementation "com.google.code.gson:gson:2.9.0"
    implementation ('io.socket:socket.io-client:2.1.0') {
        // excluding org.json which is provided by Android
        exclude group: 'org.json', module: 'json'
    }
    implementation "androidx.fragment:fragment-ktx:1.3.6"
    implementation 'com.airbnb.android:lottie:5.2.0'
}
