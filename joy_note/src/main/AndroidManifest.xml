<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <application
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        tools:targetApi="n">
        <activity
            android:name=".permission.SecuritySettingActivity"
            android:exported="false" />
        <activity
            android:name=".permission.AccessScopeActivity"
            android:exported="false" />
        <activity
            android:name=".permission.JoyNoteAuthorizeActivity"
            android:exported="false" />
        <activity
            android:name=".share.JoyNoteShareActivity"
            android:exported="false" />
        <activity
            android:name=".translate.RealTimeTranslateActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".record.RecordAudioActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".detail.JoyNoteDetailActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.JoyNoteSingleListActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".JoyNoteParticipatorListActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.player.MePlayActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" />

        <service
            android:name=".record.RecordAudioService"
            android:foregroundServiceType="microphone" />
        <service
            android:name=".notefloat.NoteFloatService"
            android:exported="false" />
        <service
            android:name=".translate.FloatingTranslateService"
            android:exported="false" />
        <service
            android:name="com.jd.oa.asr.AsrService"
            android:exported="false"
            android:foregroundServiceType="microphone" />
    </application>

</manifest>