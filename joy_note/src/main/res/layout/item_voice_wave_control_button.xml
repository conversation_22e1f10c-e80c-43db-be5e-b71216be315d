<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="8dp"
    app:cardElevation="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingTop="20dp"
            android:paddingBottom="16dp">

            <com.jd.oa.ui.voice.VoiceWaveView
                android:id="@+id/wave_view"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:gravity="end|center_vertical"
                android:paddingHorizontal="9dp"
                app:waveLineColor="#4C7CFF"
                app:waveLineSpace="4dp"
                app:waveLineWidth="1dp"
                app:waveMode="right_left_no_repeat" />

            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="#9D9D9D"
                android:textSize="16sp"
                tools:text="11:11:11" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            android:background="#FFF"
            android:gravity="center"
            android:paddingVertical="10dp">

            <FrameLayout
                android:id="@+id/widget_parent"
                android:layout_width="120dp"
                android:layout_height="48dp"
                android:layout_marginEnd="28dp"
                android:background="@drawable/bg_btn_joy_note_blue_semicircle">

                <TextView
                    android:id="@+id/tv_play_start"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/joynote_continue"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_play_continue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@drawable/joy_note_play_continue_icon"
                    android:visibility="gone" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/tv_complete"
                android:layout_width="120dp"
                android:layout_height="48dp"
                android:background="@drawable/bg_btn_joy_note_blue_line_semicircle"
                android:gravity="center">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:text="@string/icon_prompt_check"
                    android:textColor="#1869F5"
                    android:textSize="@dimen/JMEIcon_16" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/joynote_complete"
                    android:textColor="#1869F5"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>