<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="8dp">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:gravity="center"
        android:textColor="#4C7CFF" />


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginStart="8dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="#4C7CFF"
        tools:text="链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题链接标题" />
</LinearLayout>