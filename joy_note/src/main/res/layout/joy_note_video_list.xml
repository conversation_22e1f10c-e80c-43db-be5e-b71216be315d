<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/joy_note_filter_layout"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:gravity="center">

        <com.jd.oa.joy.note.compunent.JoyNoteSelectBtn
            android:id="@+id/joy_note_mine_btn"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="21dp"
            android:layout_marginEnd="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:selectedText="@string/joynote_home_mycreate"
            tools:layout_width="90dp" />

        <com.jd.oa.joy.note.compunent.JoyNoteSelectBtn
            android:id="@+id/joy_note_creator_btn"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/joy_note_mine_btn"
            app:layout_constraintTop_toTopOf="parent"
            app:selectedText="@string/joynote_create"
            tools:layout_width="72dp" />

        <com.jd.oa.joy.note.compunent.JoyNoteSelectBtn
            android:id="@+id/joy_note_sender_btn"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/joy_note_creator_btn"
            app:layout_constraintTop_toTopOf="parent"
            app:selectedText="@string/joynote_sender"
            tools:layout_width="72dp" />

        <TextView
            android:id="@+id/reset_btn"
            android:layout_width="46dp"
            android:layout_height="52dp"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="0dp"
            android:layout_marginEnd="0dp"
            android:gravity="center"
            android:text="@string/joynote_reset"
            android:textColor="#4C7CFF"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.jd.oa.pulltorefresh.PullToRefreshLayout
        android:id="@+id/joy_note_main_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/video_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:overScrollMode="never"
            android:scrollbars="none" />
    </com.jd.oa.pulltorefresh.PullToRefreshLayout>
</LinearLayout>