<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="78dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/video_cover_card"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginStart="16dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/video_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/joy_note_list_video_def"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/video_time"
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:layout_gravity="bottom|end"
            android:background="@drawable/bg_btn_joy_note_time"
            android:gravity="center"
            android:paddingStart="4dp"
            android:paddingEnd="4dp"
            android:textColor="@color/white"
            android:textSize="8dp"
            tools:ignore="SpUsage"
            tools:text="10:29" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/video_title"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:singleLine="true"
        android:textColor="#1B1B1B"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@id/joy_note_more_btn"
        app:layout_constraintStart_toEndOf="@id/video_cover_card"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage"
        tools:text="TEST_TEST_TEST_TEST_TEST_TEST_TEST_TEST_TEST_" />

    <TextView
        android:id="@+id/video_author"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:maxWidth="80dp"
        android:singleLine="true"
        android:textColor="#6A6A6A"
        android:textSize="12dp"
        app:layout_constraintStart_toEndOf="@id/video_cover_card"
        app:layout_constraintTop_toBottomOf="@id/video_title"
        tools:ignore="SpUsage"
        tools:text="TEST" />

    <View
        android:id="@+id/video_flag"
        android:layout_width="1dp"
        android:layout_height="12dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:background="#CECECE"
        android:gravity="start|center_vertical"
        app:layout_constraintLeft_toRightOf="@id/video_author"
        app:layout_constraintTop_toBottomOf="@id/video_title"
        tools:ignore="SpUsage" />

    <TextView
        android:id="@+id/video_date"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6dp"
        android:ellipsize="none"
        android:gravity="start|center_vertical"
        android:maxWidth="130dp"
        android:singleLine="true"
        android:textColor="#6A6A6A"
        android:textSize="12dp"
        app:layout_constraintStart_toEndOf="@id/video_flag"
        app:layout_constraintTop_toBottomOf="@id/video_title"
        tools:ignore="SpUsage"
        tools:text="TEST" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="4dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="2dp"
        app:layout_constraintStart_toEndOf="@+id/video_date"
        app:layout_constraintTop_toTopOf="@+id/video_date"
        app:layout_constraintBottom_toBottomOf="@id/video_date">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/if_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/JMEIcon_12"
            android:textColor="#F63218"
            tools:text="@string/icon_exit"/>

        <TextView
            android:id="@+id/tv_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:textSize="12sp"
            android:textColor="#F63218"
            tools:text="录制中"/>
    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/joy_note_more_btn"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="end|center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:text="@string/icon_tabbar_more"
        android:textColor="#9D9D9D"
        android:textSize="@dimen/JMEIcon_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

</androidx.constraintlayout.widget.ConstraintLayout>
