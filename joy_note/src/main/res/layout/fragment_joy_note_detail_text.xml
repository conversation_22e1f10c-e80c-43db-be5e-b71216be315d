<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_search_layout"
        android:orientation="vertical">

        <com.zhy.view.flowlayout.TagFlowLayout
            android:id="@+id/flowlayout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            android:visibility="gone"
            app:max_select="1" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="vertical"
            tools:itemCount="5"
            tools:listitem="@layout/joy_note_item_detail_text_list" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/joy_note_book_icon" />

        <TextView
            android:id="@+id/load_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/libui_content_empty"
            android:textColor="#6A6A6A"
            android:textSize="12sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_search_layout"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:background="#D9D9D9"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="4dp"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:background="@drawable/bg_eidt_joy_note_corner"
            android:gravity="center_vertical"
            android:paddingHorizontal="12dp">

            <com.jd.oa.ui.IconFontView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:gravity="center"
                android:text="@string/icon_general_search"
                android:textColor="#333"
                android:textSize="@dimen/JMEIcon_16"
                tools:ignore="SpUsage" />

            <EditText
                android:id="@+id/et_search_input"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1"
                android:background="@null"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:hint="@string/joy_note_detail_search_placeholder"
                android:imeOptions="actionNone"
                android:paddingVertical="6dp"
                android:singleLine="true"
                android:textColorHint="#BFC1C4"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_search_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0/0"
                android:textSize="14dp" />
        </LinearLayout>


        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_search_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:text="@string/icon_direction_down"
            android:textColor="#333"
            android:textSize="@dimen/JMEIcon_26"
            tools:ignore="SpUsage" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_search_last"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="6dp"
            android:gravity="center"
            android:text="@string/icon_direction_up"
            android:textColor="#333"
            android:textSize="@dimen/JMEIcon_26"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/tv_search_complete"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="4dp"
            android:gravity="center"
            android:paddingHorizontal="4dp"
            android:text="@string/joy_note_detail_search_done"
            android:textColor="#333"
            android:textSize="16sp" />
    </LinearLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/return_speaking_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_marginBottom="50dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/jdme_note_bg_audio_record"
        android:visibility="gone"
        app:cardCornerRadius="20dp"
        app:cardElevation="6dp"
        app:cardPreventCornerOverlap="true"
        app:cardUseCompatPadding="true"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center"
            android:paddingHorizontal="12dp">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_tabbar_taget_de"
                android:textSize="@dimen/JMEIcon_16"
                android:textColor="#1869F5" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#1869F5"
                android:textSize="14sp"
                android:layout_marginStart="4dp"
                android:text="@string/joynote_mentions_return_speaking_position" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>