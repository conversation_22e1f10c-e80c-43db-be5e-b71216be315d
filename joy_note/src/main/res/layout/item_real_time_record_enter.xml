<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="72dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_real_time_record"
            android:paddingHorizontal="12dp"
            android:paddingVertical="16dp">

            <ImageView
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"
                android:src="@drawable/icon_real_time_record" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/joynote_real_time_record_title"
                android:textColor="#333333"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <com.jd.oa.ui.IconFontView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignEnd="@+id/card_container"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="-10dp"
        android:layout_marginBottom="-10dp"
        android:text="@string/icon_edit_voice3"
        android:textColor="#33FFFFFF"
        android:textSize="70sp" />
</RelativeLayout>

