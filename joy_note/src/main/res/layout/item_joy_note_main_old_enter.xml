<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/collapse_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="top|center_horizontal"
    android:baselineAligned="false"
    android:orientation="horizontal"
    android:paddingTop="56dp"
    android:paddingBottom="20dp">

    <LinearLayout
        android:id="@+id/list_create"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_btn_joy_note_red"
            android:gravity="center"
            android:orientation="vertical">

            <com.jd.oa.ui.IconFontView
                tools:ignore="SpUsage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/icon_edit_addcontact"
                android:textColor="#fff"
                android:textSize="@dimen/JMEIcon_22" />
        </LinearLayout>

        <TextView
            tools:ignore="SpUsage"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginTop="10dp"
            android:text="@string/joynote_my_create_title"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/list_receive"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_btn_joy_note_blue"
            android:gravity="center"
            android:orientation="vertical">

            <com.jd.oa.ui.IconFontView
                tools:ignore="SpUsage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/icon_hj_received"
                android:textColor="#fff"
                android:textSize="@dimen/JMEIcon_22" />
        </LinearLayout>

        <TextView
            tools:ignore="SpUsage"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginTop="10dp"
            android:text="@string/joynote_home_received"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/list_recycle"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_btn_joy_note_oringe"
            android:gravity="center"
            android:orientation="vertical">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/ift_tab_icon"
                tools:ignore="SpUsage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/icon_edit_delete"
                android:textColor="#fff"
                android:textSize="@dimen/JMEIcon_22" />
        </LinearLayout>

        <TextView
            tools:ignore="SpUsage"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginTop="10dp"
            android:text="@string/joynote_home_trash"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

</LinearLayout>