<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="scope"
            type="com.jd.oa.joy.note.model.ScopeContent" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="@{scope.iconString}"
            android:textColor="@{scope.iconColor}"
            android:textSize="@dimen/JMEIcon_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/icon_general_lock"
            tools:textColor="@color/me_text_link" />

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="13dp"
            android:layout_marginStart="12dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/icon"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@{scope.title}"
                android:textColor="@color/color_1B1B1B"
                android:textSize="16sp"
                tools:text="仅协作者可见" />

            <TextView
                android:id="@+id/desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@{scope.desc}"
                android:textColor="@color/color_6A6A6A"
                android:textSize="14sp"
                tools:text="仅慧记协作者可通过链接访问" />
        </LinearLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:text="@string/icon_prompt_check"
            android:textColor="@color/color_FFF63218"
            android:textSize="@dimen/JMEIcon_14"
            android:textStyle="bold"
            android:visibility="@{scope.check ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <View
            android:id="@+id/divide"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/color_F0F1F2"
            app:layout_constraintLeft_toLeftOf="@+id/linearLayout"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>