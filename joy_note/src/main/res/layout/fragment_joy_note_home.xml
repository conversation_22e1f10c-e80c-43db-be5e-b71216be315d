<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapse_tool_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:minHeight="44dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <LinearLayout
                android:id="@+id/collapse_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="56dp"
                android:layout_marginHorizontal="16dp"
                android:orientation="horizontal"
                android:paddingBottom="20dp">

                <include
                    android:id="@+id/real_time_record"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_72"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    layout="@layout/item_real_time_record_enter" />

                <include
                    android:id="@+id/real_time_translate"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_72"
                    android:layout_weight="1"
                    layout="@layout/item_real_time_translate_enter" />
            </LinearLayout>

            <com.jd.oa.joy.note.compunent.JoyNoteTitleBar
                android:id="@+id/main_toolbar"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                app:layout_collapseMode="pin" />

            <com.jd.oa.joy.note.compunent.JoyNoteTitleBarTab
                android:id="@+id/main_toolbar_tab"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                app:layout_collapseMode="pin" />
        </com.google.android.material.appbar.CollapsingToolbarLayout>

        <com.jd.oa.joy.note.compunent.JoyNoteTab
            android:id="@+id/tab_layout"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:background="@color/white"
            app:tabIndicator="@drawable/bg_tab_joy_note"
            app:tabIndicatorColor="#FFFE3B30"
            app:tabMode="auto"
            app:tabRippleColor="@android:color/transparent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F5F5F5" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/main_fab"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_gravity="center"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="86dp"
        android:background="@drawable/bg_btn_joy_note_oval_red"
        android:elevation="5dp"
        android:outlineAmbientShadowColor="#ffF63218"
        android:outlineSpotShadowColor="#ffF63218"
        android:scaleType="center"
        android:src="@drawable/joynote_new_btn"
        android:translationZ="2dp"
        app:borderWidth="0dp"
        app:fabCustomSize="44dp"
        app:layout_anchor="@id/view_pager"
        app:layout_anchorGravity="bottom|end"
        app:maxImageSize="18dp"
        tools:ignore="ContentDescription,UnusedAttribute" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>