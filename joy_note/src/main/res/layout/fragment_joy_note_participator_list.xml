<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/jdme_view_joy_note_action_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginTop="6dp"
        android:visibility="gone">

        <EditText
            android:id="@+id/et_search"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/bg_eidt_joy_note_corner"
            android:hint="搜索姓名、erp"
            android:paddingStart="30dp"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_cancel"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:gravity="center"
            android:text="@string/icon_general_search"
            android:textColor="#BFC1C4"
            tools:ignore="SpUsage"
            android:textSize="@dimen/JMEIcon_16"
            app:layout_constraintBottom_toBottomOf="@id/et_search"
            app:layout_constraintStart_toStartOf="@id/et_search"
            app:layout_constraintTop_toTopOf="@id/et_search" />


        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="16dp"
            android:gravity="center"
            android:text="取消"
            android:textColor="#232930"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_participators"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:itemCount="10"
        tools:listitem="@layout/joy_note_item_participator" />

</LinearLayout>