<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="16dp">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_text"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:itemCount="2"
        tools:listitem="@layout/joy_note_item_record_list" />

    <androidx.cardview.widget.CardView
        android:id="@+id/button_location"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:cardCornerRadius="100dp"
        app:cardElevation="8dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/icon_direction_doubledown"
            android:textColor="#1869F5"
            android:textSize="@dimen/JMEIcon_16" />
    </androidx.cardview.widget.CardView>

</FrameLayout>

