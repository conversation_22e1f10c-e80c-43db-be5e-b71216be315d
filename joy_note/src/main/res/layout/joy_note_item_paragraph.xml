<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_index"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:textColor="@color/joy_note_timeline_paragraph_text_color"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="start"
        tools:text="片段9"/>
    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="#6A6A6A"
        tools:text="02:00开始"/>
</LinearLayout>