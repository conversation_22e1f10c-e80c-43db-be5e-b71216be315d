<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- 顶部布局 -->
    <RelativeLayout
        android:id="@+id/topFunctionLayer"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="@drawable/jdliv_home_bg_drawable"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/me_close_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="20dp"
                android:src="@drawable/player_chevron_left" />

            <TextView
                android:id="@+id/me_video_live_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical|left"
                android:maxWidth="125dp"
                android:singleLine="true"
                tools:text="标题"
                android:textColor="@color/white"
                android:textSize="15dp" />

        </LinearLayout>

        <!-- 小窗按钮 -->
        <TextView
            android:id="@+id/playback_more_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_btn_joy_note_speed_bg"
            android:gravity="center"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="3dp"
            android:paddingBottom="3dp"
            android:text="@string/joynote_speed_btn"
            android:textColor="#FFFFFF"
            android:textSize="12dp"
            android:visibility="visible"
            tools:ignore="SpUsage" />
    </RelativeLayout>


    <!-- 底部栏-->
    <LinearLayout
        android:id="@+id/ll_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="74dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/jdliv_home_bg_drawable_reverse"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="visible">

        <RelativeLayout
            android:id="@+id/app_video_play_rl"
            android:layout_width="67dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical">

            <ImageView
                android:id="@+id/app_video_play"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_centerInParent="true"
                android:layout_marginTop="25dp"
                android:contentDescription="@string/un_video_screen_pause"
                android:src="@drawable/vd_play_video" />
        </RelativeLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="20dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/app_video_currentTime_full"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="18dp"
                android:text="00:00"
                android:textColor="@color/white"
                android:textSize="11dp" />


            <SeekBar
                android:id="@+id/app_video_seekBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/app_video_currentTime_full"
                android:maxHeight="4dp"
                android:minHeight="4dp"
                android:paddingLeft="10dp"
                android:paddingTop="4dp"
                android:paddingRight="10dp"
                android:paddingBottom="5dp"
                android:splitTrack="false"
                android:thumb="@drawable/seekbar_thumb" />

            <TextView
                android:id="@+id/app_video_endTime_full"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_gravity="right"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="10dp"
                android:text="00:00"
                android:textColor="@color/white"
                android:textSize="11dp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/app_video_fullscreen"
            android:layout_width="48dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:visibility="visible">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="4dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/un_video_screen_v_to_h"
                android:contentDescription="@string/un_video_screen_change" />
        </RelativeLayout>


        <ImageView
            android:id="@+id/iv_bottom_voice_copy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="18dp"
            android:background="@drawable/video_player_voice_on"
            android:visibility="gone" />
    </LinearLayout>
</RelativeLayout>