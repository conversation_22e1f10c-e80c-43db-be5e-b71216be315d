<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp">

        <TextView
            android:id="@+id/tv_time_title"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="4dp"
            android:gravity="center_vertical"
            android:minWidth="56dp"
            android:text="@string/joy_note_detail_info_time"
            android:textColor="#333"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#333"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_title"
            app:layout_constraintStart_toEndOf="@id/tv_time_title"
            app:layout_constraintTop_toTopOf="@id/tv_time_title"
            tools:text="2023年7月30日 18:00" />


        <View
            android:id="@+id/v_line"
            android:layout_width="1dp"
            android:layout_height="10dp"
            android:layout_marginStart="8dp"
            android:background="#E6E6E6"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_title"
            app:layout_constraintStart_toEndOf="@id/tv_time"
            app:layout_constraintTop_toTopOf="@id/tv_time_title" />

        <TextView
            android:id="@+id/tv_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="#333"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_title"
            app:layout_constraintStart_toEndOf="@id/v_line"
            app:layout_constraintTop_toTopOf="@id/tv_time_title"
            tools:text="120分30秒" />

        <TextView
            android:id="@+id/tv_owner_title"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="4dp"
            android:gravity="center_vertical"
            android:minWidth="56dp"
            android:text="@string/joy_note_detail_info_owner"
            android:textColor="#333"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_time_title" />

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/iv_owner_photo"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@color/black"
            app:layout_constraintBottom_toBottomOf="@id/tv_owner_title"
            app:layout_constraintStart_toEndOf="@id/tv_owner_title"
            app:layout_constraintTop_toTopOf="@id/tv_owner_title" />

        <TextView
            android:id="@+id/tv_owner"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textColor="#333"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_owner_title"
            app:layout_constraintStart_toEndOf="@id/iv_owner_photo"
            app:layout_constraintTop_toTopOf="@id/tv_owner_title"
            tools:text="宋瑞进" />

        <TextView
            android:id="@+id/tv_participator_title"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="4dp"
            android:gravity="center_vertical"
            android:minWidth="56dp"
            android:text="@string/joy_note_detail_info_participator"
            android:textColor="#333"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_owner_title" />


        <com.jd.oa.joy.note.compunent.JoyNoteAvatarGroup
            android:id="@+id/view_participator"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:avatarHeight="28dp"
            app:avatarWidth="28dp"
            app:borderWidth="0dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_participator_title"
            app:layout_constraintEnd_toStartOf="@id/iv_participator_more"
            app:layout_constraintStart_toEndOf="@id/tv_participator_title"
            app:layout_constraintTop_toTopOf="@id/tv_participator_title"
            app:maxTotal="10"
            app:offset="4dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_participator_more"
            android:layout_width="24dp"
            android:layout_height="36dp"
            android:gravity="end|center_vertical"
            android:text="@string/icon_direction_right"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_18"
            tools:ignore="SpUsage"
            app:layout_constraintBottom_toBottomOf="@id/tv_participator_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_participator_title" />

        <TextView
            android:id="@+id/tv_title_schedule"
            style="@style/JoyNoteTitleTextStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:paddingVertical="8dp"
            android:text="@string/joynote_detail_search_schedule1"
            app:layout_constraintTop_toBottomOf="@id/tv_participator_title" />

        <LinearLayout
            android:id="@+id/ll_schedule"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="8dp"
            android:background="@drawable/bg_joy_note_corner_gray"
            android:orientation="vertical"
            android:paddingHorizontal="16dp"
            android:paddingVertical="12dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title_schedule">

            <TextView
                android:id="@+id/tv_schedule_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#232930"
                android:textSize="16dp"
                android:textStyle="bold"
                tools:text="UI设计宣讲会议" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:text="@string/icon_general_time"
                    android:textColor="#666"
                    tools:ignore="SpUsage"
                    android:textSize="@dimen/JMEIcon_14" />

                <TextView
                    android:id="@+id/tv_schedule_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="#333"
                    tools:text="2022年05月17 19:30-19:30" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/joy_note_see_details"
                    android:textColor="#4C7CFF" />
            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_title_link"
            style="@style/JoyNoteTitleTextStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:paddingVertical="8dp"
            android:text="@string/joynote_detail_search_document1"
            app:layout_constraintTop_toBottomOf="@id/ll_schedule" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_links"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tv_title_link"
            tools:itemCount="10"
            tools:listitem="@layout/joy_note_item_detail_link_list" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>