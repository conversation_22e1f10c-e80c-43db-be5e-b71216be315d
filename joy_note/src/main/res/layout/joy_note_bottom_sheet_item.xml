<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/sheet_item"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:clickable="true"
    android:focusable="true">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/sheet_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/JMEIcon_18"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="0dp"
        android:gravity="center"
        android:text="@string/icon_edit_addbelow"
        android:textColor="#1B1B1B"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

    <TextView
        android:id="@+id/sheet_item_text"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="14dp"
        android:gravity="center"
        tools:text="@string/joynote_delete"
        android:textColor="#000000"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/sheet_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/select_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/JMEIcon_18"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="14dp"
        android:gravity="center"
        android:text="@string/icon_smallcheck"
        android:textColor="#F63218"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible"
        tools:ignore="SpUsage" />

    <View
        android:id="@+id/sheet_item_gap"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_gravity="bottom"
        android:layout_marginEnd="0dp"
        android:layout_marginStart="0dp"
        android:background="#DEE0E3"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/sheet_item_text" />
</androidx.constraintlayout.widget.ConstraintLayout>