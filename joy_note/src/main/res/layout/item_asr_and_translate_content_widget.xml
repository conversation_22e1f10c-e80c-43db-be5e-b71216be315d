<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.jd.oa.joy.note.translate.StreamOutputText
        android:id="@+id/in_asr_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/guideline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <LinearLayout
        android:id="@+id/result_hint_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/animation_view"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            app:lottie_autoPlay="false"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/voice_record" />

        <TextView
            android:id="@+id/tv_tip_speak"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="8dp"
            android:text="@string/joynote_start_recording_prompt"
            android:textColor="#9D9D9D"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/animation_view" />
    </LinearLayout>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <com.jd.oa.joy.note.translate.StreamOutputText
        android:id="@+id/vs_translate_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_joy_note_top_corner"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline" />

</androidx.constraintlayout.widget.ConstraintLayout>