<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_centerInParent="true"
        android:layout_width="match_parent"
        android:layout_height="50dp">

    <TextView
        android:id="@+id/speedItemText"
        android:gravity="center"
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="#3C4056"
        android:textSize="14dp"
        android:shadowRadius="3.0"
        />

    </RelativeLayout>

</RelativeLayout>