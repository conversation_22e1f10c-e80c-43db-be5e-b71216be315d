<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="28dp"
        android:background="@drawable/bg_fragment_joy_note_timeline_shadow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/top_frame_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_fragment_joy_note_timeline"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/icon_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:padding="12dp"
                android:text="@string/icon_prompt_close"
                android:textColor="#1B1B1B"
                android:textSize="@dimen/JMEIcon_20" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center"
                android:paddingVertical="10dp"
                android:text="@string/joynote_translate_dialog_title_original"
                android:textColor="#1B1B1B"
                android:textSize="16sp"
                android:textStyle="bold" />
        </RelativeLayout>


        <ScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/top_frame_layout">

            <TextView
                android:id="@+id/original_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#1B1B1B"
                android:lineSpacingExtra="5dp"
                android:textSize="14sp" />
        </ScrollView>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>