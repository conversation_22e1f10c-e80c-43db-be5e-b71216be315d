<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".share.JoyNoteShareActivity">

    <RelativeLayout
        android:id="@+id/relative_container"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:paddingHorizontal="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:focusable="true"
            android:includeFontPadding="false"
            android:text="@string/icon_direction_left"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joynote_share"
            android:textColor="#FF242931"
            android:textSize="18dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_edit_addcontact"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22" />
    </RelativeLayout>

    <com.jd.oa.ui.widget.JoyListEmptyView
        android:id="@+id/empty_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:emptyImage="@drawable/jdme_contact_list_empty_icon"
        app:emptyText="@string/joynote_contact_empty"
        app:emptyTextSize="12sp"
        app:emptyTextColor="#6A6A6A"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/relative_container" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycle_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/relative_container" />

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginBottom="21dp"
        android:gravity="center|start"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/confirm"
        app:layout_constraintStart_toStartOf="parent">

        <com.jd.oa.ui.JoyCheckBox
            android:id="@+id/minutes_assistant_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="8dp"
            app:selectBackground="@drawable/jdme_select_checkbox"
            app:text="@string/joynote_share_send_minutes_assistant"
            app:text_color="#9D9D9D"
            app:unselectBackground="@drawable/jdme_unselect_checkbox" />

        <com.jd.oa.ui.JoyCheckBox
            android:id="@+id/dd_chat_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:selectBackground="@drawable/jdme_select_checkbox"
            app:text="@string/joynote_share_send_dd_chat"
            app:text_color="#9D9D9D"
            app:unselectBackground="@drawable/jdme_unselect_checkbox" />
    </LinearLayout>

    <TextView
        android:id="@+id/confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:background="@drawable/jdme_text_view_round_bg"
        android:paddingHorizontal="24dp"
        android:paddingVertical="8dp"
        android:text="@string/me_confirm"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/linearLayout"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>