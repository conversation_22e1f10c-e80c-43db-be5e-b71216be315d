<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingVertical="8dp">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/avatar"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_marginVertical="4dp"
        android:background="@color/transparent"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/department"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#9D9D9D"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/linear_container"
        app:layout_constraintStart_toEndOf="@+id/avatar"
        tools:text="综合业务设计组 交互设计师" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/linear_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintLeft_toRightOf="@+id/avatar"
        app:layout_constraintRight_toLeftOf="@id/permission_edit"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/realName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/label"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="张三" />

        <TextView
            android:id="@+id/label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="4dp"
            android:paddingVertical="2dp"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/realName"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="文档"
            tools:textColor="@color/color_F63218"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/flag_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center"
        android:text="@string/icon_padding_caredown"
        android:textColor="#9D9D9D"
        android:textSize="@dimen/JMEIcon_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/permission_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="2dp"
        android:clickable="true"
        android:focusable="true"
        android:textColor="@color/color_1B1B1B"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/flag_down"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="可编辑" />

</androidx.constraintlayout.widget.ConstraintLayout>