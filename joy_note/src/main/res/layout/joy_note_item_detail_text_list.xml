<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="20dp"
    tools:ignore="SpUsage">

    <ImageView
        android:id="@+id/iv_photo"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"
        tools:src="@color/black" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="#1B1B1B"
        android:textSize="14dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/iv_photo"
        app:layout_constraintStart_toEndOf="@id/iv_photo"
        app:layout_constraintTop_toTopOf="@id/iv_photo"
        tools:text="丁家辰" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="#666"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_photo"
        app:layout_constraintStart_toEndOf="@id/tv_name"
        app:layout_constraintTop_toTopOf="@id/iv_photo"
        tools:text="00:00" />

    <LinearLayout
        android:id="@+id/ll_merge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_photo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_photo"
        tools:visibility="visible">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="@string/icon_general_merge2"
            android:textColor="#666666"
            android:textSize="@dimen/JMEIcon_18" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"
            android:text="@string/joynote_extract_paragraph"
            android:textColor="#666666" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/parent_frame_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_photo">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selector_joy_note_detail_text"
            android:lineSpacingExtra="2dp"
            android:paddingHorizontal="4dp"
            android:paddingVertical="8dp"
            android:textColor="#1B1B1B"
            android:textSize="15dp"
            tools:text="云文档（JoySpace）汇集了协同文档、协同表格等创作工具，提供了安全、强大的云端存储和内容管理能力，多人协同内容永远是最新的，并可灵活管理各类权限，让团队协同与知识管理既高效又安全。让团队协同与知识管理" />

        <!--这是盖在上面的专门显示关键词的一层-->
        <TextView
            android:id="@+id/tv_content_keyword"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="2dp"
            android:paddingHorizontal="4dp"
            android:paddingVertical="8dp"
            android:textColor="#00FFFFFF"
            android:textSize="15dp"
            tools:text="云文档（JoySpace）汇集了协同文档、协同表格等创作工具，提供了安全、强大的云端存储和内容管理能力，多人协同内容永远是最新的，并可灵活管理各类权限，让团队协同与知识管理既高效又安全。" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/parent_frame_layout"
        app:layout_constraintEnd_toEndOf="@id/parent_frame_layout"
        android:background="@drawable/joy_note_translate_loading_color"
        android:paddingBottom="11dp"
        android:visibility="invisible"
        tools:visibility="visible">
        <ProgressBar
            android:id="@+id/pb_progress"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="12dp"
            style="@style/progressBarRed"/>
        <ImageView
            android:id="@+id/iv_failed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:visibility="invisible"
            android:src="@drawable/joynote_translate_fail_icon"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>