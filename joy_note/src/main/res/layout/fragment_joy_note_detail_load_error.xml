<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="SpUsage">


    <com.jd.oa.joy.note.compunent.JoyNoteTitleBar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="44dp" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_error"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/joynote_load_error"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="@string/joy_note_detail_load_error"
            android:textColor="#8F959E"
            android:textSize="14dp" />


        <TextView
            android:id="@+id/tv_refresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@drawable/selector_joy_note_btn_gray_line"
            android:gravity="center"
            android:minWidth="80dp"
            android:paddingHorizontal="12dp"
            android:paddingVertical="9dp"
            android:text="@string/joynote_detail_refresh"
            android:textColor="#232930"
            android:textSize="14dp" />


    </LinearLayout>

</RelativeLayout>