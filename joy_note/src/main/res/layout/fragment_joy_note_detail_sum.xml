<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_summary_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="1dp"
            android:padding="16dp"
            android:textColor="#333"
            android:textSize="15dp"
            tools:ignore="SpUsage"
            tools:text="听课的时候能把老师说的话记录下来转换成文字，效率真的不要高了太多，而且老板或者客户跟你说的话，你也可以转化成文字，真的是不要太方便。" />
    </ScrollView>


    <LinearLayout
        android:id="@+id/ll_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:src="@drawable/joynote_is_recording"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/joynote_empty_sum"
            android:textColor="#8F959E"
            android:textSize="14dp"
            tools:ignore="SpUsage" />
    </LinearLayout>

</FrameLayout>