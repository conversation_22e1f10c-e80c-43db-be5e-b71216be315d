<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="SpUsage">


    <com.jd.oa.joy.note.compunent.JoyNoteTitleBar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="44dp" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/joynote_no_permission"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:textColor="#8F959E"
            tools:text="你没有该文档访问权限\n所有者：@宋瑞进,@宋瑞进,@宋瑞进" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="45dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_back_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_btn_joy_note_gray_line"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:paddingVertical="9dp"
                android:text="@string/joynote_detail_placeholder_back"
                android:textColor="#232930"
                android:textSize="14dp" />


            <TextView
                android:id="@+id/tv_request_permission"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:background="@drawable/selector_joy_note_btn_gray_line_enable"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:paddingVertical="9dp"
                android:text="@string/joynote_detail_placeholder_apply_permission"
                android:textColor="@drawable/selector_joy_note_btn_enable_text_color"
                android:textSize="14dp"
                tools:enabled="false" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>