<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@color/white"
    app:layout_collapseMode="pin"
    tools:ignore="SpUsage">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/joy_note_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="@string/icon_direction_left"
        android:textColor="#1B1B1B"
        tools:ignore="SpUsage"
        android:textSize="@dimen/JMEIcon_22"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/joy_note_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:maxWidth="150dp"
            android:singleLine="true"
            android:textColor="#1B1B1B"
            android:textSize="18dp"
            android:textStyle="bold"
            tools:text="张三的慧记张三的慧记张三的慧记张三的慧记" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/joy_note_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:gravity="center"
            android:text="@string/icon_hj_edit"
            android:textColor="#6A6A6A"
            android:textSize="@dimen/JMEIcon_18"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/joy_note_share"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/icon_general_share1"
        android:textColor="#333"
        android:textSize="@dimen/JMEIcon_22"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/joy_note_more"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/joy_note_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="@string/icon_tabbar_more"
        android:textColor="#333"
        android:textSize="@dimen/JMEIcon_22"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>