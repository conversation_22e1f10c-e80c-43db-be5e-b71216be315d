<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/float_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/bg_float_joy_note_real_translate"
    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="8dp">

    <FrameLayout
        android:id="@+id/top_control_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/close_float_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_prompt_close"
            android:textColor="#DCDEE0"
            android:textSize="@dimen/JMEIcon_20" />

        <ImageView
            android:id="@+id/enlarge_float_view"
            android:layout_width="@dimen/JMEIcon_20"
            android:layout_height="@dimen/JMEIcon_20"
            android:layout_gravity="end"
            android:src="@drawable/joy_note_pad_independent_view" />
    </FrameLayout>

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="65dp"
        android:layout_marginTop="4dp"
        android:clickable="false"
        android:focusable="false"
        android:scrollbars="none"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/top_control_container">

        <TextView
            android:id="@+id/tv_translate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:lineSpacingExtra="5dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:text="@string/jdme_auth_ellipsis"
            android:textColor="#DCDEE0"
            android:textSize="14sp"
            tools:text="When listening to the class, you can record what the teacher said and convert it into words. The efficiency is really not too high. Moreover, you " />

    </ScrollView>

    <LinearLayout
        android:id="@+id/warn_tip_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scroll_view"
        tools:visibility="visible">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/icon_padding_infocirclecircle"
            android:textColor="#FBB731"
            android:textSize="@dimen/JMEIcon_16" />

        <TextView
            android:id="@+id/tip_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="4dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#FBB731"
            android:textSize="14sp"
            tools:text="录制中断，请返回主界面，手动开启继续录制" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>