<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/tip_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFF8EA"
        android:visibility="gone"
        tools:visibility="visible"
        android:orientation="horizontal"
        android:paddingHorizontal="12dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="4dp"
            android:text="@string/icon_padding_infocirclecircle"
            android:textColor="#FBB731"
            android:textSize="@dimen/JMEIcon_16" />

        <TextView
            android:id="@+id/tip_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginVertical="8dp"
            android:layout_weight="1"
            android:textColor="#C99227"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="录制中断，请手动开启继续录制" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/interrupt_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="#C99227"
            android:textSize="@dimen/JMEIcon_16"
            android:text="@string/icon_prompt_close"/>
    </LinearLayout>
</merge>
