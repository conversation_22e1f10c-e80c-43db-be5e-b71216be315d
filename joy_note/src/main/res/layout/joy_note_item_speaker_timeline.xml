<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="timeline"
            type="com.jd.oa.joy.note.model.SpeakerTimeline" />

        <variable
            name="recordDuration"
            type="Long" />

        <variable
            name="proportion"
            type="int" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="8dp"
        android:paddingStart="8dp"
        android:paddingEnd="16dp">

        <com.jd.oa.joy.note.compunent.ShapeWavingView
            android:id="@+id/waving_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeWaveColor="#74A5F9"
            app:shapeWaveInitState="pause"
            app:shapeWaveLength="8dp"
            app:shapeWaveLineNum="2"
            app:shapeWaveLineWidth="1dp"
            app:shapeWaveShape="superEllipse">
            <com.jd.oa.elliptical.SuperEllipticalImageView
                android:id="@+id/avatar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerCrop"
                android:background="@color/transparent" />
        </com.jd.oa.joy.note.compunent.ShapeWavingView>

        <TextView
            android:id="@+id/tv_custom_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="@id/waving_view"
            app:layout_constraintRight_toRightOf="@id/waving_view"
            app:layout_constraintTop_toTopOf="@id/waving_view"
            app:layout_constraintBottom_toBottomOf="@id/waving_view"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="1"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{timeline.realName}"
            android:textColor="@color/color_1B1B1B"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/waving_view"
            app:layout_constraintStart_toEndOf="@id/waving_view"
            app:layout_constraintTop_toTopOf="@id/waving_view"
            tools:text="尼古拉斯.赵四" />

        <TextView
            android:id="@+id/tv_percent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#6A6A6A"
            android:text="@{@string/joynote_speaker_time_percent(proportion)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_name" />

        <com.jd.oa.joy.note.compunent.SpeakerTimelineView
            android:id="@+id/timeline_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:paddingVertical="4dp"
            android:paddingHorizontal="5dp"
            app:timeColor="@{timeline.color}"
            app:highlightHeight="4dp"
            app:indicatorRadius="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/waving_view"
            app:paragraph="@{timeline.scopes}"
            app:totalDuration="@{recordDuration}"
            app:trackColor="#FFEBECEE"
            app:trackHeight="4dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>