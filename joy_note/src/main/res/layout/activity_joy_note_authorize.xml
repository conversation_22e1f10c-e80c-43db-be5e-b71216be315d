<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_F8F8F9"
        android:orientation="vertical"
        tools:context=".permission.JoyNoteAuthorizeActivity">

        <RelativeLayout
            android:id="@+id/title_container"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:paddingHorizontal="16dp"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:clickable="true"
                android:focusable="true"
                android:includeFontPadding="false"
                android:text="@string/icon_direction_left"
                android:textColor="#333333"
                android:textSize="@dimen/JMEIcon_22" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:includeFontPadding="false"
                android:text="@string/joynote_permission_manager"
                android:textColor="#FF242931"
                android:textSize="18dp" />
        </RelativeLayout>

        <com.jd.oa.ui.RadiusLineaLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp"
            android:orientation="vertical">

            <include
                android:id="@+id/access_scope"
                layout="@layout/item_permission_manager_layout"
                bind:desc="@{@string/joynote_authorize_only_collaborators_visible}"
                bind:showRightIcon="@{true}"
                bind:title="@{@string/joynote_authorize_access_scope_symbol}" />

            <include
                android:id="@+id/security_config"
                layout="@layout/item_permission_manager_layout"
                bind:desc="@{@string/joynote_authorize_modify}"
                bind:showRightIcon="@{false}"
                bind:title="@{@string/joynote_authorize_security_settings}" />
        </com.jd.oa.ui.RadiusLineaLayout>

        <TextView
            android:id="@+id/list_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginVertical="4dp"
            android:text="@string/joynote_authorize_manage_collaborators"
            android:textColor="@color/color_6A6A6A"
            android:textSize="16sp"
            android:visibility="invisible"
            tools:visibility="visible" />

        <com.jd.oa.ui.RadiusLineaLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.jd.oa.ui.RadiusLineaLayout>
    </LinearLayout>
</layout>