<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/select_btn_bg"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@drawable/bg_btn_joy_note_gray"
    android:paddingStart="12dp"
    android:paddingEnd="12dp">

    <TextView
        android:id="@+id/select_btn_text"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_gravity="center_vertical|start"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:gravity="center"
        android:textColor="#232930"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage"
        tools:text="创建人" />

    <com.jd.oa.joy.note.compunent.JoyNoteAvatarGroup
        android:id="@+id/select_btn_avatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="0dp"
        android:visibility="gone"
        app:avatarHeight="15dp"
        app:avatarWidth="15dp"
        app:borderColor="#FFFFEBEA"
        app:borderWidth="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/select_btn_text"
        app:layout_constraintTop_toTopOf="parent"
        app:maxTotal="3"
        app:offset="-4dp" />

</androidx.constraintlayout.widget.ConstraintLayout>