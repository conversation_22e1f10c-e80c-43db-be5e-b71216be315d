<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.jd.oa.joy.note.compunent.JoyNoteTitleBar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="44dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <FrameLayout
        android:id="@+id/fl_video"
        android:layout_width="0dp"
        android:layout_height="200dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        android:background="@color/black" />

    <LinearLayout
        android:id="@+id/fl_chat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/fl_video"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="parent">

        <com.jd.oa.joy.note.compunent.JoyNoteTab
            android:id="@+id/tab_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            tools:layout_height="36dp"
            app:tabIndicator="@drawable/bg_tab_joy_note"
            app:tabMinWidth="80dp"
            app:tabMode="scrollable"
            app:tabRippleColor="@android:color/transparent"
            app:tabSelectedTextColor="@color/joy_note_tab_selected"
            app:tabTextColor="@color/joy_note_tab_normal"
            tools:background="#666" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_search"
            android:layout_width="44dp"
            android:layout_height="36dp"
            app:layout_constrainedWidth="true"
            android:gravity="center"
            android:layout_gravity="center"
            android:text="@string/icon_general_search"
            android:textColor="#666"
            tools:ignore="SpUsage"
            android:textSize="@dimen/JMEIcon_18" />
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fl_chat"
        android:background="#EDEDEE" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider"
        app:layout_constraintBottom_toTopOf="@+id/fl_audio"/>

    <View
        android:id="@+id/view_mask"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"/>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/fl_chat"
        app:layout_constraintBottom_toTopOf="@+id/fl_audio"
        app:layout_constraintVertical_bias="1"/>

    <FrameLayout
        android:id="@+id/fl_audio"
        android:layout_width="match_parent"
        android:layout_height="116dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/black"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>