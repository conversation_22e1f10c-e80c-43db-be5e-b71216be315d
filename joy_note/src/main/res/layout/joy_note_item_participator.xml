<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_joy_note_item_selector"
    android:minHeight="64dp"
    android:paddingHorizontal="16dp"
    android:paddingVertical="13dp"
    tools:ignore="SpUsage">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_photo"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        tools:src="@color/black" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_toEndOf="@id/iv_photo"
        android:ellipsize="end"
        android:lineSpacingExtra="0dp"
        android:lines="1"
        android:maxWidth="150dp"
        android:textColor="#232930"
        android:textSize="16dp"
        tools:text="张三张三张三张三张三张三张三张三张三张三" />

    <TextView
        android:id="@+id/tv_department"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_name"
        android:layout_marginStart="10dp"
        android:layout_marginTop="6dp"
        android:layout_toEndOf="@id/iv_photo"
        android:ellipsize="end"
        android:lineSpacingExtra="0dp"
        android:lines="1"
        android:singleLine="true"
        android:textColor="#8F959E"
        android:textSize="14dp"
        tools:text="智能管理与效率平台部-企业效率组企业效率组企业效率组企业效率组" />


    <TextView
        android:id="@+id/tv_post"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_toEndOf="@id/tv_name"
        android:gravity="end"
        android:textColor="#8F959E"
        android:textSize="14dp"
        tools:text="APP开发工程师岗" />

</RelativeLayout>