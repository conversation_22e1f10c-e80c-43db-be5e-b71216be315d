<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_joy_note_dialog_edit"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource,SpUsage">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="24dp"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxWidth="280dp"
        android:text="@string/joy_note_rename"
        android:textColor="#232930"
        android:textSize="16dp"
        android:textStyle="bold" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bg_eidt_joy_note_corner2"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp">

        <EditText
            android:id="@+id/edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@id/iv_clear"
            android:background="@null"
            android:focusable="true"
            android:maxLength="30"
            android:gravity="center_vertical"
            android:textColor="#232930"
            android:textSize="14dp"
            tools:text="222222222222222222" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:ignore="SpUsage"
            android:textSize="@dimen/JMEIcon_16"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="@string/icon_padding_closecircle"
            android:textColor="#8F959E" />
    </RelativeLayout>


    <View

        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#DEE0E3" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_cancel"
            android:textColor="#232930"
            android:textSize="16dp" />

        <View
            android:id="@+id/divider"
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="#DEE0E3" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="#FE3B30"
            android:textSize="16dp"
            android:text="@string/me_ok" />
    </LinearLayout>
</LinearLayout>