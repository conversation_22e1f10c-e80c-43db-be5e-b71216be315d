<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.jd.oa.theme.view.JoyWorkThemeView
        android:id="@+id/theme_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_gravity="bottom"
        android:paddingStart="12dp"
        android:paddingEnd="12dp">

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:includeFontPadding="false"
            android:text="@string/joynote_title"
            android:textColor="#333333"
            android:textSize="22dp"
            android:textStyle="bold"
            tools:ignore="SpUsage" />
    </FrameLayout>
</FrameLayout>