<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".record.RecordAudioActivity">

    <com.jd.oa.joy.note.compunent.JoyNoteTitleBar
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp" />

    <include layout="@layout/item_record_tip_widget" />

    <FrameLayout
        android:id="@+id/translate_switch_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"/>

    <FrameLayout
        android:id="@+id/asr_translate_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <FrameLayout
        android:id="@+id/play_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</LinearLayout>