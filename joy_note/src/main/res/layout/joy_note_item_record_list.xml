<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="20dp"
    tools:ignore="SpUsage">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/iv_photo"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginStart="16dp"
            tools:src="@color/black" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="#1B1B1B"
            android:textSize="14dp"
            android:textStyle="bold"
            tools:text="丁家辰" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="#6A6A6A"
            android:textSize="14dp"
            tools:text="00:00" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/ll_merge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="@string/icon_general_merge2"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_18" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:text="摘取段落"
                android:textColor="#666666" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="6dp"
        android:lineSpacingExtra="2dp"
        android:textColor="#1B1B1B"
        android:textSize="16dp"
        tools:text="云文档（JoySpace）汇集了协同文档、协同表格等创作工具，提供了安全、强大的云端存储和内容管理能力，多人协同内容永远是最新的，并可灵活管理各类权限，让团队协同与知识管理既高效又安全。" />

</LinearLayout>