<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="SpUsage">

    <com.jd.oa.joy.note.compunent.JoyNoteTitleBar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="44dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/joynote_deleted"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="@string/joynote_detail_deleted_tips"
            android:textColor="#8F959E"
            android:textSize="14dp" />

        <LinearLayout
            android:id="@+id/ll_delete_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/bg_btn_joy_note_gray_line"
            android:orientation="vertical"
            android:paddingHorizontal="20dp"
            android:paddingVertical="16dp"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/joynote_delete_view_title_link"
                    android:textColor="#666666"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_link"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    tools:text="https://joyspace.jd.com/paghttps://joyspace.jd.com/pag" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/joynote_delete_view_title_deleter"
                    android:textColor="#666666"
                    android:textSize="14dp" />

                <com.jd.oa.ui.CircleImageView
                    android:id="@+id/iv_photo_deleter"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="4dp"
                    android:src="#666666" />

                <TextView
                    android:id="@+id/tv_name_deleter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    tools:text="宋瑞进" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/joynote_delete_view_title_deletetime"
                    android:textColor="#666666"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_delete_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    tools:text="2023年×月×日" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_back_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_btn_joy_note_gray_line"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:paddingVertical="9dp"
                android:text="@string/joynote_detail_placeholder_back"
                android:textColor="#232930"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_restore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:background="@drawable/bg_btn_joy_note_red2"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:paddingVertical="9dp"
                android:text="@string/joynote_recyclebin_restore"
                android:textColor="@color/white"
                android:textSize="14dp"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>