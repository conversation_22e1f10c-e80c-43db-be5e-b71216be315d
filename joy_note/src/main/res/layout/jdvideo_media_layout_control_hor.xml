<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- 顶部布局 ， title，人数 ， 右侧按钮  -->
    <RelativeLayout
        android:id="@+id/topFunctionLayer"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="@drawable/jdliv_home_bg_drawable"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/me_close_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="10dp"
                android:src="@drawable/player_chevron_left" />

            <TextView
                android:id="@+id/me_video_live_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:ellipsize="end"
                android:gravity="center_vertical|left"
                android:maxWidth="300dp"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="16dp"
                tools:text="标题" />

        </LinearLayout>

        <!-- 小窗按钮 -->
        <TextView
            android:id="@+id/me_tv_speed_set"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_btn_joy_note_speed_bg"
            android:gravity="center"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="3dp"
            android:paddingBottom="3dp"
            android:text="@string/joynote_speed_btn"
            android:textColor="#FFFFFF"
            android:textSize="12dp"
            android:visibility="visible"
            tools:ignore="SpUsage" />
    </RelativeLayout>


    <!-- 底部栏-->
    <LinearLayout
        android:id="@+id/ll_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="82dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/jdliv_home_bg_drawable_reverse"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="visible">

        <RelativeLayout
            android:id="@+id/app_video_play_rl"
            android:layout_width="90dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical">

            <ImageView
                android:id="@+id/app_video_play"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_centerInParent="true"
                android:layout_marginTop="20dp"
                android:contentDescription="@string/un_video_screen_pause"
                android:src="@drawable/vd_play_video_hor" />
        </RelativeLayout>


        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <TextView
                android:id="@+id/app_video_currentTime_full"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:layout_marginTop="22dp"
                android:text="00:00"
                android:textColor="@color/white"
                android:textSize="11dp" />


            <SeekBar
                android:id="@+id/app_video_seekBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/app_video_currentTime_full"
                android:maxHeight="4dp"
                android:minHeight="4dp"
                android:paddingLeft="10dp"
                android:paddingTop="6dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp"
                android:splitTrack="false"
                android:thumb="@drawable/seekbar_thumb" />

            <TextView
                android:id="@+id/app_video_endTime_full"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_gravity="right"
                android:layout_marginTop="22dp"
                android:layout_marginRight="10dp"
                android:text="00:00"
                android:textColor="@color/white"
                android:textSize="11dp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/app_video_fullscreen"
            android:layout_width="90dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="20dp"
                android:background="@drawable/un_video_screen_v_to_h"
                android:contentDescription="@string/un_video_screen_change" />
        </RelativeLayout>


        <ImageView
            android:id="@+id/iv_bottom_voice_copy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="18dp"
            android:background="@drawable/video_player_voice_on"
            android:visibility="gone" />
    </LinearLayout>
</RelativeLayout>