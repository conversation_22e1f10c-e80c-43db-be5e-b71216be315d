<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true">

    <View
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/bg_fragment_joy_note_timeline_shadow"/>

    <LinearLayout
        android:id="@+id/layout_content"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_fragment_joy_note_timeline">
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.jd.oa.ui.IconFontView
                android:id="@+id/icon_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:padding="12dp"
                android:text="@string/icon_prompt_close"
                android:textColor="#1B1B1B"
                android:textSize="@dimen/JMEIcon_20"/>
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="#1B1B1B"
                android:textSize="16sp"
                android:paddingVertical="10dp"
                android:textStyle="bold"
                android:text="@string/joynote_speaker_timeline_title"/>
        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingEnd="16dp"
            android:gravity="center_vertical">
            <com.jd.oa.joy.note.compunent.ShapeWavingView
                android:id="@+id/avatar_wave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                app:shapeWaveColor="#74A5F9"
                app:shapeWaveInitState="pause"
                app:shapeWaveLength="8dp"
                app:shapeWaveLineNum="2"
                app:shapeWaveLineWidth="1dp"
                app:shapeWaveShape="superEllipse">
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <com.jd.oa.elliptical.SuperEllipticalImageView
                        android:id="@+id/avatar"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        tools:src="@drawable/default_person_blue_avatar"
                        android:scaleType="centerCrop"/>
                    <TextView
                        android:id="@+id/tv_custom_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        tools:text="1"/>
                </FrameLayout>
            </com.jd.oa.joy.note.compunent.ShapeWavingView>
            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#1B1B1B"
                tools:text="崔智琴"/>

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:listitem="@layout/joy_note_item_paragraph"/>

    </LinearLayout>
</FrameLayout>