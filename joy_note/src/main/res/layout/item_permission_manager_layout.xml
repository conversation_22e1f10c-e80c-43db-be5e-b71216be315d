<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable name="title" type="String" />
        <variable name="desc" type="String" />
        <variable name="showRightIcon" type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="12dp"
        android:paddingVertical="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_6A6A6A"
            android:textSize="16sp"
            android:text="@{title}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="访问范围：" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:text="@string/icon_general_lock"
            android:textColor="@color/color_1B1B1B"
            android:textSize="@dimen/JMEIcon_16"
            android:visibility="@{showRightIcon ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_desc"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:textColor="@{showRightIcon ? @color/color_1B1B1B : @color/color_9D9D9D}"
            android:textSize="14sp"
            android:text="@{desc}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/icon_right"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="仅协作者可见" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/icon_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_smallright"
            android:textSize="@dimen/JMEIcon_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>