<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp">

    <TextView
        android:id="@+id/from_language"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:background="@drawable/bg_btn_joy_note_translate_language_type"
        android:gravity="center"
        android:paddingHorizontal="12dp"
        android:text="@string/joynote_real_translate_auto_identification"
        android:textColor="#1B1B1B"
        android:textSize="14sp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/to_symbol"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="8dp"
        android:layout_toEndOf="@+id/from_language"
        android:text="@string/icon_direction_arrowright" />

    <LinearLayout
        android:id="@+id/to_language"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:layout_toEndOf="@+id/to_symbol"
        android:background="@drawable/bg_btn_joy_note_translate_language_type"
        android:gravity="center"
        android:paddingHorizontal="12dp"
        android:textColor="#1B1B1B"
        android:textSize="14sp">

        <TextView
            android:id="@+id/tv_target_language"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxWidth="56dp"
            android:maxLines="1"
            android:textColor="#1B1B1B"
            android:textSize="14sp"
            tools:text="Simplified Chinese" />

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/icon_smalldown" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/check_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:gravity="end"
        android:layout_marginStart="10dp"
        android:layout_toEndOf="@+id/to_language">

        <ImageView
            android:id="@+id/check_only_translate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:background="@drawable/joynote_translate_selector_checkbox" />

        <TextView
            android:id="@+id/only_translation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/joynote_real_translate_only_translation"
            android:textColor="@color/black"
            android:textSize="12sp"
            tools:text="Only Translate" />
    </LinearLayout>
</RelativeLayout>