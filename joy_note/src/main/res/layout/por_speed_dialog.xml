<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/change_platform_background"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:visibility="visible">

        <Button
            android:id="@+id/cancel_btn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@color/transparent"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="#3C4056"
            android:textSize="14dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:layout_gravity="bottom" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/bottom_ll"
        android:layout_marginLeft="31dp"
        android:layout_marginRight="31dp"
        android:background="#E4E4E4" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom_ll"
        android:layout_marginLeft="31dp"
        android:layout_marginRight="31dp"
        android:layout_marginBottom="12dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="18dp">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/me_player_speed_title"
                    android:textColor="#3C4056"
                    android:textSize="14dp"
                    android:textStyle="bold" />

                <RelativeLayout
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true" />
                </RelativeLayout>
            </RelativeLayout>

            <android.widget.ListView
                android:id="@+id/speed_lv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:divider="#E4E4E4"
                android:dividerHeight="1dp"
                android:listSelector="@android:color/transparent"
                android:scrollbars="none" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>