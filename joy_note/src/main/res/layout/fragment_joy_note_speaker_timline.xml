<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.jd.oa.joy.note.viewmodel.JoyNoteDetailSpeakerViewModel" />

        <variable
            name="minutesId"
            type="String" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:itemCount="8"
            tools:listitem="@layout/joy_note_item_speaker_timeline" />

        <LinearLayout
            android:id="@+id/loading_group_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/joy_note_book_icon" />

            <TextView
                android:id="@+id/load_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="#6A6A6A"
                android:textSize="12sp"
                tools:text="暂无内容" />

            <TextView
                android:id="@+id/btn_refresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="21dp"
                android:background="@drawable/jdme_bg_btn_red_radius"
                android:onClick="@{() -> viewModel.getSpeakerTimeLine(minutesId, true)}"
                android:paddingHorizontal="12dp"
                android:paddingVertical="5dp"
                android:text="@string/joynote_detail_refresh"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/load_placeHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginTop="12dp"
                android:src="@drawable/joy_note_load_empty_page" />

            <com.jd.oa.ui.LoadingView
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>
    </FrameLayout>
</layout>