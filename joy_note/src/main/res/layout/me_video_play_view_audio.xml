<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <tv.danmaku.ijk.media.example.widget.media.IjkVideoView
        android:id="@+id/video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <RelativeLayout
        android:id="@+id/app_video_play_rl"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="10dp"
        android:background="@drawable/bg_btn_joy_note_play">

        <ImageView
            android:id="@+id/app_video_play"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            android:src="@drawable/joy_note_play_btn"
            tools:ignore="ContentDescription" />
    </RelativeLayout>

    <TextView
        android:id="@+id/audio_back_btn"
        android:layout_width="20dp"
        android:layout_height="22dp"
        android:layout_alignTop="@id/app_video_play_rl"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="31dp"
        android:layout_toStartOf="@id/app_video_play_rl"
        android:background="@drawable/joy_note_audio_back"
        android:gravity="center"
        android:paddingTop="2dp"
        android:text="15"
        android:textColor="#FF1B1B1B"
        android:textSize="10dp"
        tools:ignore="HardcodedText,SpUsage" />

    <TextView
        android:id="@+id/audio_forward_btn"
        android:layout_width="20dp"
        android:layout_height="22dp"
        android:layout_alignTop="@id/app_video_play_rl"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="31dp"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="16dp"
        android:layout_toEndOf="@id/app_video_play_rl"
        android:background="@drawable/joy_note_audio_forward"
        android:gravity="center"
        android:paddingTop="2dp"
        android:text="15"
        android:textColor="#FF1B1B1B"
        android:textSize="10dp"
        tools:ignore="HardcodedText,SpUsage" />

    <TextView
        android:id="@+id/me_tv_speed_set"
        android:layout_width="80dp"
        android:layout_height="48dp"
        android:layout_alignTop="@id/app_video_play_rl"
        android:layout_alignParentEnd="true"
        android:gravity="end|center_vertical"
        android:paddingStart="8dp"
        android:paddingEnd="16dp"
        android:text="@string/joynote_speed_btn"
        android:textColor="#FF1B1B1B"
        android:textSize="14dp"
        android:textStyle="bold"
        tools:ignore="SpUsage" />

    <SeekBar
        android:id="@+id/app_video_seekBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="12dp"
        android:maxHeight="4dp"
        android:minHeight="4dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:progressDrawable="@drawable/bg_btn_joy_note_progress"
        android:thumb="@drawable/bg_btn_joy_note_seek" />

    <TextView
        android:id="@+id/app_video_currentTime_full"
        android:layout_width="80dp"
        android:layout_height="16dp"
        android:layout_below="@id/app_video_seekBar"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:gravity="center|start"
        android:textColor="#FF9D9D9D"
        android:textSize="12dp"
        tools:ignore="SpUsage"
        tools:text="00:00:00" />

    <TextView
        android:id="@+id/app_video_endTime_full"
        android:layout_width="80dp"
        android:layout_height="16dp"
        android:layout_below="@id/app_video_seekBar"
        android:layout_alignParentEnd="true"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:gravity="center|end"
        android:textColor="#FF9D9D9D"
        android:textSize="12dp"
        tools:ignore="SpUsage"
        tools:text="00:00:00" />

    <include
        layout="@layout/me_video_play_view_audio_placeholder"
        android:visibility="gone" />
</RelativeLayout>
