<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:apps="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="77dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/bg_btn_joy_note_black"
        android:paddingStart="12dp"
        android:paddingEnd="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/joy_note_tips_restore_msg"
            android:textColor="#FFF8F9FA"
            android:textSize="16dp"
            apps:layout_constraintBottom_toBottomOf="parent"
            apps:layout_constraintStart_toStartOf="parent"
            apps:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/restore_tips_btn"
            android:layout_width="76dp"
            android:layout_height="26dp"
            android:background="@drawable/bg_btn_joy_note_white"
            android:gravity="center"
            android:text="@string/joy_note_tips_restore_btn"
            android:textColor="#FF4C7CFF"
            android:textSize="14dp"
            apps:layout_constraintBottom_toBottomOf="parent"
            apps:layout_constraintEnd_toEndOf="parent"
            apps:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>