<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_joy_note_dialog_edit"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource,SpUsage">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="24dp"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxWidth="280dp"
        android:text="@string/joynote_permission_apply"
        android:textColor="#232930"
        android:textSize="16dp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="18dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="*"
            android:textColor="#F5222D"
            android:textSize="14dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:text="@string/joynote_permission_apply_title"
            android:textColor="#232930"
            android:textSize="14dp" />
    </LinearLayout>


    <RadioGroup
        android:id="@+id/rg_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:orientation="horizontal"
        android:paddingTop="8dp">

        <RadioButton
            android:id="@+id/rb_read"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@null"
            android:checked="true"
            android:drawableLeft="@drawable/selector_joy_note_permission_check_box"
            android:drawablePadding="7dp"
            android:text="@string/joynote_permission_only_read"
            android:textColor="#232930"
            android:textSize="14dp" />

        <RadioButton
            android:id="@+id/rb_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:button="@null"
            android:drawableLeft="@drawable/selector_joy_note_permission_check_box"
            android:drawablePadding="7dp"
            android:gravity="center"
            android:text="@string/joynote_permission_can_edit"
            android:textColor="#232930"
            android:textSize="14dp" />
    </RadioGroup>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bg_eidt_joy_note_corner"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp">

        <EditText
            android:id="@+id/edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="0dp"
            android:background="@null"
            android:focusable="true"
            android:gravity="top"
            android:hint="@string/joynote_permission_apply_tips"
            android:maxLength="100"
            android:minHeight="120dp"
            android:padding="12dp"
            android:textColor="#232930"
            android:textColorHint="#8F959E"
            android:textSize="14dp" />
    </RelativeLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#DEE0E3" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_cancel"
            android:textColor="#232930"
            android:textSize="16dp" />

        <View
            android:id="@+id/divider"
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="#DEE0E3" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_ok"
            android:textColor="#FE3B30"
            android:textSize="16dp" />
    </LinearLayout>
</LinearLayout>