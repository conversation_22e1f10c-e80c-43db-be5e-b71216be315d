<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:visibility="gone">

    <RelativeLayout
        android:id="@+id/jd_video_ui_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/loadingLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal" />

    <View
        android:id="@+id/app_video_replay_icon"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/liveIcon"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription" />

    <com.jingdong.common.widget.image.UnNetImageView
        android:id="@+id/iv_corver"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/shareIcon"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/voiceIcon"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/voiceParent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal" />

    <ImageView
        android:id="@+id/iv_bottom_voice"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription" />

    <FrameLayout
        android:id="@+id/fl_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/videoLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/errorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal" />

    <TextView
        android:id="@+id/errorTipTv"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/loadErrorIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/app_video_top_box_small"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageButton
        android:id="@+id/app_video_finish_small"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/play_icon_voice_small"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ProgressBar
        android:id="@+id/app_video_bottom_progressbar_small"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/errorLayoutSmall"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal" />

    <TextView
        android:id="@+id/retrySmall"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/retry"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/errorTipTvSmall"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/loadingLayoutSmall"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal" />

    <com.jd.jdvideoplayer.playback.ShowChangeLayout
        android:id="@+id/scl"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/centerTipLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/centerTipIcon"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/centerTipIext"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/centerTipIextTimer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/topFunctionLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/me_video_live_title"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/me_close_new"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/ll_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/app_video_box"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/play_icon_center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription" />

    <ProgressBar
        android:id="@+id/app_video_bottom_progressbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/app_video_fullscreen"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/playback_more_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/iv_bottom_voice_copy"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription" />
</FrameLayout>
