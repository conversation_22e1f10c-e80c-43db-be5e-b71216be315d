<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/app_video_box"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <RelativeLayout
        android:id="@+id/videoLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <tv.danmaku.ijk.media.example.widget.media.IjkVideoView
            android:id="@+id/video_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true" />

        <!-- 封面显示-->
        <com.jingdong.common.widget.image.UnNetImageView
            android:id="@+id/iv_corver"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true" />

        <ImageView
            android:id="@+id/liveIcon"
            android:layout_width="39dp"
            android:layout_height="21dp"
            android:layout_alignTop="@+id/video_view"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:visibility="gone" />


        <TextView
            android:id="@+id/app_video_replay_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/vd_replay_video"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/errorLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#a6000000"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/errorTipTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/video_player_net_error"
                android:textColor="@color/white"
                android:textSize="13dp" />

            <ImageView
                android:id="@+id/loadErrorIv"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="14dp"
                android:src="@drawable/video_player_error_icon"
                android:visibility="gone" />

            <TextView
                android:id="@+id/retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:background="@drawable/vd_video_retry_bg"
                android:clickable="true"
                android:drawableLeft="@drawable/video_player_fresh_icon"
                android:drawablePadding="6dp"
                android:paddingLeft="5dp"
                android:paddingTop="5dp"
                android:paddingRight="10dp"
                android:paddingBottom="5dp"
                android:text="@string/video_player_net_error_small"
                android:textColor="@color/white"
                android:textSize="12dp"
                android:visibility="gone" />
        </LinearLayout>

        <ImageView
            android:id="@+id/play_icon_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="8dp"
            android:background="@drawable/video_player_center_play_large"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/loadingLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:visibility="gone">

            <ProgressBar
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:indeterminate="true"
                android:indeterminateBehavior="repeat"
                android:indeterminateDrawable="@drawable/video_player_loading"
                android:indeterminateDuration="1000" />
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/jd_video_ui_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"></RelativeLayout>

    <FrameLayout
        android:id="@+id/fl_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_bottom_voice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center|right"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="18dp"
            android:background="@drawable/video_player_voice_on"
            android:visibility="gone" />
    </FrameLayout>


    <LinearLayout
        android:id="@+id/voiceParent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="60dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/shareIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/video_player_share"
            android:visibility="gone" />

        <TextView
            android:id="@+id/voiceIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:visibility="gone" />
    </LinearLayout>

    <ProgressBar
        android:id="@+id/app_video_bottom_progressbar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:maxHeight="4dp"
        android:minHeight="4dp"
        android:visibility="gone" />

    <ProgressBar
        android:id="@+id/app_video_bottom_progressbar_small"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:maxHeight="2dp"
        android:minHeight="2dp"
        android:progressDrawable="@drawable/vd_progressbar"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/errorLayoutSmall"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#a6000000"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/retrySmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/video_player_retry_small" />

        <TextView
            android:id="@+id/errorTipTvSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/white"
            android:textSize="12sp" />
    </LinearLayout>

    <!-- 小屏顶部栏-->
    <RelativeLayout
        android:id="@+id/app_video_top_box_small"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone">

        <ImageButton
            android:id="@+id/app_video_finish_small"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentRight="true"
            android:background="@drawable/video_player_close_btn_small"
            android:contentDescription="@string/un_video_close" />

        <TextView
            android:id="@+id/play_icon_voice_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/loadingLayoutSmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/video_player_loading_bg_small"
        android:gravity="center"
        android:visibility="gone">

        <ProgressBar
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:indeterminate="true"
            android:indeterminateBehavior="repeat"
            android:indeterminateDrawable="@drawable/video_player_loading_small"
            android:indeterminateDuration="1000" />
    </LinearLayout>

    <include
        android:id="@+id/centerTipLayout"
        layout="@layout/center_layout" />

    <com.jd.jdvideoplayer.playback.ShowChangeLayout
        android:id="@+id/scl"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_centerInParent="true"/>
    <View
        android:id="@+id/audio_forward_btn"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <View
        android:id="@+id/audio_back_btn"
        android:layout_width="0dp"
        android:layout_height="0dp" />

</RelativeLayout>
