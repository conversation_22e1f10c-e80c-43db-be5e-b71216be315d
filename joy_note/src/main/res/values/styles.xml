<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="BottomSheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/bottomSheetStyleWrapper</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:navigationBarColor">@color/white</item>
        <item name="android:statusBarColor">@color/transparent</item>
    </style>

    <style name="bottomSheetStyleWrapper" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="JoyNoteTitleTextStyle">
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:textStyle">bold</item>
    </style>


    <style name="JoyNoteTabLayoutTextStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- 全透明背景Dialog的样式-->
    <style name="TransDialogStyle" parent="@android:style/Theme.Dialog">
        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- 浮于Activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- Dialog以外的区域模糊效果 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">false</item>
        <!-- Dialog进入及退出动画 -->
        <item name="android:windowAnimationStyle">@style/ActionSheetDialogAnimation</item>
    </style>


    <!-- ActionSheet进出动画 -->
    <style name="ActionSheetDialogAnimation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/fade_in</item>
        <item name="android:windowExitAnimation">@anim/fade_out</item>
    </style>

    <style name="JoyNoteDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:backgroundDimAmount">0.3</item>
    </style>


    <!--    <style name="joyNotePlayBtnPlay" parent="Widget.AppCompat.TextView">-->
    <!--        <item name="android:background">@drawable/bg_btn_joy_note_gray_line_semicircle</item>-->
    <!--        <item name="android:textSize">20dp</item>-->
    <!--        <item name="android:text">暂停</item>-->
    <!--        <item name="android:textColor">#1869F5</item>-->
    <!--    </style>-->

    <style name="joyNotePlayBtnPause" parent="Widget.AppCompat.TextView">
        <item name="android:background">@drawable/bg_btn_joy_note_blue_semicircle</item>
        <item name="android:textSize">16dp</item>
        <item name="android:text">开始</item>
        <item name="android:textColor">#FFFFFF</item>
    </style>
    <style name="progressBarRed" parent="@style/Theme.AppCompat">
        <item name="colorControlActivated">#F63218</item>
    </style>

    <style name="BottomDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/joy_note_detail_timeline_fragment_show</item>
        <item name="android:windowExitAnimation">@anim/joy_note_detail_timeline_fragment_hide</item>
    </style>

    <style name="BottomDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowAnimationStyle">@style/BottomDialogAnimation</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
</resources>