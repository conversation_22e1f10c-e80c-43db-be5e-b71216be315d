<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="JoyNoteAvatarGroup">
        <attr name="avatarWidth" format="dimension" />
        <attr name="avatarHeight" format="dimension" />
        <attr name="maxTotal" format="integer" />
        <attr name="borderWidth" format="dimension" />
        <attr name="offset" format="dimension" />
        <attr name="borderColor" format="color" />
    </declare-styleable>
    <declare-styleable name="JoyNoteSelectBtn">
        <attr name="selectedTextColor" format="color" />
        <attr name="selectedText" format="string" />
        <attr name="unselectedTextColor" format="color" />
    </declare-styleable>

    <declare-styleable name="SpeakerTimelineView">
        <attr name="trackHeight" format="dimension" />
        <attr name="trackColor" format="color" />
        <attr name="indicatorRadius" format="dimension"/>
        <attr name="indicatorColor" format="color"/>
        <attr name="highlightHeight" format="dimension"/>
        <attr name="highlightColor" format="color"/>
    </declare-styleable>

    <declare-styleable name="ShapeWavingView">
        <attr name="shapeWaveColor" format="color" />
        <attr name="shapeWaveLineWidth" format="dimension" />
        <attr name="shapeWaveLength" format="dimension" />
        <attr name="shapeWaveShape" format="enum">
            <enum name="circle" value="0" />
            <enum name="superEllipse" value="1" />
        </attr>
        <attr name="shapeWaveLineNum" format="integer"/>
        <attr name="shapeWaveLineInterval" format="integer"/>
        <attr name="shapeWaveInitState" format="enum">
            <enum name="pause" value="0" />
            <enum name="running" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="JoyNoteTitleBar">
        <attr name="title" format="string" />
    </declare-styleable>
</resources>
