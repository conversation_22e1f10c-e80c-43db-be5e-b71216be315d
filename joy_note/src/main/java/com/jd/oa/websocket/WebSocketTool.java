package com.jd.oa.websocket;

import android.net.Uri;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.ToNumberPolicy;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.joy.note.BuildConfig;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.PreferenceManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import io.socket.client.IO;
import io.socket.client.Socket;
import io.socket.engineio.client.transports.WebSocket;

public class WebSocketTool {
    private static final Gson gson = new GsonBuilder()
            .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
            .create();
//    private static final String WSS_NOTES_PRE_JD_COM = "clevernotes-pre.jd.com";
    private static final String WSS_PATH = "collabwsgateway/joyspace/";
    private static final String JOY_NOTE_LOG = "joyNoteLog";

    static Socket getSocketIO(String joyNoteId) {
        String url = getJoyNoteUrl(joyNoteId);
        URI uri = URI.create(url);
        showSocketDebugLog("uri=" + uri.getScheme() + "://" + uri.getHost());
        showSocketDebugLog("setPath=" + uri.getPath());
        showSocketDebugLog("setQuery=" + uri.getQuery());
        IO.Options options = IO.Options.builder()
                // IO factory options
                .setForceNew(false)
                .setMultiplex(true)

                // low-level engine options
//                    .setTransports(new String[]{Polling.NAME, WebSocket.NAME})
//                    .setUpgrade(true)
//                    .setRememberUpgrade(false)
                .setPath(uri.getPath())
                .setQuery(uri.getQuery())
//                    .setExtraHeaders(null)

                // Manager options
                .setReconnection(true)
                .setReconnectionAttempts(Integer.MAX_VALUE)
                .setReconnectionDelay(1_000)
                .setReconnectionDelayMax(5_000)
                .setRandomizationFactor(0.5)
                .setTimeout(10_000)

                // Socket options
                .setAuth(null)
                .build();
        options.transports = new String[]{WebSocket.NAME};
        try {
            return IO.socket(uri.getScheme() + "://" + uri.getHost(), options);
        } catch (URISyntaxException e) {
            return null;
        }
    }

    public static String getJoyNoteUrl(@NonNull String channelId) {
        try {
            //慧记14 WebSocketTool 拼接websocket Url  collabwsgateway/joyspace/+appId+channelId+(1##+token+##+teamId+"##ME##zh_CN")
            Uri uriTemp = Uri.parse(LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getNoteWS());
            String token = "1##" +
                    TokenManager.getInstance().getAccessToken() + "##" +
                    PreferenceManager.UserInfo.getTeamId() + "##ME##zh_CN";
            showSocketDebugLog(URLEncoder.encode(token, StandardCharsets.UTF_8.toString()));
            Uri uri = new Uri.Builder().scheme(uriTemp.getScheme()).authority(uriTemp.getAuthority()).path(WSS_PATH)
                    .appendQueryParameter("appId", "joyminutes")
                    .appendQueryParameter("channelId", channelId)
                    .appendQueryParameter("token", URLEncoder.encode(token, StandardCharsets.UTF_8.toString()))
                    .build();
            return uri.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    static void showSocketDebugLog(@NonNull String socketLog) {
        if (BuildConfig.DEBUG) {
            if (socketLog.length() > 1000) {
                return;
            }
            System.out.println("socket--io--state----" + socketLog);

            MELogUtil.localV(JOY_NOTE_LOG, socketLog);
            MELogUtil.onlineV(JOY_NOTE_LOG, socketLog);
        } else {
            if (socketLog.length() > 1000) {
                return;
            } else if (socketLog.length() > 500) {
                socketLog = socketLog.substring(0, 500);
            }
            MELogUtil.localV(JOY_NOTE_LOG, socketLog);
            MELogUtil.onlineV(JOY_NOTE_LOG, socketLog);
        }
    }

    public static JSONObject getEventJson(Object obj) {
        try {
            String str = gson.toJson(obj);
            showSocketDebugLog("getEventJson=" + str);
            return new JSONObject(str);
        } catch (JSONException e) {
            return new JSONObject();
        }
    }

    static String getEventJsonStr(Object obj) {
        return gson.toJson(obj);
    }

    static <T> List<T> getEventList(Object[] obj, Class<T> entityClazz) {
        List<T> data = new ArrayList<>();
        try {
            JsonArray list = gson.fromJson(Arrays.toString(obj), JsonArray.class);
            Type type = TypeToken.get(entityClazz).getType();
            for (JsonElement object : list) {
                data.add(gson.fromJson(object, type));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    static <T> T getEventObj(String jsonStr, Class<T> entityClazz) {
        return gson.fromJson(jsonStr, entityClazz);
    }
}
