package com.jd.oa.websocket;

import android.os.Handler;
import android.os.Message;

import com.jd.oa.AppBase;
import com.jd.oa.asr.Logger;
import com.jd.oa.asr.websocket.AudioFinishListener;
import com.jd.oa.asr.websocket.AudioStateListener;
import com.jd.oa.asr.websocket.AudioTextListener;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.ErrorMsg;
import com.jd.oa.asr.websocket.model.ReceiveMsg;
import com.jd.oa.asr.websocket.model.SendMsg;
import com.jd.oa.asr.websocket.model.TextMsg;
import com.jd.oa.asr.websocket.model.UpEvent;
import com.jd.oa.asr.websocket.model.WrongMsg;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteFinishInfo;
import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.ConnectivityUtils;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.encrypt.Base64Encoder;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import androidx.annotation.NonNull;
import io.socket.client.Socket;

import static com.jd.oa.asr.websocket.model.EventName.DIFFERENT_DEVICES;
import static com.jd.oa.asr.websocket.model.EventName.DOWNSIDE_SEND_AUDIO;
import static com.jd.oa.asr.websocket.model.EventName.DOWNSIDE_STOP_RECORD;
import static com.jd.oa.asr.websocket.model.EventName.NOT_ALLOW_RECORD;
import static com.jd.oa.asr.websocket.model.EventName.STOP_RECORD_FAIL;
import static com.jd.oa.asr.websocket.model.EventName.UPSIDE_ALLOW_RECORD;
import static com.jd.oa.asr.websocket.model.EventName.UPSIDE_RECEIVE_AUDIO;
import static com.jd.oa.asr.websocket.model.EventName.UPSIDE_SEND_TEXT;
import static com.jd.oa.asr.websocket.model.EventName.UPSIDE_STOPPED_RECORD;
import static com.jd.oa.websocket.WebSocketTool.getEventJson;
import static com.jd.oa.websocket.WebSocketTool.getEventJsonStr;
import static com.jd.oa.websocket.WebSocketTool.getEventList;
import static com.jd.oa.websocket.WebSocketTool.getEventObj;
import static com.jd.oa.websocket.WebSocketTool.showSocketDebugLog;

public class AudioFileSender {

    private static final int FLAG_CONNECTED = 0;
    private static final int FLAG_CONNECTING = FLAG_CONNECTED - 1;
    private static final int FLAG_DISCONNECTED = FLAG_CONNECTED - 2;
    private static final int FLAG_ERROR = FLAG_CONNECTED - 3;
    private static final int FLAG_REFRESH_TOKEN = FLAG_CONNECTED - 4;
    private static final int FLAG_FINISHING = FLAG_CONNECTED - 5;
    private static final int FLAG_CONNECTING_FILE_END = FLAG_CONNECTED + 1;
    private static final int FLAG_CONNECTING_PAUSING = FLAG_CONNECTED + 2;
    private static final int FLAG_CONNECTING_PAUSE = FLAG_CONNECTED + 3;
    private static final int CHECK_STATE = 100;
    private static final int CHECK_SEND = 101;
    public static final int FINISH_FAIL = -1;
    public static final int FINISH_SUCCESS = 0;
    private int blockSize = 6400;
    private final AtomicInteger connectionState = new AtomicInteger(FLAG_DISCONNECTED);
    private Socket socket;
    private String joyNoteId;
    private String filePath;
    private long index = -1;
    private boolean hasStarted;
    private final Map<Long, Boolean> receiveIndex = new HashMap<>();
    private final Map<Long, Long> sendIndex = new HashMap<>();
    private final Handler checkHandler = new CheckHandler();
    private AudioStateListener audioStateListener;
    private AudioTextListener audioTextListener;
    private AudioFinishListener audioFinishListener;
    private String bizId;
    private int waitTime = 200;
    private long finalFileSize;
    private RandomAccessFile randomAccessFile = null;

    private AudioFileSender() {
        showSocketDebugLog("AudioFileSender");
    }

    private static final AudioFileSender instance = new AudioFileSender();

    public static AudioFileSender getInstance() {
        return instance;
    }

    synchronized boolean isConnecting() {
        return connectionState.get() >= FLAG_CONNECTING;
    }

    /**
     * @noinspection UnusedReturnValue
     */
    synchronized boolean connect() {
        try {
            if (hasNotNet()) {
                return false;
            }
            if (joyNoteId == null || isConnecting() || connectionState.get() == FLAG_CONNECTED) {
                return false;
            }
            if (socket != null) {
                if (socket.isActive() || socket.connected()) {
                    return false;
                }
            }
            connectionState.set(FLAG_CONNECTING);
            //慧记13 AudioFileSender 初始化socket
            socket = WebSocketTool.getSocketIO(joyNoteId);
            if (socket == null) {
                return false;
            }
            socket.connect();
            showSocketDebugLog("connect");
            socket.on("connect", args -> {
                startRecord();
                showSocketDebugLog("connect=" + Arrays.toString(args));
            });
            socket.on("message", args -> {
                //慧记15 AudioFileSender socket消息交互
                showSocketDebugLog("message=" + Arrays.toString(args));
                List<DownEvent> downList = getEventList(args, DownEvent.class);
                if (downList.size() == 0) {
                    return;
                }
                String msg = downList.get(0).getMessage();
                String event = downList.get(0).getEventName();
                switch (event) {
                    case UPSIDE_ALLOW_RECORD:
                        if (hasStarted) {
                            index -= 5;
                            if (index <= 0) {
                                index = 0;
                            }
                            startSendData(1000);
                        } else {
                            startSendData(100);
                        }
                        if (!hasStarted) {
                            hasStarted = true;
                        }
                        if (audioStateListener != null) {
                            audioStateListener.onStart();
                        }
                        break;
                    case UPSIDE_RECEIVE_AUDIO:
                        ReceiveMsg receiveMsg = getEventObj(msg, ReceiveMsg.class);
                        receiveIndex.put(Long.parseLong(receiveMsg.sendAudioId), true);
                        break;
                    case UPSIDE_STOPPED_RECORD:
                        connectionState.set(FLAG_CONNECTING_PAUSE);
                        break;
                    case DIFFERENT_DEVICES:
                        errorAction(AppBase.getAppContext().getString(R.string.joynote_audio_other_is_recording_tips));
                        break;
                    case NOT_ALLOW_RECORD:
                    case STOP_RECORD_FAIL:
                        WrongMsg wrongMsg = getEventObj(msg, WrongMsg.class);
                        if (wrongMsg == null) {
                            break;
                        }
                        int tipId = R.string.joynote_audio_finish_tips;
                        if (!wrongMsg.exist) {
                            tipId = R.string.joynote_audio_del_tips;
                        }
                        errorAction(AppBase.getAppContext().getString(tipId));
                        break;
                    case UPSIDE_SEND_TEXT:
                        TextMsg textMsg = getEventObj(msg, TextMsg.class);
                        if (audioTextListener != null) {
                            audioTextListener.onText(textMsg);
                        }
                        break;
                }
            });
            socket.on("connect_error", args -> {
                //慧记16 AudioFileSender socket connect_error
                setErrorState();
                String error = Arrays.toString(args);
                showSocketDebugLog("connection_error=" + error);
                if (error.contains("EngineIOException")) {
                    return;
                }
                List<ErrorMsg> downList = getEventList(args, ErrorMsg.class);
                if (downList.size() == 0) {
                    return;
                }
                ErrorMsg errorMsg = downList.get(0);
                if ("authentication_error".equals(errorMsg.getMessage())) {
                    errorAction(AppBase.getAppContext().getString(R.string.joynote_token_fail));
                }
            });
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    private void errorAction(String errorMsg) {
        stopAction();
        if (audioStateListener != null) {
            audioStateListener.error(errorMsg);
        }
    }

    private void stopAction() {
        try {
            connectionState.set(FLAG_FINISHING);
            checkHandler.removeMessages(CHECK_STATE);
            checkHandler.removeMessages(CHECK_SEND);
            if (socket != null) {
                socket.disconnect();
            }
            joyNoteId = null;
            bizId = null;
            index = -1;
            hasStarted = false;
            filePath = null;
            closeFile();
            showSocketDebugLog("errorAction---audioStateListener=" + audioStateListener);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void startSendData(int delay) {
        connectionState.set(FLAG_CONNECTED);
        checkHandler.sendEmptyMessageDelayed(CHECK_SEND, delay);
    }

    private void sendDataBlock() {
        if (joyNoteId == null || socket == null || !socket.isActive() || !socket.connected()) {
            setErrorState();
            return;
        }
        if (Boolean.TRUE.equals(receiveIndex.get(sendIndex.get(index)))) {
            showSocketDebugLog("Boolean.TRUE.equals(receive----Index" + index);
            index++;
            checkHandler.sendEmptyMessageDelayed(CHECK_SEND, waitTime);
            return;
        }
        long lastSendTime = System.currentTimeMillis();
        UpEvent upEvent = new UpEvent();
        upEvent.setDeviceId(DeviceUtil.getDeviceUniqueId());
        upEvent.setEventName(DOWNSIDE_SEND_AUDIO);
        upEvent.setChannelID(joyNoteId);
        SendMsg sendMsg = new SendMsg();
        sendMsg.setDeviceId(DeviceUtil.getDeviceUniqueId());
        sendMsg.id = upEvent.getId();
        byte[] data = new byte[blockSize];
        int readLength = getFileData(index, filePath, data);
        byte[] dataEnd = null;
        if (readLength < blockSize) {
            showSocketDebugLog("index=" + index + "   r1=" + readLength);
            if (audioFinishListener != null) {
                showSocketDebugLog("audioFinishListener != null---index" + index + "   r1=" + readLength);
                if (readLength == -1) {
                    sendFinishRequest();
                    return;
                } else {
                    dataEnd = new byte[readLength];
                    System.arraycopy(data, 0, dataEnd, 0, readLength);
                }
            } else {
                showSocketDebugLog("fileEnd");
                connectionState.set(FLAG_CONNECTING_FILE_END);
                return;
            }
        }
        connectionState.set(FLAG_CONNECTED);
        showSocketDebugLog("send==>>index=" + index + "upEvent=" + upEvent.getId() + "   send-len=" + readLength);
        sendMsg.audio = Base64Encoder.encode(dataEnd == null ? data : dataEnd);
        upEvent.setMessage(getEventJsonStr(sendMsg));
        receiveIndex.put(upEvent.getId(), false);
        sendIndex.put(index, upEvent.getId());
        socket.emit("message", getEventJson(upEvent));
        if (audioFinishListener != null) {
            if (finalFileSize > 0 && index % 5 == 0) {
                int percent = (int) (100 * ((float) index * blockSize / finalFileSize));
                if (((index + 20) * blockSize) < finalFileSize) {
                    audioFinishListener.onUpload(Math.min(percent, 100));
                }
            }
        }
        index++;
        long t = waitTime - (System.currentTimeMillis() - lastSendTime);
        checkHandler.sendEmptyMessageDelayed(CHECK_SEND, t < 0 ? 0 : t);
    }

    private void sendFinishRequest() {
        showSocketDebugLog("sendFinishRequest----connectionState.get()=" + connectionState.get());
        if (connectionState.get() == FLAG_FINISHING) {
            return;
        }
        connectionState.set(FLAG_FINISHING);
        checkHandler.removeMessages(CHECK_STATE);
        checkHandler.removeMessages(CHECK_SEND);
        if (socket != null) {
            socket.disconnect();
        }
        showSocketDebugLog("stop");
        if (joyNoteId == null) {
            if (audioFinishListener != null) {
                audioFinishListener.onResult(true, FINISH_SUCCESS);
            }
            return;
        }
        VideoListRepository.getUserRepository().finishJoyNote(joyNoteId, bizId, new LoadDataCallback<JoyNoteFinishInfo>() {
            @Override
            public void onDataLoaded(JoyNoteFinishInfo joyNoteFinishInfo) {
                AudioFileSender.this.joyNoteId = null;
                AudioFileSender.this.bizId = null;
                index = -1;
                hasStarted = false;
                filePath = null;
                closeFile();
                connectionState.set(FLAG_DISCONNECTED);
                if (audioFinishListener != null) {
                    audioFinishListener.onResult(true, FINISH_SUCCESS);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                connectionState.set(FLAG_DISCONNECTED);
                if (audioFinishListener != null) {
                    audioFinishListener.onResult(false, FINISH_FAIL);
                }
            }
        });
    }

    private void startRecord() {
        if (joyNoteId == null || socket == null || !socket.isActive() || !socket.connected()) {
            setErrorState();
            return;
        }
        UpEvent upEvent = new UpEvent();
        upEvent.setDeviceId(DeviceUtil.getDeviceUniqueId());
        upEvent.setChannelID(joyNoteId);
        socket.emit("message", getEventJson(upEvent));

        Logger.getDefault().info("AudioFileSender", "startRecord() : " + getEventJson(upEvent));
    }

    private void pauseRecord() {
        if (joyNoteId == null || socket == null || !socket.isActive() || !socket.connected()) {
            setErrorState();
            return;
        }
        UpEvent upEvent = new UpEvent();
        upEvent.setDeviceId(DeviceUtil.getDeviceUniqueId());
        upEvent.setChannelID(joyNoteId);
        upEvent.setEventName(DOWNSIDE_STOP_RECORD);
        socket.emit("message", getEventJson(upEvent));

        Logger.getDefault().info("AudioFileSender", "pauseRecord() : " + getEventJson(upEvent));
    }

    /**
     * @noinspection UnusedReturnValue
     */
    public void startUploadAudioFile(@NonNull String joyNoteId,
                                     @NonNull String filePath,
                                     @NonNull AudioStateListener audioStateListener,
                                     @NonNull AudioTextListener audioTextListener) {
        showSocketDebugLog("start");
        if (this.joyNoteId != null || index != -1) {
            stopAction();
        }
        connectionState.set(FLAG_DISCONNECTED);
        this.joyNoteId = joyNoteId;
        this.filePath = filePath;
        this.audioStateListener = audioStateListener;
        blockSize = audioStateListener.getAudioBufferSize() * 5;
        this.audioTextListener = audioTextListener;
        receiveIndex.clear();
        sendIndex.clear();
        index = 0;
        audioFinishListener = null;
        checkHandler.sendEmptyMessage(CHECK_STATE);
        //慧记12 AudioFileSender 启动录制+链接socket
        connect();
    }

    private void checkState() {
        if (audioStateListener != null) {
            audioStateListener.netState(hasNotNet());
        }
        int state = connectionState.get();
        if (state == FLAG_CONNECTED) {
            if (audioStateListener != null) {
                audioStateListener.netState(false);
            }
        }
        showSocketDebugLog("checkState----=" + state);
        if (state != FLAG_CONNECTED && audioFinishListener != null) {
            sendFinishRequest();
            return;
        }
        switch (state) {
            case FLAG_CONNECTED:
            case FLAG_CONNECTING:
            case FLAG_CONNECTING_PAUSING:
            case FLAG_REFRESH_TOKEN:
            case FLAG_FINISHING:
                break;
            case FLAG_ERROR:
                if (socket != null) {
                    if (socket.isActive() || socket.connected()) {
                        socket.disconnect();
                    }
                }
                if (joyNoteId != null) {
                    connect();
                }
                break;
            case FLAG_CONNECTING_FILE_END:
                if (audioStateListener.hasPause()) {
                    connectionState.set(FLAG_CONNECTING_PAUSING);
                    pauseRecord();
                } else {
                    sendDataBlock();
                }
                break;
            case FLAG_CONNECTING_PAUSE:
                if (socket != null) {
                    if (socket.isActive() || socket.connected()) {
                        socket.disconnect();
                    }
                }
                connectionState.set(FLAG_DISCONNECTED);
            case FLAG_DISCONNECTED:
                if (!audioStateListener.hasPause()) {
                    connect();
                }
                break;
        }
    }

    private static boolean hasNotNet() {
        return ConnectivityUtils.getNetWorkType(AppBase.getAppContext()) <= 0;
    }

    private void setErrorState() {
        if (socket != null) {
            socket.disconnect();
        }
        connectionState.set(FLAG_ERROR);
    }

    public void finishUploadAudioFileForce() {
        stopAction();
    }

    public void finishUploadAudioFile(@NonNull String joyNoteId, String bizId, @NonNull final AudioFinishListener audioFinishListener) {
        try {
            if (randomAccessFile != null) {
                finalFileSize = randomAccessFile.length();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (hasNotNet()) {
            audioFinishListener.onResult(false, FINISH_FAIL);
            return;
        }
        this.audioFinishListener = audioFinishListener;
        this.bizId = bizId;
        if (this.joyNoteId == null) {
            this.joyNoteId = joyNoteId;
            sendFinishRequest();
        }
    }

    private static class CheckHandler extends Handler {
        @Override
        public void handleMessage(@NonNull Message msg) {
            AudioFileSender audioFileSender = AudioFileSender.getInstance();
            if (audioFileSender.joyNoteId == null) {
                return;
            }
            if (msg.what == CHECK_STATE) {
                showSocketDebugLog("msg.what----=" + msg.what);
                audioFileSender.checkState();
                postDelayed(() -> sendEmptyMessage(CHECK_STATE), 1000);
            } else if (msg.what == CHECK_SEND) {
                audioFileSender.sendDataBlock();
            }
        }
    }

    private int getFileData(long index, String filePath, byte[] data) {
        if (index < 0 || filePath == null) {
            return -1;
        }
        try {
            int len = data.length;
            if (randomAccessFile == null) {
                randomAccessFile = new RandomAccessFile(new File(filePath), "r");
            }
            long length = randomAccessFile.length();
            if ((length > (index + 4) * len) || audioFinishListener != null) {
                waitTime = 50;
            } else {
                waitTime = 200;
            }
            if (length > (index + 3) * len || audioFinishListener != null) {
                randomAccessFile.seek(index * len);
                return randomAccessFile.read(data);
            } else {
                return -1;
            }
        } catch (Exception e) {
            e.printStackTrace();
            closeFile();
        }
        return -1;
    }

    private void closeFile() {
        if (randomAccessFile != null) {
            try {
                randomAccessFile.close();
                randomAccessFile = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
