package com.jd.oa.player;

import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.hardware.SensorManager;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.OrientationEventListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.IntDef;
import androidx.core.content.ContextCompat;

import com.jd.jdvideoplayer.playback.BrightnessHelper;
import com.jd.jdvideoplayer.playback.ShowChangeLayout;
import com.jd.jdvideoplayer.view.IconDialog;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.joy.note.R;
import com.jingdong.common.DpiUtil;
import com.jingdong.common.UnLog;
import com.jingdong.common.unification.video.VideoConstant;
import com.jingdong.common.unification.video.player.AVideoMtaListener;
import com.jingdong.common.unification.video.player.AVideoPlayStateListener;
import com.jingdong.common.unification.video.player.AVideoViewBtClickListener;
import com.jingdong.common.unification.video.player.IProgrssChangeListener;
import com.jingdong.common.unification.video.player.IVideoViewOnTouchListener;
import com.jingdong.common.unification.video.player.ItemVideoPlayerController;
import com.jingdong.common.unification.video.player.NetUtils;
import com.jingdong.common.unification.video.player.VideoPlayUtil;
import com.jingdong.common.unification.video.player.VideoSeekBarBgDrawable;
import com.jingdong.common.videoplayer.IVideoPlayerCtrlViewListener;
import com.jingdong.common.videoplayer.IViewPlayerControl;
import com.jingdong.common.videoplayer.VideoPlayerUtils;
import com.jingdong.common.widget.image.UnNetImageView;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

import tv.danmaku.ijk.media.example.utils.IjkLib;
import tv.danmaku.ijk.media.example.widget.media.IPlayerControl;
import tv.danmaku.ijk.media.example.widget.media.IjkVideoView;
import tv.danmaku.ijk.media.player.IMediaPlayer;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;


/**
 * 视频播放器
 *
 * <AUTHOR>
 * 2022.08
 * @noinspection UnusedReturnValue, unused
 */
class JoyNoteVideoPlayView extends FrameLayout implements View.OnClickListener, View.OnTouchListener, IPlayerControl.OnPlayerStateListener {
    /**
     * TAG
     */
    private static final String TAG = JoyNoteVideoPlayView.class.getSimpleName();
    /**
     * MAX_PROGRESS 最大进度
     */
    private static final int MAX_PROGRESS = 1000;
    private static final int MESSAGE_HIDE_CENTER_TIP = 5;

    private static final int MESSAGE_SEEK_TO_POSITION = 6;
    private static final int SLIDE_ANGLE = 45;
    private static final int SPAN_MINUTES = 15;
    /**
     * mContext
     */
    private Context mContext;
    /**
     * rootLayout 根布局
     */
    public RelativeLayout rootLayout;
    public RelativeLayout jdVideoUiLayout;
    /**
     * 播放器
     */
    private IjkVideoView videoView;

    /**
     * 底部视频分享按钮
     */
    private TextView bottomShareIcon;

    /**
     * 播放器底部控制bar
     */
    private View bottomBarLayout;
    /**
     * 视频bottomBar的播放按钮
     */
    private ImageView playIvOnBottomBar;
    /**
     * 视频全屏按钮
     */
    private RelativeLayout fullscreenIv;

    /**
     * 视频portrait速度按钮
     */
    private TextView mSpeedSetIv;

    /**
     * 视频landscape速度按钮
     */
    private TextView mSpeedSetIvLandscape;
    /**
     * 视频播放进度条
     */
    private SeekBar mSeekBar;
    /**
     * 当前播放的时间
     */
    private TextView currentTimeTv;
    /**
     * 总时间
     */
    private TextView endTimeTv;

    /**
     * 播放器封面，播放前的封面或者缩列图
     */
    private UnNetImageView coverIv;
    /**
     * 视频中间的播放按钮
     */
    private ImageView centerPlayIv;
    /**
     * 重新播放按钮
     */
    private View replayIcon;
    /**
     * 有声 无声按钮
     */
    private TextView voiceIcon;
    /**
     * 底部进度条
     */
    private ProgressBar bottomProgressBar;

    /**
     * 直播标识
     */
    private ImageView liveIconIv;
    /**
     * loading 圈
     */
    private LinearLayout loadingLayout;
    /**
     * 网络错误提示
     */
    private LinearLayout mNetErrorLayout;
    /**
     * 加载错误文案
     */
    private TextView errorTipTv;
    /**
     * 加载错误图标
     */
    private ImageView loadErrorIv;
    /**
     * 重试
     */
    private TextView mClickToRetry;

    /**
     * 小屏UI 播放器顶部控制bar
     */
    private View smallTopBarLayout;
    /**
     * 有声 无声按钮
     */
    private TextView voiceIconSmall;
    /**
     * 底部进度条
     */
    private ProgressBar bottomProgressBarSmall;
    private LinearLayout errorLayoutSmall;
    private TextView retryTvSmall;
    private TextView errorTipTvSmall;
    /**
     * loading 圈
     */
    private LinearLayout loadingLayoutSmall;
    /**
     * 是否是横屏
     */
    private boolean isLandScape;
    /**
     * 当前播放位置
     */
    public int playPostion;
    /**
     * Activity界面方向监听
     */
    private OrientationEventListener orientationEventListener;

    /**
     * 上一次旋转角度
     */
    private int oldOrientation;
    /**
     * item 内嵌播放器布局
     */
    private View itemVideo;
    private ItemVideoPlayerController itemCtrl;

    /**
     * 视频资源加载失败的情况下是否显示“重试”按钮
     */
    private boolean loadErrorRetry;
    private boolean isShowErrorLayout = true;

    /**
     * * 空闲
     */
    public static final int STATE_IDLE = 330;
    /**
     * 播放出错
     */
    public static final int STATE_ERROR = 331;
    /**
     * 准备中/加载中
     */
    public static final int STATE_PREPARING = 332;
    /**
     * 准备完成
     */
    public static final int STATE_PREPARED = 333;
    /**
     * 播放中
     */
    public static final int STATE_PLAYING = 334;
    /**
     * 暂停
     */
    public static final int STATE_PAUSED = 335;
    /**
     * 播放完成
     */
    public static final int STATE_COMPLETED = 336;

    /**
     * 停止
     */
    public static final int STATE_STOP = 337;
    /**
     * 当前状态
     */
    private int status = STATE_IDLE;
    /**
     * 弹层状态
     */
    public static final int STATE_NET_ERROR = 400;
    public static final int STATE_LOAD_ERROR = 401;
    public static final int STATE_NO_WIFI_TIP = 402;
    /**
     * 弹框标识 -1表示没有弹框（异常或非wifi提示）
     */
    private int currentTipState = -1;
    /**
     * ====================播放器中使用的播放状态 结束=================================
     */

    private boolean isLoading = false;
    private final boolean sharedEnable = false;
    private boolean bottomSharedEnable = false;
    private int thisProgress = 0;
    private int oldProgress = 0;
    private int buffProgress = 0;
    private AVideoPlayStateListener onPlayerStateListener;
    /**
     * 埋点实现类
     */
    private AVideoMtaListener mtaListener;
    /**
     * 控制模块view控制监听器，目前提供显示 和隐藏
     */
    private IVideoPlayerCtrlViewListener ctrlViewListener;
    private IProgrssChangeListener progressChangeListener;
    private IProgrssChangeListener seekBarChangeListener;
    private AVideoViewBtClickListener videoViewBtClickListener;
    private IVideoViewOnTouchListener videoViewOnTouchListener;
    /**
     * 当前播放位置
     */
    private int currentPosition;
    /**
     * 播放总时长
     */
    private int duration;
    /**
     * 正常视频播放总时长
     */
    private int normalVideoDuration;

    /**
     * 声音按钮状态 -1时 不显示声音按钮  0中部声音按钮显示 1 底部声音按钮显示
     */
    private int voiceIconState;

    /**
     * 是否一直显示声音图标(在全屏模式下)
     */
    private boolean isVoiceIconKeepVisiInFullScreen;
    /**
     * 是否无声播放 默认有声
     */
    private boolean isVoiceOff;
    /**
     * 声音按钮是否点击过，如果点击过，随着状态栏显隐，如果没点击过，一直显示
     */
    private boolean isVoiceFirstClicked;
    /**
     * 第一次打开播放
     */
    private boolean isFirstPlay = true;
    /**
     * 底部进度跳是否显示  默认不显示
     */
    private boolean isShowBottomProgressBar;

    /**
     * 是否保持底部控制条常显
     */
    private boolean isKeepBottomProgressBarVisi = false;
    /**
     * 是否自动播放  默认自动播放
     */
    private boolean isAutoPlay = true;
    /**
     * 记录进行后台时的播放状态1为播放，0为暂停，-1为初始态
     */
    private int bgState = -1;
    /**
     * 是否是直播 默认为非直播，true为直播false为点播
     */
    private boolean isLive;
    /**
     * 是否隐藏中间播放按钮，默认隐藏，true为隐藏，false为不隐藏
     */
    private boolean isHideCenterPlayer = true;
    /**
     * 是否隐藏buttonBar，true为隐藏，false为不隐藏
     */
    private boolean isHideBottomBar;
    /**
     * 是否隐藏额外布局 头 底部
     */
    private boolean isHideControlPanl;
    /**
     * 是否隐藏重新播放按钮
     */
    private boolean isHideRetryBt;
    private boolean isDestory = false;
    /**
     * loading 超时时间
     */
    private static final int TIME_OUT = 15 * 1000;
    /**
     * 3s 自动隐藏top bottom
     */
    private static final int SHOW_TIME_MAX = 6000;
    /**
     * 同步进度
     */
    private static final int MESSAGE_SHOW_PROGRESS = 1;
    /**
     * loading 超时
     */
    private final static int TIME_OUT_WHAT = 2;
    /**
     * 自动隐藏top bottom
     */
    private final static int HIDE_WHAT = 3;
    /**
     * 获取下载速度
     */
    private final static int MESSAGE_HANDLER_SPEED = 4;
    /**
     * 来源
     */
    public int jumpFrom = -1;
    /**
     * 是否是小窗  默认大窗
     */
    private int screenState = SCREEN_NORMAL;//0 正常屏幕   1 小屏   2 全屏 3 ITEM 内嵌
    private int beforeScreenState = 0;//从哪一屏幕状态切换过来
    /**
     * SCREEN_NORMAL 正常屏幕（大屏展示场景，非全屏）
     */
    public final static int SCREEN_NORMAL = 0;
    /**
     * SCREEN_SMALL 小屏，悬浮屏样式
     */
    public final static int SCREEN_SMALL = 1;
    /**
     * SCREEN_BIG 全屏展示
     */
    public final static int SCREEN_BIG = 2;
    /**
     * SCREEN_ITEM item展示（需要看ui是否适合）
     */
    public final static int SCREEN_ITEM = 3;
    /**
     * SCREEN_NO_UI 只有中间按钮
     */
    public final static int SCREEN_NO_UI = 4;
    /**
     * 视频角度
     */
    private int degree;
    /**
     * 是否提示过非wifi
     */
    private boolean isAlreadyShowNoWifi = false;
    /**
     * 是否需要处理4g切wifi
     */
    private boolean isNeedAccessChangeToWifi;
    /**
     * 锚点view
     */
    private View mPointView;
    /**
     * seekbar背景
     */
    private VideoSeekBarBgDrawable nomalSeekBarProgrssBg;
    private VideoSeekBarBgDrawable nomalSeekBarSecondProgressBg;
    /**
     * bottomseekbar背景
     */
    private VideoSeekBarBgDrawable bottomSeekBarProgrssBg;
    private VideoSeekBarBgDrawable bottomSeekBarSecondProgressBg;
    private List<Integer> points;
    private int currentPoint = -1;
    /**
     * 进入播放器要不要判断是不是流量
     */
    private boolean isNeedJudgeNetOnStart = true;
    private GestureDetector gestureDetector;
    /**
     * 是否需要暂停时，唤起后台音乐 默认需要
     */
    private boolean isResumeBgMusicOnPause = true;
    /**
     * 是否设置过视频地址  主要用户进入时，判断是不是流量用
     */
    private boolean isSetSource = true;
    /**
     * 是否已经startPlay，主要解决非自动播放，非wifi下，弹出流量框后继续播放，不播放的问题
     */
    private boolean isAlreadyStartPlay = true;
    /**
     * 视频播放地址
     */
    private String videoPath;
    /**
     * 占位图片的高度和宽度
     */
    private int coverBitmapWidth;
    private int coverBitmapHeight;
    /**
     * 标记是暂停背景还是唤醒背景音乐的次数
     */
    private int pauseCount = 0;
    private int resumeCount = 0;
    /**
     * 是否来自评价
     */
    private boolean isFromComment;
    /**
     * 屏幕是否常亮
     */
    private boolean isScreenOn = false;
    /**
     * 是否需要在播放的时候保持屏幕常亮 默认需要
     */
    private boolean isNeedKeepScreenOn = true;
    /**
     * 片尾视频url
     */
    private String tailSource;
    /**
     * 片尾视频的封面图
     */
    private String tailCoverUrl;
    /**
     * 是否正在播放片尾
     */
    private boolean isPlayingTail;

    /**
     * voiceParent 静音按钮 父布局
     */
    private LinearLayout voiceParent;

    /**
     * 底部声音按钮
     */
    private ImageView bottomVoice;
    /**
     * 底部控制栏 声音按钮 占位作用  INVISIBLE
     */
    private ImageView bottomVoicePlaceHolder;


    /**
     * 当使用流量播放视频时  弹出toast提醒
     */
    private boolean flowToastAlert = false;

    /**
     * 底部控制栏副本 只包含一个声音按钮
     */
    private FrameLayout bottomBarLayoutCopy;

    /**
     * 底部声音按钮 有声时的图片资源
     */
    private int bottomVoiceOnRes;

    /**
     * 底部声音按钮 无声时的图片资源
     */
    private int bottomVoiceOffRes;

    /**
     * isLoopPlay 循环播放标识
     */
    private boolean isLoopPlay;
    /**
     * isLocalVideo 是否是本地视频
     */
    private boolean isLocalVideo;

    private boolean isNeedVideoTop;
    private int videoTopMargin;
    private RelativeLayout videoLayout;

    private final int mMinScrollDistance = 20;
    /**
     * 消息处理
     */
    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                //同步播放进度
                case MESSAGE_SHOW_PROGRESS:
                    syncProgress();
                    if (status == STATE_PLAYING) {
                        msg = obtainMessage(MESSAGE_SHOW_PROGRESS);
                        sendMessageDelayed(msg, 100);
                    }
                    break;
                case TIME_OUT_WHAT:
                    showLoadErrorLayout(true);
                    break;
                case HIDE_WHAT:
                    hide();
                    break;
                case MESSAGE_HANDLER_SPEED:
                    if (status == STATE_PLAYING) {
                        msg = obtainMessage(MESSAGE_HANDLER_SPEED);
                        sendMessageDelayed(msg, 2000);
                    }
                    break;
                case MESSAGE_HIDE_CENTER_TIP:
                    mCenterChangeTip.setVisibility(View.GONE);
                    break;
                case MESSAGE_SEEK_TO_POSITION:
                    seekToPosition(downPlayPosition);
                    break;
                default:
                    break;
            }
        }
    };
    private TextView mLiveNameTxt;
    private View topFunctionLayer;
    private AudioManager mAudioManager;
    private int mMaxVolume;
    private View mCenterChangeTip;
    private ImageView centerTipImage;
    private TextView centerTipText;

    /**
     * gesture  start
     */

    private static final int NONE = 0, VOLUME = 1, BRIGHTNESS = 2, FF_REW = 3;
    private @ScrollMode int mScrollMode = NONE;
    private ShowChangeLayout scl;
    private TextView centerTipTextTimer;
    private OnFloatWindowClick mOnFloatWindowClick = new OnFloatWindowClick() {
        @Override
        public void onClick() {

        }
    };

    private boolean closeActivity;

    private MePlayerStateListener mePlayerStateListener;

    public void setBackBtnFunction(boolean closeActivity) {
        this.closeActivity = closeActivity;
    }

    @IntDef({NONE, VOLUME, BRIGHTNESS, FF_REW})
    @Retention(RetentionPolicy.SOURCE)
    private @interface ScrollMode {
    }

    //    private GestureDetector mGestureDetector;
//    private VideoGestureListener mVideoGestureListener;
    //横向偏移检测，让快进快退不那么敏感
    private final int offsetX = 1;
    private boolean hasFF_REW = false;

    private int maxVolume = 0;
    private int oldVolume = 0;
    private int newProgress = 0, oldProgressLater = 0;
    private BrightnessHelper mBrightnessHelper;
    private float brightness = 1;
    private Window mWindow;
    private WindowManager.LayoutParams mLayoutParams;

    private View closeBtn;
    private boolean audioSkin = false;

    public JoyNoteVideoPlayView(Context context) {
        super(context);
        initView(false);
    }

    public JoyNoteVideoPlayView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(false);
    }

    public JoyNoteVideoPlayView(Context context, boolean isAudioSkin) {
        super(context);
        initView(isAudioSkin);
    }


    public void setmContext(Context newContext) {
        this.mContext = newContext;
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initView(boolean isAudioSkin) {
        audioSkin = isAudioSkin;
        mContext = getContext();
        IjkLib.init();
        inflate(mContext, audioSkin ? R.layout.me_video_play_view_audio : R.layout.joy_note_video_view, this);
        rootLayout = (RelativeLayout) findViewById(R.id.app_video_box);
        jdVideoUiLayout = (RelativeLayout) findViewById(R.id.jd_video_ui_layout);
        bottomProgressBar = (ProgressBar) findViewById(R.id.app_video_bottom_progressbar);
        bottomProgressBar.setMax(MAX_PROGRESS);
        bottomProgressBar.setProgress(0);
        videoView = (IjkVideoView) findViewById(R.id.video_view);
        initUIlayout(true);
        loadingLayout = (LinearLayout) findViewById(R.id.loadingLayout);
        liveIconIv = (ImageView) findViewById(R.id.liveIcon);

        coverIv = findViewById(R.id.iv_corver);
        bottomShareIcon = (TextView) findViewById(R.id.shareIcon);

        centerPlayIv = (ImageView) findViewById(R.id.play_icon_center);
        replayIcon = findViewById(R.id.app_video_replay_icon);
        voiceIcon = (TextView) findViewById(R.id.voiceIcon);
        voiceParent = findViewById(R.id.voiceParent);
        bottomVoice = findViewById(R.id.iv_bottom_voice);
        bottomBarLayoutCopy = findViewById(R.id.fl_bottom_bar);
        videoLayout = findViewById(R.id.videoLayout);

        mNetErrorLayout = (LinearLayout) findViewById(R.id.errorLayout);
        errorTipTv = (TextView) findViewById(R.id.errorTipTv);
        mClickToRetry = (TextView) findViewById(R.id.retry);
        loadErrorIv = (ImageView) findViewById(R.id.loadErrorIv);
        //小屏
        smallTopBarLayout = findViewById(R.id.app_video_top_box_small);
        //  视频关闭按钮
        ImageButton smallCloseIb = (ImageButton) findViewById(R.id.app_video_finish_small);
        voiceIconSmall = (TextView) findViewById(R.id.play_icon_voice_small);
        bottomProgressBarSmall = (ProgressBar) findViewById(R.id.app_video_bottom_progressbar_small);
        bottomProgressBarSmall.setMax(MAX_PROGRESS);
        smallCloseIb.setOnClickListener(this);
        voiceIconSmall.setOnClickListener(this);
        errorLayoutSmall = (LinearLayout) findViewById(R.id.errorLayoutSmall);
        retryTvSmall = (TextView) findViewById(R.id.retrySmall);
        errorTipTvSmall = (TextView) findViewById(R.id.errorTipTvSmall);
        retryTvSmall.setOnClickListener(this);
        loadingLayoutSmall = (LinearLayout) findViewById(R.id.loadingLayoutSmall);
        /**
         * 音频前进
         */
        View audioForwardBtn = findViewById(R.id.audio_forward_btn);
        /**
         * 音频后退
         */
        View audioBackBtn = findViewById(R.id.audio_back_btn);
        audioForwardBtn.setOnClickListener(this);
        audioBackBtn.setOnClickListener(this);
//        大屏
        videoView.setOnTouchListener(this);
        this.setOnTouchListener(this);

        mClickToRetry.setOnClickListener(this);
        centerPlayIv.setOnClickListener(this);
        bottomShareIcon.setOnClickListener(this);
        replayIcon.setOnClickListener(this);
        voiceIcon.setOnClickListener(this);
        videoView.setOnPlayerStateListener(this);
        bottomVoice.setOnClickListener(this);
        orientationEventListener = new OrientationEventListener(mContext, SensorManager.SENSOR_DELAY_NORMAL) {
            @Override
            public void onOrientationChanged(int orientation) {
                if (orientation == OrientationEventListener.ORIENTATION_UNKNOWN) {
                    return;  //手机平放时，检测不到有效的角度
                }
                //只检测是否有四个角度的改变
                if (orientation > 350 || orientation < 10) { //0度
                    orientation = 0;
                } else if (orientation > 80 && orientation < 100) { //90度
                    orientation = 90;
                } else if (orientation > 170 && orientation < 190) { //180度
                    orientation = 180;
                } else if (orientation > 260 && orientation < 280) { //270度
                    orientation = 270;
                } else {
                    return;
                }
                optOrientation(orientation);
            }
        };
        optEnable(false);
        if (isHideControlPanl) {
            hide();
        } else {
            show(true);
        }


        setOptions(false);
        int fullScreenPortResourceId = R.drawable.un_video_screen_v_to_h;
        int fullScreenLandResourceId = R.drawable.un_video_screen_h_to_v;

        bottomVoiceOnRes = R.drawable.video_player_voice_on;
        bottomVoiceOffRes = R.drawable.video_player_voice_off;

        scl = (ShowChangeLayout) findViewById(R.id.scl);
        mCenterChangeTip = findViewById(R.id.centerTipLayout);
        centerTipImage = (ImageView) mCenterChangeTip.findViewById(R.id.centerTipIcon);
        centerTipText = (TextView) mCenterChangeTip.findViewById(R.id.centerTipIext);
        centerTipTextTimer = (TextView) mCenterChangeTip.findViewById(R.id.centerTipIextTimer);

        int mScreenWidthPixels = mContext.getResources().getDisplayMetrics().widthPixels;
        int mScreenHeightPixels = mContext.getResources().getDisplayMetrics().heightPixels;

        //初始化获取音量属性
        mAudioManager = (AudioManager) mContext.getSystemService(Service.AUDIO_SERVICE);
        maxVolume = mAudioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);

        //初始化亮度调节
        mBrightnessHelper = new BrightnessHelper(mContext);

        //下面这是设置当前APP亮度的方法配置
        mWindow = getActivity().getWindow();
        mLayoutParams = mWindow.getAttributes();
        brightness = mLayoutParams.screenBrightness;
    }

    private void initUIlayout(boolean isLand) {
        isLandScape = isLand;
        if (!audioSkin) {
            if (isLand) {
                jdVideoUiLayout.removeAllViews();
                jdVideoUiLayout.addView(inflate(mContext, R.layout.jdvideo_media_layout_control_hor, null));
            } else {
                jdVideoUiLayout.removeAllViews();
                jdVideoUiLayout.addView(inflate(mContext, R.layout.jdvideo_media_layout_control_por, null));
            }
        }

        topFunctionLayer = findViewById(R.id.topFunctionLayer);
        mLiveNameTxt = (TextView) findViewById(R.id.me_video_live_title);
        closeBtn = findViewById(R.id.me_close_new);
        closeBtn.setOnClickListener(v -> {
            if (!isLandScape || closeActivity) {
                getActivity().finish();
            } else {
                //全屏半屏切换
                toggleFullScreen(0);
            }
        });
        bottomBarLayout = findViewById(R.id.ll_bottom_bar);
        bottomBarLayout.setOnTouchListener(this);
        findViewById(R.id.app_video_play_rl).setOnClickListener(this);
        playIvOnBottomBar = (ImageView) findViewById(R.id.app_video_play);

        currentTimeTv = (TextView) findViewById(R.id.app_video_currentTime_full);
        endTimeTv = (TextView) findViewById(R.id.app_video_endTime_full);
        mSeekBar = (SeekBar) findViewById(R.id.app_video_seekBar);
        setSeekBar();
        fullscreenIv = (RelativeLayout) findViewById(R.id.app_video_fullscreen);
        fullscreenIv.setOnClickListener(this);
        if (isLandScape) {
            mSpeedSetIv = findViewById(R.id.me_tv_speed_set);
            findViewById(R.id.me_tv_speed_set).setOnClickListener(v -> {
                //倍速
                speedDialogShow();
            });
            mSpeedSetIv.setText(sDefaultItem.getName());
//            mSpeedSetIv.setImageResource(sDefaultItem.getResourceId());
        } else {
            mSpeedSetIvLandscape = findViewById(R.id.playback_more_iv);
            mSpeedSetIvLandscape.setText(sDefaultItem.getName());
            mSpeedSetIvLandscape.setOnClickListener(v -> new PlaySpeedDialog(mContext, item -> {
                if (item == null) {
                    return;
                }
                if (item == sDefaultItem) {
                    return;
                }
                if (item.getValue() < 0) {
                    return;
                }
                sDefaultItem = item;
                float speed = (float) item.getValue();
                if (videoView != null && videoView.getIjkMediaPlayer() != null) {
                    videoView.getIjkMediaPlayer().setSpeed(speed);
                    mSpeedSetIvLandscape.setText(item.getName());
                    show(true);
                }
            }, sDefaultItem).show());
        }
        bottomVoicePlaceHolder = findViewById(R.id.iv_bottom_voice_copy);
        updatePausePlay();
        videoView.setOnPlayerEventListener(new IMediaPlayer.OnPlayerEventListener() {
            @Override
            public void onPlayEvent(int i) {
                if (mePlayerStateListener == null) return;
                if (IMediaPlayer.OnPlayerEventListener.PLAYER_PAUSE == i ||
                    IMediaPlayer.OnPlayerEventListener.PLAYER_STOP == i) {
                    mePlayerStateListener.onPause();
                } else if (IMediaPlayer.OnPlayerEventListener.PLAYER_START == i ||
                        IMediaPlayer.OnPlayerEventListener.PLAYER_RENDERING_START == i) {
                    mePlayerStateListener.onPlay();
                }
            }
        });
    }

    private void setSeekBar() {
        Drawable bgDrawable = ContextCompat.getDrawable(mContext, R.drawable.video_player_seek_bg);
        nomalSeekBarSecondProgressBg = new VideoSeekBarBgDrawable(null, Color.parseColor("#cbcbcb"), Color.parseColor("#cbcbcb"), MAX_PROGRESS);
        nomalSeekBarProgrssBg = new VideoSeekBarBgDrawable(null, Color.parseColor("#FF4848"), Color.parseColor("#FF4848"), MAX_PROGRESS);
        Drawable[] drawables = new Drawable[]{bgDrawable, nomalSeekBarSecondProgressBg, nomalSeekBarProgrssBg};
        LayerDrawable layerDrawable = new LayerDrawable(drawables);
        layerDrawable.setId(0, android.R.id.background);
        layerDrawable.setId(1, android.R.id.secondaryProgress);
        layerDrawable.setId(2, android.R.id.progress);
        if (!audioSkin) {
            mSeekBar.setProgressDrawable(layerDrawable);
        }

        Drawable bottomBgDrawable = ContextCompat.getDrawable(mContext, R.drawable.video_player_bottom_seek_bg);
        bottomSeekBarSecondProgressBg = new VideoSeekBarBgDrawable(null, Color.parseColor("#cbcbcb"), Color.parseColor("#cbcbcb"), MAX_PROGRESS);
        bottomSeekBarProgrssBg = new VideoSeekBarBgDrawable(null, Color.parseColor("#FFFFFF"), Color.parseColor("#FFFFFF"), MAX_PROGRESS);
        Drawable[] bottomDrawables = new Drawable[]{bottomBgDrawable, bottomSeekBarSecondProgressBg, bottomSeekBarProgrssBg};
        LayerDrawable bottomLayerDrawable = new LayerDrawable(bottomDrawables);
        bottomLayerDrawable.setId(0, android.R.id.background);
        bottomLayerDrawable.setId(1, android.R.id.secondaryProgress);
        bottomLayerDrawable.setId(2, android.R.id.progress);
        bottomProgressBar.setProgressDrawable(bottomLayerDrawable);

        mSeekBar.setMax(MAX_PROGRESS);
        mSeekBar.setOnSeekBarChangeListener(mSeekListener);
    }

    SpeedItem sDefaultItem = new SpeedItem("1.0X", 1);
    IconDialog speedDialog = null;

    void speedDialogShow() {

        if (videoView == null) {
            return;
        }

        PlayBackSpeedView speedView = new PlayBackSpeedView(mContext, item -> {
            if (speedDialog != null) {
                speedDialog.dismiss();
            }
            if (item == null) {
                return;
            }
            if (item == sDefaultItem) {
                return;
            }
            if (item.getValue() < 0) {
                return;
            }
            sDefaultItem = item;
            float speed = (float) item.getValue();
            if (videoView != null && videoView.getIjkMediaPlayer() != null) {
                videoView.getIjkMediaPlayer().setSpeed(speed);
                show(true);
            }
            mSpeedSetIv.setText(item.getName());
//            mSpeedSetIv.setImageResource(item.getResourceId());
        }, sDefaultItem);
        speedDialog = new IconDialog(mContext, speedView);
        if (speedDialog.getWindow() != null) {
            speedDialog.getWindow().setWindowAnimations(R.style.speed_dialog_animation);
        }
        speedDialog.show();
    }

    public IjkVideoView getIjkVideoView() {
        return videoView;
    }

    public void setLiveRoomName(String mLiveRoomName) {
        if (mLiveNameTxt != null) {
            if (!TextUtils.isEmpty(mLiveRoomName)) {
                mLiveNameTxt.setText(mLiveRoomName);
            } else {
                mLiveNameTxt.setText("");
            }
        }
    }

    private void setOptions(boolean isLive) {
        IPlayerControl.PlayerOptions options = new IPlayerControl.PlayerOptions(isLive);
        options.setAspectRatio(IPlayerControl.PlayerOptions.AR_ASPECT_FIT_PARENT);
        options.setIsRequestAudioFocus(false);
        //设置此属性画面更清晰或者使用硬编码
        options.addCustomOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_loop_filter", 0);
        videoView.setPlayerOptions(options);
    }

    /**
     * 设置循环
     */
    public void setLoop(boolean loop) {
        isLoopPlay = loop;
    }

    /**
     * 设置video 到顶部 不居中
     */
    public void setVideoTop(boolean isNeedVideoTop, int videoMarginTop) {
        this.isNeedVideoTop = isNeedVideoTop;
        this.videoTopMargin = videoMarginTop;
    }

    /**
     * 处理到顶的属性问题
     */
    public void videoTopParams() {
        if (!isNeedVideoTop) {
            return;
        }
        RelativeLayout.LayoutParams videoParams = null;
        float videoWidth = getVideoWidth();
        float videoHeight = getVideoHeight();

        Configuration mConfiguration = this.getResources().getConfiguration(); //获取设置的配置信息
        int ori = mConfiguration.orientation; //获取屏幕方向
        if (ori == Configuration.ORIENTATION_LANDSCAPE) {
            //横屏
            videoParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            videoParams.addRule(RelativeLayout.CENTER_IN_PARENT);
            videoLayout.setLayoutParams(videoParams);
        } else if (ori == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏，只有宽度大的时候才做本操作
            if (videoWidth > videoHeight && isNeedVideoTop) {
                int width = DpiUtil.getWidth(mContext) + 1; //为了防止无法占满屏幕
                float height = width * videoHeight / videoWidth;
                videoParams = new RelativeLayout.LayoutParams(width, (int) height);
                videoParams.topMargin = videoTopMargin;
                videoParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
                videoLayout.setLayoutParams(videoParams);
            }

        }
    }

    /**
     * 点击事件监听
     */
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.playBackFloatWindow) {
            mOnFloatWindowClick.onClick();
        } else if (v.getId() == R.id.audio_back_btn || v.getId() == R.id.audio_forward_btn) {
            if (videoView == null) {
                return;
            }
            int newPosition = videoView.getCurrentPosition();
            if (v.getId() == R.id.audio_back_btn) {
                newPosition -= SPAN_MINUTES * 1000;
            } else {
                newPosition += SPAN_MINUTES * 1000;
            }
            videoView.seekTo(newPosition);
            if (!videoView.isPlaying()) {
                startPlay();
            }
        } else if (v.getId() == R.id.app_video_fullscreen) {
            if (videoViewBtClickListener != null) {
                return;
            }
            //全屏半屏切换
            toggleFullScreen(-1);
            show(true);
        } else if (v.getId() == R.id.app_video_play_rl || v.getId() == R.id.play_icon_center) {
            //播放/暂停
            //如果是中间播放按钮，并且自己实现了点击事件返回true，则控件不处理
            if (v.getId() == R.id.play_icon_center && videoViewBtClickListener != null && videoViewBtClickListener.centerPlayClick()) {
                return;
            }
            if (videoView.isPlaying()) {
                pausePlay();

                if (mtaListener != null) {
                    mtaListener.clickPauseOrPlay(false);
                }
                if (videoViewBtClickListener != null) {
                    videoViewBtClickListener.pauseOrPlayClick(true);
                }
            } else {
                if (VideoPlayUtil.isMobileNet(mContext) && !isAlreadyShowNoWifi) {
                    centerPlayIv.setVisibility(GONE);
                    showNetChangelayout();
                } else {
                    startPlay();
                    if (mtaListener != null) {
                        mtaListener.clickPauseOrPlay(true);
                    }
                    if (videoViewBtClickListener != null) {
                        videoViewBtClickListener.pauseOrPlayClick(false);
                    }
                }
            }
            show(true);
        } else if (v.getId() == R.id.app_video_back) {
            //返回
            if (videoViewBtClickListener != null && videoViewBtClickListener.bigBackClick()) {
                return;
            }
            getActivity().finish();
        } else if (v.getId() == R.id.app_video_finish_small) {
            //关闭
            if (videoViewBtClickListener != null && videoViewBtClickListener.smallCloseClick()) {
                return;
            }
            getActivity().finish();
        } else if (v.getId() == R.id.app_video_share || v.getId() == R.id.shareIcon) {
            //分享
            if (mtaListener != null) {
                mtaListener.clickShare();
            }
        } else if (v.getId() == R.id.retry || v.getId() == R.id.retrySmall) {
            if (!isSetSource) {
                this.hideErrorLayout();
                setPlaySource(videoPath, playPostion);
            } else if (!isAlreadyStartPlay) {
                this.hideErrorLayout();
                startPlay();
            } else {
                reTry();
            }
        } else if (v.getId() == R.id.app_video_replay_icon) {
            //重新播放
            videoView.seekTo(0);

            startPlay();
        } else if (v.getId() == R.id.voiceIcon || v.getId() == R.id.play_icon_voice_small || v.getId() == R.id.iv_bottom_voice) {
            isVoiceOff = !isVoiceOff;
            if (!isVoiceOff) {
                voiceIconSmall.setVisibility(GONE);
            }
            if (!isVoiceFirstClicked && screenState != SCREEN_SMALL) {
                if (bottomBarLayout.getVisibility() == GONE && !isVoiceIconKeepVisiInFullScreen) {
                    voiceIcon.setVisibility(GONE);
                }
                isVoiceFirstClicked = true;
            }
            changeVoiceState();
            if (mtaListener != null) {
                mtaListener.clickVoice(isVoiceOff);
            }
        }
    }

    /**
     * 设置静音按钮位置
     */
    public void setVoiceParentLP(RelativeLayout.LayoutParams layoutParams) {
        if (layoutParams != null) {
            voiceParent.setLayoutParams(layoutParams);
        }
    }

    /**
     * 获取静音按钮父布局属性
     */
    public RelativeLayout.LayoutParams getVoiceParentLP() {
        return (RelativeLayout.LayoutParams) voiceParent.getLayoutParams();

    }

    /**
     * 隐藏标题和底部栏
     */
    public void hide() {
        if (UnLog.D) {
            UnLog.d(TAG, "hide");
        }
        mHandler.removeMessages(HIDE_WHAT);
        if (ctrlViewListener != null) {
            ctrlViewListener.hide();
        }

        if (voiceIconState == 0 && isVoiceFirstClicked && voiceIcon.getVisibility() == VISIBLE && !isVoiceIconKeepVisiInFullScreen) {
            Animation animationVoice = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_leave_from_bottom, null);
            voiceIcon.clearAnimation();
            voiceIcon.startAnimation(animationVoice);
            voiceIcon.setVisibility(GONE);


        }

        if (voiceIconState == 1 && isVoiceFirstClicked && bottomVoice.getVisibility() == VISIBLE && !isVoiceIconKeepVisiInFullScreen) {
            Animation animationVoice = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_leave_from_bottom, null);
            bottomVoice.clearAnimation();
            bottomVoice.startAnimation(animationVoice);
            bottomVoice.setVisibility(GONE);


        }


        Animation animationTop = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_leave_from_top, new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (UnLog.D) {
                    UnLog.d(TAG, "isShowBottomProgressBar:" + isShowBottomProgressBar);
                }
                if (isShowBottomProgressBar) {
                    bottomProgressBar.setVisibility(VISIBLE);
                } else {
                    bottomProgressBar.setVisibility(GONE);
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        if (bottomBarLayout.getVisibility() == VISIBLE) {
            bottomBarLayout.clearAnimation();
            Animation animationBottom = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_leave_from_bottom, null);
            bottomBarLayout.startAnimation(animationBottom);
            bottomBarLayout.setVisibility(GONE);

        }

        if (isLive && liveIconIv.getVisibility() == VISIBLE) {
            liveIconIv.clearAnimation();
            liveIconIv.startAnimation(animationTop);
            liveIconIv.setVisibility(GONE);
        }

        if (mPointView != null && mPointView.getVisibility() == VISIBLE) {
            ObjectAnimator.ofFloat(mPointView, "translationY", 0, DpiUtil.dip2px(mContext, 30)).setDuration(300).start();
        }

        if (bottomSharedEnable && bottomShareIcon.getVisibility() == VISIBLE) {
            Animation animationShare = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_leave_from_bottom, null);
            bottomShareIcon.clearAnimation();
            bottomShareIcon.startAnimation(animationShare);
            bottomShareIcon.setVisibility(GONE);
        }

        if (topFunctionLayer != null) {
            topFunctionLayer.startAnimation(animationTop);
            topFunctionLayer.setVisibility(GONE);
        }
    }


    /**
     * 显示标题和底部栏
     *
     * @param isAutoHide 是否自动隐藏
     */
    public void show(boolean isAutoHide) {
        if (UnLog.D) {
            UnLog.d(TAG, "show");
        }
        if (ctrlViewListener != null) {
            ctrlViewListener.show();
        }

        if (voiceIconState == 0 && isVoiceFirstClicked && voiceIcon.getVisibility() != VISIBLE) {
            Animation animationVoice = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_entry_from_bottom, null);
            voiceIcon.clearAnimation();
            voiceIcon.startAnimation(animationVoice);
            voiceIcon.setVisibility(VISIBLE);

        }

        if (voiceIconState == 1 && isVoiceFirstClicked && bottomVoice.getVisibility() != VISIBLE) {
            Animation animationVoice = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_entry_from_bottom, null);
            bottomVoice.clearAnimation();
            bottomVoice.startAnimation(animationVoice);
            bottomVoice.setVisibility(VISIBLE);
        }

        if (!isHideBottomBar && bottomBarLayout.getVisibility() != VISIBLE) {
            Animation animationBottom = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_entry_from_bottom, null);
            bottomBarLayout.setVisibility(VISIBLE);
            bottomBarLayout.clearAnimation();
            bottomBarLayout.startAnimation(animationBottom);
        }

        if (!isHideBottomBar && voiceIconState == 1 && isVoiceFirstClicked && bottomBarLayoutCopy.getVisibility() != VISIBLE) {
            Animation animationBottom = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_entry_from_bottom, null);
            bottomBarLayoutCopy.setVisibility(VISIBLE);
            bottomBarLayoutCopy.clearAnimation();
            bottomBarLayoutCopy.startAnimation(animationBottom);
        }

        mHandler.removeMessages(HIDE_WHAT);
        if (isAutoHide && !isHideControlPanl) {
            mHandler.sendMessageDelayed(mHandler.obtainMessage(HIDE_WHAT), SHOW_TIME_MAX);
        }

        if (mPointView != null && mPointView.getVisibility() == VISIBLE) {
            ObjectAnimator.ofFloat(mPointView, "translationY", DpiUtil.dip2px(mContext, 30), 0).setDuration(300).start();
        }

        if (bottomSharedEnable && bottomShareIcon.getVisibility() != VISIBLE) {
            Animation animationShare = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_entry_from_bottom, null);
            bottomShareIcon.clearAnimation();
            bottomShareIcon.startAnimation(animationShare);
            bottomShareIcon.setVisibility(VISIBLE);
        }
        if (bottomProgressBar.getVisibility() == VISIBLE && !isKeepBottomProgressBarVisi) {
            bottomProgressBar.setVisibility(GONE);
        }

        if (topFunctionLayer != null) {
            Animation animationShare = VideoPlayUtil.getAnimation(mContext, R.anim.vd_option_entry_from_top, null);
            topFunctionLayer.clearAnimation();
            topFunctionLayer.startAnimation(animationShare);
            topFunctionLayer.setVisibility(VISIBLE);
        }
    }

    /**
     * 外部设置loading
     */
    public void setLoadingView(View loading) {
        if (loading != null) {
            loadingLayout.setBackgroundColor(Color.TRANSPARENT);
            loadingLayout.removeAllViews();
            loadingLayout.addView(loading);
        }
    }

    /**
     * 显示loading圈
     */
    public void showLoading() {
        if (!isLoading) {
            centerPlayIv.setVisibility(GONE);

            if (screenState == SCREEN_SMALL || screenState == SCREEN_NO_UI) {
                loadingLayoutSmall.setVisibility(View.VISIBLE);
            } else {
                loadingLayout.setVisibility(View.VISIBLE);
            }
            mHandler.sendEmptyMessageDelayed(TIME_OUT_WHAT, TIME_OUT);
        }
        isLoading = true;
    }

    /**
     * 隐藏loading圈
     */
    public void hideLoading() {
        if (isLoading) {
            if (screenState == SCREEN_SMALL || screenState == SCREEN_NO_UI) {
                loadingLayoutSmall.setVisibility(View.GONE);
            } else {
                loadingLayout.setVisibility(View.GONE);
            }
            mHandler.removeMessages(TIME_OUT_WHAT);
        }
        isLoading = false;
    }

    /**
     * 进度条滑动监听
     */
    private int dragPosition;
    private boolean isBuffering;
    boolean isDragging;
    private final SeekBar.OnSeekBarChangeListener mSeekListener = new SeekBar.OnSeekBarChangeListener() {

        /**数值的改变*/
        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            if (!fromUser) {
                //不是用户拖动的，自动播放滑动的情况
                return;
            }
            if (seekBarChangeListener != null && videoView != null) {
                seekBarChangeListener.onProgressChange((int) (progress / (float) MAX_PROGRESS * videoView.getDuration()), videoView.getDuration());
            }
            onProgressSought(progress);
        }

        /**开始拖动*/
        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {
            isDragging = true;
            mHandler.removeMessages(MESSAGE_SHOW_PROGRESS);
            mHandler.removeMessages(HIDE_WHAT);
            mHandler.removeMessages(MESSAGE_HANDLER_SPEED);
            if (mtaListener != null) {
                mtaListener.startTrackingTouch();
            }
        }

        /**停止拖动*/
        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {
//            int lastPosition = videoView.getDuration() * mSeekBar.getProgress() / 1000;
            if (UnLog.D) {
                UnLog.d(TAG, "dragPosition:" + dragPosition);
            }
            isDragging = false;
            if (mtaListener != null) {
                mtaListener.stopTrackingTouch();
            }
            if (videoView != null) {
                videoView.seekTo(dragPosition);
            }
            setSelectPoint(dragPosition, false);
            syncProgress();
            mHandler.sendEmptyMessage(MESSAGE_SHOW_PROGRESS);
            mHandler.sendEmptyMessage(MESSAGE_HANDLER_SPEED);
            mHandler.sendMessageDelayed(mHandler.obtainMessage(HIDE_WHAT), SHOW_TIME_MAX);
        }
    };

    private void onProgressSought(int progress) {
        showLoading();
        isBuffering = true;
        int newPosition = (int) ((float) getDuration() * progress / MAX_PROGRESS);

        if (duration - newPosition < MAX_PROGRESS) {
            newPosition = duration;
        }
        int position;
        if (newPosition >= duration) {
            position = duration - 500;
        } else {
            position = newPosition;
        }
        if (UnLog.D) {
            UnLog.d(TAG, "OnSeekBarChangeListener position:" + position);
        }
        seekTo = position;
        if (!videoView.isPlaying()) {
            startPlay();
        }
//            videoView.seekTo(position);
        //position * MAX_PROGRESS  视频很长时，会int越界
        int seekProgress = (int) ((float) position * MAX_PROGRESS / duration);
        mSeekBar.setProgress(seekProgress);
        setSeekBarBgProgress(seekProgress, false);
        if (mtaListener != null) {
            mtaListener.progressChangedFromUser(seekProgress);
        }
        if (isShowBottomProgressBar) {
            bottomProgressBar.setProgress(seekProgress);
            setSeekBarBgProgress(seekProgress, true);
        }
        if (screenState == SCREEN_SMALL) {
            bottomProgressBarSmall.setProgress(seekProgress);
        }
        if (progressChangeListener != null) {
            progressChangeListener.onProgressChange(seekProgress, MAX_PROGRESS);
        }
        dragPosition = position;
        if (currentTimeTv != null) {
            String time = VideoPlayUtil.generateTime(newPosition);
            currentTimeTv.setText(time);
        }
        if (seekTo == 0) {
            videoView.seekTo(position);
        }
    }

    private void optEnable(boolean enable) {
        mSeekBar.setEnabled(enable);
        playIvOnBottomBar.setEnabled(enable);
    }

    /**
     * 网络连接问题及网络切换，重试
     */
    private void reTry() {

        hideErrorLayout();
        if (UnLog.D) {
            UnLog.d(TAG, "reTry showloading");
        }
        showLoading();
        if (!isLocalVideo && !NetUtils.isNetworkAvailable(mContext)) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            showNetErrorlayout();
                        }
                    });

                }
            }).start();
        } else {
            if (UnLog.D) {
                UnLog.d(TAG, "playPostion:" + playPostion);
            }
            videoView.suspend();
            videoView.initRenders();
            videoView.seekTo(playPostion);
        }

    }

    public void liveReLink() {
        hideErrorLayout();
        videoView.postDelayed(new Runnable() {
            @Override
            public void run() {
                videoView.suspend();
                videoView.initRenders();
                videoView.start();
            }
        }, 50);
    }

    /**
     * 对应activity onPause
     */
    public void onPause() {
        if (UnLog.D) {
            UnLog.d(TAG, "onPause: isPlay" + videoView.isPlaying());
        }
        bgState = (status == STATE_PLAYING ? 1 : 0);
        if (bgState == 1) {
            pausePlay();
        }
    }

    /**
     * 对应activity onResume  onPause 之后调用
     */
    public void onResume() {
        if (UnLog.D) {
            UnLog.d(TAG, "onResume: bgState" + bgState);
        }
        if (bgState != -1) {
            //重新设置render 不然画面不更新
            videoView.initRenders();
            if (bgState == 1) {
                startPlay();
            }
        }
    }

    /**
     * 改变声音状态  有声音或无声音
     */
    public void setVoiceState(boolean isVoiceOn) {
        isVoiceOff = !isVoiceOn;
        videoView.setVolume(isVoiceOn ? 1 : 0);
    }


    public void onDestroy() {
        if (isDestory) {
            return;
        }
        isDestory = true;
        setScreenOff();
        JMAudioCategoryManager.getInstance().releaseJoyNoteVideo();
        if (orientationEventListener != null) {
            orientationEventListener.disable();
        }

        mHandler.removeMessages(MESSAGE_SHOW_PROGRESS);
        mHandler.removeMessages(TIME_OUT_WHAT);
        mHandler.removeMessages(HIDE_WHAT);
        mHandler.removeMessages(HIDE_WHAT);
        mHandler.removeMessages(MESSAGE_HANDLER_SPEED);
        VideoPlayUtil.clearAnimationCache();
        releaseInThread();
        resumeOtherVoice();

        if (videoView != null) {
            videoView.reportStop();
        }
    }

    /**
     * 显示缩略图
     */
    public JoyNoteVideoPlayView setCoverUrl(String url) {
        coverIv.setVisibility(VISIBLE);
        coverIv.setImage(url);
        return this;
    }

    /**
     * 设置占位图
     */
    public JoyNoteVideoPlayView setCoverBitmap(Bitmap coverBitmap) {
        if (coverBitmap != null) {
            coverIv.setVisibility(VISIBLE);
            coverIv.setImageBitmap(coverBitmap);
        }
        return this;
    }

    public JoyNoteVideoPlayView setShowBottomProgressBar(boolean showBottomProgressBar) {
        isShowBottomProgressBar = showBottomProgressBar;
        if (!isShowBottomProgressBar) {
            bottomProgressBar.setVisibility(GONE);
        }
        return this;
    }

    /**
     * 是否保持BottomProgressBar常显
     */
    public JoyNoteVideoPlayView setKeepBottomProgressBarVisi(boolean keepVisi) {
        isKeepBottomProgressBarVisi = keepVisi;
        return this;
    }

    /**
     * 设置底部进度条显示（独立使用）
     */
    public JoyNoteVideoPlayView setBottomProgressBarVisible(boolean visible) {
        if (visible) {
            bottomProgressBar.setVisibility(VISIBLE);
        } else {
            bottomProgressBar.setVisibility(GONE);
        }
        return this;
    }

    /**
     * 设置中部声音按钮是否一直显示  调用此方法时中部声音按钮 voiceIcon必须visi 否则没意义
     */
    public JoyNoteVideoPlayView setVoiceIconKeepVisiInFullScreen(boolean keepVisi) {
        this.isVoiceIconKeepVisiInFullScreen = keepVisi;

        return this;
    }


    /**
     * 是否显示底部声音图标 及有声还是无声
     */
    public JoyNoteVideoPlayView setShowBottomVoice(boolean showBottomVoice, boolean voiceOff) {
        isVoiceOff = voiceOff;
        if (showBottomVoice) {
            bottomVoice.setVisibility(VISIBLE);
            bottomVoicePlaceHolder.setVisibility(INVISIBLE);
            bottomBarLayoutCopy.setVisibility(VISIBLE);
            voiceIcon.setVisibility(GONE);
        } else {
            bottomVoice.setVisibility(GONE);
            bottomVoicePlaceHolder.setVisibility(GONE);
            bottomBarLayoutCopy.setVisibility(GONE);
        }


        if (isVoiceOff) {
            bottomVoice.setBackgroundResource(bottomVoiceOffRes);
            voiceIconSmall.setBackgroundResource(R.drawable.video_player_voice_off_small);
        } else {
            bottomVoice.setBackgroundResource(bottomVoiceOnRes);
            voiceIconSmall.setBackgroundResource(R.drawable.video_player_voice_on_small);
            //有声播放时，声音按钮不一直显示
            isVoiceFirstClicked = true;

        }

        voiceIconState = !showBottomVoice ? -1 : 1;

        return this;
    }

    /**
     * 设置底部声音按钮的图片资源
     */
    public JoyNoteVideoPlayView setBottomVioceRes(int onRes, int offRes) {
        if (onRes != 0) {
            bottomVoiceOnRes = onRes;
        }

        if (offRes != 0) {
            bottomVoiceOffRes = offRes;
        }
        return this;

    }

    /**
     * 当使用流量播放视频时  是否弹出toast提醒
     */
    public JoyNoteVideoPlayView setFlowToastAlert(boolean toastAlert) {
        flowToastAlert = toastAlert;
        return this;

    }


    /**
     * 设置播放地址
     * 设置完后会自动播放
     */
    public JoyNoteVideoPlayView setPlaySource(String url) {
        if (videoView != null) {
            videoView.mtaUrl = (url != null ? url : "");
        }
        return setPlaySource(url, 0);
    }

    /**
     * 设置播放地址
     * 设置完后会自动播放
     */
    public JoyNoteVideoPlayView setPlaySource(String url, String videoId) {
        if (videoView != null) {
            videoView.mtaUrl = (url != null ? url : "");
            videoView.mtaVideo_id = (videoId != null ? videoId : "");
        }
        return setPlaySource(url, 0);
    }

    /**
     * 只设置路径 不播放
     */
    public JoyNoteVideoPlayView setPlaySourceWithoutPlay(String url) {
        isAutoPlay = false;
        videoView.getPlayerOptions().setStartOnPrepared(false);
        setPlaySource(url);
        if (isHideCenterPlayer) {
            centerPlayIv.setVisibility(GONE);
        } else {
            centerPlayIv.setVisibility(VISIBLE);
        }
        isAlreadyStartPlay = false;
        return this;
    }

    /**
     * 只设置路径 不播放
     */
    public JoyNoteVideoPlayView setPlaySourceWithoutPlay(String url, boolean isFromComment) {
        this.isFromComment = isFromComment;
        setPlaySourceWithoutPlay(url);
        return this;
    }

    /**
     * 是否是本地视频
     */
    public JoyNoteVideoPlayView setIsLocalVideo(boolean isLocalVideo) {
        this.isLocalVideo = isLocalVideo;
        return this;
    }

    /**
     * 设置播放地址
     * 设置完后会自动播放
     */
    public JoyNoteVideoPlayView setPlaySource(String url, int seekPositon) {
        if (UnLog.D) {
            UnLog.d(TAG, "setPlaySource:" + url + ",isSetSource:" + isSetSource);
        }
        videoPath = url;
        if (isAutoPlay) {
            //检测网络
            if (!isLocalVideo && isNeedJudgeNetOnStart && VideoPlayUtil.isMobileNet(mContext) && !isAlreadyShowNoWifi) {
                isSetSource = false;
                if (!flowToastAlert) {
                    //如果使用流量播放时 暂停视频 ui更改 提醒用户
                    showNetChangelayout();
                    return this;
                } else {
                    //如果使用流量播放视频时  弹出吐司 其他操作不做
                    Toast toast = Toast.makeText(mContext, mContext.getResources().getString(R.string.video_player_no_wifi_toast), Toast.LENGTH_SHORT);
                    toast.setGravity(Gravity.CENTER, 0, 0);
                    toast.show();
                    isAlreadyShowNoWifi = true;
                }

            }
            if (centerPlayIv.getVisibility() == VISIBLE) {
                centerPlayIv.setVisibility(GONE);
            }
            showLoading();
        }
        videoView.setVideoPath(url);
        if (isPlayingTail || isSetSource) {
            releaseInThread();
            videoView.initRenders();
        }
        if (seekPositon > 0) {
            videoView.seekTo(seekPositon);
        }
        status = STATE_PLAYING;
        isSetSource = true;
        return this;
    }

    /**
     * 推荐位重置播放器
     */
    public void resetState() {
        pausePlay();
        if (currentTipState != -1) {
            hideErrorLayout();
        }
    }

    /**
     * 在线程中释放播放器
     */
    public void releaseInThread() {
        //在线程中释放videoView
        try {
            videoView.releaseInThread(true);
        } catch (Exception e) {
            if (UnLog.E) {
                e.printStackTrace();
            }
        }
    }

    private int seekTo = 0;

    /**
     * 跳转到制定点
     */
    public JoyNoteVideoPlayView seekToPosition(int position) {
        if (position >= 0) {
            showLoading();
            isBuffering = true;
            seekTo = position;
            playPostion = position;
            duration = getDuration();
            if (duration > 0) {
                int pos = (int)((playPostion * 1.0 / duration) * MAX_PROGRESS);
                mSeekBar.setProgress(pos);
                currentTimeTv.setText(VideoPlayUtil.generateTime(position));
                setSeekBarBgProgress(pos, false);
                if (isShowBottomProgressBar) {
                    bottomProgressBar.setProgress(pos);
                    setSeekBarBgProgress(pos, true);
                }
            }
            if (!videoView.isPlaying()) {
                startPlay();
            }
            show(true);
            videoView.seekTo(position);
        }
        return this;
    }

    /**
     * 开始播放
     */
    public JoyNoteVideoPlayView startPlay() {
        JMAudioCategoryManager.JMEAudioCategorySet result = JMAudioCategoryManager.getInstance().setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY, () -> {
            //被其他高优先级应用占用声道时的处理
            pausePlay();
        });
        if (!result.available) { //声道占用失败
            return this;
        }
        if (currentTipState != -1) {
            pausePlay();
        } else if (isNeedAccessChangeToWifi) {
            reTry();
            isNeedAccessChangeToWifi = false;
        } else {
            hideLoading();
            coverIv.setVisibility(GONE);

            status = STATE_PLAYING;

            changeVoiceState();
            oldProgress = 0;
            if (isLive) {
                videoView.seekTo(0);
            }
            if (UnLog.D) {
                UnLog.d(TAG, "startPlay showloading");
            }
            if (buffProgress <= thisProgress) {
                showLoading();
            }
            centerPlayIv.setVisibility(GONE);
            replayIcon.setVisibility(GONE);
            videoView.start();

            setScreenOn();
            isAlreadyStartPlay = true;
            if (itemCtrl != null) {
                itemCtrl.start();
            }
            updatePausePlay();
            mHandler.sendEmptyMessage(MESSAGE_SHOW_PROGRESS);
            mHandler.sendEmptyMessage(MESSAGE_HANDLER_SPEED);
        }
        return this;
    }

    /**
     * 暂停播放
     */
    public JoyNoteVideoPlayView pausePlay() {
        if (UnLog.D) {
            UnLog.d(TAG, "pausePlay: " + status + " " + isPlaying());
        }
        //释放声道占用
        JMAudioCategoryManager.getInstance().releaseJoyNoteVideo();
        mHandler.removeMessages(MESSAGE_HANDLER_SPEED);
        //暂停状态 如果有loading圈则隐藏
        hideLoading();
        setScreenOff();
        if (status == STATE_PAUSED) {
            return this;
        }
        status = STATE_PAUSED;
        getCurrentPosition();
        videoView.pause();
        if (isResumeBgMusicOnPause && currentTipState == -1) {
            resumeOtherVoice();
        }
        //异常情况下 不显示中间播放按钮
        if (isHideCenterPlayer || currentTipState != -1) {
            centerPlayIv.setVisibility(GONE);
        } else {
            centerPlayIv.setVisibility(VISIBLE);
        }
        updatePausePlay();
        return this;
    }

    /**
     * 停止播放
     */
    public JoyNoteVideoPlayView stopPlay() {
        status = STATE_STOP;
        if (itemCtrl != null) {
            itemCtrl.stop();
        }
        videoView.releaseInThread(true);
        setScreenOff();
        if (mHandler != null) {
            mHandler.removeMessages(MESSAGE_SHOW_PROGRESS);
            mHandler.removeMessages(MESSAGE_HANDLER_SPEED);
        }
        return this;
    }

    /**
     * 获取当前播放位置
     */
    public int getCurrentPosition() {
        if (!isLive) {
            currentPosition = videoView.getCurrentPosition();
        } else {
            //直播
            currentPosition = -1;
        }
        return currentPosition;
    }

    /**
     * 获得缓存进度 提供外部使用
     */
    public int getBufferPercentage() {
        if (videoView != null) {
            return videoView.getBufferPercentage();
        }
        return -1;
    }

    /**
     * 获取视频播放总时长
     */
    public int getNormalVideoDuration() {
        return normalVideoDuration;
    }

    /**
     * 获取视频播放总时长 当前视频
     */
    public int getDuration() {
        if (duration <= 0) {
            duration = videoView.getDuration();
            if (!isPlayingTail) {
                normalVideoDuration = duration;
            }
        }
        return duration;
    }

    /**
     * 当前播放的是否是直播
     */
    public JoyNoteVideoPlayView setLive(boolean live) {
        isLive = live;

        if (isLive) {
            if (videoView != null && videoView.getPlayerOptions() != null) {
                videoView.updateOptionsWithoutChangeView(true);
            }
            hide();//直播 先不显示头部和底部，prepared 显示
            centerPlayIv.setVisibility(GONE);
            playIvOnBottomBar.setVisibility(GONE);
            endTimeTv.setVisibility(GONE);
            mSeekBar.setVisibility(GONE);
            bottomBarLayout.setBackgroundResource(R.drawable.uni_video_live_bottom_bg);
            //直播不提示流量
            isAlreadyShowNoWifi = true;

        }
        setOptions(isLive);
        return this;
    }

    /**
     * 当前播放的是否是直播
     */
    public JoyNoteVideoPlayView setHideRetryBt(boolean isHide) {
        isHideRetryBt = isHide;
        return this;
    }

    /**
     * 是否自动播放
     */
    public JoyNoteVideoPlayView setAutoPlay(boolean isAutoPlay) {
        this.isAutoPlay = isAutoPlay;
        if (!isAutoPlay && videoView.isPlaying()) {
            pausePlay();
        }
        return this;
    }

    /**
     * 设置直播标识
     */
    public JoyNoteVideoPlayView setLiveIcon(Bitmap bitmap) {
        if (liveIconIv != null && bitmap != null) {
            liveIconIv.setImageBitmap(bitmap);
        }
        return this;
    }

    /**
     * 隐藏所有状态界面
     */
    public JoyNoteVideoPlayView hideAllUI() {
        isHideControlPanl = true;
        // 是否隐藏topBar，true为隐藏，false为不隐藏
        boolean isHideTopBar = true;
        isHideBottomBar = true;
        isHideCenterPlayer = true;
        isHideRetryBt = true;
        smallTopBarLayout.setVisibility(View.GONE);
        bottomProgressBarSmall.setVisibility(View.GONE);
        bottomBarLayout.setVisibility(View.GONE);
        bottomBarLayoutCopy.setVisibility(View.GONE);
        centerPlayIv.setVisibility(View.GONE);
        replayIcon.setVisibility(View.GONE);
        return this;
    }

    /**
     * 是否显示中部声音图标及有声还是无声
     */
    public JoyNoteVideoPlayView setShowVoice(boolean showVoiceIcon, boolean voiceOff) {
        voiceIconState = showVoiceIcon ? -1 : 0;
        isVoiceOff = voiceOff;
//        changeVoiceState();
        if (showVoiceIcon) {
            voiceIcon.setVisibility(VISIBLE);
            bottomVoice.setVisibility(GONE);
            bottomVoicePlaceHolder.setVisibility(GONE);
            bottomBarLayoutCopy.setVisibility(GONE);
        } else {
            voiceIcon.setVisibility(GONE);
        }
        if (isVoiceOff) {
            voiceIconSmall.setBackgroundResource(R.drawable.video_player_voice_off_small);
            voiceIcon.setBackgroundResource(R.drawable.video_player_voice_off);
        } else {
            voiceIconSmall.setBackgroundResource(R.drawable.video_player_voice_on_small);
            voiceIcon.setBackgroundResource(R.drawable.video_player_voice_on);
            //有声播放时，声音按钮不一直显示
            isVoiceFirstClicked = true;
        }
        return this;
    }


    /**
     * 改变声音状态
     */
    private void changeVoiceState() {
        if (UnLog.D) {
            UnLog.d(TAG, "changeVoiceState:" + isVoiceOff);
        }
        if (isVoiceOff) {
            //关闭播放器声音  打开背景音乐
            setVoiceState(false);
            if (status == STATE_PLAYING) {
                resumeOtherVoice();
            }
        } else {
            //需要检查是否可以获取声道占用
            int curAudioCategory = JMAudioCategoryManager.getInstance().getCurrentAudioCategory();
            //如果当前已经占用声道或者可以占用声道
            if (curAudioCategory == JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY ||
            JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE_VIDEO_PLAY)) {
                //打开播放器声音  关闭背景音乐
                setVoiceState(true);
                if (status == STATE_PLAYING) {
                    pauseOtherVoice();
                }
                if (UnLog.D) {
                    UnLog.d(TAG, "changeVoiceState: pauseOtherVoice");
                }
            }
        }

        if (isVoiceOff) {
            voiceIconSmall.setBackgroundResource(R.drawable.video_player_voice_off_small);
            voiceIcon.setBackgroundResource(R.drawable.video_player_voice_off);
            bottomVoice.setBackgroundResource(bottomVoiceOffRes);
        } else {
            voiceIconSmall.setBackgroundResource(R.drawable.video_player_voice_on_small);
            voiceIcon.setBackgroundResource(R.drawable.video_player_voice_on);
            bottomVoice.setBackgroundResource(bottomVoiceOnRes);
        }
    }

    /**
     * 获取底部控制barview
     */
    public View getBottomBarView() {
        return bottomBarLayout;
    }

    /**
     * 获取底部bar的播放view
     */
    public ImageView getBarPlayerView() {
        return playIvOnBottomBar;
    }

    /**
     * 获取中间的播放view
     */
    public ImageView getCenterPlayerView() {
        return centerPlayIv;
    }

    public void setiViewPlayerControl(IViewPlayerControl control) {
        //关闭按钮接口
    }

    public void setCtrlViewListener(IVideoPlayerCtrlViewListener listener) {
        ctrlViewListener = listener;
    }

    /**
     * 隐藏分享键，true隐藏，false为显示
     */
    public JoyNoteVideoPlayView setBottomSharedEnable(boolean enable) {
        bottomSharedEnable = enable;
        bottomShareIcon.setVisibility(enable ? View.VISIBLE : View.GONE);
        return this;
    }

    /**
     * 改变分享按钮的enable状态
     */
    public JoyNoteVideoPlayView setBottomSharedState(boolean enable) {
        bottomSharedEnable = enable;
        return this;
    }

    /**
     * 隐藏全屏按钮，true隐藏，false为显示
     */
    public JoyNoteVideoPlayView hideFullscreen(boolean isHide) {
        if (fullscreenIv != null) {
            fullscreenIv.setVisibility(isHide ? View.GONE : View.VISIBLE);
        }
        return this;
    }

    /**
     * 加载错误或者超时未播放的情况下是否显示重试按钮
     */
    public JoyNoteVideoPlayView loadErrorRetry(boolean retry) {
        this.loadErrorRetry = retry;
        return this;
    }

    public JoyNoteVideoPlayView isShowErrorLayout(boolean isShowErrorLayout) {
        this.isShowErrorLayout = isShowErrorLayout;
        return this;
    }

    /**
     * 隐藏中间播放按钮,ture为隐藏，false为不做隐藏处理，但不是显示
     * 直播不显示此按钮
     */
    public JoyNoteVideoPlayView hideCenterPlayer(boolean isHide) {
        isHideCenterPlayer = isHide;
        return this;
    }

    /**
     * 是否隐藏bottonbar，true为隐藏，false为不隐藏，但不一定是显示
     */
    public JoyNoteVideoPlayView hideBottomBar(boolean isHide) {
        isHideBottomBar = isHide;
        bottomBarLayout.setVisibility(isHideBottomBar ? View.GONE : View.VISIBLE);
        bottomBarLayoutCopy.setVisibility(isHideBottomBar ? View.GONE : View.VISIBLE);
        return this;
    }

    /**
     * 是否隐藏上下bar，true为隐藏，false为不隐藏，但不一定是显示
     */
    public JoyNoteVideoPlayView hideControlPanl(boolean isHide) {
        isHideControlPanl = isHide;
        hideBottomBar(isHide);
        return this;
    }

    /**
     * 是否自动切换横竖屏
     */
    public JoyNoteVideoPlayView isAutoChangeScreen(boolean isAuto) {
        if (isAuto) {
            if (orientationEventListener != null && orientationEventListener.canDetectOrientation()) {
                orientationEventListener.enable();
            }
        } else {
            if (orientationEventListener != null && orientationEventListener.canDetectOrientation()) {
                orientationEventListener.disable();
            }
        }
        return this;
    }

    /**
     * 处理自动旋转角度
     */
    private void optOrientation(int orientation) {
        if (oldOrientation == orientation) {
            return;
        }
        int screenOrientation = getActivity().getRequestedOrientation();
        if (UnLog.D) {
            UnLog.d("h-v", orientation + "---orientation   screenOrientation--" + screenOrientation);
        }
        oldOrientation = orientation;
        if (0 == orientation) {
            //0 切换到竖屏
            if (screenOrientation != ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
                toggleFullScreen(0);
            }
        } else if (90 == orientation) {
            if (screenOrientation != ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE) {
                //90 切换到反向横屏
                toggleFullScreen(2);
            }
        } else if (180 == orientation) {
            if (screenOrientation != ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT) {
                //180 切换到反向竖屏
                toggleFullScreen(3);
            }
        } else {
            if (screenOrientation != ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
                //270 切换到横屏
                toggleFullScreen(1);
            }
        }
    }


    /**
     * 全屏切换
     *
     * @param changeState -1:自动切换 0：切换到竖屏 1：切换到横屏 2:切换到反向横屏 3:切换到反向竖屏
     */
    @SuppressLint("SourceLockedOrientationActivity")
    void toggleFullScreen(int changeState) {
        int screenOrientation = getActivity().getRequestedOrientation();
        if ((changeState == -1 && (screenOrientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE || screenOrientation == ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE)) || changeState == 0 || changeState == 3) {
            if (changeState == 3) {
                getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
            } else {
                getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
            isLandScape = false;

            //竖屏  非全屏
            mHandler.postDelayed(hideRunnable, 300);
        } else if ((changeState == -1 && (screenOrientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT || screenOrientation == ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT)) || changeState == 1 || changeState == 2) {
            if (changeState == 2) {
                getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE);
            } else {
                getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            }
            isLandScape = true;

            //横屏 全屏
            mHandler.postDelayed(showRunnable, 300);
        }
        if (mtaListener != null) {
            mtaListener.clickScreen(isLandScape);
        }
        videoTopParams();
        initUIlayout(isLandScape);
    }

    /**
     * 设置是否横屏显示
     */
    public void setScreenState(boolean isLand) {
        initUIlayout(isLand);
        if (isLand) {
            toggleFullScreen(1);
        } else {
            toggleFullScreen(0);
        }
    }

    /**
     * 设置全屏
     */
    public void setFullScreen() {
        if (isLandScape) {
            mHandler.postDelayed(showRunnable, 300);
        }
    }

    Runnable showRunnable = new Runnable() {
        @Override
        public void run() {
            VideoPlayerUtils.setActivityFullScreen(videoView);

        }
    };
    Runnable hideRunnable = new Runnable() {
        @Override
        public void run() {
            VideoPlayerUtils.setActivityNotFullScreen(videoView);

        }
    };


    public JoyNoteVideoPlayView setOnPlayerStateListener(AVideoPlayStateListener onPlayerStateListener) {
        this.onPlayerStateListener = onPlayerStateListener;
        return this;
    }

    public boolean isPlaying() {
        return status == STATE_PLAYING;
    }

    /**
     * 同步进度
     */
    public void syncProgress() {
        if (isBuffering || videoView == null) {
            return;
        }
        if (videoView.getCurrentPosition() > seekTo) {
            seekTo = 0;
        } else {
            return;
        }
        //视频处于异常情况时，即切换到非wifi时，有时视频并没有暂停，再暂停一次
        if (currentTipState != -1) {
            pausePlay();
        }
        if (!isVoiceOff && status == STATE_PLAYING && pauseCount < 8) {
            pauseOtherVoice();
        } else if (isVoiceOff && status == STATE_PLAYING && resumeCount < 8) {
            resumeOtherVoice();
        }
        int position = videoView.getCurrentPosition();
        int duration = getDuration();
        if (mSeekBar != null) {
            if (duration > 0) {
                //MAX_PROGRESS * position 视频很长时，会int越界
                int pos = (int) ((float) MAX_PROGRESS * position / duration);
                mSeekBar.setProgress(pos);
                nomalSeekBarProgrssBg.setDuration(duration);
                setSeekBarBgProgress(pos, false);
                setSelectPoint(position, true);
                if (pos == 0) {
                    mSeekBar.invalidate();
                }
                if (progressChangeListener != null) {
                    progressChangeListener.onProgressChange(pos, MAX_PROGRESS);
                }
                if (isShowBottomProgressBar) {
                    bottomProgressBar.setProgress(pos);
                    bottomSeekBarProgrssBg.setDuration(duration);
                    setSeekBarBgProgress(pos, true);
                    setSelectPoint(position, true);
                    if (pos == 0) {
                        bottomProgressBar.invalidate();
                    }
                }
                if (screenState == SCREEN_SMALL) {
                    bottomProgressBarSmall.setProgress(pos);
                }
                thisProgress = position;
                if (position != 0 && status != STATE_COMPLETED) {
                    playPostion = position;
                }
            }
            int percent = videoView.getBufferPercentage();
            buffProgress = percent * 10;
            mSeekBar.setSecondaryProgress(buffProgress);
            nomalSeekBarSecondProgressBg.setProgress(buffProgress);
            if (isShowBottomProgressBar) {
                bottomProgressBar.setSecondaryProgress(buffProgress);
                bottomSeekBarSecondProgressBg.setProgress(buffProgress);
            }
            if (screenState == SCREEN_SMALL) {
                bottomProgressBarSmall.setSecondaryProgress(buffProgress);
            }
        }
        if (itemCtrl != null) {
            itemCtrl.setProgress(duration, playPostion);
        }
        progressState();
        currentTimeTv.setText(VideoPlayUtil.generateTime(position));
        endTimeTv.setText(VideoPlayUtil.generateTime(duration));
    }

    private void progressState() {
        //直播oldProgress和thisProgress都是0
        if (isLive) {
            return;
        }
        if (videoView.isPlaying() && buffProgress != 1090) {
            if (oldProgress == thisProgress && !isLoading) {
                if (!isLocalVideo && !NetUtils.isNetworkAvailable(mContext)) {
                    showNetErrorlayout();
                } else {
                    if (UnLog.D) {
                        UnLog.d(TAG, "ProgressState showloading");
                    }
                    showLoading();
                }
            } else if (oldProgress != thisProgress && isLoading) {
                if (UnLog.D) {
                    UnLog.d(TAG, "ProgressState hideLoading");
                }
                hideLoading();
            }
        }
        oldProgress = thisProgress;
    }

    /**
     * 更新播放、暂停和停止按钮
     */
    private void updatePausePlay() {
        if (status == STATE_PLAYING) {
            if (audioSkin) {
                playIvOnBottomBar.setImageResource(R.drawable.vd_pause_video_hor);
            } else {
                if (isLandScape) {
                    playIvOnBottomBar.setImageResource(R.drawable.vd_pause_video_hor);
                } else {
                    playIvOnBottomBar.setImageResource(R.drawable.vd_pause_video);
                }
                playIvOnBottomBar.setContentDescription(getResources().getString(R.string.un_video_screen_pause));
            }
        } else {
            if (audioSkin) {
                playIvOnBottomBar.setImageResource(R.drawable.joy_note_play_btn);
            } else {
                if (isLandScape) {
                    playIvOnBottomBar.setImageResource(R.drawable.vd_play_video_hor);
                } else {
                    playIvOnBottomBar.setImageResource(R.drawable.vd_play_video);
                }
                playIvOnBottomBar.setContentDescription(getResources().getString(R.string.un_video_screen_start));
            }
        }
        if (isLandScape) {
            if (closeBtn != null) {
                closeBtn.setVisibility(VISIBLE);
            }
            if (mLiveNameTxt != null) {
                mLiveNameTxt.setVisibility(VISIBLE);
            }
        } else {
            if (closeBtn != null) {
                closeBtn.setVisibility(GONE);
            }
            if (mLiveNameTxt != null) {
                mLiveNameTxt.setVisibility(GONE);
            }
        }
    }

    private Activity getActivity() {
        return (Activity) mContext;
    }

    /**
     * 显示网络连接问题布局
     */
    private void showNetErrorlayout() {

        if (UnLog.D) {
            UnLog.d(TAG, "showNetErrorlayout hideLoading");
        }
        hideLoading();
        if (!isShowErrorLayout) {
            return;
        }
        if (screenState == SCREEN_SMALL || screenState == SCREEN_NO_UI || screenState == SCREEN_ITEM) {
            errorLayoutSmall.setVisibility(VISIBLE);
            retryTvSmall.setBackgroundResource(R.drawable.video_player_retry_small);
            retryTvSmall.setEnabled(true);
            errorTipTvSmall.setText(mContext.getResources().getString(R.string.video_player_net_error_small));
        } else {
            mNetErrorLayout.setVisibility(View.VISIBLE);
            errorTipTv.setText(mContext.getResources().getString(R.string.video_player_net_error));
            loadErrorIv.setVisibility(GONE);
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.video_player_fresh_icon);
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            }
            mClickToRetry.setCompoundDrawables(drawable, null, null, null);
            mClickToRetry.setText(mContext.getResources().getString(R.string.video_player_net_error_small));
            mClickToRetry.setVisibility(VISIBLE);
        }
        currentTipState = STATE_NET_ERROR;
        pausePlay();
        optEnable(false);
    }


    private void showLoadErrorLayout(boolean isNeedMta) {
        if (isDestory || mContext == null) {
            return;
        }
        if (UnLog.D) {
            UnLog.d(TAG, "showErrorDiaLog hideLoading");
        }
        hideLoading();
        if (!isShowErrorLayout) {
            return;
        }
        centerPlayIv.setVisibility(GONE);
        if (screenState == SCREEN_SMALL || screenState == SCREEN_NO_UI || screenState == SCREEN_ITEM) {
            errorLayoutSmall.setVisibility(VISIBLE);
            retryTvSmall.setBackgroundResource(loadErrorRetry ? R.drawable.video_player_retry_small : R.drawable.video_player_error_icon_small);
            retryTvSmall.setEnabled(loadErrorRetry);
            errorTipTvSmall.setText(mContext.getResources().getString(R.string.video_player_load_error_small));
        } else {
            mNetErrorLayout.setVisibility(View.VISIBLE);
            errorTipTv.setText(mContext.getResources().getString(R.string.video_player_load_error));
            loadErrorIv.setVisibility(loadErrorRetry ? GONE : VISIBLE);
            mClickToRetry.setVisibility(loadErrorRetry ? VISIBLE : GONE);
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.video_player_fresh_icon);
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            }
            mClickToRetry.setCompoundDrawables(drawable, null, null, null);
        }
        currentTipState = STATE_LOAD_ERROR;
        pausePlay();
        optEnable(false);
    }


    /**
     * 显示流量提醒
     */
    private void showNetChangelayout() {
        if (UnLog.D) {
            UnLog.d(TAG, "showNetChangelayout hideLoading");
        }
        hideLoading();
        if (screenState == SCREEN_SMALL || screenState == SCREEN_NO_UI) {
            errorLayoutSmall.setVisibility(VISIBLE);
            retryTvSmall.setBackgroundResource(R.drawable.video_player_play_icon_small);
            retryTvSmall.setEnabled(true);
            errorTipTvSmall.setText(mContext.getResources().getString(R.string.video_player_no_wifi_small));
        } else {
            mNetErrorLayout.setVisibility(View.VISIBLE);
            errorTipTv.setText(mContext.getResources().getString(R.string.video_player_no_wifi));
            loadErrorIv.setVisibility(GONE);
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.video_player_play_icon);
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            }
            mClickToRetry.setCompoundDrawables(drawable, null, null, null);
            mClickToRetry.setText(mContext.getResources().getString(R.string.video_player_continue_play));
            mClickToRetry.setVisibility(VISIBLE);
        }
        currentTipState = STATE_NO_WIFI_TIP;
        pausePlay();
        isAlreadyShowNoWifi = true;
        optEnable(false);
    }


    private void hideErrorLayout() {
        mNetErrorLayout.setVisibility(View.GONE);
        errorLayoutSmall.setVisibility(GONE);
        currentTipState = -1;
        optEnable(true);
    }


    @Override
    public boolean onTouch(View v, MotionEvent event) {
        int id = v.getId();
        if (id == R.id.app_video_top_box) {
            return true;
        } else if (id == R.id.ll_bottom_bar || id == R.id.topFunctionLayer) {
            return true;
        } else {
            if (videoViewOnTouchListener != null) {
                return videoViewOnTouchListener.onTouch(v, event);
            }
            //播放片尾 不需要执行点击及双击屏幕操作
            if (isPlayingTail) {
                return true;
            }
            //直播 双击和点击一样
            if (isLive) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    if (bottomBarLayout.getVisibility() == View.VISIBLE) {
                        mHandler.removeMessages(HIDE_WHAT);
                        hide();
                    } else {
                        show(true);
                    }
                }
            } else {
                if (gestureDetector == null) {
                    initGestureDetector();
                    gestureDetector.setIsLongpressEnabled(false);
                }
                if (gestureDetector.onTouchEvent(event)) {
                    return true;
                }

                if (MotionEvent.ACTION_UP == event.getAction()) {
                    endGesture();
                    if (hasFF_REW) {
                        hasFF_REW = false;
                    }
//                    seekToPosition(downPlayPosition - 500);
                    return true;
                }


            }
            return true;
        }
    }

    // 手势结束
    private void endGesture() {
        volumeSumDelta = 0;
        mHandler.removeMessages(MESSAGE_HIDE_CENTER_TIP);
        mHandler.sendEmptyMessageDelayed(MESSAGE_HIDE_CENTER_TIP, 500);
    }

    private void showCenterTip(boolean isVolume, String tipText) {

        if (isVolume) {
            showCenterTip(tipText, R.drawable.player_volume_up, false);
        } else {
            showCenterTip(tipText, R.drawable.player_brightness, false);
        }
    }

    private void showCenterTip(String tipText, int imageResource, boolean isTimer) {
        mCenterChangeTip.setVisibility(View.VISIBLE);
        if (imageResource > 0) {
            centerTipImage.setVisibility(VISIBLE);
            centerTipImage.setImageResource(imageResource);
        } else {
            centerTipImage.setVisibility(GONE);
        }
        centerTipTextTimer.setVisibility(GONE);
        centerTipText.setVisibility(GONE);


        if (isTimer) {
            centerTipTextTimer.setText(tipText);
            centerTipTextTimer.setVisibility(VISIBLE);
        } else {
            centerTipText.setText(tipText);
            centerTipText.setVisibility(VISIBLE);
        }
    }

    int downPlayPosition;


    /**
     * 处理双击和点击
     */
    private void initGestureDetector() {
        gestureDetector = new GestureDetector(mContext, new GestureDetector.SimpleOnGestureListener() {

            // 按下
            @Override
            public boolean onDown(MotionEvent e) {
                hasFF_REW = false;
                //每次按下都重置为NONE
                mScrollMode = NONE;
                downPlayPosition = getCurrentPosition();

                //每次按下的时候更新当前亮度和音量，还有进度
                oldProgressLater = newProgress;
                oldVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
                brightness = mLayoutParams.screenBrightness;
                if (brightness == -1) {
                    //一开始是默认亮度的时候，获取系统亮度，计算比例值
                    brightness = mBrightnessHelper.getBrightness() / 255f;
                }

                return true;
            }

            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                switch (mScrollMode) {
                    case NONE:
                        //offset是让快进快退不要那么敏感的值
                        if (Math.abs(distanceX) - Math.abs(distanceY) > offsetX) {
                            mScrollMode = FF_REW;
                        } else {
                            if (e1.getX() < getWidth() / 2f) {
                                mScrollMode = BRIGHTNESS;
                            } else {
                                mScrollMode = VOLUME;
                            }
                        }
                        break;
                    case VOLUME:
                        int value = (int) ((videoView.getHeight() * 0.4) / maxVolume);
                        int newVolume = (int) ((e1.getY() - e2.getY()) / value + oldVolume);

                        mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, newVolume, 0);
                        //要强行转Float类型才能算出小数点，不然结果一直为0
                        int volumeProgress = (int) (newVolume / (float) maxVolume * 100);
                        if (volumeProgress < 0) {
                            volumeProgress = 0;
                        }
                        if (volumeProgress > 100) {
                            volumeProgress = 100;
                        }
                        showCenterTip(true, volumeProgress + "%");
                        break;
                    case BRIGHTNESS:

                        //下面这是设置当前APP亮度的方法
                        float newBrightness = (float) ((e1.getY() - e2.getY()) / videoView.getHeight() * 2);
                        newBrightness += brightness;
                        if (newBrightness < 0) {
                            newBrightness = 0;
                        } else if (newBrightness > 1) {
                            newBrightness = 1;
                        }
                        mLayoutParams.screenBrightness = newBrightness;
                        mWindow.setAttributes(mLayoutParams);
                        int i = (int) (newBrightness * 100);
                        if (i < 0) {
                            i = 0;
                        }
                        if (i > 100) {
                            i = 100;
                        }
                        showCenterTip(false, i + "%");
                        break;
                    case FF_REW:

                        if (videoView != null && videoView.isPlaying()) {

                            float offset = e2.getX() - e1.getX();
                            //根据移动的正负决定快进还是快退
                            int resourceId;
                            if (offset > 0) {
                                downPlayPosition += 3000;
                                resourceId = R.drawable.ff;
                                scl.setImageResource(R.drawable.ff);
                                newProgress = (int) (oldProgressLater + offset / videoView.getWidth() * 100);
                                if (newProgress > 100) {
                                    newProgress = 100;
                                }
                            } else {
                                downPlayPosition -= 3000;
                                scl.setImageResource(R.drawable.fr);
                                resourceId = R.drawable.fr;
                                newProgress = (int) (oldProgressLater + offset / videoView.getWidth() * 100);
                                if (newProgress < 0) {
                                    newProgress = 0;
                                }
                            }
                            scl.setProgress(newProgress);
//                        scl.show();
                            if (downPlayPosition <= 0) {
                                downPlayPosition = 0;
                            } else if (downPlayPosition > getDuration()) {
                                downPlayPosition = getDuration();
                            }
                            mHandler.removeMessages(MESSAGE_SEEK_TO_POSITION);
                            mHandler.sendEmptyMessageDelayed(MESSAGE_SEEK_TO_POSITION, 500);
                            showCenterTip("" + VideoPlayUtil.generateTime(downPlayPosition) + " / " + endTimeTv.getText().toString() + "", resourceId, true);
                            hasFF_REW = true;
                            if (seekBarChangeListener != null && videoView != null) {
                                seekBarChangeListener.onProgressChange(downPlayPosition, videoView.getDuration());
                            }
                        }
                        break;
                }
                return true;
            }

            /**
             * 发生确定的单击时执行
             */
            @Override
            public boolean onSingleTapConfirmed(MotionEvent event) {//单击事件
                if (UnLog.D) {
                    UnLog.d(TAG, "onSingleTapConfirmed");
                }
                if (screenState == SCREEN_NO_UI) {
                    return true;
                }
                if (bottomBarLayout.getVisibility() == View.VISIBLE) {
                    hide();
                } else {
                    show(true);
                }
                return true;
            }

            /**
             * 双击发生时的通知
             */
            @Override
            public boolean onDoubleTap(MotionEvent e) {//双击事件
                if (UnLog.D) {
                    UnLog.d(TAG, "onDoubleTap");
                }
                if (screenState == SCREEN_NO_UI) {
                    return true;
                }
                if (status == STATE_PLAYING) {
                    pausePlay();
                    if (mtaListener != null) {
                        mtaListener.doubleClick(false);
                    }
                } else if ((status == STATE_PAUSED || status == STATE_COMPLETED) && currentTipState == -1) {
                    startPlay();
                    if (mtaListener != null) {
                        mtaListener.doubleClick(true);
                    }
                }
                return true;
            }
        });
    }

    float volumeSumDelta = 0;

    // 滑动改变声音大小
    private void onVolumeSlide(float percent) {
        int volume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        if (volume < 0)
            volume = 0;

        int index = (int) (percent * mMaxVolume) + volume;
        if (index > mMaxVolume)
            index = mMaxVolume;
        else if (index < 0)
            index = 0;

        // 变更声音
        mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, index, 0);

//        if (percent < 0) {
//            mAudioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC,AudioManager.ADJUST_LOWER,
//                                                0);
//        } else {
//            mAudioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC,AudioManager.ADJUST_RAISE,
//                    0);
//        }


        // 变更进度条
        int i = (int) (index * 1.0 / mMaxVolume * 100);
        String tipText = i + "%";
        if (i == 0) {
            tipText = "off";
        }
        // 显示
        showCenterTip(true, tipText);
    }

    // 滑动改变亮度
    private void onBrightnessSlide(float percent) {
        float brightness = getActivity().getWindow().getAttributes().screenBrightness;
        if (brightness <= 0.00f) {
            brightness = 0.50f;
        } else if (brightness < 0.01f) {
            brightness = 0.01f;
        }

        //Log.d(this.getClass().getSimpleName(), "brightness:" + brightness + ",percent:" + percent);

        WindowManager.LayoutParams lpa = getActivity().getWindow().getAttributes();
        lpa.screenBrightness = brightness + percent;
        if (lpa.screenBrightness > 1.0f) {
            lpa.screenBrightness = 1.0f;
        } else if (lpa.screenBrightness < 0.01f) {
            lpa.screenBrightness = 0.01f;
        }
        //query.id(ResourceUtils.getResourceIdByName(mContext, "id", "app_video_brightness")).text(((int) (lpa.screenBrightness * 100)) + "%");
        showCenterTip(false, ((int) (lpa.screenBrightness * 100)) + "%");
        getActivity().getWindow().setAttributes(lpa);
    }

    @Override
    public void onCreatePlayer() {
        if (onPlayerStateListener != null) {
            onPlayerStateListener.onCreatePlayer();
        }
        if (UnLog.D) {
            UnLog.d(TAG, "onCreatePlayer");
        }
    }

    /**
     * 添加一个view，对齐到视频的右下角
     */
    public void addViewAlignVideoRightEnd(View view, RelativeLayout.LayoutParams params) {
        if (view == null) {
            return;
        }
        if (params == null) {
            params = (RelativeLayout.LayoutParams) view.getLayoutParams();
        }
        if (params == null) {
            params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        }
        RelativeLayout.LayoutParams videoParams = (RelativeLayout.LayoutParams) videoView.getLayoutParams();
        videoParams.height = RelativeLayout.LayoutParams.WRAP_CONTENT;
        videoParams.width = RelativeLayout.LayoutParams.WRAP_CONTENT;
        params.addRule(RelativeLayout.ALIGN_END, R.id.video_view);
        params.addRule(RelativeLayout.ALIGN_BOTTOM, R.id.video_view);
        rootLayout.addView(view, params);
    }

    @Override
    public void onPrepared(long l) {
        if (UnLog.D) {
            UnLog.d(TAG, "onPrepared");
        }
        if (isDestory) {
            return;
        }
        int seekToPosition = 0;
        if (mSeekBar.getProgress() > 0 && isFromComment) {
            seekToPosition = (mSeekBar.getProgress() * getDuration()) / MAX_PROGRESS;
        }
        if (getDuration() > 0) {
            if (seekToPosition >= 0) {
                currentTimeTv.setText(VideoPlayUtil.generateTime(seekToPosition));
            }
            endTimeTv.setText(VideoPlayUtil.generateTime(getDuration()));
        }
        if (seekToPosition > 0) {
            seekToPosition(seekToPosition);
        }
        optEnable(true);
        if (onPlayerStateListener != null) {
            onPlayerStateListener.onPrepared(l);
        }
        if (UnLog.D) {
            UnLog.d(TAG, "statusChange PLAYING hideLoading");
        }
        status = STATE_PREPARED;
        if (isLive) {
            show(true);
        }
        mHandler.removeMessages(HIDE_WHAT);
        if (!isPlayingTail) {
            mHandler.sendMessageDelayed(mHandler.obtainMessage(HIDE_WHAT), SHOW_TIME_MAX);
        }
        IjkMediaPlayer ijkMediaPlayer = videoView.getIjkMediaPlayer();
        if (ijkMediaPlayer != null) {
            ijkMediaPlayer.setLooping(isLoopPlay);
        }
        videoTopParams();

    }

    Runnable delaySetTimer = new Runnable() {
        @Override
        public void run() {
            //补齐时间戳
            currentTimeTv.setText(endTimeTv.getText().toString());
            mSeekBar.setProgress(MAX_PROGRESS);
            setSeekBarBgProgress(MAX_PROGRESS, false);
        }
    };

    @Override
    public void onCompletion() {
        setScreenOff();
        JMAudioCategoryManager.getInstance().releaseJoyNoteVideo();
        if (!isLive) {
            if (UnLog.D) {
                UnLog.d(TAG, "onCompletion");
            }

            playPostion = getDuration();
            status = STATE_COMPLETED;
            mHandler.removeMessages(MESSAGE_HANDLER_SPEED);
            //如果有网，有片尾视频，播放片尾视频
            if (!isPlayingTail && NetUtils.isNetworkAvailable(mContext) && !TextUtils.isEmpty(tailSource)) {
                playVideoTail();
                return;
            }
            if (onPlayerStateListener != null) {
                onPlayerStateListener.onCompletion();
                if (onPlayerStateListener.onCustomCompletion()) {
                    return;
                }
            }
            currentPosition = 0;
//            videoView.seekTo(0);
            show(false);
            hideLoading();
            if (UnLog.D) {
                UnLog.d(TAG, "isHideCenterPlayer:" + isHideCenterPlayer);
            }
            if (isHideCenterPlayer) {
                centerPlayIv.setVisibility(GONE);
                if (isHideRetryBt) {
                    replayIcon.setVisibility(GONE);
                } else {
                    replayIcon.setVisibility(VISIBLE);
                }
            } else {
                centerPlayIv.setVisibility(VISIBLE);
                replayIcon.setVisibility(GONE);
            }
            if (itemCtrl != null) {
                itemCtrl.setProgress(duration, 0);
            }
            updatePausePlay();

            mHandler.postDelayed(delaySetTimer, 100);
        }
    }

    /**
     * 继续播放视频
     */
    private void playVideoTail() {
        playPostion = 0;
        duration = 0;

        //设置片尾视频封面图
        if (!TextUtils.isEmpty(tailCoverUrl)) {
            setCoverUrl(tailCoverUrl);
        }
        isPlayingTail = true;
        setPlaySource(tailSource);
        setVideoTailUi();
        if (mtaListener != null) {
            mtaListener.changeToVideoTail();
        }
    }


    /**
     * 设置播放非主视频时，ui的显示
     */
    private void setVideoTailUi() {
        //播放片尾视频时的UI
        if (!isPlayingTail) {
            return;
        }
        //不显示分段视频标签
        if (mPointView != null) {
            mPointView.setVisibility(GONE);
        }
        if (screenState == SCREEN_NORMAL || screenState == SCREEN_BIG) {
            //标题栏一直都在，底部栏不在，
            hideBottomBar(true);
            mHandler.removeMessages(HIDE_WHAT);
            //声音及分享按钮隐藏
            voiceIcon.setVisibility(GONE);
            bottomVoice.setVisibility(GONE);
            bottomShareIcon.setVisibility(GONE);
            //底部进度条隐藏
            bottomProgressBar.setVisibility(GONE);
        } else if (screenState == SCREEN_SMALL) {
            //声音及底部进度条隐藏
            voiceIconSmall.setVisibility(GONE);
            bottomProgressBarSmall.setVisibility(GONE);
        }
    }

    @Override
    public boolean onError(int frameworkErr, int implErr) {
        if (UnLog.D) {
            UnLog.d(TAG, "onError frameworkErr:" + frameworkErr + ",implErr:" + implErr);
        }
        status = STATE_ERROR;
        boolean error = false;
        if (onPlayerStateListener != null) {
            error = onPlayerStateListener.onError(frameworkErr, implErr);
        }
        if (error) {
            return true;
        }
        if (!isLocalVideo && !NetUtils.isNetworkAvailable(mContext)) {
            showNetErrorlayout();
        } else {
            if (!isLive && frameworkErr == -10000) {
                reTry();
            } else {
                showLoadErrorLayout(true);
            }
        }
        return true;
    }

    @Override
    public boolean onInfo(int mediaInfo, int degree) {
        if (UnLog.D) {
            UnLog.d(TAG, "onInfo mediaInfo:" + mediaInfo);
        }
        switch (mediaInfo) {
            case IMediaPlayer.MEDIA_INFO_VIDEO_SEEK_RENDERING_START:
            case IMediaPlayer.MEDIA_INFO_AUDIO_SEEK_RENDERING_START:
            case IMediaPlayer.MEDIA_INFO_AUDIO_DECODED_START:
            case IMediaPlayer.MEDIA_INFO_VIDEO_DECODED_START:
                hideLoading();
                isBuffering = false;
                break;
            case IMediaPlayer.MEDIA_INFO_VIDEO_TRACK_LAGGING:
                // 视频跟踪滞后
                break;
            case IMediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START:
            case IMediaPlayer.MEDIA_INFO_AUDIO_RENDERING_START:
                if (status == STATE_PREPARED && isFirstPlay) {
                    postDelayed(this::pausePlay, 0);
                    isFirstPlay = false;
                    break;
                }
                if (status == STATE_STOP) {
                    postDelayed(this::stopPlay, 100);
                    break;
                }
                hideLoading();
                isBuffering = false;
                // 视频渲染的开始 开始播放
                hideLoading();
                coverIv.setVisibility(GONE);
                status = STATE_PLAYING;
                centerPlayIv.setVisibility(GONE);
                mHandler.sendEmptyMessage(MESSAGE_SHOW_PROGRESS);
                if (currentTipState != -1) {
                    pausePlay();
                } else if (isNeedJudgeNetOnStart && VideoPlayUtil.isMobileNet(mContext) && !isAlreadyShowNoWifi) {
                    if (UnLog.D) {
                        UnLog.d(TAG, "onInfo show wifi");
                    }
                    showNetChangelayout();
                } else {
                    changeVoiceState();
                    setScreenOn();
                }
                updatePausePlay();

                mHandler.sendEmptyMessage(MESSAGE_HANDLER_SPEED);
                if (UnLog.D) {
                    UnLog.d(TAG, "视频渲染的开始");
                }
                break;
            case IMediaPlayer.MEDIA_INFO_BUFFERING_START:
                // 缓冲启动
                if (UnLog.D) {
                    UnLog.d(TAG, "缓冲启动");
                }
                showLoading();
                isBuffering = true;
                break;
            case IMediaPlayer.MEDIA_INFO_BUFFERING_END:
                // 缓冲结束
                if (UnLog.D) {
                    UnLog.d(TAG, "缓冲结束");
                }
                hideLoading();
                isBuffering = false;
                break;
            case IMediaPlayer.MEDIA_INFO_NETWORK_BANDWIDTH:
                // 网络带宽
                break;
            case IMediaPlayer.MEDIA_INFO_BAD_INTERLEAVING:
                // 交错
                break;
            case IMediaPlayer.MEDIA_INFO_NOT_SEEKABLE:
                // 不可见
                break;
            case IMediaPlayer.MEDIA_INFO_METADATA_UPDATE:
                // 元数据更新
                break;
            case IMediaPlayer.MEDIA_INFO_UNSUPPORTED_SUBTITLE:
                // 不支持 SUBTITLE
                break;
            case IMediaPlayer.MEDIA_INFO_SUBTITLE_TIMED_OUT:
                // SUBTITLE 超时
                break;
            case IMediaPlayer.MEDIA_INFO_VIDEO_ROTATION_CHANGED:
                // 视频旋转改变
                this.degree = degree;
                break;
            default:
                break;
        }
        if (onPlayerStateListener != null) {
            return onPlayerStateListener.onInfo(mediaInfo, degree);
        }
        return true;
    }

    /**
     * 恢复系统其它媒体的状态
     */
    public void resumeOtherVoice() {
        VideoPlayUtil.muteAudioFocus(getActivity(), false);
        resumeCount++;
        pauseCount = 0;
    }

    /**
     * 暂停系统其它媒体的状态
     */
    public void pauseOtherVoice() {
        VideoPlayUtil.muteAudioFocus(getActivity(), true);
        resumeCount = 0;
        pauseCount++;
    }

    @Override
    public void onSeekComplete() {
        if (UnLog.D) {
            UnLog.d(TAG, "onSeekComplete position:" + videoView.getCurrentPosition());
        }
        if (onPlayerStateListener != null) {
            onPlayerStateListener.onSeekComplete();
        }
    }

    public JoyNoteVideoPlayView setMtaListener(AVideoMtaListener mtaListener) {
        this.mtaListener = mtaListener;
        return this;
    }

    /**
     * 设置播放进度拖动监听
     */
    public void setProgressChangeListener(IProgrssChangeListener progressChangeListener) {
        this.progressChangeListener = progressChangeListener;
    }

    /**
     * 设置播放进度改变监听
     */
    public void setSeekBarChangeListener(IProgrssChangeListener progressChangeListener) {
        this.seekBarChangeListener = progressChangeListener;
    }

    /**
     * 切换窗口状态
     */
    public JoyNoteVideoPlayView changeToScreen(int state) {
        screenState = state;
        //切换到小屏
        switch (screenState) {
            case SCREEN_NORMAL:
                changeToNormalUI();
                break;
            case SCREEN_BIG:
                changeToBigUI();
                break;
            case SCREEN_SMALL:
                changeToSmallUI();
                break;
            case SCREEN_ITEM:
                changeToItemUI();
                break;
            case SCREEN_NO_UI:
                changeToNoUI();
                break;
            default:
                break;
        }
        beforeScreenState = screenState;
        return this;
    }


    private void changeToSmallUI() {
        isLandScape = false;
        smallTopBarLayout.setVisibility(VISIBLE);
        if (isVoiceOff) {
            voiceIconSmall.setVisibility(VISIBLE);
        } else {
            voiceIconSmall.setVisibility(GONE);
        }
        bottomProgressBarSmall.setVisibility(VISIBLE);
        if (voiceIcon.getVisibility() == VISIBLE) {
            voiceIcon.setVisibility(GONE);
        }
        if (bottomVoice.getVisibility() == VISIBLE) {
            bottomVoice.setVisibility(GONE);
        }
        if (currentTipState != -1) {
            mNetErrorLayout.setVisibility(GONE);
            switch (currentTipState) {
                case STATE_NET_ERROR:
                    showNetErrorlayout();
                    break;
                case STATE_LOAD_ERROR:
                    showLoadErrorLayout(false);
                    break;
                case STATE_NO_WIFI_TIP:
                    showNetChangelayout();
                    break;
                default:
                    break;
            }
        }
        if (isLoading) {
            loadingLayout.setVisibility(GONE);
            loadingLayoutSmall.setVisibility(VISIBLE);
        }
        hide();
        //如果是正在播放片尾，显示片尾的UI
        setVideoTailUi();
    }

    /**
     * 不显示任何UI，只显示中间播放按钮，无网提示等显示为小屏UI
     */
    private void changeToNoUI() {
        isLandScape = false;
        hideCenterPlayer(true);
        hideAllUI();
        if (currentTipState != -1) {
            mNetErrorLayout.setVisibility(GONE);
            switch (currentTipState) {
                case STATE_NET_ERROR:
                    showNetErrorlayout();
                    break;
                case STATE_LOAD_ERROR:
                    showLoadErrorLayout(false);
                    break;
                case STATE_NO_WIFI_TIP:
                    showNetChangelayout();
                    break;
                default:
                    break;
            }
        }
        if (isLoading) {
            loadingLayout.setVisibility(GONE);
            loadingLayoutSmall.setVisibility(VISIBLE);
        }
        hide();
        //如果是正在播放片尾，显示片尾的UI
        setVideoTailUi();
    }

    private void changeToNormalUI() {
        isLandScape = false;
        hideControlPanl(false);
        show(true);
        //重新设置进度条上展示分段视频的点
        bottomSeekBarProgrssBg.setPoints(points);
        if (beforeScreenState == SCREEN_SMALL) {
            smallTopBarLayout.setVisibility(GONE);
            bottomProgressBarSmall.setVisibility(GONE);
            if (currentTipState != -1) {
                errorLayoutSmall.setVisibility(GONE);
                switch (currentTipState) {
                    case STATE_NET_ERROR:
                        showNetErrorlayout();
                        break;
                    case STATE_LOAD_ERROR:
                        showLoadErrorLayout(false);
                        break;
                    case STATE_NO_WIFI_TIP:
                        showNetChangelayout();
                        break;

                    default:
                        break;
                }
            }
        }

        if (isLoading) {
            loadingLayout.setVisibility(VISIBLE);
            loadingLayoutSmall.setVisibility(GONE);
        }
        //如果是正在播放片尾，显示片尾的UI
        setVideoTailUi();
    }

    private void changeToBigUI() {
        if (UnLog.D) {
            UnLog.d(TAG, "degree:" + degree);
            UnLog.d(TAG, "width:" + getVideoWidth());
            UnLog.d(TAG, "height:" + getVideoHeight());
        }

        if (beforeScreenState == SCREEN_SMALL) {
            changeToNormalUI();
        }
        //大屏简化进度条不展示分段视频的点
        bottomSeekBarProgrssBg.setPoints(null);
        isLandScape = true;
        //如果是正在播放片尾，显示片尾的UI
        setVideoTailUi();
    }

    /**
     * item 内嵌view
     */
    private void changeToItemUI() {
        if (itemCtrl == null) {
            itemCtrl = new ItemVideoPlayerController(mContext);
            itemVideo = itemCtrl.getView();
            this.addView(itemVideo);
        }
        hideBottomBar(true);
        if (itemVideo != null) {
            itemVideo.setVisibility(VISIBLE);
        }
        setShowVoice(false, false);
        setShowBottomVoice(false, false);
    }


    /**
     * 设置播放器内的按钮点击事件
     */
    public JoyNoteVideoPlayView setVideoViewBtClickListener(AVideoViewBtClickListener videoViewBtClickListener) {
        this.videoViewBtClickListener = videoViewBtClickListener;
        return this;
    }


    /**
     * 获取实际的视频宽度（非控件）
     */
    public int getVideoWidth() {
        int width = 0;
        if (videoView != null) {
            IjkMediaPlayer md = videoView.getIjkMediaPlayer();
            if (md != null) {
                if (degree == 90 || degree == 270) {
                    width = md.getVideoHeight();
                } else {
                    width = md.getVideoWidth();
                }
            }
        }
        if (width == 0) {
            width = coverBitmapWidth;
        }
        return width;
    }

    /**
     * 获取实际的视频高度（非控件）
     */
    public int getVideoHeight() {
        int height = 0;
        if (videoView != null) {
            IjkMediaPlayer md = videoView.getIjkMediaPlayer();
            if (md != null) {
                if (degree == 90 || degree == 270) {
                    height = md.getVideoWidth();
                } else {
                    height = md.getVideoHeight();
                }
            }
        }
        if (height == 0) {
            height = coverBitmapHeight;
        }
        return height;
    }

    /**
     * 网络变成非wifi
     */
    public void wifiChangeTo4G() {
        //播放状态时，没有提示过nowifi，则提示,提示过的，则自动切换
        //暂停状态时，等点击开始播放的时候，处理
        //异常状态，不处理
        if (UnLog.D) {
            UnLog.d(TAG, "wifiChangeTo4G");
        }
        if (status == STATE_PLAYING) {
            if (!isAlreadyShowNoWifi) {
                showNetChangelayout();
            } else {
                videoView.pause();
                reTry();
            }
        } else if (status == STATE_PAUSED && currentTipState == -1) {
            isNeedAccessChangeToWifi = true;
        }
    }

    /**
     * 手机网络变成wifi
     */
    public void mobileNetChangeToWifi() {
        //播放状态时，立即重试
        //暂停状态，等点击开始播放的时候，处理
        //流量提示状态时，隐藏提示，其他异常状态时，不处理
        if (UnLog.D) {
            UnLog.d(TAG, "mobileNetChangeToWifi");
        }
        if (status == STATE_PLAYING) {
            videoView.pause();
            reTry();
        } else if (status == STATE_PAUSED && currentTipState == -1) {
            isNeedAccessChangeToWifi = true;
        } else if (currentTipState == STATE_NO_WIFI_TIP) {
            hideErrorLayout();
            isNeedAccessChangeToWifi = true;
        }
    }


    /**
     * 获取底部状态栏是否可见
     */
    public boolean isBottomBarVisible() {
        return bottomBarLayout.getVisibility() == VISIBLE;
    }

    /**
     * 获取播放器状态
     */
    public int getVideoState() {
        return status;
    }

    public void setVideoViewOnTouchListener(IVideoViewOnTouchListener videoViewOnTouchListener) {
        this.videoViewOnTouchListener = videoViewOnTouchListener;
    }

    public void setPointPositions(List<Integer> positions) {
        if (positions == null || positions.size() == 0) {
            return;
        }
        this.points = positions;
        nomalSeekBarProgrssBg.setPoints(points);
        bottomSeekBarProgrssBg.setPoints(points);
    }

    public void setPointView(View pointView) {
        if (pointView == null || pointView.getParent() != null) {
            return;
        }
        mPointView = pointView;
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        layoutParams.bottomMargin = DpiUtil.dip2px(mContext, 50);
        rootLayout.addView(pointView, layoutParams);
    }


    private void setSelectPoint(int position, boolean isPlay) {
        if (points == null || points.size() == 0 || progressChangeListener == null) {
            return;
        }
        if (isPlay) {
            for (int i = 0; i < points.size(); i++) {
                if (position >= points.get(i) && position <= points.get(i) + 1000) {
                    if (currentPoint != i) {
                        if (UnLog.D) {
                            UnLog.d(TAG, "ProgressPointSelect:" + i);
                        }
                        progressChangeListener.onProgressPointSelect(i);
                        currentPoint = i;
                    }
                    break;
                }
            }
        } else {
            for (int i = 1; i < points.size(); i++) {
                if (position >= points.get(i - 1) && position < points.get(i)) {
                    if (currentPoint != (i - 1)) {
                        progressChangeListener.onProgressPointSelect(i - 1);
                        currentPoint = i - 1;
                    }
                    break;
                }
                if (i == points.size() - 1 && position >= points.get(i)) {
                    if (currentPoint != i) {
                        progressChangeListener.onProgressPointSelect(i);
                        currentPoint = i;
                    }
                    break;
                }
            }
        }
    }

    private void setSeekBarBgProgress(int progress, boolean isBottomBar) {
        if (isBottomBar) {
            bottomSeekBarProgrssBg.setProgress(progress);
            bottomProgressBar.invalidate();
        } else {
            nomalSeekBarProgrssBg.setProgress(progress);
        }
    }

    /**
     * 是否需要开始播放的时候检测网络
     */
    public JoyNoteVideoPlayView setNeedJudgeNetOnStart(boolean needJudgeNet) {
        isNeedJudgeNetOnStart = needJudgeNet;
        return this;
    }

    /**
     * 是否需要暂停时，唤起后台音乐
     */
    public JoyNoteVideoPlayView setResumeBgMusicOnPause(boolean resumeBgMusicOnPause) {
        isResumeBgMusicOnPause = resumeBgMusicOnPause;
        return this;
    }


    public JoyNoteVideoPlayView setSku(String sku) {

        return this;
    }


    public JoyNoteVideoPlayView setVideoId(String videoId) {
        return this;
    }

    public JoyNoteVideoPlayView setJumpFrom(int jumpFrom) {
        this.jumpFrom = jumpFrom;
        return this;
    }

    /**
     * 设置分段视频标签
     */
    public JoyNoteVideoPlayView setMark(String mark) {
        return this;
    }


    /**
     * 设置屏幕常亮
     */
    private void setScreenOn() {
        if (!isNeedKeepScreenOn || isScreenOn) {
            return;
        }
        Activity activity = getActivity();
        if (null != activity) {
            if (UnLog.D) {
                UnLog.d(TAG, "setScreenOn");
            }
            isScreenOn = true;
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        }
    }

    /**
     * 关闭屏幕常亮
     */
    private void setScreenOff() {
        if (!isNeedKeepScreenOn || !isScreenOn) {
            return;
        }
        Activity activity = getActivity();
        if (null != activity) {
            if (UnLog.D) {
                UnLog.d(TAG, "setScreenOff");
            }
            isScreenOn = false;
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        }
    }

    /**
     * 设置播放状态时，是否要保持屏幕常亮
     */
    public JoyNoteVideoPlayView setNeedKeepScreenOn(boolean needKeepScreenOn) {
        isNeedKeepScreenOn = needKeepScreenOn;
        return this;
    }

    /**
     * 设置片尾视频的播放地址
     */
    public JoyNoteVideoPlayView setTailSource(String tailSource) {
        this.tailSource = tailSource;
        return this;
    }

    /**
     * 设置片尾视频封面图的url
     */
    public JoyNoteVideoPlayView setTailCoverUrl(String tailCoverUrl) {
        this.tailCoverUrl = tailCoverUrl;
        return this;
    }

    /**
     * 是否是播放片尾
     */
    public boolean isPlayingTail() {
        return isPlayingTail;
    }

    /**
     * <pre>
     *     fitParent:可能会剪裁,保持原视频的大小，显示在中心,当原视频的大小超过view的大小超过部分裁剪处理
     *     fillParent:可能会剪裁,等比例放大视频，直到填满View为止,超过View的部分作裁剪处理
     *     wrapContent:将视频的内容完整居中显示，如果视频大于view,则按比例缩视频直到完全显示在view中
     *     fitXY:不剪裁,非等比例拉伸画面填满整个View
     *     16:9:不剪裁,非等比例拉伸画面到16:9,并完全显示在View中
     *     4:3:不剪裁,非等比例拉伸画面到4:3,并完全显示在View中
     * </pre>
     */
    public JoyNoteVideoPlayView setScaleType(String scaleType) {
        if (VideoConstant.SCALETYPE_FITPARENT.equals(scaleType)) {
            videoView.setAspectRatio(IPlayerControl.PlayerOptions.AR_ASPECT_FIT_PARENT);
        } else if (VideoConstant.SCALETYPE_FILLPARENT.equals(scaleType)) {
            videoView.setAspectRatio(IPlayerControl.PlayerOptions.AR_ASPECT_FILL_PARENT);
        } else if (VideoConstant.SCALETYPE_WRAPCONTENT.equals(scaleType)) {
            videoView.setAspectRatio(IPlayerControl.PlayerOptions.AR_ASPECT_WRAP_CONTENT);
        } else if (VideoConstant.SCALETYPE_FITXY.equals(scaleType)) {
            videoView.setAspectRatio(IPlayerControl.PlayerOptions.AR_MATCH_PARENT);
        } else if (VideoConstant.SCALETYPE_16_9.equals(scaleType)) {
            videoView.setAspectRatio(IPlayerControl.PlayerOptions.AR_16_9_FIT_PARENT);
        } else if (VideoConstant.SCALETYPE_4_3.equals(scaleType)) {
            videoView.setAspectRatio(IPlayerControl.PlayerOptions.AR_4_3_FIT_PARENT);
        }
        return this;
    }

    /**
     * 封面图
     */
    public JoyNoteVideoPlayView setCoverImageScaleType(ImageView.ScaleType scaleType) {
        if (coverIv != null) {
            coverIv.setScaleType(scaleType);
        }
        return this;
    }

    public void setOnFloatWindowClickListener(OnFloatWindowClick listener) {
        this.mOnFloatWindowClick = listener;
    }

    public void setOnMePlayerStateListener(MePlayerStateListener listener) {
        this.mePlayerStateListener = listener;
    }

    public interface OnFloatWindowClick {
        void onClick();
    }

}
