package com.jd.oa.player;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.joy.note.R;
import com.jd.oa.router.DeepLink;

@Route(DeepLink.ME_VIDEO_PLAYER)
public class MePlayActivity extends BaseActivity {
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().setStatusBarColor(Color.WHITE);
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_me_play_main);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
    }

}
