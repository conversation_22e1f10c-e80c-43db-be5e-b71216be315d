package com.jd.oa.player;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static com.jd.oa.deeplink.DeepLinkTools.FALSE;
import static com.jd.oa.deeplink.DeepLinkTools.LANDSCAPE;
import static com.jd.oa.deeplink.DeepLinkTools.MPARAM;
import static com.jd.oa.deeplink.DeepLinkTools.TRUE;
import static com.jd.oa.deeplink.DeepLinkTools.VIDEO_TITLE;
import static com.jd.oa.deeplink.DeepLinkTools.VIDEO_URL;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.JoyNoteTitleBar;

import org.json.JSONObject;

public class MePlayFragment extends Fragment {
    public static final String VIDEO_COVER_URL = "coverUrl";
    private MePlayer2 mePlayer = null;
    private FrameLayout playerLayout = null;
    private JoyNoteTitleBar joyNoteTitleBar = null;
    private String title = null;
    private String landscape = TRUE;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_me_play_main, container, false);
        initView(view);
        return view;
    }

    public void initView(@NonNull View view) {
        playerLayout = view.findViewById(R.id.me_video_layout);
        joyNoteTitleBar = view.findViewById(R.id.me_video_toolbar);
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        Intent intent = activity.getIntent();
        if (intent == null) {
            return;
        }
        String url = null, coverUrl = null;
        String param = intent.getStringExtra(MPARAM);
        if (param == null) {
            url = intent.getStringExtra(VIDEO_URL);
            coverUrl = intent.getStringExtra(VIDEO_COVER_URL);
            title = intent.getStringExtra(VIDEO_TITLE);
        } else {
            try {
                JSONObject paramJson = new JSONObject(param);
                url = paramJson.optString(VIDEO_URL);
                title = paramJson.optString(VIDEO_TITLE);
                coverUrl = paramJson.optString(VIDEO_COVER_URL);
                landscape = paramJson.optString(LANDSCAPE, TRUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        mePlayer = new MePlayer(activity, url, coverUrl);
        mePlayer.setTitle(title);
        joyNoteTitleBar.setTitle(title);
        mePlayer.setBackBtnFunction(true);
        playerLayout.addView(mePlayer.getVideoView());

        view.post(() -> {
            int currentOrientation = getResources().getConfiguration().orientation;
            if (FALSE.equals(landscape)) {
                if (currentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
                    landscape = null;
                    mePlayer.toggleFullScreen(0);
                }
            }
        });
    }


    @Override
    public void onResume() {
        super.onResume();
        if (mePlayer != null) {
            mePlayer.onResume();
        }
    }

    @Override
    public void onPause() {
        if (mePlayer != null) {
            mePlayer.onPause();
        }
        super.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mePlayer != null) {
            mePlayer.onDestroy();
        }
    }
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mePlayer != null) {
            mePlayer.configurationChanged(newConfig);
        }
        if (playerLayout == null) {
            return;
        }
        if (this.getResources().getConfiguration().orientation
                == Configuration.ORIENTATION_LANDSCAPE) {
            if (joyNoteTitleBar != null) {
                joyNoteTitleBar.setVisibility(View.GONE);
            }
        } else {
            if (joyNoteTitleBar != null) {
                joyNoteTitleBar.setVisibility(View.VISIBLE);
            }
        }
        playerLayout.getLayoutParams().width = MATCH_PARENT;
        playerLayout.getLayoutParams().height = MATCH_PARENT;
        if (mePlayer != null) {
            mePlayer.syncProgress();
            mePlayer.setTitle(title);
        }
    }

}
