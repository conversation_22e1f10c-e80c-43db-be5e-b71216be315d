package com.jd.oa.player;

import android.content.Context;

import androidx.annotation.NonNull;

public class MePlayerService {
    public static MePlayer2 getMePlay(@NonNull Context context, @NonNull String url, String coverUrl) {
        return getMePlay(context, url, coverUrl, false);
    }

    public static MePlayer2 getMePlay(@NonNull Context context, @NonNull String url, String coverUrl, boolean audio) {
        if (audio) {
            return new MePlayerNew(context, url);
        } else {
            return new MePlayer(context, url, coverUrl);
        }
    }
}
