package com.jd.oa.player;

import android.content.Context;
import android.content.res.Configuration;
import android.media.AudioManager;
import android.media.MediaMetadataRetriever;
import android.media.MediaPlayer;
import android.media.PlaybackParams;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.View;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.joy.note.R;
import com.jingdong.common.unification.video.player.VideoPlayUtil;

/**
 * @noinspection unused
 */
class MePlayerNew implements MePlayer2 {
    private static final int MAX_SEEK = 1000;
    private static final int CHECK_POS = 100;
    private static final int CHECK_POS_ONCE = 101;
    private static final int TIME_GAP = 15 * 1000;
    private MediaPlayer audioPlayer;
    private final View audioView;
    private final SeekBar seekBar;
    private final TextView currentTime;
    private final TextView totalTime;
    private final ImageView playBtnInside;
    private MePlayerListener mePlayerListener;
    private MePlayerSeekBarListener mePlayerSeekBarListener;
    private boolean prepare, seeking;
    private int durationTime;
    private boolean firstPlay;
    SpeedItem sDefaultItem = new SpeedItem("1.0X", 1);
    private final Handler handler = new Handler(Looper.getMainLooper());

    private MePlayerStateListener mePlayerStateListener;
    CheckHandler checkHandler;

    MePlayerNew(Context context, String url) {
        audioPlayer = new MediaPlayer();
        audioView = View.inflate(context, R.layout.me_video_play_view_audio, null);
        seekBar = audioView.findViewById(R.id.app_video_seekBar);
        currentTime = audioView.findViewById(R.id.app_video_currentTime_full);
        totalTime = audioView.findViewById(R.id.app_video_endTime_full);
        View playBtn = audioView.findViewById(R.id.app_video_play_rl);
        playBtnInside = audioView.findViewById(R.id.app_video_play);
        View forwardBtn = audioView.findViewById(R.id.audio_forward_btn);
        TextView speedBtn = audioView.findViewById(R.id.me_tv_speed_set);
        View backBtn = audioView.findViewById(R.id.audio_back_btn);
        audioPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
        seekBar.setMax(MAX_SEEK);
        checkHandler = new CheckHandler(this);
        checkHandler.sendEmptyMessage(CHECK_POS);
        audioPlayer.setOnCompletionListener(mp -> {
            //结束音频播放完成，释放声道占用
            JMAudioCategoryManager.getInstance().releaseJoyNoteAudio();
        });
        audioPlayer.setOnBufferingUpdateListener((mp, percent) -> {
            if (mp == null) {
                return;
            }
            seekBar.setSecondaryProgress(percent * MAX_SEEK);
        });
        audioPlayer.setOnSeekCompleteListener(mediaPlayer -> {
            JMAudioCategoryManager.JMEAudioCategorySet result = setAudioCategory();
            if (result.available) {
                startPlay();
            }
            seeking = false;
        });
        speedBtn.setOnClickListener(v -> new PlaySpeedDialog(audioView.getContext(), item -> {
            if (item == null) {
                return;
            }
            if (item == sDefaultItem) {
                return;
            }
            if (item.getValue() < 0) {
                return;
            }
            sDefaultItem = item;
            float speed = (float) item.getValue();
            if (audioPlayer != null) {
                try {//这里播放器没初始化好的时候调getPlaybackParams会报IllegalStateException
                    PlaybackParams params = audioPlayer.getPlaybackParams();
                    params.setSpeed(speed);
                    audioPlayer.setPlaybackParams(params);
                    speedBtn.setText(item.getName());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, sDefaultItem).show());
        forwardBtn.setOnClickListener(v -> {
            if (audioPlayer == null || !prepare || seeking) {
                return;
            }
            int seekPosition = getCurrentPosition() + TIME_GAP;
            if (seekPosition > getDuration()) {
                seekPosition = getDuration();
            }
            updateSeeker(seekPosition);
            seekToPosition(seekPosition);
        });
        backBtn.setOnClickListener(v -> {
            if (audioPlayer == null || !prepare || seeking) {
                return;
            }
            int seekPosition = getCurrentPosition() - TIME_GAP;
            if (seekPosition < 0) {
                seekPosition = 0;
            }
            updateSeeker(seekPosition);
            seekToPosition(seekPosition);
        });
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (!prepare) {
                    return;
                }
                if (fromUser) {
                    int pos = (int) (((float) progress / MAX_SEEK) * getDuration());
                    updateSeekerText(pos);
                    if (mePlayerListener != null) {
                        mePlayerListener.onProgressChange(getCurrentPosition());
//                        System.out.println("audioPlayer.getCurrentPosition()222=" + audioPlayer.getCurrentPosition());
                    }
                } else {
                    if (mePlayerSeekBarListener != null) {
                        mePlayerSeekBarListener.onProgressChange(getCurrentPosition(), getDuration());
//                        System.out.println("audioPlayer.getCurrentPosition()333=" + audioPlayer.getCurrentPosition());
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if (!prepare) {
                    return;
                }
                int position = (int) (seekBar.getProgress() / (float) MAX_SEEK * getDuration());
                seekToPosition(position);
            }
        });
        playBtn.setOnClickListener(v -> {
            if (audioPlayer == null || !prepare || seeking) {
                if (!prepare) {
                    initUrl(url, () -> clickPlayBtn(checkHandler));
                }
                return;
            }
            clickPlayBtn(checkHandler);
        });
        initUrl(url, null);
    }

    private void initUrl(String url, Runnable runnable) {
        Thread thread = new Thread(() -> {
            MediaMetadataRetriever retriever = null;
            try {
                try {
                    retriever = new MediaMetadataRetriever();
                    retriever.setDataSource(url);
                    String time = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                    if (time != null) {
                        durationTime = Integer.parseInt(time);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                audioPlayer.setDataSource(url);
                audioPlayer.prepare();
                if (durationTime == 0) {
                    durationTime = getDuration();
                }
                prepare = true;
                handler.post(() -> updateSeeker(0));
                if (runnable != null) {
                    runnable.run();
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        if (retriever != null) {
                            retriever.close();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        thread.start();
    }

    private void clickPlayBtn(CheckHandler checkHandler) {
        if (audioPlayer.isPlaying()) {
            onPause();
            playBtnInside.setImageResource(R.drawable.joy_note_play_btn);
        } else {
            JMAudioCategoryManager.JMEAudioCategorySet result = setAudioCategory();
            if (result.available) {
                startPlay();
                playBtnInside.setImageResource(R.drawable.vd_pause_video_hor_audio);
            }
        }
        checkHandler.sendEmptyMessage(CHECK_POS);
    }

    @Override
    public View getVideoView() {
        return audioView;
    }

    @Override
    public void startPlay() {
        if (audioPlayer != null && !audioPlayer.isPlaying()) {
            audioPlayer.start();
            firstPlay = true;

            if (mePlayerStateListener != null) {
                mePlayerStateListener.onPlay();
            }
        }
    }

    @Override
    public void onPause() {
        if (audioPlayer != null) {
            JMAudioCategoryManager.getInstance().releaseJoyNoteAudio();
            audioPlayer.pause();

            if (mePlayerStateListener != null) {
                mePlayerStateListener.onPause();
            }
        }
    }

    @Override
    public void setTitle(String title) {
    }

    @Override
    public void setBackBtnFunction(boolean closeActivity) {
    }

    @Override
    public void onResume() {
    }

    /**
     * 全屏切换
     *
     * @param changeState -1:自动切换 0：切换到竖屏 1：切换到横屏 2:切换到反向横屏 3:切换到反向竖屏
     */
    @Override
    public void toggleFullScreen(int changeState) {
    }

    @Override
    public int getDuration() {
        if (durationTime != 0) {
            return durationTime;
        }
        if (audioPlayer != null) {
            return audioPlayer.getDuration();
        }
        return 0;
    }

    @Override
    public int getCurrentPosition() {
        if (audioPlayer != null) {
            return audioPlayer.getCurrentPosition();
        }
        return 0;
    }

    @Override
    public void seekToPosition(int position) {
        if (audioPlayer != null && prepare) {
            seeking = true;
            if (!firstPlay) {
                JMAudioCategoryManager.JMEAudioCategorySet result = setAudioCategory();
                if (result.available) {
                    startPlay();
                }
                handler.postDelayed(() -> {
                    audioPlayer.seekTo(position);
                    updateSeeker(position);
                }, 100);
            } else {
                audioPlayer.seekTo(position);
                updateSeeker(position);
            }
        }
    }

    @Override
    public void onDestroy() {
        JMAudioCategoryManager.getInstance().releaseJoyNoteAudio();
        if (audioPlayer != null) {
            audioPlayer.release();
            audioPlayer = null;
        }
        checkHandler.removeCallbacksAndMessages(null);
    }

    @Override
    public void syncProgress() {
    }

    @Override
    public void configurationChanged(Configuration newConfig) {
    }

    @Override
    public void setMePlayerListener(final MePlayerListener mePlayerListener) {
        this.mePlayerListener = mePlayerListener;
    }

    @Override
    public void setMePlayerSekBarListener(final MePlayerSeekBarListener mePlayerSeekBarListener) {
        this.mePlayerSeekBarListener = mePlayerSeekBarListener;
    }

    @Override
    public void setMePlayerStateListener(MePlayerStateListener listener) {
        this.mePlayerStateListener = listener;
    }

    private void updateSeeker() {
        if (audioPlayer != null) {
            updateSeeker(audioPlayer.getCurrentPosition());
        }
    }

    private void updateSeeker(int position) {
        if (!prepare || audioPlayer == null) {
            return;
        }
        updateSeekerBar((float) position);
        updateSeekerText(position);
    }

    private void updateSeekerBar(float position) {
        if (seekBar != null) {
            int pos = (int) (position / getDuration() * MAX_SEEK);
            seekBar.setProgress(pos);
        }
    }

    private void updateSeekerText(int position) {
        if (!prepare || audioPlayer == null) {
            return;
        }
        if (currentTime != null) {
            currentTime.setText(VideoPlayUtil.generateTime(position));
        }
        if (totalTime != null) {
            totalTime.setText(VideoPlayUtil.generateTime(getDuration()));
        }
        if (audioPlayer.isPlaying()) {
            playBtnInside.setImageResource(R.drawable.vd_pause_video_hor_audio);
        } else {
            playBtnInside.setImageResource(R.drawable.joy_note_play_btn);
        }
    }

    private JMAudioCategoryManager.JMEAudioCategorySet setAudioCategory() {
        return JMAudioCategoryManager.getInstance().setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE_AUDIO_PLAY, () -> {
            if (audioPlayer != null) {
                audioPlayer.pause();
                playBtnInside.setImageResource(R.drawable.joy_note_play_btn);
            }
        });
    }

    private static class CheckHandler extends Handler {
        private final MePlayerNew mePlayerNew;

        public CheckHandler(@NonNull MePlayerNew mePlayerNew) {
            this.mePlayerNew = mePlayerNew;
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.what == CHECK_POS || msg.what == CHECK_POS_ONCE) {
                mePlayerNew.updateSeeker();
                if (msg.what == CHECK_POS_ONCE) {
                    return;
                }
                postDelayed(() -> sendEmptyMessage(CHECK_POS), 500);
            }
        }
    }
}
