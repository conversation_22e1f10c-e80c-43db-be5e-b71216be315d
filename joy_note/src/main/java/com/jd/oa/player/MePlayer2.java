package com.jd.oa.player;

import android.content.res.Configuration;
import android.view.View;

public interface MePlayer2 {
    View getVideoView();

    void startPlay();

    void onPause();

    void setTitle(String title);

    void setBackBtnFunction(boolean closeActivity);

    void onResume();

    void toggleFullScreen(int changeState);

    int getDuration();

    int getCurrentPosition();

    void seekToPosition(int position);

    void onDestroy();

    void syncProgress();

    void configurationChanged(Configuration newConfig);

    void setMePlayerListener(final MePlayerListener mePlayerListener);

    void setMePlayerSekBarListener(final MePlayerSeekBarListener mePlayerSeekBarListener);

    void setMePlayerStateListener(final MePlayerStateListener listener);
}
