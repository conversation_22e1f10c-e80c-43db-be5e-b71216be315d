package com.jd.oa.player;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.jd.oa.joy.note.R;

import java.util.ArrayList;
import java.util.List;

/**
 * @noinspection unused
 */
@SuppressLint("ViewConstructor")
class PlayBackSpeedView extends LinearLayout {

    private final OnSpeedItemClickListener mListener;
    private LayoutInflater mLayoutInflater;
    private List<SpeedItem> items;
    private SpeedItem sDefaultItem;


    public PlayBackSpeedView(Context context, OnSpeedItemClickListener listener, SpeedItem item) {
        super(context);
        this.mListener = listener;
        this.sDefaultItem = item;
        initView(context);
    }

    private void initView(Context context) {
        mLayoutInflater = LayoutInflater.from(context);
        inflate(context, R.layout.view_playback_speed, this);

        findViewById(R.id.mePlayBackSpeedEmpty).setOnClickListener(view -> mListener.onSpeedItemClick(null));

        items = new ArrayList<>(6);

        items.add(new SpeedItem(context.getString(R.string.me_player_speed_title), -1));
//        items.add(new SpeedItem("3.0X", 3));
        items.add(new SpeedItem("2.0X", 2));
        items.add(new SpeedItem("1.5X", 1.5));
        items.add(new SpeedItem("1.25X", 1.25));
        items.add(new SpeedItem("1.0X", 1));
        items.add(new SpeedItem("0.75X", 0.75));
        items.add(new SpeedItem("0.5X", 0.5));

        ListView listView = findViewById(R.id.mePlayBackSpeedList);
        listView.setOnItemClickListener((adapterView, view, i, l) -> {
            SpeedItem item = items.get(i);
            if (item.getValue() > 0) {
                PlayBackSpeedView.this.sDefaultItem = item;
                mListener.onSpeedItemClick(PlayBackSpeedView.this.sDefaultItem);
            } else {
                mListener.onSpeedItemClick(null);
            }
        });
        listView.setAdapter(new BaseAdapter() {
            @Override
            public int getCount() {
                return items.size();
            }

            @Override
            public SpeedItem getItem(int i) {
                return items.get(i);
            }

            @Override
            public long getItemId(int i) {
                return i;
            }

            @Override
            public View getView(int i, View view, ViewGroup viewGroup) {
                @SuppressLint("ViewHolder") View itemView = mLayoutInflater.inflate(R.layout.joy_note_playback_speed_item, null);
                TextView text = itemView.findViewById(R.id.speedItemText);
                SpeedItem item = getItem(i);

                if (item.getValue() == sDefaultItem.getValue()) {
                    text.setTextColor(Color.parseColor("#FF4848"));
                } else {
                    text.setTextColor(Color.parseColor("#FFFFFFFF"));
                }

                text.setText(item.getName());
                return itemView;
            }
        });
    }

    public void setDefaultItem(SpeedItem sDefaultItem) {
        this.sDefaultItem = sDefaultItem;
    }

    public interface OnSpeedItemClickListener {
        void onSpeedItemClick(SpeedItem item);
    }
}
