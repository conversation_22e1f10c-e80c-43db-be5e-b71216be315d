package com.jd.oa.player;

import static com.jd.oa.utils.DeviceUtil.getScreenHeight;
import static com.jd.oa.utils.DeviceUtil.getScreenWidth;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;

import com.jd.lib.un.basewidget.widget.simple.utils.DpiUtils;
import com.jd.oa.joy.note.R;

import java.util.ArrayList;
import java.util.List;

class PlaySpeedDialog {


    private Context mContext;
    private OnSpeedItemClickListener mListener;
    private Dialog dialog;

    private List<SpeedItem> items;
    private SpeedItem sDefaultItem;

    public PlaySpeedDialog(Context context, OnSpeedItemClickListener listener, SpeedItem sDefaultItem) {
        mContext = context;
        this.mListener = listener;
        this.sDefaultItem = sDefaultItem;
        builder();
    }

    public PlaySpeedDialog builder() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.por_speed_dialog, null);
        view.setMinimumWidth(getScreenWidth(mContext));
        ListView speed_lv = view.findViewById(R.id.speed_lv);
        items = new ArrayList<>(6);

//        items.add(new SpeedItem("3.0X", 3));
        items.add(new SpeedItem("2.0X", 2));
        items.add(new SpeedItem("1.5X", 1.5));
        items.add(new SpeedItem("1.25X", 1.25));
        items.add(new SpeedItem("1.0X", 1));
        items.add(new SpeedItem("0.75X", 0.75));
        items.add(new SpeedItem("0.5X", 0.5));

        speed_lv.setOnItemClickListener((adapterView, view1, i, l) -> {
            SpeedItem item = items.get(i);
            if (item.getValue() > 0) {
                sDefaultItem = item;
                mListener.onSpeedItemClick(PlaySpeedDialog.this.sDefaultItem);
            } else {
                mListener.onSpeedItemClick(null);
            }
            dialog.dismiss();
        });
        speed_lv.setAdapter(new BaseAdapter() {
            @Override
            public int getCount() {
                return items.size();
            }

            @Override
            public SpeedItem getItem(int i) {
                return items.get(i);
            }

            @Override
            public long getItemId(int i) {
                return i;
            }

            @Override
            public View getView(int i, View view, ViewGroup viewGroup) {
                View itemView = LayoutInflater.from(mContext).inflate(R.layout.view_playback_speed_item_por, null);
                TextView text = itemView.findViewById(R.id.speedItemText);
                SpeedItem item = getItem(i);

                if (item.getValue() == sDefaultItem.getValue()) {
                    text.setTextColor(Color.parseColor("#FF4848"));
                } else {
                    text.setTextColor(Color.parseColor("#3C4056"));
                }

                text.setText(item.getName());
                return itemView;
            }
        });

        Button cancel_btn = view.findViewById(R.id.cancel_btn);
        cancel_btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        //定义dialog的布局
        dialog = new Dialog(mContext, R.style.TransDialogStyle);
        dialog.setCancelable(true);
        dialog.setCanceledOnTouchOutside(true);
        dialog.setContentView(view);
        Window dialogWindow = dialog.getWindow();
        dialogWindow.setGravity(Gravity.LEFT | Gravity.BOTTOM);
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.x = 0;
        lp.y = 0;
        int h = getScreenHeight(mContext);
        int contentH = DpiUtils.dp2px(440);
        if (contentH > h) {
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        } else {
            lp.height = contentH;
        }
        lp.width = getScreenWidth(mContext);
        dialogWindow.setAttributes(lp);
        return this;
    }

    public void show() {
        dialog.show();
    }

    public interface OnSpeedItemClickListener {
        void onSpeedItemClick(SpeedItem item);
    }

}
