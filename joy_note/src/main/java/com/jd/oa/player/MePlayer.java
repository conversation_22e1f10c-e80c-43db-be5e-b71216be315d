package com.jd.oa.player;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.view.View;

import com.jd.jdvideoplayer.live.SmallTV;
import com.jingdong.common.unification.video.VideoConstant;
import com.jingdong.common.unification.video.player.AVideoPlayStateListener;
import com.jingdong.common.unification.video.player.IProgrssChangeListener;

import tv.danmaku.ijk.media.example.widget.media.IPlayerControl;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

/**
 * @noinspection unused
 */
class MePlayer implements MePlayer2 {
    private final JoyNoteVideoPlayView mVideoView;
    private MePlayerListener mePlayerListener;
    private MePlayerSeekBarListener mePlayerSeekBarListener;

    private MePlayerStateListener mePlayerStateListener;

    MePlayer(Context context, String url, String coverUrl) {
        this(context, url, coverUrl, false);
    }

    MePlayer(Context context, String url, String coverUrl, boolean audioSkin) {
        SmallTV.getInstance().enableFlowWindow(false);
        mVideoView = new JoyNoteVideoPlayView(context, audioSkin);
        mVideoView.setAutoPlay(false);
        mVideoView.setScreenState(context.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE);
        mVideoView.setBackgroundColor(Color.BLACK);
        mVideoView.hideFullscreen(false);
        mVideoView.hideCenterPlayer(false);
        mVideoView.setHideRetryBt(false);
        mVideoView.setCoverUrl(coverUrl);
        mVideoView.setIsLocalVideo(false);
        mVideoView.setPlaySource(url);
        mVideoView.loadErrorRetry(true);
        mVideoView.hideCenterPlayer(false);
        mVideoView.setProgressChangeListener(new IProgrssChangeListener() {
            @Override
            public void onProgressChange(int seekProgress, int maximum) {
                if (mePlayerListener != null && !mVideoView.isDragging) {
                    mePlayerListener.onProgressChange(mVideoView.getCurrentPosition());
                }
            }

            @Override
            public void onProgressPointSelect(int i) {

            }
        });
        mVideoView.setOnMePlayerStateListener(mePlayerStateListener);
        mVideoView.setSeekBarChangeListener(new IProgrssChangeListener() {
            @Override
            public void onProgressChange(int position, int count) {
                if (mePlayerSeekBarListener != null) {
                    mePlayerSeekBarListener.onProgressChange(position, count);
                }
            }

            @Override
            public void onProgressPointSelect(int i) {

            }
        });
        IPlayerControl.PlayerOptions options = new IPlayerControl.PlayerOptions(false);
//        options.setAspectRatio(contentMode);
        options.setIsRequestAudioFocus(false);
        options.setCouldMediaCodec(true);
        options.setEnableAccurateSeek(false);
        options.addCustomOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "soundtouch", 1);
        mVideoView.getIjkVideoView().setPlayerOptions(options);
    }

    @Override
    public View getVideoView() {
        return mVideoView;
    }

    @Override
    public void startPlay() {
        if (mVideoView == null) {
            return;
        }
        mVideoView.startPlay();
    }

    @Override
    public void onPause() {
        if (mVideoView == null) {
            return;
        }
        mVideoView.onPause();
    }

    @Override
    public void setTitle(String title) {
        if (mVideoView == null) {
            return;
        }
        mVideoView.setLiveRoomName(title);
    }

    @Override
    public void setBackBtnFunction(boolean closeActivity) {
        if (mVideoView == null) {
            return;
        }
        mVideoView.setBackBtnFunction(closeActivity);
    }

    @Override
    public void onResume() {
        if (mVideoView == null) {
            return;
        }
        mVideoView.setScaleType(VideoConstant.SCALETYPE_WRAPCONTENT);
        mVideoView.onResume();
    }

    /**
     * 全屏切换
     *
     * @param changeState -1:自动切换 0：切换到竖屏 1：切换到横屏 2:切换到反向横屏 3:切换到反向竖屏
     */
    @Override
    public void toggleFullScreen(int changeState) {
        if (mVideoView == null) {
            return;
        }
        mVideoView.toggleFullScreen(changeState);
    }

    @Override
    public int getDuration() {
        if (mVideoView == null) {
            return 0;
        }
        return mVideoView.getDuration();
    }

    @Override
    public int getCurrentPosition() {
        if (mVideoView == null) {
            return 0;
        }
        return mVideoView.getCurrentPosition();
    }

    @Override
    public void seekToPosition(int position) {
        if (mVideoView == null) {
            return;
        }
        mVideoView.seekToPosition(position);
    }

    @Override
    public void onDestroy() {
        if (mVideoView == null) {
            return;
        }
        mVideoView.onDestroy();
    }

    @Override
    public void syncProgress() {
        if (mVideoView == null) {
            return;
        }
        mVideoView.syncProgress();
    }

    @Override
    public void configurationChanged(Configuration newConfig) {

        if (mVideoView == null) {
            return;
        }
        mVideoView.dispatchConfigurationChanged(newConfig);
    }

    @Override
    public void setMePlayerListener(final MePlayerListener mePlayerListener) {
        this.mePlayerListener = mePlayerListener;
    }

    @Override
    public void setMePlayerSekBarListener(final MePlayerSeekBarListener mePlayerSeekBarListener) {
        this.mePlayerSeekBarListener = mePlayerSeekBarListener;
    }

    @Override
    public void setMePlayerStateListener(MePlayerStateListener listener) {
        this.mePlayerStateListener = listener;
        if (mVideoView != null) {
            mVideoView.setOnMePlayerStateListener(mePlayerStateListener);
        }
    }
}
