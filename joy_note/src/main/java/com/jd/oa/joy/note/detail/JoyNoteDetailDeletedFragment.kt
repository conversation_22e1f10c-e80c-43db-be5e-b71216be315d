package com.jd.oa.joy.note.detail

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.bumptech.glide.Glide
import com.jd.oa.AppBase
import com.jd.oa.configuration.local.LocalConfigHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joy.note.DateTimeUtils
import com.jd.oa.joy.note.JoyNoteTools.MINUTES
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.compunent.JoyNoteTitleBar
import com.jd.oa.joy.note.detail.JoyNoteDetailActivity.updateStatus
import com.jd.oa.joy.note.model.VideoItemModel
import com.jd.oa.joy.note.model.VideoItemModel.RESTORE_MINUTES
import com.jd.oa.joy.note.repository.OperateCallback
import com.jd.oa.joy.note.repository.VideoListRepository
import com.jd.oa.ui.CircleImageView
import com.jd.oa.utils.ClipboardUtils
import com.jd.oa.utils.ToastUtils

class JoyNoteDetailDeletedFragment : BaseFragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_joy_note_detail_deleted, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val mToolBar = view.findViewById<JoyNoteTitleBar>(R.id.toolbar)
        mToolBar.title = getString(R.string.joynote_title)
        val tvBackHome = view.findViewById<TextView>(R.id.tv_back_home)
        tvBackHome.setOnClickListener(View.OnClickListener { v: View? ->
            activity?.let {
//                Router.build(DeepLink.JOY_NOTE_MAIN).go(it)
                it.finish()
            }
        })

        if (activity != null && activity is JoyNoteDetailActivity) {
            val activity = activity as JoyNoteDetailActivity

            if (activity.joyNoteDetailInfoModel?.minutesDetail?.deleter != null) {
                val llDeleteInfo: LinearLayout = view.findViewById(R.id.ll_delete_info)
                val tvName: TextView = view.findViewById(R.id.tv_name_deleter)
                val ivAvatar: CircleImageView = view.findViewById(R.id.iv_photo_deleter)
                val tvTime: TextView = view.findViewById(R.id.tv_delete_time)
                val tvLink: TextView = view.findViewById(R.id.tv_link)
                llDeleteInfo.visibility = View.VISIBLE
                tvLink.text = buildString {
                    append(LocalConfigHelper.getInstance(AppBase.getAppContext()).urlConstantsModel.noteUrl)
                    append(MINUTES)
                    append(activity.minutesId)
                }
                tvLink.setOnClickListener {
                    ClipboardUtils.setClipboardText(activity, tvLink.text.toString())
                    ToastUtils.showToast(R.string.joynote_copy_successful)
                }
                tvName.text =
                    activity.joyNoteDetailInfoModel?.minutesDetail?.deleter?.realName ?: ""
                Glide.with(activity)
                    .load(activity.joyNoteDetailInfoModel?.minutesDetail?.deleter?.imageUrl)
                    .into(ivAvatar)
                if ((activity.joyNoteDetailInfoModel?.minutesDetail?.deleteAt ?: 0L) != 0L)
                    tvTime.text =
                        DateTimeUtils.getFormatDateAndTime(
                            activity.joyNoteDetailInfoModel?.minutesDetail?.deleteAt
                                ?: 0L
                        )
            }
            if (activity.joyNoteDetailInfoModel?.minutesDetail?.permissions?.contains(
                    RESTORE_MINUTES
                ) == true
            ) {
                //有恢复权限时
                val tvRestore: TextView = view.findViewById(R.id.tv_restore)
                tvRestore.visibility = View.VISIBLE
                tvRestore.setOnClickListener {
                    VideoListRepository.getUserRepository()
                        .restoreItem(activity.minutesId,
                            OperateCallback {
                                ToastUtils.showToast(R.string.joy_note_tips_restore_msg)
                                val intent = Intent(VideoItemModel.ACTION_LIST_UPDATE)
                                intent.putExtra(VideoItemModel.OPERATION_TYPE, RESTORE_MINUTES)
                                intent.putExtra(VideoItemModel.MINUTE_ID, activity.minutesId)
                                LocalBroadcastManager.getInstance(activity)
                                    .sendBroadcast(intent)
                                updateStatus(1, activity.minutesId, activity)
                                activity.finish()
                            })
                }
            }
        }
    }
}
