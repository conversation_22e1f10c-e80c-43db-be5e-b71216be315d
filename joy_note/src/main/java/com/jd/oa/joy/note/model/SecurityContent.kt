package com.jd.oa.joy.note.model

import android.os.Parcelable
import androidx.databinding.ObservableBoolean
import com.jd.oa.joy.note.share.SharePermission
import kotlinx.parcelize.Parcelize

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/20
 * @Description:
 */
@Parcelize
data class SecurityContent(
    val title: String, val type: SharePermission, val doShareMode: String
) : Parcelable {
    val isCheck: ObservableBoolean
        get() = ObservableBoolean(doShareMode == type.permission.toString())
}
