package com.jd.oa.joy.note.main;

import static com.jd.oa.joy.note.model.VideoItemModel.AUDIO;
import static com.jd.oa.joy.note.model.VideoItemModel.AUDIO_TRANSLATE;
import static com.jd.oa.joy.note.model.VideoItemModel.GENERATION;
import static com.jd.oa.joy.note.model.VideoItemModel.RECORDING;
import static com.jd.oa.joy.note.model.VideoItemModel.RECYCLING;

import android.annotation.SuppressLint;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joy.note.DateTimeUtils;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.OnclickListener;
import com.jd.oa.joy.note.model.VideoItemModel;
import com.jd.oa.ui.IconFontView;

import java.util.List;

class JoyNoteListAdapter extends RecyclerView.Adapter<JoyNoteListAdapter.ViewHolder> {
    private final int TYPE_EMPTY = 0;
    List<VideoItemModel> items;
    private OnclickListener<VideoItemModel> mMoreBtnListener;
    private OnclickListener<VideoItemModel> mItemListener;
    private final boolean isRecycleBin;


    public JoyNoteListAdapter(List<VideoItemModel> items, boolean isRecycleBin) {
        super();
        this.items = items;
        this.isRecycleBin = isRecycleBin;
    }

    @Override
    public int getItemViewType(int position) {
        if (items.isEmpty()) {
            return TYPE_EMPTY;
        }
        return 1;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void refreshList(List<VideoItemModel> items) {
        this.items = items;
        notifyDataSetChanged();
    }

    void addList(List<VideoItemModel> items) {
        if (items == null || items.size() == 0) {
            return;
        }
        int start = this.items.size();
        this.items.addAll(items);
        notifyItemRangeInserted(start, items.size());
    }

    void updateItem(int position) {
        if (position >= 0 && position < items.size()) {
            notifyItemChanged(position);
        }
    }

    void removeItem(int position) {
        items.remove(position);
        notifyItemRemoved(position);
    }

    void updateItemState(String minuteId, int state) {
        if (minuteId == null || items == null) {
            return;
        }
        int itemNeedChange = -1;
        for (int a = 0; a < items.size(); a++) {
            VideoItemModel videoItemModel = items.get(a);
            if (minuteId.equals(videoItemModel.minutesId)) {
                if (state != videoItemModel.status) {
                    itemNeedChange = a;
                    videoItemModel.status = state;
                }
                break;
            }
        }
        if (itemNeedChange >= 0) {
            notifyItemChanged(itemNeedChange);
        }
    }

    void updateItem(String minuteId, String title) {
        if (minuteId == null || items == null) {
            return;
        }
        int itemNeedChange = -1;
        for (int a = 0; a < items.size(); a++) {
            VideoItemModel videoItemModel = items.get(a);
            if (minuteId.equals(videoItemModel.minutesId)) {
                if (title != null && !title.equals(videoItemModel.title)) {
                    itemNeedChange = a;
                    videoItemModel.title = title;
                }
                break;
            }
        }
        if (itemNeedChange >= 0) {
            notifyItemChanged(itemNeedChange);
        }
    }

    void removeItem(String minuteId) {
        if (minuteId == null || items == null) {
            return;
        }
        int del = -1;
        for (int a = 0; a < items.size(); a++) {
            if (minuteId.equals(items.get(a).minutesId)) {
                del = a;
                break;
            }
        }
        if (del >= 0) {
            items.remove(del);
            notifyItemRemoved(del);
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (viewType == TYPE_EMPTY) {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(
                    isRecycleBin ? R.layout.joy_note_video_list_item_empty_recycle :
                            R.layout.joy_note_video_list_item_empty, viewGroup, false);
            return new ViewHolder(view);
        }
        View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.joy_note_video_list_item, viewGroup, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        try {
            if (holder.cover == null) {
                return;
            }
            if (items == null) {
                return;
            }
            VideoItemModel videoItem;
            try {
                videoItem = items.get(position);
            } catch (Exception e) {
                return;
            }
            if (videoItem == null) {
                return;
            }
            holder.ifView.setVisibility(View.GONE);
            holder.tvState.setVisibility(View.GONE);
            holder.videoTime.setText(DateTimeUtils.millisecondsToTime(videoItem.recordDuration, true));
            holder.videoTitle.setText(videoItem.title);
            holder.videoDate.setText(DateTimeUtils.getFormatDateAndTime(videoItem.receiveTime));
            if (videoItem.userInfo != null) {
                holder.author.setText(videoItem.userInfo.realName);
            }
            if (!TextUtils.isEmpty(videoItem.cover)) {
                holder.cover.setImageURI(Uri.parse(videoItem.cover));
            } else {
                if (videoItem.type == AUDIO || videoItem.type == AUDIO_TRANSLATE) {
                    holder.cover.setImageResource(R.drawable.joy_note_list_audio_def);
                } else {
                    holder.cover.setImageResource(R.drawable.joy_note_list_video_def);
                }
            }
            if (videoItem.isInterview()) {
                holder.cover.setImageResource(R.drawable.joy_note_list_interview_def);
            }
            if (videoItem.status == RECYCLING) {
                holder.cover.setImageResource(R.drawable.joy_note_list_deleted);
            } else if (videoItem.status == GENERATION || videoItem.status == RECORDING) {
                if (videoItem.status == GENERATION) {
                    holder.ifView.setText(R.string.icon_padding_custom);
                    holder.tvState.setText(R.string.joynote_permission_status_generating);
                } else {
                    holder.ifView.setText(R.string.icon_exit);
                    holder.tvState.setText(R.string.joynote_permission_status_recording);
                }
                holder.ifView.setVisibility(View.VISIBLE);
                holder.tvState.setVisibility(View.VISIBLE);
            } else {
                holder.cover.setVisibility(View.VISIBLE);
            }
            if (videoItem.permissions == null || videoItem.permissions.isEmpty()) {
                holder.moreBtn.setVisibility(View.GONE);
            }
            holder.moreBtn.setOnClickListener(v -> {
                if (mMoreBtnListener != null) {
                    mMoreBtnListener.onViewClick(videoItem, holder.getAbsoluteAdapterPosition());
                }
            });
            holder.itemView.setOnClickListener(v -> {
                if (mItemListener != null) {
                    mItemListener.onViewClick(videoItem, holder.getAbsoluteAdapterPosition());
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        if (items.isEmpty()) {
            return 1;
        }
        return items.size();
    }

    public void setOnInnerItemOnClickListener(OnclickListener<VideoItemModel> listener) {
        this.mMoreBtnListener = listener;
    }

    public void setItemOnClickListener(OnclickListener<VideoItemModel> listener) {
        this.mItemListener = listener;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        protected ImageView cover;
        protected TextView videoTime;
        protected TextView videoTitle;
        protected TextView videoDate;
        protected TextView author;
        protected View moreBtn;
        protected IconFontView ifView;
        protected TextView tvState;

        public ViewHolder(View convertView) {
            super(convertView);
            cover = convertView.findViewById(R.id.video_cover);
            videoTime = convertView.findViewById(R.id.video_time);
            videoTitle = convertView.findViewById(R.id.video_title);
            videoDate = convertView.findViewById(R.id.video_date);
            author = convertView.findViewById(R.id.video_author);
            moreBtn = convertView.findViewById(R.id.joy_note_more_btn);
            ifView = convertView.findViewById(R.id.if_view);
            tvState = convertView.findViewById(R.id.tv_state);
        }

    }
}
