package com.jd.oa.joy.note.model;

import com.jd.oa.utils.CollectionUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class JoyNoteAsrData {
    public static final int TRANSLATE_STATE_NONE = 0;
    public static final int TRANSLATE_STATE_TRANSLATING = 1;
    public static final int TRANSLATE_STATE_SUCCESS = 2;
    public static final int TRANSLATE_STATE_FAILURE = 3;

    public String recordId;
    public Integer endMs;
    public List<Sentences> sentences;
    public Integer beginMs;
    public String meetingCode;
    public String meetingId;
    public String paragraphId;
    public User user;
    public String minutesId;

    public int translateState;

    public int getTranslateState(JoyNoteLanguageModel languageModel) {
        if (CollectionUtil.isEmptyOrNull(sentences)) {
            return TRANSLATE_STATE_NONE;
        }
        int state = TRANSLATE_STATE_NONE;
        int count = 0;
        for (int i = 0; i < sentences.size(); i++) {
            Sentences s = sentences.get(i);
            if (s.getTranslateState(languageModel) == TRANSLATE_STATE_FAILURE) {
                state = TRANSLATE_STATE_FAILURE;
                break;
            } else if (s.getTranslateState(languageModel) == TRANSLATE_STATE_TRANSLATING) {
                state = TRANSLATE_STATE_TRANSLATING;
                break;
            } else if (s.getTranslateState(languageModel) == TRANSLATE_STATE_SUCCESS) {
                count++;
            }
        }
        if (count == sentences.size()) return TRANSLATE_STATE_SUCCESS;
        return state;
    }

    public static class User {
        public String ddAppId;
        public boolean edited;
        public Object tenantCode;
        public String userId;
        public String deptFullName;
        public String realName;
        public Object external;
        public String titleName;
        public String imageUrl;
        public String teamId;
        public String userType;
        public String account;
        public Object email;
    }

    public static class Sentences {
        public Integer endMs;
        public Integer beginMs;
        public String sentenceId;
        public String text;
        public Object speakerType;
        public Map<String,String> translatedText;
        public Map<String,Integer> translateState;

        public Integer getTranslateState(JoyNoteLanguageModel languageModel) {
            if (languageModel == null || languageModel.isDefaultLanguage()) return TRANSLATE_STATE_NONE;
            if (translateState == null) {
                translateState = new HashMap<>();
            }
            return translateState.getOrDefault(languageModel.language, TRANSLATE_STATE_NONE);
        }

        public void setTranslateState(JoyNoteLanguageModel languageModel, int state) {
            if (languageModel == null || languageModel.isDefaultLanguage()) return;
            if (translateState == null) {
                translateState = new HashMap<>();
            }
            translateState.put(languageModel.language, state);
        }

        public String getText(JoyNoteLanguageModel languageModel) {
            if (languageModel == null || languageModel.isDefaultLanguage() || translatedText == null) return text;
            String translated =  translatedText.get(languageModel.language);
            if (translated != null) return translated;
            return text;
        }
    }

    public String getAllSentences(JoyNoteLanguageModel languageModel) {
        StringBuilder sb = new StringBuilder();
        for (Sentences s : sentences) {
            if (languageModel != null
                    && !languageModel.isDefaultLanguage()
                    && s.translatedText != null
                    && s.translatedText.get(languageModel.language) != null
            ) {
                String text = s.translatedText.get(languageModel.language);
                sb.append(text);
            } else {
                sb.append(s.text);
            }
        }
        return sb.toString();
    }
}
