package com.jd.oa.joy.note.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;
import java.util.Objects;

public class JoyNoteDetailInfoModel implements Parcelable {

    public MinutesDetail minutesDetail;

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(this.minutesDetail, flags);
    }

    public JoyNoteDetailInfoModel() {
    }

    protected JoyNoteDetailInfoModel(Parcel in) {
        this.minutesDetail = in.readParcelable(MinutesDetail.class.getClassLoader());
    }

    public static final Creator<JoyNoteDetailInfoModel> CREATOR = new Creator<JoyNoteDetailInfoModel>() {
        @Override
        public JoyNoteDetailInfoModel createFromParcel(Parcel source) {
            return new JoyNoteDetailInfoModel(source);
        }

        @Override
        public JoyNoteDetailInfoModel[] newArray(int size) {
            return new JoyNoteDetailInfoModel[size];
        }
    };

    public static class MinutesDetail implements Parcelable {
        public String summary;
        public Owner owner;
        public List<Attachments> attachments;
        public List<String> keywords;
        public ScheduleInfo scheduleInfo;
        public String recordDuration;
        public List<String> permissions;
        //        参与人，最多只返回10个：
        public List<JoyNoteMember> members;
        public String videoFileUrl;
        // 0无效数据 1正常数据 2生成中的数据 3录制中的数据 4进入回收站的数据 5录制暂停的数据 6录制未开始的数据
        public String status;
        public String title;
        public String bizType;
        //慧记类型 1视频 2音频
        public int type;
        public long createAt;
        public String minutesId;
        public int memberNums;
        //删除时间
        public long deleteAt;
        public JoyNoteAsrData.User deleter;
        // 是否展示「与我相关」Tab
        public boolean speechRelatedToMe = false;

        public boolean isInterview() {
            return Objects.equals(bizType, VideoItemModel.BizType.OFFLINE_INTERVIEW.value) || Objects.equals(bizType, VideoItemModel.BizType.ONLINE_INTERVIEW.value);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.summary);
            dest.writeParcelable(this.owner, flags);
            dest.writeTypedList(this.attachments);
            dest.writeStringList(this.keywords);
            dest.writeParcelable(this.scheduleInfo, flags);
            dest.writeString(this.recordDuration);
            dest.writeStringList(this.permissions);
            dest.writeTypedList(this.members);
            dest.writeString(this.videoFileUrl);
            dest.writeString(this.status);
            dest.writeString(this.title);
            dest.writeString(this.bizType);
            dest.writeInt(this.type);
            dest.writeLong(this.createAt);
            dest.writeString(this.minutesId);
            dest.writeInt(this.memberNums);
            dest.writeLong(this.deleteAt);
        }

        public MinutesDetail() {
        }

        protected MinutesDetail(Parcel in) {
            this.summary = in.readString();
            this.owner = in.readParcelable(Owner.class.getClassLoader());
            this.attachments = in.createTypedArrayList(Attachments.CREATOR);
            this.keywords = in.createStringArrayList();
            this.scheduleInfo = in.readParcelable(ScheduleInfo.class.getClassLoader());
            this.recordDuration = in.readString();
            this.permissions = in.createStringArrayList();
            this.members = in.createTypedArrayList(JoyNoteMember.CREATOR);
            this.videoFileUrl = in.readString();
            this.status = in.readString();
            this.title = in.readString();
            this.bizType = in.readString();
            this.type = in.readInt();
            this.createAt = in.readLong();
            this.minutesId = in.readString();
            this.memberNums = in.readInt();
            this.deleteAt = in.readLong();
        }

        public static final Creator<MinutesDetail> CREATOR = new Creator<MinutesDetail>() {
            @Override
            public MinutesDetail createFromParcel(Parcel source) {
                return new MinutesDetail(source);
            }

            @Override
            public MinutesDetail[] newArray(int size) {
                return new MinutesDetail[size];
            }
        };

        public static class ScheduleInfo implements Parcelable {
            public String subject;// 会议标题
            public long start;//会议开始时间
            public long end;//会议截止时间
            public String calendarId;//日历id
            public String scheduleId;//日程id


            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.subject);
                dest.writeLong(this.start);
                dest.writeLong(this.end);
                dest.writeString(this.calendarId);
                dest.writeString(this.scheduleId);
            }

            public ScheduleInfo() {
            }

            protected ScheduleInfo(Parcel in) {
                this.subject = in.readString();
                this.start = in.readLong();
                this.end = in.readLong();
                this.calendarId = in.readString();
                this.scheduleId = in.readString();
            }

            public static final Creator<ScheduleInfo> CREATOR = new Creator<ScheduleInfo>() {
                @Override
                public ScheduleInfo createFromParcel(Parcel source) {
                    return new ScheduleInfo(source);
                }

                @Override
                public ScheduleInfo[] newArray(int size) {
                    return new ScheduleInfo[size];
                }
            };
        }

        public static class Owner implements Parcelable {
            public String ddAppId;
            public String realName;
            public Object external;
            public String imageUrl;
            public String teamId;
            public String userType;
            public String tenantCode;
            public String userId;
            public String account;
            public String email;


            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.ddAppId);
                dest.writeString(this.realName);
                dest.writeString(this.imageUrl);
                dest.writeString(this.teamId);
                dest.writeString(this.userType);
                dest.writeString(this.tenantCode);
                dest.writeString(this.userId);
                dest.writeString(this.account);
                dest.writeString(this.email);
            }

            public Owner() {
            }

            protected Owner(Parcel in) {
                this.ddAppId = in.readString();
                this.realName = in.readString();
                this.imageUrl = in.readString();
                this.teamId = in.readString();
                this.userType = in.readString();
                this.tenantCode = in.readString();
                this.userId = in.readString();
                this.account = in.readString();
                this.email = in.readString();
            }

            public static final Creator<Owner> CREATOR = new Creator<Owner>() {
                @Override
                public Owner createFromParcel(Parcel source) {
                    return new Owner(source);
                }

                @Override
                public Owner[] newArray(int size) {
                    return new Owner[size];
                }
            };
        }

        public static class Attachments implements Parcelable {
            public String fromSys;
            public String fileName;
            public String fileUrl;
            public String fileType;
            public String minutesId;


            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.fromSys);
                dest.writeString(this.fileName);
                dest.writeString(this.fileUrl);
                dest.writeString(this.fileType);
                dest.writeString(this.minutesId);
            }

            public Attachments() {
            }

            protected Attachments(Parcel in) {
                this.fromSys = in.readString();
                this.fileName = in.readString();
                this.fileUrl = in.readString();
                this.fileType = in.readString();
                this.minutesId = in.readString();
            }

            public static final Creator<Attachments> CREATOR = new Creator<Attachments>() {
                @Override
                public Attachments createFromParcel(Parcel source) {
                    return new Attachments(source);
                }

                @Override
                public Attachments[] newArray(int size) {
                    return new Attachments[size];
                }
            };
        }
    }
}
