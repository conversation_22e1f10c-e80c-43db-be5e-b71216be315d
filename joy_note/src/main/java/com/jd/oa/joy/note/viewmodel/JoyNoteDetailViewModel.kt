package com.jd.oa.joy.note.viewmodel;

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.AppBase
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.model.JoyNoteLanguageModel
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo
import com.jd.oa.melib.mvp.LoadDataCallback

class JoyNoteDetailViewModel: ViewModel() {

    private val _languages = MutableLiveData<List<JoyNoteLanguageModel>?>()
    val languageList: List<JoyNoteLanguageModel>? get() {
        if (_languages.value == null) return null
        return mutableListOf<JoyNoteLanguageModel>().apply {
            add(JoyNoteLanguageModel(
                    AppBase.getAppContext().getString(R.string.joynote_translate_origin_text),
                null
                ).apply { isDefaultLanguage = true }
            )
            addAll(_languages.value!!)
        }
    }

    private val _selectedLanguage = MutableLiveData<JoyNoteLanguageModel?>()
    val selectedLanguage get() = _selectedLanguage

    fun getLanguages() {
        JoyNoteDetailRepo.getRepo().getTranslateLanguages(object :LoadDataCallback<List<JoyNoteLanguageModel>>{
            override fun onDataLoaded(data: List<JoyNoteLanguageModel>?) {
                if (!data.isNullOrEmpty()) {
                    _languages.postValue(data)
                }
            }

            override fun onDataNotAvailable(error: String?, code: Int) {
            }
        }, JoyNoteDetailRepo.JOY_NOTE_LANGUAGE)
    }

    fun updateSelectedLanguage(languageModel: JoyNoteLanguageModel) {
        _selectedLanguage.postValue(languageModel)
    }
}
