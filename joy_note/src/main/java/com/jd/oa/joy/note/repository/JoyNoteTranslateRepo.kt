package com.jd.oa.joy.note.repository

import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.joy.note.model.ContentText
import com.jd.oa.joy.note.model.JoyNoteAsrData
import com.jd.oa.joy.note.model.TranslateContent
import com.jd.oa.network.httpmanager.ColorGatewayNetEnvironment
import com.jd.oa.network.sse.SSEManager
import com.jd.oa.network.sse.SSEManager.SSECallback
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import okhttp3.sse.EventSource
import java.util.UUID

class JoyNoteTranslateRepo {

    companion object {
        const val TAG = "JoyNoteTranslateRepo"
        const val TRANSLATE_BASE_HOST_PROD = "https://meeting-api-prod.jd.com"
        const val TRANSLATE_BASE_HOST_PRE = "https://meeting-api-pre.jd.com"
        const val TRANSLATE_URL_PATH = "/chat/stream/translate/"
    }

    fun translate(language: String, list: List<JoyNoteAsrData>) = callbackFlow<TranslateState>{
        val params: MutableMap<String, Any> = HashMap()
        params["targetLanguage"] = language
        params["sourceType"] = "MULTIPLE_SENTENCE"
        params["translateContents"] = convertToTranslateContent(list)

        val headers = hashMapOf("x-system" to "JoyMinutes")

        val host = if (ColorGatewayNetEnvironment.getCurrentEnv().isPre) {
            TRANSLATE_BASE_HOST_PRE
        } else {
            TRANSLATE_BASE_HOST_PROD
        }
        val streamId = UUID.randomUUID().toString()
        val url = "$host$TRANSLATE_URL_PATH$streamId"

        SSEManager.post(url, params, headers, object : SSECallback {
            override fun onEvent(eventSource: EventSource, event: String?, data: String) {
                trySend(TranslateState.OnEvent(event, data))
            }

            override fun onError(throwable: Throwable) {
                MELogUtil.localI(TAG, "error:" + throwable.message)
                close(throwable)
            }

            override fun onConnected() {
                MELogUtil.localI(TAG, "onConnected")
                trySend(TranslateState.OnConnected)
            }

            override fun onDisconnected() {
                MELogUtil.localI(TAG, "onDisconnected")
                trySend(TranslateState.OnDisconnected)
                //cancel("onDisconnected")
            }
        })

        awaitClose {
            SSEManager.disconnect(url)
        }
    }

    private fun convertToTranslateContent(asrData:List<JoyNoteAsrData>): List<TranslateContent>{
        return asrData.map { asrData ->
            val content = TranslateContent().apply {
                id = asrData.paragraphId
                textList = asrData.sentences.map { sentence ->
                    ContentText().apply {
                        id = sentence.sentenceId
                        text = sentence.text
                    }
                }
            }
            return@map content
        }
    }
}

sealed class TranslateState {
    class OnEvent(val event: String?, val data: String) : TranslateState()
    class OnError(val error: Throwable) : TranslateState()
    object OnConnected : TranslateState()
    object OnDisconnected: TranslateState()
}