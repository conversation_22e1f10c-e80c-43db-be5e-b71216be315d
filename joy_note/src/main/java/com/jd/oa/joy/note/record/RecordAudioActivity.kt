package com.jd.oa.joy.note.record

import android.os.Bundle
import com.chenenyu.router.annotation.Route
import com.jd.oa.joy.note.model.VideoItemModel.AUDIO
import com.jd.oa.joy.note.notefloat.NoteFloatManager
import com.jd.oa.router.DeepLink

@Route(DeepLink.JOY_NOTE_CREATE)
class RecordAudioActivity : BaseAudioActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        NoteFloatManager.hideFloatingView(this)
        loadAsrTranslateWidget(false)
        AudioRecordCurrent.type = AUDIO
    }

    override fun showFloatWindow() {
        NoteFloatManager.getInstance().showFloatingView(this)
    }
}