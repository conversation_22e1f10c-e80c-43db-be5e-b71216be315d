package com.jd.oa.joy.note.model;

import androidx.annotation.Keep;

@Keep
public class TranslatedText {

    private String id;
    private String transText;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTransText() {
        return transText;
    }

    public void setTransText(String transText) {
        this.transText = transText;
    }
}