package com.jd.oa.joy.note.compunent;

import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.jd.oa.joy.note.R;

import java.util.function.Function;

public class JoyNoteTab extends TabLayout {
    private TabLayout.OnTabSelectedListener tabSelectedListener;

    public JoyNoteTab(@NonNull Context context) {
        super(context);
    }

    public JoyNoteTab(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public JoyNoteTab(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void initView(@NonNull ViewPager2 viewPager2, @NonNull Function<Integer, String> getTitle) {
        new TabLayoutMediator(this, viewPager2,
                (tab, position) -> {
                    tab.setCustomView(R.layout.joy_note_custom_tab_item);
                    if (tab.getCustomView() != null) {
                        TextView tv = tab.getCustomView().findViewById(R.id.custom_tab_item_text);
                        tv.setText(getTitle.apply(position));
                        if (position == 0) {
                            tv.setTextColor(0xff232930);
                            tv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                        }
                    }
                }
        ).attach();
        addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab.getCustomView() == null) {
                    return;
                }
                TextView tv = tab.getCustomView().findViewById(R.id.custom_tab_item_text);
                tv.setSelected(true);
                tv.setTextColor(0xff232930);
                tv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                tv.invalidate();
                if (tabSelectedListener != null) {
                    tabSelectedListener.onTabSelected(tab);
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                if (tab.getCustomView() == null) {
                    return;
                }
                TextView tv = tab.getCustomView().findViewById(R.id.custom_tab_item_text);
                tv.setSelected(false);
                tv.setTextColor(0xff62656D);
                tv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                tv.invalidate();
                if (tabSelectedListener != null) {
                    tabSelectedListener.onTabUnselected(tab);
                }
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
            }
        });
    }

    public void addOnSelectedListener(@NonNull TabLayout.OnTabSelectedListener tabSelectedListener) {
        this.tabSelectedListener = tabSelectedListener;
    }
}
