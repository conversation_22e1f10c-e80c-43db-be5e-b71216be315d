package com.jd.oa.joy.note.permission

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joy.note.databinding.ItemAccessScopeLayoutBinding
import com.jd.oa.joy.note.model.ScopeContent

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/19
 */
class ScopeAdapter(
    private val data: MutableList<ScopeContent>,
    val selectedScope: (ScopeContent) -> Unit
) :
    RecyclerView.Adapter<ScopeAdapter.AccessScopeViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AccessScopeViewHolder {
        val binding = ItemAccessScopeLayoutBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AccessScopeViewHolder(binding)
    }

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: AccessScopeViewHolder, position: Int) {
        holder.bind(data[position])
        holder.binding.divide.isVisible = position != data.lastIndex
        holder.binding.root.setOnClickListener {
            data[position].isCheck.set(!data[position].isCheck.get())
            val lastCheckPosition = data.indexOfFirst { it.isCheck.get() }
            if (lastCheckPosition in 0 until data.size) {
                data[lastCheckPosition].isCheck.set(!data[lastCheckPosition].isCheck.get())
            }
            selectedScope(data[position])
        }
    }

    inner class AccessScopeViewHolder(val binding: ItemAccessScopeLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(scope: ScopeContent) {
            binding.scope = scope
            binding.lifecycleOwner = itemView.context as? LifecycleOwner
            binding.executePendingBindings()
        }
    }
}