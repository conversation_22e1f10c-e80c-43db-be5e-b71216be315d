package com.jd.oa.joy.note.translate

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.IBinder
import android.util.Log
import android.view.WindowManager
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.asr.AsrService.Companion.ACTION_RECORD_TEXT_CHANGE
import com.jd.oa.asr.AsrService.Companion.ARG_ASR_TRANSLATE_TEXT
import com.jd.oa.asr.Logger
import com.jd.oa.asr.websocket.model.TextMsg
import com.jd.oa.joy.note.BaseFloatingService

/**
 * @Author: hepiao3
 * @CreateTime: 2025/5/13
 * @Description:
 */
class FloatingTranslateService : BaseFloatingService() {
    private lateinit var floatingView: FloatingTranslateView
    private lateinit var windowManager: WindowManager
    private val translateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val textMsg = intent?.getParcelableExtra(ARG_ASR_TRANSLATE_TEXT) as TextMsg?
            textMsg?.let {
                floatingView.refreshTranslateList(
                    it.payload?.dynamicResult?.translateResult ?: "",
                    it.payload?.stableResult?.translateResult ?: ""
                )
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Logger.getDefault().info(javaClass.simpleName, "onCreate")
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        floatingView = FloatingTranslateView(this)
        val filter = IntentFilter().apply {
            addAction(ACTION_RECORD_TEXT_CHANGE)
        }
        LocalBroadcastManager.getInstance(this).registerReceiver(translateReceiver, filter)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.getStringExtra(ARG_ACTION)
        when (action) {
            ACTION_SHOW_DEFAULT -> {
                showFloatingView()
            }

            ACTION_HIDE_DEFAULT -> {
                hideFloatingView()
            }
        }
        return super.onStartCommand(intent, flags, startId)
    }

    override fun showFloatingView() {
        if (!::floatingView.isInitialized) {
            floatingView = FloatingTranslateView(this)
        }
        floatingView.show(windowManager)
    }

    override fun hideFloatingView() {
        if (::floatingView.isInitialized) {
            floatingView.hide()
        }
    }

    override fun resetPosition() {
        if (!::floatingView.isInitialized) {
            floatingView = FloatingTranslateView(this)
        }
        floatingView.centerHorizontallyWithAnimation()
    }

    override fun startPlay() {
        if (!::floatingView.isInitialized) {
            floatingView = FloatingTranslateView(this)
        }
        floatingView.start()
    }

    override fun stopPlay() {
        if (!::floatingView.isInitialized) {
            floatingView = FloatingTranslateView(this)
        }
        floatingView.stop()
    }

    override fun notifyException(errorType: String?, errorMsg: String?) {
        if (!::floatingView.isInitialized) {
            floatingView = FloatingTranslateView(this)
        }
        floatingView.notifyException(errorType, errorMsg)
    }

    override val isGlobalWork: Boolean
        get() = true

    override fun onDestroy() {
        Logger.getDefault().info(javaClass.simpleName, "onDestroy")
        floatingView.hide()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(translateReceiver)
        super.onDestroy()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    companion object {
        const val ARG_ACTION = "arg.action_note_float"
        const val ACTION_SHOW_DEFAULT = "action_show_note_float"
        const val ACTION_HIDE_DEFAULT = "action_hide_note_float"
    }
}