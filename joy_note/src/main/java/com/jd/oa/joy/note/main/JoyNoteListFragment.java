package com.jd.oa.joy.note.main;

import static android.app.Activity.RESULT_OK;
import static com.jd.oa.joy.note.JoyNoteTools.MINUTES_ID;
import static com.jd.oa.joy.note.JoyNoteTools.NEW_TITLE;
import static com.jd.oa.joy.note.JoyNoteTools.POSITION;
import static com.jd.oa.joy.note.JoyNoteTools.RESULT_REFRESH;
import static com.jd.oa.joy.note.JoyNoteTools.copyShareUrl;
import static com.jd.oa.joy.note.JoyNoteTools.getAppID;
import static com.jd.oa.joy.note.JoyNoteTools.selectUserFromIm;
import static com.jd.oa.joy.note.main.JoyNoteMainFragment.FILTER_NONE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.CREATE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.FRONT_PAGE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.LIST_TYPE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECEIVE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECENT;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECYCLE_BIN;
import static com.jd.oa.joy.note.model.VideoItemModel.ACTION_LIST_UPDATE;
import static com.jd.oa.joy.note.model.VideoItemModel.AUDIO;
import static com.jd.oa.joy.note.model.VideoItemModel.AUDIO_TRANSLATE;
import static com.jd.oa.joy.note.model.VideoItemModel.DELETE_MINUTES;
import static com.jd.oa.joy.note.model.VideoItemModel.DESTROY_MINUTES;
import static com.jd.oa.joy.note.model.VideoItemModel.EDIT;
import static com.jd.oa.joy.note.model.VideoItemModel.ERROR;
import static com.jd.oa.joy.note.model.VideoItemModel.FINISH;
import static com.jd.oa.joy.note.model.VideoItemModel.GENERATION;
import static com.jd.oa.joy.note.model.VideoItemModel.MINUTE_ID;
import static com.jd.oa.joy.note.model.VideoItemModel.NEW_ADD;
import static com.jd.oa.joy.note.model.VideoItemModel.NEW_TITLE_NAME;
import static com.jd.oa.joy.note.model.VideoItemModel.OPERATION_TYPE;
import static com.jd.oa.joy.note.model.VideoItemModel.RECEIVED;
import static com.jd.oa.joy.note.model.VideoItemModel.RECENT_TYPE;
import static com.jd.oa.joy.note.model.VideoItemModel.RECORDING;
import static com.jd.oa.joy.note.model.VideoItemModel.RECYCLING;
import static com.jd.oa.joy.note.model.VideoItemModel.REMOVE_MINUTES;
import static com.jd.oa.joy.note.model.VideoItemModel.RENAME_MINUTES;
import static com.jd.oa.joy.note.model.VideoItemModel.RESTORE_MINUTES;
import static com.jd.oa.joy.note.model.VideoItemModel.SHARE;
import static com.jd.oa.joy.note.share.JoyNoteShareActivity.PERMISSIONS;
import static com.jd.oa.pulltorefresh.PullToRefreshLayout.SCROLL_DOWN;
import static com.jd.oa.pulltorefresh.PullToRefreshLayout.SCROLL_UP;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.JDMAConstants;
import com.jd.oa.JDMAPages;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.joy.note.JoyNoteShareUtil;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.BottomSheetItem;
import com.jd.oa.joy.note.compunent.JoyNoteBottomSheet;
import com.jd.oa.joy.note.compunent.JoyNoteEditDialog;
import com.jd.oa.joy.note.compunent.JoyNoteSelectBtn;
import com.jd.oa.joy.note.detail.JoyNoteDetailActivity;
import com.jd.oa.joy.note.model.JoyNoteMember;
import com.jd.oa.joy.note.model.VideoItemModel;
import com.jd.oa.joy.note.model.VideoListViewModel;
import com.jd.oa.joy.note.record.AudioRecordCurrent;
import com.jd.oa.joy.note.record.AudioRecordStatus;
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo;
import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.pulltorefresh.PullToRefreshLayout;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

public class JoyNoteListFragment extends Fragment {
    static final int FILTER_CREATOR = 2;
    static final int FILTER_SENDER = 3;
    private static final String PAGE_NUM = "pageNum";
    private static final String PAGE_SIZE = "pageSize";
    private static final String TYPE = "type";
    private static final String DD_APP_ID = "ddAppId";
    private static final String ACCOUNT = "account";
    private static final String USER_LIST = "userList";
    private static final int PAGE = 20;
    private PullToRefreshLayout mExperienceRefreshLayout;
    private JoyNoteListAdapter adapter;
    private VideoListViewModel videoListViewModel;
    private JoyNoteMainFragment joyNoteMainFragment;
    private JoyNoteSelectBtn mineBtn, creatorBtn, senderBtn;
    private int currentPage = 0;
    private boolean isLoading = false;
    private String type = FRONT_PAGE;
    private int filterType = FILTER_NONE;
    private List<JoyNoteMember> list = new ArrayList<>();
    private final ActivityResultLauncher<Intent> launcher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
        if (result.getResultCode() == RESULT_OK) {
            try {
                Intent newIntent = result.getData();
                if (newIntent != null && newIntent.hasExtra(NEW_TITLE)) {
                    int p = newIntent.getIntExtra(POSITION, 0);
                    VideoItemModel videoItemModel = adapter.items.get(p);
                    String id = newIntent.getStringExtra(MINUTES_ID);
                    if (videoItemModel != null && videoItemModel.minutesId.equals(id)) {
                        videoItemModel.title = newIntent.getStringExtra(NEW_TITLE);
                        adapter.updateItem(p);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (result.getResultCode() == RESULT_REFRESH) {
            refresh();
        }
    });
    private BroadcastReceiver receiver;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        initViewModel();
        registerItemChanger();
        return inflater.inflate(R.layout.joy_note_video_list, container, false);
    }

    private void registerItemChanger() {
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        receiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent == null || adapter == null) {
                    return;
                }
                String operationType = intent.getStringExtra(OPERATION_TYPE);
                String minuteId = intent.getStringExtra(MINUTE_ID);
                if (minuteId == null || operationType == null) {
                    return;
                }
                switch (operationType) {
                    case DELETE_MINUTES:
                        if (!Objects.equals(type, RECYCLE_BIN)) {
                            adapter.updateItemState(minuteId, RECYCLING);
                        }
                        break;
                    case RESTORE_MINUTES:
                        if (Objects.equals(type, RECYCLE_BIN)) {
                            adapter.removeItem(minuteId);
                        } else {
                            adapter.updateItemState(minuteId, FINISH);
                        }
                        break;
                    case RENAME_MINUTES:
                        String title = intent.getStringExtra(NEW_TITLE_NAME);
                        if (title != null) {
                            adapter.updateItem(minuteId, title);
                        }
                        break;
                }
            }
        };
        LocalBroadcastManager.getInstance(activity).registerReceiver(receiver, new IntentFilter(ACTION_LIST_UPDATE));

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        RecyclerView recyclerView = view.findViewById(R.id.video_list_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(view.getContext()) {
            @Override
            public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
                try {
                    super.onLayoutChildren(recycler, state);
                } catch (IndexOutOfBoundsException e) {
                    e.printStackTrace();
                    MELogUtil.localE("NOTE", e.getMessage(), e);
                }
            }
        });
        if (getActivity() != null && getActivity().getIntent() != null) {
            if (getActivity().getIntent().hasExtra(LIST_TYPE)) {
                type = getActivity().getIntent().getStringExtra(LIST_TYPE);
            }
        }
        if (getArguments() != null && getArguments().containsKey(LIST_TYPE)) {
            type = getArguments().getString(LIST_TYPE, FRONT_PAGE);
        }
        View filterLayout = view.findViewById(R.id.joy_note_filter_layout);
        senderBtn = view.findViewById(R.id.joy_note_sender_btn);
        mineBtn = view.findViewById(R.id.joy_note_mine_btn);
        creatorBtn = view.findViewById(R.id.joy_note_creator_btn);
        if (CREATE.equals(type) || RECYCLE_BIN.equals(type)) {
            filterLayout.setVisibility(View.GONE);
        }
        if (RECENT.equals(type)) {
            senderBtn.setVisibility(View.GONE);
        }
        if (RECEIVE.equals(type)) {
            creatorBtn.setVisibility(View.GONE);
            mineBtn.setVisibility(View.GONE);
        }
        View resetBtn = view.findViewById(R.id.reset_btn);
        mineBtn.setOnClickListener(v -> {
            if (isLoading()) {
                return;
            }
            mineBtn.setUserList(null);
            creatorBtn.reset();
            senderBtn.reset();
            resetBtn.setVisibility(View.VISIBLE);
            ArrayList<JoyNoteMember> listMine = new ArrayList<>();
            JoyNoteMember joyNoteMember = new JoyNoteMember();
            joyNoteMember.account = PreferenceManager.UserInfo.getUserName();
            joyNoteMember.ddAppId = getAppID();
            joyNoteMember.imageUrl = PreferenceManager.UserInfo.getUserCover();
            listMine.add(joyNoteMember);
            setFilter(listMine, FILTER_CREATOR);
            if (FRONT_PAGE.equals(type)) {
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List, JDMAConstants.Mobile_Event_Minute_Recentaddedcreated, new HashMap<>());
            }
        });
        creatorBtn.setOnClickListener(v -> {
            if (isLoading()) {
                return;
            }
            selectUserFromIm(creatorBtn.getUserList(), (listUsers, position) -> {
                list = listUsers;
                creatorBtn.setUserList(listUsers);
                mineBtn.reset();
                senderBtn.reset();
                resetBtn.setVisibility(View.VISIBLE);
                setFilter(creatorBtn.getUserList(), FILTER_CREATOR);
            });
            if (FRONT_PAGE.equals(type)) {
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List, JDMAConstants.Mobile_Event_Minute_Recentaddedcreator, new HashMap<>());
            }
        });
        senderBtn.setOnClickListener(v -> {
            if (isLoading()) {
                return;
            }
            selectUserFromIm(senderBtn.getUserList(), (listUsers, position) -> {
                list = listUsers;
                senderBtn.setUserList(listUsers);
                creatorBtn.reset();
                mineBtn.reset();
                resetBtn.setVisibility(View.VISIBLE);
                setFilter(senderBtn.getUserList(), FILTER_SENDER);
            });
            if (FRONT_PAGE.equals(type)) {
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List, JDMAConstants.Mobile_Event_Minute_Recentaddedsender, new HashMap<>());
            }
        });
        resetBtn.setOnClickListener(v -> {
            if (isLoading()) {
                return;
            }
            mineBtn.reset();
            creatorBtn.reset();
            senderBtn.reset();
            resetBtn.setVisibility(View.GONE);
            setFilter(null, FILTER_NONE);
        });

        adapter = new JoyNoteListAdapter(new ArrayList<>(), Objects.equals(type, RECYCLE_BIN));
        adapter.setItemOnClickListener(this::clickNoteItem);
        recyclerView.setAdapter(adapter);
        adapter.setOnInnerItemOnClickListener((videoItemModel, position) -> onMoreBtnClick(getActivity(), videoItemModel, position));
        recyclerView.post(this::refresh);
        mExperienceRefreshLayout = view.findViewById(R.id.joy_note_main_refresh_layout);
        mExperienceRefreshLayout.setCanRefresh(true);
        mExperienceRefreshLayout.setCanLoadMore(true);
        mExperienceRefreshLayout.setRefreshListener(new PullToRefreshLayout.BaseRefreshListener() {
            @Override
            public void refresh() {
                getListDate(false);
            }

            @Override
            public void loadMore() {
                getListDate(true);
            }
        });
        mExperienceRefreshLayout.setOnChildScrollCallback((parent, child, direction) -> {
            if (child instanceof RecyclerView) {
                RecyclerView videoList = (RecyclerView) child;
                if (direction == SCROLL_UP) {
                    if (joyNoteMainFragment != null && joyNoteMainFragment.isAppBarFold()) {
                        return true;
                    }
                    return videoList.canScrollVertically(SCROLL_DOWN);
                } else {
                    if (adapter == null || adapter.items == null || adapter.items.size() < PAGE) {
                        return true;
                    }
                    if (joyNoteMainFragment != null && !joyNoteMainFragment.isAppBarFold()) {
                        return true;
                    }
                    return videoList.canScrollVertically(SCROLL_UP);
                }
            }
            return true;
        });
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            FragmentManager fragmentManager = getActivity().getSupportFragmentManager();
            List<Fragment> list = fragmentManager.getFragments();
            if (list.size() > 0) {
                for (int a = list.size() - 1; a >= 0; a--) {
                    if (list.get(a) instanceof JoyNoteMainFragment) {
                        joyNoteMainFragment = (JoyNoteMainFragment) list.get(a);
                    }
                }
            }
        }
        videoListViewModel = new ViewModelProvider(this).get(VideoListViewModel.class);
        videoListViewModel.getVideoListLiveData().observe(getViewLifecycleOwner(), mainList -> {
            if (mainList != null && mainList.isSuccess()) {
                List<VideoItemModel> list = mainList.getItemModelList();
                if (mainList.isLoadMore() && list != null && list.size() > 0) {
                    currentPage++;
                    adapter.addList(list);
                }
                if (!mainList.isLoadMore() && list != null) {
                    currentPage = 0;
                    adapter.refreshList(list);
                }
            } else if (AppBase.getTopActivity() != null && AppBase.getTopActivity() instanceof JoyNoteSingleListActivity) {
                ToastUtils.showToast(getString(R.string.joynote_detail_refresh_tips));
            }
            finishRefresh();
        });
    }

    private void clickNoteItem(VideoItemModel v, int position) {
        if (v == null || v.minutesId == null || (v.status == GENERATION &&
                (v.type != AUDIO && v.type != AUDIO_TRANSLATE))) {
            ToastUtils.showToast(getString(R.string.joy_note_generating));
            return;
        }
        if (getContext() != null && v.status == RECORDING && (v.type == AUDIO || v.type == AUDIO_TRANSLATE)) {
            AudioRecordStatus status = AudioRecordCurrent.INSTANCE.getCurrentStatus();
            String url = v.type == AUDIO ? DeepLink.JOY_NOTE_CREATE : DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE;
            if (status == AudioRecordStatus.RECORDING || status == AudioRecordStatus.PAUSE) {
                DeepLinkTools.goJoyNotePage(getContext(), v.title, v.minutesId, url);
            } else {
                Intent intent = DeepLinkTools.getJoyNoteIntent(getContext(), v.title, v.minutesId, url);
                if (intent != null) {
                    intent.putExtra(MINUTES_ID, v.minutesId);
                    intent.putExtra(POSITION, position);
                    launch(intent);
                }
            }
            return;
        }
        Intent intent = new Intent(getActivity(), JoyNoteDetailActivity.class);
        intent.putExtra(MINUTES_ID, v.minutesId);
        intent.putExtra(POSITION, position);
        launch(intent);
    }

    private void finishRefresh() {
        new Handler(Looper.getMainLooper()).post(() -> {
            isLoading = false;
            if (mExperienceRefreshLayout == null) {
                return;
            }
            mExperienceRefreshLayout.finishRefresh();
            mExperienceRefreshLayout.finishLoadMore();
        });
    }

    private void initViewModel() {
        ViewModelProvider viewModelProvider = new ViewModelProvider(this);
        videoListViewModel = viewModelProvider.get(VideoListViewModel.class);
    }

    private void getListDate(boolean loadMore) {
        isLoading = true;
        JSONObject param = new JSONObject();
        try {
            int pageParam = 0;
            if (loadMore) {
                pageParam = currentPage + 1;
            }
            param.put(PAGE_NUM, pageParam + 1);
            param.put(PAGE_SIZE, PAGE);
            if (FILTER_NONE != filterType) {
                param.put(TYPE, filterType);
            }
            JSONArray userList = new JSONArray();
            for (int a = 0; list != null && a < list.size(); a++) {
                JSONObject item = new JSONObject();
                item.put(DD_APP_ID, list.get(a).ddAppId);
                item.put(ACCOUNT, list.get(a).account);
                userList.put(item);
            }
            param.put(USER_LIST, userList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        videoListViewModel.getVideoList(loadMore, type, param);
    }

    private void setFilter(List<JoyNoteMember> list, int filterType) {
        if (list == null) {
            list = new ArrayList<>();
        }
        this.list = list;
        this.filterType = filterType;
        refresh();
    }

    public void refresh() {
        try {
            if (!isLoading && mExperienceRefreshLayout != null) {
                mExperienceRefreshLayout.autoRefresh();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showEditDialog(@NonNull Activity activity, @NonNull VideoItemModel videoItemModel, int position) {
        JoyNoteEditDialog joyNoteEditDialog = new JoyNoteEditDialog(activity, videoItemModel.title);
        joyNoteEditDialog.setOnClickListener(new JoyNoteEditDialog.OnClickListener() {
            @Override
            public void onClickOk(String inputText) {
                if (TextUtils.isEmpty(inputText)) {
                    ToastUtils.showToast(getString(R.string.joy_note_edit_input_null));
                    return;
                }
                JoyNoteDetailRepo.getRepo().updateTitle(videoItemModel.minutesId, inputText, new LoadDataCallback<Object>() {
                    @Override
                    public void onDataLoaded(Object o) {
                        Intent intent = new Intent();
                        intent.putExtra(NEW_TITLE, inputText);
                        intent.putExtra(MINUTES_ID, videoItemModel.minutesId);
                        intent.putExtra(POSITION, position);
                        activity.setResult(Activity.RESULT_OK, intent);
                        videoItemModel.title = inputText;
                        adapter.updateItem(position);
                        ToastUtils.showToast(getString(R.string.joynote_modify_success));
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        ToastUtils.showToast(getString(R.string.joynote_modify_fail));
                    }
                });
            }

            @Override
            public void onClickCancel() {

            }
        });
        joyNoteEditDialog.show();
    }

    private void onMoreBtnClick(Activity activity, VideoItemModel v, int position) {
        if (activity == null) {
            return;
        }
        if (v == null || v.minutesId == null || (v.status == GENERATION && v.type != AUDIO)) {
            ToastUtils.showToast(getString(R.string.joy_note_generating));
            return;
        }
        List<String> permissions = v.permissions;
        List<BottomSheetItem> items = new ArrayList<>();

        if (permissions != null && !permissions.isEmpty() && permissions.contains(SHARE)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_share), R.string.icon_general_share1));
        }
        if (permissions != null && !permissions.isEmpty() && permissions.contains(SHARE)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_permission_manager), R.string.icon_sky_basic));
        }
        if (!Objects.equals(type, RECYCLE_BIN)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_copy_link), R.string.icon_edit_copy));
        }
        if (permissions != null && !permissions.isEmpty() && permissions.contains(EDIT)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_rename), R.string.icon_hj_edit));
        }
        if (permissions != null && !permissions.isEmpty() && permissions.contains(REMOVE_MINUTES)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_remove), R.string.icon_edit_clearformatting));
        }
        if (permissions != null && !permissions.isEmpty() && permissions.contains(RESTORE_MINUTES)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_restore), R.string.icon_direction_menufold));
        }
        if (v.status != RECYCLING && v.status != RECORDING && permissions != null && !permissions.isEmpty() && permissions.contains(DELETE_MINUTES)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_delete), R.string.icon_edit_delete));
        }
        if (permissions != null && !permissions.isEmpty() && permissions.contains(DESTROY_MINUTES)) {
            items.add(new BottomSheetItem(getString(R.string.joynote_destroy), R.string.icon_edit_delete));
        }
        JoyNoteBottomSheet joyNoteBottomSheet = new JoyNoteBottomSheet(activity, items);
        joyNoteBottomSheet.setItemListener((bottomSheetItem, sheetPosition) -> {
            joyNoteBottomSheet.dismiss();
            String title = bottomSheetItem.getTitle();
            if (getString(R.string.joynote_share).equals(title)) {
                JoyNoteShareUtil.INSTANCE.share(activity, v.minutesId, permissions, null);
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List, JDMAConstants.Mobile_Event_Minute_Share, new HashMap<>());
            } else if (getString(R.string.joynote_copy_link).equals(title)) {
                copyShareUrl(v.title, v.minutesId);
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List, JDMAConstants.Mobile_Event_Minute_Copylink, new HashMap<>());
            } else if (getString(R.string.joynote_rename).equals(title)) {
                showEditDialog(activity, v, position);
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List, JDMAConstants.Mobile_Event_Minute_Rename, new HashMap<>());
            } else if (getString(R.string.joynote_delete).equals(title)) {
                VideoListRepository.getUserRepository().delItem(v.minutesId, () -> {
                    v.status = RECYCLING;
                    adapter.updateItem(position);
                });
            } else if (getString(R.string.joynote_remove).equals(title)) {
                String typeApi = NEW_ADD;
                if (RECENT.equals(type)) {
                    typeApi = RECENT_TYPE;
                } else if (RECEIVE.equals(type)) {
                    typeApi = RECEIVED;
                }
                VideoListRepository.getUserRepository().removeItem(v.minutesId, typeApi, () -> {
                    v.status = ERROR;
                    adapter.removeItem(position);
                });
            } else if (getString(R.string.joynote_destroy).equals(title)) {
                AlertDialog.Builder builder = new AlertDialog.Builder(activity)
                        .setTitle(R.string.joy_note_new_tip_title)
                        .setMessage(R.string.joy_note_destroy_tip_msg)
                        .setNegativeButton(R.string.joy_note_new_tip_negative, (dialog, which) -> dialog.dismiss())
                        .setPositiveButton(R.string.joy_note_destroy_tip_positive, (dialog, which) -> {
                            if (getActivity() != null) {
                                dialog.dismiss();
                                VideoListRepository.getUserRepository().destroyItem(v.minutesId, () -> {
                                    v.status = ERROR;
                                    adapter.removeItem(position);
                                });
                            }
                        });
                AlertDialog dialog = builder.show();
                dialog.getButton(DialogInterface.BUTTON_POSITIVE).setTextColor(0xffFE3B30);
                dialog.getButton(DialogInterface.BUTTON_NEGATIVE).setTextColor(0xff232930);
            } else if (getString(R.string.joynote_restore).equals(title)) {
                VideoListRepository.getUserRepository().restoreItem(v.minutesId, () -> {
                    adapter.removeItem(position);
                    Intent intent = new Intent(VideoItemModel.ACTION_LIST_UPDATE);
                    intent.putExtra(VideoItemModel.OPERATION_TYPE, VideoItemModel.RESTORE_MINUTES);
                    intent.putExtra(VideoItemModel.MINUTE_ID, v.minutesId);
                    LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
                    RestoreDialog dialog = new RestoreDialog(this, v);
                    dialog.show(getParentFragmentManager(), "restore");
                });
            } else if (getString(R.string.joynote_permission_manager).equals(title)) {
                Intent intent = new Intent(activity, JoyNoteAuthorizeActivity.class);
                intent.putExtra("mid", v.minutesId);
                if (permissions != null) {
                    intent.putStringArrayListExtra(PERMISSIONS, new ArrayList<>(permissions));
                }
                launch(intent);
            }
        });
        JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List, JDMAConstants.Mobile_Event_Minute_More, new HashMap<>());
        joyNoteBottomSheet.show();
    }


    private boolean isLoading() {
        return isLoading;
    }

    void launch(Intent intent) {
        launcher.launch(intent);
    }

    @Override
    public void onDestroyView() {
        Activity activity = getActivity();
        if (activity != null) {
            LocalBroadcastManager.getInstance(activity).unregisterReceiver(receiver);
        }
        super.onDestroyView();
    }
}
