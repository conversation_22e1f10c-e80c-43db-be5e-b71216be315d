package com.jd.oa.joy.note.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import androidx.lifecycle.ViewModel
import com.jd.oa.ext.combineLatestWith

/**
 * @Author: hepiao3
 * @CreateTime: 2025/1/16
 * @Description:
 */
class JoyNoteMediaPlayerViewModel: ViewModel() {
    private val _mediaInfo = MutableLiveData<Triple<Int, Boolean, Boolean>>()
    val mediaInfoLiveData: LiveData<Triple<Int, Boolean, Boolean>> = _mediaInfo

    private val _mediaPlayingState = MutableLiveData(false)
    val mediaStateLiveData: LiveData<Pair<Boolean?, Int?>> = _mediaPlayingState
        .combineLatestWith(Transformations.map(_mediaInfo) { it.first }) {values ->
            return@combineLatestWith Pair<Boolean?, Int?>(values[0] as? Boolean, values[1] as? Int)
        }

    fun setMediaProgress(position: Int, sourceSelf: <PERSON><PERSON><PERSON>, sourceTimeLine: Boolean = false) {
        _mediaInfo.value = Triple(position, sourceSelf, sourceTimeLine)
    }

    fun setMediaState(state: Boolean) {
        _mediaPlayingState.value = state
    }

    private val _sentences = MutableLiveData<Int>()
    val sentencesLiveData: LiveData<Int> = _sentences

    fun setSentences(position: Int) {
        _sentences.value = position
    }

    private val _paragraph = MutableLiveData<String>()
    val paragraphLiveData: LiveData<String> = _paragraph
    fun setParagraphId(paragraphId: String) {
        _paragraph.value = paragraphId
    }
}