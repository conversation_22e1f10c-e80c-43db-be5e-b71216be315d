package com.jd.oa.joy.note.model;

import androidx.annotation.Keep;

import java.util.HashMap;
import java.util.Map;

/**
 * {
 *    "id": "0",
 *    "text": "豫章故郡，洪都新府。",
 *    "frontType": "",
 *    "isoTrans": false
 *  }
 */
@Keep
public class ContentText {

    String id;
    String text;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("text", text);
        return map;
    }
}
