package com.jd.oa.joy.note.compunent;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteMember;

import java.util.ArrayList;
import java.util.List;

/**
 * @noinspection unused
 */
public class JoyNoteSelectBtn extends ConstraintLayout {
    private int selectedTextColor = 0xffFE3B30;
    private int unselectedTextColor = 0xff232930;
    private String selectedText = "";
    private TextView selectBtnText;
    private View selectBtnBg;
    private JoyNoteAvatarGroup avatarGroup;

    public JoyNoteSelectBtn(@NonNull Context context) {
        super(context);
    }

    public JoyNoteSelectBtn(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initCustomAttrs(context, attrs);
        init();
    }

    public JoyNoteSelectBtn(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initCustomAttrs(context, attrs);
        init();
    }

    public JoyNoteSelectBtn(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initCustomAttrs(context, attrs);
        init();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return true;
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(R.layout.joy_note_select_btn, this, true);
        selectBtnText = findViewById(R.id.select_btn_text);
        selectBtnBg = findViewById(R.id.select_btn_bg);
        avatarGroup = findViewById(R.id.select_btn_avatar);
        ArrayList<JoyNoteMember> list = new ArrayList<>();
        avatarGroup.init(list);
        selectBtnText.setText(selectedText);
    }

    public void setUserList(List<JoyNoteMember> userList) {
        if (userList == null || userList.size() == 0) {
            if (avatarGroup != null) {
                avatarGroup.setVisibility(View.GONE);
                avatarGroup.refresh(new ArrayList<>());
            }
        } else {
            avatarGroup.setVisibility(View.VISIBLE);
            avatarGroup.refresh(userList);
        }

        if (selectBtnText != null) {
            selectBtnText.setTextColor(selectedTextColor);
        }
        if (selectBtnBg != null) {
            selectBtnBg.setBackgroundResource(R.drawable.bg_btn_joy_note_pink);
        }
    }

    public void reset() {
        if (avatarGroup != null) {
            avatarGroup.setVisibility(View.GONE);
            avatarGroup.refresh(new ArrayList<>());
        }
        if (selectBtnText != null) {
            selectBtnText.setTextColor(unselectedTextColor);
        }
        if (selectBtnBg != null) {
            selectBtnBg.setBackgroundResource(R.drawable.bg_btn_joy_note_gray);
        }
    }

    private void initCustomAttrs(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.JoyNoteSelectBtn);
        try {
            selectedTextColor = ta.getColor(R.styleable.JoyNoteSelectBtn_selectedTextColor, selectedTextColor);
            unselectedTextColor = ta.getColor(R.styleable.JoyNoteSelectBtn_unselectedTextColor, unselectedTextColor);
            selectedText = ta.getString(R.styleable.JoyNoteSelectBtn_selectedText);
        } finally {
            ta.recycle();
        }
    }

    @NonNull
    public List<JoyNoteMember> getUserList() {
        if (avatarGroup != null) {
            return avatarGroup.getUserList();
        }
        return new ArrayList<>();
    }
}
