package com.jd.oa.joy.note.model;

import androidx.annotation.Keep;

import java.util.Objects;

@Keep
public class JoyNoteLanguageModel {

    String display;
    String language;
    boolean defaultLanguage;

    public JoyNoteLanguageModel() {
    }

    public JoyNoteLanguageModel(String display, String language) {
        this.display = display;
        this.language = language;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public boolean isDefaultLanguage() {
        return defaultLanguage;
    }

    public void setDefaultLanguage(boolean defaultLanguage) {
        this.defaultLanguage = defaultLanguage;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof JoyNoteLanguageModel)) return false;
        JoyNoteLanguageModel that = (JoyNoteLanguageModel) o;
        return Objects.equals(display, that.display) && Objects.equals(language, that.language);
    }

    @Override
    public int hashCode() {
        return Objects.hash(display, language);
    }
}
