package com.jd.oa.joy.note.record

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/27
 * @Description:
 */
sealed class RecordIntent : Comparable<RecordIntent> {
    // 抽象权重属性用于比较
    abstract val weight: Int

    // 实现Comparable接口
    override fun compareTo(other: RecordIntent): Int = this.weight.compareTo(other.weight)

    // 按执行顺序定义权重值
    object Starting : RecordIntent() { override val weight = 0 }
    object Start : RecordIntent() { override val weight = 1 }
    object Pausing : RecordIntent() { override val weight = 2 }
    object Pause : RecordIntent() { override val weight = 3 }
    class End(val success: Boolean) : RecordIntent() { override val weight = 4 }
}