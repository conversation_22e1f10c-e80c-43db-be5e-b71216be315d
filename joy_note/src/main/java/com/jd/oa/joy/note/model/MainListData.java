package com.jd.oa.joy.note.model;

import androidx.lifecycle.LiveData;

import java.util.List;

public class MainListData extends LiveData<MainListData> {

    private List<VideoItemModel> itemModelList;
    private boolean success;
    private boolean loadMore;

    public List<VideoItemModel> getItemModelList() {
        return itemModelList;
    }

    public boolean isSuccess() {
        return success;
    }

    public boolean isLoadMore() {
        return loadMore;
    }

    public void setList(List<VideoItemModel> itemModelList, boolean success, boolean loadMore) {
        this.itemModelList = itemModelList;
        this.success = success;
        this.loadMore = loadMore;
        postValue(this);
    }
}
