package com.jd.oa.joy.note.model;

public class JoyNoteSentencePosition {

    public int segmentPosition;
    //第几句
    public int sentencePosition;

    //句子在段落中中的索引
    public int startIndex;
    public int endIndex;

    public String text;
    public long startTime;
    public long endTime;
    public String paragraphId;
    public String sentenceId;

    public JoyNoteSentencePosition(
            int segmentPosition,
            int sentencePosition,
            int startIndex,
            int endIndex,
            String text,
            long startTime,
            long endTime,
            String paragraphId,
            String sentenceId
    ) {
        this.segmentPosition = segmentPosition;
        this.sentencePosition = sentencePosition;
        this.startIndex = startIndex;
        this.endIndex = endIndex;
        this.text = text;
        this.startTime = startTime;
        this.endTime = endTime;
        this.paragraphId = paragraphId;
        this.sentenceId = sentenceId;
    }
}
