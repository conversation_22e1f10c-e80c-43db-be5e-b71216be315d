package com.jd.oa.joy.note.record

import android.media.AudioFormat
import android.os.Environment
import android.util.Log
import java.io.File

object AudioRecordCurrent {
    //当前的状态
    var currentStatus = AudioRecordStatus.COMPLETE
        set(value) {
            Log.d(javaClass.simpleName, "set currentStatus value: ${value.name}")
            field = value
        }

    //采样率
    const val sampleRateInHz = 16000

    //声道   CHANNEL_IN_MONO-单声道
    const val channelConfig = AudioFormat.CHANNEL_IN_MONO

    //采样精度
    const val audioFormat = AudioFormat.ENCODING_PCM_16BIT

    val rootPath =
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).path + File.separator
    val pcmFilePath = rootPath + "joyNote.pcm"

    //当前录音对应的慧记ID
    var minuteId = ""
        set(value) {
            Log.d(javaClass.simpleName, "set minuteId value: $value")
            field = value
        }

    //当前录音对应的 joySpace ID
    var bizId = ""

    // 渠道
    var channel = ""

    var recordDuration = 0L

    var title = ""

    var type: Int? = null

    fun reset() {
        minuteId = ""
        bizId = ""
        channel = ""
        type = null
        recordDuration = 0L
        currentStatus = AudioRecordStatus.COMPLETE
    }
}