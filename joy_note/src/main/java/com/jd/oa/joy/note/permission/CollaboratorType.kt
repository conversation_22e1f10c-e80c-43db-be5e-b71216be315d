package com.jd.oa.joy.note.permission

import android.content.Context
import android.graphics.Color
import androidx.annotation.ColorInt
import androidx.annotation.StringRes
import com.jd.oa.joy.note.R

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/18
 * @Description: 协作者类型
 */
enum class CollaboratorType(
    val type: Int,
    @ColorInt val color: Int = Color.BLACK,
    @StringRes val nameRes: Int = -1
) {
    // 个人
    PERSONAL(1),

    // 部门
    DEPARTMENT(2, R.color.color_F85B46, R.string.joynote_authorize_label_org),

    // 群聊
    GROUP(3, R.color.me_text_link, R.string.joynote_authorize_label_group),

    // 文档
    JOY_SPACE(4, R.color.color_F63218, R.string.joynote_authorize_label_document),

    // 日程
    SCHEDULE(5, R.color.color_FBB731, R.string.joynote_authorize_label_schedule);

    companion object {
        fun getColorResByType(context: Context, type: Int): Int {
            return context.getColor(
                values().find { it.type == type }?.color ?: android.R.color.black
            )
        }

        fun getTextResByType(context: Context, type: Int): String {
            return values().find { it.type == type }?.nameRes?.let {
                context.getString(it)
            } ?: ""
        }
    }
}