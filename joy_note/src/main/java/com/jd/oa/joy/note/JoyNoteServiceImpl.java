package com.jd.oa.joy.note;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;

import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.model.service.JoyNoteService;

import java.util.List;

public class JoyNoteServiceImpl implements JoyNoteService {
    @Override
    public void share(Activity activity, String minuteId, List<String> permissions, ActivityResultLauncher<Intent> launcher) {
        JoyNoteShareUtil.INSTANCE.share(activity, minuteId, permissions, launcher);
    }

    @Override
    public void openMePlayer(@NonNull Context context, @NonNull String videoUrl, String videoTitle) {
        DeepLinkTools.openMedia(context, videoUrl, videoTitle, true);
    }
}
