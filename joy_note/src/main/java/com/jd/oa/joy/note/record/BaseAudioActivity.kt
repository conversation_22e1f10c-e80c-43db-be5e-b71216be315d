package com.jd.oa.joy.note.record

import android.Manifest
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.view.View
import android.view.WindowInsetsController
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.fragment.app.commit
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.BaseActivity
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog
import com.jd.oa.ui.dialog.bottomsheet.BottomAction
import com.jd.oa.joy.note.JoyNoteTools
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.compunent.JoyNoteEditDialog
import com.jd.oa.joy.note.databinding.ActivityRecordAudioBinding
import com.jd.oa.joy.note.databinding.ItemRecordTipWidgetBinding
import com.jd.oa.joy.note.model.RecordPageParam
import com.jd.oa.joy.note.model.VideoItemModel
import com.jd.oa.joy.note.translate.AsrTranslateListFragment
import com.jd.oa.joy.note.translate.FloatingTranslateManager
import com.jd.oa.joy.note.translate.PlayControlFragment
import com.jd.oa.joy.note.viewmodel.JoyNoteRecordAudioViewModel
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ToastUtils
import org.json.JSONObject

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/25
 * @Description:
 */
abstract class BaseAudioActivity : BaseActivity() {
    private var isFrom0start: Boolean = false
    protected lateinit var binding: ActivityRecordAudioBinding
    private lateinit var tipBinding: ItemRecordTipWidgetBinding
    private val playControlFragment by lazy { PlayControlFragment() }
    protected val viewModel: JoyNoteRecordAudioViewModel by viewModels(factoryProducer = {
        JoyNoteRecordAudioViewModel.Factory()
    })
    private val backCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            if (playControlFragment.audioLaunchIsFailure()) {
                this.remove()
                onBackPressedDispatcher.onBackPressed()
                AudioRecordCurrent.reset()
                FloatingTranslateManager.stopFloatTranslateService(this@BaseAudioActivity)
                return
            }
            handleActivityBackPressed()
        }
    }

    abstract fun showFloatWindow()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRecordAudioBinding.inflate(layoutInflater)
        tipBinding = ItemRecordTipWidgetBinding.bind(binding.root)
        setContentView(binding.root)
        // 设置 StatusBar
        window.statusBarColor = Color.WHITE
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val insetsController = window.insetsController
            insetsController?.setSystemBarsAppearance(
                WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
            )
        } else {
            var visibility = window.decorView.systemUiVisibility
            visibility = visibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            window.decorView.systemUiVisibility = visibility
        }
        onBackPressedDispatcher.addCallback(this, backCallback)
        if (AudioRecordCurrent.currentStatus == AudioRecordStatus.COMPLETE) isFrom0start = true
        supportActionBar?.hide()
        paramsExtra()
        initViewModel()
        initView()
        viewModel.getRecordInfo()
    }

    open fun initView() {
        loadPlayControlWidget()
        binding.titleBar.apply {
            title = viewModel.recordPageParam?.name
            setRightBtnBListener { clickMore() }
            setRightBtnCListener { showRenameDialog() }
        }
        tipBinding.interruptTip.setOnClickListener {
            tipBinding.tipContainer.isVisible = false
        }
    }

    open fun initViewModel() {
        viewModel.recordState.observe(this) {
            when (it) {
                RecordIntent.Start -> {
                    tipBinding.tipContainer.isVisible = false
                }

                else -> {}
            }
        }
        viewModel.recordPageInfo.observe(this) {
            val jsonObject = JSONObject(it)
            val content = jsonObject.getJSONObject("content")
            val title = content.optString("title")
            val status = content.optInt("status")
            if (title.isNotEmpty() && !title.equals("null", true)) {
                binding.titleBar.title = title
                viewModel.recordPageParam?.name = title
            }
            if (status == 3 || status == 5) {
                val recordDuration = content.optString("recordDuration", "0")
                if (isFrom0start && recordDuration != "null") {
                    isFrom0start = false
                    AudioRecordCurrent.recordDuration = recordDuration.toLong()
                }
                viewModel.getRecordContent()
            }
        }
        viewModel.updateTitle.observe(this) {
            if (!it.second) {
                ToastUtils.showToast(getString(R.string.joynote_modify_fail))
                return@observe
            }
            // 同步更新慧记列表对应 Item Title
            binding.titleBar.title = it.first
            AudioRecordCurrent.title = it.first
            ToastUtils.showToast(getString(R.string.joynote_rename_success))
            val intent = Intent().apply {
                putExtra("newTitle", it.first)
                val param = <EMAIL>
                putExtra(
                    JoyNoteTools.MINUTES_ID,
                    param.getStringExtra(JoyNoteTools.MINUTES_ID)
                )
                putExtra(
                    JoyNoteTools.POSITION,
                    param.getIntExtra(JoyNoteTools.POSITION, -1)
                )
            }
            setResult(RESULT_OK, intent)

            //通知JoySpace修改标题
            val joySpaceIntent = Intent("action.joynote.update.joyspace").apply {
                putExtra("type", "EVENT_RENAME")
                putExtra("name", it.first)
                putExtra("minutesId", viewModel.recordPageParam?.minutesId)
            }
            LocalBroadcastManager.getInstance(this).sendBroadcast(joySpaceIntent)

            //通知列表页刷新
            val actionListIntent = Intent(VideoItemModel.ACTION_LIST_UPDATE).apply {
                putExtra(VideoItemModel.OPERATION_TYPE, VideoItemModel.RENAME_MINUTES)
                putExtra(VideoItemModel.MINUTE_ID, viewModel.recordPageParam?.minutesId)
                putExtra(VideoItemModel.NEW_TITLE_NAME, it.first)
            }
            LocalBroadcastManager.getInstance(this).sendBroadcast(actionListIntent)
        }

        viewModel.audioException.observe(this) {
            if (it.first.isNotEmpty() && it.second.isNotEmpty()) {
                tipBinding.tipContainer.isVisible = true
                tipBinding.tipContent.text = it.second
            }
        }
    }

    private fun paramsExtra() {
        if (intent != null && intent.hasExtra(DeepLink.DEEPLINK_PARAM)) {
            try {
                intent.getStringExtra(DeepLink.DEEPLINK_PARAM)?.let {
                    val minutesId = JSONObject(it).optString(
                        "minutesId",
                        intent.getStringExtra("minutesId") ?: ""
                    )
                    val name = JSONObject(it).optString("name", getString(R.string.joynote_title))
                    val channel = JSONObject(it).optString("channel")
                    val bizId = JSONObject(it).optString("bizId")
                    val bizType = JSONObject(it).optString("bizType")
                    val autoRecording = JSONObject(it).optBoolean("autoRecording", true)
                    val recordPageParam =
                        RecordPageParam(minutesId, channel, bizId, bizType, name, autoRecording)
                    viewModel.initRecordPageParam(recordPageParam)
                    AudioRecordCurrent.minuteId = viewModel.recordPageParam?.minutesId ?: ""
                    AudioRecordCurrent.title = viewModel.recordPageParam?.name ?: ""
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    protected fun loadAsrTranslateWidget(enableTranslate: Boolean) {
        supportFragmentManager.commit {
            replace(
                R.id.asr_translate_container,
                AsrTranslateListFragment().apply {
                    arguments = Bundle().apply {
                        putBoolean("enable_translate", enableTranslate)
                    }
                },
                AsrTranslateListFragment::class.simpleName
            )
        }
    }

    private fun loadPlayControlWidget() {
        supportFragmentManager.commit {
            replace(
                R.id.play_container,
                playControlFragment,
                PlayControlFragment::class.simpleName
            )
        }
    }

    /**
     * TitleBar “更多”按钮点击
     */
    private fun clickMore() {
        val actions = mutableListOf(
            BottomAction(
                iconStyle = R.style.BottomSheetDialogIcon_Copy,
                title = getString(R.string.joy_note_copy_link),
                isSelect = false,
                data = {
                    viewModel.recordPageParam?.let {
                        JoyNoteTools.copyShareUrl(it.name, it.minutesId)
                    } ?: run {}
                }),
            BottomAction(
                iconStyle = R.style.BottomSheetDialogIcon_Rename,
                title = getString(R.string.joy_note_rename),
                isSelect = false,
                data = { showRenameDialog() })
        )
        val builder = JoyBottomSheetDialog.Builder<() -> Unit>(this).apply {
            setActions(actions)
            setOnItemClickListener { action, _ ->
                action.data.invoke()
            }
        }
        JoyBottomSheetDialog(this, builder).show()
    }

    private fun showRenameDialog() {
        val joyNoteEditDialog = JoyNoteEditDialog(this, binding.titleBar.title)
        joyNoteEditDialog.setOnClickListener(object : JoyNoteEditDialog.OnClickListener {
            override fun onClickOk(inputText: String) {
                if (TextUtils.isEmpty(inputText)) {
                    ToastUtils.showToast(getString(R.string.joy_note_edit_input_null))
                    return
                }
                viewModel.updateTitle(viewModel.recordPageParam?.minutesId ?: "", inputText)
            }

            override fun onClickCancel() {}
        })
        joyNoteEditDialog.show()
    }

    protected fun handleActivityBackPressed() {
        if (Settings.canDrawOverlays(this)) {
            showFloatWindow()
            backCallback.remove()
            onBackPressedDispatcher.onBackPressed()
        } else {
            PermissionHelper.requestPermission(
                this,
                getString(R.string.me_eval_request_author),
                object : RequestPermissionCallback {
                    override fun allGranted() {
                        showFloatWindow()
                        backCallback.remove()
                        onBackPressedDispatcher.onBackPressed()
                    }

                    override fun denied(deniedList: MutableList<String>?) {
                    }
                },
                Manifest.permission.SYSTEM_ALERT_WINDOW
            )
        }
    }
}