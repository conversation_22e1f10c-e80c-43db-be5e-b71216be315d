package com.jd.oa.joy.note.permission

import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.BaseActivity
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.ActivityJoyNoteAuthorizeBinding
import com.jd.oa.joy.note.model.JoyNotePermissionUser
import com.jd.oa.joy.note.model.JoyNoteShareUser
import com.jd.oa.joy.note.model.Org
import com.jd.oa.joy.note.model.PermissionItem
import com.jd.oa.joy.note.model.PermissionUserItem
import com.jd.oa.joy.note.model.ScopeContent
import com.jd.oa.joy.note.model.SecurityContent
import com.jd.oa.joy.note.model.ShareGroupItem
import com.jd.oa.joy.note.model.ShareModeData
import com.jd.oa.joy.note.model.TaskUserBase
import com.jd.oa.joy.note.model.VideoItemModel.ADD_OWNER
import com.jd.oa.joy.note.permission.AccessScopeActivity.Companion.ORG
import com.jd.oa.joy.note.permission.AccessScopeActivity.Companion.SELECTED_SCOPE
import com.jd.oa.joy.note.permission.SecuritySettingActivity.Companion.SELECTED_SECURITY_SETTING
import com.jd.oa.joy.note.share.JoyNoteShareActivity.Companion.KEY_MID
import com.jd.oa.joy.note.share.JoyNoteShareActivity.Companion.PERMISSIONS
import com.jd.oa.joy.note.viewmodel.JoyNoteShareAuthorizeViewModel
import com.jd.oa.preference.PreferenceManager
import kotlinx.coroutines.launch

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/13
 * @Description: 权限
 */
class JoyNoteAuthorizeActivity : BaseActivity() {
    private val viewModel by viewModels<JoyNoteShareAuthorizeViewModel>()
    private val mid: String by lazy { intent.getStringExtra(KEY_MID) ?: "" }
    private val permissions by lazy { intent.getStringArrayListExtra(PERMISSIONS) ?: listOf() }
    private val hasOwner by lazy { permissions.contains(ADD_OWNER) }
    private lateinit var binding: ActivityJoyNoteAuthorizeBinding
    private val extraMemberList: MutableList<JoyNotePermissionUser?> = mutableListOf()
    private val permissionAdapter by lazy {
        PermissionAdapter(viewModel, permissions.toMutableList()) { user, isDelete ->
            viewModel.modifyCollaboratorPermission(
                mid,
                listOf(user.convertShareItem()),
                PreferenceManager.UserInfo.getTimlineAppID()
            )
            if (isDelete) {
                extraMemberList.removeIf { it?.userId == user.userId }
            }
        }
    }
    private val scopeLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val scopeContent = result.data?.getParcelableExtra(SELECTED_SCOPE) as? ScopeContent
            scopeContent?.apply {
                viewModel.updateLinkShareMode(type)
                viewModel.modifyPermissionSetting(
                    mid,
                    LINK_SHARE_MODE,
                    type,
                    PreferenceManager.UserInfo.getTimlineAppID(),
                    org
                )
            }
        }
    }
    private val securitySettingLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val securityContent =
                result.data?.getParcelableExtra(SELECTED_SECURITY_SETTING) as? SecurityContent
            securityContent?.apply {
                viewModel.updateDoShareMode(type)
                viewModel.modifyPermissionSetting(
                    mid,
                    DO_SHARE_MODE,
                    type.permission.toString(),
                    PreferenceManager.UserInfo.getTimlineAppID(),
                    null
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.hide()
        binding = DataBindingUtil.setContentView(this, R.layout.activity_joy_note_authorize)
        initView()
        initViewModel()
    }

    private fun initView() {
        binding.back.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
        binding.accessScope.iconRight.isInvisible = !hasOwner
        binding.recycleView.adapter = permissionAdapter
        binding.recycleView.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        binding.accessScope.tvDesc.setOnClickListener {
            viewModel.shareModeData.value?.let {
                Intent(this, AccessScopeActivity::class.java).apply {
                    putExtra(LINK_SHARE_MODE, it.linkShareMode)
                    putExtra(ORG, it.org)
                    scopeLauncher.launch(this)
                }
            }
        }
        binding.accessScope.tvDesc.isClickable = hasOwner
        binding.securityConfig.root.isVisible = hasOwner
        binding.securityConfig.tvDesc.setOnClickListener {
            viewModel.shareModeData.value?.let {
                Intent(this, SecuritySettingActivity::class.java).apply {
                    putExtra(DO_SHARE_MODE, it.doShareMode)
                    securitySettingLauncher.launch(this)
                }
            }
        }

        viewModel.getMemberEntityPermissionList(mid)
        viewModel.queryPermissionSetting(mid, PreferenceManager.UserInfo.getTimlineAppID())
    }

    private fun initViewModel() {
        viewModel.permissionUserList.observe(this) {
            if (it.isNullOrEmpty()) return@observe
            permissionAdapter.addData(it.toJoyNoteShareUserList())
            val newAddUserList = it.filterNot { user -> user in extraMemberList }
            extraMemberList.addAll(newAddUserList)
            binding.listTitle.isVisible = true
        }
        viewModel.shareModeData.observe(this) {
            val combinationInfo = when (it.linkShareMode) {
                LinkShareMode.ONLY_COLLABORATORS.type -> {
                    Pair(
                        getString(R.string.icon_general_lock),
                        getString(R.string.joynote_authorize_only_collaborators_visible)
                    )
                }

                LinkShareMode.ALL_PEOPLE_EDIT.type, LinkShareMode.ALL_PEOPLE_READ.type -> {
                    Pair(
                        getString(R.string.icon_globaljd),
                        getString(R.string.joynote_authorize_intra_enterprise_disclosure)
                    )
                }

                LinkShareMode.BGBU_EDIT.type, LinkShareMode.BGBU_READ.type -> {
                    Pair(
                        getString(R.string.icon_architecture),
                        getString(R.string.joynote_authorize_bgbu_disclosure)
                    )
                }

                else -> {
                    Pair("", "")
                }
            }
            binding.accessScope.icon.text = combinationInfo.first
            binding.accessScope.tvDesc.text = combinationInfo.second
            getLoginUserInfo(it)
        }
    }

    private fun MutableList<JoyNotePermissionUser>.toJoyNoteShareUserList(): MutableList<JoyNoteShareUser?> {
        return this.map { user ->
            JoyNoteShareUser(
                permissionType = user.permissionType,
                appId = user.teamId,
                userId = user.userId,
                userName = user.name,
                department = user.orgName ?: "",
                position = user.positionName ?: "",
                avatar = user.headImg,
                type = user.userType.toInt()
            )
        }.toMutableList()
    }

    private fun JoyNoteShareUser?.convertShareItem(): PermissionItem? =
        this?.run {
            if (type == CollaboratorType.GROUP.type) {
                ShareGroupItem(userId, appId, CollaboratorType.GROUP.type, permissionType)
            } else {
                PermissionUserItem(userId, appId, CollaboratorType.PERSONAL.type, permissionType)
            }
        }

    private fun getLoginUserInfo(shareModeData: ShareModeData) {
        // 当 query 接口返回 orgName 不为 null（说明当前设置的范围是 BGBU），
        // 或者之前已经调过getUserInfoBatch接口orgName 不为空，那么终止新的用户信息查询
        if (!shareModeData.org.orgName.isNullOrEmpty() &&
            !shareModeData.org.orgName.equals("null", false)) return
        lifecycleScope.launch {
            // 获取当前登录用户信息，方便后续设置 BGBU 部门范围使用
            val usersInfo = viewModel.getUsersInfo(
                listOf(
                    TaskUserBase(
                        PreferenceManager.UserInfo.getUserName(),
                        PreferenceManager.UserInfo.getTimlineAppID()
                    )
                )
            )
            usersInfo?.users?.firstOrNull()?.deptInfo?.let {
                val orgCode = it.fullPath?.split('/')?.getOrNull(2)
                val orgName = it.fullName?.split('-')?.getOrNull(1)
                viewModel.updateOrg(Org(orgCode, orgName))
            }
        }
    }

    companion object {
        const val LINK_SHARE_MODE = "linkShareMode" // 访问范围
        const val DO_SHARE_MODE = "doShareMode" // 安全配置
        const val ORG_CODE = "orgCode"
        const val ORG_NAME = "orgName"
    }
}
