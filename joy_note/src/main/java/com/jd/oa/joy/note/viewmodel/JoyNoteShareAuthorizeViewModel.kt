package com.jd.oa.joy.note.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.jd.oa.joy.note.model.JoyNotePermissionUser
import com.jd.oa.joy.note.model.Org
import com.jd.oa.joy.note.model.PermissionItem
import com.jd.oa.joy.note.model.ShareItem
import com.jd.oa.joy.note.model.ShareModeData
import com.jd.oa.joy.note.model.TaskUserBase
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity.Companion.DO_SHARE_MODE
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity.Companion.LINK_SHARE_MODE
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity.Companion.ORG_CODE
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity.Companion.ORG_NAME
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo
import com.jd.oa.joy.note.share.SharePermission
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.im.dd.entity.BatchUserInfo
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.utils.JsonUtils
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/11
 * @Description:
 */
class JoyNoteShareAuthorizeViewModel : ViewModel() {
    private val _memberList = MutableLiveData<MutableList<MemberEntityJd?>>()
    val memberList: LiveData<MutableList<MemberEntityJd?>> = _memberList

    private val _permissionUserList = MutableLiveData<MutableList<JoyNotePermissionUser>>()
    val permissionUserList: LiveData<MutableList<JoyNotePermissionUser>> = _permissionUserList

    private var _shareModeData = MutableLiveData<ShareModeData>()
    val shareModeData: LiveData<ShareModeData> = _shareModeData

    private var _shareResult = MutableLiveData<Boolean>()
    val shareResult: LiveData<Boolean> = _shareResult

    private fun addMember(memberList: MutableList<MemberEntityJd?>?) {
        this._memberList.value = memberList
    }

    fun clearMember() {
        this._memberList.value = null
    }

    private fun addPermissionUser(permissionUserList: MutableList<JoyNotePermissionUser>) {
        this._permissionUserList.value = permissionUserList
    }

    private fun updateShareModeData(shareModeData: ShareModeData) {
        _shareModeData.value = shareModeData
    }

    fun updateLinkShareMode(linkShareMode: String) {
        _shareModeData.value = _shareModeData.value?.copy(linkShareMode = linkShareMode)
    }

    fun updateDoShareMode(doShareMode: SharePermission) {
        _shareModeData.value =
            _shareModeData.value?.copy(doShareMode = doShareMode.permission.toString())
    }

    fun updateOrg(org: Org) {
        _shareModeData.value = _shareModeData.value?.copy(org = org)
    }

    private fun updateShareResult(result: Boolean) {
        _shareResult.value = result
    }

    /**
     * 查询用户详细信息
     */
    fun getWholeContactDetailInfo(memberList: ArrayList<MemberEntityJd?>) {
        val taskUserBaseList =
            memberList
                .filter { it?.type == MemberEntityJd.TYPE_CONTACT }
                .map {
                    it?.let {
                        TaskUserBase(it.mId, it.mApp ?: MultiAppConstant.APPID)
                    }
                }
        if (taskUserBaseList.isEmpty()) {
            addMember(memberList)
            return
        }
        viewModelScope.launch {
            runCatching {
                val batchUserInfo = getUsersInfo(taskUserBaseList)
                val addUserList = batchUserInfo?.users.toMemberEntityJd()
                addUserList?.forEach { user ->
                    val index = memberList.indexOfFirst { it?.mId == user?.mId }
                    user?.type = MemberEntityJd.TYPE_CONTACT
                    if (index > -1 && index < memberList.size) {
                        memberList[index] = user
                    }
                }
                addMember(memberList)
            }.getOrElse {
                addMember(memberList)
            }
        }
    }

    /**
     * 获取多个用户基础信息
     */
    suspend fun getUsersInfo(taskUserBaseList: List<TaskUserBase?>) =
        suspendCancellableCoroutine<BatchUserInfo?> { continuation ->
            JoyNoteDetailRepo.getRepo().getUserInfoBatch(
                taskUserBaseList,
                object : LoadDataCallback<BatchUserInfo> {
                    override fun onDataLoaded(p0: BatchUserInfo?) {
                        continuation.resumeWith(Result.success(p0))
                    }

                    override fun onDataNotAvailable(p0: String?, p1: Int) {
                        continuation.resumeWith(Result.failure(Exception(p0)))
                    }
                })
        }

    /**
     * 指定慧记分享给某人、某群
     */
    fun share(
        mid: String,
        shareItems: List<ShareItem?>,
        sendToChat: Boolean,
        sendToMinutesRobot: Boolean,
        content: String = ""
    ) {
        JoyNoteDetailRepo.getRepo().share(
            mid,
            shareItems,
            sendToChat,
            sendToMinutesRobot,
            content,
            object : LoadDataCallback<Any?> {
                override fun onDataLoaded(o: Any?) {
                    updateShareResult(true)
                }

                override fun onDataNotAvailable(s: String, i: Int) {
                    updateShareResult(false)
                }
            })
    }

    /**
     * 获取协作人列表
     */
    fun getMemberEntityPermissionList(mid: String) {
        JoyNoteDetailRepo.getRepo().getPermissionList(
            mid, object : LoadDataCallback<Any> {
                override fun onDataLoaded(p0: Any?) {
                    JSONObject(p0?.toString() ?: "").optJSONObject("content")
                        ?.optJSONArray("users")?.let {
                            val permissionUsers =
                                JsonUtils.getGson().fromJson<MutableList<JoyNotePermissionUser>>(
                                    it.toString(),
                                    object : TypeToken<MutableList<JoyNotePermissionUser>>() {}.type
                                )
                            addPermissionUser(permissionUsers)
                        }
                }

                override fun onDataNotAvailable(p0: String?, p1: Int) {
                }
            })
    }

    /**
     * 查询访问范围、安全配置
     */
    fun queryPermissionSetting(minutesId: String, appId: String) {
        JoyNoteDetailRepo.getRepo().queryPermissionSetting(
            minutesId, appId,
            object : LoadDataCallback<Any> {
                override fun onDataLoaded(p0: Any?) {
                    p0?.let {
                        JSONObject(p0.toString()).optJSONObject("content")?.apply {
                            updateShareModeData(
                                ShareModeData(
                                    optString(LINK_SHARE_MODE),
                                    optString(DO_SHARE_MODE),
                                    Org(optString(ORG_CODE), optString(ORG_NAME))
                                )
                            )
                        }
                    }
                }

                override fun onDataNotAvailable(p0: String?, p1: Int) {
                }
            })
    }

    /**
     * 修改安全配置、访问范围
     */
    fun modifyPermissionSetting(
        minutesId: String,
        type: String,
        value: String,
        appId: String,
        org: Org?
    ) {
        JoyNoteDetailRepo.getRepo().modifyPermissionSetting(minutesId, type, value, appId, org)
    }

    /**
     * 修改协作人权限
     */
    fun modifyCollaboratorPermission(
        minutesId: String,
        permissionList: List<PermissionItem?>,
        appId: String
    ) {
        JoyNoteDetailRepo.getRepo().modifyCollaboratorPermission(minutesId, permissionList, appId)
    }

    private fun List<Members>?.toMemberEntityJd(): MutableList<MemberEntityJd?>? {
        return this?.map { member ->
            MemberEntityJd().apply {
                mId = member.emplAccount ?: member.userId
                mApp = member.ddAppId ?: member.app
                department = member.deptInfo?.fullName
                mAvatar = member.headImg
                position = member.position
                mName = member.realName
                titleName = member.titleName
                chief = member.chief
            }
        }?.toMutableList()
    }
}