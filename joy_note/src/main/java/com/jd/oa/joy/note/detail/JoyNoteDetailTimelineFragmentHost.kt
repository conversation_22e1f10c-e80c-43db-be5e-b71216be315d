package com.jd.oa.joy.note.detail

import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.annotation.IdRes
import androidx.fragment.app.FragmentManager
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.model.SpeakerTimeline
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

/**
 * @Author: peidongbiao
 * @CreateTime: 2025/1/19
 * @Description:
 */
class JoyNoteDetailTimelineFragmentHost(
    private val fragment: JoyNoteDetailFragment,
    private val parent: View,
    private val fragmentManager: FragmentManager,
    @IdRes private val container: Int,
    @IdRes private val mask: Int
) {
    private var mParagraphFragment: JoyNoteDetailTimelineFragment? = null
    private val maskView: View by lazy { parent.findViewById(mask) }

    private val backPressedCallback = object : OnBackPressedCallback(false) {
        override fun handleOnBackPressed() {
            hideParagraphFragment()
        }
    }

    init {
        val activity = fragment.activity
        activity?.onBackPressedDispatcher?.addCallback(fragment.getViewLifecycleOwner(), backPressedCallback)
    }

    fun showParagraphFragment(speakerTimeline: SpeakerTimeline, totalDuration: Long) {
        if (mParagraphFragment == null) {
            mParagraphFragment = JoyNoteDetailTimelineFragment.newInstance()
            mParagraphFragment!!.onCloseClickListener = {
                hideParagraphFragment()
            }

            mParagraphFragment!!.speakerTimeline = speakerTimeline
            mParagraphFragment!!.totalDuration = totalDuration

            fragmentManager
                .beginTransaction()
                .addToBackStack(null)
                .setCustomAnimations(
                    R.anim.joy_note_detail_timeline_fragment_show,
                    R.anim.joy_note_detail_timeline_fragment_hide,
                    R.anim.joy_note_detail_timeline_fragment_show,
                    R.anim.joy_note_detail_timeline_fragment_hide
                )
                .add(container, mParagraphFragment!!)
                .commitAllowingStateLoss()

            maskView.setOnClickListener {
                hideParagraphFragment()
            }
        } else {
            mParagraphFragment!!.speakerTimeline = speakerTimeline
            mParagraphFragment!!.totalDuration = totalDuration

            fragmentManager
                .beginTransaction()
                .addToBackStack(null)
                .setCustomAnimations(
                    R.anim.joy_note_detail_timeline_fragment_show,
                    R.anim.joy_note_detail_timeline_fragment_hide,
                    R.anim.joy_note_detail_timeline_fragment_show,
                    R.anim.joy_note_detail_timeline_fragment_hide
                )
                .show(mParagraphFragment!!)
                .commitAllowingStateLoss()

            mParagraphFragment!!.scrollToRunningScope()
        }
        maskView.visible()
        backPressedCallback.isEnabled = true
    }

    fun hideParagraphFragment() {
        if (mParagraphFragment != null) {
            fragmentManager
                .beginTransaction()
                .addToBackStack(null)
                .setCustomAnimations(
                    R.anim.joy_note_detail_timeline_fragment_show,
                    R.anim.joy_note_detail_timeline_fragment_hide,
                    R.anim.joy_note_detail_timeline_fragment_show,
                    R.anim.joy_note_detail_timeline_fragment_hide
                )
                .hide(mParagraphFragment!!)
                .commitAllowingStateLoss()
        }
        maskView.gone()
        backPressedCallback.isEnabled = false
    }

}