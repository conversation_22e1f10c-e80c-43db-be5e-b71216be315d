package com.jd.oa.joy.note.detail

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.FragmentJoyNoteSpeakerTimlineBinding
import com.jd.oa.joy.note.model.DataState
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel
import com.jd.oa.joy.note.model.SpeakerTimeline
import com.jd.oa.joy.note.repository.JoyNoteSpeakerRepo
import com.jd.oa.joy.note.viewmodel.JoyNoteDetailSpeakerViewModel
import com.jd.oa.joy.note.viewmodel.JoyNoteMediaPlayerViewModel

/**
 * @Author: hepiao3
 * @CreateTime: 2025/1/16
 * @Description:
 */
class JoyNoteDetailSpeakerFragment(
    private var onTimelineClick: ((SpeakerTimeline, Long) -> Unit)? = null,
    private var onUpdateSpeakerTabText: ((Int) -> Unit)? = null
) : BaseFragment() {
    private lateinit var binding: FragmentJoyNoteSpeakerTimlineBinding
    private lateinit var mAdapter: SpeakerTimelineAdapter
    private var joyNoteDetailInfoModel: JoyNoteDetailInfoModel? = null

    private val speakerViewModel: JoyNoteDetailSpeakerViewModel by activityViewModels(
        factoryProducer = {
            JoyNoteDetailSpeakerViewModel.Factory(JoyNoteSpeakerRepo())
        })

    private val mediaPlayerViewModel: JoyNoteMediaPlayerViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentJoyNoteSpeakerTimlineBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initViewModel()
    }

    private fun initView() {
        joyNoteDetailInfoModel = arguments?.getParcelable("info") as? JoyNoteDetailInfoModel
        speakerViewModel.getSpeakerTimeLine(joyNoteDetailInfoModel?.minutesDetail?.minutesId ?: "")

        binding.apply {
            lifecycleOwner = this@JoyNoteDetailSpeakerFragment
            viewModel = speakerViewModel
            minutesId = joyNoteDetailInfoModel?.minutesDetail?.minutesId ?: ""
            recyclerView.apply {
                layoutManager = LinearLayoutManager(context)
                mAdapter = SpeakerTimelineAdapter(
                    requireContext(),
                    speakerViewModel,
                    mediaPlayerViewModel
                ) {
                    onTimelineClick?.invoke(
                        it,
                        speakerViewModel.totalSpeakerTimeline.value?.recordDuration ?: 0
                    )
                }
                adapter = mAdapter
            }
        }
    }

    private fun initViewModel() {
        speakerViewModel.totalSpeakerTimeline.observe(viewLifecycleOwner) {
            it.timelines?.let { list -> mAdapter.updateAllData(list) }
            onUpdateSpeakerTabText?.invoke(it.timelines?.size ?: 0)
        }
        // 数据状态信息，修改 view 显示状态
        speakerViewModel.dataState.observe(viewLifecycleOwner) {
            binding.apply {
                btnRefresh.isVisible = it is DataState.Error
                loadTip.isVisible = !(it is DataState.Loading || it is DataState.Success)
                recyclerView.isVisible = it !is DataState.Loading
                loadingGroupView.isVisible = it is DataState.Error || it is DataState.Empty
                loadPlaceHolder.isVisible = it is DataState.Loading

                loadTip.text = resources.getString(
                    if (it is DataState.Error) R.string.me_load_failed else R.string.joynote_speaker_timeline_no_person
                )
            }
        }
    }
}