package com.jd.oa.joy.note.detail;

import static com.jd.oa.joy.note.repository.JoyNoteDetailRepo.ERROR_CODE_IS_RECORDING;
import static com.jd.oa.joy.note.repository.JoyNoteDetailRepo.ERROR_CODE_IS_RECORDING_BY_ME;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chenenyu.router.Router;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.JoyNoteTitleBar;
import com.jd.oa.router.DeepLink;

public class JoyNoteDetailErrorFragment extends BaseFragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_joy_note_detail_load_error, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        JoyNoteTitleBar mToolBar = view.findViewById(R.id.toolbar);
        mToolBar.setTitle(getString(R.string.joynote_title));
        TextView mTvRefresh = view.findViewById(R.id.tv_refresh);
        mTvRefresh.setOnClickListener(v -> {
            JoyNoteDetailActivity joyNoteDetailActivity = (JoyNoteDetailActivity) getActivity();
            if (joyNoteDetailActivity != null) {
                joyNoteDetailActivity.getDetail();
            }
        });


        if (getArguments() != null) {
            int type = getArguments().getInt("type");
            if (type == ERROR_CODE_IS_RECORDING || type == ERROR_CODE_IS_RECORDING_BY_ME) {
                mTvRefresh.setText(R.string.joynote_detail_placeholder_back);
                mTvRefresh.setOnClickListener(v -> {
                    if (getActivity() != null) {
                        Router.build(DeepLink.JOY_NOTE_MAIN).go(getActivity());
                        getActivity().finish();
                    }
                });
                ImageView ivError = view.findViewById(R.id.iv_error);
                TextView tvTip = view.findViewById(R.id.tv_tip);
                ivError.setImageResource(R.drawable.joynote_is_recording);
                if (type == ERROR_CODE_IS_RECORDING_BY_ME) {
                    tvTip.setText(R.string.joy_note_other_tip_msg);
                } else tvTip.setText(R.string.joy_note_error_is_recording);
            }

        }
    }
}
