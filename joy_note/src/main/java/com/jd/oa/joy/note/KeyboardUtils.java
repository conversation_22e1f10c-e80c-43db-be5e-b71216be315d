package com.jd.oa.joy.note;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.WindowInsetsController;
import android.view.inputmethod.InputMethodManager;
import android.os.Build;
import android.view.WindowInsets;
import android.widget.EditText;

public class KeyboardUtils {
    public static void showKeyboard(EditText view, Activity activity) {
        if (activity == null) {
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 使用 WindowInsetsController
            View decorView = activity.getWindow().getDecorView();
            WindowInsetsController insetsController = decorView.getWindowInsetsController();
            if (insetsController != null) {
                insetsController.show(WindowInsets.Type.ime());
            }
        } else {
            // 传统方法
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                view.requestFocus();
                imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
            }
        }
    }

    public static void hideKeyboard(EditText view, Activity activity) {
        if (activity == null) {
            return;
        }
        // Android 11 (API 30) 及以上版本推荐使用 WindowInsetsController
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowInsetsController insetsController = view.getWindowInsetsController();
            if (insetsController != null) {
                insetsController.hide(WindowInsets.Type.ime());
            }
        } else {
            // 传统方法（API 30 以下）
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
            }
        }
    }

    public static void toggleSoftInput(View view) {
        InputMethodManager imm = (InputMethodManager) view.getContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.toggleSoftInput(0, 0);
        }
    }
}
