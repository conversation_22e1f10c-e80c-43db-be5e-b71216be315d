package com.jd.oa.joy.note.translate

import android.Manifest.permission
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresPermission
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.jd.oa.loading.loadingDialog.LoadingDialog
import com.chenenyu.router.Router
import com.jd.oa.asr.AsrClient
import com.jd.oa.asr.AsrService
import com.jd.oa.asr.AsrService.AsrServiceBinder
import com.jd.oa.asr.Logger
import com.jd.oa.asr.websocket.model.EventName
import com.jd.oa.asr.websocket.model.TextMsg
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joy.note.DateTimeUtils
import com.jd.oa.joy.note.JoyNoteTools.RESULT_REFRESH
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.ItemVoiceWaveControlButtonBinding
import com.jd.oa.joy.note.detail.JoyNoteDetailActivity.updateStatus
import com.jd.oa.joy.note.model.VideoItemModel.ERROR
import com.jd.oa.joy.note.model.VideoItemModel.FINISH
import com.jd.oa.joy.note.model.VideoItemModel.GENERATION
import com.jd.oa.joy.note.model.VideoItemModel.RECORDING
import com.jd.oa.joy.note.model.VideoItemModel.RECORD_NO_START
import com.jd.oa.joy.note.model.VideoItemModel.RECORD_STOP
import com.jd.oa.joy.note.model.VideoItemModel.RECYCLING
import com.jd.oa.joy.note.record.AudioRecordCurrent
import com.jd.oa.joy.note.record.AudioRecordStatus
import com.jd.oa.joy.note.record.RecordIntent
import com.jd.oa.joy.note.viewmodel.JoyNoteRecordAudioViewModel
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ToastUtils
import com.jd.oa.websocket.WebSocketTool
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.io.File

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/28
 * @Description:
 */
class PlayControlFragment : BaseFragment() {
    private var asrService: AsrService? = null
    private var lastTimeClickPair: Pair<Long, Long> = Pair(0, 0)
    private var lastRecordDuration: Long = 0
    private var mRecordDuration: Long = 0
    private lateinit var asrServiceConnection: ServiceConnection
    private var _binding: ItemVoiceWaveControlButtonBinding? = null
    private val binding get() = _binding!!
    private val loadingDialog by lazy { LoadingDialog(requireContext(), "") }
    private val filePath by lazy { requireContext().externalCacheDir?.path + File.separator + "audio.pcm" }
    private val viewModel: JoyNoteRecordAudioViewModel by activityViewModels {
        JoyNoteRecordAudioViewModel.Factory()
    }
    private val breakEventList: MutableList<Pair<String, Int>> by lazy {
        mutableListOf(
            Pair(EventName.DIFFERENT_DEVICES, R.string.joy_note_other_device_recording_tip_msg),
            Pair(EventName.STOP_TIME_OUT, R.string.joynote_is_over_time_for_record)
        )
    }
    private val intent: Intent by lazy {
        Intent(requireActivity(), AsrService::class.java).apply {
            viewModel.recordPageParam?.minutesId?.let {
                putExtra(AsrService.ARG_CHANNEL_ID, it)
                putExtra(AsrService.ARG_URL, WebSocketTool.getJoyNoteUrl(it))
            }
            putExtra(AsrService.ARG_FILE_PATH, filePath)
            putExtra(AsrService.ARG_RECORD_DURATION, lastRecordDuration)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ItemVoiceWaveControlButtonBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViewModel()
        initView()
    }

    private fun initView() {
        binding.widgetParent.setOnClickListener { playOrPause() }
        binding.tvComplete.setOnClickListener { completeRecord() }
    }

    private fun initViewModel() {
        viewModel.recordState.observe(viewLifecycleOwner) {
            when (it) {
                RecordIntent.Starting, RecordIntent.Start -> {
                    binding.waveView.start()
                    viewModel.updateAudioException()
                }

                RecordIntent.Pausing, RecordIntent.Pause -> {
                    binding.waveView.stop()
                }

                is RecordIntent.End -> {
                    finishRecord(it)
                }
            }
            setPlayPauseText(it)
        }
        viewModel.recordPageInfo.observe(viewLifecycleOwner) {
            baseInfoParse(it)
        }
        viewModel.realTimeTranslate.observe(viewLifecycleOwner) {
            asrService?.setTranslate(it?.first?.realTimeTranslateTargetLang)
        }
        viewModel.audioException.observe(viewLifecycleOwner) { exception ->
            if (exception.first.isEmpty()) return@observe
            if (breakEventList.firstOrNull { it.first == exception.first } != null) {
                binding.widgetParent.alpha = 0.2f
            }
            if (exception.first == EventName.NOT_ALLOW_RECORD) {
                finishRecord(RecordIntent.End(true))
            } else if (viewModel.recordState.value != RecordIntent.Pause) {
                viewModel.updateRecordState(RecordIntent.Pause)
            }
        }
    }

    private fun baseInfoParse(info: String?) {
        runCatching {
            info ?: return
            val jsonObject = JSONObject(info)
            val content = jsonObject.getJSONObject("content")
            val status = content.optInt("status")
            val recordDuration = content.optString("recordDuration", "0")
            if (recordDuration != "null") {
                this.lastRecordDuration = recordDuration.toLong()
                binding.tvDuration.text = DateTimeUtils.getShowTextByMs2(this.lastRecordDuration)
            }
            // 0无效数据 1正常数据 2生成中的数据 3录制中的数据 4进入回收站的数据 5录制暂停的数据 6录制未开始的数据
            when (status) {
                FINISH, GENERATION, RECYCLING -> {
                    openDetail()
                    activity?.finish()
                }

                ERROR, RECORDING, RECORD_STOP, RECORD_NO_START -> {
                    bindService(status)
                }

                else -> {}
            }
        }
    }

    private fun finishRecord(recordIntent: RecordIntent.End) {
        if (recordIntent.success) {
            activity?.setResult(RESULT_REFRESH)
            AudioRecordCurrent.reset()
            ToastUtils.showToast(R.string.joy_note_finish_tip)
            lifecycleScope.launch(Dispatchers.Main) {
                delay(2000)
                loadingDialog.dismiss()
                updateStatus(
                    GENERATION,
                    viewModel.recordPageParam?.minutesId ?: "",
                    requireContext()
                )
                openDetail()
                activity?.apply {
                    FloatingTranslateManager.stopFloatTranslateService(this)
                    finish()
                }
            }
            val file = File(filePath)
            if (file.exists()) file.delete()
        } else {
            loadingDialog.dismiss()
            ToastUtils.showToastLong(R.string.me_server_time_out_not_translate)
        }
    }

    private fun setPlayPauseText(status: RecordIntent) {
        when (status) {
            RecordIntent.Start, RecordIntent.Starting -> {
                binding.widgetParent.setBackgroundResource(R.drawable.bg_btn_joy_note_gray_line_semicircle)
                binding.ivPlayContinue.isVisible = true
                binding.tvPlayStart.isVisible = false
            }

            else -> {
                binding.widgetParent.setBackgroundResource(R.drawable.bg_btn_joy_note_blue_semicircle)
                binding.ivPlayContinue.isVisible = false
                binding.tvPlayStart.isVisible = true
            }
        }
    }

    private fun applyPermissionStart() {
        PermissionHelper.requestPermissions(
            requireActivity(),
            resources.getString(R.string.me_request_permission_title_normal),
            resources.getString(R.string.me_request_permission_audio_normal),
            object : RequestPermissionCallback {
                @RequiresPermission(permission.RECORD_AUDIO)
                override fun allGranted() {
                    asrService?.startRecord()
                }

                override fun denied(deniedList: List<String>) {
                }
            },
            permission.RECORD_AUDIO,
            permission.WRITE_EXTERNAL_STORAGE
        )
    }

    private fun bindService(status: Int) {
        if (asrService != null) return
        asrServiceConnection = object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName, service: IBinder) {
                Logger.getDefault().info(TAG, "onServiceConnected: $status")
                val binder = service as AsrServiceBinder
                asrService = binder.getService()
                initServiceListener()
                if (viewModel.recordPageParam?.autoRecording == true) {
                    syncAudioStatus(status)
                }
            }

            override fun onServiceDisconnected(name: ComponentName) {
                Logger.getDefault().info(TAG, "onServiceDisconnected")
                asrService = null
            }
        }.apply {
            requireActivity().startService(intent)
            // 仅用 bindService()：服务生命周期与绑定组件同步，组件销毁则服务销毁
            requireActivity().bindService(intent, this, Context.BIND_AUTO_CREATE)
        }
    }

    private fun syncAudioStatus(status: Int) {
        when (status) {
            RECORD_STOP -> {
                AudioRecordCurrent.currentStatus = AudioRecordStatus.PAUSE
                viewModel.updateRecordState(RecordIntent.Pause)
            }

            else -> {
                playOrPause()
                if (status == RECORDING) {
                    AudioRecordCurrent.currentStatus = AudioRecordStatus.RECORDING
                    viewModel.updateRecordState(RecordIntent.Start)
                }
            }
        }
    }

    private val textUpdateListener: (TextMsg) -> Unit = {
        viewModel.refreshAsrText(it)
    }

    private val volumeChangeListener: (Int) -> Unit = {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - lastTimeClickPair.first >= 50) {
            binding.waveView.duration = 50
            // 调整波动太小
            val adjustVol = minOf(maxOf((it.minus(36)) * 2.1, 10.0), 100.0)
            activity?.runOnUiThread {
                binding.waveView.addBody(adjustVol.toInt())
                viewModel.recordState.value?.let { state ->
                    if (state in RecordIntent.Start..RecordIntent.Starting) {
                        binding.waveView.start()
                    }
                }
            }
            lastTimeClickPair = lastTimeClickPair.copy(currentTimeMillis)
        }
    }

    private val durationChangeListener: (Long) -> Unit = {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - lastTimeClickPair.second >= 50) {
            mRecordDuration = it
            activity?.runOnUiThread {
                binding.tvDuration.text = DateTimeUtils.getShowTextByMs2(mRecordDuration)
            }
            lastTimeClickPair = lastTimeClickPair.copy(second = currentTimeMillis)
        }
    }

    private val playStateChangeListener: (RecordIntent) -> Unit = {
        Logger.getDefault().info(TAG, "playStateChange: ${it.javaClass.simpleName}")
        viewModel.updateRecordState(it)
    }

    private val asrErrorListener = { errorType: String, errorMsg: String ->
        Logger.getDefault().info(TAG, "errorType: $errorType, errorMsg: $errorMsg")
        viewModel.updateAudioException(errorType, errorMsg)
    }

    private fun initServiceListener() {
        asrService?.apply {
            setTranslate(viewModel.realTimeTranslate.value?.first?.realTimeTranslateTargetLang)
            addOnTextUpdateListener(textUpdateListener)
            addVolumeChangeListener(volumeChangeListener)
            addDurationChangeListener(durationChangeListener)
            addPlayStateChangeListener(playStateChangeListener)
            addOnAsrErrorListener(asrErrorListener)
        }
    }

    private fun playOrPause() {
        Logger.getDefault()
            .info(TAG, "playOrPause: ${viewModel.recordState.value?.javaClass?.simpleName}")
        breakEventList.firstOrNull { it.first == viewModel.audioException.value?.first }?.let {
            ToastUtils.showToast(getString(it.second))
            return
        }

        val status = when (viewModel.recordState.value) {
            RecordIntent.Starting, RecordIntent.Pausing -> RECORDING
            RecordIntent.Start -> {
                asrService?.pauseRecord()
                RECORD_STOP
            }

            RecordIntent.Pause -> {
                // 录制时长超过 4 个小时暂停
                if (mRecordDuration >= MAX_AUDIO_DURATION) {
                    ToastUtils.showToast(R.string.joynote_is_over_time_for_record)
                    null
                } else {
                    applyPermissionStart()
                    RECORDING
                }
            }

            is RecordIntent.End, null -> {
                applyPermissionStart()
                RECORDING
            }
        }
        status?.let {
            viewModel.recordPageParam?.minutesId?.let {
                updateStatus(status, it, requireContext())
            }
        }
    }

    private fun completeRecord() {
        Logger.getDefault().info(TAG, "completeRecord")
        loadingDialog.show()
        binding.waveView.stop()
        asrService?.finishRecord()
    }

    private fun openDetail() {
        val jsonObject = JSONObject()
        jsonObject.put("minutesId", viewModel.recordPageParam?.minutesId)
        val deeplink =
            DeepLink.JOY_NOTE_DETAIL.toUri().buildUpon()
                .appendQueryParameter(
                    DeepLink.DEEPLINK_PARAM,
                    jsonObject.toString()
                ).build()
        Router.build(deeplink).go(this)
    }

    fun audioLaunchIsFailure(): Boolean {
        return asrService == null || asrService?.getAudioState() == AsrClient.STATE_STOPPED ||
                asrService?.getAudioState() == AsrClient.STATE_INIT
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::asrServiceConnection.isInitialized) {
            requireActivity().unbindService(asrServiceConnection)
        }
        binding.waveView.stop()
        asrService?.apply {
            removeOnTextUpdateListener(textUpdateListener)
            removeVolumeChangeListener(volumeChangeListener)
            removeDurationChangeListener(durationChangeListener)
            removePlayStateChangeListener(playStateChangeListener)
            removeOnAsrErrorListener(asrErrorListener)
        }
        asrService = null
    }

    companion object {
        const val MAX_AUDIO_DURATION = 4 * 60 * 60 * 1000
    }
}