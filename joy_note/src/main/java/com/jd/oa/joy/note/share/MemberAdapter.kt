package com.jd.oa.joy.note.share

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog
import com.jd.oa.ui.dialog.bottomsheet.BottomAction
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.ItemShareMemberLayoutBinding
import com.jd.oa.joy.note.model.JoyNoteShareUser
import com.jd.oa.joy.note.model.VideoItemModel.ADD_EDITABLE
import com.jd.oa.joy.note.model.VideoItemModel.ADD_OWNER
import com.jd.oa.joy.note.viewmodel.JoyNoteShareAuthorizeViewModel
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.ImageLoader

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/11
 * @Description:
 */
open class MemberAdapter(
    private val viewModel: JoyNoteShareAuthorizeViewModel,
    protected open val permissions: MutableList<String>,
    protected open val updateUser: ((user: JoyNoteShareUser, isDelete: Boolean) -> Unit)
) : RecyclerView.Adapter<MemberAdapter.MemberViewHolder>() {
    protected val memberList: MutableList<JoyNoteShareUser?> = mutableListOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberViewHolder {
        val binding =
            ItemShareMemberLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MemberViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MemberViewHolder, position: Int) {
    }

    override fun onBindViewHolder(
        holder: MemberViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        memberList[position]?.let { user ->
            holder.binding.apply {
                permissionEdit.text = when (user.permissionType) {
                    SharePermission.CREATOR.permission -> holder.itemView.context.getString(R.string.joynote_creator)
                    SharePermission.EDITOR.permission -> holder.itemView.context.getString(R.string.joynote_can_edit)
                    SharePermission.READER.permission -> holder.itemView.context.getString(R.string.joynote_can_read)
                    else -> ""
                }
                if (permissionEdit.isClickable) {
                    permissionEdit.setOnClickListener {
                        showPermissionDialog(holder.itemView.context, user)
                    }
                }
                if (payloads.isNotEmpty()) return
                realName.text = user.userName
                if (user.type in multiTypes) {
                    department.text = if (user.department.isNullOrEmpty()) {
                        user.position
                    } else {
                        user.department + " " + user.position
                    }
                    val params = linearContainer.layoutParams as ConstraintLayout.LayoutParams
                    params.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                } else if (user.type in singleTypes) {
                    department.text = ""
                    // 修改 linearContainer 约束条件
                    val params = linearContainer.layoutParams as ConstraintLayout.LayoutParams
                    params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                }
                ImageLoader.load(
                    holder.itemView.context,
                    avatar,
                    user.avatar,
                    false,
                    R.drawable.default_person_blue_avatar,
                    R.drawable.default_person_blue_avatar
                )
            }
        }
    }

    override fun getItemCount(): Int {
        return memberList.size
    }

    fun addData(memberList: MutableList<JoyNoteShareUser?>) {
        this.memberList.addAll(memberList)
        notifyItemInserted(this.memberList.size - 1)
    }

    private fun removeData(member: JoyNoteShareUser?) {
        if (member == null) return
        val index = memberList.indexOf(member)
        memberList.remove(member)
        notifyItemRemoved(index)
        member.permissionType = SharePermission.DELETE.permission
        updateUser(member, true)
        if (memberList.isEmpty()) viewModel.clearMember()
    }

    fun updateData(member: JoyNoteShareUser?) {
        val index = memberList.indexOfFirst { member?.userId == it?.userId }
        memberList.getOrNull(index)?.let {
            memberList[index] = member
        }
        notifyItemChanged(
            index,
            listOf(member?.permissionType ?: SharePermission.READER.permission)
        )
        if (member != null) {
            updateUser(member, false)
        }
    }

    fun getShareUserList(): MutableList<JoyNoteShareUser?> = memberList

    private fun showPermissionDialog(context: Context, user: JoyNoteShareUser) {
        // 权限选项列表
        val permissionOptions = when {
            permissions.contains(ADD_OWNER) -> listOf(
                SharePermission.CREATOR to R.string.joynote_creator,
                SharePermission.EDITOR to R.string.joynote_can_edit,
                SharePermission.READER to R.string.joynote_can_read
            )

            permissions.contains(ADD_EDITABLE) -> listOf(
                SharePermission.EDITOR to R.string.joynote_can_edit,
                SharePermission.READER to R.string.joynote_can_read
            )

            else -> listOf(
                SharePermission.READER to R.string.joynote_can_read
            )
        }

        // 转换权限选项为 MultiAction
        val actions = permissionOptions.map { (permission, stringRes) ->
            BottomAction(
                title = context.getString(stringRes),
                isSelect = user.permissionType == permission.permission,
                data = permission
            )
        }.toMutableList()

        // 移除操作
        val bottomAction = BottomAction(
            title = context.getString(R.string.joynote_remove_person),
            titleColor = context.getColor(R.color.color_F63218),
            isSelect = false,
            data = SharePermission.DELETE
        )
        val deleteActionList =
            if (fixedShowDelete || permissions.contains(ADD_OWNER)) listOf(bottomAction) else emptyList()
        val builder = JoyBottomSheetDialog.Builder<SharePermission>(context).apply {
            setActions(actions, deleteActionList)
            setCheckStyle(R.style.BottomSheetDialogCheck_Tick)
            setOnItemClickListener { action, _ ->
                when (val sharePermission = action.data) {
                    SharePermission.DELETE -> removeData(user)
                    else -> updateData(user.copy(permissionType = sharePermission.permission))
                }
            }
        }
        // 对话框
        JoyBottomSheetDialog(context, builder).show()
    }

    // 协作人、分享 Member 成员 type 不一样，需要根据实际场景获取 type
    open val multiTypes: List<Int> = listOf(MemberEntityJd.TYPE_CONTACT)
    open val singleTypes: List<Int> = listOf(MemberEntityJd.TYPE_GROUP)

    // 删除选项 权限管理 非所有者无法删除；分享页固定展示 delete 选项
    open val fixedShowDelete: Boolean = true

    inner class MemberViewHolder(val binding: ItemShareMemberLayoutBinding) :
        RecyclerView.ViewHolder(binding.root)
}