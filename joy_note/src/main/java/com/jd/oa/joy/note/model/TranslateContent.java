package com.jd.oa.joy.note.model;

import androidx.annotation.Keep;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Keep
public class TranslateContent {

    private String id;

    private List<ContentText> textList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<ContentText> getTextList() {
        return textList;
    }

    public void setTextList(List<ContentText> textList) {
        this.textList = textList;
    }

    Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("textList", textList);
        return map;
    }
}

