package com.jd.oa.joy.note.model;

import java.util.List;

public class RecordParagraph {

    public String paragraph;
    public Long gmtModified;
    public String avatarUrl;
    public String paragraphId;
    public Long startTime;
    public String noteId;
    public Integer endTime;
    public Long gmtCreate;
    public String userName;
    public String userId;
    public List<ContentDTO> content;
    public String paragraphTranslate;

    public static class ContentDTO {
        public String sentence;
        public Integer startTime;
        public Integer endTime;
        public String userId;
    }
}
