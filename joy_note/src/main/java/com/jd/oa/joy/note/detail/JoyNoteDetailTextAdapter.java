package com.jd.oa.joy.note.detail;

import static android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.BackgroundColorSpan;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.jd.oa.joy.note.DateTimeUtils;
import com.jd.oa.joy.note.KeyWordUtil;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.TextDrawable;
import com.jd.oa.joy.note.model.JoyNoteAsrData;
import com.jd.oa.joy.note.model.JoyNoteKeywordPosition;
import com.jd.oa.joy.note.model.JoyNoteLanguageModel;
import com.jd.oa.joy.note.model.JoyNoteSentencePosition;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Utils;

import java.util.List;

public class JoyNoteDetailTextAdapter extends RecyclerView.Adapter<JoyNoteDetailTextAdapter.BaseDetailViewHolder> {
    private static final String TAG = "JoyNoteDetailTextAdapte";

    public static final int REFRESH_TEXT = 1;
    public static final int REFRESH_STATE = 2;

    private List<JoyNoteAsrData> mList;
    private Context mContext;
    private int viewType;

    private String Keyword = "";

    private boolean isFromJoySpace = false;

    public TextView highLightTv;
    //    public TextView lastKeywordTv;
    private int lastHighStartIndex;
    private int lastHighEndIndex;
    private boolean isAudio = false;
    private JoyNoteKeywordPosition joyNoteKeywordPosition;
    private OnDetailItemClickListener onDetailItemClickListener;

    public JoyNoteLanguageModel languageModel;

    public void setJoyNoteKeywordPosition(JoyNoteKeywordPosition joyNoteKeywordPosition) {
        this.joyNoteKeywordPosition = joyNoteKeywordPosition;
    }

    public JoyNoteDetailTextAdapter(List<JoyNoteAsrData> mList, Context mContext, int viewType) {
        this.mList = mList;
        this.mContext = mContext;
        this.viewType = viewType;
    }


    public void setKeyword(String keyword) {
        Keyword = keyword;
    }

    public JoyNoteAsrData getItemAt(int index) {
        return mList.get(index);
    }

    @NonNull
    @Override
    public BaseDetailViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        BaseDetailViewHolder holder;
        if (viewType == JoyNoteBaseSegmentFragment.JOY_NOTE_ASR) {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(
                    R.layout.joy_note_item_detail_text_list,
                    viewGroup,
                    false
            );
            holder = new ViewHolder(view);
        } else {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(
                    R.layout.joy_note_item_mentions_list,
                    viewGroup,
                    false
            );
            holder = new MentionsViewHolder(view);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull BaseDetailViewHolder holder, int position) {

    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void onBindViewHolder(@NonNull BaseDetailViewHolder viewHolder, int i, @NonNull List<Object> payloads) {
        JoyNoteAsrData detail = mList.get(i);
        if (detail == null) return;
        if (isFromJoySpace && viewHolder instanceof ViewHolder) {
            ((ViewHolder) viewHolder).llMerge.setVisibility(View.VISIBLE);
            ((ViewHolder) viewHolder).llMerge.setOnClickListener(v -> {
                if (onDetailItemClickListener != null)
                    onDetailItemClickListener.onMerge(detail.getAllSentences(null));
            });
        }
        if (viewHolder instanceof MentionsViewHolder) {
            ((MentionsViewHolder) viewHolder).jumpOriginal.setOnClickListener(v -> {
                if (onDetailItemClickListener != null) {
                    onDetailItemClickListener.onOriginalClick(detail.paragraphId);
                }
            });
        }
        viewHolder.tvTime.setText(DateTimeUtils.getShowTextByMs2(detail.beginMs));
        if (detail.user != null) {
            if ("FORMAL".equalsIgnoreCase(detail.user.userType) || "VIRTUAL".equalsIgnoreCase(detail.user.userType)) {
                viewHolder.ivPhoto.setOnClickListener(v -> {
                    if (onDetailItemClickListener != null) {
                        onDetailItemClickListener.onUserClick(detail.user);
                    }
                });
            }
            if (isAudio) {
                if (LocaleUtils.getUserSetLocaleStr(mContext).contains("zh")) {
                    viewHolder.tvName.setText(getName(detail.user.realName, detail.user.account));
                } else {
                    viewHolder.tvName.setText(getName(detail.user.account, detail.user.realName));
                }
            } else {
                viewHolder.tvName.setText(getName(detail.user.realName, ""));
            }
            if ("CUSTOM".equalsIgnoreCase(detail.user.userType) && !TextUtils.isEmpty(viewHolder.tvName.getText())) {
                TextDrawable textDrawable = TextDrawable.builder()
                        .beginConfig()
                        .textColor(Color.WHITE)
                        .useFont(Typeface.DEFAULT)
                        .fontSize(Utils.dip2px(mContext, 16)) /* size in px*/
                        .toUpperCase()
                        .endConfig()
                        .buildRound(viewHolder.tvName.getText().toString().substring(0, 1), Color.parseColor("#CECECE"));
                viewHolder.ivPhoto.setImageDrawable(textDrawable);
            } else {
                if (payloads.isEmpty()) {
                    Glide.with(mContext).load(detail.user.imageUrl)
                            .error(R.drawable.joynote_avatar_default)
                            .transform(new CenterCrop(), new RoundedCorners(100))
                            .into(viewHolder.ivPhoto);
                }
            }
        } else {
            if (payloads.isEmpty()) {
                Glide.with(mContext).load(R.drawable.joynote_avatar_default)
                        .transform(new CenterCrop(), new RoundedCorners(100))
                        .into(viewHolder.ivPhoto);
            }
        }
        viewHolder.tvContent.setHighlightColor(mContext.getResources().getColor(android.R.color.transparent));
        viewHolder.tvContent.setOnLongClickListener(v -> {
            onDetailItemClickListener.onSentenceLongClick(viewHolder.tvContent, detail.paragraphId);
            return true;
        });
        if (!TextUtils.isEmpty(Keyword)) {
            int keywordStartIndex = -1;
            if (joyNoteKeywordPosition != null && joyNoteKeywordPosition.segmentPosition == i) {
                keywordStartIndex = joyNoteKeywordPosition.startIndex;
            }
            keywordHighLight(detail.getAllSentences(null), Keyword, viewHolder.tvContentKeyword, keywordStartIndex);
        } else {
            viewHolder.tvContentKeyword.setText(detail.getAllSentences(languageModel));
        }
        addClickHighLight(detail.getAllSentences(languageModel), viewHolder.tvContent, detail.sentences, languageModel);
        if (joyNoteSentencePosition != null && joyNoteSentencePosition.segmentPosition == i) {
            showBlueHighLight(viewHolder.tvContent, joyNoteSentencePosition.startIndex, joyNoteSentencePosition.endIndex);
        }
        if (languageModel != null) {
            int state = detail.getTranslateState(languageModel);
            if (state == JoyNoteAsrData.TRANSLATE_STATE_TRANSLATING) {
                viewHolder.state.setVisibility(View.VISIBLE);
                viewHolder.progress.setVisibility(View.VISIBLE);
                viewHolder.failed.setVisibility(View.INVISIBLE);
            } else if (state == JoyNoteAsrData.TRANSLATE_STATE_FAILURE) {
                viewHolder.state.setVisibility(View.VISIBLE);
                viewHolder.progress.setVisibility(View.INVISIBLE);
                viewHolder.failed.setVisibility(View.VISIBLE);
            } else {
                viewHolder.state.setVisibility(View.INVISIBLE);
            }
        } else {
            viewHolder.state.setVisibility(View.INVISIBLE);
        }

        viewHolder.state.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int state = detail.getTranslateState(languageModel);
                if (state == JoyNoteAsrData.TRANSLATE_STATE_FAILURE) {
                    onDetailItemClickListener.onFailedClick(detail);
                }
            }
        });
    }

    public void addClickHighLight(String text, TextView textView, List<JoyNoteAsrData.Sentences> sens, JoyNoteLanguageModel languageModel) {
        try {
            SpannableStringBuilder sb = new SpannableStringBuilder(text);
            int index = 0;
            for (JoyNoteAsrData.Sentences s : sens) {
                int endIndex = index + s.getText(languageModel).length();
                int finalIndex = index;
                sb.setSpan(new ClickableSpan() {
                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(Color.parseColor("#000000"));
                        ds.setUnderlineText(false);
                    }

                    @Override
                    public void onClick(@NonNull View widget) {
                        showBlueHighLight(textView, finalIndex, endIndex);
                        if (onDetailItemClickListener != null) {
                            onDetailItemClickListener.onSentenceClick(s);
                        }
                    }
                }, index, endIndex, SPAN_EXCLUSIVE_EXCLUSIVE);
                index = endIndex;
            }
            textView.setText(sb);
            textView.setMovementMethod(new InterceptLinkMovementMethod());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showBlueHighLight(TextView textView, int startIndex, int endIndex) {
        if (textView == null ||
                (textView == highLightTv && startIndex == lastHighStartIndex && endIndex == lastHighEndIndex)) {
            //重复高亮同一区域
            return;
        }
        clearHighLight();
        SpannableStringBuilder stringBuilder = new SpannableStringBuilder(textView.getText());
        try {
            stringBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#FFFFFF")), startIndex, endIndex, SPAN_EXCLUSIVE_EXCLUSIVE);
            stringBuilder.setSpan(new BackgroundColorSpan(Color.parseColor("#1869F5")), startIndex, endIndex, SPAN_EXCLUSIVE_EXCLUSIVE);
            highLightTv = textView;
            lastHighStartIndex = startIndex;
            lastHighEndIndex = endIndex;
        } catch (Exception e) {
            e.printStackTrace();
        }

        textView.setText(stringBuilder);
    }

    public void clearHighLight() {
        if (highLightTv != null) {
            //清除之前高亮的句子
            SpannableStringBuilder sb = new SpannableStringBuilder(highLightTv.getText());
            BackgroundColorSpan[] backgroundColorSpans = sb.getSpans(0, highLightTv.getText().length(), BackgroundColorSpan.class);
            ForegroundColorSpan[] foregroundColorSpans = sb.getSpans(0, highLightTv.getText().length(), ForegroundColorSpan.class);
            try {
                sb.removeSpan(backgroundColorSpans[0]);
                sb.removeSpan(foregroundColorSpans[0]);
            } catch (Exception e) {
                e.printStackTrace();
            }
            highLightTv.setText(sb);
        }
    }


    public void keywordHighLight(String text, String keyword, TextView tv, int selectedIndex) {
        SpannableStringBuilder stringBuilder = new SpannableStringBuilder(text);
        List<Integer> allKeywordIndex = KeyWordUtil.getAllKeywordIndex(text, keyword);
        for (Integer i : allKeywordIndex) {
            stringBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#000000")), i, i + keyword.length(), SPAN_EXCLUSIVE_EXCLUSIVE);
            stringBuilder.setSpan(new BackgroundColorSpan(Color.parseColor(i == selectedIndex ? "#FFA53D" : "#FFCF33")), i, i + keyword.length(), SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        tv.setText(stringBuilder);
    }

    public void setFromJoySpace(boolean fromJoySpace) {
        isFromJoySpace = fromJoySpace;
    }

    public void setIsAudio(boolean isAudio) {
        this.isAudio = isAudio;
    }


    private String getName(String name1, String name2) {
        if (!TextUtils.isEmpty(name1)) {
            return name1;
        } else if (!TextUtils.isEmpty(name2)) {
            return name2;
        }
        return "";
    }

    @Override
    public int getItemCount() {
        return mList == null ? 0 : mList.size();
    }

    private JoyNoteSentencePosition joyNoteSentencePosition;

    public void setVideoPosition(JoyNoteSentencePosition joyNoteSentencePosition) {
        this.joyNoteSentencePosition = joyNoteSentencePosition;
    }

    public void setOnDetailItemClickListener(OnDetailItemClickListener onDetailItemClickListener) {
        this.onDetailItemClickListener = onDetailItemClickListener;
    }

    public void refreshDatList(JoyNoteLanguageModel languageModel, List<JoyNoteAsrData> data, Object payload) {
        this.languageModel = languageModel;
//        for (int i = 0; i < data.size(); i++) {
//            JoyNoteAsrData joyNoteAsrData = data.get(i);
//            int index = -1;
//            for (int j = 0; j < mList.size(); j++) {
//                JoyNoteAsrData asrData = mList.get(j);
//                if (Objects.equals(asrData.paragraphId,joyNoteAsrData.paragraphId)) {
//                    index = j;
//                    break;
//                }
//            }
//            if (index != -1) {
//                notifyItemChanged(index, payload);
//            }
//        }
        notifyDataSetChanged();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void resetDefaultLanguage() {
        this.languageModel = null;
        this.notifyDataSetChanged();
    }

    public static class BaseDetailViewHolder extends RecyclerView.ViewHolder {
        TextView tvName, tvTime, tvContent, tvContentKeyword;
        ImageView ivPhoto;
        View state;
        View progress;
        View failed;

        public BaseDetailViewHolder(@NonNull View itemView) {
            super(itemView);

            tvName = itemView.findViewById(R.id.tv_name);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvContent = itemView.findViewById(R.id.tv_content);
            ivPhoto = itemView.findViewById(R.id.iv_photo);
            tvContentKeyword = itemView.findViewById(R.id.tv_content_keyword);
            state = itemView.findViewById(R.id.loading);
            progress = itemView.findViewById(R.id.pb_progress);
            failed = itemView.findViewById(R.id.iv_failed);
        }
    }

    static class ViewHolder extends BaseDetailViewHolder {
        View llMerge;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            llMerge = itemView.findViewById(R.id.ll_merge);
        }
    }

    static class MentionsViewHolder extends BaseDetailViewHolder {
        IconFontView jumpOriginal;

        public MentionsViewHolder(@NonNull View itemView) {
            super(itemView);
            jumpOriginal = itemView.findViewById(R.id.jump_original_text);
        }
    }
}
