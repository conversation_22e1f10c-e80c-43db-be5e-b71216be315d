package com.jd.oa.joy.note.detail

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.eventbus.JmEventDispatcher.dispatchEvent
import com.jd.oa.eventbus.JmEventVoidReturnProcessor
import com.jd.oa.joy.note.viewmodel.JoyNoteMediaPlayerViewModel
import org.json.JSONObject

class JoyNoteSummaryProcessor(
    val fragment: Fragment
): JmEventVoidReturnProcessor<JSONObject>(
    JS_SKD_EVENT_JOY_NOTE_VIDEO_SEEK_TO,
    JS_SKD_EVENT_JOY_NOTE_LANGUAGE_TO_ORIGINAL
) {
    private val viewModel: JoyNoteMediaPlayerViewModel by lazy {
        ViewModelProvider(fragment.requireActivity())[JoyNoteMediaPlayerViewModel::class.java]
    }

    override fun processEvent(context: Context, event: String, args: JSONObject?) {
        val jsonObject = args as JSONObject
        MELogUtil.localI(TAG, "JsEvent sendEvent args: $jsonObject")
        val action = jsonObject.optString("eventId")
        when(action) {
            JS_SKD_EVENT_JOY_NOTE_LANGUAGE_TO_ORIGINAL -> {
                if (!fragment.isResumed) return
                val data = args.optString("data")
                val dataObject = JSONObject(data)
                val content = dataObject.optString("content")
                val minutesId = dataObject.optString("minutesId")
                fragment.requireActivity().runOnUiThread {
                    val dialogFragment = BottomDialogFragment.newInstance(content)
                    dialogFragment.setOnDismissListener {
                        dispatchEvent<JSONObject, Any>(
                            fragment.requireActivity(),
                            JOY_NOTE_LANGUAGE_ORIGINAL_LANGUAGE_CLOSE,
                            null,
                            null
                        )
                    }
                    dialogFragment.show(fragment.childFragmentManager, null)
                }
            }
            JS_SKD_EVENT_JOY_NOTE_VIDEO_SEEK_TO -> {
                val data = args.optJSONObject("data")
                val time = data?.optInt("time")
                val minutesId = data?.optString("minutesId")
                if (time != null && minutesId != null) {
                    viewModel.setMediaProgress(time, sourceSelf = false, sourceTimeLine = true)
                }
            }
        }
    }

    companion object {
        private const val TAG = "JoyNoteSummaryProcessor"
        private const val JS_SKD_EVENT_JOY_NOTE_VIDEO_SEEK_TO = "JSSKD_EVENT_JOYNOTE_VIDEO_SEEK_TO"
        // 慧记-智能总结-通知 H5 收起原文弹窗
        private const val JOY_NOTE_LANGUAGE_ORIGINAL_LANGUAGE_CLOSE: String =
            "NATIVE_EVENT_JOYNOTE_LANGUAGE_ORIGINAL_LANGUAGE_CLOSE"
        // H5 通知 native 长按选择查看原文
        private const val JS_SKD_EVENT_JOY_NOTE_LANGUAGE_TO_ORIGINAL =
            "JSSKD_EVENT_JOYNOTE_LANGUAGE_TO_ORIGINAL"
    }
}
