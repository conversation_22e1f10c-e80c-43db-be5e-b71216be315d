package com.jd.oa.joy.note.notefloat;

import static com.jd.oa.Constant.SHORTCUT_ACTION;
import static com.jd.oa.asr.AsrService.CLASS_NAME;
import static com.jd.oa.joy.note.detail.JoyNoteDetailActivity.updateStatus;
import static com.jd.oa.joy.note.model.VideoItemModel.RECORDING;
import static com.jd.oa.joy.note.model.VideoItemModel.RECORD_STOP;
import static com.jd.oa.joy.note.record.RecordAudioService.ACTION_RECORD_AUDIO;
import static com.jd.oa.joy.note.translate.PlayControlFragment.MAX_AUDIO_DURATION;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.AppBase;
import com.jd.oa.joy.note.BaseFloatingService;
import com.jd.oa.joy.note.DateTimeUtils;
import com.jd.oa.joy.note.record.AudioRecordCurrent;
import com.jd.oa.joy.note.record.AudioRecordUtil;
import com.jd.oa.joy.note.record.RecordAction;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.ui.voice.VoiceWaveView;
import com.jd.oa.ui.voice.WaveMode;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;


public class NoteFloatService extends BaseFloatingService {
    private static final String TAG = "NoteFloatService";
    static final String ARG_ACTION = "arg.action_note_float";
    static final String ARG_VIEW_LOCATION_X = "arg.view_note_float.location.x";
    static final String ARG_VIEW_LOCATION_Y = "arg.view_note_float.location.y";

    static final String ACTION_SHOW_DEFAULT = "action_show_note_float";
    static final String ACTION_HIDE_DEFAULT = "action_hide_note_float";
    static final String ACTION_RESET = "action_reset_note_float";

    private static final int STATE_REMOVE = 1;
    private static final int STATE_ADD = 2;

    private WindowManager mWindowManager;
    private FloatView mFloatView;
    private final NoteFloatManager noteFloatManager = NoteFloatManager.getInstance();
    private BroadcastReceiver mConfigurationChangeReceiver;
    private int mLastX;
    private int mLastY;
    private long mDuration;
    private int mState = STATE_REMOVE;
    private VoiceWaveView waveView1;
    private VoiceWaveView waveView2;
    private VoiceWaveView waveView3;
    private VoiceWaveView waveView4;
    private VoiceWaveView waveView5;
    private VoiceWaveView waveView6;
    private LinearLayout waveGroup;
    private IconFontView exceptionIcon;
    private IconFontView control;
    private TextView tv_time;

    @Override
    public boolean isGlobalWork() {
        return false;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "onCreate");
        if (mFloatView == null) {
            createView(this);
        }
        //安卓平板转屏
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            mConfigurationChangeReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (TabletUtil.isEasyGoEnable() && ViewCompat.isAttachedToWindow(mFloatView)) {
                        mFloatView.onConfigurationChanged();//service的config不准确，无法用来检测分屏
                    }
                }
            };
            LocalBroadcastManager.getInstance(this).registerReceiver(
                    mConfigurationChangeReceiver,
                    new IntentFilter(TabletUtil.ACTION_SPLIT_MODE_CHANGE)
            );
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand: ");
        if (intent == null) return super.onStartCommand(null, flags, startId);
        String action = intent.getStringExtra(ARG_ACTION);
        if (action == null) throw new IllegalArgumentException("action is null!");
        switch (action) {
            case ACTION_SHOW_DEFAULT:
                if (TabletUtil.isFold()) {
                    new Handler(Looper.getMainLooper()).post(this::showFloatingView);
                } else {
                    showFloatingView();
                }
                break;
            case ACTION_HIDE_DEFAULT:
                hideFloatingView();
                break;
            case ACTION_RESET:
                resetPosition();
                break;
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy: ");
        super.onDestroy();
        hideFloatingView();
        if (mConfigurationChangeReceiver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mConfigurationChangeReceiver);
            mConfigurationChangeReceiver = null;
        }
        stopWave();
        if (mFloatView != null) {
            mFloatView.removeAllViews();
        }
        mFloatView = null;
    }

    @Override
    public void startPlay() {
        if (mFloatView == null) {
            createView(this);
        }
        control.setText(R.string.icon_float_pause);
        control.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
        waveGroup.setVisibility(View.VISIBLE);
        exceptionIcon.setVisibility(View.GONE);
        startWave();
    }

    @Override
    public void stopPlay() {
        if (mFloatView == null) {
            createView(this);
        }
        control.setText(R.string.icon_edit_voice3);
        control.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
        stopWave();
    }

    @Override
    public void showFloatingView() {
        Log.d(TAG, "showFloatingView");
        if (!Settings.canDrawOverlays(this)) {
            Log.e(TAG, "showFloatingView, draw overlays permission denied!");
            return;
        }
        if (!AppJoint.service(AppService.class).isForeground()) {
            return;
        }
        int centerX = ScreenUtil.getScreenWidth(AppBase.getAppContext());
        int centerY = ScreenUtil.getScreenHeight(AppBase.getAppContext());
        int x = mLastX == 0 ? centerX : mLastX;
        int y = mLastY == 0 ? centerY / 2 : mLastY;
        if (ViewCompat.isAttachedToWindow(mFloatView) || mState == STATE_ADD) return;
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) mFloatView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = generateDefaultLayoutParams();
            layoutParams.x = x;
            layoutParams.y = y;
        }
        try {
            mFloatView.clearAnimation();
            mWindowManager.addView(mFloatView, layoutParams);
            mState = STATE_ADD;
        } catch (Exception e) {
            e.printStackTrace();
        }
        switch (AudioRecordCurrent.INSTANCE.getCurrentStatus()) {
            case RECORDING:
                startPlay();
                break;
            case PAUSE:
                stopPlay();
                if (TextUtils.isEmpty(tv_time.getText())) {
                    tv_time.setText(DateTimeUtils.getShowTextByMs3(
                            (long) AudioRecordUtil.INSTANCE.getDuration() * 1000)
                    );
                }
                break;
        }
    }

    private WindowManager.LayoutParams generateDefaultLayoutParams() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        return layoutParams;
    }

    @Override
    public void hideFloatingView() {
        Log.d(TAG, "hideView");
        if (mFloatView == null) return;
        if (mWindowManager == null) return;
        if (ViewCompat.isAttachedToWindow(mFloatView)) {
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) mFloatView.getLayoutParams();
            mLastX = layoutParams.x;
            mLastY = layoutParams.y;
            try {
                mFloatView.clearAnimation();
                mWindowManager.removeViewImmediate(mFloatView);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        mState = STATE_REMOVE;
    }

    @Override
    public void resetPosition() {
        if (mState == STATE_ADD) {
            if (mFloatView != null && ViewCompat.isAttachedToWindow(mFloatView)) {
                mFloatView.reset();
            }
        }
    }

    @Override
    public void notifyException(@Nullable String errorType, @Nullable String errorMsg) {
        if (mFloatView == null) {
            createView(this);
        }
        waveGroup.setVisibility(View.GONE);
        exceptionIcon.setVisibility(View.VISIBLE);
    }

    @Override
    public void notifyDurationChange(long duration) {
        if (mFloatView == null) {
            createView(this);
        }
        mDuration = duration;
        tv_time.setText(DateTimeUtils.getShowTextByMs3(duration));
    }

    private void createView(final Context context) {
        mFloatView = FloatView.create(context);
        mFloatView.setOnClickListener(new AvoidFastClickListener(500) {
            @Override
            public void onAvoidedClick(View view) {
                noteFloatManager.showRecodingPage(context);
            }
        });
        mWindowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        mFloatView.setWindowManager(mWindowManager);
        control = mFloatView.getFloatChildView().findViewById(R.id.iftv_control);
        control.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                Intent intent = new Intent(ACTION_RECORD_AUDIO);
                intent.putExtra(CLASS_NAME, NoteFloatService.class.getName());
                switch (AudioRecordCurrent.INSTANCE.getCurrentStatus()) {
                    case RECORDING:
                        intent.putExtra(SHORTCUT_ACTION, RecordAction.PAUSE.name());
                        updateStatus(RECORD_STOP, AudioRecordCurrent.INSTANCE.getMinuteId(), context);
                        break;
                    case PAUSE:
                        if (mDuration >= MAX_AUDIO_DURATION) {
                            ToastUtils.showToast(com.jd.oa.joy.note.R.string.joynote_is_over_time_for_record);
                            return;
                        }
                        intent.putExtra(SHORTCUT_ACTION, RecordAction.RESUME.name());
                        updateStatus(RECORDING, AudioRecordCurrent.INSTANCE.getMinuteId(), context);
                        break;
                    default:
                        break;
                }
                LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
            }
        });

        waveView1 = mFloatView.getFloatChildView().findViewById(R.id.float_wave1);
        waveView2 = mFloatView.getFloatChildView().findViewById(R.id.float_wave2);
        waveView3 = mFloatView.getFloatChildView().findViewById(R.id.float_wave3);
        waveView4 = mFloatView.getFloatChildView().findViewById(R.id.float_wave4);
        waveView5 = mFloatView.getFloatChildView().findViewById(R.id.float_wave5);
        waveView6 = mFloatView.getFloatChildView().findViewById(R.id.float_wave6);
        tv_time = mFloatView.getFloatChildView().findViewById(R.id.tv_time);
        waveGroup = mFloatView.getFloatChildView().findViewById(R.id.voice_wave_group);
        exceptionIcon = mFloatView.getFloatChildView().findViewById(R.id.exception_icon);
    }

    private void startWave() {
        waveView1.getBodyWaveList().clear();
        waveView2.getBodyWaveList().clear();
        waveView3.getBodyWaveList().clear();
        waveView4.getBodyWaveList().clear();
        waveView5.getBodyWaveList().clear();
        waveView6.getBodyWaveList().clear();
        waveView1.setWaveMode(WaveMode.UP_DOWN);
        waveView2.setWaveMode(WaveMode.UP_DOWN);
        waveView3.setWaveMode(WaveMode.UP_DOWN);
        waveView4.setWaveMode(WaveMode.UP_DOWN);
        waveView5.setWaveMode(WaveMode.UP_DOWN);
        waveView6.setWaveMode(WaveMode.UP_DOWN);
        waveView1.addBody(20);
        waveView2.addBody(50);
        waveView3.addBody(30);
        waveView4.addBody(60);
        waveView5.addBody(20);
        waveView6.addBody(10);
        waveView1.start();
        waveView2.start();
        waveView3.start();
        waveView4.start();
        waveView5.start();
        waveView6.start();
    }

    private void stopWave() {
        waveView1.getBodyWaveList().clear();
        waveView2.getBodyWaveList().clear();
        waveView3.getBodyWaveList().clear();
        waveView4.getBodyWaveList().clear();
        waveView5.getBodyWaveList().clear();
        waveView6.getBodyWaveList().clear();
        waveView1.addBody(0);
        waveView2.addBody(0);
        waveView3.addBody(0);
        waveView4.addBody(0);
        waveView5.addBody(0);
        waveView6.addBody(0);
        waveView1.stop();
        waveView2.stop();
        waveView3.stop();
        waveView4.stop();
        waveView5.stop();
        waveView6.stop();
    }
}