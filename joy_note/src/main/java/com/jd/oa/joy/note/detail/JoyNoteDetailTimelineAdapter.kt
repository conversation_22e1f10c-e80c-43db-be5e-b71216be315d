package com.jd.oa.joy.note.detail

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.model.Scope
import com.jd.oa.joy.note.viewmodel.JoyNoteMediaPlayerViewModel

class JoyNoteDetailTimelineAdapter(
    val context: Context,
    private val mediaPlayerViewModel: JoyNoteMediaPlayerViewModel,
    val totalDuration: Long?,
    val listener: (Scope) -> Unit,
): RecyclerView.Adapter<JoyNoteDetailTimelineAdapter.ViewHolder>() {

    private val mData: MutableList<Scope> = mutableListOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(LayoutInflater.from(context).inflate(R.layout.joy_note_item_paragraph, parent, false))
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data =  mData[position]
        holder.index.text = context.getString(R.string.joynote_speaker_timeline_paragraph_index, position + 1)
        data.startTime?.let {
            val totalHour = if (totalDuration != null) {
                (totalDuration / 1000f / 60 / 60).toInt()
            } else 0
            val totalSeconds = it / 1000
            val hours: Long = totalSeconds / 3600
            val minutes: Long = (totalSeconds % 3600) / 60
            val seconds: Long = totalSeconds % 60
            if (totalHour > 0) {
                holder.time.text = context.getString(R.string.joynote_speaker_timeline_paragraph_time_hour, hours, minutes, seconds)
            } else {
                holder.time.text = context.getString(R.string.joynote_speaker_timeline_paragraph_time, minutes, seconds)
            }
        }
    }

    override fun getItemCount(): Int = mData.size

    fun refresh(paragraph: List<Scope>) {
        mData.clear()
        mData.addAll(paragraph)
        this.notifyDataSetChanged()
    }

    inner class ViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val index: TextView = itemView.findViewById<TextView>(R.id.tv_index)
        val time: TextView = itemView.findViewById<TextView>(R.id.tv_time)
        var clicked: Boolean = false

        init {
            itemView.setOnClickListener {
                val position = bindingAdapterPosition
                if (position == RecyclerView.NO_POSITION) return@setOnClickListener
                clicked = true
                listener.invoke(mData[position])
            }

            mediaPlayerViewModel.mediaInfoLiveData.observe(context as LifecycleOwner) {
                val position = bindingAdapterPosition
                if (position == RecyclerView.NO_POSITION) return@observe
                val data =  mData[position]
                if (data.startTime == null || data.endTime == null) return@observe
                if (clicked || it.first > data.startTime && it.first < data.endTime) {
                    index.isSelected = true
                } else {
                    index.isSelected = false
                }
                clicked = false
            }
        }
    }
}