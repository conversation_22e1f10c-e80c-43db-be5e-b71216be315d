package com.jd.oa.joy.note;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.jd.oa.joy.note.model.JoyNoteMember;
import com.jd.oa.ui.CircleImageView;

import java.util.List;

public class JoyNoteParticipatorAdapter extends RecyclerView.Adapter<JoyNoteParticipatorAdapter.ViewHolder> {

    private List<JoyNoteMember> mList;
    private Context mContext;


    public JoyNoteParticipatorAdapter(List<JoyNoteMember> mList, Context mContext) {
        this.mList = mList;
        this.mContext = mContext;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.joy_note_item_participator, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        JoyNoteMember member = mList.get(i);
        if (member == null) return;
        viewHolder.tvName.setText(member.realName);
        viewHolder.tvDepartment.setText(member.deptFullName);
        viewHolder.tvPost.setText(member.titleName);
        Glide.with(mContext).load(member.imageUrl)
                .error(R.drawable.joynote_avatar_default)
                .into(viewHolder.ivPhoto);
        viewHolder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(member);
            }
        });

    }

    @Override
    public int getItemCount() {
        return mList == null ? 0 : mList.size();
    }


    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvName, tvDepartment, tvPost;
        CircleImageView ivPhoto;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPhoto = itemView.findViewById(R.id.iv_photo);
            tvName = itemView.findViewById(R.id.tv_name);
            tvDepartment = itemView.findViewById(R.id.tv_department);
            tvPost = itemView.findViewById(R.id.tv_post);
        }
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(JoyNoteMember member);
    }
}
