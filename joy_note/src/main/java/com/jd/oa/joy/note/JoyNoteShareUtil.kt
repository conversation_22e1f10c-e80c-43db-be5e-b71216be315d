package com.jd.oa.joy.note

import android.app.Activity
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.im.listener.Callback
import com.jd.oa.joy.note.share.JoyNoteShareActivity
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.preference.PreferenceManager

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/11
 * @Description:
 */
object JoyNoteShareUtil {
    private const val MAX_VALUE = 500

    fun share(
        activity: Activity?,
        mid: String?,
        permissions: List<String>? = emptyList(),
        launcher: ActivityResultLauncher<Intent>?
    ) {
        selectMember(activity, mid) { memberList ->
            // 分享限制选择自己
            memberList?.filter { !it.isSelf() }?.let {
                // 启动分享列表活动页面
                val intent = Intent(activity, JoyNoteShareActivity::class.java).apply {
                    putExtra(JoyNoteShareActivity.KEY_MID, mid)
                    permissions?.let {
                        putStringArrayListExtra(
                            JoyNoteShareActivity.PERMISSIONS,
                            ArrayList(it)
                        )
                    }
                    putParcelableArrayListExtra(
                        JoyNoteShareActivity.KEY_MEMBER_LIST,
                        ArrayList(it)
                    )
                }
                launcher?.launch(intent) ?: activity?.startActivityForResult(
                    intent,
                    JoyNoteShareActivity.KEY_REQUEST_CODE
                )
            }
        }
    }

    fun selectMember(
        activity: Activity?,
        mid: String?,
        filterMembers: ArrayList<MemberEntityJd?>? = arrayListOf(),
        callback: (ArrayList<MemberEntityJd?>?) -> Unit
    ) {
        if (activity == null || mid == null) return
        val service = AppJoint.service(ImDdService::class.java)
        val memberListEntityJd = MemberListEntityJd()
        memberListEntityJd
            .setFrom(UIHelperConstantJd.TYPE_SHARE)
            .setShowConstantFilter(true)
            .setConstantFilter(filterMembers)
            .setShowSelf(false) // FIXME 该配置 7.9.50 迭代存在bug，下个迭代修复
            .setOptionalFilter(null)
            .setShowOptionalFilter(false)
            .setSelectMode(MemberListEntityJd.SELECT_MODE_MULTI)
            .setSpecifyAppId(TenantConfigBiz.getCollaborativelyApps())
            .setMaxNum(MAX_VALUE - (filterMembers?.size ?: 0))

        service.gotoMemberList(
            activity,
            100,
            memberListEntityJd,
            object : Callback<ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(bean: ArrayList<MemberEntityJd?>?) {
                    callback(bean)
                }

                override fun onFail() {
                    callback(null)
                }
            })
    }

    fun MemberEntityJd?.isSelf(): Boolean {
        return this?.let {
            id.equals(PreferenceManager.UserInfo.getUserName(), true)
                    && app.equals(PreferenceManager.UserInfo.getTimlineAppID(), true)
        } ?: false
    }
}
