package com.jd.oa.joy.note.detail;

import static com.jd.oa.joy.note.repository.JoyNoteDetailRepo.ERROR_CODE_DELETE;
import static com.jd.oa.joy.note.repository.JoyNoteDetailRepo.ERROR_CODE_DESTROY;
import static com.jd.oa.joy.note.repository.JoyNoteDetailRepo.ERROR_CODE_IS_RECORDING;
import static com.jd.oa.joy.note.repository.JoyNoteDetailRepo.ERROR_CODE_IS_RECORDING_BY_ME;
import static com.jd.oa.joy.note.repository.JoyNoteDetailRepo.ERROR_CODE_NO_PERMISSION;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel;
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.DeepLink;

import org.json.JSONObject;

@Route(DeepLink.JOY_NOTE_DETAIL)
public class JoyNoteDetailActivity extends BaseActivity {

    public String minutesId;
    public String channel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().setStatusBarColor(Color.WHITE);
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_joy_note_detail);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        if (savedInstanceState == null) {
            getDetail();
        }
    }

    public JoyNoteDetailInfoModel joyNoteDetailInfoModel;

    public void getDetail() {
        minutesId = getIntent().getStringExtra("minutesId");
        if (getIntent() != null && getIntent().hasExtra("mparam")
                && !TextUtils.isEmpty(getIntent().getStringExtra("mparam"))) {
            try {
                String mparam = getIntent().getStringExtra("mparam");
                minutesId = new JSONObject(mparam).getString("minutesId").trim();
                channel = new JSONObject(mparam).optString("channel", "");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        JoyNoteDetailRepo.getRepo().getDetail(minutesId, new LoadDataCallback<JoyNoteDetailInfoModel>() {
            @Override
            public void onDataLoaded(JoyNoteDetailInfoModel joyNoteDetailInfoModel) {
                if (isFinishing() || isDestroyed()) {
                    return;
                }
                JoyNoteDetailActivity.this.joyNoteDetailInfoModel = joyNoteDetailInfoModel;
                if (joyNoteDetailInfoModel != null && joyNoteDetailInfoModel.minutesDetail != null
                        && "4".equals(joyNoteDetailInfoModel.minutesDetail.status)) {
                    JoyNoteDetailDeletedFragment deletedFragment = new JoyNoteDetailDeletedFragment();
                    getSupportFragmentManager().beginTransaction()
                            .replace(R.id.container, deletedFragment)
                            .commitNowAllowingStateLoss();
                } else {
                    JoyNoteDetailFragment joyNoteDetailFragment = new JoyNoteDetailFragment();
                    Bundle bundle = new Bundle();
                    bundle.putParcelable("info", joyNoteDetailInfoModel);
                    joyNoteDetailFragment.setArguments(bundle);
                    getSupportFragmentManager().beginTransaction()
                            .replace(R.id.container, joyNoteDetailFragment)
                            .commitNowAllowingStateLoss();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isFinishing() || isDestroyed()) {
                    return;
                }
                if (i == ERROR_CODE_NO_PERMISSION) {
                    JoyNoteDetailPermissionFragment joyNoteDetailPermissionFragment = new JoyNoteDetailPermissionFragment();
                    Bundle bundle = new Bundle();
                    bundle.putString("minutesId", minutesId);
                    joyNoteDetailPermissionFragment.setArguments(bundle);
                    getSupportFragmentManager().beginTransaction()
                            .replace(R.id.container, joyNoteDetailPermissionFragment)
                            .commitNowAllowingStateLoss();
                } else if (i == ERROR_CODE_DELETE || i == ERROR_CODE_DESTROY) {
                    JoyNoteDetailDeletedFragment deletedFragment = new JoyNoteDetailDeletedFragment();
                    getSupportFragmentManager().beginTransaction()
                            .replace(R.id.container, deletedFragment)
                            .commitNowAllowingStateLoss();
                } else {
                    JoyNoteDetailErrorFragment joyNoteDetailErrorFragment = new JoyNoteDetailErrorFragment();
                    if (i == ERROR_CODE_IS_RECORDING || i == ERROR_CODE_IS_RECORDING_BY_ME) {
                        Bundle bundle = new Bundle();
                        bundle.putInt("type", i);
                        joyNoteDetailErrorFragment.setArguments(bundle);
                    }
                    getSupportFragmentManager().beginTransaction()
                            .replace(R.id.container, joyNoteDetailErrorFragment)
                            .commitNowAllowingStateLoss();
//                    ToastUtils.showToast("加载失败");
                }
            }
        });
//        JoyNoteDetailRepo.getRepo().getAsr(getIntent().getStringExtra("minutesId"));
    }

    /**
     * 通知joyspace更新状态
     * 0 无效数据无效数据
     * 1 正常数据
     * 2 生成中的数据
     * 3 录制中的数据
     * 4 回收站中数据
     * 5 录制暂停的数据
     */
    public static void updateStatus(int status, String minutesId, Context context) {
        Intent intent = new Intent("action.joynote.update.joyspace");
        intent.putExtra("type", "EVENT_STATUS_CHANGE");
        intent.putExtra("status", status);
        intent.putExtra("minutesId", minutesId);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    public JoyNoteDetailInfoModel getJoyNoteDetailInfoModel() {
        return joyNoteDetailInfoModel;
    }
}