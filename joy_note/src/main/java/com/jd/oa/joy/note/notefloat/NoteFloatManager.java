package com.jd.oa.joy.note.notefloat;


import static com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_MEETING;
import static com.jd.oa.joy.note.notefloat.NoteFloatCallback.shouldHideIt;
import static com.jd.oa.utils.TabletUtil.isFold;
import static com.jd.oa.utils.TabletUtil.isSplitMode;
import static com.jd.oa.utils.TabletUtil.isTablet;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.joy.note.model.VideoItemModel;
import com.jd.oa.joy.note.record.AudioRecordCurrent;
import com.jd.oa.joy.note.record.AudioRecordStatus;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

public class NoteFloatManager {
    private static final String TAG = NoteFloatManager.class.getSimpleName();
    private static final int FLOAT_VIEW_BOTTOM_OFFSET = 200;
    private List<FloatItemInfo> flowItemList = new ArrayList<>();
    private final List<FloatItemInfo> historyTaskInfo = new ArrayList<>();
    private static WeakReference<Activity> oldWindow = null;

    private static class SingletonHolder {
        static NoteFloatManager sInstance = new NoteFloatManager();
    }

    public static NoteFloatManager getInstance() {
        return SingletonHolder.sInstance;
    }

    public static boolean isRnMultiWindows() {
        return false;
    }

    public static boolean isPad() {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return isFold();
        }
        return isSplitMode(activity) || isFold() || isTablet();
    }

    void showRecodingPage(Context context) {
        MELogUtil.localI(TAG, "showRecodingPage");
        Integer type = AudioRecordCurrent.INSTANCE.getType();
        if (type != null) {
            if (type == VideoItemModel.AUDIO) {
                DeepLinkTools.goJoyNotePage(
                        context,
                        AudioRecordCurrent.INSTANCE.getTitle(),
                        AudioRecordCurrent.INSTANCE.getMinuteId(),
                        DeepLink.JOY_NOTE_CREATE
                );
            } else if (type == VideoItemModel.AUDIO_TRANSLATE) {
                DeepLinkTools.getJoyNoteIntent(
                        context,
                        AudioRecordCurrent.INSTANCE.getTitle(),
                        AudioRecordCurrent.INSTANCE.getMinuteId(),
                        DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE
                );
            }
        }
    }

    private void showMainActivity(Activity top) {
        PackageManager packageManager = top.getPackageManager();
        Intent intent = packageManager.getLaunchIntentForPackage(top.getPackageName());
        top.startActivity(intent);
    }

    boolean hideWindowWithoutAddFlowList(Activity activity, boolean showMainActivity, boolean rn) {
        if (activity == null) {
            return true;
        }
        if (isPad()) {
            activity.finish();
            return true;
        }
        if (rn) {
            if (!isRnMultiWindows()) {
                activity.finish();
                return true;
            }
        }
        return moveToBack(activity, showMainActivity);
    }

    private FloatItemInfo findItem(int taskId) {
        try {
            for (int a = flowItemList.size() - 1; a >= 0; a--) {
                FloatItemInfo floatItemInfo = flowItemList.get(a);
                if (floatItemInfo.taskId != null && taskId == floatItemInfo.taskId) {
                    return floatItemInfo;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private boolean moveToBack(Activity activity, boolean showMainActivity) {
        if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
            return true;
        }
        if (showMainActivity) {
            showMainActivity(activity);
        }
        JMAudioCategoryManager jmAudioCategoryManager = JMAudioCategoryManager.getInstance();
        if (jmAudioCategoryManager.getCurrentAudioCategory() != JME_AUDIO_CATEGORY_JOY_MEETING) {
            FloatItemInfo floatItemInfo = findItem(activity.getTaskId());
            if (floatItemInfo != null) {
                activity.moveTaskToBack(true);
            } else {
                activity.finishAndRemoveTask();
            }
        } else {
            activity.moveTaskToBack(true);
        }
        return true;
    }

    public void showFloatingView(Activity activity) {
        AudioRecordStatus status = AudioRecordCurrent.INSTANCE.getCurrentStatus();
        if (status != AudioRecordStatus.RECORDING && status != AudioRecordStatus.PAUSE) {
            return;
        }
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.postDelayed(() -> showFloatingViewWithLocation(null), 300);
            return;
        }
        showFloatingViewWithLocation(activity);
    }

    private void showFloatingViewWithLocation(Activity activity) {
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        if (activity == null) {
            return;
        }
        int[] location = new int[2];
        try {
            floatViewDefaultLocation(activity, location);
            showFloatingView(activity, location[0], location[1]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showFloatingView(Activity activity, int x, int y) {
        try {
            if (shouldHideIt(activity)) {
                return;
            }
            if (!Settings.canDrawOverlays(activity)) return;
            if (AudioRecordCurrent.INSTANCE.getCurrentStatus() == AudioRecordStatus.PAUSE ||
                    AudioRecordCurrent.INSTANCE.getCurrentStatus() == AudioRecordStatus.RECORDING) {
                Intent intent = new Intent(activity, NoteFloatService.class);
                intent.putExtra(NoteFloatService.ARG_ACTION, NoteFloatService.ACTION_SHOW_DEFAULT);
                intent.putExtra(NoteFloatService.ARG_VIEW_LOCATION_X, x);
                intent.putExtra(NoteFloatService.ARG_VIEW_LOCATION_Y, y);
                activity.startService(intent);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void hideFloatingView(Activity activity) {
        if (activity == null) {
            return;
        }
        try {
            if (!Settings.canDrawOverlays(activity)) return;
            Intent intent = new Intent(activity, NoteFloatService.class);
            intent.putExtra(NoteFloatService.ARG_ACTION, NoteFloatService.ACTION_HIDE_DEFAULT);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void logoutFloatingView(Activity activity) {
        try {
            ActivityManager manager = (ActivityManager) AppBase.getAppContext().getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.AppTask> appTaskList = manager.getAppTasks();
            for (ActivityManager.AppTask appTask : appTaskList) {
                for (FloatItemInfo floatItemInfo : historyTaskInfo) {
                    if (floatItemInfo.taskId != null && floatItemInfo.taskId.equals(appTask.getTaskInfo().id)) {
                        appTask.finishAndRemoveTask();
                    }
                }
            }
            if (!Settings.canDrawOverlays(activity)) return;
            Intent intent = new Intent(activity, NoteFloatService.class);
            activity.stopService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    void resetPosition(Activity activity) {
        try {
            Intent intent = new Intent(activity, NoteFloatService.class);
            intent.putExtra(NoteFloatService.ARG_ACTION, NoteFloatService.ACTION_RESET);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void floatViewDefaultLocation(Context context, int[] location) {
        if (location == null || location.length < 2)
            throw new IllegalArgumentException("location illegal");
        int width = isSplitMode(context) ? TabletUtil.getDeviceLongSide() : DisplayUtil.getScreenWidth(context);
        location[0] = width - context.getResources().getDimensionPixelOffset(R.dimen.me_evaluation_float_view_width);
        location[1] = DisplayUtil.getScreenHeight(context)
                - context.getResources().getDimensionPixelOffset(R.dimen.me_bottom_bar_height)
                - DensityUtil.dp2px(context, FLOAT_VIEW_BOTTOM_OFFSET);
    }
}
