package com.jd.oa.joy.note.translate

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.asr.websocket.model.TextMsg
import com.jd.oa.joy.note.databinding.ItemTranslateRecyclerViewBinding
import com.jd.oa.joy.note.model.RecordParagraph
import com.jd.oa.joy.note.record.RecordTextAdapter
import com.jd.oa.joy.note.record.RecordTextModel
import com.jd.oa.utils.DensityUtil
import org.json.JSONObject


/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/29
 * @Description:
 */
class StreamOutputText @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), LifecycleEventObserver {
    private var lastUpdateTime = 0L
    private var newState: Int = RecyclerView.SCROLL_STATE_IDLE
    private var adapter: RecordTextAdapter
    private val textList: MutableList<RecordTextModel> = mutableListOf()
    private var visibleBottom: (() -> Unit)? = null
    private var isProgrammaticScroll: Boolean? = null

    private var binding: ItemTranslateRecyclerViewBinding =
        ItemTranslateRecyclerViewBinding.inflate(
            LayoutInflater.from(context),
            this,
            true
        )

    init {
        adapter = RecordTextAdapter(context, textList)
        initView()
    }

    private fun initView() {
        binding.rvText.adapter = adapter
        binding.rvText.layoutManager =
            SnapToEndLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        binding.rvText.addOnScrollListener(object : OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                <EMAIL> = newState
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val isVisible = isItemBottomVisible()
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING -> {
                        isProgrammaticScroll = false
                        binding.buttonLocation.isVisible = !isVisible
                        if (isVisible && dy >= 0) visibleBottom?.invoke()
                    }

                    RecyclerView.SCROLL_STATE_IDLE, RecyclerView.SCROLL_STATE_SETTLING -> {
                        if (isProgrammaticScroll == false) {
                            binding.buttonLocation.isVisible = !isVisible
                            if (isVisible && dy >= 0) visibleBottom?.invoke()
                        }
                    }
                }
            }
        })
        binding.buttonLocation.setOnClickListener {
            smoothScroll()
            visibleBottom?.invoke()
        }
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (event == Lifecycle.Event.ON_RESUME) {
            smoothScroll()
        }
    }

    private fun isItemBottomVisible(): Boolean {
        val layoutManager = (binding.rvText.layoutManager as? LinearLayoutManager) ?: return false
        if (layoutManager.findLastCompletelyVisibleItemPosition() == 0) return true
        val itemView = layoutManager.findViewByPosition(adapter.itemCount - 1)
        if (itemView != null) {
            return itemView.bottom - DensityUtil.dp2px(
                context,
                40f
            ) <= layoutManager.height - layoutManager.paddingBottom
        }
        return false
    }

    fun smoothScroll() {
        isProgrammaticScroll = true
        binding.buttonLocation.isVisible = false
        val layoutManager = binding.rvText.layoutManager as? LinearLayoutManager ?: return
        val visibleLastIndex = layoutManager.findLastVisibleItemPosition()
        val lastIndex = adapter.itemCount - 1
        if (visibleLastIndex == lastIndex) {
            binding.rvText.smoothScrollToPosition(lastIndex)
        } else {
            binding.rvText.scrollToPosition(lastIndex)
        }
    }

    fun setVisibleViewBottom(callback: () -> Unit) {
        this.visibleBottom = callback
    }

    fun refreshAsrTranslateList(
        textMsg: TextMsg,
        dynamicResult: String,
        stableResult: String,
        hideTip: () -> Unit
    ) {
        if (TextMsg.PARAGRAPH_BEGIN.equals(textMsg.textType, true)) {
            hideTip()
            val recordTextModel = RecordTextModel(dynamicResult, stableResult).apply {
                startTime = textMsg.paragraphStartTime
                userName = textMsg.user?.name ?: ""
                userAvatar = textMsg.user?.avatarUrl ?: ""
            }
            textList.add(recordTextModel)
            adapter.notifyItemRangeChanged(0, adapter.itemCount)
        } else if (textList.isNotEmpty()) {
            hideTip()
            textList[textList.lastIndex].apply {
                stableText += stableResult
                dynamicText = dynamicResult
            }
            // 保证只刷新文本
            adapter.notifyItemChanged(textList.lastIndex, listOf(""))
            if (binding.buttonLocation.isVisible) return
            if (stableResult.isNotEmpty()) {
                smoothScroll()
            } else if (dynamicResult.isNotEmpty()) {
                if (System.currentTimeMillis() - lastUpdateTime > 2000) {
                    lastUpdateTime = System.currentTimeMillis()
                    smoothScroll()
                }
            }
        }
    }

    fun setRecordContent(content: String?, showTranslate: Boolean, hideTip: () -> Unit) {
        if (content.isNullOrEmpty()) {
            textList.add(RecordTextModel())
            return
        }
        runCatching {
            val jb = JSONObject(content)
            val data = jb.getJSONObject("data")
            val paragraphs = data.getJSONObject("paragraphs")
            val list = paragraphs.getJSONArray("list")
            val paragraphList: List<RecordParagraph> = Gson().fromJson(
                list.toString(),
                object : TypeToken<List<RecordParagraph>>() {}.type
            )
            if (paragraphList.isNotEmpty()) {
                hideTip()
                for (gra: RecordParagraph in paragraphList) {
                    val model = RecordTextModel(
                        stableText = if (!showTranslate) gra.paragraph else gra.paragraphTranslate
                    ).apply {
                        userAvatar = gra.avatarUrl
                        userName = gra.userName
                        startTime = gra.startTime
                    }
                    textList.add(model)
                }
                adapter.notifyItemRangeChanged(0, adapter.itemCount)
                handler.post { binding.rvText.scrollToPosition(adapter.itemCount - 1) }
            }
        }.getOrElse {
            textList.add(RecordTextModel())
        }
    }

    inner class SnapToEndLinearLayoutManager(
        context: Context,
        orientation: Int,
        reverseLayout: Boolean
    ) : LinearLayoutManager(context, orientation, reverseLayout) {
        override fun smoothScrollToPosition(
            recyclerView: RecyclerView?,
            state: RecyclerView.State?,
            position: Int
        ) {
            recyclerView?.let {
                if (it.canScrollVertically(1) &&
                    recyclerView.layoutManager?.isSmoothScrolling == false
                ) {
                    val smoothScroller: LinearSmoothScroller =
                        object : LinearSmoothScroller(it.context) {
                            override fun getVerticalSnapPreference(): Int {
                                // 滚动时对齐到底部
                                return SNAP_TO_END
                            }
                        }
                    smoothScroller.targetPosition = position
                    startSmoothScroll(smoothScroller)
                }
            }
        }
    }
}