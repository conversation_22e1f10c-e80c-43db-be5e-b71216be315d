package com.jd.oa.joy.note.model;

import java.util.List;
import java.util.Objects;

/**
 * @noinspection unused
 */
public class VideoItemModel {
    public static final int VIDEO = 1;
    public static final int AUDIO = 2;
    public static final int AUDIO_TRANSLATE = 3;
    public static final int ERROR = 0;
    public static final int FINISH = 1;
    public static final int GENERATION = 2;
    public static final int RECORDING = 3;
    public static final int RECYCLING = 4;
    public static final int RECORD_STOP = 5;
    public static final int RECORD_NO_START = 6;
    public static final String SHARE = "SHARE";
    public static final String EDIT = "EDIT";
    public static final String DELETE_MINUTES = "DELETE_MINUTES";
    public static final String REMOVE_MINUTES = "REMOVE_MINUTES";
    public static final String DESTROY_MINUTES = "DESTROY_MINUTES";
    public static final String RESTORE_MINUTES = "RESTORE_MINUTES";
    public static final String RENAME_MINUTES = "RENAME_MINUTES";
    public static final String ADD_OWNER = "ADD_OWNER";
    public static final String ADD_EDITABLE = "ADD_EDITABLE";
    public static final String NEW_ADD = "NEWADD";
    public static final String RECENT_TYPE = "RECENT";
    public static final String RECEIVED = "RECEIVED";
    public static final String MINUTE_ID = "minuteId";
    public static final String NEW_TITLE_NAME = "newTitle";

    public static final String OPERATION_TYPE = "operationType";
    public static final String ACTION_LIST_UPDATE = "joy.note.list.update";
    public long receiveTime;
    public String summaryImage;
    public UserInfo userInfo;
    public String recordTime;
    public String recordDuration;
    public List<String> permissions;
    public String title;
    public String bizType;
    public String minutesId;
    public String cover;
    public int status;// 0无效数据 1正常数据 2生成中的数据 3录制中的数据 4进入回收站的数据 5录制暂停的数据 6录制未开始的数据
    public int type;

    public boolean isInterview() {
        return Objects.equals(bizType, BizType.OFFLINE_INTERVIEW.value) || Objects.equals(bizType, BizType.ONLINE_INTERVIEW.value);
    }

    enum BizType {
        ONLINE_INTERVIEW("ONLINE_INTERVIEW"),// 线上面试
        OFFLINE_INTERVIEW("OFFLINE_INTERVIEW"), // 线下面试
        LIVE_RECORDING("LIVE_RECORDING"), // 实时录制
        ME_MEETING("ME_MEETING"); //  音视频会议
        public String value;

        BizType(String value) {
            this.value = value;
        }

    }

}
