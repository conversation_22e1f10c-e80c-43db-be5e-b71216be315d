package com.jd.oa.joy.note.detail;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel;

public class JoyNoteDetailSumFragment extends BaseFragment {
    private JoyNoteDetailInfoModel infoModel;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_joy_note_detail_sum, container);
        initView(view);
        return view;
    }

    private void initView(View view) {
        TextView mTvSummaryContent = view.findViewById(R.id.tv_summary_content);
        View mLlEmpty = view.findViewById(R.id.ll_empty);
        if (getActivity() != null && getActivity() instanceof JoyNoteDetailActivity) {
            JoyNoteDetailActivity activity = (JoyNoteDetailActivity) getActivity();
            infoModel = activity.getJoyNoteDetailInfoModel();
        }
        if (infoModel != null && infoModel.minutesDetail != null && !TextUtils.isEmpty(infoModel.minutesDetail.summary)) {
            mTvSummaryContent.setText(infoModel.minutesDetail.summary);
            mLlEmpty.setVisibility(View.GONE);
        } else {
            mTvSummaryContent.setVisibility(View.GONE);
            mLlEmpty.setVisibility(View.VISIBLE);
        }

    }

}
