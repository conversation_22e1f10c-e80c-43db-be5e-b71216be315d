package com.jd.oa.joy.note.notefloat;

import android.app.Activity;
import android.content.res.Configuration;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.joy.note.record.RecordAudioActivity;
import com.jd.oa.utils.ActivityLifecycleCallbacksAdapter;

import java.util.ArrayList;
import java.util.List;

public class NoteFloatCallback extends ActivityLifecycleCallbacksAdapter {

    private static final List<Class<? extends Activity>> HIDE_ACTIVITIES = new ArrayList<>();
    private static final List<String> HIDE_FRAGMENTS = new ArrayList<>();

    void addWhiteList(List<Class<? extends Activity>> whiteActivities, List<String> whiteFragments) {
        if (whiteActivities != null) {
            HIDE_ACTIVITIES.addAll(whiteActivities);
        }
        if (whiteFragments != null) {
            HIDE_FRAGMENTS.addAll(whiteFragments);
        }
    }

    void clear() {
        HIDE_ACTIVITIES.clear();
        HIDE_FRAGMENTS.clear();
    }

    @Override
    public void onActivityStarted(Activity activity) {
        if (activity instanceof RecordAudioActivity) {
            return;
        }
        if (shouldHideIt(activity)) {
            NoteFloatManager.hideFloatingView(activity);
        } else {
            NoteFloatManager.getInstance().showFloatingView(activity);
        }

        if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            //打开横屏界面
            NoteFloatManager.getInstance().resetPosition(activity);
        }
    }

    public static boolean shouldHideIt(Activity activity) {
        if (HIDE_ACTIVITIES.contains(activity.getClass())) return true;
        if (activity instanceof FunctionActivity && activity.getIntent() != null) {
            return HIDE_FRAGMENTS.contains(activity.getIntent().getStringExtra("function"));
        }
        return false;
    }
}
