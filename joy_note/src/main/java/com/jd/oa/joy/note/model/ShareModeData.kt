package com.jd.oa.joy.note.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/18
 * @Description:
 */
data class ShareModeData(val linkShareMode: String, val doShareMode: String, val org: Org) {
    // liveData setValue 方法中通过 class equals 方法去判断当前对象有没有发生变化
    // 默认的 equals 方法雷子 Any，只是对类引用的比较，使用 copy 时并没有改变类的引用
    override fun equals(other: Any?): Boolean {
        return other is ShareModeData && linkShareMode == other.linkShareMode
                && doShareMode == other.doShareMode
    }

    override fun hashCode(): Int {
        return javaClass.hashCode()
    }
}

@Parcelize
data class Org(val orgCode: String?, val orgName: String?) : Parcelable
