package com.jd.oa.joy.note.detail

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.jd.oa.joy.note.R
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.DensityUtil

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/7
 * @Description:
 */
typealias DismissCallback = () -> Unit

class BottomDialogFragment : DialogFragment() {
    private lateinit var originalText: TextView
    private var mDismissCallback: DismissCallback? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.BottomDialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.dialog_content, container, false)
        originalText = view.findViewById(R.id.original_text)
        view.findViewById<IconFontView>(R.id.icon_close).setOnClickListener {
            dismiss()
        }
        return view
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.let { window ->
            // 设置窗口位置和尺寸
            window.setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT, DensityUtil.dp2px(context, 316f)
            )
            window.setGravity(Gravity.BOTTOM)
            window.setDimAmount(0f)
            window.setElevation(0f)
        }
        arguments?.let {
            originalText.text = it.getString(ORIGINAL)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mDismissCallback?.invoke()
    }

    fun setOnDismissListener(callback: DismissCallback) {
        mDismissCallback = callback
    }

    companion object {
        private const val ORIGINAL = "original"

        fun newInstance(text: String): BottomDialogFragment {
            val args = Bundle()
            args.putSerializable(ORIGINAL, text)
            val fragment = BottomDialogFragment()
            fragment.arguments = args
            return fragment
        }
    }
}