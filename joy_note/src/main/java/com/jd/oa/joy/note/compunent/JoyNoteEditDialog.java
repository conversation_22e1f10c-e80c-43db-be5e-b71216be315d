package com.jd.oa.joy.note.compunent;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDialog;

import com.jd.oa.joy.note.R;


public class JoyNoteEditDialog extends AppCompatDialog {

    public interface OnClickListener {
        void onClickOk(String inputText);

        void onClickCancel();
    }

    private String mContentText;
    private OnClickListener mListener;
    private Context context;

    public JoyNoteEditDialog(Context context, String contentText) {
        super(context, R.style.JoyNoteDialogStyle);
        this.context = context;
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_joy_note_dialog_edit);
        Window window = getWindow();
        if (window != null) {
            if (window.getDecorView() != null) {
                window.getDecorView().setBackgroundColor(Color.TRANSPARENT);
            }
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.CENTER;
            window.setAttributes(layoutParams);
        }
        mContentText = contentText;
        setCancelable(true);
        setCanceledOnTouchOutside(true);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            EditText edit = findViewById(R.id.edit);
            if (!TextUtils.isEmpty(mContentText)) {
                edit.setText(mContentText);
                edit.setSelection(edit.getText().length());
            }
            View ivClear = findViewById(R.id.iv_clear);
            ivClear.setOnClickListener(v -> edit.setText(""));
            TextView confirm = findViewById(R.id.confirm);
            confirm.setOnClickListener(v -> {
                dismiss();
                if (mListener != null && edit != null) {
                    if (TextUtils.isEmpty(edit.getText().toString().trim())) {
                        return;
                    }
                    mListener.onClickOk(edit.getText().toString().trim());
                }
            });
            edit.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    ivClear.setVisibility(TextUtils.isEmpty(s.toString()) ? View.GONE : View.VISIBLE);
                }
            });
            TextView cancel = findViewById(R.id.cancel);
            cancel.setOnClickListener(v -> {
                dismiss();
                if (mListener != null) {
                    mListener.onClickCancel();
                }
            });
            edit.postDelayed(() -> {
                edit.requestFocus();
                if (context != null) {
                    InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
                    inputMethodManager.showSoftInput(edit, InputMethodManager.SHOW_IMPLICIT);
                }
            }, 200);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setOnClickListener(OnClickListener listener) {
        mListener = listener;
    }

    @Override
    public void show() {
        super.show();
    }
}
