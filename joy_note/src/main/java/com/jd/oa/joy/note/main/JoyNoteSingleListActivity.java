package com.jd.oa.joy.note.main;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import com.jd.oa.BaseActivity;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.JoyNoteTitleBar;

public class JoyNoteSingleListActivity extends BaseActivity {
    public static final String LIST_TYPE = "LIST_TYPE";
    public static final String CREATE = "CREATE";
    public static final String RECEIVE = "RECEIVE";
    public static final String RECYCLE_BIN = "RECYCLE_BIN";
    public static final String RECENT = "RECENT";
    public static final String FRONT_PAGE = "FRONT_PAGE";

    protected void onCreate(Bundle savedInstanceState) {
        getWindow().setStatusBarColor(Color.WHITE);
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_joy_note_list);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        JoyNoteTitleBar joyNoteTitleBar = findViewById(R.id.list_toolbar);
        if (CREATE.equals(getIntent().getStringExtra(LIST_TYPE))) {
            joyNoteTitleBar.setTitle(getString(R.string.joynote_my_create_title));
        } else if (RECYCLE_BIN.equals(getIntent().getStringExtra(LIST_TYPE))) {
            joyNoteTitleBar.setTitle(getString(R.string.joynote_recycle_bin));
        } else {
            joyNoteTitleBar.setTitle(getString(R.string.joynote_home_received));
        }
    }
}
