package com.jd.oa.joy.note.model;

import androidx.annotation.Keep;

import java.util.List;

@Keep
public class TranslateResult {

    private String id;

    private List<ContentText> textList;

    private List<TranslatedText> transList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<ContentText> getTextList() {
        return textList;
    }

    public void setTextList(List<ContentText> textList) {
        this.textList = textList;
    }

    public List<TranslatedText> getTransList() {
        return transList;
    }

    public void setTransList(List<TranslatedText> transList) {
        this.transList = transList;
    }
}