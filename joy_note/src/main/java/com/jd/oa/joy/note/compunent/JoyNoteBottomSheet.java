package com.jd.oa.joy.note.compunent;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.jd.oa.joy.note.R;

import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * @noinspection unused
 */
public class JoyNoteBottomSheet {
    private BottomSheetDialog bottomSheetDialog;
    private BottomSheetAdapter bottomSheetAdapter;

    public JoyNoteBottomSheet(@NotNull Context context, @NotNull List<BottomSheetItem> items) {
        if (bottomSheetDialog == null) {
            View view = View.inflate(context, R.layout.joy_note_bottom_sheet, null);
            RecyclerView recyclerView = view.findViewById(R.id.dialog_recycleView);
            recyclerView.setLayoutManager(new LinearLayoutManager(context));
            bottomSheetAdapter = new BottomSheetAdapter(items);
            recyclerView.setAdapter(bottomSheetAdapter);
            bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialog);
            bottomSheetDialog.setCanceledOnTouchOutside(true);
            bottomSheetDialog.setContentView(view);
        }
    }

    public void show() {
        bottomSheetDialog.show();
    }

    public void dismiss() {
        bottomSheetDialog.dismiss();
    }

    public void setItemListener(@NotNull OnclickListener<BottomSheetItem> onClickListener) {
        if (bottomSheetAdapter == null) {
            return;
        }
        bottomSheetAdapter.setOnItemClickListener(onClickListener);
    }
}
