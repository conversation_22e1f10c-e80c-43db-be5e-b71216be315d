package com.jd.oa.joy.note.model;

public class JoyNoteKeywordPosition {

    //段落定位 第几段
    public int segmentPosition;
    //段落中第几个关键词
    public int position;
    //关键词在段落中的索引
    public int startIndex;
    public int endIndex;

    public JoyNoteKeywordPosition(int segmentPosition, int position, int startIndex, int endIndex) {
        this.segmentPosition = segmentPosition;
        this.position = position;
        this.startIndex = startIndex;
        this.endIndex = endIndex;
    }
}
