package com.jd.oa.joy.note.record;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joy.note.DateTimeUtils;
import com.jd.oa.joy.note.R;
import com.jd.oa.ui.CircleImageView;

import java.util.List;

public class RecordTextAdapter extends RecyclerView.Adapter<RecordTextAdapter.ViewHolder> {
    private final Context mContext;
    private final List<RecordTextModel> mList;

    public RecordTextAdapter(Context mContext, List<RecordTextModel> mList) {
        this.mContext = mContext;
        this.mList = mList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(View.inflate(mContext, R.layout.joy_note_item_record_list, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {

    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position, @NonNull List<Object> payloads) {
        RecordTextModel recordTextModel = mList.get(position);
        if (payloads.isEmpty()) {
            // 设置头像、时间
            holder.ivPhoto.setImageResource(R.drawable.joynote_avatar_default);
            holder.tvName.setText(R.string.joy_note_speaker);
            holder.tvTime.setText(DateTimeUtils.getShowTextByMs(recordTextModel.getStartTime()));
            setText(recordTextModel, holder.tvText);
        } else {
            setText(recordTextModel, holder.tvText);
        }
    }

    public void setText(RecordTextModel model, TextView textView) {
        String stableText = TextUtils.isEmpty(model.stableText) ? "" : model.stableText;
        String dynamicText = TextUtils.isEmpty(model.dynamicText) ? "" : model.dynamicText;
        SpannableStringBuilder span = new SpannableStringBuilder(stableText);
        span.append(dynamicText);
        span.setSpan(new ForegroundColorSpan(Color.parseColor("#55666666")), stableText.length()
                , span.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        textView.setText(span);
    }

    @Override
    public int getItemCount() {
        return mList == null ? 0 : mList.size();
    }


    static class ViewHolder extends RecyclerView.ViewHolder {
        CircleImageView ivPhoto;
        TextView tvName, tvTime, tvText;
        View vMerge;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPhoto = itemView.findViewById(R.id.iv_photo);
            tvName = itemView.findViewById(R.id.tv_name);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvText = itemView.findViewById(R.id.tv_content);
            vMerge = itemView.findViewById(R.id.ll_merge);
        }
    }
}
