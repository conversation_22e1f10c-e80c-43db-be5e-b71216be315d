package com.jd.oa.joy.note.record

import android.media.AudioFormat
import android.util.Log
import java.io.BufferedInputStream
import java.io.File
import java.io.FileInputStream

object AudioRecordUtil {
    /**
     * 将一个pcm文件转化为wav文件
     * @param pcmPath  pcm文件路径
     * @param dstPath 目标文件路径(wav)
     * @param deletePcmFile   是否删除源文件
     * @return
     */
    fun makePCMFileToWAVFile(
        pcmPath: String,
        dstPath: String,
        deletePcmFile: Boolean
    ) {
        val start = System.currentTimeMillis()
        val pcmFile = File(pcmPath)
        val wavFile = File(dstPath)
        val sampleRate = AudioRecordCurrent.sampleRateInHz
        val bitDepth = 16
        val channelCount = 1
        val wavWriter = WavWriter(wavFile, sampleRate, bitDepth, channelCount)
        BufferedInputStream(FileInputStream(pcmFile)).use { bis ->
            val buf = ByteArray(8192)
            var length: Int
            while (bis.read(buf, 0, 8192).also { length = it } != -1) {
                wavWriter.writeData(buf, length)
            }
        }
        wavWriter.close()
        Log.e(
            "audioRecordTest",
            "makePCMFileToWAVFile:" + "wav文件写入成功，使用时间：${System.currentTimeMillis() - start}" +
                    "文件大小：${wavFile.length()}"
        )
        if (deletePcmFile && pcmFile.exists()) {
            pcmFile.delete()
            Log.e("audioRecordTest", "录音源文件删除成功")
        }
    }


    fun byteArray2ShortArray(data: ByteArray, items: Int): ShortArray {
        val retVal = ShortArray(items)
        for (i in retVal.indices) retVal[i] =
            (data[i * 2].toInt() and 0xff or (data[i * 2 + 1].toInt() and 0xff shl 8)).toShort()
        return retVal
    }


    /**
     * 获取录音文件时长 单位:秒
     */
    fun getDuration(): Float {
        var bitSize = 16
        when (AudioRecordCurrent.audioFormat) {
            AudioFormat.ENCODING_PCM_16BIT -> bitSize = 16
            AudioFormat.ENCODING_PCM_8BIT -> bitSize = 8
        }
        var channelSize = 1
        when (AudioRecordCurrent.channelConfig) {
            AudioFormat.CHANNEL_IN_MONO -> channelSize = 1
            AudioFormat.CHANNEL_IN_STEREO -> channelSize = 2
        }
        val file = File(AudioRecordCurrent.pcmFilePath)
        if (file.exists() && file.isFile) {
            val lastDuration: Long = AudioRecordCurrent.recordDuration.div(1000)
            return (file.length() * 8) / (channelSize.toFloat() * bitSize * AudioRecordCurrent.sampleRateInHz) + lastDuration
        }
        return 0F
    }

    /**
     * 是否超时
     */
    fun isOverTime(): Boolean {
        return getDuration() >= 4 * 60 * 60
    }

}