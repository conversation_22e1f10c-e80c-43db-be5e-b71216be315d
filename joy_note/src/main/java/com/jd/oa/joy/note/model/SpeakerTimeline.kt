package com.jd.oa.joy.note.model

import androidx.annotation.Keep

/**
 * @Author: hepiao3
 * @CreateTime: 2025/1/16
 * @Description:
 */

@Keep
data class TotalTimeLine(
    val timelines: List<SpeakerTimeline>?,
    val total: Int,
    val recordDuration: Long
)

@Keep
data class SpeakerTimeline (
    val account: String? = null,
    val color: String? = null,
    val ddAppId: String? = null,
    val endTime: Long? = null,
    val imageUrl: String? = null,
    val proportion: Double? = null,
    val realName: String? = null,
    val scopes: List<Scope> = emptyList(),
    val startTime: Long? = null,
    val totalTime: Long? = null,
    val userType: String? = null
) {

    companion object {
        const val TYPE_FORMAL = "FORMAL"
        const val TYPE_VIRTUAL = "VIRTUAL"
        const val TYPE_CUSTOM = "CUSTOM"
    }
    fun isSpeaking(time: Long?): Boolean {
        if (scopes.isEmpty() || time == null)  return false
        val item = scopes
            .filter { it.startTime != null && it.endTime != null }
            .find { time > it.startTime!! && time < it.endTime!! }
        return item != null
    }
}

@Keep
data class Scope(
    val startTime: Long?,
    val endTime: Long?
)

enum class UserType {
    CUSTOM, //自定义用户类型
    VOICEPRINT_CLUSTERING, // 声纹聚类识别的角色，发言人1、2、3.....
    FORMAL, // 普通用户
    DEVICE, // 会议室、设备等其他类型
    UN_KNOWN, // 未知类型
}


