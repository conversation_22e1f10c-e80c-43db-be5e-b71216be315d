package com.jd.oa.joy.note.share

import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.BaseActivity
import com.jd.oa.ext.binding
import com.jd.oa.joy.note.JoyNoteShareUtil
import com.jd.oa.joy.note.JoyNoteShareUtil.isSelf
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.ActivityJoyNoteShareBinding
import com.jd.oa.joy.note.model.JoyNoteShareUser
import com.jd.oa.joy.note.model.ShareGroupItem
import com.jd.oa.joy.note.model.ShareItem
import com.jd.oa.joy.note.model.SharePersonItem
import com.jd.oa.joy.note.permission.CollaboratorType
import com.jd.oa.joy.note.viewmodel.JoyNoteShareAuthorizeViewModel
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.ToastUtils

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/10
 * @Description: 慧记分享列表
 */
class JoyNoteShareActivity : BaseActivity() {
    private val binding by binding(ActivityJoyNoteShareBinding::inflate)
    private val viewModel by viewModels<JoyNoteShareAuthorizeViewModel>()
    private val mid: String by lazy { intent.getStringExtra(KEY_MID) ?: "" }
    private val permissions by lazy { intent.getStringArrayListExtra(PERMISSIONS) ?: listOf() }
    private val extraMemberList: ArrayList<MemberEntityJd?> by lazy {
        intent.getParcelableArrayListExtra(KEY_MEMBER_LIST) ?: arrayListOf()
    }
    private val adapter: MemberAdapter by lazy {
        MemberAdapter(viewModel, permissions.toMutableList()) { user, isDelete ->
            if (isDelete) extraMemberList.removeIf { it?.mId == user.userId }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        supportActionBar?.hide()
        initView()
        initViewModel()
    }

    private fun initView() {
        // 添加分享人
        binding.add.setOnClickListener {
            JoyNoteShareUtil.selectMember(this, mid, extraMemberList) { members ->
                members?.filter { !it.isSelf() }?.let {
                    viewModel.getWholeContactDetailInfo(ArrayList(it))
                }
            }
        }
        // 返回
        binding.back.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
        // 确认分享
        binding.confirm.setOnClickListener {
            viewModel.share(
                mid,
                adapter.getShareUserList().convertShareItems(),
                binding.ddChatCheck.isChecked(),
                binding.minutesAssistantCheck.isChecked()
            )
        }
        binding.recycleView.adapter = adapter
        binding.recycleView.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        viewModel.getWholeContactDetailInfo(extraMemberList)
        // 默认选择
        binding.minutesAssistantCheck.setChecked(true)
    }

    private fun initViewModel() {
        viewModel.memberList.observe(this) {
            if (!it.isNullOrEmpty()) {
                adapter.addData(it.toShareUserList())
                val newAddMemberList =
                    it.distinct().filterNot { memberEntityJd -> memberEntityJd in extraMemberList }
                extraMemberList.addAll(newAddMemberList)
            }
            binding.ddChatCheck.isEnabled = extraMemberList.isNotEmpty()
            binding.minutesAssistantCheck.isEnabled = extraMemberList.isNotEmpty()
            binding.confirm.alpha = if (extraMemberList.isEmpty()) 0.5f else 1f
            binding.confirm.isClickable = extraMemberList.isNotEmpty()
            binding.emptyView.isVisible = extraMemberList.isEmpty()
        }
        viewModel.shareResult.observe(this) {
            if (it) {
                setResult(RESULT_OK)
                ToastUtils.showToast(getString(R.string.joynote_share_success))
                finish()
            } else {
                setResult(RESULT_CANCELED)
                ToastUtils.showToast(getString(R.string.joynote_share_fail))
            }
        }
    }

    private fun List<JoyNoteShareUser?>.convertShareItems(): List<ShareItem?> =
        map { shareUser ->
            shareUser?.run {
                if (type == MemberEntityJd.TYPE_GROUP) {
                    ShareGroupItem(userId, appId, CollaboratorType.GROUP.type, permissionType)
                } else {
                    SharePersonItem(userId, appId, CollaboratorType.PERSONAL.type, permissionType)
                }
            }
        }

    private fun MutableList<MemberEntityJd?>.toShareUserList(): MutableList<JoyNoteShareUser?> {
        return this.map { member ->
            member?.let {
                JoyNoteShareUser(
                    permissionType = SharePermission.READER.permission,
                    appId = member.mApp,
                    userId = member.id,
                    userName = member.name,
                    department = member.department ?: "",
                    position = member.position ?: "",
                    avatar = member.avatar,
                    type = member.type
                )
            } ?: run {
                null
            }
        }.toMutableList()
    }

    companion object {
        const val KEY_MID = "mid"
        const val PERMISSIONS = "permissions"
        const val KEY_MEMBER_LIST = "member_list"
        const val KEY_REQUEST_CODE = 1000
    }

}