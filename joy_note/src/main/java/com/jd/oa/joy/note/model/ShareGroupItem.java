package com.jd.oa.joy.note.model;

import android.text.TextUtils;

public class ShareGroupItem implements ShareItem, PermissionItem {
    /* "toId":"10204635420", // 群号或者成员userid
             "toTeamId": "", // 租户信息
             "toType":1, // 1个人，3群组
             "toPermission":1 // 分享的权限类型*/
    public String toId;
    public String toTeamId;
    public int toType;
    public int toPermission;


    public ShareGroupItem(String toId, String toTeamId, int toType, int toPermission) {
        this.toId = toId;
        if (!TextUtils.isEmpty(toTeamId)) {
            this.toTeamId = toTeamId;
        }
        this.toType = toType;
        this.toPermission = toPermission;
    }
}
