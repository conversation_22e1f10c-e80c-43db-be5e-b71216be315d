package com.jd.oa.joy.note.permission

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.model.JoyNoteShareUser
import com.jd.oa.joy.note.model.VideoItemModel.ADD_EDITABLE
import com.jd.oa.joy.note.model.VideoItemModel.ADD_OWNER
import com.jd.oa.joy.note.share.MemberAdapter
import com.jd.oa.joy.note.share.SharePermission
import com.jd.oa.joy.note.viewmodel.JoyNoteShareAuthorizeViewModel
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.DensityUtil

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/17
 * @Description:
 */
class PermissionAdapter(
    viewModel: JoyNoteShareAuthorizeViewModel,
    override val permissions: MutableList<String>,
    override val updateUser: (user: JoyNoteShareUser, isDelete: Boolean) -> Unit
) : MemberAdapter(viewModel, permissions, updateUser) {

    override val multiTypes: List<Int>
        get() = listOf(CollaboratorType.PERSONAL.type)

    override val singleTypes: List<Int>
        get() = listOf(
            CollaboratorType.DEPARTMENT.type,
            CollaboratorType.GROUP.type,
            CollaboratorType.SCHEDULE.type,
            CollaboratorType.JOY_SPACE.type
        )

    override val fixedShowDelete: Boolean
        get() = false

    override fun onBindViewHolder(
        holder: MemberViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        memberList[position]?.let { user ->
            holder.binding.apply {
                flagDown.isInvisible = !canEditUserPermission(user)
                permissionEdit.isClickable = canEditUserPermission(user)
                label.isVisible = if (user.type != CollaboratorType.PERSONAL.type) {
                    label.apply {
                        text = CollaboratorType.getTextResByType(context, user.type)
                        setTextColor(CollaboratorType.getColorResByType(context, user.type))
                        background = GradientDrawable().apply {
                            shape = GradientDrawable.RECTANGLE
                            cornerRadius = DensityUtil.dp2px(context, 4f).toFloat()
                            setStroke(
                                DensityUtil.dp2px(context, 1f),
                                CollaboratorType.getColorResByType(context, user.type)
                            )
                            setColor(Color.TRANSPARENT)
                        }
                    }
                    true
                } else {
                    false
                }
            }
        }
        super.onBindViewHolder(holder, position, payloads)
        // 协作者列表，日程类型，当标题为空时默认标题为“日程”
        memberList[position]?.let { user ->
            if (user.type == CollaboratorType.SCHEDULE.type && user.userName.isNullOrEmpty()) {
                holder.binding.realName.text =
                    holder.itemView.context.getString(R.string.joynote_authorize_label_schedule)
            }
        }
    }

    private fun canEditUserPermission(user: JoyNoteShareUser): Boolean {
        if (user.userId == PreferenceManager.UserInfo.getUserId()) {
            return false
        }
        val isOwner = permissions.contains(ADD_OWNER)
        val canAddEdit = permissions.contains(ADD_EDITABLE)
        return isOwner || (canAddEdit && user.permissionType != SharePermission.CREATOR.permission)
    }
}