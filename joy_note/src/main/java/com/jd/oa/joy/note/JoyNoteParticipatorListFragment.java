package com.jd.oa.joy.note;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joy.note.model.JoyNoteMember;
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.ui.IconFontView;

import java.util.ArrayList;
import java.util.List;

public class JoyNoteParticipatorListFragment extends BaseFragment {

    private TextView mTvTitle;
    private List<JoyNoteMember> participators;
    private JoyNoteParticipatorAdapter adapter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_joy_note_participator_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        RecyclerView mRvParticipators = view.findViewById(R.id.rv_participators);
        mRvParticipators.setLayoutManager(new LinearLayoutManager(getContext()));
        if (getActivity() == null) return;
        view.findViewById(R.id.joy_note_back).setOnClickListener(v -> getActivity().finish());
        participators = new ArrayList<>();
        mTvTitle = view.findViewById(R.id.joy_note_title);
        mTvTitle.setText(getString(R.string.joy_note_participator_actionbar_title, participators.size()));
        adapter = new JoyNoteParticipatorAdapter(participators, getContext());
        adapter.setOnItemClickListener(member -> {
            if (member == null) return;
            if ("FORMAL".equalsIgnoreCase(member.userType) || "VIRTUAL".equalsIgnoreCase(member.userType)) {
                ImDdService service = AppJoint.service(ImDdService.class);
                service.showContactDetailInfo(getActivity(), member.ddAppId, member.account);
            }
        });
        mRvParticipators.setAdapter(adapter);
        IconFontView mIvShare = view.findViewById(R.id.joy_note_share);
        if (getActivity().getIntent() != null) {
            String mid = getActivity().getIntent().getStringExtra("mid");
            List<String> permissions = getActivity().getIntent().getStringArrayListExtra("permissions");
            boolean showShare = getActivity().getIntent().getBooleanExtra("showShare", false);
            if (showShare) {
                mIvShare.setVisibility(View.VISIBLE);
                mIvShare.setOnClickListener(v -> JoyNoteShareUtil.INSTANCE.share(getActivity(), mid, permissions, null));
            }
            getMembers(mid);
        }
    }

    public void getMembers(String mid) {
        JoyNoteDetailRepo.getRepo().getMembers(mid, new LoadDataCallback<List<JoyNoteMember>>() {
            @Override
            public void onDataLoaded(List<JoyNoteMember> joyNoteMembers) {
                if (getActivity() != null && !getActivity().isFinishing() && joyNoteMembers != null) {
                    participators.clear();
                    participators.addAll(joyNoteMembers);
                    adapter.notifyDataSetChanged();
                    mTvTitle.setText(getString(R.string.joy_note_participator_actionbar_title, participators.size()));
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }
}
