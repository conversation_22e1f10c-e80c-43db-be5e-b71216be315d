package com.jd.oa.joy.note.preference;

import android.content.Context;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class JoyNotePreference extends AbsKvEntities {

    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    private static JoyNotePreference preference;

    public static KvEntity<String> KV_ENTITY_VIDEO_CREATE = new KvEntity<>("video_create", "");
    public static KvEntity<String> KV_ENTITY_VIDEO_RECEIVE = new KvEntity<>("video_receive", "");

    private JoyNotePreference() {
    }

    public static synchronized JoyNotePreference getInstance() {
        if (preference == null) {
            preference = new JoyNotePreference();
        }
        return preference;
    }


    @NonNull
    @Override
    public String getPrefrenceName() {
        return "joy_note";
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }
}
