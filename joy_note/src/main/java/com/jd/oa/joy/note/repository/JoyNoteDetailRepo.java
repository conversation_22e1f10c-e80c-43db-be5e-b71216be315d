package com.jd.oa.joy.note.repository;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.joy.note.model.JoyNoteAsrData;
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel;
import com.jd.oa.joy.note.model.JoyNoteLanguageModel;
import com.jd.oa.joy.note.model.JoyNoteMember;
import com.jd.oa.joy.note.model.PermissionItem;
import com.jd.oa.joy.note.model.ShareItem;
import com.jd.oa.joy.note.model.Org;
import com.jd.oa.joy.note.model.TaskUserBase;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.entity.BatchUserInfo;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JoyNoteDetailRepo {
    private static final String JOY_NOTE_GET_DETAIL = "minutes.detail";
    private static final String JOY_NOTE_UPDATE_TITLE = "minutes.update.title";
    private static final String JOY_NOTE_PERMISSION_APPLY = "minutes.permission.apply";
    private static final String JOY_NOTE_PERMISSION_GET_OWNERS = "minutes.permission.getOwners";
    private static final String JOY_NOTE_GET_MEMBERS = "minutes.members";
    private static final String JOY_NOTE_SHARE = "minutes.share";
    private static final String JOY_NOTE_BASE_INFO = "minutes.baseInfo";
    private static final String JOY_NOTE_RECORD_CONTENT = "clevernote.content";
    public static final String JOY_NOTE_LANGUAGE = "minutes.languages";
    public static final String JOY_NOTE_USER_INFO_BATCH = "minutes.getUserInfoBatch";
    public static final String JOY_NOTE_PERMISSION_LIST = "minutes.permission.list";
    public static final String JOY_NOTE_SETTING_QUERY = "minutes.setting.query";
    public static final String JOY_NOTE_SETTING_MODIFY = "minutes.setting.modify";
    public static final String JOY_NOTE_PERMISSION_MODIFY = "minutes.permission.modify";

    public static final int ERROR_CODE_DELETE = 10010002;
    //被彻底删除了
    public static final int ERROR_CODE_DESTROY = 10010006;
    public static final int ERROR_CODE_NO_PERMISSION = 10010001;
    public static final int ERROR_CODE_IS_RECORDING = 10010005;
    public static final int ERROR_CODE_IS_RECORDING_BY_ME = 100666;


    private static final String CONTENT = "content";
    private static final String ERROR_CODE = "errorCode";
    private static final String STATUS = "status";

    private static JoyNoteDetailRepo mRepo;

    public static JoyNoteDetailRepo getRepo() {
        if (mRepo == null) {
            mRepo = new JoyNoteDetailRepo();
        }
        return mRepo;
    }


    public void getDetail(String mId, final LoadDataCallback<JoyNoteDetailInfoModel> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        HttpManager.post(null, params, new SimpleRequestCallback<String>(AppBase.getAppContext(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    if (info != null && !TextUtils.isEmpty(info.result)) {
                        JSONObject result = new JSONObject(info.result);
                        String errorCode = result.getString(ERROR_CODE);
                        if (!"0".equals(errorCode)) {
                            callback.onDataNotAvailable("", Integer.parseInt(errorCode));
                            return;
                        }
                        JSONObject content = result.getJSONObject(CONTENT);
                        JoyNoteDetailInfoModel joyNoteDetailInfoModel = JsonUtils.getGson().fromJson(content.toString(), JoyNoteDetailInfoModel.class);
                        if (joyNoteDetailInfoModel != null && joyNoteDetailInfoModel.minutesDetail != null
                                && ("3".equals(joyNoteDetailInfoModel.minutesDetail.status) || "5".equals(joyNoteDetailInfoModel.minutesDetail.status))) {
                            callback.onDataNotAvailable("", ERROR_CODE_IS_RECORDING_BY_ME);
                            return;
                        }

                        if (joyNoteDetailInfoModel != null) {
                            callback.onDataLoaded(joyNoteDetailInfoModel);
                        } else callback.onDataNotAvailable("", -1);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onDataNotAvailable("", -1);

            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, -1);
            }
        }, JOY_NOTE_GET_DETAIL);
    }

    public void getParagraphList(String mId, String api, final LoadDataCallback<List<JoyNoteAsrData>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        HttpManager.color().post(params, null, api,
                new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        callback.onDataNotAvailable(errorMsg, code);
                    }

                    @Override
                    protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                        super.onSuccess(jsonObject, tArray, rawData);
                        try {
                            String asrData = null;
                            if (rawData != null) {
                                JSONObject jsonObject1 = new JSONObject(rawData);
                                JSONObject content = jsonObject1.getJSONObject(CONTENT);
                                JSONArray jsonArray = content.getJSONArray("asrDataList");
                                asrData = jsonArray.toString();
                            }
                            if (!TextUtils.isEmpty(asrData)) {
                                List<JoyNoteAsrData> videoList = JsonUtils.getGson().fromJson(asrData,
                                        new TypeToken<List<JoyNoteAsrData>>() {
                                        }.getType());
                                callback.onDataLoaded(videoList);
                                return;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        callback.onDataNotAvailable("", -1);
                    }
                }));
    }

    public void updateTitle(String mId, String title, final LoadDataCallback<Object> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        params.put("title", title);
        HttpManager.post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable("", -1);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                super.onSuccess(jsonObject, tArray, rawData);
                callback.onDataLoaded(null);
            }
        }), JOY_NOTE_UPDATE_TITLE);
    }

    public void getMembers(String mId, final LoadDataCallback<List<JoyNoteMember>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        HttpManager.post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable("", -1);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                super.onSuccess(jsonObject, tArray, rawData);
                try {
                    if (!TextUtils.isEmpty(rawData)) {
                        JSONObject jsonObject1 = new JSONObject(rawData);
                        JSONObject content = jsonObject1.getJSONObject("content");
                        JSONArray users = content.getJSONArray("userList");
                        String s = users.toString();
                        if (!TextUtils.isEmpty(s)) {
                            List<JoyNoteMember> members = JsonUtils.getGson().fromJson(s, new TypeToken<List<JoyNoteMember>>() {
                            }.getType());
                            callback.onDataLoaded(members);
                            return;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onDataNotAvailable("", -1);
            }
        }), JOY_NOTE_GET_MEMBERS);
    }

    public void getOwners(String mId, final LoadDataCallback<List<JoyNoteMember>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        HttpManager.post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable("", -1);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                super.onSuccess(jsonObject, tArray, rawData);
                try {
                    if (!TextUtils.isEmpty(rawData)) {
                        JSONObject jsonObject1 = new JSONObject(rawData);
                        JSONObject content = jsonObject1.getJSONObject("content");
                        JSONArray users = content.getJSONArray("users");
                        String s = users.toString();
                        if (!TextUtils.isEmpty(s)) {
                            List<JoyNoteMember> members = JsonUtils.getGson().fromJson(s, new TypeToken<List<JoyNoteMember>>() {
                            }.getType());
                            callback.onDataLoaded(members);
                            return;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onDataNotAvailable("", -1);
            }
        }), JOY_NOTE_PERMISSION_GET_OWNERS);
    }

    public void permissionApply(String mId, String info, int permissionType, final LoadDataCallback<Object> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        params.put("verifyInfo", info);
        params.put("permissionType", permissionType);
        HttpManager.post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable("", -1);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                super.onSuccess(jsonObject, tArray, rawData);
                callback.onDataLoaded(null);
            }
        }), JOY_NOTE_PERMISSION_APPLY);
    }


    public void share(String mId, List<ShareItem> to, boolean sendToChat, boolean sendToMinutesRobot, String content, final LoadDataCallback<Object> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        params.put("to", to);
        params.put("sendToChat", sendToChat);
        //捎句话
        params.put("content", content);
        // 是否发送给慧记机器人
        params.put("notify", sendToMinutesRobot);
        HttpManager.post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable("", -1);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                super.onSuccess(jsonObject, tArray, rawData);
                callback.onDataLoaded(null);
            }
        }), JOY_NOTE_SHARE);
    }

    public void getUserInfoBatch(List<TaskUserBase> users, final LoadDataCallback<BatchUserInfo> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("users", users);
        HttpManager.color().post(params, null, JOY_NOTE_USER_INFO_BATCH,
                new SimpleReqCallbackAdapter<>(new AbsReqCallback<BatchUserInfo>(BatchUserInfo.class) {
                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        callback.onDataNotAvailable("", -1);
                    }

                    @Override
                    protected void onSuccess(BatchUserInfo batchUserInfo, List<BatchUserInfo> tArray) {
                        super.onSuccess(batchUserInfo, tArray);
                        callback.onDataLoaded(batchUserInfo);
                    }
                }));
    }

    public void getPermissionList(String mId, final LoadDataCallback<Object> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        HttpManager.color().post(params, null, JOY_NOTE_PERMISSION_LIST,
                new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
                    @Override
                    protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                        super.onSuccess(jsonObject, tArray, rawData);
                        callback.onDataLoaded(rawData);
                    }

                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        callback.onDataNotAvailable("", -1);
                    }
                }));
    }

    /**
     * 录制状态查询、基本信息接口
     *
     * @param mId
     * @param callback
     */
    public void recordInfo(String mId, final LoadDataCallback<Object> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", mId);
        params.put("deviceId", DeviceUtil.getDeviceUniqueId());
        HttpManager.post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable("", -1);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                super.onSuccess(jsonObject, tArray, rawData);
                callback.onDataLoaded(rawData);
            }
        }), JOY_NOTE_BASE_INFO);
    }


    public void getRecordContent(String mId, final LoadDataCallback<Object> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("noteId", mId);
        params.put("channel", "joyminutes");
        params.put("offset", 0);
        params.put("size", 100);
        params.put("deviceId", DeviceUtil.getDeviceUniqueId());
        HttpManager.post(null, params, new SimpleRequestCallback<String>(AppBase.getAppContext(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    if (info != null && !TextUtils.isEmpty(info.result)) {
                        JSONObject result = new JSONObject(info.result);
                        String status = result.getString(STATUS);
                        if (!"success".equals(status)) {
                            callback.onDataNotAvailable("", -1);
                            return;
                        }
                        callback.onDataLoaded(result.toString());
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onDataNotAvailable("", -1);

            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, -1);
            }
        }, JOY_NOTE_RECORD_CONTENT);
    }

    public void getTranslateLanguages(LoadDataCallback<List<JoyNoteLanguageModel>> callback, String api) {
        Map<String, Object> params = new HashMap<>();
        HttpManager.color().post(params, null, api, new SimpleRequestCallback<String>(AppBase.getAppContext(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<List<JoyNoteLanguageModel>> response = ApiResponse.parse(info.result, new TypeToken<List<JoyNoteLanguageModel>>() {
                }.getType(), "languages");
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), -1);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, -1);
            }
        });
    }

    public void queryPermissionSetting(String minutesId, String appId, final LoadDataCallback<Object> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", minutesId);
        params.put("jdmeAppId", appId);
        HttpManager.color().post(params, null, JOY_NOTE_SETTING_QUERY,
                new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
                    @Override
                    protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                        super.onSuccess(jsonObject, tArray, rawData);
                        callback.onDataLoaded(rawData);
                    }

                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        callback.onDataNotAvailable("", -1);
                    }
                })
        );
    }

    public void modifyPermissionSetting(String minutesId, String type, String value, String appId, Org org) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", minutesId);
        params.put("propName", type);
        params.put("propValue", value);
        params.put("ext", org);
        params.put("jdmeAppId", appId);
        HttpManager.color().post(params, null, JOY_NOTE_SETTING_MODIFY, null);
    }

    public void modifyCollaboratorPermission(String minutesId, List<PermissionItem> permissionList, String appId) {
        Map<String, Object> params = new HashMap<>();
        params.put("minutesId", minutesId);
        params.put("toUserPermissionList", permissionList);
        params.put("jdmeAppId", appId);
        params.put("checkAll", false);
        HttpManager.color().post(params, null, JOY_NOTE_PERMISSION_MODIFY, null);
    }
}
