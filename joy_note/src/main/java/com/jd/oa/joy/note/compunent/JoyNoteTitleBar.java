package com.jd.oa.joy.note.compunent;

import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.jd.oa.joy.note.R;
import com.jd.oa.ui.IconFontView;

/**
 * @noinspection unused
 */
public class JoyNoteTitleBar extends ConstraintLayout {
    private TextView title;
    private IconFontView rBtnA;
    private View rBtnB;
    private View rBtnC;

    public JoyNoteTitleBar(@NonNull Context context) {
        super(context);
        initView(context, null, 0);
    }

    public JoyNoteTitleBar(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context, attrs, 0);
    }

    public JoyNoteTitleBar(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context, attrs, defStyleAttr);
    }

    public JoyNoteTitleBar(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context, attrs, defStyleAttr);
    }

    private void initView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        LayoutInflater.from(context).inflate(R.layout.jdme_view_joy_note_action_bar, this, true);
        TypedArray typedArray = context.obtainStyledAttributes(attrs,
                R.styleable.JoyNoteTitleBar, defStyleAttr, 0);
        String title = typedArray.getString(R.styleable.JoyNoteTitleBar_title);
        rBtnA = findViewById(R.id.joy_note_share);
        rBtnB = findViewById(R.id.joy_note_more);
        rBtnC = findViewById(R.id.joy_note_edit);
        this.title = findViewById(R.id.joy_note_title);
        this.title.setText(title);
        View back = findViewById(R.id.joy_note_back);
        back.setOnClickListener(v -> {
            try {
                Activity activity = (Activity) getContext();
                activity.onBackPressed();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public void setTitle(String title) {
        if (!TextUtils.isEmpty(title)) {
            this.title.setText(title);
        }
    }

    public String getTitle() {
        return title.getText().toString();
    }

    public void setRightBtnAListener(int resId, OnClickListener onClickListener) {
        rBtnA.setText(resId);
        rBtnA.setVisibility(View.VISIBLE);
        rBtnA.setOnClickListener(onClickListener);
    }

    public void setRightBtnBListener(OnClickListener onClickListener) {
        rBtnB.setVisibility(View.VISIBLE);
        rBtnB.setOnClickListener(onClickListener);
    }


    public void setRightBtnCListener(OnClickListener onClickListener) {
        rBtnC.setVisibility(View.VISIBLE);
        rBtnC.setOnClickListener(onClickListener);
    }
}
