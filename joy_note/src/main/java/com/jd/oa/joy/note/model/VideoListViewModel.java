package com.jd.oa.joy.note.model;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.JoyNoteCreateInfo;

import org.json.JSONObject;

import java.util.List;

public class VideoListViewModel extends ViewModel {
    private final MainListData userListLiveData;
    private final MutableLiveData<JoyNoteCreateInfo> createLiveData;

    public VideoListViewModel() {
        userListLiveData = new MainListData();
        createLiveData = new MutableLiveData<>();
    }

    public void getVideoList(boolean loadMore, String type, JSONObject param) {

        VideoListRepository.getUserRepository().getVideoList(type, param, new LoadDataCallback<List<VideoItemModel>>() {
            @Override
            public void onDataLoaded(List<VideoItemModel> videoItemModels) {
                userListLiveData.setList(videoItemModels, true, loadMore);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                userListLiveData.setList(null, false, loadMore);
            }
        });
    }

    public void create() {

        VideoListRepository.getUserRepository().createJoyNote(null, null, null, new LoadDataCallback<JoyNoteCreateInfo>() {
            @Override
            public void onDataLoaded(JoyNoteCreateInfo createInfo) {
                createLiveData.postValue(createInfo);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                createLiveData.postValue(null);
            }
        });
    }

    public MainListData getVideoListLiveData() {
        return userListLiveData;
    }

    public MutableLiveData<JoyNoteCreateInfo> getCreateData() {
        return createLiveData;
    }

}
