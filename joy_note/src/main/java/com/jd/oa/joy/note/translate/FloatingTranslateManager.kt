package com.jd.oa.joy.note.translate

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.provider.Settings
import com.jd.oa.abtest.ABTestManager
import com.jd.oa.asr.AsrService
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.joy.note.notefloat.NoteFloatCallback
import com.jd.oa.joy.note.notefloat.NoteFloatService
import com.jd.oa.joy.note.record.AudioRecordCurrent.currentStatus
import com.jd.oa.joy.note.record.AudioRecordStatus
import com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_ON

/**
 * @Author: hepiao3
 * @CreateTime: 2025/5/14
 * @Description:
 */
object FloatingTranslateManager {
    // 实时翻译-画中画灰度 key
    private const val ANDROID_JOY_MINUTES_REALTIME_TRANSLATE_PIP_ENABLE =
        "android.joyminutes.realtime.translate.pip.enable"

    fun showFloatingView(activity: Activity?) {
        try {
            if (activity == null
                || !Settings.canDrawOverlays(activity)
                || NoteFloatCallback.shouldHideIt(activity)
                || ConfigurationManager.get().getEntry(
                    ANDROID_JOY_MINUTES_REALTIME_TRANSLATE_PIP_ENABLE, MULTI_TASK_ON
                ) != MULTI_TASK_ON
            ) return
            if (currentStatus == AudioRecordStatus.PAUSE ||
                currentStatus == AudioRecordStatus.RECORDING
            ) {
                val intent = Intent(activity, FloatingTranslateService::class.java)
                intent.putExtra(
                    FloatingTranslateService.ARG_ACTION,
                    FloatingTranslateService.ACTION_SHOW_DEFAULT
                )
                activity.startService(intent)
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    fun hideFloatingView(activity: Activity?) {
        try {
            if (!Settings.canDrawOverlays(activity) || activity == null) return
            val intent = Intent(activity, FloatingTranslateService::class.java)
            intent.putExtra(
                FloatingTranslateService.ARG_ACTION,
                FloatingTranslateService.ACTION_HIDE_DEFAULT
            )
            activity.startService(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun stopFloatTranslateService(context: Context?) {
        if (context == null) return

        try {
            val translateIntent = Intent(context, FloatingTranslateService::class.java)
            context.stopService(translateIntent)

            val asrIntent = Intent(context, AsrService::class.java)
            context.stopService(asrIntent)

            val floatService = Intent(context, NoteFloatService::class.java)
            context.stopService(floatService)
        } catch (ignore: Exception) {
        }
    }
}