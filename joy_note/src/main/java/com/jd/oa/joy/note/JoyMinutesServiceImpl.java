package com.jd.oa.joy.note;

import android.Manifest;
import android.app.Activity;

import com.jd.oa.joy.note.record.AudioRecordCurrent;
import com.jd.oa.joy.note.record.AudioRecordStatus;
import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.model.JoyNoteCreateInfo;
import com.jd.oa.model.service.JoyMinutesService;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;

import java.util.List;

public class JoyMinutesServiceImpl implements JoyMinutesService {

    @Override
    public void startRealTimeRecording(Activity activity, String name, String channel, StartRecordingCallback callback) {
        PermissionHelper.requestPermissions(activity
                , activity.getString(R.string.me_request_permission_title_normal)
                , activity.getString(R.string.me_request_permission_audio_normal)
                , new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        AudioRecordStatus status = AudioRecordCurrent.INSTANCE.getCurrentStatus();
                        if (status == AudioRecordStatus.RECORDING || status == AudioRecordStatus.PAUSE) {
                            callback.onConflicts();
                        } else {
                            createRecording(name, channel, callback);
                        }
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        callback.onPermissionDenied();
                    }
                }, Manifest.permission.RECORD_AUDIO,
                Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    private void createRecording(String name, String channel, StartRecordingCallback callback) {
        VideoListRepository.getUserRepository().createJoyNote(name, channel, null, new com.jd.oa.melib.mvp.LoadDataCallback<JoyNoteCreateInfo>() {
            @Override
            public void onDataLoaded(JoyNoteCreateInfo joyNoteCreateInfo) {
                callback.onSuccess(joyNoteCreateInfo);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                callback.onNetworkError(s, i);
            }
        });
    }

    public boolean isRecording() {
        return AudioRecordCurrent.INSTANCE.getCurrentStatus() == AudioRecordStatus.RECORDING || AudioRecordCurrent.INSTANCE.getCurrentStatus() == AudioRecordStatus.PAUSE;
    }
}
