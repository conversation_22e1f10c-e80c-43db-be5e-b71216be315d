package com.jd.oa.joy.note.permission

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joy.note.databinding.ItemSecuritySettingItemBinding
import com.jd.oa.joy.note.model.SecurityContent

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/20
 */
class SecuritySettingAdapter(
    private val data: MutableList<SecurityContent>,
    val selectedSecurity: (SecurityContent) -> Unit
) :
    RecyclerView.Adapter<SecuritySettingAdapter.SecuritySettingViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SecuritySettingViewHolder {
        val binding = ItemSecuritySettingItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SecuritySettingViewHolder(binding)
    }

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: SecuritySettingViewHolder, position: Int) {
        holder.bind(data[position])
        holder.binding.root.setOnClickListener {
            data[position].isCheck.set(!data[position].isCheck.get())
            val lastCheckPosition = data.indexOfFirst { it.isCheck.get() }
            if (lastCheckPosition in 0 until data.size) {
                data[lastCheckPosition].isCheck.set(!data[lastCheckPosition].isCheck.get())
            }
            selectedSecurity(data[position])
        }
    }

    inner class SecuritySettingViewHolder(val binding: ItemSecuritySettingItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(scope: SecurityContent) {
            binding.security = scope
            binding.lifecycleOwner = itemView.context as? LifecycleOwner
            binding.executePendingBindings()
        }
    }
}