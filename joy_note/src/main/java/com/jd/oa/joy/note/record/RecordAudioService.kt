package com.jd.oa.joy.note.record

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.asr.websocket.AudioStateListener
import com.jd.oa.asr.websocket.AudioTextListener
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE
import com.jd.oa.joy.note.DateTimeUtils
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.detail.JoyNoteDetailActivity.updateStatus
import com.jd.oa.joy.note.notefloat.NoteFloatManager
import com.jd.oa.joy.note.record.AudioRecordCurrent.audioFormat
import com.jd.oa.joy.note.record.AudioRecordCurrent.channelConfig
import com.jd.oa.joy.note.record.AudioRecordCurrent.pcmFilePath
import com.jd.oa.joy.note.record.AudioRecordCurrent.rootPath
import com.jd.oa.joy.note.record.AudioRecordCurrent.sampleRateInHz
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.utils.NotificationUtils
import com.jd.oa.utils.NotificationUtils.CHANNEL_DESCRIPTION_OTHER
import com.jd.oa.utils.ToastUtils
import com.jd.oa.websocket.AudioFileSender
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.math.log10


class RecordAudioService : Service() {
    private val TAG = "audioRecordTest"
    private lateinit var receiver: Receiver
    private var audioRecord: AudioRecord? = null
    private var recordBufsize = 0
    private lateinit var recordingThread: Thread
    private var tm: TelephonyManager? = null
    private var ams: AudioManager? = null
    private var listener: JoyNoteListener? = null
    private lateinit var builder: NotificationCompat.Builder
    private var notificationId = 111111
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    companion object {
        const val STATUS_BAD_NETWORK = 1001

        const val STATUS_OTHER_DEVICE_IS_CONNECTED = 1002

        const val ACTION_RECORD_AUDIO = "record.audio.service.action"
        const val ACTION_RECORD_STATUS = "record.status.service.action"
        const val ACTION_RECORD_RECORDING = "action.audio.record.recording"
        const val ACTION_SOCKET_UPDATE_TEXT = "action.socket.update.text"
    }

    private lateinit var notification: Notification
    override fun onCreate() {
        super.onCreate()
        //慧记9 AsrRecordAudioService onCreate 注册record.audio.service.action和record.status.service.action本地广播
        receiver = Receiver()
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(receiver, IntentFilter(ACTION_RECORD_AUDIO))
        // 后台监听电话的呼叫状态。
        // 得到电话管理器
        try {
            tm = this.getSystemService(TELEPHONY_SERVICE) as TelephonyManager
            listener = JoyNoteListener()
            tm?.listen(listener, PhoneStateListener.LISTEN_CALL_STATE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        //慧记10 AsrRecordAudioService onCreate 初始化AUDIO_SERVICE
        try {
            ams = getSystemService(AUDIO_SERVICE) as AudioManager
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        notification = createNotification(NotificationUtils.CHANNEL_ID_OTHER)
        startForeground(notificationId, notification)
        return START_STICKY
    }

    private fun createNotification(channelId: String): Notification {
        // 创建通知渠道（Android 8.0及以上需要）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                NotificationUtils.CHANNEL_NAME_OTHER,
                NotificationManager.IMPORTANCE_LOW
            )
            channel.description = CHANNEL_DESCRIPTION_OTHER;
            val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
        // 创建通知
        builder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.jdme_app_launcher)
            .setContentTitle(getString(R.string.joynote_notification_title))
            .setContentText("00:00")
            .setOngoing(true)
        return builder.build()
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(receiver)
        audioRecord?.release()
        try {
            tm?.listen(listener, PhoneStateListener.LISTEN_NONE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        listener = null
        try {
            ams?.abandonAudioFocus(mAudioFocusListener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        JMAudioCategoryManager.getInstance().setOutCall(false)
        JMAudioCategoryManager.getInstance().releaseJoyNote()
    }

    private fun createAudioRecord() {
        recordBufsize = AudioRecord
            .getMinBufferSize(
                sampleRateInHz,
                channelConfig,
                audioFormat
            )
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            sampleRateInHz,
            channelConfig,
            audioFormat,
            recordBufsize
        )
    }

    fun startRecord(isContinue: Boolean) {
        //慧记 AsrRecordAudioService startRecord 初始化AUDIO_SERVICE  11
        if (!PermissionHelper.isGranted(this, Manifest.permission.RECORD_AUDIO)) {
            Log.e(TAG, "startRecord: 无录音权限")
            return
        }
        if (audioRecord == null) {
            createAudioRecord()
        }
        if (AudioRecordCurrent.currentStatus == AudioRecordStatus.RECORDING) {
            return
        }
        //获取audiofocus
        ams?.requestAudioFocus(mAudioFocusListener, AudioManager.STREAM_SYSTEM, AudioManager.AUDIOFOCUS_GAIN)
        AudioRecordCurrent.currentStatus = AudioRecordStatus.RECORDING
        if (!isContinue) {
            AudioFileSender.getInstance()
                .startUploadAudioFile(AudioRecordCurrent.minuteId,
                    pcmFilePath,
                    object : AudioStateListener {
                        override fun hasPause(): Boolean {
                            return AudioRecordCurrent.currentStatus == AudioRecordStatus.PAUSE
                        }

                        override fun onStart() {
                            updateStatus(3, AudioRecordCurrent.minuteId, this@RecordAudioService)
                        }

                        override fun getAudioBufferSize(): Int {
                            return recordBufsize
                        }

                        override fun netState(notGood: Boolean) {
                            var intent = Intent(ACTION_RECORD_STATUS)
                            intent.putExtra("status", STATUS_BAD_NETWORK)
                            intent.putExtra("notGood", notGood)
                            LocalBroadcastManager.getInstance(this@RecordAudioService)
                                .sendBroadcast(intent)
                        }

                        override fun error(eventName: String) {
                            //别的设备正在录音
                            AudioRecordCurrent.currentStatus = AudioRecordStatus.COMPLETE
                            val intent = Intent(ACTION_RECORD_STATUS)
                            intent.putExtra("msg", eventName)
                            intent.putExtra("status", STATUS_OTHER_DEVICE_IS_CONNECTED)
                            LocalBroadcastManager.getInstance(this@RecordAudioService)
                                .sendBroadcast(intent)
                            stopSelf()
                            NoteFloatManager.hideFloatingView(AppBase.getTopActivity())
                        }

                        override fun logMessage(message: String?) {}
                    },
                    AudioTextListener { textMsg ->
                        textMsg?.let {
                            val intent = Intent(ACTION_SOCKET_UPDATE_TEXT)
                            intent.putExtra("text", it)
//                            Log.e(
//                                TAG,
//                                "startRecord: ${it.payload.stableResult.result + "==" + it.payload.dynamicResult.result}"
//                            )
                            LocalBroadcastManager.getInstance(this@RecordAudioService)
                                .sendBroadcast(intent)
                        }
                    })
        }
        if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
            MELogUtil.localE(TAG, "audioRecord state not initialized, " + audioRecord?.state)
            return
        }
        audioRecord?.startRecording()
//        Log.i("audioRecordTest", "开始录音")
        recordingThread = object : Thread() {
            override fun run() {
                super.run()
                var file = File(pcmFilePath)
                var os: FileOutputStream? = null
                try {
                    if (!File(rootPath).exists()) {
                        File(rootPath).mkdirs()
                    }
                    if (!file.exists()) {
                        file.createNewFile()
//                        Log.i("audioRecordTest", "创建录音文件->$pcmFilePath")
                    }
                    os = FileOutputStream(file, isContinue)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                var read: Int? = 0
                var data = ByteArray(recordBufsize)
                if (os != null) {
                    while (AudioRecordCurrent.currentStatus == AudioRecordStatus.RECORDING) {
                        if (AudioRecordUtil.isOverTime()) {
                            AudioRecordCurrent.currentStatus = AudioRecordStatus.PAUSE
                            val intent = Intent(ACTION_RECORD_AUDIO)
                            intent.putExtra("action", RecordAction.PAUSE.name)
                            LocalBroadcastManager.getInstance(this@RecordAudioService)
                                .sendBroadcast(intent)
                            Handler(Looper.getMainLooper()).post(Runnable { ToastUtils.showToast(R.string.joynote_is_over_time_for_record) })
                        }
                        read = audioRecord?.read(data, 0, recordBufsize)
                        if (AudioRecord.ERROR_INVALID_OPERATION != read) {
                            try {
                                os.write(data)
//                                Log.i("audioRecordTest", "写录音数据->$read")
                                val intent = Intent(ACTION_RECORD_RECORDING)
                                if (read != null && read != 0) {
                                    val s = AudioRecordUtil.byteArray2ShortArray(data, read.div(2))
                                    var v: Long = 0
                                    // 将 buffer 内容取出，进行平方和运算
                                    for (i in s.indices) {
                                        v += s[i] * s[i]
                                    }
                                    // 平方和除以数据总长度，得到音量大小。
                                    val mean = v / read.div(2)
                                    if (mean.toInt() <= 0) {
                                        intent.putExtra("volume", 0)
                                    } else {
                                        val volume = 10 * log10(mean.toDouble())
                                        intent.putExtra("volume", volume)
                                    }
                                }
                                intent.putExtra("duration", AudioRecordUtil.getDuration())
                                LocalBroadcastManager.getInstance(this@RecordAudioService)
                                    .sendBroadcast(intent)
                                notifyNotification()
                            } catch (e: IOException) {
                                e.printStackTrace()
                            }
                        }
                    }
                }
                try {
                    os?.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        recordingThread.start()
    }

    fun stopRecord(isComplete: Boolean) {
        JMAudioCategoryManager.getInstance().releaseJoyNote()
        if (audioRecord == null) return
        audioRecord?.stop()
        if (isComplete) {
            AudioRecordCurrent.currentStatus = AudioRecordStatus.COMPLETE
            audioRecord?.release()
            audioRecord = null
            /* if (BuildConfig.DEBUG) {
                 var dstFile = File(rootPath + "joyNote.wav")
                 if (!dstFile.exists()) {
                     dstFile.createNewFile()
                 }
                 object : Thread() {
                     override fun run() {
                         super.run()
                         AudioRecordUtil.makePCMFileToWAVFile(pcmFilePath, dstFile.path, false)
                     }
                 }.start()
             }*/
            stopSelf()
        } else {
            AudioRecordCurrent.currentStatus = AudioRecordStatus.PAUSE
            Handler(Looper.getMainLooper()).postDelayed(Runnable { notifyNotification() }, 500)
        }
    }

    private fun notifyNotification() {
        val manager =
            getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        if (AudioRecordCurrent.currentStatus == AudioRecordStatus.PAUSE) {
            builder.setContentTitle(getString(R.string.joynote_notification_content))
        } else {
            builder.setContentTitle(getString(R.string.joynote_notification_title))
            builder.setContentText(
                DateTimeUtils.getShowTextByMs2(
                    AudioRecordUtil.getDuration().toLong().times(1000)
                )
            )
        }
        manager.notify(notificationId, builder.build())
    }

    inner class Receiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            kotlin.runCatching {
                when (intent?.getStringExtra("action")) {
                    RecordAction.START.name -> startRecord(false)
                    RecordAction.RESUME.name -> startRecord(true)
                    RecordAction.PAUSE.name -> stopRecord(false)
                    RecordAction.END.name -> stopRecord(true)
                    else -> println("not implement !")
                }
            }.onFailure {
                MELogUtil.localE(TAG, "onReceive", it)
            }
        }
    }

    private inner class JoyNoteListener : PhoneStateListener() {
        // 当电话的呼叫状态发生变化的时候调用的方法
        override fun onCallStateChanged(state: Int, incomingNumber: String) {
            super.onCallStateChanged(state, incomingNumber)
//            Log.d("qcl111", "state$state")
            try {
                when (state) {
                    TelephonyManager.CALL_STATE_IDLE -> {
                        //继续
                        JMAudioCategoryManager.getInstance().setOutCall(false)
//                        intent.putExtra("action", RecordAction.RESUME.name)
                    }

                    TelephonyManager.CALL_STATE_RINGING -> {
                        //暂停
                        val category = JMAudioCategoryManager.getInstance().currentAudioCategory
                        if (category != JME_AUDIO_CATEGORY_JOY_NOTE) {
                            return
                        }
                        val intent = Intent(ACTION_RECORD_AUDIO)
                        JMAudioCategoryManager.getInstance().setOutCall(true)
                        intent.putExtra("action", RecordAction.PAUSE.name)
                        LocalBroadcastManager.getInstance(this@RecordAudioService)
                            .sendBroadcast(intent)
                    }

//                    TelephonyManager.CALL_STATE_OFFHOOK -> Log.v("myService", "通话状态")
                    else -> {}
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
    }

    private val mAudioFocusListener =
        AudioManager.OnAudioFocusChangeListener { focusChange ->
//            Log.d("qcl111", "focusChange----------> $focusChange")
            when (focusChange) {
                AudioManager.AUDIOFOCUS_GAIN -> {
                    //重新获取AudioFocus, 一般是AudioFocus_Loss_Transient结束后自动获得
                    JMAudioCategoryManager.getInstance().setOutCall(false)
                }
                AudioManager.AUDIOFOCUS_LOSS -> {
                    //AudioFocus将会被其他应用占用未知时间
                    val category = JMAudioCategoryManager.getInstance().currentAudioCategory
                    if (category != JME_AUDIO_CATEGORY_JOY_NOTE) {
                        return@OnAudioFocusChangeListener
                    }
                    //暂停慧记
                    val intent = Intent(ACTION_RECORD_AUDIO)
                    intent.putExtra("action", RecordAction.PAUSE.name)
                    LocalBroadcastManager.getInstance(this@RecordAudioService)
                        .sendBroadcast(intent)
                }
                else -> {
                    //其他情况（外部电话），包括AUDIOFOCUS_LOSS_TRANSIENT
                    //AudioFocus将会被其他应用短暂占用并在结束时触发AUDIOFOCUS_GAIN
                    val category = JMAudioCategoryManager.getInstance().currentAudioCategory
                    if (category != JME_AUDIO_CATEGORY_JOY_NOTE) {
                        return@OnAudioFocusChangeListener
                    }
                    //设置OutCall状态，设置为true之后不能再开始录制慧记，直到被设为false
                    JMAudioCategoryManager.getInstance().setOutCall(true)
                    //暂停慧记
                    val intent = Intent(ACTION_RECORD_AUDIO)
                    intent.putExtra("action", RecordAction.PAUSE.name)
                    LocalBroadcastManager.getInstance(this@RecordAudioService)
                        .sendBroadcast(intent)
                }
            }
        }
}