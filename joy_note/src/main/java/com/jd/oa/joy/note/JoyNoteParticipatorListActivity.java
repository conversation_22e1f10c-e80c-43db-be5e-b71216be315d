package com.jd.oa.joy.note;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.view.Window;

import com.jd.oa.BaseActivity;

public class JoyNoteParticipatorListActivity extends BaseActivity {
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().setStatusBarColor(Color.WHITE);
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_joy_note_participator_list);
    }
}
