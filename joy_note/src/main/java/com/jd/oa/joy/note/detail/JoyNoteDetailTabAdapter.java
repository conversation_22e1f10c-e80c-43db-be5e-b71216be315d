package com.jd.oa.joy.note.detail;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_DETAIL_URL;
import static com.jd.oa.fragment.WebFragment2.EXTRA_NAV;
import static com.jd.oa.joy.note.detail.IntelligentSummaryFragment.JOY_NOTE_MINUTES_ID;
import static com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_OFF;
import static com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_ON;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.JDMAPages;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.IntStream;

import kotlin.Triple;

public class JoyNoteDetailTabAdapter extends FragmentStateAdapter {
    private static final String JOY_MINUTES_TAB_SPEAKER_ENABLE = "android.joyminutes.tab.speaker.enable";
    JoyNoteDetailInfoModel joyNoteDetailInfoModel;
    JoyNoteDetailTimelineFragmentHost timelineFragmentHost;
    private final List<Triple<Fragment, String, String>> fragmentInfoList = new ArrayList<>();
    private final Fragment fragment;

    public JoyNoteDetailTabAdapter(
            @NonNull Fragment fragment,
            JoyNoteDetailInfoModel joyNoteDetailInfoModel,
            JoyNoteDetailTimelineFragmentHost timelineFragmentHost
    ) {
        super(fragment);
        this.fragment = fragment;
        this.joyNoteDetailInfoModel = joyNoteDetailInfoModel;
        this.timelineFragmentHost = timelineFragmentHost;
        createTabFragment();
    }

    @NonNull
    @Override
    public Fragment createFragment(int i) {
        return fragmentInfoList.get(i).getFirst();
    }

    private void createTabFragment() {
        Bundle bundle = new Bundle();
        if (joyNoteDetailInfoModel != null) {
            bundle.putParcelable("info", joyNoteDetailInfoModel);
        }
        // 智能总结 Tab
        IntelligentSummaryFragment intelligentSummaryFragment = new IntelligentSummaryFragment();
        bundle.putString(EXTRA_NAV, "0");
        String joyNoteHost = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getNoteUrl();
        bundle.putString(EXTRA_APP_DETAIL_URL, joyNoteHost + "/m/summary/" + joyNoteDetailInfoModel.minutesDetail.minutesId);
        bundle.putString(JOY_NOTE_MINUTES_ID, joyNoteDetailInfoModel.minutesDetail.minutesId);
        // JoyNoteDetailSumFragment joyNoteDetailSumFragment = new JoyNoteDetailSumFragment();
        intelligentSummaryFragment.setArguments(bundle);
        JmEventDispatcher.registerProcessor(fragment, new JoyNoteSummaryProcessor(fragment));
        fragmentInfoList.add(new Triple<>(
                intelligentSummaryFragment,
                fragment.getString(R.string.joy_note_detail_tab_sum_interview),
                JDMAConstants.Mobile_event_Minutes_DetailHome_AISummary));

        // 文字记录 Tab
        JoyNoteDetailTextFragment joyNoteDetailTextFragment = new JoyNoteDetailTextFragment();
        joyNoteDetailTextFragment.setArguments(bundle);
        fragmentInfoList.add(new Triple<>(
                joyNoteDetailTextFragment,
                fragment.getString(R.string.joy_note_detail_tab_text),
                JDMAConstants.Mobile_Event_Minute_Textrecord
        ));

        // 发言人 Tab
        if (Objects.equals(ABTestManager.getInstance().getConfigByKey(
                JOY_MINUTES_TAB_SPEAKER_ENABLE, MULTI_TASK_ON), MULTI_TASK_ON)) {
            JoyNoteDetailSpeakerFragment speakerTimelineFragment = getJoyNoteDetailSpeakerFragment(bundle);
            fragmentInfoList.add(new Triple<>(
                    speakerTimelineFragment,
                    fragment.getString(R.string.joy_note_detail_tab_speaker),
                    JDMAConstants.MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB
            ));
        }

        // 与我相关 Tab
        if (joyNoteDetailInfoModel.minutesDetail.speechRelatedToMe) {
            JoyNoteMentionsFragment joyNoteMentionsFragment = new JoyNoteMentionsFragment();
            joyNoteMentionsFragment.setArguments(bundle);
            fragmentInfoList.add(new Triple<>(
                    joyNoteMentionsFragment,
                    fragment.getString(R.string.joy_note_detail_mentions),
                    JDMAConstants.MOBILE_EVENT_MINUTES_MENTIONS
            ));
        }

        // 会议信息 Tab
        JoyNoteDetailInfoFragment joyNoteDetailInfoFragment = new JoyNoteDetailInfoFragment();
        joyNoteDetailInfoFragment.setArguments(bundle);
        fragmentInfoList.add(new Triple<>(
                joyNoteDetailInfoFragment,
                fragment.getString(R.string.joy_note_detail_tab_info),
                JDMAConstants.Mobile_Event_Minute_Basicinfo

        ));
    }

    @NonNull
    private JoyNoteDetailSpeakerFragment getJoyNoteDetailSpeakerFragment(Bundle bundle) {
        JoyNoteDetailSpeakerFragment speakerTimelineFragment =
                new JoyNoteDetailSpeakerFragment((speakerTimeline, totalDuration) -> {
                    JDMAUtils.clickEvent(
                            JDMAPages.Mobile_Page_Minute_Detail,
                            JDMAConstants.MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB_SPEECH_CLIPS,
                            new HashMap<>()
                    );
                    timelineFragmentHost.showParagraphFragment(speakerTimeline, totalDuration);
                    return null;
                }, num -> {
                    updateSpeakerTabText(num);
                    return null;
                });
        speakerTimelineFragment.setArguments(bundle);
        return speakerTimelineFragment;
    }

    @Override
    public int getItemCount() {
        return fragmentInfoList.size();
    }

    public String getTabTitle(int position) {
        if (position >= 0 && position < fragmentInfoList.size()) {
            return fragmentInfoList.get(position).getSecond();
        }
        return "";
    }

    public String getTabClickEventId(int position) {
        if (position >= 0 && position < fragmentInfoList.size()) {
            return fragmentInfoList.get(position).getThird();
        }
        return "";
    }

    private void updateSpeakerTabText(int num) {
        int index = getFragmentIndex(JoyNoteDetailSpeakerFragment.class);
        if (index >= 0 && index < fragmentInfoList.size()) {
            Triple<Fragment, String, String> oldTriple = fragmentInfoList.get(index);
            Triple<Fragment, String, String> newTriple = new Triple<>(
                    oldTriple.getFirst(),
                    fragment.getString(R.string.joy_note_detail_tab_speaker_num, num),
                    oldTriple.getThird()
            );
            fragmentInfoList.set(index, newTriple);
            notifyItemRangeChanged(0, fragmentInfoList.size());
        }
    }

    public <T> int getFragmentIndex(Class<T> clazz) {
        return IntStream.range(0, fragmentInfoList.size())
                .filter(i -> clazz.isInstance(fragmentInfoList.get(i).getFirst()))
                .findFirst().orElse(-1);
    }
}
