package com.jd.oa.joy.note.detail

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.JDMAConstants
import com.jd.oa.JDMAPages
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.compunent.ShapeWavingView
import com.jd.oa.joy.note.model.SpeakerTimeline
import com.jd.oa.joy.note.viewmodel.JoyNoteMediaPlayerViewModel
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.ImageLoader
import com.jd.oa.utils.JDMAUtils

/**
 * @Author: peidongbiao
 * @CreateTime: 2025/1/19
 * @Description:
 */
class JoyNoteDetailTimelineFragment : BaseFragment() {

    companion object {

        @JvmStatic
        fun newInstance(): JoyNoteDetailTimelineFragment {
            val args = Bundle()
            val fragment = JoyNoteDetailTimelineFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private val mediaPlayerViewModel: JoyNoteMediaPlayerViewModel by activityViewModels()

    private val mAvatarWave: ShapeWavingView by lazy { requireView().findViewById(R.id.avatar_wave) }
    private val mAvatar: ImageView by lazy { requireView().findViewById(R.id.avatar) }
    private val mName: TextView by lazy { requireView().findViewById(R.id.tv_name) }
    private val mRecyclerView: RecyclerView by lazy { requireView().findViewById(R.id.recycler_view) }
    private val mClose: View by lazy { requireView().findViewById(R.id.icon_close) }
    private val mCustomName: TextView by lazy { requireView().findViewById(R.id.tv_custom_name) }

    private var mAdapter: JoyNoteDetailTimelineAdapter? = null

    var onCloseClickListener: (() -> Unit)? = null

    var speakerTimeline: SpeakerTimeline? = null
        set(value) {
            field = value
            if (view != null) {
                setAvatar(value)
                mName.text = value?.realName
                mAdapter?.refresh(value?.scopes ?: emptyList())
            }
        }

    var totalDuration: Long? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return LayoutInflater.from(context)
            .inflate(R.layout.fragment_joy_note_detail_timline, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mAdapter = JoyNoteDetailTimelineAdapter(requireContext(), mediaPlayerViewModel, totalDuration) {
            JDMAUtils.clickEvent(
                JDMAPages.Mobile_Page_Minute_Detail,
                JDMAConstants.MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB_SPEECH_CLIPS_CLIP,
                emptyMap()
            )
            mediaPlayerViewModel.setMediaProgress(
                it.startTime!!.toInt(),
                true,
                sourceTimeLine = true
            )
        }
        mRecyclerView.adapter = mAdapter
        mRecyclerView.layoutManager = LinearLayoutManager(requireContext())

        initView(view)
    }

    private fun initView(view: View) {
        if (speakerTimeline != null) {
            setAvatar(speakerTimeline)
            mName.text = speakerTimeline?.realName
            mAdapter?.refresh(speakerTimeline?.scopes ?: emptyList())
            scrollToRunningScope()
        }
        mClose.setOnClickListener {
            onCloseClickListener?.invoke()
        }

        mediaPlayerViewModel.mediaStateLiveData.observe(viewLifecycleOwner) { state ->
            state.second?.let { position ->
                val scope = speakerTimeline?.scopes?.find { it.startTime!! <= position && it.endTime!! >= position }
                if (state.first == true && scope != null) {
                    mAvatarWave.start()
                } else {
                    mAvatarWave.pause()
                }
            }
        }

        mAvatarWave.setOnClickListener {
            val imDdService = AppJoint.service(ImDdService::class.java)
            if (SpeakerTimeline.TYPE_FORMAL.equals(speakerTimeline?.userType, ignoreCase = true) ||
                SpeakerTimeline.TYPE_VIRTUAL.equals(speakerTimeline?.userType, ignoreCase = true)
            ) {
                imDdService.showContactDetailInfo(context, speakerTimeline?.ddAppId, speakerTimeline?.account)
            }
        }
    }

    fun scrollToRunningScope() {
        val currentTime = mediaPlayerViewModel.mediaInfoLiveData.value?.first
        if (currentTime != null && currentTime != 0) {
            val scope = speakerTimeline?.scopes?.find {
                it.startTime!! <= currentTime && it.endTime!! >= currentTime
            }
            if (scope != null) {
                val index = speakerTimeline?.scopes?.indexOf(scope)
                if (index != null && index != -1) {
                    mRecyclerView.scrollToPosition(index)
                }
            }
        }
    }

    private fun setAvatar(speakerTimeline: SpeakerTimeline?) {
        if (SpeakerTimeline.TYPE_CUSTOM.equals(speakerTimeline?.userType, ignoreCase = true) &&
            !TextUtils.isEmpty(speakerTimeline?.realName)) {
            mCustomName.text = speakerTimeline?.realName?.firstOrNull().toString()
            mAvatar.setImageDrawable(ColorDrawable(requireContext().getColor(R.color.joy_note_bg_custom_avatar)))
        } else {
            mCustomName.text = null
            ImageLoader.load(
                requireContext(),
                mAvatar,
                speakerTimeline?.imageUrl,
                true,
                R.drawable.default_person_blue_avatar,
                R.drawable.default_person_blue_avatar
            )
        }
    }
}