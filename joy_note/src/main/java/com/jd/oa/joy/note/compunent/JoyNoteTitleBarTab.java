package com.jd.oa.joy.note.compunent;

import static com.jd.oa.utils.DensityUtil.dp2px;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.joy.note.R;
import com.jd.oa.theme.manager.Constants;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.theme.view.JoyWorkThemeView;
import com.jd.oa.utils.StatusBarConfig;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

/**
 * @noinspection unused
 */
public class JoyNoteTitleBarTab extends ConstraintLayout {
    private TextView title;
    private JoyWorkThemeView joyWorkThemeView;

    private final BroadcastReceiver mThemeDataChangeObserver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            onThemeDataChange();
        }
    };

    public JoyNoteTitleBarTab(@NonNull Context context) {
        super(context);
    }

    public JoyNoteTitleBarTab(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public JoyNoteTitleBarTab(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public JoyNoteTitleBarTab(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.joy_note_tab_title, this, true);
        title = findViewById(R.id.mTitle);
        joyWorkThemeView = findViewById(R.id.theme_bg);

        if (StatusBarConfig.enableImmersive()) {
            int statusBarHeight = QMUIStatusBarHelper.getStatusbarHeight(getContext());
            joyWorkThemeView.getLayoutParams().height = dp2px(context, 44) + statusBarHeight;
            joyWorkThemeView.requestLayout();
        }
        joyWorkThemeView.addOnAttachStateChangeListener(new OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                LocalBroadcastManager.getInstance(getContext()).registerReceiver(
                        mThemeDataChangeObserver, new IntentFilter(Constants.ACTION_CHANGE_THEME)
                );
            }

            @Override
            public void onViewDetachedFromWindow(View v) {
                LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mThemeDataChangeObserver);
            }
        });
        onThemeDataChange();
    }

    private void onThemeDataChange() {
        joyWorkThemeView.onThemeDataChange();
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        if (themeData != null && themeData.isGlobal() && themeData.isDarkTheme()) {
            title.setTextColor(0xffffffff);
        } else {
            title.setTextColor(0xff333333);
        }
    }

    public void setTitle(String title) {
        if (title != null && !TextUtils.isEmpty(title)) {
            this.title.setText(title);
        }
    }

    public String getTitle() {
        return title.getText().toString();
    }


}
