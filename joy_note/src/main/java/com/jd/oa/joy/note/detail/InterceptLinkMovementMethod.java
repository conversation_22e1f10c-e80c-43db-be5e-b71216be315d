package com.jd.oa.joy.note.detail;

import android.text.Spannable;
import android.text.method.LinkMovementMethod;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.widget.TextView;

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/8
 * @Description: 拦截长按点击
 */
public class InterceptLinkMovementMethod extends LinkMovementMethod {
    private boolean isLongClickHandled = false;

    @Override
    public boolean onTouchEvent(TextView widget, Spannable buffer, MotionEvent event) {
        int action = event.getActionMasked();
        if (action == MotionEvent.ACTION_DOWN) {
            // 检测长按事件
            widget.postDelayed(() -> {
                isLongClickHandled = true;
            }, ViewConfiguration.getLongPressTimeout());
        } else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL) {
            // 抬起或取消时重置标志位
            widget.postDelayed(() -> isLongClickHandled = false, ViewConfiguration.getTapTimeout());
        }

        if (isLongClickHandled) {
            // 如果是长按后的抬起事件，直接拦截
            return true;
        }
        return super.onTouchEvent(widget, buffer, event);
    }
}