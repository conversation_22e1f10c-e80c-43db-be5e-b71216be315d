package com.jd.oa.joy.note.repository

import com.google.gson.reflect.TypeToken
import com.jd.oa.joy.note.model.DataState
import com.jd.oa.joy.note.model.TotalTimeLine
import com.jd.oa.network.colorPost
import com.jd.oa.utils.JsonUtils
import org.json.JSONObject

/**
 * @Author: hepiao3
 * @CreateTime: 2025/1/15
 * @Description:
 */
class JoyNoteSpeakerRepo {
    suspend fun getSpeakerTimeLine(minutesId: String): Pair<DataState, TotalTimeLine?> {
        val params = mutableMapOf(MINUTES_ID to minutesId)
        var totalTimeLine: TotalTimeLine? = null
        val state: DataState = kotlin.runCatching {
            colorPost(params, TIME_LINE_ACTION).result?.let {
                val content = JSONObject(it)
                totalTimeLine = JsonUtils.getGson().fromJson<TotalTimeLine>(
                    content.optString("content"),
                    object : TypeToken<TotalTimeLine>() {}.type
                )
                if (totalTimeLine?.timelines.isNullOrEmpty()) {
                    DataState.Empty
                } else {
                    DataState.Success
                }
            } ?: DataState.Error
        }.getOrNull() ?: DataState.Error

        return Pair(state, totalTimeLine)
    }

    companion object {
        const val TIME_LINE_ACTION = "minutes.speakers.timelines"
        const val MINUTES_ID = "minutesId"
    }
}