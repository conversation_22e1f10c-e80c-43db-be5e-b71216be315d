package com.jd.oa.joy.note.detail;

import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE_V2_DETAIL;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.PhotoPreviewActivity;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.model.PhotoInfo;
import com.jd.oa.fragment.utils.FileType;
import com.jd.oa.joy.note.DateTimeUtils;
import com.jd.oa.joy.note.JoyNoteParticipatorListActivity;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.JoyNoteAvatarGroup;
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel;
import com.jd.oa.joy.note.model.JoyNoteMember;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.IconFontView;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JoyNoteDetailInfoFragment extends BaseFragment {

    private RecyclerView mRvLinks;
    private JoyNoteDetailLinkAdapter mLinkAdapter;
    private JoyNoteAvatarGroup mViewParticipator;
    private List<JoyNoteDetailInfoModel.MinutesDetail.Attachments> mLinksList;
    private TextView mTvLinkTitle;
    private TextView mTvOwner;
    private CircleImageView mIvOwner;
    private IconFontView mIvParticipatorMore;

    public static String ATTACHMENT_TYPE_JOY_SPACE = "joyspace";
    public static String ATTACHMENT_TYPE_FILE = "file";

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_joy_note_detail_info, container);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mRvLinks = view.findViewById(R.id.rv_links);
        TextView mTvDuration = view.findViewById(R.id.tv_duration);
        TextView mTvTime = view.findViewById(R.id.tv_time);
        mTvLinkTitle = view.findViewById(R.id.tv_title_link);
        mIvParticipatorMore = view.findViewById(R.id.iv_participator_more);
        TextView mTvParticipatorTitle = view.findViewById(R.id.tv_participator_title);
        mTvOwner = view.findViewById(R.id.tv_owner);
        mIvOwner = view.findViewById(R.id.iv_owner_photo);
        mViewParticipator = view.findViewById(R.id.view_participator);
        TextView mTvSchedule = view.findViewById(R.id.tv_title_schedule);
        LinearLayout mLlSchedule = view.findViewById(R.id.ll_schedule);
        TextView mTvScheduleName = view.findViewById(R.id.tv_schedule_name);
        TextView mTvScheduleTime = view.findViewById(R.id.tv_schedule_time);
        mRvLinks.setLayoutManager(new LinearLayoutManager(getContext()));


        if (getArguments() != null && getActivity() != null) {
            JoyNoteDetailInfoModel joyNoteDetailInfoModel = getArguments().getParcelable("info");
            if (joyNoteDetailInfoModel != null && joyNoteDetailInfoModel.minutesDetail != null) {
                //所有者
                JoyNoteDetailInfoModel.MinutesDetail.Owner owner = joyNoteDetailInfoModel.minutesDetail.owner;
                if (owner != null) {
                    mTvOwner.setText(owner.realName);
                    Glide.with(getActivity()).load(owner.imageUrl).into(mIvOwner);
                    mTvOwner.setOnClickListener(v -> jumpToUserInfo(owner.ddAppId, owner.account));
                    mIvOwner.setOnClickListener(v -> jumpToUserInfo(owner.ddAppId, owner.account));
                }

//链接
                mLinksList = joyNoteDetailInfoModel.minutesDetail.attachments;
                if (mLinksList != null && mLinksList.size() > 0) {
                    mLinkAdapter = new JoyNoteDetailLinkAdapter(mLinksList, getActivity());
                    mLinkAdapter.setOnLinkClickListener(attachments -> {
                        if (!TextUtils.isEmpty(attachments.fileUrl)) {
                            if (attachments.fileType.equalsIgnoreCase(ATTACHMENT_TYPE_JOY_SPACE)) {
                                OpennessApi.openUrl(attachments.fileUrl, false);
                            } else //if (attachments.fileType.equalsIgnoreCase(ATTACHMENT_TYPE_FILE))
                            {
                                int urlType = FileType.getTypeInt(attachments.fileUrl);
                                int nameType = -1;
                                if (!TextUtils.isEmpty(attachments.fileName)) {
                                    nameType = FileType.getTypeInt(attachments.fileName);
                                }
                                if (FileType.isImage(urlType) || FileType.isImage(nameType)) {
                                    Intent intent = new Intent(getActivity(), PhotoPreviewActivity.class);
                                    ArrayList<PhotoInfo> list = new ArrayList<>();
                                    list.add(new PhotoInfo(attachments.fileName, "", "", attachments.fileUrl));
                                    intent.putParcelableArrayListExtra("photos", list);
                                    getActivity().startActivity(intent);
                                } else if (FileType.isVideo(urlType) || FileType.isAudio(urlType)
                                        || FileType.isVideo(nameType) || FileType.isAudio(nameType)) {
                                    DeepLinkTools.openMedia(getActivity(), attachments.fileUrl, attachments.fileName, true);
                                } else {
                                    //其他链接类型
//                                    openFileByX5(getActivity(), "JoyNoteAttachment", attachments.fileUrl, attachments.fileName, attachments.fileUrl, true, false);
                                    Router.build(DeepLink.webUrl(attachments.fileUrl)).go(getActivity());
                                }
                            }
                        }
                    });
                    mRvLinks.setAdapter(mLinkAdapter);
                } else {
                    mRvLinks.setVisibility(View.GONE);
                    mTvLinkTitle.setVisibility(View.GONE);
                }
                mTvDuration.setText(DateTimeUtils.getShowTextByMs2(getActivity(), joyNoteDetailInfoModel.minutesDetail.recordDuration));
                mTvTime.setText(DateTimeUtils.getFormatDateAndTime(joyNoteDetailInfoModel.minutesDetail.createAt));
//                boolean isVideo = joyNoteDetailInfoModel.minutesDetail.type == 1;
                if (joyNoteDetailInfoModel.minutesDetail.members != null && joyNoteDetailInfoModel.minutesDetail.members.size() > 0) {
                    ArrayList<JoyNoteMember> members = new ArrayList<>(joyNoteDetailInfoModel.minutesDetail.members);
                    if (joyNoteDetailInfoModel.minutesDetail.memberNums > members.size()) {
                        for (int i = members.size(); i < joyNoteDetailInfoModel.minutesDetail.memberNums; i++) {
                            members.add(new JoyNoteMember());
                        }
                    }
                    mViewParticipator.setAutoMaxTotal(true, joyNoteDetailInfoModel.minutesDetail.members.size());
                    mViewParticipator.init(members);
                    mViewParticipator.setOnclickListener((v, position) -> {
                        try {
                            JoyNoteMember member = members.get(position);
                            if ("FORMAL".equalsIgnoreCase(member.userType) || "VIRTUAL".equalsIgnoreCase(member.userType)) {
                                jumpToUserInfo(member.ddAppId, member.account);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
                    mIvParticipatorMore.setOnClickListener(v -> {
                        Intent intent = new Intent(getActivity(), JoyNoteParticipatorListActivity.class);
                        boolean share = joyNoteDetailInfoModel.minutesDetail.permissions != null
                                && joyNoteDetailInfoModel.minutesDetail.permissions.contains("SHARE");
                        intent.putExtra("showShare", share);
                        intent.putExtra("mid", joyNoteDetailInfoModel.minutesDetail.minutesId);
                        intent.putStringArrayListExtra("permissions", new ArrayList<>(joyNoteDetailInfoModel.minutesDetail.permissions));
//                        intent.putParcelableArrayListExtra("participators", members);
                        startActivity(intent);
                    });
                } else {
                    mIvParticipatorMore.setVisibility(View.GONE);
                    mViewParticipator.setVisibility(View.GONE);
                    mTvParticipatorTitle.setVisibility(View.GONE);
                }
                JoyNoteDetailInfoModel.MinutesDetail.ScheduleInfo scheduleInfo = joyNoteDetailInfoModel.minutesDetail.scheduleInfo;
                if (scheduleInfo != null) {
                    mTvSchedule.setVisibility(View.VISIBLE);
                    mLlSchedule.setVisibility(View.VISIBLE);
                    if (DateTimeUtils.isSameDay(scheduleInfo.start, scheduleInfo.end)) {
                        mTvScheduleTime.setText(String.format("%s-%s", DateTimeUtils.getFormatDateAndTime(scheduleInfo.start), DateTimeUtils.justGetFormatTime(scheduleInfo.end)));
                    } else {
                        mTvScheduleTime.setText(String.format("%s-%s", DateTimeUtils.getFormatDateAndTime(scheduleInfo.start), DateTimeUtils.getFormatDateAndTime(scheduleInfo.end)));
                    }
                    mTvScheduleName.setText(scheduleInfo.subject);
                    mLlSchedule.setOnClickListener(v -> {
                        onScheduleMeetingDetail(scheduleInfo.scheduleId, scheduleInfo.calendarId, scheduleInfo.start, scheduleInfo.end);
                    });
                } else {
                    mTvSchedule.setVisibility(View.GONE);
                    mLlSchedule.setVisibility(View.GONE);
                }
            }
        }
    }


    private void jumpToUserInfo(String appId, String erp) {
        ImDdService service = AppJoint.service(ImDdService.class);
        service.showContactDetailInfo(getActivity(), appId, erp);
    }


    public void onScheduleMeetingDetail(String scheduleId, String calendarId, long startTime, long endTime) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }

        JSONObject object = new JSONObject();
        try {
            Date start = new Date(startTime);
            Date end = new Date(endTime);

            object.put("calendarId", calendarId)
                    .put("scheduleId", scheduleId)
                    .put("beginTime", start.getTime())
                    .put("endTime", end.getTime())
                    .put("isNeedCheck", true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Uri deeplink = Uri.parse(CALENDER_SCHEDULE_V2_DETAIL).buildUpon()
                .appendQueryParameter("mparam", object.toString())
                .build();
        Router.build(deeplink).go(activity);
    }
}
