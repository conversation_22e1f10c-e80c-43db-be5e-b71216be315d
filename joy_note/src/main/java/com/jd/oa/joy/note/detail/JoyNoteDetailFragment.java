package com.jd.oa.joy.note.detail;

import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;
import static com.jd.oa.JDMAConstants.MOBILE_EVENT_MINUTES_DETAIL_HOME_MENTIONS_SEARCH;
import static com.jd.oa.joy.note.JoyNoteTools.MINUTES_ID;
import static com.jd.oa.joy.note.JoyNoteTools.POSITION;
import static com.jd.oa.joy.note.JoyNoteTools.copyShareUrl;
import static com.jd.oa.joy.note.detail.JoyNoteDetailActivity.updateStatus;
import static com.jd.oa.joy.note.model.VideoItemModel.ACTION_LIST_UPDATE;
import static com.jd.oa.joy.note.model.VideoItemModel.DELETE_MINUTES;
import static com.jd.oa.joy.note.model.VideoItemModel.MINUTE_ID;
import static com.jd.oa.joy.note.model.VideoItemModel.NEW_TITLE_NAME;
import static com.jd.oa.joy.note.model.VideoItemModel.OPERATION_TYPE;
import static com.jd.oa.joy.note.model.VideoItemModel.RENAME_MINUTES;
import static com.jd.oa.joy.note.model.VideoItemModel.SHARE;
import static com.jd.oa.joy.note.share.JoyNoteShareActivity.PERMISSIONS;
import static com.jd.oa.notification.ChatNotificationManager.ACTION_CANCEL_JOY_NOTE_FULL_SCREEN;
import static com.jd.oa.utils.DensityUtil.dp2px;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager2.widget.ViewPager2;

import com.jd.oa.JDMAConstants;
import com.jd.oa.JDMAPages;
import com.jd.oa.ui.dialog.bottomsheet.BottomAction;
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.joy.note.JoyNoteShareUtil;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.BottomSheetItem;
import com.jd.oa.joy.note.compunent.JoyNoteBottomSheet;
import com.jd.oa.joy.note.compunent.JoyNoteEditDialog;
import com.jd.oa.joy.note.compunent.JoyNoteTab;
import com.jd.oa.joy.note.compunent.JoyNoteTitleBar;
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel;
import com.jd.oa.joy.note.model.JoyNoteLanguageModel;
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity;
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo;
import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.joy.note.viewmodel.JoyNoteDetailViewModel;
import com.jd.oa.joy.note.viewmodel.JoyNoteMediaPlayerViewModel;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.player.MePlayer2;
import com.jd.oa.player.MePlayerService;
import com.jd.oa.player.MePlayerStateListener;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.SoftKeyBoardListener;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;


public class JoyNoteDetailFragment extends BaseFragment {

    private JoyNoteTitleBar mToolbar;
    private JoyNoteTab mTabLayout;
    private FrameLayout mFlVideo;
    private FrameLayout mFlAudio;
    private ViewPager2 mViewPager;
    private View mChatView;
    private IconFontView mSearch;

    public static final String ACTION_JOY_NOTE_DETAIL_TEXT = "jdme.action.joy.note.detail";

    public static final String NATIVE_EVENT_JOYNOTE_HASUPDATED_PLAYER_PROGRESS = "NATIVE_EVENT_JOYNOTE_HASUPDATED_PLAYER_PROGRESS";

    public static int TYPE_SEARCH = 10001;
    public static int TYPE_EXTRACT_ALL_PARAGRAPHS = 10002;
    public static final String ACTION_UPDATE_VIDEO_PROGRESS = "jdme.action.joy.note.update.video.progress";
    private MePlayer2 mePlayer;
    private JoyNoteDetailInfoModel joyNoteDetailInfoModel;
    private String channel = "";
    private JoyNoteMediaPlayerViewModel mViewModel;

    private JoyNoteDetailTimelineFragmentHost timelineFragmentHost;

    private long lastVideoTime = -1;

    private JoyNoteDetailViewModel mDetailViewModel;

    private final List<Integer> pageVisibleStateList = Arrays.asList(
            View.GONE, View.VISIBLE, View.VISIBLE, View.GONE, View.GONE
    );

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_joy_note_detail, container, false);
        initView(view);
        initViewModel();
        return view;
    }

    private void initViewModel() {
        mViewModel = new ViewModelProvider(requireActivity()).get(JoyNoteMediaPlayerViewModel.class);
        mViewModel.getSentencesLiveData().observe(getViewLifecycleOwner(), position -> {
            if (mePlayer != null) {
                mePlayer.seekToPosition(position);
            }
        });
        // 发言人进度拖动，同步到播放器
        mViewModel.getMediaInfoLiveData().observe(getViewLifecycleOwner(), info -> {
            if (mePlayer != null && info.getThird()) {
                mePlayer.seekToPosition(info.getFirst());
            } else {
                syncMediaProgressToSummary();
            }
        });
        mViewModel.getParagraphLiveData().observe(getViewLifecycleOwner(), id -> {
            JoyNoteDetailTabAdapter adapter = (JoyNoteDetailTabAdapter) mViewPager.getAdapter();
            if (!id.isEmpty() && adapter != null) {
                int index = adapter.getFragmentIndex(JoyNoteDetailTextFragment.class);
                if (index >= 0 && index < adapter.getItemCount()) {
                    mViewPager.setCurrentItem(index, true);
                }
            }
        });

        mDetailViewModel = new ViewModelProvider(requireActivity()).get(JoyNoteDetailViewModel.class);
        mDetailViewModel.getLanguages();

        mDetailViewModel.getSelectedLanguage().observe(getViewLifecycleOwner(), new Observer<JoyNoteLanguageModel>() {
            @Override
            public void onChanged(JoyNoteLanguageModel languageModel) {
                if (languageModel == null || languageModel.isDefaultLanguage()) {
                    mSearch.setTextColor(getContext().getColor(R.color.color_tag_gray));
                } else {
                    mSearch.setTextColor(getContext().getColor(R.color.color_9D9D9D));
                }
            }
        });
    }

    private void syncMediaProgressToSummary() {
        try {
            int currentPosition = mePlayer.getCurrentPosition();
            if (Math.abs(currentPosition - lastVideoTime) / 1000 < 1) {
                return;
            }
            lastVideoTime = currentPosition;

            List<Fragment> fragments = getChildFragmentManager().getFragments();
            for (int i = 0; i < fragments.size(); i++) {
                Fragment fragment = fragments.get(i);

                if (fragment instanceof WebFragment2) {
                    int currentItem = mViewPager.getCurrentItem();
                    if (currentItem != i) {
                        break;
                    }

                    JSONObject result = new JSONObject();
                    JSONObject data = new JSONObject();
                    data.put("time", mePlayer.getCurrentPosition());
                    data.put("minutesId", joyNoteDetailInfoModel.minutesDetail.minutesId);
                    result.put("data", data);
                    JmEventDispatcher.dispatchEvent(getActivity(), NATIVE_EVENT_JOYNOTE_HASUPDATED_PLAYER_PROGRESS, result, null);
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            mePlayer.toggleFullScreen(0);

        }
    };

    @Override
    public void onStart() {
        super.onStart();
        if (getActivity() != null)
            LocalBroadcastManager.getInstance(getActivity()).registerReceiver(mReceiver, new IntentFilter(ACTION_CANCEL_JOY_NOTE_FULL_SCREEN));
    }

    @Override
    public void onStop() {
        super.onStop();
        if (getActivity() != null)
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(mReceiver);
    }

    public void initView(View view) {
        mToolbar = view.findViewById(R.id.toolbar);
        mToolbar = view.findViewById(R.id.toolbar);
        mFlVideo = view.findViewById(R.id.fl_video);
        mFlAudio = view.findViewById(R.id.fl_audio);
        mChatView = view.findViewById(R.id.fl_chat);
        mTabLayout = view.findViewById(R.id.tab_layout);
        mViewPager = view.findViewById(R.id.vp);
        mSearch = view.findViewById(R.id.iv_search);
        mSearch.setOnClickListener(v -> {
            JoyNoteLanguageModel languageModel = mDetailViewModel.getSelectedLanguage().getValue();
            if (languageModel == null || languageModel.isDefaultLanguage()) {
                if (getChildFragmentManager().getFragments().get(mViewPager.getCurrentItem())
                        instanceof JoyNoteMentionsFragment) {
                    JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_Detail,
                            MOBILE_EVENT_MINUTES_DETAIL_HOME_MENTIONS_SEARCH, null);
                }
                showSearch();
            } else {
                ToastUtils.showToast(getContext(), R.string.joynote_translate_cant_search);
            }
        });

        if (getActivity() != null && getActivity() instanceof JoyNoteDetailActivity) {
            JoyNoteDetailActivity activity = (JoyNoteDetailActivity) getActivity();
            channel = activity.channel;
        }

        timelineFragmentHost = new JoyNoteDetailTimelineFragmentHost(
                this, view,
                getChildFragmentManager(),
                R.id.fragment_container,
                R.id.view_mask
        );

        if (getArguments() != null) {
            joyNoteDetailInfoModel = getArguments().getParcelable("info");
            if (joyNoteDetailInfoModel != null && joyNoteDetailInfoModel.minutesDetail != null) {
                boolean isVideo = joyNoteDetailInfoModel.minutesDetail.type == 1;
                mToolbar.setTitle(joyNoteDetailInfoModel.minutesDetail.title);

                mePlayer = MePlayerService.getMePlay(requireContext()
                        , joyNoteDetailInfoModel.minutesDetail.videoFileUrl
                        , "", !isVideo);
                mePlayer.setTitle(joyNoteDetailInfoModel.minutesDetail.title);
                if (isVideo) {
                    mFlAudio.setVisibility(View.GONE);
                    mFlVideo.setVisibility(View.VISIBLE);
                    mFlVideo.addView(mePlayer.getVideoView());
                } else {
                    mFlVideo.setVisibility(View.GONE);
                    mFlAudio.setVisibility(View.VISIBLE);
                    mFlAudio.addView(mePlayer.getVideoView());
                    if (getActivity() != null) {
                        SoftKeyBoardListener.setListener(getActivity(), new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
                            @Override
                            public void keyBoardShow(int height) {
                                mFlAudio.setVisibility(View.GONE);
                            }

                            @Override
                            public void keyBoardHide(int height) {
                                mFlAudio.setVisibility(View.VISIBLE);
                            }
                        });

                        View fragmentContainer = view.findViewById(R.id.fragment_container);
                        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) fragmentContainer.getLayoutParams();
                        layoutParams.matchConstraintMaxHeight = DensityUtil.dp2px(requireContext(), 380);
                        fragmentContainer.setLayoutParams(layoutParams);
                    }
                }
                //自动播放时
                mePlayer.setMePlayerListener(position -> {
                    if (getContext() != null) {
                        mViewModel.setMediaProgress(position, false, false);
                    }
                });
                //手动滑动时
                mePlayer.setMePlayerSekBarListener((position, count) -> {
                    if (getContext() != null) {
                        mViewModel.setMediaProgress(position, true, false);
                    }
                });
                mePlayer.setMePlayerStateListener(new MePlayerStateListener() {
                    @Override
                    public void onPause() {
                        mViewModel.setMediaState(false);
                    }

                    @Override
                    public void onPlay() {
                        mViewModel.setMediaState(true);
                    }
                });
                List<String> permissions = joyNoteDetailInfoModel.minutesDetail.permissions;
                if (permissions != null && permissions.contains("SHARE")) {
                    mToolbar.setRightBtnAListener(R.string.icon_general_share1, v ->
                            JoyNoteShareUtil.INSTANCE.share(
                                    getActivity(),
                                    joyNoteDetailInfoModel.minutesDetail.minutesId,
                                    permissions,
                                    null)
                    );
                }
                if (permissions != null && permissions.contains("EDIT")) {
                    mToolbar.setRightBtnCListener(v -> showEditDialog());
                }
                //因为所有人都有复制链接的权限 所以这个按钮要显示
                mToolbar.setRightBtnBListener(v -> {
                            List<BottomSheetItem> items = new ArrayList<>();

                            if (permissions != null && permissions.contains("SHARE")) {
                                items.add(new BottomSheetItem(getString(R.string.joy_note_share), R.string.icon_general_share1));
                            }
                            if (permissions != null && !permissions.isEmpty() && permissions.contains(SHARE)) {
                                items.add(new BottomSheetItem(getString(R.string.joynote_permission_manager), R.string.icon_sky_basic));
                            }
                            items.add(new BottomSheetItem(getString(R.string.joy_note_copy_link), R.string.icon_edit_copy));

                            if (CollectionUtil.notNullOrEmpty(mDetailViewModel.getLanguageList())) {
                                items.add(new BottomSheetItem(getString(R.string.joy_note_translate), R.string.icon_translate));
                            }

                            if (permissions != null && permissions.contains("EDIT")) {
                                items.add(new BottomSheetItem(getString(R.string.joy_note_rename), R.string.icon_hj_edit));
                                if ("joyspace".equalsIgnoreCase(channel)) {
                                    items.add(new BottomSheetItem(getString(R.string.joynote_extract_full_text), R.string.icon_general_merge2));
                                }
                            }
                            if (permissions != null && permissions.contains(DELETE_MINUTES)) {
                                items.add(new BottomSheetItem(getString(R.string.joynote_delete), R.string.icon_edit_delete));
                            }

                            JoyNoteBottomSheet joyNoteBottomSheet = new JoyNoteBottomSheet(view.getContext(), items);
                            joyNoteBottomSheet.setItemListener((sheetItem, sheetPosition) -> {
                                joyNoteBottomSheet.dismiss();
                                if (getString(R.string.joy_note_share).equals(items.get(sheetPosition).getTitle())) {
                                    JoyNoteShareUtil.INSTANCE.share(getActivity(), joyNoteDetailInfoModel.minutesDetail.minutesId, permissions, null);
                                } else if (getString(R.string.joy_note_copy_link).equals(items.get(sheetPosition).getTitle())) {
                                    copyShareUrl(joyNoteDetailInfoModel.minutesDetail.title, joyNoteDetailInfoModel.minutesDetail.minutesId);
                                } else if (getString(R.string.joy_note_rename).equals(items.get(sheetPosition).getTitle())) {
                                    showEditDialog();
                                } else if (getString(R.string.joynote_extract_full_text).equals(items.get(sheetPosition).getTitle())) {
                                    Intent intent = new Intent(ACTION_JOY_NOTE_DETAIL_TEXT);
                                    intent.putExtra("type", TYPE_EXTRACT_ALL_PARAGRAPHS);
                                    if (getContext() != null)
                                        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
                                } else if (getString(R.string.joynote_delete).equals(items.get(sheetPosition).getTitle())) {
                                    VideoListRepository.getUserRepository().delItem(joyNoteDetailInfoModel.minutesDetail.minutesId, () -> {
                                        if (getActivity() != null) {
                                            Intent intent = new Intent(ACTION_LIST_UPDATE);
                                            intent.putExtra(OPERATION_TYPE, DELETE_MINUTES);
                                            intent.putExtra(MINUTE_ID, joyNoteDetailInfoModel.minutesDetail.minutesId);
                                            LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(intent);
                                            //通知joyspace刷新
                                            updateStatus(4, joyNoteDetailInfoModel.minutesDetail.minutesId, getActivity());
                                            getActivity().finish();
                                        }
                                    });
                                } else if (getString(R.string.joy_note_translate).equals(items.get(sheetPosition).getTitle())) {
                                    joyNoteBottomSheet.dismiss();
                                    if (CollectionUtil.notNullOrEmpty(mDetailViewModel.getLanguageList())) {
                                        showTranslateDialog(mDetailViewModel.getLanguageList());
                                    }
                                } else if (getString(R.string.joynote_permission_manager).equals(items.get(sheetPosition).getTitle()) && getContext() != null) {
                                    Intent intent = new Intent(getContext(), JoyNoteAuthorizeActivity.class);
                                    intent.putExtra("mid", joyNoteDetailInfoModel.minutesDetail.minutesId);
                                    if (permissions != null) {
                                        intent.putStringArrayListExtra(PERMISSIONS, new ArrayList<>(permissions));
                                    }
                                    getContext().startActivity(intent);
                                }
                            });
                            joyNoteBottomSheet.show();
                        }
                );
            } else {
                mToolbar.setTitle(getString(R.string.joynote_title));
            }
        }

        JoyNoteDetailTabAdapter adapter = new JoyNoteDetailTabAdapter(
                this,
                joyNoteDetailInfoModel,
                timelineFragmentHost
        );
        mViewPager.setAdapter(adapter);
        mViewPager.setOffscreenPageLimit(3);
        mViewPager.setUserInputEnabled(false);
        if (joyNoteDetailInfoModel != null && joyNoteDetailInfoModel.minutesDetail != null) {
            mTabLayout.initView(mViewPager, adapter::getTabTitle);
        }
        mViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                mSearch.setVisibility(pageVisibleStateList.get(position));
                String eventId = adapter.getTabClickEventId(position);
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_Detail, eventId, null);
            }
        });
    }

    private void showTranslateDialog(List<JoyNoteLanguageModel> languageModels) {
        JoyNoteLanguageModel currentModel = mDetailViewModel.getSelectedLanguage().getValue();
        List<BottomAction<JoyNoteLanguageModel>> actions = new ArrayList<>();
        for (JoyNoteLanguageModel model : languageModels) {
            BottomAction<JoyNoteLanguageModel> action = new BottomAction<>(
                    model,
                    model.getDisplay(),
                    ((currentModel == null || currentModel.isDefaultLanguage())
                            && model.isDefaultLanguage())
                            || (currentModel != null
                            && TextUtils.equals(currentModel.getLanguage(), model.getLanguage())));
            actions.add(action);
        }
        JoyBottomSheetDialog.Builder<JoyNoteLanguageModel> builder = new JoyBottomSheetDialog.Builder<>(requireContext());
        builder.setActions(actions)
                .setTitle(getString(R.string.joynote_translate_display))
                .setCheckStyle(R.style.BottomSheetDialogCheck_Tick)
                .setDescription(getString(R.string.joynote_translate_translated_by_yanxi))
                .setOnItemClickListener(true, (action, integer) -> {
            mDetailViewModel.updateSelectedLanguage(action.getData());
            return null;
        });
        JoyBottomSheetDialog<JoyNoteLanguageModel> sheetDialog = new JoyBottomSheetDialog<>(requireContext(), builder);
        sheetDialog.show();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mePlayer != null) {
            mePlayer.onResume();
        }
    }

    @Override
    public void onPause() {
        if (mePlayer != null) {
            mePlayer.onPause();
        }
        super.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mePlayer != null) {
            mePlayer.onDestroy();
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mePlayer != null) {
            mePlayer.configurationChanged(newConfig);
        }
        Activity activity = getActivity();
        if (mFlVideo == null || activity == null) {
            return;
        }
        if (this.getResources().getConfiguration().orientation
                == Configuration.ORIENTATION_LANDSCAPE) {
            mToolbar.setVisibility(View.GONE);
            mChatView.setVisibility(View.GONE);
            mViewPager.setVisibility(View.GONE);
            activity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN);
            activity.getWindow().getDecorView().invalidate();
            mFlVideo.getLayoutParams().height = WRAP_CONTENT;

        } else {
            mToolbar.setVisibility(View.VISIBLE);
            mChatView.setVisibility(View.VISIBLE);
            mViewPager.setVisibility(View.VISIBLE);
            final WindowManager.LayoutParams attrs = activity.getWindow().getAttributes();
            attrs.flags &= (~WindowManager.LayoutParams.FLAG_FULLSCREEN);
            activity.getWindow().setAttributes(attrs);
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
            float height = dp2px(activity, 200.f);
            mFlVideo.getLayoutParams().height = (int) height;
        }
        mFlVideo.getLayoutParams().width = WRAP_CONTENT;
        if (mePlayer != null) {
            mePlayer.syncProgress();
            mePlayer.setTitle(joyNoteDetailInfoModel.minutesDetail.title);
        }
    }

    private void showSearch() {
        Intent intent = new Intent(ACTION_JOY_NOTE_DETAIL_TEXT);
        intent.putExtra("type", TYPE_SEARCH);
        if (getContext() != null) {
            LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
        }
        JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_Detail, JDMAConstants.Mobile_Event_Minute_Search, new HashMap<>());
    }


    private void showEditDialog() {
        JoyNoteEditDialog joyNoteEditDialog = new JoyNoteEditDialog(getContext(), mToolbar.getTitle());
        joyNoteEditDialog.setOnClickListener(new JoyNoteEditDialog.OnClickListener() {
            @Override
            public void onClickOk(String inputText) {
                if (TextUtils.isEmpty(inputText)) {
                    ToastUtils.showToast(getString(R.string.joy_note_edit_input_null));
                    return;
                }
                if (getActivity() != null && getActivity() instanceof JoyNoteDetailActivity) {
                    JoyNoteDetailActivity activity = (JoyNoteDetailActivity) getActivity();
                    JoyNoteDetailRepo.getRepo().updateTitle(activity.joyNoteDetailInfoModel.minutesDetail.minutesId, inputText, new LoadDataCallback<Object>() {
                        @Override
                        public void onDataLoaded(Object o) {
                            mToolbar.setTitle(inputText);
                            ToastUtils.showToast(getString(R.string.joynote_rename_success));

                            Intent intent = new Intent();
                            intent.putExtra("newTitle", inputText);
                            try {
                                Intent param = activity.getIntent();
                                intent.putExtra(MINUTES_ID, param.getStringExtra(MINUTES_ID));
                                intent.putExtra(POSITION, param.getIntExtra(POSITION, -1));

                                Intent intent1 = new Intent("action.joynote.update.joyspace");
                                intent1.putExtra("type", "EVENT_RENAME");
                                intent1.putExtra("name", inputText);
                                intent1.putExtra("minutesId", activity.minutesId);
                                LocalBroadcastManager.getInstance(activity)
                                        .sendBroadcast(intent1);

                                //通知列表页刷新
                                Intent intent2 = new Intent(ACTION_LIST_UPDATE);
                                intent2.putExtra(OPERATION_TYPE, RENAME_MINUTES);
                                intent2.putExtra(MINUTE_ID, activity.minutesId);
                                intent2.putExtra(NEW_TITLE_NAME, inputText);
                                LocalBroadcastManager.getInstance(activity).sendBroadcast(intent2);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            activity.setResult(Activity.RESULT_OK, intent);
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            ToastUtils.showToast(getString(R.string.joynote_modify_fail));
                        }
                    });
                }
            }

            @Override
            public void onClickCancel() {

            }
        });
        joyNoteEditDialog.show();
    }
}