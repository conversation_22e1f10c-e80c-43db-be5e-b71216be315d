package com.jd.oa.joy.note.compunent

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.annotation.IntDef
import com.jd.oa.joy.note.R
import com.jd.oa.utils.DensityUtil
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.properties.Delegates

class SpeakerTimelineView(
    context: Context?,
    attrs: AttributeSet?,
    defStyleAttr: Int
) : View(context, attrs, defStyleAttr) {

    companion object {
        const val DEFAULT_HEIGHT = 6f    //DP
        const val INDICATOR_COLOR = 0xFF4687F7
        const val DEFAULT_PADDING = 4
        const val DEFAULT_TRACK_COLOR = Color.LTGRAY
        const val HIGHLIGHT_COLOR = 0xFF4687F7
    }

    var paragraphs = mutableListOf<SpeakerParagraph>()
    set(value) {
        field = value
        invalidate()
    }

    var totalDuration: Long = 0
    set(value) {
        field = value
        invalidate()
    }

    var color: Int = HIGHLIGHT_COLOR.toInt()
        set(value) {
            field = value
            mIndicatorColor = value
            mHighlightColor = value
            mParagraphPaint.color = mHighlightColor
            mProgressIndicatorPaint.color = mIndicatorColor
            invalidate()
        }

    private var progressIndicator: ProgressIndicator = ProgressIndicator(
        0f,
        ProgressIndicator.STATE_HIDE
    )

    private var mTrackHeight by Delegates.notNull<Int>()

    private var mTrackColor by Delegates.notNull<Int>()
    private var mIndicatorRadius by Delegates.notNull<Int>()

    private var mIndicatorColor by Delegates.notNull<Int>()
    private var mHighlightHeight by Delegates.notNull<Int>()

    private var mHighlightColor by Delegates.notNull<Int>()

    private var mBackgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    private var mParagraphPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    private var mProgressIndicatorPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = DensityUtil.dp2px(context, 2f).toFloat()
    }

    private var mHighlightParagraph: SpeakerParagraph? = null
    private var listener: ISpeakerProgress? = null

    private var mLastX = 0f
    private var mLastY = 0f

    private val contentWidth
        get() = width - paddingStart - paddingEnd

    constructor(context: Context?): this(context, null, 0)
    constructor(context: Context?, attrs: AttributeSet?): this(context, attrs, 0)

    init {
        val typedArray = context?.obtainStyledAttributes(attrs, R.styleable.SpeakerTimelineView)
        mTrackHeight = typedArray?.getDimensionPixelOffset(R.styleable.SpeakerTimelineView_trackHeight, DensityUtil.dp2px(context, DEFAULT_HEIGHT)) ?: DensityUtil.dp2px(context, DEFAULT_HEIGHT)
        mTrackColor = typedArray?.getColor(R.styleable.SpeakerTimelineView_trackColor, DEFAULT_TRACK_COLOR) ?: DEFAULT_TRACK_COLOR
        mIndicatorRadius = typedArray?.getDimensionPixelOffset(R.styleable.SpeakerTimelineView_indicatorRadius, (mTrackHeight / 1.8).toInt()) ?: (mTrackHeight / 1.8).toInt()
        mIndicatorColor = typedArray?.getColor(R.styleable.SpeakerTimelineView_indicatorColor, INDICATOR_COLOR.toInt()) ?: INDICATOR_COLOR.toInt()
        mHighlightHeight = typedArray?.getDimensionPixelOffset(R.styleable.SpeakerTimelineView_highlightHeight, mTrackHeight) ?: mTrackHeight
        mHighlightColor = typedArray?.getColor(R.styleable.SpeakerTimelineView_highlightColor, HIGHLIGHT_COLOR.toInt()) ?: HIGHLIGHT_COLOR.toInt()
        typedArray?.recycle()

        mBackgroundPaint.color = mTrackColor
        mParagraphPaint.color = mHighlightColor
        mProgressIndicatorPaint.color = mIndicatorColor
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        val height = when(heightMode) {
            MeasureSpec.EXACTLY -> heightSize
            MeasureSpec.AT_MOST -> {
                min(max(mTrackHeight, mIndicatorRadius * 2), heightSize)
            }
            else -> mIndicatorRadius * 2
        } + paddingTop + paddingBottom

        val widthSize = MeasureSpec.getSize(widthMeasureSpec)

        setMeasuredDimension(widthSize, height)
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.drawRoundRect(
            paddingStart.toFloat(),
            (height - mTrackHeight) / 2f,
           width.toFloat() - paddingEnd,
            (height + mTrackHeight) / 2f,
            mTrackHeight / 2f,
            mTrackHeight / 2f,
            mBackgroundPaint
        )

        canvas?.save()
        canvas?.translate(paddingStart.toFloat(), 0f)
        repeat(paragraphs.size) {
            val paragraph = paragraphs[it]
            val start = paragraph.start.toFloat() / totalDuration * contentWidth
            val end = paragraph.end.toFloat() / totalDuration * contentWidth

            canvas?.drawRoundRect(
                start,
                (height - mHighlightHeight) / 2f,
                end,
                (height + mHighlightHeight) / 2f,
                mHighlightHeight / 2f,
                mHighlightHeight / 2f,
                mParagraphPaint
            )
        }
        canvas?.restore()

        if (progressIndicator.state == ProgressIndicator.STATE_SHOW) {
            mProgressIndicatorPaint.color = Color.WHITE
            mProgressIndicatorPaint.style = Paint.Style.FILL
            canvas?.drawCircle(progressIndicator.position, height / 2f, mIndicatorRadius.toFloat(), mProgressIndicatorPaint)

            mProgressIndicatorPaint.color = mIndicatorColor
            mProgressIndicatorPaint.style = Paint.Style.STROKE
            canvas?.drawCircle(progressIndicator.position, height / 2f, mIndicatorRadius.toFloat(), mProgressIndicatorPaint)
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        val x = event?.getX(0) ?: return false
        val y = event.getY(0)
        when(event.action) {
            MotionEvent.ACTION_DOWN -> {
                val time = positionToTime(x)
                mHighlightParagraph = paragraphs.find { time >= it.start && time < it.end }
                if (mHighlightParagraph != null) {
                    progressIndicator.run {
                        position = x
                        state = ProgressIndicator.STATE_SHOW
                    }
                    progressIndicator.touching = true
                    invalidate()
                    return true
                } else {
                    progressIndicator.run {
                        position = 0f
                        state = ProgressIndicator.STATE_HIDE
                    }
                    return false
                }
            }
            MotionEvent.ACTION_MOVE -> {
                mHighlightParagraph?.let {
                    val time = positionToTime(x)
                    if (time >= it.start && time <= it.end) {
                        progressIndicator.position = x
                        invalidate()
                    }
                }
                val deltaX = x - mLastX
                val deltaY = y - mLastY
                if (abs(deltaX) > abs(deltaY)) {
                    parent.requestDisallowInterceptTouchEvent(true)
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                mHighlightParagraph?.let {
                    val time = positionToTime(x)
                    if (time >= it.start && time <= it.end) {
                        listener?.progress(time.toFloat())
                    } else if (time < it.start) {
                        listener?.progress(it.start.toFloat())
                    } else {
                        listener?.progress(it.end.toFloat())
                    }
                }
                progressIndicator.touching = false
                invalidate()
            }
        }
        mLastX = x
        mLastY = y

        return super.onTouchEvent(event)
    }

    private fun positionToTime(position: Float): Long = ((position - paddingStart) / contentWidth.toFloat() * totalDuration).toLong()

    private fun timeToPosition(time: Long): Float = paddingStart + (time / totalDuration.toFloat() * contentWidth)

    fun setProgressListener(listener: ISpeakerProgress) {
        this.listener = listener
    }

    fun updateProgress(progress: Long) {
        if (progressIndicator.touching) return

        val paragraph = paragraphs.find { progress >= it.start && progress < it.end }
        if (paragraph != null) {
            progressIndicator.position = timeToPosition(progress)
            progressIndicator.state = ProgressIndicator.STATE_SHOW
        } else {
            progressIndicator.state = ProgressIndicator.STATE_HIDE
        }
        invalidate()
    }
}

class ProgressIndicator(
    var position: Float = 0f,
    @IndicatorState var state: Int,
    var touching: Boolean = false
) {
    companion object {
        const val STATE_HIDE = 0
        const val STATE_SHOW = 1

        @Retention(AnnotationRetention.SOURCE)
        @IntDef(STATE_HIDE, STATE_SHOW)
        annotation class IndicatorState
    }
}

class SpeakerParagraph(val start: Long, val end: Long)

interface ISpeakerProgress {
    fun progress(currentProgress: Float)
}