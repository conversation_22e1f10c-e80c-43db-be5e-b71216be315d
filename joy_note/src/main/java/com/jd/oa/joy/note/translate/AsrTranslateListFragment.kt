package com.jd.oa.joy.note.translate

import android.animation.ValueAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.animation.doOnEnd
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.jd.oa.JDMAConstants.MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_SHUANGYU
import com.jd.oa.JDMAConstants.MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_YIWEN
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joy.note.databinding.ItemAsrAndTranslateContentWidgetBinding
import com.jd.oa.joy.note.record.RecordIntent
import com.jd.oa.joy.note.viewmodel.JoyNoteRecordAudioViewModel
import com.jd.oa.utils.clickEvent
import kotlinx.coroutines.launch

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/28
 * @Description:
 */
class AsrTranslateListFragment : BaseFragment() {
    private var _binding: ItemAsrAndTranslateContentWidgetBinding? = null
    private val binding get() = _binding!!
    private val viewModel: JoyNoteRecordAudioViewModel by activityViewModels {
        JoyNoteRecordAudioViewModel.Factory()
    }
    private val hideHideView: () -> Unit by lazy {
        {
            binding.resultHintContainer.isVisible = false
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ItemAsrAndTranslateContentWidgetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViewModel()
        initView()
    }

    override fun onResume() {
        super.onResume()
        binding.inAsrText.smoothScroll()
        binding.vsTranslateText.smoothScroll()
    }

    private fun initView() {
        binding.vsTranslateText.isVisible = if (enableTranslate()) {
            binding.guideline.setGuidelinePercent(0.5f)
            true
        } else {
            binding.guideline.setGuidelinePercent(1f)
            false
        }
        binding.inAsrText.setVisibleViewBottom {
            binding.vsTranslateText.smoothScroll()
        }
        binding.vsTranslateText.setVisibleViewBottom {
            binding.inAsrText.smoothScroll()
        }
        // lottie json 第 21帧开始都是空白帧
        binding.animationView.setMinAndMaxFrame(0, 19)
    }

    private fun initViewModel() {
        viewModel.onlyDisplayTranslate.observe(viewLifecycleOwner) {
            binding.inAsrText.isVisible = !it
            ValueAnimator.ofFloat(*(if (!it) floatArrayOf(0f, 0.5f) else floatArrayOf(0.5f, 0f)))
                .apply {
                    duration = 200
                    addUpdateListener { value ->
                        binding.guideline.setGuidelinePercent(value.animatedValue as Float)
                    }
                    doOnEnd {
                        binding.vsTranslateText.smoothScroll()
                    }
                    start()
                }
            clickEvent(
                if (it) {
                    MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_YIWEN
                } else {
                    MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_SHUANGYU
                }
            )
        }
        viewModel.recordContent.observe(viewLifecycleOwner) {
            binding.inAsrText.setRecordContent(it, false, hideHideView)
            binding.vsTranslateText.setRecordContent(it, true, hideHideView)
        }
        lifecycleScope.launch {
            viewModel.asrTranslateText.collect {
                // asr、translate 同时返回
                binding.inAsrText.refreshAsrTranslateList(
                    it,
                    it.payload?.dynamicResult?.result ?: "",
                    it.payload?.stableResult?.result ?: "",
                    hideHideView
                )
                if (enableTranslate()) {
                    binding.vsTranslateText.refreshAsrTranslateList(
                        it,
                        it.payload?.dynamicResult?.translateResult ?: "",
                        it.payload?.stableResult?.translateResult ?: "",
                        hideHideView
                    )
                }
            }
        }
        viewModel.recordState.observe(viewLifecycleOwner) {
            if (it < RecordIntent.Pause) {
                binding.animationView.playAnimation()
            } else {
                binding.animationView.pauseAnimation()
            }
        }
    }

    private fun enableTranslate() = arguments?.getBoolean("enable_translate") == true
}