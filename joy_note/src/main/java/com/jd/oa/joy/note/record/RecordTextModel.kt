package com.jd.oa.joy.note.record

class RecordTextModel(dynamicText: String = "", stableText: String = "") {


    lateinit var dynamicText: String
    lateinit var stableText: String
    lateinit var userName: String
    lateinit var userAvatar: String
    var startTime: Long = 0

    init {
        if (dynamicText.isNotEmpty()) {
            this.dynamicText = dynamicText
        } else {
            this.dynamicText = ""
        }
        if (stableText.isNotEmpty())
            this.stableText = stableText
        else
            this.stableText = ""
    }


}