package com.jd.oa.joy.note.translate

import androidx.fragment.app.commit
import com.chenenyu.router.annotation.Route
import com.jd.oa.JDMAConstants.MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_DETACH
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.model.VideoItemModel.AUDIO_TRANSLATE
import com.jd.oa.joy.note.notefloat.NoteFloatManager
import com.jd.oa.joy.note.record.AudioRecordCurrent
import com.jd.oa.joy.note.record.BaseAudioActivity
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.clickEvent

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/25
 * @Description:
 */
@Route(DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE)
class RealTimeTranslateActivity : BaseAudioActivity() {

    override fun initView() {
        super.initView()
        loadTranslateWidget()
        loadAsrTranslateWidget(true)
        viewModel.getTranslateLanguage(this)
        binding.titleBar.setRightBtnAListener(R.string.icon_pad_independentview) {
            handleActivityBackPressed()
            clickEvent(MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_DETACH)
        }
        AudioRecordCurrent.type = AUDIO_TRANSLATE
    }

    override fun onResume() {
        super.onResume()
        FloatingTranslateManager.hideFloatingView(this)
        NoteFloatManager.getInstance().logoutFloatingView(this)
    }

    override fun showFloatWindow() {
        FloatingTranslateManager.showFloatingView(this)
    }

    private fun loadTranslateWidget() {
        supportFragmentManager.commit {
            replace(
                R.id.translate_switch_container,
                TranslateControlFragment(),
                TranslateControlFragment::class.simpleName
            )
        }
    }
}