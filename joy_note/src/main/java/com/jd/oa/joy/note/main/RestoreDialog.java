package com.jd.oa.joy.note.main;

import static com.jd.oa.joy.note.JoyNoteTools.MINUTES_ID;
import static com.jd.oa.joy.note.JoyNoteTools.POSITION;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.VideoItemModel;
import com.jd.oa.router.DeepLink;

public class RestoreDialog extends DialogFragment {
    private final VideoItemModel itemModel;
    private final JoyNoteListFragment joyNoteListFragment;

    private final Handler handler = new Handler(Looper.getMainLooper());

    public RestoreDialog(@NonNull JoyNoteListFragment joyNoteListFragment, @NonNull VideoItemModel itemModel) {
        this.itemModel = itemModel;
        this.joyNoteListFragment = joyNoteListFragment;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        Dialog dialog = new Dialog(requireContext(), R.style.commDialogStyle_no_bg);

        handler.postDelayed(() -> {
            if (dialog.isShowing()) {
                dismiss();
            }
        }, 3000);
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.joy_note_dialog_restore, container, true);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        View dialogViewContent = view.findViewById(R.id.restore_tips_btn);
        dialogViewContent.setOnClickListener(v -> {
            dismiss();
            Activity activity = getActivity();
            if (activity == null) {
                return;
            }

            Intent intent = DeepLinkTools.getJoyNoteIntent(getActivity(), itemModel.title, itemModel.minutesId, DeepLink.JOY_NOTE_DETAIL);
            if (intent == null) {
                return;
            }
            intent.putExtra(MINUTES_ID, itemModel.minutesId);
            intent.putExtra(POSITION, -1);
            joyNoteListFragment.launch(intent);
        });
    }
}

