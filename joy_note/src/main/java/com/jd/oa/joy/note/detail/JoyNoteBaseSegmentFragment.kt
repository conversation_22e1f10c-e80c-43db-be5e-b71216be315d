package com.jd.oa.joy.note.detail

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.Html
import android.text.Html.FROM_HTML_MODE_LEGACY
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.jd.oa.JDMAConstants
import com.jd.oa.JDMAPages
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joy.note.KeyWordUtil
import com.jd.oa.joy.note.KeyboardUtils
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.model.JoyNoteAsrData
import com.jd.oa.joy.note.model.JoyNoteAsrData.Sentences
import com.jd.oa.joy.note.model.JoyNoteAsrData.TRANSLATE_STATE_SUCCESS
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel
import com.jd.oa.joy.note.model.JoyNoteKeywordPosition
import com.jd.oa.joy.note.model.JoyNoteLanguageModel
import com.jd.oa.joy.note.model.JoyNoteSentencePosition
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo
import com.jd.oa.joy.note.viewmodel.JoyNoteBaseTextViewModel
import com.jd.oa.joy.note.viewmodel.JoyNoteDetailViewModel
import com.jd.oa.joy.note.viewmodel.JoyNoteMediaPlayerViewModel
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.JDMAUtils
import com.zhy.view.flowlayout.FlowLayout
import com.zhy.view.flowlayout.TagAdapter
import com.zhy.view.flowlayout.TagFlowLayout
import kotlin.math.min

/**
 * @Author: hepiao3
 * @CreateTime: 2025/3/13
 * @Description:
 */
abstract class JoyNoteBaseSegmentFragment : BaseFragment() {
    private lateinit var mRvTextList: RecyclerView
    private lateinit var mFlowLayout: TagFlowLayout
    private lateinit var mLlSearch: LinearLayout
    private lateinit var mEtSearchInput: EditText
    private lateinit var mIvSearchNext: IconFontView
    private lateinit var mIvSearchLast: IconFontView
    private lateinit var mTvSearchComplete: TextView
    private lateinit var mTvSearchPosition: TextView
    private lateinit var mAdapter: JoyNoteDetailTextAdapter
    private lateinit var mLlEmpty: View
    private lateinit var speakingLocationButton: View

    // 所有关键词列表 用于当前关键词高亮显示
    private val keywordPositionList: MutableList<JoyNoteKeywordPosition> = arrayListOf()

    // 所有句子的列表
    protected val sentencePositionList: MutableList<JoyNoteSentencePosition> = arrayListOf()
    private val joyNoteAsrDataList: MutableList<JoyNoteAsrData> = arrayListOf()

    // 当前是在全文中的第几句
    private var mCurrentSentence = -1
    private var currentKeyWordPosition = -1
    private var lastUpdateVideoPositionTime = 0L
    private var infoModel: JoyNoteDetailInfoModel? = null
    private var isFromJoySpace = false
    private var pendingVideoPosition: Long? = null
    private var isProhibitAutoScroll = false
    private var mIsManual = false
    private var mPopupWindow: PopupWindow? = null

    // 建立延迟任务的目的：保证最后一次通知的视频进度被拦截，后续仍然能更新
    private val updateRunnable = Runnable {
        pendingVideoPosition?.let { pos ->
            processVideoPosition(pos, mIsManual)
            pendingVideoPosition = null
        }
    }
    private val handler by lazy { Handler(Looper.getMainLooper()) }
    protected val mViewModel: JoyNoteMediaPlayerViewModel by lazy {
        ViewModelProvider(requireActivity())[JoyNoteMediaPlayerViewModel::class.java]
    }

    protected val mDetailViewModel: JoyNoteDetailViewModel by lazy {
        ViewModelProvider(requireActivity())[JoyNoteDetailViewModel::class.java]
    }

    protected val mTextViewModel: JoyNoteBaseTextViewModel by lazy {
        ViewModelProvider(this)[JoyNoteBaseTextViewModel::class.java]
    }

    abstract val viewType: Int

    abstract val api: String?

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_joy_note_detail_text, container)
        initView(view)
        initViewModel()
        return view
    }

    protected open fun initViewModel() {
        mViewModel.mediaInfoLiveData.observe(viewLifecycleOwner) { info: Triple<Int, Boolean, Boolean?> ->
            // 手动更新视频进度，解除列表自动滚动限制
            if (info.second) prohibitAutoScroll(false)
            setSentenceHighLightByTime(info.first.toLong(), info.second)
        }
        mViewModel.sentencesLiveData.observe(viewLifecycleOwner) {
            prohibitAutoScroll(false)
            processVideoPosition(it.toLong(), true)
        }

        mDetailViewModel.selectedLanguage.observe(viewLifecycleOwner) {
            if (it == null || it.isDefaultLanguage) {
                mAdapter.resetDefaultLanguage()
                JDMAUtils.clickEvent(
                    JDMAPages.Mobile_Page_Minute_Detail,
                    MOBILE_EVENT_MINUTES_DETAIL_HOME_TRANSLATE_VIEW_ORIGINAL_TEXT,
                    null
                )
            } else {
                onSearchComplete()

                mAdapter.languageModel = it
                val list = findVisibleUntranslatedItem(it)
                mTextViewModel.translateAsrData(it, list)

                val params = hashMapOf("language" to it.language)
                JDMAUtils.clickEvent(
                    JDMAPages.Mobile_Page_Minute_Detail,
                    MOBILE_EVENT_MINUTES_DETAIL_HOME_TRANSLATE,
                    params
                )
            }
            mAdapter.notifyDataSetChanged()
        }

        mTextViewModel.translatedAsrData.observe(viewLifecycleOwner) { list ->
            val languageModel = mDetailViewModel.selectedLanguage.value
            if (list == null || list.isEmpty()) return@observe

            val language = if (languageModel?.isDefaultLanguage == true) {
                null
            } else {
                languageModel
            }
            updateSentencePosition(language, list)
            mAdapter.refreshDatList(language, list, JoyNoteDetailTextAdapter.REFRESH_TEXT)
        }

        mTextViewModel.translatingAsrData.observe(viewLifecycleOwner) { list ->
            if (list == null || list.isEmpty()
                || mDetailViewModel.selectedLanguage.value == null
                || mDetailViewModel.selectedLanguage.value?.isDefaultLanguage == true
            ) return@observe
            mAdapter.refreshDatList(
                mDetailViewModel.selectedLanguage.value,
                list,
                JoyNoteDetailTextAdapter.REFRESH_STATE
            )
        }
    }

    private fun initView(view: View) {
        mFlowLayout = view.findViewById(R.id.flowlayout)
        mRvTextList = view.findViewById(R.id.rv_text)
        mLlSearch = view.findViewById(R.id.ll_search_layout)
        mEtSearchInput = view.findViewById(R.id.et_search_input)
        mIvSearchLast = view.findViewById(R.id.iv_search_last)
        mIvSearchNext = view.findViewById(R.id.iv_search_next)
        mTvSearchPosition = view.findViewById(R.id.tv_search_position)
        mTvSearchComplete = view.findViewById(R.id.tv_search_complete)
        mLlEmpty = view.findViewById(R.id.ll_empty)
        speakingLocationButton = view.findViewById(R.id.return_speaking_location)
        speakingLocationButton.setOnClickListener {
            mViewModel.mediaInfoLiveData.value?.let {
                val sentenceIndex = if (mCurrentSentence in 0 until sentencePositionList.size) {
                    mCurrentSentence
                } else {
                    findCurrentSentenceIndex(it.first.toLong())
                }
                handleScrollAction(sentenceIndex, true)
                prohibitAutoScroll(false)
            }
        }
        if (activity != null && activity is JoyNoteDetailActivity) {
            val activity = activity as? JoyNoteDetailActivity
            infoModel = activity!!.getJoyNoteDetailInfoModel()
            isFromJoySpace = JOY_SPACE.equals(activity.channel, ignoreCase = true)
        }
        mRvTextList.layoutManager = LinearLayoutManager(context)
        mAdapter = JoyNoteDetailTextAdapter(joyNoteAsrDataList, context, viewType)
        mAdapter.setFromJoySpace(isFromJoySpace)
        infoModel?.minutesDetail?.let {
            val isAudio = it.type == 2
            mAdapter.setIsAudio(isAudio)
        }
        mAdapter.setOnDetailItemClickListener(object : OnDetailItemClickListener {
            override fun onSentenceClick(sentencesBean: Sentences) {
                prohibitAutoScroll(false)
                mCurrentSentence = sentencePositionList.indexOfFirst {
                    it.startTime == sentencesBean.beginMs.toLong()
                }
                mViewModel.setSentences(sentencesBean.beginMs)
                if (viewType == JOY_NOTE_RELATED_ME) {
                    JDMAUtils.clickEvent(
                        JDMAPages.Mobile_Page_Minute_Detail,
                        JDMAConstants.MOBILE_EVENT_MINUTES_DETAIL_HOME_MENTIONS_CLICK_SPEECH,
                        null
                    )
                }
            }

            override fun onUserClick(user: JoyNoteAsrData.User) {
                val service = AppJoint.service(ImDdService::class.java)
                service.showContactDetailInfo(activity, user.ddAppId, user.account)
            }

            override fun onMerge(mergeText: String) {
                infoModel?.minutesDetail?.let {
                    val list = ArrayList<String>()
                    list.add(mergeText)
                    extractParagraph(list, it.minutesId)
                }
            }

            override fun onOriginalClick(paragraphId: String) {
                prohibitAutoScroll(true)
                mViewModel.setParagraphId(paragraphId)
                JDMAUtils.clickEvent(
                    JDMAPages.Mobile_Page_Minute_Detail,
                    JDMAConstants.MOBILE_EVENT_MINUTES_DETAIL_HOME_MENTIONS_VIEW_CONTEXT, null
                )
            }

            override fun onSentenceLongClick(view: TextView, paragraphId: String?) {
                val longClickSentence =
                    joyNoteAsrDataList.firstOrNull { it.paragraphId == paragraphId }
                mDetailViewModel.selectedLanguage.value?.let {
                    if (!it.isDefaultLanguage &&
                        longClickSentence?.getTranslateState(it) == TRANSLATE_STATE_SUCCESS
                    ) {
                        view.setSelected(true)
                        showBubblePopup(view, longClickSentence.getAllSentences(null) ?: "")
                    }
                }
            }

            override fun onFailedClick(asrData: JoyNoteAsrData?) {
                translateVisibleItems()
                JDMAUtils.clickEvent(
                    JDMAPages.Mobile_Page_Minute_Detail,
                    MOBILE_EVENT_MINUTES_DETAIL_HOME_TRANSLATE_RETRY,
                    null
                )
            }
        })
        mRvTextList.adapter = mAdapter
        mRvTextList.itemAnimator = null
        mRvTextList.addOnScrollListener(object : OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                // 检测列表是否被拖动
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING -> {
                        prohibitAutoScroll(true)
                        mPopupWindow?.dismiss()
                    }

                    RecyclerView.SCROLL_STATE_IDLE -> {
                        translateVisibleItems()
                    }

                    RecyclerView.SCROLL_STATE_SETTLING -> {
                        mPopupWindow?.dismiss()
                    }
                }
            }
        })
        infoModel?.minutesDetail?.let {
            getParagraphList(it.minutesId)
        }
        initSearch()
        initFlowLayout(mFlowLayout)
    }

    private fun translateVisibleItems() {
        val language = mDetailViewModel.selectedLanguage.value
        if (language?.isDefaultLanguage == true) return
        val list = findVisibleUntranslatedItem(language)
        mTextViewModel.translateAsrData(language, list)
        mAdapter.notifyDataSetChanged()
    }

    private fun initSearch() {
        mTvSearchComplete.setOnClickListener { onSearchComplete() }
        mIvSearchNext.setOnClickListener { nextKeyword() }
        mIvSearchLast.setOnClickListener { lastKeyword() }
        mEtSearchInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable) {
                findKeyWords(s.toString())
            }
        })
    }

    private fun onSearchComplete() {
        mEtSearchInput.setText("")
        KeyboardUtils.hideKeyboard(mEtSearchInput, activity)
        mLlSearch.visibility = View.GONE
        // 取消关键词选中状态
        if (mFlowLayout.adapter != null) {
            mFlowLayout.adapter.setSelectedList()
        }
    }

    private fun initFlowLayout(mFlowLayout: TagFlowLayout) {
        if (infoModel == null || infoModel?.minutesDetail == null) return
        if (infoModel?.minutesDetail?.keywords.isNullOrEmpty()) {
            mFlowLayout.visibility = View.GONE
            return
        }
        val keywords = infoModel?.minutesDetail?.keywords
        mFlowLayout.adapter = object : TagAdapter<String?>(keywords) {
            override fun getView(flowLayout: FlowLayout, i: Int, text: String?): View {
                val tv = View.inflate(
                    context,
                    R.layout.joy_note_item_detail_flow_layout_tag,
                    null
                ) as TextView
                tv.text = text
                return tv
            }
        }
        mFlowLayout.visibility = View.VISIBLE
        mFlowLayout.setOnTagClickListener { _: View?, i: Int, _: FlowLayout? ->
            val selectedList = mFlowLayout.selectedList
            if (selectedList == null || selectedList.isEmpty()) {
                mEtSearchInput.setText("")
                mLlSearch.visibility = View.GONE
            } else {
                //暂时不用考虑多选的情况
                if (mLlSearch.visibility != View.VISIBLE) {
                    mLlSearch.visibility = View.VISIBLE
                }
                mEtSearchInput.setText(keywords?.get(i) ?: "")
            }

            JDMAUtils.clickEvent(
                JDMAPages.Mobile_Page_Minute_Detail,
                JDMAConstants.Mobile_Event_Minute_Keyword,
                HashMap()
            )
            false
        }
        mFlowLayout.adapter.notifyDataChanged()
    }

    protected fun prohibitAutoScroll(enable: Boolean) {
        isProhibitAutoScroll = enable
        handler.removeCallbacks(updateRunnable)
        if (!enable) {
            speakingLocationButton.isVisible = false
        }
    }

    private fun getParagraphList(mId: String?) {
        JoyNoteDetailRepo.getRepo()
            .getParagraphList(mId, api, object : LoadDataCallback<List<JoyNoteAsrData>?> {
                @SuppressLint("NotifyDataSetChanged")
                override fun onDataLoaded(joyNoteAsrData: List<JoyNoteAsrData>?) {
                    if (activity == null || activity!!.isFinishing) return
                    if (joyNoteAsrData.isNullOrEmpty()) {
                        sentencePositionList.clear()
                        joyNoteAsrDataList.clear()
                        mAdapter.notifyDataSetChanged()
                        mLlEmpty.visibility = View.VISIBLE
                        return
                    }

                    joyNoteAsrDataList.clear()
                    joyNoteAsrDataList.addAll(joyNoteAsrData)
                    sentencePositionList.clear()

                    updateSentencePosition(null, joyNoteAsrData)

                    mAdapter.notifyDataSetChanged()
                }

                override fun onDataNotAvailable(s: String, i: Int) {
                }
            })
    }

    private fun updateSentencePosition(
        languageModel: JoyNoteLanguageModel?,
        joyNoteAsrData: List<JoyNoteAsrData>
    ) {
        for (i in joyNoteAsrData.indices) {
            val asrData = joyNoteAsrData[i]
            val sentences = asrData.sentences
            var startIndex = 0
            val paragraphId = asrData.paragraphId
            for (j in sentences.indices) {
                val sentencesBean = sentences[j]
                val position =
                    sentencePositionList.find { it.paragraphId == asrData.paragraphId && it.sentenceId == sentencesBean.sentenceId }
                val text = sentencesBean.getText(languageModel) ?: sentencesBean.text
                if (position == null) {
                    sentencePositionList.add(
                        JoyNoteSentencePosition(
                            i, j, startIndex,
                            startIndex + text.length,
                            text, sentencesBean.beginMs.toLong(),
                            sentencesBean.endMs.toLong(),
                            paragraphId,
                            sentencesBean.sentenceId
                        )
                    )
                } else {
                    position.startIndex = startIndex
                    position.endIndex = startIndex + text.length
                    position.text = text
                }

                startIndex += text.length
            }
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun findKeyWords(keyword: String = "") {
        prohibitAutoScroll(true)
        if (mLlSearch.visibility != View.VISIBLE) {
            mLlSearch.visibility = View.VISIBLE
            speakingLocationButton.isVisible = false
        }
        if (mEtSearchInput.text.toString() != keyword) {
            mEtSearchInput.setText(keyword)
        }
        keywordPositionList.clear()
        for (i in joyNoteAsrDataList.indices) {
            val segment = joyNoteAsrDataList[i]
            val allKeywordIndex =
                KeyWordUtil.getAllKeywordIndex(segment.getAllSentences(null), keyword)
            for (j in allKeywordIndex.indices) {
                keywordPositionList.add(
                    JoyNoteKeywordPosition(
                        i, j,
                        allKeywordIndex[j], allKeywordIndex[j] + keyword.length
                    )
                )
            }
        }

        if (keywordPositionList.isNotEmpty()) {
            currentKeyWordPosition = 0
            mAdapter.setJoyNoteKeywordPosition(keywordPositionList[0])
            mTvSearchPosition.visibility = View.VISIBLE
            mIvSearchLast.visibility = View.VISIBLE
            mIvSearchNext.visibility = View.VISIBLE

            searchKeywordScroll(0)
        } else {
            mTvSearchPosition.visibility = View.GONE
            mIvSearchLast.visibility = View.INVISIBLE
            mIvSearchNext.visibility = View.INVISIBLE
        }
        updateKeywordPosition()
        mAdapter.setKeyword(keyword)
        mAdapter.highLightTv = null
        mAdapter.notifyDataSetChanged()
    }


    private fun nextKeyword() {
        if (currentKeyWordPosition + 1 >= keywordPositionList.size) {
            currentKeyWordPosition = 0
        } else currentKeyWordPosition++
        searchKeywordScroll(currentKeyWordPosition)
        mAdapter.setJoyNoteKeywordPosition(keywordPositionList[currentKeyWordPosition])
        updateKeywordPosition()
        mAdapter.notifyItemChanged(keywordPositionList[currentKeyWordPosition].segmentPosition)
        // 上一个关键词
        val lastPosition =
            if (currentKeyWordPosition <= 0) keywordPositionList.size - 1 else currentKeyWordPosition - 1
        if (keywordPositionList[lastPosition].segmentPosition !=
            keywordPositionList[currentKeyWordPosition].segmentPosition
        ) {
            // 上一个也需要更新
            mAdapter.notifyItemChanged(keywordPositionList[lastPosition].segmentPosition)
        }
    }

    private fun lastKeyword() {
        if (currentKeyWordPosition - 1 < 0) {
            currentKeyWordPosition = keywordPositionList.size - 1
        } else currentKeyWordPosition--
        searchKeywordScroll(currentKeyWordPosition)
        mAdapter.setJoyNoteKeywordPosition(keywordPositionList[currentKeyWordPosition])
        updateKeywordPosition()
        mAdapter.notifyItemChanged(keywordPositionList[currentKeyWordPosition].segmentPosition)

        // 下一个关键词
        val nextPosition =
            if (currentKeyWordPosition >= keywordPositionList.size - 1) 0 else currentKeyWordPosition + 1
        if (keywordPositionList[nextPosition].segmentPosition !=
            keywordPositionList[currentKeyWordPosition].segmentPosition
        ) {
            // 下一个也需要更新
            mAdapter.notifyItemChanged(keywordPositionList[nextPosition].segmentPosition)
        }
    }

    private fun searchKeywordScroll(currentKeyWordPosition: Int) {
        (mRvTextList.layoutManager as? LinearLayoutManager)?.scrollToPositionWithOffset(
            keywordPositionList[currentKeyWordPosition].segmentPosition,
            0
        )
    }

    private fun updateKeywordPosition() {
        val position = currentKeyWordPosition + 1
        val content =
            "<font color=\"#333333\">" + position + "</font>" +
                    "<font color=\"#999999\">" + "/" + keywordPositionList.size + "</font>"
        mTvSearchPosition.text = Html.fromHtml(content, FROM_HTML_MODE_LEGACY)
    }

    override fun onResume() {
        super.onResume()
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(
            receiver,
            IntentFilter(JoyNoteDetailFragment.ACTION_JOY_NOTE_DETAIL_TEXT)
        )
    }

    override fun onPause() {
        super.onPause()
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(receiver)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        handler.removeCallbacks(updateRunnable)
    }

    private val receiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val type = intent.getIntExtra("type", 0)
            if (JoyNoteDetailFragment.TYPE_SEARCH == type) {
                findKeyWords()
                KeyboardUtils.showKeyboard(mEtSearchInput, activity)
            } else if (JoyNoteDetailFragment.TYPE_EXTRACT_ALL_PARAGRAPHS == type) {
                extractAllParagraphs()
            }
        }
    }

    /**
     * 根据时间高亮指定的句子
     */
    private fun setSentenceHighLightByTime(videoPosition: Long, isManual: Boolean) {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - lastUpdateVideoPositionTime

        // 更新最新位置并重置延迟任务
        pendingVideoPosition = videoPosition
        handler.removeCallbacks(updateRunnable)

        when {
            elapsedTime > JOY_NOTE_DELAY_TIME -> {
                // 立即处理并更新计时
                processVideoPosition(videoPosition, isManual)
                lastUpdateVideoPositionTime = currentTime
            }

            else -> {
                // 延迟剩余时间后处理
                mIsManual = isManual
                handler.postDelayed(updateRunnable, JOY_NOTE_DELAY_TIME)
            }
        }
    }

    /**
     * 定位按钮，句子高亮、滚动统一处理
     */
    private fun processVideoPosition(videoPosition: Long, isManual: Boolean) {
        val targetIndex = findCurrentSentenceIndex(videoPosition)
        if (targetIndex !in 0 until sentencePositionList.size) return
        controlReturnLocationButton(targetIndex, isManual)
        handleScrollAction(targetIndex, isManual)
        updateHighlightPosition(targetIndex, videoPosition)
    }

    private fun findCurrentSentenceIndex(videoPosition: Long): Int {
        if (sentencePositionList.isEmpty()) return -1

        var left = 0
        var right = sentencePositionList.lastIndex

        while (left <= right) {
            val mid = (left + right) ushr 1
            val current = sentencePositionList[mid]

            when {
                videoPosition >= current.endTime -> left = mid + 1
                videoPosition < current.startTime -> right = mid - 1
                else -> return mid // 精确匹配
            }
        }

        // 处理边界情况
        if (right < 0) return left
        if (left >= sentencePositionList.size) return right

        // 计算最近区间
        val leftDiff = sentencePositionList[left].startTime - videoPosition
        val rightDiff = videoPosition - sentencePositionList[right].endTime

        return if (rightDiff <= leftDiff) right else left
    }

    /**
     * 句子高亮和清除高亮
     */
    private fun updateHighlightPosition(
        targetIndex: Int,
        videoPosition: Long
    ) {
        val sentence = sentencePositionList[targetIndex]
        // 保存旧位置用于刷新,只刷新新旧位置的项目
        val previousPosition = mCurrentSentence.takeIf { it >= 0 }
            ?.let { sentencePositionList[it].segmentPosition }

        if (videoPosition in sentence.startTime..sentence.endTime) {
            if (targetIndex == mCurrentSentence) return
            mCurrentSentence = targetIndex
            mAdapter.setVideoPosition(sentence)
            previousPosition?.let {
                if (it != sentence.segmentPosition) {
                    mAdapter.notifyItemChanged(it, sentencePositionList[it].text)
                }
            }
            mAdapter.notifyItemChanged(sentence.segmentPosition, sentence.text)
        } else {
            mAdapter.clearHighLight()
        }
    }

    /**
     * 回到当前发言位置按钮 visible、gone 控制
     */
    private fun controlReturnLocationButton(targetIndex: Int, isManual: Boolean) {
        if (mLlSearch.isVisible || isManual || !isProhibitAutoScroll) return
        val targetSentence = sentencePositionList[targetIndex]
        val layoutManager = (mRvTextList.layoutManager as? LinearLayoutManager) ?: return
        val firstVisible = layoutManager.findFirstVisibleItemPosition()
        val lastVisible = layoutManager.findLastVisibleItemPosition()
        val targetPosition = targetSentence.segmentPosition

        speakingLocationButton.isVisible = targetPosition !in firstVisible..lastVisible
    }

    /**
     * 处理列表滚动
     */
    protected fun handleScrollAction(targetIndex: Int, isManual: Boolean = false) {
        if (!isManual && (isProhibitAutoScroll || targetIndex == mCurrentSentence)) return
        val sentence = sentencePositionList[targetIndex]
        val layoutManager = (mRvTextList.layoutManager as? LinearLayoutManager) ?: return
        val targetPosition = sentence.segmentPosition

        // 获取当前可见位置范围
        val firstVisible = layoutManager.findFirstVisibleItemPosition()
        val lastVisible = layoutManager.findLastVisibleItemPosition()
        val targetView = layoutManager.findViewByPosition(targetPosition)

        when {
            // view 非可见
            targetPosition !in firstVisible..lastVisible -> {
                // 快速定位 targetPosition 时取消滚动动画
                mRvTextList.apply {
                    if (isManual) {
                        scrollToPosition(targetPosition)
                        mRvTextList.post {
                            translateVisibleItems()
                        }
                    } else {
                        stopScroll()
                        smoothScrollToPosition(targetPosition)
                    }
                }
            }

            // view 部分可见
            targetView != null && !isViewFullyVisible(targetView) -> {
                val recyclerViewHeight: Int = mRvTextList.height
                val paddingBottom: Int = mRvTextList.paddingBottom
                val targetBottom = targetView.bottom

                val offset = targetBottom - (recyclerViewHeight - paddingBottom)
                if (offset != 0) mRvTextList.smoothScrollBy(0, offset)
            }
        }

        // 更新最后处理时间
        lastUpdateVideoPositionTime = System.currentTimeMillis()
    }

    /**
     * 判断视图是否完全可见
     */
    private fun isViewFullyVisible(view: View): Boolean {
        val layoutManager = mRvTextList.layoutManager as? LinearLayoutManager ?: return false
        return view.top >= layoutManager.paddingTop &&
                view.bottom <= layoutManager.height - layoutManager.paddingBottom
    }

    /**
     * 摘取全文
     */
    private fun extractAllParagraphs() {
        val paragraph = ArrayList<String>()
        for (asrData in joyNoteAsrDataList) {
            paragraph.add(asrData.getAllSentences(null))
        }
        if (paragraph.isNotEmpty()) {
            extractParagraph(paragraph, infoModel!!.minutesDetail.minutesId)
        }
    }

    /**
     * 摘取段落
     */
    private fun extractParagraph(list: ArrayList<String>, minutesId: String) {
        val intent = Intent(JOY_NOTE_UPDATE_JOY_SPACE_ACTION)
        intent.putExtra("type", EXTRACT_EVENT_TYPE)
        val toJSON = JSONArray.parseArray(JSON.toJSONString(list))
        intent.putExtra("texts", toJSON.toString())
        intent.putExtra("minutesId", minutesId)
        LocalBroadcastManager.getInstance(requireActivity()).sendBroadcast(intent)
    }

    private fun findVisibleUntranslatedItem(languageModel: JoyNoteLanguageModel?): List<JoyNoteAsrData> {
        val manager = mRvTextList.layoutManager as LinearLayoutManager
        val first = manager.findFirstVisibleItemPosition()
        var last = manager.findLastVisibleItemPosition()
        last = min(last + 3, mAdapter.itemCount - 1)
        val list = mutableListOf<JoyNoteAsrData>()
        if (first != RecyclerView.NO_POSITION && last != RecyclerView.NO_POSITION && last >= first) {
            for (index in first..last) {
                val data = mAdapter.getItemAt(index)
                list.add(data)
            }
        }
        return list.filter {
            val state = it.getTranslateState(languageModel)
            state == JoyNoteAsrData.TRANSLATE_STATE_NONE || state == JoyNoteAsrData.TRANSLATE_STATE_FAILURE
        }
    }

    /**
     * 长按段落弹出气泡弹窗
     * 出现位置：段落顶部、右侧、底部
     */
    private fun showBubblePopup(anchorView: TextView, originalText: String) {
        val popupView = LayoutInflater.from(anchorView.context)
            .inflate(R.layout.pop_bubble_layout, null)

        // 获取控件坐标
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val anchorY = location[1]
        mRvTextList.getLocationOnScreen(location)
        val rvTextListY = location[1]
        popupView.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )

        val screenWidth = Resources.getSystem().displayMetrics.widthPixels
        val parentWidth = screenWidth - 2 * DensityUtil.dp2px(requireContext(), 12f)
        val verticalSpace = DensityUtil.dp2px(requireContext(), 14f) // 垂直间距
        val horizontalSpace = DensityUtil.dp2px(requireContext(), 15f) // 横向间距
        val popupWidth = popupView.measuredWidth - 2 * horizontalSpace
        val horizontalOffset: Int
        val verticalOffset: Int
        val backgroundResId =
            if (anchorView.lineCount > 1 || parentWidth - anchorView.width < popupWidth) {
                horizontalOffset = parentWidth / 2 - popupView.measuredWidth / 2
                if (rvTextListY >= anchorY) {
                    verticalOffset = -verticalSpace * 2
                    R.drawable.detail_up_arrow_bubble_bg
                } else {
                    verticalOffset =
                        -anchorView.height - popupView.measuredHeight + 2 * verticalSpace
                    R.drawable.detail_down_arrow_bubble_bg
                }
            } else {
                horizontalOffset = anchorView.right - horizontalSpace
                verticalOffset = -anchorView.height - 2 * verticalSpace
                R.drawable.detail_left_arrow_bubble_bg
            }

        popupView.apply {
            setBackgroundResource(backgroundResId)
        }
        mPopupWindow = PopupWindow(anchorView.context).apply {
            contentView = popupView
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            isTouchable = true
            isFocusable = true
            isOutsideTouchable = true
            setOnDismissListener {
                anchorView.isSelected = false
            }
        }
        popupView.setOnClickListener {
            mPopupWindow?.dismiss()
            val dialogFragment = BottomDialogFragment.newInstance(originalText)
            dialogFragment.show(childFragmentManager, null)
        }
        mPopupWindow?.showAsDropDown(anchorView, horizontalOffset, verticalOffset)
    }

    companion object {
        const val JOY_NOTE_ASR: Int = 0
        const val JOY_NOTE_RELATED_ME: Int = 1
        const val JOY_NOTE_DELAY_TIME = 300L
        const val JOY_SPACE = "joyspace"
        const val JOY_NOTE_GET_DETAIL_ASR: String = "minutes.asr"
        const val JOY_NOTE_SPEECH_RELATED_TO_ME: String = "minutes.speechRelatedToMe"
        const val JOY_NOTE_UPDATE_JOY_SPACE_ACTION = "action.joynote.update.joyspace"
        const val EXTRACT_EVENT_TYPE = "EVENT_PICK_PARAGRAPH_TEXT"

        // 埋点
        const val MOBILE_EVENT_MINUTES_DETAIL_HOME_TRANSLATE_VIEW_ORIGINAL_TEXT =
            "Mobile_event_Minutes_DetailHome_Translate_Vieworiginaltext"
        const val MOBILE_EVENT_MINUTES_DETAIL_HOME_TRANSLATE_RETRY =
            "Mobile_event_Minutes_DetailHome_Translate_Retry"
        const val MOBILE_EVENT_MINUTES_DETAIL_HOME_TRANSLATE =
            "Mobile_event_Minutes_DetailHome_Translate"
    }
}
