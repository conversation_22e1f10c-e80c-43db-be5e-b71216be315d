package com.jd.oa.joy.note.detail;

import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chenenyu.router.Router;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.JoyNotePermissionDialog;
import com.jd.oa.joy.note.compunent.JoyNoteTitleBar;
import com.jd.oa.joy.note.model.JoyNoteMember;
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ToastUtils;

import java.util.List;

public class JoyNoteDetailPermissionFragment extends BaseFragment {

    private JoyNoteTitleBar mToolBar;
    private TextView mTvContent;
    private String minutesId;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_joy_note_detail_permission, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mToolBar = view.findViewById(R.id.toolbar);
        mToolBar.setTitle("慧记");
        mTvContent = view.findViewById(R.id.tv_content);
        TextView mTvBackHome = view.findViewById(R.id.tv_back_home);
        TextView mTvRequestPermission = view.findViewById(R.id.tv_request_permission);

        if (getArguments() != null) {
            minutesId = getArguments().getString("minutesId");
        }
        mTvRequestPermission.setOnClickListener(v -> {
            if (getActivity() != null) {
                JoyNotePermissionDialog dialog = new JoyNotePermissionDialog(getActivity());
                dialog.setOnClickListener(new JoyNotePermissionDialog.OnClickListener() {
                    @Override
                    public void onClickOk(String inputText, int type) {
                        mTvRequestPermission.setEnabled(false);
                        if (getArguments() != null) {
                            String minutesId = getArguments().getString("minutesId");
                            JoyNoteDetailRepo.getRepo().permissionApply(minutesId, inputText, type, new LoadDataCallback<Object>() {
                                @Override
                                public void onDataLoaded(Object o) {
                                    mTvRequestPermission.setText(getString(R.string.joynote_permission_applied));
                                    mTvRequestPermission.setTextColor(Color.parseColor("#232930"));
                                    ToastUtils.showToast(getString(R.string.joynote_permission_apply_success));
                                }

                                @Override
                                public void onDataNotAvailable(String s, int i) {
                                    mTvRequestPermission.setEnabled(true);
                                    ToastUtils.showToast(getString(R.string.joynote_permission_apply_fail));
                                }
                            });
                        }
                    }

                    @Override
                    public void onClickCancel() {

                    }
                });
                dialog.show();
            }
        });
        mTvBackHome.setOnClickListener(v -> {
            Router.build(DeepLink.JOY_NOTE_MAIN).go(getContext());
            if (getActivity() != null) getActivity().finish();
        });
        getOwners(minutesId);
    }


    private void getOwners(String mid) {
        JoyNoteDetailRepo.getRepo().getOwners(mid, new LoadDataCallback<List<JoyNoteMember>>() {
            @Override
            public void onDataLoaded(List<JoyNoteMember> joyNoteMembers) {
                SpannableStringBuilder ssb = new SpannableStringBuilder(getString(R.string.joynote_permission_owners_tips));
                for (JoyNoteMember member : joyNoteMembers) {
                    SpannableStringBuilder sb = new SpannableStringBuilder("@" + member.realName + ",");
                    sb.setSpan(new ClickableSpan() {
                        @Override
                        public void updateDrawState(@NonNull TextPaint ds) {
//                    super.updateDrawState(ds);
                            ds.setUnderlineText(false);
                            ds.setColor(Color.parseColor("#4C7CFF"));
                            ds.bgColor = Color.TRANSPARENT;
                        }

                        @Override
                        public void onClick(@NonNull View widget) {
                            ImDdService service = AppJoint.service(ImDdService.class);
                            service.showContactDetailInfo(getActivity(), member.ddAppId, member.account);
                        }
                    }, 0, member.realName.length() + 1, 0);
                    ssb.append(sb);
                }
                if (ssb.toString().endsWith(","))
                    ssb.delete(ssb.length() - 1, ssb.length());
                mTvContent.setText(ssb);
                mTvContent.setMovementMethod(LinkMovementMethod.getInstance());
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mTvContent.setText(getString(R.string.joynote_permission_owners_tips2));
            }
        });
    }
}
