package com.jd.oa.joy.note.viewmodel;

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.jd.oa.joy.note.model.JoyNoteAsrData
import com.jd.oa.joy.note.model.JoyNoteAsrData.Sentences
import com.jd.oa.joy.note.model.JoyNoteLanguageModel
import com.jd.oa.joy.note.model.TranslateResult
import com.jd.oa.joy.note.model.TranslatedText
import com.jd.oa.joy.note.repository.JoyNoteTranslateRepo
import com.jd.oa.joy.note.repository.TranslateState
import com.jd.oa.network.ApiResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

class JoyNoteBaseTextViewModel : ViewModel() {

    private val _translatedAsrData = MutableLiveData<List<JoyNoteAsrData>>()
    val translatedAsrData: LiveData<List<JoyNoteAsrData>>
        get() = _translatedAsrData

    private val _translatingAsrData = MutableLiveData<List<JoyNoteAsrData>>()
    val translatingAsrData: LiveData<List<JoyNoteAsrData>>
        get() = _translatingAsrData

    private val repo = JoyNoteTranslateRepo()

    fun translateAsrData(language: JoyNoteLanguageModel?, list: List<JoyNoteAsrData>) {
        if (language == null || language.language == null || list.isEmpty()) return
        viewModelScope.launch {
            list.forEach { asrData ->
                asrData.sentences?.forEach {
                    it.setTranslateState(language, JoyNoteAsrData.TRANSLATE_STATE_TRANSLATING)
                }
            }
            _translatingAsrData.postValue(list)

            translateText(language, list)
                .filterNotNull()
                .catch { e->
                    e.printStackTrace()
                    val data = mutableListOf<JoyNoteAsrData>()
                    visitSentences(list.filter {
                        return@filter it.getTranslateState(language) == JoyNoteAsrData.TRANSLATE_STATE_TRANSLATING
                    }, null, object : ResultVisitor {
                        override fun visitParagraph(paragraph: JoyNoteAsrData) {}

                        override fun visitSentence(sentences: Sentences, text: TranslatedText?) {
                            sentences.setTranslateState(language, JoyNoteAsrData.TRANSLATE_STATE_FAILURE)
                        }

                        override fun visitParagraphEnd(paragraph: JoyNoteAsrData) {
                            data.add(paragraph)
                        }
                    })
                    emit(data)
                }
                .collect { data ->
                    if (data.isEmpty()) return@collect
                    _translatedAsrData.value = data
                }
        }
    }

    private fun translateText(language: JoyNoteLanguageModel, list: List<JoyNoteAsrData>): Flow<List<JoyNoteAsrData>?> {
        return repo.translate(language.language, list)
            .filterNot { it is TranslateState.OnConnected }
            .map { state ->
                when (state) {
                    is TranslateState.OnEvent -> {
                        val translated = parseEvent(language, list, state)
                        return@map translated
                    }
                    is TranslateState.OnError -> {
                        throw state.error
                    }
                    else -> {
                        return@map null
                    }
                }
            }
    }

    private fun parseEvent(
        language: JoyNoteLanguageModel,
        list: List<JoyNoteAsrData>,
        event: TranslateState.OnEvent
    ): List<JoyNoteAsrData> {
        val result = mutableListOf<JoyNoteAsrData>()
        val response = ApiResponse.parse<List<TranslateResult>>(event.data, object : TypeToken<List<TranslateResult>>(){}.type, "results")
        val isSuccessful = response.isSuccessful

        visitSentences(list, response.data, object : ResultVisitor {
            override fun visitParagraph(paragraph: JoyNoteAsrData) {}

            override fun visitSentence(sentences: Sentences, text: TranslatedText?) {
                if (isSuccessful && text != null) {
                    if (sentences.translatedText == null) {
                        sentences.translatedText = hashMapOf()
                    }
                    sentences.translatedText[language.language] = text.transText
                    sentences.setTranslateState(language, JoyNoteAsrData.TRANSLATE_STATE_SUCCESS)
                } else {
                    sentences.setTranslateState(language, JoyNoteAsrData.TRANSLATE_STATE_FAILURE)
                }
            }

            override fun visitParagraphEnd(paragraph: JoyNoteAsrData) {
                if (paragraph.getTranslateState(language) == JoyNoteAsrData.TRANSLATE_STATE_SUCCESS) {
                    result.add(paragraph)
                }
            }
        })

        return result
    }

    private fun visitSentences(
        list: List<JoyNoteAsrData>,
        result: List<TranslateResult>?,
        visitor: ResultVisitor
    ) {
        if (result == null) {
            list.forEach { paragraph ->
                visitor.visitParagraph(paragraph)
                paragraph.sentences?.forEach { sentences ->
                    visitor.visitSentence(sentences, null)
                }
                visitor.visitParagraphEnd(paragraph)
            }
        } else {
            result.forEach { translateResult ->
                val asrData = list.find { joyNoteAsrData -> joyNoteAsrData.paragraphId == translateResult.id }
                asrData?.let { data ->
                    visitor.visitParagraph(data)

                    translateResult.transList?.forEach { text ->
                        val sentences = data.sentences?.find { sentences -> sentences.sentenceId == text.id }
                        sentences?.let {
                            visitor.visitSentence(it, text)
                        }
                    }

                    visitor.visitParagraphEnd(data)
                }
            }
        }
    }
}

interface ResultVisitor {
    fun visitParagraph(paragraph: JoyNoteAsrData)
    fun visitSentence(sentences: Sentences, text: TranslatedText?)
    fun visitParagraphEnd(paragraph: JoyNoteAsrData)
}