package com.jd.oa.joy.note.permission

import android.content.Intent
import android.os.Bundle
import androidx.databinding.ObservableBoolean
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.BaseActivity
import com.jd.oa.ext.binding
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.ActivityAccessScopeBinding
import com.jd.oa.joy.note.model.Org
import com.jd.oa.joy.note.model.ScopeContent
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity.Companion.LINK_SHARE_MODE

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/19
 * @Description: 慧记-访问范围修改页面
 */
class AccessScopeActivity : BaseActivity() {
    private val binding by binding(ActivityAccessScopeBinding::inflate)
    private val mAdapter: ScopeAdapter by lazy { ScopeAdapter(scopeList) {
        setResult(RESULT_OK, Intent().apply { putExtra(SELECTED_SCOPE, it) })
        finish()
    } }
    private val org: Org? by lazy { intent.getParcelableExtra(ORG) }
    private val linkShareMode: String by lazy { intent.getStringExtra(LINK_SHARE_MODE) ?: "" }
    private val scopeList by lazy {
        mutableListOf(
            ScopeContent(
                LinkShareMode.ONLY_COLLABORATORS.type,
                getString(R.string.icon_general_lock),
                getColor(R.color.me_text_link),
                getString(R.string.joynote_authorize_only_collaborators_visible),
                getString(R.string.joynote_authorize_only_collaborators_can_access_via_link),
                ObservableBoolean(linkShareMode == LinkShareMode.ONLY_COLLABORATORS.type)
            ),
            ScopeContent(
                LinkShareMode.BGBU_READ.type,
                getString(R.string.icon_architecture),
                getColor(R.color.color_FFB416),
                getString(R.string.joynote_authorize_bgbu_disclosure),
                getString(
                    R.string.joynote_authorize_all_department_users_can_access_via_link,
                    org?.orgName
                ),
                ObservableBoolean(
                    linkShareMode in listOf(
                        LinkShareMode.BGBU_EDIT.type,
                        LinkShareMode.BGBU_READ.type
                    )
                ),
                org
            ),
            ScopeContent(
                LinkShareMode.ALL_PEOPLE_READ.type,
                getString(R.string.icon_globaljd),
                getColor(R.color.color_FBB731),
                getString(R.string.joynote_authorize_intra_enterprise_disclosure),
                getString(R.string.joynote_authorize_jd_users_can_access_via_link),
                ObservableBoolean(linkShareMode in listOf(
                    LinkShareMode.ALL_PEOPLE_EDIT.type, LinkShareMode.ALL_PEOPLE_READ.type
                ))
            )
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.hide()
        setContentView(binding.root)
        initView()
    }

    private fun initView() {
        binding.recycleView.adapter = mAdapter
        binding.recycleView.layoutManager = LinearLayoutManager(this)
    }

    companion object {
        const val ORG = "org"
        const val SELECTED_SCOPE = "selectedScope"
    }
}