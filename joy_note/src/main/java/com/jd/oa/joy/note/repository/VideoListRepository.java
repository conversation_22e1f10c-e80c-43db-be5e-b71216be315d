package com.jd.oa.joy.note.repository;

import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.CREATE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECEIVE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECENT;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECYCLE_BIN;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteFinishInfo;
import com.jd.oa.joy.note.model.VideoItemModel;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.JoyNoteCreateInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

public class VideoListRepository {
    private static final String JOY_NOTE_GET_VIDEO_LIST_CREATE = "minutes.list.create";
    private static final String JOY_NOTE_GET_VIDEO_LIST_RECEIVE = "minutes.list.received";
    private static final String JOY_NOTE_GET_VIDEO_LIST_MAIN = "minutes.list.frontpage";
    private static final String JOY_NOTE_GET_VIDEO_LIST_RECENT = "minutes.list.recent";
    private static final String JOY_NOTE_CREATE_JOY_NOTE = "clevernote.create";
    private static final String JOY_NOTE_FINISH_JOY_NOTE = "clevernote.complete ";
    private static final String JOY_NOTE_GET_VIDEO_LIST_RECYCLED = "minutes.list.recycled";
    private static final String JOY_NOTE_DELETE_MINUTES = "minutes.list.delete";
    private static final String JOY_NOTE_REMOVE_MINUTES = "minutes.list.remove";
    private static final String JOY_NOTE_DESTROY_MINUTES = "minutes.list.destroy";
    private static final String JOY_NOTE_RESTORE_MINUTES = "minutes.list.restore";
    private static final String CONTENT = "content";
    private static final String PAGES = "pages";

    private static VideoListRepository videoListRepository;

    private final Gson gson = new GsonBuilder()
            .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
            .create();

    public static VideoListRepository getUserRepository() {
        if (videoListRepository == null) {
            videoListRepository = new VideoListRepository();
        }
        return videoListRepository;
    }

    public void delItem(@NonNull String itemId, @NonNull final OperateCallback callback) {
        delOperation(itemId, JOY_NOTE_DELETE_MINUTES, null, callback);
    }

    public void removeItem(@NonNull String itemId, @NonNull String type, @NonNull final OperateCallback callback) {
        delOperation(itemId, JOY_NOTE_REMOVE_MINUTES, type, callback);
    }

    public void destroyItem(@NonNull String itemId, @NonNull final OperateCallback callback) {
        delOperation(itemId, JOY_NOTE_DESTROY_MINUTES, null, callback);
    }

    public void restoreItem(@NonNull String itemId, @NonNull final OperateCallback callback) {
        delOperation(itemId, JOY_NOTE_RESTORE_MINUTES, null, callback);
    }

    private void delOperation(@NonNull String itemId, @NonNull String api, String type, @NonNull final OperateCallback callback) {
        LoadingDialog loadingDialog = new LoadingDialog(AppBase.getTopActivity());
        loadingDialog.show();
        SimpleRequestCallback<String> callback2 = new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                loadingDialog.dismiss();
                if (JOY_NOTE_DELETE_MINUTES.equals(api)) {
                    ToastUtils.showToast(R.string.joynote_delete_fail);
                } else if (JOY_NOTE_RESTORE_MINUTES.equals(api)) {
                    ToastUtils.showToast(R.string.joy_note_tips_restore_msg_fail);
                }
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                loadingDialog.dismiss();
                boolean isSucceed = false;
                try {
                    if (info != null && info.getErrorCode() != null && "0".equals(info.getErrorCode())) {
                        isSucceed = true;
                        callback.finish();
                    }
                    switch (api) {
                        case JOY_NOTE_DELETE_MINUTES:
                            ToastUtils.showToast(isSucceed ? R.string.joynote_delete_success : R.string.joynote_delete_fail);
                            break;
//                        case JOY_NOTE_RESTORE_MINUTES:
//                            //恢复
//                            ToastUtils.showToast(isSucceed ? R.string.joy_note_tips_restore_msg : R.string.joy_note_tips_restore_msg_fail);
//                            break;
                        case JOY_NOTE_DESTROY_MINUTES:
                            if (isSucceed)
                                ToastUtils.showToast(R.string.joynote_delete_complete_success);
                            break;
                        case JOY_NOTE_REMOVE_MINUTES:
                            if (isSucceed) ToastUtils.showToast(R.string.joynote_remove_success);
                            break;
                        default:
                            break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        Map<String, Object> map = new HashMap<>();
        List<String> arrayList = new ArrayList<>();
        arrayList.add(itemId);
        map.put("minutesIds", arrayList);
        if (type != null) {
            map.put("listType", type);
        }
        HttpManager.post(null, map, callback2, api);
    }

    public void createJoyNote(String name, String channel, String bizId, @NonNull final LoadDataCallback<JoyNoteCreateInfo> callback) {
        SimpleRequestCallback<String> callback2 = new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, exception.getExceptionCode());
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    if (info != null && info.result != null) {
                        JSONObject jsonObject = new JSONObject(info.result);
                        JoyNoteCreateInfo createInfo = gson.fromJson(jsonObject.toString(), JoyNoteCreateInfo.class);
                        callback.onDataLoaded(createInfo);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onDataNotAvailable("", -1);
            }
        };
        Map<String, Object> map = new HashMap<>();
        if (bizId != null) {
            map.put("bizId", bizId);
            map.put("bizType", 1);
        }
        if (name != null) {
            map.put("name", name);
        }
        if (channel != null) {
            map.put("channel", channel);
        } else {
            map.put("channel", "joyminutes");
        }
        map.put("deviceId", DeviceUtil.getDeviceUniqueId());
        HttpManager.post(null, map, callback2, JOY_NOTE_CREATE_JOY_NOTE);
    }

    public void finishJoyNote(@NonNull String noteId, String bizId, @NonNull final LoadDataCallback<JoyNoteFinishInfo> callback) {
        SimpleRequestCallback<String> callback2 = new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, exception.getExceptionCode());
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    if (info != null && info.result != null) {
                        JSONObject jsonObject = new JSONObject(info.result);
                        JoyNoteFinishInfo finishInfo = gson.fromJson(jsonObject.toString(), JoyNoteFinishInfo.class);
                        callback.onDataLoaded(finishInfo);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onDataNotAvailable("", -1);
            }
        };
        Map<String, Object> map = new HashMap<>();
        if (bizId != null) {
            map.put("bizId", bizId);
            map.put("bizType", 1);
        }
        map.put("channel", "joyminutes");
        map.put("noteId", noteId);
//        map.put("deviceId" , DeviceUtil.getDeviceUniqueId());
        HttpManager.post(null, map, callback2, JOY_NOTE_FINISH_JOY_NOTE);
    }

    public void getVideoList(String type, JSONObject param, final LoadDataCallback<List<VideoItemModel>> callback) {
        SimpleReqCallbackAdapter<JSONObject> callback2 = new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                try {
                    String pages = null;
                    if (rawData != null) {
                        JSONObject jsonObject = new JSONObject(rawData);
                        JSONObject content = jsonObject.getJSONObject(CONTENT);
                        JSONArray jsonArray = content.getJSONArray(PAGES);
                        pages = jsonArray.toString();
                    }
                    if (pages != null) {
                        List<VideoItemModel> videoList = gson.fromJson(pages, new TypeToken<List<VideoItemModel>>() {
                        }.getType());
                        callback.onDataLoaded(videoList);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onDataNotAvailable("", -1);
            }
        });
        Map<String, Object> map = new HashMap<>();
        if (param != null) {
            map = gson.fromJson(param.toString(), new TypeToken<Map<String, Object>>() {
            }.getType());
        }
        String api = JOY_NOTE_GET_VIDEO_LIST_MAIN;
        if (CREATE.equals(type)) {
            api = JOY_NOTE_GET_VIDEO_LIST_CREATE;
        } else if (RECEIVE.equals(type)) {
            api = JOY_NOTE_GET_VIDEO_LIST_RECEIVE;
        } else if (RECENT.equals(type)) {
            api = JOY_NOTE_GET_VIDEO_LIST_RECENT;
        } else if (RECYCLE_BIN.equals(type)) {
            api = JOY_NOTE_GET_VIDEO_LIST_RECYCLED;
        }
        HttpManager.post(null, map, callback2, api);
    }
}