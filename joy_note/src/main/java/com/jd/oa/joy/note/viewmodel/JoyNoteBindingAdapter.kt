package com.jd.oa.joy.note.viewmodel

import android.graphics.Color
import android.graphics.drawable.Drawable
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide
import com.jd.oa.joy.note.compunent.SpeakerParagraph
import com.jd.oa.joy.note.compunent.SpeakerTimelineView
import com.jd.oa.joy.note.model.Scope
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * @Author: hepiao3
 * @CreateTime: 2025/1/16
 * @Description:
 */
object JoyNoteBindingAdapter {
    /**
     * 填充 SpeakerTimeLineView 数据（totalDuration、paragraph）
     */
    @JvmStatic
    @BindingAdapter("totalDuration", "paragraph", "timeColor")
    fun bindingTimeLineView(
        timelineView: SpeakerTimelineView,
        totalDuration: Long,
        paragraph: List<Scope>,
        timeColor: String
    ) {
        kotlin.runCatching {
            timelineView.apply {
                this.totalDuration = totalDuration
                paragraphs = paragraph
                    .filter { it.startTime != null && it.endTime != null }
                    .map { SpeakerParagraph(it.startTime ?: 0, it.endTime ?: 0) }
                    .toMutableList()
                this.color = Color.parseColor(timeColor)
            }
        }
    }
}