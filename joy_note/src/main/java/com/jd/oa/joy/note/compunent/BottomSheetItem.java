package com.jd.oa.joy.note.compunent;

public class BottomSheetItem {

    String title;
    Integer icon;
    Object data;
    boolean selected;

    public BottomSheetItem(String title, Integer icon) {
        this.title = title;
        this.icon = icon;
    }

    public BottomSheetItem(String title, Integer icon, Object data) {
        this.title = title;
        this.icon = icon;
        this.data = data;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getIcon() {
        return icon;
    }

    public void setIcon(int icon) {
        this.icon = icon;
    }

    public void setIcon(Integer icon) {
        this.icon = icon;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
