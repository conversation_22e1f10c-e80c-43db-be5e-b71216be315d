package com.jd.oa.joy.note.detail

/**
 * @Author: hepiao3
 * @CreateTime: 2025/3/5
 * @Description:
 */
class JoyNoteDetailTextFragment(
    override val viewType: Int = JOY_NOTE_ASR,
    override val api: String = JOY_NOTE_GET_DETAIL_ASR
) : JoyNoteBaseSegmentFragment() {

    override fun initViewModel() {
        super.initViewModel()
        mViewModel.paragraphLiveData.observe(viewLifecycleOwner) { id ->
            val targetIndex = sentencePositionList.indexOfFirst { it.paragraphId == id }
            handleScrollAction(targetIndex, true)
            prohibitAutoScroll(true)
        }
    }
}