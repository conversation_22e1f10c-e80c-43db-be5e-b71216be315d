package com.jd.oa.joy.note.translate

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.method.ScrollingMovementMethod
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import androidx.core.view.isVisible
import com.jd.oa.AppBase
import com.jd.oa.asr.websocket.model.EventName
import com.jd.oa.deeplink.DeepLinkTools
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.FloatRealTranslateCardBinding
import com.jd.oa.joy.note.notefloat.NoteFloatManager
import com.jd.oa.joy.note.record.AudioRecordCurrent
import com.jd.oa.joy.note.record.AudioRecordStatus
import com.jd.oa.router.DeepLink
import androidx.core.graphics.toColorInt

/**
 * @Author: hepiao3
 * @CreateTime: 2025/5/14
 * @Description:
 */
class FloatingTranslateView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), View.OnClickListener {

    private var windowManager: WindowManager? = null
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var binding: FloatRealTranslateCardBinding

    private var screenWidth = 0
    private var screenHeight = 0
    private var marginHorizontal = 0
    private var marginVertical = 0
    private var lastX = 0f
    private var lastY = 0f
    private var downX = 0f
    private var downY = 0f
    private var isShowing = false
    private var lastUpdateTime = 0L
    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop

    private var mStableText: String = ""
    private var mDynamicText: String = ""

    init {
        initView(context)
        initConfiguration()
    }

    fun show(windowManager: WindowManager) {
        if (!isShowing) {
            this.windowManager = windowManager
            windowManager.addView(this, params)
            isShowing = true
            resetViewSize()
            stop()
        }
    }

    fun start() {
        if (AudioRecordCurrent.currentStatus == AudioRecordStatus.RECORDING) {
            binding.warnTipContainer.isVisible = false
            binding.tipContent.text = ""
        }
    }

    fun stop() {
        // 主动暂停异常提醒，优先级小于其它任何异常类型
        if (AudioRecordCurrent.currentStatus == AudioRecordStatus.PAUSE
            && !binding.warnTipContainer.isVisible) {
            notifyException(
                EventName.UPSIDE_STOPPED_RECORD,
                context.getString(R.string.joynote_real_translate_pause_tip)
            )
        }
    }

    fun hide() {
        if (isShowing) {
            windowManager?.removeView(this)
            windowManager = null
            isShowing = false
            binding.warnTipContainer.isVisible = false
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initView(context: Context) {
        binding = FloatRealTranslateCardBinding.inflate(LayoutInflater.from(context), this, true)

        binding.floatContainer.setOnClickListener(this)
        binding.closeFloatView.setOnClickListener(this)
        binding.enlargeFloatView.setOnClickListener(this)

        binding.tvTranslate.movementMethod = ScrollingMovementMethod()
        binding.scrollView.setOnTouchListener { _, event ->
            binding.floatContainer.onTouchEvent(event)
            false
        }
    }

    private fun initConfiguration() {
        val metrics = resources.displayMetrics
        screenWidth = metrics.widthPixels
        screenHeight = metrics.heightPixels
        // 初始化左右水平间距 12
        marginHorizontal = (12 * metrics.density).toInt()
        // 初始化底部间距 35
        marginVertical = (35 * metrics.density).toInt()
        // 设置窗口宽度（屏幕宽度 - 左右边距）
        val windowWidth = screenWidth - 2 * marginHorizontal
        // FLAG_LAYOUT_NO_LIMITS 允许浮窗移动超出屏幕
        params = WindowManager.LayoutParams(
            windowWidth,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_SYSTEM_ALERT,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.BOTTOM or Gravity.START
            x = (screenWidth - windowWidth) / 2
            y = marginVertical
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            downX = ev.rawX
            downY = ev.rawY
            lastX = ev.rawX
            lastY = ev.rawY
            return false
        } else if (ev.action == MotionEvent.ACTION_MOVE) {
            val dx = ev.rawX - downX
            val dy = ev.rawY - downY
            if (dx * dx + dy * dy > touchSlop * touchSlop) {
                return true
            }
        }
        return super.onInterceptTouchEvent(ev)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        return when (event?.action) {
            MotionEvent.ACTION_MOVE -> {
                // 计算相对于上一次事件的偏移量（而非固定初始值）
                val deltaX = event.rawX - lastX
                val deltaY = event.rawY - lastY

                params.x += deltaX.toInt()
                params.y -= deltaY.toInt()
                windowManager?.updateViewLayout(this, params)

                // 动态更新上一次坐标
                lastX = event.rawX
                lastY = event.rawY
                true
            }

            MotionEvent.ACTION_UP -> {
                centerHorizontallyWithAnimation()
                true
            }

            else -> false
        }
    }

    /**
     * 滑动浮窗后，如果浮窗部分横向超出屏幕会回到横向居中位置；如果部分内容超出顶部/底部则回到距离顶部/底部35像素位置
     */
    fun centerHorizontallyWithAnimation() {
        resetViewSize()

        val targetX = (screenWidth - width) / 2
        val targetY: Int = if (params.y < 0) {
            marginVertical
        } else if (params.y + height > screenHeight) {
            screenHeight - marginVertical - height
        } else {
            params.y
        }

        ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 300
            interpolator = AccelerateDecelerateInterpolator()

            val startX = params.x.toFloat()
            val startY = params.y.toFloat()

            addUpdateListener { animator ->
                // 获取当前动画进度 (0.0 - 1.0)
                val fraction = animator.animatedValue as Float

                // 计算当前帧的位置
                params.x = (startX + (targetX - startX) * fraction).toInt()
                params.y = (startY + (targetY - startY) * fraction).toInt()

                windowManager?.updateViewLayout(this@FloatingTranslateView, params)
            }

            start()
        }
    }

    private fun resetViewSize() {
        screenWidth = resources.displayMetrics.widthPixels
        screenHeight = resources.displayMetrics.heightPixels
        params.width = screenWidth - 2 * marginHorizontal
        params.x = (screenWidth - params.width) / 2
        windowManager?.updateViewLayout(this@FloatingTranslateView, params)
    }

    fun refreshTranslateList(
        dynamicResult: String,
        stableResult: String
    ) {
        mStableText += stableResult
        mDynamicText = dynamicResult

        val stableText = if (TextUtils.isEmpty(mStableText)) "" else mStableText
        val dynamicText = if (TextUtils.isEmpty(mDynamicText)) "" else mDynamicText
        if (stableText.isNotEmpty() || dynamicText.isNotEmpty()) {
            Log.d(javaClass.simpleName, "dynamicResult: $dynamicResult, stableResult: $stableResult")
            val span = SpannableStringBuilder(stableText)
            span.append(dynamicText)
            span.setSpan(
                ForegroundColorSpan("#6A6A6A".toColorInt()), stableText.length,
                span.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            binding.tvTranslate.text = span
        }

        if (stableResult.isNotEmpty()) {
            binding.scrollView.fullScroll(View.FOCUS_DOWN)
        } else if (dynamicResult.isNotEmpty()) {
            if (System.currentTimeMillis() - lastUpdateTime > 2000) {
                lastUpdateTime = System.currentTimeMillis()
                binding.scrollView.fullScroll(View.FOCUS_DOWN)
            }
        }
    }

    fun notifyException(errorType: String?, errorMsg: String?) {
        binding.warnTipContainer.isVisible = !errorMsg.isNullOrEmpty()
        binding.tipContent.text = errorMsg
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        // 确保测量后更新宽度参数
        screenWidth = resources.displayMetrics.widthPixels
        screenHeight = resources.displayMetrics.heightPixels
    }

    override fun onSaveInstanceState(): Parcelable {
        super.onSaveInstanceState()
        return Bundle().apply {
            putInt("LAST_X", params.x)
            putInt("LAST_Y", params.y)
        }
    }

    override fun onRestoreInstanceState(state: Parcelable) {
        (state as? Bundle)?.let {
            params.x = it.getInt("LAST_X")
            params.y = it.getInt("LAST_Y")
            windowManager?.updateViewLayout(this, params)
        }
        super.onRestoreInstanceState(state)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.close_float_view -> {
                val translateIntent =
                    Intent(AppBase.getTopActivity(), FloatingTranslateService::class.java)
                AppBase.getTopActivity()?.stopService(translateIntent)
                NoteFloatManager.getInstance().showFloatingView(AppBase.getTopActivity())
            }

            R.id.enlarge_float_view -> {
                try {
                    DeepLinkTools.getJoyNoteIntent(
                        context,
                        AudioRecordCurrent.title,
                        AudioRecordCurrent.minuteId,
                        DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE
                    )?.apply {
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                        context.startActivity(this)
                    }
                } catch (e: ActivityNotFoundException) {
                    e.printStackTrace()
                }
            }
        }
    }
}