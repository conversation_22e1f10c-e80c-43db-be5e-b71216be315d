package com.jd.oa.joy.note.compunent;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.RequestManager;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.jd.oa.AppBase;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteMember;

import java.util.ArrayList;
import java.util.List;

class AvatarGroupAdapter extends RecyclerView.Adapter<AvatarGroupAdapter.ViewHolder> {

    private List<JoyNoteMember> userList;
    private final int imgWidth, imgHeight, borderWidth, borderColor;
    private final int maxTotal;
    private OnclickListener<JoyNoteMember> mOnItemClickListener;
    private final int TYPE_MAX_TOTAL = 0;
    private final GradientDrawable drawable;

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public ImageView item;

        public ViewHolder(View view) {
            super(view);
            if (view instanceof ImageView) {
                item = (ImageView) view;
            }
        }

    }

    @Override
    public int getItemViewType(int position) {
        if (maxTotal >= 0) {
            if (userList.size() > maxTotal) {
                if (position == maxTotal) {
                    return TYPE_MAX_TOTAL;
                }
            }
        }
        return 1;
    }

    AvatarGroupAdapter(@NonNull List<JoyNoteMember> userList, int imgWidth, int imgHeight, int borderWidth, int borderColor, int maxTotal) {
        this.userList = userList;
        this.imgHeight = imgHeight + borderWidth * 2;
        this.imgWidth = imgWidth + borderWidth * 2;
        this.borderWidth = borderWidth;
        this.borderColor = borderColor;
        this.maxTotal = maxTotal;
        drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setStroke(borderWidth, borderColor);
        drawable.setCornerRadius(imgHeight);
        drawable.setColor(0xFFFBADA3);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (viewType == TYPE_MAX_TOTAL) {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.joy_note_avatar_group_plus, viewGroup, false);
            view.setLayoutParams(new ViewGroup.LayoutParams(imgWidth, imgHeight));
            return new ViewHolder(view);
        }
        Context context = viewGroup.getContext();
        ImageView imageView = new ImageView(context);
        imageView.setLayoutParams(new ViewGroup.LayoutParams(imgWidth, imgHeight));
        return new ViewHolder(imageView);
    }

    @Override
    public void onBindViewHolder(ViewHolder viewHolder, final int position) {
        if (viewHolder.itemView instanceof TextView) {
            TextView textView = (TextView) viewHolder.itemView;
            int maxInt = (userList.size() - maxTotal);
            if (maxInt > 99) {
                maxInt = 99;
            }
            String max = "+" + maxInt;
            float fontSize = 8f / 14 * imgHeight;
            if (maxInt > 9) {
                fontSize = fontSize * 0.8f;
            }
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, fontSize);
            textView.setText(max);
            textView.setBackground(drawable);
            return;
        }
        Context context = AppBase.getAppContext();
        JoyNoteMember user = userList.get(position);
        ImageView imageView = viewHolder.item;
        RequestManager requestManager = Glide.with(context);
        RequestBuilder<Drawable> requestBuilder;
        if (user != null && user.imageUrl != null) {
            requestBuilder = requestManager.load(Uri.parse(user.imageUrl));
        } else {
            requestBuilder = requestManager.load(R.drawable.joynote_avatar_default);
        }
        requestBuilder.error(AppCompatResources.getDrawable(context, R.drawable.joynote_avatar_default))
                .apply(new RequestOptions().centerCrop().diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .transform(new GlideCircleWithBorder(borderWidth, borderColor)))
                .into(imageView);
        if (mOnItemClickListener != null) {
            imageView.setOnClickListener(view -> {
                int p = viewHolder.getBindingAdapterPosition();
                mOnItemClickListener.onViewClick(user, p);
            });
        }
    }

    @Override
    public int getItemCount() {
        if (maxTotal >= 0) {
            if (userList.size() > maxTotal) {
                return maxTotal + 1;
            }
        }
        return userList.size();
    }


    public void setOnItemClickListener(OnclickListener<JoyNoteMember> mOnItemClickListener) {
        this.mOnItemClickListener = mOnItemClickListener;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void refreshList(List<JoyNoteMember> userList) {
        if (userList == null) {
            userList = new ArrayList<>();
        }
        this.userList = userList;
        notifyDataSetChanged();
    }

    public List<JoyNoteMember> getUserList() {
        return userList;
    }
}

