package com.jd.oa.joy.note.permission

import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.BaseActivity
import com.jd.oa.ext.binding
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.ActivitySecuritySettingBinding
import com.jd.oa.joy.note.model.SecurityContent
import com.jd.oa.joy.note.permission.JoyNoteAuthorizeActivity.Companion.DO_SHARE_MODE
import com.jd.oa.joy.note.share.SharePermission

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/19
 * @Description: 安全设置
 */
class SecuritySettingActivity : BaseActivity() {
    private val binding by binding(ActivitySecuritySettingBinding::inflate)
    private val doShareMode: String by lazy { intent.getStringExtra(DO_SHARE_MODE) ?: "" }
    private val adapter by lazy {
        SecuritySettingAdapter(securityContentList) {
            setResult(RESULT_OK, Intent().apply { putExtra(SELECTED_SECURITY_SETTING, it) })
            finish()
        }
    }
    private val securityContentList by lazy {
        mutableListOf(
            SecurityContent(
                getString(R.string.joynote_authorize_read_permission_user),
                SharePermission.READER,
                doShareMode
            ),
            SecurityContent(
                getString(R.string.joynote_authorize_edit_permission_user),
                SharePermission.EDITOR,
                doShareMode
            ),
            SecurityContent(
                getString(R.string.joynote_authorize_who_owner_permission_user),
                SharePermission.CREATOR,
                doShareMode
            ),
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.hide()
        setContentView(binding.root)
        initView()
    }

    private fun initView() {
        binding.recycleView.adapter = adapter
        binding.recycleView.layoutManager = LinearLayoutManager(this)
    }

    companion object {
        const val SELECTED_SECURITY_SETTING = "selected_security_setting"
    }
}