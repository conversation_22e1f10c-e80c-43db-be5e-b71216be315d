package com.jd.oa.joy.note.translate

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import com.jd.oa.JDMAConstants.MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_TRANSLATE
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog
import com.jd.oa.ui.dialog.bottomsheet.BottomAction
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.databinding.ItemTranslateWidgetSwitchBinding
import com.jd.oa.joy.note.model.JoyNoteLanguageModel
import com.jd.oa.joy.note.viewmodel.JoyNoteRecordAudioViewModel
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/28
 * @Description:
 */
class TranslateControlFragment : BaseFragment() {
    private var _binding: ItemTranslateWidgetSwitchBinding? = null
    private val binding get() = _binding!!

    private val viewModel: JoyNoteRecordAudioViewModel by activityViewModels {
        JoyNoteRecordAudioViewModel.Factory()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ItemTranslateWidgetSwitchBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViewModel()
        binding.apply {
            toLanguage.setOnClickListener { translateFor() }
            checkContainer.setOnClickListener {
                checkOnlyTranslate.isSelected = !checkOnlyTranslate.isSelected
                viewModel.updateOnlyDisplayTranslateContent(checkOnlyTranslate.isSelected)
            }
        }
    }

    private fun initViewModel() {
        viewModel.realTimeTranslate.observe(viewLifecycleOwner) { language ->
            binding.tvTargetLanguage.text = language?.third
            binding.checkOnlyTranslate.isSelected =
                language?.first?.realTimeTranslateShowType == TranslateShowType.ONLY_TRANSLATE.value
        }
    }

    /**
     * 修改目标翻译语言
     */
    private fun translateFor() {
        viewModel.realTimeTranslate.value?.second?.map {
            BottomAction(
                title = it.display,
                isSelect = viewModel.realTimeTranslate.value?.first?.realTimeTranslateTargetLang == it.language,
                data = it
            )
        }?.let { actions ->
            val builder =
                JoyBottomSheetDialog.Builder<JoyNoteLanguageModel>(requireContext())
                    .apply {
                        setTitle(getString(R.string.joynote_real_translate_to))
                        setActions(actions)
                        setDescription(getString(R.string.joynote_translate_translated_by_yanxi))
                        setCheckStyle(R.style.BottomSheetDialogCheck_Tick)
                        setOnItemClickListener { action, _ ->
                            clickEvent(MOBILE_EVENT_MINUTES_REAL_TIME_TRANSLATION_TRANSLATE)
                            viewModel.setTranslateLanguage(action.data)
                        }
                    }
            JoyBottomSheetDialog(requireContext(), builder).show()
        } ?: run {
            ToastUtils.showToast(context, R.string.joynote_real_translate_languages_list_exception)
        }
    }
}