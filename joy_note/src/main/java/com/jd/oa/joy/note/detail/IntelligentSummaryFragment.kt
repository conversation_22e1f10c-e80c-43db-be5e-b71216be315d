package com.jd.oa.joy.note.detail

import android.os.Bundle
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.eventbus.JmEventDispatcher.dispatchEvent
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.joy.note.viewmodel.JoyNoteDetailViewModel
import org.json.JSONObject

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/3
 * @Description:
 */
class IntelligentSummaryFragment : WebFragment2() {
    private val mDetailViewModel: JoyNoteDetailViewModel by lazy {
        ViewModelProvider(requireActivity())[JoyNoteDetailViewModel::class.java]
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mDetailViewModel.selectedLanguage.observe(viewLifecycleOwner) {
            val language = it?.language
            val minutesId = arguments?.getString(JOY_NOTE_MINUTES_ID)
            val data = JSONObject().apply {
                put(JOY_NOTE_MINUTES_ID, minutesId)
                put(LANGUAGE, language)
            }
            val result = JSONObject()
            result.put("data", data)
            dispatchEvent<JSONObject, Any>(
                activity,
                JOY_NOTE_LANGUAGE_DID_CHANGED,
                result,
                null
            )
        }
    }

    override fun onPause() {
        super.onPause()
        dispatchEvent<JSONObject, Any>(
            activity,
            JOY_NOTE_LANGUAGE_ORIGINAL_LANGUAGE_CLOSE,
            null,
            null
        )
    }

    companion object {
        private const val LANGUAGE = "language"
        const val JOY_NOTE_MINUTES_ID = "minutesId"
        // 慧记-智能总结-原生通知H5多语言翻译
        private const val JOY_NOTE_LANGUAGE_DID_CHANGED: String =
            "NATIVE_EVENT_JOYNOTE_LANGUAGE_DID_CHANGED"
        // 慧记-智能总结-通知 H5 收起原文弹窗
        private const val JOY_NOTE_LANGUAGE_ORIGINAL_LANGUAGE_CLOSE: String =
            "NATIVE_EVENT_JOYNOTE_LANGUAGE_ORIGINAL_LANGUAGE_CLOSE"
    }
}