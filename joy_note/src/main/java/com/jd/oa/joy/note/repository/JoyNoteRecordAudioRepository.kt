package com.jd.oa.joy.note.repository

import com.google.gson.reflect.TypeToken
import com.jd.oa.joy.note.model.JoyNoteLanguageModel
import com.jd.oa.joy.note.model.RealTimeTranslate
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.network.colorPost
import com.jd.oa.utils.JsonUtils
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject
import kotlin.coroutines.resumeWithException

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/25
 * @Description:
 */
class JoyNoteRecordAudioRepository {
    /**
     * 获取当前录制的慧记状态
     */
    suspend fun getRecordInfo(minutesId: String) = suspendCancellableCoroutine<String> {
        //慧记4 获取慧记状态
        JoyNoteDetailRepo.getRepo().recordInfo(minutesId,
            object : LoadDataCallback<Any> {
                override fun onDataLoaded(p0: Any?) {
                    it.resumeWith(Result.success(p0.toString()))
                }

                override fun onDataNotAvailable(p0: String?, p1: Int) {
                    it.resumeWithException(Exception())
                }

            }
        )
    }

    /**
     * 获取已录制内容
     */
    suspend fun getRecordContent(minutesId: String) = suspendCancellableCoroutine<String> {
        JoyNoteDetailRepo.getRepo().getRecordContent(minutesId, object : LoadDataCallback<Any> {
            override fun onDataLoaded(p0: Any?) {
                it.resumeWith(Result.success(p0.toString()))
            }

            override fun onDataNotAvailable(p0: String?, p1: Int) {
                it.resumeWithException(Exception())
            }
        })
    }

    /**
     * 重命名标题
     */
    suspend fun updateTitle(minutesId: String, inputText: String) =
        suspendCancellableCoroutine<Boolean> {
            JoyNoteDetailRepo.getRepo()
                .updateTitle(minutesId, inputText, object : LoadDataCallback<Any> {
                    override fun onDataLoaded(p0: Any?) {
                        it.resumeWith(Result.success(true))
                    }

                    override fun onDataNotAvailable(p0: String?, p1: Int) {
                        it.resumeWithException(Exception())
                    }
                })
        }

    /**
     * 修改翻译目标语言
     */
    suspend fun setTranslateLanguage(realTimeTranslate: RealTimeTranslate): Boolean {
        val map = hashMapOf("userSettings" to realTimeTranslate)
        return runCatching {
            colorPost(map, SET_TRANSLATE_LANGUAGE).isSuccessful
        }.getOrElse { false }
    }

    /**
     * 获取目标翻译语言
     */
    suspend fun getTranslateLanguage(): RealTimeTranslate? {
        return colorPost(functionId = GET_TRANSLATE_LANGUAGE).result?.let {
            val data = JSONObject(it)
            val content = JSONObject(data.optString("content"))
            if (data.optString("errorCode") != "0") {
                throw Exception("errorCode not equal to 0")
            }
            JsonUtils.getGson().fromJson(
                content.optString("userSettings"),
                object : TypeToken<RealTimeTranslate>() {}.type
            )
        }
    }

    /**
     * 拉取语言列表
     */
    suspend fun getLanguages() = suspendCancellableCoroutine<MutableList<JoyNoteLanguageModel>?> {
        JoyNoteDetailRepo.getRepo()
            .getTranslateLanguages(object : LoadDataCallback<MutableList<JoyNoteLanguageModel>> {
                override fun onDataLoaded(data: MutableList<JoyNoteLanguageModel>?) {
                    it.resumeWith(Result.success(data))
                }

                override fun onDataNotAvailable(error: String?, code: Int) {
                    it.resumeWithException(Exception(error))
                }
            }, GET_LANGUAGES_LIST)
    }

    companion object {
        // 设置翻译语言
        private const val SET_TRANSLATE_LANGUAGE = "minutes.user.setting"

        // 获取翻译语言
        private const val GET_TRANSLATE_LANGUAGE = "minutes.user.getSetting"

        // 获取语言列表
        private const val GET_LANGUAGES_LIST = "minutes.languages.realTime"
    }
}