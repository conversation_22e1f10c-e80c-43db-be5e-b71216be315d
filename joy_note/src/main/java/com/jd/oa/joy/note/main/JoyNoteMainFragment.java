package com.jd.oa.joy.note.main;

import static com.jd.oa.Constant.ANDROID_JOY_MINUTES_REALTIME_TRANSLATE_ENABLE;
import static com.jd.oa.joy.note.JoyNoteInterceptor.showJoyNoteCreateDialog;
import static com.jd.oa.joy.note.JoyNoteTools.MINUTES_ID;
import static com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_OFF;
import static com.jd.oa.router.DeepLink.JOY_NOTE_MAIN;
import static com.jd.oa.router.DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE;
import static com.jd.oa.utils.DensityUtil.dp2px;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.widget.ViewPager2;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.tabs.TabLayout;
import com.jd.oa.BaseActivity;
import com.jd.oa.JDMAConstants;
import com.jd.oa.JDMAPages;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.compunent.AppBarStateChangeListener;
import com.jd.oa.joy.note.compunent.JoyNoteTab;
import com.jd.oa.joy.note.compunent.JoyNoteTitleBar;
import com.jd.oa.joy.note.compunent.JoyNoteTitleBarTab;
import com.jd.oa.joy.note.model.VideoListViewModel;
import com.jd.oa.joy.note.record.AudioRecordCurrent;
import com.jd.oa.joy.note.record.AudioRecordStatus;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StatusBarConfig;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

@Route(JOY_NOTE_MAIN)
public class JoyNoteMainFragment extends Fragment {
    static final int FILTER_NONE = -1;
    private static final String IS_TAB = "isTab";
    private boolean appBarFold = false;
    private int fragmentIndex = 0;
    private boolean isTab;
    private FloatingActionButton fab;
    private View realTimeTranslateEnter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            isTab = bundle.getBoolean(IS_TAB);
        }
        Activity activity = getActivity();
        if (activity instanceof BaseActivity) {
            isTab = !(activity instanceof FunctionActivity);
            if (((BaseActivity) activity).getSupportActionBar() != null) {
                ((BaseActivity) activity).getSupportActionBar().hide();
            }
        }
        return inflater.inflate(R.layout.fragment_joy_note_home, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        JoyNoteMainTabAdapter joyNoteMainTabAdapter = new JoyNoteMainTabAdapter(this);
        JoyNoteTitleBar joyNoteTitleBar = view.findViewById(R.id.main_toolbar);
        JoyNoteTitleBarTab joyNoteTitleBarTab = view.findViewById(R.id.main_toolbar_tab);
        LinearLayout collapse = view.findViewById(R.id.collapse_layout);
        CollapsingToolbarLayout collapsingToolbarLayout = view.findViewById(R.id.collapse_tool_layout);
        joyNoteTitleBar.setTitle(getString(R.string.joynote_title));
        realTimeTranslateEnter = view.findViewById(R.id.real_time_translate);
        LoadingDialog loadingDialog = new LoadingDialog(getActivity());
        if (isTab) {
            joyNoteTitleBar.setVisibility(View.GONE);
            joyNoteTitleBarTab.setVisibility(View.VISIBLE);
            int statusBarHeight = QMUIStatusBarHelper.getStatusbarHeight(getContext());
            joyNoteTitleBarTab.getLayoutParams().height = dp2px(view.getContext(), 44) + statusBarHeight;
            joyNoteTitleBarTab.requestLayout();
            collapsingToolbarLayout.setMinimumHeight(dp2px(view.getContext(), 44) + statusBarHeight);
            collapse.setPadding(collapse.getPaddingStart(), collapse.getPaddingTop() + statusBarHeight, collapse.getPaddingEnd(), collapse.getPaddingBottom());
            joyNoteTitleBarTab.initView(getContext());
            joyNoteTitleBarTab.setTitle(getString(R.string.joynote_title));
        } else {
            joyNoteTitleBar.setVisibility(View.VISIBLE);
            joyNoteTitleBarTab.setVisibility(View.GONE);
        }

        VideoListViewModel videoListViewModel = new ViewModelProvider(this).get(VideoListViewModel.class);
        videoListViewModel.getCreateData().observe(getViewLifecycleOwner(), joyNoteCreateInfo -> {
            loadingDialog.dismiss();
            Activity activity = getActivity();
            if (activity == null || joyNoteCreateInfo == null || !"success".equals(joyNoteCreateInfo.getStatus())) {
                return;
            }
            Intent intent = DeepLinkTools.getJoyNoteIntent(
                    activity,
                    joyNoteCreateInfo.getData().getName(),
                    joyNoteCreateInfo.getData().getNoteId(),
                    DeepLink.JOY_NOTE_CREATE);
            if (intent != null) {
                intent.putExtra(MINUTES_ID, joyNoteCreateInfo.getData().getNoteId());
                launch(intent);
            }
        });
        fab = view.findViewById(R.id.main_fab);
        boolean minutesEnable = ABTestManager.getInstance().getConfigByKey("android.minutes.new.enable", "1").equals("1");
        if (minutesEnable) {
            fab.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#FFF63218")));
            fab.setOnClickListener(new AvoidFastClickListener() {
                @Override
                public void onAvoidedClick(View view) {
                    if (getActivity() == null) {
                        return;
                    }
                    PermissionHelper.requestPermissions(getActivity()
                            , getString(R.string.me_request_permission_title_normal)
                            , getString(R.string.me_request_permission_audio_normal)
                            , new RequestPermissionCallback() {
                                @Override
                                public void allGranted() {
                                    AudioRecordStatus status = AudioRecordCurrent.INSTANCE.getCurrentStatus();
                                    if (status == AudioRecordStatus.RECORDING || status == AudioRecordStatus.PAUSE) {
                                        showJoyNoteCreateDialog(getActivity(), R.string.joy_note_new_tip_msg);
                                    } else {
                                        loadingDialog.show();
                                        videoListViewModel.create();
                                    }
                                }

                                @Override
                                public void denied(List<String> deniedList) {

                                }
                            }, Manifest.permission.RECORD_AUDIO,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE);

                }
            });
        } else {
            fab.setVisibility(View.GONE);
        }

        AppBarLayout appBarLayout = view.findViewById(R.id.app_bar);
        appBarLayout.addOnOffsetChangedListener(new AppBarStateChangeListener() {
            @Override
            public void onStateChanged(AppBarLayout appBarLayout, State state) {
                if (state == AppBarStateChangeListener.State.EXPANDED) {
                    appBarFold = false;
                } else if (state == State.COLLAPSED) {
                    appBarFold = true;
                } else {
                    appBarFold = true;
                }
            }
        });
        ViewPager2 viewPager = view.findViewById(R.id.view_pager);
        viewPager.setUserInputEnabled(false);
        viewPager.setOffscreenPageLimit(2);
        viewPager.setAdapter(joyNoteMainTabAdapter);
        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                JDMAUtils.clickEvent(JDMAPages.Mobile_Page_Minute_List,
                        position == 0 ? JDMAConstants.Mobile_Event_Minute_Recentadded
                                : JDMAConstants.Mobile_Event_Minute_Recentlyopen
                        , new HashMap<>());
            }
        });
        view.findViewById(R.id.real_time_record).setOnClickListener(v -> fab.performClick());
        realTimeTranslateEnter.setOnClickListener(v ->
                Router.build(JOY_NOTE_REAL_TIME_TRANSLATE).go(getActivity())
        );
        JoyNoteTab tabLayout = view.findViewById(R.id.tab_layout);
        tabLayout.initView(viewPager, position -> joyNoteMainTabAdapter.getTabTitle(getContext(), position));
        tabLayout.addOnSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                viewPager.setCurrentItem(tab.getPosition(), false);
                fragmentIndex = tab.getPosition();
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
    }

    public boolean isAppBarFold() {
        return appBarFold;
    }

    private void launch(Intent intent) {
        Fragment fragment = getChildFragmentManager().findFragmentByTag("f" + fragmentIndex);
        if (fragment instanceof JoyNoteListFragment) {
            ((JoyNoteListFragment) fragment).launch(intent);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (Objects.equals(ABTestManager.getInstance().getConfigByKey(
                ANDROID_JOY_MINUTES_REALTIME_TRANSLATE_ENABLE, MULTI_TASK_OFF), MULTI_TASK_OFF)) {
            realTimeTranslateEnter.findViewById(R.id.label_stay_tuned).setVisibility(View.VISIBLE);
            realTimeTranslateEnter.setClickable(false);
        } else {
            realTimeTranslateEnter.findViewById(R.id.label_stay_tuned).setVisibility(View.GONE);
            realTimeTranslateEnter.setClickable(true);
        }
        if (!isTab) {
            return;
        }
        updateStatusBarStyle();
        try {
            if (fragmentIndex == 0) {
                Fragment fragment = getChildFragmentManager().findFragmentByTag("f" + fragmentIndex);
                if (fragment instanceof JoyNoteListFragment) {
                    ((JoyNoteListFragment) fragment).refresh();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (!isTab) {
            return;
        }
        Activity a = getActivity();
        if (a != null && StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.setStatusBarLightMode(a);
        }
    }

    private void updateStatusBarStyle() {
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data != null && data.isGlobal()) {
            if (data.isDarkTheme()) {
                ThemeApi.checkAndSetDarkTheme(getActivity());
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
            }
        } else {
            QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
        }
    }
}