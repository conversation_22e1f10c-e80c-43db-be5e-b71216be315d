package com.jd.oa.joy.note.detail

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.compunent.ISpeakerProgress
import com.jd.oa.joy.note.databinding.JoyNoteItemSpeakerTimelineBinding
import com.jd.oa.joy.note.model.SpeakerTimeline
import com.jd.oa.joy.note.viewmodel.JoyNoteDetailSpeakerViewModel
import com.jd.oa.joy.note.viewmodel.JoyNoteMediaPlayerViewModel
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.ImageLoader

/**
 * @Author: hepiao3
 * @CreateTime: 2025/1/16
 * @Description:
 */
class SpeakerTimelineAdapter(
    val context: Context,
    private val speakerViewModel: JoyNoteDetailSpeakerViewModel,
    private val mediaPlayerViewModel: JoyNoteMediaPlayerViewModel,
    private val onItemClickListener: (SpeakerTimeline) -> Unit
) : RecyclerView.Adapter<SpeakerTimelineAdapter.ViewHolder>() {

    val data = mutableListOf<SpeakerTimeline>()

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = JoyNoteItemSpeakerTimelineBinding.inflate(
            LayoutInflater.from(context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = data[position]
        kotlin.runCatching {
            holder.binding.apply {
                timeline = item
                proportion = item.proportion?.times(100)?.toInt() ?: 0
                recordDuration = speakerViewModel.totalSpeakerTimeline.value?.recordDuration ?: 0
                executePendingBindings()
                root.setOnClickListener {
                    onItemClickListener.invoke(data[holder.bindingAdapterPosition])
                }
                val lifecycleOwner = holder.itemView.context as? LifecycleOwner
                lifecycleOwner?.let { it ->
                    // 只在首次绑定时添加观察者
                    if (holder.mediaStateObserver == null) {
                        val observer = Observer<Pair<Boolean?, Int?>> { state ->
                            if (state.first == true) {
                                state.second?.let { progress ->
                                    timelineView.updateProgress(progress.toLong())
                                }
                            }
                            if (state.first == true && item.isSpeaking(state.second?.toLong())) {
                                wavingView.start()
                            } else {
                                wavingView.pause()
                            }
                        }
                        mediaPlayerViewModel.mediaStateLiveData.observe(it, observer)
                        holder.mediaStateObserver = observer
                    }
                }
                timelineView.setProgressListener(object : ISpeakerProgress {
                    override fun progress(currentProgress: Float) {
                        mediaPlayerViewModel.setMediaProgress(
                            currentProgress.toInt(),
                            true,
                            sourceTimeLine = true
                        )
                    }
                })
                if (SpeakerTimeline.TYPE_CUSTOM.equals(item.userType, ignoreCase = true) &&
                    !TextUtils.isEmpty(item.realName)) {
                    tvCustomName.text = item.realName?.firstOrNull().toString()
                    avatar.setImageDrawable(ColorDrawable(context.getColor(R.color.joy_note_bg_custom_avatar)))
                } else {
                    tvCustomName.text = null
                    ImageLoader.load(
                        context,
                        avatar,
                        item.imageUrl,
                        true,
                        R.drawable.default_person_blue_avatar,
                        R.drawable.default_person_blue_avatar
                    )
                }
                avatar.setOnClickListener {
                    val imDdService = AppJoint.service(ImDdService::class.java)
                    if (SpeakerTimeline.TYPE_FORMAL.equals(item.userType, ignoreCase = true) ||
                        SpeakerTimeline.TYPE_VIRTUAL.equals(item.userType, ignoreCase = true)
                    ) {
                        imDdService.showContactDetailInfo(context, item.ddAppId, item.account)
                    }
                }
            }
        }
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        val lifecycleOwner = holder.itemView.context as? LifecycleOwner
        lifecycleOwner?.let {
            holder.mediaStateObserver?.let { observer ->
                mediaPlayerViewModel.mediaStateLiveData.removeObserver(observer)
            }
        }
    }

    fun updateAllData(data: List<SpeakerTimeline>) {
        this.data.clear()
        this.data.addAll(data)
        notifyItemRangeChanged(0, itemCount)
    }

    class ViewHolder(val binding: JoyNoteItemSpeakerTimelineBinding) :
        RecyclerView.ViewHolder(binding.root) {
        var mediaStateObserver: Observer<Pair<Boolean?, Int?>>? = null
    }
}