package com.jd.oa.joy.note

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.os.IBinder
import android.text.TextUtils
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.AppBase
import com.jd.oa.Constant.SHORTCUT_ACTION
import com.jd.oa.asr.AsrService.Companion.CLASS_NAME
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.joy.note.event.DurationEvent
import com.jd.oa.joy.note.event.ExceptionEvent
import com.jd.oa.joy.note.record.BaseAudioActivity
import com.jd.oa.joy.note.record.RecordAction
import com.jd.oa.joy.note.record.RecordAudioActivity
import com.jd.oa.joy.note.record.RecordAudioService.Companion.ACTION_RECORD_AUDIO
import com.jd.oa.joy.note.translate.RealTimeTranslateActivity
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.ActivityLifecycleCallbacksAdapter
import de.greenrobot.event.EventBus

/**
 * @Author: hepiao3
 * @CreateTime: 2025/5/21
 * @Description:
 */
abstract class BaseFloatingService : Service() {
    private val hideActivities: List<Class<out Activity?>> = arrayListOf(
        RecordAudioActivity::class.java, RealTimeTranslateActivity::class.java
    )
    private val hideFragments: List<String> = ArrayList()

    abstract fun showFloatingView()

    abstract fun hideFloatingView()

    abstract fun resetPosition()

    abstract fun startPlay()

    abstract fun stopPlay()

    abstract fun notifyException(errorType: String?, errorMsg: String?)

    open fun notifyDurationChange(duration: Long) {}

    abstract val isGlobalWork: Boolean

    @SuppressLint("WrongConstant")
    override fun onCreate() {
        super.onCreate()
        IntentFilter().apply {
            addAction(AppBase.ACTION_JDME_APP_FOREGROUND)
            addAction(AppBase.ACTION_JDME_APP_BACKGROUND)
            addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
            addAction(Intent.ACTION_CONFIGURATION_CHANGED)
            ContextCompat.registerReceiver(
                this@BaseFloatingService,
                mReceiver,
                this,
                ContextCompat.RECEIVER_NOT_EXPORTED
            )
        }
        IntentFilter().apply {
            addAction(ACTION_RECORD_AUDIO)
            LocalBroadcastManager.getInstance(this@BaseFloatingService)
                .registerReceiver(mRecordingStateReceiver, this)
        }
        EventBus.getDefault().register(this)
        AppBase.getAppContext().registerActivityLifecycleCallbacks(lifecycleCallback)
        EventBus.getDefault().getStickyEvent(ExceptionEvent::class.java)?.let {
            notifyException(it.errorType, it.errorMsg)
        }
        EventBus.getDefault().getStickyEvent(DurationEvent::class.java)?.let {
            notifyDurationChange(it.duration)
        }
    }

    private val mReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (AppBase.getTopActivity() == null) {
                return
            }
            val activity = AppBase.getTopActivity()
            val action = intent?.action

            if (AppBase.ACTION_JDME_APP_FOREGROUND == intent?.action) {
                val user = PreferenceManager.UserInfo.getUserName()
                if (activity is BaseAudioActivity) {
                    return
                }
                if (!TextUtils.isEmpty(user)) {
                    if (!shouldHideIt(activity)) {
                        showFloatingView()
                    }
                }
            } else if (AppBase.ACTION_JDME_APP_BACKGROUND == intent?.action) {
                if (!isGlobalWork) hideFloatingView() else showFloatingView()
            } else if (Intent.ACTION_CLOSE_SYSTEM_DIALOGS == action) {
                val from = intent.getStringExtra(SYSTEM_DIALOG_FROM_KEY)
                if (SYSTEM_DIALOG_FROM_HOME_KEY == from || SYSTEM_DIALOG_FROM_RECENT_APPS == from) {
                    if (!isGlobalWork) hideFloatingView() else showFloatingView()
                }
            } else if (intent?.action == Intent.ACTION_CONFIGURATION_CHANGED) {
                resetPosition()
            }
        }
    }

    private val mRecordingStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.getStringExtra(CLASS_NAME) == <EMAIL>) return
            val act = intent.getStringExtra(SHORTCUT_ACTION) ?: return
            if (RecordAction.PAUSE.name == act || RecordAction.END.name == act) {
                stopPlay()
            } else if (RecordAction.START.name == act || RecordAction.RESUME.name == act) {
                startPlay()
            }
        }
    }

    private val lifecycleCallback = object : ActivityLifecycleCallbacksAdapter() {
        override fun onActivityStarted(activity: Activity) {
            if (activity is BaseAudioActivity) return
            if (shouldHideIt(activity)) hideFloatingView()
            if (activity.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                resetPosition()
            }
        }
    }

    fun shouldHideIt(activity: Activity?): Boolean {
        if (activity == null || hideActivities.contains(activity.javaClass)) return true
        if (activity is FunctionActivity && activity.getIntent() != null) {
            return hideFragments.contains(
                activity.getIntent().getStringExtra(FunctionActivity.FLAG_FUNCTION)
            )
        }
        return false
    }

    fun onEventMainThread(event: ExceptionEvent) {
        Log.i(
            <EMAIL>,
            "errorType: ${event.errorType}, errorMsg: ${event.errorMsg}"
        )
        notifyException(event.errorType, event.errorMsg)
    }

    fun onEventMainThread(event: DurationEvent) {
        notifyDurationChange(event.duration)
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(mReceiver)
        EventBus.getDefault().unregister(this)
        LocalBroadcastManager.getInstance(this@BaseFloatingService).unregisterReceiver(mRecordingStateReceiver)
        AppBase.getAppContext().unregisterActivityLifecycleCallbacks(lifecycleCallback)
    }

    companion object {
        const val SYSTEM_DIALOG_FROM_KEY: String = "reason"
        const val SYSTEM_DIALOG_FROM_RECENT_APPS: String = "recentapps"
        const val SYSTEM_DIALOG_FROM_HOME_KEY: String = "homekey"
    }
}