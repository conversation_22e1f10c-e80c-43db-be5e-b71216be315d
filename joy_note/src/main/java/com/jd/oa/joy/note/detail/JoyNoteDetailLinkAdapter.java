package com.jd.oa.joy.note.detail;

import static com.jd.oa.joy.note.detail.JoyNoteDetailInfoFragment.ATTACHMENT_TYPE_JOY_SPACE;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteDetailInfoModel;

import java.util.List;

public class JoyNoteDetailLinkAdapter extends RecyclerView.Adapter<JoyNoteDetailLinkAdapter.ViewHolder> {

    private List<JoyNoteDetailInfoModel.MinutesDetail.Attachments> mList;

    private Context mContext;


    public JoyNoteDetailLinkAdapter(List<JoyNoteDetailInfoModel.MinutesDetail.Attachments> mList, Context mContext) {
        this.mList = mList;
        this.mContext = mContext;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(View.inflate(mContext, R.layout.joy_note_item_detail_link_list, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        JoyNoteDetailInfoModel.MinutesDetail.Attachments attachments = mList.get(i);
        if (attachments == null) return;
        viewHolder.tvTitle.setText(attachments.fileName);
        viewHolder.itemView.setOnClickListener(v -> {
            if (onLinkClickListener != null) onLinkClickListener.onLinkClick(attachments);
        });
        viewHolder.ivIcon.setImageResource(attachments.fileType.equalsIgnoreCase(ATTACHMENT_TYPE_JOY_SPACE) ? R.drawable.icon_joynote_joyspace : R.drawable.icon_joynote_link);
    }

    @Override
    public int getItemCount() {
        return mList == null ? 0 : mList.size();
    }


    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivIcon;
        TextView tvTitle;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivIcon = itemView.findViewById(R.id.iv_icon);
            tvTitle = itemView.findViewById(R.id.tv_title);
        }
    }

    public interface OnLinkClickListener {
        void onLinkClick(JoyNoteDetailInfoModel.MinutesDetail.Attachments attachments);
    }

    private OnLinkClickListener onLinkClickListener;

    public void setOnLinkClickListener(OnLinkClickListener onLinkClickListener) {
        this.onLinkClickListener = onLinkClickListener;
    }
}
