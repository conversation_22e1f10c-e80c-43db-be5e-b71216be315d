package com.jd.oa.joy.note.main;

import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.CREATE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.FRONT_PAGE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.LIST_TYPE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECEIVE;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECENT;
import static com.jd.oa.joy.note.main.JoyNoteSingleListActivity.RECYCLE_BIN;

import android.content.Context;
import android.os.Bundle;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.jd.oa.joy.note.R;

import java.util.ArrayList;
import java.util.List;

public class JoyNoteMainTabAdapter extends FragmentStateAdapter {
    private final List<Pair<String, Integer>> tabs = new ArrayList<>();

    public JoyNoteMainTabAdapter(Fragment fragment) {
        super(fragment);
        tabs.add(new Pair<>(FRONT_PAGE, R.string.joynote_home_tab1));
        tabs.add(new Pair<>(RECENT, R.string.joynote_home_tab2));
        tabs.add(new Pair<>(CREATE, R.string.joynote_my_create_title));
        tabs.add(new Pair<>(RECEIVE, R.string.joynote_home_received));
        tabs.add(new Pair<>(RECYCLE_BIN, R.string.joynote_home_trash));
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        Fragment fragment = new JoyNoteListFragment();
        Bundle args = new Bundle();
        args.putString(LIST_TYPE, tabs.get(position).first);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getItemCount() {
        return tabs.size();
    }

    public String getTabTitle(Context context, int position) {
        if (position >= 0 && position < tabs.size()) {
            return context.getString(tabs.get(position).second);
        }
        return "";
    }
}