package com.jd.oa.joy.note;

import static com.jd.oa.joy.note.model.VideoItemModel.RECORDING;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.joy.note.model.VideoItemModel;
import com.jd.oa.joy.note.record.AudioRecordCurrent;
import com.jd.oa.joy.note.record.AudioRecordStatus;
import com.jd.oa.joy.note.repository.JoyNoteDetailRepo;
import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.JoyNoteCreateInfo;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class JoyNoteInterceptor implements RouteInterceptor {
    private static final String TAG = "JoyNoteInterceptor";
    private static final String BIZ_JOY_NOTE_CREATE = "/biz/joyNote/create";
    private static final String BIZ_JOY_NOTE_REAL_TRANSLATE = "/biz/realTimeTranslation";

    public static void showJoyNoteCreateDialog(Context context, int msgId) {
        if (context == null) {
            context = AppBase.getTopActivity();
        }
        if (context == null) {
            return;
        }
        Context finalContext = context;
        AlertDialog.Builder builder = new AlertDialog.Builder(context)
                .setTitle(R.string.joy_note_new_tip_title)
                .setMessage(msgId)
                .setNegativeButton(R.string.joy_note_new_tip_negative, (dialog, which) -> dialog.dismiss())
                .setPositiveButton(R.string.joy_note_new_tip_positive, (dialog, which) -> {
                    dialog.dismiss();
                    Integer type = AudioRecordCurrent.INSTANCE.getType();
                    if (type != null) {
                        if (type == VideoItemModel.AUDIO) {
                            DeepLinkTools.goJoyNotePage(
                                    finalContext,
                                    AudioRecordCurrent.INSTANCE.getTitle(),
                                    AudioRecordCurrent.INSTANCE.getMinuteId(),
                                    DeepLink.JOY_NOTE_CREATE
                            );
                        } else if (type == VideoItemModel.AUDIO_TRANSLATE) {
                            DeepLinkTools.getJoyNoteIntent(
                                    finalContext,
                                    AudioRecordCurrent.INSTANCE.getTitle(),
                                    AudioRecordCurrent.INSTANCE.getMinuteId(),
                                    DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE
                            );
                        }
                    }
                });
        AlertDialog dialog = builder.show();
        dialog.getButton(DialogInterface.BUTTON_POSITIVE).setTextColor(0xffFE3B30);
        dialog.getButton(DialogInterface.BUTTON_NEGATIVE).setTextColor(0xff232930);
    }

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (BIZ_JOY_NOTE_CREATE.equals(uri.getPath())) {
                String param = uri.getQueryParameter(DeepLink.DEEPLINK_PARAM);
                if (param == null) {
                    return chain.process();
                }
                JSONObject jsonObject = new JSONObject(param);
                String minutesId = jsonObject.getString("minutesId");
                if (TextUtils.isEmpty(minutesId)) {
                    return chain.process();
                }
                audioPermissionCheck(granted -> {
                    if (granted) {
                        getRecordInfo(minutesId, chain);
                    }
                    return null;
                });
                return chain.intercept();
            } else if (BIZ_JOY_NOTE_REAL_TRANSLATE.equals(uri.getPath())) {
                String param = uri.getQueryParameter(DeepLink.DEEPLINK_PARAM);
                if (param == null) {
                    audioPermissionCheck(granted -> {
                        if (granted) {
                            createJoyNote(chain, uri);
                        }
                        return null;
                    });
                } else {
                    JSONObject jsonObject = new JSONObject(param);
                    String minutesId = jsonObject.optString("minutesId");
                    if (TextUtils.isEmpty(minutesId)) {
                        audioPermissionCheck(granted -> {
                            if (granted) {
                                createJoyNote(chain, uri);
                            }
                            return null;
                        });
                    } else {
                        audioPermissionCheck(granted -> {
                            if (granted) {
                                getRecordInfo(minutesId, chain);
                            }
                            return null;
                        });
                    }
                }
                return chain.intercept();
            }
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
        }
        return chain.process();
    }

    private void getRecordInfo(String minutesId, Chain chain) {
        JoyNoteDetailRepo.getRepo().recordInfo(minutesId, new LoadDataCallback<Object>() {
            @Override
            public void onDataLoaded(Object p0) {
                String toString = p0.toString();
                try {
                    Context context = chain.getContext();
                    Activity activity = AppBase.getTopActivity();
                    if (context instanceof Activity) {
                        activity = (Activity) context;
                    }
                    JSONObject jsonObject = new JSONObject(toString);
                    JSONObject content = jsonObject.getJSONObject("content");
                    int status = content.optInt("status");

                    if (status == VideoItemModel.FINISH || status == VideoItemModel.GENERATION || status == VideoItemModel.RECYCLING) {
                        openDetailActivity(chain, minutesId);
                        return;
                    }
                    // 0无效数据 1正常数据 2生成中的数据 3录制中的数据 4进入回收站的数据 5录制暂停的数据 6录制未开始的数据
                    String minuteId = AudioRecordCurrent.INSTANCE.getMinuteId();
                    if (!minutesId.equals(minuteId)) {
                        if (status == RECORDING) {
                            ToastUtils.showToast(R.string.joy_note_other_device_recording_wait_tip_msg);
                        } else {
                            if (!minuteId.isEmpty()) {
                                showJoyNoteCreateDialog(activity, R.string.joy_note_other_tip_msg);
                            } else {
                                openRecordActivity(chain);
                            }
                        }
                    } else {
                        openRecordActivity(chain);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
            }
        });
    }

    private void createJoyNote(Chain chain, Uri uri) {
        AudioRecordStatus status = AudioRecordCurrent.INSTANCE.getCurrentStatus();
        String minuteId = AudioRecordCurrent.INSTANCE.getMinuteId();
        if (!minuteId.isEmpty() && (status == AudioRecordStatus.RECORDING || status == AudioRecordStatus.PAUSE)) {
            showJoyNoteCreateDialog(chain.getContext(), R.string.joy_note_new_tip_msg);
            return;
        }
        VideoListRepository.getUserRepository().createJoyNote(
                null,
                "joyminutes-realTime-translate",
                null,
                new LoadDataCallback<JoyNoteCreateInfo>() {
                    @Override
                    public void onDataLoaded(JoyNoteCreateInfo createInfo) {
                        if (!"success".equals(createInfo.getStatus())) {
                            return;
                        }
                        try {
                            JSONObject param = new JSONObject();
                            param.put("minutesId", createInfo.getData().getNoteId());
                            param.put("name", createInfo.getData().getName());
                            Uri newUri = uri.buildUpon()
                                    .clearQuery()
                                    .appendQueryParameter(DeepLink.DEEPLINK_PARAM, param.toString())
                                    .build();
                            Router.build(newUri).skipInterceptors().go(chain.getContext());
                        } catch (JSONException ignore) {
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                    }
                });
    }


    public void openRecordActivity(Chain chain) {
        Uri uri = chain.getRequest().getUri();
        Router.build(uri).skipInterceptors().go(chain.getContext());
    }

    public void openDetailActivity(Chain chain, String minutesId) {
        try {
            JSONObject param = new JSONObject();
            param.put("minutesId", minutesId);
            Uri uri = Uri.parse(DeepLink.JOY_NOTE_DETAIL).buildUpon()
                    .appendQueryParameter(DeepLink.DEEPLINK_PARAM, param.toString())
                    .build();
            Router.build(uri).skipInterceptors().go(chain.getContext());
        } catch (JSONException ignore) {
        }
    }

    private void audioPermissionCheck(Function1<Boolean, Unit> callback) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            callback.invoke(false);
            return;
        }
        PermissionHelper.requestPermissions(activity
                , activity.getString(R.string.me_request_permission_title_normal)
                , activity.getString(R.string.me_request_permission_audio_normal)
                , new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        callback.invoke(true);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        callback.invoke(false);
                    }
                }, Manifest.permission.RECORD_AUDIO,
                Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }
}

