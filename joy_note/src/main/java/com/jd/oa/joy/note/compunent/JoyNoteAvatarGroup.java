package com.jd.oa.joy.note.compunent;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteMember;

import java.util.ArrayList;
import java.util.List;

/**
 * @noinspection unused
 */
public class JoyNoteAvatarGroup extends ConstraintLayout {
    private int imgWidth = 14, imgHeight = 14, borderWidth = 1, offset = 0, borderColor = Color.WHITE;

    private AvatarGroupAdapter avatarGroupAdapter;

    private int maxTotal = 10;

    public JoyNoteAvatarGroup(@NonNull Context context) {
        super(context);
    }

    public JoyNoteAvatarGroup(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initCustomAttrs(context, attrs);
    }

    public JoyNoteAvatarGroup(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initCustomAttrs(context, attrs);
    }

    public JoyNoteAvatarGroup(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initCustomAttrs(context, attrs);
    }


    public void init(List<JoyNoteMember> userList) {
        LayoutInflater.from(getContext()).inflate(R.layout.joy_note_avatar_group, this, true);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        avatarGroupAdapter = new AvatarGroupAdapter(userList, imgWidth, imgHeight, borderWidth, borderColor, maxTotal);

        RecyclerView recyclerView = findViewById(R.id.avatar_group);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent.getChildLayoutPosition(view) != 0) {
                    outRect.left = offset;
                }
            }
        });
        avatarGroupAdapter.setOnItemClickListener((v, position) -> {
            if (onclickListener != null) {
                onclickListener.onViewClick(v, position);
            }
        });
        recyclerView.setAdapter(avatarGroupAdapter);
        if (needAutoMax) {
            recyclerView.post(() -> {
                int lastCompletelyVisibleItemPosition = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                if (lastCompletelyVisibleItemPosition >= 0) {
                    int max = autoMax;
                    if (lastCompletelyVisibleItemPosition + 1 < autoMax) {
                        max = lastCompletelyVisibleItemPosition ;
                    }
                    avatarGroupAdapter = new AvatarGroupAdapter(userList, imgWidth, imgHeight, borderWidth, borderColor, max);
                    avatarGroupAdapter.setOnItemClickListener((v, position) -> {
                        if (onclickListener != null) {
                            onclickListener.onViewClick(v, position);
                        }
                    });
                    recyclerView.setAdapter(avatarGroupAdapter);
                }
            });
        }
    }

    private void initCustomAttrs(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.JoyNoteAvatarGroup);
        try {
            imgWidth = (int) ta.getDimension(R.styleable.JoyNoteAvatarGroup_avatarWidth, imgWidth);
            imgHeight = (int) ta.getDimension(R.styleable.JoyNoteAvatarGroup_avatarHeight, imgHeight);
            borderWidth = (int) ta.getDimension(R.styleable.JoyNoteAvatarGroup_borderWidth, borderWidth);
            offset = (int) ta.getDimension(R.styleable.JoyNoteAvatarGroup_offset, offset);
            borderColor = ta.getColor(R.styleable.JoyNoteAvatarGroup_borderColor, borderColor);
            maxTotal = ta.getInteger(R.styleable.JoyNoteAvatarGroup_maxTotal, maxTotal);
        } finally {
            ta.recycle();
        }
    }

    private OnclickListener<JoyNoteMember> onclickListener;

    public void setOnclickListener(OnclickListener<JoyNoteMember> onclickListener) {
        this.onclickListener = onclickListener;
    }

    public void refresh(List<JoyNoteMember> userList) {
        if (avatarGroupAdapter != null) {
            avatarGroupAdapter.refreshList(userList);
        }
    }

    @NonNull
    public List<JoyNoteMember> getUserList() {
        if (avatarGroupAdapter != null) {
            return avatarGroupAdapter.getUserList();
        }
        return new ArrayList<>();
    }

    private boolean needAutoMax = false;
    private int autoMax = 10;

    public void setAutoMaxTotal(boolean need, int autoMax) {
        needAutoMax = true;
        this.autoMax = autoMax;
    }
}
