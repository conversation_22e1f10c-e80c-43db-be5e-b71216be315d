package com.jd.oa.joy.note.compunent

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.animation.AccelerateInterpolator
import androidx.core.graphics.ColorUtils
import com.jd.oa.joy.note.R
import kotlin.math.min
import kotlin.properties.Delegates

class ShapeWavingView(context: Context?,
                      attrs: AttributeSet?,
                      defStyleAttr: Int): ViewGroup(context, attrs, defStyleAttr) {

    companion object {
        const val DEFAULT_LINE_WIDTH = 2
        const val DEFAULT_WAVE_LENGTH = 24
        const val DEFAULT_WAVE_LINE_NUM = 2
        const val DEFAULT_WAVE_INTERVAL = 360

        const val ANIMATION_DURATION = 1200L

        const val SHAPE_CIRCLE = 0
        const val SHAPE_SUPER_ELLIPSE = 1

        const val STATE_PAUSE = 0;
        const val STATE_RUNNING = 1;
    }

    var color: Int by  Delegates.notNull<Int>()
    private var mWaveLineWidth by Delegates.notNull<Float>()
    private var mWaveDistance by Delegates.notNull<Float>()
    private var mShape by Delegates.notNull<Int>()

    private var mCirclePaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    private var mWaveStartRadius: Float? = null
    private var mWaveEndRadius: Float? = null

    private var mLineNum: Int by Delegates.notNull<Int>()
    private var mLineInterval: Int by Delegates.notNull<Int>()

    private var mAnimatorSet: AnimatorSet? = null
    private var mAnimatedValues: FloatArray by Delegates.notNull<FloatArray>()

    private var mState = STATE_PAUSE
    private var mInitState = STATE_PAUSE

    public val state
        get() = mState

    private val mSuperEllipsizedDrawer: SuperEllipsizedPathDrawer by lazy {
        SuperEllipsizedPathDrawer()
    }

    private val mPath: Path by lazy {
        Path()
    }

    constructor(context: Context): this(context, null, 0)
    constructor(context: Context, attrs: AttributeSet?): this(context, attrs, 0)

    init {
        val typedArray = context?.obtainStyledAttributes(attrs, R.styleable.ShapeWavingView)
        mWaveLineWidth = typedArray?.getDimensionPixelOffset(R.styleable.ShapeWavingView_shapeWaveLineWidth, DEFAULT_LINE_WIDTH)?.toFloat() ?: DEFAULT_LINE_WIDTH.toFloat()
        mWaveDistance = typedArray?.getDimensionPixelOffset(R.styleable.ShapeWavingView_shapeWaveLength, DEFAULT_WAVE_LENGTH)?.toFloat() ?: DEFAULT_WAVE_LENGTH.toFloat()
        mShape = typedArray?.getInt(R.styleable.ShapeWavingView_shapeWaveShape, SHAPE_CIRCLE) ?: SHAPE_CIRCLE
        color = typedArray?.getColor(R.styleable.ShapeWavingView_shapeWaveColor, Color.RED) ?: Color.RED
        mLineNum = typedArray?.getInt(R.styleable.ShapeWavingView_shapeWaveLineNum, DEFAULT_WAVE_LINE_NUM) ?: DEFAULT_WAVE_LINE_NUM
        mLineInterval = typedArray?.getInt(R.styleable.ShapeWavingView_shapeWaveLineInterval, DEFAULT_WAVE_INTERVAL) ?: DEFAULT_WAVE_INTERVAL
        mInitState = typedArray?.getInt(R.styleable.ShapeWavingView_shapeWaveInitState, STATE_PAUSE) ?: STATE_PAUSE

        mCirclePaint.color = color
        mCirclePaint.strokeWidth = mWaveLineWidth

        mAnimatedValues = FloatArray(mLineNum)

        typedArray?.recycle()

        setWillNotDraw(false)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)

        val firstChild = getChildAt(0)
        if (firstChild == null) {
            setMeasuredDimension(0, 0)
            return
        }

        //measureChild(firstChild, widthMeasureSpec, heightMeasureSpec)
        measureChildWithMargins(firstChild, widthMeasureSpec, 0, heightMeasureSpec, 0)

        mWaveStartRadius = min(firstChild.measuredWidth, firstChild.measuredHeight) / 2f
        mWaveEndRadius = mWaveStartRadius!! + mWaveDistance

        val width = when(widthMode) {
            MeasureSpec.EXACTLY -> widthSize
            MeasureSpec.AT_MOST -> {
                min(firstChild.measuredWidth + (mWaveDistance + mWaveLineWidth) * 2, widthSize.toFloat())
            }
            else -> firstChild.measuredWidth + (mWaveDistance + mWaveLineWidth) * 2
        }
        val height = when(heightMode) {
            MeasureSpec.EXACTLY -> heightSize
            MeasureSpec.AT_MOST -> {
                min(firstChild.measuredHeight + (mWaveDistance + mWaveLineWidth) * 2, heightSize.toFloat())
            }

            else -> firstChild.measuredHeight + (mWaveDistance + mWaveLineWidth) * 2
        }
        setMeasuredDimension(width.toInt(), height.toInt())
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        val firstChild = getChildAt(0)
        val childLeft = (width - firstChild.measuredWidth) / 2
        val childTop = (height - firstChild.measuredHeight) / 2
        firstChild.layout(childLeft, childTop, childLeft + firstChild.measuredWidth, childTop + firstChild.measuredHeight)

        if (changed) {
            val width = measuredWidth
            val height = measuredHeight
            mSuperEllipsizedDrawer.setSize(width.toFloat(), height.toFloat())
            mSuperEllipsizedDrawer.drawPath(mPath)
        }
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        if (!isAttachedToWindow) return
        if (mState == STATE_RUNNING) {
            val firstChild = getChildAt(0)
            mAnimatedValues.forEachIndexed { index, mAnimatedValue ->
                mCirclePaint.color = ColorUtils.setAlphaComponent(color, (0xFF * (1 - mAnimatedValue)).toInt())

                if (mShape == SHAPE_CIRCLE) {
                    if (mWaveStartRadius != null) {
                        val currentRadius = mWaveStartRadius!! + (mWaveEndRadius!! - mWaveStartRadius!!) * mAnimatedValue
                        canvas?.drawCircle(width / 2f, height / 2f, currentRadius, mCirclePaint)
                    }
                } else if (mShape == SHAPE_SUPER_ELLIPSE) {
                    if (mWaveStartRadius != null) {
                        val mCurrentWidth = firstChild.measuredWidth + mWaveDistance * 2 * mAnimatedValue
                        val mCurrentHeight = firstChild.measuredHeight + mWaveDistance * 2 * mAnimatedValue
                        mSuperEllipsizedDrawer.setSize(mCurrentWidth, mCurrentHeight)
                        mSuperEllipsizedDrawer.drawPath(mPath)
                        canvas?.save()
                        val distance = mWaveDistance
                        canvas?.translate(distance * (1 - mAnimatedValue) + mWaveLineWidth, distance * (1 - mAnimatedValue) + mWaveLineWidth)
                        canvas?.drawPath(mPath, mCirclePaint)
                        canvas?.restore()
                    }
                }
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (mInitState == STATE_RUNNING) {
            start()
        }
    }

    fun pause() {
        if (mState == STATE_PAUSE) return

        mAnimatorSet?.pause()
        mState = STATE_PAUSE
        invalidate()
    }

    fun start() {
        if (mState == STATE_RUNNING) return

        if (mAnimatorSet == null) {
            mAnimatorSet = createAnimator()
            mAnimatorSet?.start()
        } else if (mAnimatorSet?.isPaused == true) {
            mAnimatorSet?.resume()
        } else {
            mAnimatorSet?.cancel()
            mAnimatorSet = createAnimator()
            mAnimatorSet?.start()
        }
        mState = STATE_RUNNING
    }

    private fun createAnimator(): AnimatorSet {
        return AnimatorSet().apply {
            mAnimatedValues.fill(0f)
            val animators = mutableListOf<Animator>()
            for (i in 0 until mLineNum) {
                ValueAnimator.ofFloat(0f, 1f).apply {
                    startDelay = i * mLineInterval.toLong()
                    duration = ANIMATION_DURATION
                    repeatCount = ValueAnimator.INFINITE
                    repeatMode = ValueAnimator.RESTART
                    interpolator = AccelerateInterpolator()
                    addUpdateListener {
                        mAnimatedValues[i] = it.animatedValue as Float
                        invalidate()
                    }
                    animators.add(this)
                }
            }
            playTogether(animators)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        //pause()
    }

    override fun generateLayoutParams(p: ViewGroup.LayoutParams?): ViewGroup.LayoutParams {
        return LayoutParams(p)
    }

    override fun generateLayoutParams(attrs: AttributeSet?): ViewGroup.LayoutParams {
        return LayoutParams(context, attrs)
    }

    override fun generateDefaultLayoutParams(): ViewGroup.LayoutParams {
        return LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    class LayoutParams : ViewGroup.MarginLayoutParams {

        constructor(c: Context?, attrs: AttributeSet?): super(c, attrs)

        constructor(source: ViewGroup.LayoutParams) : super(source)

        constructor(width: Int, height: Int) : super(width, height)

        constructor(source: ViewGroup.MarginLayoutParams) : super(source)
    }
}

class SuperEllipsizedPathDrawer {
    private var width = 0f
    private var height = 0f

    fun drawPath(path: Path) {
        path.reset()

        path.moveTo(width, height / 2f)
        path.cubicTo(width, height * 0.1f, width * 0.9f, 0f, width / 2f, 0f)
        path.cubicTo(width * 0.1f, 0f, 0f, height * 0.1f, 0f, height / 2f)
        path.cubicTo(0f, height * 0.9f, width * 0.1f, height, width / 2f, height)
        path.cubicTo(width * 0.9f, height, width, height * 0.9f, width, height / 2f)
    }

    fun setSize(width: Float, height: Float) {
        this.width = width
        this.height = height
    }
}