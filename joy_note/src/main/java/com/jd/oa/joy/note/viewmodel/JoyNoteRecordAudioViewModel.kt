package com.jd.oa.joy.note.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.jd.oa.asr.websocket.model.TextMsg
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.model.JoyNoteLanguageModel
import com.jd.oa.joy.note.model.RealTimeTranslate
import com.jd.oa.joy.note.model.RecordPageParam
import com.jd.oa.joy.note.record.RecordIntent
import com.jd.oa.joy.note.repository.JoyNoteRecordAudioRepository
import com.jd.oa.joy.note.translate.TranslateShowType
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

/**
 * @Author: hepiao3
 * @CreateTime: 2025/4/25
 * @Description:
 */
class JoyNoteRecordAudioViewModel(
    private val repo: JoyNoteRecordAudioRepository
) : ViewModel() {
    // 初始化页面完整信息获取
    private val _recordPageInfo = MutableLiveData<String>()
    val recordPageInfo: LiveData<String> = _recordPageInfo

    // 原录制信息
    private val _recordContent = MutableLiveData<String>()
    val recordContent: LiveData<String> = _recordContent

    // 标题修改
    private val _updateTitle = MutableLiveData<Pair<String, Boolean>>()
    val updateTitle: LiveData<Pair<String, Boolean>> = _updateTitle

    // 当前录制状态
    private val _recordState = MutableLiveData<RecordIntent>()
    val recordState: LiveData<RecordIntent> = _recordState

    // 翻译语言信息
    private val _realTimeTranslate =
        MutableLiveData<Triple<RealTimeTranslate?, MutableList<JoyNoteLanguageModel>?, String>?>()
    val realTimeTranslate: LiveData<Triple<RealTimeTranslate?, MutableList<JoyNoteLanguageModel>?, String>?> =
        _realTimeTranslate

    // 仅展示译文
    private val _onlyDisplayTranslate = MutableLiveData<Boolean>()
    val onlyDisplayTranslate: LiveData<Boolean> = _onlyDisplayTranslate

    // 页面参数信息
    private val _recordPageParam = MutableLiveData<RecordPageParam>()
    val recordPageParam: RecordPageParam? get() = _recordPageParam.value

    // asr 文本
    private val _asrTranslateText = MutableSharedFlow<TextMsg>()
    val asrTranslateText: SharedFlow<TextMsg> = _asrTranslateText

    // 录制异常信息
    private val _audioException = MutableLiveData<Pair<String, String>>()
    val audioException: LiveData<Pair<String, String>> = _audioException

    /**
     * 获取实时慧记页面详情信息
     */
    fun getRecordInfo() {
        viewModelScope.safeLaunch {
            _recordPageInfo.value = runCatching {
                _recordPageParam.value?.minutesId?.let {
                    repo.getRecordInfo(it)
                }
            }.getOrElse {
                ""
            }
        }
    }

    /**
     * 请求录制内容信息
     */
    fun getRecordContent() {
        viewModelScope.safeLaunch {
            _recordContent.value = runCatching {
                _recordPageParam.value?.minutesId?.let {
                    repo.getRecordContent(it)
                }
            }.getOrElse {
                ""
            }
        }
    }

    /**
     * 更新标题
     */
    fun updateTitle(minutesId: String, inputText: String) {
        viewModelScope.safeLaunch {
            _updateTitle.value = runCatching {
                Pair(inputText, repo.updateTitle(minutesId, inputText))
            }.getOrElse {
                Pair(inputText, false)
            }
        }
    }

    /**
     * 更新当前语音录制状态
     */
    fun updateRecordState(state: RecordIntent) {
        _recordState.postValue(state)
    }

    /**
     * 设置翻译语言
     */
    fun setTranslateLanguage(model: JoyNoteLanguageModel) {
        if (model.language == null) return
        viewModelScope.safeLaunch {
            _realTimeTranslate.value?.first?.let {
                it.realTimeTranslateTargetLang = model.language
                if (repo.setTranslateLanguage(it)) {
                    _realTimeTranslate.value = _realTimeTranslate.value?.copy(
                        first = _realTimeTranslate.value?.first?.copy(
                            realTimeTranslateTargetLang = model.language
                        ),
                        third = model.display
                    )
                }
            }
        }
    }

    /**
     * 获取翻译目标语言、目标语言列表
     */
    fun getTranslateLanguage(context: Context) {
        viewModelScope.safeLaunch {
            _realTimeTranslate.value = runCatching {
                val languages = async {
                    runCatching {
                        repo.getLanguages()
                    }.getOrElse {
                        null
                    }
                }.await()
                val targetLanguage = async {
                    runCatching {
                        repo.getTranslateLanguage()
                    }.getOrElse {
                        // 获取目标语言失败，则默认英文
                        RealTimeTranslate(
                            "en_US",
                            TranslateShowType.BILINGUAL.value,
                            "14"
                        )
                    }
                }.await()
                // 目标语言和语言列表进行匹配，如果匹配不上，修改目标语言为英文
                val display =
                    languages?.first { it.language == targetLanguage?.realTimeTranslateTargetLang }
                        ?.display ?: run {
                            targetLanguage?.realTimeTranslateTargetLang = "en_US"
                            context.getString(R.string.joynote_real_translate_default_display_name)
                        }
                _onlyDisplayTranslate.value =
                    targetLanguage?.realTimeTranslateShowType == TranslateShowType.ONLY_TRANSLATE.value
                Triple(targetLanguage, languages, display)
            }.getOrNull()
        }
    }

    /**
     * 更新“仅展示译文”开关状态
     */
    fun updateOnlyDisplayTranslateContent(enable: Boolean) {
        viewModelScope.safeLaunch {
            _realTimeTranslate.value?.first?.let {
                it.realTimeTranslateShowType = if (enable) {
                    TranslateShowType.ONLY_TRANSLATE.value
                } else {
                    TranslateShowType.BILINGUAL.value
                }
                if (repo.setTranslateLanguage(it)) {
                    _onlyDisplayTranslate.value = enable
                }
            }
        }
    }

    fun initRecordPageParam(param: RecordPageParam) {
        _recordPageParam.value = param
    }

    fun refreshAsrText(asrText: TextMsg) {
        viewModelScope.safeLaunch {
            _asrTranslateText.emit(asrText)
        }
    }

    fun updateAudioException(errorType: String = "", errorMsg: String = "") {
        _audioException.postValue(Pair(errorType, errorMsg))
    }

    // FIXME 使用Hilt可以省略掉自定义 Factory创建（自动将 Repository 注入到 ViewModel）
    // 但是 Hilt 使用需要同时在 Application 也添加@HiltAndroidApp注解，目前考虑到对项目侵入较大
    class Factory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T =
            JoyNoteRecordAudioViewModel(repository) as T
    }

    companion object {
        private val repository by lazy { JoyNoteRecordAudioRepository() }
    }

}