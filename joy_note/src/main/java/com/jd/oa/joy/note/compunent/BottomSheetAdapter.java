package com.jd.oa.joy.note.compunent;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joy.note.R;
import com.jd.oa.ui.IconFontView;

import java.util.List;

class BottomSheetAdapter extends RecyclerView.Adapter<BottomSheetAdapter.ViewHolder> {

    private final List<BottomSheetItem> items;

    private OnclickListener<BottomSheetItem> mOnItemClickListener;

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView text;
        private final IconFontView icon;
        private final View item;
        private final View gap;
        private final IconFontView select;

        public ViewHolder(View view) {
            super(view);
            text = view.findViewById(R.id.sheet_item_text);
            icon = view.findViewById(R.id.sheet_icon);
            item = view.findViewById(R.id.sheet_item);
            gap = view.findViewById(R.id.sheet_item_gap);
            select = view.findViewById(R.id.select_icon);
        }

        public TextView getText() {
            return text;
        }

        public IconFontView getIcon() {
            return icon;
        }

        public View getItem() {
            return item;
        }

        public IconFontView getSelect() {
            return select;
        }
    }

    BottomSheetAdapter(List<BottomSheetItem> items) {
        this.items = items;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int viewType) {
        View view = LayoutInflater.from(viewGroup.getContext())
                .inflate(R.layout.joy_note_bottom_sheet_item, viewGroup, false);

        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder viewHolder, final int position) {
        BottomSheetItem item = items.get(position);
        viewHolder.getText().setText(item.getTitle());
        if (item.getIcon() == null || item.getIcon() == 0) {
            viewHolder.getIcon().setText(null);
        } else {
            viewHolder.getIcon().setText(viewHolder.getItem().getContext().getString(item.getIcon()));
        }

        if (position == items.size() - 1) {
            viewHolder.gap.setVisibility(View.GONE);
        }
        viewHolder.getItem().setOnClickListener(view -> {
            if (mOnItemClickListener != null) {
                int p = viewHolder.getBindingAdapterPosition();
                mOnItemClickListener.onViewClick(items.get(position), p);
            }
        });
        if (item.isSelected()) {
            viewHolder.getSelect().setVisibility(View.VISIBLE);
        } else {
            viewHolder.getSelect().setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return items.size();
    }


    public void setOnItemClickListener(OnclickListener<BottomSheetItem> mOnItemClickListener) {
        this.mOnItemClickListener = mOnItemClickListener;
    }
}

