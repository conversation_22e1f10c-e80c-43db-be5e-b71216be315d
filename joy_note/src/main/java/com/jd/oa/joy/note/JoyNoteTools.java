package com.jd.oa.joy.note;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.basic.ImBasic;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.joy.note.compunent.OnclickListener;
import com.jd.oa.joy.note.model.JoyNoteMember;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @noinspection unused
 */
public class JoyNoteTools {
    public static final String NEW_TITLE = "newTitle";
    public static final String MINUTES_ID = "minutesId";
    public static final String POSITION = "position";
    private static final String LABEL_URL = "url";
    public static final String MINUTES = "/minutes/";
    public static final int RESULT_REFRESH = 100;


    private static void copyToClipboard(@NonNull String text) {
        ClipboardManager clipboard = (ClipboardManager) AppBase.getAppContext().getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText(LABEL_URL, text);
        clipboard.setPrimaryClip(clip);
    }

    public static String getAppID() {
        String appId = PreferenceManager.UserInfo.getTimlineAppID();
        if (TextUtils.isEmpty(appId)) {
            appId = MultiAppConstant.APPID;
        }
        return appId;
    }

    public static void copyShareUrl(@NonNull String title, @NonNull String id) {
        String host = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getNoteUrl();
        if (host != null && AppBase.getTopActivity() != null) {
            //copyToClipboard(AppBase.getTopActivity().getString(R.string.joynote_more_copy_title) + title + "\n" + );
            copyToClipboard(host + MINUTES + id);
            ToastUtils.showToast(AppBase.getTopActivity().getString(R.string.joynote_copy_successful));
        }
    }

    public static void selectUserFromIm(List<JoyNoteMember> params, OnclickListener<ArrayList<JoyNoteMember>> onclickListener) {
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray jsonArray = new JSONArray();
            for (int a = 0; params != null && a < params.size(); a++) {
                JSONObject member = new JSONObject();
                member.put("appId", params.get(a).ddAppId);
                member.put("erp", params.get(a).account);
                jsonArray.put(member);
            }
            jsonObject.put("selected", jsonArray);
            jsonObject.put("maxNum", 99);
            jsonObject.put("title", AppBase.getAppContext().getString(R.string.me_cmn_h5_select_contact_default_title));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Map<String, Object> map = new Gson().fromJson(jsonObject.toString(), new TypeToken<HashMap<String, Object>>() {
        }.getType());
        ImBasic.openContactSelector(AppBase.getTopActivity(), map, (data, resultCode) -> {
            if (resultCode == Activity.RESULT_OK && data != null) {
                try {
                    @SuppressWarnings("unchecked")
                    ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                    ArrayList<JoyNoteMember> listUsers = new ArrayList<>();
                    if (selected != null) {
                        for (int i = 0; i < selected.size(); i++) {
                            MemberEntityJd entity = selected.get(i);
                            JoyNoteMember joyNoteMember = new JoyNoteMember();
                            joyNoteMember.account = entity.mId;
                            joyNoteMember.ddAppId = entity.mApp;
                            joyNoteMember.imageUrl = entity.mAvatar;
                            listUsers.add(joyNoteMember);
                        }
                        if (onclickListener != null) {
                            onclickListener.onViewClick(listUsers, 0);
                        }
                    }
                } catch (Throwable e) {
                    e.printStackTrace();
                }
            }
        });
    }
}
