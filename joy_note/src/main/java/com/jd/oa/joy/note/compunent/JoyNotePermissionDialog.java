package com.jd.oa.joy.note.compunent;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDialog;

import com.jd.oa.joy.note.R;


public class JoyNotePermissionDialog extends AppCompatDialog {

    public interface OnClickListener {
        void onClickOk(String inputText, int type);

        void onClickCancel();
    }

    private OnClickListener mListener;

    public JoyNotePermissionDialog(Context context) {
        super(context, R.style.JoyNoteDialogStyle);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_joy_note_dialog_permission);
        Window window = getWindow();
        if (window != null) {
            if (window.getDecorView() != null) {
                window.getDecorView().setBackgroundColor(Color.TRANSPARENT);
            }
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.CENTER;
            window.setAttributes(layoutParams);
        }
        setCancelable(true);
        setCanceledOnTouchOutside(true);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            EditText editText = findViewById(R.id.edit);
            RadioGroup radioGroup = findViewById(R.id.rg_type);
            TextView confirm = findViewById(R.id.confirm);
            confirm.setOnClickListener(v -> {
                dismiss();
                if (mListener != null) {
                    String input = "";
                    if (editText != null) {
                        input = editText.getText().toString();
                    }
                    int type = 2;//只读
                    if (radioGroup != null && radioGroup.getCheckedRadioButtonId() == R.id.rb_edit) {
                        type = 1;//编辑
                    }
                    mListener.onClickOk(input, type);
                }
            });

            TextView cancel = findViewById(R.id.cancel);
            cancel.setOnClickListener(v -> {
                dismiss();
                if (mListener != null) {
                    mListener.onClickCancel();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setOnClickListener(OnClickListener listener) {
        mListener = listener;
    }

    @Override
    public void show() {
        super.show();
    }
}
