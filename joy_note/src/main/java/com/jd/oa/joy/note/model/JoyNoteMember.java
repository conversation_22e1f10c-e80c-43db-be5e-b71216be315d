package com.jd.oa.joy.note.model;

import android.os.Parcel;
import android.os.Parcelable;

public class JoyNoteMember implements Parcelable {

    public String ddAppId;
    public Object tenantCode;
    public String userId;
    public String deptFullName;
    public String realName;
    public Object external;
    public String titleName;
    public String imageUrl;
    public String teamId;
    public String userType;
    public String account;
    public String email;

    public JoyNoteMember() {

    }
    public JoyNoteMember(String ddAppId, Object tenantCode, String userId, String deptFullName, String realName, Object external, String titleName, String imageUrl, String teamId, String userType, String account, String email) {
        this.ddAppId = ddAppId;
        this.tenantCode = tenantCode;
        this.userId = userId;
        this.deptFullName = deptFullName;
        this.realName = realName;
        this.external = external;
        this.titleName = titleName;
        this.imageUrl = imageUrl;
        this.teamId = teamId;
        this.userType = userType;
        this.account = account;
        this.email = email;
    }

    protected JoyNoteMember(Parcel in) {
        ddAppId = in.readString();
        userId = in.readString();
        deptFullName = in.readString();
        realName = in.readString();
        titleName = in.readString();
        imageUrl = in.readString();
        teamId = in.readString();
        userType = in.readString();
        account = in.readString();
        email = in.readString();
    }

    public static final Creator<JoyNoteMember> CREATOR = new Creator<JoyNoteMember>() {
        @Override
        public JoyNoteMember createFromParcel(Parcel in) {
            return new JoyNoteMember(in);
        }

        @Override
        public JoyNoteMember[] newArray(int size) {
            return new JoyNoteMember[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(ddAppId);
        dest.writeString(userId);
        dest.writeString(deptFullName);
        dest.writeString(realName);
        dest.writeString(titleName);
        dest.writeString(imageUrl);
        dest.writeString(teamId);
        dest.writeString(userType);
        dest.writeString(account);
        dest.writeString(email);
    }
}
