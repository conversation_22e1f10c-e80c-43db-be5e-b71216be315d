package com.jd.oa.joy.note;

import android.app.Activity;
import android.content.Context;

import com.jd.oa.AppBase;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * @noinspection unused
 */
public class DateTimeUtils {
    public static String getFormatDateAndTime(String timeStr) {
        return getFormatDateAndTime(Long.parseLong(timeStr));
    }

    public static String getFormatDateAndTime(long time) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return "";
        }
        //是今年就不显示 年
        String pattern = isThisYear(time) ?
                activity.getString(R.string.jou_note_time_without_year)
                : activity.getString(R.string.jou_note_time);
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern, Locale.getDefault());
        return dateFormat.format(new Date(time));
    }

    public static String justGetFormatTime(long time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
        return dateFormat.format(new Date(time));
    }

    public static boolean isSameDay(long time1, long time2) {
        try {
            Date date1 = new Date(time1);
            Date date2 = new Date(time2);
            Calendar cal1 = Calendar.getInstance();
            Calendar cal2 = Calendar.getInstance();
            cal1.setTime(date1);
            cal2.setTime(date2);
            return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                    cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是不是今年
     *
     * @param time
     * @return
     */
    public static boolean isThisYear(long time) {
        try {
            Date date1 = new Date(time);
            Date date2 = new Date();
            Calendar cal1 = Calendar.getInstance();
            Calendar cal2 = Calendar.getInstance();
            cal1.setTime(date1);
            cal2.setTime(date2);
            return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    public static String millisecondsToTime(String milliseconds, boolean showHours) {
        if (milliseconds == null) {
            milliseconds = "0";
        }
        return millisecondsToTime(Integer.parseInt(milliseconds), showHours);
    }

    public static String millisecondsToTime(int milliseconds, boolean showHours) {
        milliseconds = milliseconds / 1000;
        int h = milliseconds / 3600;            //小时
        int m = (milliseconds % 3600) / 60;        //分钟
        if (!showHours) {
            m = milliseconds / 60;        //分钟
        }
        String mSpan = "";
        if (m < 10) {
            mSpan = "0";
        }
        int s = (milliseconds % 3600) % 60;        //秒
        String sSpan = "";
        if (s < 10) {
            sSpan = "0";
        }
        if (h > 0) {
            if (!showHours) {
                return m + ":" + sSpan + s;
            }
            return h + ":" + mSpan + m + ":" + sSpan + s;
        }
        if (m > 0) {
            return mSpan + m + ":" + sSpan + s;
        }
        return "00:" + sSpan + s;
    }

    /**
     * 输入毫秒值 输出 XXX分xx秒
     *
     * @param durationMs
     * @return
     */
    public static String getShowTextByMs(Context context, String durationMs) {
        try {
            String min = context.getString(R.string.jou_note_time_min);
            String s = context.getString(R.string.jou_note_time_sec);

            long sec = Long.parseLong(durationMs) / 1000;
            return (sec / 60 > 0 ? (sec / 60 >= 10 ? sec / 60 : "0" + sec / 60) + min : "")
                    + (sec % 60 < 10 ? "0" + sec % 60 : sec % 60) + s;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 输出 x小时x分x秒
     *
     * @param context
     * @param durationMs
     * @return
     */
    public static String getShowTextByMs2(Context context, String durationMs) {
        try {
            String h = context.getString(R.string.jou_note_time_hour);
            String m = context.getString(R.string.jou_note_time_min);
            String s = context.getString(R.string.jou_note_time_sec);
            long sec = Long.parseLong(durationMs) / 1000;
            StringBuilder sb = new StringBuilder();
            if (sec >= 3600) {
                sb.append(sec / 3600);
                sb.append(h);
            }
            if (sec % 3600 / 60 > 0) {
                sb.append(sec % 3600 / 60);
                sb.append(m);
            }
            if (sec % 3600 % 60 > 0) {
                sb.append(sec % 3600 % 60);
                sb.append(s);
            }
            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


    /**
     * 输入毫秒值时间  输出00:00样式
     *
     * @param durationMs
     * @return
     */
    public static String getShowTextByMs(long durationMs) {
        try {
            long sec = durationMs / 1000;
            return (sec >= 600 ? sec / 60 : "0" + sec / 60) + ":"
                    + (sec % 60 < 10 ? "0" + sec % 60 : sec % 60);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    //00:00:00 显示这种样式
    public static String getShowTextByMs2(long durationMs) {
        try {
            long sec = durationMs / 1000;
            StringBuilder sb = new StringBuilder();
            if (sec >= 3600) {
                if (sec < 36000) {
                    sb.append("0");
                }
                sb.append(sec / 3600);
                sb.append(":");
            }
            sec = sec % 3600;
            return sb.append(sec >= 600 ? sec / 60 : "0" + sec / 60)
                    .append(":")
                    .append(sec % 60 < 10 ? "0" + sec % 60 : sec % 60)
                    .toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
    public static String getShowTextByMs3(long durationMs) {
        try {
            long sec = durationMs / 1000;
            StringBuilder sb = new StringBuilder();
            if (sec >= 3600) {
                if (sec < 36000) {
                    sb.append("0");
                }
                sb.append(sec / 3600);
                sb.append(":");
            }else {
                sb.append("00:");
            }
            sec = sec % 3600;
            return sb.append(sec >= 600 ? sec / 60 : "0" + sec / 60)
                    .append(":")
                    .append(sec % 60 < 10 ? "0" + sec % 60 : sec % 60)
                    .toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


}
