package com.jd.oa.joy.note.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.jd.oa.JDMAConstants
import com.jd.oa.JDMAPages
import com.jd.oa.joy.note.model.DataState
import com.jd.oa.joy.note.model.TotalTimeLine
import com.jd.oa.joy.note.repository.JoyNoteSpeakerRepo
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.safeLaunch

class JoyNoteDetailSpeakerViewModel(val repo: JoyNoteSpeakerRepo) : ViewModel() {

    private val _totalSpeakerTimeline = MutableLiveData<TotalTimeLine>()
    val totalSpeakerTimeline: LiveData<TotalTimeLine> = _totalSpeakerTimeline

    private val _dataState = MutableLiveData<DataState>()
    val dataState: LiveData<DataState> = _dataState

    fun getSpeakerTimeLine(minutesId: String, isRefresh: Boolean = false) {
        if (isRefresh) {
            JDMAUtils.clickEvent(
                JDMAPages.Mobile_Page_Minute_Detail,
                JDMAConstants.MOBILE_EVENT_MINUTES_DETAIL_HOME_SPOKE_TAB_REFRESH,
                emptyMap()
            )
        }
        _dataState.value = DataState.Loading
        viewModelScope.safeLaunch {
            val speakerTimeLinePair = repo.getSpeakerTimeLine(minutesId)
            _dataState.value = speakerTimeLinePair.first
            speakerTimeLinePair.second?.let {
                _totalSpeakerTimeline.value = it
            }
        }
    }

    class Factory(val repo: JoyNoteSpeakerRepo) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = JoyNoteDetailSpeakerViewModel(repo) as T
    }
}

