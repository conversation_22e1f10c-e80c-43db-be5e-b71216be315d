package com.jd.oa.joy.note.model

import android.os.Parcelable
import androidx.databinding.ObservableBoolean
import kotlinx.parcelize.Parcelize

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/19
 * @Description:
 */
@Parcelize
data class ScopeContent(
    val type: String,
    val iconString: String,
    val iconColor: Int,
    val title: String,
    val desc: String,
    val isCheck: ObservableBoolean,
    val org: Org? = null,
) : Parcelable
