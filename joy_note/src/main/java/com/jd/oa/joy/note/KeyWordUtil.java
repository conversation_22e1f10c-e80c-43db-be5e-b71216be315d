package com.jd.oa.joy.note;

import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class KeyWordUtil {

    /**
     * 找出句子里面所有关键词索引
     *
     * @param text
     * @param keyword
     * @return
     */
    public static List<Integer> getAllKeywordIndex(String text, String keyword) {
        List<Integer> indexList = new ArrayList<>();
        if (TextUtils.isEmpty(keyword)) {
            return indexList;
        }
        int startIndex = 0;
        //忽略字母大小写
        text = text.toLowerCase();
        keyword = keyword.toLowerCase();
        while (startIndex + keyword.length() < text.length() && text.indexOf(keyword, startIndex) >= 0) {
            int index = text.indexOf(keyword, startIndex);
            indexList.add(index);
            startIndex = index + keyword.length();
        }
        return indexList;
    }


    /***
     * 指定关键字高亮 字符串整体高亮
     * @param originString    原字符串
     * @param keyWords        高亮字符串
     * @param highLightColor  高亮色值
     * @return 高亮后的字符串
     */
    public static SpannableStringBuilder getHighLightWord(String originString, String keyWords, int highLightColor) {
        SpannableStringBuilder originSpannableString = new SpannableStringBuilder(originString);
        if (!TextUtils.isEmpty(keyWords)) {
            Pattern pattern = Pattern.compile(keyWords);
            Matcher matcher = pattern.matcher(originSpannableString);
            while (matcher.find()) {
                int startIndex = matcher.start();
                int endIndex = matcher.end();
                originSpannableString.setSpan(new ForegroundColorSpan(highLightColor), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }
        return originSpannableString;
    }

}
