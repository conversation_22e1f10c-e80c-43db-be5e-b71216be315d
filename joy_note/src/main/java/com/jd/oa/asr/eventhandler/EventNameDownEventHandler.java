package com.jd.oa.asr.eventhandler;

import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.Logger;
import com.jd.oa.asr.websocket.model.DownEvent;

public abstract class EventNameDownEventHandler implements DownEventHandler {

    private String eventName;

    public EventNameDownEventHandler(String eventName) {
        this.eventName = eventName;
    }

    @Override
    public boolean handleDownEvent(DownEvent downEvent) {
        return eventName.equalsIgnoreCase(downEvent.getEventName());
    }

    @Override
    public void onDownEvent(AsrClient client, DownEvent downEvent) {
        Logger.getDefault().info(AsrClient.TAG,"onDownEvent, " + eventName + ", event: " + downEvent.getMessage());
    }
}
