package com.jd.oa.asr

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioManager
import android.media.MediaRecorder
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import android.text.TextUtils
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.core.app.NotificationCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.Constant.SHORTCUT_ACTION
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.asr.AsrClient.AsrClientListener
import com.jd.oa.asr.AsrClient.STATE_RUNNING
import com.jd.oa.asr.audio.AudioDurationCalculator
import com.jd.oa.asr.audio.AudioRecorder
import com.jd.oa.asr.audio.AudioVolumeCalculator
import com.jd.oa.asr.exception.AsrException
import com.jd.oa.asr.websocket.model.EventName
import com.jd.oa.asr.websocket.model.TextMsg
import com.jd.oa.audio.JMAudioCategoryManager
import com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE
import com.jd.oa.joy.note.DateTimeUtils
import com.jd.oa.joy.note.R
import com.jd.oa.joy.note.event.DurationEvent
import com.jd.oa.joy.note.event.ExceptionEvent
import com.jd.oa.joy.note.record.AudioRecordCurrent
import com.jd.oa.joy.note.record.AudioRecordStatus
import com.jd.oa.joy.note.record.RecordAction
import com.jd.oa.joy.note.record.RecordAudioService.Companion.ACTION_RECORD_AUDIO
import com.jd.oa.joy.note.record.RecordIntent
import com.jd.oa.joy.note.translate.PlayControlFragment.Companion.MAX_AUDIO_DURATION
import com.jd.oa.utils.NotificationUtils
import com.jd.oa.utils.NotificationUtils.CHANNEL_DESCRIPTION_OTHER
import de.greenrobot.event.EventBus
import java.lang.ref.WeakReference
import java.util.concurrent.CopyOnWriteArrayList

class AsrService : Service() {

    companion object {
        private const val TAG = "AsrClient-AsrService"
        const val ARG_URL = "arg.url"
        const val ARG_CHANNEL_ID = "arg.channelId"
        const val ARG_FILE_PATH = "arg.filePath"
        const val ARG_RECORD_DURATION = "arg.duration"
        const val DURATION = "duration"
        const val CLASS_NAME = "class.name"
        const val ARG_ASR_TRANSLATE_TEXT = "arg.translate.text"
        const val ACTION_RECORD_TEXT_CHANGE = "action.audio.record.text.change"
    }

    private var binder: AsrServiceBinder? = null

    // 多线程高频修改，使用CopyOnWriteArrayList
    private val onTextUpdate: MutableList<(TextMsg) -> Unit> = CopyOnWriteArrayList()
    private val onAsrError: MutableList<(errorType: String, errorMsg: String) -> Unit> = CopyOnWriteArrayList()
    private val onVolumeChange: MutableList<(Int) -> Unit> = CopyOnWriteArrayList()
    private val onDurationUpdate: MutableList<(Long) -> Unit> = CopyOnWriteArrayList()
    private val playStateChange: MutableList<(RecordIntent) -> Unit> = CopyOnWriteArrayList()

    private var asrClient: AsrClient? = null
    private var url: String? = null
    private var channelId: String? = null
    private var filePath: String? = null
    private var recordDuration: Long = 0

    private lateinit var notification: Notification
    private var notificationId = 111111

    private var duration = 0L

    private var tm: TelephonyManager? = null
    private var ams: AudioManager? = null
    private var listener: JoyNoteListener? = null

    private val mAudioFocusListener = AudioFocusListener()

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            kotlin.runCatching {
                if (intent?.getStringExtra(CLASS_NAME) == <EMAIL>) return
                when (intent?.getStringExtra(SHORTCUT_ACTION)) {
                    RecordAction.START.name, RecordAction.RESUME.name -> {
                        if (checkSelfPermission(Manifest.permission.RECORD_AUDIO)
                            == PackageManager.PERMISSION_GRANTED
                        ) {
                            startRecord()
                        }
                    }

                    RecordAction.PAUSE.name -> pauseRecord()
                    RecordAction.END.name -> finishRecord()
                    else -> println("not implement !")
                }
            }.onFailure {
                MELogUtil.localE(TAG, "onReceive", it)
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Logger.getDefault().info(TAG, "onCreate")

        binder = AsrServiceBinder(this)
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(receiver, IntentFilter(ACTION_RECORD_AUDIO))

        LocalBroadcastManager.getInstance(this)
            .registerReceiver(receiver, IntentFilter(ACTION_RECORD_AUDIO))
        // 后台监听电话的呼叫状态。
        // 得到电话管理器
        try {
            tm = this.getSystemService(TELEPHONY_SERVICE) as TelephonyManager

            listener = JoyNoteListener()
            tm?.listen(listener, PhoneStateListener.LISTEN_CALL_STATE)

            val state = tm?.getCallState()
            if (state == TelephonyManager.CALL_STATE_RINGING || state == TelephonyManager.CALL_STATE_OFFHOOK) {
                JMAudioCategoryManager.getInstance().setOutCall(true)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        //慧记10 AsrRecordAudioService onCreate 初始化AUDIO_SERVICE
        try {
            ams = getSystemService(AUDIO_SERVICE) as AudioManager
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.getDefault().info(TAG, "onStartCommand, intent: $intent")

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        this.url = intent?.getStringExtra(ARG_URL)
        this.channelId = intent?.getStringExtra(ARG_CHANNEL_ID)
        this.filePath = intent?.getStringExtra(ARG_FILE_PATH)
        this.recordDuration = intent?.getLongExtra(ARG_RECORD_DURATION, 0L) ?: 0L

        Logger.getDefault()
            .info(TAG, "onBind, url: $url, channelId: $channelId, filePath: $filePath")

        if (TextUtils.isEmpty(url)) throw IllegalArgumentException("url is empty")
        if (TextUtils.isEmpty(channelId)) throw IllegalArgumentException("channelId is empty")
        if (TextUtils.isEmpty(filePath)) {
            filePath = "$externalCacheDir/audio.pcm"
        }
        if (asrClient == null) {
            asrClient = createAsrClient(url!!, channelId!!, filePath!!)
        }

        notification = createNotification(NotificationUtils.CHANNEL_ID_OTHER)
        startForeground(notificationId, notification)

        return binder
    }

    private fun createAsrClient(url: String, channelId: String, filePath: String): AsrClient {
        val audioRecorder = AudioRecorder.Builder()
            .setAudioFormat(AudioFormat.ENCODING_PCM_16BIT)
            .setAudioSource(MediaRecorder.AudioSource.MIC)
            .setFilePath(filePath)
            .addAudioDataProcessors(object : AudioVolumeCalculator() {
                override fun onVolumeChanged(volume: Int) {
                    onVolumeChange.forEach { it.invoke(volume) }
                }
            })
            .addAudioDataProcessors(object : AudioDurationCalculator(recordDuration) {
                override fun onDurationUpdate(duration: Long) {
                    onDurationUpdate.forEach { it.invoke(duration) }
                    <EMAIL> = duration
                    notifyRecording(<EMAIL>)
                    notifyTimeOut()
                }
            })
            .setSampleRateInHz(16000)
            .build()
        val client = AsrClient.Builder(this)
            .setChannelId(channelId)
            .setUrl(url)
            .setFilePath(filePath)
            .setAudioRecorder(audioRecorder)
            .setLogger(AsrLogger())
            .setListener(object : AsrClientListener {

                override fun onStarting() {
                    playStateChange.forEach { it.invoke(RecordIntent.Starting) }
                }

                override fun onStarted() {
                    Log.d(TAG, "onStart")
                    notifyRecording(duration)
                    Intent(ACTION_RECORD_AUDIO).apply {
                        putExtra(SHORTCUT_ACTION, RecordAction.START.name)
                        putExtra(CLASS_NAME, <EMAIL>)
                        LocalBroadcastManager.getInstance(this@AsrService).sendBroadcast(this)
                    }
                    playStateChange.forEach { it.invoke(RecordIntent.Start) }
                    AudioRecordCurrent.currentStatus = AudioRecordStatus.RECORDING
                }

                override fun onPausing() {
                    playStateChange.forEach { it.invoke(RecordIntent.Pausing) }
                }

                override fun onPaused() {
                    notifyPause()
                    Intent(ACTION_RECORD_AUDIO).apply {
                        putExtra(SHORTCUT_ACTION, RecordAction.PAUSE.name)
                        putExtra(CLASS_NAME, <EMAIL>)
                        LocalBroadcastManager.getInstance(this@AsrService).sendBroadcast(this)
                    }
                    playStateChange.forEach { it.invoke(RecordIntent.Pause) }
                    AudioRecordCurrent.currentStatus = AudioRecordStatus.PAUSE
                }

                override fun onText(textMsg: TextMsg) {
                    onTextUpdate.forEach { it.invoke(textMsg) }
                    Intent(ACTION_RECORD_TEXT_CHANGE).apply {
                        putExtra(ARG_ASR_TRANSLATE_TEXT, textMsg)
                        LocalBroadcastManager.getInstance(this@AsrService).sendBroadcast(this)
                    }
                }

                override fun onError(e: Exception, errorType: String, errorMsg: String) {
                    Log.e(TAG, "onError, errorType: $errorType, errorMsg: $errorMsg")
                    onAsrError.forEach { it.invoke(errorType, errorMsg) }
                    EventBus.getDefault().postSticky(ExceptionEvent(errorType, errorMsg))
                }

                override fun onReconnecting() {
                    /*Log.e(TAG, "onReconnecting: ${getString(R.string.joynote_alert_net_poor)}")
                    Intent(ACTION_RECORD_EXCEPTION_TIP).apply {
                        putExtra(ARG_EXCEPTION_TEXT, getString(R.string.joynote_alert_net_poor))
                        LocalBroadcastManager.getInstance(this@AsrService).sendBroadcast(this)
                    }*/
                }

                override fun onFinished(success: Boolean, errorCode: Int) {
                    Log.i(TAG, "onFinished, success: $success, errorCode: $errorCode")
                    playStateChange.forEach { it.invoke(RecordIntent.End(success)) }
                    AudioRecordCurrent.currentStatus = AudioRecordStatus.COMPLETE
                }
            }).build()
        return client
    }

    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    fun startRecord() {
        if (checkSelfPermission(Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_DENIED) {
            throw IllegalStateException("RECORD_AUDIO permission denied")
        }
        val result =
            JMAudioCategoryManager.getInstance().setAudioCategory(JME_AUDIO_CATEGORY_JOY_NOTE) {
                pauseRecord()
            }
        if (!result.available) {
            //onAsrError.forEach { it.invoke(AsrClient.AUDIO_CATEGORY_ERROR, "") }
            return
        }
        //ams?.requestAudioFocus(mAudioFocusListener, AudioManager.STREAM_SYSTEM, AudioManager.AUDIOFOCUS_GAIN)

        asrClient?.start()
    }

    fun pauseRecord() {
        asrClient?.pause()
        JMAudioCategoryManager.getInstance().releaseJoyNote()
    }

    fun finishRecord() {
        asrClient?.finish()
        JMAudioCategoryManager.getInstance().releaseJoyNote()
    }

    fun setTranslate(translate: String?) {
        asrClient?.translate = translate
    }

    fun addOnTextUpdateListener(listener: (TextMsg) -> Unit) {
        onTextUpdate.add(listener)
    }

    fun removeOnTextUpdateListener(listener: (TextMsg) -> Unit) {
        onTextUpdate.remove(listener)
    }

    fun addOnAsrErrorListener(listener: (errorType: String, errorMsg: String) -> Unit) {
        onAsrError.add(listener)
    }

    fun removeOnAsrErrorListener(listener: (errorType: String, errorMsg: String) -> Unit) {
        onAsrError.remove(listener)
    }

    fun addVolumeChangeListener(listener: (Int) -> Unit) {
        onVolumeChange.add(listener)
    }

    fun removeVolumeChangeListener(listener: (Int) -> Unit) {
        onVolumeChange.remove(listener)
    }

    fun addDurationChangeListener(listener: (Long) -> Unit) {
        onDurationUpdate.add(listener)
    }

    fun removeDurationChangeListener(listener: (Long) -> Unit) {
        onDurationUpdate.remove(listener)
    }

    fun addPlayStateChangeListener(listener: (RecordIntent) -> Unit) {
        playStateChange.add(listener)
    }

    fun removePlayStateChangeListener(listener: (RecordIntent) -> Unit) {
        playStateChange.remove(listener)
    }

    private fun createNotification(channelId: String): Notification {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                NotificationUtils.CHANNEL_NAME_OTHER,
                NotificationManager.IMPORTANCE_LOW
            )
            channel.description = CHANNEL_DESCRIPTION_OTHER
            val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
        // 创建通知
        val builder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.jdme_app_launcher)
            .setContentTitle(getString(R.string.joynote_notification_title))
            .setContentText("00:00")
            .setOngoing(true)
        return builder.build()
    }

    private fun notifyRecording(duration: Long) {
        val builder = NotificationCompat.Builder(this, NotificationUtils.CHANNEL_ID_OTHER)
            .setSmallIcon(R.drawable.jdme_app_launcher)
            .setContentTitle(getString(R.string.joynote_notification_title))
            .setContentText(DateTimeUtils.getShowTextByMs2(duration))
            .setOngoing(true)
        val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        manager.notify(notificationId, builder.build())
        EventBus.getDefault().postSticky(DurationEvent(duration))
    }

    private fun notifyPause() {
        val builder = NotificationCompat.Builder(this, NotificationUtils.CHANNEL_ID_OTHER)
            .setSmallIcon(R.drawable.jdme_app_launcher)
            .setContentTitle(getString(R.string.joynote_notification_content))
            .setContentText(DateTimeUtils.getShowTextByMs2(duration))
            .setOngoing(true)
        val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        manager.notify(notificationId, builder.build())
    }

    fun getAudioState() = asrClient?.state

    fun notifyTimeOut() {
        if (getAudioState() == STATE_RUNNING && duration >= MAX_AUDIO_DURATION) {
            pauseRecord()
            asrClient?.asrClientListener?.onError(
                AsrException(),
                EventName.STOP_TIME_OUT,
                getString(R.string.joynote_is_over_time_for_record)
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        asrClient?.pause()

        LocalBroadcastManager.getInstance(this).unregisterReceiver(receiver)

        try {
            tm?.listen(listener, PhoneStateListener.LISTEN_NONE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        listener = null
        try {
            ams?.abandonAudioFocus(mAudioFocusListener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        JMAudioCategoryManager.getInstance().setOutCall(false)
        JMAudioCategoryManager.getInstance().releaseJoyNote()
        asrClient?.release()
        binder = null

        Logger.getDefault().info(TAG, "onDestroy")
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Logger.getDefault().info(TAG, "onUnbind, intent: $intent")
        return super.onUnbind(intent)
    }

    class AsrServiceBinder(service: AsrService) : Binder() {
        private val serviceRef = WeakReference(service)
        fun getService(): AsrService? = serviceRef.get()
    }

    private inner class JoyNoteListener : PhoneStateListener() {
        // 当电话的呼叫状态发生变化的时候调用的方法
        override fun onCallStateChanged(state: Int, incomingNumber: String) {
            super.onCallStateChanged(state, incomingNumber)
            try {
                when (state) {
                    TelephonyManager.CALL_STATE_IDLE -> {
                        //继续
                        JMAudioCategoryManager.getInstance().setOutCall(false)
                    }

                    TelephonyManager.CALL_STATE_RINGING -> {
                        //暂停
                        val category = JMAudioCategoryManager.getInstance().currentAudioCategory
                        if (category != JME_AUDIO_CATEGORY_JOY_NOTE) {
                            return
                        }
                        JMAudioCategoryManager.getInstance().setOutCall(true)
                        pauseRecord()
                    }

                    else -> {}
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
    }

    inner class AudioFocusListener : AudioManager.OnAudioFocusChangeListener {

        override fun onAudioFocusChange(focusChange: Int) {
            when (focusChange) {
                AudioManager.AUDIOFOCUS_GAIN -> {
                    //重新获取AudioFocus, 一般是AudioFocus_Loss_Transient结束后自动获得
                    //JMAudioCategoryManager.getInstance().setOutCall(false)
                }

                AudioManager.AUDIOFOCUS_LOSS -> {
                    //AudioFocus将会被其他应用占用未知时间
                    val category = JMAudioCategoryManager.getInstance().currentAudioCategory
                    if (category != JME_AUDIO_CATEGORY_JOY_NOTE) return
                    //暂停录制
                    pauseRecord()
                }

                else -> {
                    //其他情况（外部电话），包括AUDIOFOCUS_LOSS_TRANSIENT
                    //AudioFocus将会被其他应用短暂占用并在结束时触发AUDIOFOCUS_GAIN
                    val category = JMAudioCategoryManager.getInstance().currentAudioCategory
                    if (category != JME_AUDIO_CATEGORY_JOY_NOTE) return
                    //设置OutCall状态，设置为true之后不能再开始录制慧记，直到被设为false
                    //JMAudioCategoryManager.getInstance().setOutCall(true)
                    pauseRecord()
                }
            }
        }
    }
}


class AsrLogger : Logger.DefaultLogger() {

    override fun verbose(tag: String?, msg: String?) {
        super.verbose(tag, msg)
    }

    override fun debug(tag: String?, msg: String?) {
        super.debug(tag, msg)
        MELogUtil.localD(tag, msg)
    }

    override fun info(tag: String?, msg: String?) {
        super.info(tag, msg)
        MELogUtil.localI(tag, msg)
    }

    override fun warn(tag: String?, msg: String?) {
        super.warn(tag, msg)
        MELogUtil.localW(tag, msg)
    }

    override fun error(tag: String?, msg: String?, e: Throwable?) {
        super.error(tag, msg, e)
        MELogUtil.localE(tag, msg, e)
    }
}