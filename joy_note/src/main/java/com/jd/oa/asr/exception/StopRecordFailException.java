package com.jd.oa.asr.exception;

public class StopRecordFailException extends AsrException {

    public boolean exist;

    public StopRecordFailException(boolean exist) {
        this.exist = exist;
    }

    public StopRecordFailException(boolean exist, String message) {
        super(message);
        this.exist = exist;
    }

    public StopRecordFailException(boolean exist, String message, Throwable cause) {
        super(message, cause);
        this.exist = exist;
    }

    public StopRecordFailException(boolean exist, Throwable cause) {
        super(cause);
        this.exist = exist;
    }
}
