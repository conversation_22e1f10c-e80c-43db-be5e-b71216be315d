package com.jd.oa.asr.eventhandler;


import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.EventName;
import com.jd.oa.asr.websocket.model.TextMsg;

public class SendTextHand<PERSON> extends EventNameDownEventHandler {
    private static final String TAG = "SendTextHandler";

    public SendTextHandler() {
        super(EventName.UPSIDE_SEND_TEXT);
    }

    @Override
    public void onDownEvent(AsrClient client, DownEvent downEvent) {
        TextMsg textMsg = client.parseMessage(downEvent.getMessage(), TextMsg.class);
        client.getAsrClientListener().onText(textMsg);
    }
}