package com.jd.oa.asr.audio;

import android.media.AudioFormat;

public abstract class AudioDurationCalculator implements AudioRecorder.AudioDataProcessor {

    private long bytesRead = 0;

    private long lastDuration = 0;

    private final long initDuration;

    public AudioDurationCalculator(long initDuration) {
        this.initDuration = initDuration;
    }

    @Override
    public byte[] process(AudioRecorder recorder, int read, byte[] data) {
        bytesRead += read;
        int sampleRate = recorder.getAudioRecord().getSampleRate();
        int channelCount = recorder.getAudioRecord().getChannelCount();
        int audioFormat = recorder.getAudioRecord().getAudioFormat();
        int bitDepth = getBitDepth(audioFormat);

        long duration = (bytesRead / ((long) sampleRate * channelCount * bitDepth / 8)) * 1000 + initDuration;
        if (duration - lastDuration >= 1000) {
            lastDuration = duration;
            onDurationUpdate(duration);
        }
        return data;
    }

    private int getBitDepth(int audioFormat) {
        switch (audioFormat) {
            case AudioFormat.ENCODING_PCM_8BIT:
                return 8;
            case AudioFormat.ENCODING_PCM_16BIT:
                return 16;
            case AudioFormat.ENCODING_PCM_FLOAT:
                return 32;
            default:
                throw new IllegalArgumentException("Not supported audio format: " + audioFormat);
        }
    }

    public abstract void onDurationUpdate(long duration);
}
