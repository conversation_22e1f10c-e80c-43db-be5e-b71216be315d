package com.jd.oa.asr.eventhandler;

import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.EventName;

import java.io.File;

public class AllowRecordHandler extends EventNameDownEventHandler {

    public AllowRecordHandler() {
        super(EventName.UPSIDE_ALLOW_RECORD);
    }

    @Override
    public void onDownEvent(AsrClient client, DownEvent downEvent) {
        super.onDownEvent(client, downEvent);
        client.getReadAudioFileLoop().loop();
        client.getAsrClientListener().onStarted();
        client.setState(AsrClient.STATE_RUNNING);
    }
}