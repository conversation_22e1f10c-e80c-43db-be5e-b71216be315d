package com.jd.oa.asr.eventhandler;

import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.exception.StopRecordFailException;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.EventName;
import com.jd.oa.asr.websocket.model.WrongMsg;
import com.jd.oa.joy.note.R;

public class StopRecordFailHandler extends EventNameDownEventHandler {

    public StopRecordFailHandler() {
        super(EventName.STOP_RECORD_FAIL);
    }

    @Override
    public void onDownEvent(AsrClient client, DownEvent downEvent) {
        super.onDownEvent(client, downEvent);
        WrongMsg wrongMsg = client.parseMessage(downEvent.getMessage(), WrongMsg.class);
        if (wrongMsg == null) {
            return;
        }
        int tipId = R.string.joynote_audio_finish_tips;
        if (!wrongMsg.exist) {
            tipId = R.string.joynote_audio_del_tips;
        }
        AsrClient.AsrClientListener listener = client.getAsrClientListener();
        if (listener != null) {
            client.getAsrClientListener().onError(new StopRecordFailException(wrongMsg.exist), downEvent.getEventName(), client.getContext().getString(tipId));
        }

        client.forceStop();
    }
}
