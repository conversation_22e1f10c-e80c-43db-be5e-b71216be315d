package com.jd.oa.asr;


import java.util.HashMap;
import java.util.Map;

public class UpEventStateMap {

    private Map<Long,String> indexIdMap = new HashMap<>();
    private Map<String,UpEventState> idStateMap = new HashMap<>();

    public int getState(String id) {
        UpEventState state = idStateMap.get(id);
        if (state == null) {
            return UpEventState.STATE_NONE;
        }
        return state.state;
    }

    public int getState(long index) {
        String id = indexIdMap.get(index);
        if (id == null) {
            return UpEventState.STATE_NONE;
        }
        return getState(id);
    }

    public void setState(String id, int state) {
        UpEventState upEventState = idStateMap.computeIfAbsent(id, k -> new UpEventState());
        upEventState.state = state;
    }

    public void setState(long index, String id, int state) {
        indexIdMap.put(index, id);
        setState(id, state);
    }

    public void clear() {
        indexIdMap.clear();
        idStateMap.clear();
    }

    public static class UpEventState {
        public static final int STATE_NONE = 0;
        public static final int STATE_CREATE = 1;
        public static final int STATE_SENT = 2;
        public static final int STATE_RECEIVED = 3;

        String id;
        long index;
        int state;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public long getIndex() {
            return index;
        }

        public void setIndex(long index) {
            this.index = index;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }
    }
}
