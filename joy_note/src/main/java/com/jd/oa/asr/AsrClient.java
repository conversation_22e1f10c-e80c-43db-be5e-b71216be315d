package com.jd.oa.asr;

import static com.jd.oa.websocket.WebSocketTool.getEventJson;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioFormat;
import android.media.MediaRecorder;

import androidx.annotation.RequiresPermission;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import com.jd.oa.asr.eventhandler.AllowRecordHandler;
import com.jd.oa.asr.eventhandler.DifferentDevicesHandler;
import com.jd.oa.asr.eventhandler.DownEventHandler;
import com.jd.oa.asr.eventhandler.NotAllowRecordHandler;
import com.jd.oa.asr.eventhandler.ReceiveAudioHandler;
import com.jd.oa.asr.eventhandler.SendTextHandler;
import com.jd.oa.asr.eventhandler.StopRecordFailHandler;
import com.jd.oa.asr.eventhandler.StopRecordHandler;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.ErrorMsg;
import com.jd.oa.asr.websocket.model.TextMsg;
import com.jd.oa.asr.websocket.model.UpEvent;
import com.jd.oa.asr.audio.AudioRecorder;
import com.jd.oa.joy.note.R;
import com.jd.oa.joy.note.model.JoyNoteFinishInfo;
import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.asr.socket.WebSocket;
import com.jd.oa.asr.socket.WebSocketException;
import com.jd.oa.websocket.AudioFileSender;

import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class AsrClient {
    public static final String TAG = "AsrClient";
    public static final int STATE_INIT = 0;
    public static final int STATE_STARTING = 1;
    public static final int STATE_RUNNING = 3;
    public static final int STATE_PAUSING = 4;
    public static final int STATE_PAUSED = 5;
    public static final int STATE_FINISHING = 6;
    public static final int STATE_FINISHED = 7;
    public static final int STATE_STOPPED = 8;

    static String stateToString(int state) {
        switch (state) {
            case STATE_INIT: return "INIT";
            case STATE_STARTING: return "STARTING";
            case STATE_RUNNING: return "RUNNING";
            case STATE_PAUSING: return "PAUSING";
            case STATE_PAUSED: return "PAUSED";
            case STATE_FINISHING: return "FINISHING";
            case STATE_FINISHED: return "FINISHED";
            case STATE_STOPPED: return "STOPPED";
            default: return "UNKNOWN: "+ state;
        }
    }

    public static final String AUTHENTICATION_ERROR = "authentication_error";
    public static final String WEBSOCKET_ERROR = "websocket_error";
    public static final String AUDIO_CATEGORY_ERROR = "audio_category_error";

    private Gson gson = new GsonBuilder()
            .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
            .create();
    private String channelId;

    private final List<DownEventHandler> downEventHandlers = Arrays.asList(
            new AllowRecordHandler(),
            new SendTextHandler(),
            new NotAllowRecordHandler(),
            new StopRecordFailHandler(),
            new ReceiveAudioHandler(),
            new DifferentDevicesHandler(),
            new StopRecordHandler()
    ); // <DownEventHandler>

    private String filePath;
    private final WebSocket webSocket;
    private AudioRecorder audioRecorder;
    private final ReadAudioFileLoop readAudioFileLoop;

    private final WebSocketListener webSocketListener = new WebSocketListener();

    //private final UpEventStateMap upEventStateMap = new UpEventStateMap();

    private AsrClientListener asrClientListener;

    private int state = STATE_INIT;

    private String translate;

    private UpEventFactory mUpEventFactory;

    private Context context;

    private ReentrantReadWriteLock rwLock;

    public AsrClient(Builder builder) {
        this.channelId = builder.channelId;
        this.asrClientListener = builder.listener;
        this.filePath = builder.filePath;
        this.context = builder.context.getApplicationContext();

        this.mUpEventFactory = new UpEventFactory(builder.channelId);

        webSocket = new WebSocket.Builder()
                .setUrl(builder.url)
                .setOnStateChangeListener(webSocketListener)
                .build();

        this.audioRecorder = builder.audioRecorder;
        if (this.audioRecorder == null) {
            this.audioRecorder = new AudioRecorder.Builder()
                    .setAudioFormat(AudioFormat.ENCODING_PCM_16BIT)
                    .setAudioSource(MediaRecorder.AudioSource.MIC)
                    .setFilePath(builder.filePath)
                    .setSampleRateInHz(16000)
                    .build();
        }
        rwLock = new ReentrantReadWriteLock();
        audioRecorder.setLock(rwLock);

        this.readAudioFileLoop = new ReadAudioFileLoop(
                this,
                builder.filePath,
                rwLock,
                audioRecorder.getBufferSize() * 5,
                new OnReadAudioDataListener()
        );
    }

    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    public void start() {
        Logger.getDefault().info(TAG,"start(), state: " + stateToString(state));
        if (state == STATE_STARTING) return;

        try {
            webSocket.connect();
            audioRecorder.start();
            this.state = STATE_STARTING;

            if (asrClientListener != null) {
                asrClientListener.onStarting();
            }
        } catch (Exception e) {
            Logger.getDefault().error(TAG,"start(), state: " + stateToString(state), e);
        }
    }

    public void pause() {
        Logger.getDefault().info(TAG,"pause(), state: " + stateToString(state));
        if (state == STATE_RUNNING) {
            audioRecorder.pause();
            readAudioFileLoop.pause();

            sendPauseEvent();
            this.state = STATE_PAUSING;

            if (asrClientListener != null) {
                asrClientListener.onPausing();
            }
        }
    }

    public void sendPauseEvent() {
        JSONObject jsonObject = mUpEventFactory.createPauseEvent();
        webSocket.send(jsonObject);
        Logger.getDefault().info(TAG, "sendPauseRecordMsg,isActive:"  + webSocket.isActive() + ", isConnected: " + webSocket.isConnected() + ", msg: "+ jsonObject);
    }

    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    public void resume() {
        start();
        Logger.getDefault().info(TAG,"resume()");
    }

    public void forceStop() {
        Logger.getDefault().info(TAG,"forceStop(), state: " + stateToString(state));
        try {
            audioRecorder.stop();
            webSocket.close();
            readAudioFileLoop.stop();
            state = STATE_STOPPED;
        } catch (Exception e) {
            Logger.getDefault().error(TAG,"forceStop(), state: " + stateToString(state), e);
        }
    }

    public void finish() {
        Logger.getDefault().info(TAG,"finish(), state: " + stateToString(state));
        if (this.state == STATE_PAUSED || this.state == STATE_PAUSING) {
            onFinish(true);
        } else if (state == STATE_STOPPED || state == STATE_INIT) {
            sedFinishRequest();
        } else {
            onFinish(false);
            this.state = STATE_FINISHING;
        }
    }

    private void onFinish(boolean complete) {
        Logger.getDefault().info(TAG,"onFinish(), complete: " + complete + "state: " + stateToString(state));
        if (complete) {
            forceStop();
            sedFinishRequest();
        } else {
            audioRecorder.stop();
            readAudioFileLoop.stop();
        }
    }

    private void sedFinishRequest() {
        VideoListRepository.getUserRepository().finishJoyNote(channelId, null, new LoadDataCallback<JoyNoteFinishInfo>() {
            @Override
            public void onDataLoaded(JoyNoteFinishInfo joyNoteFinishInfo) {
                AsrClient.this.state = STATE_FINISHED;
                asrClientListener.onFinished(true, AudioFileSender.FINISH_SUCCESS);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                asrClientListener.onFinished(false, AudioFileSender.FINISH_FAIL);
            }
        });
    }

    public String getTranslate() {
        return translate;
    }

    public void setTranslate(String translate) {
        this.translate = translate;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public Context getContext() {
        return context;
    }

    public <T> T parseDownEvent(Object[] args, Class<T> tClass) {
        if (args == null || args.length == 0) return null;
        JSONObject msg = (JSONObject) args[0];
        return gson.fromJson(msg.toString(), tClass);
    }

    public <T> T parseMessage(String message, Class<T> tClass) {
        return gson.fromJson(message, tClass);
    }

    public WebSocket getWebSocket() {
        return webSocket;
    }

    public ReadAudioFileLoop getReadAudioFileLoop() {
        return readAudioFileLoop;
    }

    public AsrClientListener getAsrClientListener() {
        return asrClientListener;
    }

    public void release() {
        asrClientListener = null;
        webSocket.release();
    }

    class WebSocketListener implements WebSocket.WebSocketListener {
        @Override
        public void onConnected() {
            Logger.getDefault().info(TAG, "webSocket onConnected, state: " + stateToString(state));
            if (state == STATE_STARTING || state == STATE_RUNNING) {
                JSONObject jsonObject = mUpEventFactory.createStartEvent();
                webSocket.send(jsonObject);
                Logger.getDefault().info(TAG,"webSocket onConnected sendStartRecordMsg:" + jsonObject);
            }
        }

        @Override
        public void onClose() {
            Logger.getDefault().info(TAG,"webSocket onClose()");
        }

        @SuppressLint("MissingPermission")
        @Override
        public void onError(WebSocketException error) {
            Logger.getDefault().info(TAG,"webSocket onError(), error:" + error);
            readAudioFileLoop.pause();

            Object[] args = error.getArgs();
            if (args == null || args.length == 0) return;
            boolean errorCalled = false;
            if (args[0] instanceof JSONObject) {
                ErrorMsg errorMsg = parseDownEvent(args, ErrorMsg.class);
                if (errorMsg != null && AUTHENTICATION_ERROR.equals(errorMsg.getMessage())) {
                    asrClientListener.onError(error, AUTHENTICATION_ERROR, context.getString(R.string.joynote_token_fail));
                    errorCalled = true;
                }
            }
            if (!errorCalled) {
                asrClientListener.onError(error, WEBSOCKET_ERROR, context.getString(R.string.joynote_real_translate_socket_error));
            }
        }

        @Override
        public void onReconnecting(int attempts) {
            if (asrClientListener != null) {
                asrClientListener.onReconnecting();
            }
        }

        @Override
        public void onMessage(Object[] args) {
            if (args == null || args.length == 0) return;

            DownEvent downEvent = parseDownEvent(args, DownEvent.class);
            if (downEvent == null) {
                Logger.getDefault().info(TAG, "onMessage: downEvent is null");
                return;
            }
            for (DownEventHandler downEventHandler : downEventHandlers) {
                if (downEventHandler.handleDownEvent(downEvent)) {
                    downEventHandler.onDownEvent(AsrClient.this, downEvent);
                }
            }
        }
    }

    class OnReadAudioDataListener implements ReadAudioFileLoop.OnReadAudioDataCallback {
        @Override
        public void onReadAudioData(long index, byte[] data) {
            if (!webSocket.isActive() || !webSocket.isConnected()) {
                Logger.getDefault().info(TAG,"onReadAudioData webSocket is not connected");
                return;
            }
            UpEvent upEvent = mUpEventFactory.createAudioEvent(data, translate, (msg) -> gson.toJson(msg));

            webSocket.send(getEventJson(upEvent));
        }

        @Override
        public void onReadComplete() {
            Logger.getDefault().info(TAG, "onReadComplete, state: " + stateToString(state));
            if (state == STATE_FINISHING) {
                onFinish(true);
            }
        }
    }

    public interface AsrClientListener {

        void onStarting();

        void onStarted();

        void onText(TextMsg textMsg);

        void onPausing();

        void onPaused();

        void onReconnecting();

        void onError(Exception e, String errorType, String errorMsg);

        void onFinished(boolean success, int errorCode);
    }

    public static class Builder {
        String url;
        String channelId;
        String filePath;
        AudioRecorder audioRecorder;
        AsrClientListener listener;
        private Logger logger;
        private String translate;
        private Context context;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public Builder setChannelId(String channelId) {
            this.channelId = channelId;
            return this;
        }

        public Builder setFilePath(String filePath) {
            this.filePath = filePath;
            return this;
        }

        public Builder setListener(AsrClientListener listener) {
            this.listener = listener;
            return this;
        }

        public Builder setLogger(Logger logger) {
            this.logger = logger;
            return this;
        }

        public Builder setAudioRecorder(AudioRecorder audioRecorder) {
            this.audioRecorder = audioRecorder;
            return this;
        }

        public Builder setTranslate(String translate) {
            this.translate = translate;
            return this;
        }

        public AsrClient build() {
            if (logger != null) {
                Logger.setDefaultLogger(logger);
            }
            return new AsrClient(this);
        }
    }
}
