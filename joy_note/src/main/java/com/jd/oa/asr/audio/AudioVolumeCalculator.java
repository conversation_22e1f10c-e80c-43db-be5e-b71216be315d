package com.jd.oa.asr.audio;

import static java.lang.Math.log10;
import com.jd.oa.joy.note.record.AudioRecordUtil;

import java.math.BigDecimal;

 public abstract class AudioVolumeCalculator implements AudioRecorder.AudioDataProcessor {

    @Override
    public byte[] process(AudioRecorder recorder, int read, byte[] data) {
        try {
            int items = new BigDecimal(read).divide(new BigDecimal(2), 4).intValue();
            short[] s = AudioRecordUtil.INSTANCE.byteArray2ShortArray(data, items);
            long v = 0;
            double volume = 0;
            // 将 buffer 内容取出，进行平方和运算
            for (int i = 0; i < s.length; i++) {
                v += s[i] * s[i];
            }
            // 平方和除以数据总长度，得到音量大小。
            double mean = new BigDecimal(v).divide(new BigDecimal(items), 4).doubleValue();
            if (mean <= 0) {
                volume = 0;
            } else {
                volume = 10 * log10(mean);
            }
            int adjustVol = (int) Math.min(Math.max((volume - 36) * 2.1, 10.0), 100.0);

            onVolumeChanged((int) adjustVol);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    protected abstract void onVolumeChanged(int volume);
}
