package com.jd.oa.asr;

import static com.jd.oa.asr.websocket.model.EventName.DOWNSIDE_SEND_AUDIO;
import static com.jd.oa.asr.websocket.model.EventName.DOWNSIDE_START_NAME;
import static com.jd.oa.asr.websocket.model.EventName.DOWNSIDE_STOP_RECORD;
import static com.jd.oa.websocket.WebSocketTool.getEventJson;

import com.jd.oa.asr.websocket.model.SendMsg;
import com.jd.oa.asr.websocket.model.UpEvent;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.encrypt.Base64Encoder;

import org.json.JSONObject;

import java.util.function.Function;

public class UpEventFactory {

    private final String channelId;

    public UpEventFactory(String channelId) {
        if (channelId == null || channelId.isEmpty()) {
            throw new IllegalArgumentException("channelId is null or empty");
        }
        this.channelId = channelId;
    }

    public JSONObject createStartEvent() {
        UpEvent upEvent = new UpEvent();
        upEvent.setDeviceId(DeviceUtil.getDeviceUniqueId());
        upEvent.setEventName(DOWNSIDE_START_NAME);
        upEvent.setChannelID(channelId);
        JSONObject jsonObject = getEventJson(upEvent);
        return jsonObject;
    }

    public JSONObject createPauseEvent() {
        UpEvent upEvent = new UpEvent();
        upEvent.setDeviceId(DeviceUtil.getDeviceUniqueId());
        upEvent.setChannelID(channelId);
        upEvent.setEventName(DOWNSIDE_STOP_RECORD);
        JSONObject jsonObject = getEventJson(upEvent);
        return jsonObject;
    }

    public UpEvent createAudioEvent(byte[] data, String translate, Function<SendMsg, String> msgSerializer) {
        UpEvent upEvent = new UpEvent();
        upEvent.setDeviceId(DeviceUtil.getDeviceUniqueId());
        upEvent.setEventName(DOWNSIDE_SEND_AUDIO);
        upEvent.setChannelID(channelId);
        SendMsg sendMsg = new SendMsg();
        sendMsg.setDeviceId(DeviceUtil.getDeviceUniqueId());
        sendMsg.setTranslate(translate);
        sendMsg.id = upEvent.getId();
        sendMsg.audio = Base64Encoder.encode(data);
        upEvent.setMessage(msgSerializer.apply(sendMsg));
        return upEvent;
    }
}
