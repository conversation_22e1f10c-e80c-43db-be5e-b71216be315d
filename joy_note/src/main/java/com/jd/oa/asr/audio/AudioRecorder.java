package com.jd.oa.asr.audio;

import android.Manifest;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;

import androidx.annotation.RequiresPermission;

import com.jd.oa.asr.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/** @noinspection FieldMayBeFinal*/
public class AudioRecorder {
    private static final String TAG = "AudioRecorder";

    private static final int STATE_INIT = 0;
    private static final int STATE_RECORDING = 1;
    private static final int STATE_PAUSE = 2;
    private static final int STATE_STOP = 3;

    private Builder builder;
    private int bufferSize;
    private AudioRecord audioRecord;
    private String filePath;
    private OnAudioRecordListener onAudioRecordListener;
    private List<AudioDataProcessor> audioDataProcessors;

    private OutputStream audioOutputStream;

    private AtomicInteger state = new AtomicInteger(STATE_INIT);

    private RecordThread recordThread;

    private ReentrantReadWriteLock lock;

    public AudioRecorder(Builder builder) {
        this.builder = builder;
        this.onAudioRecordListener = builder.onAudioRecordListener;
        this.audioDataProcessors = builder.audioDataProcessors;
        this.filePath = builder.filePath;
        this.bufferSize = AudioRecord.getMinBufferSize(
                builder.sampleRateInHz,
                builder.channelConfig,
                builder.audioFormat
        );
    }

    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    public void start() {
        Logger.getDefault().info(TAG,"start(), " + "audioRecord: " + audioRecord + ", recordThread: " + recordThread);
        if (state.get() == STATE_RECORDING) return;

        if (audioRecord == null) {
            audioRecord = new AudioRecord(
                    builder.audioSource,
                    builder.sampleRateInHz,
                    builder.channelConfig,
                    builder.audioFormat,
                    bufferSize
            );
        }

        try {
            File file = new File(filePath);

            if (state.get() != STATE_PAUSE && file.exists()) {
                file.delete();
                Logger.getDefault().info(TAG,"start(), " + "file.delete(), state: " + state.get());
            }
            if (!file.exists()) {
                //file.mkdirs();
                file.createNewFile();
            }
            audioOutputStream = new FileOutputStream(file, state.get() == STATE_PAUSE);
        } catch (IOException e) {
            Logger.getDefault().error(TAG, "start error", e);
            throw new RuntimeException(e);
        }

        Logger.getDefault().info(TAG,"start(), " + "audioRecord state: " + audioRecord.getState());

        if (audioRecord.getState() != AudioRecord.RECORDSTATE_RECORDING) {
            audioRecord.startRecording();
        }
        state.set(STATE_RECORDING);

        if (recordThread == null || !recordThread.isAlive()) {
            recordThread  = new RecordThread();
            recordThread.start();
        }

        if (onAudioRecordListener != null) {
            onAudioRecordListener.onAudioRecordStart();
        }
    }

    public void pause() {
        Logger.getDefault().info(TAG,"pause(), audio record state: " + audioRecord.getState());

        try {
            if (audioRecord != null) {
                try {
                    audioRecord.stop();
                } catch (IllegalStateException e) {
                    Logger.getDefault().error(TAG, "pause error, audioRecord.stop()", e);
                }
            }
            state.set(STATE_PAUSE);
            onPause();
        } catch (IllegalStateException e) {
            Logger.getDefault().error(TAG, "pause error", e);
        }
    }

    private void onPause() {
        try {
            if (audioOutputStream != null) {
                audioOutputStream.flush();
            }
            if (onAudioRecordListener != null) {
                onAudioRecordListener.onAudioRecordPause();
            }
        } catch (Exception e) {
            if (onAudioRecordListener != null) {
                onAudioRecordListener.onAudioRecordError(new Exception("onPause error", e));
            }
        }
    }

    public void stop() {
        Logger.getDefault().info(TAG,"stop(), state: " + state.get());
        try {
            //audioRecord.stop();
            if (audioRecord != null) {
                audioRecord.release();
            }
            state.set(STATE_STOP);
            onComplete();
        } catch (Exception e) {
            Logger.getDefault().error(TAG, "stop error", e);
        }
    }

    private void onComplete() {
        try {
            if (audioOutputStream != null) {
                audioOutputStream.flush();
                audioOutputStream.close();
            }
            if (onAudioRecordListener != null) {
                onAudioRecordListener.onAudioRecordStop();
            }
        } catch (Exception e) {
            if (onAudioRecordListener != null) {
                onAudioRecordListener.onAudioRecordError(new Exception("onComplete error", e));
            }
        }
    }

    public int getBufferSize() {
        return bufferSize;
    }

    public AudioRecord getAudioRecord() {
        return audioRecord;
    }

    public void setLock(ReentrantReadWriteLock lock) {
        this.lock = lock;
    }

    class RecordThread extends Thread {
        @Override
        public void run() {
            super.run();
            Logger.getDefault().info(TAG,"RecordThread run, state: " + state.get() + ", audioRecord state: " + audioRecord.getState());

            int read = 0;
            byte[] data = new byte[bufferSize];
            while (state.get() == STATE_RECORDING) {
                if (audioRecord == null) {
                    if (onAudioRecordListener != null) {
                        onAudioRecordListener.onAudioRecordError(new Exception("AudioRecord is null"));
                    }
                    break;
                }
                if (audioRecord.getRecordingState() != AudioRecord.RECORDSTATE_RECORDING) {
                    break;
                }
                read = audioRecord.read(data, 0, bufferSize);

                if (AudioRecord.ERROR_INVALID_OPERATION != read) {
                    if (audioDataProcessors != null) {
                        byte[] result = data;
                        for (AudioDataProcessor audioDataProcessor : audioDataProcessors) {
                            result = audioDataProcessor.process(AudioRecorder.this, read, result);
                        }
                    }

                    try {
                        lock.writeLock().lock();

                        audioOutputStream.write(data);
                        audioOutputStream.flush();
                    } catch (Exception e) {
                        e.printStackTrace();
                        if (onAudioRecordListener != null) {
                            onAudioRecordListener.onAudioRecordError(e);
                        }
                    } finally {
                        if (lock.getWriteHoldCount() > 0) {
                            lock.writeLock().unlock();
                        }
                    }
                }
            }

            try {
                audioOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

            Logger.getDefault().info(TAG,"RecordThread exit, state: " + state.get() + ", audioRecord state: " + audioRecord.getState());
        }
    }

    public interface AudioDataProcessor {
        byte[] process(AudioRecorder recorder, int read, byte[] data);
    }

    public interface OnAudioRecordListener {

        void onAudioRecordStart();

        void onAudioRecordPause();

        void onAudioRecordStop();

        void onAudioRecordError(Exception throwable);
    }

    public static class Builder {
        private int audioSource = MediaRecorder.AudioSource.MIC;
        private int sampleRateInHz = 16000;
        private int channelConfig = AudioFormat.CHANNEL_IN_MONO;
        private int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
        private int maxDuration = Integer.MAX_VALUE;
        private String filePath;
        private OnAudioRecordListener onAudioRecordListener;
        private List<AudioDataProcessor> audioDataProcessors;

        public Builder setAudioSource(int audioSource) {
            this.audioSource = audioSource;
            return this;
        }

        public Builder setSampleRateInHz(int sampleRateInHz) {
            this.sampleRateInHz = sampleRateInHz;
            return this;
        }

        public Builder setChannelConfig(int channelConfig) {
            this.channelConfig = channelConfig;
            return this;
        }

        public Builder setAudioFormat(int audioFormat) {
            this.audioFormat = audioFormat;
            return this;
        }

        public Builder setFilePath(String filePath) {
            this.filePath = filePath;
            return this;
        }

        public Builder setOnAudioRecordListener(OnAudioRecordListener onAudioRecordListener) {
            this.onAudioRecordListener = onAudioRecordListener;
            return this;
        }

        public Builder addAudioDataProcessors(AudioDataProcessor audioDataProcessors) {
            if (this.audioDataProcessors == null) {
                this.audioDataProcessors = new ArrayList<>();
            }
            this.audioDataProcessors.add(audioDataProcessors);
            return this;
        }

        public AudioRecorder build() {
            return new AudioRecorder(this);
        }
    }
}
