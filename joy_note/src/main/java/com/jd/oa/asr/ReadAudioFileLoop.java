package com.jd.oa.asr;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;

import androidx.annotation.NonNull;


import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.Arrays;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class ReadAudioFileLoop {
    private static final String TAG = "TransferAudioFileLoop";

    private static final int STATE_INIT = 0;
    private static final int STATE_RUNNING = 1;
    private static final int STATE_PAUSE = 2;
    private static final int STATE_STOP = 3;

    private static final int MSG_READ_AUDIO = 1;
    private static final int SPEED_CHECK_LOOP = 10;

    private static final int MIN_INTERVAL = 50;
    private static final int MAX_INTERVAL = 200;
    private static final int READ_BLOCK_NUM = 4;

    private long blockSize = 6400;
    String filePath;
    long index = 0;

    private HandlerThread handlerThread;
    private RandomAccessFile randomAccessFile = null;
    byte[] buffer;
    private AsrClient client;
    private Handler handler;
    private int state = STATE_INIT;
    private OnReadAudioDataCallback onReadAudioDataCallback;
    private File file;

    private double interval = MAX_INTERVAL;

    private long lastFileSize = 0;

    private ReentrantReadWriteLock lock;

    public ReadAudioFileLoop(
            AsrClient client,
            String filePath,
            ReentrantReadWriteLock lock,
            int blockSize,
            OnReadAudioDataCallback onReadAudioDataCallback
    ) {
        this.client = client;
        this.onReadAudioDataCallback = onReadAudioDataCallback;
        this.blockSize = blockSize;
        this.filePath = filePath;
        this.lock = lock;
        this.file = new File(filePath);
        this.buffer = new byte[blockSize];
    }

    public void loop() {
        Logger.getDefault().info(TAG,"loop(), handlerThread: " + handlerThread);

        if (handlerThread == null || !handlerThread.isAlive()) {
            handlerThread = new HandlerThread("AudioFileReader");
            handlerThread.start();
            handler = new Handler(handlerThread.getLooper()) {
                @Override
                public void handleMessage(@NonNull Message msg) {
                    super.handleMessage(msg);
                    if (msg.what == MSG_READ_AUDIO) {
                        boolean complete = doReadAudioBlockLoop();
                        if (complete && (state == STATE_PAUSE || state == STATE_STOP)) {
                            Logger.getDefault().debug(TAG,"getAudioData complete" + ", index: " + index);

                            onStop(state == STATE_STOP);

                            onReadAudioDataCallback.onReadComplete();
                            return;
                        }
                        nextTick();
                    }
                }
            };
        }
        if (!handler.hasMessages(MSG_READ_AUDIO)) {
            handler.sendEmptyMessage(MSG_READ_AUDIO);
        }
        state = STATE_RUNNING;
    }

    private void nextTick() {
        if (handlerThread == null || !handlerThread.isAlive()) return;

        handler.sendEmptyMessageDelayed(MSG_READ_AUDIO, (long) interval);
    }

    public void pause() {
        state = STATE_PAUSE;
        if (handler != null) {
            handler.removeMessages(MSG_READ_AUDIO);
        }
    }

    public void stop() {
        state = STATE_STOP;
    }

    private void onStop(boolean resetIndex) {
        Logger.getDefault().info(TAG,"stop()");

        handler.removeMessages(MSG_READ_AUDIO);
        handlerThread.quitSafely();
        if (randomAccessFile != null) {
            try {
                randomAccessFile.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            randomAccessFile = null;
        }
        if (resetIndex) {
            index = 0;
        }
    }

    private boolean doReadAudioBlockLoop() {
        clearBuffer();

//        int state = client.getUpEventStateMap().getState(index);
//        if (state == UpEventStateMap.UpEventState.STATE_RECEIVED) {
//            index++;
//            return false;
//        }
        long fileSize = file.length();
        if (fileSize == 0) return false;

        if (index % SPEED_CHECK_LOOP == 0) {
            if ((index + READ_BLOCK_NUM) * blockSize < fileSize) {
                interval = MIN_INTERVAL;
            } else {
                double rate = ((double) (fileSize - lastFileSize)) / (blockSize * SPEED_CHECK_LOOP);
                double newInterval = interval / rate;

                this.interval = Math.min(Math.max(newInterval, MIN_INTERVAL), MAX_INTERVAL);
                this.lastFileSize = fileSize;
            }
            Logger.getDefault().verbose(TAG,"doReadAudioBlockLoop(), interval: " + interval + ", distance: " + 1d * (fileSize - index * blockSize) / blockSize);
        }

        int readLength = readIndexData(index, buffer);
        if (readLength == -1) return true;

        boolean complete = readLength < blockSize;

        byte[] dataEnd = null;
        if (readLength < blockSize) {
            dataEnd = new byte[readLength];
            System.arraycopy(buffer, 0, dataEnd, 0, readLength);
        }

        onReadAudioDataCallback.onReadAudioData(index, dataEnd == null ? buffer : dataEnd);
        index++;
        return complete;
    }

    private void clearBuffer() {
        Arrays.fill(buffer, (byte) 0);
    }

    private int readIndexData(long index, byte[] data) {
        Lock readLock = lock.readLock();
        readLock.lock();
        try {
            if (randomAccessFile == null) {
                if (index < 0 || !file.exists()) {
                    return -1;
                }
                randomAccessFile = new RandomAccessFile(file, "r");
            }
            if ((index + 1) * this.blockSize > file.length()) {
                return -1;
            }

            randomAccessFile.seek(index * this.blockSize);
            int readLength = randomAccessFile.read(data);

            //Logger.getDefault().verbose(TAG,"readIndexData(),file length: " + randomAccessFile.length() + " index: " + index + ", seek: " + index * this.blockSize + " readLength: " + readLength);

            return readLength;
        } catch (Exception e) {
            e.printStackTrace();
            closeFile();
        } finally {
            if(lock.getReadHoldCount() > 0) {
                readLock.unlock();
            }
        }
        return -1;
    }

    private void closeFile() {
        if (randomAccessFile != null) {
            try {
                randomAccessFile.close();
                randomAccessFile = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public interface OnReadAudioDataCallback {
        void onReadAudioData(long index, byte[] data);
        void onReadComplete();
    }
}
