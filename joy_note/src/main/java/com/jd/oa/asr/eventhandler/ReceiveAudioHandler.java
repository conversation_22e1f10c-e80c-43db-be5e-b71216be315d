package com.jd.oa.asr.eventhandler;

import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.EventName;
import com.jd.oa.asr.websocket.model.ReceiveMsg;

public class ReceiveAudioHand<PERSON> extends EventNameDownEventHandler {

    public ReceiveAudioHandler() {
        super(EventName.UPSIDE_RECEIVE_AUDIO);
    }

    @Override
    public void onDownEvent(AsrClient client, DownEvent downEvent) {
        ReceiveMsg receiveMsg = client.parseMessage(downEvent.getMessage(), ReceiveMsg.class);
        //client.getUpEventStateMap().setState(receiveMsg.sendAudioId, UpEventStateMap.UpEventState.STATE_RECEIVED);
    }
}
