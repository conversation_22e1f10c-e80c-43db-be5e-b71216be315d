package com.jd.oa.asr;

import android.util.Log;

import com.jd.oa.joy.note.BuildConfig;

public abstract class Logger {

    static Logger defaultLogger;

    public static Logger getDefault() {
        if (defaultLogger == null) {
            defaultLogger = new DefaultLogger();
        }
        return defaultLogger;
    }

    public static void setDefaultLogger(Logger defaultLogger) {
        Logger.defaultLogger = defaultLogger;
    }

    public abstract void verbose(String tag, String msg);

    public abstract void debug(String tag, String msg);

    public abstract void info(String tag, String msg);

    public abstract void warn(String tag, String msg);

    public abstract void error(String tag, String msg, Throwable e);

    public static class DefaultLogger extends Logger {

        private static final String TAG = "AsrClient-";

        @Override
        public void verbose(String tag, String msg) {
            if (BuildConfig.DEBUG) {
                Log.v(TAG + tag, msg);
            }
        }

        @Override
        public void debug(String tag, String msg) {
            Log.d(TAG + tag, msg);

        }

        @Override
        public void info(String tag, String msg) {
            Log.i(TAG +tag, msg);
        }

        @Override
        public void warn(String tag, String msg) {
            Log.w(TAG +tag, msg);
        }

        @Override
        public void error(String tag, String msg, Throwable e) {
            Log.e(TAG +tag, msg, e);
        }
    }
}
