package com.jd.oa.asr.eventhandler;

import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.Logger;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.EventName;

public class StopRecordHandler extends EventNameDownEventHandler {
    private static final String TAG = "StopRecordHandler";

    public StopRecordHandler() {
        super(EventName.UPSIDE_STOPPED_RECORD);
    }

    @Override
    public void onDownEvent(AsrClient client, DownEvent downEvent) {
        super.onDownEvent(client, downEvent);
        client.getWebSocket().close();
        client.setState(AsrClient.STATE_PAUSED);

        AsrClient.AsrClientListener listener = client.getAsrClientListener();
        if (listener != null) {
            client.getAsrClientListener().onPaused();
        }
    }
}