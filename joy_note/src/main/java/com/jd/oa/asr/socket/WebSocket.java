package com.jd.oa.asr.socket;

import android.net.Uri;
import android.util.Log;

import com.jd.oa.asr.Logger;

import java.net.URISyntaxException;
import java.util.Arrays;

import io.socket.client.IO;
import io.socket.client.Manager;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;

public class WebSocket {
    private static final String TAG = "WebSocket";
    static final String EVENT_MESSAGE = "message";

    private Builder builder;

    private Socket socket;

    WebSocket(Builder builder) {
        this.builder = builder;
        this.socket = createSocket();
        if (socket != null) {
            listenEvent(socket);
        }
    }

    private Socket createSocket() {
        Logger.getDefault().info(TAG,"createSocket, url:"  + builder.url);

        Uri uri = Uri.parse(builder.url);
        IO.Options options = IO.Options.builder()
                .setForceNew(false)
                .setMultiplex(true)
                .setPath(uri.getPath())
                .setQuery(uri.getQuery())
                .setReconnection(true)
                .setReconnectionAttempts(Integer.MAX_VALUE)
                .setReconnectionDelay(1_000)
                .setReconnectionDelayMax(2_000)
                .setRandomizationFactor(0.5)
                //.setTimeout(builder.timeout)
                .setAuth(null)
                .setTransports(new String[]{io.socket.engineio.client.transports.WebSocket.NAME})
                .build();
        try {
            return IO.socket(uri.getScheme() + "://" + uri.getHost(), options);
        } catch (URISyntaxException e) {
            Logger.getDefault().error(TAG,"IO.socket, url:"  + builder.url, e);
            return null;
        }
    }

    private void listenEvent(Socket socket) {
        socket.on(Socket.EVENT_CONNECT, args -> {
            Logger.getDefault().info(TAG,Socket.EVENT_CONNECT + ", args:" + Arrays.toString(args));
            if (builder.webSocketListener != null) {
                builder.webSocketListener.onConnected();
            }
        });
        socket.on(Socket.EVENT_DISCONNECT, args -> {
            Logger.getDefault().info(TAG,Socket.EVENT_DISCONNECT + ", args:" + Arrays.toString(args));
            if (builder.webSocketListener != null) {
                builder.webSocketListener.onClose();
            }
        });
        socket.on(EVENT_MESSAGE, args -> {
            //Logger.getDefault().verbose(TAG, EVENT_MESSAGE + ", args:" + Arrays.toString(args));
            if (builder.webSocketListener != null) {
                builder.webSocketListener.onMessage(args);
            }
        });
        socket.on(Socket.EVENT_CONNECT_ERROR, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Logger.getDefault().info(TAG,Socket.EVENT_CONNECT_ERROR + ", args:" + Arrays.toString(args));

                WebSocketException error = new WebSocketException();
                error.setArgs(args);
                if (builder.webSocketListener != null) {
                    builder.webSocketListener.onError(error);
                }
            }
        });
        socket.io().on(Manager.EVENT_RECONNECT_ATTEMPT, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Logger.getDefault().info(TAG,Manager.EVENT_RECONNECT_ATTEMPT + ", args:" + Arrays.toString(args));
                int attempts = (int) args[0];
                if (builder.webSocketListener != null) {
                    builder.webSocketListener.onReconnecting(attempts);
                }
            }
        });
//        socket.io().on(Manager.EVENT_RECONNECT, new Emitter.Listener() {
//            @Override
//            public void call(Object... args) {
//                Log.d(TAG, "call: ");
//            }
//        });
    }

    public void connect() {
        Logger.getDefault().info(TAG,"connect(), connected:" + socket.connected());
        if (socket == null || socket.connected()) return;
        socket.connect();
    }

    public void close() {
        Logger.getDefault().info(TAG,"close()");
        if (socket == null) return;

        socket.disconnect();
    }

    public void release() {
        builder.setOnStateChangeListener(null);
    }

    public void send(final Object... args) {
        socket.send(args);
    }

    public boolean isConnected() {
        return socket.connected();
    }

    public boolean isActive() {
        return socket.isActive();
    }

    public static class Builder {
        private String url;
        private long timeout = 10_000;
        private WebSocketListener webSocketListener;

        public Builder() {
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public Builder setTimeout(long timeout) {
            this.timeout = timeout;
            return this;
        }

        public Builder setOnStateChangeListener(WebSocketListener webSocketListener) {
            this.webSocketListener = webSocketListener;
            return this;
        }

        public WebSocket build() {
            return new WebSocket(this);
        }
    }

    public interface WebSocketListener {

        void onConnected();

        void onClose();

        void onError(WebSocketException error);

        void onReconnecting(int attempts);

        void onMessage(Object[] args);
    }
}

