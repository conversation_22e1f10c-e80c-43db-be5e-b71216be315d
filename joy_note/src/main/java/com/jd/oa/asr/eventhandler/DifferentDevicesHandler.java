package com.jd.oa.asr.eventhandler;

import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.exception.AsrException;
import com.jd.oa.asr.websocket.model.DownEvent;
import com.jd.oa.asr.websocket.model.EventName;
import com.jd.oa.joy.note.R;

public class DifferentDevicesHandler extends EventNameDownEventHandler {

    public DifferentDevicesHandler() {
        super(EventName.DIFFERENT_DEVICES);
    }

    @Override
    public void onDownEvent(AsrClient client, DownEvent downEvent) {
        super.onDownEvent(client, downEvent);
        AsrClient.AsrClientListener listener = client.getAsrClientListener();
        if (listener != null) {
            client.getAsrClientListener().onError(new AsrException(), downEvent.getEventName(), client.getContext().getString(R.string.joynote_audio_other_is_recording_tips));
        }
        client.forceStop();
    }
}
