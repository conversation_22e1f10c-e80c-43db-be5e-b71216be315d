## This file is automatically generated by Android Studio.
# Do not modify this file -- YOUR CHANGES WILL BE ERASED!
#
# This file must *NOT* be checked into Version Control Systems,
# as it contains information specific to your local configuration.
#
# Location of the SDK. This is only used by <PERSON>radle.
# For customization when using a Version Control System, please read the
# header note.
#Wed Jul 26 16:57:41 CST 2017

org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.configuration-cache=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.configuration-cache.problems=warn
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Djava.net.preferIPv4Stack=true -XX:+UseParallelGC


#Tinker
TINKER_VERSION = 1.8.1
#Tinker
TINKER_BASE_APK_NAME = apk

## for zoom\u89C6\u9891\u96C6\u6210
#android.enableAapt2=false

#apmsInstrumentationEnabled=false
#android.experimental.legacyTransform.forceNonIncremental=true

# for timline Debug
timlineDebug=false
timlineSaas=false
isDebug=false
artifactory_user=wangtao26
#http://artifactory.jd.com/webapp apk key
artifactory_password=AKCp5Zk9KL16H26MFVuKquBcRGdtJW8uRgyYKRSQ4iBGsfAZg4eACzzr4XR4mnw17ebJGdi5B
#jd
artifactory_contextUrl=http://artifactory.jd.com
#groupId
artifactory_group=com.jd.cdyjy
#http://cf.jd.com/pages/viewpage.action?pageId=52396429
#jar
SNAPSHOT_REPOSITORY_KEY=libs-snapshots-local
#jar
RELEASE_REPOSITORY_KEY=libs-releases-local
android.injected.testOnly=false

#\u4E3Bapp\u91CC\u7F16\u8BD1appcenter\u6A21\u5757
build_appcenter=true
#\u4E3Bapp\u91CC\u7F16\u8BD1\u5458\u5DE5\u51FA\u884C\u6A21\u5757
build_use_car=true
#\u4E3Bapp\u91CC\u7F16\u8BD1\u5DE5\u4F5C\u53F0\u6A21\u5757
build_workbench=true
#\u4E3Bapp\u91CC\u7F16\u8BD1IM\u6A21\u5757
build_im_dd=true

flutterLocalDebug=false
FLUTTE_ENGINE_ID=1837b5be5f0f1376a1ccf383950e83a80177fb4e

## Android \u63D2\u4EF6\u4F1A\u4F7F\u7528\u5BF9\u5E94\u7684 AndroidX \u5E93\u800C\u975E\u652F\u6301\u5E93\u3002
android.useAndroidX=true
## Android \u63D2\u4EF6\u4F1A\u901A\u8FC7\u91CD\u5199\u73B0\u6709\u7B2C\u4E09\u65B9\u5E93\u7684\u4E8C\u8FDB\u5236\u6587\u4EF6\uFF0C\u81EA\u52A8\u5C06\u8FD9\u4E9B\u5E93\u8FC1\u79FB\u4E3A\u4F7F\u7528 AndroidX\u3002
android.enableJetifier=true

#joyMeeting\u9002\u914DAndroidx\u65F6\u6253\u5305\u5931\u8D25\u5904\u7406
android.enableD8.desugaring =true
android.useDexArchive = true

#android.enableR8.libraries=false
#android.enableR8=false
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# ????SDK Version, ???????????
VIDEO_MEETING_VERSION=


####################################################################################################
# project run mode
isAppModule = false
isRunLoginAlone = false
####################################################################################################
# configs for me app
####################################################################################################
#Release
STORE_FILE = ../key/jd.keystore
STORE_PASS = pass
KEY_ALIAS = alias
KEY_PASS = pass


ME_VERSION_CODE = 245
ME_VERSION_NAME = 7.10.9
APP_NAME=JoyME
BUILD_ENV=
BUILD_TIME=
SHOW_SERVER_SWITCHER = false
GIT_BRANCH_TAG = br
DEBUGGABLE=true
Build_Version=local
CHANNEL_NAME = jdme
Custom_Build_Version=
####################################################################################################
# configs for saas app
####################################################################################################
#Release placeholder
SAAS_STORE_FILE = ../key/jdmesaas.keystore
SAAS_STORE_PASS = jdme35711saas
SAAS_KEY_ALIAS = jdmesaas
SAAS_KEY_PASS = jdme35711saas


SAAS_VERSION_CODE = 2
SAAS_VERSION_NAME = 0.1.40
SAAS_APP_NAME=JoyWE
SAAS_BUILD_ENV=
SAAS_BUILD_TIME=
SAAS_SHOW_SERVER_SWITCHER = false
SAAS_GIT_BRANCH_TAG = br
SAAS_DEBUGGABLE=true
SAAS_Build_Version=local
SAAS_CHANNEL_NAME = jdwe
SAAS_Custom_Build_Version=
####################################################################################################

# ============================================
# == Demo Project Dependency Configuration ==
# ============================================
# \u8BBE\u7F6E\u4E3Atrue\u65F6\u4F7F\u7528\u672C\u5730includeBuild\u65B9\u5F0F\uFF0C\u4FBF\u4E8E\u5F00\u53D1\u8C03\u8BD5
# \u8BBE\u7F6E\u4E3Afalse\u65F6\u4F7F\u7528Maven\u4F9D\u8D56\u65B9\u5F0F\uFF0C\u9002\u7528\u4E8E\u751F\u4EA7\u53D1\u5E03
USE_LOCAL_DEMO_PROJECT=false
####################################################################################################

