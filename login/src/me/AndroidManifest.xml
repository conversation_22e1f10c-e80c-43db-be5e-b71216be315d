<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <application
        tools:replace="android:supportsRtl,android:allowBackup,android:label,android:theme"
        android:allowBackup="false"
        android:supportsRtl="true"
        android:label="login"
        android:name="com.jd.oa.business.LoginApp"
        android:theme="@style/Theme.AppCompat.Light">
        <activity
            android:name="com.jd.oa.business.login.controller.LoginActivity"
            android:screenOrientation="portrait"
            android:label="@string/me_app_name"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode|smallestScreenSize"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <meta-data
            android:name="FLAVOR"
            android:value="${FLAVOR}" />
    </application>


</manifest>