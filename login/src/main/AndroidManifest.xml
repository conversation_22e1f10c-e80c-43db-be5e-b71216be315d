<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>

        <activity
            android:name="com.jd.oa.business.loginauth.LoginAuthActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!-- 登录 -->
        <activity android:name="com.jd.oa.business.login.controller.TabletLoginActivity" android:screenOrientation="portrait"/>
        <activity
            android:name="com.jd.oa.business.login.controller.LoginActivity"
            android:screenOrientation="portrait"
            android:label="@string/me_app_name"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode|smallestScreenSize"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize" />
        <!-- 新刷脸 -->
        <activity
            android:name="com.jd.oa.business.liveness_new.LivenessNewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!--刷脸打卡-->
<!--        <activity-->
<!--            android:name="com.jd.oa.business.liveness.LivenessClockInActivity"-->
<!--            android:screenOrientation="portrait" />-->

        <activity android:name="com.jd.oa.business.LoginActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode|smallestScreenSize"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />

        <activity
            android:name="com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/NoAnimTheme"/>

        <activity android:name="com.jd.oa.business.jdsaaslogin.ui.success.LoginSuccessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name="com.jd.oa.business.jdsaaslogin.ui.scan.LoginScanActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

    </application>

</manifest>