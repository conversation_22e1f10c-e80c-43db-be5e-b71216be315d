<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5">

    <com.jd.oa.ui.FrameView
        android:id="@+id/me_frameView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.jd.oa.ui.SettingActionbar
                android:id="@+id/actionbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_marginTop="12dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/me_top_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="24dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/jdme_liveness_face" />

                <TextView
                    android:id="@+id/me_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingTop="@dimen/setting_item_padding_vertical"
                    android:paddingBottom="@dimen/setting_item_padding_vertical"
                    android:text="@string/me_agree_liveness_protocol"
                    android:textColor="@color/me_setting_foreground"
                    android:textSize="16dp"
                    android:visibility="visible" />

            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@drawable/jdme_setting_divider" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/me_container_repeat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_ripple_white_top_corner8"
                    android:padding="16dp"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:text="@string/me_face_shot"
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="16dp" />

                    <TextView
                        android:id="@+id/tv_repeat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:drawableEnd="@drawable/jdme_icon_arrow_right_gray"
                        android:drawablePadding="4dp"
                        android:text="@string/me_shot_again"
                        android:textColor="@color/me_setting_foreground_light"
                        android:textSize="16dp" />

                </RelativeLayout>

                <FrameLayout
                    android:id="@+id/divider"
                    android:layout_width="match_parent"
                    android:background="@color/white"
                    android:layout_height="wrap_content">

                    <View
                        android:background="#E6E6E6"
                        android:layout_width="match_parent"
                        android:layout_marginStart="16dp"
                        android:layout_height="1px" />

                </FrameLayout>

                <!--刷脸登录-->
                <RelativeLayout
                    android:id="@+id/me_rl_login"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:background="@drawable/jdme_ripple_white_bottom_corner8"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:text="@string/me_liveness_login"
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="16dp" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/me_switch_liveness"
                        style="@style/SwitchStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true" />

                </RelativeLayout>

<!--刷脸打卡
                <RelativeLayout
                    android:id="@+id/me_rl_clock_in"
                    style="@style/set_container_no_divide"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="0dp"
                    android:paddingTop="@dimen/setting_item_padding_vertical"
                    android:paddingEnd="0dp"
                    android:paddingBottom="@dimen/setting_item_padding_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/setting_item_padding_horizontal"
                        android:text="@string/me_liveness_clock_in"
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="@dimen/setting_name_text_size" />

                    <android.support.v7.widget.SwitchCompat
                        android:id="@+id/me_switch_clock_in"
                        style="@style/SwitchStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/setting_item_padding_horizontal" />

                </RelativeLayout>
-->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/me_setting_background" />
            </androidx.appcompat.widget.LinearLayoutCompat>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </com.jd.oa.ui.FrameView>
</ScrollView>