<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <include layout="@layout/jdsaas_login_bg_with_gradient"/>

    <include
        android:id="@+id/titleBar"
        layout="@layout/jdsaas_login_titlebar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_team"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/titleBar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="8dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>