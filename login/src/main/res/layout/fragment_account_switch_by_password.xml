<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <include layout="@layout/jdsaas_login_bg_with_gradient"/>

    <include
        android:id="@+id/titleBar"
        layout="@layout/jdsaas_login_titlebar" />


    <TextView
        android:id="@+id/tv_password_tip1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_login_account_login"
        android:textColor="@color/jdsaas_black"
        android:textSize="@dimen/jdsaas_text_size_20"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar"
        android:layout_marginTop="8dp"
        android:layout_marginStart="22dp"
        android:layout_marginEnd="22dp"/>

    <TextView
        android:id="@+id/tv_password_tip2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:hint="@string/jdsaas_login_dialog_password_tip1"
        android:textColor="@color/black"
        android:textSize="@dimen/jdsaas_text_size_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_password_tip1"
        android:layout_marginTop="4dp"
        android:layout_marginStart="22dp"
        android:layout_marginEnd="22dp"/>

    <RelativeLayout
        android:id="@+id/rl_password"
        android:layout_width="match_parent"
        android:layout_height="@dimen/jdsaas_login_edittext_height"
        android:layout_marginTop="16dp"
        android:paddingLeft="22dp"
        android:paddingRight="22dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_password_tip2">

        <EditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@null"
            android:hint="@string/jdsaas_login_password_hint"
            android:inputType="textPassword"
            android:textColor="@color/jdsaas_black_1B1B1B"
            android:textColorHint="@color/jdsaas_login_hint_color"
            android:textSize="@dimen/jdsaas_text_size_14" />

        <CheckBox
            android:id="@+id/cb_password"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:button="@null"
            android:background="@drawable/jdsaas_eye_selector"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true" />

        <ImageView
            android:id="@+id/iv_password_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/jdme_icon_delete"
            android:layout_toLeftOf="@+id/cb_password"
            android:layout_marginRight="15dp"
            android:layout_centerVertical="true"
            android:visibility="gone"
            tools:visibility="visible"/>
        <View
            android:id="@+id/view_password_line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/jdsaas_black_DCDEE0"
            android:layout_alignParentBottom="true"/>
    </RelativeLayout>
    <TextView
        android:id="@+id/tv_forget_password"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_login_password_forget"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/jdsaas_black_6A6A6A"
        app:layout_constraintTop_toBottomOf="@+id/rl_password"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="8dp"
        android:layout_marginRight="22dp"/>

    <Button
        android:id="@+id/btn_password_next"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_login_next"
        android:textColor="@color/white"
        android:layout_gravity="bottom"
        android:background="@drawable/jdsaas_selector_button_rounde_corner"
        android:enabled="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_forget_password"
        android:layout_marginTop="175dp"
        android:layout_marginStart="26dp"
        android:layout_marginEnd="26dp"/>

    <TextView
        android:id="@+id/tv_login_type_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_password_next"
        android:text="切换到手机验证码登录"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:layout_marginTop="12dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>