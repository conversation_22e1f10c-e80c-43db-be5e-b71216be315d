<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="24dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_team_choice_tip1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/jdsaas_login_team_choice_tip1"
        android:textSize="@dimen/jdsaas_text_size_20"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:layout_marginTop="8dp"
        android:paddingLeft="22dp"
        android:paddingRight="22dp"/>

    <TextView
        android:id="@+id/tv_team_choice_tip2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/jdsaas_login_team_choice_tip2"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/jdsaas_black"
        app:layout_constraintTop_toBottomOf="@+id/tv_team_choice_tip1"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="4dp"
        android:paddingLeft="22dp"
        android:paddingRight="22dp"/>

</LinearLayout>