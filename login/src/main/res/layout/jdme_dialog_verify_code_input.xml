<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/me_tv_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="9dp"
        android:layout_marginRight="8dip"
        android:layout_marginTop="20dip"
        android:text="@string/me_identifying_code"
        android:textColor="@color/black_main_summary"
        android:textSize="16sp" />

    <!-- 短信验证码布局 -->
    <LinearLayout
        android:id="@+id/msg_layout"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="12dp"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/me_et_code"
            android:layout_width="0dp"
            android:layout_height="@dimen/me_edit_height"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_edittext_shualian"
            android:gravity="center_vertical|left"
            android:hint="@string/me_message_identifying_code"
            android:inputType="number"
            android:maxLines="1"
            android:paddingLeft="15dp"
            android:textColor="@color/black_edit"
            android:textColorHint="@color/black_edit_hit"
            android:textSize="15sp" />
        <!-- 短信验证码 -->

        <TextView
            android:id="@+id/me_tv_get_code"
            android:layout_width="120dp"
            android:layout_height="@dimen/me_edit_height"
            android:layout_gravity="center"
            android:background="@drawable/jdme_selector_button_default_shualian"
            android:gravity="center"
            android:text="@string/me_msg_code"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>

</LinearLayout>