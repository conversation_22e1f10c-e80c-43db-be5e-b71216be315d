<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="top"
        android:orientation="vertical" />

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_animation_img"
        android:layout_width="2dip"
        android:layout_height="2dip"
        android:layout_marginTop="72dip"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:src="@color/skin_color_default"
        android:visibility="invisible" />

</androidx.constraintlayout.widget.ConstraintLayout>