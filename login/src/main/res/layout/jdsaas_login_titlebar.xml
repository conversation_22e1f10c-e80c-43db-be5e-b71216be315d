<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_direction_left"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_22"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:paddingLeft="16dp"
        android:paddingRight="8dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/jdsaas_text_size_16"
        android:textColor="@color/black"
        app:layout_constraintLeft_toRightOf="@+id/iv_back"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>