<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <requestFocus />

    <com.jd.oa.ui.FrameView
        android:id="@+id/me_frameView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/me_img_verification"
                android:layout_width="132dp"
                android:layout_height="145dp"
                android:layout_marginTop="29dp"
                android:src="@drawable/jdme_liveness_sample"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/me_tv_verification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="20dp"
                android:gravity="center"
                android:text="@string/me_for_account_safe_with_liveness_login"
                android:textColor="@color/me_login_input_bg"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/me_img_verification" />

            <LinearLayout
                android:id="@+id/me_ll_verification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="26dp"
                android:layout_marginEnd="30dp"
                android:background="@drawable/me_login_input_bg"
                android:gravity="center_vertical"
                android:padding="1dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/me_tv_verification">

                <TextView
                    android:id="@+id/tv_phone_code"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/me_edit_height"
                    android:layout_marginStart="20dp"
                    android:layout_weight="2"
                    android:background="@color/white"
                    android:gravity="center"
                    android:paddingStart="0dp"
                    android:paddingEnd="5dp"
                    android:textColor="@color/black_first"
                    android:textSize="16sp" />

                <View
                    android:layout_width="1px"
                    android:layout_height="22dp"
                    android:background="@color/me_login_input_bg" />

                <EditText
                    android:id="@+id/edit_phone"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/me_edit_height"
                    android:layout_marginTop="0dp"
                    android:layout_marginEnd="0dp"
                    android:layout_weight="5"
                    android:background="@color/white"
                    android:focusable="false"
                    android:gravity="center"
                    android:inputType="phone"
                    android:paddingStart="6dp"
                    android:paddingEnd="5dp"
                    android:singleLine="true"
                    android:textColor="@color/black_first"
                    android:textColorHint="@color/black_edit_hit"
                    android:textCursorDrawable="@drawable/jdme_edit_cursor"
                    android:textSize="16sp"
                    tools:ignore="LabelFor" />

                <TextView
                    android:id="@+id/me_change_phone"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/me_edit_height"
                    android:layout_gravity="center"
                    android:layout_marginEnd="24dp"
                    android:layout_weight="4"
                    android:gravity="end|center_vertical"
                    android:text="@string/me_modify_phone"
                    android:textColor="@color/me_login_tv_bt"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/me_ll_verification_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="30dp"
                android:background="@drawable/me_login_input_bg"
                android:gravity="center_vertical"
                android:padding="1dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/me_ll_verification">

                <EditText
                    android:id="@+id/me_et_code"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/me_edit_height"
                    android:layout_marginStart="24dp"
                    android:layout_weight="7"
                    android:background="@color/white"
                    android:hint="@string/me_msg_code_hint"
                    android:inputType="number"
                    android:singleLine="true"
                    android:textColor="@color/black_first"
                    android:textColorHint="@color/me_app_workbench_approval_light_text"
                    android:textCursorDrawable="@drawable/jdme_edit_cursor"
                    android:textSize="14sp" />
                <!-- 短信验证码 -->


                <View
                    android:layout_width="1px"
                    android:layout_height="22dp"
                    android:background="@color/me_login_input_bg" />

                <TextView
                    android:id="@+id/me_tv_get_code"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/me_edit_height"
                    android:layout_gravity="center"
                    android:layout_marginEnd="24dp"
                    android:layout_weight="4"
                    android:gravity="end|center_vertical"
                    android:text="@string/me_msg_code"
                    android:textColor="@color/me_login_tv_bt"
                    android:textSize="14sp" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.jd.oa.ui.FrameView>
</ScrollView>