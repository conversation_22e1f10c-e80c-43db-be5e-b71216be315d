<?xml version="1.0" encoding="utf-8"?>

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal">

        <TextView
            android:id="@+id/me_login_locale"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:padding="8dp"
            android:text="@string/me_language_locale"
            android:textColor="@color/skin_color_default"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/me_login_liveness"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:padding="8dp"
            android:text="@string/me_liveness_login"
            android:textColor="@color/skin_color_default"
            android:textSize="14sp" />

        <!--margintop:32 -->
        <com.jd.oa.ui.CircleImageView
            android:id="@id/me_iv_circle"
            android:layout_width="70dip"
            android:layout_height="70dip"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="32dp"
            android:src="@drawable/jdme_picture_user_default" />
        <TextView
            android:id="@+id/jdme_tv_debug"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_below="@id/me_iv_circle"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:textSize="12sp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="debug 4.5.1"/>
        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:layout_below="@+id/me_iv_circle"
            android:orientation="vertical">

            <!-- 登陆框		start -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="75dip"
                android:layout_marginLeft="16dip"
                android:layout_marginRight="16dip"
                android:layout_marginTop="32dip"
                android:background="@drawable/jdme_login_input_bg"
                android:orientation="vertical">

                <com.jd.oa.ui.ClearEditText
                    android:id="@+id/et_username"
                    android:layout_width="match_parent"
                    android:layout_height="37dp"
                    android:background="@null"
                    android:drawableLeft="@drawable/jdme_icon_user_default"
                    android:drawablePadding="14dp"
                    android:hint="@string/me_erp_account"
                    android:inputType="textEmailAddress"
                    android:paddingLeft="14dp"
                    android:paddingRight="4dp"
                    android:singleLine="true"
                    android:textColor="@color/black_edit"
                    android:textColorHint="@color/black_edit_hit"
                    android:textCursorDrawable="@drawable/jdme_edit_cursor"
                    android:textSize="15sp" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:src="@drawable/jdme_icon_pwd_default"
                        android:layout_marginRight="14dp"
                        android:layout_marginLeft="14dp"/>
                    <com.jd.oa.ui.ClearEditText
                        android:id="@+id/et_pwd"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="37dp"
                        android:background="@null"
                        android:hint="@string/me_erp_pwd"
                        android:imeOptions="actionDone"
                        android:inputType="textPassword"
                        android:paddingRight="15dp"
                        android:singleLine="true"
                        android:textColor="@color/black_edit"
                        android:textColorHint="@color/black_edit_hit"
                        android:textCursorDrawable="@drawable/jdme_edit_cursor"
                        android:textSize="15sp" />
                    <CheckBox
                        android:id="@+id/cbPwd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:button="@drawable/jdme_eye_selector"
                        android:layout_marginRight="15dp"
                        />
                </LinearLayout>

            </LinearLayout>

            <!-- 短信验证码布局 -->
            <LinearLayout
                android:id="@+id/msg_layout"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dip"
                android:layout_marginRight="16dip"
                android:layout_marginTop="16dip"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <EditText
                    android:id="@+id/tv_msg"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/me_warn_button_height"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_login_msgcode_input"
                    android:gravity="center"
                    android:hint="@string/me_msg_code_hint"
                    android:inputType="number"
                    android:singleLine="true"
                    android:textColor="@color/black_edit"
                    android:textColorHint="@color/black_edit_hit"
                    android:textCursorDrawable="@drawable/jdme_edit_cursor"
                    android:textSize="15sp" />
                <!-- 短信验证码 -->

                <TextView
                    android:id="@+id/tv_msg_valid"
                    android:layout_width="120dp"
                    android:layout_height="@dimen/me_warn_button_height"
                    android:layout_gravity="center"
                    android:background="@drawable/jdme_selector_button_default"
                    android:gravity="center"
                    android:text="@string/me_msg_code"
                    android:textColor="@color/white"
                    android:textSize="15sp" />
            </LinearLayout>

            <!-- 登录提示信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingTop="2dp"
                android:paddingBottom="2dp">

                <TextView
                    android:id="@+id/tv_login_tip"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dip"
                    android:layout_marginRight="16dip"
                    android:layout_weight="1"
                    android:gravity="left|center_vertical"
                    android:text=""
                    android:textColor="@color/red"
                    android:textSize="12sp"
                    tools:text="已发送短信到186****7635已发送短信到186****7635"/>

                <TextView
                    android:id="@+id/tv_login_tip_h5"
                    android:layout_width="wrap_content"
                    android:layout_height="32dip"
                    android:layout_marginRight="16dip"
                    android:gravity="right|center_vertical"
                    android:singleLine="true"
                    android:text="@string/me_login_fail_receive_verification_code"
                    android:textColor="@color/black_assist"
                    android:textSize="12sp"
                    android:visibility="gone"
                    tools:visibility="visible"/>
            </LinearLayout>

            <!-- 登录按钮 -->

            <TextView
                android:id="@+id/tv_login"
                android:layout_width="match_parent"
                android:layout_height="42dip"
                android:layout_marginLeft="16dip"
                android:layout_marginRight="16dip"
                android:background="@drawable/jdme_selector_button_default"
                android:gravity="center"
                android:text="@string/me_login"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <ViewStub
                android:id="@+id/layout_server_switcher"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout="@layout/jdme_server_switcher" />

            <View
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <ImageView
                android:layout_width="165dip"
                android:layout_height="21dip"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="42dip"
                android:src="@drawable/jdme_pic_login_subtitle" />
        </LinearLayout>

    </RelativeLayout>
    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_animation_img"
        android:layout_width="2dip"
        android:layout_height="2dip"
        android:layout_marginTop="72dip"
        android:layout_gravity="center_horizontal"
        android:src="@color/skin_color_default"
        android:visibility="gone"
        tools:visibility="visible"/>
</FrameLayout>