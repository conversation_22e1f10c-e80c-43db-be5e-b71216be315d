<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:gravity="center_horizontal"
              android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:layout_marginTop="4dp"
        android:text="@string/me_choose_server"
        android:textColor="@color/black_main_summary"
        android:textSize="14sp"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <RadioGroup
            android:id="@+id/rg_server_switcher"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="left">

            <RadioButton
                android:id="@+id/rb_server_switcher_39"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="6dp"
                android:text="@string/me_serve_three"
                android:textColor="@color/black_assist"
                android:textSize="14sp"/>

            <RadioButton
                android:id="@+id/rb_server_switcher_55"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="6dp"
                android:text="@string/me_serve_five"
                android:textColor="@color/black_assist"
                android:textSize="14sp"/>

            <RadioButton
                android:id="@+id/rb_server_switcher_gy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="6dp"
                android:text="@string/me_serve_pre_release"
                android:textColor="@color/black_assist"
                android:textSize="14sp"/>

            <RadioButton
                android:id="@+id/rb_server_switcher_official"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="6dp"
                android:text="@string/me_serve_release"
                android:textColor="@color/black_assist"
                android:textSize="14sp"/>

            <RadioButton
                android:id="@+id/tv_server_switcher_others"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="6dp"
                android:text="@string/me_serve_other"
                android:textColor="@color/black_assist"
                android:textSize="14sp"/>

        </RadioGroup>
    </ScrollView>
</LinearLayout>