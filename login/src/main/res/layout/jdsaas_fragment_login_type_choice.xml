<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--底部背景，切换手机和账号登录时，通过scaleX=-1镜像显示-->
    <RelativeLayout
        android:id="@+id/rl_login_type_bg"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:orientation="vertical"
        android:background="@drawable/jdsaas_bg_white_corner_16"
        android:scaleX="1">
        <com.jd.oa.business.jdsaaslogin.ui.view.RoundAngleFrameLayout
            android:id="@+id/fl_login_choice_bg"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            app:topLeftRadius="12dp"
            app:topRightRadius="12dp">
            <com.jd.oa.ui.SimpleRoundImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/jdsaas_login_type_top_bg"
                android:scaleType="fitXY" />
        </com.jd.oa.business.jdsaaslogin.ui.view.RoundAngleFrameLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@drawable/jdsaas_login_type_bottom_bg"
            android:layout_below="@+id/fl_login_choice_bg"/>
    </RelativeLayout>

    <!--上面文字-->
    <LinearLayout
        android:id="@+id/ll_login_choice"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/ll_phone"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">
            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/jdsaas_login_phone"
                android:textColor="@color/jdsaas_color_red_F63218"
                android:textSize="@dimen/jdsaas_text_size_14" />
            <View
                android:id="@+id/view_phone_choice"
                android:layout_width="22dp"
                android:layout_height="2dp"
                android:background="@color/jdsaas_color_red_F63218"
                android:layout_below="@+id/tv_phone"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="4dp"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ll_account"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_toRightOf="@+id/ll_phone">
            <TextView
                android:id="@+id/tv_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jdsaas_login_account"
                android:textSize="@dimen/jdsaas_text_size_14"
                android:textColor="@color/jdsaas_black_6A6A6A"
                android:layout_centerInParent="true"/>
            <View
                android:id="@+id/view_account_choice"
                android:layout_width="22dp"
                android:layout_height="2dp"
                android:background="@color/jdsaas_color_red_F63218"
                android:layout_below="@+id/tv_account"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="4dp"
                android:visibility="gone"/>
        </RelativeLayout>

    </LinearLayout>

</FrameLayout>