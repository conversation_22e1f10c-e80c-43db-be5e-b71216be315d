<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_direction_left"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_22"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:paddingLeft="16dp"
        android:paddingRight="8dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_login_scan_title"
        android:textSize="@dimen/jdsaas_text_size_18"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_back"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:background="#F4F5F6"
        app:layout_constraintTop_toBottomOf="@+id/iv_back"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center_horizontal"
        android:paddingLeft="26dp"
        android:paddingRight="26dp">
        
        <ImageView
            android:layout_width="130dp"
            android:layout_height="90dp"
            android:src="@drawable/jdme_login_scan"
            android:layout_marginTop="50dp"/>

        <TextView
            android:id="@+id/tv_login_scan_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/jdsaas_login_scan_tip"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/jdsaas_black_6A6A6A"
            android:layout_marginTop="50dp"
            android:gravity="center"/>
        
        <TextView
            android:id="@+id/tv_login"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            tools:text="@string/jdsaas_login_scan_confirm"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="20dp"
            android:background="@drawable/jdsaas_scan_login_bg"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="middle"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"/>
        <TextView
            android:id="@+id/tv_login_cancel"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:text="@string/jdsaas_login_scan_cancel"
            android:textSize="@dimen/text_size_14"
            android:textColor="@color/jdsaas_black_1B1B1B"
            app:layout_constraintTop_toBottomOf="@+id/tv_login_cancel"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="20dp"
            android:background="@drawable/jdsaas_scan_login_cancel_bg"
            android:gravity="center"/>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>