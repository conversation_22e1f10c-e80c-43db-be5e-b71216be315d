<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <androidx.appcompat.widget.Toolbar
        style="@style/MeToolbarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:padding="8dp"
                android:text="@string/me_login_auth_close" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textSize="16sp"
                android:textColor="@android:color/black"
                tools:text="VPN登录" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_bias="0.3"
        android:src="@drawable/jdme_img_computer" />

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="12dp"
        android:lineSpacingMultiplier="1.2"
        android:paddingStart="40dp"
        android:paddingEnd="40dp"
        android:textSize="16sp"
        android:textColor="@color/black"
        tools:text="请确认登录VPN请确认登录VPN" />

    <Button
        android:id="@+id/btn_login"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:minHeight="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintVertical_bias="0.8"
        app:layout_constraintWidth_max="300dp"
        android:background="@drawable/jdme_login_auth_btn_login"
        android:textSize="16sp"
        android:text="@string/me_login_auth_login" />

    <Button
        android:id="@+id/btn_cancel"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:minHeight="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_login"
        app:layout_constraintWidth_max="300dp"
        android:background="@drawable/jdme_login_auth_btn_cancel"
        android:textSize="16sp"
        android:textColor="#686868"
        android:text="@string/me_login_auth_cancel_login" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="btn_login,btn_cancel"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>