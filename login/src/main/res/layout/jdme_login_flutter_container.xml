<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/flutter_activity_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/flutter_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="top"
        android:orientation="vertical" />

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_animation_img"
        android:layout_width="2dip"
        android:layout_height="2dip"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="72dip"
        android:src="@color/skin_color_default"
        android:visibility="invisible" />
</FrameLayout>