<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/drawerLayout">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">
        <Button
            android:id="@+id/btn_open"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="打开租户侧边栏"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/white"
            android:layout_gravity="bottom"
            android:background="@drawable/jdsaas_selector_button_rounde_corner"
            android:enabled="true"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="20dp"/>

        <Button
            android:id="@+id/btn_logout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="退出登录"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/white"
            android:layout_gravity="bottom"
            android:background="@drawable/jdsaas_selector_button_rounde_corner"
            android:enabled="true"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="20dp"/>

        <Button
            android:id="@+id/btn_test_token"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="token校验"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/white"
            android:layout_gravity="bottom"
            android:background="@drawable/jdsaas_selector_button_rounde_corner"
            android:enabled="true"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="20dp"/>
        <Button
            android:id="@+id/btn_personal_center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="个人中心"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/white"
            android:layout_gravity="bottom"
            android:background="@drawable/jdsaas_selector_button_rounde_corner"
            android:enabled="true"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="20dp"/>
    </LinearLayout>



    <fragment
        android:id="@+id/fragment_account"
        android:name="com.jd.oa.business.jdsaaslogin.ui.account.AccountDrawerFragment"
        android:layout_width="300dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/white">

    </fragment>
</androidx.drawerlayout.widget.DrawerLayout>