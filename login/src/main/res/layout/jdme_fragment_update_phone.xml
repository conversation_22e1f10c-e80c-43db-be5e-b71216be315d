<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="12dp">

    <ImageView
        android:id="@+id/iv_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/edit_phone"
        app:layout_constraintBottom_toBottomOf="@id/edit_phone"
        android:src="@drawable/jdme_login_phone" />

    <Spinner
        android:id="@+id/spinner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/iv_phone"
        app:layout_constraintTop_toTopOf="@id/iv_phone"
        app:layout_constraintBottom_toBottomOf="@id/iv_phone"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:paddingEnd="10dp"
        android:spinnerMode="dropdown"
        android:background="@drawable/jdme_selector_phone_dropdown"
        android:dropDownVerticalOffset="28dp"
        android:entries="@array/me_week_day" />

    <com.jd.oa.ui.ClearEditText
        android:id="@+id/edit_phone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/spinner"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/white"
        android:inputType="phone"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:singleLine="true"
        android:textColor="#2d2d2d"
        android:textColorHint="@color/black_edit_hit"
        android:textCursorDrawable="@drawable/jdme_edit_cursor"
        android:hint="@string/me_input_phone"
        android:textSize="15sp" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="@dimen/libui_0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintTop_toBottomOf="@id/edit_phone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="4dp"
        android:background="@color/comm_divider" />

    <ImageView
        android:id="@+id/iv_lock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/et_pwd"
        app:layout_constraintBottom_toBottomOf="@+id/et_pwd"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="2dp"
        android:src="@drawable/jdme_login_phone_password" />

    <EditText
        android:id="@id/et_pwd"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider"
        app:layout_constraintLeft_toRightOf="@id/iv_lock"
        app:layout_constraintRight_toLeftOf="@+id/cb_pwd"
        app:layout_constrainedWidth="true"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="8dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingStart="14dp"
        android:paddingEnd="12dp"
        android:inputType="textPassword"
        android:singleLine="true"
        android:textColor="#2d2d2d"
        android:textColorHint="@color/black_edit_hit"
        android:textCursorDrawable="@drawable/jdme_edit_cursor"
        android:textSize="15sp"
        android:hint="@string/me_input_erp_pwd"
        android:background="@color/white" />

    <CheckBox
        android:id="@+id/cb_pwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/et_pwd"
        app:layout_constraintBottom_toBottomOf="@id/et_pwd"
        app:layout_constraintLeft_toRightOf="@id/et_pwd"
        app:layout_constraintRight_toRightOf="parent"
        android:button="@drawable/jdme_eye_selector" />

    <View
        android:id="@+id/view_divider2"
        android:layout_width="@dimen/libui_0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintTop_toBottomOf="@id/et_pwd"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="4dp"
        android:background="@color/comm_divider" />

    <Button
        android:id="@+id/btn_confirm"
        style="@style/my_button"
        android:layout_width="0dp"
        android:layout_height="42dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider2"
        android:layout_marginTop="48dp"
        android:minWidth="90dp"
        android:background="@drawable/jdme_btn_red"
        android:textColor="@color/white"
        android:textSize="@dimen/comm_text_normal_large"
        android:minHeight="30dp"
        android:text="@string/me_confirm" />
</androidx.constraintlayout.widget.ConstraintLayout>