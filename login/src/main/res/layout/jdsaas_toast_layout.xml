<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/jdsaas_toast_bg"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/ic_toast_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:includeFontPadding="false"
        tools:text="@string/icon_prompt_closecircle"
        android:textColor="@color/white"
        android:textSize="@dimen/JMEIcon_24" />

    <TextView
        android:id="@+id/tv_toast_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textColor="@color/white"
        android:textSize="@dimen/jdsaas_text_size_14"
        tools:text="登录失败" />

    <TextView
        android:id="@+id/tv_toast_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/jdsaas_text_size_12"
        tools:text="账号已停用"
        android:layout_marginTop="2dp"/>

</LinearLayout>