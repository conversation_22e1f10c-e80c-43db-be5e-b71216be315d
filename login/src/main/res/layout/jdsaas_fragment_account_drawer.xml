<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/tv_account_switch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_account_drawer_account_switch"
        android:textColor="@color/black"
        android:textSize="@dimen/jdsaas_text_size_16"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"/>

    <LinearLayout
        android:id="@+id/ll_account_token_verify"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_account_switch"
        app:layout_constraintBottom_toBottomOf="@+id/tv_account_switch"
        android:layout_marginRight="16dp"
        android:gravity="center_vertical">
        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_add"
            android:textColor="@color/jdsaas_black_6A6A6A"
            android:textSize="@dimen/JMEIcon_16"
            android:layout_marginRight="4dp"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="扫码登录"
            android:textColor="@color/black"
            android:textSize="@dimen/jdsaas_text_size_14" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_account"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_account_switch"
        android:layout_marginTop="12dp" />

</androidx.constraintlayout.widget.ConstraintLayout>