<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_marginBottom="8dp">
    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/jdsaas_personal_default_avatar"/>
        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_user_avatar_mask_layer"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@color/jdsaas_black_A3A3A380"
            android:visibility="gone"/>
    </FrameLayout>
    <TextView
        android:id="@+id/tv_user_name"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        tools:text="京晓东京晓东京晓东京晓东京晓东京晓东京晓东京晓东京晓东"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"/>

    <TextView
        android:id="@+id/tv_user_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:background="@drawable/jdsaas_tv_team_status_unactivated"
        tools:text="@string/jdsaas_login_unactivated"
        tools:textColor="@color/jdsaas_color_blue_4A5FE8"
        android:textSize="@dimen/jdsaas_text_size_12"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_user_selected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_check"
        android:textColor="@color/jdsaas_color_red_F63218"
        android:textSize="@dimen/JMEIcon_10"
        android:padding="3dp"
        android:visibility="gone"/>
</LinearLayout>