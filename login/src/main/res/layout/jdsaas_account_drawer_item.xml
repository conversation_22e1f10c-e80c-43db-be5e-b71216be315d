<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_marginBottom="12dp"
    android:id="@+id/ll_root">

    <View
        android:id="@+id/view_account_selected"
        android:layout_width="3dp"
        android:layout_height="30dp"
        android:background="@drawable/jdsaas_view_account_selected_bg"
        android:layout_marginTop="14dp"
        tools:visibility="visible"/>

    <RelativeLayout
        android:id="@+id/rl_account"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="11dp"
        android:layout_marginRight="14dp"
        android:orientation="vertical"
        android:background="@drawable/jdsaas_login_team_stroke_corner_bg">
        <TextView
            android:id="@+id/tv_account_login_currently"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/jdsaas_login_account_currently"
            android:textSize="@dimen/jdsaas_text_size_12"
            android:textColor="@color/jdsaas_color_red_F63218"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:layout_marginTop="2dp"
            android:layout_marginRight="2dp"
            android:background="@drawable/jdsaas_tv_account_login_currently"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            tools:visibility="visible"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_account_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <com.jd.oa.elliptical.SuperEllipticalImageView
                android:id="@+id/iv_account_avatar"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:src="@drawable/jdsaas_personal_default_avatar"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>
            <com.jd.oa.elliptical.SuperEllipticalImageView
                android:id="@+id/iv_account_avatar_mask_layer"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:src="@color/jdsaas_black_A3A3A380"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:visibility="gone"/>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_centerVertical="true"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                app:layout_constraintLeft_toRightOf="@+id/iv_account_avatar"
                app:layout_constraintRight_toLeftOf="@+id/tv_account_status"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">
                <TextView
                    android:id="@+id/tv_account_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    tools:hint="测试租户测试租户测试租户测试租户测试租户测试租户测试租户"
                    android:maxLines="1"
                    android:textColor="@color/jdsaas_black_1B1B1B"
                    android:textSize="@dimen/jdsaas_text_size_14"
                    android:textStyle="bold"/>
                <TextView
                    android:id="@+id/tv_account_subname"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="京晓东"
                    android:textColor="@color/jdsaas_black_6A6A6A"
                    android:textSize="@dimen/jdsaas_text_size_12"
                    android:maxLines="1"
                    android:ellipsize="middle" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_account_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:background="@drawable/jdsaas_tv_team_status_unactivated"
                tools:text="@string/jdsaas_login_unactivated"
                tools:textColor="@color/jdsaas_color_blue_4A5FE8"
                android:textSize="@dimen/jdsaas_text_size_12"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:visibility="gone" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_sub_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:layout_below="@+id/cl_account_info"
            android:visibility="visible"/>
    </RelativeLayout>



</LinearLayout>