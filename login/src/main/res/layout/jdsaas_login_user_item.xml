<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/jdsaas_login_team_stroke_corner_bg"
    android:padding="12dp"
    android:layout_marginLeft="22dp"
    android:layout_marginRight="22dp"
    android:layout_marginBottom="12dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_root">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_user_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/jdsaas_personal_default_avatar"/>
    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_user_avatar_mask_layer"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@color/jdsaas_black_A3A3A380"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"/>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@+id/iv_user_avatar"
        app:layout_constraintRight_toLeftOf="@+id/fl_right"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="测试租户测试租户测试租户测试租户测试租户测试租户测试租户"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/jdsaas_black_1B1B1B"
            android:textStyle="bold"/>
        <TextView
            android:id="@+id/tv_user_sub_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="middle"
            tools:text="测试租户测试租户测试租户测试租户测试租户测试租户测试租户"
            android:textSize="@dimen/jdsaas_text_size_12"
            android:textColor="@color/jdsaas_black_6A6A6A"
            android:layout_marginTop="2dp" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_user_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_direction_right"
            android:textColor="@color/jdsaas_black_9D9D9D"
            android:textSize="@dimen/JMEIcon_16" />

        <TextView
            android:id="@+id/tv_user_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:background="@drawable/jdsaas_tv_team_status_unactivated"
            tools:text="@string/jdsaas_login_unactivated"
            tools:textColor="@color/jdsaas_color_blue_4A5FE8"
            android:textSize="@dimen/jdsaas_text_size_12"
            android:visibility="gone" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>