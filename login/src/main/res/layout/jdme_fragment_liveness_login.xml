<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/me_iv_circle"
        android:layout_width="70dip"
        android:layout_height="70dip"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="32dp"
        android:src="@drawable/jdme_profile_gray" />

    <TextView
        android:id="@+id/me_username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/me_iv_circle"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:textColor="@color/jdme_color_first"
        android:textSize="15sp" />

    <!-- 登录按钮 -->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="fill_parent"
        android:layout_below="@+id/me_username"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/tv_login"
            android:layout_width="match_parent"
            android:layout_height="42dip"
            android:layout_marginLeft="32dp"
            android:layout_marginRight="32dp"
            android:layout_marginTop="48dp"
            android:background="@drawable/jdme_selector_button_default"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/jdme_liveness_logion_ico" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:text="@string/me_liveness_login"
                android:textColor="@color/white"
                android:textSize="18sp" />
        </LinearLayout>


        <TextView
            android:id="@+id/me_btn_login_by_pwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="40dp"
            android:gravity="center"
            android:text="@string/me_pwd_login"
            android:textColor="#458bff" />

        <View
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:layout_width="165dip"
            android:layout_height="21dip"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="42dip"
            android:src="@drawable/jdme_pic_login_subtitle" />
    </LinearLayout>


</RelativeLayout>