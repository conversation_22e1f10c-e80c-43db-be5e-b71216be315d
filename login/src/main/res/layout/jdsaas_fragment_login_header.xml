<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_login_logo"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/jdsaas_personal_default_avatar_red"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginLeft="28dp"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@+id/iv_login_logo"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="28dp">
        <TextView
            android:id="@+id/tv_login_welcome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Hi，你好"
            android:textSize="@dimen/jdsaas_text_size_24"
            android:textColor="@color/jdsaas_black"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintLeft_toRightOf="@+id/iv_login_logo"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tv_login_slogan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/jdsaas_login_me_saas_welcome"
            android:textSize="@dimen/jdsaas_text_size_16"
            android:textColor="@color/jdsaas_black_6A6A6A"
            app:layout_constraintLeft_toRightOf="@+id/iv_login_logo"
            app:layout_constraintTop_toBottomOf="@+id/tv_login_welcome" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>