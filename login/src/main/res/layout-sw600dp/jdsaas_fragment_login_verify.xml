<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <include layout="@layout/jdsaas_login_bg_with_gradient" />

    <include
        android:id="@+id/titleBar"
        layout="@layout/jdsaas_login_titlebar" />
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="400dp"
                android:layout_height="500dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="120dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/jdsaas_login_pad_bg_white_corner_16"
                android:elevation="3dp">
                <TextView
                    android:id="@+id/tv_verify_tip1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/jdsaas_login_verifycode_tip"
                    android:textColor="@color/jdsaas_black"
                    android:textSize="@dimen/jdsaas_text_size_20"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginTop="40dp"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="40dp"/>

                <TextView
                    android:id="@+id/tv_verify_tip2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="@string/jdsaas_login_verifycode_send"
                    android:textColor="@color/jdsaas_black_6A6A6A"
                    android:textSize="@dimen/jdsaas_text_size_14"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_verify_tip1"
                    android:layout_marginTop="4dp"
                    android:paddingLeft="40dp"
                    android:paddingRight="40dp"/>

                <com.jd.oa.business.jdsaaslogin.ui.view.LoginVerifyCodeView
                    android:id="@+id/lvcv_verify_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:lvcv_et_cursor_visible="false"
                    app:lvcv_et_inputType="number"
                    app:lvcv_et_number="6"
                    app:lvcv_et_text_color="@color/jdsaas_black"
                    app:lvcv_et_text_size="@dimen/jdsaas_text_size_8"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_verify_tip2"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="40dp" />

                <TextView
                    android:id="@+id/tv_verify_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="@string/jdsaas_login_verifycode_reacquire"
                    android:textSize="@dimen/jdsaas_text_size_14"
                    android:textColor="@color/jdsaas_black_6A6A6A"
                    android:layout_marginTop="8dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/lvcv_verify_code"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="40dp"/>

                <Button
                    android:id="@+id/btn_verify_next"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/jdsaas_login_next"
                    android:textSize="@dimen/jdsaas_text_size_14"
                    android:textColor="@color/white"
                    android:layout_gravity="bottom"
                    android:background="@drawable/jdsaas_selector_button_rounde_corner"
                    android:enabled="false"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginBottom="55dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>