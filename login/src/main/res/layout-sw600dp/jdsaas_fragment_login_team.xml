<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <include layout="@layout/jdsaas_login_bg_with_gradient"/>

    <include
        android:id="@+id/titleBar"
        layout="@layout/jdsaas_login_titlebar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">
            <FrameLayout
                android:layout_width="400dp"
                android:layout_height="500dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginTop="120dp"
                android:layout_marginBottom="10dp"
                android:paddingTop="32dp"
                android:paddingBottom="40dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:background="@drawable/jdsaas_login_pad_bg_white_corner_16"
                android:elevation="3dp">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_team"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </FrameLayout>
        </LinearLayout>

    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>