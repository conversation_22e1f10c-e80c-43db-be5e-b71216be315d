<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    tools:ignore="MissingDefaultResource">

    <include layout="@layout/jdsaas_login_bg_with_gradient" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="351dp"
                android:layout_height="0dp"
                android:layout_weight="1"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginTop="120dp">

                <include
                    android:id="@+id/login_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    layout="@layout/jdsaas_fragment_login_header"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@+id/login_header"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="43dp"
                    android:background="@drawable/jdsaas_login_pad_bg_white_corner_16"
                    android:elevation="1dp">

                    <include
                        layout="@layout/jdsaas_fragment_login_type_choice"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/jdsaas_login_pad_bg_white_corner_16"
                        android:focusable="true"
                        android:focusableInTouchMode="true">
                        <RelativeLayout
                            android:id="@+id/rl_phone"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/jdsaas_login_edittext_height"
                            android:layout_marginTop="9dp"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:visibility="visible">
                            <TextView
                                android:id="@+id/tv_phone_countrycode"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="+86"
                                android:textSize="@dimen/jdsaas_text_size_14"
                                android:textColor="@color/jdsaas_black_1B1B1B"
                                android:layout_centerVertical="true" />

                            <View
                                android:layout_width="1dp"
                                android:layout_height="11dp"
                                android:background="@color/jdsaas_black_D9D9D9"
                                android:layout_marginLeft="16dp"
                                android:layout_toRightOf="@+id/tv_phone_countrycode"
                                android:layout_centerVertical="true"/>

                            <EditText
                                android:id="@+id/et_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:background="@null"
                                android:hint="@string/jdsaas_login_phone_hint"
                                android:inputType="number"
                                android:maxLength="11"
                                android:paddingLeft="59dp"
                                android:textColor="@color/jdsaas_black_1B1B1B"
                                android:textColorHint="@color/jdsaas_login_hint_color"
                                android:textSize="@dimen/jdsaas_dimen_14" />

                            <TextView
                                android:id="@+id/tv_phone_last_login"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/jdsaas_login_last_login"
                                android:textColor="@color/jdsaas_color_red_F63218"
                                android:paddingLeft="8dp"
                                android:paddingRight="8dp"
                                android:paddingTop="1dp"
                                android:paddingBottom="1dp"
                                android:layout_centerVertical="true"
                                android:background="@drawable/jdsaas_login_last_corner_bg"
                                android:visibility="gone" />

                            <ImageView
                                android:id="@+id/iv_phone_clear"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/jdme_icon_delete"
                                android:layout_alignParentRight="true"
                                android:layout_centerVertical="true"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <View
                                android:id="@+id/view_phone_line"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="@color/jdsaas_black_DCDEE0"
                                android:layout_alignParentBottom="true"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_account"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/jdsaas_login_edittext_height"
                            android:layout_marginTop="30dp"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:visibility="gone">
                            <EditText
                                android:id="@+id/et_account"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/jdsaas_login_account_hint"
                                android:textSize="@dimen/jdsaas_text_size_14"
                                android:textColorHint="@color/jdsaas_login_hint_color"
                                android:textColor="@color/jdsaas_black_1B1B1B"
                                android:background="@null"
                                android:layout_centerVertical="true" />
                            <TextView
                                android:id="@+id/tv_account_last_login"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/jdsaas_login_last_login"
                                android:textColor="@color/jdsaas_color_red_F63218"
                                android:paddingLeft="8dp"
                                android:paddingRight="8dp"
                                android:paddingTop="1dp"
                                android:paddingBottom="1dp"
                                android:layout_centerVertical="true"
                                android:background="@drawable/jdsaas_login_last_corner_bg"
                                android:visibility="gone" />
                            <ImageView
                                android:id="@+id/iv_account_clear"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/jdme_icon_delete"
                                android:layout_alignParentRight="true"
                                android:layout_centerVertical="true"
                                android:visibility="gone" />
                            <View
                                android:id="@+id/view_account_line"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="@color/jdsaas_black_DCDEE0"
                                android:layout_alignParentBottom="true"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_password"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/jdsaas_login_edittext_height"
                            android:layout_marginTop="20dp"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:visibility="gone">

                            <EditText
                                android:id="@+id/et_password"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:background="@null"
                                android:hint="@string/jdsaas_login_password_hint"
                                android:inputType="textPassword"
                                android:textColor="@color/jdsaas_black_1B1B1B"
                                android:textColorHint="@color/jdsaas_login_hint_color"
                                android:textSize="@dimen/jdsaas_text_size_14" />

                            <CheckBox
                                android:id="@+id/cb_password"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:button="@null"
                                android:background="@drawable/jdsaas_eye_selector"
                                android:layout_alignParentRight="true"
                                android:layout_centerInParent="true" />

                            <ImageView
                                android:id="@+id/iv_password_clear"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/jdme_icon_delete"
                                android:layout_toLeftOf="@+id/cb_password"
                                android:layout_marginRight="15dp"
                                android:layout_centerVertical="true"
                                android:visibility="gone"/>
                            <View
                                android:id="@+id/view_password_line"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="@color/jdsaas_black_DCDEE0"
                                android:layout_alignParentBottom="true"/>
                        </RelativeLayout>

                        <TextView
                            android:id="@+id/tv_forget_password"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/jdsaas_login_password_forget"
                            android:textSize="@dimen/jdsaas_text_size_14"
                            android:textColor="@color/jdsaas_black_6A6A6A"
                            android:layout_gravity="end"
                            android:paddingRight="16dp"
                            android:paddingTop="8dp"
                            android:visibility="gone"/>

                        <CheckBox
                            android:id="@+id/cb_agreement"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:hint="@string/jdsaas_login_aggrement_read_aggre"
                            android:textSize="@dimen/jdsaas_text_size_12"
                            android:textColor="@color/jdsaas_black_6A6A6A"
                            android:button="@drawable/jdsaas_login_checkbox_selector_aggrement"
                            android:background="@color/translucent"
                            android:paddingLeft="6dp"
                            android:layout_marginTop="48dp"
                            android:layout_marginLeft="16dp"
                            android:layout_marginRight="16dp"
                            android:layout_gravity="left"/>

                        <Button
                            android:id="@+id/btn_next"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/jdsaas_login_next"
                            android:textSize="@dimen/jdsaas_text_size_14"
                            android:textColor="@color/white"
                            android:layout_gravity="bottom"
                            android:background="@drawable/jdsaas_selector_button_rounde_corner"
                            android:enabled="false"
                            android:layout_marginTop="8dp"
                            android:layout_marginLeft="16dp"
                            android:layout_marginRight="16dp"
                            android:layout_marginBottom="26dp"/>

                    </LinearLayout>
                    <ViewStub
                        android:id="@+id/layout_server_switcher"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:layout="@layout/jdme_server_switcher" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:layout_marginBottom="30dp"
                android:layout_marginTop="30dp">
                <com.jd.oa.elliptical.SuperEllipticalImageView
                    android:id="@+id/iv_saas_logo"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/jdme_app_icon"/>
                <TextView
                    android:id="@+id/tv_achieve_excellent_team_cn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/jdsaas_login_slogan_cn"
                    android:textSize="@dimen/jdsaas_text_size_22"
                    android:textColor="@color/jdsaas_black_1B1B1B"
                    android:layout_marginTop="10dp"/>
                <TextView
                    android:id="@+id/tv_achieve_excellent_team_en"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/jdsaas_login_slogan_en"
                    android:textSize="@dimen/jdsaas_text_size_14"
                    android:textColor="@color/jdsaas_black_C4C7CC"
                    android:layout_marginTop="4dp"/>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>