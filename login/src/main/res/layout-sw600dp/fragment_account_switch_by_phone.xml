<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <include layout="@layout/jdsaas_login_bg_with_gradient"
        android:visibility="gone"/>

    <include
        android:id="@+id/titleBar"
        layout="@layout/jdsaas_login_titlebar"
        android:visibility="gone"/>


    <TextView
        android:id="@+id/tv_verify_tip1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_login_account_login"
        android:textColor="@color/jdsaas_black"
        android:textSize="@dimen/jdsaas_text_size_20"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar"
        android:layout_marginTop="40dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"/>

    <TextView
        android:id="@+id/tv_verify_tip2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:hint="@string/jdsaas_login_verifycode_send"
        android:textColor="@color/black"
        android:textSize="@dimen/jdsaas_text_size_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_verify_tip1"
        android:layout_marginTop="4dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"/>

    <com.jd.oa.business.jdsaaslogin.ui.view.LoginVerifyCodeView
        android:id="@+id/lvcv_verify_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        app:lvcv_et_cursor_visible="false"
        app:lvcv_et_inputType="number"
        app:lvcv_et_number="6"
        app:lvcv_et_text_color="@color/black"
        app:lvcv_et_text_size="@dimen/jdsaas_text_size_8"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_verify_tip2"
        android:layout_marginTop="16dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"/>

    <TextView
        android:id="@+id/tv_verify_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="重新获取验证码"
        android:textColor="@color/jdsaas_black_6A6A6A"
        android:layout_marginTop="8dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lvcv_verify_code"
        android:layout_marginStart="22dp"
        android:layout_marginEnd="22dp"/>

    <Button
        android:id="@+id/btn_verify_next"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_login_login"
        android:textColor="@color/white"
        android:layout_gravity="bottom"
        android:background="@drawable/jdsaas_selector_button_rounde_corner"
        android:enabled="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_verify_cancel"
        android:layout_marginBottom="12dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"/>
    <Button
        android:id="@+id/btn_verify_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/jdsaas_login_login_cancel"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:layout_gravity="bottom"
        android:background="@drawable/jdsaas_button_border_rounde_corner_4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_login_type_switch"
        android:layout_marginBottom="12dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"/>

    <TextView
        android:id="@+id/tv_login_type_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:text="切换到密码登录"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/jdsaas_color_blue_1869F5"
        android:layout_marginBottom="40dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>