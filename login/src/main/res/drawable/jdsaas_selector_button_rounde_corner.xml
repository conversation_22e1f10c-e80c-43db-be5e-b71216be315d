<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_enabled="true"
        android:state_pressed="false">
        <shape>
            <gradient android:startColor="@color/jdsaas_color_red_FF4B66"
                android:endColor="@color/jdsaas_color_red_F92E1D"
                android:angle="0"/>
            <corners android:radius="4dp" />
        </shape>
    </item>
    <item
        android:state_enabled="true"
        android:state_pressed="true">
        <shape>
            <solid android:color="@color/jdsaas_color_red_FBADA3" />
            <corners android:radius="4dp" />
            <corners android:radius="4dp" />
        </shape>
    </item>

    <!-- 默认效果 -->
    <item>
        <shape>
            <solid android:color="@color/jdsaas_color_red_FBADA3" />
            <corners android:radius="4dp" />
        </shape>
    </item>
</selector>