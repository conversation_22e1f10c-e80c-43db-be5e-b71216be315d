<resources>

    <string name="me_string_server_switcher_title">Please Input the Service Address</string>
    <string name="me_string_server_switcher_hint">http://*************</string>

    <!-- 邮箱验证码 -->
    <string name="me_login_fail_receive_verification_code">Failed to receive verification code</string>
    <string name="me_login_wrong_phone_number">Wrong phone number</string>
    <string name="me_login_email_verification_coder">Send verification code to Email</string>
    <string name="me_login_code_wait_tip">Please try again after the countdown</string>

    <string name="me_feedback_type_other">Other</string>

    <string name="me_logining">Login…</string>
    <string name="me_login_check_signature_fail">Check Signature Fail</string>

    <string name="me_erp_account_is_empty">Please input the ERP account</string>
    <string name="me_pass_is_empty">Please input the password</string>
    <string name="me_erp_pass_error">ERP Password Error</string>

    <string name="me_input_code_six">Input Verification Code</string>
    <string name="me_code_error_input_again">Verification code input error\n please acquire the verification code again</string>
    <string name="me_photo_state">Phone State</string>
    <string name="me_storage_sys">Storage System</string>
    <string name="me_refused_permerssion">You have refused JoyME permission request, it will result in ME function unusual, if you cannot confirm the permission, please contact JoyME</string>
    <string name="me_re_send_after">S</string>
    <string name="me_shot_again">Shot Again</string>
    <string name="me_face_shot">Face Shot</string>
    <string name="me_shot_not_yet">Shot Not Yet</string>
    <string name="me_not_liveness_need_photo">You have not taken a photo for snap login need, are you sure to take a photo?</string>
    <string name="me_for_account_safe_with_liveness_login">For your account safety, face login is only limited to your own device\n please confirm your phone number and verity through SMS </string>
    <string name="me_default_phone_number">130****5678</string>
    <string name="me_update_phone_number">Update The Phone Number</string>

    <string name="me_msg_code">Get the Verification Code</string>
    <string name="me_modify_phone">modify</string>

    <!-- 新增内容 -->
    <string name="me_language_locale">Language</string>
    <string name="me_language_locale_multi">Language</string>
    <string name="me_language_set">Language Setting</string>

    <string name="me_liveness_login">Face Login</string>
    <string name="me_setting_security_face_setting">Face Setting</string>
    <string name="me_identify_fail">Identification Fails</string>
    <string name="me_code_error_retry">Verification code error, please input it again!</string>
    <string name="me_verify_user">Confirm Authentication</string>
    <string name="me_liveness_info"><Data><![CDATA[Enable means agreeing with <font color="#f0250f"> the snap use protocol</font>]]></Data></string>
    <string name="me_liveness_cmd_1">Please Place the Face Within the Box~</string>
    <string name="me_camera_open_fail">Camera Open Fails</string>
    <string name="me_close_livess_login">Are you sure to close face login?</string>

    <string name="me_msg_code_hint">Input Verification Code</string>
    <string name="me_login_need_change_phone_no_tips">In case of phone number change, please try again after change in the HR system</string>
    <string name="me_login_phone_hint">Wrong Phone Number?</string>

    <string name="me_login">Login</string>
    <string name="me_pwd_login">Password Login</string>

    <string name="me_choose_server">Server Chooser:</string>
    <string name="me_serve_three">test Server</string>
    <string name="me_serve_five">jdos Server</string>
    <string name="me_serve_pre_release">Pre-Release Server</string>
    <string name="me_serve_release">Release Server</string>
    <string name="me_serve_other">other</string>

    <string name="me_camera_title">Camera</string>
    <string name="me_storage">Storage</string>
    <string name="me_liveness_check_fail">Liveness check fails, please try again</string>
    <string name="me_login_fail">Login Failure</string>

    <string name="me_liveness_permission">Camera Permission And External Storage Permission Required for Face Login</string>

    <string name="me_login_auth_login">Login</string>
    <string name="me_login_auth_tip">Loing With VPN</string>
    <string name="me_login_auth_close">Close</string>
    <string name="me_login_auth_cancel_login">Cancel Login</string>
    <string name="me_login_auth_client_canceled">Client auth canceled</string>
    <string name="me_login_auth_client_confirmed">Client auth confirmed</string>
    <string name="me_login_auth_expired">Auth expired</string>
    <string name="me_login_auth_canceled">Auth canceled</string>

    <string name="me_erp_account">Please input the ERP account</string>
    <string name="me_erp_pwd">Please input the ERP password</string>

    <!--刷脸打卡  start -->
    <string name="me_liveness_clock_in_notice">Use face clock ?</string>
    <string name="me_liveness_clock_in">Face Clock</string>
    <string name="me_close_livess_clock_in">Turn off face clock ?</string>
    <string name="me_liveness_clock_in_fail">Face clock  failed</string>
    <!--刷脸打卡  end -->

    <string name="me_identifying_code">Verification Code</string>
    <string name="me_message_identifying_code">SMS Verification Code</string>
    <string name="me_input_ve_code">Input the Verification Code</string>
    <string name="me_send_msg_code_phone">Already sent a verification code SMS to phone %s</string>
    <string name="me_gain_regain">Regain</string>
    <string name="me_update_phone">Edit the mobile phone number</string>
    <string name="me_input_phone">Please Input the Phone Number</string>
    <string name="me_input_erp_pwd">Please Input the ERP Password</string>
    <string name="me_input_right_phone">Please Input the Right Phone Number</string>
    <string name="me_pass_error">Password Error</string>

    <string name="detect_check_success">verify success</string>
    <string name="detect_detect">detecting</string>
    <string name="detect_failure">failure</string>

    <string name="detect_feedBackCode0">The action is normal</string>
    <string name="detect_feedBackCode10">Can not find face</string>
    <string name="detect_feedBackCode11">Too far</string>
    <string name="detect_feedBackCode12">Too near</string>
    <string name="detect_feedBackCode13">Too left</string>
    <string name="detect_feedBackCode14">Too right</string>
    <string name="detect_feedBackCode15">Too up</string>
    <string name="detect_feedBackCode16">Too down</string>
    <string name="detect_feedBackCode20">The light is too bright</string>
    <string name="detect_feedBackCode21">The light is too dark</string>
    <string name="detect_feedBackCode30">Not a positive face</string>
    <string name="detect_feedBackCode31">Can not rise</string>
    <string name="detect_feedBackCode32">Can not bow</string>
    <string name="me_login_scan_opening">Starting</string>

    <string name="detect_interrupted">Recognition is interrupted</string>
    <string name="detect_liveness_failed">Authentication failed</string>
    <string name="detect_loading">uploading</string>
    <string name="detect_no">no</string>
    <string name="detect_no_permission">No permission</string>
    <string name="detect_pass">pass</string>
    <string name="detect_permission_failed">Turn on camera failed, please confirm that camera permissions are turned on</string>
    <string name="detect_please_waiting">Please wait…</string>
    <string name="detect_prestart_tip">Please put your face in the wireframe</string>
    <string name="detect_retry">Whether to try again</string>
    <string name="detect_timeout">Detection timeout</string>
    <string name="detect_tip1">Please shake your head left and right</string>
    <string name="detect_tip2">Please shake your head up and down</string>
    <string name="detect_tip3">Please open your mouth</string>
    <string name="detect_tip4">Please blink</string>
    <string name="detect_upload_success">Uploaded successfully</string>
    <string name="detect_yes">Yes</string>
    <string name="json_exception">Parse json exception</string>
    <string name="me_judging">detecting</string>
    <string name="me_face_config">face config</string>

    <string name="jdsaas_login_me_saas_welcome">Welcome to JoyWE!</string>
    <string name="jdsaas_login_phone">Phone</string>
    <string name="jdsaas_login_account">Account</string>
    <string name="jdsaas_login_phone_hint">Please enter your phone number.</string>
    <string name="jdsaas_login_account_hint">Please enter your Pin</string>
    <string name="jdsaas_login_password_hint">Please enter your password</string>
    <string name="jdsaas_login_aggrement_read_aggre">I have read and agree to</string>
    <string name="jdsaas_login_aggrement_aggre">Please agree to</string>
    <string name="jdsaas_login_privacy_aggrement">JoyWE Privacy Policy</string>
    <string name="jdsaas_login_next">Next</string>
    <string name="jdsaas_login_aggrement_dialog_title">Tips</string>
    <string name="jdsaas_login_aggrement_dialog_cancel">Cancel</string>
    <string name="jdsaas_login_aggrement_dialog_confirm">Agree and login</string>
    <string name="jdsaas_login_verifycode_tip">Please enter the 6-digit verification code</string>
    <string name="jdsaas_login_verifycode_send">The verification code has been sent to</string>
    <string name="jdsaas_login_verifycode_valid">Valid for %1$s minutes</string>
    <string name="jdsaas_login_verifycode_countdown">Retrievable in %1$ss seconds.</string>
    <string name="jdsaas_login_verifycode_not_received">Not received? </string>
    <string name="jdsaas_login_verifycode_reacquire">Retrieve the verification code</string>
    <string name="jdsaas_login_team_choice_tip1">Choose the team to enter</string>
    <string name="jdsaas_login_team_choice_tip2">has already set up an account with one of the following teams, you can access any of the following teams</string>
    <string name="jdsaas_login_user_choice_tip1">Choose identity</string>
    <string name="jdsaas_login_user_choice_tip2">There are %2$s account identities in %1$s, you can choose to enter any of the following account identities</string>
    <string name="jdsaas_account_drawer_account_switch">Switch Accounts</string>
    <string name="jdsaas_login_dialog_password_tip1">Please enter</string>
    <string name="jdsaas_login_dialog_password_tip2">account and password</string>
    <string name="jdsaas_login_password_forget">Forgotten password?</string>
    <string name="jdsaas_login_dialog_phone_switch">Switch to phone verification code login</string>
    <string name="jdsaas_login_dialog_password_switch">Switch to password login</string>
    <string name="jdsaas_login_loading">loading</string>
    <string name="jdsaas_login_fail">Login Fail</string>
    <string name="jdsaas_login_unactivated">inactive</string>
    <string name="jdsaas_login_deactivated">deactivated</string>
    <string name="jdsaas_login_team_sub_name">%1$s %2$s identities</string>
    <string name="jdsaas_login_account_currently">Current</string>
    <string name="jdsaas_login_od_dialog_title">Tips</string>
    <string name="jdsaas_login_od_dialog_message">The account is already logged in on another device, if you continue to log in, the other device will launch this account</string>
    <string name="jdsaas_login_od_dialog_negative_btn">Cancel</string>
    <string name="jdsaas_login_od_dialog_positive_btn">Continue</string>
    <string name="jdsaas_login_welcome">Hi</string>
    <string name="jdsaas_login_error_dialog_positive_btn">Confirm</string>
    <string name="jdsaas_login_other_device_login">Account already logged in on another device</string>
    <string name="jdsaas_login_logout_dialog_title">Log out of all accounts?</string>
    <string name="jdsaas_login_logout_dialog_message">After logging out you will not receive message notifications for all teams</string>
    <string name="jdsaas_login_expired_tip">Expired, please log in again</string>
    <string name="jdsaas_login_error_dialog_message">The user has not registered on this platform yet</string>
    <string name="jdsaas_login_last_login">Last login</string>
    <string name="jdsaas_login_data_exception">Data anomaly</string>
    <string name="jdsaas_login_account_login">Login</string>
    <string name="jdsaas_login_switch_success">Switch successfully</string>
    <string name="jdsaas_login_show_all_teams">Show all teams</string>
    <string name="jdsaas_login_login">Login</string>
    <string name="jdsaas_login_login_cancel">Cancel</string>
    <string name="jdsaas_login_scan_title">QR Code</string>
    <string name="jdsaas_login_scan_tip">Your account is currently logged in on a computer.\n Please confirm that this device is logged in by you.</string>
    <string name="jdsaas_login_scan_confirm">Authorize 「%1$s」</string>
    <string name="jdsaas_login_scan_cancel">Cancel</string>
    <string name="jdsaas_login_scan_data_failed">Data request failed.</string>
    <string name="jdsaas_login_scan_qr_invalid">Invalid QR code.</string>
    <string name="jdsaas_login_scan_qr_expired">QR code has expired.</string>
    <string name="jdsaas_login_scan_account_invalid">Invalid account. Please check your account.</string>
    <string name="jdsaas_login_scan_account_expired">Account has expired. Please log in again.</string>
    <string name="jdsaas_login_scan_password_updated">Password has been updated. Please log in again to continue.</string>
    <string name="jdsaas_login_scan_account_security">Account has expired for security reasons. Please log in again.</string>
    <string name="jdsaas_login_scan_account_logged_out">You have been logged out. Please log in again.</string>
    <string name="jdsaas_login_scan_account_status_expired">Account has expired. Please log in again.</string>
    <string name="jdsaas_login_scan_successful_authorization">Successful authorization!</string>
    <string name="jdsaas_login_scan_authorization_cancelled">Authorization has been cancelled.</string>
    <string name="jdsaas_login_slogan_cn">乐在正道，赢向未来</string>
    <string name="jdsaas_login_slogan_en">Joy We Joy Win</string>
</resources>
