<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 自定义验证码输入框-->
    <declare-styleable name="LoginVerifyCodeView">
        <!--输入框的数量-->
        <attr name="lvcv_et_number" format="integer" />
        <!--输入类型-->
        <attr name="lvcv_et_inputType">
            <enum name="number" value="0" />
            <enum name="numberPassword" value="1" />
            <enum name="text" value="2" />
            <enum name="textPassword" value="3" />
        </attr>
        <!--输入框的宽度-->
        <attr name="lvcv_et_width" format="dimension|reference" />
        <!--输入框文字颜色-->
        <attr name="lvcv_et_text_color" format="color|reference" />
        <!--输入框文字大小-->
        <attr name="lvcv_et_text_size" format="dimension|reference" />
        <!--输入框背景-->
        <attr name="lvcv_et_bg" format="reference" />
        <!--光标样式-->
        <attr name="lvcv_et_cursor" format="reference" />
        <!--是否隐藏光标-->
        <attr name="lvcv_et_cursor_visible" format="boolean" />
        <!--输入框间距，不输入则代表平分-->
        <attr name="lvcv_et_spacing" format="dimension|reference" />

    </declare-styleable>


    <declare-styleable name="RoundAngleFrameLayout">
        <attr name="radius" format="dimension" />
        <attr name="topLeftRadius" format="dimension" />
        <attr name="topRightRadius" format="dimension" />
        <attr name="bottomLeftRadius" format="dimension" />
        <attr name="bottomRightRadius" format="dimension" />
    </declare-styleable>

</resources>