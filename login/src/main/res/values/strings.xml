<resources>

    <string name="me_string_server_switcher_title">请输入服务地址</string>
    <string name="me_string_server_switcher_hint">http://*************</string>

    <!-- 邮箱验证码 -->
    <string name="me_login_fail_receive_verification_code">收不到验证码？</string>
    <string name="me_login_wrong_phone_number">手机号有误</string>
    <string name="me_login_email_verification_coder">发送验证码至邮箱</string>
    <string name="me_login_code_wait_tip">请在倒计时之后重试</string>

    <string name="me_feedback_type_other">其他</string>

    <string name="me_logining">登录中…</string>
    <string name="me_login_check_signature_fail">校验响应签名失败</string>

    <string name="me_erp_account_is_empty">请输入ERP账号</string>
    <string name="me_pass_is_empty">请输入密码</string>
    <string name="me_erp_pass_error">ERP密码错误</string>

    <string name="me_input_code_six">请输入6位短信验证码</string>
    <string name="me_code_error_input_again">验证码输入错误\n请重新获取验证码</string>

    <string name="me_photo_state">手机状态</string>
    <string name="me_storage_sys">存储系统</string>
    <string name="me_refused_permerssion">您拒绝了京东ME的权限请求，将会导致程序功能异常，如您无法确认权限，请联系京东ME</string>
    <string name="me_re_send_after">秒后重发</string>

    <string name="me_msg_code">获取验证码</string>
    <string name="me_modify_phone">修改</string>

    <string name="me_language_locale" translatable="false">语言</string>
    <string name="me_language_locale_multi">多语言</string>
    <string name="me_language_set">语言设置</string>

    <string name="me_liveness_login">刷脸登录</string>
    <string name="me_setting_security_face_setting">刷脸设置</string>
    <string name="me_identify_fail">识别失败</string>
    <string name="me_verify_user">身份验证</string>
    <string name="me_liveness_info"><Data><![CDATA[开启即视为同意<font color="#f0250f">《人脸使用协议》</font>]]></Data></string>
    <string name="me_liveness_cmd_1">请把脸置于线框内~</string>
    <string name="me_code_error_retry">验证码错误，请重新输入</string>
    <string name="me_shot_again">重新拍摄</string>
    <string name="me_face_shot">脸部拍摄</string>
    <string name="me_shot_not_yet">未拍摄</string>
    <string name="me_not_liveness_need_photo">您还未拍摄过刷脸，是否拍摄？</string>
    <string name="me_for_account_safe_with_liveness_login">考虑您的账号安全，刷脸仅限本人设备登录\n请确认本人手机号码，并通过短信验证</string>
    <string name="me_default_phone_number">130****5678</string>
    <string name="me_update_phone_number">更改手机号</string>

    <string name="me_msg_code_hint">请输入6位验证码</string>
    <string name="me_login_need_change_phone_no_tips">如更换手机号请在HR系统修改后重试</string>
    <string name="me_login_phone_hint">手机号有误?</string>

    <string name="me_login">登 录</string>
    <string name="me_pwd_login">密码登录</string>

    <string name="me_choose_server" translatable="false">服务器选择器：</string>
    <string name="me_serve_three">测试环境</string>
    <string name="me_serve_five">jdos</string>
    <string name="me_serve_pre_release">预发环境</string>
    <string name="me_serve_release">正式</string>
    <string name="me_serve_other">自定义</string>

    <string name="me_camera_title">相机</string>
    <string name="me_storage">存储</string>
    <string name="me_liveness_check_fail">活体检测失败，请重试</string>
    <string name="me_login_fail">登录失败</string>
    <string name="me_camera_open_fail">相机打开失败</string>
    <string name="me_close_livess_login">确定关闭刷脸登录？</string>

    <string name="me_liveness_permission">刷脸登录需要开启相机、存储权限</string>

    <string name="me_login_auth_login">登录</string>
    <string name="me_login_auth_tip">请确认登录VPN</string>
    <string name="me_login_auth_close">关闭</string>
    <string name="me_login_auth_cancel_login">取消登录</string>
    <string name="me_login_auth_client_canceled">手机客户端已取消授权</string>
    <string name="me_login_auth_client_confirmed">手机客户端已确认登录</string>
    <string name="me_login_auth_expired">认证已过期</string>
    <string name="me_login_auth_canceled">认证已取消</string>

    <!-- 登陆界面 -->
    <string name="me_erp_account">请输入ERP账号</string>
    <string name="me_erp_pwd">请输入ERP密码</string>


    <!--刷脸打卡  start -->
    <string name="me_liveness_clock_in_notice">是否通过刷脸打卡？</string>
    <string name="me_liveness_clock_in">刷脸打卡</string>
    <string name="me_close_livess_clock_in">确定关闭刷脸打卡？</string>
    <string name="me_liveness_clock_in_fail">刷脸打卡失败</string>
    <!--刷脸打卡  end -->

    <string name="me_identifying_code">验证码</string>
    <string name="me_message_identifying_code">短信验证码</string>
    <string name="me_input_ve_code">输入验证码</string>
    <string name="me_send_msg_code_phone">已向手机%s发送短信验证码</string>
    <string name="me_gain_regain">重新获取</string>
    <string name="me_update_phone">修改手机号</string>
    <string name="me_input_phone">请输入手机号</string>
    <string name="me_input_erp_pwd">请输入ERP密码</string>
    <string name="me_input_right_phone">请输入正确的手机号</string>
    <string name="me_pass_error">密码错误</string>

    <string name="me_change_language_prompt" translatable="false">Switch application language to English？</string>
    <string name="me_change_language_prompt_confirm" translatable="false">OK</string>
    <string name="me_change_language_prompt_cancel" translatable="false">Cancel</string>

    <string name="detect_check_success">校验成功</string>
    <string name="detect_detect">识别中</string>
    <string name="detect_failure">异常失败</string>
    <string name="detect_feedBackCode0">动作正常</string>
    <string name="detect_feedBackCode10">请面对镜头</string>
    <string name="detect_feedBackCode11">太远</string>
    <string name="detect_feedBackCode12">太近</string>
    <string name="detect_feedBackCode13">偏左</string>
    <string name="detect_feedBackCode14">偏右</string>
    <string name="detect_feedBackCode15">偏上</string>
    <string name="detect_feedBackCode16">偏下</string>
    <string name="detect_feedBackCode20">光线太亮</string>
    <string name="detect_feedBackCode21">光线太暗</string>
    <string name="detect_feedBackCode30">侧脸了</string>
    <string name="detect_feedBackCode31">抬头了</string>
    <string name="detect_feedBackCode32">低头了</string>
    <string name="me_login_scan_opening">启动中</string>
    <string name="detect_interrupted">识别中断</string>
    <string name="detect_liveness_failed">认证失败</string>
    <string name="detect_loading">上传中</string>
    <string name="detect_no">否</string>
    <string name="detect_no_permission">无权限</string>
    <string name="detect_pass">通过</string>
    <string name="detect_permission_failed">打开相机失败，请确认已开启相机权限</string>
    <string name="detect_please_waiting">请稍等…</string>
    <string name="detect_prestart_tip">请把脸置入线框内~</string>
    <string name="detect_retry">是否重试</string>
    <string name="detect_timeout">检测超时</string>
    <string name="detect_tip1">请左右摇头</string>
    <string name="detect_tip2">请上下摇头</string>
    <string name="detect_tip3">请张嘴</string>
    <string name="detect_tip4">请眨眼</string>
    <string name="detect_upload_success">上传成功</string>
    <string name="detect_yes">是</string>
    <string name="json_exception">json解析异常</string>
    <string name="me_judging">识别中</string>
    <string name="me_face_config">刷脸设置</string>
    
    <string name="jdsaas_login_me_saas_welcome">欢迎登录京东WE</string>
    <string name="jdsaas_login_phone">手机号</string>
    <string name="jdsaas_login_account">账号</string>
    <string name="jdsaas_login_phone_hint">请输入您的手机号</string>
    <string name="jdsaas_login_account_hint">请输入您的账号</string>
    <string name="jdsaas_login_password_hint">请输入您的密码</string>
    <string name="jdsaas_login_aggrement_read_aggre">我已阅读并同意</string>
    <string name="jdsaas_login_aggrement_aggre">请先同意</string>
    <string name="jdsaas_login_privacy_aggrement">京东WE隐私政策</string>
    <string name="jdsaas_login_next">下一步</string>
    <string name="jdsaas_login_aggrement_dialog_title">提示</string>
    <string name="jdsaas_login_aggrement_dialog_cancel">取消</string>
    <string name="jdsaas_login_aggrement_dialog_confirm">同意并登录</string>
    <string name="jdsaas_login_verifycode_tip">请输入6位验证码</string>
    <string name="jdsaas_login_verifycode_send">验证码已发送至</string>
    <string name="jdsaas_login_verifycode_valid">有效期%1$s分钟</string>
    <string name="jdsaas_login_verifycode_countdown">%1$ss 后可重新获取</string>
    <string name="jdsaas_login_verifycode_not_received">没有收到？</string>
    <string name="jdsaas_login_verifycode_reacquire">重新获取验证码</string>
    <string name="jdsaas_login_team_choice_tip1">选择要进入的企业/团队</string>
    <string name="jdsaas_login_team_choice_tip2">已在以下团队绑定了账号，您可以进入以下任一企业/团队</string>
    <string name="jdsaas_login_user_choice_tip1">选择账号身份</string>
    <string name="jdsaas_login_user_choice_tip2">在%1$s下有%2$s个身份账号，您可以选择进入以下任一身份账号</string>
    <string name="jdsaas_account_drawer_account_switch">点击切换账号</string>
    <string name="jdsaas_login_dialog_password_tip1">请输入</string>
    <string name="jdsaas_login_dialog_password_tip2">的账号密码</string>
    <string name="jdsaas_login_password_forget">忘记密码</string>
    <string name="jdsaas_login_dialog_phone_switch">切换到手机验证码登录</string>
    <string name="jdsaas_login_dialog_password_switch">切换到密码登录</string>
    <string name="jdsaas_login_loading">加载中</string>
    <string name="jdsaas_login_fail">登录失败</string>
    <string name="jdsaas_login_unactivated">待激活</string>
    <string name="jdsaas_login_deactivated">已停用</string>
    <string name="jdsaas_login_team_sub_name">%1$s等%2$s个身份</string>
    <string name="jdsaas_login_account_currently">当前登录</string>
    <string name="jdsaas_login_od_dialog_title">温馨提示</string>
    <string name="jdsaas_login_od_dialog_message">账号已在其他设备登录，如继续登录，其他设备将退出此账号</string>
    <string name="jdsaas_login_od_dialog_negative_btn">取消登录</string>
    <string name="jdsaas_login_od_dialog_positive_btn">继续登录</string>
    <string name="jdsaas_login_welcome">Hi，你好</string>
    <string name="jdsaas_login_error_dialog_positive_btn">确认</string>
    <string name="jdsaas_login_other_device_login">账号已在其他设备登录</string>
    <string name="jdsaas_login_logout_dialog_title">退出当前设备登录的所有账号？</string>
    <string name="jdsaas_login_logout_dialog_message">退出后您将无法收到所有账号的消息通知</string>
    <string name="jdsaas_login_expired_tip">账号已过期 请重新登录</string>
    <string name="jdsaas_login_error_dialog_message">用户暂未注册本平台</string>
    <string name="jdsaas_login_last_login">上次登录</string>
    <string name="jdsaas_login_data_exception">数据异常</string>
    <string name="jdsaas_login_account_login">账号登录</string>
    <string name="jdsaas_login_switch_success">切换成功</string>
    <string name="jdsaas_login_show_all_teams">展示全部团队</string>
    <string name="jdsaas_login_login">登录</string>
    <string name="jdsaas_login_login_cancel">取消</string>
    <string name="jdsaas_login_scan_title">扫码登录</string>
    <string name="jdsaas_login_scan_tip">您的账号正在电脑上登录\n请确认该设备在您的身边并且是本人登录</string>
    <string name="jdsaas_login_scan_confirm">授权登录「%1$s」"</string>
    <string name="jdsaas_login_scan_cancel">取消登录</string>
    <string name="jdsaas_login_scan_data_failed">请求数据错误</string>
    <string name="jdsaas_login_scan_qr_invalid">二维码无效</string>
    <string name="jdsaas_login_scan_qr_expired">二维码已过期</string>
    <string name="jdsaas_login_scan_account_invalid">账号无效，请检查您的账号</string>
    <string name="jdsaas_login_scan_account_expired">账号登录已过期，请重新登录</string>
    <string name="jdsaas_login_scan_password_updated">账号密码已修改，请重新登录</string>
    <string name="jdsaas_login_scan_account_security">因涉及安全问题，账号登录已过期，请重新登录</string>
    <string name="jdsaas_login_scan_account_logged_out">账号已退出登录，请重新登录</string>
    <string name="jdsaas_login_scan_account_status_expired">登录态已失效，请重新登录</string>
    <string name="jdsaas_login_scan_successful_authorization">手机客户端已确认登录</string>
    <string name="jdsaas_login_scan_authorization_cancelled">手机客户端已取消授权</string>
    <string name="jdsaas_login_slogan_cn">乐在正道，赢向未来</string>
    <string name="jdsaas_login_slogan_en">Joy We Joy Win</string>
</resources>
