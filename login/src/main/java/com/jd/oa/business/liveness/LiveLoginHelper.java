package com.jd.oa.business.liveness;

import static com.jd.oa.business.login.controller.LoginActivity.getDefaultTabDeepLink;
import static com.jd.oa.business.login.controller.LoginFragment.loginSuccessData;
import static com.jd.oa.business.login.controller.LoginFragment.loginSuccessUI;

import android.app.Activity;
import android.content.Intent;
import android.os.Message;

import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.Router;
import com.jd.flutter.common.JDFHelper;
import com.jd.oa.AppBase;
import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.model.service.TabbarService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.gateway.ServeConfig;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.PauseHandler;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.encrypt.RSAUtil;
import com.jme.login.R;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;

public class LiveLoginHelper {

    public void onResume() {
        final Activity activity = AppBase.getTopActivity();
        handler.setActivity(activity);
        handler.resume();
    }

    public void onPause() {
        handler.pause();
    }

    public void onDestroy() {
        handler.setActivity(null);
    }

    public void onActivityResult(int requestCode, Intent data) {
        final Activity activity = AppBase.getTopActivity();
        if (requestCode == 15 && data != null) {
            String filePath = data.getStringExtra("path");
            final String faceSecretKey = data.getStringExtra("faceSecretKey");
            try {
                final File file = new File(filePath);
                if (file.exists() && activity != null) {
                    PromptUtils.showLoadDialog(activity, AppBase.getAppContext().getString(R.string.me_logining));
                    ExecutorService executors = Executors.newSingleThreadExecutor();
                    executors.execute(new Runnable() {
                        @Override
                        public void run() {
                            doLogin(file, faceSecretKey);
                        }
                    });
                } else {
                    ToastUtils.showWarnToast(R.string.me_liveness_check_fail);
                }
            } catch (Exception e) {
                ToastUtils.showWarnToast(R.string.me_liveness_check_fail);
            }
        }
    }


    private void doLogin(final File file, final String faceSecretKey) {
        NetWorkManagerLogin.exchangePublicKey(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME),
                KeyManager.getInstance().getPublicKey(), new SimpleRequestCallback<String>(null, false, true) {
                    @Override
                    public void onStart() {
                        super.onStart();
                        // 1.显示加载框
                        final Activity activity = AppBase.getTopActivity();
                        if (activity != null) {
                            PromptUtils.showLoadDialog(activity, AppBase.getAppContext().getString(R.string.me_logining));
                        }
                    }

                    @Override
                    public void onSuccess(final ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, Map.class);
                        if (response.isSuccessful()) {
                            String publicKey = response.getData().get("serverPublicKeyStr");
                            KeyManager.getInstance().storeServerPublicKey(publicKey);
                            snapLogin(file, faceSecretKey);
                        } else {
                            ToastUtils.showToast(response.getErrorMessage());
                            handler.sendMessage(handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        // 2.移除加载框
                        handler.sendMessage(handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));
                    }
                });

    }

    private void snapLogin(File file, String faceSecretKey) {

        NetWorkManagerLogin.snapLogin(file, faceSecretKey, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    Map<String, String> map = response.getData(Map.class);
                    String sign = map.get("sign");
                    String encryptData = map.get("User");
                    if (checkResponseSign(encryptData, sign)) {
                        final String data = decryptResponse(encryptData);
                        loginSuccessData(data, "");
                        ServeConfig.getInstance(AppBase.getTopActivity()).getRemoteConfig(AppBase.getTopActivity());
                        // 获取Tabbar
                        final TabbarService tabbarService = AppJoint.service(TabbarService.class);
                        tabbarService.getTabbarConfig(new TabbarService.ITabbarConfigCallback() {
                            @Override
                            public void onSucsess() {
                                loginSuccessUI();
                                afterLogin();
                            }

                            @Override
                            public void onSuccess(String info) {

                            }

                            @Override
                            public void onFailed() {
                                loginSuccessUI();
                                afterLogin();
                            }
                        },true);

                    } else {
                        loginFail(AppBase.getAppContext().getString(R.string.me_login_check_signature_fail));
                    }
                } else {
                    //海外无刷脸权限用户刷脸失败后隐藏刷脸登录按钮
                    if("1040711".equals(response.getErrorCode())){
                        PreferenceManager.UserInfo.setHasFaceLoginPermission("0");
                        JDFHelper.invisibleFaceLogin();
                    }
                    loginFail(response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                loginFail(info);
            }

        });

    }


    private void loginFail(String errMsg) {
        handler.sendMessage(handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));
        final Activity activity = AppBase.getTopActivity();
        if (activity != null) {
            PromptUtils.showAlertDialog(activity, errMsg);
        }

    }


    private void afterLogin() {
        //由于无界版咚咚，在fragment初始化做了autologin，如果在获取咚咚token之前
        //就到主界面会出现"用户名无效的错误"，所以加个无用转圈
        //咚咚下版本修复 ---20190220
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                final Activity activity = AppBase.getTopActivity();
                if (activity == null) {
                    return;
                } else if (activity instanceof LoginActivity) {
                    LoginActivity loginActivity = (LoginActivity) activity;
                    loginActivity.toIndex();
                } else {
                    Router.build(getDefaultTabDeepLink()).go(AppBase.getAppContext());
                    activity.finish();
                }

                handler.sendMessage(handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));

            }
        }, 0, TimeUnit.SECONDS);
    }

    /**
     * Used for "what" parameter to handler messages
     */
    private final static int MSG_WHAT = 22;
    private final static int MSG_REMOVE_DIALOG = 1;

    /**
     * Message Handler class that supports buffering up of messages when the
     * activity is paused i.e. in the background.
     */
    private static class MessageHandler extends PauseHandler {
        /**
         * Activity instance
         */
        protected Activity activity;

        final void setActivity(Activity activity) {
            this.activity = activity;
        }

        @Override
        final protected boolean storeMessage(Message message) {
            return true;
        }


        @Override
        final protected void processMessage(Message msg) {
            final Activity activity = this.activity;
            if (activity != null) {
                if (msg.what == MSG_WHAT) {
                    PromptUtils.removeLoadDialog((FragmentActivity) activity);
                }
            }
        }
    }


    private boolean checkResponseSign(String content, String sign) {
        String publicKey = KeyManager.getInstance().getServerPublicKey();
        return RSAUtil.verify(publicKey, content, sign);
    }

    private String decryptResponse(String encryptData) {
        String privateKey = KeyManager.getInstance().getPrivateKey();
        return RSAUtil.decrypt(privateKey, encryptData, KeyManager.DECRYPT_SEGMENT_SIZE);
    }


    /**
     * Handler for this activity
     */
    public MessageHandler handler = new MessageHandler();
}
