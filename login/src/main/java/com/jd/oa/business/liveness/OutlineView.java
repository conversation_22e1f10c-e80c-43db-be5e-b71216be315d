package com.jd.oa.business.liveness;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;

import com.jme.login.R;

/**
 * Created by hufeng7 on 2016/12/20
 */

public class OutlineView extends androidx.appcompat.widget.AppCompatImageView {

    private Drawable left_top;
    private Drawable left_bottom;
    private Drawable right_top;
    private Drawable right_bottom;
    private static final int BOUND_GAP = 20;

    public OutlineView(Context context) {
        super(context);
        init();
    }

    public OutlineView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public OutlineView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        left_top = getResources().getDrawable(R.drawable.jdme_liveness_icon_left_top);
        left_bottom = getResources().getDrawable(R.drawable.jdme_liveness_left_bottom);
        right_top = getResources().getDrawable(R.drawable.jdme_liveness_right_top);
        right_bottom = getResources().getDrawable(R.drawable.jdme_liveness_right_bottom);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        left_top.setBounds(BOUND_GAP, BOUND_GAP, left_top.getIntrinsicWidth() + BOUND_GAP, left_top.getIntrinsicHeight() + BOUND_GAP);
        left_top.draw(canvas);

        left_bottom.setBounds(BOUND_GAP, getHeight() - left_bottom.getIntrinsicHeight() - BOUND_GAP, left_top.getIntrinsicWidth() + BOUND_GAP, getHeight() - BOUND_GAP);
        left_bottom.draw(canvas);

        right_top.setBounds(getWidth() - right_top.getIntrinsicWidth() - BOUND_GAP, BOUND_GAP, getWidth() - BOUND_GAP, left_top.getIntrinsicHeight() + BOUND_GAP);
        right_top.draw(canvas);

        right_bottom.setBounds(getWidth() - right_bottom.getIntrinsicWidth() - BOUND_GAP, getHeight() - right_bottom.getIntrinsicHeight() - BOUND_GAP, getWidth() - BOUND_GAP, getHeight() - BOUND_GAP);
        right_bottom.draw(canvas);
    }

    public int getBoundGap() {
        return BOUND_GAP;
    }
}
