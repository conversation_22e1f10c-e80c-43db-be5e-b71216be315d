package com.jd.oa.business.liveness.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by liyao8 on 2017/12/11.
 */

public class AllLimitBean implements Parcelable {
    private String isPrivallege;//刷脸登录权限
    private String isSnapPunchPrivallege;//刷脸打卡权限

    public String getIsPrivallege() {
        return isPrivallege;
    }

    public void setIsPrivallege(String isPrivallege) {
        this.isPrivallege = isPrivallege;
    }

    public String getIsSnapPunchPrivallege() {
        return isSnapPunchPrivallege;
    }

    public void setIsSnapPunchPrivallege(String isSnapPunchPrivallege) {
        this.isSnapPunchPrivallege = isSnapPunchPrivallege;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.isPrivallege);
        dest.writeString(this.isSnapPunchPrivallege);
    }

    public AllLimitBean() {
    }

    protected AllLimitBean(Parcel in) {
        this.isPrivallege = in.readString();
        this.isSnapPunchPrivallege = in.readString();
    }

    public static final Parcelable.Creator<AllLimitBean> CREATOR = new Parcelable.Creator<AllLimitBean>() {
        @Override
        public AllLimitBean createFromParcel(Parcel source) {
            return new AllLimitBean(source);
        }

        @Override
        public AllLimitBean[] newArray(int size) {
            return new AllLimitBean[size];
        }
    };
}
