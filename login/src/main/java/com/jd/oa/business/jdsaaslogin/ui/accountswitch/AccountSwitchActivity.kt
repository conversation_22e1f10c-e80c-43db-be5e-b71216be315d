package com.jd.oa.business.jdsaaslogin.ui.accountswitch

import android.os.Bundle
import android.text.TextUtils
import android.view.Window
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.BaseActivity
import com.jd.oa.business.InjectorUtil
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginDialog
import com.jd.oa.business.jdsaaslogin.util.LoginUtil
import com.jd.oa.utils.FragmentUtils
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper

class AccountSwitchActivity : BaseActivity() {

    val viewModel by lazy { ViewModelProvider(this, InjectorUtil.getAccountSwitchModelFactory()).get(
        AccountSwitchViewModel::class.java) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this)
        }
        setContentView(R.layout.activity_account_switch)

        val msgCodeExpireTime = intent.getIntExtra(MSG_CODE_EXPIRE_TIME,0)
        viewModel.mMsgCodeExpireTime = msgCodeExpireTime
        viewModel.mGetMessageCodeToken = intent.getStringExtra(GET_MESSAGE_CODE_TOKEN)
        viewModel.mTeamAccountInfo = intent.getSerializableExtra(TEAM_ACCOUNT_INFO) as TeamAccountInfo?
        viewModel.mTeamUserInfo = intent.getSerializableExtra(TEAM_USER_INFO) as TeamUserInfo?

        viewModel.startMsgCodeExpireTimeCountDown(msgCodeExpireTime)//开始验证码倒计时
        showAccountSwitchByPhoneFragment()

        viewModel.mOtherDeviceLoginLiveData.observe(this){
            if(it == 0){
                JdSaasLoginDialog.showOtherDeviceLoginDialog(this, loginContinue = {
                    viewModel.loginByToken(1)
                }, loginCancel = {
                    finish()
                })
            }
        }
        viewModel.mAccountExceptionLiveData.observe(this){
            var message = getString(R.string.jdsaas_login_error_dialog_message)
            if(!TextUtils.isEmpty(it)){
                message = it
            }
            JdSaasLoginDialog.showAccountExceptionDialog(this, message){
                finish()
            }
        }
        viewModel.mLoginSuccessLiveData.observe(this){
            finish()
        }
        viewModel.mMsgCodeSendSuccessLiveData.observe(this){
            showAccountSwitchByPhoneFragment()
        }
        viewModel.mMessageCodeAlreadySendLiveData.observe(this){
            showAccountSwitchByPhoneFragment()
        }
    }

    fun showAccountSwitchByPhoneFragment(){
        FragmentUtils.replaceWithCommit(this,
            AccountSwitchByPhoneFragment::class.java,R.id.fl_container,false,null,false)
    }

    companion object{
        const val MSG_CODE_EXPIRE_TIME = "msgCodeExpireTime"
        const val GET_MESSAGE_CODE_TOKEN = "getMessageCodeToken"
        const val TEAM_ACCOUNT_INFO = "teamAccountInfo"
        const val TEAM_USER_INFO = "TeamUserInfo"
    }

    override fun finish() {
        super.finish()
        if(!LoginUtil.isTablet(this)){
            overridePendingTransition(R.anim.jdme_left_in, R.anim.jdme_right_out)
        }
    }
}