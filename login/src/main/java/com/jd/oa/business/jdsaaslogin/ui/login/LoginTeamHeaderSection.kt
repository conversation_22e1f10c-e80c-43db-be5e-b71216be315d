package com.jd.oa.business.jdsaaslogin.ui.login

import android.content.Context
import android.graphics.Typeface
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jme.login.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

const val TYPE_TEAM = 0
const val TYPE_PIN = 1
class LoginTeamHeaderSection(val context: Context,val type : Int) : Section(
    SectionParameters.builder().itemResourceId(R.layout.jdsaas_login_team_header).build()) {

    var userRepresent : String? = null
    var teamAccountInfo : TeamAccountInfo? = null

    override fun getContentItemsTotal(): Int {
        return 1
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, i: Int) {
        val holder = viewHolder as ItemViewHolder
        var loginChoiceTip2 : String
        if(type == TYPE_TEAM){
            holder.tv_team_choice_tip1.setText(context.getString(R.string.jdsaas_login_team_choice_tip1))
            loginChoiceTip2 = context.getString(R.string.jdsaas_login_team_choice_tip2)
        }else{
            holder.tv_team_choice_tip1.setText(context.getString(R.string.jdsaas_login_user_choice_tip1))
            loginChoiceTip2 = context.getString(R.string.jdsaas_login_user_choice_tip2,teamAccountInfo?.teamFullName,teamAccountInfo?.teamUserInfoList?.size.toString())
        }
        val spanBuilder = SpannableStringBuilder()
        var span = SpannableString("$userRepresent ")
        span.setSpan(StyleSpan(Typeface.BOLD),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        span.setSpan(ForegroundColorSpan(context.getColor(R.color.jdsaas_black_1B1B1B)),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        spanBuilder.append(span)
        spanBuilder.append(loginChoiceTip2)
        holder.tv_team_choice_tip2.setText(spanBuilder)
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val tv_team_choice_tip1 = view.findViewById<TextView>(R.id.tv_team_choice_tip1)
        val tv_team_choice_tip2 = view.findViewById<TextView>(R.id.tv_team_choice_tip2)
    }
}