package com.jd.oa.business.liveness.widget;

import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.fragment.dialog.Unbind.MyDialogFragmentListener;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.login.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * 验证码 对话框
 *
 * <AUTHOR>
 */
public class VerifyInputDialog extends DialogFragment {

    /**
     * bundle key
     */
    public static final String BUNDLE_KEY_PASS_RIGHT = "bundle_key_right";

    private MyDialogFragmentListener mDialogDoneListener;

    /**
     * 输入框
     */
    private EditText mEdit;
    private TextView mCmdTv;
    private String phone = "";
    private String prefix = "";

    private MyCountDownTimer countDownTimer;

    /**
     * 获取实例
     *
     * @param phone 标题
     * @return 实例
     */
    public static VerifyInputDialog getInstance(String phone, String prefix) {
        Bundle bundle = new Bundle();
        bundle.putString("phone", phone);
        bundle.putString("prefix", prefix);
        VerifyInputDialog input = new VerifyInputDialog();
        input.setArguments(bundle);
        input.setCancelable(false);
        return input;
    }

    /**
     * 创建传统的dialog
     */
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getActivity(), R.style.me_custom_dialog1);
        LayoutInflater inflater = getLayoutInflater();
        View view = inflater.inflate(R.layout.jdme_dialog_verify_code_input, null);
        builder.setView(view);

        Bundle arguments = getArguments();
        if (arguments != null) {
            phone = arguments.getString("phone");
            prefix = arguments.getString("prefix");
        }

        builder.setTitle(R.string.me_input_ve_code);
        TextView tvInfo = (TextView) view.findViewById(R.id.me_tv_info);
        mEdit = (EditText) view.findViewById(R.id.me_et_code);
        mCmdTv = (TextView) view.findViewById(R.id.me_tv_get_code);
        try {
            tvInfo.setText(tvInfo.getContext().getString(R.string.me_send_msg_code_phone, ("" + phone.subSequence(0, 3) + "****" + phone.substring(7))));
        } catch (Exception e) {
        }

        // 确定按钮
        builder.setPositiveButton(R.string.me_ok,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                    }
                });

        // 取消按钮
        builder.setNegativeButton(R.string.me_cancel,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (mDialogDoneListener != null) {
                            mDialogDoneListener.onDialogDone(true, null, null);
                        }
                    }
                });

        mCmdTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 获取验证码
                getCode(phone);
            }
        });

        mCmdTv.performClick();
        mCmdTv.setEnabled(false);

        return builder.create();
    }

    /**
     * 避免点击 按钮关闭 dialog
     */
    @Override
    public void onResume() {
        super.onResume();
        final AlertDialog d = (AlertDialog) getDialog();
        if (d != null) {
            Button positiveButton = d.getButton(Dialog.BUTTON_POSITIVE);
            positiveButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    toVerifyPWD();
                }
            });
            d.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(getActivity(), R.color.colorPrimary));
            d.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(getActivity(), R.color.colorPrimary));
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        clearTimeDown();
    }

    public void startTimeDown() {
        clearTimeDown();
        countDownTimer = new MyCountDownTimer(60 * 1000, 1000);
        countDownTimer.start();
    }

    /**
     * 获取验证码
     *
     * @param phone
     */
    private void getCode(String phone) {

        NetWorkManagerLogin.getVeriCode(phone, "10003", prefix, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray) {
                super.onSuccess(map, tArray);
                mCmdTv.setEnabled(false);
                startTimeDown();
            }

            @Override
            public void onFailure(String errorMsg) {
                super.onFailure(errorMsg);
                ToastUtils.showInfoToast("" + errorMsg);
                mCmdTv.setEnabled(true);
                clearTimeDown();
            }
        }));
    }

    private void clearTimeDown() {
        if (null != countDownTimer) {
            countDownTimer.cancel();
            countDownTimer = null;
        }
    }

    private class MyCountDownTimer extends CountDownTimer {
        public MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(final long millisUntilFinished) {
            mCmdTv.setText(millisUntilFinished / 1000 + mCmdTv.getContext().getString(R.string.me_re_send_after));
        }

        @Override
        public void onFinish() {
            mCmdTv.setEnabled(true);
            mCmdTv.setText(R.string.me_gain_regain);
        }
    }


    /**
     * 用户取消了
     */
    @Override
    public void onCancel(DialogInterface dialog) {
        super.onCancel(dialog);
        if (mDialogDoneListener != null) {
            mDialogDoneListener.onDialogDone(true, null, null);
        }

        clearTimeDown();
    }

    public void setDialogDoneListener(
            MyDialogFragmentListener mDialogDoneListener) {
        this.mDialogDoneListener = mDialogDoneListener;
    }

    /**
     * 连接服务器验证是否正确
     */
    private void toVerifyPWD() {
        final Bundle bundle = new Bundle();

        String pass = mEdit.getText().toString().trim();
        if (StringUtils.isEmptyWithTrim(pass)) {
            ToastUtils.showToast(R.string.me_input_error);
            return;
        }

        // 真正的修改逻辑
        NetWorkManagerLogin.updateUserPhone(phone, pass, prefix, new SimpleRequestCallback<String>(getActivity(), true) {
            @Override
            public void onSuccess(final ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getActivity() == null) {
                    return;
                }

                ResponseParser parser = new ResponseParser(info.result, getActivity());
                parser.parse(new ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            JSONObject json = new JSONObject(info.result);
                            String toastMsg;
                            if ("1042303".equals(json.optString("errorCode"))) {
                                toastMsg = json.optString("errorMsg");
                                if (!TextUtils.isEmpty(toastMsg)) {
                                    ToastUtils.showInfoToast(toastMsg);
                                }
                                return;
                            }
                            toastMsg = json.optString("content");
                            if (!TextUtils.isEmpty(toastMsg)) {
                                ToastUtils.showInfoToast(toastMsg);
                            }
                            // 回调方法调用
                            if (null != mDialogDoneListener) {
                                bundle.putBoolean(BUNDLE_KEY_PASS_RIGHT, true);
                                mDialogDoneListener.onDialogDone(false, null, bundle);
                            }
                            clearTimeDown();
                        } catch (JSONException e) {
                            ToastUtils.showInfoToast(R.string.me_code_error_retry);
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        ToastUtils.showInfoToast("" + errorMsg);
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                ToastUtils.showInfoToast(R.string.me_code_error_retry);
            }
        });
    }
}
