package com.jd.oa.business

import com.jd.oa.business.jdsaaslogin.data.LoginScanRepository
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchModelFactory
import com.jd.oa.business.jdsaaslogin.ui.scan.LoginScanModelFactory

object InjectorUtil {

    fun getLoginModelFactory() = LoginModelFactory(LoginRepository())

    fun getAccountSwitchModelFactory() = AccountSwitchModelFactory(LoginRepository())

    fun getLoginScanModelFactory() = LoginScanModelFactory(LoginScanRepository())
}