package com.jd.oa.business.liveness.capture;

import android.os.Handler;
import android.text.TextUtils;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jme.login.R;

import java.io.File;
import java.util.List;

/**
 * Created by zhaoyu1 on 2016/12/21.
 */
public class LivenessCapturePresenter extends AbsMVPPresenter<LivenessCaptureConstract.View> implements LivenessCaptureConstract.Presenter {


    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public LivenessCapturePresenter(LivenessCaptureConstract.View view) {
        super(view);
    }

//上传刷脸数据
    @Override
    public void doUploadTemplate(File[] files, String faceSecretKey, String faceToken, String faceFileType, final String sourceSwitch) {
        view.showUploadLayer(view.getContext().getString(R.string.me_judging));

        SimpleReqCallbackAdapter call = new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            protected void onSuccess(Object o, List<Object> tArray, final String rawData) {
                super.onSuccess(o, tArray, rawData);
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (isAlive()) {
                            if (!TextUtils.isEmpty(sourceSwitch)) {
                                sign(sourceSwitch);//调用签署协议接口
                            } else {
                                view.showContent();
                                view.uploadTemplateSuccess();
                            }
                        }
                    }
                }, 0);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                if (isAlive()) {
                    view.showContent();
                    view.showError(view.getContext().getString(R.string.me_identify_fail) + errorMsg);
                }
            }
        });

        NetWorkManagerLogin.uploadTemplate(files, faceSecretKey, faceToken, faceFileType, call);
    }

    /**
     * 调用签署协议接口
     */
    private void sign(String sourceSwitch) {

        NetWorkManagerLogin.signAgreement(sourceSwitch, "1", "", new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            protected void onSuccess(Object str, List<Object> tArray, String rawData) {
                super.onSuccess(str, tArray, rawData);
                if (isAlive()) {
                    view.showContent();//获取协议成功去调用识别接口
                    view.uploadTemplateSuccess();//上传成功
                }
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                if (isAlive()) {
                    view.showContent();
                    view.showError(view.getContext().getString(R.string.me_load_failed) + errorMsg);
                }
            }
        }));
    }


    @Override
    public void onCreate() {
    }

    @Override
    public void onDestroy() {

    }
}
