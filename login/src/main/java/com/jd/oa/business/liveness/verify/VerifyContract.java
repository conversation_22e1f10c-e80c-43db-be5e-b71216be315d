package com.jd.oa.business.liveness.verify;

import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/12/19.
 */
public interface VerifyContract {
    interface View extends IMVPView {
        void showContent();

        void enableAllWidget(boolean isEnable);

        void showMobile(String code, String mobile);

        void startTimeDown();

        void showCaptureView();
    }

    interface Presenter extends IMVPPresenter {
        /**
         * @param isForce 是否强制加载，强制加载时，不控制页面状态
         */
        void getUserMobile(boolean isForce);

        void getVerifyCode(String code, String mobileNumber);

        void checkVerifyCode(String verifyCode);
    }

    interface Repo extends IMVPRepo {
        void getUserMobile(LoadDataCallback<Map> callback);

        void getVeriCode(LoadDataCallback<Object> callback, String code, String mobileNumber);

        void checkVerifyCode(LoadDataCallback<Object> callback, String verifyCode);
    }
}
