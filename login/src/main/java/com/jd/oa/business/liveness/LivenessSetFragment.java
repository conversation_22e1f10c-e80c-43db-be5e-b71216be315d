package com.jd.oa.business.liveness;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.MyPlatform;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.liveness.model.AllLimitBean;
import com.jd.oa.business.liveness.model.AllStatusBean;
import com.jd.oa.business.liveness.set.LivenessSetContract;
import com.jd.oa.business.liveness.set.LivenessSetPresenter;
import com.jd.oa.business.liveness_new.LivenessNewActivity;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.WebViewUtils;
import com.jme.login.R;

import java.util.List;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

/**
 * 刷脸设置首页
 * Created by zhaoyu1 on 2016/12/8.
 */
@FontScalable(scaleable = false)
@Navigation(backGroundRes = android.R.color.transparent)
public class LivenessSetFragment extends BaseFragment implements LivenessSetContract.View {

    public static final String SOURCE_LIVESS_LOGIN = "2";
    public static final String SOURCE_LIVESS_CLOCKIN = "5";
    public static final String SOURCE_SWITCH_KEY = "SOURCE_SWITCH_KEY";
    /**
     * 签署协议
     */
    private final int SIGN_AGREEMENT = 11;
    private final int REQUEST_CODE_CAPTURE = 12;
    private FrameView me_frameView;
    private TextView me_info;
    private SwitchCompat me_switch_liveness;
//    private SwitchCompat me_switch_clock_in;
    /**
     * 重新拍摄
     */
    private View me_container_repeat;
    private ImageView me_top_container;
    private LivenessSetContract.Presenter mPresenter;
    private LivenessLoginSwitchListener switchListener = new LivenessLoginSwitchListener();
//    private View me_rl_clock_in;
    private View me_rl_login;
    private TextView tv_repeat;
    private AllStatusBean allStatusBean;
    private boolean hasPicUpload;
    private View divider;

    @Nullable
    @Override
    public View onCreateView(final LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_liveness_set, container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_security_face_setting);
//        ActionBarHelper.changeActionBarTitle(this, getString(R.string.me_setting_security_face_setting));
        me_info = (TextView) view.findViewById(R.id.me_info);
        me_switch_liveness = (SwitchCompat) view.findViewById(R.id.me_switch_liveness);
//        me_switch_clock_in = view.findViewById(R.id.me_switch_clock_in);
        me_container_repeat = view.findViewById(R.id.me_container_repeat);
        me_top_container = (ImageView) view.findViewById(R.id.me_top_container);
        me_frameView = (FrameView) view.findViewById(R.id.me_frameView);
//        me_rl_clock_in = view.findViewById(R.id.me_rl_clock_in);
        me_rl_login = view.findViewById(R.id.me_rl_login);
        tv_repeat = view.findViewById(R.id.tv_repeat);
        divider = view.findViewById(R.id.divider);
//        me_container_repeat.setVisibility(View.GONE);

        String closeProcess = view.getContext().getResources().getString(R.string.me_liveness_info);
        me_info.setText(Html.fromHtml(closeProcess));
        me_info.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toOpenProtocol();
            }
        });

        me_container_repeat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, UserVerifyFragment.class.getName());
                startActivityForResult(intent, SIGN_AGREEMENT);
            }
        });

        initView();
        return view;
    }

    private void initView() {
        if (mPresenter == null) {
            mPresenter = new LivenessSetPresenter(this);
            //刷脸设置的初始化逻辑，这里有点乱,怎么改都不太顺
            mPresenter.checkLivenessUpload();
            mPresenter.getAllLimit();
            mPresenter.getAllAgreementStatus();
        }

        // 初始化开关
        me_switch_liveness.setOnCheckedChangeListener(switchListener);
//        me_switch_clock_in.setOnCheckedChangeListener(new LivenessClockSwitchListener());
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //打开录入人脸前的短信验证页面的回调
        if (SIGN_AGREEMENT == requestCode) {
            if (data != null && data.getBooleanExtra("isOK", false)) {
                toLiveness(data.getStringExtra(SOURCE_SWITCH_KEY));
            } else {
                switchStatus();
            }
            //采集人脸的回调
        } else if (requestCode == REQUEST_CODE_CAPTURE) {
            if (resultCode == Activity.RESULT_OK && data != null && data.getBooleanExtra("isOK", false)) {
                // 修改刷脸的文字
                tv_repeat.setText(R.string.me_shot_again);
                //修改人脸上传的状态,不知道为什么本地状态写入有问题
                hasPicUpload = true;
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD,"1");
            } else if (resultCode == BaseActivity.RESULT_ERROR) {
                getActivity().setResult(BaseActivity.RESULT_ERROR);
            }
            //开关太多了，重新访问网络获取开关状态
            mPresenter.getAllAgreementStatus();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    @Override
    public void showContent() {
        me_frameView.setContainerShown(false);
    }

    @Override
    public void showMsgToast(String msg) {
        ToastUtils.showWarnToast("" + msg);
    }

    @Override
    public void onCheckUploadFinish(boolean hasUpload) {
        hasPicUpload = hasUpload;
        if (hasUpload) {
            tv_repeat.setText(R.string.me_shot_again);
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD,"1");
        } else {
            //修改拍摄按钮的文字
            tv_repeat.setText(R.string.me_shot_not_yet);
            switchStatus();
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD,"0");
            showNeedUpload();
        }
    }

    @Override
    public void onSignLoginFinish(boolean isSuccess, boolean isAgree) {
        // 修改刷脸登录开关的回调
//        switchStatus();
        me_switch_liveness.setChecked(isAgree);
        allStatusBean.setIsAgreeLog(isAgree ? "1" : "0");
    }

    @Override
    public void onSignClockFinish(boolean isSuccess, boolean isAgree) {
        //修改刷脸打卡checkbox的回调
//        me_switch_clock_in.setChecked(isAgree);
        allStatusBean.setIsAgreeAttend(isAgree ? "1" : "0");
//        PreferenceManager.UserInfo.setLivenessClockIn(isAgree ? 1 : 0);
    }

    @Override
    public void changeAllAgreementStatus(AllStatusBean data) {
        // 修改所有刷脸项的开关状态
        allStatusBean = data;
        me_switch_liveness.setChecked(allStatusBean.getIsAgreeLog().equals("1"));
//        me_switch_clock_in.setChecked(allStatusBean.getIsAgreeAttend().equals("1"));
    }

    @Override
    public void showAllLimits(AllLimitBean data) {
        // 显示有权限的刷脸项目
        if (data.getIsPrivallege().equals("1")) {
            //有刷脸登录权限
            me_rl_login.setVisibility(View.VISIBLE);
        }
//        if (data.getIsSnapPunchPrivallege().equals("1")) {
            //有刷脸打卡的权限
//            me_rl_clock_in.setVisibility(View.VISIBLE);
//        }
        if (me_rl_login.getVisibility() == View.VISIBLE) {
            me_container_repeat.setBackgroundResource(R.drawable.jdme_ripple_white_top_corner8);
            divider.setVisibility(View.VISIBLE);
        } else {
            me_container_repeat.setBackgroundResource(R.drawable.jdme_ripple_white_corner8);
            divider.setVisibility(View.GONE);
        }
    }

    @Override
    public void showLoading(String msg) {
        me_frameView.setLoadInfo(R.string.me_empty);
        me_frameView.setContainerProgressShown(false);
    }

    @Override
    public void showError(String msg) {
//        me_frameView.setErrorShow(msg, false);
        ToastUtils.showInfoToast(msg);
    }

    /**
     * 打开捕获页面
     */
    private void toLiveness(final String sourceSwitch) {
        PermissionHelper.requestPermissions(getActivity(),getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),getResources().getString(com.jme.common.R.string.me_request_permission_camera_liveness),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        liveness(sourceSwitch);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                    }
                },Manifest.permission.CAMERA,Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    private void liveness(String sourceSwitch) {
        Intent intent = new Intent(getContext(), LivenessNewActivity.class);
        intent.putExtra("erp", MyPlatform.getCurrentUser().getUserName());
        intent.putExtra("action", "verify");        // 采集
        intent.putExtra("from", "set");
        intent.putExtra(SOURCE_SWITCH_KEY, sourceSwitch);
        startActivityForResult(intent, REQUEST_CODE_CAPTURE);
    }

    /**
     * 未上传人脸信息的时 所有开关置为false
     */
    private void switchStatus() {
        //这里判断的是有没有上传过人脸模板 不是判断有没有同意人脸使用协议,呵呵
//        if ("1".equals(PreferenceManager.getString(PreferenceManager.Other.KEY_LIVENESS_IS_UPLOAD))) {
        if ("1".equals(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD))) {
            if (allStatusBean != null) {
                me_switch_liveness.setChecked("1".equals(allStatusBean.getIsAgreeLog()));
//                me_switch_clock_in.setChecked("1".equals(allStatusBean.getIsAgreeAttend()));
            }
        } else {
            me_switch_liveness.setChecked(false);
//            me_switch_clock_in.setChecked(false);
        }
    }

    private void toOpenProtocol() {
        Intent intent = new Intent(getActivity(), FunctionActivity.class);
        WebBean bean = new WebBean("https://storage.360buyimg.com/jdmedocs/agreemeent/snapme.html", WebConfig.H5_NATIVE_HEAD_SHOW);
        intent.putExtra(EXTRA_WEB_BEAN, bean);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
        getActivity().startActivity(intent);
    }

    public void showNeedUpload() {
        PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_not_liveness_need_photo, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, UserVerifyFragment.class.getName());
                startActivityForResult(intent, SIGN_AGREEMENT);
            }
        });
    }

    private class LivenessLoginSwitchListener implements CompoundButton.OnCheckedChangeListener {
        @Override
        public void onCheckedChanged(final CompoundButton buttonView, final boolean isChecked) {
            if (!buttonView.isPressed()) {
                // 不是人为点击
                return;
            }
            if (!isChecked) {
                // 关闭状态
                mPresenter.signLoginAgreement("0");
            } else {
                if (!hasPicUpload) {
                    //跳转上传刷脸模板的流程
                    Intent intent = new Intent(getActivity(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, UserVerifyFragment.class.getName());
                    intent.putExtra(SOURCE_SWITCH_KEY, SOURCE_LIVESS_LOGIN);
                    startActivityForResult(intent, SIGN_AGREEMENT);
                } else {
                    mPresenter.signLoginAgreement("1");
                }
            }
        }
    }

    private class LivenessClockSwitchListener implements CompoundButton.OnCheckedChangeListener {
        @Override
        public void onCheckedChanged(final CompoundButton buttonView, final boolean isChecked) {
            if (!buttonView.isPressed()) {
                // 不是人为点击
                return;
            }
            if (!isChecked) {
                mPresenter.signClock("0");
            } else {
                if (!hasPicUpload) {
                    //跳转上传刷脸模板的流程
                    Intent intent = new Intent(getActivity(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, UserVerifyFragment.class.getName());
                    intent.putExtra(SOURCE_SWITCH_KEY, SOURCE_LIVESS_CLOCKIN);
                    startActivityForResult(intent, SIGN_AGREEMENT);
                } else {
                    mPresenter.signClock("1");
                }
            }
        }
    }
}
