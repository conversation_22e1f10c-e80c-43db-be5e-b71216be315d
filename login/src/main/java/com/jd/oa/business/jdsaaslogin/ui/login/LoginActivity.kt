package com.jd.oa.business

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.lifecycle.ViewModelProvider
import com.chenenyu.router.Router
import com.chenenyu.router.annotation.Route
import com.jd.oa.AppBase
import com.jd.oa.BaseActivity
import com.jd.oa.MyPlatform
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.ui.success.LoginSuccessActivity
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginDialog
import com.jd.oa.business.jdsaaslogin.util.LoginUtil
import com.jd.oa.business.login.controller.LoginActivity
import com.jd.oa.business.login.controller.TabletLoginActivity
import com.jd.oa.listener.OperatingListener
import com.jd.oa.preference.JDMETenantPreference
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.FragmentUtils
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.jdsaas_activity_login.iv_animation_img

@Route(DeepLink.ACCOUNT_SAAS)
class LoginActivity : BaseActivity(),OperatingListener{

    companion object{
        const val EXTRA_ROUTER = "route_activity"
    }

    val viewModel by lazy { ViewModelProvider(this,InjectorUtil.getLoginModelFactory()).get(LoginViewModel::class.java) }

    lateinit var mLogin_anim: Animation
    var route_activity : String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this)
        }
        setContentView(R.layout.jdsaas_activity_login)
        if(LoginUtil.isTablet(this)){//pad显示阴影边框
            window.setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        }
        //踢出提示
        val msg = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG)
        if(!TextUtils.isEmpty(msg)){
            JdSaasLoginDialog.showAccountExceptionDialog(this, msg){
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG, "")
            }
        }
        route_activity = intent.getStringExtra(EXTRA_ROUTER)
        if(PreferenceManager.UserInfo.getLogin() && null != MyPlatform.sUser){
            toIndex()
        }else{
            showLoginFragment()
        }

        mLogin_anim = AnimationUtils.loadAnimation(
            AppBase.getAppContext(),
            com.jd.flutter.R.anim.jdme_anim_login
        )
        mLogin_anim.fillAfter = true
        mLogin_anim.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
            }

            override fun onAnimationRepeat(animation: Animation) {}
            override fun onAnimationEnd(animation: Animation) {
                // 通知login事件
                FragmentUtils.updateUI(OperatingListener.OPERATE_LOGIN, null)
            }
        })
        viewModel.mMsgCodeSendSuccessLiveData.observe(this){
            val fragment = supportFragmentManager.findFragmentById(R.id.fragment_container)
            if(fragment !is LoginVerifyFragment){
                showVerifyFragment()
            }
        }
        viewModel.mMessageCodeAlreadySendLiveData.observe(this){
            val fragment = supportFragmentManager.findFragmentById(R.id.fragment_container)
            if(fragment is LoginFragment){
                showVerifyFragment()
            }
        }
        viewModel.mToLoginTeamFragmentLiveData.observe(this){
            showTeamFragment()
        }
        viewModel.mToLoginUserFragmentLiveData.observe(this){
            showUserFragment()
        }
        viewModel.mOtherDeviceLoginLiveData.observe(this){
            if(it == 0 && !PreferenceManager.UserInfo.getLogin()){
                JdSaasLoginDialog.showOtherDeviceLoginDialog(this, loginContinue = {
                    viewModel.loginByToken(1,null,null)
                }, loginCancel = {
                    val fragment = supportFragmentManager.findFragmentById(R.id.fragment_container)
                    if(fragment is LoginVerifyFragment){
                        supportFragmentManager.popBackStack()
                    }
                })
            }
        }
        viewModel.mAccountExceptionLiveData.observe(this){
            var message = getString(R.string.jdsaas_login_error_dialog_message)
            if(!TextUtils.isEmpty(it)){
                message = it
            }
            JdSaasLoginDialog.showAccountExceptionDialog(this,message){
                val fragment = supportFragmentManager.findFragmentById(R.id.fragment_container)
                if(fragment is LoginVerifyFragment){
                    supportFragmentManager.popBackStack()
                }
            }
        }
        viewModel.mLoginSuccessLiveData.observe(this){
            startAnim()
        }
    }

    fun showLoginFragment(){
        FragmentUtils.replaceWithCommit(this,LoginFragment::class.java,R.id.fragment_container,false,null,false)
    }

    fun showVerifyFragment(){
        //清空回退栈
        if(supportFragmentManager.backStackEntryCount > 0){
            for(index in 0 .. supportFragmentManager.backStackEntryCount){
                supportFragmentManager.popBackStackImmediate()
            }
        }
        FragmentUtils.addWithCommit(this,LoginVerifyFragment::class.java,R.id.fragment_container,true,true,null)
    }

    fun showTeamFragment(){
        FragmentUtils.addWithCommit(this,LoginTeamFragment::class.java,R.id.fragment_container,true,true,null)
        if(viewModel.loginType == LoginType.TYPE_PHONE){
            FragmentUtils.removeWithCommit(this,LoginVerifyFragment::class.java.name)
        }
    }

    fun showUserFragment(){
        FragmentUtils.addWithCommit(this,LoginUserFragment::class.java,R.id.fragment_container,true,true,null)
        //一个租户下多账号直接跳选择账号页，要移除LoginVerifyFragment
        if(viewModel.loginType == LoginType.TYPE_PHONE){
            FragmentUtils.removeWithCommit(this,LoginVerifyFragment::class.java.name)
        }
    }

    fun startAnim() {
        if (iv_animation_img == null) {
            return
        }
        iv_animation_img.setVisibility(View.VISIBLE)
        iv_animation_img.startAnimation(mLogin_anim)
    }

    override fun onOperate(optionFlag: Int, args: Bundle?): Boolean {
        if (OperatingListener.OPERATE_LOGIN == optionFlag) {
            // 跳转至首页
            toIndex()
        }
        return false
    }

    fun toIndex() {
        if(isLaunchActivity()){
            startActivity(Intent(this,LoginSuccessActivity::class.java))
            finish()
            return
        }
//        EventBusMgr.getInstance().post(FINISH_LOGIN_ACTIVITY);
        if (intent != null && intent.getBooleanExtra(TabletLoginActivity.FROM_TABLET, false)) {
            setResult(TabletLoginActivity.RESULT_TO_INDEX)
            finish()
            return
        }
        if (!TextUtils.isEmpty(route_activity)) {
            Router.build(route_activity).go(AppBase.getAppContext())
        } else {
            // 主页
            Router.build(LoginActivity.getDefaultTabDeepLink()).go(AppBase.getAppContext())
        }
        finish()
    }

    fun isLaunchActivity() : Boolean{
        val intent = this.application.packageManager.getLaunchIntentForPackage(this.packageName)
        val launchComponentName = intent?.component
        val componentName = this.componentName
        return componentName.toString() == launchComponentName.toString()
    }
}