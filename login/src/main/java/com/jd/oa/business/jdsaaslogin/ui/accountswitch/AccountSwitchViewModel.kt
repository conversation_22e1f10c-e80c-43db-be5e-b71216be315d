package com.jd.oa.business.jdsaaslogin.ui.accountswitch

import android.os.CountDownTimer
import android.util.Base64
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.alibaba.fastjson.JSONObject
import com.jd.oa.AppBase
import com.jd.oa.business.LoginRepository
import com.jd.oa.business.jdsaaslogin.data.LoginErrorCode
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamData
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.ui.account.AccountDrawerFragment
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.JdSaasPromptUtil
import com.jd.oa.business.jdsaaslogin.util.WJLoginUtil
import com.jd.oa.db.greendao.YxDatabaseSession
import com.jd.oa.eventbus.EventBusMgr
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.JdSaasLoadingDialog
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.preference.JdSaasLoginPreference
import com.jd.oa.utils.DeviceUtil
import com.jd.oa.utils.ToastUtils
import com.jme.login.R

open class AccountSwitchViewModel(private val repository: LoginRepository) : ViewModel() {

    companion object{
        const val EVENT_ACCOUNT_SWITCH_SUCCESS = "accountSwitchSuccess"
    }

    val mWJLoginUtil : WJLoginUtil = WJLoginUtil()
    val mMsgCodeSendSuccessLiveData = MutableLiveData<Int>()
    val mMessageCodeAlreadySendLiveData = MutableLiveData<String>()
    val mClearMessageCodeLiveData = MutableLiveData<String>()
    val mLoginSuccessLiveData = MutableLiveData<String>()
    val mMsgCodeCountDownLiveData = MutableLiveData<Long>()
    val mOtherDeviceLoginLiveData = MutableLiveData<Int>()
    val mAccountExceptionLiveData = MutableLiveData<String>()
    var mMsgCodeExpireTime : Int? = null
    var mGetMessageCodeToken : String? = ""
    var mCheckMessageCodeToken : String? = ""
    var countDownTimer : CountDownTimer? = null
    var mTeamAccountInfo : TeamAccountInfo? = null
    var mTeamUserInfo : TeamUserInfo? = null

    fun loginByPassword(password: String?){
        mWJLoginUtil.loginByPassword(mTeamUserInfo?.pin,password,object : WJLoginUtil.LoginByPasswordCallback{
            override fun onLoginSuccess() {
                val jsonParams = JdSaasLoginHelper.getJsonParamsForPasswordLogin(mTeamUserInfo?.pin)
                getAccountList(LoginType.TYPE_ACCOUNT,jsonParams)
            }
        })
    }

    /*切换租户先坚持token是否有效，有效则直接上报登录态后切换*/
    fun loginByPhoneAfterCheckToken(){
        mWJLoginUtil.checkToken(mTeamUserInfo?.pin,object : WJLoginUtil.CheckTokenCallback{
            override fun onSuccess() {
                mWJLoginUtil.switchUserByPin(mTeamUserInfo?.pin)
                loginByToken(0)
            }

            override fun onError() {
                loginByPhone()
            }
        })
    }

    fun loginByPhone(){
        val activity = AppBase.getTopActivity()
        JdSaasLoadingDialog.showLoadingDialog(activity)
        mWJLoginUtil.loginByPhone(mTeamUserInfo?.countryCode,mTeamUserInfo?.phone,object : WJLoginUtil.LoginByPhoneCallback{
            override fun onGetMessageCode(msgCodeExpireTime: Int, getMessageCodeToken: String?) {
                JdSaasLoadingDialog.cancelLoadingDialog(activity)
                mMsgCodeSendSuccessLiveData.postValue(msgCodeExpireTime)
                mGetMessageCodeToken = getMessageCodeToken
                startMsgCodeExpireTimeCountDown(msgCodeExpireTime)
            }

            override fun onMessageCodeAlreadySend() {
                JdSaasLoadingDialog.cancelLoadingDialog(activity)
                mMessageCodeAlreadySendLiveData.postValue(null)
            }

            override fun onError() {
                JdSaasLoadingDialog.cancelLoadingDialog(activity)
            }
        })
    }

    fun checkMessageCodeByPhone(msgCode: String?,loginType: LoginType){
        mWJLoginUtil.checkMessageCodeForCompanyLogin(mTeamUserInfo?.phone,mTeamUserInfo?.countryCode,mGetMessageCodeToken,msgCode,object : WJLoginUtil.LoginByPhoneCallback{
            override fun onCheckMessageCode(jsonStr: String, checkMessageCodeToken: String) {
                mCheckMessageCodeToken = checkMessageCodeToken
                getAccountList(loginType,jsonStr)
            }
            override fun onError() {
                mClearMessageCodeLiveData.postValue(null)
            }
        })
    }

    fun loginWithName(){
        if(mTeamAccountInfo != null && mTeamUserInfo != null){
            mWJLoginUtil.loginWithNameForCompanyLogin(mCheckMessageCodeToken,mTeamUserInfo?.pin,object : WJLoginUtil.LoginByPhoneCallback{
                override fun onLoginWithName(loginName: String?) {
                    loginByToken(0)
                }
            })
        }
    }

    fun loginByToken(forceLogin : Int){
        val headers = hashMapOf<String,String>(
            "x-token" to mWJLoginUtil.getToken(),
            "x-user-id" to (mTeamUserInfo?.userId?:""),
            "x-did" to DeviceUtil.getDeviceUniqueId(),
            "x-team-id" to (mTeamAccountInfo?.teamId?:""),
            "Cookie" to "wskey=${mWJLoginUtil.getToken()}"
        )
        repository.loginByToken(forceLogin,mTeamUserInfo?.employeeId?:"",headers,
            object : SimpleRequestCallback<String>(AppBase.getTopActivity(),true){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                if(info?.errorCode == LoginErrorCode.OTHER_DEVICE_LOGIN){
                    mOtherDeviceLoginLiveData.postValue(forceLogin)
                }else if(info?.errorCode == LoginErrorCode.PIN_NULL ||
                    info?.errorCode == LoginErrorCode.TEAM_USER_OUT ||
                    info?.errorCode == LoginErrorCode.TEAM_STOP){
                    mAccountExceptionLiveData.postValue(info?.errorMessage)
                }else if (info?.isSuccessful?:false){
                    val response : ApiResponse<Map<String,Any>> = ApiResponse.parse(info?.result,Map::class.java)
                    if(response.data["successFlag"] == 0){
                        mOtherDeviceLoginLiveData.postValue(forceLogin)
                    }else{
                        loginSuccess()
                    }
                }else{
                    JdSaasPromptUtil.showErrorToast(info?.errorMessage)
                }
            }
            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
            }
        })
    }

    fun loginSuccess(){
        // 关闭数据库
        YxDatabaseSession.release()

        setUserInfo()
        updateAndSaveTeamData()
        mLoginSuccessLiveData.postValue(mWJLoginUtil.getToken())
        JdSaasPromptUtil.showJdSaasToast(AppBase.getAppContext(),AppBase.getAppContext().getString(R.string.jdsaas_login_switch_success),null,R.string.icon_prompt_circlecheck)
        EventBusMgr.getInstance().post(EVENT_ACCOUNT_SWITCH_SUCCESS)
    }

    fun getAccountList(loginType : LoginType,jsonString : String){
        val headers = hashMapOf<String,String>()
        if(loginType == LoginType.TYPE_PHONE){
            headers.put("x-tp",Base64.encodeToString(mTeamUserInfo?.phone?.toByteArray(),Base64.NO_WRAP))
        }
        repository.getAccountList(jsonString,headers,object : SimpleRequestCallback<String>(){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val apiResponse = ApiResponse.parse<TeamData>(info?.result, TeamData::class.java)
                if(info?.errorCode == LoginErrorCode.PIN_LIST_NULL
                    || info?.errorCode == LoginErrorCode.ME_TEAM_NULL
                    || info?.errorCode == LoginErrorCode.ME_EMPL_NULL){
                    mAccountExceptionLiveData.postValue(info.errorMessage)
                }else if(apiResponse.isSuccessful){
                    val teamAccountInfoList = apiResponse.data.teamAccountInfoList
                    if((teamAccountInfoList?.size ?: 0) == 0){
                        JdSaasPromptUtil.showErrorToast(AppBase.getAppContext().getString(R.string.jdsaas_login_data_exception))
                        return
                    }
                    teamAccountInfoList?.forEach {
                        if(it.teamId == mTeamAccountInfo?.teamId){
                            it.teamUserInfoList?.forEach {
                                if(it.pin == mTeamUserInfo?.pin){
                                    if(loginType == LoginType.TYPE_PHONE){
                                        loginWithName()
                                    }else if(loginType == LoginType.TYPE_ACCOUNT){
                                        loginByToken(0)
                                    }
                                }
                            }
                        }
                    }
                }else{
                    JdSaasPromptUtil.showErrorToast(apiResponse.errorMessage)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                JdSaasPromptUtil.showErrorToast(info)
            }
        })
    }

    fun setUserInfo(){
        JdSaasLoginHelper.setUserInfo(mWJLoginUtil.getToken(),mTeamUserInfo?.phone,mTeamAccountInfo,mTeamUserInfo)
    }

    fun updateAndSaveTeamData(){
        var teamDataJsonStr = JdSaasLoginPreference.get(JdSaasLoginPreference.KV_ENTITY_TEAM_DATA)
        val teamData = JSONObject.parseObject(teamDataJsonStr,TeamData::class.java)
        JdSaasLoginHelper.updateAndSaveTeamData(teamData,mTeamAccountInfo?.teamId,mTeamUserInfo?.pin)
    }

    fun startMsgCodeExpireTimeCountDown(msgCodeExpireTime : Int){
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer((msgCodeExpireTime+1) * 1000L,1000L){
            override fun onTick(time : Long) {
                mMsgCodeCountDownLiveData.postValue(time/1000)
            }

            override fun onFinish() {
            }
        }
        countDownTimer?.start()
    }

    fun stopMsgCodeExpireTimeCountDown(){
        countDownTimer?.cancel()
    }

    fun testTokenVerify(){
        repository.testTokenVerify(object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                if(info?.isSuccessful?:false){
                    ToastUtils.showToast("有效账号")
                }else{
                    JdSaasPromptUtil.showErrorToast(info?.errorMessage)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
            }
        })
    }
}