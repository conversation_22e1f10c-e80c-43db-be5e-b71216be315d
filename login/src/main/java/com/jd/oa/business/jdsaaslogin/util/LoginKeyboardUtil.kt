package com.jd.oa.business.jdsaaslogin.util

import android.content.Context
import android.view.View
import android.view.inputmethod.InputMethodManager

object LoginKeyboardUtil {

    fun showKeyboard(context: Context?,focusView : View){
        focusView.postDelayed(object : Runnable{
            override fun run() {
                focusView.requestFocus()
                showSoftInput(context,focusView)
            }
        },500)
    }

    fun showSoftInput(context : Context?,view: View){
        val imm = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
        imm?.showSoftInput(view,0)
        imm?.toggleSoftInput(0,0)
    }

    fun hideKeyboard(context: Context?,view: View){
        val imm = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
        imm?.hideSoftInputFromWindow(view.windowToken,0)
    }
}