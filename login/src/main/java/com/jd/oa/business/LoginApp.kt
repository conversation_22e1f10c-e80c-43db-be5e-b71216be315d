package com.jd.oa.business

import android.app.Activity
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.flutter.common.JDFHelper
import com.jd.oa.AppBase
import com.jd.oa.BaseApp
import com.jd.oa.CommonApp
import com.jd.oa.GlobalLocalLightBC
import com.jd.oa.MyPlatform
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jd.oa.abilities.apm.StartRunTimeMonitor
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.login.model.UserEntity
import com.jd.oa.preference.JDMETenantPreference
import com.jd.oa.preference.PreferenceManager
import com.jme.login.BuildConfig
import java.lang.ref.WeakReference

class LoginApp : BaseApp(), GlobalLocalLightBC.LightEventListener {

    companion object{
        const val TAG = "LoginApp"
    }

    override fun appInit() {
        registerApplicationInit(CommonApp::class.java)
        registerLifeActivityCallbacks()
        initBuildConfig()
    }

    private fun initBuildConfig() {
        AppBase.DEBUG = BuildConfig.DEBUG
        AppBase.SHOW_SERVER_SWITCHER = BuildConfig.SHOW_SERVER_SWITCHER
    }

    override fun onCreate() {
        super.onCreate()
        try {
            StartRunTimeMonitor.getInstance().record("Apps JDFHelper init")
            JDFHelper.getInstance().init(this)
            StartRunTimeMonitor.getInstance().end("Apps JDFHelper init")
            ApmLoaderHepler.getInstance(this).init(AppBase.DEBUG, BuildConfig.SHOW_SERVER_SWITCHER)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        initUser()
        registerLocalBroadCast()
    }

    fun initUser(){
        // 4.初始化当前登录用户
        val userName = PreferenceManager.UserInfo.getUserName()

        if (!TextUtils.isEmpty(userName)) {
            val user = UserEntity(
                userName,
                PreferenceManager.UserInfo.getUserRealName(),
                PreferenceManager.UserInfo.getUserCover(),
                PreferenceManager.UserInfo.getJdAccount(),
                PreferenceManager.UserInfo.getUserAttendance(),
                PreferenceManager.UserInfo.getUserSexFlag()
            )
            user.appId = PreferenceManager.UserInfo.getTimlineAppID()
            user.teamId = PreferenceManager.UserInfo.getTeamId()
            user.userId = PreferenceManager.UserInfo.getUserId()
            MyPlatform.setCurrentUser(user)
        }
    }

    fun registerLifeActivityCallbacks() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                AppBase.topActivity = WeakReference(activity)
            }

            override fun onActivityStarted(activity: Activity) {
            }

            override fun onActivityResumed(activity: Activity) {
                AppBase.topActivity = WeakReference(activity)
            }

            override fun onActivityPaused(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {
            }
        })
    }

    private fun registerLocalBroadCast() {
        var mNotifyDataChangedBroadcast: GlobalLocalLightBC? = null
        // 注册全局局部广播事件
        try {
            if (mNotifyDataChangedBroadcast != null) {
                val lbm = LocalBroadcastManager.getInstance(this)
                lbm.unregisterReceiver(mNotifyDataChangedBroadcast)
            }
            mNotifyDataChangedBroadcast = GlobalLocalLightBC(this)
            val filter = IntentFilter()
            val lbm = LocalBroadcastManager.getInstance(this)
            filter.addAction(GlobalLocalLightBC.ACTION)
            lbm.registerReceiver(mNotifyDataChangedBroadcast, filter)
        } catch (e: java.lang.Exception) {
            Log.e(TAG, "unregisterLocalNotifyReceiver:Exception:=$e")
        }
    }

    override fun onLightEventNotify(intent: Intent?) {
        val event = intent!!.getStringExtra(GlobalLocalLightBC.KEY_EVENT)
        if (GlobalLocalLightBC.EVENT_USER_KICK_OUT == event) {   // 全局踢出事件
            val b = intent!!.getBundleExtra(GlobalLocalLightBC.KEY_EVENT_VALUE)
            val message = if (b != null) b.getString("message") else ""
            val leavUrl = if (b != null) b.getString("leaveUrl") else ""
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG,message!!)
            JdSaasLoginHelper.logout()
        }
    }

}