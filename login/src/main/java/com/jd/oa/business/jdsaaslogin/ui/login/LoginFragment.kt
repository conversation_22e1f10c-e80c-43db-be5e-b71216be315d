package com.jd.oa.business

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.Layout
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.HideReturnsTransformationMethod
import android.text.method.LinkMovementMethod
import android.text.method.PasswordTransformationMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.LinearLayout
import android.widget.RadioGroup
import android.widget.RelativeLayout
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.LoginKeyboardUtil
import com.jd.oa.business.jdsaaslogin.util.LoginUtil
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.configuration.local.LocalConfigHelper
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.model.service.AppService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.network.NetworkConstant
import com.jd.oa.network.httpmanager.ColorGatewayNetEnvironment
import com.jd.oa.network.httpmanager.GatewayNetEnvironment
import com.jd.oa.network.httpmanager.NetEnvironment
import com.jd.oa.preference.JdSaasLoginPreference
import com.jd.oa.preference.JdSaasLoginPreference.KV_ENTITY_ACCOUNT
import com.jd.oa.preference.JdSaasLoginPreference.KV_ENTITY_ACCOUNT_SAVE_TIME
import com.jd.oa.preference.JdSaasLoginPreference.KV_ENTITY_PHONE
import com.jd.oa.preference.JdSaasLoginPreference.KV_ENTITY_PHONE_SAVE_TIME
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.Holder
import com.jd.oa.utils.NClick
import com.jd.oa.utils.PromptUtils
import com.jd.oa.utils.StringUtils
import com.jd.oa.utils.isVisible
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.jdme_server_switcher.view.rb_server_switcher_39
import kotlinx.android.synthetic.main.jdme_server_switcher.view.rb_server_switcher_55
import kotlinx.android.synthetic.main.jdme_server_switcher.view.rb_server_switcher_gy
import kotlinx.android.synthetic.main.jdme_server_switcher.view.rb_server_switcher_official
import kotlinx.android.synthetic.main.jdme_server_switcher.view.rg_server_switcher
import kotlinx.android.synthetic.main.jdme_server_switcher.view.tv_server_switcher_others
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.btn_next
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.cb_agreement
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.cb_password
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.et_account
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.et_password
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.et_phone
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.iv_account_clear
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.iv_password_clear
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.iv_phone_clear
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.layout_server_switcher
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.login_header
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.rl_account
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.rl_password
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.rl_phone
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.tv_account_last_login
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.tv_achieve_excellent_team_cn
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.tv_achieve_excellent_team_en
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.tv_forget_password
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.tv_phone_last_login
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.view_account_line
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.view_password_line
import kotlinx.android.synthetic.main.jdsaas_fragment_login.view.view_phone_line
import kotlinx.android.synthetic.main.jdsaas_fragment_login_header.view.tv_login_slogan
import kotlinx.android.synthetic.main.jdsaas_fragment_login_header.view.tv_login_welcome
import kotlinx.android.synthetic.main.jdsaas_fragment_login_type_choice.view.ll_account
import kotlinx.android.synthetic.main.jdsaas_fragment_login_type_choice.view.ll_phone
import kotlinx.android.synthetic.main.jdsaas_fragment_login_type_choice.view.rl_login_type_bg
import kotlinx.android.synthetic.main.jdsaas_fragment_login_type_choice.view.tv_account
import kotlinx.android.synthetic.main.jdsaas_fragment_login_type_choice.view.tv_phone
import kotlinx.android.synthetic.main.jdsaas_fragment_login_type_choice.view.view_account_choice
import kotlinx.android.synthetic.main.jdsaas_fragment_login_type_choice.view.view_phone_choice

class LoginFragment : BaseFragment() {

    lateinit var mView : View

    val viewModel by lazy { ViewModelProvider(requireActivity(),InjectorUtil.getLoginModelFactory()).get(LoginViewModel::class.java) }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.jdsaas_fragment_login,container,false)
        mView.login_header.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        setLoginHeader() //设置头部文字
        setAgreementText() //设置隐私协议文案
        setEditTextFocusLineColor() //设置输入框焦点获取和失去后底部线条颜色
        switchLoginType() //切换登录方式

        mView.et_phone.addTextChangedListener {
            setNextButtonEnable()
            if(TextUtils.isEmpty(it.toString())){
                mView.iv_phone_clear.visibility = View.GONE
            }else{
                mView.iv_phone_clear.visibility = View.VISIBLE
            }
        }
        mView.iv_phone_clear.setOnClickListener{
            mView.et_phone.text = null
            mView.tv_phone_last_login.visibility = View.GONE
        }

        mView.et_account.addTextChangedListener{
            setNextButtonEnable()
            if(TextUtils.isEmpty(it.toString())){
                mView.iv_account_clear.visibility = View.GONE
            }else{
                mView.iv_account_clear.visibility = View.VISIBLE
            }
        }
        mView.iv_account_clear.setOnClickListener{
            mView.et_account.text = null
            mView.tv_account_last_login.visibility = View.GONE
        }

        mView.cb_password.setOnClickListener {
            if(mView.cb_password.isChecked){
                mView.et_password.transformationMethod = HideReturnsTransformationMethod.getInstance()
            }else{
                mView.et_password.transformationMethod = PasswordTransformationMethod.getInstance()
            }
            mView.et_password.setSelection(mView.et_password.length())
        }

        mView.et_password.addTextChangedListener{
            setNextButtonEnable()
            if(TextUtils.isEmpty(it.toString())){
                mView.iv_password_clear.visibility = View.GONE
            }else{
                mView.iv_password_clear.visibility = View.VISIBLE
            }
        }
        mView.iv_password_clear.setOnClickListener{
            mView.et_password.text = null
        }
        mView.tv_forget_password.setOnClickListener {
            JdSaasLoginHelper.forgetPassword()
        }
        mView.btn_next.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            LoginKeyboardUtil.hideKeyboard(context,mView)
            if(mView.cb_agreement.isChecked){
                loginRequest()
            }else{
                showAgreementDialog()
            }
        }

        reDisplayLoginInfo() //回显

        if(LoginUtil.isTablet(requireActivity())){
            val tf = Typeface.createFromAsset(requireActivity().assets, "fonts/JDLangZhengTi_Regular.TTF")
            mView.tv_achieve_excellent_team_cn?.setTypeface(tf)
            mView.tv_achieve_excellent_team_en?.setTypeface(tf)
        }
        if (AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER) {
            initDebugView()
        }
        return mView
    }

    override fun onResume() {
        super.onResume()
        val loginType = JdSaasLoginPreference.get(JdSaasLoginPreference.KV_ENTITY_LAST_LOGIN_TYPE)
        if(loginType == LoginType.TYPE_ACCOUNT.name){
            mView.ll_account.performClick()
        }
    }

    private fun setLoginHeader(){
        val userName = JdSaasLoginPreference.get(JdSaasLoginPreference.KV_ENTITY_USERNAME)
        val avatar = JdSaasLoginPreference.get(JdSaasLoginPreference.KV_ENTITY_AVATAR)
        mView.tv_login_welcome.paint.isFakeBoldText = true
//        if(TextUtils.isEmpty(userName)){
//            mView.tv_login_welcome.setText(requireContext().getString(R.string.jdsaas_login_welcome))
//        }else{
//            mView.tv_login_welcome.setText("Hi，${userName}")
//        }
        mView.tv_login_welcome.setText(requireContext().getString(R.string.jdsaas_login_welcome))
        mView.tv_login_slogan.setText(requireContext().getString(R.string.jdsaas_login_me_saas_welcome))
//        JdSaasAvatarUtil.loadPersonalAvatar(requireActivity(),mView.iv_login_logo,avatar)
    }

    private fun switchLoginType() {
        mView.tv_phone.paint.isFakeBoldText = true
        mView.ll_phone.setOnClickListener {
            viewModel.loginType = LoginType.TYPE_PHONE
            mView.rl_login_type_bg.scaleX = 1f
            mView.tv_phone.setTextColor(resources.getColor(R.color.jdsaas_color_red_F63218,null))
            mView.tv_phone.paint.isFakeBoldText = true
            mView.view_phone_choice.visibility = View.VISIBLE
            mView.tv_account.setTextColor(resources.getColor(R.color.jdsaas_black_6A6A6A,null))
            mView.tv_account.paint.isFakeBoldText = false
            mView.view_account_choice.visibility = View.GONE
            mView.rl_phone.visibility = View.VISIBLE
            mView.rl_account.visibility = View.GONE
            mView.rl_password.visibility = View.GONE
            mView.tv_forget_password.visibility = View.GONE
            setNextButtonEnable()
        }
        mView.ll_account.setOnClickListener {
            viewModel.loginType = LoginType.TYPE_ACCOUNT
            mView.rl_login_type_bg.scaleX = -1f //背景镜像显示
            mView.tv_phone.setTextColor(resources.getColor(R.color.jdsaas_black_6A6A6A,null))
            mView.tv_phone.paint.isFakeBoldText = false
            mView.view_phone_choice.visibility = View.GONE
            mView.tv_account.setTextColor(resources.getColor(R.color.jdsaas_color_red_F63218,null))
            mView.tv_account.paint.isFakeBoldText = true
            mView.view_account_choice.visibility = View.VISIBLE
            mView.rl_phone.visibility = View.GONE
            mView.rl_account.visibility = View.VISIBLE
            mView.rl_password.visibility = View.VISIBLE
            mView.tv_forget_password.visibility = View.VISIBLE
            setNextButtonEnable()
        }
    }

    private fun setEditTextFocusLineColor() {
        val focusColor = resources.getColor(R.color.jdsaas_black_1B1B1B,null)
        val unFocusColor = resources.getColor(R.color.jdsaas_black_DCDEE0,null)
        //手机号登录
        mView.et_phone.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus){
                if(mView.tv_phone_last_login.isVisible()){
                    mView.tv_phone_last_login.visibility = View.GONE
                    mView.et_phone.text = null
                }
                mView.view_phone_line.setBackgroundColor(focusColor)
            }else{
                mView.view_phone_line.setBackgroundColor(unFocusColor)
            }
        }
        //账号密码登录
        mView.et_account.setOnFocusChangeListener { v, hasFocus ->
            if(hasFocus){
                if(mView.tv_account_last_login.isVisible()){
                    mView.tv_account_last_login.visibility = View.GONE
                    mView.et_account.text = null
                }
                mView.view_account_line.setBackgroundColor(focusColor)
            } else {
                mView.view_account_line.setBackgroundColor(unFocusColor)
            }
        }
        mView.et_password.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) mView.view_password_line.setBackgroundColor(focusColor)
            else mView.view_password_line.setBackgroundColor(unFocusColor)
        }
    }

    fun setNextButtonEnable(){
        if(viewModel.loginType == LoginType.TYPE_PHONE){
            if(mView.et_phone.length() > 0){
                mView.btn_next.isEnabled = true
            }else{
                mView.btn_next.isEnabled = false
            }
        }else if(viewModel.loginType == LoginType.TYPE_ACCOUNT){
            if(!TextUtils.isEmpty(mView.et_account.text) && !TextUtils.isEmpty(mView.et_password.text)){
                mView.btn_next.isEnabled = true
            }else{
                mView.btn_next.isEnabled = false
            }
        }
    }

    fun reDisplayLoginInfo(){
        //30天未登录，清空上次登录的手机号或账号
        val phoneLastSaveTime = JdSaasLoginPreference.get(KV_ENTITY_PHONE_SAVE_TIME)
        val accountLastSaveTime = JdSaasLoginPreference.get(KV_ENTITY_ACCOUNT_SAVE_TIME)
        val currentTime = System.currentTimeMillis()/1000
        val maxSaveTime = 30 * 24 * 60 * 60
        if(phoneLastSaveTime != 0L && currentTime - phoneLastSaveTime > maxSaveTime){
            JdSaasLoginPreference.put(KV_ENTITY_PHONE,"")
        }
        if(accountLastSaveTime != 0L && currentTime - accountLastSaveTime > maxSaveTime){
            JdSaasLoginPreference.put(KV_ENTITY_ACCOUNT,"")
        }
        val phone = JdSaasLoginPreference.get(KV_ENTITY_PHONE)
        val account = JdSaasLoginPreference.get(KV_ENTITY_ACCOUNT)
        mView.et_phone.setText(JdSaasLoginHelper.maskPhoneNumber(phone))
        mView.et_account.setText(account)

        if(!TextUtils.isEmpty(phone)){
            //设置手机号登录上次登录按钮的位置
            val lpPhone = mView.tv_phone_last_login.layoutParams as RelativeLayout.LayoutParams
            val paint = mView.et_phone.paint
            val text = mView.et_phone.text
            val width = Layout.getDesiredWidth(text.toString(),0,text.length,paint) + DisplayUtils.dip2px(59f+8f)
            lpPhone.setMargins(width.toInt(),0,0,0)
            mView.tv_phone_last_login.layoutParams = lpPhone
            mView.tv_phone_last_login.visibility = View.VISIBLE
        }
        if(!TextUtils.isEmpty(account)){
            //设置账号登录上次登录按钮的位置
            val lpAccount = mView.tv_account_last_login.layoutParams as RelativeLayout.LayoutParams
            val paint = mView.et_account.paint
            val text = mView.et_account.text
            val width = Layout.getDesiredWidth(text.toString(),0,text.length,paint) + DisplayUtils.dip2px(8f)
            lpAccount.setMargins(width.toInt(),0,0,0)
            mView.tv_account_last_login.layoutParams = lpAccount
            mView.tv_account_last_login.visibility = View.VISIBLE
        }
    }

    fun loginRequest(){
        if(viewModel.loginType == LoginType.TYPE_PHONE){
            var phone = mView.et_phone.text.toString()
            if(mView.tv_phone_last_login.isVisible()){
                phone = JdSaasLoginPreference.get(KV_ENTITY_PHONE)
            }
            viewModel.loginByPhone("86",phone)
        }else if(viewModel.loginType == LoginType.TYPE_ACCOUNT){
            val account = mView.et_account.text.toString()
            val password = mView.et_password.text.toString()
            viewModel.loginByPassword(account,password)
        }
    }

    fun setAgreementText(){
        val spanBuilder = SpannableStringBuilder(" ${requireContext().getString(R.string.jdsaas_login_aggrement_read_aggre)} ")
        var span = getSpannableString(requireContext().getString(R.string.jdsaas_login_privacy_aggrement),object : OnClickListener{
            override fun onClick(view: View?) {
                openPrivacyAggrement()
            }
        })
        spanBuilder.append(span)
        mView.cb_agreement.text = spanBuilder
        mView.cb_agreement.setMovementMethod(LinkMovementMethod.getInstance())
        mView.cb_agreement.setHighlightColor(Color.TRANSPARENT)
    }

    private fun showAgreementDialog() {
        val confirmDialog = ConfirmDialog(requireContext())
        confirmDialog.setTitle(requireContext().getString(R.string.jdsaas_login_aggrement_dialog_title))
        val spanBuilder = SpannableStringBuilder(requireContext().getString(R.string.jdsaas_login_aggrement_aggre))
        var span = getSpannableString("《${requireContext().getString(R.string.jdsaas_login_privacy_aggrement)}》",object : OnClickListener{
            override fun onClick(view: View?) {
                openPrivacyAggrement()
            }
        })
        spanBuilder.append(span)
        confirmDialog.setSpanMessage(spanBuilder)
        confirmDialog.setPositiveButton(getString(R.string.jdsaas_login_aggrement_dialog_confirm))
        confirmDialog.setPositiveClickListener(object : View.OnClickListener{
            override fun onClick(view : View?) {
                if (!NClick.isFastDoubleClick()) {
                    mView.cb_agreement.isChecked = true
                    loginRequest()
                }
            }
        })
        confirmDialog.show()
        NClick.clearLastClickTime()
    }

    private fun openPrivacyAggrement(){
        val appService = AppJoint.service(AppService::class.java)
        val url: String = appService.getPorivacyPolicy()
        Router.build(DeepLink.webUrl(url, 1)).go(requireActivity())
    }

    fun getSpannableString(text : String,listener : OnClickListener?) : SpannableString{
        var span = SpannableString(text)
        span.setSpan(object : ClickableSpan(){
            override fun onClick(view: View) {
                listener?.onClick(view)
            }
            override fun updateDrawState(ds: TextPaint) {
                ds.isUnderlineText = false
            }
        },0,span.length,Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        span.setSpan(ForegroundColorSpan(resources.getColor(R.color.jdsaas_color_blue_1869F5,null)),0,span.length,Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        return span
    }

    //初始化debug版本时的控件
    private fun initDebugView() {
        val viewStub : ViewStub = mView.layout_server_switcher
        val ll_server_switcher = viewStub.inflate() as LinearLayout
        val rg_server_switcher = mView.rg_server_switcher
        val rb_server_switcher_39 = mView.rb_server_switcher_39
        val rb_server_switcher_55 = mView.rb_server_switcher_55
        val rb_server_switcher_gy = mView.rb_server_switcher_gy
        val rb_server_switcher_official = mView.rb_server_switcher_official
        val tv_server_switcher_others = mView.tv_server_switcher_others
        val netEnvironmentConfig =
            LocalConfigHelper.getInstance(requireContext()).netEnvironmentConfig
        var currentEnv = PreferenceManager.UserInfo.getNetEnvironment()
        if (TextUtils.isEmpty(currentEnv)) {
            currentEnv = netEnvironmentConfig.env
        }
        if (NetEnvironmentConfigModel.TEST == currentEnv) {
            rb_server_switcher_39.setChecked(true)
        } else if (NetEnvironmentConfigModel.JDOS == currentEnv) {
            rb_server_switcher_55.setChecked(true)
        } else if (NetEnvironmentConfigModel.PROD == currentEnv) {
            rb_server_switcher_official.setChecked(true)
        } else if (NetEnvironmentConfigModel.PREV == currentEnv) {
            rb_server_switcher_gy.setChecked(true)
        } else {
            tv_server_switcher_others.setChecked(true)
            tv_server_switcher_others.setText(
                tv_server_switcher_others.getContext().getString(R.string.me_feedback_type_other)
            )
        }
        rg_server_switcher.setOnCheckedChangeListener(RadioGroup.OnCheckedChangeListener { group, checkedId ->
            val holder = Holder<String>()
            holder.set(NetEnvironmentConfigModel.PROD)
            if (checkedId == R.id.rb_server_switcher_39) {
                holder.set(NetEnvironmentConfigModel.TEST)
            } else if (checkedId == R.id.rb_server_switcher_55) {
                holder.set(NetEnvironmentConfigModel.JDOS)
            } else if (checkedId == R.id.rb_server_switcher_gy) {
                holder.set(NetEnvironmentConfigModel.PREV)
            } else if (checkedId == R.id.tv_server_switcher_others) {
                var defText: String? = getString(R.string.me_string_server_switcher_hint)
                val address = PreferenceManager.Other.getCustomServerAddress()
                if (!TextUtils.isEmpty(address)) {
                    defText = address
                }
                PromptUtils.showInputDialog(
                    activity, R.string.me_string_server_switcher_title,
                    R.string.me_string_server_switcher_title,
                    defText, false
                ) { activity, isCancel, message ->
                    val address: String
                    address = if (StringUtils.isNotEmptyWithTrim(message.toString())) {
                        if (message.toString().contains("http://") || message.toString()
                                .contains("https://")
                        ) {
                            message.toString()
                        } else {
                            "http://$message"
                        }
                    } else {
                        NetworkConstant.ADDRESS_SERVER_PERSONAL_PC
                    }
                    PreferenceManager.Other.setCustomServerAddress(address)
                    holder.set(NetEnvironmentConfigModel.CUSTOM)
                    tv_server_switcher_others.setText(
                        tv_server_switcher_others.getContext()
                            .getString(R.string.me_feedback_type_other) + address
                    )
                }
            } else {
                holder.set(NetEnvironmentConfigModel.PROD)
            }
            val legacy: NetEnvironment
            val gateway: GatewayNetEnvironment
            val chooseEnv = holder.get()!!
            legacy = if (NetEnvironmentConfigModel.CUSTOM == chooseEnv) {
                val address = PreferenceManager.Other.getCustomServerAddress()
                NetEnvironment(address, address)
            } else {
                netEnvironmentConfig.getNonGateway(chooseEnv)
            }
            gateway = netEnvironmentConfig.getGateway(chooseEnv)
            NetEnvironment.setCurrentEnv(legacy)
            GatewayNetEnvironment.setCurrentEnv(gateway)
            ColorGatewayNetEnvironment.setCurrentEnv(netEnvironmentConfig.getColorGateway(chooseEnv))
            PreferenceManager.UserInfo.setNetEnvironment(chooseEnv)
            viewModel.mWJLoginUtil.setWJLoginEnv() //统一登录环境切换
            Thread { ConfigurationManager.get().syncConfigurations() }.start()
        })
    }
}