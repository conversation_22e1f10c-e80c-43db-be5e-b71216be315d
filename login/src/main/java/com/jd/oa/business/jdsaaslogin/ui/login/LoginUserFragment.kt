package com.jd.oa.business

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.ui.login.LoginTeamHeaderSection
import com.jd.oa.business.jdsaaslogin.ui.login.LoginUserItemSection
import com.jd.oa.business.jdsaaslogin.ui.login.TYPE_PIN
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.fragment.BaseFragment
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter
import kotlinx.android.synthetic.main.jdsaas_fragment_login_user.view.rv_pin
import kotlinx.android.synthetic.main.jdsaas_fragment_login_user.view.titleBar
import kotlinx.android.synthetic.main.jdsaas_login_titlebar.view.iv_back

class LoginUserFragment : BaseFragment(), LoginUserItemSection.ClickListener {
    val viewModel by lazy { ViewModelProvider(requireActivity(),InjectorUtil.getLoginModelFactory()).get(LoginViewModel::class.java) }
    lateinit var mView: View
    var teamAccountInfo : TeamAccountInfo? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.jdsaas_fragment_login_user,container,false)
        mView.titleBar.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        mView.iv_back.setOnClickListener {
            backPress()
        }

        val sectionedAdapter = SectionedRecyclerViewAdapter()
        val loginTeamHeaderSection = LoginTeamHeaderSection(requireContext(),TYPE_PIN)
        teamAccountInfo = viewModel.mToLoginUserFragmentLiveData.value
        if(viewModel.loginType == LoginType.TYPE_PHONE){
            loginTeamHeaderSection.userRepresent = "+${viewModel.mCountryCode}${JdSaasLoginHelper.maskPhoneNumber(viewModel.mPhone)}"
        }else if(viewModel.loginType == LoginType.TYPE_ACCOUNT){
            loginTeamHeaderSection.userRepresent = viewModel.mAccount
        }
        loginTeamHeaderSection.teamAccountInfo = teamAccountInfo
        sectionedAdapter.addSection(loginTeamHeaderSection)
        sectionedAdapter.addSection(LoginUserItemSection(requireContext(),teamAccountInfo?.teamUserInfoList,this))
        val manager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        mView.rv_pin.setLayoutManager(manager)
        mView.rv_pin.adapter = sectionedAdapter
        return mView
    }

    override fun onBackPressed(): Boolean {
        backPress()
        return true
    }

    private fun backPress(){
        requireActivity().supportFragmentManager.popBackStack()
    }

    override fun onItemClickListener(
        section: LoginUserItemSection,
        position: Int,
        teamUserInfo : TeamUserInfo?
    ) {
        viewModel.loginWithName(teamAccountInfo,teamUserInfo)
    }
}