package com.jd.oa.business.jdsaaslogin.ui.account

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder
import com.jme.login.R

class UserDrawerAdapter(val context : Context,companyUsers : List<TeamUserInfo>?)
    : BaseRecyclerViewAdapter<TeamUserInfo>(context,companyUsers) {
    override fun getItemLayoutId(viewType: Int): Int {
        return R.layout.jdsaas_user_drawer_item
    }

    override fun onConvert(holder: BaseRecyclerViewHolder, item: TeamUserInfo?, position: Int) {

        holder.setText(R.id.tv_user_name,item?.realName)
        val iv_user_avatar = holder.getView<ImageView>(R.id.iv_user_avatar)
        val tv_user_status = holder.getView<TextView>(R.id.tv_user_status)
        JdSaasAvatarUtil.loadPersonalAvatar(context,iv_user_avatar,item?.avatar)

        if(item?.loginCurrently?:false){
            holder.setVisible(R.id.iv_user_selected,View.VISIBLE)
            holder.setVisible(R.id.iv_user_avatar_mask_layer, View.GONE)
            holder.setTextColor(R.id.tv_user_name,context.getColor(R.color.jdsaas_black_1B1B1B))
            tv_user_status.visibility = View.GONE
        } else if(item?.activeStatus == 0){
            holder.setVisible(R.id.iv_user_selected,View.GONE)
            holder.setVisible(R.id.iv_user_avatar_mask_layer, View.VISIBLE)
            holder.setTextColor(R.id.tv_user_name,context.getColor(R.color.jdsaas_black_6A6A6A))
            tv_user_status.visibility = View.VISIBLE
            tv_user_status.text = context.getString(R.string.jdsaas_login_unactivated)
            tv_user_status.background = context.getDrawable(R.drawable.jdsaas_tv_team_status_unactivated)
            tv_user_status.setTextColor(context.getColor(R.color.jdsaas_color_blue_4A5FE8))
        }
    }

}