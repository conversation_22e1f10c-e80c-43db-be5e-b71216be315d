package com.jd.oa.business.jdsaaslogin.ui.scan

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.chenenyu.router.annotation.Route
import com.jd.oa.SingleFragmentActivity
import com.jd.oa.business.InjectorUtil
import com.jd.oa.router.DeepLink
import org.json.JSONObject

@Route(DeepLink.JDSAAS_LOGIN_SCAN)
class LoginScanActivity : SingleFragmentActivity() {

    val viewModel by lazy { ViewModelProvider(this, InjectorUtil.getLoginScanModelFactory()).get(
        LoginScanViewModel::class.java) }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val mparam = intent.getStringExtra("mparam")
        val url = mparam?.let { JSONObject(it).getString("url").trim() }?:""
        viewModel.mScannedQRCodeUrl = url
    }

    override fun createFragment(): Fragment {
        return LoginScanFragment()
    }
}