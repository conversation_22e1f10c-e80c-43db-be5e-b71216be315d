package com.jd.oa.business.jdsaaslogin.ui.scan

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.AppBase
import com.jd.oa.business.jdsaaslogin.data.LoginScanRepository
import com.jd.oa.business.jdsaaslogin.util.JdSaasPromptUtil
import com.jd.oa.business.jdsaaslogin.util.WJLoginUtil
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jme.login.R
import jd.wjlogin_sdk.common.listener.OnCommonCallback
import jd.wjlogin_sdk.common.listener.OnDataCallback
import jd.wjlogin_sdk.model.ErrorResult
import jd.wjlogin_sdk.model.FailResult
import jd.wjlogin_sdk.model.QRCodeScannedResult

class LoginScanViewModel(private val repository: LoginScanRepository) : ViewModel() {


    companion object{
        const val TAG = "LoginScanViewModel"
    }

    val mWJLoginUtil : WJLoginUtil = WJLoginUtil()
    val mCancelQRCodeLoginLiveData = MutableLiveData<String>()
    val mQRCodeLoginSuccessLiveData = MutableLiveData<String>()
    var mScannedQRCodeUrl : String = ""
    var mQrCodeKey : String = ""

    fun verifyQRCode(){
        mWJLoginUtil.verifyQRCode(mQrCodeKey,2,object : OnCommonCallback(){
            override fun onSuccess() {
                confirmQRCodeScanned()
            }

            override fun onError(errorResult: ErrorResult) {
            }

            override fun onFail(failResult: FailResult) {
            }
        })
    }

    fun getQRCodeKeyFromUrl(){
        mQrCodeKey =  mWJLoginUtil.getQRCodeKeyFromUrl(mScannedQRCodeUrl)
        confirmQRCodeScanned()
    }

    /*更新为扫描状态*/
    fun  confirmQRCodeScanned(){
        mWJLoginUtil.confirmQRCodeScanned(mQrCodeKey,object : OnDataCallback<QRCodeScannedResult>(){
            override fun onSuccess(p0: QRCodeScannedResult?) {
            }

            override fun onError(errorResult: ErrorResult) {
                showErrorToast(errorResult.errorCode)
            }

            override fun onFail(failResult: FailResult) {
                showErrorToast(failResult.replyCode.toInt())
            }
        })
    }

    fun confirmQRCodeLogined(){
        mWJLoginUtil.confirmQRCodeLogined(mQrCodeKey,object : OnCommonCallback(){
            override fun onSuccess() {
                repository.mobileScanRecord(object : SimpleRequestCallback<String>() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        mQRCodeLoginSuccessLiveData.postValue(null)
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                    }
                })
            }

            override fun onError(errorResult: ErrorResult) {
                showErrorToast(errorResult.errorCode)
            }

            override fun onFail(failResult: FailResult) {
                showErrorToast(failResult.replyCode.toInt())
            }

        })
    }

    fun cancelQRCodeLogined(){
        mWJLoginUtil.cancelQRCodeLogined(mQrCodeKey,object : OnCommonCallback(){
            override fun onSuccess() {}
            override fun onError(errorResult: ErrorResult) {}
            override fun onFail(failResult: FailResult) {}
        })
        mCancelQRCodeLoginLiveData.postValue(null)
    }

    fun showErrorToast(code : Int){
        var errorMsg = ""
        if(code == 0x1){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_data_failed)
        }else if(code == 0x36){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_qr_invalid)
        }else if(code == 0x37){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_qr_expired)
        }else if(code == 0xb){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_account_invalid)
        }else if(code == 0xc){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_account_expired)
        }else if(code == 0xd){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_password_updated)
        }else if(code == 0xe){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_account_security)
        }else if(code == 0xa5){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_account_logged_out)
        }else if(code == 0xa6){
            errorMsg = AppBase.getAppContext().getString(R.string.jdsaas_login_scan_account_status_expired)
        }
        if(!TextUtils.isEmpty(errorMsg)){
            JdSaasPromptUtil.showErrorToast(errorMsg)
        }
    }
}