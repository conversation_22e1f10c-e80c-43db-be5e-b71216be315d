package com.jd.oa.business.jdsaaslogin.ui.success

import android.os.Bundle
import android.view.Window
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import com.chenenyu.router.Router
import com.jd.oa.BaseActivity
import com.jd.oa.business.LoginRepository
import com.jd.oa.business.jdsaaslogin.ui.account.AccountDrawerFragment
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginDialog
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.LoginUtil.setDrawerLeftEdgeSize
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.activity_login_success.btn_logout
import kotlinx.android.synthetic.main.activity_login_success.btn_open
import kotlinx.android.synthetic.main.activity_login_success.btn_personal_center
import kotlinx.android.synthetic.main.activity_login_success.btn_test_token
import kotlinx.android.synthetic.main.activity_login_success.drawerLayout
import kotlinx.android.synthetic.main.activity_login_success.fragment_account

class LoginSuccessActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this)
        }
        setContentView(R.layout.activity_login_success)
        initDrawerLayout()

        btn_logout.setOnClickListener {
            JdSaasLoginDialog.showLogoutDialog(this){
                JdSaasLoginHelper.logout()
            }
        }
        btn_test_token.setOnClickListener {
            LoginRepository().testTokenVerify(object : SimpleRequestCallback<String>() {})
        }
        btn_personal_center.setOnClickListener {
            Router.build(DeepLink.PERSONAL_CENTER_LAUNCH_ACTIVITY).go(this);
        }
    }

    private fun initDrawerLayout() {
        drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
        setDrawerLeftEdgeSize(this, drawerLayout, 0.2f)
        val accountFragment = fragment_account as AccountDrawerFragment
        accountFragment.setOnAccountDrawerCloseListener{
            drawerLayout.closeDrawer(GravityCompat.START)
        }
        btn_open.setOnClickListener {
            drawerLayout.openDrawer(GravityCompat.START)
            accountFragment.refreshData()
        }
    }

}