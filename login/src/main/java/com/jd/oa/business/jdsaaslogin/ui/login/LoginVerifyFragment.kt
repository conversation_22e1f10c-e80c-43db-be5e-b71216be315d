package com.jd.oa.business

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.ui.view.LoginVerifyCodeView
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.LoginKeyboardUtil
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.NClick
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.jdsaas_fragment_login_verify.view.btn_verify_next
import kotlinx.android.synthetic.main.jdsaas_fragment_login_verify.view.lvcv_verify_code
import kotlinx.android.synthetic.main.jdsaas_fragment_login_verify.view.titleBar
import kotlinx.android.synthetic.main.jdsaas_fragment_login_verify.view.tv_verify_code
import kotlinx.android.synthetic.main.jdsaas_fragment_login_verify.view.tv_verify_tip2
import kotlinx.android.synthetic.main.jdsaas_login_titlebar.view.iv_back

class LoginVerifyFragment : BaseFragment() {

    val viewModel by lazy { ViewModelProvider(requireActivity(),InjectorUtil.getLoginModelFactory()).get(LoginViewModel::class.java) }
    lateinit var mView : View
    var mMsgCode : String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.mClearMessageCodeLiveData.observe(requireActivity()){
            if(::mView.isInitialized){
                mView.lvcv_verify_code?.setEmpty()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.jdsaas_fragment_login_verify,container,false)
        if (StatusBarConfig.enableImmersive()) {
            mView.titleBar.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        }

        mView.iv_back.setOnClickListener{
            LoginKeyboardUtil.hideKeyboard(context,mView.lvcv_verify_code)
            requireActivity().supportFragmentManager.popBackStack()
        }

        setTvVerifyTip2()

        LoginKeyboardUtil.showKeyboard(context,mView.lvcv_verify_code)
        //监听验证码输入
        mView.lvcv_verify_code.onCodeFinishListener = object : LoginVerifyCodeView.OnCodeFinishListener{
            override fun onTextChange(view: View?, content: String?) {
                mView.btn_verify_next.isEnabled = false
            }

            override fun onComplete(view: View?, content: String?) {
                mMsgCode = content
                mView.btn_verify_next.isEnabled = true
                checkMessageCode()
            }
        }
        //下一步
        mView.btn_verify_next.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            if(mMsgCode?.length == 6){
                checkMessageCode()
            }
        }
        //设置短信验证码倒计时时间
        setMsgCodeCountDownTime()
        return mView
    }

    fun checkMessageCode(){
        viewModel.checkMessageCodeByPhone(mMsgCode,LoginType.TYPE_PHONE)
        LoginKeyboardUtil.hideKeyboard(context,mView.lvcv_verify_code)
    }

    private fun setTvVerifyTip2() {
        val msgCodeExpireTime = viewModel.mMsgCodeSendSuccessLiveData.value
        if(msgCodeExpireTime != null){
            val spanBuilder = SpannableStringBuilder(getString(R.string.jdsaas_login_verifycode_send))
            val span = SpannableString("+${viewModel.mCountryCode}${JdSaasLoginHelper.maskPhoneNumber(viewModel.mPhone)}")
            span.setSpan(StyleSpan(Typeface.BOLD),0,span.length,Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            span.setSpan(ForegroundColorSpan(resources.getColor(R.color.jdsaas_black_1B1B1B)),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            spanBuilder.append(span)
            spanBuilder.append("，${getString(R.string.jdsaas_login_verifycode_valid,(msgCodeExpireTime / 60).toString())}")
            mView.tv_verify_tip2.setText(spanBuilder)
        }
    }

    private fun setMsgCodeCountDownTime() {
        viewModel.mMsgCodeCountDownLiveData.observe(requireActivity()){
            mView.tv_verify_code.setText(requireActivity().getString(R.string.jdsaas_login_verifycode_countdown,it.toString()))
            if(it == 0L){
                val spanBuilder = SpannableStringBuilder(requireActivity().getString(R.string.jdsaas_login_verifycode_not_received))
                var span = SpannableString(requireActivity().getString(R.string.jdsaas_login_verifycode_reacquire))
                span.setSpan(object : ClickableSpan(){
                    override fun onClick(view: View) {
                        if (NClick.isFastDoubleClick()) {
                            return
                        }
                        viewModel.loginByPhone(viewModel.mCountryCode,viewModel.mPhone)
                        mView.lvcv_verify_code.setEmpty()
                    }
                    override fun updateDrawState(ds: TextPaint) {
                        ds.isUnderlineText = false
                    }
                },0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                span.setSpan(ForegroundColorSpan(Color.RED),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                spanBuilder.append(span)
                mView.tv_verify_code.setText(spanBuilder)
                mView.tv_verify_code.setMovementMethod(LinkMovementMethod.getInstance())
                mView.tv_verify_code.setHighlightColor(Color.TRANSPARENT)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.mMsgCodeCountDownLiveData.removeObservers(requireActivity())
    }
}