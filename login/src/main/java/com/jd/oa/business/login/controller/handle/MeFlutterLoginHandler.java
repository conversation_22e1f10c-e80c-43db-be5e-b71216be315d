package com.jd.oa.business.login.controller.handle;

import static com.jd.flutter.common.JDFHelper.callFlutter;
import static com.jd.oa.business.liveness.LivenessLoginFragment.liveLogin;
import static com.jd.oa.business.login.controller.LoginFragment.checkResponseSign;
import static com.jd.oa.business.login.controller.LoginFragment.decryptResponse;
import static com.jd.oa.business.login.controller.LoginFragment.loginSuccessData;
import static com.jd.oa.business.login.controller.LoginFragment.loginSuccessUI;
import static com.jd.oa.network.NetworkConstant.getCurrentServerName;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Handler;
import android.text.TextUtils;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.listener.MyDialogDoneListener;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.TabbarService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.ServerName;
import com.jd.oa.network.gateway.ServeConfig;
import com.jd.oa.network.httpmanager.ColorGatewayNetEnvironment;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.preference.FlutterPreference;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.Holder;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.Utils2Activity;
import com.jd.oa.utils.Utils2String;
import com.jd.push.JDPushManager;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;
import com.jme.login.R;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class MeFlutterLoginHandler implements IJDFChannelHandler {
    public static final String FLUTTER_BUSINESS_LOGIN = "com.jdme.flutter/business/login";

    @Override
    public String getModuleName() {
        return FLUTTER_BUSINESS_LOGIN;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        HashMap<String, String> result = new HashMap<>();
        switch (methodName) {
            case "storeServerPublicKey":
                try {
                    String key = (String) map.get("key");
                    KeyManager.getInstance().storeServerPublicKey(key);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                resultMap.success(result);
                break;
            case "getRSAPublicKey":
                result.put("key", KeyManager.getInstance().getPublicKey());
                resultMap.success(result);
                break;
            case "login":
                try {
                    String json = (String) map.get("json");
                    String pwd = (String) map.get("pwd");
                    flutterLogin(json, pwd);
                    setLoginSuccessInfo();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                resultMap.success(result);
                break;
            case "changeLanguage":
//                try {
//                    String language = (String) map.get("language");
//                    final String[] lang = language.split("_");// zh_CN
//                    Locale locale;
//                    if (lang.length == 2) {
//                        locale = new Locale(lang[0], lang[1]);
//                    } else {
//                        locale = new Locale(lang[0], "");
//                    }
//                    changLanguage(locale);
//                    LocaleUtils.changeLanguage(AppBase.getTopActivity(), locale, null);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                resultMap.success(result);
                break;
            case "loginFaceRecognition":
                // 切换到刷脸登录
                toLoginByLive();
                resultMap.success(result);
                break;
            case "debugModeServerSelector":
                showServerChoiceDialog(resultMap);
                break;
            case "erpInfoFillin":
                //把erp带过去
                String userErp = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME);//把用户名带过去
                String local = LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext());
                String forgetPassword = "https://autherp.jd.com/inside/res/H5/index.html#/sso/ResetPwd?type=1&erp=" + userErp + "&lang=" + local;
                Router.build(DeepLink.webUrl(forgetPassword)).go(Utils2Activity.getTopActivity());
                resultMap.success(result);
                break;
            case "nativeInit":
                result.put("showFaceLoginEntry", (PreferenceManager.UserInfo.hasFaceLoginPermission()
                        && "1".equals(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD)))
                        ? "1" : "0");
                result.put("showServerSelector", (AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER) ? "1" : "0");
                resultMap.success(result);
                break;
            case "getPrivacyPolicy":
                AppService appService = AppJoint.service(AppService.class);
                String url = appService.getPorivacyPolicy();
                result.put("url", url);
                resultMap.success(result);
            default:
                resultMap.notImplemented();
                break;
        }
    }

    // 登录成功，同步信息给flutter
    private static void setLoginSuccessInfo() {

        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_DID, DeviceUtil.getDeviceUniqueId());
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_CLIENT, "Android");
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_TENANTCODE, MyPlatform.getCurrentUser().getTenantCode());

        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_USERNAME, JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME));
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_REALNAME, PreferenceManager.UserInfo.getUserRealName());
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_USERICON, PreferenceManager.UserInfo.getUserCover());

        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_APPID, PreferenceManager.UserInfo.getTimlineAppID());
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_TEAMID, PreferenceManager.UserInfo.getTeamId());
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_USERID, PreferenceManager.UserInfo.getUserId());

        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_NETENVIRONMENT, getCurrentServerName().getName());
        String deviceToken = JDPushManager.getDeviceToken(AppBase.getAppContext());
        if (!TextUtils.isEmpty(deviceToken)) {
            FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_DEVICEPTOKEN, deviceToken);
        }

        callFlutter(FLUTTER_BUSINESS_LOGIN, "updateSp", null, null);
    }

    private void toLoginByLive() {
        String erp = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME);
        if (Utils2String.isNotEmptyWithTrim(erp)) {
            liveLogin(erp);
        }
    }


    @SuppressWarnings("rawtypes")
    private void showServerChoiceDialog(final IJDFMessageResult<Map> resultMap) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        String op0 = activity.getString(R.string.me_serve_three);
        String op1 = activity.getString(R.string.me_serve_five);
        String op2 = activity.getString(R.string.me_serve_pre_release);
        String op3 = activity.getString(R.string.me_serve_release);
        String op4 = activity.getString(R.string.me_serve_other);

        ServerName checkedItem = getCurrentServerName();

        final String[] items = {op0, op1, op2, op3, op4};

        AlertDialog.Builder singleChoiceDialog =
                new AlertDialog.Builder(activity);

        singleChoiceDialog.setTitle(activity.getString(R.string.me_choose_server));

        singleChoiceDialog.setSingleChoiceItems(items, checkedItem.index,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        onChangedServer(which);
                    }
                });
        singleChoiceDialog.setPositiveButton(R.string.me_ok,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        try {
                            resultMap.success(new HashMap<>());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
        singleChoiceDialog.setCancelable(false);
        singleChoiceDialog.show();
    }


    @SuppressLint("WrongConstant")
    public void onChangedServer(int checkedId) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        final Holder<String> holder = new Holder<>();
        if (checkedId == 0) {
            holder.set(NetEnvironmentConfigModel.TEST);
        } else if (checkedId == 1) {
            holder.set(NetEnvironmentConfigModel.JDOS);
        } else if (checkedId == 2) {
            holder.set(NetEnvironmentConfigModel.PREV);
        } else if (checkedId == 3) {
            holder.set(NetEnvironmentConfigModel.PROD);
        } else if (checkedId == 4) {
            String defText = activity.getString(R.string.me_string_server_switcher_hint);
            String address = PreferenceManager.Other.getCustomServerAddress();
            if (!TextUtils.isEmpty(address)) {
                defText = address;
            }
            PromptUtils.showInputDialog(activity, R.string.me_string_server_switcher_title,
                    R.string.me_string_server_switcher_title,
                    defText, false, new MyDialogDoneListener() {
                        @Override
                        public void onDialogDone(Activity activity, boolean isCancel, CharSequence message) {
                            String address;
                            if (StringUtils.isNotEmptyWithTrim(message.toString())) {
                                if (message.toString().contains("http://") || message.toString().contains("https://")) {
                                    address = message.toString();
                                } else {
                                    address = "http://" + message.toString();
                                }
                            } else {
                                address = NetworkConstant.ADDRESS_SERVER_PERSONAL_PC;
                            }
                            PreferenceManager.Other.setCustomServerAddress(address);

                            holder.set(NetEnvironmentConfigModel.CUSTOM);
                        }
                    });
        } else {
            holder.set(NetEnvironmentConfigModel.PROD);
        }

        NetEnvironmentConfigModel netEnvironmentConfig = LocalConfigHelper.getInstance(activity).getNetEnvironmentConfig();
        NetEnvironment legacy;
        GatewayNetEnvironment gateway;
        String chooseEnv = holder.get();
        if (NetEnvironmentConfigModel.CUSTOM.equals(chooseEnv)) {
            String address = PreferenceManager.Other.getCustomServerAddress();
            legacy = new NetEnvironment(address, address);
        } else {
            legacy = netEnvironmentConfig.getNonGateway(chooseEnv);
        }
        gateway = netEnvironmentConfig.getGateway(chooseEnv);

        NetEnvironment.setCurrentEnv(legacy);
        GatewayNetEnvironment.setCurrentEnv(gateway);
        ColorGatewayNetEnvironment.setCurrentEnv(netEnvironmentConfig.getColorGateway(chooseEnv));

        PreferenceManager.UserInfo.setNetEnvironment(chooseEnv);

        new Thread(new Runnable() {
            @Override
            public void run() {
                ConfigurationManager.get().syncConfigurations();
            }
        }).start();
        setLoginSuccessInfo();
    }

    // flutter login success
    void flutterLogin(String json, final String pwd) {
        final JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(json == null ? "{}" : json);
            JSONObject contentJson = jsonObject.optJSONObject("content");
            if (contentJson == null) {
                return;
            }
            final Activity activity = AppBase.getTopActivity();
            // 是否首次登录
//            final boolean isFiirstLogin  = TextUtils.isEmpty(PreferenceManager.UserInfo.getUserName());
            String sign = contentJson.optString("sign");
            String encryptData = contentJson.optString("User");
            if (checkResponseSign(encryptData, sign)) {
                final String data = decryptResponse(encryptData);
                loginSuccessData(data, pwd);  // 通知login事件
                PromptUtils.showLoadDialog(activity, AppBase.getAppContext().getString(R.string.me_logining));
                ServeConfig.getInstance(activity).getRemoteConfig(activity);
                // 获取Tabbar
                final TabbarService tabbarService = AppJoint.service(TabbarService.class);
                tabbarService.getTabbarConfig(new TabbarService.ITabbarConfigCallback() {
                    @Override
                    public void onSucsess() {
                        PromptUtils.removeLoadDialog(activity);
                        loginSuccessUI();
                        new Handler(AppBase.getAppContext().getMainLooper()).post(new Runnable() {
                            @Override
                            public void run() {
                                if (activity != null) {
                                    InputMethodUtils.hideSoftInput(activity);
                                }
                                startAnim(activity);
                            }
                        });
                    }

                    @Override
                    public void onSuccess(String info) {

                    }

                    @Override
                    public void onFailed() {
                        PromptUtils.removeLoadDialog(activity);
                        loginSuccessUI();
                        new Handler(AppBase.getAppContext().getMainLooper()).post(new Runnable() {
                            @Override
                            public void run() {
                                if (activity != null) {
                                    InputMethodUtils.hideSoftInput(activity);
                                }
                                startAnim(activity);
                            }
                        });
                    }
                },true);
            } else {
                Toast.makeText(AppBase.getAppContext(), R.string.me_login_check_signature_fail, Toast.LENGTH_SHORT).show();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void startAnim(Activity activity) {
        if (activity instanceof LoginActivity) {
            ((LoginActivity) activity).startAnim();
        }
    }

//    private void showLeaveDialog(String msg, final String leaveUrl) {
//        PromptUtils.showAlertDialog(mActivity, -1, msg, new DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogInterface dialog, int which) {
//                toLeaveWeb(leaveUrl);
//            }
//        }, false);
//    }

//    private void toLeaveWeb(String url) {
//
//        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Function).getIntent(AppBase.getAppContext());
//        WebBean bean = new WebBean(url, 1);
//        intent.putExtra(EXTRA_WEB_BEAN, bean);
//        intent.putExtra(AppBase.FLAG_FUNCTION, WebViewUtils.getName());
//
//        mActivity.startActivity(intent);
//        PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_DISMISSION);
//    }
}
