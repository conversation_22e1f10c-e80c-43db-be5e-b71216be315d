package com.jd.oa.business.liveness;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.liveness.widget.VerifyInputDialog;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.dialog.Unbind;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.PauseHandler;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.login.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jd.oa.router.DeepLink.F_PHONE;

/**
 * 修改手机号
 *
 * <AUTHOR>
 */
@FontScalable(scaleable = false)
@Navigation()
@Route({F_PHONE})
public class ChangePhoneFragment extends BaseFragment {
    public static final String TAG = "ChangePhoneFragment";
    public static final String RESULT_PHONE = "result.phone";

    private static final String DEFAULT_PHONE_PREFIX = "86";

    private EditText mEditText;
    private EditText mEtPwd;
    private CheckBox mCbPwd;
    private Button mBtnConfirm;
    private Spinner mSpinner;

    private ConcreteTestHandler mHandler = new ConcreteTestHandler();

    private ArrayAdapter<String> mAdapter;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.jdme_fragment_update_phone, container, false);
        ActionBarHelper.init(this, view);
        ActionBarHelper.changeActionBarTitle(this, getString(R.string.me_update_phone));
        mEditText = view.findViewById(R.id.edit_phone);
        mEtPwd = view.findViewById(R.id.et_pwd);
        mCbPwd = view.findViewById(R.id.cb_pwd);
        mBtnConfirm = view.findViewById(R.id.btn_confirm);
        mSpinner = view.findViewById(R.id.spinner);
        initView();
        getPhonePrefixList(getContext(), mAdapter);
        return view;
    }

    private void initView() {
        StringUtils.showPwd(mCbPwd, mEtPwd);

        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                verifyPassword(mEtPwd.getText().toString().trim());
            }
        });

        //mAdapter = ArrayAdapter.createFromResource(getContext(), R.array.me_week_day, android.R.layout.simple_spinner_item);
        mAdapter = new ArrayAdapter<String>(requireContext(), android.R.layout.simple_spinner_item, new ArrayList<String>() {{
            add(DEFAULT_PHONE_PREFIX);
        }}) {
            @NonNull
            @Override
            public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                TextView textView = (TextView) super.getView(position, convertView, parent);
                textView.setText(String.format("+ %s", getItem(position)));
                return textView;
            }
        };
        mAdapter.setDropDownViewResource(R.layout.jdme_layout_phone_prefix);
        mSpinner.setAdapter(mAdapter);
    }

    @Override
    public void onResume() {
        super.onResume();
        mEditText.requestFocus();
        mHandler.setActivity(getActivity(), this);
        mHandler.resume();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mHandler.sendMessage(mHandler.obtainMessage(MSG_REMOVE));
        mHandler.setActivity(null, null);
    }

    @Override
    public void onPause() {
        super.onPause();
        mHandler.pause();
    }

    @Override
    public void onDestroyView() {
        if (getActivity() != null) {
            InputMethodUtils.hideSoftInput(getActivity());
        }
        super.onDestroyView();
        // 发送删除fragment的通知
        FragmentUtils.removeAndNotifyPrev(getActivity(), this, null);
    }

    private void showVerifyCodeDialog() {
        String phone = mEditText.getText().toString();
        // 加入验证码逻辑 for V__3.5
        final Message message = mHandler.obtainMessage(VERIFY_SHOW);
        message.obj = phone;
        mHandler.sendMessage(message);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (getActivity() != null && android.R.id.home == item.getItemId()) {// actionbar 返回
            getActivity().onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 连接服务器验证密码是否正确
     */
    private void verifyPassword(String password) {
        String phone = mEditText.getText().toString().trim();
        if (TextUtils.isEmpty(phone)) {
            ToastUtils.showInfoToast(R.string.me_input_right_phone);
            return;
        }
        if (StringUtils.isEmptyWithTrim(password)) {
            ToastUtils.showToast(R.string.me_input_erp_pwd);
            return;
        }

        NetWorkManagerLogin.checkPassword(password,
                new SimpleRequestCallback<String>(getActivity(), true) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                        }.getType());
                        if (!response.isSuccessful()) {
                            ToastUtils.showToast(response.getErrorMessage());
                            return;
                        }

                        if ("1".equals(response.getData().get("checkPassword"))) {
                            showVerifyCodeDialog();
                        } else {
                            ToastUtils.showToast(R.string.me_pass_error);
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                    }
                });
    }

    // ================ 避免异常
    final static int MSG_REMOVE = 1; // 移除
    final static int VERIFY_SHOW = 33;  // 验证码

    static class ConcreteTestHandler extends PauseHandler {
        /**
         * Activity instance
         */
        protected Activity activity;
        private ChangePhoneFragment mFragment;

        final void setActivity(Activity activity, ChangePhoneFragment fragment) {
            this.activity = activity;
            this.mFragment = fragment;
        }

        @Override
        final protected boolean storeMessage(Message message) {
            return true;
        }

        @Override
        final protected void processMessage(Message msg) {
            final Activity activity = this.activity;
            if (activity != null) {
                switch (msg.what) {
                    case MSG_REMOVE:
                        if (verifyDialog != null) {
                            verifyDialog.dismissAllowingStateLoss();
                        }
                        verifyDialog = null;
                        break;
                    case VERIFY_SHOW:
                        doCheckVerifyCode(activity, (String) msg.obj);
                        break;
                }
            }
        }

        // 验证码输入框dialog
        private VerifyInputDialog verifyDialog;

        // 弹框，验证码的
        private void doCheckVerifyCode(final Activity activity, final String phone) {
            if (verifyDialog != null && verifyDialog.isAdded()) return;
            String prefix = (String) mFragment.mSpinner.getSelectedItem();
            verifyDialog = VerifyInputDialog.getInstance(phone, prefix);
            verifyDialog.setDialogDoneListener(new Unbind.MyDialogFragmentListener() {
                @Override
                public void onDialogDone(boolean isCancel, String doneMessage, Bundle bundle) {
                    if (activity != null && !activity.isFinishing()) {
                        if (!isCancel && (null != bundle && bundle.getBoolean(VerifyInputDialog.BUNDLE_KEY_PASS_RIGHT))) {
                            verifyDialog.dismissAllowingStateLoss(); // 正确
                            Intent intent = new Intent();
                            intent.putExtra(RESULT_PHONE, phone);
                            activity.setResult(Activity.RESULT_OK, intent);
                            activity.onBackPressed();
                        }
                    }
                }
            });

            if (activity instanceof FragmentActivity) {
                verifyDialog.show(((FragmentActivity) activity).getSupportFragmentManager(), null);
            }
        }
    }


    protected static void getPhonePrefixList(Context context, final ArrayAdapter<String> mAdapter) {
        if (context == null || mAdapter == null) {
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("type", "1");
        HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(context, true) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<List<String>> response = ApiResponse.parse(info.result, new TypeToken<List<String>>() {}.getType());
                if (!response.isSuccessful()) {
                    ToastUtils.showToast(response.getErrorMessage());
                    return;
                }
                mAdapter.clear();
                mAdapter.addAll(response.getData());
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

        }, NetworkConstant.API_GET_MOBILE_MAIL_PREFIX);
    }
}
