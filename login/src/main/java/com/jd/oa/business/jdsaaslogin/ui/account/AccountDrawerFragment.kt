package com.jd.oa.business.jdsaaslogin.ui.account

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSONObject
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.business.InjectorUtil
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamData
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchActivity
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchActivity.Companion.GET_MESSAGE_CODE_TOKEN
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchActivity.Companion.MSG_CODE_EXPIRE_TIME
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchActivity.Companion.TEAM_ACCOUNT_INFO
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchActivity.Companion.TEAM_USER_INFO
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchViewModel
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchViewModel.Companion.EVENT_ACCOUNT_SWITCH_SUCCESS
import com.jd.oa.business.jdsaaslogin.ui.login.LoginTeamFooterSection
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginDialog
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.LoginUtil
import com.jd.oa.eventbus.EventBusMgr
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.preference.JdSaasLoginPreference
import com.jd.oa.qrcode.ScanResultDispatcher
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter
import kotlinx.android.synthetic.main.jdsaas_fragment_account_drawer.view.ll_account_token_verify
import kotlinx.android.synthetic.main.jdsaas_fragment_account_drawer.view.rv_account
import kotlinx.android.synthetic.main.jdsaas_fragment_account_drawer.view.tv_account_switch

class AccountDrawerFragment : BaseFragment(), AccountDrawerItemSection.ClickListener {

    val viewModel by lazy { ViewModelProvider(this, InjectorUtil.getAccountSwitchModelFactory()).get(
        AccountSwitchViewModel::class.java) }

    lateinit var mView: View
    var mTeamData: TeamData? = null
    lateinit var mSectionedAdapter : SectionedRecyclerViewAdapter
    var mTeamAccountInfo : TeamAccountInfo? = null
    var mTeamUserInfo : TeamUserInfo? = null
    var mAccountDrawerCloseListener :  (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initData()

        viewModel.mMsgCodeSendSuccessLiveData.observe(this){
            toAccountSwitchActivity(it)
            //开启倒记时，切换租户时如果上次验证码有效，再次进入验证码页面显示当前记时
            viewModel.startMsgCodeExpireTimeCountDown(it)
        }
        viewModel.mLoginSuccessLiveData.observe(this){
            mAccountDrawerCloseListener?.invoke()
        }
        viewModel.mMessageCodeAlreadySendLiveData.observe(this){
            val expireTime = viewModel.mMsgCodeCountDownLiveData.value?.toInt()
            if(expireTime != null){
                toAccountSwitchActivity(expireTime)
            }
        }
        viewModel.mOtherDeviceLoginLiveData.observe(this){
            if(it == 0){
                JdSaasLoginDialog.showOtherDeviceLoginDialog(requireActivity(), loginContinue = {
                    viewModel.loginByToken(1)
                }, loginCancel = {
                    mAccountDrawerCloseListener?.invoke()
                })
            }
        }
        EventBusMgr.getInstance().register(this)
    }

    fun toAccountSwitchActivity(expireTime : Int){
        val intent = Intent(AppBase.getAppContext(), AccountSwitchActivity::class.java)
        intent.putExtra(MSG_CODE_EXPIRE_TIME,expireTime)
        intent.putExtra(GET_MESSAGE_CODE_TOKEN,viewModel.mGetMessageCodeToken)
        intent.putExtra(TEAM_ACCOUNT_INFO,mTeamAccountInfo)
        intent.putExtra(TEAM_USER_INFO,mTeamUserInfo)
        requireActivity().startActivity(intent)
        //手机上显示打开动画效果，pad弹窗样式不显示打开动画
        if(!LoginUtil.isTablet(requireActivity())){
            requireActivity().overridePendingTransition(R.anim.jdme_right_in, R.anim.jdme_left_out)
        }

        mView.postDelayed(object : Runnable{
            override fun run() {
                mAccountDrawerCloseListener?.invoke()
            }
        },500)
    }

    fun refreshData(){
        initData()
        initRecyclerView()
    }

    private fun initData() {
//        val teamDataJsonStr = "{\"teamAccountInfoList\":[{\"teamFullName\":\"SaaS测试租户1\",\"teamId\":\"********\",\"teamUserInfoList\":[{\"activeStatus\":0,\"employeeId\":\"kq75i6N2i5TZVMESaas01\",\"pin\":\"aimldir\",\"realName\":\"Honery\",\"userId\":\"Y1aO3wsZoMESaaStest01\",\"phone\":\"***********\",\"countryCode\":\"86\"},{\"activeStatus\":0,\"employeeId\":\"kq75i6N2i5TZVMESaas02\",\"pin\":\"aimldir\",\"realName\":\"jd_633\",\"userId\":\"Y1aO3wsZoMESaaStest02\",\"phone\":\"***********\",\"countryCode\":\"86\"},{\"activeStatus\":0,\"employeeId\":\"kq75i6N2i5TZVMESaas03\",\"pin\":\"aimldir\",\"realName\":\"灿烂千阳\",\"userId\":\"Y1aO3wsZoMESaaStest03\",\"phone\":\"***********\",\"countryCode\":\"86\"}]},{\"teamFullName\":\"SaaS测试租户1\",\"teamId\":\"********\",\"teamUserInfoList\":[{\"activeStatus\":0,\"employeeId\":\"kq75i6N2i5TZVMESaas01\",\"pin\":\"aimldir\",\"realName\":\"Honery\",\"userId\":\"Y1aO3wsZoMESaaStest01\",\"phone\":\"***********\",\"countryCode\":\"86\"},{\"activeStatus\":0,\"employeeId\":\"kq75i6N2i5TZVMESaas02\",\"pin\":\"aimldir\",\"realName\":\"jd_633\",\"userId\":\"Y1aO3wsZoMESaaStest02\",\"phone\":\"***********\",\"countryCode\":\"86\"},{\"activeStatus\":0,\"employeeId\":\"kq75i6N2i5TZVMESaas03\",\"pin\":\"aimldir\",\"realName\":\"灿烂千阳\",\"userId\":\"Y1aO3wsZoMESaaStest03\",\"phone\":\"***********\",\"countryCode\":\"86\"}]},{\"teamFullName\":\"SaaS测试租户2\",\"teamId\":\"00046790\",\"teamUserInfoList\":[{\"activeStatus\":0,\"employeeId\":\"kq75i6N2i5TZVMESaas04\",\"pin\":\"aimldir\",\"realName\":\"Honery\",\"userId\":\"Y1aO3wsZoMESaaStest01\",\"phone\":\"***********\",\"countryCode\":\"86\"}]}]}"
        val teamDataJsonStr = JdSaasLoginPreference.get(JdSaasLoginPreference.KV_ENTITY_TEAM_DATA)
        mTeamData = JSONObject.parseObject(teamDataJsonStr,TeamData::class.java)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.jdsaas_fragment_account_drawer,container,false)
        if (StatusBarConfig.enableImmersive()) {
            mView.tv_account_switch.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
            mView.ll_account_token_verify.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        }
        mView.ll_account_token_verify.setOnClickListener {
//            viewModel.testTokenVerify()
            val intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext())
            startActivityForResult(intent,100)
        }
        initRecyclerView()
        return mView
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(requestCode == 100){
            if (null != data) {
                val result = data.getStringExtra("result")
                ScanResultDispatcher.dispatch(AppBase.getAppContext(), result)
            }
        }
    }

    fun initRecyclerView(){
        val manager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        mView.rv_account.setLayoutManager(manager)
        mSectionedAdapter = SectionedRecyclerViewAdapter()
        var teamAccountInfoList = mTeamData?.teamAccountInfoList
        val hasMoreData = (teamAccountInfoList?.size?:0) > 10
        if(hasMoreData){
            teamAccountInfoList = teamAccountInfoList?.subList(0,10)
            mSectionedAdapter.addSection(AccountDrawerItemSection(requireContext(),teamAccountInfoList,this))
            mSectionedAdapter.addSection(LoginTeamFooterSection(object : LoginTeamFooterSection.ClickListener {
                override fun onLoadMoreClicked() {
                    mSectionedAdapter.removeAllSections()
                    mSectionedAdapter.addSection(AccountDrawerItemSection(requireContext(),mTeamData?.teamAccountInfoList,this@AccountDrawerFragment))
                    mSectionedAdapter.notifyDataSetChanged()
                }
            }))
        }else{
            mSectionedAdapter.addSection(AccountDrawerItemSection(requireContext(),teamAccountInfoList,this))
        }
        mView.rv_account.adapter = mSectionedAdapter
    }

    override fun onAccountItemClickListener(
        section: AccountDrawerItemSection,
        position: Int,
        item: TeamAccountInfo?
    ) {
        if(!(item?.loginCurrently?:false) && item?.teamUserInfoList?.size == 1){//直接切换租户
            mTeamAccountInfo = item
            mTeamUserInfo = item.teamUserInfoList.first()
            loginByPhone()
        }
        mSectionedAdapter.notifyItemChanged(position)
    }

    override fun onUserItemClickListener(
        section: AccountDrawerItemSection,
        position: Int,
        teamAccountInfo: TeamAccountInfo?,
        teamUserInfo: TeamUserInfo?
    ) {
        mTeamAccountInfo = teamAccountInfo
        mTeamUserInfo = teamUserInfo
        loginByPhone()
    }

    fun loginByPhone(){
        if(!(mTeamUserInfo?.loginCurrently?:false)){
            JdSaasLoginHelper.showSwitchAccountDialog(requireActivity()){
                viewModel.mTeamAccountInfo = mTeamAccountInfo
                viewModel.mTeamUserInfo = mTeamUserInfo
                viewModel.loginByPhoneAfterCheckToken()
            }
        }
    }

    fun setOnAccountDrawerCloseListener(listener : () -> Unit ){
        this.mAccountDrawerCloseListener = listener
    }

    fun onEventMainThread(eventStr : String){
        if(eventStr != EVENT_ACCOUNT_SWITCH_SUCCESS){
            return
        }
        refreshData()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBusMgr.getInstance().unregister(this)
    }
}