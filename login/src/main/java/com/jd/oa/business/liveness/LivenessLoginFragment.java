package com.jd.oa.business.liveness;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.login.controller.CommunicationListener;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.router.DeepLink;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.login.R;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;

import static com.jd.oa.business.liveness_new.LivenessNewActivity.LOGIN;

/**
 * Created by zhaoyu1 on 2016/12/8.
 */
public class LivenessLoginFragment extends BaseFragment {//刷脸登录的类
    private ImageView me_iv_circle;
    private TextView me_username;
    private TextView me_btn_login_by_pwd;
    private CommunicationListener communicationListener;
    LiveLoginHelper liveLoginHelper;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 登录页时，清空一些标记
        MyPlatform.sHasLock = false;
        MyPlatform.sMainActivityUnlocked = false;
        liveLoginHelper = new LiveLoginHelper();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_liveness_login, container, false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            view.setPadding(0, DisplayUtil.getStatusBarHeight(getContext()), 0, 0);
        }
        initView(view);
        return view;
    }

    private void initView(View view) {
        me_iv_circle = (ImageView) view.findViewById(R.id.me_iv_circle);
        me_username = (TextView) view.findViewById(R.id.me_username);
        me_btn_login_by_pwd = (TextView) view.findViewById(R.id.me_btn_login_by_pwd);

        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail((R.drawable.jdme_picture_user_default)).showImageOnLoading(R.drawable.jdme_picture_user_default).showImageForEmptyUri(R.drawable.jdme_picture_user_default).build();
        ImageLoaderUtils.getInstance().displayImage(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_COVER), me_iv_circle, displayImageOptions);
        me_username.setText(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME));

        me_btn_login_by_pwd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (communicationListener != null) {
                    communicationListener.doCommunication();
//                    PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_LOGIN_WITH_PWD);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_login_accountLogin_login_click,JDMAConstants.mobile_login_accountLogin_login_click);
                }
            }
        });

        view.findViewById(R.id.tv_login).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_login_faceLogin_login_click,JDMAConstants.mobile_login_faceLogin_login_click);
                startLogin();
            }
        });
    }

    private void startLogin() {//点击开始刷脸登录后的逻辑
        PermissionHelper.requestPermissions(getActivity(),getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),getResources().getString(com.jme.common.R.string.me_request_permission_camera_liveness),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        liveLogin(me_username.getText().toString());
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        ToastUtils.showToast(R.string.me_liveness_permission);
                    }
                },Manifest.permission.CAMERA,Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    public static void liveLogin(String erp) {
        final Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_LivenessNew).getIntent(activity);
        intent.putExtra("erp", erp);
        intent.putExtra("action", "verify");        // 采集
        intent.putExtra("FTAG_INTENT", LOGIN);
        activity.startActivityForResult(intent, 15);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        liveLoginHelper.onActivityResult(requestCode, data);
    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof CommunicationListener) {
            communicationListener = (CommunicationListener) context;
        }
    }


    @Override
    public void onDetach() {
        super.onDetach();
        communicationListener = null;
    }

    @Override
    public void onResume() {
        super.onResume();
        liveLoginHelper.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        liveLoginHelper.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        liveLoginHelper.onDestroy();
    }

}
