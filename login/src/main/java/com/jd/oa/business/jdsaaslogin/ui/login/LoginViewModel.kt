package com.jd.oa.business

import android.app.Activity
import android.os.CountDownTimer
import android.os.Handler
import android.util.Base64
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.AppBase
import com.jd.oa.business.jdsaaslogin.data.LoginErrorCode
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamData
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.JdSaasPromptUtil
import com.jd.oa.business.jdsaaslogin.util.WJLoginUtil
import com.jd.oa.business.login.controller.LoginFragment
import com.jd.oa.model.service.TabbarService
import com.jd.oa.model.service.TabbarService.ITabbarConfigCallback
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.gateway.ServeConfig
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.JdSaasLoadingDialog
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.network.token.KeyManager
import com.jd.oa.preference.JdSaasLoginPreference
import com.jd.oa.utils.DeviceUtil
import com.jd.oa.utils.InputMethodUtils
import com.jd.oa.utils.PromptUtils
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.encrypt.RSAUtil
import com.jme.login.R
import org.json.JSONException
import org.json.JSONObject


class LoginViewModel(private val repository: LoginRepository) : ViewModel(){

     companion object{
         const val TAG = "LoginViewModel"
     }

    val mWJLoginUtil : WJLoginUtil = WJLoginUtil()
    val mClearMessageCodeLiveData = MutableLiveData<String>()
    val mMsgCodeSendSuccessLiveData = MutableLiveData<Int>()
    val mMsgCodeCountDownLiveData = MutableLiveData<Long>()
    val mMessageCodeAlreadySendLiveData = MutableLiveData<String>()
    val mToLoginTeamFragmentLiveData = MutableLiveData<String>()
    val mToLoginUserFragmentLiveData = MutableLiveData<TeamAccountInfo>()
    val mLoginSuccessLiveData = MutableLiveData<String>()
    val mOtherDeviceLoginLiveData = MutableLiveData<Int>()
    val mAccountExceptionLiveData = MutableLiveData<String>()
    var loginType : LoginType = LoginType.TYPE_PHONE
    var mPhone : String? = ""
    var mCountryCode : String? = ""
    var mAccount : String? = ""
    var mGetMessageCodeToken : String? = ""
    var mCheckMessageCodeToken : String? = ""
    var countDownTimer : CountDownTimer? = null
    var mTeamAccountInfo: TeamAccountInfo? = null
    var mTeamUserInfo: TeamUserInfo? = null
    var mGetAccountListApiResponse : ApiResponse<TeamData>? = null

    fun login(userName: String,password : String){
        val publicKey = KeyManager.getInstance().publicKey
        repository.exchangePublicKey(userName,publicKey,object : SimpleRequestCallback<String>(){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                info?.result?.let {
                    val response : ApiResponse<Map<String,String>> = ApiResponse.parse(it,Map::class.java)
                    if(!response.isSuccessful){
                        //请求失败时清除秘钥，下次重新生成
                        KeyManager.getInstance().clearKeys()
                        ToastUtils.showToast(response.errorMessage)
                        return
                    }
                    val serverPublicKeyStr = response.data.get("serverPublicKeyStr")
                    KeyManager.getInstance().storeServerPublicKey(serverPublicKeyStr)
                    loginByPassword(userName,password,serverPublicKeyStr)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                //请求失败时清除秘钥，下次重新生成
                KeyManager.getInstance().clearKeys()
            }
        })
    }

    private fun loginByPassword(userName: String,password : String,serverPublicKey : String?){
        val encryptPassword = RSAUtil.encrypt(serverPublicKey, password)
        repository.login(userName,encryptPassword,object : SimpleRequestCallback<String>(){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                info?.result?.let {
                    val response : ApiResponse<Map<String,String>> = ApiResponse.parse(it,Map::class.java)
                    val errorCode = response.errorCode
                    val errorMsg = response.errorMessage
                    val data = response.data
                    if(errorCode == "0"){
                        LoginSuccess(it,password)
                    }else if(errorCode == "1"){
                        val operationCode = data.get("operationCode")
                        if("0001".equals(operationCode)){//需要验证码登录
                            startAnim(AppBase.getTopActivity())
                        }
                    }
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
            }
        })
    }

    private fun loginWithVerifyCode(userName: String,encryptPassword : String,msgCode : String ){
        repository.loginWithVerifyCode(userName,encryptPassword,msgCode,object : SimpleRequestCallback<String>(){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
            }
        })
    }

    fun LoginSuccess(json: String?, pwd: String?) {
        val jsonObject: JSONObject
        try {
            jsonObject = JSONObject(json ?: "{}")
            val contentJson = jsonObject.optJSONObject("content") ?: return
            val activity = AppBase.getTopActivity()
            // 是否首次登录
//            final boolean isFiirstLogin  = TextUtils.isEmpty(PreferenceManager.UserInfo.getUserName());
            val sign = contentJson.optString("sign")
            val encryptData = contentJson.optString("User")
            if (LoginFragment.checkResponseSign(encryptData, sign)) {
                val data = LoginFragment.decryptResponse(encryptData)
                LoginFragment.loginSuccessData(data, pwd) // 通知login事件
                PromptUtils.showLoadDialog(
                    activity,
                    AppBase.getAppContext().getString(R.string.me_logining)
                )
                ServeConfig.getInstance(activity).getRemoteConfig(activity)
                // 获取Tabbar
                val tabbarService = AppJoint.service(TabbarService::class.java)
                tabbarService.getTabbarConfig(object : ITabbarConfigCallback {
                    override fun onSucsess() {
                        PromptUtils.removeLoadDialog(activity)
                        LoginFragment.loginSuccessUI()
                        Handler(AppBase.getAppContext().mainLooper).post {
                            if (activity != null) {
                                InputMethodUtils.hideSoftInput(activity)
                            }
                            startAnim(activity)
                        }
                    }

                    override fun onSuccess(info: String) {}
                    override fun onFailed() {
                        PromptUtils.removeLoadDialog(activity)
                        LoginFragment.loginSuccessUI()
                        Handler(AppBase.getAppContext().mainLooper).post {
                            if (activity != null) {
                                InputMethodUtils.hideSoftInput(activity)
                            }
                            startAnim(activity)
                        }
                    }
                }, true)
            } else {
                Toast.makeText(
                    AppBase.getAppContext(),
                    R.string.me_login_check_signature_fail,
                    Toast.LENGTH_SHORT
                ).show()
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    private fun startAnim(activity: Activity?) {
        if (activity is LoginActivity) {
            activity.startAnim()
        }
    }

    fun loginByPassword(userName: String?,password: String?){
        mAccount = userName
        mWJLoginUtil.loginByPassword(userName,password,object : WJLoginUtil.LoginByPasswordCallback{
            override fun onLoginSuccess() {
                val jsonParams = JdSaasLoginHelper.getJsonParamsForPasswordLogin(userName)
                getAccountList(jsonParams)
            }
        })
    }

    fun loginByPhone(countryCode: String?,phone: String?){
        mPhone = phone
        mCountryCode = countryCode
        val activity = AppBase.getTopActivity()
        JdSaasLoadingDialog.getInstance().showLoadingDialog(activity)
        mWJLoginUtil.loginByPhone(countryCode,phone,object : WJLoginUtil.LoginByPhoneCallback{
            override fun onGetMessageCode(msgCodeExpireTime: Int, getMessageCodeToken: String?) {
                JdSaasLoadingDialog.getInstance().cancelLoadingDialog()
                mMsgCodeSendSuccessLiveData.postValue(msgCodeExpireTime)
                mGetMessageCodeToken = getMessageCodeToken
                startMsgCodeExpireTimeCountDown(msgCodeExpireTime)
            }

            override fun onMessageCodeAlreadySend() {
                JdSaasLoadingDialog.getInstance().cancelLoadingDialog()
                mMessageCodeAlreadySendLiveData.postValue(null)
            }

            override fun onError() {
                JdSaasLoadingDialog.getInstance().cancelLoadingDialog()
            }
        })
    }

    fun checkMessageCodeByPhone(msgCode: String?,loginType: LoginType){
        mWJLoginUtil.checkMessageCodeForCompanyLogin(mPhone,mCountryCode,mGetMessageCodeToken,msgCode,object : WJLoginUtil.LoginByPhoneCallback{
            override fun onCheckMessageCode(jsonStr: String, checkMessageCodeToken: String) {
                mCheckMessageCodeToken = checkMessageCodeToken
                getAccountList(jsonStr)
            }
            override fun onError() {
                mClearMessageCodeLiveData.postValue(null)
            }
        })
    }

    fun loginWithName(teamAccountInfo: TeamAccountInfo?,teamUserInfo: TeamUserInfo?){
        if(teamAccountInfo != null && teamUserInfo != null){
            mWJLoginUtil.loginWithNameForCompanyLogin(mCheckMessageCodeToken,teamUserInfo.pin,object : WJLoginUtil.LoginByPhoneCallback{
                override fun onLoginWithName(loginName: String?) {
                    loginByToken(0,teamAccountInfo, teamUserInfo)
                }
            })
        }
    }

    fun loginByToken(forceLogin : Int,teamAccountInfo: TeamAccountInfo?,teamUserInfo: TeamUserInfo?){
        if(forceLogin == 0){ //直接登录
            mTeamAccountInfo = teamAccountInfo?:mTeamAccountInfo
            mTeamUserInfo = teamUserInfo?:mTeamUserInfo
        }
        val headers = hashMapOf<String,String>(
            "x-token" to mWJLoginUtil.getToken(),
            "x-user-id" to (mTeamUserInfo?.userId?:""),
            "x-did" to DeviceUtil.getDeviceUniqueId(),
            "x-team-id" to (mTeamAccountInfo?.teamId?:""),
            "Cookie" to "wskey=${mWJLoginUtil.getToken()}"
        )
        repository.loginByToken(forceLogin,mTeamUserInfo?.employeeId?:"",headers,
            object : SimpleRequestCallback<String>(AppBase.getTopActivity(),true){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                if(info?.errorCode == LoginErrorCode.OTHER_DEVICE_LOGIN){
                    mOtherDeviceLoginLiveData.postValue(forceLogin)
                }else if(info?.errorCode == LoginErrorCode.PIN_NULL ||
                    info?.errorCode == LoginErrorCode.TEAM_USER_OUT ||
                    info?.errorCode == LoginErrorCode.TEAM_STOP){
                    mAccountExceptionLiveData.postValue(info?.errorMessage)
                }else if (info?.isSuccessful?:false){
                    val response : ApiResponse<Map<String,Any>> = ApiResponse.parse(info?.result,Map::class.java)
                    if(response.data["successFlag"] == 0){
                        mOtherDeviceLoginLiveData.postValue(forceLogin)
                    }else{
                        loginSuccess()
                    }
                }else{
                    JdSaasPromptUtil.showErrorToast(info?.errorMessage)
                }
            }
            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
            }
        })
    }

    fun loginSuccess(){
        setUserInfo()
        updateAndSaveTeamData()
        // 获取Tabbar
        val tabbarService = AppJoint.service(TabbarService::class.java)
        tabbarService.getTabbarConfig(object : ITabbarConfigCallback {
            override fun onSucsess() {
                mLoginSuccessLiveData.postValue(mWJLoginUtil.getToken())
            }
            override fun onSuccess(info: String) {}
            override fun onFailed() {
                mLoginSuccessLiveData.postValue(mWJLoginUtil.getToken())
            }
        }, true)
    }

    fun setUserInfo(){
        //记录上次登录的手机或账号
        if(loginType == LoginType.TYPE_PHONE){
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_LAST_LOGIN_TYPE,loginType.name)
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_PHONE,mPhone?:"")
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_PHONE_SAVE_TIME,System.currentTimeMillis()/1000)
        }else if(loginType == LoginType.TYPE_ACCOUNT){
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_LAST_LOGIN_TYPE,loginType.name)
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_ACCOUNT,mAccount?:"")
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_ACCOUNT_SAVE_TIME,System.currentTimeMillis()/1000)
        }
        //保存用户信息
        JdSaasLoginHelper.setUserInfo(mWJLoginUtil.getToken(),mPhone,mTeamAccountInfo,mTeamUserInfo)
    }

    fun updateAndSaveTeamData(){
        JdSaasLoginHelper.updateAndSaveTeamData(mGetAccountListApiResponse?.data,
            mTeamAccountInfo?.teamId,mTeamUserInfo?.pin)
    }

    fun getAccountList(jsonString : String){
        val headers = hashMapOf<String,String>()
        if(loginType == LoginType.TYPE_PHONE){
            headers.put("x-tp",Base64.encodeToString(mPhone?.toByteArray(),Base64.NO_WRAP))
        }
        repository.getAccountList(jsonString,headers,object : SimpleRequestCallback<String>(AppBase.getTopActivity(),true){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                mGetAccountListApiResponse = ApiResponse.parse(info?.result,TeamData::class.java)
                //模拟数据
//                val teamDataJsonStr= "{\"teamAccountInfoList\":[{\"teamFullName\":\"SaaS测试租户1\",\"teamId\":\"********\",\"teamUserInfoList\":[{\"activeStatus\":1,\"countryCode\":\"86\",\"employeeId\":\"kq75i6N2i5TZVMESaas01\",\"phone\":\"***********\",\"pin\":\"aimldir\",\"realName\":\"Honery\",\"userId\":\"Y1aO3wsZoMESaaStest01\"}]}]}"
//                val teamData = com.alibaba.fastjson.JSONObject.parseObject(teamDataJsonStr, TeamData::class.java)
//                apiResponse.data = teamData

                if(info?.errorCode == LoginErrorCode.PIN_LIST_NULL
                    || info?.errorCode == LoginErrorCode.ME_TEAM_NULL
                    || info?.errorCode == LoginErrorCode.ME_EMPL_NULL){
                    mAccountExceptionLiveData.postValue(info.errorMessage)
                }else if(mGetAccountListApiResponse?.isSuccessful?:false){
                    val teamAccountInfoList = mGetAccountListApiResponse?.data?.teamAccountInfoList
                    if((teamAccountInfoList?.size ?: 0) == 0){
                        JdSaasPromptUtil.showErrorToast(AppBase.getAppContext().getString(R.string.jdsaas_login_data_exception))
                        return
                    }
                    //把手机号放入请求的租户列表，后面切换租户手机号登录使用
                    teamAccountInfoList?.forEach {
                        it.teamUserInfoList?.forEach {
                            it.countryCode = mCountryCode
                            it.phone = mPhone
                        }
                    }
                    //只有一个租户和一个用户直接上报a2并跳转登录成功页面
                    if(teamAccountInfoList?.size == 1 && teamAccountInfoList.first().teamUserInfoList?.size == 1){
                        val teamAccountInfo = teamAccountInfoList.first()
                        val teamUserInfo = teamAccountInfo.teamUserInfoList?.first()
                        if(loginType == LoginType.TYPE_PHONE){ //手机号登录
                            if(teamUserInfo != null){
                                loginWithName(teamAccountInfo,teamUserInfo)
                            }
                        }else if(loginType == LoginType.TYPE_ACCOUNT){ //账号密码登录
                            loginByToken(0,teamAccountInfo, teamUserInfo)
                        }
                    }else if(teamAccountInfoList?.size == 1){//只有一个租户，直接进入到选择账号页面
                        toLoginUserFragment(teamAccountInfoList.first())
                    }else{
                        //有多个租户跳转租户选择页面
                        mToLoginTeamFragmentLiveData.postValue(null)
                    }
                }else{
                    JdSaasPromptUtil.showErrorToast(mGetAccountListApiResponse?.errorMessage)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                JdSaasPromptUtil.showErrorToast(info)
            }
        })
    }

    fun toLoginUserFragment(item: TeamAccountInfo?){
        mToLoginUserFragmentLiveData.postValue(item)
    }

    fun startMsgCodeExpireTimeCountDown(msgCodeExpireTime : Int){
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer((msgCodeExpireTime+1) * 1000L,1000L){
            override fun onTick(time : Long) {
                mMsgCodeCountDownLiveData.postValue(time/1000)
            }

            override fun onFinish() {
            }
        }
        countDownTimer?.start()
    }

    override fun onCleared() {
        super.onCleared()
        countDownTimer?.cancel()
        mWJLoginUtil.unRegisterRiskManagementReceiver()
    }

}