package com.jd.oa.business.jdsaaslogin.util

import android.content.Context
import android.text.TextUtils
import com.alibaba.fastjson.JSONObject
import com.jd.oa.business.LoginRepository
import com.jd.oa.business.jdsaaslogin.data.model.TeamData
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.preference.JdSaasLoginPreference
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jme.login.R

object JdSaasLoginDialog {
    fun showOtherDeviceLoginDialog(context : Context, loginContinue : () -> Unit,loginCancel : () -> Unit){
        val confirmDialog = ConfirmDialog(context)
        confirmDialog.setTitle(context.getString(R.string.jdsaas_login_od_dialog_title))
        confirmDialog.setMessage(context.getString(R.string.jdsaas_login_od_dialog_message))
        confirmDialog.setNegativeButton(context.getString(R.string.jdsaas_login_od_dialog_negative_btn))
        confirmDialog.setPositiveButton(context.getString(R.string.jdsaas_login_od_dialog_positive_btn))
        confirmDialog.setPositiveClickListener {
            loginContinue()
        }
        confirmDialog.setNegativeClickListener {
            loginCancel()
        }
        confirmDialog.show()
    }

    fun showAccountExceptionDialog(context: Context, message: String, confirm : () -> Unit){
        val confirmDialog = ConfirmDialog(context)
        confirmDialog.setTitle(context.getString(R.string.jdsaas_login_od_dialog_title))
        confirmDialog.setMessage(message)
        confirmDialog.setNegativeButton(null)
        confirmDialog.setPositiveButton(context.getString(R.string.jdsaas_login_error_dialog_positive_btn))
        confirmDialog.setPositiveClickListener {
            confirm()
        }
        confirmDialog.show()
    }

    fun showLogoutDialog(context: Context, confirm: () -> Unit){
        val confirmDialog = ConfirmDialog(context)
        confirmDialog.setTitle(context.getString(R.string.jdsaas_login_logout_dialog_title))
        confirmDialog.setMessage(context.getString(R.string.jdsaas_login_logout_dialog_message))
        confirmDialog.setPositiveClickListener {
            logoutByToken(object : SimpleRequestCallback<String>(context,true) {
                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    if(info?.isSuccessful?:false){
                        confirm()
                    }else{
                        JdSaasPromptUtil.showErrorToast(info?.errorMessage)
                    }
                }
            })
        }
        confirmDialog.show()
    }

    fun logoutByToken(callback: SimpleRequestCallback<String>){
        var teamDataJsonStr = JdSaasLoginPreference.get(JdSaasLoginPreference.KV_ENTITY_TEAM_DATA)
        val teamData = JSONObject.parseObject(teamDataJsonStr, TeamData::class.java)
        val userPinList = mutableListOf<Map<String,String>>()
        teamData.teamAccountInfoList?.forEach {
            it.teamUserInfoList?.forEach {
                val pin = it.pin?:""
                val userId = it.userId?:""
                val param = mapOf("userId" to userId,"pin" to pin)
                if(!TextUtils.isEmpty(pin) && !TextUtils.isEmpty(userId) && !userPinList.contains(param)){
                    userPinList.add(param)
                }
            }
        }
        LoginRepository().logoutByToken(userPinList,callback)
    }
}