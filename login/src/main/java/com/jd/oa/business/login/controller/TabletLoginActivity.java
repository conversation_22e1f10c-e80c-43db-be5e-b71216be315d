package com.jd.oa.business.login.controller;

import android.content.Intent;
import android.os.Bundle;
import android.view.Window;

import androidx.annotation.Nullable;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.multiapp.MultiAppConstant;

public class TabletLoginActivity extends BaseActivity {

    public static final int REQUEST_LOGIN = 1234;
    public static final String FROM_TABLET = "fromTablet";
    public static final int RESULT_TO_INDEX = 8888;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        Intent intent = new Intent(getIntent());//深拷贝intent，透传
        intent.setClass(AppBase.getAppContext(), MultiAppConstant.getLoginActivityClass());
        intent.putExtra(FROM_TABLET, true);
        startActivityForResult(intent, REQUEST_LOGIN);
        overridePendingTransition(0, 0);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_LOGIN) {
            if (resultCode == RESULT_TO_INDEX) {
                Router.build(LoginActivity.getDefaultTabDeepLink()).go(AppBase.getAppContext());
            }
            finish();//和RealLoginActivity同步销毁
        }
    }
}