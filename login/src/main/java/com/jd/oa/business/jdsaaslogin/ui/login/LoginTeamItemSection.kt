package com.jd.oa.business.jdsaaslogin.ui.login

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.ui.CircleImageView
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.NClick
import com.jme.login.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters


class LoginTeamItemSection(val context: Context, val list : List<TeamAccountInfo>?, val clickListener : ClickListener?) : Section(
    SectionParameters.builder()
        .itemResourceId(R.layout.jdsaas_login_team_item)
        .build()) {

    override fun getContentItemsTotal(): Int {
        return list?.size?:0
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(holder: RecyclerView.ViewHolder?, position: Int) {
        val teamAccountInfo = list?.get(position)
        val itemHolder = holder as ItemViewHolder
        itemHolder.tv_team_name.text = teamAccountInfo?.teamFullName
        if(teamAccountInfo?.teamUserInfoList?.size == 1){
            itemHolder.tv_team_sub_name.text = teamAccountInfo.teamUserInfoList.first().realName
        }else{
            itemHolder.tv_team_sub_name.text = context.getString(R.string.jdsaas_login_team_sub_name,teamAccountInfo?.teamUserInfoList?.first()?.realName,teamAccountInfo?.teamUserInfoList?.size?.toString())
        }
        JdSaasAvatarUtil.loadGroupAvatar(context,itemHolder.iv_team_avatar,teamAccountInfo?.avatar,teamAccountInfo?.teamFullName)

        if(teamAccountInfo?.teamUserInfoList?.size == 1){
            val teamUserInfo = teamAccountInfo?.teamUserInfoList.first()
            val activeStatus = teamUserInfo.activeStatus
            if(activeStatus == 0){ //待激活
                itemHolder.cl_root.background = context.getDrawable(R.drawable.jdsaas_login_team_stroke_corner_bg_unactivated)
                itemHolder.iv_team_avatar_mask_layer.visibility = View.VISIBLE
                itemHolder.tv_team_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
                itemHolder.tv_team_sub_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
                itemHolder.iv_team_right_arrow.visibility = View.GONE
                itemHolder.tv_team_status.visibility = View.VISIBLE
                itemHolder.tv_team_status.text = context.getString(R.string.jdsaas_login_unactivated)
                itemHolder.tv_team_status.background = context.getDrawable(R.drawable.jdsaas_tv_team_status_unactivated)
                itemHolder.tv_team_status.setTextColor(context.getColor(R.color.jdsaas_color_blue_4A5FE8))
            }
//            else if(position == ){ //已停用
//                itemHolder.cl_root.background = context.getDrawable(R.drawable.jdsaas_login_team_stroke_corner_bg_unactivated)
//                itemHolder.view_team_avatar_mask_layer.visibility = View.VISIBLE
//                itemHolder.tv_team_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
//                itemHolder.tv_team_sub_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
//                itemHolder.iv_team_right_arrow.visibility = View.GONE
//                itemHolder.tv_team_status.visibility = View.VISIBLE
//                itemHolder.tv_team_status.text = context.getString(R.string.jdsaas_login_deactivated)
//                itemHolder.tv_team_status.background = context.getDrawable(R.drawable.jdsaas_tv_team_status_deactivated)
//                itemHolder.tv_team_status.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
//            }
        }

        itemHolder.itemView.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            clickListener?.onItemClickListener(this,position,list?.get(position))
        }
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val cl_root = view.findViewById<ConstraintLayout>(R.id.cl_root)
        val tv_team_name = view.findViewById<TextView>(R.id.tv_team_name)
        val tv_team_sub_name = view.findViewById<TextView>(R.id.tv_team_sub_name)
        val iv_team_avatar = view.findViewById<CircleImageView>(R.id.iv_team_avatar)
        val iv_team_avatar_mask_layer = view.findViewById<CircleImageView>(R.id.iv_team_avatar_mask_layer)
        val tv_team_status = view.findViewById<TextView>(R.id.tv_team_status)
        val iv_team_right_arrow = view.findViewById<IconFontView>(R.id.iv_team_right_arrow)

    }

    interface ClickListener{
        fun onItemClickListener(section: LoginTeamItemSection, position: Int, item: TeamAccountInfo?)
    }
}