package com.jd.oa.business.jdsaaslogin.ui.accountswitch

import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.InjectorUtil
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.LoginKeyboardUtil
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.NClick
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.btn_password_cancel
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.btn_password_next
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.tv_login_type_switch
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.cb_password
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.et_password
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.iv_password_clear
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.titleBar
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.tv_forget_password
import kotlinx.android.synthetic.main.fragment_account_switch_by_password.view.tv_password_tip2
import kotlinx.android.synthetic.main.jdsaas_login_titlebar.view.iv_back

class AccountSwitchByPasswordFragment : BaseFragment() {

    val viewModel by lazy { ViewModelProvider(requireActivity(), InjectorUtil.getAccountSwitchModelFactory()).get(
        AccountSwitchViewModel::class.java) }

    lateinit var mView: View
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.fragment_account_switch_by_password,container,false)
        if (StatusBarConfig.enableImmersive()) {
            mView.titleBar.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        }
        mView.iv_back.setOnClickListener {
            requireActivity().finish()
        }
        setTvVerifyTip2()
        mView.cb_password.setOnClickListener {
            if(mView.cb_password.isChecked){
                mView.et_password.transformationMethod = HideReturnsTransformationMethod.getInstance()
            }else{
                mView.et_password.transformationMethod = PasswordTransformationMethod.getInstance()
            }
            mView.et_password.setSelection(mView.et_password.length())
        }

        mView.et_password.addTextChangedListener{
            setNextButtonEnable()
            if(TextUtils.isEmpty(it.toString())){
                mView.iv_password_clear.visibility = View.GONE
            }else{
                mView.iv_password_clear.visibility = View.VISIBLE
            }
        }
        mView.iv_password_clear.setOnClickListener{
            mView.et_password.text = null
        }
        mView.tv_forget_password.setOnClickListener {
            JdSaasLoginHelper.forgetPassword()
        }
        mView.btn_password_next.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            LoginKeyboardUtil.hideKeyboard(context,mView)
            val password = mView.et_password.text.toString()
            viewModel.loginByPassword(password)
        }
        //pad上取消按钮
        mView.btn_password_cancel?.setOnClickListener {
            requireActivity().finish()
        }

        mView.tv_login_type_switch.setOnClickListener {
            LoginKeyboardUtil.hideKeyboard(context,mView)
            viewModel.loginByPhone()
        }
        return mView
    }

    private fun setTvVerifyTip2() {
        val msgCodeExpireTime = viewModel.mMsgCodeSendSuccessLiveData.value ?: viewModel.mMsgCodeExpireTime
        if(msgCodeExpireTime != null){
            val spanBuilder = SpannableStringBuilder(getString(R.string.jdsaas_login_dialog_password_tip1))
            val span = SpannableString("「${viewModel.mTeamUserInfo?.realName}」")
            span.setSpan(StyleSpan(Typeface.BOLD),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            span.setSpan(ForegroundColorSpan(resources.getColor(R.color.jdsaas_black_1B1B1B)),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            spanBuilder.append(span)
            spanBuilder.append(getString(R.string.jdsaas_login_dialog_password_tip2))
            mView.tv_password_tip2.setText(spanBuilder)
        }
    }

    private fun setNextButtonEnable() {
        if(!TextUtils.isEmpty(mView.et_password.text) && !TextUtils.isEmpty(mView.et_password.text)){
            mView.btn_password_next.isEnabled = true
        }else{
            mView.btn_password_next.isEnabled = false
        }
    }
}