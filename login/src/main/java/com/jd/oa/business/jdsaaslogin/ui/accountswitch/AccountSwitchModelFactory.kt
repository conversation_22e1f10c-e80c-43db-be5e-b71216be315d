package com.jd.oa.business.jdsaaslogin.ui.accountswitch

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.LoginRepository

class AccountSwitchModelFactory(private val repository: LoginRepository) :
    ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return AccountSwitchViewModel(repository) as T
    }
}