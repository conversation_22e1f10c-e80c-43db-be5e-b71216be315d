package com.jd.oa.business.liveness.set;

import com.jd.oa.business.liveness.model.AllLimitBean;
import com.jd.oa.business.liveness.model.AllStatusBean;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jme.login.R;

import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/12/19.
 */
public class LivenessSetPresenter extends AbsMVPPresenter<LivenessSetContract.View> implements LivenessSetContract.Presenter {


    LivenessSetContract.Repo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public LivenessSetPresenter(LivenessSetContract.View view) {
        super(view);
        mRepo = new LivenessSetRepo();
    }

    /**
     * 获取所有协议签署状态
     */
    @Override
    public void getAllAgreementStatus() {
        view.showLoading(view.getContext().getString(R.string.me_loading_message));
        mRepo.getAllAgreementStatus(new LoadDataCallback<AllStatusBean>() {
            @Override
            public void onDataLoaded(AllStatusBean data) {
                if (isAlive()) {
                    view.showContent();
                    view.changeAllAgreementStatus(data);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showContent();
                    showDefaultError();
                }
            }
        });
    }

    /**
     * 签署刷脸登录协议
     *
     * @param flag
     */
    @Override
    public void signLoginAgreement(final String flag) {
        view.showLoading(view.getContext().getString(R.string.me_loading_message));
        mRepo.signLoginAgreement(new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String data) {
                if (isAlive()) {
                    view.showContent();
                    view.onSignLoginFinish(true, "1".equals(flag) ? true : false);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showContent();
                    view.showMsgToast(view.getContext().getString(R.string.me_data_comm_fail) + msg);
                    // 数据通信失败，需要将状态反过来
                    view.onSignLoginFinish(false, "1".equals(flag) ? false : true);
                }
            }
        }, flag);
    }

    /**
     * 是否上传过 图片
     */
    @Override
    public void checkLivenessUpload() {
        mRepo.getUploadLiveness(new LoadDataCallback<Map>() {
            @Override
            public void onDataLoaded(Map data) {
                if (isAlive()) {
                    view.showContent();
                    String isUpload = "";
                    try {
                        isUpload = (String) data.get("isUpload");
                        view.onCheckUploadFinish("1".equals(isUpload));
                    } catch (Exception e) {
                        isUpload = "";
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showContent();
                }
            }
        });
    }

    /**
     * 签署刷脸打卡协议
     *
     * @param flag
     */
    @Override
    public void signClock(final String flag) {
        view.showLoading(view.getContext().getString(R.string.me_loading_message));
        mRepo.signClock(new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String data) {
                if (isAlive()) {
                    view.showContent();
                    view.onSignClockFinish(true, "1".equals(flag) ? true : false);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showContent();
                    view.showMsgToast(view.getContext().getString(R.string.me_data_comm_fail) + msg);
                    // 数据通信失败，需要将状态反过来
                    view.onSignClockFinish(false, "1".equals(flag) ? false : true);
                }
            }
        }, flag);
    }

    /**
     * 获取所有刷脸权限信息
     */
    @Override
    public void getAllLimit() {
        mRepo.getAllLimit(new LoadDataCallback<AllLimitBean>() {
            @Override
            public void onDataLoaded(AllLimitBean allLimitBean) {
                //  检测所有权限
                if (isAlive()) {
                    // 获取所有权限
                    view.showAllLimits(allLimitBean);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    @Override
    public void onCreate() {
    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
    }
}
