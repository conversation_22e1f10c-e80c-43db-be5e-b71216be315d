package com.jd.oa.business.liveness_new;

import static com.jd.oa.BaseActivity.RESULT_ERROR;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.business.liveness.LivenessSetFragment;
import com.jd.oa.business.liveness.capture.LivenessCaptureConstract;
import com.jd.oa.business.liveness.capture.LivenessCapturePresenter;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.storage.UseType;
import com.jd.oa.tablet.MIUI;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.NamedThreadFactory;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.cache.FileCache;
import com.jme.login.R;

import java.io.File;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import ejoy.livedetector.LiveDetectActivity;
import ejoy.livedetector.camera.CameraInterface;
import ejoy.livedetector.util.RSAAESEncryption;

/**
 * Created by zhaoyu1 on 2017/5/10.
 */
@Route({DeepLink.ACTIVITY_URI_LivenessNew, DeepLink.FACE})
public class LivenessNewActivity extends LiveDetectActivity implements LiveDetectActivity.DetectCallback,
        LiveDetectActivity.PrestartCallback, LiveDetectActivity.LivenessCallback, LivenessCaptureConstract.View {
    //人脸识别的子类
    public static final String FTAG_INTENT = "FTAG_INTENT";
    public static final int LOGIN = 20;         // 登录
    public static final int CAPTURE = 10;       // 捕获

    private final String ACTION_KEY = "action_key";
    ProgressDialog mProgressBar;
    private LivenessCapturePresenter mPresenter;
    private boolean isLogin;       // 是否是登录识别
    //    private String mUserName;
    private ExecutorService mExecutes;
    private String mAbsolutePath;
    private boolean isJsFaceAuthentication = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        snap = ConfigurationManager.get().getEntry("snap.initparam", "1");
        super.onCreate(savedInstanceState);
//        ActionBarHelper.init(this);
//       ActionBarHelper.changeActionBarTitle(this, getString(R.string.me_update_phone));
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle(R.string.me_liveness_login);
            Intent intent = getIntent();
            if (intent != null) {
                String action = intent.getStringExtra("from");
                if (action != null && action.equals("set")) {
                    actionBar.setTitle(R.string.me_face_config);
                }
                isJsFaceAuthentication = intent.getBooleanExtra("isJsFaceAuthentication",false);
            }
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setDisplayShowHomeEnabled(false);
        }

//        if (getIntent() != null) {
//            mUserName = getIntent().getStringExtra("erp");
//        }

        setUploadModelImgNum(5);
        setDetectCallback(this);
        setPreviewCallback(this);
        setLivenessCallback(this);

        if (mPresenter == null) {
            mPresenter = new LivenessCapturePresenter(this);
        }


        if (getIntent() != null) {
            isLogin = getIntent().getIntExtra(FTAG_INTENT, CAPTURE) == LOGIN ? true : false;
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            this.finish();
            return false;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 实现onDetectFinish方法，对参数result判断，进行超时、无权限、点击相机关闭按钮操作。
     *
     * @param result
     */
    @Override
    public void onDetectFinish(DetectStatus result) {
        // 检测识别，开始上传或者识别
        if (DetectStatus.SUCCESS == result) {
        } else {
            Toast.makeText(this, R.string.me_identify_fail, Toast.LENGTH_SHORT).show();
            //一般是超时的情况
            if(isJsFaceAuthentication){
                setResult(RESULT_ERROR);
            }
            finish();
        }
    }


    // list[i]就是照片原图，JPG格式
    @Override
    public void onLivenessSuccess(final List<byte[]> imgList) {

        if (imgList == null || imgList.size() == 0) {
            Toast.makeText(this, R.string.me_identify_fail, Toast.LENGTH_SHORT).show();
            finish();
            return;
        }


        try {
            if (mExecutes == null) {
                mExecutes = Executors.newSingleThreadExecutor(new NamedThreadFactory(this.getClass().getName()));
            }

            showUploadLayer(getString(R.string.me_judging));

            mExecutes.submit(new Runnable() {
                @Override
                public void run() {
                    try {

                        // 保存图片
                        String key = System.currentTimeMillis() + "abc";
                        final String ketEncrypt = RSAAESEncryption.encryptionAESKey(key);
                        final String faceSecretKey = URLEncoder.encode(ketEncrypt, "UTF-8");
                        final String token = URLEncoder.encode("b8c146ff-3afc-41ac-b7cf-9715cce73270", "UTF-8");

                        final File[] files = new File[imgList.size()];
                        for (int i = 0; i < imgList.size(); i++) {
                            files[i] = FileUtils.saveFile(RSAAESEncryption.getImgBase64EncryptionStr(key, imgList.get(i)),
                                    FileCache.getInstance().getCacheFile(UseType.USER), "liveness_face" + i + ".png", false);

                            //files[i] = FileUtils.saveFile(imgList.get(i), FileCache.getInstance().getCacheFile(), "liveness_face" + i + ".png");
                        }

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (!isLogin) { //如果不是登录识别，就上传刷脸的数据到服务器，上传成功之后关闭网页

                                    String cmdList = ConfigurationManager.get().getEntry("snap.initparam", "1");
                                    cmdList = '[' + cmdList + ']';
                                    mPresenter.doUploadTemplate(files, faceSecretKey, token, cmdList, getIntent().getStringExtra(LivenessSetFragment.SOURCE_SWITCH_KEY));

                                } else {
                                    final Intent intent = new Intent();

                                    intent.putExtra("faceSecretKey", faceSecretKey);
                                    if (files[0] != null) {
                                        mAbsolutePath = files[0].getAbsolutePath();
                                    }
                                    intent.putExtra("path", mAbsolutePath);
                                    showUploadLayer(getString(R.string.me_judging));
                                    getWindow().getDecorView().postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (!isFinishing()) {
                                                setResult(RESULT_OK, intent);
                                                finish();
                                                LivenessNewActivity.this.overridePendingTransition(0, 0);
                                            }
                                        }
                                    }, 0);
                                }
                            }
                        });

                    } catch (Exception e) {
                        ToastUtils.showToast(R.string.me_identify_fail);
//                            if(LivenessNewActivity.this != null){
//                                finish();
//                            }
                    }
                }
            });
        } catch (Exception e) {
            Toast.makeText(this, R.string.me_identify_fail, Toast.LENGTH_SHORT).show();
            finish();
        }
    }


    @Override
    protected void onStop() {
        super.onStop();
        if (mExecutes != null) {
            mExecutes.shutdownNow();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CameraInterface.getInstance().doStopCamera();
    }

    @Override
    public void onLivenessFail(int i, int i1) {
        Toast.makeText(this, R.string.me_identify_fail, Toast.LENGTH_SHORT).show();
        finish();
    }

    @Override
    public void onOpenCameraFailed() {
        ToastUtils.showToast(R.string.me_camera_open_fail);
        finish();
    }

    @Override
    public void onResumeMethod() {
//        AlertDialog.Builder builder = new AlertDialog.Builder(this);
//        // 设置参数
//        builder.setTitle(R.string.detect_interrupted)
//                .setMessage(R.string.detect_retry)
//                .setPositiveButton(R.string.detect_yes, new DialogInterface.OnClickListener() {
//
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//                        startLiveDetect();
//                    }
//                }).setNegativeButton(R.string.detect_no, new DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogInterface dialogInterface, int i) {
//                finish();
//            }
//        });
//        builder.create().show();
    }

    @Override
    public boolean isSplitMode(Configuration configuration) {
        return TabletUtil.isSplitMode(configuration);
    }

    @Override
    public boolean isXiaomiTablet() {
        return TabletUtil.isTablet() && MIUI.isXiaoMi();
    }

    @Override
    public void onPrestartFail(int i, int i1) {
    }

    /////////////////////////////////////////////////////////// View 接口定义的方法

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    @Override
    public void showLoading(String msg) {
        if (mProgressBar == null) {
            mProgressBar = new ProgressDialog(this);
            mProgressBar.setMessage("" + msg);
        }
        mProgressBar.show();
    }

    @Override
    public void showError(String msg) {
        if (!isFinishing()) {
            new AlertDialog.Builder(this)
                    .setMessage(msg)
                    .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            setResult(RESULT_ERROR);
                            finish();
                        }
                    }).show();
        }
    }

    @Override
    public Context getContext() {
        return getApplicationContext();
    }

    @Override
    public void showContent() {
        if (mProgressBar != null) {
            mProgressBar.dismiss();
        }
    }


    @Override
    public void showUploadLayer(String message) {
//        // 显示遮罩层
//        ViewGroup root = (ViewGroup) getWindow().getDecorView();
//        View view = getLayoutInflater().inflate(R.layout.jdme_liveness_scan_loading, root, false);
//        ImageView me_frameViewframe_image = (ImageView) view.findViewById(R.id.me_frameViewframe_image);
//        TextView me_liveness_judge_info = (TextView) view.findViewById(R.id.me_liveness_judge_info);
//        me_liveness_judge_info.setText("" + message);
//        me_frameViewframe_image.setImageResource(R.drawable.jdme_liveness_judge_frame);
//        root.removeView(view);
//        root.addView(view);
//        view.bringToFront();
//
//        // 显示动画
//        AnimationDrawable animationDrawable = (AnimationDrawable) me_frameViewframe_image.getDrawable();
//        animationDrawable.start();
        if (mtip != null) {
            mtip.setText(message);
        }
        if (detectTip != null) {
            detectTip.setText(message);
        }
    }

    @Override
    public void uploadTemplateSuccess() {
        // 设置上传成功标记
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD, "1");
//        MediaPlayerHandler.getInstance(getApplicationContext()).doPlay(R.raw.liveness_success);
        getWindow().getDecorView().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isFinishing()) {
                    Intent intent = new Intent();
                    intent.putExtra("isOK", true);
                    ToastUtils.showToast(R.string.me_face_upload_success);
                    setResult(RESULT_OK, intent);
                    finish();
                }
            }
        }, 0);
    }

}
