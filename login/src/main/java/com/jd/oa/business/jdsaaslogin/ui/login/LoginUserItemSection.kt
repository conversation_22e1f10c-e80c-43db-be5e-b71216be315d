package com.jd.oa.business.jdsaaslogin.ui.login

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.elliptical.SuperEllipticalImageView
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.NClick
import com.jme.login.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class LoginUserItemSection(val context: Context, val list : List<TeamUserInfo>?, val clickListener : ClickListener?) : Section(
    SectionParameters.builder()
        .itemResourceId(R.layout.jdsaas_login_user_item)
        .build()) {

    override fun getContentItemsTotal(): Int {
        return list?.size?:0
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(holder: RecyclerView.ViewHolder?, position: Int) {
        val teamUserInfo = list?.get(position)
        val itemHolder = holder as ItemViewHolder
        itemHolder.tv_user_name.text = teamUserInfo?.realName
        itemHolder.tv_user_sub_name.visibility = View.GONE
        JdSaasAvatarUtil.loadPersonalAvatar(context,itemHolder.iv_user_avatar,teamUserInfo?.avatar)

        if(teamUserInfo?.activeStatus == 0){ //待激活
            itemHolder.cl_root.background = context.getDrawable(R.drawable.jdsaas_login_team_stroke_corner_bg_unactivated)
            itemHolder.iv_user_avatar_mask_layer.visibility = View.VISIBLE
            itemHolder.tv_user_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
            itemHolder.tv_user_sub_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
            itemHolder.iv_user_right_arrow.visibility = View.GONE
            itemHolder.tv_user_status.visibility = View.VISIBLE
            itemHolder.tv_user_status.text = context.getString(R.string.jdsaas_login_unactivated)
            itemHolder.tv_user_status.background = context.getDrawable(R.drawable.jdsaas_tv_team_status_unactivated)
            itemHolder.tv_user_status.setTextColor(context.getColor(R.color.jdsaas_color_blue_4A5FE8))
        }
//        else if(position == ){ //已停用
//            itemHolder.cl_root.background = context.getDrawable(R.drawable.jdsaas_login_team_stroke_corner_bg_unactivated)
//            itemHolder.view_team_avatar_mask_layer.visibility = View.VISIBLE
//            itemHolder.tv_team_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
//            itemHolder.tv_team_sub_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
//            itemHolder.iv_team_right_arrow.visibility = View.GONE
//            itemHolder.tv_team_status.visibility = View.VISIBLE
//            itemHolder.tv_team_status.text = context.getString(R.string.jdsaas_login_deactivated)
//            itemHolder.tv_team_status.background = context.getDrawable(R.drawable.jdsaas_tv_team_status_deactivated)
//            itemHolder.tv_team_status.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
//        }


        itemHolder.itemView.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            clickListener?.onItemClickListener(this,position,list?.get(position))
        }
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val cl_root = view.findViewById<ConstraintLayout>(R.id.cl_root)
        val tv_user_name = view.findViewById<TextView>(R.id.tv_user_name)
        val tv_user_sub_name = view.findViewById<TextView>(R.id.tv_user_sub_name)
        val iv_user_avatar = view.findViewById<SuperEllipticalImageView>(R.id.iv_user_avatar)
        val iv_user_avatar_mask_layer = view.findViewById<View>(R.id.iv_user_avatar_mask_layer)
        val tv_user_status = view.findViewById<TextView>(R.id.tv_user_status)
        val iv_user_right_arrow = view.findViewById<IconFontView>(R.id.iv_user_right_arrow)

    }

    interface ClickListener{
        fun onItemClickListener(section: LoginUserItemSection, position: Int, item: TeamUserInfo?)
    }
}