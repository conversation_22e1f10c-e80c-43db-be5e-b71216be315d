package com.jd.oa.business.liveness.set;

import com.jd.oa.business.liveness.model.AllLimitBean;
import com.jd.oa.business.liveness.model.AllStatusBean;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/12/19.
 */
public interface LivenessSetContract {
    interface View extends IMVPView {
        void showContent();

        void showMsgToast(String msg);

        /**
         * 协议回调
         *
         * @param isSuccess 通信是否成功
         * @param isAgree   是否同意
         */
        void onSignLoginFinish(boolean isSuccess, boolean isAgree);

        void onCheckUploadFinish(boolean hasUpload);

        void onSignClockFinish(boolean isSuccess, boolean isAgree);

        void changeAllAgreementStatus(AllStatusBean allStatusBean);

        void showAllLimits(AllLimitBean allLimitBean);
    }

    interface Presenter extends IMVPPresenter {
        /**
         * 获取所有刷脸项的开关状态
         */
        void getAllAgreementStatus();

        void signLoginAgreement(String flag);

        /**
         * 检测是否上传过刷脸模板
         */
        void checkLivenessUpload();

        /**
         * 修改刷脸打卡的协议状态（开关）
         *
         * @param flag
         */
        void signClock(String flag);

        /**
         * 获取所有刷脸项的权限
         */
        void getAllLimit();
    }

    interface Repo extends IMVPRepo {
        /**
         * 签署刷脸登录的协议
         *
         * @param callback
         */
        void signLoginAgreement(LoadDataCallback<String> callback, String flag);

        void getUploadLiveness(LoadDataCallback<Map> callback);

        /**
         * 刷脸打卡签署协议
         *
         * @param callback
         */
        void signClock(LoadDataCallback<String> callback, String flag);

        /**
         * 获取所有刷脸的权限
         *
         * @param callback
         */
        void getAllLimit(LoadDataCallback<AllLimitBean> callback);

        /**
         * 获取所有刷脸的协议状态
         *
         * @param callback
         */
        void getAllAgreementStatus(LoadDataCallback<AllStatusBean> callback);
    }
}
