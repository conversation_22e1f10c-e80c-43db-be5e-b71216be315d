package com.jd.oa.business

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.ui.login.LoginTeamFooterSection
import com.jd.oa.business.jdsaaslogin.ui.login.LoginTeamHeaderSection
import com.jd.oa.business.jdsaaslogin.ui.login.LoginTeamItemSection
import com.jd.oa.business.jdsaaslogin.ui.login.TYPE_TEAM
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter
import kotlinx.android.synthetic.main.jdsaas_fragment_login_team.view.rv_team
import kotlinx.android.synthetic.main.jdsaas_fragment_login_team.view.titleBar
import kotlinx.android.synthetic.main.jdsaas_login_titlebar.view.iv_back
import kotlinx.android.synthetic.main.jdsaas_login_titlebar.view.tv_title

class LoginTeamFragment : BaseFragment(), LoginTeamItemSection.ClickListener {

    val viewModel by lazy { ViewModelProvider(requireActivity(),InjectorUtil.getLoginModelFactory()).get(LoginViewModel::class.java) }
    lateinit var mView: View

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.jdsaas_fragment_login_team,container,false)
        if (StatusBarConfig.enableImmersive()) {
            mView.titleBar.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        }
        mView.iv_back.setOnClickListener {
            backPress()
        }

        val teamData = viewModel.mGetAccountListApiResponse?.data
        val manager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        mView.rv_team.setLayoutManager(manager)
        val sectionedAdapter = SectionedRecyclerViewAdapter()
        val loginTeamHeaderSection = LoginTeamHeaderSection(requireContext(),TYPE_TEAM)
        if(viewModel.loginType == LoginType.TYPE_PHONE){
            loginTeamHeaderSection.userRepresent = "+${viewModel.mCountryCode}${JdSaasLoginHelper.maskPhoneNumber(viewModel.mPhone)}"
        }else if(viewModel.loginType == LoginType.TYPE_ACCOUNT){
            loginTeamHeaderSection.userRepresent = viewModel.mAccount
        }
        sectionedAdapter.addSection(loginTeamHeaderSection)
        var teamAccountInfoList = teamData?.teamAccountInfoList
        val hasMoreData = (teamAccountInfoList?.size?:0) > 10
        if(hasMoreData){
            teamAccountInfoList = teamData?.teamAccountInfoList?.subList(0,10)
            sectionedAdapter.addSection(LoginTeamItemSection(requireContext(),teamAccountInfoList,this))
            sectionedAdapter.addSection(LoginTeamFooterSection(object : LoginTeamFooterSection.ClickListener{
                override fun onLoadMoreClicked() {
                    sectionedAdapter.removeAllSections()
                    sectionedAdapter.addSection(loginTeamHeaderSection)
                    sectionedAdapter.addSection(LoginTeamItemSection(requireContext(),teamData?.teamAccountInfoList,this@LoginTeamFragment))
                    sectionedAdapter.notifyDataSetChanged()
                }
            }))
        }else{
            sectionedAdapter.addSection(LoginTeamItemSection(requireContext(),teamAccountInfoList,this))
        }
        mView.rv_team.adapter = sectionedAdapter

        mView.rv_team.addOnScrollListener(object : OnScrollListener(){
            var distanceY = 0
            val titleHeight = Math.round(requireActivity().resources.displayMetrics.density * 35)
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                distanceY += dy
                if(distanceY > titleHeight){
                    mView.tv_title.visibility = View.VISIBLE
                    mView.tv_title.text = getString(R.string.jdsaas_login_team_choice_tip1)
                }else{
                    mView.tv_title.visibility = View.GONE
                    mView.tv_title.text = null
                }
            }
        })
        return mView
    }

    override fun onBackPressed(): Boolean {
        backPress()
        return true
    }

    private fun backPress(){
        requireActivity().supportFragmentManager.popBackStack()
    }

    override fun onItemClickListener(
        section: LoginTeamItemSection,
        position: Int,
        item: TeamAccountInfo?
    ) {
        if(item?.teamUserInfoList?.size == 1){
            val teamAccountInfo = item
            val teamUserInfo = item.teamUserInfoList.first()
            if(viewModel.loginType == LoginType.TYPE_PHONE){
                viewModel.loginWithName(teamAccountInfo,teamUserInfo)
            }else if(viewModel.loginType == LoginType.TYPE_ACCOUNT){
                viewModel.loginByToken(0,teamAccountInfo,teamUserInfo)
            }
        }else{
            viewModel.toLoginUserFragment(item)
        }
    }
}