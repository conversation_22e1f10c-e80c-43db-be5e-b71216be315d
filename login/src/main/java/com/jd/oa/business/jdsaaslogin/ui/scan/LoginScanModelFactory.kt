package com.jd.oa.business.jdsaaslogin.ui.scan

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.jdsaaslogin.data.LoginScanRepository

class LoginScanModelFactory(private val repository: LoginScanRepository) :
    ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return LoginScanViewModel(repository) as T
    }
}