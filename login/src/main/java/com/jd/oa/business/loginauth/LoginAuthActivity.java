package com.jd.oa.business.loginauth;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;
import android.os.Bundle;
import androidx.constraintlayout.widget.Group;
import androidx.appcompat.app.ActionBar;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.melib.exception.NetException;
import com.jd.oa.router.DeepLink;
import com.jme.login.R;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.BiConsumer;
import io.reactivex.functions.Consumer;

/**
 * Created by peidongbiao on 2018/4/25.
 */
@Route(DeepLink.VPN_OLD)
public class LoginAuthActivity extends BaseActivity {
    private static final String TAG = "LoginAuthActivity";

    public static final String ARG_BUSINESS_ID = "businessId";
    private static final int EXPIRED_TIME = 5;

    private TextView mTvClose;
    private TextView mTvTitle;
    private TextView mTvTips;

    private Button mBtnLogin;
    private Button mBtnCancel;
    private Group mGroup;

    private String mBusinessId;
    private AuthInfo mAuthInfo;
    private AuthRepo mAuthRepo;
    private CompositeDisposable mCompositeDisposable;
    private Disposable mCountdownDisposable;
    private ProgressDialog mDialogLoading;

    public static void start(Activity activity, String businessId) {
        Intent intent = new Intent(activity, LoginAuthActivity.class);
        intent.putExtra(ARG_BUSINESS_ID, businessId);
        activity.startActivity(intent);
        activity.overridePendingTransition(R.anim.jdme_anim_slide_up, R.anim.jdme_anim_no_animation);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
        setContentView(R.layout.jdme_activity_login_auth);

        mBusinessId = getIntent().getStringExtra(ARG_BUSINESS_ID);
        mCompositeDisposable = new CompositeDisposable();

        mBtnLogin = findViewById(R.id.btn_login);
        mTvClose = findViewById(R.id.tv_close);
        mTvTitle = findViewById(R.id.tv_title);
        mTvTips = findViewById(R.id.tv_tips);
        mBtnCancel = findViewById(R.id.btn_cancel);
        mGroup = findViewById(R.id.group_btn);
        mAuthRepo = AuthRepo.get(this);

        mTvClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                auth(false);
                finish();
            }
        });
        mBtnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                auth(true);
            }
        });
        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                auth(false);
                finish();
            }
        });

        getAuthInfo(mBusinessId);
        countdown();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        String businessId = intent.getStringExtra(ARG_BUSINESS_ID);
        if (TextUtils.isEmpty(businessId)) return;
        mBusinessId = businessId;
        getAuthInfo(mBusinessId);
        countdown();
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.jdme_anim_no_animation, R.anim.jdme_anim_slide_down);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mCompositeDisposable.dispose();
    }

    private void countdown() {
        if (mCountdownDisposable != null) {
            mCountdownDisposable.dispose();
        }
        mCountdownDisposable = Observable.timer(EXPIRED_TIME, TimeUnit.MINUTES)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                        mGroup.setVisibility(View.GONE);
                        mTvTips.setText(R.string.me_login_auth_expired);
                    }
                });
        mCompositeDisposable.add(mCountdownDisposable);
    }

    private void fill(AuthInfo info) {
        mAuthInfo = info;
        mTvTitle.setText(mAuthInfo.getBusinessName());
        if (info.getStatus() == AuthInfo.STATUS_NORMAL) {
            mGroup.setVisibility(View.VISIBLE);
        } else if (info.getStatus() == AuthInfo.STATUS_CANCELED) {
            mGroup.setVisibility(View.INVISIBLE);
        } else if (info.getStatus() == AuthInfo.STATUS_EXPIRED) {
            mGroup.setVisibility(View.INVISIBLE);
        }
        mTvTips.setText(mAuthInfo.getShowMessage());
    }

    private void getAuthInfo(String businessId) {
        Disposable disposable = mAuthRepo.getAuthInfo(businessId)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<AuthInfo>() {
                    @Override
                    public void accept(AuthInfo info) throws Exception {
                        if (isFinishing()) return;
                        fill(info);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "accept: ", throwable);
                        if (isFinishing()) return;
                        if (throwable instanceof NetException) {
                            mTvTips.setText(throwable.getMessage());
                        }
                    }
                });
        mCompositeDisposable.add(disposable);
    }

    private void auth(final boolean result) {
        if (mAuthInfo == null) return;
        Disposable disposable = mAuthRepo.auth(mAuthInfo.getBusinessId(), result)
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe(new Consumer<Disposable>() {
                    @Override
                    public void accept(Disposable disposable) throws Exception {
                        if (result) {
                            showLoadingDialog(getString(R.string.me_loading_message));
                        }
                    }
                })
                .doOnEvent(new BiConsumer<String, Throwable>() {
                    @Override
                    public void accept(String s, Throwable throwable) throws Exception {
                        if (!isFinishing()) {
                            hideLoadingDialog();
                        }
                    }
                })
                .subscribe(new Consumer<String>() {
                    @Override
                    public void accept(String s) throws Exception {
                        if (isFinishing()) return;
                        if (TextUtils.isEmpty(s)) {
                            mTvTips.setText(result ? R.string.me_login_auth_client_confirmed : R.string.me_login_auth_client_canceled);
                        } else {
                            mTvTips.setText(s);
                        }
                        mTvTips.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                finish();
                            }
                        }, 1000);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "accept: ", throwable);
                        if (isFinishing()) return;
                        if (throwable instanceof NetException) {
                            mTvTips.setText(throwable.getMessage());
                        }
                    }
                });
        mCompositeDisposable.add(disposable);
    }

    public void showLoadingDialog(String message) {
        if (this.mDialogLoading == null) {
            this.mDialogLoading = new ProgressDialog(this);
            this.mDialogLoading.setProgressStyle(ProgressDialog.STYLE_SPINNER);
            this.mDialogLoading.setCancelable(true);
            this.mDialogLoading.setCanceledOnTouchOutside(true);
        }

        this.mDialogLoading.setMessage(message);
        this.mDialogLoading.show();
    }

    public void hideLoadingDialog() {
        if (this.mDialogLoading != null && this.mDialogLoading.isShowing()) {
            this.mDialogLoading.dismiss();
        }
    }
}