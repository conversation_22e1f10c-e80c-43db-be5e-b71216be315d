package com.jd.oa.business.login.controller;

import static com.jd.oa.audio.JMAudioCategoryManager.AUDIO_MANAGER;
import static com.jd.oa.business.login.controller.LoginActivity.PIN_TAG;
import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.utils.WebViewUtils.openWeb;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Paint;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Looper;
import android.os.Message;
import android.os.MessageQueue;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.animation.Animation;
import android.view.animation.Animation.AnimationListener;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.Router;
import com.jd.flutter.common.handler.MeFlutterThemePlugin;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.apm.ApmLoaderHepler;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.business.login.model.UserEntity;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.listener.MyDialogDoneListener;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.ColorGatewayNetEnvironment;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.FlutterPreference;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.MailPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.provider.PunchNotifyProvider;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.NetModule;
import com.jd.oa.storage.StorageHelper;
import com.jd.oa.theme.manager.ThemeManager;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Holder;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.PauseHandler;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.WebViewUtils;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jd.oa.utils.encrypt.RSAUtil;
import com.jd.push.JDPushManager;
import com.jme.login.R;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * 登录fragment
 *
 * <AUTHOR>
 */

public class LoginFragment extends BaseFragment implements View.OnClickListener {

    /**
     * Used for "what" parameter to handler messages
     */
    public static final String RECEIVE_TYPE_MAIL = "1";
    public static final String RECEIVE_TYPE_MSG = "2";
    final static int MSG_WHAT = 22;
    final static int MSG_REMOVE_DIALOG = 1;
    private static final String TAG = "LoginFragment";
    private final static String ERROR_CODE_ONE = "0001";
    private final long time = 60 * 1000; //计时器时长
    private final long duration = 1000;  //计时器频率
    /**
     * Handler for this activity
     */
    public ConcreteTestHandler handler = new ConcreteTestHandler();
    LinearLayout ll_server_switcher;

    RadioGroup rg_server_switcher;

    RadioButton rb_server_switcher_39;

    RadioButton rb_server_switcher_55;

    RadioButton rb_server_switcher_gy;

    RadioButton rb_server_switcher_official;

    RadioButton tv_server_switcher_others;
    private Animation mLogin_anmi;

    private EditText mAccount;

    private EditText mPwd;
    /**
     * login Btn
     */

    private TextView mLoginBtn;

    private TextView mLoginTipBtn;

    private TextView mLoginTipH5;

    private LinearLayout msgLayout;

    private TextView msgBtn; //获取验证码Button

    private EditText msg; //短信验证码

    private ImageView iv_animation_img;

    private TextView tv_msg_valid;
    private boolean isNeedValidMsg = false; //是否调用验证码登录接口标记
    private MyCountDownTimer countDownTimer;
    /**
     * if user change account the new icon will show in this imageview
     */

    private ImageView mUserIcon;

    private View me_login_liveness;

    private View me_login_locale;

    private CheckBox cbPwd;

    private TextView mTvDebug;

    /**
     * 通信用
     */
    private CommunicationListener communicationListener;
    private String mReceiveType = RECEIVE_TYPE_MSG;
    private boolean mAbleToSendEmail = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 登录页时，清空一些标记
        MyPlatform.sHasLock = false;
        MyPlatform.sMainActivityUnlocked = false;
        //清除用户的图像与姓名
        clearUserLocalInfo();
    }

    private void clearUserLocalInfo() {
        PreferenceManager.UserInfo.setUserCover("");
        PreferenceManager.UserInfo.setUserRealName("");
        Uri uri = Uri.parse("content://" + PunchNotifyProvider.AUTHORITY() + "/punch");
        ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
        contentResolver.delete(uri, null, null);
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof CommunicationListener) {
            communicationListener = (CommunicationListener) context;
        } else { // 解决异常，有可能在
            // throw new RuntimeException("Activity must implement " + CommunicationListener.class.getName());
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        communicationListener = null;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_login, container, false);
        ActionBarHelper.init(this, view);

        mAccount = view.findViewById(R.id.et_username);
        mPwd = view.findViewById(R.id.et_pwd);

        mLoginBtn = view.findViewById(R.id.tv_login);
        mLoginTipBtn = view.findViewById(R.id.tv_login_tip);
        mLoginTipH5 = view.findViewById(R.id.tv_login_tip_h5);
        msgLayout = view.findViewById(R.id.msg_layout);
        msgBtn = view.findViewById(R.id.tv_msg_valid);
        msg = view.findViewById(R.id.tv_msg);
        iv_animation_img = view.findViewById(R.id.iv_animation_img);
        tv_msg_valid = view.findViewById(R.id.tv_msg_valid);

        mUserIcon = view.findViewById(R.id.me_iv_circle);
        me_login_liveness = view.findViewById(R.id.me_login_liveness);
        me_login_locale = view.findViewById(R.id.me_login_locale);
        cbPwd = view.findViewById(R.id.cbPwd);

        mTvDebug = view.findViewById(R.id.jdme_tv_debug);

        mLoginBtn.setOnClickListener(this);
        mLoginTipBtn.setOnClickListener(this);
        msgBtn.setOnClickListener(this);
        me_login_liveness.setOnClickListener(this);
        me_login_locale.setOnClickListener(this);
        mLoginTipH5.setOnClickListener(this);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            View containerLayout = view.findViewById(R.id.layout_container);
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) containerLayout.getLayoutParams();
            layoutParams.topMargin = DisplayUtil.getStatusBarHeight(getContext());
            containerLayout.setLayoutParams(layoutParams);
        }

        mLogin_anmi = AnimationUtils.loadAnimation(this.getActivity(), R.anim.jdme_anim_login);
        mLogin_anmi.setFillAfter(true);
        mLogin_anmi.setAnimationListener(new AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                // 通知login事件
                FragmentUtils.updateUI(OperatingListener.OPERATE_LOGIN, null);
            }
        });

        setListener();
        InputMethodUtils.registerHideSoftWhenClick(getActivity(), view);

        if (AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER) {
            initDebugView(view);
        } else {
            //  ll_server_switcher.setVisibility(View.INVISIBLE);
        }
        return view;
    }

    //初始化debug版本时的控件
    private void initDebugView(View view) {
        mTvDebug.setVisibility(View.VISIBLE);
        mTvDebug.setText(String.format(Locale.getDefault(), "%s %s", AppBase.VERSION_NAME, AppBase.BUILD_TYPE));
        ViewStub viewStub = (ViewStub) view.findViewById(R.id.layout_server_switcher);
        ll_server_switcher = (LinearLayout) viewStub.inflate();

        rg_server_switcher = view.findViewById(R.id.rg_server_switcher);
        rb_server_switcher_39 = view.findViewById(R.id.rb_server_switcher_39);
        rb_server_switcher_55 = view.findViewById(R.id.rb_server_switcher_55);
        rb_server_switcher_gy = view.findViewById(R.id.rb_server_switcher_gy);
        rb_server_switcher_official = view.findViewById(R.id.rb_server_switcher_official);
        tv_server_switcher_others = view.findViewById(R.id.tv_server_switcher_others);

        final NetEnvironmentConfigModel netEnvironmentConfig = LocalConfigHelper.getInstance(requireContext()).getNetEnvironmentConfig();
        String currentEnv = PreferenceManager.UserInfo.getNetEnvironment();
        if (TextUtils.isEmpty(currentEnv)) {
            currentEnv = netEnvironmentConfig.getEnv();
        }
        if (NetEnvironmentConfigModel.TEST.equals(currentEnv)) {
            rb_server_switcher_39.setChecked(true);
        } else if (NetEnvironmentConfigModel.JDOS.equals(currentEnv)) {
            rb_server_switcher_55.setChecked(true);
        } else if (NetEnvironmentConfigModel.PROD.equals(currentEnv)) {
            rb_server_switcher_official.setChecked(true);
        } else if (NetEnvironmentConfigModel.PREV.equals(currentEnv)) {
            rb_server_switcher_gy.setChecked(true);
        } else {
            tv_server_switcher_others.setChecked(true);
            tv_server_switcher_others.setText(tv_server_switcher_others.getContext().getString(R.string.me_feedback_type_other));
        }

        rg_server_switcher.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @SuppressLint("WrongConstant")
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                final Holder<String> holder = new Holder<>();
                holder.set(NetEnvironmentConfigModel.PROD);
                if (checkedId == R.id.rb_server_switcher_39) {
                    holder.set(NetEnvironmentConfigModel.TEST);
                } else if (checkedId == R.id.rb_server_switcher_55) {
                    holder.set(NetEnvironmentConfigModel.JDOS);
                } else if (checkedId == R.id.rb_server_switcher_gy) {
                    holder.set(NetEnvironmentConfigModel.PREV);
                } else if (checkedId == R.id.tv_server_switcher_others) {
                    String defText = getString(R.string.me_string_server_switcher_hint);
                    String address = PreferenceManager.Other.getCustomServerAddress();
                    if (!TextUtils.isEmpty(address)) {
                        defText = address;
                    }
                    PromptUtils.showInputDialog(getActivity(), R.string.me_string_server_switcher_title,
                            R.string.me_string_server_switcher_title,
                            defText, false, new MyDialogDoneListener() {
                                @Override
                                public void onDialogDone(Activity activity, boolean isCancel, CharSequence message) {
                                    String address;
                                    if (StringUtils.isNotEmptyWithTrim(message.toString())) {
                                        if (message.toString().contains("http://") || message.toString().contains("https://")) {
                                            address = message.toString();
                                        } else {
                                            address = "http://" + message.toString();
                                        }
                                    } else {
                                        address = NetworkConstant.ADDRESS_SERVER_PERSONAL_PC;
                                    }
                                    PreferenceManager.Other.setCustomServerAddress(address);

                                    holder.set(NetEnvironmentConfigModel.CUSTOM);

                                    tv_server_switcher_others.setText(tv_server_switcher_others.getContext().getString(R.string.me_feedback_type_other) + address);
                                }
                            });
                } else {
                    holder.set(NetEnvironmentConfigModel.PROD);
                }

                NetEnvironment legacy;
                GatewayNetEnvironment gateway;
                String chooseEnv = holder.get();
                if (NetEnvironmentConfigModel.CUSTOM.equals(chooseEnv)) {
                    String address = PreferenceManager.Other.getCustomServerAddress();
                    legacy = new NetEnvironment(address, address);
                } else {
                    legacy = netEnvironmentConfig.getNonGateway(chooseEnv);
                }
                gateway = netEnvironmentConfig.getGateway(chooseEnv);

                NetEnvironment.setCurrentEnv(legacy);
                GatewayNetEnvironment.setCurrentEnv(gateway);
                ColorGatewayNetEnvironment.setCurrentEnv(netEnvironmentConfig.getColorGateway(chooseEnv));

                PreferenceManager.UserInfo.setNetEnvironment(chooseEnv);

                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        ConfigurationManager.get().syncConfigurations();
                    }
                }).start();
            }
        });
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mAccount.setText(PreferenceManager.UserInfo.getUserName());

        // 设置用户头像
        String coverUrl = PreferenceManager.UserInfo.getUserCover();
        ImageLoaderUtils.getInstance().displayHeadImage(coverUrl, mUserIcon, true);

        // 刷脸的代码
        // 人脸识别相关
        me_login_liveness.setVisibility("1".equals(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD)) ? View.VISIBLE : View.GONE);
        // mUserIcon
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail((R.drawable.jdme_profile_gray)).showImageOnLoading(R.drawable.jdme_profile_gray).showImageForEmptyUri(R.drawable.jdme_profile_gray).build();
        ImageLoaderUtils.getInstance().displayImage(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_COVER), mUserIcon, displayImageOptions);
        mAccount.setText(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME));
    }

    private void setListener() {
        // 设置登录不可点击状态
        mAccount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                setViewEnabled(mLoginBtn, mPwd.getText().toString(), mAccount.getText().toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        mPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                setViewEnabled(mLoginBtn, mAccount.getText().toString(), mPwd.getText().toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        mAccount.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (!hasFocus) { // 失去焦点
                    String newAccount = mAccount.getText().toString().trim();
                    if (StringUtils.isNotEmptyWithTrim(newAccount)) {
                        // 每次都保存新的用户名
                        PreferenceManager.UserInfo.setUserName(newAccount);
                    }
                }
            }
        });
        //显示或隐藏密码
        StringUtils.showPwd(cbPwd, mPwd);
    }

    /***
     * 根据input来 enable View
     *
     * @param input 输入值
     * @param view  操作的view对象
     */
    private void setViewEnabled(View view, String... input) {
        if (input != null) {
            for (String str : input) {
                if (StringUtils.isEmptyWithTrim(str)) {
                    view.setEnabled(false);
                    break;
                }
                view.setEnabled(true);
            }
        } else {
            view.setEnabled(false);
        }
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);

        int id = v.getId();

        if (id == R.id.tv_login) {
            // 登录操作
            InputMethodUtils.hideSoftInput(getActivity()); //隐藏输入法
            if (!NClick.isFastDoubleClick()) {
                if (isNeedValidMsg) {
                    doUnionLogin();
                } else {
                    // 记住当前用户名至配置文件
                    String account = mAccount.getText().toString().trim();
                    PreferenceManager.UserInfo.setUserName(account);
                    MyPlatform.getCurrentUser().setUserName(account);
                    InputMethodUtils.hideSoftInput(getActivity());
                    if (checkInput()) {
                        ExecutorService executors = Executors.newSingleThreadExecutor();
                        executors.execute(new Runnable() {
                            @Override
                            public void run() {
                                doLogin();
                            }
                        });
                    }
                }
            }
        } else if (id == R.id.tv_login_tip_h5) {
            showFailReceiveCodeDialog();
        } else if (id == R.id.tv_msg_valid) {
            /**
             * 由于支持自动发送验证码，所以这里不控制DoubleClick
             */
            // 短信
            sendValidateMsg();
        } else if (id == R.id.me_login_liveness) {
            // 切换登录形式
            toLoginByLiveness();
        } else if (id == R.id.me_login_locale) {
            Intent intent = Router.build(DeepLink.ACTIVITY_URI_Function).getIntent(getActivity());
            intent.putExtra(AppBase.FLAG_FUNCTION, "com.jd.oa.business.setting.LanguageLocalSetFragment");
            startActivity(intent);
        }
    }

    private void sendValidateMsg() {
        msgBtn.setEnabled(false);
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        countDownTimer = new MyCountDownTimer(time, duration);
        countDownTimer.start();

        NetWorkManagerLogin.msgValidate(this, mAccount.getText().toString().trim(), mPwd.getText().toString().trim(), mReceiveType,
                new SimpleRequestCallback<String>(null, true) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        if (!isAlive()) return;
                        String json = info.result;
                        ResponseParser parser = new ResponseParser(json, getActivity());
                        parser.parse(new ParseCallback() {
                            @Override
                            public void parseObject(JSONObject jsonObject) {
                                try {
                                    String msg = jsonObject.getString("msg");
                                    String ableToSendEmail = jsonObject.optString("ableToSendEmail");
                                    mAbleToSendEmail = TextUtils.equals(ableToSendEmail, "1");
                                    mLoginTipBtn.setText(msg);
                                    mLoginTipH5.setVisibility(View.VISIBLE);
                                    mLoginTipH5.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //文本下划线
                                    mLoginTipH5.getPaint().setAntiAlias(true);                    //消除锯齿
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void parseArray(JSONArray jsonArray) {
                            }

                            @Override
                            public void parseError(String errorMsg) {
                            }
                        });
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                    }
                });
    }

    private void showFailReceiveCodeDialog() {
        List<String> list = new ArrayList<>();
        list.add(getString(R.string.me_login_wrong_phone_number));
        if (mAbleToSendEmail) {
            list.add(getString(R.string.me_login_email_verification_coder));
        }
        PromptUtils.showListDialog(getActivity(), getString(R.string.me_login_fail_receive_verification_code), list, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int index) {
                switch (index) {
                    case 0:
                        //手机号有误
                        if (!NClick.isFastDoubleClick()) {
                            openWeb(getActivity(), NetworkConstant.PARAM_SERVER_OUTTER + "/notice.html", WebConfig.H5_NATIVE_HEAD_HIDE);
                        }
                        break;
                    case 1:
                        if (!msgBtn.isEnabled()) {
                            ToastUtils.showToast(R.string.me_login_code_wait_tip);
                            return;
                        }
                        //发送验证码到邮箱
                        mReceiveType = RECEIVE_TYPE_MAIL;
                        sendValidateMsg();
                        mReceiveType = RECEIVE_TYPE_MSG;
                        break;
                }
            }
        });
    }

    private void toLoginByLiveness() {
        if (communicationListener != null) {
            communicationListener.doCommunication();
        }
    }

    public void toLeaveWeb(String url) {

        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Function).getIntent(AppBase.getAppContext());
        WebBean bean = new WebBean(url, WebConfig.H5_NATIVE_HEAD_SHOW);
        intent.putExtra(EXTRA_WEB_BEAN, bean);
        intent.putExtra(AppBase.FLAG_FUNCTION, WebViewUtils.getName());

        getActivity().startActivity(intent);
//        PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_DISMISSION);
        JDMAUtils.onEventClick(JDMAConstants.mobile_resignationData_click,JDMAConstants.mobile_resignationData_click);
    }

    /**
     * 执行登录请求
     */
    private void doLogin() {
        //生成公私钥
        String publicKey = KeyManager.getInstance().getPublicKey();
        NetWorkManagerLogin.exchangePublicKey(mAccount.getText().toString().trim(), publicKey, new SimpleRequestCallback<String>(null, false, true) {
            @Override
            public void onStart() {
                super.onStart();
                // 1.显示加载框
                PromptUtils.showLoadDialog(getActivity(), AppBase.getAppContext().getString(R.string.me_logining));
            }

            @Override
            public void onSuccess(final ResponseInfo<String> info) {
                super.onSuccess(info);
                if (!isAlive()) return;
                // 2.移除加载框 (修改：避免 异常 onSaveInstanceState 异常)
                LoginFragment.this.handler.sendMessage(LoginFragment.this.handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, Map.class);
                if (!response.isSuccessful()) {
                    //请求失败时清除秘钥，下次重新生成
                    KeyManager.getInstance().clearKeys();
                    ToastUtils.showToast(response.getErrorMessage());
                    return;
                }
                String publicKey = response.getData().get("serverPublicKeyStr");
                KeyManager.getInstance().storeServerPublicKey(publicKey);
                doNetworkLogin();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if (!isAlive()) return;
                //请求失败时清除秘钥，下次重新生成
                KeyManager.getInstance().clearKeys();
                // 2.移除加载框
                LoginFragment.this.handler.sendMessage(LoginFragment.this.handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));
            }
        });
    }

    /**
     * 记住用户信息
     *
     * @param user
     */
    private static void rememberUser(UserEntity user) {
        MyPlatform.setCurrentUser(user);
        PreferenceManager.UserInfo.setUserName(user.getUserName());
        PreferenceManager.UserInfo.setUserRealName(user.getRealName());
        PreferenceManager.UserInfo.setJdAccount(user.getJdAccount());
        PreferenceManager.UserInfo.setUserCover(user.getUserIcon());
        PreferenceManager.UserInfo.setUserAttendance(user.getAttendance());
        PreferenceManager.UserInfo.setUserSexFlag(user.getSex());
        PreferenceManager.UserInfo.setLogin(true);
        PreferenceManager.UserInfo.setTenantCode(user.getTenantCode());
        PreferenceManager.UserInfo.setTimlineAppID(user.getAppId());
        PreferenceManager.UserInfo.setTenantCodeList(user.getTenantCodeList());
        PreferenceManager.UserInfo.setBirthday(user.getBirthday());
        PreferenceManager.UserInfo.setTeamId(user.getTeamId());
        PreferenceManager.UserInfo.setUserId(user.getUserId());
        PreferenceManager.UserInfo.setTeamName(user.getTeamName());
        PreferenceManager.UserInfo.setLevelDesc(user.getLevelDesc());
        PreferenceManager.UserInfo.setWjLoginPin(user.getWjLoginPin());
    }

    /**
     * 检查用户输入
     */
    private boolean checkInput() {
        if (StringUtils.isEmptyWithTrim(mAccount.getText().toString())) {
            ToastUtils.showInfoToast(R.string.me_erp_account_is_empty);
            return false;
        }
        if (StringUtils.isEmptyWithTrim(mPwd.getText().toString())) {
            ToastUtils.showInfoToast(R.string.me_pass_is_empty);
            return false;
        }
        return true;
    }

    /**
     * 校验登录成功的签名
     *
     * @return
     */
    static public boolean checkResponseSign(String content, String sign) {
        String publicKey = KeyManager.getInstance().getServerPublicKey();
        return RSAUtil.verify(publicKey, content, sign);
    }

    static public String decryptResponse(String encryptData) {
        String privateKey = KeyManager.getInstance().getPrivateKey();
        return RSAUtil.decrypt(privateKey, encryptData, KeyManager.DECRYPT_SEGMENT_SIZE);
    }

    /**
     * 登录成功处理，发送消息至Activity，交由Activity处理
     */
    public static void loginSuccessData(String data, String pwd) {
        Log.i("=====================l", data);
        try {
            MELogUtil.localV(AUDIO_MANAGER, "loginSuccessData");
            MELogUtil.onlineV(AUDIO_MANAGER, "loginSuccessData");
            JMAudioCategoryManager.getInstance().init();
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            LogRecorder.getDefault().record(MELogUtil.TAG_LGI, TextUtils.isEmpty(pwd) ? "snapLoginSuccess" : "loginSuccess", null);
            JSONObject jsonObject = new JSONObject(data);
            String realName = jsonObject.optString("realName");
            String jdAccount = jsonObject.optString("jdAccount");
            String userIcon = jsonObject.optString("userIcon", "");
            String attendance = jsonObject.optString("isAttendance");
            String sex = jsonObject.optString("sex");
            String homeDeeplink = jsonObject.optString("homeDeeplink");
            String refreshToken = jsonObject.optString("refreshToken");
            String accessToken = jsonObject.optString("accessToken");
            String randomKey = jsonObject.optString("randomKey");
            String tenantCode = jsonObject.optString("tenantCode");
            String tenantCodeList = jsonObject.optString("tenantCodeList");
            String appId = jsonObject.optString("appId");
            String tenantConfig = jsonObject.optString("tenantConfig");
            String erp = jsonObject.optString("userName");
            String isSnap = jsonObject.optString("isSnap");
            String birthday = jsonObject.optString("birthday");

            String levelDesc = jsonObject.optString("levelDesc");

            String email = jsonObject.optString("email");
            String domainUserName = jsonObject.optString("domainUserName");
            /* 邮箱设置需求取消上线
            JSONObject setting = jsonObject.optJSONObject("setting");
            String isAutoBindEmail = "1";
            if (null != setting) {
                isAutoBindEmail = setting.optString("isAutoBindEmail");
            }
             */

            String teamId = jsonObject.optString("teamId");
            String userId = jsonObject.optString("userId");
            String teamName = jsonObject.optString("teamName");
            String wjLoginPin = jsonObject.optString("wjLoginPin");

            String mobile = jsonObject.optString("mobile");
            String telePhone = jsonObject.optString("telePhone");
            String userCode = jsonObject.optString("userCode");
            String organizationName = jsonObject.optString("organizationName");
            //是否有刷脸权限 一般用来区分国内、国外用户
            String hasFacePermission = jsonObject.optString("hasSnapPermission","1");

            PreferenceManager.UserInfo.setMobile(mobile);
            PreferenceManager.UserInfo.setTelephone(telePhone);
            PreferenceManager.UserInfo.setUserCode(userCode);
            PreferenceManager.UserInfo.setOrganizationName(organizationName);
            PreferenceManager.UserInfo.setHasFaceLoginPermission(hasFacePermission);
            //更新当前用户信息
            StorageHelper.getInstance(AppBase.getAppContext()).login(LocalConfigHelper.getInstance(AppBase.getAppContext()).getAppID(), appId, erp);

            //保存flutter通讯用参数
            FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_USERICON,userIcon);

            //保存token
            TokenManager.getInstance().storeToken(refreshToken, accessToken);
            PreferenceManager.UserInfo.setRandomKey(randomKey);

            // 咚咚登录的token
            String loginToken = jsonObject.optString("loginToken");
            String nonce = jsonObject.optString("nonce");
            if (!TextUtils.isEmpty(loginToken)) {
//                PreferenceManager.setString(PreferenceManager.UserInfo.TIMLINE_TOKEN, loginToken);
//                PreferenceManager.setString(PreferenceManager.UserInfo.TIMLINE_NONCE, nonce);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_TOKEN,loginToken);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_NONCE,nonce);
            }

            //邮箱地址
            PreferenceManager.UserInfo.setEmailAddress(email);
            //邮箱密码
            if (!TextUtils.isEmpty(pwd)) {
                String encryptString = JdmeEncryptUtil.getEncryptString(pwd);
                PreferenceManager.UserInfo.setEmailPwd(encryptString);
            }
            //邮箱帐号
            PreferenceManager.UserInfo.setEmailAccount(domainUserName);
//            PreferenceManager.UserInfo.setIsAutoBindEmail("1".equals(isAutoBindEmail));

            PreferenceManager.UserInfo.setHasBindEmailAccount(true);
            PreferenceManager.UserInfo.setHasShowBindEmailAccount(true);

            PreferenceManager.UserInfo.setTenantConfig(tenantConfig);

            UserEntity user = new UserEntity(erp, realName, userIcon, jdAccount, attendance, sex);
            user.setTenantCode(tenantCode);
            user.setTenantCodeList(tenantCodeList);
            user.setAppId(appId);
            user.setBirthday(birthday);
            user.setTeamId(teamId);
            user.setUserId(userId);
            user.setLevelDesc(levelDesc);
            user.setTeamName(teamName);
            user.setWjLoginPin(wjLoginPin);
            rememberUser(user);

            // 修改踢掉标记
            if (AppBase.iAppBase != null) {
                MELogUtil.localI(PIN_TAG, "loginSuccessData");
                MELogUtil.onlineI(PIN_TAG, "loginSuccessData");
                AppBase.iAppBase.setKickOut(false);
            }

            // 设置男女默认主题
            autoChangeThemeBySex(user.getSex());
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT,false);
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_OUT_TIPS,"");
            AppBase.loginImFromUserUi();

            // 人脸识别记录信息
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD,isSnap);
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_COVER,userIcon);

            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME,user.getUserName());

            // 重新初始化网络模块
            NetModule.initNetModule(AppBase.getAppContext());
            //更新用户Id
            ApmLoaderHepler.getInstance(AppBase.getAppContext()).updateUserId();

            //记录默认首页的标记
//            if (!TextUtils.isEmpty(homeDeeplink)) {
//                PreferenceManager.UserInfo.setDefaultTab(homeDeeplink);
//            }


            JSONObject agreements = jsonObject.optJSONObject("agreements");
            if (null != agreements) {
                /*
                 * 1、优惠券
                 * 2、刷脸协议 ？
                 * 3、拼车协议
                 * 4、会议室
                 * 5、刷脸协议 ？
                 * 6、隐私协议 启动项
                 * 7、京东互通
                 * 8、安全责任书
                 * 9、无痕打卡
                 *
                 * TODO 补全
                 */
                // 1 优惠券
//                PreferenceManager.setBoolean(PreferenceManager.Key.JDME_WELFARE_LOOKED, "1".equals(agreements.optString("1")));
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_WELFARE_LOOKED,"1".equals(agreements.optString("1")));
                // 3 拼车协议
                TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_JDME_AGREEMENT_TRAVLE,"1".equals(agreements.optString("3")));
                // 6 隐私协议
                PreferenceManager.UserInfo.setUserAgreementStatus("1".equals(agreements.optString("6")));
                // 7 京东互通
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_AGREEMENT_EVAL,"1".equals(agreements.optString("7")));
                // 9 无痕打卡
                PreferenceManager.UserInfo.setQuickDakaOpen("1".equals(agreements.optString("9")));
            }
            //JDPUSH 解绑
            JDPushManager.bindPin(AppBase.getAppContext(), user.getUserName());
            if (jsonObject.has("isFourWall")) {
                String isFourWall = jsonObject.optString("isFourWall");
                PreferenceManager.UserInfo.setIsFourWall(isFourWall);
            }

//            if ("1".equals(isAutoBindEmail)) {
            Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
                @Override
                public boolean queueIdle() {
                    subscribeMail();
                    return false;
                }
            });
//            }
            //获取theme
            ThemeManager.getInstance().checkTheme();
            //初始化浮动窗口
            MultiTaskManager.getInstance().resetFloatDate();
            //初始化Flutter主题
            MeFlutterThemePlugin.changTheme();

            // 增加动态化补偿接口
            MEDynamic.getInstance().reloadAllCard();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void loginSuccessUI(){
        MyPlatform.initAfterUserLogon();
    }

    static private void autoChangeThemeBySex(String sex) {
        String themeStyle = "jdme_AppTheme_Defalut";
        //Apps.Theme = R.style.jdme_AppTheme_Defalut;
        PreferenceManager.UserInfo.setThemeResName(themeStyle);
    }

    private void showLeaveDialog(String msg, final String leaveUrl) {
        PromptUtils.showAlertDialog(getActivity(), -1, msg, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                toLeaveWeb(leaveUrl);
            }
        }, false);
    }


    private void loginCodeOne(String errorMsg, JSONObject content) {

        if (content == null) {
            mLoginTipBtn.setText(errorMsg);
            return;
        }
        //已离职的erp，弹出dialog提示跳转离职H5
        String leaveUrl = content.optString("leaveUrl");
        if (!TextUtils.isEmpty(leaveUrl)) {
            showLeaveDialog(errorMsg, leaveUrl);
            return;
        }

        String operationCode = content.optString("operationCode");
        if (!TextUtils.isEmpty(operationCode)) {
            if (ERROR_CODE_ONE.equals(operationCode)) {
                //判断需要验证码的登录，需要进行第二次登录
                isNeedValidMsg = true;
                //显示验证码
                msgLayout.setVisibility(View.VISIBLE);
                //自动获取验证码
                tv_msg_valid.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        tv_msg_valid.performClick();
                    }
                }, 200);
            }
        } else {
            mLoginTipBtn.setText(errorMsg);
        }
    }

    /**
     * 登录,不带验证码
     */
    private void doNetworkLogin() {
        final String pwd = mPwd.getText().toString().trim();
        NetWorkManagerLogin.login(this, mAccount.getText().toString().trim(), pwd,
                new SimpleRequestCallback<String>(null) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        if (!isAlive()) return;

                        ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                        try {
                            String errorCode = response.getErrorCode();
                            String errorMsg = response.getErrorMessage();
                            final JSONObject content = new JSONObject(response.getData() == null ? "{}" : response.getData());
                            if ("1".equals(errorCode)) { //需要解析optionCode
                                loginCodeOne(errorMsg, content);
                            } else if ("0".equals(errorCode)) {
                                //登录成功
                                String sign = content.optString("sign");
                                String encryptData = content.optString("User");
                                if (checkResponseSign(encryptData, sign)) {
                                    String data = decryptResponse(encryptData);
                                    loginSuccessData(data, pwd);
                                    loginSuccessUI();
                                    mLoginBtn.setEnabled(false);
                                    iv_animation_img.setVisibility(View.VISIBLE);
                                    iv_animation_img.startAnimation(mLogin_anmi);
                                } else {
                                    Toast.makeText(getContext(), R.string.me_login_check_signature_fail, Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                mLoginTipBtn.setText(errorMsg);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                    }
                });
    }


    /**
     * 调用联合登录接口,带验证码
     */
    private void doUnionLogin() {
        // 检查是否输入了验证码，并且是6位的验证码
        String code = msg.getText().toString().trim();
        if (StringUtils.isEmptyWithTrim(code)) {
            ToastUtils.showInfoToastWithoutIcon(getString(R.string.me_input_code_six));
            return;
        }
        if (code.length() != 6) {
            ToastUtils.showInfoToastWithoutIcon(getString(R.string.me_code_error_input_again));
            return;
        }

        final String pwd = mPwd.getText().toString().trim();
        NetWorkManagerLogin.loginWithVerifyCode(mAccount.getText().toString().trim(), pwd, code,
                new SimpleRequestCallback<String>(null, false) {
                    @Override
                    public void onStart() {
                        super.onStart();
                        // 显示加载框
                        PromptUtils.showLoadDialog(getActivity(), AppBase.getAppContext().getString(R.string.me_logining));
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        if (!isAlive()) return;
                        //移除加载框 (修改：避免 异常 onSaveInstanceState 异常)
                        LoginFragment.this.handler.sendMessage(LoginFragment.this.handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));
                        ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                        try {
                            String errorCode = response.getErrorCode();
                            String errorMsg = response.getErrorMessage();
                            final JSONObject content = new JSONObject(response.getData() == null ? "{}" : response.getData());
                            if ("1".equals(errorCode)) {
                                loginCodeOne(errorMsg, content);
                            } else if ("0".equals(errorCode)) {
                                //登录成功
                                // 计时器处理
                                if (null != countDownTimer) {
                                    countDownTimer.cancel();
                                    countDownTimer = null;
                                }
                                String sign = content.optString("sign");
                                String encryptData = content.optString("User");
                                if (checkResponseSign(encryptData, sign)) {
                                    String data = decryptResponse(encryptData);
                                    if (!TextUtils.isEmpty(data)) {
                                        loginSuccessData(data, pwd);
                                        loginSuccessUI();
                                        iv_animation_img.setVisibility(View.VISIBLE);
                                        iv_animation_img.startAnimation(mLogin_anmi);
                                    }
                                } else {
                                    Toast.makeText(getContext(), R.string.me_login_check_signature_fail, Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                mLoginTipBtn.setText(errorMsg);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        //移除加载框 (修改：避免 异常 onSaveInstanceState 异常)
                        LoginFragment.this.handler.sendMessage(LoginFragment.this.handler.obtainMessage(MSG_WHAT, MSG_REMOVE_DIALOG));
                    }
                });
    }


    @Override
    public void onDestroyView() {
        // 计时器处理
        if (null != countDownTimer) {
            countDownTimer.cancel();
            countDownTimer = null;
        }
        super.onDestroyView();
    }

    @Override
    public void onResume() {
        super.onResume();
        handler.setActivity(getActivity());
        handler.resume();
    }

    @Override
    public void onPause() {
        super.onPause();
        handler.pause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        handler.setActivity(null);
    }

    /**
     * Message Handler class that supports buffering up of messages when the
     * activity is paused i.e. in the background.
     */
    static class ConcreteTestHandler extends PauseHandler {
        /**
         * Activity instance
         */
        protected Activity activity;

        final void setActivity(Activity activity) {
            this.activity = activity;
        }

        @Override
        final protected boolean storeMessage(Message message) {
            return true;
        }


        @Override
        final protected void processMessage(Message msg) {
            final Activity activity = this.activity;
            if (activity != null) {
                switch (msg.what) {
                    case MSG_WHAT:
                        PromptUtils.removeLoadDialog((FragmentActivity) activity);
                        break;
                }
            }
        }
    }

    private class MyCountDownTimer extends CountDownTimer {

        public MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(final long millisUntilFinished) {
//			ToastUtils.showToast("还剩："+ millisUntilFinished / 1000);
            msgBtn.setText(millisUntilFinished / 1000 + msgBtn.getContext().getString(R.string.me_re_send_after));
        }

        @Override
        public void onFinish() {
//			ToastUtils.showToast("计时器结束");
            msgBtn.setEnabled(true);
            msgBtn.setText(getResources().getString(R.string.me_msg_code));
        }
    }

    // 邮件订阅
    private static void subscribeMail() {
        if (TextUtils.isEmpty(PreferenceManager.UserInfo.getEmailAddress())) return;

        NetWorkManager.subMail(null, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map> response = ApiResponse.parse(info.result, Map.class);
                if (!response.isSuccessful()) return;
                String currentDate = DateUtils.getFormatString(System.currentTimeMillis(), DateUtils.DATE_FORMATE_SIMPLE);
                MailPreference.getInstance().put(MailPreference.KV_ENTITY_MAIL_SUBSCRIPT,currentDate);
            }
        }, "1");
    }
}
