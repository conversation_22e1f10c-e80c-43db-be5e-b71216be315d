package com.jd.oa.business.jdsaaslogin.util

import android.content.Context
import android.graphics.drawable.BitmapDrawable
import android.widget.ImageView
import androidx.annotation.DrawableRes
import com.jd.oa.ui.groupicon.AvatarUtil
import com.jd.oa.utils.ImageLoader
import com.jme.login.R

object JdSaasAvatarUtil {
    val defaultColor = "#F63218"

    fun loadGroupAvatar(context: Context,imageView : ImageView,url : String?,text : String?){
        val bitmap = AvatarUtil.getAvatarWithText(context, text, defaultColor)
        val drawable = BitmapDrawable(context.resources,bitmap)
        ImageLoader.load(context, imageView, url,drawable,2)
    }

    fun loadGroupAvatar(context: Context, imageView : ImageView, url : String?, @DrawableRes defaultRes : Int){
        ImageLoader.load(context, imageView, url,defaultRes,2)
    }

    fun loadPersonalAvatar(context: Context,imageView : ImageView,url: String?){
        ImageLoader.load(context, imageView, url, R.drawable.jdsaas_personal_default_avatar,2)
    }
}