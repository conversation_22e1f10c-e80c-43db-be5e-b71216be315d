package com.jd.oa.business.jdsaaslogin.util

import android.app.Activity
import android.content.Context
import android.util.DisplayMetrics
import android.util.Log
import androidx.customview.widget.ViewDragHelper
import androidx.drawerlayout.widget.DrawerLayout

object LoginUtil{

    fun setDrawerLeftEdgeSize(
        activity: Activity,
        drawerLayout: DrawerLayout,
        displayWidthPercentage: Float
    ) {
        try {
            val leftDraggerField = drawerLayout.javaClass.getDeclaredField("mLeftDragger")
            leftDraggerField.isAccessible = true
            val leftDragger = leftDraggerField[drawerLayout] as ViewDragHelper
            val edgeSizeField = leftDragger.javaClass.getDeclaredField("mEdgeSize")
            edgeSizeField.isAccessible = true
            val edgeSize = edgeSizeField.getInt(leftDragger)
            val dm = DisplayMetrics()
            activity.windowManager.defaultDisplay.getMetrics(dm)
            edgeSizeField.setInt(leftDragger, Math.max(edgeSize, (dm.widthPixels * displayWidthPercentage).toInt())
            )
        } catch (e: Exception) {
            Log.e("Exception", e.message.toString())
        }
    }

    fun isTablet(context: Context): Boolean {
        val metrics = context.resources.displayMetrics
        val widthInDp = metrics.widthPixels / metrics.density
        return widthInDp >= 600 // 600dp和资源文件layout-sw600dp对应上
    }
}
