package com.jd.oa.business.jdsaaslogin.util

import android.content.Context
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.AppBase
import com.jd.oa.ui.IconFontView
import com.jme.login.R

object JdSaasPromptUtil {

    fun showErrorToast(message: String?){
        if(TextUtils.isEmpty(message)){
            return
        }
        val context = AppBase.getAppContext()
        showJdSaasToast(context,context.getString(R.string.jdsaas_login_fail),message, R.string.icon_prompt_closecircle)
    }

    fun showJdSaasToast(
        context: Context?,
        title: CharSequence?,
        message: CharSequence?,
        icon: Int
    ) {
        val view = LayoutInflater.from(context).inflate(R.layout.jdsaas_toast_layout, null)
        val iconView = view.findViewById<IconFontView>(R.id.ic_toast_image)
        val titleView = view.findViewById<TextView>(R.id.tv_toast_title)
        val messageView = view.findViewById<TextView>(R.id.tv_toast_message)
        titleView.text = title
        if (icon == 0) {
            iconView.visibility = View.GONE
        } else {
            iconView.setText(icon)
        }
        if (TextUtils.isEmpty(message)) {
            messageView.visibility = View.GONE
        } else {
            messageView.text = message
        }
        val toast = Toast(context)
        toast.view = view
        toast.setGravity(Gravity.CENTER, 0, 0)
        toast.duration = Toast.LENGTH_SHORT
        toast.show()
    }
}