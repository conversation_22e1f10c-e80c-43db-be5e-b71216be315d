package com.jd.oa.business.jdsaaslogin.ui.account

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.elliptical.SuperEllipticalImageView
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.NClick
import com.jd.oa.utils.isVisible
import com.jme.login.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class AccountDrawerItemSection(val context: Context,val list : List<TeamAccountInfo>?,val clickListener : ClickListener?) : Section(
    SectionParameters.builder().itemResourceId(R.layout.jdsaas_account_drawer_item).build()) {

    var isOpenFirst = true

    override fun getContentItemsTotal(): Int {
        return list?.size?:0
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, position: Int) {
        val holder = viewHolder as ItemViewHolder
        val teamAccountInfo = list?.get(position)

        holder.apply {
            tv_account_name.text = teamAccountInfo?.teamFullName
            if(teamAccountInfo?.teamUserInfoList?.size == 1){
                tv_account_subname.setText(teamAccountInfo.teamUserInfoList.first().realName)
            }else{
                tv_account_subname.setText(context.getString(R.string.jdsaas_login_team_sub_name,teamAccountInfo?.teamUserInfoList?.first()?.realName,teamAccountInfo?.teamUserInfoList?.size?.toString()))
            }
            cl_account_info.setOnClickListener {
                if (NClick.isFastDoubleClick()) {
                    return@setOnClickListener
                }
                if((teamAccountInfo?.teamUserInfoList?.size?:0) > 1){
                    teamAccountInfo?.isExpanded = !(teamAccountInfo?.isExpanded?:false)
                }
                clickListener?.onAccountItemClickListener(this@AccountDrawerItemSection,position,teamAccountInfo)
            }
            if(teamAccountInfo?.loginCurrently?:false){//当前登录
                if(isOpenFirst && (teamAccountInfo?.teamUserInfoList?.size?:0) > 1){
                    isOpenFirst = false
                    teamAccountInfo?.isExpanded = true
                }
                tv_account_login_currently.visibility = View.VISIBLE
                val ivLp = iv_account_avatar.layoutParams as ConstraintLayout.LayoutParams
                ivLp.setMargins(0,DisplayUtils.dip2px(20f),0,DisplayUtils.dip2px(20f))
                iv_account_avatar.layoutParams = ivLp

                val viewLp = view_account_selected.layoutParams as LinearLayout.LayoutParams
                viewLp.setMargins(0,DisplayUtils.dip2px(20f),0,0)
                view_account_selected.visibility = View.VISIBLE //只有登录的显示

                rl_account.background = context.getDrawable(R.drawable.jdsaas_login_team_stroke_corner_bg)
                iv_account_avatar_mask_layer.visibility = View.GONE
                tv_account_name.setTextColor(context.getColor(R.color.jdsaas_black_1B1B1B))
                tv_account_subname.setTextColor(context.getColor(R.color.jdsaas_black_6A6A6A))
                tv_account_status.visibility = View.GONE
            }else{
                tv_account_login_currently.visibility = View.GONE
                val layoutParams = iv_account_avatar.layoutParams as ConstraintLayout.LayoutParams
                layoutParams.setMargins(0,DisplayUtils.dip2px(12f),0,DisplayUtils.dip2px(12f))
                iv_account_avatar.layoutParams = layoutParams

//                val viewLp = view_account_selected.layoutParams as LinearLayout.LayoutParams
//                viewLp.setMargins(0,DisplayUtils.dip2px(12f),0,0)
                view_account_selected.visibility = View.GONE
                //当前租户下仅有一个用户且未激活时，未激活态显示在租户上
                if(teamAccountInfo?.teamUserInfoList?.size == 1
                    && teamAccountInfo.teamUserInfoList.first().activeStatus == 0){
                    rl_account.background = context.getDrawable(R.drawable.jdsaas_login_team_stroke_corner_bg_unactivated)
                    iv_account_avatar_mask_layer.visibility = View.VISIBLE
                    tv_account_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
                    tv_account_subname.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
                    tv_account_status.visibility = View.VISIBLE
                    tv_account_status.text = context.getString(R.string.jdsaas_login_unactivated)
                    tv_account_status.background = context.getDrawable(R.drawable.jdsaas_tv_team_status_unactivated)
                    tv_account_status.setTextColor(context.getColor(R.color.jdsaas_color_blue_4A5FE8))
                }else{
                    rl_account.background = context.getDrawable(R.drawable.jdsaas_login_team_stroke_corner_bg)
                    iv_account_avatar_mask_layer.visibility = View.GONE
                    tv_account_name.setTextColor(context.getColor(R.color.jdsaas_black_1B1B1B))
                    tv_account_subname.setTextColor(context.getColor(R.color.jdsaas_black_6A6A6A))
                    tv_account_status.visibility = View.GONE
                }
            }
            JdSaasAvatarUtil.loadGroupAvatar(context,iv_account_avatar,teamAccountInfo?.avatar, teamAccountInfo?.teamFullName)

            if(teamAccountInfo?.isExpanded?:false){
                val accountDrawerAdapter = UserDrawerAdapter(context,teamAccountInfo?.teamUserInfoList)
                val manager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                rv_sub_account.setLayoutManager(manager)
                rv_sub_account.adapter = accountDrawerAdapter
                accountDrawerAdapter.setOnItemClickListener(object : RecyclerViewItemOnClickListener<TeamUserInfo>{
                    override fun onItemClick(view: View?, position: Int, item: TeamUserInfo?) {
                        if (NClick.isFastDoubleClick()) {
                            return
                        }
                        clickListener?.onUserItemClickListener(this@AccountDrawerItemSection,position,teamAccountInfo,item)
                    }

                    override fun onItemLongClick(view: View?, position: Int, item: TeamUserInfo?) {
                    }
                })
                rv_sub_account.visibility = View.VISIBLE
                tv_account_subname.visibility = View.GONE
                tv_account_name.maxLines = 2
            }else{
                rv_sub_account.visibility = View.GONE
                tv_account_subname.visibility = View.VISIBLE
                tv_account_name.maxLines = 1
            }
        }
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val ll_root = view.findViewById<LinearLayout>(R.id.ll_root)
        val rl_account = view.findViewById<RelativeLayout>(R.id.rl_account)
        val cl_account_info = view.findViewById<ConstraintLayout>(R.id.cl_account_info)
        val tv_account_name = view.findViewById<TextView>(R.id.tv_account_name)
        val tv_account_subname = view.findViewById<TextView>(R.id.tv_account_subname)
        val view_account_selected = view.findViewById<View>(R.id.view_account_selected)
        val tv_account_login_currently = view.findViewById<TextView>(R.id.tv_account_login_currently)
        val iv_account_avatar = view.findViewById<SuperEllipticalImageView>(R.id.iv_account_avatar)
        val iv_account_avatar_mask_layer = view.findViewById<SuperEllipticalImageView>(R.id.iv_account_avatar_mask_layer)
        val tv_account_status = view.findViewById<TextView>(R.id.tv_account_status)
        val rv_sub_account = view.findViewById<RecyclerView>(R.id.rv_sub_account)
    }

    interface ClickListener{
        fun onAccountItemClickListener(section: AccountDrawerItemSection, position: Int, teamAccountInfo: TeamAccountInfo?)
        fun onUserItemClickListener(section: AccountDrawerItemSection,position: Int,teamAccountInfo: TeamAccountInfo?,teamUserInfo: TeamUserInfo?)
    }
}