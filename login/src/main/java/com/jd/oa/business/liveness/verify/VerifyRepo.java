package com.jd.oa.business.liveness.verify;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/12/19.
 */
public class VerifyRepo implements VerifyContract.Repo {
    @Override
    public void getUserMobile(final LoadDataCallback<Map> callback) {

        SimpleRequestCallback<String> callback1 = new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map> response = ApiResponse.parse(info.result, Map.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                }
            }

            @Override
            public void onFailure(HttpException exception, String errorMsg) {
                super.onFailure(exception, errorMsg);
                callback.onDataNotAvailable(errorMsg, -1);
            }
        };

//        NetWorkManagerLogin.getMySelfInfo(null,  PreferenceManager.UserInfo.getUserName(), callback1);
        NetWorkManagerLogin.getPhone(callback1);
    }

    @Override
    public void getVeriCode(final LoadDataCallback<Object> callback, String code, String mobileNumber) {
        if (code != null) {
            code = code.replace("+", "");
        }
        NetWorkManagerLogin.getVeriCode(mobileNumber, "10002", code, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            protected void onSuccess(Object obj, List<Object> tArray, String rawData) {
                super.onSuccess(obj, tArray, rawData);
                callback.onDataLoaded(obj);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }));
    }

    @Override
    public void checkVerifyCode(final LoadDataCallback<Object> callback, String verifyCode) {

        NetWorkManagerLogin.checkVerifyCode(verifyCode, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            protected void onSuccess(Object obj, List<Object> tArray, String rawData) {
                super.onSuccess(obj, tArray, rawData);
                callback.onDataLoaded(obj);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }));
    }

    @Override
    public void onDestroy() {

    }
}
