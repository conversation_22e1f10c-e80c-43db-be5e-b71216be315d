package com.jd.oa.business.liveness.verify;

import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.StringUtils;
import com.jme.login.R;

import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/12/20.
 */
public class VerifyPresenter extends AbsMVPPresenter<VerifyContract.View> implements VerifyContract.Presenter {

    private VerifyContract.Repo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public VerifyPresenter(VerifyContract.View view) {
        super(view);
        mRepo = new VerifyRepo();
    }

    @Override
    public void onCreate() {
        getUserMobile(false);
    }

    @Override
    public void getUserMobile(final boolean isForce) {
        view.showLoading("");
        mRepo.getUserMobile(new LoadDataCallback<Map>() {
            @Override
            public void onDataLoaded(Map data) {
                if (isAlive()) {
                    try {
                        view.showContent();
                        view.showMobile((String) data.get("code"),(String) data.get("mobile"));
                        if (!isForce) {
                            view.enableAllWidget(true);
                        }
                    } catch (Exception e) {
                        onDataNotAvailable(view.getContext().getString(R.string.me_data_parse_error), 0);
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showContent();
                    if (!isForce) {
                        showDefaultError();
                        view.enableAllWidget(false);
                    }
                }
            }
        });
    }

    @Override
    public void getVerifyCode(String code, String mobileNumber) {
        if (StringUtils.isNotEmptyWithTrim(mobileNumber)) {
            view.showLoading("");

            mRepo.getVeriCode(new LoadDataCallback<Object>() {
                @Override
                public void onDataLoaded(Object data) {
                    if (isAlive()) {
                        view.showContent();
                        view.startTimeDown();
                    }
                }

                @Override
                public void onDataNotAvailable(String msg, int code) {
                    if (isAlive()) {
                        view.showContent();
                        showDefaultError();
                    }
                }
            }, code, mobileNumber);
        }
    }

    @Override
    public void checkVerifyCode(String verifyCode) {
        if (StringUtils.isNotEmptyWithTrim(verifyCode)) {
            view.showLoading("");
            mRepo.checkVerifyCode(new LoadDataCallback<Object>() {
                @Override
                public void onDataLoaded(Object data) {
                    if (isAlive()) {
                        view.showContent();
                        view.showCaptureView();
                    }
                }

                @Override
                public void onDataNotAvailable(String msg, int code) {
                    if (isAlive()) {
                        view.showContent();
                        view.showError(msg);
                    }
                }
            }, verifyCode);
        } else {
            view.showError(view.getContext().getString(R.string.me_code_error_retry));
        }
    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
        view = null;
    }
}
