package com.jd.oa.business.jdsaaslogin.data.model

/*京ME SaaS的用户数据映射到JdSaasUserInfo，统一调用京ME登录成功后的方法*/
data class JdSaasUserInfo(
    val accessToken : String?,
    val teamId : String?,
    val appId : String?,
    val userId : String?,
    val userName : String?,
    val realName : String?,
    val userIcon :String?,
    val mobile : String?,
    val tenantCode : String?,
    val teamName : String?,
    val wjLoginPin : String?,


//    val jdAccount : String?,
//    val isAttendance : String?,
//    val sex : String?,
//    val homeDeeplink : String?,
//    val randomKey : String?,
//    val tenantCodeList : String?,
//    val tenantConfig : String?,
//    val isSnap : String?,
//    val birthday : String?,
//    val levelDesc : String?,
//    val email : String?,
//    val domainUserName : String?,
//    val telePhone : String?,
//    val userCode : String?,
//    val organizationName : String?,
//    val hasSnapPermission : String?,
//    val loginToken : String?,
//    val nonce : String?,
//    val agreements : String?
)