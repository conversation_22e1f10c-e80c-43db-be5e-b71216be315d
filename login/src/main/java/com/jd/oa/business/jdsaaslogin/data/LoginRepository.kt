package com.jd.oa.business

import android.util.Base64
import com.jd.oa.network.NetWorkManagerLogin
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.utils.encrypt.MD5Utils

open class LoginRepository {
    companion object{
        const val API_GET_ACCOUNT_LIST = "jdme.login.getAccountList" //获取账号列表
        const val API_LOGIN_BY_TOKEN = "jdme.login.loginByToken" //上报登录态
        const val API_LOGOUT_BY_TOKEN = "jdme.login.logoutByToken"//退出登录
        const val API_TEST_TOKEN_VERIFY = "jdme.login.testTokenVerify" //token校验
    }

    fun exchangePublicKey(userName : String, publicKey : String, callback : SimpleRequestCallback<String>){
        val params = mapOf("userName" to userName,"publicKey" to publicKey)
        HttpManager.post(null, params, callback, NetWorkManagerLogin.API2_LOGIN_EXCHANGE_PUBLIC_KEY)
    }

    fun login(userName: String,password : String,callback : SimpleRequestCallback<String>){
        val params = mapOf("userName" to userName,"password" to password)
        HttpManager.post(null, params, callback, NetWorkManagerLogin.API2_UNION_LOGIN)
    }

    fun loginWithVerifyCode(userName : String,password : String,msgCode : String, callback : SimpleRequestCallback<String>){
        val params = mapOf("userName" to userName,"password" to password,"verificationCode" to msgCode)
        HttpManager.post(null, params, callback, NetWorkManagerLogin.API2_UNION_LOGIN)
    }

    fun getAccountList(jsonString: String,headers : Map<String,String>,callback: SimpleRequestCallback<String>){
//        val bizParam = "W3siaWQiOjAsImxvZ2luTmFtZSI6ImFpbWxkaXIiLCJzdGF0dXMiOjIsInVzZXJMZXZlbCI6OTB9LHsiaWQiOjEsImxvZ2luTmFtZSI6IuadjuawkTMyNjkwNSIsInN0YXR1cyI6MSwidXNlckxldmVsIjo2MX1d"
        val bizParam = Base64.encodeToString(jsonString.toByteArray(),Base64.NO_WRAP)
        val timestamp = System.currentTimeMillis()
        val salt = "2a1937936348dcf01eda0bb50372e156"
        val signString = "$timestamp$salt$bizParam"
        val sign = MD5Utils.getMD5(signString)
        val params = mapOf<String,Any>("bizParam" to bizParam,"timestamp" to timestamp,"sign" to sign)
        HttpManager.color().post(params,headers,API_GET_ACCOUNT_LIST,callback)
    }

    //1：是否继续登录；0：直接登录
    fun loginByToken(forceLogin : Int,employeeId : String,headers : Map<String,String>,callback: SimpleRequestCallback<String>){
        val params = mapOf<String,Any>("forceLogin" to forceLogin,"employeeId" to employeeId)
        HttpManager.color().post(params,headers, API_LOGIN_BY_TOKEN,callback)
    }

    /*pinList-需要退出登录的userId 数组 */
    fun logoutByToken(userPinList : List<Map<String,String>>,callback: SimpleRequestCallback<String>){
        val params = mapOf<String,Any>("userPinList" to userPinList)
        HttpManager.color().post(params,null, API_LOGOUT_BY_TOKEN,callback)
    }

    fun testTokenVerify(callback: SimpleRequestCallback<String>){
        HttpManager.color().post(null,null, API_TEST_TOKEN_VERIFY,callback)
    }
}