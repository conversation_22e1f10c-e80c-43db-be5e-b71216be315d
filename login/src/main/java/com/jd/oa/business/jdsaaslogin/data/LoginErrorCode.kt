package com.jd.oa.business.jdsaaslogin.data

object LoginErrorCode {
    const val OTHER_DEVICE_LOGIN = "********" //已在其他设备登录

    const val ERROR = "500" //错误，请稍后再试

    const val ERROR_ME = "501" //调用ME服务失败

    const val TOKEN_MISSING = "5001" //token不存在

    const val TOKEN_DISABLED = "5002" //token已失效

    const val PIN_LIST_NULL = "100001" //账号暂未开通本平台服务，getAccountList接口使用

    const val PIN_NULL = "100002" //账号暂未开通本平台服务，如有疑问，请联系************* ，loginByToken接口使用

    const val TEAM_USER_OUT = "300001" //您已被当前团队踢出 ，loginByToken接口使用

    const val TEAM_STOP = "300002" //租户已停用，loginByToken接口使用

    const val ME_TEAM_NULL = "400000" //账号暂未加入团队,getAccountList接口使用

    const val ME_EMPL_NULL = "400001" //租户异常,getAccountList接口使用
}