package com.jd.oa.business.jdsaaslogin.data
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.SimpleRequestCallback

class LoginScanRepository {

    companion object{
        const val API_MOBILE_SCAN_RECORD = "jdme.login.mobileScanRecord" //移动端扫码之后上报租户信息
    }

    fun mobileScanRecord(callback : SimpleRequestCallback<String>){
        HttpManager.color().post(null, null, API_MOBILE_SCAN_RECORD,callback)
    }
}