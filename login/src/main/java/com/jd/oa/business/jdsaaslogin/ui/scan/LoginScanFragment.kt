package com.jd.oa.business.jdsaaslogin.ui.scan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.InjectorUtil
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.ActionBarHelper
import com.jme.login.R
import kotlinx.android.synthetic.main.fragment_login_scan.view.iv_back
import kotlinx.android.synthetic.main.fragment_login_scan.view.tv_login
import kotlinx.android.synthetic.main.fragment_login_scan.view.tv_login_cancel
import kotlinx.android.synthetic.main.fragment_login_scan.view.tv_login_scan_tip

class LoginScanFragment : BaseFragment() {
    val viewModel by lazy { ViewModelProvider(requireActivity(), InjectorUtil.getLoginScanModelFactory()).get(
        LoginScanViewModel::class.java) }

    lateinit var mView : View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.getQRCodeKeyFromUrl()

        viewModel.mQRCodeLoginSuccessLiveData.observe(requireActivity()){
            mView.tv_login_scan_tip.setText(getString(R.string.jdsaas_login_scan_successful_authorization))
            mView.tv_login.visibility = View.GONE
            mView.tv_login_cancel.visibility = View.GONE
            mView.postDelayed({ requireActivity().finish() },1000)
        }
        viewModel.mCancelQRCodeLoginLiveData.observe(requireActivity()){
            mView.tv_login_scan_tip.setText(getString(R.string.jdsaas_login_scan_authorization_cancelled))
            mView.tv_login.visibility = View.GONE
            mView.tv_login_cancel.visibility = View.GONE
            mView.postDelayed({ requireActivity().finish() },1000)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.fragment_login_scan,container,false)
        ActionBarHelper.hide(this)
        mView.iv_back.setOnClickListener {
            requireActivity().finish()
        }
        val teamName = PreferenceManager.UserInfo.getTeamName()
        mView.tv_login.setText(getString(R.string.jdsaas_login_scan_confirm,teamName))
        mView.tv_login.setOnClickListener {
            viewModel.confirmQRCodeLogined()
        }
        mView.tv_login_cancel.setOnClickListener {
            viewModel.cancelQRCodeLogined()
        }
        return mView
    }
}