package com.jd.oa.business.login.controller;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.flutter.common.MeFlutterBoostContainerFragment;
import com.jd.jrapp.library.sgm.annotation.StartupDone;
import com.jd.jrapp.library.sgm.launch.ApmLaunchTime;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.liveness.LiveLoginHelper;
import com.jd.oa.business.login.controller.handle.MeFlutterLoginHandler;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StatusBarUtil;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.WebViewUtils;
import com.jdshare.jdf_container_plugin.components.channel.api.JDFChannelHelper;
import com.jme.login.R;

import io.flutter.embedding.android.FlutterFragment;

import static com.jd.flutter.common.JDFHelper.callFlutter;
import static com.jd.oa.business.login.controller.handle.MeFlutterLoginHandler.FLUTTER_BUSINESS_LOGIN;
import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.router.DeepLink.ACCOUNT;

/**
 * 登陆Activity
 *
 * <AUTHOR>
 */
@StartupDone
@Route({ACCOUNT})
public class LoginActivity extends BaseActivity implements OperatingListener, CommunicationListener {
//    public static final String FINISH_LOGIN_ACTIVITY = "finishLoginActivity";
    public static final String EXTRA_MESSAGE_TIP = "message";
    public static final String EXTRA_ROUTER = "route_activity";
    private static final String LOGIN_MAIN = "loginMain";

    private String route_activity;
    LoginMethod loginMethod = LoginMethod.LOGIN_PASSWD;
    private LiveLoginHelper liveLoginHelper;
    private Animation mLogin_anim;
    private ImageView iv_animation_img;


    enum LoginMethod {
        LOGIN_PASSWD, LOGIN_SNAPME
    }

    public static final String PIN_TAG = "JdPinUtils";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//            getWindow().setStatusBarColor(getColor(R.color.transparent));
//        }
//        setStatusBar(getStatusBarColor());

        route_activity = getIntent().getStringExtra(LoginActivity.EXTRA_ROUTER);


        MELogUtil.localI(PIN_TAG, "LoginActivity---onCreate");
        MELogUtil.onlineI(PIN_TAG, "LoginActivity---onCreate");
        if(AppBase.iAppBase != null){
            AppBase.iAppBase.setKickOut(false);
        }
//        FlutterMain.startInitialization(getApplicationContext());
//        flutterLogin = "1";

//        if (isSplit()) {
//            if ("1".equals(flutterLogin)) {
//                JDFHelper.getInstance().addCachedEngine(getApplication(), LOGIN_ENGINE, LOGIN_MAIN);
//            }
//        }
        super.onCreate(savedInstanceState);
        // 需要初始化为false，否则登录后，又进入登录界面
        MyPlatform.sHasLock = false;
        MyPlatform.sMainActivityUnlocked = false;
        liveLoginHelper = new LiveLoginHelper();
        // 没有公钥生产一个
        if (!KeyManager.getInstance().hasPublicKey()) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    KeyManager.getInstance().getPublicKey();
                }
            }).start();
        }


//        setVerify(false);
//        ActionBarHelper.init(this);
        setContentView(R.layout.jdme_login_flutter_container);
//        if (getSupportActionBar() != null) {
//            getSupportActionBar().hide();
//        }
        getWindow().setBackgroundDrawable(null);
//        MyPlatform.addActivity(this);
        StatusBarUtil.setTranslucentForImageViewInFragment(this, 0, null);

        iv_animation_img = findViewById(R.id.iv_animation_img);
        final View flutter = findViewById(R.id.flutter_container);
        mLogin_anim = AnimationUtils.loadAnimation(AppBase.getAppContext(), com.jd.flutter.R.anim.jdme_anim_login);
        mLogin_anim.setFillAfter(true);
        mLogin_anim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                flutter.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                // 通知login事件
                FragmentUtils.updateUI(OperatingListener.OPERATE_LOGIN, null);
            }
        });

        if ("1".equals(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD))) {
            loginMethod = LoginMethod.LOGIN_SNAPME;
        }

        // 发给 UI 时，去掉下面的代码 toIndex();
        if ((PreferenceManager.UserInfo.getLogin() && null != MyPlatform.sUser)
                && (MyPlatform.sUser.getAttendance() != null
                && !MyPlatform.sUser.getAttendance().isEmpty())) {
            toIndex();
        } else {
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    toLoginFragment();
                }
            }, 0);
        }
        if (!(TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet()))) {
            callFlutter(FLUTTER_BUSINESS_LOGIN, "updateSp", null, null);
        }
//        String message = getIntent().getStringExtra(LoginActivity.EXTRA_MESSAGE_TIP);
//        if (!TextUtils.isEmpty(message)) {
        if (!TextUtils.isEmpty(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG))) {

            PromptUtils.showAlertDialog(this, -1, JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG, "");
                    if (!TextUtils.isEmpty(getIntent().getStringExtra("leaveUrl"))) {
                        // 跳转离职H5界面
                        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Function).getIntent(AppBase.getAppContext());
                        WebBean bean = new WebBean(getIntent().getStringExtra("leaveUrl"), WebConfig.H5_NATIVE_HEAD_SHOW);
                        intent.putExtra(EXTRA_WEB_BEAN, bean);
                        intent.putExtra(AppBase.FLAG_FUNCTION, WebViewUtils.getName());
                        startActivity(intent);
                        //埋点
//                        PageEventUtil.onEvent(LoginActivity.this, PageEventUtil.EVENT_DISMISSION);
                        JDMAUtils.onEventClick(JDMAConstants.mobile_resignationData_click, JDMAConstants.mobile_resignationData_click);

                    }
                    dialog.dismiss();
                }
            }, false);
        }
//        EventBusMgr.getInstance().register(this);
    }

    @Override
    protected void onStart() {
        try {
            ApmLaunchTime.recordStartTime("Main", "onWindowFocusChanged");
            super.onStart();
            ApmLaunchTime.recordEndTime("Main", "onWindowFocusChanged");
        } catch (Exception e) {
            MELogUtil.localE(TAG, "onStart ", e);
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        MELogUtil.localI(TAG, "onWindowFocusChanged " + hasFocus);
    }

    /**
     * 跳转到登录Fragment
     */
    private void toLoginFragment() {
        FlutterFragment fragment;
//            if (isSplit()) {
//                JDFHelper.getInstance().addCachedEngine(getApplication(), LOGIN_ENGINE, LOGIN_MAIN);
//                FlutterFragment.CachedEngineFragmentBuilder cachedEngineFragmentBuilder = new FlutterFragment.CachedEngineFragmentBuilder(MeFlutterContainerFragment.class, LOGIN_ENGINE);
//                fragment = cachedEngineFragmentBuilder.build();
//                TabCommonHandler.addHandler(MeFlutterLoginHandler.FLUTTER_BUSINESS_LOGIN, new MeFlutterLoginHandler());
//            } else {
        JDFChannelHelper.registerMethodChannel(new MeFlutterLoginHandler());
        fragment = new MeFlutterBoostContainerFragment
                .CachedEngineFragmentBuilder(MeFlutterBoostContainerFragment.class)
                .url("login")  // flutter侧注册的路由名称
//                    .urlParams(params)
                .build();
//            }
        try {
            getSupportFragmentManager().beginTransaction().replace(R.id.flutter_container, fragment).commitAllowingStateLoss();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void toIndex() {
//        EventBusMgr.getInstance().post(FINISH_LOGIN_ACTIVITY);
        if (getIntent() != null && getIntent().getBooleanExtra(TabletLoginActivity.FROM_TABLET, false)) {
            setResult(TabletLoginActivity.RESULT_TO_INDEX);
            finish();
            return;
        }
        if (!TextUtils.isEmpty(route_activity)) {
            Router.build(route_activity).go(AppBase.getAppContext());
        } else {
            // 主页
            Router.build(getDefaultTabDeepLink()).go(AppBase.getAppContext());
        }
        finish();
    }

    public static String getDefaultTabDeepLink() {
        return DeepLink.TAB;
//        String defaultTabDeepLink = PreferenceManager.UserInfo.getDefaultTab();
//        if (Utils2String.isNotEmptyWithTrim(defaultTabDeepLink)) {
//            return defaultTabDeepLink;
//        } else {
//            return DeepLink.MESSAGE_OLD;
//        }
    }

    /**
     * 登录通信处理
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OperatingListener.OPERATE_LOGIN == optionFlag) {
            // 跳转至首页
            toIndex();
        }
        return false;
    }

    @Override
    public void doCommunication() {
        if (loginMethod == LoginMethod.LOGIN_PASSWD) {
            loginMethod = LoginMethod.LOGIN_SNAPME;
        } else {
            loginMethod = LoginMethod.LOGIN_PASSWD;
        }
        toLoginFragment();
    }

    public void startAnim() {
        if (iv_animation_img == null) {
            return;
        }
        iv_animation_img.setVisibility(View.VISIBLE);
        iv_animation_img.startAnimation(mLogin_anim);
    }

    @Override
    public void onResume() {
        super.onResume();
        liveLoginHelper.onResume();
        StartRunTimeMonitor.getInstance().end("Apps AppStartup");
        StartRunTimeMonitor.getInstance().uploadLog();
    }

    @Override
    public void onPause() {
        super.onPause();
        liveLoginHelper.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        liveLoginHelper.onDestroy();
//        EventBusMgr.getInstance().unregister(this);//反注册EventBus
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        liveLoginHelper.onActivityResult(requestCode, data);
    }

//    @SuppressWarnings("unused")
//    public void onEventMainThread(final String eventStr) {
//        if (!eventStr.equals(FINISH_LOGIN_ACTIVITY)) {
//            return;
//        }
//        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                JDFHelper.getInstance().resetLoginPath();
//            }
//        }, 1000);
//    }
}