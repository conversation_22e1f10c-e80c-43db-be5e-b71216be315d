package com.jd.oa.business.liveness;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.liveness.verify.VerifyContract;
import com.jd.oa.business.liveness.verify.VerifyPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jme.login.R;

/**
 * e
 * 身份验证页面
 * Created by zhaoyu1 on 2016/12/8.
 */
@Navigation(displayHome = true, hidden = false)
public class UserVerifyFragment extends BaseFragment implements VerifyContract.View {

    private static final int CODE_LENGTH = 6;
    public static final String CN_JD_GROUP = "CN.JD.GROUP";
    private static final String CODE_86 = "+86";
    private static final String CODE_66 = "+66";
    private final int RESULT_CODE = 11;

    private TextView me_change_phone, tv_code;
    private EditText edit_phone;
    private EditText me_et_code;
    private TextView me_tv_get_code;
    //    private TextView me_tv_next;
    private FrameView me_frameView;
    private String code, phone;

    private VerifyContract.Presenter mPresenter;

    private MyCountDownTimer countDownTimer;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_user_verify, container, false);
        ActionBarHelper.init(this);
        ActionBarHelper.changeActionBarTitle(this, getString(R.string.me_verify_user));
        me_change_phone = view.findViewById(R.id.me_change_phone);
        edit_phone = view.findViewById(R.id.edit_phone);
        tv_code = view.findViewById(R.id.tv_phone_code);
        String rentCode = PreferenceManager.UserInfo.getTenantCode();
        if (rentCode.equals(CN_JD_GROUP)) {
            tv_code.setText(CODE_86);
        } else {
            tv_code.setText(CODE_66);
        }
        me_et_code = view.findViewById(R.id.me_et_code);
        me_tv_get_code = view.findViewById(R.id.me_tv_get_code);
//        me_tv_next = view.findViewById(R.id.me_tv_next);
        me_frameView = view.findViewById(R.id.me_frameView);

        if (mPresenter == null) {
            mPresenter = new VerifyPresenter(this);
            mPresenter.onCreate();
        }

        setWidgetStatus(false);

        me_change_phone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goUpdatePhone();
            }
        });
        me_tv_get_code.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.getVerifyCode(code, phone);
                me_tv_get_code.setEnabled(false);
            }
        });
        me_et_code.setFilters(new InputFilter[]{new InputFilter.LengthFilter(CODE_LENGTH)});
        me_et_code.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                String code = me_et_code.getText().toString().trim();
                if (code.length() == CODE_LENGTH) {
                    Activity activity = getActivity();
                    if (activity != null && isResumed()) {
                        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
                        boolean isOpen = imm.isActive();
                        if (isOpen) {
                            imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
                        }
                    }
                    mPresenter.checkVerifyCode(code);
                }
            }
        });
        return view;
    }

    private void setWidgetStatus(boolean isEnable) {
        me_tv_get_code.setEnabled(isEnable);
//        me_tv_next.setEnabled(isEnable);
    }

    private void goUpdatePhone() {
        Intent intent = new Intent(getActivity(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ChangePhoneFragment.class.getName());
        startActivityForResult(intent, RESULT_CODE);
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == RESULT_CODE && null != mPresenter) {
            // 重新获取手机号
            mPresenter.getUserMobile(true);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPresenter) {
            mPresenter.onDestroy();
            mPresenter = null;
        }
        clearTimeDown();
    }

    private void clearTimeDown() {
        if (null != countDownTimer) {
            countDownTimer.cancel();
            countDownTimer = null;
        }
    }

    @Override
    public void showContent() {
        me_frameView.setContainerShown(false);
    }

    @Override
    public void enableAllWidget(boolean isEnable) {
        setWidgetStatus(isEnable);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void showMobile(String code, String mobile) {
        if (code != null && tv_code != null) {
            tv_code.setText(code);
        }
        this.code = code;
        phone = mobile;
        if (mobile != null) {
            try {
                edit_phone.setText("" + mobile.subSequence(0, 3) + "****" + mobile.substring(7));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void startTimeDown() {
        clearTimeDown();
        countDownTimer = new MyCountDownTimer(60 * 1000, 1000);
        countDownTimer.start();
    }

    @Override
    public void showCaptureView() {
        if (getActivity() == null) {
            return;
        }
        clearTimeDown();
        Intent intent = new Intent();
        intent.putExtra("isOK", true);
        intent.putExtra(LivenessSetFragment.SOURCE_SWITCH_KEY, getArguments().getString(LivenessSetFragment.SOURCE_SWITCH_KEY));
        getActivity().setResult(Activity.RESULT_OK, intent);
        getActivity().finish();
    }

    @Override
    public void showLoading(String msg) {
        me_frameView.setLoadInfo(R.string.me_empty);
        me_frameView.setContainerProgressShown(false);
    }

    @Override
    public void showError(String msg) {
        Toast toast = new Toast(getActivity());
        toast.setGravity(Gravity.CENTER, 0, 0);
        View view = getActivity().getLayoutInflater().inflate(R.layout.jdme_toast_setting_clear_cache, null);
        TextView textView = (TextView) view.findViewById(R.id.jdme_id_custom_toast_text);
        Drawable left = getResources().getDrawable(R.drawable.jdme_icoon_fragment_welfare_protocol);
        left.setBounds(0, 0, left.getIntrinsicWidth(), left.getIntrinsicHeight());
        textView.setText(msg);
        textView.setCompoundDrawables(left, null, null, null);
        toast.setView(view);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.show();
    }

    private class MyCountDownTimer extends CountDownTimer {
        public MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(final long millisUntilFinished) {
            me_tv_get_code.setText(millisUntilFinished / 1000 + me_tv_get_code.getContext().getString(R.string.me_re_send_after));
        }

        @Override
        public void onFinish() {
            me_tv_get_code.setEnabled(true);
            me_tv_get_code.setText(getResources().getString(R.string.me_msg_code));
        }
    }
}
