package com.jd.oa.business.liveness.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by liyao8 on 2017/12/11.
 */

public class AllStatusBean implements Parcelable {
    private String isAgreeLog;//刷脸登录开关 1开 0关
    private String isAgreeAttend;//刷脸打卡开关

    public String getIsAgreeLog() {
        return isAgreeLog;
    }

    public void setIsAgreeLog(String isAgreeLog) {
        this.isAgreeLog = isAgreeLog;
    }

    public String getIsAgreeAttend() {
        return isAgreeAttend;
    }

    public void setIsAgreeAttend(String isAgreeAttend) {
        this.isAgreeAttend = isAgreeAttend;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.isAgreeLog);
        dest.writeString(this.isAgreeAttend);
    }

    public AllStatusBean() {
    }

    protected AllStatusBean(Parcel in) {
        this.isAgreeLog = in.readString();
        this.isAgreeAttend = in.readString();
    }

    public static final Parcelable.Creator<AllStatusBean> CREATOR = new Parcelable.Creator<AllStatusBean>() {
        @Override
        public AllStatusBean createFromParcel(Parcel source) {
            return new AllStatusBean(source);
        }

        @Override
        public AllStatusBean[] newArray(int size) {
            return new AllStatusBean[size];
        }
    };
}
