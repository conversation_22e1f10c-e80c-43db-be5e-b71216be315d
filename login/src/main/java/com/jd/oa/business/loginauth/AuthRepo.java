package com.jd.oa.business.loginauth;

import android.content.Context;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.exception.NetException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleOnSubscribe;

/**
 * Created by peidongbiao on 2018/4/25.
 */

public class AuthRepo {
    private static AuthRepo sInstance;
    private Context mContext;

    public static AuthRepo get(Context context) {
        if (sInstance == null) {
            synchronized (AuthRepo.class) {
                if (sInstance == null) {
                    sInstance = new AuthRepo(context);
                }
            }
        }
        return sInstance;
    }

    private AuthRepo(Context context) {
        this.mContext = context.getApplicationContext();
    }

    public Single<AuthInfo> getAuthInfo(String businessId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("businessId", businessId);
        return Single.create(new SingleOnSubscribe<AuthInfo>() {
            @Override
            public void subscribe(final SingleEmitter<AuthInfo> emitter) throws Exception {
                NetWorkManager.request(null, NetworkConstant.API_LOGIN_AUTH_INFO, new SimpleReqCallbackAdapter<>(new AbsReqCallback<AuthInfo>(AuthInfo.class) {
                    @Override
                    protected void onSuccess(AuthInfo info, List<AuthInfo> tArray, String rawData) {
                        super.onSuccess(info, tArray, rawData);
                        emitter.onSuccess(info);
                    }

                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        emitter.onError(new NetException(errorMsg));
                    }
                }), params);
            }
        });
    }

    public Single<String> auth(String businessId, boolean auth) {
        final Map<String, Object> params = new HashMap<>();
        params.put("businessId", businessId);
        params.put("authResult", auth ? "1" : "0");
        return Single.create(new SingleOnSubscribe<String>() {
            @Override
            public void subscribe(final SingleEmitter<String> emitter) throws Exception {
                NetWorkManager.request(null, NetworkConstant.API_LOGIN_AUTH, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
                    @Override
                    protected void onSuccess(Map result, List<Map> tArray, String rawData) {
                        super.onSuccess(result, tArray, rawData);
                        String msg = (String) result.get("msg");
                        emitter.onSuccess(msg);
                    }

                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        emitter.onError(new NetException(errorMsg));
                    }
                }), params);
            }
        });
    }
}