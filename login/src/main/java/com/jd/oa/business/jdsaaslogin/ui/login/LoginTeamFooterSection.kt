package com.jd.oa.business.jdsaaslogin.ui.login

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.jme.login.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class LoginTeamFooterSection(val clickListener: ClickListener) : Section(
    SectionParameters.builder().itemResourceId(R.layout.jdsaas_account_drawer_footer).build()) {
    override fun getContentItemsTotal(): Int {
        return 1
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }


    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, i: Int) {
        viewHolder?.itemView?.setOnClickListener {
            clickListener?.onLoadMoreClicked()
        }
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {

    }

    interface ClickListener{
        fun onLoadMoreClicked()
    }


}