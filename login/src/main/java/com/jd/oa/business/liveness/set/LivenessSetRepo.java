package com.jd.oa.business.liveness.set;

import com.jd.oa.MyPlatform;
import com.jd.oa.business.liveness.model.AllLimitBean;
import com.jd.oa.business.liveness.model.AllStatusBean;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2016/12/19
 */
public class LivenessSetRepo implements LivenessSetContract.Repo {

    @Override
    /**
     * 获取所有的刷脸协议状态
     */
    public void getAllAgreementStatus(final LoadDataCallback<AllStatusBean> callback) {

        NetWorkManagerLogin.getAllAgreementStatus(new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);

                ApiResponse<HashMap<String, String>> response = ApiResponse.parse(info.result, Map.class);

                if (response.isSuccessful()) {
                    HashMap<String, String> map = response.getData();

                    AllStatusBean statusBean = new AllStatusBean();
                    if (map.containsKey("isAgreeAttend")) {
                        statusBean.setIsAgreeAttend(map.get("isAgreeAttend"));
                    } else {
                        statusBean.setIsAgreeAttend(map.get("5"));
                    }

                    if (map.containsKey("isAgreeLog")) {
                        statusBean.setIsAgreeLog(map.get("isAgreeLog"));
                    } else {
                        statusBean.setIsAgreeLog(map.get("2"));
                    }

                    callback.onDataLoaded(statusBean);
//                    PreferenceManager.UserInfo.setLivenessClockIn(Integer.parseInt(statusBean.getIsAgreeAttend()));
                }
            }

            @Override
            public void onFailure(HttpException e, String errorMsg) {
                super.onFailure(e, errorMsg);
                callback.onDataNotAvailable(errorMsg, -1);
            }
        });
    }

    /**
     * @param callback
     * @param flag     是否同意（1：同意；0：不同意）
     */
    @Override
    public void signLoginAgreement(final LoadDataCallback<String> callback, final String flag) {

        NetWorkManagerLogin.signAgreement("2", flag, "", new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            protected void onSuccess(Object str, List<Object> tArray, String rawData) {
                super.onSuccess(str, tArray, rawData);
                callback.onDataLoaded(rawData);
                //修改刷脸打卡的开关状态后，存储本地
//                PreferenceManager.UserInfo.setLivenessClockIn(Integer.parseInt(flag));
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }));
    }

    @Override
    public void getUploadLiveness(final LoadDataCallback<Map> callback) {
        NetWorkManagerLogin.snapIsUpload(null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray) {
                super.onSuccess(map, tArray);
                callback.onDataLoaded(map);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }));
    }

//    @Override
//    public void getClockStatus(final LoadDataCallback<Map> callback) {
//        Map<String, String> params = new HashMap<>();
//        params.put("agreementType", "5");
//        NetWorkManager.request(this, NetworkConstant.API_JD_STATUS, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
//            @Override
//            protected void onSuccess(Map map, List<Map> tArray) {
//                super.onSuccess(map, tArray);
//                callback.onDataLoaded(map);
//            }
//
//            @Override
//            public void onFailure(String errorMsg, int code) {
//                super.onFailure(errorMsg, code);
//                callback.onDataNotAvailable(errorMsg, code);
//            }
//        }), params);
//    }

    @Override
    public void signClock(final LoadDataCallback<String> callback, String flag) {

        NetWorkManagerLogin.signAgreement("5", flag, "", new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            protected void onSuccess(Object str, List<Object> tArray, String rawData) {
                super.onSuccess(str, tArray, rawData);
                callback.onDataLoaded(rawData);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }));
    }

    @Override
    public void getAllLimit(final LoadDataCallback<AllLimitBean> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("userName", MyPlatform.getCurrentUser().getUserName());
        NetWorkManager.request(this, NetworkConstant.GET_FACE_AGREEMENT_PRIVILEGE, new SimpleReqCallbackAdapter<>(new AbsReqCallback<AllLimitBean>(AllLimitBean.class) {
            @Override
            protected void onSuccess(AllLimitBean bean, List<AllLimitBean> tArray) {
                super.onSuccess(bean, tArray);
                callback.onDataLoaded(bean);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }


    @Override
    public void onDestroy() {

    }
}
