package com.jd.oa.business.jdsaaslogin.util

import android.content.Context
import android.content.Intent
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.jd.oa.AppBase
import com.jd.oa.MyPlatform
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.business.jdsaaslogin.data.model.JdSaasUserInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamData
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.login.controller.LoginFragment.loginSuccessData
import com.jd.oa.configuration.local.ThirdPartyConfigHelper
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.fragment.model.WebBean
import com.jd.oa.fragment.web.WebConfig
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.network.gateway.ServeConfig
import com.jd.oa.network.token.KeyManager
import com.jd.oa.network.token.TokenManager
import com.jd.oa.preference.JdSaasLoginPreference
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.storage.StorageHelper
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jd.oa.wjloginclient.ClientUtils

object JdSaasLoginHelper {
    fun setUserInfo(token : String?,phone : String?,teamAccountInfo: TeamAccountInfo?, teamUserInfo: TeamUserInfo?){
        if(token != null && teamAccountInfo != null && teamUserInfo != null){
            //保存用户名和头像，下次登录显示
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_USERNAME,teamUserInfo.realName?:"")
            JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_AVATAR,teamUserInfo.avatar?:"")
            //保存用户信息,数据映射到京ME的用户模型
            val accessToken = token
            val mobile = phone
            val teamId = teamAccountInfo.teamId
            val appId = teamAccountInfo.teamId
            val userId = teamUserInfo.userId
            val userName = teamUserInfo.userId
            val realName = teamUserInfo.realName
            val userIcon = teamUserInfo.avatar
            val tenantCode = teamAccountInfo.teamId
            val teamName = teamAccountInfo.teamFullName
            val wjLoginPin = teamUserInfo.pin
            val userInfo = JdSaasUserInfo(accessToken,teamId,appId,userId,userName,realName,userIcon,mobile,tenantCode,teamName,wjLoginPin)
            loginSuccessData(JSONObject.toJSONString(userInfo),null)

            // 更新首页头像
            val imDdService = AppJoint.service(ImDdService::class.java)
            imDdService.updateCurrentUserInfo()
        }
    }

    fun updateAndSaveTeamData(teamData : TeamData?,loginTeamId : String?,loginPin : String?) {
        teamData?.teamAccountInfoList?.forEach {
            it.loginCurrently = false
            if(it.teamId == loginTeamId){
                it.loginCurrently = true
                it.teamUserInfoList?.forEach {
                    it.loginCurrently = false
                    if(it.pin == loginPin){
                        it.activeStatus = 1 //登录成功改为已激活
                        it.loginCurrently = true
                    }
                }
            }else{
                it.teamUserInfoList?.forEach { it.loginCurrently = false }
            }
        }
        //当前登录的租户放到第一个
        val teamAccountInfoList = teamData?.teamAccountInfoList?.sortedBy {
            if(it.loginCurrently) -1 else 1
        }
        val teamDataJsonStr = JSON.toJSONString(TeamData(teamAccountInfoList))
        JdSaasLoginPreference.put(JdSaasLoginPreference.KV_ENTITY_TEAM_DATA,teamDataJsonStr)
    }

    fun showSwitchAccountDialog(context: Context,confirm: () -> Unit){
        val confirmDialog = ConfirmDialog(context)
        confirmDialog.setTitle("")
        confirmDialog.setMessage("是否切换账号？")
        confirmDialog.setPositiveClickListener {
            confirm()
        }
        confirmDialog.show()
    }

    fun getJsonParamsForPasswordLogin(loginName: String?) : String{
        // [{"id":0,"loginName":"Honery016","status":2,"userLevel":90}]
        return "[{\"loginName\":\"${loginName}\"}]"
    }

    fun maskPhoneNumber(phoneNumber : String?) : String{
        if(phoneNumber == null || phoneNumber.length < 11){
            return phoneNumber?:""
        }
        return phoneNumber.substring(0,3) + "*****"+phoneNumber.substring(8)
    }

    fun forgetPassword(){
        val wjLoginModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).wjLoginModel
        val url = "https://plogin.m.jd.com/cgi-bin/m/mfindpwd?appid=${wjLoginModel.appId}&show_title=0&hasEmail=0&hasPhone=0&returnurl=https%3A%2F%2Fm.jd.com"
        val webBean = WebBean(url, WebConfig.H5_NATIVE_HEAD_SHOW,WebConfig.H5_SHARE_HIDE)
        val intent = Intent(AppBase.getTopActivity(), FunctionActivity::class.java)
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2::class.java.name)
        intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean)
        AppBase.getTopActivity()?.startActivity(intent)
    }

    /*
    * 登录模块单独运行退出登录使用
    * */
    fun logout(){
        // 移除本地信息
        PreferenceManager.UserInfo.removeAll() // 移除当前用户配置信息
        MyPlatform.setCurrentUser(null)
        //删除旧的token，公私钥
        KeyManager.getInstance().clearKeys()
        TokenManager.getInstance().clearTokens()

        MyPlatform.finishAllActivity()
        val intent = AppBase.getAppContext().packageManager.getLaunchIntentForPackage(AppBase.getAppContext().packageName)
        intent?.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK) // 添加清空栈标记
        intent?.putExtra("isRestart", true)
        AppBase.getAppContext().startActivity(intent)

        //解绑JD账号
        val helper = ClientUtils.getWJLoginHelper()
        helper.exitLogin()
        //当前用户登录标记
        StorageHelper.getInstance(AppBase.getAppContext()).logout()
        ServeConfig.getInstance(AppBase.getAppContext()).clear()
    }

}