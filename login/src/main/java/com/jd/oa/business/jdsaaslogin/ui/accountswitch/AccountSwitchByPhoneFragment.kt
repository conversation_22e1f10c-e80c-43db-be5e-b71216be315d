package com.jd.oa.business.jdsaaslogin.ui.accountswitch

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.InjectorUtil
import com.jd.oa.business.jdsaaslogin.ui.view.LoginVerifyCodeView
import com.jd.oa.business.jdsaaslogin.data.LoginType
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.jdsaaslogin.util.LoginKeyboardUtil
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.FragmentUtils
import com.jd.oa.utils.NClick
import com.jd.oa.utils.StatusBarConfig
import com.jme.login.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.fragment_account_switch_by_phone.view.btn_verify_cancel
import kotlinx.android.synthetic.main.fragment_account_switch_by_phone.view.titleBar
import kotlinx.android.synthetic.main.fragment_account_switch_by_phone.view.btn_verify_next
import kotlinx.android.synthetic.main.fragment_account_switch_by_phone.view.lvcv_verify_code
import kotlinx.android.synthetic.main.fragment_account_switch_by_phone.view.tv_login_type_switch
import kotlinx.android.synthetic.main.fragment_account_switch_by_phone.view.tv_verify_code
import kotlinx.android.synthetic.main.fragment_account_switch_by_phone.view.tv_verify_tip2
import kotlinx.android.synthetic.main.jdsaas_login_titlebar.view.iv_back

class AccountSwitchByPhoneFragment : BaseFragment() {

    val viewModel by lazy { ViewModelProvider(requireActivity(), InjectorUtil.getAccountSwitchModelFactory()).get(
        AccountSwitchViewModel::class.java) }

    lateinit var mView: View
    var mMsgCode : String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.mClearMessageCodeLiveData.observe(requireActivity()){
            if(::mView.isInitialized){
                mView.lvcv_verify_code?.setEmpty()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.fragment_account_switch_by_phone,container,false)
        if (StatusBarConfig.enableImmersive()) {
            mView.titleBar.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        }
        mView.iv_back.setOnClickListener {
            requireActivity().finish()
        }

        mView.tv_login_type_switch.setOnClickListener {
            FragmentUtils.replaceWithCommit(requireActivity(),
                AccountSwitchByPasswordFragment::class.java,R.id.fl_container,false,null,false)
        }

        LoginKeyboardUtil.showKeyboard(context,mView.lvcv_verify_code)
        //监听验证码输入
        mView.lvcv_verify_code.onCodeFinishListener = object : LoginVerifyCodeView.OnCodeFinishListener{
            override fun onTextChange(view: View?, content: String?) {
                mView.btn_verify_next.isEnabled = false
            }

            override fun onComplete(view: View?, content: String?) {
                mMsgCode = content
                mView.btn_verify_next.isEnabled = true
                viewModel.checkMessageCodeByPhone(mMsgCode,LoginType.TYPE_PHONE)
                LoginKeyboardUtil.hideKeyboard(context,mView.lvcv_verify_code)
            }
        }
        //下一步
        mView.btn_verify_next.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            if(mMsgCode?.length == 6){
                viewModel.checkMessageCodeByPhone(mMsgCode,LoginType.TYPE_PHONE)
                LoginKeyboardUtil.hideKeyboard(context,mView.lvcv_verify_code)
            }
        }
        //pad上取消按钮
        mView.btn_verify_cancel?.setOnClickListener {
            requireActivity().finish()
        }
        setTvVerifyTip2()
        //设置短信验证码倒计时时间
        setMsgCodeCountDownTime()
        return mView
    }

    private fun setTvVerifyTip2() {
        val msgCodeExpireTime = viewModel.mMsgCodeSendSuccessLiveData.value ?: viewModel.mMsgCodeExpireTime
        if(msgCodeExpireTime != null){
            val spanBuilder = SpannableStringBuilder(getString(R.string.jdsaas_login_verifycode_send))
            val span = SpannableString("+${viewModel.mTeamUserInfo?.countryCode}${JdSaasLoginHelper.maskPhoneNumber(viewModel.mTeamUserInfo?.phone)}")
            span.setSpan(StyleSpan(Typeface.BOLD),0,span.length,Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            span.setSpan(ForegroundColorSpan(resources.getColor(R.color.jdsaas_black_1B1B1B)),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            spanBuilder.append(span)
            spanBuilder.append("，${getString(R.string.jdsaas_login_verifycode_valid,(msgCodeExpireTime / 60).toString())}")
            mView.tv_verify_tip2.setText(spanBuilder)
        }
    }

    private fun setMsgCodeCountDownTime() {
        viewModel.mMsgCodeCountDownLiveData.observe(requireActivity()){
            mView.tv_verify_code.setText(requireContext().getString(R.string.jdsaas_login_verifycode_countdown,it.toString()))
            if(it == 0L){
                val spanBuilder = SpannableStringBuilder(requireContext().getString(R.string.jdsaas_login_verifycode_not_received))
                var span = SpannableString(requireContext().getString(R.string.jdsaas_login_verifycode_reacquire))
                span.setSpan(object : ClickableSpan(){
                    override fun onClick(view: View) {
                        viewModel.loginByPhone()
                        mView.lvcv_verify_code.setEmpty()
                    }
                    override fun updateDrawState(ds: TextPaint) {
                        ds.isUnderlineText = false
                    }
                },0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                span.setSpan(ForegroundColorSpan(Color.RED),0,span.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                spanBuilder.append(span)
                mView.tv_verify_code.setText(spanBuilder)
                mView.tv_verify_code.setMovementMethod(LinkMovementMethod.getInstance())
                mView.tv_verify_code.setHighlightColor(Color.TRANSPARENT)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.mMsgCodeCountDownLiveData.removeObservers(requireActivity())
    }
}