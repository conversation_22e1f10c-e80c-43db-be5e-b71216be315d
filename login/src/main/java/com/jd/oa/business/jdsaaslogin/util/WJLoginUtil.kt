package com.jd.oa.business.jdsaaslogin.util

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.text.TextUtils
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.business.LoginViewModel
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.cache.FileCache
import com.jd.oa.configuration.local.ThirdPartyConfigHelper
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.fragment.model.WebBean
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jd.oa.cache.LogRecorder
import com.jd.oa.utils.ToastUtils
import com.jd.oa.wjloginclient.ClientUtils
import com.jd.verify.SSLDialogCallback
import com.jd.verify.Verify
import com.jd.verify.model.IninVerifyInfo
import jd.wjlogin_sdk.common.DevelopType
import jd.wjlogin_sdk.common.WJLoginHelper
import jd.wjlogin_sdk.common.listener.LoginFailProcessor
import jd.wjlogin_sdk.common.listener.OnCommonCallback
import jd.wjlogin_sdk.common.listener.OnDataCallback
import jd.wjlogin_sdk.common.listener.OnLoginCallback
import jd.wjlogin_sdk.model.ErrorResult
import jd.wjlogin_sdk.model.FailResult
import jd.wjlogin_sdk.model.JumpResult
import jd.wjlogin_sdk.model.QRCodeScannedResult
import jd.wjlogin_sdk.model.SuccessResult
import jd.wjlogin_sdk.util.MD5
import org.json.JSONObject

class WJLoginUtil{

    var helper : WJLoginHelper
    var verify : Verify
    var mLogRecorder: LogRecorder
    var mUserName: String? = null
    var mPassword: String? = null
    var mLoginByPasswordCallback : LoginByPasswordCallback? = null

    init {
        helper = ClientUtils.getWJLoginHelper()
        setWJLoginEnv()
        verify = Verify.getInstance()
        mLogRecorder = LogRecorder.with(FileCache.getInstance().startUpLogFile)
    }

    fun setWJLoginEnv(){
        val env = PreferenceManager.UserInfo.getNetEnvironment()
        if(env == NetEnvironmentConfigModel.PROD){
            helper.setDevelop(DevelopType.PRODUCT)
        }else if(env == NetEnvironmentConfigModel.PREV){
            helper.setDevelop(DevelopType.BETA)
        }else if(env == NetEnvironmentConfigModel.TEST){
            helper.setDevelop(DevelopType.DEBUG)
            helper.debugUrl = "http://test-wlogin.m.jd.com/"
        }
    }

    private val mRiskManagementReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val url = intent.getStringExtra("url")
            handleRiskManagementResult(url)
        }
    }

    fun unRegisterRiskManagementReceiver(){
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(mRiskManagementReceiver)
    }

    /***
     * 账号密码登录
     */
    fun loginByPassword(userName: String?,password: String?,callback : LoginByPasswordCallback?){
        if(userName == null || password == null){
            return
        }
        mUserName = userName
        mPassword = MD5.encrypt32(password)
        mLoginByPasswordCallback = callback
        getCaptchaSidForPassword(userName,password)
    }


    fun getCaptchaSidForPassword(userName: String?,password: String?) {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("loginName", mUserName)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        helper.getCaptchaSid(4, jsonObject, object : OnCommonCallback() {
            override fun onSuccess() {
                helper.JDLoginWithPasswordNew(mUserName, mPassword, "", "", onPasswordLoginCallback)
            }

            override fun onError(errorResult: ErrorResult) {
                var tip: String? = "矮油，程序出错了！"
                if (null != errorResult.errorMsg) {
                    tip = errorResult.errorMsg
                }
                ToastUtils.showToast(AppBase.getAppContext(), tip)
            }

            override fun onFail(failResult: FailResult) {
                val sid = failResult.strVal
                if (TextUtils.isEmpty(sid)) {
                    ToastUtils.showToast(AppBase.getAppContext(), failResult.message)
                    return
                }
                //第三个参数是uuid,app中有uuid 请填上
                verify.init(sid, AppBase.getTopActivity(), "", mUserName, object : SSLDialogCallback{
                    override fun onSSLError() {
                        mLogRecorder.record(LoginViewModel.TAG, "verifyCallback onSSLError")
                    }

                    override fun showButton(i: Int) {
                        mLogRecorder.record(LoginViewModel.TAG, "verifyCallback showButton")
                    }

                    override fun invalidSessiongId() {
                        mLogRecorder.record(LoginViewModel.TAG, "verifyCallback invalidSessiongId")
                        getCaptchaSidForPassword(mUserName,mPassword)
                    }

                    override fun onSuccess(ininVerifyInfo: IninVerifyInfo) {
                        mLogRecorder.record(LoginViewModel.TAG, "verifyCallback onSuccess")
                        helper.JDLoginWithPasswordNew(
                            mUserName,
                            mPassword,
                            sid,
                            ininVerifyInfo.vt,
                            onPasswordLoginCallback
                        )
                    }

                    override fun onFail(s: String) {
                        mLogRecorder.record(LoginViewModel.TAG, "verifyCallback onFail")
                        //滑动验证码sdk已经提示，不需要做其他操作
                    }
                })
            }
        })
    }

    // 登录接口回调。
    var onPasswordLoginCallback: OnLoginCallback = object : OnLoginCallback(object : LoginFailProcessor() {
        override fun onCommonHandler(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult?.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback onCommonHandler " + failResult.message)
        }

        override fun getBackPassword(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback getBackPassword " + failResult.message)
        }

        override fun accountNotExist(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback accountNotExist " + failResult.message)
        }

        override fun handle0x6a(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback handle0x6a " + failResult.message)
        }

        override fun handle0x64(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback handle0x64 " + failResult.message)
        }

        override fun handle0x8(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback handle0x8 " + failResult.message)
        }

        override fun handleBetween0x77And0x7a(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback handleBetween0x77And0x7a " + failResult.message)
        }

        override fun handleBetween0x7bAnd0x7e(failResult: FailResult) {
            JdSaasPromptUtil.showErrorToast(failResult.message)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback handleBetween0x7bAnd0x7e " + failResult.message)
        }

        override fun onSendMsgWithoutDialog(failResult: FailResult) {
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback onSendMsgWithoutDialog " + failResult.message)
            val dialog = ConfirmDialog(AppBase.getTopActivity()!!)
            dialog.setMessage(failResult.message)
            val jumpResult = failResult.jumpResult
            if (jumpResult != null) {
                dialog.setPositiveClickListener { openRiskManagementUrl(jumpResult) }
            }
            dialog.show()
        }

        override fun onSendMsg(failResult: FailResult) {
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback onSendMsg " + failResult.message)
            val dialog = ConfirmDialog(AppBase.getTopActivity()!!)
            dialog.setMessage(failResult.message)
            val jumpResult = failResult.jumpResult
            if (jumpResult != null) {
                dialog.setPositiveClickListener { openRiskManagementUrl(jumpResult) }
            }
            dialog.show()
        }
    }) {
        override fun beforeHandleResult() {
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback beforeHandleResult ")
        }

        override fun onSuccess() {
            mLoginByPasswordCallback?.onLoginSuccess()
            // 登录成功
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback onSuccess " + helper.pin)
            // 绑定PIN
//            bind(helper.pin)
        }

        override fun onError(error: ErrorResult) {
            JdSaasPromptUtil.showErrorToast(error.errorMsg)
            mLogRecorder.record(LoginViewModel.TAG, "onLoginCallback onError $error")
        }
    }

    private fun openRiskManagementUrl(jumpResult: JumpResult?) {
        if (jumpResult == null) return
        val jumpToken = jumpResult.token
        val url = jumpResult.url
        val wjLoginModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).wjLoginModel
        val riskUrl = String.format(
            "%1\$s?appid=%2\$s&token=%3\$s&returnurl=jdlogin.safecheck.jdmobile://communication",
            url, wjLoginModel?.appId, jumpToken
        )
        val webBean = WebBean(riskUrl, "1")
        val intent = Intent(AppBase.getTopActivity(), FunctionActivity::class.java)
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2::class.java.name)
        intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean)
        AppBase.getTopActivity()?.startActivity(intent)
        LocalBroadcastManager.getInstance(AppBase.getAppContext())
            .registerReceiver(mRiskManagementReceiver, IntentFilter("RISK_MANAGEMENT_RESULT"))
    }

    private fun handleRiskManagementResult(url: String?) {
        if (TextUtils.isEmpty(url)) return
        val uri = Uri.parse(url)
        val type = uri.getQueryParameter("typelogin_in")
        val status = uri.getQueryParameter("status")
        val token = uri.getQueryParameter("safe_token")
        if ("wjlogin" == type && "true" == status && !TextUtils.isEmpty(token)) {
            helper.h5BackToApp(token, object : OnCommonCallback() {
                override fun onSuccess() {
                    mLoginByPasswordCallback?.onLoginSuccess()
                }

                override fun onError(errorResult: ErrorResult) {
                    mLogRecorder.record(LoginViewModel.TAG, "h5BackToApp onError $errorResult")
                    JdSaasPromptUtil.showErrorToast(errorResult.errorMsg)
                }

                override fun onFail(failResult: FailResult) {
                    mLogRecorder.record(LoginViewModel.TAG, "h5BackToApp onError " + failResult.message + " " + failResult.replyCode)
                    JdSaasPromptUtil.showErrorToast(failResult.message)
                }
            })
        }
    }


    fun loginByPhone(countryCode: String?,phone: String?,callback : LoginByPhoneCallback?){
        if(countryCode == null || phone == null){
            return
        }
        getCaptchaSidForCompanyLogin(countryCode,phone,callback)
    }

    /*企业账号手机验证码登录*/
    fun getCaptchaSidForCompanyLogin(countryCode: String?,phone: String?,callback : LoginByPhoneCallback?) {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("phone",phone)
            jsonObject.put("countryCode",countryCode)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        helper.getCaptchaSid(3, jsonObject, object : OnCommonCallback() {
            override fun onSuccess() {
                getMessageCodeForCompanyLogin(phone,countryCode,"","",callback)
            }

            override fun onError(errorResult: ErrorResult) {
                callback?.onError()
                var tip: String? = "矮油，程序出错了！"
                if (null != errorResult.errorMsg) {
                    tip = errorResult.errorMsg
                }
                ToastUtils.showToast(AppBase.getAppContext(), tip)
            }

            override fun onFail(failResult: FailResult) {
                callback?.onError()
                val sid = failResult.strVal
                if (TextUtils.isEmpty(sid)) {
                    JdSaasPromptUtil.showErrorToast(failResult.message)
                    return
                }
                //第三个参数是uuid,app中有uuid 请填上
                verify.init(sid, AppBase.getTopActivity(), "", countryCode,phone, object : SSLDialogCallback{
                    override fun onSSLError() {
                        mLogRecorder.record(LoginViewModel.TAG, "companyLoginVerifyCallback onSSLError")
                    }

                    override fun showButton(i: Int) {
                        mLogRecorder.record(LoginViewModel.TAG, "companyLoginVerifyCallback showButton")
                    }

                    override fun invalidSessiongId() {
                        mLogRecorder.record(LoginViewModel.TAG, "companyLoginVerifyCallback invalidSessiongId")
                        getCaptchaSidForCompanyLogin(countryCode,phone,callback)
                    }

                    override fun onSuccess(ininVerifyInfo: IninVerifyInfo) {
                        mLogRecorder.record(LoginViewModel.TAG, "companyLoginVerifyCallback onSuccess")
                        getMessageCodeForCompanyLogin(phone,countryCode,ininVerifyInfo.vt,sid,callback)
                    }

                    override fun onFail(s: String) {
                        mLogRecorder.record(LoginViewModel.TAG, "companyLoginVerifyCallback onFail")
                        //滑动验证码sdk已经提示，不需要做其他操作
                    }
                })
            }
        })
    }

    fun getMessageCodeForCompanyLogin(phone: String?,countryCode: String?,vt : String,sid : String,callback : LoginByPhoneCallback?){
        helper.getMessageCodeForCompanyLogin(phone,countryCode,sid,vt,object : OnDataCallback<SuccessResult>(){
            override fun onSuccess(successResult: SuccessResult?) {
                val msgCodeExpireTime = successResult?.intVal ?: 0
                val companyLoginResult = successResult?.companyLoginResult
                val getMessageCodeToken = companyLoginResult?.token
                callback?.onGetMessageCode(msgCodeExpireTime,getMessageCodeToken)
            }

            override fun onError(errorResult: ErrorResult?) {
                callback?.onError()
                JdSaasPromptUtil.showErrorToast(errorResult?.errorMsg)
                MELogUtil.localV(LoginViewModel.TAG,"onCompanyLoginDataCallback onError code = ${errorResult?.errorCode} , errorMsg = ${errorResult?.errorMsg}")
            }

            override fun onFail(failResult: FailResult?) {
                //-55表示验证码已发送，请勿重复发送
                if(failResult?.replyCode == "-55".toByte()){
                    callback?.onMessageCodeAlreadySend()
                }else{
                    callback?.onError()
                    JdSaasPromptUtil.showErrorToast(failResult?.message)
                }
                MELogUtil.localV(LoginViewModel.TAG,"onCompanyLoginDataCallback onFail code = ${failResult?.replyCode} ,  message = ${failResult?.message}")
            }
        })
    }

    fun checkMessageCodeForCompanyLogin(
        phone: String?,
        countryCode: String?,
        getMessageCodeToken: String?,
        msgCode: String?,
        callback: LoginByPhoneCallback?){
        helper.checkMessageCodeForCompanyLogin(phone, countryCode, msgCode,getMessageCodeToken,object : OnDataCallback<SuccessResult>(){
            override fun onSuccess(successResult: SuccessResult?) {
                val companyLoginResult = successResult?.getCompanyLoginResult()
                val checkMessageCodeToken = companyLoginResult?.getToken()
                val jsonStr = companyLoginResult?.getJsonStr()
                if(checkMessageCodeToken != null && jsonStr != null){
                    callback?.onCheckMessageCode(jsonStr,checkMessageCodeToken)
                }
            }

            override fun onError(errorResult: ErrorResult?) {
                callback?.onError()
                JdSaasPromptUtil.showErrorToast(errorResult?.errorMsg)
                MELogUtil.localV(LoginViewModel.TAG,"onCheckCompanyDataCallback onError code = ${errorResult?.errorCode} , errorMsg = ${errorResult?.errorMsg}")
            }

            override fun onFail(failResult : FailResult?) {
                callback?.onError()
                JdSaasPromptUtil.showErrorToast(failResult?.message)
                MELogUtil.localV(LoginViewModel.TAG,"onCheckCompanyDataCallback onFail code = ${failResult?.replyCode} ,  message = ${failResult?.message}")
            }

        })
    }

    fun loginWithNameForCompanyLogin(checkMessageCodeToken : String?,loginName : String?,callback: LoginByPhoneCallback?){
        helper.loginWithNameForCompanyLogin(loginName,checkMessageCodeToken,object : OnCommonCallback(){
            override fun onSuccess() {
                callback?.onLoginWithName(loginName)
                MELogUtil.localV(LoginViewModel.TAG,"loginWithNameForCompanyLogin onSuccess - ${helper.a2} , ${helper.pin}")
            }

            override fun onError(error: ErrorResult?) {
                JdSaasPromptUtil.showErrorToast(error?.errorMsg)
                MELogUtil.localV(LoginViewModel.TAG,"loginWithNameForCompanyLogin onError - ${error?.errorCode} , ${error?.errorCode}")
            }

            override fun onFail(fail : FailResult?) {
                JdSaasPromptUtil.showErrorToast(fail?.message)
                MELogUtil.localV(LoginViewModel.TAG,"loginWithNameForCompanyLogin onFail - ${fail?.replyCode} , ${fail?.message}")
            }

        })
    }



    fun getToken() : String{
        return helper.a2
    }

    fun checkToken(pin : String?,callback: CheckTokenCallback?){
        helper.checkA2(pin,object : OnCommonCallback(){
            override fun onSuccess() {
                callback?.onSuccess()
            }

            override fun onError(error: ErrorResult?) {
                callback?.onError()
            }

            override fun onFail(fail: FailResult?) {
                callback?.onError()
            }

        })
    }

    fun switchUserByPin(pin : String?){
        helper.switchUserByPin(pin)
    }

    fun deleteUserByPin(pin : String?){
        helper.deleteUserByPin(pin)
    }

    /*检查二维码状态
    * 客户端类型（1：TV端；2：PC端；3：未知；4：冰箱端）
    * */
    fun verifyQRCode(qrCodekey : String,QRtype : Byte,callback: OnCommonCallback){
        helper.verifyQRCode(qrCodekey,QRtype,callback)
    }

    /*从扫描二维码得到的url解析出qrCodeKey*/
    fun getQRCodeKeyFromUrl(scannedQRCodeUrl : String) : String{
        return helper.getQRCodeKeyFromUrl(scannedQRCodeUrl)
    }

    /*更新为扫描状态*/
    fun confirmQRCodeScanned(qrCodeKey : String,callback : OnDataCallback<QRCodeScannedResult>){
        helper.confirmQRCodeScanned(qrCodeKey,callback)
    }

    /*确认登陆*/
    fun confirmQRCodeLogined(qrCodeKey : String,callback: OnCommonCallback){
        helper.confirmQRCodeLogined(qrCodeKey,callback)
    }

    /*取消登陆*/
    fun cancelQRCodeLogined(qrCodeKey : String,callback: OnCommonCallback){
        helper.cancelQRCodeLogined(qrCodeKey,callback)
    }

    interface LoginByPasswordCallback{
        fun onLoginSuccess()
    }

    interface LoginByPhoneCallback{
        fun onGetMessageCode(msgCodeExpireTime : Int,getMessageCodeToken : String?){}

        fun onMessageCodeAlreadySend(){}

        fun onCheckMessageCode(jsonStr : String,checkMessageCodeToken : String){}

        fun onLoginWithName(loginName: String?){}

        fun onError(){}
    }

    interface CheckTokenCallback{
        fun onSuccess()
        fun onError()
    }

}