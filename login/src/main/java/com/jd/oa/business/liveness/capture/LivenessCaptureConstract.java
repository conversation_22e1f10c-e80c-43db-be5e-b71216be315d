package com.jd.oa.business.liveness.capture;

import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.io.File;

/**
 * 识别接口
 * Created by zhaoyu1 on 2016/12/16.
 */
public interface LivenessCaptureConstract {
    interface View extends IMVPView {
        void showContent();

        /**
         * 显示长传进度遮罩
         *
         * @param message
         */
        void showUploadLayer(String message);

        /**
         * 模版头像上传成功
         */
        void uploadTemplateSuccess();
    }

    interface Presenter extends IMVPPresenter {

        /**
         * 上传模版
         */
        void doUploadTemplate(File[] files, String faceSecretKey, String faceToken, String faceFileType, String sourceSwitch);

    }
}
