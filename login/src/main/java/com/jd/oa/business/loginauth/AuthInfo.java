package com.jd.oa.business.loginauth;

import androidx.annotation.Keep;

/**
 * 登录授权信息
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/28.
 */
@Keep
public class AuthInfo {
    public static final int STATUS_EXPIRED = 0;
    public static final int STATUS_NORMAL = 1;
    public static final int STATUS_CANCELED = 3;
    private String clientId;
    private String businessId;
    private String businessName;
    private String showMessage;
    private int status;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getShowMessage() {
        return showMessage;
    }

    public void setShowMessage(String showMessage) {
        this.showMessage = showMessage;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
