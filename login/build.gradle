if (isRunLoginAlone.toBoolean()) {
    apply plugin: 'com.android.application'
}else{
    apply plugin: 'com.android.library'
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'com.chenenyu.router'
apply plugin: 'maven-publish'
apply plugin: 'com.jfrog.artifactory'

android {
    compileSdkVersion COMPILE_SDK_VERSION
//    defaultPublishConfig "release"
//    flavorDimensions "default"

//    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        if(isRunLoginAlone.toBoolean()){
            flavorDimensions.addAll(flavor_dimensions)
            productFlavors product_flavors_module
            applicationIdSuffix ".login"

            compileOptions {
                sourceCompatibility JavaVersion.VERSION_1_8
                targetCompatibility JavaVersion.VERSION_1_8
            }
            kotlinOptions {
                jvmTarget = "1.8"
            }
        }

        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION


        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        //显示服务器选择器
        buildConfigField 'Boolean', 'SHOW_SERVER_SWITCHER', project.SHOW_SERVER_SWITCHER.toLowerCase()

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.jme.login'
    lint {
        abortOnError false
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])

    implementation COMPILE_COMMON.gson
    implementation "androidx.constraintlayout:constraintlayout:$constraintlayoutVersion"


//    implementation 'com.jingdong.wireless.libs:jdmasdk:4.0.6'

//    implementation 'com.tencent.bugly:crashreport:2.8.6.0'
//    implementation 'com.tencent.bugly:nativecrashreport:3.6.0.0'

//    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'

    // 需要升级2.0.1->2.1.1
    implementation 'io.reactivex.rxjava2:rxandroid:2.0.1'
    //需要2.1.1->2.2.8
    implementation 'io.reactivex.rxjava2:rxjava:2.1.1'
    implementation libs.universal.image.loader

    implementation 'com.jd.oa:mae-bundles-monitorfragment:1.0.2-SNAPSHOT'
    implementation(libs.lib.utils)
    implementation('com.jd.oa:mae-bundles-net:1.1.5-SNAPSHOT')

    implementation project(":common")
    implementation project(":wifiauth")
    api project(":lib_me_flutter")
    api project(':liblivedetect')

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.google.guava:guava:27.0.1-android'
    implementation 'io.github.luizgrp.sectionedrecyclerviewadapter:sectionedrecyclerviewadapter:1.2.0'
}

ext {
    version_name = '0.0.1-SNAPSHOT'
}
apply from: "../publish_to_artifactory.gradle"
