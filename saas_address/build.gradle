if (isAppModule.toBoolean()) {
    apply plugin: 'com.android.application'
}else{
    apply plugin: 'com.android.library'
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'com.chenenyu.router'

android {
    namespace 'com.jdsaas.address'
    compileSdk COMPILE_SDK_VERSION

    defaultConfig {
        if(isAppModule.toBoolean()){
            flavorDimensions.addAll(flavor_dimensions)
            productFlavors product_flavors_module
            applicationIdSuffix ".address"

            compileOptions {
                sourceCompatibility JavaVersion.VERSION_1_8
                targetCompatibility JavaVersion.VERSION_1_8
            }
            kotlinOptions {
                jvmTarget = "1.8"
            }
        }
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        sourceSets{
            main{
                if(isAppModule.toBoolean()){
                    manifest.srcFile 'src/main/module/AndroidManifest.xml'
                }else{
                    manifest.srcFile 'src/main/AndroidManifest.xml'
                }
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation COMPILE_COMMON.gson
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'io.github.luizgrp.sectionedrecyclerviewadapter:sectionedrecyclerviewadapter:1.2.0'
    implementation project(":common")
    implementation project(":login")

}