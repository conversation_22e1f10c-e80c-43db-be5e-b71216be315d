package com.jd.oa.business.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.Window
import com.alibaba.fastjson.JSON
import com.chenenyu.router.Router
import com.jd.oa.BaseActivity
import com.jd.oa.business.LoginActivity
import com.jd.oa.business.LoginActivity.Companion.EXTRA_ROUTER
import com.jd.oa.business.jdsaaslogin.ui.success.LoginSuccessActivity
import com.jd.oa.business.ui.organization.OrganizationViewModel.Companion.PAGE_TYPE_ORGANIZATION_STRUCTURE
import com.jd.oa.business.ui.organization.OrganizationViewModel.Companion.PAGE_TYPE_RELATIVE_TEAM
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.StatusBarConfig
import com.jdsaas.address.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.activity_organization_launch.btn_login
import kotlinx.android.synthetic.main.activity_organization_launch.btn_login_success
import kotlinx.android.synthetic.main.activity_organization_launch.btn_organization
import kotlinx.android.synthetic.main.activity_organization_launch.btn_relative_team

class OrganizationLaunchActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this)
        }
        setContentView(R.layout.activity_organization_launch)

        btn_organization.setOnClickListener {
            val params = mapOf("pageType" to PAGE_TYPE_ORGANIZATION_STRUCTURE)
            Router.build(DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam=${Uri.encode(JSON.toJSONString(params))}").go(this)
        }
        btn_relative_team.setOnClickListener {
            val params = mapOf("pageType" to PAGE_TYPE_RELATIVE_TEAM)
            Router.build(DeepLink.JDSAAS_ADDRESS_ORGANIZATION + "?mparam=${Uri.encode(JSON.toJSONString(params))}").go(this)
        }
        btn_login.setOnClickListener {
            val intent = Intent(this,LoginActivity::class.java)
            intent.putExtra(EXTRA_ROUTER,DeepLink.JDSAAS_ADDRESS_ORGANIZATION)
            startActivity(intent)
        }
        btn_login_success.setOnClickListener {
            val intent = Intent(this,LoginSuccessActivity::class.java)
            startActivity(intent)
        }
    }
}