package com.jd.oa.business.ui.organization

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.data.model.DeptInfo
import com.jd.oa.business.data.model.SubNodeInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.business.ui.organization.OrganizationViewModel.Companion.PAGE_TYPE_ORGANIZATION_STRUCTURE
import com.jd.oa.business.ui.organization.OrganizationViewModel.Companion.PAGE_TYPE_RELATIVE_TEAM
import com.jd.oa.elliptical.SuperEllipticalImageView
import com.jd.oa.ui.IconFontView
import com.jdsaas.address.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class OrganizationDeptSection(val context: Context, val pageType : String, val deptInfo: DeptInfo?, val clickListener:ClickListener?) : Section(
    SectionParameters.builder().itemResourceId(R.layout.jdsaas_organization_dept_item).build()) {
    override fun getContentItemsTotal(): Int {
        return deptInfo?.subNodeList?.size?:0
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, position: Int) {
        val holder = viewHolder as ItemViewHolder
        val subNodeInfo = deptInfo?.subNodeList?.get(position)
        holder.ll_dept_item.setOnClickListener {
            clickListener?.onDeptItemClickListener(this,deptInfo,subNodeInfo,position)
        }
        if(subNodeInfo?.relativeTeamInfo != null){
            JdSaasAvatarUtil.loadGroupAvatar(context, holder.iv_dept_avatar,subNodeInfo?.relativeTeamInfo?.avatar,subNodeInfo?.relativeTeamInfo?.relatedOrgName)
            holder.tv_dept_name.setText(subNodeInfo?.relativeTeamInfo?.relatedOrgName)
        }else{
            if(pageType == PAGE_TYPE_ORGANIZATION_STRUCTURE){
                JdSaasAvatarUtil.loadGroupAvatar(context, holder.iv_dept_avatar,null,R.drawable.jdsaas_address_organization_structure)
            }else if(pageType == PAGE_TYPE_RELATIVE_TEAM){
                JdSaasAvatarUtil.loadGroupAvatar(context, holder.iv_dept_avatar,null,R.drawable.jdsaas_address_relative_team)
            }
            holder.tv_dept_name.setText(subNodeInfo?.deptName)
        }
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val ll_dept_item = view.findViewById<LinearLayout>(R.id.ll_dept_item)
        val iv_dept_avatar = view.findViewById<SuperEllipticalImageView>(R.id.iv_dept_avatar)
        val tv_dept_name = view.findViewById<TextView>(R.id.tv_dept_name)
        val iv_dept_right_arrow = view.findViewById<IconFontView>(R.id.iv_dept_right_arrow)
    }

    interface ClickListener{
        fun onDeptItemClickListener(section: OrganizationDeptSection,deptInfo: DeptInfo?,subNodeInfo: SubNodeInfo?,position: Int)
    }
}
