package com.jd.oa.business.ui.organization

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.data.model.DeptInfo
import com.jd.oa.business.data.model.EmplInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.elliptical.SuperEllipticalImageView
import com.jdsaas.address.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class OrganizationUserSection(val context: Context, val deptInfo: DeptInfo?, val clickListener: ClickListener?) : Section(
    SectionParameters.builder().itemResourceId(R.layout.jdsaas_organization_user_item).build()){

    override fun getContentItemsTotal(): Int {
        return deptInfo?.emplList?.size?:0
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, position: Int) {
        val holder = viewHolder as ItemViewHolder
        val emplInfo = deptInfo?.emplList?.get(position)
        JdSaasAvatarUtil.loadPersonalAvatar(context,holder.iv_user_avatar,emplInfo?.avatar)
        holder.tv_user_name.setText(emplInfo?.realName)
        if(TextUtils.isEmpty(emplInfo?.titleName)){
            holder.tv_user_post.visibility = View.GONE
        }else{
            holder.tv_user_post.visibility = View.VISIBLE
            holder.tv_user_post.setText(emplInfo?.titleName)
        }
        holder.ll_user_item.setOnClickListener {
            clickListener?.onUserItemClickListener(this,emplInfo,position)
        }
//        if(true){
//            holder.tv_user_identify.visibility = View.VISIBLE
//            holder.tv_user_identify.setText(context.getString(R.string.jdsaas_address_super_manager))
//            holder.tv_user_identify.setTextColor(context.getColor(R.color.jdsaas_color_purple_8031F5))
//            holder.tv_user_identify.background = context.getDrawable(R.drawable.jdsaas_organization_identity_round_purple_bg)
//        }else{
//            holder.tv_user_identify.setText(context.getString(R.string.jdsaas_address_manager))
//            holder.tv_user_identify.setTextColor(context.getColor(R.color.jdsaas_color_red_F63218))
//            holder.tv_user_identify.background = context.getDrawable(R.drawable.jdsaas_organization_identity_round_red_bg)
//        }
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val ll_user_item = view.findViewById<LinearLayout>(R.id.ll_user_item)
        val iv_user_avatar = view.findViewById<SuperEllipticalImageView>(R.id.iv_user_avatar)
        val tv_user_name = view.findViewById<TextView>(R.id.tv_user_name)
        val tv_user_post = view.findViewById<TextView>(R.id.tv_user_post)
        val tv_user_identify = view.findViewById<TextView>(R.id.tv_user_identify)
    }

    interface ClickListener{
        fun onUserItemClickListener(section: OrganizationUserSection, emplInfo: EmplInfo?,position: Int)
    }

}
