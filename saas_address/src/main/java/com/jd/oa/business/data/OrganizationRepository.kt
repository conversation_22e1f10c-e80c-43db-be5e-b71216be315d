package com.jd.oa.business.data

import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.preference.PreferenceManager

class OrganizationRepository {

    companion object{
        const val API_GET_EBOOK_DEPTINFO = "ebook.getEbookDeptInfoV2" //获取组织架构列表
        const val API_GET_RELATED_ORG_LIST = "ebook.getRelatedOrgList";//关联组织列表
        const val API_GET_RELATED_ORG_INFO = "ebook.getRelatedOrgInfo";//查看关联组织架构
    }

    fun getEbookDeptInfo(deptId : String?,callback: SimpleRequestCallback<String>){
        val params = mapOf("deptId" to deptId)
        HttpManager.color().post(params,null,API_GET_EBOOK_DEPTINFO,callback)
    }

    fun getRelativeTeamList(callback: SimpleRequestCallback<String>){
        HttpManager.color().post(null,null, API_GET_RELATED_ORG_LIST,callback)
    }

    fun getRelatedOrgInfo(relatedOrgId : String?,deptId : String?,deptType : String?,callback: SimpleRequestCallback<String>){
        val params = mapOf("relatedOrgId" to relatedOrgId,"deptId" to deptId,"deptType" to deptType)
        HttpManager.color().post(params,null, API_GET_RELATED_ORG_INFO,callback)
    }


    fun getEbookDeptInfoByCache(deptId : String?) : String? {
        val params = hashMapOf("deptId" to deptId)
        val cache = ResponseCacheGreenDaoHelper.loadCache(
            PreferenceManager.UserInfo.getUserName(),API_GET_EBOOK_DEPTINFO,params
        )
        if (cache?.response != null) return cache.response
        return null
    }

    fun getRelativeTeamListByCache() : String? {
        val cache = ResponseCacheGreenDaoHelper.loadCache(
            PreferenceManager.UserInfo.getUserName(),API_GET_RELATED_ORG_LIST,null
        )
        if (cache?.response != null) return cache.response
        return null
    }

    fun getRelatedOrgInfo(relatedOrgId : String?,deptId : String?,deptType : String?) : String?{
        val params = hashMapOf("relatedOrgId" to relatedOrgId,"deptId" to deptId,"deptType" to deptType)
        val cache = ResponseCacheGreenDaoHelper.loadCache(
            PreferenceManager.UserInfo.getUserName(),API_GET_RELATED_ORG_INFO,params
        )
        if (cache?.response != null) return cache.response
        return null
    }

}