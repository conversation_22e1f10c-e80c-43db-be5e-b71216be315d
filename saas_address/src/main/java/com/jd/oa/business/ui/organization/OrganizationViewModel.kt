package com.jd.oa.business.ui.organization

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.AppBase
import com.jd.oa.business.data.OrganizationRepository
import com.jd.oa.business.data.model.DeptInfo
import com.jd.oa.business.data.model.RelativeTeamInfo
import com.jd.oa.business.data.model.RelativeTeamList
import com.jd.oa.business.data.model.SubNodeInfo
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.preference.PreferenceManager
import com.jdsaas.address.R
import java.util.HashMap

class OrganizationViewModel(private val repository: OrganizationRepository) : ViewModel() {

    companion object{
        val TAG = "OrganizationViewModel"

        val PAGE_TYPE_ORGANIZATION_STRUCTURE = "organizationStructure" //组织架构
        val PAGE_TYPE_RELATIVE_TEAM = "relativeTeam" //关联组织

        val FIRST_NODEID = "firstNodeId"
        val SECOND_NODEID = "secondNodeId"
    }
    //保存上方导航列表数据
    val mSubNodeInfoList = mutableListOf<SubNodeInfo>()
    val mShowSubFragment = MutableLiveData<String>()
    val mBackToSpecifiedFragment = MutableLiveData<String>()
    val mDeptInfoLiveData = MutableLiveData<Map<String,Any?>>()
    var pageType : String = PAGE_TYPE_ORGANIZATION_STRUCTURE
    var mRelativeTeamId : String? = null //关联租户所选租户的teamId

    fun initSubNodeInfoList(){
        val deptName = if(pageType == PAGE_TYPE_ORGANIZATION_STRUCTURE)
            "" else AppBase.getAppContext().getString(R.string.jdsaas_address_my_relative_team)
        mSubNodeInfoList.clear()
        //上方导航第一个条“通讯录”，点击回到通讯录首页
        val addressNode = SubNodeInfo(null,AppBase.getAppContext().getString(R.string.jdsaas_address_address), FIRST_NODEID,null,null)
        //上方导航第二条当前租户，点击回到组织架构第一级
        val teamNode = SubNodeInfo(null,deptName, SECOND_NODEID,null,null)
        mSubNodeInfoList.add(addressNode)
        mSubNodeInfoList.add(teamNode)
    }

    fun isOrganizationStructure() : Boolean{
        return pageType == PAGE_TYPE_ORGANIZATION_STRUCTURE
    }

    fun isRelativeTeamPage() : Boolean{
        return pageType == PAGE_TYPE_RELATIVE_TEAM
    }

    fun getEbookDeptInfo(deptId : String?){
        var paramDeptId : String? = deptId
        if(deptId == SECOND_NODEID){
            paramDeptId = ""
        }
        val cacheResponse = repository.getEbookDeptInfoByCache(deptId)
        if(cacheResponse != null){
            handleEbookDeptInfoResponse(deptId,cacheResponse)
        }
        repository.getEbookDeptInfo(paramDeptId,object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                if(info?.isSuccessful?:false){
                    if(cacheResponse == null){
                        handleEbookDeptInfoResponse(deptId,info?.result)
                    }
                    if(info?.result != null){
                        val params = hashMapOf("deptId" to deptId)
                        addResponseCache(OrganizationRepository.API_GET_EBOOK_DEPTINFO,params,info.result)
                    }
                }else{
                    mDeptInfoLiveData.postValue(null)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                mDeptInfoLiveData.postValue(null)
            }
        })
    }

    fun handleEbookDeptInfoResponse(deptId : String?,result : String?){
        if(result != null){
            val deptInfo = ApiResponse.parse<DeptInfo>(result,DeptInfo::class.java)
            val map = mapOf("deptId" to deptId,"deptInfo" to deptInfo.data)
            if(deptInfo.data.parentList?.isNotEmpty()?:false){
                mSubNodeInfoList.removeIf{it -> it.deptId != FIRST_NODEID && it.deptId != SECOND_NODEID}
                mSubNodeInfoList.addAll(deptInfo.data.parentList!!)
            }
            mDeptInfoLiveData.postValue(map)
        }
    }

    fun getRelativeTeamList(deptId: String?){
        val cacheResponse = repository.getRelativeTeamListByCache()
        if(cacheResponse != null){
            handleRelativeTeamListResponse(deptId,cacheResponse)
        }
        repository.getRelativeTeamList(object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                if(info?.isSuccessful?:false){
                    if(cacheResponse == null){
                        handleRelativeTeamListResponse(deptId,info?.result)
                    }
                    if(info?.result != null){
                        addResponseCache(OrganizationRepository.API_GET_RELATED_ORG_LIST,null,info.result)
                    }
                }else{
                    mDeptInfoLiveData.postValue(null)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                mDeptInfoLiveData.postValue(null)
            }
        })
    }

    fun handleRelativeTeamListResponse(deptId: String?,result: String?){
        if(result != null){
            val relativeTeamList = ApiResponse.parse<RelativeTeamList>(result,RelativeTeamList::class.java)
            //RelativeTeamInfo转换成SubNodeInfo
            val subNodeInfoList = mutableListOf<SubNodeInfo>()
            relativeTeamList?.data?.relatedOrgList?.forEach {
                subNodeInfoList.add(SubNodeInfo(it.relatedOrgId,it.relatedOrgName,null,null,it))
            }
            val deptInfo = DeptInfo(null,null,null,subNodeInfoList,null,null,null,null,null)
            val map = mapOf("deptId" to deptId,"deptInfo" to deptInfo)
            mDeptInfoLiveData.postValue(map)
        }
    }

    fun getRelatedOrgInfo(relatedOrgId : String?,deptId : String?,deptType : String?,isRelativeTeamInfo : Boolean){
        val cacheResponse = repository.getRelatedOrgInfo(relatedOrgId,deptId,deptType)
        if(cacheResponse != null){
            handleRelatedOrgInfoResponse(deptId,cacheResponse)
        }
        //我的关联组织一级的deptId和deptType为null
        val deptIdParam = if(isRelativeTeamInfo) "" else deptId
        val deptTypeParam = if(isRelativeTeamInfo) "" else deptType
        repository.getRelatedOrgInfo(relatedOrgId,deptIdParam,deptTypeParam,object : SimpleRequestCallback<String>(){
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                if(info?.isSuccessful?:false){
                    if(cacheResponse == null){
                        handleRelatedOrgInfoResponse(deptId,info?.result)
                    }
                    if(info?.result != null){
                        val params = hashMapOf("relatedOrgId" to relatedOrgId,"deptId" to deptId,"deptType" to deptType)
                        addResponseCache(OrganizationRepository.API_GET_RELATED_ORG_INFO,params,info.result)
                    }
                }else{
                    mDeptInfoLiveData.postValue(null)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                mDeptInfoLiveData.postValue(null)
            }
        })
    }

    fun handleRelatedOrgInfoResponse(deptId : String?,result : String?){
        if(result != null){
            val deptInfo = ApiResponse.parse<DeptInfo>(result,DeptInfo::class.java)
            val map = mapOf("deptId" to deptId,"deptInfo" to deptInfo.data)
            val parentList = mutableListOf<SubNodeInfo>()

            for (i in 0 until  (deptInfo.data.parentList?.size?:0)){
                val subNodeInfo = deptInfo.data.parentList?.get(i)
                subNodeInfo?.relatedOrgId = deptInfo.data.relatedOrgId
                if(i == 0){ //对返回的parentList,针对第一条数据构建一个和我的关联组织的item相同的relativeTeamInfo
                    subNodeInfo?.relativeTeamInfo = RelativeTeamInfo(deptInfo.data.relatedOrgId,null,subNodeInfo?.deptId,null)
                }
                if(subNodeInfo != null){
                    parentList.add(subNodeInfo)
                }
            }
            //我的关联组织列表item作为面包屑保留，因为它使用的是teamId作为fragment的tag,parentList使用的是deptId
            if(mSubNodeInfoList.size >=3 && mSubNodeInfoList.get(2).relativeTeamInfo != null){
                parentList.removeAt(0)
            }
            if(parentList.isNotEmpty()){
                mSubNodeInfoList.removeIf{it -> it.deptId != FIRST_NODEID && it.deptId != SECOND_NODEID && it.relativeTeamInfo == null}
                mSubNodeInfoList.addAll(parentList)
            }
            mDeptInfoLiveData.postValue(map)
        }
    }

    fun removeToSpecifiedSubNodeInfo(subNodeInfo: SubNodeInfo?){
        if(mSubNodeInfoList.contains(subNodeInfo)){
            var last = mSubNodeInfoList.last()
            while (mSubNodeInfoList.isNotEmpty() && last != subNodeInfo){
                mSubNodeInfoList.removeLast()
                last = mSubNodeInfoList.last()
            }
        }
    }

    fun removeLastSubNodeInfo(subNodeInfo: SubNodeInfo?){
        if(mSubNodeInfoList.isNotEmpty() && mSubNodeInfoList.last() == subNodeInfo){
            mSubNodeInfoList.remove(subNodeInfo)
        }
    }

    fun addResponseCache(url : String, params : HashMap<String, String?>?, content : String){
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(),url,params,content)
    }
}