package com.jd.oa.business

import android.app.Activity
import android.os.Bundle
import com.jd.oa.AppBase
import com.jd.oa.BaseApp
import com.jd.oa.CommonApp
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jme.login.BuildConfig
import java.lang.ref.WeakReference

class AddressApp : BaseApp() {
    override fun appInit() {
        registerApplicationInit(CommonApp::class.java)
        registerLifeActivityCallbacks()
        initBuildConfig()
    }

    override fun onCreate() {
        super.onCreate()
        ApmLoaderHepler.getInstance(this).init(AppBase.DEBUG, BuildConfig.SHOW_SERVER_SWITCHER)
    }

    private fun initBuildConfig() {
        AppBase.DEBUG = BuildConfig.DEBUG
        AppBase.SHOW_SERVER_SWITCHER = BuildConfig.SHOW_SERVER_SWITCHER
    }

    fun registerLifeActivityCallbacks() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                AppBase.topActivity = WeakReference(activity)
            }

            override fun onActivityStarted(activity: Activity) {
            }

            override fun onActivityResumed(activity: Activity) {
                AppBase.topActivity = WeakReference(activity)
            }

            override fun onActivityPaused(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {
            }
        })
    }
}