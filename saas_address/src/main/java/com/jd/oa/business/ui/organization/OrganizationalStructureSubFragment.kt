package com.jd.oa.business.ui.organization

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.util.AddressInjectorUtil
import com.jd.oa.fragment.BaseFragment
import com.jdsaas.address.R

class OrganizationalStructureSubFragment : BaseFragment() {

    val viewModel by lazy { ViewModelProvider(requireActivity(), AddressInjectorUtil.getOrganizationModelFactory()).get(
        OrganizationViewModel::class.java) }
    lateinit var mView : View

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.fragment_organizational_structure_sub,container,false)
        return mView
    }

    override fun onCreateAnimation(transit: Int, enter: Boolean, nextAnim: Int): Animation? {
        if(enter){
            return AnimationUtils.loadAnimation(requireContext(),R.anim.h_fragment_enter)
        }else{
            return AnimationUtils.loadAnimation(requireContext(),R.anim.h_fragment_exit)
        }
    }
}