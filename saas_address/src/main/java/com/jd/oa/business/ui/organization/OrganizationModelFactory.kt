package com.jd.oa.business.ui.organization

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.data.OrganizationRepository

class OrganizationModelFactory(private val repository: OrganizationRepository)
    : ViewModelProvider.NewInstanceFactory(){

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return OrganizationViewModel(repository) as T
    }
}