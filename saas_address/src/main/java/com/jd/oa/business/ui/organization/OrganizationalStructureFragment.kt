package com.jd.oa.business.ui.organization

import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chenenyu.router.Router
import com.jd.oa.business.data.model.DeptInfo
import com.jd.oa.business.data.model.EmplInfo
import com.jd.oa.business.data.model.SubNodeInfo
import com.jd.oa.business.ui.organization.OrganizationViewModel.Companion.SECOND_NODEID
import com.jd.oa.business.util.AddressInjectorUtil
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.StatusBarConfig
import com.jdsaas.address.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.ll_empty
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.ll_organization_member_add
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.ll_search
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.pb_loading
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.rv_organization_horizontal
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.rv_organization_vertical
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.titleBar
import kotlinx.android.synthetic.main.fragment_organizational_structure.view.view_organization_member_add
import kotlinx.android.synthetic.main.jdsaas_address_titlebar.view.iv_back
import kotlinx.android.synthetic.main.jdsaas_address_titlebar.view.tv_title

class OrganizationalStructureFragment : BaseFragment(),
    OrganizationHorizontalSection.ClickListener, OrganizationUserSection.ClickListener,
    OrganizationDeptSection.ClickListener {

    val viewModel by lazy { ViewModelProvider(requireActivity(), AddressInjectorUtil.getOrganizationModelFactory()).get(
        OrganizationViewModel::class.java) }

    lateinit var mView : View
    var mDeptInfo : DeptInfo? = null
    var mCurrentSubNodeInfo : SubNodeInfo? = null
    var mDeptId : String? = null
    var mRelatedOrgId : String? = null
    var mDeptType : String? = null
    var mFrom : String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments.let {
            mDeptId = it?.getString("deptId")
            mRelatedOrgId = it?.getString("relatedOrgId")
            mDeptType = it?.getString("deptType")
            mFrom = it?.getString("from")
        }

        val subNodeInfoList = viewModel.mSubNodeInfoList
        if(subNodeInfoList.isNotEmpty()){
            mCurrentSubNodeInfo = subNodeInfoList.last().apply {
                //搜索进入，subNodeInfoList中数据对不上，此时用搜索传进来id
                if(mFrom != "search"){
                    mDeptId = deptId
                    mRelatedOrgId = relatedOrgId
                    mDeptType = deptType
                }
            }
        }

        viewModel.mDeptInfoLiveData.observe(this){
            if(it == null){ //请求失败的时候
                showOrganizationList()
            }else{
                val deptId = it.get("deptId")
                val deptInfo = it.get("deptInfo")
                if(deptId == mDeptId){
                    if(deptInfo != null){
                        mDeptInfo = deptInfo as DeptInfo
                        if(deptId == SECOND_NODEID && viewModel.isOrganizationStructure()){//组织架构第二级重新创建SubNodeInfo
                            subNodeInfoList.removeLast()
                            subNodeInfoList.add(SubNodeInfo(mDeptInfo?.relatedOrgId,mDeptInfo?.deptName, SECOND_NODEID,mDeptInfo?.deptType,null))
                        }
                    }
                    showOrganizationList()
                    showOrganizationHorizontalList()
                }
            }
        }

        if(viewModel.isRelativeTeamPage()){
            if(mDeptId == SECOND_NODEID){ //关联组织列表请求
                viewModel.getRelativeTeamList(mDeptId)
            }else{
                val isRelativeTeamInfo = mCurrentSubNodeInfo?.relativeTeamInfo != null //true-我的关联组织列表item点击
                viewModel.getRelatedOrgInfo(mRelatedOrgId,mDeptId,mDeptType,isRelativeTeamInfo)
            }
        }else{
            viewModel.getEbookDeptInfo(mDeptId)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.fragment_organizational_structure,container,false)
        if (StatusBarConfig.enableImmersive()) {
            mView.titleBar.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(context), 0, 0)
        }
        mView.iv_back.setOnClickListener {
            val backStackCount = requireActivity().supportFragmentManager.backStackEntryCount
            if(viewModel.mSubNodeInfoList.isEmpty() || backStackCount == 1) {
                requireActivity().finish()
            }else{
                removeCurrentSubNodeInfo()
                requireActivity().supportFragmentManager.popBackStack()
            }
        }
        mView.tv_title.setText(
            if(viewModel.isRelativeTeamPage()) getString(R.string.jdsaas_address_relative_team)
            else getString(R.string.jdsaas_address_organization_structure))
        //进入到组织架构，只有第一级有添加成员
//        if(viewModel.isOrganizationStructure() && mCurrentSubNodeInfo?.deptId == SECOND_NODEID){
//            mView.ll_organization_member_add.visibility = View.VISIBLE
//            mView.view_organization_member_add.visibility = View.VISIBLE
//        }else{
            //隐藏添加成员入口
            mView.ll_organization_member_add.visibility = View.GONE
            mView.view_organization_member_add.visibility = View.GONE
//        }

        mView.ll_search.setOnClickListener {
            val uri = Uri.parse(DeepLink.UNIFIED_SEARCH)
                .buildUpon()
                .appendQueryParameter("mparam", "{\"defaultTab\": \"1\"}")
                .build()
            Router.build(uri).go(activity)
        }
        showOrganizationHorizontalList()
        mView.pb_loading.visibility = View.VISIBLE
        return mView
    }

    fun showOrganizationHorizontalList(){
        val horizontalManager = LinearLayoutManager(activity,LinearLayoutManager.HORIZONTAL,false)
        mView.rv_organization_horizontal.layoutManager = horizontalManager
        val horizontalSectionAdapter = SectionedRecyclerViewAdapter()
        val organizationHorizontalSection = OrganizationHorizontalSection(requireActivity(),viewModel.mSubNodeInfoList,this)
        horizontalSectionAdapter.addSection(organizationHorizontalSection)
        mView.rv_organization_horizontal.adapter = horizontalSectionAdapter
        mView.post({
            mView.rv_organization_horizontal.scrollToPosition(viewModel.mSubNodeInfoList.size -1)
        })
    }

    fun showOrganizationList() {
        mView.pb_loading.visibility = View.GONE
        if((mDeptInfo?.emplList?.size?:0) > 0 ||  (mDeptInfo?.subNodeList?.size?:0) > 0){
            mView.rv_organization_vertical.visibility = View.VISIBLE
            mView.ll_empty.visibility = View.GONE

            val verticalManager = LinearLayoutManager(activity,LinearLayoutManager.VERTICAL,false)
            mView.rv_organization_vertical.layoutManager = verticalManager
            val verticalSectionAdapter = SectionedRecyclerViewAdapter()
            val organizationDeptSection = OrganizationDeptSection(requireActivity(),viewModel.pageType,mDeptInfo,this)
            val organizationUserSection = OrganizationUserSection(requireActivity(),mDeptInfo,this)
            verticalSectionAdapter.addSection(organizationDeptSection)
            verticalSectionAdapter.addSection(organizationUserSection)
            mView.rv_organization_vertical.adapter = verticalSectionAdapter
        }else{
            mView.rv_organization_vertical.visibility = View.GONE
            mView.ll_empty.visibility = View.VISIBLE
        }
    }

    override fun onOHItemClickListener(
        section: OrganizationHorizontalSection,
        subNodeInfo: SubNodeInfo,
        position: Int
    ) {
        var id = subNodeInfo.deptId
        if(subNodeInfo.relativeTeamInfo != null){ //我的关联组织列表item作为面包屑的点击
            id = subNodeInfo.relativeTeamInfo?.teamId
        }
        viewModel.mBackToSpecifiedFragment.postValue(id)
        viewModel.removeToSpecifiedSubNodeInfo(subNodeInfo)
    }

    override fun onDeptItemClickListener(
        section: OrganizationDeptSection,
        deptInfo: DeptInfo?,
        subNodeInfo: SubNodeInfo?,
        position: Int
    ) {
        if(subNodeInfo != null){
            //不是我的关联组织列表的数据，需要将DeptInfo外层中的relatedOrgId、deptType赋值给SubNodeInfo，请求接口使用
            if(subNodeInfo.relativeTeamInfo == null){
                subNodeInfo.relatedOrgId = deptInfo?.relatedOrgId
                subNodeInfo.deptType = deptInfo?.deptType
            }
            viewModel.mSubNodeInfoList.add(subNodeInfo)

            var id = subNodeInfo.deptId
            if(subNodeInfo.relativeTeamInfo != null){
                viewModel.mRelativeTeamId = subNodeInfo.relativeTeamInfo?.teamId
                id = subNodeInfo.relativeTeamInfo?.teamId
            }
            viewModel.mShowSubFragment.postValue(id)
        }
    }

    override fun onUserItemClickListener(
        section: OrganizationUserSection,
        emplInfo: EmplInfo?,
        position: Int
    ) {
        val imDdService = AppJoint.service(ImDdService::class.java)
        imDdService.showContactDetailInfo(activity, emplInfo?.teamId,emplInfo?.userId)
    }

    override fun onCreateAnimation(transit: Int, enter: Boolean, nextAnim: Int): Animation? {
        if(enter){
            return AnimationUtils.loadAnimation(requireContext(),R.anim.h_fragment_enter)
        }else{
            return AnimationUtils.loadAnimation(requireContext(),R.anim.h_fragment_exit)
        }
    }

    override fun onBackPressed(): Boolean {
        val backStackCount = requireActivity().supportFragmentManager.backStackEntryCount
        if(viewModel.mSubNodeInfoList.isEmpty() || backStackCount == 1) {
            requireActivity().finish()
            return true
        }
        removeCurrentSubNodeInfo()
        return super.onBackPressed()
    }

    /*页面退出后从租户导航列表中移除*/
    private fun removeCurrentSubNodeInfo(){
        if(mCurrentSubNodeInfo != null){
            viewModel.removeLastSubNodeInfo(mCurrentSubNodeInfo)
        }
    }

}