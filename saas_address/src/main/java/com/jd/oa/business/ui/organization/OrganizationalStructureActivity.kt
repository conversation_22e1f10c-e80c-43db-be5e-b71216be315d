package com.jd.oa.business.ui.organization

import android.os.Bundle
import android.view.Window
import androidx.lifecycle.ViewModelProvider
import com.alibaba.fastjson.JSONObject
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.business.util.AddressInjectorUtil
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.StatusBarConfig
import com.jdsaas.address.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper

@Route(DeepLink.JDSAAS_ADDRESS_ORGANIZATION)
class OrganizationalStructureActivity : BaseActivity() {

    val viewModel by lazy { ViewModelProvider(this, AddressInjectorUtil.getOrganizationModelFactory()).get(
        OrganizationViewModel::class.java) }

    var mDeptId : String? = null
    var mRelatedOrgId : String? = null
    var mDeptType : String? = null
    var mFrom : String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this)
        }
        setContentView(R.layout.activity_organizational_structure)

        val param = intent.getStringExtra("mparam")
        try {
            val jsonObject = JSONObject.parseObject(param)
            viewModel.pageType = jsonObject.getString("pageType")
            mDeptId = jsonObject.getString("deptId")
            mRelatedOrgId = jsonObject.getString("relatedOrgId")
            mDeptType = jsonObject.getString("deptType")
            mFrom = jsonObject.getString("from")
        } catch (e: Exception) {}

        if(mFrom == "search"){ //通过大搜进来
            viewModel.initSubNodeInfoList()
            showOrganizationalStructureFragment(mDeptId!!)
        }else{
            viewModel.initSubNodeInfoList()
            showOrganizationalStructureFragment(OrganizationViewModel.SECOND_NODEID)
        }

        viewModel.mShowSubFragment.observe(this){
            showOrganizationalStructureSubFragment(it.toString())
        }
        viewModel.mBackToSpecifiedFragment.observe(this){
            if(it == OrganizationViewModel.FIRST_NODEID){
                finish()
            }else{
                popToSubFragment(it.toString())
            }
        }
    }

    private fun showOrganizationalStructureFragment(tag : String) {
        val fragment = OrganizationalStructureFragment()
        fragment.arguments = Bundle().apply {
            putString("deptId", mDeptId)
            putString("relatedOrgId",mRelatedOrgId)
            putString("deptType",mDeptType)
            putString("from",mFrom)
        }
        supportFragmentManager
            .beginTransaction()
            .replace(R.id.fl_container,fragment,tag)
            .addToBackStack(tag)
            .commit()
    }

    private fun showOrganizationalStructureSubFragment(tag : String) {
        val fragment = OrganizationalStructureFragment()
        supportFragmentManager
            .beginTransaction()
            .add(R.id.fl_container,fragment,tag)
            .addToBackStack(tag)
            .commit()
    }


    private fun popToSubFragment(tag : String){
        val fragment = supportFragmentManager.findFragmentByTag(tag)
        if(fragment != null){
            supportFragmentManager.popBackStack(tag,0)
        }else{//点击通讯录-组织架构，直接跳到某一个级，返回上级先移除当前显示的，再跳到指定级
            while (supportFragmentManager.backStackEntryCount > 0) {
                supportFragmentManager.popBackStackImmediate()
            }
            showOrganizationalStructureSubFragment(tag)
        }
    }
}