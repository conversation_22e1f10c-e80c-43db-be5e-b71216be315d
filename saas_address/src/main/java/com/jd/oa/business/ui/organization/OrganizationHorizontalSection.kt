package com.jd.oa.business.ui.organization

import android.content.Context
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.data.model.SubNodeInfo
import com.jd.oa.ui.IconFontView
import com.jdsaas.address.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class OrganizationHorizontalSection(val context : Context,val subNodeInfoList: List<SubNodeInfo>,val clickListener : ClickListener?) : Section(
    SectionParameters.builder().itemResourceId(R.layout.jdsaas_organization_horizontal_item).build()) {
    override fun getContentItemsTotal(): Int {
        return subNodeInfoList.size
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, position: Int) {
        val holder = viewHolder as ItemViewHolder
        val subNodeInfo = subNodeInfoList.get(position)
        var deptName = subNodeInfo.deptName?:""
        if(position == 0 || TextUtils.isEmpty(deptName)){
            holder.iv_organization_right_arrow.visibility = View.GONE
        }else{
            holder.iv_organization_right_arrow.visibility = View.VISIBLE
        }
        if(deptName.length > 10){
            deptName = deptName.substring(0,10)+"..."
        }
        holder.tv_organization_name.setText(deptName)
        if(isLastItem(position)){
           holder.tv_organization_name.setTextColor(context.getColor(R.color.jdsaas_black_9D9D9D))
        }else{
            holder.tv_organization_name.setTextColor(context.getColor(R.color.jdsaas_black_1B1B1B))
            holder.tv_organization_name.setOnClickListener {
                clickListener?.onOHItemClickListener(this,subNodeInfo,position)
            }
        }
    }

    fun isLastItem(position: Int) : Boolean{
        return position == getContentItemsTotal() - 1
    }

    class ItemViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val iv_organization_right_arrow = view.findViewById<IconFontView>(R.id.iv_organization_right_arrow)
        val tv_organization_name = view.findViewById<TextView>(R.id.tv_organization_name)
    }

    interface ClickListener{
        fun onOHItemClickListener(section: OrganizationHorizontalSection,subNodeInfo: SubNodeInfo,position: Int)
    }
}