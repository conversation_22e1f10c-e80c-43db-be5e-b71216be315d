<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/white">

    <include
        android:id="@+id/titleBar"
        layout="@layout/jdsaas_address_titlebar"/>

    <LinearLayout
        android:id="@+id/ll_search"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:orientation="horizontal"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:background="@drawable/jdsaas_organization_search_round_bg">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end|center_vertical"
            android:contentDescription="workbench search"
            android:gravity="center"
            android:text="@string/icon_general_search"
            android:textColor="@color/jdsaas_black_9D9D9D"
            android:textSize="@dimen/JMEIcon_16"
            android:paddingLeft="12dp"
            android:paddingRight="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:hint="@string/jdsaas_address_search_hint"
            android:textColorHint="@color/jdsaas_black_9D9D9D"
            android:textSize="@dimen/text_size_14"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:ellipsize="end"/>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/jdsaas_black_F5F5F5"/>

    <LinearLayout
        android:id="@+id/ll_organization_member_add"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">
        <RelativeLayout
            android:id="@+id/fl_organization_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp">

            <com.jd.oa.elliptical.SuperEllipticalImageView
                android:id="@+id/iv_user_avatar"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/jdsaas_organization_add_member_round_bg"/>
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginRight="8dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_padding_addingcontacts"
                android:textColor="@color/white"
                android:textSize="@dimen/JMEIcon_26" />
        </RelativeLayout>
        <TextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="@string/jdsaas_address_add_member"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/jdsaas_black_1B1B1B"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"/>
        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_team_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_direction_right"
            android:textColor="@color/jdsaas_black_9D9D9D"
            android:textSize="@dimen/JMEIcon_16" />
    </LinearLayout>


    <View
        android:id="@+id/view_organization_member_add"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@color/jdsaas_black_F5F5F5"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_organization_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_organization_vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
        <LinearLayout
            android:id="@+id/ll_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_gravity="center"
            android:gravity="center">
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@drawable/jdsaas_address_organization_empty" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jdsaas_address_no_data"
                android:textSize="@dimen/jdsaas_text_size_14"
                android:textColor="@color/jdsaas_black_6A6A6A"
                android:layout_marginTop="16dp"/>
        </LinearLayout>
        <ProgressBar
            android:id="@+id/pb_loading"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:indeterminateTint="@color/jdsaas_color_red_F63218"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>
</LinearLayout>