<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <Button
        android:id="@+id/btn_organization"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="组织架构"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/white"
        android:background="@drawable/jdsaas_selector_button_rounde_corner"
        android:enabled="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="20dp"/>

    <Button
        android:id="@+id/btn_relative_team"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="关联组织"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/white"
        android:background="@drawable/jdsaas_selector_button_rounde_corner"
        android:enabled="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="20dp"/>

    <Button
        android:id="@+id/btn_login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="登录"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/white"
        android:background="@drawable/jdsaas_selector_button_rounde_corner"
        android:enabled="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="20dp"/>

    <Button
        android:id="@+id/btn_login_success"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="登录成功页"
        android:textSize="@dimen/jdsaas_text_size_14"
        android:textColor="@color/white"
        android:background="@drawable/jdsaas_selector_button_rounde_corner"
        android:enabled="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="20dp"/>

</LinearLayout>