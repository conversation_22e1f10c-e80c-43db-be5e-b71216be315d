<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingLeft="8dp"
    android:paddingRight="8dp">

    <LinearLayout
        android:id="@+id/ll_dept_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:gravity="center_vertical"
        android:background="@drawable/jdsaas_origanization_ripple_white">
        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_dept_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp" />

        <TextView
            android:id="@+id/tv_dept_name"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            tools:text="技术共享组"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/jdsaas_black_1B1B1B"
            android:maxLines="1"
            android:ellipsize="end"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_dept_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_direction_right"
            android:textColor="@color/jdsaas_black_9D9D9D"
            android:textSize="@dimen/JMEIcon_16" />
    </LinearLayout>

</FrameLayout>