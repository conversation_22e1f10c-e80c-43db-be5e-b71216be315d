<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingLeft="8dp"
    android:paddingRight="8dp">

    <LinearLayout
        android:id="@+id/ll_user_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:background="@drawable/jdsaas_origanization_ripple_white">
        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/jdsaas_personal_default_avatar" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="12dp"
            android:paddingRight="12dp">
            <TextView
                android:id="@+id/tv_user_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="京晓东"
                android:textSize="@dimen/jdsaas_text_size_14"
                android:textColor="@color/jdsaas_black_1B1B1B"
                android:maxLines="1"
                android:ellipsize="end"/>
            <TextView
                android:id="@+id/tv_user_post"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="综合业务设计组-采销"
                android:textSize="@dimen/jdsaas_text_size_12"
                android:textColor="@color/jdsaas_black_9D9D9D"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginTop="2dp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_user_identify"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/jdsaas_address_super_manager"
            tools:textColor="@color/jdsaas_color_purple_8031F5"
            android:textSize="@dimen/jdsaas_text_size_12"
            tools:background="@drawable/jdsaas_organization_identity_round_purple_bg"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:visibility="gone"
            tools:visibility="visible"/>
    </LinearLayout>

</FrameLayout>