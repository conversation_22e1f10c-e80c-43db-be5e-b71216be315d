<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        tools:replace="android:supportsRtl,android:allowBackup,android:label,android:theme"
        android:allowBackup="false"
        android:supportsRtl="true"
        android:label="address"
        android:name="com.jd.oa.business.AddressApp"
        android:theme="@style/MEWhiteTheme">
        <activity
            android:name="com.jd.oa.business.ui.organization.OrganizationalStructureActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>
        <activity android:name="com.jd.oa.business.ui.OrganizationLaunchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <meta-data
            android:name="FLAVOR"
            android:value="${FLAVOR}" />
    </application>

</manifest>