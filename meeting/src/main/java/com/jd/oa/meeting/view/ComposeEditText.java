package com.jd.oa.meeting.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.DigitsKeyListener;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.jd.oa.meeting.R;


/**
 * Created by TangKe on 2017/4/5.
 */

public class ComposeEditText extends LinearLayout {
    private final static int[] STATE_ERROR = new int[]{R.attr.state_error};
    private EditText mEdit;
    private ImageButton mClear;

    private ViewGroup mAccessories;
    private boolean mIsError;
    private boolean mZoom;

    private TextView mZoomView;
    private PopupWindow mPopupWindow;

    private OnFocusChangeListener mOuterOnFocusChangeListener;
    private boolean mIsClearEnable;

    private ZoomFormatter mZoomFormatter;
    private OnErrorListener mOnErrorListener;

    private TextWatcher mEditChangeWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            mZoomView.setText(null != mZoomFormatter ? mZoomFormatter.format(s.toString()) : s);
            popupZoomIfNeeded();
            showClearIfNeeded();
        }
    };

    private OnFocusChangeListener mOnEditFocusChangeListener = new OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            popupZoomIfNeeded();
            //通知外部设置的监听器
            if (null != mOuterOnFocusChangeListener) {
                mOuterOnFocusChangeListener.onFocusChange(ComposeEditText.this, hasFocus);
            }
        }
    };

    private OnLayoutChangeListener mZoomLayoutChangeListener = new OnLayoutChangeListener() {
        @Override
        public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
            popupZoomIfNeeded();
        }
    };


    private OnClickListener mOnClearClickListener = new OnClickListener() {
        @Override
        public void onClick(View v) {
            mEdit.getText().clear();
        }
    };

    public ComposeEditText(Context context) {
        this(context, null);
    }

    public ComposeEditText(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, R.attr.composeEditTextStyle);
    }

    public ComposeEditText(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        final LayoutInflater inflater = LayoutInflater.from(context);
        inflater.inflate(R.layout.meeting_layout_edit_text, this);
        mZoomView = (TextView) inflater.inflate(R.layout.meeting_layout_edit_text_zoom, null);
        mZoomView.addOnLayoutChangeListener(mZoomLayoutChangeListener);
        mEdit = (EditText) findViewById(R.id.edit);
        mEdit.setOnFocusChangeListener(mOnEditFocusChangeListener);
        mEdit.addTextChangedListener(mEditChangeWatcher);
        mClear = (ImageButton) findViewById(R.id.clear);
        mClear.setOnClickListener(mOnClearClickListener);
        mAccessories = (ViewGroup) findViewById(R.id.accessories);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.ComposeEditText, defStyleAttr, 0);
        mZoom = a.getBoolean(R.styleable.ComposeEditText_zoom, false);
        mIsClearEnable = a.getBoolean(R.styleable.ComposeEditText_clearEnable, false);
        int accessoryLayoutResId = a.getResourceId(R.styleable.ComposeEditText_accessoryLayout, 0);
        if (0 != accessoryLayoutResId) {
            addAccessory(inflater.inflate(accessoryLayoutResId, mAccessories, false));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_background)) {
            setBackgroundResource(a.getResourceId(R.styleable.ComposeEditText_android_background, 0));
        }
        setEnabled(a.getBoolean(R.styleable.ComposeEditText_android_enabled, true));
        applyEditTextAttributes(a);
        a.recycle();
    }

    /**
     * 由于直接获取当前View有bug,暂时用传递方式
     *
     * @param a
     */
    private void applyEditTextAttributes(TypedArray a) {
        if (a.hasValue(R.styleable.ComposeEditText_android_text)) {
            mEdit.setText(a.getText(R.styleable.ComposeEditText_android_text));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_hint)) {
            mEdit.setHint(a.getText(R.styleable.ComposeEditText_android_hint));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_textColor)) {
            mEdit.setTextColor(a.getColorStateList(R.styleable.ComposeEditText_android_textColor));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_textSize)) {
            mEdit.setTextSize(TypedValue.COMPLEX_UNIT_PX, a.getDimensionPixelSize(R.styleable.ComposeEditText_android_textSize, 15));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_singleLine)) {
            mEdit.setSingleLine(a.getBoolean(R.styleable.ComposeEditText_android_singleLine, false));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_ellipsize)) {
            switch (a.getInt(R.styleable.ComposeEditText_android_ellipsize, -1)) {
                case 1:
                    mEdit.setEllipsize(TextUtils.TruncateAt.START);
                    break;
                case 2:
                    mEdit.setEllipsize(TextUtils.TruncateAt.MIDDLE);
                    break;
                case 3:
                    mEdit.setEllipsize(TextUtils.TruncateAt.END);
                    break;
                case 4:
                    mEdit.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                    break;
            }
        }

        int[] drawables = new int[]{0, 0, 0, 0};
        if (a.hasValue(R.styleable.ComposeEditText_android_drawableLeft)) {
            drawables[0] = a.getResourceId(R.styleable.ComposeEditText_android_drawableLeft, 0);
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_drawableTop)) {
            drawables[1] = a.getResourceId(R.styleable.ComposeEditText_android_drawableTop, 0);
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_drawableRight)) {
            drawables[2] = a.getResourceId(R.styleable.ComposeEditText_android_drawableRight, 0);
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_drawableBottom)) {
            drawables[3] = a.getResourceId(R.styleable.ComposeEditText_android_drawableBottom, 0);
        }
        mEdit.setCompoundDrawablesWithIntrinsicBounds(drawables[0], drawables[1], drawables[2], drawables[3]);

        if (a.hasValue(R.styleable.ComposeEditText_android_imeOptions)) {
            mEdit.setImeOptions(a.getInt(R.styleable.ComposeEditText_android_imeOptions, 0));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_drawablePadding)) {
            mEdit.setCompoundDrawablePadding(a.getDimensionPixelOffset(R.styleable.ComposeEditText_android_drawablePadding, 0));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_inputType)) {
            mEdit.setInputType(a.getInt(R.styleable.ComposeEditText_android_inputType, mEdit.getInputType()));
        }

        if (a.hasValue(R.styleable.ComposeEditText_android_maxLength)) {
            mEdit.setFilters(new InputFilter[]{new InputFilter.LengthFilter(a.getInt(R.styleable.ComposeEditText_android_maxLength, -1))});
        }

        //应SDK键盘要求, 需要把Tag设置到EditText上
        String tag = a.getString(R.styleable.ComposeEditText_android_tag);
        mEdit.setTag(tag);

        if (a.hasValue(R.styleable.ComposeEditText_android_digits)) {
            mEdit.setKeyListener(DigitsKeyListener.getInstance(a.getString(R.styleable.ComposeEditText_android_digits)));
        }
    }

    public void setError(boolean isError) {
        mIsError = isError;
        refreshDrawableState();
    }

    public OnErrorListener getOnErrorListener() {
        return mOnErrorListener;
    }

    public void setOnErrorListener(OnErrorListener onErrorListener) {
        mOnErrorListener = onErrorListener;
    }

    @Override
    protected int[] onCreateDrawableState(int extraSpace) {
        if (mIsError) {
            int[] state = super.onCreateDrawableState(extraSpace + 1);
            return mergeDrawableStates(state, STATE_ERROR);
        }
        return super.onCreateDrawableState(extraSpace);
    }

    @Override
    public void setOnFocusChangeListener(OnFocusChangeListener l) {
        mOuterOnFocusChangeListener = l;
    }

    public void addTextChangedListener(TextWatcher watcher) {
        mEdit.addTextChangedListener(watcher);
    }

    public void removeTextChangedListener(TextWatcher watcher) {
        mEdit.removeTextChangedListener(watcher);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (null != mPopupWindow) {
            mPopupWindow.setWidth(w);
        }
    }

    private void popupZoomIfNeeded() {
        if (!mZoom) {
            return;
        }
        ensureZoomPopup();
        if (!TextUtils.isEmpty(getText()) && mEdit.isFocused()) {
            if (mPopupWindow.isShowing()) {
                mPopupWindow.update(mEdit, 0, -mZoomView.getHeight() - mEdit.getHeight(), getWidth(), ViewGroup.LayoutParams.WRAP_CONTENT);
            } else {
                mPopupWindow.showAsDropDown(mEdit, 0, -mZoomView.getHeight() - mEdit.getHeight());
            }
        } else {
            mPopupWindow.dismiss();
        }
    }

    private void showClearIfNeeded() {
        mClear.setVisibility(!mIsClearEnable || TextUtils.isEmpty(mEdit.getText()) ? View.GONE : View.VISIBLE);
    }

    private void ensureZoomPopup() {
        if (null == mPopupWindow) {
            int width = getWidth();
            mPopupWindow = new PopupWindow(mZoomView, 0 == width ? ViewGroup.LayoutParams.MATCH_PARENT : width, ViewGroup.LayoutParams.WRAP_CONTENT);
            mPopupWindow.setTouchable(false);
        }
    }

    public void addAccessory(View accessory) {
        mAccessories.addView(accessory);
    }

    public final void setText(CharSequence text) {
        mEdit.setText(text);
    }

    public final void setText(@StringRes int textRes) {
        mEdit.setText(textRes);
    }

    public final void setHint(@StringRes int textRes) {
        mEdit.setHint(textRes);
    }

    public void setTextColor(@ColorInt int color) {
        mEdit.setTextColor(color);
    }

    public void setTextSize(float size) {
        mEdit.setTextSize(size);
    }

    public Editable getText() {
        return mEdit.getText();
    }

    public void setInputType(int type) {
        mEdit.setInputType(type);
    }

    public void setCompoundDrawablePadding(int pad) {
        mEdit.setCompoundDrawablePadding(pad);
    }

    public void setCompoundDrawablesWithIntrinsicBounds(@DrawableRes int left, @DrawableRes int top, @DrawableRes int right, @DrawableRes int bottom) {
        mEdit.setCompoundDrawablesWithIntrinsicBounds(left, top, right, bottom);
    }

    public int getInputType() {
        return mEdit.getInputType();
    }

    public void setImeOptions(int imeOptions) {
        mEdit.setImeOptions(imeOptions);
    }

    public void setOnEditorActionListener(TextView.OnEditorActionListener l) {
        mEdit.setOnEditorActionListener(l);
    }

    public EditText getEditText() {
        return mEdit;
    }

    public void setZoom(boolean isZoom) {
        mZoom = isZoom;
        popupZoomIfNeeded();
    }

    public void setClearEnable(boolean isClearEnable) {
        mIsClearEnable = isClearEnable;
        showClearIfNeeded();
    }

    public void setZoomFormatter(ZoomFormatter zoomFormatter) {
        mZoomFormatter = zoomFormatter;
    }

    public interface ZoomFormatter {
        CharSequence format(CharSequence source);
    }

    public interface OnErrorListener {
        void onError(CharSequence errorMessage);
    }

    public int getSelectionEnd() {
        return mEdit.getSelectionEnd();
    }

    public void setSelection(int index) {
        mEdit.setSelection(index);
    }

    public void setSelection(int start, int stop) {
        mEdit.setSelection(start, stop);
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        setEnableTraversal(enabled, this);
    }

    private void setEnableTraversal(boolean enabled, View view) {
        if (view != this) {
            view.setEnabled(enabled);
        }
        if (view instanceof ViewGroup) {
            ViewGroup group = (ViewGroup) view;
            for (int index = 0, count = group.getChildCount(); index < count; index++) {
                setEnableTraversal(enabled, group.getChildAt(index));
            }
        }
    }
}
