package com.jd.oa.meeting.detail

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.meeting.entity.JoinRecord
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingAttachment
import com.jd.oa.meeting.entity.MeetingNote
import com.jd.oa.meeting.entity.MeetingUser
import com.jd.oa.meeting.entity.MinutesInfo
import com.jingdong.conference.conference.model.Conference
import com.jingdong.conference.conference.model.ConferenceUserImpl
import com.jingdong.conference.conference.model.Page
import com.jingdong.conference.integrate.ServiceHubLazy
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class MeetingDetailViewModel(val repo: MeetingDetailDataSource) : ViewModel() {

    companion object {
        const val TAG = "MeetingDetailViewModel"
    }

    val meeting = MutableLiveData<Meeting>()

    val participants: MutableLiveData<MutableList<MeetingUser>> = MutableLiveData<MutableList<MeetingUser>>()
    val participantsDevice: MutableLiveData<List<MeetingUser>> = MutableLiveData<List<MeetingUser>>()

    val attachments: MutableLiveData<List<MeetingAttachment>> = MutableLiveData()

    val notes: MutableLiveData<List<MeetingNote>> = MutableLiveData()

    val joinRecords: MutableLiveData<List<JoinRecord>> = MutableLiveData()

    val handler = CoroutineExceptionHandler { _, exception ->
        Log.e("MeetingDetailViewModel", "Caught $exception", exception)
    }

    fun getMeetingDetail(meetingId: String, meetingSelector: Int, callback: () -> Unit?) {
        viewModelScope.launch(handler) {
            kotlin.runCatching {
                repo.getMeetingInfo(meetingId, meetingSelector)
            }.onSuccess {
                meeting.value = it
            }.onFailure {
                callback.invoke()
            }
        }
    }

    fun getAllParticipants(meetingId: String, meetingSelector: Int) {
        viewModelScope.launch (handler){
            kotlin.runCatching {
                repo.getAllParticipants(meetingId, meetingSelector)
            }.onSuccess {
                participants.value = it
            }
        }
    }

    fun getAllDeviceParticipants(meetingId: String, meetingSelector: Int) {
        viewModelScope.launch (handler){
            kotlin.runCatching {
                repo.getAllDeviceParticipants(meetingId, meetingSelector)
            }.onSuccess {
                participantsDevice.value = it
            }
        }
    }

    fun getAttachments(scheduleId: String, start: Long, callback: () -> Unit?) {
        viewModelScope.launch(handler) {
            kotlin.runCatching {
                repo.getAppointmentAttachment(scheduleId, start)
            }.onSuccess {
                attachments.value = it
            }.onFailure {
                callback.invoke()
            }
        }
    }

    fun getNotes(meetingId: String, meetingSelector: Int, callback: () -> Unit?) {
        viewModelScope.launch(handler) {
            kotlin.runCatching {
                repo.getNotes(meetingId, meetingSelector)
            }.onSuccess {
                notes.value = it
            }.onFailure {
                callback.invoke()
            }
        }
    }

    fun getJoinRecords(meetingId: String, selector: Int) {
        viewModelScope.launch(handler) {
            kotlin.runCatching {
                val records = repo.getJoinRecords(meetingId, selector, 1, 100)  //产品说不分页，加载100条
                return@runCatching records.filter { it.joinTime != null || it.leftTime != null }
            }.onSuccess {
                joinRecords.value = it
            }
        }
    }

    fun shareNote(minutesInfo: MinutesInfo) {
        viewModelScope.launch(handler) {
            kotlin.runCatching {

            }
        }
    }

    fun shareMeeting(meeting: Meeting?) {
        meeting?.apply {
            val conference = Conference(
                    meetingId!!,
                    meetingCode!!,
                    null, null, null, null,
                    subject,
                    password,
                    null,
                    realMeetingStatus!!,
                    totalNum!!,
                    startTime ?: realStartTime,
                    endTime ?: realEndTime,
                    hoster?.displayName,//这里用remindTime 传递的主持人 displayName
                    null, false,
                    duration,
                    null,
                    scheduleStartTime, null, null, null,
                    meetingSelector!!
            ).apply {
                host = ConferenceUserImpl(
                    meeting.hoster?.pin,
                    meeting.hoster?.teamId,
                    meeting.hoster?.appId,
                ).apply {
                    nickname = meeting.hoster?.displayName
                }
            }
            GlobalScope.launch(Dispatchers.Main) {
                ServiceHubLazy.externalService?.onShare(conference, Page.DETAIL)
            }
        }
    }

    class Factory(val repo: MeetingDetailDataSource) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = MeetingDetailViewModel(repo) as T
    }
}