package com.jd.oa.meeting.detail

import androidx.lifecycle.MutableLiveData
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.meeting.entity.JoinRecord
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingAttachment
import com.jd.oa.meeting.entity.MeetingNote
import com.jd.oa.meeting.entity.MeetingUser
import com.jd.oa.network.httpmanager.CancelTag
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

class MeetingDetailRepository(val remoteDetailData: MeetingDetailDataSource) : MeetingDetailDataSource {

    companion object{
        const val TAG = "MeetingDetailRepository"
    }

    override suspend fun getMeetingInfo(meetingId: String, meetingSelector: Int): Meeting {
        return remoteDetailData.getMeetingInfo(meetingId, meetingSelector)
    }

    override suspend fun getAllParticipants(meetingId: String, meetingSelector: Int): MutableList<MeetingUser> {
        return remoteDetailData.getAllParticipants(meetingId, meetingSelector)
    }

    override suspend fun getJoinRecords(meetingId: String, selector: Int, pageNo: Int, pageSize: Int): List<JoinRecord> {
        return remoteDetailData.getJoinRecords(meetingId, selector, pageNo, pageSize)
    }

    override suspend fun getAllDeviceParticipants(meetingId: String, meetingSelector: Int): MutableList<MeetingUser> {
        return remoteDetailData.getAllDeviceParticipants(meetingId, meetingSelector)
    }

    override suspend fun getAppointmentAttachment(
        scheduleId: String,
        start: Long,
    ): List<MeetingAttachment> {
        return remoteDetailData.getAppointmentAttachment(scheduleId, start)
    }

    override suspend fun getNotes(meetingId: String, meetingSelector: Int): List<MeetingNote> {
        return remoteDetailData.getNotes(meetingId, meetingSelector)
    }
}