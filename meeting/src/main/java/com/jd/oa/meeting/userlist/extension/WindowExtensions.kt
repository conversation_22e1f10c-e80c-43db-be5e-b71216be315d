package com.jd.oa.meeting.userlist.extension

import android.view.Window
import android.view.WindowInsets
import kotlin.math.min

/**
 * 监听键盘弹出，保证Activity的softInputMode为adjustResize
 */
inline fun Window.onSoftInputToggle(crossinline callback: (toggle: Boolean, insets: WindowInsets, topInsetMin: Int, bottomInsetMin: Int) -> Boolean) {
    var systemWindowInsetsTopMin = Int.MAX_VALUE
    var systemWindowInsetsBottomMin = Int.MAX_VALUE

    decorView.setOnApplyWindowInsetsListener { v, insets ->
        if (!insets.hasSystemWindowInsets()) {
            if (callback(false, insets, 0, 0)) insets.consumeSystemWindowInsets() else insets
        } else {
            val insetsTop = insets.systemWindowInsetTop
            val insetsBottom = insets.systemWindowInsetBottom

            systemWindowInsetsTopMin = min(systemWindowInsetsTopMin, insetsTop)

            systemWindowInsetsBottomMin = min(systemWindowInsetsBottomMin, insetsBottom)

            if (callback(
                    insetsBottom > systemWindowInsetsBottomMin,
                    insets,
                    systemWindowInsetsTopMin,
                    systemWindowInsetsBottomMin
                )
            ) insets.consumeSystemWindowInsets() else insets
        }
    }
}