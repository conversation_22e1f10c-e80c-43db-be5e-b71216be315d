package com.jd.oa.meeting.userlist

import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.ActionBar
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.meeting.R
import com.jd.oa.meeting.detail.MeetingDetailRemoteDataSource
import com.jd.oa.meeting.detail.MeetingDetailRepository
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.userlist.extension.debounce
import com.jd.oa.meeting.userlist.extension.onSoftInputToggle
import com.jd.oa.meeting.view.ComposeEditText

class MeetingUsersActivity : AppCompatActivity(), View.OnClickListener {
    private val toolbar: Toolbar by lazy { findViewById(R.id.toolbar) }
    private val cancel: Button by lazy { findViewById(R.id.cancel) }
    private val search: ComposeEditText by lazy { findViewById(R.id.search) }
    private val list: RecyclerView by lazy { findViewById(R.id.list) }
    private val noResult: TextView by lazy { findViewById(R.id.noResult) }
    private val title: TextView by lazy { findViewById(R.id.title) }
    private lateinit var viewModel: MeetingUserViewModel
    private lateinit var meeting: Meeting
    private var isDevices: Boolean = false
    //TODO appid硬编码
    private val deviceAppId1 = "sip.device.ee"//咚咚会议 设备
    private val deviceAppId2 = "room.ee"//咚咚会议 会议室
    private val deviceAppId3 = "open.mail.out"//咚咚会议 设备
    private val standaloneApp = "standalone.app" //独立端用户
    private val clientType2 = "2"//京me会议 room用户
    private val clientType3 = "3"//京me会议 设备用户
    private val adapter by lazy {
        MeetingUserListAdapter(meeting) { holder, _ ->
            if (meeting.meetingSelector == 2 && (holder.data().userInfo?.clientType == clientType2 || holder.data().clientType == clientType3)) {
                return@MeetingUserListAdapter
            }else if (holder.data().userInfo?.appId == deviceAppId1 ||
                holder.data().userInfo?.appId == deviceAppId2 ||
                holder.data().userInfo?.appId == deviceAppId3 ||
                holder.data().userInfo?.appId == standaloneApp) {
                return@MeetingUserListAdapter
            }
            viewModel.showUser(holder.itemView.context, holder.data())
        }
    }

    val repository: MeetingDetailRepository by lazy {
        val remoteDataSource = MeetingDetailRemoteDataSource()
        MeetingDetailRepository(remoteDataSource)
    }

    val updateRunnable = Runnable {
        checkVisibleListItem()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.meeting_activity_conference_users)

        val bundle = intent.getBundleExtra("extra")
        bundle?.apply {
            meeting = getParcelable<Meeting>("meeting")!!
            isDevices = getBoolean("isDevices")
        }
    }

    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
        setSupportActionBar(toolbar)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.meeting_ic_close)
        supportActionBar?.displayOptions = ActionBar.DISPLAY_HOME_AS_UP

        viewModel = ViewModelProvider(this, MeetingUserViewModel.Factory(repository, isDevices)).get(MeetingUserViewModel::class.java)

        window.onSoftInputToggle { toggle, _, _, _ ->
//            requireVersion(Build.VERSION_CODES.LOLLIPOP_MR1) {
//                TransitionManager.beginDelayedTransition(
//                        binding.root,
//                        defaultTransition.clone().apply {
//                            excludeChildren(R.id.list, true)
//                        })
//            }
            if (!toggle) {
                //键盘关闭
                cancel.isVisible = false
                search.text.clear()
                viewModel.searchMode.updateValue(false)
            } else {
                //键盘弹出
                cancel.isVisible = true
                viewModel.searchMode.updateValue(true)
            }
            false
        }

        list.itemAnimator = null

        list.adapter = adapter
        viewModel.conferenceUsersState.observe(this) { it ->
            it.onLoading {
//                if (!viewModel.loadSuccess) showProgress(false)
            }.onFailure {
//                dismissProgress()
//                it.handle(this)
                if (!viewModel.loadSuccess) finish()
            }.onSuccess {
//                dismissProgress()
            }
        }

        viewModel.conferenceUsers.observe(this) {
            search.setHint(R.string.meeting_users_hint_search)
            title.text = getString(
                    R.string.meeting_users_title_with_user_count,
                    viewModel.conferenceUsersContainer.size
            )
            adapter.setAllData(it)
            noResult.isVisible =
                    it.isEmpty() && viewModel.searchMode.value!! && !viewModel.keyword.value.isNullOrEmpty()
            debounce(400L, updateRunnable)
        }

        viewModel.conferenceDevices.observe(this) {
            search.setHint(R.string.meeting_devices_hint_search)
            title.text = getString(
                    R.string.meeting_users_title_with_device_count,
                    viewModel.conferenceDevicesContainer.size
            )
            adapter.setAllData(it)
            noResult.isVisible =
                    it.isEmpty() && viewModel.searchMode.value!! && !viewModel.keyword.value.isNullOrEmpty()
            debounce(400L, updateRunnable)
        }

        search.editText.doAfterTextChanged {
            viewModel.keyword.updateValue(it.toString())
        }

        list.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    RecyclerView.SCROLL_STATE_IDLE -> {
                        checkVisibleListItem()
                    }
                }
            }
        })

        viewModel.userListUpdateInfo.observe(this) {
            adapter.notifyItemChanged(it)
        }
        viewModel.load(meeting.meetingId, meeting.meetingSelector!!)
    }

    private fun checkVisibleListItem() {
        val manager = list.layoutManager as LinearLayoutManager
        val first = manager.findFirstVisibleItemPosition()
        val last = manager.findLastVisibleItemPosition()
        if (first != RecyclerView.NO_POSITION && last != RecyclerView.NO_POSITION && last >= first) {
            for (index in first..last) {
                val user = adapter[index]
                if (!user.hasDetail()) {
                    viewModel.updateUser(user, index)
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> finish()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onClick(v: View) {
        when (v) {
            cancel -> {
                (getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager)?.hideSoftInputFromWindow(
                        v.windowToken,
                        0
                )
            }
        }
    }
}