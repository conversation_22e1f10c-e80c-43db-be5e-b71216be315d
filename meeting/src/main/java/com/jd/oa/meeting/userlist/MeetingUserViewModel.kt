package com.jd.oa.meeting.userlist

import android.content.Context
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.jd.oa.AppBase
import com.jd.oa.im.listener.Callback
import com.jd.oa.meeting.detail.MeetingDetailDataSource
import com.jd.oa.meeting.entity.MeetingUser
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import kotlinx.coroutines.launch

class MeetingUserViewModel(val repo: MeetingDetailDataSource, val isDevices: Boolean) :
    ViewModel() {

//    val isDevices: Boolean? = savedStateHandle[Routers.Conference.Extras.EXTRA_EXTRAS]

    val conferenceUsersContainer = mutableListOf<MeetingUser>()

    val conferenceDevicesContainer = mutableListOf<MeetingUser>()

    val conferenceUsersState = MutableLiveData<LoadState<List<MeetingUser>>>()

    val conferenceDevicesState = MutableLiveData<LoadState<List<MeetingUser>>>()

    val keyword = MutableLiveData<String?>()

    val searchMode = MutableLiveData(false)

    val userListUpdateInfo = MutableLiveData<Int>()

//    val repo: MeetingDetailDataSource

    val conferenceUsers = MediatorLiveData<List<MeetingUser>>().apply {
        if (isDevices != true) {
            addSource(keyword) { updateValue(filterParticipants(conferenceUsersContainer)) }
            addSource(searchMode) { updateValue(filterParticipants(conferenceUsersContainer)) }
            addSource(conferenceUsersState) {
                updateValue(
                    filterParticipants(
                        conferenceUsersContainer
                    )
                )
            }
        }
    }

    val conferenceDevices = MediatorLiveData<List<MeetingUser>>().apply {
        if (isDevices == true) {
            addSource(keyword) { updateValue(filterParticipants(conferenceDevicesContainer)) }
            addSource(searchMode) { updateValue(filterParticipants(conferenceDevicesContainer)) }
            addSource(conferenceDevicesState) {
                updateValue(
                    filterParticipants(
                        conferenceDevicesContainer
                    )
                )
            }
        }
    }

    var loadSuccess = false
        private set

    private fun filterParticipants(conferenceUsers: List<MeetingUser>?): List<MeetingUser> {
        return conferenceUsers?.filter {
            when {
                keyword.value.isNullOrEmpty() -> true
                else -> {
                    val erp = it.pin ?: it.userInfo?.pin ?: it.nickname
                    it.displayName.contains(
                        keyword.value ?: "",
                        true
                    ) || erp?.contains(keyword.value ?: "", true) == true
                }
            }
        }.orEmpty()
    }

    fun load(meetingId: String, meetingSelector: Int) {
        if (isDevices == true) {
            loadDevices(meetingId, meetingSelector)
        } else {
            loadUser(meetingId, meetingSelector)
        }
    }

    private fun loadUser(meetingId: String, meetingSelector: Int) {
        viewModelScope.launch {
            kotlin.runCatching {
                conferenceUsersState.updateValue(LoadState.loading())
                repo.getAllParticipants(meetingId, meetingSelector)
//                conferenceService?.conferenceUsers(conference)
            }.onFailure {
                conferenceUsersState.updateValue(LoadState.failure(it))
            }.onSuccess {
                if (null != it) {
                    conferenceUsersContainer.clear()
                    conferenceUsersContainer += it
                }
                conferenceUsersState.updateValue(LoadState.success(conferenceUsersContainer))
                loadSuccess = true
            }
        }
    }

    private fun loadDevices(meetingId: String, meetingSelector: Int) {
        viewModelScope.launch {
            kotlin.runCatching {
                conferenceDevicesState.updateValue(LoadState.loading())
                repo.getAllDeviceParticipants(meetingId, meetingSelector)
//                conferenceService?.conferenceDevices(conference, null)
            }.onFailure {
                conferenceDevicesState.updateValue(LoadState.failure(it))
            }.onSuccess {
                if (null != it) {
                    conferenceDevicesContainer.clear()
                    conferenceDevicesContainer += it
                }
                conferenceDevicesState.updateValue(LoadState.success(conferenceDevicesContainer))
                loadSuccess = true
            }
        }
    }

    fun showUser(context: Context, user: MeetingUser) {
        viewModelScope.launch {
            kotlin.runCatching {
                if (!isDevices) {
                    if (user.userInfo?.appId.isNullOrEmpty()) {
                        AppJoint.service(ImDdService::class.java)
                            ?.showContactDetailInfo(context, user.userInfo?.pin)
                    } else {
                        AppJoint.service(ImDdService::class.java)?.showContactDetailInfo(
                            context,
                            user.userInfo?.appId,
                            user.userInfo?.pin
                        )
                    }
                }
            }
        }
    }

    val imDdService: ImDdService = AppJoint.service(ImDdService::class.java)
    fun updateUser(user: MeetingUser, position: Int) {
        viewModelScope.launch {
            kotlin.runCatching {
//                conferenceService?.updateUserInfo(user)
                imDdService.getContactInfo(
                    user.userInfo?.appId,
                    user.userInfo?.pin,
                    object : Callback<MemberEntityJd> {
                        override fun onSuccess(bean: MemberEntityJd?) {
                            if (bean?.avatar?.isNotEmpty() == true) {
                                user.portrait = bean.avatar
                            }
                            if (bean?.name?.isNotEmpty() == true) {
                                user.nickname = bean.name.toString()
                            }
                            user.dept = bean?.department
                            user.position = bean?.position
                        }

                        override fun onFail() {
                        }
                    })
                userListUpdateInfo.updateValue(position)
            }
        }
    }

    class Factory(val repo: MeetingDetailDataSource, val isDevices: Boolean) :
        ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T =
            MeetingUserViewModel(repo, isDevices) as T
    }
}