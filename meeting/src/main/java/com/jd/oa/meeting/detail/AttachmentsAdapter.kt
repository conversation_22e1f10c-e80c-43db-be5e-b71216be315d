package com.jd.oa.meeting.detail

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.fragment.utils.FileType
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.AttachmentType
import com.jd.oa.meeting.entity.FileAttachment
import com.jd.oa.meeting.entity.JoyspaceDoc
import com.jd.oa.meeting.entity.MeetingAttachment
import com.jd.oa.utils.ImageLoader
import com.jd.oa.utils.setPaddingTop

class AttachmentsAdapter(
    val context: Context,
    val data: MutableList<MeetingAttachment>,
    val onItemClickListener: (MeetingAttachment) -> Unit
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val TYPE_HEADER = 1
        const val TYPE_ITEM = 2
        const val TYPE_FOOTER = 3

        const val STATE_EXPAND = 1
        const val STATE_COLLAPSE = 2

        const val EXPAND_NUM = 100

        const val HEADER = 1
        const val HEADER_AND_FOOTER = 2
    }

    private var state: Int = STATE_COLLAPSE

    override fun getItemViewType(position: Int): Int {
        if (position == 0) return TYPE_HEADER
        if (data.size <= EXPAND_NUM) return TYPE_ITEM
        if (position == itemCount - 1) return TYPE_FOOTER
        return TYPE_ITEM
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TYPE_HEADER) {
            AttachmentHeaderViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_item_attachment_header, parent, false))
        } else if (viewType == TYPE_FOOTER) {
            ExpandViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_item_attachment_footer, parent, false)) {
                this.expand()
            }
        } else {
            val holder = AttachmentViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_item_attachment, parent, false))
            holder.itemView.setOnClickListener {
                val position = holder.bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClickListener.invoke(data[position - 1])
                }
            }
            return holder
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is AttachmentViewHolder) {
            val attachment = data[position - 1]
            if (attachment.type == AttachmentType.FILE) {
                val file = attachment as FileAttachment
                val type = FileType.getTypeInt(file.fileName)
                if (FileType.isImage(type)) {
                    ImageLoader.load(context, holder.icon, file.iconUrl)
                } else {
                    holder.icon.setImageResource(fileIconDrawable(type))
                }
            } else if (attachment.type == AttachmentType.JOYSPACE_DOC) {
                val document = attachment as JoyspaceDoc
                ImageLoader.load(context, holder.icon, document.iconUrl)
            }

            holder.title.text = attachment.title

            if (data.size <= EXPAND_NUM && position == data.size) {
                holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    holder.itemView.paddingTop,
                    holder.itemView.paddingRight,
                    context.resources.getDimensionPixelOffset(R.dimen.meeting_detail_expand_padding)
                )
            } else {
                holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    holder.itemView.paddingTop,
                    holder.itemView.paddingRight,
                    0
                )
            }
        } else if (holder is ExpandViewHolder) {
           if (state == STATE_COLLAPSE) {
               holder.expand.visibility = View.VISIBLE
               holder.container.setPaddingTop(R.dimen.meeting_detail_collapse_padding)
           } else if (state == STATE_EXPAND) {
               holder.expand.visibility = View.GONE
               holder.container.setPaddingTop(R.dimen.meeting_detail_expand_padding)
           }
        }
    }

    override fun getItemCount(): Int {
        if (data.isEmpty()) return 0
        if (data.size <= EXPAND_NUM) return data.size + HEADER
        if (state == STATE_COLLAPSE) return EXPAND_NUM + HEADER_AND_FOOTER
        if (state == STATE_EXPAND) return data.size + HEADER_AND_FOOTER
        return 0
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refresh(data: List<MeetingAttachment>) {
        this.data.clear()
        this.data.addAll(data)
//        this.notifyDataSetChanged()
    }

    fun add(data: List<MeetingAttachment>) {
        val size = this.data.size
        this.data.addAll(data)
        this.notifyItemRangeInserted(size, data.size)
    }

    fun expand() {
        if (state == STATE_EXPAND) return
        val count = itemCount
        this.notifyItemRangeInserted(count, data.size + HEADER_AND_FOOTER - (count))
        this.state = STATE_EXPAND
    }

    class AttachmentHeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    }

    class AttachmentViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val icon: ImageView by lazy { itemView.findViewById(R.id.iv_icon) }
        val title: TextView by lazy { itemView.findViewById(R.id.tv_attach_name) }
    }

    class ExpandViewHolder(itemView: View, onExpandClickListener: View.OnClickListener): RecyclerView.ViewHolder(itemView) {

        val container: View by lazy { itemView.findViewById(R.id.layout_container) }
        val expand: View by lazy { itemView.findViewById(R.id.layout_expand) }

        init {
            expand.setOnClickListener(onExpandClickListener)
        }
    }
}

@DrawableRes
private fun fileIconDrawable(type: Int): Int {
    return if (FileType.isImage(type) || FileType.isGif(type) || FileType.isFlash(type) || FileType.isJpg(type)) {
        R.drawable.file_imgs
    } else if (FileType.isAudio(type) || FileType.isPlayList(type)) {
        R.drawable.file_musics
    } else if (FileType.isPowerPoint(type)) {
        R.drawable.file_ppt
    } else if (FileType.isExcel(type)) {
        R.drawable.file_excel
    } else if (FileType.isWord(type) || FileType.isTxt(type)) {
        R.drawable.file_word
    } else if (type == FileType.TYPE_PDF) {
        R.drawable.file_pdf
    } else if (FileType.isVideo(type)) {
        R.drawable.file_videos
    } else if (FileType.isZip(type)) {
        R.drawable.file_compression
    } else if (type == 0) {
        R.drawable.file_unknown
    } else {
        R.drawable.file_files
    }
}