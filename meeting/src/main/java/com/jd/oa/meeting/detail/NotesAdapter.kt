package com.jd.oa.meeting.detail

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.MeetingNote
import com.jd.oa.meeting.entity.MinutesInfo
import com.jd.oa.meeting.formatDuration
import com.jd.oa.meeting.formatStartAndEnd
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.ImageLoader
import java.util.Calendar

/**
 * 会议详情_慧记部分
 */
class NotesAdapter(
    val context: Context,
    val data: MutableList<MeetingNote>,
    val onItemClick: (MeetingNote) -> Unit,
    val onMinuteShare: (MinutesInfo) -> Unit
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        const val TYPE_HEADER = 1
        const val TYPE_ITEM = 2
        const val STATUS_RECORDING = 1//录制中or生成中
        const val STATUS_FINISH = 2//完成
        const val STATUS_FAILED = 3//失败
        const val STAGE_FINISH = "1"//minute慧记状态 完成
        const val STAGE_RECORDING = "2"//minute慧记状态 生成中
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val holder = if (viewType == TYPE_HEADER) {
            val noteTitleHolder = NoteTitleViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_item_note_header, parent, false))
            noteTitleHolder.mNoteTitle.text = if (TenantConfigBiz.isJoyMinutes())
                context.getString(R.string.meeting_records_videos_notes)
            else
                context.getString(R.string.meeting_records_videos)
            noteTitleHolder
        } else {
            val noteViewHolder =  NoteViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_item_note, parent, false), getItemViewType(viewType))
            noteViewHolder.mNoteShare.setOnClickListener {
                val position = noteViewHolder.bindingAdapterPosition
                if (position == RecyclerView.NO_POSITION) return@setOnClickListener
                val minutesInfo = data[noteViewHolder.bindingAdapterPosition - 1].minutesInfo ?: return@setOnClickListener
                onMinuteShare.invoke(minutesInfo)
            }
            noteViewHolder.itemView.setOnClickListener {
                val position = noteViewHolder.bindingAdapterPosition
                if (position == RecyclerView.NO_POSITION) return@setOnClickListener
                val note = data[noteViewHolder.bindingAdapterPosition - 1]
                onItemClick.invoke(note)
            }
            noteViewHolder
        }
        return holder
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) TYPE_HEADER else TYPE_ITEM
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (position == 0) return
        val viewHolder = holder as NoteViewHolder
        val note = data[position - 1]
        val isNote = note.minutesInfo != null
        viewHolder.mNoteName.text = note.minutesInfo?.title ?: note.fileName
        val name = note.creator?.nickname ?: note.creator?.pin ?: "-"
        viewHolder.mNoteOwner.text = context.getString(R.string.meeting_video_creator, name)

        if (note.minutesInfo?.image != null) {
            ImageLoader.load(context, viewHolder.cover, note.minutesInfo.image)
        }

        if (position == itemCount - 1) {
            viewHolder.itemView.setPadding(viewHolder.itemView.paddingLeft, viewHolder.itemView.paddingTop, viewHolder.itemView.paddingRight, context.resources.getDimensionPixelOffset(R.dimen.meeting_detail_expand_padding))
        } else {
            viewHolder.itemView.setPadding(viewHolder.itemView.paddingLeft, viewHolder.itemView.paddingTop, viewHolder.itemView.paddingRight,0)
        }
        if (!isNote) {//录屏
            viewHolder.asr.visibility = View.INVISIBLE
            //            viewHolder.mNoteShare.visibility = View.INVISIBLE
            viewHolder.timeLayout.visibility = View.INVISIBLE
            when (note.recordStatus) {
                STATUS_RECORDING -> {//录制中
                    viewHolder.generating.visibility = View.VISIBLE
                    viewHolder.generating.text = context.getString(R.string.meeting_video_recording)
                }
                STATUS_FINISH -> {//完成
                    viewHolder.icon.visibility = View.VISIBLE
                    viewHolder.tvIcon.visibility = View.INVISIBLE
                    viewHolder.cover.setImageResource(R.drawable.meeting_item_bg_reserve)
                }
                STATUS_FAILED -> {//失败
                    //viewHolder.cover.setImageResource(R.drawable.meeting_note_failed)
                    viewHolder.generating.visibility = View.VISIBLE
                    viewHolder.generating.text = context.getString(R.string.meeting_video_failed)
                }
                else -> {}
            }
        } else {//慧记
            viewHolder.asr.visibility = View.VISIBLE
            //            viewHolder.mNoteShare.visibility = View.VISIBLE
            if (!note.minutesInfo?.stage.isNullOrEmpty()) {
                if (note.minutesInfo?.stage == STAGE_FINISH) {//完成
                    viewHolder.cover.setImageResource(R.drawable.meeting_list_history)
                    viewHolder.icon.visibility = View.INVISIBLE
                    viewHolder.tvIcon.visibility = View.VISIBLE
                } else if (note.minutesInfo?.stage == STAGE_RECORDING) {//生成中
                    viewHolder.generating.visibility = View.VISIBLE
                    viewHolder.generating.text = context.getString(R.string.meeting_video_generating)
                } else {
                    if (note.asrStatus == STATUS_FAILED) {
                        viewHolder.generating.visibility = View.VISIBLE
                        viewHolder.generating.text = context.getString(R.string.meeting_video_failed)
                        //viewHolder.cover.setImageResource(R.drawable.meeting_note_failed)
                    }
                }
            } else {
                when (note.asrStatus) {
                    STATUS_RECORDING -> {//生成中
                        viewHolder.generating.visibility = View.VISIBLE
                        viewHolder.generating.text = context.getString(R.string.meeting_video_generating)
                        viewHolder.timeLayout.visibility = View.INVISIBLE
                    }
                    STATUS_FINISH -> {//完成
                        viewHolder.cover.setImageResource(R.drawable.meeting_list_history)
                        viewHolder.icon.visibility = View.INVISIBLE
                        viewHolder.tvIcon.visibility = View.VISIBLE
                        viewHolder.timeLayout.visibility = View.VISIBLE
                    }
                    STATUS_FAILED -> {//失败
                        //viewHolder.cover.setImageResource(R.drawable.meeting_note_failed)
                        viewHolder.generating.visibility = View.VISIBLE
                        viewHolder.generating.text = context.getString(R.string.meeting_video_failed)
                        viewHolder.timeLayout.visibility = View.INVISIBLE
                    }
                    else -> {}
                }
            }

            if (note.minutesInfo?.startTime != null && note.minutesInfo.endTime != null) {
                viewHolder.timeLayout.visibility = View.VISIBLE
                val start = Calendar.getInstance().apply {
                    timeInMillis = note.minutesInfo.startTime
                }
                val end = Calendar.getInstance().apply {
                    timeInMillis = note.minutesInfo.endTime
                }
                viewHolder.time.text = formatStartAndEnd(context, start, end)
                viewHolder.duration.text = formatDuration(context, note.minutesInfo.endTime - note.minutesInfo.startTime)
            } else {
                viewHolder.timeLayout.visibility = View.INVISIBLE
            }

            viewHolder.mNoteShare.visibility = if (note.minutesInfo?.permissions?.contains(MinutesInfo.PERMISSION_SHARE) == true) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    override fun getItemCount(): Int {
        if (data.isEmpty()) return 0
        return data.size + 1
    }

    fun refresh(it: List<MeetingNote>?) {
        data.clear()
        data.addAll(it ?: emptyList())
//        notifyDataSetChanged()
    }

    class NoteTitleViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val mNoteTitle: TextView by lazy { itemView.findViewById(R.id.tv_note_title) }
    }

    inner class NoteViewHolder(itemView: View, itemType: Int) : RecyclerView.ViewHolder(itemView) {
        val mNoteName: TextView by lazy { itemView.findViewById(R.id.tv_note_name) }
        val mNoteOwner: TextView by lazy { itemView.findViewById(R.id.tv_note_owner) }
        val mNoteShare: IconFontView by lazy { itemView.findViewById(R.id.note_share) }
        val asr: ImageView by lazy { itemView.findViewById(R.id.iv_asr) }
        val cover: ImageView by lazy { itemView.findViewById(R.id.iv_cover) }
        val generating: TextView by lazy { itemView.findViewById(R.id.tv_generating) }
        val icon: ImageView by lazy { itemView.findViewById(R.id.icon) }
        val tvIcon: TextView by lazy { itemView.findViewById(R.id.tv_icon) }
        val time: TextView by lazy { itemView.findViewById(R.id.tv_time) }
        val duration: TextView by lazy { itemView.findViewById(R.id.tv_duration) }
        val timeLayout: ViewGroup by lazy { itemView.findViewById(R.id.layout_time) }
    }
}