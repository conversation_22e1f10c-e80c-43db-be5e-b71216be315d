package com.jd.oa.meeting.userlist

import android.view.ViewGroup
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingUser

class MeetingUserListAdapter(
    private val conference: Meeting,
    itemClick: (MeetingUserListViewHolder, Int) -> Unit
) : AbstractBaseRecyclerAdapter<MeetingUser, MeetingUserListViewHolder>(itemClick) {
    init {
        setHasStableIds(true)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): MeetingUserListViewHolder {
        return MeetingUserListViewHolder(parent, conference)
    }

    override fun getItemId(position: Int): Long {
        val data = this[position]
        return "${data.pin ?: data.userInfo?.pin}${data.nickname ?: data.userInfo?.nickname}${data.participantId}$position".hashCode().toLong()
    }

    override fun areContentsTheSame(oldItem: MeetingUser, newItem: MeetingUser): Boolean =
        oldItem.nickname == newItem.nickname && oldItem.portrait == newItem.portrait

    override fun areItemsTheSame(oldItem: MeetingUser, newItem: MeetingUser): Boolean =
        oldItem.sameUser(newItem)
}