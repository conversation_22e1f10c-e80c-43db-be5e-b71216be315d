package com.jd.oa.meeting.detail

import com.jd.oa.meeting.entity.*

interface MeetingDetailDataSource {

    suspend fun getMeetingInfo(meetingId: String, meetingSelector: Int): Meeting

    suspend fun getAllParticipants(meetingId: String, meetingSelector: Int): MutableList<MeetingUser>

    suspend fun getJoinRecords(meetingId: String, selector: Int, pageNo: Int, pageSize: Int): List<JoinRecord>

    suspend fun getAllDeviceParticipants(meetingId: String, meetingSelector: Int): MutableList<MeetingUser>
    suspend fun getAppointmentAttachment(scheduleId: String, start: Long): List<MeetingAttachment>
    suspend fun getNotes(meetingId: String, meetingSelector: Int): List<MeetingNote>
}