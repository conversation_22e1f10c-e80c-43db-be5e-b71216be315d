package com.jd.oa.meeting.entity

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class Setting(
    /**
     * 是否是周期会议
     */
    val periodic: Boolean,

    /**
     * 重复频率 daily-每天；workday-每个工作日；weekly-每周；fortnightly-每两周；monthly-每月
     */
    val repeatFrequency: String?,

    /**
     * 重复结束时间，utc时间，格式：yyyy-MM-dd'T'HH:mm:ss'Z'
     */
    val repeatEndTime: String?,

    /**
     * 重复次数
     */
    val repeatTimes: Int?,

    /**
     * 是否添加到日历 true-是 false-否
     */
    val addToCalendar: Boolean,

    /**
     * 是否允许提前进入 true-是 false-否
     */
    val allowInAdvance: Boolean,

    /**
     * 进入自动静音 true-是 false-否
     */
    val autoMuteJoin: Boolean,

    /**
     * 允许参会者取消静音 true-是 false-否
     */
    val allowUnmuteSelf: Boolean,

    /**
     * 全部静音 true-是 false-否
     */
    var muteAll: Boolean,

    /**
     * 全部禁言
     */
    val silentAll: Boolean,

    /**
     * 是否开启共享水印 true-是 false-否
     */
    val allowShareWatermark: Boolean,

    /**
     * 是否开启屏幕共享 true-是 false-否
     */
    val allowShareScreen: Boolean,

    /**
    * joyday主日程ID
    */
    val scheduleId: String? = null,

    /**
    * joyday日历ID
    */
    val calendarId: String? = null,

    /**
    * 会议锁状态 -1-解锁会议室 1-锁定会议室
    */
    val lockState: Int?
) : Parcelable