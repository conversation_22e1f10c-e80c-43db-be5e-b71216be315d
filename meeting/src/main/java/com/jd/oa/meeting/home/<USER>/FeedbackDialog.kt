package com.jd.oa.meeting.home.feedback

import android.app.Dialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.format.DateFormat
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import com.jd.oa.AppBase
import com.jd.oa.abilities.api.OpennessApi
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.abtest.ABTestManager
import com.jd.oa.meeting.MeetingJDMAConstant
import com.jd.oa.meeting.R
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.JdMeetingService
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.JDMAUtils
import com.jingdong.conference.core.extension.nullOrNot
import com.jingdong.sdk.talos.LogX
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import java.util.Calendar
import kotlin.coroutines.resumeWithException

class FeedbackDialog(feedbackItems: ArrayList<FeedbackItemData<*>>): DialogFragment(R.layout.meeting_dialog_feedback) {

    companion object {
        private const val TAG = "FeedbackDialog"
        private const val ARG_ITEMS = "args.feedbackItems"
        private const val ERROR_GROUP_NOT_EXIST = 1
        private const val ERROR_GROUP_NOT_MEMBER = 2
    }

    private val mIvClose: TextView? by lazy { view?.findViewById(R.id.tv_close) }
    private val mLayoutContainer: ViewGroup? by lazy { view?.findViewById(R.id.layout_container) }
    private val mTvUploadLog: TextView? by lazy { view?.findViewById(R.id.tv_upload_log) }
    private val tvTitle: TextView? by lazy { view?.findViewById(R.id.tv_title) }

    private var mFeedbackItems: ArrayList<FeedbackItemData<*>>? = null

    private val imDdService: ImDdService by lazy { AppJoint.service(ImDdService::class.java) }
    private val jdMeetingService: JdMeetingService by lazy { AppJoint.service(JdMeetingService::class.java) }

    init {
        arguments = Bundle().apply {
            putParcelableArrayList(ARG_ITEMS, feedbackItems)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        @Suppress("DEPRECATION")
        mFeedbackItems = arguments?.getParcelableArrayList(ARG_ITEMS) ?: arrayListOf()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.apply {
            setBackgroundDrawableResource(R.drawable.meeting_bg_feedback_dialog)
        }
        return dialog
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mLayoutContainer?.removeAllViews()
        mFeedbackItems?.forEach {
            val itemView = FeedbackItem(requireContext()).apply {
                onLinkClickListener = ::onLinkClick
                onJoinGroupClickListener = ::onJoinGroupClick
                onTextClickListener = ::onTextClick
            }
            itemView.setItem(it)
            mLayoutContainer?.addView(itemView)
        }

        mIvClose?.setOnClickListener {
            dismissAllowingStateLoss()
        }

        setUploadHighlightText()

        val lzRegular = Typeface.createFromAsset(view.context.assets, "fonts/JDLangZhengTi_Regular.TTF")
        if (lzRegular != null) {
            tvTitle?.setTypeface(lzRegular)
        }
    }

    private fun setUploadHighlightText() {
        val span = SpannableStringBuilder(getString(R.string.meeting_feedback_upload_log))
        val highlight = getString(R.string.meeting_feedback_upload)
        val start = span.indexOf(highlight)
        val end = start + highlight.length
        //span.setSpan(ForegroundColorSpan(requireContext().getColor(R.color.meeting_feedback_link)), start, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        span.setSpan(object : ClickableSpan() {

            override fun updateDrawState(textPaint: TextPaint) {
                textPaint.color = requireContext().resources.getColor(R.color.meeting_feedback_link)
                textPaint.isUnderlineText = false
                textPaint.clearShadowLayer()
            }

            override fun onClick(widget: View) {
                uploadLog()
                uploadLocalLog()
                dismissAllowingStateLoss()
            }
        }, start, end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)

        mTvUploadLog?.movementMethod = LinkMovementMethod.getInstance()
        mTvUploadLog?.text = span
        mTvUploadLog?.highlightColor = Color.TRANSPARENT
    }

    private fun onLinkClick(link: String?) {
        dismissAllowingStateLoss()
        OpennessApi.openUrl(link, true)
    }

    private fun onJoinGroupClick(groupId: String) {
        val disable = ABTestManager.getInstance().getConfigByKey("monkey.test.disable", "0") != "0"
        if (disable) return

        lifecycleScope.launch {
            runCatching {
                openGroup(groupId)
            }.onFailure {
                if (it is ImChatException) {
                    if (it.code == ERROR_GROUP_NOT_EXIST) {
                        //已失效
                        Toast.makeText(context, R.string.meeting_feedback_group_not_exist, Toast.LENGTH_SHORT).show()
                    } else if (it.code == ERROR_GROUP_NOT_MEMBER) {
                        //不是成员
                        kotlin.runCatching {
                            joinGroup(groupId, null)
                        }.onSuccess {
                            dismissAllowingStateLoss()
                            openGroup(groupId)
                        }.onFailure {
                            Toast.makeText(context, R.string.meeting_feedback_join_group_failed, Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }.onSuccess {
                dismissAllowingStateLoss()
            }
        }
    }

    private fun onTextClick(text: String) {
        dismissAllowingStateLoss()
        //复制到剪切板text
        runCatching {
            val clipboard: ClipboardManager = context?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData: ClipData = ClipData.newPlainText("Label", text)
            clipboard.setPrimaryClip(clipData)
        }.onSuccess {
            Toast.makeText(context, R.string.meeting_copied_to_clipboard, Toast.LENGTH_SHORT).show()
        }
    }

    private suspend fun getGroupRoster(groupId: String) = suspendCancellableCoroutine<ArrayList<MemberEntityJd>>{
        imDdService.getGroupRoster(groupId, true, object : LoadDataCallback<ArrayList<MemberEntityJd>> {

            override fun onDataLoaded(list: ArrayList<MemberEntityJd>?) {
                it.resumeWith(Result.success(list ?: arrayListOf()))
            }

            override fun onDataNotAvailable(error: String?, code: Int) {
                it.resumeWithException(ImChatException(code, error))
            }
        })
    }

    private suspend fun joinGroup(groupId: String, code: String?) = suspendCancellableCoroutine<Unit> {
        imDdService.joinGroup(groupId, code, object : LoadDataCallback<Void> {
            override fun onDataLoaded(p0: Void?) {
                it.resumeWith(Result.success(Unit))
            }

            override fun onDataNotAvailable(error: String?, code: Int) {
                it.resumeWithException(ImChatException(code, error))
            }
        })
    }

    private suspend fun openGroup(groupId: String) = suspendCancellableCoroutine<Unit?> {
        imDdService.openChat(requireContext(), null, null, groupId, object : LoadDataCallback<Void?> {
            override fun onDataLoaded(p0: Void?) {
                it.resumeWith(Result.success(null))
            }

            override fun onDataNotAvailable(error: String?, code: Int) {
                it.resumeWithException(ImChatException(code, error))
            }
        })
    }

    private fun uploadLog() {
        jdMeetingService.uploadLog(requireActivity(), object : JdMeetingService.UploadLogCallback {
            override fun onSuccess() {

                Toast.makeText(AppBase.getAppContext(), R.string.meeting_feedback_upload_success, Toast.LENGTH_SHORT).show()
            }

            override fun onFailed(code: String?, msg: String?) {
                Toast.makeText(AppBase.getAppContext(), R.string.meeting_feedback_upload_failed, Toast.LENGTH_SHORT).show()
            }
        })
        JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_event_meeting_contactUS_uploadinfo, null)
    }

    private fun uploadLocalLog() {
        val date = DateFormat.format(DateUtils.DATE_FORMATE_SIMPLE, Calendar.getInstance()).toString()
        //上传回捞日志
        LogX.uploadLogManually(date, object : com.jingdong.sdk.talos.UploadCallback {
            override fun onLogFileNotFound(date: String) {
                Log.e("LogX", "onLogFileNotFound:$date")
            }

            override fun onBeginUpload(date: String, file: File) {
                Log.e("LogX", "onBeginUpload:" + date + ",file:" + file.absolutePath)
            }

            override fun onUploadProgressUpdate(
                date: String,
                file: File,
                totalSize: Long,
                finishedSize: Long
            ) {
                Log.e(
                    "LogX",
                    "onUploadProgressUpdate:" + date + ", progress:" + finishedSize + "/" + totalSize + "=" + (finishedSize * 100 / totalSize) + "%"
                )
            }

            override fun onUploadSuccess(date: String, file: File) {
                Log.e("LogX", "onUploadSuccess:" + date + ",file:" + file.absolutePath)
            }

            override fun onUploadFailed(date: String, file: File, errorMsg: String, e: Throwable) {
                MELogUtil.onlineE(
                    "LogX",
                    "onUploadFailed:" + date + ",file:" + file.absolutePath + ",errorMsg:" + errorMsg,
                    e
                )
            }
        })
    }
}

private class ImChatException(val code: Int, message: String? = null, cause: Throwable? = null): Exception(message, cause)