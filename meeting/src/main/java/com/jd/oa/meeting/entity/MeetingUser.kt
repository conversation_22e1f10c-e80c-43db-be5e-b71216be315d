package com.jd.oa.meeting.entity

import android.os.Parcelable
import com.alibaba.fastjson.annotation.JSONField
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * 已入会成员接口
 */
@Parcelize
data class MeetingUser(
        @SerializedName("appid")
        @JSONField(name = "appid")
        var appId: String? = null,
        @SerializedName("erp")
        @JSONField(name = "erp")
        var pin: String? = null,
        @SerializedName("teamid")
        @JSONField(name = "teamid")
        var teamId: String? = null,
        var avatar: String? = null,
        @SerializedName("nickname")
        @JSONField(name = "nickname")
        var nickname: String? = null,
        var dept: String? = null,
        var position: String? = null,
        @SerializedName("participantPeerid")
        @JSONField(name = "participantPeerid")
        var participantPeerId: Long? = 0,
        var participantId: String? = null,
        var portrait: String? = null,
        val isHost: Boolean = false,
        val isCoHost: Boolean = false,
        val userInfo: MeetingUser? = null,
        val clientType: String? = null,

        var createTime: String? = null,
        var meetingId: String? = null,
        var meetingCode: Long? = null,
        var subject: String? = null,
        var participant: String? = null,
        var creator: String? = null,
        var creatorPeerid: String? = null,
        var hostPriority: String? = null,
        var inviteTime: String? = null,
        var isCreator: Boolean? = null,
        var joinTime: String? = null,
        var leftTime: String? = null,
        var isOnline: Boolean? = null,
        var seqno: Int? = null,
        var status: Int? = null,
        var updateTime: Int? = null,
): Parcelable {

    constructor() : this(appId = null)

    val displayName: String
        get() = nickname?.let { if (it == "null") null else it } ?: pin ?: userInfo?.nickname ?: userInfo?.pin ?: ""

    fun sameUser(other: Any?): Boolean {
        if (this === other) return true
        return when (other) {
            is MeetingUser -> pin == other.userInfo?.pin && appId == other.userInfo?.appId && teamId == other.userInfo?.teamId
            else -> false
        }
    }

    fun sameData(user: MeetingUser?): Boolean {
        return pin == user?.pin &&
                appId == user?.appId &&
                teamId == user?.teamId &&
                nickname == user?.nickname &&
                avatar == user?.avatar
    }

    fun hasDetail() = !nickname.isNullOrBlank() && !portrait.isNullOrBlank() && !dept.isNullOrBlank()

    fun isSelf() = false

    fun isHost(meeting: Meeting?): Boolean =
        meeting?.hoster?.sameUser(this) == true

    fun isSubHost(meeting: Meeting?): Boolean =
        meeting?.subHost?.none {
            sameUser(it)
        } == false
}