package com.jd.oa.meeting.entity

import android.os.Parcelable
import androidx.annotation.Keep
import com.alibaba.fastjson.annotation.JSONField
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
@Keep
data class MeetingNote(
    val recordId: Int,
    val url: String?,
    val format: String?,
    val fileName: String?,
    val recordStatus: Int,
    val asrStatus: Int,
    val creator: MeetingUser?,
    val minutesInfo: MinutesInfo?,
): Parcelable

@Parcelize
@Keep
data class MinutesInfo(
    val minutesId: String,
    val title: String?,
    val recordId: String?,
    val creatorUserId: String?,
    val creatorTeamId: String?,
    val stage: String?,
    @SerializedName("summaryImage")
    @JSONField(name = "summaryImage")
    val image: String?,
    val permissions: List<String>?,
    val startTime: Long?,
    val endTime: Long?,
): Parcelable {

    companion object {
        /*
        "SHARE",
        "VISIT_MINUTES",
        "ADD_OWNER",
        "ADD_EDITABLE",
        "ADD_READ_ONLY",
        "ADJUST_ACCESS_SCOPE",
        "DELETE_MINUTES",
        "EDIT",
        "EXPORT_SOURCE_FILE_URL",
        "REMOVE_MINUTES"
         */
        const val PERMISSION_SHARE = "SHARE"
    }
}