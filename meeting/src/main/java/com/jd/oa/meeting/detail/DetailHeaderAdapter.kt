package com.jd.oa.meeting.detail

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.im.listener.Callback
import com.jd.oa.meeting.DurationCountDownTimer
import com.jd.oa.meeting.MeetingJDMAConstant
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingUser
import com.jd.oa.meeting.formatDuration
import com.jd.oa.meeting.formatStartAndEnd
import com.jd.oa.meeting.userlist.MeetingUsersActivity
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.ui.CircleImageView
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.ImageLoader
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.ScreenUtil
import com.jd.oa.utils.ToastUtils
import java.util.Calendar


class DetailHeaderAdapter(
    val lifecycleOwner: LifecycleOwner,
    val context: Context,
    var meeting: Meeting,
    val onScheduleDetailClick: View.OnClickListener
) : RecyclerView.Adapter<DetailHeaderAdapter.HeaderViewHolder>() {

    companion object{
        /**
         * 已取消
         */
        const val STATUS_CANCEL  = -1
        /**
         * 未开始
         */
        const val STATUS_NOT_START  = 0
        /**
         * 进行中
         */
        const val STATUS_ON  = 1
        /**
         * 已结束
         */
        const val STATUS_END  = 2
        /**
         * 即将开始
         */
        const val STATUS_PENDING  = 10

        const val TAG = "MeetingDetailActivity"
    }

    init {
        lifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                timer.cancel()
                timer.clearOnTickListener()
            }
        })
    }

    @SuppressLint("ClickableViewAccessibility")
    inner class HeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mTitle: TextView by lazy { itemView.findViewById(R.id.tv_title) }
        val mTime: TextView by lazy { itemView.findViewById(R.id.tv_time) }
        val mMeetingId: TextView by lazy { itemView.findViewById(R.id.tv_meeting_id) }
        val mLayoutPassword: View by lazy { itemView.findViewById(R.id.ll_meeting_password) }
        val mTvPassword: TextView by lazy { itemView.findViewById(R.id.tv_meeting_password) }
        val mTvMembers: TextView by lazy { itemView.findViewById(R.id.tv_meeting_members) }
        val mRvMembers: RecyclerView by lazy { itemView.findViewById(R.id.rv_members) }
        val mMeetingRooms: RecyclerView by lazy { itemView.findViewById(R.id.rv_meeting_rooms) }
        val mMemberRoot: ViewGroup by lazy { itemView.findViewById(R.id.ll_members) }
        val mDevicesRoot: RelativeLayout by lazy { itemView.findViewById(R.id.ll_meeting_rooms) }
        val mLottie: View by lazy { itemView.findViewById(R.id.lottie) }
        val mStatusTime: TextView by lazy { itemView.findViewById(R.id.tv_status_time) }
        val memberAdapter = MemberAdapter(itemView.context, mutableListOf())
        val deviceAdapter = MeetingRoomsAdapter(itemView.context, mutableListOf())
        val calendarDetail: View by lazy { itemView.findViewById(R.id.ll_calendar_detail) }

        init {
            mRvMembers.layoutManager = LinearLayoutManager(itemView.context, LinearLayoutManager.HORIZONTAL, false)
            mRvMembers.adapter = memberAdapter
            mMeetingRooms.layoutManager = LinearLayoutManager(itemView.context, LinearLayoutManager.HORIZONTAL, false)
            mMeetingRooms.adapter = deviceAdapter

            calendarDetail.setOnClickListener(onScheduleDetailClick)
            memberAdapter.setOnItemClickListener(object : ItemClickListener{
                override fun onItemClick(meetingUser: MeetingUser, position: Int) {
                    if (meetingUser.userInfo?.appId.isNullOrEmpty()) {
                        AppJoint.service(ImDdService::class.java)
                            ?.showContactDetailInfo(itemView.context, meetingUser.userInfo?.pin)
                    } else {
                        AppJoint.service(ImDdService::class.java)?.showContactDetailInfo(
                            itemView.context,
                            meetingUser.userInfo?.appId,
                            meetingUser.userInfo?.pin,
                        )
                    }
                }
            })
            memberAdapter.setOnCountClickListener(object : View.OnClickListener {
                override fun onClick(v: View?) {
                    goMemberListPage(false)
                }
            })
            mMeetingRooms.setOnTouchListener(View.OnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_UP) {
                    goMemberListPage(true)
                }
                false
            })
            mMemberRoot.setOnClickListener {
                goMemberListPage(false)
            }
            mMeetingRooms.setOnClickListener {
                goMemberListPage(true)
            }
            mDevicesRoot.setOnClickListener{
                goMemberListPage(true)
            }
        }

        private fun goMemberListPage(isDevices: Boolean) {
            val intent = Intent(itemView.context, MeetingUsersActivity::class.java)
            val bundle = Bundle()
            bundle.putParcelable("meeting", meeting)
            bundle.putBoolean("isDevices", isDevices)
            intent.putExtra("extra", bundle)
            itemView.context.startActivity(intent)
            if (isDevices) {
                JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_ViewRooms, null)
            } else {
                JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_ViewPartipant, null)
            }
        }
    }

    val timer: DurationCountDownTimer = DurationCountDownTimer()
    var devices: MutableList<MeetingUser> = mutableListOf()
    var users: MutableList<MeetingUser> = mutableListOf()
    private val isDetailOpenInCalendar: Boolean by lazy { TenantConfigBiz.isDetailOpenInCalendar() }//是否显示“在日历中查看详情”功能

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HeaderViewHolder {
        val holder = HeaderViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_item_detail, parent, false))
        return holder
    }

    override fun getItemCount() = 1

    override fun onBindViewHolder(holder: HeaderViewHolder, position: Int) {
        holder.apply {
            mTitle.text = meeting.subject
            mLottie.visibility = if (1 == meeting.realMeetingStatus) View.VISIBLE else View.GONE
            mTime.text = formatStartAndEnd(
                    context,
                if (STATUS_END == meeting.realMeetingStatus) meeting.realStartTimeInCalendar ?: meeting.startTimeInCalendar else meeting.startTimeInCalendar,
                if (STATUS_END == meeting.realMeetingStatus) meeting.realEndTimeInCalendar else meeting.endTimeInCalendar)

            val startTime = meeting.realStartTimeInCalendar ?: meeting.realJoinTimeInCalendar
                ?: meeting.startTimeInCalendar ?: meeting.scheduleStartTimeInCalendar
            handleStatusText(holder, mStatusTime, meeting.realMeetingStatus, meeting.duration, startTime)
            mMeetingId.text = context.getString(R.string.meeting_detail_meeting_id, meeting.meetingCode.toString())

            if (meeting.password?.isNotEmpty() == true) {
                mLayoutPassword.visibility = View.VISIBLE
                mTvPassword.text = context.getString(R.string.meeting_detail_host_key, meeting.password)
            } else {
                mLayoutPassword.visibility = View.GONE
            }

            mMemberRoot.visibility = if (memberAdapter.members.isEmpty()) View.GONE else View.VISIBLE
            mTvMembers.text = context.resources.getQuantityString(R.plurals.meeting_detail_meeting_members, memberAdapter.members.size, memberAdapter.members.size)
            mDevicesRoot.visibility = if (deviceAdapter.devices.isEmpty()) View.GONE else View.VISIBLE

            memberAdapter.refreshMembers(users)
            deviceAdapter.refreshDevices(devices)

            calendarDetail.visibility = if (!TextUtils.isEmpty(meeting.scheduleId) && isDetailOpenInCalendar) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    override fun onViewDetachedFromWindow(holder: HeaderViewHolder) {
        holder.apply {
//            timer.removeOnTickListener(::onTimerTick)
            timer.clearOnTickListener()
        }
    }

    private fun resetTimer(start: Boolean) = when {
        start && !timer.isStart -> {
            timer.start()
        }

        !start -> {
            timer.cancel()
        }

        else -> {}
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refreshData(meeting: Meeting) {
        this.meeting = meeting
        notifyDataSetChanged()
    }

    private fun handleCopyMeeting() {
        var text = context.getString(R.string.meeting_jdme_meeting).plus("\n")
                .plus(context.getString(R.string.meeting_id)).plus(": ").plus(meeting.meetingCode.toString()).plus("\n")
                .plus(context.getString(R.string.meeting_password)).plus(": ").plus(if (meeting.password != null && meeting.password!!.isNotEmpty()) meeting.password else context.getString(R.string.meeting_null)).plus("\n")
                .plus(context.getString(R.string.meeting_address)).plus(": ").plus(meeting.getDesktopUrl())
        if (meeting.joinPhoneNumber?.isNotEmpty() == true) {
            text = text.plus("\n")
                .plus(context.getString(R.string.meeting_join_phone_number))
                .plus(meeting.joinPhoneNumber)
        }
        val clipboard: ClipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("label", text)
        clipboard.setPrimaryClip(clip)
        ToastUtils.showCenterToast(R.string.meeting_copy_success)
        MELogUtil.localI(TAG, "copy meeting : $text")
    }

    private fun handleStatusText(holder: HeaderViewHolder, textView: TextView, status: Int?, duration: Long?, startTime: Calendar?) {
        holder.apply {
            when (status) {
                STATUS_ON -> {//进行中
                        //textView.setTextColor(context.getColor(R.color.color_333333))
                        if (startTime != null) {
                            val durationInMillis = Calendar.getInstance().timeInMillis - startTime.timeInMillis
                            textView.text = formatDuration(context, durationInMillis)
                            textView.tag = startTime
                            timer.addOnTickListener {
                                val startTime1 = textView.tag
                                if (startTime1 != null && startTime1 is Calendar) {
                                    textView.text = formatDuration(context, Calendar.getInstance().timeInMillis - startTime.timeInMillis)
                                }
                            }
                            resetTimer(1 == meeting.realMeetingStatus)
                        }
                }

                STATUS_END -> {//已结束
                    textView.runCatching {
                        setTextColor(context.getColor(R.color.meeting_detail_icon_color))
                        text = context.getString(R.string.meeting_status_end)
                                .plus(" ")
                                .plus(formatDuration(context, meeting.duration))
                    }
                }

                STATUS_NOT_START -> {//未开始
                    textView.setTextColor(context.getColor(R.color.meeting_detail_icon_color))
                    textView.text = context.getString(R.string.meeting_status_not_start)
                }

                STATUS_PENDING -> {//即将开始
                    textView.setTextColor(context.getColor(R.color.meeting_detail_icon_color))
                    textView.text = context.getString(R.string.meeting_status_pending_start)
                }

                STATUS_CANCEL -> {//已取消
                    textView.setTextColor(context.getColor(R.color.meeting_detail_icon_color))
                    textView.text = context.getString(R.string.meeting_status_cancel)
                }

                else -> {}
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setParticipants(meetingUsers: MutableList<MeetingUser>?) {
        if (!meetingUsers.isNullOrEmpty()) {
            users.clear()
            users.addAll(meetingUsers)
            notifyDataSetChanged()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setDeviceParticipants(meetingDevices: List<MeetingUser>?) {
        if (!meetingDevices.isNullOrEmpty()) {
            devices.clear()
            devices.addAll(meetingDevices)
            notifyDataSetChanged()
        }
    }
}

class MemberAdapter(val context: Context, var members: List<MeetingUser>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val TYPE_MEMBER = 1
        const val TYPE_COUNT = 2
    }
    inner class MemberViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mHead: CircleImageView by lazy { itemView.findViewById(R.id.iv_head) }
        val mHost: TextView by lazy { itemView.findViewById(R.id.tv_host) }
        val mHostBg: View by lazy { itemView.findViewById(R.id.view_bg) }

        init {
        }
    }

    inner class MemberCountViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val count: TextView by lazy { itemView.findViewById(R.id.tv_count) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TYPE_COUNT) {
            MemberCountViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_detail_item_member_count, parent, false))
        } else {
            MemberViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_detail_item_member, parent, false))
        }
    }

    override fun getItemCount(): Int {
        val screenWidth = ScreenUtil.getScreenWidth(context)
        val displayCount = (screenWidth - DensityUtil.dp2px(context, 58f)) / DensityUtil.dp2px(context, 36f)
        return if (displayCount < members.size) displayCount else members.size
    }

    override fun getItemViewType(position: Int): Int {
        val count = itemCount
        return if (position < count - 1) {
            TYPE_MEMBER
        } else {
            if (count == members.size) {
                TYPE_MEMBER
            } else {
                TYPE_COUNT
            }
        }
    }

    val imDdService: ImDdService by lazy { AppJoint.service(ImDdService::class.java) }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is MemberViewHolder) {
            members[position].apply {
                if (isHost) {
                    holder.mHost.visibility = View.VISIBLE
                    holder.mHostBg.visibility = View.VISIBLE
                } else {
                    holder.mHost.visibility = View.GONE
                    holder.mHostBg.visibility = View.GONE
                }
                if (!portrait.isNullOrEmpty()) {
                    ImageLoader.load(context, holder.mHead, portrait, R.drawable.meeting_default_avatar)
                } else {
                    imDdService.getContactInfo(appId ?: userInfo?.appId,
                        pin ?: userInfo?.pin ?: nickname,
                        object : Callback<MemberEntityJd>{
                            override fun onSuccess(bean: MemberEntityJd?) {
                                portrait = bean?.avatar
                                ImageLoader.load(context, holder.mHead, portrait, R.drawable.meeting_default_avatar)
                            }

                            override fun onFail() {
                                ImageLoader.load(context, holder.mHead, R.drawable.meeting_default_avatar)
                            }
                        })
                }
                holder.itemView.setOnClickListener{
                    itemClickListener.onItemClick(this, position)
                }
            }
        } else if (holder is MemberCountViewHolder) {
            val num = members.size - itemCount
            if (num <= 0) {
                holder.count.visibility = View.GONE
            } else {
                holder.count.visibility = View.VISIBLE
                @SuppressLint("SetTextI18n")
                holder.count.text = "+${(num + 1)}"
            }
            holder.itemView.setOnClickListener {
                countClickListener?.onClick(it)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refreshMembers(members: List<MeetingUser>) {
        this.members = members
        notifyDataSetChanged()
    }

    lateinit var itemClickListener: ItemClickListener

    fun setOnItemClickListener(itemClickListener: ItemClickListener) {
        this.itemClickListener = itemClickListener
    }

    var countClickListener: View.OnClickListener? = null

    fun setOnCountClickListener(countClickListener: View.OnClickListener) {
        this.countClickListener = countClickListener
    }

}

interface ItemClickListener{
    fun onItemClick(meetingUser: MeetingUser, position: Int)
}

class MeetingRoomsAdapter(val context: Context, var devices: List<MeetingUser>) : RecyclerView.Adapter<MeetingRoomsAdapter.MeetingRoomsHolder>() {
    class MeetingRoomsHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mRoomName: TextView by lazy { itemView.findViewById(R.id.tv_room_name) }

        init {
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MeetingRoomsHolder {
        val holder = MeetingRoomsHolder(LayoutInflater.from(context).inflate(R.layout.meeting_detail_item_rooms, parent, false))
        return holder
    }

    override fun getItemCount(): Int {
        val screenWidth = ScreenUtil.getScreenWidth(context)
        val displayCount = (screenWidth - DensityUtil.dp2px(context, 62f)) / DensityUtil.dp2px(context, 36f) - 1
        return if (displayCount < devices.size) displayCount else devices.size
    }

    override fun onBindViewHolder(holder: MeetingRoomsHolder, position: Int) {
        devices[position].apply {
            holder.mRoomName.text = nickname?.substring(0, 1) ?: ""
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refreshDevices(devices: List<MeetingUser>) {
        this.devices = devices
        notifyDataSetChanged()
    }
}