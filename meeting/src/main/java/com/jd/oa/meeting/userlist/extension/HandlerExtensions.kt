package com.jd.oa.meeting.userlist.extension

import android.os.Handler
import android.os.Looper

internal object MainHandler : <PERSON><PERSON>(Looper.getMainLooper())

fun post(runnable: Runnable) = MainHandler.post(runnable)

fun postDelayed(delay: Long, runnable: Runnable) = MainHandler.postDelayed(runnable, delay)

fun removeCallbacks(runnable: Runnable) = MainHandler.removeCallbacks(runnable)

fun hasCallbacks(runnable: Runnable) = MainHandler.hasCallbacks(runnable)

fun debounce(delay: Long, runnable: Runnable) {
    MainHandler.removeCallbacks(runnable)
    MainHandler.postDelayed(runnable, delay)
}

fun isMainThread(): Boolean = Looper.getMainLooper() === Looper.myLooper()