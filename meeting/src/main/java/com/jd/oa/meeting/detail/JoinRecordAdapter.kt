package com.jd.oa.meeting.detail

import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.JoinRecord
import com.jd.oa.meeting.getDateFromDateString
import com.jd.oa.meeting.getTimeFromDateString
import com.jd.oa.ui.recycler.BaseRecyclerAdapter

class JoinRecordAdapter(
    context: Context,
    data: List<JoinRecord>
    ) : BaseRecyclerAdapter<JoinRecord, JoinRecordAdapter.ViewHolder>(context, data) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): JoinRecordAdapter.ViewHolder {
        val holder = ViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_item_join_record, parent, false))
        return holder
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        data[position].apply {
            holder.mDate.text = getDateFromDateString(joinTime)
            holder.mJoinTime.text = getTimeFromDateString(joinTime)
            holder.mExitTime.text = getTimeFromDateString(leftTime)
        }
    }

    override fun getItemCount(): Int {
        return data.size
    }

    @RequiresApi(Build.VERSION_CODES.N)
    fun refreshData(list: MutableList<JoinRecord>?) {
        if (!list.isNullOrEmpty()) {
            data.clear()
            data.addAll(list)
            notifyDataSetChanged()
        }
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mDate: TextView by lazy { itemView.findViewById(R.id.tv_date) }
        val mJoinTime: TextView by lazy { itemView.findViewById(R.id.tv_join_time) }
        val mExitTime: TextView by lazy { itemView.findViewById(R.id.tv_exit_time) }

        init {
        }
    }
}