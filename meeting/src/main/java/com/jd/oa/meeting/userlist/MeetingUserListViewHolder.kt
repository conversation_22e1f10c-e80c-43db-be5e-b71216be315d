package com.jd.oa.meeting.userlist

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import com.google.android.material.imageview.ShapeableImageView
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingUser
import com.jd.oa.utils.ImageLoader
import com.jd.oa.utils.visible
import org.json.JSONException
import org.json.JSONObject

class MeetingUserListViewHolder(viewGroup: ViewGroup, private val conference: Meeting) :
        AbstractViewHolder<MeetingUser>(
                LayoutInflater.from(viewGroup.context).inflate(
                        R.layout.meeting_activity_conference_user_list_item, viewGroup, false
                )
        ) {
    private val name: TextView by lazy { itemView.findViewById(R.id.name) }
    private val dutyIcon: ImageView by lazy { itemView.findViewById(R.id.duty_icon) }
    private val avatar: ShapeableImageView by lazy { itemView.findViewById(R.id.avatar) }
    private val dept: TextView by lazy { itemView.findViewById(R.id.tv_dept) }
    override fun onBind() {
        val data = data()
        ImageLoader.load(itemView.context, avatar, data.portrait, R.drawable.meeting_default_avatar)
        name.text = data.displayName.ifBlank { context.getString(R.string.meeting_detail_quit_ser) }
        when {
            data.isHost -> {
                dutyIcon.isVisible = true
                dutyIcon.setImageResource(R.drawable.meeting_ic_host_white)
            }

            data.isCoHost -> {
                dutyIcon.isVisible = true
                dutyIcon.setImageResource(R.drawable.meeting_ic_sub_host_white)
            }

            else -> {
                dutyIcon.isVisible = false
                dutyIcon.setImageResource(0)
            }
        }
        if (data.dept.isNullOrBlank()) {
            //dept.gone()
            dept.text = null
        } else {
            dept.visible()
            @SuppressLint("SetTextI18n")
            dept.text = "${parseDept(data.dept)} ${data.position ?: ""}"
        }
    }

    private fun parseDept(dept: String?): String? {
        if (dept.isNullOrBlank()) return null
        return runCatching {
            val department = try {
                val json = JSONObject(dept)
                json.optString("name")
            } catch (e: JSONException) {
                dept
            }
            department.split("-").lastOrNull()
        }.getOrNull()
    }
}