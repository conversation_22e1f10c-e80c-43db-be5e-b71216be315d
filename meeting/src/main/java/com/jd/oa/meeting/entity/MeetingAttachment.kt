package com.jd.oa.meeting.entity

import android.net.Uri
import android.text.TextUtils
import androidx.annotation.Keep

enum class AttachmentType {
    FILE, JOYSPACE_DOC
}
abstract class MeetingAttachment {
    abstract val title: String
    abstract val iconUrl: String?
    abstract val type: AttachmentType
}


/**
    "permissionType": null,
    "pageType": "15",
    "permissionList": null,
    "id": "71ELwJRvJekYsJDBtMyr",
    "title": "会议材料详情",
    "url": "https://joyspace-pre2.jd.com/meeting/71ELwJRvJekYsJDBtMyr"
 */
@Keep
data class JoyspaceDoc(
    val permissionType: String?,
    val pageType: String?,
    val permissionList: String?,
    val id: String?,
    val url: String?,
    override val title: String,
): MeetingAttachment() {

    override val type: AttachmentType
        get() = AttachmentType.JOYSPACE_DOC

    companion object {
        private const val API_HOST = "https://apijoyspace.jd.com"
    }

    override val iconUrl: String
        get() {
            val builder = Uri.parse("$API_HOST/v1/common/icons").buildUpon()
            if (pageType == "6") {
                val titleText = if (TextUtils.isEmpty(title)) "" else title
                builder.appendQueryParameter("title", titleText)
            }
            builder.appendQueryParameter("pageType", pageType)
            return builder.appendQueryParameter("client", "web").build().toString()
        }
}

/**
    "fileName": "议题 (2).png",
    "fileSize": 551,
    "fileUrl": "https://eefs.jd.com/res/download/646k1bzm.png?appKey=yxnykjwzd6rkn2ml",
    "fileType": "image/png",
    "extended": null,
    "fileId": "5WsXfMo_Y21pdb_T_Lc5Y"
 */
@Keep
data class FileAttachment(
    val fileName: String?,
    val fileSize: Long = 0,
    val fileUrl: String?,
    val fileType: String?,
    val extended: String?,
    val fileId: String?,
): MeetingAttachment() {

    override val iconUrl: String?
        get() = fileUrl

    override val title: String
        get() = fileName ?: ""

    override val type: AttachmentType
        get() = AttachmentType.FILE
}