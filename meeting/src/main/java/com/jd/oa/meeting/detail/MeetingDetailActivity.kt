package com.jd.oa.meeting.detail

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.Router
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.abilities.api.OpennessApi
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.business.netdisk.GlideEngine
import com.jd.oa.fragment.utils.FileType
import com.jd.oa.meeting.MeetingJDMAConstant
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.*
import com.jd.oa.meeting.resetDateFormat
import com.jd.oa.model.service.CalendarService
import com.jd.oa.model.service.JdMeetingService
import com.jd.oa.model.service.JoyNoteService
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import com.jd.oa.timezone.TimeZoneChangeNotifier
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.OpenFileUtil
import com.jd.oa.utils.ToastUtils
import com.jdcloud.mt.me.modle.SourceType
import com.yu.bundles.album.MaeAlbum
import com.yu.bundles.album.image.ImageEngine
import org.json.JSONObject
import java.net.URLDecoder
import java.net.URLEncoder
import java.util.*

@Route(DeepLink.MEETING_DETAIL)
class MeetingDetailActivity : BaseActivity() {

    val mRecyclerView: RecyclerView by lazy { findViewById(R.id.recycler_view) }
    val mBtnJoin: Button by lazy { findViewById(R.id.btn_join) }
    val mBack: IconFontView by lazy { findViewById(R.id.tv_back) }
    val mShare: IconFontView by lazy { findViewById(R.id.tv_share) }
    val mbtnRoot: LinearLayout by lazy { findViewById(R.id.btn_root) }
    val mBtnCopy: Button by lazy { findViewById(R.id.btn_copy) }

    val repository: MeetingDetailRepository by lazy {
        val remoteDataSource = MeetingDetailRemoteDataSource()
        MeetingDetailRepository(remoteDataSource)
    }

    private lateinit var mAdapter: ConcatAdapter
    private lateinit var mDetailAdapter: DetailHeaderAdapter
    private lateinit var mAttachmentsAdapter: AttachmentsAdapter//附件
    private lateinit var mNotesAdapter: NotesAdapter//慧记
    private lateinit var mJoinRecordAdapter: JoinRecordAdapter//出入会记录
    private lateinit var mLoadingStateAdapter: LoadingStateAdapter
    private lateinit var mErrorStateAdapter: LoadingStateAdapter
    private lateinit var mPaddingAdapter: PaddingAdapter

    private val jdMeetingService = AppJoint.service(JdMeetingService::class.java)
    private val imDdService = AppJoint.service(ImDdService::class.java)
    private var attachmentsSuccess: Boolean? = null
    private var notesSuccess: Boolean? = null

    lateinit var mViewModel: MeetingDetailViewModel

    private val mImageEngine: ImageEngine by lazy { GlideEngine() }

    companion object {
        fun createParamDeepLink(
                meetingId: String,
                meetingCode: Long,
                meetingSelector: Int,
                scheduleId: String?,
                calendarId: String?,
                scheduleStartTime: String?,
                subject:String?,
                status:Int,
                startTime:String?,
                endTime:String?,
        ): String {
            val mparam = mutableMapOf<String, Any>()
            mparam["meetingId"] = meetingId
            mparam["meetingCode"] = meetingCode
            mparam["meetingSelector"] = meetingSelector
            mparam["scheduleId"] = scheduleId ?: ""
            mparam["calendarId"] = calendarId ?: ""
            mparam["scheduleStartTime"] = scheduleStartTime ?: ""
            mparam["subject"] = subject ?: ""
            mparam["status"] = status
            mparam["startTime"] = startTime?: ""
            mparam["endTime"] = endTime ?: ""
            val buildUpon = Uri.parse(DeepLink.MEETING_DETAIL).buildUpon()
            val tmp = URLEncoder.encode(JsonUtils.getGson().toJson(mparam), "UTF-8")
            buildUpon.appendQueryParameter("mparam", tmp)
            return buildUpon.toString()
        }
    }

    var meetingId: String? = null
    var meetingCode: Long? = null
    var meetingSelector: Int? = null
    var scheduleId: String? = null
    var calendarId: String? = null
    var scheduleStartTime: String? = null
    var subject: String? = null
    var startTime: String? = null
    var endTime: String? = null
    var status: Int? = null
    var meeting:Meeting? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.meeting_activity_detail)

        val param = intent.getStringExtra("mparam")
        kotlin.runCatching {
            if (!param.isNullOrEmpty()) {
                val tmp = URLDecoder.decode(param, "UTF-8")
                val json = JSONObject(tmp)
                meetingId = json.optString("meetingId")
                meetingCode = json.optLong("meetingCode")
                meetingSelector = json.optInt("meetingSelector")
                scheduleId = json.optString("scheduleId")
                calendarId = json.optString("calendarId")
                scheduleStartTime = json.optString("scheduleStartTime")
                subject = json.optString("subject")
                status = json.optInt("status")
            }
        }

        mViewModel = ViewModelProvider(this, MeetingDetailViewModel.Factory(repository)).get(MeetingDetailViewModel::class.java)
        supportActionBar?.hide()

        val meeting1 = Meeting()
        meeting1.meetingId = meetingId ?: ""
        meeting1.meetingCode = meetingCode
        meeting1.subject = subject
        meeting1.status = status
        meeting1.meetingSelector = meetingSelector
        meeting1.startTime = startTime
        meeting1.endTime = endTime
        mDetailAdapter = DetailHeaderAdapter(this, this, meeting1) {
            val scheduleId = mDetailAdapter.meeting.scheduleId
            val calendarId = mDetailAdapter.meeting.calendarId
            val start = mDetailAdapter.meeting.scheduleStartTime?.let { it1 ->
                Meeting.dateFormat.parse(
                    it1
                )?.time ?: 0
            }

            val service = AppJoint.service(CalendarService::class.java)
            service.openScheduleDetail(scheduleId, calendarId, start ?: 0, 0)
            JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_ViewSchedule, null)
            MELogUtil.localI(TAG, "click openScheduleDetail scheduleId:$scheduleId calendarId$calendarId start:$start originalStart:0")
        }
        mAttachmentsAdapter = AttachmentsAdapter(this, mutableListOf(), ::onAttachmentTap)
        mNotesAdapter = NotesAdapter(this, mutableListOf(), {
           if (it.minutesInfo == null) {
               if (it.recordStatus == NotesAdapter.STATUS_FINISH) {
                   it.url?.let { url ->
                       val joyNoteService = AppJoint.service(JoyNoteService::class.java)
                       joyNoteService.openMePlayer(this@MeetingDetailActivity, url, it.fileName ?: "")
                   }
               }
               JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_ViewRecording, null)
           } else {
               if (it.minutesInfo.stage == NotesAdapter.STAGE_FINISH) {
                   val jsonObject = JSONObject();
                   jsonObject.put("minutesId", it.minutesInfo.minutesId)
                   val deeplink = Uri.parse(DeepLink.JOY_NOTE_DETAIL)
                           .buildUpon()
                           .appendQueryParameter("mparam", jsonObject.toString())
                           .build()
                   Router.build(deeplink).go(this)
                   JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_ViewMinutes, null)
               }
           }
        }) {
            //mViewModel.shareNote(it)
            val joyNoteService = AppJoint.service(JoyNoteService::class.java)
            joyNoteService.share(this, it.minutesId, it.permissions, null)
        }
        mJoinRecordAdapter = JoinRecordAdapter(this, mutableListOf())
        mLoadingStateAdapter = LoadingStateAdapter(this, LoadingStateAdapter.STATE_LOADING, null)
        mErrorStateAdapter = LoadingStateAdapter(this, LoadingStateAdapter.STATE_ERROR) {
            initData()
            MELogUtil.localI(TAG, "error state click refresh meetingId:$meetingId meetingCode:$meetingCode status:$status")
        }
        mPaddingAdapter = PaddingAdapter(this, Color.WHITE, DensityUtil.dp2px(this, 36f))

        mAdapter = ConcatAdapter(
                mDetailAdapter,
                mLoadingStateAdapter,
//                mErrorStateAdapter
//                mAttachmentsAdapter,
//                mNotesAdapter,
//                mJoinRecordAdapter,
        )

        mRecyclerView.adapter = mAdapter
        mRecyclerView.layoutManager = LinearLayoutManager(this)
        mRecyclerView.itemAnimator = null

        mViewModel.meeting.observe(this) {
            meeting = it
            meeting?.meetingSelector = meetingSelector
            mbtnRoot.visibility = View.VISIBLE
            val joinedMeeting = jdMeetingService.currentMeeting
            joinedMeeting?.run {
                meeting?.joined = meeting?.meetingCode == second
                mBtnJoin.setText(if(it.joined) R.string.meeting_joined else R.string.meeting_join_meeting)
                mBtnJoin.isEnabled = !it.joined
            }
            mDetailAdapter.refreshData(meeting!!)
            if (meeting?.realMeetingStatus == DetailHeaderAdapter.STATUS_ON ||
                    meeting?.realMeetingStatus == DetailHeaderAdapter.STATUS_PENDING ||
                    meeting?.realMeetingStatus == DetailHeaderAdapter.STATUS_NOT_START) {
                mShare.visibility = View.VISIBLE
            } else {
                mShare.visibility = View.GONE
            }
            mBtnJoin.visibility = if (meeting?.isHistory == true || meeting?.isEnd == true) View.GONE else View.VISIBLE
        }
        mViewModel.participants.observe(this) {
            mDetailAdapter.setParticipants(it)
        }
        mViewModel.participantsDevice.observe(this){
            mDetailAdapter.setDeviceParticipants(it)
        }
        mBack.setOnClickListener {
            finish()
        }
        mShare.setOnClickListener {
            mViewModel.shareMeeting(meeting)
            JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_Share, null)
        }
        mBtnJoin.setOnClickListener {
            jdMeetingService.joinMeeting(this@MeetingDetailActivity, meetingId, meetingCode, mDetailAdapter.meeting.password, SourceType.DETAIL.type.toString(), null)
            jdMeetingService.currentMeetingLiveData.observe(this){
                if (it?.second == null) {
                    mBtnJoin.isEnabled = true
                    mBtnJoin.setText(R.string.meeting_join_meeting)
                    MELogUtil.localI(TAG, "join meeting failed or leave")
                } else if (meetingCode == it.second) {
                    mBtnJoin.isEnabled = false
                    mBtnJoin.setText(R.string.meeting_joined)
                    MELogUtil.localI(TAG, "join meeting success ${it.first} ${it.second}")
                }
            }
            JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_Join, null)
            MELogUtil.localI(TAG, "join meeting click, meetingId:$meetingId meetingCode:$meetingCode password:${meeting?.password}")
        }

        mBtnCopy.setOnClickListener {
            handleCopyMeeting()
            JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_Copy, null)
        }

        mViewModel.attachments.observe(this) {
            attachmentsSuccess = true
            mAttachmentsAdapter.refresh(it)
            handleAdapter()
        }

        mViewModel.notes.observe(this) {
            notesSuccess = true
            mNotesAdapter.refresh(it)
            handleAdapter()
        }

        mViewModel.joinRecords.observe(this) {
            mJoinRecordAdapter.refresh(it)
        }

        TimeZoneChangeNotifier.getInstance().timeZoneLiveData.observe(this) {
            meeting?.resetCalendarObj()
            resetDateFormat()
            mDetailAdapter.notifyDataSetChanged()
        }

        initData()
    }

    private fun initData() {
        if (!meetingId.isNullOrEmpty() && meetingSelector != null) {
            mViewModel.getMeetingDetail(meetingId!!, meetingSelector!!) {
            }
        }
        if (!TextUtils.isEmpty(scheduleId) && !TextUtils.isEmpty(scheduleStartTime)) {
            val start = scheduleStartTime?.let { Meeting.dateFormat.parse(it)?.time ?: 0 }
            mViewModel.getAttachments(scheduleId!!, start!!) {
                attachmentsSuccess = false
                handleAdapter()
            }
        } else {
            attachmentsSuccess = true
            handleAdapter()
        }
//        mViewModel.getJoinRecords(meetingId!!, meetingSelector!!)
        mViewModel.getAllParticipants(meetingId!!, meetingSelector!!)
        mViewModel.getAllDeviceParticipants(meetingId!!, meetingSelector!!)
        mViewModel.getNotes(meetingId!!, meetingSelector!!) {
            notesSuccess = false
            handleAdapter()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun handleAdapter() {
        if (attachmentsSuccess == null || notesSuccess == null) {
            return
        }
        if (attachmentsSuccess!! && notesSuccess!!) {
            mAdapter.adapters.forEach {
                if (it == mLoadingStateAdapter) {
                    mAdapter.removeAdapter(it)
                }
                if (it == mErrorStateAdapter) {
                    mAdapter.removeAdapter(it)
                }
            }
            mAdapter.addAdapter( mAttachmentsAdapter)
            mAdapter.addAdapter(mNotesAdapter)
            mAttachmentsAdapter.notifyDataSetChanged()
            mNotesAdapter.notifyDataSetChanged()
            mAdapter.addAdapter(mPaddingAdapter)
            mAdapter.notifyDataSetChanged()
        } else {
            mAdapter.removeAdapter(mLoadingStateAdapter)
            mAdapter.addAdapter(mErrorStateAdapter)
            mAdapter.notifyDataSetChanged()
            MELogUtil.localI(TAG, "show error state")
        }
    }

    private fun preview(image: FileAttachment, list: List<MeetingAttachment>) {
        val images = list.filter { it.type == AttachmentType.FILE }
            .map { it as FileAttachment }
            .filter {
                val type = FileType.getTypeInt(it.fileName)
                return@filter FileType.isImage(type)
            }
        val index = images.indexOf(image)
        MaeAlbum.setStyle(com.jme.common.R.style.MeAlum)
        MaeAlbum.setImageEngine(mImageEngine)
        val intent = MaeAlbum.getPreviewIntent(
            this,
            ArrayList(images.map { it.fileUrl }),
            index,
            false,
            false,
            false
        )
        startActivity(intent)
    }

    private fun onAttachmentTap(attachment: MeetingAttachment) {
        if (attachment.type == AttachmentType.JOYSPACE_DOC) {
            val document = attachment as JoyspaceDoc
            OpennessApi.openUrl(document.url, false)
        } else if (attachment.type == AttachmentType.FILE) {
            val file = attachment as FileAttachment
            val type = FileType.getTypeInt(file.fileName)
            if (FileType.isImage(type)) {
                preview(attachment, mAttachmentsAdapter.data)
            } else {
                if (attachment.fileUrl != null && attachment.fileName != null) {
                    OpenFileUtil.openFileByX5(
                        this,
                        "MeetingAttachment",
                        attachment.fileUrl,
                        attachment.fileName,
                        attachment.fileId ?: UUID.randomUUID().toString(),
                        false,
                        true
                    )
                }
            }
        }
        JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Detail_viewAttachment, null)
        MELogUtil.localI(TAG, "onAttachmentTap: ".plus(JsonUtils.getJsonString(attachment)))
    }

    private fun handleCopyMeeting() {
        if (meeting == null) return
        var text = getString(R.string.meeting_jdme_meeting).plus("\n")
            .plus(getString(R.string.meeting_id)).plus(": ").plus(meeting!!.meetingCode.toString()).plus("\n")
            .plus(getString(R.string.meeting_password)).plus(": ").plus(if (meeting!!.password != null && meeting!!.password!!.isNotEmpty()) meeting!!.password else getString(R.string.meeting_null)).plus("\n")
            .plus(getString(R.string.meeting_address)).plus(": ").plus(meeting!!.getDesktopUrl())
        if (meeting?.joinPhoneNumber?.isNotEmpty() == true) {
            text = text.plus("\n")
                .plus(getString(R.string.meeting_join_phone_number))
                .plus(meeting?.joinPhoneNumber)
        }
        val clipboard: ClipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("label", text)
        clipboard.setPrimaryClip(clip)
        ToastUtils.showCenterToast(R.string.meeting_copy_success)
        MELogUtil.localI(DetailHeaderAdapter.TAG, "copy meeting : $text")
    }
}