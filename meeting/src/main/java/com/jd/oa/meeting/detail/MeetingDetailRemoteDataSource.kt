package com.jd.oa.meeting.detail

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.meeting.entity.FileAttachment
import com.jd.oa.meeting.entity.JoinRecord
import com.jd.oa.meeting.entity.JoyspaceDoc
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingAttachment
import com.jd.oa.meeting.entity.MeetingNote
import com.jd.oa.meeting.entity.MeetingUser
import com.jd.oa.meeting.home.MeetingListRemoteDataSource
import com.jd.oa.meeting.home.post
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.preference.PreferenceManager
import kotlinx.coroutines.Dispatchers

import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.Locale

class MeetingDetailRemoteDataSource() : MeetingDetailDataSource {

    companion object {
        const val API_QUERY_MEETING_DETAIL = "meeting.queryMeetingDetail"//查询会议详情
        const val API_QUERY_JOIN_RECORD_LIST = "meeting.queryJoinRecordList"//出入会记录
        const val API_GET_MEETING_MATERIAL_DETAIL = "joyday.appointment.getMaterial"//附件列表
        const val API_QUERY_SCREEN_RECORD_LIST = "meeting.queryScreenRecordList"//屏幕录制列表
        const val API_QUERY_ALL_PARTICIPANTS = "meeting.queryAllParticipants"//查询参会成员列表
        const val API_QUERY_ALL_DEVICE_PARTICIPANTS = "meeting.proxyQueryAllDevPart"//参会ROOMS, SIP设备列表

        const val DEVICE_TYPE = "Android"
    }

    private val meetingPin: String by lazy {
        if (MultiAppConstant.isSaasFlavor()) {//Saas统一用UserId
            PreferenceManager.UserInfo.getUserId()
        } else {
            PreferenceManager.UserInfo.getUserName().toLowerCase(Locale.getDefault())
        }
    }

    override suspend fun getMeetingInfo(meetingId: String, meetingSelector: Int): Meeting {
        val params = mutableMapOf<String, Any>()
        params["meetingId"] = meetingId
        params["meetingSelector"] = meetingSelector
        params["userId"] = meetingPin
        params["deviceType"] = DEVICE_TYPE
        val response = post(null, params, API_QUERY_MEETING_DETAIL)
        try {
            val json = JSONObject(response)
            val code = json.optString("errorCode", "1")
            if (code != MeetingListRemoteDataSource.CODE_SUCCESS) throw HttpException("Request failed, code: $code")
            val result = json.optString("content")
            val meeting = Gson().fromJson<Meeting>(result, object : TypeToken<Meeting>() {}.type)

            return meeting
        } catch (e: Exception) {
            e.printStackTrace()
            throw HttpException(e)
        }
    }

    override suspend fun getAllParticipants(meetingId: String, meetingSelector: Int): MutableList<MeetingUser> {
        val params = mutableMapOf<String, Any>()
        params["meetingId"] = meetingId
        params["meetingSelector"] = meetingSelector
        params["userId"] = meetingPin
        params["deviceType"] = DEVICE_TYPE
        val response = post(null, params, API_QUERY_ALL_PARTICIPANTS)
        try {
            val json = JSONObject(response)
            val code = json.optString("errorCode", "1")
            if (code != MeetingListRemoteDataSource.CODE_SUCCESS) throw HttpException("Request failed, code: $code")
            val result = json.optJSONObject("content")
            val content = result?.optString("content")

            if (content.isNullOrEmpty()) return mutableListOf()

            val users = Gson().fromJson<MutableList<MeetingUser>>(content, object : TypeToken<MutableList<MeetingUser>>() {}.type)

            return users
        } catch (e: Exception) {
            e.printStackTrace()
            throw HttpException(e)
        }
    }

    /**
     * {
        "deviceType": "Android",
        "meetingId": "7b308566-de1d-43d1-952f-35eba7519633",
        "meetingSelector": 2,
        "pageNo": 1,
        "pageSize": 20,
        "userId": "sunwei145"
        }
     */
    override suspend fun getJoinRecords(meetingId: String, selector: Int, pageNo: Int, pageSize: Int): List<JoinRecord> {
        val params = mapOf<String,Any>(
            "deviceType" to "Android",
            "meetingId" to meetingId,
            "meetingSelector" to selector,
            "pageNo" to pageNo,
            "pageSize" to pageSize,
            "userId" to meetingPin
        )
        val response = post(null, params, API_QUERY_JOIN_RECORD_LIST)
        val attachments = withContext<List<JoinRecord>>(Dispatchers.Default) {
            return@withContext kotlin.runCatching {
                val json = JSONObject(response)
                val errorCode = json.optString("errorCode", "1")
                if (errorCode != MeetingListRemoteDataSource.CODE_SUCCESS) throw HttpException("Request failed, code: ${errorCode}")
                val result = json.optJSONObject("content")
                val list = result?.optString("content")

                if (list.isNullOrEmpty()) return@runCatching emptyList()

                return@runCatching Gson().fromJson<List<JoinRecord>>(list, object : TypeToken<List<JoinRecord>>(){}.type)
            }.getOrThrow()
        }
        return attachments
    }

    override suspend fun getAllDeviceParticipants(meetingId: String, meetingSelector: Int): MutableList<MeetingUser> {
        val params = mutableMapOf<String, Any>()
        params["meetingId"] = meetingId
        params["meetingSelector"] = meetingSelector
        params["appIds"] = ""
        params["userId"] = meetingPin
        params["deviceType"] = DEVICE_TYPE
        val response = post(null, params, API_QUERY_ALL_DEVICE_PARTICIPANTS)
        try {
            val json = JSONObject(response)
            val code = json.optString("errorCode", "1")
            if (code != MeetingListRemoteDataSource.CODE_SUCCESS) throw HttpException("Request failed, code: $code")
            val result = json.optJSONObject("content")
            val content = result.optString("content")
            val devices = Gson().fromJson<MutableList<MeetingUser>>(content, object : TypeToken<MutableList<MeetingUser>>() {}.type)

            return devices
        } catch (e: Exception) {
            e.printStackTrace()
            throw HttpException(e)
        }
    }

    override suspend fun getAppointmentAttachment(scheduleId: String, start: Long): List<MeetingAttachment> {
        val params = mapOf(
                "scheduleId" to scheduleId,
                "start" to start,
        )
        val response = post(null, params, API_GET_MEETING_MATERIAL_DETAIL)
        val attachments = withContext<List<MeetingAttachment>>(Dispatchers.Default) {
            return@withContext kotlin.runCatching {
                val json = JSONObject(response)
                val errorCode = json.optString("errorCode", "1")
                if (errorCode != MeetingListRemoteDataSource.CODE_SUCCESS) throw HttpException("Request failed, code: ${errorCode}")

                val list = mutableListOf<MeetingAttachment>()
                val gson = Gson()
                val content = json.getJSONObject("content")

                val meetingDoc = content.optJSONObject("meetingDoc")
                if (meetingDoc != null && meetingDoc.has("doc")) {
                    val doc = meetingDoc.getString("doc")
                    list.add(gson.fromJson(doc, JoyspaceDoc::class.java))
                }

                if (!content.isNull("joyspaceInfo")) {
                    val joyspaceInfo = content.getString("joyspaceInfo")
                    list.addAll(gson.fromJson(joyspaceInfo, object : TypeToken<List<JoyspaceDoc>>(){}.type))
                }

                if (!content.isNull("attachment")) {
                    val attachment = content.getString("attachment")
                    list.addAll(gson.fromJson(attachment, object : TypeToken<List<FileAttachment>>(){}.type))
                }

                return@runCatching list
            }.getOrThrow()
        }
        return attachments
    }

    override suspend fun getNotes(meetingId: String, meetingSelector: Int): List<MeetingNote> {
        val params = mutableMapOf<String, Any>()
        params["meetingId"] = meetingId
        params["meetingSelector"] = meetingSelector
        params["userId"] = meetingPin
        params["deviceType"] = DEVICE_TYPE
        return kotlin.runCatching {
            val response = post(null, params, API_QUERY_SCREEN_RECORD_LIST)
            val json = JSONObject(response)
            val errorCode = json.optString("errorCode", "1")
            if (errorCode != "0") throw HttpException("Request failed, code: $errorCode")
            val result = json.optJSONObject("content") ?: return@runCatching emptyList<MeetingNote>()
            if (!result.has("content") || result.isNull("content")) {
                return@runCatching emptyList<MeetingNote>()
            }
            val content = result.optString("content")
            val notes = Gson().fromJson<MutableList<MeetingNote>>(content, object : TypeToken<MutableList<MeetingNote>>() {}.type)
            notes
        }.getOrThrow()
    }
}