package com.jd.oa.meeting

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.home.ActionItem
import com.jd.oa.meeting.home.LocalCacheStorage
import com.jd.oa.meeting.home.feedback.FeedbackItemData
import com.jd.oa.storage.UseType
import com.jd.oa.storage.entity.AbsKvEntities
import com.jd.oa.storage.entity.KvEntity

class MeetingPreference(@JvmField val context: Context): AbsKvEntities(), LocalCacheStorage {

    companion object {
        private val actionList: KvEntity<String?> = KvEntity<String?>("action_items", "", UseType.TENANT)
    }

    override fun getPrefrenceName() = "JDME_MEETING"

    override fun getDefaultUseType() = UseType.TENANT

    override fun getContext() = context

    override fun putActionList(list: List<ActionItem>) {
        val value = Gson().toJson(list)
        put(actionList, value)
    }

    override fun putAppointmentList(list: List<Meeting>) {
        TODO("Not yet implemented")
    }

    override fun getAppointmentList(): List<Meeting> {
        TODO("Not yet implemented")
    }

    override fun putHistoryList(history: List<Meeting>) {
        TODO("Not yet implemented")
    }

    override fun getHistoryList(): List<Meeting> {
        TODO("Not yet implemented")
    }

    override fun putFeedbackItems(list: List<FeedbackItemData<*>>) {
        TODO("Not yet implemented")
    }

    override fun getFeedbackItems(): List<FeedbackItemData<*>>? {
        TODO("Not yet implemented")
    }
}