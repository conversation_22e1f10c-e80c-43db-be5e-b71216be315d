package com.jd.oa.meeting

import android.content.Context
import com.jd.oa.AppBase
import com.jd.oa.utils.LocaleUtils
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

private var monthFormat: SimpleDateFormat? = null
private var yearMonthFormat: SimpleDateFormat? = null
private var timeFormat: SimpleDateFormat? = null

fun resetDateFormat() {
    monthFormat = null
    yearMonthFormat = null
    timeFormat = null
}

fun formatDuration(context: Context, duration: Long?): String {
    if (duration == null || duration < 0) return "--"

    val inSeconds = duration / 1000
    val hour = inSeconds / 60 / 60
    val minutes = inSeconds / 60 % 60
    val seconds = inSeconds % 60

    val sb = StringBuilder()

    if (hour > 0) {
        sb.append(hour)
        sb.append(context.getString(R.string.meeting_hour))
    }
    if (minutes > 0) {
        sb.append(minutes)
        sb.append(context.getString(R.string.meeting_minutes))
    }
    if (seconds > 0) {
        sb.append(seconds)
        sb.append(context.getString(R.string.meeting_second))
    }
    if (sb.isEmpty()) return "--"

    return sb.toString()
}
fun formatDate(context: Context, date: Calendar): String {
    val today = Calendar.getInstance()
    if (isSameDay(today, date)) return context.getString(R.string.meeting_today)

    val tomorrow = (today.clone() as Calendar).apply {
        add(Calendar.DAY_OF_MONTH, 1)
    }
    if (isSameDay(tomorrow, date)) return context.getString(R.string.meeting_tomorrow)

    val yesterday = (today.clone() as Calendar).apply {
        add(Calendar.DAY_OF_MONTH, -1)
    }
    if (isSameDay(yesterday, date)) return context.getString(R.string.meeting_yesterday)

    if (isSameYear(today, date)) {
        if (monthFormat == null) {
            monthFormat = SimpleDateFormat(context.getString(R.string.meeting_date_format_month), Locale.getDefault())
        }
        return monthFormat!!.format(date.time)
    }
    if (yearMonthFormat == null) {
        yearMonthFormat = SimpleDateFormat(context.getString(R.string.meeting_date_format_year), Locale.getDefault())
    }
    return yearMonthFormat!!.format(date.time)
}
fun formatTime(context: Context, time: Calendar): String {
    if (timeFormat == null) {
        timeFormat = SimpleDateFormat(context.getString(R.string.meeting_date_format_time), Locale.getDefault())
    }
    return timeFormat?.format(time.time) ?: ""
}


fun formatStartAndEnd(context: Context, start: Calendar?, end: Calendar?): String {
    if (start == null) return ""
    if (end == null) {
        return formatDate(context, start) + " " + formatTime(context, start)
    }
    return if (isSameDay(start, end)) {
        formatDate(context, start) + " " + formatTime(context, start) + "-" + formatTime(context, end)
    } else {
        formatDate(context, start) + " " + formatTime(context, start) + "-" +
                formatDate(context, end) + " " + formatTime(context, end)
    }
}
private fun isSameDay(date1: Calendar, date2: Calendar): Boolean {
    return isSameMonth(date1, date2) &&
            date1.get(Calendar.DAY_OF_MONTH) == date2.get(Calendar.DAY_OF_MONTH)
}

private fun isSameMonth(date1: Calendar, date2: Calendar): Boolean {
    return date1.get(Calendar.YEAR) == date2.get(Calendar.YEAR) &&
            date1.get(Calendar.MONTH) == date2.get(Calendar.MONTH)
}
private fun isSameYear(date1: Calendar, date2: Calendar): Boolean {
    return date1.get(Calendar.YEAR) == date2.get(Calendar.YEAR)
}

fun getDateFromDateString(inputDate: String?): String {
    val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
    var outputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    return if (inputDate.isNullOrEmpty()) {
        ""
    } else if ("en_US" == LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext())) {
        outputFormat.format(inputFormat.parse(inputDate))
    } else {
        outputFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
        val date = inputFormat.parse(inputDate)
        outputFormat.format(date)
    }
}

fun getTimeFromDateString(dateString: String?): String {
    return if (dateString.isNullOrEmpty()) {
        ""
    } else {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
        val outputFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        val time = inputFormat.parse(dateString)
        outputFormat.format(time)
    }
}