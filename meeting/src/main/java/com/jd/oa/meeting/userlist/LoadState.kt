package com.jd.oa.meeting.userlist

class LoadState<out T> internal constructor(internal val value: Any?) {
    val success: <PERSON>olean
        get() = null != value && value !== Loading && value !is Error
    val loading: Boolean
        get() = value === Loading
    val failure: <PERSON><PERSON><PERSON>
        get() = value is Error

    fun onSuccess(block: (T?) -> Unit): LoadState<T> {
        if (success) {
            block.invoke(value as? T)
        }
        return this
    }

    fun onFailure(block: (Throwable?) -> Unit): LoadState<T> {
        if (failure) {
            block.invoke((value as? Error)?.cause)
        }
        return this
    }

    fun onLoading(block: () -> Unit): LoadState<T> {
        if (loading) {
            block.invoke()
        }
        return this
    }

    fun getOrNull(): T? {
        return value.takeIf { success } as? T
    }

    internal object Loading
    internal class Error(val cause: Throwable?)

    companion object {
        fun <T> success(value: T?): LoadState<T> = LoadState(value)
        fun <T> failure(cause: Throwable? = null): LoadState<T> = LoadState(Error(cause))
        fun <T> loading(): LoadState<T> = LoadState(Loading)
    }
}