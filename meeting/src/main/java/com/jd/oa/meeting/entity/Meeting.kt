package com.jd.oa.meeting.entity

import android.os.Parcelable
import androidx.annotation.Keep
import com.jd.oa.utils.JsonUtils
import kotlinx.android.parcel.Parcelize
import kotlinx.parcelize.IgnoredOnParcel
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.TimeZone

abstract class MeetingListItem(
    open var id: String,
    open var title: String?
)

@Keep
@Parcelize
data class Meeting(
    var meetingId: String,
    var meetingCode: Long? = null,
    var peerId: Long? = null,
    var hoster: MeetingUser? = null,
    var subHost: List<MeetingUser>? = null,
    var creator: MeetingUser? = null,
    var subject: String? = null,
    var password: String? = null,
    val meetingType: Int? = TYPE_INSTANT,
    var status: Int? = 0,
    val totalNum: Int? = 0,
    var startTime: String? = null,
    var endTime: String? = null,
    val advanceRemindTime: String? = null,
    val settings: Setting? = null,
    val isHistory: Boolean? = false,
    val duration: Long? = 0,
    val realJoinTime: String? = null,
    val realStartTime: String? = null,
    val realEndTime: String? = null,
    val scheduleStartTime: String? = null,
    var recordStatus: Int? = RECORD_STATUS_IDLE,    //录制状态：0未开启，1已开启
    var recordPeerId: Long? = null,
    var businessType: Int? = null,
    val realMeetingStatus: Int? = 0, //会议实际状态。status只是兼容老版本。-1 -已取消 0-未开始 1-进行中 2-已结束 10-即将开始
    val createEntrance:Int? = 0,
    var meetingSelector:Int? = 2,
    val scheduleId:String? = null,
    val userPin:String? = null,
    val calendarId:String? = null,
    val asrStatus:Int? = 0,     //ASR状态：0未开启，1已开启
    val joinLink:String? = null,
    val joinPhoneNumber: String? = null
) : MeetingListItem(meetingId, subject), Parcelable {

    @JvmOverloads
    constructor() : this(meetingId = "")

    var joined: Boolean = false

    fun canAttend(): Boolean =
        STATUS_CREATED == status || STATUS_PROCESSING == status || STATUS_BEGINNING == status

    fun isCalendar(): Boolean =
        !settings?.calendarId.isNullOrEmpty() && !settings?.scheduleId.isNullOrEmpty()

    val isSingle get() = businessType == BUSINESS_TYPE_SINGLE

    val isOnGoing get() = status == STATUS_PROCESSING

    val isEnd get() = status == STATUS_END

    val isCanceled get() = status == STATUS_CANCELED

    val isBeginning get() =  status == STATUS_BEGINNING

    val isCreated get() =  status == STATUS_CREATED


    @IgnoredOnParcel
    var startTimeCalendar: Calendar? = null
    @IgnoredOnParcel
    val startTimeInCalendar: Calendar?
        get() {
            if (startTimeCalendar == null) {
                startTimeCalendar = parseTime(startTime)
            }
            return startTimeCalendar
        }

    @IgnoredOnParcel
    var endTimeCalendar: Calendar? = null
    @IgnoredOnParcel
    val endTimeInCalendar: Calendar?
        get() {
            if (endTimeCalendar == null) {
                endTimeCalendar = parseTime(endTime)
            }
            return endTimeCalendar
        }

    @IgnoredOnParcel
    var realJoinTimeCalendar: Calendar? = null
    @IgnoredOnParcel
    val realJoinTimeInCalendar: Calendar?
        get() {
            if (realJoinTimeCalendar == null) {
                realJoinTimeCalendar = parseTime(realJoinTime)
            }
            return realJoinTimeCalendar
        }

    @IgnoredOnParcel
    var realEndTimeCalendar: Calendar? = null
    @IgnoredOnParcel
    val realEndTimeInCalendar: Calendar?
        get() {
            if (realEndTimeCalendar == null) {
                realEndTimeCalendar = parseTime(realEndTime)
            }
            return realEndTimeCalendar
        }

    @IgnoredOnParcel
    var realStartTimeCalendar: Calendar? = null
    @IgnoredOnParcel
    val realStartTimeInCalendar: Calendar?
        get() {
            if (realStartTimeCalendar == null) {
                realStartTimeCalendar = parseTime(realStartTime)
            }
            return realStartTimeCalendar
        }

    @IgnoredOnParcel
    var scheduleStartTimeCalendar: Calendar? = null
    @IgnoredOnParcel
    val scheduleStartTimeInCalendar: Calendar?
        get() {
            if (scheduleStartTimeCalendar == null) {
                scheduleStartTimeCalendar = parseTime(scheduleStartTime)
            }
            return scheduleStartTimeCalendar
        }

    private fun parseTime(timeString: String?): Calendar? {
        if (timeString == null) return null
        return dateFormat.parse(timeString)?.let {
            Calendar.getInstance().apply { time = it }
        }
    }

    fun getDesktopUrl() :String{
        val json = joinLink?.replace("\\", "")
        val map = JsonUtils.fromJson(json)
        return map["desktopUrl"].toString()

    }

    fun resetCalendarObj() {
        this.startTimeCalendar = null
        this.endTimeCalendar = null
        this.realJoinTimeCalendar = null
        this.realEndTimeCalendar = null
        this.realStartTimeCalendar = null
        this.scheduleStartTimeCalendar = null
    }

    companion object {

        const val TYPE_INSTANT = 0
        const val TYPE_SCHEDULE = 1
        const val TYPE_DONGDONG_INSTANT = 2

        const val BUSINESS_TYPE_SINGLE = 0
        const val BUSINESS_TYPE_GROUP = 1

        /**
         * 未开始
         */
        const val STATUS_CREATED = 0

        /**
         * 进行中
         */
        const val STATUS_PROCESSING = 1

        /**
         * 已结束
         */
        const val STATUS_END = 2

        /**
         * 已取消
         */
        const val STATUS_CANCELED = -1

        /**
         * 即将开始
         */
        const val STATUS_BEGINNING = 10

        /**
         * 屏幕录制中
         */
        const val RECORD_STATUS_RECORDING = 1

        /**
         * 未录制
         */
        const val RECORD_STATUS_IDLE = 2

        const val RECORD_STATUS_ON = 1
        const val RECORD_STATUS_CLOSE = 0

        const val ASR_STATUS_ON = 1
        const val ASR_STATUS_CLOSE = 0

        const val MEETING_TYPE_TIMLINE = 1
        const val MEETING_TYPE_JDME = 2
        const val MEETING_TYPE_JOYMEETING = 3

        val dateFormat: DateFormat by lazy {
            SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").apply {
                timeZone = TimeZone.getTimeZone("UTC")
            }
        }

        val comparator = Comparator<Meeting> { v1, v2 ->
            if (v1.isOnGoing) return@Comparator -1
            return@Comparator v1.startTimeInCalendar?.timeInMillis?.compareTo(v2.startTimeInCalendar?.timeInMillis ?: 0) ?: 0
        }
    }
}