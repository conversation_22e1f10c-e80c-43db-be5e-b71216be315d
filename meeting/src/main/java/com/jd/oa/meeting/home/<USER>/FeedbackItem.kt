package com.jd.oa.meeting.home.feedback

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import com.jd.oa.meeting.R

class FeedbackItem @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleRes: Int = 0
): FrameLayout(context, attrs, defStyleRes) {

    var mTvName: TextView
    var mActionContainer: ViewGroup

    var onJoinGroupClickListener: ((groupId: String) -> Unit)? = null
    var onLinkClickListener: ((groupId: String?) -> Unit)? = null
    var onTextClickListener: ((text: String) -> Unit)? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.meeting_feedback_item, this)
        mTvName = findViewById(R.id.tv_name)
        mActionContainer = findViewById(R.id.layout_action)
    }

    @SuppressLint("SetTextI18n")
    fun setItem(item: FeedbackItemData<*>) {
        mTvName.text = String.format("%s：", item.name?.getText(context))
        mActionContainer.removeAllViews()

        if (item.type ==  FeedbackItemData.TYPE_JOIN_GROUP) {
            @Suppress("UNCHECKED_CAST")
            (item.value as? ArrayList<String>)?.forEachIndexed { index, s ->
                val text = TextView(context, null, 0, R.style.MeetingFeedbackActionText)
                if (index < item.value.size - 1) {
                    text.text = "$s,"
                } else {
                    text.text = s
                }
                text.setOnClickListener { onJoinGroupClickListener?.invoke(s) }
                mActionContainer.addView(text)
            }
        } else if (item.type ==  FeedbackItemData.TYPE_LINK) {
            @Suppress("UNCHECKED_CAST")
            (item.value as? ArrayList<LinkValue>)?.forEachIndexed { index, s ->
                val text = TextView(context, null, 0, R.style.MeetingFeedbackActionText)
                if (index < item.value.size - 1) {
                    text.text = "${s.name?.getText(context)},"
                } else {
                    text.text = s.name?.getText(context)
                }
                text.setOnClickListener { onLinkClickListener?.invoke(s.link) }
                mActionContainer.addView(text)
            }
        } else if (item.type ==  FeedbackItemData.TYPE_TEXT) {
            mTvName.text = item.name?.getText(context)
            item.name?.getText(context)?.let { s ->
                mTvName.setOnLongClickListener {
                    onTextClickListener?.invoke(s)
                    true
                }
            }
        }
    }
}