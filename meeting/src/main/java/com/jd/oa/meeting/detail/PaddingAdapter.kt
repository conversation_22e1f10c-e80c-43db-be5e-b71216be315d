package com.jd.oa.meeting.detail

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.meeting.R

class PaddingAdapter(val context: Context, @ColorInt val color: Int = Color.WHITE, val height: Int): RecyclerView.Adapter<PaddingAdapter.ViewHolder>() {

    private val background: ColorDrawable = ColorDrawable(color)

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): ViewHolder {
        return ViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_activity_detail_bottom, p0, false))
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, p1: Int) {
        viewHolder.padding.background = background
        val layoutParams = viewHolder.padding.layoutParams
        layoutParams.height = height
        viewHolder.padding.layoutParams = layoutParams
    }

    override fun getItemCount(): Int = 1

    class ViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {

        val padding: View by lazy { itemView.findViewById(R.id.padding) }
    }
}