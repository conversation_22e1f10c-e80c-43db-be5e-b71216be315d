package com.jd.oa.meeting.userlist

import android.os.Looper
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.InvocationKind
import kotlin.contracts.contract

/**
 * 线程安全的更新[MutableLiveData]数据的扩展方法，如果仅仅是使用原来的值通知所有的[Observer]，无须传参
 * [pendingValue]要更新的值，默认为原始值
 * [applyBlockWhenNotNullBeforeUpdate]当要更新的值不为空的时候，在更新前可以对该对象进行处理
 */
fun <T : E?, E : Any> MutableLiveData<T>.updateValue(
    pendingValue: T? = value,
    applyBlockWhenNotNullBeforeUpdate: (E.() -> Unit)? = null
) {
    if (null != applyBlockWhenNotNullBeforeUpdate) {
        pendingValue?.applyBlockWhenNotNullBeforeUpdate()
    }
    if (Looper.getMainLooper() === Looper.myLooper()) {
        value = pendingValue
    } else {
        postValue(pendingValue)
    }
}