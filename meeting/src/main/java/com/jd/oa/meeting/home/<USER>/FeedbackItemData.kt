package com.jd.oa.meeting.home.feedback

import android.content.Context
import android.os.Parcelable
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.utils.LocaleUtils
import kotlinx.android.parcel.Parcelize
import org.json.JSONArray
import java.io.Serializable

@Parcelize
class FeedbackItemData<T: Serializable>(
    val type: String?,
    val name: FeedbackText?,
    val value: ArrayList<T>,
) : Parcelable {
    companion object {
        const val TYPE_JOIN_GROUP = "joinGroup"
        const val TYPE_LINK = "link"
        const val TYPE_TEXT = "text"
    }
}

class FeedbackText(val zhCN: String?, val enUS: String?): Serializable

class LinkValue(
    val name: FeedbackText?,
    val link: String?,
): Serializable

fun FeedbackText.getText(context: Context): String? {
    return if (LocaleUtils.getUserSetLocaleStr(context).lowercase().startsWith("zh")) {
        zhCN
    } else {
        enUS
    }
}

class FeedBackItemDataDeserializer(val list: String) {

    val gson = Gson()
    private val helpList: JSONArray = JSONArray(list)

    fun deserializer(): List<FeedbackItemData<*>> {
        val list = mutableListOf<FeedbackItemData<*>>()
        for (i in 0 until helpList.length()) {
            val item = helpList.getJSONObject(i)
            val type = item.getString("type")
            if (type == FeedbackItemData.TYPE_JOIN_GROUP || type == FeedbackItemData.TYPE_TEXT) {
                val value = gson.fromJson<FeedbackItemData<String>>(helpList.getString(i), object : TypeToken<FeedbackItemData<String>>(){}.type)
                list.add(value)
            } else if (type == FeedbackItemData.TYPE_LINK) {
                val value = gson.fromJson<FeedbackItemData<LinkValue>>(helpList.getString(i), object : TypeToken<FeedbackItemData<LinkValue>>(){}.type)
                list.add(value)
            }
        }
        return list
    }
}