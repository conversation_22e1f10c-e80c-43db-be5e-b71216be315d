package com.jd.oa.meeting.entity

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import java.util.Calendar
import java.util.Objects

/**
 * {
"bizId":"文档ID",
"bizType":1,
"bizName":"文档名称",
"projectId":"投屏ID",
"projectStatus":1,
"projectStartAt":1704953980000,
"meetingRoomId":"会议室ID",
"meetingRoomName":"会议室名称",
"screenSn": "大屏SN码",
"link": "投屏链接地址"
}
 */
@Parcelize
data class ProjectionDocument(
    val bizId: String,
    val bizName: String? = null,
    val bizType: Int? = null,
    val link: String? = null,
    val meetingRoomId: String? = null,
    val meetingRoomName: String? = null,
    val projectId: String? = null,
    val projectStartAt: Long? = null,
    val projectStatus: Int? = null,
    val screenSn: String? = null
): MeetingListItem(bizId, bizName), Parcelable {

    companion object {
        const val STATUS_END = 0
        const val STATUS_ONGOING = 1
    }

    @JvmOverloads
    constructor(): this(bizId = "")

    val isOngoing: Boolean
        get() = projectStatus == STATUS_ONGOING

    val isEnd: Boolean
        get() = projectStatus == STATUS_END

    @delegate:Transient
    val projectStartAtInCalendar: Calendar? by lazy {
        if (projectStartAt == null || projectStartAt == 0L) return@lazy null
        Calendar.getInstance().apply { timeInMillis = projectStartAt }
    }

    override fun equals(other: Any?): Boolean {
        if (other !is ProjectionDocument) return false
        return this.bizId == other.bizId && this.projectId == other.projectId
    }

    override fun hashCode(): Int {
        return Objects.hash(this.bizId, this.projectId)
    }
}