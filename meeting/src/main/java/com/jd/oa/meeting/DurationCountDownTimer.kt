package com.jd.oa.meeting

import android.os.CountDownTimer


typealias OnTick = (countDownTime: Long) -> Unit

class DurationCountDownTimer() {

    companion object {
        const val COUNT_DOWN_TIME = 1000 * 60 * 60 * 12
    }

    private val onTickListener: MutableList<OnTick> = mutableListOf()

    private val timer: CountDownTimer = object : CountDownTimer(COUNT_DOWN_TIME.toLong(), 1000) {

        override fun onTick(millisUntilFinished: Long) {
            onTickListener.forEach {
                it.invoke(COUNT_DOWN_TIME - millisUntilFinished)
            }
        }

        override fun onFinish() {
            onTickListener.clear()
        }
    }

    private var start: Boolean = false
    val isStart: Boolean get() = start

    fun start() {
        timer.start()
        this.start = true
    }

    fun cancel() {
        timer.cancel()
        this.start = false
    }

    fun addOnTickListener(listener: OnTick) {
        if (!onTickListener.contains(listener)) {
            onTickListener.add(listener)
        }
    }

    fun removeOnTickListener(listener: OnTick) {
        onTickListener.remove(listener)
    }

    fun clearOnTickListener() {
        onTickListener.clear()
    }
}