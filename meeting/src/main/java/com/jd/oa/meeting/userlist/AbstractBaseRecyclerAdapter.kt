package com.jd.oa.meeting.userlist

import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView

/**
 * Created by TangKe
 */
abstract class AbstractBaseRecyclerAdapter<D, VH : AbstractViewHolder<*>>(private val itemClick: ((VH, Int) -> Unit)? = null) :
    RecyclerView.Adapter<VH>() {
    val showingData = mutableListOf<D>()

    override fun getItemCount() = showingData.size

    operator fun get(position: Int): D = showingData[position]

    private val internalOnItemClickListener: (AbstractViewHolder<*>, Int) -> Unit = { holder, id ->
        itemClick?.invoke(holder as VH, id)
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        holder.data = this[position]
        holder.itemView.setOnClickListener(holder.onItemClickListener)
        holder.outerOnItemClickListener = internalOnItemClickListener
        kotlin.runCatching {
            holder.onBind()
        }
    }

    override fun onViewDetachedFromWindow(holder: VH) {
        holder.onUnbind()
    }

    fun setAllData(data: Collection<D>?) {
        val oldData = mutableListOf<D>()
        oldData += this.showingData
        val newData = mutableListOf<D>()
        if (!data.isNullOrEmpty()) {
            newData += data
        }
        val result = DiffUtil.calculateDiff(DiffCallback(this, oldData, newData))
        showingData.clear()
        if (null != data) {
            showingData += data
        }
        result.dispatchUpdatesTo(this)
    }

    fun addAllData(data: Collection<D>?) {
        val showingDataSize = showingData.size
        if (null != data) {
            showingData += data
            notifyItemRangeInserted(showingDataSize, data.size)
        }
    }

    fun addAllData(index: Int, data: Collection<D>?) {
        if (null != data) {
            this.showingData.addAll(index, data)
            notifyItemRangeInserted(index, data.size)
        }
    }

    fun addData(data: D) {
        this.showingData += data
        notifyItemInserted(showingData.size - 1)
    }

    open fun areItemsTheSame(oldItem: D, newItem: D): Boolean = false
    open fun areContentsTheSame(oldItem: D, newItem: D): Boolean = false
}

abstract class AbstractViewHolder<out D>(itemView: View) : RecyclerView.ViewHolder(itemView) {
    internal var data: Any? = null
    internal var outerOnItemClickListener: ((AbstractViewHolder<*>, Int) -> Unit)? = null
    internal val onItemClickListener = View.OnClickListener { performClick(itemView) }
    protected var context = itemView.context

    fun data(): D = data as D

    abstract fun onBind()
    open fun onUnbind() {}
    protected fun performClick(view: View) {
        outerOnItemClickListener?.invoke(this, view.id)
    }
}

internal class DiffCallback<D>(
    private val adapter: AbstractBaseRecyclerAdapter<D, *>,
    private val oldData: List<D>,
    private val newData: List<D>
) :
    DiffUtil.Callback() {
    override fun getOldListSize(): Int = oldData.size

    override fun getNewListSize(): Int = newData.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean =
        adapter.areItemsTheSame(oldData[oldItemPosition], newData[newItemPosition])

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean =
        adapter.areContentsTheSame(oldData[oldItemPosition], newData[newItemPosition])
}