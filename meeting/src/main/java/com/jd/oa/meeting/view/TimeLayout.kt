package com.jd.oa.meeting.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.core.view.marginBottom
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import androidx.core.view.marginTop
import com.jd.oa.meeting.R
import kotlin.math.max
import kotlin.math.min

class TimeLayout
@JvmOverloads
constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleRes: Int = 0
): ViewGroup(context, attrs, defStyleRes) {

    var singleLine: Boolean = true
    private var singleLineHeight: Int? = null

    override fun onViewAdded(child: View?) {
        super.onViewAdded(child)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)

        var height = 0
        var width = 0
        var maxHeight = 0
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            val layoutParams = child.layoutParams as LayoutParams
            measureChildWithMargins(child, widthMeasureSpec, 0, heightMeasureSpec, 0)
            width += child.measuredWidth + layoutParams.marginStart + layoutParams.marginEnd
            maxHeight = max(child.measuredHeight + layoutParams.topMargin + layoutParams.bottomMargin, maxHeight)
        }

        height = if (width > widthSize) {
            singleLine = false
            singleLineHeight = maxHeight
            var totalHeight = 0
            for (i in 0 until childCount) {
                val child = getChildAt(i)
                val layoutParams = child.layoutParams as LayoutParams
                totalHeight += if (layoutParams.type == LayoutParams.TYPE_VIEW) {
                    child.measuredHeight + layoutParams.topMargin + layoutParams.bottomMargin
                } else {
                    0
                }
            }
            totalHeight
        } else {
            singleLine = true
            singleLineHeight = maxHeight
            maxHeight
        }
        setMeasuredDimension(widthSize, height)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        var width = 0
        var height = 0
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            val layoutParams = child.layoutParams as LayoutParams
            if (singleLine) {
                if (layoutParams.type == LayoutParams.TYPE_DIVIDER) {
                    child.visibility = View.VISIBLE
                }
                if (child.visibility == View.GONE) {
                    continue
                }
                val realWidth = (min(child.measuredWidth , measuredWidth - width - layoutParams.marginStart - layoutParams.marginEnd))
                child.layout(width + child.marginStart, height + child.marginTop, width + child.marginStart + realWidth, height + child.measuredHeight + child.marginTop)
                width += realWidth + child.marginStart + child.marginEnd
            } else {
                if (layoutParams.type == LayoutParams.TYPE_DIVIDER) {
                    child.visibility = View.GONE
                    continue
                }
                child.layout(width + child.marginStart, height + child.marginTop, width + child.marginStart + child.measuredWidth , height + child.marginTop + child.measuredHeight )
                height += child.measuredHeight + child.marginTop + child.marginBottom
            }
        }
    }

    override fun generateLayoutParams(attrs: AttributeSet?): ViewGroup.LayoutParams {
        return LayoutParams(context, attrs)
    }

    override fun generateLayoutParams(p: ViewGroup.LayoutParams?): ViewGroup.LayoutParams {
        return LayoutParams(p)
    }

    override fun generateDefaultLayoutParams(): ViewGroup.LayoutParams {
        return LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    class LayoutParams: MarginLayoutParams {

        companion object {
            const val TYPE_VIEW = 1
            const val TYPE_DIVIDER = 2
        }

        val type: Int

        constructor(layoutParams: ViewGroup.LayoutParams?) : super(layoutParams) {
            this.type = TYPE_VIEW
        }

        constructor(width: Int, height: Int): super(width, height) {
            this.type = TYPE_VIEW
        }

        constructor(context: Context, attrs: AttributeSet?): super(context, attrs) {
            val typedValue = context.obtainStyledAttributes(attrs, R.styleable.TimeLayout_Layout)
            type = typedValue.getInt(R.styleable.TimeLayout_Layout_childType, TYPE_VIEW)
            typedValue.recycle()
        }
    }
}