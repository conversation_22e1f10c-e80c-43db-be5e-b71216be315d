package com.jd.oa.meeting.home

import com.jd.oa.AppBase
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.ProjectionDocument
import com.jd.oa.meeting.home.feedback.FeedbackItemData
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.network.httpmanager.CancelTag
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import kotlin.coroutines.resumeWithException

class MeetingRepository(val storage: LocalCacheStorage, val remoteDataSource: MeetingListDataSource): MeetingListDataSource {

    companion object {
        const val TAG = "MeetingRepository"
    }

    private val scope = CoroutineScope(CoroutineName("MeetingRepository"))

    override suspend fun getActionList(noCache: Boolean): Flow<List<ActionItem>> = flow {
        val tabList = mutableListOf<ActionItem>()
        if(TenantConfigBiz.isFailedRequestConfig()){
            //配置接口请求失败，本地无缓存，显示默认值
            tabList.add(ActionItem(ActionItem.TYPE_START_MEETING, (AppBase.getAppContext().getString(R.string.meeting_tab_action_call)), R.drawable.meeting_start))
            tabList.add(ActionItem(ActionItem.TYPE_JOIN_MEETING, AppBase.getAppContext().getString(R.string.meeting_tab_action_join), R.drawable.meeting_join))
            if (!MultiAppConstant.isSaasFlavor()) {//京me默认多-预约会议
                tabList.add(ActionItem(ActionItem.TYPE_APPOINTMENT, (AppBase.getAppContext().getString(R.string.meeting_tab_action_reserve)), R.drawable.meeting_reserve))
            }
        } else {
            //有本地缓存/配置接口成功，取config值
            if (TenantConfigBiz.isStartMeeting()) {
                tabList.add(ActionItem(ActionItem.TYPE_START_MEETING, (AppBase.getAppContext().getString(R.string.meeting_tab_action_call)), R.drawable.meeting_start))
            }
            if (TenantConfigBiz.isJoinMeeting()) {
                tabList.add(ActionItem(ActionItem.TYPE_JOIN_MEETING, AppBase.getAppContext().getString(R.string.meeting_tab_action_join), R.drawable.meeting_join))
            }
            if (TenantConfigBiz.isAppointment()) {
                tabList.add(ActionItem(ActionItem.TYPE_APPOINTMENT, (AppBase.getAppContext().getString(R.string.meeting_tab_action_reserve)), R.drawable.meeting_reserve))
            }
            if (TenantConfigBiz.isNote()) {
                tabList.add(ActionItem(ActionItem.TYPE_NOTE, (AppBase.getAppContext().getString(R.string.meeting_tab_action_note)), R.drawable.meeting_note))
            }
            if (TenantConfigBiz.isScan()) {
                tabList.add(ActionItem(ActionItem.TYPE_SCAN, (AppBase.getAppContext().getString(R.string.meeting_tab_action_scan)), R.drawable.meeting_scan))
            }
        }
        emit(tabList)
    }

    override suspend fun getHistoryMeetings(pageNumber: Int, pageSize: Int): PagedList<Meeting> {
        return remoteDataSource.getHistoryMeetings(pageNumber, pageSize)
    }

    override suspend fun getAppointmentMeetings(): List<Meeting> {
        return remoteDataSource.getAppointmentMeetings()
    }

    override suspend fun getProjectionDocuments(): List<ProjectionDocument> {
        return remoteDataSource.getProjectionDocuments()
    }

    override suspend fun quitScreenProjection(document: ProjectionDocument): Boolean {
        return remoteDataSource.quitScreenProjection(document)
    }

    override suspend fun getFeedbackItems(): List<FeedbackItemData<*>> {
        return runCatching {
            val list = withTimeout(1000) {
                remoteDataSource.getFeedbackItems()
            }
            list
        }.onSuccess {
            storage.putFeedbackItems(it)
        }.getOrElse { storage.getFeedbackItems() ?: throw it }
    }
}

interface LocalCacheStorage {
    fun putActionList(list: List<ActionItem>)

    fun putAppointmentList(list: List<Meeting>)

    fun getAppointmentList(): List<Meeting>?

    fun putHistoryList(history: List<Meeting>)

    fun getHistoryList(): List<Meeting>?

    fun putFeedbackItems(list: List<FeedbackItemData<*>>)

    fun getFeedbackItems(): List<FeedbackItemData<*>>?
}

suspend fun post(obj: CancelTag?, params: Map<String,Any>, action: String) = suspendCancellableCoroutine<String?> { continuation ->
    HttpManager.color().post(params,null,action,object: SimpleRequestCallback<String?>() {

        override fun onFailure(exception: HttpException?, info: String?) {
            super.onFailure(exception, info)
            continuation.resumeWithException(exception ?: HttpException(info))
        }

        override fun onSuccess(info: ResponseInfo<String?>?) {
            super.onSuccess(info)
            if (info?.result != null) {
                continuation.resumeWith(Result.success(info.result))
            } else {
                continuation.resumeWithException(Exception(info?.result))
            }
        }
    })
    obj?.let {
        continuation.invokeOnCancellation {
            HttpManager.cancel(obj)
        }
    }
}