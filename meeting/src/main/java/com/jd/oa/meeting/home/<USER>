package com.jd.oa.meeting.home

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.meeting.R
import com.jd.oa.utils.ImageLoader
import com.jd.oa.utils.LocaleUtils
import java.util.*

class ActionsAdapter(
    val context: Context,
    val items: MutableList<ActionItem>,
    val onClickListener: (View, ActionItem) -> Unit
    ): RecyclerView.Adapter<ActionsAdapter.ViewHolder>() {

    private val isZh: Boolean
    init {
        val locale = LocaleUtils.getUserSetLocaleStr(context)
        isZh = locale.startsWith("zh")
    }

    override fun onCreateViewHolder(parent: ViewGroup, position: Int): ViewHolder {
        return ViewHolder(LayoutInflater.from(context).inflate(R.layout.meeting_tab_top_action, parent, false))
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        val item = items[position]
        bindText(item, viewHolder)
        bindImage(item, viewHolder)
    }
    private fun bindText(item: ActionItem, viewHolder: ViewHolder) {
        val text: String? = if (!TextUtils.isEmpty(item.title)) {
            item.title
        } else if (isZh && !TextUtils.isEmpty(item.name)) {
            item.name
        } else if (!isZh && !TextUtils.isEmpty(item.enName)) {
            item.enName
        } else if (item.isAppointment) {
            context.getString(R.string.meeting_tab_action_reserve)
        } else if (item.isStartMeeting) {
            context.getString(R.string.meeting_tab_action_call)
        } else if (item.isJoinMeeting) {
            context.getString(R.string.meeting_tab_action_join)
        } else if (item.isNote) {
            context.getString(R.string.meeting_tab_action_note)
        } else {
            null
        }
        viewHolder.title.text = text
    }

    private fun bindImage(item: ActionItem, viewHolder: ViewHolder) {
        if (item.image != null) {
            viewHolder.image.setImageResource(item.image)
        } else if (item.icon != null) {
            ImageLoader.load(context, viewHolder.image, item.icon)
        } else if (item.isAppointment) {
            viewHolder.image.setImageResource(R.drawable.meeting_reserve)
        } else if (item.isStartMeeting) {
            viewHolder.image.setImageResource(R.drawable.meeting_start)
        } else if (item.isJoinMeeting) {
            viewHolder.image.setImageResource(R.drawable.meeting_join)
        } else if (item.isNote) {
            viewHolder.image.setImageResource(R.drawable.meeting_note)
        }
    }

    fun refresh(items: List<ActionItem>) {
        this.items.clear()
        this.items.addAll(items)
        this.notifyDataSetChanged()
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        val image: ImageView by lazy { itemView.findViewById(R.id.iv_image) }
        val title: TextView by lazy { itemView.findViewById(R.id.tv_title) }

        init {
            itemView.setOnClickListener {
                onClickListener(itemView, items[bindingAdapterPosition])
            }
        }
    }
}