package com.jd.oa.meeting.home

import androidx.annotation.DrawableRes
import androidx.annotation.StringDef

data class ActionItem(
    @ActionType val itemType: String? = null,
    val title: String? = null,
    @DrawableRes val image: Int? = null,
    val icon: String? = null,
    val name: String? = null,
    val enName: String? = null,
    val mobileUrl: String? = null,
    val webUrl: String? = null,
    val eventId: String? = null,
    val mobileEventId: String? = null
) {
    companion object {
        const val TYPE_START_MEETING = "startMeeting"
        const val TYPE_JOIN_MEETING = "joinMeeting"
        const val TYPE_APPOINTMENT = "appointment"
        const val TYPE_NOTE = "note"
        const val TYPE_SCAN = "scan"

        @StringDef(TYPE_START_MEETING, TYPE_JOIN_MEETING, TYPE_APPOINTMENT, TYPE_NOTE, TYPE_SCAN)
        @Retention(AnnotationRetention.SOURCE)
        @Target(
            AnnotationTarget.FIELD,
            AnnotationTarget.PROPERTY,
            AnnotationTarget.VALUE_PARAMETER,
            AnnotationTarget.PROPERTY_GETTER,
            AnnotationTarget.PROPERTY_SETTER
        )
        annotation class ActionType
    }

    val isStartMeeting: Boolean
        get() = itemType == TYPE_START_MEETING

    val isJoinMeeting: Boolean
        get() = itemType == TYPE_JOIN_MEETING

    val isAppointment: Boolean
        get() = itemType == TYPE_APPOINTMENT

    val isNote: Boolean
        get() = itemType == TYPE_NOTE

    val isScan: Boolean
        get() = itemType == TYPE_SCAN
}