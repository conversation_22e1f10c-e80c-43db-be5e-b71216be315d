package com.jd.oa.meeting.home

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.ProjectionDocument
import com.jd.oa.meeting.home.feedback.FeedbackItemData
import com.jd.oa.meeting.home.feedback.FeedBackItemDataDeserializer
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.colorPost
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.preference.PreferenceManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.Locale

class MeetingListRemoteDataSource(): MeetingListDataSource {

    companion object {
        const val API_GET_ACTION_LIST = "meeting.functionGray"//接口已废弃
        const val API_GET_APPOINTMENT_MEETINGS = "meeting.proxyGetAppoMeetings"
        const val API_GET_HISTORY_MEETINGS = "meeting.proxyGetHistoryMeetings"

        const val API_GET_PROJECTION_DOCUMENT = "joyspace.board.screenProjectionRecord"
        const val API_QUIT_SCREEN_PROJECTION = "joyspace.board.exitScreenProjection"
        const val API_GET_CONFIGURATION = "joyday.appointment.queryClientConf"

        const val STATUS_SUCCESS = "success"
        const val STATUS_FAIL = "fail"

        const val DEVICE_TYPE = "Android"
        const val CODE_SUCCESS = "0"
    }

    override suspend fun getActionList(noCache: Boolean): Flow<List<ActionItem>> = flow {
        emit(emptyList())
    }

    private val meetingPin: String by lazy {
        if (MultiAppConstant.isSaasFlavor()) {//Saas统一用UserId
            PreferenceManager.UserInfo.getUserId()
        } else {
            PreferenceManager.UserInfo.getUserName().toLowerCase(Locale.getDefault())
        }
    }

    override suspend fun getHistoryMeetings(pageNumber: Int, pageSize: Int): PagedList<Meeting> {
        val params = mapOf(
            "userId" to meetingPin,
            "deviceType" to DEVICE_TYPE,
            "pageNumber" to pageNumber,
            "pageSize" to pageSize
        )
        val response = post(null, params, API_GET_HISTORY_MEETINGS)
        val history = withContext<PagedList<Meeting>>(Dispatchers.IO) {
            return@withContext kotlin.runCatching {
                val json = JSONObject(response)
                val code = json.optString("errorCode", "1")
                if (code != CODE_SUCCESS) throw HttpException("Request failed, code: ${code}")
                val result = json.getString("content")
                //val content = result.getString("content")
                val meetings = Gson().fromJson<PagedList<Meeting>>(
                    result,
                    object : TypeToken<PagedList<Meeting>>() {}.type
                )
                return@runCatching meetings
            }.getOrThrow()
        }
        return history
    }

    override suspend fun getAppointmentMeetings(): List<Meeting> {
        val params = mapOf(
            "userId" to meetingPin,
            "deviceType" to DEVICE_TYPE,
            "pageNumber" to 1,
            "pageSize" to 100
        )
        val response = post(null, params, API_GET_APPOINTMENT_MEETINGS)
        val appointment = withContext(Dispatchers.IO) {
            return@withContext kotlin.runCatching {
                val json = JSONObject(response)
                val code = json.optString("errorCode", "1")
                if (code != CODE_SUCCESS) throw HttpException("Request failed, code: ${code}")

                val result = json.optJSONObject("content")
                val content = result?.optString("content")

                if (content.isNullOrEmpty()) {
                    return@runCatching emptyList()
                }

                val meetings = Gson().fromJson<List<Meeting>>(
                    content,
                    object : TypeToken<List<Meeting>>() {}.type
                )
                return@runCatching meetings
            }.getOrThrow()
        }
        return appointment
    }

    override suspend fun getProjectionDocuments(): List<ProjectionDocument> {
        val response = colorPost(null, API_GET_PROJECTION_DOCUMENT)
        val documents = withContext(Dispatchers.Default) {
            return@withContext kotlin.runCatching {
                val json = JSONObject(response.result)
                val status = json.optString("status", STATUS_FAIL)
                if (status != STATUS_SUCCESS) throw HttpException("Request failed, code: ${status}")

                val data = json.optJSONObject("data")
                val record = data?.optString("records")

                if (record.isNullOrEmpty()) {
                    return@runCatching emptyList()
                }

                val docs = Gson().fromJson<List<ProjectionDocument>>(
                    record,
                    object : TypeToken<List<ProjectionDocument>>() {}.type
                )
                return@runCatching docs
            }.getOrThrow()
        }
        return documents
    }

    override suspend fun quitScreenProjection(document: ProjectionDocument): Boolean {
        val params = mutableMapOf<String, Any?>(
            "uid" to document.projectId,
            "screenSn" to document.screenSn,
            "link" to document.link
        )
        val response = colorPost(params, API_QUIT_SCREEN_PROJECTION)
        val success = withContext(Dispatchers.Default) {
            return@withContext kotlin.runCatching {
                val json = JSONObject(response.result)
                val status = json.optString("status", STATUS_FAIL)

                return@runCatching status == STATUS_SUCCESS
            }.getOrThrow()
        }
        return success
    }

    override suspend fun getFeedbackItems(): List<FeedbackItemData<*>> {
        val params = mapOf<String, Any>(
            "key" to listOf("common.conference.help")
        )
        val result = post(null, params, API_GET_CONFIGURATION)
        val feedbacks = withContext(Dispatchers.Default) {
            return@withContext kotlin.runCatching {
                if (result == null) throw IllegalStateException("result is empty")
                val response = ApiResponse.parse<Map<String, Any>>(result, HashMap::class.java)
                if (!response.isSuccessful) throw HttpException("${response.errorCode}, ${response.errorMessage}")

                val data = response.data["common.conference.help"] as String
                val jsonObj = JSONObject(data)
                val helpList = jsonObj.getString("helpList")

                return@runCatching FeedBackItemDataDeserializer(helpList).deserializer()
            }.getOrThrow()
        }
        return feedbacks
    }
}