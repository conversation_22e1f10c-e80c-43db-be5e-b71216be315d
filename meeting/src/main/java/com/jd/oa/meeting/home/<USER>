package com.jd.oa.meeting.home

sealed class LoadState {

    object None: LoadState()

    object Loading: LoadState()

    object Loaded: LoadState()

    object Complete: LoadState()

    class LoadFailed(exception: Throwable): LoadState()
}

sealed class RefreshState {

    object Refreshing: RefreshState()

    class RefreshFailed(val exception: Throwable?): RefreshState()

    class RefreshSucceed<T>(val data: T): RefreshState()
}