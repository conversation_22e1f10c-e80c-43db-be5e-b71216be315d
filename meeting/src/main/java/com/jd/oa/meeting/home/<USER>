package com.jd.oa.meeting.home

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import android.view.animation.RotateAnimation
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.LiveData
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.meeting.R

class MeetingLoadStateAdapter(
    val fragment: MeetingFragment,
    val loadState: LiveData<LoadState>,
    meetingNum: Int = 0,
    val onRetryClickListener: () -> Unit
    ): RecyclerView.Adapter<MeetingLoadStateAdapter.LoadStateViewHolder>() {

    var meetingNum: Int = meetingNum
        @SuppressLint("NotifyDataSetChanged")
        set(value) {
            field = value
            this.notifyDataSetChanged()
        }

    override fun onCreateViewHolder(parent: ViewGroup, position: Int): LoadStateViewHolder {
        return LoadStateViewHolder(LayoutInflater.from(fragment.requireContext()).inflate(R.layout.meeting_refresh_footer, parent, false))
    }

    override fun onBindViewHolder(viewHolder: LoadStateViewHolder, position: Int) {
        //viewHolder.startLoadingAnimation()
    }

    override fun getItemCount() = if (meetingNum > 0) 1 else 0

    override fun onViewDetachedFromWindow(holder: LoadStateViewHolder) {
        super.onViewDetachedFromWindow(holder)
        holder.onViewDetachedFromWindow(holder)
    }

    override fun onViewAttachedToWindow(holder: LoadStateViewHolder) {
        super.onViewAttachedToWindow(holder)
        holder.onViewAttachedToWindow(holder)
    }

    inner class LoadStateViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val ivLoading: View by lazy { itemView.findViewById(R.id.loading) }
        val tvLoading: TextView by lazy { itemView.findViewById(R.id.tv_state) }
        val tvFailed: TextView by lazy { itemView.findViewById(R.id.tv_failed) }

        init {
            loadState.observe(fragment.viewLifecycleOwner) {
                when (it) {
                    is LoadState.None -> {
                        ivLoading.visibility = View.GONE
                        tvFailed.visibility = View.GONE
                        tvLoading.text = null
                    }
                    is LoadState.Loading -> {
                        ivLoading.visibility = View.VISIBLE
                        tvFailed.visibility = View.GONE
                        tvLoading.text = itemView.context.getText(R.string.meeting_refresh_loading)
                    }
                    is LoadState.Loaded -> {
                        ivLoading.visibility = View.GONE
                        tvFailed.visibility = View.GONE
                        tvLoading.text = itemView.context.getText(R.string.meeting_loaded)
                    }
                    is LoadState.Complete -> {
                        ivLoading.visibility = View.GONE
                        tvFailed.visibility = View.GONE
                        tvLoading.text = itemView.context.getText(R.string.meeting_refresh_complete)
                    }
                    is LoadState.LoadFailed -> {
                        ivLoading.visibility = View.GONE
                        tvFailed.visibility = View.VISIBLE
                        tvLoading.text = itemView.context.getText(R.string.meeting_load_failed)
                    }
                }
            }
            itemView.setOnClickListener {
                if (loadState.value is LoadState.LoadFailed) {
                    onRetryClickListener.invoke()
                }
            }
            //startLoadingAnimation()
        }

        fun startLoadingAnimation() {
            //val animation = AnimationUtils.loadAnimation(itemView.context, R.anim.meeting_refresh_loading)
            //animation.interpolator = LinearInterpolator()
            //ivLoading.startAnimation(animation)
        }

        fun onViewAttachedToWindow(holder: LoadStateViewHolder) {
            //itemView.post { startLoadingAnimation() }
        }

        fun onViewDetachedFromWindow(holder: RecyclerView.ViewHolder) {
            //ivLoading.animation?.let { it.cancel() }
        }
    }
}