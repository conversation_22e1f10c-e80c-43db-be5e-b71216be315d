package com.jd.oa.meeting.home

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.ProjectionDocument
import com.jd.oa.meeting.home.feedback.FeedbackItemData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class MockRemoteDataSource(val context: Context) : MeetingListDataSource {

    companion object {
        val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
    }

    override suspend fun getActionList(noCache: Boolean): Flow<List<ActionItem>> = flow {
//        val list = listOf(
//            ActionItem(AppBase.getAppContext().getString(R.string.meeting_tab_action_join), R.drawable.meeting_join),
//            ActionItem((AppBase.getAppContext().getString(R.string.meeting_tab_action_call)), R.drawable.meeting_start),
//            ActionItem((AppBase.getAppContext().getString(R.string.meeting_tab_action_reserve)), R.drawable.meeting_reserve),
//            ActionItem((AppBase.getAppContext().getString(R.string.meeting_tab_action_note)), R.drawable.meeting_reserve),
//        )
        delay(2000)
        emit(emptyList())
    }

    override suspend fun getHistoryMeetings(pageNumber: Int, pageSize: Int): PagedList<Meeting> = withContext(Dispatchers.IO) {
        val assetManager = context.assets

        val list = assetManager.open("historyMeetings.json").use {
            Gson().fromJson<List<Meeting>>(it.reader(), object : TypeToken<List<Meeting>>(){}.type)
        }
        return@withContext PagedList(list, pageNumber = 1, pageSize = 20, totalElements = 20, totalPages = 1)
    }

    override suspend fun getAppointmentMeetings(): List<Meeting> = withContext(Dispatchers.IO) {
        val assetManager = context.assets

        val list = assetManager.open("appointmentMeetings.json").use {
            Gson().fromJson<List<Meeting>>(it.reader(), object : TypeToken<List<Meeting>>(){}.type)
        }
        return@withContext list
    }

    override suspend fun getProjectionDocuments(): List<ProjectionDocument> {
        val records = "{\"bizId\":\"文档ID\",\"bizType\":1,\"bizName\":\"文档名称\",\"projectId\":\"投屏ID\",\"projectStatus\":1,\"projectStartAt\":1704953980000,\"meetingRoomId\":\"会议室ID\",\"meetingRoomName\":\"会议室名称\",\"screenSn\":\"大屏SN码\",\"link\":\"投屏链接地址\"}";
        return listOf(Gson().fromJson(records, ProjectionDocument::class.java))
    }

    override suspend fun quitScreenProjection(document: ProjectionDocument): Boolean {
        return true
    }

    override suspend fun getFeedbackItems(): List<FeedbackItemData<*>> {
        TODO("Not yet implemented")
    }
}