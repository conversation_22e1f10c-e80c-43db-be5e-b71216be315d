package com.jd.oa.meeting.home

import android.util.Log
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingListItem
import com.jd.oa.meeting.entity.ProjectionDocument
import com.jd.oa.preference.PreferenceManager
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch

private const val PAGE_SIZE = 20
const val PAGE_START = 1
class MeetingViewModel(val repo: MeetingListDataSource): ViewModel() {

    val actions = MutableLiveData<List<ActionItem>>()

    private val ongoingMeetings = MutableLiveData<List<Meeting>>()

    private val reserveMeetings = MutableLiveData<List<Meeting>>()

    private val historyMeetings = MutableLiveData<List<Meeting>>()

    private val projectionDocuments = MutableLiveData<List<ProjectionDocument>>()

    val meetingItems = MediatorLiveData<List<MeetingListItem>>().apply {

        addSource(ongoingMeetings) {
            value = mutableListOf<MeetingListItem>().apply {
                addAll(projectionDocuments.value ?: emptyList())
                addAll(it ?: emptyList())
                addAll(reserveMeetings.value ?: emptyList())
                addAll(historyMeetings.value ?: emptyList())
            }
        }

        addSource(historyMeetings) {
            value = mutableListOf<MeetingListItem>().apply {
                addAll(projectionDocuments.value ?: emptyList())
                addAll(ongoingMeetings.value ?: emptyList())
                addAll(reserveMeetings.value ?: emptyList())
                addAll(it ?: emptyList())
            }
        }

        addSource(projectionDocuments) {
            value = mutableListOf<MeetingListItem>().apply {
                addAll(it ?: emptyList())
                addAll(ongoingMeetings.value ?: emptyList())
                addAll(reserveMeetings.value ?: emptyList())
                addAll(historyMeetings.value ?: emptyList())
            }
        }
    }

    val refreshState = MutableLiveData<RefreshState>()
    val historyLoadingState = MutableLiveData<LoadState>(LoadState.None)

    private var mHistoryPageNo = PAGE_START
    val historyPageNo get() = mHistoryPageNo

    private val handler = CoroutineExceptionHandler { _, exception ->
        Log.e("MeetingViewModel", "Caught $exception", exception)
    }

    private val cacheStorage: LocalCacheStorage by lazy { DbCacheStorage(PreferenceManager.UserInfo.getUserName()) }

    fun getActionList() {
        viewModelScope.launch(handler) {
            repo.getActionList()
                .catch {
                    Log.e("MeetingViewModel", "getActionList", it)
                }.collect{
                    actions.value = it
                }
        }
    }

    fun refreshMeetings(useCache: Boolean = false) {
        viewModelScope.launch(handler) {
            kotlin.runCatching {
                refreshState.value = RefreshState.Refreshing

                val appointment = async {
                    kotlin.runCatching {
                        refreshAppointment(useCache)
                    }
                }

                val documents = async {
                    refreshProjectionDocuments()
                }

                val history = async {
                    kotlin.runCatching {
                        refreshHistory(useCache)
                    }
                }

                val appointmentResult = appointment.await()
                val historyResult = history.await()
                val documentsResult = documents.await()

                if (appointmentResult.isFailure && historyResult.isFailure) {
                    refreshState.value = RefreshState.RefreshFailed(appointmentResult.exceptionOrNull() ?: historyResult.exceptionOrNull())
                } else if (appointmentResult.isSuccess && historyResult.isSuccess) {
                    refreshState.value = RefreshState.RefreshSucceed(RefreshResult.All(appointmentResult.getOrNull() ?: emptyList(), historyResult.getOrNull() ?: emptyList()))
                }
            }
        }
    }

    private suspend fun refreshAppointment(useCache: Boolean = false): List<Meeting> {
        return kotlin.runCatching {
            //过滤-即将开始的会议
            val isAppointmentList = TenantConfigBiz.isAppointmentList()

            fun updateAppointmentList(list: List<Meeting>) {
                ongoingMeetings.value = list.filter { it.isOnGoing }
                reserveMeetings.value = list.filter { !it.isOnGoing }.filter { isAppointmentList }

                refreshState.value = RefreshState.RefreshSucceed(RefreshResult.Appointment(list))
            }
            if (useCache) {
                cacheStorage.getAppointmentList()?.also { updateAppointmentList(it) }
            }

            val appointment = repo.getAppointmentMeetings()

            val ongoing = appointment.filter { it.isOnGoing }
                .sortedWith { m1, m2 ->
                    val start1 = m1.realStartTimeInCalendar ?: m1.realJoinTimeInCalendar ?: m1.startTimeInCalendar
                    val start2 = m2.realStartTimeInCalendar ?: m2.realJoinTimeInCalendar ?: m2.startTimeInCalendar

                    return@sortedWith -1 * (start1?.timeInMillis?.compareTo(start2?.timeInMillis ?: 0) ?: 0)
                }
            ongoingMeetings.value = ongoing

            val reserve = appointment.filter { !it.isOnGoing }.filter { isAppointmentList }
            reserveMeetings.value = reserve

            val list = ongoing + reserve
            cacheStorage.putAppointmentList(list)

            refreshState.value = RefreshState.RefreshSucceed(RefreshResult.Appointment(list))
            list
        }.onFailure {
            //ToastUtils.showToast(R.string.meeting_load_failed)
        }.getOrThrow()
    }

    suspend fun refreshHistory(useCache: Boolean = false): List<Meeting> {
        return kotlin.runCatching {
            if (useCache) {
                cacheStorage.getHistoryList()?.let {
                    historyMeetings.value = it
                    mHistoryPageNo = PAGE_START + 1

                    historyLoadingState.value = LoadState.Loaded
                    refreshState.value = RefreshState.RefreshSucceed(RefreshResult.History(it))
                }
            }

            val history = repo.getHistoryMeetings(PAGE_START, PAGE_SIZE)
            historyMeetings.value = history.content
            mHistoryPageNo = PAGE_START + 1

            cacheStorage.putHistoryList(history.content)

            if (history.content.size < PAGE_SIZE) {
                historyLoadingState.value = LoadState.Complete
            } else {
                historyLoadingState.value = LoadState.Loaded
            }
            refreshState.value = RefreshState.RefreshSucceed(RefreshResult.History(history.content))

            history.content
        }.onFailure {
            it.printStackTrace()
            historyLoadingState.value = LoadState.LoadFailed(it)
            throw it
        }.getOrThrow()
    }

    fun nextHistoryPage() {
        viewModelScope.launch(handler) {
            kotlin.runCatching {
                historyLoadingState.value = LoadState.Loading
                repo.getHistoryMeetings(mHistoryPageNo, PAGE_SIZE)
            }.onSuccess {
                mHistoryPageNo += 1
                historyMeetings.value = (historyMeetings.value ?: emptyList()) + it.content

                if (it.content.size < PAGE_SIZE) {
                    historyLoadingState.value = LoadState.Complete
                } else {
                    historyLoadingState.value = LoadState.Loaded
                }
            }.onFailure {
                historyLoadingState.value = LoadState.LoadFailed(it)
            }
        }
    }
    private suspend fun refreshProjectionDocuments(): Unit {
        projectionDocuments.value =  kotlin.runCatching {
            repo.getProjectionDocuments()
        }.getOrElse { emptyList() }
    }

    fun quitScreenProjection(document: ProjectionDocument) {
        // 是否调用大屏投文档功能，关闭状态下不获取投屏文档列表。
        if (!TenantConfigBiz.isJoySpaceProjection()) {
            return
        }
        viewModelScope.launch {
            kotlin.runCatching {
                val result = repo.quitScreenProjection(document)
                if (result) {
                    val documents = projectionDocuments.value?.toMutableList()
                    documents?.remove(document)
                    projectionDocuments.value = documents ?: emptyList()
                }
            }
        }
    }

    suspend fun getFeedbackItems() = repo.getFeedbackItems()

    class Factory(val repo: MeetingListDataSource) : ViewModelProvider.Factory {

        override fun <T : ViewModel> create(modelClass: Class<T>): T = MeetingViewModel(repo) as T
    }
}

sealed class RefreshResult {
    class Appointment(val meetings: List<Meeting>) : RefreshResult()
    class History(val meetings: List<Meeting>): RefreshResult()
    class All(val appointment: List<Meeting>, val history: List<Meeting>): RefreshResult()
}