package com.jd.oa.meeting.home

import android.annotation.SuppressLint
import android.app.Activity
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Pair
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.Router
import com.chenenyu.router.annotation.Route
import com.gavin.com.library.PowerfulStickyDecoration
import com.gavin.com.library.listener.GroupListener
import com.gavin.com.library.listener.PowerGroupListener
import com.jd.me.activitystarter.ActivityResult
import com.jd.me.activitystarter.ActivityResultParser
import com.jd.me.activitystarter.ActivityStarter
import com.jd.oa.AppBase
import com.jd.oa.abilities.api.OpennessApi
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jd.oa.abilities.apm.BuglyProLoader
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.abtest.ABTestManager
import com.jd.oa.business.index.AppUtils
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.meeting.MeetingJDMAConstant
import com.jd.oa.meeting.R
import com.jd.oa.meeting.detail.MeetingDetailActivity
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingListItem
import com.jd.oa.meeting.entity.ProjectionDocument
import com.jd.oa.meeting.home.feedback.FeedbackDialog
import com.jd.oa.model.service.CalendarService
import com.jd.oa.model.service.JdMeetingService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.qrcode.ScanResultDispatcher
import com.jd.oa.qrcode.ScanSource
import com.jd.oa.router.DeepLink
import com.jd.oa.router.RouteNotFoundCallback
import com.jd.oa.theme.manager.ThemeApi
import com.jd.oa.timezone.TimeZoneChangeNotifier
import com.jd.oa.ui.JoyWorkTheme
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.StatusBarConfig
import com.jdcloud.mt.me.modle.SourceType
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.*


@Route(DeepLink.MEETING)
class MeetingFragment: BaseFragment() {
    companion object {
        const val ARG_IS_TAB = "isTab"

        const val TRIGGER_PAGING_NUM = 4

        const val DIALOG_TAG_FEEDBACK = "Feedback"
    }

    val repository: MeetingRepository by lazy {
        //val storage = MeetingPreference(requireContext())
        val storage = DbCacheStorage(PreferenceManager.UserInfo.getUserName())
        val remoteDataSource = MeetingListRemoteDataSource()
        //val remoteDataSource = MockRemoteDataSource(requireContext())
        MeetingRepository(storage, remoteDataSource)
    }
    lateinit var mViewModel: MeetingViewModel

    private val mRvActions: RecyclerView by lazy { requireView().findViewById(R.id.rv_action) }
    private val mRvMeetings: RecyclerView by lazy { requireView().findViewById(R.id.rv_conferences) }
    private val mRlTop: View by lazy { requireView().findViewById(R.id.layout_top) }
    private val mRefreshLayout: SmartRefreshLayout by lazy { requireView().findViewById(R.id.refreshLayout) }
    private val mEmptyStub: ViewStub by lazy { requireView().findViewById(R.id.stub_empty) }
    private val mErrorStub: ViewStub by lazy { requireView().findViewById(R.id.stub_error) }
    private val mStubSkeleton: ViewStub by lazy { requireView().findViewById(R.id.stub_skeleton) }
    private var mRefreshing: com.jd.oa.ui.LoadingView? = null

    private lateinit var mActionsAdapter: ActionsAdapter
    private lateinit var mMeetingListAdapter: MeetingListAdapter
    private lateinit var mMeetingLoadStateAdapter: MeetingLoadStateAdapter

    private lateinit var mLayoutManager: LinearLayoutManager

    private var mStickyDecoration: PowerfulStickyDecoration? = null

    private lateinit var mTopBar: MeetingTopBar

    private var mIsTab: Boolean = true

    private val jdMeetingService: JdMeetingService by lazy { AppJoint.service(JdMeetingService::class.java) }

    private var mFirstResume = true

    private var mLastClickTimestamp: Long? = null

    private val disable: Boolean by lazy {
        ABTestManager.getInstance().getConfigByKey("monkey.test.disable", "0") != "0"
    }

    private val currentMeetingObserver = CurrentMeetingObserver()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        kotlin.runCatching {
            val mparam = arguments?.getString("mparam")?.let { JSONObject(it) }
            mparam?.optString("isTab", "1") == "1"
        }.onSuccess {
            mIsTab = it
        }.onFailure {
            mIsTab = true
        }
        mTopBar = if (mIsTab) TabMeetingTopBar(requireActivity(), ::onFeedbackTap) else ActivityMeetingTopBar(requireActivity(), ::onFeedbackTap)

        mViewModel = ViewModelProvider(this, MeetingViewModel.Factory(repository)).get(MeetingViewModel::class.java)

        mViewModel.actions.observe(this) {
            mActionsAdapter.refresh(it)
        }

        mViewModel.meetingItems.observe(this) {
            val joinedMeeting = jdMeetingService.currentMeeting
            joinedMeeting?.run {
                it.filterIsInstance<Meeting>().forEach { item ->
                    item.joined = item.meetingCode == joinedMeeting.second
                }
            }

            mStickyDecoration?.clearCache()

            mMeetingLoadStateAdapter.meetingNum = it.size
            mMeetingListAdapter.refresh(it)
        }

        jdMeetingService.currentMeetingLiveData?.observeForever(currentMeetingObserver)

        mViewModel.historyLoadingState.observe(this) {
            when (it) {
                is LoadState.Loaded -> {
                    mRefreshLayout.finishLoadMore()
                }
                is LoadState.Complete -> {
                    mRefreshLayout.finishRefreshWithNoMoreData()
                }
                is LoadState.LoadFailed -> {
                    mRefreshLayout.finishLoadMore(false)
                }
            }
        }

        mViewModel.refreshState.observe(this) {
            when (it) {
                is RefreshState.Refreshing -> {
                    if (mMeetingListAdapter.items.isEmpty()) {
                        hideEmptyView()
                        hideErrorView()
                        showRefreshing()
                    }
                }
                is RefreshState.RefreshSucceed<*> -> {
                    mRefreshLayout.finishRefresh()

                    if (it.data is RefreshResult.All) {
                        if (it.data.appointment.isEmpty() && it.data.history.isEmpty()) {
                            showEmptyView()
                        } else {
                            hideEmptyView()
                        }
                    }
                    if ((it.data is RefreshResult.Appointment && it.data.meetings.isNotEmpty()) ||
                        (it.data is RefreshResult.History && it.data.meetings.isNotEmpty())) {
                        hideEmptyView()
                    }
                    hideErrorView()
                    hideRefreshing()

                    if (it.data is RefreshResult.History && (mViewModel.historyPageNo - 1) != PAGE_START) {
                        //mRvMeetings.scrollToPosition(0)
                    }
                }
                is RefreshState.RefreshFailed -> {
                    mRefreshLayout.finishRefresh(false)
                    if (mMeetingListAdapter.items.isEmpty()) {
                        hideEmptyView()
                        showErrorView()
                    }
                }
            }
        }

        mViewModel.getActionList()

        TimeZoneChangeNotifier.getInstance().timeZoneLiveData.observe(this) {
            runCatching {
                if (<EMAIL> && ::mMeetingListAdapter.isInitialized) {
                    mMeetingListAdapter.resetDataCalendar()
                    mMeetingListAdapter.notifyDataSetChanged()
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        kotlin.runCatching {
            (requireActivity() as? FunctionActivity)?.setBarHide()
        }
        val view = inflater.inflate(R.layout.meeting_fragment_meeting, container, false)

        val topContainer = view.findViewById<ViewGroup>(R.id.layout_top)
        val topBarView = mTopBar.createView(requireContext(), topContainer)
        //topContainer.addView(topBarView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        mTopBar.view = topBarView

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        kotlin.runCatching {
            initView(view)
        }.onFailure {
            val buglyLoader = BuglyProLoader(
                AppBase.getAppContext(),
                ApmLoaderHepler.getInstance(AppBase.getAppContext()).configModel
            )
            buglyLoader.upLoadException(it)
        }
    }

    private fun initView(view: View) {
        mTopBar.initView(mTopBar.view)

        mActionsAdapter = ActionsAdapter(requireContext(), mutableListOf()) { view, item ->
            val timestamp = System.currentTimeMillis()
            if (mLastClickTimestamp != null && (timestamp - mLastClickTimestamp!!) < 200) {
                Toast.makeText(<EMAIL>(), R.string.meeting_throttle_time_toast, Toast.LENGTH_SHORT).show()
                return@ActionsAdapter
            }
            mLastClickTimestamp = timestamp

            val jdMeetingService = AppJoint.service(JdMeetingService::class.java)
            when  {
                item.itemType == ActionItem.TYPE_JOIN_MEETING -> {
                    if (disable) return@ActionsAdapter
                    jdMeetingService?.attendMeeting(activity)
                    JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_JoinMeeting, null)
                }
                item.itemType == ActionItem.TYPE_START_MEETING -> {
                    if (disable) return@ActionsAdapter
                    jdMeetingService?.startMeeting(activity)
                    JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_NewMeeting, null)
                }
                item.itemType == ActionItem.TYPE_APPOINTMENT -> {
                    val activity = AppBase.getTopActivity() ?: return@ActionsAdapter
                    val bundle = Bundle()
                    bundle.putString("routeTag", "create")
                    bundle.putBoolean("timlineMeeting", true)
                    bundle.putString("from", "timlineMeeting")
                    Router.build(DeepLink.CALENDER_SCHEDULE).with(bundle).go(activity)
                    JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Schedule, null)
                }
                item.itemType == ActionItem.TYPE_NOTE -> {
                    Router.build(Uri.parse(DeepLink.JOY_NOTE_MAIN)).go(this, RouteNotFoundCallback(context))
                    JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Minutes, null)
                }
                item.itemType == ActionItem.TYPE_SCAN -> {
                    val intent = Router.build(Uri.parse(item.mobileUrl ?: DeepLink.ACTIVITY_URI_Scan)).getIntent(this)
                    ActivityStarter.from<ActivityResult>(this)
                        .setIntent(intent)
                        .setResultParser(ActivityResultParser())
                        .start {
                            if (it.resultCode == Activity.RESULT_OK) {
                                val result = it.data?.getStringExtra("result")
                                val scanMethod = it.data?.getIntExtra("scan_method", ScanSource.UNKNOWN) ?: ScanSource.UNKNOWN
                                if (!result.isNullOrEmpty()) {
                                    ScanResultDispatcher.dispatch(context, result, scanMethod, null)
                                }
                            }
                        }
                    val eventId = item.mobileEventId ?: item.eventId
                    eventId?.let { JDMAUtils.clickEvent("", it, null) }
                }
                !item.mobileUrl.isNullOrEmpty() -> {
                    Router.build(Uri.parse(item.mobileUrl)).go(this, RouteNotFoundCallback(context))
                    val eventId = item.mobileEventId ?: item.eventId
                    eventId?.let { JDMAUtils.clickEvent("", it, null) }
                }
            }
            MELogUtil.localI(TAG, "action item click itemType:${item.itemType}")
        }
        mRvActions.adapter = mActionsAdapter
        val layoutManager = object: GridLayoutManager(context, 4) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        mRvActions.layoutManager = layoutManager

        val listItems = mutableListOf<MeetingListItem>()
        mMeetingListAdapter = MeetingListAdapter(this, requireContext(), listItems, { item, position ->
            if (item is Meeting) {
                val meeting = item as Meeting
                var deepLink: String? = null
                if (meeting.meetingSelector == Meeting.MEETING_TYPE_JOYMEETING) {
                    val scheduleId = meeting.scheduleId
                    val calendarId = meeting.calendarId
                    val start = meeting.scheduleStartTime?.let { it1 ->
                        Meeting.dateFormat.parse(it1)?.time ?: 0
                    }

                    val service = AppJoint.service(CalendarService::class.java)
                    service.openScheduleDetail(scheduleId, calendarId, start ?: 0, 0)

                    deepLink = meeting.joinLink
                } else {
                    deepLink = MeetingDetailActivity.createParamDeepLink(meeting.meetingId, meeting.meetingCode!!,
                        meeting.meetingSelector!!, meeting.scheduleId ?: meeting.settings?.scheduleId, meeting.calendarId ?: meeting.settings?.calendarId,
                        meeting.scheduleStartTime, meeting.subject, meeting.realMeetingStatus ?: -2, meeting.startTime, meeting.endTime)
                    Router.build(deepLink).go(this@MeetingFragment)
                }

                JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_List, null)
                MELogUtil.localI(TAG, "open meeting detail deeplink:$deepLink")
            } else if (item is ProjectionDocument) {
                val document = item
                document.link?.let {
                    if (it.isNotEmpty()) {
                        OpennessApi.openUrl(document.link, true)
                    }
                }
            }
        }, { meeting ->
            val jdMeetingService = AppJoint.service(JdMeetingService::class.java)
            if (meeting.meetingSelector == Meeting.MEETING_TYPE_JOYMEETING) {
                if (jdMeetingService.canHandleMeeting()) {
                    if (meeting.joinLink != null && meeting.joinLink.isNotEmpty()) {
                        val uri = Uri.parse(meeting.joinLink)
                        val appId: String? = uri.getQueryParameter("appId")
                        if (!TextUtils.isEmpty(appId)) {
                            AppUtils.gainTokenAndGoPlugin(meeting.joinLink, appId)
                        }
                    }
                }
            } else {
                jdMeetingService.joinMeeting(activity, meeting.meetingId, meeting.meetingCode, meeting.password, SourceType.LIST.type.toString(), null)
            }
            JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Join, null)
            MELogUtil.localI(TAG, "join meeting click meetingId:${meeting.meetingId} meetingCode:${meeting.meetingCode} password:${meeting.password}")
        }, { document ->
            mViewModel.quitScreenProjection(document)
            JDMAUtils.clickEvent("", MeetingJDMAConstant.Mobile_Event_JingMEMeeting_Exit_Projection, null)
        })

        currentMeetingObserver.adapter = mMeetingListAdapter

        mMeetingLoadStateAdapter = MeetingLoadStateAdapter(this, mViewModel.historyLoadingState, listItems.size) {
            mViewModel.nextHistoryPage()
        }

        mRvMeetings.adapter = ConcatAdapter(
            listOf(
            //PaddingAdapter(requireContext(), height = DensityUtil.dp2px(requireContext(), 10f)),
            mMeetingListAdapter,
            mMeetingLoadStateAdapter)
        )
        mLayoutManager = LinearLayoutManager(context)
        mRvMeetings.layoutManager = mLayoutManager

        mRvMeetings.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            var scrollUp: Boolean = false

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                scrollUp = dy > 0
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val lastVisibleItem = mLayoutManager.findLastVisibleItemPosition()
                val totalItemCount = mLayoutManager.itemCount
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    val state = mViewModel.historyLoadingState.value
                    if (lastVisibleItem >= (totalItemCount - 1) - TRIGGER_PAGING_NUM && state != LoadState.Loading && state != LoadState.Complete) {
                        mViewModel.nextHistoryPage()
                    }
                }
            }
        })

        val groupListener = GroupListener { position ->
            if (mMeetingListAdapter.items.isEmpty()) return@GroupListener null
            if (position >= mMeetingListAdapter.items.size) return@GroupListener null

            val item = mMeetingListAdapter.items[position]
            when {
                item is Meeting && item.isHistory ?: false -> {
                    requireContext().getString(R.string.meeting_history)
                }
                item is Meeting && item.isOnGoing -> {
                    requireContext().getString(R.string.meeting_ongoing)
                }
                item is Meeting && (item.isBeginning || item.isCreated) -> {
                    requireContext().getString(R.string.meeting_reserve)
                }
                item is ProjectionDocument -> {
                    requireContext().getString(R.string.meeting_ongoing)
                }
                else -> {
                    requireContext().getString(R.string.meeting_reserve)
                }
            }
        }
//        val decoration = StickyDecoration.Builder
//            .init(groupListener)
//            .setGroupBackground(Color.WHITE)
//            .setGroupTextColor(0xFF333333.toInt())
//            .setGroupTextSize(DensityUtil.dp2px(context, 16.toFloat()))
//            .setTextSideMargin(DensityUtil.dp2px(context, 16.toFloat()))
//            .build()
        val listener: PowerGroupListener = object : PowerGroupListener {
            override fun getGroupName(position: Int): String? {
                return groupListener.getGroupName(position)
            }

            override fun getGroupView(position: Int): View {
                val name = getGroupName(position)
                //获取自定定义的组View
                val groupView: View = layoutInflater.inflate(R.layout.meeting_list_section, null, false)
                val text = groupView.findViewById<TextView>(R.id.tv_text)
                text.text = name
                return groupView
            }
        }
        mStickyDecoration = PowerfulStickyDecoration.Builder
            .init(listener)
            //.setGroupHeight(132)
            .build()
        mRvMeetings.addItemDecoration(mStickyDecoration!!)

        mRefreshLayout.setOnRefreshListener {
            mViewModel.refreshMeetings(useCache = false)
            mViewModel.getActionList()
        }

        mRefreshLayout.setOnLoadMoreListener {
            mViewModel.nextHistoryPage()
        }
    }

    override fun onResume() {
        super.onResume()

        mViewModel.refreshMeetings(useCache = mFirstResume)
        mFirstResume = false
        if (mIsTab) {
            val data = JoyWorkTheme.currentTheme
            if (data.isGlobal && data.isDarkTheme) {
                ThemeApi.checkAndSetDarkTheme(activity)
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(activity)
            }
        }
        JDMAUtils.eventPV(MeetingJDMAConstant.Mobile_Page_JingMEMeeting_HomePage, null)
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        //mViewModel.getActionList()
    }

    override fun onPause() {
        super.onPause()
        val a = activity ?: return
        if (mIsTab) {
            if (StatusBarConfig.enableImmersive()) {
                QMUIStatusBarHelper.setStatusBarLightMode(a)
            }
        }
    }

    override fun onDestroy() {
        jdMeetingService.currentMeetingLiveData?.removeObserver(currentMeetingObserver)
        super.onDestroy()
    }

    private fun showEmptyView() {
        mEmptyStub.visibility = View.VISIBLE
    }

    private fun hideEmptyView() {
        mEmptyStub.visibility = View.GONE
    }

    private fun showRefreshing() {
        mStubSkeleton.setOnInflateListener { stub, inflated ->
            mRefreshing = inflated.findViewById(R.id.refreshing)
            mRefreshing?.start()
        }
        mStubSkeleton.visibility = View.VISIBLE
    }

    private fun hideRefreshing() {
        mStubSkeleton.visibility = View.GONE
        mRefreshing?.cancel()
    }

    private fun showErrorView() {
        mErrorStub.setOnInflateListener { stub, inflated ->
            val retry = inflated.findViewById<Button>(R.id.btn_retry)
            retry.setOnClickListener {
                mViewModel.refreshMeetings(useCache = false)
            }
            retry.text = requireContext().getString(R.string.meeting_list_error_refresh)
        }
        mErrorStub.visibility = View.VISIBLE
    }
    private fun hideErrorView() {
        mErrorStub.visibility = View.GONE
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mStickyDecoration?.clearCache()
    }

    private fun onFeedbackTap(view: View) {
        lifecycleScope.launch {
            runCatching {
                mViewModel.getFeedbackItems()
            }.onSuccess {
                if (!parentFragmentManager.isDestroyed && !parentFragmentManager.isStateSaved) {
                    FeedbackDialog(ArrayList(it)).show(parentFragmentManager, DIALOG_TAG_FEEDBACK)
                }
            }
        }
    }
}

class CurrentMeetingObserver: androidx.lifecycle.Observer<Pair<String, Long>?> {

    private var _adapter: WeakReference<MeetingListAdapter>? = null

    var adapter: MeetingListAdapter?
        set(value) {
            _adapter = WeakReference(value)
        }
        get() = _adapter?.get()

    @SuppressLint("NotifyDataSetChanged")
    override fun onChanged(pair: Pair<String, Long>?) {
        val listAdapter = adapter ?: return
        listAdapter.items.forEachIndexed { index, item ->
            if (item is Meeting) {
                if (pair?.second == null) {
                    if (item.joined) {
                        item.joined = false
                    }
                } else if (item.meetingCode == pair.second) {
                    item.joined = true
                }
            }
        }
        listAdapter.notifyDataSetChanged()
    }
}