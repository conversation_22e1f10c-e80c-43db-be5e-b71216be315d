package com.jd.oa.meeting.home

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.animation.Animation
import android.view.animation.LinearInterpolator
import android.view.animation.RotateAnimation
import android.widget.FrameLayout
import android.widget.ImageView
import com.jd.oa.meeting.R
import com.jd.oa.utils.DensityUtil

class LoadingView(context: Context, attrs: AttributeSet?, defStyleAttr: Int): FrameLayout(context, attrs, defStyleAttr) {

    val loading: ImageView
    private val rotateAnimation: RotateAnimation

    constructor(context: Context): this(context, null, 0)

    constructor(context: Context, attrs: AttributeSet?): this(context, attrs, 0)

    init {
        loading = ImageView(context)
        loading.setImageResource(R.drawable.meeting_ic_loading)

        rotateAnimation = RotateAnimation(0f, 360f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f)
        rotateAnimation.duration = 450
        rotateAnimation.repeatCount = Animation.INFINITE
        rotateAnimation.repeatMode = Animation.RESTART
        rotateAnimation.interpolator = LinearInterpolator()

        this.addView(loading, LayoutParams(
            DensityUtil.dp2px(context, 14f),    //ViewGroup.LayoutParams.WRAP_CONTENT,
            DensityUtil.dp2px(context, 14f),    //ViewGroup.LayoutParams.WRAP_CONTENT,
            Gravity.CENTER))
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        loading.startAnimation(rotateAnimation)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        rotateAnimation.cancel()
        loading.animation = null
    }
}