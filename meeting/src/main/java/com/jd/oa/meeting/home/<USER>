package com.jd.oa.meeting.home

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import com.jd.oa.meeting.R
import com.scwang.smart.refresh.layout.api.RefreshFooter
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.simple.SimpleComponent

class LoadingFooter(
    context: Context,
    attrs: AttributeSet,
    defStyleAttr: Int,
): SimpleComponent(context, attrs, defStyleAttr), RefreshFooter {

    val mIvLoading: View
    val mTvLoading: TextView
    var mNoMoreData: Boolean = false

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.meeting_refresh_footer, this, true)
        mIvLoading = view.findViewById(R.id.loading)
        mTvLoading = view.findViewById(R.id.tv_state)
    }

    constructor(context: Context, attrs: AttributeSet): this(context, attrs, 0)


    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        val animation = AnimationUtils.loadAnimation(context, R.anim.meeting_refresh_loading)
        mIvLoading.startAnimation(animation)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mIvLoading.animation?.let {
            it.cancel()
            mIvLoading.animation = null
        }
    }

    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {
        super.onInitialized(kernel, height, maxDragHeight)
    }

    override fun setNoMoreData(noMoreData: Boolean): Boolean {
        this.mNoMoreData = noMoreData
        return super.setNoMoreData(noMoreData)
    }

    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        if (!success) {
            this.mTvLoading.text = context.getString(R.string.meeting_load_failed)
        } else if (mNoMoreData) {
            this.mTvLoading.text = context.getString(R.string.meeting_refresh_complete)
        } else {
            this.mTvLoading.text = context.getString(R.string.meeting_refresh_loading)
        }
        return 0
    }

    override fun onStateChanged(
        refreshLayout: RefreshLayout,
        oldState: RefreshState,
        newState: RefreshState,
    ) {
        super.onStateChanged(refreshLayout, oldState, newState)
        if (!mNoMoreData) {
            when(newState) {
                RefreshState.LoadFinish -> {
                    //加载完成
                    this.mIvLoading.visibility = View.GONE
                }
                else -> {
                    this.mIvLoading.visibility = View.VISIBLE
                    this.mTvLoading.text = context.getString(R.string.meeting_refresh_loading)
                }
            }
        }
    }
}