package com.jd.oa.meeting.home;

import android.os.Bundle
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.meeting.R
import com.jd.oa.router.DeepLink


@Route(DeepLink.MEETING_HOME)
class MeetingActivity : BaseActivity() {

    companion object {
        const val TAG_MEETING_LIST = "meeting_list"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.meeting_activity_meeting_list)

        supportActionBar?.hide()

        var fragment = supportFragmentManager.findFragmentById(R.id.layout_container)
        if (fragment == null) {
            fragment = MeetingFragment()
            fragment.arguments = Bundle().apply {
                putBoolean(MeetingFragment.ARG_IS_TAB, false)
            }
            supportFragmentManager.beginTransaction()
                .add(R.id.layout_container, fragment, TAG_MEETING_LIST)
                .commitAllowingStateLoss()
        }
    }
}
