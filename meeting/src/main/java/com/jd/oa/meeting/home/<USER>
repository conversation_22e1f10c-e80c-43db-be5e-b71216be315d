package com.jd.oa.meeting.home

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.TextView
import com.jd.oa.meeting.R
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle
import com.scwang.smart.refresh.layout.simple.SimpleComponent

class ProgressRefreshHeader(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int
    ): SimpleComponent(context, attrs, defStyleAttr), Refresh<PERSON>eader {

    val mIvRefresh: ImageView
    val mTvLoading: TextView
    var mState: RefreshState? = null

    constructor(context: Context, attrs: AttributeSet?): this(context, attrs, 0)
    init {
        val view = LayoutInflater.from(context).inflate(R.layout.meeting_refresh_header, this, true)
        mIvRefresh = view.findViewById(R.id.iv_refresh)
        mTvLoading = view.findViewById(R.id.tv_loading)
    }

    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {
        super.onInitialized(kernel, height, maxDragHeight)
    }

    override fun onStateChanged(
        refreshLayout: RefreshLayout,
        oldState: RefreshState,
        newState: RefreshState
    ) {
        super.onStateChanged(refreshLayout, oldState, newState)
        mState = newState
        when (newState) {
            RefreshState.None, RefreshState.PullUpToLoad -> {
                mTvLoading.text = context.getString(R.string.meeting_refresh_pull)
            }
            RefreshState.Refreshing, RefreshState.RefreshReleased -> {
                mTvLoading.text = context.getString(R.string.meeting_refresh_loading)
            }
            RefreshState.RefreshFinish -> {
                mTvLoading.text = context.getString(R.string.meeting_refresh_complete)
            }
        }
    }

    override fun onMoving(
        isDragging: Boolean,
        percent: Float,
        offset: Int,
        height: Int,
        maxDragHeight: Int,
    ) {
        super.onMoving(isDragging, percent, offset, height, maxDragHeight)
        if (mState != RefreshState.RefreshFinish && mState != RefreshState.RefreshReleased) {
            mIvRefresh.pivotX = (mIvRefresh.width / 2).toFloat()
            mIvRefresh.pivotY = (mIvRefresh.height / 2).toFloat()
            mIvRefresh.rotation = 360 * percent
        }
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return super.getSpinnerStyle()
    }

    override fun onStartAnimator(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
        super.onStartAnimator(refreshLayout, height, maxDragHeight)

        if (mIvRefresh.animation == null) {
            val animation = AnimationUtils.loadAnimation(context, R.anim.meeting_refresh_loading)
            animation.interpolator = LinearInterpolator()
            mIvRefresh.startAnimation(animation)
        }
    }

    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        mIvRefresh.animation?.let {
            Handler(Looper.getMainLooper()).postDelayed({
                it.cancel()
                mIvRefresh.animation = null
            }, 1000)
        }
        return super.onFinish(refreshLayout, success)
    }
}