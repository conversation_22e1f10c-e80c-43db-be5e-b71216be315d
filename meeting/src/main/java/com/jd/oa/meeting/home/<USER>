package com.jd.oa.meeting.home

import android.text.TextUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.home.feedback.FeedbackItemData
import com.jd.oa.meeting.home.feedback.FeedBackItemDataDeserializer

class DbCacheStorage(val username: String): LocalCacheStorage {

    private val gson: Gson by lazy { Gson() }

    override fun putActionList(list: List<ActionItem>) {
        kotlin.runCatching {
            val content = gson.toJson(list)
            putCache(MeetingListRemoteDataSource.API_GET_ACTION_LIST, null, content)
        }
    }

    override fun putAppointmentList(list: List<Meeting>) {
        kotlin.runCatching {
            val content = gson.toJson(list)
            putCache(MeetingListRemoteDataSource.API_GET_APPOINTMENT_MEETINGS, null, content)
        }
    }

    override fun getAppointmentList(): List<Meeting>? {
        return runCatching<List<Meeting>> {
            val cache = loadCache(MeetingListRemoteDataSource.API_GET_APPOINTMENT_MEETINGS, null)
            if (TextUtils.isEmpty(cache)) {
                return null
            }
            return@runCatching gson.fromJson(cache, object : TypeToken<List<Meeting>>() {}.type)
        }.getOrNull()
    }

    override fun putHistoryList(history: List<Meeting>) {
        kotlin.runCatching {
            val content = gson.toJson(history)
            putCache(MeetingListRemoteDataSource.API_GET_HISTORY_MEETINGS, null, content)
        }
    }

    override fun getHistoryList(): List<Meeting>? {
        return runCatching<List<Meeting>> {
            val cache = loadCache(MeetingListRemoteDataSource.API_GET_HISTORY_MEETINGS, null)
            if (TextUtils.isEmpty(cache)) {
                return null
            }
            return@runCatching gson.fromJson(cache, object : TypeToken<List<Meeting>>() {}.type)
        }.getOrNull()
    }

    private fun loadCache(url: String, params: HashMap<String,String>?): String? {
        val cache = ResponseCacheGreenDaoHelper.loadCache(
            username,
            url,
            params
        ) ?: return null

        return cache.response
    }

    private fun putCache(url: String, params: HashMap<String,String>?, content: String) {
        ResponseCacheGreenDaoHelper.addCache(
            username,
            url,
            params,
            content
        )
    }

    override fun putFeedbackItems(list: List<FeedbackItemData<*>>) {
        kotlin.runCatching {
            val content = gson.toJson(list)
            val params = hashMapOf<String,String>(
                "key" to listOf("common.conference.help").toString()
            )
            putCache(MeetingListRemoteDataSource.API_GET_CONFIGURATION, params, content)
        }
    }

    override fun getFeedbackItems(): List<FeedbackItemData<*>>? {
        return runCatching<List<FeedbackItemData<*>>> {
            val params = hashMapOf<String,String>(
                "key" to listOf("common.conference.help").toString()
            )
            val cache = loadCache(MeetingListRemoteDataSource.API_GET_CONFIGURATION, params)
            if (TextUtils.isEmpty(cache)) {
                return null
            }

            return FeedBackItemDataDeserializer(cache!!).deserializer()
        }.getOrNull()
    }
}