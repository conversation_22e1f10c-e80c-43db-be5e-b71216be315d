package com.jd.oa.meeting.home

import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.ProjectionDocument
import com.jd.oa.meeting.home.feedback.FeedbackItemData
import kotlinx.coroutines.flow.Flow


interface MeetingListDataSource {
    suspend fun getActionList(noCache: Boolean = false): Flow<List<ActionItem>>

    suspend fun getHistoryMeetings(pageNumber: Int, pageSize: Int): PagedList<Meeting>

    suspend fun getAppointmentMeetings(): List<Meeting>

    suspend fun getProjectionDocuments(): List<ProjectionDocument>

    suspend fun quitScreenProjection(document: ProjectionDocument): Boolean

    suspend fun getFeedbackItems(): List<FeedbackItemData<*>>
}