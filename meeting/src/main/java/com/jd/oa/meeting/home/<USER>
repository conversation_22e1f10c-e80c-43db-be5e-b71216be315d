package com.jd.oa.meeting.home

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnAttachStateChangeListener
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.meeting.R
import com.jd.oa.theme.manager.Constants
import com.jd.oa.theme.view.JoyWorkTheme
import com.jd.oa.theme.view.JoyWorkThemeView
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.StatusBarConfig
import com.jd.oa.utils.argb
import com.qmuiteam.qmui.util.QMUIStatusBarHelper

abstract class MeetingTopBar(val activity: Activity) {

    lateinit var view: View

    abstract fun createView(context: Context, parent: ViewGroup): View

    abstract fun initView(view: View?)
}

class TabMeetingTopBar(activity: Activity, val onFeedbackClickListener: OnClickListener): MeetingTopBar(activity) {

    val themeView: JoyWorkThemeView by lazy { view.findViewById(R.id.theme_bg) }
    val titleContainer: View by lazy { view.findViewById(R.id.mTitleContainer) }
    val feedback: TextView by lazy { view.findViewById(R.id.tv_feedback) }

    private val mThemeDataChangeObserver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            onThemeDataChange()
        }
    }

    override fun createView(context: Context, parent: ViewGroup): View {
        val view = LayoutInflater.from(context).inflate(R.layout.meeting_tab_title, parent, true)
        return view
    }

    override fun initView(view: View?) {
        if (view == null) return
        val height = if (StatusBarConfig.enableImmersive()) {
            val titleContainer = view.findViewById<View>(R.id.mTitleContainer)
            val p = CommonUtils.dp2px(4f)
            val statusBarHeight = QMUIStatusBarHelper.getStatusbarHeight(activity)
            titleContainer.setPadding(3 * p, statusBarHeight, p, 0)
            statusBarHeight + activity.resources.getDimensionPixelSize(R.dimen.meeting_list_tabbar_title_height)
        } else {
            view.context.resources.getDimensionPixelSize(R.dimen.meeting_list_tabbar_title_height)
        }
        view.layoutParams.height = height

        view.addOnAttachStateChangeListener(object : OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(p0: View) {
                LocalBroadcastManager.getInstance(activity).registerReceiver(
                    mThemeDataChangeObserver,
                    IntentFilter(Constants.ACTION_CHANGE_THEME)
                )
            }

            override fun onViewDetachedFromWindow(p0: View) {
                LocalBroadcastManager.getInstance(activity).unregisterReceiver(mThemeDataChangeObserver)
            }
        })
        onThemeDataChange()
        feedback.setOnClickListener(onFeedbackClickListener)
    }

    private fun onThemeDataChange() {
        themeView.onThemeDataChange()
        val title: TextView = view.findViewById(R.id.mTitle)
        val themeData = JoyWorkTheme.currentTheme
        if (themeData.isGlobal && themeData.isDarkTheme) {
            title.argb("#ffffff")
            feedback.argb("#ffffff")
        } else {
            title.argb("#333333")
            feedback.argb("#333333")
        }
    }
}

class ActivityMeetingTopBar(activity: Activity, val onFeedbackClickListener: OnClickListener): MeetingTopBar(activity) {

    override fun createView(context: Context, parent: ViewGroup): View {
        val view = LayoutInflater.from(context).inflate(R.layout.meeting_activity_top_bar, parent, true)
        return view
    }

    override fun initView(view: View?) {
        val back = view?.findViewById<View>(R.id.back)
        back?.setOnClickListener {
            activity.finish()
        }

        val feedback = view?.findViewById<View>(R.id.tv_feedback)
        feedback?.setOnClickListener(onFeedbackClickListener)
    }
}