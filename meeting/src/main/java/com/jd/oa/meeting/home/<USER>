package com.jd.oa.meeting.home

import android.content.Context
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.AppBase
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jd.oa.abilities.apm.BuglyProLoader
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.meeting.DurationCountDownTimer
import com.jd.oa.meeting.R
import com.jd.oa.meeting.entity.Meeting
import com.jd.oa.meeting.entity.MeetingListItem
import com.jd.oa.meeting.entity.ProjectionDocument
import com.jd.oa.meeting.formatDuration
import com.jd.oa.meeting.formatStartAndEnd
import com.jd.oa.meeting.resetDateFormat
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import java.util.Calendar

class MeetingListAdapter(
    val lifecycleOwner: LifecycleOwner,
    val context: Context,
    val items: MutableList<MeetingListItem>,
    val onClickListener: (MeetingListItem, Int) -> Unit,
    val onJoinClickListener: (Meeting) -> Unit,
    val onQuitClickListener: ((document: ProjectionDocument) -> Unit)?
    ): RecyclerView.Adapter<MeetingViewHolder>() {

    val timer: DurationCountDownTimer = DurationCountDownTimer()

    companion object {
        const val TAG = "MeetingListAdapter"
        const val TYPE_ONGOING = 1
        const val TYPE_RESERVE = 2
        const val TYPE_HISTORY = 3
        const val TYPE_DOCUMENT_PROJECTION = 4
    }

    init {
        lifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                timer.cancel()
                timer.clearOnTickListener()
            }
        })
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        //timer.start()
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        //timer.cancel()
    }

    override fun getItemViewType(position: Int): Int {
        val item = items[position]
        return when {
            item is Meeting && item.isHistory ?: false -> {
                TYPE_HISTORY
            }
            item is Meeting && item.isOnGoing -> {
                TYPE_ONGOING
            }
            item is Meeting && (item.isBeginning || item.isCreated) -> {
                TYPE_RESERVE
            }
            item is Meeting -> {
                TYPE_RESERVE
            }
            item is ProjectionDocument -> {
                TYPE_DOCUMENT_PROJECTION
            }
            else -> {
                TYPE_RESERVE
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MeetingViewHolder {
        val holder = when (viewType) {
            TYPE_ONGOING -> {
                val view = LayoutInflater.from(context).inflate(R.layout.meeting_item_ongoing, parent, false);
                OngoingViewHolder(view, this, onJoinClickListener)
            }
            TYPE_DOCUMENT_PROJECTION -> {
                val view = LayoutInflater.from(context).inflate(R.layout.meeting_item_document_projection, parent, false)
                ProjectionDocumentViewHolder(view, this, onQuitClickListener)
            }
            TYPE_RESERVE -> {
                val view = LayoutInflater.from(context).inflate(R.layout.meeting_item_reserve, parent, false);
                ReserveViewHolder(view, this, onJoinClickListener)
            }
            TYPE_HISTORY -> {
                val view = LayoutInflater.from(context).inflate(R.layout.meeting_item_history, parent, false);
                HistoryViewHolder(view, this)
            }
            else -> {
                throw IllegalStateException()
            }
        }
        holder.itemView.setOnClickListener {
            val position = holder.bindingAdapterPosition
            if (position == RecyclerView.NO_POSITION) return@setOnClickListener
            val meeting = items[position]
            onClickListener.invoke(meeting, position)
        }
        return holder
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: MeetingViewHolder, position: Int) {
        kotlin.runCatching {
            val item = items[position]
            holder.onBindViewHolder(holder, item, position)
        }.onFailure {
            it.printStackTrace()
            val buglyLoader = BuglyProLoader(
                AppBase.getAppContext(),
                ApmLoaderHepler.getInstance(AppBase.getAppContext()).configModel
            )
            buglyLoader.upLoadException(it)
        }
    }

    fun refresh(items: List<MeetingListItem>) {
        this.items.clear()
        this.items.addAll(items)
        resetTimer(items.any {
            (it is Meeting && it.isOnGoing) || (it is ProjectionDocument && it.isOngoing)
        })
        this.notifyDataSetChanged()
    }

    private fun resetTimer(start: Boolean) = when {
        start && !timer.isStart -> {
            timer.start()
        }
        !start -> {
            timer.cancel()
        }
        else -> {}
    }

    override fun onViewRecycled(holder: MeetingViewHolder) {
        super.onViewRecycled(holder)
    }

    override fun onViewDetachedFromWindow(holder: MeetingViewHolder) {
        super.onViewDetachedFromWindow(holder)
        holder.onViewDetachedFromWindow()
    }

    fun isLastItemType(
        index: Int,
        currentTypePredicate: (itemType: Int) -> Boolean,
        nexTypePredicate: (nextItemType: Int) -> Boolean
    ): Boolean {
        val itemType = getItemViewType(index)
        if (currentTypePredicate(itemType)) {
            if (index == itemCount - 1) return true
            val nextType = getItemViewType(index + 1)
            if (nexTypePredicate(nextType)) return true
        }
        return false
    }

    fun resetDataCalendar() {
        resetDateFormat()
        this.items.filterIsInstance<Meeting>().forEach {
            it.resetCalendarObj()
        }
    }
}

abstract class MeetingViewHolder(itemView: View, val adapter: MeetingListAdapter) : RecyclerView.ViewHolder(itemView) {

    val context: Context get() = itemView.context

    abstract fun onBindViewHolder(holder: MeetingViewHolder, item: MeetingListItem, position: Int)

    open fun onViewDetachedFromWindow() {}
}

class OngoingViewHolder(
    itemView: View,
    adapter: MeetingListAdapter,
    joinClickListener: ((Meeting) -> Unit)?
  ): MeetingViewHolder(itemView, adapter) {
    companion object {
        const val TAG = "OngoingViewHolder"
    }

    val subject: TextView by lazy { itemView.findViewById(R.id.tv_subject) }
    val duration: TextView by lazy { itemView.findViewById(R.id.tv_duration) }
    val id: TextView by lazy { itemView.findViewById(R.id.tv_id) }
    val join: Button by lazy { itemView.findViewById(R.id.btn_join) }
    val joinLayout: View by lazy { itemView.findViewById(R.id.layout_join) }
    val timeLayout: LinearLayout by lazy { itemView.findViewById(R.id.time_layout) }
    val divider: View by lazy { itemView.findViewById(R.id.divider) }
    val subTitle: View by lazy { itemView.findViewById(R.id.layout_subtitle) }
    val subTitleText: TextView by lazy { itemView.findViewById(R.id.tv_subtitle) }
    val groupDivider: View by lazy { itemView.findViewById(R.id.view_divider) }

    init {
        val onJoinClickListener = View.OnClickListener {
            val position = bindingAdapterPosition
            if (position == RecyclerView.NO_POSITION) return@OnClickListener
            val meeting = adapter.items[position] as Meeting
            joinClickListener?.invoke(meeting)
        }
        join.setOnClickListener(onJoinClickListener)
        joinLayout.setOnClickListener(onJoinClickListener)
    }
    override fun onBindViewHolder(holder: MeetingViewHolder, item: MeetingListItem, position: Int) {
        val meeting = item as Meeting
        subject.text = meeting.subject

        val startTime = meeting.realStartTimeInCalendar ?: meeting.realJoinTimeInCalendar ?: meeting.startTimeInCalendar ?: meeting.scheduleStartTimeInCalendar
        if (startTime != null) {
            val durationInMillis = Calendar.getInstance().timeInMillis - startTime.timeInMillis
            duration.text = formatDuration(context, durationInMillis)
            duration.tag = startTime

            adapter.timer.addOnTickListener(::onTimerTick)
        }

        id.text = context.getString(R.string.meeting_list_id, meeting.meetingCode?.toString())

        join.text = if (meeting.joined) context.getString(R.string.meeting_joined) else context.getString(R.string.meeting_join)
        join.isEnabled = !meeting.joined

        if (adapter.items.hasProjectionDocuments()) {
            subTitleText.text = context.getString(R.string.meeting_video_meeting)
            if (position == 0) {
                subTitle.visibility = View.VISIBLE
            } else {
                val pre = adapter.items[position - 1]
                if (pre !is Meeting) {
                    subTitle.visibility = View.VISIBLE
                } else {
                    subTitle.visibility = View.GONE
                }
            }
        } else {
            subTitle.visibility = View.GONE
        }

        val isLastOngoing = adapter.isLastItemType(bindingAdapterPosition, {
            return@isLastItemType it == MeetingListAdapter.TYPE_ONGOING || it == MeetingListAdapter.TYPE_DOCUMENT_PROJECTION
        }, {
            return@isLastItemType !(it == MeetingListAdapter.TYPE_ONGOING || it == MeetingListAdapter.TYPE_DOCUMENT_PROJECTION);
        })
        groupDivider.visibility = if (isLastOngoing) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    private fun onTimerTick(countTime: Long) {
        val startTime = duration.tag
        if (startTime != null && startTime is Calendar) {
            duration.text = formatDuration(context, Calendar.getInstance().timeInMillis - startTime.timeInMillis)
        }
    }

    override fun onViewDetachedFromWindow() {
        Log.d(TAG, "onViewDetachedFromWindow: ")
        adapter.timer.removeOnTickListener(::onTimerTick)
    }
}

class ProjectionDocumentViewHolder(itemView: View, adapter: MeetingListAdapter, onQuitClickListener: ((document: ProjectionDocument) -> Unit)?): MeetingViewHolder(itemView, adapter) {

    val subject: TextView by lazy { itemView.findViewById(R.id.tv_subject) }
    val duration: TextView by lazy { itemView.findViewById(R.id.tv_duration) }
    val room: TextView by lazy { itemView.findViewById(R.id.tv_room) }
    val quit: Button by lazy { itemView.findViewById(R.id.btn_quit) }
    val quitLayout: View by lazy { itemView.findViewById(R.id.layout_quit) }
    val timeLayout: LinearLayout by lazy { itemView.findViewById(R.id.time_layout) }
    val divider: View by lazy { itemView.findViewById(R.id.divider) }
    val ongoing: ImageView by lazy { itemView.findViewById(R.id.iv_ongoing) }
    val status: TextView by lazy { itemView.findViewById(R.id.tv_status) }
    val subTitle: View by lazy { itemView.findViewById(R.id.layout_subtitle) }
    val subTitleText: TextView by lazy { itemView.findViewById(R.id.tv_subtitle) }
    val groupDivider: View by lazy { itemView.findViewById(R.id.view_divider) }

    init {
        val onJoinClickListener = View.OnClickListener {
            val position = bindingAdapterPosition
            if (position == RecyclerView.NO_POSITION) return@OnClickListener
            val meeting = adapter.items[position] as ProjectionDocument
            onQuitClickListener?.invoke(meeting)
        }
        quit.setOnClickListener(onJoinClickListener)
        quitLayout.setOnClickListener(onJoinClickListener)
    }

    override fun onBindViewHolder(holder: MeetingViewHolder, item: MeetingListItem, position: Int) {
        val document = item as ProjectionDocument
        subject.text = document.bizName
        room.text = document.meetingRoomName
        if (document.isOngoing) {
            ongoing.visibility = View.VISIBLE
            status.text = context.getString(R.string.meeting_status_ongoing1)
            quitLayout.visibility = View.VISIBLE
        } else if (document.isEnd) {
            ongoing.visibility = View.GONE
            quitLayout.visibility = View.GONE
            status.text = context.getString(R.string.meeting_status_end)
        } else {
            ongoing.visibility = View.GONE
            quitLayout.visibility = View.VISIBLE
            status.text = null
        }

        val startTime = document.projectStartAtInCalendar
        if (startTime != null) {
            val durationInMillis = Calendar.getInstance().timeInMillis - startTime.timeInMillis
            duration.text = formatDuration(context, durationInMillis)
            duration.tag = startTime

            adapter.timer.addOnTickListener(::onTimerTick)
        }

        if (adapter.items.hasOngoingMeetings()) {
            subTitleText.text = context.getString(R.string.meeting_projection_document_title)
            if (position == 0) {
                subTitle.visibility = View.VISIBLE
            } else {
                val pre = adapter.items[position - 1]
                if (pre !is ProjectionDocument) {
                    subTitle.visibility = View.VISIBLE
                } else {
                    subTitle.visibility = View.GONE
                }
            }
        } else {
            subTitle.visibility = View.GONE
        }

        val isLastOngoing = adapter.isLastItemType(bindingAdapterPosition, {
            return@isLastItemType it == MeetingListAdapter.TYPE_ONGOING || it == MeetingListAdapter.TYPE_DOCUMENT_PROJECTION
        }, {
            return@isLastItemType !(it == MeetingListAdapter.TYPE_ONGOING || it == MeetingListAdapter.TYPE_DOCUMENT_PROJECTION);
        })
        groupDivider.visibility = if (isLastOngoing) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    private fun onTimerTick(countTime: Long) {
        val startTime = duration.tag
        if (startTime != null && startTime is Calendar) {
            duration.text = formatDuration(context, Calendar.getInstance().timeInMillis - startTime.timeInMillis)
        }
    }
    override fun onViewDetachedFromWindow() {
        adapter.timer.removeOnTickListener(::onTimerTick)
    }
}

class ReserveViewHolder(itemView: View, adapter: MeetingListAdapter, joinClickListener: ((Meeting) -> Unit)?): MeetingViewHolder(itemView, adapter) {

    val subject: TextView by lazy { itemView.findViewById(R.id.tv_subject) }
    val time: TextView by lazy { itemView.findViewById(R.id.tv_time) }
    val id: TextView by lazy { itemView.findViewById(R.id.tv_id) }
    val join: Button by lazy { itemView.findViewById(R.id.btn_join) }
    val joinLayout: View by lazy { itemView.findViewById(R.id.layout_join) }
    val timeLayout: LinearLayout by lazy { itemView.findViewById(R.id.time_layout) }
    val divider: View by lazy { itemView.findViewById(R.id.divider) }
    val groupDivider: View by lazy { itemView.findViewById(R.id.view_divider) }

    init {
        val onJoinClickListener = View.OnClickListener {
            val position = bindingAdapterPosition
            if (position == RecyclerView.NO_POSITION) return@OnClickListener
            val meeting = adapter.items[position] as Meeting
            joinClickListener?.invoke(meeting)
        }
        join.setOnClickListener(onJoinClickListener)
        joinLayout.setOnClickListener(onJoinClickListener)
    }
    override fun onBindViewHolder(holder: MeetingViewHolder, item: MeetingListItem, position: Int) {
        val meeting = item as Meeting
        subject.text = meeting.subject
        id.text = context.getString(R.string.meeting_list_id, meeting.meetingCode?.toString())
        join.text = if (meeting.joined) context.getString(R.string.meeting_joined) else context.getString(R.string.meeting_join)
        join.isEnabled = !meeting.joined
        time.text = formatStartAndEnd(context, meeting.startTimeInCalendar, meeting.endTimeInCalendar)
        divider.visibility = if (TextUtils.isEmpty(time.text)) View.GONE else View.VISIBLE

        val isLastOngoing = adapter.isLastItemType(bindingAdapterPosition, {
            return@isLastItemType it == MeetingListAdapter.TYPE_RESERVE
        }, {
            return@isLastItemType it != MeetingListAdapter.TYPE_RESERVE
        })
        groupDivider.visibility = if (isLastOngoing) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }
}

class HistoryViewHolder(itemView: View, adapter: MeetingListAdapter): MeetingViewHolder(itemView, adapter) {

    val subject: TextView by lazy { itemView.findViewById(R.id.tv_subject) }
    val time: TextView by lazy { itemView.findViewById(R.id.tv_time) }
    val duration: TextView by lazy { itemView.findViewById(R.id.tv_duration) }
    val divider: View by lazy { itemView.findViewById(R.id.divider) }
    val timeLayout: LinearLayout by lazy { itemView.findViewById(R.id.time_layout) }
    //val cover: ImageView by lazy { itemView.findViewById(R.id.iv_cover) }
    val icon: ImageView by lazy { itemView.findViewById(R.id.iv_icon) }
    val tvIcon: TextView by lazy { itemView.findViewById(R.id.tv_icon) }
    val asr: ImageView by lazy { itemView.findViewById(R.id.iv_asr) }
    val background: ImageView by lazy { itemView.findViewById(R.id.view_bg) }

    private val isJoyMinutes: Boolean by lazy { TenantConfigBiz.isJoyMinutes() }//是否展示会议纪要ui

    override fun onBindViewHolder(holder: MeetingViewHolder, item: MeetingListItem, position: Int) {
        val meeting = item as Meeting
        subject.text = meeting.subject
        time.text = formatStartAndEnd(
            context,
            meeting.realStartTimeInCalendar ?: meeting.startTimeInCalendar,
            meeting.realEndTimeInCalendar
        )
        duration.text = formatDuration(itemView.context, meeting.duration)
        divider.visibility = if (TextUtils.isEmpty(time.text)) View.GONE else View.VISIBLE

        if ((meeting.recordStatus == Meeting.RECORD_STATUS_ON ||
                meeting.asrStatus == Meeting.ASR_STATUS_ON) && isJoyMinutes) {
            background.setImageResource(R.drawable.meeting_list_history)
            tvIcon.visible()
            icon.gone()
        } else {
            background.setImageResource(R.drawable.meeting_item_bg_reserve)
            icon.setImageResource(R.drawable.meeting_ic_reserve)
            tvIcon.gone()
            icon.visible()
        }

        asr.visibility = if (meeting.asrStatus == Meeting.ASR_STATUS_ON && isJoyMinutes) View.VISIBLE else View.GONE
    }
}

private fun List<MeetingListItem>.hasOngoingMeetings(): Boolean {
    return this.any { it is Meeting && it.isOnGoing }
}

private fun List<MeetingListItem>.hasProjectionDocuments(): Boolean {
    return this.any { it is ProjectionDocument }
}