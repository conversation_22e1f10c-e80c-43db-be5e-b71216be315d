package com.jd.oa.meeting.detail

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.meeting.R

class LoadingStateAdapter(val context: Context, var loadingState: Int, val callback: (() -> Unit)?) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val STATE_LOADING = 1
        const val STATE_ERROR = 2
    }

    override fun onCreateViewHolder(parent: ViewGroup, position: Int): RecyclerView.ViewHolder {
        val layoutId = if (loadingState == STATE_LOADING) R.layout.meeting_detail_loading_empty else R.layout.meeting_detail_loading_error
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return object : RecyclerView.ViewHolder(view) {}
    }

    override fun getItemCount(): Int {
        return 1
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        holder.apply {
            if (loadingState == STATE_ERROR) {
                itemView.findViewById<TextView>(R.id.tv_refresh).setOnClickListener {
                    callback?.invoke()
                }
            }
        }
    }
}