<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="18dp"
    android:gravity="center">

    <com.jd.oa.meeting.home.LoadingView
        android:id="@+id/loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:textColor="#999999"
        android:text="@string/meeting_refresh_loading"/>
    <TextView
        android:id="@+id/tv_failed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="#4C7CFF"
        android:text="@string/meeting_load_failed_retry"/>
</LinearLayout>