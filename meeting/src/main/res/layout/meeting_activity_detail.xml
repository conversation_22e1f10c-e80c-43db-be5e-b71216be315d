<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingVertical="12dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="@string/icon_direction_left"
            android:textSize="@dimen/JMEIcon_20"/>

        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_share"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:paddingVertical="12dp"
            android:text="@string/icon_share"
            android:textSize="@dimen/JMEIcon_20"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <View
        android:background="#F5F5F5"
        android:layout_width="match_parent"
        android:layout_height="1dp" />
    <LinearLayout
        android:id="@+id/btn_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/meeting_bg_buttons"
        android:paddingHorizontal="16dp"
        android:paddingTop="4dp"
        android:paddingBottom="16dp"
        android:visibility="gone"
        android:orientation="horizontal"
        tools:visibility="visible">
        <Button
            style="@style/Widget.AppCompat.Button.Borderless"
            android:id="@+id/btn_copy"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:background="@drawable/meeting_bg_meeting_button_copy"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingVertical="12dp"
            android:text="@string/meeting_detail_copy_meeting_info"
            android:textColor="@color/meeting_detail_text_color" />
        <Button
            style="@style/Widget.AppCompat.Button.Borderless"
            android:id="@+id/btn_join"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:background="@drawable/meeting_bg_meeting_button_join"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingVertical="12dp"
            android:text="@string/meeting_join_meeting"
            android:textColor="@color/jdme_bg_jd_meeting_join" />
    </LinearLayout>
</LinearLayout>