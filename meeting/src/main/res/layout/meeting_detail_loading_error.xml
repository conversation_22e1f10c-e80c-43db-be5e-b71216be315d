<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/meeting_detail_loading_error" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#8F959E"
        android:layout_marginTop="30dp"
        android:text="@string/meeting_list_error_text" />

    <TextView
        android:id="@+id/tv_refresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:paddingStart="26dp"
        android:background="@drawable/meeting_item_bg_attachment"
        android:paddingTop="9dp"
        android:paddingEnd="26dp"
        android:paddingBottom="9dp"
        android:textColor="#232930"
        android:text="@string/meeting_refresh" />
</LinearLayout>