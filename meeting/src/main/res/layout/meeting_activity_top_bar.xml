<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:gravity="center_vertical">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/back"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_direction_left"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_22"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:includeFontPadding="false"
        android:text="@string/meeting_tab_title"
        android:textColor="#232930"
        android:textSize="18dp"
        android:textStyle="bold" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_feedback"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="10dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:textColor="#1B1B1B"
        android:textSize="@dimen/JMEIcon_22"
        android:text="@string/icon_prompt_questioncircle"/>
</RelativeLayout>