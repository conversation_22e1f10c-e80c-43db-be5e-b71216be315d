<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:gravity="center_vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="16dp">

    <FrameLayout
        android:layout_width="78dp"
        android:layout_height="44dp">

        <com.jd.oa.ui.SimpleRoundImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:round_radius="4.33dp"
            android:src="@drawable/meeting_item_bg_reserve"/>

        <ImageView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/meeting_ic_video"
            android:visibility="gone"
            tools:visibility="visible"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textSize="@dimen/JMEIcon_20"
            android:text="@string/icon_general_play"
            android:textColor="@color/white"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/iv_asr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="4dp"
            android:src="@drawable/meeting_ic_list_joynote" />

        <TextView
            android:id="@+id/tv_generating"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="#80FFFFFF"
            android:gravity="center"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:textColor="#1869F5"
            android:textSize="10sp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="生成中..." />
    </FrameLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_note_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_333333"
            android:textSize="14sp"
            tools:text="线上线下一体化设计评审会线上线下一体化设计评审会线上线下一体化设计评审会" />

        <TextView
            android:id="@+id/tv_note_owner"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_666666"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="所有者：李孝利" />
        <com.jd.oa.meeting.view.TimeLayout
            android:id="@+id/layout_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            tools:visibility="visible">
            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/color_666666"
                android:textSize="12dp"
                tools:text="2024/12/24 18:00-19:00" />
            <View
                android:layout_width="1dp"
                android:layout_height="12dp"
                android:layout_marginTop="4dp"
                android:layout_marginHorizontal="4dp"
                android:background="#CECECE"
                app:childType="divider"/>
            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/color_666666"
                android:textSize="12dp"
                tools:text="29小时30分23秒" />
        </com.jd.oa.meeting.view.TimeLayout>

    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/note_share"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:text="@string/icon_share"
        android:textColor="#1869F5"
        android:textSize="@dimen/JMEIcon_16"
        android:background="@drawable/meeting_detail_note_share_bg"
        android:visibility="invisible"
        tools:visibility="visible" />

</LinearLayout>