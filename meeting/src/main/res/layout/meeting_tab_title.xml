<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.jd.oa.theme.view.JoyWorkThemeView
        android:id="@+id/theme_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/mTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:text="@string/meeting_tab_title"
                android:textColor="#333333"
                android:textSize="22dp"
                android:textStyle="bold" />
            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"/>
            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_feedback"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="12dp"
                android:paddingVertical="10dp"
                android:textColor="#1B1B1B"
                android:textSize="@dimen/JMEIcon_22"
                android:text="@string/icon_prompt_questioncircle"/>
        </LinearLayout>
    </FrameLayout>
</FrameLayout>