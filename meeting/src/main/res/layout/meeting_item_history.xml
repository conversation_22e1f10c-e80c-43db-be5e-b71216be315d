<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingVertical="10dp"
    android:background="@android:color/white"
    android:gravity="center_vertical">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        
        <com.jd.oa.ui.SimpleRoundImageView
            android:id="@+id/view_bg"
            android:layout_width="78dp"
            android:layout_height="44dp"
            app:round_radius="4.33dp"
            android:src="@drawable/meeting_item_bg_reserve"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/meeting_item_bg_history"
            android:visibility="invisible"
            tools:visibility="visible"/>

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/meeting_ic_reserve" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textSize="@dimen/JMEIcon_20"
            android:text="@string/icon_general_play"
            android:textColor="@color/white"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/iv_asr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="4dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/meeting_ic_list_joynote"/>
    </FrameLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="10dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_subject"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/meeting_title_text_color"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="线上线下一体化设计评审线上线下一体化设计评审"/>

        <LinearLayout
            android:id="@+id/time_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_gravity="center_vertical">
            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="@color/meeting_sub_title_text_color"
                tools:text="56:23"/>
            <View
                android:id="@+id/divider"
                android:layout_width="1dp"
                android:layout_height="12dp"
                android:background="#D9D9D9"
                android:layout_marginTop="2dp"
                android:layout_marginHorizontal="6dp"
                app:childType="divider"/>
            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="12sp"
                android:textColor="@color/meeting_sub_title_text_color"
                tools:text="ID:267778"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>