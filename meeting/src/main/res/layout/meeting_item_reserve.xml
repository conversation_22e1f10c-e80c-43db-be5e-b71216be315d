<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <FrameLayout
            android:layout_width="78dp"
            android:layout_height="44dp"
            android:layout_marginVertical="10dp"
            android:layout_marginStart="16dp"
            android:background="@drawable/meeting_item_bg_reserve">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/meeting_ic_reserve"/>
        </FrameLayout>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_subject"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="@color/meeting_title_text_color"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="线上线下一体化设计评审线上线下一体化设计评审"/>

            <LinearLayout
                android:id="@+id/time_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">
                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="@color/meeting_sub_title_text_color"
                    tools:text="今天 17：30 - 56:23"/>
                <View
                    android:id="@+id/divider"
                    android:layout_width="1dp"
                    android:layout_height="12dp"
                    android:background="#D9D9D9"
                    android:layout_marginHorizontal="6dp"
                    app:childType="divider"/>
                <TextView
                    android:id="@+id/tv_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textSize="12sp"
                    android:textColor="@color/meeting_sub_title_text_color"
                    tools:text="ID:267778"/>
            </LinearLayout>
        </LinearLayout>
        <FrameLayout
            android:id="@+id/layout_join"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="20dp"
            android:paddingEnd="16dp">
            <Button
                android:id="@+id/btn_join"
                style="@style/MeetingJoinButton"
                android:layout_gravity="center"
                android:text="@string/meeting_join"/>
        </FrameLayout>
    </LinearLayout>
    <include
        layout="@layout/meeting_item_group_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginVertical="12dp"/>
</LinearLayout>