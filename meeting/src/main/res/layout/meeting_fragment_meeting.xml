<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    </FrameLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:srlEnableOverScrollBounce="false"
            app:srlEnableLoadMore="false"
            android:background="#F5F5F5">
            <com.jd.oa.meeting.home.ProgressRefreshHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_conferences"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
                <ViewStub
                    android:id="@+id/stub_skeleton"
                    android:layout="@layout/meeting_list_skeleton"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
                <ViewStub
                    android:id="@+id/stub_empty"
                    android:layout="@layout/meeting_list_empty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
                <ViewStub
                    android:id="@+id/stub_error"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout="@layout/meeting_list_error"/>
            </FrameLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:background="@android:color/white"
            app:elevation="0dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_action"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="12dp" />
                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:background="#F5F5F5"/>
            </LinearLayout>

        </com.google.android.material.appbar.AppBarLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</LinearLayout>