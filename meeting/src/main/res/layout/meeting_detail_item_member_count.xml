<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <TextView
        android:id="@+id/tv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:minWidth="28dp"
        android:minHeight="28dp"
        android:paddingHorizontal="4dp"
        android:gravity="center"
        android:textSize="12sp"
        android:textColor="#6A6A6A"
        android:background="@drawable/meeting_detail_member_count_bg"
        tools:text="+8"
        tools:visibility="visible" />
</FrameLayout>