<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_marginEnd="8dp"
    android:layout_height="wrap_content">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_head"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:src="@drawable/jdme_app_icon"
        tools:src="@drawable/jdme_app_icon" />

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_alignBottom="@id/iv_head"
        android:layout_height="11dp">

        <View
            android:id="@+id/view_bg"
            android:layout_width="28dp"
            android:layout_height="14dp"
            android:layout_gravity="bottom"
            android:background="@drawable/meeting_item_bg_host"
            android:visibility="gone"
            tools:visibility="visible" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_host"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:layout_alignBottom="@id/iv_head"
        android:layout_centerHorizontal="true"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="7dp"
        android:visibility="gone"
        android:text="主持人"
        tools:text="主持人"
        tools:visibility="visible" />

</RelativeLayout>