<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:gravity="center_vertical"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#1B1B1B"
        android:textSize="12sp"
        tools:text="共享文档"/>

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1"
        android:layout_marginStart="10dp"
        android:background="#0F000000"
        android:visibility="gone"/>
</LinearLayout>