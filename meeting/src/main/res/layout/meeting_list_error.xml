<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    android:clickable="true">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="center"
        android:layout_marginTop="-48dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/iv_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/meeting_list_error"/>

        <TextView
            android:id="@+id/tv_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:textColor="#8F959E"
            android:textSize="14sp"
            android:text="@string/meeting_list_error_text"/>
        <Button
            android:id="@+id/btn_retry"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:paddingHorizontal="26dp"
            android:paddingVertical="8dp"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="@string/meeting_list_error_refresh"
            android:background="@drawable/meeting_list_refresh_btn"
            android:textColor="@color/textColorPrimary"/>
    </LinearLayout>
</FrameLayout>