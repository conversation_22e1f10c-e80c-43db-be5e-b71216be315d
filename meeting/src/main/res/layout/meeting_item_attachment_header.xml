<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="20dp"
    android:paddingBottom="4dp"
    android:background="@android:color/white">

    <TextView
        android:id="@+id/tv_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:text="@string/meeting_attachment"
        android:textColor="@color/meeting_detail_title_text_color"
        android:textSize="14sp" />

</FrameLayout>