<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <View
        android:id="@+id/magic"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/meeting_users_title"
        android:textSize="18sp"
        android:textColor="@color/meeting_detail_text_color"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/toolbar" />

    <com.jd.oa.meeting.view.ComposeEditText
        android:id="@+id/search"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:paddingVertical="8dp"
        android:paddingHorizontal="12dp"
        android:drawableLeft="@drawable/meeting_ic_search_conference_participant_gray"
        android:drawablePadding="6dp"
        android:hint="@string/meeting_users_hint_search"
        android:imeOptions="actionSearch"
        android:background="@drawable/meeting_edit_text_background"
        android:singleLine="true"
        android:textSize="15sp"
        app:layout_constraintEnd_toStartOf="@id/cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_goneMarginEnd="15dp" />

    <Button
        android:id="@+id/cancel"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:minWidth="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:background="?android:selectableItemBackground"
        android:onClick="onClick"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="@string/meeting_button_cancel"
        android:textColor="?android:textColorPrimary"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/search"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/search"
        app:layout_constraintTop_toTopOf="@id/search"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="7dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search"
        tools:listitem="@layout/meeting_activity_conference_user_list_item" />

    <TextView
        android:id="@+id/noResult"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:text="@string/meeting_users_no_search_result"
        android:textColor="@color/color_999999"
        android:textSize="15sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/list"
        app:layout_constraintStart_toStartOf="@id/list"
        app:layout_constraintTop_toTopOf="@id/list"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>