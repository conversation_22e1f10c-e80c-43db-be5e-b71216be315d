<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#F5F5F5"
    android:paddingTop="16dp">

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="16dp"
        android:text="2023年11月30日"
        android:textColor="@color/color_666666"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2023年11月30日" />

    <View
        android:id="@+id/meetingView"
        android:layout_width="7dp"
        android:layout_height="7dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/meeting_item_oval_gray"
        app:layout_constraintBottom_toBottomOf="@+id/tv_date"
        app:layout_constraintStart_toEndOf="@+id/tv_date"
        app:layout_constraintTop_toTopOf="@+id/tv_date" />

    <TextView
        android:id="@+id/tv_join_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:text="21:00   加入会议"
        android:textColor="@color/color_666666"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_date"
        app:layout_constraintStart_toEndOf="@+id/meetingView"
        app:layout_constraintTop_toTopOf="@+id/tv_date"
        tools:text="21:00   加入会议" />

    <TextView
        android:id="@+id/tv_exit_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="22:00   离开会议"
        android:textColor="@color/color_666666"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@+id/tv_join_time"
        app:layout_constraintTop_toBottomOf="@+id/tv_join_time"
        tools:text="22:00   离开会议" />
</androidx.constraintlayout.widget.ConstraintLayout>