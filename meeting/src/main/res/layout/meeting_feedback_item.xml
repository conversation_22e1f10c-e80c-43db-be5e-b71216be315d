<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="24dp"
    android:layout_marginEnd="20dp"
    android:layout_marginTop="6dp"
    android:gravity="top">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical">
        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_float_play"
            android:textSize="@dimen/JMEIcon_10"
            android:textColor="#BFBFBF"/>
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:textColor="#333333"
            android:lineSpacingExtra="6sp"
            tools:text="加入咚咚群："/>
    </LinearLayout>

    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/layout_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        app:flexDirection="row"
        app:flexWrap="wrap">
    </com.google.android.flexbox.FlexboxLayout>
</LinearLayout>