<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="26dp"
    android:minWidth="320dp"
    android:maxWidth="320dp"
    android:background="@drawable/meeting_bg_feedback_dialog">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="36dp"
        android:layout_marginStart="20dp"
        android:src="@drawable/meeting_ic_feedback"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        android:layout_marginStart="6dp"
        android:textSize="22dp"
        android:textColor="#333333"
        android:textStyle="bold"
        android:text="@string/meeting_feedback_title" />
    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingVertical="24dp"
        android:paddingHorizontal="24dp"
        android:textSize="@dimen/JMEIcon_18"
        android:textColor="#999999"
        android:text="@string/icon_prompt_close"/>

    <LinearLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        android:layout_marginTop="18dp"
        android:orientation="vertical">

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/layout_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="6dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_float_play"
            android:textSize="@dimen/JMEIcon_10"
            android:textColor="#BFBFBF"/>
        <TextView
            android:id="@+id/tv_upload_log"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:textColor="#333333"
            android:lineSpacingExtra="6sp"
            android:text="@string/meeting_feedback_upload_log"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>