<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="16dp">

    <View
        android:id="@+id/view1"
        android:layout_width="78dp"
        android:layout_height="17dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintHorizontal_weight="0.25"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view2"
        android:layout_width="match_parent"
        android:layout_height="37dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view1" />

    <View
        android:id="@+id/meetingView3"
        android:layout_width="match_parent"
        android:layout_height="37dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view2" />

    <View
        android:id="@+id/meetingView4"
        android:layout_width="78dp"
        android:layout_height="17dp"
        android:layout_marginTop="25dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/meetingView3" />

    <View
        android:id="@+id/meetingView6"
        android:layout_width="100dp"
        android:layout_height="56dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/meetingView4" />

    <View
        android:id="@+id/meetingView7"
        android:layout_width="148dp"
        android:layout_height="17dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintStart_toEndOf="@+id/meetingView6"
        app:layout_constraintTop_toTopOf="@+id/meetingView6" />

    <View
        android:id="@+id/meetingView10"
        android:layout_width="78dp"
        android:layout_height="17dp"
        android:layout_marginStart="12dp"
        android:layout_marginBottom="6dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintBottom_toBottomOf="@+id/meetingView6"
        app:layout_constraintStart_toEndOf="@+id/meetingView6" />

    <View
        android:id="@+id/meetingView8"
        android:layout_width="100dp"
        android:layout_height="56dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/meetingView6" />

    <View
        android:id="@+id/meetingView9"
        android:layout_width="148dp"
        android:layout_height="17dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintStart_toEndOf="@+id/meetingView8"
        app:layout_constraintTop_toTopOf="@+id/meetingView8" />

    <View
        android:layout_width="78dp"
        android:layout_height="17dp"
        android:layout_marginStart="12dp"
        android:layout_marginBottom="6dp"
        android:background="@drawable/meeting_detail_empty"
        app:layout_constraintBottom_toBottomOf="@+id/meetingView8"
        app:layout_constraintStart_toEndOf="@+id/meetingView8" />

    <com.jd.oa.ui.LoadingView
        android:layout_width="match_parent"
        tools:visibility="gone"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>