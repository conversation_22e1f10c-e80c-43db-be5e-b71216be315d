<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white">

    <LinearLayout
        android:id="@+id/layout_expand"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:padding="16dp"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/meeting_attachment_expand"
            android:textColor="#4C7CFF"
            android:textSize="12dp" />
        <com.jd.oa.ui.IconFontView
            android:id="@+id/icon_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#4C7CFF"
            android:layout_marginStart="4dp"
            android:textSize="@dimen/JMEIcon_14"
            android:text="@string/icon_direction_down"/>
    </LinearLayout>

</FrameLayout>