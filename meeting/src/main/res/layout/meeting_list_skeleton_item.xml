<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="10dp"
    android:gravity="center_vertical">

    <View
        android:layout_width="78dp"
        android:layout_height="44dp"
        android:background="@drawable/meeting_skeleton_bg"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="10dp"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <View
                android:layout_width="0dp"
                android:layout_height="17dp"
                android:layout_weight="4"
                android:background="@drawable/meeting_skeleton_bg"/>

            <View
                android:layout_width="0dp"
                android:layout_height="17dp"
                android:layout_weight="1" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp">
            <View
                android:layout_width="0dp"
                android:layout_height="17dp"
                android:layout_weight="5"
                android:background="@drawable/meeting_skeleton_bg"/>

            <View
                android:layout_width="0dp"
                android:layout_height="17dp"
                android:layout_weight="4" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>