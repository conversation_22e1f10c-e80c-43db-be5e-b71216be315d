<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="12dp"
    android:background="?android:selectableItemBackground">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:background="@drawable/meeting_default_avatar"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.Conference.Circle" />

    <TextView
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        app:layout_constraintStart_toEndOf="@id/avatar"
        app:layout_constraintEnd_toStartOf="@id/duty_icon"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_dept"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintWidth_default="wrap"
        android:textSize="16sp"
        android:textColor="@color/meeting_detail_text_color"
        tools:text="wangwangyang" />
    <TextView
        android:id="@+id/tv_dept"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/name"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="2dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="14sp"
        android:textColor="@color/meeting_detail_icon_color"
        tools:text="wangwangyang" />

    <ImageView
        android:id="@+id/duty_icon"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginLeft="6dp"
        app:layout_constraintBottom_toBottomOf="@id/name"
        app:layout_constraintLeft_toRightOf="@id/name"
        app:layout_constraintTop_toTopOf="@id/name"
        tools:src="@drawable/meeting_ic_host" />

    <View
        android:layout_width="0dp"
        android:layout_height="1px"
        android:background="?android:listDivider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/name"
        android:visibility="gone"/>
</androidx.constraintlayout.widget.ConstraintLayout>