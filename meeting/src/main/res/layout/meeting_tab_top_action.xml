<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:background="@drawable/jdme_ripple_white"
    android:paddingVertical="10dp">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginHorizontal="20dp"
        tools:src="@drawable/meeting_note"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:textColor="#1B1B1B"
        android:textSize="12sp"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="note"/>
</LinearLayout>