<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:gravity="center_vertical"
        android:paddingVertical="8dp">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:scaleType="fitCenter"
            tools:src="@drawable/file_files"/>

        <TextView
            android:id="@+id/tv_attach_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_333333"
            android:textSize="14sp"
            tools:text="日历2.0.0优化需求（聚焦今天、聚焦聚焦聚焦聚焦日历2.0.0优化需求（聚焦今天、聚焦聚焦聚焦聚焦" />
    </LinearLayout>
</FrameLayout>
