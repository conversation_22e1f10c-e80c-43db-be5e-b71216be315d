<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="16dp"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingBottom="2dp"
                android:textColor="#1B1B1B"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="线上线下一体化设计评审会线上线下一体化设计评审会" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_general_time"
                android:textColor="@color/meeting_detail_icon_color"
                android:textSize="@dimen/JMEIcon_16"/>

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="@color/meeting_detail_text_color"
                android:textSize="14dp"
                tools:text="今天 18:00-19:00" />

            <View
                android:layout_width="1dp"
                android:layout_height="14dp"
                android:layout_marginStart="8dp"
                android:background="#CECECE" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/lottie"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/meeting_ic_ongoing"
                    android:visibility="gone"
                    tools:visibility="visible"/>

                <TextView
                    android:id="@+id/tv_status_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:textColor="@color/meeting_detail_text_color"
                    android:textSize="14dp"
                    tools:text="进行中 120:05" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_meeting_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="1dp"
                android:text="ID"
                android:textColor="@color/meeting_detail_icon_color"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_meeting_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="@color/meeting_detail_text_color"
                tools:text="198222" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_meeting_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/meeting_detail_icon_color"
                android:text="@string/icon_general_key"
                android:textSize="@dimen/JMEIcon_16" />

            <TextView
                android:id="@+id/tv_meeting_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="@color/meeting_detail_text_color"
                tools:text="123456" />
        </LinearLayout>
        
        <LinearLayout
            android:id="@+id/ll_members"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:gravity="center_vertical">
            <com.jd.oa.ui.IconFontView
                android:id="@+id/member_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_general_personnellist"
                android:textColor="@color/meeting_detail_icon_color"
                android:textSize="@dimen/JMEIcon_16"/>
            <TextView
                android:id="@+id/tv_meeting_members"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="@color/meeting_detail_text_color"
                tools:text="122人参会" />
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_direction_right"
                android:layout_marginStart="2dp"
                android:textColor="@color/meeting_detail_text_color"
                android:textSize="@dimen/JMEIcon_12"/>
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_members"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginStart="26dp"
            tools:itemCount="2" />

        <RelativeLayout
            android:id="@+id/ll_meeting_rooms"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="18dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/member_icon1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/icon_general_meetingroom"
                android:textColor="@color/meeting_detail_icon_color"
                android:textSize="@dimen/JMEIcon_16"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_meeting_rooms"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="16dp"
                android:layout_toEndOf="@id/member_icon1"
                tools:itemCount="2" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_calendar_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_long"
                android:textColor="#4C7CFF"
                android:textSize="@dimen/JMEIcon_16"/>

            <TextView
                android:id="@+id/tv_calendar_detail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="@string/meeting_view_detail_in_calendar"
                android:textColor="#4C7CFF"
                android:textSize="14dp" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:background="#F5F5F5"
        android:layout_width="match_parent"
        android:layout_height="8dp" />
</LinearLayout>