<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="16dp"
        android:paddingTop="12dp"
        android:clickable="true"
        android:background="@color/white">
        <View
            android:layout_width="78dp"
            android:layout_height="17dp"
            android:background="@drawable/meeting_skeleton_bg"/>
        <include
            layout="@layout/meeting_list_skeleton_item"/>
        <include
            layout="@layout/meeting_list_skeleton_item"/>
        <include
            layout="@layout/meeting_list_skeleton_item"/>

        <View
            android:layout_width="78dp"
            android:layout_height="17dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/meeting_skeleton_bg"/>
        <include
            layout="@layout/meeting_list_skeleton_item"/>
        <include
            layout="@layout/meeting_list_skeleton_item"/>
        <include
            layout="@layout/meeting_list_skeleton_item"/>
    </LinearLayout>
    <com.jd.oa.ui.LoadingView
        android:id="@+id/refreshing"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</FrameLayout>