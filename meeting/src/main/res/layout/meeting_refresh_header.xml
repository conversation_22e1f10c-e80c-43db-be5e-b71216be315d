<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="18dp"
    android:gravity="center">

    <ImageView
        android:id="@+id/iv_refresh"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:src="@drawable/meeting_ic_loading"/>
    <TextView
        android:id="@+id/tv_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:textColor="#999999"
        android:text="@string/meeting_refresh_pull"/>
</LinearLayout>