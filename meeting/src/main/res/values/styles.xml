<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="MeetingJoinButton" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minWidth">20dp</item>
        <item name="android:minHeight">20dp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingBottom">6dp</item>
        <item name="android:textColor">@color/meeting_btn_color</item>
        <item name="android:textSize">14sp</item>
        <item name="android:background">@drawable/meeting_item_btn_join</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textAppearance">@null</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="ShapeAppearance.Conference.Circle" parent="" tools:ignore="ResourceName">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="NoActionBar" parent="Theme.AppCompat.Light.NoActionBar" tools:ignore="ResourceName">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <style name="MeetingFeedbackActionText">
        <item name="android:textColor">#1869F5</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingExtra">6sp</item>
    </style>
</resources>