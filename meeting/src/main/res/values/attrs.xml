<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <declare-styleable name="TimeLayout_Layout" tools:ignore="ResourceName">
        <attr name="childType" format="enum">
            <enum name="view" value="1"/>
            <enum name="divider" value="2"/>
        </attr>
    </declare-styleable>
    <attr name="state_error" format="boolean" tools:ignore="ResourceName" />
    <attr name="composeEditTextStyle" format="reference" tools:ignore="ResourceName" />
    <declare-styleable name="ComposeEditText" tools:ignore="ResourceName">
        <attr name="accessoryLayout" format="reference" />
        <attr name="zoom" format="boolean" />
        <attr name="clearEnable" format="boolean" />
        <attr name="android:enabled" />
        <!--来自android TextView-->
        <attr name="android:digits" />
        <attr name="android:background" />
        <attr name="android:text" />
        <attr name="android:hint" />
        <attr name="android:textColor" />
        <attr name="android:textSize" />
        <attr name="android:singleLine" />
        <attr name="android:ellipsize" />
        <attr name="android:drawableTop" />
        <attr name="android:drawableBottom" />
        <attr name="android:drawableLeft" />
        <attr name="android:drawableRight" />
        <attr name="android:drawablePadding" />
        <attr name="android:imeOptions" />
        <attr name="android:inputType" />
        <attr name="android:maxLength" />
        <attr name="android:tag" />
    </declare-styleable>
</resources>