plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.chenenyu.router'
    id 'kotlin-parcelize'
}

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                arguments = ["moduleName": project.name]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    resourcePrefix 'meeting'
    namespace 'com.jd.oa.meeting'
}

dependencies {
    implementation 'androidx.core:core-ktx:1.6.0'
    implementation COMPILE_SUPPORT.appcompat
    implementation COMPILE_SUPPORT.design
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation project(":common")
    implementation project(":libjdmeeting")
//    implementation 'com.chenenyu.router:router:1.5.2'
//    kapt 'com.chenenyu.router:compiler:1.5.1'
    implementation 'com.github.peidongbiao:PullUpLoadHelper:1.4'
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.3.1"
    // LiveData
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.3.1"
    // Lifecycles only (without ViewModel or LiveData)
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.3.1"
    implementation "androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1"
    implementation  'io.github.scwang90:refresh-layout-kernel:2.0.6'      //核心必须依赖
    implementation 'com.github.Gavin-ZYX:StickyDecoration:1.6.1'
    implementation 'com.airbnb.android:lottie:5.2.0'
    implementation 'com.google.android:flexbox:2.0.1'
    def version = rootProject.ext.saasMeeting//京we 编译时依赖 implementation 在app中
    compileOnly("com.jingdong.conference:mesdk:$version")
}