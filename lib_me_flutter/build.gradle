apply plugin: 'com.android.library'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION


    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        multiDexEnabled true
        buildConfigField "boolean", "FLUTTER_LOCAL_DEBUG", "${flutterLocalDebug.toLowerCase()}"

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    packagingOptions {
        resources {
            excludes += ['META-INF/LICENSE', 'META-INF/proguard/androidx-annotations.pro']
        }
    }

    /* 新增 65536 限制配置 */

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.jd.flutter'
    lint {
        abortOnError false
    }

}

dependencies {
//    api fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    api 'com.jd.flutter:jdf_container_plugin_impl:2.0.7-RELEASE'
    api 'com.jd.flutter:jdf_router_plugin_impl:3.0.0-RELEASE'

    api project(':aar_flutter_boost_release')
    api project(':aar_jdf_channel_release')
    api project(':aar_connectivity_release')
    api project(':aar_jdf_container_plugin_release')
    api project(':aar_jdf_jdme_file_transfer_plugin_release')
    api project(':aar_jdf_jdme_network_plugin_release')
    api project(':aar_jdf_router_plugin_release')
    api project(':aar_path_provider_release')
    api project(':aar_shared_preferences_release')
    api project(':aar_sqflite_release')
    api project(':aar_back_press_release')

    //    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'
    implementation project(":common")
//    implementation "com.github.liyuzero:MaeAlbum:$maeAlbumVersion"
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.3.41@jar'
    if (flutterLocalDebug.toBoolean()) {
        api project(':aar_flutter_debug')
        api "io.flutter:flutter_embedding_debug:1.0.0-$FLUTTE_ENGINE_ID"
        implementation "io.flutter:armeabi_v7a_debug:1.0.0-$FLUTTE_ENGINE_ID"
        implementation "io.flutter:arm64_v8a_debug:1.0.0-$FLUTTE_ENGINE_ID"
    } else {
        api project(':aar_flutter_release')
        api "io.flutter:flutter_embedding_release:1.0.0-$FLUTTE_ENGINE_ID"
        implementation "io.flutter:armeabi_v7a_release:1.0.0-$FLUTTE_ENGINE_ID"
        implementation "io.flutter:arm64_v8a_release:1.0.0-$FLUTTE_ENGINE_ID"
    }

    implementation libs.librecurparser
    implementation libs.lib.me.ui
}

// 刷新 lib 缓存
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    //多个support库版本冲突时，使用默认值
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion SUPPORT_VERSION//默认使用的版本
            }
        }
    }
}
