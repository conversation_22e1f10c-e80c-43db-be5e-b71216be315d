package com.jd.flutter.common.handler;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.utils.JDMAUtils;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

public class MEFlutterJDMTAPlugin implements IJDFChannelHandler {

    public static final String BUSINESS_JDMTA = "com.jdme.flutter/business/jdmta";

    @Override
    public String getModuleName() {
        return BUSINESS_JDMTA;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        if ("clickEvent".equals(methodName)) {
            clickEvent(map, resultMap);
        } else if ("eventPV".equals(methodName)) {
            pageEvent(map, resultMap);
        } else {
            resultMap.notImplemented();
        }
    }

    private void clickEvent(@NonNull Map<String, Object> map, @SuppressWarnings("rawtypes") final IJDFMessageResult<Map> resultMap) {
        String eventId = "";
        Map<String, String> params = null;
        try {
            eventId = (String) map.get("eventId");
            //noinspection unchecked
            params = (Map<String, String>) map.get("params");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (params == null) {
            params = new HashMap<>();
        }
        String name = MultiAppConstant.getUserId();
        params.put("erp", name);
        params.put("name", eventId);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId, name,
                "", "", "", "", new HashMap<>(params));
        resultMap.success(new HashMap<>());
    }

    private void pageEvent(@NonNull Map<String, Object> map, @SuppressWarnings("rawtypes") final IJDFMessageResult<Map> resultMap) {
        String pageName = "";
        Map<String, String> params = null;
        try {
            pageName = (String) map.get("pageName");
            //noinspection unchecked
            params = (Map<String, String>) map.get("pageParam");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (params == null) {
            params = new HashMap<>();
        }
        String name = MultiAppConstant.getUserId();
        params.put("erp", name);
        params.put("name", pageName);
        JDMAUtils.onEventPagePV(AppBase.getAppContext(), pageName, "", name, pageName, new HashMap<>(params));
        resultMap.success(new HashMap<>());
    }
}
