package com.jd.flutter.common.handler;

import androidx.annotation.NonNull;

import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class TabCommonHandler implements MethodChannel.MethodCallHandler {
    public static final String COM_JDME_FLUTTER_TAB_COMMON = "com.jdme.flutter/common/methodChannel";
    private static final Map<String, IJDFChannelHandler> jdFlutterHandler = new HashMap<>();

    public TabCommonHandler() {
        jdFlutterHandler.put(MeFlutterFilePlugin.SERVICE_FILE, new MeFlutterFilePlugin());
        jdFlutterHandler.put(MeFlutterShareHandler.SERVICE_SHARE, new MeFlutterShareHandler());
        jdFlutterHandler.put(MeFlutterBrowserHandler.SERVICE_BROWSER, new MeFlutterBrowserHandler());
        jdFlutterHandler.put(MeFlutterAppHandler.SERVICE_APPLICATION, new MeFlutterAppHandler());
        jdFlutterHandler.put(MeFlutterDeepLinkHandler.SERVICE_DEEP_LINK, new MeFlutterDeepLinkHandler());
        jdFlutterHandler.put(MeFlutterUploadHandler.SERVICE_UPLOAD, new MeFlutterUploadHandler());
        jdFlutterHandler.put(MeFlutterDatePickerPlugin.SERVICE_DATETIME, new MeFlutterDatePickerPlugin());
        jdFlutterHandler.put(MEFlutterJDMTAPlugin.BUSINESS_JDMTA, new MEFlutterJDMTAPlugin());
        jdFlutterHandler.put(MeFlutterCalendarHandler.BUSINESS_CALENDAR, new MeFlutterCalendarHandler());
        jdFlutterHandler.put(MeFlutterCommonHandler.SERVICE_COMMON, new MeFlutterCommonHandler());
        jdFlutterHandler.put(MeFlutterEventHandler.BUSINESS_EVENT, new MeFlutterEventHandler());
        jdFlutterHandler.put(MeFlutterStoragePlugin.SERVICE_FILE, new MeFlutterStoragePlugin());
        jdFlutterHandler.put(MeFlutterThemePlugin.SERVICE_THEME, new MeFlutterThemePlugin());
        jdFlutterHandler.put(MEFlutterRRulePlugin.BUSINESS_R_RULE, new MEFlutterRRulePlugin());
        jdFlutterHandler.put(MeFlutterLocationHandler.SERVICE_LOCATION, new MeFlutterLocationHandler());
        jdFlutterHandler.put(MeFlutterLoggingHandler.SERVICE_LOGGING, new MeFlutterLoggingHandler());
        jdFlutterHandler.put(MeFlutterStatusBarHandler.BUSINESS_STATUS_BAR, new MeFlutterStatusBarHandler());
        jdFlutterHandler.put(MeFlutterTimeZoneHandler.SERVICE_TIME_ZONE, new MeFlutterTimeZoneHandler());
        jdFlutterHandler.put(MEFlutterJdMeetingHandler.NAME, new MEFlutterJdMeetingHandler());
        jdFlutterHandler.put(MeFlutterImHandler.BUSINESS_IM, new MeFlutterImHandler());
        jdFlutterHandler.put(MeFlutterJoyworkHandler.BUSINESS_JOYWORK, new MeFlutterJoyworkHandler());
    }

    public static void addHandler(String key, IJDFChannelHandler handler) {
        jdFlutterHandler.put(key, handler);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall methodCall, @NonNull final MethodChannel.Result result) {
        String method = methodCall.method;
        Map<String, Object> args = methodCall.arguments();
        if ("MessageToNative_Comm".equals(method)) {
            String moduleName = (String) args.get("moduleName");
            String methodName = (String) args.get("methodName");
            IJDFChannelHandler ijdfChannelHandler = jdFlutterHandler.get(moduleName);
            if (ijdfChannelHandler != null) {
                //noinspection rawtypes
                ijdfChannelHandler.onChannel(moduleName, methodName, args, new IJDFMessageResult<Map>() {
                    @Override
                    public void success(Map map) {
                        result.success(map);
                    }

                    @Override
                    public void error(String s, String s1, Object o) {
                        result.error(s, s1, o);
                    }

                    @Override
                    public void notImplemented() {
                        result.notImplemented();
                    }
                });
            }
        } else {
            result.notImplemented();
        }
    }
}
