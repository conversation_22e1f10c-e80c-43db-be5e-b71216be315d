package com.jd.flutter.common.model;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

import static com.jd.flutter.common.model.ToJson.CANCELED;

public class FlutterStringResult {
    public String type = CANCELED;
    public String params;

    @NonNull
    public Map<String, Object> toMap() {
        Map<String, Object> result = new HashMap<>();
        result.put("type", type);
        result.put("params", params);
        return result;
    }
}


