package com.jd.flutter.common.handler;

import static com.jd.flutter.common.model.ToJson.SUCCEED;

import com.jd.ee.librecurparser.BaseAppointment;
import com.jd.ee.librecurparser.RecurParser;
import com.jd.ee.librecurparser.RecurRule;
import com.jd.flutter.common.model.FlutterListResult;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.List;
import java.util.Map;

public class MEFlutterRRulePlugin implements IJDFChannelHandler {
    public static final String BUSINESS_R_RULE = "com.jdfocus.flutter/business/rrule";

    @Override
    public String getModuleName() {
        return BUSINESS_R_RULE;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        if ("getRecurrences".equals(methodName)) {
            getRecurrences(map, resultMap);
        } else if ("getRecurrencesByCount".equals(methodName)) {
            getRecurrencesByCount(map, resultMap);
        }
    }

    private void getRecurrences(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String rrule = null;
        Long repeatStart = null;
        Long limitStart = null;
        Long limitEnd = null;
        Long start = null;
        String timeZoneId = null;
        Long repeatEnd = null;
        try {
            String subject = (String) map.get("title");
            rrule = (String) map.get("rrule");
            repeatStart = (Long) map.get("repeatStart");
            start = (Long) map.get("start");
            timeZoneId = (String) map.get("timeZone");
            repeatEnd = (Long) map.get("repeatEnd");
            limitStart = (Long) map.get("limitStart");
            limitEnd = (Long) map.get("limitEnd");

            RecurParser<BaseAppointment> parser = new RecurParser<>();
            RecurRule rule = new RecurRule(rrule, repeatStart, start);
            rule.setLimitStart(limitStart);
            rule.setLimitEnd(limitEnd);
            rule.setTimeZoneId(timeZoneId);
            rule.setRepeatEnd(repeatEnd);

            List<Long> list = parser.parseRecurrenceRule(rule);

            FlutterListResult<Long> flutterListResult = new FlutterListResult<>();
            flutterListResult.type = SUCCEED;
            flutterListResult.params = list;
            resultMap.success(flutterListResult.toMap());
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.error("Exception", e.getMessage(),  e);
            if (AppBase.SHOW_SERVER_SWITCHER || AppBase.DEBUG) {
                MELogUtil.localE("MEFlutterRRulePlugin getRecurrences", "rrule: " + rrule + ", repeatStart: " + repeatStart + ", start: " + start + ", until: " + limitEnd, e);
                MELogUtil.onlineE("MEFlutterRRulePlugin getRecurrences", "rrule: " + rrule + ", repeatStart: " + repeatStart + ", start: " + start + ", until: " + limitEnd, e);
            }
        }
    }

    private void getRecurrencesByCount(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String rrule = null;
        Long repeatStart = null;
        Integer count = null;
        Long start = null;
        String timeZoneId = null;
        try {
            rrule = (String) map.get("rrule");
            repeatStart = (Long) map.get("repeatStart");
            count = (Integer) map.get("count");
            start = (Long) map.get("start");
            timeZoneId = (String) map.get("timeZone");

            RecurParser<BaseAppointment> parser = new RecurParser<>();
            RecurRule rule = new RecurRule(rrule, repeatStart, start);
            rule.setTimeZoneId(timeZoneId);

            List<Long> list = parser.parseRecurrencesByCount(rule, count);

            FlutterListResult<Long> flutterListResult = new FlutterListResult<>();
            flutterListResult.type = SUCCEED;
            flutterListResult.params = list;
            resultMap.success(flutterListResult.toMap());
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.error("Exception", e.getMessage(),  e);
            if (AppBase.SHOW_SERVER_SWITCHER || AppBase.DEBUG) {
                MELogUtil.localE("MEFlutterRRulePlugin getRecurrencesByCount", "rrule: " + rrule + ", repeatStart: " + repeatStart + ", count: " + count, e);
                MELogUtil.onlineE("MEFlutterRRulePlugin getRecurrencesByCount", "rrule: " + rrule + ", repeatStart: " + repeatStart + ", count: " + count, e);
            }
        }
    }
}