package com.jd.flutter.common.handler;

import static com.jd.flutter.common.JDFHelper.callFlutter;

import android.text.TextUtils;

import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

public class MeFlutterThemePlugin implements IJDFChannelHandler {
    public static final String SERVICE_THEME = "com.jdme.flutter/service/theme";
    private static final String CHANGE_THEME = "themeChanged";

    @Override
    public String getModuleName() {
        return SERVICE_THEME;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        try {
            Map<String, Object> result = new HashMap<>();
            //noinspection SwitchStatementWithTooFewBranches
            switch (methodName) {
                case "getThemeData":
                    ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
                    if (themeData == null || themeData.getDir() == null) {
                        resultMap.error("Failed", "", "");
                        break;
                    }
                    setThemeData(result, themeData);
                    resultMap.success(result);
                    break;
                default:
                    resultMap.notImplemented();
                    break;
            }
        } catch (Exception e) {
            resultMap.error("Failed", e.getMessage(), "");
        }
    }

    private static void setThemeData(Map<String, Object> result, ThemeData themeData) {
        if (themeData == null) {
            return;
        }
        if (!TextUtils.isEmpty(themeData.imageId)) {
            result.put("imageId", themeData.imageId);
        }
        if (!TextUtils.isEmpty(themeData.imageClassify)) {
            result.put("imageClassify", themeData.imageClassify);
        }
        if (!TextUtils.isEmpty(themeData.imageType)) {
            result.put("imageType", themeData.imageType);
        }
        if (!TextUtils.isEmpty(themeData.resourceMd5)) {
            result.put("resourceMd5", themeData.resourceMd5);
        }
        if (!TextUtils.isEmpty(themeData.getDir().getPath())) {
            result.put("imageDir", themeData.getDir().getPath());
        }
        if (!TextUtils.isEmpty(themeData.getJson().toString())) {
            result.put("configJson", themeData.getJson().toString());
        }
    }

    public static void changTheme() {
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        Map<String, Object> params = new HashMap<>();
        setThemeData(params, themeData);
        callFlutter(SERVICE_THEME, CHANGE_THEME, params, null);
    }

}