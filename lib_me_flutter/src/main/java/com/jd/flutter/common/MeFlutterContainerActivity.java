package com.jd.flutter.common;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.idlefish.flutterboost.containers.FlutterBoostActivity;
import com.jd.me.backpress.BackPressPlugin;
import com.jd.oa.utils.ActiveAnalyzeUtil;

import io.flutter.embedding.android.FlutterSurfaceView;
import io.flutter.embedding.android.RenderMode;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;


public class MeFlutterContainerActivity extends FlutterBoostActivity {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @NonNull
    @Override
    public RenderMode getRenderMode() {
        return RenderMode.surface;
    }

    @Override
    public void onFlutterSurfaceViewCreated(@NonNull FlutterSurfaceView flutterSurfaceView) {
        super.onFlutterSurfaceViewCreated(flutterSurfaceView);
        flutterSurfaceView.setZOrderMediaOverlay(true);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onPostResume() {
        super.onPostResume();
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        int option = View.SYSTEM_UI_FLAG_LAYOUT_STABLE | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;
        int vis = getWindow().getDecorView().getSystemUiVisibility();
        getWindow().getDecorView().setSystemUiVisibility(option | vis);
        getWindow().setStatusBarColor(Color.TRANSPARENT);
        getWindow().setNavigationBarColor(Color.WHITE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        JDFHelper.getInstance().onActivityResult(this, requestCode, resultCode, data);
    }

    @Override
    public void onUserInteraction() {
        super.onUserInteraction();
        ActiveAnalyzeUtil.getInstance().onUserInteraction();
    }

    @Override
    public void onBackPressed() {
        FlutterEngine engine = getFlutterEngine();
        if (engine == null) {
            super.onBackPressed();
            return;
        }
        FlutterPlugin plugin = engine.getPlugins().get(BackPressPlugin.class);
        if (plugin == null) {
            super.onBackPressed();
            return;
        }
        BackPressPlugin backPressPlugin = (BackPressPlugin) plugin;
        backPressPlugin.onBackPressed(new Function1<Boolean, Unit>() {
            @Override
            public Unit invoke(Boolean intercept) {
                if (!intercept) {
                    MeFlutterContainerActivity.super.onBackPressed();
                }
                return Unit.INSTANCE;
            }
        });
    }
}
