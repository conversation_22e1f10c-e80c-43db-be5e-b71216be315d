package com.jd.flutter.common.handler;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.fragment.web.WebConfig;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

import static com.jd.oa.utils.WebViewUtils.openWeb;

@SuppressWarnings({"SwitchStatementWithTooFewBranches"})
public class MeFlutterBrowserHandler implements IJDFChannelHandler {

    public static final String SERVICE_BROWSER = "com.jdme.flutter/service/browser";

    @Override
    public String getModuleName() {
        return SERVICE_BROWSER;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        switch (methodName) {
            case "openUrl":
                String url = null;
                String showNavString = null;
                try {
                    url = (String) map.get("url");
                    showNavString = (String) map.get("showNav");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                boolean getDeeplink = false;
                if (map.containsKey("getDeeplink")) {
                    getDeeplink = (boolean) map.get("getDeeplink");
                }
                if (getDeeplink) {
                    OpennessApi.openUrl(url, WebConfig.H5_NATIVE_HEAD_HIDE.equals(showNavString));
                } else {
                    openWeb(AppBase.getTopActivity(), url, showNavString);
                }
                resultMap.success(new HashMap<>());
                break;
            default:
                resultMap.notImplemented();
                break;
        }
    }
}
