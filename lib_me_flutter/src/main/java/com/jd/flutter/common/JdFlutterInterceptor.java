package com.jd.flutter.common;

import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class JdFlutterInterceptor implements RouteInterceptor {
    private static final String TAG = "JdFlutterInterceptor";
    private static final String HOST = "jm";
    private static final String PATH1 = "/sys/flutter";
//    jdme://jm/sys/flutter?mparam={"routeName":"","mparam":{}}

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            boolean dispose = parseJdFlutterUri(chain.getRequest().getUri());
            if (dispose) {
                return chain.intercept();
            }
            return chain.process();
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }

    private boolean parseJdFlutterUri(@NonNull Uri uri) {
        try {
            if (!HOST.equals(uri.getHost()) || !PATH1.startsWith(uri.getPath())) {
                return false;
            }
            final String params = uri.getQueryParameter("mparam");
            final JSONObject json = new JSONObject(params);
            final String routeName = json.optString("routeName");
            final JSONObject param2 = json.optJSONObject("mparam");
            final String bizParamStr = uri.getQueryParameter("bizparam");
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> bizParamMap = new HashMap<>();
            try {
                if (param2 != null) {
                    GsonBuilder gsonBuilder = new GsonBuilder();
                    gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
                    }.getType(), new MapDeserializerDoubleAsIntFix());
                    Gson gson = gsonBuilder.create();
                    map = gson.fromJson(param2.toString(), new TypeToken<Map<String, Object>>() {
                    }.getType());
                }
                if (bizParamStr != null) {
                    GsonBuilder gsonBuilder = new GsonBuilder();
                    gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
                    }.getType(), new MapDeserializerDoubleAsIntFix());
                    Gson gson = gsonBuilder.create();
                    bizParamMap = gson.fromJson(bizParamStr, new TypeToken<Map<String, Object>>() {
                    }.getType());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (routeName.length() == 0) {
                return false;
            }
            Map<String, Object> mapParam = new HashMap<>();
            mapParam.put("mparam", map);
            mapParam.put("bizparam", bizParamMap);
            String result = JDFHelper.openFlutterPage(AppBase.getTopActivity(), routeName, mapParam, 0);
            return result != null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
