package com.jd.flutter.common.handler;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.flutter.common.model.FlutterStorageEntites;
import com.jd.oa.storage.KVMethod;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.KvEntity;
import com.jd.oa.utils.JsonUtils;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MeFlutterStoragePlugin implements IJDFChannelHandler {
    public static final String SERVICE_FILE = "com.jdme.flutter/service/storage";

    @Override
    public String getModuleName() {
        return SERVICE_FILE;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        //noinspection SwitchStatementWithTooFewBranches
        if (map == null) {
            resultMap.error("fail", "map is null", null);
            return;
        }
        String fileName = (String) map.get("fileName");
        if (TextUtils.isEmpty(fileName)) {
            resultMap.error("fail", "fileName is null", null);
            return;
        }
        String key = (String) map.get("key");
        if (TextUtils.isEmpty(key) && !"allKeys".equals(methodName)) {
            resultMap.error("fail", "key is null", null);
            return;
        }
        int useType = (int) map.get("useType");
        UseType uType;
        switch (useType) {
            case 0:
                uType = UseType.APP;
                break;
            case 1:
                uType = UseType.USER;
                break;
            default:
                uType = UseType.TENANT;
                break;
        }
        int mode = (int) map.get("mode");
        KVMethod kvMethod = mode == 0 ? KVMethod.SP : KVMethod.MMKV;
        FlutterStorageEntites flutterEntites = new FlutterStorageEntites(fileName, uType, kvMethod);
        try {
            Map<String, Object> result = new HashMap<>();
            switch (methodName) {
                case "setBool":
                    boolean value = (boolean) map.get("value");
                    KvEntity<Boolean> kvEntity0 = new KvEntity(key, false);
                    flutterEntites.put(kvEntity0, value);
                    resultMap.success(result);
                    break;
                case "getBool":
                    KvEntity<Boolean> kvEntity1 = new KvEntity(key, false);
                    result.put("value", flutterEntites.get(kvEntity1));
                    resultMap.success(result);
                    break;
                case "setString":
                    String strVal = (String) map.get("value");
                    KvEntity<String> kvEntity2 = new KvEntity(key, "");
                    flutterEntites.put(kvEntity2, strVal);
                    resultMap.success(result);
                    break;
                case "getString":
                    KvEntity<String> kvEntity3 = new KvEntity(key, "");
                    result.put("value", flutterEntites.get(kvEntity3));
                    resultMap.success(result);
                    break;
                case "setInt":
                    int intVal = (int) map.get("value");
                    KvEntity<Integer> kvEntity4 = new KvEntity(key, 0);
                    flutterEntites.put(kvEntity4, intVal);
                    resultMap.success(result);
                    break;
                case "getInt":
                    KvEntity<Integer> kvEntity5 = new KvEntity(key, 0);
                    result.put("value", flutterEntites.get(kvEntity5));
                    resultMap.success(result);
                    break;
                case "setStringList":
                    List<String> listVal = (List<String>) map.get("value");
                    String val = JsonUtils.getGson().toJson(listVal);
                    KvEntity<String> kvEntity6 = new KvEntity(key, "[]");
                    flutterEntites.put(kvEntity6, val);
                    resultMap.success(result);
                    break;
                case "getStringList":
                    KvEntity<String> kvEntity7 = new KvEntity(key, "[]");
                    String str = flutterEntites.get(kvEntity7);
                    List<String> vals = JsonUtils.getGson().fromJson(str, new TypeToken<List<String>>() {
                    }.getType());
                    result.put("value", vals);
                    resultMap.success(result);
                    break;
                case "remove":
                    KvEntity<Object> kvEntity8 = new KvEntity(key, new Object());
                    flutterEntites.remove(kvEntity8);
                    break;
                case "allKeys":
                    List<String> allKey = flutterEntites.getAllKeys(uType);
                    result.put("value", allKey);
                    resultMap.success(result);
                    break;
                default:
                    resultMap.notImplemented();
                    break;
            }
        } catch (Exception e) {
            resultMap.error("fail", "exception", null);
        }
    }
}