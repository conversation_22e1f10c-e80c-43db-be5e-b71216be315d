package com.jd.flutter.common.handler;

import static com.jd.flutter.common.model.ToJson.SUCCEED;

import android.app.Activity;

import com.jd.flutter.common.model.FlutterListResult;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.Map;

public class MEFlutterJdMeetingHandler implements IJDFChannelHandler {
    public static final String NAME = "com.jdfocus.flutter/business/jdMeeting";

    @Override
    public String getModuleName() {
        return NAME;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        if ("joinMeeting".equals(methodName)) {
            joinMeeting(map, resultMap);
        }
    }

    private void joinMeeting(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String meetingId = null;
        String meetingCode = null;
        String password = null;
        String source = null;
        Long until = null;
        try {
            meetingId = (String) map.get("meetingId");
            meetingCode = (String) map.get("meetingCode");
            password = (String) map.get("password");
            source = (String) map.get("source");

            Activity activity = AppBase.getTopActivity();
            if (activity != null) {
                JdMeetingService service = AppJoint.service(JdMeetingService.class);
                service.joinMeeting(activity, meetingId, Long.parseLong(meetingCode), password, source, null);

                FlutterListResult<Long> flutterListResult = new FlutterListResult<>();
                flutterListResult.type = SUCCEED;
                resultMap.success(flutterListResult.toMap());
            } else {
                resultMap.error("Exception", "Top activity is null",  null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.error("Exception", e.getMessage(),  e);
        }
    }
}