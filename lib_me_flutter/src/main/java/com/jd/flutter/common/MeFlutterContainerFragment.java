package com.jd.flutter.common;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import io.flutter.embedding.android.FlutterFragment;

public class MeFlutterContainerFragment extends FlutterFragment {
    @Override
    public void detachFromFlutterEngine() {
        try {
            super.detachFromFlutterEngine();
        } catch (Exception e) {
//            System.out.println("detachFromFlutterEngine="+e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }
}