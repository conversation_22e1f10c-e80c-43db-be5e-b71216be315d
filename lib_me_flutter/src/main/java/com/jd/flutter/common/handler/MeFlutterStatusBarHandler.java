package com.jd.flutter.common.handler;

import android.app.Activity;

import com.jd.oa.AppBase;
import com.jd.oa.theme.manager.ThemeApi;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.Map;

public class MeFlutterStatusBarHandler implements IJDFChannelHandler {

    public static final String BUSINESS_STATUS_BAR = "com.jdme.flutter/service/statusbar";
    public static final String STYLE_LIGHT = "light";
    public static final String STYLE_DARK = "dark";

    @Override
    public String getModuleName() {
        return BUSINESS_STATUS_BAR;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        if ("setStatusBarStyle".equals(methodName)) {
            setStatusBarStyle(map, resultMap);
        }
    }

    private void setStatusBarStyle(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) return;
        String style = (String) map.get("style");
        if (STYLE_DARK.equals(style)) {
            ThemeApi.checkAndSetDarkTheme(activity);
        } else if (STYLE_LIGHT.equals(style)) {
            QMUIStatusBarHelper.setStatusBarLightMode(activity);
        }
    }
}
