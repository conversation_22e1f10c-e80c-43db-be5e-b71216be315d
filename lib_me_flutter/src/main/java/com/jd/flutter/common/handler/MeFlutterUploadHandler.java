package com.jd.flutter.common.handler;

import static com.jd.flutter.common.JDFHelper.UPLOAD_FILE_CALLBACK;

import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.AppBase;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@SuppressWarnings({"SwitchStatementWithTooFewBranches"})
public class MeFlutterUploadHandler implements IJDFChannelHandler {

    public static final String SERVICE_UPLOAD = "com.jdme.flutter/service/upload";

    @Override
    public String getModuleName() {
        return SERVICE_UPLOAD;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {

        switch (methodName) {
            case "uploadCallback":
                Intent intent = new Intent(UPLOAD_FILE_CALLBACK);
                intent.putExtra("arguments", (Serializable) map);
                LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcastSync(intent);
                resultMap.success(new HashMap<>());
            default:
                resultMap.notImplemented();
                break;
        }
    }
}
