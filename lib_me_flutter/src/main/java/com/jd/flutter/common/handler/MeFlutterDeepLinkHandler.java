package com.jd.flutter.common.handler;

import static com.jd.oa.router.DeepLink.DD_INFO;

import android.net.Uri;
import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.jd.flutter.common.JDFHelper;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.Utils2App;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

public class MeFlutterDeepLinkHandler implements IJDFChannelHandler {

    public static final String SERVICE_DEEP_LINK = "com.jdme.flutter/service/deeplink";

    @Override
    public String getModuleName() {
        return SERVICE_DEEP_LINK;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        switch (methodName) {
            case "openDeeplink":
                String deepLink = null;
                try {
                    deepLink = (String) map.get("deepLink");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (deepLink != null) {
                    boolean isNeedToken = false;
                    Uri uri = Uri.parse(deepLink);
                    if (uri.getHost() != null) {
                        isNeedToken = uri.getHost().equals("auth");
                    }
                    String appId = uri.getQueryParameter("appId");
                    if (isNeedToken && !TextUtils.isEmpty(appId)) {
                        AppUtils.gainTokenAndGoPlugin(deepLink, appId);
                    } else if (deepLink.trim().startsWith(DD_INFO)) {
                        ImDdService imDdService = AppJoint.service(ImDdService.class);
                        boolean bFlag = imDdService.sendQrCodeResult(Utils2App.getApp(), deepLink);
                        if(!bFlag) {
                            Router.build(deepLink).go(Utils2App.getApp(), new RouteNotFoundCallback(Utils2App.getApp()));
                        }
                    } else {
                        Router.build(deepLink).go(Utils2App.getApp());
                    }
                }
                resultMap.success(new HashMap<>());
                break;
            case "openRoute":
                try {
                    String routeName = (String) map.get("routeName");
                    if (routeName != null) {
                        JDFHelper.openFlutterPage(AppBase.getMainActivity(), routeName, map, 0);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                resultMap.success(new HashMap<>());
                break;
            default:
                resultMap.notImplemented();
                break;
        }
    }
}
