package com.jd.flutter.common.handler;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.provider.MediaStore;

import com.jd.flutter.R;
import com.jd.flutter.common.model.FlutterListResult;
import com.jd.flutter.common.model.FlutterStringResult;
import com.jd.oa.AppBase;
import com.jd.oa.business.netdisk.GlideEngine;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.cache.FileCache;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.CompressType;
import com.jd.oa.utils.ImageCompressUtils;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;
import com.yu.bundles.album.MaeAlbum;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

import static com.jd.flutter.common.model.ToJson.SUCCEED;

public class MeFlutterGalleryHandler implements IJDFChannelHandler {
    public static final String BUSINESS_MEDIA = "com.jdme.flutter/business/media";

    @Override
    public String getModuleName() {
        return BUSINESS_MEDIA;
    }

    private static final int DEFAULT_MAX_NUM = 9;
    private static final int REQUEST_CODE_GALLERY = 1;
    private static final int REQUEST_CODE_CAPTURE = 2;
    private static final int REQUEST_CODE_PREVIEW_IMAGE = 3;
//    private static final String FILE_SCHEME = "file://";

    //    private static final int PERMISSION_CAMERA = 1;
    //    private Activity activity;
    //    private Fragment fragment;
    private ImageCompressUtils mGallery;
    @SuppressWarnings("rawtypes")
    private IJDFMessageResult<Map> resultMap;
    private final GalleryProvider mGalleryProvider;
    private String mCapturePath;

    private List<Integer> mPreviewPosition;

    private CustomEngine mCustomEngine;

    public MeFlutterGalleryHandler() {
//        this.fragment = fragment;
//        reactContext.addActivityEventListener(this);
        mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
        mPreviewPosition = new ArrayList<>();
        mCustomEngine = new CustomEngine(mPreviewPosition);
    }

    @Override
    public void onChannel(String moduleName, String methodName, final Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        final Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            resultMap.success(new FlutterStringResult().toMap());
            return;
        }
        saveParams(map, resultMap);
        switch (methodName) {
            case "openGallery":
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        PermissionHelper.requestPermissions(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),
                                activity.getResources().getString(com.jme.common.R.string.me_request_permission_read_storage_gallery),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        Integer max = (Integer) map.get("maxNum");
                                        final int maxNum = max == null ? DEFAULT_MAX_NUM : max;
                                        mGalleryProvider.openGallery(activity, maxNum, REQUEST_CODE_GALLERY);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                        try {
                                            resultMap.success(new FlutterStringResult().toMap());
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                    }
                });
                break;
            case "getImgFromCameraParams":
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        PermissionHelper.requestPermissions(activity,activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),activity.getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                                        File file = new File(FileCache.getInstance().getImageCacheFile(), "capture_rn" + System.currentTimeMillis() + ".jpg");
                                        Uri mCaptureUri = CategoriesKt.getFileUri(AppBase.getAppContext(), file);
                                        mCapturePath = file.getAbsolutePath();
                                        intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUri);
                                        activity.startActivityForResult(intent, REQUEST_CODE_CAPTURE);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                        try {
                                            resultMap.success(new FlutterStringResult().toMap());
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                },Manifest.permission.CAMERA,Manifest.permission.WRITE_EXTERNAL_STORAGE);
                    }
                });
                break;
            case "previewImage": {
                List<String> urls = (List<String>) map.get("urls");
                Integer currentIndex = (Integer) map.get("currentIndex");
                MaeAlbum.setStyle(com.jme.common.R.style.MeAlum);
                MaeAlbum.setImageEngine(mCustomEngine);
                mPreviewPosition = new ArrayList<>();
                mCustomEngine.mList = mPreviewPosition;
                mPreviewPosition.add(currentIndex);
                Intent intent = MaeAlbum.getPreviewIntent(activity, new ArrayList(urls), currentIndex, false, false, false);
                activity.startActivityForResult(intent, REQUEST_CODE_PREVIEW_IMAGE);
                break;
            }
            default:
                resultMap.notImplemented();
                break;
        }
    }

    public void onActivityResult(final Activity activity, int requestCode, int resultCode, Intent data) {
        if (resultMap == null) {
            return;
        }
        if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
            if (resultMap != null) {
                resultMap.success(new FlutterStringResult().toMap());
                resultMap = null;
            }
            return;
        }
        if (mGallery == null) {// 选择完图片之后，不知道为什么 mGallery 会变为 null
            mGallery = ImageCompressUtils.INSTANCE;
        }
        if (requestCode == REQUEST_CODE_GALLERY) {
            if (resultCode == Activity.RESULT_OK) {
//                List<Uri> list = mGalleryProvider.getSelectedFiles(data);
                final List<String> pList = MaeAlbum.obtainPathResult(data);
                if (CollectionUtil.notNullOrEmpty(pList)) {
                    final List<String> filePath = new ArrayList<>();
                    File parent = new File(activity.getExternalCacheDir() + "/rn");
                    DialogUtils.showLoadDialog(activity, R.string.me_rn_compress_image);
                    for (String p : pList) {
                        mGallery.compressAsync(p, new File(parent, System.currentTimeMillis() + ".jpg").getAbsolutePath(), new Function1<String, Unit>() {
                            @Override
                            public Unit invoke(String s) {
                                filePath.add(s);
                                if (filePath.size() >= pList.size()) {
                                    FlutterListResult<String> flutterListResult = new FlutterListResult<>();
                                    flutterListResult.type = SUCCEED;
                                    flutterListResult.params = filePath;
                                    try {
                                        resultMap.success(flutterListResult.toMap());
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    DialogUtils.removeLoadDialog(activity);
                                    mGallery.reset();
                                    resultMap = null;
                                }
                                return null;
                            }
                        });
                    }
                } else {
                    mGallery.reset();
                    resultMap = null;
                }
            } else {
                try {
                    resultMap.success(new FlutterListResult<String>().toMap());
                } catch (java.lang.Exception exception) {
                    exception.printStackTrace();
                }
                mGallery.reset();
                resultMap = null;
            }
        } else if (requestCode == REQUEST_CODE_CAPTURE) {
            if (resultCode == Activity.RESULT_OK) {
                if (mCapturePath == null) {
                    return;
                }
                File parent = new File(activity.getExternalCacheDir() + "/rn");
                DialogUtils.showLoadDialog(activity, R.string.me_rn_compress_image);
                mGallery.compressAsync(mCapturePath, new File(parent, System.currentTimeMillis() + ".jpg").getAbsolutePath(), new Function1<String, Unit>() {
                    @Override
                    public Unit invoke(String s) {
//                        List<Uri> strings = Collections.singletonList(Uri.parse(s));
                        FlutterStringResult flutterStringResult = new FlutterStringResult();
                        flutterStringResult.type = SUCCEED;
                        flutterStringResult.params = s;
                        try {
                            resultMap.success(flutterStringResult.toMap());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        DialogUtils.removeLoadDialog(activity);
                        mGallery.reset();
                        resultMap = null;
                        return null;
                    }
                });
            } else {
                mGallery.reset();
                resultMap = null;
            }
        } else if (requestCode == REQUEST_CODE_PREVIEW_IMAGE) {
            if (resultMap != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("viewedIndex", mPreviewPosition);
                resultMap.success(result);

                mPreviewPosition.clear();
                resultMap = null;
                mCustomEngine.mList = null;
            }
        }
    }

    @SuppressWarnings("rawtypes")
    private void saveParams(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        // 存储本次请求参数
        mGallery = ImageCompressUtils.INSTANCE;
        mGallery.reset();
        this.resultMap = resultMap;
        // 默认不压缩
        CompressType type;
        Integer compressType = (Integer) map.get("compressType");
        if (compressType == null) {
            type = CompressType.NO;//不压缩
        } else if (compressType == 0) {// 0 压缩到指定大小
            type = CompressType.SIZE;
            Integer value = (Integer) map.get("sizeValue");// 最终图片大小
            if (value != null) {
                mGallery.setSizeValue(value);
            } else {
                type = CompressType.NO;
            }
        } else if (compressType == 1) { // 按给定比例压缩
            type = CompressType.RATIO;
            Float value = (Float) map.get("ratioValue");
            if (value != null) {// 压缩比例
                mGallery.setRatioValue(value);
            } else {// 按比例压缩，但未指定压缩比，就认为不压缩
                type = CompressType.NO;
            }
        } else {
            type = CompressType.NO;//不压缩
        }
        mGallery.setType(type);
    }

    private static class CustomEngine extends  GlideEngine {

        List<Integer> mList;

        public CustomEngine(List<Integer> list) {
            mList = list;
        }

        @Override
        public void onOuterPreviewPageSelected(int position, Object path, boolean loadImgSuccess) {
            super.onOuterPreviewPageSelected(position, path, loadImgSuccess);
            if (mList != null && !mList.contains(position)) {
                mList.add(position);
            }
        }
    }
}
