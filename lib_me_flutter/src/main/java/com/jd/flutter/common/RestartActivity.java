package com.jd.flutter.common;

import android.os.Bundle;
import android.os.Handler;

import androidx.appcompat.app.ActionBar;

import com.jd.oa.BaseActivity;
import com.jd.oa.utils.LocaleUtils;

public class RestartActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().setStatusBarColor(getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                LocaleUtils.restartApp(RestartActivity.this);
            }
        }, 500);
    }

    @Override
    protected void configTimlineTheme() {
        //super.configTimlineTheme();
    }
}
