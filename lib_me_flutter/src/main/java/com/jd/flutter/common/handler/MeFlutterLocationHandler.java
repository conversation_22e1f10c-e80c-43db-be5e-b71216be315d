package com.jd.flutter.common.handler;

import android.Manifest;
import android.app.Activity;

import com.jd.flutter.R;
import com.jd.oa.AppBase;
import com.jd.oa.location.LocationManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.CommonUtils;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;
import com.tencent.map.geolocation.TencentLocation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

public class MeFlutterLocationHandler implements IJDFChannelHandler {

    public static final String SERVICE_LOCATION = "com.jdme.flutter/service/location";

    public static final String ERROR_CODE_SUCCESS = "0";
    public static final String ERROR_CODE_NO_PERMISSION = "1";
    public static final String ERROR_CODE_GPS_DISABLE = "2";
    public static final String ERROR_CODE_OTHER = "3";

    @Override
    public String getModuleName() {
        return SERVICE_LOCATION;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        final Activity activity = AppBase.getTopActivity();
        switch (methodName) {
            case "getLocation":
                getLocation(activity, map, resultMap);
                break;
            default:
                resultMap.notImplemented();
                break;
        }
    }

    public void getLocation(final Activity activity, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        if (activity == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("code", ERROR_CODE_OTHER);
            result.put("errorMessage", "Activity is null");
            resultMap.success(result);
            return;
        }
        boolean requestLocation = false;
        if (map.containsKey("requestPermission")) {
            requestLocation = (boolean) map.get("requestPermission");
        }

        if(requestLocation) {
            PermissionHelper.requestPermissions(activity, activity.getString(R.string.me_request_permission_title_normal), activity.getString(R.string.me_request_permission_location_normal),
                    new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            locate(activity, resultMap);
                        }

                        @Override
                        public void denied(List<String> deniedList) {
                            if (deniedList.size() < 2) {
                                locate(activity, resultMap);
                            } else {
                                Map<String, Object> result = new HashMap<>();
                                result.put("code", ERROR_CODE_NO_PERMISSION);
                                result.put("errorMessage", "Permission denied");
                                resultMap.success(result);
                            }
                        }
                    },
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
            );
        } else {
            locate(activity, resultMap);
        }
    }

    private void locate(Activity activity, final IJDFMessageResult<Map> resultMap) {
        boolean enabled = CommonUtils.isGpsEnabled(activity);
        if (!enabled) {
            //resultMap.error(ERROR_CODE_GPS_DISABLE, "Gps not opened", new Throwable());
            Map<String, Object> map = new HashMap<>();
            map.put("code", ERROR_CODE_GPS_DISABLE);
            map.put("errorMessage", "Gps is not enabled");
            resultMap.success(map);
            return;
        }
        Disposable disposable = LocationManager.get(activity)
                .getLocation()
                .timeout(10, TimeUnit.SECONDS)
                .subscribe(new Consumer<TencentLocation>() {
                    @Override
                    public void accept(TencentLocation tencentLocation) throws Exception {
                        Map<String, Object> map = new HashMap<>();
                        map.put("code", ERROR_CODE_SUCCESS);
                        Map<String,Object> result = new HashMap<>();
                        result.put("latitude", tencentLocation.getLatitude());
                        result.put("longitude", tencentLocation.getLongitude());
                        result.put("altitude", tencentLocation.getAltitude());
                        result.put("accuracy", tencentLocation.getAccuracy());

                        map.put("result", result);
                        resultMap.success(map);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        //resultMap.error(ERROR_CODE_GPS_OTHER, throwable.getMessage(), throwable);
                        Map<String, Object> map = new HashMap<>();
                        map.put("code", ERROR_CODE_OTHER);
                        map.put("errorMessage", throwable.getMessage());
                        resultMap.success(map);
                    }
                });
    }
}