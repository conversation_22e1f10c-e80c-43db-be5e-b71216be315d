package com.jd.flutter.common.handler;

import android.text.TextUtils;

import com.jd.flutter.common.model.ToJson;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

public class MeFlutterImHandler implements IJDFChannelHandler {

    public static final String BUSINESS_IM = "com.jdme.flutter/business/im";

    @Override
    public String getModuleName() {
        return BUSINESS_IM;
    }

    @Override
    public void onChannel(String moduleName, String methodName, final Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        switch (methodName) {
            case "sendJueCardToChat": {
                sendJueCardToChat(map, resultMap);
                break;
            }
        }
    }

    public void sendJueCardToChat(final Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String pin = (String) map.get("pin");
        String app = (String) map.get("app");
        Boolean isGroup = (Boolean) map.get("isGroup");
        String source = (String) map.get("source");
        String templateData = (String) map.get("templateData");

        if (isGroup == null) isGroup = false;
        if (!isGroup) {
            if (TextUtils.isEmpty(pin)) {
                resultMap.error("Pin is empty", null, null);
                return;
            } else if (TextUtils.isEmpty(app)) {
                resultMap.error("App is empty", null, null);
                return;
            }
        }

        if (TextUtils.isEmpty(templateData)) {
            resultMap.error("TemplateData is empty", null, null);
            return;
        }

        ImDdService imDdService = AppJoint.service(ImDdService.class);
        imDdService.sendJueCardToChat(pin, app, isGroup, source, templateData, new LoadDataCallback<Void>() {
            @Override
            public void onDataLoaded(Void unused) {
                Map<String,Object> result = new HashMap<>();
                result.put("type", ToJson.SUCCEED);
                resultMap.success(result);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                resultMap.error(s, "code: " + i, null);
                Map<String,Object> result = new HashMap<>();
                result.put("type", ToJson.FAILED);
                resultMap.success(result);
            }
        });
    }
}
