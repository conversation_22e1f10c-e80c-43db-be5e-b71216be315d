package com.jd.flutter.common.handler;

import static com.jd.flutter.common.JDFHelper.callFlutter;
import static com.jd.oa.utils.FileUtils.getExtension;
import static com.jd.oa.utils.OpenFileUtil.APP_SOURCE_FLUTTER;
import static com.jd.oa.utils.OpenFileUtil.sendFileClickEvent;

import android.app.Activity;

import com.jd.oa.AppBase;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.OpenFileUtil;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

public class MeFlutterFilePlugin implements IJDFChannelHandler {
    public static final String SERVICE_FILE = "com.jdme.flutter/service/file";

    @Override
    public String getModuleName() {
        return SERVICE_FILE;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        //noinspection SwitchStatementWithTooFewBranches
        switch (methodName) {
            case "view": {//预览
                String path = (String) map.get("path");
//                String type = (String) map.get("type");
                String name = (String) map.get("name");
                String url = "";
                if (map.containsKey("url")) {
                    url = (String) map.get("url");
                }
                Activity activity = AppBase.getTopActivity();
                if (activity != null) {
                    OpenFileUtil.openFile(activity, path, FileUtils.getFileRegex(name), name);
                }
                sendFileClickEvent(APP_SOURCE_FLUTTER, getExtension(name), url);
                resultMap.success(new HashMap<>());
                break;
            }
            default:
                resultMap.notImplemented();
                break;

        }
    }

    public static void chooseFileFromJS(final Map<String, Object> params, final Callback2<Boolean> cb) {
        //noinspection rawtypes
        callFlutter(SERVICE_FILE, "chooseFileFromJS", params, new IJDFMessageResult<Map>() {
            @Override
            public void success(Map result) {
                cb.onSuccess(true, true);
            }

            @Override
            public void error(String var1, String var2, Object var3) {
                cb.onFail("code: " + var1 + ", msg: " + var2);
            }

            @Override
            public void notImplemented() {
                cb.onFail("notImplemented");
            }
        });
    }
}