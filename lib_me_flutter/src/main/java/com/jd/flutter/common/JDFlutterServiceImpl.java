package com.jd.flutter.common;

import android.content.Context;
import android.content.Intent;

import com.jd.oa.model.service.JDFlutterService;

import java.util.Map;

public class JDFlutterServiceImpl implements JDFlutterService {

    @Override
    public String openFlutterPage(Context context, String routeUrl, Map<String, Object> params, int requestCode) {
        return JDFHelper.openFlutterPage(context, routeUrl, params, requestCode);
    }

    @Override
    public Intent getOpenFlutterPageIntent(Context context, String routeUrl, Map<String, Object> params) {
        return JDFHelper.getOpenFlutterPageIntent(context, routeUrl, params);
    }
}