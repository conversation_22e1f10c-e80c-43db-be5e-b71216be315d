package com.jd.flutter.common.handler;

import com.google.gson.Gson;
import com.jd.flutter.common.model.FlutterListResult;
import com.jd.flutter.common.model.ToJson;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import org.json.JSONObject;

import java.util.Map;

import static com.jd.flutter.common.model.ToJson.CANCELED;
import static com.jd.flutter.common.model.ToJson.SUCCEED;

public class MeFlutterShareHandler implements IJDFChannelHandler {

    public static final String SERVICE_SHARE = "com.jdme.flutter/service/share";
    private final FlutterListResult<Map<String, Object>> flutterListResult = new FlutterListResult<>();

    @Override
    public String getModuleName() {
        return SERVICE_SHARE;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String arg = new Gson().toJson(map);
        switch (methodName) {

            case "shareToTimlineWithParams"://分享到咚咚
//                Map params = {
//                        'url': deeplink,
//                    'title': '来自${JMEUserDefault.realName}的日程分享',
//                    'content': content,
//                    'icon':
//                'http://storage.jd.com/jd.jme.photo/message_icon_schedule2x.png?Expires=3687851871&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=eADExHDPxJlqpqGBhAWpXUo6nmw%3D',
//                    };
                try {
                    JSONObject jsonObject = new JSONObject(arg);
                    AppBase.iAppBase.imSharePic(jsonObject.getString("title"),
                            jsonObject.getString("content"),
                            jsonObject.getString("url"),
                            jsonObject.optString("icon"), "jdim_share_link");
                    flutterListResult.type = SUCCEED;
                } catch (Exception e) {
                    flutterListResult.type = CANCELED;
                    e.printStackTrace();
                }
                resultMap.success(flutterListResult.toMap());
                break;
            case "shareToTimlineWithCard"://分享到咚咚卡片
                try {
                    JSONObject jsonObject = new JSONObject(arg);
                    AppBase.iAppBase.sendVoteMsg(jsonObject.getString("gid"), jsonObject.getString("url"),
                            jsonObject.getString("title"), jsonObject.optString("content"),
                            jsonObject.optString("icon"), jsonObject.optString("source"),
                            jsonObject.optString("sourceIcon"));
                    flutterListResult.type = SUCCEED;
                } catch (Exception e) {
                    flutterListResult.type = CANCELED;
                    e.printStackTrace();
                }
                resultMap.success(flutterListResult.toMap());
                break;
            case "sendShareCardMessage":
                FlutterListResult<Map<String, Object>> result = new FlutterListResult<>();
                try {
                    String pin = "";
                    String app = "";
                    String sessionTypeStr = "";
                    JSONObject jsonParam = new JSONObject(arg);
                    JSONObject jsonObject = jsonParam.optJSONObject("session");
                    if (jsonObject == null) {
                        return;
                    }
                    String sessionKey = jsonObject.optString("sessionKey");
                    if (jsonObject.has("pin")) {
                        pin = jsonObject.optString("pin");
                    }
                    if (jsonObject.has("app")) {
                        app = jsonObject.optString("app");
                    }
                    if (jsonObject.has("sessionType")) {
                        sessionTypeStr = jsonObject.optString("sessionType");
                    }
                    AppJoint.service(ImDdService.class).sendShareLinkMsg(sessionKey, pin, app, Integer.parseInt(sessionTypeStr),
                            jsonParam.optString("url"),
                            jsonParam.optString("title"),
                            jsonParam.optString("content"),
                            jsonParam.optString("icon"),
                            jsonParam.optString("source"),
                            jsonParam.optString("sourceIcon"),
                            jsonParam.optString("category"));
                    result.type = SUCCEED;
                    resultMap.success(result.toMap());
                } catch (Exception e) {
                    e.printStackTrace();
                    resultMap.error("-1", e.getMessage(), e);
                }
            default:
                resultMap.notImplemented();
                break;
        }
    }
}
