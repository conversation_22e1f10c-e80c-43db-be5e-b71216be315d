package com.jd.flutter.common.handler;

import static com.jd.flutter.common.JDFHelper.callFlutter;

import android.os.Build;



import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class MeFlutterTimeZoneHandler implements IJDFChannelHandler {

    public static final String SERVICE_TIME_ZONE = "com.jdme.flutter/service/timezone";

    private static final String TIME_ZONE_CHANGED = "timeZoneChanged";

    @Override
    public String getModuleName() {
        return SERVICE_TIME_ZONE;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        if ("getTimeZoneOffset".equals(methodName)) {
            getTimeZoneOffset(map, resultMap);
        } else if("getLocalTimeZone".equals(methodName)) {
            getLocalTimezone(map, resultMap);
        } else if ("getAvailableTimeZones".equals(methodName)) {
            getAvailableTimezones(map, resultMap);
        } else {
            resultMap.notImplemented();
        }
    }

    private void getTimeZoneOffset(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        try {
            String timeZoneId = (String) map.get("timeZoneId");
            Long date = (Long) map.get("date");
            TimeZone timeZone = TimeZone.getTimeZone(timeZoneId);
            int rawOffset = timeZone.getRawOffset();
            int offset = timeZone.getOffset(date);
            Map<String,Integer> result = new HashMap<>();
            result.put("rawOffset", rawOffset);
            result.put("offset", offset);
            resultMap.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.error("getTimeZoneOffset error", e.getMessage(), e);
        }
    }

    private void getLocalTimezone(Map<String, Object> map, final IJDFMessageResult<Map> resultMap){
        String timeZoneId = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            timeZoneId = ZoneId.systemDefault().getId();
        } else {
            timeZoneId = TimeZone.getDefault().getID();
        }
        Map<String, String> result = new HashMap<>();
        result.put("timeZone", timeZoneId);
        resultMap.success(result);
    }

    private void getAvailableTimezones(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        List<String> timezones;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            timezones = new ArrayList<>(ZoneId.getAvailableZoneIds());
        } else {
            timezones = Arrays.asList(TimeZone.getAvailableIDs());
        }
        Map<String,Object> result = new HashMap<>();
        result.put("timeZones", timezones);
        resultMap.success(result);
    }

    public static void onTimeZoneChanged(String timeZone) {
        Map<String, Object> params = new HashMap<>();
        params.put("timeZoneId", timeZone);
        callFlutter(SERVICE_TIME_ZONE, TIME_ZONE_CHANGED, params, null);
    }
}
