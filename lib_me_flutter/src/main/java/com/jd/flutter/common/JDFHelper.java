package com.jd.flutter.common;

import static com.jd.flutter.common.handler.MeFlutterAppHandler.BUSINESS_LOGIN;
import static com.jd.flutter.common.handler.MeFlutterAppHandler.METHOD_APP_BACKGROUND;
import static com.jd.flutter.common.handler.MeFlutterAppHandler.METHOD_APP_FOREGROUND;
import static com.jd.flutter.common.handler.MeFlutterAppHandler.METHOD_RESET_VERIFY_CODE;
import static com.jd.flutter.common.handler.MeFlutterAppHandler.SERVICE_APPLICATION;
import static com.jd.flutter.common.handler.MeFlutterCalendarHandler.BUSINESS_CALENDAR;
import static com.jd.flutter.common.handler.MeFlutterUploadHandler.SERVICE_UPLOAD;
import static com.jd.flutter.common.handler.TabCommonHandler.COM_JDME_FLUTTER_TAB_COMMON;
import static com.jd.oa.fragment.BaseFragment.PARAMS;
import static com.jd.oa.fragment.js.hybrid.JsEvent.ACTION_LOGIN_RESET_VERIFY_CODE;
import static com.jd.oa.utils.LocaleUtils.LANGUAGE_CHANGED;
import static com.jd.oa.utils.TabletUtil.isSplit;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.idlefish.flutterboost.containers.FlutterBoostActivity;
import com.jd.flutter.BuildConfig;
import com.jd.flutter.common.handler.MEFlutterJDMTAPlugin;
import com.jd.flutter.common.handler.MEFlutterJdMeetingHandler;
import com.jd.flutter.common.handler.MEFlutterRRulePlugin;
import com.jd.flutter.common.handler.MeFlutterAppHandler;
import com.jd.flutter.common.handler.MeFlutterBrowserHandler;
import com.jd.flutter.common.handler.MeFlutterCalendarHandler;
import com.jd.flutter.common.handler.MeFlutterCommonHandler;
import com.jd.flutter.common.handler.MeFlutterDatePickerPlugin;
import com.jd.flutter.common.handler.MeFlutterDeepLinkHandler;
import com.jd.flutter.common.handler.MeFlutterEventHandler;
import com.jd.flutter.common.handler.MeFlutterFilePlugin;
import com.jd.flutter.common.handler.MeFlutterGalleryHandler;
import com.jd.flutter.common.handler.MeFlutterImHandler;
import com.jd.flutter.common.handler.MeFlutterJoyworkHandler;
import com.jd.flutter.common.handler.MeFlutterLocationHandler;
import com.jd.flutter.common.handler.MeFlutterLoggingHandler;
import com.jd.flutter.common.handler.MeFlutterShareHandler;
import com.jd.flutter.common.handler.MeFlutterStatusBarHandler;
import com.jd.flutter.common.handler.MeFlutterStoragePlugin;
import com.jd.flutter.common.handler.MeFlutterThemePlugin;
import com.jd.flutter.common.handler.MeFlutterTimeZoneHandler;
import com.jd.flutter.common.handler.MeFlutterUploadHandler;
import com.jd.flutter.common.handler.TabCommonHandler;
import com.jd.me.filetransfer.JdmeFileDownloadPlugin;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.preference.FlutterPreference;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.ToastUtils;
import com.jdshare.jdf_channel.JDFChannelModule;
import com.jdshare.jdf_container_plugin.assistant.JDFLogger;
import com.jdshare.jdf_container_plugin.components.channel.api.JDFChannelHelper;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;
import com.jdshare.jdf_container_plugin.components.router.api.JDFRouterHelper;
import com.jdshare.jdf_container_plugin.components.router.internal.IJDFRouterSettings;
import com.jdshare.jdf_container_plugin.container.IJDFContainerLifeCycle;
import com.jdshare.jdf_container_plugin.container.JDFComponentConfig;
import com.jdshare.jdf_container_plugin.container.JDFContainer;
import com.jdshare.jdf_router_plugin.JDFRouterChannelHandler;
import com.jdshare.jdf_router_plugin.container.JDFRouterModule;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import io.flutter.FlutterInjector;
import io.flutter.embedding.android.FlutterActivityLaunchConfigs;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.engine.FlutterEngineCache;
import io.flutter.embedding.engine.FlutterEngineGroup;
import io.flutter.embedding.engine.dart.DartExecutor;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;

/**
 * author : JOJO
 * e-mail : <EMAIL>
 * date   : 2020/6/4 12:03 PM
 * desc   : JDFlutter容器接入的辅助类
 * 职责：初始化容器、混合路由、其他所需要的原生基础能力、Flutter与Native通信时的Channel注册等。
 */
public class JDFHelper {
    public static final String UPDATE_CALENDAR = "updateCalendar";
    public static final String SAVED_SETTING = "savedSetting";
    public static final String SEND_ACTION = "sendAction";
    public static final String UPDATE_UPDATE = "update";
    public static final String UPLOAD_FILE = "uploadFile";
    public static final String UPLOAD_FILE_CALLBACK = "uploadFileCallback";

    public static final String FLUTTER_BOOST_DEFAULT_ENGINE = "flutter_boost_default_engine";
    public static final String TAB_ENGINE = "tab_engine";
    //    public static final String LOGIN_ENGINE = "login_engine";
    public static final String SCHEDULE_TAB_MAIN = "scheduleTabMain";
    private static final String MAIN = "main";
    public static volatile JDFHelper instance;
    public static JDFLogger logger = JDFLogger.getLogger("JDFAccessApp"); //使用JDFHelper.getInstance().logger打印日志时，可以使用'JDFAccessApp'过滤日志
    MeFlutterGalleryHandler meFlutterGalleryHandler = new MeFlutterGalleryHandler();

    MeFlutterCommonHandler mMeFlutterCommonHandler = new MeFlutterCommonHandler();
    private FlutterEngineGroup engineGroup;

    private final LocalBroadcastReceiver localReceiver = new LocalBroadcastReceiver();
    private final CalenderLocalBroadcastReceiver calenderLocalReceiver = new CalenderLocalBroadcastReceiver();

    public static JDFHelper getInstance() {
        if (instance == null) {
            synchronized (JDFHelper.class) {
                if (instance == null) {
                    instance = new JDFHelper();
                }
            }
        }
        return instance;
    }

    @NotNull
    private FlutterEngineGroup getEngineGroup(Application application) {
        if (engineGroup == null) {
            engineGroup = new FlutterEngineGroup(application);
        }
        return engineGroup;
    }

    public void addCachedEngine(Application application, String key, String point) {
        if (!isSplit()) {
            return;
        }
        FlutterEngineGroup engineGroup = getEngineGroup(application);
        FlutterEngine flutterEngine = FlutterEngineCache.getInstance().get(key);
        if (flutterEngine != null) {
            return;
        }
        if (point == null) {
            point = MAIN;
        }
        FlutterEngine engine;
        if (BuildConfig.FLUTTER_LOCAL_DEBUG) {
            //debug不使用FlutterEngineGroup避免restart资源找不到
            engine = new FlutterEngine(application);
            DartExecutor.DartEntrypoint dartEntrypoint = new DartExecutor.DartEntrypoint(
                    FlutterInjector.instance().flutterLoader().findAppBundlePath(), point
            );
            engine.getDartExecutor().executeDartEntrypoint(dartEntrypoint);
            ToastUtils.showToastLong(com.jme.common.R.string.security_test_tip);
        } else {
            DartExecutor.DartEntrypoint dartEntrypoint = new DartExecutor.DartEntrypoint(
                    FlutterInjector.instance().flutterLoader().findAppBundlePath(), point
            );
            engine = engineGroup.createAndRunEngine(application, dartEntrypoint);
        }
        GeneratedPluginRegistrant.registerWith(engine);

        if (!FLUTTER_BOOST_DEFAULT_ENGINE.equals(key)) {
            addMethod(engine);
        }
        FlutterEngineCache.getInstance().put(key, engine);
    }

//    public void resetLoginPath() {
//        if (!isSplit()) {
//            return;
//        }
//        FlutterEngine flutterEngine = FlutterEngineCache.getInstance().get(LOGIN_ENGINE);
//        if (flutterEngine != null) {
//            MethodChannel methodChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "com.jdme.flutter/android/login");
//            methodChannel.invokeMethod("resetPath", new HashMap<String, String>());
//        }
//    }

    public void init(final Application application) {
        StartRunTimeMonitor.getInstance().record("Apps FlutterInitDetail FlutterPreference");
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_DID, DeviceUtil.getDeviceUniqueId());
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_CLIENT, "Android");
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_APP_VERSION, DeviceUtil.getLocalVersionName(AppBase.getAppContext()));
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_OS_VERSION, android.os.Build.VERSION.RELEASE);
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_BRAND, android.os.Build.BRAND);
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_MODEL, android.os.Build.MODEL);
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_OS_NAME, "android");
        FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_MACHINE_TYPE, Build.MODEL);
        StartRunTimeMonitor.getInstance().end("Apps FlutterInitDetail FlutterPreference");
        StartRunTimeMonitor.getInstance().record("Apps FlutterInitDetail addCachedEngine");
        addCachedEngine(application, FLUTTER_BOOST_DEFAULT_ENGINE, MAIN);
        addCachedEngine(application, TAB_ENGINE, SCHEDULE_TAB_MAIN);
        StartRunTimeMonitor.getInstance().end("Apps FlutterInitDetail addCachedEngine");
        StartRunTimeMonitor.getInstance().record("Apps FlutterInitDetail initContainer");
        JDFContainer.initContainerContextAndOnRegister(application, new IJDFContainerLifeCycle() {
            @Override
            public void onRegister() {
                registerComponent(application);
                JDFRouterChannelHandler.sendNativeRoutes2Flutter();

                // 设置拦截器(全局拦截和某个页面拦截)、设置默认容器页（不推设置，用默认的）
                IJDFRouterSettings routerSettings = JDFRouterHelper.initRouterSettings(application);
                routerSettings.setCustomFlutterActivity(MeFlutterContainerActivity.class);

                registerChannelHandler();
                addMethod(JDFRouterHelper.getFlutterEngine());
//                System.out.println("--- start Time end: " + System.currentTimeMillis());
                onCreateReceiver(localReceiver, calenderLocalReceiver);
            }
        });
        StartRunTimeMonitor.getInstance().end("Apps FlutterInitDetail initContainer");
    }

    private void addMethod(FlutterEngine engine) {
        MethodChannel methodChannel = new MethodChannel(engine.getDartExecutor().getBinaryMessenger(), COM_JDME_FLUTTER_TAB_COMMON);
        methodChannel.setMethodCallHandler(new TabCommonHandler());

        //处理依赖的第三方插件，需要在GeneratedPluginRegistrant手动注册
        try {
            engine.getPlugins().add(new JdmeFileDownloadPlugin());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //注册接收Flutter的消息（监听Flutter的调用）
    private void registerChannelHandler() {
        //注册处理Flutter调用Native的Channel消息通道
        JDFChannelHelper.registerMethodChannel(new MeFlutterFilePlugin());
        JDFChannelHelper.registerMethodChannel(new MeFlutterShareHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterBrowserHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterAppHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterDeepLinkHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterUploadHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterDatePickerPlugin());
        JDFChannelHelper.registerMethodChannel(new MEFlutterJDMTAPlugin());
        JDFChannelHelper.registerMethodChannel(new MEFlutterRRulePlugin());
        JDFChannelHelper.registerMethodChannel(new MeFlutterCalendarHandler());
        JDFChannelHelper.registerMethodChannel(mMeFlutterCommonHandler);
        JDFChannelHelper.registerMethodChannel(new MeFlutterEventHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterStoragePlugin());
        JDFChannelHelper.registerMethodChannel(new MeFlutterThemePlugin());
        JDFChannelHelper.registerMethodChannel(meFlutterGalleryHandler);
        JDFChannelHelper.registerMethodChannel(new MeFlutterLocationHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterStatusBarHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterLoggingHandler());
        JDFChannelHelper.registerMethodChannel(new MEFlutterJdMeetingHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterTimeZoneHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterImHandler());
        JDFChannelHelper.registerMethodChannel(new MeFlutterJoyworkHandler());
    }

    //注入具体功能模块的实现
    private void registerComponent(Application application) {
        //注入混合路由的真正实现类，必写，否则混合路由将无法正常使用
        // 注意顺序
        JDFContainer.registerComponent(JDFComponentConfig.JDRouter, JDFRouterModule.getInstance(), application);
        JDFContainer.registerComponent(JDFComponentConfig.JDChannel, new JDFChannelModule(), application);
    }

    public static void changLanguage(Locale locale) {
        Map<String, Object> params = new HashMap<>();
        params.put("language", locale.getLanguage());
        callFlutter(SERVICE_APPLICATION, LANGUAGE_CHANGED, params, null);
    }


    public static void invisibleFaceLogin(){
        callFlutter(BUSINESS_LOGIN, "invisibleFaceLogin", new HashMap<>(), null);
    }
    /*
     * 这个方法不好区分哪个引擎，暂时将所有引擎都执行一遍。
     * */
    @SuppressWarnings("rawtypes")
    public static void callFlutter(final String moduleName, final String methodName, Map<String, Object> intentMap, IJDFMessageResult<Map> result) {
        if (intentMap == null) {
            intentMap = new HashMap<>();
        }
        if (result == null) {
            result = new IJDFMessageResult<Map>() {
                @Override
                public void success(Map map) {
                }

                @Override
                public void error(String s, String s1, Object o) {
                }

                @Override
                public void notImplemented() {
                }
            };
        }
        try {
            final Map<String, Object> finalIntentMap = intentMap;
            final IJDFMessageResult<Map> finalResult = result;
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    JDFChannelHelper.callFlutterMethod(moduleName, methodName, finalIntentMap, finalResult);
                }
            });
        } catch (Exception e) {
//            e.printStackTrace();
        }
        try {
            invokeFlutterMethod(TAB_ENGINE, moduleName, methodName, intentMap);
        } catch (Exception e) {
//            e.printStackTrace();
        }
//        try {
//            invokeFlutterMethod(LOGIN_ENGINE, moduleName, methodName, intentMap);
//        } catch (Exception e) {
////            e.printStackTrace();
//        }
    }


    public void onActivityResult(final Activity activity, int requestCode, int resultCode, Intent data) {
        meFlutterGalleryHandler.onActivityResult(activity, requestCode, resultCode, data);
        mMeFlutterCommonHandler.onActivityResult(activity, requestCode, resultCode, data);
    }

    private static void invokeFlutterMethod(String engineId, String moduleName, String methodName, final Map<String, Object> args) {
        if (!isSplit()) {
            return;
        }
        FlutterEngine flutterEngine = FlutterEngineCache.getInstance().get(engineId);
        DartExecutor dartExecutor = null;
        if (flutterEngine != null) {
            dartExecutor = flutterEngine.getDartExecutor();
        }
        if (dartExecutor != null) {
            final MethodChannel methodChannel = new MethodChannel(dartExecutor.getBinaryMessenger(), COM_JDME_FLUTTER_TAB_COMMON);
            args.put("moduleName", moduleName);
            args.put("methodName", methodName);
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    methodChannel.invokeMethod("flutterMethod", args);
                }
            });
        }
    }


    private static class LocalBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            @SuppressWarnings("rawtypes") final HashMap params = new Gson().fromJson(intent.getStringExtra(PARAMS), HashMap.class);
            if (LANGUAGE_CHANGED.equals(intent.getAction())) { //params：｛language：zh｝
                //noinspection unchecked
                callFlutter(SERVICE_APPLICATION, LANGUAGE_CHANGED, params, null);
            } else if (UPLOAD_FILE.equals(intent.getAction())) {
                String path = intent.getStringExtra("filePath");
                boolean needCdn = intent.getBooleanExtra("needCdn", false);
                boolean needAuth = intent.getBooleanExtra("needAuth", false);
                String ossBucketName = intent.getStringExtra("ossBucketName");
                final Map<String, Object> param = new HashMap<>();
                param.put("filePath", path);
                param.put("needCdn", needCdn);
                param.put("needAuthn", needAuth);
                param.put("ossBucketName", ossBucketName);
                callFlutter(SERVICE_UPLOAD, "uploadFile", param, null);
            } else if (AppBase.ACTION_JDME_APP_BACKGROUND.equals(intent.getAction())) {
                callFlutter(SERVICE_APPLICATION, METHOD_APP_BACKGROUND, params, null);
            } else if (AppBase.ACTION_JDME_APP_FOREGROUND.equals(intent.getAction())) {
                callFlutter(SERVICE_APPLICATION, METHOD_APP_FOREGROUND, params, null);
            }else if(ACTION_LOGIN_RESET_VERIFY_CODE.equals(intent.getAction())){
                callFlutter(BUSINESS_LOGIN, METHOD_RESET_VERIFY_CODE, null, null);
            }
        }
    }

    @SuppressWarnings("rawtypes")
    private static class CalenderLocalBroadcastReceiver extends BroadcastReceiver {
        @SuppressWarnings("unchecked")
        @Override
        public void onReceive(Context context, final Intent intent) {
            HashMap params = new Gson().fromJson(intent.getStringExtra(PARAMS), HashMap.class);
            if (UPDATE_CALENDAR.equals(intent.getAction())) {
                callFlutter(BUSINESS_CALENDAR, UPDATE_CALENDAR, params, null);
            } else if (SAVED_SETTING.equals(intent.getAction())) {
                callFlutter(BUSINESS_CALENDAR, SAVED_SETTING, params, null);
            } else if (SEND_ACTION.equals(intent.getAction())) {
                callFlutter(BUSINESS_CALENDAR, SEND_ACTION, params, null);
            }
        }
    }

//    private static void onDestroyReceiver(LocalBroadcastReceiver localReceiver,
//                                          CalenderLocalBroadcastReceiver calenderLocalReceiver) {
//        LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(AppBase.getAppContext());
//        localBroadcastManager.unregisterReceiver(localReceiver);
//        localBroadcastManager.unregisterReceiver(calenderLocalReceiver);
//    }

    private static void onCreateReceiver(LocalBroadcastReceiver localReceiver,
                                         CalenderLocalBroadcastReceiver calenderLocalReceiver) {
        LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(AppBase.getAppContext());
        IntentFilter intentFilter = new IntentFilter();
        if (localReceiver != null) {
            localBroadcastManager.unregisterReceiver(localReceiver);
//        intentFilter.addAction(QUIT_APP);
            intentFilter.addAction(LANGUAGE_CHANGED);
            intentFilter.addAction(UPLOAD_FILE);
            intentFilter.addAction(AppBase.ACTION_JDME_APP_BACKGROUND);
            intentFilter.addAction(AppBase.ACTION_JDME_APP_FOREGROUND);
            intentFilter.addAction(ACTION_LOGIN_RESET_VERIFY_CODE);
            localBroadcastManager.registerReceiver(localReceiver, intentFilter);
        }
        if (calenderLocalReceiver != null) {
            localBroadcastManager.unregisterReceiver(calenderLocalReceiver);
            intentFilter.addAction(UPDATE_CALENDAR);
            intentFilter.addAction(SAVED_SETTING);
            intentFilter.addAction(SEND_ACTION);
            localBroadcastManager.registerReceiver(calenderLocalReceiver, intentFilter);
        }
    }

    public static String openFlutterPage(Context context, String routeUrl, Map<String, Object> params, int requestCode) {
        try {
            if (context == null || routeUrl == null || params == null) {
                return null;
            }
            Intent intent = getOpenFlutterPageIntent(context, routeUrl, params);
            String extra_unique_id = intent.getStringExtra("unique_id");
            if (context instanceof Activity) {
                ((Activity) context).startActivityForResult(intent, requestCode);
                return extra_unique_id;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Intent getOpenFlutterPageIntent(Context context, String routeUrl, Map<String, Object> params) {
        //直接使用Jd Flutter的SDK源码，跳过一个打开的bug by tianheng
        FlutterBoostActivity.CachedEngineIntentBuilder var10000 = new FlutterBoostActivity.CachedEngineIntentBuilder(MeFlutterContainerActivity.class);
        FlutterActivityLaunchConfigs.BackgroundMode var6 = FlutterActivityLaunchConfigs.BackgroundMode.opaque;
        FlutterBoostActivity.CachedEngineIntentBuilder var8 = var10000.backgroundMode(var6);
        var8 = var8.destroyEngineWithActivity(false);
        var8 = var8.url(routeUrl);
        var8 = var8.urlParams(params);
        Intent intent = var8.build(context);
        return intent;
    }
}
