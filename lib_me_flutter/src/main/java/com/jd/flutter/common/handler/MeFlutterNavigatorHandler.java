package com.jd.flutter.common.handler;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.Utils2Activity;

import java.util.HashMap;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

@SuppressWarnings({"unused", "SwitchStatementWithTooFewBranches"})
public class MeFlutterNavigatorHandler implements MethodChannel.MethodCallHandler {
    @Override
    public void onMethodCall(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result) {
        String method = methodCall.method;
        String arg = methodCall.arguments == null ? "" : methodCall.arguments.toString();
        switch (method) {
            case "closePage":
                Activity activity = AppBase.getTopActivity();
                /**
                 * business.index.MainActivity   指首页主Activity  否则会整个app finish
                 */
                if (activity != null && !activity.isFinishing() && !activity.isDestroyed() && !"business.home.MainActivity".equals(activity.getLocalClassName())) {
                    activity.finish();
                }
                if (!TextUtils.isEmpty(arg)) {
                    HashMap<String, Object> args = new Gson().fromJson(arg, HashMap.class);
                    if (args != null && args.containsKey("schedule_deleted")) {
                        Object scheduleDeleted = args.get("schedule_deleted");
                        if (scheduleDeleted instanceof Boolean && (boolean)scheduleDeleted) {
                            LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(new Intent("schedule_deleted"));
                        }
                    }
                }
                break;
            default:
                result.notImplemented();
                break;
        }
    }
}
