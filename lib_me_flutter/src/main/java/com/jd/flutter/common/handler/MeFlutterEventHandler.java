package com.jd.flutter.common.handler;

import com.jd.oa.AppBase;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;


/**
 * JAP埋点
 * Created by peidongbiao on 2020-02-21
 */
public class MeFlutterEventHandler implements IJDFChannelHandler {

    public static final String BUSINESS_EVENT = "com.jdme.flutter/business/event";

    @Override
    public String getModuleName() {
        return BUSINESS_EVENT;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        try {
            String eventId = (String) map.get("eventId");
            Map<String, String> params = (Map<String, String>) map.get("params");
            if (eventId == null) {
                resultMap.error("eventId is null!", null, null);
                return;
            }
            /*
            switch (methodName) {
                case "onEvent":
                    onEvent(eventId, params);
                    break;
                case "onEventBegin":
                    onPageBegin(eventId);
                    break;
                case "onEventEnd":
                    onPageEnd(eventId);
                    break;
            }
            */
            resultMap.success(new HashMap<>());
        } catch (Exception e) {
            resultMap.error(e.getMessage(), null, null);
        }
    }

    /*
    private void onEvent(String eventId, Map params) {
        PageEventUtil.onEvent(AppBase.getAppContext(), eventId, params);
    }

    private void onPageBegin(String eventId) {
        PageEventUtil.onEventPageBegin(AppBase.getAppContext(), eventId);
    }

    private void onPageEnd(String eventId) {
        PageEventUtil.onEventPageEnd(AppBase.getAppContext(), eventId);
    }
    */
}
