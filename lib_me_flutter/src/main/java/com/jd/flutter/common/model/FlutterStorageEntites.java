package com.jd.flutter.common.model;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.KVMethod;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class FlutterStorageEntites extends AbsKvEntities {

    public String fileName = "";
    public UseType useType;
    public KVMethod kvMethod;

    public FlutterStorageEntites(String fileName, UseType useType, KVMethod kvMethod) {
        this.fileName = fileName;
        this.useType = useType;
        this.kvMethod = kvMethod;
    }

    @Override
    public KVMethod getKVMethod() {
        return kvMethod;
    }

    @Override
    public String getPrefrenceName() {
        return fileName;
    }

    @Override
    public UseType getDefaultUseType() {
        return useType;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    public void put(String key, String val) {
        KvEntity<String> k = new KvEntity(key, "");
        put(k, val);
    }

    public String get(String key, String defaultVal) {
        KvEntity<String> k = new KvEntity(key, defaultVal);
        return get(k);
    }
}
