package com.jd.flutter.common.handler;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.res.Configuration;
import android.os.Build;
import android.view.View;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;

import com.jd.flutter.R;
import com.jd.me.datetime.picker.DatePickerDialog;
import com.jd.me.datetime.picker.DatePickerView;
import com.jd.me.datetime.picker.DatetimePickResult;
import com.jd.me.datetime.picker.DatetimePickerDialog;
import com.jd.me.datetime.picker.SelectDatetime;
import com.jd.me.datetime.picker.TimePickerDialog;
import com.jd.me.datetime.picker.TimePickerView;
import com.jd.me.datetime.picker.WeekDays;
import com.jd.oa.AppBase;
import com.jd.oa.timezone.HolidayHelperKt;
import com.jd.oa.utils.Holder;
import com.jd.oa.utils.LocaleUtils;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class MeFlutterDatePickerPlugin implements IJDFChannelHandler {
    public static final String SERVICE_DATETIME = "com.jdme.flutter/service/datetime";

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String TIME_FORMAT = "HH:mm";
    private static final String WEEK_START_SUNDAY = "sunday";
    private static final String WEEK_START_MONDAY = "monday";

    @Override
    public String getModuleName() {
        return SERVICE_DATETIME;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        switch (methodName) {
            case "selectDate":
                selectDate(map, resultMap);
                break;
            case "selectDatetime":
                selectDatetime(map, resultMap);
                break;
            case "selectTime":
                selectTime(map, resultMap);
                break;
            default:
                resultMap.notImplemented();
                break;
        }
    }

    @SuppressWarnings({"ConstantConditions", "rawtypes"})
    private void selectDate(@NonNull Map<String, Object> map, @NonNull final IJDFMessageResult<Map> resultMap) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            resultMap.error("1", "activity has been destroyed", null);
            return;
        }
        String mode = null;
        String selectedDate = null;
        String startDate = null;
        String endDate = null;
        String minDate = null;
        String maxDate = null;
        Boolean startDateRequired = null;
        Boolean endDateRequired = null;
        Boolean bothRequired = null;
        Integer maxSelectDayRange = null;
        String weekStart = null;
        String timeZone = null;
        Holder<Boolean> needNeverEnd = new Holder<>();
        try {
            mode = (String) map.get("mode");
            selectedDate = (String) map.get("selected");
            startDate = (String) map.get("startDate");
            endDate = (String) map.get("endDate");
            minDate = (String) map.get("minDate");
            maxDate = (String) map.get("maxDate");
            startDateRequired = (Boolean) map.get("startDateRequired");
            endDateRequired = (Boolean) map.get("endDateRequired");
            bothRequired = (Boolean) map.get("bothRequired");
            maxSelectDayRange = (Integer) map.get("maxSelectDayRange");
            weekStart = (String) map.get("weekStart");
            timeZone = (String) map.get("timeZone");
            needNeverEnd.set((Boolean) map.get("neverEnd"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        setDialogLocale(activity);

        DatePickerDialog dialog = new DatePickerDialog(activity);
        DatePickerView.SelectMode selectMode;
        if ("range".equals(mode)) {
            selectMode = DatePickerView.SelectMode.RANGE;
        } else {
            selectMode = DatePickerView.SelectMode.SINGLE;
            mode = "single";
        }

        dialog.setSelectMode(selectMode);
        dialog.setMinDate(parseDate(minDate, timeZone));
        dialog.setMaxDate(parseDate(maxDate, timeZone));
        Date selected = parseDate(selectedDate, timeZone);
        if (selected == null) {
            selected = new Date();
        }
        dialog.setSelectedDate(selected);
        dialog.setStartDate(parseDate(startDate, timeZone));
        dialog.setEndDate(parseDate(endDate, timeZone));
        dialog.setMinDate(parseDate(minDate, timeZone));
        dialog.setMaxDate(parseDate(maxDate, timeZone));
        dialog.setStartDateRequired(startDateRequired != null ? startDateRequired : false);
        dialog.setEndDateRequired(endDateRequired != null ? endDateRequired : false);
        dialog.setAllRequired(bothRequired != null ? bothRequired : false);
        HolidayHelperKt.holidayFetcher(dialog);
        if (maxSelectDayRange != null) {
            dialog.setMaxSelectedDayRange(maxSelectDayRange);
        }

        if (WEEK_START_SUNDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.SUNDAY);
        } else if (WEEK_START_MONDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.MONDAY);
        }
        Holder<Boolean> neverEndResult = new Holder<>(false);
        if (needNeverEnd.get() != null) {
            View extend = dialog.addTopExtendView(activity, R.layout.layout_date_extend);
            SwitchCompat switchEndTime = extend.findViewById(R.id.sw_end_time);
            switchEndTime.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    View datePickerView = dialog.getDatePickerView();
                    if (isChecked) {
                        datePickerView.setVisibility(View.INVISIBLE);
                    } else {
                        datePickerView.setVisibility(View.VISIBLE);
                    }
                    neverEndResult.set(isChecked);
                }
            });
            switchEndTime.setChecked(needNeverEnd.get());
        }

        if (selectMode == DatePickerView.SelectMode.SINGLE) {
            final String finalMode = mode;
            dialog.setOnCalendarSelectedListener(new DatePickerView.OnCalendarSelectedListener() {
                @Override
                public void onSelected(Date date) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("mode", finalMode);
                    SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
                    map.put("date", dateFormat.format(date.getTime()));
                    if (needNeverEnd.get() != null) {
                        map.put("neverEnd", neverEndResult.get());
                    }
                    resultMap.success(map);
                }
            });
        } else if (selectMode == DatePickerView.SelectMode.RANGE) {
            final String finalMode1 = mode;
            dialog.setOnCalendarRangeSelectedListener(new DatePickerView.OnCalendarRangeSelectedListener() {
                @Override
                public void onRangeSelected(Date date, Date date1) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("mode", finalMode1);
                    SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
                    map.put("start", dateFormat.format(date.getTime()));
                    map.put("end", dateFormat.format(date1.getTime()));
                    if (needNeverEnd.get() != null) {
                        map.put("neverEnd", neverEndResult.get());
                    }
                    resultMap.success(map);
                }
            });
        }

        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                resultMap.error("1", "Canceled", null);
            }
        });

        dialog.show();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            dialog.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
    }


    @SuppressWarnings("rawtypes")
    private void selectDatetime(@NonNull Map<String, Object> map, @NonNull final IJDFMessageResult<Map> resultMap) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            resultMap.error("1", "activity has been destroyed", null);
            return;
        }
        String mode = null;
        String selectedDate = null;
        String startDate = null;
        String endDate = null;
        String minDate = null;
        String maxDate = null;
        Boolean startDateRequired = null;
        Boolean endDateRequired = null;
        Boolean bothRequired = null;
        Integer minuteStep = null;
        Integer maxSelectDayRange = null;
        String weekStart = null;
        String timeZone = null;
        List<String> disabledWeekDays = null;
        try {
            mode = (String) map.get("mode");
            selectedDate = (String) map.get("selected");
            startDate = (String) map.get("startDate");
            endDate = (String) map.get("endDate");
            minDate = (String) map.get("minDate");
            maxDate = (String) map.get("maxDate");
            startDateRequired = (Boolean) map.get("startDateRequired");
            endDateRequired = (Boolean) map.get("endDateRequired");
            bothRequired = (Boolean) map.get("bothRequired");
            minuteStep = (Integer) map.get("minuteStep");
            maxSelectDayRange = (Integer) map.get("maxSelectDayRange");
            weekStart = (String) map.get("weekStart");
            timeZone = (String) map.get("timeZone");
            if (map.containsKey("disabledWeekDays")) {
                disabledWeekDays = (List<String>) map.get("disabledWeekDays");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        DatePickerView.SelectMode selectMode;
        if ("range".equals(mode)) {
            selectMode = DatePickerView.SelectMode.RANGE;
        } else {
            selectMode = DatePickerView.SelectMode.SINGLE;
            mode = "single";
        }

        setDialogLocale(activity);

        DatetimePickerDialog dialog = new DatetimePickerDialog(activity);
        dialog.setSelectMode(selectMode);
        dialog.setSelectedDate(parseDate(selectedDate, timeZone));
        dialog.setSelectedTime(parseDate(selectedDate, timeZone));
        dialog.setStartDate(parseDate(startDate, timeZone));
        dialog.setStartTime(parseDate(startDate, timeZone));
        dialog.setEndDate(parseDate(endDate, timeZone));
        dialog.setEndTime(parseDate(endDate, timeZone));
        dialog.setMinDate(parseDate(minDate, timeZone));
        dialog.setMaxDate(parseDate(maxDate, timeZone));
        dialog.setStartDateRequired(startDateRequired != null ? startDateRequired : false);
        dialog.setEndDateRequired(endDateRequired != null ? endDateRequired : false);
        dialog.setAllRequired(bothRequired != null ? bothRequired : false);
        dialog.setSwitchTimeOpenDefault(true);
        dialog.setTimeZone(timeZone);
        if (minuteStep != null) {
            dialog.setMinuteStep(minuteStep);
        }

        if (maxSelectDayRange != null) {
            dialog.setMaxSelectedDayRange(maxSelectDayRange);
        }

        if (WEEK_START_SUNDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.SUNDAY);
        } else if (WEEK_START_MONDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.MONDAY);
        }

        List<WeekDays> weekDays = new ArrayList<>();
        if (disabledWeekDays != null) {
            for (String disabledWeekDay : disabledWeekDays) {
                weekDays.add(WeekDays.fromString(disabledWeekDay));
            }
        }
        dialog.setDisabledWeekDays(weekDays);

        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {

            }
        });

        final String finalMode = mode;
        dialog.setOnConfirmListener(new DatetimePickerDialog.OnConfirmListener() {
            @Override
            public void onConfirm(DatetimePickResult pickResult) {
                if ("single".equals(finalMode)) {
                    Map<String, Object> map = toMap(pickResult.getDatetime());
                    map.put("mode", "single");
                    Map<String, Object> dateTime = new HashMap<>();
                    dateTime.put("datetime", map);
                    resultMap.success(dateTime);
                } else {
                    Map<String, Object> start = toMap(pickResult.getStart());
                    Map<String, Object> end = toMap(pickResult.getEnd());
                    Map<String, Object> map = new HashMap<>();
                    map.put("mode", "range");
                    map.put("start", start);
                    map.put("end", end);
                    resultMap.success(map);
                }
            }
        });
        HolidayHelperKt.holidayFetcher(dialog);
        dialog.show();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            dialog.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
    }

    private void selectTime(@NonNull Map<String, Object> map, @NonNull final IJDFMessageResult<Map> resultMap) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            resultMap.error("1", "activity has been destroyed", null);
            return;
        }

        setDialogLocale(activity);

        TimePickerDialog dialog = new TimePickerDialog(activity);

        String time = null;
        String title = null;
        Integer minuteStep = null;
        try {
            time = (String) map.get("time");
            title = (String) map.get("title");
            minuteStep = (Integer) map.get("minuteStep");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (title != null) {
            dialog.setTitle(title);
        }
        if (minuteStep != null) {
            dialog.setMinuteStep(minuteStep);
        }
        Date timeValue = parseTime(time);
        if (timeValue != null) {
            dialog.setTime(timeValue);
        }
        dialog.setOnTimeConfirmListener(new TimePickerDialog.OnTimeConfirmListener() {
            @Override
            public void onConfirm(Date time, int hour, int minutes) {
                Map<String, Object> dateTime = new HashMap<>();
                dateTime.put("time", new SimpleDateFormat(TIME_FORMAT, Locale.getDefault()).format(time));
                resultMap.success(dateTime);
            }
        });
        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                resultMap.error("1", "Canceled", null);
            }
        });
        dialog.show();
    }

    private Date parseDate(String date, String timeZone) {
        if (date == null) return null;
        try {
            SimpleDateFormat datetimeFormat = new SimpleDateFormat(DATE_TIME_FORMAT, Locale.getDefault());
            //if (timeZone != null) {
                //datetimeFormat.setTimeZone(RecurUtils.getTimeZone(timeZone, TimeZone.getDefault()));
            //}
            return datetimeFormat.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
//                if (timeZone != null) {
//                    dateFormat.setTimeZone(RecurUtils.getTimeZone(timeZone, TimeZone.getDefault()));
//                }
                return dateFormat.parse(date);
            } catch (ParseException ex) {
                ex.printStackTrace();
            }
        }
        return null;
    }

    private Date parseTime(String time) {
        if (time == null) return null;
        try {
            SimpleDateFormat datetimeFormat = new SimpleDateFormat(TIME_FORMAT, Locale.getDefault());
            return datetimeFormat.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    private Map<String, Object> toMap(SelectDatetime datetime) {
        if (datetime == null) return null;
        Map<String, Object> map = new HashMap<>();
        if (datetime.getDate() != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
            map.put("date", dateFormat.format(datetime.getDate()));
        }
        if (datetime.getTime() != null) {
            SimpleDateFormat timeFormat = new SimpleDateFormat(TIME_FORMAT, Locale.getDefault());
            map.put("time", timeFormat.format(datetime.getTime()));
        }
        map.put("timeIsOpen", datetime.isTimeIsOpen());
        return map;
    }

    private void setDialogLocale(Activity activity) {
        try {
            Locale locale = LocaleUtils.getUserSetLocale(activity);
            Configuration config = new Configuration();
            config.setLocale(locale);
            activity.getResources().updateConfiguration(config, activity.getResources().getDisplayMetrics());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}