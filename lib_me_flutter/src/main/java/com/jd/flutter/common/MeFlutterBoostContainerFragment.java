package com.jd.flutter.common;

import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.idlefish.flutterboost.containers.FlutterBoostFragment;
import com.jd.oa.abilities.utils.MELogUtil;

import io.flutter.embedding.android.FlutterSurfaceView;
import io.flutter.embedding.android.FlutterTextureView;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.engine.renderer.FlutterUiDisplayListener;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class MeFlutterBoostContainerFragment extends FlutterBoostFragment {

    public static final String TAG = "FlutterTab";

    private final FlutterUiDisplayListener mFlutterUiDisplayListener = new FlutterUiDisplayListener() {
        @Override
        public void onFlutterUiDisplayed() {
            MELogUtil.localI(TAG, "onFlutterUiDisplayed");
        }

        @Override
        public void onFlutterUiNoLongerDisplayed() {
            MELogUtil.localI(TAG, "onFlutterUiNoLongerDisplayed");
        }
    };

    private final FlutterEngine.EngineLifecycleListener mEngineLifecycleListener = new FlutterEngine.EngineLifecycleListener() {
        @Override
        public void onPreEngineRestart() {
            MELogUtil.localI(TAG, "onPreEngineRestart");
        }

        @Override
        public void onEngineWillDestroy() {
            MELogUtil.localI(TAG, "onEngineWillDestroy");
        }
    };

    public MeFlutterBoostContainerFragment() {
        setArguments(new Bundle());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        registerListener();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onUpdateSystemUiOverlays() {
    }

    @Override
    public void onFlutterTextureViewCreated(FlutterTextureView flutterTextureView) {
        super.onFlutterTextureViewCreated(flutterTextureView);
        MELogUtil.localI(TAG, "MeFlutterBoostContainerFragment onFlutterTextureViewCreated, flutterTextureView: " + flutterTextureView.hashCode());
    }

    @Override
    public void onFlutterSurfaceViewCreated(@NonNull FlutterSurfaceView flutterSurfaceView) {
        super.onFlutterSurfaceViewCreated(flutterSurfaceView);
        MELogUtil.localI(TAG, "MeFlutterBoostContainerFragment onFlutterSurfaceViewCreated, flutterSurfaceView: " + flutterSurfaceView.hashCode());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        FlutterEngine engine = getFlutterEngine();
        if (engine != null) {
            engine.getRenderer().removeIsDisplayingFlutterUiListener(mFlutterUiDisplayListener);
            engine.removeEngineLifecycleListener(mEngineLifecycleListener);
        }
    }

    private void registerListener() {
        try {
            FlutterEngine engine = getFlutterEngine();
            if (engine != null) {
                engine.getRenderer().removeIsDisplayingFlutterUiListener(mFlutterUiDisplayListener);
                engine.getRenderer().addIsDisplayingFlutterUiListener(mFlutterUiDisplayListener);

                engine.removeEngineLifecycleListener(mEngineLifecycleListener);
                engine.addEngineLifecycleListener(mEngineLifecycleListener);
            }
        } catch (Exception e) {
            MELogUtil.localI(TAG, "MeFlutterBoostContainerFragment registerListener, exception", e);
        }
    }
}