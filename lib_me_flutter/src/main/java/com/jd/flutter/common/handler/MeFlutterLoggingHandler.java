package com.jd.flutter.common.handler;

import android.app.Activity;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.apm.ApmLoaderHepler;
import com.jd.oa.abilities.apm.BuglyProLoader;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MeFlutterLoggingHandler implements IJDFChannelHandler {
    private static final String TAG = "MeFlutterLoggingHandler";
    public static final String SERVICE_LOGGING = "com.jdme.flutter/service/log";

    public static final String LEVEL_ERROR = "ERROR";
    public static final String LEVEL_WARN = "WARN";
    public static final String LEVEL_INFO = "INFO";
    public static final String LEVEL_FINE = "FINE";

    public static final String LEVEL_DEBUG = "DEBUG";

    @Override
    public String getModuleName() {
        return SERVICE_LOGGING;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, IJDFMessageResult<Map> resultMap) {
        final Activity activity = AppBase.getTopActivity();
        switch (methodName) {
            case "log":
                log(activity, map, resultMap);
                break;
            default:
                resultMap.notImplemented();
                break;
        }
    }

    /**
     *   var params = <String,dynamic> {
     *     "tag": tag,
     *     "time": time,
     *     "level" : level,
     *     "message" : message,
     *     "detail": detail,
     *    };
     */
    private void log(Activity activity, Map<String, Object> map, IJDFMessageResult<Map> resultMap) {
        String tag = (String) map.get("tag");
        long time = (long) map.get("time");
        String level = (String) map.get("level");
        String message = (String) map.get("message");
        String detail = (String) map.get("detail");
        Object stackTrace = map.get("stackTrace");

        String msg = "Level: " + level + ", tag: " + tag + ", time: " + time + ", msg: " + message + (detail == null ? "" : ", detail: " + detail);
        if (LEVEL_ERROR.equals(level)) {
            MELogUtil.onlineE(MELogUtil.TAG_FLUTTER, msg);
            MELogUtil.localE(MELogUtil.TAG_FLUTTER, msg + (stackTrace != null ? JSON.toJSONString(stackTrace) : ""));
            Exception exception = new FlutterException(msg);
            if (stackTrace != null) {
                fillStackTrace(exception, stackTrace);
            }
            BuglyProLoader buglyLoader = new BuglyProLoader(AppBase.getAppContext(), ApmLoaderHepler.getInstance(AppBase.getAppContext()).getConfigModel());
            buglyLoader.upLoadException(exception);
        } else if (LEVEL_WARN.equals(level)) {
            MELogUtil.onlineW(MELogUtil.TAG_FLUTTER, msg);
            MELogUtil.localW(MELogUtil.TAG_FLUTTER, msg);
        } else if (LEVEL_INFO.equals(level)) {
            MELogUtil.onlineI(MELogUtil.TAG_FLUTTER, msg);
            MELogUtil.localI(MELogUtil.TAG_FLUTTER, msg);
        } else if (LEVEL_FINE.equals(level)) {
            MELogUtil.localI(MELogUtil.TAG_FLUTTER, msg);
        } else {
            MELogUtil.localD(MELogUtil.TAG_FLUTTER, msg);
        }

        Log.d(TAG, "flutter log: " + msg);

        Map<String, Object> result = new HashMap<>();
        result.put("code", "0");
        resultMap.success(result);
    }

    private void fillStackTrace(Exception exception, Object stackTrace) {
        List<Map<String,Object>> list = (List<Map<String, Object>>) stackTrace;
        StackTraceElement[] elements = new StackTraceElement[list.size()];
        for (int i = 0; i < list.size(); i++) {
            Map<String,Object> frame = list.get(i);
            FlutterStackTraceFrame stackTraceFrame = FlutterStackTraceFrame.fromMap(frame);

            StackTraceElement element = new StackTraceElement(
                    stackTraceFrame.className,
                    stackTraceFrame.method,
                    stackTraceFrame.packageScheme + ":" + stackTraceFrame.packageName + "/" + stackTraceFrame.packagePath,
                    stackTraceFrame.line
            );
            elements[i] = element;
        }
        exception.setStackTrace(elements);
    }
}

class FlutterException extends Exception {

    FlutterException() {}

    FlutterException(String message) {
        super(message);
    }

    FlutterException(String message, Throwable cause) {
        super(message, cause);
    }
}

class FlutterStackTraceFrame {
    int number;
    int column;
    int line;
    String packageScheme;
    String packageName;
    String packagePath;
    String className;
    String method;
    boolean isConstructor;

    public static FlutterStackTraceFrame fromMap(Map<String,Object> map) {
        FlutterStackTraceFrame stackTrace = new FlutterStackTraceFrame();
        stackTrace.number = (int) map.get("number");
        stackTrace.column = (int) map.get("column");
        stackTrace.line = (int) map.get("line");
        stackTrace.packageScheme = String.valueOf(map.get("packageScheme"));
        stackTrace.packageName = String.valueOf(map.get("package"));
        stackTrace.packagePath = String.valueOf(map.get("packagePath"));
        stackTrace.className = String.valueOf(map.get("className"));
        stackTrace.method = String.valueOf(map.get("method"));
        stackTrace.isConstructor = (boolean) map.get("isConstructor");
        return stackTrace;
    }
}