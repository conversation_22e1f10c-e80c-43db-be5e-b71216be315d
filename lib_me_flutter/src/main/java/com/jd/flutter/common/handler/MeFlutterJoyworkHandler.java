package com.jd.flutter.common.handler;

import android.app.Activity;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.model.WorkInfo;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.JoyWorkService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

public class MeFlutterJoyworkHandler implements IJDFChannelHandler {

    public static final String BUSINESS_JOYWORK = "com.jdfocus.flutter/business/joywork";

    private static final int STATUS_UN_FINISH = 1;
    private static final int STATUS_FINISH = 2;
    private static final String CODE_SUCCESS = "0";
    private static final String CODE_FAIL = "1";

    @Override
    public String getModuleName() {
        return BUSINESS_JOYWORK;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        switch (methodName) {
            case "finishWork": {
                finishWork(map, resultMap);
                break;
            }
            case "openWorkDetail": {
                openWorkDetail(map, resultMap);
                break;
            }
        }
    }

    private void finishWork(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing()) {
            resultMap.error("Illegal state", "Context is null or finishing, " + activity, null);
            return;
        }
        Gson gson = new Gson();
        String info = gson.toJson(map);
        WorkInfo workInfo = gson.fromJson(info, WorkInfo.class);

        JoyWorkService service = AppJoint.service(JoyWorkService.class);
        service.updateTaskStatus(activity, workInfo, new IServiceCallback<Void>() {
            @Override
            public void onResult(boolean success, @Nullable Void unused, @Nullable String error) {
                if (success) {
                    resultMap.success(toResultMap(CODE_SUCCESS, error, null));
                } else {
                    resultMap.success(toResultMap(CODE_FAIL, error, null));
                }
            }
        });
    }

    private void openWorkDetail(Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing()) {
            resultMap.error("Illegal state", "Context is null or finishing, " + activity, null);
            return;
        }
        FragmentActivity fragmentActivity;
        if (!(activity instanceof FragmentActivity)) {
            resultMap.error("Illegal state", "activity is not FragmentActivity " + activity, null);
            return;
        }
        fragmentActivity = (FragmentActivity) activity;

        Gson gson = new Gson();
        String info = gson.toJson(map);
        WorkInfo workInfo = gson.fromJson(info, WorkInfo.class);

        JoyWorkService service = AppJoint.service(JoyWorkService.class);
        service.openTaskDetailForResult(fragmentActivity, workInfo.getTaskId(), false, "joyday", new IServiceCallback<WorkInfo>() {
            @Override
            public void onResult(boolean success, @Nullable WorkInfo workInfo, @Nullable String error) {
                Map<String,Object> data= workInfo != null ? workInfo.toMap() : null;
                if (success) {
                    resultMap.success(toResultMap(CODE_SUCCESS, error, data));
                } else {
                    resultMap.success(toResultMap(CODE_FAIL, error, data));
                }
            }
        });
    }

    Map<String,Object> toResultMap(String code, String message, Map<String,Object> data) {
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("code", code);
        map.put("errorMessage", message);
        map.put("data", data);
        return map;
    }
}