package com.jd.flutter.common.handler;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.jd.flutter.common.RestartActivity;
import com.jd.oa.AppBase;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ToastUtils;
import com.jd.push.JDPushManager;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_JDME_CHINA;

public class MeFlutterAppHandler implements IJDFChannelHandler {
    public static final String SERVICE_APPLICATION = "com.jdme.flutter/service/application";
    public static final String BUSINESS_LOGIN = "com.jdme.flutter/business/login";

    public static final String METHOD_APP_FOREGROUND = "appForeground";

    public static final String METHOD_APP_BACKGROUND = "appBackground";
    public static final String METHOD_RESET_VERIFY_CODE = "resetVerifyCode";
    private final AppService appService = AppJoint.service(AppService.class);
    //向flutter发送quitApp已经不使用了
    //openSafari是iOS专用，不用实现

    @Override
    public String getModuleName() {
        return SERVICE_APPLICATION;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String tag = "";
        String msg = "";
        JSONObject jsonObject = new JSONObject();
        final Map<String, String> result = new HashMap<>();
        switch (methodName) {
            case "log":
                try {
                    tag = (String) map.get("tag");
                    msg = (String) map.get("msg");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                Log.d(tag, msg == null ? "me_flutter" : msg);
                break;
            case "quitApp"://强制踢出
                ToastUtils.showToast(msg);
                appService.logout();
                break;
            case "splitModeEnable"://是否是折叠屏幕
                Map<String, Boolean> splitModeMap = new HashMap<>();
                splitModeMap.put("splitModeEnable", (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())));
                resultMap.success(splitModeMap);
                break;
            case "getToken": //获取token信息
                try {
                    Map<String, String> token = new HashMap<>();
                    token.put("refreshToken", TokenManager.getInstance().getRefreshToken());
                    token.put("accessToken", TokenManager.getInstance().getAccessToken());
                    resultMap.success(token);
                } catch (Exception e) {
                    e.printStackTrace();
                    resultMap.error(methodName, null, null);
                }
                break;
            case "saveToken"://保存刷新的token信息
                try {
                    TokenManager.getInstance().storeToken(jsonObject.getString((String) map.get("refreshToken")), jsonObject.getString((String) map.get("accessToken")));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case "getRealName"://获取用户真实姓名
                Map<String, String> nameMap = new HashMap<>();
                nameMap.put("realName", PreferenceManager.UserInfo.getUserRealName());
                resultMap.success(nameMap);
                break;
            case "getTenantId"://获取租户ID
                result.put("tenantId", PreferenceManager.UserInfo.getTenantCode());
                resultMap.success(result);
                break;
            case "getAppConfig"://获取原生的ducc config
                String key = null;
                try {
                    key = (String) map.get("key");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (key == null) {
                    resultMap.error(methodName, null, null);
                    break;
                }
                String config = ConfigurationManager.get().getEntry(key, "");
                result.put("config", config);
                resultMap.success(result);
                break;
            case "showToast"://显示toast
                String text = null;
                try {
                    text = (String) map.get("text");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (text != null && !text.isEmpty()) {
                    ToastUtils.showToast(text);
                }
                resultMap.success(result);
                break;
            case "getAppId"://获取App ID
                String appId = PreferenceManager.UserInfo.getTimlineAppID();
                if (TextUtils.isEmpty(appId)) {
                    appId = APP_ID_JDME_CHINA;
                }
                result.put("type", "succeed");
                result.put("params", appId);
                resultMap.success(result);
                break;
            case "getTeamId":
                String teamId = PreferenceManager.UserInfo.getTeamId();
                if (TextUtils.isEmpty(teamId) && PreferenceManager.UserInfo.getLogin()) {
                    NetWorkManagerLogin.getMySelfInfo(null, null, new SimpleRequestCallback<String>() {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            JSONObject jsonObj;
                            try {
                                jsonObj = new JSONObject(info.result);
                                JSONObject content = jsonObj.getJSONObject("content");
                                JSONObject teamInfo = content.getJSONObject("teamInfo");
                                String teamId = teamInfo.optString("teamId");
                                if (!TextUtils.isEmpty(teamId)) {
                                    result.put("teamId", teamId);
                                    resultMap.success(result);
                                    PreferenceManager.UserInfo.setTeamId(teamId);
                                } else {
                                    resultMap.error(info.getErrorCode(), info.getErrorMessage(), null);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                resultMap.error("1", e.getMessage(), null);
                            }
                        }

                        @Override
                        public void onFailure(HttpException exception, String info) {
                            super.onFailure(exception, info);
                            resultMap.error("1", exception.getMessage(), null);
                        }
                    });
                } else {
                    result.put("teamId", teamId);
                    resultMap.success(result);
                }
                break;
            case "getUserId":
                result.put("userId", PreferenceManager.UserInfo.getUserId());
                resultMap.success(result);
                break;
            case "getUserAvatar":
                result.put("userAvatar", PreferenceManager.UserInfo.getUserCover());
                resultMap.success(result);
                break;
            case "getDeviceToken":
                result.put("deviceToken", JDPushManager.getDeviceToken(AppBase.getAppContext()));
                resultMap.success(result);
                break;
            case "getUserName":
                result.put("userName", PreferenceManager.UserInfo.getUserName());
                resultMap.success(result);
                break;
            case "restartApp": {
                Activity activity = AppBase.getTopActivity();
                if (activity == null) return;
                Intent intent = new Intent(activity, RestartActivity.class);
                activity.startActivity(intent);
                resultMap.success(result);
                break;
            }
            case "getABTestConfig" : {
                String configKey = null;
                try {
                    configKey = (String) map.get("key");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (configKey == null) {
                    resultMap.error(methodName, null, null);
                    break;
                }
                String value = ABTestManager.getInstance().getConfigByKey(configKey, "");
                result.put("config", value);
                resultMap.success(result);
                break;
            }
            case "getDeviceId":
                result.put("deviceId", DeviceUtil.getDeviceUniqueId());
                resultMap.success(result);
                break;
            default:
                resultMap.notImplemented();
                break;
        }
    }
}
