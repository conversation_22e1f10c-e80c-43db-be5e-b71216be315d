package com.jd.flutter.common.handler;

import static com.jd.flutter.common.JDFHelper.SAVED_SETTING;
import static com.jd.flutter.common.JDFHelper.SEND_ACTION;
import static com.jd.flutter.common.JDFHelper.UPDATE_CALENDAR;
import static com.jd.oa.fragment.BaseFragment.PARAMS;

import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.Utils2App;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import java.util.HashMap;
import java.util.Map;

public class MeFlutterCalendarHandler implements IJDFChannelHandler {

    public static final String BUSINESS_CALENDAR = "com.jdme.flutter/business/calendar";

    @Override
    public String getModuleName() {
        return BUSINESS_CALENDAR;
    }

    @Override
    public void onChannel(String moduleName, String methodName, Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String arg = "{}";
        try {
            arg = new Gson().toJson(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        switch (methodName) {
            case "updateCalendar"://日程创建、修改完成
                Intent intent = new Intent(UPDATE_CALENDAR);
                intent.putExtra(PARAMS, arg);
                LocalBroadcastManager.getInstance(Utils2App.getApp()).sendBroadcast(intent);
                resultMap.success(new HashMap<>());
                break;
            case "savedSetting":
                intent = new Intent(SAVED_SETTING);
                intent.putExtra(BaseFragment.PARAMS, arg);
                LocalBroadcastManager.getInstance(Utils2App.getApp()).sendBroadcast(intent);
                resultMap.success(new HashMap<>());
                break;
            case "sendAction":
                intent = new Intent(SEND_ACTION);
                intent.putExtra(PARAMS, arg);
                LocalBroadcastManager.getInstance(Utils2App.getApp()).sendBroadcast(intent);
                resultMap.success(new HashMap<>());
                break;
            default:
                resultMap.notImplemented();
                break;
        }
    }

}
