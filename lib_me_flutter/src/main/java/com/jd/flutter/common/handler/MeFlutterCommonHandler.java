package com.jd.flutter.common.handler;

import static com.jd.flutter.common.model.ToJson.CANCELED;
import static com.jd.flutter.common.model.ToJson.SUCCEED;
import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;
import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_SHARE;
import static com.jd.oa.router.DeepLink.CONTACTS;
import static com.jd.oa.utils.Utils.compatibleDeepLink;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.flutter.common.model.FlutterListResult;
import com.jd.flutter.common.model.FlutterStringResult;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.js.hybrid.JSStoragePreference;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.JoyNoteService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JDMAUtils;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFChannelHandler;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.Log;

@SuppressWarnings("rawtypes")
public class MeFlutterCommonHandler implements IJDFChannelHandler {
    public static final String SERVICE_COMMON = "com.jdme.flutter/service/common";
    private final FlutterListResult<Map<String,Object>> flutterListResult = new FlutterListResult<>();
    private static final int REQUEST_CODE_CONTACT_SELECT = 2;
    private IJDFMessageResult<Map> resultMap = null;

    private BroadcastReceiver mSelectedContactReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(this);
            ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) intent.getSerializableExtra("extra_contact");
            if (selected != null) {
                success(selected, resultMap);
            } else {
                fail(resultMap);
            }
        }
    };

    @Override
    public String getModuleName() {
        return SERVICE_COMMON;
    }

    @Override
    public void onChannel(String moduleName, String methodName, @NonNull Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String arg = new Gson().toJson(map);
        Log.i("onMethodCall=================", "" + arg);
        final Activity activity = AppBase.getTopActivity();
        switch (methodName) {
            case "openContactSelector"://打开联系人选择器
                try {
                    ArrayList<MemberEntityJd> selected = new ArrayList<>();
                    ArrayList<MemberEntityJd> alternate = new ArrayList<>();
                    int maxNum = Integer.MAX_VALUE;
                    String title = "";
                    boolean externalContactEnable = false;
                    boolean optionalEnable = false;
                    boolean noParticipants = false;
                    String selectorTitle = "";

                    if (arg != null) {
                        JSONObject params = new JSONObject(arg);
                        maxNum = params.has("maxNum") ? params.getInt("maxNum") : Integer.MAX_VALUE;
                        title = params.has("title") ? params.getString("title") : "";
                        externalContactEnable = params.optBoolean("externalContactEnable", false);
                        JSONArray selectedArray = params.optJSONArray("selected");
                        optionalEnable = params.optBoolean("optionalEnable", false);
                        selectorTitle = params.optString("ddTitle");

                        if (!params.has("selected") && !params.has("alternate")) {
                            noParticipants = true;
                        }

                        if (selectedArray != null && selectedArray.length() > 0) {
                            selected.addAll(getMemberEntities(selectedArray));
                        } else {
                            noParticipants = true;
                        }

                        if (params.has("alternate")) {
                            selectedArray = params.optJSONArray("alternate");
                            if (selectedArray != null && selectedArray.length() > 0) {
                                alternate.addAll(getMemberEntities(selectedArray));
                            }
                        }
                    }
                    ArrayList<String> appIds = new ArrayList<>(getAppIds());
                    if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
                        resultMap.error(null, null, null);
                        return;
                    }
                    this.resultMap = resultMap;
                    Intent intent = Router.build(CONTACTS).getIntent(activity);
                    intent.putExtra("extra_contact", selected);
                    intent.putExtra("extra_alternate_contact", alternate);
                    intent.putExtra("title", title);
                    intent.putExtra("max", maxNum);
                    intent.putExtra("extra_external_contact_enable", externalContactEnable);
                    intent.putExtra("extra_broadcast", true);
                    intent.putStringArrayListExtra("extra_specify_appId",appIds);
                    intent.putExtra("extra_enable_optional", optionalEnable);
                    intent.putExtra("selector_title", selectorTitle);

                    if (optionalEnable) {
                        intent.putExtra("extra_close_on_result", false);
                    }

                    activity.startActivityForResult(intent, REQUEST_CODE_CONTACT_SELECT);
                    LocalBroadcastManager.getInstance(activity).registerReceiver(mSelectedContactReceiver, new IntentFilter("contact_selector_get_result"));
                } catch (Exception e) {
                    e.printStackTrace();
                    flutterListResult.type = CANCELED;
                    resultMap.success(flutterListResult.toMap());
                }
                break;
            case "openInfoPage"://打开个人信息
                try {
                    JSONObject jsonObject = new JSONObject(arg);
                    String erp = jsonObject.getString("erp");
                    String appId = jsonObject.getString("appId");
                    AppBase.iAppBase.showContactDetailInfo(activity, appId, erp);
                } catch (Exception e) {
                    e.printStackTrace();
                    fail(resultMap);
                }
                break;
            case "getGroupRoster"://群内新建日程，根据群ID获取群成员
                try {
                    JSONObject jsonObject = new JSONObject(arg);
                    AppJoint.service(ImDdService.class).getGroupRoster(jsonObject.getString("sessionId"), true, new LoadDataCallback<ArrayList<MemberEntityJd>>() {
                        @Override
                        public void onDataLoaded(ArrayList<MemberEntityJd> memberEntityJds) {
                            success(memberEntityJds, resultMap);
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            FlutterStringResult flutterStringResult = new FlutterStringResult();
                            flutterStringResult.params = String.valueOf(i);
                            resultMap.success(flutterStringResult.toMap());
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    fail(resultMap);
                }
                break;
//            case "updateCalendar"://日程创建、修改完成
//                Intent intent = new Intent(UPDATE_CALENDAR);
//                intent.putExtra("params", arg);
//                LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
//                break;
            case "goChat"://跳转聊天指定位置接口
                try {
                    /*
                     * 我的日程查看来源
                     * {
                     * {
                     * "chatType": 1,
                     * "content": {
                     * "content": "#E-a10",
                     * "decrypt": true,
                     * "type": 1
                     * },
                     * "msgId": "4006c5fbf8634fcfab27a5fb19039c86",
                     * "sender": {
                     * "app": "ee",
                     * "avatar": "http://img11.360buyimg.com/ee/jfs/t29272/334/835926044/1630075/7d00891d/5bff3be6N9218263b.jpg",
                     * "department": "京东集团-京东零售-技术与数据中台-成都研究院-移动研发部",
                     * "nickName": "唐侃",
                     * "position": "软件开发工程师岗",
                     * "uid": "tangkan"
                     * },
                     * "sessionId": "chenqizheng1:ee:tangkan:ee",
                     * "sessionName": "唐侃",
                     * "sessionType": 0,
                     * "timestamp": 1561464229702,
                     * "to": "chenqizheng1",
                     * "toApp": "ee"
                     * }
                     */
                    JSONObject jsonObject = new JSONObject(arg);
                    String sessionKey = jsonObject.getString("sessionId");
                    String to = jsonObject.optString("to");
                    int sessionType = jsonObject.optInt("sessionType");
                    String msgId = jsonObject.optString("msgId");
                    long mid = (long) jsonObject.optDouble("mid");
                    long timestamp = (long) jsonObject.optDouble("timestamp");
                    String content = null;
                    if (jsonObject.has("content")) {
                        content = jsonObject.getJSONObject("content").optString("content");
                    }
                    String checkExist = jsonObject.optString("checkExist");
                    boolean check = "1".equals(checkExist);

                    String toApp = jsonObject.optString("toApp", "");
                    ImDdService ddService = AppJoint.service(ImDdService.class);
                    if (ddService != null) {
                        String appId = ddService.safeAppId(sessionType, toApp);
                        boolean success = AppJoint.service(ImDdService.class).goChatActivity(activity, sessionKey, to, appId, msgId, mid, content, timestamp, sessionType, check);
                        if (!success) {
                            fail(resultMap);
//                    } else {
//                        flutterListResult.type = SUCCEED;
//                        result.success(flutterListResult.toString());
                        }                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    fail(resultMap);
                }
                break;
            case "openDeepLink"://跳转deepLink  日历
                try {
                    String dp = compatibleDeepLink(map);
                    if (dp != null) {
                        jumpToDeepLink(dp);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case "pushDocumentChoose": {
                pushDocumentChoose(map, resultMap);
                break;
            }

            case "openContactSelectorWitUnInvertSelecter": {
                // val service = AppJoint.service(ImDdService::class.java)
                //        val entity = MemberListEntityJd()
                //        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true).setConstantFilter(selected)
                //                .setShowSelf(true).setOptionalFilter(null).setShowOptionalFilter(false).setMaxNum(Int.MAX_VALUE)
                //
                try {
                    JSONObject params = new JSONObject(arg);
                    int maxNum = params.optInt("maxNum", Integer.MAX_VALUE);
                    String title = params.optString("title");
                    boolean externalContactEnable = params.optBoolean("externalContactEnable", false);
                    ArrayList<MemberEntityJd> invertArray = getMemberEntities(params.optJSONArray("invertArray"));
                    ArrayList<MemberEntityJd> unInvertArray = getMemberEntities(params.optJSONArray("unInvertArray"));

                    List<String> appIds = new ArrayList<>(getAppIds());

                    MemberListEntityJd entity = new MemberListEntityJd();
                    entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER)
                            .setShowConstantFilter(true)
                            .setConstantFilter(unInvertArray)
                            .setShowSelf(true)
                            .setOptionalFilter(invertArray)
                            .setShowOptionalFilter(false)
                            .setSpecifyAppId(appIds)
                            .setExternalDataEnable(externalContactEnable)
                            .setMaxNum(maxNum);
                    imDdService.gotoMemberList(activity, 101, entity, new Callback<ArrayList<MemberEntityJd>>() {
                        @Override
                        public void onSuccess(ArrayList<MemberEntityJd> bean) {
                            success(bean, resultMap);
                        }

                        @Override
                        public void onFail() {
                            fail(resultMap);
                        }
                    });
                } catch (Exception e) {
                    fail(resultMap);
                    e.printStackTrace();
                }
                break;
            }
            case "getUserInfo": {
                try {
                    JSONObject params = new JSONObject(arg);
                    String app = params.optString("app");
                    String pin = params.optString("pin");
                    imDdService.getContactInfo(app, pin, new Callback<MemberEntityJd>() {
                        @Override
                        public void onSuccess(MemberEntityJd bean) {
                            Map<String,Object> map = new HashMap<>();
                            map.put("type", SUCCEED);
                            Map<String,Object> info = new HashMap<>();
                            info.put("app", bean.getApp());
                            info.put("avatar", bean.getAvatar());
                            info.put("erp", bean.getId());
                            info.put("name", bean.getName());
                            info.put("email", bean.getEmail());
                            info.put("position", bean.getPosition());
                            info.put("external", bean.getExternalType());
                            map.put("params", info);
                            resultMap.success(map);
                        }

                        @Override
                        public void onFail() {
                            fail(resultMap);
                        }
                    });
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            }

            case "openTimlineMeeting": {
                JSONObject params = null;
                try {
                    params = new JSONObject(arg);
                    String meetingCode = params.optString("meetingCode");
                    String meetingId = params.optString("meetingId");
                    String password = params.optString("password");
                    String source = params.optString("source");
                    Long code = null;
                    if (!TextUtils.isEmpty(meetingCode) && TextUtils.isDigitsOnly(meetingCode)) {
                        code = Long.parseLong(meetingCode);
                        meetingService.joinMeeting(activity, meetingId, code, password, source, null);
                        Map<String, Boolean> result = new HashMap<>();
                        result.put("result", true);
                        resultMap.success(result);
                    } else {
                        resultMap.error("Failed", "meetingCode is illegal: " + meetingCode, null);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    resultMap.error("Failed", e.getMessage(), null);
                }
                break;
            }
            case "createGroup": {
                try {
                    JSONObject params = new JSONObject(arg);
                    String groupName = params.optString("groupName");
                    String sourceId = params.optString("sourceId");
                    String rKey = params.optString("rKey");
                    JSONArray members = params.optJSONArray("members");
                    ArrayList<MemberEntityJd> list = new ArrayList<>();
                    if (members != null) {
                        for (int i = 0; i < members.length(); i++) {
                            JSONObject object = members.getJSONObject(i);
                            MemberEntityJd memberEntityJd = new MemberEntityJd();
                            memberEntityJd.mApp = object.optString("app");
                            memberEntityJd.mId = object.optString("pin");
                            memberEntityJd.mName = object.optString("nickName");
                            list.add(memberEntityJd);
                        }
                    }

                    ImDdService ddService = AppJoint.service(ImDdService.class);
                    ddService.createGroup(activity, sourceId, rKey, list, ImDdService.GROUP_MODE_JD_INTERNAL, groupName, true, false, new LoadDataCallback<String>() {
                        @Override
                        public void onDataLoaded(String gid) {
                            Map<String, Object> result = new HashMap<>();
                            result.put("result", true);
                            result.put("gid", gid);
                            resultMap.success(result);
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            resultMap.error("Failed",  "" + s + ", code: " + i, null);
                        }
                    });
                } catch (JSONException e) {
                    e.printStackTrace();
                    resultMap.error("Failed",  e.getMessage(), e);
                }
                break;
            }

            case "joinGroup": {
                try {
                    JSONObject params = new JSONObject(arg);
                    String gid = params.optString("gid");
                    String code = params.optString("code");
                    ImDdService ddService = AppJoint.service(ImDdService.class);
                    ddService.joinGroup(gid, code, new LoadDataCallback<Void>() {
                        @Override
                        public void onDataLoaded(Void unused) {
                            Map<String, Boolean> result = new HashMap<>();
                            result.put("result", true);
                            resultMap.success(result);
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            resultMap.error("Failed",  "" + s + ", code: " + i, null);
                        }
                    });
                    String clickType = params.optString("clickType");
                    Map<String,String> eventParams = new HashMap<>();
                    eventParams.put("app", PreferenceManager.UserInfo.getTimlineAppID());
                    eventParams.put("user", PreferenceManager.UserInfo.getUserName());
                    eventParams.put("team", PreferenceManager.UserInfo.getTenantCode());
                    eventParams.put("groupid", gid);
                    eventParams.put("source", "flutter");
                    eventParams.put("clicked_type", clickType);
                    JDMAUtils.clickEvent(JDMAConstants.Mobile_Page_OpenAPI_IMApi, JDMAConstants.Mobile_Event_OpenAPI_IMApi_JoinGroup, eventParams);
                } catch (JSONException e) {
                    e.printStackTrace();
                    resultMap.error("Failed",  e.getMessage(), e);
                }
                break;
            }

            case "selectConversation": {
                JSONObject params = null;
                try {
                    params = new JSONObject(arg);
                    int max = params.optInt("max", 1024);

                    ImDdService imDdService = AppJoint.service(ImDdService.class);
                    List<String> appIds = getAppIds();
                    MemberListEntityJd entity = new MemberListEntityJd();
                    entity.setMaxNum(max)
                            .setSpecifyAppId(appIds)
                            .setFrom(TYPE_SHARE)
                            .setShowConstantFilter(true)
                            .setSelectMode(MemberListEntityJd.SELECT_MODE_SINGLE)
                            .setExternalDataEnable(false)
                            .setShowSelf(true);
                    imDdService.gotoMemberList(activity, REQUEST_CODE_SELECT, entity, new Callback<ArrayList<MemberEntityJd>>() {
                        @Override
                        public void onSuccess(ArrayList<MemberEntityJd> entities) {
                            Map<String, Object> result = new HashMap<>();

                            List<Map<String,Object>> list = new ArrayList<>();
                            for (MemberEntityJd entity : entities) {
                                Map<String,Object> item = new HashMap<>();
                                boolean isGroup = entity.getType() == MemberEntityJd.TYPE_GROUP;
                                item.put("isGroup", isGroup);
                                if (isGroup) {
                                    item.put("groupId", entity.getId());
                                } else if (entity.getType() == MemberEntityJd.TYPE_CONTACT) {
                                    item.put("ddAppId", entity.getApp());
                                    item.put("account", entity.getId());
                                }
                                list.add(item);
                            }

                            result.put("result", list);
                            resultMap.success(result);
                        }

                        @Override
                        public void onFail() {
                            resultMap.error("Failed", "", null);
                        }
                    });
                } catch (JSONException e) {
                    e.printStackTrace();
                    resultMap.error("Failed",  e.getMessage(), e);
                }
                break;
            }
            case "getCollaborativelyApps": {
                JSONObject params = null;
                try {
                    params = new JSONObject(arg);
                    Map<String, Object> result = new HashMap<>();
                    result.put("result", getAppIds());
                    resultMap.success(result);
                } catch (Exception e) {
                    e.printStackTrace();
                    resultMap.error("Failed",  e.getMessage(), e);
                }
                break;
            }

            case "getGroupInfo": {
                try {
                    JSONObject params = new JSONObject(arg);
                    String gid = params.getString("gid");

                    final Map<String, Object> result = new HashMap<>();
                    AppJoint.service(ImDdService.class).getGroupInfo(gid, new LoadDataCallback<GroupInfoEntity>() {
                        @Override
                        public void onDataLoaded(GroupInfoEntity groupInfoEntity) {
                            Map<String,String> groupInfo = new HashMap<>();

                            groupInfo.put("gid", groupInfoEntity.getGid());
                            groupInfo.put("name", groupInfoEntity.getName());
                            groupInfo.put("avatar", groupInfoEntity.getAvatar());

                            result.put("result", groupInfo);
                            resultMap.success(result);
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            Log.d("onGroupToCalendar", "onDataNotAvailable: " + s);
                            resultMap.error("Failed",  s, i);
                        }
                    });
                    String clickType = params.optString("clickType");
                    Map<String,String> eventParams = new HashMap<>();
                    eventParams.put("app", PreferenceManager.UserInfo.getTimlineAppID());
                    eventParams.put("user", PreferenceManager.UserInfo.getUserName());
                    eventParams.put("team", PreferenceManager.UserInfo.getTenantCode());
                    eventParams.put("groupid", gid);
                    eventParams.put("source", "flutter");
                    eventParams.put("clicked_type", clickType);

                    JDMAUtils.clickEvent(JDMAConstants.Mobile_Page_OpenAPI_IMApi, JDMAConstants.Mobile_Event_OpenAPI_IMApi_GetGroupInfo, eventParams);
                } catch (Exception e) {
                    e.printStackTrace();
                    resultMap.error("Failed",  e.getMessage(), e);
                }
                break;
            }
            case "openVideo": {
                String url = (String) map.get("url");
                String title = (String) map.get("title");
                JoyNoteService joyNoteService = AppJoint.service(JoyNoteService.class);
                if (activity != null) {
                    joyNoteService.openMePlayer(activity, url, title);
                }
            }
            default:
                resultMap.notImplemented();
                break;
        }
    }

    private ArrayList<MemberEntityJd> getMemberEntities(JSONArray array) throws JSONException {
        ArrayList<MemberEntityJd> list = new ArrayList<>();
        if (array == null || array.length() == 0) return list;
        for (int i = 0; i < array.length(); i++) {
            JSONObject user = array.getJSONObject(i);
            String userId = user.optString("erp");
            String appId = user.optString("app");
            String avatar = user.optString("avatar");
            String name = user.optString("name");
            int external = user.optInt("external", 0);
            String email = user.optString("email");
            String phone = user.optString("phone");
            boolean deletable = user.optBoolean("deletable", true);
            boolean optional = user.optBoolean("optional", false);

            MemberEntityJd entity = new MemberEntityJd();
            entity.mApp = appId;
            entity.mId = userId;
            entity.mName = name;
            entity.mAvatar = avatar;
            entity.mEmail = email;
            entity.mPhone = phone;
            entity.mExternalType = external;
            entity.mExternal = external != 0;

            if (entity.getExternalType() == MemberEntityJd.EXTERNAL_TYPE_EMAIL) {
                entity.mType = MemberEntityJd.TYPE_EMAIL;
            } else if (!entity.isExternal()) {
                entity.mType = MemberEntityJd.TYPE_CONTACT;
            }
            entity.setDeletable(deletable);
            entity.setOptional(optional);

            list.add(entity);
        }
        return list;
    }

    //打开joyspace文档选择器
    //copy from focus project
    @SuppressWarnings("unchecked")
    private void pushDocumentChoose(@NonNull Map<String, Object> map, final IJDFMessageResult<Map> resultMap) {
        String key = (String) map.get("KEY");
        HashMap<String, String> value = (HashMap<String, String>) map.get("VALUE");
        if (null != key && null != value) {
            Gson gson = new Gson();
            Type gsonType = new TypeToken<HashMap>() {
            }.getType();
            String gsonString = gson.toJson(value, gsonType);
            String val = "{\"obj\":" + gsonString + "}";
            JSStoragePreference.getInstance().put(key,val);
        }

        String url = (String) map.get("url");
        WebBean webBean = new WebBean();
        webBean.setUrl(url);
        webBean.setShowNav(WebConfig.H5_NATIVE_HEAD_HIDE);
        Activity activity = AppBase.getTopActivity();
        if (activity != null) {
            Intent intent = new Intent(activity, FunctionActivity.class);
            intent.putExtra(EXTRA_WEB_BEAN, webBean);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
            activity.startActivity(intent);
        }
        resultMap.success(new HashMap<>());
    }

    public void jumpToDeepLink(String deepLink) {
        Activity activity = AppBase.getTopActivity();
        if (activity != null) {
            if (deepLink.startsWith("http") || deepLink.startsWith("https")) {
                Router.build(DeepLink.webUrl(deepLink)).go(activity);
            } else {
                Router.build(deepLink).go(activity);
            }
        }
    }

    private void success(ArrayList<MemberEntityJd> bean, @NonNull final IJDFMessageResult<Map> resultMap) {
        flutterListResult.type = SUCCEED;
        flutterListResult.params.clear();
        for (MemberEntityJd memberEntityJd : bean) {
            Map<String,Object> contact = new HashMap<>();
            contact.put("app",memberEntityJd.mApp);
            contact.put("avatar",memberEntityJd.mAvatar);
            contact.put("erp",memberEntityJd.mId);
            contact.put("name",memberEntityJd.mName);
            contact.put("email",memberEntityJd.mEmail);
            contact.put("phone",memberEntityJd.mPhone);
            contact.put("external",memberEntityJd.getExternalType());
            contact.put("optional", memberEntityJd.isOptional());
            flutterListResult.params.add(contact);
        }
        try {
            resultMap.success(flutterListResult.toMap());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void fail(@NonNull final IJDFMessageResult<Map> resultMap) {
        flutterListResult.type = CANCELED;
        try {
            resultMap.success(flutterListResult.toMap());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static final int REQUEST_CODE_SELECT = 1;
    private final ImDdService imDdService = AppJoint.service(ImDdService.class);
    private final JdMeetingService meetingService = AppJoint.service(JdMeetingService.class);

    private void selectContacts(Activity activity, int max, boolean externalContactEnable, Callback<ArrayList<MemberEntityJd>> callback) {
        List<String> appIds = new ArrayList<>(getAppIds());
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setMaxNum(max).setSpecifyAppId(appIds);
        entity.setFrom(TYPE_ADD_MEMBER).setShowConstantFilter(true).setShowSelf(true);
        entity.setExternalDataEnable(externalContactEnable);
        imDdService.gotoMemberList(activity, REQUEST_CODE_SELECT, entity, callback);
    }

    private List<String> getAppIds() {
        return TenantConfigBiz.INSTANCE.getCollaborativelyApps();
    }

    @SuppressWarnings("unchecked")
    public void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, Intent intent) {
        if (resultMap == null) {
            return;
        }
        if (activity.isDestroyed() || activity.isFinishing()) {
            resultMap.success(new HashMap<>());
            resultMap = null;
            return;
        }
        if (requestCode == REQUEST_CODE_CONTACT_SELECT) {
            if (resultCode == Activity.RESULT_OK && intent != null) {
                ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) intent.getSerializableExtra("extra_contact");
                if (selected != null) {
                    success(selected, resultMap);
                }
            } else {
                fail(resultMap);
            }
            LocalBroadcastManager.getInstance(activity).unregisterReceiver(mSelectedContactReceiver);
            resultMap = null;
        }
    }
}
