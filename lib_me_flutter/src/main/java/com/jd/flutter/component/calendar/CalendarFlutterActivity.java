package com.jd.flutter.component.calendar;

import static com.jd.flutter.common.JDFHelper.callFlutter;
import static com.jd.flutter.common.handler.MeFlutterDeepLinkHandler.SERVICE_DEEP_LINK;
import static com.jd.oa.router.DeepLink.CALENDAR_COLOR;
import static com.jd.oa.router.DeepLink.CALENDAR_CREATE;
import static com.jd.oa.router.DeepLink.CALENDAR_EDIT;
import static com.jd.oa.router.DeepLink.CALENDAR_SUBSCRIBE;
import static com.jd.oa.router.DeepLink.CALENDER_OTHER_PAGE;
import static com.jd.oa.router.DeepLink.CALENDER_OTHER_PAGE_V2;
import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;
import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE_V2;
import static com.jd.oa.router.DeepLink.CALENDER_SETTING;
import static com.jd.oa.router.DeepLink.CALENDER_SETTING_V2;
import static com.jd.oa.router.DeepLink.FLUTTER_CALENDAR_V2;
import static com.jd.oa.router.DeepLink.replaceV2;

import android.net.Uri;
import android.os.Bundle;

import androidx.appcompat.app.ActionBar;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.flutter.common.JDFHelper;
import com.jd.oa.BaseActivity;
import com.jdshare.jdf_container_plugin.components.channel.protocol.IJDFMessageResult;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Route({CALENDER_OTHER_PAGE,
        CALENDER_OTHER_PAGE_V2,
        CALENDER_SCHEDULE,
        CALENDER_SCHEDULE + "/{action}",
        CALENDER_SCHEDULE_V2,
        CALENDER_SCHEDULE_V2 + "/{action}",
        CALENDER_SETTING,
        CALENDER_SETTING_V2,
        CALENDAR_CREATE,
        CALENDAR_EDIT,
        CALENDAR_COLOR,
        CALENDAR_SUBSCRIBE,
})
public class CalendarFlutterActivity extends BaseActivity {

    String routeTag;
    String msg;
    String deepLink;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //CommonUtils.initStatusBar(this, getResources().getColor(R.color.white));

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }

        Map<String, Object> intentMap = new HashMap<>();
        intentMap.put("deeplink", getRouter());
        //noinspection rawtypes
        callFlutter(SERVICE_DEEP_LINK, "parseDeeplink", intentMap, new IJDFMessageResult<Map>() {
            @Override
            public void success(Map map) {
                String route = (String) map.get("route");
                @SuppressWarnings("unchecked")
                Map<String, Object> arguments = (Map<String, Object>) map.get("arguments");
                JDFHelper.openFlutterPage(CalendarFlutterActivity.this, route, arguments, -1);
                finish();
            }

            @Override
            public void error(String s, String s1, Object o) {
                finish();
            }

            @Override
            public void notImplemented() {
                finish();
            }
        });
    }

    private String getRouter() {
        Bundle bundle = getIntent().getExtras();
        String from = null;
        if (bundle != null) {
            routeTag = bundle.getString("routeTag");
            deepLink = bundle.getString(Router.RAW_URI);
        }
        if (routeTag == null) {
            if (deepLink != null) {
                return replaceV2(deepLink);
            }
            return FLUTTER_CALENDAR_V2;
        }

        JSONObject jsonObject = new JSONObject();
        try {
            if (bundle != null) {
                msg = bundle.getString("msg");
                if (msg != null) {
                    jsonObject.put("info", new JSONObject(msg));
                }
                from = bundle.getString("from", null);

                Set<String> keys = bundle.keySet();
                for (String key : keys) {
                    try {
                        jsonObject.put(key, JSONObject.wrap(bundle.get(key)));
                    } catch(JSONException e) {
                        //Handle exception here
                    }
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String result = CALENDER_SCHEDULE_V2 + "/create?mparam=" + Uri.encode(jsonObject.toString()) + "&maction=" + routeTag;
        if (from != null) {
            result += "&from=" + from;
        }
        return result;
    }
}
