<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="Theme.notAnimation" parent="@style/jdme_AppTheme_Defalut">
        <item name="android:windowAnimationStyle">@style/notAnimation</item>
        <item name="android:windowBackground">@color/translucent</item>
    </style>

    <style name="notAnimation" parent="@style/jdme_AppTheme_Defalut">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>

    <style name="TransparentTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@android:color/white</item>
        <!--设置窗口的背景是否为半透明，设置透明背景必须要设置此项-->
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="RestartTransparentTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>
</resources>
