<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

<!--    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />-->
    <uses-permission android:name="android.permission.VIBRATE" />

    <application>

        <activity
            android:name=".common.MeFlutterContainerActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode|screenLayout|smallestScreenSize"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".component.calendar.CalendarFlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode|screenLayout|smallestScreenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:windowSoftInputMode="adjustResize" />

<!--        <receiver android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.BOOT_COMPLETED" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

        <activity android:name=".common.RestartActivity"
            android:theme="@style/RestartTransparentTheme">

        </activity>
    </application>

</manifest>