// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.6.21'

    configurations.configureEach {
        resolutionStrategy {
            cacheChangingModulesFor 1, 'minutes'
            cacheDynamicVersionsFor 1, 'minutes'
        }
    }
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        google()
        mavenLocal()
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
//        jcenter()

        maven { url 'https://plugins.gradle.org/m2/' }
        maven { url "https://artifactory.jd.com/libs-snapshots-local/" }
        maven { url "https://artifactory.jd.com/libs-releases-local/" }
        maven {
            allowInsecureProtocol = true
            url 'http://artifactory.jd.com/libs-snapshots-local/'
        }  // For JDVApp
        maven {
            allowInsecureProtocol = true
            url "http://artifactory.jd.com/libs-releases-local/"
        }
        maven { url "https://www.jitpack.io" }
        maven {
            allowInsecureProtocol = true
            url 'http://download.flutter.io'
        }
        // 配置HMS Core SDK的Maven仓地址。
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://developer.hihonor.com/repo/' }//荣耀仓库
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://developer.hihonor.com/repo/' }//荣耀仓库
    }

    dependencies {
        classpath "com.android.tools.build:gradle:7.1.3"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jfrog.buildinfo:build-info-extractor-gradle:4.21.0"
        classpath 'com.huawei.agconnect:agcp:1.6.0.300'//华为agconnect插件
        classpath 'com.jd.jrapp.library:apm-gradle-plugin:1.1.0-20230801-541' //SGM性能监测依赖
        // jue
        classpath 'com.jdd.android.gradle:jue-plugin:1.1.3.2'

        classpath "com.chenenyu.router:gradle-plugin:1.8.3-beta1"
    }
}

allprojects {
    configurations.configureEach {
        resolutionStrategy {
            cacheChangingModulesFor 1, 'minutes'
            cacheDynamicVersionsFor 1, 'minutes'
        }
    }
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
//        jcenter()

        maven { url "https://www.jitpack.io" }
        maven {
            allowInsecureProtocol = true
            url "http://artifactory.jd.com/libs-snapshots-local"
        }  // TODO : For JDVApp
        maven {
            allowInsecureProtocol = true
            url "http://artifactory.jd.com/libs-releases-local"
        }
        maven {
            allowInsecureProtocol = true
            url "http://artifactory.jd.com/libs-snapshots"
        }
        maven {
            allowInsecureProtocol = true
            url "http://artifactory.jd.com/libs-releases"
        }
        maven { url "https://artifactory.jd.com/libs-snapshots-local/" }
        maven { url "https://artifactory.jd.com/libs-releases-local/" }
        maven {
            allowInsecureProtocol = true
            url 'http://download.flutter.io'
        }

        // 配置HMS Core SDK的Maven仓地址。
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://developer.hihonor.com/repo/' }//荣耀仓库
        maven { url 'https://repo1.maven.org/maven2/' }

        maven { url 'https://developer.hihonor.com/repo/' }//荣耀仓库
    }

    // 为所有模块设置Maven坐标，用于includeBuild依赖重定向
    group = 'com.jd.oa'
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}

//打印项目所有依赖项
// ./gradlew allDependencies > dependencies.log
subprojects {
    task allDependencies(type: DependencyReportTask) {}
}


apply from: "config_unity.gradle"
apply from: "config_me.gradle"
apply from: "config_saas.gradle"
apply from: 'config_artifactory.gradle'

if (timlineDebug.toBoolean()) {
    apply from: "config_timline.gradle"
}
