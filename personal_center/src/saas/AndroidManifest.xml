<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        tools:replace="android:supportsRtl,android:allowBackup,android:label,android:theme"
        android:allowBackup="false"
        android:supportsRtl="true"
        android:label="personal_center"
        android:name="com.jd.oa.business.PersonalCenterApp"
        android:theme="@style/MEWhiteTheme">
        <activity android:name="com.jd.oa.business.ui.PersonalCenterLaunchActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop">
        </activity>
        <activity android:name="com.jd.oa.business.LoginActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <meta-data
            android:name="FLAVOR"
            android:value="${FLAVOR}" />
    </application>
</manifest>