<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="26dp"
    android:layout_marginBottom="26dp"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:id="@+id/ll_add_account">
    
    <RelativeLayout
        android:id="@+id/rl_add_account"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        android:background="@drawable/jdsaas_personal_center_add_account_bg">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_personal_center_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_add"
            android:textColor="@color/jdsaas_black_6A6A6A"
            android:textSize="@dimen/JMEIcon_18" />
    </RelativeLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/personal_center_add_account"
        android:textSize="@dimen/jdsaas_text_size_11"
        android:textColor="@color/jdsaas_black_6A6A6A"
        android:layout_marginTop="6dp"/>
</LinearLayout>