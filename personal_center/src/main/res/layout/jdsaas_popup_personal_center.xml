<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource"
    android:paddingStart="10dp">
    
    <ImageView
        android:id="@+id/iv_popup_arrow"
        android:layout_width="16dp"
        android:layout_height="8dp"
        android:src="@drawable/jdsaas_popup_arrow_white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginLeft="12dp"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/fl_personal_center"
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_popup_arrow"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp">

        <com.jd.oa.business.ui.PersonalCenterLayout
            android:id="@+id/personal_center_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="13dp" />
    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>