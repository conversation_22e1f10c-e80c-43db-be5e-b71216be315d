<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="34dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/personal_center_add_account_tip"
        android:textSize="@dimen/jdsaas_text_size_12"
        android:textColor="@color/jdsaas_black_6A6A6A"
        android:layout_marginStart="16dp" />

    <LinearLayout
        android:id="@+id/ll_add_account"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/jdsaas_personal_center_user_item_bg">
        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/jdsaas_personal_center_add_account"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="@string/personal_center_add_account"
            android:textSize="@dimen/jdsaas_text_size_16"
            android:textColor="@color/jdsaas_black_9D9D9D"/>

    </LinearLayout>


</LinearLayout>