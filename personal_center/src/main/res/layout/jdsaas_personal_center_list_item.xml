<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:gravity="center_vertical">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:includeFontPadding="false"
        tools:text="@string/icon_prompt_add"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:textSize="@dimen/JMEIcon_20" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/personal_center_profile"
        android:textSize="@dimen/jdsaas_text_size_16"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:layout_marginLeft="12dp"/>

    <com.jd.oa.badge.RedDotView
        android:id="@+id/rdv_badge"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginLeft="8dp"
        android:visibility="gone"/>

</LinearLayout>