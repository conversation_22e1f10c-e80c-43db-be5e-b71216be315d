<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/jdsaas_team_user_dialog_bg"
    android:orientation="vertical">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_dialog_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_close"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:textSize="@dimen/JMEIcon_20"
        android:layout_marginRight="20dp"
        android:paddingTop="12dp"
        android:paddingLeft="16dp"/>
    <LinearLayout
        android:id="@+id/ll_dialog_team_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_gravity="center_horizontal">
        <com.jd.oa.ui.CircleImageView
            android:id="@+id/iv_dialog_user_avatar"
            android:layout_width="32dp"
            android:layout_height="32dp"
            tools:src="@drawable/jdsaas_login_logo" />
        <TextView
            android:id="@+id/tv_dialog_team_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="北京麦田科技有限公司"
            android:textSize="@dimen/jdsaas_text_size_14"
            android:textColor="@color/jdsaas_black_1B1B1B"
            android:layout_marginLeft="8dp"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_dialog_user"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp" />

</LinearLayout>