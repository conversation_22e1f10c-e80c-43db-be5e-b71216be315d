<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="24dp">
    
    <View
        android:id="@+id/view_indicator_line"
        android:layout_width="3dp"
        android:layout_height="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_personal_center_team_avatar"
        app:layout_constraintBottom_toBottomOf="@+id/iv_personal_center_team_avatar"
        android:background="@drawable/jdsaas_personal_center_team_indicator_bg"/>
    
    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_personal_center_team_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        tools:src="@drawable/jdsaas_login_logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_personal_center_team_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="京东"
        android:textSize="@dimen/jdsaas_text_size_11"
        android:textColor="@color/jdsaas_black"
        android:layout_marginTop="6dp"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@+id/iv_personal_center_team_avatar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>