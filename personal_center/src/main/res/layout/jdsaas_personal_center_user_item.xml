<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginBottom="12dp"
    android:background="@drawable/jdsaas_personal_center_user_item_bg">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_user_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/jdsaas_personal_default_avatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginLeft="12dp"/>
    <TextView
        android:id="@+id/tv_user_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="姓名姓名姓名"
        android:textSize="@dimen/jdsaas_text_size_16"
        android:textColor="@color/jdsaas_black_1B1B1B"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_user_avatar"
        app:layout_constraintRight_toLeftOf="@+id/fl_user_state"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"/>

    <FrameLayout
        android:id="@+id/fl_user_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="12dp">
        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_user_login_currently"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_check"
            android:textColor="@color/jdsaas_color_red_F63218"
            android:textSize="@dimen/JMEIcon_16"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_user_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:background="@drawable/jdsaas_tv_team_status_unactivated"
            tools:text="@string/jdsaas_login_unactivated"
            tools:textColor="@color/jdsaas_color_blue_4A5FE8"
            android:textSize="@dimen/jdsaas_text_size_12"
            android:visibility="gone"
            tools:visibility="visible"/>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>