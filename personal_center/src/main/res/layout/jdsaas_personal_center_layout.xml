<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/jdsaas_black_F0F1F2">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_personal_center_team"
        android:layout_width="70dp"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_personal_center"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toRightOf="@+id/rv_personal_center_team"
        app:layout_constraintRight_toRightOf="parent"
        tools:background="@drawable/jdsaas_personal_center_left_bg">
        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_personal_center_avatar"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginTop="16dp"
            android:layout_marginLeft="20dp"
            android:src="@drawable/jdsaas_personal_default_avatar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginRight="20dp"
            android:gravity="end"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/iv_personal_center_avatar"
            app:layout_constraintTop_toTopOf="@+id/iv_personal_center_avatar">

            <LinearLayout
                android:id="@+id/ll_personal_center_feeling"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:paddingTop="6dp"
                android:paddingBottom="6dp"
                android:orientation="horizontal"
                android:background="@drawable/jdsaas_personal_center_feeling_bg">
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/iv_personal_center_feeling_add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/icon_prompt_add"
                    android:textColor="@color/jdsaas_black_666666"
                    android:textSize="@dimen/JMEIcon_12" />

                <ImageView
                    android:id="@+id/iv_personal_center_feeling"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginEnd="2dp"
                    android:visibility="gone"/>
                <TextView
                    android:id="@+id/tv_personal_center_feeling"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/personal_center_feeling"
                    android:textSize="@dimen/jdsaas_text_size_12"
                    android:textColor="@color/jdsaas_black_666666"
                    android:maxLines="1"
                    android:ellipsize="end"/>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_personal_center_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="14dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            app:layout_constraintTop_toBottomOf="@+id/iv_personal_center_avatar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">
            <TextView
                android:id="@+id/tv_personal_center_name"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                tools:text="王明明明明明明明明明明明明明明明明明"
                android:textSize="@dimen/jdsaas_text_size_20"
                android:textColor="@color/jdsaas_black_1B1B1B"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_personal_center_name_arrow_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_direction_right"
                android:textColor="@color/jdsaas_black_9D9D9D"
                android:textSize="@dimen/JMEIcon_16" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_personal_center_company"
            android:layout_width="match_parent"
            tools:text="北京京东世纪贸易有限公司北京京东世纪北京京东世纪贸易有限公司"
            android:textSize="@dimen/jdsaas_text_size_11"
            android:textColor="@color/jdsaas_black_9D9D9D"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/ll_personal_center_name"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginTop="4dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:visibility="gone"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_personal_center_signature_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_general_edit"
            android:textColor="@color/jdsaas_black_666666"
            android:textSize="@dimen/JMEIcon_16"
            android:layout_marginTop="12dp"
            android:layout_marginLeft="20dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_personal_center_company"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <TextView
            android:id="@+id/tv_personal_center_signature"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/personal_center_signature_hint"
            android:textSize="@dimen/jdsaas_text_size_12"
            android:textColor="@color/jdsaas_black_6A6A6A"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="20dp"
            app:layout_constraintTop_toTopOf="@+id/tv_personal_center_signature_icon"
            app:layout_constraintBottom_toBottomOf="@+id/tv_personal_center_signature_icon"
            app:layout_constraintLeft_toRightOf="@+id/tv_personal_center_signature_icon"
            app:layout_constraintRight_toRightOf="parent"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_personal_center_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="38dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_personal_center_signature" />


        <LinearLayout
            android:id="@+id/ll_personal_center_switch"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@drawable/jdsaas_personal_center_switch_bg"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginBottom="38dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp">
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_general_switch"
                android:textColor="@color/jdsaas_black"
                android:textSize="@dimen/JMEIcon_16"
                app:layout_constraintTop_toBottomOf="@+id/tv_personal_center_company"
                app:layout_constraintLeft_toLeftOf="parent"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/personal_center_switch_account"
                android:textSize="@dimen/jdsaas_text_size_12"
                android:textColor="@color/jdsaas_black_1B1B1B"
                android:layout_marginLeft="4dp"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>