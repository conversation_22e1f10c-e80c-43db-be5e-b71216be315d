package com.jd.oa.business.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSONObject
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.MyPlatform
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamData
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.util.PersonalCenterJDMAUtil
import com.jd.oa.configuration.local.LocalConfigHelper
import com.jd.oa.configuration.local.model.UserCenterModel
import com.jd.oa.im.listener.Callback
import com.jd.oa.im.listener.Callback3
import com.jd.oa.model.service.JdMeetingService
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.JdSaasLoginPreference
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.theme.manager.Constants
import com.jd.oa.utils.ImageLoader
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.ToastUtils
import com.jdsaas.personal_center.R
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.iv_personal_center_avatar
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.iv_personal_center_feeling
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.iv_personal_center_feeling_add
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.ll_personal_center_feeling
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.ll_personal_center_name
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.ll_personal_center_switch
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.rv_personal_center_list
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.rv_personal_center_team
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.tv_personal_center_company
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.tv_personal_center_feeling
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.tv_personal_center_name
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.tv_personal_center_signature
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.tv_personal_center_signature_icon

class PersonalCenterLayout(context: Context, attributeSet: AttributeSet) :
    FrameLayout(context, attributeSet),
    PersonalCenterTeamSection.AccountCenterTeamItemClickListener,
    PersonalCenterListItemSection.AccountCenterListItemClickListener {

    var mUserCenterModel : UserCenterModel?
    lateinit var mTeamData: TeamData
    var mItems = mutableMapOf<String, String>()
    var mView: View
    private val imDdService = AppJoint.service(ImDdService::class.java)
    var personalCenterListItemSection: PersonalCenterListItemSection? = null
    var viewModel: PersonalCenterViewModel? = null
    var listener: OnPersonalCenterLayoutListener? = null

    init {
        mUserCenterModel = LocalConfigHelper.getInstance(context).userCenterConfig
        registerThemeChangeReceiver()
        initData()
        mView = LayoutInflater.from(context).inflate(R.layout.jdsaas_personal_center_layout, this)
        initView()
    }

    //主题换肤变化监听，更新头像上方小红点颜色
    private fun registerThemeChangeReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(Constants.ACTION_CHANGE_THEME)
        LocalBroadcastManager.getInstance(context)
            .registerReceiver(object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    val imDdService = AppJoint.service(ImDdService::class.java)
                    imDdService.updateCurrentUserInfo()
                }
            }, intentFilter)
    }

    private fun initData() {
        if (mUserCenterModel?.tenantInfo?:false) {//租户信息开关
            val teamDataJsonStr =
                JdSaasLoginPreference.get(JdSaasLoginPreference.KV_ENTITY_TEAM_DATA)
            mTeamData = JSONObject.parseObject(teamDataJsonStr, TeamData::class.java)

            mItems.putIfAbsent(
                context.getString(R.string.icon_me_businesscard),
                context.getString(R.string.personal_center_profile)
            )
            if (mUserCenterModel?.themeSkin?:false) {
                mItems.putIfAbsent(
                    context.getString(R.string.icon_edit_cellbackgroundcolor),
                    context.getString(R.string.personal_center_themes)
                )
            }
            if(mUserCenterModel?.feedback?:false){
                mItems.putIfAbsent(
                    context.getString(R.string.icon_help_service),
                    context.getString(R.string.personal_center_customer_service)
                )
            }
            mItems.putIfAbsent(
                context.getString(R.string.icon_general_set),
                context.getString(R.string.personal_center_setting)
            )
        } else {
            mItems.putIfAbsent(
                context.getString(R.string.icon_me_businesscard),
                context.getString(R.string.personal_center_profile)
            )
            mItems.putIfAbsent(
                context.getString(R.string.icon_edit_cellbackgroundcolor),
                context.getString(R.string.personal_center_themes)
            )
            // 是否是京东国内租户
            if (PreferenceManager.UserInfo.getTimlineAppID() == MultiAppConstant.APPID) {
                mItems.putIfAbsent(
                    context.getString(R.string.icon_joysky_manage),
                    context.getString(R.string.personal_center_customer_service)
                )
            }
            mItems.putIfAbsent(
                context.getString(R.string.icon_general_set),
                context.getString(R.string.personal_center_setting)
            )
        }
    }

    private fun initView() {
        mView.iv_personal_center_avatar.setOnClickListener {
            toExperienceSelfInfoFragment()
        }
        mView.ll_personal_center_name.setOnClickListener {
            toExperienceSelfInfoFragment()
        }

        //签名
        if(!(mUserCenterModel?.sign?:false)){
            mView.tv_personal_center_signature_icon.visibility = View.GONE
            mView.tv_personal_center_signature.visibility = View.GONE
        }
        mView.tv_personal_center_signature.setOnClickListener {
            val hint = context.getString(R.string.personal_center_signature_hint)
            val title = context.getString(R.string.personal_center_signature_title)
            imDdService?.openSignatureEdit(
                context as AppCompatActivity, title, hint,
                object : Callback<CharSequence> {
                    override fun onSuccess(formatSignature: CharSequence) {
                        if (!TextUtils.isEmpty(formatSignature)) {
                            tv_personal_center_signature.setText(formatSignature.toString())
                        } else {
                            tv_personal_center_signature.setText(hint)
                        }
                    }

                    override fun onFail() {}
                })
            PersonalCenterJDMAUtil.onPersonalCenterEventClick(PersonalCenterJDMAUtil.TOPME_LIST_SIGNATURE)
            listener?.close()
        }

        //状态
        if(mUserCenterModel?.sign?:false){
            mView.ll_personal_center_feeling.visibility = View.VISIBLE
            mView.ll_personal_center_feeling.setOnClickListener {
                imDdService?.openSetUserStatus(context)
                PersonalCenterJDMAUtil.onPersonalCenterEventClick(PersonalCenterJDMAUtil.TOPME_LIST_STATUS)
                listener?.close()
            }
            //update feeling
            imDdService?.registerUserStatusChangeListener(
                PreferenceManager.UserInfo.getUserName(),
                javaClass.simpleName,
                object : Callback3<String?> {
                    override fun onSuccess(icon: String?, title: String?, titleEn: String?) {
                        if (TextUtils.isEmpty(icon) && TextUtils.isEmpty(title) && TextUtils.isEmpty(titleEn)) { //无状态
                            val feeling = context.getString(R.string.personal_center_feeling)
                            mView.iv_personal_center_feeling.visibility = View.GONE
                            mView.iv_personal_center_feeling_add.visibility = View.VISIBLE
                            mView.tv_personal_center_feeling.setText(feeling)
                        } else {
                            mView.iv_personal_center_feeling.visibility = View.VISIBLE
                            mView.iv_personal_center_feeling_add.visibility = View.GONE
                            ImageLoader.load(context, mView.iv_personal_center_feeling, icon)
                            if (TextUtils.equals(
                                    LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()),
                                    "zh_CN")) {
                                mView.tv_personal_center_feeling.setText(title)
                            } else {
                                mView.tv_personal_center_feeling.setText(titleEn)
                            }
                        }
                    }

                    override fun onFail(msg: String) {}
                }
            )
        }else{
            mView.ll_personal_center_feeling.visibility = View.GONE
        }

        mView.rv_personal_center_list.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        val sectionedAdapter = SectionedRecyclerViewAdapter()
        personalCenterListItemSection = PersonalCenterListItemSection(context, mItems, this)
        sectionedAdapter.addSection(personalCenterListItemSection)
        mView.rv_personal_center_list.adapter = sectionedAdapter

        //切换账号
        mView.ll_personal_center_switch.visibility = if(mUserCenterModel?.changeAccount?:false) View.VISIBLE else View.GONE
        mView.ll_personal_center_switch.setOnClickListener {
            val teamAccountInfo = mTeamData.teamAccountInfoList?.find { it.loginCurrently }
            listener?.showPersonalCenterUserDialog(teamAccountInfo)
        }

        setTeamAccountList()
        setPersonalInfo()
    }

    //每次打开个人中心侧边栏刷新数据
    fun refreshData() {
        initData()
        setTeamAccountList()
        setPersonalInfo()
    }

    //个人信息设置
    private fun setPersonalInfo() {
        mView.tv_personal_center_name.setText(MyPlatform.getCurrentUser().realName)
        ImageLoader.load(
            context, mView.iv_personal_center_avatar,
            MyPlatform.getCurrentUser().userIcon,  R.drawable.jdme_profile_avatar_default
        )
        if (TextUtils.isEmpty(imDdService?.getMyFormatSignature())) {
            mView.tv_personal_center_signature.setText(context.getString(R.string.personal_center_signature_hint))
        } else {
            mView.tv_personal_center_signature.setText(
                imDdService?.getMyFormatSignature().toString()
            )
        }
        if (mUserCenterModel?.company?:false) {
            mView.tv_personal_center_company.visibility = View.VISIBLE
            mView.tv_personal_center_company.setText(
                PreferenceManager.UserInfo.getTeamName()
            )
        }
    }

    //saas租户列表设置
    private fun setTeamAccountList() {
        if ((mUserCenterModel?.tenantInfo?:false) && (mTeamData.teamAccountInfoList?.size?:0) > 1) {
            mView.rv_personal_center_team.visibility = View.VISIBLE
            mView.rv_personal_center_team.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            val teamSectionedAdapter = SectionedRecyclerViewAdapter()
            teamSectionedAdapter.addSection(
                PersonalCenterTeamSection(
                    context,
                    mTeamData.teamAccountInfoList,
                    this
                )
            )
            mView.rv_personal_center_team.adapter = teamSectionedAdapter
        }else{
            mView.rv_personal_center_team.visibility = View.GONE
        }
    }

    //跳转编辑资料页面
    private fun toExperienceSelfInfoFragment() {
        Router.build(DeepLink.EXP_SELF_INFO).go(context)
        PersonalCenterJDMAUtil.onPersonalCenterEventClick(PersonalCenterJDMAUtil.TOPME_LIST_PROFILE)

        listener?.close()
    }


    //个人中心回调接口
    interface OnPersonalCenterLayoutListener {
        fun close()
        fun showPersonalCenterUserDialog(teamAccountInfo: TeamAccountInfo?)
    }

    //租户点击
    override fun onAccountCenterTeamItemClick(teamAccountInfo: TeamAccountInfo?) {
        if (AppJoint.service(JdMeetingService::class.java).isMeetingInFloatMode) {
            ToastUtils.showCenterToast("您正在会议中，请先退出会议")
            return
        }
        if (!(teamAccountInfo?.loginCurrently?: false) && teamAccountInfo?.teamUserInfoList?.size == 1
        ) {//直接切换租户
            loginByPhone(teamAccountInfo)
        } else {
            listener?.showPersonalCenterUserDialog(teamAccountInfo)
        }
    }

    //默认切换租户手机号登录
    private fun loginByPhone(teamAccountInfo: TeamAccountInfo?) {
        val teamUserInfo: TeamUserInfo? = teamAccountInfo?.teamUserInfoList?.first()
        if (!(teamUserInfo?.loginCurrently ?: false)) {
            JdSaasLoginHelper.showSwitchAccountDialog(context) {
                AppJoint.service(JdMeetingService::class.java).signOut()//退出视频会议

                viewModel?.mTeamAccountInfo = teamAccountInfo
                viewModel?.mTeamUserInfo = teamUserInfo
                viewModel?.loginByPhoneAfterCheckToken()
            }
        }
    }

    //个人中心Item项点击
    override fun onAccountCenterListItemClick(name: String) {
        if (name == context.getString(R.string.personal_center_profile)) {
            val param = org.json.JSONObject()
            param.put("erp", MyPlatform.getCurrentUser().getUserName())
            val uri = Uri.parse(DeepLink.DD_USER_INFO).buildUpon()
            val deepLink =
                uri.appendQueryParameter(DeepLink.DEEPLINK_PARAM, param.toString()).toString()
            Router.build(deepLink).go(context)
            PersonalCenterJDMAUtil.onPersonalCenterEventClick(PersonalCenterJDMAUtil.TOPME_LIST_MYCARD)
        } else if (name == context.getString(R.string.personal_center_themes)) {
            Router.build(DeepLink.EXP_SKIN_THEME).go(context)
            PersonalCenterJDMAUtil.onPersonalCenterEventClick(PersonalCenterJDMAUtil.TOPME_LIST_THEMES)
        } else if (name == context.getString(R.string.personal_center_customer_service)) {
            Router.build(DeepLink.appCenter(MultiAppConstant.getFeedbackAppId(), null)).go(context)
            PersonalCenterJDMAUtil.onPersonalCenterEventClick(PersonalCenterJDMAUtil.TOPME_LIST_HELP)
        } else if (name == context.getString(R.string.personal_center_setting)) {
            Router.build(DeepLink.ACTIVITY_URI_SETTING).go(context)
            PersonalCenterJDMAUtil.onPersonalCenterEventClick(PersonalCenterJDMAUtil.TOPME_LIST_SETTINGS)
        }
        listener?.close()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        imDdService?.unregisterUserStatusChangeListener(javaClass.simpleName)
    }
}