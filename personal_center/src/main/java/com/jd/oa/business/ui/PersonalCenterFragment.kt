package com.jd.oa.business.ui

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.AppBase
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchActivity
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginDialog
import com.jd.oa.business.jdsaaslogin.util.LoginUtil
import com.jd.oa.business.util.PersonalCenterInjectorUtil
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.StatusBarConfig
import com.jdsaas.personal_center.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.cl_personal_center
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.rv_personal_center_team
import kotlinx.android.synthetic.main.jdsaas_popup_personal_center.view.personal_center_layout

class PersonalCenterFragment : BaseFragment(),
    PersonalCenterLayout.OnPersonalCenterLayoutListener {

    val viewModel by lazy {
        ViewModelProvider(this, PersonalCenterInjectorUtil.getPersonalCenterModelFactory()).get(
            PersonalCenterViewModel::class.java
        )
    }

    lateinit var mView: View
    var mOnDrawerCloseListener: (() -> Unit)? = null
    var personalCenterUserDialog: PersonalCenterUserDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        //验证码发送成功
        viewModel.mMsgCodeSendSuccessLiveData.observe(this) {
            toAccountSwitchActivity(it)
            //开启倒记时，切换租户时如果上次验证码有效，再次进入验证码页面显示当前记时
            viewModel.startMsgCodeExpireTimeCountDown(it)
        }
        //登录成功
        viewModel.mLoginSuccessLiveData.observe(this) {
            personalCenterUserDialog?.dismiss()
            mOnDrawerCloseListener?.invoke()
        }
        //验证码已发送
        viewModel.mMessageCodeAlreadySendLiveData.observe(this) {
            val expireTime = viewModel.mMsgCodeCountDownLiveData.value?.toInt()
            if (expireTime != null) {
                toAccountSwitchActivity(expireTime)
            }
        }
        //在其他设备登录
        viewModel.mOtherDeviceLoginLiveData.observe(this) {
            if (it == 0) {
                JdSaasLoginDialog.showOtherDeviceLoginDialog(requireActivity(), loginContinue = {
                    viewModel.loginByToken(1)
                }, loginCancel = {
                    personalCenterUserDialog?.dismiss()
                    mOnDrawerCloseListener?.invoke()
                })
            }
        }
    }

    //跳转账号切换页面
    private fun toAccountSwitchActivity(expireTime: Int) {
        val intent = Intent(AppBase.getAppContext(), AccountSwitchActivity::class.java)
        intent.putExtra(AccountSwitchActivity.MSG_CODE_EXPIRE_TIME, expireTime)
        intent.putExtra(
            AccountSwitchActivity.GET_MESSAGE_CODE_TOKEN,
            viewModel.mGetMessageCodeToken
        )
        intent.putExtra(AccountSwitchActivity.TEAM_ACCOUNT_INFO, viewModel.mTeamAccountInfo)
        intent.putExtra(AccountSwitchActivity.TEAM_USER_INFO, viewModel.mTeamUserInfo)
        requireActivity().startActivity(intent)
        //手机上显示打开动画效果，pad弹窗样式不显示打开动画
        if (!LoginUtil.isTablet(requireActivity())) {
            requireActivity().overridePendingTransition(
                com.jme.login.R.anim.jdme_right_in,
                com.jme.login.R.anim.jdme_left_out
            )
        }

        mView.postDelayed(object : Runnable {
            override fun run() {
                personalCenterUserDialog?.dismiss()
                mOnDrawerCloseListener?.invoke()
            }
        }, 500)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.jdsaas_fragment_personal_center, container, false)
        //View顶部空出状态栏的距离
        if (StatusBarConfig.enableImmersive()) {
            mView.rv_personal_center_team.setPadding(
                0,
                QMUIStatusBarHelper.getStatusbarHeight(context),
                0,
                0
            )
            mView.cl_personal_center.setPadding(
                0,
                QMUIStatusBarHelper.getStatusbarHeight(context),
                0,
                0
            )
        }
        mView.cl_personal_center.setBackgroundResource(R.drawable.jdsaas_personal_center_left_bg)
        mView.personal_center_layout.viewModel = viewModel
        mView.personal_center_layout.listener = this
        return mView
    }

    /*每次打开个人中心侧弹窗时刷新数据*/
    fun refreshData() {
        mView.personal_center_layout.refreshData()
    }

    override fun close() {
        mView.postDelayed(object : Runnable {
            override fun run() {
                mOnDrawerCloseListener?.invoke()
            }
        }, 500)
    }

    //显示租户下的用户列表弹窗
    override fun showPersonalCenterUserDialog(teamAccountInfo: TeamAccountInfo?) {
        personalCenterUserDialog = PersonalCenterUserDialog(teamAccountInfo)
        personalCenterUserDialog?.show(childFragmentManager, "AccountCenterTeamUserDialog")
    }

    fun setOnCloseListener(listener: () -> Unit) {
        this.mOnDrawerCloseListener = listener
    }
}