package com.jd.oa.business.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.business.data.PersonalCenterRepository

class PersonalCenterModelFactory(private val repository: PersonalCenterRepository) :
    ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return PersonalCenterViewModel(repository) as T
    }
}