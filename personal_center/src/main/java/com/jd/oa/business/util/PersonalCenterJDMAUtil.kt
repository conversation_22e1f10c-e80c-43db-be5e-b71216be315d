package com.jd.oa.business.util

import com.jd.oa.AppBase
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.utils.JDMAUtils

object PersonalCenterJDMAUtil {

    val MOBILE_EVENT_PLATFORM_TOPME_CLICK = "Mobile_Event_Platform_topME_click"
    val MOBILE_EVENT_PLATFORM_TOPME = "Mobile_Event_Platform_topME"
    val TOPME_LIST_PROFILE = "topme_list_profile" //头像/姓名
    val TOPME_LIST_STATUS = "topme_list_status" // 状态
    val TOPME_LIST_SIGNATURE = "topme_list_signature" //签名
    val TOPME_LIST_MYCARD = "topme_list_mycard" //我的名片
    val TOPME_LIST_THEMES = "topme_list_themes" //主题外观
    val TOPME_LIST_HELP = "topme_list_help" //帮助与客服
    val TOPME_LIST_SETTINGS = "topme_list_settings" //设置
    val TOPME_LIST_ADDACCOUNT = "topme_list_addaccount" //添加账号
    val TOPME_LIST_SWITCHACCOUNT = "topme_list_Switchaccount" //切换账号
    val TOPME_SWITCHACCOUNT_ADDACCOUNT = "topme_Switchaccount_addaccount" //切换账号里的添加
    val TOPME_LIST_SWITCHTENANT = "topme_list_Switchtenant" //切换租户

    fun onPersonalCenterEventClick(clickName: String) {
        val param = HashMap<String, String>()
        param["erp"] = MultiAppConstant.getUserId()
        param["clickName"] = clickName
        JDMAUtils.onEventClick(
            AppBase.getAppContext(), "", "", MOBILE_EVENT_PLATFORM_TOPME_CLICK,
            MultiAppConstant.getUserId(), "", "", "", MOBILE_EVENT_PLATFORM_TOPME, param
        )
    }
}