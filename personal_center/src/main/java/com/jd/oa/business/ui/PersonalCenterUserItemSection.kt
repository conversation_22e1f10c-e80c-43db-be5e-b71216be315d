package com.jd.oa.business.ui

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.elliptical.SuperEllipticalImageView
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.ToastUtils
import com.jdsaas.personal_center.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class PersonalCenterUserItemSection(
    val context: Context,
    val list: List<TeamUserInfo>?,
    val clickListener: UserItemClickListener?
) : Section(
    SectionParameters.builder()
        .itemResourceId(R.layout.jdsaas_personal_center_user_item)
        .footerResourceId(R.layout.jdsaas_personal_center_user_footer)
        .build()
) {


    interface UserItemClickListener {
        fun onUserItemClickListener(teamUserInfo: TeamUserInfo?)
    }

    override fun getContentItemsTotal(): Int {
        return list?.size ?: 0
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun getFooterViewHolder(view: View): RecyclerView.ViewHolder {
        return FooterItemViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, position: Int) {
        val itemHolder = viewHolder as ItemViewHolder
        val teamUserInfo = list?.get(position)
        JdSaasAvatarUtil.loadPersonalAvatar(
            context,
            itemHolder.iv_user_avatar,
            teamUserInfo?.avatar
        )
        itemHolder.tv_user_name.setText(teamUserInfo?.realName)
        if (teamUserInfo?.loginCurrently ?: false) {
            itemHolder.iv_user_login_currently.visibility = View.VISIBLE
        } else if (teamUserInfo?.activeStatus == 0) { //待激活
            itemHolder.iv_user_login_currently.visibility = View.GONE
            itemHolder.tv_user_state.visibility = View.VISIBLE
            itemHolder.tv_user_state.text =
                context.getString(com.jme.login.R.string.jdsaas_login_unactivated)
            itemHolder.tv_user_state.background =
                context.getDrawable(com.jme.login.R.drawable.jdsaas_tv_team_status_unactivated)
            itemHolder.tv_user_state.setTextColor(context.getColor(com.jme.login.R.color.jdsaas_color_blue_4A5FE8))
        } else {
            itemHolder.iv_user_login_currently.visibility = View.GONE
            itemHolder.tv_user_state.visibility = View.GONE
        }
        itemHolder.itemView.setOnClickListener {
            clickListener?.onUserItemClickListener(teamUserInfo)
        }
    }

    override fun onBindFooterViewHolder(holder: RecyclerView.ViewHolder?) {
        val itemHolder = holder as FooterItemViewHolder
        itemHolder.ll_add_account.setOnClickListener {
            ToastUtils.showToast("敬请期待")
        }

    }

    class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val iv_user_avatar = view.findViewById<SuperEllipticalImageView>(R.id.iv_user_avatar)
        val tv_user_name = view.findViewById<TextView>(R.id.tv_user_name)
        val iv_user_login_currently = view.findViewById<IconFontView>(R.id.iv_user_login_currently)
        val tv_user_state = view.findViewById<TextView>(R.id.tv_user_state)
    }

    class FooterItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val ll_add_account = view.findViewById<LinearLayout>(R.id.ll_add_account)
    }

}