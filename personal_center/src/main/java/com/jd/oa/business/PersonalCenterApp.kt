package com.jd.oa.business

import android.app.Activity
import android.os.Bundle
import android.text.TextUtils
import com.jd.oa.AppBase
import com.jd.oa.BaseApp
import com.jd.oa.CommonApp
import com.jd.oa.MyPlatform
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jd.oa.business.login.model.UserEntity
import com.jd.oa.preference.PreferenceManager
import com.jme.login.BuildConfig
import java.lang.ref.WeakReference

class PersonalCenterApp : BaseApp() {
    override fun appInit() {
        registerApplicationInit(CommonApp::class.java)
        registerLifeActivityCallbacks()
        initBuildConfig()
    }

    override fun onCreate() {
        super.onCreate()
        ApmLoaderHepler.getInstance(this).init(AppBase.DEBUG, BuildConfig.SHOW_SERVER_SWITCHER)
        initUser()
    }

    private fun initBuildConfig() {
        AppBase.DEBUG = BuildConfig.DEBUG
        AppBase.SHOW_SERVER_SWITCHER = BuildConfig.SHOW_SERVER_SWITCHER
    }

    private fun initUser(){
        // 4.初始化当前登录用户
        val userName = PreferenceManager.UserInfo.getUserName()

        if (!TextUtils.isEmpty(userName)) {
            val user = UserEntity(
                userName,
                PreferenceManager.UserInfo.getUserRealName(),
                PreferenceManager.UserInfo.getUserCover(),
                PreferenceManager.UserInfo.getJdAccount(),
                PreferenceManager.UserInfo.getUserAttendance(),
                PreferenceManager.UserInfo.getUserSexFlag()
            )
            user.appId = PreferenceManager.UserInfo.getTimlineAppID()
            user.teamId = PreferenceManager.UserInfo.getTeamId()
            user.userId = PreferenceManager.UserInfo.getUserId()
            MyPlatform.setCurrentUser(user)
        }
    }

    private fun registerLifeActivityCallbacks() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                AppBase.topActivity = WeakReference(activity)
            }

            override fun onActivityStarted(activity: Activity) {
            }

            override fun onActivityResumed(activity: Activity) {
                AppBase.topActivity = WeakReference(activity)
            }

            override fun onActivityPaused(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {
            }
        })
    }
}