package com.jd.oa.business.ui

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.ui.CircleImageView
import com.jd.oa.utils.ToastUtils
import com.jdsaas.personal_center.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class PersonalCenterTeamSection(
    val context: Context,
    val list: List<TeamAccountInfo>?,
    val clickListener: AccountCenterTeamItemClickListener?
) : Section(
    SectionParameters.builder()
        .itemResourceId(R.layout.jdsaas_personal_center_team_item)
        .footerResourceId(R.layout.jdsaas_personal_center_team_footer)
        .build()
) {

    override fun getContentItemsTotal(): Int {
        return list?.size ?: 0
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun getFooterViewHolder(view: View): RecyclerView.ViewHolder {
        return FooterViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, position: Int) {
        val itemViewHolder = viewHolder as ItemViewHolder
        val teamAccountInfo = list?.get(position)
        JdSaasAvatarUtil.loadGroupAvatar(
            context,
            itemViewHolder.iv_account_center_team_avatar,
            teamAccountInfo?.avatar,
            teamAccountInfo?.teamFullName
        )
        itemViewHolder.tv_account_center_team_name.setText(teamAccountInfo?.teamFullName ?: "")
        if (teamAccountInfo?.loginCurrently ?: false) {//当前登录
            itemViewHolder.view_indicator_line.visibility = View.VISIBLE
            itemViewHolder.tv_account_center_team_name.paint.isFakeBoldText = true
            itemViewHolder.tv_account_center_team_name.setTextColor(context.getColor(R.color.jdsaas_black))
        } else {
            itemViewHolder.view_indicator_line.visibility = View.GONE
            itemViewHolder.tv_account_center_team_name.paint.isFakeBoldText = false
            itemViewHolder.tv_account_center_team_name.setTextColor(context.getColor(R.color.jdsaas_black_6A6A6A))
        }

        itemViewHolder.itemView.setOnClickListener {
            clickListener?.onAccountCenterTeamItemClick(teamAccountInfo)
        }
    }

    override fun onBindFooterViewHolder(holder: RecyclerView.ViewHolder?) {
        val itemViewHolder = holder as FooterViewHolder
        itemViewHolder.itemView.setOnClickListener {
            ToastUtils.showToast("敬请期待")
        }
    }

    class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val view_indicator_line = view.findViewById<View>(R.id.view_indicator_line)
        val iv_account_center_team_avatar =
            view.findViewById<CircleImageView>(R.id.iv_personal_center_team_avatar)
        val tv_account_center_team_name =
            view.findViewById<TextView>(R.id.tv_personal_center_team_name)
    }

    class FooterViewHolder(view: View) : RecyclerView.ViewHolder(view) {

    }

    interface AccountCenterTeamItemClickListener {
        fun onAccountCenterTeamItemClick(teamAccountInfo: TeamAccountInfo?)
    }

}