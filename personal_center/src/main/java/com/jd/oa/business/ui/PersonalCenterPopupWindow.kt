package com.jd.oa.business.ui

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.util.PersonalCenterInjectorUtil
import com.jd.oa.ui.widget.AbsPopupwindow
import com.jd.oa.utils.CommonUtils
import com.jdsaas.personal_center.R
import kotlinx.android.synthetic.main.jdsaas_personal_center_layout.view.cl_personal_center
import kotlinx.android.synthetic.main.jdsaas_popup_personal_center.view.fl_personal_center
import kotlinx.android.synthetic.main.jdsaas_popup_personal_center.view.personal_center_layout

class PersonalCenterPopupWindow(val activity: Activity) : AbsPopupwindow(activity),
    PersonalCenterLayout.OnPersonalCenterLayoutListener {
    val viewModel by lazy {
        ViewModelProvider(
            activity as FragmentActivity,
            PersonalCenterInjectorUtil.getPersonalCenterModelFactory()
        ).get(PersonalCenterViewModel::class.java)
    }

    init {
        mContentView = View.inflate(activity, R.layout.jdsaas_popup_personal_center, null)
        super.initView()

        if (MultiAppConstant.isSaasFlavor()) { //设置popupWindow宽度
            mContentView.fl_personal_center.layoutParams.width = CommonUtils.dp2px(320f)
        }
        mContentView.cl_personal_center.setBackgroundResource(R.drawable.jdsaas_personal_center_pupup_bg)
        mContentView.personal_center_layout.viewModel = viewModel
        mContentView.personal_center_layout.listener = this
        mContentView.setOnClickListener {
            close()
        }
        applyDim()
    }

    override fun setData(data: MutableList<String>?, defaultVal: Int) {

    }

    override fun close() {
        dismiss()
    }

    //saas显示切换用户弹窗
    override fun showPersonalCenterUserDialog(teamAccountInfo: TeamAccountInfo?) {

    }

    //设置popupWindow的背景
    private fun applyDim() {
        val parent = activity.window.decorView.rootView as ViewGroup
        val dim: Drawable = ColorDrawable(Color.BLACK)
        dim.setBounds(0, 0, parent.width, parent.height)
        dim.alpha = (255 * 0.1).toInt()
        val overlay = parent.overlay
        overlay.add(dim)
    }

    //清除popupWindow的背景
    private fun clearDim() {
        val parent = activity.window.decorView.rootView as ViewGroup
        val overlay = parent.overlay
        overlay.clear()
    }

    override fun dismiss() {
        super.dismiss()
        clearDim()
    }
}