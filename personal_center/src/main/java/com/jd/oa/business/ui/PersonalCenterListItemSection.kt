package com.jd.oa.business.ui

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.badge.BadgeManager
import com.jd.oa.badge.RedDotView
import com.jdsaas.personal_center.R
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters

class PersonalCenterListItemSection(
    val context: Context,
    val data: Map<String, String>,
    val clickListener: AccountCenterListItemClickListener?
) : Section(
    SectionParameters.builder().itemResourceId(R.layout.jdsaas_personal_center_list_item).build()
) {


    override fun getContentItemsTotal(): Int {
        return data.size
    }

    override fun getItemViewHolder(view: View): RecyclerView.ViewHolder {
        return ItemViewHolder(view)
    }

    override fun onBindItemViewHolder(viewHolder: RecyclerView.ViewHolder?, position: Int) {
        val holder = viewHolder as ItemViewHolder
        val icon = data.keys.elementAt(position)
        val name = data.values.elementAt(position)
        holder.iv_icon.text = icon
        holder.tv_name.text = name

        if (context.getString(R.string.personal_center_setting) == name) {
            holder.rdv_badge.visibility = View.VISIBLE
            holder.rdv_badge.setAppLinks(BadgeManager.BADGE_APP_UPDATE)
            holder.rdv_badge.setImageDrawable(
                AppCompatResources.getDrawable(
                    context,
                    R.drawable.red_dot_color_red
                )
            )
        } else {
            holder.rdv_badge.visibility = View.GONE
        }

        holder.itemView.setOnClickListener {
            clickListener?.onAccountCenterListItemClick(data.values.elementAt(position))
        }
    }

    class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val iv_icon = view.findViewById<TextView>(R.id.iv_icon)
        val tv_name = view.findViewById<TextView>(R.id.tv_name)
        val rdv_badge = view.findViewById<RedDotView>(R.id.rdv_badge)
    }

    interface AccountCenterListItemClickListener {
        fun onAccountCenterListItemClick(name: String)
    }
}