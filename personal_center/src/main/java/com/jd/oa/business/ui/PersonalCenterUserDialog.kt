package com.jd.oa.business.ui

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.BottomSheetCallback
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.jd.oa.business.jdsaaslogin.data.model.TeamAccountInfo
import com.jd.oa.business.jdsaaslogin.data.model.TeamUserInfo
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginHelper
import com.jd.oa.business.util.PersonalCenterInjectorUtil
import com.jd.oa.utils.DisplayUtil
import com.jdsaas.personal_center.R
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter
import kotlinx.android.synthetic.main.dialog_personal_center_team_user.view.iv_dialog_close
import kotlinx.android.synthetic.main.dialog_personal_center_team_user.view.iv_dialog_user_avatar
import kotlinx.android.synthetic.main.dialog_personal_center_team_user.view.rv_dialog_user
import kotlinx.android.synthetic.main.dialog_personal_center_team_user.view.tv_dialog_team_name

class PersonalCenterUserDialog(val teamAccountInfo: TeamAccountInfo?) : BottomSheetDialogFragment(),
    PersonalCenterUserItemSection.UserItemClickListener {

    val viewModel by lazy {
        ViewModelProvider(
            requireParentFragment(),
            PersonalCenterInjectorUtil.getPersonalCenterModelFactory()
        ).get(
            PersonalCenterViewModel::class.java
        )
    }

    lateinit var mView: View

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mView = inflater.inflate(R.layout.dialog_personal_center_team_user, container, false)
        mView.iv_dialog_close.setOnClickListener {
            dismiss()
        }
        JdSaasAvatarUtil.loadGroupAvatar(
            requireContext(),
            mView.iv_dialog_user_avatar,
            teamAccountInfo?.avatar,
            teamAccountInfo?.teamFullName
        )
        mView.tv_dialog_team_name.setText(teamAccountInfo?.teamFullName ?: "")
        mView.rv_dialog_user.layoutManager =
            LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        val sectionedAdapter = SectionedRecyclerViewAdapter()
        sectionedAdapter.addSection(
            PersonalCenterUserItemSection(
                requireContext(),
                teamAccountInfo?.teamUserInfoList,
                this
            )
        )
        mView.rv_dialog_user.adapter = sectionedAdapter
        mView.rv_dialog_user.setHasFixedSize(true)
        mView.rv_dialog_user.isNestedScrollingEnabled = false
        return mView
    }

    override fun onStart() {
        super.onStart()
        val bottomSheet: View? = dialog?.window?.findViewById<FrameLayout>(R.id.design_bottom_sheet)
        if (bottomSheet != null) {
            val behavior: BottomSheetBehavior<*> = BottomSheetBehavior.from(bottomSheet)
            bottomSheet.background = ColorDrawable(Color.TRANSPARENT)
            val maxHeight = DisplayUtil.getScreenHeight(context) * 85 / 100
            behavior.peekHeight = maxHeight
            behavior.addBottomSheetCallback(object : BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (newState == BottomSheetBehavior.STATE_DRAGGING) {
                        behavior.state = BottomSheetBehavior.STATE_COLLAPSED
                        if (bottomSheet.measuredHeight >= maxHeight) {
                            bottomSheet.layoutParams.height = maxHeight
                            bottomSheet.layoutParams = bottomSheet.layoutParams
                        }
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {

                }
            })
        }
    }

    override fun onUserItemClickListener(teamUserInfo: TeamUserInfo?) {
        loginByPhone(teamUserInfo)
    }

    private fun loginByPhone(teamUserInfo: TeamUserInfo?) {
        if (!(teamUserInfo?.loginCurrently ?: false)) {
            JdSaasLoginHelper.showSwitchAccountDialog(requireActivity()) {
                viewModel.mTeamAccountInfo = teamAccountInfo
                viewModel.mTeamUserInfo = teamUserInfo
                viewModel.loginByPhoneAfterCheckToken()
            }
        }
    }
}