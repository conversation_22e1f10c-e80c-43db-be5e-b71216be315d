package com.jd.oa.business.ui

import android.os.Bundle
import android.view.Window
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.StatusBarConfig
import com.jdsaas.personal_center.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper

@Route(DeepLink.PERSONAL_CENTER_LAUNCH_ACTIVITY)
class PersonalCenterLaunchActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this)
        }
        setContentView(R.layout.activity_personal_center_launch)
    }
}