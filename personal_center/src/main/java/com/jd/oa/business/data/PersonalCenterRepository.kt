package com.jd.oa.business.data

import com.jd.oa.business.LoginRepository
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.SimpleRequestCallback

class PersonalCenterRepository : LoginRepository() {
    companion object {
        const val API_GET_GLOBAL_CONFIG = "exp.getGlobalConfig"
    }

    fun getGlobalConfig(callback: SimpleRequestCallback<String>) { //获取新版个人中心楼层需要在请求中加centerVer: v3
        HttpManager.post(null, null, callback, API_GET_GLOBAL_CONFIG)
    }

}