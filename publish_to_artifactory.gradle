publishing {
    publications {
        aar(MavenPublication) {
            groupId MODULE_GROUP_ID
            version = project.ext.version_name
            artifactId project.getName()
            // Tell maven to prepare the generated "* .aar" file for publishing
            artifact("$buildDir/outputs/aar/${archivesBaseName}-release.aar")

            pom.withXml {
                //Creating additional node for dependencies
                def dependenciesNode = asNode().appendNode('dependencies')

                //Defining configuration names from which dependencies will be taken (debugCompile or releaseCompile and compile)
                def configurationNames = ["releaseImplementation", "api",'implementation','annotationProcessor']
                configurationNames.each { configurationName ->
                    project.configurations.getByName(configurationName).allDependencies.each {
                        if (it.group != null && it.name != null) {
                            println 'dependencies ======>' + it.group + ":" + it.name + "" + it.version + ","
                            println("${it.group}:${it.name}:${it.version}:v_lib_framework_http:")
                            def dependencyNode = dependenciesNode.appendNode('dependency')
                            dependencyNode.appendNode('groupId', it.group)
                            dependencyNode.appendNode('artifactId', it.name)
                            dependencyNode.appendNode('version', it.version)

                            //If there are any exclusions in dependency
                            if (it.excludeRules.size() > 0) {
                                def exclusionsNode = dependencyNode.appendNode('exclusions')
                                it.excludeRules.each { rule ->
                                    def exclusionNode = exclusionsNode.appendNode('exclusion')
                                    exclusionNode.appendNode('groupId', rule.group)
                                    exclusionNode.appendNode('artifactId', rule.module)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

artifactory {
    contextUrl = MAVEN_URL
    publish {
        repository {
            // The Artifactory repository key to publish to
            repoKey = SNAP_REPO
            username = MODULE_USERNAME
            password = MODULE_PWD
        }
        defaults {
            // Tell the Artifactory Plugin which artifacts should be published to Artifactory.
            publications(MODULE_TYPE)
            publishArtifacts = true
            // Properties to be attached to the published artifacts.
            properties = ['qa.level': 'basic', 'dev.team': 'core']
            // Publish generated POM files to Artifactory (true by default)
            publishPom = true
        }
    }
}

