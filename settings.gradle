// ============================================
// ==          New Architecture Libs         ==
// ============================================

//include ':joy_note_app'
include ':joy_note'
include ':experience'
//include ':libpermission'
include ':manto'

include ':aar_chatsdk'
project(':aar_chatsdk').projectDir = new File("aar_module/aar_chatsdk")
include ':aar_connectivity_release'
project(':aar_connectivity_release').projectDir = new File("aar_module/aar_connectivity_release")
include ':aar_flutter_boost_release'
project(':aar_flutter_boost_release').projectDir = new File("aar_module/aar_flutter_boost_release")
include ':aar_flutter_debug'
project(':aar_flutter_debug').projectDir = new File("aar_module/aar_flutter_debug")
include ':aar_flutter_release'
project(':aar_flutter_release').projectDir = new File("aar_module/aar_flutter_release")
include ':aar_back_press_release'
project(':aar_back_press_release').projectDir = new File("aar_module/aar_back_press_release")
include ':aar_jdf_channel_release'
project(':aar_jdf_channel_release').projectDir = new File("aar_module/aar_jdf_channel_release")
include ':aar_jdf_container_plugin_release'
project(':aar_jdf_container_plugin_release').projectDir = new File("aar_module/aar_jdf_container_plugin_release")
include ':aar_jdf_jdme_file_transfer_plugin_release'
project(':aar_jdf_jdme_file_transfer_plugin_release').projectDir = new File("aar_module/aar_jdf_jdme_file_transfer_plugin_release")
include ':aar_jdf_jdme_network_plugin_release'
project(':aar_jdf_jdme_network_plugin_release').projectDir = new File("aar_module/aar_jdf_jdme_network_plugin_release")
include ':aar_jdf_router_plugin_release'
project(':aar_jdf_router_plugin_release').projectDir = new File("aar_module/aar_jdf_router_plugin_release")
include ':aar_path_provider_release'
project(':aar_path_provider_release').projectDir = new File("aar_module/aar_path_provider_release")
include ':aar_shared_preferences_release'
project(':aar_shared_preferences_release').projectDir = new File("aar_module/aar_shared_preferences_release")
include ':aar_migratechatdata'
project(':aar_migratechatdata').projectDir = new File("aar_module/aar_migratechatdata")
include ':aar_speechcodec'
project(':aar_speechcodec').projectDir = new File("aar_module/aar_speechcodec")
include ':aar_sqflite_release'
project(':aar_sqflite_release').projectDir = new File("aar_module/aar_sqflite_release")
include ':aar_videoRecorder'
project(':aar_videoRecorder').projectDir = new File("aar_module/aar_videoRecorder")
include ':aar_android-sdk-gatewaysign'
project(':aar_android-sdk-gatewaysign').projectDir = new File("aar_module/aar_android-sdk-gatewaysign")
include ':aar_animated-base-support'
project(':aar_animated-base-support').projectDir = new File("aar_module/aar_animated-base-support")
include ':aar_animated-gif'
project(':aar_animated-gif').projectDir = new File("aar_module/aar_animated-gif")
include ':aar_ares-framework'
project(':aar_ares-framework').projectDir = new File("aar_module/aar_ares-framework")
include ':aar_jdreact-android-plugin-gradient'
project(':aar_jdreact-android-plugin-gradient').projectDir = new File("aar_module/aar_jdreact-android-plugin-gradient")
include ':aar_jdreact-android-plugin-json'
project(':aar_jdreact-android-plugin-json').projectDir = new File("aar_module/aar_jdreact-android-plugin-json")
include ':aar_jdreact-android-plugin-network'
project(':aar_jdreact-android-plugin-network').projectDir = new File("aar_module/aar_jdreact-android-plugin-network")
include ':aar_jdreact-android-plugin-utils'
project(':aar_jdreact-android-plugin-utils').projectDir = new File("aar_module/aar_jdreact-android-plugin-utils")
include ':aar_jdreact-download'
project(':aar_jdreact-download').projectDir = new File("aar_module/aar_jdreact-download")
include ':aar_jdreact-framework'
project(':aar_jdreact-framework').projectDir = new File("aar_module/aar_jdreact-framework")
include ':aar_jdreact-sdk'
project(':aar_jdreact-sdk').projectDir = new File("aar_module/aar_jdreact-sdk")
include ':aar_jdreact-android-plugin-jdmodal'
project(':aar_jdreact-android-plugin-jdmodal').projectDir = new File("aar_module/aar_jdreact-android-plugin-jdmodal")


import java.util.regex.Pattern

include ':app'
include ':workbench'
include ':libvehicle'
include ':libtravel'
//include ':daogenerator'
include ':common'
include ':wifiauth'
include ':libadvert'
//include ':libsharesdk'
include ':login'
//include ':libasr'
include ':libscanner'
include ':libjdreact'
include ':lib_me_flutter'
include ':im_dd'
include ':liblivedetect'
//include ':im_dd_redpacket'

//include ':im_dd_app'
//include ':workbench_app'
//include ':usecar_app'
include ':unifiedsearch'
//include ':viewpagerindicator'

// timline
if (timlineDebug.toBoolean()) {
    include ':JIMSdk'
    project(':JIMSdk').projectDir = new File("../jd-icsp/JIMSdk")
    include ':JIMAudio'
    project(':JIMAudio').projectDir = new File("../jd-icsp/JIMAudio")
    include ':JIMUi'
    project(':JIMUi').projectDir = new File("../jd-icsp/JIMUi")
    include ':JIMCore'
    project(':JIMCore').projectDir = new File("../jd-icsp/JIMCore")
    include ':JIMSmiley'
    project(':JIMSmiley').projectDir = new File("../jd-icsp/JIMSmiley")
    include ':JIMWidget'
    project(':JIMWidget').projectDir = new File("../jd-icsp/JIMWidget")
    include ':JIMMap'
    project(':JIMMap').projectDir = new File("../jd-icsp/JIMMap")
    include ':JIMOkhttp'
    project(':JIMOkhttp').projectDir = new File("../jd-icsp/JIMOkhttp")
    include ':JIMGlide'
    project(':JIMGlide').projectDir = new File("../jd-icsp/JIMGlide")
    include ':JIMBase'
    project(':JIMBase').projectDir = new File("../jd-icsp/JIMBase")
    include ':JIMGallery'
    project(':JIMGallery').projectDir = new File("../jd-icsp/JIMGallery")
    include ':JIMDownloadUpload'
    project(':JIMDownloadUpload').projectDir = new File("../jd-icsp/JIMDownloadUpload")
    include ':jimutils'
    project(':jimutils').projectDir = new File("../jd-icsp/jimutils")
    include ':signalvariant'
    project(':signalvariant').projectDir = new File("../jd-icsp/signalvariant")
} else {
    include ':aar_jimaudio'
    project(':aar_jimaudio').projectDir = new File("aar_module/aar_jimaudio")
    include ':aar_jimbase'
    project(':aar_jimbase').projectDir = new File("aar_module/aar_jimbase")
    include ':aar_jimcore'
    project(':aar_jimcore').projectDir = new File("aar_module/aar_jimcore")
    include ':aar_JIMDownloadUpload'
    project(':aar_JIMDownloadUpload').projectDir = new File("aar_module/aar_JIMDownloadUpload")
    include ':aar_jimgallery'
    project(':aar_jimgallery').projectDir = new File("aar_module/aar_jimgallery")
    include ':aar_jimglide'
    project(':aar_jimglide').projectDir = new File("aar_module/aar_jimglide")
    include ':aar_jimmap'
    project(':aar_jimmap').projectDir = new File("aar_module/aar_jimmap")
    include ':aar_jimokhttp'
    project(':aar_jimokhttp').projectDir = new File("aar_module/aar_jimokhttp")
    include ':aar_jimsdk'
    project(':aar_jimsdk').projectDir = new File("aar_module/aar_jimsdk")
    include ':aar_jimsmiley'
    project(':aar_jimsmiley').projectDir = new File("aar_module/aar_jimsmiley")
    include ':aar_jimui'
    project(':aar_jimui').projectDir = new File("aar_module/aar_jimui")
    include ':aar_jimutils'
    project(':aar_jimutils').projectDir = new File("aar_module/aar_jimutils")
    include ':aar_jimwidget'
    project(':aar_jimwidget').projectDir = new File("aar_module/aar_jimwidget")
    include ':aar_jimsignalvariant'
    project(':aar_jimsignalvariant').projectDir = new File("aar_module/aar_jimsignalvariant")
}

//include ':datetime-picker'
//project(':datetime-picker').projectDir = new File("../datetime-picker/datetime-picker")

gradle.addBuildListener(new BuildListener() {
//    @Override
//    void buildStarted(Gradle gradle) {
//
//    }

    @Override
    void settingsEvaluated(Settings settings) {
        File file = new File(settings.rootDir.path + "/app/src/me/assets/jdreact/JDReactJoySpaceHub/JDReactJoySpaceHub.version")
        if (!file.exists()) {
            throw new RuntimeException(file.path + " 不存在，需修改至新的路径")
        }
        file.eachLine { line, num ->
            if (line != null && line.contains("moduleCode")) {
                String codeS = line.substring(line.indexOf(":") + 1).trim()
                Pattern pattern = Pattern.compile("\"(\\d+)(\\.\\d+)*\".+")
                def matcher = pattern.matcher(codeS)
                if (matcher.matches()) {
                    codeS = matcher.group(1)
                    try {
                        double code = Double.parseDouble(codeS)
                        println("moduleCode = " + code)
                        if (code >= 1000) {// 此处暂定 1000。如果业务版本超过 1000，可修改成别的值
                            throw new RuntimeException(file.path + " 中 moduleCode 不合法，请修改后再编译")
                        }
                    } catch (Exception e) {
                        if (e instanceof NumberFormatException) {
                            throw new RuntimeException(file.path + " 中 moduleCode 不合法，请修改后再编译")
                        } else {
                            throw e
                        }
                    }
                } else {
                    throw new RuntimeException(file.path + " 中 moduleCode 不合法，请修改后再编译")
                }
            }
        }
    }

    @Override
    void projectsLoaded(Gradle gradle) {

    }

    @Override
    void projectsEvaluated(Gradle gradle) {

    }

    @Override
    void buildFinished(BuildResult result) {

    }
})

include ':joywork'
include ':libanalyze'

include ':libdynamic'
//include ':joywork_app'
//include ':lib_mae_album'
include ':calendar'
//include ':librecurparser'
include ':libdynamic_biz'
include ':meeting'
//include ':lib_me_ui'
include ':libphotoeditor'
include ':personal_center'
include ':libjdmeeting'

// ============================================
// == Demo Project (Independent) ==
// Demo项目依赖配置：支持本地调试和Maven发布两种模式
// 通过gradle.properties中的USE_LOCAL_DEMO_PROJECT参数控制

/**
 * Demo项目依赖策略选择
 *
 * 开发模式(USE_LOCAL_DEMO_PROJECT=true):
 * - 使用本地includeBuild方式
 * - 便于实时调试和代码修改
 * - 自动包含最新的本地更改
 *
 * 发布模式(USE_LOCAL_DEMO_PROJECT=false):
 * - 使用Maven依赖方式
 * - 依赖已发布的稳定版本
 * - 确保构建的可重现性
 */
enableFeaturePreview('VERSION_CATALOGS')

// 从gradle.properties中读取配置
Properties properties = new Properties()
File propertiesFile = new File(rootDir, 'gradle.properties')
if (propertiesFile.exists()) {
    propertiesFile.withInputStream { properties.load(it) }
}

def useLocalDemoProject = Boolean.parseBoolean(properties.getProperty('USE_LOCAL_DEMO_PROJECT', 'true'))

if (useLocalDemoProject) {
    println "📦 Demo项目: 使用本地includeBuild模式 (开发调试)"
    includeBuild '../jdme_demo_project'
} else {
    println "📦 Demo项目: 使用Maven依赖模式 (生产发布)"
    // Maven依赖将在app/build.gradle中配置
}

include ':saas_address'
//includeBuild '../saas_address_project'

//include ':libstorage'
//includeBuild '../storage_project'

//lib_utils
if (false) {
    includeBuild '../lib_utils_project'
} else {
    println "📦 lib_utils使用坐标方式依赖"
}

//employeecard
if (false) {
    includeBuild '../employeecard_project'
} else {
    println "📦 employeecard使用坐标方式依赖"
}

if (false) {
    includeBuild '../lib_web_project'
}

include ':libjoymeeting'
//includeBuild('../joymeeting_project')

//includeBuild '../lib_config_project'

