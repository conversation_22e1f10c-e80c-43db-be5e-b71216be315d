#!/bin/bash

bundleDir=$1
targetDir=./app/src/saas

cp -f ${bundleDir}/JDReactJoySpaceHubWE.jsbundle ${targetDir}/assets/jdreact/JDReactJoySpaceHubWE/JDReactJoySpaceHubWE.jsbundle
cp -f ${bundleDir}/JDReactJoySpaceHubWE.version ${targetDir}/assets/jdreact/JDReactJoySpaceHubWE/JDReactJoySpaceHubWE.version

cp -af ${bundleDir}/raw/. ${targetDir}/assets/rnhtml/jdreactjoyspacehubwe

cp -af ${bundleDir}/drawable-xxxhdpi/. ${targetDir}/res/drawable-xxxhdpi
cp -af ${bundleDir}/drawable-xxhdpi/. ${targetDir}/res/drawable-xxhdpi
cp -af ${bundleDir}/drawable-xhdpi/. ${targetDir}/res/drawable-xhdpi
cp -af ${bundleDir}/drawable-hdpi/. ${targetDir}/res/drawable-hdpi
cp -af ${bundleDir}/drawable-mdpi/. ${targetDir}/res/drawable-mdpi

echo 'done'