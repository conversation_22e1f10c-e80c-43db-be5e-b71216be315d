<resources>

    <!-- Base application theme. -->
    <style name="MediaMakerAppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
<!--        <item name="android:windowFullscreen">true</item>-->
    </style>

    <style name="dialog_botton_anim">
        <item name="android:windowEnterAnimation">@anim/anim_down_in</item>
        <item name="android:windowExitAnimation">@anim/anim_down_out</item>
    </style>
    <style name="mm_dialog_alpha">
        <item name="android:windowEnterAnimation">@anim/mmanim_alpha_in</item>
        <item name="android:windowExitAnimation">@anim/mmanim_alpha_out</item>
    </style>

    <style name="DropDownTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="just_dialog_white_button">
        <item name="android:background">@drawable/mm_just_dialog_white_button</item>
        <item name="android:textColor">@color/mm_color_main</item>
        <item name="android:textSize">14dp</item>
    </style>

    <style name="just_dialog_red_button">
        <item name="android:background">@drawable/mm_button_b</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14dp</item>
    </style>

    <style name="mm_CustomDialogTheme" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/black</item> <!-- 设置背景为不透明的白色 -->
    </style>
</resources>
