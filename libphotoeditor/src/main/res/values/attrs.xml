<resources xmlns:ns1="http://schemas.android.com/tools">
    <declare-styleable name="BasePasteView">
        <attr name="bpEditable" format="boolean" />          <!-- 是否处于可编辑状态 -->
        <attr name="bpFrameColor" format="color" />         <!-- 边框颜色 -->
        <attr name="bpFrameWidth" format="dimension" />     <!-- 边框线宽度 -->
        <attr name="bpFramePadding" format="dimension" />   <!-- 边框与图片的间距 -->
        <attr name="bpDegree" format="float" />             <!-- 旋转角度 -->
        <attr name="bpScale" format="float" />              <!-- 缩放比例 -->
        <attr name="bpRotateScaleDrawable" format="reference" /> <!-- 缩放旋转图标 -->
        <attr name="bpRotateScaleLocation">                     <!-- 缩放旋转图标的位置 -->
            <enum name="left_top" value="0" />
            <enum name="right_top" value="1" />
            <enum name="right_bottom" value="2" />
            <enum name="left_bottom" value="3" />
        </attr>

        <attr name="bpCloseDrawable" format="reference" /> <!-- 关闭图标 -->
        <attr name="bpCloseLocation">                     <!-- 关闭图标的位置 -->
            <enum name="left_top" value="0" />
            <enum name="right_top" value="1" />
            <enum name="right_bottom" value="2" />
            <enum name="left_bottom" value="3" />
        </attr>
    </declare-styleable>
    <declare-styleable name="ViewPagerTab">
        <attr name="ptScrollOffset" format="dimension" />
        <attr name="ptTextSize" format="dimension" />
        <attr name="ptTextSizeSelected" format="dimension" />
        <attr name="ptTabPaddingLeftRight" format="dimension" />
        <attr name="ptTextColorNormal" format="color" />
        <attr name="ptTextColorSelected" format="color" />
        <attr name="ptShouldExpand" format="boolean" />
        <attr name="ptFirstLine" format="boolean" />
        <attr name="ptShowIndicator" format="boolean" />
    </declare-styleable>


    <declare-styleable name="HorizontalWheelView">
        <attr name="wheelable" format="boolean" />
        <attr name="hwv_ppi" format="dimension" />
        <attr name="item_text_size" format="dimension" />
        <attr name="item_text_shadowColor" format="color" />
        <attr name="item_text_shadowDy" format="integer" />
        <attr name="item_text_shadowRadius" format="integer" />
        <attr name="item_text_textColor" format="color" />
        <attr name="item_text_seletedtextColor" format="color" />
        <attr name="item_text_padding_top" format="dimension" />
        <attr name="item_text_padding_botton" format="dimension" />
        <attr name="item_text_padding_left" format="dimension" />
        <attr name="item_text_padding_right" format="dimension" />
        <attr name="item_text_max_increase_scale" format="float" />
    </declare-styleable>

    <declare-styleable name="RecordButton">
        <attr name="rb_bg_normal_color" format="color" />
        <attr name="rb_bg_blackStyle_color" format="color" />
        <attr name="rb_fc_color" format="color" />
        <attr name="rb_outsideWidth" format="dimension" />
        <attr name="rb_normal_draw" format="reference" />
        <attr name="rb_normal_margin" format="dimension" />
        <attr name="rb_pause_margin" format="dimension" />
        <attr name="rb_record_draw" format="reference" />
        <attr name="rb_pause_draw" format="reference" />
        <attr name="rb_fc_color_record" format="color" />

    </declare-styleable>

    <declare-styleable name="RangeCutView">
        <attr name="lineHeight" format="dimension" />
        <attr name="thumbWidth" format="dimension" />
        <attr name="lineColor" format="color" />
        <attr name="maskColor" format="color" />
        <attr name="tickCount" format="integer" />
        <attr name="leftThumbIndex" format="integer" />
        <attr name="rightThumbIndex" format="integer" />
        <attr name="leftThumbDrawable" format="reference" />
        <attr name="rightThumbDrawable" format="reference" />
    </declare-styleable>

    <declare-styleable name="ProgressCircle">
        <!-- 如果圆的边框是有宽度(厚度)的, 那么 circleRadius特指该圆的外边框的半径, 实际在绘制该圆时,
            绘制所用的半径是该 circleRadius减去圆边框厚度的 1/2后的值 -->
        <attr name="circleOuterRadius" format="dimension" />
        <!-- 圆边框的宽度(厚度) -->
        <attr name="circleBorderWidth" format="dimension" />
        <attr name="pcTextSize" format="dimension" />
        <!-- 圆边框的颜色 -->
        <attr name="circleBorderColor" format="color" />
        <!-- 圆内扇形的颜色 -->
        <attr name="innerPieColor" format="color" />
        <!-- 绘制圆内扇形的起始角度 -->
        <attr name="innerPieStartingAngle" format="float" />
        <!-- 绘制圆内扇形的最大进度 -->
        <attr name="innerPieMaxProgress" format="integer" />
        <!-- 绘制圆内扇形的进度 -->
        <attr name="innerPieProgress" format="integer" />
    </declare-styleable>

    <declare-styleable name="ColorButtom">
        <attr name="mmcb_src" format="reference" />
        <attr name="mmcb_img_w" format="dimension"/>
        <attr name="mmcb_img_h" format="dimension"/>
        <attr name="mmcb_view_interval" format="dimension"/>
        <attr name="mmcb_text" format="string" />
        <attr name="mmcb_color" format="reference" />
        <attr name="mmcb_change_image" format="boolean" />
        <attr name="mmcb_change_text" format="boolean" />
        <attr name="mmcb_textsize" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="VideoRecord">
        <attr name="cover_color" format="color" />
        <attr name="insideSizeControl" format="boolean" />
    </declare-styleable>
</resources>