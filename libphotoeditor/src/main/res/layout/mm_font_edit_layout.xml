<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="@android:color/transparent"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/ifv_font_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_font_color"
            android:textColor="@color/mm_color_white"
            android:textSize="@dimen/JMEIcon_24"
            tools:ignore="SpUsage" />

        <View
            android:layout_width="1dp"
            android:layout_height="16dp"
            android:layout_marginLeft="16dp"
            android:background="@color/mm_color_menu_split" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewFont"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginLeft="16dp"
            android:background="@android:color/transparent"
            tools:background="@android:color/holo_green_dark" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_weight="1"
        android:background="#2B2E33"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_weight="1"
            android:background="@color/mm_color_dark"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/editInput"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="10dp"
                android:layout_weight="1"
                android:background="@color/mm_color_dark"
                android:gravity="center_vertical|left"
                android:hint="@string/mm_input_text"
                android:maxLength="500"
                android:maxLines="1"
                android:padding="2dp"
                android:textColor="@color/mm_color_white"
                android:textColorHint="@color/mm_color_reset_disable"
                android:textSize="13dp" />

            <ImageView
                android:id="@+id/iv_delete"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical"
                android:padding="10dp"
                android:src="@drawable/mm_maker_close_w"
                android:visibility="gone" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>