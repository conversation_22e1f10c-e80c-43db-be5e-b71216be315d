<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <com.jd.oa.lib.editor.photo.clip.CutImageView
        android:id="@+id/mCutImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/ll_bottom"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        android:paddingBottom="28dp" />

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="122dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/mm_edit_memu_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="68dp"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1"
            android:gravity="center">

            <LinearLayout
                android:id="@+id/mBtnLeft90"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_general_rectanglespin"
                    android:textColor="@color/mm_color_edit_menu"
                    android:textSize="@dimen/JMEIcon_26" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="@string/mm_cut_left_90"
                    android:textColor="@color/mm_color_cut_normal"
                    android:textSize="14dp" />
            </LinearLayout>

            <View
                android:id="@+id/view_sep"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_margin="18dp"
                android:background="@color/mm_color_cut_353535" />

            <LinearLayout
                android:id="@+id/ll_rate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="16dp"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <LinearLayout
                        android:id="@+id/iv_cut_auto"
                        android:layout_width="48dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/mm_cut_bg_opt"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/mm_cut_icon_free" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/mm_cut_free"
                            android:textColor="@color/mm_color_cut_normal"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <LinearLayout
                        android:id="@+id/iv_cut_34"
                        android:layout_width="48dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/mm_cut_bg_opt"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/mm_cut_icon_34" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="3:4"
                            android:textColor="@color/mm_color_cut_normal"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <LinearLayout
                        android:id="@+id/iv_cut_11"
                        android:layout_width="48dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/mm_cut_bg_opt"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/mm_cut_icon_11" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="1:1"
                            android:textColor="@color/mm_color_cut_normal"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <LinearLayout
                        android:id="@+id/iv_cut_43"
                        android:layout_width="48dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/mm_cut_bg_opt"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/mm_cut_icon_43" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="4:3"
                            android:textColor="@color/mm_color_cut_normal"
                            android:textSize="10dp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:text="@string/icon_prompt_close"
                android:textColor="@color/mm_color_edit_menu"
                android:textSize="@dimen/JMEIcon_26" />

            <TextView
                android:id="@+id/tv_reset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center_horizontal"
                android:text="@string/mm_edit_reset"
                android:textColor="@color/mm_color_reset_disable"
                android:textSize="18dp" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_sure"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:text="@string/icon_prompt_check"
                android:textColor="@color/mm_color_edit_menu"
                android:textSize="@dimen/JMEIcon_26" />

        </RelativeLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="10dp" />
    </LinearLayout>
</RelativeLayout>
