<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="8dp"
    android:paddingRight="8dp"
    android:background="@color/mm_color_dark"
    android:paddingTop="25dp"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/iv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="取消"
            android:textColor="#FFFFFFFF"
            android:layout_gravity="center_vertical|left"
            android:textSize="14sp"
            android:padding="10dp"
            android:shadowRadius="3.0"/>

        <ImageView
            android:id="@+id/ic_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="10dp"
            android:src="@drawable/ic_mask_back"/>

        <TextView
            android:id="@+id/iv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:shadowRadius="3.0"
            android:text="@string/media_dialog_confrim"
            android:padding="10dp"
            android:textColor="@color/mm_color_main"
            android:layout_gravity="center_vertical|right"
            android:textSize="16sp" />

    </FrameLayout>


    <LinearLayout
        android:id="@+id/radio_mosaic"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_height="34dp" >

        <ImageView
            android:id="@+id/radio_mosaic_small"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:padding="12dp"
            android:src="@drawable/ic_mosaic_selector"/>

        <ImageView
            android:id="@+id/radio_mosaic_middle"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:padding="10dp"
            android:src="@drawable/ic_mosaic_selector"/>

        <ImageView
            android:id="@+id/radio_mosaic_big"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:padding="6dp"
            android:layout_marginLeft="6dp"
            android:src="@drawable/ic_mosaic_selector"/>


    </LinearLayout>
</LinearLayout>