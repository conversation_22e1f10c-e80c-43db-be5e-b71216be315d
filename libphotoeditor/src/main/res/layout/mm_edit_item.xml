<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="@dimen/mm_selected_frame_wh"
        android:layout_height="@dimen/mm_selected_frame_wh"
        android:layout_gravity="center">

        <ImageView
            android:id="@+id/mImageView"
            android:layout_width="@dimen/mm_selected_frame_wh"
            android:layout_height="@dimen/mm_selected_frame_wh"
            android:layout_gravity="center"
            android:background="@color/mm_bg_gray"
            android:padding="3dp"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/mDelete"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_gravity="right"
            android:layout_marginTop="3dp"
            android:layout_marginRight="3dp"
            android:src="@drawable/mm_picker_photo_delete_icon" />
    </FrameLayout>
</FrameLayout>