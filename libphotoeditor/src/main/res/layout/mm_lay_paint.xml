<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="8dp"
    android:paddingRight="8dp"
    android:background="@color/mm_color_dark"
    android:paddingTop="25dp"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/iv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="10dp"
            android:text="取消"
            android:textColor="#FFFFFFFF"
            android:layout_gravity="center_vertical|left"
            android:textSize="14sp"
            android:shadowRadius="3.0"/>

        <ImageView
            android:id="@+id/ic_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="10dp"
            android:src="@drawable/ic_mask_back"/>

        <TextView
            android:id="@+id/iv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:shadowRadius="3.0"
            android:padding="10dp"
            android:text="@string/media_dialog_confrim"
            android:textColor="@color/mm_color_main"
            android:layout_gravity="center_vertical|right"
            android:textSize="16sp" />

    </FrameLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_width="match_parent"
        android:layout_height="34dp" />
</LinearLayout>