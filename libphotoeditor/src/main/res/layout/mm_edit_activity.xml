<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".JdmmPhotoEditActivity">

    <FrameLayout
        android:id="@+id/fl_back"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/mm_title_height"
            tools:background="@android:color/white">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/btn_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:text="@string/icon_prompt_close"
                android:textColor="@android:color/white"
                android:textSize="@dimen/JMEIcon_22"
                tools:ignore="SpUsage"
                tools:textColor="@android:color/black" />

            <TextView
                android:id="@+id/btn_option_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:text="@string/cancel"
                android:textColor="@android:color/white"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                tools:ignore="SpUsage"
                tools:textColor="@android:color/black"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/btn_option_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:layout_marginRight="16dp"
                android:text="@string/media_dialog_ok"
                android:textColor="@color/mm_color_btn_me"
                android:textSize="@dimen/sp_14"
                android:visibility="gone"
                tools:ignore="SpUsage"
                tools:visibility="visible" />

        </RelativeLayout>

        <com.jd.oa.lib.editor.photo.paste.view.PasteLayout
            android:id="@+id/mSavePasteLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/mm_title_height"
            android:layout_marginBottom="48dp"
            android:gravity="center_horizontal" />

        <com.jd.oa.lib.editor.photo.paste.view.CsViewPager
            android:id="@+id/editViewPager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mm_title_height"
            android:layout_marginBottom="48dp"
            android:background="@android:color/black" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_gravity="bottom"
            android:layout_marginLeft="16dp"
            android:layout_marginBottom="48dp"
            android:background="@drawable/mm_edit_memu_bg"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:visibility="gone"
            tools:background="@android:color/holo_green_dark"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/radio_mosaic"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_gravity="bottom"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="48dp"
            android:background="@drawable/mm_edit_memu_bg"
            android:orientation="horizontal"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:visibility="gone"
            tools:background="@android:color/holo_green_dark"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/radio_mosaic_small"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:padding="12dp"
                android:src="@drawable/ic_mosaic_selector" />

            <ImageView
                android:id="@+id/radio_mosaic_middle"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:padding="10dp"
                android:src="@drawable/ic_mosaic_selector" />

            <ImageView
                android:id="@+id/radio_mosaic_big"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp"
                android:padding="8dp"
                android:src="@drawable/ic_mosaic_selector" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/mFLBottom"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_gravity="bottom"
            android:background="@drawable/mm_edit_memu_bg"
            android:gravity="center"
            android:orientation="horizontal">


            <LinearLayout
                android:id="@+id/mFlLeft"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight=".15"
                android:orientation="vertical">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/ifv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:text="@string/icon_direction_revoke"
                    android:textColor="@color/mm_color_edit_menu_disable"
                    android:textSize="@dimen/JMEIcon_24"
                    tools:ignore="SpUsage"
                    tools:textColor="@color/mm_color_edit_menu" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/mFlCenter"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight=".55"
                android:gravity="center"
                android:orientation="horizontal"
                tools:background="@android:color/white">

                <LinearLayout
                    android:id="@+id/mBtnPaint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="25dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/ifv_pain"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_pencil"
                        android:textColor="@color/mm_color_edit_menu"
                        android:textSize="@dimen/JMEIcon_24"
                        tools:ignore="SpUsage" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mBtnFonts"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="25dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/ifv_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_edit_text"
                        android:textColor="@color/mm_color_edit_menu"
                        android:textSize="@dimen/JMEIcon_24"
                        tools:ignore="SpUsage" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mBtnEdit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="25dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.jd.oa.ui.IconFontView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_cut_out"
                        android:textColor="@color/mm_color_edit_menu"
                        android:textSize="@dimen/JMEIcon_24"
                        tools:ignore="SpUsage" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mBtnMosaic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/ifv_mosaic"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_mosaic"
                        android:textColor="@color/mm_color_edit_menu"
                        android:textSize="@dimen/JMEIcon_24"
                        tools:ignore="SpUsage" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/mFlRight"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight=".25"
                android:gravity="right"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/btn_finish"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_marginRight="16dp"
                    android:background="@drawable/mm_button_b_me"
                    android:gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:singleLine="true"
                    android:text="@string/mm_complete"
                    android:textColor="@android:color/white"
                    android:textSize="14dp" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>

    <com.jd.oa.lib.editor.photo.paste.fonts.FontToolBar
        android:id="@+id/mFontToolBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/holo_red_dark"
        android:visibility="gone" />
</FrameLayout>