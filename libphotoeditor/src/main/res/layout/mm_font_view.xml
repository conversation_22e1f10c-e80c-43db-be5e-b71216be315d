<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.jd.oa.lib.editor.photo.paste.view.CsTextView
        android:id="@+id/back_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="none"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:shadowColor="@color/black"
        android:shadowRadius="1"
        android:text="@string/mm_edit_dtype"
        android:textColor="#FFFFFFFF"
        android:textSize="44dp" />

    <com.jd.oa.lib.editor.photo.paste.view.CsTextView
        android:id="@+id/front_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:ellipsize="none"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:text="@string/mm_edit_dtype"
        android:textColor="#FFFFFFFF"
        android:textSize="44dp" />

</FrameLayout>



