<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <View
        android:id="@+id/mVLine"
        android:layout_width="1dp"
        android:layout_height="8dp"
        android:layout_gravity="center_vertical"
        android:background="#FF979797" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mLayout"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/mText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:singleLine="true"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/mHLine"
            android:layout_width="match_parent"
            android:layout_height="3dp"
            android:layout_gravity="bottom"
            android:layout_marginLeft="2.5dp"
            android:layout_marginRight="2.5dp"
            android:background="@drawable/mm_indicator"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/mText"
            app:layout_constraintRight_toRightOf="@id/mText" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>