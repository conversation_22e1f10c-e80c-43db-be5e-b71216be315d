package com.jd.oa.lib.editor.pub.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Build;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.viewpager.widget.ViewPager;

import com.jd.oa.lib.editor.photo.R;
import com.jd.oa.lib.editor.util.DataConfig;
import com.jd.oa.utils.UnitUtils;

/**
 * des: ViewPage title
 * <EMAIL>
 * erp:zhoumin117
 *
 * @author： zhoumin6
 * @date： 2020 -08-05.18:01
 */
public class ViewPagerTab extends HorizontalScrollView {


    /**
     * M cur index
     */
    private int mCurIndex = 0;
    /**
     * ATTRS
     */
    private static final int[] ATTRS = new int[]{android.R.attr.textSize, android.R.attr.textColor};

    /**
     * Default tab layout params
     */
    private LinearLayout.LayoutParams defaultTabLayoutParams;
    /**
     * Expanded tab layout params
     */
    private LinearLayout.LayoutParams expandedTabLayoutParams;

    /**
     * Page listener
     */
    private final PageListener pageListener = new PageListener();
    /**
     * Delegate page listener
     */
    private ViewPager.OnPageChangeListener delegatePageListener;

    /**
     * Tabs container
     */
// view 容器
    private LinearLayout tabsContainer;
    /**
     * Pager
     */
    private ViewPager pager;
    /**
     * Tab count
     */
// 总数量
    private int tabCount;
    /**
     * Current position
     */
// 当前位置
    private int currentPosition = 0;

    /**
     * Should expand
     */
    private boolean shouldExpand = false;

    /**
     * Tab padding
     */
    private float tabPadding = 0;

    /**
     * Scroll offset
     */
    private float scrollOffset = 52;

    /**
     * Tab text size
     */
    private float tabTextSize = 12;
    /**
     * Tab text sizep selected
     */
    private float tabTextSizepSelected = 14;
    /**
     * Tab text color normal
     */
    private int tabTextColorNormal = 0xFF666666;
    /**
     * Tab text color selected
     */
    private int tabTextColorSelected = 0xFF666666;
    /**
     * Last scroll x
     */
    private int lastScrollX = 0;
    /**
     * 第一条纵向分隔线是否显示
     */
    private boolean isFirstLineVisible = true;

    /**
     * Is show indicator
     */
    private boolean isShowIndicator = false;

    /**
     * SHOW_TAB_COUNT
     */
    private static final int SHOW_TAB_COUNT = 3;// 屏幕显示的标签个数

    /**
     * View pager tab
     *
     * @param context context
     */
    public ViewPagerTab(Context context) {
        this(context, null);
    }

    /**
     * View pager tab
     *
     * @param context context
     * @param attrs   attrs
     */
    public ViewPagerTab(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     * Sets tab text color normal *
     *
     * @param tabTextColorNormal tab text color normal
     */
    public void setTabTextColorNormal(int tabTextColorNormal) {
        this.tabTextColorNormal = tabTextColorNormal;
        updateTabStyles();
    }

    /**
     * Sets tab text color selected *
     *
     * @param tabTextColorSelected tab text color selected
     */
    public void setTabTextColorSelected(int tabTextColorSelected) {
        this.tabTextColorSelected = tabTextColorSelected;
        updateTabStyles();
    }

    /**
     * View pager tab
     *
     * @param context  context
     * @param attrs    attrs
     * @param defStyle def style
     */
    public ViewPagerTab(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);

        setFillViewport(true);
        setWillNotDraw(false);

        tabsContainer = new LinearLayout(context);
        tabsContainer.setOrientation(LinearLayout.HORIZONTAL);
        // tabsContainer.setLayoutParams(new
        // LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
        addView(tabsContainer);

        // get custom attrs

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.ViewPagerTab);

        tabPadding = a.getDimension(R.styleable.ViewPagerTab_ptTabPaddingLeftRight, tabPadding);

        scrollOffset = a.getDimension(R.styleable.ViewPagerTab_ptScrollOffset, scrollOffset);
        tabTextSize = a.getDimension(R.styleable.ViewPagerTab_ptTextSize, tabTextSize);
        tabTextSizepSelected = a.getDimension(R.styleable.ViewPagerTab_ptTextSizeSelected, tabTextSizepSelected);
        shouldExpand = a.getBoolean(R.styleable.ViewPagerTab_ptShouldExpand, shouldExpand);
        isFirstLineVisible = a.getBoolean(R.styleable.ViewPagerTab_ptFirstLine, isFirstLineVisible);

        tabTextColorNormal = a.getColor(R.styleable.ViewPagerTab_ptTextColorNormal, tabTextColorNormal);
        tabTextColorSelected = a.getColor(R.styleable.ViewPagerTab_ptTextColorSelected, tabTextColorSelected);


        isShowIndicator= a.getBoolean(R.styleable.ViewPagerTab_ptShowIndicator, isShowIndicator);
//        indicatorHeight = a.getDimensionPixelSize(R.styleable.ViewPagerTab_ptIndicatorHeight, indicatorHeight);
//        lineBottomPadding = a.getDimensionPixelSize(R.styleable.ViewPagerTab_ptBottomPadding, lineBottomPadding);

        a.recycle();

        defaultTabLayoutParams = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT);
        expandedTabLayoutParams = new LinearLayout.LayoutParams(0, LayoutParams.MATCH_PARENT, 1.0f);

//        rectPaint = new Paint();
//        rectPaint.setAntiAlias(true);
//        rectPaint.setStyle(Paint.Style.FILL);

    }

    /**
     * Sets view pager *
     *
     * @param pager pager
     */
    public void setViewPager(ViewPager pager) {
        this.pager = pager;

        if (pager.getAdapter() == null) {
            throw new IllegalStateException("ViewPager does not have adapter instance.");
        }

        pager.addOnPageChangeListener(pageListener);
        notifyDataSetChanged();
    }

    /**
     * Sets on page change listener *
     *
     * @param listener listener
     */
    public void setOnPageChangeListener(ViewPager.OnPageChangeListener listener) {
        this.delegatePageListener = listener;
    }

    /**
     * Notify data set changed
     */
    public void notifyDataSetChanged() {
        if (tabsContainer != null) {
            tabsContainer.removeAllViews();
        }
        if (pager != null) {
            tabCount = pager.getAdapter().getCount();
            for (int i = 0; i < tabCount; i++) {
                if (pager.getAdapter() instanceof LineTextProvider) {
                    addLinTextTab(i, pager.getAdapter().getPageTitle(i).toString(), i == 0 ? isFirstLineVisible : true);
                } else {
                    addTextTab(i, pager.getAdapter().getPageTitle(i).toString());
                }
            }
        }

        updateTabStyles();

        getViewTreeObserver().addOnGlobalLayoutListener(new OnGlobalLayoutListener() {

            @Override
            public void onGlobalLayout() {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN) {
                    getViewTreeObserver().removeGlobalOnLayoutListener(this);
                } else {
                    getViewTreeObserver().removeOnGlobalLayoutListener(this);
                }
                if (pager != null) {
                    currentPosition = pager.getCurrentItem();
                }
                scrollToChild(currentPosition, 0);
            }
        });

    }

    /**
     * Add text tab *
     *
     * @param position position
     * @param title    title
     */
    private void addTextTab(final int position, String title) {
        TextView tab = new TextView(getContext());
        tab.setText(title);
        tab.setGravity(Gravity.CENTER);
        tab.setSingleLine();
        addTab(position, tab);
    }

    /**
     * Add lin text tab *
     *
     * @param position   position
     * @param title      title
     * @param isShowLine 是否显示线
     */
    private void addLinTextTab(final int position, String title, boolean isShowLine) {
        PagerTabItem tab = new PagerTabItem(getContext());
        tab.getTextView().setText(title);
        tab.setGravity(Gravity.CENTER);
        tab.setLineVisible(isShowLine);
        addTab(position, tab);
    }


    /**
     * Add tab *
     *
     * @param position position
     * @param tab      tab
     */
    private void addTab(final int position, View tab) {
        if (tabsContainer != null && tab != null) {
            tab.setFocusable(true);
            tab.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (pager != null) {
                        pager.setCurrentItem(position);
                    }
                }
            });
            if (tab instanceof PagerTabItem) {
                ((PagerTabItem) tab).getTextLayout().setPadding((int) tabPadding, 0, (int) tabPadding, 0);
            } else {
                tab.setPadding((int) tabPadding, 0, (int) tabPadding, 0);
            }

            tabsContainer.addView(tab, position, shouldExpand ? expandedTabLayoutParams : defaultTabLayoutParams);
        }
    }

    /**
     * Update tab styles
     */
    private void updateTabStyles() {
        if (tabsContainer != null) {
            for (int i = 0; i < tabsContainer.getChildCount(); i++) {
                View v = tabsContainer.getChildAt(i);
                TextView tab = null;
                if (v instanceof TextView) {
                    tab = (TextView) v;
                } else if (v instanceof PagerTabItem) {
                    tab = ((PagerTabItem) v).getTextView();
                    if(isShowIndicator){
                        View hLive = ((PagerTabItem) v).getLineV();
                        if (hLive != null) {
                            hLive.setVisibility(i == mCurIndex ? View.VISIBLE : View.GONE);
                        }
                    }
                }
                if (tab != null) {
                    if (i == mCurIndex) {
                        tab.setTextSize(TypedValue.COMPLEX_UNIT_DIP, UnitUtils.px2dip(getContext(), tabTextSizepSelected));
                        tab.getPaint().setFakeBoldText(true);
                        tab.setTextColor(tabTextColorSelected);
                    } else {
                        tab.setTextColor(tabTextColorNormal);
                        tab.getPaint().setFakeBoldText(false);
                        tab.setTextSize(TypedValue.COMPLEX_UNIT_DIP, UnitUtils.px2dip(getContext(), tabTextSize));
                    }
                }

            }
        }
    }

    /**
     * Scroll to child *
     *
     * @param position position
     * @param offset   offset
     */
    private void scrollToChild(int position, int offset) {
        if (tabCount == 0) {
            return;
        }
        int newScrollX = tabsContainer.getChildAt(position).getLeft() + offset;
        if (position > 0 || offset > 0) {
            newScrollX -= scrollOffset;
        }
        if (newScrollX != lastScrollX) {
            lastScrollX = newScrollX;
            scrollTo(newScrollX, 0);
        }
    }


    /**
     * Page listener
     */
    private class PageListener implements ViewPager.OnPageChangeListener {

        /**
         * On page scrolled *
         *
         * @param position             position
         * @param positionOffset       position offset
         * @param positionOffsetPixels position offset pixels
         */
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            currentPosition = position;
//            currentPositionOffset = positionOffset;
            if (tabsContainer != null && tabsContainer.getChildAt(position) != null) {
                scrollToChild(position, (int) (positionOffset * tabsContainer.getChildAt(position).getWidth()));
            }
            invalidate();
            if (delegatePageListener != null) {
                delegatePageListener.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }
        }

        /**
         * On page scroll state changed *
         *
         * @param state state
         */
        @Override
        public void onPageScrollStateChanged(int state) {
            if (state == ViewPager.SCROLL_STATE_IDLE && pager != null) {
                scrollToChild(pager.getCurrentItem(), 0);
            }

            if (delegatePageListener != null) {
                delegatePageListener.onPageScrollStateChanged(state);
            }
        }

        /**
         * On page selected *
         *
         * @param position position
         */
        @Override
        public void onPageSelected(int position) {
            mCurIndex = position;
            updateTabStyles();
            if (delegatePageListener != null) {
                delegatePageListener.onPageSelected(position);
            }
        }
    }

    /**
     * Line text provider
     */
    public interface LineTextProvider {
    }


    /**
     * Pager tab item
     */
    public static class PagerTabItem extends LinearLayout {

        /**
         * Textview
         */
        private TextView textview;
        /**
         * M layout
         */
        private ViewGroup mLayout;
        /**
         * M line
         */
        private View mLineV;

        /**
         * M h line
         */
        private View mLineH;
        /**
         * Lout inflater
         */
        private LayoutInflater loutInflater;

        /**
         * Gets textview *
         *
         * @return the textview
         */
        public TextView getTextview() {
            return textview;
        }


        /**
         * Pager tab item
         *
         * @param context context
         */
        public PagerTabItem(Context context) {
            super(context);
            init(context);
        }

        /**
         * Init *
         *
         * @param context context
         */
        private void init(Context context) {
            this.setOrientation(HORIZONTAL);
            loutInflater = LayoutInflater.from(context);
            loutInflater.inflate(R.layout.mm_item_tab,this, true);
            mLayout = findViewById(R.id.mLayout);
            // 文字
            textview = findViewById(R.id.mText);
            mLineV = findViewById(R.id.mVLine);
            mLineH = findViewById(R.id.mHLine);
            LayoutParams layoutParams = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER;
            this.setLayoutParams(layoutParams);
            setVerticalGravity(Gravity.CENTER);
        }

        /**
         * Sets line visible *
         *
         * @param is is
         */
        public void setLineVisible(boolean is) {
            if (mLineV != null) {
                mLineV.setVisibility(is ? VISIBLE : GONE);
            }
        }

        /**
         * Gets h line *
         *
         * @return the h line
         */
        public View getLineV() {
            return mLineH;
        }

        /**
         * Gets text view *
         *
         * @return the text view
         */
        public ViewGroup getTextLayout() {
            return mLayout;
        }

        /**
         * Gets text view *
         *
         * @return the text view
         */
        public TextView getTextView() {
            return textview;
        }
    }


}
