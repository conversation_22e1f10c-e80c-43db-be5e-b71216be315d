package com.jd.oa.lib.editor.photo.paste.view;


import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.jd.oa.lib.editor.photo.R;
import com.jd.oa.lib.editor.photo.paste.PasteBean;
import com.jd.oa.lib.editor.photo.paste.fonts.FontToolPresenter;
import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.lib.editor.pub.data.ReBean;

/**
 * Font view
 *
 * <AUTHOR> zhoumin Email: <EMAIL>
 * @date : 2018/8/1 Time: 上午14:27 Description: 图片 与自动控制大小的TextView ,进行缩放，旋转，平移操作
 */
public class FontView extends FrameLayout implements IViewType {

    /**
     * Is edit text
     */
    boolean isEditText = false;

    /**
     * 默认文案
     */
    private String mDefaultText;
    /**
     * 用于旋转缩放的Bitmap
     */
    private String mText;

    /**
     * Mini scale
     */
    final float MINI_SCALE = 0.2F;
    /**
     * Max scale
     */
    final float MAX_SCALE = 2.5F;

    /**
     * 内容容器
     */
    private FrameLayout mFlRoot;
    /**
     * 阴影与背景文字
     */
    private CsTextView mBackText;
    /**
     * 前景文字
     */
    private CsTextView mFrontText;

    /**
     * 恢复
     */
    protected boolean isRecover = false;

    /**
     * 当前所处的状态
     */
    private int mStatus = STATUS_INIT;

    /**
     * 外边框与图片之间的间距, 单位是dip
     */
    protected int framePadding = DEFAULT_FRAME_PADDING;

    /**
     * 外边框颜色
     */
    private int frameColor = DEFAULT_FRAME_COLOR;

    /**
     * 外边框线条粗细, 单位是 dip
     */
    private int frameWidth = DEFAULT_FRAME_WIDTH;

    /**
     * 是否处于可以缩放，平移，旋转状态
     */
    private boolean isEditable = DEFAULT_EDITABLE;
    /**
     * 操作线框是否显示
     */
    private boolean isControlVisible = true;

    /**
     * 是否已经测量过
     */
    private boolean isOnMeasured = false;

    /**
     * 上一点
     */
    private final Point mPreMovePointF = new Point();
    /**
     * 当前点
     */
    private final Point mCurMovePointF = new Point();

    /**
     * 中心点坐标，相对于其父类布局而言的
     */
    protected final Point mCenterPoint = new Point();
    /**
     * 中心点相对父布局所处比例
     */
    protected final PointF mCenterPointRate = new PointF();


    /**
     * View的宽度和高度，随着图片的旋转而变化(不包括控制旋转，缩放图片的宽高)
     */
    private int mViewWidth, /**
     * M view height
     */
    mViewHeight;

    /**
     * 图片的旋转角度
     */
    public float mDegree = DEFAULT_DEGREE;

    /**
     * 图片的缩放比例
     */
    public float mScale = DEFAULT_SCALE;
    /**
     * 图片的缩放比例
     */
    private float mLastScale = 0;

    /**
     * 距离父类布局的左间距
     */
    private int mViewPaddingLeft;

    /**
     * 距离父类布局的上间距
     */
    private int mViewPaddingTop;

    /**
     * 图片四个点坐标 左上角
     */
    private Point mLtPoint = new Point();
    /**
     * 图片四个点坐标 右上角
     */
    private Point mRtPoint = new Point();
    /**
     * 图片四个点坐标 右下角
     */
    private Point mRbPoint = new Point();
    /**
     * 图片四个点坐标 右下角
     */
    private Point mLbPoint = new Point();

    /**
     * 用于缩放，旋转的控制点的坐标
     */
    private Point mRsPoint = new Point();
    /**
     * 旋转缩放图标
     */
    private Drawable mRsDrawable;
    /**
     * 旋转缩放位置
     */
    protected int mRsLocation = RIGHT_BOTTOM;
    /**
     * 旋转缩放图标宽高
     */
    protected int mRsDrawableWidth = 0,
    /**
     * M rs drawable height
     */
    mRsDrawableHeight = 0;

    /**
     * 用于关闭图标
     */
    private Point mClosePoint = new Point();
    /**
     * 删除图标
     */
    private Drawable mCloseDrawable;
    /**
     * 删除图标位置
     */
    private int mCloseLocation = RIGHT_TOP;
    /**
     * 删除图标宽高
     */
    private int mCloseDrawableWidth = 0,
    /**
     * M close drawable height
     */
    mCloseDrawableHeight = 0;


    /**
     * 画外围框的Path
     */
    private final Path mPath = new Path();

    /**
     * 最后显示状态
     */
    private boolean mLastControlVisible = false;

    /**
     * 点击限制X 按下值
     */
    private float mDownValueX;
    /**
     * 点击限制Y 按下值
     */
    private float mDownValueY;
    /**
     * 是否在移动
     */
    private boolean mMoving = false;


    /**
     * 图片在旋转时x方向的偏移量
     */
    protected int offsetX;
    /**
     * 图片在旋转时y方向的偏移量
     */
    protected int offsetY;


    /**
     * 父容器宽高
     */
    int parentWidth;
    /**
     * 父容器宽高
     */
    int parentHeight;


    /**
     * 双指操作 存储两点间的距离
     */
    private double mDistance = 0f;

    /**
     * 手势
     */
    private GestureDetector mGesture;


    /**
     * 回调
     */
    protected PasteListener mPasterListener;

    /**
     * Font view
     *
     * @param context context
     * @param attrs   attrs
     */
    public FontView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     * Font view
     *
     * @param context context
     */
    public FontView(Context context) {
        this(context, null);
    }

    /**
     * Font view
     *
     * @param context  context
     * @param attrs    attrs
     * @param defStyle def style
     */
    public FontView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        obtainStyledAttributes(context, attrs);
        setWillNotDraw(false);
        init();
    }


    /**
     * Init
     */
    protected void init() {
        mFlRoot = (FrameLayout) LayoutInflater.from(getContext()).inflate(R.layout.mm_font_view, null);
        mBackText = mFlRoot.findViewById(R.id.back_text);
        mFrontText = mFlRoot.findViewById(R.id.front_text);
        this.setPadding(framePadding, framePadding, framePadding, framePadding);
        LayoutParams fl = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);


        fl.gravity = Gravity.CENTER;
        addView(mFlRoot, fl);

        mDefaultText = getResources().getString(R.string.mm_edit_dtype);
        mText = mDefaultText;

        mBackText.setText(mDefaultText);
        mFrontText.setText(mDefaultText);
        updateTextWidth();

        mFlRoot.addOnLayoutChangeListener(new OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                transformDraw();
            }
        });

        mGesture = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (isPointInRect(new Point((int) e.getX(), (int) e.getY()), false)) {
                    if (mLastControlVisible) {
                        setControlVisible(true);
                        if (mPasterListener != null) {
                            mPasterListener.doubleType(FontView.this, mReBean, mStyleBean);
                        }
                    }
                } else {
                    setControlVisible(false);
                }
                return super.onDoubleTap(e);
            }

        });


        mPaint.setAntiAlias(true);
        mPaint.setColor(frameColor);
        mPaint.setStrokeWidth(frameWidth);
        mPaint.setStyle(Paint.Style.STROKE);

        if (mRsDrawable != null) {
            mRsDrawableWidth = mRsDrawable.getIntrinsicWidth();
            mRsDrawableHeight = mRsDrawable.getIntrinsicHeight();
        }

        if (mCloseDrawable != null) {
            mCloseDrawableWidth = mCloseDrawable.getIntrinsicWidth();
            mCloseDrawableHeight = mCloseDrawable.getIntrinsicHeight();
        }

        transformDraw();
    }


    /**
     * 获取自定义属性
     *
     * @param context context
     * @param attrs   attrs
     */
    private void obtainStyledAttributes(Context context, AttributeSet attrs) {
        TypedArray mTypedArray = context.obtainStyledAttributes(attrs, R.styleable.BasePasteView);


        framePadding = mTypedArray.getDimensionPixelSize(R.styleable.BasePasteView_bpFramePadding, framePadding);
        frameWidth = mTypedArray.getDimensionPixelSize(R.styleable.BasePasteView_bpFrameWidth, frameWidth);
        frameColor = mTypedArray.getColor(R.styleable.BasePasteView_bpFrameColor, DEFAULT_FRAME_COLOR);
        mScale = mTypedArray.getFloat(R.styleable.BasePasteView_bpScale, DEFAULT_SCALE);
        mDegree = mTypedArray.getFloat(R.styleable.BasePasteView_bpDegree, DEFAULT_DEGREE);

        mRsDrawable = mTypedArray.getDrawable(R.styleable.BasePasteView_bpRotateScaleDrawable);
        mRsLocation = mTypedArray.getInt(R.styleable.BasePasteView_bpRotateScaleLocation, RIGHT_BOTTOM);

        mCloseDrawable = mTypedArray.getDrawable(R.styleable.BasePasteView_bpCloseDrawable);
        mCloseLocation = mTypedArray.getInt(R.styleable.BasePasteView_bpCloseLocation, RIGHT_TOP);

        isEditable = mTypedArray.getBoolean(R.styleable.BasePasteView_bpEditable, DEFAULT_EDITABLE);

        mTypedArray.recycle();

    }


    /**
     * On draw *
     *
     * @param canvas canvas
     */
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //处于可编辑状态才画边框和控制图标

        if (isEditable && isControlVisible) {
            mPath.reset();
            mPath.moveTo(mLtPoint.x, mLtPoint.y);
            mPath.lineTo(mRtPoint.x, mRtPoint.y);
            mPath.lineTo(mRbPoint.x, mRbPoint.y);
            mPath.lineTo(mLbPoint.x, mLbPoint.y);
            mPath.lineTo(mLtPoint.x, mLtPoint.y);
            mPath.lineTo(mRtPoint.x, mRtPoint.y);

            canvas.drawPath(mPath, mPaint);
            //画旋转, 缩放图标
            if (mRsDrawable != null) {
                mRsDrawable.setBounds(mRsPoint.x - mRsDrawableWidth / 2,
                        mRsPoint.y - mRsDrawableHeight / 2, mRsPoint.x + mRsDrawableWidth
                                / 2, mRsPoint.y + mRsDrawableHeight / 2);
                mRsDrawable.draw(canvas);
            }


            //画关闭
            if (mCloseDrawable != null) {
                mCloseDrawable.setBounds(mClosePoint.x - mCloseDrawableWidth / 2,
                        mClosePoint.y - mCloseDrawableHeight / 2, mClosePoint.x + mCloseDrawableWidth
                                / 2, mClosePoint.y + mCloseDrawableHeight / 2);
                mCloseDrawable.draw(canvas);
            }

        }
    }

    /**
     * 恢复 坐标 角度 缩放值
     *
     * @param bean    记录数据
     * @param pWidth  p width
     * @param pHeight p height
     */
    protected void recover(PasteBean bean, int pWidth, int pHeight) {
        if (bean != null && bean.centerPoint != null) {
            setControlVisible(false);
            isRecover = true;
            mCenterPoint.set(bean.centerPoint.x, bean.centerPoint.y);
            mCenterPointRate.set(bean.centerPoint.x / (float) pWidth, bean.centerPoint.y / (float) pHeight);
            mDegree = bean.degree;
            mScale = bean.scale;
        }
    }

    /**
     * 设置是否处于可缩放，平移，旋转状态
     *
     * @param isEditable 可编辑状态
     */
    @Override
    public void setEditable(boolean isEditable) {
        this.isEditable = isEditable;
        invalidate();
    }


    /**
     * Sets control visible *
     *
     * @param controlVisible control visible
     */
    @Override
    public void setControlVisible(boolean controlVisible) {
        if (!controlVisible) {
            mLastControlVisible = false;
        }
        isControlVisible = controlVisible;
        if (getParent() != null) {
            getParent().requestDisallowInterceptTouchEvent(controlVisible);
        }
        invalidate();
    }

    /**
     * Is control visible boolean
     *
     * @return the boolean
     */
    @Override
    public boolean isControlVisible() {
        return isControlVisible;
    }

    /**
     * 是否可编辑
     *
     * @return the boolean
     */
    @Override
    public boolean isEditable() {
        return isEditable;
    }

    /**
     * Gets view *
     *
     * @return the view
     */
    @Override
    public View getView() {
        return this;
    }


    /**
     * Is view enable boolean
     *
     * @return the boolean
     */
    @Override
    public boolean isViewEnable() {
        return isControlVisible && isEditable;
    }

    /**
     * 调整View的大小，位置
     */
    protected void adjustLayout() {
        int actualWidth = mViewWidth + mRsDrawableWidth;
        int actualHeight = mViewHeight + mRsDrawableHeight;

        int newPaddingLeft = mCenterPoint.x - actualWidth / 2;
        int newPaddingTop = mCenterPoint.y - actualHeight / 2;

        if (mViewPaddingLeft != newPaddingLeft || mViewPaddingTop != newPaddingTop) {
            mViewPaddingLeft = newPaddingLeft;
            mViewPaddingTop = newPaddingTop;
//			layout(newPaddingLeft, newPaddingTop, newPaddingLeft + actualWidth, newPaddingTop + actualHeight);
        }

        layout(newPaddingLeft, newPaddingTop, newPaddingLeft + actualWidth, newPaddingTop + actualHeight);
    }


    /**
     * 设置回调监听
     *
     * @param r r
     */
    @Override
    public void setListener(PasteListener r) {
        mPasterListener = r;
    }


    /**
     * 点在矩形区域内
     *
     * @param point point
     * @param isOut is out
     * @return the boolean
     */
    @Override
    public boolean isPointInRect(Point point, boolean isOut) {
        if (isOut) {
            point = new Point(point.x - getLeft(), point.y - getTop());
        }
        return PointInRect.isPointInRect(point, mLtPoint, mRtPoint, mRbPoint, mLbPoint);
    }


    /**
     * Sets center point *
     *
     * @param pWidth  p width
     * @param pHeight p height
     */
    @Override
    public void setCenterPoint(int pWidth, int pHeight) {
        if (pWidth >= 0 && pHeight >= 0) {
            parentWidth = pWidth;
            parentHeight = pHeight;
            int offsetW = Utils.getRandomNumber(0, parentWidth / 8);
            int offsetH = Utils.getRandomNumber(0, parentHeight / 8);
            if (isRecover) {
                if (mCenterPoint.x > pWidth) {
                    mCenterPoint.x = pWidth;
                }
                if (mCenterPoint.y > pHeight) {
                    mCenterPoint.y = pHeight;
                }
            } else {
                mCenterPoint.set(parentWidth / 2 + (Utils.getRandomNumber(0, 10) < 5 ? offsetW : -offsetW), parentHeight / 3 - offsetH);
            }
            mCenterPointRate.set(mCenterPoint.x / (float) pWidth, mCenterPoint.y / (float) pHeight);
            isOnMeasured = true;
        }
    }

    /**
     * 根据
     *
     * @param pWidth  parentWidth
     * @param pHeight parentHeight
     */
    @Override
    public void updateCenterPoint(int pWidth, int pHeight) {
        if (pWidth > 0 && pHeight > 0) {
            parentWidth = pWidth;
            parentHeight = pHeight;
            if (mCenterPoint.x > pWidth) {
                mCenterPoint.x = pWidth;
                mCenterPointRate.x = mCenterPoint.x / (float) pWidth;
            } else {
//                mCenterPoint.x = (int) (pWidth * mCenterPointRate.x);
                if (mCenterPoint.x > pWidth) {
                    mCenterPoint.x = pWidth;
                    mCenterPointRate.x = 1f;
                } else if (mCenterPoint.x < 0) {
                    mCenterPoint.x = 0;
                    mCenterPointRate.x = 0f;
                }
            }
            if (mCenterPoint.y > pHeight) {
                mCenterPoint.y = pHeight;
                mCenterPointRate.y = mCenterPoint.y / (float) pHeight;
            } else {
//                mCenterPoint.y = (int) (pWidth * mCenterPointRate.y);
                if (mCenterPoint.y > pHeight) {
                    mCenterPoint.y = pHeight;
                    mCenterPointRate.y = 1f;
                } else if (mCenterPoint.y < 0) {
                    mCenterPoint.y = 0;
                    mCenterPointRate.y = 0f;
                }
            }
            transformDraw();
        }
    }

    /**
     * On measure *
     *
     * @param widthMeasureSpec  width measure spec
     * @param heightMeasureSpec height measure spec
     */
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        //获取SingleTouchView所在父布局的中心点
        if (getParent() instanceof ViewGroup) {
            ViewGroup mViewGroup = (ViewGroup) getParent();
            if (null != mViewGroup) {
                parentWidth = mViewGroup.getWidth();
                parentHeight = mViewGroup.getHeight();
                if (parentWidth == 0 || parentHeight == 0) {
                    parentWidth = MeasureSpec.getSize(widthMeasureSpec);
                    parentHeight = MeasureSpec.getSize(heightMeasureSpec);
                }
                if (!isOnMeasured) {
                    int offsetW = Utils.getRandomNumber(0, parentWidth / 8);
                    int offsetH = Utils.getRandomNumber(0, parentHeight / 8);
                    if (isRecover) {
                        if (mCenterPoint.x > parentWidth) {
                            mCenterPoint.x = parentWidth;
                        }
                        if (mCenterPoint.y > parentHeight) {
                            mCenterPoint.y = parentHeight;
                        }
                    } else {
                        mCenterPoint.set(parentWidth / 2 + (Utils.getRandomNumber(0, 10) < 5 ? offsetW : -offsetW), parentHeight / 3 - offsetH);
                    }
                    mCenterPointRate.set(mCenterPoint.x / (float) parentWidth, mCenterPoint.y / (float) parentWidth);
                    isOnMeasured = true;
                }
            }
        }
    }


    /**
     * Remove self
     */
    protected void removeSelf() {
        if (getParent() instanceof ViewGroup) {
            ViewGroup mViewGroup = (ViewGroup) getParent();
            if (null != mViewGroup) {
                mViewGroup.removeView(this);
            }
        }
        if (mPasterListener != null) {
            mPasterListener.removed(this);
        }
    }


    /**
     * On touch event boolean
     *
     * @param event event
     * @return the boolean
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isEditable) {
            return super.onTouchEvent(event);
        }
        int pointCount = event.getPointerCount();
        switch (event.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                mDownValueX = event.getX();
                mDownValueY = event.getY();

                mPreMovePointF.set((int) (event.getX() + mViewPaddingLeft), (int) (event.getY() + mViewPaddingTop));
                mStatus = judgeStatus((int) event.getX(), (int) event.getY());
                if (mStatus == STATUS_DRAG) {
                    if (isPointInRect(new Point((int) event.getX(), (int) event.getY()), false)) {
                        mStatus = STATUS_DRAG;
                        mLastControlVisible = isControlVisible;
                        setControlVisible(true);
                        setLastView();


                    } else {
                        mStatus = STATUS_INIT;
                        setControlVisible(false);
                    }
                } else {
                    mLastControlVisible = isControlVisible;
                    if (mStatus != STATUS_CLOSE) {
                        setControlVisible(true);
                        setLastView();
                    }
                }

            case MotionEvent.ACTION_POINTER_DOWN:
//                if (pointCount >= 2 && isPointInRect(new Point((int) event.getX(1), (int) event.getY(1)), false)) {
//                    mStatus = STATUS_ROTATE_ZOOM_INSIDE;
//                    //获取两点间距离
//                    mDistance = Utils.getDistance(event);
//                    mLastControlVisible = isControlVisible;
//                    if (mPasterListener != null) {
//                        mPasterListener.setLastView(this,mReBean,mStyleBean);
//                    }
//                }
                break;
            case MotionEvent.ACTION_POINTER_UP:
                mStatus = STATUS_INIT;
                mLastScale = 0;
                break;
            case MotionEvent.ACTION_UP:
                mMoving = false;
                if (mStatus == judgeStatus((int) event.getX(), (int) event.getY())) {
                    if (mStatus == STATUS_CLOSE) {
                        removeSelf();
                    }
                }
                mStatus = STATUS_INIT;
                break;
            case MotionEvent.ACTION_MOVE:
                /* 点击限制X 移动*/
                float mMoveValueX = event.getX();
                /* 点击限制Y 移动*/
                float mMoveValueY = event.getY();
                if (Math.abs(mMoveValueX - mDownValueX) > mRsDrawableWidth / 2 || Math.abs(mMoveValueY - mDownValueY) > mRsDrawableWidth / 2) {
                    mMoving = true;
                } else {
                    if (!mMoving) {
                        break;
                    }
                }

                mCurMovePointF.set((int) (event.getX() + mViewPaddingLeft), (int) (event.getY() + mViewPaddingTop));
                if (mStatus == STATUS_ROTATE_ZOOM_OUTSIDE) {
                    zoomRotateOutSide();
                } else if (mStatus == STATUS_ROTATE_ZOOM_INSIDE) {
                    if (pointCount >= 2) {
                        zoomRotateInside(event);
                    }
                } else if (mStatus == STATUS_DRAG) {

                    if (mPasterListener != null) {
                        float tagX = mCenterPoint.x + mCurMovePointF.x - mPreMovePointF.x;
                        if (mPasterListener.isCenterXinLayout(this, tagX, mViewWidth)) {
                            mCenterPoint.x += mCurMovePointF.x - mPreMovePointF.x;
                        } else {
                            //异常情况处理：如果旋转将角位置移到了限制之外，侧移动时强制将中心点设置回来
                            if (mCenterPoint.x < 0) {
                                mCenterPoint.x = LIMIT_DISTANCE - mViewWidth / 2;
                            } else if (mCenterPoint.x > parentWidth) {
                                mCenterPoint.x = parentWidth - LIMIT_DISTANCE + mViewWidth / 2;
                            }
                        }
                        float tagY = mCenterPoint.y + mCurMovePointF.y - mPreMovePointF.y;
                        if (mPasterListener.isCenterYinLayout(this, tagY, mViewHeight)) {
                            mCenterPoint.y += mCurMovePointF.y - mPreMovePointF.y;
                        } else {
                            //异常情况处理：如果旋转将角位置移到了限制之外，侧移动时强制将中心点设置回来
                            if (mCenterPoint.y < 0) {
                                mCenterPoint.y = LIMIT_DISTANCE - mViewHeight / 2;
                            } else if (mCenterPoint.y > parentHeight) {
                                mCenterPoint.y = parentHeight - LIMIT_DISTANCE + mViewHeight / 2;
                            }
                        }
                    } else {
                        mCenterPoint.x += mCurMovePointF.x - mPreMovePointF.x;
                        mCenterPoint.y += mCurMovePointF.y - mPreMovePointF.y;
                    }
                    mCenterPointRate.set(mCenterPoint.x / (float) parentWidth, mCenterPoint.y / (float) parentWidth);
                    transformDraw();
                }
                mPreMovePointF.set(mCurMovePointF.x, mCurMovePointF.y);
                break;
            default:
                break;
        }
        mGesture.onTouchEvent(event);
        return isControlVisible ? true : super.onTouchEvent(event);
    }


    /**
     * Zoom rotate inside *
     *
     * @param event event
     */
    private synchronized void zoomRotateInside(MotionEvent event) {
        //获取缩放比率
        //存储两点间的距离
        float scale = (float) (Utils.getDistance(event) / mDistance);
        if (mLastScale <= 0) {
            mLastScale = scale;
            return;
        }

        float newDegree = Utils.getDegree(mCenterPoint, mPreMovePointF, mCurMovePointF);
        mDegree += newDegree;

        mScale += (scale - mLastScale);
        mLastScale = scale;
        if (mScale <= MINI_SCALE) {
            mScale = MINI_SCALE;
        } else if (mScale >= MAX_SCALE) {
            mScale = MAX_SCALE;
        }

        transformDraw();
    }


    /**
     * Zoom rotate out side
     */
    private void zoomRotateOutSide() {
        float scale;
        int halfBitmapWidth = (mFlRoot == null ? DEFAULT_OTHER_DRAWABLE_SIZE : mFlRoot.getWidth()) / 2;
        int halfBitmapHeight = (mFlRoot == null ? DEFAULT_OTHER_DRAWABLE_SIZE : mFlRoot.getHeight()) / 2;
        //图片某个点到图片中心的距离
        float bitmapToCenterDistance = (float) Math.sqrt(halfBitmapWidth * halfBitmapWidth + halfBitmapHeight * halfBitmapHeight);
        //移动的点到图片中心的距离
        float moveToCenterDistance = Utils.distance4PointF(mCenterPoint, mCurMovePointF);
        //计算缩放比例
        scale = moveToCenterDistance / bitmapToCenterDistance;
        //缩放比例的界限判断
        if (scale <= MINI_SCALE) {
            scale = MINI_SCALE;
        } else if (scale >= MAX_SCALE) {
            scale = MAX_SCALE;
        }
        float newDegree = Utils.getDegree(mCenterPoint, mPreMovePointF, mCurMovePointF);
        mDegree += newDegree;
        mScale = scale;
        transformDraw();
    }


    /**
     * 获取四个点和View的大小
     *
     * @param left   左
     * @param top    上
     * @param right  右
     * @param bottom 下
     * @param degree 角度
     */
    protected void computeRect(int left, int top, int right, int bottom, float degree) {
        Point lt = new Point(left, top);
        Point rt = new Point(right, top);
        Point rb = new Point(right, bottom);
        Point lb = new Point(left, bottom);
        Point cp = new Point((left + right) / 2, (top + bottom) / 2);
        mLtPoint = Utils.obtainRotationPoint(cp, lt, degree);
        mRtPoint = Utils.obtainRotationPoint(cp, rt, degree);
        mRbPoint = Utils.obtainRotationPoint(cp, rb, degree);
        mLbPoint = Utils.obtainRotationPoint(cp, lb, degree);

        //计算X坐标最大的值和最小的值
        int maxCoordinateX = Utils.getMaxValue(mLtPoint.x, mRtPoint.x, mRbPoint.x, mLbPoint.x);
        int minCoordinateX = Utils.getMinValue(mLtPoint.x, mRtPoint.x, mRbPoint.x, mLbPoint.x);

        mViewWidth = maxCoordinateX - minCoordinateX;


        //计算Y坐标最大的值和最小的值
        int maxCoordinateY = Utils.getMaxValue(mLtPoint.y, mRtPoint.y, mRbPoint.y, mLbPoint.y);
        int minCoordinateY = Utils.getMinValue(mLtPoint.y, mRtPoint.y, mRbPoint.y, mLbPoint.y);

        mViewHeight = maxCoordinateY - minCoordinateY;


        //View中心点的坐标
        Point viewCenterPoint = new Point((maxCoordinateX + minCoordinateX) / 2, (maxCoordinateY + minCoordinateY) / 2);

        /*
         * 图片在旋转时x方向的偏移量
         */
        offsetX = mViewWidth / 2 - viewCenterPoint.x;
        /*
         * 图片在旋转时y方向的偏移量
         */
        offsetY = mViewHeight / 2 - viewCenterPoint.y;


        int halfDrawableWidth = mRsDrawableWidth / 2;
        int halfDrawableHeight = mRsDrawableHeight / 2;

        //将Bitmap的四个点的X的坐标移动offsetX + halfDrawableWidth
        mLtPoint.x += (offsetX + halfDrawableWidth);
        mRtPoint.x += (offsetX + halfDrawableWidth);
        mRbPoint.x += (offsetX + halfDrawableWidth);
        mLbPoint.x += (offsetX + halfDrawableWidth);

        //将Bitmap的四个点的Y坐标移动offsetY + halfDrawableHeight
        mLtPoint.y += (offsetY + halfDrawableHeight);
        mRtPoint.y += (offsetY + halfDrawableHeight);
        mRbPoint.y += (offsetY + halfDrawableHeight);
        mLbPoint.y += (offsetY + halfDrawableHeight);

        mRsPoint = locationToPoint(mRsLocation);
        mClosePoint = locationToPoint(mCloseLocation);
    }


    /**
     * 根据位置判断控制图标处于那个点
     *
     * @param location location
     * @return point point
     */
    private Point locationToPoint(int location) {
        switch (location) {
            case LEFT_TOP:
                return mLtPoint;
            case RIGHT_TOP:
                return mRtPoint;
            case RIGHT_BOTTOM:
                return mRbPoint;
            case LEFT_BOTTOM:
                return mLbPoint;
            default:
                break;
        }
        return mLtPoint;
    }


    /**
     * 根据点击的位置判断是否点中控制旋转，缩放的图片， 初略的计算
     *
     * @param x x
     * @param y y
     * @return int int
     */
    private int judgeStatus(int x, int y) {
        Point touchPoint = new Point(x, y);
        Point controlPointF = new Point(mRsPoint);

        Point closePointF = new Point(mClosePoint);

        //点击的点到控制旋转，缩放点的距离
        float distanceToRs = Utils.distance4PointF(touchPoint, controlPointF);
        //点击的点到关闭按钮位置
        float distanceToClose = Utils.distance4PointF(touchPoint, closePointF);

        //如果两者之间的距离小于 控制图标的宽度，高度的最小值，则认为点中了控制图标
        if (distanceToRs < Math.max(mRsDrawableWidth / 2, mRsDrawableHeight / 2)) {
            return STATUS_ROTATE_ZOOM_OUTSIDE;
        } else if (distanceToClose < Math.max(mCloseDrawableWidth / 2, mCloseDrawableHeight / 2)) {
            return STATUS_CLOSE;
        }

        return STATUS_DRAG;
    }


    /**
     * Sets text *
     *
     * @param text     text
     * @param isUpdate is update
     */
    public void setText(String text, boolean isUpdate) {
        if (!TextUtils.isEmpty(mText) && mText.equals(text)) {
            return;
        }
        mText = text;
        if (TextUtils.isEmpty(mText)) {
            isEditText = false;
            mText = mDefaultText;
        } else {
            isEditText = true;
        }
        mBackText.setText(mText);
        mFrontText.setText(mText);
        if (isUpdate) {
            updateTextWidth();
        }
    }

    /**
     * Update text width
     */
    private void updateTextWidth() {
        if (mBackText == null) {
            return;
        }
        String text = mBackText.getText().toString();
        int lines = 1;
        int width;
        if (text.indexOf("\n") >= 0) {
            String[] texts = text.split("\n");
            width = getTextWidth(mBackText, texts);
            lines = texts.length;
        } else {
            width = (int) mBackText.getPaint().measureText(text, 0, text.length()) + mBackText.getTotalPaddingLeft() + mBackText.getTotalPaddingRight();
        }
        Paint.FontMetrics fm = mBackText.getPaint().getFontMetrics();
        float textHeight = fm.bottom - fm.top; // 计算文字的高度
        float totalHeight = textHeight * lines + 20;

        mBackText.getLayoutParams().width = width;
        mBackText.getLayoutParams().height = (int) totalHeight;
        if (mFlRoot != null) {
            mFlRoot.getLayoutParams().width = width;
            mFlRoot.getLayoutParams().height = (int) totalHeight;
        }

        if (mFrontText != null) {
            mFrontText.getLayoutParams().width = width;
            mFrontText.getLayoutParams().height = (int) totalHeight;
        }
        requestLayout();
    }

    private int getTextWidth(CsTextView mBackText, String[] texts) {
        int maxWidth = 0;
        for (String text : texts) {
            int tmpWidth = (int) mBackText.getPaint().measureText(text, 0, text.length()) + mBackText.getTotalPaddingLeft() + mBackText.getTotalPaddingRight();
            if (maxWidth < tmpWidth) {
                maxWidth = tmpWidth;
            }
        }
        return maxWidth;
    }

    /**
     * Gets text *
     *
     * @return the text
     */
    public String getText() {
        return isEditText ? mBackText.getText().toString() : "";
    }


    /**
     * M re bean
     */
    private ReBean mReBean;

    /**
     * Sets type face *
     *
     * @param bean     bean
     * @param isUpdate is update
     */
    public void setTypeFace(ReBean bean, boolean isUpdate) {
        mReBean = bean;
        if (mBackText != null) {
            mBackText.setTypefaceWithPath(bean == null ? "" : bean.getPath());
        }
        if (mFrontText != null) {
            mFrontText.setTypefaceWithPath(bean == null ? "" : bean.getPath());
        }
        if (isUpdate) {
            updateTextWidth();
        }
    }

    /**
     * Sets font *
     *
     * @param bean         bean
     * @param pasterLayout paster layout
     */
    public void setFont(PasteBean bean, PasteLayout pasterLayout) {
        if (pasterLayout == null || bean == null) {
            return;
        }
        Log.e("FontView setFont", bean.toString());
        recover(bean, pasterLayout.getWidth(), pasterLayout.getHeight());
        pasterLayout.addViewWithType(this);
        setText(bean.content, false);
        setTypeFace(bean.bean, false);
        setStyle(bean.stylebean, false);
        transformDraw();
        updateTextWidth();

    }

    /**
     * M style bean
     */
    private StyleBean mStyleBean;

    /**
     * Sets style *
     *
     * @param bean          bean
     * @param requestLayout request layout
     */
    public void setStyle(StyleBean bean, boolean requestLayout) {
        mStyleBean = bean;
        if (mBackText != null) {
            mBackText.setStyle(bean);
        }
        if (mFrontText != null) {
            mFrontText.setBackgroundColor(0x00000000);
            mFrontText.setTextColor(bean != null ? FontToolPresenter.stringToColor(bean.fColor, Color.WHITE) : Color.WHITE);
        }
        if (requestLayout) {
            requestLayout();
        }
    }

    /**
     * Gets reBean *
     *
     * @return the reBean
     */
    public ReBean getReBean() {
        return mReBean;
    }

    /**
     * Gets style bean *
     *
     * @return the style bean
     */
    public StyleBean getStyleBean() {
        return mStyleBean;
    }


    /**
     * Gets paste bean *
     *
     * @return the paste bean
     */
    @Override
    public PasteBean getPasteBean() {
        PasteBean pb = new PasteBean(ReBean.TYPE.FONT, getReBean(), getStyleBean());
        pb.content = mText;
        pb.centerPoint = mCenterPoint;
        pb.scale = mScale;
        pb.degree = mDegree;
        Log.e("FontView getPasteBean", pb.toString());
        return pb;
    }


    /**
     * 设置Matrix, 强制刷新
     */
    protected void transformDraw() {
        if (mFlRoot == null) {
            return;
        }
        int width = (int) (mFlRoot.getWidth() * mScale);
        int height = (int) (mFlRoot.getHeight() * mScale);
        computeRect(-framePadding, -framePadding, width + framePadding, height + framePadding, mDegree);

        mFlRoot.setRotation(mDegree % 360);
        mFlRoot.setScaleX(mScale);
        mFlRoot.setScaleY(mScale);
        mFlRoot.invalidate();
        adjustLayout();
    }

    /**
     * Gets id *
     *
     * @return the id
     */
    @Override
    public String getID() {
        return "";
    }

    /**
     * Sets last view *
     */
    protected void setLastView() {
        if (mPasterListener != null) {
            mPasterListener.setLastView(this, getReBean(), getStyleBean());
        }
    }

    @Override
    public void release() {
    }


}
