package com.jd.oa.lib.editor.photo;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.photo.paste.view.IViewType;
import com.jd.oa.lib.editor.photo.paste.view.Listener;
import com.jd.oa.lib.editor.photo.paste.view.PasteLayout;
import com.jd.oa.lib.editor.util.BitmapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分组ViewPager适配器
 *
 * <AUTHOR> zhoumin1117 Email: <EMAIL>
 * @date : 2018/7/30 Time: 下午3:04 Description:
 */
public class EditViewPagerAdapter extends PagerAdapter {

    /**
     * 上下文
     */
    private final Context mContext;

    /**
     * 所有分组数据
     */
    private final List<LocalMedia> mList = new ArrayList<>();

    /**
     * cur position
     */
    private int mCurPosition = 0;

    /**
     * M hash map
     */
    private final ConcurrentHashMap<Integer, View> mBufferViewHashMap = new ConcurrentHashMap<>();


    /**
     * M listener
     */
    private final EditViewPagerListener mListener;
    /**
     * 线程池
     */
    private final EditPhotoPresenter mEditPhotoPresenter;

    /**
     * 构造
     *
     * @param mContext           m context
     * @param list               list
     * @param editPhotoPresenter EditPhotoPresenter
     * @param mListener          m listener
     */
    public EditViewPagerAdapter(Context mContext, List<LocalMedia> list, EditPhotoPresenter editPhotoPresenter, @NonNull EditViewPagerListener mListener) {
        super();
        this.mContext = mContext;
        this.mList.addAll(list);
        this.mListener = mListener;
        this.mEditPhotoPresenter = editPhotoPresenter;
    }

    /**
     * Sets cur position *
     *
     * @param position position
     */
    public void setCurPosition(int position) {
        this.mCurPosition = position;
    }

    /**
     * Sets data *
     *
     * @param list list
     */
    public void setData(List<LocalMedia> list) {
        this.mList.clear();
        if (list == null || list.isEmpty()) {
            notifyDataSetChanged();
            return;
        }
        this.mList.addAll(list);
        notifyDataSetChanged();
    }


    /**
     * 获取总分组数
     *
     * @return count count
     */
    @Override
    public int getCount() {
        return mList == null ? 0 : mList.size();
    }

    /**
     * Is view from object boolean
     *
     * @param arg0 arg 0
     * @param arg1 arg 1
     * @return the boolean
     */
    @Override
    public boolean isViewFromObject(@NonNull View arg0, @NonNull Object arg1) {
        return arg0 == arg1;
    }


    /**
     * Destroy item *
     *
     * @param viewGroup view group
     * @param position  position
     * @param view      view
     */
    @Override
    public void destroyItem(@NonNull ViewGroup viewGroup, int position, @NonNull Object view) {

        try {
            if (viewGroup instanceof ViewPager && view instanceof View) {
                View removeView = (View) view;
                viewGroup.removeView(removeView);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            View dView = mBufferViewHashMap.remove(position);
            cachePasterInfo(dView, position, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 缓存贴纸文字信息到数据
     *
     * @param view     view
     * @param position position
     * @param isClear  is clear
     */
    private void cachePasterInfo(View view, int position, boolean isClear) {
        if (view == null) {
            return;
        }
        final PasteLayout mPasteLayout = view.findViewById(R.id.mPasteLayout);
        if (mPasteLayout != null && mPasteLayout.getChildCount() > 0) {
            if (mList != null && position < mList.size()) {
                LocalMedia item = mList.get(position);
                item.rPasterBean = null;
                item.rPasterBean = new ArrayList<>();
                for (int i = 0; i < mPasteLayout.getChildCount(); i++) {
                    View subView = mPasteLayout.getChildAt(i);
                    if (subView instanceof IViewType) {
                        IViewType iView = (IViewType) subView;
                        if (iView.getPasteBean() != null) {
                            item.rPasterBean.add(iView.getPasteBean());
                        }
                    }
                }
            }
            if (isClear && mPasteLayout != null) {
                for (int i = 0; i < mPasteLayout.getChildCount(); i++) {
                    View subView = mPasteLayout.getChildAt(i);
                    if (subView instanceof IViewType) {
                        IViewType iView = (IViewType) subView;
                        iView.release();
                    }
                }
                mPasteLayout.clear();
            }
        }
    }

    /**
     * Instantiate item object
     *
     * @param viewGroup view group
     * @param position  position
     * @return the object
     */
    @Override
    public Object instantiateItem(@NonNull final ViewGroup viewGroup, final int position) {
        final View view = LayoutInflater.from(mContext).inflate(R.layout.mm_edit_display_item, null);

        mBufferViewHashMap.put(position, view);
        viewGroup.addView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        final LocalMedia item = mList.get(position);
        if (mEditPhotoPresenter != null && mEditPhotoPresenter.getDiscardPool() != null && mCurPosition != position) {
            mEditPhotoPresenter.getDiscardPool().execute(new Runnable() {
                @Override
                public void run() {
                    bindView(viewGroup, view, item, position);
                }
            });
        } else {
            bindView(viewGroup, view, item, position);
        }
        return view;
    }


    /**
     * Bind view *
     *
     * @param viewGroup view group
     * @param view      view
     * @param item      item
     */
    private void bindView(final ViewGroup viewGroup, final View view, final LocalMedia item, final int position) {
        if (item == null) {
            return;
        }
        final Bitmap preBitmap = BitmapUtils.getFitSampleBitmap(mContext, item.getEditPath());
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Bitmap bitmap = preBitmap;
                if (bitmap == null || bitmap.isRecycled()) {
                    bitmap = BitmapUtils.getFitSampleBitmap(mContext, item.getEditPath());
                }

                final FilterImageView mEditImageView = view.findViewById(R.id.mEditImageView);
                final PasteLayout mPasteLayout = view.findViewById(R.id.mPasteLayout);
                mPasteLayout.setListener(mListener);
                mEditImageView.setImageBitmap(bitmap);
                if (!mEditImageView.isSameFilter(item.getFilterPath(), item.fIntensity)) {
                    mEditImageView.setFilter(bitmap, item.getFilterPath(), item.fIntensity, false, mEditPhotoPresenter.getDiscardPool());
                }
                int width = bitmap == null ? 0 : bitmap.getWidth();
                int height = bitmap == null ? 0 : bitmap.getHeight();
                View paintView = view.findViewById(R.id.paintView);
                boolean isSameSize = EditPhotoPresenter.adjustSize(viewGroup, mEditImageView, mPasteLayout, paintView,width, height);

                if (mListener != null && mCurPosition == position) {
                    mListener.instantiateItem(view, item, position);
                }
                if (mPasteLayout != null) {
                    EditPhotoPresenter.applyPasterInfo(mPasteLayout, item, isSameSize, null);
                }
            }
        });
    }


    /**
     * Gets view with position *
     *
     * @param position position
     * @return the view with position
     */
    public View getViewWithPosition(int position) {
        if (mBufferViewHashMap.containsKey(position)) {
            return mBufferViewHashMap.get(position);
        }
        return null;
    }

    /**
     * 在主线程运行
     *
     * @param run run
     */
    public void runOnUiThread(Runnable run) {
        if (mContext instanceof Activity) {
            Activity act = (Activity) mContext;
            if (!act.isFinishing()) {
                act.runOnUiThread(run);
            }
        }
    }


    /**
     * Buffer中的View(将左右+当前)中的文字贴纸信息 记录到具体数据对象（LocalMedia）上。
     *
     * @param isClear is clear
     */
    public void cachePasteOfBufferView(boolean isClear) {
        try {
            for (Integer key : mBufferViewHashMap.keySet()) {
                View view = mBufferViewHashMap.get(key);
                if (view != null) {
                    cachePasterInfo(view, key, isClear);
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * 将滤镜应用到 ViewPager 缓存的View 中 一般前一个View 与后一个View
     *
     * @param path            path
     * @param intensity       intensity
     * @param currentPosition currentPosition 当前图片已经应用了滤镜
     */
    public void applyFilterWithoutCurrent(final String path, final float intensity, int currentPosition) {
        try {
            for (Integer key : mBufferViewHashMap.keySet()) {
                if (!key.equals(currentPosition)) {
                    View view = mBufferViewHashMap.get(key);
                    final LocalMedia item = mList.get(key);
                    if (view != null && item != null) {
                        final FilterImageView mEditImageView = view.findViewById(R.id.mEditImageView);
                        if (mEditImageView.isSameFilter(path, intensity)) {
                            return;
                        }
                        mEditPhotoPresenter.getDiscardPool().execute(new Runnable() {
                            @Override
                            public void run() {
//                                Bitmap bitmap = mEditImageView.getSrcBitmap();
//                                if (bitmap == null) {
                                Bitmap  bitmap = BitmapUtils.getFitSampleBitmap(mEditImageView.getContext(), item.getEditPath());
//                                }
                                mEditImageView.setFilter(bitmap, path, intensity, false, mEditPhotoPresenter.getDiscardPool());
                            }
                        });
                    }
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Edit view pager listener
     */
    public interface EditViewPagerListener extends Listener {
        /**
         * Instantiate item *
         *
         * @param view     view
         * @param position position
         */
        void instantiateItem(View view, LocalMedia item, final int position);
    }

}
