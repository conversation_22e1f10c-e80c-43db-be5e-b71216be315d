package com.jd.oa.lib.editor.photo;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

import java.util.concurrent.ExecutorService;

/**
 * des:  滤镜图片控件
 *
 * <AUTHOR>
 * @date 2021 /10/14 : 9:24 上午
 * erp  ：      zhoumin117
 * mail   ：    <EMAIL>
 */
public class FilterImageView extends AppCompatImageView {

    /**
     * Filter image view
     *
     * @param context context
     */
    public FilterImageView(Context context) {
        super(context);
    }

    /**
     * Filter image view
     *
     * @param context context
     * @param attrs   attrs
     */
    public FilterImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    /**
     * Filter image view
     *
     * @param context      context
     * @param attrs        attrs
     * @param defStyleAttr def style attr
     */
    public FilterImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 缓存滤镜
     */
    private volatile String mCacheFilter = "";
    /**
     * 缓存滤镜强度
     */
    private volatile float mCacheIntensity;
    /**
     * 最后滤镜效果
     */
    private volatile String mLastFilter = null;
    /**
     * 最后滤镜强度
     */
    private volatile float mLastIntensity = 0;

    /**
     * M src bitmap
     */
    private Bitmap mSrcBitmap;

    /**
     * Sets image bitmap *
     *
     * @param srcBitmap src bitmap
     */
    @Override
    public void setImageBitmap(Bitmap srcBitmap) {
        super.setImageBitmap(srcBitmap);
        mSrcBitmap = srcBitmap;
        mCacheFilter = "";
        mLastFilter = null;
        mLastIntensity = 1.0f;
        mCacheIntensity = 1.0f;
    }

    /**
     * Gets src bitmap *
     *
     * @return the src bitmap
     */
    public Bitmap getSrcBitmap() {
        return mSrcBitmap;
    }

    /**
     * Is same filter boolean
     *
     * @param filter    filter
     * @param intensity intensity
     * @return the boolean
     */
    public boolean isSameFilter(String filter, final float intensity) {
        boolean isSameFilter = true;

        if (TextUtils.isEmpty(filter)) {
            isSameFilter = TextUtils.isEmpty(mLastFilter);
        } else {
            isSameFilter = filter.equals(mLastFilter) && mLastIntensity == intensity;
        }
        return isSameFilter;
    }

    /**
     * Sets image bitmap filter *
     *
     * @param bm       bm
     * @param recycled recycled
     */
    public void setImageBitmapFilter(Bitmap bm, boolean recycled) {
        super.setImageBitmap(bm);
        invalidate();
//        if (mSrcBitmap != null && !mSrcBitmap.isRecycled() & recycled) {
//            mSrcBitmap.recycle();
//            mSrcBitmap = null;
//        }
    }


    /**
     * Is filter Applying
     */
    private volatile boolean isFilterApplying = false;

    /**
     * Has cache filter
     */
    private volatile boolean hasCacheFilter = false;

    /**
     * 处理滤镜
     *
     * @param srcBitmap       src bitmap
     * @param filter          滤镜路径
     * @param intensity       强度
     * @param isLastOne       滑杆控制强度，当前滤镜正在处理时，手势离开，需要处理最后强度
     * @param executorService executor service
     */
    public void setFilter(final Bitmap srcBitmap, String filter, final float intensity, final boolean isLastOne, @NonNull final ExecutorService executorService) {
        try {
            if (filter == null) {
                filter = "";
            }
            if (isLastOne) {
                Log.d("LookupFilterHelper", "最终需要应用滤镜:" + intensity);
            }
            if (isFilterApplying && isLastOne) {
//            滑杆控制强度，当前滤镜正在处理时，滑杆最最后，手势离开
                hasCacheFilter = true; //记录有后续操作
                mCacheFilter = filter; //记录滤镜
                mCacheIntensity = intensity; //记录强度
                return;
            } else {
                hasCacheFilter = false;  //正常更新，清除记录
                mCacheFilter = "";  //正常更新，清除记录
                mCacheIntensity = 1.0f;//正常更新，清除记录
            }
            if (srcBitmap == null || srcBitmap.isRecycled()) {
                //图片异常，不处理
                return;
            }
            if (filter.equals(mLastFilter) && intensity == mLastIntensity) {
                //与最后滤镜一样，不处理
                return;
            }
            if (TextUtils.isEmpty(filter) || "null".equals(filter) || intensity < 0.1f) {
                /* 不处理滤镜条件，滑动条只设置了
                 * {@see FilterDialogFragment#MAX_FILTER_VALUE} 最大值1，所以为0.1
                 */
                //此种情况相当没有没有滤镜 记录
                mLastFilter = filter;
                mLastIntensity = intensity;
                Log.e("LookupFilterHelper", "应用原图:" + intensity);
                post(new Runnable() {
                    @Override
                    public void run() {
                        setImageBitmapFilter(srcBitmap, false);
                    }
                });
                return;
            }
            applyFilter(srcBitmap, filter, intensity, executorService);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Apply filter *
     *
     * @param srcBitmap       src bitmap
     * @param sFilter         sFilter
     * @param intensity       intensity
     * @param executorService executor service
     */
    private void applyFilter(final Bitmap srcBitmap, String sFilter, final float intensity, final ExecutorService executorService) {
//        if (sFilter == null) {
//            sFilter = "";
//        }
//        final String filter = sFilter;
//        isFilterApplying = true;
//        executorService.execute(new Runnable() {
//            @Override
//            public void run() {
//                try {
//                    if (srcBitmap == null || srcBitmap.isRecycled()) {//图片释放了，返回
//                        isFilterApplying = false;
//                        return;
//                    }
//                    if (filter.equals(mLastFilter) && intensity == mLastIntensity) {
//                        //与最后滤镜一样，不处理
//                        isFilterApplying = false;
//                        return;
//                    }
//                    if (TextUtils.isEmpty(filter) || "null".equals(filter) || intensity < 0.1f) {
//                        /* 不处理滤镜条件，滑动条只设置了
//                         * {@see FilterDialogFragment#MAX_FILTER_VALUE} 最大值1，所以为0.1
//                         */
//
//                        //此种情况为没有滤镜 记录
//                        mLastFilter = filter;
//                        mLastIntensity = intensity;
//                        Log.e("LookupFilterHelper", "应用原图:" + intensity);
//                        isFilterApplying = false;
//                        post(new Runnable() {
//                            @Override
//                            public void run() {
//                                setImageBitmapFilter(srcBitmap, false);
//                            }
//                        });
//                        return;
//                    }
//                    final Bitmap bitmap = FilterPresenter.processFilter(getContext(), srcBitmap, filter, intensity);
//                    Log.e("LookupFilterHelper", "应用滤镜:" + intensity);
//                    mLastFilter = filter;
//                    mLastIntensity = intensity;
//                    isFilterApplying = false;
//                    post(new Runnable() {
//                        @Override
//                        public void run() {
//                            if (bitmap != null && !bitmap.isRecycled()) {
//                                setImageBitmapFilter(bitmap, true);
//                            } else {
//                                setImageBitmapFilter(srcBitmap, false);
//                                ToastUtil.showLongToast(getContext(), "应用滤镜失败，请重试");
//                            }
//                            if (hasCacheFilter) {
//                                hasCacheFilter = false;
//                                // 需要处理手势离开  最后
//                                Log.e("LookupFilterHelper", "需要处理手势离开:" + mCacheIntensity);
//                                applyFilter(srcBitmap, mCacheFilter, mCacheIntensity, executorService);
//                            }
//                        }
//                    });
//                } catch (Throwable e) {
//                    e.printStackTrace();
//                }
//            }
//        });
    }
}
