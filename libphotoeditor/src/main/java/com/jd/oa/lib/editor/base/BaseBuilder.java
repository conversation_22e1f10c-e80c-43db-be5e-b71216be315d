package com.jd.oa.lib.editor.base;

import android.graphics.Bitmap;

import com.jd.oa.lib.editor.photo.clip.CutImageType;
import com.jd.oa.lib.editor.pub.Constants;
import com.jd.oa.lib.editor.pub.Size;

import java.util.List;

/**
 * The type Base builder.
 *
 * @param <Builder> the type parameter
 * @param <Data>    the type parameter
 * <AUTHOR>
 * @date 2021 /10/18
 */
public abstract class BaseBuilder<Builder, Data extends BaseParam> {

    /**
     * The M param.
     */
    protected final Data mParam;
    /**
     * The Builder.
     */
    protected Builder builder;

    /**
     * Instantiates a new Base builder.
     *
     * @param param the param
     */
    public BaseBuilder(Data param) {
        this.mParam = param;
    }

    /**
     * 是否显示滤镜
     *
     * @param showFilter true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showFilter(boolean showFilter) {
        mParam.showFilter = showFilter;
        return builder;
    }

    /**
     * 是否显示贴纸
     *
     * @param showDecals true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showDecals(boolean showDecals) {
        mParam.showDecals = showDecals;
        return builder;
    }

    /**
     * 是否显示跟我拍
     *
     * @param showFollowTake true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showFollowTake(boolean showFollowTake) {
        mParam.showFollowTake = showFollowTake;
        return builder;
    }

    /**
     * 是否显示道具
     *
     * @param showProps true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showProps(boolean showProps) {
        mParam.showProps = showProps;
        return builder;
    }

    /**
     * 设置渠道
     *
     * @param channel the channel
     * @return builder builder
     */
    public Builder channel(String channel) {
        mParam.mChannel = channel;
        return builder;
    }

    /**
     * 是否显示视频剪辑中音乐
     *
     * @param showMusic true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showMusic(boolean showMusic) {
        mParam.showMusic = showMusic;
        return builder;
    }

    /**
     * 是否显示视频剪辑中封面
     *
     * @param showCover true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showCover(boolean showCover) {
        mParam.showCover = showCover;
        return builder;
    }

    /**
     * 是否显示图片编辑中的文字
     *
     * @param showFont true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showFont(boolean showFont) {
        mParam.showFont = showFont;
        return builder;
    }

    /**
     * 是否显示图片编辑中的裁剪
     *
     * @param showClip true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showClip(boolean showClip) {
        mParam.showCutImage = showClip;
        return builder;
    }

    /**
     * 是否显示视频编辑中的剪辑
     *
     * @param showCut true：显示 false：隐藏
     * @return builder builder
     */
    public Builder showCut(boolean showCut) {
        mParam.showClipVideo = showCut;
        return builder;
    }

    /**
     * 设置分辨率信息
     *
     * @param resolution 分辨率信息
     * @return builder builder
     */
//    public Builder resolution(Resolution resolution) {
//        mParam.resolution = resolution;
//        return builder;
//    }


    /**
     * Enable save to album photo editer builder
     *
     * @param isEnablePhoto is enable photo
     * @param isEnableVideo is enable video
     * @return the photo edit builder
     */
    public Builder enableSaveToAlbum(boolean isEnablePhoto, boolean isEnableVideo) {
        mParam.isSavePhotoToAlbum = isEnablePhoto;
        mParam.isSaveVideoToAlbum = isEnableVideo;
        return builder;
    }

    /**
     * 是否可以编辑
     *
     * @param needEditorMedia need editor media
     * @return media maker builder
     */
    public Builder needEditorMedia(boolean needEditorMedia) {
        mParam.needEditorMedia = needEditorMedia;
        return builder;
    }

    /**
     * 商品三级分类
     *
     * @param ate3Id ate 3 id
     * @return media maker builder
     */
    public Builder cate3Id(String ate3Id) {
        mParam.cate3Id = ate3Id;
        return builder;
    }

    /**
     * 设置打开跟我拍UI
     *
     * @param open open
     * @return media maker builder
     */
    public Builder openFollowTakeUI(boolean open) {
        mParam.openFollowTakeUi = open;
        return builder;
    }

    /**
     * Is use system album media maker builder
     *
     * @param isUseSystemAlbum 是否使用系统相册
     * @return media maker builder
     */
    public Builder isUseSystemAlbum(boolean isUseSystemAlbum) {
        mParam.isUseSystemAlbum = isUseSystemAlbum;
        return builder;
    }

    /**
     * Cut image types builder
     *
     * @param cutImageTypes 图片裁剪支持的类型
     * @return builder builder
     */
    public Builder cutImageTypes(List<CutImageType> cutImageTypes) {
        mParam.cutImageTypes = cutImageTypes;
        return builder;
    }

    /**
     * Cut image scroll able builder
     *
     * @param isImageCutRectDrag 图片裁剪是否启用线框拖动
     * @return builder builder
     */
    public Builder cutImageRectDrag(boolean isImageCutRectDrag) {
        mParam.isImageCutRectDrag = isImageCutRectDrag;
        return builder;
    }


    /**
     * 图片裁剪限制像素
     *
     * @param cutMinImageSize  min picture size px
     * @return builder builder
     */
    public Builder cutImageMinPx(Size cutMinImageSize) {
        if (cutMinImageSize != null) {
            if (cutMinImageSize.width > 1080) {
                cutMinImageSize.width = 1080;
            }
            if (cutMinImageSize.height > 1080) {
                cutMinImageSize.height = 1080;
            }
        }
        mParam.cutMinImageSize = cutMinImageSize;
        return builder;
    }

    /**
     * 图片裁剪限制像素
     *
     * @param cutCustomImageRate  cutRateImageSize
     * @return builder builder
     */
    public Builder cutCustomImageRate(Size cutCustomImageRate) {
        mParam.cutCustomImageRate = cutCustomImageRate;
        return builder;
    }

    /**
     * Need image cut builder
     *
     * @param needImageCut need image cut
     * @return the builder
     */
    public Builder needImageCut(boolean needImageCut) {
        mParam.needImageCut = needImageCut;
        return builder;
    }

    /**
     * 裁剪图片显示旋转
     * @param showCutRotate
     * @return
     */
    public Builder showCutImageRotate(boolean showCutRotate) {
        mParam.showCutRotate = showCutRotate;
        return builder;
    }

    /**
     * 裁剪图片显示 重置
     * @param showCutReset
     * @return
     */
    public Builder showCutImageReset(boolean showCutReset) {
        mParam.showCutReset = showCutReset;
        return builder;
    }

    /**
     * 图片是存储为png 格式  默认false（jpg）
     * @param fo 图片保存类型
     * @return
     */
    public Builder saveBitmapFormat(Bitmap.CompressFormat fo) {
        mParam.bitmapFormat = fo;
        return builder;
    }

    /**
     *  设置此参数后，视频列表3秒-3分钟选择限制失效
     * @param maxVideoFileSize
     * @return
     */
    public Builder maxVideoFileSizeMB(int maxVideoFileSize){
        mParam.maxVideoFileSizeMB = maxVideoFileSize;
        return builder;
    }

    public Builder maxUploadImageFileSizeMB(int maxUploadImageFileSizeMB){
        mParam.maxUploadImageFileSizeMB = maxUploadImageFileSizeMB;
        return builder;
    }

    /**
     *  是否开启HDR功能。默认打开
     * @param enableHdr
     * @return
     */
    public Builder enableHdr(boolean enableHdr){
        mParam.enableHdr = enableHdr;
        return builder;
    }
    /**
     * 是否从编辑界面过来的 处理图片压缩逻辑时，图片编辑添加时根据此变量判断是否需要压缩。
     * @param is
     * @return
     */
    public Builder enteredEdit(boolean is){
        mParam.isEnteredEdit = is;
        return builder;
    }
    /**
     * 是否从编辑界面过来的 处理图片压缩逻辑时，图片编辑添加时根据此变量判断是否需要压缩。
     * @param is
     * @return
     */
    public Builder setAlbumLoadByStep(boolean is){
        mParam.isAlbumLoadByStep = is;
        return builder;
    }

    /**
     * 录制视频最短时间
     *
     * @param videoRecordMinTime video record min time
     * @return media picker builder
     */
    public Builder videoRecordMinTime(long videoRecordMinTime) {
        if (videoRecordMinTime > 0) {
            mParam.videoRecordMinTime = videoRecordMinTime;
        }
        return builder;
    }

    /**
     * 录制视频最大时间
     *
     * @param videoRecordMaxTime video record max time
     * @return media picker builder
     */
    public Builder videoRecordMaxTime(long videoRecordMaxTime) {
        if (videoRecordMaxTime > 0 && videoRecordMaxTime <= Constants.VIDEO_MAX_DURATION) {
            mParam.videoRecordMaxTime = videoRecordMaxTime;
        } else {
            mParam.videoRecordMaxTime = Constants.VIDEO_MAX_DURATION;
        }
        return builder;
    }

    /**
     * 图片白名单设置过滤规则
     * @param picSuffixTypes 图片后缀名过滤，多个以"|"分隔。比如：png|jpg|jpeg|
     * @param picMineTypes 图片MINETYPE过滤，多个以"|"分隔。比如：image/jpeg|image/png|image/gif
     * @return
     */
    public Builder picWhiteList(String picSuffixTypes, String picMineTypes) {
        mParam.picSuffixTypes = picSuffixTypes;
        mParam.picMineTypes = picMineTypes;
        return builder;
    }

    /**
     * 视频白名单设置过滤规则
     * @param videoSuffixTypes 视频后缀名过滤，多个以"|"分隔。
     * 比如：mp4|3gp|3g2|3gpp|mov|m4v|mkv|wmv|webm|vob|rmvb|mpg|avi|flv|mpeg|asf
     * @param videoMineTypes 视频MIMETYPE过滤，多个以"|"分隔。
     * 比如：video/mp4|video/avc|video/3gp|video/3gpp|video/3gpp2|video/x-matroska|video/mkv|video/m4v|video/webm|video/quicktime
     *                       |video/x-ms-wmv|video/mpeg|video/x-flv|video/avi|video/x-pn-realvideo
     * @return
     */
    public Builder videoWhiteList(String videoSuffixTypes, String videoMineTypes) {
        mParam.videoSuffixTypes = videoSuffixTypes;
        mParam.videoMineTypes = videoMineTypes;
        return builder;
    }

    /**
     * 设置是否支持一键成片
     * @param support
     * @return
     */
    public Builder supportEasyClip(boolean support){
        mParam.isSupportEasyClip = support;
        return builder;
    }

    /**
     * 设置是否使用Uri
     * @param useUri
     * @return
     */
    public Builder useUri(boolean useUri){
        mParam.isUseUri = useUri;
        return builder;
    }

    /**
     * 设置是否copy到沙盒
     * @param copy
     * @return
     */
    public Builder copy2Sandbox(boolean copy){
        mParam.isCopy2Sandbox = copy;
        return builder;
    }

    /**
     * 设置是否支持原图功能
     * @param support
     * @return
     */
    public Builder supportOriginal(boolean support){
        mParam.isSupportOriginal = support;
        return builder;
    }

    public Builder processType(int processType){
        mParam.processType = processType;
        return builder;
    }


    public Builder darkMode(boolean darkMode) {
        mParam.darkMode = darkMode;
        return builder;
    }

    public Builder guideMessage(String guideMessage){
        mParam.guideMessage = guideMessage;
        return builder;
    }

    /**
     * 设置是否开启线上调试开关
     * @param support
     * @return
     */
    public Builder supportOnLineDebug(boolean support){
        mParam.isOnlineDebug = support;
        return builder;
    }

    /**
     * 设置是否显示相册按钮
     * @param show
     * @return
     */
    public Builder showAlbum(boolean show){
        mParam.isShowAlbum = show;
        return builder;
    }

    /**
     * 设置是否显示画笔编辑入口
     * @param show
     * @return
     */
    public Builder showPaint(boolean show){
        mParam.showPaint = show;
        return builder;
    }

    /**
     * 设置是否显示马赛克编辑入口
     * @param show
     * @return
     */
    public Builder showMosaic(boolean show){
        mParam.showMosaic = show;
        return builder;
    }

    /**
     * 设置是否显示美颜开关
     * @param show
     * @return
     */
    public Builder showBeauty(boolean show){
        mParam.showBeauty = show;
        return builder;
    }

    /**
     * Copy builder.
     *
     * @param baseParam the base param
     * @return the builder
     */
    public Builder copy(BaseParam baseParam) {
        showFilter(baseParam.showFilter);
        showDecals(baseParam.showDecals);
        showFollowTake(baseParam.showFollowTake);
        showProps(baseParam.showProps);
        channel(baseParam.mChannel);
        showMusic(baseParam.showMusic);
        showCover(baseParam.showCover);
        showFont(baseParam.showFont);
        showClip(baseParam.showCutImage);
        showCut(baseParam.showClipVideo);
//        resolution(baseParam.resolution);
        enableSaveToAlbum(baseParam.isSavePhotoToAlbum, baseParam.isSaveVideoToAlbum);
        cate3Id(baseParam.cate3Id);
        needEditorMedia(baseParam.needEditorMedia);
        openFollowTakeUI(baseParam.openFollowTakeUi);
        isUseSystemAlbum(baseParam.isUseSystemAlbum);
        cutImageTypes(baseParam.cutImageTypes);
        cutImageRectDrag(baseParam.isImageCutRectDrag);
        cutImageMinPx(baseParam.cutMinImageSize);
        cutCustomImageRate(baseParam.cutCustomImageRate);
        needImageCut(baseParam.needImageCut);
        showCutImageRotate(baseParam.showCutRotate);
        showCutImageReset(baseParam.showCutReset);
        saveBitmapFormat(baseParam.bitmapFormat);
        maxVideoFileSizeMB(baseParam.maxVideoFileSizeMB);
        maxVideoFileSizeMB(baseParam.maxVideoFileSizeMB);
        maxUploadImageFileSizeMB(baseParam.maxUploadImageFileSizeMB);
        enableHdr(baseParam.enableHdr);
        videoRecordMaxTime(baseParam.videoRecordMaxTime);
        videoRecordMinTime(baseParam.videoRecordMinTime);
        enteredEdit(baseParam.isEnteredEdit);
        setAlbumLoadByStep(baseParam.isAlbumLoadByStep);
        picWhiteList(baseParam.picSuffixTypes,baseParam.picMineTypes);
        useUri(baseParam.isUseUri);
        copy2Sandbox(baseParam.isCopy2Sandbox);
        videoWhiteList(baseParam.videoSuffixTypes,baseParam.videoMineTypes);
        supportEasyClip(baseParam.isSupportEasyClip);
        supportOriginal(baseParam.isSupportOriginal);
        showAlbum(baseParam.isShowAlbum);
        supportOnLineDebug(baseParam.isOnlineDebug);
        processType(baseParam.processType);
        darkMode(baseParam.darkMode);
        guideMessage(baseParam.guideMessage);
        showPaint(baseParam.showPaint);
        showMosaic(baseParam.showMosaic);
        showBeauty(baseParam.showBeauty);
        return builder;
    }


    /**
     * Gets param *
     *
     * @return the param
     */
    public Data getParam() {
        return mParam;
    }
}
