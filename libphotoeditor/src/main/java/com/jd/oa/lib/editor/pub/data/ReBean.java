package com.jd.oa.lib.editor.pub.data;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * des: 下载资源统一用数据实体
 *
 * @author： zhoumin6
 * @date： 2020 /12/10.3:06 PM
 */
public class ReBean implements Parcelable {

    /**
     * Type
     */
    public enum TYPE {
        /**
         * 滤镜
         */
        FILTER,
        /**
         * 道具
         */
        PROP,
        /**
         * 字体
         */
        FONT,
        /**
         * 贴纸
         */
        DECALS,
        /**
         * 模板
         */
        TEMPLATE
    }

    /**
     * 类型
     */
    public TYPE type;

    /**
     * id
     */
    public String id;


    /**
     * Name
     */
    public String name;
    /**
     * pic_url
     */
    public String picUrl;
    /**
     * file_url
     */
    public String fileUrl;
    /**
     * file_md5
     */
    public String fileMd5;
    /**
     * tag_id
     */
    public String tagId;

    /**
     * gid 所属分组ID
     */
    public ReGroup g;


    /**
     * Version
     */
    public String version;


    /**
     * Gets path *
     *
     * @return the path
     */
    public String getPath() {
        String path = "";
//        if (type == TYPE.PROP) {
//            path = FileUtils.getPropZipDir(fileUrl);
//        } else if (type == TYPE.FILTER) {
//            path = FileUtils.getFilterPath(fileUrl);
//        } else if (type == TYPE.FONT) {
//            path = FileUtils.getFontPath(fileUrl);
//        } else if (type == TYPE.DECALS) {
//            path = FileUtils.getDecalsPath(fileUrl);
//        } else if (type == TYPE.TEMPLATE) {
//            path = FileUtils.getTemplatePath(fileUrl);
//        }
        return path;
    }

    /**
     * Is downloading
     */
    public volatile boolean isDownloading = false;
    /**
     * Download progress
     */
    public int downloadProgress = 0;

    /**
     * Re bean
     */
    private ReBean() {
    }

    /**
     * Re bean
     *
     * @param type type
     */
    public ReBean(TYPE type) {
        this.type = type;
    }

    /**
     * Re bean
     *
     * @param bean bean
     */
    public ReBean(ReBean bean) {
        this.type = bean.type;
        this.id = bean.id;
        this.name = bean.name;
        this.picUrl = bean.picUrl;
        this.fileUrl = bean.fileUrl;
        this.tagId = bean.tagId;
        this.fileMd5 = bean.fileMd5;
        this.g = bean.g;
        this.version = bean.version;
        this.downloadProgress = bean.downloadProgress;
        this.isDownloading = bean.isDownloading;
    }


    /**
     * Re bean
     *
     * @param type     type
     * @param id       id
     * @param name     name
     * @param picUrl  pic url
     * @param fileUrl file url
     * @param fileMd5 file md 5
     * @param tagId   tag id
     */
    public ReBean(TYPE type, String id, String name, String picUrl, String fileUrl, String fileMd5, String tagId) {
        this.type = type;
        this.id = id;
        this.name = name;
        this.picUrl = picUrl;
        this.fileUrl = fileUrl;
        this.fileMd5 = fileMd5;
        this.tagId = tagId;
    }


    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.type == null ? -1 : this.type.ordinal());
        dest.writeString(this.id);
        dest.writeString(this.name);
        dest.writeString(this.picUrl);
        dest.writeString(this.fileUrl);
        dest.writeString(this.fileMd5);
        dest.writeString(this.tagId);
        dest.writeParcelable(this.g, flags);
        dest.writeString(this.version);
        dest.writeByte(this.isDownloading ? (byte) 1 : (byte) 0);
        dest.writeInt(this.downloadProgress);
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    public void readFromParcel(Parcel source) {
        int tmpType = source.readInt();
        this.type = tmpType == -1 ? null : TYPE.values()[tmpType];
        this.id = source.readString();
        this.name = source.readString();
        this.picUrl = source.readString();
        this.fileUrl = source.readString();
        this.fileMd5 = source.readString();
        this.tagId = source.readString();
        this.g = source.readParcelable(ReGroup.class.getClassLoader());
        this.version = source.readString();
        this.isDownloading = source.readByte() != 0;
        this.downloadProgress = source.readInt();
    }

    /**
     * Re bean
     *
     * @param in in
     */
    protected ReBean(Parcel in) {
        int tmpType = in.readInt();
        this.type = tmpType == -1 ? null : TYPE.values()[tmpType];
        this.id = in.readString();
        this.name = in.readString();
        this.picUrl = in.readString();
        this.fileUrl = in.readString();
        this.fileMd5 = in.readString();
        this.tagId = in.readString();
        this.g = in.readParcelable(ReGroup.class.getClassLoader());
        this.version = in.readString();
        this.isDownloading = in.readByte() != 0;
        this.downloadProgress = in.readInt();
    }

    /**
     * CREATOR
     */
    public static final Creator<ReBean> CREATOR = new Creator<ReBean>() {
        @Override
        public ReBean createFromParcel(Parcel source) {
            return new ReBean(source);
        }

        @Override
        public ReBean[] newArray(int size) {
            return new ReBean[size];
        }
    };
}
