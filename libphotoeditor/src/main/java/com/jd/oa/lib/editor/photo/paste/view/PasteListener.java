package com.jd.oa.lib.editor.photo.paste.view;

/**
 * des:
 *
 * <AUTHOR>
 * @date 2021/9/14 : 11:45 上午
 * erp        zhoumin117
 * mail       <EMAIL>
 */
public interface PasteListener extends Listener {
    /**
     * 是否贴纸中心X在可视范围内
     *
     * @param x x
     * @return the boolean
     */
    boolean isCenterXinLayout(IViewType view, float x, int width);

    /**
     * 是否贴纸中心Y在可视范围内
     *
     * @param y y
     * @return the boolean
     */
    boolean isCenterYinLayout(IViewType view, float y, int height);
}
