package com.jd.oa.lib.editor.photo;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.ViewPager;

import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.photo.paint.MosaicMask;
import com.jd.oa.lib.editor.photo.paint.PaintMask;
import com.jd.oa.lib.editor.photo.paint.PaintStruct;
import com.jd.oa.lib.editor.photo.paint.PaintView;
import com.jd.oa.lib.editor.photo.paste.PasteBean;
import com.jd.oa.lib.editor.photo.paste.view.FontView;
import com.jd.oa.lib.editor.photo.paste.view.PasteLayout;
import com.jd.oa.lib.editor.pub.data.ReBean;
import com.jd.oa.lib.editor.util.BitmapUtils;
import com.jd.oa.lib.editor.util.FileUtils;
import com.jd.oa.lib.editor.util.ThreadPoolUtil;
import com.jd.oa.lib.editor.util.ToastUtil;

import java.util.ArrayList;
import java.util.Stack;
import java.util.concurrent.ExecutorService;

/**
 * des: 图片编辑 Presenter
 *
 * <AUTHOR>
 * @date 2021 /11/26 : 3:48 下午 erp        zhoumin117 mail       <EMAIL>
 */
public class EditPhotoPresenter {

    private PaintView paintView;

    /**
     * Edit view
     */
    public interface EditView {

        /**
         * Show loading
         */
        void showLoading(int resStrID);

        /**
         * Hide loading
         */
        void hideLoading();

        /**
         * Error data report *
         *
         * @param errorCode  error code
         * @param count      count
         * @param errorCount error count
         */
        void errorDataReport(int errorCode, int count, int errorCount);

        /**
         * Finish edit *
         *
         * @param save    save
         * @param isClear is clear
         */
        void finishEdit(final boolean save, final boolean isClear);

    }

    /**
     * View
     */
    private final EditView iView;
    /**
     * M act
     */
    private Activity mAct;
    /**
     * M edit view pager
     */
    private ViewPager mEditViewPager;
    /**
     * M save paste layout
     */
    private PasteLayout mSavePasteLayout;
    /**
     * M edit view pager adapter
     */
    private EditViewPagerAdapter mEditViewPagerAdapter;
    /**
     * M list
     */
    private ArrayList<LocalMedia> mList;
    /**
     * COMPOUND_HANDLER
     */
    private static final Handler COMPOUND_HANDLER = new Handler(Looper.getMainLooper());

    /**
     * 合成处理索引 对应mList.
     */
    private int compoundIndex = 0;


    /**
     * M discard pool
     */
    private ExecutorService mDiscardPool;

    /**
     * Gets discard pool *
     *
     * @return the discard pool
     */
    public ExecutorService getDiscardPool() {
        if (mDiscardPool == null) {
            mDiscardPool = ThreadPoolUtil.createDiscardPool(4, 6);
        }
        return mDiscardPool;
    }

    /**
     * M sing discard pool
     */
    private ExecutorService mSingDiscardPool;

    /**
     * Gets sing discard pool *
     *
     * @return the sing discard pool
     */
    public ExecutorService getSingDiscardPool() {
        if (mSingDiscardPool == null) {
            mSingDiscardPool = ThreadPoolUtil.createDiscardPool(1, 1);
        }
        return mSingDiscardPool;
    }

    /**
     * M caller runs pool
     */
    private ExecutorService mCallerRunsPool;

    /**
     * Gets caller runs pool *
     *
     * @return the caller runs pool
     */
    public ExecutorService getCallerRunsPool() {
        if (mCallerRunsPool == null) {
            mCallerRunsPool = ThreadPoolUtil.createCallerRunsPool(1, 2);
        }
        return mCallerRunsPool;
    }

    /**
     * 是否保存为PNG 格式 默认jpg false  保存为png 设置成true
     */
    private Bitmap.CompressFormat bitmapFormat = Bitmap.CompressFormat.PNG;

    /**
     * Edit photo presenter
     *
     * @param iView view
     */
    public EditPhotoPresenter(@NonNull EditView iView) {
        this.iView = iView;
    }

    /**
     * Set *
     *
     * @param act                  act
     * @param editViewPager        edit view pager
     * @param savePasteLayout      save paste layout
     * @param editViewPagerAdapter edit view pager adapter
     * @param paintView
     * @param list                 list
     */
    public void set(final Activity act, ViewPager editViewPager, PasteLayout savePasteLayout, EditViewPagerAdapter editViewPagerAdapter, PaintView paintView, ArrayList<LocalMedia> list, Bitmap.CompressFormat format) {
        this.mAct = act;
        this.mEditViewPager = editViewPager;
        this.mSavePasteLayout = savePasteLayout;
        this.mEditViewPagerAdapter = editViewPagerAdapter;
        this.mList = list;
        this.bitmapFormat = format;
        this.paintView = paintView;
    }


    /**
     * Compound data
     */
    public void compoundData() {
        if (mList == null || mList.size() <= 0) {
            return;
        }
//        iView.showLoading(R.string.mm_save_photo);
        compoundIndex = 0;
        checkPasteFromCache();
    }

    /**
     * Continue compound
     */
    private void continueCompound() {
        COMPOUND_HANDLER.post(new Runnable() {
            @Override
            public void run() {
                compoundIndex++;
                checkPasteFromCache();
            }
        });
    }


    /**
     * 图片合成
     */
    private void checkPasteFromCache() {
        if (mList == null || compoundIndex >= mList.size()) {
            iView.hideLoading();
            iView.finishEdit(true, false);
            return;
        }
        LocalMedia item = mList.get(compoundIndex);


        /* 是否需要合成 */
        if (!isNeedCompound(item)) //没处理过贴纸与文字
        {
            //当前不需要合成，检查下一个
            continueCompound();
            return;
        }
        Bitmap mPasterBitmap = null;
        if (mEditViewPagerAdapter != null) {
            View view = mEditViewPagerAdapter.getViewWithPosition(compoundIndex);
            if (view != null) {
                PasteLayout mPasteLayout = view.findViewById(R.id.mPasteLayout);
                if (mPasteLayout != null && mPasteLayout.getChildCount() > 0) {
                    mPasterBitmap = mPasteLayout.getBitmapFromView();
                }
            }
        }
        compoundLogic(mPasterBitmap, item);
    }

    private static boolean isNeedCompound(LocalMedia item) {
        if (!TextUtils.isEmpty(item.getTempPath())) {
            //裁剪过
            return true;
        }
        if (item.rFilterBean != null) {
            return true; // 滤镜
        }
        ArrayList<PasteBean> rPasterBean = item.rPasterBean;
        if (rPasterBean != null && !rPasterBean.isEmpty()) {
            return true; // 贴纸和滤镜
        }

        return item.paintTouchStack != null || item.mosaicTouchStack != null;//马赛克和画笔
    }


    /**
     * 获取原图或者 滤镜后的图片
     *
     * @param item item
     * @return the src bitmap or filter
     */
    private Bitmap getSrcBitmapOrFilter(final LocalMedia item) {
        Bitmap srcBitmap = BitmapUtils.getFitSampleBitmap(mAct, item.getEditPath());
        if (item.rFilterBean != null) {
//            Bitmap filterBitmap = FilterPresenter.processFilter(mAct, srcBitmap, item.getFilterPath(), item.fIntensity);
//            if (filterBitmap != null && !filterBitmap.isRecycled()) {
////                if (srcBitmap != null && !srcBitmap.isRecycled()) {
////                    srcBitmap.recycle();
////                }
//                srcBitmap = filterBitmap;
//            } else {
//                iView.errorDataReport(5007, mList.size(), 0);
//            }
        }

        if (srcBitmap == null || srcBitmap.isRecycled()) {
            //万一图片为空 ，异常处理
            int width = mEditViewPager == null ? 200 : mEditViewPager.getWidth();
            int height = mEditViewPager == null ? 200 : mEditViewPager.getHeight();
            if (width <= 0) {
                width = 200;
            }
            if (height <= 0) {
                height = 200;
            }
            srcBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(srcBitmap);
            canvas.drawColor(0xffffffff);
        }
        return srcBitmap;
    }

    /**
     * Gets bitmap from file *
     *
     * @param pasterCacheBitmap paster cache bitmap
     * @param item              item
     */
    private void compoundLogic(final Bitmap pasterCacheBitmap, final LocalMedia item) {
        getCallerRunsPool().execute(new Runnable() {
            @Override
            public void run() {
                Bitmap srcBitmap = getSrcBitmapOrFilter(item);
                srcBitmap = compoundPaint(srcBitmap,item);
                ArrayList<PasteBean> rPasterBean = item.rPasterBean;
                if (pasterCacheBitmap == null && rPasterBean != null && rPasterBean.size() > 0) {
                    getPasterBitmap(srcBitmap, item);
                } else {
                    saveFinalBitmap(srcBitmap, pasterCacheBitmap, item);
                }
            }
        });

    }

    /**
     * 合成画笔和马赛克
     * @param srcBitmap 原图
     * @param item 媒体信息
     * @return 合成图
     */
    private Bitmap compoundPaint(Bitmap srcBitmap,LocalMedia item) {
        if (srcBitmap == null || srcBitmap.isRecycled() || item == null) {
            return srcBitmap;
        }
        Stack<PaintStruct.TouchStack> mosaicTouchStack = item.mosaicTouchStack;
        Stack<PaintStruct.TouchStack> paintTouchStack = item.paintTouchStack;
        if (mosaicTouchStack == null && paintTouchStack == null) {
            return srcBitmap;
        }

        try {
            Bitmap bitmap = Bitmap.createBitmap(srcBitmap.getWidth(), srcBitmap.getHeight(), Bitmap.Config.ARGB_8888);

            Canvas canvas = new Canvas(bitmap);
            canvas.drawBitmap(srcBitmap, 0, 0, new Paint());
            if (mosaicTouchStack != null) {
                MosaicMask mosaicMask = new MosaicMask(srcBitmap);
                mosaicMask.setCacheTouchStack(mosaicTouchStack);
                mosaicMask.setCanvasSize(item.canvasWidth, item.canvasHeight);
                mosaicMask.onDraw(canvas);
            }

            if (paintTouchStack != null) {
                PaintMask paintMask = new PaintMask();
                paintMask.setCacheTouchStack(paintTouchStack);
                paintMask.setCanvasSize(item.canvasWidth, item.canvasHeight);
                paintMask.onDraw(canvas);
            }
            canvas.save();
            return bitmap;
        } catch (Exception e) {
            return srcBitmap;
        }
    }

    public Bitmap compoundPaint(Bitmap srcBitmap, PaintStruct paintMask) {
        if (srcBitmap == null || srcBitmap.isRecycled() || paintMask == null) {
            return srcBitmap;
        }
        try {
            Bitmap bitmap = Bitmap.createBitmap(srcBitmap.getWidth(), srcBitmap.getHeight(), Bitmap.Config.ARGB_8888);

            Canvas canvas = new Canvas(bitmap);
            canvas.drawBitmap(srcBitmap, 0, 0, new Paint());

            paintMask.onDraw(canvas);
            canvas.save();
            return bitmap;
        } catch (Exception e) {
            return srcBitmap;
        }
    }

    /**
     * Save final bitmap *
     *
     * @param srcBitmap     src bitmap
     * @param mDecalsBitmap m decals bitmap
     * @param item          item
     */
    private void saveFinalBitmap(Bitmap srcBitmap, Bitmap mDecalsBitmap, final LocalMedia item) {
        final Bitmap last = compoundBitmap(srcBitmap, mDecalsBitmap);
        String tempPath = FileUtils.saveBitmap(last, null, bitmapFormat);
        if (!FileUtils.isFileExist(tempPath)) {
            iView.errorDataReport(5005, mList.size(), 0);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    iView.hideLoading();
                    ToastUtil.showShortToast(mAct, "图片保存失败，请重试");
                }
            });
            return;
        }
        if (!TextUtils.isEmpty(item.getTempPath())) {
            FileUtils.deleteFile(item.getTempPath());
        }
        item.setTempPath(tempPath);

        String filterInfo = item.rFilterBean != null ? item.rFilterBean.name : "0";
        item.setFilterInfo(filterInfo);
        item.addExtra(LocalMedia.MM_EDIT_FILTER_ID, filterInfo);

        String decalsInfo = getDecalsIds(item);
        item.setPasterInfo(decalsInfo);
        item.addExtra(LocalMedia.MM_DECAL_ID, TextUtils.isEmpty(decalsInfo) ? "0" : decalsInfo);

        String fontInfo = getFontsIds(item);
        item.addExtra(LocalMedia.MM_IMAGE_FONT_ID, TextUtils.isEmpty(fontInfo) ? "0" : fontInfo);

        continueCompound();
    }


    /**
     * 获取文字贴纸截图
     *
     * @param originalBitmap original bitmap
     * @param item           item
     */
    private void getPasterBitmap(final Bitmap originalBitmap, final LocalMedia item) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                int width = originalBitmap == null ? 0 : originalBitmap.getWidth();
                int height = originalBitmap == null ? 0 : originalBitmap.getHeight();
                boolean isSameSize = adjustSize(mEditViewPager, null, mSavePasteLayout,paintView, width, height);
                applyPasterInfo(mSavePasteLayout, item, isSameSize, new View.OnLayoutChangeListener() {
                    @Override
                    public void onLayoutChange(View view, int left, int top, int right, int bottom,
                                               int oldLeft, int oldTop, int oldRight, int oldBottom) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                final Bitmap mDecalsBitmap = mSavePasteLayout.getBitmapFromView();
                                mSavePasteLayout.clear();
                                getCallerRunsPool().execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        saveFinalBitmap(originalBitmap, mDecalsBitmap, item);
                                    }
                                });
                            }
                        });
                    }
                });
                if (mSavePasteLayout != null) {
                    mSavePasteLayout.requestLayout();
                } else {
                    ToastUtil.showShortToast(mAct, "图片保存异常，请重试");
                    iView.hideLoading();
                }
            }
        });
    }


    /**
     * Gets decals ids *
     *
     * @param item item
     * @return the decals ids
     */
    public String getDecalsIds(LocalMedia item) {
        if (item == null) {
            return "";
        }
        ArrayList<PasteBean> rPasterBean = item.rPasterBean;
        if (rPasterBean == null || rPasterBean.size() <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        try {
            int count =0;
            for (int i = 0; i < rPasterBean.size(); i++) {
                PasteBean pBean = rPasterBean.get(i);
                if (pBean.type == ReBean.TYPE.DECALS && pBean.bean != null) {
                    if (count>=1) {
                        sb.append(",");
                    }
                    sb.append(pBean.bean.id);
                    count++;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return sb.toString();
    }


    /**
     * Gets decals ids *
     *
     * @param item item
     * @return the decals ids
     */
    public String getFontsIds(LocalMedia item) {
        if (item == null) {
            return "";
        }
        ArrayList<PasteBean> rPasterBean = item.rPasterBean;
        if (rPasterBean == null || rPasterBean.size() <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        try {
            int count =0;
            for (int i = 0; i < rPasterBean.size(); i++) {
                PasteBean pBean = rPasterBean.get(i);
                if (pBean.type == ReBean.TYPE.FONT ) {
                    if (count>=1) {
                        sb.append(",");
                    }
                    sb.append(pBean.bean==null?"0":pBean.bean.id);
                    sb.append("_");
                    sb.append(pBean.stylebean==null?"0":pBean.stylebean.id);
                    count++;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return sb.toString();
    }


    private static void addPasteToPasterLayout(final PasteLayout mPasteLayout, final ArrayList<PasteBean> pasterBean){
        if (mPasteLayout == null || pasterBean == null || pasterBean.size() <= 0) {
            return;
        }
        mPasteLayout.clear();
        for (PasteBean bean : pasterBean) {
            if (bean.type == ReBean.TYPE.FONT) {
                @SuppressLint("InflateParams")
                FontView fView = (FontView) LayoutInflater.from(mPasteLayout.getContext()).inflate(R.layout.mm_font_layout, null);
                fView.setFont(bean, mPasteLayout);
            }
        }
    }

    /**
     * 缓存贴纸文字信息
     *
     * @param mPasteLayout           m paste layout
     * @param item                   item
     * @param onLayoutChangeListener on layout change listener
     */
    public static void applyPasterInfo(final PasteLayout mPasteLayout, final LocalMedia item,boolean isSameSize, final View.OnLayoutChangeListener onLayoutChangeListener) {
        if (mPasteLayout == null || item == null) {
            return;
        }
        final ArrayList<PasteBean> pasterBean = item.rPasterBean;
        if (pasterBean != null && pasterBean.size() > 0) {
            if(isSameSize) {
                mPasteLayout.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        addPasteToPasterLayout(mPasteLayout, pasterBean);
                        if (onLayoutChangeListener != null) {
                            onLayoutChangeListener.onLayoutChange(mPasteLayout, mPasteLayout.getLeft(), mPasteLayout.getTop(), mPasteLayout.getRight(), mPasteLayout.getBottom(), 0, 0, 0, 0);
                        }
                    }
                },100);
            }else {
                mPasteLayout.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
                    @Override
                    public void onLayoutChange(View view, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                        addPasteToPasterLayout(mPasteLayout, pasterBean);
                        view.removeOnLayoutChangeListener(this);
                        if (onLayoutChangeListener != null) {
                            onLayoutChangeListener.onLayoutChange(view, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom);
                        }
                    }
                });
            }
        }

    }


    /**
     * Ad just size *
     *
     * @param viewGroup       view group
     * @param viewImage       m edit image view
     * @param viewPasteLayout m paste layout
     * @param width           width
     * @param height          height
     */
    public static boolean adjustSize(View viewGroup, View viewImage, View viewPasteLayout, View paintView,int width, int height) {

        if (viewGroup == null) {
            return false;
        }

        float viewWidth = viewGroup.getWidth();
        float viewHeight = viewGroup.getHeight();

        if (width <= 0) {
            width = (int) viewWidth;
        }
        if (height <= 0) {
            height = (int) viewHeight;
        }

        //更新当前编辑的图片
        float minRate = Math.min(viewWidth / width, viewHeight / height);
        int w = (int) (width * minRate);
        int h = (int) (height * minRate);
        if (viewImage != null) {
            viewImage.getLayoutParams().height = w;
            viewImage.getLayoutParams().height = h;
        }

        if (paintView != null) {
            ViewGroup.LayoutParams layoutParams = paintView.getLayoutParams();
            if (layoutParams != null) {
                layoutParams.width = w;
                layoutParams.height = h;
            }
        }

        if (viewPasteLayout != null) {
            int lWidth = viewPasteLayout.getWidth();
            int lHeight = viewPasteLayout.getHeight();
            viewPasteLayout.getLayoutParams().width = w;
            viewPasteLayout.getLayoutParams().height = h;
            if (lWidth >= 0 && lHeight >= 0 && w == lWidth && h == lHeight) {
//                lWidth 与 lHeight 为0 说明控件还获取不到宽高 。目标宽高与当前宽高如果一致。不需要调整，不走Onlayout事件
                viewGroup.requestLayout();
                return true;
            }
        }
        viewGroup.requestLayout();
        return false;
    }


    /**
     * 合成图片
     *
     * @param mSrcBitmap    当前编辑的图片
     * @param mDecalsBitmap 贴纸生成的图片，相当于View截图
     * @return the bitmap
     */
    private Bitmap compoundBitmap(Bitmap mSrcBitmap, Bitmap mDecalsBitmap) {
        if (mDecalsBitmap == null) {
            return mSrcBitmap;
        }
        if (mSrcBitmap == null || mSrcBitmap.isRecycled()) {
            return null;
        }

        int decalsWidth = mDecalsBitmap.getWidth();
        int decalsHeight = mDecalsBitmap.getHeight();

        float bitmapWidth = mSrcBitmap.getWidth();
        float bitmapHeight = mSrcBitmap.getHeight();

        if (bitmapWidth < decalsWidth || bitmapHeight < decalsHeight) {
            Bitmap tagBitmap = Bitmap.createBitmap(decalsWidth, decalsHeight, Bitmap.Config.ARGB_8888);
            Canvas canvasAdjust = new Canvas(tagBitmap);
            canvasAdjust.drawBitmap(mSrcBitmap, null, new Rect(0, 0, decalsWidth, decalsHeight), null);
            mSrcBitmap = tagBitmap;
        }
        bitmapWidth = mSrcBitmap.getWidth();
        bitmapHeight = mSrcBitmap.getHeight();


        float minRateW = bitmapWidth / decalsWidth;
        float minRateH = bitmapHeight / decalsHeight;
        float mRate = Math.max(minRateW, minRateH);

        Matrix matrix = new Matrix();
        matrix.postScale(mRate, mRate);

        float offsetX = (bitmapWidth - decalsWidth * mRate) / 2;
        float offsetY = (bitmapHeight - decalsHeight * mRate) / 2;
        matrix.postTranslate(offsetX, offsetY);


        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setFilterBitmap(true);

        Bitmap tagBitmap = Bitmap.createBitmap(mSrcBitmap.getWidth(), mSrcBitmap.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvasLast = new Canvas(tagBitmap);
        canvasLast.drawColor(0xffffffff);
        canvasLast.drawBitmap(mSrcBitmap, 0, 0, paint);
        canvasLast.drawBitmap(mDecalsBitmap, matrix, paint);
        mSrcBitmap.recycle();
        if (!mDecalsBitmap.isRecycled()) {
            mDecalsBitmap.recycle();
        }
        return tagBitmap;
    }

    /**
     * Run on ui thread *
     *
     * @param r Runnable
     */
    private void runOnUiThread(Runnable r) {
        COMPOUND_HANDLER.post(r);
    }
}
