package com.jd.oa.lib.editor;

import android.app.Activity;
import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.AppBase;
import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.pub.Constants;

import java.util.ArrayList;

/*
 * Time: 2024/6/3
 * Author: qudongshi
 * Description:
 */
public class PhotoEditorHelper {

    public static void openPhotoEditor(String filePath, Activity activity, boolean autoClose, IPhotoEditorCallback callback) {
        int REQUEST_CODE_MEDIA = 58702;
        String channel = "evaluate";
        ArrayList<LocalMedia> list = new ArrayList<>();
        LocalMedia m = new LocalMedia();
        m.setPath(filePath);
        list.add(m);
        PhotoEditer.builder().editPhotos(list).channel(channel).maxPhotoNumber(1).start(activity, callback, REQUEST_CODE_MEDIA, autoClose);
    }

    public static void closePhotoEditor() {
        Intent intent = new Intent(Constants.ACTION_EDITOR_PHOTO_CLOSE);
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(intent);
    }
}
