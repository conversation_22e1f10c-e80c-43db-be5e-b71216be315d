package com.jd.oa.lib.editor.photo.paste.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Point;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.lib.editor.pub.data.ReBean;


/**
 * Paste layout
 *
 * <AUTHOR> zhou min Email: <EMAIL>
 * @date : 2018/8/1 Time: 下午3:37 Description:  贴纸与文字的容器
 */
public class PasteLayout extends RelativeLayout implements PasteListener, View.OnLayoutChangeListener {

    /**
     * 最后操作的贴纸
     */
    private IViewType mLastView;

    /**
     * Decals layout
     *
     * @param context context
     */
    public PasteLayout(@NonNull Context context) {
        super(context);
    }

    /**
     * Decals layout
     *
     * @param context context
     * @param attrs   attrs
     */
    public PasteLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    /**
     * Decals layout
     *
     * @param context      context
     * @param attrs        attrs
     * @param defStyleAttr def style attr
     */
    public PasteLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    /**
     * Removed *
     *
     * @param view view
     */
    @Override
    public void removed(IViewType view) {
        if (mListener != null) {
            mListener.removed(view);
        }
        if (view instanceof FontView) {
            FontView lastFontView = null;
            for (int i = getChildCount() - 1; i >= 0; i--) {
                View child = getChildAt(i);
                if (child instanceof FontView) {
                    lastFontView = (FontView) child;
                    break;
                }
            }
            mLastView = lastFontView;
            if (mLastView != null) {
                mLastView.setControlVisible(true);
                setLastView(mLastView, lastFontView.getReBean(), lastFontView.getStyleBean());
            }
        } else {
            if (mLastView == view) {
                mLastView = null;
            }
        }

    }

    /**
     * Double type *
     *
     * @param view   view
     * @param reBean reBean
     * @param sBean  s bean
     */
    @Override
    public void doubleType(IViewType view, ReBean reBean, StyleBean sBean) {
        if (mListener != null) {
            boolean hasMarkLayer = false;
            for (int i = getChildCount() - 1; i >= 0; i--) {
                if (getChildAt(i) == mListener.getMaskLayer()) {
                    hasMarkLayer = true;
                    break;
                }
            }
            if (!hasMarkLayer) {
                super.removeView(view.getView());
                addView(mListener.getMaskLayer());
                super.addView(view.getView());
            }
            mListener.doubleType(view, reBean, sBean);
        }
    }

    /**
     * Sets last view *
     *
     * @param lastView last view
     * @param reBean   re bean
     * @param sBean    s bean
     */
    @Override
    public void setLastView(IViewType lastView, ReBean reBean, StyleBean sBean) {
        if (mLastView != null && mLastView != lastView) {
            mLastView.setControlVisible(false);
        }
        mLastView = lastView;
        //交点击的放到最上层逻辑
        if (getChildCount() > 0) {
            if (mLastView != null && mLastView != getChildAt(getChildCount() - 1)) {
                removeView(mLastView.getView());
                super.addView(mLastView.getView());
            }
        }
        if (mListener != null) {
            mListener.setLastView(lastView, reBean, sBean);
        }
    }


    /**
     * Is center xin layout boolean
     *
     * @param view  view
     * @param x     x
     * @param width width
     * @return the boolean
     */
    @Override
    public boolean isCenterXinLayout(IViewType view, float x, int width) {
        if (view instanceof DecalsView) {
            return x >= 0 && x <= getWidth();
        } else if (view instanceof FontView) {
            return x + width / 2 >= FontView.LIMIT_DISTANCE && x - width / 2 <= getWidth() - FontView.LIMIT_DISTANCE;
        }
        return false;
    }

    /**
     * Is center yin layout boolean
     *
     * @param view   view
     * @param y      y
     * @param height height
     * @return the boolean
     */
    @Override
    public boolean isCenterYinLayout(IViewType view, float y, int height) {
        if (view instanceof DecalsView) {
            return y >= 0 && y <= getHeight();
        } else if (view instanceof FontView) {
            return y + height / 2 >= FontView.LIMIT_DISTANCE && y - height / 2 <= getHeight() - FontView.LIMIT_DISTANCE;
        }
        return false;
    }


    /**
     * 增加贴纸
     *
     * @param child child
     */
    public void addViewWithType(IViewType child) {
        if (child != null) {
            child.setListener(this);
            if (mLastView != null && mLastView != child) {
                mLastView.setControlVisible(false);
            }
            mLastView = child;
            child.setCenterPoint(getWidth(), getHeight());
            if(mListener != null){
                if (mLastView instanceof FontView) {
                    super.addView(mListener.getMaskLayer());
                }
            }
            super.addView(child.getView());
            requestLayout();
        }
    }

    /**
     * 控制是否显示最后一个view 的操作按钮
     */
    public void hideAllControlVisible() {
        if (mLastView != null) {
            mLastView.setControlVisible(false);
            mLastView = null;
            if (mListener != null) {
                mListener.setLastView(null, null, null);
            }
        }
    }

    /**
     * down x
     */
    float mDownX = 0;
    /**
     * down y
     */
    float mDownY = 0;

    /**
     * On touch event boolean
     *
     * @param event event
     * @return the boolean
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        Log.i("DecalsAndFontLayout", event.getAction() + "");
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = event.getX();
                mDownY = event.getY();
                if (!isPointOnViews(this, event)) {
                    if (!isFontEditing()) {
                        hideAllControlVisible();
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                if (Math.abs(event.getX() - mDownX) < 50 && Math.abs(event.getY() - mDownY) < 50) {
                    if (!isPointOnViews(this, event)) {
                        if (!isFontEditing()) {
                            hideAllControlVisible();
                        }
                    }
                }
                break;
            default:
                break;

        }
        return super.onTouchEvent(event);
    }


    /**
     * 是否还有文字控件
     *
     * @return the boolean
     */
    public boolean isHasMoreFontView() {
        int count = 0;
        for (int i = 0; i < getChildCount(); i++) {
            View view = getChildAt(i);
            if (view instanceof FontView) {
                count++;
            }
        }
        return count > 0;

    }

    /**
     * 控制View 是否可操控
     *
     * @param isFontView true 控制文字View false 贴纸View
     * @param is         is true 可操作状态  false 不可操控
     */
    public void updateEditAble(boolean isFontView, boolean is) {
        for (int i = 0; i < getChildCount(); i++) {
            View view = getChildAt(i);
            if (isFontView) {
                if (view instanceof FontView) {
                    FontView type = (FontView) view;
                    type.setEditable(is);
                }
            } else {
                if (view instanceof DecalsView) {
                    DecalsView type = (DecalsView) view;
                    type.setEditable(is);
                }
            }
        }
    }


    /**
     * 位置更新
     *
     * @param width  width
     * @param height height
     */
    public void updatePosition(int width, int height) {
        for (int i = 0; i < getChildCount(); i++) {
            View view = getChildAt(i);
            if (view instanceof IViewType) {
                ((IViewType) view).updateCenterPoint(width, height);
            }
        }
    }

    /**
     * Clear
     */
    public void clear() {
        removeAllViews();
        mLastView = null;

    }


    /**
     * Disable child edit able
     */
    public void disableChildEditAble() {
        for (int i = 0; i < getChildCount(); i++) {
            View view = getChildAt(i);
            if (view instanceof DecalsView) {
                DecalsView type = (DecalsView) view;
                type.setEditable(false);
            }
        }
    }

    /**
     * 从View获取Bitmap
     *
     * @param view   View
     * @param config config
     * @return Bitmap bitmap from view
     */
    public static Bitmap getBitmapFromView(View view, Bitmap.Config config) {
        if (view == null || view.getWidth() == 0 || view.getHeight() == 0) {
            return null;
        }
        Bitmap bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(),
                config);
        Canvas canvas = new Canvas(bitmap);
        view.layout(view.getLeft(), view.getTop(), view.getRight(),
                view.getBottom());
        view.draw(canvas);

        return bitmap;
    }

    /**
     * 从View获取Bitmap
     *
     * @return Bitmap bitmap from view
     */
    public Bitmap getBitmapFromView() {
        if (getWidth() == 0 || getHeight() == 0 || getChildCount() <= 0) {
            return null;
        }
        hideAllControlVisible();
        Bitmap bitmap = Bitmap.createBitmap(getWidth(), getHeight(),
                Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        layout(getLeft(), getTop(), getRight(),
                getBottom());
        draw(canvas);
        return bitmap;
    }


    /**
     * 判断触摸的位置是否落在 child 可编辑区域身上
     *
     * @param viewGroup view group
     * @param ev        ev
     * @return the boolean
     */
    private boolean isPointOnViews(ViewGroup viewGroup, MotionEvent ev) {
        boolean result = false;
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof IViewType) {
                IViewType type = (IViewType) view;
                if (type.isPointInRect(new Point((int) ev.getX(), (int) ev.getY()), true)) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }


    /**
     * Apply text boolean
     *
     * @param text text
     */
    public void applyText(String text) {
        if (mLastView != null && mLastView instanceof FontView) {
            FontView view = (FontView) mLastView;
            view.setText(text, true);
        }
    }

    public void removeLastFontView() {
        if (mLastView != null && mLastView instanceof FontView) {
            removed(mLastView);
            ((FontView) mLastView).removeSelf();
        }
    }

    /**
     * Apply font boolean
     *
     * @param bean bean
     * @return the boolean
     */
    public boolean applyFont(ReBean bean) {
        if (mLastView != null && mLastView instanceof FontView) {
            FontView view = (FontView) mLastView;
            view.setTypeFace(bean, true);
            return true;
        }
        return false;
    }

    /**
     * Apply style boolean
     *
     * @param bean bean
     * @return the boolean
     */
    public boolean applyStyle(StyleBean bean) {
        if (mLastView != null && mLastView instanceof FontView) {
            FontView view = (FontView) mLastView;
            view.setStyle(bean, true);
            currentStyleBean = bean;
            return true;
        }
        return false;
    }

    public StyleBean currentStyleBean;

    /**
     * listener
     */
    private Listener mListener;

    /**
     * Sets listener *
     *
     * @param mListener m listener
     */
    public void setListener(Listener mListener) {
        this.mListener = mListener;
    }

    /**
     * Is font editing boolean
     *
     * @return the boolean
     */
    @Override
    public boolean isFontEditing() {
        return mListener != null && mListener.isFontEditing();
    }

    @Override
    public View getMaskLayer() {
        if (mListener == null) {
            return null;
        }
        return mListener.getMaskLayer();
    }

    /**
     * On attached to window
     */
    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        addOnLayoutChangeListener(this);
    }

    /**
     * On detached from window
     */
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeOnLayoutChangeListener(this);
    }

    /**
     * On layout change *
     *
     * @param view      view
     * @param left      left
     * @param top       top
     * @param right     right
     * @param bottom    bottom
     * @param oldLeft   old left
     * @param oldTop    old top
     * @param oldRight  old right
     * @param oldBottom old bottom
     */
    @Override
    public void onLayoutChange(View view, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
        int newWidth = right - left;
        int newHeight = bottom - top;
        int oldWidth = oldRight - left;
        int oldHeight = oldBottom - oldTop;
        if (newWidth != oldWidth || newHeight != oldHeight) {
            updatePosition(newWidth, newHeight);
        }
    }
}
