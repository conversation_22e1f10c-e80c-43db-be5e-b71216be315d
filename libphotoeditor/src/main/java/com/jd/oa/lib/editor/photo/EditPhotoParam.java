package com.jd.oa.lib.editor.photo;

import android.os.Parcel;

import com.jd.oa.lib.editor.base.BaseParam;
import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.pub.MmType;

import java.util.ArrayList;

/**
 * des: 图片编辑配置参数
 * <EMAIL>
 * erp:zhoumin117
 *
 * @author： zhoumin6
 * @date： 2020 /8/12.4:05 PM
 */
public class EditPhotoParam extends BaseParam {


    /**
     * 最多选择图片数量
     */
    public int maxPhotoNumber;

    /**
     * 最大贴纸数
     */
    public int maxDecalsNumber;

    /**
     * 需要编辑的图片路径数组
     */
    public ArrayList<LocalMedia> photoPath;


    /**
     * M from
     */
    public MmType.FROM_TYPE mFrom = MmType.FROM_TYPE.ALBUM;

    /**
     * 数据是否可用，必须得要有图片数据
     *
     * @return the boolean
     */
    public boolean isValid() {
        return photoPath != null && photoPath.size() > 0;
    }


    /**
     * 构造
     */
    public EditPhotoParam() {
        showFollowTake = true;
        showProps = true;
    }

    /**
     * Gets max photo number *
     *
     * @return the max photo number
     */
    public int getMaxPhotoNumber() {
        if (maxPhotoNumber > 15) {
            maxPhotoNumber = 15;
        }
        return maxPhotoNumber;
    }

    /**
     * Gets max decals number *
     *
     * @return the max decals number
     */
    public int getMaxDecalsNumber() {
        if (maxDecalsNumber > 50) {
            maxDecalsNumber = 50;
        }
        return maxDecalsNumber;
    }


    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeInt(this.maxPhotoNumber);
        dest.writeInt(this.maxDecalsNumber);
        dest.writeTypedList(this.photoPath);
        dest.writeInt(this.mFrom == null ? -1 : this.mFrom.ordinal());
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    @Override
    public void readFromParcel(Parcel source) {
        super.readFromParcel(source);
        this.maxPhotoNumber = source.readInt();
        this.maxDecalsNumber = source.readInt();
        this.photoPath = source.createTypedArrayList(LocalMedia.CREATOR);
        int tmpMFrom = source.readInt();
        this.mFrom = tmpMFrom == -1 ? null : MmType.FROM_TYPE.values()[tmpMFrom];
    }

    /**
     * Edit photo param
     *
     * @param in in
     */
    protected EditPhotoParam(Parcel in) {
        super(in);
        this.maxPhotoNumber = in.readInt();
        this.maxDecalsNumber = in.readInt();
        this.photoPath = in.createTypedArrayList(LocalMedia.CREATOR);
        int tmpMFrom = in.readInt();
        this.mFrom = tmpMFrom == -1 ? null : MmType.FROM_TYPE.values()[tmpMFrom];
    }

    /**
     * CREATOR
     */
    public static final Creator<EditPhotoParam> CREATOR = new Creator<EditPhotoParam>() {
        @Override
        public EditPhotoParam createFromParcel(Parcel source) {
            return new EditPhotoParam(source);
        }

        @Override
        public EditPhotoParam[] newArray(int size) {
            return new EditPhotoParam[size];
        }
    };
}
