package com.jd.oa.lib.editor.photo.clip;

import android.graphics.RectF;

/**
 * 定义裁剪框相关参数以及锚点
 * <AUTHOR>
 * @date 21-11-17
 */
public interface CutImageInterface {

    /**
     * CLIP_MARGIN
     */
    float CLIP_MARGIN = 0f;

    /**
     * CLIP_CORNER_SIZE
     */
    float CLIP_CORNER_SIZE = 48f;

    /**
     * CLIP_FRAME_MIN
     */
    float CLIP_FRAME_MIN = CLIP_CORNER_SIZE * 3.14f;

    /**
     * CLIP_THICKNESS_FRAME
     */
    float CLIP_THICKNESS_FRAME = 2f;

    /**
     * CLIP_THICKNESS_SEWING
     */
    float CLIP_THICKNESS_SEWING = 6f;

    /**
     * Clip size ratio
     */
    float[] CLIP_SIZE_RATIO = {0, 1, 0.33f, 0.66f};

    /**
     * CLIP_CORNER_STRIDES
     */
    int CLIP_CORNER_STRIDES = 0x0AAFF550;

    /**
     * Clip corner steps
     */
    float[] CLIP_CORNER_STEPS = {0, 3, -3};

    /**
     * Clip corner sizes
     */
    float[] CLIP_CORNER_SIZES = {0, CLIP_CORNER_SIZE, -CLIP_CORNER_SIZE};

    /**
     * Clip corners
     */
    byte[] CLIP_CORNERS = {
            0x8, 0x8, 0x9, 0x8,
            0x6, 0x8, 0x4, 0x8,
            0x4, 0x8, 0x4, 0x1,
            0x4, 0xA, 0x4, 0x8,
            0x4, 0x4, 0x6, 0x4,
            0x9, 0x4, 0x8, 0x4,
            0x8, 0x4, 0x8, 0x6,
            0x8, 0x9, 0x8, 0x8
    };

    /**
     * Anchor
     */
    enum Anchor {
        /**
         * Left anchor
         */
        LEFT(1),//1
        /**
         * Right anchor
         */
        RIGHT(2),//10
        /**
         * Top anchor
         */
        TOP(4),//100
        /**
         * Bottom anchor
         */
        BOTTOM(8),//1000
        /**
         * Left top anchor
         */
        LEFT_TOP(5),//101
        /**
         * Right top anchor
         */
        RIGHT_TOP(6),//110
        /**
         * Left bottom anchor
         */
        LEFT_BOTTOM(9),//1001
        /**
         * Right bottom anchor
         */
        RIGHT_BOTTOM(10);//1010
        /**
         * 1 10 100 1000
         */
        int v;

        /**
         * Pn
         */
        static final int[] PN = {1, -1};

        /**
         *
         * @param v v
         */
        Anchor(int v) {
            this.v = v;
        }

        /**
         * Move *
         *
         * @param win   win
         * @param frame frame
         * @param dx    dx
         * @param dy    dy
         */
        public void move(RectF win, RectF frame, float dx, float dy) {
            float[] maxFrame = getFrame(win, CLIP_MARGIN);
            float[] minFrame = getFrame(frame, CLIP_FRAME_MIN);
            float[] theFrame = getFrame(frame, 0);

            float[] dxy = {dx, 0, dy};
            for (int i = 0; i < 4; i++) {
                // 判断v的第i位是否为1
                if (((1 << i) & v) != 0) {
                    int pn = PN[i & 1];
                    theFrame[i] = pn * getRealValue(pn * (theFrame[i] + dxy[i & 2]),
                            pn * maxFrame[i], pn * minFrame[i + PN[i & 1]]);
                }
            }

            frame.set(theFrame[0], theFrame[2], theFrame[1], theFrame[3]);
        }

        /**
         * Gets real value *
         *
         * @param v   v
         * @param min min
         * @param max max
         * @return the real value
         */
        public static float getRealValue(float v, float min, float max) {
            return Math.min(Math.max(v, min), max);
        }

        /**
         * Get frame float [ ]
         *
         * @param win win
         * @param v   v
         * @return the float [ ]
         */
        public static float[] getFrame(RectF win, float v) {
            return new float[]{
                    win.left + v, win.right - v,
                    win.top + v, win.bottom - v
            };
        }

        /**
         * Is cohesion contains boolean
         *
         * @param frame frame
         * @param v     v
         * @param x     x
         * @param y     y
         * @return the boolean
         */
        public static boolean isCohesionContains(RectF frame, float v, float x, float y) {
            return frame.left + v < x && frame.right - v > x
                    && frame.top + v < y && frame.bottom - v > y;
        }

        /**
         * Value of anchor
         *
         * @param v v
         * @return the anchor
         */
        public static Anchor valueOf(int v) {
            Anchor[] values = values();
            for (Anchor anchor : values) {
                if (anchor.v == v) {
                    return anchor;
                }
            }
            return null;
        }
    }
}