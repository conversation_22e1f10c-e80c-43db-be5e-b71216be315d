package com.jd.oa.lib.editor.photo.paint;

import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Path;
import android.graphics.RectF;
import android.view.MotionEvent;

import com.jd.oa.abilities.utils.MELogUtil;

import java.util.Iterator;
import java.util.Stack;

public abstract class PaintStruct {

    private final String TAG = "PaintStruct";
    protected TouchStack currentStack;
    public final Stack<TouchStack> touchStacks = new Stack<>();
    protected Stack<TouchStack> cacheTouchStacks;

    public float canvasWidth, canvasHeight;
    private boolean isActive = false;

    public void onStackBack() {
        if (this.touchStacks.isEmpty()) {
            return;
        }
        this.touchStacks.pop();
        popStack();
    }

    public void setCanvasSize(float canvasWidth, float canvasHeight) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
    }

    public void changeCanvasSize(float canvasWidth, float canvasHeight) {
        if (this.canvasWidth == canvasWidth && this.canvasHeight == canvasHeight) {
            return;
        }
        if (this.canvasWidth == 0 && this.canvasHeight == 0) {
            this.canvasWidth = canvasWidth;
            this.canvasHeight = canvasHeight;
            return;
        }
        Matrix matrix = new Matrix();
        RectF src = new RectF(0, 0, this.canvasWidth, this.canvasHeight);
        RectF dst = new RectF(0, 0, canvasWidth, canvasHeight);
        matrix.setRectToRect(src, dst, Matrix.ScaleToFit.FILL);
        changeCache(matrix, canvasWidth / this.canvasWidth);
        this.setCanvasSize(canvasWidth, canvasHeight);
    }

    protected void changeCache(Matrix matrix, float scale) {
        changeCache(matrix, scale, cacheTouchStacks);
        changeCache(matrix, scale, touchStacks);
    }

    private void changeCache(Matrix matrix, float scale, Stack<TouchStack> touchStacks) {
        if (touchStacks == null) {
            return;
        }
        Iterator<TouchStack> iterator = touchStacks.iterator();
        while (iterator.hasNext()) {
            TouchStack next = iterator.next();
            next.path.transform(matrix);
            next.size = next.size * scale;
        }
    }

    public void setCacheTouchStack(Stack<TouchStack> cacheTouchStacks) {
        this.cacheTouchStacks = cacheTouchStacks;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isActive() {
        return isActive;
    }

    public boolean onTouchEvent(MotionEvent event) {
        if (!isActive) {
            return false;
        }
        float x = event.getX();
        float y = event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                currentStack = createTouchInfo();
                touchStacks.add(currentStack);
                addStack();
                MELogUtil.localD(TAG, "onTouchEvent ACTION_DOWN touchStacks.ad");
                if (currentStack != null) {
                    currentStack.path.moveTo(x, y);
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (currentStack != null) {
                    currentStack.path.lineTo(x, y);
                }
                break;

            case MotionEvent.ACTION_UP:
                break;
        }
        return true;
    }


    protected abstract TouchStack createTouchInfo();

    public final void onDraw(Canvas canvas) {
        if (canvas.getWidth() != this.canvasWidth || canvas.getHeight() != this.canvasHeight) {
            changeCanvasSize(canvas.getWidth(), canvas.getHeight());
        }
        doDraw(canvas);
    }

    public abstract void doDraw(Canvas canvas);

    public abstract void addStack();

    public abstract void popStack();

    public boolean hasStack() {
        if (touchStacks == null) {
            return false;
        }
        return touchStacks.size() > 0;
    }

    public static final class TouchStack {
        public Path path;
        public final int color;
        public float size;

        public TouchStack(Path path, int color, float size) {
            this.path = path;
            this.color = color;
            this.size = size;
        }

    }
}
