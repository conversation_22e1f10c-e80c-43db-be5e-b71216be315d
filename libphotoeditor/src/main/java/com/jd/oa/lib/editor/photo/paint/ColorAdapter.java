package com.jd.oa.lib.editor.photo.paint;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.utils.UnitUtils;

import java.util.List;

public class ColorAdapter extends RecyclerView.Adapter<ColorAdapter.ColorViewHolder> {
    private final List<String> mData;
    private final int viewSize, viewMargin, viewPadding;

    private OnItemClickListener onItemClickListener;
    private int lastSelectPosition = 2;

    // 提供数据
    public ColorAdapter(List<String> data, Context context) {
        this.mData = data;
        viewSize = UnitUtils.dip2px(context, 30);
        viewMargin = UnitUtils.dip2px(context, 2);
        viewPadding = UnitUtils.dip2px(context, 4);
    }

    // 创建新视图（由布局管理器调用）
    @Override
    public ColorViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        ColorItem view = new ColorItem(parent.getContext());
        ViewGroup.MarginLayoutParams marginLayoutParams = new ViewGroup.MarginLayoutParams(viewSize, viewSize);
        marginLayoutParams.leftMargin = marginLayoutParams.rightMargin = viewMargin;
        view.setPadding(viewPadding, viewPadding, viewPadding, viewPadding);
        view.setLayoutParams(marginLayoutParams);
        ColorViewHolder viewHolder = new ColorViewHolder(view);
        return viewHolder;
    }

    // 替换内容（由布局管理器调用）
    @Override
    public void onBindViewHolder(ColorViewHolder holder, int position) {
        holder.colorItem.setSelected(position == lastSelectPosition);
        holder.colorItem.setColor(mData.get(position));
    }

    // 返回数据集大小（由布局管理器调用）
    @Override
    public int getItemCount() {
        return mData.size();
    }

    public String getSelectColor() {
        return mData.get(lastSelectPosition);
    }

    public void setSelectColor(String color) {
        int colorIndexOf = mData.indexOf(color);
        if (colorIndexOf >= 0) {
            lastSelectPosition = colorIndexOf;
            notifyDataSetChanged();
        }
    }

    public interface OnItemClickListener {
        void onItemClick(String color);
    }

    // 在Adapter中设置点击事件监听器
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    // 自定义的 ViewHolder
    public class ColorViewHolder extends RecyclerView.ViewHolder {

        private ColorItem colorItem;

        public ColorViewHolder(ColorItem view) {
            super(view);
            colorItem = view;
            colorItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener == null) {
                        return;
                    }
                    int position = getAdapterPosition();
                    if (position == RecyclerView.NO_POSITION) {
                        return;
                    }
                    if (lastSelectPosition != -1) {
                        int tempPosition = lastSelectPosition;
                        lastSelectPosition = position;
                        notifyItemChanged(tempPosition);
                    }
                    lastSelectPosition = position;
                    notifyItemChanged(position);
                    onItemClickListener.onItemClick(mData.get(position));
                }
            });
        }


    }
}
