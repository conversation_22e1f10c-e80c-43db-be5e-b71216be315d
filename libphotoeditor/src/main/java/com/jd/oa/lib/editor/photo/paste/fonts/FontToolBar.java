package com.jd.oa.lib.editor.photo.paste.fonts;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.lib.editor.base.OnClickLimitListener;
import com.jd.oa.lib.editor.photo.R;
import com.jd.oa.lib.editor.photo.paint.ColorAdapter;
import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.lib.editor.pub.Constants;
import com.jd.oa.lib.editor.pub.data.ReBean;
import com.jd.oa.lib.editor.util.ColorUtil;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.DensityUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * des: 文字工具条
 *
 * <AUTHOR>
 * @date 2021 /9/9 : 4:25 下午
 * erp ：  zhoumin117
 * mail ：    <EMAIL>
 */
public class FontToolBar extends LinearLayout {

    /**
     * 输入框
     */
    private EditText mEditInput;

    /**
     * 操作回调
     */
    private Listener mListener;

    private RecyclerView mRecyclerView;
    private ColorAdapter fontAdapter;

    private boolean isInit = false;

    private String FONT_STYLE_MODE = Constants.MODE_FONT_STYLE_COLOR;

    private IconFontView ifv_font_style;

    private CharSequence oldContent;

    /**
     * Font tool bar
     *
     * @param context context
     */
    public FontToolBar(Context context) {
        super(context);
        init(context);
    }

    /**
     * Font tool bar
     *
     * @param context context
     * @param attrs   attrs
     */
    public FontToolBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    /**
     * Font tool bar
     *
     * @param context      context
     * @param attrs        attrs
     * @param defStyleAttr def style attr
     */
    public FontToolBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    /*** 初始化  @param context context @param context context*/
    private void init(Context context) {
        setOrientation(VERTICAL);
//        LayoutInflater.from(context).inflate(DataConfig.getInstance().getResourceId(R.layout.mm_font_edit_layout), this, true);
        LayoutInflater.from(context).inflate(R.layout.mm_font_edit_layout, this, true);
        setBackgroundColor(Color.TRANSPARENT);
        mEditInput = findViewById(R.id.editInput);
        /* 删除文字按钮 */
        ImageView mIvDelete = findViewById(R.id.iv_delete);
//        if (!DataConfig.getInstance().isDarkMode()) {
//            mIvDelete.setColorFilter(ColorButtom.getColorMatrixColorFilter(true));
//        }

        /* 样式列表 */
        mRecyclerView = findViewById(R.id.recyclerViewFont);

        mEditInput.addTextChangedListener(watcher);

        mIvDelete.setOnClickListener(onClickLimitListener);

//        mIvClearFont.setOnClickListener(onClickLimitListener);
//
//        mFlKeyboard.setOnClickListener(onClickLimitListener);
//
//        mFlStyle.setOnClickListener(onClickLimitListener);

//        mIvConfirm.setOnClickListener(onClickLimitListener);
        if (!isInit) {
            mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            mRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                    super.getItemOffsets(outRect, view, parent, state);
                    if (parent.getChildLayoutPosition(view) != 0) {
                        outRect.left = DensityUtil.dp2px(getContext(), 15.0f);
                    }
                }
            });

        }
        ifv_font_style = findViewById(R.id.ifv_font_style);
        ifv_font_style.setOnClickListener(new OnClickLimitListener() {
            @Override
            public void onClickLimit(View v) {
                if (FONT_STYLE_MODE.equalsIgnoreCase(Constants.MODE_FONT_STYLE_COLOR)) {
                    FONT_STYLE_MODE = Constants.MODE_FONT_STYLE_BACKGROUND;
                    ifv_font_style.setText(R.string.icon_font_background);
                } else {
                    FONT_STYLE_MODE = Constants.MODE_FONT_STYLE_COLOR;
                    ifv_font_style.setText(R.string.icon_font_color);
                }
                applyFontStyle(fontAdapter.getSelectColor());
            }
        });

        isInit = true;
    }

    /**
     * Load data *
     */
    public void loadData() {
        List<String> datas = ColorUtil.getPaintColors();
        fontAdapter = new ColorAdapter(datas, getContext());
        mRecyclerView.setAdapter(fontAdapter);

        if (mListener != null) {
            applyFontStyle(datas.get(2));
        }

        fontAdapter.setOnItemClickListener(new ColorAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(String color) {
                applyFontStyle(color);
            }
        });
    }

    public void addEvent(View confirmView, View cancelView) {
        if (confirmView != null) {
            confirmView.setOnClickListener(onClickLimitListener);
        }
        if (cancelView != null) {
            cancelView.setOnClickListener(onClickLimitListener);
        }
    }

    public void applyFontStyle(String color) {
        if (mListener != null) {
            mListener.applyStyle(getFontStyle(color));
        }
    }

    public StyleBean getFontStyle(String color) {
        StyleBean bean = new StyleBean();
        if (FONT_STYLE_MODE.equalsIgnoreCase(Constants.MODE_FONT_STYLE_COLOR)) {
            bean.eColor = ColorUtil.hexToRGBA(color);
            bean.fColor = ColorUtil.hexToRGBA(color);
        } else {
            if (color.equalsIgnoreCase("#FFFFFF")) {
                bean.fColor = ColorUtil.hexToRGBA("#1A1A1A");
                bean.eColor = ColorUtil.hexToRGBA("#1A1A1A");
                bean.sColor = ColorUtil.hexToRGBA("#1A1A1A");
            } else {
                bean.fColor = ColorUtil.hexToRGBA("#FFFFFF");
                bean.eColor = ColorUtil.hexToRGBA("#FFFFFF");
                bean.sColor = ColorUtil.hexToRGBA("#FFFFFF");
            }
            bean.bColor = ColorUtil.hexToRGBA(color);
        }
        return bean;
    }

    /**
     * Watcher
     */
    private final TextWatcher watcher = new TextWatcher() {
        /**
         * 记录文字
         */
        private String last;

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (!isSyncTextForOutside) {
                String news = mEditInput.getText().toString();
                if (mListener != null && mEditInput != null && !news.equals(last)) {
                    mListener.applyText(news);
                }
                last = news;
            }
            isSyncTextForOutside = false;

        }
    };

    /**
     * 设置回调监听
     *
     * @param mListener m listener
     */
    public void setListener(Listener mListener) {
        this.mListener = mListener;
    }

    public Listener getListener() {
        return mListener;
    }

    /**
     * Is sync text for outside
     */
    boolean isSyncTextForOutside = false;

    /**
     * Update tab out side *
     *
     * @param s            s
     * @param reBean       re bean
     * @param sBean        s bean
     * @param showKeyboard show keyboard
     * @param syncText     sync text
     */
    public void updateTabOutSide(CharSequence s, ReBean reBean, StyleBean sBean, boolean showKeyboard, boolean syncText) {
        oldContent = s;
        if (mEditInput != null && syncText) {
            isSyncTextForOutside = true;
            mEditInput.setText(s);
            mEditInput.setSelection(TextUtils.isEmpty(s) ? 0 : s.length());
        }
        updateStyle(sBean);
        if (showKeyboard) {
            updateTab(true);
        }
    }

    private void updateTab(final boolean isKeyboard) {
        if (isKeyboard) {
            show(false);
        } else {
            hideKeyboard();
        }
    }

    public void updateStyle(StyleBean sBean) {
        if (TextUtils.isEmpty(sBean.bColor)) {
            updateFontStyleMode(Constants.MODE_FONT_STYLE_COLOR);
            fontAdapter.setSelectColor(ColorUtil.rgbaToHex(sBean.fColor));
        } else {
            updateFontStyleMode(Constants.MODE_FONT_STYLE_BACKGROUND);
            fontAdapter.setSelectColor(ColorUtil.rgbaToHex(sBean.bColor));
        }
    }

    private void updateFontStyleMode(String mode) {
        if (mode.equalsIgnoreCase(Constants.MODE_FONT_STYLE_BACKGROUND)) {
            FONT_STYLE_MODE = Constants.MODE_FONT_STYLE_BACKGROUND;
            ifv_font_style.setText(R.string.icon_font_background);
        } else {
            FONT_STYLE_MODE = Constants.MODE_FONT_STYLE_COLOR;
            ifv_font_style.setText(R.string.icon_font_color);
        }
    }

    /*** 点击回调 */
    private final OnClickLimitListener onClickLimitListener = new OnClickLimitListener() {
        @Override
        public void onClickLimit(View v) {
            if (v.getId() == R.id.iv_delete) {
                if (mEditInput != null) {
                    mEditInput.setText("");
                }
            } else if (v.getId() == R.id.btn_option_confirm) {
                mEditInput.removeTextChangedListener(watcher);
                post(new Runnable() {
                    @Override
                    public void run() {
                        hide();
                        mEditInput.addTextChangedListener(watcher);
                    }
                });
            } else if (v.getId() == R.id.btn_option_cancel) {
                cancel();
            }
        }
    };


    /*** 显示  @param isClearSelect is clear select @param isClearSelect is clear select*/
    public void show(boolean isClearSelect) {
        if (isClearSelect) {
            mEditInput.setText("");
        }
        if (oldContent == null || oldContent.length() == 0) {
            mEditInput.setText("");
        }
        setVisibility(VISIBLE);
        mEditInput.post(new Runnable() {
            @Override
            public void run() {
                mEditInput.requestFocus();
                showKeyboard();
            }
        });
        if (mListener != null) {
            mListener.show();
        }
    }

    /*** 隐藏 */
    public void hide() {
        mEditInput.clearFocus();
        setVisibility(GONE);
        hideKeyboard();
        if (mListener != null) {
            mListener.hide();
            isSyncTextForOutside = false;
        }
        oldContent = null;
    }

    public void cancel() {
        if (oldContent != null && oldContent.length() > 0) {
            if (mEditInput != null) {
                mEditInput.setText(oldContent.toString());
            }
        } else {
            if (mEditInput != null) {
                mEditInput.setText("");
            }
            if (mListener != null) {
                mListener.removed();
            }
        }
        hide();
    }

    /*** 显示键盘 */
    private void showKeyboard() {
        Object inputObject = getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputObject instanceof InputMethodManager) {
            InputMethodManager input = (InputMethodManager) inputObject;
            if (input != null) {
                input.showSoftInput(mEditInput, 0);
            }
        }
    }

    /*** 隐藏键盘 */
    private void hideKeyboard() {
        Object inputObject = getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputObject instanceof InputMethodManager) {
            InputMethodManager input = (InputMethodManager) inputObject;
            if (input != null) {
                input.hideSoftInputFromWindow(mEditInput.getWindowToken(), 0);
            }
        }
    }

    public static StyleBean getDefaultStyleBean() {
        StyleBean styleBean = new StyleBean();
        styleBean.eColor = ColorUtil.hexToRGBA("#FF3D4D");
        styleBean.fColor = ColorUtil.hexToRGBA("#FF3D4D");
        return styleBean;
    }

    /*** 操作回调 */
    public interface Listener {
        /***  更新文本  @param s s @param s s*/
        void applyText(String s);

        /*** 新增编辑框 */
        void addNewText();

        /*** 应用字体  @param bean bean @param bean bean*/
        void applyFonts(ReBean bean);

        /*** 应用样式  @param bean bean @param bean bean*/
        void applyStyle(StyleBean bean);

        /**
         * Show
         */
        void show();

        /**
         * Hide
         */
        void hide();

        StyleBean getCurrentStyle();

        void removed();
    }
}
