package com.jd.oa.lib.editor.photo;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.jd.oa.BaseActivity;
import com.jd.oa.lib.editor.base.OnClickLimitListener;
import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.photo.clip.CutImageType;
import com.jd.oa.lib.editor.photo.clip.CutImageView;
import com.jd.oa.lib.editor.pub.Constants;
import com.jd.oa.lib.editor.util.BitmapUtils;
import com.jd.oa.lib.editor.util.FileUtils;
import com.jd.oa.lib.editor.util.ThreadPoolUtil;
import com.jd.oa.lib.editor.util.ToastUtil;
import com.jd.oa.utils.DisplayUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * des:
 *
 * <AUTHOR>
 * @date 2021 /12/20 : 5:52 下午
 * erp        zhoumin117
 * mail       <EMAIL>
 */
public class JdmmPhotoClipActivity extends BaseActivity {

    /**
     * M local media
     */
    private LocalMedia mLocalMedia;
    /**
     * param
     */
    private CutPhotoParam mParam;

    /**
     * Cut types
     */
    private List<CutImageType> cutImageTypes;

    /**
     * default cut type
     */
    private CutImageType mDefaultCutImageType = null;

    /**
     * On create *
     *
     * @param savedInstanceState saved instance state
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        if (intent == null || !intent.hasExtra(Constants.KEY_PARAM)) {
            finish();
            return;
        }
        mParam = intent.getParcelableExtra(Constants.KEY_PARAM);

        ArrayList<LocalMedia> mList = mParam.photoPath;
        if (mList == null || mList.size() <= 0) {//数据为空
            ToastUtil.showShortToast(this, "数据异常");
            finish();
            return;
        } else { //有数据不空，获取
            mLocalMedia = mList.get(0);
            if (mLocalMedia == null || !FileUtils.isFileExist(mLocalMedia)) { //path如果空
                ToastUtil.showShortToast(this, "数据异常");
                finish();
                return;
            }
        }
        cutImageTypes = mParam.cutImageTypes;
        if (cutImageTypes == null || cutImageTypes.size() <= 0) {
            cutImageTypes = new ArrayList<>();
            cutImageTypes.add(CutImageType.FREE);
            cutImageTypes.add(CutImageType.C4_3);
            cutImageTypes.add(CutImageType.C1_1);
            cutImageTypes.add(CutImageType.C3_4);
        }

        if (cutImageTypes.size() >= 1 && mDefaultCutImageType == null) {
            mDefaultCutImageType = cutImageTypes.get(0);
        }
//        setContentView(DataConfig.getInstance().getResourceId(R.layout.mm_photo_cuter));
        setContentView(R.layout.mm_photo_cuter);
//        if (DataConfig.getInstance().isDarkMode()) {
//            setStatusBarDarkMode();
//        } else {
//            if (UnStatusBarTintUtil.setStatusBarLightMode(this)) {
//                UnStatusBarTintUtil.setBackgroundColor(this, getResources().getColor(R.color.white));
//            } else {
//                UnStatusBarTintUtil.setBackgroundColor(this, getResources().getColor(R.color.gray_33));
//            }
//        }
        initView();
    }

    /**
     * Init view
     */
    private void initView() {
        TextView tvCancel = findViewById(R.id.tv_cancel);
        TextView tvSure = findViewById(R.id.tv_sure);
        TextView mBtnReset = findViewById(R.id.tv_reset);
        mBtnReset.setVisibility(mParam.showCutReset? View.VISIBLE : View.GONE);

//        LinearLayout mBtnRight90 = findViewById(R.id.mBtnRight90);
//        mBtnRight90.setVisibility(mParam.showCutRotate?View.VISIBLE: View.GONE);
//        LinearLayout.LayoutParams lpR = (LinearLayout.LayoutParams) mBtnRight90.getLayoutParams();
//        lpR.weight = cutImageTypes != null && cutImageTypes.size() > 1 ? 2.74f : 1f;

        View mViewSep = findViewById(R.id.view_sep);
        mViewSep.setVisibility(mParam.showCutRotate && cutImageTypes != null && cutImageTypes.size() > 0 ? View.VISIBLE : View.GONE);

        final CutImageView mCutImageView = findViewById(R.id.mCutImageView);
        mCutImageView.setCutTypeInit(mDefaultCutImageType);
        mCutImageView.setImageCutRectDrag(mParam.isImageCutRectDrag);
        mCutImageView.setMinImageSize(mParam.cutMinImageSize);
        mCutImageView.setCustomImageRate(mParam.cutCustomImageRate);
        mCutImageView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                loadBitmap(mCutImageView, mLocalMedia.getEditPath());
                mCutImageView.removeOnLayoutChangeListener(this);
            }
        });

        final LinearLayout llRate = findViewById(R.id.ll_rate);
        llRate.setVisibility(cutImageTypes.size() >= 1 ? View.VISIBLE : View.GONE);


        View viewTemp;
        View ivCutAuto = findViewById(R.id.iv_cut_auto);
        ivCutAuto.setVisibility(cutImageTypes.contains(CutImageType.FREE) ? View.VISIBLE : View.GONE);
        viewTemp = mDefaultCutImageType == CutImageType.FREE ? ivCutAuto : null;

        View ivCut43 = findViewById(R.id.iv_cut_43);
        ivCut43.setVisibility(cutImageTypes.contains(CutImageType.C4_3) ? View.VISIBLE : View.GONE);
        viewTemp = (viewTemp == null && mDefaultCutImageType == CutImageType.C4_3) ? ivCut43 : viewTemp;

        View ivCut11 = findViewById(R.id.iv_cut_11);
        ivCut11.setVisibility(cutImageTypes.contains(CutImageType.C1_1) ? View.VISIBLE : View.GONE);
        viewTemp = (viewTemp == null && mDefaultCutImageType == CutImageType.C1_1) ? ivCut11 : viewTemp;

        View ivCut34 = findViewById(R.id.iv_cut_34);
        ivCut34.setVisibility(cutImageTypes.contains(CutImageType.C3_4) ? View.VISIBLE : View.GONE);
        viewTemp = (viewTemp == null && mDefaultCutImageType == CutImageType.C3_4) ? ivCut34 : viewTemp;

        final View defaultRateView = viewTemp;

        applyFilterSelect(llRate, defaultRateView);

        View.OnClickListener onClickListener = new OnClickLimitListener() {
            @Override
            public void onClickLimit(View v) {
                if (mCutImageView != null) {
                    CutImageType targetCutType = null;
                    if (v.getId() == R.id.iv_cut_auto) {
                        targetCutType = CutImageType.FREE;
                    } else if (v.getId() == R.id.iv_cut_43) {
                        targetCutType = CutImageType.C4_3;
                    } else if (v.getId() == R.id.iv_cut_11) {
                        targetCutType = CutImageType.C1_1;
                    } else if (v.getId() == R.id.iv_cut_34) {
                        targetCutType = CutImageType.C3_4;
                    }
                    if (mCutImageView.setCutType(targetCutType) == 1) {
                        mDefaultCutImageType = targetCutType;
                        applyFilterSelect(llRate, v);
                    } else if (mCutImageView.setCutType(targetCutType) == 2) {
                        if (mParam.cutMinImageSize != null) {
                            String s = "像素";
                            if (mParam.cutMinImageSize.width > 0 && mParam.cutMinImageSize.height > 0) {
                                s = mParam.cutMinImageSize.width + "*" + mParam.cutMinImageSize.height;
                            }
                            ToastUtil.showShortToast(JdmmPhotoClipActivity.this, "aaaa");
                        }
                    }
                }
            }
        };

        ivCutAuto.setOnClickListener(onClickListener);
        ivCut43.setOnClickListener(onClickListener);
        ivCut11.setOnClickListener(onClickListener);
        ivCut34.setOnClickListener(onClickListener);

        final OnClickLimitListener mOnClickLimitListener = new OnClickLimitListener() {
            @Override
            public void onClickLimit(View v) {
                if (v.getId() == R.id.tv_cancel) {
                    finishEdit(false);
                } else if (v.getId() == R.id.tv_sure) {
                    if (mCutImageView != null && mCutImageView.isCanSave()) {
                        Bitmap bitmap = mCutImageView.getClippedImage();
                        saveClip(bitmap, mCutImageView.isEdited());
                    }
                } else if (v.getId() == R.id.tv_reset) {
                    applyFilterSelect(llRate, defaultRateView);
                    mCutImageView.reset();
                }
//                else if (v.getId() == R.id.mBtnRight90) {
//                    if (mCutImageView.rotate(90) == 2) {
//                        if (mParam.cutMinImageSize != null) {
//                            String s = "像素";
//                            if (mParam.cutMinImageSize.width > 0 && mParam.cutMinImageSize.height > 0) {
//                                s = mParam.cutMinImageSize.width + "*" + mParam.cutMinImageSize.height;
//                            }
//                            ToastUtil.showShortToast(JdmmPhotoClipActivity.this, "aaaa");
//                        }
//                    }
//                }
            }
        };

        tvCancel.setOnClickListener(mOnClickLimitListener);
        tvSure.setOnClickListener(mOnClickLimitListener);
        mBtnReset.setOnClickListener(mOnClickLimitListener);
//        mBtnRight90.setOnClickListener(mOnClickLimitListener);
    }

    /**
     * Save clip *
     *
     * @param bitmap   bitmap
     * @param isEdited is edited
     */
    private void saveClip(final Bitmap bitmap, boolean isEdited) {
        if (mLocalMedia != null) {
            mLocalMedia.mCutImageType = mDefaultCutImageType;
            mLocalMedia.addExtra(LocalMedia.MM_IMAGE_IS_CROP, isEdited ? "1" : "0");
            ThreadPoolUtil.createCallerRunsPool(1, 1).execute(new Runnable() {
                @Override
                public void run() {
                    if (bitmap != null && !bitmap.isRecycled()) {
                        String tempPath = FileUtils.saveBitmap(bitmap, null, mParam.bitmapFormat);
                        if (FileUtils.isFileExist(tempPath)) {
                            if (!TextUtils.isEmpty(mLocalMedia.getTempPath())) {
                                FileUtils.deleteFile(mLocalMedia.getTempPath());
                            }
                            mLocalMedia.setTempPath(tempPath);
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    finishEdit(true);
                                }
                            });
                        } else {
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtil.showShortToast(JdmmPhotoClipActivity.this, "裁剪图片失败，请重试");
                                }
                            });
                        }
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                ToastUtil.showShortToast(JdmmPhotoClipActivity.this, "裁剪图片失败，请重试");
                            }
                        });
                    }
                }
            });
        }
    }

    /**
     * Load bitmap *
     *
     * @param path path
     */
    private void loadBitmap(final CutImageView mCutImageView, final String path) {
        ThreadPoolUtil.createDiscardPool(1, 1).execute(new Runnable() {
            @Override
            public void run() {
                if (isFinishing()) {
                    return;
                }
                int screenWidth = DisplayUtils.getScreenWidth();
                int screenHeight = DisplayUtils.getScreenHeight();
                final Bitmap bitmap = BitmapUtils.getFitSampleBitmap(JdmmPhotoClipActivity.this, path, screenWidth * 2, screenHeight * 2);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!isFinishing() && mCutImageView != null) {
                            mCutImageView.setBitmap(bitmap,false);
                        }
                    }
                });

            }
        });
    }


    /**
     * Apply filter select *
     *
     * @param llRate ll rate
     * @param v      v
     */
    private void applyFilterSelect(ViewGroup llRate, View v) {
        for (int i = 0; i < llRate.getChildCount(); i++) {
            View item = llRate.getChildAt(i);
            if (item.getVisibility() == View.VISIBLE) {
                item.setSelected(v == item);
            }
        }
    }


    /**
     * 完成编辑逻辑
     *
     * @param save save
     */
    public void finishEdit(final boolean save) {
        ArrayList<LocalMedia> result = new ArrayList<>();
        if (save) {
            if (mLocalMedia != null && FileUtils.isFileExist(mLocalMedia.getTempPath())) {
                if (mParam.isSavePhotoToAlbum) {
//                    FileUtils.insertToAlbum(this, mLocalMedia.getTempPath(), false);
                }
                mLocalMedia.setPath(mLocalMedia.getTempPath());
                mLocalMedia.setTempPath(null);
                result.add(mLocalMedia);
            }
        } else { //返回按钮，直接清除数据中的临时数据
            if (!TextUtils.isEmpty(mLocalMedia.getTempPath())) {
                FileUtils.deleteDir(new File(mLocalMedia.getTempPath()));
                mLocalMedia.setTempPath(null);
            }
        }

        Intent intent = new Intent();
        intent.putParcelableArrayListExtra(Constants.KEY_PARAM, result);
        setResult(save ? RESULT_OK : RESULT_FIRST_USER, intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        finishEdit(false);
    }

    /**
     * 根据参数 启动当前Activity
     *
     * @param activity    activity
     * @param param       CutPhotoParam
     * @param requestCode request code
     */
    public static void startActivityForResult(@NonNull Activity activity, CutPhotoParam param, int requestCode) {
        if (param == null || !param.isValid()) {
            return;
        }
        Intent intent = new Intent(activity, JdmmPhotoClipActivity.class);
        intent.putExtra(Constants.KEY_PARAM, param);
        activity.startActivityForResult(intent, requestCode);
    }
}
