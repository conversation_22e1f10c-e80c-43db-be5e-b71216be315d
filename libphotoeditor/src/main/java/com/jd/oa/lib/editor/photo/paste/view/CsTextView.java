package com.jd.oa.lib.editor.photo.paste.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.jd.oa.lib.editor.photo.paste.fonts.FontToolPresenter;
import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.utils.DensityUtil;

import java.io.File;

import android.graphics.drawable.GradientDrawable;

/**
 * des: 描边 阴影 TextView
 *
 * <AUTHOR>
 * @date 2021 /9/13 : 6:09 下午 erp        zhou min117 mail       <EMAIL>
 */
public class CsTextView extends AppCompatTextView {

    /**
     * E width
     */
    double eWidth = 0;
    /**
     * E color
     */
    int eColor = 0x00000000;

    /**
     * C text view
     *
     * @param context context
     */
    public CsTextView(Context context) {
        super(context);
        init();
    }

    /**
     * C text view
     *
     * @param context context
     * @param attrs   attrs
     */
    public CsTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    /**
     * C text view
     *
     * @param context      context
     * @param attrs        attrs
     * @param defStyleAttr def style attr
     */
    public CsTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    /**
     * Init
     */
    private void init() {
        Paint paint = getPaint();
        if (paint != null) {
            paint.setAntiAlias(true);
            paint.setFilterBitmap(true);
        }
        setLayerType(LAYER_TYPE_HARDWARE, paint);
    }

    /**
     * Sets typeface with path *
     *
     * @param fontPath font path
     * @return the typeface with path
     */
    public void setTypefaceWithPath(String fontPath) {
        try {
            Typeface tf;
            if (TextUtils.isEmpty(fontPath) || !new File(fontPath).exists()) {
                tf = null;
            } else {
                tf = Typeface.createFromFile(fontPath);
            }
            super.setTypeface(tf);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Sets style *
     *
     * @param bean bean
     * @return the style
     */
    public boolean setStyle(StyleBean bean) {
        if (bean == null) {
            setBackgroundColor(Color.TRANSPARENT);
            setTextColor(Color.WHITE);
//            setShadowLayer(3, 0, 0, Color.BLACK);
            setEdge(0, Color.TRANSPARENT);
        } else {
            setTextColor(FontToolPresenter.stringToColor(bean.fColor, Color.WHITE));
            GradientDrawable gd = new GradientDrawable();
            gd.setColor(FontToolPresenter.stringToColor(bean.bColor, Color.TRANSPARENT)); // 设置背景颜色
            gd.setCornerRadius(DensityUtil.dp2px(getContext(), 15.0f));
            setBackground(gd);
//            setBackgroundColor(FontToolPresenter.stringToColor(bean.bColor, Color.TRANSPARENT));
//            setShadowLayer((float) bean.sRadius, bean.sOffset.x, bean.sOffset.y, FontToolPresenter.stringToColor(bean.sColor, Color.BLACK));
//            setEdge(bean.eWidth, FontToolPresenter.stringToColor(bean.eColor, Color.TRANSPARENT));
        }
        return true;
    }


    /**
     * Sets edge *
     *
     * @param width width
     * @param ec    ec
     */
    private void setEdge(double width, int ec) {
        eWidth = width;
        eColor = ec;
    }

    /**
     * On draw *
     *
     * @param canvas canvas
     */
    @Override
    public void onDraw(Canvas canvas) {
        if (eWidth > 0) {
            TextPaint paint = getPaint();
            paint.setStrokeWidth((int) eWidth);
            paint.setStyle(Paint.Style.STROKE);
            setTextColor(eColor);
        }
        super.onDraw(canvas);
    }

}
