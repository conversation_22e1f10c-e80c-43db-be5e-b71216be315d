package com.jd.oa.lib.editor.photo.paint;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.view.View;

import com.jd.oa.lib.editor.photo.R;
import com.jd.oa.utils.UnitUtils;

public class ColorItem extends View {
    private int colorRingSize, colorRingSelectSize;
    private int circleSize, circleSelectSize;
    private final Paint ringPaint;
    private final Paint circlePaint;

    public ColorItem(Context context) {
        super(context);
        ringPaint = new Paint();
        ringPaint.setColor(Color.WHITE);
        ringPaint.setStyle(Paint.Style.STROKE);
        ringPaint.setAntiAlias(true);

        circlePaint = new Paint(ringPaint);
        circlePaint.setStyle(Paint.Style.FILL);

        colorRingSize = UnitUtils.dip2px(context, 2);
        colorRingSelectSize = UnitUtils.dip2px(context, 4);

        circleSize = UnitUtils.dip2px(context, 16);
        circleSelectSize = UnitUtils.dip2px(context, 20);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int width = getWidth() - getPaddingLeft() - getPaddingRight();
        int height = getHeight() - getPaddingTop() - getPaddingBottom();
        if (isSelected()) {
            width = (int) (width * 1.2);
            height = (int) (height * 1.2);
        }
        int radius = Math.min(width, height) / 2;

        int ringSize;
        int innerSpace = 0;
        if (isSelected()) {
            ringSize = colorRingSelectSize;
            innerSpace = -1;
        } else {
            ringSize = colorRingSize;
            innerSpace = -1;
        }
        ringPaint.setStrokeWidth(ringSize);
        if (isSelected()) {
            canvas.drawCircle(getWidth() / 2, getHeight() / 2, circleSelectSize / 2, circlePaint);
            canvas.drawCircle(getWidth() / 2, getHeight() / 2, circleSelectSize / 2, ringPaint);
        } else {
            canvas.drawCircle(getWidth() / 2, getHeight() / 2, circleSize / 2, circlePaint);
            canvas.drawCircle(getWidth() / 2, getHeight() / 2, circleSize / 2, ringPaint);
        }

    }

    public void setColor(String color) {
        circlePaint.setColor(Color.parseColor(color));
    }
}
