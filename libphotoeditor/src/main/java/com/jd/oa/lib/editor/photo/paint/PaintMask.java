package com.jd.oa.lib.editor.photo.paint;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.UnitUtils;

import java.util.Iterator;
import java.util.Stack;

public class PaintMask extends PaintStruct {

    private final Paint paint = new Paint();
    private final int defaultSize;
    private int color;

    private final String TAG = "PaintMask";

    public PaintMask() {
        defaultSize = UnitUtils.dip2px(AppBase.getAppContext(), 4);
        paint.setStrokeWidth(defaultSize);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeJoin(Paint.Join.ROUND);
    }

    public void setPaintColor(String color) {
        try {
            if (TextUtils.isEmpty(color)) {
                this.color = Color.parseColor("#ffffff");
                return;
            }
            this.color = Color.parseColor(color);
        } catch (Exception e) {
        }
    }

    @Override
    protected PaintStruct.TouchStack createTouchInfo() {
        return new PaintStruct.TouchStack(new Path(), color, defaultSize);
    }

    @Override
    public void doDraw(Canvas canvas) {
        drawEach(canvas, super.cacheTouchStacks);
        drawEach(canvas, super.touchStacks);
        MELogUtil.localD(TAG, "do draw");
    }

    @Override
    public void addStack() {
        if (mListener != null) {
            mListener.addStackCallback();
        }
    }

    @Override
    public void popStack() {
        if (mListener != null) {
            mListener.popStackCallback();
        }
    }

    private void drawEach(Canvas canvas, Stack<TouchStack> touchStacks) {
        if (touchStacks == null) {
            return;
        }
        Iterator<TouchStack> iterator = touchStacks.iterator();
        while (iterator.hasNext()) {
            PaintStruct.TouchStack element = iterator.next();
            paint.setStrokeWidth(element.size);
            paint.setColor(element.color);
            canvas.drawPath(element.path, paint);
        }
    }

    private IPaintListener mListener;

    /**
     * 添加 IPaintListener
     *
     * @param listener
     */
    public void setListener(IPaintListener listener) {
        mListener = listener;
    }
}
