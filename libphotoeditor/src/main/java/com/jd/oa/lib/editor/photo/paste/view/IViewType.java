package com.jd.oa.lib.editor.photo.paste.view;

import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Point;
import android.view.View;

import com.jd.oa.lib.editor.photo.paste.PasteBean;

/**
 * 贴纸与文字 接口
 *
 * <AUTHOR> zhoumin
 * @date : 2018/8/11 Time: 下午2:00 Email: <EMAIL> Description:
 */
public interface IViewType {

    /***  控制缩放，旋转图标所在四个点得位置 */
    int LEFT_TOP = 0;
    /***  RIGHT_TOP */
    int RIGHT_TOP = 1;
    /***  RIGHT_BOTTOM */
    int RIGHT_BOTTOM = 2;
    /***  LEFT_BOTTOM */
    int LEFT_BOTTOM = 3;


    /***  一些默认的常量 */
    int DEFAULT_FRAME_PADDING = 8;
    /***  DEFAULT_FRAME_WIDTH */
    int DEFAULT_FRAME_WIDTH = 2;
    /***  DEFAULT_FRAME_COLOR */
    int DEFAULT_FRAME_COLOR = Color.WHITE;
    /***  DEFAULT_SCALE */
    float DEFAULT_SCALE = 1.0f;
    /***  DEFAULT_DEGREE */
    float DEFAULT_DEGREE = 0;
    /***  DEFAULT_EDITABLE */
    boolean DEFAULT_EDITABLE = true;
    /***  DEFAULT_OTHER_DRAWABLE_WIDTH */
    int DEFAULT_OTHER_DRAWABLE_SIZE = 50;


    /*** 初始状态 */
    int STATUS_INIT = 0;

    /*** 拖动状态 */
    int STATUS_DRAG = 1;

    /*** 旋转或者放大状态  外部按钮控制 */
    int STATUS_ROTATE_ZOOM_OUTSIDE = 2;
    /*** 内部手势操作 */
    int STATUS_ROTATE_ZOOM_INSIDE = 5;

    /*** STATUS_CLOSE */
    int STATUS_CLOSE = 4;

    /*** 文字移动距离限制 */
    int LIMIT_DISTANCE = 100;
    /*** 画外围框的画笔*/
    Paint mPaint = new Paint();

    /**
     * Sets editable *
     *
     * @param is is
     */
    void setEditable(boolean is);

    /**
     * Sets control visible *
     *
     * @param is is
     */
    void setControlVisible(boolean is);

    /**
     * Is control visible boolean
     *
     * @return the boolean
     */
    boolean isControlVisible();

    /**
     * Is editable boolean
     *
     * @return the boolean
     */
    boolean isEditable();

    /**
     * Gets view *
     *
     * @return the view
     */
    View getView();

    /**
     * Gets id *
     *
     * @return the id
     */
    String getID();

    /**
     * Is view enble boolean
     *
     * @return the boolean
     */
    boolean isViewEnable();

    /**
     * Sets lisenter *
     *
     * @param r r
     */
    void setListener(PasteListener r);

    /**
     * Is point in rect boolean
     *
     * @param point point
     * @param isOut is out
     * @return the boolean
     */
    boolean isPointInRect(Point point, boolean isOut);

    /**
     * 初始化控件放置中点位置
     *
     * @param pWidth  父控件宽
     * @param pHeight 父控件高
     */
    void setCenterPoint(int pWidth, int pHeight);


    /**
     * 获取记录信息
     *
     * @return paste bean
     */
    PasteBean getPasteBean();


    /**
     * 更新中心点
     *
     * @param pWidth  p width
     * @param pHeight p height
     */
    void updateCenterPoint(int pWidth, int pHeight);

    /**
     * Release
     */
    void release();
}
