package com.jd.oa.lib.editor.photo.paste.fonts;

import android.graphics.PointF;
import android.text.TextUtils;

import androidx.annotation.IntRange;

import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.lib.editor.pub.data.ReBean;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;

/**
 * des: 文字工具条 FontToolPresenter
 *
 * <AUTHOR>
 * @date 2021 /9/9 : 4:43 下午
 * erp        zhoumin117
 * mail       <EMAIL>
 */
public class FontToolPresenter {


    /**
     * Font tool view
     */
    public interface IFontToolView {

        /**
         * On font style call back *
         *
         * @param fontList  font list
         * @param styleList style list
         */
        void onFontStyleCallBack(ArrayList<ReBean> fontList, ArrayList<StyleBean> styleList);

        /**
         * On font failed *
         *
         * @param error error
         */
        void onFontFailed(String error);

    }

    /**
     * Font list
     */
    ArrayList<ReBean> fontList;
    /**
     * Style list
     */
    ArrayList<StyleBean> styleList;

    /**
     * View
     */
    private final IFontToolView view;

    /**
     * Font tool presenter
     *
     * @param view view
     */
    public FontToolPresenter(IFontToolView view) {
        this.view = view;
    }


    /**
     * Gets font and style list *
     *
     * @param source source
     */
    public void getFontAndStyleList(String source) {
//        MakerWebServerManger.getInstance().getFontAndStyleList(source,new OnServerListener() {
//            @Override
//            public void onCompleted(String result) {
//                try {
//                    if (!TextUtils.isEmpty(result)) {
//                        JSONObject rootJsonObj = new JSONObject(result);
//                        int code = rootJsonObj.optInt("code", -1);
//                        if (code == 0) {
//                            fontList = parseFontList(rootJsonObj);
//                            styleList = parseStyleList(rootJsonObj);
//                            view.onFontStyleCallBack(fontList, styleList);
//                        } else {
//                            onFailed("code " + code);
//                        }
//                    } else {
//                        onFailed("result null");
//                    }
//                } catch (Throwable e) {
//                    e.printStackTrace();
//                    onFailed(e.toString());
//                }
//            }
//
//            @Override
//            public void onFailed(String error) {
//                view.onFontFailed(error);
//            }
//        });
    }

    /**
     * 解析道具分组数据
     *
     * @param rootJsonObj root json obj
     * @return the array list
     */
    public ArrayList<ReBean> parseFontList(JSONObject rootJsonObj) {
        ArrayList<ReBean> fontList = new ArrayList<>();
        JSONArray rv = rootJsonObj.optJSONArray("fontList");
        if (rv != null) {
            int length = rv.length();
            for (int i = 0; i < length; i++) {
                ReBean item = new ReBean(ReBean.TYPE.FONT);
                JSONObject propJsonObj = rv.optJSONObject(i);
                if (propJsonObj != null) {
                    String id = propJsonObj.optString("fid");
                    String pic = propJsonObj.optString("fontImage");
                    String file = propJsonObj.optString("fontUrl");
                    String md5 = propJsonObj.optString("fontUrlMd5");
                    item.id = id;
                    item.picUrl = pic;
                    item.fileUrl = file;
                    item.fileMd5 = md5;
                }
                fontList.add(item);
            }
        }
        return fontList;
    }

    /**
     * 解析道具分组数据
     *
     * @param rootJsonObj root json obj
     * @return the array list
     */
    public ArrayList<StyleBean> parseStyleList(JSONObject rootJsonObj) {
        ArrayList<StyleBean> styleList = new ArrayList<>();
        JSONArray rv = rootJsonObj.optJSONArray("styleList");
        if (rv != null) {
            int length = rv.length();
            for (int i = 0; i < length; i++) {
                StyleBean item = new StyleBean();
                JSONObject propJsonObj = rv.optJSONObject(i);
                if (propJsonObj != null) {
                    try {
                        item.id = propJsonObj.optString("sid");
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    try {
                        item.bColor = propJsonObj.optString("backgroundColor");
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    try {
                        item.pic = propJsonObj.optString("styleImage");
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    try {
                        item.fColor = propJsonObj.optString("fontColor");
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    try {
                        item.sColor = propJsonObj.optString("shadowColor");
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    try {
                        item.sRadius = propJsonObj.optDouble("shadowWide", 0);
                    } catch (Throwable e) {
                        e.printStackTrace();
                        item.sRadius = 0;
                    }
                    try {
                        item.sOffset = stringToPointF(propJsonObj.optString("shadowPosition"));
                    } catch (Throwable e) {
                        item.sOffset = new PointF(0f, 0f);
                    }
                    try {
                        item.eColor = propJsonObj.optString("outlineColor");
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    try {
                        item.eWidth = propJsonObj.optDouble("outlineWide", 0);
                    } catch (Throwable e) {
                        e.printStackTrace();
                        item.eWidth = 0;
                    }
                }
                styleList.add(item);
            }
        }
        return styleList;
    }


    /**
     * Argb int
     *
     * @param alpha alpha
     * @param red   red
     * @param green green
     * @param blue  blue
     * @return the int
     */
    private static int argb(
            @IntRange(from = 0, to = 255) int alpha,
            @IntRange(from = 0, to = 255) int red,
            @IntRange(from = 0, to = 255) int green,
            @IntRange(from = 0, to = 255) int blue) {
        return (alpha << 24) | (red << 16) | (green << 8) | blue;
    }

    /**
     * String to color int
     *
     * @param s            s
     * @param defaultColor default color
     * @return the int
     */
    public static int stringToColor(String s, int defaultColor) {
        if (TextUtils.isEmpty(s)) {
            return defaultColor;
        }
        try {
            String[] c = s.split(",");
            int a = (int) (Float.parseFloat(c[3]) * 255f);
            int r = Integer.parseInt(c[0]);
            int g = Integer.parseInt(c[1]);
            int b = Integer.parseInt(c[2]);
            return argb(a, r, g, b);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return defaultColor;
    }

    /**
     * String to point f point f
     *
     * @param s s
     * @return the point f
     */
    public static PointF stringToPointF(String s) {
        try {
            String[] c = s.split(",");
            float x = Float.parseFloat(c[0]);
            float y = Float.parseFloat(c[1]);
            return new PointF(x, y);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return new PointF(0, 0);
    }


}
