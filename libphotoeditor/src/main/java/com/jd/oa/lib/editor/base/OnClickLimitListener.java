package com.jd.oa.lib.editor.base;

import android.view.View;

/**
 * On click limit listener
 * 防止狂点击事件
 * <AUTHOR> zhoumin Email: <EMAIL>
 * @date: 2018 /7/12 Time: 下午5:06
 */
public abstract class OnClickLimitListener implements View.OnClickListener {

    /**
     * Time
     */
    int time = 500;
    /**
     * Last click time
     */
    long lastClickTime = 0;

    /**
     * On click limit listener
     */
    public OnClickLimitListener() {
    }

    /**
     * On click limit listener
     *
     * @param time time
     */
    public OnClickLimitListener(int time) {
        this.time = time;
    }

    /**
     * On click *
     *
     * @param v v
     */
    @Override
    public void onClick(View v) {
        if (Math.abs(System.currentTimeMillis() - lastClickTime) > time) {
            lastClickTime = System.currentTimeMillis();
            onClickLimit(v);
//            Log.i("OnClickLimitListener", "yes");
        }
//        Log.i("OnClickLimitListener", "no");
    }

    /**
     * On click limit *
     *
     * @param v v
     */
    public abstract void onClickLimit(View v);
}
