package com.jd.oa.lib.editor.photo.edit;

import android.app.Activity;
import android.app.Dialog;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;

import com.jd.oa.lib.editor.base.OnClickLimitListener;
import com.jd.oa.lib.editor.photo.R;
import com.jd.oa.lib.editor.photo.clip.CutImageType;
import com.jd.oa.lib.editor.photo.clip.CutImageView;
import com.jd.oa.lib.editor.pub.Size;
import com.jd.oa.lib.editor.util.BitmapUtils;
import com.jd.oa.lib.editor.util.ThreadPoolUtil;
import com.jd.oa.lib.editor.util.ToastUtil;
import com.jd.oa.ui.IconFontView;

import java.util.ArrayList;
import java.util.List;

/**
 * Cut image dialog fragment
 *
 * <AUTHOR> zhoumin Email: <EMAIL>
 * @date: 2020 /8/14 Time: 下午3:30 Description: A simple {@link Fragment} subclass. Use the {@link CutImageDialogFragment#newInstance} factory method to create an instance of this fragment.
 */
public class CutImageDialogFragment extends DialogFragment {


    /**
     * 传参标识符
     */
    private static final String KEY_BACKGROUND_COLOR = "KEY_BACKGROUND_COLOR";


    /**
     * 创建实例
     * 优先使用 bitmap ,path 兜底
     *
     * @param bitmap    bitmap  需要裁剪的图片
     * @param path      path    需要裁剪的图片路径 ，如果bitmap 为空，则使用 图片路径读取。path 备用
     * @param mListener 回调
     * @return Instance Of  CutImageDialogFragment
     */
    public static CutImageDialogFragment newInstance(Bitmap bitmap, String path, Listener mListener) {
//        int mainBackgroundValue = DataConfig.getInstance().getMainBackgroundValue();
        int mainBackgroundValue = 0x000000;
        return newInstance(mainBackgroundValue, bitmap, path, mListener);
    }

    /**
     * 创建实例
     * 优先使用 bitmap ,path 兜底
     *
     * @param backgroundColor background color
     * @param bitmap          bitmap  需要裁剪的图片
     * @param path            patha  需要裁剪的图片路径 ，如果bitmap 为空，则使用 图片路径读取。相当于一级优化
     * @param mListener       回调
     * @return Instance Of  CutImageDialogFragment
     */
    public static CutImageDialogFragment newInstance(int backgroundColor, Bitmap bitmap, String path, Listener mListener) {
        CutImageDialogFragment dialog = new CutImageDialogFragment();
        dialog.setBitmap(bitmap);
        dialog.setPath(path);
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_BACKGROUND_COLOR, backgroundColor);
        dialog.setArguments(bundle);
        dialog.setEditListener(mListener);
        return dialog;
    }


    /**
     * M bitmap
     */
    private Bitmap mBitmap;

    /**
     * M bitmap
     */
    private String mPath;
    /**
     * M background color
     */
    private int mBackgroundColor = 0xffffffff;
    /**
     * Is edited
     */
    private boolean isEdited = false;
    /**
     * M cut image view
     */
    private CutImageView mCutImageView;
    /**
     * M listener
     */
    private Listener mListener;

    /**
     * Cut types
     */
    private List<CutImageType> cutImageTypes;

    /**
     * M default cut type
     */
    private CutImageType mDefaultCutImageType = null;

    private boolean isImageCutRectDrag = true;
    /**
     * 显示 图片裁剪 旋转
     */
    private boolean showCutRotate = true;
    /**
     * 显示 图片裁剪 重置
     */
    private boolean showCutReset = true;

    /**
     * 最小图片像素， 宽高
     */
    private Size mMinImageSize;
    /**
     * 自定义比例
     */
    private Size mCustomImageRate;

    /**
     * 最小图片像素
     *
     * @param size m min size
     */
    public void setMinImageSize(Size size) {
        this.mMinImageSize = size;
    }

    /**
     * 自定义裁剪比例
     *
     * @param size m min size
     */
    public void setCustomImageRate(Size size) {
        this.mCustomImageRate = size;
    }

    /**
     * Sets bitmap *
     *
     * @param mBitmap m bitmap
     */
    public void setBitmap(Bitmap mBitmap) {
        this.mBitmap = mBitmap;
    }

    /**
     * Sets path *
     *
     * @param mPath m path
     */
    public void setPath(String mPath) {
        this.mPath = mPath;
    }

    /**
     * 设置选中回调
     *
     * @param m 操作回调
     */
    public void setEditListener(Listener m) {
        mListener = m;
    }

    /**
     * @param imageCutRectDrag
     */
    public void setImageCutRectDrag(boolean imageCutRectDrag) {
        isImageCutRectDrag = imageCutRectDrag;
    }

    public void setShowCutRotate(boolean showCutRotate) {
        this.showCutRotate = showCutRotate;
    }

    public void setShowCutReset(boolean showCutReset) {
        this.showCutReset = showCutReset;
    }

    /**
     * Sets cut types *
     *
     * @param cutImageTypes cut types
     */
    public void setCutTypes(List<CutImageType> cutImageTypes) {
        this.cutImageTypes = cutImageTypes;
    }

    /**
     * Sets default cut type *
     *
     * @param mDefaultCutImageType m default cut type
     */
    public void setDefaultCutType(CutImageType mDefaultCutImageType) {
        this.mDefaultCutImageType = mDefaultCutImageType;
    }

    /**
     * On create *
     *
     * @param savedInstanceState saved instance state
     */
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置dialog背景透明及无边框等
        Bundle bundle = getArguments();
        if (bundle != null) {
            mBackgroundColor = bundle.getInt(KEY_BACKGROUND_COLOR);
        }
        setStyle(DialogFragment.STYLE_NORMAL, R.style.mm_CustomDialogTheme);
    }


    /**
     * On create view view
     *
     * @param inflater           inflater
     * @param container          container
     * @param savedInstanceState saved instance state
     * @return the view
     */
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        setCancelable(false);
        Dialog d = getDialog();
        if (d != null) {
            d.setCanceledOnTouchOutside(false);
            d.setCancelable(false);
            Window window = d.getWindow();
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.gravity = Gravity.BOTTOM; //底部
            lp.windowAnimations = R.style.mm_dialog_alpha;
            window.setWindowAnimations(R.style.mm_dialog_alpha);
//        lp.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            window.setAttributes(lp);
        }

        return inflater.inflate(R.layout.mm_edit_fragment, container, false);
    }


    /**
     * On view created *
     *
     * @param view               view
     * @param savedInstanceState saved instance state
     */
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        if (cutImageTypes == null || cutImageTypes.size() <= 0) {
            cutImageTypes = new ArrayList<>();
            cutImageTypes.add(CutImageType.FREE);
            cutImageTypes.add(CutImageType.C4_3);
            cutImageTypes.add(CutImageType.C1_1);
            cutImageTypes.add(CutImageType.C3_4);
        }

        if (cutImageTypes.size() >= 1 && mDefaultCutImageType == null) {
            mDefaultCutImageType = cutImageTypes.get(0);
        }


        IconFontView tvCancel = view.findViewById(R.id.tv_cancel);
        IconFontView tvSure = view.findViewById(R.id.tv_sure);
        TextView mBtnReset = view.findViewById(R.id.tv_reset);
        mBtnReset.setVisibility(showCutReset ? View.VISIBLE : View.GONE);

        LinearLayout mBtnLeftt90 = view.findViewById(R.id.mBtnLeft90);
//        mBtnLeftt90.setVisibility(true ? View.VISIBLE : View.GONE);
//        mBtnLeftt90.setVisibility(showCutRotate ? View.VISIBLE : View.GONE);
//        LinearLayout.LayoutParams lpR = (LinearLayout.LayoutParams) mBtnLeftt90.getLayoutParams();
//        lpR.weight = cutImageTypes != null && cutImageTypes.size() > 1 ? 2.74f : 1f;


        View mViewSep = view.findViewById(R.id.view_sep);
        mViewSep.setVisibility(showCutRotate && cutImageTypes != null && cutImageTypes.size() > 0 ? View.VISIBLE : View.GONE);

        mCutImageView = view.findViewById(R.id.mCutImageView);
        mCutImageView.setCutTypeInit(mDefaultCutImageType);
        mCutImageView.setImageCutRectDrag(isImageCutRectDrag);
        mCutImageView.setMinImageSize(mMinImageSize);
        mCutImageView.setCustomImageRate(mCustomImageRate);
        mCutImageView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                if (mBitmap != null && !mBitmap.isRecycled()) {
                    loadBitmap(mBitmap,false);
                } else {
                    loadBitmap(mPath, false);
                }
                mCutImageView.removeOnLayoutChangeListener(this);
            }
        });
        mCutImageView.setEditedListener(new CutImageView.IEditedListener() {
            @Override
            public void isEdited() {
                isEdited = true;
                mBtnReset.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
            }
        });

        final LinearLayout llRate = view.findViewById(R.id.ll_rate);
        llRate.setVisibility(cutImageTypes.size() >= 1 ? View.VISIBLE : View.GONE);


        View viewTemp;
        View ivCutAuto = view.findViewById(R.id.iv_cut_auto);
        ivCutAuto.setVisibility(cutImageTypes.contains(CutImageType.FREE) ? View.VISIBLE : View.GONE);
        viewTemp = mDefaultCutImageType == CutImageType.FREE ? ivCutAuto : null;

        View ivCut43 = view.findViewById(R.id.iv_cut_43);
        ivCut43.setVisibility(cutImageTypes.contains(CutImageType.C4_3) ? View.VISIBLE : View.GONE);
        viewTemp = (viewTemp == null && mDefaultCutImageType == CutImageType.C4_3) ? ivCut43 : viewTemp;

        View ivCut11 = view.findViewById(R.id.iv_cut_11);
        ivCut11.setVisibility(cutImageTypes.contains(CutImageType.C1_1) ? View.VISIBLE : View.GONE);
        viewTemp = (viewTemp == null && mDefaultCutImageType == CutImageType.C1_1) ? ivCut11 : viewTemp;

        View ivCut34 = view.findViewById(R.id.iv_cut_34);
        ivCut34.setVisibility(cutImageTypes.contains(CutImageType.C3_4) ? View.VISIBLE : View.GONE);
        viewTemp = (viewTemp == null && mDefaultCutImageType == CutImageType.C3_4) ? ivCut34 : viewTemp;

        final View defaultRateView = viewTemp;

        applyFilterSelect(llRate, defaultRateView);

        View.OnClickListener onClickListener = new OnClickLimitListener() {
            @Override
            public void onClickLimit(View v) {
                if (mCutImageView != null) {
                    CutImageType targetCutType = null;
                    if (v.getId() == R.id.iv_cut_auto) {
                        targetCutType = CutImageType.FREE;
                    } else if (v.getId() == R.id.iv_cut_43) {
                        targetCutType = CutImageType.C4_3;
                    } else if (v.getId() == R.id.iv_cut_11) {
                        targetCutType = CutImageType.C1_1;
                    } else if (v.getId() == R.id.iv_cut_34) {
                        targetCutType = CutImageType.C3_4;
                    }
                    if (mCutImageView.setCutType(targetCutType) == 1) {
                        mDefaultCutImageType = targetCutType;
                        applyFilterSelect(llRate, v);
                    } else if (mCutImageView.setCutType(targetCutType) == 2) {
                        if (mMinImageSize != null) {
                            String s = "像素";
                            if (mMinImageSize.width > 0 && mMinImageSize.height > 0) {
                                s = mMinImageSize.width + "*" + mMinImageSize.height;
                            }

                            ToastUtil.showShortToast(getContext(), "aaaaaa");
                        }
                    }
                    isEdited = true;
                    mBtnReset.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
                }
            }
        };

        ivCutAuto.setOnClickListener(onClickListener);
        ivCut43.setOnClickListener(onClickListener);
        ivCut11.setOnClickListener(onClickListener);
        ivCut34.setOnClickListener(onClickListener);

        /**
         * 点击
         */
        final OnClickLimitListener mOnClickLimitListener = new OnClickLimitListener() {
            @Override
            public void onClickLimit(View v) {
                if (v.getId() == R.id.tv_cancel) {
                    hide();
                    mCutImageView.reset();
                    if (mListener != null) {
                        mListener.cancel();
                    }
                } else if (v.getId() == R.id.tv_sure) {
                    if (mListener != null && mCutImageView != null && mCutImageView.isCanSave()) {
                        hide();
                        Bitmap bitmap = mCutImageView.getClippedImage();
                        mListener.confirm(bitmap, mDefaultCutImageType, isEdited | mCutImageView.isEdited());
                    }
                } else if (v.getId() == R.id.tv_reset) {
                    if (!isEdited) {
                        return;
                    }
                    applyFilterSelect(llRate, defaultRateView);
                    mCutImageView.reset();
                    isEdited = false;
                    mBtnReset.setTextColor(getResources().getColor(R.color.mm_color_reset_disable));
                } else if (v.getId() == R.id.mBtnLeft90) {
                    if (mCutImageView.rotate(-90) == 1) {
                        isEdited = true;
                        mBtnReset.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
                    } else if (mCutImageView.rotate(-90) == 2) {
                        if (mMinImageSize != null) {
                            String s = "像素";
                            if (mMinImageSize.width > 0 && mMinImageSize.height > 0) {
                                s = mMinImageSize.width + "*" + mMinImageSize.height;
                            }
                            ToastUtil.showShortToast(getContext(), "bbbbb");
                        }
                    }
                }
            }
        };

        tvCancel.setOnClickListener(mOnClickLimitListener);
        tvSure.setOnClickListener(mOnClickLimitListener);
        mBtnReset.setOnClickListener(mOnClickLimitListener);
        mBtnLeftt90.setOnClickListener(mOnClickLimitListener);
    }


    /**
     * Apply filter select *
     *
     * @param llRate ll rate
     * @param v      v
     */
    private void applyFilterSelect(ViewGroup llRate, View v) {
        for (int i = 0; i < llRate.getChildCount(); i++) {
            if (llRate.getChildAt(i) instanceof LinearLayout) {
                LinearLayout tmp = (LinearLayout) llRate.getChildAt(i);
                View vTmp = tmp.getChildAt(0);
                if (vTmp != null && vTmp.getVisibility() == View.VISIBLE) {
                    vTmp.setSelected(v == vTmp);
                }
            }
        }
    }

    /**
     * Load bitmap *
     *
     * @param bitmap bitmap
     */
    private void loadBitmap(final Bitmap bitmap,boolean isConfigChage) {
        if (isAdded() && isVisible()) {
            if (mCutImageView != null && bitmap != null) {
                mCutImageView.setBitmap(bitmap,isConfigChage);
                mCutImageView.setBackgroundColor(mBackgroundColor);
            }
        }
    }

    /**
     * Load bitmap *
     *
     * @param path path
     */
    private void loadBitmap(final String path, boolean isConfigChange) {

        ThreadPoolUtil.createDiscardPool(1, 1).execute(new Runnable() {
            @Override
            public void run() {
                if (!isAdded() || !isVisible()) {
                    return;
                }
                final Bitmap bitmap = BitmapUtils.getFitSampleBitmap(getContext(), path);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (isAdded() && isVisible()) {
                            if (isConfigChange) {
                                int rotation = (int) mCutImageView.getRotation();
                                Bitmap nBitmap = BitmapUtils.rotateBitmap(bitmap, rotation);
                                mCutImageView.setBitmap(nBitmap,isConfigChange);
                            } else {
                                mCutImageView.setBitmap(bitmap,isConfigChange);
                            }
                            mCutImageView.setBackgroundColor(mBackgroundColor);
                        }
                    }
                });

            }
        });
    }

    /**
     * Hide
     */
    private void hide() {
        dismissAllowingStateLoss();
    }

    @Override
    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        mCutImageView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                mCutImageView.removeOnLayoutChangeListener(this);
                Bitmap bitmap = mCutImageView.getBitmap();
                if (bitmap != null && !bitmap.isRecycled()) {
                    mCutImageView.setBitmap(bitmap,true);
                }
            }
        });
    }

    /**
     * On start
     */
    @Override
    public void onStart() {
        super.onStart();
        Dialog d = getDialog();
        if (d != null) {
            Window w = d.getWindow();
            if (w != null) {
                w.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            }
        }
    }

    /**
     * 在主线程运行
     *
     * @param run run
     */
    public void runOnUiThread(Runnable run) {
        Activity act = getActivity();
        if (act != null && !act.isFinishing()) {
            act.runOnUiThread(run);
        }
    }

    /**
     * Decals select interface
     */
    public interface Listener {

        /**
         * Confirm *
         *
         * @param mBitmap      m bitmap
         * @param cutImageType cut type
         * @param isEdited     is edited
         */
        void confirm(Bitmap mBitmap, CutImageType cutImageType, boolean isEdited);

        /**
         * Cancel
         */
        void cancel();
    }
}