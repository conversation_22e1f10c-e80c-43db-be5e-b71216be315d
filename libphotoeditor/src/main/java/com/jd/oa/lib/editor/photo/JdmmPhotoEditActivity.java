package com.jd.oa.lib.editor.photo;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseBooleanArray;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.jd.oa.BaseActivity;
import com.jd.oa.lib.editor.base.OnClickLimitListener;
import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.photo.clip.CutImageType;
import com.jd.oa.lib.editor.photo.edit.CutImageDialogFragment;
import com.jd.oa.lib.editor.photo.paint.IPaintListener;
import com.jd.oa.lib.editor.photo.paint.MosaicHelper;
import com.jd.oa.lib.editor.photo.paint.MosaicMask;
import com.jd.oa.lib.editor.photo.paint.PaintHelper;
import com.jd.oa.lib.editor.photo.paint.PaintMask;
import com.jd.oa.lib.editor.photo.paint.PaintStruct;
import com.jd.oa.lib.editor.photo.paint.PaintStructHelper;
import com.jd.oa.lib.editor.photo.paint.PaintView;
import com.jd.oa.lib.editor.photo.paste.PasteBean;
import com.jd.oa.lib.editor.photo.paste.fonts.FontToolBar;
import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.lib.editor.photo.paste.view.CsViewPager;
import com.jd.oa.lib.editor.photo.paste.view.DecalsView;
import com.jd.oa.lib.editor.photo.paste.view.FontView;
import com.jd.oa.lib.editor.photo.paste.view.IViewType;
import com.jd.oa.lib.editor.photo.paste.view.PasteLayout;
import com.jd.oa.lib.editor.pub.Constants;
import com.jd.oa.lib.editor.pub.data.ReBean;
import com.jd.oa.lib.editor.util.FileUtils;
import com.jd.oa.lib.editor.util.ToastUtil;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.StatusBarUtil;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * JdmmPhotoEditActivity
 * 图片编辑界面
 *
 * <AUTHOR>
 * @date 2020 /8/5
 */
public class JdmmPhotoEditActivity extends BaseActivity implements PhotoAdapter.Listener, EditViewPagerAdapter.EditViewPagerListener, EditPhotoPresenter.EditView, PaintStructHelper {


    /**
     * 参数传输key
     */
    public static String TAG = "JdmmPhotoEditActivity";

    /**
     * 去相册增加 请求码
     */
    public final int REQUEST_ADD_CODE = 20002;

    /**
     * M fl back
     */
    private FrameLayout mFlBack;
    /**
     * 完成按钮
     */
    private TextView mBtnFinish;
    /**
     * 滑动适配器
     */
    private EditViewPagerAdapter mEditViewPagerAdapter;

    /**
     * Edit view pager
     */
    private CsViewPager editViewPager;

    /**
     * M current position
     */
    private int mCurrentPosition = 0;
    /**
     * M paste layout
     */
    private PasteLayout mPasteLayout = null;

    /**
     * 用于将贴纸文字合成图片使用
     */
    private PasteLayout mSavePasteLayout;

    /**
     * M edit image view
     */
    private FilterImageView mEditImageView = null;

    private PaintView paintView;
    private PaintMask paintStruct;
    private MosaicMask mosaicStruct;

    /**
     * Gets paste layout *
     *
     * @return the paste layout
     */
    private PasteLayout getPasteLayout() {
        return mPasteLayout;
    }

    /**
     * Gets edit image view *
     *
     * @return the edit image view
     */
    public FilterImageView getEditImageView() {
        return mEditImageView;
    }

    public PaintView getPaintView() {
        return paintView;
    }

    /**
     * 已经选择的图片适配器
     */
    private PhotoAdapter mPhotoAdapter;

    /**
     * 已经选择的图片
     */
    private ArrayList<LocalMedia> mList;


    /**
     * 是否在编辑页中编辑过
     */
    private boolean isEdited = false;

    /**
     * 当前选择的图片数据
     */
    private volatile LocalMedia mCurrentLocalMedia;

    /**
     * M param
     */
    private EditPhotoParam mParam;

    /**
     * M page id
     */
    final String mPageID = "picture_edit";
    /**
     * 裁剪dialog
     */
    CutImageDialogFragment mCutImageDialogFragment;


    /*** 滤镜数据获取*/
    private final EditPhotoPresenter mEditPhotoPresenter = new EditPhotoPresenter(this);

    /*** 文字底部弹层 */
    private FontToolBar mFontToolBar;

    private boolean autoClose = true;

    private BroadcastReceiver closeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (mCutImageDialogFragment != null && mCutImageDialogFragment.isAdded()) {
                    mCutImageDialogFragment.dismissAllowingStateLoss();
                }
            } catch (Exception e) {
            }
            finish();
        }
    };

    private IconFontView mBtnBack;

    /**
     * On create *
     *
     * @param savedInstanceState saved instance state
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarUtil.setColorNoTranslucent(this, Color.parseColor("#000000"));
        QMUIStatusBarHelper.setStatusBarDarkMode(this);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();//隐藏头
        }

        Intent intent = getIntent();
        if (intent == null || !intent.hasExtra(Constants.KEY_PARAM)) {
            finish();
            return;
        }
        mParam = intent.getParcelableExtra(Constants.KEY_PARAM);
//        DataConfig.getInstance().setDarkMode(mParam.darkMode);
        //自动关闭
        autoClose = intent.getBooleanExtra(Constants.KEY_AUTO_CLOSE, true);

        if (!autoClose) {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(Constants.ACTION_EDITOR_PHOTO_CLOSE);
            LocalBroadcastManager.getInstance(this).registerReceiver(closeReceiver, intentFilter);
        }

        mList = mParam.photoPath;

        if (mList == null || mList.size() <= 0) {//数据为空
            ToastUtil.showShortToast(this, "数据异常");
            errorDataReport(5001, 0, 0);
            finish();
            return;
        } else { //有数据不空，获取
            int emptyCount = 0;
            int i = 0;
            int count = mList.size();
            while (i < mList.size()) {
                LocalMedia item = mList.get(i);
                if (item == null || !FileUtils.isFileExist(item.getPath())) { //path如果空
                    mList.remove(i);
                    emptyCount++;
                } else {
                    i++;
                }
            }
            if (emptyCount > 0) {
                errorDataReport(5002, count, emptyCount);
                if (mList.size() > 0) {
                    ToastUtil.showShortToast(this, "图片文件异常,已为您过滤无效文件");
                }
            }
            if (mList.size() <= 0) {
                ToastUtil.showShortToast(this, "图片文件异常");
                finish();
                return;
            }
        }

        setContentView(R.layout.mm_edit_activity);
        initView();
        initOptionView();
//        createHandler(this).postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                showPaint();
//            }
//        }, 200);
    }


    /**
     * Error data report *
     *
     * @param errorCode  error code
     * @param count      count
     * @param errorCount error count
     */
    @Override
    public void errorDataReport(int errorCode, int count, int errorCount) {
        StringBuilder sb = new StringBuilder();
        sb.append("error_code:").append(errorCode).append("_dataCount:").append(count).append("_errorCount:").append(errorCount);
        Log.e(TAG, sb.toString());
    }

    /**
     * Removed *
     *
     * @param view view
     */
    @Override
    public void removed(IViewType view) {
        isLastPasterSubViewShowing = false;
        enableViewPageScroll();
        if (view != null) {
            if (view instanceof DecalsView) {

            } else if (view instanceof FontView && getPasteLayout() != null) {
                if (!getPasteLayout().isHasMoreFontView() && mFontToolBar != null) {
                    mFontToolBar.hide();
                }
            }
        }
    }

    /**
     * Instantiate item *
     *
     * @param view     view
     * @param position position
     */
    @Override
    public void instantiateItem(View view, LocalMedia item, int position) {
        if (mCurrentPosition == position) {
            updateImageAndPasteView(view, item);
        }
    }


    /**
     * Double type *
     *
     * @param view   view
     * @param reBean reBean
     * @param sBean  s bean
     */
    @Override
    public void doubleType(IViewType view, ReBean reBean, StyleBean sBean) {
        if (view instanceof FontView && mFontToolBar != null) {
            changeEditMode(Constants.MODE_EDITOR_FONT);
            mFontToolBar.updateTabOutSide(((FontView) view).getText(), reBean, sBean, true, true);
        }
    }

    /**
     * Sets last view *
     *
     * @param view   view
     * @param reBean reBean
     * @param sBean  s bean
     */
    @Override
    public void setLastView(IViewType view, ReBean reBean, StyleBean sBean) {
        isLastPasterSubViewShowing = view != null && view.isControlVisible();
        enableViewPageScroll();
        if (view != null) {
            if (view instanceof FontView) {
                if (mFontToolBar != null) {
                    mFontToolBar.updateTabOutSide(((FontView) view).getText(), reBean, sBean, false, mFontToolBar.getVisibility() == View.VISIBLE);
                }
            } else if (view instanceof DecalsView) {
                if (mFontToolBar != null && mFontToolBar.getVisibility() == View.VISIBLE) {
                    mFontToolBar.hide();
                }
            }
        }
    }

    /**
     * Is font editing boolean
     *
     * @return the boolean
     */
    @Override
    public boolean isFontEditing() {
        return mFontToolBar != null && mFontToolBar.getVisibility() == View.VISIBLE;
    }

    private View mMaskLayer;

    @Override
    public View getMaskLayer() {
        if (mMaskLayer == null) {
            mMaskLayer = new View(this);
            mMaskLayer.setLayoutParams(mPasteLayout.getLayoutParams());
            mMaskLayer.setBackgroundColor(getResources().getColor(R.color.mm_color_mask_layer, null));
        }
        return mMaskLayer;
    }

    /**
     * 初始化View
     */
    private void initView() {

        mSavePasteLayout = findViewById(R.id.mSavePasteLayout);

        mFlBack = findViewById(R.id.fl_back);
        mFlBack.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                mFlBack.getLayoutParams().width = right - left;
                mFlBack.getLayoutParams().height = bottom - top;
                v.removeOnLayoutChangeListener(this);
            }
        });

        /* 返回按钮 */
        mBtnBack = findViewById(R.id.btn_back);
        mBtnBack.setOnClickListener(mOnClickLimitListener);

        mBtnFinish = findViewById(R.id.btn_finish);
        mBtnFinish.setOnClickListener(mOnClickLimitListener);

        editViewPager = findViewById(R.id.editViewPager);
        editViewPager.setOffscreenPageLimit(1);
        editViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (mPhotoAdapter != null) {
                    mPhotoAdapter.setPosition(position);
                }
                selectPhotoItem(mList.get(position), position, false);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        mPhotoAdapter = new PhotoAdapter(this, this);
        mPhotoAdapter.setListData(mList, mCurrentLocalMedia);

        configFunctions();

        mFontToolBar = findViewById(R.id.mFontToolBar);
        mFontToolBar.loadData();
        mFontToolBar.setListener(new FontToolBar.Listener() {
            @Override
            public void applyText(String s) {
                if (getPasteLayout() != null) {
                    getPasteLayout().applyText(s);
                }
            }

            @Override
            public void addNewText() {
                addFont();
            }

            @Override
            public void applyFonts(ReBean bean) {
                if (getPasteLayout() != null) {
                    getPasteLayout().applyFont(bean);
                }
            }

            @Override
            public void applyStyle(StyleBean bean) {
                if (getPasteLayout() != null) {
                    getPasteLayout().applyStyle(bean);
                }
            }

            @Override
            public void show() {
                isFilterDecalsFontBarShowing = true;
                enableViewPageScroll();
            }

            @Override
            public void hide() {
                isFilterDecalsFontBarShowing = false;
                enableViewPageScroll();
                // 清除 mask layer
                mPasteLayout.removeView(getMaskLayer());
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (getPasteLayout() != null && getPasteLayout().getChildCount() > 0) {
                            List<View> removes = new ArrayList<>();
                            for (int i = getPasteLayout().getChildCount() - 1; i >= 0; i--) {
                                View child = getPasteLayout().getChildAt(i);
                                if (child instanceof FontView) {
                                    FontView fv = (FontView) child;
                                    if (TextUtils.isEmpty(fv.getText())) {
                                        removes.add(child);
                                    }
                                }
                            }
                            for (View v : removes) {
                                getPasteLayout().removeView(v);
                            }
                        }
                        editModeReset();
                    }
                });
            }

            @Override
            public StyleBean getCurrentStyle() {
                if (getPasteLayout() != null && getPasteLayout().currentStyleBean != null) {
                    return getPasteLayout().currentStyleBean;
                }
                return FontToolBar.getDefaultStyleBean();
            }

            @Override
            public void removed() {
                getPasteLayout().removeLastFontView();
            }
        });
    }

    private void configFunctions() {
        SparseBooleanArray functions = new SparseBooleanArray();
        functions.put(R.id.mBtnFonts, mParam.showFont);
        functions.put(R.id.mBtnEdit, mParam.showCutImage);
        functions.put(R.id.mBtnPaint, mParam.showPaint);
        functions.put(R.id.mBtnMosaic, mParam.showMosaic);
        boolean closeAll = true;
        for (int i = 0; i < functions.size(); i++) {
            int key = functions.keyAt(i);
            boolean show = functions.get(key);
            boolean configShow = configBtn(key, show);
            if (configShow) {
                closeAll = false;
            }
        }
        findViewById(R.id.mFLBottom).setVisibility(closeAll ? View.GONE : View.VISIBLE);
    }

    private boolean configBtn(int id, boolean show) {
        View btn = findViewById(id);
        if (btn == null) {
            return false;
        }

        btn.setOnClickListener(mOnClickLimitListener);
        btn.setVisibility(show ? View.VISIBLE : View.GONE);
        return show;
    }


    /**
     * On resume
     */
    @Override
    protected void onResume() {
        super.onResume();
        Intent intent = new Intent(Constants.ACTION_EDITOR_PHOTO_READY);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }


    /**
     * On back pressed
     */
    @Override
    public void onBackPressed() {
        quitLogic();
    }

    /**
     * 退出逻辑
     */
    private void quitLogic() {
        if (mFontToolBar != null && mFontToolBar.getVisibility() == View.VISIBLE) {
            mFontToolBar.cancel();
            return;
        }
        if (isEdited) {//是否编辑过
            finishEdit(false, false);
//            final JustDialog dialog = JustDialogUtils.createDialogWithStyle2(JdmmPhotoEditActivity.this, "aaaa", "bbbb", "确认");
//            if (dialog != null) {
//                dialog.setOnLeftButtonClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        dialog.dismiss();
//                    }
//                });
//                dialog.setOnRightButtonClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        finishEdit(false, false);
//                        dialog.dismiss();
//                    }
//                });
//                dialog.show();
//            }
        } else {
            finishEdit(false, false);
        }
    }

    /**
     * 防止连续点击对象 内部 500毫秒内只允许一次点击
     */
    private final OnClickLimitListener mOnClickLimitListener = new OnClickLimitListener() {
        @Override
        public void onClickLimit(View v) {
            int id = v.getId();
            if (id == R.id.btn_back) {
                quitLogic();
            } else if (id == R.id.btn_finish) {
                if (current_edit_mode.equalsIgnoreCase(Constants.MODE_EDITOR_PAIN)) {
                    // 确认
                    savePaintOpt();
                    if (paintStruct != null && mCurrentLocalMedia != null) {
                        mCurrentLocalMedia.paintTouchStack = paintStruct.touchStacks;
                    }
                } else if (current_edit_mode.equalsIgnoreCase(Constants.MODE_EDITOR_MOSAIC)) {
                    // 确认
                    saveMosaicOpt();
                    if (mosaicStruct != null && mCurrentLocalMedia != null) {
                        mCurrentLocalMedia.mosaicTouchStack = mosaicStruct.touchStacks;
                    }
                }
                if (mEditViewPagerAdapter != null) {
                    mEditViewPagerAdapter.cachePasteOfBufferView(false);
                }
                mEditPhotoPresenter.set(JdmmPhotoEditActivity.this, editViewPager, mSavePasteLayout, mEditViewPagerAdapter, paintView, mList, mParam.bitmapFormat);
                mEditPhotoPresenter.compoundData();
                editModeReset();
            } else if (id == R.id.mBtnEdit) {
                // 调用编辑之前判断，是否有需要保存的内容
                showEditPage();
            } else if (id == R.id.mBtnFonts) {
                addFont();
            } else if (id == R.id.mBtnPaint) {
                showPaint();
            } else if (id == R.id.mBtnMosaic) {
                showMosaic();
            }
        }
    };

    private void showMosaic() {
        if (isFinishing()) {
            return;
        }
        try {
            if (paintView != null) {
                Bitmap srcBitmap = mEditImageView.getSrcBitmap();
                if (srcBitmap != null && !srcBitmap.isRecycled()) {
                    mosaicStruct = new MosaicMask(Bitmap.createScaledBitmap(srcBitmap, paintView.getWidth(), paintView.getHeight(), true));
                    paintView.setMosaicStruct(mosaicStruct);
                    mosaicStruct.setActive(true);
                    mosaicStruct.setListener(new IPaintListener() {
                        @Override
                        public void addStackCallback() {
                            ifv_back.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
                        }

                        @Override
                        public void popStackCallback() {
                            if (!mosaicStruct.hasStack()) {
                                ifv_back.setTextColor(getResources().getColor(R.color.mm_color_edit_menu_disable));
                            }
                        }
                    });
                }
            }
            if (editViewPager != null) {
                editViewPager.setScrollEnabled(false);
            }
            MosaicHelper mosaicHelper = new MosaicHelper(this, mosaicOptView, btn_option_cancel, btn_option_confirm, ifv_back, new MosaicHelper.IMosaicListener() {
                @Override
                public void finish() {
                    editModeReset();
                }
            });
            changeEditMode(Constants.MODE_EDITOR_MOSAIC);
        } catch (Exception e) {
        }
    }

    /**
     * 显示画笔
     */
    private void showPaint() {
        if (isFinishing()) {
            return;
        }
        try {

            if (paintStruct == null) {
                paintStruct = new PaintMask();
            }
            if (paintView != null) {
                paintView.setPaintStruct(paintStruct);
            }
            paintStruct.setActive(true);
            paintStruct.setListener(new IPaintListener() {
                @Override
                public void addStackCallback() {
                    ifv_back.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
                }

                @Override
                public void popStackCallback() {
                    if (!paintStruct.hasStack()) {
                        ifv_back.setTextColor(getResources().getColor(R.color.mm_color_edit_menu_disable));
                    }
                }
            });
            if (editViewPager != null) {
                editViewPager.setScrollEnabled(false);
            }
            PaintHelper paintHelper = new PaintHelper(this, recyclerView, btn_option_cancel, btn_option_confirm, ifv_back, new PaintHelper.IPaintListener() {
                @Override
                public void finish() {
                    editModeReset();
                }
            });
            changeEditMode(Constants.MODE_EDITOR_PAIN);
        } catch (Exception e) {
        }
    }

    /**
     * Show font tool bar
     */
    private void showFontToolBar() {
        if (mFontToolBar != null) {
            mFontToolBar.show(true);
        }
    }

    /**
     * Add font
     */
    private void addFont() {
        changeEditMode(Constants.MODE_EDITOR_FONT);
        isEdited = true;
        //数量限制
        if (getPasteLayout() == null || getPasteLayout().getChildCount() >= mParam.getMaxDecalsNumber()) {
            overThanMaxNumber(mParam.getMaxDecalsNumber());
            return;
        }
        @SuppressLint("InflateParams") FontView view = (FontView) LayoutInflater.from(this).inflate(R.layout.mm_font_layout, null);
        if (mFontToolBar != null && mFontToolBar.getListener() != null) {
            view.setStyle(mFontToolBar.getListener().getCurrentStyle(), true);
        } else {
            view.setStyle(FontToolBar.getDefaultStyleBean(), true);
        }

        getPasteLayout().addViewWithType(view);
        showFontToolBar();
    }

    /**
     * Update image and paste view *
     *
     * @param view view
     * @param item item
     */
    private void updateImageAndPasteView(View view, final LocalMedia item) {
        if (view == null) {
            return;
        }
        mEditImageView = view.findViewById(R.id.mEditImageView);
        paintView = view.findViewById(R.id.paintView);
        mPasteLayout = view.findViewById(R.id.mPasteLayout);
        if (item == null || paintView == null) {
            return;
        }

        ViewGroup.LayoutParams layoutParams = paintView.getLayoutParams();
        if (mCurrentLocalMedia != null && layoutParams != null) {
            mCurrentLocalMedia.canvasWidth = layoutParams.width;
            mCurrentLocalMedia.canvasHeight = layoutParams.height;
        }
        if (item.paintTouchStack != null) {
            paintStruct = new PaintMask();
            paintView.setPaintStruct(paintStruct);
            paintView.invalidate();
        } else {
            paintStruct = null;
        }
        if (item.mosaicTouchStack != null && mEditImageView != null && layoutParams != null) {
            Bitmap srcBitmap = mEditImageView.getSrcBitmap();
            if (srcBitmap != null && !srcBitmap.isRecycled()) {
                Bitmap scaledBitmap = Bitmap.createScaledBitmap(srcBitmap, layoutParams.width, layoutParams.height, true);
                mosaicStruct = new MosaicMask(scaledBitmap);
                paintView.setMosaicStruct(mosaicStruct);
                paintView.invalidate();
            }
        } else {
            mosaicStruct = null;
        }
    }

    /**
     * 应用到全部记录
     */
    private boolean isApplyAllFilter = false;

    /**
     * 滤镜类型
     */
    private ReBean mCurrentFilter = null;
    private float mCurrentFilterIntensity = 1f;
    private Bitmap mFilterBitmap = null;


    /**
     * Show edit page
     */
    private void showEditPage() {
        if (isFinishing()) {
            return;
        }
        editModeReset();
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                showEdit();
            }
        }, 200);
    }

    public void showEdit() {
        String path = mCurrentLocalMedia != null ? mCurrentLocalMedia.getEditPath() : "";

        if (TextUtils.isEmpty(path)) {
            ToastUtil.showShortToast(this, "图片数据异常，请重试");
            return;
        }
        CutImageType cutImageType = mCurrentLocalMedia != null ? mCurrentLocalMedia.mCutImageType : null;
        if (mCutImageDialogFragment == null || !mCutImageDialogFragment.isAdded() && getSupportFragmentManager().findFragmentByTag("CutImageDialogFragment") == null) {
            mCutImageDialogFragment = CutImageDialogFragment.newInstance(null, path, new CutImageDialogFragment.Listener() {

                @Override
                public void confirm(final Bitmap bitmap, CutImageType cutImageType, boolean isEdited) {
                    if (mCurrentLocalMedia != null) {
                        if (isEdited) {
                            JdmmPhotoEditActivity.this.isEdited = true;
                        }
                        mCurrentLocalMedia.mCutImageType = cutImageType;
                        mCurrentLocalMedia.addExtra(LocalMedia.MM_IMAGE_IS_CROP, isEdited ? "1" : "0");
                        mEditPhotoPresenter.getCallerRunsPool().execute(new Runnable() {
                            @Override
                            public void run() {
                                if (bitmap != null && !bitmap.isRecycled()) {
                                    String tempPath = FileUtils.saveBitmap(bitmap, null, mParam.bitmapFormat);
                                    if (FileUtils.isFileExist(tempPath)) {
                                        if (!TextUtils.isEmpty(mCurrentLocalMedia.getTempPath())) {
                                            FileUtils.deleteFile(mCurrentLocalMedia.getTempPath());
                                        }
                                        mCurrentLocalMedia.setTempPath(tempPath);
                                    } else {
                                        errorDataReport(5003, mList.size(), 0);
                                        runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                ToastUtil.showShortToast(JdmmPhotoEditActivity.this, "裁剪图片失败，请重试");
                                            }
                                        });
                                        return;
                                    }
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            setEditImageBitmap(bitmap);
                                        }
                                    });
                                } else {
                                    errorDataReport(5004, mList.size(), 0);
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            ToastUtil.showShortToast(JdmmPhotoEditActivity.this, "裁剪图片失败，请重试");
                                        }
                                    });
                                }
                            }
                        });
                    }

                    mCutImageDialogFragment = null;
                }

                @Override
                public void cancel() {
                    mCutImageDialogFragment = null;
                }
            });
            mCutImageDialogFragment.setCutTypes(mParam.cutImageTypes);
            mCutImageDialogFragment.setImageCutRectDrag(mParam.isImageCutRectDrag);
            mCutImageDialogFragment.setShowCutReset(mParam.showCutReset);
            mCutImageDialogFragment.setShowCutRotate(mParam.showCutRotate);
            mCutImageDialogFragment.setDefaultCutType(cutImageType);
            mCutImageDialogFragment.setMinImageSize(mParam.cutMinImageSize);
            mCutImageDialogFragment.setCustomImageRate(mParam.cutCustomImageRate);
            if (!isFinishing() && !getSupportFragmentManager().isDestroyed()) {
                mCutImageDialogFragment.show(getSupportFragmentManager(), "CutImageDialogFragment");
            }
        }
    }

    /**
     * 完成编辑逻辑
     *
     * @param save    save
     * @param isClear is clear
     */
    @Override
    public void finishEdit(final boolean save, final boolean isClear) {
        if (save) {
            if (mList != null && mList.size() > 0) {
                for (LocalMedia item : mList) {
                    if (item != null && FileUtils.isFileExist(item.getTempPath())) {
                        if (mParam.isSavePhotoToAlbum) {
                            FileUtils.insertToAlbum(this, item.getTempPath(), false);
                        }
                        item.setPath(item.getTempPath());
                        item.setTempPath(null);
                        //编辑过的直接使用沙盒路径，清除原始的Uri
                        item.setUri(null);
                    }
                    reportData(item);
                }
            }
        } else { //返回按钮，直接清除数据中的临时数据
            if (mList != null && mList.size() > 0) {
                for (LocalMedia item : mList) {
                    if (!TextUtils.isEmpty(item.getTempPath())) {
                        FileUtils.deleteFile(item.getTempPath());
                        item.setTempPath(null);
                    }
                }
            }
        }
        if (mList != null) {
            if (isClear) {
                mList.clear();
                notifyViewPager();
            } else {
                int i = 0;
                int emptyCount = 0;
                int count = mList.size();
                while (i < mList.size()) {
                    LocalMedia item = mList.get(i);
                    if (item == null || !FileUtils.isFileExist(item.getPath())) { //path如果空
                        emptyCount++;
                        mList.remove(i);
                    } else {
                        i++;
                    }
                }
                if (save && emptyCount > 0) {
                    notifyViewPager();
                    errorDataReport(5008, count, emptyCount);
                }
                if (save && mList.size() <= 0) {
                    ToastUtil.showShortToast(JdmmPhotoEditActivity.this, "保存图片异常，请重试");
                    finish();
                    return;
                }
            }
        }
        Intent intent = new Intent();
        if (save) {
            intent.putParcelableArrayListExtra(Constants.KEY_PARAM, mList);
        }
        if (autoClose) {
            setResult(save ? RESULT_OK : RESULT_FIRST_USER, intent);
        } else {
            intent.setAction(Constants.ACTION_EDITOR_PHOTO_CALL_BACK);
            if (save) {
                intent.putExtra(Constants.KEY_RESULT, RESULT_OK);
            } else {
                intent.putExtra(Constants.KEY_RESULT, RESULT_FIRST_USER);
            }
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
        }

        if (save && autoClose || !save) {
            finish();
        }
    }

    /**
     * 上摄子午线数据
     *
     * @param item item
     */
    private void reportData(LocalMedia item) {
        if (item != null) {
            try {
                ReBean fBean = item.rFilterBean;
                if (fBean != null) {
                    StringBuffer s = new StringBuffer();
                    s.append(fBean.id).append("_").append("滤镜").append("_").append(fBean.g == null ? "NULL" : fBean.g.name).append("_").append(TextUtils.isEmpty(fBean.version) ? "NULL" : fBean.version).append("_").append("图片编辑页");
                    item.rFilterBean = null;
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
            try {
                ArrayList<PasteBean> dBeans = item.rPasterBean;
                if (dBeans != null && dBeans.size() > 0) {
                    for (PasteBean f : dBeans) {
                        if (f.type == ReBean.TYPE.DECALS && f.bean != null) {
                            ReBean d = f.bean;
                            StringBuffer s = new StringBuffer();
                            s.append(d.id).append("_").append("贴纸").append("_").append(d.g == null ? "NULL" : d.g.name).append("_").append(TextUtils.isEmpty(d.version) ? "NULL" : d.version).append("_").append("图片编辑页");
                        } else if (f.type == ReBean.TYPE.FONT && f.bean != null) {
                            if (f != null) {
                                StringBuffer s = new StringBuffer();
                                s.append(f.bean == null ? "NULL" : f.bean.id).append("_").append(f.stylebean == null ? "NULL" : f.stylebean.id);
                            }
                        }
                    }
                    item.rPasterBean = null;
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 设置当前编辑的图片
     *
     * @param bitmap bitmap
     */
    private void setEditImageBitmap(final Bitmap bitmap) {
        //将图片放到编辑区域
        if (bitmap != null) {
            //更新当前编辑的图片
            FilterImageView filterImageView = getEditImageView();
            if (filterImageView != null) {
                filterImageView.setImageBitmap(bitmap);
                if (mCurrentLocalMedia != null && !filterImageView.isSameFilter(mCurrentLocalMedia.getFilterPath(), mCurrentLocalMedia.fIntensity)) {
                    filterImageView.setFilter(bitmap, mCurrentLocalMedia.getFilterPath(), mCurrentLocalMedia.fIntensity, false, mEditPhotoPresenter.getDiscardPool());
                }
            }
            int width = bitmap == null ? 0 : bitmap.getWidth();
            int height = bitmap == null ? 0 : bitmap.getHeight();
            EditPhotoPresenter.adjustSize(editViewPager, getEditImageView(), getPasteLayout(), getPaintView(), width, height);

        }
    }


    /**
     * 第一次同步数据
     */
    boolean isFirst = true;

    /**
     * Update view page display *
     *
     * @param position position
     */
    private void updateViewPageDisplay(int position) {
        notifyViewPager();
        mEditViewPagerAdapter = new EditViewPagerAdapter(JdmmPhotoEditActivity.this, mList, mEditPhotoPresenter, this);
        mEditViewPagerAdapter.setCurPosition(position);
        editViewPager.setAdapter(mEditViewPagerAdapter);
        notifyViewPager();
    }

    private void notifyViewPager() {
        if (mEditViewPagerAdapter != null) {
            mEditViewPagerAdapter.notifyDataSetChanged();
        }
    }

    /**
     * Is filter decals font bar showing
     */
    private boolean isFilterDecalsFontBarShowing = false;
    /**
     * Is last paster view showing
     */
    private boolean isLastPasterSubViewShowing = false;

    /**
     * Enable view page scroll *
     */
    private void enableViewPageScroll() {
        if (editViewPager != null) {
            editViewPager.setScrollEnabled(!isFilterDecalsFontBarShowing && !isLastPasterSubViewShowing);
        }
    }

    /**
     * 照片被选中
     *
     * @param item         item
     * @param position     position
     * @param isNeedUpdate is need update
     */
    @Override
    public void selectPhotoItem(final LocalMedia item, final int position, final boolean isNeedUpdate) {
        isFilterDecalsFontBarShowing = false;
        isLastPasterSubViewShowing = false;
        enableViewPageScroll();
        mCurrentLocalMedia = item;
        if (isFirst) {
            if (editViewPager != null) {
                //第一次图片默认选中也走了适配器设置数据流程，第一次调用在initView(OnCreate)中，这时候需要根据图片控件的父容器计算图片控件的宽高（adJustLayout方法），父容器在这个时候偶现测量未完成，获取到的宽高为0。导致图片未显示出来
                editViewPager.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
                    @Override
                    public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                        if (isNeedUpdate) {
                            updateViewPageDisplay(position);
                        }
                        mCurrentPosition = position;
                        if (mEditViewPagerAdapter != null) {
                            mEditViewPagerAdapter.setCurPosition(position);
                        }
                        editViewPager.setCurrentItem(position, false);
                        v.removeOnLayoutChangeListener(this);
                    }
                });
            }
        } else {
            if (isNeedUpdate) {
                updateViewPageDisplay(position);
            }
            mCurrentPosition = position;
            if (mEditViewPagerAdapter != null) {
                mEditViewPagerAdapter.setCurPosition(position);
            }
            editViewPager.setCurrentItem(position, false);
            if (mEditViewPagerAdapter != null) {
                View view = mEditViewPagerAdapter.getViewWithPosition(mCurrentPosition);
                updateImageAndPasteView(view, item);
            }
        }
        isFirst = false;
    }

    /**
     * Delete last item boolean
     */
    @Override
    public void deleteLastItem() {
//        final JustDialog dialog = JustDialogUtils.createDialogWithStyle2(JdmmPhotoEditActivity.this, "aaa", "cancel", "ok");
//        if (dialog != null) {
//            dialog.setOnLeftButtonClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    dialog.dismiss();
//                }
//            });
//            dialog.setOnRightButtonClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    finishEdit(false, true);
//                    dialog.dismiss();
//                }
//            });
//            dialog.show();
//        } else {
//            finishEdit(false, true);
//        }
    }

    /**
     * Delete item
     */
    @Override
    public void deleteItem() {
        if (mEditViewPagerAdapter != null) {
            mEditViewPagerAdapter.notifyDataSetChanged();
            mEditViewPagerAdapter.cachePasteOfBufferView(true);
        }
    }

    /**
     * 显示加载中
     */
    @Override
    public void showLoading(int resStrID) {

    }

    /**
     * 隐藏 加载
     */
    @Override
    public void hideLoading() {

    }

    /**
     * Sets finish visible *
     *
     * @param isVisible is visible
     */
    private void setFinishVisible(boolean isVisible) {
        if (mBtnFinish != null) {
            mBtnFinish.setVisibility(isVisible ? View.VISIBLE : View.GONE);
        }
    }


    /**
     * 贴纸超数提示
     *
     * @param num num
     */
    private void overThanMaxNumber(int num) {
//        String maxStr = getResources().getString(R.string.mm_max_decals);
//        maxStr = String.format(maxStr, num);
//        ToastUtil.showShortToast(this, maxStr);
    }

    /**
     * 根据参数 启动当前Activity
     *
     * @param activity       activity
     * @param editPhotoParam editPhotoParam
     * @param requestCode    request code
     */
    public static void startActivityForResult(@NonNull Activity activity, EditPhotoParam editPhotoParam, int requestCode) {
        if (editPhotoParam == null || !editPhotoParam.isValid()) {
            return;
        }
        Intent intent = new Intent(activity, JdmmPhotoEditActivity.class);
        intent.putExtra(Constants.KEY_PARAM, editPhotoParam);
        activity.startActivityForResult(intent, requestCode);
    }


    /**
     * 选择数据返回
     *
     * @param requestCode 请求码
     * @param resultCode  结果
     * @param data        返回数据
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_ADD_CODE && resultCode == RESULT_OK && data != null) {
            //从相册添加照片后返回
            if (data.hasExtra(Constants.KEY_PARAM)) {
                ArrayList<LocalMedia> newList = data.getParcelableArrayListExtra(Constants.KEY_PARAM);
                if (newList != null && newList.size() > 0) {
                    int i = 0;
                    int emptyCount = 0;
                    int count = newList.size();
                    while (i < newList.size()) {
                        LocalMedia item = newList.get(i);
                        if (item == null || !FileUtils.isFileExist(item.getPath())) { //path如果空
                            emptyCount++;
                            newList.remove(i);
                        } else {
                            i++;
                        }
                    }
                    if (emptyCount > 0) {
                        errorDataReport(5011, count, emptyCount);
                    }
                    if (newList.size() <= 0) {
                        ToastUtil.showShortToast(JdmmPhotoEditActivity.this, "添加图片失败，请重试");
                        return;
                    }

                    HashMap<String, LocalMedia> newHashMap = new HashMap<>();
                    for (LocalMedia item : newList) {
                        if (isApplyAllFilter && item.rFilterBean == null) {
                            item.rFilterBean = mCurrentFilter;
                            item.fIntensity = mCurrentFilterIntensity;
                        }
                        newHashMap.put(item.getPath(), item);
                    }

                    int index = 0;
                    if (mList != null && mList.size() > 0) {
                        for (LocalMedia oldItem : mList) {
                            //从新数据内去 查询老数据是否存在
                            LocalMedia newItem = newHashMap.get(oldItem.getPath());
                            if (newItem != null) {
                                //新数组内存在老数据 将老数据拷贝到 新数据
                                newItem.setTempPath(oldItem.getTempPath());
                                newItem.setExtraMap(oldItem.getExtraMap());
                                newItem.setFilterInfo(oldItem.getFilterInfo());
                                newItem.rFilterBean = oldItem.rFilterBean;
                                newItem.rPasterBean = oldItem.rPasterBean;
                                newItem.fIntensity = oldItem.fIntensity;

                                newItem.rPropGrop = oldItem.rPropGrop;
                                newItem.rPropBean = oldItem.rPropBean;
                                newItem.mCutImageType = oldItem.mCutImageType;
                            } else {
                                //新数组内不存在老数据 将老数据临时文件删除
                                if (!TextUtils.isEmpty(oldItem.getTempPath())) {
                                    FileUtils.deleteDir(new File(oldItem.getTempPath()));
                                }
                            }
                        }

                        //增加了图片，移动到最后
                        if (newList.size() > mList.size()) {
                            index = newList.size() - 1;
                        }
                    }
                    mList = newList;
                    if (mEditViewPagerAdapter != null) {
                        mEditViewPagerAdapter.setData(newList);
                    }
                    if (mPhotoAdapter != null) {
                        mPhotoAdapter.setListData(mList, mList.get(index));
                    }
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        notifyViewPager();
        if (!autoClose) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(closeReceiver);
            Intent intent = new Intent();
            intent.setAction(Constants.ACTION_EDITOR_PHOTO_CALL_BACK);
            intent.putExtra(Constants.KEY_RESULT, Constants.VAL_RESULT_FINISH);
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
        }
        super.onDestroy();
    }

    @Override
    public PaintMask getPaintStruct() {
        return this.paintStruct;
    }

    @Override
    public MosaicMask getMosaicStruct() {
        return this.mosaicStruct;
    }

    @Override
    public void invalidatePaint() {
        if (paintView != null) {
            paintView.invalidate();
        }
    }

    @Override
    public void saveMosaicStack() {
        onSavePaint(mosaicStruct);
    }

    @Override
    public void savePaintStack() {
        onSavePaint(paintStruct);
    }

    private void onSavePaint(final PaintStruct paintStruct) {
        if (mCurrentLocalMedia == null || paintStruct == null || paintStruct.touchStacks.isEmpty() || mEditPhotoPresenter == null) {
            return;
        }
        isEdited = true;
        mEditPhotoPresenter.getCallerRunsPool().execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Bitmap srcBitmap = mEditImageView.getSrcBitmap();
                    Bitmap bitmap = mEditPhotoPresenter.compoundPaint(srcBitmap, paintStruct);
                    if (bitmap == null || bitmap.isRecycled()) {
                        return;
                    }
                    final Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, paintView.getWidth(), paintView.getHeight(), true);
                    paintStruct.touchStacks.clear();
                    if (scaledBitmap == null || scaledBitmap.isRecycled()) {
                        return;
                    }
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (scaledBitmap == null || scaledBitmap.isRecycled()) {
                                return;
                            }
                            try {
                                setEditImageBitmap(scaledBitmap);
                                invalidatePaint();
                            } catch (Exception e) {
                            }
                        }
                    });

                    String tempPath = FileUtils.saveBitmap(scaledBitmap, null, mParam.bitmapFormat);
                    if (!FileUtils.isFileExist(tempPath)) {
                        return;
                    }
                    if (!TextUtils.isEmpty(mCurrentLocalMedia.getTempPath())) {
                        FileUtils.deleteFile(mCurrentLocalMedia.getTempPath());
                    }
                    mCurrentLocalMedia.setTempPath(tempPath);
                } catch (Exception e) {
                }
            }
        });
    }

    @Override
    public void dismissPaint() {
        if (editViewPager != null) {
            editViewPager.setScrollEnabled(true);
        }
    }

    private String current_edit_mode = Constants.MODE_EDITOR_NORMAL;

    private TextView btn_option_cancel; // 取消
    private TextView btn_option_confirm; // 确认

    private IconFontView ifv_pain;
    private IconFontView ifv_font;
    private IconFontView ifv_mosaic;
    private IconFontView ifv_back;

    private RecyclerView recyclerView;
    private LinearLayout mosaicOptView;


    public void initOptionView() {
        btn_option_cancel = findViewById(R.id.btn_option_cancel);
        btn_option_cancel.setVisibility(View.GONE);
        btn_option_confirm = findViewById(R.id.btn_option_confirm);
        btn_option_confirm.setVisibility(View.GONE);

        ifv_pain = findViewById(R.id.ifv_pain);
        ifv_mosaic = findViewById(R.id.ifv_mosaic);
        ifv_font = findViewById(R.id.ifv_font);
        ifv_back = findViewById(R.id.ifv_back);

        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        recyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent.getChildLayoutPosition(view) != 0) {
                    outRect.left = DensityUtil.dp2px(JdmmPhotoEditActivity.this, 15.0f);
                }
            }
        });
        mosaicOptView = findViewById(R.id.radio_mosaic);
    }

    public void changeEditMode(String editMode) {
        if (current_edit_mode.equals(editMode)) {
            return;
        }
        editModeReset();
        switch (editMode) {
            case Constants.MODE_EDITOR_PAIN:
                mBtnBack.setVisibility(View.GONE);
                btn_option_cancel.setVisibility(View.VISIBLE);
                btn_option_confirm.setVisibility(View.VISIBLE);
                ifv_pain.setTextColor(getResources().getColor(R.color.mm_color_btn_me));
                break;
            case Constants.MODE_EDITOR_MOSAIC:
                mBtnBack.setVisibility(View.GONE);
                btn_option_cancel.setVisibility(View.VISIBLE);
                btn_option_confirm.setVisibility(View.VISIBLE);
                ifv_mosaic.setTextColor(getResources().getColor(R.color.mm_color_btn_me));
                break;
            case Constants.MODE_EDITOR_FONT:
                mBtnBack.setVisibility(View.GONE);
                btn_option_cancel.setVisibility(View.VISIBLE);
                btn_option_confirm.setVisibility(View.VISIBLE);
                ifv_font.setTextColor(getResources().getColor(R.color.mm_color_btn_me));
                if (mFontToolBar != null) {
                    mFontToolBar.addEvent(btn_option_confirm, btn_option_cancel);
                }
                break;
            default:
                break;
        }
        current_edit_mode = editMode;
    }

    /**
     * 重置编辑模式
     */
    public void editModeReset() {
        if (current_edit_mode.equalsIgnoreCase(Constants.MODE_EDITOR_NORMAL)) {
            return;
        }
        btn_option_cancel.setVisibility(View.GONE);
        btn_option_confirm.setVisibility(View.GONE);
        mBtnBack.setVisibility(View.VISIBLE);
        ifv_back.setTextColor(getResources().getColor(R.color.mm_color_edit_menu_disable));

        switch (current_edit_mode) {
            case Constants.MODE_EDITOR_PAIN:
                savePaintOpt();
                ifv_pain.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
                recyclerView.setVisibility(View.INVISIBLE);
                break;
            case Constants.MODE_EDITOR_MOSAIC:
                saveMosaicOpt();
                ifv_mosaic.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
                mosaicOptView.setVisibility(View.GONE);
                break;
            case Constants.MODE_EDITOR_FONT:
                ifv_font.setTextColor(getResources().getColor(R.color.mm_color_edit_menu));
                break;
            default:
                break;
        }
        current_edit_mode = Constants.MODE_EDITOR_NORMAL;
    }

    public void saveMosaicOpt() {
        if (getMosaicStruct() != null) {
            saveMosaicStack();
            dismissPaint();
            getMosaicStruct().setActive(false);
        }
    }

    public void savePaintOpt() {
        if (getPaintStruct() != null) {
            savePaintStack();
            dismissPaint();
            getPaintStruct().setActive(false);
        }
    }


    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        mFlBack.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                mFlBack.removeOnLayoutChangeListener(this);
                mFlBack.post(new Runnable() {
                    @Override
                    public void run() {
                        WindowManager windowManager = (WindowManager) mFlBack.getContext().getSystemService(Context.WINDOW_SERVICE);
                        Display defaultDisplay = windowManager.getDefaultDisplay();
                        Point point = new Point();
                        defaultDisplay.getSize(point);
                        int x = point.x;
                        int y = point.y;
                        ViewGroup.LayoutParams layoutParams = mFlBack.getLayoutParams();
                        layoutParams.width = x - left;
                        layoutParams.height = y - top - QMUIStatusBarHelper.getStatusbarHeight(v.getContext());
                        mFlBack.setLayoutParams(layoutParams);
                    }
                });
            }
        });
        super.onConfigurationChanged(newConfig);
    }

}
