package com.jd.oa.lib.editor.util;

import java.util.ArrayList;
import java.util.List;

/***
 * 颜色工具类
 */
public class ColorUtil {

    /**
     * hex to rgba
     *
     * @param hexValue
     * @return
     */
    public static String hexToRGBA(String hexValue) {
        int color = (int) Long.parseLong(hexValue.replaceFirst("#", ""), 16);
        int red = (color >> 16) & 0xFF;
        int green = (color >> 8) & 0xFF;
        int blue = color & 0xFF;
        int alpha = 1; // 默认不透明
        return red + "," + green + "," + blue + "," + alpha;
    }

    /**
     * rgba to hex
     *
     * @param color
     * @return
     */
    public static String rgbaToHex(String color) {
        try {
            if (color.indexOf(",") < 0) {
                return color;
            }
            String[] rgba = color.split(",");
            if (rgba != null && rgba.length == 4) {
                String hex = String.format("#%02X%02X%02X%02X", Integer.valueOf(rgba[0]), Integer.valueOf(rgba[1]), Integer.valueOf(rgba[2]), Integer.valueOf(rgba[3]));
                return hex.replaceFirst("#FF", "#");
            }
        } catch (Exception e) {
        }
        return color;
    }

    public static List<String> getPaintColors() {
        List<String> datas = new ArrayList<>();
        datas.add("#FFFFFF");
        datas.add("#000000");
        datas.add("#FF3D4D");
        datas.add("#FF860D");
        datas.add("#FFCC00");
        datas.add("#6EDA01");
        datas.add("#05C5EB");
        datas.add("#1988FA");
        datas.add("#7142FF");
        return datas;
    }
}
