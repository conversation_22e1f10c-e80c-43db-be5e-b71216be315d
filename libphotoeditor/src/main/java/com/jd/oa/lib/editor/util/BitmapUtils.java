package com.jd.oa.lib.editor.util;

import android.content.ContentResolver;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;

import com.jd.oa.utils.DisplayUtils;

import java.io.FileDescriptor;
import java.io.IOException;
import java.io.InputStream;

/*
 * Time: 2024/5/31
 * Author: qudongshi
 * Description:
 */
public class BitmapUtils {
    /**
     * 对宽或高大于屏幕0.5倍以上的图片进行缩放，缩放到宽或高小于等于屏幕大小
     *
     * @param context context
     * @param path    path
     * @return 旋转及压缩后的图片 fit sample bitmap
     */
    public static Bitmap getFitSampleBitmap(Context context, String path) {
        int screenWidth = DisplayUtils.getScreenWidth();
        int screenHeight = DisplayUtils.getScreenHeight();
//        int screenWidth = Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP_MR1 ? AmDpiUtil.getScreenWidth(context) : 720;
//        int screenHeight = Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP_MR1 ? AmDpiUtil.getScreenHeight(context) : 720;

        try {
            if (!FileUtils.isFileExist(path)) {
                return null;
            }
            return fitSampleBitmap(context, path,screenWidth,screenHeight);
        } catch (Exception e) {
            e.printStackTrace();
            Runtime.getRuntime().gc();
            return fitSampleBitmap(context, path,screenWidth,screenHeight);
        } catch (Error e) {
            Runtime.getRuntime().gc();
            e.printStackTrace();
            return fitSampleBitmap(context, path,screenWidth,screenHeight);
        }
    }

    /**
     * Fit sample bitmap bitmap
     *
     * @param context context
     * @param path    path
     * @return the bitmap
     */
    private static Bitmap fitSampleBitmap(Context context, String path,int screenWidth,int screenHeight) {
        if(FileUtils.isUri(path)){
            return fitSampleBitmap(context, Uri.parse(path), screenWidth, screenHeight);
        }
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(path, options);
        //图片不存在
        if (options.outHeight < 0 || options.outWidth < 0) {
            return null;
        }

        int degree = getBitmapDegree(path);
        options.inSampleSize = getFitInSampleSize(context, options, degree,screenWidth,screenHeight);
        options.inJustDecodeBounds = false;
        Bitmap bitmap = BitmapFactory.decodeFile(path, options);
        //如果图片角度不为0，将图片按照degree旋转

        if ((degree != 0) && (bitmap != null)) {
            bitmap = rotateBitmap(bitmap, degree);
        }
        return bitmap;
    }

    /**
     * 获取缩放比例
     *
     * @param context context
     * @param options 图片options
     * @param degree  degree
     * @return 压缩比例 fit in sample size
     */
    private static int getFitInSampleSize(Context context, BitmapFactory.Options options, int degree,int screenWidth,int screenHeight) {
        int inSampleSize = 1;
        int bitmapWidth;
        int bitmapHeight;
        int degree90 = 90;
        int degree270 = 270;
        if (degree == degree90 || degree == degree270) {
            bitmapWidth = options.outHeight;
            bitmapHeight = options.outWidth;
        } else {
            bitmapWidth = options.outWidth;
            bitmapHeight = options.outHeight;
        }
        if (bitmapWidth > screenWidth || bitmapHeight > screenHeight) {
            int widthRatio = Math.round((float) bitmapWidth / (float) screenWidth);
            int heightRatio = Math.round((float) bitmapHeight / (float) screenHeight);
            inSampleSize = Math.max(widthRatio, heightRatio);
        }
        return inSampleSize;
    }

    public static Bitmap rotateBitmap(Bitmap bitmap, int degree) {
        Matrix matrix = new Matrix();
        matrix.postRotate((float) degree);
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        bitmap = Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true);
        return bitmap;
    }

    private static Bitmap fitSampleBitmapFD(Context context, Uri fileUri,int screenWidth,int screenHeight) {
        // Open a specific media item using InputStream.
        ContentResolver resolver = context.getContentResolver();
        Bitmap bitmap = null;
        ParcelFileDescriptor optionDescriptor = null;

        try {
            // 尝试打开URI指向的文件描述符
            optionDescriptor = resolver.openFileDescriptor(fileUri, "r");

            if(optionDescriptor == null){
                return null;
            }
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            FileDescriptor fileDescriptor = optionDescriptor.getFileDescriptor();
            BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options);

            int degree = getBitmapDegree(fileUri.toString());
            options.inSampleSize = getFitInSampleSize(context, options, degree,screenWidth,screenHeight);
            options.inJustDecodeBounds = false;
            bitmap = BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options);

            //如果图片角度不为0，将图片按照degree旋转
            if ((degree != 0) && (bitmap != null)) {
                bitmap = rotateBitmap(bitmap, degree);
            }

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (optionDescriptor != null) {
                try {
                    optionDescriptor.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return bitmap;
    }

    /**
     * Fit sample bitmap bitmap
     *
     * @param context context
     * @param fileUri    fileUri
     * @return the bitmap
     */
    private static Bitmap fitSampleBitmap(Context context, Uri fileUri,int screenWidth,int screenHeight) {
//        if (DataConfig.getInstance().isUseBitmapFd()) {
//            Bitmap bitmap = fitSampleBitmapFD(context, fileUri, screenWidth, screenHeight);
//            if (bitmap != null) {
//                return bitmap;
//            }
//        }

        // Open a specific media item using InputStream.
        ContentResolver resolver = context.getContentResolver();
        Bitmap bitmap = null;
        InputStream optionStream = null;
        InputStream stream = null;

        try {
            optionStream = resolver.openInputStream(fileUri);
            if(optionStream == null){
                return null;
            }
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeStream(optionStream, null, options);
            int degree = getBitmapDegree(fileUri.toString());
            options.inSampleSize = getFitInSampleSize(context, options, degree,screenWidth,screenHeight);
            options.inJustDecodeBounds = false;

            if (optionStream != null) {
                try {
                    optionStream.close();
                    optionStream = null;
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            stream = resolver.openInputStream(fileUri);
            if(stream == null){
                return null;
            }
            bitmap = BitmapFactory.decodeStream(stream, null, options);

            //如果图片角度不为0，将图片按照degree旋转
            if ((degree != 0) && (bitmap != null)) {
                bitmap = rotateBitmap(bitmap, degree);
            }

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (optionStream != null) {
                try {
                    optionStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(stream != null){
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return bitmap;
    }

    public static Bitmap getFitSampleBitmap(Context context, String path,int tarWidth,int tarHeight) {
        try {
            if (!FileUtils.isFileExist(path)) {
                return null;
            }
            return fitSampleBitmap(context, path,tarWidth,tarHeight);
        } catch (Throwable e) {
            e.printStackTrace();
            Runtime.getRuntime().gc();
            return fitSampleBitmap(context, path,tarWidth,tarHeight);
        }

    }

    public static int getBitmapDegree(String path) {
        int degree = 0;
        try {
            if (TextUtils.isEmpty(path)) {
                return degree;
            }
            // 从指定路径下读取图片，并获取其EXIF信息
            ExifInterface exifInterface = new ExifInterface(path);
            // 获取图片的旋转信息
            int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION,
                    ExifInterface.ORIENTATION_NORMAL);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    degree = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    degree = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    degree = 270;
                    break;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return degree;
    }
}
