package com.jd.oa.lib.editor.photo.paint;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.lib.editor.base.OnClickLimitListener;
import com.jd.oa.lib.editor.photo.R;

/*
 * Time: 2024/6/6
 * Author: qudongshi
 * Description:
 */
public class MosaicHelper {

    private MosaicMask mosaicMask;
    private PaintStructHelper structHelper;

    private LinearLayout mOptionLayout;
    private View mCancelView;
    private View mConfirmView;
    private View mBackView;
    private int mCancelViewId;
    private int mConfirmViewId;
    private int mBackViewId;

    private Context mContext;

    private IMosaicListener mListener;

    private View radioSmall, radioMiddle, radioBig;
    private View selectedRadio;

    private MosaicHelper() {
    }

    public MosaicHelper(Context context, LinearLayout optionLayout, View cancelView, View confirmView, View backView, IMosaicListener listener) {
        if (context instanceof PaintStructHelper) {
            structHelper = (PaintStructHelper) context;
            mosaicMask = structHelper.getMosaicStruct();
        } else {
            return;
        }

        mContext = context;
        mOptionLayout = optionLayout;
        mCancelView = cancelView;
        mConfirmView = confirmView;
        mBackView = backView;
        mListener = listener;

        initView();
    }

    private void initView() {
        mCancelView.setOnClickListener(onClickLimitListener);
        mCancelViewId = mCancelView.getId();
        mConfirmView.setOnClickListener(onClickLimitListener);
        mConfirmViewId = mConfirmView.getId();
        mBackView.setOnClickListener(onClickLimitListener);
        mBackViewId = mBackView.getId();

        mOptionLayout.setVisibility(View.VISIBLE);

        radioSmall = mOptionLayout.findViewById(R.id.radio_mosaic_small);
        radioMiddle = mOptionLayout.findViewById(R.id.radio_mosaic_middle);
        radioBig = mOptionLayout.findViewById(R.id.radio_mosaic_big);
        OnClickLimitListener l = new OnClickLimitListener() {
            @Override
            public void onClickLimit(View v) {
                onCheckRadio(v);
            }
        };
        radioSmall.setOnClickListener(l);
        radioMiddle.setOnClickListener(l);
        radioBig.setOnClickListener(l);
        onCheckRadio(radioMiddle);
    }

    private void onCheckRadio(View v) {
        if (selectedRadio != null) {
            selectedRadio.setSelected(false);
        }
        selectedRadio = v;
        selectedRadio.setSelected(true);
        if (mosaicMask != null) {
            mosaicMask.setSize(mContext, getRadioSize());
        }
    }

    private int getRadioSize() {
        if (selectedRadio == null) {
            return 20;
        }
        int id = selectedRadio.getId();
        if (R.id.radio_mosaic_small == id) {
            return 20;
        } else if (R.id.radio_mosaic_middle == id) {
            return 30;
        } else if (R.id.radio_mosaic_big == id) {
            return 40;
        }
        return 20;
    }

    public void finish() {
        structHelper = null;
        mosaicMask = null;
        mListener.finish();
    }


    private final OnClickLimitListener onClickLimitListener = new OnClickLimitListener() {
        @Override
        public void onClickLimit(View v) {
            if (v.getId() == mCancelViewId) {
                if (mosaicMask != null) {
                    mosaicMask.touchStacks.clear();
                    mosaicMask.setActive(false);
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                    structHelper.dismissPaint();
                }
                finish();
            } else if (v.getId() == mConfirmViewId) {
                if (mosaicMask != null) {
                    if (structHelper != null) {
                        structHelper.saveMosaicStack();
                        structHelper.dismissPaint();
                    }
                    mosaicMask.setActive(false);
                }
                finish();
            } else if (v.getId() == mBackViewId) {
                if (mosaicMask != null) {
                    mosaicMask.onStackBack();
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                }
            }
        }
    };

    public interface IMosaicListener {

        void finish();
    }
}
