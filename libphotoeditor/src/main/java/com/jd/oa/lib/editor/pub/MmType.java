package com.jd.oa.lib.editor.pub;

/**
 * des: 跳转中类型
 * <EMAIL>
 * erp:zhoumin117
 *
 * @author： zhoumin6
 * @date： 2020 -08-31.20:36
 */
public class MmType {

    /**
     * 相册中可选择 媒体类型   也作为默认选择类型
     */
    public enum ALBUM {
        /**
         * 两者均可选
         */
        BOTH,
        /**
         * 只能选择图片，显示视频入口
         */
        IMAGE,
        /**
         * 只能选择视频，显示图片入口
         */
        VIDEO,
        /**
         * 只先选择图片，隐藏视频入口
         */
        IMAGE_ONLY_HIDE_VIDEO,

        /**
         * 只选择视频并隐藏图片入口
         */

        VIDEO_ONLY_HIDE_IMAGE;

        public boolean isImageOnly(){
            return this == IMAGE_ONLY_HIDE_VIDEO;
        }

        public boolean isVideoOnly(){
            return this == VIDEO_ONLY_HIDE_IMAGE;
        }
    }




    public static ALBUM getAlbumTypeByAllowType(ALLOW_TAKE_TYPE type){
        if (type == ALLOW_TAKE_TYPE.RECORD_VIDEO) {
            return ALBUM.VIDEO_ONLY_HIDE_IMAGE;
        } else if (type == ALLOW_TAKE_TYPE.TAKE_PHOTO) {
            return ALBUM.IMAGE_ONLY_HIDE_VIDEO;
        } else {
            return ALBUM.BOTH;
        }
    }

    /**
     * 通过int 值获取相册类型
     *
     * @param type type
     * @return the album type by int
     */
    public static ALBUM getAlbumTypeByInt(int type) {
        if (type == ALBUM.VIDEO.ordinal()) {
            return ALBUM.VIDEO;
        } else if (type == ALBUM.IMAGE.ordinal()) {
            return ALBUM.IMAGE;
        } else {
            return ALBUM.BOTH;
        }
    }

    /**
     * 进入MediaPicker 打开哪个界面
     */
    public enum OPEN {
        /**
         * 打开录制视频界面
         */
        RECORD_VIDEO,
        /**
         * 打开拍照界面
         */
        TAKE_PHOTO,
        /**
         * 打开相册界面
         */
        ALBUM
    }

    public static OPEN getOpenTypWithInt(int type){
        if (type == 1) {
            return OPEN.TAKE_PHOTO;
        } else if (type == 2) {
            return OPEN.RECORD_VIDEO;
        }
        return OPEN.ALBUM;

    }

    /**
     * 允许显示的类型
     */
    public enum ALLOW_TAKE_TYPE {
        /**
         * 录制视频
         */
        RECORD_VIDEO,
        /**
         * 拍照
         */
        TAKE_PHOTO,
        /**
         * 所有的
         */
        ALL
    }

    /**
     * 根据int 值获取打开类型
     *
     * @param type type
     * @return the open type by int
     */
    public static ALLOW_TAKE_TYPE getTakeTypeByAllowType(ALBUM type) {
        if (type == ALBUM.VIDEO||type==ALBUM.VIDEO_ONLY_HIDE_IMAGE) {
            return ALLOW_TAKE_TYPE.RECORD_VIDEO;
        } else if (type == ALBUM.IMAGE||type==ALBUM.IMAGE_ONLY_HIDE_VIDEO) {
            return ALLOW_TAKE_TYPE.TAKE_PHOTO;
        } else {
            return ALLOW_TAKE_TYPE.ALL;
        }
    }


    /**
     * Gets open type by album *
     *
     * @param type type
     * @return the open type by album
     */
    public static OPEN getOpenTypeByAlbum(ALBUM type) {
        if (type == ALBUM.VIDEO) {
            return OPEN.RECORD_VIDEO;
        } else if (type == ALBUM.IMAGE) {
            return OPEN.TAKE_PHOTO;
        } else {
            return OPEN.ALBUM;
        }
    }


    /**
     * 进入来源
     */
    public enum FROM_TYPE {
        /**
         * 相册
         */
        ALBUM,
        /**
         * 编辑
         */
        EDITOR,
        /**
         * 拍照
         */
        TAKE_PHOTO,
        /**
         * 录制视频
         */
        RECORD_VIDEO,
        /**
         * 其他
         */
        OTHER
    }

    /**
     * Gets open type by album *
     *
     * @param type type
     * @return the open type by album
     */
    public static OPEN getOpenTypeByEditFrom(FROM_TYPE type) {
        if (type == FROM_TYPE.TAKE_PHOTO) {
            return OPEN.TAKE_PHOTO;
        } else {
            return OPEN.ALBUM;
        }
    }

}
