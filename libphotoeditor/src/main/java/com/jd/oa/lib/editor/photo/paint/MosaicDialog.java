package com.jd.oa.lib.editor.photo.paint;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.jd.oa.lib.editor.base.OnClickLimitListener;
import com.jd.oa.lib.editor.photo.R;

public class MosaicDialog extends DialogFragment {

    private final OnClickLimitListener onClickLimitListener = new OnClickLimitListener() {
        @Override
        public void onClickLimit(View v) {
            int id = v.getId();
            if (R.id.iv_cancel == id) {
                try {
                    dismissAllowingStateLoss();
                } catch (Exception e) {
                }
                if (mosaicMask != null) {
                    mosaicMask.touchStacks.clear();
                    mosaicMask.setActive(false);
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                    structHelper.dismissPaint();
                }
            } else if (R.id.iv_confirm == id) {
                try {
                    dismissAllowingStateLoss();
                } catch (Exception e) {
                }
                if (mosaicMask != null) {
                    if (structHelper != null) {
                        structHelper.saveMosaicStack();
                        structHelper.dismissPaint();
                    }
                    mosaicMask.setActive(false);
                }
            } else if (R.id.ic_back == id) {
                if (mosaicMask != null) {
                    mosaicMask.onStackBack();
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                }
            }
        }
    };

    private MosaicMask mosaicMask;
    private PaintStructHelper structHelper;

    private View radioSmall,radioMiddle,radioBig;
    private View selectedRadio;

    public static MosaicDialog newInstance() {
        MosaicDialog mosaicDialog = new MosaicDialog();
        return mosaicDialog;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof PaintStructHelper) {
            structHelper = (PaintStructHelper) context;
            mosaicMask = structHelper.getMosaicStruct();
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        structHelper = null;
        mosaicMask = null;
    }

    /**
     * On create *
     *
     * @param savedInstanceState saved instance state
     */
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置dialog背景透明及无边框等
        setStyle(DialogFragment.STYLE_NORMAL, android.R.style.Theme_Translucent_NoTitleBar_Fullscreen);
    }


    /**
     * On create view view
     *
     * @param inflater           inflater
     * @param container          container
     * @param savedInstanceState saved instance state
     * @return the view
     */
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        setCancelable(true);
        Dialog d = getDialog();
        if (d != null) {
            d.setCanceledOnTouchOutside(false);
            d.setCancelable(false);
            Window window = d.getWindow();
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.gravity = Gravity.BOTTOM; //底部
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.windowAnimations = R.style.dialog_botton_anim;
            lp.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            window.setAttributes(lp);
        }
        return inflater.inflate(R.layout.mm_lay_mosaic, container, false);
    }



    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        view.findViewById(R.id.ic_back).setOnClickListener(onClickLimitListener);
        view.findViewById(R.id.iv_cancel).setOnClickListener(onClickLimitListener);
        view.findViewById(R.id.iv_confirm).setOnClickListener(onClickLimitListener);

        radioSmall = view.findViewById(R.id.radio_mosaic_small);
        radioMiddle = view.findViewById(R.id.radio_mosaic_middle);
        radioBig = view.findViewById(R.id.radio_mosaic_big);
        OnClickLimitListener l = new OnClickLimitListener() {
            @Override
            public void onClickLimit(View v) {
                onCheckRadio(v);
            }
        };
        radioSmall.setOnClickListener(l);
        radioMiddle.setOnClickListener(l);
        radioBig.setOnClickListener(l);
        onCheckRadio(radioMiddle);
    }

    private void onCheckRadio(View v) {
        if (selectedRadio != null) {
            selectedRadio.setSelected(false);
        }
        selectedRadio = v;
        selectedRadio.setSelected(true);
        if (mosaicMask != null) {
            mosaicMask.setSize(getContext(), getRadioSize());
        }
    }

    private int getRadioSize() {
        if (selectedRadio == null) {
            return 10;
        }
        int id = selectedRadio.getId();
        if (R.id.radio_mosaic_small == id) {
            return 10;
        } else if (R.id.radio_mosaic_middle == id) {
            return 30;
        } else if (R.id.radio_mosaic_big == id) {
            return 50;
        }
        return 10;
    }

}
