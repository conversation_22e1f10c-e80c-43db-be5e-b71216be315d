package com.jd.oa.lib.editor.photo.paste.view;

import android.view.View;

import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.lib.editor.pub.data.ReBean;

/**
 * <AUTHOR> zhoumin
 * Email: <EMAIL>
 * @date : 2018/8/11
 * Time: 下午2:10
 * Description:
 */
public interface Listener {


    /**
     * 移除回调
     *
     * @param viewType view type
     */
    void removed(IViewType viewType);


    /**
     * 双击 文字回传
     *
     * @param view   view
     * @param reBean reBean
     * @param sBean  sBean
     */
    void doubleType(IViewType view, ReBean reBean, StyleBean sBean);

    /**
     * 设置为当前选中状态
     *
     * @param view   view
     * @param reBean re bean
     * @param sBean  s bean
     */
    void setLastView(IViewType view, ReBean reBean, StyleBean sBean);

    /**
     * Touch no view boolean
     *
     * @return the boolean
     */
    boolean isFontEditing();

    View getMaskLayer();
}
