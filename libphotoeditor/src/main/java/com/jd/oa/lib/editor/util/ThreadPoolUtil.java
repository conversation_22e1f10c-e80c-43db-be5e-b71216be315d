package com.jd.oa.lib.editor.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolUtil {

    /**
     * 超时时间
     */
    private static final int KEEP_ALIVE_TIME = 30;


    /**
     * 创建默认线程池
     *
     * @return 线程池
     */
    public static ExecutorService createDiscardPool(int corePoolSize,
                                                    int maximumPoolSize) {
        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new ThreadPoolExecutor.DiscardOldestPolicy());
    }

    /**
     * 创建默认线程池
     *
     * @return 线程池
     */
    public static ExecutorService createCallerRunsPool(int corePoolSize,
                                                   int maximumPoolSize) {
        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}


