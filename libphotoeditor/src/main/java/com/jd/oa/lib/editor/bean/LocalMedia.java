package com.jd.oa.lib.editor.bean;

import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.jd.oa.lib.editor.photo.clip.CutImageType;
import com.jd.oa.lib.editor.photo.paint.PaintStruct;
import com.jd.oa.lib.editor.photo.paste.PasteBean;
import com.jd.oa.lib.editor.pub.data.ReBean;
import com.jd.oa.lib.editor.pub.data.ReGroup;
import com.jd.oa.lib.editor.util.FileUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Stack;


/**
 * Local media
 * 图片数据模型
 * <AUTHOR>
 * @date
 */
public class LocalMedia implements Parcelable {

    /**
     * 图片贴纸ID                  "0"为未使用过贴纸  非"0"为使用过为贴纸ID，
     */
    public static final String MM_DECAL_ID = "ImageStrickId";
    /**
     * 图片编辑时滤镜名称           "0"为未使用滤镜    非"0"为滤镜名称
     */
    public static final String MM_EDIT_FILTER_ID = "ImageEditFilterId";
    /**
     * 图片拍照时滤镜名称           "0"为未使用滤镜    非"0"为滤镜名称
     */
    public static final String MM_TAKE_FILTER_ID = "ImageTakePhotoFilterId";
    /**
     * 图片是否裁剪过               "0"为未裁剪过     非"0"为编辑过
     */
    public static final String MM_IMAGE_IS_CROP = "ImageIsCrop";
    /**
     * 图片道具id                  "0"为未使用道具    非"0"为道具id
     */
    public static final String MM_IMAGE_PROP_ID = "ImagePropId";
    /**
     * 图片拍照时是否用美颜          "0"为未使用美颜    非"0"为使用
     */
    public static final String MM_TAKE_IS_MAKEUP = "ImageTakePhotoIsMakup";

    /**
     * 视频道具id                  "0"为未使用道具    非"0"为道具id
     */
    public static final String MM_VIDEO_PROP_ID = "VideoPropId";
    /**
     * 视频编辑时滤镜名称           "0"为未使用滤镜    非"0"为滤镜名称
     */
    public static final String MM_EDIT_VIDEO_FILTER_ID = "VideoEditFilterId";
    /**
     * 视频录制时滤镜名称           "0"为未使用滤镜    非"0"为滤镜名称
     */
    public static final String MM_ROCORD_FILTER_ID = "VideoRecordFilterId";
    /**
     * 视频音乐id                  "0"为未使用音乐   非"0"为音乐名称
     */
    public static final String MM_MUSIC_ID = "VideoMusicId";
    /**
     * 视频是否编辑过封面            "0"为未编辑过     非"0"编辑过
     */
    public static final String MM_VIDEO_EDIT_COVER = "VideoIsEditCover";
    /**
     * 视频是否剪辑过               "0"为未裁剪过     非"0"为编辑过
     */
    public static final String MM_VIDEO_CROP = "VideoIsEditCrop";
    /**
     * 视频录制时是否用美颜          "0"为未使用美颜    非"0"为使用
     */
    public static final String MM_RECORD_MAKEUP = "VideoRecordIsMakup";
    /**
     * 数据来源          "1"为相册   "2"为拍照
     */
    public static final String MM_FROM_TYPE = "FromType";
    /**
     * 拍照来源           0   无（想当于来源于相册） 3:4 / 9:16 / 1:1 /  16:9
     */
    public static final String MM_TAKE_RATE = "TakeRate";
    /**
     * 图片编辑文字数据    0 无 ；  格式：字体id1_样式id1, 字体id2_样式id2   样例 9_12,7_4   使用了字段，但未选择字体及样式：0_0
     */
    public static final String MM_IMAGE_FONT_ID = "ImageFontId";

    /**
     * 图片视频uri
     */
    private Uri uri;

    /**
     * 图片视频路径
     */
    private String path;
    /**
     * 图片编辑生成的临时文件 完成编辑以后替换到  path 回传数据
     */
    private String tempPath;
    /**
     * 视频时长
     */
    private long duration;
    /**
     * 视频时间未获取到，再次检查
     */
    private boolean isCheckedDuration = false;
    /**
     * -1 添加按钮 1,图片；2，视频
     */
    private int type;
    /**
     * 是否勾选
     */
    private boolean isPicked;
    /**
     * 是否可以勾选
     */
    private boolean isChecked;
    /**
     * 是否高亮展示
     */
    private boolean isHighlight;
    /**
     * Date
     */
    private long date;
    /**
     * Position
     */
    private int position;
    /**
     * Picture type
     */
    private String pictureType;
    /**
     * Width
     */
    private int width;
    /**
     * Height
     */
    private int height;
    /**
     * Is picture
     */
    private boolean isPicture;
    /**
     * Extra map
     */
    private HashMap<String, String> extraMap = new HashMap<String, String>();

    /**
     * 音乐ID，无音乐 0 有音乐 音乐ID
     */
    private String musicId = "0";
    /**
     * 滤镜信息，无滤镜 0 有滤镜 滤镜名称
     */
    private String filterInfo = "0";
    /**
     * 贴纸ID 无贴纸0 有贴纸 贴纸ID
     */
    private String pasterInfo = "0";
    /**
     * 裁剪过 1 未剪辑过0
     */
    private String cutInfo = "0";
    /**
     * 封面地址  无地址null  埋点根据是否有内容自行统计
     */
    private String coverPath;


    /**
     * Local media
     */
    public LocalMedia() {
        initDefaultMap();
    }


    /**
     * Local media
     *
     * @param path      path
     * @param isPicked  is picked
     * @param isPicture is picture
     */
    public LocalMedia(String path, boolean isPicked, boolean isPicture) {
        this.path = path;
        this.isPicked = isPicked;
        this.isPicture = isPicture;
        initDefaultMap();
    }

    /**
     * Local media
     *
     * @param path        path
     * @param duration    duration
     * @param isPicked    is picked
     * @param pictureType picture type
     */
    public LocalMedia(String path, long duration, boolean isPicked, String pictureType) {
        this.path = path;
        this.duration = duration;
        this.isPicked = isPicked;
        this.pictureType = pictureType;
        initDefaultMap();
    }

    /**
     * Local media
     *
     * @param path        path
     * @param duration    duration
     * @param date        date
     * @param pictureType picture type
     * @param width       width
     * @param height      height
     */
    public LocalMedia(String path, long duration, long date, String pictureType, int width, int height) {
        this.path = path;
        this.duration = duration;
        this.date = date;
        this.pictureType = pictureType;
        this.width = width;
        this.height = height;
        initDefaultMap();
    }

    /**
     * Local media
     *
     * @param path        path
     * @param duration    duration
     * @param date        date
     * @param pictureType picture type
     * @param width       width
     * @param height      height
     */
    public LocalMedia(Uri uri, String path, long duration, long date, String pictureType, int width, int height) {
        this.uri = uri;
        this.path = path;
        this.duration = duration;
        this.date = date;
        this.pictureType = pictureType;
        this.width = width;
        this.height = height;
        initDefaultMap();
    }

    /**
     * Init default map
     */
    private void initDefaultMap() {
        extraMap.put(MM_DECAL_ID, "0");
        extraMap.put(MM_EDIT_FILTER_ID, "0");
        extraMap.put(MM_TAKE_FILTER_ID, "0");
        extraMap.put(MM_IMAGE_IS_CROP, "0");
        extraMap.put(MM_IMAGE_PROP_ID, "0");
        extraMap.put(MM_TAKE_IS_MAKEUP, "0");
        extraMap.put(MM_VIDEO_PROP_ID, "0");
        extraMap.put(MM_EDIT_VIDEO_FILTER_ID, "0");
        extraMap.put(MM_ROCORD_FILTER_ID, "0");
        extraMap.put(MM_MUSIC_ID, "0");
        extraMap.put(MM_VIDEO_EDIT_COVER, "0");
        extraMap.put(MM_VIDEO_CROP, "0");
        extraMap.put(MM_RECORD_MAKEUP, "0");
        extraMap.put(MM_FROM_TYPE, "1");
        extraMap.put(MM_TAKE_RATE, "0");
        extraMap.put(MM_IMAGE_FONT_ID, "0");
    }

    /**
     * To string string
     *
     * @return the string
     */
    @Override
    public String toString() {
        return "LocalMedia{" +
//                "path='" + path + '\'' +
//                ", duration=" + duration +
//                ", type=" + type +
//                ", isPicked=" + isPicked +
//                ", isChecked=" + isChecked +
//                ", isHighlight=" + isHighlight +
//                ", date=" + date +
//                ", position=" + position +
//                ", num=" + num +
//                ", mimeType=" + mimeType +
//                ", pictureType='" + pictureType + '\'' +
//                ", width=" + width +
//                ", musicId=" + musicId +
//                ", filterInfo=" + filterInfo +
//                ", pasterInfo=" + pasterInfo +
//                ", cutInfo=" + cutInfo +
                ", extramap=" + extraMap.toString() +
                '}';
    }


    /**
     * Equals boolean
     *
     * @param o o
     * @return the boolean
     */
    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        } else if (o instanceof LocalMedia) {
            LocalMedia l = (LocalMedia) o;
            if (TextUtils.isEmpty(this.path)) {
                return false;
            }
            return this.path.equals(l.path);
        }
        return super.equals(o);
    }

    /**
     * Gets uri *
     *
     * @return the path
     */
    public Uri getUri() {
        return uri;
    }

    public String getPathEx(){
        if(FileUtils.isValidUri(uri)){
            return uri.toString();
        }
        return path;
    }

    public String getUriEx(){
        if(FileUtils.isValidUri(uri)){
            return uri.toString();
        }
        return "file://"+path;
    }

    /**
     * Gets path *
     *
     * @return the path
     */
    public void setUri(Uri uri) {
        this.uri = uri;
    }

    /**
     * Gets path *
     *
     * @return the path
     */
    public String getPath() {
        return path;
    }

    /**
     * Sets path *
     *
     * @param path path
     */
    public void setPath(String path) {
        this.path = path;
    }

    /**
     * Gets cover path *
     *
     * @return the cover path
     */
    public String getCoverPath() {
        return coverPath;
    }

    /**
     * Sets cover path *
     *
     * @param coverPath cover path
     */
    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    /**
     * Gets duration *
     *
     * @return the duration
     */
    public long getDuration() {
        return duration;
    }

    /**
     * Sets duration *
     *
     * @param duration duration
     */
    public void setDuration(long duration) {
        this.duration = duration;
    }


    /**
     * Is checked duration boolean
     *
     * @return the boolean
     */
    public boolean isCheckedDuration() {
        return isCheckedDuration;
    }

    /**
     * Sets checked duration *
     *
     * @param checkedDuration checked duration
     */
    public void setCheckedDuration(boolean checkedDuration) {
        isCheckedDuration = checkedDuration;
    }

    /**
     * Gets type *
     *
     * @return the type
     */
    public int getType() {
        return type;
    }

    /**
     * Sets type *
     *
     * @param type type
     */
    public void setType(int type) {
        this.type = type;
    }

    /**
     * Is picked boolean
     *
     * @return the boolean
     */
    public boolean isPicked() {
        return isPicked;
    }

    /**
     * Sets picked *
     *
     * @param picked picked
     */
    public void setPicked(boolean picked) {
        isPicked = picked;
    }

    /**
     * Is highlight boolean
     *
     * @return the boolean
     */
    public boolean isHighlight() {
        return isHighlight;
    }

    /**
     * Sets highlight *
     *
     * @param highlight highlight
     */
    public void setHighlight(boolean highlight) {
        isHighlight = highlight;
    }

    /**
     * Gets date *
     *
     * @return the date
     */
    public long getDate() {
        return date;
    }

    /**
     * Sets date *
     *
     * @param date date
     */
    public void setDate(long date) {
        this.date = date;
    }

    /**
     * Is picture boolean
     *
     * @return the boolean
     */
    public boolean isPicture() {
        return isPicture;
    }

    /**
     * Add extra *
     *
     * @param key   key
     * @param value value
     */
    public synchronized void addExtra(String key, String value) {
        if (extraMap == null) {
            extraMap = new HashMap<String, String>();
        }
        extraMap.put(key, value);
    }


    /**
     * Gets extra map *
     *
     * @return the extra map
     */
    public synchronized HashMap<String, String> getExtraMap() {
        return extraMap;
    }

    /**
     * Gets filter info *
     *
     * @return the filter info
     */
    public String getFilterInfo() {
        return filterInfo;
    }

    /**
     * Sets filter info *
     *
     * @param finfo finfo
     */
    public void setFilterInfo(String finfo) {
        if (TextUtils.isEmpty(finfo)) {
            return;
        }
        if ("0".equals(finfo)) {
            return;
        }
        filterInfo = finfo;
    }

    /**
     * Gets paster info *
     *
     * @return the paster info
     */
    public String getPasterInfo() {
        return pasterInfo;
    }

    /**
     * Sets paster info *
     *
     * @param pInfo p info
     */
    public void setPasterInfo(String pInfo) {
        if (TextUtils.isEmpty(pInfo)) {
            return;
        }
        if ("0".equals(pasterInfo)) {
            this.pasterInfo = pInfo;
        } else {
            this.pasterInfo += "," + pInfo;
        }

    }

    /**
     * Gets cut info *
     *
     * @return the cut info
     */
    public String getCutInfo() {
        return cutInfo;
    }

    /**
     * Sets cut info *
     *
     * @param cutInfo cut info
     */
    public void setCutInfo(String cutInfo) {
        this.cutInfo = cutInfo;
    }


    /**
     * Gets picture type *
     *
     * @return the picture type
     */
    public String getPictureType() {
        if (TextUtils.isEmpty(pictureType)) {
            pictureType = "image/jpeg";
        }
        return pictureType;
    }

    /**
     * Sets picture type *
     *
     * @param pictureType picture type
     */
    public void setPictureType(String pictureType) {
        this.pictureType = pictureType;
    }

    /**
     * Is checked boolean
     *
     * @return the boolean
     */
    public boolean isChecked() {
        return isChecked;
    }

    /**
     * Sets checked *
     *
     * @param checked checked
     */
    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    /**
     * Gets position *
     *
     * @return the position
     */
    public int getPosition() {
        return position;
    }

    /**
     * Sets position *
     *
     * @param position position
     */
    public void setPosition(int position) {
        this.position = position;
    }

    /**
     * Gets width *
     *
     * @return the width
     */
    public int getWidth() {
        return width;
    }

    /**
     * Sets width *
     *
     * @param width width
     */
    public void setWidth(int width) {
        this.width = width;
    }

    /**
     * Gets height *
     *
     * @return the height
     */
    public int getHeight() {
        return height;
    }

    /**
     * Sets height *
     *
     * @param height height
     */
    public void setHeight(int height) {
        this.height = height;
    }

    /**
     * Sets picture *
     *
     * @param isPicture is picture
     */
    public void setPicture(boolean isPicture) {
        this.isPicture = isPicture;
    }

    /**
     * Gets edit path *
     *
     * @return the edit path
     */
    public String getEditPath() {
        if (TextUtils.isEmpty(tempPath)) {
            return getPathEx();
        }
        return tempPath;
    }

    /**
     * Gets temp path *
     *
     * @return the temp path
     */
    public String getTempPath() {
        return tempPath;
    }

    /**
     * Sets temp path *
     *
     * @param tempPath temp path
     */
    public void setTempPath(String tempPath) {
        this.tempPath = tempPath;
    }

    /**
     * Gets music id *
     *
     * @return the music id
     */
    public String getMusicId() {
        return musicId;
    }

    /**
     * Sets music id *
     *
     * @param musicId music id
     */
    public void setMusicId(String musicId) {
        this.musicId = musicId;
    }

    /**
     * Sets extra map *
     *
     * @param extraMap extra map
     */
    public synchronized void setExtraMap(HashMap<String, String> extraMap) {
        this.extraMap = extraMap;
    }


    /**
     * 埋点记录上传数据 - 滤镜
     */
    public ReBean rFilterBean;

    /**
     * Gets filter path *
     *
     * @return the filter path
     */
    public String getFilterPath() {
        String path = rFilterBean != null ? rFilterBean.getPath() : null;
        if (FileUtils.isFileExist(this)) {
            return path;
        }
        return null;
    }

    /**
     * 滤镜强度
     */
    public float fIntensity = 1.0f;


    /**
     * 埋点记录上传数据 - 道具
     */
    public ReBean rPropBean;
    /**
     * R prop grop
     */
    public ReGroup rPropGrop;
    /**
     * 记录贴纸与文字数据 - 文字，包含字体与样式
     */
    public ArrayList<PasteBean> rPasterBean;

    public Stack<PaintStruct.TouchStack> paintTouchStack;

    public Stack<PaintStruct.TouchStack> mosaicTouchStack;


    public int canvasWidth,canvasHeight;

    /**
     * 记录当前图片的最后裁剪比例
     */
    public CutImageType mCutImageType =null;


    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.path);
        dest.writeString(this.uri!=null?this.uri.toString():"");
        dest.writeString(this.tempPath);
        dest.writeLong(this.duration);
        dest.writeByte(this.isCheckedDuration ? (byte) 1 : (byte) 0);
        dest.writeInt(this.type);
        dest.writeByte(this.isPicked ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isChecked ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isHighlight ? (byte) 1 : (byte) 0);
        dest.writeLong(this.date);
        dest.writeInt(this.position);
        dest.writeString(this.pictureType);
        dest.writeInt(this.width);
        dest.writeInt(this.height);
        dest.writeByte(this.isPicture ? (byte) 1 : (byte) 0);
        dest.writeSerializable(this.extraMap);
        dest.writeString(this.musicId);
        dest.writeString(this.filterInfo);
        dest.writeString(this.pasterInfo);
        dest.writeString(this.cutInfo);
        dest.writeString(this.coverPath);
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    public void readFromParcel(Parcel source) {
        this.path = source.readString();
        this.uri = Uri.parse(source.readString());
        this.tempPath = source.readString();
        this.duration = source.readLong();
        this.isCheckedDuration = source.readByte() != 0;
        this.type = source.readInt();
        this.isPicked = source.readByte() != 0;
        this.isChecked = source.readByte() != 0;
        this.isHighlight = source.readByte() != 0;
        this.date = source.readLong();
        this.position = source.readInt();
        this.pictureType = source.readString();
        this.width = source.readInt();
        this.height = source.readInt();
        this.isPicture = source.readByte() != 0;
        this.extraMap = (HashMap<String, String>) source.readSerializable();
        this.musicId = source.readString();
        this.filterInfo = source.readString();
        this.pasterInfo = source.readString();
        this.cutInfo = source.readString();
        this.coverPath = source.readString();
    }

    /**
     * Local media
     *
     * @param in in
     */
    protected LocalMedia(Parcel in) {
        this.path = in.readString();
        this.uri = Uri.parse(in.readString());
        this.tempPath = in.readString();
        this.duration = in.readLong();
        this.isCheckedDuration = in.readByte() != 0;
        this.type = in.readInt();
        this.isPicked = in.readByte() != 0;
        this.isChecked = in.readByte() != 0;
        this.isHighlight = in.readByte() != 0;
        this.date = in.readLong();
        this.position = in.readInt();
        this.pictureType = in.readString();
        this.width = in.readInt();
        this.height = in.readInt();
        this.isPicture = in.readByte() != 0;
        this.extraMap = (HashMap<String, String>) in.readSerializable();
        this.musicId = in.readString();
        this.filterInfo = in.readString();
        this.pasterInfo = in.readString();
        this.cutInfo = in.readString();
        this.coverPath = in.readString();
    }

    /**
     * CREATOR
     */
    public static final Creator<LocalMedia> CREATOR = new Creator<LocalMedia>() {
        @Override
        public LocalMedia createFromParcel(Parcel source) {
            return new LocalMedia(source);
        }

        @Override
        public LocalMedia[] newArray(int size) {
            return new LocalMedia[size];
        }
    };
}
