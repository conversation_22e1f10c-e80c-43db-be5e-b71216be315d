package com.jd.oa.lib.editor.pub;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * des: 普通Size类
 * <EMAIL>
 * erp:zhoumin117
 * [@author]： zhoumin
 * [@date]： 2021/6/17.12:49 AM
 */
public class Size implements Parcelable {
    /**
     * Width
     */
    public int width;
    /**
     * Height
     */
    public int height;

    /**
     * Size
     *
     * @param width  width
     * @param height height
     */
    public Size(int width, int height) {
        this.width = width;
        this.height = height;
    }

    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.width);
        dest.writeInt(this.height);
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    public void readFromParcel(Parcel source) {
        this.width = source.readInt();
        this.height = source.readInt();
    }

    /**
     * Size
     *
     * @param in in
     */
    protected Size(Parcel in) {
        this.width = in.readInt();
        this.height = in.readInt();
    }

    /**
     * CREATOR
     */
    public static final Creator<Size> CREATOR = new Creator<Size>() {
        @Override
        public Size createFromParcel(Parcel source) {
            return new Size(source);
        }

        @Override
        public Size[] newArray(int size) {
            return new Size[size];
        }
    };
}
