package com.jd.oa.lib.editor.photo.paint;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.lib.editor.util.ColorUtil;

import java.util.List;

/*
 * Time: 2024/6/5
 * Author: qudongshi
 * Description:
 */
public class PaintHelper {

    private PaintMask paintMask;
    private PaintStructHelper structHelper;

    private RecyclerView mRecyclerView;
    private View mCancelView;
    private View mConfirmView;
    private View mBackView;
    private int mCancelViewId;
    private int mConfirmViewId;
    private int mBackViewId;

    private Context mContext;
    private IPaintListener mListener;

    private PaintHelper() {
    }

    public PaintHelper(Context context, RecyclerView recyclerView, View cancelView, View confirmView, View backView, IPaintListener listener) {
        if (context instanceof PaintStructHelper) {
            structHelper = (PaintStructHelper) context;
            paintMask = structHelper.getPaintStruct();
        } else {
            return;
        }

        mContext = context;
        mRecyclerView = recyclerView;
        mCancelView = cancelView;
        mConfirmView = confirmView;
        mBackView = backView;
        mListener = listener;

        initView();
    }

    private void initView() {
        mCancelView.setOnClickListener(onClickLimitListener);
        mCancelViewId = mCancelView.getId();
        mConfirmView.setOnClickListener(onClickLimitListener);
        mConfirmViewId = mConfirmView.getId();
        mBackView.setOnClickListener(onClickLimitListener);
        mBackViewId = mBackView.getId();
        if (mContext == null) {
            return;
        }
        mRecyclerView.setVisibility(View.VISIBLE);

        List<String> datas = ColorUtil.getPaintColors();
        ColorAdapter adapter = new ColorAdapter(datas, mContext);
        mRecyclerView.setAdapter(adapter);
        if (paintMask != null) {
            paintMask.setPaintColor(datas.get(2));
        }

        adapter.setOnItemClickListener(new ColorAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(String color) {
                if (paintMask != null) {
                    paintMask.setPaintColor(color);
                }
            }
        });
    }

    public void finish() {
        structHelper = null;
        paintMask = null;
        mListener.finish();
    }

    private final View.OnClickListener onClickLimitListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (v.getId() == mCancelViewId) {
                if (paintMask != null) {
                    paintMask.touchStacks.clear();
                    paintMask.setActive(false);
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                    structHelper.dismissPaint();
                }
                finish();
            } else if (v.getId() == mConfirmViewId) {
                if (paintMask != null) {
                    if (structHelper != null) {
                        structHelper.savePaintStack();
                        structHelper.dismissPaint();
                    }
                    paintMask.setActive(false);
                }
                finish();
            } else if (v.getId() == mBackViewId) {
                if (paintMask != null) {
                    paintMask.onStackBack();
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                }
            }
        }
    };

    public interface IPaintListener {

        void finish();
    }
}
