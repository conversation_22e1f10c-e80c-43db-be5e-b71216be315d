package com.jd.oa.lib.editor.base;

import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.lib.editor.photo.clip.CutImageType;
import com.jd.oa.lib.editor.pub.Constants;
import com.jd.oa.lib.editor.pub.Size;

import java.util.ArrayList;
import java.util.List;

/**
 * 各界面入口参数父类（包含开关）
 *
 * <AUTHOR>
 * @date 2021 /10/18
 */
public class BaseParam implements Parcelable {

    /**
     * 是否显示贴纸
     */
    public boolean showDecals = true;
    /**
     * 是否显示滤镜
     */
    public boolean showFilter = true;

    /**
     * 是否显示跟我拍
     */
    public boolean showFollowTake;

    /**
     * 是否显示道具
     */
    public boolean showProps;
    /**
     * 渠道
     */
    public String mChannel = "";
    /**
     * 是否显示视频剪辑中的音乐
     */
    public boolean showMusic = true;

    /**
     * 是否显示视频剪辑中的封面
     */
    public boolean showCover = true;

    /**
     * 是否显示图片编辑中的文字
     */
    public boolean showFont = true;

    /**
     * 是否显示图片编辑中的裁剪
     */
    public boolean showCutImage = true;

    /**
     * 是否显示视频编辑中的剪辑
     */
    public boolean showClipVideo = true;

    /**
     * 视频录制分辨率信息 默认720P
     */
//    public Resolution resolution = Resolution.P720;


    /**
     * 是否打开 视频生成后保存到相册
     */
    public boolean isSaveVideoToAlbum = false;

    /**
     * 是否打开 图片生成后保存到相册
     */
    public boolean isSavePhotoToAlbum = false;

    /**
     * 商品三级分类
     */
    public String cate3Id = "";

    /**
     * 是否需要编辑
     */
    public boolean needEditorMedia;

    /**
     * 是否自动打开跟我拍UI
     */
    public boolean openFollowTakeUi;
    /**
     * 是否使用系统相册
     */
    public boolean isUseSystemAlbum = false;

    /**
     * 图片编辑支持的裁剪类型
     */
    public List<CutImageType> cutImageTypes;

    /**
     * 图片裁剪 操作线框是否可拖动
     */
    public boolean isImageCutRectDrag = true;
    /**
     * 直接裁剪图片
     */
    public boolean needImageCut = false;
    /**
     * 显示 图片裁剪 旋转
     */
    public boolean showCutRotate = true;
    /**
     * 显示 图片裁剪 重置
     */
    public boolean showCutReset = true;

    /**
     * 图片裁剪限制最小像素 宽
     */
    public Size cutMinImageSize;
    /**
     * 自定义图片裁剪比例
     */
    public Size cutCustomImageRate;
    /**
     * 保存为png图片，默认false（jpg）
     */
    public Bitmap.CompressFormat bitmapFormat = Bitmap.CompressFormat.PNG;
    /**
     * 视频文件最大限制 单位MB
     */
    public int maxVideoFileSizeMB = 0;

    /**
     * 上传图片文件最大限制 单位MB. 默认不压缩
     */
    public int maxUploadImageFileSizeMB = 0;

    /**
     * 开起HDR支持
     */
    public boolean enableHdr = true;

    /**
     * 录制最小时长
     */
    public long videoRecordMinTime = Constants.VIDEO_RECORD_MIN_TIME;
    /**
     * 录制最大时长
     */
    public long videoRecordMaxTime = Constants.VIDEO_RECORD_MAX_TIME;
    /**
     * 相册列表图片后缀过滤类型
     */
    public String picSuffixTypes;
    /**
     * 相册列表图片MineType过滤类型
     */
    public String picMineTypes;

    /**
     * 相册列表视频后缀过滤类型
     */
    public String videoSuffixTypes;
    /**
     * 相册列表视频MineType过滤类型
     */
    public String videoMineTypes;

    /**
     * 是否从编辑界面过来的 处理图片压缩逻辑时，图片编辑添加时根据此变量判断是否需要压缩。
     */
    public boolean isEnteredEdit = false;

    /**
     * 内部显示和读取/返回给外部的字段，是否支持Uri;
     */
    public boolean isUseUri = true;

    /**
     * 要返回给外部的Uri，拷贝到沙盒，返回path
     * 对于外部不支持Uri的情况，可以打开此开关
     */
    public boolean isCopy2Sandbox = false;

    /**
     * 相册列表数据是否分布加载。用于移动配置降级，对于统一控件相册模块的移动配置，主站统一控件传递过来开关。
     * 处理主站客诉（用户相册近19万张图片视频文件，加载解析图片耗费30+秒），
     */
    public boolean isAlbumLoadByStep = true;

    /**
     * 是否支持一键成片
     */
    public boolean isSupportEasyClip = false;

    /**
     * 是否原图功能
     */
    public boolean isSupportOriginal = false;


    /**
     * 拍照/录制时是否显示相册按钮
     */
    public boolean isShowAlbum = true;


    /**
     * 流程定义
     */
    public int processType;
    /**
     * 暗黑模式UI
     */
    public boolean darkMode = false;

    /**
     * 引导语
     */
    public String guideMessage;

    /**
     * 是否显示画笔
     */
    public boolean showPaint = true;
    /**
     * 是否显示马赛克编辑入口
     */
    public boolean showMosaic = true;
    /**
     * 是否显示美颜入口
     */
    public boolean showBeauty;
    /**
     * 线上调试开关
     * 1，图片显示失败，上传错误信息和图片；
     */
    public boolean isOnlineDebug = false;

    /**
     * Base param
     */
    public BaseParam() {
        videoRecordMinTime = 3;
        videoRecordMaxTime = 15;
    }

    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte(this.showDecals ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showFilter ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showFollowTake ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showProps ? (byte) 1 : (byte) 0);
        dest.writeString(this.mChannel);
        dest.writeByte(this.showMusic ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showCover ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showFont ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showCutImage ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showClipVideo ? (byte) 1 : (byte) 0);
//        dest.writeInt(this.resolution == null ? -1 : this.resolution.ordinal());
        dest.writeByte(this.isSaveVideoToAlbum ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isSavePhotoToAlbum ? (byte) 1 : (byte) 0);
        dest.writeString(this.cate3Id);
        dest.writeByte(this.needEditorMedia ? (byte) 1 : (byte) 0);
        dest.writeByte(this.openFollowTakeUi ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isUseSystemAlbum ? (byte) 1 : (byte) 0);
        dest.writeList(this.cutImageTypes);
        dest.writeByte(this.isImageCutRectDrag ? (byte) 1 : (byte) 0);
        dest.writeByte(this.needImageCut ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showCutRotate ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showCutReset ? (byte) 1 : (byte) 0);
        dest.writeInt(this.bitmapFormat == null ? -1 : this.bitmapFormat.ordinal());
        dest.writeParcelable(this.cutMinImageSize, flags);
        dest.writeParcelable(this.cutCustomImageRate, flags);
        dest.writeInt(this.maxVideoFileSizeMB);
        dest.writeInt(this.maxUploadImageFileSizeMB);
        dest.writeByte(this.enableHdr ? (byte) 1 : (byte) 0);
        dest.writeLong(this.videoRecordMinTime);
        dest.writeLong(this.videoRecordMaxTime);
        dest.writeByte(this.isEnteredEdit ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isAlbumLoadByStep ? (byte) 1 : (byte) 0);
        dest.writeString(this.picSuffixTypes);
        dest.writeString(this.picMineTypes);
        dest.writeByte(this.isUseUri ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isCopy2Sandbox ? (byte) 1 : (byte) 0);
        dest.writeString(this.videoSuffixTypes);
        dest.writeString(this.videoMineTypes);
        dest.writeByte(this.isSupportEasyClip ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isSupportOriginal ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isShowAlbum ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isOnlineDebug ? (byte) 1 : (byte) 0);
        dest.writeInt(this.processType);
        dest.writeByte(this.darkMode ? (byte) 1 : (byte) 0);
        dest.writeString(this.guideMessage);
        dest.writeByte(this.showPaint ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showMosaic ? (byte) 1 : (byte) 0);
        dest.writeByte(this.showBeauty ? (byte) 1 : (byte) 0);
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    public void readFromParcel(Parcel source) {
        this.showDecals = source.readByte() != 0;
        this.showFilter = source.readByte() != 0;
        this.showFollowTake = source.readByte() != 0;
        this.showProps = source.readByte() != 0;
        this.mChannel = source.readString();
        this.showMusic = source.readByte() != 0;
        this.showCover = source.readByte() != 0;
        this.showFont = source.readByte() != 0;
        this.showCutImage = source.readByte() != 0;
        this.showClipVideo = source.readByte() != 0;
//        int tmpResolution = source.readInt();
//        this.resolution = tmpResolution == -1 ? null : Resolution.values()[tmpResolution];
        this.isSaveVideoToAlbum = source.readByte() != 0;
        this.isSavePhotoToAlbum = source.readByte() != 0;
        this.cate3Id = source.readString();
        this.needEditorMedia = source.readByte() != 0;
        this.openFollowTakeUi = source.readByte() != 0;
        this.isUseSystemAlbum = source.readByte() != 0;
        this.cutImageTypes = new ArrayList<CutImageType>();
        source.readList(this.cutImageTypes, CutImageType.class.getClassLoader());
        this.isImageCutRectDrag = source.readByte() != 0;
        this.needImageCut = source.readByte() != 0;
        this.showCutRotate = source.readByte() != 0;
        this.showCutReset = source.readByte() != 0;
        int tmpBitmapFormat = source.readInt();
        this.bitmapFormat = tmpBitmapFormat == -1 ? null : Bitmap.CompressFormat.values()[tmpBitmapFormat];
        this.cutMinImageSize = source.readParcelable(Size.class.getClassLoader());
        this.cutCustomImageRate = source.readParcelable(Size.class.getClassLoader());
        this.maxVideoFileSizeMB = source.readInt();
        this.maxUploadImageFileSizeMB = source.readInt();
        this.enableHdr = source.readByte() != 0;
        this.videoRecordMinTime = source.readLong();
        this.videoRecordMaxTime = source.readLong();
        this.isEnteredEdit = source.readByte() != 0;
        this.isAlbumLoadByStep = source.readByte() != 0;
        this.picSuffixTypes = source.readString();
        this.picMineTypes = source.readString();
        this.isUseUri = source.readByte() != 0;
        this.isCopy2Sandbox = source.readByte() != 0;
        this.videoSuffixTypes = source.readString();
        this.videoMineTypes = source.readString();
        this.isSupportEasyClip = source.readByte() != 0;
        this.isSupportOriginal = source.readByte() != 0;
        this.isShowAlbum = source.readByte() != 0;
        this.isOnlineDebug = source.readByte() != 0;
        this.processType = source.readInt();
        this.darkMode = source.readByte() != 0;
        this.guideMessage = source.readString();
        this.showPaint = source.readByte() != 0;
        this.showMosaic = source.readByte() != 0;
        this.showBeauty = source.readByte() != 0;
    }

    /**
     * Base param
     *
     * @param in in
     */
    protected BaseParam(Parcel in) {
        this.showDecals = in.readByte() != 0;
        this.showFilter = in.readByte() != 0;
        this.showFollowTake = in.readByte() != 0;
        this.showProps = in.readByte() != 0;
        this.mChannel = in.readString();
        this.showMusic = in.readByte() != 0;
        this.showCover = in.readByte() != 0;
        this.showFont = in.readByte() != 0;
        this.showCutImage = in.readByte() != 0;
        this.showClipVideo = in.readByte() != 0;
//        int tmpResolution = in.readInt();
//        this.resolution = tmpResolution == -1 ? null : Resolution.values()[tmpResolution];
        this.isSaveVideoToAlbum = in.readByte() != 0;
        this.isSavePhotoToAlbum = in.readByte() != 0;
        this.cate3Id = in.readString();
        this.needEditorMedia = in.readByte() != 0;
        this.openFollowTakeUi = in.readByte() != 0;
        this.isUseSystemAlbum = in.readByte() != 0;
        this.cutImageTypes = new ArrayList<CutImageType>();
        in.readList(this.cutImageTypes, CutImageType.class.getClassLoader());
        this.isImageCutRectDrag = in.readByte() != 0;
        this.needImageCut = in.readByte() != 0;
        this.showCutRotate = in.readByte() != 0;
        this.showCutReset = in.readByte() != 0;
        int tmpBitmapFormat = in.readInt();
        this.bitmapFormat = tmpBitmapFormat == -1 ? null : Bitmap.CompressFormat.values()[tmpBitmapFormat];
        this.cutMinImageSize = in.readParcelable(Size.class.getClassLoader());
        this.cutCustomImageRate = in.readParcelable(Size.class.getClassLoader());
        this.maxVideoFileSizeMB = in.readInt();
        this.maxUploadImageFileSizeMB = in.readInt();
        this.enableHdr = in.readByte() != 0;
        this.videoRecordMinTime = in.readLong();
        this.videoRecordMaxTime = in.readLong();
        this.isEnteredEdit = in.readByte() != 0;
        this.isAlbumLoadByStep = in.readByte() != 0;
        this.picSuffixTypes = in.readString();
        this.picMineTypes = in.readString();
        this.isUseUri = in.readByte() != 0;
        this.isCopy2Sandbox = in.readByte() != 0;
        this.videoSuffixTypes = in.readString();
        this.videoMineTypes = in.readString();
        this.isSupportEasyClip = in.readByte() != 0;
        this.isSupportOriginal = in.readByte() != 0;
        this.isShowAlbum = in.readByte() != 0;
        this.isOnlineDebug = in.readByte() != 0;
        this.processType = in.readInt();
        this.darkMode = in.readByte() != 0;
        this.guideMessage = in.readString();
        this.showPaint = in.readByte() != 0;
        this.showMosaic = in.readByte() != 0;
        this.showBeauty = in.readByte() != 0;
    }

    /**
     * CREATOR
     */
    public static final Creator<BaseParam> CREATOR = new Creator<BaseParam>() {
        @Override
        public BaseParam createFromParcel(Parcel source) {
            return new BaseParam(source);
        }

        @Override
        public BaseParam[] newArray(int size) {
            return new BaseParam[size];
        }
    };

    /**
     * 是否是新评价业务
     *
     * @return
     */
    public boolean isNewEvaluate() {
        return 1 == processType;
    }
}
