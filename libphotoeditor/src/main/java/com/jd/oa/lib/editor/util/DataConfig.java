package com.jd.oa.lib.editor.util;

/*
 * Time: 2024/5/31
 * Author: qudongshi
 * Description:
 */
public class DataConfig {

    private final static DataConfig INSTANCE = new DataConfig();

    public static DataConfig getInstance() {
        return INSTANCE;
    }

    /**
     * file 判断是否使用FD
     */
    private boolean isUseFileFd = true;

    public boolean isUseFileFd() {
        return isUseFileFd;
    }

    public void setUseFileFd(boolean useFileFd) {
        isUseFileFd = useFileFd;
    }


}
