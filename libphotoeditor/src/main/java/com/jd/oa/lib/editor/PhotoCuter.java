package com.jd.oa.lib.editor;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.jd.oa.lib.editor.base.BaseBuilder;
import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.photo.CutPhotoParam;
import com.jd.oa.lib.editor.photo.JdmmPhotoClipActivity;
import com.jd.oa.lib.editor.pub.MmType;

import java.util.ArrayList;

/**
 * des:
 *
 * <AUTHOR>
 * @date 2021/12/21 : 12:04 下午
 * erp        zhoumin117
 * mail       <EMAIL>
 */
public class PhotoCuter {
    /**
     * Builder media picker builder
     *
     * @return the media picker builder
     */
    public static PhotoCutBuilder builder() {
        return new PhotoCutBuilder();
    }
    /*
    * 图片编辑 builder
     */
    public static class PhotoCutBuilder extends BaseBuilder<PhotoCutBuilder, CutPhotoParam> {

        /**
         * Photo editer builder
         */
        public PhotoCutBuilder() {
            super(new CutPhotoParam());
            super.builder = this;
        }



        /**
         * From type photo editer builder
         *
         * @param type type
         * @return the photo editer builder
         */
        public PhotoCutBuilder fromType(MmType.FROM_TYPE type) {
            mParam.mFrom = type;
            return this;
        }


        /**
         * 需要编辑的图片
         *
         * @param photoPath photo path
         * @return photo editer builder
         */
        public PhotoCutBuilder editPhotos(@NonNull ArrayList<LocalMedia> photoPath) {
            mParam.photoPath = photoPath;
            return this;
        }


        /**
         * 启动
         *
         * @param activity    activity
         * @param requestCode request code
         */
        public void start(Activity activity, int requestCode) {
            if (activity == null || activity.isFinishing()) {
                return;
            }
            JdmmPhotoClipActivity.startActivityForResult(activity, mParam, requestCode);
        }

    }

}
