package com.jd.oa.lib.editor.photo;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Grid layout manager wrap
 *
 * <AUTHOR>
 * @date 2020 /10/20 解决list数据越界问题
 */
public class GridLayoutManagerWrap extends GridLayoutManager {

    /**
     * Grid layout manager wrap
     *
     * @param context   context
     * @param spanCount span count
     */
    public GridLayoutManagerWrap(Context context, int spanCount) {
        super(context, spanCount);
    }

    /**
     * Grid layout manager wrap
     *
     * @param context      context
     * @param attrs        attrs
     * @param defStyleAttr def style attr
     * @param defStyleRes  def style res
     */
    public GridLayoutManagerWrap(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    /**
     * On layout children *
     *
     * @param recycler recycler
     * @param state    state
     */
    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        // 在这里通过try来捕获这个异常即可
        try {
            super.onLayoutChildren(recycler, state);
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
        }
    }
}