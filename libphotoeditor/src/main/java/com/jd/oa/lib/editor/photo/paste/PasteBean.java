package com.jd.oa.lib.editor.photo.paste;

import android.graphics.Point;

import com.jd.oa.lib.editor.photo.paste.fonts.data.StyleBean;
import com.jd.oa.lib.editor.pub.data.ReBean;

/**
 * des: 文字功能  埋点上报
 *
 * <AUTHOR>
 * @date 2021 /9/22 : 8:56 下午
 * erp        zhou min117 mail       <EMAIL>
 */
public class PasteBean {

    /**
     * 类型
     */
    public ReBean.TYPE type;
    /**
     * 内容
     */
    public String content;
    /**
     * 中心点
     */
    public Point centerPoint;
    /**
     * 角度
     */
    public float degree;
    /**
     * 缩放
     */
    public float scale;

    /**
     * styleBean
     */
    public StyleBean stylebean;
    /**
     * Bean
     */
    public ReBean bean;


    /**
     * Paste bean
     */
    private PasteBean() {
    }

    /**
     * Paste bean
     *
     * @param type  type
     * @param rBean  bean
     * @param sBean sBean
     */
    public PasteBean(ReBean.TYPE type, ReBean rBean, StyleBean sBean) {
        this.type = type;
        this.stylebean = sBean;
        this.bean = rBean;
    }


    /**
     * To string string
     *
     * @return the string
     */
    @Override
    public String toString() {
        return "PasteBean{" +
                "content='" + content + '\'' +
                ", degree=" + degree +
                ", scale=" + scale +
                '}';
    }


}
