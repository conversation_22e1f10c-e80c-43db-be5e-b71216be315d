package com.jd.oa.lib.editor.pub;

/**
 * des: 媒体选择常量
 * <EMAIL>
 * erp:niuzhikui1
 * [@author]： niuzhikui
 * [@date]： 2020/8/6.11:00 AM
 */
public class Constants {
    public static String TEMP_LOG = "";

    public static void addLog(String temp) {
        TEMP_LOG = TEMP_LOG + "\n" + temp;
    }

    /**
     * 跳转相册的参数
     */
    public static final String KEY_PARAM = "KEY_PARAM";
    public static final String KEY_RESULT = "KEY_RESULT";
    public static final int VAL_RESULT_FINISH = -2;

    public static final String KEY_AUTO_CLOSE = "KEY_AUTO_CLOSE";

    public static final String KEY_PARAM_JSON = "KEY_PARAM_JSON";
    /**
     * 不保存到相册的时候，在编辑页未做任何修改，不需要删除录制的文件，直接回传录制路径
     */
    public static final String KEY_EDIT_VIDEO = "KEY_EDIT_VIDEO";

    /**
     * 是否勾选原图
     */
    public static final String KEY_ORIGINAL = "KEY_ORIGINAL";

    /**
     * 编辑视频确认
     */
    public final static int REQUEST_FINISH_EDIT_VIDEO = 58701;
    /**
     * 请求 MEDIA CODE
     */
    public static final int REQUEST_CODE_MEDIA = 58702;
    /**
     * 打开相机
     */
    public static final int OPEN_CAMERA_REQUEST_CODE = 58702;
    /**
     * 点击网格图片进入预览页
     */
    public static final int PICTURE_CLICK_REQUEST_CODE = 58703;
    /**
     * 打开美化图片请求
     */
    public static final int OPEN_PIC_BEAUTIFY_REQUEST_CODE = 58704;
    /**
     * 系统相册添加图片
     */
    public static final int SYSTEM_ALBUM_RESULT = 58705;

    /**
     * 相册列数
     */
    public static final int PHOTO_COLUMN_COUNT = 4;

    /**
     * 可以选择的媒体数量
     */
    public static int MEDIA_SELECT_COUNT = 9;

    /**
     * 相册网格页所选媒体的集合
     */
    public static final String SELECT_MEDIAS = "selectMedias";

    /**
     * 相册网格页所选媒体位置
     */
    public static final String SELECT_MEDIA_POSITION = "selectMediaPosition";
    public static final String MIN_PX_LIMIT_SIZE = "MIN_PX_LIMIT_SIZE";

    /**
     * 间隔大小
     */
    public static final int PHOTO_COLUMN_SPACE_SIZE = 4;

    /**
     * video展示的最小时常
     */
    public static long VIDEO_MIN_DURATION = 3;
    /**
     * video展示的最大时常
     */
    public static long VIDEO_MAX_DURATION = 180;

    /**
     * 录制最小时常
     */
    public static final long VIDEO_RECORD_MIN_TIME = 3;
    /**
     * 录制最大时常
     */
    public static final long VIDEO_RECORD_MAX_TIME = 15;

    /**
     * 拍照或录视频返回成功还是失败的状态
     */
    public static final String VIDEO_RECORD_RETURN_STATE = "videoRecordReturnState";
    /**
     * 拍照或录视频返回类型-录视频
     */
    public static final int VIDEO_RECORD_RETURN_VIDEO_TYPE = 100;
//    /**
//     * 录视频成功返回的路径
//     */
//    public static final String VIDEO_RECORD_RETURN_VIDEO_PATH = "videoPath";
    /**
     * 拍照或录视频返回类型-拍照
     */
    public static final int VIDEO_RECORD_RETURN_IMAGE_TYPE = 101;

    /**
     * 可选择媒体的数量
     */
    public final static String CAN_SELECT_MEDIA_COUNT = "canSelectedMediaCount";


    /**
     * 相册没有拍照和录视频功能
     */
    public static final int ACTION_NO_CAMERA_AND_VIDEO = 0;

    /**
     * 相册有拍照和录视频功能
     */
    public static final int ACTION_CAMERA_AND_VIDEO = 1;


    /**
     * 磨皮
     */
    public static final String KEY_SMOOTH_STRENGTH = "SmoothStrength";
    /**
     * 美白
     */
    public static final String KEY_WHITE_STRENGTH = "WhiteStrength";
    /**
     * 大眼
     */
    public static final String KEY_EYE_ENLARGE_STRENGTH = "EyeEnlargeStrength";


    public static final String ACTION_EDITOR_PHOTO_CLOSE = "action.editor.photo.close";

    public static final String ACTION_EDITOR_PHOTO_CALL_BACK = "action.editor.photo.callback";

    public static final String ACTION_EDITOR_PHOTO_READY = "action.editor.photo.ready";

    public static final String MODE_EDITOR_NORMAL = "MODE_NORMAL";//无选中

    public static final String MODE_EDITOR_PAIN = "MODE_PAIN"; //画笔

    public static final String MODE_EDITOR_MOSAIC = "MODE_MOSAIC"; //马赛克

    public static final String MODE_EDITOR_FONT = "MODE_FONT"; //文字

    public static final String MODE_FONT_STYLE_COLOR = "MODE_FONT_STYLE_COLOR"; //文字颜色
    public static final String MODE_FONT_STYLE_BACKGROUND = "MODE_FONT_STYLE_BACKGROUND"; //文字背景


}
