package com.jd.oa.lib.editor.util;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.ParcelFileDescriptor;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.AppBase;
import com.jd.oa.lib.editor.bean.LocalMedia;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/*
 * Time: 2024/5/31
 * Author: qudongshi
 * Description:
 */
public class FileUtils {

    /**
     * TAG
     */
    final static String TAG = "FileUtils";

    /**
     * The constant FIT_SHOES_DIR_NAME. 试鞋目录名称
     */
    public static final String MEDIAMAKER_DIR_NAME = "MediaMaker";

    /**
     * 保存图片
     *
     * @param bt   要保存的图片
     * @param path 如果传入路径则使用目标文件 如果没有路径则使用默认路径
     * @return string
     */
    public static String saveBitmap(Bitmap bt, String path, Bitmap.CompressFormat format) {
        if (bt != null) {
            File file;
            if (!isFilePathExist(path)) {
                String newPath = "";
                if (TextUtils.isEmpty(path)) {
                    StringBuffer name = new StringBuffer();
                    name.append(System.currentTimeMillis())
                            .append(getSuffixWithBitmapFormat(format));
                    newPath = getMediaPath("make", name.toString());
                } else {
                    newPath = path;
                }

                file = new File(newPath);
            } else {
                file = new File(path);
            }
            try {
                FileOutputStream out = new FileOutputStream(file);
                bt.compress(format == null ? Bitmap.CompressFormat.JPEG : format, 98, out);
                out.flush();
                out.close();
                return file.getAbsolutePath();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 通过文件路径判断文件是否存在
     *
     * @param path 文件的绝对路径
     * @return 返回true或者false boolean
     */
    public static boolean isFilePathExist(String path) {
        //FIXME 无法确定当前路径是否绝对是uri，添加兜底逻辑
        return isExtraTypeFileExist(path);
    }

    /**
     * 无法确定path类型
     * 判断文件路径是否是uri，是uri按照uri判断
     * 不是uri 按绝对路径判断
     *
     * @param path 文件的绝对路径
     * @return 返回true或者false boolean
     */
    public static boolean isExtraTypeFileExist(String path) {
        if (isUri(path)) {
            Uri uri = Uri.parse(path);
            return isUriFileExist(AppBase.getAppContext(), uri);
        }
        return isFileAbsolutePathExist(path);
    }

    public static String getSuffixWithBitmapFormat(Bitmap.CompressFormat format) {
        if (Bitmap.CompressFormat.PNG == format) {
            return ".png";
        } else if (Bitmap.CompressFormat.WEBP == format) {
            return ".webp";
        }
        return ".jpg";
    }


    /**
     * 判断uri是否有效
     *
     * @param uri
     * @return
     */
    public static boolean isValidUri(Uri uri) {
        if (uri != null && !TextUtils.isEmpty(uri.toString()) && isUri(uri.toString())) {
            return true;
        }
        return false;
    }

    /**
     * 判断文件是否存在
     *
     * @param context
     * @param uri
     * @return
     */
    private static boolean isUriFileExistFD(Context context, Uri uri) {
        if (context == null) {
            return true;
        }

        if (!isValidUri(uri)) {
            return false;
        }
        ContentResolver resolver = context.getContentResolver();
        ParcelFileDescriptor parcelFileDescriptor = null;
        try {
            // 尝试打开URI指向的文件描述符
            parcelFileDescriptor = resolver.openFileDescriptor(uri, "r");
            if (parcelFileDescriptor != null) {
                // 文件描述符存在，因此URI可能是有效的
                return true;
            }
        } catch (Exception e) {
            return false;
        } finally {
            try {
                if (parcelFileDescriptor != null) {
                    parcelFileDescriptor.close();
                }
            } catch (Exception e) {
            }
        }
        return false;
    }

    /**
     * 判断路径是否为uri
     *
     * @param path
     * @return
     */
    public static boolean isUri(String path) {
        if (!TextUtils.isEmpty(path) && path.startsWith("content://media/")) {
            return true;
        }
        return false;
    }

    /**
     * 判断文件是否存在
     *
     * @param context
     * @param uri
     * @return
     */
    private static boolean isUriFileExist(Context context, Uri uri) {
        if (DataConfig.getInstance().isUseFileFd()) {
            return isUriFileExistFD(context, uri);
        }

        if (context == null) {
            return true;
        }

        if (!isValidUri(uri)) {
            return false;
        }
        ContentResolver resolver = context.getContentResolver();
        InputStream stream = null;
        try {
            stream = resolver.openInputStream(uri);
            if (stream != null && stream.available() > 0) {
                return true;
            }
        } catch (Exception e) {
            return false;
        } finally {
            try {
                if (stream != null) {
                    stream.close();
                }
            } catch (Exception e) {
            }
        }
        return false;
    }

    /**
     * 通过文件绝对路径判断文件是否存在
     *
     * @param path 文件的绝对路径
     * @return 返回true或者false boolean
     */
    private static boolean isFileAbsolutePathExist(String path) {
        if (TextUtils.isEmpty(path)) {
            return false;
        }
        File file = new File(path);
        return file.exists();
    }

    /**
     * 获取meidamaker 存储根目录
     *
     * @param path     path
     * @param fileName file name
     * @return media path
     */
    public static String getMediaPath(String path, String fileName) {
        String absoluteDir = getAbsoluteDir(MEDIAMAKER_DIR_NAME);
        if (TextUtils.isEmpty(absoluteDir)) {
            return "";
        }
        String p = absoluteDir + File.separator + path;
        File f = new File(p);
        if (!f.exists() && !f.mkdirs()) {
            return absoluteDir + File.separator + fileName;
        }
        //设置文件不被相册检测
        setNoMedia(p);
        return p + File.separator + fileName;
    }

    /**
     * Gets absolute dir *
     *
     * @param dirName dir name
     * @return the absolute dir
     */
    public static String getAbsoluteDir(String dirName) {
        StringBuffer root;

        try {
            root = getJdExternalAbsoluteFileDir();
        } catch (Exception e) {
            root = getJdInternalAbsoluteFileDir();
        }
        if (root == null) {
            return null;
        }

        root.append(File.separator);
        root.append(dirName);
        return root.toString();
    }

    /**
     * 获取主app外部存储路径
     *
     * @return 返回StringBuffer类型数据 ，便于快捷设置其他目录
     */
    public static StringBuffer getJdExternalAbsoluteFileDir() {
        Context context = AppBase.getAppContext();
        if (context == null) {
            return null;
        }
        StringBuffer root = new StringBuffer();
        root.append(context.getExternalFilesDir(null).getAbsolutePath());
        root.append("/jingdong");
        root.append("/file");
        return root;
    }

    /**
     * Gets jd internal absolute file dir.
     *
     * @return the jd internal absolute file dir
     */
    public static StringBuffer getJdInternalAbsoluteFileDir() {
        StringBuffer root = new StringBuffer();
        root.append(AppBase.getAppContext().getFilesDir().getAbsolutePath());
        root.append("/jingdong");
        root.append("/file");
        return root;
    }

    /**
     * 设置文件不被相册检测
     *
     * @param path path
     */
    private static void setNoMedia(String path) {
        File noMedia = new File(path, ".nomedia");
        if (!noMedia.exists()) {
            try {
                noMedia.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 通过 localMedia 判断文件是否存储
     * 有限判断绝对路径，绝对路径下文件不存在，再去判断 uri
     *
     * @param localMedia 媒体信息
     * @return 返回true或者false boolean
     */
    public static boolean isFileExist(LocalMedia localMedia) {
        if (isFilePathExist(localMedia.getPath())) {
            return true;
        }
        return isUriFileExist(AppBase.getAppContext(), localMedia.getUri());
    }

    public static boolean isFileExist(String filePath) {
        if (isFilePathExist(filePath)) {
            return true;
        }
        return false;
    }

    /**
     * 删除文件
     *
     * @param fileName file name
     * @return boolean
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.exists() && !file.isDirectory()) {
            return file.delete();
        }
        return false;
    }

    /**
     * Delete dir.
     *
     * @param dir the dir
     */
    public static void deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (int i = 0; i < children.length; ++i) {
                    deleteDir(new File(dir, children[i]));
                }
            }
        }

        dir.delete();
    }

    /**
     * 往相册插入图片
     *
     * @param context  context
     * @param filePath file path
     * @param isVideo  is video
     * @return the string
     */
    public static String insertToAlbum(Context context, String filePath, boolean isVideo) {
        long time = System.currentTimeMillis();
        String albumPath = null;
        if (!TextUtils.isEmpty(filePath) && null != context) {
            String start = FileUtils.getAbsoluteDir(FileUtils.MEDIAMAKER_DIR_NAME);
            if (filePath.toLowerCase().startsWith(start.toLowerCase())) {
                try {
//                  目录是沙盒中的目录,需要拷贝或往相册插入
//                  大于等于target30 使用ContentResolver插入后广播刷新
                    String s = insertToAlbumOverQ(context, filePath, isVideo);
                    albumPath = TextUtils.isEmpty(s) ? filePath : s;
                } catch (Throwable e) {
                    e.printStackTrace();
                }
            }
        }
        Log.e(TAG, "insertToAlbum cost " + (System.currentTimeMillis() - time) + " ms");
        return albumPath;
    }

    /**
     * 通过 ContentResolver 往相册插数据方式
     *
     * @param context  context
     * @param filePath file path
     * @param isVideo  is video
     * @return the string
     * @throws Throwable throwable
     */
    private static String insertToAlbumOverQ(Context context, String filePath, boolean isVideo) throws Throwable {

        ContentResolver cr = context.getContentResolver();
        ContentValues values = new ContentValues();
        values.put(MediaStore.Images.Media.MIME_TYPE, isVideo ? "video/mp4" : "image/jpeg");
        if (isVideo) {
            values.put(MediaStore.Images.Media.IS_PENDING, 1);
        }
        Uri collection = isVideo ? MediaStore.Video.Media.EXTERNAL_CONTENT_URI : MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
        Uri fileUri = cr.insert(collection, values);

        if(fileUri == null){
            return null;
        }
        ParcelFileDescriptor descriptor = context.getContentResolver().openFileDescriptor(fileUri, "w");
        FileOutputStream out = new FileOutputStream(descriptor.getFileDescriptor());
        FileInputStream inputStream = new FileInputStream(new File(filePath));
        byte[] buf = new byte[4096];
        while (true) {
            int sz = inputStream.read(buf);
            if (sz <= 0) {
                break;
            }
            out.write(buf, 0, sz);
        }
        inputStream.close();
        out.close();
        if (isVideo) {
            values.put(MediaStore.Images.Media.IS_PENDING, 0);
        }
        cr.update(fileUri, values, null, null);
        String path = uriToPath(context, fileUri, isVideo);
        return path;
    }

    /**
     * uri 中获取 绝对路径
     *
     * @param context context
     * @param uri     uri
     * @param isvideo isvideo
     * @return string
     */
    private static String uriToPath(Context context, Uri uri, boolean isvideo) {
        String path = null;
        if (Build.VERSION.SDK_INT >= 19) {
            //根据不同的uri进行不同的解析
            if (DocumentsContract.isDocumentUri(context, uri)) {
                String docId = DocumentsContract.getDocumentId(uri);
                if ("com.android.providers.media.documents".equals(uri.getAuthority())) {
                    String id = docId.split(":")[1];
                    String selection = MediaStore.Images.Media._ID + "=" + id;
                    path = getImagePath(context, isvideo ? MediaStore.Video.Media.EXTERNAL_CONTENT_URI : MediaStore.Images.Media.EXTERNAL_CONTENT_URI, selection);
                } else if ("com.android.providers.downloads.documents".equals(uri.getAuthority())) {
                    Uri contentUri = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), Long.valueOf(docId));
                    path = getImagePath(context, contentUri, null);
                }
            } else if ("content".equalsIgnoreCase(uri.getScheme())) {
                path = getImagePath(context, uri, null);
            } else if ("file".equalsIgnoreCase(uri.getScheme())) {
                path = uri.getPath();
            } else {
                path = getImagePath(context, uri, null);
            }
        } else {
            path = getImagePath(context, uri, null);
        }
        return path;
    }

    /**
     * 通过 uri 查询 绝对路径
     *
     * @param context   context
     * @param uri       uri
     * @param selection selection
     * @return image path
     */
    private static String getImagePath(Context context, Uri uri, String selection) {
        String path = "";
        Cursor cursor = context.getContentResolver().query(uri, null, selection, null, null);
        if (cursor != null) {
            int count = cursor.getCount();
            if (count > 0) {
                cursor.moveToFirst();
                do {
                    try {
                        path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA));
                        break;
                    } catch (Exception e) {
                        continue;
                    }
                } while (cursor.moveToNext());
                cursor.close();
            }
        }
        return path;
    }
}


