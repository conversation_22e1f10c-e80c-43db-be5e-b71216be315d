package com.jd.oa.lib.editor.photo.paint;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.CornerPathEffect;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;

import com.jd.oa.utils.UnitUtils;

import java.util.Iterator;
import java.util.Stack;

public class MosaicMask extends PaintStruct {

    private final Paint paint = new Paint();
    private Bitmap coverLayer;
    private int size = 50;

    public MosaicMask(Bitmap coverLayer) {
        try {
            this.coverLayer = getMosaic(coverLayer);
        } catch (Exception e) {
            this.coverLayer = coverLayer;
        }
        paint.setStyle(Paint.Style.STROKE);
        paint.setAntiAlias(true);
        paint.setStrokeJoin(Paint.Join.ROUND);
        paint.setStrokeCap(Paint.Cap.ROUND);
        paint.setPathEffect(new CornerPathEffect(10));
    }

    @Override
    protected PaintStruct.TouchStack createTouchInfo() {
        return new PaintStruct.TouchStack(new Path(), 0, size);
    }

    @Override
    public void doDraw(Canvas canvas) {
        try {
            Bitmap mosaicLayer = getMosaicLayer();
            if (mosaicLayer != null) {
                canvas.drawBitmap(mosaicLayer, 0, 0, paint);
                mosaicLayer.recycle();
            }
        } catch (Exception e) {
        } catch (Error error) {
        }
    }

    public void addStack() {
        if (mListener != null) {
            mListener.addStackCallback();
        }
    }

    @Override
    public void popStack() {
        if (mListener != null) {
            mListener.popStackCallback();
        }
    }

    @Override
    protected void changeCache(Matrix matrix, float scale) {
        super.changeCache(matrix, scale);
        try {
            if (coverLayer != null) {
                coverLayer = Bitmap.createScaledBitmap(coverLayer, (int) (coverLayer.getWidth() * scale),
                        (int) (coverLayer.getHeight() * scale), true);
            }
        } catch (Exception e) {
        }
    }

    public void setSize(Context context, int size) {
        this.size = UnitUtils.dip2px(context, size);
    }

    private Bitmap getMosaicLayer() {
        if (coverLayer == null || coverLayer.isRecycled()) {
            return null;
        }
        int measuredWidth = coverLayer.getWidth();
        int measuredHeight = coverLayer.getHeight();

        Bitmap bmMosaicLayer = Bitmap.createBitmap(measuredWidth, measuredHeight,
                Bitmap.Config.ARGB_8888);

        Bitmap bmTouchLayer = Bitmap.createBitmap(measuredWidth, measuredHeight,
                Bitmap.Config.ARGB_4444);

        Canvas canvas = new Canvas(bmTouchLayer);
        paint.setStyle(Paint.Style.STROKE);
        drawPath(canvas, super.cacheTouchStacks);
        drawPath(canvas, super.touchStacks);
        paint.setColor(Color.TRANSPARENT);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));

        canvas.setBitmap(bmMosaicLayer);
        canvas.drawARGB(0, 0, 0, 0);
        canvas.drawBitmap(coverLayer, 0, 0, null);

        paint.reset();
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_IN));
        canvas.drawBitmap(bmTouchLayer, 0, 0, paint);
        paint.setXfermode(null);
        canvas.save();
        bmTouchLayer.recycle();
        return bmMosaicLayer;
    }

    private void drawPath(Canvas canvas, Stack<TouchStack> touchStacks) {
        if (touchStacks == null) {
            return;
        }
        Iterator<TouchStack> iterator = touchStacks.iterator();
        while (iterator.hasNext()) {
            PaintStruct.TouchStack element = iterator.next();
            paint.setStrokeWidth(element.size);
            canvas.drawPath(element.path, paint);
        }
    }

    private Bitmap getMosaic(Bitmap bitmap) {
        if (bitmap == null || bitmap.isRecycled()) {
            return null;
        }
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int radius = 20;

        Bitmap mosaicBitmap = Bitmap.createBitmap(width, height,
                Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(mosaicBitmap);

        int horCount = (int) Math.ceil(width / (float) radius);
        int verCount = (int) Math.ceil(height / (float) radius);

        Paint paint = new Paint();
        paint.setAntiAlias(true);

        for (int horIndex = 0; horIndex < horCount; ++horIndex) {
            for (int verIndex = 0; verIndex < verCount; ++verIndex) {
                int l = radius * horIndex;
                int t = radius * verIndex;
                int r = l + radius;
                if (r > width) {
                    r = width;
                }
                int b = t + radius;
                if (b > height) {
                    b = height;
                }
                int color = bitmap.getPixel(l, t);
                Rect rect = new Rect(l, t, r, b);
                paint.setColor(color);
                canvas.drawRect(rect, paint);
            }
        }
        canvas.save();

        return mosaicBitmap;
    }

    private IPaintListener mListener;

    /**
     * 添加 IPaintListener
     *
     * @param listener
     */
    public void setListener(IPaintListener listener) {
        mListener = listener;
    }

}
