package com.jd.oa.lib.editor.photo.paste.fonts.data;


import android.graphics.PointF;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * des: 文字样式数据实体
 *
 * <AUTHOR>
 * @date 2021 /9/9 : 6:26 下午
 * erp        zhoumin117
 * mail       <EMAIL>
 */
public class StyleBean implements Parcelable {
    /**
     * Pic
     */
    public String pic;
    /**
     * Id
     */
    public String id;
    /**
     * F color
     */
    public String fColor;
    /**
     * E color
     */
    public String eColor = "";
    /**
     * E width
     */
    public double eWidth = 0;
    /**
     * B color
     */
    public String bColor = "";
    /**
     * S color
     */
    public String sColor = "";
    /**
     * S radius
     */
    public double sRadius = 0;
    /**
     * S offset
     */
    public PointF sOffset = new PointF(0f, 0f);

    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.pic);
        dest.writeString(this.id);
        dest.writeString(this.fColor);
        dest.writeString(this.eColor);
        dest.writeDouble(this.eWidth);
        dest.writeString(this.bColor);
        dest.writeString(this.sColor);
        dest.writeDouble(this.sRadius);
        dest.writeParcelable(this.sOffset, flags);
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    public void readFromParcel(Parcel source) {
        this.pic = source.readString();
        this.id = source.readString();
        this.fColor = source.readString();
        this.eColor = source.readString();
        this.eWidth = source.readDouble();
        this.bColor = source.readString();
        this.sColor = source.readString();
        this.sRadius = source.readDouble();
        this.sOffset = source.readParcelable(PointF.class.getClassLoader());
    }

    /**
     * Style bean
     */
    public StyleBean() {
    }

    /**
     * Style bean
     *
     * @param in in
     */
    protected StyleBean(Parcel in) {
        this.pic = in.readString();
        this.id = in.readString();
        this.fColor = in.readString();
        this.eColor = in.readString();
        this.eWidth = in.readDouble();
        this.bColor = in.readString();
        this.sColor = in.readString();
        this.sRadius = in.readDouble();
        this.sOffset = in.readParcelable(PointF.class.getClassLoader());
    }

    /**
     * CREATOR
     */
    public static final Creator<StyleBean> CREATOR = new Creator<StyleBean>() {
        @Override
        public StyleBean createFromParcel(Parcel source) {
            return new StyleBean(source);
        }

        @Override
        public StyleBean[] newArray(int size) {
            return new StyleBean[size];
        }
    };
}
