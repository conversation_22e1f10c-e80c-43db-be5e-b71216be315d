package com.jd.oa.lib.editor.photo;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.util.FileUtils;
import com.jd.oa.utils.ImageLoader;


import java.io.File;
import java.util.List;

/**
 * des: 已经选择图片列表适配器
 * <EMAIL>
 * erp:zhoumin117
 *
 * @author： zhoumin6
 * @date： 2020 -08-05.18:01
 */
public class PhotoAdapter extends RecyclerView.Adapter<PhotoAdapter.PhotoItemHolder> {

    /**
     * 布局解析器
     */
    private final LayoutInflater mLayoutInflater;
    /**
     * 当前索引
     */
    private int mIndex = 0;
    /**
     * 回调对象
     */
    private final Listener mListener;

    /**
     * Photo adapter
     *
     * @param mContext  m context
     * @param mListener m listener
     */
    public PhotoAdapter(Context mContext, @NonNull Listener mListener) {
        this.mListener = mListener;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    /**
     * 存储数据
     */
    private List<LocalMedia> mList;

    /**
     * 设置数据
     *
     * @param mList       m list
     * @param mLocalMedia m local media
     */
    public void setListData(List<LocalMedia> mList, LocalMedia mLocalMedia) {
        if (mList == null || mList.size() <= 0) {
            return;
        }
        this.mList = mList;
        notifyDataSetChanged();
        if (mLocalMedia == null) {
            mIndex = 0;
            itemClick(mList.get(mIndex), mIndex, true);
        } else {
            mIndex = 0;
            try {
                for (int i = 0; i < mList.size(); i++) {
                    LocalMedia item = mList.get(i);
                    if (item.getPath().equals(mLocalMedia.getPath())) {
                        mIndex = i;
                        break;
                    }
                }
            } catch (Throwable e) {
                e.printStackTrace();
            } finally {
                itemClick(mList.get(mIndex), mIndex, true);
            }
        }
    }

    /**
     * 设置数据
     *
     * @param position position
     */
    public void setPosition(int position) {
        if (position < 0 || position >= getItemCount()) {
            return;
        }
        int lastIndex = mIndex;
        notifyItemChanged(lastIndex, false);
        mIndex = position;
        notifyItemChanged(mIndex, false);
    }


    /**
     * On create view holder photo item holder
     *
     * @param parent   parent
     * @param viewType view type
     * @return the photo item holder
     */
    @NonNull
    @Override
    public PhotoItemHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = mLayoutInflater.inflate(R.layout.mm_edit_item, parent, false);
        return new PhotoItemHolder(itemView);
    }

    /**
     * The Last click time.
     */
    private long lastClickTime = 0;

    /**
     * On bind view holder *
     *
     * @param holder holder
     * @param po     po
     */
    @Override
    public void onBindViewHolder(@NonNull PhotoItemHolder holder, final int po) {
        final int position = holder.getAdapterPosition();
        if (mList == null || position >= getItemCount()) {
            return;
        }
        final LocalMedia item = mList.get(position);
        ImageLoader.loadFile(mLayoutInflater.getContext(), holder.imageView,item.getPath());
        holder.imageView.setBackgroundResource(position == mIndex ? R.drawable.mm_photo_b : android.R.color.transparent);
        holder.imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int maxTime = 500;
                if (Math.abs(System.currentTimeMillis() - lastClickTime) < maxTime) {
                    return;
                }
                lastClickTime = System.currentTimeMillis();
                if (mIndex == position || position >= getItemCount()) {
                    return;
                }
                notifyItemChanged(mIndex,false);
                mIndex = position;
                notifyItemChanged(mIndex,false);
                itemClick(item, mIndex, false);
            }
        });
        holder.mDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getItemCount() <= 1) {
                    if (mListener != null) {
                        mListener.deleteLastItem();
                    }
                } else {
                    if (mListener != null) {
                        mListener.deleteItem();
                    }
                    LocalMedia item = mList.remove(position);
                    if (item != null && !TextUtils.isEmpty(item.getTempPath())) {
                        FileUtils.deleteDir(new File(item.getTempPath()));
                        item.setTempPath(null);
                    }
                    if (position < mIndex) {
                        mIndex--;
                        if (mIndex < 0) {
                            mIndex = 0;
                        }
                    }
                    if (mIndex >= getItemCount()) {
                        mIndex = getItemCount() - 1;
                    }

                    LocalMedia path = mList.get(mIndex);
                    itemClick(path, mIndex, true);
                    notifyDataSetChanged();
                }
            }
        });
    }

    /**
     * 点击了哪个
     *
     * @param path         path
     * @param index        index
     * @param isNeedUpdate is need update
     */
    private void itemClick(LocalMedia path, int index, boolean isNeedUpdate) {
        if (mListener != null) {
            mListener.selectPhotoItem(path, index, isNeedUpdate);
        }
    }


    /**
     * Gets item count *
     *
     * @return the item count
     */
    @Override
    public int getItemCount() {
        return mList == null ? 0 : mList.size();
    }

    /**
     * Photo item holder
     */
    public static class PhotoItemHolder extends RecyclerView.ViewHolder {
        /**
         * 展示图片
         */
        ImageView imageView;

        /**
         * M delete
         */
        ImageView mDelete;

        /**
         * 构造
         *
         * @param itemView item view
         */
        public PhotoItemHolder(@NonNull View itemView) {
            super(itemView);
            imageView = itemView.findViewById(R.id.mImageView);
            mDelete = itemView.findViewById(R.id.mDelete);
        }
    }

    /**
     * 回调对象
     */
    interface Listener {
        /**
         * select photo item *
         *
         * @param item         item
         * @param position     position
         * @param isNeedUpdate is need update
         */
        void selectPhotoItem(LocalMedia item, int position, boolean isNeedUpdate);

        /**
         * 删除了一张图片
         */
        void deleteItem();

        /**
         * 删除最后一张图片
         *
         * @return the boolean
         */
        void deleteLastItem();
    }


}
