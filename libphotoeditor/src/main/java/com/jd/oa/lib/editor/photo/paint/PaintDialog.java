package com.jd.oa.lib.editor.photo.paint;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.lib.editor.photo.R;
import com.jd.oa.utils.DensityUtil;

import java.util.ArrayList;
import java.util.List;

public class PaintDialog extends DialogFragment {

    private final View.OnClickListener onClickLimitListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int id = v.getId();
            if (R.id.iv_cancel == id) {
                try {
                    dismissAllowingStateLoss();
                } catch (Exception e) {
                }

                if (paintMask != null) {
                    paintMask.touchStacks.clear();
                    paintMask.setActive(false);
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                    structHelper.dismissPaint();
                }
            } else if (R.id.iv_confirm == id) {
                try {
                    dismissAllowingStateLoss();
                } catch (Exception e) {
                }
                if (paintMask != null) {
                    if (structHelper != null) {
                        structHelper.savePaintStack();
                        structHelper.dismissPaint();
                    }
                    paintMask.setActive(false);
                }
            } else if (R.id.ic_back == id) {
                if (paintMask != null) {
                    paintMask.onStackBack();
                }
                if (structHelper != null) {
                    structHelper.invalidatePaint();
                }
            }
        }
    };


    private PaintMask paintMask;
    private PaintStructHelper structHelper;

    public static PaintDialog newInstance() {
        PaintDialog paintDialog = new PaintDialog();
        return paintDialog;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof PaintStructHelper) {
            structHelper = (PaintStructHelper) context;
            paintMask = structHelper.getPaintStruct();
        }
    }

    public void onDetach() {
        super.onDetach();
        structHelper = null;
        paintMask = null;
    }

    /**
     * On create *
     *
     * @param savedInstanceState saved instance state
     */
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置dialog背景透明及无边框等
        setStyle(DialogFragment.STYLE_NORMAL, android.R.style.Theme_Translucent_NoTitleBar_Fullscreen);
    }


    /**
     * On create view view
     *
     * @param inflater           inflater
     * @param container          container
     * @param savedInstanceState saved instance state
     * @return the view
     */
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        setCancelable(true);
        Dialog d = getDialog();
        if (d != null) {
            d.setCanceledOnTouchOutside(false);
            d.setCancelable(false);
            Window window = d.getWindow();
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.gravity = Gravity.BOTTOM; //底部
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            lp.windowAnimations = R.style.dialog_botton_anim;
            lp.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            window.setAttributes(lp);
        }
        return inflater.inflate(R.layout.mm_lay_paint, container, false);
    }


    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        view.findViewById(R.id.ic_back).setOnClickListener(onClickLimitListener);
        view.findViewById(R.id.iv_cancel).setOnClickListener(onClickLimitListener);
        view.findViewById(R.id.iv_confirm).setOnClickListener(onClickLimitListener);

        RecyclerView recyclerView = view.findViewById(R.id.recyclerView);
        Context context = view.getContext();
        if (context == null) {
            return;
        }
        recyclerView.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        recyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent.getChildLayoutPosition(view) != 0) {
                    outRect.left = DensityUtil.dp2px(context, 15.0f);
                }
            }
        });

        List<String> datas = new ArrayList<>();
        datas.add("#FFFFFF");
        datas.add("#000000");
        datas.add("#FF3D4D");
        datas.add("#FF860D");
        datas.add("#FFCC00");
        datas.add("#6EDA01");
        datas.add("#05C5EB");
        datas.add("#1988FA");
        datas.add("#7142FF");
        ColorAdapter adapter = new ColorAdapter(datas, context);
        recyclerView.setAdapter(adapter);
        if (paintMask != null) {
            paintMask.setPaintColor(datas.get(2));
        }

        adapter.setOnItemClickListener(new ColorAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(String color) {
                if (paintMask != null) {
                    paintMask.setPaintColor(color);
                }
            }
        });
    }

}
