package com.jd.oa.lib.editor.pub.data;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * des: 道具分组
 *
 * @author： zhoumin6
 * @date： 2020 /12/10.3:06 PM
 */
public class ReGroup implements Parcelable {

    /**
     * Re group
     */
    public ReGroup() {
    }

    /**
     * Re group
     *
     * @param id   id
     * @param name name
     */
    public ReGroup(String id, String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * id : sdfsdf
     * tag_name : 道具
     */
    public String id;
    /**
     * Name
     */
    public String name;


    /*** 用于道具 相框1 配饰2 氛围3 挡脸4 滤镜5 美妆6 */
    public String type;

    /**
     * 用于道具
     *
     * @return boolean
     */
    public boolean isArvrPorp() {
//       950需求，相框为本地实现，其他类型为调用 ARVR实现
        return !"1".equals(type);
    }

    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.id);
        dest.writeString(this.name);
        dest.writeString(this.type);
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    public void readFromParcel(Parcel source) {
        this.id = source.readString();
        this.name = source.readString();
        this.type = source.readString();
    }

    /**
     * Re group
     *
     * @param in in
     */
    protected ReGroup(Parcel in) {
        this.id = in.readString();
        this.name = in.readString();
        this.type = in.readString();
    }

    /**
     * CREATOR
     */
    public static final Creator<ReGroup> CREATOR = new Creator<ReGroup>() {
        @Override
        public ReGroup createFromParcel(Parcel source) {
            return new ReGroup(source);
        }

        @Override
        public ReGroup[] newArray(int size) {
            return new ReGroup[size];
        }
    };
}
