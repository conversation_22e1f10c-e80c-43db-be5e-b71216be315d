package com.jd.oa.lib.editor;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Linear layout manager wrap
 *
 * <AUTHOR>
 * @date 2020 /10/20 解决list数据越界问题
 */
public class LinearLayoutManagerWrap extends LinearLayoutManager {
    /**
     * Linear layout manager wrap
     *
     * @param context context
     */
    public LinearLayoutManagerWrap(Context context) {
        super(context);
    }

    /**
     * Linear layout manager wrap
     *
     * @param context       context
     * @param orientation   orientation
     * @param reverseLayout reverse layout
     */
    public LinearLayoutManagerWrap(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    /**
     * Linear layout manager wrap
     *
     * @param context      context
     * @param attrs        attrs
     * @param defStyleAttr def style attr
     * @param defStyleRes  def style res
     */
    public LinearLayoutManagerWrap(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    /**
     * On layout children *
     *
     * @param recycler recycler
     * @param state    state
     */
    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        // 在这里通过try来捕获这个异常即可
        try {
            super.onLayoutChildren(recycler, state);
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
        }
    }
}