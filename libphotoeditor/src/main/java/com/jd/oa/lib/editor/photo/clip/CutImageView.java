package com.jd.oa.lib.editor.photo.clip;

import android.animation.Animator;
import android.animation.TypeEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.lib.editor.pub.Size;
import com.jd.oa.lib.editor.util.BitmapUtils;

/**
 * des: 图片裁剪控件，包含旋转 <EMAIL> erp:zhoumin117
 *
 * <AUTHOR> zhoumin6
 * @date ： 2020 -11-30.19:51
 */
public class CutImageView extends View
        implements
        CutImageInterface,
        ScaleGestureDetector.OnScaleGestureListener,
        GestureDetector.OnGestureListener {

    /**
     * TAG
     */
    private final String TAG = "CutImageView";
    /**
     * 锚点
     */
    private CutImageInterface.Anchor mAnchor;


    /**
     * 是否正在缩放中
     */
    private boolean isScaling = false;

    /**
     * 是否可以获取裁剪后的图片 务必在获得裁剪后的图片之前进行判断
     */
    private boolean isSteady = false;
    /**
     * 移动手势
     */
    private GestureDetector mGestureDetector = null;
    /**
     * 缩放手势
     */
    private ScaleGestureDetector mScaleGestureDetector = null;


    /**
     * 在缩放过程中如果出现图片小于裁剪框 松手后回到原来的状态 避免图片小于裁剪框出现异常状态
     */
    private boolean isOutOfEdge = false;
    /**
     * 动画
     */
    private CutImageAnimator mAnimator = null;

    /**
     * paint
     */
    private final Paint mBitmapPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    /**
     * 图片目标区域
     */
    private RectF mBitmapFrame = new RectF();
    /**
     * 初始化图片区域，用于重置
     */
    private final RectF mCutBitmapInitFrame = new RectF();
    private final RectF mBitmapInitFrame = new RectF();

    /**
     * 旋转角度
     */
    private float mRotation = 0f;

    /**
     * 图片素材
     */
    private Bitmap mBitmap;

    /**
     * 可以显示图片的区域
     */
    private float mContentWidth = 0f;
    /**
     * 可以显示图片的区域
     */
    private float mContentHeight = 0f;
    /**
     * 内容比例
     */
    private float mContentRatio = 0f;

    /**
     * 裁剪框边界
     */
    private final RectF mAssistFrame = new RectF();
    /**
     * 初始裁剪框，用于重置
     */
    private final RectF mInitAssistFrame = new RectF();

    /**
     * 裁剪框限制区域 默认为图片初始化区域
     */
    private final RectF mWinFrame = new RectF();
    /**
     * 左边灰色区域
     */
    private final Rect mRemainLeftArea = new Rect();
    /**
     * 右边灰色区域
     */
    private final Rect mRemainRightArea = new Rect();
    /**
     * 上边灰色区域
     */
    private final Rect mRemainTopArea = new Rect();
    /**
     * 下边灰色区域
     */
    private final Rect mCoverBottomArea = new Rect();
    /**
     * M corners
     */
    private final float[] mCorners = new float[32];
    /**
     * M base sizes
     */
    private final float[][] mBaseSizes = new float[2][4];
    /**
     * 绘制画笔
     */
    private final Paint mLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private final Paint mLinePaintSewing = new Paint(Paint.ANTI_ALIAS_FLAG);

    /**
     * 覆盖画笔
     */
    private final Paint mCoverAreaPaint = new Paint(Paint.ANTI_ALIAS_FLAG);


    /**
     * M cut type
     */
    private CutImageType mCutImageType = CutImageType.FREE;
    /**
     * M init cut type
     */
    private CutImageType mInitCutImageType = CutImageType.FREE;
    /**
     * 最小图片像素， 宽高
     */
    private Size mMinImageSize;
    /**
     * 自定义图片裁剪 比例
     */
    private Size mCustomImageRate;
    /**
     * 是否可心手动线框
     */
    private boolean isImageCutRectDrag = true;
    /**
     * 记录最后有效的图线框区域
     */
    private final RectF mLastValidAssistFrame = new RectF();
    /**
     * 记录最后有效的图片区域
     */
    private final RectF mLastValidBitmapFrame = new RectF();

    /**
     * 是单指拖动操作，还是双指缩放操作
     */
    boolean isScroll = false, isScale = false;


    /**
     * 设置区域
     */
    private void setRemainArea() {
        mRemainLeftArea.set((int) mAssistFrame.left, (int) mAssistFrame.top, getLeft(), (int) mAssistFrame.bottom);
        mRemainRightArea.set(getRight(), (int) mAssistFrame.top, (int) mAssistFrame.right, (int) mAssistFrame.bottom);
        mRemainTopArea.set(getLeft(), (int) mAssistFrame.top, getRight(), getTop());
        mCoverBottomArea.set(getLeft(), getBottom(), getRight(), (int) mAssistFrame.bottom);
    }

    /**
     * Sets cut type *
     *
     * @param mCutImageType m cut type
     */
    public void setCutTypeInit(CutImageType mCutImageType) {
        this.mInitCutImageType = this.mCutImageType = mCutImageType;
    }

    /**
     * Sets cut type *
     *
     * @param cutImageType cut type
     */
    public int setCutType(CutImageType cutImageType) {
        return setCutType(cutImageType, false);
    }


    /**
     * Sets min px size
     *
     * @param size m min size
     */
    public void setMinImageSize(Size size) {
        this.mMinImageSize = size;
        if (mMinImageSize == null || mMinImageSize.width <= 0 || mMinImageSize.height <= 0) {
            mMinImageSize = new Size(2, 2);
        }
    }

    /**
     * Sets min px size
     *
     * @param size m min size
     */
    public void setCustomImageRate(Size size) {
        this.mCustomImageRate = size;
    }


    /**
     * Sets cut type *
     *
     * @param cutImageType m cut type
     * @param isInit       is init
     */
    private int setCutType(CutImageType cutImageType, boolean isInit) {
        if (cutImageType == null) {
            return 0;
        }

        if (!isBitmapSizeAllowOpt(cutImageType, 0) && !isInit) {
            return 2;
        }

        float wRate, hRate;
        switch (cutImageType) {
            default:
            case FREE:
                this.mCutImageType = cutImageType;
                wRate = mBitmap.getWidth();
                hRate = mBitmap.getHeight();
                break;
            case C3_4:
                wRate = 3;
                hRate = 4;
                break;
            case C1_1:
                wRate = 1;
                hRate = 1;
                break;
            case C4_3:
                wRate = 4;
                hRate = 3;
                break;
            case CUSTOM:
                if (mCustomImageRate == null || mCustomImageRate.width <= 0 || mCustomImageRate.height <= 0) {
                    return 1;
                }
                wRate = mCustomImageRate.width;
                hRate = mCustomImageRate.height;
                break;

        }


        float initWidth = mWinFrame.width();
        float initHeight = mWinFrame.height();

        float initRate = initWidth / initHeight;
        float targetRate = wRate / hRate;
        if (initRate > targetRate) {
            float halfW = (initHeight * wRate / hRate) / 2;
            mAssistFrame.left = mWinFrame.centerX() - halfW;
            mAssistFrame.right = mWinFrame.centerX() + halfW;
            mAssistFrame.top = mWinFrame.top;
            mAssistFrame.bottom = mWinFrame.bottom;
        } else {
            float halfH = (initWidth * hRate / wRate) / 2;
            mAssistFrame.left = mWinFrame.left;
            mAssistFrame.right = mWinFrame.right;
            mAssistFrame.top = mWinFrame.centerY() - halfH;
            mAssistFrame.bottom = mWinFrame.centerY() + halfH;
        }

        mAssistFrame.set(mAssistFrame);
        if (mMinImageSize != null && mMinImageSize.width > 0 && mMinImageSize.height > 0) {
            RectF clipRect = getClipRect(mBitmapFrame, mAssistFrame);
            if (Math.round(clipRect.width()) < mMinImageSize.width || Math.round(clipRect.height()) < mMinImageSize.height) {
                float scaleW = clipRect.width() / mMinImageSize.width;
                float scaleH = clipRect.height() / mMinImageSize.height;
                float scale = Math.min(scaleW, scaleH);
                onScale(scale, mAssistFrame.centerX(), mAssistFrame.centerY());
            }
        }


        checkNeedReset();
        RectF startFrame = new RectF(mBitmapFrame);
        RectF rectF = applyNeedReset(startFrame, 0, false);
        if (isInit) {
            mCutBitmapInitFrame.set(rectF);
        }
        this.mCutImageType = cutImageType;
        invalidate();
        return 1;
    }


    public void setImageCutRectDrag(boolean imageCutRectDrag) {
        isImageCutRectDrag = imageCutRectDrag;
    }

    /**
     * Cut image view
     *
     * @param context context
     */
    public CutImageView(Context context) {
        super(context);
        init(context);
    }

    /**
     * Cut image view
     *
     * @param context context
     * @param attrs   attrs
     */
    public CutImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    /**
     * Cut image view
     *
     * @param context      context
     * @param attrs        attrs
     * @param defStyleAttr def style attr
     */
    public CutImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    /**
     * 初始化
     *
     * @param context 上下文
     */
    private void init(Context context) {
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        mGestureDetector = new GestureDetector(context, this);
        mScaleGestureDetector = new ScaleGestureDetector(context, this);
        mAnimator = new CutImageAnimator();
        mAnimator.setDuration(100);
        mAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mAnchor = null;
                isSteady = false;
                mLastValidAssistFrame.set(mAssistFrame);
                mLastValidBitmapFrame.set(mBitmapFrame);
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                isSteady = false;
            }
        });
        mAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (animation != null && animation.getAnimatedValue() != null && animation.getAnimatedValue() instanceof CutImageState) {
                    CutImageState state = (CutImageState) animation.getAnimatedValue();
                    if (state != null) {
                        if (state.frame != null) {
                            mBitmapFrame = new RectF(state.frame);
                        }
                        if (state.winFrame != null) {
                            mAssistFrame.set(state.winFrame);
                        }
                    }
                    invalidate();
                }
            }
        });

        mLinePaint.setStyle(Paint.Style.STROKE);
        mLinePaint.setStrokeCap(Paint.Cap.SQUARE);
        mLinePaint.setColor(Color.WHITE);
        mLinePaint.setAlpha((int) (255 * 0.5));

        mLinePaintSewing.setStyle(Paint.Style.STROKE);
        mLinePaintSewing.setStrokeCap(Paint.Cap.SQUARE);
        mLinePaintSewing.setColor(Color.WHITE);

        mCoverAreaPaint.setStyle(Paint.Style.FILL);
        mCoverAreaPaint.setColor(Color.BLACK);
        mCoverAreaPaint.setAlpha(191);
    }

    /**
     * 开始动画
     *
     * @param sState   开始状态
     * @param eState   结束状态
     * @param duration duration
     */
    private void startAnimation(CutImageState sState, CutImageState eState, long duration) {
        if (mAnimator != null && !mAnimator.isRunning()) {
            mAnimator.setDuration(duration);
            mAnimator.setChangeState(sState, eState);
            mAnimator.start();
        }
    }

    /**
     * 直接通过bitmap对象设置图片
     *
     * @param bitmap bitmap
     */
    public void setBitmap(Bitmap bitmap, boolean isConfigChange) {
        if (bitmap == null || bitmap.isRecycled()) {
            return;
        }
        this.mBitmap = bitmap;
        int bitmapWidth = bitmap.getWidth();
        int bitmapHeight = bitmap.getHeight();
        // 原图宽高比
        float bitmapRatio = bitmapWidth * 1f / bitmapHeight;

        float viewWidth = getMeasuredWidth();
        float viewHeight = getMeasuredHeight();

        // true：左右顶边  false：上下顶边
        boolean flag;
        mContentWidth = viewWidth - getPaddingLeft() - getPaddingRight();
        mContentHeight = viewHeight - getPaddingTop() - getPaddingBottom();


        mContentRatio = mContentWidth / mContentHeight;
        float realImageWidth;
        float realImageHeight;
        if (mContentRatio < bitmapRatio) {
            realImageWidth = mContentWidth;
            flag = true;
            realImageHeight = realImageWidth / bitmapRatio;
        } else {
            realImageHeight = mContentHeight;
            flag = false;
            realImageWidth = realImageHeight * bitmapRatio;
        }

        if (flag) {//左右顶边
            float top = getPaddingTop() + (mContentHeight - realImageHeight) / 2;
            mBitmapFrame.set(getPaddingLeft(),
                    top,
                    viewWidth - getPaddingRight(),
                    top + realImageHeight);
            if (!isConfigChange) {
                mCutBitmapInitFrame.set(new RectF(mBitmapFrame));
            }
        } else {//上下顶边

            float left = getPaddingLeft() + (mContentWidth - realImageWidth) / 2;
            mBitmapFrame.set(left,
                    getPaddingTop(),
                    left + realImageWidth,
                    viewHeight - getPaddingBottom());
            if (!isConfigChange) {
                mCutBitmapInitFrame.set(new RectF(mBitmapFrame));
            }
        }
        if (!isConfigChange) {
            mBitmapInitFrame.set(mCutBitmapInitFrame);
        }

        mAssistFrame.set(new RectF(mBitmapFrame));
        if (!isConfigChange) {
            mInitAssistFrame.set(new RectF(mBitmapFrame));
        }

        mWinFrame.set(new RectF(getPaddingLeft(), getPaddingTop(), getMeasuredWidth() - getPaddingRight(), getMeasuredHeight() - getPaddingBottom()));

        setCutType(mCutImageType, true && !isConfigChange);
        if (!isConfigChange) {
            mInitAssistFrame.set(mAssistFrame);
        }
        if (isConfigChange) {
            // 修正 mInitAssistFrame、mBitmapInitFrame、mCutBitmapInitFrame
            validateInitFrame();
        }
        invalidate();
    }

    public Bitmap getBitmap() {
        return mBitmap;
    }

    /**
     * Reset
     */
    public void reset() {
        mCutImageType = mInitCutImageType;
        mBitmapFrame.set(new RectF(mCutBitmapInitFrame));
        if (mRotation != 0 && mBitmap != null && !mBitmap.isRecycled()) {
            mBitmap = BitmapUtils.rotateBitmap(mBitmap, (int) (360 - mRotation) % 360);
        }
        mRotation = 0f;
        startAnimation(new CutImageState(mBitmapFrame, mAssistFrame), new CutImageState(mCutBitmapInitFrame, mInitAssistFrame), 50);
    }

    /**
     * 获取操作后，线框对应图片上的区域，即图片保存时图片大小
     *
     * @return rect rectClip
     */
    public RectF getClipRect(@NonNull RectF bitmapFrame, @NonNull RectF assistFrame) {
        float scale = 1f / getBitmapScale(bitmapFrame);
        RectF frame = new RectF(assistFrame);
        Matrix m = new Matrix();
        m.setScale(scale, scale, frame.left, frame.top);
        m.mapRect(frame);
        return frame;
    }


    /**
     * 获得裁剪后的图片
     *
     * @return the clipped image
     */
    public Bitmap getClippedImage() {
        RectF frame = getClipRect(mBitmapFrame, mAssistFrame);
        if (Math.round(frame.width()) <= 0 || Math.round(frame.height()) <= 0) {
//            缩放操作以后，目标图片宽高像素小于1，已经在手势离开时做了控制，万一出现，返回原图
            return mBitmap;
        }

        Bitmap bitmap = Bitmap.createBitmap(Math.round(frame.width()), Math.round(frame.height()), Bitmap.Config.ARGB_8888);

        Canvas canvas = new Canvas(bitmap);

        canvas.translate(-frame.left, -frame.top);
        float scale = 1f / getBitmapScale(mBitmapFrame);
        canvas.scale(scale, scale, frame.left, frame.top);

        onDrawClipImages(canvas);

        return bitmap;
    }

    /**
     * 绘制图片
     *
     * @param canvas 画布
     */
    private void onDrawClipImages(Canvas canvas) {
        if (mBitmap != null) {
            canvas.clipRect(mAssistFrame);
            canvas.drawBitmap(mBitmap, null, mBitmapFrame, mBitmapPaint);
        }

    }

    /**
     * 绘制
     *
     * @param canvas canvas
     */
    @Override
    public void onDraw(Canvas canvas) {
        if (mBitmap == null || mBitmap.isRecycled()) {
            return;
        }
        canvas.drawBitmap(mBitmap, null, mBitmapFrame, mBitmapPaint);
        /* 绘制线框与遮挡*/
        setRemainArea();
        canvas.drawRect(mRemainLeftArea.left, mRemainLeftArea.top, mRemainLeftArea.right, mRemainLeftArea.bottom, mCoverAreaPaint);
        canvas.drawRect(mRemainRightArea, mCoverAreaPaint);
        canvas.drawRect(mRemainTopArea, mCoverAreaPaint);
        canvas.drawRect(mCoverBottomArea, mCoverAreaPaint);

        float[] size = {mAssistFrame.width(), mAssistFrame.height()};
        for (int i = 0; i < mBaseSizes.length; i++) {
            for (int j = 0; j < mBaseSizes[i].length; j++) {
                mBaseSizes[i][j] = size[i] * CLIP_SIZE_RATIO[j];
            }
        }

        for (int i = 0; i < mCorners.length; i++) {
            mCorners[i] = mBaseSizes[i & 1][CLIP_CORNER_STRIDES >>> i & 1] + CLIP_CORNER_SIZES[CLIP_CORNERS[i] & 3] + CLIP_CORNER_STEPS[CLIP_CORNERS[i] >> 2];
        }

//        RectF frame = getClipRect(mBitmapFrame, mAssistFrame);
//        Paint p = new Paint();
//        p.setTextSize(50);
//        p.setColor(Color.RED);
//        canvas.drawText(Math.round(frame.width()) + "x" + Math.round(frame.height()), 100, 100, p);

        mLinePaint.setStrokeWidth(CLIP_THICKNESS_FRAME);
        canvas.drawRect(mAssistFrame, mLinePaint);

        float partWidth = mAssistFrame.width() / 3;
        float partHeight = mAssistFrame.height() / 3;

        canvas.drawRect(mAssistFrame.left + partWidth, mAssistFrame.top, mAssistFrame.right - partWidth, mAssistFrame.bottom, mLinePaint);
        canvas.drawRect(mAssistFrame.left, mAssistFrame.top + partHeight, mAssistFrame.right, mAssistFrame.bottom - partHeight, mLinePaint);

        canvas.translate(mAssistFrame.left, mAssistFrame.top);

        mLinePaintSewing.setStrokeWidth(CLIP_THICKNESS_SEWING);
        canvas.drawLines(mCorners, mLinePaintSewing);
        canvas.restore();


    }

    /**
     * 缩放手势方法
     *
     * @param detector detector
     * @return the boolean
     */
    @Override
    public boolean onScaleBegin(ScaleGestureDetector detector) {
        isScale = true;
        isScaling = true;
        isOutOfEdge = false;
        mLastValidBitmapFrame.set(mBitmapFrame);
        return true;
    }

    /**
     * On scale boolean
     *
     * @param detector detector
     * @return the boolean
     */
    @Override
    public boolean onScale(ScaleGestureDetector detector) {
        if (mAnchor != null) {
            return true;
        }
        if (detector != null) {
            onScaleLimitBitmapMinSize();
            onScale(detector.getScaleFactor(), getScaleX() + detector.getFocusX(),
                    getScaleY() + detector.getFocusY());
            invalidate();
        }

        return true;
    }

    /**
     * On scale end *
     *
     * @param detector detector
     */
    @Override
    public void onScaleEnd(ScaleGestureDetector detector) {
        isScaling = false;
        checkNeedReset();
        editedCallback();
    }

    /**
     * 检查图片四边，是否在线框之内
     */
    private void checkNeedReset() {
        isOutOfEdge = (mBitmapFrame.left > mAssistFrame.left + 1
                || mBitmapFrame.top > mAssistFrame.top + 1
                || mBitmapFrame.right < mAssistFrame.right - 1
                || mBitmapFrame.bottom < mAssistFrame.bottom - 1);
    }

    /**
     * 应用
     *
     * @param bitmapFrame 图片Frame
     * @param duration    duration
     * @return the rect f
     */
    private RectF applyNeedReset(@NonNull final RectF bitmapFrame, long duration, boolean isAni) {
        if (isOutOfEdge) {
            if (bitmapFrame.width() < mAssistFrame.width() || bitmapFrame.height() < mAssistFrame.height()) {
                float scale = Math.max(mAssistFrame.width() / bitmapFrame.width(), mAssistFrame.height() / bitmapFrame.height());
                onScale(scale, mAssistFrame.centerX(), mAssistFrame.centerY());
            }
            isOutOfEdge = false;
        }

        RectF bitmapTarRect = getResetBitmapTransEnd();
        if (isAni) {
            startAnimation(new CutImageState(bitmapFrame, null), new CutImageState(bitmapTarRect, null), duration);
        }
        return bitmapTarRect;
    }

    /**
     * Apply reset both boolean
     *
     * @param startFrame start frame
     * @return the boolean
     */
    private boolean applyResetBoth(RectF startFrame) {
        if (!isValidSize(mBitmapFrame, mAssistFrame)) {
            mAssistFrame.set(mLastValidAssistFrame);
        }
        Object[] tempList = getEndState();
        if (tempList[1] instanceof RectF) {
            RectF winEndState = (RectF) tempList[1];
            float tempScale = (float) tempList[0];
            RectF imageEndState = getScaleEnd(tempScale, mAssistFrame.centerX(), mAssistFrame.centerY());
            // 如果裁剪框大于了图片的最终位置，则需要对图片新的位置进行修正
            Matrix mMatrix = new Matrix();
            if (winEndState.left < imageEndState.left) {
                mMatrix.postTranslate(winEndState.left - imageEndState.left, 0f);
            }
            if (winEndState.top < imageEndState.top) {
                mMatrix.postTranslate(0f, winEndState.top - imageEndState.top);
            }
            if (winEndState.right > imageEndState.right) {
                mMatrix.postTranslate(winEndState.right - imageEndState.right, 0f);
            }
            if (winEndState.bottom > imageEndState.bottom) {
                mMatrix.postTranslate(0f, winEndState.bottom - imageEndState.bottom);
            }
            mMatrix.mapRect(imageEndState);
            startAnimation(new CutImageState(startFrame, mAssistFrame), new CutImageState(imageEndState, winEndState), 100);
        }
        return true;
    }

    /**
     * Is edited boolean
     *
     * @return the boolean
     */
    public boolean isEdited() {
        boolean isFrameEquals = mAssistFrame == null ? false : !mAssistFrame.equals(mBitmapInitFrame);
        return isFrameEquals || mInitCutImageType != mCutImageType;
    }

    /**
     * 其他手势方法
     *
     * @param e1        e 1
     * @param e2        e 2
     * @param distanceX distance x
     * @param distanceY distance y
     * @return the boolean
     */
    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        if (mAnchor != null) {
            RectF tempClip = new RectF(new RectF(mAssistFrame));
            mAnchor.move(mWinFrame, mAssistFrame, -distanceX, -distanceY);
            /* 修正图片frame*/
            if (mBitmapFrame.left > mAssistFrame.left) {
                mAssistFrame.left = tempClip.left;
            }
            if (mBitmapFrame.top > mAssistFrame.top) {
                mAssistFrame.top = tempClip.top;
            }
            if (mBitmapFrame.right < mAssistFrame.right) {
                mAssistFrame.right = tempClip.right;
            }
            if (mBitmapFrame.bottom < mAssistFrame.bottom) {
                mAssistFrame.bottom = tempClip.bottom;
            }
            if (mCutImageType == null) {
                onScrollLimitAssistMinSize();
                invalidate();
                return true;
            }
            boolean isFree = mCutImageType == CutImageType.FREE;
            float wRate = 1, hRate = 1;
            switch (mCutImageType) {
                default:
                case FREE:
                    break;
                case C3_4:
                    wRate = 3;
                    hRate = 4;
                    break;
                case C1_1:
                    wRate = 1;
                    hRate = 1;
                    break;
                case C4_3:
                    wRate = 4;
                    hRate = 3;
                    break;
                case CUSTOM:
                    if (mCustomImageRate == null || mCustomImageRate.width <= 0 || mCustomImageRate.height <= 0) {
                        isFree = true;
                        break;
                    }
                    wRate = mCustomImageRate.width;
                    hRate = mCustomImageRate.height;
                    break;
            }
            if (!isFree) {
                if (mAnchor == Anchor.LEFT || mAnchor == Anchor.RIGHT) {
                    // 计算出应有的半高
                    float halfH = (mAssistFrame.width() * hRate / wRate) / 2;
                    mAssistFrame.bottom = mWinFrame.centerY() + halfH;
                    mAssistFrame.top = mWinFrame.centerY() - halfH;
                    boolean isOut = false;
                    if (mAssistFrame.top < mBitmapFrame.top) {
                        // 顶部框出图片顶  半高是图片顶部位置 到中心点的距离
                        halfH = (mWinFrame.centerY() - mBitmapFrame.top);
                        mAssistFrame.top = mWinFrame.centerY() - halfH;
                        mAssistFrame.bottom = mWinFrame.centerY() + halfH;
                        isOut = true;
                    } else if (mAssistFrame.bottom > mBitmapFrame.bottom) {
                        // 底部框出图片底  半高是图片底部到中心点的距离
                        halfH = (mBitmapFrame.bottom - mWinFrame.centerY());
                        mAssistFrame.top = mWinFrame.centerY() - halfH;
                        mAssistFrame.bottom = mWinFrame.centerY() + halfH;
                        isOut = true;
                    }
                    if (isOut) {
                        if (mAnchor == Anchor.LEFT) {
                            //重新限定左边
                            mAssistFrame.left = mAssistFrame.right - mAssistFrame.height() * wRate / hRate;
                        } else if (mAnchor == Anchor.RIGHT) {
                            //重新限定右边
                            mAssistFrame.right = mAssistFrame.left + mAssistFrame.height() * wRate / hRate;
                        }
                    }

                } else if (mAnchor == Anchor.TOP || mAnchor == Anchor.BOTTOM) {//如果锚点是上下
//                    计算出应有的半宽
                    float halfW = (mAssistFrame.height() * wRate / hRate) / 2;
                    mAssistFrame.right = mWinFrame.centerX() + halfW;
                    mAssistFrame.left = mWinFrame.centerX() - halfW;

                    boolean isOut = false;
                    if (mAssistFrame.right > mBitmapFrame.right) {//如果右边线框超出图片右边
//                        半宽是图片右边到中心点的距离
                        halfW = (mBitmapFrame.right - mWinFrame.centerX());
                        mAssistFrame.right = mWinFrame.centerX() + halfW;
                        mAssistFrame.left = mWinFrame.centerX() - halfW;
                        isOut = true;
                    } else if (mAssistFrame.left < mBitmapFrame.left) {
//                        半宽是图片左边到中心点的距离
                        halfW = (mWinFrame.centerX() - mBitmapFrame.left);
                        mAssistFrame.right = mWinFrame.centerX() + halfW;
                        mAssistFrame.left = mWinFrame.centerX() - halfW;
                        isOut = true;
                    }
                    if (isOut) {
                        if (mAnchor == Anchor.TOP) {
                            //重新限定顶部
                            mAssistFrame.top = mAssistFrame.bottom - mAssistFrame.width() * hRate / wRate;
                        } else if (mAnchor == Anchor.BOTTOM) {
                            //重新限定底部
                            mAssistFrame.bottom = mAssistFrame.top + mAssistFrame.width() * hRate / wRate;
                        }
                    }
                } else if (mAnchor == Anchor.LEFT_BOTTOM || mAnchor == Anchor.RIGHT_BOTTOM) {
                    mAssistFrame.bottom = mAssistFrame.top + mAssistFrame.width() * hRate / wRate;
                    if (mAssistFrame.bottom > mBitmapFrame.bottom) {
                        float h = mBitmapFrame.bottom - mAssistFrame.top;
                        mAssistFrame.bottom = mAssistFrame.top + h;
                        if (mAnchor == Anchor.LEFT_BOTTOM) {
                            mAssistFrame.left = mAssistFrame.right - h * wRate / hRate;
                        } else if (mAnchor == Anchor.RIGHT_BOTTOM) {
                            mAssistFrame.right = mAssistFrame.left + h * wRate / hRate;
                        }
                    }

                } else if (mAnchor == Anchor.LEFT_TOP || mAnchor == Anchor.RIGHT_TOP) {
                    mAssistFrame.top = mAssistFrame.bottom - mAssistFrame.width() * hRate / wRate;

                    if (mAssistFrame.top < mBitmapFrame.top) {
                        float h = mAssistFrame.bottom - mBitmapFrame.top;
                        mAssistFrame.top = mAssistFrame.bottom - h;
                        if (mAnchor == Anchor.LEFT_TOP) {
                            mAssistFrame.left = mAssistFrame.right - h * wRate / hRate;
                        } else if (mAnchor == Anchor.RIGHT_TOP) {
                            mAssistFrame.right = mAssistFrame.left + h * wRate / hRate;
                        }
                    }
                }
            }
            onScrollLimitAssistMinSize();
        } else {
            onScrollBitmap(distanceX, distanceY);
        }
        invalidate();
        return true;
    }

    boolean isValidSize(RectF bitmapFrame, RectF assistFrame) {
        if (mMinImageSize == null) {
            return true;
        }
        RectF frame = getClipRect(bitmapFrame, assistFrame);
        boolean isValidSize = Math.round(frame.width()) >= mMinImageSize.width && Math.round(frame.height()) >= mMinImageSize.height;
        return isValidSize;
    }

    /**
     * 记录最后有效的 辅助线框区域
     */
    private void onScrollLimitAssistMinSize() {
        if (isValidSize(mBitmapFrame, mAssistFrame)) {
            mLastValidAssistFrame.set(mAssistFrame);
        }
        editedCallback();
    }

    /**
     * 记录最后有效的图片区域
     */
    private void onScaleLimitBitmapMinSize() {
        if (isValidSize(mBitmapFrame, mAssistFrame)) {
            mLastValidBitmapFrame.set(mBitmapFrame);
        }
        editedCallback();
    }

    /**
     * On down boolean
     *
     * @param e e
     * @return the boolean
     */
    @Override
    public boolean onDown(MotionEvent e) {
        isScroll = true;
        mLastValidAssistFrame.set(mAssistFrame);
        mLastValidBitmapFrame.set(mBitmapFrame);
        if (isImageCutRectDrag) {//不开启线线框拖动，不获取点击锚点即可
            mAnchor = getAnchor(e.getX(), e.getY());
        }
        isSteady = true;
        return true;
    }

    /**
     * On fling boolean
     *
     * @param e1        e 1
     * @param e2        e 2
     * @param velocityX velocity x
     * @param velocityY velocity y
     * @return the boolean
     */
    @Override
    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
        return true;
    }

    /**
     * On long press *
     *
     * @param e e
     */
    @Override
    public void onLongPress(MotionEvent e) {
    }

    /**
     * On show press *
     *
     * @param e e
     */
    @Override
    public void onShowPress(MotionEvent e) {
    }

    /**
     * On single tap up boolean
     *
     * @param e e
     * @return the boolean
     */
    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        return true;
    }


    /**
     * 监听手指抬起动作进行图片的复位
     *
     * @param event event
     * @return the boolean
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean handled = mScaleGestureDetector != null && mScaleGestureDetector.onTouchEvent(event);
        handled = mGestureDetector != null ? mGestureDetector.onTouchEvent(event) : handled;

        if (event.getActionMasked() == MotionEvent.ACTION_UP || event.getActionMasked() == MotionEvent.ACTION_CANCEL) {
            if (!isValidSize(mBitmapFrame, mAssistFrame)) {
                if (isScroll && mAnchor != null || isScale) {
                    mBitmapFrame.set(mLastValidBitmapFrame);
                }
                isScroll = false;
                isScale = false;
            }
            RectF startFrame = new RectF(mBitmapFrame);
            if (mAnchor != null) {
                applyResetBoth(startFrame);
                return handled;
            }
            applyNeedReset(startFrame, 100, true);
        }
        return handled;
    }

    /**
     * 在view被移除的时候清空Animator防止内存泄露
     */
    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mAnimator.cancel();
        mAnimator.removeAllListeners();
        mAnimator = null;
    }


    /**
     * On scroll bitmap *
     *
     * @param x x
     * @param y y
     */
    void onScrollBitmap(float x, float y) {
        float disX = -x, disY = -y;
        Matrix matrixF = new Matrix();
        matrixF.setTranslate(disX, disY);
        matrixF.mapRect(this.mBitmapFrame);
    }

    /**
     * On scale *
     *
     * @param factor factor
     * @param focusX focus x
     * @param focusY focus y
     */
    void onScale(float factor, float focusX, float focusY) {
        if (factor == 1f) {
            return;
        }
        Matrix matrix = new Matrix();
        matrix.setScale(factor, factor, focusX, focusY);
        matrix.mapRect(mBitmapFrame);
    }

    /**
     * Gets scale end *
     *
     * @param scale  scale
     * @param focusX focus x
     * @param focusY focus y
     * @return the scale end
     */
    RectF getScaleEnd(float scale, float focusX, float focusY) {
        RectF tempFrame = new RectF();
        tempFrame.set(mBitmapFrame);
        Matrix mMatrix = new Matrix();
        mMatrix.setScale(scale, scale, focusX, focusY);
        if (focusX > mCutBitmapInitFrame.centerX()) {
            mMatrix.postTranslate(-(focusX - mCutBitmapInitFrame.centerX()), 0f);
        } else {
            mMatrix.postTranslate(mCutBitmapInitFrame.centerX() - focusX, 0f);
        }
        if (focusY > mCutBitmapInitFrame.centerY()) {
            mMatrix.postTranslate(0f, -(focusY - mCutBitmapInitFrame.centerY()));
        } else {
            mMatrix.postTranslate(0f, mCutBitmapInitFrame.centerY() - focusY);
        }
        mMatrix.mapRect(tempFrame);
        return tempFrame;
    }

    /**
     * Gets reset trans end *
     *
     * @return the reset trans end
     */
    private RectF getResetBitmapTransEnd() {
        if (mBitmapFrame.left > mAssistFrame.left) {
            onScrollBitmap(-mAssistFrame.left + mBitmapFrame.left, 0f);
        }
        if (mBitmapFrame.top > mAssistFrame.top) {
            onScrollBitmap(0f, -mAssistFrame.top + mBitmapFrame.top);
        }
        if (mBitmapFrame.right < mAssistFrame.right) {
            onScrollBitmap(mBitmapFrame.right - mAssistFrame.right, 0f);
        }
        if (mBitmapFrame.bottom < mAssistFrame.bottom) {
            onScrollBitmap(0f, mBitmapFrame.bottom - mAssistFrame.bottom);
        }
        return mBitmapFrame;
    }

    /**
     * Is can save boolean
     *
     * @return the boolean
     */
    public boolean isCanSave() {
        return (mAnimator == null || !mAnimator.isRunning()) && !isScaling && !isSteady;
    }

    /**
     * @param cutImageType
     * @param angle
     * @return
     */
    private boolean isBitmapSizeAllowOpt(CutImageType cutImageType, int angle) {
        if (mBitmap == null || mBitmap.isRecycled()) {
            return false;
        }
        if (mMinImageSize == null || mMinImageSize.width <= 0 || mMinImageSize.height <= 0) {
            return true;
        }
        boolean isRotate = angle / 90 % 2 == 1;
        int bitmapWidth = isRotate ? mBitmap.getHeight() : mBitmap.getWidth();
        int bitmapHeight = isRotate ? mBitmap.getWidth() : mBitmap.getHeight();

        if (cutImageType == CutImageType.C3_4) {
            float stepW = mMinImageSize.width / 3f;
            float stepH = mMinImageSize.height / 4f;
            int minWith = Math.round(Math.max(stepW, stepH) * 3f);
            int minHeight = Math.round(Math.max(stepW, stepH) * 4f);
            return bitmapWidth >= minWith && bitmapHeight >= minHeight;
        } else if (cutImageType == CutImageType.C4_3) {
            float stepW = mMinImageSize.width / 4.0f;
            float stepH = mMinImageSize.height / 3.0f;
            int minWith = Math.round(Math.max(stepW, stepH) * 4f);
            int minHeight = Math.round(Math.max(stepW, stepH) * 3f);
            return bitmapWidth >= minWith && bitmapHeight >= minHeight;
        } else if (cutImageType == CutImageType.C1_1) {
            return bitmapWidth >= Math.max(mMinImageSize.width, mMinImageSize.height) && bitmapHeight >= Math.max(mMinImageSize.width, mMinImageSize.height);
        } else if (cutImageType == CutImageType.CUSTOM) {
            if (mCustomImageRate == null || mCustomImageRate.width <= 0 || mCustomImageRate.height <= 0) {
                return bitmapWidth >= mMinImageSize.width && bitmapHeight >= mMinImageSize.height;
            }
            int gcd = gcd(Math.max(mCustomImageRate.width, mCustomImageRate.height), Math.min(mCustomImageRate.width, mCustomImageRate.height));
            int rateW = mCustomImageRate.width / gcd;
            int rateH = mCustomImageRate.height / gcd;
            float stepW = mMinImageSize.width / rateW;
            float stepH = mMinImageSize.height / rateH;
            int minWith = Math.round(Math.max(stepW, stepH) * rateW);
            int minHeight = Math.round(Math.max(stepW, stepH) * rateH);
            return bitmapWidth >= minWith && bitmapHeight >= minHeight;
        } else {
            return bitmapWidth >= mMinImageSize.width && bitmapHeight >= mMinImageSize.height;
        }

    }


    private int gcd(int x, int y) {
        int t;
        while (y != 0) {
            t = x % y;
            x = y;
            y = t;
        }
        return x;
    }

    public int rotate(int angle) {
        if (mAnimator != null && mAnimator.isRunning()) {
            return 0;
        }
        if (isScaling || isSteady) {
            return 0;
        }

        if (mBitmap == null || mBitmap.isRecycled()) {
            return 0;
        }

        if (!isBitmapSizeAllowOpt(mCutImageType, angle)) {
            // 图片不满足最小像素要求，不允许 旋转
            return 2;
        }

        mBitmap = BitmapUtils.rotateBitmap(mBitmap, angle);
        mRotation = (mRotation + angle) % 360;//得到目标角度

        float clipWindowWidth = mAssistFrame.width();
        float clipWindowHeight = mAssistFrame.height();
        //裁剪区域宽高比
        float clipWindowRatio = clipWindowHeight / clipWindowWidth;

        float scale;
        if (mCutImageType == null || mCutImageType == CutImageType.FREE) {
            if (clipWindowRatio < mContentRatio) {
                scale = mContentHeight / clipWindowWidth;
            } else {
                scale = mContentWidth / clipWindowHeight;
            }
        } else {
            scale = 1f;
        }

        Matrix matrix = new Matrix();
        matrix.postScale(scale, scale, mWinFrame.centerX(), mWinFrame.centerY());
        matrix.postRotate(angle, mWinFrame.centerX(), mWinFrame.centerY());
        matrix.mapRect(mBitmapFrame);

        if (mCutImageType == null || mCutImageType == CutImageType.FREE) {
            // 自由模式下，线框跟随图片旋转
            rotateLineFrame(scale, angle);
        } else {
            //旋转后修正绽放
            if (mBitmapFrame.width() < mAssistFrame.width() || mBitmapFrame.height() < mAssistFrame.height()) {
                float fixScale = Math.max(mAssistFrame.width() / mBitmapFrame.width(), mAssistFrame.height() / mBitmapFrame.height());
                onScale(fixScale, mAssistFrame.centerX(), mAssistFrame.centerY());
            }
            //旋转后尺寸不满足最小像素要求，修正缩放
            if (mMinImageSize != null && mMinImageSize.width > 0 && mMinImageSize.height > 0 && !isValidSize(mBitmapFrame, mAssistFrame)) {
                RectF clipRect = getClipRect(mBitmapFrame, mAssistFrame);
                float scaleW = Math.round(clipRect.width()) / (float) mMinImageSize.width;
                float scaleH = Math.round(clipRect.height()) / (float) mMinImageSize.height;
                float scaleLimit = Math.min(scaleW, scaleH);
                onScale(scaleLimit, mAssistFrame.centerX(), mAssistFrame.centerY());
            }
        }
        //操作后，如果位置偏移如果不满足四边条件，位移修正位置
        checkNeedReset();
        if (isOutOfEdge) {
            getResetBitmapTransEnd();
        }

        invalidate();
        return 1;
    }


    /**
     * Gets bitmap scale *
     *
     * @return the bitmap scale
     */
    float getBitmapScale(RectF frame) {
        return frame.width() / (mBitmap != null ? mBitmap.getWidth() : frame.width());
    }


    /**
     * 获得锚点，即是否触摸裁剪框 返回空即未选中裁剪框
     *
     * @param x x
     * @param y y
     * @return the anchor
     */
    private Anchor getAnchor(float x, float y) {
        if (Anchor.isCohesionContains(mAssistFrame, -CLIP_CORNER_SIZE, x, y)
                && !Anchor.isCohesionContains(mAssistFrame, CLIP_CORNER_SIZE, x, y)) {
            int v = 0;
            float[] cohesion = Anchor.getFrame(mAssistFrame, 0);
            float[] pos = {x, y};
            for (int i = 0; i < cohesion.length; i++) {
                if (Math.abs(cohesion[i] - pos[i >> 1]) < CLIP_CORNER_SIZE) {
                    v |= 1 << i;
                }
            }

            return Anchor.valueOf(v);
        }
        return null;
    }

    /**
     * 获得裁剪框需要修正的尺寸和位置
     *
     * @return the object [ ]
     */
    private void validateInitFrame() {
        float mFrameRatio = mInitAssistFrame.width() / mInitAssistFrame.height();
        float mWinRatio = mWinFrame.width() / mWinFrame.height();
        float scale;
        if (mFrameRatio < mWinRatio) {
            scale = mWinFrame.height() / mInitAssistFrame.height();
        } else {
            scale = mWinFrame.width() / mInitAssistFrame.width();
        }
        Matrix matrix = new Matrix();
        if (mInitAssistFrame.centerX() > mWinFrame.centerX()) {
            matrix.postTranslate(-(mInitAssistFrame.width() / 2 + mInitAssistFrame.left - mWinFrame.centerX()), 0f);
        } else {
            matrix.postTranslate(mInitAssistFrame.width() / 2 + mWinFrame.centerX() - mInitAssistFrame.right, 0f);
        }
        if (mInitAssistFrame.centerY() > mWinFrame.centerY()) {
            matrix.postTranslate(0f, -(mInitAssistFrame.height() / 2 + mInitAssistFrame.top - mWinFrame.centerY()));
        } else {
            matrix.postTranslate(0f, mInitAssistFrame.height() / 2 + mWinFrame.centerY() - mInitAssistFrame.bottom);
        }
        matrix.postScale(scale, scale, mWinFrame.centerX(), mWinFrame.centerY());
        matrix.mapRect(mInitAssistFrame);
        matrix.mapRect(mBitmapInitFrame);
        matrix.mapRect(mCutBitmapInitFrame);
    }

    /**
     * 获得裁剪框需要修正的尺寸和位置
     *
     * @return the object [ ]
     */
    private Object[] getEndState() {
        RectF tempFrame = new RectF();
        tempFrame.set(mAssistFrame);
        float mFrameRatio = mAssistFrame.width() / mAssistFrame.height();
        float mWinRatio = mWinFrame.width() / mWinFrame.height();
        float scale;
        if (mFrameRatio < mWinRatio) {
            scale = mWinFrame.height() / mAssistFrame.height();
        } else {
            scale = mWinFrame.width() / mAssistFrame.width();
        }
        Matrix matrix = new Matrix();
        if (mAssistFrame.centerX() > mWinFrame.centerX()) {
            matrix.postTranslate(-(mAssistFrame.width() / 2 + mAssistFrame.left - mWinFrame.centerX()), 0f);
        } else {
            matrix.postTranslate(mAssistFrame.width() / 2 + mWinFrame.centerX() - mAssistFrame.right, 0f);
        }
        if (mAssistFrame.centerY() > mWinFrame.centerY()) {
            matrix.postTranslate(0f, -(mAssistFrame.height() / 2 + mAssistFrame.top - mWinFrame.centerY()));
        } else {
            matrix.postTranslate(0f, mAssistFrame.height() / 2 + mWinFrame.centerY() - mAssistFrame.bottom);
        }
        matrix.postScale(scale, scale, mWinFrame.centerX(), mWinFrame.centerY());
        matrix.mapRect(tempFrame);
        return new Object[]{scale, tempFrame};
    }

    /**
     * Rotate line frame *
     *
     * @param scale 缩放
     * @param angle 角度
     */
    private void rotateLineFrame(float scale, float angle) {
        Matrix matrix = new Matrix();
        matrix.postScale(scale, scale, mAssistFrame.centerX(), mAssistFrame.centerY());
        matrix.postRotate(angle, mAssistFrame.centerX(), mAssistFrame.centerY());
        matrix.mapRect(mAssistFrame);
    }


    /**
     * Cut image state
     */
    static class CutImageState {

        /**
         * Frame
         */
        public RectF frame = new RectF();

        /**
         * Win frame
         */
        public RectF winFrame = null;

        /**
         * Cut image state
         *
         * @param frame    frame
         * @param winFrame win frame
         */
        public CutImageState(RectF frame, RectF winFrame) {
            this.frame.set(frame);
            if (winFrame != null) {
                this.winFrame = new RectF();
                this.winFrame.set(winFrame);
            }
        }
    }

    /**
     * 缩放与手势动画
     */
    static class CutImageAnimator extends ValueAnimator {
        /**
         * M evaluator
         */
        private CutImageEvaluator mEvaluator = null;

        /**
         * Cut image animator
         */
        public CutImageAnimator() {
            setInterpolator(new AccelerateDecelerateInterpolator());
        }

        /**
         * Sets object values *
         *
         * @param values values
         */
        @Override
        public void setObjectValues(Object... values) {
            super.setObjectValues(values);
            if (mEvaluator == null) {
                mEvaluator = new CutImageEvaluator();
            }
            setEvaluator(mEvaluator);
        }


        /**
         * Sets change state *
         *
         * @param sState s state
         * @param eState e state
         */
        public void setChangeState(CutImageState sState, CutImageState eState) {
            setObjectValues(sState, eState);
        }
    }


    /**
     * 动画求值程序
     */
    static class CutImageEvaluator implements TypeEvaluator<CutImageState> {
        /**
         * 临时状态
         */
        private CutImageState cutImageState = null;

        /**
         * Evaluate cut image state
         *
         * @param fraction   fraction
         * @param startValue start value
         * @param endValue   end value
         * @return the cut image state
         */
        @Override
        public CutImageState evaluate(float fraction, CutImageState startValue, CutImageState endValue) {
            if (startValue != null && endValue != null) {
                float left = startValue.frame.left + fraction * (endValue.frame.left - startValue.frame.left);
                float top = startValue.frame.top + fraction * (endValue.frame.top - startValue.frame.top);
                float right = startValue.frame.right + fraction * (endValue.frame.right - startValue.frame.right);
                float bottom = startValue.frame.bottom + fraction * (endValue.frame.bottom - startValue.frame.bottom);
                if (cutImageState != null) {
                    cutImageState.frame = new RectF(left, top, right, bottom);
                } else {
                    cutImageState = new CutImageState(new RectF(left, top, right, bottom), null);
                }
                if (startValue.winFrame != null) {
                    left = startValue.winFrame.left + fraction * (endValue.winFrame.left - startValue.winFrame.left);
                    top = startValue.winFrame.top + fraction * (endValue.winFrame.top - startValue.winFrame.top);
                    right = startValue.winFrame.right + fraction * (endValue.winFrame.right - startValue.winFrame.right);
                    bottom = startValue.winFrame.bottom + fraction * (endValue.winFrame.bottom - startValue.winFrame.bottom);

                    cutImageState.winFrame = new RectF(left, top, right, bottom);
                } else {
                    cutImageState.winFrame = null;
                }
            }

            return cutImageState != null ? cutImageState : new CutImageState(new RectF(0f, 0f, 0f, 0f), null);
        }
    }

    public void editedCallback() {
        if (mEditedListener != null) {
            mEditedListener.isEdited();
        }
    }

    public float getRotation() {
        return mRotation;
    }

    private IEditedListener mEditedListener;

    public void setEditedListener(IEditedListener listener) {
        mEditedListener = listener;
    }

    public interface IEditedListener {

        void isEdited();
    }

}
