package com.jd.oa.lib.editor.photo.paste.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

/**
 * des: 自定义 限制左右滑动的ViewPager
 *
 * <AUTHOR>
 * @date 2021 /10/27 : 11:22 上午
 * erp        zhoumin117
 * mail       <EMAIL>
 */
public class CsViewPager extends ViewPager {
    /**
     * C view pager
     *
     * @param context context
     */
    public CsViewPager(@NonNull Context context) {
        super(context);
    }

    /**
     * C view pager
     *
     * @param context context
     * @param attrs   attrs
     */
    public CsViewPager(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    /**
     * Is scroll enabled
     */
    private boolean isScrollEnabled = true;

    /**
     * On touch event boolean
     *
     * @param event event
     * @return the boolean
     */
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return this.isScrollEnabled && super.onTouchEvent(event);
    }

    /**
     * On intercept touch event boolean
     *
     * @param event event
     * @return the boolean
     */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        return this.isScrollEnabled && super.onInterceptTouchEvent(event);
    }

    /**
     * Sets scroll enabled *
     *
     * @param b b
     */
    public void setScrollEnabled(boolean b) {
        this.isScrollEnabled = b;
    }

    /**
     * On measure *
     *
     * @param widthMeasureSpec  width measure spec
     * @param heightMeasureSpec height measure spec
     */
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        try {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
