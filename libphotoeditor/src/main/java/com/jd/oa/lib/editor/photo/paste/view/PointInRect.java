package com.jd.oa.lib.editor.photo.paste.view;

import android.graphics.Point;

/**
 * <AUTHOR> zhoumin
 * Email: <EMAIL>
 * @date : 2018/8/15
 * Time: 下午7:29
 * Description:
 */
public class PointInRect {


    /**
     * 点是否在矩形区域内
     *
     * @param point point
     * @param mLt   m lt
     * @param mRt   m rt
     * @param mRb   m rb
     * @param mLb   m lb
     * @return the boolean
     */
    public static boolean isPointInRect(Point point,//目标点
                                        Point mLt,//左上
                                        Point mRt,//右上
                                        Point mRb,//右下
                                        Point mLb//左下
    ) {
        if (point == null) {
            return false;
        }
        if (mLt == null || mRt == null) {
            return false;
        }
        if (mRb == null || mLb == null) {
            return false;
        }
        Point mLtPoint = new Point(mLt.x , mLt.y);
        Point mRtPoint = new Point(mRt.x , mRt.y);
        Point mRbPoint = new Point(mRb.x , mRb.y);
        Point mLbPoint = new Point(mLb.x , mLb.y);


        int width = (int) lineSpace(mLtPoint, mRtPoint);
        int height = (int) lineSpace(mLtPoint, mLbPoint);

        int distanceTop = (int) pointToLine(point, mLtPoint, mRtPoint);
        int distanceBottom = (int) pointToLine(point, mRbPoint, mLbPoint);

        int distanceRight = (int) pointToLine(point, mRtPoint, mRbPoint);
        int distanceLeft = (int) pointToLine(point, mLtPoint, mLbPoint);

        int dV = distanceBottom + distanceTop;
        int dH = distanceRight + distanceLeft;

        boolean isIn = false;

        if (dV <= height && dH <= width) {
            isIn = true;
        }
        return isIn;
    }


    /**
     * 点到线的行走距离
     *
     * @param point      point
     * @param linePoint1 line point 1
     * @param linePoint2 line point 2
     * @return the double
     */
    public static double pointToLine(Point point, Point linePoint1, Point linePoint2) {
        double space;
        double a, b, c;
        a = lineSpace(linePoint1, linePoint2);// 线段的长度
        b = lineSpace(linePoint1, point);// (x1,y1)到点的距离
        c = lineSpace(linePoint2, point);// (x2,y2)到点的距离
        if (c <= 0.000001 || b <= 0.000001) {
            space = 0;
            return space;
        }
        if (a <= 0.000001) {
            space = b;
            return space;
        }
        if (c * c >= a * a + b * b) {
            space = b;
            return space;
        }
        if (b * b >= a * a + c * c) {
            space = c;
            return space;
        }
        double p = (a + b + c) / 2;// 半周长
        double s = Math.sqrt(p * (p - a) * (p - b) * (p - c));// 海伦公式求面积
        space = 2 * s / a;// 返回点到线的距离（利用三角形面积公式求高）
        return space;
    }

    /**
     * 计算两点之间的距离
     *
     * @param point1 point 1
     * @param point2 point 2
     * @return the double
     */
    public static double lineSpace(Point point1, Point point2) {
        double lineLength;
        lineLength = Math.sqrt((point1.x - point2.x) * (point1.x - point2.x) + (point1.y - point2.y)
                * (point1.y - point2.y));
        return lineLength;

    }

}
