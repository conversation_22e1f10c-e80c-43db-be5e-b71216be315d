package com.jd.oa.lib.editor.photo;

import android.os.Parcel;

import com.jd.oa.lib.editor.base.BaseParam;
import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.pub.MmType;

import java.util.ArrayList;

/**
 * des: 图片裁剪配置参数
 * <EMAIL>
 * erp:zhoumin117
 *
 * @author： zhoumin6
 * @date： 2020 /8/12.4:05 PM
 */
public class CutPhotoParam extends BaseParam {

    /**
     * 需要编辑的图片路径数组
     */
    public ArrayList<LocalMedia> photoPath;


    /**
     * from
     */
    public MmType.FROM_TYPE mFrom = MmType.FROM_TYPE.ALBUM;

    /**
     * 数据是否可用，必须得要有图片数据
     *
     * @return the boolean
     */
    public boolean isValid() {
        return photoPath != null && photoPath.size() > 0;
    }


    /**
     * 构造
     */
    public CutPhotoParam() {
    }


    /**
     * Describe contents int
     *
     * @return the int
     */
    @Override
    public int describeContents() {
        return 0;
    }

    /**
     * Write to parcel *
     *
     * @param dest  dest
     * @param flags flags
     */
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeTypedList(this.photoPath);
        dest.writeInt(this.mFrom == null ? -1 : this.mFrom.ordinal());
    }

    /**
     * Read from parcel *
     *
     * @param source source
     */
    @Override
    public void readFromParcel(Parcel source) {
        super.readFromParcel(source);
        this.photoPath = source.createTypedArrayList(LocalMedia.CREATOR);
        int tmpMFrom = source.readInt();
        this.mFrom = tmpMFrom == -1 ? null : MmType.FROM_TYPE.values()[tmpMFrom];
    }

    /**
     * Edit photo param
     *
     * @param in in
     */
    protected CutPhotoParam(Parcel in) {
        super(in);
        this.photoPath = in.createTypedArrayList(LocalMedia.CREATOR);
        int tmpMFrom = in.readInt();
        this.mFrom = tmpMFrom == -1 ? null : MmType.FROM_TYPE.values()[tmpMFrom];
    }

    /**
     * CREATOR
     */
    public static final Creator<CutPhotoParam> CREATOR = new Creator<CutPhotoParam>() {
        @Override
        public CutPhotoParam createFromParcel(Parcel source) {
            return new CutPhotoParam(source);
        }

        @Override
        public CutPhotoParam[] newArray(int size) {
            return new CutPhotoParam[size];
        }
    };
}
