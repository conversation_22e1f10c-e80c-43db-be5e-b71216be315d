package com.jd.oa.lib.editor.photo.paint;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;

import androidx.annotation.Nullable;

public class PaintView extends View {

    private PaintStruct paintStruct;

    private PaintStruct mosaicStruct;

    public PaintView(Context context) {
        this(context, null);
    }

    public PaintView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mosaicStruct != null && mosaicStruct.isActive()) {
            boolean b = mosaicStruct.onTouchEvent(event);
            if (b) {
                ViewParent parent = getParent();
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(true);
                }
                invalidate();
                return true;
            }
        }
        if (paintStruct != null && paintStruct.isActive()) {
            boolean b = paintStruct.onTouchEvent(event);
            if (b) {
                ViewParent parent = getParent();
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(true);
                }
                invalidate();
                return true;
            }
        }
        return super.onTouchEvent(event);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        try {
            if (mosaicStruct != null) {
                mosaicStruct.onDraw(canvas);
            }
        } catch (Exception e) {
        }
        try {
            if (paintStruct != null) {
                paintStruct.onDraw(canvas);
            }
        } catch (Exception e) {
        }
    }

    public void setPaintStruct(PaintStruct paintStruct) {
        this.paintStruct = paintStruct;
    }

    public void setMosaicStruct(PaintStruct mosaicStruct) {
        this.mosaicStruct = mosaicStruct;
    }
}
