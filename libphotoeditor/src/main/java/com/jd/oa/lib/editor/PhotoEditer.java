package com.jd.oa.lib.editor;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.lib.editor.base.BaseBuilder;
import com.jd.oa.lib.editor.bean.LocalMedia;
import com.jd.oa.lib.editor.photo.EditPhotoParam;
import com.jd.oa.lib.editor.photo.JdmmPhotoEditActivity;
import com.jd.oa.lib.editor.pub.Constants;
import com.jd.oa.lib.editor.pub.MmType;

import java.util.ArrayList;

/**
 * des: 图片编辑构建
 * <EMAIL>
 * erp:zhoumin117
 *
 * @author： zhoumin6
 * @date： 2020 -08-12.16:53
 */
public class PhotoEditer {

    /**
     * 构建者
     *
     * @return the photo editer . photo editer builder
     */
    public static PhotoEditerBuilder builder() {
        return new PhotoEditerBuilder();
    }

    /**
     * 图片编辑 builder
     */
    public static class PhotoEditerBuilder extends BaseBuilder<PhotoEditerBuilder, EditPhotoParam> {

        /**
         * Photo editer builder
         */
        public PhotoEditerBuilder() {
            super(new EditPhotoParam());
            mParam.maxDecalsNumber = 50;
            super.builder = this;
        }


        /**
         * 最多可编辑数量
         *
         * @param number numbervv
         * @return photo editer builder
         */
        public PhotoEditerBuilder maxPhotoNumber(int number) {
            mParam.maxPhotoNumber = number;
            return this;
        }

        /**
         * 最多可添加贴纸数量
         *
         * @param number number
         * @return photo editer builder
         */
        public PhotoEditerBuilder maxDecalsNumber(int number) {
            mParam.maxDecalsNumber = number;
            return this;
        }

        /**
         * From type photo editer builder
         *
         * @param type type
         * @return the photo editer builder
         */
        public PhotoEditerBuilder fromType(MmType.FROM_TYPE type) {
            mParam.mFrom = type;
            return this;
        }


        /**
         * 需要编辑的图片
         *
         * @param photoPath photo path
         * @return photo editer builder
         */
        public PhotoEditerBuilder editPhotos(@NonNull ArrayList<LocalMedia> photoPath) {
            mParam.photoPath = photoPath;
            return this;
        }

        /**
         * 启动
         *
         * @param activity    activity
         * @param requestCode request code
         */
        private void start(Activity activity, int requestCode) {
            if (activity == null || activity.isFinishing()) {
                return;
            }
            JdmmPhotoEditActivity.startActivityForResult(activity, mParam, requestCode);
        }

        public void start(Activity activity, IPhotoEditorCallback callback, int requestCode, boolean autoClose) {
            if (activity == null || activity.isFinishing()) {
                return;
            }
            if (callback == null) {
                return;
            }
            IntentFilter readyIntentFilter = new IntentFilter();
            readyIntentFilter.addAction(Constants.ACTION_EDITOR_PHOTO_READY);
            LocalBroadcastManager.getInstance(activity).registerReceiver(new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    callback.ready();
                    LocalBroadcastManager.getInstance(context).unregisterReceiver(this);
                }
            }, readyIntentFilter);

            if (activity instanceof FragmentActivity) {
                if (!autoClose) {
                    IntentFilter intentFilter = new IntentFilter();
                    intentFilter.addAction(Constants.ACTION_EDITOR_PHOTO_CALL_BACK);
                    LocalBroadcastManager.getInstance(activity).registerReceiver(new BroadcastReceiver() {
                        @Override
                        public void onReceive(Context context, Intent intent) {
                            int resultCode = intent.getIntExtra(Constants.KEY_RESULT, 0);
                            if (resultCode == Constants.VAL_RESULT_FINISH) {
                                LocalBroadcastManager.getInstance(context).unregisterReceiver(this);
                            } else {
                                handleCallback(resultCode, intent, callback);
                            }
                        }
                    }, intentFilter);
                }
                FragmentActivity act = (FragmentActivity) activity;
                Intent intent = new Intent(activity, JdmmPhotoEditActivity.class);
                intent.putExtra(Constants.KEY_PARAM, mParam);
                intent.putExtra(Constants.KEY_AUTO_CLOSE, autoClose);
                AutoUnregisterResultCallback<ActivityResult> autoCallback = new AutoUnregisterResultCallback<ActivityResult>() {
                    @Override
                    public void onActivityResult(ActivityResult o, boolean u) {
                        int resultCode = o.getResultCode();
                        if (autoClose) {
                            handleCallback(resultCode, o.getData(), callback);
                        }
                    }
                };
                String key = "CrossPlatformPan_editor_photo" + System.currentTimeMillis();
                ActivityResultLauncher<Intent> register = act.getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), autoCallback);
                autoCallback.setLauncher(register);
                register.launch(intent);
            } else {
                start(activity, requestCode);
            }
        }
    }

    private static void handleCallback(int resultCode, Intent intent, IPhotoEditorCallback callback) {
        if (resultCode == Activity.RESULT_OK || resultCode == Activity.RESULT_FIRST_USER) {
            ArrayList<LocalMedia> list = intent.getParcelableArrayListExtra(Constants.KEY_PARAM);
            if (list != null && list.size() > 0) {
                LocalMedia resultMedia = list.get(0);
                callback.done(resultMedia.getPath());
            } else {
                callback.cancel();
            }
        } else {
            callback.cancel();
        }

    }
}
