package com.jd.oa.lib.editor.photo.paste.view;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.PointF;
import android.view.MotionEvent;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR> zhoumin
 * Email: <EMAIL>
 * @date : 2018/10/26
 * Time: 下午12:14
 * Description:
 */
public class Utils {

    /**
     * 获取旋转某个角度之后的点
     *
     * @param center center
     * @param source source
     * @param degree degree
     * @return point
     */
    public static Point obtainRotationPoint(Point center, Point source, float degree) {
        //两者之间的距离
        Point disPoint = new Point();
        disPoint.x = source.x - center.x;
        disPoint.y = source.y - center.y;

        //没旋转之前的弧度
        double originRadian = 0;

        //没旋转之前的角度
        double originDegree ;

        //旋转之后的角度
        double resultDegree ;

        //旋转之后的弧度
        double resultRadian ;

        //经过旋转之后点的坐标
        Point resultPoint = new Point();

        double distance = Math.sqrt(disPoint.x * disPoint.x + disPoint.y * disPoint.y);
        if (disPoint.x == 0 && disPoint.y == 0) {
            return center;
            // 第一象限
        } else if (disPoint.x >= 0 && disPoint.y >= 0) {
            // 计算与x正方向的夹角
            originRadian = Math.asin(disPoint.y / distance);

            // 第二象限
        } else if (disPoint.x < 0 && disPoint.y >= 0) {
            // 计算与x正方向的夹角
            originRadian = Math.asin(Math.abs(disPoint.x) / distance);
            originRadian = originRadian + Math.PI / 2;

            // 第三象限
        } else if (disPoint.x < 0 && disPoint.y < 0) {
            // 计算与x正方向的夹角
            originRadian = Math.asin(Math.abs(disPoint.y) / distance);
            originRadian = originRadian + Math.PI;
        } else if (disPoint.x >= 0 && disPoint.y < 0) {
            // 计算与x正方向的夹角
            originRadian = Math.asin(disPoint.x / distance);
            originRadian = originRadian + Math.PI * 3 / 2;
        }

        // 弧度换算成角度
        originDegree = radianToDegree(originRadian);
        resultDegree = originDegree + degree;

        // 角度转弧度
        resultRadian = degreeToRadian(resultDegree);

        resultPoint.x = (int) Math.round(distance * Math.cos(resultRadian));
        resultPoint.y = (int) Math.round(distance * Math.sin(resultRadian));
        resultPoint.x += center.x;
        resultPoint.y += center.y;

        return resultPoint;
    }

    /**
     * 获取变长参数最大的值
     *
     * @param array array
     * @return max value
     */
    public static int getMaxValue(Integer... array) {
        List<Integer> list = Arrays.asList(array);
        Collections.sort(list);
        return list.get(list.size() - 1);
    }


    /**
     * 获取变长参数最大的值
     *
     * @param array array
     * @return min value
     */
    public static int getMinValue(Integer... array) {
        List<Integer> list = Arrays.asList(array);
        Collections.sort(list);
        return list.get(0);
    }


    /**
     * 弧度换算成角度
     *
     * @param radian radian
     * @return double
     */
    public static double radianToDegree(double radian) {
        return radian * 180 / Math.PI;
    }

    /**
     * 角度换算成弧度
     *
     * @param degree degree
     * @return double
     */
    public static double degreeToRadian(double degree) {
        return degree * Math.PI / 180;
    }

    /**
     * 两个点之间的距离
     *
     * @param pf1 pf 1
     * @param pf2 pf 2
     * @return float
     */
    public static  float distance4PointF(Point pf1, Point pf2) {
        if (pf1 == null || pf2 == null) {
            return 0;
        }

        float disX = pf2.x - pf1.x;
        float disY = pf2.y - pf1.y;
        return (float) Math.sqrt(disX * disX + disY * disY);
    }


    /**
     * Gets distance *
     *
     * @param event event
     * @return the distance
     */
//返回两点间的距离
    public static double getDistance(MotionEvent event) {
        //计算X的变化量
        float deltaX = event.getX(0) - event.getX(1);
        //计算Y的变化量
        float deltaY = event.getY(0) - event.getY(1);
        //计算距离
        return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    }

    /**
     * 水平镜像图片
     *
     * @param bm bm
     * @return bitmap bitmap
     */
    public static Bitmap mirror(Bitmap bm) {
        Bitmap modBm = Bitmap.createBitmap(bm.getWidth(), bm.getHeight(), bm.getConfig());
        Canvas canvas = new Canvas(modBm);
        Paint paint = new Paint();

        Matrix matrix = new Matrix();
        matrix.setScale(-1, 1);//翻转
        matrix.postTranslate(bm.getWidth(), 0);

        canvas.drawBitmap(bm, matrix, paint);
        return modBm;
    }


    /**
     * Gets degree *
     *
     * @param center center
     * @param pre    pre
     * @param cur    cur
     * @return the degree
     */
    public static float getDegree(Point center, Point pre, Point cur) {
// 角度
        double a = Utils.distance4PointF(center, pre);
        double b = Utils.distance4PointF(pre, cur);
        double c = Utils.distance4PointF(center, cur);

        double cosB = (a * a + c * c - b * b) / (2 * a * c);

        if (cosB >= 1) {
            cosB = 1f;
        }

        double radian = Math.acos(cosB);
        float newDegree = (float) Utils.radianToDegree(radian);

        //center -> proMove的向量， 我们使用PointF来实现
        PointF centerToProMove = new PointF((pre.x - center.x), (pre.y - center.y));

        //center -> curMove 的向量
        PointF centerToCurMove = new PointF((cur.x - center.x), (cur.y - center.y));

        //向量叉乘结果, 如果结果为负数， 表示为逆时针， 结果为正数表示顺时针
        float result = centerToProMove.x * centerToCurMove.y - centerToProMove.y * centerToCurMove.x;

        if (result < 0) {
            newDegree = -newDegree;
        }
        return newDegree;
    }

    /**
     * Gets random number *
     *
     * @param min min
     * @param max max
     * @return the random number
     */
    public static int getRandomNumber(int min, int max) {
        if (max <= 0) {
            max = 10;
        }
        Random random = new Random();
        return random.nextInt(max) % (max - min + 1) + min;
    }




}
