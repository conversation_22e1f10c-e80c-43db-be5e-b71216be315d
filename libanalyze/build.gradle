plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }
    namespace 'com.jd.oa.analyze'

}

dependencies {
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation COMPILE_COMMON.gson

    //子午线埋点sdk
    api(group: 'com.jingdong.wireless.libs', name: 'jdmasdk', version: '6.3.44')
    api 'com.jingdong.wireless.jdsdk:baseinfo:1.7.8'

}