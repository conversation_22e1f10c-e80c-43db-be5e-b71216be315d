package com.jd.oa.analyze;

import android.content.Context;
import android.util.Log;

import com.jingdong.jdma.JDMA;
import com.jingdong.jdma.JDMAConfig;
import com.jingdong.jdma.minterface.JDMABaseInfo;
import com.jingdong.sdk.baseinfo.BaseInfo;

public class MEAnalyze {

    public static Logger mLogger;

    public static void init(Context context, final String deviceType
            , String versionName, String channel
            , int versionCode, String UUID, Logger analyzeLogger,String siteId) {

        JDMABaseInfo baseInfo = new JDMABaseInfo() {

            @Override
            public String getAndroidId() {
                return BaseInfo.getAndroidId();
            }

            @Override
            public String getDeviceBrand() {
                return BaseInfo.getDeviceBrand();
            }

            @Override
            public String getDeviceModel() {
                return deviceType;
            }

            @Override
            public String getOsVersionName() {
                return BaseInfo.getAndroidVersion();
            }

            @Override
            public int getOsVersionInt() {
                return BaseInfo.getAndroidSDKVersion();
            }

            @Override
            public String getScreenSize() {
                return BaseInfo.getScreenHeight() + "*" + BaseInfo.getScreenWidth();
            }

            @Override
            public String getSimOperator() {
                return BaseInfo.getSimOperator();
            }
        };

        JDMAConfig config = new JDMAConfig.JDMAConfigBuilder()
                .siteId(siteId) // 站点编码
                .channel("自分发") //渠道
                .appVersionName(versionName) // 版本名称
                .appVersionCode(String.valueOf(versionCode)) // 版本号
                .appBuildId(String.valueOf(versionCode)) // build号
                .appDevice(JDMAConfig.ANDROID) // android设备类型，分别有”ANDROID“，”ANDROID-HD“
                .uid(UUID) // 唯一ID
                .channel(channel)
                .JDMABaseInfo(baseInfo) // 基础信息，SDK来动态传入涉及隐私的字段
                .build();
        JDMA.startWithConfig(context, config);
        mLogger = analyzeLogger == null ? new Logger.DefaultLogger() : analyzeLogger;
    }

    public static Logger getLogger() {
        return mLogger == null ? new Logger.DefaultLogger() : mLogger;
    }

    public interface Logger{
        void log(String tag, String content);

        void logLocal(String tag, String content);

        void logOnline(String tag, String content);

        class DefaultLogger implements Logger {
            @Override
            public void log(String tag, String content) {
                Log.i(tag, content);
            }

            @Override
            public void logLocal(String tag, String content) {
                Log.i(tag, content);
            }

            @Override
            public void logOnline(String tag, String content) {
                Log.i(tag, content);
            }
        }
    }

}
