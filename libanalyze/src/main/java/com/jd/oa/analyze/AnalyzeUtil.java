package com.jd.oa.analyze;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.jingdong.jdma.JDMA;
import com.jingdong.jdma.minterface.ClickInterfaceParam;
import com.jingdong.jdma.minterface.PvInterfaceParam;

import java.util.HashMap;
import java.util.Map;

public class AnalyzeUtil {
    private static final String TAG = "EVE";

    public static void onEventPage(Context cx, String pageName, String pageParam, String eventId, String pin, String paramValue,
                                   String nextPageName, String shopId, String pageId, HashMap<String, String> params) {
        if (TextUtils.isEmpty(paramValue) && params != null) {
            paramValue = new Gson().toJson(params);
        }
        ClickInterfaceParam clickInterfaceParam = new ClickInterfaceParam();
        clickInterfaceParam.page_name = pageName;
        clickInterfaceParam.page_param = pageParam;
        clickInterfaceParam.event_id = eventId;
        clickInterfaceParam.event_param = paramValue;
        clickInterfaceParam.next_page_name = nextPageName;
        clickInterfaceParam.shop = shopId;
        clickInterfaceParam.page_id = pageId;
        clickInterfaceParam.pin = pin;
        clickInterfaceParam.map = params;
        MEAnalyze.getLogger().logLocal(TAG, "onEventPage:" + clickInterfaceParam.toMap());
        JDMA.sendClickData(cx, clickInterfaceParam);
    }

    public static void onEventClick(Context cx, String pageName, String pageParam, String eventId, String pin, String paramValue,
                                    String nextPageName, String shopId, String pageId, HashMap<String, String> params) {
        if (TextUtils.isEmpty(paramValue) && params != null) {
            paramValue = new Gson().toJson(params);
        }
        ClickInterfaceParam clickInterfaceParam = new ClickInterfaceParam();
        clickInterfaceParam.page_name = pageName;
        clickInterfaceParam.page_param = pageParam;
        clickInterfaceParam.event_id = eventId;
        clickInterfaceParam.event_param = paramValue;
        clickInterfaceParam.next_page_name = nextPageName;
        clickInterfaceParam.shop = shopId;
        clickInterfaceParam.page_id = pageId;
        clickInterfaceParam.pin = pin;
        clickInterfaceParam.map = params;
        MEAnalyze.getLogger().logLocal(TAG, "onEventClick:" + clickInterfaceParam.toMap());
        JDMA.sendClickData(cx, clickInterfaceParam);
    }

    public static void onEventClickWithLocation(Context cx, String pageName, String pageParam, String eventId, String pin, String paramValue,
                                                String nextPageName, String shopId, String pageId, String lat, String lon, HashMap<String, String> params) {
        if (TextUtils.isEmpty(paramValue) && params != null) {
            paramValue = new Gson().toJson(params);
        }
        ClickInterfaceParam clickInterfaceParam = new ClickInterfaceParam();
        clickInterfaceParam.page_name = pageName;
        clickInterfaceParam.page_param = pageParam;
        clickInterfaceParam.event_id = eventId;
        clickInterfaceParam.event_param = paramValue;
        clickInterfaceParam.next_page_name = nextPageName;
        clickInterfaceParam.shop = shopId;
        clickInterfaceParam.page_id = pageId;
        clickInterfaceParam.pin = pin;
        clickInterfaceParam.map = params;
        if (!TextUtils.isEmpty(lat)) clickInterfaceParam.lat = lat;
        if (!TextUtils.isEmpty(lon)) clickInterfaceParam.lon = lon;
        MEAnalyze.getLogger().logLocal(TAG, "onEventClickWithLocation:" + clickInterfaceParam.toMap());
        JDMA.sendClickData(cx, clickInterfaceParam);
    }

    public static void onEventPagePV(Context cx, String pageName, String pageId, HashMap<String, String> param) {
        String pageParams = "";
        if (param != null) {
            pageParams = new Gson().toJson(param);
        }
        PvInterfaceParam pageInterfaceParam = new PvInterfaceParam();
        pageInterfaceParam.page_id = pageId;
        pageInterfaceParam.page_param = pageParams;
        pageInterfaceParam.page_name = pageName;
        pageInterfaceParam.map = param;
        MEAnalyze.getLogger().logLocal(TAG, "onEventPagePV:" + pageInterfaceParam.toMap());
        JDMA.sendPvData(cx, pageInterfaceParam);
    }

    public static void onEventPagePV(Context cx, String pageName, String pageParam, String pin, String pageId, HashMap<String, String> params) {
        if (TextUtils.isEmpty(pageParam) && params != null) {
            pageParam = new Gson().toJson(params);
        }
        PvInterfaceParam pageInterfaceParam = new PvInterfaceParam();
        pageInterfaceParam.page_name = pageName;
        pageInterfaceParam.page_param = pageParam;
        pageInterfaceParam.pin = pin;
        pageInterfaceParam.page_id = pageId;
        pageInterfaceParam.map = params;
        MEAnalyze.getLogger().logLocal(TAG, "onEventPagePV:" + pageInterfaceParam.toMap());
        JDMA.sendPvData(cx, pageInterfaceParam);
    }

    // NEW 页面埋点
    public static void clickPageId(Context cx, String pageId, String eventId, Map<String, String> pageParam, String pin, HashMap<String, String> eventParams) {
        ClickInterfaceParam interfaceParam = new ClickInterfaceParam();
        interfaceParam.event_id = eventId;
        interfaceParam.page_id = pageId;
        interfaceParam.page_param = new Gson().toJson(pageParam);
        interfaceParam.event_param = new Gson().toJson(eventParams);
        interfaceParam.map = eventParams;
        interfaceParam.pin = pin;
        MEAnalyze.getLogger().logLocal(TAG, "clickPageId:" + interfaceParam.toMap());
        JDMA.sendClickData(cx, interfaceParam);
    }

    // NEW PV
    public static void eventPV(Context cx, String pageId, HashMap<String, String> pageParam, String pin) {
        PvInterfaceParam interfaceParam = new PvInterfaceParam();
        interfaceParam.page_id = pageId;
        interfaceParam.page_param = new Gson().toJson(pageParam);
        interfaceParam.pin = pin;
        MEAnalyze.getLogger().logLocal(TAG, "eventPV:" + interfaceParam.toMap());
        JDMA.sendPvData(cx, interfaceParam);
    }


}
