// me app的配置在这里
ext {
    ME_VERSION_NAME = project.ME_VERSION_NAME

    SUBBUS_COMPILE_SDK_VERSION = COMPILE_SDK_VERSION
    SUBBUS_BUILD_TOOLS_VERSION = BUILD_TOOLS_VERSION

    SUBBUS_MIN_SDK_VERSION = MIN_SDK_VERSION
    SUBBUS_TARGET_SDK_VERSION = TARGET_SDK_VERSION

    // support依赖支持包
    SUPPORT_VERSION = "28.0.0"
    COMPILE_SUPPORT = [
            annotations : 'androidx.annotation:annotation:1.0.0',
            appcompat   : 'androidx.appcompat:appcompat:1.6.1',
            cardview    : 'androidx.cardview:cardview:1.0.0',
            design      : 'com.google.android.material:material:1.0.0',
            recyclerview: 'androidx.recyclerview:recyclerview:1.2.0',
    ]
    SUBBUS_COMPILE_SUPPORT = COMPILE_SUPPORT

    // 常用库包
    constraintVersion = "1.1.3"
    gsonVersion = "2.8.7"
    glideVersion = "4.9.0"
    glideTransformationsVersion = "2.0.1"
    eventbusVersion = "2.4.1"
    okhttpVersion = "3.12.13"
    lifecycleVersion = "1.1.0"
    wcdbVersion = "1.0.8"
    multidexVersion = "1.0.3"
    wheelPickerVersion = "1.5.4"
    photoViewVersion = "1.2.4"
    protobufVersion = "2.5.0"
    subsamplingScaleImageViewVersion = '3.10.0'
    fastjsonVersion = '1.2.51'
    aroundVersion = '1.6.2-SNAPSHOT'
    COMPILE_COMMON = [
            gson: "com.google.code.gson:gson:${gsonVersion}"
    ]
    //kotlinVersion = "1.3.31"
    coroutinesVersion = "1.3.5"
    kotlinVersion = "1.6.21"
    contextInjector = "1.0.8"
    maeAlbumVersion = "1.2.8"

    constraintlayoutVersion = "2.1.0"

    meMeeting = "3.6.1"

    meDependences = [
            saas_address                : ":saas_address",
    ]
}