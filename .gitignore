*.iml
.DS_Store
/app/proguardMapping.txt
.gradle

/localrepo
/local.properties

/.idea
/.idea/workspace.xml
/.idea/libraries

/build
build

build/
# Built application files
*.apk
*.ap_
# Files for the Dalvik VM
*.dex
# Java class files
*.class
# Generated files
bin/
gen/
# Gradle files
.gradle/
app/rocoofix/
# Local configuration file (sdk path, etc)
local.properties
# Proguard folder generated by Eclipse
proguard/
# Log Files
*.log
*.iml
jdme_android.iml
app.iml
#gradle
#gradle/*
welfareplugin/*
;buildsrc/
welfareplugin/
gradlew
aura.json
JIMCore/
TimLineLib/
/app/release/

/libweb2/src/main/assets/toumi
/jdme_flutter/pubspec.lock
/key/*
#/scripts/*
/app/debug/output.json
/joywork_app
/*.hprof
*/*.hprof
.trae/
.vscode/
buglybin/
.cursorignore
dependencies.txt
dependencies-graph.html
