if (isAppModule.toBoolean()) {
    apply plugin: 'com.android.application'
} else {
    apply plugin: 'com.android.library'
    apply plugin: 'com.jfrog.artifactory'
    apply plugin: 'maven-publish'
}

apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.chenenyu.router'

android {
    namespace 'com.jd.oa.experience'

    compileSdk COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        versionCode 227
        versionName ME_VERSION_NAME

        if (isAppModule.toBoolean()) {
            applicationId "com.jd.oa.experience.demo"
            manifestPlaceholders = [
                    notificationClickAction: "com.jd.oa_notification_click",
                    noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                    PNAME                  : applicationId ?: ""
            ]
        }

        //显示服务器选择器
        buildConfigField 'Boolean', 'SHOW_SERVER_SWITCHER', project.SHOW_SERVER_SWITCHER.toLowerCase()

        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled = true
    }

//    flavorDimensions.addAll(flavor_dimensions)
//    productFlavors product_flavors

    lint {
        abortOnError false
    }

    sourceSets {
        main {
            if (!isAppModule.toBoolean()) {
                manifest.srcFile 'src/main/AndroidManifest.xml'
            } else {
                manifest.srcFile 'src/main/demo/AndroidManifest.xml'
                java.srcDirs 'src/main/demo/java'
            }
        }
    }

//    repositories {
//        flatDir {
//            dirs 'lib_outer'
//        }
//    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.3.1'
//    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.3.1'
    implementation 'androidx.webkit:webkit:1.3.0'

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

    implementation COMPILE_SUPPORT.design
    implementation COMPILE_SUPPORT.annotations
    implementation COMPILE_SUPPORT.recyclerview
    implementation COMPILE_COMMON.gson
    implementation COMPILE_SUPPORT.cardview

//    implementation 'com.chenenyu.router:router:1.5.2'
//    kapt 'com.chenenyu.router:compiler:1.5.1'

    // support package
    api "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation "androidx.constraintlayout:constraintlayout:$constraintlayoutVersion"

    implementation 'com.githang:status-bar-compat:latest.integration'
    implementation 'androidx.multidex:multidex:2.0.0'

    implementation libs.lib.utils
    implementation project(':common')
    implementation project(':login')
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    implementation "com.github.bumptech.glide:glide:$glideVersion"
    implementation 'com.githang:status-bar-compat:latest.integration'
    implementation 'co.infinum:materialdatetimepicker-support:3.2.2'

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.2'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.2'

    implementation 'com.github.mmin18:realtimeblurview:1.2.1'

    api('com.jd.oa:mae-bundles-widget:1.0.7-SNAPSHOT') {
        exclude group: 'com.squareup.okhttp3'
    }
    api(libs.lib.utils)
    implementation("com.jd.oa:around:${aroundVersion}") {
        exclude group: 'com.squareup.okhttp3'
        exclude group: 'com.jd.oa', module: 'network'
    }
    implementation 'com.airbnb.android:lottie:5.2.0'
    implementation 'com.hyman:flowlayout-lib:1.1.2'

    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
}

if (!isAppModule.toBoolean()) {
    ext {
        version_name = '0.0.1-SNAPSHOT'
    }

    apply from: "../publish_to_artifactory.gradle"
}
