<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.jd.oa">

    <application>
        <activity
            android:name=".experience.activity.HRAppsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".experience.activity.ChangeMyBgActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".experience.activity.MyInstructionActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".experience.activity.MyHomePageActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".experience.activity.HomePageDutyActivity"
            android:configChanges="keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name=".business.groupicon.GroupIconModifyActivity"
            android:configChanges="keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
    </application>
</manifest>