<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AutoSoftKeyboardDialogStyle" parent="BottomDialogStyle">
        <item name="android:windowSoftInputMode">stateAlwaysVisible</item><!--显示软件盘-->
    </style>

    <style name="RecommendSummaryItemTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">4dp</item>
        <item name="android:layout_marginEnd">4dp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingStart">4dp</item>
        <item name="android:paddingEnd">4dp</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:paddingBottom">2dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:visibility">invisible</item>
    </style>

    <style name="TransparentDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>


    <style name="BottomSheetEdit" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="ExpSwitchStyle">
        <item name="android:thumb">@drawable/jdme_exp_switch_thumb_selector</item>
        <item name="track">@drawable/jdme_exp_switch_track_selector</item>
    </style>

    <style name="ExpSwitchStyle2">
        <item name="android:thumb">@drawable/jdme_exp_switch_thumb_selector2</item>
        <item name="track">@drawable/jdme_exp_switch_track_selector2</item>
    </style>

    <style name="ExpDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:backgroundDimAmount">0.3</item>
    </style>

    <declare-styleable name="PagerSnapRecyclerView">
        <attr name="autoScrollEnable" format="boolean" />
    </declare-styleable>

</resources>