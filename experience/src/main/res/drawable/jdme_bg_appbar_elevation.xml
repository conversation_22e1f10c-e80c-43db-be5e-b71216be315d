<selector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <!--折叠状态下的阴影-->
    <item app:state_collapsed="true">
        <objectAnimator
            android:propertyName="elevation"
            android:valueTo="0dp"
            android:valueType="floatType" />
    </item>
    <!--展开状态下的阴影-->
    <item app:state_collapsed="false">
        <objectAnimator
            android:propertyName="elevation"
            android:valueTo="0dp"
            android:valueType="floatType" />
    </item>
</selector>