<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />
            <solid android:color="#0DE10D0D" />
            <corners android:radius="5dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />
            <solid android:color="#10CCCCCC" />
            <corners android:radius="5dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />
            <solid android:color="#15CCCCCC" />
            <corners android:radius="5dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />
            <solid android:color="#20CCCCCC" />
            <corners android:radius="5dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="1dp"
                android:right="1dp"
                android:top="1dp" />
            <solid android:color="#ece9e9" />
            <corners android:radius="5dp" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="#FFF" />
            <corners android:radius="5dp" />
            <size android:height="30dp" android:width="50dp"/>
        </shape>
    </item>
</layer-list>