<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFA"
    android:focusable="true"
    android:focusableInTouchMode="true">
    <!--皮肤背景-->
    <FrameLayout
        android:id="@+id/skin_bkgnd_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/exp_skin_layout_height"
        android:background="#FAFAFA">
        <ImageView
            android:id="@+id/iv_theme_center"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|top"
            android:scaleType="centerCrop"/>
        <ImageView
            android:id="@+id/iv_theme_left"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="start|top"
            android:scaleType="fitStart"/>
        <ImageView
            android:id="@+id/iv_theme_right"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="end|top"
            android:scaleType="fitEnd"/>
    </FrameLayout>

    <include layout="@layout/jdme_fragment_experience_main"
        tools:visibility="visible"/>
    <!--骨架屏-->
    <include layout="@layout/jdme_fragment_experience_empty"
        tools:visibility="gone"/>

    <include layout="@layout/jdme_fragment_experience_main_avatar_layer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</FrameLayout>