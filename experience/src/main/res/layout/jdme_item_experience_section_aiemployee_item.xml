<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_ai_employee_container"
    android:layout_width="120dp"
    android:layout_height="match_parent"
    android:paddingTop="12dp"
    android:paddingStart="8dp"
    android:paddingBottom="16dp"
    android:paddingEnd="12dp"
    android:layout_marginEnd="8dp"
    android:alpha="0.8"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/jdme_all_round_8">

    <TextView
        android:id="@+id/tv_ai_employee_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="#1B1B1B"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="开个证明"/>

    <TextView
        android:id="@+id/tv_ai_employee_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textSize="12sp"
        android:textColor="#9D9D9D"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="@id/tv_ai_employee_title"
        app:layout_constraintTop_toBottomOf="@id/tv_ai_employee_title"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="人事证明 智能办理"/>

</androidx.constraintlayout.widget.ConstraintLayout>