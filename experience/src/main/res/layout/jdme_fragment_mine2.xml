<?xml version="1.0" encoding="utf-8"?>

<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_setting_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/layout_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="180dp"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_img_mine_bg" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="4dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/jdme_img_mine_bg_top"
                android:paddingLeft="24dp"
                android:paddingTop="24dp"
                android:paddingRight="24dp"
                android:paddingBottom="12dp">

                <LinearLayout
                    android:id="@+id/layout_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/rl_avatar"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tv_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:singleLine="true"
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="28dp"
                        android:textStyle="bold"
                        tools:text="JohnJohn" />

                    <ImageView
                        android:id="@+id/iv_qrcode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="10dp"
                        android:paddingEnd="5dp"
                        android:src="@drawable/jdme_icon_qrcode" />

                    <FrameLayout
                        android:id="@+id/layout_birthday"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="2dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <pl.droidsonroids.gif.GifImageView
                            android:id="@+id/iv_birthday_bg"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:scaleType="fitXY"
                            android:src="@drawable/jdme_icon_birthday_bg" />

                        <ImageView
                            android:id="@+id/iv_birthday"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_gravity="center"
                            android:layout_marginBottom="2dp"
                            android:scaleType="fitCenter"
                            android:visibility="invisible"
                            tools:src="@drawable/jdme_icon_badge_birthday"
                            tools:visibility="visible" />

                        <ImageView
                            android:id="@+id/iv_medal"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_gravity="center"
                            android:layout_marginTop="2dp"
                            android:scaleType="fitCenter"
                            android:visibility="invisible"
                            tools:src="@drawable/jdme_icon_badge_medal_gold" />

                        <ImageView
                            android:id="@+id/iv_party"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_gravity="center"
                            android:scaleType="fitCenter"
                            android:visibility="invisible"
                            tools:src="@drawable/jdme_icon_badge_medal_gold" />

                        <TextView
                            android:id="@+id/tv_company_age"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:textColor="@color/white"
                            android:textSize="10sp"
                            android:visibility="invisible"
                            tools:text="4" />
                    </FrameLayout>

                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/rl_avatar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.jd.oa.ui.CircleImageView
                        android:id="@+id/iv_avatar"
                        android:layout_width="67dp"
                        android:layout_height="67dp"
                        android:layout_centerInParent="true"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="5dp"
                        android:src="@drawable/jdme_picture_user_default_white"
                        tools:src="@mipmap/img_default" />

                    <ImageView
                        android:id="@+id/iv_avatar_bg"
                        android:layout_width="72dp"
                        android:layout_height="72dp"
                        android:layout_centerInParent="true"
                        tools:background="@color/black" />
                </RelativeLayout>


                <LinearLayout
                    android:id="@+id/layout_peer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/rl_avatar">

                    <ImageView
                        android:id="@+id/image_peer_1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:scaleType="fitCenter" />

                    <ImageView
                        android:id="@+id/image_peer_2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="1dp"
                        android:scaleType="fitCenter" />

                    <ImageView
                        android:id="@+id/image_peer_3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="1dp"
                        android:scaleType="fitCenter" />

                    <ImageView
                        android:id="@+id/image_peer_4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="1dp"
                        android:scaleType="fitCenter" />

                    <ImageView
                        android:id="@+id/image_peer_5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="1dp"
                        android:scaleType="fitCenter"
                        android:visibility="gone" />
                </LinearLayout>


                <com.google.android.flexbox.FlexboxLayout
                    android:id="@+id/layout_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    app:flexDirection="row"
                    app:flexWrap="wrap"
                    app:layout_constraintEnd_toStartOf="@id/rl_avatar"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_name">

                    <TextView
                        android:id="@+id/tv_job"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="15sp"
                        tools:text="交互设计师岗交互设计师岗交互设" />

                    <TextView
                        android:id="@+id/tv_work_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="15sp"
                        tools:text="在职1099天" />
                </com.google.android.flexbox.FlexboxLayout>

                <View
                    android:id="@+id/view_divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="36dp"
                    android:background="#eef1f4"
                    app:layout_constraintTop_toBottomOf="@id/layout_info" />

                <LinearLayout
                    android:id="@+id/layout_signature"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="@id/view_divider">

                    <TextView
                        android:id="@+id/tv_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:hint="@string/me_signature_hint"
                        android:paddingTop="15dp"
                        android:paddingBottom="15dp"
                        android:singleLine="true"
                        android:textColor="@color/me_setting_foreground_light"
                        android:textSize="13sp"
                        tools:text="签名签名签名签名签名签名签名签名签名" />

                    <ImageView
                        android:id="@+id/iv_signature_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:src="@drawable/jdme_icon_signature_edit" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/medium_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:visibility="gone"
            tools:visibility="visible"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="8dp">

            <View
                android:id="@+id/view_vertical_divider"
                android:layout_width="0.5dp"
                android:layout_height="42dp"
                android:background="#d6dbe1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/layout_score"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="@id/view_vertical_divider"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="bottom"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textColor="@color/me_setting_foreground_red"
                        android:textSize="25dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="3dp"
                        android:text="@string/me_mine_score"
                        android:textColor="@color/me_setting_foreground_light"
                        android:textSize="12dp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_mine_welfare_score"
                    android:textColor="@color/me_setting_foreground"
                    android:textSize="15dp" />
            </LinearLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:src="@drawable/jdme_icon_arrow_right_gray"
                app:layout_constraintBottom_toBottomOf="@id/layout_score"
                app:layout_constraintLeft_toRightOf="@id/layout_score"
                app:layout_constraintTop_toTopOf="@id/layout_score" />

            <LinearLayout
                android:id="@+id/layout_holiday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:layout_constraintLeft_toRightOf="@id/view_vertical_divider"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="bottom"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_holiday"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textColor="@color/me_setting_foreground_red"
                        android:textSize="25dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="3dp"
                        android:text="@string/me_mine_hour"
                        android:textSize="12dp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_mine_my_holiday"
                    android:textColor="@color/me_setting_foreground"
                    android:textSize="15dp" />
            </LinearLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:src="@drawable/jdme_icon_arrow_right_gray"
                app:layout_constraintBottom_toBottomOf="@id/layout_holiday"
                app:layout_constraintLeft_toRightOf="@id/layout_holiday"
                app:layout_constraintTop_toTopOf="@id/layout_holiday" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layout_app_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/me_setting_background" />

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <com.jd.oa.business.setting.settingitem.SettingItem
                    android:id="@+id/setting"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:setting_icon="@drawable/jdme_icon_setting"
                    app:setting_name="@string/me_mine_setting"
                    app:setting_show_divider="true" />

                <com.jd.oa.badge.RedDotView
                    android:id="@+id/badge_view"
                    android:layout_gravity="right|center_vertical"
                    android:layout_marginEnd="26dp"
                    android:layout_width="8dp"
                    android:layout_height="8dp" />
            </FrameLayout>

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/feedback"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_icon="@drawable/jdme_icon_feedback"
                app:setting_name="@string/me_my_feedback"
                android:visibility="gone"
                app:setting_show_divider="true" />
            <LinearLayout
                android:id="@+id/llShow"
                android:background="#ffffffff"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/idShowLetter"
                    android:layout_marginBottom="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginLeft="12dp"
                    android:text="显示欢迎页"
                    android:textColor="#2e2d2d"
                    android:textSize="16sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp" />
    </LinearLayout>
</ScrollView>
