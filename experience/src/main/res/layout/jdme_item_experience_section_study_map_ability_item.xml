<?xml version="1.0" encoding="utf-8"?>
<!--集团全员合规培训 - 体系级-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    tools:layout_height="69dp"
    android:layout_weight="1"
    android:background="@drawable/jdme_bg_study_map_ability_item">

    <TextView
        android:id="@+id/level_name_tv"
        android:layout_width="54dp"
        android:layout_height="17dp"
        android:background="@drawable/jdme_bg_study_map_level_name"
        android:paddingHorizontal="4dp"
        android:layout_gravity="end|top"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#FF6B23"
        android:textSize="10dp"
        android:includeFontPadding="false"/>

    <TextView
        android:id="@+id/source_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="100dp"
        android:layout_gravity="start|bottom"
        android:layout_marginStart="12dp"
        android:layout_marginBottom="14dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#E25300"
        android:textSize="11dp"
        android:includeFontPadding="false"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="14dp"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/complete_status_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="40dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#FFA333"
            android:textSize="11dp"
            android:includeFontPadding="false"/>
        <TextView
            android:id="@+id/study_duration_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="80dp"
            android:layout_marginStart="4dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#FFA32E"
            android:textSize="11dp"
            android:includeFontPadding="false"/>
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:layout_marginTop="17dp">
        <TextView
            android:id="@+id/course_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/new_course"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_bias="0"
            android:maxLines="1"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#232930"
            android:textSize="14dp"
            android:includeFontPadding="false"/>
        <ImageView
            android:id="@+id/new_course"
            android:layout_width="26dp"
            android:layout_height="14dp"
            android:layout_marginStart="4dp"
            app:layout_constraintStart_toEndOf="@id/course_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/jdme_bg_exp_study_map_new_label"
            android:visibility="gone"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>