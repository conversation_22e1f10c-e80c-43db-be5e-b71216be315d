<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:overScrollMode="never"
    android:scrollbars="none">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="#ffffff">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">
            <com.jd.oa.ui.IconFontView
                android:id="@+id/close_btn"
                android:layout_width="44dp"
                android:layout_height="match_parent"
                android:layout_marginStart="3dp"
                android:layout_gravity="start"
                android:gravity="center"
                android:textSize="@dimen/JMEIcon_20"
                android:textColor="#333333"
                android:text="@string/icon_prompt_close"/>
            <TextView
                android:id="@+id/save_btn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:layout_gravity="end"
                android:gravity="center"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="16dp"
                android:textColor="#FE3E33"
                android:text="@string/exp_homepage_duty_confirm"
                android:includeFontPadding="false"
                android:visibility="visible"/>
        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/jdme_bg_homepage_icon_work_duty"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:maxLines="1"
                android:ellipsize="end"
                android:textStyle="bold"
                android:textColor="#232930"
                android:textSize="24dp"
                android:text="@string/exp_homepage_instruction_work_duty"
                android:includeFontPadding="false"/>
        </LinearLayout>

        <TextView
            android:id="@+id/business_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#666666"
            android:textSize="14dp"
            android:text="@string/exp_homepage_duty_business_title"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/business_no_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="40dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#CDCDCD"
            android:textSize="12dp"
            android:text="@string/exp_homepage_duty_business_no_tag"
            android:includeFontPadding="false"/>

        <com.zhy.view.flowlayout.TagFlowLayout
            android:id="@+id/business_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="30dp"/><!--16-8, 16-6, 16-8, 40-10-->

        <TextView
            android:id="@+id/content_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#666666"
            android:textSize="14dp"
            android:text="@string/exp_homepage_duty_content_title"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/content_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_bg_homepage_duty_desc"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="10dp"
            android:padding="8dp"
            android:maxLines="4"
            android:ellipsize="end"
            android:textColor="#999999"
            android:textSize="14dp"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/content_no_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="40dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#CDCDCD"
            android:textSize="12dp"
            android:text="@string/exp_homepage_duty_content_no_tag"
            android:includeFontPadding="false"/>

        <com.zhy.view.flowlayout.TagFlowLayout
            android:id="@+id/content_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="30dp"/><!--16-8, 16-6, 16-8, 40-10-->

        <TextView
            android:id="@+id/other_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="10dp"
            android:textStyle="bold"
            android:textColor="#666666"
            android:textSize="14dp"
            android:text="@string/exp_homepage_duty_other_title"
            android:includeFontPadding="false"/>

        <FrameLayout
            android:id="@+id/other_view"
            android:layout_width="match_parent"
            android:layout_height="143dp"
            android:layout_marginHorizontal="16dp"
            android:background="@drawable/jdme_bg_homepage_duty_edit">
            <EditText
                android:id="@+id/other_et"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:layout_marginTop="9dp"
                android:layout_marginBottom="34dp"
                android:background="@null"
                android:gravity="start|top"
                android:inputType="textMultiLine"
                android:maxLength="200"
                android:textColorHint="#CDCDCD"
                android:hint="@string/exp_homepage_duty_other_hint"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text=""
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/other_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|bottom"
                android:layout_marginEnd="7dp"
                android:layout_marginBottom="8dp"
                android:textColor="#999999"
                android:textSize="14dp"
                android:text="0/200"
                android:includeFontPadding="false"/>
        </FrameLayout>

    </LinearLayout>
</ScrollView>