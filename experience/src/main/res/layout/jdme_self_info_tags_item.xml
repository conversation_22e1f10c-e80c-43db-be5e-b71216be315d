<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/top_margin"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/self_info_item_bkgnd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"
        android:background="@drawable/jdme_bg_self_info_middle">
        <TextView
            android:id="@+id/self_info_item_name"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_marginTop="17dp"
            android:layout_marginEnd="10dp"
            android:layout_gravity="top"
            android:gravity="center_vertical"
            android:textSize="16dp"
            android:textColor="#2E2D2D"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"/>
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:minHeight="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp">

            <com.zhy.view.flowlayout.TagFlowLayout
                android:id="@+id/self_info_item_tags"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="18dp"
                android:paddingBottom="9dp"
                app:tag_gravity="right"/>

            <TextView
                android:id="@+id/self_info_item_tags_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:textSize="14dp"
                android:textColor="#CDCDCD"
                android:visibility="gone"
                android:maxLines="1"
                android:ellipsize="end"
                android:includeFontPadding="false"/>
        </FrameLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/self_info_item_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:layout_marginTop="18dp"
            android:text="@string/icon_direction_right"
            android:textColor="#BFC1C4"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/self_info_item_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#ffffff"
        android:visibility="gone">
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="#EBEDF0"/>
    </FrameLayout>

    <View
        android:id="@+id/bottom_margin"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:visibility="gone"/>
</LinearLayout>