<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginHorizontal="8dp"
    android:layout_width="match_parent"
    android:layout_height="0dp">

    <View
        android:id="@+id/iv_bg_gradient"
        android:layout_width="0dp"
        android:layout_height="155dp"
        android:background="@drawable/experience_ai_employee_bg"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    <TextView
        android:id="@+id/tv_title_main"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginStart="15dp"
        android:maxLines="1"
        android:maxWidth="200dp"
        android:ellipsize="end"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#4B78FF"
        app:layout_constraintTop_toTopOf="@id/iv_bg_gradient"
        app:layout_constraintStart_toStartOf="@id/iv_bg_gradient"
        tools:text="你好，我是数字员工 Ella"/>

    <LinearLayout
        android:id="@+id/ll_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/tv_title_main"
        app:layout_constraintStart_toStartOf="@id/tv_title_main">
        <TextView
            android:id="@+id/tv_title_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:layout_gravity="center_vertical"
            android:maxWidth="250dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#4B78FF"
            tools:text="带你玩转职场生活，找我聊聊"/>
        <ImageView
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/experience_ai_employee_arrow"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="5dp"/>
    </LinearLayout>

    <com.jd.oa.experience.view.ChronicleRecyclerView
        android:id="@+id/rv_ai_employee"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="93dp"
        android:layout_marginBottom="12dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="20dp"
        android:orientation="horizontal"
        android:elevation="1dp"
        android:fadingEdgeLength="17dp"
        app:fadingEdgeRight="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/iv_ai_employee_image"
        android:layout_width="94dp"
        android:layout_height="105dp"
        android:layout_marginBottom="-12dp"
        android:layout_marginEnd="9dp"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>