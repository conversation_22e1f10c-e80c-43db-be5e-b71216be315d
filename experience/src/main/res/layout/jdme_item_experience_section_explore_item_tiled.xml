<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="87dp"
    android:background="@drawable/jdme_bg_gray_corner">

    <com.jd.oa.ui.SimpleRoundImageView
        android:id="@+id/explore_item_iv"
        android:layout_width="154dp"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:round_radius="8dp"/>
    <TextView
        android:id="@+id/explore_item_name_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="166dp"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="12dp"
        android:layout_gravity="top"
        android:gravity="start"
        android:maxLines="2"
        android:ellipsize="end"
        android:lineSpacingExtra="4dp"
        android:textStyle="bold"
        android:textColor="#232930"
        android:textSize="14dp"
        android:includeFontPadding="false"/>

    <TextView
        android:id="@+id/explore_item_source_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="166dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="10dp"
        android:layout_gravity="bottom"
        android:gravity="start"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#999999"
        android:textSize="11dp"
        android:includeFontPadding="false"/>
</FrameLayout>