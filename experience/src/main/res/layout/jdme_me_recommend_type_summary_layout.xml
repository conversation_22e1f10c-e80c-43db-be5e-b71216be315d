<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="263dp"
    android:layout_marginTop="4dp"
    android:background="@drawable/jdme_bg_shadow_corner"
    android:orientation="vertical"
    android:paddingStart="12dp"
    android:paddingTop="12dp"
    android:paddingEnd="12dp">

    <include layout="@layout/jdme_me_recommend_header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"/>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="4dp"
        android:layout_weight="1">

        <com.jd.oa.ui.SimpleRoundImageView
            android:id="@+id/iv_content_bg_gradient"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            app:round_radius="6dp"/>

        <com.jd.oa.ui.SimpleRoundImageView
            android:id="@+id/iv_content_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitEnd"
            app:round_radius="6dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="6dp"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#FEFFFE"
                android:textSize="11dp"
                android:textAlignment="center"
                android:includeFontPadding="false"
                tools:text="2021/11/29" />

            <com.jd.oa.ui.WrapContentViewPager
                android:id="@+id/vp_summary"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>
    </FrameLayout>

    <com.jd.oa.viewpager.indicator.CirclePageIndicator
        android:id="@+id/page_indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        app:centered="true"
        app:circleSpacing="6dp"
        app:fillColor="#333333"
        app:pageColor="#E6E6E6"
        app:radius="2.5dp"
        app:strokeWidth="0dp" />

    <include
        layout="@layout/jdme_me_recommend_footer_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>