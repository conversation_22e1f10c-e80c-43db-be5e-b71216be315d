<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:background="@drawable/jdme_bg_shadow_corner"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_chart_title"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="12dp">

        <ImageView
            android:id="@+id/iv_icon_chart_title"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/jdme_exp_worktime_icon_chart" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="41dp"
                android:layout_height="14dp"
                android:layout_marginStart="6dp"
                android:layout_marginTop="20dp"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_bg_section_title" />

            <TextView
                android:id="@+id/tv_chart_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:autoSizeMinTextSize="14dp"
                android:autoSizeTextType="uniform"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxWidth="178dp"
                android:maxLines="1"
                android:text="@string/exp_work_time_chart_title"
                android:textColor="#333"
                android:textSize="16dp"
                android:textStyle="bold" />
        </RelativeLayout>


        <TextView
            android:id="@+id/tv_chart_more"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center_vertical|end"
            android:maxWidth="90dp"
            android:maxEms="6"
            android:maxLines="1"
            android:text="@string/exp_work_time_more"
            android:textColor="#666666"
            android:textSize="14dp"
            android:visibility="gone"
            tools:text="查看更多"
            tools:visibility="visible" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_chart_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/icon_direction_right"
            android:textColor="#FF62656D"
            android:textSize="@dimen/JMEIcon_14"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>


    <TextView
        android:id="@+id/tv_chart_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="4dp"
        android:textColor="#666666"
        android:textSize="14dp"
        android:visibility="gone"
        tools:text="2022/09/01"
        tools:visibility="gone" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/jdme_bg_exp_worktime_bg"
        android:gravity="center"
        android:orientation="horizontal">

        <com.github.mikephil.charting.charts.PieChart
            android:id="@+id/pie_chart"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:background="@drawable/jdme_exp_worktime_chart_bg" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_work_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            tools:itemCount="5"
            tools:listitem="@layout/jdme_item_experience_work_time_list" />

        <LinearLayout
            android:id="@+id/ll_empty_view"
            android:layout_width="match_parent"
            android:layout_height="140dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/exp_bg_chart_emty" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:shadowRadius="3.0"
                android:text="@string/exp_work_time_chart_empty"
                android:textColor="#FF999999"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>


</LinearLayout>