<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="4dp"
    android:orientation="vertical"
    android:background="@drawable/jdme_bg_shadow_corner">

    <com.jd.oa.experience.view.TitleView
        android:id="@+id/title_view"
        android:layout_width="match_parent"
        android:layout_height="48dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="7dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="12dp"
        android:orientation="horizontal">
        <FrameLayout
            android:id="@+id/scan"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp">
            <View
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_gravity="top|center_horizontal"
                android:background="@drawable/quc_section_scan"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:gravity="bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_quc_scan"
                android:includeFontPadding="false"/>
        </FrameLayout>
        <FrameLayout
            android:id="@+id/pass"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp">
            <View
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_gravity="top|center_horizontal"
                android:background="@drawable/quc_section_pass"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:gravity="bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_quc_pass"
                android:includeFontPadding="false"/>
        </FrameLayout>
        <FrameLayout
            android:id="@+id/pay"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp">
            <View
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_gravity="top|center_horizontal"
                android:background="@drawable/quc_section_pay"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:gravity="bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_quc_pay"
                android:includeFontPadding="false"/>
        </FrameLayout>
        <FrameLayout
            android:id="@+id/wallet"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp">
            <View
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_gravity="top|center_horizontal"
                android:background="@drawable/quc_section_wallet"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:gravity="bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_quc_wallet"
                android:includeFontPadding="false"/>
        </FrameLayout>
    </LinearLayout>

    <View
        android:id="@+id/separator"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="14dp"
        android:background="#F2F2F2"/>

    <com.jd.oa.experience.view.PagerSnapRecyclerView
        android:id="@+id/app_list_rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="7dp"
        android:layout_marginTop="6dp"/>

</LinearLayout>