<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:id="@+id/layout"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:background="@drawable/jdme_bg_signature_edit"
        app:layout_behavior="com.jd.oa.business.mine.behavior.DragDialogBehavior">
        <FrameLayout
            android:id="@+id/layout_touch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/jdme_img_signature_top"/>
        </FrameLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/me_mine_add_work_signature"
            android:textColor="@color/me_setting_foreground"
            android:textStyle="bold"
            android:textSize="18sp"/>
        <EditText
            android:id="@+id/edit"
            android:layout_width="match_parent"
            android:layout_height="140dp"
            android:layout_marginTop="20dp"
            android:gravity="top"
            android:background="@color/white"
            android:textColor="@color/black"
            android:hint="@string/me_signature_hint"
            android:textColorHint="@color/comm_text_secondary"
            tools:text="签名签名签名签名签名签名"/>
        <Button
            style="@style/Base.Widget.AppCompat.Button.Borderless"
            android:id="@+id/btn_save"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/jdme_btn_bg_red"
            android:textSize="17sp"
            android:textColor="@color/white"
            android:text="@string/me_mine_save_signature"/>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
