<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="263dp"
    android:layout_marginTop="4dp"
    android:background="@drawable/jdme_bg_shadow_corner"
    android:orientation="vertical"
    android:paddingStart="12dp"
    android:paddingTop="12dp"
    android:paddingEnd="12dp">

    <include layout="@layout/jdme_me_recommend_header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/iv_content_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="12dp"
        android:layout_weight="1"
        app:cardBackgroundColor="@color/transparent"
        app:cardCornerRadius="6dp"
        app:cardElevation="0dp">
        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/iv_gif_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="gone"/>
        <ImageView
            android:id="@+id/iv_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="visible"/>
    </androidx.cardview.widget.CardView>
    <include
        layout="@layout/jdme_me_recommend_footer_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp" />
</LinearLayout>