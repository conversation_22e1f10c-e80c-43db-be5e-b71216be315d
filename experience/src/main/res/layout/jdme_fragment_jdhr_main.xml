<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.pulltorefresh.PullToRefreshLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:id="@+id/experience_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="44dp">
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:stateListAnimator="@drawable/jdme_bg_appbar_elevation">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapse"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <com.jd.oa.experience.view.ProfileLayout
                    android:id="@+id/profile_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_collapseMode="none"/>

                <FrameLayout
                    android:id="@+id/profile_avatar"
                    android:layout_width="54dp"
                    android:layout_height="54dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="8dp"
                    app:layout_collapseMode="none">
                    <com.jd.oa.elliptical.SuperEllipticalImageView
                        android:id="@+id/iv_profile_avatar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                    <ImageView
                        android:id="@+id/iv_profile_pendant"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                </FrameLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <!--viewpager-->
        <FrameLayout
            android:id="@+id/fl_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">
        </FrameLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</com.jd.oa.pulltorefresh.PullToRefreshLayout>