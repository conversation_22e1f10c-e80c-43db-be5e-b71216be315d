<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <ImageView
        android:id="@+id/custom_background_iv"
        android:layout_width="match_parent"
        android:layout_height="230dp"
        android:background="@drawable/jdme_bg_homepage_default_gradient"/>

    <View
        android:id="@+id/v_bg_mask"
        android:layout_width="match_parent"
        android:layout_height="230dp"
        android:background="@drawable/jdme_bg_homepage_mask_gradient"/>

    <com.jd.oa.pulltorefresh.PullToRefreshLayout
        android:id="@+id/homepage_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:focusableInTouchMode="true">
        <androidx.core.widget.NestedScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:overScrollMode="never"
            android:scrollbars="none">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/bk_click_area"
                    android:layout_width="match_parent"
                    android:layout_height="230dp">

                    <LinearLayout
                        android:id="@+id/ll_change_bg"
                        android:visibility="gone"
                        android:layout_marginTop="54dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/jdme_bg_exp_btn_change_bg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:orientation="horizontal"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="6dp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="12dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/exp_homepage_change_cover"
                            android:textColor="@color/white"
                            android:textSize="12dp" />
                    </LinearLayout>
                </RelativeLayout>


                <include layout="@layout/jdme_activity_my_homepage_header_info"
                    android:id="@+id/header_info_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-80dp"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#F5F5F5"/>

                <include layout="@layout/jdme_activity_my_homepage_badge"
                    android:id="@+id/badge_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <include layout="@layout/jdme_activity_my_homepage_manual"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="36dp"
                    android:layout_marginBottom="24dp"
                    android:layout_gravity="center_horizontal"
                    android:textColor="#999999"
                    android:textSize="14dp"
                    android:text="@string/exp_homepage_deadline"
                    android:includeFontPadding="false"/>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.jd.oa.pulltorefresh.PullToRefreshLayout>

    <LinearLayout
        android:id="@+id/mask_view"
        android:layout_width="match_parent"
        android:layout_height="88dp"
        android:paddingTop="44dp"
        android:orientation="horizontal"
        android:background="@color/transparent">
        <com.jd.oa.ui.IconFontView
            android:id="@+id/mask_view_back"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:gravity="center"
            android:text="@string/icon_direction_left"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/JMEIcon_20"/>
        <FrameLayout
            android:id="@+id/mask_view_avatar_layout"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:visibility="gone">
            <!--头像-->
            <com.jd.oa.elliptical.SuperEllipticalImageView
                android:id="@+id/mask_view_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/me_user_icon"
                android:src="@drawable/profile_section_avatar_default"/>
            <!--挂件-->
            <ImageView
                android:id="@+id/mask_view_pendant"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="invisible"
                android:contentDescription="@string/me_user_icon"/>
        </FrameLayout>
        <TextView
            android:id="@+id/mask_view_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="16dp"
            android:gravity="start"
            android:textStyle="bold"
            android:textColor="#2E2D2D"
            android:textSize="20dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:visibility="gone"/>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/thumbs_float_layout"
        android:layout_width="66dp"
        android:layout_height="66dp"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="134dp"
        android:background="@drawable/jdme_bg_homepage_thumps_up_bkgnd">
        <ImageView
            android:id="@+id/thumbs_up_one"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:src="@drawable/jdme_bg_homepage_thumbs_up"/>
    </FrameLayout>

    <!--430 + 140 marginBottom-->
    <FrameLayout
        android:id="@+id/lottie_view_container"
        android:layout_width="match_parent"
        android:layout_height="570dp"
        android:layout_gravity="bottom"/>

</FrameLayout>