<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="4dp"
    android:orientation="vertical"
    android:background="@drawable/jdme_bg_shadow_corner">

    <com.jd.oa.experience.view.TitleView
        android:id="@+id/title_view"
        android:layout_width="match_parent"
        android:layout_height="48dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/like_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:background="@drawable/jdme_bg_gray_corner">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="12dp"
                android:layout_marginTop="11dp"
                android:src="@drawable/jdme_bg_section_interaction_like_icon"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="11dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#666666"
                android:textSize="12dp"
                android:text="@string/exp_interaction_like"
                android:includeFontPadding="false"/>

            <View
                android:id="@+id/like_red_dot"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_marginStart="74dp"
                android:layout_marginTop="9dp"
                android:background="@drawable/jdme_bg_red_dot_interaction"
                android:visibility="gone"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/like_count_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:maxWidth="60dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginBottom="7.5dp"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#333333"
                    android:textSize="18dp"
                    android:includeFontPadding="false"
                    android:fontFamily="@font/din_condensed_bold"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="3dp"
                    android:layout_marginBottom="11dp"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#999999"
                    android:textSize="12dp"
                    android:text="@string/exp_interaction_like_unit"
                    android:includeFontPadding="false"/>
            </LinearLayout>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/liked_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginHorizontal="8dp"
            android:background="@drawable/jdme_bg_gray_corner">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="12dp"
                android:layout_marginTop="11dp"
                android:src="@drawable/jdme_bg_section_interaction_liked_icon"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="11dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#666666"
                android:textSize="12dp"
                android:text="@string/exp_interaction_liked"
                android:includeFontPadding="false"/>

            <View
                android:id="@+id/liked_red_dot"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_marginStart="74dp"
                android:layout_marginTop="9dp"
                android:background="@drawable/jdme_bg_red_dot_interaction"
                android:visibility="gone"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/liked_count_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:maxWidth="60dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginBottom="7.5dp"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#333333"
                    android:textSize="18dp"
                    android:includeFontPadding="false"
                    android:fontFamily="@font/din_condensed_bold"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="3dp"
                    android:layout_marginBottom="11dp"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#999999"
                    android:textSize="12dp"
                    android:text="@string/exp_interaction_like_unit"
                    android:includeFontPadding="false"/>
            </LinearLayout>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/home_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginEnd="16dp"
            android:background="@drawable/jdme_bg_gray_corner">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="12dp"
                android:layout_marginTop="11dp"
                android:src="@drawable/jdme_bg_section_interaction_home_icon"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="80dp"
                android:layout_marginStart="30dp"
                android:layout_marginTop="11dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#666666"
                android:textSize="12dp"
                android:text="@string/exp_homepage_title"
                android:includeFontPadding="false"/>

            <View
                android:id="@+id/home_red_dot"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_marginStart="74dp"
                android:layout_marginTop="9dp"
                android:background="@drawable/jdme_bg_red_dot_interaction"
                android:visibility="gone"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/home_count_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:maxWidth="60dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginBottom="7.5dp"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#333333"
                    android:textSize="18dp"
                    android:includeFontPadding="false"
                    android:fontFamily="@font/din_condensed_bold"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="3dp"
                    android:layout_marginBottom="11dp"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#999999"
                    android:textSize="12dp"
                    android:text="@string/exp_interaction_home_unit"
                    android:includeFontPadding="false"/>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</LinearLayout>