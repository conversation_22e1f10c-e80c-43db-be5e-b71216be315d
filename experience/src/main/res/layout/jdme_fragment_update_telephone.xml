<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:padding="12dp">

    <ImageView
        android:id="@+id/iv_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/edit_telephone"
        app:layout_constraintBottom_toBottomOf="@+id/edit_telephone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/edit_telephone"
        android:src="@drawable/jdme_ic_telephone" />

    <com.jd.oa.ui.ClearEditText
        android:id="@+id/edit_telephone"
        android:layout_width="0dp"
        android:layout_height="50dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/iv_phone"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/white"
        android:inputType="phone"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:singleLine="true"
        android:textColor="#2d2d2d"
        android:textColorHint="@color/black_edit_hit"
        android:textCursorDrawable="@drawable/jdme_edit_cursor"
        android:textSize="16sp"
        android:hint="@string/me_hint_input_telephone" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="@dimen/libui_0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintTop_toBottomOf="@id/edit_telephone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/comm_divider" />
</androidx.constraintlayout.widget.ConstraintLayout>