<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_recommend_header"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical">

        <com.jd.oa.ui.SimpleRoundImageView
            android:id="@+id/iv_card_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:round_radius="3dp"
            android:src="@drawable/jdme_exp_recommend_image_holder"
            tools:src="@drawable/jdme_exp_recommend_image_holder" />

        <TextView
            android:id="@+id/tv_card_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:lines="1"
            android:maxEms="8"
            android:textColor="@color/color_333333"
            android:textSize="16dp"
            android:textStyle="bold"
            tools:text="健康关怀" />

        <TextView
            android:id="@+id/tv_card_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:lines="1"
            android:maxEms="4"
            android:textColor="#999999"
            android:textSize="12dp"
            tools:text="查看更多"
            android:visibility="gone"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iftv_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_direction_right"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/color_999999"
        android:textSize="11dp"
        tools:text="您的健康是我们最宝贵的财富" />
</LinearLayout>