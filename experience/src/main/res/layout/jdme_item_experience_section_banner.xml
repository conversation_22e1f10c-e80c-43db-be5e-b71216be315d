<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/banner_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="4dp"
    android:background="@drawable/jdme_bg_shadow_corner">

    <!--皮肤背景-->
    <FrameLayout
        android:id="@+id/skin_bkgnd_layout"
        android:layout_width="match_parent"
        android:layout_height="223dp"
        android:background="#FAFAFA">

        <ImageView
            android:id="@+id/iv_theme_center"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|top"
            android:scaleType="centerCrop" />

        <com.jd.oa.ui.SimpleRoundImageView
            android:id="@+id/iv_theme_left"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="start|top"
            android:scaleType="fitStart"
            app:round_radius="6dp" />

        <com.jd.oa.ui.SimpleRoundImageView
            android:id="@+id/iv_theme_right"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="end|top"
            android:scaleType="fitEnd"
            app:round_radius="5.5dp" />
    </FrameLayout>

    <com.jd.oa.ui.SimpleRoundImageView
        android:id="@+id/title_pic_img"
        android:layout_width="90dp"
        android:layout_height="70dp"
        android:layout_gravity="end|top"
        android:scaleType="centerCrop"
        app:round_radius="4dp" />

    <LinearLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="223dp"
        android:orientation="vertical">

        <com.jd.oa.experience.view.TitleView2
            android:id="@+id/title_view"
            android:layout_width="match_parent"
            android:layout_height="40dp" />

        <LinearLayout
            android:id="@+id/title_container_v3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="12dp"
            android:visibility="gone">
            <TextView
                android:id="@+id/title_text_v3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="#1B1B1B"
                android:textStyle="bold"
                tools:text="文化空间"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/subtitle_layout"
            android:layout_width="match_parent"
            android:layout_height="18.5dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="70dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/subpic_img"
                android:layout_width="29dp"
                android:layout_height="14dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="6dp"
                android:scaleType="centerCrop" />

            <TextView
                android:id="@+id/subtitle_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:gravity="start"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#666666"
                android:textSize="12dp" />
        </LinearLayout>

        <com.jd.oa.experience.view.PagerSnapRecyclerView
            android:id="@+id/banner_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="12dp"
            app:autoScrollEnable="true" />
    </LinearLayout>

</FrameLayout>