<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/layout_label"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingStart="6px"
    android:paddingEnd="6px"
    android:layout_marginStart="4dp"
    android:gravity="center_vertical"
    android:background="@drawable/jdme_bg_exp_profile_label_light">
    <View
        android:layout_width="8dp"
        android:layout_height="20dp"
        android:layout_marginTop="6px"
        android:layout_marginBottom="6px"/>
    <TextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:textColor="#62656D"
        android:textSize="12dp"
        android:includeFontPadding="false"
        android:visibility="visible"/>
    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:text="@string/icon_prompt_add"
        android:textColor="@color/color_arrow_normal"
        android:textSize="@dimen/JMEIcon_10"
        android:visibility="gone"/><!--color_arrow_dark-->
    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_go"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/icon_direction_right"
        android:textColor="@color/color_arrow_normal"
        android:textSize="@dimen/JMEIcon_10"
        android:visibility="gone"/><!--color_arrow_dark-->
    <View
        android:layout_width="8dp"
        android:layout_height="20dp"
        android:layout_marginTop="6px"
        android:layout_marginBottom="6px"/>
</LinearLayout>