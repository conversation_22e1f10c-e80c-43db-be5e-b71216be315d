<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="#FAFAFA">

    <FrameLayout
        android:id="@+id/bottom_line_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <View
            android:id="@+id/bottom_line_bg"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="33dp"
            android:layout_marginEnd="33dp"
            android:background="#E6E6E6"/>
        <TextView
            android:id="@+id/bottom_line_tv"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:background="#FAFAFA"
            android:layout_gravity="center_horizontal"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="12dp"
            android:textColor="#BFC1C4"/>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/empty_tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="114dp"
        android:orientation="vertical">
        <ImageView
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/jdme_bg_tab_empty"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="27dp"
            android:layout_gravity="center_horizontal"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#8F959E"
            android:textSize="14dp"
            android:text="@string/empty_tab_text"
            android:includeFontPadding="false"/>
    </LinearLayout>
</FrameLayout>