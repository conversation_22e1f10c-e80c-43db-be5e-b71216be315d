<?xml version="1.0" encoding="utf-8"?><!-- 个人信息界面 -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">


    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:divider="@drawable/jdme_setting_divider_inset"
        app:showDividers="middle">

        <RelativeLayout
            android:id="@+id/rl_myself"
            style="@style/set_container_bottom"
            android:layout_height="wrap_content"
            android:paddingStart="12dp"
            android:paddingTop="12dp"
            android:paddingEnd="12dp"
            android:paddingBottom="12dp">

            <TextView
                android:id="@+id/header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_icon"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <View
                android:id="@+id/header_red_dot"
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_marginStart="10dp"
                android:layout_marginTop="17dp"
                android:layout_toEndOf="@id/header"
                android:background="@drawable/jdme_exp_red_dot"
                android:visibility="gone" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/iv_arrow">

                <com.jd.oa.ui.CircleImageView
                    android:id="@id/me_iv_circle"
                    android:layout_width="55dip"
                    android:layout_height="55dip"
                    android:layout_centerInParent="true"
                    android:contentDescription="@string/me_user_icon"
                    android:src="@drawable/jdme_icon_user_default_avatar_circle"
                    tools:background="@color/black" />

                <ImageView
                    android:id="@+id/me_iv_circle_bg"
                    android:layout_width="60dip"
                    android:layout_height="60dip"
                    android:layout_centerInParent="true"
                    android:contentDescription="@string/me_user_icon"
                    tools:background="@color/blue" />

            </RelativeLayout>


            <ImageView
                android:id="@+id/iv_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="12dp"
                android:src="@drawable/jdme_icon_arrow_right_gray" />
        </RelativeLayout>

        <!-- 姓名 -->

        <RelativeLayout
            android:id="@+id/rl_check_work"
            style="@style/set_container_bottom"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_name_user"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp" />
        </RelativeLayout>
        <!--二维码名片-->
        <TextView
            android:id="@+id/rl_check_qt"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/jdme_icon_arrow_right_gray"
            android:gravity="center_vertical"
            android:paddingLeft="8dp"
            android:paddingTop="16dp"
            android:paddingRight="8dp"
            android:paddingBottom="16dp"
            android:text="@string/me_qr_code_ticket"
            android:textColor="@color/me_setting_foreground"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible"/>
        <!-- 部门 -->

        <RelativeLayout
            android:id="@+id/rl_org"
            style="@style/set_container_bottom"
            android:layout_height="wrap_content"
            android:paddingLeft="8dp"
            android:paddingTop="16dp"
            android:paddingRight="8dp"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/tv_dept_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_department"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_dept"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="12dip"
                android:layout_toRightOf="@+id/tv_dept_name"
                android:drawablePadding="12dip"
                android:gravity="right"
                android:paddingTop="4dip"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp" />
        </RelativeLayout>

        <!-- 部门 -->

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/me_setting_background" />


        <RelativeLayout
            style="@style/set_container_no_divide"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/tv_post_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_position"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_post"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/tv_post_title"
                android:gravity="right"
                android:singleLine="true"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp" />
        </RelativeLayout>

        <!-- ########################## 联系信息 ########################## -->


        <!-- userName -->

        <RelativeLayout
            style="@style/set_container_bottom"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_erp"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_erp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp" />
        </RelativeLayout>

        <!-- 员工ID -->
        <RelativeLayout
            style="@style/set_container_bottom"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_employee_id"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_employee_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_phone"
            style="@style/set_container_bottom"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_phone"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:drawableEnd="@drawable/jdme_icon_arrow_right_gray"
                android:drawablePadding="12dp"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp"
                tools:text="18600000000" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_phone_2"
            style="@style/set_container_bottom"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_phone_2"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_phone_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:drawableEnd="@drawable/jdme_icon_arrow_right_gray"
                android:drawablePadding="12dp"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp"
                tools:text="18600000000" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_email"
            style="@style/set_container_no_divide"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_email"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/logistics_hr"
            style="@style/set_container_bottom"
            android:paddingStart="12dp"
            android:paddingTop="16dp"
            android:paddingEnd="12dp"
            android:paddingBottom="16dp"
            android:visibility="gone"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/me_user_hrbp"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/logistics_hr_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:drawableEnd="@drawable/jdme_icon_arrow_right_gray"
                android:drawablePadding="12dp"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="16sp"
                tools:text="yuzhihui8" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/me_setting_background" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</ScrollView>