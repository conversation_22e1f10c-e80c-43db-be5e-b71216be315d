<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/me_appbar"
        android:layout_width="match_parent"
        android:layout_height="274dp"
        android:stateListAnimator="@null"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:minHeight="200dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:src="@drawable/jdme_mine_icon_bg" />

                <RelativeLayout
                    android:id="@+id/me_rl_portait"
                    android:layout_width="110dp"
                    android:layout_height="110dp"
                    android:layout_centerInParent="true">

                    <com.jd.oa.elliptical.SuperEllipticalImageView
                        android:id="@id/me_iv_circle"
                        android:layout_width="105dp"
                        android:layout_height="105dp"
                        android:layout_centerInParent="true"
                        android:contentDescription="@string/me_user_icon"
                        android:src="@drawable/jdme_icon_logo_gray" />

                    <ImageView
                        android:id="@+id/me_iv_circle_bg"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:layout_centerVertical="true"
                        android:contentDescription="@string/me_user_icon" />
                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:paddingBottom="40dp"
                    android:text="@string/me_set_user_icon_tips"
                    android:textColor="#99000000"
                    android:textSize="@dimen/around_text_size_normal" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/tool_bar_default_height"
                android:layout_marginTop="32dp"
                app:layout_collapseMode="pin">

                <ImageButton
                    android:id="@+id/ib_back"
                    style="@style/AroundBackButton"
                    android:layout_centerVertical="true"
                    android:background="@drawable/around_ripple_circle"
                    android:src="@drawable/around_default_back_arrow" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@+id/ib_back"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/me_set_icon"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

                <TextView
                    android:id="@+id/tv_setg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/me_set_g"
                    android:textColor="@color/black"
                    android:textSize="18dp"
                    android:visibility="gone"
                    tools:visibility="visible"/>
            </RelativeLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_portrait"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-20dp"
        android:background="@drawable/jdme_bg_mine_all_protrait"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>