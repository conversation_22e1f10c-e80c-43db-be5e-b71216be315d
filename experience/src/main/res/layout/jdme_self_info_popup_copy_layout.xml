<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="@dimen/self_info_copy_popup_width"
        android:layout_height="@dimen/self_info_copy_popup_height"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_self_info_copy_text">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/exp_self_info_copy_text"
                android:textColor="#FFFFFF"
                android:textSize="14dp"
                android:includeFontPadding="false"/>
        </RelativeLayout>
        <View
            android:layout_width="12dp"
            android:layout_height="6dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/jdme_bg_self_info_copy_triangle"/>
    </LinearLayout>

</FrameLayout>