<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp">

    <ImageView
        android:layout_width="41dp"
        android:layout_height="14dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="20dp"
        android:src="@drawable/jdme_bg_section_title"
        android:scaleType="fitXY"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:scaleType="fitXY"/>

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="start"
            android:layout_marginStart="6dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="16dp"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="60dp"
            android:layout_marginStart="5dp"
            android:layout_marginTop="12dp"
            android:gravity="bottom"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#333333"
            android:textSize="26dp"
            android:includeFontPadding="false"
            android:fontFamily="@font/din_condensed_bold"/>

        <TextView
            android:id="@+id/unit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="start"
            android:layout_marginStart="5dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="16dp"
            android:text="@string/title_view_count_unit"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="90dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="6dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#666666"
            android:textSize="14dp"
            android:includeFontPadding="false"
            android:visibility="gone"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/go_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_gravity="center_vertical"
            android:textColor="#62656D"
            android:textSize="@dimen/JMEIcon_14"
            android:text="@string/icon_direction_right"
            android:visibility="gone"/>
    </LinearLayout>
</FrameLayout>