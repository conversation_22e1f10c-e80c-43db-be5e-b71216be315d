<?xml version="1.0" encoding="utf-8"?>
<!--学习地图：基础能力，落地能力-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="279dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/study_map_name_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="14dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="12dp"
        android:gravity="start"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#333333"
        android:textSize="14dp"
        android:includeFontPadding="false"/>

    <include layout="@layout/jdme_item_experience_section_study_map_ability_item"
        android:id="@+id/study_map_item_one"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="14dp"
        android:layout_marginBottom="8dp"/>

    <include layout="@layout/jdme_item_experience_section_study_map_ability_item"
        android:id="@+id/study_map_item_two"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="14dp"
        android:layout_marginBottom="8dp"/>

    <include layout="@layout/jdme_item_experience_section_study_map_ability_item"
        android:id="@+id/study_map_item_three"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="14dp"
        android:layout_marginBottom="8dp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="6dp"/>
</LinearLayout>