<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="32dp"
    android:layout_marginHorizontal="12dp"
    android:layout_marginTop="6dp"
    android:layout_marginBottom="10dp"
    android:orientation="horizontal"
    android:background="@drawable/jdme_bg_homepage_add_duty">

    <com.jd.oa.ui.IconFontView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="4dp"
        android:textColor="#FF6E1A"
        android:textSize="@dimen/JMEIcon_14"
        android:text="@string/icon_general_edit"/>

    <TextView
        android:id="@+id/tag_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp"
        android:textSize="14dp"
        android:textColor="#FF6E1A"
        android:text="@string/exp_homepage_instruction_duty_add"
        android:includeFontPadding="false"/>

</LinearLayout>