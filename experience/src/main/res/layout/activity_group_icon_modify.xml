<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1999"
    android:orientation="vertical"
    tools:context="com.jd.oa.business.groupicon.GroupIconModifyActivity">

    <LinearLayout
        android:id="@+id/ll_top"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_back"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:text="@string/icon_direction_left"
            android:textColor="#1B1B1B"
            android:textSize="20dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/group_icon_modify_edit_photo"
            android:textColor="#1B1B1B"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/me_save"
            android:textColor="#F63218"
            android:textSize="16dp"
            android:textStyle="bold" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/jdme_im_group_icon_corner_bg_white"
        app:layout_constraintTop_toBottomOf="@id/ll_top">

        <com.jd.oa.elliptical.JdMeAvatarView
            android:id="@+id/avatar_view"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:layout_marginVertical="30dp"
            tools:background="@color/color_1b96fe" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_camera"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignEnd="@id/avatar_view"
            android:layout_alignBottom="@id/avatar_view"
            android:layout_marginEnd="-6dp"
            android:layout_marginBottom="-2dp"
            android:background="@drawable/jdme_bg_camera_btn"
            android:gravity="center"
            android:text="@string/icon_general_camera"
            android:textColor="@color/white"
            android:textSize="18dp" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/jdme_im_group_icon_corner_bg_white"
        android:orientation="vertical"
        android:padding="12dp"
        app:layout_constraintTop_toBottomOf="@id/ll_top">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/group_icon_modify_customized_text"
            android:textColor="#1B1B1B"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/et_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:background="@drawable/jdme_im_group_icon_edit_bg"
            android:hint="@string/group_icon_modify_input_text"
            android:paddingVertical="12dp"
            android:paddingStart="12dp"
            android:textSize="16dp"
            android:paddingEnd="42dp"
            android:singleLine="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_clear"
            android:layout_width="18dp"
            android:visibility="gone"
            android:layout_height="18dp"
            android:layout_marginEnd="12dp"
            android:padding="2dp"
            android:gravity="center"
            android:textColor="#9D9D9D"
            android:text="@string/icon_padding_closecircle"
            app:layout_constraintBottom_toBottomOf="@id/et_input"
            app:layout_constraintEnd_toEndOf="@id/et_input"
            app:layout_constraintTop_toTopOf="@id/et_input" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/jdme_im_group_icon_corner_bg_white"
        android:orientation="vertical"
        android:padding="12dp"
        app:layout_constraintTop_toBottomOf="@id/ll_top">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/group_icon_modify_select_color"
            android:textColor="#1B1B1B"
            android:textSize="16dp"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/ll_colors"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="6dp"
            android:orientation="horizontal" />

    </LinearLayout>

</LinearLayout>