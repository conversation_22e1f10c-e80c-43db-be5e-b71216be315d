<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginHorizontal="8dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/ll_left_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:layout_marginEnd="8dp"
        android:orientation="vertical"
        android:visibility="gone"
        android:background="@drawable/jdme_all_round_8"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintEnd_toStartOf="@id/ll_right_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_left_title_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_left_chart_more"
                app:layout_constraintTop_toTopOf="parent">
                <TextView
                    android:id="@+id/tv_left_header_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1B1B1B"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ll_left_value_unit_container"
                    tools:text="专属福利" />

                <LinearLayout
                    android:id="@+id/ll_left_value_unit_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_left_header_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed">
                    <TextView
                        android:id="@+id/tv_left_header_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#1B1B1B"
                        android:includeFontPadding="true"
                        android:layout_marginHorizontal="4dp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:maxWidth="50dp"
                        tools:text="31"/>

                    <TextView
                        android:id="@+id/tv_left_header_unit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#1B1B1B"
                        android:text="@string/exp_welfare_my_welfare_unit"
                        android:maxLines="1"
                        android:ellipsize="end"
                        tools:text="项"/>
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/iv_left_chart_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/icon_direction_right"
                android:textColor="#FF62656D"
                android:textSize="@dimen/JMEIcon_12"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/ll_left_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_left_header_title"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_right_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:orientation="vertical"
        android:visibility="gone"
        android:background="@drawable/jdme_all_round_8"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/ll_left_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_right_title_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_right_chart_more"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_right_header_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1B1B1B"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ll_value_unit_container"
                    tools:text="专属福利专属福利专属福利专属福利专属福利专属福利" />

                <LinearLayout
                    android:id="@+id/ll_value_unit_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_right_header_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed">
                    <TextView
                        android:id="@+id/tv_right_header_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#1B1B1B"
                        android:includeFontPadding="true"
                        android:layout_marginHorizontal="4dp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:maxWidth="50dp"
                        tools:text="31"/>

                    <TextView
                        android:id="@+id/tv_right_header_unit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#1B1B1B"
                        android:text="@string/exp_welfare_my_welfare_unit"
                        android:maxLines="1"
                        android:ellipsize="end"
                        tools:text="项"/>
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/iv_right_chart_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/icon_direction_right"
                android:textColor="#FF62656D"
                android:textSize="@dimen/JMEIcon_12"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/ll_right_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_right_header_title"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>