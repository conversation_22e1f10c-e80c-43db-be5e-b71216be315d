<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:overScrollMode="never"
        android:scrollbars="none">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:layout_width="match_parent"
                android:layout_height="245dp"
                android:layout_gravity="top"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_exp_instruction_gradient_bg"/>

            <ImageView
                android:layout_width="244dp"
                android:layout_height="228dp"
                android:layout_gravity="start|top"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_bg_instruction_left_top"/>

            <ImageView
                android:layout_width="165dp"
                android:layout_height="428dp"
                android:layout_gravity="start|top"
                android:layout_marginTop="39dp"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_bg_instruction_left_bottom"/>

            <ImageView
                android:layout_width="168dp"
                android:layout_height="503dp"
                android:layout_gravity="end|top"
                android:layout_marginTop="174dp"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_bg_instruction_right_bottom"/>

            <ImageView
                android:layout_width="180dp"
                android:layout_height="188dp"
                android:layout_gravity="end|top"
                android:layout_marginTop="21dp"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_bg_instruction_edit"/>

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="28dp"
                android:paddingStart="3dp"
                android:layout_marginTop="54dp"
                android:textColor="#333333"
                android:textStyle="bold"
                android:textSize="28dp"
                android:text="@string/exp_instruction_title"
                android:maxLines="1"
                android:ellipsize="end"
                android:includeFontPadding="false"/>

            <TextView
                android:id="@+id/title_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="28dp"
                android:paddingStart="3dp"
                android:layout_marginEnd="160dp"
                android:layout_marginTop="102dp"
                android:lineSpacingMultiplier="1.8"
                android:textColor="#666666"
                android:textSize="14dp"
                android:text="@string/exp_instruction_desc"
                android:maxLines="2"
                android:ellipsize="end"
                android:includeFontPadding="false"/>

            <!--信封内容-->
            <LinearLayout
                android:id="@+id/content_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/instruction_content_margin_top"
                android:layout_marginBottom="@dimen/instruction_content_margin_bottom"
                android:orientation="vertical">
                <!--信封上部分-->
                <!--101-9=92，9是虚线区域虚线上边部分高度，paddingTop=9为了方便垂直居中-->
                <LinearLayout
                    android:id="@+id/content_demo"
                    android:layout_width="match_parent"
                    android:layout_height="92dp"
                    android:layout_marginStart="@dimen/instruction_content_margin"
                    android:layout_marginEnd="@dimen/instruction_content_margin"
                    android:paddingTop="9dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_bg_instruction_content_top">
                    <ImageView
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="13dp"
                        android:src="@drawable/jdme_bg_instruction_icon"/>
                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:lineSpacingExtra="6dp"
                        android:textColor="#333333"
                        android:textSize="16dp"
                        android:text="@string/exp_instruction_demo_list"
                        android:maxLines="2"
                        android:ellipsize="end"
                        android:includeFontPadding="false"/>
                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/content_demo_btn"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:gravity="center"
                        android:text="@string/icon_direction_right"
                        android:textColor="#A1A1A1"
                        android:textSize="@dimen/JMEIcon_14"/>
                </LinearLayout>

                <!--信封上部分隔虚线-->
                <include layout="@layout/jdme_activity_my_instruction_separator"/>

                <!--以下部分根据需要展示-->
                <!--信封中间部分-->
                <include layout="@layout/jdme_activity_my_instruction_view" android:id="@+id/content_update"/>

                <!--信封中部分隔虚线-->
                <include layout="@layout/jdme_activity_my_instruction_separator" android:id="@+id/content_separator"/>

                <!--信封下部分，更新说明书-->
                <include layout="@layout/jdme_activity_my_instruction_view" android:id="@+id/content_setting"/>

                <!--信封下部分，新建说明书-->
                <include layout="@layout/jdme_activity_my_instruction_view" android:id="@+id/content_create"/>

                <View
                    android:id="@+id/content_bottom_padding"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:visibility="gone"/>
            </LinearLayout>

            <View
                android:layout_width="38dp"
                android:layout_height="36dp"
                android:layout_gravity="end|top"
                android:layout_marginEnd="@dimen/instruction_content_margin"
                android:layout_marginTop="@dimen/instruction_content_margin_top"
                android:background="@drawable/jdme_bg_instruction_edit_blur"/>
        </FrameLayout>
    </androidx.core.widget.NestedScrollView>

    <RelativeLayout
        android:id="@+id/mask_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent">
        <com.jd.oa.ui.IconFontView
            android:id="@+id/back"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_alignParentStart="true"
            android:gravity="center"
            android:text="@string/icon_direction_left"
            android:textColor="#232930"
            android:textSize="@dimen/JMEIcon_22"/>
        <TextView
            android:id="@+id/mask_view_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#2E2D2D"
            android:textSize="18dp"
            android:text="@string/exp_instruction_title"
            android:includeFontPadding="false"
            android:visibility="gone"/>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/send_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="bottom"
        android:visibility="gone">
        <View
            android:layout_width="match_parent"
            android:layout_height="5dp"
            android:background="@drawable/jdme_bg_instruction_bottom_shadow"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:orientation="horizontal"
            android:background="#ffffff">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:gravity="start"
                android:layout_marginStart="16dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#62656D"
                android:textSize="14dp"
                android:text="@string/exp_send_my_instruction"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/send_button"
                android:layout_width="78dp"
                android:layout_height="34dp"
                android:background="@drawable/jdme_bg_instruction_send_button_enable"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="16dp"
                android:gravity="center"
                android:textColor="#FFFFFF"
                android:textSize="16dp"
                android:text="@string/exp_send"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>