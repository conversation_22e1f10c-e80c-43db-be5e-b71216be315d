<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="132dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="62dp"
        android:orientation="horizontal"
        android:weightSum="2">

        <FrameLayout
            android:id="@+id/mContainer00"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.jd.oa.ui.SimpleRoundImageView
                android:id="@+id/mImg00"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="end|top"
                android:scaleType="fitEnd"
                app:round_radius="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingHorizontal="12dp"
                android:paddingVertical="13dp">

                <TextView
                    android:id="@+id/mText00"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF333333"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    tools:text="这里面的 img 是" />

                <androidx.legacy.widget.Space
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/mSubText00"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:gravity="bottom"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF666666"
                    android:textSize="12dp"
                    tools:text="这里面的 img 是" />
            </LinearLayout>
        </FrameLayout>

        <androidx.legacy.widget.Space
            android:layout_width="8dp"
            android:layout_height="1dp" />

        <FrameLayout
            android:id="@+id/mContainer01"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.jd.oa.ui.SimpleRoundImageView
                android:id="@+id/mImg01"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="end|top"
                android:scaleType="fitEnd"
                app:round_radius="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingHorizontal="12dp"
                android:paddingVertical="13dp">

                <TextView
                    android:id="@+id/mText01"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF333333"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    tools:text="这里面的 img 是" />

                <androidx.legacy.widget.Space
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/mSubText01"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:gravity="bottom"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF666666"
                    android:textSize="12dp"
                    tools:text="这里面的 img 是" />
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="62dp"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:weightSum="2">

        <FrameLayout
            android:id="@+id/mContainer10"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.jd.oa.ui.SimpleRoundImageView
                android:id="@+id/mImg10"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="end|top"
                android:scaleType="fitEnd"
                app:round_radius="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingHorizontal="12dp"
                android:paddingVertical="13dp">

                <TextView
                    android:id="@+id/mText10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF333333"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    tools:text="这里面的 img 是" />

                <androidx.legacy.widget.Space
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/mSubText10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:gravity="bottom"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF666666"
                    android:textSize="12dp"
                    tools:text="这里面的 img 是" />
            </LinearLayout>
        </FrameLayout>

        <androidx.legacy.widget.Space
            android:layout_width="8dp"
            android:layout_height="1dp" />

        <FrameLayout
            android:id="@+id/mContainer11"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.jd.oa.ui.SimpleRoundImageView
                android:id="@+id/mImg11"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="end|top"
                android:scaleType="fitEnd"
                app:round_radius="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingHorizontal="12dp"
                android:paddingVertical="13dp">

                <TextView
                    android:id="@+id/mText11"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF333333"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    tools:text="这里面的 img 是" />

                <androidx.legacy.widget.Space
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/mSubText11"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:ellipsize="end"
                    android:gravity="bottom"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF666666"
                    android:textSize="12dp"
                    tools:text="这里面的 img 是" />
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</LinearLayout>