<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="18dp"
    android:layout_marginStart="@dimen/instruction_content_margin"
    android:layout_marginEnd="@dimen/instruction_content_margin"
    android:orientation="horizontal">
    <!--半圆高度14 + 上边距2 + 下边距2-->
    <!--半圆宽度7 + 右边距8-->
    <ImageView
        android:layout_width="15dp"
        android:layout_height="match_parent"
        android:src="@drawable/jdme_bg_instruction_content_separator_left"/>
    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="#E6FFFFFF">
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:background="@drawable/jdme_exp_instruction_dashline"/>
    </FrameLayout>
    <ImageView
        android:layout_width="15dp"
        android:layout_height="match_parent"
        android:src="@drawable/jdme_bg_instruction_content_separator_right"/>
</LinearLayout>