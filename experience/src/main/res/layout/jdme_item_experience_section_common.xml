<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginTop="5dp"
    android:paddingTop="3dp"
    android:paddingBottom="5dp"
    android:orientation="vertical"
    android:background="@drawable/jdme_bg_shadow_corner">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_commons"
        android:layout_width="match_parent"
        android:layout_height="76dp"
        android:orientation="horizontal"
        android:overScrollMode="never"
        android:scrollbars="none" />

    <SeekBar
        android:id="@+id/sb_scroll_bar"
        android:layout_width="20dp"
        android:layout_height="3dp"
        android:layout_marginBottom="6dp"
        android:layout_gravity="center_horizontal"
        android:focusable="true"
        android:progress="0"
        android:progressDrawable="@drawable/jdme_bg_scroll_bar_track"
        android:thumb="@drawable/jdme_bg_scroll_bar_thumb"
        android:visibility="gone" />
    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"/>
</LinearLayout>