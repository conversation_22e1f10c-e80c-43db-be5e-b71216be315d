<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp">

    <ImageView
        android:id="@+id/iv_decoration"
        android:layout_width="41dp"
        android:layout_height="14dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="20dp"
        android:scaleType="fitXY"
        android:src="@drawable/jdme_bg_section_title" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="22dp"
        android:layout_marginTop="13dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginStart="16dp"
            android:scaleType="fitXY" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:ellipsize="end"
            android:gravity="start"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="16dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:ellipsize="end"
            android:fontFamily="@font/din_condensed_bold"
            android:gravity="bottom"
            android:includeFontPadding="false"
            android:maxWidth="60dp"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="26dp" />

        <TextView
            android:id="@+id/unit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="5dp"
            android:ellipsize="end"
            android:gravity="start"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="@string/title_view_count_unit"
            android:textColor="#333333"
            android:textSize="16dp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="6dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxWidth="90dp"
            android:maxLines="1"
            android:textColor="#666666"
            android:textSize="14dp"
            android:visibility="gone" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/go_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:text="@string/icon_direction_right"
            android:textColor="#62656D"
            android:textSize="@dimen/JMEIcon_14"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>
</FrameLayout>