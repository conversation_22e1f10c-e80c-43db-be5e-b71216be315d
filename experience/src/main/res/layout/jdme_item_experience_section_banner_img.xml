<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="132dp">

    <com.jd.oa.ui.SimpleRoundImageView
        android:id="@+id/mImg"
        android:layout_width="match_parent"
        android:layout_height="132dp"
        android:layout_gravity="end|top"
        android:scaleType="centerCrop"
        app:round_radius="8dp" />

    <View
        android:id="@+id/mShadow"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_gravity="bottom"
        android:background="@drawable/jdme_bg_exp_banner_txt"
        android:visibility="gone" />

    <TextView
        android:id="@+id/mText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="bottom"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:textColor="#FFFFFFFF"
        android:textSize="12dp"
        android:visibility="visible"
        tools:text="这里面的 img 是" />
</FrameLayout>