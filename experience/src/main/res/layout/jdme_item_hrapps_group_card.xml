<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:background="@drawable/jdme_all_round_8_with_border"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_group_title"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#1b1b1b"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="假勤服务"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_group_items"
        app:layout_constraintTop_toBottomOf="@id/tv_group_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="12dp"
        android:layout_marginHorizontal="12dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>