<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff">
    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_gravity="bottom"
        android:background="#F5F5F5"/>
    <ImageView
        android:layout_width="89dp"
        android:layout_height="66dp"
        android:layout_gravity="end|top"
        android:layout_marginEnd="16dp"
        android:src="@drawable/jdme_bg_homepage_badge"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="21dp"
        android:orientation="vertical">
        <LinearLayout
            android:id="@+id/badge_title_layout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:layout_marginBottom="3dp">
            <TextView
                android:id="@+id/badge_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:textColor="#333333"
                android:textStyle="bold"
                android:textSize="16dp"
                android:textAlignment="center"
                android:includeFontPadding="false"/>
            <View
                android:layout_width="2dp"
                android:layout_height="2dp"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="6dp"
                android:background="@drawable/jdme_bg_homepage_badge_title_dot"/>
            <TextView
                android:id="@+id/badge_count_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="60dp"
                android:textColor="#cdcdcd"
                android:textSize="11dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textAlignment="center"
                android:includeFontPadding="false"/>
            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
            <TextView
                android:id="@+id/badge_btn_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#666666"
                android:textSize="12dp"
                android:textAlignment="center"
                android:includeFontPadding="false"/>
            <com.jd.oa.ui.IconFontView
                android:id="@+id/badge_btn_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="16dp"
                android:textColor="#D8D8D8"
                android:textSize="@dimen/JMEIcon_12"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/default_badge_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">
            <com.jd.oa.ui.IconFontView
                android:id="@+id/default_badge_icon"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:textSize="@dimen/JMEIcon_18"
                android:textColor="#666666"
                android:text="@string/icon_me_badge"/>

            <com.jd.oa.experience.view.BadgeRecyclerView
                android:id="@+id/default_badge_rv"
                android:layout_width="0dp"
                android:layout_height="38dp"
                android:layout_weight="1"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:orientation="horizontal"
                android:scrollbars="horizontal"
                android:scrollbarThumbHorizontal="@drawable/jdme_bg_scroll_bar_homepage_badge"
                android:scrollbarStyle="outsideOverlay"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/custom_badge_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:textSize="@dimen/JMEIcon_18"
                android:textColor="#666666"
                android:text="@string/icon_me_insignia"/>

            <com.jd.oa.experience.view.BadgeRecyclerView
                android:id="@+id/custom_badge_rv"
                android:layout_width="0dp"
                android:layout_height="38dp"
                android:layout_weight="1"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:orientation="horizontal"
                android:scrollbars="horizontal"
                android:scrollbarThumbHorizontal="@drawable/jdme_bg_scroll_bar_homepage_badge"
                android:scrollbarStyle="outsideOverlay"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/single_line_badge_list"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:orientation="horizontal"
            android:visibility="gone">
            <com.jd.oa.ui.IconFontView
                android:layout_width="@dimen/homepage_badge_icon_width"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/homepage_badge_left_margin"
                android:gravity="start|center_vertical"
                android:textSize="@dimen/JMEIcon_18"
                android:textColor="#666666"
                android:text="@string/icon_me_badge"/>
            <LinearLayout
                android:id="@+id/default_badge_container"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="horizontal"/>
            <ImageView
                android:layout_width="@dimen/homepage_badge_separator_width"
                android:layout_height="14dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/jdme_bg_homepage_badge_separator"/>
            <com.jd.oa.ui.IconFontView
                android:layout_width="@dimen/homepage_badge_icon_width"
                android:layout_height="match_parent"
                android:gravity="start|center_vertical"
                android:textSize="@dimen/JMEIcon_18"
                android:textColor="#666666"
                android:text="@string/icon_me_insignia"/>
            <LinearLayout
                android:id="@+id/custom_badge_container"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="horizontal" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>