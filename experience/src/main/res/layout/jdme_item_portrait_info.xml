<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical"
    android:paddingTop="10dp"
    android:paddingBottom="10dp">

    <RelativeLayout
        android:id="@+id/me_rl_item_portrait"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal">

        <com.jd.oa.mae.bundles.widget.CircleImageView
            android:id="@+id/me_iv_circle"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_centerInParent="true"
            android:contentDescription="@string/me_user_icon"
            android:scaleType="centerCrop"
            android:src="@drawable/jdme_icon_user_default_avatar_circle" />

        <ImageView
            android:id="@+id/me_iv_checked"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_centerInParent="true"
            android:src="@drawable/jdme_mine_portrait_checked"
            android:visibility="visible" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:textColor="#99000000"
        android:textSize="@dimen/around_text_size_normal"
        tools:text="测试" />
</LinearLayout>