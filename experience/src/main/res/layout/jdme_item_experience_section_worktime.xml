<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="22dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:gravity="start"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="#666666"
        android:textSize="16dp"
        android:visibility="gone" />

    <SeekBar
        android:id="@+id/seekBar1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:max="25"
        android:paddingBottom="12dp"
        android:visibility="gone" />

    <SeekBar
        android:id="@+id/seekBar2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:max="200"
        android:paddingBottom="12dp"
        android:visibility="gone" />


    <include layout="@layout/jdme_item_experience_section_worktime_chart" />

<!--    <include layout="@layout/jdme_item_experience_section_worktime_meeting" />-->


</LinearLayout>