<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:gravity="center_vertical">

    <com.jd.oa.ui.SimpleRoundImageView
        android:id="@+id/iv_app_icon"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="4dp"
        app:round_radius="6dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/color_333333"
            android:textSize="14dp"
            tools:text="应用名称应用"
            android:includeFontPadding="false"/>

<!--        <TextView
            android:id="@+id/tv_app_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/color_999999"
            android:textSize="10dp"
            tools:text="电脑故障快速解决" />-->

    </LinearLayout>

</LinearLayout>