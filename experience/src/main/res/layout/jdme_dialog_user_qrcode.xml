<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">
    <LinearLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:paddingBottom="20dp"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:background="@drawable/jdme_img_bg_my_qrcode">
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:gravity="center"
            android:textSize="28dp"
            android:textColor="@color/me_setting_foreground"
            tools:text="琼恩琼恩琼恩琼恩琼恩 琼恩琼恩琼恩琼恩琼恩" />
        <TextView
            android:id="@+id/tv_job"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="18dp"
            android:textColor="@color/me_setting_foreground"
            tools:text="交互设计师岗"/>

        <ImageView
            android:id="@+id/iv_qrcode"
            android:layout_width="240dp"
            android:layout_height="240dp"
            android:layout_marginTop="30dp"
            android:src="@mipmap/img_default"/>
        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textSize="13dp"
            android:textColor="@color/me_setting_foreground_light"
            android:text="@string/me_add_friend_tips"/>
    </LinearLayout>
    <View
        android:id="@+id/view_top"
        android:layout_width="match_parent"
        android:layout_height="40dp"/>
    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_avatar"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="center_horizontal"
        android:clickable="true"
        android:src="@mipmap/img_default"/>
</FrameLayout>