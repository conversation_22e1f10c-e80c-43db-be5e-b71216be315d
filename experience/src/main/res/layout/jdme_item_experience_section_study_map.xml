<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="4dp"
    android:background="@drawable/jdme_bg_shadow_corner">

    <ImageView
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_gravity="end|top"
        android:scaleType="fitXY"
        android:src="@drawable/jdme_bg_section_study_map_right_icon"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.jd.oa.experience.view.TitleView
            android:id="@+id/title_view"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginEnd="70dp"
            android:layout_marginBottom="5dp"/>

        <TextView
            android:id="@+id/map_name_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="70dp"
            android:layout_marginBottom="18dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#666666"
            android:textSize="12dp"
            android:includeFontPadding="false"/>

        <com.jd.oa.experience.view.PagerSnapRecyclerView
            android:id="@+id/study_map_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:visibility="visible"/>

        <com.jd.oa.experience.view.PagerSnapRecyclerView
            android:id="@+id/course_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:visibility="gone"/>
    </LinearLayout>

</FrameLayout>