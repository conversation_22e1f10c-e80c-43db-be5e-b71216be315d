<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="263dp"
    android:layout_marginTop="4dp"
    android:background="@drawable/jdme_bg_shadow_corner"
    android:orientation="vertical"
    android:paddingStart="12dp"
    android:paddingTop="12dp"
    android:paddingEnd="12dp">

    <include layout="@layout/jdme_me_recommend_header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"/>

    <com.jd.oa.ui.WrapContentViewPager
        android:id="@+id/vp_app"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_weight="1" />

    <com.jd.oa.viewpager.indicator.CirclePageIndicator
        android:id="@+id/page_indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        app:centered="true"
        app:circleSpacing="6dp"
        app:fillColor="#333333"
        app:pageColor="#E6E6E6"
        app:radius="2.5dp"
        app:strokeWidth="0dp" />

    <include
        layout="@layout/jdme_me_recommend_footer_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible" />
</LinearLayout>