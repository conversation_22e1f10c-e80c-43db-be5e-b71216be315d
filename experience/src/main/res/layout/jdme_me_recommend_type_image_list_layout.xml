<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="263dp"
    android:layout_marginTop="4dp"
    android:background="@drawable/jdme_bg_shadow_corner"
    android:orientation="vertical"
    android:paddingStart="12dp"
    android:paddingTop="12dp"
    android:paddingEnd="12dp">

    <include layout="@layout/jdme_me_recommend_header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"/>

    <com.jd.oa.ui.banner.Banner
        android:id="@+id/image_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="12dp"
        app:banner_default_image="@drawable/jdme_exp_recommend_banner_holder"
        app:delay_time="3000"
        app:image_scale_type="center_crop"
        app:indicator_drawable_selected="@drawable/jdme_exp_recommend_indicator_selected"
        app:indicator_drawable_unselected="@drawable/jdme_exp_recommend_indicator_normal"
        app:indicator_height="5dp"
        app:indicator_width="10dp"
        app:is_auto_play="true" />

    <include
        layout="@layout/jdme_me_recommend_footer_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp" />
</LinearLayout>