<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingVertical="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_item_icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/iv_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#1B1B1B"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/iv_item_icon"
        app:layout_constraintEnd_toStartOf="@id/ll_item_value"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iv_item_icon"
        app:layout_constraintTop_toTopOf="@id/iv_item_icon"
        tools:text="福利劵" />


    <LinearLayout
        android:id="@+id/ll_item_value"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginStart="4dp"
        android:gravity="end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_item_title"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/iv_item_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#1B1B1B"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginEnd="2dp"
            tools:text="590" />

        <TextView
            android:id="@+id/iv_item_unit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#9D9D9D"
            android:textSize="12sp"
            tools:text="元" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>