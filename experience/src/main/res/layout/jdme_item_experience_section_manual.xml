<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="4dp"
    android:orientation="vertical"
    android:background="@drawable/jdme_bg_shadow_corner">

    <com.jd.oa.experience.view.TitleView
        android:id="@+id/title_view"
        android:layout_width="match_parent"
        android:layout_height="48dp"/>

    <FrameLayout
        android:id="@+id/duty_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_gray_corner">
        <TextView
            android:id="@+id/duty_editable_tv"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:background="@drawable/jdme_bg_section_manual_card_edit"
            android:paddingStart="7dp"
            android:paddingEnd="5dp"
            android:layout_gravity="end|top"
            android:gravity="center_vertical"
            android:textColor="#FF6B23"
            android:textSize="12dp"
            android:text="@string/exp_homepage_instruction_edit"
            android:includeFontPadding="false"/>
        <ImageView
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/jdme_bg_section_manual_duty_icon"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="11dp"
            android:layout_gravity="start|top"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="14dp"
            android:text="@string/exp_homepage_instruction_work_duty"
            android:includeFontPadding="false"/>

        <com.zhy.view.flowlayout.TagFlowLayout
            android:id="@+id/duty_business_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:layout_marginTop="38dp"
            android:layout_marginBottom="8dp"
            app:tag_gravity="left"/>

        <LinearLayout
            android:id="@+id/duty_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="44dp"
            android:layout_marginBottom="16dp"
            android:layout_gravity="center_horizontal"
            android:visibility="gone">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#999999"
                android:textSize="11dp"
                android:text="@string/exp_homepage_instruction_duty_empty"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/duty_add"
                android:layout_width="56dp"
                android:layout_height="22dp"
                android:background="@drawable/jdme_bg_homepage_duty_add_btn"
                android:layout_marginTop="8dp"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_homepage_instruction_duty_add"
                android:includeFontPadding="false"/>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/okr_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_gray_corner">
        <TextView
            android:id="@+id/okr_editable_tv"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:background="@drawable/jdme_bg_section_manual_card_edit"
            android:paddingStart="7dp"
            android:paddingEnd="5dp"
            android:layout_gravity="end|top"
            android:gravity="center_vertical"
            android:textColor="#FF6B23"
            android:textSize="12dp"
            android:text="@string/exp_homepage_instruction_edit"
            android:includeFontPadding="false"/>
        <ImageView
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/jdme_bg_section_manual_okr_icon"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="11dp"
            android:layout_gravity="start|top"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="14dp"
            android:text="@string/exp_homepage_instruction_work_my_okr"
            android:includeFontPadding="false"/>

        <LinearLayout
            android:id="@+id/okr_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="44dp"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_homepage_okr_prefix"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/okr_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="45dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="2dp"
                android:layout_gravity="bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#FE3E33"
                android:textSize="18dp"
                android:includeFontPadding="false"
                android:fontFamily="@font/din_condensed_bold"/>
            <TextView
                android:id="@+id/okr_suffix"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#333333"
                android:textSize="12dp"
                android:includeFontPadding="false"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/okr_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="46dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textColor="#999999"
                android:textSize="12dp"
                android:text="@string/exp_manual_okr_go"
                android:includeFontPadding="false"/>
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_gravity="center_vertical"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_14"
                android:text="@string/icon_direction_right"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/okr_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="44dp"
            android:layout_marginBottom="16dp"
            android:layout_gravity="center_horizontal"
            android:visibility="gone">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#999999"
                android:textSize="11dp"
                android:text="@string/exp_homepage_instruction_okr_empty_self"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/okr_add"
                android:layout_width="56dp"
                android:layout_height="22dp"
                android:background="@drawable/jdme_bg_homepage_duty_add_btn"
                android:layout_marginTop="8dp"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_homepage_instruction_duty_add"
                android:includeFontPadding="false"/>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/manual_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/jdme_bg_gray_corner">
        <TextView
            android:id="@+id/show_editable_tv"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:background="@drawable/jdme_bg_section_manual_card_edit"
            android:paddingStart="7dp"
            android:paddingEnd="5dp"
            android:layout_gravity="end|top"
            android:gravity="center_vertical"
            android:textColor="#FF6B23"
            android:textSize="12dp"
            android:text="@string/exp_homepage_instruction_edit"
            android:includeFontPadding="false"/>
        <ImageView
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/jdme_bg_section_manual_personality_icon"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="11dp"
            android:layout_gravity="start|top"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="14dp"
            android:text="@string/exp_homepage_instruction_personality"
            android:includeFontPadding="false"/>

        <LinearLayout
            android:id="@+id/manual_show"
            android:layout_width="match_parent"
            android:layout_height="21dp"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="46dp"
            android:layout_marginBottom="11dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_manual_manual_show"
                android:includeFontPadding="false"/>
            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:layout_gravity="center_vertical"
                android:textColor="#999999"
                android:textSize="12dp"
                android:text="@string/exp_manual_manual_hint"
                android:includeFontPadding="false"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/manual_switch"
                style="@style/ExpSwitchStyle2"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/manual_empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="44dp"
            android:layout_marginBottom="16dp"
            android:visibility="gone">
            <TextView
                android:id="@+id/manual_empty_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#999999"
                android:textSize="11dp"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/manual_add"
                android:layout_width="56dp"
                android:layout_height="22dp"
                android:background="@drawable/jdme_bg_homepage_duty_add_btn"
                android:layout_marginTop="8dp"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:textColor="#333333"
                android:textSize="12dp"
                android:text="@string/exp_manual_manual_add"
                android:includeFontPadding="false"/>
        </LinearLayout>
    </FrameLayout>

</LinearLayout>