<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">
    <LinearLayout
        android:layout_width="78dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="14dp"
        android:orientation="vertical">
        <FrameLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal">
            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"/>
            <pl.droidsonroids.gif.GifImageView
                android:id="@+id/iv_gif_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"
                android:visibility="gone"/>
            <ImageView
                android:id="@+id/iv_tip"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_gravity="top|end"
                android:src="@drawable/jdme_bg_red_dot_tip"
                android:visibility="visible" />
        </FrameLayout>
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="78dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="1dp"
            android:textColor="#333333"
            android:textSize="13dp"
            tools:text="我的画像画像"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAlignment="center"
            android:includeFontPadding="false"/>
    </LinearLayout>
</FrameLayout>