<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_root"
    android:background="#f8f8f9"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl_toolbar_normal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#f8f8f9">

        <ImageView
            android:id="@+id/iv_me_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:padding="10dp"
            android:layout_marginStart="1dp"
            android:src="@drawable/ic_text_leftarrow" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_me_back"
            android:text="@string/jdme_title_joyhr_apps"
            android:textColor="#1B1B1B"
            android:textSize="18dp"
            android:textStyle="bold"
            android:includeFontPadding="false"/>

        <ImageView
            android:id="@+id/iv_search"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_me_back"
            app:layout_constraintBottom_toBottomOf="parent"
            android:padding="10dp"
            android:layout_marginEnd="6dp"
            android:src="@drawable/jdme_workbench_app_search" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tl_category"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#f8f8f9"
        app:tabPaddingEnd="12dp"
        app:tabPaddingStart="12dp"
        app:tabBackground="@android:color/transparent"
        app:tabRippleColor="@android:color/transparent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_all_hr_apps"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="8dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f8f8f9"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

    </androidx.recyclerview.widget.RecyclerView>
</LinearLayout>