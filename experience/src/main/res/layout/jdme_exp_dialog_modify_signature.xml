<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_bg_exp_qrcode">


    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_sign"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="24dp"
        android:layout_weight="1"
        android:background="@color/transparent"
        android:gravity="top|left"
        android:hint="@string/exp_signature_input_sign"
        android:inputType="textMultiLine"
        android:maxLength="300"
        android:textColor="#232930"
        android:textColorHint="#8F959E"
        android:textSize="16sp"
        android:theme="@style/JoyWorkEditTextStyle" />

    <View
        android:id="@+id/v_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@id/et_sign"
        android:background="#DEE0E3" />


    <TextView
        android:id="@+id/tv_text_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/v_line"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:layout_marginTop="13dp"
        android:textColor="#8F959E"
        android:textSize="14sp"
        tools:text="299/300" />

    <Button
        android:id="@+id/btn_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/v_line"
        android:layout_alignParentEnd="true"
        android:background="@null"
        android:enabled="false"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="13dp"
        android:text="@string/exp_signature_save"
        android:textColor="@color/jdme_selector_btn_save" />


</RelativeLayout>
