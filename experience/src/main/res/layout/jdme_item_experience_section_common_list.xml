<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/commons_item_width"
    android:layout_height="match_parent">

    <View
        android:id="@+id/v_divider"
        android:layout_width="0.5dp"
        android:layout_height="32dp"
        android:layout_gravity="start|top"
        android:layout_marginTop="22dp"
        android:background="#E6E6E6"
        android:visibility="gone"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:shadowRadius="3.0"
                android:textColor="#806D5B"
                android:textSize="13dp"
                android:includeFontPadding="false"/>

            <FrameLayout
                android:id="@+id/iftv_eye_layout"
                android:layout_width="15dp"
                android:layout_height="30dp"
                android:layout_gravity="top"
                android:paddingTop="11dp"
                android:visibility="gone">
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/iftv_eye"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|end"
                    android:text="@string/icon_general_eye"
                    android:textColor="#806D5B"
                    android:textSize="@dimen/JMEIcon_14"/>
            </FrameLayout>
        </LinearLayout>
        <TextView
            android:id="@+id/tv_mid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:maxLines="1"
            android:ellipsize="end"
            android:shadowRadius="3.0"
            android:textColor="#4F4031"
            android:textSize="16dp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            tools:text="99,999.99" />
    </LinearLayout>
</FrameLayout>