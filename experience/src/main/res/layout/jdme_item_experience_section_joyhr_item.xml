<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_joyhr_item_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/ll_joyhr_item_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="8dp">
        <ImageView
            android:id="@+id/iv_joyhr_item_icon"
            android:layout_marginEnd="6dp"
            android:layout_width="14dp"
            android:layout_gravity="center_vertical"
            android:layout_height="14dp"/>
        <TextView
            android:id="@+id/tv_joyhr_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#1b1b1b"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="测试测试"/>
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_joyhr_item_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_joyhr_item_header">

        <TextView
            android:id="@+id/tv_joyhr_item_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_joyhr_item_unit"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#1B1B1B"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="124.5"/>

        <TextView
            android:id="@+id/tv_joyhr_item_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_joyhr_item_value"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/tv_joyhr_item_value"
            app:layout_constraintTop_toTopOf="@id/tv_joyhr_item_value"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constrainedWidth="true"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#9D9D9D"
            android:textSize="12sp"
            tools:text="小时"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>