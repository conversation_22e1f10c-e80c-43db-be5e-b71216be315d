<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff">

    <FrameLayout
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="top|center_horizontal">
        <ImageView
            android:id="@+id/icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"/>
        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"/>
        <ImageView
            android:id="@+id/iv_tip"
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_gravity="top|end"
            android:src="@drawable/jdme_bg_red_dot_tip"
            android:visibility="visible" />
    </FrameLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:layout_gravity="bottom|center_horizontal"
        android:gravity="bottom|center_horizontal"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#333333"
        android:textSize="12dp"
        android:includeFontPadding="false"/>

</FrameLayout>