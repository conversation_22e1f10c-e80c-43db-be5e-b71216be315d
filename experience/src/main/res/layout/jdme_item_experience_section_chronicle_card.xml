<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/chronicle_card_margin"
    android:background="@drawable/jdme_bg_chronicle_shadow"><!--12,12,7,35-->

    <androidx.cardview.widget.CardView
        android:layout_width="@dimen/chronicle_card_width"
        android:layout_height="313dp"
        app:cardCornerRadius="18dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/transparent">

        <ImageView
            android:id="@+id/bkgnd_iv"
            android:layout_width="match_parent"
            android:layout_height="207dp"
            android:scaleType="fitXY"/>

        <ImageView
            android:id="@+id/bkgnd_gradient"
            android:layout_width="match_parent"
            android:layout_height="104dp"
            android:layout_gravity="bottom"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_marginBottom="104dp"
            android:layout_gravity="bottom"
            android:background="#FFFFFF"/>

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="146dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="77dp"
            android:layout_gravity="start|bottom"
            android:gravity="start"
            android:maxLines="1"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#FFFFFF"
            android:textSize="16dp"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/detail_tv"
            android:layout_width="146dp"
            android:layout_height="56dp"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="16dp"
            android:layout_gravity="start|bottom"
            android:gravity="start|top"
            android:maxLines="4"
            android:lineSpacingExtra="2dp"
            android:ellipsize="end"
            android:textColor="#A8FFFFFF"
            android:textSize="10dp"
            android:includeFontPadding="false"/>
        
        <TextView
            android:id="@+id/date_tv"
            android:layout_width="wrap_content"
            android:layout_height="21dp"
            android:layout_marginBottom="106dp"
            android:layout_gravity="start|bottom"
            android:paddingHorizontal="8dp"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#FFFFFF"
            android:textSize="11dp"
            android:includeFontPadding="false"/>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/index_tv"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:background="@drawable/jdme_bg_chronicle_index"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:paddingHorizontal="11dp"
        android:layout_gravity="end|top"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#FFFFFF"
        android:textSize="10dp"
        android:includeFontPadding="false"/>
</FrameLayout>