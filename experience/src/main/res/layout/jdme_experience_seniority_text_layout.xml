<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/cv_senior"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top">
        <ImageView
            android:id="@+id/iv_senior_bkgnd"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"/>
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="18dp"
            app:cardBackgroundColor="@color/transparent"
            app:cardCornerRadius="9dp"
            app:cardElevation="0dp">
            <com.jd.oa.ui.LightAnimView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="1dp"/>
        </androidx.cardview.widget.CardView>

    </FrameLayout>
    <LinearLayout
        android:id="@+id/text_layout"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:paddingStart="10dp"
        android:paddingEnd="8dp"
        android:paddingBottom="1dp"
        android:orientation="horizontal"
        android:layout_gravity="top"
        android:gravity="center_vertical">
        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:gravity="center_vertical"
            android:layout_marginEnd="3dp">
            <ImageView
                android:id="@+id/iv_seniority_cn"
                android:layout_width="20dp"
                android:layout_height="10dp"
                android:scaleType="fitXY"/>
            <ImageView
                android:id="@+id/iv_seniority_en"
                android:layout_width="38dp"
                android:layout_height="9dp"
                android:scaleType="fitXY"/>
        </FrameLayout>
        <TextView
            android:id="@+id/tv_seniority"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="2dp"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="10dp"
            android:textAlignment="center"
            android:includeFontPadding="false"/>
        <com.jd.oa.ui.IconFontView
            android:id="@+id/btn_seniority"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_direction_right"
            android:textColor="#666666"
            android:textSize="@dimen/JMEIcon_08"/>
    </LinearLayout>
</FrameLayout>