<?xml version="1.0" encoding="utf-8"?>
<!--头像浮层-->
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layer_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <FrameLayout
        android:id="@+id/avatar_layout"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:padding="8dp"
        android:layout_marginTop="36dp">
        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <ImageView
            android:id="@+id/iv_pendant"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="54dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="17dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_userinfo"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#333333"
            android:textStyle="bold"
            android:textSize="22dp"
            android:includeFontPadding="false"
            android:visibility="invisible"
            tools:visibility="visible"/>

        <TextView
            android:id="@+id/btn_feedback"
            android:layout_width="wrap_content"
            android:layout_marginEnd="22dp"
            android:layout_marginStart="2dp"
            android:textSize="12dp"
            android:text="@string/jdme_feedback"
            android:textColor="#505050"
            android:background="@drawable/jdme_bg_btn_feedback_light"
            android:layout_height="wrap_content"/>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/btn_skin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_me_skin"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"/>
        <View
            android:id="@+id/skin_hint_dot"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:background="@drawable/jdme_bg_experience_skin_hint_dot"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="10dp"
            android:visibility="invisible"/>
        <com.jd.oa.ui.IconFontView
            android:id="@+id/btn_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_general_set"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"/>
    </LinearLayout>

    <com.jd.oa.badge.RedDotView
        android:id="@+id/setting_badge"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="9dp"
        android:layout_width="6dp"
        android:layout_height="6dp" />
</RelativeLayout>