<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/line_one"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <FrameLayout
            android:id="@+id/container_1"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:background="@drawable/jdme_bg_gray_corner"/>
        <FrameLayout
            android:id="@+id/container_2"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:background="@drawable/jdme_bg_gray_corner"/>
        <FrameLayout
            android:id="@+id/container_3"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:background="@drawable/jdme_bg_gray_corner"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/line_two"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">
        <FrameLayout
            android:id="@+id/container_4"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:background="@drawable/jdme_bg_gray_corner"/>
        <FrameLayout
            android:id="@+id/container_5"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:background="@drawable/jdme_bg_gray_corner"/>
        <FrameLayout
            android:id="@+id/container_6"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:background="@drawable/jdme_bg_gray_corner"/>
    </LinearLayout>
</LinearLayout>