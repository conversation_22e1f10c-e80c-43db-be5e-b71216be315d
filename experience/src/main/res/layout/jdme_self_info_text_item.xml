<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/top_margin"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/self_info_item_bkgnd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"
        android:orientation="horizontal"
        android:background="@drawable/jdme_bg_self_info_middle">
        <TextView
            android:id="@+id/self_info_item_name"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_marginTop="17dp"
            android:layout_marginEnd="32dp"
            android:layout_gravity="top"
            android:gravity="center_vertical"
            android:textSize="16dp"
            android:textColor="#2E2D2D"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"/>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:layout_marginEnd="4dp">
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"/>
            <FrameLayout
                android:id="@+id/self_info_item_text_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="18dp"
                android:paddingBottom="18dp">
                <TextView
                    android:id="@+id/self_info_item_text_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:gravity="end"
                    android:textSize="14dp"
                    android:textColor="#666666"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:includeFontPadding="false"/>
            </FrameLayout>

        </LinearLayout>
        <com.jd.oa.ui.IconFontView
            android:id="@+id/self_info_item_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:text="@string/icon_direction_right"
            android:textColor="#BFC1C4"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/self_info_item_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#ffffff"
        android:visibility="gone">
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="#EBEDF0"/>
    </FrameLayout>

    <View
        android:id="@+id/bottom_margin"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:visibility="gone"/>
</LinearLayout>