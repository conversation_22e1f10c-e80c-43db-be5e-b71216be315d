<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="47dp"
    android:layout_marginHorizontal="4dp"
    android:layout_marginTop="0.5dp"
    android:layout_marginBottom="0.5dp"
    android:orientation="horizontal"
    android:background="@drawable/jdme_bg_homepage_show_duty">

    <TextView
        android:id="@+id/tag_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="150dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="14dp"
        android:textColor="#333333"
        android:includeFontPadding="false"/>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="8dp"
        android:textColor="#999999"
        android:textSize="@dimen/JMEIcon_10"
        android:text="@string/icon_prompt_close"
        android:visibility="visible"/>

    <View
        android:layout_width="16dp"
        android:layout_height="8dp"
        android:layout_gravity="center_vertical"/>
</LinearLayout>