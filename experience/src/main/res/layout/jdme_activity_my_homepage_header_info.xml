<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/ll_head"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:layout_marginTop="46dp"
        android:orientation="vertical"
        android:background="@drawable/jdme_bg_homepage_header">
        <!--名字-->
        <TextView
            android:id="@+id/homepage_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="44dp"
            android:gravity="center_horizontal"
            android:textStyle="bold"
            android:textSize="20dp"
            android:textColor="#333333"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"/>
        <!--心情状态-->
        <LinearLayout
            android:id="@+id/homepage_feeling_layout"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="10dp"
            android:paddingStart="8dp"
            android:paddingEnd="6dp"
            android:layout_gravity="center_horizontal"
            android:orientation="horizontal"
            android:background="@drawable/jdme_bg_homepage_feeling">
            <ImageView
                android:id="@+id/homepage_feeling_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="1dp"/>
            <TextView
                android:id="@+id/homepage_feeling_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="100dp"
                android:layout_gravity="center_vertical"
                android:textSize="12dp"
                android:textColor="#62656D"
                android:maxLines="1"
                android:ellipsize="end"
                android:includeFontPadding="false"/>
            <com.jd.oa.ui.IconFontView
                android:id="@+id/homepage_feeling_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_gravity="center_vertical"
                android:text="@string/icon_direction_right"
                android:textColor="#8F959E"
                android:textSize="@dimen/JMEIcon_10"/>
        </LinearLayout>

        <!--标签-->
        <FrameLayout
            android:id="@+id/tags_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="18dp"
            android:layout_marginBottom="8dp">
            <!--普通标签-->
            <com.zhy.view.flowlayout.TagFlowLayout
                android:id="@+id/tag_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:tag_gravity="left"/>

            <!--他人视角无标签-->
            <TextView
                android:id="@+id/no_tag_tv"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_gravity="center_horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp"
                android:textColor="#62656D"
                android:textSize="12dp"
                android:text="@string/exp_homepage_empty_tag_other"/>
        </FrameLayout>

        <!--点赞-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="22dp"
            android:orientation="horizontal">
            <FrameLayout
                android:id="@+id/homepage_visited"
                android:layout_width="67dp"
                android:layout_height="match_parent"
                android:paddingStart="8dp"
                android:paddingEnd="8dp">
                <TextView
                    android:id="@+id/homepage_visited_count"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_gravity="top"
                    android:gravity="center_horizontal"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:textSize="16dp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:includeFontPadding="false"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="33dp"
                    android:layout_gravity="top"
                    android:gravity="center_horizontal"
                    android:textColor="#999999"
                    android:textSize="11dp"
                    android:text="@string/exp_homepage_visited"
                    android:includeFontPadding="false"/>
            </FrameLayout>
            <View
                android:layout_width="0.5dp"
                android:layout_height="26dp"
                android:background="#F1F1F1"
                android:layout_gravity="center_vertical"/>
            <FrameLayout
                android:id="@+id/homepage_liked"
                android:layout_width="67dp"
                android:layout_height="match_parent"
                android:paddingStart="8dp"
                android:paddingEnd="8dp">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_gravity="top">
                    <View
                        android:id="@+id/view"
                        android:layout_width="0.5dp"
                        android:layout_height="1dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/homepage_liked_count"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"/>
                    <TextView
                        android:id="@+id/homepage_liked_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toEndOf="@id/view"
                        app:layout_constraintEnd_toStartOf="@id/liked_red_dot"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintHorizontal_bias="0"
                        android:textStyle="bold"
                        android:textColor="#333333"
                        android:textSize="16dp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:includeFontPadding="false"/>
                    <View
                        android:id="@+id/liked_red_dot"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        app:layout_constraintStart_toEndOf="@id/homepage_liked_count"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        android:background="@drawable/jdme_bg_red_dot"
                        android:visibility="gone"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="33dp"
                    android:layout_gravity="top"
                    android:gravity="center_horizontal"
                    android:textColor="#999999"
                    android:textSize="11dp"
                    android:text="@string/exp_homepage_liked"
                    android:includeFontPadding="false"/>
            </FrameLayout>
            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
            <TextView
                android:id="@+id/go_self_or_msg"
                android:layout_width="84dp"
                android:layout_height="34dp"
                android:background="@drawable/jdme_bg_homepage_go_self_or_msg"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:textSize="14dp"
                android:textColor="#FF6B23"/>
            <com.jd.oa.ui.IconFontView
                android:id="@+id/personal_card"
                android:layout_width="44dp"
                android:layout_height="34dp"
                android:background="@drawable/jdme_bg_homepage_personal_card"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:gravity="center"
                android:text="@string/icon_me_businesscard"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_20"/>
        </LinearLayout>
    </LinearLayout>

    <!--头像-->
    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/homepage_avatar"
        android:layout_width="78dp"
        android:layout_height="78dp"
        android:layout_gravity="top|center_horizontal"
        android:contentDescription="@string/me_user_icon"
        android:src="@drawable/profile_section_avatar_default"/>
    <!--挂件-->
    <ImageView
        android:id="@+id/homepage_pendant"
        android:layout_width="78dp"
        android:layout_height="78dp"
        android:layout_gravity="top|center_horizontal"
        android:visibility="invisible"
        android:contentDescription="@string/me_user_icon"/>

</FrameLayout>