<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="210dp"
    android:layout_height="92dp"
    android:layout_marginEnd="8dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="6dp">

    <ImageView
        android:id="@+id/iv_bg_gradient"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"/>

    <ImageView
        android:id="@+id/iv_bg_right"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:scaleType="fitEnd"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="11dp"
        android:layout_gravity="top"
        android:maxLines="2"
        android:textSize="14dp"
        android:textColor="#774100"
        android:lineSpacingExtra="3dp"
        android:ellipsize="end"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="bottom"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp">
        <TextView
            android:id="@+id/tv_category"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="8dp"
            android:maxLines="1"
            android:textSize="11dp"
            android:textColor="#666666"
            android:ellipsize="end"
            android:includeFontPadding="false"/>
        <FrameLayout
            android:id="@+id/button_layout"
            android:layout_width="66dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/jdme_exp_recommend_card_list_button">
            <TextView
                android:id="@+id/tv_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:textSize="11dp"
                android:textColor="#333333"
                android:ellipsize="end"
                android:includeFontPadding="false" />
        </FrameLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>