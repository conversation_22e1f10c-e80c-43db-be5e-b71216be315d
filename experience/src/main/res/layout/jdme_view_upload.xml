<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="wrap_content"
    android:paddingHorizontal="20dp"
    android:paddingVertical="12dp"
    android:background="#B3000000"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_centerHorizontal="true"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="16dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/profile_section_qrcode_loading"
        android:layout_height="16dp" />

    <TextView
        android:id="@+id/tv"
        android:layout_gravity="center_vertical"
        tools:text="111111"
        android:layout_width="wrap_content"
        android:textSize="14sp"
        android:layout_marginStart="10dp"
        android:textColor="#FFFFFF"
        android:layout_height="wrap_content"/>

</LinearLayout>