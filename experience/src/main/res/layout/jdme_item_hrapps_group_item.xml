<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_app_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.jd.oa.elliptical.SuperEllipticalImageView
        android:id="@+id/iv_app_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="40dp"
        android:layout_height="40dp"/>
    
    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="68dp"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toStartOf="@id/iv_app_icon"
        app:layout_constraintEnd_toEndOf="@id/iv_app_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_app_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center"
        android:textColor="#1B1B1B"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="我的假期我的假期我的假期我的假期"/>
</androidx.constraintlayout.widget.ConstraintLayout>