<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="47dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/shape_bg_exp_corner_white_8dp"
        android:orientation="horizontal"
        android:padding="10dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_joysky_manage"
            android:textSize="@dimen/JMEIcon_22"/>

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="@string/icon_general_set"
            android:textSize="@dimen/JMEIcon_22"/>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_1"
        android:layout_width="118dp"
        android:layout_height="103dp"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="64dp"
        android:layout_marginEnd="99dp"
        android:src="@drawable/exp_guide_line2" />


    <LinearLayout
        android:id="@+id/ll_3"
        android:layout_width="275dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_1"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="28dp"
        android:background="@drawable/shape_bg_exp_corner_white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginLeft="13dp"
                android:src="@drawable/exp_guide_icon_1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="@string/exp_guide_app5"
                android:textColor="#333333"
                android:textSize="14dp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="10dp"
            android:background="#E6E6E6" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginLeft="13dp"
                android:src="@drawable/exp_guide_icon_6" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="@string/exp_guide_app6"
                android:textColor="#333333"
                android:textSize="14dp" />

        </LinearLayout>
    </LinearLayout>

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/iv_title2"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_below="@id/ll_3"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="28dp"
        android:src="@drawable/exp_guide_title_2" />

    <TextView
        android:id="@+id/tv_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_title2"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="78dp"
        android:background="@drawable/jdme_bg_corner_gray_line2"
        android:paddingHorizontal="46dp"
        android:paddingVertical="9dp"
        android:text="@string/exp_guide_start"
        android:textColor="#FFFFFF"
        android:textSize="14dp"
        android:textStyle="bold" />
</RelativeLayout>