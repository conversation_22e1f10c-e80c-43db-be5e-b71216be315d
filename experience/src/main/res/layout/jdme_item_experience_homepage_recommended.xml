<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="104dp"
    android:layout_height="140dp"
    android:layout_marginEnd="10dp"
    app:cardBackgroundColor="#FFFFFF"
    app:cardCornerRadius="8dp"
    app:cardElevation="0dp">

    <ImageView
        android:id="@+id/recommend_bkgnd"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_gravity="top"
        android:scaleType="center"/><!--不要使用其他scaleType-->

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#07000000"/>

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/recommend_avatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="17dp"
        android:layout_gravity="center_horizontal"
        android:contentDescription="@string/me_user_icon"
        android:src="@drawable/profile_section_avatar_default"/>

    <View
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginTop="15dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/jdme_bg_homepage_recommend_avatar_circle"/>

    <TextView
        android:id="@+id/recommend_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="75dp"
        android:layout_marginBottom="12dp"
        android:gravity="center_horizontal"
        android:textColor="#333333"
        android:textSize="14dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"/>

    <TextView
        android:id="@+id/recommend_btn"
        android:layout_width="68dp"
        android:layout_height="22dp"
        android:background="@drawable/jdme_bg_homepage_recommend_btn"
        android:layout_marginTop="101dp"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:textColor="#FF6B23"
        android:textSize="12dp"
        android:text="@string/exp_homepage_recommended_open"/>

</androidx.cardview.widget.CardView>