<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/instruction_dialog"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/jdme_bg_homepage_instruction_dialog">
    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_gravity="top"
        android:gravity="center"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textStyle="bold"
        android:textColor="#242931"
        android:textSize="18dp"
        android:maxLines="1"
        android:ellipsize="end"/>
    <com.jd.oa.ui.IconFontView
        android:id="@+id/close"
        android:layout_width="48dp"
        android:layout_height="44dp"
        android:layout_gravity="top|end"
        android:gravity="center"
        android:textSize="@dimen/JMEIcon_18"
        android:textColor="#333333"
        android:text="@string/icon_prompt_close"/>
    <FrameLayout
        android:id="@+id/webview_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="44dp"
        android:layout_marginBottom="12dp"
        android:background="#FFFFFF"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_gravity="bottom"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="11dp"
        android:background="@color/gray"/>
</FrameLayout>