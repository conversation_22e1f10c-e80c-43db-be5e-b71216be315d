<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/instruction_content_margin"
    android:layout_marginEnd="@dimen/instruction_content_margin"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="33dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/jdme_exp_instruction_title_bg">
        <TextView
            android:id="@+id/content_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="20dp"
            android:maxLines="1"
            android:includeFontPadding="false"/>
    </FrameLayout>

    <TextView
        android:id="@+id/content_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:lineSpacingExtra="10dp"
        android:textColor="#333333"
        android:textSize="16dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:includeFontPadding="false"/>

    <RelativeLayout
        android:id="@+id/update_btn"
        android:layout_width="219dp"
        android:layout_height="44dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="23dp"
        android:background="@drawable/jdme_exp_instruction_button_light_bg"
        android:visibility="gone">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:textColor="#333333"
            android:textSize="16dp"
            android:text="@string/exp_instruction_update_btn"
            android:maxLines="1"
            android:includeFontPadding="false"/>
        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="@string/icon_direction_right"
            android:textColor="#A1A1A1"
            android:textSize="@dimen/JMEIcon_14"/>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/setting_btn"
        android:layout_width="219dp"
        android:layout_height="44dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/jdme_exp_instruction_button_light_bg"
        android:visibility="gone">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:textColor="#333333"
            android:textSize="16dp"
            android:text="@string/exp_instruction_setting_btn"
            android:maxLines="1"
            android:includeFontPadding="false"/>
        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/setting_switch"
            style="@style/ExpSwitchStyle"
            android:layout_width="44dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"/>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/create_btn"
        android:layout_width="217dp"
        android:layout_height="42dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="34dp"
        android:layout_marginBottom="31dp"
        android:background="@drawable/jdme_exp_instruction_button_dark_bg"
        android:visibility="gone">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#ffffff"
            android:textSize="16dp"
            android:text="@string/exp_instruction_create_btn"
            android:maxLines="1"
            android:includeFontPadding="false"/>
    </RelativeLayout>
</LinearLayout>