<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="4dp"
    android:orientation="vertical"
    android:background="@drawable/jdme_bg_shadow_corner">

    <com.jd.oa.experience.view.TitleView
        android:id="@+id/title_view"
        android:layout_width="match_parent"
        android:layout_height="48dp"/>

    <!--无卡片-->
    <View
        android:id="@+id/empty_iv"
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="72dp"
        android:background="@drawable/jdme_bg_chronicle_empty"/>

    <TextView
        android:id="@+id/empty_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="239dp"
        android:layout_marginBottom="12dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#8F959E"
        android:textSize="14dp"
        android:text="@string/exp_chronicle_empty_text"
        android:includeFontPadding="false"/>

    <!--1张卡片-->
    <include layout="@layout/jdme_item_experience_section_chronicle_card"
        android:id="@+id/single_card_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="64dp"
        android:visibility="gone"/>

    <!--2张以上-->
    <com.jd.oa.experience.view.ChronicleRecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="64dp"
        android:overScrollMode="never"
        android:scrollbars="none"/>
    
    <LinearLayout
        android:id="@+id/reset"
        android:layout_width="wrap_content"
        android:layout_height="231dp"
        android:minWidth="28dp"
        android:alpha="0"
        android:layout_gravity="end|bottom"
        android:layout_marginBottom="88dp"
        android:orientation="vertical"
        android:background="@drawable/jdme_bg_chronicle_reset">

        <View
            android:layout_width="28dp"
            android:layout_height="0dp"
            android:layout_weight="1"/>
        <TextView
            android:id="@+id/reset_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:textColor="#999999"
            android:textSize="14dp"
            android:text="@string/exp_chronicle_reset"
            android:includeFontPadding="false"/>
        <com.jd.oa.ui.IconFontView
            android:id="@+id/reset_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:layout_gravity="center_horizontal"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_14"
            android:text="@string/icon_me_back"/>
        <View
            android:layout_width="28dp"
            android:layout_height="0dp"
            android:layout_weight="1"/>
    </LinearLayout>

    <ImageView
        android:id="@+id/dog"
        android:layout_width="96dp"
        android:layout_height="114dp"
        android:layout_marginTop="276dp"/>

</FrameLayout>