<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="116dp"
    android:background="@drawable/jdme_bg_homepage_dialog_duty">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/input_et"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="24dp"
        android:background="@color/transparent"
        android:gravity="start|top"
        android:inputType="textMultiLine"
        android:textColorHint="#8F959E"
        android:textColor="#232930"
        android:textSize="16dp"
        android:theme="@style/JoyWorkEditTextStyle" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="70dp"
        android:background="#DEE0E3" />

    <TextView
        android:id="@+id/tv_text_num"
        android:layout_width="wrap_content"
        android:layout_height="46dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="70dp"
        android:layout_gravity="start|bottom"
        android:gravity="center_vertical"
        android:textColor="#333333"
        android:textSize="14dp"
        tools:text="0/8" />

    <Button
        android:id="@+id/btn_save"
        android:layout_width="wrap_content"
        android:layout_height="46dp"
        android:layout_marginTop="70dp"
        android:layout_gravity="end|bottom"
        android:gravity="center_vertical"
        android:background="@null"
        android:enabled="false"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:paddingHorizontal="16dp"
        android:text="@string/exp_homepage_duty_confirm"
        android:textColor="@color/jdme_selector_btn_save" />


</FrameLayout>
