<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_bg_exp_qrcode">

    <ImageView
        android:id="@+id/v_top"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_alignParentTop="true"
        android:background="@drawable/jdme_exp_bg_qrcode" />

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_avatar"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="40dp"
        android:layout_marginTop="31dp"
        android:clickable="true"
        android:focusable="true"
        android:src="@drawable/jdme_icon_exp_defult_photo" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="16dp"
        android:layout_toEndOf="@id/iv_avatar"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="#333333"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="琼恩琼恩琼恩恩琼恩琼恩琼恩琼恩琼恩恩琼恩琼恩琼恩琼恩琼恩恩琼恩琼恩" />

    <TextView
        android:id="@+id/tv_job"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_name"
        android:layout_marginStart="8dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="16dp"
        android:layout_toRightOf="@id/iv_avatar"
        android:ellipsize="end"
        android:lines="2"
        android:textColor="#666666"
        android:textSize="12sp"
        tools:text="交互设计师岗交互设计师岗交互设计师岗交互设计师岗交互设计师岗交互设计师岗交互设计师岗交互设计师岗交互设计师岗交互设计师岗" />

    <RelativeLayout
        android:id="@+id/rl_qrcode"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_below="@id/iv_avatar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:background="@drawable/jdme_bg_exp_qrcode_inner"
        android:padding="25dp">

        <ImageView
            android:id="@+id/iv_qrcode"
            android:layout_width="300dp"
            android:layout_height="300dp"
            android:layout_centerHorizontal="true"
            android:src="@drawable/jdme_exp_qrcode_defult" />

        <LinearLayout
            android:id="@+id/ll_load_status"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:background="#B1000000"
            android:orientation="vertical"
            android:visibility="visible">

            <ImageView
                android:id="@+id/iv_load"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="90dp" />

            <TextView
                android:id="@+id/tv_load_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="加载失败\n点击刷新二维码" />
        </LinearLayout>
    </RelativeLayout>


    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_qrcode"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="52dp"
        android:text="@string/exp_qrcode_tip"
        android:textColor="@color/me_setting_foreground_light"
        android:textSize="12sp" />


</RelativeLayout>