<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/icon_iv"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="11dp"
        android:scaleType="fitXY"/>

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="10.5dp"
        android:gravity="start|top"
        android:maxLines="1"
        android:ellipsize="end"
        android:textStyle="bold"
        android:textColor="#333333"
        android:textSize="14dp"
        android:includeFontPadding="false"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="9dp"
        android:layout_gravity="bottom">

        <TextView
            android:id="@+id/count_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/unit_tv"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_bias="0"
            android:gravity="bottom"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#666666"
            android:textSize="16dp"
            android:includeFontPadding="false"
            android:fontFamily="@font/din_condensed_bold"/>

        <TextView
            android:id="@+id/unit_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="75dp"
            android:layout_marginBottom="2dp"
            app:layout_constraintStart_toEndOf="@id/count_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:gravity="bottom"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#999999"
            android:textSize="12dp"
            android:includeFontPadding="false"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>