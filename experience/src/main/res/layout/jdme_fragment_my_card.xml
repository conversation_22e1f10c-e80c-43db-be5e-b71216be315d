<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_setting_background"
    android:orientation="vertical"
    android:paddingEnd="40dp"
    android:paddingLeft="40dp"
    android:paddingRight="40dp"
    android:paddingStart="40dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="1dp"
        android:layout_marginEnd="1dp"
        android:layout_marginBottom="-5dp"
        android:src="@drawable/jdme_icon_mycard_header" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingBottom="26dp"
        android:background="@drawable/jdme_expcard_bottom_bg"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp">

            <com.jd.oa.ui.CircleImageView
                android:id="@+id/jdme_id_mycard_avator"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="14dp"
                android:src="@drawable/jdme_icon_mycard_avator_default" />

            <TextView
                android:id="@+id/jdme_id_mycard_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/jdme_id_mycard_avator"
                android:layout_marginEnd="15dp"
                android:layout_marginTop="8dp"
                android:layout_toEndOf="@id/jdme_id_mycard_avator"
                android:textColor="@color/comm_text_title"
                android:textSize="16sp"
                tools:text="琼恩"/>

            <TextView
                android:id="@+id/jdme_id_mycard_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignStart="@id/jdme_id_mycard_name"
                android:layout_below="@id/jdme_id_mycard_name"
                android:layout_marginTop="4dp"
                android:layout_toEndOf="@id/jdme_id_mycard_avator"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="#848484"
                android:textSize="13sp"
                tools:text="京东商城-视觉设计师"/>
        </RelativeLayout>

        <!--给的二维码图片本身就有一圈27px的空白-->
        <ImageView
            android:scaleType="fitXY"
            android:layout_marginLeft="70dp"
            android:layout_marginRight="70dp"
            android:id="@+id/jdme_id_mycard_qt"
            android:layout_width="206dp"
            android:layout_height="206dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:src="@drawable/jdme_mine_main_qt" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="12dp"
            android:text="@string/jdme_my_card_tip"
            android:textColor="#848484"
            android:textSize="13sp" />
    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2.5" />
</LinearLayout>

