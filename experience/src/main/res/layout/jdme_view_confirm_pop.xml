<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    android:background="@drawable/jdme_mine_btn_bg">

    <Button
        android:id="@+id/me_use"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="160dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/jdme_bg_exp_punch"
        android:gravity="center_vertical|center_horizontal"
        android:text="@string/me_portrait_use"
        android:textColor="@color/white"
        android:textSize="@dimen/comm_text_title" />

</RelativeLayout>
