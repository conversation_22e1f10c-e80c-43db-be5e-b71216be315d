<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.jd.oa.experience.view.NestedHorizontalRecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="6dp"
        android:orientation="horizontal"
        android:overScrollMode="never"
        android:scrollbars="none" />

    <com.jd.oa.experience.view.PagerSnapIndicator
        android:id="@+id/indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:centered="true"
        app:fillColor="#333333"
        app:pageColor="#E6E6E6"
        app:radius="2.5dp"
        app:strokeWidth="0dp" />

    <View
        android:id="@+id/mSpace"
        android:layout_width="match_parent"
        android:layout_height="10dp" />

</LinearLayout>