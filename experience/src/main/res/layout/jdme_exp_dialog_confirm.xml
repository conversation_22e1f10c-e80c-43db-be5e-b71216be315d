<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/jdme_exp_instruction_dialog">
        <TextView
            android:id="@+id/hint_text_view"
            android:layout_width="match_parent"
            android:maxWidth="280dp"
            android:layout_height="wrap_content"
           android:layout_margin="24dp"
            android:gravity="center_horizontal"
            android:textColor="#232930"
            android:textSize="16dp"
            android:ellipsize="end"
            android:includeFontPadding="false"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#DEE0E3"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/cancel"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                tools:visibility="gone"
                android:text="@string/exp_instruction_dialog_button_cancel"
                android:textColor="#232930"
                android:textSize="16dp"/>

            <View
                android:id="@+id/divider"
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="#DEE0E3"/>

            <TextView
                android:id="@+id/confirm"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                tools:text="111111"
                android:textColor="#FE3B30"
                android:textSize="16dp"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>