<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_course_container"
    android:layout_width="124dp"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_course_image"
        android:layout_width="match_parent"
        android:layout_height="77dp"
        app:cardCornerRadius="3.56dp"
        app:cardElevation="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/iv_course_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"/>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_course_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textSize="14sp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textColor="#1B1B1B"
        app:layout_constraintTop_toBottomOf="@id/card_course_image"
        app:layout_constraintStart_toStartOf="@id/card_course_image"
        app:layout_constraintEnd_toEndOf="@id/card_course_image"
        tools:text="【2023年储备项目】银储备人才项目第3期"/>

</androidx.constraintlayout.widget.ConstraintLayout>