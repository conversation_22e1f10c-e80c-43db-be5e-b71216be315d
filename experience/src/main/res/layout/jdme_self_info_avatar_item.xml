<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/top_margin"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/self_info_item_bkgnd"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="12dp"
        android:background="@drawable/jdme_bg_self_info_middle">
        <TextView
            android:id="@+id/self_info_item_name"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_marginEnd="32dp"
            android:gravity="center_vertical"
            android:textSize="16dp"
            android:textColor="#2E2D2D"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"/>

        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"/>

        <FrameLayout
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="4dp">

            <com.jd.oa.elliptical.SuperEllipticalImageView
                android:id="@+id/self_info_item_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:contentDescription="@string/me_user_icon"
                android:src="@drawable/profile_section_avatar_default"/>

            <ImageView
                android:id="@+id/self_info_item_pendant"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:contentDescription="@string/me_user_icon"/>
        </FrameLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/self_info_item_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:text="@string/icon_direction_right"
            android:textColor="#BFC1C4"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/self_info_item_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#ffffff"
        android:visibility="gone">
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="#EBEDF0"/>
    </FrameLayout>

    <View
        android:id="@+id/bottom_margin"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:visibility="gone"/>
</LinearLayout>