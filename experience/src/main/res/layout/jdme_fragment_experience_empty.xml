<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.pulltorefresh.PullToRefreshLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/empty_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F3F3F3">
    <ScrollView
        android:id="@+id/empty_layout_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        android:scrollbars="none">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="95dp">
                <com.jd.oa.elliptical.SuperEllipticalImageView
                    android:layout_width="46dp"
                    android:layout_height="46dp"
                    android:layout_marginTop="10dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:src="#FFFFFF"/>
                <androidx.cardview.widget.CardView
                    android:layout_width="112dp"
                    android:layout_height="25dp"
                    android:layout_marginStart="52dp"
                    android:layout_marginTop="10dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    app:cardCornerRadius="14dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#FFFFFF"/>
                <androidx.cardview.widget.CardView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="40dp"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    app:cardCornerRadius="4dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#FFFFFF"/>
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/btn_setting_empty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    android:text="@string/icon_general_set"
                    android:textColor="#232930"
                    android:textSize="@dimen/JMEIcon_22"/>
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="11dp"
                    android:layout_marginStart="52dp"
                    android:layout_marginBottom="31dp"
                    android:layout_marginEnd="37dp"
                    android:layout_alignParentBottom="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#FFFFFF"/>
            </RelativeLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="14dp"
                android:orientation="horizontal">
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:cardCornerRadius="4dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#FFFFFF"/>
                <View
                    android:layout_width="9dp"
                    android:layout_height="match_parent"/>
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:cardCornerRadius="4dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#FFFFFF"/>
                <View
                    android:layout_width="9dp"
                    android:layout_height="match_parent"/>
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:cardCornerRadius="4dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#FFFFFF"/>
                <View
                    android:layout_width="9dp"
                    android:layout_height="match_parent"/>
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    app:cardCornerRadius="4dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#FFFFFF"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="98dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/jdme_bg_experience_empty_card"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:paddingStart="16dp"
                    android:orientation="vertical">
                    <androidx.cardview.widget.CardView
                        android:layout_width="61dp"
                        android:layout_height="10dp"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="19dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="49dp"
                        android:layout_height="21dp"
                        app:cardCornerRadius="11dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="11dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="25dp"
                        android:layout_height="10dp"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="10dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                </LinearLayout>
                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="35dp"
                    android:layout_marginBottom="31dp"
                    android:background="#E6E6E6"/>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:paddingStart="16dp"
                    android:orientation="vertical">
                    <androidx.cardview.widget.CardView
                        android:layout_width="61dp"
                        android:layout_height="10dp"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="19dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="49dp"
                        android:layout_height="21dp"
                        app:cardCornerRadius="11dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="11dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="25dp"
                        android:layout_height="10dp"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="10dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                </LinearLayout>
                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="35dp"
                    android:layout_marginBottom="31dp"
                    android:background="#E6E6E6"/>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:paddingStart="16dp"
                    android:orientation="vertical">
                    <androidx.cardview.widget.CardView
                        android:layout_width="61dp"
                        android:layout_height="10dp"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="19dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="49dp"
                        android:layout_height="21dp"
                        app:cardCornerRadius="11dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="11dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="25dp"
                        android:layout_height="10dp"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        android:layout_marginTop="10dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_marginBottom="8dp"
                android:orientation="horizontal">
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_experience_empty_card">
                    <androidx.cardview.widget.CardView
                        android:layout_width="61dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="80dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginBottom="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentBottom="true"
                        app:cardCornerRadius="11dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginEnd="17dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="4dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                </RelativeLayout>
                <View
                    android:layout_width="9dp"
                    android:layout_height="match_parent"/>
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_experience_empty_card">
                    <androidx.cardview.widget.CardView
                        android:layout_width="61dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="80dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginBottom="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentBottom="true"
                        app:cardCornerRadius="11dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginEnd="17dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="4dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                </RelativeLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:orientation="horizontal">
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_experience_empty_card">
                    <androidx.cardview.widget.CardView
                        android:layout_width="61dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="80dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginBottom="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentBottom="true"
                        app:cardCornerRadius="11dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginEnd="17dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="4dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                </RelativeLayout>
                <View
                    android:layout_width="9dp"
                    android:layout_height="match_parent"/>
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_experience_empty_card">
                    <androidx.cardview.widget.CardView
                        android:layout_width="61dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="80dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginBottom="14dp"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentBottom="true"
                        app:cardCornerRadius="11dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginEnd="17dp"
                        android:layout_marginTop="14dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentTop="true"
                        app:cardCornerRadius="4dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#F3F3F3"/>
                </RelativeLayout>
            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:layout_width="66dp"
                android:layout_height="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                app:cardBackgroundColor="#E8E8E8"
                app:cardElevation="0dp"
                app:cardCornerRadius="10dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="254dp"
                android:orientation="horizontal">
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_experience_empty_card">
                    <androidx.cardview.widget.CardView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginTop="11dp"
                        android:layout_marginStart="12dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentStart="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="8dp"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="55dp"
                        android:layout_height="16dp"
                        android:layout_marginTop="11dp"
                        android:layout_marginStart="35dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentStart="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="8dp"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginTop="33dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="14dp"
                        android:layout_alignParentTop="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="8dp"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="144dp"
                        android:layout_marginTop="54dp"
                        android:layout_marginStart="12dp"
                        android:layout_marginEnd="12dp"
                        android:layout_alignParentTop="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="6dp"/>
                </RelativeLayout>
                <View
                    android:layout_width="10dp"
                    android:layout_height="match_parent"/>
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/jdme_bg_experience_empty_card">
                    <androidx.cardview.widget.CardView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginTop="11dp"
                        android:layout_marginStart="12dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentStart="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="8dp"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="55dp"
                        android:layout_height="16dp"
                        android:layout_marginTop="11dp"
                        android:layout_marginStart="35dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentStart="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="8dp"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginTop="33dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="14dp"
                        android:layout_alignParentTop="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="8dp"/>
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="144dp"
                        android:layout_marginTop="54dp"
                        android:layout_marginStart="12dp"
                        android:layout_marginEnd="12dp"
                        android:layout_alignParentTop="true"
                        app:cardBackgroundColor="#F3F3F3"
                        app:cardElevation="0dp"
                        app:cardCornerRadius="6dp"/>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
    <com.jd.oa.ui.LoadingView
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</com.jd.oa.pulltorefresh.PullToRefreshLayout>