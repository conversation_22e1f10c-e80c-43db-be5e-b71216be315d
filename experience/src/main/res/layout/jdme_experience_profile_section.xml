<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="16dp"
    android:layout_marginBottom="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="62dp"><!--留出头像位置-->
        <!--中间部分第一行-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="top">
                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="top"
                    android:includeFontPadding="false"/>
            </LinearLayout>
        </LinearLayout>
        <!--中间部分第二行，不一定显示-->
        <LinearLayout
            android:id="@+id/badge_and_seniority_line"
            android:layout_width="match_parent"
            android:layout_height="23dp"
            android:orientation="horizontal">
            <LinearLayout
                android:id="@+id/culture_wrapper"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible">
                <FrameLayout
                    android:id="@+id/layout_culture"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"><!-- 生日 > 司龄 > 党员 -->
                    <ImageView
                        android:id="@+id/iv_culture"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:scaleType="fitCenter"/>
                    <TextView
                        android:id="@+id/tv_culture"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxWidth="18dp"
                        android:layout_gravity="bottom|center_horizontal"
                        android:layout_marginBottom="5.5dp"
                        android:maxLines="1"
                        android:textColor="#FFFFFF"
                        android:textSize="9dp"
                        android:textAlignment="center"
                        android:includeFontPadding="false"
                        android:visibility="gone"/>
                </FrameLayout>
                <View
                    android:layout_width="2dp"
                    android:layout_height="match_parent"/>
            </LinearLayout>
            <com.jd.oa.experience.view.SeniorityTextLayout
                android:id="@+id/layout_seniority"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="8dp"/>
            <LinearLayout
                android:id="@+id/homepage_top_entry_layout"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:orientation="horizontal"
                android:paddingHorizontal="8dp"
                android:background="@drawable/jdme_bg_homepage_entry_new"
                android:visibility="gone">
                <ImageView
                    android:id="@+id/arrow_homepage_top_entry"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/exp_home_smile"
                    android:layout_marginVertical="3dp"
                    android:text="@string/icon_direction_right"
                    android:textColor="#9D9D9D"
                    android:textSize="@dimen/JMEIcon_10"/>
                <TextView
                    android:id="@+id/tv_homepage_top_entry"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="3dp"
                    android:textColor="#6A6A6A"
                    android:textSize="10sp"
                    android:text="@string/exp_homepage_title"
                    android:includeFontPadding="false"/>
            </LinearLayout>

        </LinearLayout>
        <!--中间部分第三行,原第二行，不一定显示-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/second_line"
            android:layout_width="match_parent"
            android:layout_height="22dp"
            android:orientation="horizontal"
            android:layout_marginBottom="2dp"
            android:gravity="center_vertical">
            <com.jd.oa.experience.view.BadgeRecyclerView
                android:id="@+id/rv_worth_badge"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/separator"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintHorizontal_bias="0"
                android:orientation="horizontal"
                android:scrollbars="horizontal"
                android:scrollbarThumbHorizontal="@drawable/jdme_bg_scroll_bar_badge"
                android:scrollbarStyle="insideOverlay"/>
            <ImageView
                android:id="@+id/separator"
                android:layout_width="17dp"
                android:layout_height="match_parent"
                android:src="@drawable/jdme_bg_exp_profile_separator"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/rv_worth_badge"
                app:layout_constraintEnd_toStartOf="@id/layout_energy"/>
            <FrameLayout
                android:id="@+id/layout_energy"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingTop="3dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/separator"
                app:layout_constraintEnd_toEndOf="parent">
                <LinearLayout
                    android:id="@+id/layout_energy_background"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@drawable/jdme_bg_experience_energy_light"
                    android:gravity="center_vertical"
                    android:layout_gravity="start|top">
                    <View
                        android:layout_width="18dp"
                        android:layout_height="match_parent"/>
                    <TextView
                        android:id="@+id/tv_energy_level"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="0.5dp"
                        android:maxLines="1"
                        android:textColor="#F96827"
                        android:textSize="12dp"
                        android:textAlignment="center"
                        android:includeFontPadding="false"
                        android:fontFamily="@font/gothamblack_italic"/>
                    <TextView
                        android:id="@+id/tv_energy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="2dp"
                        android:maxLines="1"
                        android:textColor="#333333"
                        android:textSize="11dp"
                        android:textAlignment="center"
                        android:includeFontPadding="false"/>
                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/btn_energy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_direction_right"
                        android:textColor="#8F959E"
                        android:textSize="@dimen/JMEIcon_08"/>
                    <View
                        android:layout_width="5dp"
                        android:layout_height="match_parent"/>
                </LinearLayout>
                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="start|top"
                    android:src="@drawable/profile_section_energy"/>
            </FrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="18dp"
            android:layout_gravity="start|center_vertical">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_edit_signature"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:id="@+id/tv_edit_signature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:textColor="#666666"
                    android:textSize="12sp"
                    android:text="@string/exp_signature_hint"
                    android:includeFontPadding="false"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/btn_edit_signature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_general_edit"
                    android:textColor="#8F959E"
                    android:textSize="@dimen/JMEIcon_10"
                    android:layout_marginStart="4dp"
                    app:layout_constraintStart_toEndOf="@id/tv_edit_signature"
                    app:layout_constraintTop_toTopOf="@id/tv_edit_signature"
                    app:layout_constraintBottom_toBottomOf="@id/tv_edit_signature" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_signature"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:id="@+id/tv_signature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btn_signature"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintHorizontal_bias="0"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="#666666"
                    android:textSize="12dp"
                    android:textAlignment="center"
                    android:includeFontPadding="false"/>
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/btn_signature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_signature"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:text="@string/icon_direction_right"
                    android:textColor="#8F959E"
                    android:textSize="@dimen/JMEIcon_10"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>
    </LinearLayout>

<!--    <LinearLayout
        android:id="@+id/tags_area_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/exp_profile_tags_height"
        android:orientation="horizontal"
        android:paddingBottom="6dp">
        <View
            android:id="@+id/no_label_padding_view"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:visibility="gone"/>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_labels"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:scrollbars="none"/>
    </LinearLayout>-->

    <LinearLayout
        android:id="@+id/social_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/exp_profile_social_height"
        android:layout_marginStart="62dp"
        android:layout_marginTop="2dp"
        android:orientation="horizontal">
        <LinearLayout
            android:id="@+id/liked_layout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="26dp">
            <TextView
                android:id="@+id/tv_liked_count_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textColor="#666666"
                android:textSize="13dp"
                android:text="@string/exp_homepage_liked"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/tv_liked_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="50dp"
                android:layout_marginStart="4dp"
                android:layout_gravity="center_vertical"
                android:textStyle="bold"
                android:textColor="#000000"
                android:textSize="13dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:includeFontPadding="false"/>
            <View
                android:id="@+id/liked_red_dot"
                android:layout_width="4dp"
                android:layout_height="4dp"
                android:layout_marginTop="7dp"
                android:background="@drawable/jdme_bg_experience_skin_hint_dot"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/like_layout"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_like_count_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textColor="#666666"
                android:textSize="13dp"
                android:text="@string/exp_homepage_thumbs_up"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/tv_like_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="50dp"
                android:layout_marginStart="4dp"
                android:layout_gravity="center_vertical"
                android:textStyle="bold"
                android:textColor="#000000"
                android:textSize="13dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:includeFontPadding="false"/>
        </LinearLayout>
        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"/>
        <LinearLayout
            android:id="@+id/homepage_entry_layout"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:background="@drawable/jdme_bg_homepage_entry_light">
            <TextView
                android:id="@+id/tv_homepage_entry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:textColor="#666666"
                android:textSize="12dp"
                android:text="@string/exp_homepage_title"
                android:includeFontPadding="false"/>
            <com.jd.oa.ui.IconFontView
                android:id="@+id/arrow_homepage_entry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="10dp"
                android:text="@string/icon_direction_right"
                android:textColor="#D8D8D8"
                android:textSize="@dimen/JMEIcon_10"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>