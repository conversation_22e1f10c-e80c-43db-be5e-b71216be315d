<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginHorizontal="4dp"
    android:layout_marginTop="4dp"
    android:background="@drawable/jdme_bg_shadow_corner"
    android:orientation="vertical"
    tools:ignore="SpUsage"
    tools:layout_height="wrap_content">


    <LinearLayout
        android:id="@+id/ll_meeting_title"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="12dp">


        <ImageView
            android:id="@+id/iv_icon_meeting_title"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/jdme_exp_worktime_icon_meeting" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="41dp"
                android:layout_height="14dp"
                android:layout_marginStart="6dp"
                android:layout_marginTop="20dp"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_bg_section_title" />

            <TextView
                android:id="@+id/tv_meeting_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:autoSizeMinTextSize="14dp"
                android:autoSizeTextType="uniform"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxWidth="168dp"
                android:maxLines="1"
                android:text="@string/exp_work_time_meeting_title"
                android:textColor="#333"
                android:textSize="16dp"
                android:textStyle="bold"
                tools:text="Meeting Effectiveness" />
        </RelativeLayout>


        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_tip_meeting_title"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="3dp"
            android:gravity="center"
            android:text="@string/icon_general_information"
            android:textColor="#999"
            android:textSize="@dimen/JMEIcon_16"
            android:visibility="gone"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/tv_meeting_more"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_weight="1"
            android:autoSizeMaxTextSize="14dp"
            android:autoSizeMinTextSize="12dp"
            android:autoSizeTextType="uniform"
            android:ellipsize="end"
            android:gravity="center_vertical|end"
            android:maxWidth="90dp"
            android:maxEms="6"
            android:maxLines="1"
            android:text="@string/exp_work_time_more"
            android:textColor="#666666"
            android:textSize="14dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_meeting_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/icon_direction_right"
            android:textColor="#FF62656D"
            android:textSize="@dimen/JMEIcon_14"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>


    <TextView
        android:id="@+id/tv_meeting_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="4dp"
        android:textColor="#666666"
        android:textSize="14dp"
        android:visibility="gone"
        tools:text="2022/09/01" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_meeting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp">

        <RelativeLayout
            android:id="@+id/rl_meeting_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/jdme_bg_exp_worktime_bg"
            android:paddingStart="12dp"
            android:paddingBottom="12dp"
            app:layout_constraintEnd_toStartOf="@id/rl_meeting_efficiency"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_default="spread">

            <ImageView
                android:id="@+id/iv_meeting_count_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="5dp"
                android:src="@drawable/jdme_exp_icon_meeting_count" />

            <TextView
                android:id="@+id/tv_title_meeting_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:layout_toEndOf="@id/iv_meeting_count_icon"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:shadowRadius="3.0"
                android:text="@string/exp_meeting_count_title"
                android:textColor="#FF666666"
                android:textSize="12dp" />


            <TextView
                android:id="@+id/tv_num_meeting_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_title_meeting_count"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/din_condensed_bold"
                android:gravity="bottom"
                android:text="-"
                android:textColor="#333"
                android:textSize="18dp"
                android:textStyle="bold"
                tools:text="111999.0" />

            <TextView
                android:id="@+id/tv_unit_meeting_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/tv_num_meeting_count"
                android:layout_marginStart="4dp"
                android:layout_marginBottom="1.5dp"
                android:layout_toEndOf="@id/tv_num_meeting_count"
                android:ellipsize="end"
                android:maxLines="1"
                android:shadowRadius="3.0"
                android:text="@string/exp_meeting_count_unit"
                android:textColor="#999999"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/tv_status_meeting_count"
                android:layout_width="wrap_content"
                android:layout_height="17dp"
                android:layout_alignParentEnd="true"
                android:gravity="center"
                android:maxLines="1"
                android:minWidth="36dp"
                android:paddingHorizontal="4dp"
                android:textSize="10dp"
                tools:background="@drawable/jdme_bg_exp_meeting_bg_not_good"
                tools:text="Excessive" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_meeting_participant"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/jdme_bg_exp_worktime_bg"
            android:paddingStart="12dp"
            android:paddingBottom="12dp"
            app:layout_constraintEnd_toEndOf="@id/rl_meeting_count"
            app:layout_constraintHeight_default="spread"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="@id/rl_meeting_count"
            app:layout_constraintTop_toBottomOf="@id/rl_meeting_count"
            app:layout_constraintWidth_default="spread">

            <ImageView
                android:id="@+id/iv_meeting_participant_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="5dp"
                android:src="@drawable/jdme_exp_icon_meeting_participant" />

            <TextView
                android:id="@+id/tv_title_meeting_participant"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:layout_toEndOf="@id/iv_meeting_participant_icon"
                android:ellipsize="marquee"
                android:freezesText="true"
                android:gravity="center_vertical"
                android:marqueeRepeatLimit="marquee_forever"
                android:scrollHorizontally="true"
                android:shadowRadius="3.0"
                android:singleLine="true"
                android:text="@string/exp_meeting_participant_title"
                android:textColor="#FF666666"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/tv_num_meeting_participant"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_title_meeting_participant"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/din_condensed_bold"
                android:gravity="bottom"
                android:text="-"
                android:textColor="#333"
                android:textSize="18dp"
                android:textStyle="bold"
                tools:text="111444.0" />

            <TextView
                android:id="@+id/tv_num_unit_meeting_participant"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/tv_num_meeting_participant"
                android:layout_marginStart="4dp"
                android:layout_marginBottom="1.5dp"
                android:layout_toEndOf="@id/tv_num_meeting_participant"
                android:ellipsize="end"
                android:maxLines="1"
                android:shadowRadius="3.0"
                android:text="@string/exp_meeting_participant_unit"
                android:textColor="#999999"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/tv_status_meeting_participant"
                android:layout_width="wrap_content"
                android:layout_height="17dp"
                android:layout_alignParentEnd="true"
                android:gravity="center"
                android:maxLines="1"
                android:minWidth="44dp"
                android:paddingHorizontal="4dp"
                android:textSize="10dp"
                tools:background="@drawable/jdme_bg_exp_meeting_bg_not_good"
                tools:text="good" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_meeting_duration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/jdme_bg_exp_worktime_bg"
            android:paddingStart="12dp"
            android:paddingBottom="12dp"
            app:layout_constraintEnd_toEndOf="@id/rl_meeting_count"
            app:layout_constraintStart_toStartOf="@id/rl_meeting_count"
            app:layout_constraintTop_toBottomOf="@id/rl_meeting_participant"
            app:layout_constraintWidth_default="spread">

            <ImageView
                android:id="@+id/iv_meeting_duration_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="5dp"
                android:src="@drawable/jdme_exp_icon_meeting_duration" />

            <TextView
                android:id="@+id/tv_title_meeting_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:layout_toEndOf="@id/iv_meeting_duration_icon"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:shadowRadius="3.0"
                android:text="@string/exp_meeting_duration_title"
                android:textColor="#FF666666"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/tv_num_meeting_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_title_meeting_duration"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/din_condensed_bold"
                android:gravity="bottom"
                android:text="-"
                android:textColor="#333"
                android:textSize="18dp"
                android:textStyle="bold"
                tools:text="111999.0" />

            <TextView
                android:id="@+id/tv_num_unit_meeting_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/tv_num_meeting_duration"
                android:layout_marginStart="4dp"
                android:layout_marginBottom="1.5dp"
                android:layout_toEndOf="@id/tv_num_meeting_duration"
                android:ellipsize="end"
                android:maxLines="1"
                android:shadowRadius="3.0"
                android:text="@string/exp_meeting_duration_unit"
                android:textColor="#999999"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/tv_status_meeting_duration"
                android:layout_width="wrap_content"
                android:layout_height="17dp"
                android:layout_alignParentEnd="true"
                android:gravity="center"
                android:maxLines="1"
                android:minWidth="36dp"
                android:paddingHorizontal="4dp"
                android:textSize="10dp"
                tools:background="@drawable/jdme_bg_exp_meeting_bg_not_good"
                tools:text="Promotable" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/rl_meeting_efficiency"
            android:layout_width="104dp"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/jdme_bg_exp_worktime_bg"
            app:layout_constraintBottom_toBottomOf="@id/rl_meeting_duration"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_default="spread">

            <TextView
                android:id="@+id/tv_meeting_efficiency_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:autoSizeMaxTextSize="17dp"
                android:autoSizeMinTextSize="12dp"
                android:autoSizeTextType="uniform"
                android:gravity="center"
                android:maxLines="1"
                android:text="@string/exp_meeting_efficiency_title_default"
                android:textColor="#000"
                android:textSize="17dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_meeting_efficiency_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_meeting_efficiency_title"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="18dp"
                android:autoSizeMaxTextSize="12dp"
                android:autoSizeMinTextSize="8dp"
                android:autoSizeTextType="uniform"
                android:gravity="center"
                android:lineSpacingExtra="6dp"
                android:maxLines="3"
                android:shadowRadius="3.0"
                android:text="@string/exp_meeting_efficiency_content"
                android:textColor="#FF333333"
                android:textSize="12dp"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="66dp"
                android:layout_height="71dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:src="@drawable/jdme_exp_worktime_rocket" />
        </RelativeLayout>

        <View
            android:id="@+id/v_meeting_suggest_top"
            android:layout_width="28dp"
            android:layout_height="7dp"
            android:layout_marginStart="40dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/jdme_bg_worktime_suggest_top"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rl_meeting_duration"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/ll_meeting_suggest"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:background="@drawable/jdme_bg_exp_worktime_bg"
            android:orientation="vertical"
            android:padding="14dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_meeting_suggest_top"
            app:layout_constraintWidth_default="spread"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:background="@drawable/jdme_bg_exp_meeting_bg_suggest"
                    android:gravity="center"
                    android:maxLines="1"
                    android:minWidth="32dp"
                    android:paddingHorizontal="5dp"
                    android:text="@string/exp_meeting_suggest"
                    android:textColor="#FFFE3E33"
                    android:textSize="10dp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="4dp"
                    android:shadowRadius="3.0"
                    android:text="@string/exp_meeting_suggest_title"
                    android:textColor="#FF666666"
                    android:textSize="12dp" />


            </LinearLayout>

            <TextView
                android:id="@+id/tv_meeting_suggest"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:lineSpacingExtra="9dp"
                android:textColor="#333"
                android:textSize="12dp"
                android:textStyle="bold"
                tools:text="·控制会议时长实打实大所大所大所大所大所大所多撒大萨达实打所大萨达所大萨达实打所\n·控制会议时长\n·控制会议时长" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_margin="16dp"
        android:background="@drawable/jdme_bg_exp_worktime_bg"
        android:visibility="gone">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="12dp"
            android:src="@drawable/exp_bg_meeting_emty" />

        <TextView
            android:id="@+id/tv_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:gravity="center_horizontal"
            android:ellipsize="end"
            android:maxLines="2"
            android:shadowRadius="3.0"
            android:textColor="#FF999999"
            android:textSize="12sp" />
    </LinearLayout>

</LinearLayout>