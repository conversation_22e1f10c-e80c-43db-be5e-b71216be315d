<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".activity.ChangeMyBgActivity">

    <com.yu.bundles.album.photoview.PhotoView
        android:id="@+id/iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:scaleType="fitCenter"
        android:transitionName="bgImage" />

    <TextView
        android:id="@+id/tv_change"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="29dp"
        android:background="@drawable/jdme_bg_half_circle_gray"
        android:paddingHorizontal="29dp"
        android:paddingVertical="12dp"
        android:text="@string/exp_homepage_change_bg"
        android:textColor="@color/white"
        android:visibility="gone" />

</RelativeLayout>