<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginTop="5dp"
    android:paddingTop="3dp"
    android:paddingBottom="5dp"
    android:orientation="vertical"
    android:background="@drawable/jdme_bg_shadow_corner">

    <View
        android:layout_width="151dp"
        android:layout_height="144dp"
        android:layout_gravity="end|top"
        android:background="@drawable/jdme_bg_welfare_icon"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <FrameLayout
            android:id="@+id/my_welfare_title_layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="28dp"
            android:layout_marginEnd="86dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="18dp">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="35dp"
                android:layout_gravity="start|top"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/my_welfare_title"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="3dp"
                    android:maxWidth="170dp"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textStyle="bold"
                    android:textSize="16dp"
                    android:textColor="#333333"
                    android:text="@string/exp_welfare_my_welfare"
                    android:includeFontPadding="false"/>
                <TextView
                    android:id="@+id/my_welfare_count_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:maxWidth="60dp"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textStyle="bold"
                    android:textSize="30dp"
                    android:textColor="#333333"
                    android:includeFontPadding="false"
                    android:visibility="gone"/>
                <TextView
                    android:id="@+id/my_welfare_count_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="3dp"
                    android:gravity="bottom"
                    android:textStyle="bold"
                    android:textSize="16dp"
                    android:textColor="#333333"
                    android:text="@string/exp_welfare_my_welfare_unit"
                    android:includeFontPadding="false"
                    android:visibility="gone"/>
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/my_welfare_go_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="7dp"
                    android:layout_marginBottom="8dp"
                    android:gravity="bottom"
                    android:textSize="@dimen/JMEIcon_12"
                    android:textColor="#8F959E"
                    android:text="@string/icon_direction_right"/>
            </LinearLayout>
            <TextView
                android:id="@+id/my_welfare_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="21dp"
                android:layout_gravity="bottom"
                android:gravity="start|bottom"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="12dp"
                android:textColor="#999999"
                android:includeFontPadding="false"/>
        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="129dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:orientation="horizontal">
            <!--我的钱包-->
            <FrameLayout
                android:id="@+id/my_wallet_layout"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/jdme_bg_welfare_shadow">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|top"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="14dp"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textSize="13dp"
                    android:textColor="#333333"
                    android:text="@string/exp_welfare_my_wallet"
                    android:includeFontPadding="false"/>
                <TextView
                    android:id="@+id/my_wallet_value_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxWidth="130dp"
                    android:layout_gravity="start|top"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="35dp"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textStyle="bold"
                    android:textSize="15dp"
                    android:textColor="#333333"
                    android:text="0"
                    android:includeFontPadding="false"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|bottom"
                    android:layout_marginStart="14dp"
                    android:layout_marginBottom="37dp"
                    android:gravity="bottom"
                    android:textSize="13dp"
                    android:textColor="#333333"
                    android:text="@string/exp_welfare_jd_wallet"
                    android:includeFontPadding="false"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|bottom"
                    android:layout_marginStart="14dp"
                    android:layout_marginBottom="15dp"
                    android:gravity="bottom"
                    android:textSize="13dp"
                    android:textColor="#333333"
                    android:text="@string/exp_welfare_my_card"
                    android:includeFontPadding="false"/>
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/my_wallet_eye_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="28dp"
                    android:paddingEnd="14dp"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp"
                    android:layout_gravity="end|top"
                    android:gravity="center"
                    android:textSize="@dimen/JMEIcon_14"
                    android:textColor="#8F959E"
                    android:text="@string/icon_general_eyeinvisible"/>
                <TextView
                    android:id="@+id/my_wallet_jd_value_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxWidth="60dp"
                    android:layout_gravity="end|bottom"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="36dp"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textStyle="bold"
                    android:textSize="15dp"
                    android:textColor="#333333"
                    android:text="0.00"
                    android:includeFontPadding="false"/>
                <TextView
                    android:id="@+id/my_wallet_card_value_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxWidth="80dp"
                    android:layout_gravity="end|bottom"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="14dp"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textStyle="bold"
                    android:textSize="15dp"
                    android:textColor="#333333"
                    android:text="0.00"
                    android:includeFontPadding="false"/>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="14dp"
                    android:layout_marginEnd="13dp"
                    android:layout_marginTop="59dp"
                    android:background="@drawable/jdme_exp_instruction_dashline"/>
            </FrameLayout>
            <View
                android:layout_width="2dp"
                android:layout_height="match_parent"/>
            <!--福利券-->
            <FrameLayout
                android:id="@+id/my_coupon_layout"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/jdme_bg_welfare_shadow">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|top"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="14dp"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textSize="13dp"
                    android:textColor="#333333"
                    android:text="@string/exp_welfare_my_coupon"
                    android:includeFontPadding="false"/>
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/my_coupon_go_btn"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:layout_gravity="end|top"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="14dp"
                    android:gravity="center"
                    android:textSize="@dimen/JMEIcon_12"
                    android:textColor="#8F959E"
                    android:text="@string/icon_direction_right"/>
                <LinearLayout
                    android:id="@+id/no_coupon_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="20dp"
                    android:orientation="vertical">
                    <View
                        android:layout_width="66dp"
                        android:layout_height="37dp"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/jdme_bg_welfare_no_coupon"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="9dp"
                        android:layout_gravity="center_horizontal"
                        android:textSize="11dp"
                        android:textColor="#666666"
                        android:text="@string/exp_welfare_no_coupon"
                        android:includeFontPadding="false"/>
                </LinearLayout>
                <com.jd.oa.experience.view.NestedHorizontalRecyclerView
                    android:id="@+id/rv_coupons"
                    android:layout_width="match_parent"
                    android:layout_height="73dp"
                    android:layout_marginStart="9.5dp"
                    android:layout_marginEnd="0dp"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="8dp"
                    android:orientation="horizontal"
                    android:scrollbars="none"/>
            </FrameLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="69dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">
            <!--我的假期-->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/jdme_bg_welfare_shadow">
                <LinearLayout
                    android:id="@+id/my_holiday_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginBottom="8dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:textStyle="bold"
                            android:textSize="13dp"
                            android:textColor="#333333"
                            android:text="@string/exp_welfare_my_holiday"
                            android:includeFontPadding="false"/>
                        <View
                            android:layout_width="0dp"
                            android:layout_height="13dp"
                            android:layout_weight="1"/>
                        <LinearLayout
                            android:id="@+id/my_holiday_apply_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">
                            <TextView
                                android:id="@+id/my_holiday_go_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:layout_gravity="center_vertical"
                                android:gravity="center"
                                android:textSize="11dp"
                                android:textColor="#999999"
                                android:text="@string/exp_welfare_go_use"
                                android:includeFontPadding="false"/>
                            <com.jd.oa.ui.IconFontView
                                android:id="@+id/my_holiday_go_btn"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:layout_gravity="center_vertical"
                                android:gravity="center"
                                android:textSize="@dimen/JMEIcon_12"
                                android:textColor="#8F959E"
                                android:text="@string/icon_direction_right"/>
                        </LinearLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="bottom"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="12dp"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/my_holiday_value_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxWidth="70dp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textStyle="bold"
                            android:textSize="14dp"
                            android:textColor="#333333"
                            android:text="0"
                            android:includeFontPadding="false"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="3dp"
                            android:layout_marginBottom="0.5dp"
                            android:textSize="10dp"
                            android:textColor="#999999"
                            android:text="@string/exp_welfare_unit_hour"
                            android:includeFontPadding="false"/>
                    </LinearLayout>
                </LinearLayout>
            </FrameLayout>
            <View
                android:layout_width="2dp"
                android:layout_height="match_parent"/>
            <!--福利积分-->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/jdme_bg_welfare_shadow">
                <LinearLayout
                    android:id="@+id/my_balance_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginBottom="8dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:textStyle="bold"
                            android:textSize="13dp"
                            android:textColor="#333333"
                            android:text="@string/exp_welfare_my_balance"
                            android:includeFontPadding="false"/>
                        <View
                            android:layout_width="0dp"
                            android:layout_height="13dp"
                            android:layout_weight="1"/>
                        <TextView
                            android:id="@+id/my_balance_go_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:textSize="11dp"
                            android:textColor="#999999"
                            android:text="@string/exp_welfare_go_use"
                            android:includeFontPadding="false"/>
                        <com.jd.oa.ui.IconFontView
                            android:id="@+id/my_balance_go_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:gravity="center"
                            android:textSize="@dimen/JMEIcon_12"
                            android:textColor="#8F959E"
                            android:text="@string/icon_direction_right"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="12dp"
                        android:gravity="bottom"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/my_balance_value_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxWidth="70dp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textStyle="bold"
                            android:textSize="14dp"
                            android:textColor="#333333"
                            android:text="0"
                            android:includeFontPadding="false"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4.5dp"
                            android:layout_marginBottom="0.5dp"
                            android:textSize="10dp"
                            android:textColor="#999999"
                            android:text="@string/exp_welfare_unit_score"
                            android:includeFontPadding="false"/>
                    </LinearLayout>
                </LinearLayout>
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>