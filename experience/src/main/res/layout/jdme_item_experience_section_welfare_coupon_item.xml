<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="73dp"
    android:layout_height="match_parent">
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:src="@drawable/jdme_bg_welfare_envelope"/>
    <TextView
        android:id="@+id/couponType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="47dp"
        android:layout_gravity="center_horizontal|top"
        android:layout_marginTop="13dp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="11dp"
        android:textColor="#8D690A"
        tools:text="东券"
        android:includeFontPadding="false"/>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="58dp"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="14.5dp"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/couponValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="12dp"
            android:textColor="#8D690A"
            android:includeFontPadding="false"/>
        <TextView
            android:id="@+id/couponUnit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="25dp"
            android:layout_marginStart="2dp"
            android:layout_gravity="center_vertical"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center"
            android:textSize="10dp"
            android:textColor="#8D690A"
            android:includeFontPadding="false"/>
    </LinearLayout>
</FrameLayout>