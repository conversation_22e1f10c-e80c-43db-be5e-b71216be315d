<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="#ffffff">

    <TextView
        android:id="@+id/instruction_title_tv"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="start|center_vertical"
        android:layout_marginStart="16dp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:textSize="16dp"
        android:text="@string/exp_send_my_instruction"
        android:includeFontPadding="false"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="12dp"
        android:orientation="vertical"
        android:background="@drawable/jdme_bg_homepage_instruction_work_gradient">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="41dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:orientation="horizontal">
            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="bottom"
                android:src="@drawable/jdme_bg_homepage_icon_work"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginStart="10dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textStyle="bold"
                android:textColor="#333333"
                android:textSize="15dp"
                android:text="@string/exp_homepage_instruction_work"
                android:includeFontPadding="false"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:gravity="start"
                android:layout_marginStart="4dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#999999"
                android:textSize="12dp"
                android:text="@string/exp_homepage_instruction_work_desc"
                android:includeFontPadding="false"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:orientation="horizontal"><!--16 + 15 + 15--><!--完整度进度条-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_gravity="center_vertical"
                android:textColor="#999999"
                android:textSize="12dp"
                android:text="@string/exp_homepage_instruction_progress"
                android:includeFontPadding="false"/>
            <com.jd.oa.experience.view.HomePageProgressBar
                android:id="@+id/complete_status_pb"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="11dp"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="4dp"
            android:orientation="horizontal">
            <FrameLayout
                android:id="@+id/duty_card"
                android:layout_width="0dp"
                android:layout_height="197dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_bg_homepage_instruction_work"><!--177 + 8 + 12-->
                <TextView
                    android:id="@+id/duty_editable_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="22dp"
                    android:background="@drawable/jdme_bg_homepage_instruction_work_edit"
                    android:paddingStart="7dp"
                    android:paddingEnd="5dp"
                    android:layout_gravity="end|top"
                    android:gravity="center_vertical"
                    android:textColor="#FF6B23"
                    android:textSize="12dp"
                    android:text="@string/exp_homepage_instruction_edit"
                    android:includeFontPadding="false"/>
                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="15dp"
                    android:layout_gravity="start|top"
                    android:src="@drawable/jdme_bg_homepage_icon_work_duty"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="16dp"
                    android:layout_gravity="start|top"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:textSize="12dp"
                    android:text="@string/exp_homepage_instruction_work_duty"
                    android:includeFontPadding="false"/>
                <com.zhy.view.flowlayout.TagFlowLayout
                    android:id="@+id/duty_business_tag"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginTop="43dp"
                    android:layout_marginBottom="10dp"
                    android:layout_gravity="top"
                    app:tag_gravity="left"/>

                <LinearLayout
                    android:id="@+id/duty_empty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="16dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginTop="45dp"
                    android:layout_gravity="top">
                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="38dp"
                        android:layout_marginTop="9dp"
                        android:layout_marginBottom="7dp"
                        android:layout_gravity="center_horizontal"
                        android:src="@drawable/jdme_bg_homepage_duty_empty"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textColor="#CDCDCD"
                        android:textSize="11dp"
                        android:text="@string/exp_homepage_instruction_duty_empty"
                        android:includeFontPadding="false"/>
                    <TextView
                        android:id="@+id/duty_add"
                        android:layout_width="56dp"
                        android:layout_height="20dp"
                        android:background="@drawable/jdme_bg_homepage_duty_add_btn"
                        android:layout_marginTop="8dp"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:textColor="#333333"
                        android:textSize="12dp"
                        android:text="@string/exp_homepage_instruction_duty_add"
                        android:includeFontPadding="false"/>
                </LinearLayout>
            </FrameLayout>
            <FrameLayout
                android:id="@+id/okr_card"
                android:layout_width="0dp"
                android:layout_height="197dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_bg_homepage_instruction_work">
                <TextView
                    android:id="@+id/okr_editable_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="22dp"
                    android:background="@drawable/jdme_bg_homepage_instruction_work_edit"
                    android:paddingStart="7dp"
                    android:paddingEnd="5dp"
                    android:layout_gravity="end|top"
                    android:gravity="center_vertical"
                    android:textColor="#FF6B23"
                    android:textSize="12dp"
                    android:text="@string/exp_homepage_instruction_edit"
                    android:includeFontPadding="false"/>
                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="15dp"
                    android:layout_gravity="start|top"
                    android:src="@drawable/jdme_bg_homepage_icon_work_okr"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="16dp"
                    android:layout_gravity="start|top"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:textSize="12dp"
                    android:text="@string/exp_homepage_instruction_work_okr"
                    android:includeFontPadding="false"/>

                <LinearLayout
                    android:id="@+id/okr_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginTop="44dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textColor="#333333"
                        android:textSize="11dp"
                        android:text="@string/exp_homepage_okr_prefix"
                        android:includeFontPadding="false"/>
                    <TextView
                        android:id="@+id/okr_num"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="2dp"
                        android:layout_gravity="bottom"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textColor="#FE3E33"
                        android:textSize="18dp"
                        android:includeFontPadding="false"/>
                    <TextView
                        android:id="@+id/okr_suffix"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textColor="#333333"
                        android:textSize="11dp"
                        android:includeFontPadding="false"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/okr_btn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginTop="76dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textColor="#FF6B23"
                        android:textSize="11dp"
                        android:text="@string/exp_homepage_okr_go"
                        android:includeFontPadding="false"/>
                    <com.jd.oa.ui.IconFontView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_gravity="center_vertical"
                        android:textColor="#FF6B23"
                        android:textSize="@dimen/JMEIcon_10"
                        android:text="@string/icon_direction_right"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/okr_empty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginTop="45dp"
                    android:layout_gravity="top">
                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="38dp"
                        android:layout_marginTop="9dp"
                        android:layout_marginBottom="7dp"
                        android:layout_gravity="center_horizontal"
                        android:src="@drawable/jdme_bg_homepage_duty_empty"/>
                    <TextView
                        android:id="@+id/okr_empty_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textColor="#CDCDCD"
                        android:textSize="11dp"
                        android:includeFontPadding="false"/>
                    <TextView
                        android:id="@+id/okr_add"
                        android:layout_width="56dp"
                        android:layout_height="20dp"
                        android:background="@drawable/jdme_bg_homepage_duty_add_btn"
                        android:layout_marginTop="8dp"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:textColor="#333333"
                        android:textSize="12dp"
                        android:text="@string/exp_homepage_instruction_duty_add"
                        android:includeFontPadding="false"/>
                </LinearLayout>
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:orientation="horizontal"
        android:background="@drawable/jdme_bg_homepage_instruction_personality_gradient">
        <ImageView
            android:layout_width="30dp"
            android:layout_height="59dp"
            android:layout_gravity="bottom"
            android:layout_marginStart="12dp"
            android:paddingBottom="29dp"
            android:src="@drawable/jdme_bg_homepage_icon_personality"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginStart="10dp"
            android:paddingBottom="29dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="15dp"
            android:text="@string/exp_homepage_instruction_personality"
            android:includeFontPadding="false"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:gravity="start"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="8dp"
            android:paddingBottom="29dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#999999"
            android:textSize="12dp"
            android:text="@string/exp_homepage_instruction_personality_desc"
            android:includeFontPadding="false"/><!-- 70-11-30=29 -->
    </LinearLayout>

    <FrameLayout
        android:id="@+id/instruction_preview_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/homepage_webview_height"
        android:background="#FFFFFF"
        android:visibility="gone">
        <com.jd.me.web2.webview.JMEWebview
            android:id="@+id/instruction_preview_webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="67dp"
            android:layout_gravity="bottom"
            android:orientation="horizontal"
            android:background="@drawable/jdme_bg_homepage_instruction_gradient">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/instruction_preview_btn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingTop="17dp"
                android:paddingBottom="17dp"
                android:layout_gravity="bottom">
                <TextView
                    android:id="@+id/preview_btn_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/preview_btn_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    android:textColor="#FF6B23"
                    android:textSize="16dp"
                    android:text="@string/exp_homepage_instruction_preview"
                    android:includeFontPadding="false"/>
                <com.jd.oa.ui.IconFontView
                    android:id="@+id/preview_btn_icon"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:layout_constraintStart_toEndOf="@id/preview_btn_text"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    android:gravity="end|center_vertical"
                    android:textColor="#FF6B23"
                    android:textSize="@dimen/JMEIcon_12"
                    android:text="@string/icon_direction_doubleup"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <View
                android:id="@+id/instruction_separator"
                android:layout_width="1dp"
                android:layout_height="18dp"
                android:layout_marginBottom="16dp"
                android:layout_gravity="bottom"
                android:background="#E6E6E6"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/instruction_edit_btn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingTop="17dp"
                android:paddingBottom="17dp"
                android:layout_gravity="bottom">
                <TextView
                    android:id="@+id/edit_btn_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/edit_btn_privacy"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    android:textColor="#333333"
                    android:textSize="16dp"
                    android:text="@string/exp_homepage_instruction_edit"
                    android:includeFontPadding="false"/>
                <TextView
                    android:id="@+id/edit_btn_privacy"
                    android:layout_width="47dp"
                    android:layout_height="16dp"
                    app:layout_constraintStart_toEndOf="@id/edit_btn_text"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    android:layout_marginStart="6dp"
                    android:gravity="center"
                    android:textColor="#FF6B23"
                    android:textSize="11dp"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/instruction_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:background="#FFFFFF"
        android:visibility="visible">

        <TextView
            android:id="@+id/instruction_empty_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="21dp"
            android:layout_gravity="center_horizontal"
            android:textColor="#8F959E"
            android:textSize="14dp"
            android:includeFontPadding="false"/>

        <View
            android:id="@+id/instruction_empty_iv"
            android:layout_width="343dp"
            android:layout_height="149dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="21dp"
            android:background="@drawable/jdme_bg_instruction_empty_cn"/>

        <TextView
            android:id="@+id/instruction_create_tv"
            android:layout_width="148dp"
            android:layout_height="36dp"
            android:background="@drawable/jdme_bg_homepage_instruction_create_new"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="26dp"
            android:gravity="center"
            android:textColor="#FF6B23"
            android:textSize="14dp"
            android:text="@string/exp_homepage_instruction_create_now"/>
    </LinearLayout>

</LinearLayout>