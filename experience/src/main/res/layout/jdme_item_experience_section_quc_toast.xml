<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingBottom="4dip">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/jdme_drawable_clear_cache_bg"
        android:minWidth="200dip"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="25dip"
            android:layout_height="25dip"
            android:layout_gravity="center"
            android:layout_marginBottom="8dip"
            android:layout_marginTop="4dip"
            android:background="@drawable/jdme_icon_info"
            android:contentDescription="@null"
            android:gravity="center" />

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#ffffff"
            android:textSize="14sp" />
    </LinearLayout>

</RelativeLayout>