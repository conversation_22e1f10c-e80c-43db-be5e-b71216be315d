<?xml version="1.0" encoding="utf-8"?><!-- 个人信息界面 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="#FFFFFF">
        <TextView
            android:id="@+id/tv_self_info_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/exp_self_info_title"
            android:textColor="#2E2D2D"
            android:textSize="18dp"
            android:textStyle="bold" />
        <RelativeLayout
            android:id="@+id/back"
            android:layout_width="52dp"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true">
            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/icon_direction_left"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_22"/>
        </RelativeLayout>
    </RelativeLayout>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_self_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="24dp"
        android:background="@color/transparent"
        android:orientation="vertical"
        android:overScrollMode="never"
        android:scrollbars="none"/>
</LinearLayout>