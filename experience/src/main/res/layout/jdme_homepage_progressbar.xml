<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="match_parent"
        android:layout_height="6dp"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:layout_gravity="center_vertical"
        android:max="5"
        android:progress="0"
        android:progressDrawable="@drawable/jdme_bg_homepage_instruction_progressbar"/>
    <TextView
        android:id="@+id/duty_progress_zero"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="7dp"
        android:layout_gravity="start|bottom"
        android:layout_marginBottom="2dp"
        android:textColor="#333333"
        android:textSize="12dp"
        android:text="0"
        android:includeFontPadding="false"/>
    <TextView
        android:id="@+id/duty_progress_max"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="7dp"
        android:layout_gravity="end|bottom"
        android:layout_marginBottom="2dp"
        android:textColor="#333333"
        android:textSize="12dp"
        android:text="5"
        android:includeFontPadding="false"/>
    <ImageView
        android:id="@+id/thumb"
        android:layout_width="@dimen/homepage_progressbar_thumb_size"
        android:layout_height="@dimen/homepage_progressbar_thumb_size"
        android:layout_gravity="start|center_vertical"
        android:src="@drawable/jdme_bg_homepage_instruction_pb_thumb"/>
</FrameLayout>