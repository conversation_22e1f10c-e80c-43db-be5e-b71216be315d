<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_mycard_qt_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/jdsaas_black_F5F5F5">

        <ImageView
            android:id="@+id/iv_mycard_bg"
            android:layout_width="match_parent"
            android:layout_height="396dp"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/jdsaas_bg_my_card"
            android:scaleType="fitXY"
            app:layout_constraintTop_toTopOf="parent" />
        <View
            android:layout_width="match_parent"
            android:layout_height="396dp"
            android:background="@drawable/jdsaas_mycard_bg_graient"
            app:layout_constraintTop_toTopOf="parent"/>

        <LinearLayout
            android:id="@+id/ll_mycard_qt"
            android:layout_width="match_parent"
            android:layout_height="396dp"
            android:orientation="vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginTop="124dp"
            android:background="@drawable/jdsaas_mycard_qt_corner_bg">
            <ImageView
                android:scaleType="fitXY"
                android:id="@+id/jdsaas_id_mycard_qt"
                android:layout_width="228dp"
                android:layout_height="228dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="89dp"
                android:src="@drawable/jdme_mine_main_qt" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="17dp"
                android:text="@string/jdme_my_card_tip"
                android:textColor="#848484"
                android:textSize="13sp" />
        </LinearLayout>

        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:src="@drawable/jdsaas_mycard_qt_corner_bg"
            android:layout_width="84dp"
            android:layout_height="84dp"
            app:layout_constraintLeft_toLeftOf="@+id/ll_mycard_qt"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="94dp"
            android:layout_marginLeft="14dp"/>

        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/jdsaas_id_mycard_avator"
            android:src="@drawable/jdsaas_personal_default_avatar"
            android:layout_width="80dp"
            android:layout_height="80dp"
            app:layout_constraintLeft_toLeftOf="@+id/ll_mycard_qt"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="96dp"
            android:layout_marginLeft="16dp"/>

        <TextView
            android:id="@+id/jdsaas_id_mycard_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/jdsaas_black_222222"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="琼恩"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@+id/ll_mycard_qt"
            app:layout_constraintLeft_toRightOf="@+id/jdsaas_id_mycard_avator"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="2dp"
            android:layout_marginLeft="10dp"
            android:paddingRight="22dp"/>

        <TextView
            android:id="@+id/jdsaas_id_mycard_position"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/jdsaas_black_1B1B1B"
            android:textSize="14sp"
            tools:text="京东商城-视觉设计师"
            android:layout_marginRight="22dp"
            app:layout_constraintStart_toStartOf="@+id/jdsaas_id_mycard_name"
            app:layout_constraintTop_toBottomOf="@+id/jdsaas_id_mycard_name"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_direction_left"
        android:textColor="@color/jdsaas_black_232930"
        android:textSize="@dimen/JMEIcon_22"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:paddingLeft="18dp"
        android:paddingRight="8dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:layout_marginTop="45dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="530dp"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp">
        <LinearLayout
            android:id="@+id/ll_mycard_scan"
            android:layout_width="wrap_content"
            android:layout_height="110dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:background="@drawable/jdsaas_mycard_btn_corner_bg"
            android:layout_marginRight="10dp">
            <com.jd.oa.ui.IconFontView
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:text="@string/icon_scan"
                android:textColor="@color/jdsaas_black_1B1B1B"
                android:textSize="@dimen/JMEIcon_26"
                android:textStyle="bold" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jdsaas_my_card_scan"
                android:textSize="14sp"
                android:textColor="@color/jdsaas_black_26292D"
                android:layout_marginTop="8dp"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_mycard_save"
            android:layout_width="wrap_content"
            android:layout_height="110dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:background="@drawable/jdsaas_mycard_btn_corner_bg"
            android:layout_marginRight="10dp">
            <com.jd.oa.ui.IconFontView
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:text="@string/icon_direction_download"
                android:textColor="@color/jdsaas_black_1B1B1B"
                android:textSize="@dimen/JMEIcon_26"
                android:textStyle="bold" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jdsaas_my_card_save"
                android:textSize="14sp"
                android:textColor="@color/jdsaas_black_26292D"
                android:layout_marginTop="8dp"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_mycard_share"
            android:layout_width="wrap_content"
            android:layout_height="110dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:background="@drawable/jdsaas_mycard_btn_corner_bg">
            <com.jd.oa.ui.IconFontView
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:text="@string/icon_share"
                android:textColor="@color/jdsaas_black_1B1B1B"
                android:textSize="@dimen/JMEIcon_26"
                android:textStyle="bold" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jdsaas_my_card_forward"
                android:textSize="14sp"
                android:textColor="@color/jdsaas_black_26292D"
                android:layout_marginTop="8dp"/>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>