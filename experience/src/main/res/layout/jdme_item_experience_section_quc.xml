<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginTop="5dp"
    android:paddingTop="14dp"
    android:paddingBottom="14dp"
    android:orientation="horizontal"
    android:background="@drawable/jdme_bg_shadow_corner">

    <LinearLayout
        android:id="@+id/qr_scan_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <ImageView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginBottom="10dp"
            android:scaleType="fitXY"
            android:src="@drawable/quc_section_scan"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="13dp"
            android:text="@string/exp_quc_scan"
            android:includeFontPadding="false"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/pass_code_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <ImageView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginBottom="10dp"
            android:scaleType="fitXY"
            android:src="@drawable/quc_section_pass"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="13dp"
            android:text="@string/exp_quc_pass"
            android:includeFontPadding="false"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/pay_code_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <ImageView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginBottom="10dp"
            android:scaleType="fitXY"
            android:src="@drawable/quc_section_pay"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="13dp"
            android:text="@string/exp_quc_pay"
            android:includeFontPadding="false"/>
    </LinearLayout>
</LinearLayout>