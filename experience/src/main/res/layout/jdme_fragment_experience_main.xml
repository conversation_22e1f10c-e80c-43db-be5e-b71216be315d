<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.pulltorefresh.PullToRefreshLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:id="@+id/experience_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="44dp">
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:stateListAnimator="@drawable/jdme_bg_appbar_elevation">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapse"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <com.jd.oa.experience.view.ProfileLayout
                    android:id="@+id/profile_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_collapseMode="none"/>

                <FrameLayout
                    android:id="@+id/profile_avatar"
                    android:layout_width="54dp"
                    android:layout_height="54dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="8dp"
                    app:layout_collapseMode="none">
                    <com.jd.oa.elliptical.SuperEllipticalImageView
                        android:id="@+id/iv_profile_avatar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                    <ImageView
                        android:id="@+id/iv_profile_pendant"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                </FrameLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>

            <!--TabLayout-->
            <com.jd.oa.tablayout.SlidingTabLayout
                android:id="@+id/tab_layout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/tab_layout_height"
                android:paddingHorizontal="6dp"
                android:background="@drawable/jdme_bg_exp_tabs_bkgnd_gradient"
                tl:tl_textBold="SELECT"
                tl:tl_textSelectColor="#232930"
                tl:tl_textUnselectColor="#62656D"
                tl:tl_textsize="16dp"
                tl:tl_tab_padding="10dp"
                tl:tl_indicator_margin_bottom="4dp"
                tl:tl_indicator_style="IMAGE"
                tl:tl_indicator_height="8dp"
                tl:tl_indicator_width="23dp"/>

        </com.google.android.material.appbar.AppBarLayout>

        <!--viewpager-->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/view_pager_exp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:scrollbars="none"/>
            <View
                android:id="@+id/bottom_shadow"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_gravity="top"
                android:background="@drawable/jdme_bg_exp_tabs_bottom_shadow"/>
        </FrameLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</com.jd.oa.pulltorefresh.PullToRefreshLayout>