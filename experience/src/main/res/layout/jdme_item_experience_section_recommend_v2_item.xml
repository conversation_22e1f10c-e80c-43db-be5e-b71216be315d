<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    android:background="@drawable/jdme_bg_shadow_corner"
    android:orientation="vertical">

    <com.jd.oa.experience.view.TitleView
        android:id="@+id/title_view"
        android:layout_width="match_parent"
        android:layout_height="48dp" />

    <com.jd.oa.experience.view.PagerSnapRecyclerView
        android:id="@+id/rec_rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingBottom="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_btn1"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_rec_v2_btn"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="10dp"
            android:textColor="#333"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/tv_btn2"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_rec_v2_btn"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="10dp"
            android:shadowRadius="3.0"
            android:textColor="#333"
            android:textSize="14dp" />
    </LinearLayout>

</LinearLayout>