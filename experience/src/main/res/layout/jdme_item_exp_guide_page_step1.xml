<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:id="@+id/ll_1"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="10dp"
        android:background="@drawable/jdme_bg_circle_whte"
        android:gravity="center"
        android:orientation="vertical">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_tabbar_joywork_de"
            android:textSize="@dimen/JMEIcon_24"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/exp_guide_workbench"
            android:textSize="11dp" />
    </LinearLayout>


    <TextView
        android:id="@+id/tv_sure"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/ll_1"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="115dp"
        android:background="@drawable/jdme_bg_corner_gray_line2"
        android:paddingHorizontal="60dp"
        android:paddingVertical="9dp"
        android:text="@string/exp_guide_i_have_known"
        android:textColor="#FFFFFF"
        android:textSize="14dp"
        android:textStyle="bold" />

    <ImageView
        android:layout_width="192dp"
        android:layout_height="210dp"
        android:layout_above="@id/ll_1"
        android:layout_alignEnd="@id/ll_1"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="-8dp"
        android:src="@drawable/exp_guide_page_line1" />


    <LinearLayout
        android:id="@+id/ll_2"
        android:layout_width="275dp"
        android:layout_height="wrap_content"
        android:layout_above="@id/tv_sure"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="70dp"
        android:background="@drawable/shape_bg_exp_corner_white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginLeft="13dp"
                android:src="@drawable/exp_guide_icon_2" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="@string/exp_guide_app1"
                android:textColor="#333333"
                android:textSize="14dp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="10dp"
            android:background="#E6E6E6" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginLeft="13dp"
                android:src="@drawable/exp_guide_icon_3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="@string/exp_guide_app2"
                android:textColor="#333333"
                android:textSize="14dp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="10dp"
            android:background="#E6E6E6" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginLeft="13dp"
                android:src="@drawable/exp_guide_icon_4" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="@string/exp_guide_app3"
                android:textColor="#333333"
                android:textSize="14dp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="10dp"
            android:background="#E6E6E6" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginLeft="13dp"
                android:src="@drawable/exp_guide_icon_5" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:text="@string/exp_guide_app4"
                android:textColor="#333333"
                android:textSize="14dp" />

        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_title1"
        android:layout_width="255dp"
        android:layout_height="65dp"
        android:layout_above="@id/ll_2"
        android:layout_alignStart="@id/ll_2"
        android:layout_marginStart="-15dp"
        android:layout_marginEnd="66dp"
        android:layout_marginBottom="33dp"
        android:src="@drawable/exp_guide_title1" />

    <TextView
        android:id="@+id/tv_skip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="18dp"
        android:background="@drawable/jdme_bg_corner_gray_line"
        android:paddingHorizontal="18dp"
        android:paddingVertical="6dp"
        android:text="@string/exp_guide_skip"
        android:textColor="#BFBFBF"
        android:textSize="12dp" />
</RelativeLayout>