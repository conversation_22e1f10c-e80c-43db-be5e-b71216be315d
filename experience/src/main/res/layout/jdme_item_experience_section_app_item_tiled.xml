<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="7dp"
    android:paddingBottom="6dp"
    android:background="@drawable/jdme_bg_shadow_corner">
    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_gravity="end|center_vertical"
        android:layout_marginEnd="6dp"
        android:scaleType="fitXY"/>
    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/iv_gif_icon"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_gravity="end|center_vertical"
        android:layout_marginEnd="6dp"
        android:scaleType="fitXY"
        android:visibility="gone"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="start|center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="64dp"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:autoSizeMaxTextSize="16dp"
            android:autoSizeMinTextSize="13dp"
            android:autoSizeTextType="uniform"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_tip"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_bias="0"
            android:ellipsize="end"
            android:gravity="top|start"
            android:lines="1"
            android:paddingEnd="2dp"
            android:shadowRadius="3.0"
            android:textColor="#FF333333"
            android:textSize="16dp"
            android:textStyle="bold"
            tools:text="大萨达多神鼎飞丹砂sdasda" />
        <ImageView
            android:id="@+id/iv_tip"
            android:layout_width="5dp"
            android:layout_height="5dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/jdme_bg_red_dot_tip"
            android:visibility="visible"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>