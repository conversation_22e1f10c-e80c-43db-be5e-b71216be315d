<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="4dp"
    android:background="@drawable/jdme_bg_shadow_corner">

    <com.jd.oa.ui.SimpleRoundImageView
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_gravity="end|top"
        android:scaleType="fitXY"
        app:round_radius="4dp"
        android:src="@drawable/jdme_bg_section_welfare_right_icon"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.jd.oa.experience.view.TitleView
            android:id="@+id/title_view"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginEnd="70dp"
            android:layout_marginBottom="5dp"/>

        <FrameLayout
            android:id="@+id/subtitle_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="70dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="16dp">
            <View
                android:layout_width="31dp"
                android:layout_height="14dp"
                android:layout_marginStart="16dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/jdme_bg_welfare_section_subtitle_new"/>
            <TextView
                android:id="@+id/subtitle_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="54dp"
                android:layout_gravity="center_vertical"
                android:gravity="start"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="#666666"
                android:textSize="12dp"
                android:includeFontPadding="false"/>
        </FrameLayout>

        <com.jd.oa.experience.view.PagerSnapRecyclerView
            android:id="@+id/welfare_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"/>
    </LinearLayout>

</FrameLayout>