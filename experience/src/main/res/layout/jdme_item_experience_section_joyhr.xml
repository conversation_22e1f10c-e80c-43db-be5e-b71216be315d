<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginHorizontal="8dp"
    android:background="@drawable/jdme_all_round_8"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_joyhr_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_header_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#1B1B1B"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxWidth="200dp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="JoyHRJoyHRJoyHRJoyHRJoyHRJoyHRJoyHRJoyHRJoyHRJoyHRJoyHR" />

        <TextView
            android:id="@+id/tv_chart_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:maxWidth="90dp"
            android:maxLines="1"
            android:textColor="#9D9D9D"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_chart_more"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="全部应用全部应用全部应用全部应用全部应用全部应用" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv_chart_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/icon_direction_right"
            android:textColor="#FF62656D"
            android:textSize="@dimen/JMEIcon_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.jd.oa.experience.view.ChronicleRecyclerView
        android:id="@+id/rv_joyhr_cards"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_joyhr_header"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>