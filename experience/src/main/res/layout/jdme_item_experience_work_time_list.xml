<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:src="#666666"
        app:me_border_color="@color/white"
        app:me_border_width="1dp" />

    <TextView
        android:id="@+id/tv_type"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:autoSizeMinTextSize="10dp"
        android:autoSizeTextType="uniform"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="#666"
        android:textSize="12dp"
        tools:text="communication" />

    <TextView
        android:id="@+id/tv_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/din_condensed_bold"
        android:gravity="end"
        android:minWidth="18dp"
        android:textColor="#FF333333"
        android:textSize="14dp"
        android:textStyle="bold"
        tools:text="999" />

    <TextView
        android:id="@+id/tv_percent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:shadowRadius="3.0"
        android:text="%"
        android:textColor="#666"
        android:textSize="12dp"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>