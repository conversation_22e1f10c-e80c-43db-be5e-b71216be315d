<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jd.oa.experience">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="com.huawei.easygo.permission.READ_PERMISSION" />
    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        tools:remove="android:maxSdkVersion" />
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />

    <application
        android:name=".demo.Apps"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:theme="@style/MEWhiteTheme"
        tools:remove="android:roundIcon,android:supportsRtl"
        tools:replace="android:icon,android:label,android:theme,android:name,android:hardwareAccelerated,android:allowBackup">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <meta-data
            android:name="EasyGoClient"
            android:value="true" />
        <uses-library
            android:name="com.huawei.easygo"
            android:required="false" />

        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />

        <activity
            android:name=".demo.ExpStartupActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/startUpTheme"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".demo.ExpDemoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MainActivityTheme"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden">
            <intent-filter>
                <action android:name="com.jd.oa.ACTION.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".demo.ExpLoginActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode|smallestScreenSize"
            android:label="@string/me_app_name"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <!-- JDPUSH -->
        <meta-data
            android:name="JDPUSH_APPID"
            android:value="1124" />
        <!-- 来自开发者平台取得的secret -->
        <meta-data
            android:name="JDPUSH_APPSECRET"
            android:value="241376f498de412c8d0ed3e05baf113d" />
        <!-- 是否打印日志 0:不打印，1:打印 -->
        <meta-data
            android:name="PUSH_LOG"
            android:value="0" />
        <!-- 是否使用SSL加密配置:0不使用,1使用，默认使用，可以不配置 -->
        <meta-data
            android:name="PUSH_SSL"
            android:value="1" />
        <!--      小米的配置  -->
        <meta-data
            android:name="MIUI_APPID"
            android:value="MI2882303761518333227" />
        <meta-data
            android:name="MIUI_APPKEY"
            android:value="MI5101833310227" />
        <!--        魅族的配置-->
        <meta-data
            android:name="FLYME_APPID"
            android:value="128344" />
        <meta-data
            android:name="FLYME_APPKEY"
            android:value="e5c568c5f02048329bb4e6ee72662715" />
        <!--        华为的配置-->
        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="appid=101804225" />
        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="900743301" />

        <!-- 来自Oppo平台取得的AppID -->
        <meta-data
            android:name="OPPO_APPKEY"
            android:value="21dab112a2944ff58752abf160c4cac2" />

        <meta-data
            android:name="OPPO_SECRET"
            android:value="47a92751695f42c9a58313ee27dbcb34" />
        <!-- 来自Vivo平台取得的AppID -->
        <meta-data
            android:name="com.vivo.push.app_id"
            android:value="105541657" />
        <meta-data
            android:name="com.vivo.push.api_key"
            android:value="73d1d22f9bb55a47994d090c09bef0fb" />
    </application>
</manifest>