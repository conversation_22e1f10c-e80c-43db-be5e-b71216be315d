package com.jd.oa.experience.demo;

import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;
import android.view.Window;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.experience.R;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.StatusBarConfig;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

@Route("jdme://activity/ExpDemo")
public class ExpDemoActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this);
            QMUIStatusBarHelper.setStatusBarLightMode(this);
        }
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_exp_demo);
        FragmentUtils.addWithCommit(this, ExperienceFragment.class, R.id.container, false, false, null);
    }
}