package com.jd.oa.experience.demo;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.webkit.WebView;

import androidx.annotation.RequiresApi;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.multidex.MultiDex;

import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.apm.ApmLoaderHepler;
import com.jd.oa.business.setting.FontScaleUtils;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.experience.BuildConfig;
import com.jd.oa.experience.R;
import com.jd.oa.fragment.dialog.WebActionDialog;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.listener.BatchCallback;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.JdmeHttpManagerConfig;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.NetModule;
import com.jd.oa.storage.StorageHelper;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.upload.IUploadCallback;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.TabletUtil;
import com.jingdong.sdk.baseinfo.BaseInfo;
import com.jingdong.sdk.baseinfo.IBackForegroundCheck;
import com.jingdong.sdk.baseinfo.IBuildConfigGetter;
import com.jingdong.sdk.baseinfo.IPrivacyCheck;
import com.liulishuo.filedownloader.FileDownloader;
import com.liulishuo.filedownloader.connection.FileDownloadUrlConnection;
import com.tencent.mmkv.MMKV;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Locale;
import java.util.Map;

import lib.basenet.NetUtils;

public class Apps extends Application {
    private static Application sContext;
    private static Apps apps;
    private static final String TAG = "Tinker.AppsLike";


    static {
        AppJoint.init(ImDdServiceImplTest.class, AppServiceImplTest.class);
    }

    public Apps() {

    }

    private void initBuildConfig() {
        AppBase.DEBUG = BuildConfig.DEBUG;
        AppBase.SHOW_SERVER_SWITCHER = BuildConfig.SHOW_SERVER_SWITCHER;
        AppBase.BUILD_TYPE = BuildConfig.BUILD_TYPE;
        AppBase.VERSION_NAME = Apps.getVersionName(this);
    }

    public static Application getAppContext() {
        return sContext;
    }

    public static Apps getApps() {
        return apps;
    }

    /**
     * 获取版本号
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.P)
    public static long getVersionCode(Context context) {
        try {
            return context.getPackageManager().getPackageInfo(context.getPackageName(), 0).getLongVersionCode();
        } catch (PackageManager.NameNotFoundException e) {
            return -1;
        }
    }

    /**
     * 获取版本名字
     *
     * @param context 上下文
     */
    public static String getVersionName(Context context) {
        try {
            return context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            return "-1";
        }
    }

    @Override
    public void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        AppBase.setAppContext(this);
        MultiDex.install(base);
        LocalConfigHelper localConfigHelper = LocalConfigHelper.getInstance(this);
        initHttpManager();
        if (!isNotMainProcess(this)) {
            StorageHelper.getInstance(this).init(LocalConfigHelper.getInstance(this).getAppID());
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        sContext = getApplication();
        apps = this;
        initBuildConfig();
        initAppBase();
        initBaseInfo();

        try {
            ApmLoaderHepler.getInstance(this).init(AppBase.DEBUG, BuildConfig.SHOW_SERVER_SWITCHER);
            RouterConfig.init(getApplication());
        } catch (Throwable e) {
            e.printStackTrace();
        }

        initWebView();
        if (isNotMainProcess(this)) {
            return;
        }

        MMKV.initialize(this);
        TabletUtil.init();
        NetUtils.init(new NetUtils.Builder()
                .timeout(30)
                .app(getApplication())
        );
        MyPlatform.initSystem(getApplication());
        NetModule.initNetModule(this);
        registerLifeActivityCallbacks();
        JdmeRounter.init(AppBase.getAppContext());
        // 文件下载 离线安装装包相关
        FileDownloader.setupOnApplicationOnCreate(getApplication())
                .connectionCreator(new FileDownloadUrlConnection.Creator(new FileDownloadUrlConnection.Configuration()
                        .connectTimeout(15_000) // set connection timeout.
                        .readTimeout(15_000) // set read timeout.
                ))
                .commit();
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(() -> {
            ThemeManager.getInstance().checkTheme();
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (themeData != null) {
                System.out.println("themeData=" + themeData.getDir().getPath());
                System.out.println("themeData=" + themeData.getJson().toString());
            }
        }, 5000);
    }

    void initAppBase() {
        AppBase.jdme_AppTheme_Defalut = R.style.jdme_AppTheme_Defalut;
        AppBase.MEWhiteTheme = R.style.MEWhiteTheme;
        AppBase.iAppBase = new AppBase.IAppBase<ArrayList<MemberEntityJd>, MemberListEntityJd>() {

            @Override
            public void showChattingActivity(Context context, String erp) {

            }

            @Override
            public void qrCodeDecode(Context context, WebActionDialog dialog, String imageUrl) {

            }

            @Override
            public void handlePushBizData(Context context, JSONObject bizData) {

            }

            @Override
            public void showContactDetailInfo(Context context, String userName) {

            }

            @Override
            public void showContactDetailInfo(Context context, String appId, String userName) {

            }

            @Override
            public void onQrResultForMigrate(String data) {

            }

            @Override
            public void registerTimlineMessage(String flag, TimlineMessageListener listener) {

            }

            @Override
            public void unregisterListener(String flag, TimlineMessageListener listener) {

            }

            @Override
            public void gotoMemberList(Activity activity, int requestCode, MemberListEntityJd entity, Callback<ArrayList<MemberEntityJd>> cb) {

            }

            @Override
            public String getTimlineAppId() {
                return null;
            }

            @Override
            public void loginTimline() {

            }

            @Override
            public void loginImFromUserUi() {

            }

            @Override
            public boolean isLogin() {
                return PreferenceManager.UserInfo.getLogin() && !TextUtils.isEmpty(MyPlatform.getCurrentUser().getUserName());
            }

            @Override
            public void setKickOut(boolean kickOut) {

            }

            @Override
            public void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl) {

            }

            @Override
            public void imSharePic(String title, String content, String url, String icon, String type) {

            }

            @Override
            public void imClearNoticeUnReadCount(String noticeId) {

            }

            @Override
            public boolean isVirtualErp() {
                return false;
            }

            @Override
            public void uploadFile(String filePath, boolean needAuth, boolean needCdn, String ossBucketName, IUploadCallback callback) {

            }

            @Override
            public void chooseFileFromJs(Map<String, Object> params, Callback2<Boolean> cb) {

            }

            @Override
            public void openMiniApp(String appId, String debugType, String launchPath, String extrasJson, String pageAlias, String scene, String menuInfo, String appInfo) {

            }

            @Override
            public boolean checkMiniAppUrl(String url) {
                return false;
            }

            @Override
            public void gestureAuthenticate(boolean showBiometricPrompt) {

            }

            @Override
            public void getWXBatch(Activity activity, ProgressDialog mProgressDialog, BatchCallback callback) {

            }

            @Override
            public boolean isDebug() {
                return false;
            }

            @Override
            public boolean isTest() {
                return false;
            }

            @Override
            public boolean isForeground() {
                return false;
            }
        };
    }

    public Application getApplication() {
        return this;
    }

    private static String getProcessName(Context context, int pid) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader("/proc/" + pid + "/cmdline"));
            String processName = reader.readLine();
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim();
            }
            return processName;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException exception) {
                exception.printStackTrace();
            }
        }
        return getCurProcessName(context);
    }

    static String getCurProcessName(Context context) {
        int pid = android.os.Process.myPid();

        try {
            ActivityManager mActivityManager = (ActivityManager) context
                    .getSystemService(Context.ACTIVITY_SERVICE);
            for (ActivityManager.RunningAppProcessInfo appProcess : mActivityManager
                    .getRunningAppProcesses()) {
                if (appProcess.pid == pid) {
                    return appProcess.processName;
                }
            }
        } catch (Exception ignored) {

        }

        return "";
    }

    private static boolean isNotMainProcess(Context context) {
        try {
            String curProcess = getProcessName(context, Process.myPid());
            if (curProcess != null) {
                if (context.getPackageName().equals(curProcess)) {
                    return false;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.d(TAG, "onConfigurationChanged: ");
        if (isNotMainProcess(this)) {
            return;
        }
        //折叠屏手机折叠，平板转屏会触发这个方法，监控分屏状态变化，发广播给观察者
        //冷启动入口不会调用这个函数
        //不能用ainActivity的onConfigurationChanged，某些情况下无法收到，比如当一个全屏activity浮在上面时，或者折叠屏手机右屏是EmptyActivity
        //不能用SensorManager检测横竖屏切换，分屏后横屏时获取角度一直是0
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> LocalBroadcastManager.getInstance(Apps.this).sendBroadcast(new Intent(TabletUtil.ACTION_SPLIT_MODE_CHANGE)), 500);
        }
        HttpManager.reset();
        FontScaleUtils.resetFontScaleWhenSystemLanguage(this, newConfig);
    }

    private void initHttpManager() {
        NetEnvironmentConfigModel netEnvConfig = LocalConfigHelper.getInstance(this).getNetEnvironmentConfig();
        String env = NetEnvironmentConfigModel.PROD;
        if (netEnvConfig != null) {
            env = netEnvConfig.getEnv();
        }
        if (AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER) {
            String chooseEnv = PreferenceManager.UserInfo.getNetEnvironment();
            if (!TextUtils.isEmpty(chooseEnv)) {
                env = chooseEnv;
            }
            NetEnvironment legacy;
            GatewayNetEnvironment gateway;
            if (NetEnvironmentConfigModel.CUSTOM.equals(env)) {
                String address = PreferenceManager.Other.getCustomServerAddress();
                legacy = new NetEnvironment(address, address);
            } else {
                legacy = netEnvConfig.getNonGateway(env);
            }
            gateway = netEnvConfig.getGateway(env);

            NetEnvironment.setCurrentEnv(legacy);
            GatewayNetEnvironment.setCurrentEnv(gateway);
        } else {
            NetEnvironment.setCurrentEnv(netEnvConfig.getNonGateway(env));
            GatewayNetEnvironment.setCurrentEnv(netEnvConfig.getGateway(env));
            PreferenceManager.UserInfo.setNetEnvironment(env);
        }

        boolean enableHttp2 = Boolean.parseBoolean(ConfigurationManager.get().getEntry("http2.enable", "false"));

        HttpManagerConfig config = new HttpManagerConfig.Builder()
                .setDeviceInfo(new JdmeHttpManagerConfig.DeviceInfo())
                .setUserInfo(new JdmeHttpManagerConfig.UserInfo())
                .setEncryptUtil(new JdmeHttpManagerConfig.EncryptUtil())
                .setLogger(new JdmeHttpManagerConfig.FileLogger())
                .setEventListener(new JdmeHttpManagerConfig.EventListener())
                .setRecordFullLogs(AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER)
                .setRecordErrorLogs(AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER)
                .setEnableHttp2(enableHttp2)
                .build();
        HttpManager.init(this, config);
    }

    private void initBaseInfo() {
        BaseInfo.init(this);
        BaseInfo.setPrivacyCheckUtil(new IPrivacyCheck() {
            @Override
            public boolean isUserAgreed() {
                return true;//必须返回true，否则收不上崩溃
            }
        });
        BaseInfo.setBackForegroundCheckUtil(new IBackForegroundCheck() {
            @Override
            public boolean isAppForeground() {
                return true;//直接返回true，否则会有问题
            }
        });
        BaseInfo.setBuildConfigGetter(new IBuildConfigGetter() {
            @Override
            public String getAppName() {
                return "jdme";
            }

            @Override
            public String getPackageName() {
                return Apps.this.getPackageName();
            }

            @Override
            public String getVersionName() {
                return Apps.getVersionName(Apps.this);//必须设置，否则鹰眼无法上传崩溃
            }

            @RequiresApi(api = Build.VERSION_CODES.P)
            @Override
            public int getVersionCode() {
                return (int) Apps.getVersionCode(Apps.this);//必须设置，否则鹰眼无法上传崩溃
            }
        });
    }

    private void initWebView() {
        if (Build.VERSION.SDK_INT >= 28 && this.getApplication().getApplicationInfo().targetSdkVersion >= 28) {
            String processName = getProcessName();
            WebView.setDataDirectorySuffix(processName);
        }

    }

    private void registerLifeActivityCallbacks() {
        this.registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                AppBase.topActivity = new WeakReference<>(activity);
                if (activity instanceof ExpDemoActivity) {
                    TabletUtil.leftScreenTopActivity = new WeakReference<>(activity);
                } else {
                    TabletUtil.rightScreenTopActivity = new WeakReference<>(activity);
                }

            }

            @Override
            public void onActivityStarted(Activity activity) {
            }

            @Override
            public void onActivityResumed(Activity activity) {
                AppBase.topActivity = new WeakReference<>(activity);
                if (activity instanceof ExpDemoActivity) {
                    TabletUtil.leftScreenTopActivity = new WeakReference<>(activity);
                } else {
                    TabletUtil.rightScreenTopActivity = new WeakReference<>(activity);
                }

                Locale userSetLocale = LocaleUtils.getUserSetLocale(activity);
                LocaleUtils.setLocaleWhenConfigChange(activity, userSetLocale);
            }

            @Override
            public void onActivityPaused(Activity activity) {
            }

            @Override
            public void onActivityStopped(Activity activity) {
            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                Activity rightActivity = TabletUtil.getRightScreenTopActivity();
                if (activity == rightActivity) {
                    TabletUtil.rightScreenTopActivity = null;
                }
            }
        });
    }
}
