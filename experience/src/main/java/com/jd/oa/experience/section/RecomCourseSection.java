package com.jd.oa.experience.section;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_Course;
import static com.jd.oa.experience.util.Constants.RECOM_COURSE_CACHE_KEY;
import static com.jd.oa.experience.util.Constants.RECOM_COURSE_SECTION_API;

import android.content.Context;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.RecomCourseAdapter;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.RecomCourseData;
import com.jd.oa.experience.model.RefreshObserver;
import com.jd.oa.experience.repo.JDHRRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;

import org.w3c.dom.Text;

import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class RecomCourseSection extends StatelessSection implements Destroyable, RefreshObserver {
    private final Context mContext;
    private final String mTabCode;
    private boolean mIsFirst;
    private final SectionedRecyclerViewAdapter mAdapter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final JDHRRepo<RecomCourseData> repo;
    private RecomCourseData mData;

    public RecomCourseSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_recomcourse)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = JDHRRepo.create(RECOM_COURSE_CACHE_KEY, new TypeToken<RecomCourseData>(){});

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        RecomCourseData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.fetchData(null, RECOM_COURSE_SECTION_API, new LoadDataCallback<RecomCourseData>() {
            @Override
            public void onDataLoaded(RecomCourseData recomCourseData) {
                showData(recomCourseData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }

    private void showData(RecomCourseData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        refreshUI();
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        if (mData.items == null || mData.items.isEmpty()) { // 如果没有数据则隐藏楼层
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            lp.setMargins(0, 0, 0, 0);
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }
        //如果有数据则显示楼层，正常高度
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        int defaultMargin = DensityUtil.dp2px(mContext, 8);
        lp.setMargins(defaultMargin, 0, defaultMargin, defaultMargin);
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);
        mItemViewHolder.mTitle.setText(mData.title);
        mItemViewHolder.mMoreText.setText(mData.jumpText);
        if (TextUtils.isEmpty(mData.jumpUrl)) {
            mItemViewHolder.mMoreText.setVisibility(View.GONE);
            mItemViewHolder.mMoreIcon.setVisibility(View.GONE);
        } else {
            mItemViewHolder.mMoreText.setVisibility(View.VISIBLE);
            mItemViewHolder.mMoreIcon.setVisibility(View.VISIBLE);
        }
        mItemViewHolder.mHeaderContainer.setOnClickListener(new AvoidFastClickListener(1500) {
            @Override
            public void onAvoidedClick(View view) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    JDMAUtils.clickEvent(mobile_EXP_Main_Course, mobile_EXP_Main_Course, null);
                    Router.build(mData.jumpUrl).go(mContext);
                }
            }
        });
        mItemViewHolder.mRecyclerView.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
        mItemViewHolder.mRecyclerView.setAdapter(new RecomCourseAdapter(mData.items));
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    @Override
    public void onRefresh() {
        loadData();
    }

    @Override
    public void onConfigChanged() {
        refreshUI();
    }


    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public ConstraintLayout mHeaderContainer;
        public RecyclerView mRecyclerView;
        public TextView mTitle;
        public TextView mMoreText;
        public TextView mMoreIcon;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mHeaderContainer = itemView.findViewById(R.id.cl_recomcourse_header);
            mRecyclerView = itemView.findViewById(R.id.rv_course_cards);
            mTitle = itemView.findViewById(R.id.tv_header_title);
            mMoreText = itemView.findViewById(R.id.tv_course_more);
            mMoreIcon = itemView.findViewById(R.id.iv_course_more);
        }
    }
}
