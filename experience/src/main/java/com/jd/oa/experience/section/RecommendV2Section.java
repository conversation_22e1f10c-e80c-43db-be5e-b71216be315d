package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.RecommendV2Data;
import com.jd.oa.experience.repo.RecommendV2Repo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.PagerSnapAdapter;
import com.jd.oa.experience.view.PagerSnapRecyclerView;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.SimpleRoundImageView;
import com.jd.oa.utils.ImageLoader;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class RecommendV2Section extends StatelessSection implements Destroyable {

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private RecommendV2Section.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private RecommendV2Repo repo;
    private RecommendV2Data mData;


    private final int BANNER_DELAY_TIME = 5000;

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                case Constants.ACTION_EXP_ON_RESUME:

                    break;
                case Constants.ACTION_EXP_ON_PAUSE:

                    break;
                default:
                    break;
            }
        }
    };

    public RecommendV2Section(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_recommend_v2)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = RecommendV2Repo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        RecommendV2Data cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getRecommendV2Data(new LoadDataCallback<RecommendV2Data>() {
            @Override
            public void onDataLoaded(RecommendV2Data RecommendV2Data) {
                showData(RecommendV2Data);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(RecommendV2Data data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new RecommendV2Section.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        List<RecommendV2Data.Recommend> dataList = mData.getList();

        if (dataList != null && !dataList.isEmpty()) {
            //是否有一条以上IMAGE_LIST_V2类型的数据，没有就认为是空列表，因为这个版本其他类型的样式没有实现
            boolean hasImageListV2Item = false;
            for (int i = 0; i < dataList.size(); i++) {
                if (dataList.get(i).getTplType().equals("IMAGE_LIST_V2")) hasImageListV2Item = true;
            }
            if (hasImageListV2Item) {
                RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
                lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
                mItemViewHolder.itemView.setLayoutParams(lp);
                ExpTabFragment.notifySectionVisible(mContext, mTabCode);
            } else {
                RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
                lp.height = 0;
                mItemViewHolder.itemView.setLayoutParams(lp);
            }
        } else {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }

        mItemViewHolder.mLlRecList.removeAllViews();
        for (int i = 0; i < dataList.size(); i++) {
            // 因为这个版本只有一种轮播图的样式    过滤掉非IMAGE_LIST_V2类型的推荐卡片
            if (dataList.get(i) != null && dataList.get(i).getTplType().equals("IMAGE_LIST_V2")) {
                mItemViewHolder.mLlRecList.addView(getCardView(dataList.get(i)));
            }
        }

    }

    /**
     * 构建单张卡片布局
     *
     * @param data
     * @return
     */
    public View getCardView(@NotNull final RecommendV2Data.Recommend data) {
        View view = View.inflate(mContext, R.layout.jdme_item_experience_section_recommend_v2_item, null);
        final HashMap<String, String> params = new HashMap<>();
        if (!TextUtils.isEmpty(data.getId())) {
            params.put("cardId", data.getId());
        }
        //1、设置卡片标题
        TitleView mTitleView = view.findViewById(R.id.title_view);
        mTitleView.setTitle(false, data.getIcon(), data.getTitle(), data.getJumpUrl(), data.getJumpText());
        mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mContext != null && !TextUtils.isEmpty(data.getJumpUrl())) {
                    Router.build(data.getJumpUrl()).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INSIGHTS, params);
                }
            }
        });

        //2、设置底部按钮
        LinearLayout mLlButtons = view.findViewById(R.id.ll_bottom);
        if (data.getData() != null && data.getData().getFooter() != null) {
            List<RecommendV2Data.Recommend.Data.Footer> footer = data.getData().getFooter();
            if (footer == null || footer.size() == 0) {
                mLlButtons.setVisibility(View.GONE);
            } else {
                mLlButtons.setVisibility(View.VISIBLE);
                TextView tv1 = view.findViewById(R.id.tv_btn1);
                TextView tv2 = view.findViewById(R.id.tv_btn2);
                if (footer.size() >= 2) {
                    tv1.setVisibility(View.VISIBLE);
                    tv2.setVisibility(View.VISIBLE);
                    tv1.setText(footer.get(0).getTitle());
                    tv2.setText(footer.get(1).getTitle());
                    setFooterClick(tv1, footer.get(0).getUrl(), data.getId(), footer.get(0).getId());
                    setFooterClick(tv2, footer.get(1).getUrl(), data.getId(), footer.get(1).getId());
                } else if (footer.size() == 1) {
                    tv1.setVisibility(View.VISIBLE);
                    tv1.setText(footer.get(0).getTitle());
                    setFooterClick(tv1, footer.get(0).getUrl(), data.getId(), footer.get(0).getId());
                    tv2.setVisibility(View.GONE);
                } else {
                    tv1.setVisibility(View.GONE);
                    tv2.setVisibility(View.GONE);
                }
            }
        }

        //3、设置中间轮播图
        final PagerSnapRecyclerView<RecommendV2Data.Recommend.Data.Body.Items> mRecRv = view.findViewById(R.id.rec_rv);
        //填充轮播图数据
        if (data.getData() != null && data.getData().getBody() != null) {
            if (data.getData().getBody().getItems() == null || data.getData().getBody().getItems().size() == 0) {
                mRecRv.setVisibility(View.GONE);
            } else {
                mRecRv.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
                mRecRv.setVisibility(View.VISIBLE);
                mRecRv.setAdapter(new PagerSnapAdapter<RecommendV2Data.Recommend.Data.Body.Items>(
                        mContext, data.getData().getBody().getItems(), PagerSnapAdapter.VIEW_TYPE_BANNER,
                        R.layout.jdme_item_experience_section_recommend_v2_item_carditem) {
                    @Override
                    public void onBindView(View view, final RecommendV2Data.Recommend.Data.Body.Items recommend) {
                        SimpleRoundImageView iv = (SimpleRoundImageView) view;
                        ImageLoader.load(mContext, iv, recommend.getImage(), false
                                , R.drawable.jdme_exp_image_recv2_placeholder);
                        view.setOnClickListener(new NoDoubleClickListener() {
                            @Override
                            protected void onNoDoubleClick(View v) {
                                if (!TextUtils.isEmpty(recommend.getUrl())) {
                                    Router.build(recommend.getUrl()).go(mContext, new RouteNotFoundCallback(mContext));
                                    if (!TextUtils.isEmpty(recommend.getId())) {
                                        params.put("clickId", recommend.getId());
                                    }
                                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INSIGHTS, params);
                                }
                            }
                        });
                    }
                });
            }
        }
        return view;
    }


    /**
     * 设置footer点击事件
     *
     * @param tv
     * @param url
     */
    public void setFooterClick(TextView tv, final String url, String cardId, String footId) {
        if (tv != null && !TextUtils.isEmpty(url) && mContext != null) {
            final HashMap<String, String> params = new HashMap<>();
            if (!TextUtils.isEmpty(cardId)) {
                params.put("cardId", cardId);
            }
            if (!TextUtils.isEmpty(footId)) {
                params.put("clickId", footId);
            }
            tv.setOnClickListener(new NoDoubleClickListener() {
                @Override
                protected void onNoDoubleClick(View v) {
                    if (!TextUtils.isEmpty(url) && mContext != null)
                        Router.build(url).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INSIGHTS, params);
                }
            });
        }
    }


    private static class ItemViewHolder extends RecyclerView.ViewHolder {
        public LinearLayout mLlRecList;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mLlRecList = itemView.findViewById(R.id.ll_rec_list);
        }
    }

    public interface BannerLifeCycleListener {
        void onResume();

        void onPause();
    }
}
