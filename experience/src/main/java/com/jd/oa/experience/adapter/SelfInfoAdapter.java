package com.jd.oa.experience.adapter;

import static com.jd.oa.router.DeepLink.APP_CENTER;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.MinePortraitFragment;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.UserInfoEditModel;
import com.jd.oa.experience.R;
import com.jd.oa.experience.activity.MyInstructionActivity;
import com.jd.oa.experience.model.UserInfoData;
import com.jd.oa.experience.model.UserInfoFieldSet;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback3;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ToastUtils;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;
import com.zhy.view.flowlayout.TagFlowLayout;

import java.util.ArrayList;
import java.util.List;

public class SelfInfoAdapter extends RecyclerView.Adapter<SelfInfoAdapter.ViewHolder> {

    public interface OnItemClickListener {
        void onMobileClick();
        void onTelephoneClick();
        void openPersonalDetails(String hrbpUserName);
    }
    private static final int TYPE_ITEM_NONE = 0;
    private static final int TYPE_ITEM_TEXT = 1;
    private static final int TYPE_ITEM_AVATAR = 2;
    private static final int TYPE_ITEM_IMAGE = 3;
    private static final int TYPE_ITEM_TAGS = 4;
    private static final int TYPE_ITEM_SIGNATURE = 5;
    private static final int TYPE_ITEM_FEELING = 6;
    private static final int TYPE_ITEM_QRCODE = 7;

    private final Context mContext;
    private UserInfoData mUserInfoData;
    private List<UserInfoFieldSet> mUserInfoFieldSetList;

    private final ImDdService imDdService;

    private SelfInfoAdapter.ViewHolder mAvatarViewHolder;
    private SelfInfoAdapter.ViewHolder mMobileViewHolder;
    private SelfInfoAdapter.ViewHolder mTelephoneViewHolder;
    private SelfInfoAdapter.ViewHolder mSignatureViewHolder;
    private SelfInfoAdapter.ViewHolder mFeelingViewHolder;

    private int mItemCount;
    private OnItemClickListener mOnClickListener;
    private UserInfoEditModel mUserInfoEditModel;

    public SelfInfoAdapter(Context context, ImDdService imDdService, OnItemClickListener listener) {
        mContext = context;
        this.imDdService = imDdService;
        mOnClickListener = listener;
        this.mUserInfoEditModel = LocalConfigHelper.getInstance(context).getUserInfoEditConfig();
    }

    //UserInfoData为提测前临时修改的新版数据格式，和原版数据格式UserInfoFieldSet差距太大，避免提测前大量修改逻辑
    //将UserInfoData转换为UserInfoFieldSet
    //这个功能是按可灵活配置做的，突然改了需求，变成固定配置了

    public void setData(UserInfoData data) {
        mUserInfoData = data;
        mUserInfoFieldSetList = new ArrayList<>();
        mItemCount = 0;
        //用新数据格式生成老数据格式，没有时间改逻辑
        UserInfoFieldSet firstSet = new UserInfoFieldSet();
        UserInfoFieldSet secondSet = new UserInfoFieldSet();
        mUserInfoFieldSetList.add(firstSet);
        mUserInfoFieldSetList.add(secondSet);

        if(MultiAppConstant.isSaasFlavor()){
            addSaaSItem(data,firstSet,secondSet);
        }else {
            addMEItem(data,firstSet,secondSet);
        }

        notifyDataSetChanged();
    }

    private void addSaaSItem(UserInfoData data,UserInfoFieldSet firstSet,UserInfoFieldSet secondSet){
        //上半部分
        firstSet.fieldset = new ArrayList<>();
        if(mUserInfoEditModel.avatar){
            String fieldLabel = mUserInfoEditModel.avatarAccessories ? mContext.getString(R.string.exp_self_info_avatar) : mContext.getString(R.string.me_user_icon);
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("headImg", "native",fieldLabel, data.avatar));
            mItemCount++;
        }
        if(mUserInfoEditModel.name){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_name), data.realName));
            mItemCount++;
        }
        if(mUserInfoEditModel.account){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_erp), data.pin));
            mItemCount++;
        }
        //我的二维码
        if(mUserInfoEditModel.myQRCode){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("qrcode", "native", mContext.getString(R.string.exp_self_info_myqrcode),""));
            mItemCount++;
        }
        if(mUserInfoEditModel.employeeID){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_id), data.userCode));
            mItemCount++;
        }
        if(mUserInfoEditModel.mobile){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("mobile", "text", mContext.getString(R.string.exp_self_info_mobile), data.mobile));
            mItemCount++;
        }
        if(mUserInfoEditModel.landline){
            UserInfoFieldSet.UserInfoField field = new UserInfoFieldSet.UserInfoField("telephone", "text", mContext.getString(R.string.exp_self_info_telephone), data.telephone);
            field.placeholder = mContext.getString(R.string.exp_self_info_telephone_hint);
            firstSet.fieldset.add(field);
            mItemCount++;
        }
        if(mUserInfoEditModel.email){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_email), data.email));
            mItemCount++;
        }
        if (!TextUtils.isEmpty(data.hrbpUserName)) {//这个特殊处理，有可能不展示
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("hrbp", "text", mContext.getString(R.string.exp_self_info_hrbp), data.hrbpUserName));
            mItemCount++;
        }
        //京东WE工作签名显示在上半部分
        if(mUserInfoEditModel.workSignature){
            UserInfoFieldSet.UserInfoField sign = new UserInfoFieldSet.UserInfoField("sign", "native", mContext.getString(R.string.exp_self_info_signature), "");
            firstSet.fieldset.add(sign);
            mItemCount++;
        }

        //下半部分
        secondSet.fieldset = new ArrayList<>();
        if (mUserInfoEditModel.personalTag){
            UserInfoFieldSet.UserInfoField tags = new UserInfoFieldSet.UserInfoField("", "tag", mContext.getString(R.string.exp_self_info_tags), data.tags);
            tags.placeholder = mContext.getString(R.string.exp_self_info_tags_hint);
            tags.deeplink = APP_CENTER + "/202206061284";
            secondSet.fieldset.add(tags);
            mItemCount++;
        }
        if(mUserInfoEditModel.status){
            UserInfoFieldSet.UserInfoField feeling = new UserInfoFieldSet.UserInfoField("feeling", "native", mContext.getString(R.string.exp_self_info_feeling), "");
            feeling.placeholder = mContext.getString(R.string.exp_self_info_feeling_hint);
            secondSet.fieldset.add(feeling);
            mItemCount++;
        }
        if(mUserInfoEditModel.themeSkin){
            UserInfoFieldSet.UserInfoField image = new UserInfoFieldSet.UserInfoField("bgImage", "image", mContext.getString(R.string.exp_self_info_skin), data.bgImageUrl);
            image.placeholder = mContext.getString(R.string.exp_self_info_skin_hint);
            image.deeplink = DeepLink.EXP_SKIN_THEME;
            secondSet.fieldset.add(image);
            mItemCount++;
        }
        if (mUserInfoEditModel.myManual && data.userManual != null) {
            String instructionValue = data.userManual ? mContext.getString(R.string.exp_self_info_instruction) : "";
            UserInfoFieldSet.UserInfoField instruction = new UserInfoFieldSet.UserInfoField("instruction", "text", mContext.getString(R.string.exp_self_info_instruction), instructionValue);
            instruction.placeholder = mContext.getString(R.string.exp_self_info_instruction_hint);
            secondSet.fieldset.add(instruction);
            mItemCount++;
        }
        //京WE上企业显示在下半部分
        if(mUserInfoEditModel.company){
            UserInfoFieldSet.UserInfoField company = new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_company), data.enterprise);
            company.multiLine = true;
            secondSet.fieldset.add(company);
            mItemCount++;
        }
        //京WE上部门显示在下半部分
        if(mUserInfoEditModel.department){
            UserInfoFieldSet.UserInfoField department = new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_dept), data.dept);
            department.multiLine = true;
            secondSet.fieldset.add(department);
            mItemCount++;
        }
    }

    private void addMEItem(UserInfoData data,UserInfoFieldSet firstSet,UserInfoFieldSet secondSet){
        //上半部分
        firstSet.fieldset = new ArrayList<>();
        if(mUserInfoEditModel.avatar){
            String fieldLabel = mUserInfoEditModel.avatarAccessories ? mContext.getString(R.string.exp_self_info_avatar) : mContext.getString(R.string.me_user_icon);
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("headImg", "native",fieldLabel, data.avatar));
            mItemCount++;
        }
        if(mUserInfoEditModel.name){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_name), data.realName));
            mItemCount++;
        }
        //京ME上部门显示在上半部分
        if(mUserInfoEditModel.department){
            UserInfoFieldSet.UserInfoField department = new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_dept), data.organizationFullName);
            // 注掉 deeplink，会自动屏蔽点击事件以及右边的箭头
//        if (TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_USEDEPTDETAIL)) {
//            if (NetworkConstant.getCurrentServerName().index == 3) {
//                department.deeplink = DeepLink.appCenter("************", null);
//            } else {
//                department.deeplink = DeepLink.appCenter("************", null);
//            }
//        }
            department.multiLine = true;
            firstSet.fieldset.add(department);
            mItemCount++;
        }
        if(mUserInfoEditModel.account){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_erp), data.userName));
            mItemCount++;
        }
        if(mUserInfoEditModel.employeeID){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_id), data.userCode));
            mItemCount++;
        }
        if(mUserInfoEditModel.mobile){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("mobile", "text", mContext.getString(R.string.exp_self_info_mobile), data.mobile));
            mItemCount++;
        }
        if(mUserInfoEditModel.landline){
            UserInfoFieldSet.UserInfoField field = new UserInfoFieldSet.UserInfoField("telephone", "text", mContext.getString(R.string.exp_self_info_telephone), data.telephone);
            field.placeholder = mContext.getString(R.string.exp_self_info_telephone_hint);
            firstSet.fieldset.add(field);
            mItemCount++;
        }
        if(mUserInfoEditModel.email){
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("", "text", mContext.getString(R.string.exp_self_info_email), data.email));
            mItemCount++;
        }
        if (!TextUtils.isEmpty(data.hrbpUserName)) {//这个特殊处理，有可能不展示
            firstSet.fieldset.add(new UserInfoFieldSet.UserInfoField("hrbp", "text", mContext.getString(R.string.exp_self_info_hrbp), data.hrbpUserName));
            mItemCount++;
        }

        //下半部分
        secondSet.fieldset = new ArrayList<>();
        //京东ME工作签名显示在下半部分
        if(mUserInfoEditModel.workSignature){
            UserInfoFieldSet.UserInfoField sign = new UserInfoFieldSet.UserInfoField("sign", "native", mContext.getString(R.string.exp_self_info_signature), "");
            secondSet.fieldset.add(sign);
            mItemCount++;
        }
        if (mUserInfoEditModel.personalTag){
            UserInfoFieldSet.UserInfoField tags = new UserInfoFieldSet.UserInfoField("", "tag", mContext.getString(R.string.exp_self_info_tags), data.tags);
            tags.placeholder = mContext.getString(R.string.exp_self_info_tags_hint);
            tags.deeplink = APP_CENTER + "/202206061284";
            secondSet.fieldset.add(tags);
            mItemCount++;
        }
        if(mUserInfoEditModel.status){
            UserInfoFieldSet.UserInfoField feeling = new UserInfoFieldSet.UserInfoField("feeling", "native", mContext.getString(R.string.exp_self_info_feeling), "");
            feeling.placeholder = mContext.getString(R.string.exp_self_info_feeling_hint);
            secondSet.fieldset.add(feeling);
            mItemCount++;
        }
        if(mUserInfoEditModel.themeSkin){
            UserInfoFieldSet.UserInfoField image = new UserInfoFieldSet.UserInfoField("bgImage", "image", mContext.getString(R.string.exp_self_info_skin), data.bgImageUrl);
            image.placeholder = mContext.getString(R.string.exp_self_info_skin_hint);
            image.deeplink = DeepLink.EXP_SKIN_THEME;
            secondSet.fieldset.add(image);
            mItemCount++;
        }
        if (mUserInfoEditModel.myManual && data.userManual != null) {
            String instructionValue = data.userManual ? mContext.getString(R.string.exp_self_info_instruction) : "";
            UserInfoFieldSet.UserInfoField instruction = new UserInfoFieldSet.UserInfoField("instruction", "text", mContext.getString(R.string.exp_self_info_instruction), instructionValue);
            instruction.placeholder = mContext.getString(R.string.exp_self_info_instruction_hint);
            secondSet.fieldset.add(instruction);
            mItemCount++;
        }
    }

    public UserInfoData getData() {
        return mUserInfoData;
    }

    public void onDestroy() {
        imDdService.unregisterUserStatusChangeListener(getClass().getSimpleName());
    }

    @Override
    public int getItemViewType(int position) {
        UserInfoFieldSet.UserInfoField uif = getFieldByPosition(position);
        if (uif == null) {
            return TYPE_ITEM_NONE;
        }

        if (TextUtils.equals(uif.fieldType, "text")) {//文字
            return TYPE_ITEM_TEXT;
        } else if (TextUtils.equals(uif.fieldType, "native")) {//头像，签名
            if (TextUtils.equals(uif.fieldId, "headImg")) {
                return TYPE_ITEM_AVATAR;
            } else if (TextUtils.equals(uif.fieldId, "sign")) {
                return TYPE_ITEM_SIGNATURE;
            } else if (TextUtils.equals(uif.fieldId, "feeling")) {
                return TYPE_ITEM_FEELING;
            } else if (TextUtils.equals(uif.fieldId,"qrcode")){
                return TYPE_ITEM_QRCODE;
            } else {
                return TYPE_ITEM_NONE;
            }
        } else if (TextUtils.equals(uif.fieldType, "tag")) {
            return TYPE_ITEM_TAGS;
        } else if (TextUtils.equals(uif.fieldType, "image")) {
            return TYPE_ITEM_IMAGE;
        } else {
            return TYPE_ITEM_NONE;
        }
    }

    private UserInfoFieldSet.UserInfoField getFieldByPosition(int position) {
        if (mUserInfoFieldSetList == null || mUserInfoFieldSetList.isEmpty()) {
            return null;
        }
        for (UserInfoFieldSet data : mUserInfoFieldSetList) {
            if (data == null || data.fieldset == null) {
                continue;
            }
            if (position < data.fieldset.size()) {
                return data.fieldset.get(position);
            } else {
                position -= data.fieldset.size();
            }
        }
        return null;
    }

    @NonNull
    @Override
    public SelfInfoAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;
        if (viewType == TYPE_ITEM_TEXT) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_text_item, parent, false);
        } else if (viewType == TYPE_ITEM_AVATAR) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_avatar_item, parent, false);
        } else if (viewType == TYPE_ITEM_IMAGE) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_image_item, parent, false);
        } else if (viewType == TYPE_ITEM_TAGS) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_tags_item, parent, false);
        } else if (viewType == TYPE_ITEM_SIGNATURE) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_signature_item, parent, false);
        } else if (viewType == TYPE_ITEM_FEELING) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_feeling_item, parent, false);
        } else if (viewType == TYPE_ITEM_QRCODE) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_qrcode_item,parent,false);
        } else {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_text_item, parent, false);
        }

        return new SelfInfoAdapter.ViewHolder(view);
    }

    private static final int TOP_IN_FIELDSET = 0;
    private static final int MIDDLE_IN_FIELDSET = 1;
    private static final int BOTTOM_IN_FIELDSET = 2;

    private int getItemSequence(int position) {
        for (UserInfoFieldSet data : mUserInfoFieldSetList) {
            if (data == null || data.fieldset == null || data.fieldset.isEmpty()) {
                continue;
            }
            if (position == 0) {
                return TOP_IN_FIELDSET;
            } else if (position < data.fieldset.size() - 1) {
                return MIDDLE_IN_FIELDSET;
            } else if (position == data.fieldset.size() - 1) {
                return BOTTOM_IN_FIELDSET;
            } else {
                position -= data.fieldset.size();
            }
        }
        return -1;
    }

    @Override
    public void onBindViewHolder(@NonNull final SelfInfoAdapter.ViewHolder holder, int position) {
        if (TYPE_ITEM_NONE == holder.getItemViewType()) {
            return;
        }

        UserInfoFieldSet.UserInfoField uif = getFieldByPosition(position);
        if (uif == null) {
            return;
        }

        int sequence = getItemSequence(position);
        if (sequence == TOP_IN_FIELDSET) {
            holder.mTopMargin.setVisibility(View.VISIBLE);
            holder.mBottomMargin.setVisibility(View.GONE);
            holder.mItemBackGround.setBackgroundResource(R.drawable.jdme_bg_self_info_top);
            holder.mItemDivider.setVisibility(View.VISIBLE);
        } else if (sequence == BOTTOM_IN_FIELDSET) {
            holder.mTopMargin.setVisibility(View.GONE);
            if (position >= mItemCount - 1) {
                holder.mBottomMargin.setVisibility(View.VISIBLE);
            } else {
                holder.mBottomMargin.setVisibility(View.GONE);
            }
            holder.mItemBackGround.setBackgroundResource(R.drawable.jdme_bg_self_info_bottom);
            holder.mItemDivider.setVisibility(View.GONE);
        } else if (sequence == MIDDLE_IN_FIELDSET) {
            holder.mTopMargin.setVisibility(View.GONE);
            holder.mBottomMargin.setVisibility(View.GONE);
            holder.mItemBackGround.setBackgroundResource(R.drawable.jdme_bg_self_info_middle);
            holder.mItemDivider.setVisibility(View.VISIBLE);
        } else {
            return;
        }

        if (TYPE_ITEM_TEXT == holder.getItemViewType()) {
            if (TextUtils.equals(uif.fieldId, "mobile")) {
                mMobileViewHolder = holder;
            } else if (TextUtils.equals(uif.fieldId, "telephone")) {
                mTelephoneViewHolder = holder;
            }
            bindTextItem(holder, uif);
        } else if (TYPE_ITEM_AVATAR == holder.getItemViewType()) {
            mAvatarViewHolder = holder;
            bindAvatarItem(holder, uif);
        } else if (TYPE_ITEM_IMAGE == holder.getItemViewType()) {
            bindImageItem(holder, uif);
        } else if (TYPE_ITEM_SIGNATURE == holder.getItemViewType()) {
            mSignatureViewHolder = holder;
            bindSignatureItem(holder, uif);
        } else if (TYPE_ITEM_TAGS == holder.getItemViewType()) {
            bindTagsItem(holder, uif);
        } else if (TYPE_ITEM_FEELING == holder.getItemViewType()) {
            mFeelingViewHolder = holder;
            bindFeelingItem(holder, uif);
        } else if (TYPE_ITEM_QRCODE == holder.getItemViewType()) {
            bindQrCodeItem(holder,uif);
        }
    }

    @Override
    public int getItemCount() {
        return mItemCount;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        public View mTopMargin;
        public View mBottomMargin;
        public View mItemBackGround;
        public View mItemDivider;

        public TextView mItemNameTv;
        public View mItemButton;

        public View mItemTextLayout;
        public TextView mItemTextValue;
        public ImageView mItemAvatar;
        public ImageView mItemPendant;
        public View mItemImageView;
        public ImageView mItemImage;
        public TextView mItemImageTextHint;
        public TextView mItemSignature;
        public TextView mItemSignatureHint;
        public View mItemSignatureLayout;
        public TagFlowLayout mItemTagsFlow;
        public TextView mItemTagsHint;
        public ImageView mItemIconValue;
        public TextView mItemTitleValue;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);

            mTopMargin = itemView.findViewById(R.id.top_margin);
            mBottomMargin = itemView.findViewById(R.id.bottom_margin);
            mItemBackGround = itemView.findViewById(R.id.self_info_item_bkgnd);
            mItemDivider = itemView.findViewById(R.id.self_info_item_divider);
            mItemNameTv = itemView.findViewById(R.id.self_info_item_name);
            mItemButton = itemView.findViewById(R.id.self_info_item_button);
            //text
            mItemTextLayout = itemView.findViewById(R.id.self_info_item_text_layout);
            mItemTextValue = itemView.findViewById(R.id.self_info_item_text_value);
            //avatar
            mItemAvatar = itemView.findViewById(R.id.self_info_item_avatar);
            mItemPendant = itemView.findViewById(R.id.self_info_item_pendant);
            //image
            mItemImageView = itemView.findViewById(R.id.self_info_item_image_layout);
            mItemImage = itemView.findViewById(R.id.self_info_item_image);
            mItemImageTextHint = itemView.findViewById(R.id.self_info_item_image_hint);
            //signature
            mItemSignature = itemView.findViewById(R.id.self_info_item_signature);
            mItemSignatureHint = itemView.findViewById(R.id.self_info_item_signature_hint);
            mItemSignatureLayout = itemView.findViewById(R.id.layout_signature);
            //tags
            mItemTagsFlow = itemView.findViewById(R.id.self_info_item_tags);
            mItemTagsHint = itemView.findViewById(R.id.self_info_item_tags_hint);
            //feeling
            mItemIconValue = itemView.findViewById(R.id.self_info_item_icon);
            mItemTitleValue = itemView.findViewById(R.id.self_info_item_title);
        }
    }

    private void bindTextItem(@NonNull final SelfInfoAdapter.ViewHolder holder, final UserInfoFieldSet.UserInfoField uif) {
        holder.mItemTextValue.setMaxLines(uif.multiLine ? 2 : 1);
        holder.mItemNameTv.setText(uif.fieldLabel);
        boolean show = (TextUtils.equals(uif.fieldId, "hrbp") ||
                TextUtils.equals(uif.fieldId, "mobile") ||
                TextUtils.equals(uif.fieldId, "telephone") ||
                TextUtils.equals(uif.fieldId, "instruction") ||
                !TextUtils.isEmpty(uif.deeplink));
        holder.mItemButton.setVisibility(show ? View.VISIBLE : View.GONE);
        if (uif.fieldValue instanceof String && !uif.fieldValue.toString().isEmpty()) {
            holder.mItemTextValue.setText(uif.fieldValue.toString());
            holder.mItemTextValue.setTextColor(0xff666666);
        } else {
            holder.mItemTextValue.setText(uif.placeholder);
            holder.mItemTextValue.setTextColor(0xffcdcdcd);
        }
        //有deeplink带箭头，有箭头的可以跳转，没有箭头的可以复制
        if (show) {
            View.OnClickListener listener = new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (TextUtils.equals(uif.fieldId, "mobile")) {
                        if (mOnClickListener != null) {
                            mOnClickListener.onMobileClick();
                            ExpJDMAUtil.onSelfInfoEventClick("手机");
                        }
                        return;
                    }
                    if (TextUtils.equals(uif.fieldId, "telephone")) {
                        if (mOnClickListener != null) {
                            mOnClickListener.onTelephoneClick();
                            ExpJDMAUtil.onSelfInfoEventClick("座机");
                        }
                        return;
                    }
                    if (TextUtils.equals(uif.fieldId, "hrbp") && !TextUtils.isEmpty(uif.fieldValue.toString())) {
                        if (mOnClickListener != null) {
                            mOnClickListener.openPersonalDetails(uif.fieldValue.toString());
                            ExpJDMAUtil.onSelfInfoEventClick("HRBP");
                        }
                        return;
                    }
                    if (TextUtils.equals(uif.fieldId, "instruction")) {
                        if (mOnClickListener != null) {
                            Intent intent = new Intent(mContext, MyInstructionActivity.class);
                            mContext.startActivity(intent);
                            ExpJDMAUtil.onSelfInfoEventClick("我的说明书");
                        }
                        return;
                    }
                    if (!TextUtils.isEmpty(uif.deeplink)) {
                        Router.build(uif.deeplink).go(mContext, new RouteNotFoundCallback(mContext));
                        onClickEvent(uif.fieldLabel);
                    }
                }
            };
            holder.mItemBackGround.setOnClickListener(listener);
            holder.mItemTextLayout.setOnClickListener(listener);
        } else {
            holder.mItemBackGround.setOnClickListener(null);
            holder.mItemTextLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    popupCopyText(holder.mItemTextValue, uif.fieldLabel);
                }
            });
        }
    }

    private void onClickEvent(String fieldLabel) {
        if (TextUtils.equals(fieldLabel, mContext.getString(R.string.exp_self_info_name))) {
            ExpJDMAUtil.onSelfInfoEventClick("姓名");
        } else if (TextUtils.equals(fieldLabel, mContext.getString(R.string.exp_self_info_dept))) {
            ExpJDMAUtil.onSelfInfoEventClick("部门");
        } else if (TextUtils.equals(fieldLabel, mContext.getString(R.string.exp_self_info_erp))) {
            ExpJDMAUtil.onSelfInfoEventClick("账号");
        } else if (TextUtils.equals(fieldLabel, mContext.getString(R.string.exp_self_info_id))) {
            ExpJDMAUtil.onSelfInfoEventClick("员工ID");
        } else if (TextUtils.equals(fieldLabel, mContext.getString(R.string.exp_self_info_email))) {
            ExpJDMAUtil.onSelfInfoEventClick("邮箱");
        }
    }

    //头像
    private void bindAvatarItem(@NonNull final SelfInfoAdapter.ViewHolder holder, final UserInfoFieldSet.UserInfoField uif) {
        holder.mItemNameTv.setText(uif.fieldLabel);
        holder.mItemButton.setVisibility(View.VISIBLE);
        holder.mItemBackGround.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (MultiAppConstant.isSaasFlavor()){
                    ToastUtils.showToast(mContext.getString(R.string.exp_self_info_modify_not_supported));
                }else{
                    Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, MinePortraitFragment.class.getName());
                    mContext.startActivity(intent);
                    ExpJDMAUtil.onSelfInfoEventClick("头像及挂件");
                }
            }
        });

        //头像
        String avatar = PreferenceManager.UserInfo.getUserCover();
        if (TextUtils.isEmpty(avatar) && uif.fieldValue instanceof String && !TextUtils.isEmpty((String)uif.fieldValue)) {
            avatar = (String)uif.fieldValue;
            PreferenceManager.UserInfo.setUserCover(avatar);
        }
        ImageLoader.load(mContext, holder.mItemAvatar, avatar, R.drawable.jdsaas_personal_default_avatar);
        //挂件
        if(mUserInfoEditModel.avatarAccessories){
            updatePendant();
        }
    }

    private void bindFeelingItem(@NonNull final SelfInfoAdapter.ViewHolder holder, final UserInfoFieldSet.UserInfoField uif) {
        holder.mItemNameTv.setText(uif.fieldLabel);
        holder.mItemButton.setVisibility(View.VISIBLE);
        holder.mItemBackGround.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                imDdService.openSetUserStatus(mContext);
                ExpJDMAUtil.onSelfInfoEventClick("状态");
            }
        });
        //默认
        holder.mItemIconValue.setVisibility(View.INVISIBLE);
        holder.mItemTitleValue.setText(uif.placeholder);
        holder.mItemTitleValue.setTextColor(0xffcdcdcd);
        //update feeling
        imDdService.registerUserStatusChangeListener(PreferenceManager.UserInfo.getUserName(), getClass().getSimpleName(), new Callback3<String>() {
            @Override
            public void onSuccess(String icon, String title, String titleEn) {
                if (mFeelingViewHolder == null) {
                    return;
                }
                if (TextUtils.isEmpty(icon) && TextUtils.isEmpty(title) && TextUtils.isEmpty(titleEn)) {//无状态
                    mFeelingViewHolder.mItemIconValue.setVisibility(View.INVISIBLE);
                    mFeelingViewHolder.mItemTitleValue.setText(uif.placeholder);
                    mFeelingViewHolder.mItemTitleValue.setTextColor(0xffcdcdcd);
                } else {
                    mFeelingViewHolder.mItemIconValue.setVisibility(!TextUtils.isEmpty(icon) ? View.VISIBLE : View.INVISIBLE);
                    ImageLoader.load(mContext, mFeelingViewHolder.mItemIconValue, icon);
                    if (TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN")) {
                        mFeelingViewHolder.mItemTitleValue.setText(title);
                    } else {
                        mFeelingViewHolder.mItemTitleValue.setText(titleEn);
                    }
                    mFeelingViewHolder.mItemTitleValue.setTextColor(0xff666666);
                }
            }

            @Override
            public void onFail(String msg) {

            }
        });
    }

    //主题背景
    private void bindImageItem(@NonNull final SelfInfoAdapter.ViewHolder holder, final UserInfoFieldSet.UserInfoField uif) {
        holder.mItemNameTv.setText(uif.fieldLabel);
        holder.mItemButton.setVisibility(TextUtils.isEmpty(uif.deeplink) ? View.GONE : View.VISIBLE);
        holder.mItemBackGround.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(uif.deeplink)) {
                    Router.build(uif.deeplink).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onSelfInfoEventClick("主题换肤");
                }
            }
        });
        if (uif.fieldValue instanceof String && !TextUtils.isEmpty((String)uif.fieldValue)) {
            holder.mItemImageView.setVisibility(View.VISIBLE);
            holder.mItemImageTextHint.setVisibility(View.GONE);
            ImageLoader.load(mContext, holder.mItemImage, (String) uif.fieldValue, false, R.drawable.jdme_self_info_image_default);
        } else {
            holder.mItemImageView.setVisibility(View.GONE);
            holder.mItemImageTextHint.setVisibility(View.VISIBLE);
            holder.mItemImageTextHint.setText(uif.placeholder);
        }
    }

    //签名
    private void bindSignatureItem(@NonNull final SelfInfoAdapter.ViewHolder holder, UserInfoFieldSet.UserInfoField uif) {
        holder.mItemNameTv.setText(uif.fieldLabel);
        holder.mItemButton.setVisibility(View.VISIBLE);
        holder.mItemSignatureLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mContext instanceof AppCompatActivity) {
                    String hint = mContext.getString(R.string.exp_self_info_signature_hint);
                    String title = mContext.getString(R.string.exp_signature_title);
                    imDdService.openSignatureEdit((AppCompatActivity)mContext, title, hint, new Callback<CharSequence>() {
                        @Override
                        public void onSuccess(CharSequence formatSignature) {
                            if (!TextUtils.isEmpty(formatSignature)) {
                                mSignatureViewHolder.mItemSignature.setVisibility(View.VISIBLE);
                                mSignatureViewHolder.mItemSignatureHint.setVisibility(View.GONE);
                                mSignatureViewHolder.mItemSignature.setText(formatSignature.toString());
                            } else {
                                mSignatureViewHolder.mItemSignature.setVisibility(View.GONE);
                                mSignatureViewHolder.mItemSignatureHint.setVisibility(View.VISIBLE);
                                mSignatureViewHolder.mItemSignatureHint.setText(mContext.getString(R.string.exp_self_info_signature_hint));
                            }
                        }

                        @Override
                        public void onFail() {

                        }
                    });
                }
                ExpJDMAUtil.onSelfInfoEventClick("工作签名");
            }
        });

        updateSignature();
    }

    private void bindTagsItem(@NonNull final SelfInfoAdapter.ViewHolder holder, final UserInfoFieldSet.UserInfoField uif) {
        holder.mItemNameTv.setText(uif.fieldLabel);
        holder.mItemButton.setVisibility(TextUtils.isEmpty(uif.deeplink) ? View.GONE : View.VISIBLE);
        holder.mItemBackGround.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(uif.deeplink)) {
                    Router.build(uif.deeplink).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onSelfInfoEventClick("个性标签");
                }
            }
        });

        List<String> tagList = new ArrayList<>();
        if (uif.fieldValue instanceof List) {
            tagList = (List<String>)uif.fieldValue;
        }
        if (!tagList.isEmpty()) {
            holder.mItemTagsFlow.setVisibility(View.VISIBLE);
            holder.mItemTagsHint.setVisibility(View.GONE);

        } else {
            holder.mItemTagsFlow.setVisibility(View.GONE);
            holder.mItemTagsHint.setVisibility(View.VISIBLE);
            holder.mItemTagsHint.setText(uif.placeholder);
        }

        holder.mItemTagsFlow.setAdapter(new TagAdapter<String>(tagList) {
            @Override
            public View getView(FlowLayout parent, int position, String tag) {
                View tagView = LayoutInflater.from(mContext).inflate(R.layout.jdme_self_info_tag_view_item, parent, false);
                TextView tvTag = tagView.findViewById(R.id.tvTag);
                if (tvTag != null) {
                    tvTag.setText(tag);
                }
                return tagView;
            }
        });
        holder.mItemTagsFlow.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                if (!TextUtils.isEmpty(uif.deeplink)) {
                    Router.build(uif.deeplink).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onSelfInfoEventClick("个性标签");
                }
                return false;
            }
        });
    }

    private void bindQrCodeItem(@NonNull final SelfInfoAdapter.ViewHolder holder, UserInfoFieldSet.UserInfoField uif){
        holder.mItemNameTv.setText(uif.fieldLabel);
        holder.mItemBackGround.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //我的二维码
                Router.build(DeepLink.MY_SAAS_QR_CARD).go(mContext);
            }
        });
    }

    public void updateAvatar(String url) {
        mUserInfoData.avatar = url;
        if (mAvatarViewHolder != null) {
            ImageLoader.load(mContext, mAvatarViewHolder.mItemAvatar, url, R.drawable.profile_section_avatar_default);
        }
    }

    public void updatePendant() {
        if (mAvatarViewHolder == null) {
            return;
        }
        String pendantSmall = imDdService.getPendan();
        if (!TextUtils.isEmpty(pendantSmall)) {
            ImageLoader.load(mContext, mAvatarViewHolder.mItemPendant, pendantSmall);
        } else {
            imDdService.getPendanByNet(new Callback<String>() {
                @Override
                public void onSuccess(String str) {
                    if (!TextUtils.isEmpty(str)) {
                        ImageLoader.load(mContext, mAvatarViewHolder.mItemPendant, str);
                    } else {
                        mAvatarViewHolder.mItemPendant.setImageResource(0);
                    }
                }

                @Override
                public void onFail() {

                }
            });
        }
    }

    public void updateMobile(String phone) {
        mUserInfoData.mobile = phone;
        if (mMobileViewHolder != null) {
            mMobileViewHolder.mItemTextValue.setText(phone);
        }
    }

    public void updateTelephone(String telephone) {
        mUserInfoData.telephone = telephone;
        if (mTelephoneViewHolder != null) {
            mTelephoneViewHolder.mItemTextValue.setText(telephone);
        }
    }

    public void updateSignature() {
        if (mSignatureViewHolder != null) {
            CharSequence text = imDdService.getMyFormatSignature();
            if (!TextUtils.isEmpty(text)) {
                mSignatureViewHolder.mItemSignature.setVisibility(View.VISIBLE);
                mSignatureViewHolder.mItemSignatureHint.setVisibility(View.GONE);
                mSignatureViewHolder.mItemSignature.setText(text.toString());
            } else {
                mSignatureViewHolder.mItemSignature.setVisibility(View.GONE);
                mSignatureViewHolder.mItemSignatureHint.setVisibility(View.VISIBLE);
                mSignatureViewHolder.mItemSignatureHint.setText(mContext.getString(R.string.exp_self_info_signature_hint));
            }
        }
    }

    private PopupWindow mCopyPopup;
    
    private void popupCopyText(final TextView textView, final String fieldLabel) {
        LayoutInflater layoutInflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View popupView = layoutInflater.inflate(R.layout.jdme_self_info_popup_copy_layout, null);
        mCopyPopup = new PopupWindow(mContext);
        mCopyPopup.setContentView(popupView);
        mCopyPopup.setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
        mCopyPopup.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        mCopyPopup.setBackgroundDrawable(null);
        mCopyPopup.setFocusable(false);
        mCopyPopup.setOutsideTouchable(true);
        mCopyPopup.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                textView.setBackgroundColor(mContext.getResources().getColor(android.R.color.transparent));
            }
        });
        popupView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ClipboardManager clipboardManager = (ClipboardManager) mContext.getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clip = ClipData.newPlainText("simple text", textView.getText().toString().trim());
                clipboardManager.setPrimaryClip(clip);
                mCopyPopup.dismiss();
                onClickEvent(fieldLabel);
            }
        });
        int[] location = new int[2];
        textView.getLocationInWindow(location);
        int x = location[0] + textView.getMeasuredWidth() / 2 - mContext.getResources().getDimensionPixelSize(R.dimen.self_info_copy_popup_width) / 2;
        int y = location[1] - mContext.getResources().getDimensionPixelSize(R.dimen.self_info_copy_popup_height);
        mCopyPopup.showAtLocation(textView, Gravity.NO_GRAVITY, x, y);
        textView.setBackgroundColor(Color.parseColor("#E6E6E6"));
    }
}
