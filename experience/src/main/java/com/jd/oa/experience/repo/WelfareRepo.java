package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.WelfareData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class WelfareRepo {
    private static final String WELFARE_CACHE_KEY = "exp.welfare.repo.cache.key";
    private static WelfareRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static WelfareRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new WelfareRepo(context);
        }
        return sInstance;
    }

    private WelfareRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public WelfareData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), WELFARE_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        WelfareData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<WelfareData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(WelfareData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), WELFARE_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getWelfareData(final LoadDataCallback<WelfareData> callback) {
        Log.i("zhn", "getWelfareData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<WelfareData> response = ApiResponse.parse(info.result, new TypeToken<WelfareData>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.getWelfareData");
    }
}
/*
{
	"errorCode": "0",
	"errorMsg": "",
	"content": {
		"welfareItemCount": "25",
		"welfareDetailUrl": "jdme://abc",
		"account": {
			"url": "jdme://abc",
			"total": 9816800,
			"walletBalance": 0,
			"yiktongBalance": 9816800
		},
		"coupons": {
			"url": "jdme://abc",
			"list": [{
					"couponTypeName": "东券",
					"couponTypeCode": "01",
					"value": 600,
					"unit": "元"
				},
				{
					"couponTypeName": "快递券",
					"couponTypeCode": "02",
					"value": 16,
					"unit": "张"
				}
			]
		},
		"holiday": {
			"detailPageUrl": "jdme://abc",
			"applyPageUrl": "jdme://abc",
			"surplus": "88"
		},
		"scores": {
			"url": "jdme://abc",
			"balance": 618
		}
	}
}
* */
