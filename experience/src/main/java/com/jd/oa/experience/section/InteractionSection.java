package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.InteractionData;
import com.jd.oa.experience.repo.InteractionRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class InteractionSection extends StatelessSection implements Destroyable {

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private InteractionSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private InteractionRepo repo;
    private InteractionData mData;

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                case Constants.ACTION_HOMEPAGE_UPDATE_LIKED:
                case Constants.ACTION_HOMEPAGE_FINISH:
                    loadData();
                    break;
                case Constants.ACTION_HOMEPAGE_CLICK_LIKED:
                    String likeCount = intent.getStringExtra("likeCount");
                    String likedCount = intent.getStringExtra("likedCount");
                    if (mItemViewHolder != null) {
                        updateSocial(likeCount, likedCount);
                    }
                    break;
                default:
                    break;
            }
        }
    };

    public InteractionSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_interaction)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = InteractionRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_CLICK_LIKED);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_LIKED);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_FINISH);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        InteractionData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getInteractionData(new LoadDataCallback<InteractionData>() {
            @Override
            public void onDataLoaded(InteractionData interactionData) {
                showData(interactionData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(InteractionData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new InteractionSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mTitleView.setTitle(false, mData.icon, mData.title, mData.jumpUrl, mData.jumpText);
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        });

        final InteractionData.Social social = mData.items;

        //获赞
        if (social != null && social.liked != null) {
            mItemViewHolder.mLikedCountTv.setText(TextUtils.isEmpty(social.liked.count) ? "0" : social.liked.count);
            mItemViewHolder.mLikedRedDot.setVisibility(social.liked.hasNew ? View.VISIBLE : View.GONE);
        } else {
            mItemViewHolder.mLikedCountTv.setText("0");
            mItemViewHolder.mLikedRedDot.setVisibility(View.GONE);
        }
        mItemViewHolder.mLikedLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (social != null && social.liked != null && !TextUtils.isEmpty(social.liked.url)) {
                    Uri uri = Uri.parse(social.liked.url);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.InteractionInfo.LIKED.keyName, ExpJDMAConstants.InteractionInfo.LIKED.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INTERACTION, map);
                }
            }
        });

        //点赞
        if (social != null && social.like != null && !TextUtils.isEmpty(social.like.count)) {
            mItemViewHolder.mLikeCountTv.setText(TextUtils.isEmpty(social.like.count) ? "0" : social.like.count);
            mItemViewHolder.mLikeRedDot.setVisibility(social.like.hasNew ? View.VISIBLE : View.GONE);
        } else {
            mItemViewHolder.mLikeCountTv.setText("0");
        }
        mItemViewHolder.mLikeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (social != null && social.like != null && !TextUtils.isEmpty(social.like.url)) {
                    Uri uri = Uri.parse(social.like.url);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.InteractionInfo.LIKE.keyName, ExpJDMAConstants.InteractionInfo.LIKE.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INTERACTION, map);
                }
            }
        });

        //个人主页入口
        if (social != null && social.homepage != null && !TextUtils.isEmpty(social.homepage.count)) {
            mItemViewHolder.mHomeCountTv.setText(TextUtils.isEmpty(social.homepage.count) ? "0" : social.homepage.count);
            mItemViewHolder.mHomeRedDot.setVisibility(social.homepage.hasNew ? View.VISIBLE : View.GONE);
        } else {
            mItemViewHolder.mHomeCountTv.setText("0");
        }
        mItemViewHolder.mHomeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
/*                if (social == null || social.homepage == null ||TextUtils.isEmpty(social.homepage.url)) {
                    return;
                }*/
                //jdme://jm/biz/myspace?mparam={"userName":""}
                String payload = "{\"userName\":\"" + PreferenceManager.UserInfo.getUserName() + "\"}";
                Router.build(DeepLink.EXP_MY_JOYSPACE + "?mparam=" + Uri.encode(payload)).go(mContext, new RouteNotFoundCallback(mContext));
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.InteractionInfo.HOMEPAGE.keyName, ExpJDMAConstants.InteractionInfo.HOMEPAGE.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INTERACTION, map);
            }
        });
    }

    public void updateSocial(String likeCount, String likedCount) {
        if (!TextUtils.isEmpty(likeCount)) {
            mItemViewHolder.mLikeCountTv.setText(likeCount);
            mItemViewHolder.mLikedCountTv.setText(likedCount);
        }
        if (!TextUtils.isEmpty(likedCount)) {
            mItemViewHolder.mLikeCountTv.setText(likeCount);
            mItemViewHolder.mLikedCountTv.setText(likedCount);
            if (!TextUtils.equals(mItemViewHolder.mLikeCountTv.getText().toString().trim(), likeCount)) {
                mItemViewHolder.mLikeRedDot.setVisibility(View.VISIBLE);
            }
            if (!TextUtils.equals(mItemViewHolder.mLikedCountTv.getText().toString().trim(), likedCount)) {
                mItemViewHolder.mLikedRedDot.setVisibility(View.VISIBLE);
            }
        }
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public TitleView mTitleView;
        public TextView mLikeCountTv;
        public View mLikeRedDot;
        public TextView mLikedCountTv;
        public View mLikedRedDot;
        public TextView mHomeCountTv;
        public View mHomeRedDot;
        public View mLikeLayout;
        public View mLikedLayout;
        public View mHomeLayout;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mTitleView = itemView.findViewById(R.id.title_view);
            mLikeCountTv = itemView.findViewById(R.id.like_count_tv);
            mLikeRedDot = itemView.findViewById(R.id.like_red_dot);
            mLikedCountTv = itemView.findViewById(R.id.liked_count_tv);
            mLikedRedDot = itemView.findViewById(R.id.liked_red_dot);
            mHomeCountTv = itemView.findViewById(R.id.home_count_tv);
            mHomeRedDot = itemView.findViewById(R.id.home_red_dot);
            mLikeLayout = itemView.findViewById(R.id.like_layout);
            mLikedLayout = itemView.findViewById(R.id.liked_layout);
            mHomeLayout = itemView.findViewById(R.id.home_layout);
        }
    }
}
