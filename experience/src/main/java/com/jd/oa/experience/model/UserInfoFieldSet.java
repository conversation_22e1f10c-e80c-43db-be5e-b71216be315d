package com.jd.oa.experience.model;

import java.util.List;

//旧版的返回数据结构，提测前突然间改了返回内容，不再使用新的数据结构，避免大量修改业务逻辑
//将新数据结构转换成这个
public class UserInfoFieldSet {

    public List<UserInfoField> fieldset;

    public static class UserInfoField {
        public String fieldId;
        public String fieldType;
        public String fieldLabel;
        public boolean multiLine;
        public Object fieldValue;
        public String placeholder;
        public String deeplink;

        public UserInfoField(String fieldId, String fieldType, String fieldLabel, Object fieldValue) {
            this.fieldId = fieldId;
            this.fieldType = fieldType;
            this.fieldLabel = fieldLabel;
            this.fieldValue = fieldValue;
            this.multiLine = false;
            this.placeholder = "";
            this.deeplink = "";
        }
    }
}
