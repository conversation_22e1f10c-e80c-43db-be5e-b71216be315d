package com.jd.oa.experience.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

public class TabFragmentRecyclerView extends RecyclerView {

    public TabFragmentRecyclerView(@NonNull Context context) {
        super(context);
        init();
    }

    public TabFragmentRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public TabFragmentRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setScrollingTouchSlop(RecyclerView.TOUCH_SLOP_PAGING);
    }

    @Override
    public boolean fling(int velocityX, int velocityY) {
        velocityY *= 0.7;
        return super.fling(velocityX, velocityY);
    }
}
