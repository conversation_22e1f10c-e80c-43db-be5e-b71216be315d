package com.jd.oa.experience.model;

import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.preference.PreferenceManager;

public class ExpCommonEyeCache {
    private static ExpCommonEyeCache sInstance;

    public static ExpCommonEyeCache getInstance() {
        if (sInstance == null) {
            sInstance = new ExpCommonEyeCache();
        }
        return sInstance;
    }

    public boolean getCache(String key) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName()+"_info", key, null);

        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return false;
        }
        boolean isOpen = false;
        try {
            isOpen = Boolean.parseBoolean(cache.getResponse());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isOpen;
    }

    public void addCache(String key, boolean isOpen) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName()+"_info", key, null, String.valueOf(isOpen));
    }



}
