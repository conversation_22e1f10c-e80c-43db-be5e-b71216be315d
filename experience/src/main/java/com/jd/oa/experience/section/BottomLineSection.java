package com.jd.oa.experience.section;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;

import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class BottomLineSection extends StatelessSection implements Destroyable {
    private Context mContext;
    private String mDesc;
    private SectionedRecyclerViewAdapter mAdapter;
    private BottomLineSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private boolean showEmpty = true;
    private boolean showBottom = false;

    public BottomLineSection(Context context, SectionedRecyclerViewAdapter adapter, String desc) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_bottom_line)
                .build());
        mContext = context;
        mAdapter = adapter;
        mDesc = desc;
    }

    public void setDesc(String desc) {
        mDesc = desc;
        if (mItemViewHolder != null) {//如果没有滚动到底部，mItemViewHolder为null
            if (!TextUtils.isEmpty(mDesc)) {
                mItemViewHolder.mLineView.setVisibility(View.VISIBLE);
                mItemViewHolder.mDescTv.setVisibility(View.VISIBLE);
                mItemViewHolder.mDescTv.setText(mDesc);
            } else {
                mItemViewHolder.mLineView.setVisibility(View.GONE);
                mItemViewHolder.mDescTv.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new BottomLineSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        mItemViewHolder.mEmptyLayout.setVisibility(showEmpty ? View.VISIBLE : View.GONE);
        mItemViewHolder.mBottomLayout.setVisibility(showBottom ? View.VISIBLE : View.GONE);
        if (!TextUtils.isEmpty(mDesc)) {
            mItemViewHolder.mLineView.setVisibility(View.VISIBLE);
            mItemViewHolder.mDescTv.setVisibility(View.VISIBLE);
            mItemViewHolder.mDescTv.setText(mDesc);
        } else {
            mItemViewHolder.mLineView.setVisibility(View.GONE);
            mItemViewHolder.mDescTv.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    public void showBottom() {
        showEmpty = false;
        showBottom = true;
        if (mItemViewHolder != null && mItemViewHolder.mBottomLayout != null && mItemViewHolder.mEmptyLayout != null) {
            mItemViewHolder.mBottomLayout.setVisibility(View.VISIBLE);
            mItemViewHolder.mEmptyLayout.setVisibility(View.GONE);
        }
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {
        public View mLineView;
        public TextView mDescTv;
        public View mBottomLayout;
        public View mEmptyLayout;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mLineView = itemView.findViewById(R.id.bottom_line_bg);
            mDescTv = itemView.findViewById(R.id.bottom_line_tv);
            mBottomLayout = itemView.findViewById(R.id.bottom_line_layout);
            mEmptyLayout = itemView.findViewById(R.id.empty_tab_layout);
        }
    }
}
