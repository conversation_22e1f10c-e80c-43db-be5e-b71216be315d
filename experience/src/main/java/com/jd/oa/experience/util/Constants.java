package com.jd.oa.experience.util;

public class Constants {
    public static final String ACTION_REFRESH_EXP_SECTIONS = "com.jd.oa.experience.REFRESH_EXP_SECTIONS";
    public static final String ACTION_REFRESH_EXP_FROM_TAB = "com.jd.oa.experience.REFRESH_EXP_FROM_TAB";
    public static final String ACTION_UPDATE_TAGS = "JSSKD_EVENT_EXP_LABEL_PAGE_UPDATE";
    public static final String ACTION_SHOW_ANNIVERSARY = "com.jd.oa.experience.SHOW_ANNIVERSARY";
    public static final String ACTION_SCREEN_WIDTH_CHANGE = "com.jd.oa.experience.SCREEN_WIDTH_CHANGE";
    public static final String ACTION_EXP_ON_RESUME = "com.jd.oa.experience.on.resume";
    public static final String ACTION_EXP_ON_PAUSE = "com.jd.oa.experience.on.pause";
    public static final String ACTION_REPORT_CHRONICLE_SCROLL = "com.jd.oa.experience.report.chronicle.scroll";

    public static final String ACTION_SECTION_VISIBLE = "com.jd.oa.experience.ACTION_SECTION_VISIBLE";

    public static final String ACTION_EXP_PROFILE_REFRESH_SIGNATURE_NEW = "action.exp.profile.refresh.signature.new";
    public static final String ACTION_EXP_PROFILE_OPEN_QRCODE = "action.exp.profile.open.qrcode";

    public static final String ACTION_HOMEPAGE_UPDATE_LIKED = "JSSKD_EVENT_EXP_LIKED_PAGE_UPDATE";
    public static final String ACTION_HOMEPAGE_CLICK_LIKED = "ACTION_EXP_HOMEPAGE_CLICK_LIKED";
    public static final String ACTION_HOMEPAGE_UPDATE_BADGES = "JSSKD_EVENT_EXP_MEDAL_PAGE_UPDATE";
    public static final String ACTION_STUDY_MAP_UPDATE = "JSSKD_EVENT_EXP_STUDY_MAP_UPDATE";
    public static final String ACTION_HOMEPAGE_UPDATE_WORK_CARD = "ACTION_EXP_HOMEPAGE_UPDATE_WORK_CARD";
    public static final String ACTION_HOMEPAGE_UPDATE_OKR_CARD = "ACTION_EXP_HOMEPAGE_UPDATE_OKR_CARD";
    public static final String JSSDK_EVENT_EXP_OBJECTIVE_NUM_UPDATE = "JSSDK_EVENT_EXP_OBJECTIVE_NUM_UPDATE";
    public static final String ACTION_HOMEPAGE_FINISH = "ACTION_HOMEPAGE_FINISH";

    public static final String ACTION_UPDATE_INSTRUCTION_STATE = "com.jd.oa.experience.ACTION_UPDATE_INSTRUCTION_STATE";

    public static final String ACTION_EXP_COMMON_ON_RESUME = "intent.filter.action.exp.common.on.resume";
    public static final String ACTION_EXP_COMMON_ON_PAUSE = "intent.filter.action.exp.common.on.pause";

    public static final String ACTION_EXP_APP_ON_RESUME = "intent.filter.action.exp.app.on.resume";
    public static final String ACTION_EXP_APP_ON_PAUSE = "intent.filter.action.exp.app.on.pause";

    public static final String AIEMPLOYEE_CACHE_KEY = "exp.aiemployee.repo.cache.key";
    public static final String AIEMPLOYEE_SECTION_API = "exp.v3.getDigitalEmployeeInfo";
    public static final String JOYHR_CACHE_KEY = "exp.joyhr.repo.cache.key";
    public static final String JOYHR_SECTION_API = "exp.v3.getHrAppList";
    public static final String RECOM_COURSE_CACHE_KEY = "exp.recomcourse.repo.cache.key";
    public static final String RECOM_COURSE_SECTION_API = "getMobileRecommend";
    public static final String MY_EVENT_V3_CACHE_KEY = "exp.banner.repo.memorabilia.cache.key";
    public static final String MY_EVENT_V3_SECTION_API = "exp.v3.getMyEvent";
    public static final String WELFARE_CACHE_KEY = "exp.welfarev3.repo.cache.key";
    public static final String SERVICE_CACHE_KEY = "exp.servicev3.repo.cache.key";
    public static final String WELFARE_SECTION_API = "exp.v3.getWelfareData";
    public static final String SERVICE_SECTION_API = "exp.v3.getServiceData";

}
