package com.jd.oa.experience.section;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.experience.R;
import com.jd.oa.experience.activity.HomePageDutyActivity;
import com.jd.oa.experience.activity.MyHomePageActivity;
import com.jd.oa.experience.activity.MyInstructionActivity;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.dialog.ExpDialog;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.ManualData;
import com.jd.oa.experience.repo.ManualRepo;
import com.jd.oa.experience.repo.MyInstructionRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;
import com.zhy.view.flowlayout.TagFlowLayout;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.appcompat.widget.SwitchCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class ManualSection extends StatelessSection implements Destroyable {

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private ManualRepo repo;
    private ManualData mData;
    private final MyInstructionRepo mInstructionRepo = new MyInstructionRepo();

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                case Constants.ACTION_UPDATE_INSTRUCTION_STATE:
                case Constants.ACTION_HOMEPAGE_UPDATE_WORK_CARD:
                case Constants.JSSDK_EVENT_EXP_OBJECTIVE_NUM_UPDATE:
                    loadData();
                default:
                    break;
            }
        }
    };

    private CompoundButton.OnCheckedChangeListener mListener = new CompoundButton.OnCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            if (mData != null && mData.items != null && mData.items.manual != null && mData.items.manual.content != null) {
                showConfirmDialog(isChecked);
            }
        }
    };

    public ManualSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_manual)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = ManualRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        intentFilter.addAction(Constants.ACTION_UPDATE_INSTRUCTION_STATE);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_WORK_CARD);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_OKR_CARD);
        intentFilter.addAction(Constants.JSSDK_EVENT_EXP_OBJECTIVE_NUM_UPDATE);

        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        ManualData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getManualData(new LoadDataCallback<ManualData>() {
            @Override
            public void onDataLoaded(ManualData manualData) {
                showData(manualData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(ManualData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    @SuppressLint("SetTextI18n")
    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mTitleView.setTitle(false, mData.icon, mData.title, mData.jumpUrl, mData.jumpText);
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        });

        final ManualData.Manual manual;
        final ManualData.Duty duty;
        final ManualData.OKR okr;
        if (mData.items != null) {
            manual = mData.items.manual;
            duty = mData.items.responsibility;
            okr = mData.items.aim;
        } else {
            manual = null;
            duty = null;
            okr = null;
        }

        //okr
        final Integer okrNum;
        final boolean isGrayUser;
        if (okr != null && okr.content != null) {
            okrNum = okr.content.targetNum;
            isGrayUser = okr.content.toIsGray != null && okr.content.toIsGray;
        } else {
            okrNum = null;
            isGrayUser = false;
        }

        mItemViewHolder.mOkrCard.setVisibility(isGrayUser ? View.VISIBLE : View.GONE);
        if (okrNum != null && okrNum > 0) {
            mItemViewHolder.mOkrNumTv.setText(okrNum.toString());
            mItemViewHolder.mOkrSuffix.setText(mContext.getResources().getQuantityString(R.plurals.exp_homepage_okr_suffix, okrNum));
            mItemViewHolder.mOkrTv.setVisibility(View.VISIBLE);
            mItemViewHolder.mOkrBtn.setVisibility(View.VISIBLE);
            mItemViewHolder.mOkrEmpty.setVisibility(View.GONE);
            mItemViewHolder.mOkrEditableTv.setVisibility(View.VISIBLE);
        } else {
            mItemViewHolder.mOkrNumTv.setText("");
            mItemViewHolder.mOkrSuffix.setText("");
            mItemViewHolder.mOkrTv.setVisibility(View.GONE);
            mItemViewHolder.mOkrBtn.setVisibility(View.GONE);
            mItemViewHolder.mOkrEmpty.setVisibility(View.VISIBLE);
            mItemViewHolder.mOkrEditableTv.setVisibility(View.GONE);
        }
        NoDoubleClickListener goToJoyWork = new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if ((okrNum == null || okrNum <= 0) && !isGrayUser) {
                    return;
                }
                JSONObject jsonObject = new JSONObject();
                try {
                    String xingyunObjectAppId= LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getXingyunObjectiveAppId();
                    String xingyunObjectiveUrl=LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getXingyunObjectiveUrl();
                    String url = xingyunObjectiveUrl +"&erp="+ PreferenceManager.UserInfo.getUserName();
                    jsonObject.put("appId", xingyunObjectAppId);
                    jsonObject.put("url", url);
                    jsonObject.put("isNativeHead", "0");
                    jsonObject.put("SafeArea", "0");
                    String deepLink = DeepLink.BROWSER + "?mparam=" + Uri.encode(jsonObject.toString());
                    Router.build(deepLink).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_OKR, new HashMap<String, String>());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        };
        mItemViewHolder.mOkrCard.setOnClickListener(goToJoyWork);
        mItemViewHolder.mOkrAdd.setOnClickListener(goToJoyWork);

        //工作职责
        final List<String> businessTags = new ArrayList<>();
        if (duty != null && duty.content != null) {
            for (ManualData.Duty.Content content : duty.content) {
                if ((TextUtils.equals(content.questionCode, "Q100101") || TextUtils.equals(content.questionCode, "Q100102")) &&
                        content.answerKeyword != null) {//Q100101:主要负责的业务/产品线名称，Q100102:重点工作内容
                    businessTags.addAll(content.answerKeyword);
                }
            }
        }

        mItemViewHolder.mDutyBusinessTags.setVisibility(!businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mItemViewHolder.mDutyEmpty.setVisibility(businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mItemViewHolder.mDutyAdd.setVisibility(businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mItemViewHolder.mDutyEditableTv.setVisibility(!businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mItemViewHolder.mDutyBusinessTags.setAdapter(new TagAdapter<String>(businessTags) {
            @Override
            public View getView(FlowLayout parent, int position, String tag) {
                View tagView = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_experience_section_manual_duty_tag, parent, false);
                TextView tagTv = tagView.findViewById(R.id.tag_tv);
                if (tagTv != null) {
                    tagTv.setText(tag);
                }
                return tagView;
            }
        });
        final String cardId = duty != null ? duty.cardId : "";
        mItemViewHolder.mDutyBusinessTags.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                startDutyActivity(cardId);
                return true;
            }
        });
        mItemViewHolder.mDutyAdd.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                startDutyActivity(cardId);
            }
        });
        mItemViewHolder.mDutyCard.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                startDutyActivity(cardId);
            }
        });

        //说明书
        boolean isEmpty = manual == null || manual.content == null || TextUtils.isEmpty(manual.content.openUrl) || Build.VERSION.SDK_INT < 28;
        mItemViewHolder.mManualShow.setVisibility(!isEmpty ? View.VISIBLE : View.GONE);
        mItemViewHolder.mManualEmpty.setVisibility(isEmpty ? View.VISIBLE : View.GONE);
        mItemViewHolder.mManualEmptyTv.setText(Build.VERSION.SDK_INT >= 28 ? R.string.exp_homepage_empty_instruction_mine : R.string.exp_homepage_instruction_low_version);
        mItemViewHolder.mManualAdd.setVisibility(Build.VERSION.SDK_INT >= 28 ? View.VISIBLE : View.INVISIBLE);
        mItemViewHolder.mManualAdd.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                Intent intent = new Intent(mContext, MyInstructionActivity.class);
                mContext.startActivity(intent);

                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.ManualInfo.MANUAL_CREATE.keyName
                        , ExpJDMAConstants.ManualInfo.MANUAL_CREATE.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_MANUAL, map);
            }
        });
        mItemViewHolder.mShowEditableTv.setVisibility(!isEmpty ? View.VISIBLE : View.GONE);
        mItemViewHolder.mShowEditableTv.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if (manual != null && manual.content != null && !TextUtils.isEmpty(manual.content.openUrl)) {
                    Uri uri = Uri.parse(manual.content.openUrl);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(mContext, new RouteNotFoundCallback(mContext));

                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.ManualInfo.MANUAL_EDIT.keyName
                            , ExpJDMAConstants.ManualInfo.MANUAL_EDIT.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_MANUAL, map);
                }
            }
        });
        mItemViewHolder.mManualSwitch.setOnCheckedChangeListener(null);
        mItemViewHolder.mManualSwitch.setChecked(manual != null && manual.content != null && manual.content.isShow);
        mItemViewHolder.mManualSwitch.setOnCheckedChangeListener(mListener);

    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public TitleView mTitleView;
        private View mDutyCard;
        private TagFlowLayout mDutyBusinessTags;
        private View mOkrTv;
        private View mOkrBtn;
        private View mDutyEmpty;
        private View mOkrEmpty;
        private View mDutyAdd;
        private View mOkrAdd;
        private View mOkrCard;
        private View mDutyEditableTv;
        private View mOkrEditableTv;
        private TextView mOkrNumTv;
        private TextView mOkrSuffix;
        private View mManualShow;
        private SwitchCompat mManualSwitch;
        private View mManualEmpty;
        private TextView mManualEmptyTv;
        private View mManualAdd;
        private View mShowEditableTv;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mTitleView = itemView.findViewById(R.id.title_view);
            mDutyCard = itemView.findViewById(R.id.duty_card);
            mDutyBusinessTags = itemView.findViewById(R.id.duty_business_tag);
            mOkrTv = itemView.findViewById(R.id.okr_tv);
            mOkrBtn = itemView.findViewById(R.id.okr_btn);
            mDutyEmpty = itemView.findViewById(R.id.duty_empty);
            mOkrEmpty = itemView.findViewById(R.id.okr_empty);
            mOkrCard = itemView.findViewById(R.id.okr_card);
            mDutyAdd = itemView.findViewById(R.id.duty_add);
            mOkrAdd = itemView.findViewById(R.id.okr_add);
            mDutyEditableTv = itemView.findViewById(R.id.duty_editable_tv);
            mOkrEditableTv = itemView.findViewById(R.id.okr_editable_tv);
            mOkrNumTv = itemView.findViewById(R.id.okr_num);
            mOkrSuffix = itemView.findViewById(R.id.okr_suffix);

            mManualShow = itemView.findViewById(R.id.manual_show);
            mManualSwitch = itemView.findViewById(R.id.manual_switch);
            mManualEmpty = itemView.findViewById(R.id.manual_empty);
            mManualEmptyTv = itemView.findViewById(R.id.manual_empty_tv);
            mManualAdd = itemView.findViewById(R.id.manual_add);
            mShowEditableTv = itemView.findViewById(R.id.show_editable_tv);
        }
    }

    private void startDutyActivity(String cardId) {
        Intent intent = new Intent(mContext, HomePageDutyActivity.class);
        intent.putExtra(MyHomePageActivity.EXTRA_IS_SELF, true);
        intent.putExtra(MyHomePageActivity.EXTRA_CARD_ID, cardId);
        intent.putExtra(MyHomePageActivity.EXTRA_ERP, PreferenceManager.UserInfo.getUserName());
        mContext.startActivity(intent);
        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_DUTY, new HashMap<String, String>());
    }

    private void showConfirmDialog(final boolean isChecked) {
        int contentResId;
        int confirmResId;
        contentResId = isChecked ? R.string.exp_instruction_setting_hint_enable : R.string.exp_instruction_setting_hint_disable;
        confirmResId = isChecked ? R.string.exp_instruction_dialog_button_enable : R.string.exp_instruction_dialog_button_disable;
        ExpDialog dialog = new ExpDialog(mContext, contentResId, confirmResId);
        dialog.setOnClickListener(new ExpDialog.OnClickListener() {
            @Override
            public void onClickOk() {
                mInstructionRepo.setInstructionShow(isChecked, new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        mData.items.manual.content.isShow = isChecked;
                        repo.addCache(mData);

                        Map<String, String> map = new HashMap<>();
                        map.put(ExpJDMAConstants.ManualInfo.MANUAL_SWITCH.keyName
                                , ExpJDMAConstants.ManualInfo.MANUAL_SWITCH.KeyValue);
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_MANUAL, map);
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        if (mItemViewHolder != null && mItemViewHolder.mManualSwitch != null) {
                            mItemViewHolder.mManualSwitch.setOnCheckedChangeListener(null);
                            mItemViewHolder.mManualSwitch.setChecked(mData.items.manual.content.isShow);//开关复原
                            mItemViewHolder.mManualSwitch.setOnCheckedChangeListener(mListener);
                        }
                        Toast.makeText(mContext, R.string.exp_instruction_api_fail, Toast.LENGTH_SHORT).show();
                    }
                });
            }

            @Override
            public void onClickCancel() {
                if (mItemViewHolder != null && mItemViewHolder.mManualSwitch != null) {
                    mItemViewHolder.mManualSwitch.setOnCheckedChangeListener(null);
                    mItemViewHolder.mManualSwitch.setChecked(mData.items.manual.content.isShow);//防止开关复原时弹出对话框
                    mItemViewHolder.mManualSwitch.setOnCheckedChangeListener(mListener);
                }
            }
        });
        dialog.show();
    }
}
