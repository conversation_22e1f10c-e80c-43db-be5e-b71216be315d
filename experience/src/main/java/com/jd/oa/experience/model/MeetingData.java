package com.jd.oa.experience.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class MeetingData {


    @SerializedName("date")
    private long date;
    @SerializedName("participantAvg")
    private double participantAvg = -1d;
    @SerializedName("durationAvgLevel")
    private String durationAvgLevel;
    @SerializedName("meetingCountLevel")
    private String meetingCountLevel;
    @SerializedName("meetingEfficiency")
    private String meetingEfficiency;
    @SerializedName("icon")
    private String icon;
    @SerializedName("participantAvgLevel")
    private String participantAvgLevel;
    @SerializedName("suggest")
    private List<String> suggest;
    @SerializedName("title")
    private String title;
    @SerializedName("jumpUrl")
    private String jumpUrl;
    @SerializedName("tips")
    private String tips;
    @SerializedName("jumpText")
    private String jumpText;
    @SerializedName("durationAvg")
    private double durationAvg = -1d;
    @SerializedName("meetingCount")
    private double meetingCount = -1d;

    public long getDate() {
        return date;
    }

    public void setDate(long date) {
        this.date = date;
    }

    public double getParticipantAvg() {
        return participantAvg;
    }

    public void setParticipantAvg(double participantAvg) {
        this.participantAvg = participantAvg;
    }

    public String getDurationAvgLevel() {
        return durationAvgLevel;
    }

    public void setDurationAvgLevel(String durationAvgLevel) {
        this.durationAvgLevel = durationAvgLevel;
    }

    public String getMeetingCountLevel() {
        return meetingCountLevel;
    }

    public void setMeetingCountLevel(String meetingCountLevel) {
        this.meetingCountLevel = meetingCountLevel;
    }

    public String getMeetingEfficiency() {
        return meetingEfficiency;
    }

    public void setMeetingEfficiency(String meetingEfficiency) {
        this.meetingEfficiency = meetingEfficiency;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getParticipantAvgLevel() {
        return participantAvgLevel;
    }

    public void setParticipantAvgLevel(String participantAvgLevel) {
        this.participantAvgLevel = participantAvgLevel;
    }

    public List<String> getSuggest() {
        return suggest;
    }

    public void setSuggest(List<String> suggest) {
        this.suggest = suggest;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public String getJumpText() {
        return jumpText;
    }

    public void setJumpText(String jumpText) {
        this.jumpText = jumpText;
    }

    public double getDurationAvg() {
        return durationAvg;
    }

    public void setDurationAvg(double durationAvg) {
        this.durationAvg = durationAvg;
    }

    public double getMeetingCount() {
        return meetingCount;
    }

    public void setMeetingCount(double meetingCount) {
        this.meetingCount = meetingCount;
    }
}
