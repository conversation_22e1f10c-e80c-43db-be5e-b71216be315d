package com.jd.oa.experience.guide;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import com.bumptech.glide.Glide;
import com.jd.oa.AppBase;
import com.jd.oa.experience.R;
import com.jd.oa.utils.LocaleUtils;

public class ExpGuidePageAdapter extends PagerAdapter {


    Context mContext;

    public ExpGuidePageAdapter(Context context) {
        mContext = context;
    }

    @Override
    public int getCount() {
        return 2;
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object o) {
        return view == o;
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        View view;
        if (position == 0) {
            view = View.inflate(mContext, R.layout.jdme_item_exp_guide_page_step1, null);
            ImageView ivTitle1 = view.findViewById(R.id.iv_title1);
            if (TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN")) {
                Glide.with(mContext).load(R.drawable.exp_guide_title1).into(ivTitle1);
            } else {
                Glide.with(mContext).load(R.drawable.exp_guide_title1_en).into(ivTitle1);
            }
            view.findViewById(R.id.tv_skip).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onContentClickListener != null) {
                        onContentClickListener.onSkip();
                    }
                }
            });
            view.findViewById(R.id.tv_sure).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onContentClickListener != null) {
                        onContentClickListener.onNext();
                    }
                }
            });
        } else {
            view = View.inflate(mContext, R.layout.jdme_item_exp_guide_page_step2, null);
            ImageView ivTitle2 = view.findViewById(R.id.iv_title2);
            if (TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN")) {
                Glide.with(mContext).load(R.drawable.exp_guide_title_2).into(ivTitle2);
            } else {
                Glide.with(mContext).load(R.drawable.exp_guide_title2_en).into(ivTitle2);
            }
            view.findViewById(R.id.tv_start).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onContentClickListener != null) {
                        onContentClickListener.onFinish();
                    }
                }
            });
        }
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }


    public interface OnContentClickListener {
        void onNext();

        void onSkip();

        void onFinish();
    }

    OnContentClickListener onContentClickListener;

    public void setOnContentClickListener(OnContentClickListener onContentClickListener) {
        this.onContentClickListener = onContentClickListener;
    }
}
