package com.jd.oa.experience.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.util.List;

@Deprecated
@Keep
public class ExpAppData {

    @SerializedName("content")
    private List<Apps> content;

    public List<Apps> getContent() {
        return content;
    }

    public void setContent(List<Apps> content) {
        this.content = content;
    }

    public static class Apps {
        @SerializedName("badge")
        private String badge;
        @SerializedName("subTitle")
        private SubTitle subTitle;
        @SerializedName("icon")
        private String icon;
        @SerializedName("title")
        private String title;
        @SerializedName("url")
        private String url;
        @SerializedName("id")
        private String id;
        private boolean placeHolder;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getBadge() {
            return badge;
        }

        public void setBadge(String badge) {
            this.badge = badge;
        }

        public SubTitle getSubTitle() {
            return subTitle;
        }

        public void setSubTitle(SubTitle subTitle) {
            this.subTitle = subTitle;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public void setPlaceHolder(boolean placeHolder) {
            this.placeHolder = placeHolder;
        }

        public boolean isPlaceHolder() {
            return placeHolder;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public static class SubTitle {
            @SerializedName("color")
            private String color;
            @SerializedName("text")
            private String text;

            public String getColor() {
                return color;
            }

            public void setColor(String color) {
                this.color = color;
            }

            public String getText() {
                return text;
            }

            public void setText(String text) {
                this.text = text;
            }
        }
    }
}
