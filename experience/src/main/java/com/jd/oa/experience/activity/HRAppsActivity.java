package com.jd.oa.experience.activity;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_JoyHR_search;
import android.graphics.PointF;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.android.material.tabs.TabLayout;
import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.CategoryAdapter;
import com.jd.oa.experience.model.HRAppsData;
import com.jd.oa.experience.repo.JoyHRAppsRepo;
import com.jd.oa.experience.viewmodel.HRAppsViewModel;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StatusBarConfig;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.ArrayList;
import java.util.List;

@Route(DeepLink.JOYHR_APPS)
public class HRAppsActivity extends BaseActivity {

    private ImageView mBackImageView;//返回按钮的箭头
    private ImageView mSearchImageView;//搜索栏
    private TabLayout mCategoryTabLayout;//TabLayout
    private RecyclerView mAllAppRecyclerView;//所有工具rv
    private CategoryAdapter mCategoryAdapter;
    private RecyclerView.OnScrollListener mOnScrollListener;
    private RecyclerView.SmoothScroller mSmoothScroller;
    private View mNormalModeBar;//正常的标题栏
    private HRAppsViewModel mViewModel;

    private boolean mIsScroll;
    private boolean mSmoothScrolling = false;
    private boolean mInit;

    private JoyHRAppsRepo mRepo;
    private List<HRAppsData.Category> mHRAppCategoryList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_hr_apps);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }
        mRepo = JoyHRAppsRepo.get(this);
        if (mRepo != null) {
            mViewModel = new ViewModelProvider(this).get(HRAppsViewModel.class);
            mViewModel.init(mRepo);
            initView();
            mViewModel.loadHRAppsData();
        }
    }

    private void initView() {
        mBackImageView = findViewById(R.id.iv_me_back);
        mSearchImageView = findViewById(R.id.iv_search);
        mAllAppRecyclerView = findViewById(R.id.rv_all_hr_apps);
        mNormalModeBar = findViewById(R.id.rl_toolbar_normal);
        mNormalModeBar.setVisibility(View.VISIBLE);
        mAllAppRecyclerView = findViewById(R.id.rv_all_hr_apps);

        if (StatusBarConfig.enableImmersive() && !isFinishing() && !isDestroyed()) {
            QMUIStatusBarHelper.translucent(this);
            mNormalModeBar.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(this), 0, 0);
        }

        mHRAppCategoryList = new ArrayList<>();
        mCategoryAdapter = new CategoryAdapter(mHRAppCategoryList);
        final LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        mAllAppRecyclerView.setLayoutManager(layoutManager);
        mAllAppRecyclerView.setAdapter(mCategoryAdapter);

        mSmoothScroller = new LinearSmoothScroller(this) {
            @Override
            protected int getVerticalSnapPreference() {
                return LinearSmoothScroller.SNAP_TO_START;
            }

            @Nullable
            @Override
            public PointF computeScrollVectorForPosition(int targetPosition) {
                if (layoutManager == null || isFinishing() || isDestroyed()) {
                    return null;
                }
                return layoutManager.computeScrollVectorForPosition(targetPosition);
            }

            @Override
            protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                return displayMetrics != null ? (0.05f / displayMetrics.density) : 0.1f;
            }

        };
        mCategoryTabLayout = findViewById(R.id.tl_category);
        mCategoryTabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);//设置tab模式
        mCategoryTabLayout.setSelectedTabIndicatorColor(ContextCompat.getColor(this, R.color.white_light_gray_f8f8f9));//设置选中的tab颜色
        mCategoryTabLayout.setTabTextColors(
            ContextCompat.getColor(this, R.color.color_62656D), 
            ContextCompat.getColor(this, R.color.me_app_market_tab_text_select)
        );
        mCategoryTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab == null || tab.getCustomView() == null || isFinishing() || isDestroyed()) {
                    return;
                }
                try {
                    TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);//tab的文字
                    View view = tab.getCustomView().findViewById(R.id.view_indicator);//红色下划线
                    if (tabTextView != null && view != null) {
                        view.setVisibility(View.VISIBLE);
                        tabTextView.setTypeface(null, Typeface.BOLD);
                        tabTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                        tabTextView.setTextColor(ContextCompat.getColor(HRAppsActivity.this, R.color.me_app_market_tab_text_select));
                    }

                    if (mInit || mIsScroll || mHRAppCategoryList == null || mSmoothScroller == null || 
                        layoutManager == null || mAllAppRecyclerView == null) {
                        return;
                    }

                    int position = tab.getPosition();
                    if (position >= 0 && position < mHRAppCategoryList.size()) {
                        mSmoothScroller.setTargetPosition(position);
                        layoutManager.startSmoothScroll(mSmoothScroller);
                    }
                } catch (Exception e) {
                    MELogUtil.localE("HRAppsActivity", "onTabSelected error", e);
                    e.printStackTrace();
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                if (tab == null || tab.getCustomView() == null) return;
                try {
                    TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);
                    View view = tab.getCustomView().findViewById(R.id.view_indicator);
                    if (tabTextView != null && view != null) {
                        tabTextView.setTypeface(null, Typeface.NORMAL);
                        tabTextView.setTextColor(ContextCompat.getColor(HRAppsActivity.this, R.color.color_62656D));
                        tabTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                        view.setVisibility(View.GONE);
                    }
                } catch (Exception e) {
                    MELogUtil.localE("HRAppsActivity", "onTabUnselected error", e);
                    e.printStackTrace();
                }
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
                //tab重新被选中，什么也不做
            }
        });
        mOnScrollListener = new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    mIsScroll = false;
                    mSmoothScrolling = false;
                } else {
                    mIsScroll = true;
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (mSmoothScrolling || mCategoryTabLayout == null || layoutManager == null) {
                    return;
                }
                try {
                    int position = layoutManager.findFirstVisibleItemPosition();
                    if (position >= 0 && position < mCategoryTabLayout.getTabCount()) {
                        TabLayout.Tab tabAt = mCategoryTabLayout.getTabAt(position);
                        if (tabAt != null && !tabAt.isSelected()) {
                            tabAt.select();
                        }
                    }
                } catch (Exception e) {
                    MELogUtil.localE("HRAppsActivity", "onScrolled error", e);
                    e.printStackTrace();
                }
            }
        };
        
        if (mAllAppRecyclerView != null) {
            mAllAppRecyclerView.addOnScrollListener(mOnScrollListener);
        }

        mSearchImageView.setOnClickListener(v -> {
            //跳转到主搜
            Uri uri = Uri.parse(DeepLink.UNIFIED_SEARCH)
                    .buildUpon()
                    .appendQueryParameter("mparam", "{\"defaultTab\": \"5\"}")
                    .build();
            Router.build(uri).go(this);
            JDMAUtils.clickEvent(mobile_EXP_Main_JoyHR_search, mobile_EXP_Main_JoyHR_search, null);
        });
        //点击就关闭页面的监听接口
        mBackImageView.setOnClickListener(v -> {
            finish();
        });

        mViewModel.getLoadingLiveData().observe(this, isLoading -> {
            if (isFinishing() || isDestroyed()) return;
            if (isLoading) {
                PromptUtils.showLoadDialog(this, "");
            } else {
                PromptUtils.removeLoadDialog(this);
            }
        });

        mViewModel.getErrorMsgLiveData().observe(this, msg -> {
            if (!TextUtils.isEmpty(msg) && !isFinishing() && !isDestroyed()) {
                Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
            }
        });

        mViewModel.getHRAppsDataLiveData().observe(this, hrAppsData -> {
            if (isFinishing() || isDestroyed() || hrAppsData == null) return;
            mInit = true;
            //根据数据渲染页面
            if (hrAppsData.categoryList != null) {
                if (mHRAppCategoryList != null) {
                    mHRAppCategoryList.clear();
                } else {
                    mHRAppCategoryList = new ArrayList<>();
                }
                if (mCategoryTabLayout != null) {
                    mCategoryTabLayout.removeAllTabs();
                }
                for (HRAppsData.Category cat : hrAppsData.categoryList) {
                    if (mCategoryTabLayout != null) {
                        TabLayout.Tab tab = mCategoryTabLayout.newTab().setText(cat.title);
                        tab.setCustomView(R.layout.jdme_item_app_category_tab);
                        View view = tab.getCustomView();
                        if (view != null) {
                            TextView textView = view.findViewById(R.id.tv_tab);
                            if (textView != null) {
                                textView.setText(tab.getText());
                            }
                            mCategoryTabLayout.addTab(tab);
                        }
                    }
                    if (mHRAppCategoryList != null) {
                        mHRAppCategoryList.add(cat);
                    }
                }

                if (mCategoryTabLayout != null) {
                    ViewGroup tabStrip = (ViewGroup) mCategoryTabLayout.getChildAt(0);
                    if (tabStrip != null) {
                        for (int idx = 0; idx < tabStrip.getChildCount(); idx++) {
                            View tabView = tabStrip.getChildAt(idx);
                            if (tabView != null) {
                                tabView.setOnLongClickListener(v -> true);
                            }
                        }
                    }
                }
            }
            if (mCategoryAdapter != null) {
                mCategoryAdapter.notifyDataSetChanged();
            }
            setRecyclerViewEmptySpaceHeight();
            mInit = false;
        });
    }

    private void setRecyclerViewEmptySpaceHeight() {
        if (mHRAppCategoryList != null && !mHRAppCategoryList.isEmpty()) {
            HRAppsData.Category lastCategory = mHRAppCategoryList.get(mHRAppCategoryList.size() - 1);
            if (lastCategory.items != null) {
                int lastCategoryAppCount = mHRAppCategoryList.get(mHRAppCategoryList.size() - 1).items.size();
                int numberOfRows = (int) Math.ceil((double) lastCategoryAppCount / 4);
                int singleRowHeight = 122;
                int additionalRowHeight = 60;
                // 只有一行卡片的高度是122，每增加一行多60
                int emptyHeight = ImageUtils.dp2px(this, (numberOfRows - 1) * additionalRowHeight + singleRowHeight);
                int recyclerviewHeight = mAllAppRecyclerView.getHeight();
                if (recyclerviewHeight > emptyHeight) {
                    mAllAppRecyclerView.setPadding(0, 0, 0, recyclerviewHeight - emptyHeight);
                }
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!isFinishing() && !isDestroyed() && StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.setStatusBarLightMode(this);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (!isFinishing() && !isDestroyed() && StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.setStatusBarLightMode(this);
        }
    }
}
