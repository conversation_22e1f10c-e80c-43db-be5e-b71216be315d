package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.StudyMapData;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.repo.StudyMapRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.PagerSnapAdapter;
import com.jd.oa.experience.view.PagerSnapRecyclerView;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class StudyMapSection extends StatelessSection implements Destroyable {

    private static final int COURSE_TYPE_45 = 2;//四五课
    private static final int COURSE_STUDY_MAP = 3;//学习地图

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private StudyMapSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private StudyMapRepo repo;
    private StudyMapData mData;
    private Gson mGson = new Gson();

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                case Constants.ACTION_STUDY_MAP_UPDATE:
                    loadData();
                    break;
                default:
                    break;
            }
        }
    };

    public StudyMapSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_study_map)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = StudyMapRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        intentFilter.addAction(Constants.ACTION_STUDY_MAP_UPDATE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        StudyMapData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getStudyMapData(new LoadDataCallback<StudyMapData>() {
            @Override
            public void onDataLoaded(StudyMapData studyMapData) {
                showData(studyMapData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(StudyMapData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new StudyMapSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        //如果同时有学习地图和四五课，展示学习地图，如果只有四五课，展示四五课
        StudyMapData.CourseType courseType = null;
        if (mData.courseTypeList != null) {
            for (int idx = 0; idx < mData.courseTypeList.size(); idx++) {
                StudyMapData.CourseType courseTypeItem = mData.courseTypeList.get(idx);
                if (courseTypeItem == null || courseTypeItem.courseTypeCode == null) {
                    continue;
                }
                if (courseTypeItem.courseTypeCode == COURSE_TYPE_45) {
                    courseType = courseTypeItem;
                } else if (courseTypeItem.courseTypeCode == COURSE_STUDY_MAP) {
                    courseType = courseTypeItem;
                    break;
                }
            }
        }

        if (courseType == null || courseType.items == null || courseType.items.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mTitleView.setTitle(true, mData.icon, mData.title, mData.jumpUrl, mData.jumpText);
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onDevelopmentEventClick(ExpJDMAConstants.EXP_MAIN_DEV_STUDYMAP, new HashMap<String, String>());
                }
            }
        });

        if (!TextUtils.isEmpty(courseType.mapName)) {
            mItemViewHolder.mMapNameTv.setVisibility(View.VISIBLE);
            mItemViewHolder.mMapNameTv.setText(courseType.mapName);
        } else {
            mItemViewHolder.mMapNameTv.setVisibility(View.GONE);
        }

        if (courseType.courseTypeCode == COURSE_STUDY_MAP) {
            mItemViewHolder.mStudyMapRv.setVisibility(View.VISIBLE);
            mItemViewHolder.mCourseRv.setVisibility(View.GONE);
            ArrayList<StudyMapData.CourseGroup> list = mGson.fromJson(mGson.toJson(courseType.items), new TypeToken<ArrayList<StudyMapData.CourseGroup>>() {}.getType());
            mItemViewHolder.mStudyMapRv.setAdapter(new PagerSnapAdapter<StudyMapData.CourseGroup>(
                    mContext, list, PagerSnapAdapter.VIEW_TYPE_CARD, R.layout.jdme_item_experience_section_study_map_item) {
                @Override
                public void onBindView(View view, StudyMapData.CourseGroup data) {
                    onBindStudyMap(view, data);
                }
            });
        } else {
            mItemViewHolder.mStudyMapRv.setVisibility(View.GONE);
            mItemViewHolder.mCourseRv.setVisibility(View.VISIBLE);
            ArrayList<StudyMapData.Course> list = mGson.fromJson(mGson.toJson(courseType.items), new TypeToken<ArrayList<StudyMapData.Course>>() {}.getType());
            mItemViewHolder.mCourseRv.setAdapter(new PagerSnapAdapter<StudyMapData.Course>(
                    mContext, list, PagerSnapAdapter.VIEW_TYPE_LIST, R.layout.jdme_item_experience_section_course_item) {
                @Override
                public void onBindView(View view, StudyMapData.Course data) {
                    onBindCourse(view, data);
                }
            });
        }
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public TitleView mTitleView;
        public TextView mMapNameTv;

        public PagerSnapRecyclerView<StudyMapData.CourseGroup> mStudyMapRv;
        public PagerSnapRecyclerView<StudyMapData.Course> mCourseRv;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mTitleView = itemView.findViewById(R.id.title_view);
            mMapNameTv = itemView.findViewById(R.id.map_name_tv);

            mStudyMapRv = itemView.findViewById(R.id.study_map_rv);
            mCourseRv = itemView.findViewById(R.id.course_rv);
        }
    }

    //学习地图
    private static final int COURSE_GROUP_TYPE_BASE = 1;//基础能力
    private static final int COURSE_GROUP_TYPE_APTITUDE = 2;//五项素质
    private static final int COURSE_GROUP_TYPE_KNOWLEDGE = 3;//应知应会

    private static final int COURSE_COMPLETE_STATUS_NOT_START = -1;//未开始
    private static final int COURSE_COMPLETE_STATUS_IN_PROGRESS = 0;//进行中
    private static final int COURSE_COMPLETE_STATUS_DONE = 1;//已完成

    private void onBindStudyMap(View view, StudyMapData.CourseGroup courseGroup) {
        TextView studyMapNameTv = view.findViewById(R.id.study_map_name_tv);
        View studyMapOne = view.findViewById(R.id.study_map_item_one);
        View studyMapTwo = view.findViewById(R.id.study_map_item_two);
        View studyMapThree = view.findViewById(R.id.study_map_item_three);
        
        //基础能力，应知应会，五大素质
        studyMapNameTv.setText(!TextUtils.isEmpty(courseGroup.courseGroupName) ? courseGroup.courseGroupName : "");
        //卡片颜色
/*        if (courseGroup.courseGroupType == COURSE_GROUP_TYPE_BASE) {
            view.setBackgroundResource(R.drawable.jdme_bg_study_map_base_ability);
        } else if (courseGroup.courseGroupType == COURSE_GROUP_TYPE_APTITUDE) {
            view.setBackgroundResource(R.drawable.jdme_bg_study_map_aptitude_ability);
        } else if (courseGroup.courseGroupType == COURSE_GROUP_TYPE_KNOWLEDGE) {
            view.setBackgroundResource(R.drawable.jdme_bg_study_map_knowledge_ability);
        }*/
        int size = courseGroup.courseList != null ? courseGroup.courseList.size() : 0;
        //能力卡片里课程条目数量
        studyMapOne.setVisibility(size > 0 ? View.VISIBLE : View.INVISIBLE);
        studyMapTwo.setVisibility(size > 1 ? View.VISIBLE : View.INVISIBLE);
        studyMapThree.setVisibility(size > 2 ? View.VISIBLE : View.INVISIBLE);
        //只展示前3个
        if (studyMapOne.getVisibility() == View.VISIBLE) {
            setStudyMapItem(studyMapOne, courseGroup.courseList.get(0));
        }
        if (studyMapTwo.getVisibility() == View.VISIBLE) {
            setStudyMapItem(studyMapTwo, courseGroup.courseList.get(1));
        }
        if (studyMapThree.getVisibility() == View.VISIBLE) {
            setStudyMapItem(studyMapThree, courseGroup.courseList.get(2));
        }
    }

    private void setStudyMapItem(View studyMapItem, final StudyMapData.Course course) {
        if (course == null) {
            return;
        }

        TextView levelNameTv = studyMapItem.findViewById(R.id.level_name_tv);
        TextView sourceTv = studyMapItem.findViewById(R.id.source_tv);
        TextView completeStatusTv = studyMapItem.findViewById(R.id.complete_status_tv);
        TextView studyDurationTv = studyMapItem.findViewById(R.id.study_duration_tv);
        TextView courseName = studyMapItem.findViewById(R.id.course_name);
        View newCourse = studyMapItem.findViewById(R.id.new_course);

        levelNameTv.setText(!TextUtils.isEmpty(course.levelName) ? course.levelName : "");
        levelNameTv.setVisibility(!TextUtils.isEmpty(course.levelName) ? View.VISIBLE : View.INVISIBLE);
        sourceTv.setText(!TextUtils.isEmpty(course.source) ? course.source : "");
        if (course.completeStatus != null) {
            completeStatusTv.setVisibility(View.VISIBLE);
            completeStatusTv.setTextColor(getCompleteStatusTextColor(course.completeStatus));
            completeStatusTv.setText(getCompleteStatus(course.completeStatus));
            if (course.completeStatus == COURSE_COMPLETE_STATUS_IN_PROGRESS && !TextUtils.isEmpty(course.studyDuration)) {
                studyDurationTv.setVisibility(View.VISIBLE);
                studyDurationTv.setText(course.studyDuration);
            } else {
                studyDurationTv.setVisibility(View.GONE);
            }
        } else {
            completeStatusTv.setVisibility(View.GONE);
        }
        courseName.setText(!TextUtils.isEmpty(course.courseName) ? course.courseName : "");
        newCourse.setVisibility(course.isNew ? View.VISIBLE : View.GONE);
        studyMapItem.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(course.courseUrl)) {
                    Router.build(course.courseUrl).go(mContext, new RouteNotFoundCallback(mContext));

                    Map<String, String> map = new HashMap<>();
                    map.put("appId", course.courseId);
                    ExpJDMAUtil.onDevelopmentEventClick(ExpJDMAConstants.EXP_MAIN_DEV_RESOURCE, map);
                }
            }
        });
    }

    private String getCompleteStatus(int completeStatus) {
        if (completeStatus == COURSE_COMPLETE_STATUS_NOT_START) {
            return mContext.getString(R.string.exp_course_not_started);
        } else if (completeStatus == COURSE_COMPLETE_STATUS_IN_PROGRESS) {
            return mContext.getString(R.string.exp_course_started);
        } else if (completeStatus == COURSE_COMPLETE_STATUS_DONE) {
            return mContext.getString(R.string.exp_course_complete);
        } else {
            return "";
        }
    }

    private int getCompleteStatusTextColor(int completeStatus) {
        if (completeStatus == COURSE_COMPLETE_STATUS_NOT_START) {
            return 0xFFFFA333;
        } else if (completeStatus == COURSE_COMPLETE_STATUS_IN_PROGRESS) {
            return 0xFF666666;
        } else if (completeStatus == COURSE_COMPLETE_STATUS_DONE) {
            return 0xFF999999;
        } else {
            return 0xFF000000;
        }
    }

    //四五课
    private void onBindCourse(View view, final StudyMapData.Course course) {
        ImageView courseIv = view.findViewById(R.id.course_iv);
        TextView courseNameTv = view.findViewById(R.id.course_name_tv);
        TextView courseSourceTv = view.findViewById(R.id.course_source_tv);
        
        ImageLoader.load(mContext, courseIv, course.imageUrl, R.drawable.jdme_bg_exp_course_default);
        courseNameTv.setText(TextUtils.isEmpty(course.courseName) ? "" : course.courseName);
        courseSourceTv.setText(TextUtils.isEmpty(course.source) ? "" : mContext.getString(R.string.exp_course_source_prefix) + course.source);
        view.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(course.courseUrl)) {
                    Router.build(course.courseUrl).go(mContext, new RouteNotFoundCallback(mContext));

                    Map<String, String> map = new HashMap<>();
                    map.put("appId", course.courseId);
                    ExpJDMAUtil.onDevelopmentEventClick(ExpJDMAConstants.EXP_MAIN_DEV_RESOURCE, map);
                }
            }
        });
    }
}
