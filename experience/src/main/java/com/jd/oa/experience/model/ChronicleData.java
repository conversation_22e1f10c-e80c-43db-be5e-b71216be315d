package com.jd.oa.experience.model;

import java.util.ArrayList;

public class ChronicleData {
    public String title;
    public String icon;
    public String jumpText;
    public String jumpUrl;

    public Integer count;
    public ArrayList<ChronicleData.Card> userEventList;

    public static class Card {
        public String eventId;
        public String imageUrl;
        public String joyUrl;
        public String eventTitle;
        public String eventContent;
        public String startColor;
        public String endColor;
        public String eventTime;
        public String eventUrl;
        //预留
        public String orderNum;
        public String eventCode;
        public String eventType;
    }
}

/*
{
	"content": {
		"count": 3,
		"url": "jdme://123",
		"userEventList": [{
			"eventId": "1",
			"imageUrl": "https://apijoyspace.jd.com/v1/files/QsQ6ON4WsJb5VUtshf9n/link",
			"eventTitle": "加入京东",
			"eventContent": "2022年10月30日，你的京东梦幻之旅正式开启。你加入京东，为京东注入了新鲜血液。",
			"joyUrl": "https://apijoyspace.jd.com/v1/files/l74R1WlZK4naekhZgw8e/link",
			"startColor": "#FF0E00",
			"endColor": "#FF6E66",
			"orderNum": "",
			"eventCode": "",
			"eventType": "",
			"eventTime": "2022年11月15日"
		}, {
			"eventId": "2",
			"imageUrl": "https://apijoyspace.jd.com/v1/files/x3PhKnaZAitkTBIRn6bA/link",
			"eventTitle": "升职加薪",
			"eventContent": "2022年10月30日，你的京东梦幻之旅正式开启。你加入京东，为京东注入了新鲜血液。",
			"joyUrl": "https://apijoyspace.jd.com/v1/files/c6yxted17bNCdcRAWSjn/link",
			"startColor": "#FF0E00",
			"endColor": "#FF6E66",
			"orderNum": "",
			"eventCode": "",
			"eventType": "",
			"eventTime": "2022年11月15日"
		}, {
			"eventId": "3",
			"imageUrl": "https://apijoyspace.jd.com/v1/files/3GM6yd2t69EVY7RTYjCd/link",
			"eventTitle": "5年大佬",
			"eventContent": "2022年10月30日，你的京东梦幻之旅正式开启。你加入京东，为京东注入了新鲜血液。",
			"joyUrl": "https://apijoyspace.jd.com/v1/files/9hGIxi4M8gWdQdd0XN0o/link",
			"startColor": "#FF0E00",
			"endColor": "#FF6E66",
			"orderNum": "",
			"eventCode": "",
			"eventType": "",
			"eventTime": "2022年11月15日"
		}]
	},
	"errorMsg": "",
	"errorCode": "0"
}
* */

