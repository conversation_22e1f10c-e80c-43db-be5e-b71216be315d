package com.jd.oa.experience.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;
import android.renderscript.Type;

import com.jd.oa.utils.DensityUtil;

public class ImageUtil {

    public static Bitmap getBlurAvatar(Context context, Bitmap src, int width, int height, int imageHeight, int bkColor, int radius) {
        Bitmap dst = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(dst);
        canvas.drawColor(bkColor);
        int cropHeight = src.getWidth() * imageHeight / width;
        int offset = (src.getHeight() - cropHeight) / 2;
        Rect srcRect = new Rect(0, offset, src.getWidth(), src.getHeight() - offset);
        Rect dstRect = new Rect(0, 0, width, imageHeight);
        canvas.drawBitmap(src, srcRect, dstRect, null);
        return ImageUtil.blurBitmap(context, dst, radius);
    }

    public static Bitmap blurBitmap(Context context, Bitmap bitmap, float radius) {
        //Create renderscript
        RenderScript rs = RenderScript.create(context);
        //Create allocation from Bitmap
        Allocation allocation = Allocation.createFromBitmap(rs, bitmap);

        Type type = allocation.getType();
        //Create allocation with the same type
        Allocation blurredAllocation = Allocation.createTyped(rs, type);
        //Create script
        ScriptIntrinsicBlur blurScript = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs));
        //Set blur radius (maximum 25.0)
        blurScript.setRadius(radius);
        //Set input for script
        blurScript.setInput(allocation);
        //Call script for output allocation
        blurScript.forEach(blurredAllocation);
        //Copy script result into bitmap
        blurredAllocation.copyTo(bitmap);
        //Destroy everything to free memory
        allocation.destroy();
        blurredAllocation.destroy();
        blurScript.destroy();
        rs.destroy();
        return bitmap;
    }
}
