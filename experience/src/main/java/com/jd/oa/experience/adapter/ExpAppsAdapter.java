package com.jd.oa.experience.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.ExpAppData;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.utils.DensityUtil;

import java.util.List;

public class ExpAppsAdapter extends RecyclerView.Adapter<ExpAppsAdapter.ViewHolder> {

    private static final int TYPE_ITEM_TILED = 0;
    private static final int TYPE_ITEM_VERTICAL = 1;

    private final Context mContext;
    private List<ExpAppData.Apps> mList;
    private int mParentWidth;

    public ExpAppsAdapter(Context context, List<ExpAppData.Apps> apps) {
        mContext = context;
        mList = apps;
    }

    @Override
    public int getItemViewType(int position) {
        if (mList.size() >= 3) {
            return TYPE_ITEM_VERTICAL;
        } else {
            return TYPE_ITEM_TILED;
        }
    }

    @NonNull
    @Override
    public ExpAppsAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        mParentWidth = parent.getMeasuredWidth();
        View view;
        if (viewType == TYPE_ITEM_VERTICAL) {
            view = View.inflate(mContext, R.layout.jdme_item_experience_section_app_item_vertical, null);
        } else {
            view = View.inflate(mContext, R.layout.jdme_item_experience_section_app_item_tiled, null);
        }
        return new ViewHolder(view);
    }

    private int getItemWidth() {
        if (mList.size() <= 3) {
            return mParentWidth / 3;//3个等分
        } else {
            int width = mContext.getResources().getDimensionPixelSize(R.dimen.apps_item_width) * mList.size();
            width += DensityUtil.dp2px(mContext, 10 + 10);//边距
            width += DensityUtil.dp2px(mContext, 9) * (mList.size() - 1);//间距
            if (width > mParentWidth) {//滚动
                return mContext.getResources().getDimensionPixelSize(R.dimen.apps_item_width);
            } else {//size等分
                return mParentWidth / mList.size();
            }
        }
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, int position) {
        if (mList.isEmpty() || position >= mList.size()) {
            return;
        }

        final ExpAppData.Apps apps = mList.get(position);
        if (apps == null) {
            return;
        }

        if (holder.getItemViewType() == TYPE_ITEM_VERTICAL) {//3到8个
            int itemWidth = getItemWidth();
            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(itemWidth, FrameLayout.LayoutParams.WRAP_CONTENT);
            if (itemWidth == mContext.getResources().getDimensionPixelSize(R.dimen.apps_item_width)) {//滚动模式
                int marginWidth = DensityUtil.dp2px(mContext, 10);
                int sepWidth = DensityUtil.dp2px(mContext, 4);
                if (position <= 0) {//第一个
                    lp.setMargins(marginWidth, 0, sepWidth, 0);
                } else if (position >= mList.size() - 1) {//最后一个
                    lp.setMargins(0, 0, marginWidth, 0);
                } else {
                    lp.setMargins(0, 0, sepWidth, 0);
                }
            }
            holder.itemView.setLayoutParams(lp);
        } else if (holder.getItemViewType() == TYPE_ITEM_TILED) {//1个,2个
            holder.itemView.setLayoutParams(new ViewGroup.LayoutParams(mParentWidth / mList.size(), ViewGroup.LayoutParams.WRAP_CONTENT));
        }

        if (apps.isPlaceHolder()) {
            holder.tvTitle.setText(mContext.getResources().getString(R.string.exp_app_coming_soon));
            holder.tvTitle.setPadding(0, 0, 0, 0);
            holder.ivTip.setVisibility(View.GONE);
            holder.ivIcon.setVisibility(View.VISIBLE);
            holder.ivGifIcon.setVisibility(View.GONE);
            Glide.with(mContext)
                    .load(R.drawable.exp_app_coming_soon_icon)
                    .error(R.drawable.jdme_icon_exp_defult_photo)
                    .placeholder(R.drawable.jdme_icon_exp_defult_photo)
                    .into(holder.ivIcon);
        } else {
            holder.tvTitle.setText(apps.getTitle());
            holder.ivTip.setVisibility(apps.getBadge().equals("1") ? View.VISIBLE : View.GONE);
            Glide.with(mContext)
                    .load(apps.getIcon())
                    .error(R.drawable.jdme_icon_exp_defult_photo)
                    .placeholder(R.drawable.jdme_icon_exp_defult_photo)
                    .into(new SimpleTarget<Drawable>() {
                        @Override
                        public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                            if (resource instanceof GifDrawable) {
                                holder.ivIcon.setVisibility(View.GONE);
                                holder.ivGifIcon.setVisibility(View.VISIBLE);
                                GifDrawable gifDrawable = (GifDrawable)resource;
                                holder.ivGifIcon.setImageDrawable(gifDrawable);
                                gifDrawable.setLoopCount(0);
                                gifDrawable.start();
                            } else {
                                holder.ivIcon.setVisibility(View.VISIBLE);
                                holder.ivGifIcon.setVisibility(View.GONE);
                                holder.ivIcon.setImageDrawable(resource);
                            }
                        }
                    });
        }
        holder.itemView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(apps);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView tvTitle;
        public ImageView ivIcon;
        public ImageView ivGifIcon;
        public ImageView ivTip;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_title);
            ivIcon = itemView.findViewById(R.id.iv_icon);
            ivGifIcon = itemView.findViewById(R.id.iv_gif_icon);
            ivTip = itemView.findViewById(R.id.iv_tip);
        }
    }


    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(ExpAppData.Apps app);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }


}
