package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.RecommendAdapter;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.experience.repo.RecommendRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.util.OnItemEnterOrExitVisibleHelper;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.DensityUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/**
 * 员工体验平台 推荐部分
 */
@Deprecated
public class RecommendSection extends StatelessSection implements Destroyable {

    private Context mContext;
    private String mTabCode;
    private boolean mIsFirst;
    private SectionedRecyclerViewAdapter mAdapter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private List<RecommendData> mList = new ArrayList<>();
    private RecommendRepo repo;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        initData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    if (null == mItemViewHolder) {
                        return;
                    }
                    initData();
                    recommendAdapter = new RecommendAdapter(mContext, mList);
                    mItemViewHolder.rv_recommend.setAdapter(recommendAdapter);
                    OnItemEnterOrExitVisibleHelper helper = new OnItemEnterOrExitVisibleHelper();
                    helper.setRecyclerScrollListener(mItemViewHolder.rv_recommend);
                    helper.setOnScrollStatusListener(listener);
                    List<RecommendData> cache = repo.getRecommendCache();
                    if (cache != null) {
                        showRecommendData(cache);
                    }
                    break;
                case Constants.ACTION_EXP_ON_RESUME:
                    if (recommendAdapter != null) {
                        recommendAdapter.startBannerAutoPlay();
                    }
                    break;
                case Constants.ACTION_EXP_ON_PAUSE:
                    if (recommendAdapter != null) {
                        recommendAdapter.stopBannerAutoPlay();
                    }
                    break;
            }
        }
    };
    private RecommendAdapter recommendAdapter;

    public RecommendSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_me_recommend_layout)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = RecommendRepo.get(mContext);
        recommendAdapter = new RecommendAdapter(mContext, mList);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_EXP_ON_RESUME);
        intentFilter.addAction(Constants.ACTION_EXP_ON_PAUSE);
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        List<RecommendData> cache = repo.getRecommendCache();
        if (cache != null) {
            showRecommendData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            initData();
        }
    }

    private void initData() {
        repo.getRecommendData(new LoadDataCallback<List<RecommendData>>() {
            @Override
            public void onDataLoaded(List<RecommendData> recommendData) {
                showRecommendData(recommendData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }

    private void showRecommendData(List<RecommendData> data) {
        if (mItemViewHolder != null && data != null && !data.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
            mItemViewHolder.itemView.setLayoutParams(lp);
            ExpTabFragment.notifySectionVisible(mContext, mTabCode);
        }

        List<String> types = Arrays.asList("IMAGE", "LIST", "SUMMARY", "BANNER", "IMAGE_LIST", "SCROLL_LIST");
        //客户端去掉成长发展，成长发展单独占一个Tab，为了兼容老版本，后台不能调整数据，开会决定由客户端通过id过滤
        List<String> excludeIds = Arrays.asList("oF0ktt5tgEXjbvNbpaay5");
        if (data.size() > 0) {
            mList.clear();
            for (RecommendData item : data) {
                if (types.contains(item.tplType) && !excludeIds.contains(item.id)) {
                    mList.add(item);
                }
            }
            recommendAdapter.clearBannerHolders();
            recommendAdapter.notifyDataSetChanged();
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/

        ItemViewHolder holder = (ItemViewHolder) viewHolder;
        holder.rv_recommend.setLayoutManager(new GridLayoutManager(mContext, 2));
        holder.rv_recommend.setAdapter(recommendAdapter);
        holder.rv_recommend.setNestedScrollingEnabled(false);
        OnItemEnterOrExitVisibleHelper helper = new OnItemEnterOrExitVisibleHelper();
        helper.setRecyclerScrollListener(holder.rv_recommend);
        helper.setOnScrollStatusListener(listener);
    }

    private OnItemEnterOrExitVisibleHelper.OnScrollStatusListener listener = new OnItemEnterOrExitVisibleHelper.OnScrollStatusListener() {
        public void onSelectEnterPosition(int postion) {
            Log.v("RecommendSection", "进入Enter：" + postion);
            ExpJDMAUtil.onInsightsEventPagePV(mContext, mList.get(postion).id, postion);
        }

        public void onSelectExitPosition(int postion) {
            Log.v("RecommendSection", "退出Exit：" + postion);
        }
    };

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        return map.containsValue(this);
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {
        RecyclerView rv_recommend;

        public ItemViewHolder(View itemView) {
            super(itemView);
            rv_recommend = itemView.findViewById(R.id.rv_recommend);
        }
    }
}
