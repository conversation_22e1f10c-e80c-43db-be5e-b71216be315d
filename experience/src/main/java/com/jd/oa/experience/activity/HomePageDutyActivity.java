package com.jd.oa.experience.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.dialog.ExpDialog;
import com.jd.oa.experience.dialog.HomePageTagInputDialog;
import com.jd.oa.experience.model.InstructionData;
import com.jd.oa.experience.repo.MyInstructionRepo;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.DutyToast;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.TextWatcherAdapter;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;
import com.zhy.view.flowlayout.TagFlowLayout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class HomePageDutyActivity extends BaseActivity {

    private static final int TAG_ONCLICK_ACTION_ADD = 88;
    private static final int TAG_ONCLICK_ACTION_DELETE = 89;
    private static final String CACHE_KEY = "exp.duty.cache.key.";

    private static class TagItem {
        public String tag;
        public int action;

        public TagItem(String tag, int action) {
            this.tag = tag;
            this.action = action;
        }
    }
    
    private View mSaveBtn;
    private View mCloseBtn;
    private TextView mBusinessTitle;
    private TextView mContentTitle;
    private View mBusinessNoTag;
    private View mContentNoTag;
    private TextView mContentTips;
    private TagFlowLayout mBusinessTagLayout;
    private TagFlowLayout mContentTagLayout;
    private DutyTagAdapter businessAdapter;
    private DutyTagAdapter contentAdapter;
    private TextView mOtherTitle;
    private View mOtherView;
    private EditText mOtherEt;
    private TextView mOtherCountTv;
    private HomePageTagInputDialog mInputDialog;

    private boolean mIsSelf = false;
    private String mCardId = "";
    private String mErp = "";
    private boolean mChanged = false;
    private final MyInstructionRepo mRepo = new MyInstructionRepo();
    private final Gson mGson = new Gson();

    private DutyCardData mDutyData = new DutyCardData();
    private final List<TagItem> mBusinessTagItems = new ArrayList<>();
    private final List<TagItem> mContentTagItems = new ArrayList<>();
    private final TextWatcherAdapter mTextChangeListener = new TextWatcherAdapter() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            super.beforeTextChanged(s, start, count, after);
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            super.onTextChanged(s, start, before, count);
        }

        @SuppressLint("SetTextI18n")
        @Override
        public void afterTextChanged(Editable s) {
            super.afterTextChanged(s);
            if (s.toString().length() >= 200) {
                DutyToast.showToast(HomePageDutyActivity.this, HomePageDutyActivity.this.getString(R.string.exp_homepage_duty_dialog_hint, String.valueOf(200)));
            }
            mOtherCountTv.setText(Math.min(s.length(), 200) + "/200");
            mChanged = true;
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_homepage_duty);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }
        Intent intent = getIntent();
        if (intent != null) {
            mIsSelf = intent.getBooleanExtra(MyHomePageActivity.EXTRA_IS_SELF, false);
            mCardId = intent.getStringExtra(MyHomePageActivity.EXTRA_CARD_ID);
            mErp = intent.getStringExtra(MyHomePageActivity.EXTRA_ERP);
        }
        loadCache();
        initView();
        requestData();
    }

    @Override
    public void onBackPressed() {
        if (mChanged && mIsSelf) {
            ExpDialog dialog = new ExpDialog(HomePageDutyActivity.this, R.string.exp_homepage_confirm_dialog_text, R.string.exp_homepage_confirm_dialog_button);
            dialog.setOnClickListener(new ExpDialog.OnClickListener() {
                @Override
                public void onClickOk() {
                    HomePageDutyActivity.super.onBackPressed();
                }

                @Override
                public void onClickCancel() {

                }
            });
            dialog.show();
        } else {
            super.onBackPressed();
        }
    }

    @SuppressLint("SetTextI18n")
    private void initView() {
        mCloseBtn = findViewById(R.id.close_btn);
        mCloseBtn.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mChanged && mIsSelf) {
                    ExpDialog dialog = new ExpDialog(HomePageDutyActivity.this, R.string.exp_homepage_confirm_dialog_text, R.string.exp_homepage_confirm_dialog_button);
                    dialog.setOnClickListener(new ExpDialog.OnClickListener() {
                        @Override
                        public void onClickOk() {
                            finish();
                        }

                        @Override
                        public void onClickCancel() {

                        }
                    });
                    dialog.show();
                } else {
                    finish();
                }
            }
        });
        mSaveBtn = findViewById(R.id.save_btn);
        mSaveBtn.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
        mSaveBtn.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                saveData();
            }
        });

        mBusinessTitle = findViewById(R.id.business_title);
        mContentTitle = findViewById(R.id.content_title);
        mBusinessTagLayout = findViewById(R.id.business_tag);
        mContentTagLayout = findViewById(R.id.content_tag);
        mBusinessNoTag = findViewById(R.id.business_no_tag);
        mContentNoTag = findViewById(R.id.content_no_tag);
        mContentTips = findViewById(R.id.content_tips);

        updateBusinessTagItems();
        //有标签，展示标签；自己没有标签，展示添加；他人没有标签，gone
        mBusinessNoTag.setVisibility(mBusinessTagItems.isEmpty() ? View.VISIBLE : View.GONE);
        mBusinessTagLayout.setVisibility(!mBusinessTagItems.isEmpty() ? View.VISIBLE : View.GONE);
        businessAdapter = new DutyTagAdapter(mBusinessTagItems);
        mBusinessTagLayout.setAdapter(businessAdapter);
        mBusinessTagLayout.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                if (!mIsSelf) {
                    return true;
                }
                TagItem tagItem = businessAdapter.getItem(position);
                if (tagItem.action == TAG_ONCLICK_ACTION_ADD) {
                    if (mInputDialog == null) {
                        mInputDialog = new HomePageTagInputDialog(HomePageDutyActivity.this);
                    }
                    mInputDialog.setMaxLength(10);
                    mInputDialog.setInputText("");
                    mInputDialog.setOnTagListener(new HomePageTagInputDialog.OnTagListener() {
                        @Override
                        public void onSaveTag(String tag) {
                            if (mDutyData.businessKeyWords.size() < 1) {
                                mDutyData.businessKeyWords.add(tag);
                                updateBusinessTagItems();
                                businessAdapter.notifyDataChanged();
                                mChanged = true;
                            }
                        }

                        @Override
                        public boolean isDuplicate(String tag) {
                            return false;//只有一个不会重复
                        }
                    });
                    mInputDialog.show();
                } else if (tagItem.action == TAG_ONCLICK_ACTION_DELETE) {
                    if (position >= 0 && position < mDutyData.businessKeyWords.size()) {
                        mDutyData.businessKeyWords.remove(position);
                        updateBusinessTagItems();
                        businessAdapter.notifyDataChanged();
                        mChanged = true;
                    }
                }
                return true;
            }
        });

        updateContentTagItems();
        //有标签，展示标签；自己没有标签，展示添加；他人没有标签，gone
        if (!mContentTagItems.isEmpty()) {
            mContentTagLayout.setVisibility(View.VISIBLE);
            mContentNoTag.setVisibility(View.GONE);
        } else {
            mContentTagLayout.setVisibility(View.GONE);
            mContentNoTag.setVisibility(TextUtils.isEmpty(mDutyData.contentTips) ? View.VISIBLE : View.GONE);
        }
        mContentTips.setVisibility(!TextUtils.isEmpty(mDutyData.contentTips) ? View.VISIBLE : View.GONE);
        String title = getResources().getString(R.string.exp_homepage_duty_content_title);
        if (mIsSelf) {
            title += getResources().getString(R.string.exp_homepage_duty_content_title_suffix);
        }
        mContentTitle.setText(title);
        if (!TextUtils.isEmpty(mDutyData.contentTips)) {
            mContentTips.setText(mDutyData.contentTips);
        }
        contentAdapter = new DutyTagAdapter(mContentTagItems);
        mContentTagLayout.setAdapter(contentAdapter);
        mContentTagLayout.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                if (!mIsSelf) {
                    return true;
                }
                TagItem tagItem = contentAdapter.getItem(position);
                if (tagItem.action == TAG_ONCLICK_ACTION_ADD) {
                    if (mInputDialog == null) {
                        mInputDialog = new HomePageTagInputDialog(HomePageDutyActivity.this);
                    }
                    mInputDialog.setMaxLength(8);
                    mInputDialog.setInputText("");
                    mInputDialog.setOnTagListener(new HomePageTagInputDialog.OnTagListener() {
                        @Override
                        public void onSaveTag(String tag) {
                            if (mDutyData.contentKeyWords.size() < 3) {
                                mDutyData.contentKeyWords.add(tag);
                                updateContentTagItems();
                                contentAdapter.notifyDataChanged();
                                mChanged = true;
                            }
                        }

                        @Override
                        public boolean isDuplicate(String tag) {
                            return mDutyData.contentKeyWords.contains(tag);
                        }
                    });
                    mInputDialog.show();
                } else if (tagItem.action == TAG_ONCLICK_ACTION_DELETE) {
                    if (position >= 0 && position < mDutyData.contentKeyWords.size()) {
                        mDutyData.contentKeyWords.remove(position);
                        updateContentTagItems();
                        contentAdapter.notifyDataChanged();
                        mChanged = true;
                    }
                }
                return true;
            }
        });

        //其他说明
        mOtherTitle = findViewById(R.id.other_title);
        mOtherView = findViewById(R.id.other_view);
        mOtherEt = findViewById(R.id.other_et);
        mOtherCountTv = findViewById(R.id.other_tv);
        mOtherEt.setEnabled(mIsSelf);
        mOtherCountTv.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);

        mOtherEt.removeTextChangedListener(mTextChangeListener);
        mOtherEt.setText(mDutyData.otherAnswerText);
        mOtherCountTv.setText(Math.min(mDutyData.otherAnswerText.length(), 200) + "/200");
        mOtherEt.addTextChangedListener(mTextChangeListener);

        if (!mIsSelf && TextUtils.isEmpty(mDutyData.otherAnswerText)) {
            mOtherTitle.setVisibility(View.GONE);
            mOtherView.setVisibility(View.GONE);
        } else {
            mOtherTitle.setVisibility(View.VISIBLE);
            mOtherView.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        JDMAUtils.onEventPagePV(AppBase.getAppContext(), ExpJDMAConstants.EXP_MAIN_HOME_WORK_DUTY_PAGE, "EXP_Main_Home_WorkDuty_Page");
    }

    private void updateBusinessTagItems() {
        mBusinessTagItems.clear();
        for (String keyword : mDutyData.businessKeyWords) {
            mBusinessTagItems.add(new TagItem(keyword, TAG_ONCLICK_ACTION_DELETE));
        }
        if (mIsSelf && mBusinessTagItems.size() < 1) {
            mBusinessTagItems.add(new TagItem("", TAG_ONCLICK_ACTION_ADD));
        }
    }

    private void updateContentTagItems() {
        mContentTagItems.clear();
        for (String keyword : mDutyData.contentKeyWords) {
            mContentTagItems.add(new TagItem(keyword, TAG_ONCLICK_ACTION_DELETE));
        }
        if (mIsSelf && mContentTagItems.size() < 3) {
            mContentTagItems.add(new TagItem("", TAG_ONCLICK_ACTION_ADD));
        }
    }

    private void loadCache() {
        InstructionData.CardDetail cacheData = getCache();
        if (cacheData == null) {
            return;
        }
        parseData(cacheData);
    }

    private void requestData() {
        mRepo.getCardDetail(mCardId, mErp, new LoadDataCallback<InstructionData.CardDetail>() {

            @Override
            public void onDataLoaded(InstructionData.CardDetail cardDetail) {
                parseData(cardDetail);
                addCache(cardDetail);
                initView();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void parseData(InstructionData.CardDetail cardDetail) {
        if (mDutyData == null) {
            mDutyData = new DutyCardData();
        }
        mDutyData.businessKeyWords.clear();
        mDutyData.contentKeyWords.clear();
        mDutyData.contentTips = "";
        mDutyData.otherAnswerText = "";

        for (InstructionData.CardDetail.Content data : cardDetail.content) {
            if (TextUtils.equals(data.questionCode, "Q100101")) {//Q100101:主要负责的业务/产品线名称
                mDutyData.businessQuestionId = data.questionId;
                if (data.answerKeyword != null) {
                    for (String keyword : data.answerKeyword) {
                        if (!TextUtils.isEmpty(keyword)) {
                            mDutyData.businessKeyWords.add(keyword);
                            break;//最多1个
                        }
                    }
                }
            } else if (TextUtils.equals(data.questionCode, "Q100102")) {//Q100102:重点工作内容
                mDutyData.contentQuestionId = data.questionId;
                mDutyData.contentTips = data.tips;
                if (data.answerKeyword != null) {
                    for (String keyword : data.answerKeyword) {
                        if (!TextUtils.isEmpty(keyword)) {
                            mDutyData.contentKeyWords.add(keyword);
                            if (mDutyData.contentKeyWords.size() >= 3) {
                                break;//最多3个
                            }
                        }
                    }
                }
            } else if (TextUtils.equals(data.questionCode, "Q100103")) {//Q100103:其它说明
                mDutyData.otherQuestionId = data.questionId;
                mDutyData.otherAnswerText = data.answerText;
            }
        }
    }

    private void saveData() {
        mDutyData.otherAnswerText = mOtherEt.getText() != null ? mOtherEt.getText().toString().trim() : "";
        List<InstructionData.CardContent> data = new ArrayList<>();
        data.add(new InstructionData.CardContent(mDutyData.businessQuestionId, "", mDutyData.businessKeyWords));
        data.add(new InstructionData.CardContent(mDutyData.contentQuestionId, "", mDutyData.contentKeyWords));
        data.add(new InstructionData.CardContent(mDutyData.otherQuestionId, mDutyData.otherAnswerText, new ArrayList<String>()));

        DutyToast.showLoading(this);
        mRepo.saveCard(data, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                DutyToast.stopLoading();
                DutyToast.showToast(HomePageDutyActivity.this, R.string.exp_homepage_save_success);
                LocalBroadcastManager.getInstance(HomePageDutyActivity.this).sendBroadcast(new Intent(Constants.ACTION_HOMEPAGE_UPDATE_WORK_CARD));
                finish();
                ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_WORK_DUTY_SAVE, new HashMap<String, String>());
            }

            @Override
            public void onDataNotAvailable(String errMsg, int errCode) {
                DutyToast.stopLoading();
                if (errCode == 1011) {//敏感词
                    String text = "";
                    if (TextUtils.equals("Q100101", errMsg)) {
                        text = getString(R.string.exp_homepage_duty_business_title);
                    } else if (TextUtils.equals("Q100102", errMsg)) {
                        text = getString(R.string.exp_homepage_duty_content_title);
                    } else if (TextUtils.equals("Q100103", errMsg)) {
                        text = getString(R.string.exp_homepage_duty_other_title);
                    }
                    text += getString(R.string.exp_homepage_save_sensitive);
                    DutyToast.showToast(HomePageDutyActivity.this, text);
                } else {
                    DutyToast.showToast(HomePageDutyActivity.this, R.string.exp_homepage_save_fail);
                }
            }
        });
    }

    private InstructionData.CardDetail getCache() {
        String key = CACHE_KEY + mErp;
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), key, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        InstructionData.CardDetail data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<InstructionData.CardDetail>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    private void addCache(InstructionData.CardDetail data) {
        String key = CACHE_KEY + mErp;
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), key, null, mGson.toJson(data));
    }

    public class DutyTagAdapter extends TagAdapter<TagItem> {

        public DutyTagAdapter(List<TagItem> data) {
            super(data);
        }

        @Override
        public View getView(FlowLayout parent, int position, TagItem tagItem) {
            View tagView;
            if (tagItem.action == TAG_ONCLICK_ACTION_ADD) {
                tagView = LayoutInflater.from(HomePageDutyActivity.this).inflate(R.layout.jdme_homepage_add_duty_item, parent, false);
            } else {
                tagView = LayoutInflater.from(HomePageDutyActivity.this).inflate(R.layout.jdme_homepage_show_duty_item, parent, false);
                TextView tagTv = tagView.findViewById(R.id.tag_tv);
                if (tagTv != null) {
                    tagTv.setText(tagItem.tag);
                }
                View close = tagView.findViewById(R.id.close);
                if (close != null) {
                    close.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
                }
            }
            return tagView;
        }
    }

    public static class DutyCardData {

        //产品线
        public String businessQuestionId;
        public List<String> businessKeyWords;

        //重点工作内容
        public String contentQuestionId;
        public String contentTips;
        public List<String> contentKeyWords;

        //其他说明
        public String otherQuestionId;
        public String otherAnswerText;

        public DutyCardData() {
            businessKeyWords = new ArrayList<>();
            contentKeyWords = new ArrayList<>();
            businessQuestionId = "";
            contentQuestionId = "";
            contentTips = "";
            otherQuestionId = "";
            otherAnswerText = "";
        }
    }
}
