package com.jd.oa.experience.view;

import static android.view.animation.Animation.RESTART;

import android.content.Context;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.experience.R;

import java.util.Timer;
import java.util.TimerTask;

public class UploadView {

    private static Toast mToast;
    private static Timer timer;

    public static void uploading(Context context) {
        if (mToast == null)
            mToast = new Toast(context);
        View view = View.inflate(context, R.layout.jdme_view_upload, null);
        mToast.setView(view);
        TextView tv = view.findViewById(R.id.tv);
        ImageView iv = view.findViewById(R.id.iv);
        tv.setText(R.string.exp_upload_view_uploading);
        iv.setImageResource(R.drawable.profile_section_qrcode_loading);
        RotateAnimation rotateAnimation = new RotateAnimation(0, 360, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        rotateAnimation.setDuration(1000);
        LinearInterpolator lin = new LinearInterpolator();
        rotateAnimation.setInterpolator(lin);
        rotateAnimation.setRepeatCount(-1);
        rotateAnimation.setRepeatMode(RESTART);
        iv.startAnimation(rotateAnimation);
        mToast.setDuration(Toast.LENGTH_LONG);
        showMyToast(30000);
//        mToast.show();
    }

    public static void success(Context context) {
        dismiss();
//        if (mToast == null) {
        mToast = new Toast(context);
//        }
        View view = View.inflate(context, R.layout.jdme_view_upload, null);
        mToast.setView(view);
        TextView tv = view.findViewById(R.id.tv);
        ImageView iv = view.findViewById(R.id.iv);
        iv.clearAnimation();
        iv.setImageResource(R.drawable.jdme_uploadview_success);
        tv.setText(R.string.exp_upload_view_success);
        mToast.show();
    }

    public static void failed(Context context) {
        dismiss();
//        if (mToast == null) {
        mToast = new Toast(context);
//        }
        View view = View.inflate(context, R.layout.jdme_view_upload, null);
        mToast.setView(view);
        TextView tv = view.findViewById(R.id.tv);
        ImageView iv = view.findViewById(R.id.iv);
        iv.clearAnimation();
        iv.setImageResource(R.drawable.jdme_uploadview_failed);
        tv.setText(R.string.exp_upload_view_failed);
        mToast.show();
    }

    public static void dismiss() {
        if (mToast != null)
            mToast.cancel();
        if (timer != null) {
            timer.cancel();
        }
    }


    public static void showMyToast(final int cnt) {

        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                mToast.show();
            }
        }, 0, 3500);
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                mToast.cancel();
                timer.cancel();
            }
        }, cnt);
    }
}
