package com.jd.oa.experience.viewmodel;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.jd.oa.AppBase;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.HRAppsData;
import com.jd.oa.experience.repo.JoyHRAppsRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;

public class HRAppsViewModel extends ViewModel {
    private final MutableLiveData<Boolean> mLoading = new MutableLiveData<>(false);
    private final MutableLiveData<HRAppsData> mData = new MutableLiveData<>();
    private final MutableLiveData<String> mErrorMsg = new MutableLiveData<>();

    public MutableLiveData<Boolean> getLoadingLiveData() {
        return mLoading;
    }
    public MutableLiveData<String> getErrorMsgLiveData() {
        return mErrorMsg;
    }
    public MutableLiveData<HRAppsData> getHRAppsDataLiveData() {return mData;}

    private JoyHRAppsRepo mRepo;

    public void init(JoyHRAppsRepo repo) {
        mRepo = repo;
    }

    public void loadHRAppsData() {
        mLoading.setValue(true);
        if (mRepo != null) {
            mRepo.getHRApps(new LoadDataCallback<HRAppsData>() {
                @Override
                public void onDataLoaded(HRAppsData hrAppsData) {
                    mLoading.setValue(false);
                    if (hrAppsData != null) {
                        mData.setValue(hrAppsData);
                    } else {
                        mErrorMsg.setValue(AppBase.getAppContext().getString(R.string.failed_to_retrieve_hr_apps_data));
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    mLoading.setValue(false);
                    mErrorMsg.setValue(AppBase.getAppContext().getString(R.string.failed_to_retrieve_hr_apps_data));
                }
            });
        }
    }
}
