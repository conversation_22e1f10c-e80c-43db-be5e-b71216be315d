package com.jd.oa.experience.model;

import androidx.annotation.Keep;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Keep

public class RecommendData implements Serializable {
    public String id;
    public String tplType;
    public String title;
    public String icon;
    public String url;
    public String jumpText;
    public Data data;

    public static class Data implements Serializable {
        public Header header;
        public Body body;
        public List<Title> footer;
        public List<Banner> items;
    }

    public static class Header implements Serializable {
        public Title subtitle;
        public String text;
    }

    public static class Title implements Serializable {
        public String title;
        public String text;
        public String color;
        public String url;
        public int size;
        public String id;
    }

    public static class Body implements Serializable {
        public String id = "";
        public List<BodyItem> items;
        public String image;
        public String url;
        public Title title;
        public String autoplay;
    }

    public static class BodyItem implements Serializable {
        public String id;
        public Title title;
        public Title subtitle;
        public String icon;
        public String url;
        public List<Title> widgets;
        //IMAGE_LIST
        public String image;
        //SCROLL_LIST
        public String businessId;//埋点
        public String businessType;//埋点
        public String categoryId;//预留
        public String categoryName;
        public String bgColor;
        public String bgImage;
        public String button;
        public Map<String, String> extra;//埋点
    }

    public static class Banner implements Serializable {
        public String id;
        public String image;
        public String imageLong;
        public String url;
    }
}