package com.jd.oa.experience.adapter;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.WelfareData;
import com.jd.oa.utils.DensityUtil;

import java.util.List;

public class WelfareCouponAdapter extends RecyclerView.Adapter<WelfareCouponAdapter.ViewHolder> {

    public interface OnItemClickListener {
        void onItemClick(WelfareData.Coupon coupon);
    }

    private final Context mContext;
    private List<WelfareData.Coupon> mList;
    private OnItemClickListener mListener;

    public WelfareCouponAdapter(Context context) {
        mContext = context;
    }

    public void setData(List<WelfareData.Coupon> coupons, OnItemClickListener listener) {
        mListener = listener;
        mList = coupons;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public WelfareCouponAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new WelfareCouponAdapter.ViewHolder(View.inflate(mContext, R.layout.jdme_item_experience_section_welfare_coupon_item, null));
    }

    @Override
    public void onBindViewHolder(@NonNull final WelfareCouponAdapter.ViewHolder holder, final int position) {
        if (mList == null || mList.isEmpty() || position < 0 || position >= mList.size()) {
            return;
        }

        final WelfareData.Coupon coupon = mList.get(position);
        if (coupon == null) {
            return;
        }

        holder.couponType.setText(coupon.couponTypeName);
        holder.couponValue.setText(coupon.value != null ? coupon.value.toString() : "0");
        holder.couponUnit.setText(coupon.unit);
        //58dp - 2dp
        int maxWidth = DensityUtil.dp2px(mContext, 56) - (int)holder.couponUnit.getPaint().measureText(coupon.unit) - 1;
        holder.couponValue.setMaxWidth(maxWidth);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onItemClick(coupon);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        public TextView couponType;
        public TextView couponValue;
        public TextView couponUnit;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            couponType = itemView.findViewById(R.id.couponType);
            couponValue = itemView.findViewById(R.id.couponValue);
            couponUnit = itemView.findViewById(R.id.couponUnit);
        }
    }
}
