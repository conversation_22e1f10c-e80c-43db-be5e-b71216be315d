package com.jd.oa.experience.guide;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.viewpager.widget.ViewPager;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.experience.R;
import com.jd.oa.preference.ExperiencePreference;
import com.jd.oa.utils.StatusBarConfig;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

public class ExpGuidePageDialog extends AlertDialog {

    Context mContext;

    public ExpGuidePageDialog(Context context) {
        super(context);
        mContext = context;
    }

    public ExpGuidePageDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    public ExpGuidePageDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
        View view = View.inflate(mContext, R.layout.jdme_exp_dialog_guide_page, null);
        setContentView(view);
        final ViewPager vp = view.findViewById(R.id.vp);

        if (getWindow() != null) {
            getWindow().setBackgroundDrawable(new ColorDrawable(0x00000000));
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
            getWindow().getDecorView().setPadding(0, 0, 0, 0);
            getWindow().setAttributes(layoutParams);
            if (StatusBarConfig.enableImmersive()) {
                QMUIStatusBarHelper.translucent(getWindow());
                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) vp.getLayoutParams();
                lp.topMargin += QMUIStatusBarHelper.getStatusbarHeight(mContext);
                vp.setLayoutParams(lp);
                MELogUtil.localI(MELogUtil.TAG_JIS, "ExpGuidePageDialog, statusBar enabled");
            }
        }
        ExpGuidePageAdapter adapter = new ExpGuidePageAdapter(mContext);
        adapter.setOnContentClickListener(new ExpGuidePageAdapter.OnContentClickListener() {
            @Override
            public void onNext() {
                vp.setCurrentItem(1);
            }

            @Override
            public void onSkip() {
                onGuideFinish();
            }

            @Override
            public void onFinish() {
                onGuideFinish();
            }
        });
        vp.setAdapter(adapter);
    }


    private void onGuideFinish() {
        //保存已读标记
        ExperiencePreference.getInstance().put(ExperiencePreference.KV_ENTITY_EXP_HAS_READ_GUIDE, true);
        dismiss();
    }
}
