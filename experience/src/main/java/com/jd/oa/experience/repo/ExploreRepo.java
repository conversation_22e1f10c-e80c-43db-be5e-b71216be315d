package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.ExploreData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class ExploreRepo {
    private static final String EXPLORE_CACHE_KEY = "exp.explore.repo.cache.key";
    private static ExploreRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static ExploreRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new ExploreRepo(context);
        }
        return sInstance;
    }

    private ExploreRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public ExploreData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), EXPLORE_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        ExploreData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<ExploreData>() {}.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(ExploreData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), EXPLORE_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getExploreData(final LoadDataCallback<ExploreData> callback) {
        Log.i("zhn", "getExploreData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<ExploreData> response = ApiResponse.parse(info.result, new TypeToken<ExploreData>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getMyExplore, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getMyExplore, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getMyExplore, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getMyExplore");
        //http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getMyExplore
    }
}
/*
{
	"content": {
		"title": "",
		"icon": "",
		"jumpText": "",
		"jumpUrl": "",
		"list": [{
			"deeplink": "jdme://jm/biz/appcenter/202206131299",
			"imageUrl": "https://storage.360buyimg.com/jd.jme.client/images/siwunengli.png",
			"name": "我的专属四五表",
			"id": "424465237",
			"source": "京东集团",
			"desc": ""
		}, {
			"deeplink": "jdme://jm/biz/appcenter/201806220264",
			"imageUrl": "https://storage.360buyimg.com/jd.jme.client/images/360ceping.png",
			"name": "我的360测评",
			"id": "424465237",
			"source": "京东集团",
			"desc": ""
		}, {
			"deeplink": "jdme://jm/biz/appcenter/202206131299",
			"imageUrl": "https://storage.360buyimg.com/jd.jme.client/images/siwunengli.png",
			"name": "我的专属四五表",
			"id": "424465237",
			"source": "京东集团",
			"desc": ""
		}, {
			"deeplink": "jdme://jm/biz/appcenter/201806220264",
			"imageUrl": "https://storage.360buyimg.com/jd.jme.client/images/360ceping.png",
			"name": "我的360测评",
			"id": "424465237",
			"source": "京东集团",
			"desc": ""
		}, {
			"deeplink": "jdme://jm/biz/appcenter/202206131299",
			"imageUrl": "https://storage.360buyimg.com/jd.jme.client/images/siwunengli.png",
			"name": "我的专属四五表",
			"id": "424465237",
			"source": "京东集团",
			"desc": ""
		}]
	},
	"errorCode": "0",
	"errorMsg": ""
}
* */
