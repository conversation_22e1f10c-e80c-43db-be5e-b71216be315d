package com.jd.oa.experience.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RecommendCardListAdapter extends RecyclerView.Adapter<RecommendCardListAdapter.ViewHolder> {

    private final Context mContext;
    private final List<RecommendData.BodyItem> mData;
    private final String mTplType;
    private final int mCardIndex;

    public RecommendCardListAdapter(Context context, List<RecommendData.BodyItem> items, String tplType, int cardIndex) {
        mContext = context;
        mData = items;
        mTplType = tplType;
        mCardIndex = cardIndex;
    }

    @NonNull
    @Override
    public RecommendCardListAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.jdme_me_recommend_card_list_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecommendCardListAdapter.ViewHolder holder, final int position) {
        if (mData == null || mData.isEmpty() || position < 0 || position >= mData.size()) {
            return;
        }
        final RecommendData.BodyItem item = mData.get(position);
        if (item == null) {
            return;
        }

        //背景图
        int color = ColorUtil.parseColor(item.bgColor, 0xFBF2E0);
        color &= 0x00FFFFFF;
        color |= 0x80000000;
        holder.ivGradientBg.setBackgroundColor(color);
        if (!TextUtils.isEmpty(item.bgImage)) {
            Glide.with(mContext).load(item.bgImage).into(holder.ivRightBg);
        }

        holder.tvTitle.setText(item.title.text);
        holder.tvTitle.setTextColor(ColorUtil.parseColor(item.title.color, 0xff774100));
        holder.tvCategory.setText(item.categoryName);
        holder.tvButton.setText(item.button);
        holder.layoutButton.setVisibility(StringUtils.isNotEmptyWithTrim(item.url) ? View.VISIBLE : View.GONE);
        holder.cardView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (StringUtils.isNotEmptyWithTrim(item.url) && !NClick.isFastDoubleClick()) {
                    Router.build(item.url).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> extra = new HashMap<>();
                    if (item.extra != null && !item.extra.isEmpty()) {
                        extra.putAll(item.extra);
                    }
                    if ("1".equals(item.businessType)) {
                        extra.put("appId", item.businessId);
                    } else if ("2".equals(item.businessType)) {
                        extra.put("courseId", item.businessId);
                    }
                    ExpJDMAUtil.onInsightsEventClick(item.id, mTplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_BODY.KeyValue,
                            item.businessId, String.valueOf(position), String.valueOf(mCardIndex), item.businessId, extra);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mData != null ? mData.size() : 0;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        public View cardView;
        public ImageView ivGradientBg;
        public ImageView ivRightBg;
        public TextView tvTitle;
        public TextView tvCategory;
        public TextView tvButton;
        public View layoutButton;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            ivGradientBg = itemView.findViewById(R.id.iv_bg_gradient);
            ivRightBg = itemView.findViewById(R.id.iv_bg_right);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvCategory = itemView.findViewById(R.id.tv_category);
            layoutButton = itemView.findViewById(R.id.button_layout);
            tvButton = itemView.findViewById(R.id.tv_button);
        }
    }
}
