package com.jd.oa.experience.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ScreenUtil;

import java.util.ArrayList;
import java.util.List;

public abstract class PagerSnapAdapter<T> extends RecyclerView.Adapter<PagerSnapAdapter.ViewHolder> {

    public final static int VIEW_TYPE_GRID = 1;//服务，专属福利，我的互动
    public final static int VIEW_TYPE_LIST = 2;//精品课，四五课，自我探索
    public final static int VIEW_TYPE_CARD = 3;//学习地图
    public final static int VIEW_TYPE_SINGLE_LINE = 4;//常用应用
    public final static int VIEW_TYPE_BANNER = 5;//推荐卡片的banner

    public abstract void onBindView(View view, T data);

    private final Context mContext;
    private final List<T> mData;
    private final int mViewType;
    private final int mLayoutId;
    private boolean slimScreen = false;
    
    //grid
    private int gridCountPerPage = 0;//1,2,3,4,6
    
    //list
    private int listCountPerPage = 2;

    //card
    //

    public PagerSnapAdapter(Context context, ArrayList<T> items, int viewType, int layoutId) {
        mContext = context;
        mData = items;
        mViewType = viewType;
        mLayoutId = layoutId;
        slimScreen = ScreenUtil.getScreenWidth(mContext) < DensityUtil.dp2px(mContext, 400);
        if (mData != null && mData.size() > 0 && viewType == VIEW_TYPE_GRID) {
            if (slimScreen) {
                gridCountPerPage = mData.size() < 3 ? mData.size() : 4;
            } else {
                gridCountPerPage = mData.size() < 5 ? mData.size() : 6;
            }
        }
        if (mData != null && mData.size() == 1 && viewType == VIEW_TYPE_LIST) {
            listCountPerPage = 1;//只有一个下边不留白
        }
    }

    @Override
    public int getItemViewType(int position) {
        return mViewType;
    }

    @NonNull
    @Override
    public PagerSnapAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;
        if (viewType == VIEW_TYPE_GRID) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_pager_snap_grid_type_item, parent, false);
        } else if (viewType == VIEW_TYPE_LIST) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_pager_snap_list_type_item, parent, false);
        } else if (viewType == VIEW_TYPE_SINGLE_LINE) {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_pager_snap_single_line_type_item, parent, false);
        } else if(viewType == VIEW_TYPE_BANNER){
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_pager_snap_banner_type_item, parent, false);
        }else {
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_pager_snap_card_type_item, parent, false);
        }
        PagerSnapAdapter.ViewHolder vh = new PagerSnapAdapter.ViewHolder(view);
        if (viewType == VIEW_TYPE_GRID) {
            vh.mContainer1.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainer1, false));
            vh.mContainer2.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainer2, false));
            vh.mContainer3.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainer3, false));
            vh.mContainer4.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainer4, false));
            vh.mContainer5.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainer5, false));
            vh.mContainer6.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainer6, false));
        } else if (viewType == VIEW_TYPE_LIST) {
            vh.mContainerUp.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainerUp, false));
            vh.mContainerDown.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainerDown, false));
        } else if (viewType == VIEW_TYPE_SINGLE_LINE) {
            vh.mItem1.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mItem1, false));
            vh.mItem2.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mItem2, false));
            vh.mItem3.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mItem3, false));
            vh.mItem4.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mItem4, false));
        } else {
            vh.mContainer.addView(LayoutInflater.from(mContext).inflate(mLayoutId, vh.mContainer, false));
        }
        return vh;
    }

    @SuppressLint("DefaultLocale")
    @Override
    public void onBindViewHolder(@NonNull PagerSnapAdapter.ViewHolder holder, int pagePos) {
        if (mViewType == VIEW_TYPE_GRID) {
            onBindGridViewHolder(holder, pagePos);
        } else if (mViewType == VIEW_TYPE_LIST) {
            onBindListViewHolder(holder, pagePos);
        } else if (mViewType == VIEW_TYPE_SINGLE_LINE) {
            onBindSingleLineViewHolder(holder, pagePos);
        } else {
            onBindCardViewHolder(holder, pagePos);
        }
    }

    private void onBindGridViewHolder(@NonNull PagerSnapAdapter.ViewHolder holder, int pagePos) {
        if (mData == null || mData.isEmpty() || pagePos < 0 || gridCountPerPage < 1) {
            return;
        }

        holder.mLineOne.setVisibility(View.VISIBLE);
        if (slimScreen) {
            holder.mLineTwo.setVisibility(gridCountPerPage > 2 ? View.VISIBLE : View.GONE);
        } else {
            holder.mLineTwo.setVisibility(gridCountPerPage > 3 ? View.VISIBLE : View.GONE);
        }
        holder.mContainer1.setVisibility(View.INVISIBLE);
        holder.mContainer2.setVisibility(gridCountPerPage > 1 ? View.INVISIBLE : View.GONE);
        holder.mContainer3.setVisibility(gridCountPerPage == 1 || gridCountPerPage == 2 || gridCountPerPage == 4 ? View.GONE : View.INVISIBLE);
        holder.mContainer4.setVisibility(View.INVISIBLE);
        holder.mContainer5.setVisibility(View.INVISIBLE);
        holder.mContainer6.setVisibility(gridCountPerPage == 6 ? View.INVISIBLE : View.GONE);

        for (int i = 0; i < gridCountPerPage; i++) {
            int dataPos = pagePos * gridCountPerPage + i;
            if (dataPos < mData.size()) {
                T data = mData.get(dataPos);
                if (i == 0) {
                    holder.mContainer1.setVisibility(View.VISIBLE);
                    onBindView(holder.mContainer1.getChildAt(0), data);
                } else if (i == 1) {
                    holder.mContainer2.setVisibility(View.VISIBLE);
                    onBindView(holder.mContainer2.getChildAt(0), data);
                } else if (i == 2) {
                    if (gridCountPerPage == 4) {
                        holder.mContainer4.setVisibility(View.VISIBLE);
                        onBindView(holder.mContainer4.getChildAt(0), data);
                    } else {
                        holder.mContainer3.setVisibility(View.VISIBLE);
                        onBindView(holder.mContainer3.getChildAt(0), data);
                    }
                } else if (i == 3) {
                    if (gridCountPerPage == 4) {
                        holder.mContainer5.setVisibility(View.VISIBLE);
                        onBindView(holder.mContainer5.getChildAt(0), data);
                    } else {
                        holder.mContainer4.setVisibility(View.VISIBLE);
                        onBindView(holder.mContainer4.getChildAt(0), data);
                    }
                } else if (i == 4) {
                    holder.mContainer5.setVisibility(View.VISIBLE);
                    onBindView(holder.mContainer5.getChildAt(0), data);
                } else if (i == 5) {
                    holder.mContainer6.setVisibility(View.VISIBLE);
                    onBindView(holder.mContainer6.getChildAt(0), data);
                }
            }
        }
    }

    private void onBindSingleLineViewHolder(@NonNull PagerSnapAdapter.ViewHolder holder, int pagePos) {
        if (mData == null || mData.isEmpty() || pagePos < 0) {
            return;
        }

        holder.mItem1.setVisibility(View.INVISIBLE);
        holder.mItem2.setVisibility(View.INVISIBLE);
        holder.mItem3.setVisibility(View.INVISIBLE);
        holder.mItem4.setVisibility(View.INVISIBLE);

        for (int i = 0; i < 4; i++) {
            int dataPos = pagePos * 4 + i;
            if (dataPos < mData.size()) {
                T data = mData.get(dataPos);
                if (i == 0) {
                    holder.mItem1.setVisibility(View.VISIBLE);
                    onBindView(holder.mItem1.getChildAt(0), data);
                } else if (i == 1) {
                    holder.mItem2.setVisibility(View.VISIBLE);
                    onBindView(holder.mItem2.getChildAt(0), data);
                } else if (i == 2) {
                    holder.mItem3.setVisibility(View.VISIBLE);
                    onBindView(holder.mItem3.getChildAt(0), data);
                } else {
                    holder.mItem4.setVisibility(View.VISIBLE);
                    onBindView(holder.mItem4.getChildAt(0), data);
                }
            }
        }
    }

    private void onBindListViewHolder(@NonNull PagerSnapAdapter.ViewHolder holder, int pagePos) {
        if (mData == null || mData.isEmpty() || pagePos < 0) {
            return;
        }

        holder.mContainerUp.setVisibility(View.INVISIBLE);
        holder.mContainerDown.setVisibility(listCountPerPage == 1 ? View.GONE : View.INVISIBLE);

        for (int i = 0; i < listCountPerPage; i++) {
            int dataPos = pagePos * listCountPerPage + i;
            if (dataPos < mData.size()) {
                T data = mData.get(dataPos);
                if (i == 0) {
                    holder.mContainerUp.setVisibility(View.VISIBLE);
                    onBindView(holder.mContainerUp.getChildAt(0), data);
                } else if (i == 1) {
                    holder.mContainerDown.setVisibility(View.VISIBLE);
                    onBindView(holder.mContainerDown.getChildAt(0), data);
                }
            }
        }
    }

    private void onBindCardViewHolder(@NonNull PagerSnapAdapter.ViewHolder holder, int dataPos) {
        if (mData == null || mData.isEmpty() || dataPos < 0 || dataPos >= mData.size()) {
            return;
        }

        T data = mData.get(dataPos);
        onBindView(holder.mContainer.getChildAt(0), data);
    }

    @Override
    public int getItemCount() {
        if (mData == null || mData.isEmpty()) {
            return 0;
        }
        
        if (mViewType == VIEW_TYPE_GRID) {
            return (int)Math.ceil((double) mData.size() / gridCountPerPage);
        } else if (mViewType == VIEW_TYPE_LIST) {
            return (int)Math.ceil((double) mData.size() / listCountPerPage);
        } else if (mViewType == VIEW_TYPE_SINGLE_LINE) {
            return (int)Math.ceil((double) mData.size() / 4);
        } else {
            return mData.size();
        }
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        //grid
        public View mLineOne;
        public View mLineTwo;
        public FrameLayout mContainer1;
        public FrameLayout mContainer2;
        public FrameLayout mContainer3;
        public FrameLayout mContainer4;
        public FrameLayout mContainer5;
        public FrameLayout mContainer6;
        
        //list
        public FrameLayout mContainerUp;
        public FrameLayout mContainerDown;

        //card
        public FrameLayout mContainer;

        //single line
        public FrameLayout mItem1;
        public FrameLayout mItem2;
        public FrameLayout mItem3;
        public FrameLayout mItem4;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            //grid
            mLineOne = itemView.findViewById(R.id.line_one);
            mLineTwo = itemView.findViewById(R.id.line_two);
            mContainer1 = itemView.findViewById(R.id.container_1);
            mContainer2 = itemView.findViewById(R.id.container_2);
            mContainer3 = itemView.findViewById(R.id.container_3);
            mContainer4 = itemView.findViewById(R.id.container_4);
            mContainer5 = itemView.findViewById(R.id.container_5);
            mContainer6 = itemView.findViewById(R.id.container_6);
            //list
            mContainerUp = itemView.findViewById(R.id.container_up);
            mContainerDown = itemView.findViewById(R.id.container_down);
            //card
            mContainer = itemView.findViewById(R.id.container);
            //single line
            mItem1 = itemView.findViewById(R.id.item_1);
            mItem2 = itemView.findViewById(R.id.item_2);
            mItem3 = itemView.findViewById(R.id.item_3);
            mItem4 = itemView.findViewById(R.id.item_4);
        }
    }
}
