package com.jd.oa.experience.adapter;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_JoyHR_apps;
import android.content.Context;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.JoyHRData;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JoyHRAdapter extends RecyclerView.Adapter<JoyHRAdapter.JoyHRViewHolder> {
    private List<JoyHRData.JoyHRApp> hrAppList;
    private static final String MY_DOCUMENTS_ID = "202202171207";

    public JoyHRAdapter(List<JoyHRData.JoyHRApp> hrAppList) {
        this.hrAppList = hrAppList;
    }

    @NonNull
    @Override
    public JoyHRViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.jdme_item_experience_section_joyhr_item, parent, false);
        return new JoyHRViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull JoyHRViewHolder holder, int position) {
        holder.bind(hrAppList.get(position));
    }

    @Override
    public int getItemCount() {
        return hrAppList.size();
    }

    static class JoyHRViewHolder extends RecyclerView.ViewHolder {

        private final ImageView ivIcon;
        private final TextView tvTitle;
        private final TextView tvValue;
        private final TextView tvUnit;
        private final ConstraintLayout mContainer;

        public JoyHRViewHolder(View itemView) {
            super(itemView);
            ivIcon = itemView.findViewById(R.id.iv_joyhr_item_icon);
            tvTitle = itemView.findViewById(R.id.tv_joyhr_item_title);
            tvValue = itemView.findViewById(R.id.tv_joyhr_item_value);
            tvUnit = itemView.findViewById(R.id.tv_joyhr_item_unit);
            mContainer = itemView.findViewById(R.id.cl_joyhr_item_container);
        }

        public void bind(JoyHRData.JoyHRApp joyHRAppData) {
            if (joyHRAppData != null) {
                if (ivIcon != null && ivIcon.getContext() != null) {
                    if (TextUtils.isEmpty(joyHRAppData.icon)) {
                        ivIcon.setBackgroundResource(R.drawable.jdme_bg_section_title_default_icon);
                    } else {
                        Glide.with(ivIcon.getContext()).load(joyHRAppData.icon)
                                .placeholder(R.drawable.jdme_bg_section_title_default_icon)
                                .error(R.drawable.jdme_bg_section_title_default_icon)
                                .into(ivIcon);
                    }
                }
                //我的档案不加粗数据
                if (MY_DOCUMENTS_ID.equals(joyHRAppData.id)) {
                    // 将 value 和 unit 合并，并将 value 清空以达到不加粗的效果
                    joyHRAppData.unit = String.format("%s %s", joyHRAppData.value, joyHRAppData.unit);
                    joyHRAppData.value = "";
                }
                tvTitle.setText(joyHRAppData.title);
                if (!TextUtils.isEmpty(joyHRAppData.value)) {
                    tvValue.setText(joyHRAppData.value);
                    tvValue.setVisibility(View.VISIBLE);
                } else {
                    tvValue.setVisibility(View.GONE);
                }
                if (tvValue.getContext() != null && tvValue.getContext().getAssets() != null) {
                    Typeface jdZhenghei = Typeface.createFromAsset(tvValue.getContext().getAssets(), "fonts/JDZhengHei2.1-Bold.otf");
                    if (jdZhenghei != null) {
                        tvValue.setTypeface(jdZhenghei);
                    }
                }
                tvValue.setText(joyHRAppData.value);
                tvUnit.setText(joyHRAppData.unit);
                mContainer.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Context context = mContainer.getContext();
                        if (!TextUtils.isEmpty(joyHRAppData.url) && context != null) {
                            Map<String, String> param = new HashMap<>();
                            param.put("appId", joyHRAppData.id);
                            JDMAUtils.clickEvent(mobile_EXP_Main_JoyHR_apps, mobile_EXP_Main_JoyHR_apps, param);
                            Router.build(joyHRAppData.url).go(context, new RouteNotFoundCallback(context));
                        }
                    }
                });
            }
        }
    }
}
