package com.jd.oa.experience.adapter;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.StringUtils;

import java.util.List;

/**
 * 员工体验平台-推荐-每日小结 SUMMARY
 */
public class RecommendSummaryAdapter extends RecyclerView.Adapter<RecommendSummaryAdapter.ViewHolder> {
    private Context mContext;
    private List<RecommendData.BodyItem> mData;
    private boolean isPaging;
    private boolean hasFooter;
    private OnItemClickListener itemClickListener;
    private int itemHeight;
    private final String TAG = "RecommendSummaryAdapter";
    private int itemWidth;

    protected RecommendSummaryAdapter(Context context, List<RecommendData.BodyItem> data, boolean isPaging, boolean hasFooter) {
        mContext = context;
        mData = data;
        this.isPaging = isPaging;
        this.hasFooter = hasFooter;
    }

    @Override
    public int getItemViewType(int position) {
        return super.getItemViewType(position);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        int layoutId;
//        if (hasFooter) {
//            layoutId = !isPaging && mData.size() < 2 ? R.layout.jdme_me_recommend_type_summary_item_layout3 : R.layout.jdme_me_recommend_type_summary_item_layout2;
//        } else {
//            layoutId = !isPaging && mData.size() < 2 ? R.layout.jdme_me_recommend_type_summary_item_layout1 : R.layout.jdme_me_recommend_type_summary_item_layout;
//        }
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_me_recommend_type_summary_item_layout, parent, false);
        if (isPaging) {
            itemHeight = (parent.getMeasuredHeight() - DensityUtil.dp2px(mContext, 10)) / 2;
        } else {
            itemHeight = mData.size() > 1 ? (parent.getMeasuredHeight() - DensityUtil.dp2px(mContext, 10)) / 2 : parent.getMeasuredHeight() - DensityUtil.dp2px(mContext, 10);
        }
        itemWidth = parent.getMeasuredWidth();
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        try {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            layoutParams.height = itemHeight;
            holder.itemView.setLayoutParams(layoutParams);
            final int width1 = (int) (mContext.getResources().getDisplayMetrics().density * 340);
            holder.tv_content.setVisibility(View.INVISIBLE);
            final RecommendData.BodyItem item = mData.get(position);
            ViewTreeObserver observer = holder.tv_content.getViewTreeObserver();
            observer.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    holder.tv_content.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    double width = holder.tv_content.getWidth();
                    double measureWidth = holder.tv_content.getPaint().measureText(holder.tv_content.getText().toString());
                    String n = "";
                    if (width < measureWidth) {
                        n = " ";
                    } else {
                        n = "\n";
                    }
                    try {
                        SpannableStringBuilder ssb = new SpannableStringBuilder();
                        for (int i = 0; i < item.widgets.size(); i++) {
                            RecommendData.Title title = item.widgets.get(i);
                            if (StringUtils.isNotEmptyWithTrim(title.text)) {
                                title.text += i == 0 ? n : " ";
                            }
                            if (DisplayUtil.getScreenWidth(mContext) < width1 && title.size > 10) {
                                title.size = 10;
                            }
                            if (title.size < 10) {
                                title.size = 10;
                            }
                            SpannableString ss = new SpannableString(title.text);
                            if (StringUtils.isNotEmptyWithTrim(title.color)) {
                                ForegroundColorSpan colorSpan = new ForegroundColorSpan(ColorUtil.parseColor(title.color, 0xff333333));
                                ss.setSpan(colorSpan, 0, title.text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                            }
                            AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(title.size, true);
                            ss.setSpan(sizeSpan, 0, title.text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                            ssb.append(ss);
                        }
                        holder.tv_content.post(new Runnable() {
                            @Override
                            public void run() {
                                Logger.d(TAG, "LineCount2: " + holder.tv_content.getLineCount());
                                Logger.d(TAG, "LineHeight2: " + holder.tv_content.getLineHeight());
                                int maxLines = (itemHeight - DensityUtil.dp2px(mContext, 4)) / holder.tv_content.getLineHeight();
                                Logger.d(TAG, "maxLines2: " + maxLines);
                                holder.tv_content.setMaxLines(maxLines);
                            }
                        });
                        holder.tv_content.setText(ssb);
                        holder.tv_content.setVisibility(View.VISIBLE);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
            holder.tv_content.post(new Runnable() {
                @Override
                public void run() {
                    Logger.d(TAG, "LineCount1: " + holder.tv_content.getLineCount());
                    Logger.d(TAG, "LineHeight1: " + holder.tv_content.getLineHeight());
                }
            });
            holder.tv_content.setText(item.widgets.get(0).text);
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    itemClickListener.onItemClick(item, position);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        itemClickListener = listener;
    }

    public interface OnItemClickListener {
        void onItemClick(RecommendData.BodyItem item, int position);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tv_content;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tv_content = itemView.findViewById(R.id.tv_content);
        }
    }
}
