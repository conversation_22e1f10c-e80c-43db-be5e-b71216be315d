package com.jd.oa.experience.repo;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.storage.entity.KvEntity;
import com.jd.oa.utils.JsonUtils;

import java.util.Map;

public class JDHRRepo<T> {
    private final String mCacheKey;
    private final TypeToken<T> mTypeToken;
    private final KvEntity<String> kvEntity;

    private JDHRRepo(String cacheKey, TypeToken<T> typeToken) {
        mCacheKey = cacheKey;
        mTypeToken = typeToken;
        kvEntity = new KvEntity(mCacheKey, "");
    }

    public static <T> JDHRRepo<T> create(String cacheKey, TypeToken<T> typeToken) {
        return new JDHRRepo<>(cacheKey, typeToken);
    }

    public T getCache() {
        String cache = JDMETenantPreference.getInstance().get(kvEntity);
        if (cache == null || TextUtils.isEmpty(cache)) {
            return null;
        }
        T data = null;
        try {
            data = JsonUtils.getGson().fromJson(cache, mTypeToken.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(T data) {
        if (data != null) {
            JDMETenantPreference.getInstance().put(kvEntity, JsonUtils.getGson().toJson(data));
        }
    }

    public void fetchData(Map<String, String> params, String apiMethod, final LoadDataCallback<T> callback) {
        HttpManager.color().post(null, params, apiMethod, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<T> response = ApiResponse.parse(info.result, mTypeToken.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        });
    }
}
