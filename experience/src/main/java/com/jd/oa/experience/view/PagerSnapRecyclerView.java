package com.jd.oa.experience.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;


public class PagerSnapRecyclerView<T> extends LinearLayout {

    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
        }
    };
    private Context mContext;
    private RecyclerView mRv;
    private PagerSnapIndicator mIndicator;
    private LinearLayoutManager mLinearLayoutManager;
    private final PagerSnapHelper mSnapHelper = new PagerSnapHelper();
    private Runnable mRunnable = null;
    private boolean isStarted = false;
    private boolean mAutoScrollEnable = false;

    private final RecyclerView.OnScrollListener mListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_SETTLING) {
                mRunnable = new Runnable() {
                    @Override
                    public void run() {
                        View viewIdle = mSnapHelper.findSnapView(mLinearLayoutManager);
                        if (viewIdle != null) {
                            int position = mLinearLayoutManager.getPosition(viewIdle);
                            if (mIndicator != null && mIndicator.getVisibility() == View.VISIBLE) {
                                mIndicator.setCurrentItem(position);
                            }
                        }
                    }
                };
            } else {
                if (mRunnable != null) {
                    mRunnable.run();
                    mRunnable = null;
                }
            }
            if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                stopAutoScroll();
            } else if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                startAutoScroll();
            }
        }

        @Override
        public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
        }
    };

    public PagerSnapRecyclerView(Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public PagerSnapRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        TypedArray typeArray = context.obtainStyledAttributes(attrs, R.styleable.PagerSnapRecyclerView);
        mAutoScrollEnable = typeArray.getBoolean(R.styleable.PagerSnapRecyclerView_autoScrollEnable, false);
        typeArray.recycle();
        initView();
    }

    public PagerSnapRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }

    private void initView() {
        View parent = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_pager_snap_recycler_view, this);
        mRv = parent.findViewById(R.id.rv);
        mIndicator = parent.findViewById(R.id.indicator);
        mRv.setItemViewCacheSize(10);
        mLinearLayoutManager = new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false);
        mRv.setLayoutManager(mLinearLayoutManager);
        mSnapHelper.attachToRecyclerView(mRv);
        mRv.removeOnScrollListener(mListener);
        mRv.addOnScrollListener(mListener);
        mIndicator.setVisibility(View.GONE);
    }

    public void setAdapter(PagerSnapAdapter<T> adapter) {
        mRv.setAdapter(adapter);
        int pageCount = mLinearLayoutManager.getItemCount();
        if (pageCount > 1) {
            mIndicator.setVisibility(View.VISIBLE);
            mIndicator.setCount(pageCount);
            mIndicator.setCurrentItem(0);
        } else {
            mIndicator.setVisibility(View.GONE);
        }
    }

    private final Runnable mAutoScrollRunnable = new Runnable() {
        @Override
        public void run() {
            View viewIdle = mSnapHelper.findSnapView(mLinearLayoutManager);
            if (viewIdle != null && mRv != null && mRv.getAdapter() != null) {
                int position = (mLinearLayoutManager.getPosition(viewIdle) + 1) % mRv.getAdapter().getItemCount();
                if (mIndicator != null && mIndicator.getVisibility() == View.VISIBLE) {
                    mIndicator.setCurrentItem(position);
                }
                mRv.smoothScrollToPosition(position);
            }
            mHandler.postDelayed(mAutoScrollRunnable, 3000);
        }
    };

    public void startAutoScroll() {
        if (isStarted || !mAutoScrollEnable) {
            return;
        }
        isStarted = true;
        mHandler.postDelayed(mAutoScrollRunnable, 3000);
    }

    public void stopAutoScroll() {
        isStarted = false;
        mHandler.removeCallbacksAndMessages(null);
    }

    @Override
    protected void onWindowVisibilityChanged(int visibility) {
        if (visibility == GONE || visibility == INVISIBLE) {
            stopAutoScroll();
        } else if (visibility == VISIBLE && mAutoScrollEnable) {
            startAutoScroll();
        }
        super.onWindowVisibilityChanged(visibility);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopAutoScroll();
    }

    public void setIndicatorDark() {
        if (mIndicator != null) {
            mIndicator.dark();
        }
    }

    public void setIndicatorHeight(int px) {
        if (mIndicator != null) {
//            ViewGroup.LayoutParams params = mIndicator.getLayoutParams();
//            params.height = px;
//            mIndicator.setLayoutParams(params);
            mIndicator.usdNewStrategy();
        }
        findViewById(R.id.mSpace).setVisibility(View.GONE);
    }

    public void setIndicatorLight() {
        if (mIndicator != null) {
            mIndicator.light();
        }
    }
}
