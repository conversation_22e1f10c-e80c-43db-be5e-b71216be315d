package com.jd.oa.experience.model;

import java.util.List;

public class ManualData {
    public String title;
    public String icon;
    public String jumpText;
    public String jumpUrl;
    public ManualData.WorkCard items;

    public static class WorkCard {
        public ManualData.Duty responsibility;
        public ManualData.OKR aim;
        public ManualData.Manual manual;
    }

    public static class Manual {
        public String cardId;
        public ManualData.Manual.Content content;

        public static class Content {
            public String openUrl;
            public boolean isShow;
        }
    }


    public static class Duty {
        public String cardId;
        public List<ManualData.Duty.Content> content;

        public static class Content {
            public String questionId;
            public String questionCode;
            public String answerText;
            public List<String> answerKeyword;
        }
    }

    public static class OKR {
        public String cardId;
        public ManualData.OKR.Content content;

        public static class Content {
            public Integer targetNum;
            public Boolean fromIsGray;
            public Boolean toIsGray;
        }
    }
}

/*
{
	"errorCode": "0",
	"content": {
		"icon": "https://storage.360buyimg.com/jd.jme.client/images/insightshuomingshu.png",
		"title": "我的说明书",
		"jumpText": "",
		"jumpUrl": "",
		"items": {
			"manual": {
				"cardId": "1234",
				"content": {
					"openUrl": "jdme://rn/201909020601?routeTag=manual&rnStandalone=2&page_id=7cGPh81eg0uhVtWqZ8GZ",
					"isShow": true
				}
			},
			"aim": {
				"cardId": "mbvi2379fgj120934ey",
				"content": {
					"targetNum": 6,
					"fromIsGray": true,
					"toIsGray": true
				}
			},
			"responsibility": {
				"cardId": "1d1312fsw972",
				"content": [{
					"questionId": "",
					"answerText": "",
					"answerKeyword": ["员工体验平台"],
					"questionCode": "Q100101"
				}, {
					"questionId": "",
					"answerText": "",
					"answerKeyword": ["京东ME", "研发", "企业效率组"],
					"questionCode": "Q100102"
				}, {
					"questionId": "",
					"answerText": "",
					"answerKeyword": [],
					"questionCode": "Q100103"
				}]
			}
		}
	},
	"errorMsg": ""
}
* */