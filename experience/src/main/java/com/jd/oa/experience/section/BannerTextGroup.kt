package com.jd.oa.experience.section

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import com.chenenyu.router.Router
import com.jd.oa.experience.R
import com.jd.oa.experience.constants.ExpJDMAConstants
import com.jd.oa.experience.model.BannerData
import com.jd.oa.experience.util.ExpJDMAUtil
import com.jd.oa.router.RouteNotFoundCallback
import com.jd.oa.utils.*
import java.util.regex.Pattern

class BannerTextGroup(context: Context, attrs: AttributeSet?) : FrameLayout(context, attrs) {

    private val mContainers = mutableListOf<ViewGroup>()
    private val mImgs = mutableListOf<ImageView>()
    private val mTitles = mutableListOf<TextView>()
    private val mSubTitles = mutableListOf<TextView>()

    private val mItemClick = View.OnClickListener {
        val textBanner = it.tag as? BannerData.TextBanner ?: return@OnClickListener
        val values = HashMap<String, String>()
        values["timestamp"] = "" + System.currentTimeMillis()
        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_BANNER_ITEM_CLICK, values)
        if (!TextUtils.isEmpty(textBanner.jumpUrl)) {
            Router.build(textBanner.jumpUrl).go(it.context, RouteNotFoundCallback(it.context))
        }
    }

    fun bindText(textBanners: List<BannerData.TextBanner>?) {
        if (mContainers.isEmpty()) {
            context.inflater.inflate(
                R.layout.jdme_item_experience_section_banner_txt_group,
                this,
                true
            )
            mContainers.add(findViewById(R.id.mContainer00))
            mContainers.add(findViewById(R.id.mContainer01))
            mContainers.add(findViewById(R.id.mContainer10))
            mContainers.add(findViewById(R.id.mContainer11))

            mImgs.add(findViewById(R.id.mImg00))
            mImgs.add(findViewById(R.id.mImg01))
            mImgs.add(findViewById(R.id.mImg10))
            mImgs.add(findViewById(R.id.mImg11))

            mTitles.add(findViewById(R.id.mText00))
            mTitles.add(findViewById(R.id.mText01))
            mTitles.add(findViewById(R.id.mText10))
            mTitles.add(findViewById(R.id.mText11))

            mSubTitles.add(findViewById(R.id.mSubText00))
            mSubTitles.add(findViewById(R.id.mSubText01))
            mSubTitles.add(findViewById(R.id.mSubText10))
            mSubTitles.add(findViewById(R.id.mSubText11))
        }
        val size = textBanners?.size ?: 0
        for (i in 4 downTo 1) {
            // 4,3,2,1
            val index = i - 1
            if (size >= i) {
                mContainers[index].visible()
                mContainers[index].tag = textBanners!![index]
                mContainers[index].setOnClickListener(mItemClick)
                handleItem(index, textBanners[index])
            } else {
                mContainers[index].gone()
            }
        }
    }

    private fun handleItem(index: Int, textBanner: BannerData.TextBanner) {
        setBg(textBanner, mContainers[index])
        val img = mImgs[index]
        if (textBanner.img.isBlankOrNull()) {
            img.gone()
        } else {
            img.visible()
            ImageLoader.load(
                context,
                img,
                textBanner.img ?: "",
                R.drawable.jdme_self_info_image_default_plain
            )
        }

        val title = mTitles[index]
        if (textBanner.title.isBlankOrNull()) {
            title.gone()
        } else {
            title.visible()
            title.text = textBanner.title ?: ""
        }

        val subTitle = mSubTitles[index]
        val content = textBanner.content ?: ""
        if (content.isBlankOrNull()) {
            subTitle.gone()
        } else {
            subTitle.visible()
            highlightDigit(content, subTitle)
        }
    }

    private val mDigitPattern = Pattern.compile("(\\D*)(\\d+)")

    private fun highlightDigit(c: String, subTitle: TextView) {
        val content = preprocess(c)
        val ss = SpannableString(content)
        var startIndex = 0
        val matcher = mDigitPattern.matcher(content)
        val typeface = ResourcesCompat.getFont(context, R.font.din_condensed_bold)
        kotlin.runCatching {
            while (startIndex < content.length && matcher.find(startIndex)) {
                val s = matcher.group(2) ?: break
                val regionEnd = matcher.end() // exclusive
                val regionStart = regionEnd - s.length // inclusive
                val colorSpan = ForegroundColorSpan(Color.parseColor("#FF333333"))
                ss.setSpan(colorSpan, regionStart, regionEnd, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
                val sizeSpan = AbsoluteSizeSpan(16, true)
                ss.setSpan(sizeSpan, regionStart, regionEnd, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
//                val styleSpan = StyleSpan(Typeface.BOLD)
//                ss.setSpan(styleSpan, regionStart, regionEnd, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)

                if (typeface != null) {
                    val typefaceSpan = MyTypefaceSpan(typeface)
                    ss.setSpan(
                        typefaceSpan,
                        regionStart,
                        regionEnd,
                        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                    )
                }

                replaceBlankWithSpan(regionStart - 1, subTitle, ss)
                replaceBlankWithSpan(regionEnd, subTitle, ss)

                startIndex += s.length + (matcher.group(1)?.length ?: 0)
            }
        }
        subTitle.text = ss
    }

    private fun replaceBlankWithSpan(startIndex: Int, tv: TextView, ss: SpannableString) {
        if (startIndex <= 0 || startIndex >= ss.length) {
            return
        }
        kotlin.runCatching {
            val view = context.inflater.inflate(R.layout.jdme_exp_blank_span, null)
            val typefaceSpan = MyViewSpan(view, tv, 0)
            ss.setSpan(
                typefaceSpan,
                startIndex,
                startIndex + 1,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
    }

    private fun preprocess(input: String): String {
        val regex = mDigitPattern.toRegex()
        return input.replace(regex) {
            kotlin.runCatching {
                val g1 = it.groups[1]?.value
                val g2 = it.groups[2]?.value
                "$g1 $g2 "
            }.getOrNull() ?: it.value
        }.trim()
    }

    private fun setBg(textBanner: BannerData.TextBanner, view: ViewGroup) {
        //背景色
        val colors = intArrayOf(0xffffff, 0xffffff)
        try {
            val top = ColorUtil.parseColor(textBanner.fromColor, 0xffffff)
            val bottom = ColorUtil.parseColor(textBanner.endColor, 0xffffff)
            colors[0] = top
            colors[1] = bottom
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val bkGndDrawable = GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors)
        bkGndDrawable.gradientType = GradientDrawable.LINEAR_GRADIENT
        bkGndDrawable.cornerRadius = CommonUtils.dp2px(8f).toFloat()
        bkGndDrawable.setGradientCenter(0.0f, 0.65f)
        view.background = bkGndDrawable
    }
}