package com.jd.oa.experience.view;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.LocaleUtils;

import java.util.HashMap;
import java.util.Map;

public class SeniorityTextLayout extends FrameLayout {

    private Context mContext;
    private String mUrl;

    private ImageView mSeniorityCnIv;
    private ImageView mSeniorityEnIv;
    private TextView mSeniorityTv;
    private IconFontView mSeniorityButton;
    private View mCvSeniority;
    private ImageView mSeniorityBkgnd;
    private View mTextLayout;
    private boolean mCN;

    public SeniorityTextLayout(@NonNull Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public SeniorityTextLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    public SeniorityTextLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }

    private void initView() {
        View mRoot = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_seniority_text_layout, this);
        mSeniorityTv = mRoot.findViewById(R.id.tv_seniority);
        mSeniorityCnIv = mRoot.findViewById(R.id.iv_seniority_cn);
        mSeniorityEnIv = mRoot.findViewById(R.id.iv_seniority_en);
        mSeniorityButton = mRoot.findViewById(R.id.btn_seniority);
        mCvSeniority = mRoot.findViewById(R.id.cv_senior);
        mSeniorityBkgnd = mRoot.findViewById(R.id.iv_senior_bkgnd);
        mTextLayout = mRoot.findViewById(R.id.text_layout);

        mCN = TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN");
        mSeniorityCnIv.setVisibility(mCN ? VISIBLE : GONE);
        mSeniorityEnIv.setVisibility(mCN ? GONE : VISIBLE);

        setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(mUrl)) {
                    //埋点
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_IN_WORK_DAYS.keyName
                            , ExpJDMAConstants.MainInfo.MAIN_INFO_IN_WORK_DAYS.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
                    Router.build(mUrl).go(mContext);
                }
            }
        });
    }

    public void setData(String url, int days, int years, boolean darkColor) {
        mUrl = url;

        int arrowColor;
        int color;
        int resId;
        if (years >= 10) {
            mSeniorityBkgnd.setVisibility(View.VISIBLE);
            mSeniorityBkgnd.setImageResource(darkColor ? R.drawable.profile_section_seniority_bkgnd_10_dark : R.drawable.profile_section_seniority_bkgnd_10);
            arrowColor = Color.parseColor("#623C14");
            color = arrowColor;
            resId = mCN ? R.drawable.profile_section_senority_text_gold_cn : R.drawable.profile_section_senority_text_gold_en;
        } else if (years >= 5) {
            mSeniorityBkgnd.setVisibility(View.VISIBLE);
            mSeniorityBkgnd.setImageResource(darkColor ? R.drawable.profile_section_seniority_bkgnd_5_dark : R.drawable.profile_section_seniority_bkgnd_5);
            arrowColor = Color.parseColor("#4C5059");
            color = arrowColor;
            resId = mCN ? R.drawable.profile_section_senority_text_silver_cn : R.drawable.profile_section_senority_text_silver_en;
        } else {
            mSeniorityBkgnd.setVisibility(View.VISIBLE);
            mSeniorityBkgnd.setImageResource(darkColor ? R.drawable.profile_section_seniority_bkgnd_1_dark : R.drawable.profile_section_seniority_bkgnd_1);
            arrowColor = Color.parseColor("#8F959E");
            color = Color.parseColor("#333333");
            resId = mCN ? R.drawable.profile_section_senority_text_gray_cn : R.drawable.profile_section_senority_text_gray_en;
        }
//        } else if (years >= 1) {
//            mSeniorityBkgnd.setVisibility(View.VISIBLE);
//            mSeniorityBkgnd.setImageResource(darkColor ? R.drawable.profile_section_seniority_bkgnd_gray_dark : R.drawable.profile_section_seniority_bkgnd_gray);
//            arrowColor = Color.parseColor("#8F959E");
//            color = Color.parseColor("#333333");
//            resId = mCN ? R.drawable.profile_section_senority_text_gray_cn : R.drawable.profile_section_senority_text_gray_en;
//        } else {
//            mSeniorityBkgnd.setVisibility(View.INVISIBLE);
//            arrowColor = Color.parseColor("#8F959E");
//            color = Color.parseColor("#333333");
//            resId = mCN ? R.drawable.profile_section_senority_text_gray_cn : R.drawable.profile_section_senority_text_gray_en;
//        }

        ImageLoader.load(mContext, mCN ? mSeniorityCnIv : mSeniorityEnIv, resId);
        mSeniorityButton.setTextColor(arrowColor);
        mSeniorityButton.setVisibility(!TextUtils.isEmpty(mUrl) ? View.VISIBLE : View.GONE);
        mSeniorityTv.setTextColor(color);
        mSeniorityTv.setText(mContext.getResources().getQuantityString(R.plurals.exp_work_days, days, days));
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int availWidth = MeasureSpec.getSize(widthMeasureSpec);
        super.onMeasure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED), heightMeasureSpec);
        int textWidth = mTextLayout.getMeasuredWidth();
        //Log.i("zhn", "textViewWidth = " + textWidth + ";availWidth = " + availWidth);
        setVisibility(textWidth < availWidth ? View.VISIBLE : View.INVISIBLE);
        if (textWidth < availWidth) {
            ViewGroup.LayoutParams lp = mCvSeniority.getLayoutParams();
            lp.width = textWidth;
            mCvSeniority.setLayoutParams(lp);
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}