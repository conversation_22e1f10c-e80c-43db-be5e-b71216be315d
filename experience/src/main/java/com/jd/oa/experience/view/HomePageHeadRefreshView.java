package com.jd.oa.experience.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import com.jd.oa.experience.R;
import com.jd.oa.pulltorefresh.view.HeadView;

public class HomePageHeadRefreshView extends FrameLayout implements HeadView {
    private ProgressBar progressBar;

    public HomePageHeadRefreshView(Context context) {
        this(context,null);
    }

    public HomePageHeadRefreshView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public HomePageHeadRefreshView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_homepage_pulltorefresh_header,null);
        addView(view);
        progressBar = view.findViewById(R.id.loading);
    }

    @Override
    public void begin() {
        progressBar.setVisibility(VISIBLE);
    }

    @Override
    public void progress(float progress, float all) {

    }

    @Override
    public void finishing(float progress, float all) {
        progressBar.setVisibility(GONE);
    }

    @Override
    public void loading() {
    }

    @Override
    public void normal() {
        progressBar.setVisibility(GONE);
    }

    @Override
    public View getView() {
        return this;
    }
}
