package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.WorkTimeData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class WorkTimeRepo {

    private static final String WORKTIME_CACHE_KEY = "exp.worktime.repo.cache.key";
    private static WorkTimeRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static WorkTimeRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new WorkTimeRepo(context);
        }
        return sInstance;
    }

    private WorkTimeRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public WorkTimeData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), WORKTIME_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        WorkTimeData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<WorkTimeData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(WorkTimeData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), WORKTIME_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getWorkTimeData(final LoadDataCallback<WorkTimeData> callback) {
        Log.i("zhn", "getWorkTimeData");
        HashMap<String, Object> params = new HashMap<>();
//        params.put("teamId", PreferenceManager.UserInfo.getTeamId());
        HttpManager.post(null, new HashMap<String, String>(), params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
               /* if (BuildConfig.DEBUG) {
                    info.result =
//                            "{\"content\":{\"title\":\"会议效率会议效率会议效率会议效率会议效率会议效率会议效率\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insighthuiyixiaolv.png\",\"jumpText\":\"\",\"jumpUrl\":\"jdme://jm/biz/appcenter/202205131266\",\"date\":1652767865000,\"data\":[{\"key\":\"meeting\",\"name\":\"日常会议\",\"color\":\"#ACA5F6\",\"value\":\"21%\"},{\"key\":\"communication\",\"name\":\"在线沟通\",\"color\":\"#F6E33C\",\"value\":\"37%\"},{\"key\":\"focus\",\"name\":\"自主时间\",\"color\":\"#F498A6\",\"value\":\"42%\"}]},\"errorMsg\":\"\",\"errorCode\":\"0\"}";
                            "{\"content\":{\"title\":\"会议效率会议效率会议\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insighthuiyixiaolv.png\",\"jumpText\":\"\",\"jumpUrl\":\"\",\"date\":1652767865000,\"data\":[{\"key\":\"meeting\",\"name\":\"日常会议\",\"color\":\"#ACA5F6\",\"value\":\"21%\"},{\"key\":\"communication\",\"name\":\"在线沟通\",\"color\":\"#F6E33C\",\"value\":\"37%\"},{\"key\":\"focus\",\"name\":\"自主时间\",\"color\":\"#F498A6\",\"value\":\"42%\"}]},\"errorMsg\":\"\",\"errorCode\":\"0\"}";
//                    "{\"content\":{\"title\":\"会议效率会议效率会议效率会议效率会议效率会议效率会议效率\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insighthuiyixiaolv.png\",\"jumpText\":\"\",\"jumpUrl\":\"jdme://jm/biz/appcenter/202205131266\",\"date\":1652767865000,\"data\":[{\"key\":\"meeting\",\"name\":\"日常会议\",\"color\":\"#ACA5F6\",\"value\":\"21%\"},{\"key\":\"communication\",\"name\":\"在线沟通\",\"color\":\"#F6E33C\",\"value\":\"37%\"},{\"key\":\"focus\",\"name\":\"自主时间\",\"color\":\"#F498A6\",\"value\":\"42%\"}]},\"errorMsg\":\"\",\"errorCode\":\"0\"}";
                }*/
                ApiResponse<WorkTimeData> response = ApiResponse.parse(info.result, new TypeToken<WorkTimeData>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getWorkTimeInfo, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getWorkTimeInfo, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getWorkTimeInfo, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getWorkTimeInfo");
//        }, "http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getWorkTimeInfo");
    }
}
