package com.jd.oa.experience.util;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.GlobalData;

public class GlobalDataUtil {

    public static final String CULTURE_TYPE_BIRTHDAY = "1";
    public static final String CULTURE_TYPE_SENIORITY = "2";
    public static final String CULTURE_TYPE_PARTY = "3";

    public static String getCultureType(GlobalData.UserInfo userInfo) {
        if (userInfo == null) {
            return "";
        }
        //生日 > 司龄 > 党员
        if ("1".equals(userInfo.isBirthday)) {
            return CULTURE_TYPE_BIRTHDAY;
        }
        if ("1".equals(userInfo.isEntryDay)) {
            return CULTURE_TYPE_SENIORITY;
        }
        if ("1".equals(userInfo.isCPC)) {
            return CULTURE_TYPE_PARTY;
        }
        return "";
    }

    public static int getCultureImage(GlobalData data) {
        if (data == null || data.userInfo == null) {
            return 0;
        }
        //生日 > 司龄 > 党员
        if ("1".equals(data.userInfo.isBirthday)) {
            return R.drawable.profile_section_birthday;
        }
        if ("1".equals(data.userInfo.isEntryDay) && data.entryInfo != null) {
            int years = -1;
            try {
                years = Integer.parseInt(data.entryInfo.years);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (years >= 10) {
                return R.drawable.profile_section_gold;
            } else if (years >= 5) {
                return R.drawable.profile_section_silver;
            } else if (years >= 1) {
                return R.drawable.profile_section_gray;
            }
        }
        if ("1".equals(data.userInfo.isCPC)) {
            return R.drawable.profile_section_party;
        }
        return 0;
    }
}
