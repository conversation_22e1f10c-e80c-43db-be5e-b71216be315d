package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.AppListData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class AppListRepo {

    private static final String APP_LIST_CACHE_KEY = "exp.app.list.repo.cache.key";
    private static AppListRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static AppListRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new AppListRepo(context);
        }
        return sInstance;
    }

    private AppListRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public AppListData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), APP_LIST_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        AppListData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<AppListData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(AppListData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), APP_LIST_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getAppListData(final LoadDataCallback<AppListData> callback) {
        Log.i("zhn", "getAppListData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<AppListData> response = ApiResponse.parse(info.result, new TypeToken<AppListData>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getAppList, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getAppList, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getAppList, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getAppList");
        //http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getAppList
    }
}
/*
{
	"errorCode": "0",
	"content": {
		"appList": [{
			"badge": "0",
			"icon": "https://storage.360buyimg.com/jd.jme.image.cache/mypicture-icon3.png",
			"id": "sltR3dWm035HQ2jBw3sfz",
			"title": "Profile",
			"url": "jdme://jm/biz/appcenter/202104210998"
		}, {
			"badge": "0",
			"icon": "https://storage.360buyimg.com/jd.jme.file.cache/mykpi.png",
			"id": "l9uyGPvmnRm6A361I9Xjb",
			"title": "KPI",
			"url": "jdme://jm/biz/appcenter/202206151307"
		}, {
			"badge": "",
			"icon": "https://storage.360buyimg.com/jd.jme.file.cache/myfuli.png",
			"id": "K4L0t5yAIyNDSIltoH3t",
			"title": "Welfare",
			"url": "jdme://jm/biz/appcenter/202208291366"
		}, {
			"badge": "0",
			"icon": "https://storage.360buyimg.com/jd.jme.image.cache/mysalary-icon3.png",
			"id": "ENc4zXvEN6jkeiThtkqW7",
			"title": "Payroll",
			"url": "jdme://jm/biz/appcenter/202110111119"
		}, {
			"badge": "",
			"icon": "https://storage.jd.com/jd.jme.image.cache/110A33F51C96AF3712E651411DE8A67B.png",
			"id": "NlCcq7tFft5WJNB0Pp7cR",
			"title": "JDLP",
			"url": "jdme://jm/biz/appcenter/500705629829697536"
		}],
		"icon": "https://storage.360buyimg.com/jd.jme.client/images/insightyingyong.png",
		"jumpText": "",
		"title": "常用应用",
		"jumpUrl": ""
	},
	"errorMsg": ""
}
* */