package com.jd.oa.experience.adapter;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_Course_click;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.RecomCourseData;
import com.jd.oa.utils.JDMAUtils;

import java.util.List;

public class RecomCourseAdapter extends RecyclerView.Adapter<RecomCourseAdapter.RecomCourseViewHolder>{
    private final List<RecomCourseData.CourseItem> mData;

    public RecomCourseAdapter(List<RecomCourseData.CourseItem> recomCourseData) {
        mData = recomCourseData;
    }

    @NonNull
    @Override
    public RecomCourseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.jdme_item_experience_section_recomcourse_item, parent, false);
        return new RecomCourseViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecomCourseViewHolder holder, int position) {
        holder.bind(mData.get(position));
    }

    @Override
    public int getItemCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }

    public static class RecomCourseViewHolder extends RecyclerView.ViewHolder {

        ImageView mCourseImage;
        TextView mCourseTitle;
        ConstraintLayout mContainer;

        public RecomCourseViewHolder(@NonNull View itemView) {
            super(itemView);
            mCourseImage = itemView.findViewById(R.id.iv_course_image);
            mCourseTitle = itemView.findViewById(R.id.tv_course_name);
            mContainer = itemView.findViewById(R.id.cl_course_container);
        }

        public void bind(RecomCourseData.CourseItem recomCourseData) {
            if (recomCourseData != null) {
                if (mCourseImage != null && mCourseTitle != null && mContainer != null) {
                    mCourseTitle.setText(recomCourseData.title);
                    Context context = mContainer.getContext();
                    if (context != null) {
                        Glide.with(context)
                                .load(recomCourseData.image)
                                .placeholder(R.drawable.exp_bg_course_error)
                                .error(R.drawable.exp_bg_course_error)
                                .into(mCourseImage);
                        mContainer.setOnClickListener(v -> {
                            if (!TextUtils.isEmpty(recomCourseData.url)) {
                                JDMAUtils.clickEvent(mobile_EXP_Main_Course_click, mobile_EXP_Main_Course_click, null);
                                Router.build(recomCourseData.url).go(context);
                            }
                        });
                    }
                }
            }
        }
    }
}
