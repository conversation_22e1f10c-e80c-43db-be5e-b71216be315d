package com.jd.oa.experience.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ExpUploadData {

    @SerializedName("content")
    private Content content;
    @SerializedName("errorCode")
    private String errorCode;
    @SerializedName("errorMsg")
    private String errorMsg;

    public Content getContent() {
        return content;
    }

    public void setContent(Content content) {
        this.content = content;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public static class Content {
        @SerializedName("urls")
        private List<String> urls;

        public List<String> getUrls() {
            return urls;
        }

        public void setUrls(List<String> urls) {
            this.urls = urls;
        }
    }
}
