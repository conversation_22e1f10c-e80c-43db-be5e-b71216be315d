package com.jd.oa.experience.qrcode;

/**
 * Created by huf<PERSON> on 2016/7/12
 */
public class CardPresenter implements CardConstraint.ICardPresenter {
    private CardConstraint.ICardView mView;
    private CardRepo mRepo;

    public CardPresenter(CardConstraint.ICardView mView) {
        this.mView = mView;
        mRepo = new CardRepo();
    }

    @Override
    public void getData(boolean isNewVersion) {
        mView.onStart();
        mRepo.getData(new CardRepo.CardRepoListener() {

            @Override
            public void onLoadedFinish(CardInfo info) {
                if (mView != null)
                    mView.onEnd(info, null, true);
            }

            @Override
            public void onFailure(String errorMsg) {
                if (mView != null)
                    mView.onEnd(null, errorMsg, false);
            }
        },isNewVersion);
    }

    @Override
    public void onDestory() {
        mView = null;
        mRepo = null;
    }
}
