package com.jd.oa.experience.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class WorkTimeData {

    @SerializedName("date")
    private long date;
    @SerializedName("data")
    private List<Data> data;
    @SerializedName("icon")
    private String icon;
    @SerializedName("title")
    private String title;
    @SerializedName("jumpText")
    private String jumpText;
    @SerializedName("jumpUrl")
    private String jumpUrl;

    public long getDate() {
        return date;
    }

    public void setDate(long date) {
        this.date = date;
    }

    public List<Data> getData() {
        return data;
    }

    public void setData(List<Data> data) {
        this.data = data;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getJumpText() {
        return jumpText;
    }

    public void setJumpText(String jumpText) {
        this.jumpText = jumpText;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public static class Data {
        @SerializedName("color")
        private String color;
        @SerializedName("value")
        private String value;
        @SerializedName("key")
        private String key = "";
        @SerializedName("name")
        private String name = "";

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
