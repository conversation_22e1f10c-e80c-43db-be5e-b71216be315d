package com.jd.oa.experience.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.StringUtils;

import java.util.List;

public class RecommendAppAdapter extends RecyclerView.Adapter<RecommendAppAdapter.ViewHolder> {
    private List<RecommendData.BodyItem> mData;
    private boolean hasFooter;
    private Context mContext;
    private int itemHeight = 0;
    private boolean isPaging;
    private OnItemClickListener itemClickListener;

    protected RecommendAppAdapter(Context context, List<RecommendData.BodyItem> data, boolean hasFooter, boolean isPaging) {
        mContext = context;
        mData = data;
        this.hasFooter = hasFooter;
        this.isPaging = isPaging;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        int layoutId;
        layoutId = R.layout.jdme_me_recommend_item_list_3_item_layout;
        View view = LayoutInflater.from(parent.getContext()).inflate(layoutId, parent, false);
        if (isPaging) {
            itemHeight = parent.getMeasuredHeight() / (hasFooter ? 3 : 4);
        } else {
            itemHeight = parent.getMeasuredHeight() / mData.size();
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, final int position) {
        try {
//            if (!isPaging) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) holder.itemView.getLayoutParams();
            layoutParams.height = itemHeight;
            holder.itemView.setLayoutParams(layoutParams);
//            }

            final RecommendData.BodyItem item = mData.get(position);
            holder.tv_app_name.setText(item.title.text);
            if (StringUtils.isNotEmptyWithTrim(item.title.color)) {
                holder.tv_app_name.setTextColor(ColorUtil.parseColor(item.title.color, 0xff333333));
            }
/*            if (item.subtitle != null) {
                holder.tv_app_desc.setText(item.subtitle.text);
                if (StringUtils.isNotEmptyWithTrim(item.subtitle.color)) {
                    holder.tv_app_desc.setTextColor(Color.parseColor(item.subtitle.color));
                }
            }
            holder.tv_app_desc.setVisibility(null == item.subtitle || StringUtils.isEmptyWithTrim(item.subtitle.text) ? View.GONE : View.VISIBLE);
*/
            ImageLoader.load(mContext, holder.iv_app_icon, item.icon, false,
                    R.drawable.jdme_exp_recommend_image_holder, R.drawable.jdme_exp_recommend_image_holder);
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    itemClickListener.onItemClick(item, position);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        itemClickListener = listener;
    }

    public interface OnItemClickListener {
        void onItemClick(RecommendData.BodyItem item, int position);
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tv_app_name;
        //TextView tv_app_desc;
        ImageView iv_app_icon;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tv_app_name = itemView.findViewById(R.id.tv_app_name);
            //tv_app_desc = itemView.findViewById(R.id.tv_app_desc);
            iv_app_icon = itemView.findViewById(R.id.iv_app_icon);
        }
    }
}
