package com.jd.oa.experience.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

public class BadgeRecyclerView extends RecyclerView {

    private int scrollbarDefaultDelayBeforeFade;

    public BadgeRecyclerView(@NonNull Context context) {
        super(context);
        scrollbarDefaultDelayBeforeFade = getScrollBarDefaultDelayBeforeFade();
    }

    public BadgeRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        scrollbarDefaultDelayBeforeFade = getScrollBarDefaultDelayBeforeFade();
    }

    public BadgeRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        scrollbarDefaultDelayBeforeFade = getScrollBarDefaultDelayBeforeFade();
    }

    @Override
    protected boolean awakenScrollBars(int startDelay, boolean invalidate) {
        if (startDelay > scrollbarDefaultDelayBeforeFade) {
            return false;
        }
        return super.awakenScrollBars(startDelay, invalidate);
    }
}
