package com.jd.oa.experience.model;

import android.text.TextUtils;

import androidx.annotation.Keep;

import java.util.ArrayList;
import java.util.List;

@Keep
public class GlobalData {
    public static class Badge {//第二行价值观徽章
        public String url;
        public String appId;
        public List<String> list;
        public int total;
    }

    public static class Energy {//第二行能量值
        public String url;
        public String appId;
        public String level;
        public String value;
        //public String icon;
    }

    public static class Floor {
        public String code;
        public String name;
    }

    public static class Tab {
        public ArrayList<Layout> layout;
        public String code;
        public String desc;
        public String name;

        protected Tab copy() {
            Tab copy = new Tab();
            if (this.layout == null) {
                copy.layout = null;
            } else {
                copy.layout = new ArrayList<>();
                for (Layout l : this.layout) {
                    Layout copyLayout = new Layout();
                    copyLayout.code = l.code;
                    copyLayout.name = l.name;
                    copy.layout.add(copyLayout);
                }
            }
            copy.code = this.code;
            copy.name = this.name;
            return copy;
        }
    }

    public static class Layout {
        public String code;
        public String name;
    }

    public static class UserInfo {
        public String avatar;
        public String pendant;
        public String realName;
        public String teamId;
        public String userId;
        public String userName;
        public String isBirthday;
        public String isEntryDay;
        public String isCPC;
    }

    public static class EntryInfo {
        public String url;
        public String days;
        public String years;
        public String entrySourceUrl;
        public String md5;
    }

    public static class BgImage {
        public String imageType;
        public String imagePart1;
        public String imagePart2;
        public String imagePart3;
        public String color1;
        public String color2;
        public String color3;
    }

    public static class Tags {
        public String url;
        public List<String> list;
    }

    public static class Social {
        public boolean isShow;
        public Liked liked;
        public Like like;

        public static class Liked {//获赞
            public String count;
            public boolean hasNew;
            public String url;
        }

        public static class Like {//点赞
            public String count;
            public String url;
        }
    }

    public Badge badge;
    public Energy energy;
    public String feedbackUrl;
    public ArrayList<Tab> tabs;//老版本读取floors，新版本读取tabs，第三版tabs
    public UserInfo userInfo;
    public EntryInfo entryInfo;
    public BgImage bgImage;
    public Tags tags;
    public String tabsVer;
    public String hasNewBgImage;
    public Social social;

    public ArrayList<Tab> cloneTabs() {
        if (tabs == null) {
            return null;
        }
        ArrayList<Tab> copyList = new ArrayList<>();
        for (Tab t : tabs) {
            copyList.add(t.copy());
        }
        return copyList;
    }

    public boolean checkHasInteractionInTab() {
        if (tabs == null || tabs.isEmpty()) {
            return false;
        }

        for (Tab tab : tabs) {
            if (tab != null && TextUtils.equals("3", tab.code) && tab.layout != null) {
                for (Layout layout : tab.layout) {
                    if (layout != null && TextUtils.equals("INTERACTION", layout.code)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}

/*{
    "content":{
        "badge":{
            "appId":"12345656",
            "url":"jdme://badge",
            "list":[
                "http://storage.360buyimg.com/badge/07d91202-1eff-49e6-91cc-d46d87e920f8.png",
                "http://storage.360buyimg.com/badge/15f37b85-1e74-462a-859c-d72f8b6f78bc.png",
                "http://storage.360buyimg.com/badge/f8282ec4-ad04-4887-b92c-78b4ae53b058.png",
                "http://storage.360buyimg.com/badge/8f402418-be3a-4ab6-b8ea-cab8e5c8324a.png",
                "http://storage.360buyimg.com/badge/a492d25e-9f89-4c1f-b95e-d41c8a29c2d6.png"
            ],
            "total":8
        },
        "energy":{
            "appId":"20211209001",
            "url":"jdme://appcenter/20211209001",
            "icon":"https://storage.360buyimg.com/jd.jme.client/images/icon_insight_enery_lv5.png",
            "level":"2",
            "value":"2333"
        },
        "floors":[
            {
                "code":"COM",
                "name":""
            },
            {
                "code":"APP",
                "name":""
            },
            {
                "code":"REC",
                "name":"我的推荐"
            }
        ],
        "tabs":[
            {
                "layout":[
                    {
                        "code":"QUC",
                        "name":""
                    },
                    {
                        "code":"APP",
                        "name":""
                    },
                    {
                        "code":"WEL",
                        "name":""
                    },
                    {
                        "code":"REC",
                        "name":"我的推荐"
                    },
                    {
                        "code":"COM",
                        "name":""
                    }
                ],
                "name":"我的职场",
                "desc": "我是有底线的~",
                "code":"1"
            },
            {
                "layout":[
                    {
                        "code":"EXPLORE",
                        "name":"自我探索"
                    },
                    {
                        "code":"COURSE",
                        "name":"资源推荐"
                    }
                ],
                "name":"成长发展",
                "desc": "我的成长发展哟",
                "code":"2"
            }
        ],
        "tabsV2": [{
			"layout": [{
				"code": "APP",
				"name": "便捷应用"
			}, {
				"code": "WELFARE",
				"name": "我的福利"
			}, {
				"code": "SERVICE",
				"name": "职场服务"
			}, {
				"code": "CULTURE",
				"name": "文化活动"
			}],
			"code": "1",
			"name": "我的职场",
			"desc": "我的职场我的职场文案"
		}, {
			"layout": [{
				"code": "EXPLORE",
				"name": "自我探索"
			}, {
				"code": "STUDYMAP",
				"name": "学习地图"
			}, {
				"code": "COURSE",
				"name": "精品课程"
			}],
			"code": "2",
			"name": "我的发展",
			"desc": "成长发展成长发展文案"
		}, {
			"layout": [{
				"code": "INTERACTION",
				"name": "我的互动"
			}, {
				"code": "MANUAL",
				"name": "我的说明书"
			}, {
				"code": "EVE",
				"name": "我的大事记"
			}],
			"code": "3",
			"name": "我的协作",
			"desc": "描述描述"
		}, {
			"layout": [{
				"code": "WORKTIME",
				"name": "工作时间分配"
			}, {
				"code": "MEETING",
				"name": "会议效率"
			}],
			"code": "4",
			"name": "我的效率",
			"desc": "描述描述"
		}],
        "entryInfo":{
            "days":6,
            "years":2537,
            "background":"https://storage.360buyimg.com/jd.jme.client/images/bg_insight_work_silver.png",
            "url":"jdme://appcenter/20211209001",
            "entrySourceUrl":"https://storage.360buyimg.com/jd.jme.download/entryYear1.json",
            "md5":"bfc8655b9f9cb0c18b4066f516891313"
        },
        "feedbackUrl":"jdme://appcenter/20211209002",
        "userInfo":{
            "avatar":"https://exp.jd.com/abc.jpg",
            "pendant":"https://pendant.jd.com/1.jpg",
            "realName":"小新的蜡笔",
            "teamId":"00046419",
            "userId":"bLpK4ZrvNfsVjW7XhpuWZ",
            "userName":"xiaoxin",
            "isBirthday":"1",
            "isEntryDay":"0",
            "isCPC":"0"
        },
        "bgImage":{
            "imageType":"01",
            "imagePart1":"https://storage.jd.com/usercenter/bg.jpg",
            "imagePart2":"https://storage.jd.com/usercenter/bg.jpg",
            "color1":"#000000",
            "color2":"#FFFFFF"
        },
        "tags":{
            "url":"jdme://tags",
            "list":[
                "标签一",
                "标签二",
                "标签三",
                "标签四"
            ]
        },
        "hasNewBgImage":"1",
        "social":{
            "isShow":false,
            "like":{
                "count":"1.6W",
                "url":" jdme://jm/sys/browser?mparam=%7B%22url%22%3A%22http%3A%2F%2Fjoyinsight-test.jd.com%2Fzan-history%2F%3Ftype%3D02%22%2C%22appId%22%3A%22202208020013%22%2C%22isNativeHead%22%3A%221%22%2C%22isHideShareButton%22%3A%220%22%2C%22SafeArea%22%3A%220%22%7D"
            },
            "liked":{
                "count":"1.6W",
                "hasNew":true,
                "url":"jdme://jm/sys/browser?mparam=%7B%22url%22%3A%22http%3A%2F%2Fjoyinsight-test.jd.com%2Fzan-history%2F%3Ftype%3D01%22%2C%22appId%22%3A%22202208020013%22%2C%22isNativeHead%22%3A%221%22%2C%22isHideShareButton%22%3A%220%22%2C%22SafeArea%22%3A%220%22%7D"
            },
            "homepage":{
                "text":"个人主页",
                "url":""
            }
        }
    },
    "errorCode":"0",
    "errorMsg":""
}*/