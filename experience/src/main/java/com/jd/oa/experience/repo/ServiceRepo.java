package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.ServiceData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class ServiceRepo {

    private static final String SERVICE_CACHE_KEY = "exp.service.repo.cache.key";
    private static ServiceRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static ServiceRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new ServiceRepo(context);
        }
        return sInstance;
    }

    private ServiceRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public ServiceData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), SERVICE_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        ServiceData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<ServiceData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(ServiceData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), SERVICE_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getServiceData(final LoadDataCallback<ServiceData> callback) {
        Log.i("zhn", "getServiceData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<ServiceData> response = ApiResponse.parse(info.result, new TypeToken<ServiceData>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getServiceData, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getServiceData, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getServiceData, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getServiceData");
        //http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getServiceData
    }
}
/*
{
	"errorCode": "0",
	"content": {
		"subTitle": "",
		"icon": "https://storage.360buyimg.com/jd.jme.client/images/insightzhichangfuwu.png",
		"count": 25,
		"title": "常用员工服务",
		"jumpText": "",
		"items": [{
			"unit": "个",
			"id": "1111111",
			"title": "我的资产",
			"value": "5",
			"url": ""
		}, {
			"unit": "更新",
			"id": "2222222",
			"title": "我的档案",
			"value": "2022/11/15",
			"url": ""
		}, {
			"unit": "可申请",
			"id": "3333333",
			"title": "工作居住证",
			"value": "",
			"url": ""
		}, {
			"unit": "可申请",
			"id": "4444444",
			"title": "人事证明",
			"value": "",
			"url": ""
		}],
		"jumpUrl": ""
	},
	"errorMsg": ""
}
* */