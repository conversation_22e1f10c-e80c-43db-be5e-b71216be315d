package com.jd.oa.experience.util;

import android.app.Activity;
import android.os.Bundle;
import android.widget.ImageView;

import com.jd.oa.MyPlatform;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.experience.R;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;

public class UserUtils {

    private static final String TAG = "UserUtils";
    private static ImDdService imDdService = AppJoint.service(ImDdService.class);

    /**
     * 修改用户头像
     */
    public static void changeUserIcon(final Activity activity,
                                      final ImageView imageView, final File iconFile) {
        if (iconFile.exists()) {
            NetWorkManager.changeUserIcon(iconFile,
                    new SimpleRequestCallback<String>(activity,
                            R.string.me_icon_upload) {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            iconFile.delete(); // 删除临时文件
                            ResponseParser parser = new ResponseParser(
                                    info.result, activity);
                            parser.parse(new ResponseParser.ParseCallback() {
                                public void parseObject(JSONObject jsonObject) {
                                    try {
                                        JSONArray array = jsonObject
                                                .optJSONArray("urls");
                                        if (array != null) {
                                            final String userIcon = array.getString(0);
                                            syncAvatarToTimline(userIcon, imageView);
                                        }
                                    } catch (Exception e) {
                                        Logger.e(TAG, e.getCause());
                                    }
                                }

                                public void parseArray(JSONArray jsonArray) {
                                }

                                @Override
                                public void parseError(String errorMsg) {
                                    ToastUtils.showToast(errorMsg);
                                    ImageLoaderUtils.getInstance().displayHeadImage(PreferenceManager.UserInfo.getUserCover(), imageView, true);
                                }
                            });
                        }

                        @Override
                        public void onFailure(HttpException exception,
                                              String info) {
                            super.onFailure(exception, info);
                            ImageLoaderUtils.getInstance().displayHeadImage(PreferenceManager.UserInfo.getUserCover(), imageView, true);
                        }
                    });
        }
    }

    public static void changeUserIcon(ImageView iv, String url) {
        syncAvatarToTimline(url, iv);
    }

    public static void clearPortraitCache() {
//        PreferenceManager.setString("avatar.resource.version", "");
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_AVATAR_RESOURCE_VERSION,"");
    }

    private static void syncAvatarToTimline(final String userIcon, final ImageView imageView) {
        if (userIcon == null) {
            return;
        }
        saveAvatar(userIcon);
        ImageLoaderUtils.getInstance().displayHeadImage(PreferenceManager.UserInfo.getUserCover(), imageView, true);
        imDdService.updateUserAvatar(userIcon, PreferenceManager.UserInfo.getUserName(), imDdService.getAppID());
        imDdService.modifyAvatar(userIcon);
    }

    public static void saveAvatar(final String userIcon) {
        MyPlatform.getCurrentUser()
                .setUserIcon(userIcon);
        PreferenceManager.UserInfo.setUserCover(userIcon);
        Bundle bundle = new Bundle();
        bundle.putString("url",userIcon);
        FragmentUtils.updateUI(OperatingListener.OPERATE_CHANGE_AVATAE, bundle);
        FragmentUtils.updateUI(OperatingListener.OPERATE_REFRESH_GRIDVIEW, null);
    }
}
