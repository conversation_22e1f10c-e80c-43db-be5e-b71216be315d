package com.jd.oa.experience.model;

import android.text.TextUtils;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.util.List;

@Deprecated
@Keep
public class CommonData {

    @SerializedName("content")
    private List<Commons> content;

    public List<Commons> getContent() {
        return content;
    }

    public void setContent(List<Commons> content) {
        this.content = content;
    }

    public static class Commons {
        @SerializedName("unit")
        private String unit;
        @SerializedName("appId")
        private String appId;
        @SerializedName("secret")
        private String secret;
        @SerializedName("title")
        private String title;
        @SerializedName("value")
        private String value;
        @SerializedName("url")
        private String url;

//        private boolean isShow = false;

//        public boolean isShow() {
//            return isShow;
//        }

//        public void setShow(boolean show) {
//            isShow = show;
//        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public boolean isSecret() {
            if (TextUtils.isEmpty(secret)) return false;
            return "1".equals(secret);
        }

        public void setSecret(boolean secret) {
            this.secret = (secret ? "1" : "0");
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
