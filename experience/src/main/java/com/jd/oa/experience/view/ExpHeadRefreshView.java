package com.jd.oa.experience.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.pulltorefresh.view.HeadView;
import com.jme.common.R;

import pl.droidsonroids.gif.GifDrawable;

public class ExpHeadRefreshView extends FrameLayout implements HeadView {

    private ImageView headerIv;
    private TextView headerTv;
    private GifDrawable gifDrawable;
    private boolean mDarkMode = false;

    public ExpHeadRefreshView(Context context) {
        this(context,null);
    }

    public ExpHeadRefreshView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public ExpHeadRefreshView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public void setDarkMode(boolean darkMode) {
        mDarkMode = darkMode;
    }

    private void init(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.layout_pulltorefresh_gif_header,null);
        addView(view);
        headerIv = view.findViewById(R.id.iv_header);
        headerTv = view.findViewById(R.id.loading_text);
        gifDrawable = (GifDrawable) headerIv.getDrawable();
        gifDrawable.setLoopCount(0); // 设置无限循环播放
    }

    @Override
    public void begin() {
        headerTv.setVisibility(VISIBLE);
        headerIv.setVisibility(VISIBLE);
        headerTv.setTextColor(mDarkMode ? 0xffffffff : 0xff000000);
        headerTv.setText(R.string.pull_header_text_refresh);
        gifDrawable.start();
    }

    @Override
    public void progress(float progress, float all) {

    }

    @Override
    public void finishing(float progress, float all) {
        gifDrawable.stop();
        headerIv.setVisibility(GONE);
        headerTv.setVisibility(GONE);
    }

    @Override
    public void loading() {
        headerTv.setTextColor(mDarkMode ? 0xffffffff : 0xff000000);
        headerTv.setText(R.string.pull_header_text_loading);
    }

    @Override
    public void normal() {
        gifDrawable.stop();
        headerIv.setVisibility(GONE);
        headerTv.setVisibility(GONE);
    }

    @Override
    public View getView() {
        return this;
    }
}
