package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.WelfareCouponAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.ExpCommonEyeCache;
import com.jd.oa.experience.model.WelfareData;
import com.jd.oa.experience.repo.WelfareRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.util.NumUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.IconFontView;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

//我的福利
public class WelfareSection extends StatelessSection implements Destroyable {

    private final String EYE_CACHE_KEY = "exp.welfare.eye";
    private Context mContext;
    private String mTabCode;
    private boolean mIsFirst;
    private SectionedRecyclerViewAdapter mAdapter;
    private WelfareSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private WelfareRepo repo;
    private WelfareData mData;
    private WelfareCouponAdapter mCouponAdapter;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_REFRESH_EXP_FROM_TAB:
                    loadData();
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    //showData();
                    break;
                default:
                    break;
            }
        }
    };

    @Deprecated
    public WelfareSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_welfare)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        mCouponAdapter = new WelfareCouponAdapter(mContext);
        repo = WelfareRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_FROM_TAB);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        ExpCommonEyeCache.getInstance().addCache(EYE_CACHE_KEY, false);
        WelfareData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getWelfareData(new LoadDataCallback<WelfareData>() {
            @Override
            public void onDataLoaded(WelfareData welfareData) {
                showData(welfareData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }

    private void showData(WelfareData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }


    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/

        if (mItemViewHolder != null) {
            mItemViewHolder.rvCoupons.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
            mItemViewHolder.rvCoupons.setAdapter(mCouponAdapter);
            mItemViewHolder.rvCoupons.setItemViewCacheSize(5);
        }
        refreshUI();
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        //标题
        if (mData.welfare != null && !TextUtils.isEmpty(mData.welfare.title)) {
            mItemViewHolder.myWelfareTitle.setText(mData.welfare.title);
        }
        if (mData.welfare != null && !TextUtils.isEmpty(mData.welfare.subTitle)) {
            mItemViewHolder.myWelfareDesc.setVisibility(View.VISIBLE);
            mItemViewHolder.myWelfareDesc.setText(mData.welfare.subTitle);
        } else {
            mItemViewHolder.myWelfareDesc.setVisibility(View.GONE);
        }
        if (mData.welfare != null && !TextUtils.isEmpty(mData.welfare.count)) {
            mItemViewHolder.myWelfareCountTv.setVisibility(View.VISIBLE);
            mItemViewHolder.myWelfareCountUnit.setVisibility(View.VISIBLE);
            mItemViewHolder.myWelfareCountTv.setText(mData.welfare.count);
        } else {
            mItemViewHolder.myWelfareCountTv.setVisibility(View.GONE);
            mItemViewHolder.myWelfareCountUnit.setVisibility(View.GONE);
        }
        mItemViewHolder.myWelfareGoBtn.setVisibility(mData.welfare != null && !TextUtils.isEmpty(mData.welfare.url) ? View.VISIBLE : View.GONE);
        mItemViewHolder.myWelfareTitleLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if (mData.welfare != null && !TextUtils.isEmpty(mData.welfare.url)) {
                    Router.build(mData.welfare.url).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put("appId", mData.welfare.appId);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                }
            }
        });
        //我的钱包
        mItemViewHolder.myWalletLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mData.account != null && !TextUtils.isEmpty(mData.account.url)) {
                    Router.build(mData.account.url).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put("appId", mData.account.appId);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                }
            }
        });
        boolean isShow = ExpCommonEyeCache.getInstance().getCache(EYE_CACHE_KEY);
        showWalletSecret(isShow);
        mItemViewHolder.myWalletEyeBtn.setVisibility(View.VISIBLE);
        mItemViewHolder.myWalletEyeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean show = ExpCommonEyeCache.getInstance().getCache(EYE_CACHE_KEY);
                ExpCommonEyeCache.getInstance().addCache(EYE_CACHE_KEY, !show);
                showWalletSecret(!show);
            }
        });

        //福利券
        mItemViewHolder.myCouponGoBtn.setVisibility(mData.coupons != null && !TextUtils.isEmpty(mData.coupons.url) ? View.VISIBLE : View.GONE);
        mItemViewHolder.myCouponGoLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mData.coupons != null && !TextUtils.isEmpty(mData.coupons.url)) {
                    Router.build(mData.coupons.url).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put("appId", mData.coupons.appId);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                }
            }
        });
        if (mData.coupons == null || mData.coupons.list == null || mData.coupons.list.isEmpty()) {
            mItemViewHolder.rvCoupons.setVisibility(View.GONE);
            mItemViewHolder.noCouponsLayout.setVisibility(View.VISIBLE);
        } else {
            mItemViewHolder.rvCoupons.setVisibility(View.VISIBLE);
            mItemViewHolder.noCouponsLayout.setVisibility(View.GONE);
            mCouponAdapter.setData(mData.coupons.list, new WelfareCouponAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(WelfareData.Coupon coupon) {
                    if (TextUtils.isEmpty(coupon.couponTypeCode)) {
                        Toast.makeText(mContext, "couponTypeCode为空", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    Intent intent = new Intent();
                    intent.setComponent(new ComponentName(mContext, "com.jd.oa.mae.aura.welfare.MyWelfareDetailActivity"));
                    intent.putExtra("couponTypeCode", coupon.couponTypeCode);
                    mContext.startActivity(intent);
                    Map<String, String> map = new HashMap<>();
                    map.put("appId", mData.coupons.appId);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                }
            });
        }

        //我的假期
        mItemViewHolder.myHolidayGoTv.setVisibility(mData.holiday != null && !TextUtils.isEmpty(mData.holiday.applyPageUrl) ? View.VISIBLE : View.GONE);
        mItemViewHolder.myHolidayGoBtn.setVisibility(mData.holiday != null && !TextUtils.isEmpty(mData.holiday.applyPageUrl) ? View.VISIBLE : View.GONE);
        String surplus = mData.holiday != null && !TextUtils.isEmpty(mData.holiday.surplus) ? mData.holiday.surplus : "0";
        mItemViewHolder.myHolidayValueTv.setText(NumUtil.formatTo2(surplus));
        mItemViewHolder.myHolidayLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mData.holiday != null && !TextUtils.isEmpty(mData.holiday.detailPageUrl)) {
                    Router.build(mData.holiday.detailPageUrl).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put("appId", mData.holiday.detailAppId);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                }
            }
        });
        mItemViewHolder.myHolidayApplyLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mData.holiday != null && !TextUtils.isEmpty(mData.holiday.applyPageUrl)) {
                    Router.build(mData.holiday.applyPageUrl).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put("appId", mData.holiday.applyAppId);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                }
            }
        });

        //福利积分
        mItemViewHolder.myBalanceGoTv.setVisibility(mData.scores != null && !TextUtils.isEmpty(mData.scores.url) ? View.VISIBLE : View.GONE);
        mItemViewHolder.myBalanceGoBtn.setVisibility(mData.scores != null && !TextUtils.isEmpty(mData.scores.url) ? View.VISIBLE : View.GONE);
        String value = (mData.scores != null && !TextUtils.isEmpty(mData.scores.balance)) ? mData.scores.balance : "0";
        mItemViewHolder.myBalanceValueTv.setText(NumUtil.formatTo2(value));
        mItemViewHolder.myBalanceLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mData.scores != null && !TextUtils.isEmpty(mData.scores.url)) {
                    Router.build(mData.scores.url).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put("appId", mData.scores.appId);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                }
            }
        });
    }

    private void showWalletSecret(boolean isShow) {
        if (!isShow) {
            mItemViewHolder.myWalletValueTv.setText("*******");
            mItemViewHolder.myWalletJdValueTv.setText("*******");
            mItemViewHolder.myWalletCardValueTv.setText("*******");
            mItemViewHolder.myWalletEyeBtn.setText(R.string.icon_general_eyeinvisible);
        } else {
            mItemViewHolder.myWalletValueTv.setText(NumUtil.formatToSepara(mData.account != null && mData.account.total != null ? (double)mData.account.total / 100 : 0));
            mItemViewHolder.myWalletJdValueTv.setText(NumUtil.formatToSepara(mData.account != null && mData.account.walletBalance != null ? (double)mData.account.walletBalance / 100 : 0));
            mItemViewHolder.myWalletCardValueTv.setText(NumUtil.formatToSepara(mData.account != null && mData.account.yikatongBalance != null ? (double)mData.account.yikatongBalance / 100 : 0));
            mItemViewHolder.myWalletEyeBtn.setText(R.string.icon_general_eye);
        }
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {
        public View myWelfareTitleLayout;
        public TextView myWelfareTitle;
        public TextView myWelfareCountTv;
        public View myWelfareCountUnit;
        public TextView myWelfareDesc;
        public View myWelfareGoBtn;
        public View myWalletLayout;
        public TextView myWalletValueTv;
        public IconFontView myWalletEyeBtn;
        public TextView myWalletJdValueTv;
        public TextView myWalletCardValueTv;
        public View myCouponGoLayout;
        public IconFontView myCouponGoBtn;
        public RecyclerView rvCoupons;
        public View noCouponsLayout;
        public View myHolidayLayout;
        public View myHolidayApplyLayout;
        public TextView myHolidayGoTv;
        public IconFontView myHolidayGoBtn;
        public TextView myHolidayValueTv;
        public View myBalanceLayout;
        public TextView myBalanceGoTv;
        public IconFontView myBalanceGoBtn;
        public TextView myBalanceValueTv;

        public ItemViewHolder(View itemView) {
            super(itemView);
            myWelfareTitleLayout = itemView.findViewById(R.id.my_welfare_title_layout);
            myWelfareTitle = itemView.findViewById(R.id.my_welfare_title);
            myWelfareCountTv = itemView.findViewById(R.id.my_welfare_count_tv);
            myWelfareCountUnit = itemView.findViewById(R.id.my_welfare_count_unit);
            myWelfareDesc = itemView.findViewById(R.id.my_welfare_desc);
            myWelfareGoBtn = itemView.findViewById(R.id.my_welfare_go_btn);

            myWalletLayout = itemView.findViewById(R.id.my_wallet_layout);
            myWalletValueTv = itemView.findViewById(R.id.my_wallet_value_tv);
            myWalletEyeBtn = itemView.findViewById(R.id.my_wallet_eye_btn);
            myWalletJdValueTv = itemView.findViewById(R.id.my_wallet_jd_value_tv);
            myWalletCardValueTv = itemView.findViewById(R.id.my_wallet_card_value_tv);
            myCouponGoLayout = itemView.findViewById(R.id.my_coupon_layout);
            myCouponGoBtn = itemView.findViewById(R.id.my_coupon_go_btn);
            rvCoupons = itemView.findViewById(R.id.rv_coupons);
            noCouponsLayout = itemView.findViewById(R.id.no_coupon_layout);
            myHolidayLayout = itemView.findViewById(R.id.my_holiday_layout);
            myHolidayApplyLayout = itemView.findViewById(R.id.my_holiday_apply_layout);
            myHolidayGoTv = itemView.findViewById(R.id.my_holiday_go_tv);
            myHolidayGoBtn = itemView.findViewById(R.id.my_holiday_go_btn);
            myHolidayValueTv = itemView.findViewById(R.id.my_holiday_value_tv);
            myBalanceLayout = itemView.findViewById(R.id.my_balance_layout);
            myBalanceGoTv = itemView.findViewById(R.id.my_balance_go_tv);
            myBalanceGoBtn = itemView.findViewById(R.id.my_balance_go_btn);
            myBalanceValueTv = itemView.findViewById(R.id.my_balance_value_tv);
        }
    }
}