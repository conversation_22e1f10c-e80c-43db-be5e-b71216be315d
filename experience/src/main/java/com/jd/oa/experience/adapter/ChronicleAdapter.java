package com.jd.oa.experience.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.ChronicleData;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ScreenUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ChronicleAdapter extends RecyclerView.Adapter<ChronicleAdapter.ViewHolder> {

    private final Context mContext;
    private final List<ChronicleData.Card> mData;
    private int mParentWidth;
    private int mCardWidth;

    public ChronicleAdapter(Context context, ArrayList<ChronicleData.Card> cards, int cardWidth) {
        mContext = context;
        mData = cards;
        mCardWidth = cardWidth;
        mParentWidth = ScreenUtil.getScreenWidth(mContext) - DensityUtil.dp2px(context, 16);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        mParentWidth = parent.getMeasuredWidth();
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_experience_section_chronicle_card, parent, false);
        return new ChronicleAdapter.ViewHolder(view);
    }

    @SuppressLint("DefaultLocale")
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        if (mData == null || mData.isEmpty() || position < 0) {
            return;
        }

        final ChronicleData.Card cardData = mData.get(position);
        if (cardData == null) {
            return;
        }

        //第一个和最后一个，额外增加边距
        int defaultMargin = mContext.getResources().getDimensionPixelSize(R.dimen.chronicle_card_margin);
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)holder.itemView.getLayoutParams();
        if (position == 0) {
            lp.leftMargin = (mParentWidth - mCardWidth) / 2;
            lp.rightMargin = defaultMargin;
        } else if (position == mData.size() - 1) {
            lp.leftMargin = defaultMargin;
            lp.rightMargin = (mParentWidth - mCardWidth) / 2;
        } else {
            lp.leftMargin = defaultMargin;
            lp.rightMargin = defaultMargin;
        }
        holder.mCardLayout.setLayoutParams(lp);

        holder.itemView.setTag(cardData.joyUrl);

        RequestOptions requestOptions = new RequestOptions().placeholder(R.drawable.jdme_bg_chronicle_default).error(R.drawable.jdme_bg_chronicle_default).diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(mContext).load(cardData.imageUrl).apply(requestOptions).into(holder.mBkGndIv);

        //渐变色
        int startColor = ColorUtil.parseColor(cardData.startColor, 0xFFEBEBEB);
        int endColor = ColorUtil.parseColor(cardData.endColor, 0xFFEBEBEB);
        final int[] colors = {startColor, endColor};
        GradientDrawable bkGndDrawable = new GradientDrawable(GradientDrawable.Orientation.TL_BR, colors);
        bkGndDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        holder.mGradient.setImageDrawable(bkGndDrawable);

        holder.mIndexTv.setText(String.format("%d/%d", position + 1, mData.size()));
        holder.mTitleTv.setText(cardData.eventTitle);
        holder.mDetailTv.setText(cardData.eventContent);

        //日期渐变色
        GradientDrawable dateDrawable = new GradientDrawable(GradientDrawable.Orientation.TL_BR,
                new int[]{startColor & 0x00ffffff | 0xCC000000, endColor & 0x00ffffff | 0xCC000000});
        dateDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        dateDrawable.setCornerRadii(new float[] {0, 0, DensityUtil.dp2px(mContext, 11), DensityUtil.dp2px(mContext, 11), 0, 0, 0, 0});
        holder.mDateTv.setBackground(dateDrawable);
        if (!TextUtils.isEmpty(cardData.eventTime)) {
            holder.mDateTv.setVisibility(View.VISIBLE);
            holder.mDateTv.setText(cardData.eventTime);
        } else {
            holder.mDateTv.setVisibility(View.GONE);
        }

        //点击预埋
        holder.itemView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(cardData.eventUrl)) {
                    Router.build(cardData.eventUrl).go(mContext);
                    ExpJDMAUtil.onMomentEventClick(ExpJDMAConstants.EXP_MAIN_MOMENTS_MEMORABILIA_CLICK, new HashMap<String, String>());
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mData != null ? mData.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        public View mCardLayout;
        public ImageView mBkGndIv;
        public ImageView mGradient;
        public TextView mIndexTv;
        public TextView mTitleTv;
        public TextView mDetailTv;
        public TextView mDateTv;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mCardLayout = itemView.findViewById(R.id.card_layout);
            mBkGndIv = itemView.findViewById(R.id.bkgnd_iv);
            mGradient = itemView.findViewById(R.id.bkgnd_gradient);
            mIndexTv = itemView.findViewById(R.id.index_tv);
            mTitleTv = itemView.findViewById(R.id.title_tv);
            mDetailTv = itemView.findViewById(R.id.detail_tv);
            mDateTv = itemView.findViewById(R.id.date_tv);
        }
    }
}
