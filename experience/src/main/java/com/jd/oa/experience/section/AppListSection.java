package com.jd.oa.experience.section;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.AppListData;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.repo.AppListRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.PagerSnapAdapter;
import com.jd.oa.experience.view.PagerSnapRecyclerView;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.dialog2.NormalDialog;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class AppListSection extends StatelessSection implements Destroyable {

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private AppListSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private AppListRepo repo;
    private AppListData mData;
    private AppService appService = AppJoint.service(AppService.class);

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                default:
                    break;
            }
        }
    };

    public AppListSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_app_list)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = AppListRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        AppListData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getAppListData(new LoadDataCallback<AppListData>() {
            @Override
            public void onDataLoaded(AppListData appListData) {
                showData(appListData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(AppListData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new AppListSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mTitleView.setTitle(true, mData.icon, mData.title, mData.jumpUrl, mData.jumpText);
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        });

        mItemViewHolder.mQrScanLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mContext instanceof Activity) {
                    Intent action = new Intent(mContext, mContext.getClass());
                    action.putExtra(Constant.SHORTCUT_FROM, Constant.SHORTCUT_FLAG);
                    action.putExtra(Constant.SHORTCUT_ACTION, Constant.SHORTCUT_ID_SCAN);
                    appService.onDoShortcutAction((Activity) mContext, action);
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.QUCInfo.MAIN_QUC_SCAN.keyName, ExpJDMAConstants.QUCInfo.MAIN_QUC_SCAN.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_QUC, map);
                }
            }
        });
        mItemViewHolder.mPassCodeLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_EXP_PROFILE_OPEN_QRCODE));
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.QUCInfo.MAIN_QUC_PASS.keyName, ExpJDMAConstants.QUCInfo.MAIN_QUC_PASS.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_QUC, map);
            }
        });
        mItemViewHolder.mPayCodeLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!(mContext instanceof Activity)) {
                    return;
                }

                checkJdPin(new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        if (TextUtils.isEmpty(s)) {
                            showUnbindPinDialog();
                        } else {
                            appService.checkBindWallet(new AppService.IBindWalletCallback() {
                                @Override
                                public void call(boolean isBind) {
                                    if (isBind) {
                                        Intent action = new Intent(mContext, mContext.getClass());
                                        action.putExtra(Constant.SHORTCUT_FROM, Constant.SHORTCUT_FLAG);
                                        action.putExtra(Constant.SHORTCUT_ACTION, Constant.SHORTCUT_ID_EMPLOYEE_CARD);
                                        appService.onDoShortcutAction((Activity) mContext, action);

                                        Map<String, String> map = new HashMap<>();
                                        map.put(ExpJDMAConstants.QUCInfo.MAIN_QUC_PAY.keyName, ExpJDMAConstants.QUCInfo.MAIN_QUC_PAY.KeyValue);
                                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_QUC, map);
                                    } else {
                                        showUnbindWalletDialog();
                                    }
                                }
                            });
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        showUnbindPinDialog();
                    }
                });
            }
        });
        mItemViewHolder.mWalletLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                Router.build(DeepLink.WALLET).go(mContext, new RouteNotFoundCallback(mContext));
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.QUCInfo.MAIN_QUC_WALLET.keyName, ExpJDMAConstants.QUCInfo.MAIN_QUC_WALLET.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_QUC, map);
            }
        });

        if (mData.appList == null) {
            mData.appList = new ArrayList<>();
        }
        mItemViewHolder.mSeparator.setVisibility(!mData.appList.isEmpty() ? View.VISIBLE : View.GONE);
        mItemViewHolder.mAppListRv.setVisibility(!mData.appList.isEmpty() ? View.VISIBLE : View.GONE);
        mItemViewHolder.mAppListRv.setAdapter(new PagerSnapAdapter<AppListData.AppListItem>(
                mContext, mData.appList, PagerSnapAdapter.VIEW_TYPE_SINGLE_LINE,
                R.layout.jdme_item_experience_section_app_list_item) {
            @Override
            public void onBindView(View view, final AppListData.AppListItem data) {
                final ImageView iconIv = view.findViewById(R.id.icon);
                final ImageView gifIconIv = view.findViewById(R.id.gif_icon);
                final View tipIv = view.findViewById(R.id.iv_tip);
                TextView titleTv = view.findViewById(R.id.title);

                Glide.with(mContext).load(data.icon)
                        .error(R.drawable.jdme_bg_section_title_default_icon)
                        .placeholder(R.drawable.jdme_bg_section_title_default_icon)
                        .into(new SimpleTarget<Drawable>() {
                                  @Override
                                  public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                                      if (resource instanceof GifDrawable) {
                                          iconIv.setVisibility(View.GONE);
                                          gifIconIv.setVisibility(View.VISIBLE);
                                          GifDrawable gifDrawable = (GifDrawable) resource;
                                          gifIconIv.setImageDrawable(gifDrawable);
                                          gifDrawable.setLoopCount(0);
                                          gifDrawable.start();
                                      } else {
                                          iconIv.setVisibility(View.VISIBLE);
                                          gifIconIv.setVisibility(View.GONE);
                                          iconIv.setImageDrawable(resource);
                                      }
                                  }

                                  @Override
                                  public void onLoadFailed(@Nullable Drawable errorDrawable) {
                                      iconIv.setVisibility(View.VISIBLE);
                                      gifIconIv.setVisibility(View.GONE);
                                      iconIv.setImageResource(R.drawable.jdme_bg_section_title_default_icon);
                                  }
                              }
                        );

                tipIv.setVisibility(!TextUtils.isEmpty(data.badge) && !TextUtils.equals("0", data.badge) ? View.VISIBLE : View.INVISIBLE);
                titleTv.setText(TextUtils.isEmpty(data.title) ? "" : data.title);
                view.setOnClickListener(new NoDoubleClickListener() {
                    @Override
                    protected void onNoDoubleClick(View v) {
                        if (!TextUtils.isEmpty(data.url)) {
                            Router.build(data.url).go(mContext, new RouteNotFoundCallback(mContext));
                            Map<String, String> map = new HashMap<>();
                            map.put("appId", data.id);
                            ExpJDMAUtil.onEventClick(ExpJDMAConstants.JOY_INSIGHT_ENTRY_CLICK, map);
                        }
                    }
                });
            }
        });
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public TitleView mTitleView;
        public View mQrScanLayout;
        public View mPassCodeLayout;
        public View mPayCodeLayout;
        public View mWalletLayout;
        public View mSeparator;
        public PagerSnapRecyclerView<AppListData.AppListItem> mAppListRv;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mTitleView = itemView.findViewById(R.id.title_view);
            mQrScanLayout = itemView.findViewById(R.id.scan);
            mPassCodeLayout = itemView.findViewById(R.id.pass);
            mPayCodeLayout = itemView.findViewById(R.id.pay);
            mWalletLayout = itemView.findViewById(R.id.wallet);
            mSeparator = itemView.findViewById(R.id.separator);
            mAppListRv = itemView.findViewById(R.id.app_list_rv);
        }
    }

    private void showUnbindPinDialog() {
        if (mContext == null) {
            return;
        }
        final NormalDialog dialog = new NormalDialog(AppBase.getTopActivity(), mContext.getString(R.string.exp_unbind_pin_title), mContext.getString(R.string.exp_unbind_pin_content), mContext.getString(R.string.exp_unbind_pin_ok), mContext.getString(R.string.exp_unbind_pin_cancel));
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去绑定
                dialog.dismiss();
                appService.bindPin(mContext);
            }
        });
        dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    private void showUnbindWalletDialog() {
        if (mContext == null) {
            return;
        }
        final NormalDialog dialog = new NormalDialog(AppBase.getTopActivity(), mContext.getString(R.string.exp_unbind_wallet_title), mContext.getString(R.string.exp_unbind_wallet_content), mContext.getString(R.string.exp_unbind_pin_ok), mContext.getString(R.string.exp_unbind_pin_cancel));
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去绑定
                dialog.dismiss();
                String jdPin = PreferenceManager.UserInfo.getJdAccount();
                appService.bindWallet(mContext, jdPin);
            }
        });
        dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }


    public void checkJdPin(final LoadDataCallback<String> callback) {
        NetWorkManager.request(null, NetworkConstant.API_JD_PIN, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    JSONObject obj = new JSONObject(rawData);
                    if (obj.getInt("errorCode") == 0) {
                        jsonObject = obj.getJSONObject("content");
                        String jdPin = jsonObject.optString("jdPin");
                        callback.onDataLoaded(jdPin);
                    } else {
                        onFailure(obj.getString("errorMsg"), ErrorCode.CODE_RETURN_ERROR);
                    }
                } catch (Exception e) {
                    onFailure("failed", ErrorCode.CODE_PARSE_ERROR);
                }
            }

            @Override
            public void onFailure(String errorMsg, int errorCode) {
                callback.onDataNotAvailable(errorMsg, errorCode);
            }
        }), null);
    }
}
