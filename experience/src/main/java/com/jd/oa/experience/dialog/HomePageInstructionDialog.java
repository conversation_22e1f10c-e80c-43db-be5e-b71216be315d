package com.jd.oa.experience.dialog;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.experience.R;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.TabletUtil;

import cn.com.libsharesdk.BottomSheetDialog2;

public class HomePageInstructionDialog extends BottomSheetDialog2 {

    private Context mContext;
    private FrameLayout mWebViewContainer;
    private boolean mIsSelf;
    private JMEWebview mWebView;

    private final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            //设置高度
            View view = findViewById(R.id.instruction_dialog);
            if (view != null) {
                ViewGroup.LayoutParams lp = view.getLayoutParams();
                lp.height = DisplayUtil.getScreenHeight(mContext) - DensityUtil.dp2px(mContext, 50);
                view.setLayoutParams(lp);
            }
        }
    };

    public HomePageInstructionDialog(@NonNull Context context, boolean isSelf, JMEWebview webView) {
        this(context, R.style.BottomSheetEdit, isSelf, webView);
    }

    public HomePageInstructionDialog(@NonNull Context context, int theme, boolean isSelf, JMEWebview webView) {
        super(context, theme);
        mContext = context;
        mIsSelf = isSelf;
        mWebView = webView;
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_exp_dialog_homepage_instruction, null);
        setSkipCollapsed(true);
        setContentView(view);
        //设置背景
        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            parent.setBackgroundResource(android.R.color.transparent);
        }
        if (getWindow() != null) {
            getWindow().getDecorView().setBackgroundColor(Color.TRANSPARENT);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.gravity = Gravity.BOTTOM;
            getWindow().setAttributes(layoutParams);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        //设置高度
        View view = findViewById(R.id.instruction_dialog);
        if (view != null) {
            ViewGroup.LayoutParams lp = view.getLayoutParams();
            lp.height = DisplayUtil.getScreenHeight(mContext) - DensityUtil.dp2px(mContext, 50);
            view.setLayoutParams(lp);
        }

        TextView title = findViewById(R.id.title);
        if (title != null) {
            title.setText(mIsSelf ? mContext.getString(R.string.exp_homepage_instruction_title_mine) : mContext.getString(R.string.exp_homepage_instruction_title_other));
        }

        View close = findViewById(R.id.close);
        if (close != null) {
            close.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
        }

        mWebViewContainer = findViewById(R.id.webview_container);
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).registerReceiver(mBroadcastReceiver, new IntentFilter(TabletUtil.ACTION_SPLIT_MODE_CHANGE));
        if (mWebViewContainer != null && mWebView != null) {
            mWebViewContainer.removeAllViews();
            mWebViewContainer.addView(mWebView);
        }
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(mBroadcastReceiver);
        if (mWebViewContainer != null) {
            mWebViewContainer.removeAllViews();
        }
    }
}
