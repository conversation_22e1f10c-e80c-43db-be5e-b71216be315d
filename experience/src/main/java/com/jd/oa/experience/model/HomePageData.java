package com.jd.oa.experience.model;

import java.util.List;

public class HomePageData {

    public String bgImage;
    public UserInfo userInfo;
    public String visitedCount;
    public Liked liked;
    public Tags tags;
    public Badges badges;
    //public List<String> commonTags;
    //public List<RecentContact> contactsList;
    public Instruction userManual;

    public static class UserInfo {
        public String realName;
        public String userName;
        public String avatar;
        public String pendant;
        public String ddAppId;
        public String teamId;
        public String userId;
    }

    public static class Liked {
        public String count;
        public Boolean hasNew;
        public String url;
    }

    public static class Tags {
        public String url;
        public List<String> list;
    }

    public static class Badges {
        public String url;
        public Integer count;
        public List<BadgeItem> defaultList;
        public List<BadgeItem> userCustomList;
    }

    public static class BadgeItem {
        public String id;
        public String url;
    }
/*

    public static class RecentContact {
        public String avatar;
        public String realName;
        public String userName;//暂时用不上
        public String url;
    }
*/

    public static class Instruction {
        public String previewUrl;//http://
        public String openUrl;//jdme://
        public Boolean isShow;
        public WorkCard workShow;
    }

    public static class WorkCard {
        public Integer totalNum;
        public Integer finishNum;
        public CardList cardList;
    }

    public static class CardList {
        public Duty responsibility;
        public OKR aim;
    }

    public static class Duty {
        public String cardId;
        public List<Content> content;

        public static class Content {
            public String questionId;
            public String questionCode;
            public String answerText;
            public List<String> answerKeyword;
        }
    }

    public static class OKR {
        public String cardId;
        public Content content;

        public static class Content {
            public Integer targetNum;
            public Boolean fromIsGray;
            public Boolean toIsGray;
        }
    }
/*    {
	"content": {
		"bgImage": "https://storage.jd.com/xxxxxxx.png",
		"userInfo": {
			"userId": "123",
			"teamId": "456",
			"ddAppId": "ee",
			"userName": "huangzhen122",
			"realName": "奋斗的Joy",
			"avatar": "https://exp.jd.com/abc.jpg",
			"pendant": "https://pendant.jd.com/1.jpg"
		},
		"visitedCount": "0",
		"liked": {
			"count": "0",
			"hasNew": true,
			"url": "jdme://202208020013"
		},
		"tags": {
			"url": "",
			"list": [
				"京东总部一号楼"
			]
		},
		"badges": {
			"url": "jdme://abc",
			"count": 8,
			"defaultList": [{
					"id": "Tw3pH1T9zJnvEkwD5mky7",
					"url": "https://storage.360buyimg.com/jd.jme.client/images/c3-icon.png"
				},
				{
					"id": "Tw3pH1T9zJnvEkwD5mky7",
					"url": "https://storage.360buyimg.com/jd.jme.client/images/c3-icon.png"
				}
			],
			"userCustomList": [{
					"id": "Tw3pH1T9zJnvEkwD5mky7",
					"url": "https://storage.360buyimg.com/jd.jme.client/images/c3-icon.png"
				},
				{
					"id": "Tw3pH1T9zJnvEkwD5mky7",
					"url": "https://storage.360buyimg.com/jd.jme.client/images/c3-icon.png"
				}
			]
		},
		"userManual": {
			"previewUrl": "jdme://rn/201909020601?routeTag=document_edit&rnStandalone=2&page_id=J4302HhMOSmAF7fTsFHh",
			"openUrl": "https://joyspace.jd.com/sheets/J4302HhMOSmAF7fTsFHh",
			"isShow": true,
			"workShow":{
                "totalNum":5,
                "finishNum":3,
                "cardList":{
                    "responsibility":{
                        "cardId":"1d1312fsw972",
                        "content":[
                            {
                                "questionId":"",
                                "answerText":"",
                                "answerKeyword":[
                                    "kw1",
                                    "kw2"
                                ]
                            }
                        ]
                    },
                    "aim":{
                        "cardId":"mbvi2379fgj120934ey",
                        "content":{
                            "targetNum":6
                        }
                    }
                }
            }
		}
	},
	"errorMsg": "",
	"errorCode": "0"
}*/
}
