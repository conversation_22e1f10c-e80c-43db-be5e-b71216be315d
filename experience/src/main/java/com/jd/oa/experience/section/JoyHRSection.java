package com.jd.oa.experience.section;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_JoyHR;
import static com.jd.oa.experience.util.Constants.JOYHR_CACHE_KEY;
import static com.jd.oa.experience.util.Constants.JOYHR_SECTION_API;

import android.content.Context;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.experience.GridSpacingItemDecoration;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.JoyHRAdapter;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.JoyHRData;
import com.jd.oa.experience.model.RefreshObserver;
import com.jd.oa.experience.repo.JDHRRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class JoyHRSection extends StatelessSection implements Destroyable, RefreshObserver {

    private final Context mContext;
    private final String mTabCode;
    private boolean mIsFirst;
    private final SectionedRecyclerViewAdapter mAdapter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final JDHRRepo<JoyHRData> repo;
    private JoyHRData mData;

    private final int mColumnCount = 3; //列数量
    private final int mColumnMargin = 8; //列间距
    private final int mRowMargin = 8; //行间距

    private final String TAG = "JoyHRSection";

    public JoyHRSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder().
                headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_joyhr)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = JDHRRepo.create(JOYHR_CACHE_KEY, new TypeToken<JoyHRData>(){});

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        JoyHRData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.fetchData(null, JOYHR_SECTION_API, new LoadDataCallback<JoyHRData>() {
            @Override
            public void onDataLoaded(JoyHRData joyHRData) {
                showData(joyHRData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }

    private void showData(JoyHRData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        refreshUI();
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        if (mData.items == null || mData.items.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            lp.setMargins(0, 0, 0, 0);
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        int defaultMargin = DensityUtil.dp2px(mContext, 8);
        lp.setMargins(defaultMargin, 0, defaultMargin, defaultMargin);
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);
        mItemViewHolder.mRecyclerView.setLayoutManager(new GridLayoutManager(mContext, mColumnCount));
        // 先清除旧的装饰器
        while (mItemViewHolder.mRecyclerView.getItemDecorationCount() > 0) {
            try {
                mItemViewHolder.mRecyclerView.removeItemDecorationAt(0);
            } catch (Exception e) {
                MELogUtil.localE(TAG, "移除间距装饰器失败");
                break;
            }
        }
        mItemViewHolder.mRecyclerView.addItemDecoration(new GridSpacingItemDecoration(
                mColumnCount,
                DensityUtil.dp2px(mContext, mRowMargin),
                DensityUtil.dp2px(mContext, mColumnMargin)));
        mItemViewHolder.mRecyclerView.setAdapter(new JoyHRAdapter(mData.items));
        mItemViewHolder.mTitle.setText(mData.title);
        mItemViewHolder.mMoreText.setText(mData.jumpText);
        if (TextUtils.isEmpty(mData.jumpUrl)) {
            mItemViewHolder.mMoreText.setVisibility(View.GONE);
            mItemViewHolder.mMoreIcon.setVisibility(View.GONE);
        } else {
            mItemViewHolder.mMoreText.setVisibility(View.VISIBLE);
            mItemViewHolder.mMoreIcon.setVisibility(View.VISIBLE);
        }
        mItemViewHolder.mHeaderContainer.setOnClickListener(new AvoidFastClickListener(1500) {
            @Override
            public void onAvoidedClick(View view) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    JDMAUtils.clickEvent(mobile_EXP_Main_JoyHR, mobile_EXP_Main_JoyHR, null);
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback());
                }
            }
        });
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    @Override
    public void onRefresh() {
        loadData();
    }

    @Override
    public void onConfigChanged() {
        refreshUI();
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public ConstraintLayout mHeaderContainer;
        public RecyclerView mRecyclerView;
        public TextView mTitle;
        public TextView mMoreText;
        public TextView mMoreIcon;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mHeaderContainer = itemView.findViewById(R.id.cl_joyhr_header);
            mRecyclerView = itemView.findViewById(R.id.rv_joyhr_cards);
            mTitle = itemView.findViewById(R.id.tv_header_title);
            mMoreText = itemView.findViewById(R.id.tv_chart_more);
            mMoreIcon = itemView.findViewById(R.id.iv_chart_more);
        }
    }
}
