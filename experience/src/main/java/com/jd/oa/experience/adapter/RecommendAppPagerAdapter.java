package com.jd.oa.experience.adapter;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.PagerAdapter;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.JDMAConstants;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.StringUtils;

import java.util.List;
import java.util.Map;

public class RecommendAppPagerAdapter extends PagerAdapter {
    private Context mContext;
    private List<List<RecommendData.BodyItem>> mData;
    private boolean hasFooter;
    private RecommendData cardItem;
    private int cardPosition;

    public RecommendAppPagerAdapter(Context context, List<List<RecommendData.BodyItem>> data, boolean hasFooter
            , RecommendData cardItem, int cardPosition) {
        mContext = context;
        mData = data;
        this.hasFooter = hasFooter;
        this.cardItem = cardItem;
        this.cardPosition = cardPosition;
    }

    @NonNull
    @Override
    public RecyclerView instantiateItem(@NonNull ViewGroup container, final int pagePosition) {
        RecyclerView recyclerView = new RecyclerView(mContext);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext, RecyclerView.VERTICAL, false);
        recyclerView.setLayoutManager(layoutManager);
        final RecommendAppAdapter adapter = new RecommendAppAdapter(mContext, mData.get(pagePosition), hasFooter, mData.size() > 1);
        recyclerView.setAdapter(adapter);
        recyclerView.setNestedScrollingEnabled(false);
        adapter.setOnItemClickListener(new RecommendAppAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(RecommendData.BodyItem item, int position) {
                if (StringUtils.isNotEmptyWithTrim(item.url)) {
                    int itemIndex = pagePosition > 0 ? mData.get(0).size() * pagePosition + position : position;
                    ExpJDMAUtil.onInsightsEventClick(cardItem.id, cardItem.tplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_BODY.KeyValue,item.id, String.valueOf(itemIndex), String.valueOf(cardPosition), item.id);
                    if (DeepLink.NET_DISK_OLD.equals(item.url)) {
                        openNetDisk(mContext, item.url);
                    } else {
                        Router.build(item.url).go(mContext, new RouteNotFoundCallback(mContext));
                    }
                }
            }
        });
        container.addView(recyclerView);
        return recyclerView;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }

    private void openNetDisk(final Context context, final String deeplink) {
        NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {}.getType());
                if (response.isSuccessful()) {
                    try {
                        Map<String, String> map = response.getData();
                        ToNetDiskBean bean = new ToNetDiskBean();
                        bean.setToken(map.get("third_token"));
                        bean.setUserCode(map.get("third_name"));
                        bean.setThirdTimestamp(map.get("third_timestamp"));
                        if (mContext instanceof Activity) {
                            AppJoint.service(AppService.class).openNetDisk((Activity) mContext, bean);
                        }
                    } catch (Exception e) {
                        Router.build(deeplink).go(context, new RouteNotFoundCallback(context));
                    }
                } else {
                    com.jd.oa.melib.ToastUtils.showInfoToast(context, "" + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                Router.build(deeplink).go(context, new RouteNotFoundCallback(context));
            }
        });
    }
}
