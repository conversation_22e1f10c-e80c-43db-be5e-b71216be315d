package com.jd.oa.experience.qrcode

import android.app.Activity
import android.app.Dialog
import android.view.WindowManager
import com.jd.oa.abtest.SafetyControlManager
import com.jd.oa.experience.constants.ExpJDMAConstants
import com.jd.oa.experience.util.ExpJDMAUtil
import com.jd.oa.utils.DisplayUtil

/**
 * 桌面icon快捷菜单-通行码，进入app，弹出dialog
 */
class GlobalDialogManager {

    private var dialog: Dialog? = null

    /**
     * 接收到用户点击桌面的快捷菜单，显示用户自己的通行码
     */
    fun showQRCodeDialog(mActivity: Activity?) {
        mActivity?.let { activity ->
            if (dialog == null) {
                dialog = ExpQRCodeDialog(activity)
                DisplayUtil.setBrightness(activity, 255)
                dialog!!.setOnDismissListener {
                    if (!SafetyControlManager.getInstance().isControlScreeShot) {
                        activity.getWindow()
                            .clearFlags(WindowManager.LayoutParams.FLAG_SECURE) //二维码弹窗消失时,启用截屏录屏
                    }
                    DisplayUtil.setBrightness(activity, -255)
                    dialog = null
                }
                dialog!!.show()
            }


            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE) //二维码弹窗弹出时,禁用截屏录屏
            val map: MutableMap<String, String> = HashMap()
            map[ExpJDMAConstants.MainInfo.MAIN_INFO_QRCODE.keyName] =
                ExpJDMAConstants.MainInfo.MAIN_INFO_QRCODE.KeyValue
            ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map)
        }
    }

    companion object {
        fun getInstance() = GlobalDialogManager()
    }
}
