package com.jd.oa.experience.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.PagerAdapter;

import com.chenenyu.router.Router;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.StringUtils;

import java.util.List;

public class RecommendSummaryPagerAdapter extends PagerAdapter {
    private int cardPosition;
    private RecommendData cardItem;
    private Context mContext;
    private List<List<RecommendData.BodyItem>> mData;
    private boolean hasFooter;

    public RecommendSummaryPagerAdapter(Context context, List<List<RecommendData.BodyItem>> data, boolean hasFooter, RecommendData cardItem, int cardPosition) {
        mContext = context;
        mData = data;
        this.hasFooter = hasFooter;
        this.cardItem = cardItem;
        this.cardPosition = cardPosition;
    }

    @NonNull
    @Override
    public RecyclerView instantiateItem(@NonNull ViewGroup container, final int pagePosition) {
        RecyclerView recyclerView = new RecyclerView(mContext);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext, RecyclerView.VERTICAL, false);
        recyclerView.setLayoutManager(layoutManager);
        SpaceItemDecoration decoration = new SpaceItemDecoration(DensityUtil.dp2px(mContext, 8));
//        recyclerView.addItemDecoration(decoration);
        RecommendSummaryAdapter adapter = new RecommendSummaryAdapter(mContext, mData.get(pagePosition), mData.size() > 1, hasFooter);
        adapter.setOnItemClickListener(new RecommendSummaryAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(RecommendData.BodyItem item, int position) {
                if (StringUtils.isNotEmptyWithTrim(item.url) && !NClick.isFastDoubleClick()) {
                    int itemIndex = pagePosition > 0 ? mData.get(0).size() * pagePosition + position : position;
                    ExpJDMAUtil.onInsightsEventClick(cardItem.id, cardItem.tplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_BODY.KeyValue, item.id, String.valueOf(itemIndex), String.valueOf(cardPosition), item.id);
                    Router.build(item.url).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        });
        recyclerView.setAdapter(adapter);
        container.addView(recyclerView);
        return recyclerView;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }
}
