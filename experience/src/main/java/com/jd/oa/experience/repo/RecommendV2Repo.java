package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.RecommendV2Data;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class RecommendV2Repo {

    private static final String RECOMMEND_V2_CACHE_KEY = "exp.Recommend.v2.repo.cache.key";
    private static RecommendV2Repo sInstance;

    private Context mContext;
    private Gson mGson;

    public static RecommendV2Repo get(Context context) {
        if (sInstance == null) {
            sInstance = new RecommendV2Repo(context);
        }
        return sInstance;
    }

    private RecommendV2Repo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public RecommendV2Data getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), RECOMMEND_V2_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        RecommendV2Data data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<RecommendV2Data>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(RecommendV2Data data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), RECOMMEND_V2_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getRecommendV2Data(final LoadDataCallback<RecommendV2Data> callback) {
        Log.i("zhn", "getRecommendV2Data");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
              /*  if (BuildConfig.DEBUG) {  //mock数据
//                    info.result = "{\"content\":{\"list\":[{\"tplType\":\"IMAGE_LIST_V2\",\"id\":\"424465237\",\"title\":\"文化活动推荐\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insightwenhua.png\",\"jumpUrl\":\"jdme://jm/biz/appcenter/202205131266\",\"jumpText\":\"查看\",\"data\":{\"footer\":[{\"id\":\"05b18e67\",\"title\":\"Discover\",\"url\":\"jdme://jm/biz/appcenter/202205131266\"},{\"id\":\"05b18e67\",\"title\":\"Discover\",\"url\":\"jdme://jm/biz/appcenter/202205131266\"}],\"body\":{\"items\":[{\"image\":\"https://img2.baidu.com/it/u=3170243357,3097706939&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800\",\"id\":\"789\",\"url\":\"jdme://jm/biz/appcenter/wallet\"},{\"id\":\"123\",\"image\":\"https://storage.360buyimg.com/jd.jme.client/images/insight519test.png\",\"url\":\"jdme://jm/biz/appcenter/wallet\"},{\"id\":\"123\",\"image\":\"https://img0.baidu.com/it/u=679891037,2677907410&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500\",\"url\":\"jdme://jm/biz/appcenter/wallet\"}]}}},{\"tplType\":\"IMAGE_LIST_V2\",\"id\":\"424465238\",\"title\":\"文化活动推荐2\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insightwenhua.png\",\"jumpUrl\":\"jdme://jm/biz/appcenter/202205131266\",\"jumpText\":\"全部\",\"data\":{\"footer\":[{\"id\":\"05b18e67\",\"title\":\"Discover your habits\",\"url\":\"jdme://jm/biz/appcenter/202205131266\"}],\"body\":{\"items\":[{\"image\":\"https://img2.baidu.com/it/u=3170243357,3097706939&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800\",\"id\":\"789\",\"url\":\"jdme://jm/biz/appcenter/wallet\"},{\"id\":\"123\",\"image\":\"https://storage.360buyimg.com/jd.jme.client/images/insight519test.png\",\"url\":\"jdme://jm/biz/appcenter/wallet\"}]}}}]},\"errorCode\":\"0\",\"errorMsg\":\"\"}";
                    info.result ="{\"content\":{\"list\":[{\"tplType\":\"IMAGE_LIST_V2\",\"id\":\"424465237\",\"title\":\"文化活动推荐\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insightwenhua.png\",\"jumpUrl\":\"jdme://jm/biz/appcenter/202205131266\",\"jumpText\":\"查看\",\"data\":{\"footer\":[],\"body\":{\"items\":[{\"image\":\"https://storage.360buyimg.com/jd.jme.client/images/insight519test.png\",\"id\":\"789\",\"url\":\"jdme://jm/biz/appcenter/wallet\"},{\"id\":\"123\",\"image\":\"https://storage.360buyimg.com/jd.jme.client/images/insight519test.png\",\"url\":\"jdme://jm/biz/appcenter/wallet\"}]}}},{\"tplType\":\"IMAGE_LIST_V2\",\"id\":\"424465238\",\"title\":\"文化活动推荐2\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insightwenhua.png\",\"jumpUrl\":\"jdme://jm/biz/appcenter/202205131266\",\"jumpText\":\"全部\",\"data\":{\"body\":{\"items\":[{\"image\":\"https://storage.360buyimg.com/jd.jme.client/images/insight519test.png\",\"id\":\"789\",\"url\":\"jdme://jm/biz/appcenter/wallet\"}]}}}]},\"errorCode\":\"0\",\"errorMsg\":\"\"}";
                }*/
                ApiResponse<RecommendV2Data> response = ApiResponse.parse(info.result, new TypeToken<RecommendV2Data>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getRecommendList, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getRecommendList, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getRecommendList, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getRecommendList");
    }
}
