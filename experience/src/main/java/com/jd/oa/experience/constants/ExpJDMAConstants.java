package com.jd.oa.experience.constants;

public class ExpJDMAConstants {

    public static String EXP_MAIN_HELP = "EXP_Main_Help"; //个人中心-帮助
    public static String EXP_MAIN_SETTINGS = "EXP_Main_Settings"; //个人中心-设置
    public static String EXP_MAIN_INFO = "EXP_Main_Info"; //个人中心-基本信息
    public static String EXP_MAIN_HOME_INFO = "EXP_Main_Home_Info"; //个人主页-基本信息

    public static String EXP_MAIN_CULTURE = "EXP_Main_Culture"; //个人中心-文化成就
    public static String EXP_MAIN_DIGITAL = "EXP_Main_Digital"; //个人中心-数值入口
    public static String EXP_MAIN_ENTRY = "EXP_Main_Entry"; //个人中心-应用入口
    public static String EXP_MAIN_INSIGHTS = "EXP_Main_Insights"; //个人中心-洞察卡片
    public static String EXP_MAIN_TAB = "EXP_Main_Tab"; //个人中心-Tab
    public static String EXP_MAIN_DURATION = "EXP_Main_Duration"; //个人中心-浏览时长
    public static String EXP_MAIN_ENT_DUR = "EXP_Main_EntDur"; //个人中心-入口访问时长
    public static String EXP_MAIN_INS_DUR = "EXP_Main_InsDur"; //个人中心-卡片访问时长
    public static String EXP_MAIN_THEME = "EXP_Main_Theme";//个人中心-换肤入口

    public static String EXP_MAIN_HOME_INSTRUCTION_VIEW = "EXP_Main_Home_Instructions_View";//个人主页-查看说明书
    public static String EXP_MAIN_HOME_CHANGE_COVER = "EXP_Main_Home_ChangeCover";//个人主页-封面更换入口
    public static String EXP_MAIN_HOME_CHANGE_COVER_CONFIRM = "EXP_Main_Home_ChangeCover_Confirm";//个人主页-封面更换确定
    public static String EXP_MAIN_HOME_START = "EXP_Main_Home_Start";//个人主页-我的说明书-马上开始
    public static String EXP_MAIN_HOME_SEND_MANUAL = "EXP_Main_Home_SendManual";//个人主页-我的说明书-分享说明书

    public static String EXP_MAIN_HOME_MEDAL = "EXP_Main_Home_Medal_Sort";//个人主页-徽章
    public static String EXP_MAIN_HOME_MEDAL_OTHER = "EXP_Main_Home_Medal_View";//个人主页-徽章

    public static String EXP_MAIN_HOME_WORK_DUTY = "EXP_Main_Home_WorkDuty";
    public static String EXP_MAIN_HOME_WORK_GOALS = "EXP_Main_Home_WorkGoals";
    public static String EXP_MAIN_HOME_WORK_DUTY_SAVE = "EXP_Main_Home_WorkDuty_Save";

    public static String JOY_INSIGHT_ENTRY_CLICK = "EXP_Main_Entry";//我的职场-常用应用-下半区
    public static String EXP_MAIN_QUC = "EXP_Main_ToolEntry";//我的职场-常用应用-上半区
    public static String EXP_MAIN_WELFARE = "EXP_Main_Welfare";//个人中心-我的福利
    public static String EXP_MAIN_WELFARE_DETAIL = "EXP_Main_Welfare_View_Detail";//个人中心-服务
    public static String EXP_MAIN_WELFARE_DETAIL_NEW = "EXP_Main_Welfare_View_Detail_New";//个人中心-服务
    public static String EXP_MAIN_SERVICE = "EXP_Main_Service";//个人中心-服务
    public static String EXP_MAIN_SERVICE_DETAIL = "EXP_Main_Service_View_Detail";//个人中心-服务-查看更多

    public static String EXP_MAIN_TAB_ENTRY = "EXP_Main_TabEntry";
    public static String EXP_MAIN_DEV_EXPLORE = "EXP_Main_Development_Exploration";
    public static String EXP_MAIN_DEV_RESOURCE = "EXP_Main_Development_Resource";
    public static String EXP_MAIN_DEV_STUDYMAP = "EXP_Main_Development_ResourceMapView";

    public static String EXP_MAIN_INTERACTION = "EXP_Main_Interaction";//个人中心-我的互动
    public static String EXP_MAIN_DUTY = "EXP_Main_WorkDuty";//个人中心-说明书
    public static String EXP_MAIN_OKR = "EXP_Main_WorkGoals";//个人中心-说明书
    public static String EXP_MAIN_MANUAL = "EXP_Main_Manual";//个人中心-说明书

    public static String EXP_MAIN_MOMENTS_MEMORABILIA = "EXP_Main_Moments_Memorabilia";
    public static String EXP_MAIN_MOMENTS_MEMORABILIA_CLICK = "EXP_Main_Moments_Memorabilia_click";
    public static String EXP_MAIN_MOMENTS_MEMORABILIA_DETAIL = "EXP_Main_Moments_Memorabilia_Detail";

    public static String EXP_MAIN_WORK_TIME = "EXP_Main_WorkTime";//个人中心-我的效率-工作时间分配-查看小结落地页
    public static String EXP_MAIN_MEETING_EFFICIENCY = "EXP_Main_MeetingEfficiency";//个人中心-我的效率-会议效率-查看会议效率落地页

    public static String EXP_MAIN_BANNER_DETAIL = "EXP_Main_Activity_header";//个人中心-标题点击
    public static String EXP_MAIN_BANNER_ITEM_CLICK = "EXP_Main_Activity_body_item";//个人中心-item 点击


    public static String EXP_MAIN_BANNER_ITEM_DETAIL = "EXP_Main_Custom_Banner";//个人中心-标题点击
    public static String EXP_MAIN_EDIT_INFO_INFO = "EXP_Main_EditInfo_Info";//个人中心-标题点击

    public static String EXP_MAIN_HOME_WORK_DUTY_PAGE = "EXP_Main_Home_WorkDuty_Page";//个人中心-标题点击

    public static String EXP_MAIN_EDIT_INFO = "EXP_Main_EditInfo";//用户信息编辑页面

    public  enum MainInfo {
        MAIN_INFO_AVATAR("clickId", "1"),
        MAIN_INFO_NAME("clickId", "2"),
        MAIN_INFO_IN_WORK_DAYS("clickId", "3"),
        MAIN_INFO_IN_WORK_YEARS("clickId", "4"),
        MAIN_INFO_BIRTHDAY("clickId", "5"),
        MAIN_INFO_QRCODE("clickId", "6"),
        MAIN_INFO_SIGNATURE("clickId", "7"),
        MAIN_INFO_TAGS("clickId", "8"),
        MAIN_INFO_LIKED("clickId", "9"),
        MAIN_INFO_LIKE("clickId", "10"),
        MAIN_INFO_HOMEPAGE("clickId", "11"),
        MAIN_INSIGHTS_HEADER("header区", "1"),
        MAIN_INSIGHTS_BODY("body区", "2"),
        MAIN_INSIGHTS_FOOTER("footer区", "3"),
        MAIN_TAB_ENTRY_CAREER("clickId", "1"),
        MAIN_TAB_ENTRY_DEVELOP("clickId", "2"),
        MAIN_TAB_ENTRY_COOPERATION("clickId", "3"),
        MAIN_TAB_ENTRY_EFFICIENCY("clickId", "4");
        public String keyName;
        public String KeyValue;

        MainInfo(String keyName, String KeyValue) {
            this.keyName = keyName;
            this.KeyValue = KeyValue;
        }
    }

    public  enum MainHomeInfo {
        MAIN_HOME_INFO_COVER("clickId", "1"),
        MAIN_HOME_INFO_FEELING("clickId", "2"),
        MAIN_HOME_INFO_TAGS("clickId", "3"),
        MAIN_HOME_INFO_LIKED("clickId", "4"),
        MAIN_HOME_INFO_EDIT_INFO("clickId", "5"),
        MAIN_HOME_INFO_MSG("clickId", "6"),
        MAIN_HOME_INFO_CARD("clickId", "7");
        public String keyName;
        public String KeyValue;

        MainHomeInfo(String keyName, String KeyValue) {
            this.keyName = keyName;
            this.KeyValue = KeyValue;
        }
    }

    public enum QUCInfo {
        MAIN_QUC_SCAN("clickId", "1"),
        MAIN_QUC_PASS("clickId", "2"),
        MAIN_QUC_PAY("clickId", "3"),
        MAIN_QUC_WALLET("clickId", "4");

        public String keyName;
        public String KeyValue;

        QUCInfo(String keyName, String KeyValue) {
            this.keyName = keyName;
            this.KeyValue = KeyValue;
        }
    }

    public enum ManualInfo {
        MANUAL_CREATE("clickId", "1"),
        MANUAL_EDIT("clickId", "2"),
        MANUAL_SWITCH("clickId", "3");

        public String keyName;
        public String KeyValue;

        ManualInfo(String keyName, String KeyValue) {
            this.keyName = keyName;
            this.KeyValue = KeyValue;
        }
    }

    public enum InteractionInfo {
        LIKE("clickId", "1"),
        LIKED("clickId", "2"),
        HOMEPAGE("clickId", "3");

        public String keyName;
        public String KeyValue;

        InteractionInfo(String keyName, String KeyValue) {
            this.keyName = keyName;
            this.KeyValue = KeyValue;
        }
    }
}
