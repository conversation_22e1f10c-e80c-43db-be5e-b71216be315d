package com.jd.oa.experience.dialog;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDialog;

import com.jd.oa.experience.R;

public class ExpCnofirmDialog extends AppCompatDialog {

    public interface OnClickListener {
        void onClickOk();

        void onClickCancel();
    }

    private String mContentText;
    private String mConfirmText;
    private String mCancelText;
    private OnClickListener mListener;

    public ExpCnofirmDialog(Context context, String contentText, String confirmText, String cancelText) {
        super(context, R.style.ExpDialogStyle);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_exp_dialog_confirm);
        Window window = getWindow();
        if (window != null) {
            if (window.getDecorView() != null) {
                window.getDecorView().setBackgroundColor(Color.TRANSPARENT);
            }
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.CENTER;
            window.setAttributes(layoutParams);
        }
        mConfirmText = confirmText;
        mContentText = contentText;
        mCancelText = cancelText;
        setCancelable(true);
        setCanceledOnTouchOutside(true);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            TextView textView = findViewById(R.id.hint_text_view);
            textView.setText(mContentText);
            TextView confirm = findViewById(R.id.confirm);
            confirm.setText(mConfirmText);
            confirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    if (mListener != null) {
                        mListener.onClickOk();
                    }
                }
            });

            TextView cancel = findViewById(R.id.cancel);
            View divider = findViewById(R.id.divider);
            if (TextUtils.isEmpty(mCancelText)) {
                cancel.setVisibility(View.GONE);
                divider.setVisibility(View.GONE);
                return;
            }
            cancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    if (mListener != null) {
                        mListener.onClickCancel();
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setOnClickListener(OnClickListener listener) {
        mListener = listener;
    }

    @Override
    public void show() {
        super.show();
    }
}
