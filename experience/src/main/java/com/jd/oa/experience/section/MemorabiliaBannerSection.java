package com.jd.oa.experience.section;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_Moments;
import static com.jd.oa.experience.util.Constants.MY_EVENT_V3_CACHE_KEY;
import static com.jd.oa.experience.util.Constants.MY_EVENT_V3_SECTION_API;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.MemorabiliaBannerData;
import com.jd.oa.experience.model.RefreshObserver;
import com.jd.oa.experience.repo.JDHRRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/**
 * 个人中心-大事记
 */
public class MemorabiliaBannerSection extends StatelessSection implements Destroyable, RefreshObserver {

    private final Context mContext;
    private final String mTabCode;
    private boolean mIsFirst;
    private final SectionedRecyclerViewAdapter mAdapter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final JDHRRepo<MemorabiliaBannerData> repo;
    private List<MemorabiliaBannerData> dataList;

    public MemorabiliaBannerSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder().
                headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_banner_item_memorabilia)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = JDHRRepo.create(MY_EVENT_V3_CACHE_KEY, new TypeToken<MemorabiliaBannerData>(){});

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        MemorabiliaBannerData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.fetchData(null, MY_EVENT_V3_SECTION_API,new LoadDataCallback<MemorabiliaBannerData>() {
            @Override
            public void onDataLoaded(MemorabiliaBannerData data) {
                showData(data);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }

    private void showData(MemorabiliaBannerData data) {
        if (dataList == null) {
            dataList = new ArrayList<>();
        } else {
            dataList.clear();
        }
        if (data != null) {
            dataList.add(data);
        }

        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        refreshUI();
    }

    private void refreshUI() {

        if (mItemViewHolder == null) {
            return;
        }

        if (dataList == null || dataList.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            lp.setMargins(0, 0, 0, 0);
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        int defaultMargin = DensityUtil.dp2px(mContext, 8);
        lp.setMargins(defaultMargin, 0, defaultMargin, defaultMargin);
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        MemorabiliaBannerData dataItem = dataList.get(0);
        //加载大事记图片
        if (dataItem != null) {
            Glide.with(mContext)
                    .load(dataItem.img)
                    .error(R.drawable.jdme_memorabilia_place)
                    .into(mItemViewHolder.ivImg);

            mItemViewHolder.ivImg.setOnClickListener(new NoDoubleClickListener() {
                @Override
                protected void onNoDoubleClick(View v) {
                    String imgJumpUrl = dataItem.jumpUrl;
                    if (!TextUtils.isEmpty(imgJumpUrl)) {
                        Router.build(imgJumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                        HashMap<String, String> values = new HashMap<>();
                        values.put("timestamp", "" + System.currentTimeMillis());
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_BANNER_ITEM_CLICK, values);
                        JDMAUtils.clickEvent(mobile_EXP_Main_Moments, mobile_EXP_Main_Moments, null);
                    }
                }
            });
        }


    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    @Override
    public void onRefresh() {
        loadData();
    }

    @Override
    public void onConfigChanged() {
        refreshUI();
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {
        //显示图片
        public AppCompatImageView ivImg;

        public ItemViewHolder(View itemView) {
            super(itemView);
            ivImg = itemView.findViewById(R.id.iv_img);
        }
    }
}
