package com.jd.oa.experience.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.AppBase;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.HomePageData;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class HomePageBadgeAdapter extends RecyclerView.Adapter<HomePageBadgeAdapter.ViewHolder> {

    private final Context mContext;
    private final List<HomePageData.BadgeItem> mBadges;

    public HomePageBadgeAdapter(Context context, List<HomePageData.BadgeItem> badges) {
        mContext = context;
        mBadges = badges;
    }

    @NonNull
    @Override
    public HomePageBadgeAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_activity_my_homepage_badge_item, parent, false);
        return new HomePageBadgeAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull HomePageBadgeAdapter.ViewHolder holder, int position) {
        if (position >= mBadges.size()) {
            return;
        }
        final HomePageData.BadgeItem badgeItem = mBadges.get(position);
        if (badgeItem == null) {
            return;
        }

        ImageLoader.load(AppBase.getAppContext(), holder.ivBadge, badgeItem.url, true, R.drawable.exp_badge_default, R.drawable.exp_badge_default);
    }

    @Override
    public int getItemCount() {
        return mBadges != null ? mBadges.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public ImageView ivBadge;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivBadge = itemView.findViewById(R.id.iv_badge);
        }
    }
}
