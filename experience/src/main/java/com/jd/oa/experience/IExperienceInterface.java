package com.jd.oa.experience;

import com.jd.oa.experience.model.GlobalData;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPView;

import java.util.HashMap;

public interface IExperienceInterface {
    interface View {
        boolean isAlive();
        void onGlobalData(GlobalData data, int from);
        void showGlobalCacheData(GlobalData data);
        void showEmpty();//骨架屏
        void finishRefresh();
    }

    interface Presenter {
        void getGlobalData(int from, HashMap<String ,Object> params);
        void getGlobalCache();
    }
}
