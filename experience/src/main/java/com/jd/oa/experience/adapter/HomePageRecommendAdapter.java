package com.jd.oa.experience.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.HomePageData;
import com.jd.oa.experience.util.ImageUtil;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

/*评审后为你推荐不要了*/
/*public class HomePageRecommendAdapter extends RecyclerView.Adapter<HomePageRecommendAdapter.ViewHolder> {

    private final Context mContext;
    private List<HomePageData.RecentContact> mData;

    public HomePageRecommendAdapter(Context context, List<HomePageData.RecentContact> data) {
        mContext = context;
        mData = data;
    }

    @NonNull
    @Override
    public HomePageRecommendAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_experience_homepage_recommended, parent, false);
        return new HomePageRecommendAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull final HomePageRecommendAdapter.ViewHolder holder, int position) {
        if (position >= mData.size()) {
            return;
        }
        final HomePageData.RecentContact recentContact = mData.get(position);
        if (recentContact == null) {
            return;
        }

        ImageLoader.load(mContext, holder.mAvatar, recentContact.avatar);
        //静态高斯模糊，不要用RealtimeBlurView，造成滚动卡顿，每次滚动都会重新计算
        Glide.with(mContext).load(recentContact.avatar).into(new SimpleTarget<Drawable>() {
            @Override
            public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                if (resource instanceof BitmapDrawable) {
                    Bitmap bitmap = ImageUtil.getBlurAvatar(mContext, ((BitmapDrawable)resource).getBitmap(),
                            DensityUtil.dp2px(mContext, 104), DensityUtil.dp2px(mContext, 60),
                            DensityUtil.dp2px(mContext, 48), 0xFFFFFFFF, 25);
                    holder.mBkGndImageView.setImageBitmap(bitmap);
                }
            }
        });
        holder.mUserNameTv.setText(recentContact.realName);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(recentContact.url)) {
                    Uri uri = Uri.parse(recentContact.url);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mData != null ? mData.size() : 0;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        public ImageView mAvatar;
        public ImageView mBkGndImageView;
        public TextView mUserNameTv;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mAvatar = itemView.findViewById(R.id.recommend_avatar);
            mBkGndImageView = itemView.findViewById(R.id.recommend_bkgnd);
            mUserNameTv = itemView.findViewById(R.id.recommend_name);
        }
    }
}*/
