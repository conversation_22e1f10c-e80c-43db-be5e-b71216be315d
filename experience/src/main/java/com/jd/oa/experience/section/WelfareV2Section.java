package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.WelfareV2Data;
import com.jd.oa.experience.repo.WelfareV2Repo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.util.NumUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.PagerSnapAdapter;
import com.jd.oa.experience.view.PagerSnapRecyclerView;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

//我的福利
public class WelfareV2Section extends StatelessSection implements Destroyable {

    private Context mContext;
    private String mTabCode;
    private boolean mIsFirst;
    private SectionedRecyclerViewAdapter mAdapter;
    private WelfareV2Section.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private WelfareV2Repo repo;
    private WelfareV2Data mData;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_REFRESH_EXP_FROM_TAB:
                    loadData();
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                default:
                    break;
            }
        }
    };

    public WelfareV2Section(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_welfare_v2)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = WelfareV2Repo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_FROM_TAB);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        WelfareV2Data cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getWelfareV2Data(new LoadDataCallback<WelfareV2Data>() {
            @Override
            public void onDataLoaded(WelfareV2Data welfareV2Data) {
                showData(welfareV2Data);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }

    private void showData(WelfareV2Data data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }


    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/

        refreshUI();
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        if (mData.items == null || mData.items.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mTitleView.setTitleWithCount(true, mData.icon, mData.title, mData.jumpUrl, mData.jumpText, mData.count == null ? 0 : mData.count);
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                    if (TextUtils.equals("1", mData.welfareVer)) {
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE_DETAIL, new HashMap<String, String>());
                    } else if (TextUtils.equals("2", mData.welfareVer)) {
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE_DETAIL_NEW, new HashMap<String, String>());
                    }
                }
            }
        });
        if (!TextUtils.isEmpty(mData.message)) {
            mItemViewHolder.mSubTitleLayout.setVisibility(View.VISIBLE);
            mItemViewHolder.mSubTitleTv.setText(mData.message);
        } else {
            mItemViewHolder.mSubTitleLayout.setVisibility(View.GONE);
        }

        mItemViewHolder.mWelfareRv.setAdapter(new PagerSnapAdapter<WelfareV2Data.WelfareItem>(
                mContext, mData.items, PagerSnapAdapter.VIEW_TYPE_GRID,
                R.layout.jdme_item_experience_section_welfare_v2_item) {
            @Override
            public void onBindView(View view, final WelfareV2Data.WelfareItem data) {
                ImageView iconIv = view.findViewById(R.id.icon_iv);
                TextView titleTv = view.findViewById(R.id.title_tv);
                TextView countTv = view.findViewById(R.id.count_tv);
                TextView unitTv = view.findViewById(R.id.unit_tv);

                if (!TextUtils.isEmpty(data.icon)) {
                    ImageLoader.load(mContext, iconIv, data.icon, R.drawable.jdme_bg_section_title_default_icon);
                } else {
                    iconIv.setImageResource(R.drawable.jdme_bg_section_title_default_icon);
                }
                titleTv.setText(TextUtils.isEmpty(data.title) ? "" : data.title);
                countTv.setVisibility(TextUtils.isEmpty(data.value) ? View.GONE : View.VISIBLE);
                countTv.setText(TextUtils.isEmpty(data.value) ? "" : NumUtil.formatTo2(data.value));
                unitTv.setText(TextUtils.isEmpty(data.unit) ? "" : data.unit);
                view.setOnClickListener(new NoDoubleClickListener() {
                    @Override
                    protected void onNoDoubleClick(View v) {
                        if (!TextUtils.isEmpty(data.url)) {
                            Router.build(data.url).go(mContext, new RouteNotFoundCallback(mContext));

                            Map<String, String> map = new HashMap<>();
                            map.put("appId", data.id);
                            ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WELFARE, map);
                        }
                    }
                });
            }
        });
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public TitleView mTitleView;
        public View mSubTitleLayout;
        public TextView mSubTitleTv;
        public PagerSnapRecyclerView<WelfareV2Data.WelfareItem> mWelfareRv;

        public ItemViewHolder(View itemView) {
            super(itemView);

            mTitleView = itemView.findViewById(R.id.title_view);
            mSubTitleLayout = itemView.findViewById(R.id.subtitle_layout);
            mSubTitleTv = itemView.findViewById(R.id.subtitle_tv);
            mWelfareRv = itemView.findViewById(R.id.welfare_rv);
        }
    }
}
