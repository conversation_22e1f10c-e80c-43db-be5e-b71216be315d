package com.jd.oa.experience.repo;

import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;
import java.util.Map;

public class SensitiveWordRepo {

    public SensitiveWordRepo() {
    }

    public void checkSensitive(String text, final LoadDataCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("word", text);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), Integer.valueOf(response.getErrorCode(), 10));
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 1);
            }
        }, "exp.common.sensitiveword.check");
    }
}
