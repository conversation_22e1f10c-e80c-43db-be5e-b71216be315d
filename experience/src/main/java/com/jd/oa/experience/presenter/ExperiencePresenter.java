package com.jd.oa.experience.presenter;

import com.jd.oa.experience.IExperienceInterface;
import com.jd.oa.experience.model.GlobalData;
import com.jd.oa.experience.repo.ExperienceRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;

import android.content.Context;
import android.util.Log;

import java.util.HashMap;

public class ExperiencePresenter implements IExperienceInterface.Presenter {
    private static final String TAG = "ExperiencePresenter";

    private IExperienceInterface.View mView;
    private ExperienceRepo mRepo;

    public ExperiencePresenter(Context context, IExperienceInterface.View view) {
        mView = view;
        mRepo = ExperienceRepo.get(context);
    }

    @Override
    public void getGlobalData(final int from, HashMap<String, Object> params) {
        mRepo.getGlobalData(from, params, new LoadDataCallback<GlobalData>() {
            @Override
            public void onDataLoaded(GlobalData data) {
                mView.finishRefresh();
                if (mView == null || !mView.isAlive()) return;
                mView.onGlobalData(data, from);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mView.finishRefresh();
                Log.e(TAG, "onDataNotAvailable: " + s);
            }
        });
    }

    public void destroy() {
        mView = null;
    }

    @Override
    public void getGlobalCache() {
        GlobalData cache = mRepo.getGlobalCache();
        if (cache != null) {
            mView.showGlobalCacheData(cache);
        } else {
            mView.showEmpty();//骨架屏
        }
    }
}
