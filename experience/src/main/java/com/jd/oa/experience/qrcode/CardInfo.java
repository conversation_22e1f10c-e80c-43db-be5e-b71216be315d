package com.jd.oa.experience.qrcode;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by huf<PERSON> on 2016/7/14
 */
public class CardInfo implements Parcelable {
    private String realName;
    private String userName;
    private String headImg;
    private String email;
    private String visitingCardQRCode;
    /*
    positionName String  职位
organizationName String  组织机构
     */
    private String positionName;
    private String organizationName;
    private String telephone;

    private long step;

    public long getStep() {
        return step;
    }

    public void setStep(long step) {
        this.step = step;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getHeadImg() {
        return headImg;
    }

    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getVisitingCardQRCode() {
        return visitingCardQRCode;
    }

    public void setVisitingCardQRCode(String visitingCardQRCode) {
        this.visitingCardQRCode = visitingCardQRCode;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.realName);
        dest.writeString(this.headImg);
        dest.writeString(this.email);
        dest.writeString(this.visitingCardQRCode);
        dest.writeString(this.positionName);
        dest.writeString(this.organizationName);
        dest.writeString(this.telephone);
        dest.writeLong(this.step);
    }

    public CardInfo() {
    }

    protected CardInfo(Parcel in) {
        this.realName = in.readString();
        this.headImg = in.readString();
        this.email = in.readString();
        this.visitingCardQRCode = in.readString();
        this.positionName = in.readString();
        this.organizationName = in.readString();
        this.telephone = in.readString();
        this.step = in.readLong();
    }

    public static final Creator<CardInfo> CREATOR = new Creator<CardInfo>() {
        @Override
        public CardInfo createFromParcel(Parcel source) {
            return new CardInfo(source);
        }

        @Override
        public CardInfo[] newArray(int size) {
            return new CardInfo[size];
        }
    };
}
