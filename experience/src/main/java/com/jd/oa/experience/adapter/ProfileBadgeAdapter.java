package com.jd.oa.experience.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class ProfileBadgeAdapter extends RecyclerView.Adapter<ProfileBadgeAdapter.ViewHolder> {

    public interface OnItemClickListener {
        void onItemClick();
    }

    private final Context mContext;
    private final List<String> mBadges;
    private final ProfileBadgeAdapter.OnItemClickListener mOnItemClickListener;

    public ProfileBadgeAdapter(Context context, List<String> badges, ProfileBadgeAdapter.OnItemClickListener listener) {
        mContext = context;
        mBadges = badges;
        mOnItemClickListener = listener;
    }

    @NonNull
    @Override
    public ProfileBadgeAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_experience_section_profile_badge, parent, false);
        return new ProfileBadgeAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ProfileBadgeAdapter.ViewHolder holder, int position) {
        if (position >= mBadges.size()) {
            return;
        }
        final String image = mBadges.get(position);
        if (image == null || image.isEmpty()) {
            return;
        }

        ImageLoader.load(mContext, holder.ivBadge, image, true, R.drawable.exp_badge_default, R.drawable.exp_badge_default);
        holder.ivBadge.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick();
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mBadges != null ? mBadges.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public ImageView ivBadge;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivBadge = itemView.findViewById(R.id.iv_badge);
        }
    }
}
