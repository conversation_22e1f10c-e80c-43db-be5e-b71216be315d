package com.jd.oa.experience.fragment;

import static com.jd.oa.experience.section.LeftRightSection.SECTION_SERVICE_V3;
import static com.jd.oa.experience.section.LeftRightSection.SECTION_WELFARE_V3;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.GlobalData;
import com.jd.oa.experience.model.RefreshObserver;
import com.jd.oa.experience.section.AiEmployeeSection;
import com.jd.oa.experience.section.AppListSection;
import com.jd.oa.experience.section.AppSection;
import com.jd.oa.experience.section.BannerSection;
import com.jd.oa.experience.section.BottomLineSection;
import com.jd.oa.experience.section.ChronicleSection;
import com.jd.oa.experience.section.CommonSection;
import com.jd.oa.experience.section.CourseSection;
import com.jd.oa.experience.section.ExploreSection;
import com.jd.oa.experience.section.InteractionSection;
import com.jd.oa.experience.section.LeftRightSection;
import com.jd.oa.experience.section.ManualSection;
import com.jd.oa.experience.section.MeetingSection;
import com.jd.oa.experience.section.MemorabiliaBannerSection;
import com.jd.oa.experience.section.QUCSection;
import com.jd.oa.experience.section.RecomCourseSection;
import com.jd.oa.experience.section.RecommendSection;
import com.jd.oa.experience.section.RecommendV2Section;
import com.jd.oa.experience.section.ServiceSection;
import com.jd.oa.experience.section.StudyMapSection;
import com.jd.oa.experience.section.WelfareSection;
import com.jd.oa.experience.section.WelfareV2Section;
import com.jd.oa.experience.section.WorkTimeSection;
import com.jd.oa.experience.section.JoyHRSection;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class ExpTabFragment extends Fragment {

    private static final String KEY_PREFIX = "EXP_TAB_FRAGMENT_KEY";
    private BottomLineSection mBottomLineSection;
    private SectionedRecyclerViewAdapter mSectionedAdapter;
    private Context mContext;
    private RecyclerView mRecyclerView;
    private GlobalData.Tab mTabData;
    private Gson mGson = new Gson();

    public static final int ACTION_TYPE_REFRESH = 0;
    public static final int ACTION_TYPE_CONFIG_CHANGED = 1;

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null || !TextUtils.equals(intent.getAction(), Constants.ACTION_SECTION_VISIBLE)) {
                return;
            }
            String tabCode = intent.getStringExtra("tabCode");
            if (mTabData != null && tabCode != null && TextUtils.equals(tabCode, mTabData.code) && mBottomLineSection != null) {
                mBottomLineSection.showBottom();
            }
        }
    };

    public static void notifySectionVisible(Context context, String tabCode) {
        Intent intent = new Intent(Constants.ACTION_SECTION_VISIBLE);
        intent.putExtra("tabCode", tabCode);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    public void setLayouts(GlobalData.Tab newTab, int from, String tabsVer) {
        mTabData = newTab;
        setCache(KEY_PREFIX + newTab.code, mTabData);
        Bundle arg = new Bundle();
        arg.putString("tabCode", newTab.code);
        arg.putInt("from", from);
        arg.putString("tabsVer", tabsVer);
        setArguments(arg);
    }

    public boolean needUpdate(GlobalData.Tab newTab) {
        return needBuildSection(newTab.layout, mTabData.layout);
    }

    public void updateLayouts(GlobalData.Tab newTab, int from, String tabsVer, boolean forceUpdate) {
        boolean needRebuildSection = needBuildSection(newTab.layout, mTabData.layout) || forceUpdate;
        setLayouts(newTab, from, tabsVer);
        updateSection(tabsVer, from, needRebuildSection);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = getContext();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_SECTION_VISIBLE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Bundle bundle = getArguments();
        String keyCode = bundle.getString("tabCode");
        int from = bundle.getInt("from");
        String tabsVer = bundle.getString("tabsVer");
        if (mTabData == null) {
            mTabData = getCache(KEY_PREFIX + keyCode);
        }
        View view = inflater.inflate(R.layout.jdme_fragment_experience_tab, null);
        mRecyclerView = view.findViewById(R.id.recycler_exp);
        mSectionedAdapter = new SectionedRecyclerViewAdapter();
        mRecyclerView.setAdapter(mSectionedAdapter);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false));
        mRecyclerView.setNestedScrollingEnabled(true);
        updateSection(tabsVer, from, true);//主tab由init刷新二级接口，其他tab自己创建时刷
        return view;
    }

    @Override
    public void onDestroy() {
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
        super.onDestroy();
        destroyAllSections();
    }

    private void updateSection(String tabsVer, int from, boolean needBuildSection) {
        if (needBuildSection) {//buildSections
            destroyAllSections();
            mSectionedAdapter.removeAllSections();
            if (mTabData != null && mTabData.layout != null) {
                if (TextUtils.equals("3", tabsVer)) {
                    List<Section> sections = new ArrayList<>();
                    List<String> leftRightSectionCodes = new ArrayList<>();
                    int leftRightSectionIndex = -1;

                    for (int i = 0; i < mTabData.layout.size(); i++) {
                        GlobalData.Layout layout = mTabData.layout.get(i);
                        if (layout == null) continue;

                        if (SECTION_WELFARE_V3.equals(layout.code) || SECTION_SERVICE_V3.equals(layout.code)) {
                            //福利、服务共享一个楼层特殊逻辑
                            leftRightSectionCodes.add(layout.code);
                            if (leftRightSectionIndex == -1) leftRightSectionIndex = i;
                        } else {
                            sections.add(getSectionV3(layout.code, from, i == 0));
                        }
                    }

                    if (leftRightSectionIndex != -1) {
                        LeftRightSection lrSection = new LeftRightSection(
                                mContext, from, mSectionedAdapter, mTabData.code,
                                leftRightSectionIndex == 0, leftRightSectionCodes
                        );
                        sections.add(leftRightSectionIndex, lrSection);
                    }
                    for (int i = 0; i < sections.size(); i++) {
                        Section section = sections.get(i);
                        if (section != null) {
                            mSectionedAdapter.addSection(sections.get(i));
                        }
                    }
                } else {
                    for (int i = 0; i < mTabData.layout.size(); i++) {
                        GlobalData.Layout layout = mTabData.layout.get(i);
                        if (layout == null) {
                            continue;
                        }
                        Section section;
                        if (TextUtils.equals("2", tabsVer)) {
                            section = getSectionV2(layout.code, from, i == 0);
                        } else {
                            section = getSection(layout.code, from, i == 0);
                        }
                        if (section == null) {
                            Log.d(getClass().getSimpleName(), "wrong code: " + layout.code);
                        } else {
                            mSectionedAdapter.addSection(section);
                        }
                    }
                }
                mBottomLineSection = new BottomLineSection(mContext, mSectionedAdapter, mTabData.desc);
                mSectionedAdapter.addSection(mBottomLineSection);
            }
            mSectionedAdapter.notifyDataSetChanged();
            mRecyclerView.scrollToPosition(0);
        } else {//刷新不重新创建，刷新二级接口
            if (mBottomLineSection != null) {
                mBottomLineSection.setDesc(mTabData.desc);
            }
            Intent intent = new Intent(Constants.ACTION_REFRESH_EXP_SECTIONS);
            intent.putExtra("from", from);
            intent.putExtra("tabCode", mTabData.code);
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);

            //V3的Section不通过广播通知
            notifyV3Sections(ACTION_TYPE_REFRESH);
        }
    }

    private Section getSectionV3(String code, int from, boolean first) {
        switch (code) {
            case "DIGITAL_EMPLOYEE_V3":
                return new AiEmployeeSection(mContext, from, mSectionedAdapter, mTabData.code, first);
            case "JOYHR_V3":
                return new JoyHRSection(mContext, from, mSectionedAdapter, mTabData.code, first);
            case "CULTUREBANNER":
                return new BannerSection(mContext, from, mSectionedAdapter, mTabData.code, first, true);
            case "RECOMMEND_STUDY_V3":
                return new RecomCourseSection(mContext, from, mSectionedAdapter, mTabData.code, first);
            case "EVENT_V3":
                //大事记-图片Banner
                return new MemorabiliaBannerSection(mContext, from, mSectionedAdapter, mTabData.code, first);
            default:
                return null;
        }
    }

    private Section getSectionV2(String code, int from, boolean first) {
        if ("APPLIST".equals(code)) {
            return new AppListSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("WELFARE".equals(code)) {
            return new WelfareV2Section(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("SERVICE".equals(code)) {
            return new ServiceSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("RECOMMEND".equals(code)) {
            return new RecommendV2Section(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("EXPLORE".equals(code)) {
            return new ExploreSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("STUDYMAP".equals(code)) {
            return new StudyMapSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("COURSE".equals(code)) {
            return new CourseSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("INTERACTION".equals(code)) {
            return new InteractionSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("MANUAL".equals(code)) {
            return new ManualSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("EVE".equals(code)) {
            return new ChronicleSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("WORKTIME".equals(code)) {
            return new WorkTimeSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("MEETING".equals(code)) {
            return new MeetingSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("CULTUREBANNER".equals(code)) {
            return new BannerSection(mContext, from, mSectionedAdapter, mTabData.code, first, false);
        }
        return null;
    }

    @Deprecated
    private Section getSection(String code, int from, boolean first) {
        if ("QUC".equals(code)) {
            return new QUCSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("COM".equals(code)) {
            return new CommonSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("APP".equals(code)) {
            return new AppSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("REC".equals(code)) {
            return new RecommendSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("WEL".equals(code)) {
            return new WelfareSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("EXPLORE".equals(code)) {
            return new ExploreSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("STUDYMAP".equals(code)) {
            return new StudyMapSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("COURSE".equals(code)) {
            return new CourseSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        } else if ("EVE".equals(code)) {
            return new ChronicleSection(mContext, from, mSectionedAdapter, mTabData.code, first);
        }
        return null;
    }

    private void destroyAllSections() {
        if (mSectionedAdapter == null) {
            return;
        }
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        for (Map.Entry<String, Section> entity : map.entrySet()) {
            if (entity.getValue() instanceof Destroyable) {
                ((Destroyable) entity.getValue()).onDestroy();
            }
        }
    }

    //新增section，删除section，section顺序变化，重新删除添加sections
    private boolean needBuildSection(List<GlobalData.Layout> layouts, List<GlobalData.Layout> oldLayouts) {
        if (oldLayouts == null) {
            return true;
        }
        if (layouts.size() != oldLayouts.size()) {
            return true;
        }
        int num = layouts.size();
        for (int i = 0; i < num; i++) {
            GlobalData.Layout oldLayout = oldLayouts.get(i);
            GlobalData.Layout newLayout = layouts.get(i);
            if (oldLayout == null || newLayout == null) {
                return true;
            }
            if (!TextUtils.equals(newLayout.code, oldLayout.code)) {
                return true;
            }
        }
        return false;
    }

    public boolean canScrollVertical(int direction) {
        return mRecyclerView != null && mRecyclerView.canScrollVertically(direction);
    }

    public GlobalData.Tab getCache(String key) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), key, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        GlobalData.Tab data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<GlobalData.Tab>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void setCache(String key, GlobalData.Tab data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), key, null, mGson.toJson(data));
    }

    public void notifyV3Sections(int actionType) {
        if (mSectionedAdapter != null) {
            if (!mSectionedAdapter.getCopyOfSectionsMap().keySet().isEmpty()) {
                for (String sectionTag: mSectionedAdapter.getCopyOfSectionsMap().keySet()) {
                    if (mSectionedAdapter.getSection(sectionTag) instanceof RefreshObserver) {
                        if (actionType == ACTION_TYPE_REFRESH) {
                            ((RefreshObserver) mSectionedAdapter.getSection(sectionTag)).onRefresh();
                        } else if (actionType == ACTION_TYPE_CONFIG_CHANGED) {
                            ((RefreshObserver) mSectionedAdapter.getSection(sectionTag)).onConfigChanged();
                        }
                    }
                }
            }
        }
    }
}
