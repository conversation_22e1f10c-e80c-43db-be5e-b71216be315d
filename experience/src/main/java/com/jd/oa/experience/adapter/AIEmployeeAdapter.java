package com.jd.oa.experience.adapter;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_HRAI_click;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.AIEmployeeData;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AIEmployeeAdapter extends RecyclerView.Adapter<AIEmployeeAdapter.AIEmployeeViewHolder> {
    private final List<AIEmployeeData.MenuItem> mData;

    public AIEmployeeAdapter(List<AIEmployeeData.MenuItem> data) {
        mData = data;
    }

    @NonNull
    @Override
    public AIEmployeeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.jdme_item_experience_section_aiemployee_item, parent, false);
        return new AIEmployeeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AIEmployeeViewHolder holder, int position) {
        holder.bind(mData.get(position));
    }

    @Override
    public int getItemCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }

    public static class AIEmployeeViewHolder extends RecyclerView.ViewHolder {

        TextView mTitle;
        TextView mSubtitle;
        ConstraintLayout mContainer;

        public AIEmployeeViewHolder(@NonNull View itemView) {
            super(itemView);
            mTitle = itemView.findViewById(R.id.tv_ai_employee_title);
            mSubtitle = itemView.findViewById(R.id.tv_ai_employee_subtitle);
            mContainer = itemView.findViewById(R.id.cl_ai_employee_container);
        }

        public void bind(AIEmployeeData.MenuItem menuItem) {
            if (menuItem != null) {
                if (mTitle != null && mSubtitle != null) {
                    mTitle.setText(menuItem.title);
                    mSubtitle.setText(menuItem.unit);
                    mContainer.setOnClickListener(new AvoidFastClickListener(1000) {
                        @Override
                        public void onAvoidedClick(View view) {
                            Context context = mContainer.getContext();
                            //先跳转聊天页面
                            if (!TextUtils.isEmpty(menuItem.url) && context != null) {
                                Map<String, String> param = new HashMap<>();
                                param.put("appId", menuItem.eventId);
                                JDMAUtils.clickEvent(mobile_EXP_Main_HRAI_click, mobile_EXP_Main_HRAI_click, param);
                                Router.build(menuItem.url).go(context, new RouteNotFoundCallback(context));
                            }
                            //再调用发送消息deeplink，不然会在聊天页面看到浮窗推送ella回复的消息
                            if (!TextUtils.isEmpty(menuItem.conservationUrl) && context != null) {
                                 Router.build(menuItem.conservationUrl).go(context, new RouteNotFoundCallback(context));
                            }
                        }
                    });
                }
            }
        }
    }
}
