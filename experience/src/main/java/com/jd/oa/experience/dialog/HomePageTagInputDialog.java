package com.jd.oa.experience.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatEditText;

import com.jd.oa.experience.R;
import com.jd.oa.experience.repo.SensitiveWordRepo;
import com.jd.oa.experience.util.DutyToast;
import com.jd.oa.melib.mvp.LoadDataCallback;

import cn.com.libsharesdk.BottomSheetDialog2;

public class HomePageTagInputDialog extends BottomSheetDialog2 {

    public interface OnTagListener {
        void onSaveTag(String tag);
        boolean isDuplicate(String tag);
    }

    private TextView tvTextNum;
    private Button btnSave;
    private AppCompatEditText etInput;
    private Context mContext;
    private OnTagListener mListener;
    private int mMaxLength;
    private final SensitiveWordRepo mRepo = new SensitiveWordRepo();

    public HomePageTagInputDialog(Context context) {
        this(context, R.style.BottomSheetEdit);
    }

    public HomePageTagInputDialog(Context context, int theme) {
        super(context, theme);
        mContext = context;
        mMaxLength = 8;
//        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_exp_dialog_duty_tag, null);
        setContentView(view);
        //设置背景
        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            parent.setBackgroundResource(android.R.color.transparent);
        }

        tvTextNum = view.findViewById(R.id.tv_text_num);
        btnSave = view.findViewById(R.id.btn_save);
        etInput = view.findViewById(R.id.input_et);
        etInput.setHint(mContext.getString(R.string.exp_homepage_duty_dialog_hint, String.valueOf(mMaxLength)));
        etInput.requestFocus();
        if (getWindow() != null) {
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
                    | WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.gravity = Gravity.BOTTOM;
            getWindow().setAttributes(layoutParams);
        }
        etInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                showTextLength(s.toString());
            }
        });
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mContext == null) {
                    return;
                }

                String text;
                if (etInput.getText() != null) {
                    text = etInput.getText().toString();
                } else {
                    text = "";
                }
                if (text.length() > mMaxLength) {
                    DutyToast.showToast(mContext, mContext.getString(R.string.exp_homepage_duty_dialog_hint, String.valueOf(mMaxLength)));
                    return;
                }

                final String tag = text.trim();

                if (mListener != null && mListener.isDuplicate(tag)) {
                    DutyToast.showToast(mContext, R.string.exp_homepage_same_tag);
                    return;
                }

                btnSave.setEnabled(false);
                mRepo.checkSensitive(tag, new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        if (mListener != null) {
                            mListener.onSaveTag(tag);
                        }
                        btnSave.setEnabled(true);
                        dismiss();
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        if (i == 1011) {//包含敏感词
                            DutyToast.showToast(mContext, R.string.exp_homepage_sensitive_word);
                        } else {
                            DutyToast.showToast(mContext, R.string.exp_homepage_sensitive_error);
                        }
                        btnSave.setEnabled(true);
                    }
                });
            }
        });
    }

    @Override
    public void dismiss() {
        mListener = null;//如果敏感词接口返回较慢，用户关闭输入框后不再执行onSaveTag
        super.dismiss();
    }

    public void setMaxLength(int maxLength) {
        mMaxLength = maxLength;
        etInput.setHint(mContext.getString(R.string.exp_homepage_duty_dialog_hint, String.valueOf(mMaxLength)));
    }

    public void setInputText(String input) {
        if (input != null && TextUtils.isEmpty(input.replaceAll(" ", ""))) {
            //全是空格
            input = "";
        }
        showTextLength(input);
        etInput.setText(input);
        if (!TextUtils.isEmpty(input)) {
            etInput.setSelection(input.length());//将光标移至文字末尾
        }
    }

    public void setOnTagListener(OnTagListener listener) {
        mListener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @SuppressLint("SetTextI18n")
    public void showTextLength(String s) {
        if (!TextUtils.isEmpty(s)) {
            tvTextNum.setTextColor(s.length() > mMaxLength ? 0xFFFE3B30 : 0xFF333333);
            tvTextNum.setText(s.length() + "/" + mMaxLength);
            btnSave.setEnabled(!TextUtils.isEmpty(s.trim()));
        } else {
            tvTextNum.setTextColor(0xFF8F959E);
            tvTextNum.setText("0/" + mMaxLength);
            btnSave.setEnabled(false);
        }
    }
}