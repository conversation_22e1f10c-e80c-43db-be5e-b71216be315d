package com.jd.oa.experience.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

/*体验平台所有二级内嵌rv都要使用这个类*/
public class NestedHorizontalRecyclerView extends RecyclerView {

    private int mStartX = 0;
    private int mStartY = 0;

    public NestedHorizontalRecyclerView(@NonNull Context context) {
        super(context);
        setNestedScrollingEnabled(false);//必须这样设置，否则会导致AppBarLayout问题，按住内嵌rv区域上滑，外部rv滑到CollapsingLayout下边
    }

    public NestedHorizontalRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setNestedScrollingEnabled(false);
    }

    public NestedHorizontalRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setNestedScrollingEnabled(false);
    }

    /*
    当ViewPager2设置mViewPager.setUserInputEnabled(false)，禁止左右滑动切换tab页，产品希望能够支持滑动切换
    当ViewPager2不禁止滑动切换，viewpager会拦截二级rv的滑动事件，导致二级rv无法横向滚动
    可以通过对二级rv设置setNestedScrollingEnabled(true)解决，但这样处理会导致collapsedToolbar与二级rv出现冲突，
    即按住内嵌rv区域上滑，外部rv滑到CollapsingLayout下边，所有二级rv必须设置setNestedScrollingEnabled(false)，
    需要重写dispatchTouchEvent防止viewpager接管横滑事件
    * */
    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mStartX = (int)ev.getX();
                mStartY = (int)ev.getY();
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_MOVE:
                int endX = (int) ev.getX();
                int endY = (int) ev.getY();
                int disX = Math.abs(endX - mStartX);
                int disY = Math.abs(endY - mStartY);
                if (disX == 0 && disY == 0) {
                    getParent().requestDisallowInterceptTouchEvent(true);
                    break;
                }
                if (disX > disY) {//2 * disX > disY
                    //如果是纵向滑动，告知父布局不进行时间拦截，交由子布局消费
                    getParent().requestDisallowInterceptTouchEvent(canScrollHorizontally(mStartX - endX));
                } else {
                    getParent().requestDisallowInterceptTouchEvent(canScrollVertically(mStartY - endY));
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(false);
                break;
            default:
                break;
        }
        return super.dispatchTouchEvent(ev);
    }
}
