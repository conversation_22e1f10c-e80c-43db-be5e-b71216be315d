package com.jd.oa.experience.fragment;

import static com.jd.oa.experience.fragment.ExpTabFragment.ACTION_TYPE_CONFIG_CHANGED;
import static com.jd.oa.preference.JDMETenantPreference.KV_ENTITY_JDHR_SHOW_TAB_RED_DOT;
import static com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME;
import static com.jd.oa.theme.manager.Constants.ACTION_THEME_RED_DOT_GONE;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.chenenyu.router.Router;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.badge.RedDotView;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.ShowAnimationSubject;
import com.jd.oa.business.mine.main.MineAnimFragment;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.experience.IExperienceInterface;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.GlobalData;
import com.jd.oa.experience.presenter.ExperiencePresenter;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.util.GlobalDataUtil;
import com.jd.oa.experience.view.ExpHeadRefreshView;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.ProfileLayout;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.guide.BizRule;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.listener.UserServiceListener;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.FeedbackTipPopupWindow;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.pulltorefresh.PullToRefreshLayout;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.NetworkFileUtil;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.StatusBarConfig;
import com.jd.oa.utils.TabletUtil;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Observable;
import java.util.Observer;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

//JDHR页，与"我的"用一个tab，根据配置显示"我的"或"员工体验"或"JDHR"
public class JDHRFragment extends BaseFragment implements IExperienceInterface.View, Observer, UserServiceListener {

    public static final int REFRESH_FROM_CACHE = 0;
    public static final int REFRESH_FROM_TAB = 1;//切tab刷新
    public static final int REFRESH_FROM_PULL = 2;//下拉
    public static final int REFRESH_FROM_INIT = 3;

    public static final String EVENT_GUIDE_SHOW_ANIMATION = "event_guide_show_animation";
    private boolean isPaused = false;

    public enum State {
        EXPANDED,
        COLLAPSED,
        IDLE
    }

    //数据
    private Context mContext;
    private FragmentActivity mActivity;
    private final ImDdService imDdService = AppJoint.service(ImDdService.class);
    private boolean mUserVisible;
    private ExperiencePresenter mPresenter;
    private GlobalData mGlobalData;
    private boolean mDarkMode = false;
    private boolean needShowSkinHint = false;
    private int mScreenWidth = 0;
    private long mExposureStart;
    private ExpTabFragment mChildFragment;
    private boolean mFirstOnResume = true;

    //皮肤
    private View mBkGndLayout;
    private View mSkinLayout;
    private ImageView mThemeLeftIv;
    private ImageView mThemeRightIv;
    private ImageView mThemeCenterIv;

    //骨架屏
    private PullToRefreshLayout mEmptyRefreshLayout;
    private IconFontView mEmptySettingButton;
    private ScrollView mEmptyLayoutScrollView;

    //主UI
    private PullToRefreshLayout mExperienceRefreshLayout;
    private ExpHeadRefreshView mHeadRefreshView;
    private AppBarLayout appBarLayout;
    private CollapsingToolbarLayout mCollapse;
    private State mCurrentState = State.EXPANDED;

    //浮层头像
    private View mLayerLayout;
    private View mLayerAvatar;
    private ImageView mLayerAvatarIv;
    private ImageView mLayerPendantIv;
    private TextView mUserInfoTv;
    private final int mMax = DisplayUtils.dip2px(40);//头像缩放滑动阈值，超过这个阈值头像不再继续缩放
    private final int avatarLargeSize = DisplayUtils.dip2px(70);//54 + 8 + 8 padding
    private final int avatarSmallSize = DisplayUtils.dip2px(44);//28 + 8 + 8 padding
    private final int fromMarginStart = DisplayUtils.dip2px(0);
    private final int toMarginStart = DisplayUtils.dip2px(8);
    private final int fromMarginTop = DisplayUtils.dip2px(36);
    private final int toMarginTop = DisplayUtils.dip2px(0);

    //浮层右上角按钮
    private IconFontView mSettingButton;
    private TextView mFeedbackButton;
    private IconFontView mSkinButton;
    private View mSkinHintDot;
    private RedDotView mSettingBadge;

    //头部区域ProfileLayout
    private ProfileLayout mProfileLayout;
    private View mProfileAvatar;
    private ImageView mProfileAvatarIv;
    private ImageView mProfilePendantIv;

    private final int ANIMATION_DURATION = 2000;

    //司龄动画
    private boolean needShowBirthday = false;
    private boolean needShowHonor = false;
    //    private String animationYears = "";
    private boolean firstLoadShowFeedbackTip = false;
    private Disposable mShowAnimationDisposable;
    private final Consumer<Pair<Boolean, String>> mShowAnimationConsumer = new Consumer<Pair<Boolean, String>>() {
        @Override
        public void accept(Pair<Boolean, String> pair) throws Exception {
            if (!pair.first || mGlobalData == null || TextUtils.isEmpty(pair.second)) return;

            String cultureType = GlobalDataUtil.getCultureType(mGlobalData.userInfo);
            if (pair.second.equals(cultureType) && mGlobalData.entryInfo != null) {
                if (GlobalDataUtil.CULTURE_TYPE_BIRTHDAY.equals(cultureType)) {
                    showAnniversaryAnimation(MineAnimFragment.TYPE_BIRTHDAY, String.valueOf(mGlobalData.entryInfo.years));
                } else if (GlobalDataUtil.CULTURE_TYPE_SENIORITY.equals(cultureType)) {
                    showAnniversaryAnimation(MineAnimFragment.TYPE_HONOR, String.valueOf(mGlobalData.entryInfo.years));
                }
            }
        }
    };

    private final BroadcastReceiver mExpFragmentReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (Constants.ACTION_EXP_PROFILE_REFRESH_SIGNATURE_NEW.equals(intent.getAction())) {
                if (mProfileLayout != null) {
                    mProfileLayout.showSignature();
                }
            } else if (Constants.ACTION_HOMEPAGE_CLICK_LIKED.equals(intent.getAction()) && mGlobalData != null && !mGlobalData.checkHasInteractionInTab()) {
                String likeCount = intent.getStringExtra("likeCount");
                String likedCount = intent.getStringExtra("likedCount");
                if (mProfileLayout != null) {
                    mProfileLayout.updateSocial(likeCount, likedCount);
                }
            } else if (ACTION_CHANGE_THEME.equals(intent.getAction())) {
                updateProfileLayout();
                updateAvatarLayout();
            } else if (Constants.ACTION_SHOW_ANNIVERSARY.equals(intent.getAction())) {
                if (mGlobalData == null) return;
                String cultureType = GlobalDataUtil.getCultureType(mGlobalData.userInfo);
                if (mGlobalData.entryInfo != null) {
                    if (GlobalDataUtil.CULTURE_TYPE_BIRTHDAY.equals(cultureType)) {
                        Map<String, String> map = new HashMap<>();
                        map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_BIRTHDAY.keyName
                                , ExpJDMAConstants.MainInfo.MAIN_INFO_BIRTHDAY.KeyValue);
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
                        showAnniversaryAnimation(MineAnimFragment.TYPE_BIRTHDAY, String.valueOf(mGlobalData.entryInfo.years));
                    } else if (GlobalDataUtil.CULTURE_TYPE_SENIORITY.equals(cultureType)) {
                        Map<String, String> map = new HashMap<>();
                        map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_IN_WORK_YEARS.keyName
                                , ExpJDMAConstants.MainInfo.MAIN_INFO_IN_WORK_YEARS.KeyValue);
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
                        showAnniversaryAnimation(MineAnimFragment.TYPE_HONOR, String.valueOf(mGlobalData.entryInfo.years));
                    }
                }
            } else if (Constants.ACTION_UPDATE_TAGS.equals(intent.getAction()) && TabletUtil.isSplitMode(mContext)) {
                getGlobalDataV3(REFRESH_FROM_TAB);
            } else if (ACTION_THEME_RED_DOT_GONE.equals(intent.getAction())) {
                mSkinHintDot.setVisibility(View.INVISIBLE);
            } else if (Constants.ACTION_HOMEPAGE_UPDATE_LIKED.equals(intent.getAction()) && mGlobalData != null && !mGlobalData.checkHasInteractionInTab()) {
                getGlobalDataV3(REFRESH_FROM_TAB);
            }
        }
    };

    private void getGlobalDataV3(int from) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("centerVer", "v3");
        if (mPresenter != null) {
            mPresenter.getGlobalData(from, params);
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = getContext();
        mActivity = getActivity();
        mPresenter = new ExperiencePresenter(mContext, this);
        ExperienceSelfInfoFragment.observerList.add(this);
        imDdService.addUserService(this);
        mScreenWidth = DisplayUtil.getScreenWidth(mContext);
        mExposureStart = System.currentTimeMillis() / 1000;
        Bundle args = getArguments();
        if (args != null) {
            mUserVisible = args.getBoolean("isVisibleToUser", false);
        }
        if (JDMETenantPreference.getInstance().get(KV_ENTITY_JDHR_SHOW_TAB_RED_DOT)) {
            BadgeManager.showBadge(mContext, BadgeManager.BADGE_USER_CENTER);
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_jdhr, container, false);
        //皮肤
        mBkGndLayout = view.findViewById(R.id.layout_background);
        mSkinLayout = view.findViewById(R.id.skin_bkgnd_layout);
        mThemeLeftIv = view.findViewById(R.id.iv_theme_left);
        mThemeRightIv = view.findViewById(R.id.iv_theme_right);
        mThemeCenterIv = view.findViewById(R.id.iv_theme_center);

        //骨架屏
        mEmptyRefreshLayout = view.findViewById(R.id.empty_refresh_layout);
        mEmptySettingButton = view.findViewById(R.id.btn_setting_empty);
        mEmptyLayoutScrollView = view.findViewById(R.id.empty_layout_scroll_view);

        //主UI
        mExperienceRefreshLayout = view.findViewById(R.id.experience_refresh_layout);
        mHeadRefreshView = new ExpHeadRefreshView(mContext);
        appBarLayout = view.findViewById(R.id.appbar_layout);
        mCollapse = view.findViewById(R.id.collapse);
        //fix bug：在tabbar中，将个人中心调整到日历前边，会导致日历沉浸出问题
        //原因是CollapsingToolbarLayout加载后执行某些逻辑，影响jdflutter非原生部分的逻辑，无法定位非常具体的原因，需要jdflutter协助
        mCollapse.setOnApplyWindowInsetsListener(null);

        //浮层头像
        mLayerLayout = view.findViewById(R.id.layer_layout);
        mLayerAvatar = view.findViewById(R.id.avatar_layout);
        mLayerAvatarIv = view.findViewById(R.id.iv_avatar);
        mLayerPendantIv = view.findViewById(R.id.iv_pendant);
        mUserInfoTv = view.findViewById(R.id.tv_userinfo);

        //浮层右上角按钮
        mSettingButton = view.findViewById(R.id.btn_setting);
        mFeedbackButton = view.findViewById(R.id.btn_feedback);
        mSkinButton = view.findViewById(R.id.btn_skin);
        mSkinHintDot = view.findViewById(R.id.skin_hint_dot);
        mSettingBadge = view.findViewById(R.id.setting_badge);
        mSettingBadge.setAppLinks(BadgeManager.BADGE_APP_UPDATE);

        //头部区域ProfileLayout
        mProfileLayout = view.findViewById(R.id.profile_layout);
        mProfileAvatar = view.findViewById(R.id.profile_avatar);
        mProfileAvatarIv = view.findViewById(R.id.iv_profile_avatar);
        mProfilePendantIv = view.findViewById(R.id.iv_profile_pendant);
        if (mProfileLayout != null) {
            mProfileLayout.showProfileEntry();
        }

        initImmersiveStatusBar();
        initAppBarLayout();
        initExpPullRefresh();//下拉刷新
        initEmptyPullRefresh();//骨架屏下拉刷新
        initLayerLayout();//浮层

        mPresenter.getGlobalCache();

        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
//        mViewPager.unregisterOnPageChangeCallback(mPageChangeListener);
        if (mShowAnimationDisposable != null) {
            mShowAnimationDisposable.dispose();
        }
        if (mPresenter != null) {
            mPresenter.destroy();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        ExperienceSelfInfoFragment.observerList.remove(this);
        imDdService.removeUserService(this);
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mExpFragmentReceiver);
//        mTabFragments.clear();
        mChildFragment = null;
    }

    @Override
    public void onResume() {
        super.onResume();
        isPaused = false;
        if (mFirstOnResume) {
            onInitData();
            //显示过页面后消除tabbar红点
            if (JDMETenantPreference.getInstance().get(KV_ENTITY_JDHR_SHOW_TAB_RED_DOT)) {
                //下次不再显示
                JDMETenantPreference.getInstance().put(KV_ENTITY_JDHR_SHOW_TAB_RED_DOT, false);
                BadgeManager.hideBadge(mContext, BadgeManager.BADGE_USER_CENTER);
            }
            //只在第一次创建时显示，点击的是首页底部导航的 个人中心tab，
            //只有点击 个人中心tab 时才会显示这个引导页，而且是全局唯一的引导页
            //在灰度内，弹出引导页；灰度外不弹出引导页;外层已经判断灰度
            BizGuideHelper.getInstance().showGuideDialog((AppCompatActivity) getContext(), BizRule.HOME_PAGE_NEW_GUIDE);
        }
        if (StatusBarConfig.enableImmersive()) {
            if (mDarkMode) {
                ThemeApi.checkAndSetDarkTheme(mActivity);
                MELogUtil.localI(MELogUtil.TAG_JIS, "onResume, statusBar dark mode");
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(mActivity);
                MELogUtil.localI(MELogUtil.TAG_JIS, "onResume, statusBar light mode");
            }
        }
        mExposureStart = System.currentTimeMillis() / 1000;
        if (mContext != null) {
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_EXP_ON_RESUME));
        }
        Activity topActivity = AppBase.getTopActivity();
        if (topActivity != null) {
            Intent topIntent = topActivity.getIntent();
            if (topIntent.hasExtra(Constant.SHORTCUT_FROM)) {
                String from = topIntent.getStringExtra(Constant.SHORTCUT_FROM);
                if (Constant.SHORTCUT_FLAG.equals(from)) {
                    //子午线埋点
                    JDMAUtils.onEventClick(JDMAConstants.mobile_me_icon_access_card_click, JDMAConstants.mobile_me_icon_access_card_click);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (mContext != null) {
                                LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_EXP_PROFILE_OPEN_QRCODE));
                            }
                        }
                    }, 500);
                }
                topIntent.removeExtra(Constant.SHORTCUT_FROM);//每次展示完弹窗将标识去掉,避免重复展示
            }
        }
        if (mPresenter != null && !mFirstOnResume) {
            getGlobalDataV3(REFRESH_FROM_TAB);
        }
        mFirstOnResume = false;
        JDMAUtils.onEventPagePV(AppBase.getAppContext(), "EXP_Main", "EXP_Main");
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_EXP_SKIN_TOAST_FORCE_SHOWN, false);
    }

    @Override
    public void onPause() {
        super.onPause();
        isPaused = true;
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.setStatusBarLightMode(mActivity);
            MELogUtil.localI(MELogUtil.TAG_JIS, "onPause, statusBar light mode");
        }
        Map<String, String> map = new HashMap<>();
        map.put("duration", String.valueOf(System.currentTimeMillis() / 1000 - mExposureStart));
        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_DURATION, map);
        if (mContext != null) {
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_EXP_ON_PAUSE));
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        int width = DisplayUtil.getScreenWidth(mContext);
        if (mScreenWidth != width) {
            mScreenWidth = width;
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_SCREEN_WIDTH_CHANGE));
            if (mChildFragment != null) {
                mChildFragment.notifyV3Sections(ACTION_TYPE_CONFIG_CHANGED);
            }
            if (mProfileLayout != null) {
                mProfileLayout.refreshUI();
            }
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        mUserVisible = isVisibleToUser;
        //周年动画
        if (mUserVisible && mGlobalData != null) {
            int resId = GlobalDataUtil.getCultureImage(mGlobalData);
            String cultureType = GlobalDataUtil.getCultureType(mGlobalData.userInfo);
            if (resId > 0 && shouldShowAnimation(cultureType) && mGlobalData.entryInfo != null) {
                if (GlobalDataUtil.CULTURE_TYPE_BIRTHDAY.equals(cultureType)) {
                    showAnniversaryAnimation(MineAnimFragment.TYPE_BIRTHDAY, String.valueOf(mGlobalData.entryInfo.years));
                } else if (GlobalDataUtil.CULTURE_TYPE_SENIORITY.equals(cultureType)) {
                    showAnniversaryAnimation(MineAnimFragment.TYPE_HONOR, String.valueOf(mGlobalData.entryInfo.years));
                }
            }
        }
    }

    private void onInitData() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_SHOW_ANNIVERSARY);
        intentFilter.addAction(ACTION_CHANGE_THEME);
        intentFilter.addAction(ACTION_THEME_RED_DOT_GONE);
        intentFilter.addAction(Constants.ACTION_UPDATE_TAGS);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_LIKED);
        intentFilter.addAction(Constants.ACTION_EXP_PROFILE_REFRESH_SIGNATURE_NEW);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_CLICK_LIKED);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mExpFragmentReceiver, intentFilter);

        getGlobalDataV3(REFRESH_FROM_INIT);
        mShowAnimationDisposable = ShowAnimationSubject.subscribe(mShowAnimationConsumer);
        if (mGlobalData != null) {
            if (!JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_EXP_FEEDBACK_TIP_SHOWN)) {
                showFeedbackTip();
            }
        } else {
            firstLoadShowFeedbackTip = true;
        }
    }

    private void showFeedbackTip() {
        try {
            if (mContext != null && !TextUtils.isEmpty(mGlobalData.feedbackUrl) && !isPaused) {
                FeedbackTipPopupWindow popupWindow = new FeedbackTipPopupWindow(mContext);
                popupWindow.showAsDropDown(mFeedbackButton, 0, 0, Gravity.CENTER_HORIZONTAL);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_EXP_FEEDBACK_TIP_SHOWN, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //处理沉浸后头部UI边距
    private void initImmersiveStatusBar() {
        if (StatusBarConfig.enableImmersive()) {
            FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams) mExperienceRefreshLayout.getLayoutParams();
            lp.topMargin += QMUIStatusBarHelper.getStatusbarHeight(mContext);
            mExperienceRefreshLayout.setLayoutParams(lp);
            //骨架屏
            mEmptyRefreshLayout.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(mContext), 0, 0);
            //浮层
            mLayerLayout.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(mContext), 0, 0);
            //皮肤背景
            FrameLayout.LayoutParams lpSkin = (FrameLayout.LayoutParams) mSkinLayout.getLayoutParams();
            lpSkin.height += QMUIStatusBarHelper.getStatusbarHeight(mContext);
            mSkinLayout.setLayoutParams(lpSkin);
        }
    }

    //初始化AppBarLayout
    private void initAppBarLayout() {
        appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                if (mChildFragment == null || mChildFragment.getView() == null) {
                    return;
                }

                float scrollRange = appBarLayout.getTotalScrollRange();
                float offsetRatio = Math.abs(verticalOffset) / scrollRange; // 0（完全展开）→ 1（完全折叠）

                // 计算透明度（0~255）
                int alpha = (int) (offsetRatio * 255);
                int backgroundColor = Color.argb(alpha, 0xFA, 0xFA, 0xFA); // #FAFAFA + 动态透明度

                // 应用背景色
                mChildFragment.getView().setBackgroundColor(backgroundColor);

                // 更新状态（可选）
                if (verticalOffset == 0) {
                    mCurrentState = State.EXPANDED;
                } else if (Math.abs(verticalOffset) >= scrollRange) {
                    mCurrentState = State.COLLAPSED;
                } else {
                    mCurrentState = State.IDLE;
                }

                onAppBarOffsetChanged(Math.abs(verticalOffset), (int) scrollRange);
            }
        });
    }

    //初始化UI
    private void initExpPullRefresh() {
        mExperienceRefreshLayout.setHeaderView(mHeadRefreshView);
        mExperienceRefreshLayout.setCanRefresh(true);
        mExperienceRefreshLayout.setCanLoadMore(false); //去掉tablayout后不再需要上滑切换到下一个tab的逻辑
        mExperienceRefreshLayout.setRefreshListener(new PullToRefreshLayout.BaseRefreshListener() {
            @Override
            public void refresh() {
                getGlobalDataV3(REFRESH_FROM_PULL);
            }

            @Override
            public void loadMore() {
                // DO NOTHING
            }
        });
        mExperienceRefreshLayout.setOnChildScrollCallback(new PullToRefreshLayout.OnChildScrollCallback() {
            @Override
            public boolean canChildScroll(@NonNull PullToRefreshLayout parent, @Nullable View child, int direction) {
                if (direction == PullToRefreshLayout.SCROLL_DOWN) {
                    return mCurrentState != State.COLLAPSED;
                }
                return mCurrentState != State.EXPANDED;
            }
        });
    }

    private void initEmptyPullRefresh() {
        mEmptyRefreshLayout.setCanRefresh(true);
        mEmptyRefreshLayout.setCanLoadMore(false);
        mEmptyRefreshLayout.setRefreshListener(new PullToRefreshLayout.BaseRefreshListener() {
            @Override
            public void refresh() {
                getGlobalDataV3(REFRESH_FROM_PULL);
            }

            @Override
            public void loadMore() {
            }
        });
        mEmptyRefreshLayout.setOnChildScrollCallback(new PullToRefreshLayout.OnChildScrollCallback() {
            @Override
            public boolean canChildScroll(@NonNull PullToRefreshLayout parent, @Nullable View child, int direction) {
                return mEmptyLayoutScrollView.canScrollVertically(direction == PullToRefreshLayout.SCROLL_UP ? -1 : 1);
            }
        });
        mEmptySettingButton.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_SETTINGS, null);
                Router.build(DeepLink.ACTIVITY_URI_SETTING).go(mContext);
            }
        });
    }

    private void initLayerLayout() {
        mLayerAvatar.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                goSelfInfo();
            }
        });
        mProfileAvatar.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                goSelfInfo();
            }
        });
        mUserInfoTv.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                goSelfInfo();
            }
        });
        mSettingButton.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_SETTINGS, null);
                Router.build(DeepLink.ACTIVITY_URI_SETTING).go(mContext);
            }
        });
        mFeedbackButton.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mGlobalData != null && !TextUtils.isEmpty(mGlobalData.feedbackUrl)) {
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_HELP, null);
                    Router.build(mGlobalData.feedbackUrl).go(mContext);
                }
            }
        });
        mSkinButton.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                Router.build(DeepLink.EXP_SKIN_THEME).go(mContext, new RouteNotFoundCallback(mContext));
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_THEME, null);
            }
        });
    }

    private void onAppBarOffsetChanged(int yOffset, int scrollRange) {
        if (yOffset < 0 || yOffset > scrollRange) {
            return;
        }

        mLayerAvatar.setVisibility(mCurrentState == State.EXPANDED ? View.INVISIBLE : View.VISIBLE);
        mProfileAvatar.setVisibility(mCurrentState == State.EXPANDED ? View.VISIBLE : View.INVISIBLE);

        if (yOffset < mMax) {//放缩中
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mLayerAvatar.getLayoutParams();
            lp.height = lp.width = avatarLargeSize - (avatarLargeSize - avatarSmallSize) * yOffset / mMax;
            int leftMargin = fromMarginStart + (toMarginStart - fromMarginStart) * yOffset / mMax;
            int topMargin = fromMarginTop + (toMarginTop - fromMarginTop) * yOffset / mMax;
            lp.setMargins(leftMargin, topMargin, lp.rightMargin, lp.bottomMargin);
            mLayerAvatar.setLayoutParams(lp);
            mUserInfoTv.setVisibility(View.INVISIBLE);
        } else {//收起状态
            mUserInfoTv.setVisibility(View.VISIBLE);
            mUserInfoTv.setText(mGlobalData.userInfo.realName);
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mLayerAvatar.getLayoutParams();
            lp.height = lp.width = avatarSmallSize;
            lp.setMargins(toMarginStart, toMarginTop, lp.rightMargin, lp.bottomMargin);
            mLayerAvatar.setLayoutParams(lp);
        }
    }

    @Override
    public void onDateLoad() {

    }

    @Override
    public void onGlobalData(GlobalData data, int from) {
        if (data == null) {
            return;
        }
        mLayerLayout.setVisibility(View.VISIBLE);
        mSkinLayout.setVisibility(View.VISIBLE);
        mExperienceRefreshLayout.setVisibility(View.VISIBLE);
        mEmptyRefreshLayout.setVisibility(View.GONE);
        List<GlobalData.Tab> oldTabs = null;
        if (mGlobalData != null) {
            oldTabs = mGlobalData.cloneTabs();
        }
        mGlobalData = data;
        setHorizontalScrollEnable();
        updateAvatarLayout();
        updateProfileLayout();
        if (from == REFRESH_FROM_TAB) {
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_REFRESH_EXP_FROM_TAB));
        }
        if (mGlobalData.tabs != null) {//切换tab只刷新头
            buildTabs(from, oldTabs);
        }

        //预加载司龄动画逻辑
        if (data.entryInfo != null) {
            NetworkFileUtil.getDownloadFile(mContext, data.entryInfo.entrySourceUrl, data.entryInfo.md5);
        }
        //第一次网络请求加载周年动画
        String cultureType = GlobalDataUtil.getCultureType(data.userInfo);
        if (shouldShowAnimation(cultureType) && mUserVisible && data.entryInfo != null) {
            if (GlobalDataUtil.CULTURE_TYPE_BIRTHDAY.equals(cultureType)) {
                showAnniversaryAnimation(MineAnimFragment.TYPE_BIRTHDAY, String.valueOf(data.entryInfo.years));
            } else if (GlobalDataUtil.CULTURE_TYPE_SENIORITY.equals(cultureType)) {
                showAnniversaryAnimation(MineAnimFragment.TYPE_HONOR, String.valueOf(data.entryInfo.years));
            }
        }
    }

    @Override
    public void showGlobalCacheData(GlobalData data) {//缓存不加载周年动画，可能是几天前的数据
        if (data == null) {
            showEmpty();
            return;
        }
        mLayerLayout.setVisibility(View.VISIBLE);
        mSkinLayout.setVisibility(View.VISIBLE);
        mExperienceRefreshLayout.setVisibility(View.VISIBLE);
        mEmptyRefreshLayout.setVisibility(View.GONE);
        mGlobalData = data;
        setHorizontalScrollEnable();
        updateAvatarLayout();
        updateProfileLayout();
        if (mGlobalData.tabs != null) {
            buildTabs(REFRESH_FROM_CACHE, null);
        }
    }

    @Override
    public void finishRefresh() {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mExperienceRefreshLayout.finishRefresh();
                mEmptyRefreshLayout.finishRefresh();
            }
        });
    }

    @Override
    public void showEmpty() {//骨架屏
        mLayerLayout.setVisibility(View.GONE);
        mSkinLayout.setVisibility(View.GONE);
        mExperienceRefreshLayout.setVisibility(View.GONE);
        mEmptyRefreshLayout.setVisibility(View.VISIBLE);
    }

    private void setHorizontalScrollEnable() {
        boolean gray = false;
        if (!TextUtils.isEmpty(mGlobalData.tabsVer)) {
            try {
                gray = Integer.parseInt(mGlobalData.tabsVer) >= 2;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void updateProfileLayout() {
        if (mProfileLayout != null) {
            mProfileLayout.setData(mGlobalData);
        }
    }

    private void updateAvatarLayout() {
        //头像
        String avatar = PreferenceManager.UserInfo.getUserCover();
        if (TextUtils.isEmpty(avatar) && mGlobalData != null && mGlobalData.userInfo != null) {
            avatar = mGlobalData.userInfo.avatar;
        }
        ImageLoader.load(mContext, mLayerAvatarIv, avatar, R.drawable.profile_section_avatar_default_white);
        ImageLoader.load(mContext, mProfileAvatarIv, avatar, R.drawable.profile_section_avatar_default_white);
        //挂件
        String pendantSmall = imDdService.getPendan();
        if (!TextUtils.isEmpty(pendantSmall)) {
            ImageLoader.load(mContext, mLayerPendantIv, pendantSmall);
            ImageLoader.load(mContext, mProfilePendantIv, pendantSmall);
        } else {
            imDdService.getPendanByNet(new Callback<String>() {
                @Override
                public void onSuccess(String str) {
                    if (!TextUtils.isEmpty(str)) {
                        ImageLoader.load(mContext, mLayerPendantIv, str);
                        ImageLoader.load(mContext, mProfilePendantIv, str);
                    } else {
                        mLayerPendantIv.setImageResource(0);
                        mProfilePendantIv.setImageResource(0);
                    }
                }

                @Override
                public void onFail() {

                }
            });
        }
        //背景色
        boolean useDefault = true;
        int[] colors = {0xffffffff, 0xffffffff, 0xfffafafa};
        String imagePart1 = null;
        String imagePart2 = null;
        String imagePart3 = null;
        String imageType = null;
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        if (themeData != null) {
            imageType = themeData.imageType;
            useDefault = false;
            try {
                JSONObject themeConfig = themeData.getJson();
                colors[0] = ColorUtil.parseColor(themeConfig.optString("insightBg_color1"), 0xffffffff);
                colors[1] = ColorUtil.parseColor(themeConfig.optString("insightBg_color2"), 0xffffffff);
                colors[2] = ColorUtil.parseColor(themeConfig.optString("insightBg_color3"), 0xfffafafa);
            } catch (Exception e) {
                e.printStackTrace();
                imageType = "01";
                colors = new int[]{0xffffffff, 0xffffffff, 0xfffafafa};
            }
            try {
                File themePath = themeData.getDir();
                if (themePath != null && themePath.exists()) {
                    imagePart1 = themePath.getPath() + File.separator + "exp_part1.png";
                    imagePart2 = themePath.getPath() + File.separator + "exp_part2.png";
                    imagePart3 = themePath.getPath() + File.separator + "exp_part3.png";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (isFile(imagePart1)) {
                    loadThemeBgImage(imagePart1, mThemeLeftIv, true);
                } else {
                    mThemeLeftIv.setImageDrawable(null);
                }

                if (isFile(imagePart2)) {
                    loadThemeBgImage(imagePart2, mThemeRightIv, false);
                } else {
                    mThemeRightIv.setImageDrawable(null);
                }

                if (isFile(imagePart3)) {
                    Glide.with(mContext).load(imagePart3).into(mThemeCenterIv);
                } else {
                    mThemeCenterIv.setImageDrawable(null);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            if (mGlobalData != null && mGlobalData.bgImage != null &&
                    !TextUtils.isEmpty(mGlobalData.bgImage.color1) &&
                    !TextUtils.isEmpty(mGlobalData.bgImage.color2) &&
                    !TextUtils.isEmpty(mGlobalData.bgImage.color3)) {
                try {
                    int top = ColorUtil.parseColor(mGlobalData.bgImage.color1, 0xffffffff);
                    int middle = ColorUtil.parseColor(mGlobalData.bgImage.color2, 0xffffffff);
                    int bottom = ColorUtil.parseColor(mGlobalData.bgImage.color3, 0xfffafafa);
                    colors[0] = top;
                    colors[1] = middle;
                    colors[2] = bottom;
                    useDefault = false;
                    if (mGlobalData != null && mGlobalData.bgImage != null && !TextUtils.isEmpty(mGlobalData.bgImage.imagePart1)) {
                        loadThemeBgImage(mGlobalData.bgImage.imagePart1, mThemeLeftIv, true);
                    } else {
                        mThemeLeftIv.setImageDrawable(null);
                    }

                    if (mGlobalData != null && mGlobalData.bgImage != null && !TextUtils.isEmpty(mGlobalData.bgImage.imagePart2)) {
                        loadThemeBgImage(mGlobalData.bgImage.imagePart2, mThemeRightIv, false);
                    } else {
                        mThemeRightIv.setImageDrawable(null);
                    }

                    if (mGlobalData != null && mGlobalData.bgImage != null && !TextUtils.isEmpty(mGlobalData.bgImage.imagePart3)) {
                        Glide.with(mContext).load(mGlobalData.bgImage.imagePart3).into(mThemeCenterIv);
                    } else {
                        mThemeCenterIv.setImageDrawable(null);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (useDefault) {
            Glide.with(mContext).load(R.drawable.exp_default_bkgnd).into(mThemeLeftIv);
            mThemeRightIv.setImageDrawable(null);
            mThemeCenterIv.setImageDrawable(null);
            colors = new int[]{0xffffffff, 0xffffffff, 0xfffafafa};
        }
        GradientDrawable bkGndDrawable = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors);
        bkGndDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        bkGndDrawable.setGradientCenter(0.0f, 0.65f);
        mSkinLayout.setBackground(bkGndDrawable);
        mBkGndLayout.setBackgroundColor(colors[2]);

        //深色模式
        if (themeData != null) {
            mDarkMode = "02".equals(imageType);
        } else {
            if (mGlobalData != null && mGlobalData.bgImage != null) {
                mDarkMode = "02".equals(mGlobalData.bgImage.imageType);
            }
        }

        if (useDefault) {
            mDarkMode = false;
        }

        if (StatusBarConfig.enableImmersive() && isResumed()) {
            if (mDarkMode) {
                ThemeApi.checkAndSetDarkTheme(mActivity);
                MELogUtil.localI(MELogUtil.TAG_JIS, "updateData, statusBar dark mode");
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(mActivity);
                MELogUtil.localI(MELogUtil.TAG_JIS, "updateData, statusBar light mode");
            }
        }
        mHeadRefreshView.setDarkMode(mDarkMode);

        int color = mDarkMode ?
                mContext.getResources().getColor(R.color.color_button_dark) :
                mContext.getResources().getColor(R.color.color_button_normal);
        mSkinButton.setTextColor(color);
        if (mGlobalData != null) {
            mSkinHintDot.setVisibility(TextUtils.equals("1", mGlobalData.hasNewBgImage) ? View.VISIBLE : View.INVISIBLE);
        } else {
            mSkinHintDot.setVisibility(View.INVISIBLE);
        }
        mSkinHintDot.setEnabled(mDarkMode);
        mFeedbackButton.setTextColor(mDarkMode ? Color.parseColor("#FFFFFF") : Color.parseColor("#505050"));
        mFeedbackButton.setBackgroundResource(mDarkMode ? R.drawable.jdme_bg_btn_feedback_dark : R.drawable.jdme_bg_btn_feedback_light);
        mSettingButton.setTextColor(color);

        int textColor = mDarkMode ?
                mContext.getResources().getColor(R.color.color_text_dark) :
                mContext.getResources().getColor(R.color.color_text_normal);
        mUserInfoTv.setTextColor(textColor);
        mSettingBadge.setImageDrawable(ContextCompat.getDrawable(requireContext(),
                mDarkMode ? R.drawable.red_dot_color_white : R.drawable.red_dot_color_red));
        if (mGlobalData != null) {
            mFeedbackButton.setVisibility(TextUtils.isEmpty(mGlobalData.feedbackUrl) ? View.GONE : View.VISIBLE);
        } else {
            mFeedbackButton.setVisibility(View.GONE);
        }
        if (firstLoadShowFeedbackTip && !JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_EXP_FEEDBACK_TIP_SHOWN)) {
            showFeedbackTip();
            firstLoadShowFeedbackTip = !firstLoadShowFeedbackTip;
        }
    }

    @Override
    public boolean isAlive() {
        if (getActivity() == null) return false;
        if (getActivity().isFinishing() || getActivity().isDestroyed()) return false;
        return !isDetached();
    }

    private boolean needBuildTabs(List<GlobalData.Tab> newTabs, List<GlobalData.Tab> oldTabs) {
        if (oldTabs == null) {
            return true;
        }
        if (newTabs.size() != oldTabs.size()) {
            return true;
        }
        for (int i = 0; i < newTabs.size(); i++) {
            GlobalData.Tab newTab = newTabs.get(i);
            GlobalData.Tab oldTab = oldTabs.get(i);
            if (newTab == null || oldTab == null) {
                return true;
            }
            if (!TextUtils.equals(newTab.code, oldTab.code)) {
                return true;
            }
        }
        return false;
    }

    private boolean isFile(String path) {
        if (path == null) {
            return false;
        }
        try {
            File file = new File(path);
            return file.exists();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void buildTabs(int from, List<GlobalData.Tab> oldTabs) {
        if (mGlobalData == null || mGlobalData.tabs == null || mGlobalData.tabs.isEmpty()) {
            Log.i("zhn", "ExperienceFragment buildTabs err");
            return;
        }

        if (from == REFRESH_FROM_CACHE || needBuildTabs(mGlobalData.tabs, oldTabs)) {
            GlobalData.Tab tab = mGlobalData.tabs.get(0);
            mChildFragment = new ExpTabFragment();
            mChildFragment.setLayouts(tab, from, mGlobalData.tabsVer);
            getChildFragmentManager().beginTransaction()
                    .replace(R.id.fl_container, mChildFragment)
                    .commit();
        } else if (from != REFRESH_FROM_TAB && mChildFragment != null) {
            boolean needUpdate = false;
            if (from == REFRESH_FROM_INIT) {
                GlobalData.Tab tab = mGlobalData.tabs.get(0);
                needUpdate = mChildFragment.needUpdate(tab);
                mChildFragment.setLayouts(tab, from, mGlobalData.tabsVer);
            }
            mChildFragment.updateLayouts(mGlobalData.tabs.get(0), from, mGlobalData.tabsVer, needUpdate);
            getChildFragmentManager().beginTransaction()
                    .replace(R.id.fl_container, mChildFragment)
                    .commit();
        }
    }

    private boolean shouldShowAnimation(String type) {
        if (GlobalDataUtil.CULTURE_TYPE_BIRTHDAY.equals(type)) {//生日
            return !JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_AUTO);
        } else if (GlobalDataUtil.CULTURE_TYPE_SENIORITY.equals(type)) {
            return !JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_SILIN_GIF_AUTO);
        } else {
            return false;
        }
    }

    private void showAnniversaryAnimation(final String type, String years) {
        if (getActivity() == null) return;
        if (type.equals(MineAnimFragment.TYPE_BIRTHDAY) && !TenantConfigBiz.INSTANCE.isBirthdayCardEnable()) { // 禁用生日
            return;
        } else if (type.equals(MineAnimFragment.TYPE_HONOR) && !TenantConfigBiz.INSTANCE.isWorkingAgeEnable()) { // 禁用司龄
            return;
        }
        FragmentManager manager = getActivity().getSupportFragmentManager();
        if (manager.isStateSaved()) return;

        if (MineAnimFragment.TYPE_HONOR.equals(type)) {//司龄动画 没有资源不处理
            if (mGlobalData == null || mGlobalData.entryInfo == null) return;
            File animFile = NetworkFileUtil.getDownloadFile(getContext(), mGlobalData.entryInfo.entrySourceUrl, mGlobalData.entryInfo.md5);
            if (null == animFile) return;
        }

        MineAnimFragment mAnimDialog = new MineAnimFragment();
        Bundle bundles = new Bundle();
        bundles.putString(MineAnimFragment.ARG_TYPE, type);
        bundles.putInt(MineAnimFragment.ARG_YEAR, Integer.parseInt(years));
        if (MineAnimFragment.TYPE_HONOR.equals(type)) {
            if (mGlobalData == null || mGlobalData.entryInfo == null) return;
            bundles.putString(MineAnimFragment.ARG_YEAR_ENTRY_SOURCE_URL, mGlobalData.entryInfo.entrySourceUrl);
            bundles.putString(MineAnimFragment.ARG_YEAR_MD5, mGlobalData.entryInfo.md5);
        }
        mAnimDialog.setArguments(bundles);
        mAnimDialog.show(manager, "anim_dialog");
        switch (type) {
            case MineAnimFragment.TYPE_BIRTHDAY:
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_AUTO, true);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_TIME, System.currentTimeMillis());
                break;
            case MineAnimFragment.TYPE_HONOR:
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SILIN_GIF_AUTO, true);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SILIN_GIF_TIME, System.currentTimeMillis());
                break;
        }
    }

    @Override
    public void update(Observable o, final Object data) {
        //更新头像
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (mLayerAvatarIv != null) {
                    ImageLoader.load(mContext, mLayerAvatarIv, (String) data, R.drawable.profile_section_avatar_default_white);
                }
                if (mProfileAvatarIv != null) {
                    ImageLoader.load(mContext, mProfileAvatarIv, (String) data, R.drawable.profile_section_avatar_default_white);
                }
            }
        });
    }

    @Override
    public void onSignatureChanged(String signature) {

    }

    @Override
    public void onSignatureChangedNew(CharSequence signature) {
        if (mContext != null) {
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_EXP_PROFILE_REFRESH_SIGNATURE_NEW));
        }
    }

    @Override
    public void onAvatarChanged(String avatar) {
        ImageLoader.load(mContext, mLayerAvatarIv, avatar, R.drawable.profile_section_avatar_default_white);
        ImageLoader.load(mContext, mProfileAvatarIv, avatar, R.drawable.profile_section_avatar_default_white);
    }

    private void goSelfInfo() {
        Intent intent = new Intent(mContext, FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ExperienceSelfInfoFragment.class.getName());
        mContext.startActivity(intent);
        //埋点
        Map<String, String> map = new HashMap<>();
        map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_AVATAR.keyName
                , ExpJDMAConstants.MainInfo.MAIN_INFO_AVATAR.KeyValue);
        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
    }

    private void loadThemeBgImage(String url, final ImageView imageView, final boolean left) {
        Glide.with(mContext).load(url).into(new SimpleTarget<Drawable>() {
            @Override
            public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                if (resource instanceof BitmapDrawable && isAdded()) {
                    Bitmap src = ((BitmapDrawable) resource).getBitmap();
                    if (src != null) {
                        int dstHeight = getResources().getDimensionPixelSize(R.dimen.exp_skin_layout_height) + QMUIStatusBarHelper.getStatusbarHeight(mContext);
                        int dstWidth = ScreenUtil.getScreenWidth(mContext);
                        //图片过长
                        int maxWidth = dstWidth * src.getHeight() / dstHeight;
                        if (maxWidth < src.getWidth()) {
                            int x = left ? 0 : (src.getWidth() - maxWidth);
                            Bitmap dst = Bitmap.createBitmap(src, x, 0, maxWidth, src.getHeight());
                            imageView.setImageBitmap(dst);//src不能recycle，否则glide会异常，src是glide缓存图片
                        } else {
                            imageView.setImageDrawable(resource);
                        }
                    } else {
                        imageView.setImageDrawable(resource);
                    }
                } else {
                    imageView.setImageDrawable(resource);
                }
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        EventBusMgr.getInstance().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        EventBusMgr.getInstance().unregister(this);
    }


    @SuppressWarnings("unused")
    public void onEventMainThread(final String eventStr) {
        if (eventStr.equals(EVENT_GUIDE_SHOW_ANIMATION)) {
            showGuideBottomAnimation();
        }
    }


    /**
     * 显示底部引导的手势动效
     */
    private void showGuideBottomAnimation() {
        // 动态加载布局
        View animationView = LayoutInflater.from(getContext())
                .inflate(R.layout.jdme_guide_bottom_animation, (ViewGroup) mBkGndLayout, false);

        // 设置布局参数（底部对齐）
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
        );
        params.gravity = Gravity.BOTTOM;

        // 添加到容器
        ((ViewGroup) mBkGndLayout).addView(animationView, params);


        AppCompatImageView ivHand = animationView.findViewById(R.id.iv_hand);
        ObjectAnimator animator = ObjectAnimator.ofFloat(ivHand, "translationY", 0, -CommonUtils.dp2px(25));
        // 动画持续时间为2秒
        animator.setDuration(ANIMATION_DURATION);
        // 添加动画监听器
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 动画结束时执行的操作
                MELogUtil.localD(TAG, "guide底部手势动效执行结束");
                ((ViewGroup) mBkGndLayout).removeView(animationView);
            }
        });

        // 默认执行一次动画，就结束
        animator.start();
    }

}
