package com.jd.oa.experience.model;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class RecommendV2Data {


    @SerializedName("list")
    private List<Recommend> list;

    public List<Recommend> getList() {
        return list;
    }

    public void setList(List<Recommend> list) {
        this.list = list;
    }

    public static class Recommend {
        @SerializedName("tplType")
        private String tplType = "";
        @SerializedName("id")
        private String id = "";
        @SerializedName("title")
        private String title;
        @SerializedName("icon")
        private String icon;
        @SerializedName("jumpUrl")
        private String jumpUrl;
        @SerializedName("jumpText")
        private String jumpText;
        @SerializedName("data")
        private Data data;

        public String getTplType() {
            return tplType;
        }

        public void setTplType(String tplType) {
            this.tplType = tplType;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getJumpUrl() {
            return jumpUrl;
        }

        public void setJumpUrl(String jumpUrl) {
            this.jumpUrl = jumpUrl;
        }

        public String getJumpText() {
            return jumpText;
        }

        public void setJumpText(String jumpText) {
            this.jumpText = jumpText;
        }

        public Data getData() {
            return data;
        }

        public void setData(Data data) {
            this.data = data;
        }

        public static class Data {
            @SerializedName("footer")
            private java.util.List<Footer> footer;
            @SerializedName("body")
            private Body body;

            public List<Footer> getFooter() {
                return footer;
            }

            public void setFooter(List<Footer> footer) {
                this.footer = footer;
            }

            public Body getBody() {
                return body;
            }

            public void setBody(Body body) {
                this.body = body;
            }

            public static class Body {
                @SerializedName("items")
                private java.util.ArrayList<Items> items;

                public ArrayList<Items> getItems() {
                    return items;
                }

                public void setItems(ArrayList<Items> items) {
                    this.items = items;
                }

                public static class Items {
                    @SerializedName("image")
                    private String image;
                    @SerializedName("id")
                    private String id = "";
                    @SerializedName("url")
                    private String url;

                    public String getImage() {
                        return image;
                    }

                    public void setImage(String image) {
                        this.image = image;
                    }

                    public String getId() {
                        return id;
                    }

                    public void setId(String id) {
                        this.id = id;
                    }

                    public String getUrl() {
                        return url;
                    }

                    public void setUrl(String url) {
                        this.url = url;
                    }
                }
            }

            public static class Footer {
                @SerializedName("id")
                private String id = "";
                @SerializedName("title")
                private String title;
                @SerializedName("url")
                private String url;

                public String getId() {
                    return id;
                }

                public void setId(String id) {
                    this.id = id;
                }

                public String getTitle() {
                    return title;
                }

                public void setTitle(String title) {
                    this.title = title;
                }

                public String getUrl() {
                    return url;
                }

                public void setUrl(String url) {
                    this.url = url;
                }
            }
        }
    }
}
