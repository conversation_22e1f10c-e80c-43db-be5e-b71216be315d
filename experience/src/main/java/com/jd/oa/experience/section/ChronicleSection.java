package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.NinePatchDrawable;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.experience.R;

import com.jd.oa.experience.adapter.ChronicleAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.ChronicleData;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.repo.ChronicleRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.theme.util.ThemeToast;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ScreenUtil;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/******************************    大事记    *******************************/
public class ChronicleSection extends StatelessSection implements Destroyable {

    private static int mParentWidth;
    private static int mPivotYOffset;
    private static int mCardWidth;

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private ChronicleSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private ChronicleRepo repo;
    private ChronicleData mData;


    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                case Constants.ACTION_REPORT_CHRONICLE_SCROLL:
                    ExpJDMAUtil.onMomentEventClick(ExpJDMAConstants.EXP_MAIN_MOMENTS_MEMORABILIA, new HashMap<String, String>());
                    break;
                default:
                    break;
            }
        }
    };

    public ChronicleSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_chronicle)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = ChronicleRepo.get(mContext);

        mParentWidth = DensityUtil.dp2px(AppBase.getAppContext(), 344);//屏宽，使用固定值，否则宽度不同的手机卡片放缩比例不一致
        mPivotYOffset = DensityUtil.dp2px(AppBase.getAppContext(), 35);
        mCardWidth = mContext.getResources().getDimensionPixelSize(R.dimen.chronicle_card_width) + DensityUtil.dp2px(mContext, 24);
        Drawable drawable = ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.jdme_bg_chronicle_shadow, null);
        if (drawable instanceof NinePatchDrawable) {
            NinePatchDrawable ninePatchDrawable = (NinePatchDrawable) drawable;
            Rect padding = new Rect();
            if (ninePatchDrawable.getPadding(padding)) {
                mPivotYOffset = padding.bottom;
                mCardWidth = mContext.getResources().getDimensionPixelSize(R.dimen.chronicle_card_width) + padding.left + padding.right;
            }
        }

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        intentFilter.addAction(Constants.ACTION_REPORT_CHRONICLE_SCROLL);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        ChronicleData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getChronicleData(new LoadDataCallback<ChronicleData>() {
            @Override
            public void onDataLoaded(ChronicleData chronicleData) {
                showData(chronicleData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(ChronicleData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ChronicleSection.ItemViewHolder(view, mContext);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mTitleView.setTitle(false, mData.icon, mData.title, mData.jumpUrl, mData.jumpText);
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                    ExpJDMAUtil.onMomentEventClick(ExpJDMAConstants.EXP_MAIN_MOMENTS_MEMORABILIA_DETAIL, new HashMap<String, String>());
                }
            }
        });

        if (mData.userEventList == null || mData.userEventList.size() <= 0) {//没有卡片
            mItemViewHolder.mEmptyIv.setVisibility(View.VISIBLE);
            mItemViewHolder.mEmptyTv.setVisibility(View.VISIBLE);
            mItemViewHolder.mResetView.setVisibility(View.GONE);
            mItemViewHolder.mRv.setVisibility(View.GONE);
            mItemViewHolder.mSingleCard.setVisibility(View.GONE);
            mItemViewHolder.mDogIv.setVisibility(View.GONE);
            return;
        }

        mItemViewHolder.mEmptyIv.setVisibility(View.GONE);
        mItemViewHolder.mEmptyTv.setVisibility(View.GONE);
        mItemViewHolder.mDogIv.setVisibility(View.VISIBLE);

        FrameLayout.LayoutParams lpDog = (FrameLayout.LayoutParams) mItemViewHolder.mDogIv.getLayoutParams();
        int marginStart = ScreenUtil.getScreenWidth(mContext) / 2 + DensityUtil.dp2px(mContext, 50);
        lpDog.setMarginStart(marginStart);
        mItemViewHolder.mDogIv.setLayoutParams(lpDog);
        mItemViewHolder.mRv.setVisibility(mData.userEventList.size() > 1 ? View.VISIBLE : View.GONE);
        mItemViewHolder.mSingleCard.setVisibility(mData.userEventList.size() == 1 ? View.VISIBLE : View.GONE);

        if (mData.userEventList.size() > 1) {
            mItemViewHolder.mRv.setItemViewCacheSize(10);
            mItemViewHolder.mRv.setLayoutManager(mItemViewHolder.mLinearLayoutManager);
            mItemViewHolder.mSnapHelper.attachToRecyclerView(mItemViewHolder.mRv);
            mItemViewHolder.mRv.removeOnScrollListener(mItemViewHolder.mOnScrollListener);
            mItemViewHolder.mRv.addOnScrollListener(mItemViewHolder.mOnScrollListener);
            mItemViewHolder.mRv.setAdapter(new ChronicleAdapter(mContext, mData.userEventList, mCardWidth));
            //循环，position大小不会影响内存和性能
            mItemViewHolder.mLinearLayoutManager.scrollToPositionWithOffset(
                    mData.userEventList.size() * 1000, (ScreenUtil.getScreenWidth(mContext) - mCardWidth) / 2);
            mItemViewHolder.mRv.post(new Runnable() {
                @Override
                public void run() {//初始化水平居中
                    if (mData != null && mData.userEventList != null) {
                        View snapView = mItemViewHolder.mSnapHelper.findSnapView(mItemViewHolder.mLinearLayoutManager);
                        if (snapView == null) {
                            return;
                        }

                        //背景图位置
                        int middle = ScreenUtil.getScreenWidth(mContext) / 2;
                        int childMiddle = (int) (snapView.getX() + mContext.getResources().getDimensionPixelSize(R.dimen.chronicle_card_width) / 2);
                        int gap = (middle - childMiddle);
                        mItemViewHolder.mRv.smoothScrollBy(gap, 0);

                        //狗图片位置
                        String url = (String) snapView.getTag();
                        mItemViewHolder.updateDogImage(url);
                        mItemViewHolder.mDogIv.setAlpha(1.0f);
                    }
                }
            });
            boolean isCN = TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN");
            mItemViewHolder.mResetTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, isCN ? 14 : 11);
            mItemViewHolder.mResetView.setVisibility(View.VISIBLE);
            mItemViewHolder.mResetView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mItemViewHolder != null && mItemViewHolder.mRv != null) {
                        if (mItemViewHolder.mResetView.getAlpha() != 0.0f) {
                            mItemViewHolder.mRv.smoothScrollToPosition(0);
                        }
                    }
                }
            });
            mItemViewHolder.mResetView.animate().alpha(0.0f).setDuration(50);
        } else {
            final ChronicleData.Card card = mData.userEventList.get(0);
            if (card != null) {
                ImageView bkGndIv = mItemViewHolder.mSingleCard.findViewById(R.id.bkgnd_iv);
                ImageView gradient = mItemViewHolder.mSingleCard.findViewById(R.id.bkgnd_gradient);
                TextView titleTv = mItemViewHolder.mSingleCard.findViewById(R.id.title_tv);
                TextView detailTv = mItemViewHolder.mSingleCard.findViewById(R.id.detail_tv);
                TextView indexTv = mItemViewHolder.mSingleCard.findViewById(R.id.index_tv);
                TextView dateTv = mItemViewHolder.mSingleCard.findViewById(R.id.date_tv);
                RequestOptions requestOptions = new RequestOptions().placeholder(R.drawable.jdme_bg_chronicle_default).error(R.drawable.jdme_bg_chronicle_default).diskCacheStrategy(DiskCacheStrategy.ALL);
                Glide.with(mContext).load(card.imageUrl).apply(requestOptions).into(bkGndIv);
                int startColor = ColorUtil.parseColor(card.startColor, 0xFFEBEBEB);
                int endColor = ColorUtil.parseColor(card.endColor, 0xFFEBEBEB);
                final int[] colors = {startColor, endColor};
                GradientDrawable bkGndDrawable = new GradientDrawable(GradientDrawable.Orientation.TL_BR, colors);
                bkGndDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                gradient.setImageDrawable(bkGndDrawable);
                indexTv.setText("1/1");
                titleTv.setText(card.eventTitle);
                detailTv.setText(card.eventContent);
                //日期渐变色
                GradientDrawable dateDrawable = new GradientDrawable(GradientDrawable.Orientation.TL_BR,
                        new int[]{startColor & 0x00ffffff | 0xCC000000, endColor & 0x00ffffff | 0xCC000000});
                dateDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                dateDrawable.setCornerRadii(new float[] {0, 0, DensityUtil.dp2px(mContext, 11), DensityUtil.dp2px(mContext, 11), 0, 0, 0, 0});
                dateTv.setBackground(dateDrawable);
                if (!TextUtils.isEmpty(card.eventTime)) {
                    dateTv.setVisibility(View.VISIBLE);
                    dateTv.setText(card.eventTime);
                } else {
                    dateTv.setVisibility(View.GONE);
                }
                //点击预埋
                mItemViewHolder.mSingleCard.setOnClickListener(new NoDoubleClickListener() {
                    @Override
                    protected void onNoDoubleClick(View v) {
                        if (!TextUtils.isEmpty(card.eventUrl)) {
                            Router.build(card.eventUrl).go(mContext);
                            ExpJDMAUtil.onMomentEventClick(ExpJDMAConstants.EXP_MAIN_MOMENTS_MEMORABILIA_CLICK, new HashMap<String, String>());
                        }
                    }
                });

                mItemViewHolder.updateDogImage(card.joyUrl);
                mItemViewHolder.mDogIv.setAlpha(1.0f);
            }
            mItemViewHolder.mResetView.setVisibility(View.GONE);
        }
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        private Context mContext;
        public TitleView mTitleView;
        public RecyclerView mRv;
        public ImageView mDogIv;
        public View mSnapItem;
        public View mEmptyIv;
        public View mEmptyTv;
        public View mSingleCard;
        public View mResetView;
        public TextView mResetTv;
        private LinearLayoutManager mLinearLayoutManager;
        private final PagerSnapHelper mSnapHelper = new PagerSnapHelper();

        public RecyclerView.OnScrollListener mOnScrollListener = new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NotNull final RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    return;
                }

                View viewIdle = mSnapHelper.findSnapView(mLinearLayoutManager);
                if (viewIdle == null) {
                    return;
                }

                int position = mLinearLayoutManager.getPosition(viewIdle);
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    if (position == 0) {
                        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!recyclerView.canScrollHorizontally(-1)) {
                                    ThemeToast.showToast(mContext, R.string.exp_chronicle_toast_first);
                                }
                            }
                        }, 200);
                    }
                } else if (newState == RecyclerView.SCROLL_STATE_SETTLING) {
                    if (position == mLinearLayoutManager.getItemCount() - 1) {
                        mResetView.animate().alpha(1.0f).setDuration(50);
                    }
                }
            }

            @Override
            public void onScrolled(@NotNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                for (int i = 0; i < recyclerView.getChildCount(); i++) {//这里getChildCount一般是2或3
                    View child = recyclerView.getChildAt(i);
                    int childMiddle = (int) (child.getX() + child.getWidth() / 2);
                    int gap = Math.abs(recyclerView.getWidth() / 2 - childMiddle);
                    float fraction = gap * 1.0f / mParentWidth / 2;
                    final float MIN_SCALE = 0.65f;
                    float scaleFactor = MIN_SCALE + (1 - MIN_SCALE) * (1 - Math.abs(fraction));
                    child.setPivotX(child.getWidth() / 2.0f);
                    child.setPivotY(child.getHeight() - mPivotYOffset);
                    child.setScaleX(scaleFactor);
                    child.setScaleY(scaleFactor);
                    if (scaleFactor > 0.990f) {
                        mDogIv.setAlpha((scaleFactor - 0.990f) * 100.0f);
                        if (mSnapItem != child) {
                            LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(new Intent(Constants.ACTION_REPORT_CHRONICLE_SCROLL));
                        }
                        mSnapItem = child;
                    } else {
                        if (child == mSnapItem) {
                            mDogIv.setAlpha(0.0f);
                        }
                    }
                    if (mSnapItem != null) {
                        String url = (String) mSnapItem.getTag();
                        updateDogImage(url);
                    }
/*                    if (child instanceof FrameLayout) {
                        View indexTv = ((FrameLayout) child).getChildAt(1);
                        if (indexTv != null && indexTv.getId() == R.id.index_tv) {
                            indexTv.setAlpha(mDogIv.getAlpha());
                        }
                    }*/
                }
                if (mResetView.getAlpha() != 0.0f) {
                    mResetView.animate().alpha(0.0f).setDuration(50);
                }
            }
        };

        private void updateDogImage(String url) {
            RequestOptions requestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL);
            Glide.with(AppBase.getAppContext()).load(url).apply(requestOptions).into(mDogIv);
        }

        public ItemViewHolder(View itemView, Context context) {
            super(itemView);
            mContext = context;
            mTitleView = itemView.findViewById(R.id.title_view);
            mRv = itemView.findViewById(R.id.rv);
            mDogIv = itemView.findViewById(R.id.dog);
            mEmptyIv = itemView.findViewById(R.id.empty_iv);
            mEmptyTv = itemView.findViewById(R.id.empty_tv);
            mSingleCard = itemView.findViewById(R.id.single_card_layout);
            mLinearLayoutManager = new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false);
            mResetView = itemView.findViewById(R.id.reset);
            mResetTv = itemView.findViewById(R.id.reset_tv);
        }
    }
}
