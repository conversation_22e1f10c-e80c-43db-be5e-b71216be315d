package com.jd.oa.experience.repo;

import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.experience.model.HomePageData;
import com.jd.oa.experience.model.ThumbsUpData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class MyHomePageRepo {

    public MyHomePageRepo() {
    }

    //erp，查看自己主页时不能传，查看他人主页时必传
    public void getHomePageData(String erp, boolean isCount/*下拉时传false，不计入热度*/, final LoadDataCallback<HomePageData> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("viewedUserName", erp);
        params.put("isCount", isCount);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<HomePageData> response = ApiResponse.parse(info.result, HomePageData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.homepage");
    }

    //需要自己上传图片，这个接口只负责更新设置
    public void saveCustomImage(String url, final LoadDataCallback<HomePageData> callback) {
        if (TextUtils.isEmpty(url)) {
            return;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("bgImage", url);
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                Log.e("666666", "onSuccess:111 " + info);
                ApiResponse<HomePageData> response = ApiResponse.parse(info.result, HomePageData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.homepage.saveBgImage");
    }

    public void thumbsUp(String erp, String subjectType, String subjectId, String count, final LoadDataCallback<ThumbsUpData> callback) {
        if (TextUtils.isEmpty(erp)) {
            return;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("likedUserName", erp);//被点赞人
        params.put("subjectType", subjectType);//点赞主体类型（01：个人主页）
        params.put("subjectId", subjectId);//点赞主体ID，subjectType为01时可为空
        params.put("count", count);
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<ThumbsUpData> response = ApiResponse.parse(info.result, ThumbsUpData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.homepage.like");
    }

    public static void changeBGImage(File iconFile, SimpleRequestCallback<String> callBack) {
        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("file", iconFile);
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().upload(NetworkConstant.API_UPLOAD_FILE_TO_JFS, params, fileMap, callBack);
    }
}