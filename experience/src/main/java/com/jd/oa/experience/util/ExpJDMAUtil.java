package com.jd.oa.experience.util;

import android.content.Context;

import com.alibaba.fastjson.JSON;
import com.jd.oa.AppBase;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class ExpJDMAUtil {
    public static void onEventClick(String eventId, Map<String, String> paramMap) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        param.put("name", eventId);
        if (paramMap != null) {
            param.putAll(paramMap);
        }
        String eventParam = JSON.toJSONString(paramMap);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId,
                PreferenceManager.UserInfo.getUserName(), eventParam, "", "", "EXP_Main", param);
    }

    /**
     * id:卡片的id
     * tplName：卡片的tplName
     * clickId：（header区-1，body区-2，footer区-3
     * clickValue: 接口中items中每个item的id
     * index:（从0开始）
     */
    public static void onInsightsEventClick(String cardId, String tplName, String clickId, String clickValue, String itemIndex, String cardIndex , String itemId) {
        onInsightsEventClick(cardId, tplName, clickId, clickValue, itemIndex, cardIndex, itemId, null);
    }

    public static void onInsightsEventClick(String cardId, String tplName, String clickId, String clickValue, String itemIndex, String cardIndex , String itemId, Map<String, String> extra) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        param.put("cardId", cardId);
        param.put("tplName", tplName);
        param.put("clickId", clickId);
        param.put("clickValue", clickValue);
        param.put("itemIndex", itemIndex);
        param.put("cardIndex", cardIndex);
        param.put("trace", StringUtils.isEmptyWithTrim(itemId) ? "" : cardId + "-" + itemId);
        if (extra != null && !extra.isEmpty()) {
            param.putAll(extra);
        }
        String eventParam = JSON.toJSONString(param);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", ExpJDMAConstants.EXP_MAIN_INSIGHTS,
                PreferenceManager.UserInfo.getUserName(), eventParam, "", "", "EXP_Main", param);
    }

    public static void onInsightsEventPagePV(Context context, String cardId, int cardIndex) {
        HashMap<String, String> params = new HashMap<>();
        params.put("erp", PreferenceManager.UserInfo.getUserName());
        params.put("cardId", cardId);
        params.put("cardIndex", String.valueOf(cardIndex));
        JDMAUtils.onEventPagePV(context, ExpJDMAConstants.EXP_MAIN_INSIGHTS, "",
                PreferenceManager.UserInfo.getUserName(), "EXP_Main", params);
    }

    public static void onSelfInfoEventClick(String clickName) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        param.put("clickName", clickName);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", ExpJDMAConstants.EXP_MAIN_EDIT_INFO_INFO,
                PreferenceManager.UserInfo.getUserName(), "", "", "", "EXP_Main_EditInfo", param);
    }

    public static void onHomePageEventClick(String eventId, Map<String, String> paramMap) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        if (paramMap != null) {
            param.putAll(paramMap);
        }
        String eventParam = JSON.toJSONString(paramMap);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId,
                PreferenceManager.UserInfo.getUserName(), eventParam, "", "", "EXP_Main_Home", param);
    }

    public static void onDevelopmentEventClick(String eventId, Map<String, String> paramMap) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        if (paramMap != null) {
            param.putAll(paramMap);
        }
        String eventParam = JSON.toJSONString(paramMap);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId,
                PreferenceManager.UserInfo.getUserName(), eventParam, "", "", "EXP_Main_Development", param);
    }

    public static void onMomentEventClick(String eventId, Map<String, String> paramMap) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        if (paramMap != null) {
            param.putAll(paramMap);
        }
        String eventParam = JSON.toJSONString(paramMap);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId,
                PreferenceManager.UserInfo.getUserName(), eventParam, "", "", "EXP_Main_Moments", param);
    }
}
