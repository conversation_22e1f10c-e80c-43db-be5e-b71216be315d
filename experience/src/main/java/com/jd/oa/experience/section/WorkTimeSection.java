package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.WorkTimeAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.WorkTimeData;
import com.jd.oa.experience.repo.WorkTimeRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class WorkTimeSection extends StatelessSection implements Destroyable {

    private final Context mContext;
    private final String mTabCode;
    private boolean mIsFirst;
    private final SectionedRecyclerViewAdapter mAdapter;
    private WorkTimeSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final WorkTimeRepo repo;
    private WorkTimeData mData;


    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                default:
                    break;
            }
        }
    };

    public WorkTimeSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_worktime)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = WorkTimeRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        WorkTimeData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getWorkTimeData(new LoadDataCallback<WorkTimeData>() {
            @Override
            public void onDataLoaded(WorkTimeData workTimeData) {
                showData(workTimeData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

                setState(State.FAILED);
            }
        });
    }

    private void showData(WorkTimeData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new WorkTimeSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mChart.setUsePercentValues(true);
        mItemViewHolder.mChart.setExtraOffsets(8, 8, 8, 8);
        mItemViewHolder.mChart.getDescription().setEnabled(false);
        mItemViewHolder.mChart.setDrawEntryLabels(false);//每片饼上没有文字描述

        mItemViewHolder.mChart.setDrawRoundedSlices(false);

        mItemViewHolder.mChart.setDragDecelerationFrictionCoef(0.95f);

        mItemViewHolder.mChart.setDrawHoleEnabled(true);//没有中空
        mItemViewHolder.mChart.setDrawSlicesUnderHole(false);//没有中空

//        mItemViewHolder.mChart.setHoleColor(Color.parseColor("#0F000000"));//没有中空
        mItemViewHolder.mChart.setHoleRadius(0f);//中间洞的大小

//        mItemViewHolder.mChart.setTransparentCircleColor(Color.WHITE);//内圈半透明
//        mItemViewHolder.mChart.setTransparentCircleAlpha(255);
        mItemViewHolder.mChart.setTransparentCircleRadius(0f);//内圈半透明

        mItemViewHolder.mChart.setDrawCenterText(false);//没有中空文字

        mItemViewHolder.mChart.setRotationAngle(0);
        // enable rotation of the mItemViewHolder.mChart by touch
        mItemViewHolder.mChart.setRotationEnabled(true);//禁止触摸旋转
        mItemViewHolder.mChart.setHighlightPerTapEnabled(true);//点击单片饼处理
        mItemViewHolder.mChart.setDragDecelerationEnabled(false);//旋转惯性

        // mItemViewHolder.mChart.setUnit(" €");
        // mItemViewHolder.mChart.setDrawUnitsInmItemViewHolder.mChart(true);

        // add a selection listener
        //mItemViewHolder.mChart.setOnmItemViewHolder.mChartValueSelectedListener(this);

//        mItemViewHolder.mChart.animateY(1000, Easing.EaseInOutQuad);//初始化动画
        initData();
    }

    private final int[] defaultColors = {
            Color.rgb(244, 152, 166), Color.rgb(255, 172, 62),
            Color.rgb(246, 227, 60), Color.rgb(172, 165, 246)
    };

    private void initData() {
        if (mData == null) return;
        setChartData();

        mItemViewHolder.mTvDate.setText(DateUtils.getFormatString(mData.getDate(), "yyyy/MM/dd"));
        mItemViewHolder.mTvDate.setVisibility(mData.getDate() == 0L ? View.GONE : View.VISIBLE);

        WorkTimeAdapter workTimeAdapter = new WorkTimeAdapter(mContext, mData.getData(), defaultColors);
        mItemViewHolder.mRvWorkTime.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false));
        if (mItemViewHolder.mRvWorkTime.getItemDecorationCount() == 0)
            mItemViewHolder.mRvWorkTime.addItemDecoration(new SpaceItemDecoration(CommonUtils.dp2px(6)));
        mItemViewHolder.mRvWorkTime.setAdapter(workTimeAdapter);

        mItemViewHolder.mTvMore.setVisibility(TextUtils.isEmpty(mData.getJumpText()) || TextUtils.isEmpty(mData.getJumpUrl()) ? View.GONE : View.VISIBLE);
        mItemViewHolder.mIvMore.setVisibility(TextUtils.isEmpty(mData.getJumpUrl()) ? View.GONE : View.VISIBLE);
        if (!TextUtils.isEmpty(mData.getJumpUrl())) {
            NoDoubleClickListener onMoreClick = new NoDoubleClickListener() {
                @Override
                protected void onNoDoubleClick(View v) {
                    if (mContext != null && !TextUtils.isEmpty(mData.getJumpUrl())) {
                        Router.build(mData.getJumpUrl()).go(mContext);
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_WORK_TIME, new HashMap<String, String>());
                    }
                }
            };
            mItemViewHolder.mLlTitle.setOnClickListener(onMoreClick);
        }
        mItemViewHolder.mTvTitle.setText(TextUtils.isEmpty(mData.getTitle()) ? mContext.getString(R.string.exp_work_time_chart_title) : mData.getTitle());
        mItemViewHolder.mTvMore.setText(TextUtils.isEmpty(mData.getJumpText()) ? mContext.getString(R.string.exp_work_time_more) : mData.getJumpText());
        if (!TextUtils.isEmpty(mData.getIcon())) {
            Glide.with(mContext).load(mData.getIcon()).error(R.drawable.jdme_exp_worktime_icon_chart).into(mItemViewHolder.mIvIconTitle);
        }

    }


    private void setChartData() {
        List<WorkTimeData.Data> timeData = mData.getData();
        boolean isEmptyData = timeData == null || timeData.size() == 0;
        mItemViewHolder.mChart.setVisibility(isEmptyData ? View.GONE : View.VISIBLE);
        mItemViewHolder.mRvWorkTime.setVisibility(isEmptyData ? View.GONE : View.VISIBLE);
        mItemViewHolder.mLlEmpty.setVisibility(isEmptyData ? View.VISIBLE : View.GONE);
        if (isEmptyData) {
            return;
        }
        ArrayList<Integer> colors = new ArrayList<>();
        ArrayList<PieEntry> entries = new ArrayList<>();
        for (int i = 0; i < mData.getData().size(); i++) {
            float num = Float.parseFloat(timeData.get(i).getValue().replaceAll("%", ""));
            entries.add(i, new PieEntry(num, "", null));
            try {
                if (!TextUtils.isEmpty(timeData.get(i).getColor())) {
                    colors.add(i, Color.parseColor(timeData.get(i).getColor()));
                }
            } catch (Exception e) {
                colors.add(i, defaultColors[i % timeData.size()]);
            }
        }
        PieDataSet dataSet = new PieDataSet(entries, "");
        dataSet.setDrawIcons(false);
        dataSet.setSliceSpace(0f);
//        dataSet.setIconsOffset(new MPPointF(0, 40));
        dataSet.setColors(colors);
        dataSet.setSelectionShift(5f);
        dataSet.setDrawValues(false);
        PieData data = new PieData(dataSet);
      /*  data.setValueFormatter(new PercentFormatter());
        data.setValueTextSize(11f);
        data.setValueTextColor(Color.WHITE);*/
//        data.setValueTypeface(tfLight);
        mItemViewHolder.mChart.setData(data);
        Legend l = mItemViewHolder.mChart.getLegend();
        l.setEnabled(false);
        // undo all highlights
        mItemViewHolder.mChart.highlightValues(null);
        mItemViewHolder.mChart.invalidate();
        mItemViewHolder.mChart.animateY(1000, Easing.EaseInOutQuad);
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public LinearLayout mLlTitle;
        public PieChart mChart;
        public RecyclerView mRvWorkTime;
        public TextView mTvDate;
        public TextView mTvMore;
        public TextView mTvTitle;
        public IconFontView mIvMore;
        public ImageView mIvIconTitle;
        public LinearLayout mLlEmpty;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mChart = itemView.findViewById(R.id.pie_chart);
            mRvWorkTime = itemView.findViewById(R.id.rv_work_time);
            mTvDate = itemView.findViewById(R.id.tv_chart_date);
            mLlTitle = itemView.findViewById(R.id.ll_chart_title);
            mTvMore = itemView.findViewById(R.id.tv_chart_more);
            mTvTitle = itemView.findViewById(R.id.tv_chart_title);
            mIvMore = itemView.findViewById(R.id.iv_chart_more);
            mIvIconTitle = itemView.findViewById(R.id.iv_icon_chart_title);
            mLlEmpty = itemView.findViewById(R.id.ll_empty_view);
        }
    }
}