package com.jd.oa.experience.view.banner;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.RecommendAdapter;
import com.jd.oa.ui.banner.ImageLoaderInterface;

public class BannerImageLoader implements ImageLoaderInterface<View> {

    private int mType;

    public BannerImageLoader(int type) {
        mType = type;
    }

    @SuppressLint({"CheckResult", "ResourceType"})
    @Override
    public void displayView(final Context context, Object path, View view) {
        String info = (String) path;
        final ImageView image = view.findViewById(R.id.iv_banner);
        Glide.with(context)
                .load(info)
                .placeholder(R.drawable.jdme_exp_recommend_banner_holder)
                .error(R.drawable.jdme_exp_recommend_banner_holder)
                .into(image);
    }

    @Override
    public View createView(Context context) {
        if (mType == RecommendAdapter.TYPE_IMAGE_LIST) {
            return LayoutInflater.from(context).inflate(R.layout.jdme_layout_image_list_item, null);
        } else if (mType == RecommendAdapter.TYPE_BANNER) {
            return LayoutInflater.from(context).inflate(R.layout.jdme_layout_banner_item, null);
        } else {
            return LayoutInflater.from(context).inflate(R.layout.jdme_layout_banner_item, null);
        }
    }
}