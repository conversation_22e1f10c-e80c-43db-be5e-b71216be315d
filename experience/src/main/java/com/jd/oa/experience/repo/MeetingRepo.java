package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.MeetingData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class MeetingRepo {

    private static final String MEETING_CACHE_KEY = "exp.meeting.repo.cache.key";
    private static MeetingRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static MeetingRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new MeetingRepo(context);
        }
        return sInstance;
    }

    private MeetingRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public MeetingData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), MEETING_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        MeetingData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<MeetingData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(MeetingData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), MEETING_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getMeetingData(final LoadDataCallback<MeetingData> callback) {
        Log.i("zhn", "getMeetingData");
        HashMap<String, Object> params = new HashMap<>();
        params.put("teamId", PreferenceManager.UserInfo.getTeamId());
        HttpManager.post(null, new HashMap<String, String>(), params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                /*if (BuildConfig.DEBUG) {  //mock数据
//                    info.result = "{\"content\":{\"title\":\"会议效率\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insighthuiyixiaolv.png\",\"jumpText\":\"查看更多\",\"jumpUrl\":\"jdme://jm/biz/appcenter/202205131266\",\"tips\":\"根据个人线上数据分析，仅供本人查看，并将严格保密。建议供参考，请理性看待。\",\"date\":1652767865000,\"meetingEfficiency\":\"-1\",\"meetingCount\":61899.55,\"meetingCountLevel\":\"-1\",\"participantAvg\":24123.000,\"participantAvgLevel\":\"0\",\"durationAvg\":5123.999,\"durationAvgLevel\":\"1\",\"suggest\":[\"提前准备会议材料\",\"控制会议时长\",\"会议场次较多但效率不高\"]},\"errorMsg\":\"\",\"errorCode\":\"0\"}";
                    info.result = "{\"content\":{\"date\":1672675200000,\"durationAvgLevel\":\"\",\"icon\":\"https://storage.360buyimg.com/jd.jme.client/images/insighthuiyixiaolv.png\",\"jumpText\":\"查看更多\",\"jumpUrl\":\"jdme://jm/biz/appcenter/525723714895056896\",\"meetingCountLevel\":\"\",\"meetingEfficiency\":\"\",\"participantAvgLevel\":\"\",\"tips\":\"根据个人线上数据分析，仅供本人查看，并将严格保密。建议供参考，请理性看待。\",\"title\":\"会议效率\"},\"errorCode\":\"0\",\"errorMsg\":\"\"}";
                }*/
                ApiResponse<MeetingData> response = ApiResponse.parse(info.result, new TypeToken<MeetingData>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getMeetingEfficiency, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getMeetingEfficiency, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                //callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                callback.onDataLoaded(new MeetingData());
                MELogUtil.localV(MELogUtil.TAG_JIS, "getMeetingEfficiency, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getMeetingEfficiency");
//        }, "http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getMeetingEfficiency");
    }
}
