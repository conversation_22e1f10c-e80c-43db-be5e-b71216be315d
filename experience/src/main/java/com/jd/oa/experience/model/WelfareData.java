package com.jd.oa.experience.model;

import java.io.Serializable;
import java.util.List;

public class WelfareData implements Serializable {
    public Welfare welfare;
    public Account account;
    public Coupons coupons;
    public Holiday holiday;
    public Scores scores;

    public static class Welfare {
        public String title;
        public String subTitle;
        public String appId;
        public String url;
        public String count;
    }

    public static class Account {
        public String appId;
        public String url;
        public Integer total;
        public Integer walletBalance;
        public Integer yikatongBalance;//1分为单位，自己转成字符串
    }

    public static class Coupons {
        public String appId;
        public String url;
        public List<Coupon> list;
    }

    public static class Coupon {
        public String couponTypeName;
        public String couponTypeCode;
        public Integer value;
        public String unit;
    }

    public static class Holiday {
        public String detailAppId;
        public String detailPageUrl;
        public String applyAppId;
        public String applyPageUrl;
        public String surplus;
    }

    public static class Scores {
        public String appId;
        public String url;
        public String balance;
    }
}

/*
{
	"content": {
		"account": {
			"appId": "10038",
			"total": 49780,
			"url": "jdme://jm/biz/appcenter/wallet",
			"walletBalance": 295057,
			"yikatongBalance": 20223
		},
		"coupons": {
			"appId": "8",
			"list": [{
					"couponTypeCode": "00",
					"couponTypeName": "Dong",
					"unit": "yuan",
					"value": "355"
				},
				{
					"couponTypeCode": "10",
					"couponTypeName": "Express",
					"unit": "piece",
					"value": "16"
				},
				{
					"couponTypeCode": "5",
					"couponTypeName": "Travel",
					"unit": "piece",
					"value": "21"
				},
				{
					"couponTypeCode": "3",
					"couponTypeName": "Service",
					"unit": "piece",
					"value": "12"
				},
				{
					"couponTypeCode": "4",
					"couponTypeName": "Medical",
					"unit": "piece",
					"value": "45"
				}
			],
			"url": "jdme://jm/biz/appcenter/8"
		},
		"holiday": {
			"applyAppId": "",
			"applyPageUrl": "jdme://jm/biz/appcenter/process/vacation",
			"detailAppId": "apply",
			"detailPageUrl": "jdme://jm/biz/appcenter/holiday",
			"surplus": "120"
		},
		"scores": {
			"appId": "201806080253",
			"balance": 2780.2,
			"url": "jdme://jm/biz/appcenter/201806080253"
		},
		"welfare": {
			"title": "我的专属福利",
			"subTitle": "我是一个副标题",
			"appId": "202206081291",
			"count": "",
			"url": "jdme://jm/biz/appcenter/202206081291"
		}
	},
	"errorCode": "0",
	"errorMsg": ""
}*/