package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Deprecated
public class RecommendRepo {
    private static final String RECOMMEND_CACHE_KEY = "exp.recommend.repo.cache.key";
    private static RecommendRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static RecommendRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new RecommendRepo(context);
        }
        return sInstance;
    }

    private RecommendRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public List<RecommendData> getRecommendCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), RECOMMEND_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        List<RecommendData> data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<List<RecommendData>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(List<RecommendData> data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), RECOMMEND_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getRecommendData(final LoadDataCallback<List<RecommendData>> callback) {
        Log.i("zhn", "getRecommendData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<List<RecommendData>> response = ApiResponse.parse(info.result, new TypeToken<ArrayList<RecommendData>>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.getRecommendList");
//        }, "http://j-api.jd.com/mocker/zh/657046553/");
    }
}
