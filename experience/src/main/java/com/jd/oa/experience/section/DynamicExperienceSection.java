package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Point;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.JueCallback;
import com.jd.oa.dynamic.view.DynamicContainerLayout;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.ExperienceDynamicData;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.LocaleUtils;

import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class DynamicExperienceSection extends Section implements Destroyable {

    private boolean mIsFirst;
    private String jsonData = "{}";
    private final Context mContext;
    private final String mTabCode;
    private final SectionedRecyclerViewAdapter mAdapter;
    private final String mTemplateId;
    private ItemHolder mItemViewHolder;
    private boolean mDestroyed;
    private ExperienceDynamicData data = new ExperienceDynamicData();
    private final String TAG = "DynamicExperienceSection";

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        notifyCardLoadData();
                    }
                    break;
                case Constants.ACTION_REFRESH_EXP_FROM_TAB:
                    notifyCardLoadData();
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    screenSizeChange();
                    break;
                default:
                    break;
            }
        }
    };

    public DynamicExperienceSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first, String templateId) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_dynamic_section)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        mTemplateId = templateId;
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_FROM_TAB);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
        if(mItemViewHolder != null && mItemViewHolder.llContainer != null){
            MEDynamic.getInstance().removeInstance(mItemViewHolder.llContainer);
        }
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        final ItemHolder itemHolder = (ItemHolder) viewHolder;
        try {
            if (!TextUtils.isEmpty(mTemplateId) && MEDynamic.getInstance().existJue(mTemplateId)) {
                showCard(viewHolder.itemView.getContext(), mTemplateId, itemHolder.llContainer);
            } else { //卡片还未下载
                showCard(viewHolder.itemView.getContext(), MEDynamic.ERROR_CARD_ID, itemHolder.llContainer);
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "onBindItemViewHolder exception");
            showCard(viewHolder.itemView.getContext(), MEDynamic.ERROR_CARD_ID, itemHolder.llContainer);

        }
    }

    public void showCard(Context context, String cardId, ViewGroup container) {
        MEDynamic.getInstance().loadDynamicCard(context, cardId, container);
        showDetail();
        MELogUtil.localD(TAG, "show card " + cardId);
    }

    public void showDetail() {
        handleData();
        MELogUtil.localD(TAG, "showDetail");
        cardLoadData();
    }

    private void cardLoadData() {
        ExperienceDynamicData tmpData = JsonUtils.getGson().fromJson(jsonData, ExperienceDynamicData.class);
        if (tmpData.viewSize == null || tmpData.viewSize.width == 0) {
            getHander().postDelayed(new Runnable() {
                @Override
                public void run() {
                    showDetail();
                }
            }, 200);
            return;
        }
        MELogUtil.localD(TAG, " cardLoadData jsonData = " + jsonData);
        MEDynamic.getInstance().loadData(mItemViewHolder.llContainer, jsonData);
    }

    private void handleData() {
        if (mItemViewHolder == null || TextUtils.isEmpty(mTemplateId)) {
            return;
        }
        data.templateId = mTemplateId;
        if (mContext != null) {
            data.language = LocaleUtils.getUserSetLocaleStr(mContext);
            data.viewSize.width = DensityUtil.px2dp(mContext, (mItemViewHolder.llContainer.getLayoutParams().width == -1 || mItemViewHolder.llContainer.getLayoutParams().width == 0) ? mItemViewHolder.llContainer.getWidth() : mItemViewHolder.llContainer.getLayoutParams().width);
        }
        jsonData = JsonUtils.getGson().toJson(data);
        MELogUtil.localD(TAG, "handleData jsonData = " + jsonData);
    }



    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        return map.containsValue(this);
    }

    public void showLoading() {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                setState(State.LOADING);
                try {
                    mAdapter.notifyItemRangeChangedInSection(DynamicExperienceSection.this, 0, getContentItemsTotal());
                } catch (Exception e) {
                    MELogUtil.localE(TAG, "showLoading exception ", e);
                }
            }
        });
    }

    public void notifyCardLoadData() {
        if (mItemViewHolder == null || mItemViewHolder.llContainer == null) {
            return;
        }
        MEDynamic.getInstance().refreshData(mItemViewHolder.llContainer.hashCode(), new JueCallback() {
            @Override
            public void call(Object o) {
                MELogUtil.localD(TAG, "call js refreshData " + o);
            }
        });
    }

    private void changeState(final State state) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            setState(state);
            mAdapter.notifyItemRangeChangedInSection(this, 0, getContentItemsTotal());
        } else {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    setState(state);
                    try {
                        mAdapter.notifyItemRangeChangedInSection(DynamicExperienceSection.this, 0, getContentItemsTotal());
                    } catch (Exception e) {
                        MELogUtil.localE(TAG, "change state notifyItemRangeChangedInSection exception " + e.toString(), e);
                    }
                }
            });
        }
    }

    public synchronized void screenSizeChange() {
        if (mItemViewHolder == null || mItemViewHolder.llContainer == null) {
            return;
        }
        final int oldHeight = mItemViewHolder.llContainer.getHeight();
        final int oldWidth = mItemViewHolder.llContainer.getWidth();
        MELogUtil.localD(TAG, "screenSizeChange oldHeight = " + oldHeight + " oldWidth=" + oldWidth);
        MELogUtil.localD(TAG, "screenSizeChange getMeasuredHeight = " + mItemViewHolder.llContainer.getMeasuredHeight() + " getMeasuredWidth=" + mItemViewHolder.llContainer.getMeasuredWidth());
        if (mItemViewHolder.llContainer.getWidth() > 0) {
            resetWidth();
        } else {
            mItemViewHolder.llContainer.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    MELogUtil.localD(TAG, "screenSizeChange onGlobalLayout Height = " + mItemViewHolder.llContainer.getHeight() + " Width=" + mItemViewHolder.llContainer.getWidth());
                    if (mContext != null) {
                        data.viewSize.width = DensityUtil.px2dp(mContext, mItemViewHolder.llContainer.getWidth());
                    }
                    jsonData = JsonUtils.getGson().toJson(data);
                    cardLoadData();
                    notifyCardLoadData();
                    mItemViewHolder.llContainer.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                }
            });
        }
    }

    private void resetWidth() {
        WindowManager windowManager = (WindowManager) mItemViewHolder.llContainer.getContext().getSystemService(Context.WINDOW_SERVICE);
        Display defaultDisplay = windowManager.getDefaultDisplay();
        Point point = new Point();
        defaultDisplay.getSize(point);
        int x = point.x;
        int y = point.y;
        MELogUtil.localD(TAG, " screen width = " + x + ",screen height = " + y);

        if (mContext instanceof FragmentActivity) {
            final int newWidth = x - 2 * DensityUtil.dp2px(mContext, 8);
            MELogUtil.localD(TAG, " screen new width = " + newWidth);
            MELogUtil.localD(TAG, " getLayoutParams width = " + mItemViewHolder.llContainer.getLayoutParams().width);
            ((FragmentActivity) mContext).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mItemViewHolder.llContainer.getLayoutParams();
                    MELogUtil.localD(TAG, " llContainer.post params.getMarginStart = " + params.getMarginStart() + " params.getMarginEnd() = " + params.getMarginEnd());
                    params.width = newWidth;
                    params.setMargins(DensityUtil.dp2px(mContext, 8), 0, DensityUtil.dp2px(mContext, 8), DensityUtil.dp2px(mContext, 8));
                    mItemViewHolder.llContainer.setLayoutParams(params);
                    data.viewSize.width = DensityUtil.px2dp(mContext, newWidth);
                    jsonData = JsonUtils.getGson().toJson(mItemViewHolder);
                    MELogUtil.localD(TAG, " llContainer.post jsonData = " + jsonData);
                    cardLoadData();
                }
            });
        }

    }

    private class ItemHolder extends RecyclerView.ViewHolder {
        DynamicContainerLayout llContainer;

        public ItemHolder(@NonNull View itemView) {
            super(itemView);
            llContainer = itemView.findViewById(R.id.jdme_dynamic_container);
            if (llContainer == null) {
                return;
            }
            llContainer.putViewCallback(new DynamicContainerLayout.ViewCallback() {
                @Override
                public void call(final Map<String, Object> param) {
                    if (param == null || AppBase.getTopActivity() == null || AppBase.getTopActivity().isDestroyed()) {
                        return;
                    }
                    String action = param.get("action").toString();
                    if (TextUtils.isEmpty(action)) {
                        return;
                    }
                    switch (action) {
                        case "showLoading":
                            showLoading();
                            break;
                        case "dismissLoading":
                            changeState(State.LOADED);
                            break;
                        case "reload":
                            final String dId = param.get("dynamicId").toString();
                            if (TextUtils.isEmpty(dId)) {
                                return;
                            }
                            changeState(State.LOADED);
                            MEDynamic.getInstance().reLoadDynamicCard(mItemViewHolder.itemView.getContext(), dId, mItemViewHolder.llContainer);
                            showDetail();
                            break;
                        default:
                            break;
                    }
                }
            });
        }
    }

    private Handler mHandler;

    private Handler getHander() {
        if (mHandler == null) {
            mHandler = new Handler();
        }
        return mHandler;
    }
}
