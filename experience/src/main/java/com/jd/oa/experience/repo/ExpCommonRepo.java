package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.CommonData;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Deprecated
public class ExpCommonRepo {
    private static final String CACHE_KEY = "exp.repo.cache.key.common";
    private static ExpCommonRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static ExpCommonRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new ExpCommonRepo(context);
        }
        return sInstance;
    }

    private ExpCommonRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }


    public List<CommonData.Commons> getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        List<CommonData.Commons> data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<List<CommonData.Commons>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(List<CommonData.Commons> data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null, mGson.toJson(data));
    }


    public void getCommonData(final LoadDataStatusCallBack<List<CommonData.Commons>> callback) {
        Log.i("zhn", "getCommonData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String result = info.result;
//                String result = getRandomResult();
                ApiResponse<List<CommonData.Commons>> response = ApiResponse.parse(result, new TypeToken<ArrayList<CommonData.Commons>>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData(), getCache() == null || getCache().size() == 0 || (response.getData().size() != getCache().size()));
                    addCache(response.getData());
                } else {
                    callback.onDataNotAvailable(info.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.getCommonData");
//        }, "http://j-api.jd.com/mocker/zh/382963141/");
    }

    public String getRandomResult() {
        //1条
        String result1 = "{\"errorCode\":\"0\",\"content\":[{\"unit\":\"小时\",\"appId\":\"20191027058\",\"secret\":\"1\",\"title\":\"我的假期\",\"value\":168.5,\"url\":\"jdme://appcenter/20191027058\"}],\"errorMsg\":\"\"}";
        //2条
        String result2 = "{\"content\":[{\"appId\":\"201806080253\",\"secret\":\"1\",\"title\":\"福利积分\",\"unit\":\"积分\",\"url\":\"jdme://jm/biz/appcenter/201806080253\",\"value\":\"0\"},{\"appId\":\"10038\",\"secret\":\"1\",\"title\":\"我的钱包\",\"unit\":\"\",\"url\":\"jdme://jm/biz/appcenter/wallet\",\"value\":\"0\"}],\"errorCode\":\"0\",\"errorMsg\":\"\"}";
        //3条
        String result4 = "{\"content\":[{\"appId\":\"201806080253\",\"secret\":\"1\",\"title\":\"福利积分\",\"unit\":\"积分\",\"url\":\"jdme://jm/biz/appcenter/201806080253\",\"value\":\"0\"},{\"appId\":\"10038\",\"secret\":\"1\",\"title\":\"我的钱包\",\"unit\":\"\",\"url\":\"jdme://jm/biz/appcenter/wallet\",\"value\":\"0\"},{\"appId\":\"10038\",\"secret\":\"1\",\"title\":\"我的钱包\",\"unit\":\"\",\"url\":\"jdme://jm/biz/appcenter/wallet\",\"value\":\"0\"}],\"errorCode\":\"0\",\"errorMsg\":\"\"}";
        //多条
        String result3 = "{\"content\":[{\"appId\":\"holiday\",\"secret\":\"0\",\"title\":\"我的假期\",\"unit\":\"小时\",\"url\":\"jdme://jm/biz/appcenter/holiday\",\"value\":\"0.5\"},{\"appId\":\"201806080253\",\"secret\":\"0\",\"title\":\"福利积分福利积分福利积分福利积分\",\"unit\":\"积分\",\"url\":\"jdme://jm/biz/appcenter/201806080253\",\"value\":\"0.0.0.0\"},{\"appId\":\"1003800\",\"secret\":\"1\",\"title\":\"我的钱包\",\"unit\":\"\",\"url\":\"jdme://jm/biz/appcenter/wallet\",\"value\":\"0.\"},{\"appId\":\"1003801\",\"secret\":\"1\",\"title\":\"我的钱包我的钱包\",\"unit\":\"\",\"url\":\"jdme://jm/biz/appcenter/wallet\",\"value\":\"99,999.123\"},{\"appId\":\"1003802\",\"secret\":\"1\",\"title\":\"我的钱包我的钱包我的钱包我的钱包我的钱包我的钱包\",\"unit\":\"\",\"url\":\"jdme://jm/biz/appcenter/wallet\",\"value\":\"99,999,999,999.9999\"}],\"errorCode\":\"0\",\"errorMsg\":\"\"}";
        String[] results = new String[4];
        results[0] = result1;
        results[1] = result2;
        results[2] = result3;
        results[3] = result4;
        return results[(int) (System.currentTimeMillis() / 1000 % 4)];
    }
}
