package com.jd.oa.experience.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;

/*体验平台所有二级内嵌rv都要使用这个类*/
public class ChronicleRecyclerView extends RecyclerView {
    private boolean mFadingEdgeLeft = false;
    private boolean mFadingEdgeTop = false;
    private boolean mFadingEdgeRight = false;
    private boolean mFadingEdgeBottom = false;
    private int mFadingEdgeLength = 0;

    private int mStartX = 0;
    private int mStartY = 0;

    public ChronicleRecyclerView(@NonNull Context context) {
        super(context);
        init(context, null);
    }

    public ChronicleRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public ChronicleRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        setNestedScrollingEnabled(false);//必须这样设置，否则会导致AppBarLayout问题，按住内嵌rv区域上滑，外部rv滑到CollapsingLayout下边

        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.ChronicleRecyclerView);
            mFadingEdgeLeft = a.getBoolean(R.styleable.ChronicleRecyclerView_fadingEdgeLeft, false);
            mFadingEdgeTop = a.getBoolean(R.styleable.ChronicleRecyclerView_fadingEdgeTop, false);
            mFadingEdgeRight = a.getBoolean(R.styleable.ChronicleRecyclerView_fadingEdgeRight, false);
            mFadingEdgeBottom = a.getBoolean(R.styleable.ChronicleRecyclerView_fadingEdgeBottom, false);
            mFadingEdgeLength = a.getDimensionPixelSize(R.styleable.ChronicleRecyclerView_fadingEdgeLength,
                    getVerticalFadingEdgeLength());
            a.recycle();
        }

        // Enable fading edge if any edge is set to fade
        if (mFadingEdgeLeft || mFadingEdgeRight) {
            setHorizontalFadingEdgeEnabled(true);
        }
        if (mFadingEdgeTop || mFadingEdgeBottom) {
            setVerticalFadingEdgeEnabled(true);
        }
    }

    @Override
    protected float getLeftFadingEdgeStrength() {
        return mFadingEdgeLeft ? 1.0f : 0.0f;
    }

    @Override
    protected float getRightFadingEdgeStrength() {
        return mFadingEdgeRight ? 1.0f : 0.0f;
    }

    @Override
    protected float getTopFadingEdgeStrength() {
        return mFadingEdgeTop ? 1.0f : 0.0f;
    }

    @Override
    protected float getBottomFadingEdgeStrength() {
        return mFadingEdgeBottom ? 1.0f : 0.0f;
    }

    @Override
    public int getHorizontalFadingEdgeLength() {
        return mFadingEdgeLength;
    }

    @Override
    public int getVerticalFadingEdgeLength() {
        return mFadingEdgeLength;
    }

    /*
    当ViewPager2设置mViewPager.setUserInputEnabled(false)，禁止左右滑动切换tab页，产品希望能够支持滑动切换
    当ViewPager2不禁止滑动切换，viewpager会拦截二级rv的滑动事件，导致二级rv无法横向滚动
    可以通过对二级rv设置setNestedScrollingEnabled(true)解决，但这样处理会导致collapsedToolbar与二级rv出现冲突，
    即按住内嵌rv区域上滑，外部rv滑到CollapsingLayout下边，所有二级rv必须设置setNestedScrollingEnabled(false)，
    需要重写dispatchTouchEvent防止viewpager接管横滑事件
    * */
    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mStartX = (int)ev.getX();
                mStartY = (int)ev.getY();
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_MOVE:
                int endX = (int) ev.getX();
                int endY = (int) ev.getY();
                int disX = Math.abs(endX - mStartX);
                int disY = Math.abs(endY - mStartY);
                if (disX > disY) {//2 * disX > disY
                    getParent().requestDisallowInterceptTouchEvent(true);
                } else {
                    getParent().requestDisallowInterceptTouchEvent(3 * disX >= disY);
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            default:
                break;
        }
        return super.dispatchTouchEvent(ev);
    }
}
