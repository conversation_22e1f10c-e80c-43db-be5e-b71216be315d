package com.jd.oa.experience.repo;

import com.jd.oa.experience.model.UserInfoData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;
import java.util.Map;

public class UserInfoRepo {
    private static UserInfoRepo sInstance;

    public static UserInfoRepo get() {
        if (sInstance == null) {
            sInstance = new UserInfoRepo();
        }
        return sInstance;
    }

    private UserInfoRepo() {
    }

    public void getUserInfoData(final LoadDataCallback<UserInfoData> callback) {
        Map<String, Object> params = new HashMap<>();
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<UserInfoData > response = ApiResponse.parse(info.result, UserInfoData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.getUserInfo");
    }
}
