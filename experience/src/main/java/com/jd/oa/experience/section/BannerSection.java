package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.chenenyu.router.Router;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.BannerData;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.repo.BannerRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.PagerSnapAdapter;
import com.jd.oa.experience.view.PagerSnapRecyclerView;
import com.jd.oa.experience.view.TitleView2;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.ScreenUtil;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class BannerSection extends StatelessSection implements Destroyable {

    private boolean mIsFirst;
    private final Context mContext;
    private final String mTabCode;
    private final SectionedRecyclerViewAdapter mAdapter;
    private BannerSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final BannerRepo repo;
    private BannerData mData;
    private final boolean mBannerSectionV3;

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_REFRESH_EXP_FROM_TAB:
                    loadData();
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                default:
                    break;
            }
        }
    };

    public BannerSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first, boolean isV3) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_banner)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        mBannerSectionV3 = isV3;
        repo = BannerRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_FROM_TAB);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        BannerData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getBanner(new LoadDataCallback<BannerData>() {
            @Override
            public void onDataLoaded(BannerData data) {
                showData(data);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(BannerData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new BannerSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        return map.containsValue(this);
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        // 无内容不展示
        if (mData.bannerList == null || mData.bannerList.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        int defaultMargin = DensityUtil.dp2px(mContext, 4);
        if (mBannerSectionV3) {
            //新版本间距用8dp
            defaultMargin = DensityUtil.dp2px(mContext, 8);
        }
        lp.setMargins(defaultMargin, 0, defaultMargin, defaultMargin);
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        // 头部图片
        boolean hasRightImg = !TextUtils.isEmpty(mData.illustration);
//        LinearLayout.LayoutParams subLayoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        LinearLayout.LayoutParams subLayoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, mContext.getResources().getDimensionPixelSize(R.dimen.exp_banner_subtitle_height));
        subLayoutParams.setMarginStart(mContext.getResources().getDimensionPixelSize(R.dimen.exp_banner_margin));
        subLayoutParams.setMargins(0, -1, 0, 0);
        if (hasRightImg) {
            subLayoutParams.setMarginEnd(CommonUtils.dp2px(90));
            ImageLoader.load(mContext, mItemViewHolder.mTitleImg, mData.illustration, false, R.drawable.jdme_mine_load_img_error, R.drawable.jdme_mine_load_img_error);
        } else {
            subLayoutParams.setMarginEnd(mContext.getResources().getDimensionPixelSize(R.dimen.exp_banner_margin));
            mItemViewHolder.mTitleImg.setImageDrawable(null);
        }
        mItemViewHolder.mSubTitleLayout.setLayoutParams(subLayoutParams);

        mItemViewHolder.mTitleView.setTitle(hasRightImg, mData.icon, mData.title, mData.jumpUrl, "", 90);
        //新版本中隐藏icon,并设置背景为圆角不带边框
        if (mBannerSectionV3) {
            mItemViewHolder.mTitleView.setVisibility(View.GONE);
            mItemViewHolder.mTitleV3Container.setVisibility(View.VISIBLE);
            mItemViewHolder.mBannerContainer.setBackgroundResource(R.drawable.jdme_all_round_8);
            mItemViewHolder.mTitleViewV3.setText(mData.title);
        } else {
            mItemViewHolder.mTitleView.setVisibility(View.VISIBLE);
            mItemViewHolder.mTitleV3Container.setVisibility(View.GONE);
            mItemViewHolder.mBannerContainer.setBackgroundResource(R.drawable.jdme_bg_shadow_corner);
        }
        mItemViewHolder.mTitleView.mTitleTv.setTextColor(mData.getTextColorInt());
        mItemViewHolder.mTitleView.mGoBtn.setTextColor(mData.getTextColorInt());
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                    HashMap<String, String> values = new HashMap<>();
                    values.put("timestamp", "" + System.currentTimeMillis());
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_BANNER_DETAIL, values);
                }
            }
        });

        // 副标题
        if (!TextUtils.isEmpty(mData.dynamicContent)) {
            mItemViewHolder.mSubTitleLayout.setVisibility(View.VISIBLE);
            mItemViewHolder.mSubTitleTv.setText(mData.dynamicContent);
            mItemViewHolder.mSubTitleTv.setTextColor(mData.getTextColorInt());
            if (!TextUtils.isEmpty(mData.dynamicImg)) {
                mItemViewHolder.mSubPic.setVisibility(View.VISIBLE);
                ImageLoader.load(mContext, mItemViewHolder.mSubPic, mData.dynamicImg);
            } else {
                mItemViewHolder.mSubPic.setVisibility(View.GONE);
            }
        } else {
            mItemViewHolder.mSubTitleLayout.setVisibility(View.GONE);
        }

        if (mData.isDarkModel()) {
            mItemViewHolder.mServiceRv.setIndicatorLight();
        } else {
            mItemViewHolder.mServiceRv.setIndicatorDark();
        }
        mItemViewHolder.mServiceRv.setIndicatorHeight(mContext.getResources().getDimensionPixelSize(R.dimen.exp_banner_indicator_height));

        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mItemViewHolder.mServiceRv.getLayoutParams();
        params.setMargins(params.getMarginStart(), CommonUtils.dp2px(13) - 1, params.getMarginEnd(), 0);
        mItemViewHolder.mServiceRv.setAdapter(new PagerSnapAdapter<BannerData.BannerList>(
                mContext, mData.bannerList, PagerSnapAdapter.VIEW_TYPE_BANNER,
                R.layout.jdme_item_experience_section_banner_item) {
            @Override
            public void onBindView(View view, final BannerData.BannerList data) {
                BannerImageView singleSiv = view.findViewById(R.id.banner_item_srv_line_0);
                LinearLayout multipleLl = view.findViewById(R.id.banner_item_ll_line_1);
                BannerImageView multipleSivFirst = view.findViewById(R.id.banner_item_srv_line_1_0);
                BannerImageView multipleSivSecond = view.findViewById(R.id.banner_item_srv_line_1_1);
                BannerTextGroup bannerTextGroup = view.findViewById(R.id.mBannerTextGroup);
                bannerTextGroup.setVisibility(View.GONE);
                if (data != null && data.banners.size() == 1) {
                    singleSiv.setVisibility(View.VISIBLE);
                    multipleLl.setVisibility(View.GONE);
                    String imgUrl = data.banners.get(0).img;
                    final String link = data.banners.get(0).jumpUrl;
                    singleSiv.bindWithData(imgUrl, data.banners.get(0).text);
//                    ImageLoader.load(mContext, singleSiv, imgUrl, R.drawable.jdme_self_info_image_default);
                    singleSiv.setOnClickListener(new NoDoubleClickListener() {
                        @Override
                        protected void onNoDoubleClick(View v) {
                            if (!TextUtils.isEmpty(link)) {
                                Router.build(link).go(mContext, new RouteNotFoundCallback(mContext));
                                HashMap<String, String> values = new HashMap<>();
                                values.put("timestamp", "" + System.currentTimeMillis());
                                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_BANNER_ITEM_CLICK, values);
                            }
                        }
                    });
                } else if (data.banners != null && data.banners.size() >= 2) {
                    singleSiv.setVisibility(View.GONE);
                    multipleLl.setVisibility(View.VISIBLE);
                    BannerData.Banner first = data.banners.get(0);
                    multipleSivFirst.bindWithData(first.img, first.text);
                    BannerData.Banner second = data.banners.get(1);
                    multipleSivSecond.bindWithData(second.img, second.text);
//
//                    ImageLoader.load(mContext, multipleSivFirst, data.banners.get(0).img, R.drawable.jdme_self_info_image_default);
//                    ImageLoader.load(mContext, multipleSivSecond, data.banners.get(1).img, R.drawable.jdme_self_info_image_default);

                    multipleSivFirst.setOnClickListener(new NoDoubleClickListener() {
                        @Override
                        protected void onNoDoubleClick(View v) {
                            if (!TextUtils.isEmpty(data.banners.get(0).jumpUrl)) {
                                Router.build(data.banners.get(0).jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                                HashMap<String, String> values = new HashMap<>();
                                values.put("timestamp", "" + System.currentTimeMillis());
                                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_BANNER_ITEM_CLICK, values);
                            }
                        }
                    });

                    multipleSivSecond.setOnClickListener(new NoDoubleClickListener() {
                        @Override
                        protected void onNoDoubleClick(View v) {
                            if (!TextUtils.isEmpty(data.banners.get(1).jumpUrl)) {
                                Router.build(data.banners.get(1).jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                                HashMap<String, String> values = new HashMap<>();
                                values.put("timestamp", "" + System.currentTimeMillis());
                                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_BANNER_ITEM_CLICK, values);
                            }
                        }
                    });
                } else if (data.texts != null && !data.texts.isEmpty()) {
                    bannerTextGroup.setVisibility(View.VISIBLE);
                    bannerTextGroup.bindText(data.texts);
                }
            }

        });
        // 设置背景
        setBannerBg();

        setViewMatchHeight(mItemViewHolder.mContentContainer);
        setViewMatchHeight(mItemViewHolder.mSkinLayout);
    }

    private void setBannerBg() {
        BannerSection.ItemViewHolder viewHolder = mItemViewHolder;
        if (viewHolder == null) {
            return;
        }
        //背景色
        boolean useDefault = true;
        final int[] colors = {0xffffffff, 0xffffffff};
        if (mData != null && mData.bgImg != null &&
                !TextUtils.isEmpty(mData.bgImg.fromColor) &&
                !TextUtils.isEmpty(mData.bgImg.endColor)) {
            try {
                int top = ColorUtil.parseColor(mData.bgImg.fromColor, 0xffffffff);
                int bottom = ColorUtil.parseColor(mData.bgImg.endColor, 0xfffafafa);
                colors[0] = top;
                colors[1] = bottom;
                useDefault = false;
                BannerData.BannerBg bgImage = mData == null ? null : mData.bgImg;
                if (bgImage != null) {
                    if (!TextUtils.isEmpty(bgImage.leftImg)) {
                        loadThemeBgImage(bgImage.leftImg, viewHolder.mThemeLeftIv, true);
                    } else {
                        Glide.with(mContext).load("").into(viewHolder.mThemeLeftIv);
                    }
                    if (!TextUtils.isEmpty(bgImage.rightImg)) {
                        loadThemeBgImage(bgImage.rightImg, viewHolder.mThemeRightIv, false);
                    } else {
                        Glide.with(mContext).load("").into(viewHolder.mThemeRightIv);
                    }
                    if (!TextUtils.isEmpty(bgImage.img)) {
                        Glide.with(mContext).load(bgImage.img).into(viewHolder.mThemeCenterIv);
                    } else {
                        Glide.with(mContext).load("").into(viewHolder.mThemeCenterIv);
                    }
                } else {
                    Glide.with(mContext).load("").into(viewHolder.mThemeLeftIv);
                    Glide.with(mContext).load("").into(viewHolder.mThemeRightIv);
                    Glide.with(mContext).load("").into(viewHolder.mThemeCenterIv);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (useDefault) {
            Glide.with(mContext).load("").into(viewHolder.mThemeLeftIv);
            Glide.with(mContext).load("").into(viewHolder.mThemeRightIv);
            Glide.with(mContext).load("").into(viewHolder.mThemeCenterIv);
        }
        GradientDrawable bkGndDrawable = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors);
        bkGndDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        bkGndDrawable.setCornerRadius(CommonUtils.dp2px(6));
        bkGndDrawable.setGradientCenter(0.0f, 0.65f);
        viewHolder.mSkinLayout.setBackground(bkGndDrawable);
    }

    private void setViewMatchHeight(View view) {
        ViewGroup.LayoutParams params1 = view.getLayoutParams();
        params1.height = getContentHeight();
        view.setLayoutParams(params1);
    }

    private int getContentHeight() {
        if (!TextUtils.isEmpty(mData.dynamicContent)) {
            return mContext.getResources().getDimensionPixelSize(R.dimen.exp_banner_max_height);
//            return CommonUtils.dp2px(mContext, 223);
        } else {
            return mContext.getResources().getDimensionPixelSize(R.dimen.exp_banner_max_height) - mContext.getResources().getDimensionPixelSize(R.dimen.exp_banner_subtitle_height);
        }
    }

    private void loadThemeBgImage(String url, final ImageView imageView, final boolean left) {
        Glide.with(mContext).load(url).into(new SimpleTarget<Drawable>() {
            @Override
            public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                if (resource instanceof BitmapDrawable) {
                    Bitmap src = ((BitmapDrawable) resource).getBitmap();
                    if (src != null) {
                        int dstHeight = getContentHeight();
                        int dstWidth = ScreenUtil.getScreenWidth(mContext);
                        //图片过长
                        int maxWidth = dstWidth * src.getHeight() / dstHeight;
                        if (maxWidth < src.getWidth()) {
                            int x = left ? 0 : (src.getWidth() - maxWidth);
                            Bitmap dst = Bitmap.createBitmap(src, x, 0, maxWidth, src.getHeight());
                            imageView.setImageBitmap(dst);//src不能recycle，否则glide会异常，src是glide缓存图片
                        } else {
                            imageView.setImageDrawable(resource);
                        }
                    } else {
                        imageView.setImageDrawable(resource);
                    }
                } else {
                    imageView.setImageDrawable(resource);
                }
            }
        });
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public FrameLayout mBannerContainer;
        public ImageView mTitleImg;
        public TitleView2 mTitleView;
        public LinearLayout mTitleV3Container;
        public TextView mTitleViewV3;
        public View mSubTitleLayout;
        public ImageView mSubPic;
        public TextView mSubTitleTv;
        public PagerSnapRecyclerView<BannerData.BannerList> mServiceRv;
        private final View mSkinLayout;
        private final ImageView mThemeLeftIv;
        private final ImageView mThemeRightIv;
        private final ImageView mThemeCenterIv;
        public View mContentContainer;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mBannerContainer = itemView.findViewById(R.id.banner_container);
            mTitleV3Container = itemView.findViewById(R.id.title_container_v3);
            mTitleViewV3 = itemView.findViewById(R.id.title_text_v3);
            mContentContainer = itemView.findViewById(R.id.content_container);
            mTitleImg = itemView.findViewById(R.id.title_pic_img);
            mTitleView = itemView.findViewById(R.id.title_view);
            mSubTitleLayout = itemView.findViewById(R.id.subtitle_layout);
            mSubPic = itemView.findViewById(R.id.subpic_img);
            mSubTitleTv = itemView.findViewById(R.id.subtitle_tv);
            mServiceRv = itemView.findViewById(R.id.banner_rv);
            mSkinLayout = itemView.findViewById(R.id.skin_bkgnd_layout);
            mThemeLeftIv = itemView.findViewById(R.id.iv_theme_left);
            mThemeRightIv = itemView.findViewById(R.id.iv_theme_right);
            mThemeCenterIv = itemView.findViewById(R.id.iv_theme_center);
        }
    }
}
