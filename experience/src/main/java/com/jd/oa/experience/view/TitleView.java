package com.jd.oa.experience.view;

import android.content.Context;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.experience.R;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.ScreenUtil;

public class TitleView extends FrameLayout {

    private Context mContext;

    public TextView mTitleTv;
    private ImageView mIconIv;
    public TextView mGoBtn;
    private TextView mMoreTv;
    private TextView mCountTv;
    private TextView mUnitTv;

    private int MARGIN_RIGHT = 70;
    
    public TitleView(@NonNull Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public TitleView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    public TitleView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }
    
    private void initView() {
        View parent = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_experience_section_title_layout, this);
        mTitleTv = parent.findViewById(R.id.title);
        mIconIv = parent.findViewById(R.id.icon);
        mGoBtn = parent.findViewById(R.id.go_btn);
        mMoreTv = parent.findViewById(R.id.more);
        mCountTv = parent.findViewById(R.id.count);
        mUnitTv = parent.findViewById(R.id.unit);
    }

    public void setTitle(boolean hasRightImage, String iconUrl, String title, final String jumpUrl, String jumpText,int marginRight) {
        this.MARGIN_RIGHT = marginRight;
        setTitle(hasRightImage, iconUrl, title, jumpUrl, jumpText);
    }

    public void setTitle(boolean hasRightImage, String iconUrl, String title, final String jumpUrl, String jumpText) {
        if (!TextUtils.isEmpty(iconUrl)) {
            ImageLoader.load(mContext, mIconIv, iconUrl, false, R.drawable.jdme_bg_section_title_default_icon, R.drawable.jdme_bg_section_title_default_icon);
        } else {
            mIconIv.setImageResource(R.drawable.jdme_bg_section_title_default_icon);
        }

        mGoBtn.setVisibility(!TextUtils.isEmpty(jumpUrl) ? View.VISIBLE : View.GONE);
        mMoreTv.setVisibility(!TextUtils.isEmpty(jumpUrl) && !TextUtils.isEmpty(jumpText) ? View.VISIBLE : View.GONE);
        mMoreTv.setText(jumpText != null ? jumpText : "");
        mCountTv.setVisibility(View.GONE);
        mUnitTv.setVisibility(View.GONE);

        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) mTitleTv.getLayoutParams();
        if (mMoreTv.getVisibility() == View.VISIBLE) {
            lp.weight = 1;
            lp.width = 0;
        } else {
            lp.weight = 0;
            lp.width = LinearLayout.LayoutParams.WRAP_CONTENT;
        }
        mTitleTv.setLayoutParams(lp);

        if (mMoreTv.getVisibility() != View.VISIBLE) {
            int maxWidth = ScreenUtil.getScreenWidth(mContext) - DensityUtil.dp2px(
                    mContext, 52 + (hasRightImage ? MARGIN_RIGHT : 0) + 8 + 28);
            mTitleTv.setMaxWidth(maxWidth);
        }

        if (!TextUtils.isEmpty(title)) {
            mTitleTv.setText(title);
        }
    }

    public void setTitleWithCount(boolean hasRightImage, String iconUrl, String title, final String jumpUrl, String jumpText, int count) {
        if (!TextUtils.isEmpty(iconUrl)) {
            ImageLoader.load(mContext, mIconIv, iconUrl, false, R.drawable.jdme_bg_section_title_default_icon,R.drawable.jdme_bg_section_title_default_icon);
        } else {
            mIconIv.setImageResource(R.drawable.jdme_bg_section_title_default_icon);
        }

        mGoBtn.setVisibility(!TextUtils.isEmpty(jumpUrl) ? View.VISIBLE : View.GONE);
        mMoreTv.setVisibility(!TextUtils.isEmpty(jumpUrl) && !TextUtils.isEmpty(jumpText) ? View.VISIBLE : View.GONE);
        mMoreTv.setText(jumpText != null ? jumpText : "");
        if (count > 0) {
            mCountTv.setVisibility(View.VISIBLE);
            mCountTv.setText(String.valueOf(count));
            mUnitTv.setVisibility(View.VISIBLE);
        } else {
            mCountTv.setVisibility(View.GONE);
            mUnitTv.setVisibility(View.GONE);
        }

        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) mUnitTv.getLayoutParams();
        if (mMoreTv.getVisibility() == View.VISIBLE) {
            lp.weight = 1;
            lp.width = 0;
        } else {
            lp.weight = 0;
            lp.width = LinearLayout.LayoutParams.WRAP_CONTENT;
        }
        mUnitTv.setLayoutParams(lp);


        if (mMoreTv.getVisibility() != View.VISIBLE) {
            TextPaint paint = mCountTv.getPaint();
            int maxWidth = ScreenUtil.getScreenWidth(mContext)
                    - DensityUtil.dp2px(mContext, 52 + (hasRightImage ? MARGIN_RIGHT : 0) + 8 + 28 + 26)
                    - (int) paint.measureText(mCountTv.getText().toString());
            mTitleTv.setMaxWidth(maxWidth);
        }

        if (!TextUtils.isEmpty(title)) {
            mTitleTv.setText(title);
        }
    }
}
