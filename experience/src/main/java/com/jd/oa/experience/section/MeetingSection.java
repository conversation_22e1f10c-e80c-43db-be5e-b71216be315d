package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.dialog.ExpCnofirmDialog;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.MeetingData;
import com.jd.oa.experience.repo.MeetingRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.util.NumUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.DateUtils;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class MeetingSection extends StatelessSection implements Destroyable {

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private MeetingSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private MeetingRepo repo;
    private MeetingData mData;

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                default:
                    break;
            }
        }
    };

    public MeetingSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_worktime_meeting)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = MeetingRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        MeetingData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getMeetingData(new LoadDataCallback<MeetingData>() {
            @Override
            public void onDataLoaded(MeetingData meetingData) {
                showData(meetingData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(MeetingData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new MeetingSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null || mContext == null) {
            return;
        }

        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

//        mItemViewHolder.mTvDate.setText(DateUtils.getFetureDate(-1));
        String dateText = DateUtils.getFormatString(mData.getDate(), "yyyy/MM/dd");
        mItemViewHolder.mTvDate.setText(dateText);
        mItemViewHolder.mTvDate.setVisibility(mData.getDate() == 0L ? View.GONE : View.VISIBLE);

        boolean isEmptyData = mData.getMeetingCount() == -1 && mData.getParticipantAvg() == -1 && mData.getDurationAvg() == -1;
        mItemViewHolder.mLlEmptyView.setVisibility(isEmptyData ? View.VISIBLE : View.GONE);
        mItemViewHolder.mLlMeetingView.setVisibility(isEmptyData ? View.GONE : View.VISIBLE);
        mItemViewHolder.mTvEmpty.setText(mContext.getString(R.string.exp_work_time_meeting_empty, dateText));

        //title布局  start
        if (!TextUtils.isEmpty(mData.getTips())) {
            mItemViewHolder.mIvTip.setVisibility(View.VISIBLE);
            mItemViewHolder.mIvTip.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showTipsDialog();
                }
            });
        } else {
            mItemViewHolder.mIvTip.setVisibility(View.GONE);
        }
        mItemViewHolder.mMeetingMore.setVisibility(TextUtils.isEmpty(mData.getJumpText()) || TextUtils.isEmpty(mData.getJumpUrl()) ? View.GONE : View.VISIBLE);
        mItemViewHolder.mIvMore.setVisibility(TextUtils.isEmpty(mData.getJumpUrl()) ? View.GONE : View.VISIBLE);
        if (!TextUtils.isEmpty(mData.getJumpUrl())) {
            NoDoubleClickListener onMoreClick = new NoDoubleClickListener() {
                @Override
                protected void onNoDoubleClick(View v) {
                    if (mContext != null && !TextUtils.isEmpty(mData.getJumpUrl())) {
                        Router.build(mData.getJumpUrl()).go(mContext);
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_MEETING_EFFICIENCY, new HashMap<String, String>());
                    }
                }
            };
            mItemViewHolder.mLlMeetingTitle.setOnClickListener(onMoreClick);
        }
        mItemViewHolder.mTitleTv.setText(TextUtils.isEmpty(mData.getTitle()) ? mContext.getString(R.string.exp_work_time_meeting_title) : mData.getTitle());
        mItemViewHolder.mMeetingMore.setText(TextUtils.isEmpty(mData.getJumpText()) ? mContext.getString(R.string.exp_work_time_more) : mData.getJumpText());
        if (!TextUtils.isEmpty(mData.getIcon())) {
            Glide.with(mContext).load(mData.getIcon()).error(R.drawable.jdme_exp_worktime_icon_meeting).into(mItemViewHolder.mIvIcon);
        }
        //title布局  end

        if (isEmptyData) {
            return;
        }

        mItemViewHolder.mMeetingCount.setText(getShowNum(mData.getMeetingCount(), true));
        mItemViewHolder.mMeetingParticipant.setText(getShowNum(mData.getParticipantAvg(), false));
        mItemViewHolder.mMeetingDuration.setText(getShowNum(mData.getDurationAvg(), false));

        setTextByStatus(mData.getParticipantAvgLevel(), mItemViewHolder.mStatusMeetingParticipant);
        setTextByStatus(mData.getDurationAvgLevel(), mItemViewHolder.mStatusMeetingDuration);
        setTextByStatus(mData.getMeetingCountLevel(), mItemViewHolder.mStatusMeetingCount);

        if (mData.getSuggest() != null && mData.getSuggest().size() > 0) {
            StringBuilder suggests = new StringBuilder("");
            mItemViewHolder.vMeetingSuggestTop.setVisibility(View.VISIBLE);
            mItemViewHolder.mLlMeetingSuggest.setVisibility(View.VISIBLE);
            for (int i = 0; i < mData.getSuggest().size(); i++) {
                if (i != mData.getSuggest().size() - 1) {
                    suggests.append("·").append(mData.getSuggest().get(i)).append("\n");
                } else {
                    suggests.append("·").append(mData.getSuggest().get(i));
                }
            }
            mItemViewHolder.mMeetingSuggest.setText(suggests.toString());
        } else {
            mItemViewHolder.vMeetingSuggestTop.setVisibility(View.GONE);
            mItemViewHolder.mLlMeetingSuggest.setVisibility(View.GONE);
        }


        setEfficiencyByStatus(mData.getMeetingEfficiency());
    }


    public void setTextByStatus(String level, TextView textView) {
        if (TextUtils.isEmpty(level) && textView != null) {
            textView.setBackgroundResource(R.color.transparent);
            textView.setText("");
            return;
        }
        if (mContext == null || textView == null) {
            return;
        }
        switch (level) {
            case "偏多":
            case "偏长":
            case "-1":
                textView.setBackgroundResource(R.drawable.jdme_bg_exp_meeting_bg_not_good);
                textView.setTextColor(Color.parseColor("#FFFE3E33"));
                textView.setText(mContext.getString(R.string.exp_meeting_status_not_good));
                break;
            case "好":
            case "优秀":
            case "1":
                textView.setBackgroundResource(R.drawable.jdme_bg_exp_meeting_bg_good);
                textView.setTextColor(Color.parseColor("#FF29CC31"));
                textView.setText(mContext.getString(R.string.exp_meeting_status_good));
                break;
            case "正常":
            case "0":
                textView.setBackgroundResource(R.drawable.jdme_bg_exp_meeting_bg_normal);
                textView.setTextColor(Color.parseColor("#FF4C7CFF"));
                textView.setText(mContext.getString(R.string.exp_meeting_status_normal));
                break;
        }
    }


    /**
     * 大于1000显示K
     *
     * @return
     */
    public String getShowNum(double num, boolean isInt) {
        if (num == -1) {
            return "-";
        }
        if (num >= 1000) {
            return //isInt ? (int) (num / 1000) + "K" :
                    NumUtil.formatTo0(num / 1000) + "k";
        } else {
            return isInt ? String.valueOf((int) num) : NumUtil.formatTo0(num);
        }
    }


    public void setEfficiencyByStatus(String status) {
        Typeface jdRegular = Typeface.createFromAsset(mContext.getAssets(), "fonts/JDLangZhengTi_Regular.TTF");
        mItemViewHolder.mMeetingEfficiencyTitle.setTypeface(jdRegular);
        mItemViewHolder.mMeetingEfficiencyContent.setTypeface(jdRegular);
        switch (status) {
            case "-1":
                mItemViewHolder.mRlMeetingEfficiency.setBackgroundResource(R.drawable.jdme_bg_exp_meeting_efficiency_not_good);
                mItemViewHolder.mMeetingEfficiencyTitle.setText(mContext.getString(R.string.exp_meeting_efficiency_title_not_good));
                mItemViewHolder.mMeetingEfficiencyTitle.setTextColor(Color.parseColor("#FFFE3E33"));
                break;
            case "1":
                mItemViewHolder.mRlMeetingEfficiency.setBackgroundResource(R.drawable.jdme_bg_exp_meeting_efficiency_good);
                mItemViewHolder.mMeetingEfficiencyTitle.setText(mContext.getString(R.string.exp_meeting_efficiency_title_good));
                mItemViewHolder.mMeetingEfficiencyTitle.setTextColor(Color.parseColor("#FF14B41B"));
                break;
            case "0":
                mItemViewHolder.mRlMeetingEfficiency.setBackgroundResource(R.drawable.jdme_bg_exp_meeting_efficiency_normal);
                mItemViewHolder.mMeetingEfficiencyTitle.setText(mContext.getString(R.string.exp_meeting_efficiency_title_normal));
                mItemViewHolder.mMeetingEfficiencyTitle.setTextColor(Color.parseColor("#FF4C7CFF"));
                break;
            default:
                mItemViewHolder.mRlMeetingEfficiency.setBackgroundResource(R.drawable.jdme_bg_exp_worktime_bg);
                mItemViewHolder.mMeetingEfficiencyTitle.setText(mContext.getString(R.string.exp_meeting_efficiency_title_default));
                mItemViewHolder.mMeetingEfficiencyTitle.setTextColor(Color.parseColor("#FF333333"));
                break;
        }
    }

    public void showTipsDialog() {
        if (mContext == null) return;
        final ExpCnofirmDialog dialog = new ExpCnofirmDialog(mContext, mData.getTips(), mContext.getString(R.string.exp_meeting_efficiency_dialog_sure_btn), "");
        dialog.setOnClickListener(new ExpCnofirmDialog.OnClickListener() {
            @Override
            public void onClickOk() {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onClickCancel() {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        dialog.show();
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public TextView mTitleTv, mTvDate, mMeetingCount, mMeetingDuration, mMeetingParticipant, mStatusMeetingCount;
        public TextView mMeetingEfficiencyTitle, mMeetingEfficiencyContent, mStatusMeetingDuration, mStatusMeetingParticipant, mMeetingSuggest, mMeetingMore, mTvEmpty;
        public RelativeLayout mRlMeetingEfficiency;
        public LinearLayout mLlMeetingSuggest, mLlMeetingTitle;
        public IconFontView mIvTip, mIvMore;
        public ImageView mIvIcon, mIvMeetingCount, mIvMeetingParticipant, mIvMeetingDuration;
        public View vMeetingSuggestTop, mLlMeetingView, mLlEmptyView;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mTitleTv = itemView.findViewById(R.id.tv_meeting_title);
            mTvDate = itemView.findViewById(R.id.tv_meeting_date);
            mMeetingCount = itemView.findViewById(R.id.tv_num_meeting_count);
            mMeetingDuration = itemView.findViewById(R.id.tv_num_meeting_duration);
            mMeetingParticipant = itemView.findViewById(R.id.tv_num_meeting_participant);
            mStatusMeetingParticipant = itemView.findViewById(R.id.tv_status_meeting_participant);
            mStatusMeetingCount = itemView.findViewById(R.id.tv_status_meeting_count);
            mStatusMeetingDuration = itemView.findViewById(R.id.tv_status_meeting_duration);
            mMeetingSuggest = itemView.findViewById(R.id.tv_meeting_suggest);
            mMeetingEfficiencyTitle = itemView.findViewById(R.id.tv_meeting_efficiency_title);
            mMeetingEfficiencyContent = itemView.findViewById(R.id.tv_meeting_efficiency_content);
            mRlMeetingEfficiency = itemView.findViewById(R.id.rl_meeting_efficiency);
            mIvTip = itemView.findViewById(R.id.iv_tip_meeting_title);
            mMeetingMore = itemView.findViewById(R.id.tv_meeting_more);
            mIvMore = itemView.findViewById(R.id.iv_meeting_more);
            mIvIcon = itemView.findViewById(R.id.iv_icon_meeting_title);
            mIvMeetingCount = itemView.findViewById(R.id.iv_meeting_count_icon);
            mIvMeetingParticipant = itemView.findViewById(R.id.iv_meeting_participant_icon);
            mIvMeetingDuration = itemView.findViewById(R.id.iv_meeting_duration_icon);
            vMeetingSuggestTop = itemView.findViewById(R.id.v_meeting_suggest_top);
            mLlMeetingSuggest = itemView.findViewById(R.id.ll_meeting_suggest);
            mLlMeetingTitle = itemView.findViewById(R.id.ll_meeting_title);
            mLlMeetingView = itemView.findViewById(R.id.ll_meeting);
            mLlEmptyView = itemView.findViewById(R.id.ll_empty);
            mTvEmpty = itemView.findViewById(R.id.tv_empty);
        }
    }
}