package com.jd.oa.experience.activity;

import android.animation.Animator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.webkit.JavascriptInterface;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.airbnb.lottie.LottieAnimationView;
import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.HomePageBadgeAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.dialog.HomePageInstructionDialog;
import com.jd.oa.experience.fragment.ExperienceSelfInfoFragment;
import com.jd.oa.experience.model.HomePageData;
import com.jd.oa.experience.model.ThumbsUpData;
import com.jd.oa.experience.repo.MyHomePageRepo;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.HomePageHeadRefreshView;
import com.jd.oa.experience.view.HomePageProgressBar;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.fragment.js.hybrid.JsEvent;
import com.jd.oa.fragment.js.hybrid.JsIm;
import com.jd.oa.fragment.js.hybrid.JsUser;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback3;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.listener.UserServiceListener;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.ExperiencePreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.pulltorefresh.PullToRefreshLayout;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.StatusBarUtil;
import com.tencent.smtt.export.external.interfaces.WebResourceError;
import com.tencent.smtt.export.external.interfaces.WebResourceRequest;
import com.tencent.smtt.export.external.interfaces.WebResourceResponse;
import com.tencent.smtt.sdk.CookieManager;
import com.tencent.smtt.sdk.CookieSyncManager;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;
import com.zhy.view.flowlayout.TagFlowLayout;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Observable;
import java.util.Observer;
import java.util.Random;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.core.app.ActivityOptionsCompat;
import androidx.core.widget.NestedScrollView;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import wendu.dsbridge.CompletionHandler;

@Route(DeepLink.EXP_MY_JOYSPACE)
public class MyHomePageActivity extends BaseActivity implements Observer, UserServiceListener {

    private static final String CACHE_KEY = "exp.homepage.cache.key.";
    public static final int MESSAGE_CLICK_LIKE = 8;
    public static final String EXTRA_IS_SELF = "extra_is_self";
    public static final String EXTRA_CARD_ID = "extra_card_id";
    public static final String EXTRA_ERP = "extra_erp";

    private NestedScrollView mScrollView;
    private ImageView mCustomBkGndIv;
    private ImageView mAvatarIv;
    private ImageView mPendantIv;
    private TextView mNameTv;
    private ImageView mFeelingIcon;
    private TextView mFeelingTv;
    private IconFontView mFeelingBtn;
    private View mFeelingLayout;

    private TextView mVisitedCountTv;
    private TextView mLikedCountTv;
    private View mVisitedLayout;
    private View mLikedLayout;
    private View mLikedRedDot;

    private TextView mGoSelfOrMsg;
    private View mGoPersonalCard;

    public View mTagLayout;
    public TagFlowLayout mTagContainer;
    public View mTagEmpty;

    public View mBadgeSection;
    public View mBadgeTitle;
    public TextView mBadgeTitleTv;
    public TextView mBadgeTitleCountTv;
    public TextView mBadgeTitleBtnTv;
    public IconFontView mBadgeTitleBtnIcon;
    public View mDefaultBadgeView;
    public View mCustomBadgeView;
    public View mSingleLineBadgeView;
    public RecyclerView mDefaultBadgeRv;
    public RecyclerView mCustomBadgeRv;
    public LinearLayout mDefaultBadgeContainer;
    public LinearLayout mCustomBadgeContainer;

    private RelativeLayout mBkClickView;
    private PullToRefreshLayout mRefreshLayout;
    private HomePageHeadRefreshView mHeadRefreshView;
    private View mMaskView;
    private IconFontView mMaskViewBack;
    private ImageView mMaskViewAvatarIv;
    private ImageView mMaskViewPendantIv;
    private View mMaskViewAvatarLayout;
    private TextView mMaskViewTitleTv;

    //小卡片
    private HomePageProgressBar mCompleteStatusBar;
    private TextView mProgressZero;
    private TextView mProgressMax;
    private View mDutyCard;
    private TagFlowLayout mDutyBusinessTags;
    private View mOkrTv;
    private View mOkrBtn;
    private View mDutyEmpty;
    private View mOkrEmpty;
    private View mDutyAdd;
    private TextView mOkrEmptyTv;
    private View mOkrAdd;
    private View mOkrCard;
    private View mDutyEditableTv;
    private View mOkrEditableTv;
    private TextView mOkrNumTv;
    private TextView mOkrSuffix;

    private View mInstructionEmptyLayout;
    private FrameLayout mInstructionPreviewLayout;
    private JMEWebview mInstructionWebView;
    //private ImageView mInstructionScreenShot;//截图
    private TextView mInstructionTitle;
    private TextView mInstructionEmptyTv;
    private View mInstructionEmptyIv;
    private View mInstructionCreateNew;
    private View mInstructionPreviewBtn;
    private View mInstructionSeparator;
    private View mInstructionEditBtn;
    private TextView mInstructionEditPrivacy;

    private View mThumbFloatLayout;
    private View thumbsUpIcon;
    private FrameLayout mLottieViewContainer;
    private LinearLayout llChangeBg;

    private HomePageInstructionDialog homePageInstructionDialog;

    private HomePageData mHomeData;
    private String mErp = "";
    private boolean mIsSelf;
    private final MyHomePageRepo mRepo = new MyHomePageRepo();
    private final Gson mGson = new Gson();
    private final ImDdService imDdService = AppJoint.service(ImDdService.class);
    private final JsSdkKit jsSdkKit = new JsSdkKit();
    private Animation mThumbIconAnim;
    private Random random = new Random();
    //private Bitmap mWebViewScreenShot;//截图
    private long mOnTouchLikeCount = 0;
    private Typeface fontLZ;

    private Handler mMoveHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.what == MESSAGE_CLICK_LIKE) {
                Message message = Message.obtain();
                message.what = MESSAGE_CLICK_LIKE;
                sendMessageDelayed(message, 300);

                thumbsUpIcon.clearAnimation();
                thumbsUpIcon.startAnimation(mThumbIconAnim);
                startLottieAnim();
                mOnTouchLikeCount++;
            }
        }
    };

    private final BroadcastReceiver mHomePageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            switch (intent.getAction()) {
                case Constants.ACTION_UPDATE_TAGS:
                case Constants.ACTION_HOMEPAGE_UPDATE_LIKED:
                case Constants.ACTION_UPDATE_INSTRUCTION_STATE:
                case Constants.ACTION_HOMEPAGE_UPDATE_BADGES:
                case Constants.ACTION_HOMEPAGE_UPDATE_WORK_CARD:
                    requestHomePageData(false);
                    break;
                case Constants.ACTION_HOMEPAGE_UPDATE_OKR_CARD:
                case Constants.JSSDK_EVENT_EXP_OBJECTIVE_NUM_UPDATE:
                    if (mIsSelf) {
                        requestHomePageData(false);
                    }
                    break;
                default:
                    break;
            }
        }
    };
    private LinearLayout mllHead;
    private View mVBGMask;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        StatusBarUtil.setTranslucentForImageViewInFragment(this, 10, null);
        StatusBarUtil.setLightMode(this);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_my_homepage);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_UPDATE_TAGS);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_LIKED);
        intentFilter.addAction(Constants.ACTION_UPDATE_INSTRUCTION_STATE);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_BADGES);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_WORK_CARD);
        intentFilter.addAction(Constants.ACTION_HOMEPAGE_UPDATE_OKR_CARD);
        intentFilter.addAction(Constants.JSSDK_EVENT_EXP_OBJECTIVE_NUM_UPDATE);
        LocalBroadcastManager.getInstance(this).registerReceiver(mHomePageReceiver, intentFilter);

        String param = getIntent().getStringExtra("mparam");//jdme://jm/biz/myspace?mparam={"userName":""}
        try {
            JSONObject object = new JSONObject(param);
            mErp = object.getString("userName");
        } catch (Exception e) {
        }
        mIsSelf = mErp != null && mErp.equalsIgnoreCase(PreferenceManager.UserInfo.getUserName());

        ExperienceSelfInfoFragment.observerList.add(this);
        imDdService.addUserService(this);

        mThumbIconAnim = AnimationUtils.loadAnimation(this, R.anim.anim_thumbs_up_scale);

        AssetManager assets = AppBase.getAppContext().getAssets();
        fontLZ = Typeface.createFromAsset(assets, "fonts/JDLangZhengTi_Regular.TTF");

        initView();
        initWebView();
        loadCache();
        updateBackGround();
        updateHeader();
        updateFeeling(false);
        updateTags();
        updateLikes();
        updateBadge();
        updateWorkCard();
        updateInstruction();
        requestHomePageData(true);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
/*        mInstructionScreenShot.setImageDrawable(null);//截图
        if (mWebViewScreenShot != null) {
            mWebViewScreenShot.recycle();
        }*/
        ExperienceSelfInfoFragment.observerList.remove(this);
        imDdService.removeUserService(this);
        imDdService.unregisterUserStatusChangeListener(getClass().getSimpleName());
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mHomePageReceiver);
        mInstructionWebView.loadUrl("about:blank");
        mInstructionWebView.destroy();//销毁web view
        mInstructionWebView = null;
        homePageInstructionDialog = null;
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMoveHandler.removeMessages(MESSAGE_CLICK_LIKE);
        mOnTouchLikeCount = 0;
        JDMAUtils.onEventPagePV(AppBase.getAppContext(), "EXP_Main_Home", "EXP_Main_Home");
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMoveHandler.removeMessages(MESSAGE_CLICK_LIKE);
        mOnTouchLikeCount = 0;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        updateTags();
        updateBadge();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        String param = intent.getStringExtra("mparam");//jdme://jm/biz/myspace?mparam={"userName":""}
        try {
            JSONObject object = new JSONObject(param);
            mErp = object.getString("userName");
        } catch (Exception e) {
        }
        mIsSelf = mErp != null && mErp.equalsIgnoreCase(PreferenceManager.UserInfo.getUserName());
        initView();
        loadCache();
        updateBackGround();
        updateHeader();
        updateFeeling(false);
        updateTags();
        updateLikes();
        updateBadge();
        updateWorkCard();
        updateInstruction();
        requestHomePageData(true);
    }

    private void initView() {
        mScrollView = findViewById(R.id.scroll_view);
        mScrollView.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                handleOnScrollChange(scrollY);
            }
        });
        mBkClickView = findViewById(R.id.bk_click_area);
        mMaskView = findViewById(R.id.mask_view);
        mMaskViewTitleTv = findViewById(R.id.mask_view_title);
        mMaskViewBack = findViewById(R.id.mask_view_back);
        mMaskViewAvatarIv = findViewById(R.id.mask_view_avatar);
        mMaskViewPendantIv = findViewById(R.id.mask_view_pendant);
        mMaskViewAvatarLayout = findViewById(R.id.mask_view_avatar_layout);
        mCustomBkGndIv = findViewById(R.id.custom_background_iv);
        mVBGMask = findViewById(R.id.v_bg_mask);
        mMaskViewBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        //下拉
        mRefreshLayout = findViewById(R.id.homepage_refresh);
        mRefreshLayout.setOnChildScrollCallback(new PullToRefreshLayout.OnChildScrollCallback() {
            @Override
            public boolean canChildScroll(@NonNull PullToRefreshLayout parent, @Nullable View child, int direction) {
                return mScrollView.canScrollVertically(-1);
            }
        });
        mHeadRefreshView = new HomePageHeadRefreshView(this);
        mRefreshLayout.setHeaderView(mHeadRefreshView);
        final int bgHeight = mCustomBkGndIv.getLayoutParams().height;
        final int bgWidth = getWindow().getWindowManager().getDefaultDisplay().getWidth();
        final int bgPivotX = (int) bgWidth / 2;
        mCustomBkGndIv.setPivotY(0);
        mVBGMask.setPivotY(0);
        mCustomBkGndIv.setPivotX(bgPivotX);
        mVBGMask.setPivotX(bgPivotX);
        mHeadRefreshView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                int height = bottom - top;
                float scale = (float) (bgHeight + height) / bgHeight;
                if (scale < 1) {
                    scale = 1;
                }
                mCustomBkGndIv.setScaleY(scale);
                mCustomBkGndIv.setScaleX(scale);
                mVBGMask.setScaleY(scale);
                mVBGMask.setScaleX(scale);
                //暂不使用addOnGlobalLayoutListener，下拉到最下边不松手，OnGlobalLayout依然会回调
            }
        });
        mRefreshLayout.setCanRefresh(true);
        mRefreshLayout.setCanLoadMore(false);
        mRefreshLayout.setRefreshListener(new PullToRefreshLayout.BaseRefreshListener() {
            @Override
            public void refresh() {
                requestHomePageData(false);
                updateFeeling(true);
            }

            @Override
            public void loadMore() {
            }
        });
        //头像
        mAvatarIv = findViewById(R.id.homepage_avatar);
        mllHead = findViewById(R.id.ll_head);
        mPendantIv = findViewById(R.id.homepage_pendant);
        mNameTv = findViewById(R.id.homepage_name);
        mFeelingIcon = findViewById(R.id.homepage_feeling_icon);
        mFeelingTv = findViewById(R.id.homepage_feeling_text);
        mFeelingBtn = findViewById(R.id.homepage_feeling_btn);
        mFeelingBtn.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
        mFeelingLayout = findViewById(R.id.homepage_feeling_layout);
        mFeelingLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!mIsSelf) {
                    return;
                }
                imDdService.openSetUserStatus(MyHomePageActivity.this);
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_FEELING.keyName, ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_FEELING.KeyValue);
                ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INFO, map);
            }
        });
        //点赞
        mVisitedLayout = findViewById(R.id.homepage_visited);//浏览没有跳转
        mLikedLayout = findViewById(R.id.homepage_liked);
        mllHead.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        mAvatarIv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        mLikedLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mIsSelf && mHomeData != null && mHomeData.liked != null && !TextUtils.isEmpty(mHomeData.liked.url)) {
                    Uri uri = Uri.parse(mHomeData.liked.url);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(MyHomePageActivity.this, new RouteNotFoundCallback(MyHomePageActivity.this));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_LIKED.keyName, ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_LIKED.KeyValue);
                    ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INFO, map);
                }
            }
        });
        mVisitedCountTv = findViewById(R.id.homepage_visited_count);
        mLikedCountTv = findViewById(R.id.homepage_liked_count);
        mLikedRedDot = findViewById(R.id.liked_red_dot);
        //跳转编辑资料
        mGoSelfOrMsg = findViewById(R.id.go_self_or_msg);
        mGoSelfOrMsg.setText(mIsSelf ? R.string.exp_homepage_go_self_edit : R.string.exp_homepage_go_send_msg);
        mGoSelfOrMsg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mIsSelf) {
                    Intent intent = new Intent(MyHomePageActivity.this, FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, ExperienceSelfInfoFragment.class.getName());
                    MyHomePageActivity.this.startActivity(intent);
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_EDIT_INFO.keyName, ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_EDIT_INFO.KeyValue);
                    ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INFO, map);
                } else {
                    imDdService.showChattingActivity(MyHomePageActivity.this, mErp);//外部用户没有个人主页
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_MSG.keyName, ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_MSG.KeyValue);
                    ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INFO, map);
                }
            }
        });
        mGoPersonalCard = findViewById(R.id.personal_card);
        mGoPersonalCard.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                imDdService.showContactDetailInfo(MyHomePageActivity.this, mErp);
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_CARD.keyName, ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_CARD.KeyValue);
                ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INFO, map);
            }
        });

        //标签
        mTagContainer = findViewById(R.id.tag_container);
        mTagEmpty = findViewById(R.id.no_tag_tv);
        mTagLayout = findViewById(R.id.tags_layout);//点击区域
        mTagLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mIsSelf && mHomeData != null && mHomeData.tags != null && !TextUtils.isEmpty(mHomeData.tags.url)) {//别人页面不能点击
                    Uri uri = Uri.parse(mHomeData.tags.url);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(MyHomePageActivity.this, new RouteNotFoundCallback(MyHomePageActivity.this));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_TAGS.keyName, ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_TAGS.KeyValue);
                    ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INFO, map);
                }
            }
        });

        //徽章
        mBadgeSection = findViewById(R.id.badge_section);
        mBadgeTitle = findViewById(R.id.badge_title_layout);
        mBadgeTitle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mHomeData != null && mHomeData.badges != null && !TextUtils.isEmpty(mHomeData.badges.url)) {
                    Router.build(mHomeData.badges.url).go(MyHomePageActivity.this, new RouteNotFoundCallback(MyHomePageActivity.this));
                    ExpJDMAUtil.onHomePageEventClick(mIsSelf ? ExpJDMAConstants.EXP_MAIN_HOME_MEDAL : ExpJDMAConstants.EXP_MAIN_HOME_MEDAL_OTHER, null);
                }
            }
        });
        mBadgeTitleTv = findViewById(R.id.badge_title_tv);
        mBadgeTitleCountTv = findViewById(R.id.badge_count_text);
        mBadgeTitleBtnTv = findViewById(R.id.badge_btn_text);
        mBadgeTitleBtnIcon = findViewById(R.id.badge_btn_icon);
        mBadgeTitleBtnTv.setText(mIsSelf ? R.string.exp_homepage_badge_title_my_btn : R.string.exp_homepage_badge_title_other_btn);
        mBadgeTitleBtnIcon.setText(mIsSelf ? R.string.icon_direction_sortascending : R.string.icon_direction_right);
        mDefaultBadgeView = findViewById(R.id.default_badge_list);
        mDefaultBadgeRv = findViewById(R.id.default_badge_rv);
        mCustomBadgeView = findViewById(R.id.custom_badge_list);
        mCustomBadgeRv = findViewById(R.id.custom_badge_rv);
        mSingleLineBadgeView = findViewById(R.id.single_line_badge_list);
        mDefaultBadgeContainer = findViewById(R.id.default_badge_container);
        mCustomBadgeContainer = findViewById(R.id.custom_badge_container);

        mThumbFloatLayout = findViewById(R.id.thumbs_float_layout);
        thumbsUpIcon = findViewById(R.id.thumbs_up_one);
        mLottieViewContainer = findViewById(R.id.lottie_view_container);

        //小卡片
        mCompleteStatusBar = findViewById(R.id.complete_status_pb);
        mProgressZero = findViewById(R.id.duty_progress_zero);
        mProgressMax = findViewById(R.id.duty_progress_max);
        mDutyCard = findViewById(R.id.duty_card);
        mDutyBusinessTags = findViewById(R.id.duty_business_tag);
        mOkrTv = findViewById(R.id.okr_tv);
        mOkrBtn = findViewById(R.id.okr_btn);
        mDutyEmpty = findViewById(R.id.duty_empty);
        mOkrEmpty = findViewById(R.id.okr_empty);
        mOkrCard = findViewById(R.id.okr_card);
        mDutyAdd = findViewById(R.id.duty_add);
        mOkrEmptyTv = findViewById(R.id.okr_empty_tv);
        mOkrAdd = findViewById(R.id.okr_add);
        mDutyEditableTv = findViewById(R.id.duty_editable_tv);
        mOkrEditableTv = findViewById(R.id.okr_editable_tv);
        mOkrNumTv = findViewById(R.id.okr_num);
        mOkrSuffix = findViewById(R.id.okr_suffix);

        //说明书
        mInstructionEmptyLayout = findViewById(R.id.instruction_empty_layout);
        mInstructionPreviewLayout = findViewById(R.id.instruction_preview_layout);
        mInstructionWebView = findViewById(R.id.instruction_preview_webview);
        //mInstructionScreenShot = findViewById(R.id.instruction_preview_screenshot);//截图
        mInstructionTitle = findViewById(R.id.instruction_title_tv);
        mInstructionEmptyTv = findViewById(R.id.instruction_empty_tv);
        mInstructionEmptyIv = findViewById(R.id.instruction_empty_iv);
        mInstructionCreateNew = findViewById(R.id.instruction_create_tv);
        mInstructionCreateNew.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MyHomePageActivity.this, MyInstructionActivity.class);
                MyHomePageActivity.this.startActivity(intent);
                ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_START, new HashMap<String, String>());
            }
        });
        mInstructionPreviewBtn = findViewById(R.id.instruction_preview_btn);
        mInstructionSeparator = findViewById(R.id.instruction_separator);
        mInstructionEditBtn = findViewById(R.id.instruction_edit_btn);
        mInstructionEditPrivacy = findViewById(R.id.edit_btn_privacy);
        mInstructionSeparator.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
        mInstructionEditBtn.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
        mInstructionPreviewBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
/*                if (mWebViewScreenShot != null) {//截图
                    mInstructionScreenShot.setImageBitmap(mWebViewScreenShot);
                }*/
                if (homePageInstructionDialog == null) {
                    homePageInstructionDialog = new HomePageInstructionDialog(MyHomePageActivity.this, mIsSelf, mInstructionWebView);
                    homePageInstructionDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            mInstructionPreviewLayout.addView(mInstructionWebView, 0);
                            mInstructionWebView.setTag("attachActivity");
                            //由于dialog和activity共用同一个webview，在dialog webview里滚动后，返回activity webview会展示当前滚动位置，这时将页面置顶
                            //由于Joyspace页面特殊性，scrollTo(0, 1)无法生效，和小悦沟通后使用以下脚本处理页面置顶
                            mInstructionWebView.loadUrl("javascript:var ele = document.querySelector('.mobile-page-main');if (ele) {ele.scrollTop = 0;}");
                        }
                    });
                }
                mInstructionPreviewLayout.removeView(mInstructionWebView);
                mInstructionWebView.setTag("attachDialog");
                homePageInstructionDialog.show();
                ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INSTRUCTION_VIEW, new HashMap<String, String>());
            }
        });
        mInstructionEditBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MyHomePageActivity.this, MyInstructionActivity.class);
                MyHomePageActivity.this.startActivity(intent);
            }
        });

        //表情
        mThumbFloatLayout.setOnTouchListener(new View.OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //按住不动，有些手机没有move消息（测试的小米12），有些手机有move消息，由move触发动画改为delayMessage实现
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    Message message = Message.obtain();
                    message.what = MESSAGE_CLICK_LIKE;
                    mMoveHandler.sendMessage(message);
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    mMoveHandler.removeMessages(MESSAGE_CLICK_LIKE);
                    if (mOnTouchLikeCount > 0) {
                        mRepo.thumbsUp(mErp, "01", "", String.valueOf(mOnTouchLikeCount), new LoadDataCallback<ThumbsUpData>() {
                            @Override
                            public void onDataLoaded(ThumbsUpData data) {
                                if (data != null && data.likeMark) {
                                    if (!TextUtils.isEmpty(data.likedCount)) {
                                        mLikedCountTv.setText(data.likedCount);
                                        mLikedRedDot.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
                                    }
                                    if (mIsSelf) {
                                        Intent intent = new Intent(Constants.ACTION_HOMEPAGE_CLICK_LIKED);
                                        intent.putExtra("likeCount", data.likeCount != null ? data.likeCount : "0");
                                        intent.putExtra("likedCount", data.likedCount != null ? data.likedCount : "0");
                                        LocalBroadcastManager.getInstance(MyHomePageActivity.this).sendBroadcast(intent);
                                    }
                                }
                            }

                            @Override
                            public void onDataNotAvailable(String s, int i) {

                            }
                        });
                    }
                    mOnTouchLikeCount = 0;
                }
                return true;
            }
        });

        //切换背景图
        mBkClickView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goToChangeBg();
            }
        });
        llChangeBg = findViewById(R.id.ll_change_bg);
        if (mIsSelf && !ExperiencePreference.getInstance().get(ExperiencePreference.KV_ENTITY_EXP_HAS_ETER_CHANGE_BG)) {
            llChangeBg.setVisibility(View.VISIBLE);
            llChangeBg.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    goToChangeBg();
                }
            });
        } else {
            llChangeBg.setVisibility(View.GONE);
        }
    }

    @Override
    public void finish() {
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(Constants.ACTION_HOMEPAGE_FINISH));
        super.finish();
    }

    public ActivityResultLauncher<Intent> intentActivityResultLauncher =
            registerForActivityResult(new ActivityResultContracts.StartActivityForResult()
            , new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (result.getData() != null && result.getResultCode() == Activity.RESULT_OK) {
                        //更换背景图成功时
                        String url = result.getData().getStringExtra("url");
                        if (!TextUtils.isEmpty(url)) {
//                            requestHomePageData(false);
                            mHomeData.bgImage = url;
                            ImageLoader.load(AppBase.getAppContext(), mCustomBkGndIv, url);
                        }
                    } else {
                        //未更换成功
//                        Log.e(TAG, "onActivityResult: " + result.getResultCode());
                    }
                }
            });


    /**
     *
     * 跳转修改背景图
     */
    public void goToChangeBg() {
        if (mIsSelf && !ExperiencePreference.getInstance().get(ExperiencePreference.KV_ENTITY_EXP_HAS_ETER_CHANGE_BG)) {
            ExperiencePreference.getInstance().put(ExperiencePreference.KV_ENTITY_EXP_HAS_ETER_CHANGE_BG, true);
            llChangeBg.setVisibility(View.GONE);
        }
        Intent intent = new Intent(MyHomePageActivity.this, ChangeMyBgActivity.class);
        intent.putExtra("isSelf", mIsSelf);
        if (mHomeData != null && !TextUtils.isEmpty(mHomeData.bgImage)) {
            intent.putExtra("imageUrl", mHomeData.bgImage);
            ActivityOptionsCompat bgImage = ActivityOptionsCompat
                    .makeSceneTransitionAnimation(MyHomePageActivity.this, mCustomBkGndIv, "bgImage");
            intentActivityResultLauncher.launch(intent, bgImage);
//            overridePendingTransition(0, 0);
        } else {
            //默认图不共享元素 直接跳
            intentActivityResultLauncher.launch(intent);
//            overridePendingTransition(0, 0);
        }
        Map<String, String> map = new HashMap<>();
        map.put(ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_COVER.keyName, ExpJDMAConstants.MainHomeInfo.MAIN_HOME_INFO_COVER.KeyValue);
        ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_INFO, map);
        ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_CHANGE_COVER, new HashMap<String, String>());
    }

    private void updateBackGround() {
        if (mHomeData == null || TextUtils.isEmpty(mHomeData.bgImage)) {
            //默认图
            mCustomBkGndIv.setScaleType(ImageView.ScaleType.FIT_START);
            ImageLoader.load(AppBase.getAppContext(), mCustomBkGndIv, R.drawable.exp_default_bkgnd);
        } else {
            mCustomBkGndIv.setScaleType(ImageView.ScaleType.CENTER_CROP);
            ImageLoader.load(AppBase.getAppContext(), mCustomBkGndIv, mHomeData.bgImage);
        }
    }

    private void updateHeader() {
        if (mIsSelf) {
            //头像
            String avatar = PreferenceManager.UserInfo.getUserCover();
            if (TextUtils.isEmpty(avatar) && mHomeData != null && mHomeData.userInfo != null && !TextUtils.isEmpty(mHomeData.userInfo.avatar)) {
                avatar = mHomeData.userInfo.avatar;
            }
            ImageLoader.load(AppBase.getAppContext(), mAvatarIv, avatar, R.drawable.profile_section_avatar_default);
            ImageLoader.load(AppBase.getAppContext(), mMaskViewAvatarIv, avatar, R.drawable.profile_section_avatar_default);

            //挂件
            String pendantSmall = imDdService.getPendan();
            if (!TextUtils.isEmpty(pendantSmall)) {
                ImageLoader.load(AppBase.getAppContext(), mPendantIv, pendantSmall);
            } else {
                imDdService.getPendanByNet(new Callback<String>() {
                    @Override
                    public void onSuccess(String str) {
                        if (!TextUtils.isEmpty(str)) {
                            ImageLoader.load(AppBase.getAppContext(), mPendantIv, str);
                            ImageLoader.load(AppBase.getAppContext(), mMaskViewPendantIv, str);
                        } else {
                            mPendantIv.setImageResource(0);
                            mMaskViewPendantIv.setImageResource(0);
                        }
                    }

                    @Override
                    public void onFail() {

                    }
                });
            }
        } else {
            String avatar = "";
            String pendant = "";
            if (mHomeData != null && mHomeData.userInfo != null) {
                if (!TextUtils.isEmpty(mHomeData.userInfo.avatar)) {
                    avatar = mHomeData.userInfo.avatar;
                }
                if (!TextUtils.isEmpty(mHomeData.userInfo.pendant)) {
                    pendant = mHomeData.userInfo.pendant;
                }
            }
            ImageLoader.load(AppBase.getAppContext(), mAvatarIv, avatar, R.drawable.profile_section_avatar_default);
            ImageLoader.load(AppBase.getAppContext(), mMaskViewAvatarIv, avatar, R.drawable.profile_section_avatar_default);

            if (!TextUtils.isEmpty(pendant)) {
                ImageLoader.load(AppBase.getAppContext(), mPendantIv, pendant);
                ImageLoader.load(AppBase.getAppContext(), mMaskViewPendantIv, pendant);
            } else {
                mPendantIv.setImageResource(0);
                mMaskViewPendantIv.setImageResource(0);
            }
        }


        //名字
        if (mHomeData != null && mHomeData.userInfo != null && !TextUtils.isEmpty(mHomeData.userInfo.realName)) {
            mNameTv.setText(mHomeData.userInfo.realName);
            mMaskViewTitleTv.setText(mHomeData.userInfo.realName);
        }
    }

    private void updateFeeling(boolean pullRefresh) {
        //心情
        if (!pullRefresh) {//初始化，先展示无心情UI，防止咚咚接口不回调
            mFeelingLayout.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
            if (mIsSelf) {
                mFeelingIcon.setVisibility(View.GONE);
                mFeelingTv.setText(R.string.exp_homepage_add_feeling);
                mFeelingBtn.setText(R.string.icon_prompt_add);
            }
        }
        imDdService.registerUserStatusChangeListener(mErp, getClass().getSimpleName(), new Callback3<String>() {
            @Override
            public void onSuccess(String icon, String title, String titleEn) {//回调null代表选择了无状态
                if (TextUtils.isEmpty(icon) && TextUtils.isEmpty(title) && TextUtils.isEmpty(titleEn)) {
                    mFeelingLayout.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
                    if (mIsSelf) {
                        mFeelingIcon.setVisibility(View.GONE);
                        mFeelingTv.setText(R.string.exp_homepage_add_feeling);
                        mFeelingBtn.setText(R.string.icon_prompt_add);
                    }
                } else {
                    mFeelingLayout.setVisibility(View.VISIBLE);
                    mFeelingIcon.setVisibility(!TextUtils.isEmpty(icon) ? View.VISIBLE : View.GONE);
                    ImageLoader.load(AppBase.getAppContext(), mFeelingIcon, icon);
                    if (TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN")) {
                        mFeelingTv.setText(title);
                    } else {
                        mFeelingTv.setText(titleEn);
                    }
                    mFeelingBtn.setText(R.string.icon_direction_right);
                }
            }

            @Override
            public void onFail(String msg) {}
        });
    }

    private void updateTags() {
        if (mHomeData == null || mHomeData.tags == null || mHomeData.tags.list == null || mHomeData.tags.list.isEmpty()) {
            //没有数据
            if (mIsSelf) {//如果是自己
                mTagContainer.setVisibility(View.VISIBLE);
                mTagEmpty.setVisibility(View.GONE);
                List<String> tags = new ArrayList<>();
                tags.add("");
                mTagContainer.setAdapter(new TagAdapter<String>(tags) {
                    @Override
                    public View getView(FlowLayout parent, int position, String tag) {
                        return LayoutInflater.from(MyHomePageActivity.this).inflate(R.layout.jdme_homepage_tag_add_item, parent, false);
                    }
                });
                mTagContainer.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
                    @Override
                    public boolean onTagClick(View view, int position, FlowLayout parent) {
                        return mTagLayout.performClick();
                    }
                });
            } else {//如果是他人
                mTagContainer.setVisibility(View.GONE);
                mTagEmpty.setVisibility(View.VISIBLE);
            }
            return;
        }

        //有数据
        mTagContainer.setVisibility(View.VISIBLE);
        mTagEmpty.setVisibility(View.GONE);
        List<String> tagList = new ArrayList<>(mHomeData.tags.list);
        if (mIsSelf) {
            tagList.add("");//别人页面不带向右箭头，不能跳转
        }
        mTagContainer.setAdapter(new TagAdapter<String>(tagList) {
            @Override
            public View getView(FlowLayout parent, int position, String tag) {
                View tagView;
                if (TextUtils.isEmpty(tag) && position >= mHomeData.tags.list.size()) {//最后一个箭头
                    tagView = LayoutInflater.from(MyHomePageActivity.this).inflate(R.layout.jdme_homepage_tag_go_item, parent, false);
                } else {
                    tagView = LayoutInflater.from(MyHomePageActivity.this).inflate(R.layout.jdme_homepage_tag_view_item, parent, false);
                    TextView tvTag = tagView.findViewById(R.id.tvTag);
                    if (tvTag != null) {
                        tvTag.setText(tag);
                    }
                }

                return tagView;
            }
        });
        mTagContainer.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                return mTagLayout.performClick();
            }
        });
    }

    private void updateLikes() {
        if (mHomeData == null) {
            return;
        }

        mVisitedCountTv.setText(TextUtils.isEmpty(mHomeData.visitedCount) ? "0" : mHomeData.visitedCount);
        if (mHomeData.liked != null) {
            mLikedCountTv.setText(TextUtils.isEmpty(mHomeData.liked.count) ? "0" : mHomeData.liked.count);
            mLikedRedDot.setVisibility(mIsSelf && mHomeData.liked.hasNew != null && mHomeData.liked.hasNew ? View.VISIBLE : View.GONE);
        } else {
            mLikedCountTv.setText("0");
            mLikedRedDot.setVisibility(View.GONE);
        }
    }

    private void updateBadge() {
        if (mHomeData == null || mHomeData.badges == null) {
            mBadgeSection.setVisibility(View.GONE);
            return;
        }
        
        int defaultBadgeCount = mHomeData.badges.defaultList != null ? mHomeData.badges.defaultList.size() : 0;
        int customBadgeCount = mHomeData.badges.userCustomList != null ? mHomeData.badges.userCustomList.size() : 0;
        if (mHomeData.badges.count == null || mHomeData.badges.count <= 0 || (defaultBadgeCount <= 0 && customBadgeCount <= 0)) {
            mBadgeSection.setVisibility(View.GONE);
            return;
        }
        
        mBadgeSection.setVisibility(View.VISIBLE);
        mBadgeTitleTv.setText(mIsSelf ? R.string.exp_homepage_badge_title : R.string.exp_homepage_badge_title_other);
        mBadgeTitleCountTv.setText(getString(R.string.exp_homepage_badge_title_count, mHomeData.badges.count.toString()));
        if (!TextUtils.isEmpty(mHomeData.badges.url)) {
            mBadgeTitleBtnTv.setVisibility(View.VISIBLE);
            mBadgeTitleBtnIcon.setVisibility(View.VISIBLE);
        } else {
            mBadgeTitleBtnTv.setVisibility(View.GONE);
            mBadgeTitleBtnIcon.setVisibility(View.GONE);
        }
        //两类徽章数量都是0不展示楼层，徽章数量都不为零判断是否一行能展示全
        if (defaultBadgeCount > 0 && customBadgeCount > 0) {
            int totalWidth = getResources().getDimensionPixelSize(R.dimen.homepage_badge_left_margin) +
                            getResources().getDimensionPixelSize(R.dimen.homepage_badge_icon_width) * 2 +
                            getResources().getDimensionPixelSize(R.dimen.homepage_badge_separator_width);
            totalWidth += (defaultBadgeCount + customBadgeCount) *
                    (getResources().getDimensionPixelSize(R.dimen.homepage_badge_item_width) + getResources().getDimensionPixelSize(R.dimen.homepage_badge_item_width_margin));
            if (totalWidth < ScreenUtil.getScreenWidth(this)) {//可以一行展示
                mDefaultBadgeView.setVisibility(View.GONE);
                mCustomBadgeView.setVisibility(View.GONE);
                mSingleLineBadgeView.setVisibility(View.VISIBLE);
                mDefaultBadgeContainer.removeAllViews();
                for (HomePageData.BadgeItem item : mHomeData.badges.defaultList) {
                    View view = LayoutInflater.from(this).inflate(R.layout.jdme_activity_my_homepage_badge_item, mDefaultBadgeContainer, false);
                    ImageView badgeIv = view.findViewById(R.id.iv_badge);
                    ImageLoader.load(AppBase.getAppContext(), badgeIv, item.url);
                    mDefaultBadgeContainer.addView(view);
                }
                mCustomBadgeContainer.removeAllViews();
                for (HomePageData.BadgeItem item : mHomeData.badges.userCustomList) {
                    View view = LayoutInflater.from(this).inflate(R.layout.jdme_activity_my_homepage_badge_item, mDefaultBadgeContainer, false);
                    ImageView badgeIv = view.findViewById(R.id.iv_badge);
                    ImageLoader.load(AppBase.getAppContext(), badgeIv, item.url);
                    mCustomBadgeContainer.addView(view);
                }
                return;
            }
        }
        //非一行展示两种徽章
        mSingleLineBadgeView.setVisibility(View.GONE);
        if (defaultBadgeCount > 0) {
            mDefaultBadgeView.setVisibility(View.VISIBLE);
            mDefaultBadgeRv.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
            mDefaultBadgeRv.setAdapter(new HomePageBadgeAdapter(this, mHomeData.badges.defaultList));
        } else {
            mDefaultBadgeView.setVisibility(View.GONE);
        }
        if (customBadgeCount > 0) {
            mCustomBadgeView.setVisibility(View.VISIBLE);
            mCustomBadgeRv.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
            mCustomBadgeRv.setAdapter(new HomePageBadgeAdapter(this, mHomeData.badges.userCustomList));
        } else {
            mCustomBadgeView.setVisibility(View.GONE);
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initWebView() {
        mInstructionWebView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                Object tag = mInstructionWebView.getTag();
                return !(tag instanceof String) || !TextUtils.equals((String) tag, "attachDialog");//拦截
            }
        });
        mInstructionWebView.addJavascriptObject(new JsEvent(mInstructionWebView) {
            @Override
            @JavascriptInterface
            public void sendEvent(Object args, CompletionHandler<Object> handler) {
                super.sendEvent(args, handler);
            }
        }, JsEvent.DOMAIN);
        mInstructionWebView.addJavascriptObject(new JsIm(jsSdkKit, null), JsIm.DOMAIN);
        mInstructionWebView.addJavascriptObject(new JsUser(mInstructionWebView, null), JsUser.DOMAIN);
        mInstructionWebView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
        mInstructionWebView.getSettings().setTextZoom(100);
        mInstructionWebView.setWebViewClient(new WebViewClient() {

            @Override
            public void onPageFinished(WebView webView, String s) {
                super.onPageFinished(webView, s);
/*                MyHomePageActivity.this.runOnUiThread(new Runnable() {//截图
                    @Override
                    public void run() {
                        int wholeWidth = mInstructionWebView.computeHorizontalScrollRange();
                        int wholeHeight = getResources().getDimensionPixelSize(R.dimen.homepage_webview_height);
                        Bitmap x5bitmap = Bitmap.createBitmap(wholeWidth, wholeHeight, Bitmap.Config.ARGB_8888);
                        Canvas x5canvas = new Canvas(x5bitmap);
                        x5canvas.scale((float)wholeWidth / (float)mInstructionWebView.getContentWidth(), (float)wholeHeight / (float)mInstructionWebView.getContentHeight());
                        if (mInstructionWebView.getX5WebViewExtension() == null) {
                            return;
                        }
                        mInstructionWebView.getX5WebViewExtension().snapshotWholePage(x5canvas, false, false);
                        //mWebViewScreenShot = x5bitmap;
                    }
                });*/
            }

            @Override
            public void onReceivedError(WebView webView, WebResourceRequest webResourceRequest, WebResourceError webResourceError) {
                super.onReceivedError(webView, webResourceRequest, webResourceError);
            }

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest webResourceRequest) {
                return super.shouldInterceptRequest(webView, webResourceRequest);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }
        });
    }

    private void setEmptyBgImage() {
        boolean isCN = TextUtils.equals(LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()), "zh_CN");
        int maxWidth = ScreenUtil.getScreenWidth(this) - DensityUtil.dp2px(this, 32);
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) mInstructionEmptyIv.getLayoutParams();
        lp.width = Math.min(maxWidth, DensityUtil.dp2px(this, 343));
        lp.height = lp.width * (isCN ? 149 : 167) / 343;
        mInstructionEmptyIv.setBackgroundResource(isCN ? R.drawable.jdme_bg_instruction_empty_cn : R.drawable.jdme_bg_instruction_empty_en);
    }

    @SuppressLint("SetTextI18n")
    private void updateWorkCard() {
        HomePageData.WorkCard workShow = null;
        if (mHomeData != null && mHomeData.userManual != null && mHomeData.userManual.workShow != null) {
            workShow = mHomeData.userManual.workShow;
        }
        //进度条
        int progress = 0;
        int max = 5;
        if (workShow != null) {
            if (workShow.totalNum != null) {
                max = workShow.totalNum;
            }
            if (workShow.finishNum != null) {
                progress = workShow.finishNum;
            }
        }
        mProgressZero.setTypeface(fontLZ);
        mProgressMax.setTypeface(fontLZ);
        mProgressMax.setText(String.valueOf(max));
        setCompleteProgress(progress, max);

        HomePageData.Duty duty = null;
        HomePageData.OKR okr = null;
        if (workShow != null && workShow.cardList != null) {
            duty = workShow.cardList.responsibility;
            okr = workShow.cardList.aim;
        }

        //okr
        final Integer okrNum;
        final boolean isGrayUser;
        if (okr != null && okr.content != null) {
            okrNum = okr.content.targetNum;
            isGrayUser = okr.content.toIsGray != null && okr.content.toIsGray;
        } else {
            okrNum = null;
            isGrayUser = false;
        }

        mOkrCard.setVisibility(isGrayUser ? View.VISIBLE : View.GONE);
        if (okrNum != null && okrNum > 0) {
            mOkrNumTv.setTypeface(fontLZ);
            mOkrNumTv.setText(okrNum.toString());
            mOkrSuffix.setText(getResources().getQuantityString(R.plurals.exp_homepage_okr_suffix, okrNum));
            mOkrTv.setVisibility(View.VISIBLE);
            mOkrBtn.setVisibility(View.VISIBLE);
            mOkrEmpty.setVisibility(View.GONE);
            mOkrEmptyTv.setText("");
            mOkrAdd.setVisibility(View.GONE);
            mOkrEditableTv.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
        } else {
            mOkrNumTv.setText("");
            mOkrSuffix.setText("");
            mOkrTv.setVisibility(View.GONE);
            mOkrBtn.setVisibility(View.GONE);
            mOkrEmpty.setVisibility(View.VISIBLE);
            mOkrEmptyTv.setText(mIsSelf ? R.string.exp_homepage_instruction_okr_empty_self : R.string.exp_homepage_instruction_okr_empty_other);
            mOkrAdd.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
            mOkrEditableTv.setVisibility(View.GONE);
        }
        NoDoubleClickListener goToJoyWork = new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if (mHomeData == null || mHomeData.userInfo == null) {
                    return;
                }
                if ((okrNum == null || okrNum <= 0) && (!mIsSelf || !isGrayUser)) {
                    return;
                }
                JSONObject jsonObject = new JSONObject();
                try {
                    String xingyunObjectAppId=LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getXingyunObjectiveAppId();
                    String xingyunObjectiveUrl=LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getXingyunObjectiveUrl();

                    String url = xingyunObjectiveUrl +"&erp="+ mErp;
                    jsonObject.put("appId", xingyunObjectAppId);
                    jsonObject.put("url", url);
                    jsonObject.put("isNativeHead", "0");
                    jsonObject.put("SafeArea", "0");
                    String deepLink = DeepLink.BROWSER + "?mparam=" + Uri.encode(jsonObject.toString());
                    Router.build(deepLink).go(MyHomePageActivity.this, new RouteNotFoundCallback(MyHomePageActivity.this));
                    ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_WORK_GOALS, new HashMap<String, String>());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        };
        mOkrCard.setOnClickListener(goToJoyWork);
        mOkrAdd.setOnClickListener(goToJoyWork);

        //工作职责
        final List<String> businessTags = new ArrayList<>();
        if (duty != null && duty.content != null) {
            for (HomePageData.Duty.Content content : duty.content) {
                if ((TextUtils.equals(content.questionCode, "Q100101") || TextUtils.equals(content.questionCode, "Q100102")) &&
                        content.answerKeyword != null) {//Q100101:主要负责的业务/产品线名称，Q100102:重点工作内容
                    businessTags.addAll(content.answerKeyword);
                }
            }
        }

        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) mDutyCard.getLayoutParams();
        lp.height = mOkrCard.getVisibility() == View.GONE ? LinearLayout.LayoutParams.WRAP_CONTENT : DensityUtil.dp2px(this, 197);
        mDutyCard.setLayoutParams(lp);

        mDutyBusinessTags.setVisibility(!businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mDutyEmpty.setVisibility(businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mDutyAdd.setVisibility(mIsSelf && businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mDutyEditableTv.setVisibility(mIsSelf && !businessTags.isEmpty() ? View.VISIBLE : View.GONE);
        mDutyBusinessTags.setAdapter(new TagAdapter<String>(businessTags) {
            @Override
            public View getView(FlowLayout parent, int position, String tag) {
                View tagView = LayoutInflater.from(MyHomePageActivity.this).inflate(R.layout.jdme_homepage_duty_tag_item, parent, false);
                TextView tagTv = tagView.findViewById(R.id.tag_tv);
                if (tagTv != null) {
                    tagTv.setText(tag);
                }
                return tagView;
            }
        });
        final String cardId = duty != null ? duty.cardId : "";
        mDutyBusinessTags.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                startDutyActivity(cardId);
                return true;
            }
        });
        mDutyAdd.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                startDutyActivity(cardId);
            }
        });
        mDutyCard.setOnClickListener(new NoDoubleClickListener() {
            @Override
            protected void onNoDoubleClick(View v) {
                if (businessTags.isEmpty() && !mIsSelf) {
                    return;
                }
                startDutyActivity(cardId);
            }
        });
    }

    private void startDutyActivity(String cardId) {
        Intent intent = new Intent(MyHomePageActivity.this, HomePageDutyActivity.class);
        intent.putExtra(EXTRA_IS_SELF, mIsSelf);
        intent.putExtra(EXTRA_CARD_ID, cardId);
        intent.putExtra(EXTRA_ERP, mErp);
        startActivity(intent);
        ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_WORK_DUTY, new HashMap<String, String>());
    }

    private void setCompleteProgress(int progress, int max) {
        progress = Math.min(max, progress);
        progress = Math.max(0, progress);
        mCompleteStatusBar.setProgress(progress, max);
    }

    private void updateInstruction() {
        mInstructionTitle.setText(mIsSelf ? R.string.exp_homepage_instruction_title_mine : R.string.exp_homepage_instruction_title_other);
        if (Build.VERSION.SDK_INT < 28) {//JoySpace不支持安卓9以下
            mInstructionEmptyLayout.setVisibility(View.VISIBLE);
            mInstructionPreviewLayout.setVisibility(View.GONE);
            mInstructionEmptyTv.setText(R.string.exp_homepage_instruction_low_version);
            mInstructionCreateNew.setVisibility(View.GONE);
            setEmptyBgImage();
            return;
        }
        if (mHomeData == null || mHomeData.userManual == null || TextUtils.isEmpty(mHomeData.userManual.previewUrl)
                || (!mIsSelf && (mHomeData.userManual.isShow == null || !mHomeData.userManual.isShow))) {//他人未公开
            mInstructionEmptyLayout.setVisibility(View.VISIBLE);
            mInstructionPreviewLayout.setVisibility(View.GONE);
            mInstructionEmptyTv.setText(mIsSelf ? R.string.exp_homepage_empty_instruction_mine : R.string.exp_homepage_empty_instruction_other);
            mInstructionCreateNew.setVisibility(mIsSelf ? View.VISIBLE : View.GONE);
            setEmptyBgImage();
            return;
        }
        mInstructionEmptyLayout.setVisibility(View.GONE);
        mInstructionPreviewLayout.setVisibility(View.VISIBLE);
        mInstructionEditPrivacy.setTextColor(mHomeData.userManual.isShow != null && mHomeData.userManual.isShow ? 0xFFFF6B23 : 0xFF999999);
        mInstructionEditPrivacy.setBackgroundResource(mHomeData.userManual.isShow != null && mHomeData.userManual.isShow ? R.drawable.jdme_bg_homepage_instruction_public : R.drawable.jdme_bg_homepage_instruction_privacy);
        mInstructionEditPrivacy.setText(mHomeData.userManual.isShow != null && mHomeData.userManual.isShow ? R.string.exp_homepage_instruction_public : R.string.exp_homepage_instruction_privacy);
/*        if (mWebViewScreenShot != null) {//截图
            mWebViewScreenShot.recycle();
            mWebViewScreenShot = null;
        }*/
        CookieSyncManager.createInstance(this);
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.setCookie(mHomeData.userManual.previewUrl, "focus-token=" + TokenManager.getInstance().getAccessToken());
        cookieManager.setCookie(mHomeData.userManual.previewUrl, "focus-team-id=" + PreferenceManager.UserInfo.getTenantCode());
        cookieManager.setCookie(mHomeData.userManual.previewUrl, "focus-client=Android");
        cookieManager.setCookie(mHomeData.userManual.previewUrl, "focus-lang=" + LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
        CookieSyncManager.getInstance().sync();
        mInstructionWebView.setNestedScrollingEnabled(true);
        Map<String, String> additionalHeaders = new HashMap<>();
        additionalHeaders.put("focus-token", TokenManager.getInstance().getAccessToken());
        additionalHeaders.put("focus-team-id", PreferenceManager.UserInfo.getTenantCode());
        additionalHeaders.put("focus-client", "Android");
        additionalHeaders.put("focus-lang", LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
        mInstructionWebView.loadUrl(mHomeData.userManual.previewUrl, additionalHeaders);
        mInstructionWebView.setTag("attachActivity");
        MELogUtil.localI(MELogUtil.TAG_JIS, "webview loadUrl = " + mHomeData.userManual.previewUrl);
        MELogUtil.localI(MELogUtil.TAG_JIS, "webview headers = " + additionalHeaders);
    }

    //erp，查看自己主页时不能传，查看他人主页时必传
    private void requestHomePageData(final boolean visited) {//计入浏览次数
        mRepo.getHomePageData(mErp, visited, new LoadDataCallback<HomePageData>() {
            @Override
            public void onDataLoaded(HomePageData data) {
                boolean needUpdateWebView = true;
                if (visited && data != null && mHomeData != null
                        && data.userManual != null && mHomeData.userManual != null
                        && TextUtils.equals(data.userManual.previewUrl, mHomeData.userManual.previewUrl)
                        && data.userManual.isShow == mHomeData.userManual.isShow) {

                    needUpdateWebView = false;
                }
                mHomeData = data;
                addCache(data);
                updateBackGround();
                updateHeader();
                updateTags();
                updateLikes();
                updateBadge();
                updateWorkCard();
                if (needUpdateWebView) {
                    updateInstruction();
                }
                finishRefresh();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Log.e(TAG, "onDataNotAvailable: " + s);
                finishRefresh();
            }
        });
    }

    private void finishRefresh() {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mRefreshLayout.finishRefresh();
            }
        });
    }

    private void loadCache() {
        HomePageData cacheData = getCache();
        if (cacheData == null) {
            return;
        }
        mHomeData = cacheData;
    }

    private HomePageData getCache() {
        String key = CACHE_KEY + mErp;
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), key, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        HomePageData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<HomePageData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    private void addCache(HomePageData data) {
        String key = CACHE_KEY + mErp;
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), key, null, mGson.toJson(data));
    }


    private final int mMaskStart = DisplayUtils.dip2px(62);//头像上方到遮罩下方距离
    private final int mMaskEnd = DisplayUtils.dip2px(128);//头像上方到屏幕上方距离为150，根据效果调整
    private final int mMax = mMaskEnd - mMaskStart;

    private void handleOnScrollChange(int scrollY) {
        if (scrollY < mMaskStart) {//未开始放缩
            mMaskView.setBackgroundColor(0x00FFFFFF);//透明
            mMaskViewBack.setTextColor(0xFFFFFFFF);
            mMaskViewAvatarLayout.setVisibility(View.INVISIBLE);
            mMaskViewTitleTv.setVisibility(View.INVISIBLE);
            mMaskView.setClickable(false);//点击事件透传给下层，打开切换封面
        } else if (scrollY > mMaskEnd) {//收起状态
            mMaskView.setBackgroundColor(0xFFFFFFFF);//不透明
            mMaskViewBack.setTextColor(0xFF000000);
            mMaskViewAvatarLayout.setVisibility(View.VISIBLE);
            mMaskViewTitleTv.setVisibility(View.VISIBLE);
            mMaskView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //屏蔽下层点击事件，防止打开心情状态
                }
            });
        } else {//放缩中
            int alpha = (scrollY - mMaskStart) * 255 / mMax;
            mMaskView.setBackgroundColor(alpha << 24 | 0x00FFFFFF);
            mMaskViewBack.setTextColor(alpha > 100 ? 0xFF000000 : 0xFFFFFFFF);
            mMaskViewAvatarLayout.setVisibility(View.INVISIBLE);
            mMaskViewTitleTv.setVisibility(View.INVISIBLE);
            mMaskView.setClickable(false);//点击事件透传给下层，打开切换封面
        }
    }

    //更新头像
    @Override
    public void onSignatureChanged(String signature) {
    }

    @Override
    public void onSignatureChangedNew(CharSequence signature) {

    }

    @Override
    public void onAvatarChanged(String avatar) {
        if (mAvatarIv != null) {
            ImageLoader.load(AppBase.getAppContext(), mAvatarIv, avatar, R.drawable.profile_section_avatar_default);
        }
        if (mMaskViewAvatarIv != null) {
            ImageLoader.load(AppBase.getAppContext(), mMaskViewAvatarIv, avatar, R.drawable.profile_section_avatar_default);
        }
    }

    @Override
    public void update(Observable o, final Object data) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (mAvatarIv != null) {
                    ImageLoader.load(AppBase.getAppContext(), mAvatarIv, (String) data, R.drawable.profile_section_avatar_default);
                }
                if (mMaskViewAvatarIv != null) {
                    ImageLoader.load(AppBase.getAppContext(), mMaskViewAvatarIv, (String) data, R.drawable.profile_section_avatar_default);
                }
            }
        });
    }

    private void startLottieAnim() {
        final LottieAnimationView[] lottieView = {(LottieAnimationView) LayoutInflater.from(this).inflate(
                R.layout.jdme_activity_my_homepage_lottie_view, mLottieViewContainer, false)};
        int angle = random.nextInt(60) - 30;//-30 ~ 30随机
        lottieView[0].setPivotX(DensityUtil.dp2px(this, 375));
        lottieView[0].setPivotY(DensityUtil.dp2px(this, 430));
        lottieView[0].setRotation(angle);
        lottieView[0].setRepeatCount(0);
        lottieView[0].addAnimatorListener(new Animator.AnimatorListener() {

            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                lottieView[0].setVisibility(View.GONE);
                mLottieViewContainer.removeView(lottieView[0]);
                lottieView[0] = null;
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        mLottieViewContainer.addView(lottieView[0]);
        lottieView[0].playAnimation();
    }
}