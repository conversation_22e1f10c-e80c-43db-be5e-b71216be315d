package com.jd.oa.experience.model;

import java.util.List;

public class InstructionData {

    public static class MyInstruction {
        public String demoFolderUrl;
        public String userManualUrl;
        public boolean isShow;
    }

    public static class Create {
        public String userManualUrl;
        public boolean isShow;
    }

    public static class CardContent {
        public CardContent(String questionId, String answerText, List<String> answerKeyword) {
            this.questionId = questionId;
            this.answerText = answerText;
            this.answerKeyword = answerKeyword;
        }
        public String questionId;
        public String answerText;
        public List<String> answerKeyword;
    }

    public static class CardDetail {
        public String cardId;
        public List<CardDetail.Content> content;

        public static class Content {
            public String questionId;
            public String questionCode;
            public String tips;
            public String answerText;
            public List<String> answerKeyword;
        }
    }
    /*
{
    "content":{
        "cardId":"1d1312fsw972",
        "content":[
            {
                "questionId":"",
                "questionCode":"",
                "tips":"",
                "answerText":"",
                "answerKeyword":[
                    "kw1",
                    "kw2"
                ]
            }
        ]
    },
    "errorMsg":"",
    "errorCode":"0"
}
    * */
}
