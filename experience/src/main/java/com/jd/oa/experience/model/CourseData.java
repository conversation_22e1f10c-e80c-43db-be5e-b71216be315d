package com.jd.oa.experience.model;

import java.util.ArrayList;

public class CourseData {
    public String title;
    public String icon;
    public String jumpText;
    public String jumpUrl;
    public ArrayList<CourseData.CourseType> courseTypeList;

    public static class CourseType {
        public String courseTypeName;
        public Integer courseTypeCode;
        public String mapName;
        public String moreUrl;
        public ArrayList<Course> items;
    }

    public static class Course {
        public String courseId;
        public String courseName;
        public String courseUrl;
        public String imageUrl;
        public String levelName;
        public String source;
        public String studyDuration;
    }
}
