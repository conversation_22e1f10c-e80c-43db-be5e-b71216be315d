package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.ExpAppData;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Deprecated
public class ExpAppRepo {
    private static final String CACHE_KEY = "exp.repo.cache.key.app";
    private static ExpAppRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static ExpAppRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new ExpAppRepo(context);
        }
        return sInstance;
    }

    private ExpAppRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }


    public List<ExpAppData.Apps> getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        List<ExpAppData.Apps> data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<List<ExpAppData.Apps>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(List<ExpAppData.Apps> data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null, mGson.toJson(data));
    }




    public void getAppData(final LoadDataStatusCallBack<List<ExpAppData.Apps>> callback) {
        Log.i("zhn", "getAppData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<List<ExpAppData.Apps>> response = ApiResponse.parse(info.result, new TypeToken<ArrayList<ExpAppData.Apps>>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData(), getCache() == null || getCache().size() == 0 || (response.getData().size() != getCache().size()));
                    addCache(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.getAppList");
//        }, "http://j-api.jd.com/mocker/zh/154193895/");
    }


}
