package com.jd.oa.experience.util;

import android.text.TextUtils;

import java.text.DecimalFormat;

public class NumUtil {
    /**
     * 数字转换成金额显示  66,666,666,666.00
     *
     * @param data
     * @return
     */
    public static String formatToSepara(double data) {
//        DecimalFormat df = new DecimalFormat("#,##0.00");
        DecimalFormat df = new DecimalFormat("###,###,##0.00");
        return df.format(data);
    }

    /**
     * 保留两位小数
     *
     * @param data
     * @return
     */
    public static String formatTo00(double data) {
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(data);
    }


    public static String formatTo0(double data) {
        DecimalFormat df = new DecimalFormat("0.0");
        return df.format(data);
    }

    /**
     * 如果小数点后超过2位小数 保留两位小数
     *
     * @param s
     * @return
     */
    public static String formatTo2(String s) {
        try {
            if (s.contains(".")
                    && !TextUtils.isEmpty(s.split("\\.")[1])
                    && s.split("\\.").length > 1
                    && s.split("\\.")[1].length() >= 3) {
                String[] split = s.split("\\.");
                return split[0] + "." + split[1].substring(0, 2);
            } else return s;
        } catch (Exception e) {
            return s;
        }

    }
}
