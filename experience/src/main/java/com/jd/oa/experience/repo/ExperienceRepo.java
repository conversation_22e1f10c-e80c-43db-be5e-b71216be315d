package com.jd.oa.experience.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.GlobalData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class ExperienceRepo {
    private static final String CACHE_KEY = "exp.repo.cache.key";
    private static ExperienceRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static ExperienceRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new ExperienceRepo(context);
        }
        return sInstance;
    }

    private ExperienceRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public void getGlobalData(final int from, HashMap<String, Object> params, final LoadDataCallback<GlobalData> callback) {
        HttpManager.post(null, new HashMap<String, String>(), params == null ? new HashMap<>() : params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<GlobalData> response = ApiResponse.parse(info.result, GlobalData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getGlobalConfig, from = " + from + "; data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getGlobalConfig, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getGlobalConfig, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.getGlobalConfig");
//        }, "http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.getGlobalConfig");
    }

    public GlobalData getGlobalCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        GlobalData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<GlobalData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(GlobalData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null, mGson.toJson(data));
    }
}
