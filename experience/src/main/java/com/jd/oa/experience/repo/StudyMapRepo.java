package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.StudyMapData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class StudyMapRepo {

    private static final String STUDY_MAP_CACHE_KEY = "exp.study.map.repo.cache.key";
    private static StudyMapRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static StudyMapRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new StudyMapRepo(context);
        }
        return sInstance;
    }

    private StudyMapRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public StudyMapData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), STUDY_MAP_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        StudyMapData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<StudyMapData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(StudyMapData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), STUDY_MAP_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getStudyMapData(final LoadDataCallback<StudyMapData> callback) {
        Log.i("zhn", "getStudyMapData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<StudyMapData> response = ApiResponse.parse(info.result, new TypeToken<StudyMapData>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getStudyMap, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getStudyMap, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getStudyMap, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getStudyMap");
        //http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getStudyMap
    }
}

/*
{
	"errorCode": "0",
	"content": {
		"courseTypeList": [{
			"courseTypeName": "学习地图",
			"mapName": "企业效率组-UI设计师岗-P5",
			"moreUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fmobile%2FV4%2FjingMe%23%2Fmap",
			"items": [{
				"courseList": [{
					"courseName": "复盘 将经验转化为能力-邱昭良",
					"imageUrl": "",
					"completeStatus": 0,
					"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fcourse%2Fcourse.du%3Fid%3D102752376",
					"levelName": "部门级",
					"studyDuration": "1分0秒",
					"isNew": true,
					"source": "认知能力",
					"courseId": "102752376"
				}, {
					"courseName": "学习力-刻意练习",
					"imageUrl": "",
					"completeStatus": -1,
					"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fcourse%2Fcourse.du%3Fid%3D151046128",
					"levelName": "部门级",
					"studyDuration": "0秒",
					"isNew": true,
					"source": "认知能力",
					"courseId": "151046128"
				}, {
					"courseName": "快速学会用思维导图",
					"imageUrl": "",
					"completeStatus": 1,
					"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fcourse%2Fcourse.du%3Fid%3D129679674",
					"levelName": "部门级",
					"studyDuration": "2小时0分0秒",
					"isNew": false,
					"source": "认知能力",
					"courseId": "129679674"
				}],
				"courseGroupType": 1,
				"courseGroupCode": "CI00000002",
				"courseGroupName": "基础能力"
			}, {
				"courseList": [{
					"courseName": "读懂家法，读懂京东-话题1 源起",
					"imageUrl": "",
					"completeStatus": -1,
					"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fcourse%2Fcourse.du%3Fid%3D151866563",
					"levelName": "部门级",
					"studyDuration": "0秒",
					"isNew": false,
					"source": "应知应会",
					"courseId": "151866563"
				}, {
					"courseName": "读懂家法，读懂京东-话题2 事业初心",
					"imageUrl": "",
					"completeStatus": -1,
					"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fcourse%2Fcourse.du%3Fid%3D151966946",
					"levelName": "部门级",
					"studyDuration": "0秒",
					"isNew": false,
					"source": "应知应会",
					"courseId": "151966946"
				}],
				"courseGroupType": 3,
				"courseGroupCode": "89756348e4fa41e0ab53fa381242c039",
				"courseGroupName": "应知应会"
			}],
			"courseTypeCode": 3
		}, {
			"courseTypeName": "四五系列",
			"mapName": "",
			"moreUrl": "",
			"items": [{
				"courseName": "JDTECH&技术说 第26期 《金奖专利是这样“炼”成的(一）》",
				"imageUrl": "https://jduimg.jd.com/tenant2-r/tenant2/image/dd1c7480a62a44348ed42c6f5d639ddb.jpg",
				"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fproject%2Fproject.du%3Fproject_id%3D350201330",
				"levelName": "",
				"studyDuration": "",
				"source": "京东集团",
				"courseId": "350201330"
			}, {
				"courseName": "【JDTECH】第6期 浅谈产业算法",
				"imageUrl": "https://jduimg.jd.com/tenant2-r/tenant2/image/6f4a0f738dfa41419815087ba336c3e5.jpg",
				"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fproject%2Fproject.du%3Fproject_id%3D278661295",
				"levelName": "",
				"studyDuration": "",
				"source": "京东集团",
				"courseId": "278661295"
			}, {
				"courseName": "JDTALK第62期积极心理学:快乐激发创新",
				"imageUrl": "https://jduimg.jd.com/tenant2-r/tenant2/image/8cae33a37fde4388947730f05f68dba4.jpg",
				"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fproject%2Fproject.du%3Fproject_id%3D59259247",
				"levelName": "",
				"studyDuration": "",
				"source": "通用力中心",
				"courseId": "59259247"
			}],
			"courseTypeCode": 2
		}],
		"icon": "https://storage.360buyimg.com/jd.jme.client/images/insightxuexiditu.png",
		"title": "学习地图",
		"jumpText": "",
		"jumpUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fmobile%2FV4%2FjingMe%23%2Fmap"
	},
	"errorMsg": ""
}
* */
