package com.jd.oa.experience.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.ui.IconFontView;

import java.util.ArrayList;
import java.util.List;

public class ProfileLabelsAdapter extends RecyclerView.Adapter<ProfileLabelsAdapter.ViewHolder> {

    public interface OnItemClickListener {
        void onItemClick();
    }

    private final Context mContext;
    private final List<String> mData;
    private final boolean isDarkColor;
    private final OnItemClickListener mOnItemClickListener;

    public ProfileLabelsAdapter(Context context, List<String> list, boolean darkColor, OnItemClickListener listener) {
        mContext = context;
        mData = list != null ? list : new ArrayList<String>();
        isDarkColor = darkColor;
        mOnItemClickListener = listener;
    }

    @NonNull
    @Override
    public ProfileLabelsAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_layout_profile_label_item, parent, false);
        return new ProfileLabelsAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ProfileLabelsAdapter.ViewHolder holder, int position) {
        if (mData.size() < 1 || position < 0 || position >= mData.size()) {//不存在0个标签
            return;
        }

        holder.layoutLabel.setBackgroundResource(isDarkColor ? R.drawable.jdme_bg_exp_profile_label_dark : R.drawable.jdme_bg_exp_profile_label_light);
        holder.layoutLabel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick();
                }
            }
        });
        //只有一个item，用户未选择标签，展示"添加标签 +"
        if (mData.size() == 1) {
            holder.tvLabel.setVisibility(View.VISIBLE);
            holder.tvAdd.setVisibility(View.VISIBLE);
            holder.tvGo.setVisibility(View.GONE);
            holder.tvLabel.setText(mContext.getResources().getString(R.string.exp_profile_add_label));
            holder.tvLabel.setTextColor(isDarkColor ? 0xffffffff : 0xff62656d);
            holder.tvAdd.setTextColor(isDarkColor ? mContext.getResources().getColor(R.color.color_arrow_dark) : mContext.getResources().getColor(R.color.color_arrow_normal));
            return;
        }

        if (position == mData.size() - 1) {//最后一个标签 >
            holder.tvLabel.setVisibility(View.GONE);
            holder.tvAdd.setVisibility(View.GONE);
            holder.tvGo.setVisibility(View.VISIBLE);
            holder.tvGo.setTextColor(isDarkColor ? mContext.getResources().getColor(R.color.color_arrow_dark) : mContext.getResources().getColor(R.color.color_arrow_normal));
        } else {//普通标签
            holder.tvLabel.setVisibility(View.VISIBLE);
            holder.tvAdd.setVisibility(View.GONE);
            holder.tvGo.setVisibility(View.GONE);
            holder.tvLabel.setText(mData.get(position));
            holder.tvLabel.setTextColor(isDarkColor ? 0xffffffff : 0xff62656d);
        }
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView tvLabel;
        public IconFontView tvAdd;
        public IconFontView tvGo;
        public View layoutLabel;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvLabel = itemView.findViewById(R.id.tv_label);
            tvAdd = itemView.findViewById(R.id.tv_add);
            tvGo = itemView.findViewById(R.id.tv_go);
            layoutLabel = itemView.findViewById(R.id.layout_label);
        }
    }
}