package com.jd.oa.experience.fragment;

import static com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.annotation.Route;
import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.selfInfo.ChangeTelephoneActivity;
import com.jd.oa.business.mine.selfInfo.ChangeTelephoneFragment;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.SelfInfoAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.UserInfoData;
import com.jd.oa.experience.repo.UserInfoRepo;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Observer;

@Route(DeepLink.EXP_SELF_INFO)
public class ExperienceSelfInfoFragment extends BaseFragment implements FragmentOperatingListener, OperatingListener {

    private static final String CACHE_KEY = "exp.userdata.cache.key";

    private static final int REQUEST_UPDATE_PHONE = 100;
    private static final int REQUEST_UPDATE_TELEPHONE = 101;

    private SelfInfoAdapter mSelfInfoAdapter;

    public static List<Observer> observerList = new ArrayList<>();

    private final ImDdService imDdService = AppJoint.service(ImDdService.class);
    private UserInfoRepo mRepo;
    private Context mContext;

    private int mWidth = 0;

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            switch (intent.getAction()) {
                case Constants.ACTION_EXP_PROFILE_REFRESH_SIGNATURE_NEW:
                    if (mSelfInfoAdapter != null) {
                        mSelfInfoAdapter.updateSignature();
                    }
                    break;
                case ACTION_CHANGE_THEME:
                case Constants.ACTION_UPDATE_TAGS:
                case Constants.ACTION_UPDATE_INSTRUCTION_STATE:
                    if (mSelfInfoAdapter != null) {
                        requestUserData();
                    }
                    break;
                default:
                    break;
            }
        }
    };

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        int width = DisplayUtil.getScreenWidth(getContext());
        if (mWidth != width) {
            mWidth = width;
            if (mSelfInfoAdapter != null) {
                mSelfInfoAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mWidth = DisplayUtil.getScreenWidth(getContext());
        mContext = getContext();
        ActionBarHelper.hide(this);
        View view = inflater.inflate(R.layout.jdme_fragment_experience_self_info, container, false);
        initView(view);
        loadCache();
        mRepo = UserInfoRepo.get();
        requestUserData();

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_EXP_PROFILE_REFRESH_SIGNATURE_NEW);
        intentFilter.addAction(ACTION_CHANGE_THEME);
        intentFilter.addAction(Constants.ACTION_UPDATE_TAGS);
        intentFilter.addAction(Constants.ACTION_UPDATE_INSTRUCTION_STATE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);

        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mSelfInfoAdapter != null) {
            mSelfInfoAdapter.onDestroy();
        }
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    private void initView(View view) {
        TextView title = view.findViewById(R.id.tv_self_info_title);
        title.setText(R.string.exp_self_info_title);
        RecyclerView mRvInfoList = view.findViewById(R.id.rv_self_info);
        mRvInfoList.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false));
        mSelfInfoAdapter = new SelfInfoAdapter(mContext, imDdService, new SelfInfoAdapter.OnItemClickListener() {
            @Override
            public void onMobileClick() {
                toUpdatePhoneFragment();
            }

            @Override
            public void onTelephoneClick() {
                toUpdateTelephoneFragment();
            }

            @Override
            public void openPersonalDetails(String erp) {//打开咚咚个人详情页面
                imDdService.showContactDetailInfo(getActivity(), erp);//仿照跳转聊天页面
                JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_personInfo_hr_click,JDMAConstants.mobile_Mine_personInfo_hr_click);
            }
        });
        mRvInfoList.setAdapter(mSelfInfoAdapter);

        View back = view.findViewById(R.id.back);
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().onBackPressed();
            }
        });
    }

    /**
     * 获取个人信息
     */
    private void requestUserData() {
        if (getActivity() == null) {
            return;
        }

        mRepo.getUserInfoData(new LoadDataCallback<UserInfoData>() {
            @Override
            public void onDataLoaded(UserInfoData data) {
                if (data == null) {
                    return;
                }
                if (mSelfInfoAdapter != null) {
                    mSelfInfoAdapter.setData(data);
                }
                addCache(data);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_UPDATE_PHONE: {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    String phone = data.getStringExtra("result.phone");
                    if (mSelfInfoAdapter != null) {
                        mSelfInfoAdapter.updateMobile(phone);
                        addCache(mSelfInfoAdapter.getData());
                    }
                }
                break;
            }
            case REQUEST_UPDATE_TELEPHONE:
                if (resultCode == Activity.RESULT_OK && data != null) {
                    String telephone = data.getStringExtra(ChangeTelephoneFragment.RESULT_TELEPHONE);
                    if (mSelfInfoAdapter != null) {
                        mSelfInfoAdapter.updateTelephone(telephone);
                        addCache(mSelfInfoAdapter.getData());
                    }
                }
                break;
            default:
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mSelfInfoAdapter != null) {
            mSelfInfoAdapter.updatePendant();
        }
        JDMAUtils.onEventPagePV(AppBase.getAppContext(), ExpJDMAConstants.EXP_MAIN_EDIT_INFO, "EXP_Main_EditInfo");
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
        ActionBarHelper.init(this, getView());
        requestUserData();
    }

    //更新手机
    private void toUpdatePhoneFragment() {
        //Router.build(DeepLink.F_PHONE).go(mContext, new RouteNotFoundCallback(mContext));
        Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.liveness.ChangePhoneFragment");
        startActivityForResult(intent, REQUEST_UPDATE_PHONE);
    }

    //更新座机
    private void toUpdateTelephoneFragment() {
        Intent intent = new Intent(getActivity(), ChangeTelephoneActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ChangeTelephoneFragment.class.getName());
        startActivityForResult(intent, REQUEST_UPDATE_TELEPHONE);
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (optionFlag == OPERATE_CHANGE_AVATAE) {
            if (args == null) {
                return false;
            }
            String url = args.getString("url");
            if (TextUtils.isEmpty(url)) {
                return false;
            }

            if (mSelfInfoAdapter != null) {
                mSelfInfoAdapter.updateAvatar(url);
                addCache(mSelfInfoAdapter.getData());
            }
            for (Observer o : observerList) {
                o.update(null, url);
            }
            return true;
        }
        return false;
    }

    //缓存
    private void loadCache() {
        UserInfoData cacheData = null;
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null);
        if (ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            try {
                cacheData = new Gson().fromJson(cache.getResponse(), UserInfoData.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (cacheData == null) {//如果没有cache，使用UserInfo
            cacheData = new UserInfoData();
            cacheData.avatar = PreferenceManager.UserInfo.getUserCover();
            cacheData.realName = PreferenceManager.UserInfo.getUserRealName();
            cacheData.userName = PreferenceManager.UserInfo.getUserName();
            cacheData.email = PreferenceManager.UserInfo.getEmailAddress();
            cacheData.mobile = PreferenceManager.UserInfo.getMobile();
            cacheData.telephone = PreferenceManager.UserInfo.getTelephone();
            cacheData.userCode = PreferenceManager.UserInfo.getUserCode();
            cacheData.organizationFullName = PreferenceManager.UserInfo.getOrganizationName();
        }
        if (mSelfInfoAdapter != null) {
            mSelfInfoAdapter.setData(cacheData);
        }
    }

    private void addCache(UserInfoData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null, new Gson().toJson(data));
    }
}
