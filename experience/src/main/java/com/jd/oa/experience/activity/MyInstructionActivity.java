package com.jd.oa.experience.activity;

import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.NinePatchDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.SwitchCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.widget.NestedScrollView;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.dialog.ExpDialog;
import com.jd.oa.experience.model.InstructionData;
import com.jd.oa.experience.repo.MyInstructionRepo;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.MessageRecord;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.ScreenUtil;

import org.json.JSONObject;

import java.util.HashMap;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

@Route(DeepLink.EXP_MANUAL)
public class MyInstructionActivity extends BaseActivity {

    private static final String CACHE_KEY = "exp.instruction.cache.key";
    private InstructionData.MyInstruction mMyInstruction;
    private SwitchCompat mSettingSwitch;
    private View mMaskView;
    private TextView mMaskViewTitleTv;
    private TextView mTitleTv;
    private final int mMax = DisplayUtils.dip2px(40);
    private final MyInstructionRepo mRepo = new MyInstructionRepo();
    private final Gson mGson = new Gson();
    private boolean mFromChat = false;
    private MessageRecord mMessageRecord;
    private View mSendButton;
    private LoadingDialog mLoadingDialog;

    CompoundButton.OnCheckedChangeListener mListener = new CompoundButton.OnCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            showConfirmDialog(isChecked, mFromChat);
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_my_instruction);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }
        String param = getIntent().getStringExtra("mparam");
        try {
            JSONObject object = new JSONObject(param);
            String origin = object.getString("origin");
            mFromChat = TextUtils.equals(origin, "chat");
            if (mFromChat) {
                mMessageRecord = new Gson().fromJson(object.getString("info"), MessageRecord.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        loadCache();
        initView();
    }

    @Override
    protected void onResume() {
        super.onResume();
        requestData();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public void finish() {
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(Constants.ACTION_UPDATE_INSTRUCTION_STATE));
        super.finish();
    }

    private void initView() {
        View mBack = findViewById(R.id.back);
        mBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        NestedScrollView scrollView = findViewById(R.id.scroll_view);
        scrollView.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                handleOnScrollChange(scrollY);
            }
        });
        mMaskView = findViewById(R.id.mask_view);
        mMaskViewTitleTv = findViewById(R.id.mask_view_title);
        mMaskViewTitleTv.setVisibility(View.INVISIBLE);

        mTitleTv = findViewById(R.id.title_tv);
        TextView tvDesc = findViewById(R.id.title_desc);
        int screenWidth = ScreenUtil.getScreenWidth(this);
        if (screenWidth < DensityUtil.dp2px(this, 370)) {//小屏手机
            mTitleTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 22);
            tvDesc.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
            mTitleTv.setPadding(0, 0, 0, 0);
            tvDesc.setPadding(0, 0, 0, 0);
        }

        View contentDemo = findViewById(R.id.content_demo);
        View contentDemoBtn = findViewById(R.id.content_demo_btn);
        View contentUpdate = findViewById(R.id.content_update);
        View contentSetting = findViewById(R.id.content_setting);
        View contentCreate = findViewById(R.id.content_create);
        View contentSeparator = findViewById(R.id.content_separator);
        View contentLayout = findViewById(R.id.content_layout);

        mSettingSwitch = contentSetting.findViewById(R.id.setting_switch);

        contentDemoBtn.setVisibility(mMyInstruction != null && !TextUtils.isEmpty(mMyInstruction.demoFolderUrl) ? View.VISIBLE : View.INVISIBLE);
        contentDemo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mMyInstruction != null && !TextUtils.isEmpty(mMyInstruction.demoFolderUrl)) {
                    Uri uri = Uri.parse(mMyInstruction.demoFolderUrl);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(MyInstructionActivity.this, new RouteNotFoundCallback(MyInstructionActivity.this));
                }
            }
        });

        boolean isNewCreate = mMyInstruction == null || TextUtils.isEmpty(mMyInstruction.userManualUrl);
        if (isNewCreate) {
            contentUpdate.setVisibility(View.GONE);
            contentSetting.setVisibility(View.GONE);
            contentSeparator.setVisibility(View.GONE);
            contentCreate.setVisibility(View.VISIBLE);
            contentCreate.setBackgroundResource(R.drawable.jdme_bg_instruction_content_bottom);

            TextView tvCreateTitle = contentCreate.findViewById(R.id.content_title);
            TextView tvCreateDesc = contentCreate.findViewById(R.id.content_desc);
            tvCreateTitle.setText(R.string.exp_instruction_create_title);
            tvCreateDesc.setText(R.string.exp_instruction_create_desc);

            View createButton = contentCreate.findViewById(R.id.create_btn);
            View updateButton = contentCreate.findViewById(R.id.update_btn);
            View settingButton = contentCreate.findViewById(R.id.setting_btn);
            createButton.setVisibility(View.VISIBLE);
            updateButton.setVisibility(View.GONE);
            settingButton.setVisibility(View.GONE);
            createButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mRepo.createMyInstruction(new LoadDataCallback<InstructionData.Create>() {
                        @Override
                        public void onDataLoaded(InstructionData.Create create) {
                            if (!TextUtils.isEmpty(create.userManualUrl)) {
                                Uri uri = Uri.parse(create.userManualUrl);
                                if (uri == null || uri.getScheme() == null) {
                                    return;
                                }
                                Router.build(uri).go(MyInstructionActivity.this, new RouteNotFoundCallback(MyInstructionActivity.this));
                            }
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            Toast.makeText(MyInstructionActivity.this, R.string.exp_instruction_api_fail, Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            });
        } else {
            contentUpdate.setVisibility(View.VISIBLE);
            contentSetting.setVisibility(View.VISIBLE);
            contentSeparator.setVisibility(View.VISIBLE);
            contentCreate.setVisibility(View.GONE);
            contentUpdate.setBackgroundResource(R.drawable.jdme_bg_instruction_content_middle);
            contentSetting.setBackgroundResource(R.drawable.jdme_bg_instruction_content_bottom);

            TextView tvUpdateTitle = contentUpdate.findViewById(R.id.content_title);
            TextView tvUpdateDesc = contentUpdate.findViewById(R.id.content_desc);
            tvUpdateTitle.setText(R.string.exp_instruction_update_title);
            tvUpdateDesc.setText(R.string.exp_instruction_update_desc);

            View createButton = contentUpdate.findViewById(R.id.create_btn);
            View updateButton = contentUpdate.findViewById(R.id.update_btn);
            View settingButton = contentUpdate.findViewById(R.id.setting_btn);
            createButton.setVisibility(View.GONE);
            updateButton.setVisibility(View.VISIBLE);
            settingButton.setVisibility(View.GONE);
            updateButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mMyInstruction != null && !TextUtils.isEmpty(mMyInstruction.userManualUrl)) {
                        Uri uri = Uri.parse(mMyInstruction.userManualUrl);
                        if (uri == null || uri.getScheme() == null) {
                            return;
                        }
                        Router.build(uri).go(MyInstructionActivity.this, new RouteNotFoundCallback(MyInstructionActivity.this));
                    }
                }
            });

            TextView tvSettingTitle = contentSetting.findViewById(R.id.content_title);
            TextView tvSettingDesc = contentSetting.findViewById(R.id.content_desc);
            tvSettingTitle.setText(R.string.exp_instruction_setting_title);
            tvSettingDesc.setText(R.string.exp_instruction_setting_desc);

            View createBtn = contentSetting.findViewById(R.id.create_btn);
            View updateBtn = contentSetting.findViewById(R.id.update_btn);
            View settingBtn = contentSetting.findViewById(R.id.setting_btn);
            createBtn.setVisibility(View.GONE);
            updateBtn.setVisibility(View.GONE);
            settingBtn.setVisibility(View.VISIBLE);

            mSettingSwitch.setOnCheckedChangeListener(null);
            mSettingSwitch.setChecked(mMyInstruction != null && mMyInstruction.isShow);
            mSettingSwitch.setOnCheckedChangeListener(mListener);
        }
        //计算真实的marginStart，原因是缺口为普通图片，背景为.9图片，设置同样margin会导致普通图片和.9图片看起来左边距不对齐，.9图片边距不包含阴影
        if (getResources() != null) {
            Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.jdme_bg_instruction_content_top, null);
            if (drawable instanceof NinePatchDrawable) {
                NinePatchDrawable ninePatchDrawable = (NinePatchDrawable)drawable;
                Rect padding = new Rect();
                if (ninePatchDrawable.getPadding(padding)) {
                    int horizontalMargin = getResources().getDimensionPixelSize(R.dimen.instruction_content_margin);//16dp
                    horizontalMargin -= padding.left;
                    int topMargin = getResources().getDimensionPixelSize(R.dimen.instruction_content_margin_top);//180dp
                    topMargin -= padding.top;
                    int bottomMargin = getResources().getDimensionPixelSize(R.dimen.instruction_content_margin_bottom);//36dp
                    if (horizontalMargin >= 0) {
                        LinearLayout.LayoutParams lpDemo = (LinearLayout.LayoutParams)contentDemo.getLayoutParams();
                        lpDemo.setMargins(horizontalMargin, 0, horizontalMargin, 0);
                        contentDemo.setLayoutParams(lpDemo);

                        LinearLayout.LayoutParams lpUpdate = (LinearLayout.LayoutParams)contentUpdate.getLayoutParams();
                        lpUpdate.setMargins(horizontalMargin, 0, horizontalMargin, 0);
                        contentUpdate.setLayoutParams(lpUpdate);

                        LinearLayout.LayoutParams lpSetting = (LinearLayout.LayoutParams)contentSetting.getLayoutParams();
                        lpSetting.setMargins(horizontalMargin, 0, horizontalMargin, 0);
                        contentUpdate.setLayoutParams(lpSetting);

                        LinearLayout.LayoutParams lpCreate = (LinearLayout.LayoutParams)contentCreate.getLayoutParams();
                        lpCreate.setMargins(horizontalMargin, 0, horizontalMargin, 0);
                        contentCreate.setLayoutParams(lpCreate);
                    }

                    if (topMargin >= 0) {
                        FrameLayout.LayoutParams lpContent = (FrameLayout.LayoutParams)contentLayout.getLayoutParams();
                        lpContent.setMargins(0, topMargin, 0, bottomMargin);
                        contentLayout.setLayoutParams(lpContent);
                    }
                }
            }
        }

        //咚咚入口
        boolean showSend = mFromChat && !isNewCreate;
        View contentBottomPadding = findViewById(R.id.content_bottom_padding);
        contentBottomPadding.setVisibility(showSend ? View.VISIBLE : View.GONE);
        View sendLayout = findViewById(R.id.send_layout);
        sendLayout.setVisibility(showSend ? View.VISIBLE : View.GONE);
        mSendButton = findViewById(R.id.send_button);
        mSendButton.setBackgroundResource((mMyInstruction != null && mMyInstruction.isShow) ? R.drawable.jdme_bg_instruction_send_button_enable : R.drawable.jdme_bg_instruction_send_button_disable);
        mSendButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!mFromChat || mMessageRecord == null || !(mMyInstruction != null && mMyInstruction.isShow)) {
                    return;
                }

                String to = "";
                String toApp = "";
                String sessionId = "";
                if (mMessageRecord.getSessionType() == 0 || mMessageRecord.getSessionType() == 2) {//单聊，文件助手
                    to = mMessageRecord.getTo();
                    toApp = mMessageRecord.getToApp();
                } else if (mMessageRecord.getSessionType() == 1) {//群聊
                    sessionId = mMessageRecord.getSessionId();
                } else {
                    return;
                }
                MELogUtil.localI("MyInstructionActivity", "mMessageRecord = " + mMessageRecord.toString());
                MELogUtil.onlineI("MyInstructionActivity", "mMessageRecord = " + mMessageRecord.toString());
                mRepo.shareMyInstruction(to, toApp, sessionId, new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        if (mLoadingDialog != null) {
                            mLoadingDialog.dismiss();
                            mLoadingDialog = null;
                        }
                        finish();
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        if (mLoadingDialog != null) {
                            mLoadingDialog.dismiss();
                            mLoadingDialog = null;
                        }
                        Toast.makeText(MyInstructionActivity.this, R.string.exp_share_fail, Toast.LENGTH_SHORT).show();
                    }
                });
                if (mLoadingDialog == null) {
                    mLoadingDialog = new LoadingDialog(MyInstructionActivity.this);
                }
                mLoadingDialog.show();

                ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_SEND_MANUAL, new HashMap<String, String>());
            }
        });

    }

    private void requestData() {
        mRepo.getMyInstruction(new LoadDataCallback<InstructionData.MyInstruction>() {
            @Override
            public void onDataLoaded(InstructionData.MyInstruction data) {
                if (data == null) {
                    return;
                }
                mMyInstruction = data;
                initView();
                addCache(data);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Log.e(TAG, "onDataNotAvailable: " + s);
            }
        });
    }

    private void loadCache() {
        InstructionData.MyInstruction cacheData = getCache();
        if (cacheData != null) {
            mMyInstruction = cacheData;
        }
    }

    private InstructionData.MyInstruction getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        InstructionData.MyInstruction data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<InstructionData.MyInstruction>() {}.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    private void addCache(InstructionData.MyInstruction data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null, mGson.toJson(data));
    }

    private void showConfirmDialog(final boolean isChecked, boolean isShare) {
        int contentResId;
        int confirmResId;
        if (isShare) {
            contentResId = isChecked ? R.string.exp_instruction_setting_hint_share_enable : R.string.exp_instruction_setting_hint_share_disable;
        } else {
            contentResId = isChecked ? R.string.exp_instruction_setting_hint_enable : R.string.exp_instruction_setting_hint_disable;
        }
        confirmResId = isChecked ? R.string.exp_instruction_dialog_button_enable : R.string.exp_instruction_dialog_button_disable;
        ExpDialog dialog = new ExpDialog(this, contentResId, confirmResId);
        dialog.setOnClickListener(new ExpDialog.OnClickListener() {
            @Override
            public void onClickOk() {
                mRepo.setInstructionShow(isChecked, new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        mMyInstruction.isShow = isChecked;
                        mSendButton.setBackgroundResource(mMyInstruction.isShow ? R.drawable.jdme_bg_instruction_send_button_enable : R.drawable.jdme_bg_instruction_send_button_disable);
                        addCache(mMyInstruction);
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        mSettingSwitch.setOnCheckedChangeListener(null);
                        mSettingSwitch.setChecked(mMyInstruction.isShow);//开关复原
                        mSettingSwitch.setOnCheckedChangeListener(mListener);
                        Toast.makeText(MyInstructionActivity.this, R.string.exp_instruction_api_fail, Toast.LENGTH_SHORT).show();
                    }
                });
            }

            @Override
            public void onClickCancel() {
                if (mSettingSwitch != null) {
                    mSettingSwitch.setOnCheckedChangeListener(null);
                    mSettingSwitch.setChecked(mMyInstruction.isShow);//防止开关复原时弹出对话框
                    mSettingSwitch.setOnCheckedChangeListener(mListener);
                }
            }
        });
        dialog.show();
    }

    private void handleOnScrollChange(int scrollY) {
        if (scrollY < mMax) {//放缩中
            int alpha = scrollY * 255 / mMax;
            alpha = alpha << 24;
            mMaskView.setBackgroundColor(alpha | 0x00FFFFFF);
            mMaskViewTitleTv.setVisibility(View.INVISIBLE);
        } else {//收起状态
            mMaskView.setBackgroundColor(0xFFFFFFFF);
            mMaskViewTitleTv.setVisibility(View.VISIBLE);
        }
    }
}
