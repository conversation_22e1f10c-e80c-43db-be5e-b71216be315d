package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.BannerData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class BannerRepo {

    private static final String BANNER_CACHE_KEY = "exp.banner.repo.cache.key";
    private static BannerRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static BannerRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new BannerRepo(context);
        }
        return sInstance;
    }

    private BannerRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public BannerData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), BANNER_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        BannerData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<BannerData>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(BannerData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), BANNER_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getBanner(final LoadDataCallback<BannerData> callback) {
        Log.i("zhn", "getBanner");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<BannerData> response = ApiResponse.parse(info.result, new TypeToken<BannerData>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getBanner, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getBanner, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getBanner, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getBanner");
        //http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getBanner
    }
}