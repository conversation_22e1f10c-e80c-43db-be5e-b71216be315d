package com.jd.oa.experience.model;

import java.util.ArrayList;

public class StudyMapData {
    public String title;
    public String icon;
    public String jumpText;
    public String jumpUrl;
    public ArrayList<StudyMapData.CourseType> courseTypeList;

    public static class CourseType {
        public String courseTypeName;
        public Integer courseTypeCode;
        public String mapName;
        public String moreUrl;
        public ArrayList<Object> items;
    }

    public static class CourseGroup {
        public String courseGroupCode;
        public String courseGroupName;
        public Integer courseGroupType;
        public ArrayList<StudyMapData.Course> courseList;
    }

    public static class Course {
        public String courseId;
        public String courseName;
        public String courseUrl;
        public Boolean isNew;
        public String levelName;
        public String source;
        public Integer completeStatus;
        public String studyDuration;
        public String imageUrl;
    }
}
