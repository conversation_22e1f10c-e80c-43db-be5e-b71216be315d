package com.jd.oa.experience.model;

import android.graphics.Color;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class BannerData {
    //动态内容 副标题
    public String dynamicContent;
    //动态内容前链接
    public String dynamicImg;
    //图标
    public String icon;
    //右上角插图链接
    public String illustration;
    public String img;
    //整体卡片跳转链接
    public String jumpUrl;
    //主标题
    public String title;
    // banner 的背景
    public BannerBg bgImg;
    // 文字颜色。
    public String imageType;

    public ArrayList<BannerData.BannerList> bannerList;

    public int getTextColorInt() {
        if (!isDarkModel()) {
            return Color.parseColor("#333333");
        } else {
            return Color.parseColor("#FFE6E6E6");
        }
    }

    public boolean isDarkModel() {
        return !Objects.equals(imageType, "02");
    }

    public static class BannerList {
        public List<Banner> banners;
        public List<TextBanner> texts;
    }

    public static class TextBanner {
        public String content;
        public String endColor;
        public String fromColor;
        public String img;
        public String title;
        public String jumpUrl;
    }

    public static class Banner {
        public String img;
        public String jumpUrl;
        public String text;
    }

    public static class BannerBg {
        public String img;
        public String leftImg;
        public String rightImg;
        public String fromColor;
        public String endColor;
    }
}
