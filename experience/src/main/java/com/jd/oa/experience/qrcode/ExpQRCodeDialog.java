package com.jd.oa.experience.qrcode;

import static android.view.animation.Animation.RESTART;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.jd.oa.experience.R;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.qrcode.QRCodeUtil;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.DisplayUtil;

/**
 * 用户二维码弹窗
 * Created by peidongbiao on 2018/7/26.
 */

public class ExpQRCodeDialog extends BottomSheetDialog implements CardConstraint.ICardView {
    private ImageView mIvAvatar;
    private TextView mTvName;
    private TextView mTvJob;
    private ImageView mIvQrcode;
    private LinearLayout llLoadStatus;
    private ImageView ivLoad;
    private TextView tvLoadStaus;
    private Bitmap qrBitmap;

    private Long millisInFuture = 0L;
    private CountDownTimer timer = new CountDownTimer(10000, 1000) {
        @Override
        public void onTick(long millisUntilFinished) {

        }

        @Override
        public void onFinish() {
            if (isShowing() && mPresenter != null) {
                mPresenter.getData(true);
                timer.start();
            }
        }
    };

    private CardConstraint.ICardPresenter mPresenter;

    public ExpQRCodeDialog(@NonNull Context context) {
        this(context, 0);
    }

    public ExpQRCodeDialog(@NonNull Context context, int theme) {
        super(context, theme);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_dialog_user_qrcode_exp, null);
        setContentView(view);
        //设置背景
        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            parent.setBackgroundResource(android.R.color.transparent);
        }
        //设置初始高度
        View bottomSheet = findViewById(R.id.design_bottom_sheet);
        if (bottomSheet != null) {
            BottomSheetBehavior behavior = BottomSheetBehavior.from(bottomSheet);
            behavior.setPeekHeight(DisplayUtil.getScreenHeight(context));
        }
        mIvAvatar = findViewById(R.id.iv_avatar);
        mTvName = findViewById(R.id.tv_name);
        mTvJob = findViewById(R.id.tv_job);
        mIvQrcode = findViewById(R.id.iv_qrcode);
        llLoadStatus = findViewById(R.id.ll_load_status);
        ivLoad = findViewById(R.id.iv_load);
        tvLoadStaus = findViewById(R.id.tv_load_status);
//        View vTop = findViewById(R.id.v_top);
        ImageView vTop = findViewById(R.id.v_top);
        LinearLayout mLayoutContainer = findViewById(R.id.layout_container);

        Glide.with(context).load(R.drawable.jdme_exp_bg_qrcode).into(vTop);
        if (mLayoutContainer != null) {
            ViewGroup.LayoutParams layoutParams = mLayoutContainer.getLayoutParams();
            layoutParams.height = (int) (DisplayUtil.getScreenHeight(context) * 0.7f);
            mLayoutContainer.setLayoutParams(layoutParams);
        }


        mPresenter = new CardPresenter(this);
        mPresenter.getData(true);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
    }

    @Override
    public void onStart() {
        tvLoadStaus.setText(R.string.exp_qrcode_loading);
        ivLoad.setImageResource(R.drawable.profile_section_qrcode_loading);
        RotateAnimation rotateAnimation = new RotateAnimation(0, 360, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        rotateAnimation.setDuration(1000);
        LinearInterpolator lin = new LinearInterpolator();
        rotateAnimation.setInterpolator(lin);
        rotateAnimation.setRepeatCount(-1);
        rotateAnimation.setRepeatMode(RESTART);
        ivLoad.startAnimation(rotateAnimation);
        llLoadStatus.setEnabled(false);
//        Glide.with(getContext()).load(R.drawable.jdme_exp_qrcode_defult).into(mIvQrcode);
        llLoadStatus.setVisibility(View.VISIBLE);
    }

    @Override
    public void onEnd(CardInfo qt, String errorMsg, boolean success) {
        if (!success) {
            tvLoadStaus.setText(R.string.exp_qrcode_load_failure);
            ivLoad.clearAnimation();
            ivLoad.setImageResource(R.drawable.profile_section_qrcode_refresh);
            llLoadStatus.setVisibility(View.VISIBLE);
            llLoadStatus.setEnabled(true);
            llLoadStatus.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    timer.cancel();
                    timer.start();
                    mPresenter.getData(true);
                }
            });
            if (getContext() != null) {
                Glide.with(getContext()).load(R.drawable.jdme_exp_qrcode_defult).into(mIvQrcode);
            }
        } else {
            llLoadStatus.setVisibility(View.GONE);
        }
        if (!isShowing() || !success) return;
        //Glide.with(getContext()).load(qt.getHeadImg()).placeholder(R.drawable.jdme_picture_user_default_white).into(mIvAvatar);
        ImageLoaderUtils.getInstance().displayImage(PreferenceManager.UserInfo.getUserCover(), mIvAvatar, R.drawable.jdme_picture_user_default_white);
        mTvName.setText(qt.getRealName());
        mTvJob.setText(qt.getPositionName());
        qrBitmap = QRCodeUtil.createQRCode(qt.getVisitingCardQRCode(), 300, 300);
        mIvQrcode.setImageBitmap(qrBitmap);

        if (millisInFuture == 0 && qt.getStep() >= 5) {
            millisInFuture = qt.getStep() * 1000;
            timerStart(millisInFuture);
        }
    }

    private void timerStart(long time) {
        if (timer != null) {
            timer.cancel();
        }
        timer = new CountDownTimer(time, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {

            }

            @Override
            public void onFinish() {
                if (isShowing() && mPresenter != null) {
                    mPresenter.getData(true);
                    timer.start();
                }
            }
        };
        timer.start();
    }

    @Override
    protected void onStop() {
        super.onStop();
        ivLoad.clearAnimation();
        if (qrBitmap != null) {
            qrBitmap.recycle();
        }
        if (timer != null) {
            timer.cancel();
        }
        if (mPresenter != null) {
            mPresenter.onDestory();
        }
    }
}
