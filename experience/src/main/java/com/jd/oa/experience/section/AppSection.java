package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.ExpAppsAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.ExpAppData;
import com.jd.oa.experience.model.GlobalData;
import com.jd.oa.experience.repo.ExpAppRepo;
import com.jd.oa.experience.repo.LoadDataStatusCallBack;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ScreenUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

@Deprecated
public class AppSection extends StatelessSection implements Destroyable {

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private AppSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private ExpAppRepo mRepo;
    private List<ExpAppData.Apps> mList;
    private ExpAppsAdapter adapter;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    if (mItemViewHolder != null) {
                        adapter = new ExpAppsAdapter(mContext, mList);
                        adapter.setOnItemClickListener(onItemClickListener);
                        mItemViewHolder.rvApps.setAdapter(adapter);
                        List<ExpAppData.Apps> cache = mRepo.getCache();
                        if (mRepo.getCache() != null) {
                            mItemViewHolder.sbScrollBar.setVisibility(showSeekBar(mList.size()) ? View.VISIBLE : View.GONE);
                            showData(cache, false);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private final ExpAppsAdapter.OnItemClickListener onItemClickListener = new ExpAppsAdapter.OnItemClickListener() {
        @Override
        public void onItemClick(ExpAppData.Apps app) {
            if (TextUtils.isEmpty(app.getUrl())) {
                return;
            }
            Map<String, String> map = new HashMap<>();
            map.put("appId", app.getId());
            ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_ENTRY, map);

            Router.build(app.getUrl()).go(mContext, new RouteNotFoundCallback(mContext));
        }
    };

    public AppSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_app)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        mRepo = ExpAppRepo.get(mContext);
        mList = new ArrayList<>();
        this.adapter = new ExpAppsAdapter(mContext, mList);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_EXP_APP_ON_PAUSE);
        intentFilter.addAction(Constants.ACTION_EXP_APP_ON_RESUME);
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        List<ExpAppData.Apps> cache = mRepo.getCache();
        if (cache != null) {
            showData(cache, false);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new AppSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/

        final AppSection.ItemViewHolder holder = (AppSection.ItemViewHolder) viewHolder;
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false);
        holder.rvApps.setLayoutManager(layoutManager);
        adapter.setOnItemClickListener(onItemClickListener);
        holder.rvApps.setAdapter(adapter);
        holder.rvApps.setNestedScrollingEnabled(false);
        holder.rvApps.setItemViewCacheSize(5);
        if (holder.rvApps.getItemAnimator() != null)
            ((SimpleItemAnimator) holder.rvApps.getItemAnimator()).setSupportsChangeAnimations(false);
        holder.itemView.setBackgroundResource(mList.size() > 2 ? R.drawable.jdme_bg_shadow_app_corner : R.drawable.jdme_bg_shadow_app_transparent);
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) holder.rvApps.getLayoutParams();
        int margin = mList.size() > 2 ? DensityUtil.dp2px(mContext, 5) : 0;
        lp.setMargins(margin, margin, margin, margin);
        holder.sbScrollBar.setVisibility(showSeekBar(mList.size()) ? View.VISIBLE : View.GONE);
        holder.sbScrollBar.setPadding(0, 0, 0, 0);
        holder.sbScrollBar.setThumbOffset(0);
        holder.rvApps.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                handleScrolled(dx);
            }
        });
    }

    private void handleScrolled(int dx) {
        if (mItemViewHolder == null) {
            return;
        }
        //显示区域的高度。
        int extent = mItemViewHolder.rvApps.computeHorizontalScrollExtent();
        //整体的高度，注意是整体，包括在显示区域之外的。
        int range = mItemViewHolder.rvApps.computeHorizontalScrollRange();
        //已经向下滚动的距离，为0时表示已处于顶部。
        int offset = mItemViewHolder.rvApps.computeHorizontalScrollOffset();
//                Log.i("dx------", range + "****" + extent + "****" + offset);
        //此处获取seekbar的getThumb，就是可以滑动的小的滚动游标
        GradientDrawable gradientDrawable = (GradientDrawable) mItemViewHolder.sbScrollBar.getThumb();
        //根据列表的个数，动态设置游标的大小，设置游标的时候，progress进度的颜色设置为和seekbar的颜色设置的一样的，所以就不显示进度了。
        gradientDrawable.setSize(mItemViewHolder.sbScrollBar.getMeasuredWidth() * extent / range, DensityUtil.dp2px(mContext, 3));
        //设置可滚动区域
        mItemViewHolder.sbScrollBar.setMax((range - extent));
        if (offset == 0) {
            mItemViewHolder.sbScrollBar.setProgress(0);
        } else if (dx > 0) {
//                    Log.i("dx------", "右滑");
            mItemViewHolder.sbScrollBar.setProgress(offset);
        } else if (dx < 0) {
//                    Log.i("dx------", "左滑");
            mItemViewHolder.sbScrollBar.setProgress(offset);

        }
    }

    public void loadData() {
        mRepo.getAppData(new LoadDataStatusCallBack<List<ExpAppData.Apps>>() {
            @Override
            public void onDataLoaded(List<ExpAppData.Apps> apps, boolean isDataChanged) {
                if (mItemViewHolder != null) {
                    mItemViewHolder.sbScrollBar.setVisibility(showSeekBar(apps.size()) ? View.VISIBLE : View.GONE);
                    mItemViewHolder.itemView.setBackgroundResource(apps.size() > 2 ? R.drawable.jdme_bg_shadow_app_corner : R.drawable.jdme_bg_shadow_app_transparent);
                    LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) mItemViewHolder.rvApps.getLayoutParams();
                    int margin = apps.size() > 2 ? DensityUtil.dp2px(mContext, 5) : 0;
                    lp.setMargins(margin, margin, margin, margin);
                }
                showData(apps, isDataChanged);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(List<ExpAppData.Apps> apps, boolean isDataChanged) {
        if (mItemViewHolder != null && apps != null && !apps.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
            mItemViewHolder.itemView.setLayoutParams(lp);
            ExpTabFragment.notifySectionVisible(mContext, mTabCode);
        }

        if (apps.size() > 0) {
            mList.clear();
            //最多展示8个
            if (apps.size() > 8) {
                mList.addAll(apps.subList(0, 8));
            } else {
                mList.addAll(apps);
            }
            //如果只有1个，补位
            if (mList.size() == 1) {
                ExpAppData.Apps app = new ExpAppData.Apps();
                app.setPlaceHolder(true);
                mList.add(app);
            }
            if (adapter != null)
                adapter.notifyDataSetChanged();
            if (isDataChanged && mList.size() > 4)
                handleScrolled(0);
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private class ItemViewHolder extends RecyclerView.ViewHolder {

        public RecyclerView rvApps;
        public SeekBar sbScrollBar;

        public ItemViewHolder(View itemView) {
            super(itemView);
            rvApps = itemView.findViewById(R.id.rv_apps);
            sbScrollBar = itemView.findViewById(R.id.sb_scroll_bar);
        }
    }

    private boolean showSeekBar(int size) {
        if (size > 3) {
            int maxWidth = mContext.getResources().getDimensionPixelSize(R.dimen.apps_item_width) * size;
            maxWidth += DensityUtil.dp2px(mContext, 18);//边距，这里取10，20平板上有临界问题
            maxWidth += DensityUtil.dp2px(mContext, 4) * (size - 1);//间距
            return maxWidth > ScreenUtil.getScreenWidth(mContext) - DensityUtil.dp2px(mContext, 16);
        }
        return false;
    }
}