package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.WelfareV2Data;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class WelfareV2Repo {
    private static final String WELFARE_CACHE_KEY = "exp.welfare.repo.cache.key";
    private static WelfareV2Repo sInstance;

    private Context mContext;
    private Gson mGson;

    public static WelfareV2Repo get(Context context) {
        if (sInstance == null) {
            sInstance = new WelfareV2Repo(context);
        }
        return sInstance;
    }

    private WelfareV2Repo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public WelfareV2Data getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), WELFARE_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        WelfareV2Data data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<WelfareV2Data>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(WelfareV2Data data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), WELFARE_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getWelfareV2Data(final LoadDataCallback<WelfareV2Data> callback) {
        Log.i("zhn", "getWelfareV2Data");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<WelfareV2Data> response = ApiResponse.parse(info.result, new TypeToken<WelfareV2Data>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getWelfareData2, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getWelfareData2, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getWelfareData2, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getWelfareData");
        //http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getWelfareData
    }
}
/*
{
	"errorCode": "0",
	"content": {
		"message": "恭喜获得了1项新的福利",
		"icon": "https://storage.360buyimg.com/jd.jme.client/images/insightfuli.png",
		"count": 25,
		"title": "我的专属福利",
		"jumpText": "",
		"items": [{
			"unit": "积分",
			"id": "1111111",
			"title": "福利积分",
			"value": "618",
			"url": ""
		}, {
			"unit": "小时",
			"id": "2222222",
			"title": "我的假期",
			"value": "24",
			"url": ""
		}, {
			"unit": "张",
			"id": "3333333",
			"title": "福利券",
			"value": "18",
			"url": ""
		}, {
			"unit": "可申请",
			"id": "4444444",
			"title": "安居计划",
			"value": "",
			"url": ""
		}, {
			"unit": "可申请",
			"id": "5555555",
			"title": "福利房",
			"value": "",
			"url": ""
		}, {
			"unit": "截止",
			"id": "6666666",
			"title": "健康体检",
			"value": "12/31",
			"url": ""
		}],
		"jumpUrl": "jdme://jm/biz/appcenter/202208291366?mparam=%7B%22url%22%3A%22https%3A%2F%2Fjdme-welfare-pro.pf.jd.com%2Fall%3FshowType%3D1%22%2C%22isNativeHead%22%3A%220%22%2C%22isHideShareButton%22%3A%220%22%2C%22SafeArea%22%3A%220%22%7D"
	},
	"errorMsg": ""
}
* */
