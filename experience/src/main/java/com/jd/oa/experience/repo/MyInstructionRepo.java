package com.jd.oa.experience.repo;

import android.text.TextUtils;

import com.jd.oa.experience.model.InstructionData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MyInstructionRepo {

    public MyInstructionRepo() {
    }

    public void getMyInstruction(final LoadDataCallback<InstructionData.MyInstruction> callback) {
        Map<String, Object> params = new HashMap<>();

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<InstructionData.MyInstruction> response = ApiResponse.parse(info.result, InstructionData.MyInstruction.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.manual.get");
    }
    /*
    {
    "content":{
        "demoList":"",
        "manualUrl":"jdme://rn/201909020601?routeTag=document_edit&rnStandalone=2&page_id=J4302HhMOSmAF7fTsFHh",
        "isShow":false
    },
    "errorMsg":"",
    "errorCode":"0"
    }
    * */

    public void createMyInstruction(final LoadDataCallback<InstructionData.Create> callback) {
        Map<String, Object> params = new HashMap<>();

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<InstructionData.Create> response = ApiResponse.parse(info.result, InstructionData.Create.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.manual.create");
    }
    /*
    {
    "content":{
        "manualUrl":"jdme://rn/201909020601?routeTag=document_edit&rnStandalone=2&page_id=J4302HhMOSmAF7fTsFHh",
        "isShow":false
    },
    "errorMsg":"",
    "errorCode":"0"
    }
    * */


    public void setInstructionShow(boolean show, final LoadDataCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("isShow", show);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.manual.setting");
    }
    /*
{
    "content":{},
    "errorMsg":"",
    "errorCode":"0"
}
    * */

    public void shareMyInstruction(String erp, String appId, String sessionId, final LoadDataCallback<String> callback) {
        ArrayList<Map<String, String>> users = new ArrayList<>();
        ArrayList<String> groupIds = new ArrayList<>();

        //单聊
        if (!TextUtils.isEmpty(erp) && !TextUtils.isEmpty(appId)) {
            Map<String, String> user = new HashMap<>();
            user.put("emplAccount", erp);
            user.put("ddAppId", appId);
            users.add(user);
        }

        //群聊
        if (!TextUtils.isEmpty(sessionId)) {
            groupIds.add(sessionId);
        }

        Map<String, Object> params = new HashMap<>();
        params.put("users", users);
        params.put("groupIds", groupIds);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.manual.share");
    }
    /*
    {
        "users": [{
        "emplAccount": "yunjie",
        "ddAppId": "ee"
        }],
        "groupIds": ["11111", "11111"]
    }
    */

    public void getCardDetail(String cardId, String viewedUserName, final LoadDataCallback<InstructionData.CardDetail> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("cardId", cardId);
        params.put("viewedUserName", viewedUserName);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<InstructionData.CardDetail> response = ApiResponse.parse(info.result, InstructionData.CardDetail.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, "exp.manual.card.detail");
    }

    public void saveCard(List<InstructionData.CardContent> data, final LoadDataCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("data", data);

        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), Integer.valueOf(response.getErrorCode(), 10));
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 1);
            }
        }, "exp.manual.card.save");
    }
/*
{
	"data": [{
		"questionId": "",
		"answerText": "",
		"answerKeyword": ["kw1", "kw2"]
	}]
}
* */
}
