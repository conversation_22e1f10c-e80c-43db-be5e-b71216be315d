package com.jd.oa.experience.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.HRAppsData;

import java.util.List;

public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder> {

    private final List<HRAppsData.Category> mData;

    public CategoryAdapter(List<HRAppsData.Category> data) {
        mData = data;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.jdme_item_hrapps_group_card, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        if (mData != null) {
            HRAppsData.Category category = mData.get(position);
            holder.bind(category);
        }
    }

    @Override
    public int getItemCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }

    public static class CategoryViewHolder extends RecyclerView.ViewHolder {

        private final TextView tvCategoryName;
        private final RecyclerView rvIcons;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategoryName = itemView.findViewById(R.id.tv_group_title);
            rvIcons = itemView.findViewById(R.id.rv_group_items);
        }

        public void bind(HRAppsData.Category category) {
            if (category != null) {
                tvCategoryName.setText(category.title);
                // 设置内层 RecyclerView（每行 4 个 Icon）
                rvIcons.setLayoutManager(new GridLayoutManager(itemView.getContext(), 4));
                String eventId = category.eventId == null ? "" : category.eventId;
                rvIcons.setAdapter(new AppIconAdapter(category.items, eventId));
            }
        }
    }
}
