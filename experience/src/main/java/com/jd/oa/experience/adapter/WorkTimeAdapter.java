package com.jd.oa.experience.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.WorkTimeData;
import com.jd.oa.ui.CircleImageView;

import java.util.List;

public class WorkTimeAdapter extends RecyclerView.Adapter<WorkTimeAdapter.ViewHolder> {
    private Context mContext;
    private List<WorkTimeData.Data> mDataList;
    private int[] mColors;

    public WorkTimeAdapter(Context mContext, List<WorkTimeData.Data> mDataList, int[] colors) {
        this.mContext = mContext;
        this.mDataList = mDataList;
        this.mColors = colors;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = View.inflate(mContext, R.layout.jdme_item_experience_work_time_list, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int i) {
        try {
            WorkTimeData.Data data = mDataList.get(i);
            holder.tvType.setText(!TextUtils.isEmpty(data.getName())?data.getName():data.getKey());
            Drawable drawableColor;
            if (data.getColor() != null) {
                drawableColor = new ColorDrawable(Color.parseColor(data.getColor()));
            } else {
                drawableColor = new ColorDrawable(mColors[i % mDataList.size()]);
            }
            holder.ivRound.setImageDrawable(drawableColor);
            String value = data.getValue();
            if (value.trim().endsWith("%")) {
                value = value.replaceAll("%", "");
            }
            if (!TextUtils.isEmpty(value)) holder.tvPercent.setVisibility(View.VISIBLE);
//            Typeface tf = Typeface.createFromAsset(mContext.getAssets(), "font/DINCondensed.TTF");
//            holder.tvNum.setTypeface(tf, Typeface.BOLD);
            holder.tvNum.setText(value);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public int getItemCount() {
        return mDataList == null ? 0 : mDataList.size();
    }


    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvType;
        TextView tvNum;
        CircleImageView ivRound;
        TextView tvPercent;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvType = itemView.findViewById(R.id.tv_type);
            tvNum = itemView.findViewById(R.id.tv_num);
            ivRound = itemView.findViewById(R.id.iv);
            tvPercent = itemView.findViewById(R.id.tv_percent);
        }
    }
}
