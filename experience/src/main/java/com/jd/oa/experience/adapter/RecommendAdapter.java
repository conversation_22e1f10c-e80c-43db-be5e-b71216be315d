package com.jd.oa.experience.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.gif.GifDrawable;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.RecommendData;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.banner.BannerImageLoader;
import com.jd.oa.experience.view.banner.BannerPageTransformer;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.ui.WrapContentViewPager;
import com.jd.oa.ui.banner.Banner;
import com.jd.oa.ui.banner.BannerConfig;
import com.jd.oa.ui.banner.OnBannerListener;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.viewpager.indicator.CirclePageIndicator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RecommendAdapter extends RecyclerView.Adapter<RecommendAdapter.ViewHolder> {
    public static final int TYPE_IMAGE = 1;
    public static final int TYPE_LIST = 2;
    public static final int TYPE_SUMMARY = 3;
    public static final int TYPE_BANNER = 4;//之前作为成长发展展位图，目前废弃
    public static final int TYPE_IMAGE_LIST = 5;
    public static final int TYPE_SCROLL_LIST = 6;//成长发展入口

    private final List<RecommendData> mData;
    private final Context mContext;
    private final int itemWidth;//占一列
    private final int doubleItemWidth;//独占两列
    private final List<ViewHolder> bannerHolders;

    public RecommendAdapter(Context context, List<RecommendData> data) {
        mContext = context;
        mData = data;
        bannerHolders = new ArrayList<>();
        doubleItemWidth = ScreenUtil.getScreenWidth(mContext) - DensityUtil.dp2px(mContext, 8);
        itemWidth = doubleItemWidth / 2;
    }

    private int getItemLayoutId(int viewType) {
        switch (viewType) {
            case TYPE_IMAGE:
                return R.layout.jdme_me_recommend_type_image_layout;
            case TYPE_LIST:
                return R.layout.jdme_me_recommend_type_list_layout;
            case TYPE_SUMMARY:
                return R.layout.jdme_me_recommend_type_summary_layout;
            case TYPE_BANNER:
                return R.layout.jdme_me_recommend_type_banner_layout;
            case TYPE_IMAGE_LIST:
                return R.layout.jdme_me_recommend_type_image_list_layout;
            case TYPE_SCROLL_LIST:
                return R.layout.jdme_me_recommend_type_card_list_layout;
            default:
                return 0;
        }
    }

    /**
     * 底部按钮区域
     *
     * @param holder holder
     * @param item   data
     */
    private boolean loadFooter(ViewHolder holder, RecommendData item, int position) {
        if (TYPE_BANNER == holder.getItemViewType() || TYPE_SCROLL_LIST == holder.getItemViewType()) {
            return false;
        }
        if (item.data.footer.size() == 1) {//一个按钮
            holder.fl_footer.setVisibility(View.VISIBLE);
            holder.btn_right.setVisibility(View.GONE);
            holder.btn_left.setOnClickListener(getFooterClickListener(item, position, 0));
            holder.btn_left.setText(item.data.footer.get(0).title);
            return true;
        } else if (item.data.footer.size() == 2) {//两个按钮
            holder.fl_footer.setVisibility(View.VISIBLE);
            holder.btn_right.setVisibility(View.VISIBLE);
            holder.btn_left.setOnClickListener(getFooterClickListener(item, position, 0));
            holder.btn_right.setOnClickListener(getFooterClickListener(item, position, 1));
            holder.btn_left.setText(item.data.footer.get(0).title);
            holder.btn_right.setText(item.data.footer.get(1).title);
            return true;
        } else {//无按钮
            holder.fl_footer.setVisibility(View.GONE);
            return false;
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        int itemLayoutId = getItemLayoutId(viewType);
        View view = LayoutInflater.from(parent.getContext()).inflate(itemLayoutId, parent, false);
        return new ViewHolder(view, viewType);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        if (mData == null || position >= mData.size()) {
            return;
        }
        try {
            ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
            if (TYPE_BANNER == holder.getItemViewType() || TYPE_SCROLL_LIST == holder.getItemViewType()) {
                layoutParams.width = doubleItemWidth;
            } else {
                layoutParams.width = itemWidth;
            }
            holder.itemView.setLayoutParams(layoutParams);
            Logger.d("RecommendAdapter", "width: " + holder.itemView.getWidth() + ",height: " + holder.itemView.getHeight());
            final RecommendData item = mData.get(position);
            if (item == null || item.data == null) {
                return;
            }
            HashMap<String, String> jdmaParams = new HashMap<>();
            jdmaParams.put("cardId", item.id);
            jdmaParams.put("tplName", item.tplType);
            jdmaParams.put("cardIndex", String.valueOf(position));
            loadHeader(holder, item, position, jdmaParams);
            boolean hasFooter = loadFooter(holder, item, position);
            switch (holder.getItemViewType()) {
                case TYPE_IMAGE:
                    Glide.with(mContext)
                            .load(item.data.body.image)
                            .placeholder(R.drawable.jdme_exp_recommend_image_holder)
                            .into(new SimpleTarget<Drawable>() {
                                @Override
                                public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                                    if (resource instanceof GifDrawable) {
                                        holder.iv_content.setVisibility(View.GONE);
                                        holder.iv_gif_content.setVisibility(View.VISIBLE);
                                        GifDrawable gifDrawable = (GifDrawable)resource;
                                        holder.iv_gif_content.setImageDrawable(gifDrawable);
                                        gifDrawable.setLoopCount(0);
                                        gifDrawable.start();
                                    } else {
                                        holder.iv_content.setVisibility(View.VISIBLE);
                                        holder.iv_gif_content.setVisibility(View.GONE);
                                        holder.iv_content.setImageDrawable(resource);
                                    }
                                }
                            });
                    holder.iv_content_layout.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (StringUtils.isNotEmptyWithTrim(item.data.body.url) && !NClick.isFastDoubleClick()) {
                                ExpJDMAUtil.onInsightsEventClick(item.id, item.tplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_BODY.KeyValue, item.data.body.id, "", String.valueOf(position), item.data.body.id);
                                Router.build(item.data.body.url).go(mContext, new RouteNotFoundCallback(mContext));
                            }
                        }
                    });
                    break;
                case TYPE_LIST:
                    List<List<RecommendData.BodyItem>> apps = splitList(item.data.body.items, hasFooter ? 3 : 4);
                    RecommendAppPagerAdapter pagerAdapter = new RecommendAppPagerAdapter(mContext, apps, hasFooter, item, position);
                    holder.vp_app.setAdapter(pagerAdapter);
                    holder.vp_app.setOffscreenPageLimit(apps.size() - 1);
                    holder.appIndicator.setVisibility(apps.size() > 1 ? View.VISIBLE : View.INVISIBLE);
                    if (apps.size() > 1 && View.VISIBLE == holder.appIndicator.getVisibility()) {
                        holder.appIndicator.setViewPager(holder.vp_app);
                        holder.appIndicator.notifyDataSetChanged();
                    }
                    break;
                case TYPE_SUMMARY:
                    List<List<RecommendData.BodyItem>> summaries = splitList(item.data.body.items, 2);
                    RecommendSummaryPagerAdapter summaryAdapter = new RecommendSummaryPagerAdapter(mContext, summaries, hasFooter, item, position);
                    holder.vp_summary.setAdapter(summaryAdapter);
                    holder.vp_summary.setOffscreenPageLimit(summaries.size() - 1);
                    holder.summaryIndicator.setVisibility(summaries.size() > 1 ? View.VISIBLE : View.INVISIBLE);
                    if (summaries.size() > 1 && View.VISIBLE == holder.summaryIndicator.getVisibility()) {
                        holder.summaryIndicator.setViewPager(holder.vp_summary);
                        holder.summaryIndicator.notifyDataSetChanged();
                    }
                    final int resId = hasFooter ? R.drawable.jdme_exp_recommond_summary_has_footer : R.drawable.jdme_exp_recommond_summary_no_footer;
                    Glide.with(mContext).load(item.data.body.image).error(resId).into(new SimpleTarget<Drawable>() {
                        @Override
                        public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                            if (resource instanceof BitmapDrawable) {
                                Bitmap src = ((BitmapDrawable)resource).getBitmap();
                                if (src != null && src.getWidth() > 10) {
                                    Bitmap bkGnd = Bitmap.createBitmap(src, 50, 0, 10, src.getHeight());//起点x为50避开圆角
                                    if (bkGnd != null) {
                                        holder.iv_content_bg_gradient.setImageBitmap(bkGnd);
                                        holder.iv_content_bg.setVisibility(View.VISIBLE);
                                        holder.iv_content_bg.setImageDrawable(resource);
                                        return;
                                    }
                                }
                            }

                            holder.iv_content_bg.setVisibility(View.GONE);
                            holder.iv_content_bg_gradient.setImageResource(resId);
                        }
                    });
                    holder.tv_date.setText(item.data.body.title.text);
                    if (StringUtils.isNotEmptyWithTrim(item.data.body.title.color)) {
                        holder.tv_date.setTextColor(ColorUtil.parseColor(item.data.body.title.color, 0xff333333));
                    }
                    break;
                case TYPE_BANNER:
                    List<String> images = new ArrayList<>();
                    for (RecommendData.Banner banner : item.data.items) {
                        images.add(banner.imageLong);
                    }
                    if (!CollectionUtil.isEquals((ArrayList<String>) holder.banner.getImageUrls(), images)) {
                        holder.banner.update(images);
                    }
                    holder.banner.setOnBannerListener(new OnBannerListener() {
                        @Override
                        public void onBannerClick(int itemPosition) {
                            if (!CollectionUtil.isEmptyOrNull(item.data.items) && StringUtils.isNotEmptyWithTrim(item.data.items.get(itemPosition).url)) {
                                ExpJDMAUtil.onInsightsEventClick(item.id, item.tplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_BODY.KeyValue, item.data.items.get(itemPosition).id, String.valueOf(itemPosition), String.valueOf(position), item.data.items.get(itemPosition).id);
                                Router.build(item.data.items.get(itemPosition).url).go(mContext, new RouteNotFoundCallback(mContext));
                            }
                        }
                    });
                    bannerHolders.add(holder);
                    break;
                case TYPE_IMAGE_LIST:
                    if (item.data.body == null || item.data.body.items == null) {
                        break;
                    }
                    List<String> contents = new ArrayList<>();
                    int max = Math.min(item.data.body.items.size(), 3);
                    for (int i = 0; i < max; i++) {
                        RecommendData.BodyItem bodyItem = item.data.body.items.get(i);
                        if (bodyItem != null) {
                            contents.add(bodyItem.image);
                        }
                    }
                    holder.imageList.setAutoPlay(!TextUtils.equals(item.data.body.autoplay, "0"));//默认自动轮播
                    if (!CollectionUtil.isEquals((ArrayList<String>) holder.imageList.getImageUrls(), contents)) {
                        holder.imageList.update(contents);
                    } else {
                        if (holder.imageList.isAutoPlay()) {
                            holder.imageList.startAutoPlay();
                        }
                    }
                    holder.imageList.setOnBannerListener(new OnBannerListener() {
                        @Override
                        public void onBannerClick(int pos) {
                            if (pos < item.data.body.items.size()) {
                                RecommendData.BodyItem bodyItem = item.data.body.items.get(pos);
                                if (bodyItem != null && StringUtils.isNotEmptyWithTrim(bodyItem.url) && !NClick.isFastDoubleClick()) {
                                    ExpJDMAUtil.onInsightsEventClick(item.id, item.tplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_BODY.KeyValue, bodyItem.id, "", String.valueOf(position), bodyItem.id);
                                    Router.build(bodyItem.url).go(mContext, new RouteNotFoundCallback(mContext));
                                }
                            }
                        }
                    });
                    bannerHolders.add(holder);
                    break;
                case TYPE_SCROLL_LIST://成长发展
                    holder.cardListRv.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
                    RecommendCardListAdapter adapter = new RecommendCardListAdapter(mContext, item.data.body.items, item.tplType, position);
                    holder.cardListRv.setAdapter(adapter);
                    holder.cardListRv.setItemAnimator(null);
// TODO:                   holder.cardListRv.setHasFixedSize(true);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadHeader(ViewHolder holder, RecommendData item, int position, Map<String, String> jdmaParams) {
        if (TYPE_BANNER == holder.getItemViewType()) {
            return;
        }
        if (StringUtils.isNotEmptyWithTrim(item.icon)) {
            ImageLoader.load(mContext, holder.iv_card_icon, item.icon, false,
                    R.drawable.jdme_exp_recommend_image_holder, R.drawable.jdme_exp_recommend_image_holder);
        }
        holder.tv_card_title.setText(item.title);
        if (item.data.header != null) {
            if (item.data.header.subtitle != null) {
                holder.tv_desc.setVisibility(View.VISIBLE);
                holder.tv_desc.setText(item.data.header.subtitle.text);
                if (StringUtils.isNotEmptyWithTrim(item.data.header.subtitle.color)) {
                    holder.tv_desc.setTextColor(Color.parseColor(item.data.header.subtitle.color));
                }
            } else if (!TextUtils.isEmpty(item.data.header.text)) {
                holder.tv_desc.setVisibility(View.VISIBLE);
                holder.tv_desc.setText(item.data.header.text);
            } else {
                holder.tv_desc.setVisibility(View.GONE);
            }
        } else {
            holder.tv_desc.setVisibility(View.GONE);
        }
        holder.ll_recommend_header.setOnClickListener(getHeaderClickListener(item, position));
        holder.iftv_right.setVisibility(StringUtils.isEmptyWithTrim(item.url) ? View.GONE : View.VISIBLE);
        if (!TextUtils.isEmpty(item.jumpText) && !StringUtils.isEmptyWithTrim(item.url)) {
            holder.tv_card_more.setVisibility(View.VISIBLE);
            holder.tv_card_more.setText(item.jumpText);
        } else {
            holder.tv_card_more.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (mData == null || position < 0 || position >= mData.size()) {
            return 0;
        }
        switch (mData.get(position).tplType) {
            case "IMAGE":
                return TYPE_IMAGE;
            case "LIST":
                return TYPE_LIST;
            case "SUMMARY":
                return TYPE_SUMMARY;
            case "BANNER":
                return TYPE_BANNER;
            case "IMAGE_LIST":
                return TYPE_IMAGE_LIST;
            case "SCROLL_LIST":
                return TYPE_SCROLL_LIST;
            default:
                return 0;
        }
    }

    @Override
    public int getItemCount() {
        return mData != null ? mData.size() : 0;
    }

    private List<List<RecommendData.BodyItem>> splitList(List<RecommendData.BodyItem> target, int size) {
        List<List<RecommendData.BodyItem>> listArr = new ArrayList<>();
        //获取被拆分的数组个数
        int arrSize = target.size() % size == 0 ? target.size() / size : target.size() / size + 1;
        for (int i = 0; i < arrSize; i++) {
            List<RecommendData.BodyItem> sub = new ArrayList<>();
            //把指定索引数据放入到list中
            for (int j = i * size; j <= size * (i + 1) - 1; j++) {
                if (j <= target.size() - 1) {
                    sub.add(target.get(j));
                }
            }
            listArr.add(sub);
        }
        return listArr;
    }

    private View.OnClickListener getHeaderClickListener(final RecommendData item, final int position) {
        return new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (StringUtils.isNotEmptyWithTrim(item.url) && !NClick.isFastDoubleClick()) {
                    ExpJDMAUtil.onInsightsEventClick(item.id, item.tplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_HEADER.KeyValue, "", "", String.valueOf(position), "");
                    Router.build(item.url).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        };
    }

    private View.OnClickListener getFooterClickListener(final RecommendData item, final int position, final int footerIndex) {
        return new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                List<RecommendData.Title> footer = item.data.footer;
                if (StringUtils.isNotEmptyWithTrim(footer.get(footerIndex).url) && !NClick.isFastDoubleClick()) {
                    ExpJDMAUtil.onInsightsEventClick(item.id, item.tplType, ExpJDMAConstants.MainInfo.MAIN_INSIGHTS_FOOTER.KeyValue, "", String.valueOf(position), String.valueOf(position), footer.get(footerIndex).id);
                    Router.build(footer.get(footerIndex).url).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        };
    }

    @Override //动态设置布局管理器行数
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();

        if (manager instanceof GridLayoutManager) {
            final GridLayoutManager gridManager = ((GridLayoutManager) manager);
            gridManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    if (getItemViewType(position) == TYPE_BANNER || getItemViewType(position) == TYPE_SCROLL_LIST) {
                        return 2; //返回2展示一行
                    } else {
                        return 1;//返回1展示两行
                    }

                }
            });
        }
    }

    public void stopBannerAutoPlay() {
        if (bannerHolders == null) {
            return;
        }
        for (ViewHolder vh : bannerHolders) {
            if (vh.getItemViewType() == TYPE_IMAGE_LIST && vh.imageList != null && vh.imageList.isAutoPlay()) {
                vh.imageList.stopAutoPlay();
            } else if (vh.getItemViewType() == TYPE_BANNER && vh.banner != null && vh.banner.isAutoPlay()) {
                vh.banner.stopAutoPlay();
            }
        }
    }

    public void startBannerAutoPlay() {
        if (bannerHolders == null) {
            return;
        }
        for (ViewHolder vh : bannerHolders) {
            if (vh.getItemViewType() == TYPE_IMAGE_LIST && vh.imageList != null && vh.imageList.isAutoPlay()) {
                vh.imageList.startAutoPlay();
            } else if (vh.getItemViewType() == TYPE_BANNER && vh.banner != null && vh.banner.isAutoPlay()) {
                vh.banner.startAutoPlay();
            }
        }
    }

    public void clearBannerHolders() {
        bannerHolders.clear();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        //header部分
        ImageView iv_card_icon;
        TextView tv_card_title;
        TextView tv_card_more;
        TextView tv_desc;
        IconFontView iftv_right;
        LinearLayout ll_recommend_header;
        //footer部分
        FrameLayout fl_footer;
        LinearLayout ll_recommend_footer;
        Button btn_right;
        Button btn_left;
        //TYPE_IMAGE
        View iv_content_layout;
        ImageView iv_content;
        ImageView iv_gif_content;
        //TYPE_LIST
        WrapContentViewPager vp_app;
        CirclePageIndicator appIndicator;
        //TYPE_SUMMARY
        WrapContentViewPager vp_summary;
        ImageView iv_content_bg;
        ImageView iv_content_bg_gradient;
        TextView tv_date;
        CirclePageIndicator summaryIndicator;
        //TYPE_BANNER
        Banner banner;
        //TYPE_IMAGE_LIST
        Banner imageList;
        //TYPE_SCROLL_LIST
        RecyclerView cardListRv;

        public ViewHolder(@NonNull View itemView, int viewType) {
            super(itemView);
            //header部分
            iv_card_icon = itemView.findViewById(R.id.iv_card_icon);
            tv_card_title = itemView.findViewById(R.id.tv_card_title);
            tv_card_more = itemView.findViewById(R.id.tv_card_more);
            tv_desc = itemView.findViewById(R.id.tv_desc);
            iftv_right = itemView.findViewById(R.id.iftv_right);
            ll_recommend_header = itemView.findViewById(R.id.ll_recommend_header);
            //footer部分
            fl_footer = itemView.findViewById(R.id.fl_footer);
            ll_recommend_footer = itemView.findViewById(R.id.ll_recommend_footer);
            btn_right = itemView.findViewById(R.id.btn_right);
            btn_left = itemView.findViewById(R.id.btn_left);
            switch (viewType) {
                case TYPE_IMAGE:
                    iv_content_layout = itemView.findViewById(R.id.iv_content_layout);
                    iv_content = itemView.findViewById(R.id.iv_content);
                    iv_gif_content = itemView.findViewById(R.id.iv_gif_content);
                    break;
                case TYPE_LIST:
                    vp_app = itemView.findViewById(R.id.vp_app);
                    appIndicator = itemView.findViewById(R.id.page_indicator);
                    break;
                case TYPE_SUMMARY:
                    vp_summary = itemView.findViewById(R.id.vp_summary);
                    tv_date = itemView.findViewById(R.id.tv_date);
                    iv_content_bg = itemView.findViewById(R.id.iv_content_bg);
                    iv_content_bg_gradient = itemView.findViewById(R.id.iv_content_bg_gradient);
                    summaryIndicator = itemView.findViewById(R.id.page_indicator);
                    break;
                case TYPE_BANNER:
                    banner = itemView.findViewById(R.id.banner);
                    banner.setImageLoader(new BannerImageLoader(TYPE_BANNER));
                    banner.setSinglePage(1f, 0);
                    banner.setIndicatorGravity(BannerConfig.CENTER);
                    banner.setPageTransformer(false, new BannerPageTransformer());
                    break;
                case TYPE_IMAGE_LIST:
                    imageList = itemView.findViewById(R.id.image_list);
                    imageList.setImageLoader(new BannerImageLoader(TYPE_IMAGE_LIST));
                    imageList.setSinglePage(1f, 0);
                    imageList.setDelayTime(3000);
                    imageList.setIndicatorGravity(BannerConfig.CENTER);
                    imageList.setPageTransformer(false, new BannerPageTransformer());
                    break;
                case TYPE_SCROLL_LIST:
                    cardListRv = itemView.findViewById(R.id.card_list_rv);
                    break;
            }
        }
    }
}