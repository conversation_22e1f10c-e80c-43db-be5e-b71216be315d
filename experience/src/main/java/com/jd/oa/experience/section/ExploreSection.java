package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.ExploreData;
import com.jd.oa.experience.repo.ExploreRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.experience.view.PagerSnapAdapter;
import com.jd.oa.experience.view.PagerSnapRecyclerView;
import com.jd.oa.experience.view.TitleView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class ExploreSection extends StatelessSection implements Destroyable {

    private final Context mContext;
    private final String mTabCode;
    private boolean mIsFirst;
    private final SectionedRecyclerViewAdapter mAdapter;
    private ExploreSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final ExploreRepo repo;
    private ExploreData mData;

    private final BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    refreshUI();
                    break;
                default:
                    break;
            }
        }
    };

    public ExploreSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_explore_v2)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = ExploreRepo.get(mContext);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        ExploreData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.getExploreData(new LoadDataCallback<ExploreData>() {
            @Override
            public void onDataLoaded(ExploreData exploreData) {
                showData(exploreData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void showData(ExploreData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        } else {
            //setState(State.EMPTY);
        }
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ExploreSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        refreshUI();
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }

        if (mData.list == null || mData.list.isEmpty()) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        mItemViewHolder.mTitleView.setTitle(false, mData.icon, mData.title, mData.jumpUrl, mData.jumpText);
        mItemViewHolder.mTitleView.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        });

        mItemViewHolder.mExploreRv.setAdapter(new PagerSnapAdapter<ExploreData.ExploreItem>(
                mContext, mData.list, PagerSnapAdapter.VIEW_TYPE_LIST,
                R.layout.jdme_item_experience_section_explore_item_tiled) {
            @Override
            public void onBindView(View view, final ExploreData.ExploreItem data) {
                ImageView exploreItemIv = view.findViewById(R.id.explore_item_iv);
                TextView exploreItemNameTv = view.findViewById(R.id.explore_item_name_tv);
                TextView exploreItemSourceTv = view.findViewById(R.id.explore_item_source_tv);
                ImageLoader.load(mContext, exploreItemIv, data.imageUrl, R.drawable.jdme_bg_exp_course_default);
                exploreItemNameTv.setText(TextUtils.isEmpty(data.name) ? "" : data.name);
                exploreItemSourceTv.setText(TextUtils.isEmpty(data.source) ? "" : mContext.getString(R.string.exp_course_source_prefix) + data.source);
                view.setOnClickListener(new NoDoubleClickListener() {
                    @Override
                    protected void onNoDoubleClick(View v) {
                        if (!TextUtils.isEmpty(data.deeplink)) {
                            Router.build(data.deeplink).go(mContext, new RouteNotFoundCallback(mContext));

                            Map<String, String> map = new HashMap<>();
                            map.put("appId", data.id);
                            ExpJDMAUtil.onDevelopmentEventClick(ExpJDMAConstants.EXP_MAIN_DEV_EXPLORE, map);
                        }
                    }
                });
            }
        });
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public TitleView mTitleView;
        public PagerSnapRecyclerView<ExploreData.ExploreItem> mExploreRv;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mTitleView = itemView.findViewById(R.id.title_view);
            mExploreRv = itemView.findViewById(R.id.explore_rv);
        }
    }
}
