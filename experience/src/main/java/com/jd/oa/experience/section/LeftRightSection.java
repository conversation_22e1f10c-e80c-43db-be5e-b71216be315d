package com.jd.oa.experience.section;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_Service;
import static com.jd.oa.JDMAConstants.mobile_EXP_Main_Service_HRClick;
import static com.jd.oa.JDMAConstants.mobile_EXP_Main_Welfare;
import static com.jd.oa.JDMAConstants.mobile_EXP_Main_Welfare_HRClick;
import static com.jd.oa.experience.util.Constants.SERVICE_CACHE_KEY;
import static com.jd.oa.experience.util.Constants.SERVICE_SECTION_API;
import static com.jd.oa.experience.util.Constants.WELFARE_CACHE_KEY;
import static com.jd.oa.experience.util.Constants.WELFARE_SECTION_API;

import android.content.Context;
import android.content.IntentFilter;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.experience.R;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.LeftRightData;
import com.jd.oa.experience.model.RefreshObserver;
import com.jd.oa.experience.repo.JDHRRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class LeftRightSection extends StatelessSection implements Destroyable, RefreshObserver {
    private static final String TAG = "LeftRightSection";

    private final Context mContext;
    private final String mTabCode;
    private boolean mIsFirst;
    private final SectionedRecyclerViewAdapter mAdapter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final JDHRRepo<LeftRightData> mWelfareRepo;
    private final JDHRRepo<LeftRightData> mServiceRepo;
    private LeftRightData mWelfareData;
    private LeftRightData mServiceData;

    private final int mAppDisplayLimit = 3;
    private final List<Integer> mSectionTypes = new ArrayList<>();

    public static final String SECTION_WELFARE_V3 = "WELFARE_V3";
    public static final String SECTION_SERVICE_V3 = "SERVICE_V3";

    public static final int TYPE_WELFARE = 0;
    public static final int TYPE_SERVICE = 1;

    private LeftRightData getCacheByType(int type) {
        if (type == TYPE_WELFARE) {
            return mWelfareRepo.getCache();
        } else if (type == TYPE_SERVICE) {
            return mServiceRepo.getCache();
        }
        return null;
    }

    private String getApiForType(Integer type) {
        switch (type) {
            case TYPE_WELFARE:
                return WELFARE_SECTION_API;
            case TYPE_SERVICE:
                return SERVICE_SECTION_API;
            default:
                return null;
        }
    }

    public LeftRightSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first, List<String> leftRightSectionCodes) {
        super(SectionParameters.builder().
                headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_leftright)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        mWelfareRepo = JDHRRepo.create(WELFARE_CACHE_KEY, new TypeToken<LeftRightData>(){});
        mServiceRepo = JDHRRepo.create(SERVICE_CACHE_KEY, new TypeToken<LeftRightData>(){});
        if (leftRightSectionCodes != null) {
            for (String code : leftRightSectionCodes) {
                if (code.equals("WELFARE_V3")) {
                    mSectionTypes.add(TYPE_WELFARE);
                } else if (code.equals("SERVICE_V3")) {
                    mSectionTypes.add(TYPE_SERVICE);
                }
            }
        }

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);

        for (Integer type : mSectionTypes) {
            LeftRightData typeCache = getCacheByType(type);
            if (typeCache != null) {
                showData(typeCache, type);
            }
        }

        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        final AtomicInteger failedCount = new AtomicInteger(0);
        for (Integer type : mSectionTypes) {
            String api = getApiForType(type);
            if (api != null) {
                fetchData(type, api, failedCount);
            }
        }
    }

    private void fetchData(final Integer type, String api, final AtomicInteger failedCount) {
        LoadDataCallback<LeftRightData> callback = new LoadDataCallback<LeftRightData>() {
            @Override
            public void onDataLoaded(LeftRightData leftRightData) {
                showData(leftRightData, type);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                MELogUtil.localE(TAG, "左右楼层获取数据失败, type: " + type);
                if (failedCount.incrementAndGet() == mSectionTypes.size()) {
                    // 如果全部失败则设置状态
                    setState(State.FAILED);
                }
            }
        };

        if (type == TYPE_WELFARE) {
            mWelfareRepo.fetchData(null, api, callback);
        } else if (type == TYPE_SERVICE) {
            mServiceRepo.fetchData(null, api, callback);
        }
    }

    private void showData(LeftRightData data, int type) {
        if (type == TYPE_WELFARE) {
            mWelfareData = data;
        } else if (type == TYPE_SERVICE) {
            mServiceData = data;
        } else {
            return;
        }
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        }
    }

    private void refreshUI() {
        if (mItemViewHolder == null || (mWelfareData == null && mServiceData == null)) {
            return;
        }
        if (mSectionTypes.isEmpty() || ((mWelfareData == null || mWelfareData.items == null || mWelfareData.items.isEmpty()) &&
                (mServiceData == null || mServiceData.items == null || mServiceData.items.isEmpty()))) {
            //两侧均无数据或显示模式未设置
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            lp.setMargins(0, 0, 0, 0);
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }
        //有一侧或两侧都有数据
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        int defaultMargin = DensityUtil.dp2px(mContext, 8);
        lp.setMargins(defaultMargin, 0, defaultMargin, defaultMargin);
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);

        if (mSectionTypes.contains(TYPE_WELFARE)) { //需要显示左边
            fillLeftContainer();
        }

        if (mSectionTypes.contains(TYPE_SERVICE)) { //需要显示右边
            fillRightContainer();
        }

        if (mSectionTypes.contains(TYPE_WELFARE) && mSectionTypes.contains(TYPE_SERVICE)) { //两侧都需要显示
            if (mWelfareData != null && mWelfareData.items != null && !mWelfareData.items.isEmpty() &&
                    mServiceData != null && mServiceData.items != null && !mServiceData.items.isEmpty()) {
                //两侧都有数据，数据条目少的那一边应和另一边对齐高度
                int leftItemCount = Math.min(mWelfareData.items.size(), mAppDisplayLimit);
                int rightItemCount = Math.min(mServiceData.items.size(), mAppDisplayLimit);
                if (leftItemCount < rightItemCount) {
                    //左侧数据条目少，右侧数据条目多，左侧容器高度应和右侧对齐
                    for (int i = 0; i < rightItemCount - leftItemCount; i++) {
                        LayoutInflater inflater = LayoutInflater.from(mContext);
                        View emptyView = inflater.inflate(R.layout.jdme_item_experience_section_leftright_item, mItemViewHolder.mLeftItemContainer, false);
                        mItemViewHolder.mLeftItemContainer.addView(emptyView);
                    }
                } else if (leftItemCount > rightItemCount) {
                    //右侧数据条目少，左侧数据条目多，右侧容器高度应和左侧对齐
                    for (int i = 0; i < leftItemCount - rightItemCount; i++) {
                        LayoutInflater inflater = LayoutInflater.from(mContext);
                        View emptyView = inflater.inflate(R.layout.jdme_item_experience_section_leftright_item, mItemViewHolder.mLeftItemContainer, false);
                        mItemViewHolder.mRightItemContainer.addView(emptyView);
                    }
                }
            }
        }
    }

    private void fillLeftContainer() {
        if (mItemViewHolder == null || mItemViewHolder.mLeftItemContainer == null) {
            return;
        }
        if (mWelfareData == null || mWelfareData.items == null || mWelfareData.items.isEmpty()) {
            mItemViewHolder.mLeftContainer.setVisibility(View.GONE);
            return;
        }
        mItemViewHolder.mLeftContainer.setVisibility(View.VISIBLE);
        mItemViewHolder.mLeftTitle.setText(mWelfareData.title);
        if (TextUtils.isEmpty(mWelfareData.jumpUrl)) {
            mItemViewHolder.mLeftMoreIcon.setVisibility(View.GONE);
        } else {
            mItemViewHolder.mLeftMoreIcon.setVisibility(View.VISIBLE);
        }
        mItemViewHolder.mLeftTitleContainer.setOnClickListener(new AvoidFastClickListener(1500) {
            @Override
            public void onAvoidedClick(View view) {
                if (!TextUtils.isEmpty(mWelfareData.jumpUrl)) {
                    JDMAUtils.clickEvent(mobile_EXP_Main_Welfare, mobile_EXP_Main_Welfare, null);
                    Router.build(mWelfareData.jumpUrl).go(mContext, new RouteNotFoundCallback());
                }
            }
        });
        if (mWelfareData.count > 0) {
            mItemViewHolder.mLeftCount.setVisibility(View.VISIBLE);
            mItemViewHolder.mLeftCount.setTypeface(getTypeface());
            mItemViewHolder.mLeftUnit.setVisibility(View.VISIBLE);
            mItemViewHolder.mLeftCount.setText(String.valueOf(mWelfareData.count));
        } else {
            mItemViewHolder.mLeftCount.setVisibility(View.GONE);
            mItemViewHolder.mLeftUnit.setVisibility(View.GONE);
        }
        int appCapacity = Math.min(mWelfareData.items.size(), mAppDisplayLimit);
        mItemViewHolder.mLeftItemContainer.removeAllViews();
        for (int i = 0; i < appCapacity; i++) {
            LeftRightData.LeftRightItem item = mWelfareData.items.get(i);
            LayoutInflater inflater = LayoutInflater.from(mContext);
            View childView = inflater.inflate(R.layout.jdme_item_experience_section_leftright_item, mItemViewHolder.mLeftItemContainer, false);
            ImageView icon = childView.findViewById(R.id.iv_item_icon);
            TextView title = childView.findViewById(R.id.iv_item_title);
            TextView value = childView.findViewById(R.id.iv_item_value);
            TextView unit = childView.findViewById(R.id.iv_item_unit);
            Glide.with(mContext).load(item.icon)
                    .placeholder(R.drawable.jdme_bg_section_title_default_icon)
                    .error(R.drawable.jdme_bg_section_title_default_icon)
                    .into(icon);
            title.setText(item.title);
            value.setText(item.value);
            value.setTypeface(getTypeface());
            unit.setText(item.unit);
            childView.setOnClickListener(v -> {
                if (!TextUtils.isEmpty(item.url)) {
                    Map<String, String> param = new HashMap<>();
                    param.put("appId", item.id);
                    JDMAUtils.clickEvent(mobile_EXP_Main_Welfare_HRClick, mobile_EXP_Main_Welfare_HRClick, param);
                     Router.build(item.url).go(mContext, new RouteNotFoundCallback());
                 }
            });
            mItemViewHolder.mLeftItemContainer.addView(childView);
        }
    }

    private void fillRightContainer() {
        if (mItemViewHolder == null || mItemViewHolder.mRightItemContainer == null) {
            return;
        }
        if (mServiceData == null || mServiceData.items == null || mServiceData.items.isEmpty()) {
            mItemViewHolder.mRightContainer.setVisibility(View.GONE);
            return;
        }
        mItemViewHolder.mRightContainer.setVisibility(View.VISIBLE);
        mItemViewHolder.mRightTitle.setText(mServiceData.title);
        if (TextUtils.isEmpty(mServiceData.jumpUrl)) {
            mItemViewHolder.mRightMoreIcon.setVisibility(View.GONE);
        } else {
            mItemViewHolder.mRightMoreIcon.setVisibility(View.VISIBLE);
        }
        mItemViewHolder.mRightTitleContainer.setOnClickListener(new AvoidFastClickListener(1500) {
            @Override
            public void onAvoidedClick(View view) {
                if (!TextUtils.isEmpty(mServiceData.jumpUrl)) {
                    JDMAUtils.clickEvent(mobile_EXP_Main_Service, mobile_EXP_Main_Service, null);
                    Router.build(mServiceData.jumpUrl).go(mContext, new RouteNotFoundCallback());
                }
            }
        });
        if (mServiceData.count > 0) {
            mItemViewHolder.mRightCount.setVisibility(View.VISIBLE);
            mItemViewHolder.mRightCount.setTypeface(getTypeface());
            mItemViewHolder.mRightUnit.setVisibility(View.VISIBLE);
            mItemViewHolder.mRightCount.setText(String.valueOf(mServiceData.count));
        } else {
            mItemViewHolder.mRightCount.setVisibility(View.GONE);
            mItemViewHolder.mRightUnit.setVisibility(View.GONE);
        }
        int appCapacity = Math.min(mServiceData.items.size(), mAppDisplayLimit);
        mItemViewHolder.mRightItemContainer.removeAllViews();
        for (int i = 0; i < appCapacity; i++) {
            LeftRightData.LeftRightItem item = mServiceData.items.get(i);
            LayoutInflater inflater = LayoutInflater.from(mContext);
            View childView = inflater.inflate(R.layout.jdme_item_experience_section_leftright_item, mItemViewHolder.mRightItemContainer, false);
            ImageView icon = childView.findViewById(R.id.iv_item_icon);
            TextView title = childView.findViewById(R.id.iv_item_title);
            TextView value = childView.findViewById(R.id.iv_item_value);
            TextView unit = childView.findViewById(R.id.iv_item_unit);
            Glide.with(mContext).load(item.icon)
                    .placeholder(R.drawable.jdme_bg_section_title_default_icon)
                    .error(R.drawable.jdme_bg_section_title_default_icon)
                    .into(icon);
            title.setText(item.title);
            value.setText(item.value);
            value.setTypeface(getTypeface());
            unit.setText(item.unit);
            childView.setOnClickListener(v -> {
                if (!TextUtils.isEmpty(item.url)) {
                    Map<String, String> param = new HashMap<>();
                    param.put("appId", item.id);
                    JDMAUtils.clickEvent(mobile_EXP_Main_Service_HRClick, mobile_EXP_Main_Service_HRClick, param);
                    Router.build(item.url).go(mContext, new RouteNotFoundCallback());
                }
            });
            mItemViewHolder.mRightItemContainer.addView(childView);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        refreshUI();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private Typeface getTypeface() {
        if (mContext != null && mContext.getAssets() != null) {
            return Typeface.createFromAsset(mContext.getAssets(), "fonts/JDZhengHei2.1-Bold.otf");
        }
        return Typeface.DEFAULT;
    }

    @Override
    public void onRefresh() {
        loadData();
    }

    @Override
    public void onConfigChanged() {
        refreshUI();
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {
        public LinearLayout mLeftContainer;
        public LinearLayout mRightContainer;
        public LinearLayout mLeftItemContainer;
        public LinearLayout mRightItemContainer;
        public ConstraintLayout mLeftTitleContainer;
        public ConstraintLayout mRightTitleContainer;
        public TextView mLeftTitle;
        public TextView mRightTitle;
        public TextView mLeftCount;
        public TextView mRightCount;
        public TextView mLeftUnit;
        public TextView mRightUnit;
        public TextView mLeftMoreIcon;
        public TextView mRightMoreIcon;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            mLeftContainer = itemView.findViewById(R.id.ll_left_container);
            mRightContainer = itemView.findViewById(R.id.ll_right_container);
            mLeftTitleContainer = itemView.findViewById(R.id.cl_left_title_container);
            mRightTitleContainer = itemView.findViewById(R.id.cl_right_title_container);
            mLeftTitle = itemView.findViewById(R.id.tv_left_header_title);
            mRightTitle = itemView.findViewById(R.id.tv_right_header_title);
            mLeftCount = itemView.findViewById(R.id.tv_left_header_count);
            mRightCount = itemView.findViewById(R.id.tv_right_header_count);
            mLeftUnit = itemView.findViewById(R.id.tv_left_header_unit);
            mRightUnit = itemView.findViewById(R.id.tv_right_header_unit);
            mLeftItemContainer = itemView.findViewById(R.id.ll_left_content);
            mRightItemContainer = itemView.findViewById(R.id.ll_right_content);
            mLeftMoreIcon = itemView.findViewById(R.id.iv_left_chart_more);
            mRightMoreIcon = itemView.findViewById(R.id.iv_right_chart_more);
        }
    }
}
