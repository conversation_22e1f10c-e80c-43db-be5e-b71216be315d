package com.jd.oa.experience.section;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.Constant;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

@Deprecated
public class QUCSection extends StatelessSection implements Destroyable {
    private boolean mIsFirst;
    private Context mContext;
    private SectionedRecyclerViewAdapter mAdapter;
    private QUCSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private AppService appService = AppJoint.service(AppService.class);
    private String mTabCode;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    //
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    //
                    break;
                default:
                    break;
            }
        }
    };

    public QUCSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_quc)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new QUCSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        if (mItemViewHolder == null) {
            return;
        }
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);
        mItemViewHolder.mQrScanLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (mContext instanceof Activity) {
                    Intent action = new Intent(mContext, mContext.getClass());
                    action.putExtra(Constant.SHORTCUT_FROM, Constant.SHORTCUT_FLAG);
                    action.putExtra(Constant.SHORTCUT_ACTION, Constant.SHORTCUT_ID_SCAN);
                    appService.onDoShortcutAction((Activity) mContext, action);
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.QUCInfo.MAIN_QUC_SCAN.keyName, ExpJDMAConstants.QUCInfo.MAIN_QUC_SCAN.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_QUC, map);
                }
            }
        });
        mItemViewHolder.mPassCodeLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_EXP_PROFILE_OPEN_QRCODE));
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.QUCInfo.MAIN_QUC_PASS.keyName, ExpJDMAConstants.QUCInfo.MAIN_QUC_PASS.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_QUC, map);
            }
        });
        mItemViewHolder.mPayCodeLayout.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                if (!(mContext instanceof Activity)) {
                    return;
                }
                String jdPin = PreferenceManager.UserInfo.getJdAccount();
                if (StringUtils.isEmptyWithTrim(jdPin)) {
                    showToast();
                    return;
                }
                Intent action = new Intent(mContext, mContext.getClass());
                action.putExtra(Constant.SHORTCUT_FROM, Constant.SHORTCUT_FLAG);
                action.putExtra(Constant.SHORTCUT_ACTION, Constant.SHORTCUT_ID_EMPLOYEE_CARD);
                appService.onDoShortcutAction((Activity) mContext, action);

                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.QUCInfo.MAIN_QUC_PAY.keyName, ExpJDMAConstants.QUCInfo.MAIN_QUC_PAY.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_QUC, map);
            }
        });
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {
        public View mQrScanLayout;
        public View mPassCodeLayout;
        public View mPayCodeLayout;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mQrScanLayout = itemView.findViewById(R.id.qr_scan_layout);
            mPassCodeLayout = itemView.findViewById(R.id.pass_code_layout);
            mPayCodeLayout = itemView.findViewById(R.id.pay_code_layout);
        }
    }

    private void showToast() {
        View v;
        TextView tv;
        Toast myToast = new Toast(mContext);
        LayoutInflater inflate = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        v = inflate.inflate(R.layout.jdme_item_experience_section_quc_toast, null);
        myToast.setView(v);
        myToast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
        myToast.setDuration(Toast.LENGTH_LONG);
        v = myToast.getView();
        tv = v.findViewById(com.jd.oa.utils.R.id.tv_message);
        tv.setText(R.string.exp_bind_jd_account);
        myToast.show();
    }
}
