package com.jd.oa.experience.repo;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.experience.model.CourseData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;

public class CourseRepo {
    private static final String COURSE_CACHE_KEY = "exp.course.repo.cache.key";
    private static CourseRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static CourseRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new CourseRepo(context);
        }
        return sInstance;
    }

    private CourseRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    public CourseData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), COURSE_CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return null;
        }
        CourseData data = null;
        try {
            data = mGson.fromJson(cache.getResponse(), new TypeToken<CourseData>() {}.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public void addCache(CourseData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), COURSE_CACHE_KEY, null, mGson.toJson(data));
    }

    public void getCourseData(final LoadDataCallback<CourseData> callback) {
        Log.i("zhn", "getCourseData");
        HttpManager.post(null, new HashMap<String, String>(), new HashMap<String, Object>(), new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<CourseData> response = ApiResponse.parse(info.result, new TypeToken<CourseData>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getMyCourse, data = " + info.result);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    MELogUtil.localV(MELogUtil.TAG_JIS, "getMyCourse, err = " + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                MELogUtil.localV(MELogUtil.TAG_JIS, "getMyCourse, err = " + (exception != null ? exception.getMessage() : ""));
            }
        }, "exp.v2.getMyCourse");
        //http://j-api.jd.com/mocker/data?p=1077&v=POST&u=exp.v2.getMyCourse
    }
}
/*
{
	"content": {
		"courseTypeList": [{
			"courseTypeCode": 1,
			"courseTypeName": "课程资源-精品系列",
			"items": [{
				"courseId": "335120974",
				"courseName": "JDTECH&技术说 | 第23期 刘鹏：互联网商业化中的数据应用新趋势",
				"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fproject%2Fproject.du%3Fproject_id%3D335120974",
				"imageUrl": "https://jduimg.jd.com/tenant2-r/tenant2/image/8d7795e2869a4044b75b5c54f95aa472.jpg",
				"levelName": "",
				"source": "京东集团",
				"studyDuration": ""
			}, {
				"courseId": "219961196",
				"courseName": "【JDTALK】第73期 用镜头丈量未至之地-陆川导演分享会",
				"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fproject%2Fproject.du%3Fproject_id%3D219961196",
				"imageUrl": "https://jduimg.jd.com/tenant2-r/tenant2/image/08787b43dc774fe0aeac8fc41a1aa11a.jpg",
				"levelName": "",
				"source": "京东集团",
				"studyDuration": ""
			}, {
				"courseId": "83413683",
				"courseName": "JDTALK 第65期 迎接组织的数字化转型 - 赋予员工面向未来的能力",
				"courseUrl": "jdme://jm/biz/appcenter/201903250420?url=https%3A%2F%2Fl.jd.com%2Fstudent%2Fproject%2Fproject.du%3Fproject_id%3D83413683",
				"imageUrl": "https://jduimg.jd.com/tenant2-r/tenant2/image/14cc5097ef5c46e99e2e195d7a530d9c.jpg",
				"levelName": "",
				"source": "通用力中心",
				"studyDuration": ""
			}],
			"mapName": "",
			"moreUrl": ""
		}],
		"icon": "https://storage.360buyimg.com/jd.jme.client/images/insightjingpinkecheng.png",
		"jumpText": "",
		"jumpUrl": "",
		"title": "精品课程"
	},
	"errorCode": "0",
	"errorMsg": ""
}
* */
