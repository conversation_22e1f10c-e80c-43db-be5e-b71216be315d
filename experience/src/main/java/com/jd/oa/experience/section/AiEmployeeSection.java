package com.jd.oa.experience.section;

import static com.jd.oa.JDMAConstants.mobile_EXP_Main_HRAI_click;
import static com.jd.oa.experience.util.Constants.AIEMPLOYEE_CACHE_KEY;
import static com.jd.oa.experience.util.Constants.AIEMPLOYEE_SECTION_API;

import android.content.Context;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Shader;
import android.text.TextPaint;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.AIEmployeeAdapter;
import com.jd.oa.experience.fragment.ExpTabFragment;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.AIEmployeeData;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.model.RefreshObserver;
import com.jd.oa.experience.repo.JDHRRepo;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

public class AiEmployeeSection extends StatelessSection implements Destroyable, RefreshObserver {
    private final Context mContext;
    private final String mTabCode;
    private boolean mIsFirst;
    private final SectionedRecyclerViewAdapter mAdapter;
    private AiEmployeeSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private final JDHRRepo<AIEmployeeData> repo;
    private AIEmployeeData mData;

    private static final int[] GRADIENT_COLORS = {
            Color.parseColor("#74D5FF"),
            Color.parseColor("#2893FF"),
            Color.parseColor("#4B78FF")
    };

    public AiEmployeeSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_aiemployee)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        repo = JDHRRepo.create(AIEMPLOYEE_CACHE_KEY, new TypeToken<AIEmployeeData>(){});

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        AIEmployeeData cache = repo.getCache();
        if (cache != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        }
    }

    private void loadData() {
        repo.fetchData(null, AIEMPLOYEE_SECTION_API, new LoadDataCallback<AIEmployeeData>() {
            @Override
            public void onDataLoaded(AIEmployeeData aiEmployeeData) {
                showData(aiEmployeeData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }

    private void showData(AIEmployeeData data) {
        mData = data;
        refreshUI();
        if (data != null) {
            setState(State.LOADED);
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        refreshUI();
    }

    private void refreshUI() {
        if (mData == null || mItemViewHolder == null) {
            return;
        }
        if (mData.items == null || mData.items.isEmpty()) { // 如果没有数据则隐藏楼层
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) mItemViewHolder.itemView.getLayoutParams();
            lp.height = 0;
            lp.setMargins(0, 0, 0, 0);
            mItemViewHolder.itemView.setLayoutParams(lp);
            return;
        }

        //如果有数据则显示楼层，正常高度
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams)mItemViewHolder.itemView.getLayoutParams();
        lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        int defaultMargin = DensityUtil.dp2px(mContext, 8);
        lp.setMargins(defaultMargin, 0, defaultMargin, defaultMargin);
        mItemViewHolder.itemView.setLayoutParams(lp);
        ExpTabFragment.notifySectionVisible(mContext, mTabCode);
        mItemViewHolder.mRecyclerview.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
        mItemViewHolder.mRecyclerview.setAdapter(new AIEmployeeAdapter(mData.items));
        mItemViewHolder.mMessageTitle.setText(mData.title);
        applyTextGradient(mItemViewHolder.mMessageTitle, mData.title);
        mItemViewHolder.mMessageDesc.setText(mData.subTitle);
        applyTextGradient(mItemViewHolder.mMessageDesc, mData.subTitle);
        if (mItemViewHolder.mEmployeeImage != null && mData.icon != null) {
            Glide.with(mContext).load(mData.icon).into(mItemViewHolder.mEmployeeImage);
        }
        mItemViewHolder.mMessageDescContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(mData.jumpUrl)) {
                    Map<String, String> param = new HashMap<>();
                    param.put("appId", mData.eventId);
                    JDMAUtils.clickEvent(mobile_EXP_Main_HRAI_click, mobile_EXP_Main_HRAI_click, param);
                    Router.build(mData.jumpUrl).go(mContext, new RouteNotFoundCallback(mContext));
                }
            }
        });
    }

    private void applyTextGradient(TextView textView, String text) {
        if (textView == null || text == null || text.isEmpty()) return;

        TextPaint paint = textView.getPaint();
        float textWidth = paint.measureText(text);

        // These positions control where each color transition starts
        // 0.0f is the start, 1.0f is the end
        float[] positions = new float[]{
                0.0f,    // First color starts at the beginning
                0.2f,    // Second color starts at 10% of the width
                0.5f     // Third color goes to the end
        };

        LinearGradient gradient = new LinearGradient(
                0, 0, textWidth, 0,
                GRADIENT_COLORS,
                positions,  // Pass our custom positions
                Shader.TileMode.CLAMP
        );

        paint.setShader(gradient);
        textView.invalidate();
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    @Override
    public void onRefresh() {
        loadData();
    }

    @Override
    public void onConfigChanged() {
        refreshUI();
    }

    private static class ItemViewHolder extends RecyclerView.ViewHolder {

        public ImageView mEmployeeImage;
        public TextView mMessageTitle;
        public TextView mMessageDesc;
        public RecyclerView mRecyclerview;
        public LinearLayout mMessageDescContainer;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mEmployeeImage = itemView.findViewById(R.id.iv_ai_employee_image);
            mMessageTitle = itemView.findViewById(R.id.tv_title_main);
            mMessageDesc = itemView.findViewById(R.id.tv_title_sub);
            mRecyclerview = itemView.findViewById(R.id.rv_ai_employee);
            mMessageDescContainer = itemView.findViewById(R.id.ll_subtitle);
        }
    }
}
