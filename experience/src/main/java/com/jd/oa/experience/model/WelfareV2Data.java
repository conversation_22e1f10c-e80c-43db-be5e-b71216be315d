package com.jd.oa.experience.model;

import java.io.Serializable;
import java.util.ArrayList;

public class WelfareV2Data implements Serializable {
    public String title;
    public String icon;
    public String jumpText;
    public String jumpUrl;
    public String message;
    public Integer count;
    public String welfareVer;

    public ArrayList<WelfareV2Data.WelfareItem> items;

    public static class WelfareItem {
        public String id;
        public String icon;
        public String title;
        public String value;
        public String unit;
        public String url;
    }
}

/*
{
	"errorCode": "0",
	"content": {
		"message": "恭喜获得了1项新的福利",
		"icon": "https://storage.360buyimg.com/jd.jme.client/images/insightfuli.png",
		"count": 25,
		"title": "我的专属福利",
		"jumpText": "",
		"items": [{
			"unit": "积分",
			"id": "1111111",
			"title": "福利积分",
			"value": "618",
			"url": ""
		}, {
			"unit": "小时",
			"id": "2222222",
			"title": "我的假期",
			"value": "24",
			"url": ""
		}, {
			"unit": "张",
			"id": "3333333",
			"title": "福利券",
			"value": "18",
			"url": ""
		}, {
			"unit": "",
			"id": "4444444",
			"title": "安居计划",
			"value": "可申请",
			"url": ""
		}, {
			"unit": "",
			"id": "5555555",
			"title": "福利房",
			"value": "可申请",
			"url": ""
		}, {
			"unit": "截止",
			"id": "6666666",
			"title": "健康体检",
			"value": "12/31",
			"url": ""
		}],
		"jumpUrl": "jdme://jm/biz/appcenter/202208291366?mparam=%7B%22url%22%3A%22https%3A%2F%2Fjdme-welfare-pro.pf.jd.com%2Fall%3FshowType%3D1%22%2C%22isNativeHead%22%3A%220%22%2C%22isHideShareButton%22%3A%220%22%2C%22SafeArea%22%3A%220%22%7D"
	},
	"errorMsg": ""
}
*/