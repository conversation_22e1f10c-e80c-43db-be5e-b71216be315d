package com.jd.oa.experience.adapter;

import android.content.Context;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.experience.R;
import com.jd.oa.experience.model.CommonData;
import com.jd.oa.experience.model.ExpCommonEyeCache;
import com.jd.oa.experience.util.NumUtil;
import com.jd.oa.experience.view.NoDoubleClickListener;
import com.jd.oa.ui.IconFontView;

import java.util.List;

public class ExpCommonsAdapter extends RecyclerView.Adapter<ExpCommonsAdapter.ViewHolder> {

    private final Context mContext;
    private final List<CommonData.Commons> mList;
    private int mParentWidth = 0;
    private boolean isFirstInit = false;

    public ExpCommonsAdapter(Context context, List<CommonData.Commons> commons) {
        mContext = context;
        mList = commons;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        mParentWidth = parent.getMeasuredWidth();
        return new ViewHolder(View.inflate(mContext, R.layout.jdme_item_experience_section_common_list, null));
    }

    private int getItemWidth() {
        if (mList.size() == 1) {
            return mParentWidth;
        } else if (mList.size() <= 3) {
            return mParentWidth / mList.size();//需要均分rv的类型
        } else {
            int maxWidth = mContext.getResources().getDimensionPixelSize(R.dimen.commons_item_width) * mList.size();
            //规则是：当大于等于4个，计算106dp*4，如果容器宽度大于等于这个值，平分，否则显示滚动条
            if (maxWidth > mParentWidth) {
                return mContext.getResources().getDimensionPixelSize(R.dimen.commons_item_width);
            } else {
                return mParentWidth / mList.size();
            }
        }
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        holder.itemView.setLayoutParams(new FrameLayout.LayoutParams(getItemWidth(), FrameLayout.LayoutParams.MATCH_PARENT));
        if (position != 0) {
            holder.vDivider.setVisibility(View.VISIBLE);
        }
        final CommonData.Commons commons = mList.get(position);

        if (isFirstInit && commons.isSecret()) {
            String eyeKey = TextUtils.isEmpty(commons.getAppId()) ? commons.getTitle() : commons.getAppId();
            ExpCommonEyeCache.getInstance().addCache(eyeKey, false);
        }
        holder.tvTitle.setText(commons.getTitle());
        String num = "";
       /* if (!TextUtils.isEmpty(commons.getAppId()) && commons.getAppId().equals("10038")) {
            //如果是钱包 需要显示 333,333,333.00这种格式
            num = NumUtil.formatToSepara(Double.parseDouble(commons.getValue()));
        } else if (!TextUtils.isEmpty(commons.getAppId()) && commons.getAppId().equals("201806080253")) {
            //如果是福利积分 显示 55.00 保留两位小数
            num = NumUtil.formatTo00(Double.parseDouble(commons.getValue()));
//        if(commons.isSecret()){
            num = NumUtil.formatToSepara(Double.parseDouble(commons.getValue()));
        } else {
            num = String.valueOf(commons.getValue());
        }
        */
        num = NumUtil.formatTo2(commons.getValue());
        if (num.contains(".")) {
            String[] split = num.split("\\.");
            if (split.length > 1) {
                num = split[0] + "<font color= \"#4F4031\"><small>" + "." + split[1] + "</small></font>";
            }
        }
        holder.tvMid.setText(Html.fromHtml(num));
        if (TextUtils.isEmpty(commons.getUrl())) {
            //如果url为空不显示箭头 并且没有跳转事件
//            holder.iftvRight.setVisibility(View.GONE);
            //后面改成了默认不显示
        } else {
            holder.itemView.setOnClickListener(new NoDoubleClickListener() {
                @Override
                protected void onNoDoubleClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(commons);
                    }
                }
            });
        }
        if (commons.isSecret()) {
//            holder.tvTitle.setPadding(0, 0, holder.iftvEye.getWidth(), 0);
            holder.iftvEyeLayout.setVisibility(View.VISIBLE);
            final String finalNum = num;
            final String eyeKey = TextUtils.isEmpty(commons.getAppId()) ? commons.getTitle() : commons.getAppId();
            boolean cache = ExpCommonEyeCache.getInstance().getCache(eyeKey);
            holder.iftvEyeLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    boolean cache1 = ExpCommonEyeCache.getInstance().getCache(eyeKey);
                    ExpCommonEyeCache.getInstance().addCache(eyeKey, !cache1);
                    showData(holder, !cache1, finalNum);
                }
            });

            showData(holder, cache, num);
        } else {
            holder.iftvEyeLayout.setVisibility(View.GONE);
//            holder.tvTitle.setPadding(0, 0, DensityUtil.dp2px(mContext, 5), 0);
        }
    }


    /**
     * 需要显示数据 或者显示********
     *
     * @param holder
     * @param isShow
     * @param showNum
     */
    private void showData(ViewHolder holder, boolean isShow, String showNum) {
        if (!isShow) {
            holder.tvMid.setText("*******");
            holder.iftvEye.setText(R.string.icon_general_eyeinvisible);
        } else {
            holder.tvMid.setText(Html.fromHtml(showNum));
            holder.iftvEye.setText(R.string.icon_general_eye);
        }
    }

    @Override
    public int getItemCount() {
        return mList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        TextView tvMid;
        View vDivider;
        View iftvEyeLayout;
        IconFontView iftvEye;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvMid = itemView.findViewById(R.id.tv_mid);
            vDivider = itemView.findViewById(R.id.v_divider);
            iftvEyeLayout = itemView.findViewById(R.id.iftv_eye_layout);
            iftvEye = itemView.findViewById(R.id.iftv_eye);
        }
    }


    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(CommonData.Commons common);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public void setFirstInit(boolean firstInit) {
        isFirstInit = firstInit;
    }
}
