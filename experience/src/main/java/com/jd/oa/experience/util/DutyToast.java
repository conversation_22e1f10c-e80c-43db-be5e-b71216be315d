package com.jd.oa.experience.util;

import static android.view.animation.Animation.RESTART;

import android.content.Context;
import android.os.Handler;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.experience.R;
import com.jd.oa.utils.DensityUtil;

import java.util.Timer;
import java.util.TimerTask;

public class DutyToast {

    public static void showToast(Context context, String text) {
        Toast myToast = new Toast(context);
        myToast.setGravity(Gravity.CENTER, 0, -DensityUtil.dp2px(context, 100));
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_skin_save_toast, null);
        TextView textView = (TextView) view.findViewById(R.id.toast_text);
        textView.setText(text);
        myToast.setView(view);
        myToast.setDuration(Toast.LENGTH_SHORT);
        myToast.show();
    }

    public static void showToast(Context context, int resId) {
        String text = context.getResources().getString(resId);
        showToast(context, text);
    }

    private static Timer timer;
    private static Toast mLoadingToast;
    private static final Handler sHandler = new Handler();
    private static final Runnable sShowToastRunnable = new Runnable() {
        @Override
        public void run() {
            if (mLoadingToast != null) {
                mLoadingToast.show();
                sHandler.postDelayed(this, 3500);
            }
        }
    };
    private static final Runnable sStopShowToastRunnable = new Runnable() {
        @Override
        public void run() {
            if (mLoadingToast != null) {
                mLoadingToast.cancel();
            }
            sHandler.removeCallbacks(sShowToastRunnable);
        }
    };
//    private static ScheduledThreadPoolExecutor sExecutor = new ScheduledThreadPoolExecutor(1);

    public static void showLoading(Context context) {
        if (mLoadingToast == null)
            mLoadingToast = new Toast(context);
        View view = View.inflate(context, R.layout.jdme_view_upload, null);
        mLoadingToast.setView(view);
        mLoadingToast.setGravity(Gravity.CENTER, 0, -DensityUtil.dp2px(context, 100));
        TextView tv = view.findViewById(R.id.tv);
        tv.setVisibility(View.GONE);
        ImageView iv = view.findViewById(R.id.iv);
        iv.setImageResource(R.drawable.profile_section_qrcode_loading);
        RotateAnimation rotateAnimation = new RotateAnimation(0, 360, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        rotateAnimation.setDuration(1000);
        LinearInterpolator lin = new LinearInterpolator();
        rotateAnimation.setInterpolator(lin);
        rotateAnimation.setRepeatCount(-1);
        rotateAnimation.setRepeatMode(RESTART);
        iv.startAnimation(rotateAnimation);
        mLoadingToast.setDuration(Toast.LENGTH_LONG);
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                mLoadingToast.show();
            }
        }, 0, 3500);
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                mLoadingToast.cancel();
                timer.cancel();
            }
        }, 15000);
//        sHandler.post(sShowToastRunnable);
//        sHandler.postDelayed(sShowToastRunnable, 15000);
    }

    public static void stopLoading() {
        if (mLoadingToast != null)
            mLoadingToast.cancel();
        if (timer != null) {
            timer.cancel();
        }
//        sHandler.removeCallbacks(sShowToastRunnable);
//        sHandler.removeCallbacks(sStopShowToastRunnable);
    }
}
