package com.jd.oa.experience.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.ExpCommonsAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.experience.model.CommonData;
import com.jd.oa.experience.model.Destroyable;
import com.jd.oa.experience.repo.ExpCommonRepo;
import com.jd.oa.experience.repo.LoadDataStatusCallBack;
import com.jd.oa.experience.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ScreenUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

@Deprecated
public class CommonSection extends Section implements Destroyable {

    public static final String FLAG_PIN = "flag_pin";
    public static final String FLAG_ID = "flag_app_id";

    private boolean mIsFirst;
    private Context mContext;
    private String mTabCode;
    private SectionedRecyclerViewAdapter mAdapter;
    private CommonSection.ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private ExpCommonsAdapter adapter;
    private List<CommonData.Commons> mList;
    private ExpCommonRepo mRepo;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == null) return;
            switch (intent.getAction()) {
                case Constants.ACTION_REFRESH_EXP_SECTIONS:
                    if (TextUtils.equals(intent.getStringExtra("tabCode"), mTabCode)) {
                        adapter.setFirstInit(false);
                        loadData();
                    }
                    break;
                case Constants.ACTION_SCREEN_WIDTH_CHANGE:
                    adapter = new ExpCommonsAdapter(mContext, mList);
                    adapter.setFirstInit(false);
                    adapter.setOnItemClickListener(onItemClickListener);
                    mItemViewHolder.rvCommons.setAdapter(adapter);
                    List<CommonData.Commons> cache = mRepo.getCache();
                    if (mRepo.getCache() != null) {
                        if (showSeekBar(mList.size())) {
                            mItemViewHolder.sbScrollBar.setVisibility(View.VISIBLE);
                        } else {
                            mItemViewHolder.sbScrollBar.setVisibility(View.GONE);
                        }
                        showData(cache);
                    }
                    break;
            }
        }
    };
    private final ExpCommonsAdapter.OnItemClickListener onItemClickListener= new ExpCommonsAdapter.OnItemClickListener() {
        @Override
        public void onItemClick(CommonData.Commons common) {
            Map<String, String> map = new HashMap<>();
            map.put("appId", common.getAppId());
            ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_DIGITAL, map);

            Router.build(common.getUrl()).go(mContext, new RouteNotFoundCallback(mContext));
        }
    };

    public CommonSection(Context context, int from, SectionedRecyclerViewAdapter adapter, String tabCode, boolean first) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_experience_header)
                .itemResourceId(R.layout.jdme_item_experience_section_common)
                .loadingResourceId(R.layout.jdme_loading_view)
                .emptyResourceId(R.layout.jdme_header_experience_header)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTabCode = tabCode;
        mIsFirst = first;
        mRepo = ExpCommonRepo.get(mContext);
        mList = new ArrayList<>();
        this.adapter = new ExpCommonsAdapter(mContext, mList);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.ACTION_EXP_COMMON_ON_RESUME);
        intentFilter.addAction(Constants.ACTION_EXP_COMMON_ON_PAUSE);
        intentFilter.addAction(Constants.ACTION_REFRESH_EXP_SECTIONS);
        intentFilter.addAction(Constants.ACTION_SCREEN_WIDTH_CHANGE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        List<CommonData.Commons> cache = mRepo.getCache();
        if (mRepo.getCache() != null) {
            showData(cache);
        }
        if (from != ExperienceFragment.REFRESH_FROM_CACHE) {
            loadData();
        } else {
            this.adapter.setFirstInit(true);
        }

    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new CommonSection.ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
/*        if (mIsFirst) {
            RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) viewHolder.itemView.getLayoutParams();
            lp.topMargin = DensityUtil.dp2px(mContext, 4);
        }*/

        final CommonSection.ItemViewHolder holder = (CommonSection.ItemViewHolder) viewHolder;
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        };
        holder.rvCommons.setLayoutManager(layoutManager);
        adapter.setOnItemClickListener(onItemClickListener);
        holder.rvCommons.setAdapter(adapter);
        holder.rvCommons.setNestedScrollingEnabled(false);
        holder.rvCommons.setItemViewCacheSize(5);

        if (showSeekBar(mList.size())) {
            //大于3条 滑动条显示 反之则隐藏
            holder.sbScrollBar.setVisibility(View.VISIBLE);
        } else {
            holder.sbScrollBar.setVisibility(View.GONE);
        }
        holder.sbScrollBar.setPadding(0, 0, 0, 0);
        holder.sbScrollBar.setThumbOffset(0);
        holder.rvCommons.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                //显示区域的高度。
                int extent = holder.rvCommons.computeHorizontalScrollExtent();
                //整体的高度，注意是整体，包括在显示区域之外的。
                int range = holder.rvCommons.computeHorizontalScrollRange();
                //已经向下滚动的距离，为0时表示已处于顶部。
                int offset = holder.rvCommons.computeHorizontalScrollOffset();
//                Log.i("dx------", range + "****" + extent + "****" + offset);
                //此处获取seekbar的getThumb，就是可以滑动的小的滚动游标
                GradientDrawable gradientDrawable = (GradientDrawable) holder.sbScrollBar.getThumb();
                //根据列表的个数，动态设置游标的大小，设置游标的时候，progress进度的颜色设置为和seekbar的颜色设置的一样的，所以就不显示进度了。
                gradientDrawable.setSize(holder.sbScrollBar.getMeasuredWidth() * extent / range, DensityUtil.dp2px(mContext, 3));
                //设置可滚动区域
                holder.sbScrollBar.setMax((range - extent));
                if (offset == 0) {
                    holder.sbScrollBar.setProgress(0);
                } else if (dx > 0) {
//                    Log.i("dx------", "右滑");
                    holder.sbScrollBar.setProgress(offset);
                } else if (dx < 0) {
//                    Log.i("dx------", "左滑");
                    holder.sbScrollBar.setProgress(offset);

                }
            }
        });
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private class ItemViewHolder extends RecyclerView.ViewHolder {
        RecyclerView rvCommons;
        SeekBar sbScrollBar;

        public ItemViewHolder(View itemView) {
            super(itemView);
            rvCommons = itemView.findViewById(R.id.rv_commons);
            sbScrollBar = itemView.findViewById(R.id.sb_scroll_bar);
        }
    }


    public void loadData() {
        mRepo.getCommonData(new LoadDataStatusCallBack<List<CommonData.Commons>>() {
            @Override
            public void onDataLoaded(List<CommonData.Commons> commonData, boolean isDataChanged) {
                if (isDataChanged) {
                    //如果数据列表长度发生改变 重新布局
                    adapter = new ExpCommonsAdapter(mContext, mList);
                    adapter.setOnItemClickListener(onItemClickListener);
                    mItemViewHolder.rvCommons.setAdapter(adapter);
                }
                if (showSeekBar(commonData.size())) {
                    mItemViewHolder.sbScrollBar.setVisibility(View.VISIBLE);
                } else {
                    mItemViewHolder.sbScrollBar.setVisibility(View.GONE);
                }
                showData(commonData);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                setState(State.FAILED);
            }
        });
    }


    public void showData(List<CommonData.Commons> commonData) {
        if (commonData.size() > 0) {
            mList.clear();
            mList.addAll(commonData);
            if (adapter != null)
                adapter.notifyDataSetChanged();
            setState(State.LOADED);
        } else {
            setState(State.EMPTY);
        }
    }

    private boolean showSeekBar(int size) {
        if (size > 3) {
            int maxWidth = mContext.getResources().getDimensionPixelSize(R.dimen.commons_item_width) * size;
            return maxWidth > ScreenUtil.getScreenWidth(mContext) - DensityUtil.dp2px(mContext, 16);
        }
        return false;
    }

}