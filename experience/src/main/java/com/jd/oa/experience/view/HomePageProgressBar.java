package com.jd.oa.experience.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ProgressBar;

import com.jd.oa.experience.R;

public class HomePageProgressBar extends FrameLayout {

    private Context mContext;
    private ProgressBar mProgressBar;
    private View mThumb;
    private int mMax = 5;
    private int mProgress = 0;
    private int thumbWidth;

    public HomePageProgressBar(Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public HomePageProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    public HomePageProgressBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }

    private void initView() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_homepage_progressbar, this);
        mProgressBar = view.findViewById(R.id.progress_bar);
        mThumb = view.findViewById(R.id.thumb);
        thumbWidth = mContext.getResources().getDimensionPixelSize(R.dimen.homepage_progressbar_thumb_size);
    }

    public void setProgress(int progress, int max) {
        mProgressBar.setProgress(progress);
        mProgressBar.setMax(max);
        mMax = max;
        mProgress = progress;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int parentWidth = MeasureSpec.getSize(widthMeasureSpec);
        int marginStart;
        if (mProgress >= mMax) {
            marginStart = parentWidth - thumbWidth;
        } else if (mProgress <= 0) {
            marginStart = 0;
        } else {
            marginStart = parentWidth * mProgress / mMax - thumbWidth / 2;
        }
        //计算thumb位置
        FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams)mThumb.getLayoutParams();
        lp.setMargins(marginStart, 0, 0, 0);
        mThumb.setLayoutParams(lp);
    }
}
