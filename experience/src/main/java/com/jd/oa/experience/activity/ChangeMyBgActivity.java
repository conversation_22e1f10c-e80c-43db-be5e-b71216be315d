package com.jd.oa.experience.activity;

import android.Manifest;
import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.RectF;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.core.app.SharedElementCallback;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.business.index.view.SelectPicPopupEntity;
import com.jd.oa.cache.FileCache;
import com.jd.oa.experience.R;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.model.ExpUploadData;
import com.jd.oa.experience.model.HomePageData;
import com.jd.oa.experience.repo.MyHomePageRepo;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.view.UploadView;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.prefile.OpenFileUtil;
import com.jd.oa.ui.widget.IosActionSheetDialogNew;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.StatusBarUtil;
import com.yu.bundles.album.photoview.PhotoView;

import java.io.File;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;

public class ChangeMyBgActivity extends BaseActivity implements View.OnClickListener {

    private static final String HEAD_PHOTO_FILE_NAME = "avatar_";
    private static final String CAPTURE_IMG = "capture_";
    private String mCaptureImgName;
    private String mCropImgName;
    private PhotoView iv;
    private TextView tvChange;
    private String imgUrl = "";
    private boolean isSelf = false;
    private RelativeLayout rlRoot;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        StatusBarUtil.setColorNoTranslucent(this, Color.parseColor("#000000"));
        StatusBarUtil.setDarkMode(this);
        super.onCreate(savedInstanceState);
//        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
//        setEnterSharedElementCallback(new TransitionCallBack());
        setContentView(R.layout.activity_change_my_bg);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }
        initView();
    }

    private void initView() {
        iv = findViewById(R.id.iv);
        tvChange = findViewById(R.id.tv_change);
        rlRoot = findViewById(R.id.rl_root);
        tvChange.setOnClickListener(this);

        rlRoot.setOnClickListener(this);
        iv.setOnClickListener(this);
        if (getIntent().getExtras() != null) {
            imgUrl = getIntent().getExtras().getString("imageUrl", "");
            isSelf = getIntent().getExtras().getBoolean("isSelf", false);
        }
        if (isSelf) {
            tvChange.setVisibility(View.VISIBLE);
        }
        if (TextUtils.isEmpty(imgUrl)) {
            ImageLoader.load(AppBase.getAppContext(), iv, R.drawable.exp_default_bkgnd);
        } else {
            ImageLoader.load(AppBase.getAppContext(), iv, imgUrl);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_change) {
            toGetPic();
        } else if (id == R.id.rl_root || id == R.id.iv) {
//            finish();
            supportFinishAfterTransition();
        }
    }


    private void toGetPic() {
        new IosActionSheetDialogNew(this, IosActionSheetDialogNew.SheetItemColor.BLACK).builder()
                .setCancelable(false)
                .setCanceledOnTouchOutside(true)
                .addSheetItem(getString(R.string.me_camera), IosActionSheetDialogNew.SheetItemColor.BLACK2,
                        new IosActionSheetDialogNew.OnSheetItemClickListener() {
                            @Override
                            public void onClick(int which) {
                                PermissionHelper.requestPermission(ChangeMyBgActivity.this, ChangeMyBgActivity.this.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        mCaptureImgName = FileUtils.createImageFileName(ChangeMyBgActivity.this, CAPTURE_IMG);
                                        SelectPicPopupEntity entity = new SelectPicPopupEntity(ChangeMyBgActivity.this);
                                        entity.startCamera(mCaptureImgName);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {

                                    }
                                }, Manifest.permission.CAMERA);
                            }
                        })
                .addSheetItem(getString(R.string.me_album2), IosActionSheetDialogNew.SheetItemColor.BLACK2,
                        new IosActionSheetDialogNew.OnSheetItemClickListener() {
                            @Override
                            public void onClick(int which) {
                                PermissionHelper.requestPermission(ChangeMyBgActivity.this, getResources().getString(R.string.me_request_permission_storage_normal), new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        SelectPicPopupEntity entity = new SelectPicPopupEntity(ChangeMyBgActivity.this);
                                        entity.selectPictureFormGallery();
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {

                                    }
                                }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                            }
                        })
                .show();
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        SelectPicPopupEntity entity = new SelectPicPopupEntity(this);
        switch (requestCode) {
            case SelectPicPopupEntity.PHOTO_REQUEST_TAKEPHOTO:
                if (resultCode != 0) {
                    mCropImgName = FileUtils.createImageFileName(this, HEAD_PHOTO_FILE_NAME);
                    Uri uri = CategoriesKt.getFileUri(this, entity.getFile(mCaptureImgName));
                    entity.startPictureCrop(uri, 375, 230, 1125, 690,
                            mCropImgName,
                            SelectPicPopupEntity.PHOTO_REQUEST_CUT, this, true);
                }
                break;
            case SelectPicPopupEntity.PHOTO_REQUEST_GALLERY:
                if (data != null) {
                    mCropImgName = FileUtils.createImageFileName(this, HEAD_PHOTO_FILE_NAME);
                    entity.startPictureCrop(data.getData(), 375, 230, 1125, 690,
                            mCropImgName,
                            SelectPicPopupEntity.PHOTO_REQUEST_CUT, this, false);
                }
                break;
            case SelectPicPopupEntity.PHOTO_REQUEST_CUT:
                if (mCaptureImgName != null && !mCaptureImgName.isEmpty()) {
                    File captureFile = entity.getFile(mCaptureImgName);
                    if (captureFile != null && captureFile.exists()) {
                        captureFile.delete();
                    }
                }
                if (data != null) {
                    uploadSetBg(entity.getFile(mCropImgName));
                    ExpJDMAUtil.onHomePageEventClick(ExpJDMAConstants.EXP_MAIN_HOME_CHANGE_COVER_CONFIRM, new HashMap<>());
                }
                break;

        }
    }


    public void uploadSetBg(final File iconFile) {
        UploadView.uploading(this);
        SelectPicPopupEntity entity = new SelectPicPopupEntity(this);
        File file = entity.getFile(mCropImgName);
        MyHomePageRepo.changeBGImage(file, new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                UploadView.failed(ChangeMyBgActivity.this);
                setResult(111);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (!TextUtils.isEmpty(info.result)) {
                    iconFile.delete();
                    try {
                        ExpUploadData expUploadData = new Gson().fromJson(info.result, ExpUploadData.class);
                        String url = expUploadData.getContent().getUrls().get(0);

                        Glide.with(ChangeMyBgActivity.this).load(URLDecoder.decode(url, "UTF-8")).into(iv);
                        UploadView.success(ChangeMyBgActivity.this);
                        changeBgUrl(url);
                    } catch (Exception e) {
                        e.printStackTrace();
                        UploadView.failed(ChangeMyBgActivity.this);
                        setResult(111);
                    }
                }
            }

            @Override
            public void onLoading(long total, long current, boolean isUploading) {
                super.onLoading(total, current, isUploading);
//                Log.e("666666", "onLoading: " + "==" + total + "==" + current + "==" + isUploading);
            }
        });
    }


    public void changeBgUrl(final String url) {
        new MyHomePageRepo().saveCustomImage(url, new LoadDataCallback<HomePageData>() {
            @Override
            public void onDataLoaded(HomePageData homePageData) {
                Intent intent = getIntent().putExtra("url", url);
                setResult(Activity.RESULT_OK, intent);
//                finish();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }


    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public static class TransitionCallBack extends SharedElementCallback {
        @Override
        public Parcelable onCaptureSharedElementSnapshot(View sharedElement, Matrix viewToGlobalMatrix, RectF screenBounds) {
            sharedElement.setAlpha(1);
            return super.onCaptureSharedElementSnapshot(sharedElement, viewToGlobalMatrix, screenBounds);
        }

        @Override
        public void onSharedElementStart(List<String> sharedElementNames, List<View> sharedElements, List<View> sharedElementSnapshots) {
            if (sharedElements != null && sharedElements.size() >= 1) {
                sharedElements.get(0).setAlpha(1);
            }
            super.onSharedElementStart(sharedElementNames, sharedElements, sharedElementSnapshots);
        }
    }
}