package com.jd.oa.experience.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.experience.model.HRAppsData;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;

public class JoyHRAppsRepo {
    private volatile static JoyHRAppsRepo sInstance;

    private Gson mGson;

    public static JoyHRAppsRepo get(Context context) {
        if (sInstance == null) {
            synchronized (JoyHRAppsRepo.class) {
                if (sInstance == null) {
                    sInstance = new JoyHRAppsRepo(context);
                }
            }
        }
        return sInstance;
    }

    private JoyHRAppsRepo(Context context) {
        mGson = new Gson();
    }

    public void getHRApps(final LoadDataCallback<HRAppsData> callback) {
        HttpManager.color().post(null, new HashMap<String, String>(), "exp.v3.getAllHrApp", new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<HRAppsData> response = ApiResponse.parse(info.result, new TypeToken<HRAppsData>() {}.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        });
    }
}
