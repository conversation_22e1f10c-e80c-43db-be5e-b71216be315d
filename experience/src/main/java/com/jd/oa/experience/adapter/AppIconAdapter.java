package com.jd.oa.experience.adapter;

import static com.jd.oa.JDMAConstants.Main_JoyHR_FORMAT;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.elliptical.SuperEllipticalImageView;
import com.jd.oa.experience.R;
import com.jd.oa.experience.model.HRAppsData;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

// AppIconAdapter.java
public class AppIconAdapter extends RecyclerView.Adapter<AppIconAdapter.AppIconViewHolder> {
    private List<HRAppsData.HRApp> hrAppList;
    private String categoryType;
    private static final String PARAM_NAME = "jdme_Tabbar_click_parameters";

    public AppIconAdapter(List<HRAppsData.HRApp> hrAppList, String categoryType) {
        this.hrAppList = hrAppList;
        this.categoryType = categoryType;
    }

    @NonNull
    @Override
    public AppIconViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.jdme_item_hrapps_group_item, parent, false);
        return new AppIconViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AppIconViewHolder holder, int position) {
        holder.bind(hrAppList.get(position), categoryType);
    }

    @Override
    public int getItemCount() {
        if (hrAppList != null) {
            return hrAppList.size();
        }
        return 0;
    }

    static class AppIconViewHolder extends RecyclerView.ViewHolder {
        private final SuperEllipticalImageView ivIcon;
        private final TextView tvName;
        private final ConstraintLayout mContainer;

        public AppIconViewHolder(View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_app_name);
            ivIcon = itemView.findViewById(R.id.iv_app_icon);
            mContainer = itemView.findViewById(R.id.cl_app_container);
        }

        public void bind(HRAppsData.HRApp hrAppInfo, String categoryType) {
            if (hrAppInfo != null) {
                if (ivIcon != null && ivIcon.getContext() != null) {
                    Glide.with(ivIcon.getContext()).load(hrAppInfo.icon).into(ivIcon);
                }
                tvName.setText(hrAppInfo.title);
                mContainer.setOnClickListener(v -> {
                    Context context = mContainer.getContext();
                    if (!TextUtils.isEmpty(hrAppInfo.url) && context != null) {
                        Map<String, String> param = new HashMap<>();
                        param.put(PARAM_NAME, categoryType);
                        String eventId = Main_JoyHR_FORMAT + hrAppInfo.id;
                        JDMAUtils.clickEvent(eventId, eventId, param);
                        Router.build(hrAppInfo.url).go(context, new RouteNotFoundCallback(context));
                    }
                });
            }
        }
    }
}