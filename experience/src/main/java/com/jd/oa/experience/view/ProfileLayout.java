package com.jd.oa.experience.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.experience.R;
import com.jd.oa.experience.adapter.ProfileBadgeAdapter;
import com.jd.oa.experience.constants.ExpJDMAConstants;
import com.jd.oa.experience.fragment.ExperienceSelfInfoFragment;
import com.jd.oa.experience.model.GlobalData;
import com.jd.oa.experience.util.Constants;
import com.jd.oa.experience.util.ExpJDMAUtil;
import com.jd.oa.experience.util.GlobalDataUtil;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.ImageLoader;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProfileLayout extends LinearLayout {

    //第一行
    private TextView mNameTv;

    //第二行
    private View mBadgeAndSeniorityLineLayout;
    private View mCultureContainer;
    private View mCultureLayout;
    private ImageView mCultureIv;
    private TextView mCultureTv;
    private SeniorityTextLayout mSeniorityLayout;
    private LinearLayout mProfileEntryLayout;

    //第三行，原第二行
    private View mSecondLineLayout;
    private RecyclerView mRvWorthBadge;

    private View mSeparator;

    private View mLayoutEnergy;
    private View mLayoutEnergyBkGnd;
    private TextView mEnergyLevelTv;
    private TextView mEnergyTv;
    private IconFontView mEnergyBtn;

    //第三行
    private TextView mSignatureTv;
    private TextView mSignatureEditTv;
    private IconFontView mSignatureBtn;
    private IconFontView mSignatureEditBtn;
    private View mLayoutEditSignature;
    private View mLayoutSignature;

    //private View mTagsLayout;
    //private RecyclerView mRvLabels;
    //private View mPaddingView;

    //第四行，部分非灰度用户展示
    private View mSocialLayout;

    //获赞
    private View mLikedLayout;
    private TextView mLikedCountTv;
    private TextView mLikedCountNameTv;
    private View mLikedRedDot;

    //点赞
    private View mLikeLayout;
    private TextView mLikeCountTv;
    private TextView mLikeCountNameTv;

    private View mHomePageEntryLayout;
    private TextView mHomePageEntryTv;
    private IconFontView mHomePageEntryArrow;

    private Context mContext;
    private GlobalData mGlobalData;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public ProfileLayout(Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public ProfileLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    public ProfileLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }

    private void initView() {
        View parent = LayoutInflater.from(mContext).inflate(R.layout.jdme_experience_profile_section, this);
        mNameTv = parent.findViewById(R.id.tv_name);
        mBadgeAndSeniorityLineLayout = parent.findViewById(R.id.badge_and_seniority_line);
        mCultureContainer = parent.findViewById(R.id.culture_wrapper);
        mCultureLayout = parent.findViewById(R.id.layout_culture);
        mCultureIv = parent.findViewById(R.id.iv_culture);
        mCultureTv = parent.findViewById(R.id.tv_culture);
        mSeniorityLayout = parent.findViewById(R.id.layout_seniority);
        mProfileEntryLayout = parent.findViewById(R.id.homepage_top_entry_layout);

        mSecondLineLayout = parent.findViewById(R.id.second_line);
        mRvWorthBadge = parent.findViewById(R.id.rv_worth_badge);
        mSeparator = parent.findViewById(R.id.separator);
        mLayoutEnergy = parent.findViewById(R.id.layout_energy);
        mLayoutEnergyBkGnd = parent.findViewById(R.id.layout_energy_background);
        mEnergyLevelTv = parent.findViewById(R.id.tv_energy_level);
        mEnergyTv = parent.findViewById(R.id.tv_energy);
        mEnergyBtn = parent.findViewById(R.id.btn_energy);

        mSignatureTv = parent.findViewById(R.id.tv_signature);
        mSignatureEditTv = parent.findViewById(R.id.tv_edit_signature);
        mSignatureBtn = parent.findViewById(R.id.btn_signature);
        mSignatureEditBtn = parent.findViewById(R.id.btn_edit_signature);
        mLayoutEditSignature = parent.findViewById(R.id.layout_edit_signature);
        mLayoutSignature = parent.findViewById(R.id.layout_signature);

        //mTagsLayout = parent.findViewById(R.id.tags_area_layout);
        //mRvLabels = parent.findViewById(R.id.rv_labels);
        //mPaddingView = parent.findViewById(R.id.no_label_padding_view);

        mSocialLayout = parent.findViewById(R.id.social_layout);

        mLikedLayout = parent.findViewById(R.id.liked_layout);
        mLikedCountTv = parent.findViewById(R.id.tv_liked_count);
        mLikedCountNameTv = parent.findViewById(R.id.tv_liked_count_name);
        mLikedRedDot = parent.findViewById(R.id.liked_red_dot);

        mLikeLayout = parent.findViewById(R.id.like_layout);
        mLikeCountTv = parent.findViewById(R.id.tv_like_count);
        mLikeCountNameTv = parent.findViewById(R.id.tv_like_count_name);

        mHomePageEntryLayout = parent.findViewById(R.id.homepage_entry_layout);
        mHomePageEntryTv = parent.findViewById(R.id.tv_homepage_entry);
        mHomePageEntryArrow = parent.findViewById(R.id.arrow_homepage_entry);
    }

    public void setData(GlobalData data) {
        mGlobalData = data;
        refreshUI();
    }

    public void refreshUI() {
        if (mGlobalData == null || mGlobalData.userInfo == null) {
            return;
        }
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        boolean darkColor;
        if (themeData != null) {
            String imageType = themeData.imageType;
            if (themeData.getJson().length() == 0) {
                imageType = "01";
            }
            darkColor = "02".equals(imageType);
        } else {
            darkColor = mGlobalData.bgImage != null && "02".equals(mGlobalData.bgImage.imageType);
            boolean hasError = mGlobalData.bgImage == null
                    || TextUtils.isEmpty(mGlobalData.bgImage.color1)
                    || TextUtils.isEmpty(mGlobalData.bgImage.color2)
                    || TextUtils.isEmpty(mGlobalData.bgImage.color3);
            if (hasError) {
                darkColor = false;
            }
        }
        setColorMode(darkColor);

        // 姓名
        mNameTv.setText(mGlobalData.userInfo.realName);
        mNameTv.setOnClickListener(new NoDoubleClickListener() {
            @Override
            public void onNoDoubleClick(View v) {
                openUserInfo();
            }
        });
        PreferenceManager.UserInfo.setUserRealName(mGlobalData.userInfo.realName);

        //生日，司龄，党员
        showCultureImage();
        //司龄text
        showSeniority(darkColor);
        setSecondLineVisibility();
        //签名
        showSignature();

        if ((mGlobalData.badge == null || mGlobalData.badge.list == null || mGlobalData.badge.list.isEmpty()) && mGlobalData.energy == null) {
            mSecondLineLayout.setVisibility(GONE);
        } else {
            mSecondLineLayout.setVisibility(VISIBLE);
        }
        //价值观徽章展示区
        showWorthBadgeList();
        //分隔线
        showSeparator();
        //能量值
        showEnergy();

        boolean showSocialInSection = mGlobalData.checkHasInteractionInTab();
        if (mGlobalData.social != null && mGlobalData.social.isShow && !showSocialInSection) {//点赞，个人主页入口
            //mTagsLayout.setVisibility(View.GONE);
            mSocialLayout.setVisibility(View.VISIBLE);
            showSocial();
        } else {//个性化标签
            //mTagsLayout.setVisibility(View.VISIBLE);
            mSocialLayout.setVisibility(View.GONE);
            //showTags(darkColor);
        }

        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_SIGNATURE.keyName
                        , ExpJDMAConstants.MainInfo.MAIN_INFO_SIGNATURE.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);

                if (mContext instanceof AppCompatActivity) {
                    String hint = mContext.getString(R.string.exp_signature_hint);
                    String title = mContext.getString(R.string.exp_signature_title);
                    imDdService.openSignatureEdit((AppCompatActivity)mContext, title, hint, new Callback<CharSequence>() {
                        @Override
                        public void onSuccess(CharSequence formatSignature) {
                            if (!TextUtils.isEmpty(formatSignature)) {
                                mLayoutSignature.setVisibility(View.VISIBLE);
                                mLayoutEditSignature.setVisibility(View.GONE);
                                mSignatureTv.setText(formatSignature.toString());
                            } else {
                                mLayoutSignature.setVisibility(View.GONE);
                                mLayoutEditSignature.setVisibility(View.VISIBLE);
                            }
                        }

                        @Override
                        public void onFail() {

                        }
                    });
                }
            }
        };
        mLayoutSignature.setOnClickListener(onClickListener);
        mLayoutEditSignature.setOnClickListener(onClickListener);
    }

    private void setSecondLineVisibility() {
        //检查生日，司龄，党员icon是否显示
        boolean isCultureLayoutHidden = mCultureContainer != null && mCultureContainer.getVisibility() == View.GONE;
        //检查在职时间是否显示
        boolean isSeniorityLayoutHidden = mSeniorityLayout != null && mSeniorityLayout.getVisibility() == View.GONE;
        //检查个人中心入口是否显示
        boolean isProfileEntryHidden = mProfileEntryLayout != null && mProfileEntryLayout.getVisibility() == View.GONE;
        //在职时间与生日，司龄，党员icon都不显示则不显示第二行
        if (mBadgeAndSeniorityLineLayout != null) {
            if (isCultureLayoutHidden && isSeniorityLayoutHidden && isProfileEntryHidden) {
                mBadgeAndSeniorityLineLayout.setVisibility(View.GONE);
            } else {
                mBadgeAndSeniorityLineLayout.setVisibility(View.VISIBLE);
            }
        }
    }

    private void showSeniority(boolean darkColor) {
        if (mGlobalData.entryInfo == null || TextUtils.isEmpty(mGlobalData.entryInfo.days)) {
            mSeniorityLayout.setVisibility(View.GONE);
            return;
        }
        int days = -1;
        int years = -1;
        try {
            days = Integer.parseInt(mGlobalData.entryInfo.days);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            years = Integer.parseInt(mGlobalData.entryInfo.years);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (days < 0) {
            mSeniorityLayout.setVisibility(View.GONE);
            return;
        }
        mSeniorityLayout.setVisibility(View.VISIBLE);
        mSeniorityLayout.setData(mGlobalData.entryInfo.url, days, years, darkColor);
    }

    private void showCultureImage() {
        if (mGlobalData == null || mGlobalData.userInfo == null) {
            mCultureContainer.setVisibility(View.GONE);
            return;
        }
        int resId = GlobalDataUtil.getCultureImage(mGlobalData);
        if (resId <= 0) {
            mCultureContainer.setVisibility(View.GONE);
            return;
        }

        mCultureContainer.setVisibility(View.VISIBLE);
        mCultureLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(Constants.ACTION_SHOW_ANNIVERSARY));
            }
        });

        ImageLoader.load(mContext, mCultureIv, resId);
        String type = GlobalDataUtil.getCultureType(mGlobalData.userInfo);
        if (mGlobalData.entryInfo != null && GlobalDataUtil.CULTURE_TYPE_SENIORITY.equals(type)) {
            mCultureTv.setVisibility(View.VISIBLE);
            mCultureTv.setText(String.valueOf(mGlobalData.entryInfo.years));
        } else {
            mCultureTv.setVisibility(View.GONE);
        }
    }

    @SuppressLint("SetTextI18n")
    private void showWorthBadgeList() {
        List<String> badges = new ArrayList<>();
        if (mGlobalData.badge != null && mGlobalData.badge.list != null && !mGlobalData.badge.list.isEmpty()) {
            badges.addAll(mGlobalData.badge.list);
        }
        mRvWorthBadge.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
        mRvWorthBadge.setAdapter(new ProfileBadgeAdapter(mContext, badges, new ProfileBadgeAdapter.OnItemClickListener() {
            @Override
            public void onItemClick() {
                if (mGlobalData != null && mGlobalData.badge != null && !TextUtils.isEmpty(mGlobalData.badge.url)) {
                    if (!TextUtils.isEmpty(mGlobalData.badge.appId)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("appId", mGlobalData.badge.appId);
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_CULTURE, map);
                    }
                    Router.build(mGlobalData.badge.url).go(mContext);
                }
            }
        }));
    }

    private void showSeparator() {
        if (mGlobalData.badge == null || mGlobalData.badge.list == null || mGlobalData.badge.list.isEmpty() || mGlobalData.energy == null) {
            mSeparator.setVisibility(View.GONE);
        } else {
            mSeparator.setVisibility(View.VISIBLE);
        }
    }

    @SuppressLint("SetTextI18n")
    private void showEnergy() {
        if (mGlobalData.energy == null) {
            mLayoutEnergy.setVisibility(View.GONE);
            return;
        }

        mLayoutEnergy.setVisibility(View.VISIBLE);
        mLayoutEnergy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mGlobalData != null && mGlobalData.energy != null && !TextUtils.isEmpty(mGlobalData.energy.url)) {
                    if (!TextUtils.isEmpty(mGlobalData.energy.appId)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("appId", mGlobalData.energy.appId);
                        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_CULTURE, map);
                    }
                    Router.build(mGlobalData.energy.url).go(mContext);
                }
            }
        });
        mEnergyLevelTv.setText("Lv." + (TextUtils.isEmpty(mGlobalData.energy.level) ? "1" : mGlobalData.energy.level));
        int value = 0;
        try {
            value = Integer.parseInt(mGlobalData.energy.value);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String energyValue = value > 9999 ? "9999+" : String.valueOf(value);
        mEnergyTv.setText(energyValue);
        mEnergyBtn.setVisibility(!TextUtils.isEmpty(mGlobalData.energy.url) ? View.VISIBLE : View.GONE);
    }

    public void showProfileEntry() {
        if (mProfileEntryLayout != null) {
            mProfileEntryLayout.setVisibility(View.VISIBLE);
            mProfileEntryLayout.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    String payload = "{\"userName\":\"" + PreferenceManager.UserInfo.getUserName() + "\"}";
                    Router.build(DeepLink.EXP_MY_JOYSPACE + "?mparam=" + Uri.encode(payload)).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_HOMEPAGE.keyName, ExpJDMAConstants.MainInfo.MAIN_INFO_HOMEPAGE.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
                }
            });
        }
    }

    public void showSignature() {
        CharSequence text = imDdService.getMyFormatSignature();
        if (!TextUtils.isEmpty(text)) {
            mLayoutSignature.setVisibility(View.VISIBLE);
            mLayoutEditSignature.setVisibility(View.GONE);
            mSignatureTv.setText(text.toString());
        } else {
            mLayoutSignature.setVisibility(View.GONE);
            mLayoutEditSignature.setVisibility(View.VISIBLE);
        }
    }

    private void openUserInfo() {//个人信息
        Intent intent = new Intent(mContext, FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ExperienceSelfInfoFragment.class.getName());
        mContext.startActivity(intent);
        Map<String, String> map = new HashMap<>();
        map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_NAME.keyName, ExpJDMAConstants.MainInfo.MAIN_INFO_NAME.KeyValue);
        ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);

//        JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_headIcon_click, JDMAConstants.mobile_Mine_headIcon_click);
    }

    private void setColorMode(boolean darkColor) {
        int textColor = darkColor ?
                mContext.getResources().getColor(R.color.color_text_dark) : mContext.getResources().getColor(R.color.color_text_normal);
        mNameTv.setTextColor(textColor);
        mEnergyTv.setTextColor(textColor);

        int signatureColor = darkColor ?
                mContext.getResources().getColor(R.color.color_signature_dark) : mContext.getResources().getColor(R.color.color_signature_normal);
        mSignatureTv.setTextColor(signatureColor);
        mSignatureEditTv.setTextColor(signatureColor);

        int arrowColor = darkColor ?
                mContext.getResources().getColor(R.color.color_arrow_dark) : mContext.getResources().getColor(R.color.color_arrow_normal);
        mEnergyBtn.setTextColor(arrowColor);
        mEnergyBtn.setTextColor(arrowColor);
        mSignatureBtn.setTextColor(arrowColor);
        mSignatureEditBtn.setTextColor(arrowColor);

        int levelColor = darkColor ?
                mContext.getResources().getColor(R.color.color_level_dark) : mContext.getResources().getColor(R.color.color_level_normal);
        mEnergyLevelTv.setTextColor(levelColor);

        mLayoutEnergyBkGnd.setBackgroundResource(darkColor ? R.drawable.jdme_bg_experience_energy_dark : R.drawable.jdme_bg_experience_energy_light);

        mSeparator.setEnabled(darkColor);

        mLikedCountTv.setTextColor(darkColor ? 0xFFFFFFFF : 0xFF000000);
        mLikedCountNameTv.setTextColor(darkColor ? 0xFFFFFFFF : 0xFF62656D);
        mLikedRedDot.setEnabled(darkColor);
        mLikeCountTv.setTextColor(darkColor ? 0xFFFFFFFF : 0xFF000000);
        mLikeCountNameTv.setTextColor(darkColor ? 0xFFFFFFFF : 0xFF62656D);
        mHomePageEntryLayout.setBackgroundResource(darkColor ? R.drawable.jdme_bg_homepage_entry_dark : R.drawable.jdme_bg_homepage_entry_light);
        mHomePageEntryTv.setTextColor(darkColor ? 0xFFFFFFFF : 0xFF666666);
        mHomePageEntryArrow.setTextColor(darkColor ? 0xFFE6E6E6 : 0xFF8F959E);
    }

    private void showSocial() {
        //获赞
        if (mGlobalData.social.liked != null) {
            mLikedCountTv.setText(TextUtils.isEmpty(mGlobalData.social.liked.count) ? "0" : mGlobalData.social.liked.count);
            mLikedRedDot.setVisibility(mGlobalData.social.liked.hasNew ? View.VISIBLE : View.INVISIBLE);
        } else {
            mLikedCountTv.setText("0");
            mLikedRedDot.setVisibility(View.INVISIBLE);
        }
        mLikedLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mGlobalData.social.liked != null && !TextUtils.isEmpty(mGlobalData.social.liked.url)) {
                    Uri uri = Uri.parse(mGlobalData.social.liked.url);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_LIKED.keyName, ExpJDMAConstants.MainInfo.MAIN_INFO_LIKED.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
                }
            }
        });

        //点赞
        if (mGlobalData.social.like != null && !TextUtils.isEmpty(mGlobalData.social.like.count)) {
            mLikeCountTv.setText(mGlobalData.social.like.count);
        } else {
            mLikeCountTv.setText("0");
        }
        mLikeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mGlobalData.social.like != null && !TextUtils.isEmpty(mGlobalData.social.like.url)) {
                    Uri uri = Uri.parse(mGlobalData.social.like.url);
                    if (uri == null || uri.getScheme() == null) {
                        return;
                    }
                    Router.build(uri).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_LIKE.keyName, ExpJDMAConstants.MainInfo.MAIN_INFO_LIKE.KeyValue);
                    ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
                }
            }
        });

        //个人主页入口，没有deeplink不展示个人主页入口
        mHomePageEntryLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mHomePageEntryLayout.getVisibility() != View.VISIBLE) {
                    return;
                }
                //jdme://jm/biz/myspace?mparam={"userName":""}
                String payload = "{\"userName\":\"" + PreferenceManager.UserInfo.getUserName() + "\"}";
                Router.build(DeepLink.EXP_MY_JOYSPACE + "?mparam=" + Uri.encode(payload)).go(mContext, new RouteNotFoundCallback(mContext));
                Map<String, String> map = new HashMap<>();
                map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_HOMEPAGE.keyName, ExpJDMAConstants.MainInfo.MAIN_INFO_HOMEPAGE.KeyValue);
                ExpJDMAUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
            }
        });
    }

    public void updateSocial(String likeCount, String likedCount) {
        if (!TextUtils.isEmpty(likeCount)) {
            mLikeCountTv.setText(likeCount);
        }
        if (!TextUtils.isEmpty(likedCount)) {
            mLikedCountTv.setText(likedCount);
            mLikedRedDot.setVisibility(View.VISIBLE);
        }
    }

/*    private void showTags(boolean darkColor) {
        if (mGlobalData.tags == null) {//没有tags字段时，不展示标签区域
            mTagsLayout.setVisibility(View.GONE);
            return;
        }
        mTagsLayout.setVisibility(View.VISIBLE);
        ArrayList<String> list = new ArrayList<>();
        float totalWidth = 0;
        int screenWidth = DisplayUtil.getScreenWidth(mContext);
        float maxWidth = screenWidth - DensityUtil.dp2px(mContext, 10 + 34 + 16 + 4);//leftMargin + 箭头 + rightMargin + 4
        if (mGlobalData.tags.list != null && !mGlobalData.tags.list.isEmpty()) {
            for (String tag : mGlobalData.tags.list) {
                if (TextUtils.isEmpty(tag.trim())) {
                    continue;
                }
                float width = TextHelper.getSingleLineTextWidth(tag, DensityUtil.dp2px(mContext, 12));//textView
                width += DensityUtil.dp2px(mContext, 16 + 6);//TextView marginStart & marginEnd + marginStart
                width += 12;//paddingStart + paddingEnd
                if (totalWidth + width > maxWidth) {
                    break;
                }
                list.add(tag.trim());
                totalWidth += width;
            }
        }
        list.add("");//> or 添加标签

        mPaddingView.setVisibility(list.size() == 1 ? View.VISIBLE : View.GONE);
        mRvLabels.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
        mRvLabels.setAdapter(new ProfileLabelsAdapter(mContext, list, darkColor, new ProfileLabelsAdapter.OnItemClickListener() {
            @Override
            public void onItemClick() {
                if (mGlobalData.tags != null && StringUtils.isNotEmptyWithTrim(mGlobalData.tags.url) && !NClick.isFastDoubleClick()) {
                    Router.build(mGlobalData.tags.url).go(mContext, new RouteNotFoundCallback(mContext));
                    Map<String, String> map = new HashMap<>();
                    map.put(ExpJDMAConstants.MainInfo.MAIN_INFO_TAGS.keyName, ExpJDMAConstants.MainInfo.MAIN_INFO_TAGS.KeyValue);
                    ThemeJdMaUtil.onEventClick(ExpJDMAConstants.EXP_MAIN_INFO, map);
                }
            }
        }));
    }*/
}