package com.jd.oa.experience.section

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.jd.oa.experience.R
import com.jd.oa.utils.*

class BannerImageView(context: Context, attrs: AttributeSet?) : FrameLayout(context, attrs) {
    init {
        context.inflater.inflate(R.layout.jdme_item_experience_section_banner_img, this, true)
    }

    fun bindWithData(url: String?, txt: String?) {
        val img = findViewById<ImageView>(R.id.mImg)
        ImageLoader.load(
            context,
            img,
            url ?: "",
            R.drawable.jdme_self_info_image_default_plain
        )
        val tv = findViewById<TextView>(R.id.mText)
        val shadowView = findViewById<View>(R.id.mShadow)
        if (txt.isBlankOrNull()) {
            tv.gone()
            shadowView.gone()
        } else {
            tv.visible()
            tv.text = txt ?: ""
            shadowView.visible()
        }
    }
}