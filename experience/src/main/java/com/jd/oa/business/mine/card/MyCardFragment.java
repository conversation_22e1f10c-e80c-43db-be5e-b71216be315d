package com.jd.oa.business.mine.card;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.experience.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.CircleImageView;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;

/**
 * 我的二维码名片
 */
@Route(DeepLink.MY_QR_CARD)
public class MyCardFragment extends BaseFragment implements CardConstraint.ICardView {
    private View mRootView;
    private CircleImageView mAvator;
    private ImageView mQt;
    private TextView mPosition, mName;
    private CardPresenter presenter;
    private String mQrURL;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_fragment_my_card, container, false);
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_self_qtcard);
        ActionBarHelper.getActionBar(this).setDisplayHomeAsUpEnabled(true);
        setHasOptionsMenu(true);
        initViews();
        initData();
        return mRootView;
    }

    private void initData() {
        presenter = new CardPresenter(this);
        presenter.getData(false);
    }

    private void initViews() {
        mAvator = (CircleImageView) mRootView.findViewById(R.id.jdme_id_mycard_avator);
        ImageLoaderUtils.getInstance().displayImage(PreferenceManager.UserInfo.getUserCover(), mAvator, R.drawable.jdsaas_personal_default_avatar);
        mName = (TextView) mRootView.findViewById(R.id.jdme_id_mycard_name);
        mPosition = (TextView) mRootView.findViewById(R.id.jdme_id_mycard_position);
        mQt = (ImageView) mRootView.findViewById(R.id.jdme_id_mycard_qt);
        mQt.post(new Runnable() {
            @Override
            public void run() {
                ViewGroup.LayoutParams params = mQt.getLayoutParams();
                params.height = mQt.getWidth();
                mQt.setLayoutParams(params);
            }
        });
        mQt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showBigQt();
            }
        });
    }

    //显示大的二维码界面
    private void showBigQt() {
        if (TextUtils.isEmpty(mQrURL)) {
            return;
        }
        new BigQrDialog(getActivity(), mQrURL).show();
    }

    @Override
    public void onEnd(CardInfo info, String errorMsg, boolean success) {
        if (success) {
            ImageLoaderUtils.getInstance().displayImage(info.getVisitingCardQRCode(), mQt, R.drawable.jdme_mine_main_qt);
            mQrURL = info.getVisitingCardQRCode();
            mPosition.setText(info.getOrganizationName() + "-" + info.getPositionName());
            mName.setText(info.getRealName() + " " + info.getUserName());
        } else {
            ToastUtils.showInfoToast(errorMsg);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (presenter != null) {
            presenter.onDestory();
        }
    }
}
