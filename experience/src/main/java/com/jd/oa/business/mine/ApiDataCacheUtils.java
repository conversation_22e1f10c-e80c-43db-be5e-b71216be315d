package com.jd.oa.business.mine;

import android.text.TextUtils;

import com.jd.oa.bundles.maeutils.utils.FileUtils;
import com.jd.oa.storage.UseType;
import com.jd.oa.cache.FileCache;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jd.oa.utils.encrypt.MD5Utils;

import java.io.File;

public class ApiDataCacheUtils {


    public static String getDatafromCache(String api) {
        String content = null;
        File file = getCacheFile(api);
        if (file.exists() && file.isFile()) {
            content = FileUtils.getFileContent(file);
        }
        if (TextUtils.isEmpty(content))
            return "";
        return JdmeEncryptUtil.getDecryptString(content);
    }

    public static void updateCache(String s, final String api) {
        File file = getCacheFile(api);
        String content = FileUtils.getFileContent(file);
        String string;
        if (TextUtils.isEmpty(content)) {
            string = "";
        } else {
            string = JdmeEncryptUtil.getDecryptString(content);
        }
        if (!file.exists() || !s.equals(string)) {
            FileUtils.saveFile(JdmeEncryptUtil.getEncryptString(s), file, false);
        }
    }


    private static File getCacheFile(String api) {
        String name = PreferenceManager.UserInfo.getUserName();
        String s = MD5Utils.getMD5(name + api);
        return new File(FileCache.getInstance().getCacheFile(UseType.APP), s + ".json");
    }
}
