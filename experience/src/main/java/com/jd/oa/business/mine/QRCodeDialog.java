package com.jd.oa.business.mine;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.CountDownTimer;
import androidx.annotation.NonNull;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.experience.R;
import com.jd.oa.business.mine.card.CardConstraint;
import com.jd.oa.business.mine.card.CardInfo;
import com.jd.oa.business.mine.card.CardPresenter;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.qrcode.QRCodeUtil;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.DisplayUtil;

/**
 * 用户二维码弹窗
 * Created by peidongbiao on 2018/7/26.
 */

public class QRCodeDialog extends BottomSheetDialog implements CardConstraint.ICardView {
    int i;
    private ImageView mIvAvatar;
    private TextView mTvName;
    private TextView mTvJob;
    private ImageView mIvQrcode;
    private Bitmap qrBitmap;
    private CountDownTimer timer = new CountDownTimer(60000, 1000) {
        @Override
        public void onTick(long millisUntilFinished) {

        }

        @Override
        public void onFinish() {
            if (isShowing() && mPresenter != null) {
                mPresenter.getData(true);
                timer.start();
            }
        }
    };

    private CardConstraint.ICardPresenter mPresenter;

    public QRCodeDialog(@NonNull Context context) {
        this(context, 0);
    }

    public QRCodeDialog(@NonNull Context context, int theme) {
        super(context, theme);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_dialog_user_qrcode, null);
        setContentView(view);
        //设置背景
        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            parent.setBackgroundResource(android.R.color.transparent);
        }
        //设置初始高度
        View bottomSheet = findViewById(R.id.design_bottom_sheet);
        if (bottomSheet != null) {
            BottomSheetBehavior behavior = BottomSheetBehavior.from(bottomSheet);
            behavior.setPeekHeight(DisplayUtil.getScreenHeight(context));
        }
        mIvAvatar = findViewById(R.id.iv_avatar);
        mTvName = findViewById(R.id.tv_name);
        mTvJob = findViewById(R.id.tv_job);
        mIvQrcode = findViewById(R.id.iv_qrcode);
        View mViewTop = findViewById(R.id.view_top);
        LinearLayout mLayoutContainer = findViewById(R.id.layout_container);

        if (mLayoutContainer != null) {
            ViewGroup.LayoutParams layoutParams = mLayoutContainer.getLayoutParams();
            layoutParams.height = (int) (DisplayUtil.getScreenHeight(context) * 0.7f);
            mLayoutContainer.setLayoutParams(layoutParams);
        }


        if (mViewTop != null) {
            mViewTop.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
        }

        mPresenter = new CardPresenter(this);
        mPresenter.getData(true);
        timer.start();
    }

    @Override
    public void onStart() {

    }

    @Override
    public void onEnd(CardInfo qt, String errorMsg, boolean success) {
        if (!isShowing() || !success) return;
        //Glide.with(getContext()).load(qt.getHeadImg()).placeholder(R.mipmap.img_default).into(mIvAvatar);
        ImageLoaderUtils.getInstance().displayImage(PreferenceManager.UserInfo.getUserCover(), mIvAvatar, R.mipmap.img_default);
        mTvName.setText(qt.getRealName());
        mTvJob.setText(qt.getPositionName());
        qrBitmap = QRCodeUtil.createQRCode(qt.getVisitingCardQRCode(), 300, 300);
        mIvQrcode.setImageBitmap(qrBitmap);
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (qrBitmap != null) {
            qrBitmap.recycle();
        }
        if (timer != null) {
            timer.cancel();
        }
        if (mPresenter != null) {
            mPresenter.onDestory();
        }
    }
}
