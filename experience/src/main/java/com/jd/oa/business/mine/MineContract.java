package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Context;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.model.MineInfo;

import java.util.List;

/**
 * Created by peidongbiao on 2018/7/25.
 */

public interface MineContract {

    interface View{
        void showInfo(MineInfo info);
        void showMyApps(List<AppInfo> list);
        void showError(String message);
        void showMessage(String message);
        void showSignature(String signature);
        boolean isAlive();
        //void toWallet(String jdPin);
        //void showBindJdAccountDialog();
        void clearAppTips(String appId);
        void showLoading();
        void dismissLoading();
    }

    interface Presenter{
        void getMyInfo();
        void getMyApps();
        void getSignature();
        void saveSignature(String signature);
        //void checkJdPin();
        void clearAppTips(String appId);
        void destroy();
        void goFeedback();
        void getMyHolidayData(Context context);
        void openNetDisk(Activity activity, String deeplink);
    }
}
