package com.jd.oa.business.mine.card;

import android.content.Context;
import androidx.appcompat.app.AppCompatDialog;
import android.view.WindowManager;
import android.widget.ImageView;

import com.jd.oa.experience.R;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.CommonUtils;

/**
 * Created by h<PERSON><PERSON> on 2016/9/1
 */
public class BigQrDialog extends AppCompatDialog {
    private String url;

    public BigQrDialog(Context context, String url) {
        super(context, R.style.me_ShareDialogStyle);
        this.url = url;
        ImageView view = (ImageView) getLayoutInflater().inflate(R.layout.jdme_dialog_big_qr, null);
        setContentView(view);
        ImageLoaderUtils.getInstance().displayImage(url, view, R.drawable.jdme_mine_main_qt);
        WindowManager.LayoutParams p = getWindow().getAttributes(); // 获取对话框当前的参数值
        int width = CommonUtils.getScreentWidth(context);
        p.height = (int) (width*0.9);
        p.width = (int) (width*0.9);
        getWindow().setAttributes(p); // 设置生效
    }
}
