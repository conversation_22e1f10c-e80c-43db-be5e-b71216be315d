package com.jd.oa.business.index.view;

import static com.jd.oa.utils.CategoriesKt.getFileUri;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageDecoder;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.view.Window;
import android.widget.ImageView;

import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.cache.FileCache;
import com.jd.oa.experience.R;
import com.jd.oa.fragment.ImageShowerFragment;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.SDCardUtils;
import com.jd.oa.utils.ToastUtils;

import java.io.File;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.Fragment;

public class SelectPicPopupEntity {

    public static final int PHOTO_REQUEST_GALLERY = 1; // 从相册中选择
    public static final int PHOTO_REQUEST_TAKEPHOTO = 2; // 拍照
    public static final int PHOTO_REQUEST_CUT = 3; // 结果
    private final String HEAD_PHOTO_FILE_NAME = "user_icon.jpg";
    public static final int USER_ICON_SIZE_WIDTH = 620;
    public static final int USER_ICON_SIZE_HEIGHT = 620;
    //android 11以上使用公有目录
    private String PATH_FOR_SAVE_IMG_Q = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getPath();

    private Fragment mContext;
    private Activity mActivity;

    public SelectPicPopupEntity(Fragment ctx) {
        this.mContext = ctx;
    }

    public SelectPicPopupEntity(Activity ctx) {
        this.mActivity = ctx;
    }

    public void checkSelectPicture() {
        Intent intent;
        if (mContext != null) {
            intent = new Intent(mContext.getActivity(), FunctionActivity.class);
        } else if (mActivity != null) {
            intent = new Intent(mActivity, FunctionActivity.class);
        } else {
            return;
        }
        intent.putExtra("function", ImageShowerFragment.class.getName());
        intent.putExtra("windowFeature", Window.FEATURE_ACTION_BAR_OVERLAY);
        intent.putExtra("imagePath", MyPlatform.getCurrentUser().getUserIcon());
        mContext.startActivity(intent);
    }

    public void selectPictureFormGallery() {
        if (getFile(HEAD_PHOTO_FILE_NAME).exists()) {
            getFile(HEAD_PHOTO_FILE_NAME).delete();
        }
        Intent intent;
        intent = new Intent(Intent.ACTION_PICK, null);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                "image/*");
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        if (mContext != null)
            mContext.startActivityForResult(intent, PHOTO_REQUEST_GALLERY);
        else if (mActivity != null)
            mActivity.startActivityForResult(intent, PHOTO_REQUEST_GALLERY);
    }

    public void startCamera(String fileName) {
        Intent intent;
        if (SDCardUtils.checkSDCardAvailable()) {
            if (getFile(fileName).exists()) {
                getFile(fileName).delete();
            }
            try {
                Context context;
                if (mContext != null) {
                    context = mContext.getContext();
                } else if (mActivity != null) {
                    context = mActivity;
                } else {
                    return;
                }
                intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                Uri imageUri;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    ContentValues values = new ContentValues();
                    values.put(MediaStore.Images.Media.DISPLAY_NAME, fileName);
                    values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                    values.put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES);
                    ContentResolver resolver = context.getContentResolver();
                    imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
                } else {
                    imageUri = getFileUri(context, getFile(fileName));
                }
                intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                if (mContext != null)
                    mContext.startActivityForResult(intent, PHOTO_REQUEST_TAKEPHOTO);
                else if (mActivity != null)
                    mActivity.startActivityForResult(intent, PHOTO_REQUEST_TAKEPHOTO);
            } catch (Exception e) {
                ToastUtils.showToast(R.string.around_get_camera_permission_fail);
                e.printStackTrace();
            }
        } else {
            ToastUtils.showToast(R.string.me_no_sdcard);
        }
    }

    /**
     * 图片缩放 拍照
     *
     * @param fileName
     */
    public void startPhotoZoom(String fileName, int width, int height, String output, int requestCode, Fragment ctx) {
        if (getFile(fileName).exists()) {
            Uri uri = getFileUri(ctx.requireContext(), getFile(fileName));
            startPictureCrop(uri, width, height, output, requestCode, ctx, true);
        }
    }


    /**
     * 图片缩放 从相册选取
     */
    public void startPhotoZoom(Uri uri, int width, int height, String output, int requestCode, Fragment ctx) {
        startPictureCrop(uri, width, height, output, requestCode, ctx, false);
    }

    private void startPictureCrop(
            Uri uri,
            int width,
            int height,
            String output,
            int requestCode,
            Fragment ctx,
            boolean fromCamera
    ) {
        try {
            Intent intent = getIntent(fromCamera);
            intent.setDataAndType(uri, "image/*");
            // crop为true是设置在开启的intent中设置显示的view可以剪裁
            intent.putExtra("crop", "true");
            // aspectX aspectY 是宽高的比例
            intent.putExtra("aspectX", 1);
            intent.putExtra("aspectY", 1);
            // outputX,outputY 是剪裁图片的宽高
            intent.putExtra("outputX", width);
            intent.putExtra("outputY", height);
            intent.putExtra("return-data", false);
            // 去除默认的人脸识别，否则和剪裁匡重叠
            intent.putExtra("noFaceDetection", true);
            if (getFile(output).exists()) {
                getFile(output).delete();
            }
            Uri imageUri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                //storage/emulated/0/Pictures
                ContentValues values = new ContentValues();
                values.put(MediaStore.Images.Media.DISPLAY_NAME, output);
                values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                values.put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES);
                ContentResolver resolver = ctx.requireContext().getContentResolver();
                imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
            } else {
                //storage/emulated/0/Android/data/com.xxxxx/cache
                intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
                imageUri = getFileUri(
                        ctx.requireContext(),
                        new File(FileCache.getInstance().getImageCacheFile(), output)
                );
            }
            intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
            ctx.startActivityForResult(intent, requestCode);
        } catch (Exception e) {
            ToastUtils.showToast(R.string.around_get_camera_permission_fail);
            e.printStackTrace();
        }
    }

    @NonNull
    private static Intent getIntent(boolean fromCamera) {
        Intent intent = new Intent("com.android.camera.action.CROP");
        if (fromCamera) {
            intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
        } else if (!Build.BRAND.equals("xiaomi")
                && !Build.BRAND.equals("Xiaomi")
                && !Build.BRAND.equals("Redmi")
                && !Build.BRAND.equals("blackshark")) {
            intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        }
        return intent;
    }


    /**
     * 更换背景
     *
     * @param uri
     * @param aspectX
     * @param aspectY
     * @param output
     * @param requestCode
     * @param activity
     * @param fromCamera
     */
    public void startPictureCrop(
            Uri uri,
            int aspectX,
            int aspectY,
            int outputX,
            int outputY,
            String output,
            int requestCode,
            Activity activity,
            boolean fromCamera
    ) {
        try {
            Intent intent = getIntent(fromCamera);
            intent.setDataAndType(uri, "image/*");
            // crop为true是设置在开启的intent中设置显示的view可以剪裁
            intent.putExtra("crop", "true");
            // aspectX aspectY 是宽高的比例
            if (Build.MANUFACTURER.equalsIgnoreCase("HUAWEI") && aspectX == aspectY) {
                intent.putExtra("aspectX", 9998);
                intent.putExtra("aspectY", 9999);
            } else {
                intent.putExtra("aspectX", aspectX);
                intent.putExtra("aspectY", aspectY);
            }
            intent.putExtra("circleCrop", false);
            // outputX,outputY 是剪裁图片的宽高
            intent.putExtra("outputX", outputX);
            intent.putExtra("outputY", outputY);
            intent.putExtra("scale", true);
            // 去黑边，使图片支持缩放
            intent.putExtra("scaleUpIfNeeded", true);
            intent.putExtra("return-data", false);
            // 去除默认的人脸识别，否则和剪裁匡重叠
            intent.putExtra("noFaceDetection", true);
            if (getFile(output).exists()) {
                getFile(output).delete();
            }
            Uri imageUri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                //storage/emulated/0/Pictures
                ContentValues values = new ContentValues();
                values.put(MediaStore.Images.Media.DISPLAY_NAME, output);
                values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                values.put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES);
                ContentResolver resolver = activity.getContentResolver();
                imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
            } else {
                //storage/emulated/0/Android/data/com.xxxxx/cache
                intent.putExtra("outputFormat", Bitmap.CompressFormat.JPEG.toString());
                imageUri = getFileUri(
                        activity,
                        new File(FileCache.getInstance().getImageCacheFile(), output)
                );
            }
            intent.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
            activity.startActivityForResult(intent, requestCode);
        } catch (Exception e) {
            ToastUtils.showToast(R.string.around_get_camera_permission_fail);
            e.printStackTrace();
        }
    }


    /**
     * 将进行剪裁后的图片显示到UI界面上
     *
     * @param imageView
     */
    public void setPicToView(final ImageView imageView, String fileName) {
        try {
            Context context;
            if (mContext != null) {
                context = mContext.getActivity();
            } else if (mActivity != null) {
                context = mActivity;
            } else {
                return;
            }
            Bitmap bitmap = ImageUtils.getBitmapFromUri(context,
                    Uri.fromFile(getFile(fileName)));
            if (bitmap != null) {
                ImageUtils.savePhotoToSDCard(bitmap,
                        FileCache.getInstance().getImageCacheFile().getAbsolutePath(),
                        fileName);
                imageView.setImageBitmap(ImageUtils.getRoundedBitmap(bitmap));
                bitmap.recycle();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 将进行剪裁后的图片显示到UI界面上
     */
    @RequiresApi(api = Build.VERSION_CODES.P)
    public void setPicToView(final ImageView imageView, Uri uri) {
        try {
            ImageDecoder.Source source = ImageDecoder.createSource(AppBase.getAppContext().getContentResolver(), uri);
            Bitmap bitmap = ImageDecoder.decodeBitmap(source);
            ImageUtils.savePhotoToSDCard(bitmap,
                    FileCache.getInstance().getImageCacheFile().getAbsolutePath(),
                    "user_icon.jpg");
            imageView.setImageBitmap(bitmap);
            bitmap.recycle();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void setPicToView(final ImageView imageView) {
        try {
            File mOutPutFile = new File(PATH_FOR_SAVE_IMG_Q, HEAD_PHOTO_FILE_NAME);
            Bitmap bitmap = BitmapFactory.decodeFile(mOutPutFile.getPath());
            if (bitmap != null) {
                ImageUtils.savePhotoToSDCard(bitmap,
                        FileCache.getInstance().getImageCacheFile().getAbsolutePath(),
                        "user_icon.jpg");
                imageView.setImageBitmap(bitmap);
                bitmap.recycle();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 获取裁剪后的图片文件
     *
     * @return
     */
    public File getFile(String fileName) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            File imageCacheFile = FileCache.getInstance().getImageCacheFile();
            if (!imageCacheFile.exists()) {
                imageCacheFile.mkdirs();
            }
            return new File(imageCacheFile, fileName/*"user_icon.jpg"*/);
        } else {
            return new File(PATH_FOR_SAVE_IMG_Q, fileName);
        }
    }


}
