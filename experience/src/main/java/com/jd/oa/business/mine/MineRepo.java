package com.jd.oa.business.mine;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.experience.R;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.MineInfo;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;


import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2018/7/25.
 */

public class MineRepo {

    private static MineRepo sInstance;

    private Gson mGson = new Gson();

    public static MineRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new MineRepo(context);
        }
        return sInstance;
    }

    private MineRepo(Context context){

    }

    public void getHomeHead(final LoadDataCallback<MineInfo> callback) {
        SimpleRequestCallback<String> callBack1 = new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);

                ApiResponse<MineInfo> response = ApiResponse.parse(info.result, MineInfo.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                }
            }
        };

        NetWorkManager.getHomeHead(callBack1);
    }

    public void getMyApps(final LoadDataCallback<List<AppInfo>> callback){

        SimpleRequestCallback<String> callBack1 = new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);

                if (response.isSuccessful()) {
                    List<AppInfo> appList = response.getListData(AppInfo.class, "appList");
                    callback.onDataLoaded(appList);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, -1);
            }
        };

        NetWorkManagerAppCenter.getMyAppMenu(callBack1);
    }


    public void checkJdPin(final LoadDataCallback<String> callback){
        NetWorkManager.request(null, NetworkConstant.API_JD_PIN, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    JSONObject obj = new JSONObject(rawData);
                    if (obj.getInt("errorCode") == 0) {
                        jsonObject = obj.getJSONObject("content");
                        String jdPin = jsonObject.optString("jdPin");
                        callback.onDataLoaded(jdPin);
                    } else {
                        onFailure(obj.getString("errorMsg"), ErrorCode.CODE_RETURN_ERROR);
                    }
                } catch (Exception e) {
                    onFailure(AppBase.getAppContext().getString(R.string.me_request_fail), ErrorCode.CODE_PARSE_ERROR);
                }
            }

            @Override
            public void onFailure(String errorMsg, int errorCode) {
                callback.onDataNotAvailable(errorMsg, errorCode);
            }
        }), null);
    }

    public List<AppInfo> getAppsCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_GET_MY_APP_MENU, null);
        if (cache == null || cache.getResponse() == null) return null;
        List<AppInfo> list = mGson.fromJson(cache.getResponse(), new TypeToken<ArrayList<AppInfo>>() {
        }.getType());
        return list;
    }

    public void addAppsToCache(List<AppInfo> list) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_GET_MY_APP_MENU, null, mGson.toJson(list));
    }

    public void clearAppsCache() {
        ResponseCacheGreenDaoHelper.delete(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_GET_MY_APP_MENU, null);
    }

    public void clearAppMessage(String appId, final LoadDataCallback<Boolean> callback){
       NetWorkManagerAppCenter.clearAppMessage(appId, callback);
    }
}