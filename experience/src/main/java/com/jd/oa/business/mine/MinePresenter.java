package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.experience.R;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.netdisk.NetDiskHelper;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.MineInfo;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import static com.jd.oa.utils.Utils.compatibleDeepLink;


/**
 * Created by peidongbiao on 2018/7/25.
 */

public class MinePresenter implements MineContract.Presenter {
    private static final String TAG = "MinePresenter";

    private MineContract.View mView;
    private MineRepo mRepo;
    private Handler mMainHandler;
    private boolean mLoadingApps;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public MinePresenter(Context context, MineContract.View view) {
        mView = view;
        mRepo = MineRepo.get(context);
        mMainHandler = new Handler(Looper.getMainLooper());
    }

    @Override
    public void getMyInfo() {
        mRepo.getHomeHead(new LoadDataCallback<MineInfo>() {
            @Override
            public void onDataLoaded(final MineInfo data) {
                mMainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (isDead()) return;
                        try {
                            mView.showInfo(data);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }

            @Override
            public void onDataNotAvailable(final String errMsg, int errCode) {
                if (isDead()) return;

                mMainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (isDead()) return;
                        mView.showError(errMsg);
                    }
                });
            }
        });
    }


    private boolean isDead() {
        return mView == null || !mView.isAlive();
    }

    @Override
    public void getMyApps() {
        final List<AppInfo> cache = mRepo.getAppsCache();
        if (cache != null) {
            if (isDead()) return;
            mView.showMyApps(cache);
        }
        if (mLoadingApps) return;
        mLoadingApps = true;
        mRepo.getMyApps(new LoadDataCallback<List<AppInfo>>() {
            @Override
            public void onDataLoaded(final List<AppInfo> data) {
                if (isDead()) return;
                mLoadingApps = false;
                if (!CollectionUtil.isEquals(cache, data)) {
                    mRepo.addAppsToCache(data);
                    mMainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (isDead()) return;
                            mView.showMyApps(data);
                        }
                    });

                }
            }

            @Override
            public void onDataNotAvailable(final String errMsg, int errCode) {
                if (isDead()) return;
                mLoadingApps = false;

                mMainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (isDead()) return;
                        mView.showError(errMsg);
                    }
                });
            }
        });
    }

    @Override
    public void getSignature() {
        String sign = imDdService.getMySignature();
        mView.showSignature(sign);
    }

    @Override
    public void saveSignature(final String signature) {
        imDdService.modifySignature(signature, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String data) {
                mView.showSignature(signature);
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                ToastUtils.showToast(R.string.me_info_save_signature_fail);
            }
        });
    }

/*    @Override
    public void checkJdPin() {
        mRepo.checkJdPin(new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String data) {
                if (isDead()) return;
                if (TextUtils.isEmpty(data)) {
                    mView.showBindJdAccountDialog();
                } else {
                    mView.toWallet(data);
                }
            }

            @Override
            public void onDataNotAvailable(String errMsg, int errCode) {
                if (isDead()) return;
                Logger.d(TAG, errMsg);

                if (AbsReqCallback.ErrorCode.CODE_RETURN_ERROR == errCode) {
                    mView.showBindJdAccountDialog();

                } else {
                    mView.showMessage(errMsg);
                }
            }
        });
    }*/

    @Override
    public void clearAppTips(final String appId) {
        mRepo.clearAppMessage(appId, new LoadDataCallback<Boolean>() {
            @Override
            public void onDataLoaded(Boolean data) {
                if (isDead()) return;
                if (data) {
                    mView.clearAppTips(appId);
                    mRepo.clearAppsCache();
                } else {
                    Log.e(TAG, "onFailure: ");
                }
            }

            @Override
            public void onDataNotAvailable(String errMsg, int errCode) {
                Log.e(TAG, "onFailure: " + errMsg);
            }
        });
    }

    @Override
    public void destroy() {
        mView = null;
    }

    @Override
    public void goFeedback() {
        String url = "https://ssccmpltm.jd.com";
        OpennessApi.openUrl(url,false);
    }

    @Override
    public void getMyHolidayData(final Context context) {
        mView.showLoading();
        NetWorkManagerAppCenter.getMyHolidayData(null, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String info) {
                mView.dismissLoading();
                if (isDead()) {return;}
                if (TextUtils.isEmpty(info) || "null".equals(info)) {
                    ToastUtils.showToast(R.string.me_request_failed);
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(info);
                    String appType = jsonObject.optString("appType");//"appType":"应用类型(1.SDK 2. H5 3.RN)"
                    if ("1".equals(appType)) {
                        Intent intent = new Intent(context, FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.mine.HolidayBankFragment");
                        context.startActivity(intent);
                    } else if ("2".equals(appType)) {
//                        String url = "jdme://jm/biz/appcenter/************";
                        String deepLink = compatibleDeepLink(jsonObject);
                        if (deepLink == null) {
                            return;
                        }
                        String appId = jsonObject.optString("appId");
                        String url = deepLink + "/" + appId;
                        Router.build(url).go(context);//跳转H5界面
                    } else if ("3".equals(appType)) {
                        String deepLink = compatibleDeepLink(jsonObject);
                        if (deepLink == null) {
                            return;
                        }
                        String appId = jsonObject.optString("appId");
                        AppUtils.gainTokenAndGoPlugin(deepLink, appId);//跳转本地sdk或RN应用
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                    ToastUtils.showToast(R.string.me_request_failed);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mView.dismissLoading();
                if (1 == i) {
                    ToastUtils.showToast(s);
                } else {
                    ToastUtils.showToast(R.string.me_request_failed);
                }
            }
        });
    }

    @Override
    public void openNetDisk(final Activity activity, final String deeplink) {
        NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {}.getType());
                if (response.isSuccessful()) {
                    try {
                        Map<String, String> map = response.getData();
                        ToNetDiskBean bean = new ToNetDiskBean();
                        bean.setToken(map.get("third_token"));
                        bean.setUserCode(map.get("third_name"));
                        bean.setThirdTimestamp(map.get("third_timestamp"));
                        NetDiskHelper.openNetDisk(activity, bean);
                    } catch (Exception e) {
                        Router.build(deeplink).go(activity, new RouteNotFoundCallback(activity));
                    }
                } else {
                    com.jd.oa.melib.ToastUtils.showInfoToast(activity, "" + response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                Router.build(deeplink).go(activity, new RouteNotFoundCallback(activity));
            }
        });
    }
}