package com.jd.oa.business.mine.card;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by huf<PERSON> on 2016/7/12
 */
public class CardRepo {
    private static final String TAG = CardRepo.class.getSimpleName();

    public interface CardRepoListener {
        void onLoadedFinish(CardInfo qt);

        void onFailure(String errorMsg);
    }

    public void getData(final CardRepoListener listener,boolean isNeedUpdate) {
        if (listener == null)
            return;
        Map<String,Object> map=new HashMap<>();
        if (isNeedUpdate) {
            map.put("isAutoUpdate", "1");
        }
        HttpManager.color().post(map, null,NetworkConstant.MINE_VISITINGCARD,
                new SimpleReqCallbackAdapter<>(new AbsReqCallback<CardInfo>(CardInfo.class) {
            @Override
            public void onFailure(String errorMsg) {
                listener.onFailure(errorMsg);
            }

            @Override
            public void onSuccess(CardInfo info, List<CardInfo> tArray, String rawDate) {
                listener.onLoadedFinish(info);
            }
        }));
    }
}
