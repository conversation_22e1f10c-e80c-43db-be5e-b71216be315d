package com.jd.oa.business.groupicon

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils.isEmpty
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.Space
import androidx.core.view.children
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.bumptech.glide.Glide
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.PhotoPreviewActivity
import com.jd.oa.business.index.view.SelectPicPopupEntity
import com.jd.oa.cache.FileCache
import com.jd.oa.elliptical.JdMeAvatarView
import com.jd.oa.experience.R
import com.jd.oa.fragment.model.PhotoInfo
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.router.DeepLink
import com.jd.oa.ui.groupicon.AvatarUtil
import com.jd.oa.ui.groupicon.AvatarUtil.UploadAvatarListener
import com.jd.oa.ui.groupicon.AvatarUtil.avatarSize
import com.jd.oa.ui.groupicon.AvatarUtil.backgroundColors
import com.jd.oa.ui.groupicon.TextLengthUtil
import com.jd.oa.ui.widget.IosActionSheetDialog
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.BitmapUtil
import com.jd.oa.utils.FileUtils
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.Utils
import com.jd.oa.utils.getFileUri
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import kotlin.random.Random

@Route(DeepLink.GROUP_ICON_MODIFY)
class GroupIconModifyActivity : BaseActivity() {
    lateinit var etInput: EditText
    lateinit var avatarView: JdMeAvatarView
    var currentColor: String = ""
    lateinit var mHeadPhotoSelectEntity: SelectPicPopupEntity
    private var avatarUrl = ""
    private var currentMode: Int = -1
    lateinit var llColors: LinearLayout
    lateinit var tvClear: View
    private var mCaptureImgName: String = ""
    private var mCropImgName: String = ""
    private val mBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            <EMAIL>()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_group_icon_modify)
        val actionBar = ActionBarHelper.getActionBar(this) //获取actionBar对象
        actionBar?.hide()

        initView()
        initData()
    }

    private fun initView() {
        mHeadPhotoSelectEntity = SelectPicPopupEntity(this)
        avatarView = findViewById(R.id.avatar_view)
        llColors = findViewById(R.id.ll_colors)
        etInput = findViewById(R.id.et_input)
        tvClear = findViewById(R.id.tv_clear)
        etInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                tvClear.visibility = if (isEmpty(s.toString())) View.GONE else View.VISIBLE
                if (TextLengthUtil.calculateLength(s.toString()) > AvatarUtil.maxTextLength) {
                    etInput.setText(
                        TextLengthUtil.getMaxLengthText(s.toString(), AvatarUtil.maxTextLength)
                    )
                    etInput.setSelection(etInput.getText().length)
                }
            }

            override fun afterTextChanged(s: Editable?) {
                avatarView.iconImg.setImageBitmap(
                    AvatarUtil.getAvatarWithText(
                        this@GroupIconModifyActivity, etInput.text.toString(), getCurrentBgColor()
                    )
                )
                currentMode = MODE_TEXT
            }
        })
        tvClear.setOnClickListener { etInput.text.clear() }
        for ((index, color) in backgroundColors.withIndex()) {
            val view = JdMeAvatarView(this)
            val size = Utils.dip2px(this, 32f)
            val layoutParams = LinearLayout.LayoutParams(size, size)
            layoutParams.marginStart = if (index == 0) 0 else Utils.dip2px(this, 10.0f)
            view.layoutParams = layoutParams
            view.iconImg.setImageDrawable(ColorDrawable(Color.parseColor(color)))
            val padding = Utils.dip2px(this, 6.0f)
            view.widgetImg.setPadding(padding, padding, padding, padding)
            llColors.addView(view)
            if (index < backgroundColors.size - 1) {
                val space = Space(this)
                val spaceParams =
                    LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT)
                spaceParams.weight = 1f
                llColors.addView(space, spaceParams)
            }
            view.setOnClickListener {
                currentColor = color
                updateColorSelect(false)
                avatarView.iconImg.setImageBitmap(
                    AvatarUtil.getAvatarWithText(
                        this, etInput.text.toString(), getCurrentBgColor()
                    )
                )
                currentMode = MODE_TEXT
            }
        }
        findViewById<View>(R.id.tv_back)?.setOnClickListener { finish() }
        findViewById<View>(R.id.tv_save)?.setOnClickListener {
            when (currentMode) {
                MODE_LOCAL_FILE -> SelectPicPopupEntity(this).getFile(mCropImgName)?.absolutePath?.let { it1 ->
                    uploadAvatar(
                        "",
                        "",
                        {
                            val cropFile = mHeadPhotoSelectEntity.getFile(mCropImgName)
                            if (cropFile != null && cropFile.exists()) {
                                cropFile.delete()
                            }
                        },
                        it1
                    )
                }
                else -> {
                    uploadAvatar(etInput.text.toString(), getCurrentBgColor(),null,"")
                }
            }

        }
        avatarView.setOnClickListener { toUserIcon() }
        findViewById<View>(R.id.tv_camera)?.setOnClickListener { toUserIcon() }
    }

    fun initData() {
        if (intent != null && intent.hasExtra("mparam") && intent.getStringExtra("mparam")
                ?.isNotEmpty() == true
        ) {
            try {
                val mparam = intent.getStringExtra("mparam")
                avatarUrl = mparam?.let { JSONObject(it).getString("url").trim() } ?: ""

                Uri.parse(avatarUrl).queryParameterNames?.let {
                    if (it.contains("color")) {
                        currentColor = Uri.parse(avatarUrl).getQueryParameter("color") ?: ""
                        if (!isEmpty(currentColor)) {
                            llColors.post { updateColorSelect(false) }
                        }
                    }
                    if (it.contains("text")) {
                        val text = Uri.parse(avatarUrl).getQueryParameter("text")
                        if (!isEmpty(text)) etInput.setText(text)
                    }
                }
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
        if (!isEmpty(avatarUrl)) {
            Glide.with(this).load(avatarUrl).into(avatarView.iconImg)
            if (isEmpty(currentColor)) {
                getAvatarInfo()
            }
        } else if (isEmpty(currentColor)) {
            //原来头像为空 给个默认的颜色
            getCurrentBgColor()
        }


        if (!isEmpty(currentColor)) {
            updateColorSelect(false)
            avatarView.iconImg.setImageBitmap(
                AvatarUtil.getAvatarWithText(
                    this,
                    etInput.text.toString(),
                    currentColor
                )
            )
        }

    }

    private fun updateColorSelect(reset: Boolean) {
        var checkedIndex: Int = -1
        if (backgroundColors.indexOf(currentColor) != -1) {
            checkedIndex = backgroundColors.indexOf(currentColor)
        }
        var list = ArrayList<JdMeAvatarView>()
        for (view in llColors.children) {
            if (view is JdMeAvatarView) {
                list.add(view)
                view.widgetImg.setImageDrawable(null)
            }
        }
        if (!reset && checkedIndex != -1) {
            list[checkedIndex].widgetImg.setImageResource(R.drawable.jdme_icon_group_modify_color_check)
        } else {
            currentColor = ""
        }
    }

    private fun uploadAvatar(text: String, color: String, deleteFile: (() -> Unit)? = null,filePath: String) {
        AvatarUtil.upLoadAvatar(this,text,color,object : UploadAvatarListener {
            override fun onSuccess(avatarUrl: String?, text: String?, color: String?) {
                deleteFile?.invoke()
                val jsonObject = JSONObject().put("avatarUrl", avatarUrl)
                if (!isEmpty(text)) jsonObject.put("text", text)
                if (!isEmpty(color)) jsonObject.put("backgroundColor", color)
                intent.putExtra("result", jsonObject.toString())
                setResult(RESULT_OK, intent)
                <EMAIL>()
            }

            override fun onFail() {
                deleteFile?.invoke()
                ToastUtils.showToast("头像上传失败")
            }
        },filePath);
    }

    private fun getAvatarInfo() {
        val params = mutableMapOf<String, Any?>("avatarUrl" to avatarUrl)
        HttpManager.color()
            .post(params, null, "jdme.avatar.get", object : SimpleRequestCallback<String?>() {
                override fun onSuccess(info: ResponseInfo<String?>?) {
                    super.onSuccess(info)
                    if (isFinishing || isDestroyed) {
                        return
                    }
                    info?.result?.let {
                        val content = JSONObject(it).optJSONObject("content")
                        if (content != null) {
                            if (!isEmpty(content.optString("color", ""))) {
                                currentColor = content.optString("color", "")
                                updateColorSelect(false)
                            }
                            if (!isEmpty(content.optString("text", ""))) {
                                etInput.setText(content.optString("text"))
                            }
                        }
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                }
            })
    }


    private fun toUserIcon() {
        val dialog = IosActionSheetDialog(this, IosActionSheetDialog.SheetItemColor.BLACK).builder()
            .setCancelable(false).setCanceledOnTouchOutside(false).addSheetItem(
                getString(R.string.me_camera), IosActionSheetDialog.SheetItemColor.BLACK
            ) {
                PermissionHelper.requestPermissions(
                    this@GroupIconModifyActivity,
                    resources.getString(com.jme.common.R.string.me_request_permission_title_normal),
                    resources.getString(R.string.me_request_permission_camera_normal),
                    object : RequestPermissionCallback {
                        override fun allGranted() {
                            mCaptureImgName =
                                FileUtils.createImageFileName(this@GroupIconModifyActivity, CAPTURE_IMG)
                            mHeadPhotoSelectEntity.startCamera(mCaptureImgName)
                        }

                        override fun denied(deniedList: List<String>) {
                        }
                    },
                    Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }.addSheetItem(
                getString(R.string.me_album), IosActionSheetDialog.SheetItemColor.BLACK
            ) {
                PermissionHelper.requestPermission(
                    this@GroupIconModifyActivity,
                    resources.getString(R.string.me_request_permission_storage_normal),
                    object : RequestPermissionCallback {
                        override fun allGranted() {
                            mHeadPhotoSelectEntity.selectPictureFormGallery()
                        }

                        override fun denied(deniedList: List<String>) {
                        }
                    },
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        var photos = ArrayList<PhotoInfo>()
        when (currentMode) {
            MODE_TEXT -> {
                val cacheFile = File(FileCache.getInstance().imageCacheFile, "groupIcon.jpg")
                BitmapUtil.save(
                    AvatarUtil.getAvatarWithText(
                        this@GroupIconModifyActivity,
                        etInput.text.toString(),
                        getCurrentBgColor()
                    ), cacheFile
                )
                photos = arrayListOf(PhotoInfo("groupIcon.jpg", cacheFile.absolutePath, "", ""))
            }

            MODE_LOCAL_FILE -> {
                photos = arrayListOf(
                    PhotoInfo(
                        "",
                        SelectPicPopupEntity(this@GroupIconModifyActivity).getFile(
                            mCropImgName
                        ).absolutePath,
                        "",
                        ""
                    )
                )
            }

            else -> {
                if (!isEmpty(avatarUrl)) {
                    photos = arrayListOf(PhotoInfo("", "", "", avatarUrl))
                }
            }
        }
        if (photos.isNotEmpty()) {
            dialog.addSheetItem(
                getString(R.string.group_icon_modify_view_photo),
                IosActionSheetDialog.SheetItemColor.BLACK
            ) {
                val intent = Intent(this, PhotoPreviewActivity::class.java)
                intent.putParcelableArrayListExtra("photos", photos)
                intent.putExtra("index", 0)
                intent.putExtra("useSave", false)
                startActivity(intent)
            }

        }
        dialog.show()
    }

    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val entity = SelectPicPopupEntity(this)
        when (requestCode) {
            SelectPicPopupEntity.PHOTO_REQUEST_TAKEPHOTO -> {
                if (resultCode != 0) {
                    mCropImgName =
                        FileUtils.createImageFileName(this, HEAD_PHOTO_FILE_NAME)
                    entity.startPictureCrop(
                        getFileUri(
                            this@GroupIconModifyActivity,
                            entity.getFile(mCaptureImgName)
                        ),
                        1,
                        1,
                        avatarSize,
                        avatarSize,
                        mCropImgName,
                        SelectPicPopupEntity.PHOTO_REQUEST_CUT,
                        this,
                        true
                    )
                }
            }

            SelectPicPopupEntity.PHOTO_REQUEST_GALLERY -> data?.let {
                mCropImgName =
                    FileUtils.createImageFileName(this, HEAD_PHOTO_FILE_NAME)
                entity.startPictureCrop(
                    it.data,
                    1,
                    1,
                    avatarSize,
                    avatarSize,
                    mCropImgName,
                    SelectPicPopupEntity.PHOTO_REQUEST_CUT,
                    this,
                    false
                )
            }

            SelectPicPopupEntity.PHOTO_REQUEST_CUT -> data?.let {
                //清空输入框内容
                etInput.text.clear()
                etInput.clearFocus()
                updateColorSelect(true)
                val captureFile = mHeadPhotoSelectEntity.getFile(mCaptureImgName)
                if (captureFile != null && captureFile.exists()) {
                    captureFile.delete()
                }
                avatarView.iconImg.setImageURI(it.data)
                currentMode = MODE_LOCAL_FILE
            }

        }
    }


    fun getCurrentBgColor(): String {
        if (isEmpty(currentColor)) {
            currentColor = backgroundColors[Random.nextInt(0, backgroundColors.size)]
            updateColorSelect(false)
        }
        return currentColor
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mBroadcastReceiver, IntentFilter("action.group.icon.modify.close"))
    }

    override fun onStop() {
        super.onStop()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mBroadcastReceiver)
    }

    companion object {
        private const val HEAD_PHOTO_FILE_NAME = "avatar_"
        private const val CAPTURE_IMG = "capture_"
        private const val MODE_LOCAL_FILE = 1
        private const val MODE_TEXT = 0
    }
}