package com.jd.oa.business.mine;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentManager;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.badge.RedDotView;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.ShowAnimationSubject;
import com.jd.oa.business.mine.main.MineAnimFragment;
import com.jd.oa.business.setting.settingitem.SettingItem;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.experience.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.model.MineInfo;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.listener.UserServiceListener;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.NetworkFileUtil;
import com.jd.oa.utils.StatusBarConfig;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.VerifyUtils;
import com.jd.oa.utils.WebViewUtils;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.io.File;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Observable;
import java.util.Observer;

import com.jd.oa.loading.loadingDialog.LoadingDialog;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import pl.droidsonroids.gif.GifImageView;


/**
 * 我的界面
 * Created by peidongbiao on 2018/7/25.
 */

public class MineFragment extends BaseFragment implements MineContract.View, Observer, UserServiceListener, Refreshable {
    private ViewGroup mLayoutTop;
    private TextView mTvName;
    private ImageView mIvQrcode;
    private ImageView mIvAvatar;
    private TextView mTvJob;
    private TextView mTvWorkTime;
    private TextView mTvDesc;
    private TextView mTvScore;
    private TextView mTvHoliday;
    private LinearLayout mLayoutAppContainer;
    private SettingItem mSetting;
    private SettingItem mFeedback;
    private LinearLayout mLayoutSignature;
    private ImageView mIvSignatureEdit;
    private FrameLayout mLayoutBirthday;
    private GifImageView mGifBirthdayBg;
    private ImageView mIvBirthday;
    private TextView mTvCompanyAge;
    private ImageView mIvMedal;
    private ImageView mIvParty;
    private LinearLayout mLayoutScore;
    private LinearLayout mLayoutHoliday;
    private ImageView[] imageViewList;
    private View mPeerLayout;
    private View mMediumContainer;

    private MineContract.Presenter mPresenter;
    private MineInfo mMineInfo;
    private MineAnimFragment mAnimDialog;

    private List<AppInfo> mApps;
    private boolean mUserVisible;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);
    private ImageView iv_avatar_bg;

    //创新
    private final static int peerOneList[] = new int[]{R.drawable.mine_badge_type1_grade0, R.drawable.mine_badge_type1_grade1,
            R.drawable.mine_badge_type1_grade2, R.drawable.mine_badge_type1_grade3};
    //协作
    private final static int peerTwoList[] = new int[]{R.drawable.mine_badge_type2_grade0, R.drawable.mine_badge_type2_grade1,
            R.drawable.mine_badge_type2_grade2, R.drawable.mine_badge_type2_grade3};
    //品质
    private final static int peerThreeList[] = new int[]{R.drawable.mine_badge_type3_grade0, R.drawable.mine_badge_type3_grade1,
            R.drawable.mine_badge_type3_grade2, R.drawable.mine_badge_type3_grade3};
    //高效
    private final static int peerFourList[] = new int[]{R.drawable.mine_badge_type4_grade0, R.drawable.mine_badge_type4_grade1,
            R.drawable.mine_badge_type4_grade2, R.drawable.mine_badge_type4_grade3};
    private final static int peerFiveList[] = new int[]{R.drawable.mine_badge_type5_grade0};
    private final static int peerList[][] = new int[][]{peerOneList, peerTwoList, peerThreeList, peerFourList, peerFiveList};

    private LoadingDialog mLoadingDialog;
    private SettingItem.OnClickListener mOnAppClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            AppInfo app = (AppInfo) v.getTag();
            openApp(app);
            if (!TextUtils.isEmpty(app.getAppMessage())) {
                mPresenter.clearAppTips(app.getAppID());
            }
        }
    };

    private Consumer<Pair<Boolean, String>> mShowAnimationConsumer = new Consumer<Pair<Boolean, String>>() {
        @Override
        public void accept(Pair<Boolean, String> pair) throws Exception {
            if (!pair.first || mMineInfo == null || TextUtils.isEmpty(pair.second)) return;
            String type = mMineInfo.getCartoonType();
            if (pair.second.equals(type)) {
                showAnniversaryAnimation(type, mMineInfo.getWorkedYears());
            }
        }
    };

    private Disposable mShowAnimationDisposable;
    private boolean isMineInfoLoaded = false;
    private boolean isMenuAppsLoaded = false;
    private QRCodeDialog dialog = null;
    private RedDotView mSettingBadge;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mPresenter = new MinePresenter(getContext(), this);
        MySelfInfoFragment.observerList.add(this);
        imDdService.addUserService(this);

        Bundle args = getArguments();
        if (args != null) {
            mUserVisible = args.getBoolean("isVisibleToUser", false);
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_mine2, container, false);
        if (StatusBarConfig.enableImmersive()) {
            view.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(getContext()), 0, 0);
        }
        findViews(view);
        initViews(view);
        mShowAnimationDisposable = ShowAnimationSubject.subscribe(mShowAnimationConsumer);
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!(isMenuAppsLoaded && isMineInfoLoaded)) {//修复我的设置为主页 首次密码登录进入时 我的页面加载不完全的问题
            action();
        }
        isDismiss = false;

        // 挂件
        String pendanSmall = AppJoint.service(ImDdService.class).getPendan();
        if (!TextUtils.isEmpty(pendanSmall)) {
            ImageLoaderUtils.getInstance().displayImage(pendanSmall, iv_avatar_bg);
        } else {
            AppJoint.service(ImDdService.class).getPendanByNet(new Callback<String>() {
                @Override
                public void onSuccess(String str) {
                    if (!TextUtils.isEmpty(str)) {
                        ImageLoaderUtils.getInstance().displayImage(str, iv_avatar_bg);
                    } else {
                        iv_avatar_bg.setImageResource(0);
                    }
                }

                @Override
                public void onFail() {

                }
            });
        }
        //通过shortcut启动门禁卡逻辑
        Intent topActivity = Objects.requireNonNull(AppBase.getTopActivity()).getIntent();
        if (topActivity.hasExtra(Constant.SHORTCUT_FROM)) {
            String from = topActivity.getStringExtra(Constant.SHORTCUT_FROM);
            if (Constant.SHORTCUT_FLAG.equals(from)) {
                //子午线埋点
                JDMAUtils.onEventClick(JDMAConstants.mobile_me_icon_access_card_click,JDMAConstants.mobile_me_icon_access_card_click);
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        showQRCodeDialog();
                    }
                }, 500);
            }
            topActivity.removeExtra(Constant.SHORTCUT_FROM);//每次展示完弹窗将标识去掉,避免重复展示
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        mUserVisible = isVisibleToUser;
        if (mMineInfo != null && mUserVisible && shouldShowAnimation(mMineInfo.getCartoonType())) {
            showAnniversaryAnimation(mMineInfo.getCartoonType(), mMineInfo.getWorkedYears());
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mShowAnimationDisposable != null) {
            mShowAnimationDisposable.dispose();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mPresenter.destroy();
        MySelfInfoFragment.observerList.remove(this);
        imDdService.removeUserService(this);
    }

    private void findViews(View view) {
        mLayoutTop = view.findViewById(R.id.layout_top);
        mTvName = view.findViewById(R.id.tv_name);
        mIvQrcode = view.findViewById(R.id.iv_qrcode);
        mIvAvatar = view.findViewById(R.id.iv_avatar);
        mTvJob = view.findViewById(R.id.tv_job);
        mTvWorkTime = view.findViewById(R.id.tv_work_time);
        mTvDesc = view.findViewById(R.id.tv_desc);
        mTvScore = view.findViewById(R.id.tv_score);
        mTvHoliday = view.findViewById(R.id.tv_holiday);
        mLayoutAppContainer = view.findViewById(R.id.layout_app_container);
        mSetting = view.findViewById(R.id.setting);
        mSettingBadge = view.findViewById(R.id.badge_view);
        mSettingBadge.setAppLinks(BadgeManager.BADGE_APP_UPDATE);
        mFeedback = view.findViewById(R.id.feedback);
        mLayoutSignature = view.findViewById(R.id.layout_signature);
        mIvSignatureEdit = view.findViewById(R.id.iv_signature_edit);
        mTvCompanyAge = view.findViewById(R.id.tv_company_age);
        mLayoutScore = view.findViewById(R.id.layout_score);
        mLayoutHoliday = view.findViewById(R.id.layout_holiday);
        mLayoutBirthday = view.findViewById(R.id.layout_birthday);
        mIvBirthday = view.findViewById(R.id.iv_birthday);
        mGifBirthdayBg = view.findViewById(R.id.iv_birthday_bg);
        mIvMedal = view.findViewById(R.id.iv_medal);
        mIvParty = view.findViewById(R.id.iv_party);
        mPeerLayout = view.findViewById(R.id.layout_peer);
        mMediumContainer = view.findViewById(R.id.medium_container);
        iv_avatar_bg = view.findViewById(R.id.iv_avatar_bg);

        ImageView peerOneImage = view.findViewById(R.id.image_peer_1);
        ImageView peerTwoImage = view.findViewById(R.id.image_peer_2);
        ImageView peerThreeImage = view.findViewById(R.id.image_peer_3);
        ImageView peerFourImage = view.findViewById(R.id.image_peer_4);
        ImageView peerFiveImage = view.findViewById(R.id.image_peer_5);
        imageViewList = new ImageView[]{peerOneImage, peerTwoImage, peerThreeImage, peerFourImage, peerFiveImage};
    }

    private void initViews(View view) {
        LinearLayout llShow = view.findViewById(R.id.llShow);
        TextView idShowLetter = view.findViewById(R.id.idShowLetter);
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(getContext());
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            idShowLetter.setText("显示欢迎信");
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {//英文的情况
                idShowLetter.setText("Show Welcome Letter");
            } else {
                idShowLetter.setText("显示欢迎信");
            }
        }
        if (!TenantConfigBiz.INSTANCE.isJdHuTongEnable()) {
            idShowLetter.setVisibility(View.GONE);
        }

        //显示和隐藏欢迎页
/*        llShow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                EvalAgrrementDialog mAgrrementDialog = new EvalAgrrementDialog(getContext());
                mAgrrementDialog.show();
                mAgrrementDialog.loadUrl();
            }
        });*/

        mSetting.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
/*                Intent intent = new Intent(getContext(), SettingActivity.class);
                startActivity(intent);*/
                Router.build(DeepLink.ACTIVITY_URI_SETTING).go(getContext());
            }
        });
        mFeedback.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.goFeedback();
//                PermissionUtils.checkOnePermission(getContext(), Manifest.permission.READ_EXTERNAL_STORAGE, getContext().getString(R.string.me_request_permisson_storage), new Runnable() {
//                    @Override
//                    public void run() {
//                        Uri.Builder realUriBuild = new Uri.Builder();
//                        realUriBuild.scheme(DeepLink.ROUTER_SCHEME_JDME_SIMPLE);
//                        realUriBuild.authority("rn");
//                        realUriBuild.path("201909180621");
//                        realUriBuild.appendQueryParameter("appId", "201909180621");
//                        Router.build(realUriBuild.build()).go(getActivity());
////                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
////                        intent.putExtra("function", FeedbackFragment2.class.getName());
////                        getActivity().startActivity(intent);
//                    }
//                });
            }
        });

        mLayoutTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, MySelfInfoFragment.class.getName());
                getContext().startActivity(intent);
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_MINE_AVATAR);
                JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_headIcon_click, JDMAConstants.mobile_Mine_headIcon_click);
            }
        });

        mIvQrcode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showQRCodeDialog();
                JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_QRCode_click, JDMAConstants.mobile_Mine_QRCode_click);
            }
        });

        mLayoutSignature.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_addSignature_click, JDMAConstants.mobile_Mine_addSignature_click);
                Context context = getContext();
                if (context instanceof AppCompatActivity) {
                    String hint = context.getString(R.string.me_signature_hint);
                    String title = context.getString(R.string.exp_signature_title);
                    imDdService.openSignatureEdit((AppCompatActivity)context, title, hint, new Callback<CharSequence>() {
                        @Override
                        public void onSuccess(CharSequence formatSignature) {
                            mIvSignatureEdit.setVisibility(TextUtils.isEmpty(formatSignature) ? View.VISIBLE : View.GONE);
                            mTvDesc.setText(formatSignature.toString());
                        }

                        @Override
                        public void onFail() {

                        }
                    });
                }
            }
        });

        mLayoutBirthday.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mMineInfo == null) return;
                String type = mMineInfo.getCartoonType();
                if (MineInfo.BADGE_BIRTHDAY.equals(type) || MineInfo.BADGE_COMPANY_AGE.equals(type)) {
                    showAnniversaryAnimation(type, mMineInfo.getWorkedYears());
                }
            }
        });

        mLayoutHoliday.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                String isFourWall = PreferenceManager.UserInfo.getIsFourWall();
//                if ("1".equals(isFourWall)) {
//                    String url = "jdme://jm/biz/appcenter/************";
//                    Router.build(url).go(getContext());
//                    return;
//                }
//                Intent intent = new Intent(getContext(), FunctionActivity.class);
//                intent.putExtra(FunctionActivity.FLAG_FUNCTION, HolidayBankFragment.class.getName());
//                startActivity(intent);
                JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_myHoliday_click, JDMAConstants.mobile_Mine_myHoliday_click);
                mPresenter.getMyHolidayData(getContext());
            }
        });
        // 审核账号不可点击
        if(!VerifyUtils.isVerifyUser()){
            mLayoutScore.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(getContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
                    //TODO appid硬编码
                    intent.putExtra(EXTRA_APP_ID, "************");
                    startActivity(intent);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_IntegralInfo_click, JDMAConstants.mobile_Mine_IntegralInfo_click);
                }
            });
        }


//        if (AppInitParam.isVirtualErp()) {
        mFeedback.setVisibility(View.GONE);
//        }

        String realName = PreferenceManager.UserInfo.getUserRealName();
        if (!TextUtils.isEmpty(realName)) {
            mTvName.setText(realName);
        }
    }

    /**
     * 弹出个人二维码弹窗
     */
    private void showQRCodeDialog() {
        if (dialog == null) {
            dialog = new QRCodeDialog(requireContext());
            DisplayUtil.setBrightness(requireActivity(), 255);
            dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog1) {
                    DisplayUtil.setBrightness(requireActivity(), -255);
                    dialog = null;
                }
            });
            dialog.show();
        }
    }

    @Override
    public void onDateLoad() {
        action();
    }

    private void action() {
        if (mPresenter == null) {
            return;
        }
        mPresenter.getMyInfo();
        if (!AppBase.isVirtualErp()) {
            mPresenter.getMyApps();
        }
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                CharSequence text = imDdService.getMyFormatSignature();
                mIvSignatureEdit.setVisibility(TextUtils.isEmpty(text) ? View.VISIBLE : View.GONE);
                mTvDesc.setText(text.toString());
            }
        }, 500);
    }

    @Override
    public void showInfo(MineInfo info) {
        mMineInfo = info;
        mTvName.setText(info.getRealName());
        PreferenceManager.UserInfo.setUserRealName(info.getRealName());
        mTvJob.setText(info.getPositionName());
        mTvJob.setVisibility(TextUtils.isEmpty(info.getPositionName()) ? View.GONE : View.VISIBLE);
        if (!TextUtils.isEmpty(info.getWorkedDayTip()) && !TextUtils.equals("0", info.getWorkedDayTip())) {
            mTvWorkTime.setVisibility(View.VISIBLE);
            mTvWorkTime.setText(getString(R.string.me_mine_work_days, info.getWorkedDayTip()));
        } else {
            mTvWorkTime.setVisibility(View.GONE);
        }
        mTvScore.setText(info.getBalance());
        mTvHoliday.setText(info.getTotalHoliday());

        String avatar = PreferenceManager.UserInfo.getUserCover();
        if (TextUtils.isEmpty(avatar)) {
            avatar = info.getHeadIcon();
        }
        ImageLoaderUtils.getInstance().displayImage(avatar, mIvAvatar, R.drawable.jdme_picture_user_default_white);
        showBadgeImage(mMineInfo);
        if (shouldShowAnimation(mMineInfo.getCartoonType()) && mUserVisible) {
            showAnniversaryAnimation(mMineInfo.getCartoonType(), mMineInfo.getWorkedYears());
        }
        List<MineInfo.PeerInfo> infoList = info.getBadgeInfos();
        try {
            if (infoList != null && !infoList.isEmpty()) {
                for (int i = 0; i < imageViewList.length; i++) {
                    if (i < infoList.size()) {
                        MineInfo.PeerInfo peerInfo = infoList.get(i);
                        if (peerInfo != null) {
                            int selectType = peerInfo.getBadgeType() - 1;
                            if (peerInfo.getBadgeType() == 5) {
                                if (peerInfo.getNum() != 0) {
                                    imageViewList[i].setVisibility(View.VISIBLE);
                                    imageViewList[i].setImageResource(peerList[selectType][peerInfo.getGrade()]);
                                } else {
                                    imageViewList[i].setVisibility(View.GONE);
                                }
                            } else {
                                imageViewList[i].setImageResource(peerList[selectType][peerInfo.getGrade()]);
                            }
                        }
                    } else {
                        imageViewList[i].setVisibility(View.GONE);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        performStandardVersion();
        NetworkFileUtil.getDownloadFile(getContext(), info.entrySourceUrl, info.md5);
        isMineInfoLoaded = true;
    }

    private void performStandardVersion() {
        mPeerLayout.setVisibility(TenantConfigBiz.INSTANCE.isPeerMedalEnable() ? View.VISIBLE : View.GONE);
        boolean hasMyHoliday = TenantConfigBiz.INSTANCE.isWelfareHolidaysEnable();
        boolean hasFuliScore = TenantConfigBiz.INSTANCE.isWelfareHolidaysEnable();

        if (!hasMyHoliday && !hasFuliScore) {
            mMediumContainer.setVisibility(View.GONE);
        } else {
            mMediumContainer.setVisibility(View.VISIBLE);
            mLayoutHoliday.setVisibility(hasMyHoliday ? View.VISIBLE : View.INVISIBLE);
            mLayoutScore.setVisibility(hasFuliScore ? View.VISIBLE : View.INVISIBLE);
        }
    }

    @Override
    public void showMyApps(List<AppInfo> list) {
        if (CollectionUtil.isEquals(mApps, list)) return;
        if (list == null) return;
        mLayoutAppContainer.removeAllViews();
        for (AppInfo app : list) {
            SettingItem item = new SettingItem(getContext());
            item.setName(app.getAppName());
            item.setBadge(app.getAppMessage());
            item.setShowDivider(true);
            item.setShowIcon(true);
            Glide.with(getContext()).load(app.getPhotoKey()).into(item.getIconImage());
            item.setOnSettingClickListener(mOnAppClickListener);
            item.setTag(app);
/*            if (app.getAppAddress().equals(WalletFragment.class.getName())) {
                //我的钱包id
                mWalletAppId = app.getAppID();
            }*/
            mLayoutAppContainer.addView(item);
        }
        mApps = list;
        isMenuAppsLoaded = true;
    }

    @Override
    public void clearAppTips(String appId) {
        if (mLayoutAppContainer.getChildCount() == 0) return;
        for (int i = 0; i < mLayoutAppContainer.getChildCount(); i++) {
            View view = mLayoutAppContainer.getChildAt(i);
            if (!(view instanceof SettingItem)) continue;
            SettingItem item = (SettingItem) view;
            AppInfo app = (AppInfo) item.getTag();
            if (app == null) continue;
            if (app.getAppID().equals(appId)) {
                item.setBadge(null);
            }
        }
        mApps = null;
    }

    @Override
    public void showLoading() {
        if (mLoadingDialog == null) {
            mLoadingDialog = new LoadingDialog(getContext());
        }
        mLoadingDialog.show();
    }

    @Override
    public void dismissLoading() {
        if (mLoadingDialog != null) {
            mLoadingDialog.dismiss();
        }
    }


    @Override
    public void showSignature(String signature) {
    }

    @Override
    public void showError(String message) {
        Log.e(TAG, "showError: " + message);
        performStandardVersion();
    }

    @Override
    public boolean isAlive() {
        if (getActivity() == null) return false;
        if (getActivity().isFinishing()) return false;
        if (isDetached()) return false;
        if (isDismiss) {
            return false;
        }
        return true;
    }

    private boolean isDismiss = false;

    @Override
    public void onPause() {
        super.onPause();
        isDismiss = true;
    }

/*

    @Override
    public void toWallet(String jdPin) {
        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WalletFragment.class.getName());
        intent.putExtra(WalletFragment.FLAG_PIN, jdPin);
        intent.putExtra(WalletFragment.FLAG_ID, mWalletAppId);
        getActivity().startActivity(intent);
    }

    @Override
    public void showBindJdAccountDialog() {
        PromptUtils.showConfirmDialog(getActivity(), R.string.me_mine_no_pig_msg, R.string.me_mine_no_pig_cancel, R.string.me_mine_no_pig_ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, BindJdAccountFragment.class.getName());
                getActivity().startActivity(intent);
                if (dialog != null)
                    dialog.dismiss();
            }
        });
    }
*/

    @Override
    public void showMessage(String message) {
        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
    }

    private void showBadgeImage(MineInfo info) {
        if (MineInfo.BADGE_BIRTHDAY.equals(info.getCartoonType()) && TenantConfigBiz.INSTANCE.isBirthdayCardEnable()) {
            //生日
            mLayoutBirthday.setVisibility(View.VISIBLE);
            mGifBirthdayBg.setVisibility(View.VISIBLE);
            mTvCompanyAge.setVisibility(View.INVISIBLE);
            mIvMedal.setVisibility(View.INVISIBLE);
            mTvCompanyAge.setVisibility(View.INVISIBLE);
            mIvParty.setVisibility(View.INVISIBLE);
            mIvBirthday.setVisibility(View.VISIBLE);
            Glide.with(this).load(info.getRightTopURL()).into(mIvBirthday);
        } else if (MineInfo.BADGE_COMPANY_AGE.equals(info.getCartoonType()) && TenantConfigBiz.INSTANCE.isWorkingAgeEnable()) {
            //司龄
            mLayoutBirthday.setVisibility(View.VISIBLE);
            mGifBirthdayBg.setVisibility(View.VISIBLE);
            mTvCompanyAge.setVisibility(View.VISIBLE);
            mIvBirthday.setVisibility(View.INVISIBLE);
            mIvParty.setVisibility(View.INVISIBLE);
            mIvMedal.setVisibility(View.VISIBLE);
            Glide.with(this).load(info.getRightTopURL()).into(mIvMedal);
            mTvCompanyAge.setText(info.getWorkedYears());
        } else if (MineInfo.POLITICAL_PARTY_MEMBER.equals(info.getPoliticalStatus())) {
            //党员
            mLayoutBirthday.setVisibility(View.VISIBLE);
            mGifBirthdayBg.setVisibility(View.GONE);
            mIvBirthday.setVisibility(View.INVISIBLE);
            mTvCompanyAge.setVisibility(View.INVISIBLE);
            mIvMedal.setVisibility(View.INVISIBLE);
            mIvParty.setVisibility(View.VISIBLE);
            Glide.with(this).load(info.getPoliticalStatusUrl()).into(mIvParty);
        } else {
            mLayoutBirthday.setVisibility(View.GONE);
        }
    }

    private int getCompanyAge(String days) {
        if (!TextUtils.isDigitsOnly(days)) {
            return 0;
        }
        float years = Integer.valueOf(days) / 365.0f;
        return (int) years;
    }

    private void openApp(AppInfo app) {
        if (DeepLink.DA_KA.equals(app.getDeeplink())) {//DakaHistoryFragment
            //我的考勤
            JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_myAttendance_click, JDMAConstants.mobile_Mine_myAttendance_click);
        }
        if (DeepLink.WALLET.equals(app.getDeeplink())) {
            JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_click, JDMAConstants.mobile_myWallet_click);

            //"我的钱包"需要单独处理
            //mPresenter.checkJdPin();
            Router.build(DeepLink.WALLET).go(getContext());
        } else if (TabletUtil.isEasyGoEnable() && DeepLink.NET_DISK_OLD.equals(app.getAppAddress())) {
            mPresenter.openNetDisk(getActivity(), app.getAppAddress());
            JDMAUtils.onEventClick(JDMAConstants.mobile_mine_myNetdisk_click, JDMAConstants.mobile_mine_myNetdisk_click);

        } else {
            AppUtils.openFunctionByPlugIn(getActivity(), app);
        }
    }

    private boolean shouldShowAnimation(String type) {
        if (MineInfo.BADGE_BIRTHDAY.equals(type)) {
            return !JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_AUTO);
        } else if (MineInfo.BADGE_COMPANY_AGE.equals(type)) {
            return !JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_SILIN_GIF_AUTO);
        } else {
            return false;
        }
    }

    private void showAnniversaryAnimation(final String type, String years) {
        if (getActivity() == null) return;
        if (type.equals(MineAnimFragment.TYPE_BIRTHDAY) && !TenantConfigBiz.INSTANCE.isBirthdayCardEnable()) { // 禁用生日
            return;
        } else if (type.equals(MineAnimFragment.TYPE_HONOR) && !TenantConfigBiz.INSTANCE.isWorkingAgeEnable()) { // 禁用司龄
            return;
        }
        FragmentManager manager = getActivity().getSupportFragmentManager();
        if (manager.isStateSaved()) return;

        File animFile = NetworkFileUtil.getDownloadFile(getContext(), mMineInfo.entrySourceUrl, mMineInfo.md5);
        if (null == animFile && MineAnimFragment.TYPE_HONOR.equals(type)) return;
        mAnimDialog = new MineAnimFragment();
        Bundle bundles = new Bundle();
        bundles.putString(MineAnimFragment.ARG_TYPE, type);
        bundles.putInt(MineAnimFragment.ARG_YEAR, Integer.valueOf(years));
        if (MineAnimFragment.TYPE_HONOR.equals(type)) {
            bundles.putString(MineAnimFragment.ARG_YEAR_ENTRY_SOURCE_URL, mMineInfo.entrySourceUrl);
            bundles.putString(MineAnimFragment.ARG_YEAR_MD5, mMineInfo.md5);
        }
        mAnimDialog.setArguments(bundles);
        mAnimDialog.show(manager, "anim_dialog");
        switch (type) {
            case MineAnimFragment.TYPE_BIRTHDAY:
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_AUTO, true);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_TIME, System.currentTimeMillis());
                break;
            case MineAnimFragment.TYPE_HONOR:
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SILIN_GIF_AUTO, true);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SILIN_GIF_TIME, System.currentTimeMillis());
                break;
        }
    }

    @Override
    public void update(Observable o, final Object data) {
        //更新头像
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (mIvAvatar != null) {
                    ImageLoaderUtils.getInstance().displayImage((String) data, mIvAvatar, R.drawable.jdme_picture_user_default_white);
                }
            }
        });
    }

    @Override
    public void onSignatureChanged(String signature) {
        //showSignature(signature);
    }

    @Override
    public void onSignatureChangedNew(CharSequence signature) {
        CharSequence text = imDdService.getMyFormatSignature();
        mIvSignatureEdit.setVisibility(TextUtils.isEmpty(text) ? View.VISIBLE : View.GONE);
        mTvDesc.setText(text.toString());
    }

    @Override
    public void onAvatarChanged(String avatar) {
        ImageLoaderUtils.getInstance().displayImage(avatar, mIvAvatar, R.drawable.jdme_picture_user_default_white);
    }

    @Override
    public void refresh() {
        action();
    }
}