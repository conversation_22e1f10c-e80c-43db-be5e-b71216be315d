package com.jd.oa.business.mine.behavior;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;
import androidx.customview.widget.ViewDragHelper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

/**
 * Created by peidongbiao on 2018/8/19.
 */

public class DragDialogBehavior extends CoordinatorLayout.Behavior<View> {
    public static final int STATE_EXPAND = 1;
    public static final int STATE_HIDDEN = 2;
    private ViewDragHelper mDragHelper;
    private CoordinatorLayout mParent;
    private View mChild;
    private OnStateChangeListener mOnStateChangeListener;

    public DragDialogBehavior(Context context) {
        this(context,null);
    }

    public DragDialogBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onLayoutChild(CoordinatorLayout parent, View child, int layoutDirection) {
        if(mDragHelper == null){
            mDragHelper = ViewDragHelper.create(parent,new DragCallback());
        }
        mParent = parent;
        mChild = child;
        return false;
    }

    @Override
    public boolean onInterceptTouchEvent(CoordinatorLayout parent, View child, MotionEvent ev) {
        if(mDragHelper == null) return false;
        int action = ev.getAction();
        if (action == MotionEvent.ACTION_CANCEL || action == MotionEvent.ACTION_UP) {
            mDragHelper.cancel();
            return false;
        }
        return mDragHelper.shouldInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(CoordinatorLayout parent, View child, MotionEvent ev) {
        if(mDragHelper == null) return false;
         mDragHelper.processTouchEvent(ev);
         return true;
    }

    public void setOnStateChangeListener(OnStateChangeListener onStateChangeListener) {
        mOnStateChangeListener = onStateChangeListener;
    }

    private class DragCallback extends ViewDragHelper.Callback{
        private static final String TAG = "DragCallback";

        @Override
        public boolean tryCaptureView(@NonNull View child, int pointerId) {
            return child == mChild;
        }

        @Override
        public int clampViewPositionVertical(@NonNull View child, int top, int dy) {
            Log.d(TAG, "clampViewPositionVertical,top: " + top + ", dy: " + dy);
            return top > 0 ? top : 0;
        }

        @Override
        public void onViewReleased(@NonNull View releasedChild, float xvel, float yvel) {
            int height = mParent.getHeight();
            int state;
            int top;
            if(releasedChild.getTop() < height / 2){
                top = 0;
                state = STATE_EXPAND;
            }else {
                top = height;
                state = STATE_HIDDEN;
            }
            if(mDragHelper.smoothSlideViewTo(releasedChild,0, top)){
                ViewCompat.postOnAnimation(releasedChild,new SettleRunnable(releasedChild, state));
            }
        }
    }

    private class SettleRunnable implements Runnable {
        private static final String TAG = "SettleRunnable";
        private final View mView;
        private final int mState;

        SettleRunnable(View view, int state) {
            mView = view;
            mState = state;
        }

        @Override
        public void run() {
            if (mDragHelper != null && mDragHelper.continueSettling(true)) {
                ViewCompat.postOnAnimation(mView, this);
            } else {
                Log.d(TAG, "run complete");
                if(mOnStateChangeListener != null){
                    mOnStateChangeListener.onChange(mState);
                }
            }
        }
    }

    public interface OnStateChangeListener{
        void onChange(int state);
    }
}
