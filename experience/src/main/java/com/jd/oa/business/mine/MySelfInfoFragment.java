package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.view.SelectPicPopupEntity;
import com.jd.oa.business.mine.card.MyCardFragment;
import com.jd.oa.business.mine.selfInfo.ChangeTelephoneActivity;
import com.jd.oa.business.mine.selfInfo.ChangeTelephoneFragment;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.experience.R;
import com.jd.oa.experience.util.UserUtils;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Observer;

/**
 * 个人信息
 *
 * <AUTHOR>
 */
public class MySelfInfoFragment extends BaseFragment implements
        FragmentOperatingListener, OperatingListener {
    private static final String TAG = "MySelfInfoFragment";
    private static final int REQUEST_UPDATE_PHONE = 100;
    private static final int REQUEST_UPDATE_TELEPHONE = 101;
    private final String HEAD_PHOTO_FILE_NAME = "user_icon.jpg";
    private ImageView mIV_icon;
    private TextView mTV_name;
    private TextView mTV_dept;
    private TextView mTV_post;
    private TextView mTV_erp;
    private TextView mTV_phone;
    private TextView mTelephone;
    private TextView mTV_email;
    private TextView mTv_ID;
    private View mHeaderDot;
    private TextView mTvCard;

    private ImageView me_iv_circle_bg;

    private SelectPicPopupEntity mHeadPhotoSelectEntity;
    public static List<Observer> observerList = new ArrayList<>();

    private String mCardName;
    private String mElectronicCardDeeplink;
    private String mLogisticsHRBP;
    private TextView mTV_logistics_hr_phone;//物流HRBP
    private RelativeLayout mRL_logistics_hr;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    private void initView(View view) {
        mIV_icon = view.findViewById(R.id.me_iv_circle);
        mTV_name = view.findViewById(R.id.tv_name);
        mTV_dept = view.findViewById(R.id.tv_dept);
        if (!TenantConfigBiz.INSTANCE.isDeptDetailEnable()) {
            mTV_dept.setCompoundDrawables(null, null, null, null);
        }
        mTV_post = view.findViewById(R.id.tv_post);
        mTV_erp = view.findViewById(R.id.tv_erp);
        mTV_phone = view.findViewById(R.id.tv_phone);
        mTelephone = view.findViewById(R.id.tv_phone_2);
        mTV_logistics_hr_phone = view.findViewById(R.id.logistics_hr_phone);
        mRL_logistics_hr = view.findViewById(R.id.logistics_hr);
        mTV_email = view.findViewById(R.id.tv_email);
        mTv_ID = view.findViewById(R.id.tv_employee_id);
        mTvCard = view.findViewById(R.id.rl_check_qt);
        if (!TenantConfigBiz.INSTANCE.isElectronicCardEnable()) {
            mTvCard.setVisibility(View.GONE);
        }

        me_iv_circle_bg = view.findViewById(R.id.me_iv_circle_bg);

        mHeaderDot = view.findViewById(R.id.header_red_dot);
        if (PreferenceManager.UserInfo.selfInfoDotHadShow()) {
            mHeaderDot.setVisibility(View.GONE);
        } else {
            mHeaderDot.setVisibility(View.VISIBLE);
        }
//可以在这里找到HRBP，根据情况进行显示和隐藏。
//        if (TextUtils.isEmpty(mLogisticsHRBP)){
//            mRL_logistics_hr.setVisibility(View.GONE);
//        }else{
//            mRL_logistics_hr.setVisibility(View.VISIBLE);
//        }

        if (!TextUtils.isEmpty(mCardName)) {
            mTvCard.setText(mCardName);
        }

        view.findViewById(R.id.rl_phone).setOnClickListener(this);
        view.findViewById(R.id.rl_myself).setOnClickListener(this);
        view.findViewById(R.id.rl_check_qt).setOnClickListener(this);
        view.findViewById(R.id.rl_email).setOnClickListener(this);
        view.findViewById(R.id.rl_phone_2).setOnClickListener(this);
//        view.findViewById(R.id.rl_org).setOnClickListener(this);
        view.findViewById(R.id.logistics_hr).setOnClickListener(this);

    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.jdme_fragment_myself_info, container,
                false);
        ActionBarHelper.init(this, view);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_self_info);
        ActionBarHelper.getActionBar(this).setDisplayHomeAsUpEnabled(true);
        setHasOptionsMenu(true);

        mElectronicCardDeeplink = ConfigurationManager.get().getEntry("electronicNameCard.deepLink", "");
        Locale locale = LocaleUtils.getUserSetLocale(getContext());
        if (locale != null && "en".equals(locale.getLanguage())) {
            mCardName = ConfigurationManager.get().getEntry("electronicNameCard.name_en", "");
        } else {
            mCardName = ConfigurationManager.get().getEntry("electronicNameCard.name", "");
        }

        initView(view);

        mHeadPhotoSelectEntity = new SelectPicPopupEntity(this);

        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        getInfo();
    }

    /**
     * 获取个人信息
     */
    private void getInfo() {
        if (getActivity() == null) {
            return;
        }

        ImageLoaderUtils.getInstance().displayImage(PreferenceManager.UserInfo.getUserCover(), mIV_icon, R.drawable.jdme_icon_user_default_avatar_circle);
        mTV_name.setText(MyPlatform.getCurrentUser().getRealName());
        mTV_erp.setText(MyPlatform.getCurrentUser().getUserName());

        NetWorkManagerLogin.getMySelfInfo(this, MyPlatform.getCurrentUser().getUserName(),
                new SimpleRequestCallback<String>(getActivity().getApplicationContext(), false, false) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ResponseParser parser = new ResponseParser(info.result,
                                getActivity());
                        parser.parse(new ParseCallback() {
                            public void parseObject(JSONObject jsonObject) {
                                try {
                                    mTV_dept.setText(jsonObject
                                            .getString("organizationName"));
                                    mTV_post.setText(jsonObject
                                            .getString("positionName"));
                                    mTV_phone.setText(jsonObject
                                            .getString("mobile"));

                                    mTV_email.setText(jsonObject
                                            .getString("email"));
                                    mTv_ID.setText(jsonObject.getString("userCode"));
                                    mTelephone.setText(jsonObject.optString("telePhone", ""));
                                    //存储邮箱
                                    PreferenceManager.UserInfo.setEmailAddress(jsonObject.getString("email"));

                                    String domainUserName = jsonObject.optString("domainUserName");
                                    if (!TextUtils.isEmpty(domainUserName)) {
                                        PreferenceManager.UserInfo.setEmailAccount(domainUserName);
                                    }

                                    mLogisticsHRBP = jsonObject.getString("hrbp");
//                                    mLogisticsHRBP = "";
                                    if (TextUtils.isEmpty(mLogisticsHRBP)) {
                                        mRL_logistics_hr.setVisibility(View.GONE);
                                    } else {
                                        mRL_logistics_hr.setVisibility(View.VISIBLE);
                                    }
                                    mTV_logistics_hr_phone.setText(mLogisticsHRBP);
                                } catch (Exception e) {
                                }
                            }

                            public void parseArray(JSONArray jsonArray) {
                            }

                            @Override
                            public void parseError(String errorMsg) {
//								ToastUtils.showToast(errorMsg);
                            }
                        });
                    }
                });
    }

    @Override
    public void onClick(View v) {
        if (getActivity() == null)
            return;
        int id = v.getId();
        if (id == R.id.rl_phone) { // 修改手机号
            toUpdatePhoneFragment();
        } else if (id == R.id.rl_myself) {
            PreferenceManager.UserInfo.setSelfInfoDotHadShow(true);
            mHeaderDot.setVisibility(View.GONE);
//                checkCameraPermission();
            Intent intent2 = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent2.putExtra(FunctionActivity.FLAG_FUNCTION, MinePortraitFragment.class.getName());
            getContext().startActivity(intent2);
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_MY_SELF_INFO_AVATAR);
            JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_personInfo_headIcon_click, JDMAConstants.mobile_Mine_personInfo_headIcon_click);
        } else if (id == R.id.rl_check_qt) {
            if (!TextUtils.isEmpty(mCardName) && !TextUtils.isEmpty(mElectronicCardDeeplink)) {
                Router.build(mElectronicCardDeeplink).go(this);
            } else {
                Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyCardFragment.class.getName());
                getActivity().startActivity(intent);
            }
        } else if (id == R.id.rl_email) {
        } else if (id == R.id.rl_phone_2) {
            toUpdateTelephoneFragment();
        } else if (id == R.id.logistics_hr) {
            openPersonalDetails(mLogisticsHRBP);//跳转到咚咚联系人详情
        } else if (id == R.id.rl_org) {
//            if (TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_USEDEPTDETAIL)) {
//                switch (NetworkConstant.getCurrentServerName().index) {
//                    case 3:
//                        Router.build(DeepLink.appCenter("201912160656", null)).go(this);
//                        break;
//                    default:
//                        Router.build(DeepLink.appCenter("202108251088", null)).go(this);
//                        break;
//                }
//            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case SelectPicPopupEntity.PHOTO_REQUEST_TAKEPHOTO:
                mHeadPhotoSelectEntity.startPhotoZoom(
                        HEAD_PHOTO_FILE_NAME,
                        SelectPicPopupEntity.USER_ICON_SIZE_WIDTH,
                        SelectPicPopupEntity.USER_ICON_SIZE_HEIGHT,
                        HEAD_PHOTO_FILE_NAME,
                        SelectPicPopupEntity.PHOTO_REQUEST_CUT,
                        MySelfInfoFragment.this);
                break;
            case SelectPicPopupEntity.PHOTO_REQUEST_GALLERY:
                if (data != null)
                    mHeadPhotoSelectEntity.startPhotoZoom(
                            data.getData(),
                            SelectPicPopupEntity.USER_ICON_SIZE_WIDTH,
                            SelectPicPopupEntity.USER_ICON_SIZE_HEIGHT,
                            HEAD_PHOTO_FILE_NAME,
                            SelectPicPopupEntity.PHOTO_REQUEST_CUT,
                            MySelfInfoFragment.this);
                break;
            case SelectPicPopupEntity.PHOTO_REQUEST_CUT:
                mHeadPhotoSelectEntity.setPicToView(mIV_icon, HEAD_PHOTO_FILE_NAME);
                /*
                 * 上传头像到服务器
                 */
                UserUtils.changeUserIcon(MySelfInfoFragment.this.getActivity(), mIV_icon,
                        mHeadPhotoSelectEntity.getFile(HEAD_PHOTO_FILE_NAME));
                break;
            case REQUEST_UPDATE_PHONE: {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    String phone = data.getStringExtra("result.phone");
                    mTV_phone.setText(phone);
                }
                break;
            }
            case REQUEST_UPDATE_TELEPHONE:
                if (resultCode == Activity.RESULT_OK && data != null) {
                    String telephone = data.getStringExtra(ChangeTelephoneFragment.RESULT_TELEPHONE);
                    mTelephone.setText(telephone);
                }
                break;
            default:
        }
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
        ActionBarHelper.init(this, getView());
        getInfo();
    }

    //更新手机
    private void toUpdatePhoneFragment() {
        Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.liveness.ChangePhoneFragment");
        startActivityForResult(intent, REQUEST_UPDATE_PHONE);
    }

    //更新座机
    private void toUpdateTelephoneFragment() {
        Intent intent = new Intent(getActivity(), ChangeTelephoneActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ChangeTelephoneFragment.class.getName());
        startActivityForResult(intent, REQUEST_UPDATE_TELEPHONE);
    }

    //打开咚咚个人详情页面
    private void openPersonalDetails(String erp) {
        Log.d(TAG, "跳转到咚咚的个人详情页面");
//        UIHelper.showUserInfo(getContext(),erp,"ee");
        imDdService.showContactDetailInfo(getActivity(), erp);//仿照跳转聊天页面
//        getActivity().finish();
//        getActivity().onBackPressed();
//        PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_MY_SELF_INFO_LOGISTICS_HR);
        JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_personInfo_hr_click,JDMAConstants.mobile_Mine_personInfo_hr_click);
    }

//    public static void updateIconSuccess(String url) {
//        if (null != mIV_icon)
//            ImageLoaderUtils.getInstance().displayImage((String) url, mIV_icon, R.drawable.jdme_picture_user_default_white);
//        for (Observer o : observerList) {
//            o.update(null, url);
//        }
//    }

    @Override
    public void onResume() {
        super.onResume();
        // 挂件
        String pendanSmall = AppJoint.service(ImDdService.class).getPendan();
        if (!TextUtils.isEmpty(pendanSmall)) {
            ImageLoaderUtils.getInstance().displayImage(pendanSmall, me_iv_circle_bg);
        } else {
            AppJoint.service(ImDdService.class).getPendanByNet(new Callback<String>() {
                @Override
                public void onSuccess(String str) {
                    if (!TextUtils.isEmpty(str)) {
                        ImageLoaderUtils.getInstance().displayImage(str, me_iv_circle_bg);
                    } else {
                        me_iv_circle_bg.setImageResource(0);
                    }
                }

                @Override
                public void onFail() {

                }
            });
        }
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (optionFlag == OPERATE_CHANGE_AVATAE) {
            if (args == null) {
                return false;
            }
            String url = args.getString("url");
            if (TextUtils.isEmpty(url)) {
                return false;
            }
            if (null != mIV_icon)
                ImageLoaderUtils.getInstance().displayImage((String) url, mIV_icon, R.drawable.jdme_picture_user_default_white);
            for (Observer o : observerList) {
                o.update(null, url);
            }
            return true;
        }
        return false;
    }
}
