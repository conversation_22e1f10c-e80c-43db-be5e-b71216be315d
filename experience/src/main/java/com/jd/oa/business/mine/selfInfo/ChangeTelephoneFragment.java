package com.jd.oa.business.mine.selfInfo;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import com.jd.oa.annotation.FontScalable;
import com.jd.oa.experience.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.HashMap;
import java.util.Map;

@FontScalable(scaleable = false)
public class ChangeTelephoneFragment extends BaseFragment {

    public static final String RESULT_TELEPHONE = "result.telephone";

    private EditText mEditText;


    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.jdme_fragment_update_telephone, container,
                false);
        ActionBarHelper.init(this, view);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_update_telephone);
        ActionBarHelper.getActionBar(this).setDisplayHomeAsUpEnabled(true);
        setHasOptionsMenu(true);
        mEditText = view.findViewById(R.id.edit_telephone);
        return view;
    }


    @Override
    public void onDestroyView() {
        if (getActivity() != null) {
            InputMethodUtils.hideSoftInput(getActivity());
        }
        super.onDestroyView();
    }

    private void doOk() {
        String phone = mEditText.getText().toString().trim();
        updateTelephone(phone);

    }

    private void updateTelephone(final String telephone) {
        Map<String, Object> param = new HashMap<>();
        param.put("telephone", telephone);
        HttpManager.legacy().post(this, param, new SimpleRequestCallback<String>(getActivity(), true) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (info.result == null || getActivity() == null || getActivity().isFinishing())
                    return;
                ApiResponse<Object> response = ApiResponse.parse(info.result, Object.class);
                if (response.isSuccessful()) {
                    Intent intent = new Intent();
                    intent.putExtra(RESULT_TELEPHONE, telephone);
                    getActivity().setResult(Activity.RESULT_OK, intent);
                    getActivity().onBackPressed();
                } else {
                    ToastUtils.showToast(response.getErrorMessage());
                }

            }

        }, NetworkConstant.API_COMMON_CHANGE_TELEPHONE_INFO);
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        if (getActivity() != null) {
            getActivity().getMenuInflater().inflate(R.menu.jdme_menu_more, menu);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (android.R.id.home == item.getItemId() && getActivity() != null) {// actionbar 返回
            getActivity().onBackPressed();
            return true;
        } else if (R.id.action_ok == item.getItemId()) {    // 确定Menu
            doOk();
        }
        return super.onOptionsItemSelected(item);
    }
}
