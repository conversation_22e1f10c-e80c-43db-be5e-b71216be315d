package com.jd.oa.business.mine.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.experience.R;
import com.jd.oa.business.mine.model.PortraitListBean;
import com.jd.oa.ui.recycler.TypeAdapter;

public class PortraitCategoryTitleAdapter extends TypeAdapter<PortraitListBean.CategoryBean, PortraitCategoryTitleAdapter.VH> {

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        return new VH(inflater.inflate(R.layout.jdme_item_portrait_category_title, viewGroup, false));
    }

    @Override
    protected void onBindViewHolder(PortraitListBean.CategoryBean bean, VH vh, int position) {
        vh.title.setText(bean.name);
    }

    public static class VH extends RecyclerView.ViewHolder {
        TextView title;

        public VH(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
        }
    }
}
