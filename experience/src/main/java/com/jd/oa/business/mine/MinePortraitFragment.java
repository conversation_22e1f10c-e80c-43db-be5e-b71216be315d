package com.jd.oa.business.mine;

import static com.jd.oa.utils.CategoriesKt.getFileUri;

import android.Manifest;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.google.android.material.appbar.AppBarLayout;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.UserInfoEditModel;
import com.jd.oa.experience.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.view.SelectPicPopupEntity;
import com.jd.oa.business.mine.adapter.PortraitCategoryTitleAdapter;
import com.jd.oa.business.mine.adapter.PortraitInfoAdapter;
import com.jd.oa.business.mine.model.PortraitListBean;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.recycler.MultiTypeRecyclerAdapter;
import com.jd.oa.ui.widget.IosActionSheetDialog;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.UnitUtils;
import com.jd.oa.experience.util.UserUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


@Navigation(hidden = true)
public class MinePortraitFragment extends BaseFragment implements FragmentOperatingListener, AppBarLayout.OnOffsetChangedListener {

    private View mContentView;

    private ImageButton mIbBack;
    private ImageView mCiPortrait;
    private RecyclerView mRvData;

    private MultiTypeRecyclerAdapter mAdapter;
    private List<Object> mAllData;

    private final int COLUMN_COUNT = 3;

    private SelectPicPopupEntity mHeadPhotoSelectEntity;
    private String mCaptureImgName = "";
    private String mCropImgName = "";
    private final String HEAD_PHOTO_FILE_NAME = "avatar_";
    private final String CAPTURE_IMG = "capture_";

    private PortraitListBean mData;

    private PortraitInfoAdapter mInfoAdapter;

    private final String VERSION_KEY = "avatar.resource.version";

    private AppBarLayout mAppbar;
    private RelativeLayout mRlPortrait;

    private int dpInit = 110;
    private int dpNarrow = 40;

    private PopupWindow mPopupWindow;

    private int mCheckedPosition = -1;
    private ImageView mCheckedIv;
    private String mCheckedUrl;

    private ImageView me_iv_circle_bg;

    private TextView tv_setg;

    RequestOptions ops = new RequestOptions();

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mContentView = inflater.inflate(R.layout.jdme_fragment_mine_portrait, container,
                false);
        ActionBarHelper.init(this, mContentView);
        mHeadPhotoSelectEntity = new SelectPicPopupEntity(this);
        initView();
        initData();
        return mContentView;
    }

    private void initData() {
        mInfoAdapter = new PortraitInfoAdapter(new PortraitInfoAdapter.PortraitOnClickListener() {
            @Override
            public void onItemClick(String url, int position, ImageView mIvbg) {
                Glide.with(getContext()).load(url).apply(ops).into(mCiPortrait);
                showConfirmPop(position, url, mIvbg);

            }

            @Override
            public void onDismiss() {
                if (mPopupWindow.isShowing())
                    mPopupWindow.dismiss();
                mCheckedPosition = -1;
                Glide.with(getContext()).load(PreferenceManager.UserInfo.getUserCover()).apply(ops).into(mCiPortrait);
            }
        });
        mAllData = new ArrayList<>();
        mAdapter = new MultiTypeRecyclerAdapter();
        mAdapter.setData(mAllData);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), COLUMN_COUNT);
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                if (mAllData.get(position) instanceof PortraitListBean.PortraitBean) {
                    return 1;
                } else {
                    return COLUMN_COUNT;
                }
            }
        });
        mRvData.setLayoutManager(gridLayoutManager);

        mAdapter.addTypeAdapter(PortraitListBean.CategoryBean.class, new PortraitCategoryTitleAdapter());
        mAdapter.addTypeAdapter(PortraitListBean.PortraitBean.class, mInfoAdapter);
        mRvData.setAdapter(mAdapter);
        getPortraitDataByLocal();
        getPortraitData();
    }

    private void showConfirmPop(int position, String url, ImageView mIv) {
        if (null == mPopupWindow)
            return;

        if (-1 != mCheckedPosition && null != mCheckedIv)
            mCheckedIv.setVisibility(View.INVISIBLE);

        mCheckedPosition = position;
        mCheckedIv = mIv;
        mCheckedUrl = url;
        if (!mPopupWindow.isShowing())
            mPopupWindow.showAtLocation(mContentView, Gravity.BOTTOM, 0, 0);

    }

    private void initView() {
        mIbBack = mContentView.findViewById(R.id.ib_back);
        mIbBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPopupWindow.isShowing()) {
                    mPopupWindow.dismiss();
                }
                getActivity().onBackPressed();
            }
        });

        mCiPortrait = mContentView.findViewById(R.id.me_iv_circle);
        ops.placeholder(R.drawable.jdme_icon_logo_gray);
        Glide.with(getContext()).load(PreferenceManager.UserInfo.getUserCover()).apply(ops).into(mCiPortrait);
//        ImageLoaderUtils.getInstance().displayImage(PreferenceManager.UserInfo.getUserCover(), mCiPortrait, R.drawable.jdme_icon_user_defautl_avator_circle);

        mRvData = mContentView.findViewById(R.id.rv_portrait);
        mCiPortrait.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toUserIcon();
            }
        });

        mAppbar = mContentView.findViewById(R.id.me_appbar);
        mAppbar.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                int maxScroll = appBarLayout.getTotalScrollRange();
                float percentage = (float) Math.abs(verticalOffset) / (float) maxScroll;
                int originalValue = UnitUtils.dip2px(getContext(), dpInit);
                float offsetValue = UnitUtils.dip2px(getContext(), dpNarrow) * percentage;
                if (originalValue - (int) offsetValue == mRlPortrait.getWidth())
                    return;
                RelativeLayout.LayoutParams sParam = new RelativeLayout.LayoutParams(originalValue - (int) offsetValue, originalValue - (int) offsetValue);
                sParam.addRule(RelativeLayout.CENTER_IN_PARENT);
                mRlPortrait.setLayoutParams(sParam);
            }
        });

        mRlPortrait = mContentView.findViewById(R.id.me_rl_portait);

        mPopupWindow = new PopupWindow(AppBase.getAppContext());
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_view_confirm_pop, null);
        Button btnConfirm = view.findViewById(R.id.me_use);
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                savePortrait(mCheckedUrl);
            }
        });
        mPopupWindow.setContentView(view);
        mPopupWindow.setOutsideTouchable(false);
        mPopupWindow.setBackgroundDrawable(null);
        mPopupWindow.setWidth(-1);
        mPopupWindow.setHeight(ImageUtils.dp2px(getContext(), 80));

        tv_setg = mContentView.findViewById(R.id.tv_setg);
        tv_setg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String url = "https://medal-m.jd.com/web/photo";
                Router.build(DeepLink.appCenter("201912200670", "url=" + Uri.encode(url))).go(getActivity());
            }
        });
        if (showAvatarAccessories()) {
            tv_setg.setVisibility(View.VISIBLE);
        }

        me_iv_circle_bg = mContentView.findViewById(R.id.me_iv_circle_bg);
    }

    //显示设置挂件
    private boolean showAvatarAccessories(){
        UserInfoEditModel userInfoEditModel = LocalConfigHelper.getInstance(requireContext()).getUserInfoEditConfig();
        return userInfoEditModel != null && userInfoEditModel.avatarAccessories
                && TenantConfigBiz.INSTANCE.isPendantEnable();
    }

    @Override
    public boolean onBackPressed() {
        if (mPopupWindow.isShowing()) {
            mPopupWindow.dismiss();
        }
        super.onBackPressed();
        return false;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case SelectPicPopupEntity.PHOTO_REQUEST_TAKEPHOTO:
                mCropImgName = FileUtils.createImageFileName(requireContext(), HEAD_PHOTO_FILE_NAME);
                mHeadPhotoSelectEntity.startPhotoZoom(
                        mCaptureImgName,
                        SelectPicPopupEntity.USER_ICON_SIZE_WIDTH,
                        SelectPicPopupEntity.USER_ICON_SIZE_HEIGHT,
                        mCropImgName,
                        SelectPicPopupEntity.PHOTO_REQUEST_CUT,
                        MinePortraitFragment.this);
                break;
            case SelectPicPopupEntity.PHOTO_REQUEST_GALLERY:
                if (data != null) {
                    mCropImgName = FileUtils.createImageFileName(requireContext(), HEAD_PHOTO_FILE_NAME);
                    mHeadPhotoSelectEntity.startPhotoZoom(
                            data.getData(),
                            SelectPicPopupEntity.USER_ICON_SIZE_WIDTH,
                            SelectPicPopupEntity.USER_ICON_SIZE_HEIGHT,
                            mCropImgName,
                            SelectPicPopupEntity.PHOTO_REQUEST_CUT,
                            MinePortraitFragment.this);
                }
                break;
            case SelectPicPopupEntity.PHOTO_REQUEST_CUT:
                if (mCaptureImgName != null && !mCaptureImgName.isEmpty()) {
                    File captureFile = mHeadPhotoSelectEntity.getFile(mCaptureImgName);
                    if (captureFile != null && captureFile.exists()) {
                        captureFile.delete();
                    }
                }
                File cropFile = mHeadPhotoSelectEntity.getFile(mCropImgName);
                if (data != null && data.getData() != null) {
                    mCiPortrait.setImageURI(data.getData());
                } else {
                    mCiPortrait.setImageURI(getFileUri(getContext(), cropFile));
                }
                UserUtils.changeUserIcon(MinePortraitFragment.this.getActivity(), mCiPortrait, cropFile);
                break;

            default:
        }
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {

    }

//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        PermissionUtils.requestResult(requestCode, permissions, grantResults, new Runnable() {
//            @Override
//            public void run() {
//                toUserIcon();
//            }
//        }, null);
//    }

    private void toUserIcon() {
        if (getActivity() == null) {
            return;
        }
        new IosActionSheetDialog(getActivity(), IosActionSheetDialog.SheetItemColor.BLACK).builder().setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .addSheetItem(getString(R.string.me_camera), IosActionSheetDialog.SheetItemColor.BLACK,
                        which -> PermissionHelper.requestPermissions(getActivity(), getResources().getString(com.jme.common.R.string.me_request_permission_title_normal), getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                mCaptureImgName = FileUtils.createImageFileName(requireContext(), CAPTURE_IMG);
                                mHeadPhotoSelectEntity.startCamera(mCaptureImgName);
                            }

                            @Override
                            public void denied(List<String> deniedList) {

                            }
                        }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE))
                .addSheetItem(getString(R.string.me_album), IosActionSheetDialog.SheetItemColor.BLACK,
                        which -> PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_storage_normal), new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                mHeadPhotoSelectEntity.selectPictureFormGallery();
                            }

                            @Override
                            public void denied(List<String> deniedList) {

                            }
                        }, Manifest.permission.WRITE_EXTERNAL_STORAGE))
                .addSheetItem(getString(R.string.me_scan_my_icon), IosActionSheetDialog.SheetItemColor.BLACK,
                        which -> mHeadPhotoSelectEntity.checkSelectPicture())
                .show();
    }

    public void refresh() {

        if (null == mData)
            return;
        mAllData.clear();
        for (int i = 0; i < mData.avatarCategories.size(); i++) {
            mAllData.add(mData.avatarCategories.get(i));
            if (null != mData.avatarCategories.get(i).avatars) ;
            mAllData.addAll(mData.avatarCategories.get(i).avatars);
        }

        mAdapter.setData(mAllData);
        mAdapter.notifyDataSetChanged();

    }


    private void getPortraitDataByLocal() {
        String content = ApiDataCacheUtils.getDatafromCache(NetWorkManagerLogin.API2_GET_PORTRAIT_LIST);
        if (TextUtils.isEmpty(content)) {
            UserUtils.clearPortraitCache();
            return;
        }
        ApiResponse<PortraitListBean> response = ApiResponse.parse(content, new TypeToken<PortraitListBean>() {
        }.getType());
        mData = response.getData();
        refresh();
    }


    private void getPortraitData() {
        if (isLoadData())
            NetWorkManagerLogin.getPortraitList(
                    new SimpleRequestCallback<String>(getActivity().getApplicationContext(), false, false) {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            ApiResponse<PortraitListBean> response = ApiResponse.parse(info.result, new TypeToken<PortraitListBean>() {
                            }.getType());
                            if (response.isSuccessful()) {
                                mData = response.getData();
                                refresh();
                                ApiDataCacheUtils.updateCache(info.result, NetWorkManagerLogin.API2_GET_PORTRAIT_LIST);
//                                PreferenceManager.setString(VERSION_KEY, ConfigurationManager.get().getEntry(VERSION_KEY, ""));
                                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_AVATAR_RESOURCE_VERSION, ConfigurationManager.get().getEntry(VERSION_KEY, ""));

                            }
                        }
                    });
    }

    private boolean isLoadData() {
        boolean flag = false;
        String version = ConfigurationManager.get().getEntry(VERSION_KEY, "");
//        String localVersion = PreferenceManager.getString(VERSION_KEY);
        String localVersion = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_AVATAR_RESOURCE_VERSION);
        if (TextUtils.isEmpty(localVersion) || !version.equals(localVersion)) {
            flag = true;
        }
        return flag;
    }

    private void savePortrait(final String purl) {
        // 直接通过IM提供的方法来保存头像
        UserUtils.changeUserIcon(mCiPortrait, purl);
    }

    @Override
    public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
        super.onDestroyView();
    }

    @Override
    public void onResume() {
        super.onResume();
        showPortraitBg();
    }

    // 挂件
    private void showPortraitBg() {
        String pendanSmall = AppJoint.service(ImDdService.class).getPendan();
        if (!TextUtils.isEmpty(pendanSmall)) {
            ImageLoaderUtils.getInstance().displayImage(pendanSmall, me_iv_circle_bg);
        }
        AppJoint.service(ImDdService.class).getPendanByNet(new Callback<String>() {
            @Override
            public void onSuccess(String str) {
                if (!TextUtils.isEmpty(str)) {
                    ImageLoaderUtils.getInstance().displayImage(str, me_iv_circle_bg);
                } else {
                    me_iv_circle_bg.setImageResource(0);
                }
            }

            @Override
            public void onFail() {

            }
        });
    }
}
