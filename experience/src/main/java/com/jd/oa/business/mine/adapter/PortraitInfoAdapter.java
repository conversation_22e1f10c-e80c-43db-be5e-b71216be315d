package com.jd.oa.business.mine.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.experience.R;
import com.jd.oa.business.mine.model.PortraitListBean;
import com.jd.oa.ui.recycler.TypeAdapter;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;

public class PortraitInfoAdapter extends TypeAdapter<PortraitListBean.PortraitBean, PortraitInfoAdapter.VH> {

    private PortraitOnClickListener mListener;

    public PortraitInfoAdapter(PortraitOnClickListener listener) {
        super();
        mListener = listener;
    }

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        return new VH(inflater.inflate(R.layout.jdme_item_portrait_info, viewGroup, false));
    }

    @Override
    protected void onBindViewHolder(final PortraitListBean.PortraitBean bean, final VH vh, final int position) {
        vh.title.setText(bean.name);
        ImageLoaderUtils.getInstance().displayImage(bean.hdImageUrl, vh.icon, R.drawable.jdme_icon_user_default_avatar_circle);
        vh.mIvBg.setVisibility(View.INVISIBLE);
        vh.mRlPortrait.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (View.INVISIBLE == vh.mIvBg.getVisibility()) {
                    vh.mIvBg.setVisibility(View.VISIBLE);
                    mListener.onItemClick(bean.hdImageUrl, position, vh.mIvBg);
                } else {
                    vh.mIvBg.setVisibility(View.INVISIBLE);
                    mListener.onDismiss();
                }
            }
        });
    }

    public static class VH extends RecyclerView.ViewHolder {
        TextView title;
        ImageView icon;
        ImageView mIvBg;
        RelativeLayout mRlPortrait;

        public VH(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
            icon = itemView.findViewById(R.id.me_iv_circle);
            mIvBg = itemView.findViewById(R.id.me_iv_checked);
            mRlPortrait = itemView.findViewById(R.id.me_rl_item_portrait);
        }
    }

    public interface PortraitOnClickListener {
        void onItemClick(String url, int position, ImageView iv);

        void onDismiss();
    }
}
