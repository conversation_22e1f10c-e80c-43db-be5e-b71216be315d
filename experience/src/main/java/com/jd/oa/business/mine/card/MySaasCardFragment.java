package com.jd.oa.business.mine.card;

import static com.jd.oa.BaseActivity.REQUEST_QR;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.business.jdsaaslogin.util.JdSaasAvatarUtil;
import com.jd.oa.experience.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.network.utils.Utils;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.router.DeepLink;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ToastUtils;
import com.yu.bundles.album.utils.MethodUtils;
import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.ShareParam;

/*我的京WE二维码名片*/
@Route(DeepLink.MY_SAAS_QR_CARD)
public class MySaasCardFragment extends BaseFragment implements CardConstraint.ICardView {
    private View mRootView;
    private ImageView mAvatar;
    private ImageView mQt;
    private TextView mPosition, mName;
    private CardPresenter presenter;
    private String mQrURL;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdwe_fragment_my_card, container, false);
        initViews();
        initData();
        return mRootView;
    }

    private void initData() {
        presenter = new CardPresenter(this);
        presenter.getData(false);
    }

    private void initViews() {
        mRootView.findViewById(R.id.iv_back).setOnClickListener(v -> requireActivity().finish());
        mAvatar = mRootView.findViewById(R.id.jdsaas_id_mycard_avator);
        JdSaasAvatarUtil.INSTANCE.loadPersonalAvatar(requireContext(),mAvatar,PreferenceManager.UserInfo.getUserCover());
        mName = mRootView.findViewById(R.id.jdsaas_id_mycard_name);
        mPosition = mRootView.findViewById(R.id.jdsaas_id_mycard_position);
        mQt = mRootView.findViewById(R.id.jdsaas_id_mycard_qt);
        mQt.post(() -> {
            ViewGroup.LayoutParams params = mQt.getLayoutParams();
            params.height = mQt.getWidth();
            mQt.setLayoutParams(params);
        });
        mQt.setOnClickListener(v -> showBigQt());

        mRootView.findViewById(R.id.ll_mycard_scan).setOnClickListener(v -> {
            Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
            startActivityForResult(intent,REQUEST_QR);
        });
        mRootView.findViewById(R.id.ll_mycard_save).setOnClickListener(v -> {
            ConstraintLayout qtInfoLayout = mRootView.findViewById(R.id.cl_mycard_qt_info);
            Bitmap screenshot = Bitmap.createBitmap(qtInfoLayout.getWidth(),qtInfoLayout.getHeight(),Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(screenshot);
            qtInfoLayout.draw(canvas);
            MethodUtils.saveImageToGallery(requireContext(),screenshot);
            ToastUtils.showToast(requireContext(), getString(R.string.me_save_success));
        });
        mRootView.findViewById(R.id.ll_mycard_share).setOnClickListener(v -> {
            String[] platformList = new String[]{"JDMESession"};
            ConstraintLayout qtInfoLayout = mRootView.findViewById(R.id.cl_mycard_qt_info);
            Bitmap screenshot = Bitmap.createBitmap(qtInfoLayout.getWidth(),qtInfoLayout.getHeight(),Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(screenshot);
            qtInfoLayout.draw(canvas);

            Sharing.from(requireActivity()).params(
                            new ShareParam.Builder()
                                    .shareBitmap(screenshot)
                                    .shareType(ShareParam.MINE_TYPE_PICTURE)
                                    .build())
                    .show(platformList);
        });
    }

    //显示大的二维码界面
    private void showBigQt() {
        if (TextUtils.isEmpty(mQrURL)) {
            return;
        }
        new BigQrDialog(getActivity(), mQrURL).show();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_QR) {
            if (null != data) {
                String result = data.getStringExtra("result");
                ScanResultDispatcher.dispatch(requireContext(), result);
            }
        }
    }

    @Override
    public void onEnd(CardInfo info, String errorMsg, boolean success) {
        if (success) {
            ImageLoaderUtils.getInstance().displayImage(info.getVisitingCardQRCode(), mQt, R.drawable.jdme_mine_main_qt);
            mQrURL = info.getVisitingCardQRCode();
            if(MultiAppConstant.isSaasFlavor()){
                if(!TextUtils.isEmpty(info.getTeamFullName()) && !TextUtils.isEmpty(info.getPositionName())){
                    mPosition.setText(getString(R.string.jdsaas_my_card_position_format,info.getTeamFullName(),info.getPositionName()));
                }else{
                    mPosition.setText(TextUtils.isEmpty(info.getTeamFullName()) ? info.getPositionName() : info.getTeamFullName());
                }
            }else{
                if(!TextUtils.isEmpty(info.getOrganizationName()) && !TextUtils.isEmpty(info.getPositionName())) {
                    mPosition.setText(getString(R.string.jdsaas_my_card_position_format,info.getOrganizationName(),info.getPositionName()));
                }else{
                    mPosition.setText(TextUtils.isEmpty(info.getOrganizationName()) ? info.getPositionName() : info.getOrganizationName());
                }
            }
            mName.setText(info.getRealName());
        } else {
            ToastUtils.showInfoToast(Utils.isNetworkAvailable(requireContext())
                    ? errorMsg : getString(R.string.jdsaas_my_card_net_error));
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (presenter != null) {
            presenter.onDestory();
        }
    }
}
