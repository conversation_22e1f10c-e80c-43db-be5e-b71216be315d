package com.jd.oa.business.mine.main;

import android.animation.Animator;
import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.DialogFragment;
import androidx.webkit.WebViewAssetLoader;
import androidx.webkit.WebViewClientCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.experience.R;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.NetworkFileUtil;
import com.jd.oa.utils.TabletUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * Created by hufeng on 2016/8/8.
 * 我的界面中的司龄以及生日动画
 */
public class MineAnimFragment extends DialogFragment {
    public static final String ARG_TYPE = "arg.type";
    public static final String ARG_YEAR = "arg.year";
    public static final String ARG_YEAR_ENTRY_SOURCE_URL = "arg.year.entrySourceUrl";
    public static final String ARG_YEAR_MD5 = "arg.year.md5";
    public static final String TYPE_BIRTHDAY = "1";
    public static final String TYPE_HONOR = "2";
    private static final String ANIMATION = "file:///android_asset/birthday_anim/index.html";

    private OnAnimationEndListener mOnAnimationEndListener;

    private String mType;
    private int mYear;
    private FrameLayout mLottieContainer;
    private String mYearEntrySourceUrl;
    private String mYearMD5;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mType = getArguments().getString(ARG_TYPE);
            mYear = getArguments().getInt(ARG_YEAR);
            mYearEntrySourceUrl = getArguments().getString(ARG_YEAR_ENTRY_SOURCE_URL);
            mYearMD5 = getArguments().getString(ARG_YEAR_MD5);
        }
    }

    @Override
    public Dialog onCreateDialog(Bundle saveInstanceState) {
        final Dialog dialog = new Dialog(getActivity(), R.style.TransparentDialog);
        dialog.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.jdee_fragment_mine_anim);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        if (TYPE_BIRTHDAY.equals(mType)) {//生日
            WebView webView = (WebView) dialog
                    .findViewById(R.id.jdme_id_mine_main_anim);
            webView.setVisibility(View.VISIBLE);
            webView.setBackgroundColor(Color.TRANSPARENT);
            webView.getSettings().setJavaScriptEnabled(true);
            // 自动加载图片
            webView.getSettings().setLoadsImagesAutomatically(true);
            // 控件滚动条位置
            webView.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);
            webView.requestFocus();
            // 与网页配合，支持手势
            // <meta name="viewport" content="width=device-width,user-scalable=yes  initial-scale=1.0, maximum-scale=4.0">
            WebSettings settings = webView.getSettings();
            webView.setVerticalScrollbarOverlay(true);
            //viewPort这个变量如果设置ture，会造成部分5.0手机浏览部分H5页面放大到最大倍数
            //settings.setUseWideViewPort(false);//设定支持viewport
            settings.setLoadWithOverviewMode(true);
            settings.setBuiltInZoomControls(true);
            settings.setSupportZoom(true);//设定支持缩放

            webView.getSettings().setAllowFileAccessFromFileURLs(true);
            webView.getSettings().setAllowUniversalAccessFromFileURLs(true);
            webView.getSettings().setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
            webView.getSettings().setDefaultTextEncodingName("UTF-8");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                webView.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            }

            /***打开本地缓存提供JS调用**/
            webView.getSettings().setDomStorageEnabled(true);
            // This next one is crazy. It's the DEFAULT location for your app's cache
            // But it didn't work for me without this line.no hardcoded path.
            webView.getSettings().setAllowFileAccess(true);
//            webView.getSettings().setAppCacheEnabled(true);
            settings.setPluginState(WebSettings.PluginState.ON);
            DisplayMetrics metric = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(metric);
            int width = metric.widthPixels;     // 屏幕宽度（像素）
            int height = metric.heightPixels;   // 屏幕高度（像素）
            WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
            p.height = height;
            p.width = width;
            dialog.getWindow().setAttributes(p); // 设置生效

            dialog.setCancelable(false);

            WebViewAssetLoader assetLoader = new WebViewAssetLoader.Builder()
                    .addPathHandler("/assets/", new WebViewAssetLoader.AssetsPathHandler(getContext()))
                    .addPathHandler("/res/", new WebViewAssetLoader.ResourcesPathHandler(getContext()))
                    .build();
            webView.setWebViewClient(new LocalContentWebViewClient(assetLoader));

//            int year = TYPE_BIRTHDAY.equals(mType) ? 0 : mYear;
//            webView.loadUrl("javascript:window.jme_birthday_flag=" + year);
//            webView.loadUrl(ANIMATION);
            webView.loadUrl("https://appassets.androidplatform.net/assets/birthday_anim/index.html");
            webView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (mOnAnimationEndListener != null) {
                        mOnAnimationEndListener.onAnimationEnd();
                    }
                    if (getFragmentManager() == null || getFragmentManager().isDestroyed()) return;
                    dismissAllowingStateLoss();
                }
            }, 4 * 1000);
            MELogUtil.onlineI(MELogUtil.TAG_CEL, this.getClass().getSimpleName() + "show birthday success");
            MELogUtil.localI(MELogUtil.TAG_CEL, this.getClass().getSimpleName() + "show birthday success");
        } else if (TYPE_HONOR.equals(mType)) {//司齡
            mLottieContainer = dialog.findViewById(R.id.lottie_view_container);
            double density = TabletUtil.isTablet() ? minDensity() * 0.7 :  minDensity() * 0.75;
            ViewGroup.LayoutParams layoutParams = mLottieContainer.getLayoutParams();
            layoutParams.height = (int) density;
            layoutParams.width = (int) density;
            mLottieContainer.setLayoutParams(layoutParams);
            mLottieContainer.setVisibility(View.VISIBLE);
            startLottieAnim();
            MELogUtil.onlineI(MELogUtil.TAG_CEL, this.getClass().getSimpleName() + "show siling success");
            MELogUtil.localI(MELogUtil.TAG_CEL, this.getClass().getSimpleName() + "show siling success");
        }
        return dialog;
    }

    private int minDensity() {
        return getContext() == null ? 0 : Math.min(DisplayUtil.getScreenWidth(getContext()), DisplayUtil.getScreenHeight(getContext()));
    }

    private void startLottieAnim() {
        if (getActivity() == null) return;
        File animFile = NetworkFileUtil.getDownloadFile(getContext(), mYearEntrySourceUrl, mYearMD5);
        final LottieAnimationView[] lottieView = {(LottieAnimationView) LayoutInflater.from(getActivity()).inflate(
                R.layout.jdme_layout_exp_year_lottie_view, mLottieContainer, false)};
        FileInputStream inputStream = null;
        if (animFile == null) {
            lottieView[0].setAnimationFromUrl(mYearEntrySourceUrl);
        } else {
            try {
                inputStream = new FileInputStream(animFile);
            } catch (Exception e) {
                e.printStackTrace();
                lottieView[0].setAnimationFromUrl(mYearEntrySourceUrl);
            }
            if (inputStream != null) {
                lottieView[0].setAnimation(inputStream, null);
            }
        }
        final FileInputStream finalInputStream = inputStream;
        lottieView[0].setRotation(0);
        lottieView[0].setRepeatCount(4);
        lottieView[0].addAnimatorListener(new Animator.AnimatorListener() {

            @Override
            public void onAnimationStart(Animator animation) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (lottieView[0] != null) {
                            lottieView[0].cancelAnimation();
                        }
                    }
                }, 5000);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (lottieView[0] != null) {
                    lottieView[0].setVisibility(View.GONE);
                    mLottieContainer.removeView(lottieView[0]);
                    lottieView[0] = null;
                    if (null != mOnAnimationEndListener) {
                        mOnAnimationEndListener.onAnimationEnd();
                    }
                    try {
                        if (finalInputStream != null) {
                            finalInputStream.close();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (getFragmentManager() == null || getFragmentManager().isDestroyed()) return;
                dismissAllowingStateLoss();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mLottieContainer.addView(lottieView[0]);
        lottieView[0].playAnimation();
    }

    public void setOnAnimationEndListener(OnAnimationEndListener onAnimationEndListener) {
        mOnAnimationEndListener = onAnimationEndListener;
    }

    public interface OnAnimationEndListener {
        void onAnimationEnd();
    }


    private static class LocalContentWebViewClient extends WebViewClientCompat {

        private final WebViewAssetLoader mAssetLoader;

        LocalContentWebViewClient(WebViewAssetLoader assetLoader) {
            mAssetLoader = assetLoader;
        }

        @Override
        @RequiresApi(21)
        public WebResourceResponse shouldInterceptRequest(WebView view,
                                                          WebResourceRequest request) {
            return mAssetLoader.shouldInterceptRequest(request.getUrl());
        }

        @Override
        @SuppressWarnings("deprecation") // to support API < 21
        public WebResourceResponse shouldInterceptRequest(WebView view,
                                                          String url) {
            return mAssetLoader.shouldInterceptRequest(Uri.parse(url));
        }
    }
}
