package com.jd.oa.business.mine;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDialog;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;

import com.jd.oa.experience.R;
import com.jd.oa.business.mine.behavior.DragDialogBehavior;


/**
 * Created by peidongbiao on 2018/7/26.
 */

public class SignatureEditDialog extends AppCompatDialog {
    private static final String TAG = "SignatureEditDialog";
    private ViewGroup mContentParent;
    private View mContentView;
    private EditText mEdit;
    private Button mBtnSave;
    private OnSaveClickListener mOnSaveClickListener;
    private String mSignature;

    public SignatureEditDialog(@NonNull Context context) {
        this(context, 0);
    }

    public SignatureEditDialog(@NonNull Context context, final int theme) {
        super(context, R.style.BottomDialogStyle);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        mContentView = getLayoutInflater().inflate(R.layout.jdme_dialog_signature_edit, null);
        setContentView(mContentView);
        mContentParent = (ViewGroup) mContentView.getParent();
        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mEdit = findViewById(R.id.edit);
        mBtnSave = findViewById(R.id.btn_save);
        mEdit.setText(mSignature);
        mEdit.setSelection(mEdit.getText().length());
        mBtnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnSaveClickListener != null) {
                    mOnSaveClickListener.onClick(v, mEdit.getText().toString());
                    dismiss();
                }
            }
        });

        View layout = findViewById(R.id.layout);
        CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) layout.getLayoutParams();
        DragDialogBehavior behavior = (DragDialogBehavior) layoutParams.getBehavior();
        behavior.setOnStateChangeListener(new DragDialogBehavior.OnStateChangeListener() {
            @Override
            public void onChange(int state) {
                if (state == DragDialogBehavior.STATE_HIDDEN) {
                    cancel();
                }
            }
        });
    }

    public void setOnSaveClickListener(OnSaveClickListener onSaveClickListener) {
        mOnSaveClickListener = onSaveClickListener;
    }

    public void setSignature(String signature) {
        mSignature = signature;
        if (mEdit != null) {
            mEdit.setText(mSignature);
            mEdit.setSelection(mEdit.getText().length());
        }
    }

    public String getSignature() {
        return mEdit.getText().toString();
    }

    public interface OnSaveClickListener {
        void onClick(View view, String content);
    }
}