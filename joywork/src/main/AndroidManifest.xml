<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>

        <activity
            android:name="com.jd.oa.joywork.team.SelectTaskGroupActivity"
            android:exported="true"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.jd.oa.joywork.team.SelectProjectTaskGroupActivity"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.joywork.team.SelectProjectActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.joywork.detail.ui.TaskDetailActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MainActivityTheme"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".urge.JoyWorkUrgeActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".detail.ui.des.JoyWorkDesActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".detail.ui.risk.JoyWorkRiskActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.jd.oa.joywork.collaborator.CollaboratorListActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.jd.oa.joywork.executor.ExecutorListActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.jd.oa.joywork.executor.CCListActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.jd.oa.joywork.executor.ExecutorStateListActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".team.create.TeamCreateActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.jd.oa.joywork.team.TeamMemberListActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.jd.oa.joywork.team.TeamDetailActivity"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.jd.oa.joywork2.list.project.TeamDetail2Activity"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.jd.oa.joywork.team.ProjectSettingActivity"
            android:configChanges="keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name="com.jd.oa.joywork.team.ProjectSettingActivity2"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateUnchanged" />
        <activity
            android:name="com.jd.oa.joywork.create.JoyWorkCreateActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateUnchanged" />
        <activity
            android:name="com.jd.oa.joywork.group.GroupMainActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.jd.oa.joywork.urge.JoyWorkUrgeList"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.jd.oa.joywork.create.CreateOwnerListActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.jd.oa.joywork.JoyWorkImageActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".detail.JoyWorkHistoryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".team.kpi.GoalListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".team.kpi.GoalDetailActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".team.kpi.SelectGoalActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".group.SelectGroupRosterActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />

        <activity
            android:name=".team.kpi.LikeListActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".team.kpi.GoalCreateActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".team.KREditActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysVisible" />
        <activity
            android:name=".team.kpi.WeeklySummaryActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".team.kpi.VisibleSettingActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".team.kpi.VisibleUserListActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".team.kpi.OrgListActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".executor.SetOwnerActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />
        <activity
            android:name=".team.kpi.DepartmentSearchActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />

        <activity
            android:name=".team.kpi.VisibleDepartmentsListActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait" />

        <activity
            android:name=".team.kpi.KRCommentActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".search.JoyWorkSearchActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".team.chat.ProjectSelectorActivity"
            android:configChanges="screenSize|screenLayout|smallestScreenSize|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
    </application>
</manifest>