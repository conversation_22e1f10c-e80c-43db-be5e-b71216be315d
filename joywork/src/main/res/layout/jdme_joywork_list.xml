<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.view.swipe.SwipeMenuParent xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/swipe_menu_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/tipsView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#f0f2f3"
                android:paddingBottom="8dp" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>


    <LinearLayout
        android:id="@+id/filterContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical" />
</com.jd.oa.joywork.view.swipe.SwipeMenuParent>