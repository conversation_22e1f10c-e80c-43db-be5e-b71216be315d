<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/offset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:divider="@drawable/joywork_ll_divider_2dp"
            android:dividerPadding="2dp"
            android:orientation="horizontal" />

        <LinearLayout
            android:id="@+id/offset2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:divider="@drawable/joywork_ll_divider_2dp"
            android:dividerPadding="2dp"
            android:orientation="horizontal" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/emptyContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical" />
</merge>