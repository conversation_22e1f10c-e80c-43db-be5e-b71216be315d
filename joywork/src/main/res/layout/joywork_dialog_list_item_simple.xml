<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/joywork_del"
        android:paddingVertical="16dp"
        android:textColor="#333333"
        android:textSize="18dp" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/joywork_divider" />
</LinearLayout>
