<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/hintTextContainer"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:paddingStart="6dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/hintText1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxWidth="64dp"
        android:maxLines="1"
        android:textColor="#333333"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/hintText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:includeFontPadding="false"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#333333"
        android:textSize="14dp" />
</LinearLayout>