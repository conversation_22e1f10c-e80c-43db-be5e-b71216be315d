<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/joywork_shortcut_create_dialog_bg"
    android:orientation="vertical">

    <androidx.legacy.widget.Space
        android:layout_width="match_parent"
        android:layout_height="24dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:gravity="top"
            android:hint="@string/joywork_shortcut_title_hint"
            android:inputType="textMultiLine"
            android:maxLength="500"
            android:maxLines="6"
            android:textColor="#232930"
            android:textColorHint="#8F959E"
            android:textSize="16dp"
            android:theme="@style/JoyWorkEditTextStyle"
            tools:text="" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="24dp" />

    <HorizontalScrollView
        android:id="@+id/mHorScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="12dp"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Space
                android:layout_width="16dp"
                android:layout_height="1dp" />
            <!-- 团队待办分组-->
            <LinearLayout
                android:id="@+id/assign_container"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginStart="8dp"
                android:gravity="center_vertical"
                android:minHeight="28dp"
                android:paddingHorizontal="12dp">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/icon_padding_grouping"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_16"/>

                <TextView
                    android:id="@+id/assign_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="#333333"
                    android:textColorHint="#232930"
                    android:textSize="14dp" />
            </LinearLayout>

            <View
                android:id="@+id/mGroupDivider"
                android:layout_width="0.5dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="16dp"
                android:background="#E6E6E6" />

            <LinearLayout
                android:id="@+id/owner_container"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:gravity="center_vertical"
                android:minHeight="28dp"
                android:orientation="horizontal"
                android:paddingHorizontal="12dp">

                <com.jd.oa.joywork.view.JoyWorkAvatarView
                    android:id="@+id/mAvatarView"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    app:alwaysShowHintText="true"
                    app:avatarSize="20dp"
                    app:emptyLayout="@layout/joywork_executor_empty_layout"
                    app:hatPaddingExtra="4dp"
                    app:maxChildCount="3" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mExecutorArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:paddingStart="10dp"
                    android:text="@string/icon_direction_right"
                    android:textColor="#999999"
                    android:textSize="@dimen/JMEIcon_14"/>
            </LinearLayout>

            <View
                android:layout_width="0.5dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="16dp"
                android:background="#E6E6E6" />

            <LinearLayout
                android:id="@+id/mTodayContainer"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="12dp"
                android:visibility="gone">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_edit_historyright"
                    android:textColor="#29CC31"
                    android:textSize="@dimen/JMEIcon_16"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:text="@string/joywork_plan_alert_today"
                    android:textColor="#333333"
                    android:textSize="14dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/mTomorrowContainer"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="12dp"
                android:visibility="gone">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_direction_rightcircle"
                    android:textColor="#547ef7"
                    android:textSize="@dimen/JMEIcon_16"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:text="@string/joywork_tomorrow"
                    android:textColor="#333333"
                    android:textSize="14dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/time_container"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:gravity="center_vertical"
                android:minHeight="28dp"
                android:orientation="horizontal"
                android:paddingStart="12dp">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/time_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/icon_general_calendarl"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_16"/>

                <TextView
                    android:id="@+id/time_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="1dp"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingEnd="12dp"
                    android:text="@string/joywork_shortcut_deadline_tips"
                    android:textColor="#333333"
                    android:textSize="14dp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/alarm_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingEnd="12dp"
                    android:text="@string/icon_general_bell"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_14"
                    android:visibility="gone" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/dup_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingEnd="12dp"
                    android:text="@string/icon_general_repeat"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_14"
                    android:visibility="gone" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/clear_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingEnd="12dp"
                    android:text="@string/icon_prompt_close"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_14"
                    android:visibility="gone" />

            </LinearLayout>

            <Space
                android:layout_width="16dp"
                android:layout_height="1dp" />
        </LinearLayout>
    </HorizontalScrollView>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <LinearLayout
            android:id="@+id/mAlertContainer"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:background="@drawable/solid_all_4_f5f5f5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="8dp"
            android:visibility="gone">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mAlertIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/icon_general_bell"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_16"/>

            <TextView
                android:id="@+id/mAlertText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:includeFontPadding="false"
                android:text="@string/joywork_alert_no"
                android:textColor="#333333"
                android:textSize="14dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/dup_container"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/iconDuplicate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_general_repeat"
                android:textColor="#8F959E"
                android:textSize="@dimen/JMEIcon_16"/>

            <TextView
                android:id="@+id/textDuplicate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="@string/joywork_duplicate_text"
                android:textColor="#8F959E"
                android:textSize="14dp" />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="12dp">

        <TextView
            android:id="@+id/detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:text="@string/joywork_shortcut_more"
            android:textColor="#333333"
            android:textSize="14dp" />

        <LinearLayout
            android:id="@+id/mSendDD"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/mSendIcon"
                android:layout_width="26dp"
                android:layout_height="22dp"
                android:layout_gravity="center_vertical"
                android:paddingHorizontal="6dp"
                android:paddingVertical="4dp"
                android:src="@drawable/joywork_cb" />

            <TextView
                android:id="@+id/mSendText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_send_dd"
                android:textColor="#666666"
                android:textSize="14dp" />
        </LinearLayout>

        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/create"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/joywork_new"
            android:textSize="14dp" />

    </LinearLayout>
</LinearLayout>