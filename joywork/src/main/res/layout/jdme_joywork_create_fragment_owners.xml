<?xml version="1.0" encoding="utf-8"?><!--            负责人/指派给-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mAvatarContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="5dp">

    <com.jd.oa.ui.IconFontView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="12dp"
        android:includeFontPadding="false"
        android:text="@string/icon_edit_addcontact"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_18"/>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="6dp"
        android:background="@drawable/joywork_item_bg"
        android:gravity="center_vertical"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp">

        <!--        头像-->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/owners_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false" />

        <!--姓名-->
        <TextView
            android:id="@+id/relation_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:gravity="center_vertical"
            android:hint="@string/joywork_create_relation_hint"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingStart="12dp"
            android:text="@string/icon_direction_right"
            android:textColor="#D8D8D8"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/mAvatarAdd"
        android:layout_width="24dp"
        android:layout_height="wrap_content"
        android:background="@drawable/joywork_line_circle_dashed"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_add"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_14"/>

    <TextView
        android:id="@+id/empty_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="@string/joywork_create_avatar_empty"
        android:textColor="#8F959E"
        android:textSize="14dp" />

</LinearLayout>