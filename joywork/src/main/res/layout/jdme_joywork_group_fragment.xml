<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/titleContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:id="@+id/mTabContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mTabLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/mFilterContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/mFilterName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="7dp"
                android:paddingVertical="8dp"
                android:textColor="@color/joywork_black"
                android:textSize="12dp" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mFilterIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/icon_padding_caredown"
                android:textColor="@color/joywork_filter_icon_text_sel"
                android:textSize="@dimen/JMEIcon_10"/>
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#EDEDEE" />

    <com.jd.oa.joywork.view.UntouchableViewPager
        android:id="@+id/mViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />
</LinearLayout>