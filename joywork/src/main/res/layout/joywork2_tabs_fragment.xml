<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="5dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginBottom="4dp"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mToggleMenu"
            android:layout_width="32dp"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/circle_bg_gray_f3f3f3"
            android:gravity="center"
            android:text="@string/icon_edit_justify"
            android:textSize="@dimen/JMEIcon_16" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_weight="1">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/mTabs"
                style="@style/TabStyle"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:background="@drawable/joywork2_tab_bg" />
        </FrameLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/view_type"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="3dp"
            android:gravity="center"
            android:text="@string/icon_edit_unorderedlist"
            android:textColor="#1B1B1B"
            android:textSize="@dimen/JMEIcon_16" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mShowCondition"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:gravity="center"
            android:text="@string/icon_general_screen"
            android:textColor="@color/jdme_work_filter_button_selector"
            android:textSize="@dimen/JMEIcon_16" />
    </LinearLayout>
</LinearLayout>