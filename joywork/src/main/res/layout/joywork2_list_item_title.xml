<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_title_container"
    android:layout_width="wrap_content"
    android:layout_height="44dp"
    android:baselineAligned="false"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">

    <View
        android:visibility="gone"
        android:id="@+id/indentation"
        android:layout_width="30dp"
        android:layout_height="1dp" />

    <FrameLayout
        android:id="@+id/cb_task_container"
        android:layout_width="wrap_content"
        android:layout_height="44dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/cb_task"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_circle"
            android:textSize="@dimen/JMEIcon_18" />

        <ImageView
            android:id="@+id/cb_task2"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center"
            android:src="@drawable/joywork_cb"
            android:visibility="visible" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/titleContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:layout_marginTop="0.5dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/risk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:includeFontPadding="false"
            android:paddingHorizontal="4dp"
            android:text="@string/joywork_risk_problem"
            android:textSize="10dp" />

        <!-- 标题-->
        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#232930"
            android:textSize="14dp"
            tools:text="@string/joywork_tomorrow" />

    </LinearLayout>

</LinearLayout>