<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:background="@drawable/jdme_selector_bg_click">

    <TextView
        android:id="@+id/tv_group_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_toStartOf="@id/iftv_check"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="#232930"
        android:textSize="16dp"
        tools:text="" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iftv_check"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="16dp"
        android:text="@string/icon_prompt_check"
        android:textSize="@dimen/JMEIcon_18"        android:textColor="#FE3B30" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="16dp"
        android:background="#F0F3F3" />
</RelativeLayout>