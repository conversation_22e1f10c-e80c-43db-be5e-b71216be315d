<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/mRefreshView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <RelativeLayout
        android:id="@+id/addView"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_gravity="bottom|end"
        android:background="@drawable/jdme_joywork_add_bg">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_add"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/JMEIcon_20"/>
    </RelativeLayout>
</FrameLayout>