<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/mRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingEnd="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:text="@string/icon_direction_left"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_22"/>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/icon_padding_target"
                android:textColor="#FF242931"
                android:textSize="@dimen/JMEIcon_18"/>

            <com.jd.oa.ui.CircleImageView
                android:id="@+id/mAvatar"
                android:layout_width="18dp"
                android:layout_height="18dp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/mTitleView"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="#FF242931"
                    android:textSize="18dp"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/mFocus"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mFocus"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingHorizontal="8dp"
                    android:text="@string/icon_padding_followheart"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_16"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/mTitleView"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mSearch"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingHorizontal="8dp"
                android:text="@string/icon_general_search"
                android:textColor="@color/icon_black"
                android:textSize="@dimen/JMEIcon_22" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mOrganizationList"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingHorizontal="8dp"
                android:text="@string/icon_general_personnellist"
                android:textColor="@color/icon_black"
                android:textSize="@dimen/JMEIcon_22" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mShare"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingHorizontal="8dp"
                android:text="@string/icon_general_share1"
                android:textColor="@color/icon_black"
                android:textSize="@dimen/JMEIcon_22" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mWeeklySummary"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingHorizontal="8dp"
                android:text="@string/joywork_weekly_summary_icon"
                android:textColor="@color/icon_black"
                android:textSize="@dimen/JMEIcon_22" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mTabsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:paddingHorizontal="6dp" />

        <com.jd.oa.joywork.view.UntouchableViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/mAddView"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_gravity="bottom|end"
        android:background="@drawable/jdme_joywork_add_bg">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_add"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/JMEIcon_20"/>
    </RelativeLayout>
</FrameLayout>