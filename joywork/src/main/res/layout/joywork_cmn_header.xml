<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/joywork_list_tabbar_title_height"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/header_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_prompt_close"
        android:textColor="#232930"
        android:textSize="@dimen/JMEIcon_20"/>

    <TextView
        android:id="@+id/header_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="#232930"
        android:textSize="18dp" />

    <TextView
        android:id="@+id/header_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingHorizontal="16dp"
        android:text="@string/me_save"
        android:textColor="#fe3b30"
        android:textSize="14dp" />

</LinearLayout>