<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    app:dividerStartId="@id/mIcon">

    <ImageView
        android:id="@+id/mIcon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="16dp"
        android:src="@drawable/joywork_project_icon" />

    <TextView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text=""
        android:textColor="#232930"
        android:textSize="16dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/check"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_prompt_close"
        android:textColor="#999999"
        android:textSize="@dimen/JMEIcon_16"/>

</com.jd.oa.joywork.team.view.DividerLinearLayout>