<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/ll_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_groups"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingStart="10dp"
            android:layout_weight="1"
            app:tabBackground="@color/transparent"
            app:tabPaddingEnd="6dp"
            app:tabPaddingStart="6dp"
            app:tabRippleColor="@color/transparent">

        </com.google.android.material.tabs.TabLayout>

        <FrameLayout
            android:id="@+id/fl_add_group"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="16dp">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/next_week"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:paddingStart="8dp"
                android:text="@string/icon_prompt_add"
                android:textColor="#6A6A6A"
                android:textSize="@dimen/JMEIcon_16" />

        </FrameLayout>

    </LinearLayout>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_header" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <include
                android:id="@+id/empty_layout"
                layout="@layout/jdme_joywork2_empty"
                android:visibility="gone" />

            <include
                android:id="@+id/fail_layout"
                layout="@layout/jdme_joywork2_fail"
                android:visibility="gone" />
        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
