<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="detail"
            type="com.jd.oa.joywork.detail.data.entity.TaskDetailEntity" />

        <variable
            name="presenter"
            type="com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter" />

        <import type="android.view.View" />

        <import type="com.jd.oa.joywork.detail.ui.TaskDetailFragment" />
    </data>

    <com.jd.oa.joywork.detail.ui.TaskScrollView
        android:id="@+id/scroll_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="SpUsage">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/mUpdateTipsContainer"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="#1A29CC31"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp">

                <TextView
                    android:id="@+id/mUpdateTips"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text=""
                    android:textColor="#333333"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/mUpdateTipsBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/joywork_shape_ff00a558_4dp"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/joywork_notify"
                    android:textColor="#FF00A558"
                    android:textSize="14dp" />
            </LinearLayout>
            <!-- 风险待办/待办提示-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFF2F0"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingTop="8dp"
                android:paddingBottom="12dp"
                android:visibility="visible"
                app:riskMsgContainer="@{detail}">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_padding_infocirclecircle"
                    android:textColor="#FFFE3E33"
                    android:textSize="@dimen/JMEIcon_18"
                    app:riskMsgIcon="@{detail}" />

                <com.jd.oa.joywork.view.ExpandLinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:orientation="vertical"
                    app:riskMsgParent="@{detail}">

                    <TextView
                        android:id="@+id/textView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:ellipsize="end"
                        android:text=""
                        android:textColor="#FF333333"
                        android:textSize="14dp"
                        app:riskMsg="@{detail}" />

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/ll_handled"
                        android:layout_width="match_parent"
                        android:layout_height="12dp"
                        android:gravity="center"
                        android:textSize="@dimen/JMEIcon_14"                        android:text="@string/icon_direction_down" />

                    <com.jd.oa.joywork.ui.AverageLinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:divider="@drawable/joywork_ll_divider_12"
                        android:dividerPadding="13dp"
                        android:gravity="right"
                        android:orientation="horizontal"
                        android:showDividers="middle">

                        <TextView
                            android:id="@+id/updateDes"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:gravity="center"
                            android:onClick="@{(theView) -> presenter.updateRisk(theView,detail)}"
                            android:text="@string/joywork_detail_risk_update"
                            app:riskMsgUpdate="@{detail}" />

                        <TextView
                            android:id="@+id/riskDiscuss"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:gravity="center"
                            android:text="@string/joywork_detail_discuss"
                            app:riskMsgDiscuss="@{detail}" />

                        <TextView
                            android:id="@+id/riskResolve"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:gravity="center"
                            android:onClick="@{(theView) -> presenter.solveRisk(theView,detail)}"
                            android:text="@string/joywork_detail_solve"
                            app:riskMsgSolve="@{detail}" />
                    </com.jd.oa.joywork.ui.AverageLinearLayout>
                </com.jd.oa.joywork.view.ExpandLinearLayout>
            </LinearLayout>
            <!-- 合并待办-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="#1A4C7CFF"
                android:gravity="center_vertical"
                android:onClick="@{(theView) -> presenter.mergeInfo(theView,detail)}"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:visibility="visible"
                app:mergeTaskParent="@{detail}">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/joywork_detail_merge_tips"
                    android:textColor="#FF232930" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:autoLink="all"
                    android:ellipsize="end"
                    android:paddingStart="8dp"
                    android:text=""
                    android:textColor="#4c7cff"
                    app:mergeTask="@{detail}" />
            </LinearLayout>
            <!-- 父待办-->
            <TextView
                android:id="@+id/parent"
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="8dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:paddingHorizontal="8dp"
                android:text="@string/joywork_parent_title"
                android:textColor="@color/icon_gray"
                android:textSize="@dimen/joywork_title_sub"
                android:visibility="visible"
                app:parentTask="@{detail}" />
            <!--来自-->
            <TextView
                android:id="@+id/from"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/joywork_shape_ffedf1ff_4dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:text="@string/me_joywork_from"
                android:textColor="@color/icon_black"
                android:textSize="14dp"
                app:sourceName="@{detail}" />

            <!-- 内容，背景-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingTop="14dp"
                android:paddingBottom="12dp">

                <EditText
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:inputType="textMultiLine"
                    android:lineSpacingMultiplier="1"
                    android:maxLength="500"
                    android:minLines="1"
                    android:text="@{detail.joyWorkDetail.title}"
                    android:textColor="@color/icon_black"
                    android:textSize="20dp"
                    app:edit="@{detail}"
                    tools:ignore="LabelFor,TouchTargetSizeCheck,SpeakableTextPresentCheck" />

                <TextView
                    android:id="@+id/des"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:hint="@string/joy_work_description"
                    android:includeFontPadding="false"
                    android:lineSpacingMultiplier="1"
                    android:maxLength="1000"
                    android:onClick="@{(theView) -> presenter.joyworkDes(theView,detail)}"
                    android:text=""
                    android:textColor="#666666"
                    android:textColorHint="@color/title_sub"
                    android:textSize="@dimen/joywork_title_sub"
                    app:joyworkDes="@{detail.joyWorkDetail.remark}" />
            </LinearLayout>
            <!-- 云文档列表-->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/doc_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:nestedScrollingEnabled="false"
                android:visibility="@{detail.joyWorkDetail.documents.size()>0?View.VISIBLE : View.GONE}"
                app:layoutManager="LinearLayoutManager" />
            <!-- 附件列表-->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/res_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:nestedScrollingEnabled="false"
                android:paddingBottom="8dp"
                android:visibility="@{detail.joyWorkDetail.resources.size()>0?View.VISIBLE : View.GONE}"
                app:layoutManager="LinearLayoutManager" />
            <!-- 关联目标-->
            <LinearLayout
                android:id="@+id/targetContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingTop="8dp"
                app:linkTarget="@{detail.joyWorkDetail}" />
            <!-- 关联清单-->
            <LinearLayout
                android:id="@+id/orderContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:linkOrder="@{detail.joyWorkDetail}" />

            <!-- 关联系统清单-->
            <LinearLayout
                android:id="@+id/sysOrderContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:linkSysOrder="@{detail.joyWorkDetail}" />
            <!--  负责人-->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:ownerUI="@{detail}" />

            <!--  协作者/关注人-->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:relationList="@{detail.joyWorkDetail}" />

            <!-- 截止时间-->
            <LinearLayout
                android:id="@+id/deadlineContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:endTime="@{detail.joyWorkDetail}" />

            <!-- 提醒我-->
            <LinearLayout
                android:id="@+id/remindContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:onClick="@{(theView) -> presenter.alert(theView,detail)}"
                android:orientation="vertical"
                app:remindMeVisible="@{detail}" />
            <!--  重复-->
            <LinearLayout
                android:id="@+id/dupContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:onClick="@{(theView) -> presenter.duplicate(theView,detail)}"
                android:orientation="vertical"
                app:duplicateContainer="@{detail.joyWorkDetail}" />

            <!--  优先级-->
            <LinearLayout
                android:id="@+id/priorityContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="@{(theView) -> presenter.priority(theView,detail)}"
                android:orientation="vertical"
                app:priorityType="@{detail.joyWorkDetail.priorityType}" />

            <!--启动时间-->
            <LinearLayout
                android:id="@+id/launchContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:onClick="@{(theView) -> presenter.launchTime(theView,detail)}"
                android:orientation="vertical"
                app:launchTimeVisible="@{detail}" />

            <!--  自定义字段-->
            <com.jd.oa.joywork.detail.ui.NotifyLinearLayout
                android:id="@+id/mark_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:markContainer="@{detail}" />

            <!-- 待办风险-->
            <LinearLayout
                android:id="@+id/riskContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:onClick="@{(theView) -> presenter.risk(theView,detail)}"
                android:orientation="horizontal"
                app:risk="@{detail.joyWorkDetail.riskStatus}" />

            <!-- 添加更多-->
            <LinearLayout
                android:id="@+id/moreContainer"
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                app:moreContainer="@{detail}" />

            <!-- 添加子待办-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/subTaskTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:subTitleContainer="@{detail}">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/child_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:includeFontPadding="false"
                    android:text="@string/icon_general_subtask"
                    android:textColor="@color/icon_gray"
                    android:textSize="@dimen/JMEIcon_18"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/child"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/joy_work_child_works"
                    android:textColor="@color/title_sub"
                    android:textSize="@dimen/joywork_title_sub"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/child_icon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:subTitle="@{detail}" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:onClick="@{(theView) -> presenter.urgeSubjoywork(theView,detail)}"
                    android:orientation="horizontal"
                    android:paddingEnd="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/child"
                    app:subJoyworkUrge="@{detail}">

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/subJoyworkUrge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/joywork_detail_urgy_icon"
                        android:textColor="#4C7CFF"
                        android:textSize="@dimen/JMEIcon_14"/>

                    <com.jd.oa.ui.IconFontView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="@string/icon_direction_right"
                        android:textColor="#666666"
                        android:textSize="@dimen/JMEIcon_14"/>

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.jd.oa.joywork.detail.ui.NotifyLinearLayout
                android:id="@+id/subContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/child"
                app:subListContainer="@{detail}" />


            <!--  点赞-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="24dp">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/respect_icon"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:onClick="@{(theView) -> presenter.switchLike(theView,detail)}"
                    android:text="@{presenter.getLikeIcon(detail)}"
                    android:textColor="#FFFFCC3E"
                    android:textSize="@dimen/JMEIcon_24" />

                <TextView
                    android:id="@+id/like"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@{presenter.getLikeTip(detail)}"
                    android:textColor="@color/title_sub"
                    android:textSize="12dp"
                    app:drawableEndCompat="@drawable/line_solid"
                    app:drawableStartCompat="@drawable/line_solid" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_marginTop="7dp"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="@{detail.joyWorkDetail.taskPraises.size()>0?View.VISIBLE : View.GONE}">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/like_list"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="LinearLayoutManager" />

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/like_more"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/joywork_btn_bg"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/icon_tabbar_more"
                        android:textColor="@color/icon_gray"
                        android:textSize="@dimen/JMEIcon_16"
                        android:visibility="@{detail.joyWorkDetail.taskPraises.size()>TaskDetailFragment.MAX_SHOW_LIKE?View.VISIBLE : View.GONE}" />
                </LinearLayout>
            </LinearLayout>

            <com.jd.me.web2.webview.JMEWebview
                android:id="@+id/comment"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:scrollbars="none"
                android:visibility="invisible"
                app:taskId="@{detail.joyWorkDetail.taskId}" />

            <View
                android:id="@+id/keyboard_blank"
                android:layout_width="match_parent"
                android:layout_height="0dp" />
        </LinearLayout>

    </com.jd.oa.joywork.detail.ui.TaskScrollView>
</layout>