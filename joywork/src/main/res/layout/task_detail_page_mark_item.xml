<com.jd.oa.joywork.detail.ui.PercentLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="16dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/mIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:includeFontPadding="false"
        android:text="@string/icon_general_information"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_18" />

    <LinearLayout
        android:id="@+id/mark_container"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="12dp"
        android:minWidth="50dp"
        android:paddingHorizontal="12dp">

        <TextView
            android:id="@+id/mark_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text=""
            android:textColor="#8F959E"
            android:textSize="@dimen/joywork_title_sub" />

        <View
            android:id="@+id/mark_value_vh"
            android:layout_width="12dp"
            android:layout_height="1dp"
            android:layout_gravity="center"
            android:background="#8F959E" />
    </LinearLayout>
</com.jd.oa.joywork.detail.ui.PercentLinearLayout>