<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/space"
        android:layout_width="1dp"
        android:layout_height="8dp" />

    <com.jd.oa.joywork.view.HorizontalPercentLinearLayout
        android:id="@+id/rl_1"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal"
        app:childrenIndex="0,2">

        <TextView
            android:id="@+id/tv_project_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="@drawable/solid_all_4_f5f5f5"
            android:ellipsize="end"
            android:gravity="center_vertical|start"
            android:maxLines="1"
            android:paddingHorizontal="8dp"
            android:textColor="#333333"
            android:textSize="14dp"
            tools:text="" />

        <View
            android:layout_width="8dp"
            android:layout_height="1dp" />

        <LinearLayout
            android:id="@+id/mDownArrowContainer"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:background="@drawable/solid_all_4_f5f5f5"
            android:orientation="horizontal"
            android:paddingHorizontal="8dp">

            <TextView
                android:id="@+id/tv_group_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="#333333"
                android:textSize="14dp"
                tools:text="" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mDownArrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:paddingStart="10dp"
                android:text="@string/icon_direction_down"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_14" />
        </LinearLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_close"
            android:layout_width="28dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="16dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#FF8F959E"
            android:textSize="@dimen/JMEIcon_14" />

    </com.jd.oa.joywork.view.HorizontalPercentLinearLayout>
</LinearLayout>