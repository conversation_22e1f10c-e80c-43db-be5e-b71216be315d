<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/rl_toolbar_normal"
        android:layout_width="match_parent"
        android:layout_height="44dp"
       >

        <ImageView
            android:id="@+id/iv_me_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="6dp"
            android:padding="10dp"
            android:src="@drawable/joywork_leftarrow" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_select_group_list_title"
            android:textColor="#242931"
            android:textSize="18dp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_ok"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@null"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/me_ok"
            android:textColor="@color/joywork_red" />

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_group"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
      />
</LinearLayout>