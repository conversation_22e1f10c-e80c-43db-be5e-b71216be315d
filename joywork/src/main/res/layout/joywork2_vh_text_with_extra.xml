<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="20dp"
    android:paddingBottom="8dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/indicator"
        android:layout_width="46dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingStart="16dp"
        android:text="@string/icon_direction_down"
        android:textColor="#FF8F959E"
        android:textSize="@dimen/JMEIcon_14" />

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#FF232930"
        android:textSize="16dp"
        android:textStyle="bold" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_tabbar_more"
        android:textColor="#FF8F959E"
        android:textSize="@dimen/JMEIcon_18" />
</LinearLayout>

