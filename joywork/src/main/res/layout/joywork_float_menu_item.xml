<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingVertical="9dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/menu_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="@string/icon_tabbar_joymail_de"
        android:textColor="#1b1b1b"
        android:textSize="@dimen/JMEIcon_14" />

    <TextView
        android:id="@+id/menu_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@id/menu_icon"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="123"
        android:textColor="#1b1b1b"
        android:textSize="14dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/checked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="2dp"
        android:text="@string/icon_prompt_check"
        android:textColor="#F63218"
        android:textSize="@dimen/JMEIcon_14" />
</RelativeLayout>