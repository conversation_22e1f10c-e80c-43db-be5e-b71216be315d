<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/mDivider"
        android:layout_width="1dp"
        android:layout_height="12dp" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="#FF333333"
        android:textSize="12dp" />

    <TextView
        android:id="@+id/ll_handled"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingTop="8dp"
        android:text="@string/joywork_expand"
        android:textColor="#FFFE3B30"
        android:textSize="12dp" />
</LinearLayout>
