<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRoot"
    android:layout_width="134dp"
    android:layout_height="32dp"
    android:background="#F6F6F6"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <View
        android:id="@+id/mDivider"
        android:layout_width="0.5dp"
        android:layout_height="match_parent"
        android:background="#DEE0E3" />

    <TextView
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:textColor="#62656D"
        android:textSize="12dp"
        android:textStyle="bold"
        tools:text="Source Category 1"/>

</LinearLayout>