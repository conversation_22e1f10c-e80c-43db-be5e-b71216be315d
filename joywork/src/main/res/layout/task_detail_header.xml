<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="detail"
            type="com.jd.oa.joywork.detail.data.entity.TaskDetailEntity" />

        <variable
            name="presenter"
            type="com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter" />
    </data>

    <LinearLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingEnd="5dp"
        android:orientation="horizontal"
        tools:ignore="SpUsage">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/back_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/upload"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:paddingHorizontal="11dp"
            android:includeFontPadding="false"
            android:onClick="@{(theView) -> presenter.upload(theView)}"
            android:text="@string/icon_general_paperclip"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"
            app:canUpdateFiles="@{detail}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/action_share"
            app:layout_constraintTop_toTopOf="parent" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/urge_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="11dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:onClick="@{(theView) -> presenter.urge(theView,detail)}"
            android:text="@string/joywork_detail_urgy_icon"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/action_share"
            app:layout_constraintTop_toTopOf="parent"
            app:urgeVisible="@{detail}" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/focus_icon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="11dp"
            android:onClick="@{(theView) -> presenter.focus(theView,detail)}"
            android:text="@string/icon_padding_followhear"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"
            app:focusVisible="@{detail}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/action_share"
            app:layout_constraintTop_toTopOf="parent" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/action_share"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="11dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:onClick="@{(theView) -> presenter.share(theView,detail)}"
            android:text="@string/icon_general_share1"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/more_button"
            app:layout_constraintTop_toTopOf="parent" />


        <com.jd.oa.ui.IconFontView
            android:id="@+id/more_button"
            android:layout_width="wrap_content"
            android:paddingHorizontal="11dp"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_tabbar_more"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:moreVisible="@{detail}" />
    </LinearLayout>
</layout>