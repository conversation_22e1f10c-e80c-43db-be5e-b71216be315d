<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork2.menu.SimpleDrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mDrawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    tools:visibility="visible">

    <LinearLayout
        android:id="@+id/mMenu"
        android:layout_width="300dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginStart="22dp"
            android:layout_marginBottom="5dp"
            android:gravity="center_vertical"
            android:text="@string/joywork_task_tabbar_title"
            android:textColor="#202D40"
            android:textSize="18dp"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/mFixedMenuContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

        <View
            android:layout_marginTop="20dp"
            android:layout_marginHorizontal="8dp"
            android:layout_width="match_parent"
            android:background="#F0F1F2"
            android:layout_height="0.5dp" />

<!--        <TextView-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="24dp"-->
<!--            android:layout_marginStart="22dp"-->
<!--            android:layout_marginBottom="5dp"-->
<!--            android:gravity="center_vertical"-->
<!--            android:text="@string/joywork2_menu_shortcut"-->
<!--            android:textColor="#FF6A6A6A"-->
<!--            android:textSize="12dp" />-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constrainedHeight="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0"
                app:layout_constraintVertical_chainStyle="packed" />

<!--            <FrameLayout-->
<!--                android:id="@+id/mProjectItemContainer"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:paddingTop="20dp"-->
<!--                android:paddingBottom="13dp"-->
<!--                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@id/mRv">-->

<!--                <com.jd.oa.joywork2.menu.MenuItemView-->
<!--                    android:id="@+id/mProjectItem"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="40dp" />-->
<!--            </FrameLayout>-->
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</com.jd.oa.joywork2.menu.SimpleDrawerLayout>