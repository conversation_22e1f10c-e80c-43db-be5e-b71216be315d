<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mBack"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22"/>

        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/joywork_new"
            android:textColor="#FFFE3E33"
            android:textSize="18dp" />
    </LinearLayout>

    <com.jd.oa.joywork.view.JoyWorkWebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>