<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <RelativeLayout
        android:id="@+id/rl_toolbar_normal"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/iv_me_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="6dp"
            android:padding="10dp"
            android:src="@drawable/joywork_leftarrow" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_task_link_select_project"
            android:textColor="#242931"
            android:textSize="18dp"
            android:textStyle="bold" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@null"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/me_ok"
            android:textColor="@color/joywork_red"
            android:visibility="gone" />

    </RelativeLayout>

    <!--    <FrameLayout-->
    <!--        android:id="@+id/fl_container"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content" />-->


    <com.jd.oa.ui.recycler.RefreshRecyclerLayout
        android:id="@+id/refreshView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/rl_toolbar_normal"
        app:me_load_manual="false"
        app:me_refresh_enable="false"
        app:me_refresh_scheme_color="?attr/me_theme_major_color">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycleView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:visiblity="gone" />

    </com.jd.oa.ui.recycler.RefreshRecyclerLayout>

    <LinearLayout
        android:id="@+id/ll_list_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:src="@drawable/joywork_project_list_empty" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@string/no_project"
            android:textColor="@color/color_8f959e"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/tv_create_project"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/jdme_btn_round_gray_normal_r2dp"
            android:gravity="center"
            android:paddingStart="15dp"
            android:paddingTop="5dp"
            android:paddingEnd="15dp"
            android:paddingBottom="5dp"
            android:text="@string/joywork_new_list"
            android:textColor="@color/color_232930"
            android:textSize="14dp"
            android:visibility="gone" />
    </LinearLayout>

</RelativeLayout>