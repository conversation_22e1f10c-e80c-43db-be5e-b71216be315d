<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mBack"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22"/>

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_select_title"
            android:textColor="#FF242931"
            android:textSize="18dp" />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.jd.oa.joywork.team.view.DividerLinearLayout
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:gravity="center_vertical"
        app:dividerStartId="@id/mCount">

        <TextView
            android:id="@+id/mCount"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingStart="16dp"
            android:text="@string/joywork_select_group_count"
            android:textColor="#333333"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mArrow"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingStart="8dp"
            android:text="@string/icon_direction_down"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_16"/>

        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/mSure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:paddingHorizontal="10dp"
            android:paddingVertical="5dp"
            android:text="@string/joywork_next"
            android:textColor="#FFFFFF"
            android:textSize="10dp" />
    </com.jd.oa.joywork.team.view.DividerLinearLayout>
</LinearLayout>