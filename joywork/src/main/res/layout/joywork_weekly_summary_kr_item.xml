<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="36dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">

    <ImageView
        android:id="@+id/cb"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginStart="26dp"
        android:src="@drawable/joywork_cb" />

    <ImageView
        android:id="@+id/mStatus"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_marginStart="12dp"
        android:scaleType="fitXY"
        android:src="@drawable/joywork_status_blue" />

    <TextView
        android:id="@+id/mLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:background="@drawable/joywork_all_round_100_1a4c7cff"
        android:includeFontPadding="false"
        android:paddingHorizontal="6dp"
        android:paddingVertical="1.5dp"
        android:textColor="#4C7CFF"
        android:textSize="10dp" />

    <TextView
        android:id="@+id/mTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text=""
        android:textColor="#333333"
        android:textSize="16dp" />
</LinearLayout>