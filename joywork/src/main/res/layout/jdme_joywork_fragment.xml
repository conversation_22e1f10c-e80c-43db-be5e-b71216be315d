<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/ll_title_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <HorizontalScrollView
        android:scrollbars="none"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/self_tabs_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp" />
    </HorizontalScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#EDEDEE" />

    <com.jd.oa.joywork.view.UntouchableViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />
</LinearLayout>

