<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_gravity="center_vertical"
    android:clipChildren="false"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/mStatusContainer"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center">

            <TextView
                android:id="@+id/mStatusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/joywork_screen_unfinish"
                android:textColor="#FE3E33"
                android:textSize="12dp" />

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:text="@string/icon_direction_down"
                android:textColor="#FE3E33"
                android:textSize="@dimen/JMEIcon_14"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/mFilterContainer"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="24dp"
            android:gravity="center">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mFilterIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/icon_general_screening"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_14"/>

            <TextView
                android:id="@+id/mFilterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="@string/joywork_project_setting_filter"
                android:textColor="#232930"
                android:textSize="12dp" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mFilterIcon2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:text="@string/icon_direction_down"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_14"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/mSortContainer"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="24dp"
            android:gravity="center">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mSortIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/icon_workplacesortascending"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_14"/>

            <TextView
                android:id="@+id/mSortText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:text="@string/joywork_project_setting_sort"
                android:textColor="#232930"
                android:textSize="12dp" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mSortIcon2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:text="@string/icon_direction_down"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_14"/>
        </LinearLayout>

        <View
            android:id="@+id/mDivider"
            android:layout_width="0.5dp"
            android:layout_height="12dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="#FFE6E6E6" />

        <LinearLayout
            android:id="@+id/mSaveView"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/stroke_all_4_e6e6e6"
            android:orientation="horizontal"
            android:paddingHorizontal="18dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:text="@string/joywork_save_setting"
                android:textColor="#333333"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/mSave"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/stroke_all_4_e6e6e6"
            android:orientation="horizontal"
            android:paddingHorizontal="18dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:text="@string/joywork_saved"
                android:textColor="#333333"
                android:textSize="12dp" />

        </LinearLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mClear"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_close"
            android:textColor="#FF999999"
            android:textSize="@dimen/JMEIcon_12"
            android:visibility="gone" />
    </LinearLayout>
</HorizontalScrollView>