<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mContentView"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    app:dividerStartId="@id/mContentView">

    <TextView
        android:id="@+id/mTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text=""
        android:textColor="#333333"
        android:textSize="16dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/mCheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_check"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_18"/>

</com.jd.oa.joywork.team.view.DividerLinearLayout>