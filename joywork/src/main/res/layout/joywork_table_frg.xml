<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/list_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F4F5F6"
    android:orientation="vertical">
    <!--    分组标题-->
    <com.jd.oa.joywork.team.view.ItemContainer
        android:id="@+id/title_container"
        android:layout_width="wrap_content"
        android:layout_height="32dp">

        <LinearLayout
            android:id="@+id/title_container_child"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:divider="@drawable/joywork2_vertical_divider"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:showDividers="middle" />
    </com.jd.oa.joywork.team.view.ItemContainer>

    <FrameLayout
        android:id="@+id/content_container"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.jd.oa.joywork.team.view.HRecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </FrameLayout>
</LinearLayout>