<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal" />

    <FrameLayout
        android:id="@+id/mFilterContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mSelCb"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:text="@string/icon_padding_checkcircle"
            android:textColor="#FE3B30"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:id="@+id/mSelAll"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="8dp"
            android:text="@string/me_check_all"
            android:textColor="#333333"
            android:textSize="14dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/mSave"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingVertical="5dp"
            android:layout_centerInParent="true"
            android:layout_gravity="center_vertical|end"
            android:background="@drawable/solid_all_100_fe3e33"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="10dp"
            android:text="@string/joywork_confirm"
            android:textColor="#FFFFFF"
            android:textSize="12dp" />
    </LinearLayout>
</LinearLayout>