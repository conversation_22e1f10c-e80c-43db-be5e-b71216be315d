<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:textColor="#333333"
        android:textSize="16dp" />

    <TextView
        android:visibility="gone"
        android:id="@+id/mNewLabel"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_centerInParent="true"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="4dp"
        android:background="@drawable/solid_all_100_fe3e33"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="4dp"
        android:text="@string/joywork_new2"
        android:textColor="#ffffff"
        android:textSize="12dp" />

    <Space
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:text="@string/icon_prompt_check"
        android:textColor="#FE3B30"
        android:textSize="@dimen/JMEIcon_18"/>

</com.jd.oa.joywork.team.view.DividerLinearLayout>