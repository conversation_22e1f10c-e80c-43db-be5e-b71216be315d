<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.ItemContainer xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:contentViewId="@id/normal"
    app:rightViewId="@id/swipe_right">

    <com.jd.oa.joywork.team.view.DividerLinearLayout
        android:id="@+id/item_container"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:dividerStartId="@id/titleContainer"
        app:dividerTop="true">

        <View
            android:layout_width="26dp"
            android:layout_height="1dp" />

        <LinearLayout
            android:id="@+id/ll_title_container"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <FrameLayout
                android:id="@+id/cb_task_container"
                android:layout_width="wrap_content"
                android:layout_height="44dp">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/cb_task"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/icon_prompt_circle"
                    android:textSize="@dimen/JMEIcon_18"/>

                <ImageView
                    android:id="@+id/cb_task2"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center"
                    android:src="@drawable/joywork_cb"
                    android:visibility="visible" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/titleContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="12dp"
                android:layout_marginTop="0.5dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/risk"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:includeFontPadding="false"
                    android:paddingHorizontal="4dp"
                    android:text="@string/joywork_risk_problem"
                    android:textSize="10dp" />

                <!-- 标题-->
                <TextView
                    android:id="@+id/title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:text="@string/joywork_tomorrow"
                    android:textColor="#232930"
                    android:textSize="14dp" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/joywork_divider" />

        <!--        负责人-->
        <LinearLayout
            android:id="@+id/owner_container"
            android:layout_width="132dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.jd.oa.joywork.view.JoyWorkAvatarView
                android:id="@+id/mAvatarView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                app:avatarSize="20dp"
                app:hatPaddingExtra="4dp"
                app:hintTextThreshold="1"
                app:maxChildCount="3" />
        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/joywork_divider" />

        <!--截止时间-->
        <FrameLayout
            android:id="@+id/deadline_container"
            android:layout_width="132dp"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:paddingStart="12dp">

            <LinearLayout
                android:id="@+id/deadline_dup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/deadline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text=""
                    android:textColor="@color/joywork_red"
                    android:textSize="12dp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/dupIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/icon_general_repeat"
                    android:textColor="#8F959E"
                    android:textSize="@dimen/JMEIcon_14"/>
            </LinearLayout>

            <View
                android:id="@+id/mark_value_vh"
                android:layout_width="8dp"
                android:layout_height="1dp"
                android:layout_gravity="center_vertical"
                android:background="#8F959E" />
        </FrameLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/joywork_divider" />

        <!--优先级-->
        <FrameLayout
            android:id="@+id/priority_container"
            android:layout_width="132dp"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingHorizontal="12dp">

            <TextView
                android:id="@+id/priority"
                android:layout_width="96dp"
                android:layout_height="18dp"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text=""
                android:textColor="@color/joywork_red"
                android:textSize="12dp" />

            <View
                android:id="@+id/priority_value_vh"
                android:layout_width="8dp"
                android:layout_height="1dp"
                android:layout_gravity="center_vertical"
                android:background="#8F959E" />
        </FrameLayout>

        <LinearLayout
            android:id="@+id/ex_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal" />
    </com.jd.oa.joywork.team.view.DividerLinearLayout>

    <View
        android:id="@+id/cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#99FFFFFF"
        android:visibility="gone" />
</com.jd.oa.joywork.team.view.ItemContainer>