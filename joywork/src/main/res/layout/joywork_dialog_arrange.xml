<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/joywork_shortcut_create_dialog_bg"
    android:orientation="vertical"
    android:paddingBottom="3dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:text="@string/joywork_plan_alert_title"
        android:textColor="@color/color_232930"
        android:textSize="@dimen/sp_16" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/color_dee0e3" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>
