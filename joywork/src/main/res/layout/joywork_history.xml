<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:scrollbars="none">

    <RelativeLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="SpUsage">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/back_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_detail_history"
            android:textColor="#FF333333"
            android:textSize="18dp"
            android:textStyle="bold" />
    </RelativeLayout>

    <com.jd.me.web2.webview.JMEWebview
        android:id="@+id/comment"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>