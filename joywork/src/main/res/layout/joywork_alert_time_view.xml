<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <!--        如果 Hint 的字数长于文本，显示时有问题-->
        <TextView
            android:id="@+id/alert_content_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="@string/joy_work_remind"
            android:textColor="@color/title_sub"
            android:textSize="@dimen/joywork_title_sub" />

        <TextView
            android:id="@+id/alert_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#FF232930"
            android:textSize="@dimen/joywork_title_sub" />
    </FrameLayout>


    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingVertical="12dp"
        android:paddingStart="14dp"
        android:text="@string/icon_prompt_close"
        android:textColor="#FF8F959E"
        android:textSize="@dimen/JMEIcon_12"
        android:visibility="gone" />
</LinearLayout>