<?xml version="1.0" encoding="utf-8"?><!--    加载更多-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/loadMore"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:gravity="center_horizontal"
    android:paddingTop="14dp"
    android:orientation="horizontal">

    <ProgressBar
        android:id="@+id/pb"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:indeterminateTint="@color/joywork_red"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"/>

    <TextView
        android:id="@+id/loadMoreText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:gravity="top|center_horizontal"
        android:text="@string/me_load_more_data"
        android:textColor="@color/joywork_red"
        android:textSize="14dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/loadMoreIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/icon_direction_down"
        android:textColor="@color/joywork_red"
        android:textSize="@dimen/JMEIcon_14"/>
</LinearLayout>