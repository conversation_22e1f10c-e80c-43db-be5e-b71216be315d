<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:orientation="horizontal"
    app:cardUseCompatPadding="true"
    tools:ignore="SpUsage">

    <ImageView
        android:id="@+id/res_type"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/res_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:text="@{resources.name}"
        android:textColor="#232930"
        android:textSize="16dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/res_del_icon"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_prompt_close"
        android:textColor="@color/title_sub"
        android:textSize="@dimen/JMEIcon_12"/>
</LinearLayout>