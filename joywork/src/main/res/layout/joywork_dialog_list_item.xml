<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="48dp"
    android:paddingStart="16dp"
    android:paddingTop="14dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#232930"
        android:textSize="@dimen/JMEIcon_18"
        app:layout_constraintBottom_toBottomOf="@id/content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/content" />

    <TextView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="#232930"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@id/detail"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/icon"
        app:layout_constraintTop_toTopOf="parent" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="8dp"
        android:paddingStart="12dp"
        android:paddingEnd="16dp"
        android:textColor="#62656D"
        android:textSize="@dimen/JMEIcon_16"
        app:layout_constraintBottom_toBottomOf="@id/content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/content" />

    <View
        android:id="@+id/divier"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="14dp"
        android:background="@color/joywork_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="@id/content" />
</androidx.constraintlayout.widget.ConstraintLayout>
