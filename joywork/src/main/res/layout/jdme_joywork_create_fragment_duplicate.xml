<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="1dp"
        android:layout_height="16dp" />

    <LinearLayout
        android:id="@+id/dup_container"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/icon_general_repeat"
            android:textColor="@color/icon_gray"
            android:textSize="@dimen/JMEIcon_18" />

        <TextView
            android:id="@+id/dupText"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/joywork_item_bg"
            android:gravity="center_vertical"
            android:hint="@string/joywork_duplicate_text"
            android:textColor="#333333"
            android:textColorHint="@color/title_sub"
            android:textSize="14dp" />

    </LinearLayout>
</LinearLayout>