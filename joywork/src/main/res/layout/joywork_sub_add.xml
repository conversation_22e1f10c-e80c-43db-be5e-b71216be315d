<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/add_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="12dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/cb_task_place"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/joywork_cb_unavailable"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_circle"
        android:textSize="@dimen/JMEIcon_18"
        android:visibility="invisible" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/cb_task"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_add"
        android:textColor="#FF8F959E"
        android:textSize="@dimen/JMEIcon_18"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text="@string/joy_work_child_works"
        android:textColor="#FF8F959E"
        android:textSize="16dp" />
</LinearLayout>