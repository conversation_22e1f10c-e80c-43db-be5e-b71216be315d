<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:background="@drawable/jdme_selector_set_no_divide_item">

    <ImageView
        android:id="@+id/jdme_id_myapply_dropdown_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="16dp"
        android:layout_marginEnd="@dimen/setting_item_padding_horizontal"
        android:layout_marginRight="16dp"
        android:src="@drawable/joywork_icon_setting_checked" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="8dp"
        android:textSize="@dimen/JMEIcon_16"
        android:layout_toRightOf="@id/jdme_id_myapply_dropdown_icon" />

    <TextView
        android:id="@+id/jdme_id_myapply_dropdown_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp"
        android:layout_toRightOf="@id/tv_icon"
        android:lines="1"
        android:textColor="@color/me_setting_foreground"
        android:textSize="@dimen/sp_16"
        tools:text="中文" />

    <ImageView
        android:id="@+id/iv_arrow_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="16dp"
        android:src="@drawable/jdme_icon_bold_right_arrow"
        android:visibility="invisible"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentBottom="true"
        android:background="#F0F3F3" />
</RelativeLayout>