<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tab_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/joywork_tabbar_title_my"
        android:textColor="#666666"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:src="@drawable/joywork_tab_indicator"
        app:layout_constraintEnd_toEndOf="@id/tab_title"
        app:layout_constraintStart_toStartOf="@id/tab_title"
        app:layout_constraintTop_toBottomOf="@id/tab_title" />

    <View
        android:id="@+id/paddingView"
        android:layout_width="32dp"
        android:layout_height="1dp"
        android:background="#00000000"
        android:visibility="visible"
        app:layout_constraintStart_toEndOf="@id/tab_title"
        app:layout_constraintTop_toTopOf="parent" />

    <com.jd.oa.joywork.view.BubbleView
        android:id="@+id/bubbleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="2dp"
        app:extraPaddingPercent="0.3"
        app:layout_constraintStart_toEndOf="@id/tab_title"
        app:layout_constraintBottom_toBottomOf="@id/tab_title"
        android:layout_marginBottom="8dp"
        app:numViewBgColor="#FE564C"
        app:numViewBorderColor="@color/white"
        app:numViewBorderWidth="0.5dp">

        <TextView
            android:id="@+id/countView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="99+"
            android:textColor="@color/white"
            android:textSize="7dp" />
    </com.jd.oa.joywork.view.BubbleView>
</androidx.constraintlayout.widget.ConstraintLayout>