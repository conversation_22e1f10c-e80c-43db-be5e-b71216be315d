<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/joywork_shortcut_create_dialog_bg"
    android:orientation="vertical">

    <androidx.legacy.widget.Space
        android:layout_width="match_parent"
        android:layout_height="18dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:background="@color/transparent"
            android:hint="@string/joywork_shortcut_title_hint"
            android:inputType="textMultiLine"
            android:maxLength="500"
            android:layout_weight="1"
            android:maxLines="6"
            android:textColor="#232930"
            android:textColorHint="#8F959E"
            android:textSize="16dp"
            android:theme="@style/JoyWorkEditTextStyle"
            tools:text="" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/title"
            android:layout_marginTop="2dp"
            android:text="@string/icon_general_anobliquearrow"
            android:textColor="#6a6a6a"
            android:textSize="@dimen/JMEIcon_20" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="24dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <HorizontalScrollView
            android:id="@+id/mHorScrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="-20dp"
            android:layout_weight="1"
            android:paddingStart="12dp"
            android:paddingEnd="20dp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 执行人-->
                <LinearLayout
                    android:id="@+id/owner_container"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:layout_marginEnd="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="6dp"
                    android:paddingEnd="6dp">

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/owner_def_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/icon_edit_addcontact"
                        android:textColor="#6a6a6a"
                        android:textSize="@dimen/JMEIcon_20" />

                    <com.jd.oa.joywork.view.JoyWorkAvatarView
                        android:id="@+id/mAvatarView"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        app:alwaysShowHintText="true"
                        app:avatarSize="20dp"
                        app:hatPaddingExtra="4dp"
                        app:maxAvatar="2"
                        app:maxChildCount="3"
                        app:showBorder="false"
                        app:showMore="false" />

                </LinearLayout>
                <!-- 时间-->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/time_container_indicator"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="6dp"
                        android:paddingEnd="6dp">

                        <com.jd.oa.ui.IconFontView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:text="@string/icon_padding_othertime"
                            android:textColor="#6a6a6a"
                            android:textSize="@dimen/JMEIcon_20" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/mTodayContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="12dp"
                        android:visibility="gone">

                        <com.jd.oa.ui.IconFontView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/icon_edit_historyright"
                            android:textColor="#29CC31"
                            android:textSize="@dimen/JMEIcon_20" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="@string/joywork_plan_alert_today"
                            android:textColor="#333333"
                            android:textSize="14dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/mTomorrowContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginHorizontal="8dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="12dp"
                        android:visibility="gone">

                        <com.jd.oa.ui.IconFontView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/icon_direction_rightcircle"
                            android:textColor="#547ef7"
                            android:textSize="@dimen/JMEIcon_20" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:text="@string/joywork_tomorrow"
                            android:textColor="#333333"
                            android:textSize="14dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/time_content"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="6dp"
                        android:visibility="gone">

                        <com.jd.oa.ui.IconFontView
                            android:id="@+id/time_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:text="@string/icon_padding_othertime"
                            android:textColor="#1b1b1b"
                            android:textSize="@dimen/JMEIcon_20" />

                        <TextView
                            android:id="@+id/time_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="8dp"
                            android:includeFontPadding="false"
                            android:paddingEnd="8dp"
                            android:text="@string/joywork_shortcut_deadline_tips"
                            android:textColor="#1b1b1b"
                            android:textSize="14dp" />

                        <com.jd.oa.ui.IconFontView
                            android:id="@+id/clear_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingEnd="8dp"
                            android:text="@string/icon_prompt_close"
                            android:textColor="#6a6a6a"
                            android:textSize="@dimen/JMEIcon_14"
                            android:visibility="gone" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/mAlertContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/solid_all_4_f5f5f5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="8dp"
                        android:visibility="gone">

                        <com.jd.oa.ui.IconFontView
                            android:id="@+id/mAlertIcon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:text="@string/icon_general_bell"
                            android:textColor="#1b1b1b"
                            android:textSize="@dimen/JMEIcon_16" />

                        <TextView
                            android:id="@+id/mAlertText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:includeFontPadding="false"
                            android:text="@string/joywork_alert_no"
                            android:textColor="#1b1b1b"
                            android:textSize="14dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/dup_container"
                        android:layout_marginStart="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:background="@drawable/solid_all_4_f5f5f5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="8dp"
                        android:visibility="gone">

                        <com.jd.oa.ui.IconFontView
                            android:id="@+id/iconDuplicate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/icon_general_repeat"
                            android:textColor="#1b1b1b"
                            android:textSize="@dimen/JMEIcon_16" />

                        <TextView
                            android:id="@+id/textDuplicate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:text="@string/joywork_duplicate_text"
                            android:textColor="#1b1b1b"
                            android:textSize="14dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 团队待办分组-->
                <LinearLayout
                    android:id="@+id/assign_container"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:gravity="center_vertical"
                    android:minHeight="28dp">

                    <LinearLayout
                        android:id="@+id/assign_indicator"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:gravity="center_vertical">

                        <com.jd.oa.ui.IconFontView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:text="@string/icon_edit_directory"
                            android:textColor="#6a6a6a"
                            android:textSize="@dimen/JMEIcon_20" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/assign_content"
                        android:layout_width="wrap_content"
                        android:layout_height="32dp"
                        android:visibility="gone"
                        android:gravity="center_vertical">

                        <com.jd.oa.ui.IconFontView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:includeFontPadding="false"
                            android:text="@string/icon_edit_directory"
                            android:textColor="#1b1b1b"
                            android:textSize="@dimen/JMEIcon_20" />

                        <TextView
                            android:id="@+id/assign_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:paddingStart="8dp"
                            android:text="123"
                            android:textColor="#1b1b1b"
                            android:textColorHint="#232930"
                            android:textSize="14dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </HorizontalScrollView>

        <FrameLayout
            android:id="@+id/create_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:paddingVertical="2dp"
            android:paddingStart="20dp"
            android:paddingEnd="16dp">

            <TextView
                android:id="@+id/create"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:paddingHorizontal="7dp"
                android:paddingVertical="5dp"
                android:text="@string/joywork_new"
                android:textSize="13dp" />
        </FrameLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/mSendDD"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFF9F9F9"
        android:visibility="gone"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/mSendIcon"
            android:layout_width="36dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:paddingStart="12dp"
            android:paddingEnd="6dp"
            android:paddingVertical="16dp"
            android:src="@drawable/jdme_selector_checkbox" />

        <TextView
            android:id="@+id/mSendText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingVertical="16dp"
            android:text="@string/joywork_send_dd"
            android:textColor="#333333"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/batch_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F9F9F9"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="12dp"
        android:paddingEnd="16dp"
        android:visibility="gone">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iconBatchSubJoywork"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="@string/icon_prompt_add"
            android:textColor="#4C7CFF"
            android:textSize="@dimen/JMEIcon_14" />

        <TextView
            android:id="@+id/textBatchSubJoywork"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/joywork_create_subjoywork"
            android:textColor="#4C7CFF"
            android:textSize="14dp" />

    </LinearLayout>
</LinearLayout>