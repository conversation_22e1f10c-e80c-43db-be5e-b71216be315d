<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="start"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:background="#ffffff"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/joywork_divider" />
</LinearLayout>