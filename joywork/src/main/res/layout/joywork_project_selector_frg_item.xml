<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="16dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/mCheck"
        android:textSize="@dimen/JMEIcon_16"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/icon_prompt_circlecheck" />

    <com.jd.oa.ui.IconFontView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:text="@string/icon_project"
        android:textColor="#1B1B1B"
        android:textSize="@dimen/JMEIcon_16" />

    <TextView
        android:id="@+id/mName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#1B1B1B"
        android:textSize="16dp"
        tools:text="清单名" />
</LinearLayout>