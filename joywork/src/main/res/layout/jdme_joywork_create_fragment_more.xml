<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ownerRowContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">
    <com.jd.oa.ui.IconFontView
        android:id="@+id/owner_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/icon_tabbar_more"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_18" />

    <TextView
        android:id="@+id/owner_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:text="@string/joywork_detail_more"
        android:textColor="#4C7CFF"
        android:textSize="16dp" />
</LinearLayout>