<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F8F9"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingStart="16dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mBackView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/icon_prompt_close"
            android:textColor="#232930"
            android:textSize="@dimen/JMEIcon_20"/>

        <TextView
            android:id="@+id/mTitleView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_executor"
            android:textColor="#232930"
            android:textSize="18dp"
            android:textStyle="bold" />

    </RelativeLayout>

    <com.jd.oa.joywork.team.view.DividerLinearLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:dividerStartId="@id/mTitleContainer">

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="#232930"
            android:textSize="14dp" />
    </com.jd.oa.joywork.team.view.DividerLinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#ffffff" />

    <com.jd.oa.joywork.team.view.DividerLinearLayout
        android:id="@+id/d2"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:background="#ffffff"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp"
        app:dividerBottom="false"
        app:dividerStartId="@id/d2"
        app:dividerTop="true">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mSelCb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/icon_padding_checkcircle"
            android:textColor="#FE3B30"
            android:textSize="@dimen/JMEIcon_16"/>

        <TextView
            android:id="@+id/mSelAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:text="@string/me_check_all"
            android:textColor="#232930"
            android:textSize="14dp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/mSure"
            android:layout_width="60dp"
            android:layout_height="28dp"
            android:layout_gravity="center_vertical"
            android:layout_marginVertical="12dp"
            android:gravity="center"
            android:text="@string/joywork_plan_alert_done"
            android:textColor="#FFFFFF"
            android:textSize="14dp" />
    </com.jd.oa.joywork.team.view.DividerLinearLayout>
</LinearLayout>