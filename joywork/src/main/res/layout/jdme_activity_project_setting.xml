<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f8f9fa"
    android:orientation="vertical">

    <include layout="@layout/jdme_layout_project_create_action_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="8dp">

        <LinearLayout
            android:id="@+id/ll_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/et_project_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="top"
                android:hint="@string/joywork_project_name_hint"
                android:inputType="textMultiLine"
                android:lineSpacingExtra="3dp"
                android:maxLength="200"
                android:paddingLeft="16dp"
                android:paddingTop="8dp"
                android:paddingRight="16dp"
                android:paddingBottom="8dp"
                android:textColor="#333333"
                android:textColorHint="#666666"
                android:textSize="16dp"
                tools:text="" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="12dp">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_general_filetext"
                    android:textColor="#333333"
                    android:textSize="@dimen/JMEIcon_16"/>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="6dp"
                    android:text="@string/joywork_project_des_tip"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/mMore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/joywork_more_example"
                    android:textColor="#4C7CFF"
                    android:textSize="14dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/mDescV"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="top"
                android:hint="@string/joywork_project_des_hint"
                android:lineSpacingExtra="3dp"
                android:maxLength="1000"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:paddingBottom="16dp"
                android:textColor="#666666"
                android:textColorHint="#666666"
                android:textSize="14dp"
                tools:text="" />

            <RelativeLayout
                android:id="@+id/ll_members"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="11dp"
                android:background="@color/white"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/icon1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:textSize="@dimen/JMEIcon_16"
                    android:layout_marginLeft="16dp"
                    android:text="@string/icon_general_userteam" />

                <TextView
                    android:id="@+id/tv_relation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="12dp"
                    android:layout_toEndOf="@id/icon1"
                    android:text="@string/joywork_shared_member"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_relation"
                    android:layout_width="match_parent"
                    android:layout_height="24dp"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="65dp"
                    android:layout_toEndOf="@id/tv_relation" />

                <TextView
                    android:id="@+id/tv_relation_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="14dp"
                    android:layout_toStartOf="@id/icon2"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    tools:text="" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/icon2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9dp"
                    android:layout_marginRight="16dp"
                    android:text="@string/icon_direction_right"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_14"/>
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/transparent" />

            <TextView
                android:id="@+id/tv_transfer_creator"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@color/white"
                android:gravity="center"
                android:text="@string/jdme_joywork_transfer_creator"
                android:textColor="#FE3B30"
                android:textSize="16dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_exit_team"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="1dp"
                android:background="@color/white"
                android:gravity="center"
                android:text="@string/joywork_team_exit"
                android:textColor="#FE3B30"
                android:textSize="16dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_delete"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="1dp"
                android:layout_marginBottom="20dp"
                android:background="@color/white"
                android:gravity="center"
                android:text="@string/joywork_del"
                android:textColor="#FE3B30"
                android:textSize="16dp"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>