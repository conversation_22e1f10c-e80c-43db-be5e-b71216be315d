<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.ItemContainer xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:contentViewId="@id/normal"
    app:rightViewId="@id/swipe_right">

    <com.jd.oa.joywork.team.view.DividerLinearLayout
        android:id="@+id/item_container"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:divider="@drawable/joywork2_vertical_divider"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:showDividers="none"
        app:dividerStartId="@id/cb_task_container"
        app:dividerTop="false">

    </com.jd.oa.joywork.team.view.DividerLinearLayout>

    <View
        android:id="@+id/cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#99FFFFFF"
        android:visibility="gone" />
</com.jd.oa.joywork.team.view.ItemContainer>