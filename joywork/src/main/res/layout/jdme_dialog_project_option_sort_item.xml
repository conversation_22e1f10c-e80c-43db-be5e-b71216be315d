<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:text="@string/joywork_project_setting_sort_in_group"
        android:textColor="#232930"
        android:textSize="16dp" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch_in_group"
        style="@style/SwitchStyle"
        android:layout_width="50dp"
        android:layout_height="26dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="16dp" />
</com.jd.oa.joywork.team.view.DividerLinearLayout>