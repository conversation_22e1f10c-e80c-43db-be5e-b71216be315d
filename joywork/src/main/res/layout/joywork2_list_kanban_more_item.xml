<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="12dp">

    <View
        android:id="@+id/top_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp" />

    <LinearLayout
        android:id="@+id/more_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/joywork_bottom_round_8"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_new_task"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingHorizontal="38dp"
            android:paddingVertical="12dp"
            android:text="@string/joywork2_show_new_task"
            android:textColor="#6A6A6A"
            android:textSize="16dp" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/load_more_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ProgressBar
                android:id="@+id/pb"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:indeterminateTint="#6A6A6A"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_load_more"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_load_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/joywork_load_more"
                android:textColor="#6A6A6A"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/pb"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</LinearLayout>



