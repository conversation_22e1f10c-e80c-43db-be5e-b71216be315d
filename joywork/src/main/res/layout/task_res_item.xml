<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="resources"
            type="com.jd.oa.joywork.detail.data.entity.Resources" />

        <variable
            name="resCallback"
            type="com.jd.oa.joywork.detail.ui.ResourceClickCallback" />

        <variable
            name="detail"
            type="com.jd.oa.joywork.detail.data.entity.TaskDetailEntity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:onClick="@{() ->  resCallback.onClick(resources)}"
        app:cardUseCompatPadding="true"
        tools:ignore="SpUsage">

        <ImageView
            android:id="@+id/res_type"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            app:resIcon="@{resources}"
            tools:ignore="ContentDescription" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/res_del_icon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:onClick="@{() ->  resCallback.onDelClick(resources)}"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_12"
            app:canUpdateFiles="@{detail}" />

        <TextView
            android:id="@+id/res_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="12dp"
            android:layout_toStartOf="@id/res_del_icon"
            android:layout_toEndOf="@id/res_type"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:text="@{resources.name}"
            android:textColor="@color/icon_black"
            android:textSize="16dp" />
    </RelativeLayout>
</layout>