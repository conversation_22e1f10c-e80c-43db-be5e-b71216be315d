<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="26dp"
    android:layout_marginTop="20dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/mTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:textColor="#FF6A6A6A"
        android:textSize="12dp"
        tools:text="@string/joywork2_menu_shortcut" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/mIcon"
        android:layout_marginEnd="17dp"
        android:layout_width="26dp"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textColor="#FF6A6A6A"
        android:textSize="14dp"
        tools:text="@string/icon_prompt_add" />
</LinearLayout>