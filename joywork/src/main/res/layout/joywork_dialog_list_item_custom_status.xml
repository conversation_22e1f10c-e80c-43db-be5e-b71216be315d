<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:layout_marginTop="50dp">

    <View
        android:layout_width="wrap_content"
        android:layout_height="1px"
        android:background="#E6E6E6" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:background="@drawable/jdme_bg_tv_corner_gray"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:minWidth="59dp"
            android:shadowRadius="3.0"
            android:textColor="#FF333333"
            android:textSize="12dp"
            tools:backgroundTint="#FFF2CA"
            tools:text="未开始" />

        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="2dp"
            android:text="@string/icon_prompt_check"
            android:textColor="#FE3E33"
            android:textSize="@dimen/JMEIcon_18"
            android:visibility="gone"
            tools:ignore="SpUsage"
            tools:visibility="visible" />
    </LinearLayout>

</LinearLayout>