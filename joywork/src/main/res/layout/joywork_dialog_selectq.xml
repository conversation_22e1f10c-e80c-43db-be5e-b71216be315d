<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@drawable/joywork_top_round_10"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/cancel"
            android:textColor="#666666"
            android:textSize="16dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/ok"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/confirm"
            android:textColor="#FE3E33"
            android:textSize="16dp" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/dialog_content"
        android:layout_width="match_parent"
        android:layout_height="233dp"
        android:background="#ffffff"
        android:paddingHorizontal="16dp">

        <View
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/joywork_all_round_6_f5f5f5" />


        <com.jd.oa.joywork.team.kpi.KpiQWheelView
            android:id="@+id/mWheelView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#00000000" />
    </FrameLayout>
</LinearLayout>