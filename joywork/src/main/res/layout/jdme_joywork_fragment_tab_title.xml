<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.jd.oa.theme.view.JoyWorkThemeView
        android:id="@+id/mJoyWorkThemeBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/joywork_list_tabbar_title_height"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/mTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:includeFontPadding="false"
                android:text="@string/joywork_task_tabbar_title"
                android:textColor="#333333"
                android:textSize="22dp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="5dp"
                android:orientation="horizontal">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/search"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingHorizontal="11dp"
                    android:text="@string/icon_general_search"
                    android:textColor="#333333"
                    android:textSize="@dimen/JMEIcon_22"/>

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/more_button"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingHorizontal="11dp"
                    android:text="@string/icon_prompt_questioncircle"
                    android:textColor="#333333"
                    android:textSize="@dimen/JMEIcon_22"/>
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</FrameLayout>