<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="member"
            type="com.jd.oa.model.service.im.dd.entity.Members" />

        <variable
            name="presenter"
            type="com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter" />

    </data>

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/member_avatar"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center_vertical"
        app:error="@{@drawable/joywork_default_avatar}"
        app:imageUrl="@{member.headImg}"
        app:me_border_color="#ffffff"
        app:me_border_width="@dimen/joywork_avatar_border"
        tools:ignore="ContentDescription" />
</layout>
