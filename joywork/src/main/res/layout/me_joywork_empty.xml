<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <Space
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/joywork_empty_img" />

    <TextView
        android:id="@+id/empty_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:gravity="center_horizontal"
        android:layout_marginHorizontal="20dp"
        android:text="@string/joywork_empty"
        android:textColor="#8F959E"
        android:textSize="14dp" />

    <Space
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_weight="1" />
</LinearLayout>