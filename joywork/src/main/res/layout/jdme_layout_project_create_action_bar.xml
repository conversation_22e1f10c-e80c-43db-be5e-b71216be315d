<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@color/white">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingHorizontal="@dimen/dp_10"
        android:scaleType="center"
        android:src="@drawable/jdme_btn_file_cancel" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:drawablePadding="5dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxEms="10"
        android:maxLines="1"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:text="@string/joywork_new_list"
        android:textColor="@color/color_2e2d2d"
        android:textSize="18dp"
        android:textStyle="bold" />


    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:enabled="false"
        android:gravity="center"
        android:paddingHorizontal="10dp"
        android:text="@string/joywork_new"
        android:textColor="@color/color_4bf0250f"
        android:textSize="14dp" />

</RelativeLayout>