<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/icon_general_screening"
        android:textColor="#232930"
        android:textSize="@dimen/JMEIcon_18"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:text=""
        android:textColor="@color/me_setting_foreground"
        android:textSize="16dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/icon_direction_right"
        android:textColor="#8F959E"
        android:textSize="@dimen/JMEIcon_14"/>
</com.jd.oa.joywork.team.view.DividerLinearLayout>