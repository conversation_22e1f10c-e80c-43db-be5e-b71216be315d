<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/condition_root"
    android:visibility="gone"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:visibility="visible">

    <RelativeLayout
        android:id="@+id/filter_condition"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:scrollbars="none"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/reset">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                tools:visibility="visible">

                <com.jd.oa.joywork2.view.AutoSelectLinearLayout
                    android:id="@+id/mStatusContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/mConditionStatusText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/joywork2_f63218_6a6a6a"
                        android:textSize="12dp"
                        tools:text="状态" />

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/mConditionStatusIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="@string/icon_direction_down"
                        android:textColor="@color/joywork2_f63218_6a6a6a"
                        android:textSize="@dimen/JMEIcon_14" />
                </com.jd.oa.joywork2.view.AutoSelectLinearLayout>

                <com.jd.oa.joywork2.view.AutoSelectLinearLayout
                    android:id="@+id/mSortContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="24dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/mConditionSorterText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/joywork2_f63218_6a6a6a"
                        android:textSize="12dp"
                        tools:text="排序" />

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/mConditionSorterIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="@string/icon_direction_down"
                        android:textColor="@color/joywork2_f63218_6a6a6a"
                        android:textSize="@dimen/JMEIcon_14" />
                </com.jd.oa.joywork2.view.AutoSelectLinearLayout>

                <com.jd.oa.joywork2.view.AutoSelectLinearLayout
                    android:id="@+id/mGrouperContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="24dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/mConditionGrouperText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/joywork2_f63218_6a6a6a"
                        android:textSize="12dp"
                        tools:text="分组" />

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/mConditionGrouperIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="@string/icon_direction_down"
                        android:textColor="@color/joywork2_f63218_6a6a6a"
                        android:textSize="@dimen/JMEIcon_14" />
                </com.jd.oa.joywork2.view.AutoSelectLinearLayout>
            </LinearLayout>
        </HorizontalScrollView>

        <LinearLayout
            android:id="@+id/reset"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="8dp"
            android:orientation="horizontal"
            android:paddingHorizontal="12dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:text="@string/joywork_plan_alert_reset"
                android:textColor="#1869F5"
                android:textSize="12sp" />
        </LinearLayout>

    </RelativeLayout>

    <HorizontalScrollView
        android:id="@+id/calendar_condition"
        android:layout_width="wrap_content"
        android:layout_height="46dp"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/container_linear"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            tools:visibility="visible" />
    </HorizontalScrollView>
</FrameLayout>
