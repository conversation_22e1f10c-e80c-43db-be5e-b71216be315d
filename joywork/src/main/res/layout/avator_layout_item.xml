<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mImageContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical">

        <ImageView
            android:id="@+id/hat"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:src="@drawable/joywork_yellow_hat"
            app:layout_constraintCircle="@id/avatar"
            app:layout_constraintCircleAngle="315"
            app:layout_constraintCircleRadius="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/avatar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/file_compression"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:me_border_color="#ffffff"
            app:me_border_width="@dimen/joywork_avatar_border" />

        <ImageView
            android:id="@+id/mIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="1dp"
            android:src="@drawable/joywork_list_item_icon"
            app:layout_constraintBottom_toBottomOf="@id/avatar"
            app:layout_constraintEnd_toEndOf="@id/avatar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/mImageAltTextContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="4dp">

        <TextView
            android:id="@+id/mImageAltText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:maxWidth="48dp"
            android:maxLines="1"
            android:text=""
            android:textColor="#333333"
            android:textSize="14dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/mImageAltText2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/joywork_owner2"
            android:textColor="#333333"
            android:textSize="14dp"
            android:visibility="visible" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/mDivider"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_gravity="center_vertical"
        android:paddingHorizontal="4dp"
        android:visibility="gone">

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="#666666" />
    </FrameLayout>
</LinearLayout>

