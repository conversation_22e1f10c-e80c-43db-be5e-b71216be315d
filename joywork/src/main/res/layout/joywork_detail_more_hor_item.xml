<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="96dp"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingHorizontal="24dp"
    android:paddingVertical="18dp"
    android:paddingTop="16dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_20"/>

    <View
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="测试"
        android:textColor="#8F959E"
        android:textSize="12dp" />
</LinearLayout>