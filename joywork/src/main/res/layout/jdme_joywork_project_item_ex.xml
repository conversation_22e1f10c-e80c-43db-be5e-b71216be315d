<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ex_root"
    android:layout_width="132dp"
    android:layout_height="44dp"
    android:gravity="center_vertical">

    <FrameLayout
        android:id="@+id/mark_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:paddingHorizontal="6dp"
        android:paddingVertical="2dp">

        <TextView
            android:id="@+id/mark_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text=""
            android:textColor="@color/joywork_black"
            android:textSize="12dp" />

        <View
            android:id="@+id/mark_value_vh"
            android:layout_width="8dp"
            android:layout_height="1dp"
            android:layout_gravity="center"
            android:background="#8F959E" />
    </FrameLayout>
</LinearLayout>
