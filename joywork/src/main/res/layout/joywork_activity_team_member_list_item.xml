<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:src="@drawable/joywork_empty_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/top_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/image"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:paddingTop="12dp"
            android:text=""
            android:textColor="#232930"
            android:textSize="16dp" />

        <LinearLayout
            android:id="@+id/action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:paddingTop="12dp">

            <TextView
                android:id="@+id/tv_situation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#62656D"
                android:textSize="@dimen/sp_14"
                tools:text="可编辑" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/iv_arrow_down"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/JMEIcon_10"
                android:layout_marginLeft="6dp"
                android:text="@string/icon_padding_caredown"
                android:textColor="#BFC1C4" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/department"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text=""
        android:textColor="#FF8F959E"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/top_container"
        app:layout_constraintTop_toBottomOf="@id/top_container" />

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        android:background="@color/joywork_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/top_container"
        app:layout_constraintTop_toBottomOf="@id/department" />

</androidx.constraintlayout.widget.ConstraintLayout>