<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork2.list.calendar.view.MyConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/ym_area"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_ym"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2024 9月"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="16dp" />


        <com.jd.oa.ui.IconFontView
            android:id="@+id/arrow_month"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="6dp"
            android:includeFontPadding="false"
            android:text="@string/icon_direction_up"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_16" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/week_area"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/ym_area">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/last_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:paddingEnd="8dp"
            android:text="@string/icon_direction_left"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_16" />

        <TextView
            android:id="@+id/tv_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="#333333"
            android:textSize="16dp" />


        <com.jd.oa.ui.IconFontView
            android:id="@+id/next_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:paddingStart="8dp"
            android:text="@string/icon_direction_right"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_16" />

    </LinearLayout>

    <com.haibin.calendarview.CalendarLayout
        android:id="@+id/calendar_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#fff"
        android:orientation="vertical"
        app:calendar_content_view_id="@+id/calendar_content"
        app:calendar_show_mode="both_month_week_view"
        app:gesture_mode="disabled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ym_area">

        <com.haibin.calendarview.CalendarView
            android:id="@+id/calendar_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="0dp"
            android:layout_marginRight="0dp"
            app:calendar_height="38dp"
            app:calendar_match_parent="false"
            app:calendar_padding_left="0dp"
            app:calendar_padding_right="0dp"
            app:current_day_text_color="#FFFFFF"
            app:day_text_size="17dp"
            app:month_view="com.jd.oa.joywork2.list.calendar.view.MeMonthView"
            app:month_view_scrollable="true"
            app:month_view_show_mode="mode_fix"
            app:selected_text_color="#F63218"
            app:selected_theme_color="#FEEBE8"
            app:week_bar_height="40dp"
            app:week_start_with="mon"
            app:week_text_color="#6A6A6A"
            app:week_text_size="14dp"
            app:week_view="com.jd.oa.joywork2.list.calendar.view.MeWeekView"
            app:week_view_scrollable="true"
            app:year_view_scrollable="false" />

        <LinearLayout
            android:id="@+id/calendar_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.jd.oa.joywork2.list.calendar.view.CalendarHandleLayout
                android:id="@+id/calendar_handle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:background="@color/white"
                android:elevation="5dp"
                android:outlineSpotShadowColor="#55000000"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="8dp">

                <ImageView
                    android:id="@+id/handle_indicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </com.jd.oa.joywork2.list.calendar.view.CalendarHandleLayout>

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
    </com.haibin.calendarview.CalendarLayout>

</com.jd.oa.joywork2.list.calendar.view.MyConstraintLayout>
