<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.view.swipe.SwipeMenuLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:contentViewId="@id/content"
    app:rightViewId="@id/swipe_right">

    <com.jd.oa.joywork.team.view.DividerLinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingStart="30dp"
        android:paddingTop="16dp"
        android:paddingEnd="12dp"
        app:dividerStartId="@id/mTitle">

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:text=""
            android:textColor="#333333"
            android:textSize="14dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/joyworkCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="@string/joywork_task_count"
                android:textColor="#666666"
                android:textSize="12dp" />

            <TextView
                android:paddingTop="16dp"
                android:id="@+id/summaryCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:includeFontPadding="false"
                android:paddingBottom="16dp"
                android:text="@string/joywork_summary_count2"
                android:textColor="#666666"
                android:textSize="12dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:paddingTop="16dp"
                android:id="@+id/commentCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:includeFontPadding="false"
                android:paddingBottom="16dp"
                android:text="@string/joywork_comment2"
                android:textColor="#666666"
                android:textSize="12dp" />
        </LinearLayout>
    </com.jd.oa.joywork.team.view.DividerLinearLayout>

    <LinearLayout
        android:id="@+id/swipe_right"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal" />
</com.jd.oa.joywork.view.swipe.SwipeMenuLayout>