<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mReturn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_set_visible"
            android:textColor="#333333"
            android:textSize="18dp" />

        <TextView
            android:id="@+id/mSave"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerInParent="true"
            android:enabled="false"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/me_save"
            android:textColor="@color/joywork_color_red"
            android:textSize="16dp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/mSupervisorContainer"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mSupervisorIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_circle"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:includeFontPadding="false"
            android:text="@string/joywork_supervisor_visible"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6" />

    <LinearLayout
        android:id="@+id/mAllContainer"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mAllIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_circle"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:includeFontPadding="false"
            android:text="@string/joywork_all_visible"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6" />

    <LinearLayout
        android:id="@+id/mPartContainer"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mPartIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_circle"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:includeFontPadding="false"
            android:text="@string/joywork_part_visible"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6" />

    <FrameLayout
        android:id="@+id/mPartSubInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/mVisibleItemsContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/mGettingTipsContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:visibility="gone">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mPartErrorIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="4dp"
                android:text="@string/icon_padding_closecircle"
                android:textColor="#FE3E33"
                android:textSize="@dimen/JMEIcon_14"
                android:visibility="gone" />

            <TextView
                android:id="@+id/mGettingTipsView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:includeFontPadding="false"
                android:text="@string/joywork_visible_people_getting"
                android:textColor="#333333"
                android:textSize="14dp"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/mProgressBar"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:indeterminateTint="@color/joywork_red"
                android:visibility="gone" />

            <TextView
                android:id="@+id/mPartErrorRetry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:includeFontPadding="false"
                android:text="@string/joywork_retry"
                android:textColor="#4C7CFF"
                android:textSize="14dp"
                android:visibility="gone" />
        </LinearLayout>
    </FrameLayout>

</LinearLayout>