<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:background="#CC000000"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="14dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_prompt_check"
            android:textColor="#FFD8D8D8"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:id="@+id/mContentView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/joywork_snackbar_tips"
            android:textColor="#FFFFFFFF"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/to_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/joywork_snackbar_to_detail"
            android:textColor="#FF4C7CFF"
            android:textSize="14dp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="73dp" />

    <View
        android:id="@+id/offset"
        android:layout_width="match_parent"
        android:layout_height="0dp" />
</LinearLayout>