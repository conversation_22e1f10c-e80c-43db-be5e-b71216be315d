<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginVertical="12dp">

            <FrameLayout
                android:id="@+id/tabs_container"
                android:layout_width="wrap_content"
                android:layout_height="34dp"
                android:background="@drawable/solid_all_100_f5f5f5"
                android:gravity="center_vertical"
                android:paddingHorizontal="6dp">

                <ImageView
                    android:id="@+id/mTabsIndicator"
                    android:layout_width="72dp"
                    android:layout_height="34dp"
                    android:src="@drawable/joywork_tab_bg"
                    android:scaleType="fitXY" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/tabs"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent" />
            </FrameLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mShowFilter"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:background="@drawable/joywork_all_round_4_1afe3e33"
                android:gravity="center"
                android:text="@string/icon_general_screen"
                android:textColor="#FE3E33"
                android:textSize="@dimen/JMEIcon_18"/>
        </LinearLayout>

        <FrameLayout
            android:id="@+id/mFilterContainer"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:paddingHorizontal="16dp" />

        <com.jd.oa.joywork.view.UntouchableViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/add"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_gravity="bottom|end"
        android:background="@drawable/jdme_joywork_add_bg">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_add"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/JMEIcon_20"/>
    </RelativeLayout>
</FrameLayout>