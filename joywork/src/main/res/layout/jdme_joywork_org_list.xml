<?xml version="1.0" encoding="utf-8"?>


<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="#232930"
            android:textSize="@dimen/JMEIcon_22"/>

        <TextView
            android:id="@+id/mTitleView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:text="@string/joywork_org_list_title"
            android:textColor="#333333"
            android:textSize="18dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mSearch"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_general_search"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22" />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mTabsRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:visibility="gone" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.jd.oa.joywork.view.UntouchableViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <FrameLayout
            android:id="@+id/mLoadingViewContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#ffffff">

            <com.airbnb.lottie.LottieAnimationView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="100dp"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/joywork_loading" />
        </FrameLayout>
    </FrameLayout>
</LinearLayout>