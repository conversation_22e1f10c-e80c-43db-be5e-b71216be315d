<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#F4F6F7"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="71dp">

    <ImageView
        android:layout_width="75dp"
        android:layout_height="83dp"
        android:src="@drawable/joywork_list_empty_icon" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="44dp"
        android:layout_marginTop="19dp"
        android:includeFontPadding="false"
        android:lineSpacingExtra="2dp"
        android:text="@string/joywork_list_empty_tips"
        android:textColor="#333333"
        android:textSize="12dp" />

    <TextView
        android:id="@+id/mCreate"
        android:layout_width="160dp"
        android:layout_height="36dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/joywork_all_round_100_4c7cff"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/joywork_new_list"
        android:textColor="#FFFFFF"
        android:textSize="14dp" />
</LinearLayout>