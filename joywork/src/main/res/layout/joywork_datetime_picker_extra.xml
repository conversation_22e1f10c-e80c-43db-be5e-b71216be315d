<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:background="@color/white"
        android:id="@+id/mAlertContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:includeFontPadding="false"
            android:text="@string/icon_general_bell"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:id="@+id/mAlertText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/joy_work_remind"
            android:textColor="#FF2E2D2D"
            android:textSize="16dp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/picker_ic_icon_arrow_right"
            android:textColor="#FF2E2D2D"
            android:textSize="16sp" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"/>

    <LinearLayout
        android:background="@color/white"
        android:id="@+id/mDupContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:includeFontPadding="false"
            android:text="@string/icon_general_repeat"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:id="@+id/mDupText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/joywork_update_dup2"
            android:textColor="#FF2E2D2D"
            android:textSize="16dp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/picker_ic_icon_arrow_right"
            android:textColor="#FF2E2D2D"
            android:textSize="16sp" />
    </LinearLayout>
</LinearLayout>