<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_all_round_8"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="16dp"
        android:paddingLeft="14dp">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/icon_prompt_add"
                android:textColor="#FF8F959E"
                android:textSize="@dimen/JMEIcon_16" />
        </FrameLayout>


        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/joywork_group_new_title"
            android:textColor="#6A6A6A"
            android:textSize="16dp"
            android:textStyle="bold" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="44dp" />
</LinearLayout>
