<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@drawable/joywork_bottom_round_10_ffffff"
    android:orientation="vertical">

    <View
        android:id="@+id/mDivider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/mLoadMore"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_load_more"
                android:textColor="#999999"
                android:textSize="14dp" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mArrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/icon_direction_down"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_14"/>

            <ProgressBar
                android:id="@+id/mProgressBar"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginStart="4dp"
                android:text="@string/icon_direction_down"
                android:indeterminateTint="@color/joywork_red"
                android:textColor="#999999"
                android:textSize="12dp" />
        </LinearLayout>

        <View
            android:id="@+id/mDividerVertical"
            android:layout_width="0.5dp"
            android:layout_height="16dp"
            android:layout_gravity="center_vertical"
            android:background="#E6E6E6" />

        <LinearLayout
            android:id="@+id/mAdd"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_prompt_add"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_14"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/joywork_kr_create_title"
                android:textColor="#999999"
                android:textSize="14dp" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>