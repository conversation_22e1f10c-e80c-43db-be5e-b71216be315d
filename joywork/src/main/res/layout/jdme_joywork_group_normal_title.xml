<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:text="@string/icon_direction_left"
        android:textColor="#232930"
        android:textSize="@dimen/JMEIcon_22"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/joywork_group_title"
            android:textColor="#232930"
            android:textSize="18dp"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/robot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:orientation="horizontal"
            android:paddingHorizontal="4dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/joywork_group_robot"
                android:textColor="#666666"
                android:textSize="11dp" />

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:text="@string/icon_direction_right"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_10"/>
        </LinearLayout>
    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/more_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:text="@string/icon_prompt_questioncircle"
        android:textColor="#000000"
        android:textSize="@dimen/JMEIcon_22"
        android:visibility="gone" />
</RelativeLayout>