<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="54dp"
    android:paddingHorizontal="12dp"
    android:paddingVertical="15dp">

    <TextView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/joywork2_f63218_1b1b1b"
        tools:text="内内容内容内容内容内容内容内容内容内容内容内容内容内容容" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/asc"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/joywork2_show_condition_bg"
        android:gravity="center"
        android:text="@string/icon_direction_arrowup"
        android:textColor="@color/joywork2_f63218_6a6a6a"
        android:textSize="@dimen/JMEIcon_14" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/desc"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/joywork2_show_condition_bg"
        android:gravity="center"
        android:text="@string/icon_direction_arrowdown"
        android:textColor="@color/joywork2_f63218_6a6a6a"
        android:textSize="@dimen/JMEIcon_14" />
</LinearLayout>