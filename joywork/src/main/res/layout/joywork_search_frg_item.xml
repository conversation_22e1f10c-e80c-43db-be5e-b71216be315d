<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mSwipe"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingTop="16dp">

        <TextView
            android:id="@+id/mStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:background="@drawable/solid_all_2_f5f5f5"
            android:includeFontPadding="false"
            android:padding="2dp"
            android:text="@string/joywork_screen_unfinish"
            android:textColor="#666666"
            android:textSize="10dp" />

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text=""
            android:textColor="#333333"
            android:textSize="16dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:paddingHorizontal="16dp"
        android:text="@string/joy_work_description"
        android:textColor="#999999"
        android:textSize="14dp" />

    <LinearLayout
        android:id="@+id/mOwnerContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/icon_edit_addcontact"
            android:textColor="#666666"
            android:textSize="@dimen/JMEIcon_14"/>

        <TextView
            android:id="@+id/mOwners"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#666666"
            android:textSize="12dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/deadline_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/icon_general_calendarl"
            android:textColor="#666666"
            android:textSize="@dimen/JMEIcon_14"/>

        <TextView
            android:id="@+id/tv_end_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#666666"
            android:textSize="12dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mProjectContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/icon_padding_listing"
            android:textColor="#666666"
            android:textSize="@dimen/JMEIcon_14"/>

        <TextView
            android:id="@+id/mProjects"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#666666"
            android:textSize="12dp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_marginTop="16dp"
        android:background="#F6F6F6" />
</LinearLayout>