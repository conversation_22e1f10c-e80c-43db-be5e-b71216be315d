<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="1dp"
        android:layout_height="16dp" />

    <LinearLayout
        android:id="@+id/endtime_container"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:gravity="center_vertical">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/end_time_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="12dp"
            android:includeFontPadding="false"
            android:text="@string/icon_padding_start"
            android:textColor="#666666"
            android:textSize="@dimen/JMEIcon_18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/time_container"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/joywork_item_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="12dp">

            <TextView
                android:id="@+id/time_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingEnd="12dp"
                android:text="@string/joywork_shortcut_other"
                android:textColor="#333333"
                android:textSize="14dp" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/clear_time"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingEnd="12dp"
                android:text="@string/icon_prompt_close"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_12"
                android:visibility="gone" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>