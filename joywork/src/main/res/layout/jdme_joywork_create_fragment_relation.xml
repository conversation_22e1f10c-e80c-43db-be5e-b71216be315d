<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/relationContainer"
    android:layout_width="match_parent"
    android:layout_height="32dp"
    android:layout_marginTop="16dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">
    <!--    icon-->
    <com.jd.oa.ui.IconFontView
        android:id="@+id/owner_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/icon_padding_concernedpeople"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_18" />

    <LinearLayout
        android:id="@+id/owner_container"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <!--        头像-->
        <com.jd.oa.joywork.view.JoyWorkAvatarView
            android:id="@+id/relation_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            app:alwaysShowHintText="true"
            app:avatarSize="20dp"
            app:hatPaddingExtra="4dp"
            app:maxChildCount="3" />

        <!--没关注人时提示文字-->
        <TextView
            android:id="@+id/relation_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:hint="@string/joywork_create_relation_hint"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingStart="8dp"
            android:text="@string/icon_direction_right"
            android:textColor="#D8D8D8"
            android:textSize="@dimen/JMEIcon_14"/>

    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/relation_add"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="6dp"
        android:background="@drawable/joywork_line_circle_dashed"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_add"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_12"/>
</LinearLayout>