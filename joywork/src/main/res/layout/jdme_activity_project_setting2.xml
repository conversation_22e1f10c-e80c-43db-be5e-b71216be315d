<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f8f9fa"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="#ffffff" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="8dp">

        <LinearLayout
            android:id="@+id/ll_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <TextView
                android:id="@+id/et_project_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="top"
                android:hint="@string/joywork_project_name_hint"
                android:lineSpacingExtra="3dp"
                android:paddingLeft="16dp"
                android:paddingTop="8dp"
                android:paddingRight="16dp"
                android:paddingBottom="8dp"
                android:textColor="#333333"
                android:textColorHint="#666666"
                android:textSize="16dp"
                tools:text="" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="12dp"
                android:visibility="gone">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_general_filetext"
                    android:textColor="#333333"
                    android:textSize="@dimen/JMEIcon_16"/>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="6dp"
                    android:text="@string/joywork_project_des_tip"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/mMore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/joywork_more_example"
                    android:textColor="#4C7CFF"
                    android:textSize="14dp"
                    android:visibility="gone" />
            </LinearLayout>

            <TextView
                android:id="@+id/et_project_des"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:gravity="top"
                android:hint="@string/joywork_project_desc_null_tip"
                android:lineSpacingExtra="3dp"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingRight="16dp"
                android:paddingBottom="16dp"
                android:textColor="#666666"
                android:textColorHint="#666666"
                android:textSize="14dp"
                tools:text="" />

        </LinearLayout>
    </ScrollView>
</LinearLayout>