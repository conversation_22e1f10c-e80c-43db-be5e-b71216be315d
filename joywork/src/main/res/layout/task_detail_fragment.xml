<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="taskDetailViewModel"
            type="com.jd.oa.joywork.detail.viewmodel.TaskDetailViewModel" />

        <variable
            name="taskDetailPresenter"
            type="com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="SpUsage">

        <include
            android:id="@+id/task_detail_header"
            layout="@layout/task_detail_header"
            app:detail="@{taskDetailViewModel.taskDetail}"
            app:presenter="@{taskDetailPresenter}" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <include
                android:id="@+id/task_detail_page"
                layout="@layout/task_detail_page"
                app:detail="@{taskDetailViewModel.taskDetail}"
                app:presenter="@{taskDetailPresenter}" />

            <View
                android:id="@+id/blank_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="invisible" />

            <include
                android:id="@+id/task_detail_bottom_flow"
                layout="@layout/task_detail_bottom_flow"
                app:detail="@{taskDetailViewModel.taskDetail}"
                app:presenter="@{taskDetailPresenter}" />

        </FrameLayout>
    </LinearLayout>
</layout>