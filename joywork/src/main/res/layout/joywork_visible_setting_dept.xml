<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="24dp"
    android:orientation="horizontal">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/add"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/joywork_line_circle_dashed"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_add"
        android:textColor="#666666"
        android:textSize="@dimen/JMEIcon_12"/>

    <View
        android:id="@+id/mDivider"
        android:layout_width="8dp"
        android:layout_height="1dp" />

    <TextView
        android:id="@+id/mTitle"
        android:layout_width="0dp"
        android:layout_height="24dp"
        android:layout_centerInParent="true"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text=""
        android:textColor="#333333"
        android:textSize="14dp" />
</LinearLayout>