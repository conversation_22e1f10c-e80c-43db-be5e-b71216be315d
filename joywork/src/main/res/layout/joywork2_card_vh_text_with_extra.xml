<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/joywork_top_round_8"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="20dp"
        android:paddingBottom="8dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/indicator"
            android:layout_width="46dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingStart="16dp"
            android:text="@string/icon_padding_caredown"
            android:textColor="#FF8F959E"
            android:textSize="@dimen/JMEIcon_14" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#FF1B1B1B"
                android:textSize="16dp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/count"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#FF6A6A6A"
                android:textSize="16dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/title"
                app:layout_constraintLeft_toRightOf="@id/title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/title" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_tabbar_more"
            android:textColor="#FF8F959E"
            android:textSize="@dimen/JMEIcon_18" />
    </LinearLayout>

    <View
        android:id="@+id/bottom_edge"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@drawable/joywork_bottom_round_8" />

    <View
        android:id="@+id/bottom_space"
        android:layout_width="match_parent"
        android:layout_height="12dp" />

</LinearLayout>




