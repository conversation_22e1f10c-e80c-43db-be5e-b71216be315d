<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="12dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:text="aaa"
        android:textSize="12dp" />

    <View
        android:id="@+id/divider"
        android:layout_width="1dp"
        android:layout_height="14dp"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="6dp"
        android:background="#BFC1C4" />

    <androidx.legacy.widget.Space
        android:id="@+id/space_view"
        android:layout_width="12dp"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical" />

</LinearLayout>
