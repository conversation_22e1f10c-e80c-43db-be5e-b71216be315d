<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAF5E5"
    android:orientation="horizontal"
    android:paddingStart="16dp">

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:lineSpacingExtra="2dp"
        android:paddingVertical="10dp"
        android:text="@string/joywork_issue_tips"
        android:textColor="#E6670C"
        android:textSize="14dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="10dp"
        android:text="@string/icon_prompt_close"
        android:textColor="#E6670C"
        android:textSize="@dimen/JMEIcon_14"/>
</LinearLayout>