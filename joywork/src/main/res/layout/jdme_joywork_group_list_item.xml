<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp"
    app:dividerStartId="@id/title">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <FrameLayout
            android:id="@+id/cb_task_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="1dp"
            app:layout_constraintEnd_toStartOf="@id/title"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/cb_task"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/icon_prompt_circle"
                android:textSize="@dimen/JMEIcon_18"/>

            <ImageView
                android:id="@+id/cb_task2"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/joywork_cb"
                android:visibility="visible" />
        </FrameLayout>

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text=""
            android:textColor="#232930"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/cb_task_container"
            app:layout_constraintTop_toTopOf="@id/cb_task_container"
            app:layout_constraintWidth_default="wrap" />

        <!--截止时间-->
        <TextView
            android:id="@+id/deadline"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:minHeight="18dp"
            android:text=""
            android:textColor="@color/joywork_red"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/dupIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="3dp"
            android:text="@string/icon_general_repeat"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_14"
            app:layout_constraintBottom_toBottomOf="@id/deadline"
            app:layout_constraintStart_toEndOf="@id/deadline"
            app:layout_constraintTop_toTopOf="@id/deadline" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.jd.oa.joywork.team.view.DividerLinearLayout>