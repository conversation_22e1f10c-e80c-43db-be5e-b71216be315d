<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginBottom="4dp"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mToggleMenu"
            android:layout_width="32dp"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/circle_bg_gray_f3f3f3"
            android:gravity="center"
            android:text="@string/icon_edit_justify"
            android:textSize="@dimen/JMEIcon_16" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_weight="1">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/mTabs"
                style="@style/TabStyle"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:background="@drawable/joywork2_tab_bg" />
        </FrameLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mShowCondition"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/joywork2_show_condition_bg"
            android:gravity="center"
            android:text="@string/icon_general_screen"
            android:textColor="#FE3E33"
            android:textSize="@dimen/JMEIcon_16" />
    </LinearLayout>

    <HorizontalScrollView
        android:id="@+id/mConditionContainer"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:scrollbars="none"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            tools:visibility="visible">

            <com.jd.oa.joywork2.view.AutoSelectLinearLayout
                android:id="@+id/mStatusContainer"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/mConditionStatusText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/joywork2_f63218_6a6a6a"
                    android:textSize="12dp"
                    tools:text="状态" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mConditionStatusIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/icon_direction_down"
                    android:textColor="@color/joywork2_f63218_6a6a6a"
                    android:textSize="@dimen/JMEIcon_14" />
            </com.jd.oa.joywork2.view.AutoSelectLinearLayout>

            <com.jd.oa.joywork2.view.AutoSelectLinearLayout
                android:id="@+id/mSortContainer"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/mConditionSorterText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/joywork2_f63218_6a6a6a"
                    android:textSize="12dp"
                    tools:text="排序" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mConditionSorterIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/icon_direction_down"
                    android:textColor="@color/joywork2_f63218_6a6a6a"
                    android:textSize="@dimen/JMEIcon_14" />
            </com.jd.oa.joywork2.view.AutoSelectLinearLayout>

            <com.jd.oa.joywork2.view.AutoSelectLinearLayout
                android:id="@+id/mGrouperContainer"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/mConditionGrouperText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/joywork2_f63218_6a6a6a"
                    android:textSize="12dp"
                    tools:text="分组" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mConditionGrouperIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/icon_direction_down"
                    android:textColor="@color/joywork2_f63218_6a6a6a"
                    android:textSize="@dimen/JMEIcon_14" />
            </com.jd.oa.joywork2.view.AutoSelectLinearLayout>
        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>