<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:background="@drawable/joywork_top_round_10"
    android:minHeight="76dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/mIconContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="12dp"
            android:paddingTop="22dp"
            android:paddingEnd="8dp"
            android:paddingBottom="10dp">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/icon_padding_right"
                android:textColor="#333333"
                android:textSize="@dimen/JMEIcon_12"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/mTitleContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingVertical="16dp">

            <TextView
                android:id="@+id/mTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:text="fadfadfadfadfadfdf"
                android:textColor="#333333"
                android:textSize="16dp"
                android:textStyle="bold" />

        </FrameLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mMore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="12dp"
            android:paddingTop="18dp"
            android:paddingBottom="10dp"
            android:text="@string/icon_tabbar_more"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_16"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mActionContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:orientation="horizontal"
        >

        <TextView
            android:paddingBottom="16dp"
            android:id="@+id/joyworkCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/joywork_task_count"
            android:textColor="#666666"
            android:textSize="12dp" />

        <TextView
            android:paddingBottom="16dp"
            android:id="@+id/summaryCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:includeFontPadding="false"
            android:text="@string/joywork_summary_count2"
            android:textColor="#666666"
            android:textSize="12dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:paddingBottom="16dp"
            android:id="@+id/commentCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:includeFontPadding="false"
            android:paddingHorizontal="12dp"
            android:text="@string/joywork_comment2"
            android:textColor="#666666"
            android:textSize="12dp" />
    </LinearLayout>

    <View
        android:id="@+id/mDivider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6" />
</LinearLayout>