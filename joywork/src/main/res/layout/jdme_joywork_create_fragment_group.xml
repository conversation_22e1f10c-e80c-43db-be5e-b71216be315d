<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/group_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:paddingVertical="6dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/group_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/icon_padding_grouping"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_18" />

    <TextView
        android:id="@+id/group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:background="@drawable/joywork_item_bg"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:hint="@string/joywork_select_group_list_title"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp"
        android:textColor="#333333"
        android:textColorHint="@color/title_sub"
        android:textSize="14dp" />

</LinearLayout>