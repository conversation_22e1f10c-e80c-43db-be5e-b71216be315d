<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <CheckBox
        android:id="@+id/cb"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@null"
        android:drawableStart="@drawable/jdme_selector_checkbox" />

    <TextView
        android:id="@+id/title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/cb"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="123"
        android:textColor="#1B1B1B"
        android:textSize="16dp" />

</androidx.constraintlayout.widget.ConstraintLayout>