<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="1dp"
        android:layout_height="16dp" />

    <LinearLayout
        android:id="@+id/priority_container"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:gravity="center_vertical">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/priority_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:includeFontPadding="false"
            android:text="@string/icon_general_priority"
            android:textColor="@color/icon_gray"
            android:textSize="@dimen/JMEIcon_18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/priority"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingHorizontal="12dp"
            android:text="@string/joywork_priority_title"
            android:textColor="@color/title_sub"
            android:textSize="@dimen/joywork_title_sub" />

        <TextView
            android:id="@+id/mPriorityHint"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:text="@string/joywork_priority_title"
            android:textColor="#999999"
            android:textSize="14dp" />
    </LinearLayout>
</LinearLayout>