<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="28dp"
    android:layout_marginStart="12dp"
    android:background="@drawable/jdme_bg_screen_sort_item"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iftv_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:text="@string/icon_general_screening"
        android:textColor="@color/color_FE3B30"
        android:textSize="@dimen/JMEIcon_14"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="0.5dp"
        android:includeFontPadding="false"
        android:textColor="@color/color_FE3B30"
        android:textSize="12dp"
        android:textStyle="bold"
        tools:text="1223123213" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iftv_close"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:paddingStart="8dp"
        android:paddingEnd="12dp"
        android:text="@string/icon_prompt_close"
        android:textColor="@color/color_FE3B30"
        android:textSize="@dimen/JMEIcon_12"/>
</LinearLayout>