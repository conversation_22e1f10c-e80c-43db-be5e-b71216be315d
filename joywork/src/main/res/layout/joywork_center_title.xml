<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    tools:ignore="MissingDefaultResource">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/close"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:textSize="@dimen/JMEIcon_16"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:paddingHorizontal="16dp" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:textColor="#FF232930"
        android:textSize="18dp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/action_container"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:orientation="horizontal" />
</RelativeLayout>