<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <View
        android:id="@+id/priority_value_vh"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        android:background="@drawable/joywork_priority_flag_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_all_round_8"
        android:paddingVertical="12dp"
        android:paddingStart="11dp"
        android:paddingEnd="14dp">

        <FrameLayout
            android:id="@+id/cb_task_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/cb_task"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_prompt_circle"
                android:textSize="@dimen/JMEIcon_18" />

            <ImageView
                android:id="@+id/cb_task2"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                android:src="@drawable/joywork_cb"
                android:visibility="visible" />
        </FrameLayout>

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#1B1B1B"
            android:textSize="16dp"
            android:layout_marginEnd="@dimen/joywork_8dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/cb_task_container"
            app:layout_constraintEnd_toStartOf="@+id/more_button_icon"
            app:layout_constraintStart_toEndOf="@+id/cb_task_container"
            app:layout_constraintTop_toTopOf="@id/cb_task_container" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/more_button_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:text="@string/icon_tabbar_more"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/cb_task_container"
            app:layout_constraintTop_toTopOf="@id/cb_task_container"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.jd.oa.joywork2.list.calendar.view.HorizontalLabelLayout
            android:id="@+id/label_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="6dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"/>

        <LinearLayout
            android:id="@+id/second_line"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/label_container">

            <TextView
                android:id="@+id/start_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#6A6A6A"
                android:textSize="14dp" />
            <TextView
                android:id="@+id/hyphen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="-"
                android:textColor="#6A6A6A"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/deadline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#6A6A6A"
                android:textSize="14dp" />

            <LinearLayout
                android:id="@+id/owner_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.jd.oa.joywork.view.JoyWorkAvatarView
                    android:id="@+id/mAvatarView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:avatarSize="20dp"
                    app:hatPaddingExtra="4dp"
                    app:maxAvatar="2"
                    app:maxChildCount="3"
                    app:showMore="false" />
            </LinearLayout>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/comment_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_general_question"
                android:textSize="@dimen/JMEIcon_14" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/third_line"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/second_line">

            <LinearLayout
                android:id="@+id/owner_container_third"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.jd.oa.joywork.view.JoyWorkAvatarView
                    android:id="@+id/mAvatarView_third"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:avatarSize="20dp"
                    app:hatPaddingExtra="4dp"
                    app:maxAvatar="2"
                    app:maxChildCount="3"
                    app:showMore="false" />
            </LinearLayout>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/comment_date_third"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_general_question"
                android:textSize="@dimen/JMEIcon_14" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/operate_button_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="6dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/second_line"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>



