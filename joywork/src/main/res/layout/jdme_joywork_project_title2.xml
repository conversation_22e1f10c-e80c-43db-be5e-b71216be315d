<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="52dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="20dp"
    android:paddingBottom="8dp"
    tools:ignore="MissingDefaultResource">

    <View
        android:layout_width="16dp"
        android:layout_height="1dp" />

    <com.jd.oa.ui.CircleImageView
        android:id="@+id/avatar"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#FF232930"
        android:textSize="16dp"
        android:textStyle="bold" />

</LinearLayout>
