<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/des_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:paddingVertical="14dp">

    <TextView
        android:id="@+id/des"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginStart="16dp"
        android:gravity="center_vertical"
        android:hint="@string/joy_work_description"
        android:includeFontPadding="false"
        android:maxLength="1000"
        android:textColor="#666666"
        android:textColorHint="@color/title_sub"
        android:textSize="@dimen/joywork_title_sub"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
