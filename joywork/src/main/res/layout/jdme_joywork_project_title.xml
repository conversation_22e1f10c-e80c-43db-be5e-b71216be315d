<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="20dp"
    android:paddingBottom="8dp">

    <FrameLayout
        android:id="@+id/prefix_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="16dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/joywork_cb_unavailable"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_circle"
            android:textSize="@dimen/JMEIcon_18"
            android:visibility="invisible" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/icon_direction_down"
            android:textColor="#FF8F959E"
            android:textSize="@dimen/JMEIcon_12"/>

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/avatar"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:visibility="gone" />
    </FrameLayout>


    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#FF232930"
        android:textSize="16dp"
        android:textStyle="bold" />


    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_tabbar_more"
        android:textColor="#FF8F959E"
        android:textSize="@dimen/JMEIcon_18"/>

</LinearLayout>