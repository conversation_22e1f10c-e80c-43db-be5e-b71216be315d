<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="18dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/mStatus"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="8dp"
        android:src="@drawable/joywork_status_blue" />

    <FrameLayout
        android:id="@+id/mTabContainer"
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="1dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/mTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@drawable/joywork_all_round_100_1a4c7cff"
                android:includeFontPadding="false"
                android:paddingHorizontal="6dp"
                android:paddingVertical="1dp"
                android:textColor="#4C7CFF"
                android:textSize="10dp" />

            <View
                android:layout_width="7dp"
                android:layout_height="1dp" />
        </LinearLayout>

        <ImageView
            android:layout_gravity="end|bottom"
            android:src="@drawable/joywork_lock"
            android:scaleType="fitXY"
            android:id="@+id/mImage"
            android:layout_width="12dp"
            android:layout_height="12dp" />
    </FrameLayout>

</LinearLayout>