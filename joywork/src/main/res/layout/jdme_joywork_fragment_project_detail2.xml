<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingHorizontal="12dp"
                android:text="@string/icon_direction_left"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_22" />

            <FrameLayout
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_gravity="center_vertical">

                <ImageView
                    android:id="@+id/mIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/joywork_project_icon" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mIconText"
                    tools:text="@string/icon_general_user"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1B1B1B"
                    android:textSize="@dimen/JMEIcon_18" />
            </FrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/mTitleContainer"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:paddingEnd="16dp">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FF242931"
                    android:textSize="18dp"
                    android:textStyle="bold"
                    tools:text="标题最多字数限制，超过300字就省略"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/setting"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="15dp"
                android:gravity="center"
                android:text="@string/icon_general_set"
                android:textColor="#1B1B1B"
                android:textSize="@dimen/JMEIcon_16" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/view_type"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="@string/icon_edit_unorderedlist"
                android:textColor="#1B1B1B"
                android:textSize="@dimen/JMEIcon_16" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/show_condition"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="12dp"
                android:gravity="center"
                android:text="@string/icon_general_screen"
                android:textColor="@color/jdme_work_filter_button_selector"
                android:textSize="@dimen/JMEIcon_16" />

        </LinearLayout>

        <FrameLayout
            android:id="@+id/condition_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <FrameLayout
            android:id="@+id/list_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>
</FrameLayout>