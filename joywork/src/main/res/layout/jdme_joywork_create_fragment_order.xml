<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/link_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="8dp"
        android:includeFontPadding="false"
        android:text="@string/icon_padding_listing"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_18" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_link_project_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp" />

        <LinearLayout
            android:id="@+id/add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="8dp">

            <com.jd.oa.ui.IconFontView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_prompt_add"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_14"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:text="@string/joy_work_link_task_groups"
                android:textColor="#999999"
                android:textSize="@dimen/joywork_title_sub" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>