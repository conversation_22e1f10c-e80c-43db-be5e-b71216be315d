<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f8f9fa"
    android:orientation="vertical">

    <include layout="@layout/jdme_layout_project_create_action_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/et_project_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:gravity="top"
                android:hint="@string/joywork_project_name_hint"
                android:inputType="textMultiLine"
                android:lineSpacingExtra="3dp"
                android:minHeight="64dp"
                android:paddingLeft="16dp"
                android:paddingTop="8dp"
                android:paddingRight="16dp"
                android:textColor="#333333"
                android:textColorHint="@color/color_8f959e"
                android:textSize="16dp"
                tools:text="" />

            <TextView
                android:id="@+id/mTextLength"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:background="#ffffff"
                android:gravity="end|center_vertical"
                android:includeFontPadding="false"
                android:paddingEnd="14dp"
                android:text="0"
                android:textColor="#666666"
                android:textSize="14dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingLeft="16dp"
                android:paddingTop="18dp"
                android:paddingRight="16dp"
                android:paddingBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@string/joywork_project_des_tip"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/mExample"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/joywork_more_example"
                    android:textColor="#4C7CFF"
                    android:textSize="14dp" />
            </LinearLayout>

            <EditText
                android:id="@+id/et_project_des"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="top"
                android:hint="@string/joywork_project_des_hint"
                android:lineSpacingExtra="3dp"
                android:maxLength="1000"
                android:minHeight="56dp"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:paddingBottom="16dp"
                android:textColor="#666666"
                android:textColorHint="#666666"
                android:textSize="14dp"
                tools:text="" />

            <LinearLayout
                android:id="@+id/ll_members"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="11dp"
                android:gravity="center_vertical"
                android:background="@color/white"
                android:paddingHorizontal="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/icon1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/icon_general_userteam" />

                <TextView
                    android:id="@+id/tv_relation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:text="@string/joywork_shared_member"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_relation"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="24dp"
                    android:layout_marginLeft="8dp" />

                <TextView
                    android:id="@+id/tv_relation_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="14dp"
                    android:textColor="#333333"
                    android:textSize="14dp"
                    tools:text="aaa" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/icon2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="9dp"
                    tools:visibility="gone"
                    android:text="@string/icon_direction_right"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_14"/>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</LinearLayout>