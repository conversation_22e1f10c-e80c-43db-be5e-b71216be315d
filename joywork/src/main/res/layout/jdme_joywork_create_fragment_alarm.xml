<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/alert_container"
    android:layout_width="match_parent"
    android:layout_height="32dp"
    android:layout_marginTop="16dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/alert_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="12dp"
        android:includeFontPadding="false"
        android:text="@string/icon_general_bell"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@drawable/joywork_item_bg"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/alert"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:hint="@string/joy_work_remind"
            android:paddingHorizontal="12dp"
            android:textColor="#333333"
            android:textColorHint="@color/title_sub"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingEnd="12dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_12"
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>