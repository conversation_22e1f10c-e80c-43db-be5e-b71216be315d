<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp">

        <TextView
            android:id="@+id/jdme_id_myapply_dropdown_item"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/sp_16"
            tools:text="中文" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/jdme_id_myapply_dropdown_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_prompt_check"
            android:textColor="@color/joywork_red"
            android:textSize="@dimen/JMEIcon_18"/>
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="16dp"
        android:background="#DEE0E3" />

</LinearLayout>