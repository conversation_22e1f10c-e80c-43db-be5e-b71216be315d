<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/deadline_container"
    android:layout_width="134dp"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:paddingHorizontal="12dp"
    android:paddingVertical="4dp"
    android:paddingStart="12dp">

    <LinearLayout
        android:id="@+id/deadline_dup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/deadline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:includeFontPadding="false"
            android:text=""
            android:textColor="@color/joywork_red"
            android:textSize="12dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/dupIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/icon_general_repeat"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_14" />
    </LinearLayout>

    <View
        android:id="@+id/mark_value_vh"
        android:layout_width="8dp"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:background="#8F959E" />
</FrameLayout>