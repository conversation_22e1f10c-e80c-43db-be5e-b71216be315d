<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:orientation="horizontal"
    android:paddingStart="8dp"
    android:paddingEnd="8dp">

    <LinearLayout
        android:id="@+id/mContentView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/joywork2_menu_item_bg_sel"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="14dp"
        android:paddingVertical="7dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#1B1B1B"
            android:textSize="@dimen/JMEIcon_20"
            tools:text="@string/icon_general_user" />

        <TextView
            android:id="@+id/mContentText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#1B1B1B"
            android:textSize="16sp"
            tools:text="我创我创建的我创建的我创建的建的" />

        <TextView
            android:id="@+id/mCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:textColor="@color/joywork2_menu_item_color"
            tools:text="11" />
    </LinearLayout>
</LinearLayout>