<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:orientation="horizontal"
    app:dividerStartId="@id/top_container">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/hat"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/joywork_yellow_hat"
            android:visibility="visible"
            app:layout_constraintCircle="@id/image"
            app:layout_constraintCircleAngle="315"
            app:layout_constraintCircleRadius="28dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/image"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:src="@drawable/file_compression"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/top_container"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text=""
            android:textColor="#232930"
            android:textSize="16dp" />

        <View
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/department"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text=""
            android:textColor="#FF8F959E"
            android:textSize="14dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/mSet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:background="@drawable/joywork_stroke_round_2_cdcdcd"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/joywork_set_owner"
        android:textColor="#666666" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/action"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_prompt_close"
        android:textColor="#FF8F959E"
        android:textSize="@dimen/JMEIcon_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</com.jd.oa.joywork.team.view.DividerLinearLayout>