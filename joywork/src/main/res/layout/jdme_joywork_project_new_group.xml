<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="20dp"
        android:paddingBottom="8dp">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="16dp">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/joywork_cb_unavailable"
                android:includeFontPadding="false"
                android:text="@string/icon_prompt_circle"
                android:textSize="@dimen/JMEIcon_18"
                android:visibility="invisible" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/icon_prompt_add"
                android:textColor="#FF8F959E"
                android:textSize="@dimen/JMEIcon_16"/>
        </FrameLayout>


        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/joywork_group_new_title"
            android:textColor="#8F959E"
            android:textSize="16dp"
            android:textStyle="bold" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="44dp" />
</LinearLayout>
