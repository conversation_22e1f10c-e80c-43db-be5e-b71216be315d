<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:layout_marginTop="50dp">

    <View
        android:layout_width="wrap_content"
        android:layout_height="1px"
        android:background="#E6E6E6" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#FF333333"
            android:textSize="16dp"
            tools:text="aaa" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:ellipsize="end"
                android:gravity="start"
                android:textColor="#666666"
                android:textSize="14dp"
                tools:visibility="visible" />
        </FrameLayout>


        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/icon_direction_right"
            android:textColor="#666666"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>


</LinearLayout>