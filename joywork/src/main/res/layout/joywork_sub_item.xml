<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="14dp"
    android:paddingBottom="14dp"
    app:dividerStartId="@id/cb_task">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/cb_task"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_circle"
            android:textSize="@dimen/JMEIcon_18"/>

        <ImageView
            android:id="@+id/cb_task2"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/joywork_cb"
            android:visibility="visible" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text=""
            android:textColor="#333333"
            android:textSize="16dp" />

        <LinearLayout
            android:id="@+id/mBottomTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:orientation="horizontal">

            <com.jd.oa.joywork.view.JoyWorkAvatarView
                android:id="@+id/avatar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:paddingEnd="8dp"
                app:alwaysShowHintText="false"
                app:avatarSize="16dp"
                app:hatPaddingExtra="3dp"
                app:maxChildCount="2" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/timeIcon"
                android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:text="@string/icon_general_calendarl"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_14"/>

            <TextView
                android:id="@+id/time"
                android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:textColor="#666666"
                android:textSize="12dp" />
        </LinearLayout>
    </LinearLayout>
</com.jd.oa.joywork.team.view.DividerLinearLayout>