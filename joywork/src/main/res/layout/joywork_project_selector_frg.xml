<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/mTips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F8F8F9"
        android:paddingHorizontal="16dp"
        android:paddingVertical="7dp"
        android:text="@string/joywork_project_selector_tips"
        android:textColor="#1B1B1B"
        android:textSize="14dp" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/mSwipe"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>