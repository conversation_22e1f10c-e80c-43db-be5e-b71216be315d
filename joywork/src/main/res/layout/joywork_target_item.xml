<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/space"
        android:layout_width="1dp"
        android:layout_height="8dp" />

    <LinearLayout
        android:id="@+id/rl_1"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_group_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxEms="10"
            android:maxLines="1"
            android:minEms="1"
            android:paddingStart="8dp"
            android:textColor="#FF232930"
            android:textSize="14dp"
            tools:text="" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_close"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="10dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#FF8F959E"
            android:textSize="@dimen/JMEIcon_12"/>


    </LinearLayout>
</LinearLayout>