<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/owner_def_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/icon_edit_addcontact"
        android:textColor="#666666"
        android:textSize="@dimen/JMEIcon_16"/>

    <TextView
        android:id="@+id/owner_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:includeFontPadding="false"
        android:text="@string/joywork_create_owner_hint"
        android:textColor="#333333"
        android:textSize="14dp" />
</LinearLayout>