<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/mTitle"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lineSpacingExtra="8dp"
        android:maxLines="2"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:textColor="#232930"
        android:textSize="16dp" />

    <View
        android:id="@+id/mDivider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="8dp"
        android:background="#F0F3F3" />
</LinearLayout>
