<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="12dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/mTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:paddingEnd="4dp"
        android:text=""
        android:textColor="#333333"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mTitle" />

    <TextView
        android:id="@+id/mValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="#333333"
        android:textSize="12dp"
        app:layout_constraintStart_toEndOf="@id/mTag"
        app:layout_constraintTop_toTopOf="@id/mTag" />
</LinearLayout>