<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="detail"
            type="com.jd.oa.joywork.detail.data.entity.TaskDetailEntity" />

        <variable
            name="presenter"
            type="com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter" />
    </data>

    <LinearLayout
        android:id="@+id/bottom_btn"
        android:layout_width="match_parent"
        android:layout_height="62dp"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:baselineAligned="false"
        android:divider="@drawable/joywork_ll_divider_12"
        android:dividerPadding="12dp"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingTop="6dp"
        android:paddingBottom="24dp"
        android:showDividers="middle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/comment_btn"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/joywork_gray_stroke_round">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/comment_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:includeFontPadding="false"
                android:text="@string/icon_general_review"
                android:textColor="@color/icon_gray"
                android:textSize="@dimen/JMEIcon_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/comment_tips"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/comment_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:ellipsize="middle"
                android:text="@string/joywork_detail_update"
                android:textColor="#333333"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/comment_icon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--        仅我完成/恢复自己-->
        <TextView
            android:id="@+id/finish_me"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/joywork_gray_stroke_round"
            android:gravity="center"
            android:onClick="@{(theView) -> presenter.onlyMe(theView,detail)}"
            android:text="@string/joywork_finish_only_me"
            android:textColor="#333333"
            android:textSize="14dp"
            android:visibility="gone"
            app:finishOnlyMe="@{detail}" />

        <!--        完成待办-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/finish_task_btn"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/joywork_gray_stroke_round"
            android:onClick="@{(theView) -> presenter.changeTaskStatus(theView,detail)}"
            android:visibility="gone"
            app:finishTaskBtn="@{detail}">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/finish_task_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:includeFontPadding="false"
                android:text="@string/icon_prompt_check"
                android:textSize="@dimen/JMEIcon_16"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/finish_task_tips"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/finish_task_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_finish_task"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/finish_task_icon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--        完成全部-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/finish_btn"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/joywork_gray_stroke_round"
            android:onClick="@{(theView) -> presenter.changeAllStatus(theView,detail)}"
            android:visibility="gone"
            app:finishAllBtn="@{detail}">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/finish_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:includeFontPadding="false"
                android:text="@string/icon_prompt_check"
                android:textSize="@dimen/JMEIcon_16"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/finish_tips"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.jd.oa.joywork.view.AutoSizeTextView
                android:id="@+id/finish_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_finish_all"
                android:textSize="14dp"
                app:backupTextSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/finish_icon"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

</layout>