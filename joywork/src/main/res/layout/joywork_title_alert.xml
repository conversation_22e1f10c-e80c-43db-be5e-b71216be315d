<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minWidth="280dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="24dp"
        android:text=""
        android:textColor="#FF232930"
        android:textSize="16dp" />

    <LinearLayout
        android:id="@+id/choice_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        android:paddingHorizontal="24dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="14dp"
        android:background="#FFDEE0E3" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="#FF232930"
            android:textSize="16dp" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="#FFDEE0E3" />

        <TextView
            android:id="@+id/ok"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/joywork_red"
            android:textSize="16dp" />
    </LinearLayout>
</LinearLayout>