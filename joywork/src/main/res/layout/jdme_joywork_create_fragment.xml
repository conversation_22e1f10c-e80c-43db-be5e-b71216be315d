<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">
    <!--头-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:textSize="@dimen/JMEIcon_16"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_prompt_close" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:text="@string/me_joywork_new"
            android:textColor="#FF232930"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/new_task"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingEnd="16dp"
            android:text="@string/joywork_new"
            android:textColor="@color/joywork_create_color" />
    </RelativeLayout>
    <!--    导入父待办-->
    <LinearLayout
        android:id="@+id/mInputContainer"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#1A4C7CFF"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:text="@string/joywork_input_parent"
            android:textColor="#FF232930"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/mInputText"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/joywork_input"
            android:textColor="#FF4C7CFF"
            android:textSize="14dp" />
    </LinearLayout>

    <ScrollView
        android:id="@+id/scroll_page"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:ignore="SpUsage">

        <LinearLayout
            android:id="@+id/containerParent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

        </LinearLayout>

    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/mSendDD"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#f9f9f9"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/mSendIcon"
                android:layout_width="36dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:paddingStart="12dp"
                android:paddingEnd="6dp"
                android:paddingVertical="16dp"
                android:src="@drawable/jdme_selector_checkbox" />

            <TextView
                android:id="@+id/mSendText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingVertical="16dp"
                android:text="@string/joywork_send_dd"
                android:textColor="#333333"
                android:textSize="14dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="#F5F5F5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/attachment_icon"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:text="@string/icon_general_paperclip"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_18"/>


            <com.jd.oa.ui.IconFontView
                android:id="@+id/target_item"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:text="@string/icon_padding_target"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_18"/>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/order_item"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:text="@string/icon_padding_listing"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_18"/>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/alarm_icon"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:text="@string/icon_general_bell"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_18"/>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/dup_icon"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:text="@string/icon_general_repeat"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_18"/>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/priority_icon"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:text="@string/icon_general_priority"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_18"/>


        </LinearLayout>
    </LinearLayout>

</LinearLayout>