<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/endtime_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    android:gravity="center_vertical"
    android:minHeight="32dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/end_time_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="12dp"
        android:includeFontPadding="false"
        android:text="@string/icon_general_calendarl"
        android:textColor="#666666"
        android:textSize="@dimen/JMEIcon_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/mTodayContainer"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:background="@drawable/joywork_item_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="8dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_edit_historyright"
            android:textColor="#29CC31"
            android:textSize="@dimen/JMEIcon_16"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:text="@string/joywork_plan_alert_today"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/mTomorrowContainer"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginHorizontal="8dp"
        android:background="@drawable/joywork_item_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="8dp">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_direction_rightcircle"
            android:textColor="#547ef7"
            android:textSize="@dimen/JMEIcon_16"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:text="@string/joywork_tomorrow"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/time_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/joywork_item_bg"
        android:gravity="center_vertical"
        android:minHeight="32dp"
        android:orientation="horizontal"
        android:paddingVertical="6dp"
        android:paddingStart="8dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/time_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:paddingEnd="8dp"
            android:text="@string/icon_padding_othertime"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_16"/>

        <TextView
            android:id="@+id/time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="12dp"
            android:text="@string/joywork_shortcut_other"
            android:textColor="#333333"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/clear_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="12dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_12"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>