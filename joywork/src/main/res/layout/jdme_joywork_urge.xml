<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F8F9"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingStart="16dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mBackView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/icon_prompt_close"
            android:textColor="#232930"
            android:textSize="@dimen/JMEIcon_20"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_centerHorizontal="true"
            android:includeFontPadding="false"
            android:text="@string/me_apply_urge"
            android:textColor="#232930"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/mSend"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/joywork_urge_send_btn"
            android:textColor="@color/joywork_red"
            android:textSize="14dp" />
    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:paddingHorizontal="16dp"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/mTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:includeFontPadding="false"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:textColor="#232930"
                android:textSize="14dp" />

            <LinearLayout
                android:id="@+id/mOwnerContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp">

                <TextView
                    android:id="@+id/send"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="@string/joywork_urge_members_title"
                    android:textColor="#8F959E"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/avatar"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <com.jd.oa.joywork.view.JoyWorkAvatarView
                            android:id="@+id/mAvatarView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            app:alwaysShowHintText="true"
                            app:avatarSize="32dp"
                            app:ellipsizeTextSize="15dp"
                            app:hatPaddingExtra="5dp"
                            app:hatSize="14dp"
                            app:hintTextAppearance="@style/JoyWorkUrgeOwnerTP"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@id/send"
                            app:layout_constraintTop_toTopOf="parent"
                            app:maxChildCount="3" />
                    </FrameLayout>

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:ellipsize="end"
                        android:includeFontPadding="false"
                        android:maxLines="1"
                        android:text="@string/icon_direction_right"
                        android:textColor="#666666"
                        android:textSize="@dimen/JMEIcon_14"/>
                </LinearLayout>
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/mCCContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="#FFFFFF"
                android:orientation="vertical"
                android:paddingHorizontal="16dp">

                <TextView
                    android:id="@+id/mCCTips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingVertical="12dp"
                    android:text="@string/joywork_cc"
                    android:textColor="#8F959E"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.jd.oa.joywork.view.JoyWorkAvatarView
                    android:id="@+id/mCCAvatarView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginBottom="12dp"
                    app:alwaysShowHintText="true"
                    app:avatarSize="32dp"
                    app:ellipsizeTextSize="15dp"
                    app:hintTextAppearance="@style/JoyWorkUrgeOwnerTP"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/mCCTips"
                    app:layout_constraintTop_toBottomOf="@id/mCCTips"
                    app:maxChildCount="3" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mCCAddView"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="6dp"
                    android:background="@drawable/joywork_line_circle_dashed"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/icon_prompt_add"
                    android:textColor="@color/icon_gray"
                    android:textSize="@dimen/JMEIcon_12"
                    app:layout_constraintBottom_toBottomOf="@id/mCCTips"
                    app:layout_constraintStart_toEndOf="@id/mCCTips"
                    app:layout_constraintTop_toTopOf="@id/mCCTips" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mCCArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:includeFontPadding="false"
                    android:text="@string/icon_direction_right"
                    android:textColor="#666666"
                    android:textSize="@dimen/JMEIcon_14"
                    app:layout_constraintBottom_toBottomOf="@id/mCCAvatarView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/mCCAvatarView" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <EditText
                android:id="@+id/mContentView"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginTop="10dp"
                android:gravity="top|start"
                android:hint="@string/joywork_urge_hint"
                android:includeFontPadding="false"
                android:inputType="textMultiLine"
                android:maxLength="200"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:text="@string/joywork_urge_def_text"
                android:textSize="16dp" />

        </LinearLayout>
    </ScrollView>

</LinearLayout>