<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:background="@drawable/joywork_all_round_6_f5f5f5"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="4dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/icon_general_search"
                android:textColor="#CDCDCD"
                android:textSize="@dimen/JMEIcon_16"/>

            <EditText
                android:id="@+id/et_search"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="#00000000"
                android:gravity="start|center_vertical"
                android:hint="@string/joywork_search_init"
                android:inputType="text"
                android:minHeight="32dp"
                android:singleLine="true"
                android:textColor="@color/comm_text_title"
                android:textColorHint="#CDCDCD"
                android:textSize="14dp" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mClear"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingEnd="16dp"
                android:text="@string/icon_padding_closecircle"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_18"
                android:visibility="gone" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/me_cancel"
            android:textColor="#333333"
            android:textSize="16dp" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:paddingHorizontal="4dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#F0F3F3" />

    <FrameLayout
        android:id="@+id/me_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>