<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginTop="12dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="12dp"
    android:paddingVertical="10dp">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:paddingEnd="16dp"
        android:text="@string/icon_padding_caredown"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_14"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#FF232930"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/countOuter"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/countOuter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title"
            app:layout_constraintTop_toTopOf="parent">

            <com.jd.oa.joywork.view.BubbleView
                android:id="@+id/countContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:paddingVertical="2dp"
                app:extraPaddingPercent="0.3"
                app:numViewBgColor="#FE564C"
                app:numViewBorderColor="@color/white"
                app:numViewBorderWidth="1dp">

                <TextView
                    android:id="@+id/count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="3"
                    android:textColor="#FFFFFF"
                    android:textSize="10dp" />
            </com.jd.oa.joywork.view.BubbleView>

            <TextView
                android:id="@+id/count2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:includeFontPadding="false"
                android:text="33"
                android:textColor="#333333"
                android:textSize="16dp" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>