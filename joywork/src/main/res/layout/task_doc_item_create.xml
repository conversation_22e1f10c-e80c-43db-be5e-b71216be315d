<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:orientation="horizontal"
    tools:ignore="SpUsage">

    <ImageView
        android:id="@+id/doc_type"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/doc_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="#232930"
        android:textSize="16dp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/doc_del_icon"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="16dp"
        android:text="@string/icon_prompt_close"
        android:textColor="#999999"
        android:textSize="@dimen/JMEIcon_12"/>
</LinearLayout>