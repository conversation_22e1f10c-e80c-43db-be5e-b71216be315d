<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="#ffffff">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:textSize="@dimen/JMEIcon_16"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_prompt_close"
            android:textColor="#333333" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:text="@string/joywork_alert"
            android:textColor="#232930"
            android:textSize="18dp" />

        <TextView
            android:id="@+id/mSave"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="确定"
            android:textColor="#FE3E33" />
    </RelativeLayout>

    <TextView
        android:id="@+id/mTips"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:paddingHorizontal="16dp"
        android:text="@string/joywork_alert_tips"
        android:textColor="#333333"
        android:textSize="14dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff" />
</LinearLayout>