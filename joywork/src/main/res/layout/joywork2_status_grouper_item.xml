<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/mRoot"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="54dp"
    android:paddingHorizontal="12dp"
    android:paddingVertical="15dp">

    <TextView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/joywork2_f63218_1b1b1b"
        tools:text="内内容内容内容内容内容内容内容内容内容内容内容内容内容容" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/check"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="12dp"
        android:gravity="center"
        android:text="@string/icon_prompt_check"
        android:textColor="@color/joywork2_f63218_6a6a6a"
        android:textSize="@dimen/JMEIcon_14" />

</LinearLayout>
