<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">

    <ImageView
        android:id="@+id/link_icon"
        android:layout_width="@dimen/JMEIcon_18"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_link_project_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:layout_toEndOf="@id/link_icon"
        app:layout_constraintEnd_toStartOf="@id/add"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/link_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="spread" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/add"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/joywork_line_circle_dashed"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_add"
        android:textColor="@color/icon_gray"
        android:textSize="@dimen/JMEIcon_12"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/link_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/link_icon" />
</androidx.constraintlayout.widget.ConstraintLayout>