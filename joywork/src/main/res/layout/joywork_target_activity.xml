<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mBack"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_prompt_close"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_16"/>

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/joywork_new_target"
            android:textColor="#232930"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/mCreate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/joywork_new"
            android:textColor="@color/joywork_create_color"
            android:textSize="16dp" />
    </RelativeLayout>

    <EditText
        android:id="@+id/mContentView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@color/translucent"
        android:gravity="start|top"
        android:hint="@string/joywork_target_hint"
        android:includeFontPadding="false"
        android:maxLength="200"
        android:minLines="2"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/mNum"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="right"
        android:paddingEnd="16dp"
        android:text="0/200"
        android:textColor="#666666"
        android:textSize="14dp" />
</LinearLayout>