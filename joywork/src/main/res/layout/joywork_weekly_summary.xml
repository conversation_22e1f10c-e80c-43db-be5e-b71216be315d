<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#ffffff"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mBack"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/joywork_select_goal"
            android:textColor="#242931"
            android:textSize="18dp" />

    </RelativeLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="38dp"
        android:background="#DCE5FF"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:text="@string/joywork_weekly_summary_tips"
        android:textColor="#4C7CFF"
        android:textSize="14dp" />

    <LinearLayout
        android:id="@+id/mSelectQ"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp">

        <TextView
            android:id="@+id/mQTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#333333"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:text="@string/icon_direction_down"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E6E6E6" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/mSelLocation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:includeFontPadding="false"
                android:text="@string/joywork_weekly_location"
                android:textColor="#333333"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/mSelDir"
                tools:text="这是一个丢脸发kfda"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="8dp"
                android:includeFontPadding="false"
                android:text="@string/joywork_private_dir"
                android:textColor="#4C7CFF"
                android:textSize="14dp"
                android:visibility="visible" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/mArrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:text="@string/icon_direction_down"
                android:textColor="#999999"
                android:textSize="@dimen/JMEIcon_14"
                tools:visibility="visible"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/mProgressBar"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginStart="8dp"
                android:indeterminateTint="@color/joywork_red" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />
        </LinearLayout>

        <TextView
            android:id="@+id/mSure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginVertical="12dp"
            android:background="@drawable/joywork_all_round_4_sel"
            android:includeFontPadding="false"
            android:paddingHorizontal="10dp"
            android:paddingVertical="8dp"
            android:text="@string/joywork_confirm"
            android:textColor="#ffffff"
            android:textSize="14dp" />
    </LinearLayout>
</LinearLayout>