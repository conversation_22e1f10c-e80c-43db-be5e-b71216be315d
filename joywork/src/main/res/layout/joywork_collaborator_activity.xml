<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22"/>

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_relation"
            android:textColor="#FF242931"
            android:textSize="18dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/add"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingEnd="16dp"
            android:text="@string/icon_edit_addcontact"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22"/>

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />
</LinearLayout>