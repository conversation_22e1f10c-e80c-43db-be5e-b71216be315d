<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/add"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_gravity="bottom|end"
        android:background="@drawable/jdme_joywork_add_bg">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_add"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/JMEIcon_20" />

        <View
            android:id="@+id/add_foreground"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:visibility="gone"
            android:layout_centerInParent="true"
            android:background="@drawable/joywork2_add_forground"
            android:gravity="center" />
    </RelativeLayout>
</FrameLayout>