<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mContentView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/joywork2_menu_item_bg_sel"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="14dp"
        android:paddingEnd="12dp"
        android:paddingVertical="7dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#1B1B1B"
            android:textSize="@dimen/JMEIcon_20"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="@string/icon_general_user" />

        <TextView
            android:id="@+id/mContentText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#1B1B1B"
            android:textSize="16sp"
            app:layout_constraintLeft_toRightOf="@+id/mIcon"
            app:layout_constraintRight_toLeftOf="@+id/mCount"
            app:layout_constraintTop_toTopOf="@+id/mIcon"
            app:layout_constraintBottom_toBottomOf="@+id/mIcon"
            tools:text="待我处理" />

        <TextView
            android:id="@+id/mCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:textColor="@color/joywork2_menu_item_color"
            android:maxLines="1"
            android:layout_marginEnd="16dp"
            app:layout_constraintLeft_toRightOf="@+id/mContentText"
            app:layout_constraintRight_toLeftOf="@+id/task_setting"
            app:layout_constraintTop_toTopOf="@+id/mIcon"
            app:layout_constraintBottom_toBottomOf="@+id/mIcon"
            app:layout_constraintHorizontal_bias="0"
            tools:text="11" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/task_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/JMEIcon_16"
            android:text="@string/icon_general_set"
            app:layout_constraintBottom_toBottomOf="@+id/mIcon"
            app:layout_constraintLeft_toRightOf="@+id/mCount"
            app:layout_constraintRight_toLeftOf="@+id/toggle_button"
            app:layout_constraintTop_toTopOf="@+id/mIcon" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/toggle_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:textSize="@dimen/JMEIcon_20"
            android:layout_marginStart="16dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/task_setting"
            app:layout_constraintTop_toTopOf="@+id/mIcon"
            app:layout_constraintBottom_toBottomOf="@+id/mIcon"
            android:text="@string/icon_smalldown"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 包含 Item 的容器 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/childMenuContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="28dp"/>

</LinearLayout>
