<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="vertical">

        <com.jd.oa.joywork.view.JoyWorkOffsetLayout
            android:id="@+id/offset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            app:offset="4dp"
            app:reverseDrawingOrder="false" />

        <com.jd.oa.joywork.view.JoyWorkOffsetLayout
            android:id="@+id/offset2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            app:offset="4dp"
            app:reverseDrawingOrder="false" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/hintTextContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginStart="6dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/hintText1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxWidth="64dp"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/hintText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:includeFontPadding="false"
            android:textColor="#333333"
            android:textSize="14dp" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/emptyContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical" />
</merge>