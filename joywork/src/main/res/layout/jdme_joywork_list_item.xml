<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.view.swipe.SwipeMenuLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    app:contentViewId="@id/normal"
    app:rightViewId="@id/swipe_right">

    <FrameLayout
        android:id="@+id/normal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.jd.oa.joywork.team.view.DividerLinearLayout
            android:id="@+id/mItemContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="14dp">

            <FrameLayout
                android:id="@+id/cb_task_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="16dp"
                android:paddingTop="17dp"
                android:paddingEnd="8dp">

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/cb_task"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/icon_prompt_circle"
                    android:textSize="@dimen/JMEIcon_18"/>

                <ImageView
                    android:id="@+id/cb_task2"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:includeFontPadding="false"
                    android:src="@drawable/joywork_cb"
                    android:text="@string/icon_prompt_circle"
                    android:textSize="16dp"
                    android:visibility="visible" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/titleContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingTop="14dp"
                android:paddingEnd="16dp">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lineSpacingExtra="2dp"
                    android:maxLines="2"
                    android:text="@string/joywork_tomorrow"
                    android:textColor="#232930"
                    android:textSize="16dp" />

                <LinearLayout
                    android:id="@+id/bottomContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:divider="@drawable/joywork_ll_divider_16"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:showDividers="middle">

                    <TextView
                        android:id="@+id/risk"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="4dp"
                        android:text="@string/joywork_risk_risk"
                        android:textSize="10dp" />

                    <LinearLayout
                        android:id="@+id/deadlineContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.jd.oa.ui.IconFontView
                            android:id="@+id/deadlineIcon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="@string/icon_general_calendarl"
                            android:textSize="@dimen/JMEIcon_12"/>
                        <!--截止时间-->
                        <TextView
                            android:id="@+id/deadline"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:paddingStart="6dp"
                            android:text="@string/joywork_shortcut_deadline_tips"
                            android:textColor="@color/joywork_red"
                            android:textSize="12dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/projectContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.jd.oa.ui.IconFontView
                            android:id="@+id/projectIcon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="@string/icon_padding_listing"
                            android:textColor="#999999"
                            android:textSize="@dimen/JMEIcon_12"/>
                        <!--清单-->
                        <TextView
                            android:id="@+id/project"
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_marginStart="6dp"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:maxLines="1"
                            android:text=""
                            android:textColor="#999999"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>

                <com.jd.oa.joywork.view.JoyWorkAvatarView
                    android:id="@+id/mAvatarView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="8dp"
                    app:alwaysShowHintText="true"
                    app:avatarOverlap="false"
                    app:avatarSize="16dp"
                    app:hatPaddingExtra="3dp"
                    app:hatSize="5dp"
                    app:hintTextAppearance="@style/JoyWorkListItemAvatarHint"
                    app:iconRes="@drawable/joywork_list_item_icon"
                    app:iconSize="6dp"
                    app:maxChildCount="3" />
            </LinearLayout>
        </com.jd.oa.joywork.team.view.DividerLinearLayout>

        <View
            android:id="@+id/cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#99FFFFFF"
            android:visibility="gone" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/swipe_right"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal" />
</com.jd.oa.joywork.view.swipe.SwipeMenuLayout>