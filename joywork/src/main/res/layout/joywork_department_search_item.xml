<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:orientation="horizontal"
    app:dividerStartId="@id/mContentView">

    <ImageView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:src="@drawable/joywork_department_icon" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mContentView"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="16dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/mAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginStart="4dp"
            android:background="@drawable/stroke_all_2_4c7cff"
            android:includeFontPadding="false"
            android:paddingHorizontal="4dp"
            android:paddingVertical="2dp"
            android:text="@string/joywork_added"
            android:textColor="#4C7CFF"
            android:textSize="10dp"
            app:layout_constraintBottom_toBottomOf="@id/mTitle"
            app:layout_constraintStart_toEndOf="@id/mTitle"
            app:layout_constraintTop_toTopOf="@id/mTitle" />

        <TextView
            android:id="@+id/mSubTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#999999"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/mDel"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingEnd="16dp"
        android:text="@string/icon_prompt_close"
        android:textColor="#999999"
        android:textSize="@dimen/JMEIcon_16"/>
</com.jd.oa.joywork.team.view.DividerLinearLayout>