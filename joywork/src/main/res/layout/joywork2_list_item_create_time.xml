<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/create_rooter"
    android:layout_width="134dp"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingHorizontal="12dp"
    android:paddingVertical="4dp"
    android:paddingStart="12dp">

    <TextView
        android:id="@+id/create_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:includeFontPadding="false"
        android:text=""
        android:textColor="@color/joywork_red"
        android:textSize="12dp" />

    <View
        android:id="@+id/mark_value_vh"
        android:layout_width="8dp"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:background="#8F959E" />
</FrameLayout>