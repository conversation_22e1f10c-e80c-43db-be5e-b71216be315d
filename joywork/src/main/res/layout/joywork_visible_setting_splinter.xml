<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mAllContainer"
    android:layout_width="match_parent"
    android:layout_height="78dp"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp"
    app:dividerStartId="@id/mAllContainer">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/mSubTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:includeFontPadding="false"
            android:text="@string/joywork_visible_department"
            android:textColor="#333333"
            android:textSize="14dp" />

        <View
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mSubRv"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_gravity="bottom"
            android:clickable="false" />
    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/mPartArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:includeFontPadding="false"
        android:text="@string/icon_direction_right"
        android:textColor="#FF666666"
        android:textSize="@dimen/JMEIcon_14"/>
</com.jd.oa.joywork.team.view.DividerLinearLayout>