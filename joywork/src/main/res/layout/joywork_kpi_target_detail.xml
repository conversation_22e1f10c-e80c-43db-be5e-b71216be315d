<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp" />

    <FrameLayout
        android:id="@+id/mLoadingViewContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#ffffff">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/mLoadingView"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="50dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/joywork_loading" />
    </FrameLayout>

    <com.jd.oa.joywork.view.JoyWorkWebView
        android:id="@+id/mWebView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>