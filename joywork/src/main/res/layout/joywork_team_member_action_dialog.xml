<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/joywork_shortcut_create_dialog_bg"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/editor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:lines="1"
                    android:text="@string/joywork_can_edit"
                    android:textColor="@color/me_setting_foreground"
                    android:textSize="@dimen/sp_16" />

                <ImageView
                    android:id="@+id/team_member_editor_select"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:src="@drawable/joywork_icon_setting_checked"
                    android:visibility="invisible"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="6dp"
                android:ellipsize="end"
                android:lines="1"
                android:text="@string/team_member_editor_desc"
                android:textColor="#8F959E" />

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:background="@color/color_dee0e3" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/viewer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:lines="1"
                    android:text="@string/joywork_team_viewer"
                    android:textColor="@color/me_setting_foreground"
                    android:textSize="@dimen/sp_16" />

                <ImageView
                    android:id="@+id/jdme_id_myapply_dropdown_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:src="@drawable/joywork_icon_setting_checked"
                    android:visibility="invisible"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="6dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@string/joywork_team_member_viewer_desc"
                android:textColor="#8F959E" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:background="@color/color_dee0e3" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_remove"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:paddingLeft="16dp"
            android:text="@string/joywork_alert_remove"
            android:textColor="#FE3B30"
            android:textSize="18dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/color_f8f9fa" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="match_parent"
            android:layout_height="76dp"
            android:background="@color/white"
            android:gravity="center"
            android:paddingBottom="20dp"
            android:text="@string/cancel"
            android:textColor="@color/color_232930"
            android:textSize="18dp" />
    </LinearLayout>
</FrameLayout>