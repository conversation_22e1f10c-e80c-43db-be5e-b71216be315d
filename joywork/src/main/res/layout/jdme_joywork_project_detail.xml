<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_screen_sort"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:visibility="gone" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:background="#F6F6F6">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/joywork_project_col_shadow" />
        <!--    分组标题-->
        <com.jd.oa.joywork.team.view.ItemContainer
            android:id="@+id/title_container"
            android:layout_width="wrap_content"
            android:layout_height="32dp">

            <LinearLayout
                android:id="@+id/title_container_child"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/name"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="16dp"
                    android:text="@string/joywork_project_col_name"
                    android:textColor="#62656D"
                    android:textSize="12dp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="#DEE0E3" />

                <TextView
                    android:layout_width="132dp"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="12dp"
                    android:text="@string/joywork_more_owner"
                    android:textColor="#62656D"
                    android:textSize="12dp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="#DEE0E3" />

                <TextView
                    android:layout_width="132dp"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="12dp"
                    android:text="@string/joywork_shortcut_deadline_tips"
                    android:textColor="#62656D"
                    android:textSize="12dp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="#DEE0E3" />

                <TextView
                    android:layout_width="132dp"
                    android:layout_height="30dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="12dp"
                    android:text="@string/joywork_update_priority"
                    android:textColor="#62656D"
                    android:textSize="12dp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:id="@+id/ex_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone" />
            </LinearLayout>
        </com.jd.oa.joywork.team.view.ItemContainer>
    </FrameLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.jd.oa.joywork.team.view.HRecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>