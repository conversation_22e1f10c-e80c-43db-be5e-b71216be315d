<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/itv_project_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/joywork_project_icon" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/itv_project_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/icon_padding_target"
                android:textColor="@color/color_232930"
                android:textSize="@dimen/JMEIcon_18"
                android:visibility="gone" />
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_project"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/color_232930"
                android:textSize="@dimen/sp_16"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/mDefault"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/mDefault"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="8dp"
                android:background="@drawable/solid_all_100_e6e6e6"
                android:includeFontPadding="false"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="@string/joywork_default"
                android:textColor="#666666"
                android:textSize="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_project"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <TextView
            android:id="@+id/riskTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="10dp"
            android:text="@string/joywork_risk"
            android:textSize="10dp" />

        <TextView
            android:id="@+id/riskCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="0"
            android:textColor="@color/joywork_red"
            android:textSize="10dp" />

        <TextView
            android:id="@+id/unfinishTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="10dp"
            android:text="@string/joywork_screen_unfinish"
            android:textSize="10dp" />

        <TextView
            android:id="@+id/unfinishNums"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="0"
            android:textSize="10dp" />
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_gravity="bottom"
        android:layout_marginLeft="48dp"
        android:background="#EDEDEE" />

</FrameLayout>