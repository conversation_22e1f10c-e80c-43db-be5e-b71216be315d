<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:text="@string/icon_direction_left"
                android:textColor="#232930"
                android:textSize="@dimen/JMEIcon_22" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#FF242931"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_weight="1"
                android:layout_marginEnd="16dp"
                tools:text="标题最多字数限制，超过300字就省略"/>

            <com.jd.oa.ui.IconFontView
                android:id="@+id/setting"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="12dp"
                android:gravity="center"
                android:text="@string/icon_general_set"
                android:textColor="#1B1B1B"
                android:textSize="@dimen/JMEIcon_16" />
        </LinearLayout>

        <HorizontalScrollView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:paddingHorizontal="16dp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="12dp">

                <LinearLayout
                    android:id="@+id/mStatusContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:background="@drawable/solid_all_4_0afe3b30"
                    android:gravity="center"
                    android:paddingHorizontal="12dp">

                    <TextView
                        android:id="@+id/mStatusText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/joywork_screen_unfinish"
                        android:textColor="#FE3E33"
                        android:textSize="12dp" />

                    <com.jd.oa.ui.IconFontView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:includeFontPadding="false"
                        android:text="@string/icon_direction_down"
                        android:textColor="#FE3E33"
                        android:textSize="@dimen/JMEIcon_14" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mFilterContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/solid_all_4_f5f5f5"
                    android:gravity="center"
                    android:paddingHorizontal="12dp"
                    android:visibility="gone">

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/mFilterIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/icon_general_screening"
                        android:textColor="#232930"
                        android:textSize="@dimen/JMEIcon_14" />

                    <TextView
                        android:id="@+id/mFilterText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:includeFontPadding="false"
                        android:maxLines="1"
                        android:text="@string/joywork_project_setting_filter"
                        android:textColor="#232930"
                        android:textSize="12dp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mSortContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/solid_all_4_f5f5f5"
                    android:gravity="center"
                    android:paddingHorizontal="12dp"
                    android:visibility="gone">

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/mSortIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/icon_workplacesortascending"
                        android:textColor="#232930"
                        android:textSize="@dimen/JMEIcon_14" />

                    <TextView
                        android:id="@+id/mSortText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:includeFontPadding="false"
                        android:text="@string/joywork_project_setting_sort"
                        android:textColor="#232930"
                        android:textSize="12dp" />

                </LinearLayout>

                <View
                    android:id="@+id/mDivider"
                    android:layout_width="0.5dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="8dp"
                    android:background="#FFE6E6E6"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/mSaveView"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/stroke_all_4_e6e6e6"
                    android:orientation="horizontal"
                    android:paddingHorizontal="18dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/joywork_save_setting"
                        android:textColor="#333333"
                        android:textSize="12dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mSave"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/stroke_all_4_e6e6e6"
                    android:orientation="horizontal"
                    android:paddingHorizontal="18dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/joywork_saved"
                        android:textColor="#333333"
                        android:textSize="12dp" />

                    <com.jd.oa.ui.IconFontView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:includeFontPadding="false"
                        android:text="@string/icon_padding_questioncircle"
                        android:textColor="#FFCDCDCD"
                        android:textSize="@dimen/JMEIcon_14" />
                </LinearLayout>

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mClear"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/icon_prompt_close"
                    android:textColor="#FF999999"
                    android:textSize="@dimen/JMEIcon_12"
                    android:visibility="gone" />
            </LinearLayout>
        </HorizontalScrollView>

        <FrameLayout
            android:id="@+id/mContentView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/add"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_gravity="bottom|end"
        android:background="@drawable/jdme_joywork_add_bg">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_prompt_add"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/JMEIcon_20" />
    </RelativeLayout>
</FrameLayout>