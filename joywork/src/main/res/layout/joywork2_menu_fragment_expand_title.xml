<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingHorizontal="14dp"
        android:paddingVertical="7dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/headIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/JMEIcon_20"
            android:text="@string/icon_add_box" />

        <TextView
            android:id="@+id/mTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="#1B1B1B"
            android:textSize="16sp"
            android:text="@string/joywork2_archive_project" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tailIcon"
            android:layout_height="match_parent"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:textColor="#FF6A6A6A"
            android:textSize="@dimen/JMEIcon_24"
            android:text="@string/icon_smalldown" />
    </LinearLayout>

</LinearLayout>