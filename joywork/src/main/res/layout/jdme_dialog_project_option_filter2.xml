<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.joywork.team.view.DividerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:lines="1"
                android:text=""
                android:textColor="#333333"
                android:textSize="16dp" />

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="8dp"
                android:includeFontPadding="false"
                android:text="@string/icon_direction_right"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_16"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/mSubTitleContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/mSubTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:textColor="#666666"
                android:textSize="14dp"
                tools:text="截止时" />

            <TextView
                android:id="@+id/mSubContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical|start"
                android:includeFontPadding="false"
                android:textColor="#666666"
                android:textSize="14dp"
                tools:text="截止时" />
        </LinearLayout>

    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:text="@string/icon_prompt_check"
        android:textColor="#FE3B30"
        android:textSize="@dimen/JMEIcon_18"/>

</com.jd.oa.joywork.team.view.DividerLinearLayout>