<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/joywork_list_tabbar_title_height">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:includeFontPadding="false"
            android:text="@string/joywork_project_selector"
            android:textColor="#232930"
            android:textSize="18dp"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/mNew"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <com.jd.oa.ui.IconFontView
                android:id="@+id/search"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/icon_prompt_add"
                android:textColor="#F63218"
                android:textSize="@dimen/JMEIcon_14" />

            <TextView
                android:id="@+id/some_id"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="8dp"
                android:gravity="center"
                android:text="@string/joywork_new_list"
                android:textColor="#F63218"
                android:textSize="14dp" />
        </LinearLayout>

    </RelativeLayout>

    <FrameLayout
        android:id="@+id/mContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal"
        android:paddingHorizontal="12dp"
        android:paddingVertical="4dp">


        <TextView
            android:id="@+id/mNext"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/joywork_a4_sel"
            android:gravity="center"
            android:text="@string/me_confirm"
            android:textColor="#ffffff"
            android:textSize="14dp" />
    </LinearLayout>
</LinearLayout>