<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rooter"
    android:layout_width="134dp"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:paddingHorizontal="12dp"
    android:paddingVertical="4dp"
    android:paddingStart="12dp">

    <TextView
        android:id="@+id/priority"
        android:layout_width="96dp"
        android:layout_height="18dp"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text=""
        android:textColor="@color/translucent"
        android:textSize="12dp" />

    <View
        android:id="@+id/priority_value_vh"
        android:layout_width="8dp"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:background="#8F959E"/>
</FrameLayout>