<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mBack"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="16dp"
            android:text="@string/icon_direction_left"
            android:textColor="@color/icon_black"
            android:textSize="@dimen/JMEIcon_22" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/joywork_link_target"
            android:textColor="#242931"
            android:textSize="18dp" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/mSelectQ"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp">

        <TextView
            android:id="@+id/mQTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#333333"
            android:textSize="14dp" />

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:text="@string/icon_direction_down"
            android:textColor="#999999"
            android:textSize="@dimen/JMEIcon_14"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>