<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/mOuter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/joywork_select_outer"
            android:textColor="#333333"
            android:textSize="14dp"
            android:textStyle="bold" />

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/JMEIcon_16"
            android:text="@string/icon_direction_right"
            android:textColor="#999999" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp"
        android:text="@string/joywork_cur_group"
        android:textColor="#999999"
        android:textSize="14dp" />

    <LinearLayout
        android:id="@+id/selAllContainer"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/mSelCbAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/icon_prompt_circle"
            android:textColor="#8F959E"
            android:textSize="@dimen/JMEIcon_18"/>

        <TextView
            android:id="@+id/mSelAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:text="@string/me_check_all"
            android:textColor="#232930"
            android:textSize="14dp" />
    </LinearLayout>

</LinearLayout>