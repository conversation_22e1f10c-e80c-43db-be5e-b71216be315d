<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginTop="10dp"
    android:background="@drawable/solid_all_4_ffffff"
    android:orientation="vertical"
    android:paddingBottom="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="12dp">

        <View
            android:id="@+id/mReadStatus"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            android:background="@drawable/solid_all_100_fe3e33" />

        <TextView
            android:id="@+id/mLabel"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/solid_all_2_1afe3e33"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="4dp"
            android:text=""
            android:textColor="#FFFE3E33"
            android:textSize="10dp" />

        <TextView
            android:id="@+id/mTaskName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#FF333333"
            android:textSize="16dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="13dp"
        android:orientation="horizontal">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/mHeader"
            android:layout_width="28dp"
            android:layout_height="28dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/mName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="10"
                android:textColor="#333333"
                android:textSize="12dp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/mCustomEventContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="vertical" />

            <TextView
                android:id="@+id/mTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:includeFontPadding="false"
                android:text=""
                android:textColor="#999999"
                android:textSize="12dp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>