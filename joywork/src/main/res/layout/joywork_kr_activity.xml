<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/mTitleContainer"
        android:layout_width="match_parent"
        android:layout_height="44dp" />

    <EditText
        android:id="@+id/mContentView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@color/translucent"
        android:gravity="start|top"
        android:hint="@string/joywork_kr_create_title"
        android:includeFontPadding="false"
        android:maxLength="200"
        android:minLines="2"
        android:focusable="true"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/mNum"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="right"
        android:paddingEnd="16dp"
        android:text="0/200"
        android:textColor="#666666"
        android:textSize="14dp" />
</LinearLayout>
