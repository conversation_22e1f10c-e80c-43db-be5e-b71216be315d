<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="52dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:background="@drawable/jdme_selector_bg_click">

        <ImageView
            android:id="@+id/itv_project_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:src="@drawable/joywork_project_icon" />

        <TextView
            android:id="@+id/tv_project"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_toStartOf="@id/iv"
            android:layout_toEndOf="@id/itv_project_icon"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:shadowRadius="3.0"
            android:textColor="#232930"
            android:textSize="@dimen/sp_16"
            tools:text="12sadjksald时间冻结撒滴啊上吊死激动啊极大时间段" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iv"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:text="@string/icon_direction_right"
            android:textSize="@dimen/JMEIcon_18"            android:textColor="#BFC1C4" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="48dp"
            android:background="#F0F3F3" />
    </RelativeLayout>
</RelativeLayout>