<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refreshView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycleView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#F4F6F7"
            tools:visiblity="gone" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:id="@+id/ll_list_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F4F6F7"
        android:gravity="center"
        android:visibility="gone"
        android:orientation="vertical">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/joywork_list_empty_icon" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="44dp"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@string/joywork_list_empty_tips"
            android:textColor="#333333"
            android:textSize="12dp" />


        <TextView
            android:id="@+id/tv_create_project"
            android:layout_width="160dp"
            android:layout_height="36dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/joywork_all_round_100_4c7cff"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/joywork_new_list"
            android:textColor="#FFFFFF"
            android:textSize="14dp" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/add"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_gravity="bottom|end"
        android:background="@drawable/jdme_joywork_add_bg">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/icon_edit_newentry"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/JMEIcon_20"/>
    </RelativeLayout>
</FrameLayout>