<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="joywork_confirm">Confirm</string>
    <string name="joywork_comma">,</string>
    <string name="joywork_dunhao">,</string>
    <string name="joywork_share_success">Share Succeeds</string>
    <string name="joywork_time_no_set">Not Set</string>
    <string name="joywork_del">Delete</string>
    <string name="joywork_today">Today</string>
    <string name="joywork_empty">No Task now</string>
    <string name="joywork_create_success">Created Successfully</string>
    <string name="joywork_urge">Urgent</string>
    <string name="joywork_tomorrow">Tomorrow</string>
    <string name="joywork_upload_failure">Failed</string>
    <string name="joywork_from_chat">From Message: %s</string>
    <string name="joywork_from_huiji">From Joyminutes: %s</string>
    <string name="joywork_from_shimo">From Docs: %s</string>
    <string name="joywork_from_meeting">From Meeting: %s</string>
    <string-array name="joywork_month">
        <item>January</item>
        <item>February</item>
        <item>March</item>
        <item>April</item>
        <item>May</item>
        <item>June</item>
        <item>July</item>
        <item>August</item>
        <item>September</item>
        <item>October</item>
        <item>November</item>
        <item>December</item>
    </string-array>

    <string-array name="joywork_month_short">
        <item>Jan</item>
        <item>Feb</item>
        <item>Mar</item>
        <item>Apr</item>
        <item>May</item>
        <item>Jun</item>
        <item>Jul</item>
        <item>Aug</item>
        <item>Sep</item>
        <item>Oct</item>
        <item>Nov</item>
        <item>Dec</item>
    </string-array>
    <string name="joywork_year_month">%2$s %1$s</string>

    <string name="joywork_task_todo">Inbox</string>
    <string name="joywork_task_plan">Plan</string>
    <string name="joywork_task_someday">Someday</string>
    <string name="joywork_task_assignor">I Assigned</string>
    <string name="joywork_task_cooperator">Following</string>
    <string name="joywork_task_approval">I Approval</string>
    <string name="joywork_task_tabbar_title">Tasks</string>
    <string name="joywork_plan_time">MM/dd</string>
    <string name="joywork_action_detail">Details</string>
    <string name="joywork_action_restore">Undelete</string>
    <string name="joywork_action_assgin">Mark</string>
    <string name="joywork_list_from">This task needs to be completed on the <![CDATA[<b>%1$s</b>]]> PC side</string>
    <string name="joywork_list_from2">This task needs to be completed on the <![CDATA[<b>%1$s</b>]]></string>
    <string name="joywork_list_go_third">go</string>

    <string name="joywork_shortcut_title_hint">Task name </string>

    <string name="me_joywork_start_normal">%s Start</string>

    <string name="joywork_urge_hint">Please enter a reminder message with a maximum of 200 alphanumeric characters</string>

    <string name="joywork_urge_success">Success</string>
    <string name="joywork_urge_tips_ok">Confirm</string>
    <string name="joywork_urge_send_btn">Send</string>
    <string name="joywork_shortcut_deadline_tips">Due date</string>
    <string name="joy_work_finish">Complete</string>
    <string name="joy_work_description">Description of background and objectives</string>
    <string name="joy_work_child_works">Add subtask</string>
    <string name="joy_work_like">Like</string>
    <string name="joy_work_like_2">%1$d likes</string>
    <string name="joy_work_like_3"> You liked it</string>
    <string name="joy_work_like_4">You and others liked it</string>


    <string name="joywork_alert_reverse">Undo</string>
    <string name="joywork_alert_remove">Remove</string>
    <string name="joywork_attachment_title">Attach a file</string>
    <string name="joywork_alert_tomorrow">Tomorrow  9:00</string>
    <string name="joywork_alert_week">Next Monday  9:00</string>
    <string name="joywork_alert_week_tips">Next week</string>
    <string name="joywork_alert_select_tips">Select the date and time</string>
    <string name="joywork_alert_later">Later today</string>
    <string name="joywork_alert_error">Do not support setting the past time</string>
    <string name="joywork_priority_title">Priority</string>
    <string name="joywork_des_title">Description</string>
    <string name="joywork_detail_exit">Leave task</string>
    <string name="joywork_detail_del">Delete task</string>
    <string name="joywork_detail_restore">Undelete task</string>
    <string name="task_operate_fail">Cancel</string>
    <string name="joywork_error_msg_length">130</string>
    <string name="joy_work_waiting">Loading…</string>
    <string name="joywork_sub_title">Subtask</string>
    <string name="joy_work_doc">Joyspace</string>
    <string name="joy_work_res">Attachment</string>
    <string name="joywork_read">Read</string>
    <string name="joywork_unread">Unread</string>
    <string name="joywork_take_photo">Photo</string>
    <string name="joywork_relation">Follower</string>
    <string name="joywork_relation_empty">No follower</string>

    <string name="joy_work_retry">Retry</string>
    <string name="joy_work_no_athority">No permission</string>
    <string name="joywork_parent_title">Parent task：%s</string>
    <string name="joywork_shared_member">List members</string>
    <string name="joy_work_link_task_groups">Add to team list</string>


    <string name="joywork_tabbar_title_team">My Lists</string>
    <string name="joywork_tabbar_title_my">My Tasks</string>
    <string name="joywork_team_member_list">Members</string>
    <string name="joywork_project_rename">Rename section</string>
    <string name="joywork_project_new_before">Add section above</string>
    <string name="joywork_project_new_after">Add section below</string>
    <string name="joywork_project_new_left">Add section left</string>
    <string name="joywork_project_new_right">Add section right</string>
    <string name="joywork_project_group_sort">Group Rank Management</string>
    <string name="joywork_project_section_sort">Group Rank</string>
    <string name="joywork_project_delete">Delete section</string>
    <string name="joywork_project_action_title">Section settings</string>
    <string name="joywork_project_del_title">Delete the section?</string>
    <string name="joywork_project_del_save">Delete this section and keep these tasks</string>
    <string name="joywork_project_del_hard">Delete this section and delete these tasks</string>
    <string name="joywork_can_edit">Can edit</string>
    <string name="team_member_editor_desc">Can edit tasks in the list</string>
    <string name="joywork_team_member_viewer_desc">Can view tasks in the list</string>
    <string name="joywork_team_viewer">Can view</string>
    <string name="creator">Owner</string>
    <string name="jdme_joywork_transfer_creator">Transfer owner</string>

    <string name="joywork_team_exit">Leave</string>
    <string name="joywork_please_input">Please enter</string>
    <string name="joywork_please_input_reject_reason">Please enter the reasons (no more than 200 words)</string>
    <string name="joywork_please_input_name">Please enter a section name</string>
    <string name="joywork_group_name">Section name</string>
    <string name="joywork_reject_task">Refuse Task</string>
    <string name="joywork_confirm_reject">OK</string>
    <string name="joywork_group_new_title">New section</string>
    <string name="joywork_list_setting">List information</string>
    <string name="joywork_select_group_tips">Select section</string>
    <string name="joywork_select_group_list_title">Select section</string>
    <string name="no_project">No List</string>
    <string name="joywork_new">Create</string>
    <string name="joywork_new_list">New List</string>
    <string name="joywork_project_name_hint">Please enter the name, no more than 200 characters</string>
    <string name="joywork_project_transfer_dec">After transfer, you will become a shared member (can edit)</string>

    <string name="joywork_task_link_untitled_group">Untitled section</string>
    <string name="joywork_task_link_select_project">Select list</string>
    <string name="joywork_task_link_untitled_project">Untitled project</string>
    <string name="joywork_project_delete_tip">After deleting the list, the unassigned tasks in the list will be deleted . The assigned tasks will not be affected. Are you sure to delete?</string>
    <string name="joywork_project_quit_tip">After exiting the list, you will not be able to view the tasks in the list. Are you sure to exit?</string>
    <string name="joywork_project_create_cancle_tip">Discard unsaved changes?</string>
    <string name="joywork_project_desc_null_tip">No description</string>

    <string name="joywork_action_move">Move</string>
    <string name="joywork_mark_dialog_title">Select%s</string>
    <string name="joy_work_link_task_groups_success">Success</string>

    <string name="joywork_project_col_name">Task name</string>
    <string name="joywork_project_setting_filter">Filter</string>
    <string name="joywork_project_setting_setting">Settings</string>
    <string name="joywork_project_setting_sort">Sort</string>
    <string name="joywork_project_setting_filter_only">Just my task</string>
    <string name="joywork_project_setting_filter_cur">Due this week</string>
    <string name="joywork_project_setting_filter_last">Due next week</string>
    <string name="joywork_project_setting_filter_custom">Custom filter</string>
    <string name="joywork_project_setting_filter_status">Status</string>
    <string name="joywork_project_todo_progress">Progress</string>
    <string name="joywork_project_button_move_to">Move to</string>
    <string name="joywork_project_button_transfer">Transfer</string>
    <string name="joywork_project_button_delete">Delete</string>
    <string name="joywork_project_button_reject">Reject</string>

    <string name="joywork_project_setting_sort_owner">Assignees</string>
    <string name="joywork_project_setting_sort_time">Due date</string>
    <string name="joywork_project_setting_sort_in_group">Sort within sections</string>
    <string name="joywork_list_go_look">View</string>
    <string name="joywork_project_setting_title">More</string>
    <string name="joywork_project_create_time">CreateTime</string>
    <string name="joywork_project_creator">Creator</string>

    <string name="joywork_project_setting_sort_finish">Completion date</string>
    <string name="joywork_project_sort_no">—</string>
    <string name="joywork_project_none">None</string>

    <string name="joywork_snackbar_to_detail">View details</string>
    <string name="joywork_snackbar_tips">Created successfully</string>
    <string name="joywork_create_dialog_tips">Discard unsaved changes？</string>
    <string name="joywork_delete_button_dialog_title">Do you want to delete the task?</string>
    <string name="joywork_delete_button_dialog_sub_message">It will not be retrieved if it is deleted successful.</string>
    <string name="joywork_delete_button_dialog_confirm">Delete</string>

    <string name="joywork_merge">Merge duplicate tasks</string>
    <string name="joywork_merge_cancel">Unmark as duplicate</string>
    <string name="joywork_sub">Transform to subtask</string>
    <string name="joywork_parent">Change parent task</string>
    <string name="joywork_detail_merge_tips">Closed and merged into</string>
    <string name="joywork_cancel_merge_tips">After canceling the merge, the likes and followers will be restored, and the subtasks do not support restoration for the time being</string>
    <string name="joywork_merge_tips">After the task is merged, the task will be closed, and its followers, likes, and subtasks will be added to the merged task</string>
    <string name="joywork_merge_special_tips">The target task cannot be its subtask</string>

    <string name="joywork_time_format1">M/d</string>
    <string name="joywork_dialog_ok">I got it</string>

    <bool name="zh">false</bool>
    <string name="joywork_group_title_mine">Assigned to me</string>
    <string name="joywork_group_title_other">Others</string>
    <string name="joywork_group_title">Group tasks</string>
    <string name="joywork_group_robot">Robot</string>

    <string name="joywork_duplicate_no">Never</string>
    <string name="joywork_duplicate_worktime">Working day</string>
    <string name="joywork_duplicate_everyday">Daily</string>
    <string name="joywork_duplicate_everyweek">Weekly</string>
    <string name="joywork_duplicate_every_month">Monthly</string>
    <string name="joywork_duplicate_every_year">Yearly</string>
    <string name="joywork_duplicate_text">Set repeat</string>
    <string name="joywork_urge_members_title">Urge the following people</string>
    <string name="joywork_urge_def_text">Please handle the task as soon as possible</string>
    <string name="joywork_create_subjoywork">Create new subtasks in batches</string>
    <string name="joywork_input_parent">Click “Import” to copy parent task information</string>
    <string name="joywork_input">Import</string>
    <string name="joywork_subjoywork_title">Subtasks (%s Completed)</string>
    <string name="joywork_create_avatar_empty">Select multiple employees and create an identical subtask for each of them</string>
    <string name="joywork_more_owner">Assignees</string>
    <string name="joywork_owner_empty">Unassigned</string>
    <string name="joywork_next">Next</string>
    <string name="joywork_urge_select">Select Members</string>
    <string name="joywork_create_subjoywork_success">Successfully created %s subtasks</string>
    <string name="joywork_dup_work_finish_tips">Completed, the system has automatically generated the next task</string>
    <string name="joywork_self_overdue">Overdue</string>
    <string name="joywork_self_item_project">List: %s</string>
    <string name="joywork_tabbar_title_issue">Risks</string>
    <string name="joywork_risk_normal">Normal</string>
    <string name="joywork_risk_problem">Issue</string>
    <string name="joywork_risk_risk">Risk</string>
    <string name="joywork_risk_hint">Report risk</string>
    <string name="joywork_risk_msg_normal_tips">Please describe the current task status and task progress</string>
    <string name="joywork_risk_msg_normal_title">Description of task status</string>

    <string name="joywork_risk_msg_problem_tips">Fill in the issue description and solution</string>
    <string name="joywork_risk_msg_problem_title">Issue Description</string>

    <string name="joywork_risk_msg_risk_tips">Fill in the risk description and solution</string>
    <string name="joywork_risk_msg_risk_title">Risk Description</string>
    <string name="joywork_my_tasks">My Tasks</string>
    <string name="joywork_detail_discuss">Comment</string>
    <string name="joywork_detail_update">Update progress</string>
    <string name="joywork_detail_solve">Resolve</string>
    <string name="joywork_detail_launch">Add schedule time</string>
    <string name="joywork_detail_risk_next_week">Next Monday</string>
    <string name="jowork_risk_detail_prefix">Risk Description：%s</string>
    <string name="jowork_problem_detail_prefix">Issue Description：%s</string>
    <string name="joywork_resolve_title">Solution</string>
    <string name="joywork_resolve_hint">Fill in the solution</string>

    <string name="joywork_shortcut_other">Other</string>
    <string name="joywork_create_owner">%s Execute</string>
    <string name="joywork_create_owner_text">%d assignees</string>
    <string name="joywork_create_relation_hint">Add follower</string>
    <string name="joywork_create_relation_text">%s followers</string>
    <string name="joywork_create_relation_text2">%s Follow</string>
    <string name="joywork_create_dup_tips">To set up a cycle task, you need to set the task due date first</string>
    <string name="joywork_shortcut_more">More</string>
    <string name="joywork_detail_risk_update">Update</string>
    <string name="me_joywork_plantime_normal">%s Exec</string>
    <string name="joywork_executor">Assignee</string>
    <string name="joywork_repeat_tips">%s Repeat</string>
    <string name="joywork_detail_history">History</string>
    <string name="joywork_detail_priority">Priority</string>
    <string name="joywork_detail_more">More options</string>

    <string name="joywork_detail_urgy_icon">@string/icon_general_cuiban</string>
    <string name="joywork_priority_p1">P1</string>
    <string name="joywork_priority_p2">P2</string>
    <string name="joywork_priority_p3">P3</string>
    <string name="joywork_priority_p4">P4</string>
    <string name="joywork_priority_High">High</string>
    <string name="joywork_priority_Medium">Medium</string>
    <string name="joywork_priority_Low">Low</string>
    <string name="joywork_priority_None">None</string>

    <string name="joywork_urgy">Urge</string>
    <string name="joywork_mark_finish">Mark as completed</string>
    <string name="joywork_remove_owner">Remove assignee</string>
    <string name="joywork_mark_unfinish">Mark as uncompleted</string>

    <string name="joywork_finish_only_me">I\'ve completed</string>
    <string name="joywork_restore_only_me">Reopen myself</string>
    <string name="joywork_finish_all">Complete all</string>
    <string name="joywork_restore_all">Reopen all</string>
    <string name="joywork_finish_task">Completed</string>
    <string name="joywork_restore_task">Reopen</string>
    <string name="joywork_executor_full">No more than %d members</string>

    <string name="joywork_issue_tips">The risks, issues and overdue tasks will be displayed here， you can deal with  them in a timely manner</string>
    <string name="joywork_send_dd">Send msg to the chat</string>
    <string name="joywork_issue_empty">Great! There is no risks, issues and overdue tasks.</string>
    <string name="joywork_detail_owners">%s Completed</string>
    <string name="joywork_detail_owners_short">%s COMP</string>

    <string name="joywork_unfouce_title">Confirm to unfollow？</string>
    <string name="joywork_unfouce_msg">Unfollow will not receive reminders of task progress</string>
    <string name="joywork_transfer">Transfer task</string>
    <string name="joywork_transfer_success">Transfer task successfully</string>
    <string name="joywork_transfer_failure">Transfer task failure</string>
    <string name="joywork_delete_failure">Delete failure</string>
    <string name="joywork_move_success">Move successfully</string>
    <string name="joywork_move_failure">Move failure</string>
    <string name="joywork_reject_success">Reject successfully</string>
    <string name="joywork_reject_failure">Reject failure</string>
    <string name="joywork_last_week">This week</string>
    <string name="joywork_this_week">Last week</string>
    <string name="joywork_finish_suffix">%s Completed</string>
    <string name="joywork_cancel_chief">Cancel the owner</string>
    <string name="joywork_set_chief">Mark as owner</string>
    <string name="joywork_cancel_chief_tips">Do you want to cancel the current owner?</string>
    <string name="joywork_set_chief_tips">The owner %s already exists for the current task, do you want to reset a new owner?</string>
    <string name="joywork_self_no_schedule">Unscheduled</string>
    <string name="joywork_self_future">Later</string>
    <string name="joywork_cc">Cc:</string>
    <string name="joywork_cc_num">%d members</string>
    <string name="joywork_cc_title">Cc</string>
    <string name="joywork_send_chat">Send by Message</string>
    <string name="joywork_focus">Followed</string>
    <string name="joywork_notify">Send</string>
    <string name="joywork_update_title">Task name</string>
    <string name="joywork_update_desc">Background and objectives</string>
    <string name="joywork_update_start">Start date</string>
    <string name="joywork_update_finish">Completion date</string>
    <string name="joywork_update_deadline">Due date</string>
    <string name="joywork_update_priority">Priority</string>
    <string name="joywork_update_dup">Repeat</string>
    <string name="joywork_update_text">Task information has changed：%s</string>
    <string name="joywork_update_dialog">Task information has changed：%s。\nNotify task creator and assignees?</string>
    <string name="joywork_no_send">Don\'t send</string>
    <string name="joywork_update_dialog_title">Send update notification?</string>

    <string name="joywork_finish_all_msg">Mark as ‘completed all’？All assignees will be marked as completed.</string>
    <string name="joywork_finish_task_msg">Mark as ‘completed ’？All assignees will be marked as completed.</string>
    <string name="joywork_restore_all_msg">Reopen task？Once this task is reopened，it will be reopened for all assignees.</string>
    <string name="joywork_restore_task_msg">Reopen all？Once this task is reopened，it will be reopened for all assignees.</string>
    <string name="joywork_finish_sure">Complete</string>
    <string name="joywork_finish_cancel">Cancel</string>
    <string name="joywork_finish_hint">(Optional) Fill in the task completion instructions</string>
    <string name="joywork_finish_all_tips">After complete ，all assignees will be marked as completed.</string>

    <string name="joywork_select_title">Add assignee</string>
    <string name="joywork_select_outer">Out-of-group members</string>
    <string name="joywork_cur_group">Current group members</string>
    <string name="joywork_select_group_count">Selected: %d/%d</string>
    <string name="joywork_clear">Clear</string>
    <string name="joywork_select_group_title">Selected(%d)</string>
    <string name="joywork_project_custom">Custom List</string>
    <string name="joywork_project_default"> Default List</string>
    <string name="joywork_load_more">Loading More</string>
    <string name="joywork_share">Share</string>
    <string name="joywork_update">Edit</string>
    <string name="joywork_task_link_error">Associate failed</string>
    <string name="joywork_kpi_target_empty">No Objective</string>
    <string name="joywork_del_target_title">Delete this objective?</string>
    <string name="joywork_del_target_msg">After deleting，objective content will be lost，tasks will dissociate from objective.</string>
    <string name="joywork_like">Like(%d)</string>
    <string name="joywork_new_target">Add an Objective</string>
    <string name="joywork_update_target">Update Objective</string>
    <string name="joywork_target_hint">\'Continue to...\' is no good; try \'Achieve\' , And be challenging, come on!</string>
    <string name="joywork_cmn_error">Failed to get data, please try again</string>
    <string name="joywork_continue_edit">Continue to edit</string>
    <string name="joywork_select_goal">Choose performance goals</string>
    <string name="joywork_weekly_summary_tips">Choose performance goals that need to sync progress in weekly report</string>
    <string name="joywork_weekly">Week %2$d, %1$d</string>
    <string name="joywork_weekly_location">Save to</string>
    <string name="joywork_private_dir">Private Space</string>
    <string name="joywork_check_space">Getting directory data</string>
    <string name="joywork_weight">Weight %s</string>
    <string name="joywork_weekly_summary_icon">@string/icon_work_weeklyen</string>
    <string name="joywork_kr_create_title">Add Key Result</string>
    <string name="joywork_kr_edit_title">Edit Key Result</string>
    <string name="joywork_kr_tag">KR</string>
    <string name="joywork_link_target">Associate objective/Key Result</string>
    <string name="joywork_del_kr_title">Are you sure to delete this Key Result?</string>
    <string name="joywork_del_kr_msg">Weekly Summary will be lost after deleting KR, are you sure to delete?</string>
    <string name="joywork_task_count">Key Tasks(%d)</string>
    <string name="joywork_summary_count2">Weekly Summary(%d)</string>
    <string name="joywork_other_target_empty">No goal set or undisclosed goal yet～</string>
    <string name="joywork_objective">Objective</string>

    <string name="joywork_visible_department">Visible Department</string>
    <string name="joywork_get_date_error">Failed to get data</string>
    <string name="joywork_quit_setting_msg">Confirm to discard unsaved changes？</string>
    <string name="joywork_create_owner_hint">Add owner and assignees</string>
    <string name="joywork_org_list_sub">Direct subordinates</string>
    <string name="joywork_followed_cancel">Cancelled</string>
    <string name="joywork_set_roler">Assignees</string>
    <string name="joywork_max_user">Limit %d people</string>
    <string name="joywork_supervisor_visible">Only visible to superiors</string>
    <string name="joywork_department_empty">No matching results</string>
    <string name="joywork_setting_success">Set successfully</string>
    <string name="joywork_set_owner">Set as owner</string>
    <string name="joywork_org_list_super">Direct superior</string>
    <string name="joywork_followed_cancel_msg">Whether to unfollow?</string>
    <string name="joywork_followed">Followed</string>
    <string name="joywork_quit_setting_title">Exit edit</string>
    <string name="joywork_all_visible">Visible to everyone</string>
    <string name="joywork_visible_people_getting">Getting settings information</string>
    <string name="joywork_added">Added</string>
    <string name="joywork_retry">Please try again</string>
    <string name="joywork_org_list_title">Person List</string>
    <string name="joywork_set_owner_cancel">Cancel the owner</string>
    <string name="joywork_select_executor_title">Choose assignees</string>
    <string name="joywork_set_visible">Public Settings</string>
    <string name="joywork_create_owner_text2">%d assignees</string>
    <string name="joywork_create_owner_text_short">%d assignees</string>
    <string name="joywork_department_init">Search department name</string>
    <string name="joywork_part_visible">Visible to some people and department</string>
    <string name="joywork_followed_list_empty">Haven\'t followed other people\'s goals yet~</string>
    <string name="joywork_visible_user_title">Visible people</string>
    <string name="joywork_owner2">&#160;Own</string>
    <string name="joywork_create_owner2">&#160;Execute</string>
    <string name="joywork_comment">Comment</string>
    <string name="joywork_comment2">Comments(%d)</string>
    <string name="joywork_progress">Progress %s</string>
    <string name="joywork_share_failure">Sharing failed,please try again</string>

    <string name="joywork_alert_no">No remind</string>
    <string name="joywork_alert_15m">15 minutes before</string>
    <string name="joywork_alert_30m">30 minutes before</string>
    <string name="joywork_alert_1h">1 hour before</string>
    <string name="joywork_alert_2h">2 hours before </string>
    <string name="joywork_alert_1d">1 day before</string>
    <string name="joywork_alert_2d">2 days before</string>
    <string name="joywork_alert_1w">1 week before</string>
    <string name="joywork_alert_s">total %d reminders</string>
    <string name="joywork_alert_s2">%s Remind</string>
    <string name="joy_work_remind">Reminder time</string>
    <string name="joywork_alert_tips">Remind uncompleted members(multiple choice)</string>
    <string name="joywork_alert_tips2">Remind uncompleted members</string>
    <string name="joywork_alert">Setting Remind</string>
    <string name="joywork_alert_deadline">To set reminders,you need to set the task due date first</string>
    <string name="joywork_search_init">Enter the title, background, assignees to search for tasks</string>

    <string name="joywork_project_des_hint">Add collaboration description</string>
    <string name="joywork_project_des_tip2">How we\'ll collaborate：</string>
    <string name="joywork_more_example">Show examples</string>
    <string name="joywork_go_chat">Go to Message</string>
    <string name="joywork_project_des_tip">How we\'ll collaborate</string>
    <string name="joywork_list_empty_tips">You can use "List" to keep track of important projects, meetings, events, and more. At the same time, you can add others to the list, so that all work plans and progress can be efficiently synchronized among members and promote collaboration.</string>
    <string name="joywork_create">Create</string>
    <string name="joywork_clean">Clear</string>

    <string name="joywork_saved">Saved</string>
    <string name="joywork_save_setting">Save settings</string>
    <string name="joywork_view_save_tips">Current settings are saved by %s</string>
    <string name="joywork_save_view_title">Do you want to save the filter？</string>
    <string name="joywork_save_view_content">After saving, it will take effect on all list members. Are you sure to save?</string>
    <string name="joywork_view_clear_title">Do you want to clear the filter？</string>
    <string name="joywork_view_clear_tips">After clearing, it will take effect on all list members. Are you sure to clear?</string>
    <string name="joywork_notification_center">Notifications</string>
    <string name="joywork_my_goal">My Goals</string>
    <string name="joywork_default">Default</string>
    <string name="joywork_shrink">Fold</string>
    <string name="joywork_expand">Unfold</string>
    <string name="joywork_notifiy_empty">You have not received the task notification</string>
    <string name="joywork_all_read">Mark all as read</string>
    <string name="joywork_all_read_success">Mark read successfully</string>
    <string name="joywork_self_order">Team list</string>
    <string name="joywork_sort_custom_group">Custom group</string>
    <string name="joywork_next_7d">In seven days</string>
    <string name="joywork_my_create">I Created</string>
    <string name="joywork_self_default_group">Default Group</string>
    <string name="joywork_new2">New</string>
    <string name="joywork2_pull_load_more">Pull Up To Load More</string>
    <string name="joywork_update_dup2">Recur</string>
    <string name="joywork2_sorter_create_time">Create Date</string>
    <string name="joywork2_menu_shortcut">Custom View</string>
    <string name="joywork2_started">Ongoing</string>
    <string name="joywork2_grouper_title">Group</string>
    <string name="joywork2_grouper_source">Create from</string>
    <string name="joywork2_grouper_primary_source">Source Category 1</string>
    <string name="joywork2_grouper_secondary_source">Source Category 2</string>
    <string name="joywork2_grouper_latest_comment">Progress Update Time</string>
    <string name="joywork2_grouper_no">None</string>
    <string name="joywork2_grouper_text">Group:%s</string>
    <string name="joywork2_sorter_text">Sort:%s</string>
    <string name="joywork2_sorter_custom">Custom</string>
    <string name="joywork_update_comment_date">yyyy/M/d</string>

    <string name="joywork_project_selector">List</string>
    <string name="joywork_project_title_prefix">List:</string>
    <string name="joywork_project_selector_tips">It can be automatically intergrated if you bind the task list to this group. And, the AI task assistant will follow up the progress of each item.</string>
    <string name="joywork2_grouper_latest_comment_sort">Progress Update Time</string>

    <string name="joywork2_show_type_list">List</string>
    <string name="joywork2_show_type_card">Card</string>
    <string name="joywork2_show_new_task">New Task</string>

    <string name="joywork2_show_type_calendar">Calendar</string>
    <string name="joywork2_show_type_kanban">Kanban</string>
    <string name="joywork2__choose_end_date_tips">Please add the due date</string>
    <string name="joywork2_associated_project_number">%1$d listings linked</string>
    <string name="joywork2_add">Add</string>
    <string name="joywork2_archive_project">Completed List</string>
</resources>
