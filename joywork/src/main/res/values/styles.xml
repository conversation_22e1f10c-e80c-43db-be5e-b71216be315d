<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppTheme" parent="Theme.AppCompat.Light" />

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="JoyWorkAttachmentDialogItemStyle">
        <item name="android:textColor">#333333</item>
        <item name="android:textSize">18dp</item>
    </style>

    <style name="JoyWorkDialogItemHighlightStyle">
        <item name="android:textColor">@color/joywork_red</item>
        <item name="android:textSize">18dp</item>
    </style>

    <declare-styleable name="DividerLinearLayout">
        <!--        是否在上边显示分隔线-->
        <attr name="dividerTop" format="boolean" />
        <!--        是否在下边显示分隔线-->
        <attr name="dividerBottom" format="boolean" />
        <!--        分隔线的起始 view，以它的 Left 为起始位置-->
        <attr name="dividerStartId" format="reference" />
    </declare-styleable>

    <style name="JoyWorkUrgeOwnerTP">
        <item name="android:textColor">#232930</item>
        <item name="android:textSize">16dp</item>
    </style>

    <style name="TabLayoutTextStyle">
        <item name="android:textSize">14dp</item>
    </style>

    <style name="JoyWorkListItemAvatarHint">
        <item name="android:textColor">#666666</item>
        <item name="android:textSize">12dp</item>
    </style>

    <declare-styleable name="JoyWorkOffsetLayout">
        <attr name="offset" format="dimension" />
        <attr name="reverseDrawingOrder" format="boolean" />
    </declare-styleable>
    <declare-styleable name="HorizontalPercentLinearLayout">
        <!--        需要进行大小限制的子 view 下标，如果有多个用 , 隔开-->
        <attr name="childrenIndex" format="string" />
    </declare-styleable>

    <style name="TabStyle">
        <item name="tabIndicator">@drawable/joywork2_tab_indicator</item>
        <item name="tabIndicatorColor">@color/white</item>
        <item name="tabIndicatorGravity">center</item>
        <item name="tabIndicatorHeight">28dp</item>
        <item name="tabMinWidth">10dp</item>
        <!--        此值只要足够大即可，不会文案太长会显示不全-->
        <item name="tabMaxWidth">100000dp</item>
        <item name="tabMode">scrollable</item>
        <item name="tabBackground">@null</item>
        <item name="tabRippleColor">@null</item>
    </style>

    <style name="tabNormalTextStyles">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/joywork_tab_item_color_sel</item>
    </style>
</resources>
