<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="TaskAttachmentView">
        <attr name="model" format="enum">
            <enum name="edit" value="1" />
            <enum name="show" value="2" />
        </attr>
    </declare-styleable>
    <item name="jdme_tag_id" type="id" />
    <item name="expand_rv_tag_id" type="id" />

    <declare-styleable name="SwipeMenuLayout">
        <attr name="leftViewId" format="reference|integer" />
        <attr name="rightViewId" format="reference|integer" />
        <attr name="contentViewId" format="reference|integer" />
    </declare-styleable>
    <declare-styleable name="DetailTimeView">
        <attr name="needIcon" format="boolean" />
        <attr name="hintResId" format="reference" />
    </declare-styleable>

    <declare-styleable name="BubbleView">
        <attr name="numViewBorderColor" format="reference|color" />
        <attr name="numViewBorderWidth" format="reference|dimension" />
        <attr name="numViewBgColor" format="reference|color" />
        <!--        显示非圆形时，水平方向上额外的 padding-->
        <attr name="extraPaddingH" format="dimension|reference" />
        <!--        显示非圆形时，水平方向上额外的 padding 高度的百分比-->
        <attr name="extraPaddingPercent" format="float" />
    </declare-styleable>

    <declare-styleable name="JoyWorkAvatarView">
        <!--        没有头像时显示内容-->
        <attr name="emptyLayout" format="reference" />
        <!--        最多显示几个头像，剩余人数会收缩至气泡中-->
        <attr name="maxAvatar" format="integer" />
        <!--        头像尺寸 单位 dp-->
        <attr name="avatarSize" format="reference|dimension" />
        <!--        最多有几个 item，其优先级低于 maxAvatar。不足时，全部显示；+1 够时不显示气泡；否则，显示 -1 个头像及 1 个气泡-->
        <attr name="maxChildCount" format="integer" />
        <!--        省略号的文字大小-->
        <attr name="ellipsizeTextSize" format="dimension|reference" />
        <!--        是否永久显示说明文字，其优先级高于 hintTextThreshold-->
        <attr name="alwaysShowHintText" format="boolean" />
        <!--        不足几人时(含)，显示文字说明-->
        <attr name="hintTextThreshold" format="integer" />
        <!--        提示文字的样式-->
        <attr name="hintTextAppearance" />
        <!--        帽子大小-->
        <attr name="hatSize" format="dimension|reference" />
        <!--        帽子与头像间距，计算时会自动加上头像尺寸的一半-->
        <attr name="hatPaddingExtra" format="dimension|reference" />
        <!--        头像是否重叠显示，默认为 true-->
        <attr name="avatarOverlap" format="boolean" />

        <!--        右下角图标的大小-->
        <attr name="iconSize" format="dimension|reference" />
        <!--        右下角图标的显示-->
        <attr name="iconRes" format="reference" />
        <!--头像是否显示省略号，不显示更多则显示数字-->
        <attr name="showMore" format="boolean" />
        <!--头像是否显示白色边框-->
        <attr name="showBorder" format="boolean" />
    </declare-styleable>
    <declare-styleable name="AutoSizeTextView">
        <!--        如果当前设置的 textSize 无法显示完全，就会自动调整到 backupTextSize-->
        <attr name="backupTextSize" format="dimension|reference" />
    </declare-styleable>

    <declare-styleable name="SeparatorLinearLayout">
        <attr name="separator" format="dimension|reference" />
    </declare-styleable>

    <style name="ProgressBarTheme" parent="ThemeOverlay.AppCompat.Light">
        <item name="colorAccent">@color/joywork_red</item>
    </style>
</resources>
