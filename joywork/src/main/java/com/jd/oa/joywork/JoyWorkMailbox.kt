package com.jd.oa.joywork

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager

private val riskUpdateAction = "joywork.update.risk"

fun notifyRiskUpdateWhenEndTimeUpdate(context: Context, current: Long?, old: Long?) {
    if (old == null) {
        if (current != null &&  current < System.currentTimeMillis()) {
            context.notifyRiskUpdate()
        }
    } else {
        if (old > System.currentTimeMillis() && (current != null && current < System.currentTimeMillis())) {
            context.notifyRiskUpdate()
        }
        if (old < System.currentTimeMillis() && (current != null && current > System.currentTimeMillis())) {
            context.notifyRiskUpdate()
        }
    }
}

fun Context.notifyRiskUpdate() {
    val intent = Intent(riskUpdateAction)
    LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
}

infix fun BroadcastReceiver.observeRiskUpdate(context: Context) {
    val intent = IntentFilter(riskUpdateAction)
    LocalBroadcastManager.getInstance(context).registerReceiver(this, intent)
}

infix fun BroadcastReceiver.stopObserveRiskUpdate(context: Context) {
    LocalBroadcastManager.getInstance(context).unregisterReceiver(this)
}