package com.jd.oa.joywork.utils

import android.content.Intent
import android.os.Bundle
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.router.DeepLink
import org.json.JSONObject
import java.net.URLDecoder

fun Intent?.getParamsStr(): String? {
    return this?.getStringExtra(DeepLink.DEEPLINK_PARAM)
}

fun Intent?.getDeeplinkRawString(): String? {
    return this?.getStringExtra("raw_uri")
}

fun Intent.getParamsKey(key: String, dv: String = ""): String {
    return try {
        val mparam =
            getStringExtra(DeepLink.DEEPLINK_PARAM) ?: getStringExtra(WebFragment2.BIZ_PARAM)
        val decodedParam = URLDecoder.decode(mparam,"utf-8")
        JSONObject(decodedParam).getString(key) ?: dv
    } catch (e: Exception) {
        dv
    }
}

fun Bundle?.getParamsKey(key: String, dv: String = ""): String {
    if (this == null)
        return dv
    return try {
        val param = getString(DeepLink.DEEPLINK_PARAM) ?: getString(WebFragment2.BIZ_PARAM)
        val decodedParam = URLDecoder.decode(param,"utf-8")
        JSONObject(decodedParam).getString(key) ?: dv
    } catch (e: Exception) {
        dv
    }
}