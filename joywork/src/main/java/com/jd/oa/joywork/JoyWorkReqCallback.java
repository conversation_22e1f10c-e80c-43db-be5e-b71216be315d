package com.jd.oa.joywork;

import com.jd.oa.AppBase;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.reponse.Response;
import com.jd.oa.network.legacy.ResponseInfo;

import org.json.JSONObject;

import java.util.List;

public class JoyWorkReqCallback<T> extends AbsReqCallback<T> {

    protected JoyWorkReqCallback(Class<T> clazz) {
        super(clazz);
    }

    @Override
    public void onSuccess(ResponseInfo<String> responseInfo) {
        try {
            Response<T> response = Response.getResponse(clazz, responseInfo.result);
            T obj = response.getDataObj();
            List<T> list = response.getDataList();
            if (response.isOk()) {
                onSuccess(obj, list, responseInfo.result);
            } else {
                onFailure(response.getMessage(), new JSONObject(responseInfo.result).getInt("errorCode"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            onFailure(AppBase.getAppContext().getString(R.string.me_data_parse_error), ErrorCode.CODE_PARSE_ERROR);
        }
    }
}
