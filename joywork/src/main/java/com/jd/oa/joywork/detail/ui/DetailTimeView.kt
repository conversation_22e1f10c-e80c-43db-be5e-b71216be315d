package com.jd.oa.joywork.detail.ui

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.Gravity
import android.widget.LinearLayout
import android.widget.TextView
import com.jd.oa.utils.*
import com.jd.oa.joywork.R

class DetailTimeView(context: Context, attributeSet: AttributeSet) :
    LinearLayout(context, attributeSet) {
    val bg by lazy {
        DrawableEx.roundStrokeRect(
            Color.parseColor("#DEE0E3"),
            CommonUtils.dp2px(0.5f),
            resources.getDimension(R.dimen.joywork_corner_small)
        )
    }
    val close by lazy { findViewById<TextView>(R.id.icon) }
    val tv by lazy { findViewById<TextView>(R.id.alert_content) }
    val tvHint by lazy { findViewById<TextView>(R.id.alert_content_hint) }

    val hp by lazy { CommonUtils.dp2px(12.0f) }

    var candidateVh = 0

    val vp: Int
        get() {
            return if (needIcon) {
                candidateVh
            } else {
                CommonUtils.dp2px(7.0f)
            }
        }

    var closeListener: OnClickListener? = null
    var needIcon: Boolean = true

    init {
        context.inflater.inflate(R.layout.joywork_alert_time_view, this, true)
        orientation = HORIZONTAL
        gravity = Gravity.CENTER_VERTICAL
        val mTypedArray = context.obtainStyledAttributes(attributeSet, R.styleable.DetailTimeView)
        val resId = mTypedArray.getResourceId(R.styleable.DetailTimeView_hintResId, -1)
        if (resId > 0) {
            setHint(resId)
        }
        needIcon = mTypedArray.getBoolean(R.styleable.DetailTimeView_needIcon, true)
        if (needIcon) {
            close.visible()
        } else {
            close.gone()
        }
        mTypedArray.recycle()
    }

    fun setHint(id: Int) {
        tvHint.setText(id)
    }

    fun setText(txt: String?) {
        if (txt.isBlankOrNull()) {
            showEmpty()
        } else {
            tvHint.gone()
            tv.visible()
            setPadding(hp, vp, hp, vp)
            tv.text = txt
            if (needIcon) {
                close.visible()
            }
            closeListener?.apply {
                close.setOnClickListener(this)
            }
            background = DrawableEx.roundStrokeRect(
                Color.parseColor("#DEE0E3"),
                CommonUtils.dp2px(0.5f),
                resources.getDimension(R.dimen.joywork_corner_small)
            )
        }
    }

    fun setTextColor(colorRes: Int) {
        tv.setTextColor(resources.getColor(colorRes))
    }

    private fun showEmpty() {
        tv.gone()
        tvHint.visible()
        setPadding(0, 0, 0, 0)
        close.gone()
        background = null
    }
}