package com.jd.oa.joywork.common

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.R
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.getBatchInfo
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.view.red
import com.jd.oa.utils.gone
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

open class UserListFragment : BaseFragment() {

    companion object {
        fun getMemberInstance(): UserListFragment {
            val f = UserListFragment()
            f.arguments = Bundle()
            f.arguments?.putString("type", "member")
            return f
        }

        fun getJoyWorkUserInstance(): UserListFragment {
            val f = UserListFragment()
            f.arguments = Bundle()
            f.arguments?.putString("type", "user")
            return f
        }
    }

    protected lateinit var mStrategy: IUserListStrategy
    var mItemCallback: IUserListItemCallback? = null
    private lateinit var rv: RecyclerView
    private val scope = MainScope()
    private var mAdapter: ExecutorsListAdapter? = null
    protected var mSwipeRefreshLayout: SwipeRefreshLayout? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        rv = RecyclerView(requireContext())
        rv.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        mAdapter = ExecutorsListAdapter(requireContext(), ArrayList())
        rv.adapter = mAdapter
        setupRv(rv)
        val type = arguments?.get("type")
        mStrategy = when (type) {
            "user" -> JoyWorkUserListStrategy()
            else -> MemberListStrategy()
        }
        mItemCallback = mItemCallback ?: mStrategy
        if (refreshable()) {
            val swipeRefreshLayout = SwipeRefreshLayout(requireContext())
            swipeRefreshLayout.addView(rv)
            setupSwipeRefresh(swipeRefreshLayout)
            swipeRefreshLayout.setOnRefreshListener {
                onRefresh()
            }
            swipeRefreshLayout.red(requireActivity())
            mSwipeRefreshLayout = swipeRefreshLayout
            return swipeRefreshLayout
        } else {
            return rv
        }
    }

    protected open fun setupSwipeRefresh(swipeRefreshLayout: SwipeRefreshLayout) {

    }

    protected open fun setupRv(recyclerView: RecyclerView) {

    }

    protected open fun onRefresh() {

    }

    protected fun getItemCount(): Int {
        return mAdapter?.itemCount ?: 0
    }

    protected fun refreshUI() {
        mAdapter?.notifyDataSetChanged()
    }

    private fun getInfo() {
        val adapter = mAdapter
        if (adapter != null) {
            val data = adapter.data
            val needNet = mStrategy.getNeedInfoUsers(data)
            // 过滤掉已有部门信息的人员
            if (!needNet.isLegalList()) {
                return
            }
            scope.launch {
                val r = getBatchInfo(needNet)
                r.ifSuccessSilence {
                    if (it?.users.isLegalList()) {
                        adapter.updateInfo(it?.users ?: ArrayList<Members>())
                    }
                }
            }
        }
    }

    protected fun forEachItem(callback: (Any) -> Unit) {
        mAdapter?.data?.forEach {
            callback(it)
        }
    }

    fun showEmpty(@StringRes emptyStr: Int? = null, @DrawableRes emptyDrawable: Int? = null) {
        rv.adapter = JoyWorkEmptyAdapter.empty(
            requireContext(),
            emptyStr ?: mStrategy.getEmptyStr(),
            emptyDrawable ?: mStrategy.getEmptyDrawable()
        )
        mAdapter = null
    }

    fun showEmpty(emptyStr: String, @DrawableRes emptyDrawable: Int? = null) {
        rv.adapter = JoyWorkEmptyAdapter.empty(
            requireContext(),
            emptyStr,
            emptyDrawable ?: mStrategy.getEmptyDrawable()
        )
        mAdapter = null
    }

    fun replaceData(data: List<Any>) {
        val adapter = mAdapter
        if (data.isEmpty() && adapter != null) {
            adapter.clear()
            showEmpty()
        } else if (data.isNotEmpty()) {
            if (adapter != null) {
                adapter.replace(data)
            } else {
                val tmp = ArrayList<Any>()
                tmp.addAll(data)
                mAdapter = ExecutorsListAdapter(requireContext(), tmp)
                rv.adapter = mAdapter
                setupRv(rv)
            }
            getInfo()
        }
    }

    /**
     * 向列表中追加数据
     */
    fun appendData(data: List<Any>) {
        if (data.isEmpty()) {
            return
        }
        val adapter = mAdapter
        if (adapter != null) {
            adapter.appendData(data)
        } else {
            val tmp = ArrayList<Any>()
            tmp.addAll(data)
            mAdapter = ExecutorsListAdapter(requireContext(), tmp)
            rv.adapter = mAdapter
            setupRv(rv)
        }
        getInfo()
    }

    fun remove(any: Any) {
        mAdapter?.remove(any)
    }


    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }

    protected open fun refreshable(): Boolean {
        return false
    }

    inner class ExecutorsListAdapter(val context: Context, dataC: ArrayList<Any>) :
        RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        val data = ArrayList<Any>(dataC)

        fun remove(any: Any) {
            val r = mStrategy.remove(data, any)
            if (r) {
                notifyDataSetChanged()
            }
        }

        fun updateInfo(members: List<Members>) {
            val r = mStrategy.merge(data, members)
            if (r) {
                notifyDataSetChanged()
            }
        }

        fun appendData(data: List<Any>) {
            if (data.isEmpty()) {
                return
            }
            this.data.addAll(data)
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            val ans = mItemCallback?.onCreateViewHolder(parent, viewType)
            if (ans != null) {
                return ans
            }
            val inflate: View = LayoutInflater.from(context)
                .inflate(R.layout.joywork_collaborator_activity_item, parent, false)
            return VH(inflate)
        }

        override fun getItemCount() = data.size

        override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, position: Int) {
            val c = data[position]
            if (mItemCallback?.onBindViewHolder(viewHolder, position, c).isTrue()) {
                return
            }
            val holder = viewHolder as VH
            holder.name.text = mStrategy.getTitleName(c)
            holder.department.text = mStrategy.getSubTitleText(c)

            holder.divider.visibility = if (position == data.size - 1) View.GONE else View.VISIBLE

            holder.action.tag = c
            mItemCallback?.bindItemAction(holder.action)

            holder.avatar.setTag(R.id.tag_key_data, c)
            mStrategy.bindAvatar(holder.avatar)

            holder.itemView.tag = c
            mItemCallback?.bindItem(holder.itemView)
            holder.hat.gone()
        }

        fun clear() {
            data.clear()
            notifyDataSetChanged()
        }

        fun replace(data: List<Any>) {
            this.data.clear()
            this.data.addAll(data)
            notifyDataSetChanged()
        }
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView = view.findViewById(R.id.image)
        val name: TextView = view.findViewById(R.id.name)
        val action: TextView = view.findViewById(R.id.action)
        val department: TextView = view.findViewById(R.id.department)
        val divider: View = view.findViewById(R.id.divider)
        val hat: ImageView = view.findViewById(R.id.hat)
    }
}
