package com.jd.oa.joywork.create

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.utils.string
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * 新建时，负责人列表
 */
class CreateOwnerListActivity : AppCompatActivity() {
    private lateinit var rv: RecyclerView

    private val scope = MainScope()

    companion object {

        private fun getExecutors(): ArrayList<JoyWorkUser> {
            return (OneTimeDataRepo.data as? List<*>)?.run {
                return if (isEmpty()) {
                    ArrayList()
                } else {
                    if (first() is JoyWorkUser) {
                        ArrayList(this as List<JoyWorkUser>)
                    } else {
                        ArrayList()
                    }
                }
            } ?: ArrayList()
        }
    }

    private val members: ArrayList<JoyWorkUser> by lazy {
        getExecutors()
    }

    /**
     * 最终返回给调用者的用户集合
     */
    private val result: ArrayList<JoyWorkUser> = ArrayList()
    private val titleView: TextView by lazy {
        findViewById<TextView>(R.id.title)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        result.addAll(members)
        kotlin.runCatching {
            actionBar?.hide()
        }
        kotlin.runCatching {
            supportActionBar?.hide()
        }
        setContentView(R.layout.joywork_create_owner_list_activity)
        updateTitle()
        findViewById<View>(R.id.back).setOnClickListener {
            scope.cancel()
            OneTimeDataRepo.data = result
            setResult(Activity.RESULT_OK)
            finish()
        }
        findViewById<View>(R.id.add).setOnClickListener {
            add()
        }
        rv = findViewById<RecyclerView>(R.id.rv).apply {
            layoutManager = LinearLayoutManager(
                this@CreateOwnerListActivity,
                LinearLayoutManager.VERTICAL,
                false
            )
            if (members.isEmpty()) {
                showEmpty()
            } else {
                adapter = CollaboratorListAdapter(this@CreateOwnerListActivity, members)
            }
        }
        getInfo()
    }

    private fun updateTitle() {
        titleView.text = string(R.string.joywork_more_owner) + "(" + result.size + ")"
    }

    private fun add() {
        val selected = ArrayList<MemberEntityJd>()
        if (result.isNotEmpty()) {
            result.forEach {
                it.emplAccount?.apply {
                    val jd = MemberEntityJd()
                    jd.mApp = it.app
                    jd.mId = it.emplAccount
                    selected.add(jd)
                }
            }
        }
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        val max =
            if (SUB_JOYWORK_ONETIME <= result.size) SUB_JOYWORK_ONETIME else SUB_JOYWORK_ONETIME - result.size
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setConstantFilter(selected)
            .setShowSelf(true).setOptionalFilter(null).setShowOptionalFilter(false)
            .setMaxNum(max)
        service.gotoMemberList(
            this,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    if (selected != null) {
                        val membersNet: ArrayList<JoyWorkUser> = ArrayList()
                        for (memberEntityJd in selected) {
                            memberEntityJd?.apply {
                                val member = JoyWorkUser()
                                member.fromDD(memberEntityJd)
                                member.teamId = JoyWorkUser.DEFAULT_TEAM_ID
                                membersNet.add(member)
                            }
                        }
                        if (membersNet.isEmpty()) {
                            return
                        }
                        addIfAbsent(result, membersNet)
                        (rv.adapter as? CollaboratorListAdapter)?.apply {
                            updateCollaborator(membersNet)
                            updateTitle()
                        } ?: showContent(result)
                        getInfo()
                    }
                }

                override fun onFail() {

                }
            })
    }

    private fun getInfo() {
        // 过滤掉已有部门信息的人员
        val needNet = result.filter {
            listOf(it.deptInfo?.deptName, it.titleName).hasNull()
        }
        if (!needNet.isLegalList()) {
            return
        }
        val ms = needNet.map {
            val m = Members()
            m.emplAccount = it.emplAccount
            m.ddAppId = it.ddAppId
            m
        }
        scope.launch {
            val r = getBatchInfo(ms)
            r.ifSuccessSilence {
                (rv.adapter as? CollaboratorListAdapter)?.apply {
                    updateInfo(it?.users ?: ArrayList<Members>())
                }
            }
        }
    }

    /**
     * 将 [src] 中去重复制到 [dst] 中。
     * 同时，会使用 [dst] 中的部分信息填充 [src]
     */
    private fun addIfAbsent(dst: ArrayList<JoyWorkUser>, src: List<JoyWorkUser>?) {
        src?.forEach { outer ->
            val first = dst.firstOrNull { inner ->
                inner.emplAccount == outer.emplAccount
            }
            if (first == null) {
                dst.add(outer)
            } else {
                if (first.deptInfo.fullName.isLegalString() && !outer.deptInfo.fullName.isLegalString()) {
                    outer.deptInfo = first.deptInfo
                }
            }
        }
    }

    private fun showContent(all: ArrayList<JoyWorkUser>) {
        updateTitle()
        rv.adapter = CollaboratorListAdapter(this@CreateOwnerListActivity, all)
    }

    // 移除协作人
    private fun removeNet(member: JoyWorkUser) {
        result.removeAll {
            it.emplAccount == member.emplAccount && it.ddAppId == member.ddAppId
        }
        (rv.adapter as? CollaboratorListAdapter)?.removeCollaborator(member)
        updateTitle()
    }

    override fun finish() {
        scope.cancel()
        OneTimeDataRepo.data = result
        setResult(Activity.RESULT_OK)
        super.finish()
    }

    fun showEmpty() {
        rv.adapter = JoyWorkEmptyAdapter.empty(
            this@CreateOwnerListActivity,
            R.string.joywork_owner_empty,
            R.drawable.joywork_executor_empty
        )
    }

    inner class CollaboratorListAdapter(val context: Context, dataC: ArrayList<JoyWorkUser>) :
        RecyclerView.Adapter<VH>() {

        private val data = ArrayList<JoyWorkUser>(dataC)

        private val listener = View.OnClickListener {
            val c = it.tag as JoyWorkUser
            removeNet(c)
        }

        fun updateInfo(members: List<Members>) {
            members.forEach { ms ->
                data.firstOrNull {
                    it.emplAccount == ms.emplAccount
                }?.apply {
                    deptInfo = ms.deptInfo
                    titleName = ms.titleInfo?.titleName
                }
            }
            notifyDataSetChanged()
        }

        fun updateCollaborator(members: List<JoyWorkUser>) {
            addIfAbsent(data, members)
            notifyDataSetChanged()
        }

        fun removeCollaborator(c: JoyWorkUser) {
            val index = data.indexOfFirst {
                it.isSamePerson(c)
            }
            if (index >= 0) {
                data.removeAt(index)
                if (data.isEmpty()) {
                    showEmpty()
                } else {
                    notifyItemRemoved(index)
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
            val inflate: View = LayoutInflater.from(context)
                .inflate(R.layout.joywork_collaborator_activity_item, parent, false)
            return VH(inflate)
        }

        override fun getItemCount() = data.size

        override fun onBindViewHolder(holder: VH, position: Int) {
            val c = data[position]
            holder.name.text = c.realName ?: ""
            holder.department.text = listOf(c.deptInfo?.deptName, c.titleName).joinLegalString()
            holder.divider.visibility = if (position == data.size - 1) View.GONE else View.VISIBLE
            holder.action.setText(R.string.icon_prompt_close)
            holder.action.tag = c
            holder.action.setOnClickListener(listener)
            JoyWorkViewItem.avatar(holder.avatar, c.headImg)
            holder.avatar.tag = c
            holder.avatar.setOnClickListener {
                val m = it.tag as JoyWorkUser
                <EMAIL>(m.ddAppId, m.emplAccount)
            }
        }

        fun clean() {
            data.clear()
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView by lazy { view.findViewById<ImageView>(R.id.image) }
        val name: TextView by lazy { view.findViewById<TextView>(R.id.name) }
        val action: TextView by lazy { view.findViewById<TextView>(R.id.action) }
        val department: TextView by lazy { view.findViewById<TextView>(R.id.department) }
        val divider: View by lazy { view.findViewById<View>(R.id.divider) }
    }
}

