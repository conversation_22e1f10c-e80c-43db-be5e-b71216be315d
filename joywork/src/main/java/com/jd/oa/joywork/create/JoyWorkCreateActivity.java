package com.jd.oa.joywork.create;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.BlockTypeEnum;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.joywork.JoyWorkDialog;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork.create.param.DeeplinkCreateParam;
import com.jd.oa.joywork.create.param.GoalCreateParams;
import com.jd.oa.joywork.create.param.KRCreateParams;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDialog;
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData;
import com.jd.oa.joywork.shortcut.SuccessSnackBar;
import com.jd.oa.joywork.team.bean.Group;
import com.jd.oa.joywork.utils.DeeplinkExKt;
import com.jd.oa.joywork.utils.JoyWorkNetConfig;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.Constants;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.RemoteConfigExtKt;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kotlin.Unit;
import kotlin.jvm.functions.Function3;

/**
 * 创建待办
 */
@Route({DeepLink.JOY_WORK_CREATE, DeepLink.JOY_PAGE_WORK_CREATE})
public class JoyWorkCreateActivity extends BaseActivity implements DynamicOperatorListener {
    private Fragment createFragment;

    private static final int TYPE_PROJECT = 1;
    private static final int TYPE_PERSON = 2;

    /**
     * @param isProject: true 表示团队待办，false 表示个人待办
     */
    public static void go(Context context, String title, Long startTime, Long endTime, Boolean isProject) {
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_NEW_MORE_TO, JoyWorkConstant.JOYWORK_NEW_MORE_TO);
        Intent i = new Intent(context, JoyWorkCreateActivity.class);
        if (ObjExKt.isLegalString(title)) {
            i.putExtra("title", title);
        }
        if (startTime != null) {
            i.putExtra("startTime", Math.max(startTime, 0));
        }
        if (endTime != null) {
            i.putExtra("endTime", Math.max(endTime, 0));
        }
        i.putExtra("type", isProject ? TYPE_PROJECT : TYPE_PERSON);

        context.startActivity(i);
    }

    boolean isProject() {
        return getIntent().getIntExtra("type", TYPE_PERSON) == TYPE_PROJECT;
    }

    //  BLOCK1：个人待办和团队待办在新建时各有不同参数，这里加几个静态方法各自设置
    private static BlockTypeEnum mType;

    public static void setBlockType(BlockTypeEnum type) {
        mType = type;
    }

    public static BlockTypeEnum getBlockType() {
        return mType;
    }

    public static Function3<String, Value, JoyWorkShortcutDialog, Unit> successCallback = null;
    // 负责人
    public static List<JoyWorkUser> owners = null;
    public static Set<AlertType> mTs = null;
    //    父待办
    public static ShortcutDialogTmpData tmpData = null;
    // 团队待办时，团队 id
    public static String projectId;
    public static ArrayList<Group> groups;
    public static String selId;
    public static SuccessSnackBar successRunnable = null;
    public static DuplicateEnum duplicateEnum = null;
    public static String parentId;
    public static boolean isSubJoywork = false;
    public static boolean mSendDDChecked = false;
    public static DeeplinkCreateParam deeplinkCreateParam;
    public static String viewId;
    public static String viewType;
    public static List<Documents> shimo;
    public static Boolean showGroup = false;

    // 结束 BLOCK1

    /**
     * 提供给 fragment 使用的，用于获取 intent 中的值
     */
    String getTitleStr() {
        if (getIntent().hasExtra("title")) {
            return getIntent().getStringExtra("title");
        } else {
            return "";
        }
    }

    /**
     * 提供给 fragment 使用的，用于获取 intent 中的值
     */
    Long getEndTime() {
        if (getIntent().hasExtra("endTime")) {
            return getIntent().getLongExtra("endTime", -1);
        } else {
            return null;
        }
    }

    String getBizCode() {
        if (tmpData == null) {
            return null;
        }
        return tmpData.getBizCode();
    }

    String getSessionId() {
        if (tmpData == null) {
            return null;
        }
        return tmpData.getSessionId();
    }

    /**
     * 提供给 fragment 使用的，用于获取 intent 中的值
     */
    Long getStartTime() {
        if (getIntent().hasExtra("startTime")) {
            return getIntent().getLongExtra("startTime", 0);
        } else {
            return null;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_function);
        if (getActionBar() != null) {
            getActionBar().hide();
        }
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        parseDeepLink();
        if (RemoteConfigExtKt.grayResourceSwitchEnable(
                Constants.KEY_JOY_WORK_H5_CREATE_ENABLE, "0", "1")) {
            createFragment = initH5CreateFragment();
        } else {
            createFragment = new JoyWorkCreateFragment();
        }
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.me_fragment_content, createFragment).commit();
    }

    private JoyWorkH5CreateFragment initH5CreateFragment() {
        Bundle intent = new Bundle();
        WebBean bean = new WebBean(JoyWorkNetConfig.INSTANCE.getWorkCreateUrl(viewId, viewType, this), "0");
        intent.putParcelable(WebFragment2.EXTRA_WEB_BEAN, bean);
        JoyWorkH5CreateFragment webFragment = new JoyWorkH5CreateFragment();
        webFragment.setArguments(intent);
        return webFragment;
    }

    private void parseDeepLink() {
        deeplinkCreateParam = DeeplinkCreateParam.createFromParam(DeeplinkExKt.getParamsStr(getIntent()));
        if (tmpData != null) {
            if (ObjExKt.isLegalString(tmpData.getTargetId())) {
                deeplinkCreateParam = DeeplinkCreateParam.createFromParam(GoalCreateParams.genParamsStr(tmpData.getTargetId()));
            } else if (ObjExKt.isLegalString(tmpData.getKrId())) {
                deeplinkCreateParam = DeeplinkCreateParam.createFromParam(KRCreateParams.genParamString(tmpData.getKrId(),null));
            }
        }
        if (deeplinkCreateParam != null) {
            if (tmpData == null) {
                tmpData = new ShortcutDialogTmpData();
            }
            tmpData.setBizCode(deeplinkCreateParam.bizCode);
            tmpData.setContent(deeplinkCreateParam.content);
            tmpData.setSessionId(deeplinkCreateParam.inventory);
            tmpData.setMobileContent(deeplinkCreateParam.mobileContent);
            tmpData.setTripartiteName(deeplinkCreateParam.tripartiteName);
        }
    }

    @Override
    public void finish() {
        if (createFragment != null && createFragment instanceof JoyWorkCreateFragment) {
            JoyWorkDialog.INSTANCE.showAlertDialog(getResources().getString(R.string.joywork_create_dialog_tips), dialog -> {
                dialog.dismiss();
                JoyWorkCreateActivity.super.finish();
                return null;
            });
        } else {
            JoyWorkCreateActivity.super.finish();
        }
    }

    public void superFinish() {
        super.finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (createFragment != null) {
            createFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    public void chooseFileFromJS(final Map<String, Object> params, final Callback2<Boolean> cb) {
        if (createFragment != null && createFragment instanceof JoyWorkCreateFragment) {
            ((JoyWorkCreateFragment) createFragment).chooseFileFromJS(params);
            cb.onSuccess(true, true);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mType = null;
        if (owners != null) {
            owners.clear();
            owners = null;
        }
        duplicateEnum = null;
        projectId = null;
        if (groups != null) {
            groups.clear();
            groups = null;
        }
        selId = null;
        successRunnable = null;
        parentId = null;
        isSubJoywork = false;
        mSendDDChecked = false;
        tmpData = null;
        successCallback = null;
    }

    public DuplicateEnum getDuplicateEnum() {
        return duplicateEnum;
    }

    @Override
    public void operator(Map<String, Object> param) {

    }

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        if (createFragment != null && createFragment instanceof JoyWorkH5CreateFragment) {
            ((JoyWorkH5CreateFragment) createFragment).registerCallback(requestCode, callBack);
        }
    }
}
