package com.jd.oa.joywork.create;

import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkExKt;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork.bean.KR;
import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.joywork.detail.JoyWorkContentExKt;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.model.service.im.dd.entity.Members;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.team.bean.GrayInfo;
import com.jd.oa.joywork.team.bean.Group;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import kotlin.jvm.functions.Function1;

public class Value {

    public Value(JoyWorkCreateFragment fragment) {
        mCreateViewModel = fragment.mCreateViewModel;
    }

    public Value(CreateViewModel viewModel) {
        this.mCreateViewModel = viewModel;
    }

    public Value() {

    }

    /**
     * 是否是清空
     */
    public Boolean isClean = false;

    public CreateViewModel mCreateViewModel;
    /**
     * 待办标题
     */
    public String title;


    public static final int TYPE_DEADLINE = 1;
    public static final int TYPE_START_TIME = 2;

    /**
     * 1: 选截止时间
     * 2：选开始时间
     * 3: 保持旧逻辑
     */
    public Integer type;

    public boolean getHasTime() {
        return startTime != null || endTime != null;
    }

    public void updateTitle(String title) {
        this.title = title;
        if (mCreateViewModel != null) {
            mCreateViewModel.updateTitle();
        }
    }

    // 开始、截止时间
    public Long startTime = null;
    public Long endTime = null;
    // 启动时间
    public Long launchTime = null;

    public void updateLaunchTime(Long launchTime) {
        this.launchTime = launchTime;
        if (mCreateViewModel != null) {
            mCreateViewModel.updateLaunchTime();
        }
    }

    // 关联目标
    public ArrayList<KpiTarget> targets = new ArrayList<>();

    public void replaceTargets(List<KpiTarget> targets) {
        this.targets.clear();
        if (targets != null && !targets.isEmpty()) {
            this.targets.addAll(targets);
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateTarget();
        }
    }

    public boolean addTargets(List<KpiTarget> ts) {
        boolean r = ObjExKt.union(targets, ts, new Function1<KpiTarget, String>() {
            @Override
            public String invoke(KpiTarget target) {
                return target.goalId;
            }
        });
        if (r && mCreateViewModel != null) {
            mCreateViewModel.updateTarget();
        }
        return r;
    }

    public boolean addTarget(KpiTarget target) {
        if (target == null)
            return false;
        boolean had = false;
        for (KpiTarget next : targets) {
            if (Objects.equals(next.goalId, target.goalId)) {
                had = true;
                break;
            }
        }
        if (!had) {
            targets.add(target);
            if (mCreateViewModel != null) {
                mCreateViewModel.updateTarget();
            }
        }
        return had;
    }

    public void delTarget(KpiTarget target) {
        Iterator<KpiTarget> iterator = targets.iterator();
        boolean ans = false;
        while (iterator.hasNext()) {
            KpiTarget next = iterator.next();
            if (Objects.equals(next.goalId, target.goalId)) {
                iterator.remove();
                ans = true;
            }
        }
        if (ans && mCreateViewModel != null) {
            mCreateViewModel.updateTarget();
        }
    }

    // kr
    public ArrayList<KR> krs = new ArrayList<>();

    public void replaceKrs(List<KR> targets) {
        this.krs.clear();
        if (targets != null && !targets.isEmpty()) {
            this.krs.addAll(targets);
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateKr();
        }
    }

    public boolean addKrs(List<KR> ts) {
        boolean r = ObjExKt.union(krs, ts, new Function1<KR, String>() {
            @Override
            public String invoke(KR target) {
                return target.krId;
            }
        });
        if (r && mCreateViewModel != null) {
            mCreateViewModel.updateKr();
        }
        return r;
    }

    public boolean addKr(KR target) {
        if (target == null)
            return false;
        boolean had = false;
        for (KR next : krs) {
            if (Objects.equals(next.krId, target.krId)) {
                if (!Objects.equals(next.content, target.content)) {
                    next.content = target.content;
                    if (mCreateViewModel != null) {
                        mCreateViewModel.updateKr();
                    }
                }
                had = true;
                break;
            }
        }
        if (!had) {
            krs.add(target);
            if (mCreateViewModel != null) {
                mCreateViewModel.updateKr();
            }
        }
        return had;
    }

    public void delKr(KR target) {
        Iterator<KR> iterator = krs.iterator();
        boolean ans = false;
        while (iterator.hasNext()) {
            KR next = iterator.next();
            if (Objects.equals(next.krId, target.krId)) {
                iterator.remove();
                ans = true;
            }
        }
        if (ans && mCreateViewModel != null) {
            mCreateViewModel.updateKr();
        }
    }

    // 目标与 kr 一致逻辑

    public void addGoalOrKr(Parcelable parcelable) {
        if (parcelable instanceof KR) {
            addKr((KR) parcelable);
        } else if (parcelable instanceof KpiTarget) {
            addTarget((KpiTarget) parcelable);
        }
    }

    public List<Object> mergeTargetsAndKrs() {
        List<Object> r = new ArrayList<>();
        if (targets != null && !targets.isEmpty()) {
            r.addAll(targets);
        }
        if (krs != null && !krs.isEmpty()) {
            r.addAll(krs);
        }
        return r;
    }

    // 关联的预置清单
    public String sysProjectIcon;
    public List<JoyWorkDetail.Project> sysProjects = new ArrayList<>();

    public void delSysProject(JoyWorkDetail.Project project) {
        Iterator<JoyWorkDetail.Project> iterator = sysProjects.iterator();
        boolean ans = false;
        while (iterator.hasNext()) {
            JoyWorkDetail.Project next = iterator.next();
            if (Objects.equals(next.getProjectId(), project.getProjectId())) {
                iterator.remove();
                ans = true;
            }
        }
        if (ans && mCreateViewModel != null) {
            mCreateViewModel.updateSysProject();
        }
    }

    public void replaceSysProjects(List<JoyWorkDetail.Project> projects) {
        this.sysProjects.clear();
        this.sysProjects.addAll(projects);
        if (mCreateViewModel != null) {
            mCreateViewModel.updateSysProject();
        }
    }

    // 关联的清单
    public List<JoyWorkDetail.Project> projects = new ArrayList<>();

    public void delProject(JoyWorkDetail.Project project) {
        Iterator<JoyWorkDetail.Project> iterator = projects.iterator();
        boolean ans = false;
        while (iterator.hasNext()) {
            JoyWorkDetail.Project next = iterator.next();
            if (Objects.equals(next.getProjectId(), project.getProjectId())) {
                iterator.remove();
                ans = true;
            }
        }
        if (ans && mCreateViewModel != null) {
            mCreateViewModel.updateProject();
        }
    }

    /**
     * @return 已有，返回 false；否则返回 true
     */
    public boolean addProject(JoyWorkDetail.Project project) {
        boolean had = false;
        for (JoyWorkDetail.Project next : projects) {
            if (Objects.equals(next.getProjectId(), project.getProjectId())) {
                next.setGroupId(project.getGroupId());
                next.setGroupTitle(project.getGroupTitle());
                had = true;
                break;
            }
        }
        if (!had) {
            projects.add(project);
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateProject();
        }
        return had;
    }

    public void replaceProjects(List<JoyWorkDetail.Project> projects) {
        this.projects.clear();
        this.projects.addAll(projects);
        if (mCreateViewModel != null) {
            mCreateViewModel.updateProject();
        }
    }

    public DuplicateEnum duplicateEnum;

    public void updateDup(DuplicateEnum de) {
        this.duplicateEnum = de;
        if (mCreateViewModel != null) {
            mCreateViewModel.updateDup();
        }
    }

    public void updateTime(Long startTime, Long endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        if (!ObjExKt.isLegalTimestamp(endTime)) {
            updateDup(null);
            replaceAlertType(new HashSet<>());
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateTime();
        }
    }

    // 描述
    String des = null;

    public void updateDes(String des) {
        if (ObjExKt.isLegalString(des)) {
            this.des = des;
            mCreateViewModel.updateDes();
        }
    }

    // 优先级
    Integer priority;

    public void updatePriority(Integer p) {
        this.priority = p;
        if (mCreateViewModel != null) {
            mCreateViewModel.updatePriority();
        }
    }

    // 提醒我
    Long alertTime;

    public void updateAlertTime(Long alertTime) {
        this.alertTime = alertTime;
        if (mCreateViewModel != null) {
            mCreateViewModel.updateAlertTime();
        }
    }

    // 提醒时间
    public HashSet<AlertType> mAlertType = new HashSet<>();

    public void replaceAlertType(Set<AlertType> types) {
        if (types == null) {
            return;
        }
        mAlertType.clear();
        mAlertType.addAll(types);
        if (mCreateViewModel != null) {
            mCreateViewModel.updateAlertTypes();
        }
    }

    public void cleanAlertTypes() {
        mAlertType.clear();
        if (mCreateViewModel != null) {
            mCreateViewModel.updateAlertTypes();
        }
    }

    // 文档
    public List<Documents> shimo = new ArrayList<>();

    public void updateShimo(List<Documents> documentsList) {
        if (documentsList == null || documentsList.isEmpty())
            return;
        Iterator<Documents> iterator = documentsList.iterator();
        while (iterator.hasNext()) {
            Documents documents = iterator.next();
            if (shimo != null && shimo.contains(documents)) {
                iterator.remove();
            }
        }
        // 去重后，没有新的，不更新 UI
        if (documentsList.isEmpty()) {
            return;
        }
        shimo.addAll(documentsList);
        if (mCreateViewModel != null) {
            mCreateViewModel.updateShimo();
        }
    }

    // 附件
    List<Resources> mAtt = new ArrayList<>();

    public void replaceAtt(List<Resources> rs) {
        mAtt.clear();
        if (rs != null) {
            mAtt.addAll(rs);
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateAttachment();
        }
    }

    public void updateAtt(Resources resources) {
        if (resources == null)
            return;
        mAtt.add(resources);
        if (mCreateViewModel != null) {
            mCreateViewModel.updateAttachment();
        }
    }

    // 负责人，普通待办的多人待办
    public ArrayList<JoyWorkUser> normalOwners = new ArrayList<>();

    public void setNormalOwners(List<JoyWorkUser> us) {
        if (us == null)
            return;
        normalOwners.clear();
        normalOwners.addAll(us);
        sortJoyWorkUsers();
        if (mCreateViewModel != null) {
            mCreateViewModel.updateNormalOwners();
        }
    }

    public void addNormalOwners(List<JoyWorkUser> joyWorkUsers) {
        if (joyWorkUsers != null && !joyWorkUsers.isEmpty()) {
            String key = null;
            for (JoyWorkUser user : joyWorkUsers) {
                if (JoyWorkExKt.isChief(user.chief)) {
                    key = JoyWorkContentExKt.key(user);
                    break;
                }
            }
            ObjExKt.union(normalOwners, joyWorkUsers, new Function1<JoyWorkUser, String>() {
                @Override
                public String invoke(JoyWorkUser joyWorkUser) {
                    return JoyWorkContentExKt.key(joyWorkUser);
                }
            });
            if (ObjExKt.isLegalString(key)) {
                for (JoyWorkUser user : normalOwners) {
                    if (Objects.equals(key, JoyWorkContentExKt.key(user))) {
                        user.setToChief();
                    } else {
                        user.cancelChief();
                    }
                }
            }
        }
        sortJoyWorkUsers();
        if (mCreateViewModel != null) {
            mCreateViewModel.updateNormalOwners();
        }
    }

    public boolean hasSelf() {
        boolean ans = false;
        for (JoyWorkUser user : normalOwners) {
            ans = JoyWorkExKt.isSelf(user);
            if (ans) {
                return ans;
            }
        }
        return ans;
    }

    private void sortJoyWorkUsers() {
        JoyWorkUser user = null;
        for (JoyWorkUser owner : normalOwners) {
            if (JoyWorkExKt.isChief(owner.chief)) {
                user = owner;
                break;
            }
        }
        if (user != null) {
            normalOwners.remove(user);
            ObjExKt.safeAdd(normalOwners, 0, user);
        }
    }

    // 团队待办分组
    private ArrayList<Group> groups; // 所有分组
    public String groupId; // 当前选中的分组

    public void setGroups(ArrayList<Group> gs) {
        this.groups = gs == null ? new ArrayList<Group>() : gs;
    }

    public ArrayList<Group> getGroups() {
        return groups;
    }

    public void updateGroupUI(String id) {
        if (ObjExKt.isLegalString(id)) {
            this.groupId = id;
            if (mCreateViewModel != null) {
                mCreateViewModel.updateGroup();
            }
        }
    }

    // 协作人
    public ArrayList<Members> members = new ArrayList<>();

    public void updateMembers(List<Members> ms) {
        if (ms == null || ms.isEmpty()) {
            members.clear();
        } else {
            members.clear();
            members.addAll(ms);
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateRelation();
        }
    }

    public void addMembers(ArrayList<Members> ms) {
        if (members == null) {
            this.members = new ArrayList<>();
        }
        if (ms != null && !ms.isEmpty()) {
            ObjExKt.union(members, ms, new Function1<Members, String>() {
                @Override
                public String invoke(Members joyWorkUser) {
                    return joyWorkUser.getEmplAccount() + joyWorkUser.getDdAppId();
                }
            });
        }

        if (mCreateViewModel != null) {
            mCreateViewModel.updateRelation();
        }
    }

    // 多人待办，用于子待办
    ArrayList<JoyWorkUser> subOwners = new ArrayList<>();

    public void addSubJoyworkOwners(ArrayList<JoyWorkUser> joyWorkUsers) {
        if (joyWorkUsers != null && !joyWorkUsers.isEmpty()) {
            ObjExKt.union(subOwners, joyWorkUsers, new Function1<JoyWorkUser, String>() {
                @Override
                public String invoke(JoyWorkUser joyWorkUser) {
                    return joyWorkUser.emplAccount + joyWorkUser.ddAppId;
                }
            });
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateOwners();
        }
    }

    public void setSubJoyworkOwners(ArrayList<JoyWorkUser> joyWorkUsers) {
        subOwners = new ArrayList<>();
        if (joyWorkUsers != null && !joyWorkUsers.isEmpty()) {
            subOwners.addAll(joyWorkUsers);
        }
        if (mCreateViewModel != null) {
            mCreateViewModel.updateOwners();
        }
    }

    // 以下内容只在详情中使用到
    // 待办风险问题
    public Integer riskStatus;

    public void updateRisk(Integer riskStatus) {
        this.riskStatus = riskStatus;
        if (mCreateViewModel != null) {
            mCreateViewModel.updateRisk();
        }
    }

    // 详情页专属结束

    // 团队 id
    String projectId;
    // 团队分组 id
    public String selId;

    // 额外的信息
    public String extra;
    // 灰度信息
    public GrayInfo grayInfo;

    @NonNull
    public GrayInfo safeGrayInfo() {
        if (grayInfo == null) {
            return new GrayInfo();
        }
        return grayInfo;
    }

    public void setGrayInfo(GrayInfo info) {
        this.grayInfo = info;
        if (mCreateViewModel != null) {
            mCreateViewModel.updateGrayInfo();
        }
    }
}
