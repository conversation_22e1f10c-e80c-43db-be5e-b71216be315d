package com.jd.oa.joywork.team.bean;

import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.model.service.im.dd.entity.DeptInfo;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

public class VisibleSetting {
    public static final int TYPE_ONLY_SUPERVISOR = 1;
    public static final int TYPE_PART = 2;
    public static final int TYPE_ALL = 3;

    public Integer visibleType;
    public List<JoyWorkUser> members;
    public List<DeptInfo> depts;

    // Whether visible users has been obtained
    public boolean visibleUsersObtained = false;

    public boolean isOnlySupervisor() {
        return visibleType == TYPE_ONLY_SUPERVISOR;
    }

    public boolean isAll() {
        return visibleType == TYPE_ALL;
    }

    public boolean isPart() {
        return visibleType == TYPE_PART;
    }

    public void replaceUsers(List<JoyWorkUser> us) {
        List<JoyWorkUser> newUs = us == null ? new ArrayList<JoyWorkUser>() : us;
        List<JoyWorkUser> oldUs = this.members == null ? new ArrayList<JoyWorkUser>() : this.members;
        oldUs.clear();
        oldUs.addAll(newUs);
        this.members = oldUs;
    }

    public void frontUsers(List<JoyWorkUser> us) {
        List<JoyWorkUser> oldUs = this.members == null ? new ArrayList<JoyWorkUser>() : this.members;
        frontUsers(oldUs, us);
        this.members = oldUs;
    }

    public void replaceDepts(List<DeptInfo> us) {
        List<DeptInfo> newUs = us == null ? new ArrayList<>() : us;
        List<DeptInfo> oldUs = this.depts == null ? new ArrayList<>() : this.depts;
        oldUs.clear();
        oldUs.addAll(newUs);
        this.depts = oldUs;
    }

    public void appendDepts(List<DeptInfo> us) {
        if (us == null || us.isEmpty()) {
            return;
        }
        List<DeptInfo> oldUs = this.depts == null ? new ArrayList<DeptInfo>() : this.depts;
        oldUs.addAll(us);
        this.depts = oldUs;
    }

    public static void frontUsers(List<JoyWorkUser> src, List<JoyWorkUser> us) {
        if (us == null || us.isEmpty()) {
            return;
        }
        List<JoyWorkUser> oldUs = src == null ? new ArrayList<JoyWorkUser>() : src;
        HashSet<String> oldKeys = new HashSet<>();
        for (JoyWorkUser user : oldUs) {
            oldKeys.add(getKey(user));
        }
        List<JoyWorkUser> tmp = new ArrayList<>();
        for (JoyWorkUser user : us) {
            if (!oldKeys.contains(getKey(user))) {
                tmp.add(user);
                oldKeys.add(getKey(user));
            }
        }
        oldUs.addAll(0, tmp);
    }

    public static String getKey(JoyWorkUser user) {
        return user.ddAppId + "_" + user.emplAccount;
    }

    public VisibleSetting copy() {
        VisibleSetting setting = new VisibleSetting();
        setting.visibleType = this.visibleType;
        setting.members = new ArrayList<>();
        if (ObjExKt.isLegalList(members)) {
            setting.members.addAll(this.members);
        }
        return setting;
    }
}
