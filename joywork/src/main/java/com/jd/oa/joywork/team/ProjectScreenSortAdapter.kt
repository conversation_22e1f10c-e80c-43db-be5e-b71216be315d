package com.jd.oa.joywork.team

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.inflater

class ProjectScreenSortAdapter(private val ts: ArrayList<SortValue>, private val context: Context,
                               private val onDeleteListener: (bean: SortValue) -> Unit,
                               private val onClickListener: (bean: SortValue) -> Unit)
    : RecyclerView.Adapter<ProjectScreenSortAdapter.VH>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(context.inflater.inflate(R.layout.jdme_layout_screen_sort_item, parent, false))
    }

    override fun getItemCount() = ts.size

    override fun onBindViewHolder(holder: VH, position: Int) {
        holder.itemView.visibility = (if (ts[position].code == 0) View.GONE else View.VISIBLE)
        holder.type.text = (context.getString(R.string.icon_workplacesortascending))
        holder.title.text = (ts[position].title)
        holder.close.setOnClickListener { onDeleteListener.invoke(ts[position]) }
//        holder.close.setOnClickListener()
        holder.itemView.setOnClickListener { onClickListener.invoke(ts[position]) }
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    interface OnItemClickListener {
        fun onClick(isScreenOrSoft: Int)
    }

    inner class VH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val title: TextView = itemView.findViewById(R.id.tv_title)
        val close: IconFontView = itemView.findViewById(R.id.iftv_close)
        val type: IconFontView = itemView.findViewById(R.id.iftv_type)


    }
}