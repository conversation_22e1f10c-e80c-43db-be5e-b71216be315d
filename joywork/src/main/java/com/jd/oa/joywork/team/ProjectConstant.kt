package com.jd.oa.joywork.team

import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager

object ProjectConstant {
    val NEW_GROUP_TITLE_ID = "project.new.group"

    // 完成待办
    val FINISH_ACTION = "project.task.finish"

    // 撤销完成待办
    val UNFINISH_ACTION = "project.task.unfinish"

    // 删除待办
    val DEL_ACTION = "project.task.del"

    // 还原待办
    val REVERSE_ACTION = "project.task.reverse"

    // 刷新列表。从详情返回时
    val REFRESH_ACTION = "project.task.refresh"

    // 拖动排序
    val JOYWORK_SORT = "project.task.sort"

    fun sendBroadcast(context: Context, action: String) {
        val intent = Intent()
        intent.action = action
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
    }
}