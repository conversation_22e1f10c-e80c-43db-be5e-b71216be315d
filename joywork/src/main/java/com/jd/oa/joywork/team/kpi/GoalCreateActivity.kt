package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.TextView
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.JoyWorkDialog
import com.jd.oa.joywork.R
import com.jd.oa.joywork.common.*
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.utils.getDeeplinkRawString
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.TextWatcherAdapter
import com.jd.oa.utils.setCursorEnd
import com.jd.oa.utils.string
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.*

/**
 * 目标新建、更新界面
 */
@Route(DeepLink.JOYWORK_GOAL_CREATE)
class GoalCreateActivity : BaseActivity() {
    companion object {
        /**
         * 新建目标
         */
        fun newTarget(activity: Activity, code: Int, assessPeriod: String) {
            val i = Intent(activity, GoalCreateActivity::class.java)
            i.putExtra("type", "new")
            i.putExtra("assessPeriod", assessPeriod)
            activity.startActivityForResult(i, code)
        }

        /**
         * 更新目标
         */
        fun updateTarget(activity: Activity, code: Int, goalId: String, title: String?) {
            val i = Intent(activity, GoalCreateActivity::class.java)
            i.putExtra("type", "update")
            i.putExtra("goalId", goalId)
            i.putExtra("title", title ?: "")
            activity.startActivityForResult(i, code)
        }

        private fun GoalCreateActivity.isNew(): Boolean {
            return intent.getStringExtra("type") == "new"
        }

        private fun GoalCreateActivity.assessPeriod(): String {
            return intent.getStringExtra("assessPeriod") ?: ""
        }

        private fun GoalCreateActivity.goalId(): String {
            return intent.getStringExtra("goalId") ?: ""
        }

        private fun GoalCreateActivity.title(): String {
            return intent.getStringExtra("title") ?: ""
        }

        fun parseResultIntent(intent: Intent): SerializableInteractionResult {
            return SerializableStub.readFromIntent(intent)
        }
    }

    private lateinit var mTitle: TextView
    private lateinit var mCreate: TextView
    private lateinit var mNum: TextView
    private lateinit var mContentView: EditText

    private val scope = MainScope()

    private fun fromDeeplink() {
        // jdme://jm/biz/joywork/goalList?mparam={"erp":"qianyang3","ddappid":"ee","realname":"千阳",
        //                "headimg":"https://storage.jd.com/jd.jme.avatar/5374E384BF494DCE66AC2FAB44257CA0.png","assessPeriod":"xx"}
        try {
            val deeplink = intent.getDeeplinkRawString()
            if (Objects.equals(deeplink, DeepLink.JOYWORK_GOAL_CREATE)) {
                intent.putExtra("type", "new")
            }
        } catch (e: Exception) {

        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        fromDeeplink()
        restore(savedInstanceState)
        setContentView(R.layout.joywork_target_activity)
        findViewById<View>(R.id.mBack).setOnClickListener {
            if (Objects.equals(title(), mContentView.string())) {
                finish()
                return@setOnClickListener
            }
            JoyWorkDialog.showAlertDialog(
                context = this,
                msg = getString(R.string.joywork_project_create_cancle_tip),
                okRes = R.string.joywork_urge_tips_ok,
                cancelResource = R.string.joywork_continue_edit,
                okClick = {
                    finish()
                }
            ) { _, d ->
                d.dismiss()
            }
        }
        mContentView = findViewById(R.id.mContentView)
        mCreate = findViewById(R.id.mCreate)
        mNum = findViewById(R.id.mNum)
        mTitle = findViewById(R.id.mTitle)
        mContentView.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                super.afterTextChanged(s)
                mCreate.isEnabled = mContentView.string().isNotEmpty()
                mNum.text = "${mContentView.string().length}/200"
            }
        })
        mCreate.setOnClickListener {
            if (isNew()) {
                newTarget()
            } else {
                updateTarget()
            }
        }
        if (isNew()) {
            newTargetUI()
        } else {
            updateTargetUI()
        }
    }

    private fun newTarget() {
        scope.launch {
            mCreate.isEnabled = false
            val r = KpiRepo.newTarget(
                mContentView.string(),
                assessPeriod()
            )
            mCreate.isEnabled = true
            r.ifSuccessToast {
                val i = Intent()
                val value = ArrayList<String>()
                value.add(assessPeriod())
                value.add(mContentView.string())
                val result = SerializableInteractionResult(activity_interaction_create, value)
                result.writeToIntent(i)
                setResult(RESULT_OK, i)
                finish()
            }
        }
    }

    private fun updateTarget() {
        scope.launch {
            mCreate.isEnabled = false
            val r = KpiRepo.updateTarget(
                mContentView.string(),
                goalId()
            )
            mCreate.isEnabled = false
            r.ifSuccessToast {
                val i = Intent()
                val value = ArrayList<String>()
                value.add(goalId())
                value.add(mContentView.string())
                val result = SerializableInteractionResult(activity_interaction_update, value)
                result.writeToIntent(i)
                setResult(RESULT_OK, i)
                finish()
            }
        }
    }

    private fun newTargetUI() {
        mTitle.setText(R.string.joywork_new_target)
        mContentView.setText("")
        ShortcutDialogCommon.openKeyboard(mContentView,this)
    }

    private fun updateTargetUI() {
        mTitle.setText(R.string.joywork_update_target)
        mCreate.setText(R.string.confirm)
        mContentView.setText(title())
        mContentView.setCursorEnd()
        ShortcutDialogCommon.openKeyboard(mContentView,this)
    }

    private fun restore(bundle: Bundle?) {
        if (bundle == null)
            return
        intent.putExtra("type", bundle.getString("type"))
        if (isNew()) {
            intent.putExtra("assessPeriod", bundle.getString("assessPeriod"))
        } else {
            intent.putExtra("goalId", bundle.getString("goalId"))
            intent.putExtra("title", bundle.getString("title"))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (isNew()) {
            outState.putString("type", "new")
            outState.putString("assessPeriod", assessPeriod())
        } else {
            outState.putString("type", "update")
            outState.putString("goalId", goalId())
            outState.putString("title", mContentView.string())
        }
    }
}