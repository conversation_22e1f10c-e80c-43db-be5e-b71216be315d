package com.jd.oa.joywork.team.chat

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.JoyWorkProjectList.ListDTO
import com.jd.oa.joywork.team.JoyWorkProjectCallback
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.utils.appendIfNotExist
import kotlinx.coroutines.launch
import java.util.Objects

class ProjectSelectorViewModel : ViewModel() {
    // 清单列表返回结果
    private val _mProjectLiveData =
        MutableLiveData<DataState>()
    val mProjectLiveData: LiveData<DataState> = _mProjectLiveData

    private val mProjectList = ArrayList<ListDTO>()

    private val _mCheckedProjectLiveData =
        MutableLiveData<ListDTO>(null)
    val mCheckedProjectLiveData: LiveData<ListDTO> = _mCheckedProjectLiveData

    private val _mEffectLiveData =
        MutableLiveData<Effect<Any>>()
    val mEffectLiveData: LiveData<Effect<Any>> = _mEffectLiveData

    fun updateCheckedItem(dto: ListDTO) {
        if (Objects.equals(dto.projectId, _mCheckedProjectLiveData.value?.projectId)) {
            return
        }
        _mCheckedProjectLiveData.value = dto
    }

    fun loadMore() {
        getProjectList(true)
    }

    fun initList() {
        getProjectList(false)
    }

    fun insertBefore(dto: ListDTO) {
        mProjectList.add(0, dto)
        _mProjectLiveData.value = DataState.DataChanged(mProjectList)
    }

    private fun getProjectList(append: Boolean) {
        val offset = if (append) {
            mProjectList.size
        } else {
            0
        }
        viewModelScope.launch {
            _mEffectLiveData.value = Effect.RefreshEffect(true)
            ProjectRepo.listEditableProject(
                object : JoyWorkProjectCallback() {
                    override fun call(wrapper: JoyWorkProjectList, rawData: String) {
                        val newList =
                            wrapper.list ?: ArrayList<ListDTO>()
                        if (append) {
                            _mEffectLiveData.value =
                                Effect.LoadMoreFinish(newList.size < 100)
                            mProjectList.appendIfNotExist(newList) {
                                it.projectId
                            }
                        } else {
                            mProjectList.clear()
                            mProjectList.addAll(newList)
                        }
                        _mProjectLiveData.value = DataState.DataChanged(mProjectList)
                        _mEffectLiveData.value = Effect.RefreshEffect(false)
                    }

                    override fun onError(msg: String) {
                        if (append) {
                            _mEffectLiveData.value = Effect.LoadMoreFinish(false)
                            _mProjectLiveData.value = DataState.LoadMoreFailure(msg)
                        } else {
                            _mProjectLiveData.value = DataState.RefreshFailure(msg)
                        }
                        _mEffectLiveData.value = Effect.RefreshEffect(false)
                    }
                }, offset
            )
        }
    }

    sealed class DataState {
        class DataChanged(val data: List<ListDTO>) : DataState()
        class RefreshFailure(val msg: String) : DataState()
        class LoadMoreFailure(val msg: String) : DataState()
    }

    sealed class Effect<out T> {
        class LoadMoreFinish(val completed: Boolean) : Effect<Boolean>()
        class RefreshEffect(val isRefreshing: Boolean) : Effect<Boolean>()
    }
}