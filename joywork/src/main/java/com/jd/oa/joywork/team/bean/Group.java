package com.jd.oa.joywork.team.bean;

import android.content.Context;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.bean.JoyWork;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Group implements Serializable {
    public Integer total;
    public String groupId;
    public String title;
    public Integer type;
    public String projectId;
    public Integer status;
    public List<JoyWork> tasks;

    public Boolean initialize; // 是否初始化

    // 来源于 ProjectGroups
    public List<String> permissions;

    public boolean isInit() {
        return initialize != null && initialize;
    }

    public List<String> getSafePermissions() {
        return permissions == null ? new ArrayList<String>() : permissions;
    }

    public int getSafeTotal() {
        return total == null ? 0 : total;
    }

    public List<JoyWork> getSafeTasks() {
        if (tasks == null) {
            tasks = new ArrayList<>();
        }
        return tasks;
    }

    public String safeTitle(Context context) {
        if (title != null && !title.trim().isEmpty()) {
            return title;
        }
        if (getSafeType() == ProjectGroupTypeEnum.INITIAL.getCode()) {
            return context.getResources().getString(R.string.joywork_task_link_untitled_group);
        }
        return context.getResources().getString(R.string.joywork_task_link_untitled_group);
    }

    public int getSafeType() {
        return type == null ? 0 : type;
    }

    public int getSafeStatus() {
        return status == null ? ProjectStatusEnum.DELETED.getCode() : status;
    }

    public boolean isDefaultGroup() {
        return getSafeType() == ProjectGroupTypeEnum.INITIAL.getCode();
    }

    public Group copy() {
        Group group = new Group();
        if (this.permissions != null) {
            group.permissions = new ArrayList<>(this.permissions);
        }
        group.groupId = this.groupId;
        group.projectId = this.projectId;
        group.status = this.status;
        group.title = this.title;
        group.type = this.type;
        group.tasks = new ArrayList<>(0);
        group.total = this.total;
        return group;
    }
}
