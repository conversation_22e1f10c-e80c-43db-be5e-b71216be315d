package com.jd.oa.joywork.team;

import android.content.Context;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.team.bean.ProjectList;
import com.jd.oa.ui.IconFontView;

import java.util.ArrayList;
import java.util.List;

public class GroupListAdapter extends RecyclerView.Adapter<GroupListAdapter.ViewHolder> {
    private Context mContext;
    private List<ProjectList.Content.List.Groups> mList;
    private String mProjectId;
    private ArrayList<JoyWorkDetail.Project> mLinkedGroups;
    private String mCurrentSelectGroup;
    private JoyWorkDetail.Project mCurrentSelectProject;
    private String mProjectName;

    public GroupListAdapter(Context context, List<ProjectList.Content.List.Groups> list, String projectId
            , ArrayList<JoyWorkDetail.Project> groups, String projectName) {
        mContext = context;
        mList = list;
        mProjectId = projectId;
        mLinkedGroups = groups;
        mProjectName = projectName;
        if (mLinkedGroups != null) {
            for (JoyWorkDetail.Project project : mLinkedGroups) {
                if (project.getProjectId().equals(mProjectId)) {
                    mCurrentSelectGroup = project.getGroupId();
                    mCurrentSelectProject = project;
                }
            }
        }
        //默认勾选第一个
        if (mCurrentSelectGroup == null && list.size() > 0) {
            mCurrentSelectGroup = list.get(0).getGroupId();
            ProjectList.Content.List.Groups group = mList.get(0);
            mCurrentSelectProject = new JoyWorkDetail.Project(group.getGroupId(), group.getTitle(), mProjectName, group.getProjectId(), null);

        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.joywork_group_list_item, viewGroup, false);
        mContext = view.getContext();
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        final ProjectList.Content.List.Groups groups = mList.get(i);
        if (TextUtils.isEmpty(groups.getTitle())) {
            viewHolder.tvGroupName.setText(R.string.joywork_task_link_untitled_group);
        } else
            viewHolder.tvGroupName.setText("" + groups.getTitle());

        if (groups.getGroupId().equals(mCurrentSelectGroup)) {
            viewHolder.iftvCheck.setVisibility(View.VISIBLE);
            viewHolder.tvGroupName.setTypeface(Typeface.DEFAULT_BOLD);
        } else {
            viewHolder.iftvCheck.setVisibility(View.INVISIBLE);
            viewHolder.tvGroupName.setTypeface(Typeface.DEFAULT);
        }
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCurrentSelectGroup = groups.getGroupId();
                mCurrentSelectProject = new JoyWorkDetail.Project(groups.getGroupId(), groups.getTitle(), mProjectName, groups.getProjectId(), null);
                notifyDataSetChanged();
            }
        });

    }

    @Override
    public int getItemCount() {
        return mList.size();
    }


    public String getCurrentSelectGroup() {
        return mCurrentSelectGroup;
    }

    public JoyWorkDetail.Project getmCurrentSelectProject() {
        return mCurrentSelectProject;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvGroupName;
        IconFontView iftvCheck;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvGroupName = itemView.findViewById(R.id.tv_group_name);
            iftvCheck = itemView.findViewById(R.id.iftv_check);
        }
    }
}