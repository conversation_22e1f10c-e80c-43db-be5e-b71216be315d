package com.jd.oa.joywork.dialog

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.util.Log
import android.view.Gravity
import android.view.WindowManager
import androidx.appcompat.app.AppCompatDialog
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.TabletUtil

class DialogHelper(private val dialog: AppCompatDialog) {

    private val r = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Log.e("TAG_dialog", "onContentChanged")
            dialog.window?.apply {
                decorView.setBackgroundColor(Color.TRANSPARENT)
                setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
                val layoutParams = attributes
                layoutParams.width = CommonUtils.getScreentWidth(context) * 62 / 75
                layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
                layoutParams.gravity = Gravity.CENTER
                attributes = layoutParams
            }
        }
    }

    fun register() {
        val filter = IntentFilter(TabletUtil.ACTION_SPLIT_MODE_CHANGE)
        LocalBroadcastManager.getInstance(dialog.context).registerReceiver(r, filter)
    }

    fun unregister() {
        LocalBroadcastManager.getInstance(dialog.context).unregisterReceiver(r)
    }
}