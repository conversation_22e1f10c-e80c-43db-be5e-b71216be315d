package com.jd.oa.joywork.utils

import android.content.Context
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel
import com.jd.oa.joywork.isLegalString
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.LocaleUtils

object JoyWorkNetConfig {
    const val JOY_WORK_JD = "https://joywork.jd.com"
    const val JOY_WORK_JD_PRE = "https://joywork-pre.jd.com"
    const val JOY_WORK_JD_TEST = "http://joywork-test.jd.com"

    val NET_TEST = 1
    val NET_PRE = 2
    val NET_OFFICIAL = 3

    private fun getNetType(): Int {
        val netS = PreferenceManager.UserInfo.getNetEnvironment()
        val ans = when (netS) {
            NetEnvironmentConfigModel.PROD -> {
                NET_OFFICIAL
            }

            NetEnvironmentConfigModel.PREV -> {
                NET_PRE
            }

            NetEnvironmentConfigModel.TEST -> {
                NET_TEST
            }

            else -> {
                NET_TEST
            }
        }
        return ans
    }

    /**
     * 获取石墨文档域名
     */
    fun getJoyWorkHost(): String {
        return when (getNetType()) {
            NET_TEST -> JOY_WORK_JD_TEST
            NET_OFFICIAL -> JOY_WORK_JD
            NET_PRE -> JOY_WORK_JD_PRE
            else -> JOY_WORK_JD_PRE
        }
    }

    fun getGoalCommentUrl(goalId: String, context: Context, commentId: String?): String {
        var url = (getJoyWorkHost() + "/mobile-okr-comment-page?appName=jme&goalId=" + goalId
                + "&lang=" + LocaleUtils.getUserSetLocaleStr(context))
        if (commentId.isLegalString()) {
            url = "${url}&commentId=$commentId"
        }
        return url
    }

    fun getKrCommentUrl(goalId: String, context: Context, commentId: String?): String {
        var url = (getJoyWorkHost() + "/mobile-okr-comment-page?appName=jme&krId=" + goalId
                + "&lang=" + LocaleUtils.getUserSetLocaleStr(context))
        if (commentId.isLegalString()) {
            url = "${url}&commentId=$commentId"
        }
        return url
    }

    fun getWorkHistory(taskId: String, context: Context): String {
        return (getJoyWorkHost() + "/operation-record2?appName=jme&taskId=" + taskId
                + "&lang=" + LocaleUtils.getUserSetLocaleStr(context))
    }

    fun getGoalDetailUrl(targetId: String, context: Context): String {
        return (getJoyWorkHost() + "/mobile-goal-detail-page?appName=jme&goalId=" + targetId
                + "&lang=" + LocaleUtils.getUserSetLocaleStr(context))
    }

    fun getKrDetailUrl(krId: String, context: Context): String {
        return (getJoyWorkHost() + "/mobile-kr-detail-page?appName=jme&krId=" + krId
                + "&lang=" + LocaleUtils.getUserSetLocaleStr(context))
    }

    fun getProjectDetailUrl(projectId: String): String {
        return (getJoyWorkHost() + "/external/im-embed-todo-list/?openLeftSide=0&projectId=" + projectId)
    }

    fun getWorkCreateUrl(viewId: String? = "", viewType: String? = "", context: Context): String {
        return (getJoyWorkHost() + "/offline/external/mobile-create-task-page/?viewId=$viewId&viewType" +
                "=$viewType" + "&lang=" + LocaleUtils.getUserSetLocaleStr(context))
    }

    fun getWorkDetailUrl(taskId: String? = "", commentId: String? = "", context: Context): String {
        return (getJoyWorkHost() + "/offline/external/mobile-task-detail-page/?taskId=$taskId&lang" +
                "=${LocaleUtils.getUserSetLocaleStr(context)}" + "&commentId=$commentId")
    }

    fun getJoyWorkIMAppId(): String {
        return when (getNetType()) {
            NET_TEST -> "AuB2nflqcmtJlVWHR1QxY"
            NET_PRE -> "AuB2nflqcmtJlVWHR1QxY"
            NET_OFFICIAL -> "2rspPOT7jgIhkGz48OTOQ"
            else -> "2rspPOT7jgIhkGz48OTOQ"
        }
    }
}