package com.jd.oa.joywork.detail.data.entity;

import com.jd.oa.model.service.im.dd.entity.DeptInfo;

import java.io.Serializable;

/**
 * Auto-generated: 2021-07-06 18:26:51
 */

@SuppressWarnings({"unused", "RedundantSuppression"})
public class Owner implements Serializable {

    private String app;
    private String realName;
    private String emplAccount;
    private String headImg;
    private String teamId;
    private String sex;
    private String enName;
    private String userRole;
    private DeptInfo departmentInfo; // 为兼容旧版本 deptInfo，
    private String userId;
    private String taskId;
    private String ddAppId;

    // 当前执行人对待办的完成状态
    public Integer taskStatus;
    // 当前执行人对待办的已读未读状态
    public Integer readStatus;
    // 岗位
    public String titleName;
    // 是否是真负责人，1 是负责人
    public Integer chief;

    public void setApp(String app) {
        this.app = app;
    }

    public String getApp() {
        return app;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRealName() {
        return realName;
    }

    public void setEmplAccount(String emplAccount) {
        this.emplAccount = emplAccount;
    }

    public String getEmplAccount() {
        return emplAccount;
    }

    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }

    public String getHeadImg() {
        return headImg;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getEnName() {
        return enName;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getUserRole() {
        return userRole;
    }

    public DeptInfo getDepartmentInfo() {
        return departmentInfo;
    }

    public void setDepartmentInfo(DeptInfo departmentInfo) {
        this.departmentInfo = departmentInfo;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public String getDdAppId() {
        return ddAppId;
    }

    public void setDdAppId(String ddAppId) {
        this.ddAppId = ddAppId;
    }

    public void setToChief() {
        this.chief = 1;
    }

    public void cancelChief() {
        this.chief = 0;
    }
}