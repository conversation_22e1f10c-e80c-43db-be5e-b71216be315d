package com.jd.oa.joywork.self.base

import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.team.AbsRepoCallback
import com.jd.oa.joywork.team.bean.ProjectGroups
import com.jd.oa.joywork.team.bean.ResultWithoutGroup

interface ISelfListRepo {
    fun init(
        callback: JoyWorkVMCallback,
        status: TaskStatusEnum,
        role: TaskUserRole,
        filter: FilterValue,
        sort: SortValue,
        reader: SelfListCacheReader?,
        writer: SelfListCacheWriter?
    )

    fun loadMoreWithGroup(
        blockType: Int,
        offset: Int,
        status: TaskStatusEnum,
        role: TaskUserRole,
        filter: FilterValue,
        sort: SortValue,
        callback: JoyWorkVMCallback,
    )

    fun listPriority(
        type: Int,
        projectId: String,
        filter: FilterValue,
        sort: SortValue,
        offset: Int,
        status: TaskStatusEnum,
        callback: AbsRepoCallback<ResultWithoutGroup>
    )

    fun listFinishTasks(
        role: TaskUserRole,
        offset: Int,
        callback: JoyWorkVMCallback,
    )

    fun listMyCreate(
        role: TaskUserRole,
        offset: Int,
        status: TaskStatusEnum,
        callback: JoyWorkVMCallback,
    )

    fun listCustomGroup(
        filter: FilterValue,
        sort: SortValue,
        type: Int,
        projectId: String,
        taskStatus: TaskStatusEnum,
        callback: AbsRepoCallback<ProjectGroups>
    )
}