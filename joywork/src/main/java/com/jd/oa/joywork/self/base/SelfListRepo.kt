package com.jd.oa.joywork.self.base

import com.google.gson.Gson
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.team.AbsRepoCallback
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.ProjectGroups
import com.jd.oa.joywork.team.bean.ResultWithoutGroup
import com.jd.oa.joywork.ui.JoyWorkViewModel
import org.json.JSONObject

object SelfListRepo : ISelfListRepo {

    override fun init(
        callback: JoyWorkVMCallback,
        status: TaskStatusEnum,
        role: TaskUserRole,
        filter: FilterValue,
        sort: SortValue,
        reader: SelfListCacheReader?,
        writer: SelfListCacheWriter?
    ) {
        val cacheStr = reader?.read()
        if (cacheStr.isLegalString() && reader?.interceptNetwork().isTrue()) {
            kotlin.runCatching {
                val obj = JSONObject(cacheStr!!)
                val wrapper = Gson().fromJson(obj.getString("content"), JoyWorkWrapper::class.java)
                callback.call(wrapper, cacheStr)
            }
            return
        }

        val realCallback = if (writer != null) object : JoyWorkVMCallback() {
            override fun call(wrapper: JoyWorkWrapper, rawData: String) {
                writer.write(rawData)
                callback.call(wrapper, rawData)
            }

            override fun onError(msg: String) {
                callback.onError(JoyWorkEx.filterErrorMsg(msg))
            }
        } else callback

        JoyWorkRepo.list(realCallback, filter, sort) {
            val list = arrayOf(
                RegionTypeEnum.OVERDUE,
                RegionTypeEnum.TODAY,
                RegionTypeEnum.NEXT_7DAYS,
                RegionTypeEnum.FUTURE,
                RegionTypeEnum.INBOX
            )
            setBlockTypeAndLimit(list) {
                ProjectRepo.PAGE_LIMIT
            }
            setTaskStatus(status)
            setRole(role)
        }
    }

    override fun loadMoreWithGroup(
        blockType: Int,
        offset: Int,
        status: TaskStatusEnum,
        role: TaskUserRole,
        filter: FilterValue,
        sort: SortValue,
        callback: JoyWorkVMCallback,
    ) {
        JoyWorkRepo.list(callback, filter, sort) {
            setBlockType(arrayOf(RegionTypeEnum.getObj("$blockType")))
            setTaskStatus(status)
            setRole(role)
            setOffset(offset)
        }
    }

    override fun listFinishTasks(role: TaskUserRole, offset: Int, callback: JoyWorkVMCallback) {
        JoyWorkRepo.listFinish(offset, role, TaskStatusEnum.FINISH, RegionTypeEnum.FINISH, callback)
    }

    override fun listPriority(
        type: Int,
        projectId: String,
        filter: FilterValue,
        sort: SortValue,
        offset: Int,
        status: TaskStatusEnum,
        callback: AbsRepoCallback<ResultWithoutGroup>
    ) {
        if (!projectId.isValidProjectId()) {
            callback.result(null, "", false)
            return
        }
        ProjectRepo.listProjectWorkWithoutGroup(
            offset,
            projectId,
            status.code,
            filter,
            sort,
            type,
            callback
        )
    }

    override fun listMyCreate(
        role: TaskUserRole,
        offset: Int,
        status: TaskStatusEnum,
        callback: JoyWorkVMCallback
    ) {
        JoyWorkRepo.listMyCreate(offset, role, status, callback)
    }

    override fun listCustomGroup(
        filter: FilterValue,
        sort: SortValue,
        type: Int,
        projectId: String,
        taskStatus: TaskStatusEnum,
        callback: AbsRepoCallback<ProjectGroups>
    ) {
        if (!projectId.isValidProjectId()) {
            callback.result(null, "", false)
            return
        }
        ProjectRepo.listProjectWork(
            projectId,
            status = taskStatus.code,
            filterValue = filter,
            sort,
            type,
            callback
        )
    }

    private fun String.isValidProjectId(): Boolean {
        if (!isLegalString()) {
            return false
        }
        if (startsWith("_\$_") || endsWith("_\$_")) {
            return false
        }
        return true
    }
}