package com.jd.oa.joywork.group

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.joinLegalString
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.argb
import com.jd.oa.utils.gone
import kotlinx.android.synthetic.main.jdme_joywork_urge_list_item.view.*
import kotlinx.android.synthetic.main.joywork_select_group_roster_header.view.*

/**
 * @param selectedCallback 返回值： 0 选中红色，可取消选中；1 未选中，可点击选中；其余，灰色，不可点击
 * @param vhAttachedChange If there is a mass of members in the current group, we shouldn't get all their info at one time.
 * The most appropriate way is to only request someone's information when he/she is displayed on the screen.
 * This parameter is added for this function.
 */
class GroupRosterAdapter(
    val context: SelectGroupRosterActivity,
    val owners: List<MemberEntityJd>,
    private val vhAttachedChange: (MemberEntityJd, Boolean) -> Unit,
    val selectedCallback: ((MemberEntityJd) -> Int)? = null,
    val selectChangeListener: ((MemberEntityJd) -> Unit)? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val click = View.OnClickListener {
        val user = it.tag as MemberEntityJd
        val r = selectedCallback?.invoke(user) ?: -1
        if (r == 0 || r == 1) {
            selectChangeListener?.invoke(user)
        }
    }

    inner class HeaderVH(view: View) : RecyclerView.ViewHolder(view) {
        fun bind() {
            itemView.selAllContainer.setOnClickListener {
                context.selectAll()
            }
            itemView.mOuter.setOnClickListener {
                context.outerClick()
            }
            handleSelCb(context.isAll(), itemView.mSelCbAll)
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        fun bind(joyWorkUser: MemberEntityJd) {
            itemView.hat.gone()
            itemView.name.text = joyWorkUser.name ?: joyWorkUser.mId ?: ""
            itemView.department.text =
                listOf(joyWorkUser.directDepartment, joyWorkUser.titleName).joinLegalString()
            JoyWorkViewItem.avatar(itemView.mAvatarView, joyWorkUser.avatar)
            handleSelCb2(selectedCallback?.invoke(joyWorkUser) ?: 0, itemView.mSelCb)
            itemView.tag = joyWorkUser
            itemView.setOnClickListener(<EMAIL>)
        }

    }

    private fun handleSelCb2(sel: Int, view: TextView) {
        when (sel) {
            0 -> {
                view.setText(R.string.icon_padding_checkcircle)
                view.argb("#FE3B30")
            }
            1 -> {
                view.setText(R.string.icon_prompt_circle)
                view.argb("#8F959E")
            }
            else -> {
                view.setText(R.string.icon_padding_checkcircle)
                view.argb("#CDCDCD")
            }
        }
    }

    private fun handleSelCb(sel: Boolean, view: TextView) {
        if (sel) {
            view.setText(R.string.icon_padding_checkcircle)
            view.argb("#FE3B30")
        } else {
            view.setText(R.string.icon_prompt_circle)
            view.argb("#8F959E")
        }
    }

    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        super.onViewAttachedToWindow(holder)
        if (holder.itemView.tag is MemberEntityJd) {
            vhAttachedChange.invoke(holder.itemView.tag as MemberEntityJd, true)
        }
    }

    override fun onViewDetachedFromWindow(holder: RecyclerView.ViewHolder) {
        super.onViewDetachedFromWindow(holder)
        if (holder.itemView.tag is MemberEntityJd) {
            vhAttachedChange.invoke(holder.itemView.tag as MemberEntityJd, false)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == 1) {
            // header 部分
            val view =
                (context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).inflate(
                    R.layout.joywork_select_group_roster_header,
                    parent,
                    false
                )
            return HeaderVH(view)
        }
        val view =
            (context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).inflate(
                R.layout.jdme_joywork_urge_list_item,
                parent,
                false
            )
        return VH(view)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is VH) {
            holder.bind(owners[position - 1])
        } else if (holder is HeaderVH) {
            holder.bind()
        }
    }

    override fun getItemViewType(position: Int): Int {
        if (position == 0) {
            return 1
        }
        return 2
    }

    override fun getItemCount(): Int {
        return owners.size + 1
    }
}