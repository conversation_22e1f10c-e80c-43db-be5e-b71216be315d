package com.jd.oa.joywork.self.strategy.selflist

import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.self.base.SelfListViewModel
import com.jd.oa.joywork.team.TeamDetailViewModel

class StatusStrategyHeader(
    next: StrategyBase?,
    itf: StrategyBaseItf,
    context: FragmentActivity,
    sortFilterStatusViewModel: TeamDetailViewModel,
    viewModel: SelfListViewModel
) : StrategyBase(next, itf, context, sortFilterStatusViewModel, viewModel) {
    override fun canHandle(): Boolean {
        return false
    }

    override fun needLoadMore(): Boolean {
        return false
    }

    override fun onGetList(userRole: TaskUserRole) {

    }

    override fun onHandleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return ArrayList()
    }
}