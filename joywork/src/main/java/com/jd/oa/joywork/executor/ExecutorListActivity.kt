package com.jd.oa.joywork.executor

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromMembers
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.detail.key
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.argb
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap
import kotlin.collections.HashSet

/**
 * 执行人列表
 */
class ExecutorListActivity : AppCompatActivity(), SetOwnerFragmentCallback {

    companion object {

        private val repo = HashMap<String, Any>()

        fun inflateIntent(
            intent: Intent,
            executors: List<Members>,
            sessionId: String?,
            resultCallback: (ms: ArrayList<JoyWorkUser>) -> Unit
        ) {
            OneTimeDataRepo.data = executors
            val key = "${System.currentTimeMillis()}"
            repo[key] = resultCallback
            intent.putExtra("repoId", key)
            if (sessionId.isLegalString()) {
                intent.putExtra("sessionId", sessionId)
            }
        }

        private fun ExecutorListActivity.getSessionId(): String? {
            return try {
                intent!!.getStringExtra("sessionId")
            } catch (e: Throwable) {
                null
            }
        }

        private fun ExecutorListActivity.getResultCallback(): ((ArrayList<JoyWorkUser>) -> Unit)? {
            return try {
                val key = intent!!.getStringExtra("repoId")
                repo[key!!] as? (ArrayList<JoyWorkUser>) -> Unit
            } catch (e: Throwable) {
                null
            }
        }

        private fun ExecutorListActivity.clearCallback() {
            try {
                val key = intent!!.getStringExtra("repoId")
                repo.remove(key)
            } catch (e: Throwable) {
            }
        }

        private fun getExecutors(): ArrayList<Members> {
            return (OneTimeDataRepo.data as? List<*>)?.run {
                return if (isEmpty()) {
                    ArrayList()
                } else {
                    if (first() is Members) {
                        ArrayList(this as List<Members>)
                    } else {
                        ArrayList()
                    }
                }
            } ?: ArrayList()
        }

    }

    /**
     * 当前界面所有用户
     */
    private val mAllUsers: ArrayList<JoyWorkUser> = ArrayList()
    private val mSelectUsers = HashSet<String>()

    private var mSetOwnerFragment: SetOwnerFragment? = null

    private val mSelAllClick = View.OnClickListener {
        if (mSelectUsers.size == mAllUsers.size) {
            mSetOwnerFragment?.changeSelAll(false)
        } else {
            mSetOwnerFragment?.changeSelAll(true)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getExecutors().forEach {
            val user = JoyWorkUser()
            user.fromMembers(it)
            mAllUsers.add(user)
            mSelectUsers.add(it.key())
        }
        hideAction()
        setContentView(R.layout.joywork_executor_activity)
        findViewById<View>(R.id.back).setOnClickListener { finish() }
        findViewById<View>(R.id.add).setOnClickListener {
            add()
        }
        mSetOwnerFragment = SetOwnerFragment.getInstance()
        supportFragmentManager.beginTransaction()
            .add(R.id.mFilterContainer, mSetOwnerFragment!!)
            .commit()
        findViewById<View>(R.id.mSelCb).setOnClickListener(mSelAllClick)
        findViewById<View>(R.id.mSelAll).setOnClickListener(mSelAllClick)
    }

    private fun filterSelectUsers(): ArrayList<JoyWorkUser> {
        val ans = ArrayList<JoyWorkUser>()
        mAllUsers.forEach {
            if (mSelectUsers.contains(it.key())) {
                ans.add(it)
            }
        }
        return ans
    }

    private fun add() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.CREATE_OWNER_LIST,
            JoyWorkConstant.CREATE_OWNER_LIST
        )
        val selectedOuter = ArrayList<MemberEntityJd>()
        val users = filterSelectUsers()
        if (users.isNotEmpty()) {
            users.forEach { m ->
                m.emplAccount?.apply {
                    val jd = MemberEntityJd()
                    jd.fromUser(m)
                    selectedOuter.add(jd)
                }
            }
        }
        ExecutorUtils.selectExecutors(
            this,
            selectedOuter,
            getSessionId(),
            false,
            { selected: ArrayList<MemberEntityJd?>? ->
                if (selected.isLegalList()) {
                    val membersNet: ArrayList<JoyWorkUser> = ArrayList()
                    val set = HashSet<String>()
                    val keys = mAllUsers.map {
                        it.key()
                    }
                    for (memberEntityJd in selected!!) {
                        if (memberEntityJd != null) {
                            if (!keys.contains(memberEntityJd.key())) {
                                // 新勾选的人
                                val member = JoyWorkUser()
                                member.fromDD(memberEntityJd)
                                member.userRole = TaskUserRole.EXECUTOR.code
                                member.teamId = JoyWorkUser.DEFAULT_TEAM_ID
                                membersNet.add(member)
                                mAllUsers.add(member)
                                mSelectUsers.add(member.key())
                            } else {
                                // 已有的（有可能未被勾选，所以此处需要重新勾选上）
                                mSelectUsers.add(memberEntityJd.key())
                                set.add(memberEntityJd.key())
                            }
                        }
                    }
                    handleSelAll()
                    if (membersNet.isEmpty() && set.isEmpty()) {
                        return@selectExecutors
                    }
                    mSetOwnerFragment?.appendAndSelect(membersNet, set)
                }
            },
            {

            })
    }

    override fun finish() {
        val users = filterSelectUsers()
        setResult(Activity.RESULT_OK)
        getResultCallback()?.invoke(users)
        clearCallback()
        super.finish()
    }

    // called from [SetOwnerFragmentCallback] start
    override fun getUsers(): List<JoyWorkUser> {
        val ans = ArrayList<JoyWorkUser>()
        ans.addAll(mAllUsers)
        return ans
    }

    override fun remove(joyWorkUser: JoyWorkUser) {
        val key = joyWorkUser.key()
        mSelectUsers.remove(key)
        handleSelAll()
    }

    override fun add(joyWorkUser: JoyWorkUser) {
        val key = joyWorkUser.key()
        if (mSelectUsers.contains(key)) {
            return
        }
        mSelectUsers.add(key)
        handleSelAll()
    }

    override fun changeOwner(joyWorkUser: JoyWorkUser?) {
        val key = joyWorkUser?.key() ?: "-"
        mAllUsers.forEach {
            if (Objects.equals(it.key(), key)) {
                it.setToChief()
            } else {
                it.cancelChief()
            }
        }
    }

    // called from [SetOwnerFragmentCallback] end
    private fun handleSelAll() {
        val tv = findViewById<TextView>(R.id.mSelCb)

        if (mSelectUsers.size == mAllUsers.size) {
            tv.setText(R.string.icon_padding_checkcircle)
            tv.argb("#FE3B30")
        } else {
            tv.argb("#CDCDCD")
            tv.setText(R.string.icon_prompt_circle)
        }
    }
}