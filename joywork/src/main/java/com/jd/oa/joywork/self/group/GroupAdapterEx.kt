package com.jd.oa.joywork.self.group

import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup

/**
 * 产生不同的 ViewType
 */
class SingleViewType<T : RecyclerView.ViewHolder>(
    groups: List<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    val initV: Int
) {

    // 加载更多的 viewType。不同组的使用不同的 ViewType，简单处理
    private val viewType = HashMap<ExpandableGroup<JoyWorkTitle, JoyWork>, Int>().apply {
        groups.forEachIndexed { index, expandableGroup ->
            this[expandableGroup] = initV + index
        }
    }

    fun getViewType(group: ExpandableGroup<JoyWorkTitle, JoyWork>): Int {
        if (!viewType.containsKey(group) || viewType[group] == null) {
            viewType[group] = initV + viewType.size
        }
        return viewType[group]!!
    }

    fun contain(target: Int): <PERSON>olean {
        return viewType.containsValue(target)
    }

    // 自己缓存对应的加载更多的 ViewHolder。key 为对应的 viewType
    private val vhMap = HashMap<Int, T>(groups.size)

    fun getVH(type: Int, init: () -> T): T {
        val ret = vhMap[type] ?: init.invoke()
        vhMap[type] = ret
        return ret
    }

    fun vhInvoke(targetGroup: ExpandableGroup<JoyWorkTitle, JoyWork>?, action: T.() -> Unit) {
        if (targetGroup == null)
            return
        val type = getViewType(targetGroup)
        vhMap[type]?.action()
    }
}