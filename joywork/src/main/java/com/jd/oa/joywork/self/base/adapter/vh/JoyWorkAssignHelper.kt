package com.jd.oa.joywork.self.base.adapter.vh

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.self.group.JoyWorkGroupAdapter
import com.jd.oa.joywork.BlockTypeEnum
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.TimeTitleJoyWork
import com.jd.oa.joywork.isJoyWork
import com.jd.oa.joywork.self.base.adapter.SelfBaseFragmentAdapter
import com.jd.oa.utils.DateUtils

/**
 * 用于处理 [JoyWorkGroupAdapter] 中安排后界面刷新逻辑
 */
object JoyWorkAssignHelper {
    fun assignSuccess(
        context: Context,
        srcViewHolder: RecyclerView.ViewHolder,
        adapter: SelfBaseFragmentAdapter,
        task: JoyWork,
        target: BlockTypeEnum
    ) {
        val targetGroup = adapter.groups.first {
            it.getId() == target.code
        }
        val srcGroup = task.expandableGroup

        // 源、目录都没有计划组
        if (target != BlockTypeEnum.IN_PLAN && task.expandableGroup.id != BlockTypeEnum.IN_PLAN.code) {
            // 同一组
            if (srcGroup.id == targetGroup.getId()) {
                // 1) 是第一个，不需要移动
                if (JoyWorkEx.isFirstJoyWork(srcGroup.realItems, task)) {
                    return
                } else {
                    // 不是第一个，需要移动
                    srcGroup.realItems.remove(task)
                    adapter.processor.addGroup(srcGroup, task, 0)
                    adapter.notifyDataSetChanged()
                }
            } else { // 不同组。源组删除，目录组添加到最上面
                // 将 src 从旧组中删除
                srcGroup.realItems.remove(task)
                // 插入到指定的 group 中
                adapter.processor.addGroup(targetGroup, task, 0)
                adapter.notifyDataSetChanged()
            }
        } else if (srcGroup.id == targetGroup.getId()) { // 源、目录组都是计划组
            val oldIndex = srcGroup.realItems.indexOf(task)
            // 先移除，再插入
            var needRemoveTimeHeader = false
            if (srcGroup.realItems[oldIndex - 1] is TimeTitleJoyWork) { // 有可能要移除时间头
                needRemoveTimeHeader = JoyWorkEx.isLastJoyWork(
                    srcGroup.realItems,
                    task
                ) || srcGroup.realItems[oldIndex + 1] is TimeTitleJoyWork
            }
            if (needRemoveTimeHeader) {
                srcGroup.realItems.removeAt(oldIndex - 1)
            }
            srcGroup.realItems.remove(task)
            // 插入到新的位置
            // 新时间分隔
            val format = JoyWorkEx.getTimeTitleString(task.planTime, context)
            val newTitleJoyWork = TimeTitleJoyWork(format, task.planTime)

            var insertPos = -1
            var did = false
            for (index in 0 until srcGroup.realItems.size) {
                val joyWork = srcGroup.realItems[index]
                if (joyWork is TimeTitleJoyWork) {
                    if (DateUtils.isSameDay(joyWork.titleTime, task.planTime)) {
                        did = true
                        insertPos = index + 1
                        srcGroup.realItems.add(insertPos, task)
                        srcGroup.notifyItemChange()
                        break
                    } else if (DateUtils.isAfter(joyWork.titleTime, task.planTime)) {
                        did = true
                        insertPos = index
                        srcGroup.realItems.add(insertPos, newTitleJoyWork)
                        srcGroup.realItems.add(insertPos + 1, task)
                        srcGroup.notifyItemChange()
                        break
                    }
                }
            }
            /*
            安排到计划组的最后，有两种情况：
            1）如果数据已加载完，可直接加载到最后，此时可保证数据顺序是正确的
            2) 如果数据未加载完，此时无法保证数据的顺序是正确的，故不可直接追加到最后
             */
            if (!did && srcGroup.title.count - 1 <= JoyWorkEx.countJoyWork(srcGroup.realItems)) {
                srcGroup.realItems.add(newTitleJoyWork)
                srcGroup.realItems.add(task)
                srcGroup.notifyItemChange()
            }
            adapter.processor.refreshData()
            adapter.notifyDataSetChanged()
        } else if (srcGroup.id == BlockTypeEnum.IN_PLAN.code) { // 从计划组安排到别的组
            // 插入到别的组
            targetGroup.getRealItems().add(0, task)
            targetGroup.notifyItemChange()

            // 从计划组删除
            val index = srcGroup.realItems.indexOf(task)
            var needRemoveTimeHeader = false
            if (srcGroup.realItems[index - 1] is TimeTitleJoyWork) { // 有可能要移除时间头
                needRemoveTimeHeader = JoyWorkEx.isLastJoyWork(
                    srcGroup.realItems,
                    task
                ) || srcGroup.realItems[index + 1] is TimeTitleJoyWork
            }
            if (needRemoveTimeHeader) {
                srcGroup.realItems.removeAt(index - 1)
            }
            srcGroup.realItems.remove(task)
            adapter.processor.refreshData()
            adapter.notifyDataSetChanged()
        } else { // 插入到计划组
            var joyWorkCount = 0
            var targetTimeTitleJoyWork: TimeTitleJoyWork? = null
            for (it in targetGroup.getRealItems()) {
                if (it.isJoyWork()) joyWorkCount++
                if (it is TimeTitleJoyWork && DateUtils.isSameDay(it.titleTime, task.planTime)) {
                    targetTimeTitleJoyWork = it
                }
            }
            // 新安排到计划组时，该组需要按时间排序，所以不能直接插入到组的最前面，就需要判断是否能直接插入到该组。以下情况可以
            // 1）数据已加载完; 2）要插入的时间组已存在
            if (targetTimeTitleJoyWork != null || targetGroup.getTitle().count <= joyWorkCount) {
                // 最后一个计划时间 <= task.planTime 的 JoyWork
                val lastIndex = JoyWorkEx.lastIndex(targetGroup.getRealItems(), task) {
                    it.isJoyWork() && (DateUtils.isAfter(
                        task.planTime,
                        it.planTime
                    ) || DateUtils.isSameDay(it.planTime, task.planTime))
                }

                var count = 1
                var insertPos = -1
                // 应被插入到最前面
                if (lastIndex == -1) {
                    count = 2
                    insertPos = 0
                } else {
                    val lastJoyWork = targetGroup.getRealItems()[lastIndex]
                    // 添加到当前组的前面
                    if (DateUtils.isSameDay(lastJoyWork.planTime, task.planTime)) {
                        count = 1
                        // 往回寻找插入位置
                        for (i in lastIndex downTo 0) {
                            if (targetGroup.getRealItems()[i] is TimeTitleJoyWork) {
                                insertPos = i + 1
                                break
                            }
                        }
                    } else {
                        count = 2
                        insertPos = lastIndex + 1
                    }
                }
                if (count == 2) {
                    val format = JoyWorkEx.getTimeTitleString(task.planTime, context)
                    val newTitleJoyWork = TimeTitleJoyWork(format, task.planTime)
                    targetGroup.getRealItems().add(insertPos, newTitleJoyWork)
                    targetGroup.getRealItems().add(insertPos + 1, task)

                } else {
                    targetGroup.getRealItems().add(insertPos, task)
                }
                targetGroup.notifyItemChange()
            }

            // 从目标组删除
            srcGroup.realItems.remove(task)

            adapter.processor.refreshData()
            adapter.notifyDataSetChanged()
            // 先移掉当前位置
//            adapter.notifyItemRemoved(srcViewHolder.adapterPosition)
        }
        // 从一组移动到另一组，需要更新数量
        if (srcGroup.id != targetGroup.getId()) {
            srcGroup.title.count--
            targetGroup.getTitle().count++
            adapter.updateTitleCount(srcGroup)
            adapter.updateTitleCount(targetGroup)
        }
    }
}