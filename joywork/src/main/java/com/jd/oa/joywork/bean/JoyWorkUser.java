package com.jd.oa.joywork.bean;

import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.model.service.im.dd.entity.DeptInfo;
import com.jd.oa.model.service.im.dd.entity.TitleInfo;
import com.jd.oa.model.TenantCode;
import com.jd.oa.preference.PreferenceManager;

import java.io.Serializable;
import java.util.HashMap;

public class JoyWorkUser implements Serializable {
    //TODO teamid硬编码
    public static final String DEFAULT_TEAM_ID = "********";
    // 从咚咚新建时，才传入该值
    public static final String BIZ_CODE = JoyWorkConstant.BIZ_CODE_DD;

    public String app;
    public String ddAppId;
    public String realName;
    public String emplAccount; // erp
    public String teamId = DEFAULT_TEAM_ID; // ********
    public String userId; // 类似 "oVbRMsY88yWGd4Bt109PG" 的字符串，但手机端一般拿不到
    public String headImg;
    public DeptInfo deptInfo;
    public TitleInfo titleInfo;
    // 临时存储一下，除催办外，其余地方不可使用
    public String taskId;
    public Integer taskStatus;
    // 角色
    public int userRole;

    public Integer chief;

    // 岗位信息
    public String titleName;
    public String deptName; //岗位信息

    public HashMap<String, Object> toSimpleMap() {
        HashMap<String, Object> map = new HashMap<>();
        if (TenantCode.Companion.getByDDAppId(ddAppId) != null
                || TenantCode.Companion.getByDDAppId(app) != null) {
            map.put("emplAccount", emplAccount);
            map.put("ddAppId", ddAppId != null ? ddAppId : (app == null ? MultiAppConstant.APPID : app));
        } else {
            map.put("userId", userId);
            map.put("teamId", ddAppId != null ? ddAppId: (app != null ? app : teamId));
        }
        if (chief != null && chief != 0) {
            map.put("chief", chief);
        }
        return map;
    }

    public HashMap<String, Object> toMap() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("teamId", teamId == null ? DEFAULT_TEAM_ID : teamId);
        // 不要问为啥要传三个，兼容
        map.put("app", app == null ? MultiAppConstant.APPID : app);
        map.put("appId", app == null ? MultiAppConstant.APPID : app);
        map.put("ddAppId", ddAppId != null ? ddAppId : (app == null ? MultiAppConstant.APPID : app));
        if (realName != null) {
            map.put("name", realName);
        }
        if (headImg != null) {
            map.put("avatar", headImg);
        }
        if (emplAccount != null) {
            // 不要问为啥要传三个，兼容oy
            map.put("erp", emplAccount);
            map.put("userId", emplAccount);
            map.put("emplAccount", emplAccount);
        }
        if (userId != null) {
            map.put("userId", userId);
        }
        map.put("chief", chief == null ? "0" : chief);
        return map;
    }

    /**
     * 2021年08月16日：
     * 判断是否是同一个人，两种逻辑都可以，但建议第一种
     * emplAccount + ddAppId
     * teamId + userId + app
     */
    public boolean isSamePerson(JoyWorkUser members) {
        return emplAccount != null && emplAccount.equals(members.emplAccount) && ddAppId != null && ddAppId.equals(members.ddAppId);
    }

    public void cancelChief() {
        chief = 0;
    }

    public void setToChief() {
        chief = 1;
    }

    public static JoyWorkUser getSelf() {
        JoyWorkUser user = new JoyWorkUser();
        user.headImg = PreferenceManager.UserInfo.getUserCover();
        user.emplAccount = PreferenceManager.UserInfo.getUserName();
        user.realName = PreferenceManager.UserInfo.getUserRealName();
        user.app = PreferenceManager.UserInfo.getTimlineAppID();
        user.ddAppId = PreferenceManager.UserInfo.getTimlineAppID();
        user.userId = PreferenceManager.UserInfo.getUserId();
        user.teamId = PreferenceManager.UserInfo.getTeamId();
        return user;
    }
}
