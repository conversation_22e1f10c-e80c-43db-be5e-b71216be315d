package com.jd.oa.joywork.detail.ui;

import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.joywork.JoyWorkMsgCenter;
import com.jd.oa.joywork.detail.data.db.UpdateReporter;
import com.jd.oa.joywork.self.DetailReturnParcel;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.joywork.utils.JoyWorkNetConfig;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.Constants;
import com.jd.oa.utils.RemoteConfigExtKt;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import java.util.Map;

@Route({DeepLink.JOY_WORK_DETAIL})
public class TaskDetailActivity extends BaseActivity implements DynamicOperatorListener {
    public static final String KEY_TASK_ID = "taskId";
    public static final String KEY_ACTION_SOURCE = "actionSource";
    private Fragment detailFragment;
    public static final String KEY_BIZ_FROM = "biz_from";
    public static final String KEY_BIZ_OBJ = "biz_obj";

    public static final String KEY_COMMENT_ID = "commentId";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        String taskId = getIntent().getStringExtra(KEY_TASK_ID);
        String actionSource = getIntent().getStringExtra(KEY_ACTION_SOURCE);
        // 修改详情页，需要刷新列表时，使用 UpdateReporter.INSTANCE.reportUpdate() 即可
        try {
            UpdateReporter.INSTANCE.setParcel((DetailReturnParcel) getIntent().getSerializableExtra(KEY_BIZ_OBJ));
        } catch (Exception e) {
            UpdateReporter.INSTANCE.setParcel(null);
        }
        if (taskId == null) {
            try {
                String param = getIntent().getStringExtra("mparam");
                JSONObject object = new JSONObject(param);
                taskId = object.optString(KEY_TASK_ID);
                JSONObject actionSourceObj = object.optJSONObject(KEY_ACTION_SOURCE);
                if (actionSourceObj != null) {
                    actionSource = actionSourceObj.toString();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        if (taskId == null || taskId.isEmpty()) {
            ToastUtils.showToast(R.string.task_operate_fail);
            finish();
        } else {
            setContentView(R.layout.task_detail_activity);
            String commentId = getIntent().getStringExtra(KEY_COMMENT_ID);
            if (RemoteConfigExtKt.grayResourceSwitchEnable(
                    Constants.KEY_JOY_WORK_H5_DETAIL_ENABLE, "0", "1")) {
                detailFragment = initH5CreateFragment(taskId, commentId);
            } else {
                detailFragment = TaskDetailFragment.forTask(taskId, actionSource);
                if (commentId != null) {
                    Bundle bundle = detailFragment.getArguments();
                    if (bundle != null) {
                        bundle.putString(KEY_COMMENT_ID, commentId);
                    }
                }
            }
            if (savedInstanceState == null) {
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.container, detailFragment).commitNow();
            }
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            final View v = getCurrentFocus();
            if (v == null) {
                return super.dispatchTouchEvent(event);
            }
//            System.out.println("getCurrentFocus=" + v.getClass().getName());
            if (v instanceof EditText) {
                Rect outRect = new Rect();
                v.getGlobalVisibleRect(outRect);
//                if (!outRect.contains((int) event.getRawX(), (int) event.getRawY())) {
//                    taskDetailFragment.editTitleFinish();
//                }
            } else if (v.getClass().getName().contains("WebView")) {
                Rect outRect = new Rect();
                v.getGlobalVisibleRect(outRect);
                if (!outRect.contains((int) event.getRawX(), (int) event.getRawY())) {
                    TaskUiUtils.hideKeyboard();
                }
//                Rect r = new Rect();
//                v.getWindowVisibleDisplayFrame(r);
//                int[] location = new int[2];
//                v.getLocationInWindow(location); //获取在当前窗口内的绝对坐标
//                System.out.println("getCurrentFocus----event.getRawY()=" + event.getRawY() + "  r=" + r.top + "  v=" + outRect.top + " location=" + location[1] + " location2=" + location2[1]);
//                if (event.getRawY() < location[1]) {
//                    TaskUiUtils.hideKeyboard();
//                }
            }
        }
        return super.dispatchTouchEvent(event);
    }

    private TaskDetailH5Fragment initH5CreateFragment(String taskId, String commentId) {
        Bundle intent = new Bundle();
        WebBean bean = new WebBean(JoyWorkNetConfig.INSTANCE.getWorkDetailUrl(taskId, commentId, this), "0");
        intent.putParcelable(WebFragment2.EXTRA_WEB_BEAN, bean);
        intent.putString(KEY_TASK_ID, taskId);
        TaskDetailH5Fragment webFragment = new TaskDetailH5Fragment();
        webFragment.setArguments(intent);
        return webFragment;
    }

    public void chooseFileFromJS(final Map<String, Object> params, final Callback2<Boolean> cb) {
        if (detailFragment != null && detailFragment instanceof TaskDetailFragment) {
            ((TaskDetailFragment) detailFragment).chooseFileFromJS(params);
            cb.onSuccess(true, true);

        }
    }

    public void updateWebView() {
        if (detailFragment != null && detailFragment instanceof TaskDetailFragment) {
            ((TaskDetailFragment) detailFragment).updateWebView();
        }
    }

    @Override
    public void finish() {
        String extra = getIntent().getStringExtra(KEY_BIZ_FROM);
        if (JoyWorkConstant.BIZ_DETAIL_FROM_SECTION.equals(extra)) {
            // 从工作台卡片进来，返回的时候直接发通知刷新整体
            JoyWorkMsgCenter.INSTANCE.notifySectionUpdate();
        } else if (extra != null) {
            Intent i = new Intent();
            i.putExtra(KEY_BIZ_FROM, extra);

            if (UpdateReporter.INSTANCE.getParcel() != null) {
                i.putExtra(KEY_BIZ_OBJ, UpdateReporter.INSTANCE.getParcel());
                boolean fromContract = getIntent().getBooleanExtra(JoyWorkTaskDetailContract.CONTRACT_TAG, false);
                if (!fromContract) {
                    UpdateReporter.INSTANCE.setWorkInfo(null);
                }
            }
            setResult(RESULT_OK, i);
        } else {
            setResult(RESULT_OK);
        }
        super.finish();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (detailFragment != null) {
            if (detailFragment instanceof TaskDetailFragment) {
                ((TaskDetailFragment) detailFragment).onJsResult(requestCode, resultCode, data);
            }
            detailFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onBackPressed() {
        if (detailFragment != null && detailFragment instanceof TaskDetailFragment) {
            if (!((TaskDetailFragment) detailFragment).canBack()) {
                return;
            }
        }
        super.onBackPressed();
    }

    @Override
    public void operator(Map<String, Object> param) {
        if (detailFragment != null && detailFragment instanceof DynamicOperatorListener) {
            ((DynamicOperatorListener) detailFragment).operator(param);
        }
    }

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        if (detailFragment != null && detailFragment instanceof DynamicOperatorListener) {
            ((DynamicOperatorListener) detailFragment).registerCallback(requestCode, callBack);
        }
    }
}