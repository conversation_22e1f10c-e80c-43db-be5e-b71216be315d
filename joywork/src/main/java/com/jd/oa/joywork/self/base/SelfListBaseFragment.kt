package com.jd.oa.joywork.self.base

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.jd.oa.around.base.HeaderFooterRecyclerAdapterWrapper
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.ui.dialog.bottomsheet.DialogConfig
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog
import com.jd.oa.ui.dialog.bottomsheet.BottomAction
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail.Project
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.ui.TaskDetailActivity
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.self.group.JoyWorkGroupEx
import com.jd.oa.joywork.self.strategy.selflist.*
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.shortcut.SuccessSnackBar
import com.jd.oa.joywork.table.TableColumnConfig
import com.jd.oa.joywork.table.TableFragment
import com.jd.oa.joywork.table.TableTitle
import com.jd.oa.joywork.table.TableTitleConfig
import com.jd.oa.joywork.team.*
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectGroups
import com.jd.oa.joywork.team.bean.ProjectList
import com.jd.oa.joywork.team.bean.ProjectList.Content.List.Groups
import com.jd.oa.joywork.team.bean.ProjectSortEnum
import com.jd.oa.joywork.team.dialog.CreateDialog
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.joywork.view.JoyWorkLoadMoreFooter
import com.jd.oa.joywork.view.realAdapter
import com.jd.oa.joywork2.list.ProjectAddCallback
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.string
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlin.collections.ArrayList
import kotlin.collections.HashMap

/**
 * 个人待办列表基类。展示列表所有操作，不能与数据层绑定
 */
abstract class SelfListBaseFragment : TableFragment(), TableColumnConfig, SelfListBaseAdapterItf,
    StrategyBaseItf, SelfListSortItf {
    protected var mRv: HRecyclerView? = null
    protected var mSwipeRefreshLayout: SwipeRefreshLayout? = null
    private var mViewModel: SelfListViewModel? = null
    private val mScope = MainScope()
    private var mSelfListViewModel: JoyWorkSelfListViewModel? = null

    private val refreshObserver = { pair: Pair<JoyWorkWrapper?, String?> ->
        mSwipeRefreshLayout?.isRefreshing = false
        if (pair.second.isLegalString()) {
            mRv?.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg = pair.second!!)
        } else if (!pair.first?.clientTasks.isLegalList()) {
            mRv?.adapter = JoyWorkEmptyAdapter.empty(requireContext())
        } else {
            val gs = mStrategyHeader.handleData(pair.first!!)
            val oldAdapter = mRv?.realAdapter()
            val newAdapter = mStrategyHeader.createAdapter(
                gs,
                this@SelfListBaseFragment,
                this@SelfListBaseFragment,
                mRv
            )
            if (oldAdapter is SelfListBaseAdapter && (oldAdapter::class.java == newAdapter::class.java)) {
                oldAdapter.replaceAll(gs)
            } else {
                mRv?.adapter = newAdapter
            }
        }
    }

    private val customGroupObserver = { pair: Pair<ProjectGroups?, String?> ->
        mSwipeRefreshLayout?.isRefreshing = false
        if (pair.second.isLegalString()) {
            mRv?.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg = pair.second!!)
        } else if (!pair.first?.groups.isLegalList()) {
            mRv?.adapter = JoyWorkEmptyAdapter.empty(requireContext())
        } else {
            val data = mStrategyHeader.handleData(pair.first!!)
            mRv?.adapter = mStrategyHeader.createAdapter(
                data,
                this@SelfListBaseFragment,
                this@SelfListBaseFragment,
                getRv()
            )
        }
    }

    private lateinit var mPullUpLoadHelper: PullUpLoadHelper

    private val resultListObserver = { pair: Triple<JoyWorkWrapper?, String?, Boolean> ->
        setLoadMoreAdapter(pair, { first ->
            mStrategyHeader.handleData(first)
        }) {
            mStrategyHeader.createAdapter(
                it, this@SelfListBaseFragment,
                this@SelfListBaseFragment,
                getRv()
            )
        }
    }

    private fun <T : SelfListBaseAdapter> setLoadMoreAdapter(
        pair: Triple<JoyWorkWrapper?, String?, Boolean>,
        dataHandler: (JoyWorkWrapper) -> ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        factory: (gs: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) -> T
    ) {
        mSwipeRefreshLayout?.isRefreshing = false
        val first = pair.first
        if (pair.second.isLegalString()) {
            mRv?.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg = pair.second!!)
        } else if (first == null || !first.clientTasks.isLegalList()) {
            val oldA = mRv.realAdapter()
            val adapter = factory.invoke(ArrayList())
            if (oldA != null && oldA::class.java == adapter::class.java) {
                loadMoreOnceFinish(true)
            } else {
                mRv?.adapter = JoyWorkEmptyAdapter.empty(requireContext())
            }
        } else {
            val gs = dataHandler(first)
            val a = mRv.realAdapter()
            if (a is SelfListBaseAdapter && pair.third.isTrue()) {
                val adapterGroups = a.groups
                val map = HashMap<String, ExpandableGroup<JoyWorkTitle, JoyWork>>()
                adapterGroups.forEach {
                    map[it.id] = it
                }
                gs.forEach {
                    val i = map[it.id]
                    it.ensureRealItems()
                    if (i != null) {
                        val list = i.realItems
                        list.addAll(it.realItems)
                        it.notifyItemChange()
                    } else {
                        adapterGroups.add(it)
                    }
                }
                a.replaceAll(adapterGroups)
            } else if (!gs.isLegalList()) {
                mRv?.adapter = JoyWorkEmptyAdapter.empty(requireContext())
            } else {
                mRv?.adapter = factory.invoke(gs)
                if (first.countJoyWork() >= ProjectRepo.PAGE_LIMIT) {
                    setupLoadMore()
                }
            }
            loadMoreOnceFinish(first.countJoyWork() < ProjectRepo.PAGE_LIMIT)
            // 上拉加载
        }
    }

    private fun setupLoadMore() {
        val rv = mRv ?: return
        mPullUpLoadHelper = PullUpLoadHelper(rv) {
            val a = rv.realAdapter()
            var offset = 0
            val adapterGroups = (a as? SelfListBaseAdapter)?.groups ?: return@PullUpLoadHelper
            adapterGroups.forEach {
                offset += JoyWorkEx.countJoyWork(it.realItems)
            }
            mStrategyHeader.loadMore(offset, getUserRole())
        }
        mPullUpLoadHelper.setLoadFooter(JoyWorkLoadMoreFooter(requireContext()))
        val a = rv.adapter
        val r = rv.realAdapter()
        if (a is HeaderFooterRecyclerAdapterWrapper && r is SelfListBaseAdapter) {
            rv.adapter = SelfListBaseAdapterWrapper(r, a)
        }
    }

    protected open fun onLoadMore(offset: Int) {

    }

    private fun loadMoreOnceFinish(finish: Boolean) {
        if (::mPullUpLoadHelper.isInitialized) {
            if (finish) {
                mPullUpLoadHelper.setComplete()
            } else {
                mPullUpLoadHelper.setLoaded()
            }
        }
    }

    override fun onCreateTableConfig() = this

    private lateinit var mStrategyHeader: StrategyBase

    private val mRefreshReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            mSelfListViewModel?.updateRefresh()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        ensureStrategies()
        mRefreshReceiver observeRiskUpdate requireContext()
        val filter = IntentFilter()
        getListenerAction().forEach {
            filter.addAction(it)
        }
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(mRefreshReceiver, filter)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    private fun ensureStrategies() {
        if (!isAdded || ::mStrategyHeader.isInitialized) {
            return
        }
        val endTimeSortStrategy = EndtimeSortStrategy(
            null,
            this,
            requireActivity(),
            getFilterSortStatusViewModel(),
            getSelfListViewModel()
        )
        val ownerSortStrategy = OwnerSortStrategy(
            endTimeSortStrategy,
            this,
            requireActivity(),
            getFilterSortStatusViewModel(),
            this,
            getSelfListViewModel()
        )
        val projectSortStrategy = ProjectSortStrategy(
            ownerSortStrategy,
            this,
            requireActivity(),
            getFilterSortStatusViewModel(),
            this,
            getSelfListViewModel()
        )
        val prioritySortStrategy = PrioritySortStrategy(
            projectSortStrategy,
            this,
            requireActivity(),
            getFilterSortStatusViewModel(),
            this,
            getSelfListViewModel()
        )
        val customGroupSortStrategy = CustomGroupSortStrategy(
            prioritySortStrategy,
            object : CustomGroupSortStrategyItf {
                override fun getCustomGroupType(): Int {
                    return <EMAIL>()
                }

                override fun getProjectId(): String {
                    return <EMAIL>()
                }
            },
            this,
            requireActivity(),
            getFilterSortStatusViewModel(),
            getSelfListViewModel()
        )
        val finish =
            FinishStatusStrategy(
                customGroupSortStrategy,
                this,
                requireActivity(),
                getFilterSortStatusViewModel(),
                getSelfListViewModel()
            )
        val myCreate = MyCreateStrategy(
            finish,
            this,
            requireActivity(),
            getFilterSortStatusViewModel(),
            getSelfListViewModel()
        )

        mStrategyHeader = StatusStrategyHeader(
            myCreate,
            this,
            requireActivity(),
            getFilterSortStatusViewModel(),
            getSelfListViewModel()
        )
    }

    override fun onSetupView(rv: HRecyclerView, refreshLayout: SwipeRefreshLayout) {
        mRv = rv
        mSwipeRefreshLayout = refreshLayout
        refreshLayout.setOnRefreshListener {
            refreshImmediately()
        }
        ItemTouchHelper(SelfListDrag(rv)).attachToRecyclerView(rv)
        initObserver()
    }

    private fun initObserver() {
        mSelfListViewModel = ViewModelProviders.of(this).get(JoyWorkSelfListViewModel::class.java)
        mSelfListViewModel?.observeRefresh(this) {
            refresh()
        }
        // used to accept network results
        getSelfListViewModel().refreshLiveData.observe(requireActivity(), refreshObserver)
        getSelfListViewModel().resultTasksLiveData.observe(requireActivity(), resultListObserver)
        getSelfListViewModel().customGroupLiveData.observe(requireActivity(), customGroupObserver)
        // observe filters/sort/status
        getFilterSortStatusViewModel().uiStatusLiveData.observe(requireActivity()) {
            if (!it.isInited() || !isRealVisible()) {
                return@observe
            }
            refreshImmediately()
        }
        getFilterSortStatusViewModel().sortValueLiveData.observe(requireActivity()) {
            refreshImmediately()
        }
        getFilterSortStatusViewModel().statusLiveData.observe(requireActivity()) {
            refreshImmediately()
        }
        getFilterSortStatusViewModel().filterValueLiveData.observe(requireActivity()) {
            refreshImmediately()
        }
        initSavedView()
    }

    /**
     * 初始化保存的视图
     */
    private fun initSavedView() {
        if (!isAdded) {
            return
        }
        if (!isRealVisible()) {
            return
        }
        mScope.launch {
            val outer = getProjectSetting(getProjectId(), getViewType())
            val model = getFilterSortStatusViewModel()

            val view = outer.view
            model.updateSaveUser(view?.creator)
            model.updateProjectView(view?.viewId)
            model.updateEditModel(false)
            if (view == null) {
                model.init {
                    it.mStatus = TaskStatusEnum.UN_FINISH
                    it.mFilter = FilterValue.getNullInstance()
                    it.mSort = SortValue.getSortInstance(
                        ProjectSortEnum.SORT_END_TIME.code,
                        null,
                        string(ProjectSortEnum.SORT_END_TIME.stringId())
                    )
                }
            } else {
                var sort = view.getSortValueWithDefault(context, null)
                if (view.taskStatus == TaskStatusEnum.FINISH || isFilterSortUnable()) {
                    sort = SortValue.getDefaultSort(requireContext())
                }
                if (sort == null) {
                    sort = SortValue.getSortInstance(
                        ProjectSortEnum.SORT_END_TIME.code,
                        null,
                        string(ProjectSortEnum.SORT_END_TIME.stringId())
                    )
                }
                model.init { status ->
                    status.mStatus = view.taskStatus
                    status.mFilter = view.filter
                    status.mSort = sort
                }
            }
            model.notifyUpdate()
        }
    }

    private var mSortFilterStatusViewModel: TeamDetailViewModel? = null

    protected fun getFilterSortStatusViewModel(): TeamDetailViewModel {
        mSortFilterStatusViewModel =
            mSortFilterStatusViewModel ?: ViewModelProviders.of(requireActivity())
                .get(TeamDetailViewModel::class.java)
        return mSortFilterStatusViewModel!!
    }

    override fun getTableTitleConfig() = object : TableTitleConfig {
        override fun getMainTableTitle(): List<TableTitle> {
            return listOf(TableTitle(R.string.joywork_project_col_name))
        }

        override fun getSubTableTitle(): List<TableTitle> {
            return listOf(
                TableTitle(R.string.joywork_more_owner),
                TableTitle(R.string.joywork_shortcut_deadline_tips),
                TableTitle(R.string.joywork_update_priority),
                TableTitle(R.string.joywork_self_order)
            )
        }
    }

    protected open fun getSelfListViewModel(): SelfListViewModel {
        var vm = mViewModel
        if (vm != null) {
            return vm
        }
        val factory = object : ViewModelProvider.Factory {
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return SelfListViewModel(SelfListRepo) as T
            }
        }
        val provider = if (childrenShareViewModel()) {
            ViewModelProviders.of(requireActivity(), factory)
        } else {
            ViewModelProviders.of(this, factory)
        }
        vm = provider.get(SelfListViewModel::class.java)
        mViewModel = vm
        return vm
    }

    protected open fun childrenShareViewModel(): Boolean {
        return false
    }

    override fun onResume() {
        super.onResume()
        getPagePVName()?.apply {
            JDMAUtils.onEventPagePV(
                requireActivity(),
                this,
                this
            )
        }
    }

    /**
     * 返回埋点统计时，界面 pv
     */
    open fun getPagePVName(): String? {
        return null
    }

    private val mSelfHandler = Handler()

    protected open fun refreshImmediately() {
        if (!isRealVisible()) {
            return
        }
        mSwipeRefreshLayout?.isRefreshing = true
        mStrategyHeader.getList(getUserRole())
    }

    final override fun refresh() {
        mSelfHandler.postDelayed({
            refreshImmediately()
        }, JoyWorkConstant.REFRESH_INTERVAL)
    }

    override fun onDestroyView() {
        mRefreshReceiver stopObserveRiskUpdate requireContext()
        mScope.cancel()
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(mRefreshReceiver)
        super.onDestroyView()
        mSelfHandler.clear()
    }

    override fun customGroupLoadMore(
        groupId: String,
        count: Int,
        callback: (Group?, Boolean) -> Unit
    ) {
        val model = getFilterSortStatusViewModel()
        val uiStatus = model.mUIStatus
        ProjectRepo.getGroupTasks(
            getProjectId(),
            groupId,
            uiStatus.mStatus.code,
            count,
            getProjectType(),
            uiStatus.mFilter,
            null,
            object : RepoCallback<Group> {
                override fun result(t: Group?, errorMsg: String?, success: Boolean) {
                    if (success) {
                        callback.invoke(t!!, true)
                    } else {
                        callback.invoke(null, false)
                    }
                }
            })
    }

    final override fun loadMoreWithGroup(
        type: Int,
        count: Int,
        callback: (data: List<JoyWork>, type: Int) -> Unit
    ) {
        mScope.launch {
            val r = getSelfListViewModel().loadMoreWithGroup(
                count,
                type,
                getFilterSortStatusViewModel().mUIStatus.mFilter,
                getFilterSortStatusViewModel().mUIStatus.mSort,
                getFilterSortStatusViewModel().mUIStatus.mStatus,
                getUserRole()
            )
            r.isSuccess({ t, msg, c ->
                r.toastFailure(t, msg, c)
                callback.invoke(ArrayList(), type)
            }) {
                if (!it?.clientTasks.isLegalList() || !it?.clientTasks?.firstOrNull()?.tasks.isLegalList()) {
                    callback.invoke(ArrayList(), type)
                } else {
                    callback.invoke(it!!.clientTasks.first().tasks, type)
                }
            }
        }
    }

    private var isVisibleToUser = false
    private val mUIStatus: UIStatus = UIStatus()
    private var mViewCreator: JoyWorkUser? = null
    private var mViewId: String? = null

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        ensureStrategies()
        <EMAIL> = isVisibleToUser
        mSelfListViewModel?.onFragmentVisibleChange(isRealVisible())
        if (isVisibleToUser) {
            if (mUIStatus.isInited()) {
                val model = getFilterSortStatusViewModel()
                model.updateWithoutNotify {
                    it.mStatus = mUIStatus.mStatus
                    it.mFilter = mUIStatus.mFilter
                    it.mSort = mUIStatus.mSort
                }
                model.updateSaveUser(mViewCreator)
                model.updateProjectView(mViewId)
                model.notifyUpdateUI()
            } else {
                initSavedView()
            }
        } else {
            if (!isAdded) {
                return
            }
            val m = getFilterSortStatusViewModel().mUIStatus
            if (!m.isInited()) {
                return
            }
            mUIStatus.mSort = m.mSort
            mUIStatus.mStatus = m.mStatus
            mUIStatus.mFilter = m.mFilter
            mViewId = getFilterSortStatusViewModel().customViewLiveData.value
            mViewCreator = getFilterSortStatusViewModel().saveUserLiveData.value
        }
    }

    protected fun isRealVisible(): Boolean {
        return isVisible && isVisibleToUser
    }

    override fun getRv(): HRecyclerView? {
        return mRv
    }

    /**
     * 获取当前界面用户角度，用于区分是 我负责的、我指派的、我协作的
     */
    abstract override fun getUserRole(): TaskUserRole

    protected open fun getSelfListCacheReader(): SelfListCacheReader? = null
    protected open fun getSelfListCacheWriter(): SelfListCacheWriter? = null

    protected open fun getGroups(): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return JoyWorkGroupEx.getGroups(requireActivity())
    }

    open fun handleSortActions(list: MutableList<ProjectSortAction>) {

    }

    open fun getPageId(): String {
        return ""
    }

    /**
     * 返回自定义视图时 type 值
     */
    open fun getViewType(): Int {
        return -1
    }

    /**
     * 执行人、优先级排序时，传入的 type
     */
    abstract override fun getSortType(): Int

    override fun projectClick(
        joyWork: JoyWork,
        callback: (Project?, isDel: Boolean) -> Unit
    ) {
        if (joyWork.projects.isLegalList()) {
            val dialog = JoyWorkDialog.showTaskProjectsDialog(requireContext(), joyWork.projects, {
                gotoDialogSelectProject(joyWork, callback)
            }) {
                callback.invoke(it, true)
            }
            dialog.setOnDismissListener {
                callback.invoke(null, false)
            }
        } else {
            gotoDialogSelectProject(joyWork, callback)
        }
    }

    private var callback: ((Project, isDel: Boolean, enableView: Boolean) -> Unit)? =
        null

    private fun gotoSelectProject(
        joyWork: JoyWork,
        callback: (Project, isDel: Boolean, enableView: Boolean) -> Unit
    ) {
        this.callback = callback
        val i = Intent(
            requireActivity(),
            SelectProjectTaskGroupActivity::class.java
        )
        val ps = ArrayList<Project>()
        if (joyWork.projects.isLegalList()) {
            val t = joyWork.projects.map {
                val p = Project(it.groupId, it.groupTitle, it.title, it.projectId, it.icon)
                p.permissions = it.permissions
                p
            }
            ps.addAll(t)
        }
        i.putExtra("linkedProjects", ps)
        startActivityForResult(i, 100)
    }

    private fun gotoDialogSelectProject(joyWork: JoyWork, callback: ProjectAddCallback) {
        TaskDetailWebservice.getProjectList(0, object : TaskDetailWebservice.TaskCallback() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val gson = Gson()
                val projectList = gson.fromJson(info?.result, ProjectList::class.java)
                if (projectList.content.list != null && projectList.content.list.size > 0) {
                    val actions = projectList.content.list.map {
                        BottomAction(
                            iconStyle = R.style.BottomSheetDialogIcon_Project,
                            title = it.title,
                            data = it
                        )
                    }.toMutableList()
                    val config = DialogConfig().apply {
                        height = DensityUtil.dp2px(requireContext(), 406f)
                        theme = R.style.JoyWorkSubDialogStyle
                    }
                    var sheetDialog: JoyBottomSheetDialog<ProjectList.Content.List>? =
                        null
                    val builder = JoyBottomSheetDialog.Builder<ProjectList.Content.List>(
                        requireContext()
                    ).apply {
                        setTitle(requireContext().resources.getString(R.string.joywork_task_link_select_project))
                        setActions(actions)
                        setConfig(config)
                        setCheckStyle(R.style.BottomSheetDialogIcon_Right)
                        setOnItemClickListener(false) { _, position ->
                            gotoSelectGroupDialog(
                                joyWork,
                                projectList.content.list[position]
                            ) { project, isDel ->
                                callback.invoke(project, isDel)
                                sheetDialog?.dismiss()
                            }
                        }
                    }
                    sheetDialog = JoyBottomSheetDialog(requireContext(), builder)
                    sheetDialog.show()
                }
            }
        })
    }

    private fun gotoSelectGroupDialog(
        joyWork: JoyWork,
        list: ProjectList.Content.List?,
        callback: ProjectAddCallback
    ) {
        val ps = ArrayList<Project>()
        if (joyWork.projects.isLegalList()) {
            val t = joyWork.projects.map {
                val p = Project(
                    it.groupId,
                    it.groupTitle,
                    it.title,
                    it.projectId,
                    it.icon
                )
                p.permissions = it.permissions
                p
            }
            ps.addAll(t)
        }
        val selectGroupId = ps.find { link -> link.projectId == list?.projectId }?.groupId ?: ""
        val actions = list?.groups?.map {
            BottomAction(
                title = it.title.ifEmpty { requireContext().resources.getString(R.string.joywork_task_link_untitled_group) },
                isSelect = selectGroupId == it.groupId,
                data = it
            )
        }?.toMutableList() ?: arrayListOf()
        val config = DialogConfig().apply {
            height = DensityUtil.dp2px(requireContext(), 406f)
            theme = R.style.JoyWorkSubDialogStyle
        }
        var sheetDialog: JoyBottomSheetDialog<Groups>? = null
        val builder = JoyBottomSheetDialog.Builder<Groups>(requireContext()).apply {
            setTitle(requireContext().resources.getString(R.string.joywork_select_group_list_title))
            setActions(actions)
            setConfig(config)
            setNegativeButton(R.string.icon_direction_left)
            setCheckStyle(R.style.BottomSheetDialogCheck_Tick)
            setOnItemClickListener(false) { action, _ ->
                val group = action.data
                if (selectGroupId == group.groupId) return@setOnItemClickListener
                val project = Project(
                    group.groupId,
                    group.title,
                    list?.title,
                    group.projectId,
                    null
                )
                callback.invoke(project, false)
                sheetDialog?.dismiss()
            }
        }
        sheetDialog = JoyBottomSheetDialog(requireContext(), builder)
        sheetDialog.show()
    }

    protected open fun getCustomGroupType(): Int {
        return 1
    }

    override fun getProjectType(): Int {
        return getCustomGroupType()
    }

    override fun getProjectId(): String {
        val self = JoyWorkUser.getSelf()
        return "${self.userId}_" + "$" + "_${self.teamId}"
    }

    protected open fun getListenerAction(): java.util.ArrayList<String> {
        return arrayListOf(
            ProjectConstant.DEL_ACTION,
            ProjectConstant.REVERSE_ACTION,
            ProjectConstant.JOYWORK_SORT
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (data != null && requestCode == 100 && resultCode == Activity.RESULT_OK) {
            val p = data.getParcelableExtra<Project>("project")
            if (p != null) {
                callback?.invoke(p, false, true)
            }
            callback = null
        }
        if (resultCode == Activity.RESULT_OK && data != null && data.hasExtra(TaskDetailActivity.KEY_BIZ_OBJ)) {
            val parcel: DetailReturnParcel =
                data.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as DetailReturnParcel
            // 没有更新
            if (!parcel.update) {
                return
            }
            when (data.getStringExtra(TaskDetailActivity.KEY_BIZ_FROM)) {
                JoyWorkConstant.BIZ_DETAIL_FROM_LIST -> {
                    mSelfListViewModel?.updateRefresh()
                }
            }
        }
    }

    fun onCreateJoyWork(): Boolean {
        if (!getFilterSortStatusViewModel().mUIStatus.isInited()) {
            return false
        }
        if (getFilterSortStatusViewModel().mUIStatus.mSort.code == ProjectSortEnum.SORT_CUSTOM_GROUP.code) {
            val adapter = mRv?.realAdapter() as? SelfListBaseAdapter ?: return false
            val dg = ArrayList<Group>()
            val config = ShortcutDialogTmpData()
            var selectGroupId: String? = null
            adapter.groups.forEach {
                val g = it.title.extra as? Group
                if (g != null) {
                    dg.add(g)
                    if (g.isDefaultGroup) {
                        selectGroupId = g.groupId
                    }
                }
            }
            config.hideGroups = true
            config.isProject = false
            config.selectGroupId = selectGroupId
            if (getTaskListType() == TaskListTypeEnum.HANDLE) {
                val self = JoyWorkUser.getSelf();
                self.setToChief()
                config.owners = listOf(self)
            }

            CreateDialog(requireActivity(), dg, getProjectId(), config).apply {
                successCallback = ::createSuccess
            }.show()
            return true
        }
        return false
    }

    private fun createSuccess(joywork: JoyWork, dialog: CreateDialog) {
        dialog.dismiss()
        val mModel = getFilterSortStatusViewModel()
//        if (mModel.mUIStatus.mSort.code == ProjectSortEnum.SORT_NULL.code) {
//            // 没有排序，插入
//            if (joywork.projects == null) {
//                return
//            }
//            val target = joywork.projects.filter {
//                it.projectId == getProjectId()
//            }.map {
//                it.groupId
//            }
//            (mRv?.realAdapter() as? SelfListBaseAdapter)?.insert(joywork, target)
//        } else {
//
//        }
        mModel.notifyUpdate()
        SuccessSnackBar(requireActivity()).show(joywork)
    }

    open fun getTaskListType(): TaskListTypeEnum {
        return TaskListTypeEnum.HANDLE
    }

    open fun isFilterSortUnable(): Boolean {
        return false
    }

    open fun getDefaultUIState(): UIStatus {
        val ans = UIStatus()
        ans.mStatus = TaskStatusEnum.UN_FINISH
        ans.mFilter = FilterValue.getNullInstance()
        ans.mSort = SortValue.getSortInstance(
            ProjectSortEnum.SORT_END_TIME.code,
            null,
            string(ProjectSortEnum.SORT_END_TIME.stringId())
        )
        return ans
    }
}