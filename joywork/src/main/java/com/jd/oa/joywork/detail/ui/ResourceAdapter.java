package com.jd.oa.joywork.detail.ui;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.AsyncDifferConfig;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.databinding.TaskResItemBinding;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailViewModel;

class ResourceAdapter extends ListAdapter<Resources, ResourceAdapter.ResourceViewHolder> {

    @Nullable
    private final ResourceClickCallback resourceClickCallback;
    private final TaskDetailViewModel taskDetailViewModel;

    ResourceAdapter(TaskDetailViewModel taskDetailViewModel, @Nullable ResourceClickCallback resourceClickCallback) {
        super(new AsyncDifferConfig.Builder<>(new DiffUtil.ItemCallback<Resources>() {
            @Override
            public boolean areItemsTheSame(@NonNull Resources old,
                                           @NonNull Resources comment) {
                return old.getUrl().equals(comment.getUrl());
            }

            @Override
            public boolean areContentsTheSame(@NonNull Resources old,
                                              @NonNull Resources comment) {
                return old.getUrl().equals(comment.getUrl());
            }
        }).build());
        this.resourceClickCallback = resourceClickCallback;
        this.taskDetailViewModel = taskDetailViewModel;
    }

    @Override
    @NonNull
    public ResourceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TaskResItemBinding binding = DataBindingUtil
                .inflate(LayoutInflater.from(parent.getContext()), R.layout.task_res_item,
                        parent, false);
        binding.setResCallback(resourceClickCallback);
        return new ResourceViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ResourceViewHolder holder, int position) {
        holder.binding.setResources(getItem(position));
        holder.binding.executePendingBindings();
        holder.binding.setDetail(taskDetailViewModel.getTaskDetail().getValue());
    }

    static class ResourceViewHolder extends RecyclerView.ViewHolder {

        final TaskResItemBinding binding;

        ResourceViewHolder(TaskResItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
