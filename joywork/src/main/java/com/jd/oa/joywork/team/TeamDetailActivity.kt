package com.jd.oa.joywork.team

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.FrameLayout
import androidx.activity.viewModels
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.utils.getParamsKey
import com.jd.oa.joywork.utils.getParamsStr
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.JDMAUtils
import org.json.JSONObject
import java.net.URLDecoder
import java.util.*
import kotlin.collections.ArrayList

/**
 * 团队待办列表
 */
class TeamDetailActivity : BaseActivity() {

    companion object {
        val gs = ArrayList<Group>()
        fun inflateIntent(
            context: Context,
            title: String = "",
            id: String,
            type: Int,
            iconUrl: String?,
            isTab: Boolean,
            gsp: List<JoyWorkProjectList.ListDTO.GroupsDTO> = mutableListOf(),
            psp: List<String> = mutableListOf()
        ): Intent {
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_TEAM_ITEM_CLICK,
                JoyWorkConstant.JOYWORK_TEAM_ITEM_CLICK
            )
            val bundle = Intent(context, TeamDetailActivity::class.java)
            bundle.putExtra("title", title)
            bundle.putExtra("projectId", id)
            bundle.putExtra("isTab", isTab)
            bundle.putExtra("type", type)
            bundle.putExtra("iconUrl", iconUrl ?: "")
            val ps = ArrayList<String>()
            ps.addAll(psp)
            bundle.putStringArrayListExtra("ps", ps)

            gsp.forEach {
                val g = Group()
                g.groupId = it.groupId
                g.projectId = it.projectId
                g.title = it.title
                g.type = it.type
                gs.add(g)
            }
            return bundle
        }
    }

    private val mViewModel: TeamDetailViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_function)
        hideAction()
        val frameLayout = findViewById<FrameLayout>(R.id.me_fragment_content)
        mViewModel.updateDeeplinkParams(intent.getParamsStr())
        supportFragmentManager.beginTransaction().replace(
            frameLayout.id,
            TeamDetailFragment.getInstance(
                isTab(),
                getProjectTitle(),
                getId(),
                getType(),
                getIconUrl(),
                gs,
                getPs()
            ),
            getId()
        ).commit()
    }

    private fun getPs(): ArrayList<String> {
        if (intent.hasExtra("ps")) {
            val list = intent.getStringArrayListExtra("ps")
            if (list != null) {
                return list
            }
        }
        return ArrayList()
    }

    private fun getIconUrl(): String? {
        if (intent.hasExtra("iconUrl")) {
            return intent.getStringExtra("iconUrl")
        }
        return ""
    }

    private fun getType(): Int {
        val deeplink = intent.getStringExtra("raw_uri")
        if (deeplink.isLegalString() && deeplink!!.startsWith(DeepLink.JOYWORK_PROJECT_DEFAULT)) {
            return JoyWorkProjectList.TYPE_OTHER
        }
        return intent.getIntExtra("type", JoyWorkProjectList.TYPE_NORMAL);
    }

    private fun isTab(): Boolean {
        return intent.getBooleanExtra("isTab", false)
    }

    private fun getId(): String {
        // mparam={"projectId":"352391515286536192","title":"ABCD"}
        // {"projectId":"352391515286536192","title":"ABCD"}
        var ret = intent.getParamsKey("projectId", "")
        if (!ret.isLegalString()) {// 从 deeplink 来
            val mparam = intent.getStringExtra("mparam") ?: ""
            kotlin.runCatching {
                ret = JSONObject(URLDecoder.decode(mparam, "utf-8")).getString("projectId")
            }
        }
        if (!ret.isLegalString()) {
            ret = intent.getStringExtra("projectId") ?: ""
        }
        return ret
    }

    private fun getProjectTitle(): String {
        // mparam={"projectId":"352391515286536192","title":"ABCD"}
        // {"projectId":"352391515286536192","title":"ABCD"}
        var ret = intent.getParamsKey("title", "")
        if (!ret.isLegalString()) {// 从 deeplink 来
            val mparam = intent.getStringExtra(DeepLink.DEEPLINK_PARAM) ?: ""
            kotlin.runCatching {
                ret = JSONObject(URLDecoder.decode(mparam, "utf-8")).getString("title")
            }
        }
        if (!ret.isLegalString()) {
            ret = intent.getStringExtra("title") ?: ""
        }
        return ret
    }
}