package com.jd.oa.joywork.self.base

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.CoroutineResult
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkGroup
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.team.AbsRepoCallback
import com.jd.oa.joywork.team.bean.ProjectGroups
import com.jd.oa.joywork.team.bean.ResultWithoutGroup
import kotlinx.coroutines.suspendCancellableCoroutine

class SelfListViewModel(private val mRepo: ISelfListRepo) : ViewModel() {

    // 按优先级排序
    fun listPriority(
        type: Int,
        projectId: String,
        filter: FilterValue,
        sort: SortValue,
        offset: Int,
        status: TaskStatusEnum,
    ) {
        val callback = object : AbsRepoCallback<ResultWithoutGroup>() {
            override fun result(
                t: ResultWithoutGroup?,
                errorMsg: String?,
                success: Boolean,
                errorCode: Int?
            ) {
                super.result(t, errorMsg, success, errorCode)
                var wrapper: JoyWorkWrapper? = null
                if (t != null) {
                    wrapper = JoyWorkWrapper()
                    val list = ArrayList<JoyWork>()
                    list.addAll(t.safeTasks())
                    val group = JoyWorkGroup()
                    group.tasks = list
                    val gs = ArrayList<JoyWorkGroup>()
                    gs.add(group)
                    wrapper.clientTasks = gs
                }
                _resultTasksLiveData.value = Triple(wrapper, errorMsg, offset != 0)
            }
        }
        mRepo.listPriority(type, projectId, filter, sort, offset, status, callback)
    }

    // 返回结果是一个 List<JoyWork> 需要自己聚类处理
    private val _resultTasksLiveData = MutableLiveData<Triple<JoyWorkWrapper?, String?, Boolean>>()
    val resultTasksLiveData: LiveData<Triple<JoyWorkWrapper?, String?, Boolean>> =
        _resultTasksLiveData

    fun listMyCreate(offset: Int, status: TaskStatusEnum) {
        val callback = object : JoyWorkVMCallback() {
            override fun onError(msg: String) {
                _resultTasksLiveData.value = Triple(null, msg, offset != 0)
            }

            override fun call(wrapper: JoyWorkWrapper, rawData: String) {
                _resultTasksLiveData.value = Triple(wrapper, null, offset != 0)
            }
        }
        mRepo.listMyCreate(TaskUserRole.CREATOR, offset, status, callback)
    }

    fun listFinishTasks(
        role: TaskUserRole,
        offset: Int
    ) {
        val callback = object : JoyWorkVMCallback() {
            override fun onError(msg: String) {
                _resultTasksLiveData.value = Triple(null, msg, offset != 0)
            }

            override fun call(wrapper: JoyWorkWrapper, rawData: String) {
                _resultTasksLiveData.value = Triple(wrapper, null, offset != 0)
            }
        }
        mRepo.listFinishTasks(role, offset, callback)
    }


    private val _refreshLiveData = MutableLiveData<Pair<JoyWorkWrapper?, String?>>()
    val refreshLiveData: LiveData<Pair<JoyWorkWrapper?, String?>> = _refreshLiveData
    fun refresh(
        status: TaskStatusEnum,
        role: TaskUserRole,
        filter: FilterValue,
        sort: SortValue,
        writer: SelfListCacheWriter?
    ) {
        val callback = object : JoyWorkVMCallback() {
            override fun onError(msg: String) {
                _refreshLiveData.value = Pair(null, msg)
            }

            override fun call(wrapper: JoyWorkWrapper, rawData: String) {
                _refreshLiveData.value = Pair(wrapper, null)
            }
        }
        mRepo.init(callback, status, role, filter, sort, null, writer)
    }

    suspend fun loadMoreWithGroup(
        offset: Int,
        type: Int,
        filter: FilterValue,
        sort: SortValue,
        status: TaskStatusEnum,
        role: TaskUserRole
    ): CoroutineResult<JoyWorkWrapper> {
        return suspendCancellableCoroutine { c ->
            val callback = object : JoyWorkVMCallback() {
                override fun onError(msg: String) {
                    c.resumeWith(
                        Result.success(
                            CoroutineResult<JoyWorkWrapper>().failure(msg)
                        )
                    )
                }

                override fun call(wrapper: JoyWorkWrapper, rawData: String) {
                    c.resumeWith(
                        Result.success(
                            CoroutineResult<JoyWorkWrapper>().success(wrapper)
                        )
                    )
                }
            }
            mRepo.loadMoreWithGroup(type, offset, status, role, filter, sort, callback)
        }
    }

    // 自定义分组，用清单相关接口

    private val _customGroupLiveData = MutableLiveData<Pair<ProjectGroups?, String?>>()
    val customGroupLiveData: LiveData<Pair<ProjectGroups?, String?>> = _customGroupLiveData

    fun listCustomGroup(
        filter: FilterValue,
        sort: SortValue,
        type: Int,
        projectId: String,
        taskStatus: TaskStatusEnum
    ) {
        mRepo.listCustomGroup(
            filter,
            sort,
            type,
            projectId,
            taskStatus,
            object : AbsRepoCallback<ProjectGroups>() {
                override fun result(
                    t: ProjectGroups?,
                    errorMsg: String?,
                    success: Boolean,
                    errorCode: Int?
                ) {
                    _customGroupLiveData.value = Pair(t, errorMsg)
                }
            })
    }
}