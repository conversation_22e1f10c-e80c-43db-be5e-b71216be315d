package com.jd.oa.joywork.team.kpi;

import com.jd.oa.joywork.bean.KpiQ;
import com.jd.oa.joywork.bean.KpiTarget;

import java.util.ArrayList;
import java.util.List;

public class KpiGroups {
    public List<KpiQ> periodList;
    /**
     * q 下的所有目标（目标下为 kr 时使用）
     */
    public List<KpiTarget> goalKrList;

    public List<KpiTarget> getSafeKRList() {
        if (goalKrList == null) {
            goalKrList = new ArrayList<>();
        }
        return goalKrList;
    }

    /**
     * assign-转让创建人
     * update-可编辑 标题&副标题
     * delete-可删除清单
     * member-可添加成员
     * group-可编辑分组
     * task-可在分组下操作待办
     */
    public ArrayList<String> permissions;// assign-转让创建人  update-可编辑 标题&副标题

    public List<String> getPermissions() {
        if (permissions == null) {
            permissions = new ArrayList<>();
        }
        return permissions;
    }
}
