package com.jd.oa.joywork.team

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.jd.oa.joywork.ProjectSortAction
import com.jd.oa.joywork.ProjectSortCustomGroup
import com.jd.oa.joywork.R
import com.jd.oa.joywork.SortAction
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

fun SortAction.bindItem(parent: ViewGroup, click: View.OnClickListener): Map<Int, View> {
    val ret = HashMap<Int, View>()
    val view = parent.context.inflater.inflate(R.layout.jdme_dialog_project_option_filter, parent, false)
    ret[R.id.key] = view

    val tv: TextView = view.findViewById(R.id.title)
    ret[tv.id] = tv
    tv.text = action
    tv.setTextColor(color())

    val icon: TextView = view.findViewById(R.id.icon)
    ret[icon.id] = icon

    val newLabel:TextView = view.findViewById(R.id.mNewLabel)
    if (this is ProjectSortAction && showNewLabel()) {
        newLabel.visible()
    } else {
        newLabel.gone()
    }
    ret[newLabel.id] = newLabel

    view.tag = this
    view.setOnClickListener(click)
    return ret
}



fun ProjectFilterEnum.bindItem(parent: ViewGroup, click: View.OnClickListener): Map<Int, View> {
    val ret = HashMap<Int, View>()
    val view = parent.context.inflater.inflate(R.layout.jdme_dialog_project_option_filter, parent, false)
    ret[R.id.key] = view

    val tv: TextView = view.findViewById(R.id.title)
    ret[tv.id] = tv
    tv.setText(stringId())
    tv.setTextColor(Color.parseColor("#232930"))

    val icon: TextView = view.findViewById(R.id.icon)
    ret[icon.id] = icon

    view.findViewById<View>(R.id.mNewLabel).gone()

    view.tag = this
    view.setOnClickListener(click)
    return ret
}


fun ProjectFilterEnum.bindItem2(parent: ViewGroup, click: View.OnClickListener): Map<Int, View> {
    val ret = HashMap<Int, View>()
    val view = parent.context.inflater.inflate(R.layout.jdme_dialog_project_option_filter2, parent, false)
    ret[R.id.key] = view

    val tv: TextView = view.findViewById(R.id.title)
    ret[tv.id] = tv
    tv.setText(stringId())
    tv.setTextColor(Color.parseColor("#232930"))

    val icon: TextView = view.findViewById(R.id.icon)
    ret[icon.id] = icon

    val mSubTitle = view.findViewById<TextView>(R.id.mSubTitle)
    ret[mSubTitle.id] = mSubTitle

    val mSubTitleContent = view.findViewById<TextView>(R.id.mSubContent)
    ret[mSubTitleContent.id] = mSubTitleContent

    val mSubTitleContainer = view.findViewById<View>(R.id.mSubTitleContainer)
    ret[mSubTitleContainer.id] = mSubTitleContainer

    view.tag = this
    view.setOnClickListener(click)
    return ret
}