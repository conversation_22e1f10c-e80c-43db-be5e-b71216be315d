package com.jd.oa.joywork.team.chat

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.fragment.app.commit
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.eventbus.DeeplinkCallbackProcessor
import com.jd.oa.ext.searchParams
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkProjectList.ListDTO
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.team.create.TeamCreateActivity
import com.jd.oa.model.service.AppService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.gone

@Route(DeepLink.JOY_WORK_CHAT_PROJECT)
class ProjectSelectorActivity : BaseActivity() {
    private val mViewModel: ProjectSelectorViewModel by viewModels()
    private val launcher = registerForActivityResult<Intent, ActivityResult>(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val data = result.data ?: return@registerForActivityResult
        if (result.resultCode == RESULT_OK) {
            val project = ListDTO()
            // 此处数据都从新建返回，如果缺少需要从新建界面获取
            project.desc = data.getStringExtra("desc")
            project.title = data.getStringExtra("title")
            project.projectId = data.getStringExtra("projectId")

            mViewModel.insertBefore(project)
            mViewModel.updateCheckedItem(project)
        }
    }
    private val mNext: View by lazy {
        findViewById(R.id.mNext)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.joywork_project_selector)
        hideAction()
        findViewById<View>(R.id.back).setOnClickListener {
            finish()
        }
        findViewById<View>(R.id.mNew).setOnClickListener {
            val i = TeamCreateActivity.getIntent(
                this,
                TeamCreateActivity.Companion.From.PROJECT_SELECTOR
            )
            i.putExtra("sessonType", intent.searchParams("sessionType"))
            i.putExtra("gid", intent.searchParams("gid"))
            i.putExtra("sessionId", intent.searchParams("sessionId"))
            i.putExtra("toPin", intent.searchParams("toPin"))
            i.putExtra("toApp", intent.searchParams("toApp"))
            launcher.launch(i)
        }
        findViewById<View>(R.id.mNew).gone()
        installFragments()
        initViewModels()
        mNext.setOnClickListener {
            val project = mViewModel.mCheckedProjectLiveData.value ?: return@setOnClickListener
            val deeplink = ProjectSelectorResultDeeplink(
                default = ProjectSelectorDeeplinkUtils.buildDeeplink(
                    intent, this,
                    true,
                    project.projectId,
                    project.title
                ),
                desktop = ProjectSelectorDeeplinkUtils.buildDeeplink(
                    intent,
                    this,
                    false,
                    project.projectId,
                    project.title
                ),
                mobile = ProjectSelectorDeeplinkUtils.buildDeeplink(
                    intent, this,
                    true,
                    project.projectId,
                    project.title
                )
            )
            val projectDesc = getString(R.string.joywork_project_title_prefix) + project.title
            val result = ProjectSelectorResult(project.title, projectDesc, deeplink)
            val service = AppJoint.service(AppService::class.java);
            val callbackId = intent.extras.searchParams(DeeplinkCallbackProcessor.KEY_CALLBACK_ID)
            if (callbackId.isNotEmpty()) {
                DeeplinkCallbackProcessor.notify(
                    this,
                    service.toGroupProjectJsonString(result),
                    callbackId
                )
            } else {
                val i = Intent()
                i.putExtra("result", service.toGroupProjectJsonString(result))
                setResult(RESULT_OK, i)
            }
            finish()
        }
    }


    private fun initViewModels() {
        mViewModel.mCheckedProjectLiveData.observe(this) {
            mNext.isEnabled = it != null
        }
    }

    private fun installFragments() {
        val tag = "ProjectSelectorFragment"
        if (supportFragmentManager.findFragmentByTag(tag) != null) {
            return
        }
        supportFragmentManager.commit(true) {
            setReorderingAllowed(true)
            replace(
                R.id.mContainer,
                ProjectSelectorFragment(),
                tag
            )
        }
    }
}