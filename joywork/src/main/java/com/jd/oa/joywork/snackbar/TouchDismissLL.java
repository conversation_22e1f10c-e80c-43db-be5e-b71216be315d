package com.jd.oa.joywork.snackbar;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

public class TouchDismissLL extends LinearLayout {
    public TouchDismissLL(Context context) {
        super(context);
    }

    public TouchDismissLL(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public TouchDismissLL(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {

        }
        return super.onTouchEvent(event);
    }
}
