package com.jd.oa.joywork.dialog.thirdparty

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.Spanned
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import com.jd.oa.joywork.R
import com.jd.oa.joywork.dialog.DialogHelper
import com.jd.oa.joywork.isLegalString
import com.jd.oa.utils.*
import java.lang.IllegalArgumentException

class ShitAlertDialog(
    context: Context,
    private val config: ShitAlertDialogConfig<ShitAlertDialog>
) : AppCompatDialog(context, R.style.JoyWorkAlertDialogStyle) {

    private lateinit var mContentView: View
    private lateinit var mMsgView: TextView
    private lateinit var mOkTextView: TextView

    init {
        window?.apply {
            decorView.setBackgroundColor(Color.TRANSPARENT)
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = CommonUtils.getScreentWidth(context) * 62 / 75
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            attributes = layoutParams
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    private var mDialogHelper: DialogHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mDialogHelper = DialogHelper(this)
        mDialogHelper?.register()
        mContentView = context.inflater.inflate(R.layout.jdme_dialog_joywork_alert, null)
        setContentView(mContentView)
        window?.decorView?.setBackgroundColor(Color.TRANSPARENT)
        mContentView.findViewById<ViewGroup>(R.id.root)?.apply {
            background =
                DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(config.bgCorner))
        }
        mMsgView = mContentView.findViewById(R.id.message)
        mMsgView.getStr(config.msgRes, config.msgStr, config.msgSpanner)

        mOkTextView = mContentView.findViewById(R.id.ok)
        mOkTextView.getStr(config.okRes, config.okStr, null)

        mContentView.findViewById<TextView>(R.id.cancel)
            .getStr(config.cancelRes, config.cancelStr, null)

        val mSubMsgView = mContentView.findViewById<TextView>(R.id.subMessage)
        if (config.subMsg.isLegalString()) {
            mSubMsgView.visible()
            mSubMsgView.text = config.subMsg
        } else {
            mSubMsgView.gone()
        }

        getOkClick()?.apply {
            mOkTextView.setOnClickListener {
                this.invoke(this@ShitAlertDialog)
            }
        }

        if (config.showCancel) {
            mContentView.findViewById<TextView>(R.id.cancel).setOnClickListener {
                dismiss()
                config.cancelShitClick?.invoke(it, this)
            }
        } else {
            mContentView.findViewById<TextView>(R.id.cancel).gone()
            mContentView.findViewById<TextView>(R.id.divider).gone()
        }
    }

    private fun TextView.getStr(res: Int, str: String?, spanned: Spanned?) {
        if (res <= 0 && str == null && spanned == null) {
            throw IllegalArgumentException()
        }
        when {
            str.isLegalString() -> {
                this.text = str
            }
            res > 0 -> {
                this.text = context.string(res)
            }
            else -> {
                this.text = spanned ?: ""
            }
        }
    }

    private fun getOkClick(): ((ShitAlertDialog) -> Unit)? {
        return if (config.autoDismiss) { dialog ->
            dismiss()
            config.okShitClick?.invoke(dialog)
        } else config.okShitClick
    }

    override fun dismiss() {
        super.dismiss()
        mDialogHelper?.unregister()
    }
}

open class ShitAlertDialogConfig<T : AppCompatDialog> {
    /**
     * 背景圆角, 单位 dp
     */
    var bgCorner: Int = 8

    /**
     * 提示文字
     */
    var msgRes: Int = -1
    var msgStr: String? = null
    var msgSpanner: Spanned? = null

    /**
     * ok 按钮的文案
     */
    var okRes: Int = -1
    var okStr: String? = null

    /**
     * 取消按钮文字
     */
    var cancelRes: Int = R.string.cancel
    var cancelStr: String? = null

    var showCancel: Boolean = true

    open var okShitClick: ((T) -> Unit)? = null

    /**
     * 取消按钮点击事件，设置后需要自己手动取消
     */
    var cancelShitClick: ((View, Dialog) -> Unit)? = null

    /**
     * 点确定时是否自动消失
     */
    var autoDismiss: Boolean = true

    /**
     * 子标题。可以理解为带标题时的消息体
     */
    var subMsg: String? = null
}