package com.jd.oa.joywork.detail.data.entity;

import com.jd.oa.joywork.detail.data.entity.custom.ProjectFilterExt;
import com.jd.oa.joywork.team.bean.ProjectFilterEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Nullable;

public class FilterValue {
    private static FilterValue mNullInstance;

    // 对应 ProjectFilterEnum 中的 code
    public Integer type;

    @Nullable
    public ProjectFilterExt value;

    public static FilterValue getNullInstance() {
        if (mNullInstance == null) {
            mNullInstance = new FilterValue();
            mNullInstance.type = ProjectFilterEnum.SCREEN_NULL.getCode();
        }
        return mNullInstance;
    }

    public static FilterValue getInstance(ProjectFilterEnum projectFilterEnum){
        if (projectFilterEnum == ProjectFilterEnum.SCREEN_NULL) {
            return getNullInstance();
        }
        FilterValue filterValue = new FilterValue();
        filterValue.type = projectFilterEnum.getCode();

        return filterValue;
    }

    public ProjectFilterEnum getEnum() {
        return ProjectFilterEnum.Companion.getInstance(type);
    }

    public boolean isEquals(FilterValue other) {
        if (this == other) {
            return true;
        }
        if (!Objects.equals(this.type, other.type)) {
            return false;
        }
        if (this.value == other.value) {
            return true;
        }
        if (this.value == null || other.value == null) {
            return false;
        }
        return this.value.isEquals(other.value);
    }

    public void toMap(Map<String, Object> map) {
        if (type == null) {
            return;
        }
        map.put("type", type);
        if (value != null) {
            HashMap<String,Object> valueM = new HashMap<>();
            value.toMap(valueM);
            map.put("value",valueM);
        }
    }
}
