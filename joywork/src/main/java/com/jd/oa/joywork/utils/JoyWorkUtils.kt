package com.jd.oa.joywork.utils

import android.content.res.Resources
import com.jd.oa.joywork.R
import com.jd.oa.utils.DateShowUtils
import com.jd.oa.utils.DateUtils
import org.json.JSONObject


fun getTaskDeadlineString(time: Long, resource: Resources): String {
    var ans = DateShowUtils.getSimpleTimeShowText(time)
    if (DateUtils.isSameYear(time, System.currentTimeMillis())) {
        ans = DateShowUtils.getSimpleTimeShowTextWithHourMinute(time)
    }
    return resource.getString(R.string.me_joywork_deadline_normal, ans)
}

fun getTaskPlanTimeString(time: Long, resource: Resources): String {
    var ans = DateShowUtils.getSimpleTimeShowText(time)
    if (DateUtils.isSameYear(time, System.currentTimeMillis())) {
        ans = DateShowUtils.getSimpleTimeShowTextWithHourMinute(time)
    }
    return resource.getString(R.string.me_joywork_plantime_normal, ans)
}

fun getTaskStartTimeString(time: Long, resource: Resources): String {
    var ans = DateShowUtils.getSimpleTimeShowText(time)
    if (DateUtils.isSameYear(time, System.currentTimeMillis())) {
        ans = DateShowUtils.getSimpleTimeShowTextWithHourMinute(time)
    }
    return resource.getString(R.string.me_joywork_start_normal, ans)
}

fun getTaskCompleteTimeString(time: Long, resource: Resources): String {
    var ans = DateShowUtils.getSimpleTimeShowText(time)
    if (DateUtils.isSameYear(time, System.currentTimeMillis())) {
        ans = DateShowUtils.getSimpleTimeShowTextWithHourMinute(time)
    }
    return resource.getString(R.string.joywork_finish_suffix, ans)
}

/**
 * 不带截止
 */
fun getTaskDeadlineStringWithoutDue(time: Long, resource: Resources): String {
    var ans = DateShowUtils.getSimpleTimeShowText(time)
    if (DateUtils.isSameYear(time, System.currentTimeMillis())) {
        ans = DateShowUtils.getSimpleTimeShowTextWithHourMinute(time)
    }
    return resource.getString(R.string.me_joywork_deadline_normal2, ans)
}

fun getTimeStringWithoutSuffix(time: Long, resource: Resources): String {
    var ans = DateShowUtils.getSimpleTimeShowText(time)
    if (DateUtils.isSameYear(time, System.currentTimeMillis())) {
        ans = DateShowUtils.getSimpleTimeShowTextWithHourMinute(time)
    }
    return resource.getString(R.string.me_joywork_deadline_normal2, ans)
}

/**
 * 解析时，会将 null 转换成 "null"。此方法会将在 null 时直接返回 null
 */
fun JSONObject.optStringOrNull(key: String, nullValue: String? = null): String? {
    return if (this.has(key) && !isNull(key)) {
        optString(key)
    } else {
        nullValue
    }
}