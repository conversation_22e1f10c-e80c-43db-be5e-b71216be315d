package com.jd.oa.joywork.collaborator

/**
 * 数据只可读取一次
 */
object OneTimeDataRepo {
    var read: Boolean = true
    var data: Any? = null
        set(value) {
            field = value
            read = false
        }
        get() {
            if (read) {
                return null
            }
            read = true
            return field
        }

    val dataCache = HashMap<String, Any>()
}