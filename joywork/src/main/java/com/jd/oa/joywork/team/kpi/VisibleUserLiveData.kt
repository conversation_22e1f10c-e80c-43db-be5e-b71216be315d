package com.jd.oa.joywork.team.kpi

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.joywork.team.bean.VisibleSetting
import com.jd.oa.joywork.union
import com.jd.oa.joywork.utils.JoyWorkLiveData
import java.util.*
import kotlin.collections.ArrayList

const val VISIBLE_USER_LIVE_DATA_TOKEN = "kpi.visible.user.list.livedata"

class VisibleUserLiveData : JoyWorkLiveData(VISIBLE_USER_LIVE_DATA_TOKEN) {
    private val _visibleUserLiveData = MutableLiveData<MutableList<JoyWorkUser>>()
    val visibleUserLiveData: LiveData<MutableList<JoyWorkUser>> = _visibleUserLiveData

    fun frontUsers(us: List<JoyWorkUser>) {
        val v = visibleUserLiveData.value ?: mutableListOf()
        VisibleSetting.frontUsers(v, us)
        _visibleUserLiveData.value = v
    }

    fun removeUser(joyWorkUser: JoyWorkUser) {
        val v = visibleUserLiveData.value ?: return
        val key = VisibleSetting.getKey(joyWorkUser)
        v.removeAll {
            VisibleSetting.getKey(it) == key
        }
        // force update
        _visibleUserLiveData.value = v
    }

    fun replaceVisibleUser(list: MutableList<JoyWorkUser>) {
        val newList = ArrayList<JoyWorkUser>()
        newList.addAll(list)
        _visibleUserLiveData.postValue(newList)
    }


    // department related

    private val _deptLiveData = MutableLiveData<MutableList<DeptInfo>>()
    val deptLiveData: LiveData<MutableList<DeptInfo>> = _deptLiveData

    fun appendDepts(list: MutableList<DeptInfo>) {
        val newList = deptLiveData.value ?: mutableListOf()
        newList.union(list) {
            it.deptId
        }
        _deptLiveData.postValue(newList)
    }

    fun replaceDepts(list: MutableList<DeptInfo>) {
        val newList = ArrayList<DeptInfo>()
        newList.addAll(list)
        _deptLiveData.postValue(newList)
    }

    fun removeDept(dept: DeptInfo) {
        val v = deptLiveData.value ?: return
        val key = dept.deptId
        v.removeAll {
            Objects.equals(it.deptId, key)
        }
        // force update
        _deptLiveData.value = v
    }
}