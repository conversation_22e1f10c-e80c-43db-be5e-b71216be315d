package com.jd.oa.joywork.support

enum class ProjectSettingAction {
    EXIT,
    DEL,
    TRANSFER
}

class ProjectSettingPageData {
    var newTitle: String? = null
    var newDesc:String? = null
    var actions: ArrayList<ProjectSettingAction> = ArrayList()


    /**
     * 1<<0 结束当前界面
     * 1<<1 修改标题
     * 1<<2 刷新界面
     * 1<<3 啥也不做
     */
    fun getAction(): Int {
        var ret = 0
        actions.forEach {
            ret = if (it == ProjectSettingAction.TRANSFER) {
                ret or (1 shl 2)
            } else {
                ret or (1)
            }
        }
        if (newTitle != null) {
            ret = ret or (1 shl 1)
        }
        return ret
    }
}