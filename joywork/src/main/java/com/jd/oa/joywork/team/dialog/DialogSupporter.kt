package com.jd.oa.joywork.team.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.view.View
import android.view.ViewGroup
import android.widget.ListAdapter
import android.widget.TextView
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.R
import com.jd.oa.joywork.dialog.thirdparty.AlertTypeSelectDialog
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.joywork.dialog.thirdparty.CancelShitActionController
import com.jd.oa.joywork.dialog.thirdparty.CenterTextTitleController
import com.jd.oa.joywork.dialog.thirdparty.CenterTextTitleDecorator
import com.jd.oa.joywork.dialog.thirdparty.DupSelectDialog
import com.jd.oa.joywork.dialog.thirdparty.LoadingListShitDialog
import com.jd.oa.joywork.dialog.thirdparty.LoadingListShitDialogAdapter
import com.jd.oa.joywork.dialog.thirdparty.LoadingListShitDialogConfig
import com.jd.oa.joywork.dialog.thirdparty.SelectGroupDialog
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

/**
 * CreateDialog 的辅助
 */
class DialogSupporter(private val createDialog: Dialog?, private val context: Context) {

    var dialog: LoadingListShitDialog? = null
    /**
     * 选择组
     */
    fun listGroupOld(projectId: String, groups: ArrayList<Group>, selectedId: String?, finishCallback: (group: Group?) -> Unit): LoadingListShitDialog {
        createDialog?.dismiss()

        dialog = showGroupDialog(createDialog?.context ?: context, groups, selectedId) { view, _ ->
            dialog = null
            finishCallback.invoke(view.tag as? Group)
            createDialog?.show()
        }
        dialog?.setOnCancelListener {
            dialog = null
            createDialog?.show()
            finishCallback.invoke(null)
        }

        // 2021年08月24日：理论上执行不到这处逻辑，只不过先保留着
        if (!groups.isLegalList()) {
            ProjectRepo.listGroup(projectId) { success, data, msg ->
                data?.apply {
                    groups.addAll(this)
                    dialog?.showContent = true
                }
            }
        }
        dialog!!.show()
        return dialog!!
    }

    /**
     * 选择分组 dialog
     */
    fun showGroupDialog(
        context: Context,
        groups: ArrayList<Group>,
        selectedId: String?,
        itemClickP: ((View, Int) -> Unit)?
    ): LoadingListShitDialog {
        val config = LoadingListShitDialogConfig().apply {
            contentBackground = DrawableEx.solidRect(Color.WHITE)
            adapterCreator = { ctx ->
                getAdapter(ctx, groups, selectedId)
            }
        };
        return LoadingListShitDialog(context, config).apply {
            titleController = CenterTextTitleController(object : CenterTextTitleDecorator() {
                override fun getTitleId(): Int {
                    return R.string.joywork_select_group_list_title
                }
            })
            actionController = CancelShitActionController
            showContent = groups.isLegalList()

            itemClick = itemClickP
        }
    }


    private fun getAdapter(context: Context, groups: List<Group>, selectedId: String?): ListAdapter {
        return object : LoadingListShitDialogAdapter<Group>(groups) {
            override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                val group = groups[position]
                val view = context.inflater.inflate(R.layout.joywork_filter_dialog_item, parent, false)
                view.tag = group
                val icon = view.findViewById<View>(R.id.jdme_id_myapply_dropdown_icon)
                if (group.groupId == selectedId) icon.visible() else icon.gone()
                val text = view.findViewById<TextView>(R.id.jdme_id_myapply_dropdown_item)
                text.typeface =
                    Typeface.defaultFromStyle(if (group.groupId == selectedId) Typeface.BOLD else Typeface.NORMAL)
                text.text = group.safeTitle(context)

                val divider = view.findViewById<View>(R.id.divider)
                if (position == items.size - 1) {
                    divider.gone()
                } else {
                    divider.visible()
                }
                return view
            }
        }
    }
    /**
     * 选择组
     */
    fun listGroup(
        groups: ArrayList<Group>,
        selectedId: String?,
        finishCallback: (group: Group?) -> Unit
    ): BaseShitDialog {
        createDialog?.dismiss()
        val dialog = SelectGroupDialog(createDialog?.context ?: context, groups, selectedId) {
            finishCallback.invoke(it)
            createDialog?.show()
        }
        dialog.setOnDismissListener {
            finishCallback.invoke(null)
            createDialog?.show()
        }
        dialog.setOnCancelListener {
            finishCallback.invoke(null)
            createDialog?.show()
        }

        dialog.show()
        return dialog
    }

    fun listAlerts(
        selectTypes: Set<AlertType>,
        canUpdate: Boolean,
        callback: (Set<AlertType>?) -> Unit
    ) {
        createDialog?.dismiss()
        val dialog =
            AlertTypeSelectDialog(createDialog?.context ?: context, canUpdate, selectTypes) {
                callback.invoke(it)
                createDialog?.show()
            }
        dialog.setOnDismissListener {
            callback.invoke(null)
            createDialog?.show()
        }
        dialog.setOnCancelListener {
            callback.invoke(null)
            createDialog?.show()
        }
        dialog.show()
    }

    fun listDup(
        level: DuplicateEnum,
        callback: (DuplicateEnum?) -> Unit
    ) {
        createDialog?.dismiss()
        val dialog =
            DupSelectDialog(createDialog?.context ?: context, level) {
                callback.invoke(it)
                createDialog?.show()
            }
        dialog.setOnDismissListener {
            callback.invoke(null)
            createDialog?.show()
        }
        dialog.setOnCancelListener {
            callback.invoke(null)
            createDialog?.show()
        }
        dialog.show()
    }
}