package com.jd.oa.joywork.dialog;

import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.R;

import java.util.HashSet;
import java.util.Set;

public class JoyWorkAlertDialog extends AppCompatDialog {
    private static final String TAG = "JoyWorkAlertDialog";

    private RecyclerView mRecyclerView;
    private HashSet<AlertType> mSelectTypes;
    private View.OnClickListener mSureClick;
    public boolean canUpdate = true;

    /**
     * @param clickListener：确定按钮的点击事件，本次所选内容通过 view.tag 获取，类型为 HashSet\<AlertType>
     */
    public JoyWorkAlertDialog(boolean autoDismiss, Context context, Set<AlertType> typeSet, View.OnClickListener clickListener) {
        this(context, 0);
        if (autoDismiss) {
            this.mSureClick = new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    clickListener.onClick(v);
                }
            };
        } else {
            this.mSureClick = clickListener;
        }
        mSelectTypes = new HashSet<>();
        if (typeSet == null || typeSet.isEmpty()) {
            mSelectTypes.add(AlertType.NO);
        } else {
            mSelectTypes.addAll(typeSet);
        }
    }

    private JoyWorkAlertDialog(Context context, int theme) {
        super(context, com.jd.me.datetime.picker.R.style.FullScreenDialogStyle);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.joywork_alert_dialog);

        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }
        this.initView();
    }

    private void initView() {
        View mSure = findViewById(R.id.mSave);
        TextView mTips = findViewById(R.id.mTips);
        if (canUpdate) {
            mSure.setTag(mSelectTypes);
            mSure.setVisibility(View.VISIBLE);
            mSure.setOnClickListener(mSureClick);
            mTips.setText(R.string.joywork_alert_tips);
        } else {
            mSure.setVisibility(View.GONE);
            mTips.setText(R.string.joywork_alert_tips2);
        }
        findViewById(R.id.close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancel();
            }
        });
        mRecyclerView = findViewById(R.id.mRecyclerView);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));
        mRecyclerView.setAdapter(new Adapter());
    }

    private void itemClick(AlertType type) {
        if (!canUpdate) {
            return;
        }
        if (type == AlertType.NO || mSelectTypes.contains(AlertType.NO)) {
            // 1) check no
            // 2) this time check other but last time check NO
            mSelectTypes.clear();
        }
        if (mSelectTypes.contains(type)) {
            mSelectTypes.remove(type);
        } else {
            mSelectTypes.add(type);
        }
        mRecyclerView.getAdapter().notifyDataSetChanged();
    }

    private class Adapter extends RecyclerView.Adapter<VH> {

        private final AlertType[] mTypes = AlertType.values();
        private final LayoutInflater mInflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);

        private View.OnClickListener itemClick = new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                AlertType type = (AlertType) v.getTag();
                itemClick(type);
            }
        };

        @NonNull
        @Override
        public VH onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
            return new VH(getContext(), mInflater.inflate(R.layout.joywork_alert_dialog_item, viewGroup, false));
        }

        @Override
        public void onBindViewHolder(@NonNull VH vh, int i) {
            AlertType type = mTypes[i];
            vh.bind(type, mSelectTypes.contains(type));
            vh.itemView.setTag(type);
            vh.itemView.setOnClickListener(itemClick);
        }

        @Override
        public int getItemCount() {
            return mTypes.length;
        }
    }

    private static class VH extends RecyclerView.ViewHolder {
        private final Context context;
        private final TextView mTitle;
        private final TextView mCheck;

        public VH(Context context, View view) {
            super(view);
            this.context = context;
            mTitle = view.findViewById(R.id.mTitle);
            mCheck = view.findViewById(R.id.mCheck);
        }

        private void bind(AlertType type, boolean checked) {
            mTitle.setText(type.getResId());
            if (checked) {
                mCheck.setVisibility(View.VISIBLE);
            } else {
                mCheck.setVisibility(View.GONE);
            }
        }
    }
}
