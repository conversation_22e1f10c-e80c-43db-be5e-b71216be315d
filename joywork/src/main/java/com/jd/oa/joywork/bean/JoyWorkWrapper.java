package com.jd.oa.joywork.bean;

import java.util.List;

public class JoyWorkWrapper {
    private List<JoyWorkGroup> clientTasks;
    private int total;
    private int pageSize;
    private int page;
    // getGroupTasks 接口使用。有几个接口返回值类似，使用同一个类
    private List<JoyWork> tasks;

    public List<JoyWorkGroup> getClientTasks() {
        return clientTasks;
    }

    public void setClientTasks(List<JoyWorkGroup> clientTasks) {
        this.clientTasks = clientTasks;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public void setTasks(List<JoyWork> tasks){
        this.tasks = tasks;
    }

    public List<JoyWork> getTasks() {
        return tasks;
    }
}
