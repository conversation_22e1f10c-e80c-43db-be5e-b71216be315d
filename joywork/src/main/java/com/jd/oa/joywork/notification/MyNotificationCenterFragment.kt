package com.jd.oa.joywork.notification

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.joywork.*
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.self.base.NetType
import com.jd.oa.joywork.ui.JoyWorkViewModel
import com.jd.oa.joywork.view.red
import com.jd.oa.utils.gone
import com.jd.oa.utils.vertical
import com.jd.oa.utils.visible
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.io.File

class MyNotificationCenterFragment : Fragment() {
    private lateinit var mRecyclerView: RecyclerView
    private var mRefreshLayout: SwipeRefreshLayout? = null
    private val mScope = MainScope()
    private lateinit var mViewModel: JoyWorkViewModel
    private var mPullUpLoadHelper: PullUpLoadHelper? = null
    private var mAllRead: View? = null
    private var mAdapter: NotificationAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.joywork_notification_center, container, false)
    }

    private val notificationUpdateCallback = object : MsgCenterCallback2<String>() {
        override fun onInvoke(t: String) {
            mAdapter?.markRead(t)
            if (::mViewModel.isInitialized) {
                mViewModel.postUpdateNotification(true)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        mViewModel = ViewModelProviders.of(requireActivity()).get(JoyWorkViewModel::class.java)

        mRecyclerView = view.findViewById(R.id.mRecyclerView)
        mRecyclerView.vertical()
        val refreshView = view.findViewById<SwipeRefreshLayout>(R.id.mRefreshView)
        refreshView.red(requireActivity())
        refreshView.setOnRefreshListener {
            refreshView.isRefreshing = true
            mViewModel.postUpdateNotification(true)
            net(NetType.REFRESH)
        }
        mRefreshLayout = refreshView
        net(NetType.INIT)

        mAllRead = view.findViewById<View>(R.id.mAllRead)
        mAllRead?.setOnClickListener {
            markAllRead()
        }
        mViewModel.notificationListRefreshLiveData.observe(requireActivity()) {
            net(NetType.PULL_DOWN_LOAD_MORE)
        }
        JoyWorkMsgCenter.registerNotificationRead(notificationUpdateCallback)
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser && ::mViewModel.isInitialized) {
            mViewModel.postUpdateNotification(false)
        }
    }

    private fun markAllRead() {
        mScope.launch {
            val result = NotifyRepo.markAllRead()
            result.isSuccess({ _, msg, _ ->
                Toast.makeText(requireContext(), msg, Toast.LENGTH_SHORT).show()
            }) {
                mAdapter?.markAllRead()
                mViewModel.postUpdateNotification(true)
                Toast.makeText(
                    requireContext(),
                    R.string.joywork_all_read_success,
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun net(type: NetType) {
        mScope.launch {
            when (type) {
                NetType.INIT -> {
                    val notification = NotifyRepo.listNotification()
                    notification.isSuccess({ _, msg, _ ->
                        showError(msg)
                    }) {
                        if (it == null || !it.dynamics.isLegalList()) {
                            showEmpty()
                        } else {
                            initAdapter(it)
                        }
                    }
                }
                NetType.REFRESH -> {
                    mRefreshLayout?.isRefreshing = true
                    val notification = NotifyRepo.listNotification()
                    mRefreshLayout?.isRefreshing = false
                    notification.isSuccess({ _, msg, _ ->
                        showError(msg)
                    }) {
                        if (it != null && it.dynamics.isLegalList()) {
                            if (mAdapter == null) {
                                initAdapter(it)
                            } else {
                                // 替换掉数据内容
                                mAdapter?.refresh(it.dynamics!!)
                                mAllRead?.visible()
                            }
                        } else {
                            showEmpty()
                        }
                    }
                }
                NetType.PULL_DOWN_LOAD_MORE -> {
                    val notification = mAdapter?.firstItem()
                    if (notification == null) {
                        // There is no notification when the user opened this page,
                        // and then the user switches to another page and uses it for a while
                        // During this time, the user maybe receive some notifications
                        // Therefore, when the user switches back to the current page, the if will be matched
                        net(NetType.INIT)
                        return@launch
                    }
                    val result =
                        NotifyRepo.loadMoreUpNotification(
                            notification.createTime,
                            notification.dyId,
                            notification.id
                        )
                    result.ifSuccessSilence {
                        val ds = it?.dynamics ?: mutableListOf()
                        if (ds.size >= NotifyRepo.limit) {
                            mAdapter?.refresh(ds)
                        } else {
                            mAdapter?.mergeUp(it?.dynamics ?: mutableListOf())
                        }
                    }
                }
                NetType.LOADMORE -> {
                    val notification = mAdapter?.lastItem() ?: return@launch
                    val result =
                        NotifyRepo.loadMoreDownNotification(
                            notification.createTime,
                            notification.dyId,
                            notification.id
                        )
                    result.isSuccess({ _, _, _ ->
                        mPullUpLoadHelper?.setLoaded()
                    }) {
                        if (it?.dynamics == null) {
                            mPullUpLoadHelper?.setLoaded()
                        } else if (it.dynamics.isEmpty()) {
                            mPullUpLoadHelper?.setComplete()
                        } else {
                            mAdapter?.mergeDown(it.dynamics)
                            if (it.dynamics.size < NotifyRepo.limit) {
                                mPullUpLoadHelper?.setComplete()
                            } else {
                                mPullUpLoadHelper?.setLoaded()
                            }
                        }
                    }
                }
            }
        }
    }

    private fun setupLoadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(mRecyclerView) {
            net(NetType.LOADMORE)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mScope.cancel()
        JoyWorkMsgCenter.unRegisterNotificationRead(notificationUpdateCallback)
    }

    private fun initAdapter(it: JoyWorkNotificationOuter) {
        mAllRead?.visible()
        mAdapter = NotificationAdapter(requireContext(), it.dynamics) {
            clickNotification(it)
        }
        mRecyclerView.adapter = mAdapter
        if (it.dynamics.size >= NotifyRepo.limit) {
            // 处理上拉加载更多
            setupLoadMore()
            mPullUpLoadHelper?.setLoaded()
        }
    }

    private fun clickNotification(n: JoyWorkNotification) {
        if (n.deepLink.isLegalString()) {
            n.deepLink.openDeeplink(requireContext())
        }
    }

    private fun showError(msg: String) {
        if (mAdapter == null) {
            mRecyclerView.adapter =
                JoyWorkEmptyAdapter.custom(requireContext(), msg, R.drawable.joywork_empty_like)
        } else {
            Toast.makeText(requireContext(), msg, Toast.LENGTH_SHORT).show()
        }
    }

    private fun showEmpty() {
        mAllRead?.gone()
        mRecyclerView.adapter = JoyWorkEmptyAdapter.empty(
            requireContext(),
            R.string.joywork_notifiy_empty,
            R.drawable.joywork_notification_empty
        )
    }
}