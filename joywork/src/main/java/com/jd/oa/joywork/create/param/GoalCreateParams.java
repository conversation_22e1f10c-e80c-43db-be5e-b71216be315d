package com.jd.oa.joywork.create.param;

import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.joywork.create.Value;
import com.jd.oa.joywork.team.kpi.KpiRepo;
import com.jd.oa.router.DeepLink;

import org.json.JSONException;
import org.json.JSONObject;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * 【NOTICE】：The deeplink only works in android.
 * mparam={"goalId":"xx","appName":"goal"}
 */
public class GoalCreateParams extends DeeplinkCreateParam {
    public static final String APP_NAME = "goal";

    private String goalId;

    public static String genDeeplink(String goalId) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("appName", APP_NAME);
            jsonObject.put("goalId", goalId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return DeepLink.JOY_WORK_CREATE + "?mparam=" + jsonObject;
    }

    public static String genParamsStr(String goalId) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("appName", APP_NAME);
            jsonObject.put("goalId", goalId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }

    @Override
    public void onParseParam(JSONObject object) throws JSONException {
        goalId = object.getString("goalId");
    }

    @Override
    public void initCreateValue(final Value value) {
        KpiRepo.INSTANCE.getGoalDetail(goalId, new Function1<KpiTarget, Unit>() {
            @Override
            public Unit invoke(KpiTarget target) {
                if (target != null) {
                    value.addTarget(target);
                }
                return null;
            }
        });
    }

    @Override
    public String getAppName() {
        return APP_NAME;
    }
}
