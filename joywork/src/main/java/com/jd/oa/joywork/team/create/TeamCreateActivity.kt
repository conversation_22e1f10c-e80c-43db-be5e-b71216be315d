package com.jd.oa.joywork.team.create

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TeamUserRole
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.ProjectDetail
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.dialog.thirdparty.CustomAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.CustomConfig
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.openDeeplink
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.RepoCallback
import com.jd.oa.joywork.team.TeamMemberListActivity
import com.jd.oa.router.DeepLink
import com.jd.oa.joywork.team.chat.ProjectSelectorTeamCreateHelper
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder
import com.jd.oa.utils.InputMethodUtils
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.TextWatcherAdapter
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.maxLenEx
import com.jd.oa.utils.observerLengthOf
import com.jd.oa.utils.visible
import kotlinx.android.synthetic.main.jdme_activity_project_create.et_project_des
import kotlinx.android.synthetic.main.jdme_activity_project_create.et_project_name
import kotlinx.android.synthetic.main.jdme_activity_project_create.icon2
import kotlinx.android.synthetic.main.jdme_activity_project_create.ll_members
import kotlinx.android.synthetic.main.jdme_activity_project_create.mExample
import kotlinx.android.synthetic.main.jdme_activity_project_create.mTextLength
import kotlinx.android.synthetic.main.jdme_activity_project_create.recycler_relation
import kotlinx.android.synthetic.main.jdme_activity_project_create.tv_relation_number

/**
 * 新建清单
 */
@Route(DeepLink.JOY_WORK_SET_PROJECT)
class TeamCreateActivity : BaseActivity() {
    companion object {
        const val RELATION_REQ = 100

        enum class From {
            PROJECT_SELECTOR {
                override fun createHelper(): TeamCreateHelper {
                    return ProjectSelectorTeamCreateHelper()
                }
            },
            DEFAULT {
                override fun createHelper(): TeamCreateHelper {
                    return TeamCreateHelper()
                }
            };

            abstract fun createHelper(): TeamCreateHelper
        }

        fun getIntent(context: Context, from: From): Intent {
            val intent = Intent(context, TeamCreateActivity::class.java)
            intent.putExtra("TeamCreateActivity_from", from.ordinal)
            return intent
        }

        fun getFrom(intent: Intent): From {
            return try {
                From.values()[intent.getIntExtra("TeamCreateActivity_from", From.DEFAULT.ordinal)]
            } catch (e: Throwable) {
                Log.w(
                    "TeamCreateActivity",
                    "the from ${
                        intent.getIntExtra(
                            "TeamCreateActivity_from",
                            From.DEFAULT.ordinal
                        )
                    } is illegal",
                )
                From.DEFAULT
            }
        }

        open class TeamCreateHelper {
            open fun addMember(intent: Intent, callback: (List<Members>) -> Unit) {
                callback(emptyList())
            }

            open fun memberListClickable(intent: Intent): Boolean {
                return true
            }
        }
    }

    private val mHelper: TeamCreateHelper by lazy {
        getFrom(intent).createHelper()
    }

    lateinit var tvTitle: TextView
    lateinit var tvNew: TextView
    lateinit var mAdapter: HeadAdapter
    var memberList = mutableListOf<Members>()
    var showHeadNumber: Int = 9
    var createFinish = false
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_project_create)
        initView()
    }

    private fun initView() {
        showHeadNumber = if (LocaleUtils.getUserSetLocaleStr(this) == "zh_CN") 8 else 5
        hideAction()
        initActionbar()
        et_project_name.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                val it = s?.trim().toString().isNotEmpty()
                tvNew.isEnabled = it
                tvNew.setTextColor(
                    ContextCompat.getColor(
                        this@TeamCreateActivity,
                        if (it) R.color.red_warn else R.color.color_4bf0250f
                    )
                )
            }
        })
        et_project_name.maxLenEx = 200
        mTextLength observerLengthOf et_project_name
        if (mHelper.memberListClickable(intent)) {
            ll_members.setOnClickListener {
                addRelation()
            }
            icon2.visible()
        } else {
            icon2.gone()
        }
        mExample.setOnClickListener {
            JoyWorkConstant.LIST_EXAMPLE_DEEPLINK.openDeeplink()
        }
        recycler_relation.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        mAdapter = HeadAdapter(this, mutableListOf())
        addCreatorInfo(memberList)
        recycler_relation.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                if (parent.getChildPosition(view) != (memberList.size - 1)) {
                    outRect.right = -32
                }
            }
        }, 0)
        recycler_relation.adapter = mAdapter
        notifyChanged()
        if (applicationInfo.targetSdkVersion >= Build.VERSION_CODES.P) {
            InputMethodUtils.showSoftInputFromWindow(this, et_project_name)
        }

        mHelper.addMember(intent) {
            if (it.isLegalList()) {
                memberList.addAll(it)
                notifyChanged()
            }
        }
    }

    private fun addCreatorInfo(memberList: MutableList<Members>) {
        val self = Members()
        self.fromJoyWorkUser(JoyWorkUser.getSelf())
        self.apply {
//            userId = PreferenceManager.UserInfo.getUserName()
//            realName = PreferenceManager.UserInfo.getUserRealName()
//            headImg = PreferenceManager.UserInfo.getUserCover()
//            emplAccount = PreferenceManager.UserInfo.getUserName()
//            ddAppId = PreferenceManager.UserInfo.getTimlineAppID()
            permission = 1
            userRole = TeamUserRole.CREATOR.permission
        }
        memberList.add(self)
    }

    private fun initActionbar() {
        tvTitle = findViewById(R.id.tv_title)
        tvNew = findViewById(R.id.tv_confirm)
        findViewById<View>(R.id.iv_close).setOnClickListener {
            finish()
        }
        tvNew.setOnClickListener {
            createProject()
        }
    }

    private fun createProject() {
        ProjectRepo.createProject(memberList, et_project_name.text.trim().toString(),
            et_project_des.text.trim().toString(),
            object : RepoCallback<ProjectDetail> {
                override fun result(t: ProjectDetail?, errorMsg: String?, success: Boolean) {
                    if (success && t != null) {
                        createFinish = true
                        ToastUtils.showInfoToast(R.string.me_save_success)
                        val intent = Intent().apply {
                            putExtra("projectId", t.projectId)
                            putExtra("title", t.title)
                            putExtra("desc", t.desc)
                        }
                        setResult(RESULT_OK, intent)
                        finish()
                    } else {
                        ToastUtils.showInfoToast(JoyWorkEx.filterErrorMsg(errorMsg))
                    }
                }
            })
    }

    /**
     * 添加协作人
     */
    private fun addRelation() {
        val activity = this
        if (activity.isFinishing || activity.isDestroyed) {
            return
        }
        val intent = Intent(activity, TeamMemberListActivity::class.java)
        OneTimeDataRepo.data = memberList
        activity.startActivityForResult(intent, RELATION_REQ)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == RELATION_REQ && resultCode == RESULT_OK) {
            try {
                (OneTimeDataRepo.data as? ArrayList<Members>)?.apply {
                    <EMAIL> = this
                    notifyChanged()
                }
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
    }

    private fun notifyChanged() {
        var tempList = memberList
        if (memberList.size > showHeadNumber) {
            tempList = memberList.subList(0, showHeadNumber)
        }
        tv_relation_number.text =
            if (LocaleUtils.getUserSetLocaleStr(this) == "zh_CN") "${memberList.size}人" else memberList.size.toString()
        mAdapter.replaceData(tempList)
    }

    inner class HeadAdapter(context: Context, datas: List<Members>) :
        BaseRecyclerViewAdapter<Members>(context, datas) {
        override fun getItemViewType(position: Int): Int {
            return if (memberList.size > showHeadNumber && position == showHeadNumber - 1) 1 else 0
        }

        override fun getItemLayoutId(viewType: Int): Int {
            return if (viewType == 0) R.layout.jdme_head_item else R.layout.jdme_view_head_more
        }

        override fun onConvert(holder: BaseRecyclerViewHolder?, item: Members?, position: Int) {
            if (holder?.itemViewType == 0) {
                JoyWorkViewItem.avatar(holder.getView(R.id.iv_head), item?.headImg)
            }
        }
    }

    override fun finish() {
        if (!createFinish && et_project_name.text.toString().trim().isNotEmpty()) {
            CustomAlertDialog(this, CustomConfig().apply {
                msgRes = R.string.joywork_project_create_cancle_tip
                okRes = R.string.me_ok
                okShitClick = {
                    super.finish()
                }
            }).show()
        } else {
            super.finish()
        }
    }
}