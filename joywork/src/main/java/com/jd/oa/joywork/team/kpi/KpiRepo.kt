package com.jd.oa.joywork.team.kpi

import android.net.Uri
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.*
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.search.SearchResultList
import com.jd.oa.joywork.team.bean.*
import com.jd.oa.network.SimpleReqCallbackAdapter
import com.jd.oa.network.httpmanager.GatewayNetEnvironment
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.ResponseInfo
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject
import java.util.*
import kotlin.collections.HashMap

object KpiRepo {

    val page_size = 5

    fun refreshNums(id: String?, callback: (NumbersList?) -> Unit) {
        if (!id.isLegalString()) {
            return
        }
        val params = hashMapOf<String, Any>()
        params["ids"] = listOf(id)
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<NumbersList>(NumbersList::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(null)
                }

                override fun onSuccess(
                    jsonObject: NumbersList?,
                    tArray: List<NumbersList?>?,
                    rawData: String
                ) {
                    callback.invoke(jsonObject)
                }
            }),
            "work.task.getExtraNums.v1"
        )
    }

    fun getKrDetail(krId: String, callback: (KR?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["krId"] = krId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<KR>(KR::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(null)
                }

                override fun onSuccess(
                    jsonObject: KR?,
                    tArray: List<KR?>?,
                    rawData: String
                ) {
                    callback.invoke(jsonObject)
                }
            }),
            "work.task.getKrDetail.v1"
        )
    }

    fun getGoalDetail(goalId: String, callback: (KpiTarget?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["goalId"] = goalId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<KpiTarget>(KpiTarget::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(null)
                }

                override fun onSuccess(
                    jsonObject: KpiTarget?,
                    tArray: List<KpiTarget?>?,
                    rawData: String
                ) {
                    callback.invoke(jsonObject)
                }
            }),
            "work.task.getPerfGoalDetail.v1"
        )
    }

    fun likeProgress(progressId: String) {
        val params = hashMapOf<String, Any>()
        params["progressId"] = progressId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<KpiGroups>(
                    KpiGroups::class.java
                ) {
                override fun onFailure(errorMsg: String?, code: Int) {
                }

                override fun onSuccess(
                    jsonObject: KpiGroups?,
                    tArray: List<KpiGroups?>?,
                    rawData: String
                ) {
                }
            }),
            "work.task.progressPraise.v1"
        )
    }

    fun delTarget(targetId: String, callback: (String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["goalId"] = targetId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<KpiGroups>(
                    KpiGroups::class.java
                ) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: KpiGroups?,
                    tArray: List<KpiGroups?>?,
                    rawData: String
                ) {
                    callback.invoke(null)
                }
            }),
            JoyWorkNetConstant.DEL_TARGET
        )
    }

    /**
     * Pull all goals which belonged to a specified q.
     * Also, the server will return a part of KR belonged to the first goal.
     * @param qId: Specify the q of its goals you want
     */
    suspend fun listQTargets(
        qId: String,
        returnTask: Boolean,
    ): CoroutineResult<KpiGroups> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["assessPeriod"] = qId
            if (returnTask) {
                params["returnTask"] = true
                params["need"] = page_size
                params["limit"] = page_size
            } else {
                params["returnTask"] = false
            }
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<KpiGroups>(
                        KpiGroups::class.java
                    ) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KpiGroups>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KpiGroups?,
                        tArray: List<KpiGroups?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<KpiGroups>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                JoyWorkNetConstant.LIST_TARGET_Q
            )
        }
    }

    /**
     * List all q
     */
    suspend fun listQ(): CoroutineResult<KpiGroups2> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["perfKrTypeEnum"] = "First"
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<KpiGroups2>(KpiGroups2::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KpiGroups2>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KpiGroups2?,
                        tArray: List<KpiGroups2?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<KpiGroups2>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                JoyWorkNetConstant.LIST_TARGET_CUR
            )
        }
    }

    suspend fun listOtherQTargets(
        qId: String,
        returnTask: Boolean,
        emplAccount: String, ddAppId: String
    ): CoroutineResult<KpiGroups> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["assessPeriod"] = qId
            params["emplAccount"] = emplAccount
            params["ddAppId"] = ddAppId
            if (returnTask) {
                params["returnTask"] = true
                params["need"] = page_size
                params["limit"] = page_size
            } else {
                params["returnTask"] = false
            }
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<KpiGroups>(
                        KpiGroups::class.java
                    ) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KpiGroups>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KpiGroups?,
                        tArray: List<KpiGroups?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<KpiGroups>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.getOthersGoalWithKrByPeriod.v1"
//                "work.task.getOtherPeopleGoalWithKrByPeriod.v1"
            )
        }
    }

    /**
     * List all q
     */
    suspend fun listOtherQ(emplAccount: String, ddAppId: String): CoroutineResult<KpiGroups2> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["perfKrTypeEnum"] = "First"
            params["emplAccount"] = emplAccount
            params["ddAppId"] = ddAppId
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<KpiGroups2>(KpiGroups2::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KpiGroups2>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KpiGroups2?,
                        tArray: List<KpiGroups2?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<KpiGroups2>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.getOtherPeopleGoals.v1"
            )
        }
    }

    /**
     * List q and all goals and kr which belonged to first q
     */
    suspend fun listQAndKr(): CoroutineResult<KpiGroups> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            // return all goals and their krs, instead of paging of kr
            params["perfKrTypeEnum"] = "All"
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<KpiGroups>(
                        KpiGroups::class.java
                    ) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KpiGroups>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KpiGroups?,
                        tArray: List<KpiGroups?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<KpiGroups>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.goalKrSelector.v1"
            )
        }
    }

    /**
     * Returns all goals and kr belonging to assessPeriod
     */
    suspend fun listGoalKrByQ(assessPeriod: String): CoroutineResult<KpiGroups> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["perfKrTypeEnum"] = "All"
            params["assessPeriod"] = assessPeriod
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<KpiGroups>(
                        KpiGroups::class.java
                    ) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KpiGroups>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KpiGroups?,
                        tArray: List<KpiGroups?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<KpiGroups>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.goalKrSelector.v1"
            )
        }
    }

    suspend fun newTarget(name: String, assessPeriod: String): CoroutineResult<JSONObject> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["itemName"] = name
            params["assessPeriod"] = assessPeriod
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<JSONObject>(
                        JSONObject::class.java
                    ) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: JSONObject?,
                        tArray: List<JSONObject?>?,
                        rawData: String
                    ) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().success(JSONObject())
                            )
                        )
                    }
                }),
                "work.task.createGoal.v1"
            )
        }
    }

    suspend fun updateTarget(name: String, goalId: String): CoroutineResult<JSONObject> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["itemName"] = name
            params["goalId"] = goalId
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<JSONObject>(
                        JSONObject::class.java
                    ) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: JSONObject?,
                        tArray: List<JSONObject?>?,
                        rawData: String
                    ) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().success(JSONObject())
                            )
                        )
                    }
                }),
                "work.task.editGoal.v1"
            )
        }
    }

    suspend fun newKr(content: String, goalId: String): CoroutineResult<KR> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["content"] = content
            params["goalId"] = goalId
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object : JoyWorkReqCallback<KR>(KR::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KR>().failure(
                                    JoyWorkEx.filterErrorMsg(
                                        errorMsg
                                    )
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KR?,
                        tArray: List<KR?>?,
                        rawData: String
                    ) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KR>().success(
                                    jsonObject
                                        ?: KR.stub
                                )
                            )
                        )
                    }
                }),
                "work.task.createKr.v1"
            )
        }
    }

    suspend fun updateKr(content: String, krId: String): CoroutineResult<JSONObject> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["content"] = content
            params["krId"] = krId
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<JSONObject>(
                        JSONObject::class.java
                    ) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: JSONObject?,
                        tArray: List<JSONObject?>?,
                        rawData: String
                    ) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().success(JSONObject())
                            )
                        )
                    }
                }),
                "work.task.editKr.v1"
            )
        }
    }

    fun delKr(krId: String, callback: (String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["krId"] = krId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<KpiGroups>(
                    KpiGroups::class.java
                ) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: KpiGroups?,
                    tArray: List<KpiGroups?>?,
                    rawData: String
                ) {
                    callback.invoke(null)
                }
            }),
            "work.task.deleteKr.v1"
        )
    }

    fun swapKr(krId: String, front: String?, after: String?, callback: (String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["krId"] = krId
        if (front.isLegalString()) {
            params["front"] = front!!
        }
        if (after.isLegalString()) {
            params["after"] = after!!
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<KpiGroups>(
                    KpiGroups::class.java
                ) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: KpiGroups?,
                    tArray: List<KpiGroups?>?,
                    rawData: String
                ) {
                    callback.invoke(null)
                }
            }),
            "work.task.krSort.v1"
        )
    }

    /**
     * List kr belonged to specific goal
     */
    suspend fun listTargetKr(goalId: String, offset: Int): CoroutineResult<KRList> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["goalId"] = goalId
            params["limit"] = page_size
            params["offset"] = offset
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<KRList>(KRList::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<KRList>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: KRList?,
                        tArray: List<KRList?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<KRList>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.getKrListByGoalId.v1"
            )
        }
    }

    suspend fun listLike(progressId: String): CoroutineResult<LikeList> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["progressId"] = progressId
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<LikeList>(LikeList::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<LikeList>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: LikeList?,
                        tArray: List<LikeList?>?,
                        rawData: String
                    ) {
                        if (jsonObject == null) {
                            onFailure("", -1)
                            return
                        }
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<LikeList>().success(jsonObject)
                            )
                        )
                    }
                }),
                "work.task.findProgressPraises.v1"
            )
        }
    }

    fun shareTarget(
        goalId: String,
        groupIds: List<String>?,
        users: List<Members>?,
        callback: (Boolean, String?) -> Unit
    ) {
        if (!users.isLegalList() && !groupIds.isLegalList()) {
            return
        }
        val params = hashMapOf<String, Any>()
        params["goalId"] = goalId
        if (users.isLegalList()) {
            val us = users!!.map {
                it.toSimpleMap()
            }
            params["ddUsers"] = us
        }
        if (groupIds.isLegalList()) {
            params["groupIds"] = groupIds!!
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<LikeList>(LikeList::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback(false, errorMsg)
                }

                override fun onSuccess(
                    jsonObject: LikeList?,
                    tArray: List<LikeList?>?,
                    rawData: String
                ) {
                    callback(true, null)
                }
            }),
            "work.task.shareGoal.v1"
        )
    }

    /**
     * Share all goals and KRs within one attendance cycle
     * @param owner: the user whose goals and KRs was shared
     * @param groupIds: the groups to which it was shared
     * @param users: the users to which it was shared
     */
    fun sharePeriod(
        assessPeriod: String,
        assessPeriodDesc: String,
        owner: JoyWorkUser,
        groupIds: List<String>?,
        users: List<Members>?,
        callback: (Boolean, String?) -> Unit
    ) {
        if (!users.isLegalList() && !groupIds.isLegalList()) {
            return
        }
        val params = hashMapOf<String, Any>()
        params["assessPeriod"] = assessPeriod
        params["assessPeriodDesc"] = assessPeriodDesc
        params["user"] = owner.toSimpleMap()
        if (users.isLegalList()) {
            val us = users!!.map {
                it.toSimpleMap()
            }
            params["ddUsers"] = us
        }
        if (groupIds.isLegalList()) {
            params["groupIds"] = groupIds!!
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<LikeList>(LikeList::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback(false, errorMsg)
                }

                override fun onSuccess(
                    jsonObject: LikeList?,
                    tArray: List<LikeList?>?,
                    rawData: String
                ) {
                    callback(true, null)
                }
            }),
            "work.task.shareGoal.v2"
        )
    }

    fun shareTask(
        taskId: String,
        groupIds: List<String>?,
        users: List<Members>?,
        callback: (Boolean, String?) -> Unit
    ) {
        if (!users.isLegalList() && !groupIds.isLegalList()) {
            return
        }
        val params = hashMapOf<String, Any>()
        params["taskId"] = taskId
        if (users.isLegalList()) {
            val us = users!!.map {
                it.toSimpleMap()
            }
            params["ddUsers"] = us
        }
        if (groupIds.isLegalList()) {
            params["groupIds"] = groupIds!!
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<LikeList>(LikeList::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback(false, errorMsg)
                }

                override fun onSuccess(
                    jsonObject: LikeList?,
                    tArray: List<LikeList?>?,
                    rawData: String
                ) {
                    callback(true, null)
                }
            }),
            "work.task.shareTask.v1"
        )
    }

    fun grayInfo(callback: (GrayInfo) -> Unit) {
        val params = hashMapOf<String, Any>()
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<GrayInfo>(GrayInfo::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback(GrayInfo())
                }

                override fun onSuccess(
                    jsonObject: GrayInfo?,
                    tArray: List<GrayInfo?>?,
                    rawData: String
                ) {
                    callback(jsonObject ?: GrayInfo())
                }
            }),
            "work.task.isGray.v2"
        )
    }

    suspend fun createSummary(
        joyWorkSpace: JoyWorkSpace,
        penta: Penta<Long, Long, KpiQ, Int, Int>,
        goalIds: List<WeeklyReportGoal>,
        weekNum: Int,
        assessPeriod: String,
        qDesc: String,
    ): CoroutineResult<WeeklySummaryResult> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["week"] = weekNum
            params["year"] = penta.forth
            params["assessPeriod"] = assessPeriod
            params["goalIds"] = goalIds
            params["teamId"] = joyWorkSpace.teamId
            if (joyWorkSpace.folderId.isLegalString()) {
                params["folderId"] = joyWorkSpace.folderId
            }
            params["assessPeriodDesc"] = qDesc
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<WeeklySummaryResult>(WeeklySummaryResult::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<WeeklySummaryResult>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: WeeklySummaryResult?,
                        tArray: List<WeeklySummaryResult?>?,
                        rawData: String
                    ) {
                        if (jsonObject == null) {
                            onFailure("", -1)
                        } else {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<WeeklySummaryResult>().success(jsonObject)
                                )
                            )
                        }
                    }
                }),
                "work.task.weeklyReports.v2"
            )
        }
    }


    suspend fun checkDir(
        folderId: String?,
        teamId: String?
    ): CoroutineResult<JSONObject> {
        val networkEnv = GatewayNetEnvironment.getCurrentEnv()
        val uri = Uri.parse(networkEnv.baseUrl)
        val scheme = if (Objects.equals(uri.scheme, "http")) "https" else uri.scheme
        val host: String = uri.host ?: ""
        val port = uri.port
        var url = if (port > 0) {
            "$scheme://$host:$port"
        } else {
            "$scheme://$host"
        }
        url = if (folderId.isLegalString()) {
            "$url/v2/folders/${folderId}"
        } else {
            "$url/v1/teams/${teamId}"
        }
        val a = Uri.parse(url).buildUpon().appendQueryParameter("appid", networkEnv.appId)
        if (folderId.isLegalString()) {
            a.appendQueryParameter("api", "joyspace.folder.get")
                .appendQueryParameter("sendRecent", "0")
        } else {
            a.appendQueryParameter("api", "joyspace.team.get")
        }

        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            val header = HashMap<String, String>()
            header[HttpManager.HEADER_KEY_METHOD_GET] = "true"
            HttpManager.post(
                null,
                header,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<JSONObject>(JSONObject::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(responseInfo: ResponseInfo<String?>?) {
                        if (responseInfo == null) {
                            onFailure("")
                        } else {
                            val rawData = responseInfo.result
                            if (!rawData.isLegalString()) {
                                onFailure("")
                            } else {
                                try {
                                    val j = JSONObject(rawData!!)
                                    if (Objects.equals(j.getString("status"), "success")) {
                                        val data = j.getJSONObject("data")
                                        c.resumeWith(
                                            Result.success(
                                                CoroutineResult<JSONObject>().success(
                                                    data
                                                )
                                            )
                                        )
                                    } else {
                                        onFailure("", -1)
                                    }
                                } catch (e: Exception) {
                                    onFailure("", -1)
                                }
                            }
                        }
                    }
                }),
                a.toString()
            )
        }
    }

    suspend fun getGoalVisible(
        goalId: String,
    ): CoroutineResult<VisibleSetting> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["goalId"] = goalId
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<VisibleSetting>(VisibleSetting::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<VisibleSetting>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: VisibleSetting?,
                        tArray: List<VisibleSetting?>?,
                        rawData: String
                    ) {
                        if (jsonObject == null) {
                            onFailure("", -1)
                        } else {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<VisibleSetting>().success(jsonObject)
                                )
                            )
                        }
                    }
                }),
                "work.task.getGoalVisible.v1"
            )
        }
    }

    suspend fun getVisibleUsers(
        goalId: String,
    ): CoroutineResult<VisibleSetting> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["goalId"] = goalId
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<VisibleSetting>(VisibleSetting::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<VisibleSetting>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: VisibleSetting?,
                        tArray: List<VisibleSetting?>?,
                        rawData: String
                    ) {
                        if (jsonObject == null) {
                            onFailure("", -1)
                        } else {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<VisibleSetting>().success(jsonObject)
                                )
                            )
                        }
                    }
                }),
                "work.task.getAuthorizedUsers.v1"
            )
        }
    }


    suspend fun saveVisibleSetting(
        goalId: String,
        visibleType: Int,
        added: List<JoyWorkUser>? = null,
        deleted: List<JoyWorkUser>? = null,
        addedDepts: List<DeptInfo>? = null,
        deletedDepts: List<DeptInfo>? = null,
    ): CoroutineResult<JSONObject> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["goalId"] = goalId
            params["visibleType"] = visibleType
            if (visibleType == VisibleSetting.TYPE_PART) {
                val list = mutableListOf<HashMap<String, String>>()
                added?.forEach {
                    val map = HashMap<String, String>()
                    map["emplAccount"] = it.emplAccount
                    map["ddAppId"] = it.ddAppId
                    list.add(map)
                }
                if (list.isLegalList()) {
                    params["add"] = list
                }

                val list2 = mutableListOf<HashMap<String, String>>()
                deleted?.forEach {
                    val map = HashMap<String, String>()
                    map["emplAccount"] = it.emplAccount
                    map["ddAppId"] = it.ddAppId
                    list2.add(map)
                }
                if (list2.isLegalList()) {
                    params["del"] = list2
                }
                if (addedDepts.isLegalList()) {
                    params["addDept"] = addedDepts!!
                }
                if (deletedDepts.isLegalList()) {
                    params["delDept"] = deletedDepts!!
                }
            }
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<JSONObject>(JSONObject::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: JSONObject?,
                        tArray: List<JSONObject?>?,
                        rawData: String
                    ) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().success(JSONObject())
                            )
                        )
                    }
                }),
                "work.task.setGoalVisible.v1"
            )
        }
    }

    const val SUB_PAGE_SIZE = 20

    suspend fun listOrg(type: Int): CoroutineResult<OrgList> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["type"] = type
            params["offset"] = 0
            params["limit"] = SUB_PAGE_SIZE
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<OrgList>(OrgList::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<OrgList>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: OrgList?,
                        tArray: List<OrgList?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<OrgList>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.getOrganizationList.v1"
            )
        }
    }


    suspend fun listSub(offset: Int): CoroutineResult<OrgList> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["type"] = OrgList.SUB
            params["offset"] = offset
            params["limit"] = SUB_PAGE_SIZE
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<OrgList>(OrgList::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<OrgList>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: OrgList?,
                        tArray: List<OrgList?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<OrgList>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.getOrganizationList.v1"
            )
        }
    }

    suspend fun listFollowed(offset: Int): CoroutineResult<OrgList> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            params["offset"] = offset
            params["limit"] = SUB_PAGE_SIZE
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    JoyWorkReqCallback<OrgList>(OrgList::class.java) {
                    override fun onFailure(errorMsg: String?, code: Int) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<OrgList>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }

                    override fun onSuccess(
                        jsonObject: OrgList?,
                        tArray: List<OrgList?>?,
                        rawData: String
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<OrgList>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }
                }),
                "work.task.getFollowGoalList.v1"
            )
        }
    }

    /**
     * follow or cancel follow someone
     */
    fun followOrCancelSomeone(
        emplAccount: String,
        ddAppId: String,
        followed: Boolean,
        callback: (String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["emplAccount"] = emplAccount
        params["ddAppId"] = ddAppId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<JSONObject>(JSONObject::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JSONObject?,
                    tArray: List<JSONObject?>?,
                    rawData: String
                ) {
                    callback.invoke(null)
                }
            }),
            if (followed) "work.task.cancelFollowGoal.v1" else "work.task.followGoal.v1"
        )
    }

    fun searchDepartments(
        key: String,
        offset: Int,
        callback: (DepartmentContent?, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["keyWords"] = key
        params["limit"] = 50
        params["offset"] = offset
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<DepartmentContent>(DepartmentContent::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: DepartmentContent?,
                    tArray: List<DepartmentContent?>?,
                    rawData: String
                ) {
                    if (jsonObject != null) {
                        callback.invoke(jsonObject, null)
                    } else {
                        onFailure("", -1)
                    }
                }
            }),
            "work.task.searchDept.v1"
        )
    }

    fun searchJoyWork(
        key: String,
        taskStatus: Int,
        offset: Int,
        callback: (SearchResultList?, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["keyWords"] = key.trim()
        params["taskStatus"] = taskStatus
        params["limit"] = 50
        params["offset"] = offset
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                JoyWorkReqCallback<SearchResultList>(SearchResultList::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.invoke(null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: SearchResultList?,
                    tArray: List<SearchResultList?>?,
                    rawData: String
                ) {
                    if (jsonObject != null) {
                        callback.invoke(jsonObject, null)
                    } else {
                        onFailure("", -1)
                    }
                }
            }),
            "work.task.taskSearch.v3"
        )
    }
}