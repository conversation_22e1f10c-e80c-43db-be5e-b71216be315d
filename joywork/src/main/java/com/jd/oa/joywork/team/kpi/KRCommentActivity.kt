package com.jd.oa.joywork.team.kpi

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.webkit.WebView
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.airbnb.lottie.LottieAnimationView
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.fragment.js.hybrid.JsIm
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightObserver
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.detail.ui.JsTargetInterface
import com.jd.oa.joywork.title.CenterTitleFragment
import com.jd.oa.joywork.utils.JoyWorkNetConfig
import com.jd.oa.joywork.utils.getParamsStr
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.argbId
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import kotlinx.android.synthetic.main.joywork_kpi_target_detail.*
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler
import java.util.*

private class KRCommentReceiver(private val id: String) : BroadcastReceiver() {
    var krCallback: ((String) -> Unit)? = null
    var goalCallback: ((String) -> Unit)? = null
    override fun onReceive(context: Context, intent: Intent) {
        if (Objects.equals(intent.getStringExtra("type"), "kr")) {
            krCallback?.invoke(id)
        } else {
            goalCallback?.invoke(id)
        }
        LocalBroadcastManager.getInstance(context).unregisterReceiver(this)
    }
}

@Route(DeepLink.JOYWORK_GOAL_KR_COMMENT)
class KRCommentActivity : BaseActivity(), KeyboardHeightObserver {

    companion object {
        private val goal_action = "goal.comment.receiver.action"

        fun startKRComment(context: Context, target: KR, finishCallback: (id: String) -> Unit) {
            val intent = Intent(context, KRCommentActivity::class.java)
            intent.putExtra("type", "kr")
            intent.putExtra("id", target.krId)
            val r = KRCommentReceiver(target.krId)
            r.krCallback = finishCallback
            registerCallback(context, r)
            context.startActivity(intent)
        }

        fun startGoalComment(
            context: Context,
            target: KpiTarget,
            finishCallback: (id: String) -> Unit
        ) {
            val intent = Intent(context, KRCommentActivity::class.java)
            intent.putExtra("type", "goal")
            intent.putExtra("id", target.goalId)
            val r = KRCommentReceiver(target.goalId)
            r.goalCallback = finishCallback
            registerCallback(context, r)
            context.startActivity(intent)
        }

        private fun KRCommentActivity.parseDeeplink() {
            try {
                // jdme://jm/biz/joywork/goalComment?mparam={"krId":"493112009079259136"}
                val str = intent.getParamsStr()
                val obj = JSONObject(str)
                if (obj.has("goalId")) {
                    intent.putExtra("type", "goal")
                    intent.putExtra("id", obj.getString("goalId"))
                } else {
                    intent.putExtra("type", "kr")
                    intent.putExtra("id", obj.getString("krId"))
                }
                if (obj.has("commentId")) {
                    intent.putExtra("commentId", obj.getString("commentId"))
                }
            } catch (e: Throwable) {
//                finish()
            }
        }

        private fun KRCommentActivity.getCommentId(): String? {
            return intent.getStringExtra("commentId")
        }

        private fun KRCommentActivity.getType(): String {
            return intent.getStringExtra("type") ?: ""
        }

        private fun KRCommentActivity.getId(): String {
            return intent.getStringExtra("id") ?: ""
        }

        private fun KRCommentActivity.isKr(): Boolean {
            return Objects.equals(getType(), "kr")
        }

        private fun registerCallback(context: Context, receiver: BroadcastReceiver) {
            val filter = IntentFilter(goal_action)
            LocalBroadcastManager.getInstance(context).registerReceiver(receiver, filter)
        }

        private fun KRCommentActivity.notifyReceivers() {
            val intent = Intent(goal_action)
            intent.putExtra("type", getType())
            intent.putExtra("id", getId())
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
        }
    }

    private var runnable: Runnable? = null
    private var keyBoardShowHeight = 0
    private var lastKeyBoardHeight: Int = 0

    private val handler = Handler()

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null)
                return
            when (intent.action) {
                JsTargetInterface.CLOSE_PAGE -> {
                    superFinish()
                }
                JsTargetInterface.JSSDK_EVENT_PAGE_FINISH -> {
                    mLottieAnimationView?.gone()
                    mLottieAnimationViewContainer?.gone()
                }
                JsTargetInterface.HANDLE_TITLE -> {
                    handler.clear()
                    val obj = intent.getSerializableExtra("data") as? HashMap<String, Boolean>
                    var r = true
                    if (obj != null) {
                        r = obj["show"] ?: true
                    }
                    if (r) {
                        mTitleContainer?.visible()
                    } else {
                        mTitleContainer?.gone()
                    }
                }
            }
        }
    }

    private var keyboardHeightProvider: KeyboardHeightProvider? = null
    private var mLottieAnimationView: LottieAnimationView? = null
    private var mLottieAnimationViewContainer: View? = null
    var mCenterTitleFragment: CenterTitleFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        parseDeeplink()
        setContentView(R.layout.joywork_kpi_target_detail)
        mCenterTitleFragment = CenterTitleFragment().left(R.string.icon_direction_left, R.id.back) {
            superFinish()
        }.into(this, R.id.mTitleContainer).post {
            val view = findViewById<TextView>(R.id.back)
            view.argbId(R.color.icon_black)
            view.setTextSizeRes(R.dimen.joywork_icon_big_size)
        }.title(R.string.joywork_comment)
        initWebView()
        val filter = IntentFilter()
        filter.addAction(JsTargetInterface.HANDLE_TITLE)
        filter.addAction(JsTargetInterface.CLOSE_PAGE)
        filter.addAction(JsTargetInterface.JSSDK_EVENT_PAGE_FINISH)
        LocalBroadcastManager.getInstance(this).registerReceiver(receiver, filter)
        keyboardHeightProvider = KeyboardHeightProvider(this)
        mWebView.post {
            keyboardHeightProvider?.start()
        }
        mLottieAnimationViewContainer = findViewById(R.id.mLoadingViewContainer)
    }

    private var mWebViewHandler: CompletionHandler<Any>? = null
    private var mJsSdkKit: JsSdkKit? = null

    private fun initWebView() {
        val url = if (isKr()) JoyWorkNetConfig.getKrCommentUrl(
            getId(),
            this,
            getCommentId()
        ) else JoyWorkNetConfig.getGoalCommentUrl(getId(), this, getCommentId())
        WebView.setWebContentsDebuggingEnabled(true)
        val jsSdkKit = mWebView.initJoyWork(url)
        mWebView.bindJsBrowser(jsSdkKit) {
            mWebViewHandler = it
        }
        mJsSdkKit = jsSdkKit
        mWebView.setOnLongClickListener {
            return@setOnLongClickListener false
        }
    }

    override fun onDestroy() {
        mWebView.loadUrl("about:blank")
        mWebView.destroy() // 销毁web view
        handler.clear()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(receiver)
        super.onDestroy()
    }

    override fun finish() {
//        super.finish()
        // 将事件交给网页处理，因为网页会弹出一些二级界面
        JsTargetInterface.sendToWeb(mWebView, JsTargetInterface.GO_BACK)

        // 如果网页加载出错，手机端进行一定补偿
        handler.post {
            superFinish()
        }
//        handler.postDelayed({
//            superFinish()
//        }, 0)
    }

    fun refresh() {
        JsTargetInterface.sendToWeb(mWebView, JsTargetInterface.NOTIFY_REFRESH_GOAL)
    }

    fun superFinish() {
        handler.clear()
        notifyReceivers()
        super.finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == JsIm.REQUEST_CODE_SELECT_CONTACT) { //web评论的at功能会回调这里
            runnable = Runnable {
                //这里防止键盘收起的时候onKeyboardHeightChanged退出评论状态
            }
            handler.postDelayed({
                mJsSdkKit?.onActivityResult(requestCode, resultCode, data)
            }, 0)
        }
    }

    override fun onResume() {
        keyboardHeightProvider?.setKeyboardHeightObserver(this)
        super.onResume()
    }

    override fun onPause() {
        keyboardHeightProvider?.setKeyboardHeightObserver(null)
        super.onPause()
    }

    override fun onKeyboardHeightChanged(height: Int, orientation: Int) {
        if (height == lastKeyBoardHeight) {
            return
        } else {
            lastKeyBoardHeight = height
        }
        if (height > keyBoardShowHeight) {
            keyBoardShowHeight = height
        }
        handler.post {
            if (height <= 0 && runnable == null) {
                JsTargetInterface.sendToWeb(mWebView, "NATIVE_EVENT_KEYBOARD_WILL_HIDE")
            }
            try {
                val jsonObject = JSONObject()
                val density = CommonUtils.getScreenDensity(this)
                jsonObject.put("height", height / density)
                mWebViewHandler?.setProgressData(jsonObject)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            runnable = null
        }
    }
}