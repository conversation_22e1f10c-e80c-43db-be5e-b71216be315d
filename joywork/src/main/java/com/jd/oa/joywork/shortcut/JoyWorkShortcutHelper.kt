package com.jd.oa.joywork.shortcut

import android.view.View
import android.widget.TextView
import com.jd.oa.joywork.view.BubbleViewCallback
import com.jd.oa.joywork.view.JoyWorkAvatarViewCallback
import com.jd.oa.utils.lt

/**
 * 文字个数小于 2 时显示成圆形
 */
object LT2BubbleViewCallback : BubbleViewCallback() {
    override fun shouldCircular(child: View): Boolean {
        return (child as? TextView) lt 2
    }
}
