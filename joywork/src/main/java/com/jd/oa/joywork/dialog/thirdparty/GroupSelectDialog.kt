package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitContentController
import com.jd.oa.ui.dialog.ShitDialogConfig
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible


class SelectGroupDialog(
    private val ctx: Context,
    groups: List<Group>,
    selectedId: String?,
    val callback: (Group) -> Unit
) : BaseShitDialog(ctx, ShitDialogConfig()) {
    private val listController = object : IShitContentController {
        override fun inflate(parent: FrameLayout): View {
            val inflater = ctx.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val content =
                inflater.inflate(R.layout.joywork_dialog_title_list_base_content, parent, false)
            parent.addView(content)
            return content
        }
    }

    override var contentController: IShitContentController
        get() = listController
        set(_) {}

    private val groupAdapter = GroupSelectAdapter(groups, selectedId){
        dismiss()
        callback.invoke(it)
    }

    override fun setupCustomContentView(content: View) {
        content.background = DrawableEx.roundSolidDirRect(
            Color.parseColor("#F4F5F6"),
            context.resources.getDimension(R.dimen.joywork_joywork_group_small),
            DrawableEx.DIR_TOP
        )
        content.findViewById<TextView>(R.id.title).setText(R.string.joywork_select_group_list_title)
        content.findViewById<TextView>(R.id.cancel).setOnClickListener { dismiss() }
        val recycler = content.findViewById<RecyclerView>(R.id.recycler)
        recycler.layoutManager = LinearLayoutManager(content.context)
        recycler.adapter = groupAdapter
    }


    private class GroupSelectAdapter(
        var dataList: List<Group>,
        val selectedId: String?,
        val callback: (Group) -> Unit
    ) : RecyclerView.Adapter<GroupViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GroupViewHolder {
            return GroupViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.joywork_filter_dialog_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: GroupViewHolder, position: Int) {
            val group = dataList[position]
            if (group.groupId == selectedId) holder.icon.visible() else holder.icon.gone()
//            holder.title.typeface =
//                Typeface.defaultFromStyle(if (group.groupId == selectedId) Typeface.BOLD else Typeface.NORMAL)
            holder.title.text = group.safeTitle(holder.title.context)
            holder.divider.gone()
            holder.itemView.setOnClickListener {
                callback.invoke(group)
            }
        }

        override fun getItemCount(): Int {
            return dataList.size
        }

    }

    class GroupViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var title: TextView = view.findViewById(R.id.jdme_id_myapply_dropdown_item)
        var icon: View = view.findViewById(R.id.jdme_id_myapply_dropdown_icon)
        var divider: View = view.findViewById(R.id.divider)
    }

}






