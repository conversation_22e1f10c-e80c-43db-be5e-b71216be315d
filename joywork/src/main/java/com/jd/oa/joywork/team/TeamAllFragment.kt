package com.jd.oa.joywork.team

import com.jd.oa.joywork.TaskStatusEnum

class TeamAllFragment : TeamBaseFragment(), IInsertJoyWork {
    private var finishRunnable: Runnable? = null
    override fun getDefaultStatus(): TaskStatusEnum {
        return TaskStatusEnum.ALL
    }

    override fun getPageStatus(): TaskStatusEnum {
        return TaskStatusEnum.ALL
    }

    override fun onNetworkFinish(success: <PERSON><PERSON><PERSON>) {
        finishRunnable?.run()
    }

    override fun getListenerAction(): ArrayList<String> {
        val ret = super.getListenerAction()
//        ret.add(ProjectConstant.UNFINISH_ACTION)
//        ret.add(ProjectConstant.FINISH_ACTION)
        return ret
    }
}