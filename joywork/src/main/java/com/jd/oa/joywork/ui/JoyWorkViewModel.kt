package com.jd.oa.joywork.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.EntranceType
import com.jd.oa.joywork.bean.JoyWorkNum

class JoyWorkViewModel : ViewModel() {
    // 更新数量
    private val _countFlow: MutableLiveData<JoyWorkNum> = MutableLiveData()

    val countFlow: LiveData<JoyWorkNum> = _countFlow

    fun post(num: JoyWorkNum) {
        _countFlow.postValue(num)
    }

    // 通知数量是否更新过。
    var mNumUpdated = false

    // 通知中心数量
    private val _notificationNumLiveData: MutableLiveData<Int> = MutableLiveData()
    val notificationNumLiveData: LiveData<Int> = _notificationNumLiveData
    fun postNotificationNum(num: Int) {
        _notificationNumLiveData.value = num
        mNumUpdated = true
    }

    // 通知中心数量
    private val _notificationListRefreshLiveData: MutableLiveData<Boolean> = MutableLiveData()
    val notificationListRefreshLiveData: LiveData<Boolean> = _notificationListRefreshLiveData
    fun postNotificationRefreshList(value: Boolean) {
        _notificationListRefreshLiveData.value = value
    }

    // 通知刷新通知中心数量
    private val _updateNotificationLiveData: MutableLiveData<Boolean> = MutableLiveData()
    val updateNotificationLiveData: LiveData<Boolean> = _updateNotificationLiveData
    fun postUpdateNotification(onlyUpdateNum: Boolean) {
        _updateNotificationLiveData.value = onlyUpdateNum
    }

    // 更新显示界面
    private val _uiFlow: MutableLiveData<EntranceType> = MutableLiveData()

    val uiFlow: LiveData<EntranceType> = _uiFlow

    fun setUI(num: EntranceType) {
        _uiFlow.value = num
    }

    // 是否需要刷新界面
    private val _numRefreshFlow: MutableLiveData<Boolean> = MutableLiveData()

    val numRefreshFlow: LiveData<Boolean> = _numRefreshFlow

    fun refreshNum() {
        _numRefreshFlow.value = true
    }

    //
    private val _toastLiveData: MutableLiveData<String> = MutableLiveData()
    val toastLiveData: LiveData<String> = _toastLiveData
    fun showToast(msg: String) {
        // 修改为 post，防止子线程调用
        _toastLiveData.postValue(msg)
    }
}