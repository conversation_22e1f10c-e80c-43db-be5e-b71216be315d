package com.jd.oa.joywork.detail

import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.joywork.isLegalList

/**
 * 不弹窗
 */
const val type_none = 1

const val finish_base = 100

const val restore_base = 200

/**
 * 完成，不需要提示。图 1
 */
const val type_finish_none = finish_base + 1

/**
 * 完成弹窗，中间带输入框，但下边没有完成提示。图 7
 */
const val type_finish_task_edit_no = finish_base + 2

/**
 * 完成全部。中间带文字提示 图 3
 */
const val type_finish_all_alert = finish_base + 3

/**
 * 完成弹窗，中间带输入框，下边有完成提示。图 8
 */
const val type_finish_all_edit = finish_base + 4

/**
 * 完全待办，只有文字提示 图 4
 */
const val type_finish_task_alert = finish_base + 5

/**
 * 恢复，不需要提示。图 2
 */
const val type_restore_none = restore_base + 1

/**
 * 恢复全部，中间带文字提示，图 6
 */
const val type_restore_all_alert = restore_base + 2

/**
 * 恢复待办，中间带文字提示，图 5
 */
const val type_restore_task_alert = restore_base + 3

/**
 * 是否有仅有完成功能
 */
fun JoyWorkDetail?.hasFinishOnlyMe(): Boolean {
    return hasForceComplete() && selfIsOwner() && ((this?.owners?.size ?: 0) > 1)
}

/**
 * 判断 getDialogType() 返回的结果是不是表示完成
 */
fun Int.isRestore(): Boolean {
    return this >= restore_base
}

fun JoyWorkDetail?.isFinishAll(): Boolean {
    /**
     * 有创建人权限，且有多个执行人时
     */
    return hasForceComplete() && (this?.owners?.size ?: 0 > 1)
}

/**
 * 返回完成弹窗。包括完成、恢复
 * 没有相应权限时，返回 type_none
 * 关于各 dialog 的弹窗见链接 https://apijoyspace.jd.com/v1/files/iexo1wNo2d7VLvSQYLCK/link
 */
fun JoyWorkDetail?.getDialogType(): Int {
    if (hasForceComplete()) {
        // 有创建人权限
        if (selfIsOwner()) {
            // 是执行人
            if (onlySelfIsOwner()) {
                // 只有自己是执行人
                return if (selfIsCreator()) {
                    // 自己是真正创建人
                    if (isFinish()) {
                        type_restore_none
                    } else {
                        type_finish_none
                    }
                } else {
                    // 通过其它途径得到的创建人权限
                    if (isFinish()) {
                        type_restore_none
                    } else {
                        type_finish_task_edit_no
                    }
                }
            } else {
                // 有其余执行人
                return if (selfIsCreator()) {
                    // 真正的创建人
                    if (isFinish()) {
                        type_restore_all_alert
                    } else {
                        type_finish_all_alert
                    }
                } else {
                    if (isFinish()) {
                        type_restore_all_alert
                    } else {
                        type_finish_all_edit
                    }
                }
            }
        } else { // 不是执行人
            val os = this?.owners ?: ArrayList()
            if (!os.isLegalList()) {
                // 没有执行人
                return if (selfIsCreator()) {
                    // 真正创建人
                    if (isFinish()) {
                        type_restore_none
                    } else {
                        type_finish_none
                    }
                } else {
                    if (isFinish()) {
                        type_restore_none
                    } else {
                        type_finish_task_edit_no
                    }
                }
            } else if (os.size == 1) {
                // 只有一个执行人
                return if (selfIsCreator()) {
                    // 真正创建人
                    if (isFinish()) {
                        type_restore_task_alert
                    } else {
                        type_finish_task_alert
                    }
                } else {
                    if (isFinish()) {
                        type_restore_task_alert
                    } else {
                        type_finish_task_edit_no
                    }
                }
            } else {
                // 多个执行人
                return if (selfIsCreator()) {
                    // 真正创建人
                    if (isFinish()) {
                        type_restore_all_alert
                    } else {
                        type_finish_all_alert
                    }
                } else {
                    if (isFinish()) {
                        type_restore_all_alert
                    } else {
                        type_finish_all_edit
                    }
                }
            }
        }
    } else {
        // 没有创建人权限
        return if (selfIsOwner()) {
            // 是执行人，此时只完成自己
            // 所以判断是否完成时，以执行人自己的状态判断
            if (selfIsOwnerFinish()) {
                type_restore_none
            } else {
                type_finish_task_edit_no
            }
        } else {
            type_none
        }
    }
}