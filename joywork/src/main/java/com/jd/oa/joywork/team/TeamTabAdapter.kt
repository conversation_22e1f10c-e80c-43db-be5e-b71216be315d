package com.jd.oa.joywork.team

import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.utils.*

internal class TeamTabAdapter(
    private val ts: List<String>,
    private val context: Context,
    private val defaultIndex: Int = 0,
    private val selectCallback: (Int) -> Unit
) : RecyclerView.Adapter<TeamTabAdapter.VH>() {
    private var selectedHolder: VH? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(context.inflater.inflate(R.layout.jdme_joywork_fragment_mine_tab, parent, false))
    }

    override fun getItemCount() = ts.size

    override fun onBindViewHolder(holder: VH, position: Int) {
        if (ts[position] == "-") {
            holder.title.gone()
            holder.divider.visible()
        } else {
            holder.title.visible()
            holder.divider.gone()

            holder.title.text = ts[position]
            if (position == itemCount - 1) {
                holder.space.gone()
            } else {
                holder.space.visible()
            }
            // selectedHolder == null 表示第一次加载
            if (defaultIndex == position && selectedHolder == null) {
                selectedHolder = holder
                selectedHolder?.select()
            } else {
                holder.unselect()
            }
            holder.title.setOnClickListener {
                selectedHolder?.unselect()
                selectedHolder = holder
                selectedHolder?.select()
            }

            holder.title.setTag(R.id.jdme_tag_id, position)
            holder.title.setOnClickListener {
                if (it != selectedHolder?.itemView) {
                    selectedHolder?.unselect()
                    holder.select()
                    selectedHolder = holder
                    val pos = it.getTag(R.id.jdme_tag_id) as Int
                    selectCallback.invoke(pos)
                }
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    inner class VH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val title = itemView.findViewById<TextView>(R.id.title)
        val space = itemView.findViewById<View>(R.id.space_view)
        val divider = itemView.findViewById<View>(R.id.divider)

        fun select() {
            title.bold()
            title.setTextColor(Color.parseColor("#FFFE3B30"))
            title.background = DrawableEx.roundSolidRect(
                Color.parseColor("#14FE3E33"),
                itemView.resources.getDimension(R.dimen.joywork_corner_small_half)
            )
//            title.background = DrawableEx.roundSolidStrokeRect(Color.parseColor("#0AFE3B30"), itemView.resources.getDimension(R.dimen.joywork_corner_small), Color.parseColor("#FFFE3B30"), CommonUtils.dp2px(0.5f))
        }

        fun unselect() {
            title.normal()
            title.setTextColor(Color.parseColor("#FF232930"))
            title.background = DrawableEx.roundSolidRect(
                Color.parseColor("#FFF5F5F5"),
                itemView.resources.getDimension(R.dimen.joywork_corner_small_half)
            )
        }
    }
}