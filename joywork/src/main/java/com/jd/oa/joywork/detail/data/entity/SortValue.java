package com.jd.oa.joywork.detail.data.entity;

import android.content.Context;
import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.jd.oa.joywork.ProjectSortCustom;
import com.jd.oa.joywork.ProjectSortCustomGroup;
import com.jd.oa.joywork.SortAction;
import com.jd.oa.joywork.team.bean.ProjectSortEnum;

import java.util.Objects;

/**
 * 用于传递参数给后台。也用于列表顶部显示
 */
public class SortValue implements Parcelable {

    //    @ApiModelProperty(value = "团队待办排序类型: 1截止时间，2完成时间，3指派对象，4自定义字段")
    // 值是 ProjectSortEnum 中的 code
    public Integer code;

    //    @ApiModelProperty(value = "筛选内容，根据不同的type传不同的内容。type = 1，2，3不需要传，" +
//            "type = 4 {\"columnId\": \"xxxxxx\"}")
    private SortObj value;

    /**
     * 用于显示的文字
     */
    @Expose(serialize = false, deserialize = false)
    public String title;

    private SortValue(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    private SortValue(Integer code, SortObj value, String title) {
        this.code = code;
        this.value = value;
        this.title = title;
    }

    public static SortValue getSortInstance(int code, SortObj object, String title) {
        return new SortValue(code, object, title);
    }

    public static SortValue getDefaultSort(Context context) {
        return getSortInstance(ProjectSortEnum.SORT_NULL.getCode(), null, new ProjectSortCustomGroup(context).getAction());
    }

    public boolean isSameAction(SortAction action) {
        if (code != action.javaEnum().getCode()) {
            return false;
        }
        if (code == ProjectSortEnum.SORT_CUSTOM.getCode() && (action instanceof ProjectSortCustom)) {
            // 自定义字段，需要额外比对
            if (value == null) {
                return false;
            }
            return Objects.equals(value.columnId, ((ProjectSortCustom) action).getValue());
        }
        return true;
    }

    protected SortValue(Parcel in) {
        if (in.readByte() == 0) {
            code = null;
        } else {
            code = in.readInt();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (code == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(code);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SortValue sortValue = (SortValue) o;
        return Objects.equals(code, sortValue.code) &&
                Objects.equals(value, sortValue.value) &&
                Objects.equals(title, sortValue.title);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, value);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SortValue> CREATOR = new Creator<SortValue>() {
        @Override
        public SortValue createFromParcel(Parcel in) {
            return new SortValue(in);
        }

        @Override
        public SortValue[] newArray(int size) {
            return new SortValue[size];
        }
    };

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public SortObj getValue() {
        return value;
    }

    public void setValue(SortObj value) {
        this.value = value;
    }

    public static class SortObj implements Parcelable {
        public String columnId;

        public SortObj() {

        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SortObj sortObj = (SortObj) o;
            return Objects.equals(columnId, sortObj.columnId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(columnId);
        }

        protected SortObj(Parcel in) {
            columnId = in.readString();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(columnId);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<SortObj> CREATOR = new Creator<SortObj>() {
            @Override
            public SortObj createFromParcel(Parcel in) {
                return new SortObj(in);
            }

            @Override
            public SortObj[] newArray(int size) {
                return new SortObj[size];
            }
        };
    }
}
