package com.jd.oa.joywork.detail.data.entity;

import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork.bean.JoyWork;

import java.util.ArrayList;
import java.util.List;

/**
 * Auto-generated: 2021-07-02 15:51:35
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class ChildWorks {

    private String sourceId;
    private Long gmtModified;
    private String parentTaskId;
    private String bizCode;
    private String fromMQ;
    private String remark;
    private String title;
    private String content;
    private String remindTime;
    private String mobileContent;
    private String startTime;
    private Boolean enablePermission;
    private Integer taskStatus;
    // 多人待办负责人
    public List<Owner> owners;
    private List<ChildWorks> childWorks;
    private Creator creator;
    private ParentTask parentTask;
    private String blockType;
    private String completeTime;
    private List<String> permission;
    private Integer sort;
    private Long gmtCreate;
    private Integer priorityType;
    private String extend;
    private String thirdProcess;
    private String planTime;
    private List<Executors> executors;
    private Long endTime;
    private String sourceName;
    private String userRole;
    private String projectId;
    private String taskId;
    private Integer status;
    public Boolean merged;
    // 多人待办时，子待办展示消息
    public Integer uiTaskStatus;
    public Integer finishAction;


    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setGmtModified(long gmtModified) {
        this.gmtModified = gmtModified;
    }

    public long getGmtModified() {
        return gmtModified;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public String getParentTaskId() {
        return parentTaskId;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setFromMQ(String fromMQ) {
        this.fromMQ = fromMQ;
    }

    public String getFromMQ() {
        return fromMQ;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setRemindTime(String remindTime) {
        this.remindTime = remindTime;
    }

    public String getRemindTime() {
        return remindTime;
    }

    public void setMobileContent(String mobileContent) {
        this.mobileContent = mobileContent;
    }

    public String getMobileContent() {
        return mobileContent;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setEnablePermission(Boolean enablePermission) {
        this.enablePermission = enablePermission;
    }

    public Boolean getEnablePermission() {
        return enablePermission;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setCreator(Creator creator) {
        this.creator = creator;
    }

    public Creator getCreator() {
        return creator;
    }

    public void setParentTask(ParentTask parentTask) {
        this.parentTask = parentTask;
    }

    public ParentTask getParentTask() {
        return parentTask;
    }

    public void setBlockType(String blockType) {
        this.blockType = blockType;
    }

    public String getBlockType() {
        return blockType;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    public String getCompleteTime() {
        return completeTime;
    }

    public void setPermission(List<String> permission) {
        this.permission = permission;
    }

    public List<String> getPermission() {
        return permission;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSort() {
        return sort;
    }

    public void setGmtCreate(Long gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Long getGmtCreate() {
        return gmtCreate;
    }

    public void setPriorityType(Integer priorityType) {
        this.priorityType = priorityType;
    }

    public Integer getPriorityType() {
        return priorityType;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public String getExtend() {
        return extend;
    }

    public void setThirdProcess(String thirdProcess) {
        this.thirdProcess = thirdProcess;
    }

    public String getThirdProcess() {
        return thirdProcess;
    }

    public void setPlanTime(String planTime) {
        this.planTime = planTime;
    }

    public String getPlanTime() {
        return planTime;
    }

    public void setExecutors(List<Executors> executors) {
        this.executors = executors;
    }

    public List<Executors> getExecutors() {
        return executors;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public JoyWork toJoyWork() {
        JoyWork joyWork = new JoyWork();
        joyWork.taskId = taskId;
        joyWork.status = status;
        joyWork.taskStatus = taskStatus;
        joyWork.enablePermission = enablePermission;
        joyWork.permission = permission;
        if (childWorks != null) {
            joyWork.childWorks = new ArrayList<JoyWork>();
            for (int i = 0; i < childWorks.size(); i++) {
                joyWork.childWorks.add(childWorks.get(i).toJoyWork());
            }
        }
        return joyWork;
    }

    /**
     * 由【未完成/完成】状态切换成【完成/未完成】状态。其余状态时不处理
     */
    public void toggleFinishStatus() {
        if (taskStatus == TaskStatusEnum.FINISH.getCode()) {
            taskStatus = TaskStatusEnum.UN_FINISH.getCode();
            uiTaskStatus = TaskStatusEnum.UN_FINISH.getCode();
        } else if (taskStatus == TaskStatusEnum.UN_FINISH.getCode()) {
            taskStatus = TaskStatusEnum.FINISH.getCode();
            uiTaskStatus = TaskStatusEnum.FINISH.getCode();
        }
    }

    @Override
    public String toString() {
        return "ChildWorks{" +
                "sourceId='" + sourceId + '\'' +
                ", gmtModified=" + gmtModified +
                ", parentTaskId='" + parentTaskId + '\'' +
                ", bizCode='" + bizCode + '\'' +
                ", fromMQ='" + fromMQ + '\'' +
                ", remark='" + remark + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", remindTime='" + remindTime + '\'' +
                ", mobileContent='" + mobileContent + '\'' +
                ", startTime='" + startTime + '\'' +
                ", enablePermission=" + enablePermission +
                ", taskStatus=" + taskStatus +
                ", childWorks=" + childWorks +
                ", creator=" + creator +
                ", parentTask=" + parentTask +
                ", blockType='" + blockType + '\'' +
                ", completeTime='" + completeTime + '\'' +
                ", permission=" + permission +
                ", sort=" + sort +
                ", gmtCreate=" + gmtCreate +
                ", priorityType=" + priorityType +
                ", extend='" + extend + '\'' +
                ", thirdProcess='" + thirdProcess + '\'' +
                ", planTime='" + planTime + '\'' +
                ", executors=" + executors +
                ", endTime=" + endTime +
                ", sourceName='" + sourceName + '\'' +
                ", userRole='" + userRole + '\'' +
                ", projectId='" + projectId + '\'' +
                ", taskId='" + taskId + '\'' +
                ", status=" + status +
                '}';
    }
}