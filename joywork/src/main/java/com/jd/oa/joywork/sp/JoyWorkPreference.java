package com.jd.oa.joywork.sp;

import android.content.Context;

import com.jd.oa.joywork.team.kpi.JoyWorkSpace;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class JoyWorkPreference extends AbsKvEntities {

    private Context mContext;

    public JoyWorkPreference(Context context) {
        this.mContext = context;
    }

    @Override
    public String getPrefrenceName() {
        return "joywork";
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.USER;
    }

    @Override
    public Context getContext() {
        return mContext;
    }

    // 我的待办列表缓存
    public static KvEntity<String> KV_ENTITY_PERSON_LIST = new KvEntity<>("work.task.findMyTasks.v4", "");

    // 风险问题列表中是否显示过提示
    public static KvEntity<Boolean> KV_ENTITY_ISSUE_TIPS = new KvEntity<>("work.task.issue.tips.v1", false);
    // 生成周报时所选文件 id
    public static KvEntity<String> KV_ENTITY_WEEKLY_SUMMARY_FOLDER_ID = new KvEntity<>("joywork_weekly_summary_folder_id", JoyWorkSpace.PRIVATE_DIR_ID);
    // 生成周报时所选文件 name
    public static KvEntity<String> KV_ENTITY_WEEKLY_SUMMARY_FOLDER_NAME = new KvEntity<>("joywork_weekly_summary_folder_name", "");
    // 生成周报时所选文件 team
    public static KvEntity<String> KV_ENTITY_WEEKLY_SUMMARY_FOLDER_TEAM = new KvEntity<>("joywork_weekly_summary_folder_team", JoyWorkSpace.PRIVATE_DIR_ID);
    // 记录是否选择过文件
    public static KvEntity<Boolean> KV_ENTITY_WEEKLY_SUMMARY_SELECTED = new KvEntity<>("joywork_weekly_summary_folder_selected", false);

    // 记录是否选择过文件
    public static KvEntity<String> KV_ENTITY_WORK_BENCH_TAB = new KvEntity<>("joywork_work_bench_tab", "");
    //日历状态
    public static KvEntity<Boolean> KV_ENTITY_CALENDAR_VIEW_WEEK = new KvEntity<>("joywork_work_bench_tab", false);
    //「待我处理」子项 List 缓存
    public static KvEntity<String> KV_ENTITY_JDME_MY_TASK_HANDLE_List = new KvEntity<>("key_my_task_handle_list", "");
}
