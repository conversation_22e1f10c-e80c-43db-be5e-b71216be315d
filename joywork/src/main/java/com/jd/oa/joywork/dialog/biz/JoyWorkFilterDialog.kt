package com.jd.oa.joywork.dialog.biz

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Typeface
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.AppBase
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.listener.SimpleAnimatorListener
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener
import com.jd.oa.utils.*
import com.jd.oa.joywork.R


object TaskSectionItemFactory {
    private lateinit var itemAll: JoyWorkFilterDialog.Item
    private lateinit var itemUnFinish: JoyWorkFilterDialog.Item
    private lateinit var itemFinish: JoyWorkFilterDialog.Item
    private lateinit var itemDelete: JoyWorkFilterDialog.Item
    private lateinit var itemRisk: JoyWorkFilterDialog.Item

    fun getAllItem(): JoyWorkFilterDialog.Item {
        if (!TaskSectionItemFactory::itemAll.isInitialized) {
            itemAll = JoyWorkFilterDialog.Item(R.string.joywork_screen_all, TaskStatusEnum.ALL)
        }
        return itemAll
    }

    fun getRiskItem(): JoyWorkFilterDialog.Item {
        if (!TaskSectionItemFactory::itemRisk.isInitialized) {
            itemRisk =
                JoyWorkFilterDialog.Item(R.string.joywork_tabbar_title_issue, TaskStatusEnum.RISK)
        }
        return itemRisk
    }

    fun getUnFinishItem(): JoyWorkFilterDialog.Item {
        if (!TaskSectionItemFactory::itemUnFinish.isInitialized) {
            itemUnFinish =
                JoyWorkFilterDialog.Item(R.string.joywork_screen_unfinish, TaskStatusEnum.UN_FINISH)
        }
        return itemUnFinish
    }

    fun getFinishItem(): JoyWorkFilterDialog.Item {
        if (!TaskSectionItemFactory::itemFinish.isInitialized) {
            itemFinish =
                JoyWorkFilterDialog.Item(R.string.joywork_screen_finish, TaskStatusEnum.FINISH)
        }
        return itemFinish
    }

    fun getDeleteItem(): JoyWorkFilterDialog.Item {
        if (!TaskSectionItemFactory::itemDelete.isInitialized) {
            itemDelete =
                JoyWorkFilterDialog.Item(R.string.joywork_screen_deleted, TaskStatusEnum.DELETED)
        }
        return itemDelete
    }

    fun getItemByStatus(status: TaskStatusEnum): JoyWorkFilterDialog.Item {
        return when (status) {
            TaskStatusEnum.UN_FINISH -> getUnFinishItem()
            TaskStatusEnum.FINISH -> getFinishItem()
            TaskStatusEnum.DELETED -> getDeleteItem()
            else -> getAllItem()
        }
    }
}

class JoyWorkFilterDialogBuilder {
    var selectedItem: JoyWorkFilterDialog.Item? = null
    var showAll: Boolean = true
    var showDel: Boolean = true
    var onItemSelectCallback: JoyWorkFilterDialog.OnItemSelectCallback? = null
    var clickIds: Map<TaskStatusEnum, String>? = null
    var topMargin: Int = 0
    var showCallback: (() -> Unit)? = null

    companion object {
        fun build(
            anchor: View,
            context: Context,
            config: JoyWorkFilterDialogBuilder.() -> Unit
        ): JoyWorkFilterDialog {
            val builder = JoyWorkFilterDialogBuilder()
            builder.config()
            val decorView = anchor.rootView as FrameLayout
            return JoyWorkFilterDialog(decorView, context, builder)
        }
    }
}

/**
 * 筛选弹窗
 * Created by gzf on 2021/5/13
 */
class JoyWorkFilterDialog(
    private val decorView: FrameLayout,
    private val context: Context,
    val builder: JoyWorkFilterDialogBuilder
) {
    private lateinit var recyclerView: RecyclerView
    private lateinit var bottomView: View
    private lateinit var adapter: MyAdapter
    private var callback: OnItemSelectCallback? = builder.onItemSelectCallback
    private var temp: Item? = builder.selectedItem

    private val anmDuration = 100L

    private val itemAll = TaskSectionItemFactory.getAllItem()
    private val itemUnFinish = TaskSectionItemFactory.getUnFinishItem()
    private val itemFinish = TaskSectionItemFactory.getFinishItem()
    private val itemDelete = TaskSectionItemFactory.getDeleteItem()

    private val contentView =
        context.inflater.inflate(R.layout.joywork_filter_dialog, decorView, false)

    var dismissCallback: (() -> Unit)? = null
    private var rvHeight = -1

    init {
        decorView.addView(contentView)
        initView(contentView)
    }

    private fun initView(view: View) {
        val results = mutableListOf<Item>()
        if (builder.showAll) {
            results.add(itemAll)
        }
        results.add(itemUnFinish)
        results.add(itemFinish)
        if (builder.showDel) {
            results.add(itemDelete)
        }
        adapter = MyAdapter(results)
        view.apply {
            recyclerView = findViewById(R.id.recyclerView)
        }
        recyclerView.layoutManager = LinearLayoutManager(context)
        // 先隐藏
        recyclerView.layoutParams.height = 0
        recyclerView.post {
            val anm = ValueAnimator.ofFloat(0.0f, rvHeight.toFloat())
            anm.addListener(object : SimpleAnimatorListener() {
                override fun onAnimationStart(animation: Animator) {
                    recyclerView.visible()
                }
            })
            anm.duration = anmDuration
            anm.addUpdateListener {
                Log.e("TAG", "value = ${it.animatedValue}")
                recyclerView.layoutParams.height = (it.animatedValue as Float).toInt()
                recyclerView.requestLayout()
            }
            anm.start()
        }

        adapter.setOnItemClickListener(object : RecyclerViewItemOnClickListener<Item> {
            override fun onItemClick(view: View?, position: Int, item: Item) {
                builder.clickIds?.get(item.taskStatus)?.apply {
                    JDMAUtils.onEventClick(this, this)
                }
                callback?.onSelectAction(item)
                dismiss()
            }

            override fun onItemLongClick(view: View?, position: Int, item: Item?) {

            }
        })
        recyclerView.adapter = adapter
        recyclerView.measure(0, 0)
        rvHeight = recyclerView.measuredHeight
        val topView = view.findViewById<View>(R.id.top)
        topView.visible()
        topView.isEnabled = false
        topView.layoutParams?.height = builder.topMargin

        bottomView = view.findViewById<View>(R.id.bottom)
        bottomView.visible()
        bottomView.setOnClickListener {
            dismiss()
        }
        builder.showCallback?.invoke()
    }

    fun dismiss() {
        val anm = ValueAnimator.ofFloat(1.0f, 0.0f)
        anm.addListener(object : SimpleAnimatorListener() {
            override fun onAnimationEnd(animation: Animator) {
                decorView.removeView(contentView)
                dismissCallback?.invoke()
            }
        })
        anm.duration = anmDuration
        anm.addUpdateListener {
            Log.e("TAG", "value = ${it.animatedValue}")
            recyclerView.layoutParams.height = ((it.animatedValue as Float) * rvHeight).toInt()
            bottomView.alpha = it.animatedValue as Float
            recyclerView.requestLayout()
        }
        anm.start()
    }

    inner class MyAdapter(val dataList: List<Item>) :
        BaseRecyclerViewAdapter<Item>(AppBase.getTopActivity(), dataList) {
        override fun getItemLayoutId(viewType: Int): Int = R.layout.joywork_filter_dialog_item

        override fun onConvert(holder: BaseRecyclerViewHolder?, item: Item, position: Int) {
            holder?.apply {
                setVisible(
                    R.id.jdme_id_myapply_dropdown_icon,
                    if (isSelected(position, item)) View.VISIBLE else View.INVISIBLE
                )
                val text = getView<TextView>(R.id.jdme_id_myapply_dropdown_item)
                text.typeface = Typeface.defaultFromStyle(
                    if (isSelected(
                            position,
                            item
                        )
                    ) Typeface.BOLD else Typeface.NORMAL
                )
                text.text = context.getString(item.labelId)
            }
        }

        private fun isSelected(position: Int, item: Item): Boolean {
            return if (temp != null) item.taskStatus == temp?.taskStatus else position == 0
        }
    }

    class Item(val labelId: Int, val taskStatus: TaskStatusEnum) {
        override fun equals(other: Any?): Boolean {
            return this.taskStatus.code == (other as? Item)?.taskStatus?.code
        }
    }

    interface OnItemSelectCallback {
        fun onSelectAction(item: Item)
    }
}