package com.jd.oa.joywork.filter

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.ViewModelProviders
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.R.string
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.data.entity.custom.ProjectFilterExt
import com.jd.oa.joywork.detail.data.entity.custom.ProjectViewOuter
import com.jd.oa.joywork.dialog.thirdparty.ShitAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.ShitAlertDialogConfig
import com.jd.oa.joywork.team.*
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.joywork.team.bean.ProjectSortEnum
import com.jd.oa.joywork.team.bean.ProjectSortTypeEnum
import com.jd.oa.utils.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.*
import kotlin.collections.HashMap
import kotlin.coroutines.Continuation

interface JoyWorkFilterSortFragmentItf {
    fun handleSortActions(list: MutableList<ProjectSortAction>)
    fun getPageId(): String
    fun getViewType(): Int
    fun getProjectId(): String

    /**
     * 筛选是否可用
     */
    fun isFilterSortUnable(): Boolean

    fun getDefaultUIState(): UIStatus
}

class JoyWorkFilterSortFragment : BaseFragment() {

    val mUnableFilterSortStatus = mutableListOf<TaskStatusEnum>()

    private var mModel: TeamDetailViewModel? = null

    private var mHadSaved: View? = null // 已保存
    private var mSaveSettings: View? = null // 保存设置
    private var mClear: View? = null

    private var mStatusContainer: View? = null
    private var mStatusText: TextView? = null

    private var mFilterText: TextView? = null
    private var mFilterIcon: TextView? = null
    private var mFilterIcon2: TextView? = null

    private var mSortText: TextView? = null
    private var mSortIcon: TextView? = null
    private var mSortIcon2: TextView? = null
    private val mScope = MainScope()

    private val mStatusObserver = observe@{ it: TaskStatusEnum ->
        if (!isAdded || context == null) {
            return@observe
        }
        mStatusText?.setText(it.getStringId())
        val model = mModel ?: return@observe
        if (mUnableFilterSortStatus.contains(it)) {
            unableFilterSort()
            return@observe
        }
        val isFilterSortUnable = model.itfLiveData.value?.isFilterSortUnable()
        if (isFilterSortUnable.isTrue()) {
            unableFilterSort()
            return@observe
        }
        handleFilterSortView(
            mFilterText,
            mFilterIcon,
            mFilterIcon2,
            mFilterText?.string() ?: "",
            model.mUIStatus.mFilter.enum != ProjectFilterEnum.SCREEN_NULL
        )
        handleFilterSortView(
            mSortText,
            mSortIcon,
            mSortIcon2,
            mSortText?.string() ?: "",
            it.code != ProjectSortEnum.SORT_NULL.code
        )
    }

    private val mSortObserver = observe@{ it: SortValue ->
        if (!isAdded || context == null) {
            return@observe
        }
        val model = mModel ?: return@observe
        val txt = if (it.code == ProjectSortEnum.SORT_NULL.code) {
            getString(R.string.joywork_project_setting_sort)
        } else if (it.code == ProjectSortEnum.SORT_CUSTOM.code) {
            model.extLiveData.value?.customFields?.firstOrNull { g ->
                Objects.equals(g.columnId, it.value.columnId)
            }?.title ?: getString(R.string.joywork_project_setting_sort)
        } else {
            it.title ?: ""
        }
        handleFilterSortView(
            mSortText,
            mSortIcon,
            mSortIcon2,
            txt,
            it.code != ProjectSortEnum.SORT_NULL.code
        )
    }

    private val mUserObserver = observe@{ it: JoyWorkUser? ->
        val itf = mModel?.itfLiveData?.value
        if (itf != null) {
            mModel?.updateEditModel(mEditModelBackups[itf.getPageId()] ?: false)
        }
        handleSaveView()
    }

    private val mFilterObserver = observe@{ it: FilterValue ->
        if (!isAdded || context == null) {
            return@observe
        }
        val model = mModel ?: return@observe
        val filterValue = model.mUIStatus.mFilter
        val enum = filterValue.enum ?: ProjectFilterEnum.SCREEN_NULL
        val txt = FilterValueEx.getUIString(filterValue, requireContext(), newLine = false, model)
        handleFilterSortView(
            mFilterText,
            mFilterIcon,
            mFilterIcon2,
            txt,
            enum != ProjectFilterEnum.SCREEN_NULL
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.joywork_filter_sort, container, false)
    }

    private val mUIStatusBackups = HashMap<String, UIStatus>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {

        mStatusContainer = view.bindClick(R.id.mStatusContainer) {
            val model = mModel ?: return@bindClick
            val data = mutableListOf<TaskStatusEnum>()
            data.add(TaskStatusEnum.RISK)
            data.add(TaskStatusEnum.UN_FINISH)
            data.add(TaskStatusEnum.FINISH)
            showStatusDialog(
                requireContext(),
                model.mUIStatus.mStatus,
                data,
            ) {
                val itf = model.itfLiveData.value ?: return@showStatusDialog
                val id = itf.getPageId()
                if (id.isLegalString()) {
                    val status = mUIStatusBackups[id] ?: UIStatus()
                    if (it == TaskStatusEnum.FINISH) {
                        // switch to FINISH status, save the values of filter and sorter
                        status.mFilter = model.mUIStatus.mFilter
                        status.mSort = model.mUIStatus.mSort
                    } else if (status.isInited() && model.mUIStatus.mStatus.code == TaskStatusEnum.FINISH.code) {
                        // Switch from FINISH state to another state
                        // Restoring the values of filter and sorter
                        model.mUIStatus.mSort = status.mSort
                        model.mUIStatus.mFilter = status.mFilter
                        mSortObserver.invoke(model.mUIStatus.mSort)
                        mFilterObserver.invoke(model.mUIStatus.mFilter)
                    } else if (model.mUIStatus.mStatus.code == TaskStatusEnum.FINISH.code) {
                        // Switch from FINISH state to another state
                        // But had no values of filter and sorter
                        val defaultState = itf.getDefaultUIState()
                        model.mUIStatus.mSort = defaultState.mSort
                        model.mUIStatus.mFilter = defaultState.mFilter
                        mSortObserver.invoke(model.mUIStatus.mSort)
                        mFilterObserver.invoke(model.mUIStatus.mFilter)
                    }
                    mUIStatusBackups[id] = status
                }
                val isFilterSortUnable = model.itfLiveData.value?.isFilterSortUnable()

                if (mUnableFilterSortStatus.contains(it) && isFilterSortUnable.isTrue()) {
                    unableFilterSort()
                }
                model.updateStatus(it)
            }
        }
        mStatusText = view.findViewById(R.id.mStatusText)

        view.bindClick<View>(R.id.mFilterContainer) {
            val status = mModel?.mUIStatus?.mStatus ?: return@bindClick
            val isFilterSortUnable = mModel?.itfLiveData?.value?.isFilterSortUnable()
            if (!mUnableFilterSortStatus.contains(status) && !isFilterSortUnable.isTrue()) {
                showFilterDialog()
            }
        }
        mFilterText = view.findViewById(R.id.mFilterText)
        mFilterIcon = view.findViewById(R.id.mFilterIcon)
        mFilterIcon2 = view.findViewById(R.id.mFilterIcon2)

        view.bindClick<View>(R.id.mSortContainer) {
            val status = mModel?.mUIStatus?.mStatus ?: return@bindClick
            val isFilterSortUnable = mModel?.itfLiveData?.value?.isFilterSortUnable()
            if (!mUnableFilterSortStatus.contains(status) && !isFilterSortUnable.isTrue()) {
                showSortDialog()
            }
        }
        mSortText = view.findViewById(R.id.mSortText)
        mSortIcon = view.findViewById(R.id.mSortIcon)
        mSortIcon2 = view.findViewById(R.id.mSortIcon2)

        mSaveSettings = view.bindClick(R.id.mSaveView) {
//            ShitAlertDialog(requireContext(), ShitAlertDialogConfig<ShitAlertDialog>().apply {
//                msgRes = string.joywork_save_view_title
//                subMsg = requireActivity().string(string.joywork_self_view_save_tip)
//                okRes = string.me_ok
//                okShitClick = {
//                    it.dismiss()
//                    saveView()
//                }
//            }).show()
            saveView()

        }

        mHadSaved = view.findViewById(R.id.mSave)

        mClear = view.bindClick(R.id.mClear) {
//            ShitAlertDialog(requireContext(), ShitAlertDialogConfig<ShitAlertDialog>().apply {
//                msgRes = string.joywork_view_clear_title
//                subMsg = requireActivity().string(string.joywork_self_view_clear_tips)
//                okRes = string.me_ok
//                okShitClick = {
//                    it.dismiss()
//                    clearView()
//                }
//            }).show()
            clearView()
        }
        initObserver()
    }

    private val mEditModelBackups = HashMap<String, Boolean>()

    private fun initObserver() {
        val model =
            ViewModelProviders.of(requireActivity()).get(TeamDetailViewModel::class.java)
        mModel = model

        model.unableFilterSortLiveData.observe(requireActivity()) {
            unableFilterSort()
        }
        model.uiStatusLiveData.observe(viewLifecycleOwner) {
            if (it.isInited()) {
                mFilterObserver.invoke(it.mFilter)
                mSortObserver.invoke(it.mSort)
                mStatusObserver.invoke(it.mStatus)
            }
        }
        model.onlyUILiveData.observe(requireActivity()) { unuse ->
            val it = model.mUIStatus
            val itf = model.itfLiveData.value
            if (itf != null) {
                model.updateEditModel(mEditModelBackups[itf.getPageId()] ?: false)
            }
            if (it.isInited()) {
                mFilterObserver.invoke(it.mFilter)
                mSortObserver.invoke(it.mSort)
                mStatusObserver.invoke(it.mStatus)
            }
        }
        model.statusLiveData.observe(viewLifecycleOwner, mStatusObserver)
        model.filterValueLiveData.observe(viewLifecycleOwner, mFilterObserver)
        model.sortValueLiveData.observe(viewLifecycleOwner, mSortObserver)
        model.saveUserLiveData.observe(viewLifecycleOwner, mUserObserver)

        model.editModelLiveData.observe(viewLifecycleOwner) {
            val itf = mModel?.itfLiveData?.value
            if (itf != null) {
                mEditModelBackups[itf.getPageId()] = it
            }
            handleSaveView()
        }
    }

    private fun showFilterDialog() {
        val model = mModel ?: return
        val c = model.mUIStatus.mFilter.enum.code
        val actions = listOf(
            ProjectFilterEnum.SCREEN_NULL,
            ProjectFilterEnum.SCREEN_THIS_WEEK,
            ProjectFilterEnum.SCREEN_NEXT_WEEK,
            ProjectFilterEnum.SCREEN_CUSTOMS
        )
        showFilterDialog(requireContext(), actions, model.mUIStatus.mFilter, model, {
            if (it == ProjectFilterEnum.SCREEN_CUSTOMS) {
                mutableListOf(
                    ProjectFilterEnum.SCREEN_EXECUTOR.code,
                    ProjectFilterEnum.SCREEN_EDN_TIME.code,
                    ProjectFilterEnum.SCREEN_PRIORITY.code,
                    ProjectFilterEnum.SCREEN_PROJECT.code,
                ).contains(c)
            } else {
                it.code == c
            }
        }) {
            onFilterSelect(it)
        }
    }

    private fun onFilterSelect(s: ProjectFilterEnum) {
        val model = mModel ?: return
        if (s.getClickId() == JoyWorkConstant.JOYWORK_FILTER_CUSTOM) {
            //点击自定义筛选时
            showCustomFilterDialog(
                requireContext(), model.mUIStatus, model.extLiveData.value,
                mutableListOf(ProjectFilterEnum.SCREEN_PROJECT)
            ) {
                if (it.enum == ProjectFilterEnum.SCREEN_PROJECT && !it.value?.projectId.isLegalString()) {
                    gotoSelectProject()
                } else {
                    model.updateFilter(it)
                }
            }
        } else {
            model.updateFilter(FilterValue.getInstance(s))
        }
    }

    private fun gotoSelectProject() {
        val i = Intent(
            requireActivity(),
            SelectProjectActivity::class.java
        )
        startActivityForResult(i, 101)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (data != null && requestCode == 101 && resultCode == Activity.RESULT_OK) {
            val p: JoyWorkDetail.Project? =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    data.getParcelableExtra("project", JoyWorkDetail.Project::class.java)
                } else {
                    data.getParcelableExtra("project")
                }
            val model = mModel ?: return
            val filterValue = FilterValue().apply {
                type = ProjectFilterEnum.SCREEN_PROJECT.code
                value = ProjectFilterExt().apply {
                    projectFilterEnum = ProjectFilterEnum.SCREEN_PROJECT
                    projectId = p?.projectId
                    this.title = p?.title
                }
            }
            model.updateFilter(filterValue)
            return
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun showSortDialog() {
        try {
            val model = mModel ?: return
            val sort = model.mUIStatus.mSort
            showSortDialog3(
                getSortActions(), requireContext(),
                sort, false,
            ) { sortAction: ProjectSortAction, selected, hadChanged ->
                if (!hadChanged) {
                    return@showSortDialog3
                }
                if (sortAction.getClickId().isLegalString()) {
                    JDMAUtils.onEventClick(sortAction.getClickId(), sortAction.getClickId())
                }
                val tmpType =
                    if (selected) ProjectSortTypeEnum.SORT_IN_GROUP else ProjectSortTypeEnum.SORT_OTHER
                model.updateGroupInnerSort(tmpType)
                model.updateSort(sortAction)
            }
        } catch (e: Throwable) {
            //empty
        }
    }

    private fun getSortActions(): List<ProjectSortAction> {
        val list = mutableListOf(
            // 自定义分组、截止时间、优先级、所属清单
            ProjectSortTime(requireContext()),
            ProjectPriority(requireContext()),
            ProjectSortProject(requireContext()),
        )
        mModel?.itfLiveData?.value?.handleSortActions(list)
        return list
    }

    private fun handleSaveView() {
        if (mModel?.editModelLiveData?.value.isTrue()) {
            mSaveSettings.visible()
            mHadSaved.gone()
        } else if (mModel?.saveUserLiveData?.value == null) {
            mSaveSettings.gone()
            mHadSaved.gone()
        } else {
            mSaveSettings.gone()
            mHadSaved.visible()
        }
        handleClearView()
    }

    private fun handleClearView() {
        if (mModel?.saveUserLiveData?.value == null) {
            mClear.gone()
        } else {
            mClear.visible()
        }
    }

    private fun handleFilterSortView(
        tv: TextView?,
        icon: TextView?,
        icon2: TextView?,
        text: String,
        selected: Boolean
    ) {
        if (!selected) {
            tv?.text = text
            tv?.argb("#232930")
            icon?.argb("#232930")
            icon2?.argb("#232930")
        } else {
            tv?.text = text
            tv?.argb("#FE3E33")
            icon?.argb("#FE3E33")
            icon2?.argb("#FE3E33")
        }
    }

    private fun unableFilterSort() {
        val model = mModel ?: return
        model.run {
            mUIStatus.mFilter = FilterValue.getNullInstance()
            mUIStatus.mSort = SortValue.getDefaultSort(requireContext())
        }
        mFilterObserver.invoke(model.mUIStatus.mFilter)
        mSortObserver.invoke(model.mUIStatus.mSort)

        mFilterText?.argb("#CDCDCD")
        mFilterIcon?.argb("#CDCDCD")
        mFilterIcon2?.argb("#CDCDCD")
        mSortText?.argb("#CDCDCD")
        mSortIcon?.argb("#CDCDCD")
        mSortIcon2?.argb("#CDCDCD")
    }

    private fun clearView() {
        mScope.launch {
            val itf = mModel?.itfLiveData?.value ?: return@launch
            val model = mModel ?: return@launch
            val result = clearProjectExt(
                itf.getProjectId(),
                model.customViewLiveData.value
            )
            result.ifSuccessToast {
                mUIStatusBackups.clear()
                model.updateProjectView(null)
                val defaultState = itf.getDefaultUIState()
                model.mUIStatus.mStatus = defaultState.mStatus
                model.mUIStatus.mSort = defaultState.mSort
                model.mUIStatus.mFilter = defaultState.mFilter
                model.updateSaveUser(null)
                model.notifyUpdate()
                model.updateEditModel(false)
            }
        }
    }

    private fun saveView() {
        val itf = mModel?.itfLiveData?.value ?: return
        val uiStatus = mModel?.mUIStatus ?: return
        mScope.launch {
            val result = saveProjectExt(
                itf.getProjectId(),
                uiStatus.mFilter,
                uiStatus.mSort,
                uiStatus.mStatus,
                itf.getViewType()
            )
            result.ifSuccessToast {
                val model = mModel ?: return@ifSuccessToast
                mUIStatusBackups.clear()
                model.updateProjectView(it!!.view?.viewId)
                model.updateSaveUser(it.view?.creator)
                model.updateEditModel(false)
            }
        }
    }

    suspend fun clearProjectExt(
        projectId: String,
        viewId: String?
    ): CoroutineResult<ProjectViewOuter> {
        return suspendCancellableCoroutine { c ->
            val itf = mModel?.itfLiveData?.value
            if (viewId == null || itf == null) {
                return@suspendCancellableCoroutine
            }
            ProjectRepo.clearProjectExt(
                projectId,
                viewId,
                itf.getViewType()
            ) { msg: String?, outer: ProjectViewOuter? ->
                resumeAfterNetwork(msg, outer, c)
            }
        }
    }

    suspend fun saveProjectExt(
        projectId: String,
        filterValue: FilterValue,
        sortValue: SortValue,
        status: TaskStatusEnum,
        type: Int?,
    ): CoroutineResult<ProjectViewOuter> {
        return suspendCancellableCoroutine { c ->
            ProjectRepo.saveProjectExt(
                projectId,
                filterValue,
                sortValue,
                status,
                type
            ) { msg: String?, outer: ProjectViewOuter? ->
                resumeAfterNetwork(msg, outer, c)
            }
        }
    }

    private fun resumeAfterNetwork(
        msg: String?,
        outer: ProjectViewOuter?,
        c: Continuation<CoroutineResult<ProjectViewOuter>>
    ) {
        if (outer != null) {
            c.resumeWith(Result.success(CoroutineResult<ProjectViewOuter>().success(outer)))
        } else {
            c.resumeWith(
                Result.success(
                    CoroutineResult<ProjectViewOuter>().failure(
                        JoyWorkEx.filterErrorMsg(
                            msg
                        )
                    )
                )
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mScope.cancel()
    }
}