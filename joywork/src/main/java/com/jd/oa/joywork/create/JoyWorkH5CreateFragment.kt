package com.jd.oa.joywork.create

import android.app.Activity
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.joywork.JoyWorkHandler
import com.jd.oa.joywork.JoyWorkLevel
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.common.EventWebFragment
import com.jd.oa.joywork.create.param.DeeplinkCreateParam
import com.jd.oa.joywork.create.param.EmailCreateParams
import com.jd.oa.joywork.detail.ui.des.JoyWorkDesActivity
import com.jd.oa.joywork.repo.JoyWorkCreateParams
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDialog
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDraft.Companion.removeDraft
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.team.chat.ProjectSelectorDeeplinkUtils
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.ToastUtils
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/6
 */
class JoyWorkH5CreateFragment : EventWebFragment() {
    private var mValue: Value? = null
    private lateinit var activityResultLauncher: ActivityResultLauncher<Intent>

    override fun initValue() {
        super.initValue()
        mValue = Value().apply {
            projectId =
                if (JoyWorkCreateActivity.showGroup) JoyWorkCreateActivity.projectId else null
            selId = JoyWorkCreateActivity.selId
            updateDup(JoyWorkCreateActivity.duplicateEnum)
            title = (activity as? JoyWorkCreateActivity)?.titleStr
            updateTime(
                (activity as? JoyWorkCreateActivity)?.startTime,
                (activity as? JoyWorkCreateActivity)?.endTime
            )
            updatePriority(JoyWorkLevel.NO.value)
            updateMembers(ArrayList())
            replaceAlertType(JoyWorkCreateActivity.mTs)
            getTmpData()?.let {
                extra = it.extra
                addTargets(it.kpiTarget)
            }
            updateShimo(JoyWorkCreateActivity.shimo)
            setNormalOwners(JoyWorkCreateActivity.owners)
            addSubJoyworkOwners(null)
            (getDeeplinkCreateParam() as? EmailCreateParams)?.let {
                des = it.desc
                title = it.title
                setNormalOwners(mutableListOf(JoyWorkUser.getSelf()))
            }
        }
        activityResultLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK && result.data != null) {
                val value = result.data?.getStringExtra(JoyWorkDesActivity.KEY)
                if (!value.isNullOrEmpty()) {
                    mValue?.apply {
                        des = value
                        setNormalOwners(mutableListOf(JoyWorkUser.getSelf()))
                    }
                }
            }
        }
    }

    override fun getCreatePageInfo(handler: CompletionHandler<Any>) {
        super.getCreatePageInfo(handler)
        val joyWorkCreateParams = JoyWorkCreateParams()
        val activity = activity as? JoyWorkCreateActivity
        joyWorkCreateParams.apply {
            title = mValue?.title
            startTime = activity?.startTime
            endTime = activity?.endTime
            alertTypes = JoyWorkCreateActivity.mTs
            dupEnum = JoyWorkCreateActivity.duplicateEnum
            groupId = JoyWorkCreateActivity.selId
            projectId = mValue?.projectId
            resources = mValue?.mAtt
            shimo = mValue?.shimo
            content = getTmpData()?.content ?: mValue?.extra
            mobileContent = getTmpData()?.mobileContent
            owners = mValue?.normalOwners
            bizCode = getTmpData()?.bizCode
            inventory = getTmpData()?.sessionId
            tripartiteName = getTmpData()?.tripartiteName
            groupDefaultProjectId = getDefaultProjectId()
            interactiveCard = JoyWorkCreateActivity.mSendDDChecked
            des = mValue?.des
        }
        val json = JsonUtils.getGson().toJson(joyWorkCreateParams.params)
        val jsonObject = JSONObject(json)
        val retObject = JSONObject().apply { put("opts", jsonObject) }
        MELogUtil.localD(MELogUtil.TAG_JS, "getTaskCreateInfo: $retObject")
        handler.setProgressData(retObject)
    }

    override fun createComplete(jsonObject: JSONObject) {
        super.createComplete(jsonObject)
        val optString = jsonObject.optString("data")
        val dataString = JSONObject(optString)
        val state = dataString.getString("state")
        if (state == "0") {
            afterCreateSuccess(dataString.getString("task"))
        } else {
            ToastUtils.showInfoToast(dataString.getString("errorMsg"))
        }
    }

    private fun afterCreateSuccess(result: String) {
        removeDraft()
        JoyWorkHandler.getInstance().onCreateFinish()
        mValue?.subOwners?.size?.let { JoyWorkHandler.getInstance().notifyCallback(it) }
        if (requireActivity() is JoyWorkCreateActivity) {
            (requireActivity() as JoyWorkCreateActivity).superFinish()
            JoyWorkCreateActivity.successRunnable?.show(JSONObject(result))
            getSuccessCallback()?.invoke(result, mValue, null)
        } else {
            requireActivity().finish()
        }
    }

    private fun getTmpData(): ShortcutDialogTmpData? {
        return if (activity is JoyWorkCreateActivity) {
            JoyWorkCreateActivity.tmpData
        } else {
            ShortcutDialogTmpData()
        }
    }

    private fun getSuccessCallback(): Function3<String, Value?, JoyWorkShortcutDialog?, Unit>? {
        return if (activity is JoyWorkCreateActivity) {
            JoyWorkCreateActivity.successCallback
        } else {
            null
        }
    }

    private fun getMEChattingLabel(): MEChattingLabel? {
        return if (activity is JoyWorkCreateActivity && JoyWorkCreateActivity.tmpData != null) {
            JoyWorkCreateActivity.tmpData.meChattingLabel
        } else {
            null
        }
    }

    private fun getDefaultProjectId(): String? {
        val labelOuter: MEChattingLabel? = getMEChattingLabel()
        if (labelOuter?.hasJoyWorkLabel() == false) {
            return null
        }
        var defaultProjectId: String? = null
        labelOuter?.labels?.forEach {
            defaultProjectId = ProjectSelectorDeeplinkUtils.getProjectId(it.linkUrl.mobile)
        }
        return defaultProjectId
    }

    private fun getDeeplinkCreateParam(): DeeplinkCreateParam? {
        return if (activity is JoyWorkCreateActivity) {
            JoyWorkCreateActivity.deeplinkCreateParam
        } else {
            null
        }
    }

}