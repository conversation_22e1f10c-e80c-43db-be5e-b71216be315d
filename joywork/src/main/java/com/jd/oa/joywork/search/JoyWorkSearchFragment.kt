package com.jd.oa.joywork.search

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.around.base.HeaderFooterRecyclerAdapterWrapper
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkDetailParam
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.JoyWorkMediator
import com.jd.oa.joywork.R
import com.jd.oa.joywork.clear
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.kpi.KpiRepo
import com.jd.oa.joywork.union
import com.jd.oa.joywork.view.red
import com.jd.oa.utils.vertical
import java.util.Objects

class JoyWorkSearchFragment : BaseFragment() {
    private var mJoyWorkSearchViewModel: JoyWorkSearchViewModel? = null
    private val mSearchHandler = Handler()
    private val mHistories = HashMap<String, SearchInfo>()
    private var mSwipe: SwipeRefreshLayout? = null
    private var mPullUpLoadHelper: PullUpLoadHelper? = null
    private var mRv: RecyclerView? = null

    private val mNetRunnable = Runnable {
        search(false)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mJoyWorkSearchViewModel =
            ViewModelProviders.of(requireActivity()).get(JoyWorkSearchViewModel::class.java)
        val view = inflater.inflate(R.layout.joywork_search_frg, container, false)
        mRv = view.findViewById(R.id.mRecyclerView)
        mRv?.vertical()
        showInitUI()
        mJoyWorkSearchViewModel?.contentLiveData?.observe(requireActivity()) {
            if (!it.isLegalString()) {
                cleanBeforeNet()
                showInitUI()
            } else {
                searchDelay()
            }
        }
        mJoyWorkSearchViewModel?.statusLiveData?.observe(requireActivity()) {
            searchDelay()
        }
        mSwipe = view.findViewById(R.id.mSwipe)
        mSwipe.red(requireActivity())
        mSwipe?.setOnRefreshListener {
            // pull to refresh, we should cancel all plans of searching
            cleanBeforeNet()
            mSwipe?.isRefreshing = true
            search(false)
        }
        return view
    }

    private fun searchDelay() {
        cleanBeforeNet()
        mSearchHandler.postDelayed(mNetRunnable, 100)
    }

    private fun search(useCache: Boolean) {
        val infoOuter = getCurrentInfo() ?: return
        infoOuter.firstSearch(useCache) net@{ it, info ->
            mSwipe?.isRefreshing = false
            // If the result can't match the current text, should ignore it!
            if (!Objects.equals(info.key, mJoyWorkSearchViewModel?.key() ?: "")) {
                return@net
            }
            val new = it.tasks ?: mutableListOf()
            if (new.isLegalList()) {
                val adapter = getRvAdapter()
                if (adapter != null) {
                    adapter.replace(new)
                } else {
                    mRv?.adapter = JoyWorkSearchAdapter(requireContext(), new)
                    getRvAdapter()?.itemClick = { itemInfo ->
                        val params = JoyWorkDetailParam(itemInfo.title, itemInfo.taskId, null)
                        JoyWorkMediator.goDetail(requireContext(), params)
//                        activity?.finish()
                    }
                    loadMore()
                }
                it.finishLoadMore()
            } else {
                showEmpty()
            }
        }
    }

    private fun cleanBeforeNet() {
        mSearchHandler.clear()
    }

    private fun showEmpty() {
        mRv?.adapter = JoyWorkEmptyAdapter.empty(
            requireContext(),
            R.string.joywork_department_empty,
            R.drawable.joywork_department_empty
        )
    }

    private fun showInitUI() {
        mRv?.adapter = JoyWorkEmptyAdapter.empty(
            requireContext(),
            R.string.joywork_search_init,
            R.drawable.joywork_department_empty
        )
    }

    private fun loadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(mRv) {
            val info = getCurrentInfo() ?: return@PullUpLoadHelper
            info.loadMore { searchResultList, searchInfo ->
                // If the result can't match the current text, should ignore it!
                if (!Objects.equals(searchInfo.key, mJoyWorkSearchViewModel?.key() ?: "")) {
                    return@loadMore
                }
                if (searchResultList == null) {
                    //mPullUpLoadHelper?.setLoaded()
                    mPullUpLoadHelper?.setComplete()
                } else {
                    searchResultList.finishLoadMore()
                    // If the result can't match the current text, should ignore it!
                    if (!Objects.equals(info.key, mJoyWorkSearchViewModel?.key() ?: "")) {
                        return@loadMore
                    }
                    getRvAdapter()?.append(searchResultList.tasks)
                }
            }
        }
    }


    private fun getCurrentInfo(): SearchInfo? {
        val str = mJoyWorkSearchViewModel?.key() ?: return null
        val content = mJoyWorkSearchViewModel?.contentLiveData?.value ?: return null
        val status = mJoyWorkSearchViewModel?.statusLiveData?.value ?: return null
        val info = mHistories[str] ?: SearchInfo(
            requireContext(),
            str,
            content,
            status
        )
        mHistories[str] = info
        return info
    }

    private fun getRvAdapter(): JoyWorkSearchAdapter? {
        val adapter = mRv?.adapter ?: return null
        if (adapter is HeaderFooterRecyclerAdapterWrapper) {
            return adapter.targetAdapter as? JoyWorkSearchAdapter
        } else if (adapter is JoyWorkSearchAdapter) {
            return adapter
        } else {
            return null
        }
    }

    private fun SearchResultList.finishLoadMore() {
        if ((tasks?.size ?: 0) >= 20) {
            mPullUpLoadHelper?.setLoaded()
        } else {
            mPullUpLoadHelper?.setComplete()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mSearchHandler.clear()
    }

    private class SearchInfo(
        private val context: Context,
        val key: String,
        val content: String,
        val status: Int
    ) {
        val data = ArrayList<SearchResult>()
        fun firstSearch(useCache: Boolean, callback: (SearchResultList, SearchInfo) -> Unit) {
            if (data.isLegalList() && useCache) {
                // already had cache, so go back immediately
                val cache = ArrayList(data)
                val ans = SearchResultList()
                ans.tasks = cache
                callback.invoke(ans, this)
            } else {
                KpiRepo.searchJoyWork(content, status, 0) { ds, msg ->
                    if (ds != null) {
                        // cache
                        val ans = ds.tasks ?: ArrayList()
                        ds.tasks = ans
                        data.union(ds.tasks) {
                            it.taskId
                        }
                        callback.invoke(ds, this)
                    } else {
                        Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                            .show()
                    }
                }
            }
        }

        fun loadMore(callback: (SearchResultList?, SearchInfo) -> Unit) {
            KpiRepo.searchJoyWork(content, status, data.size) { ds, msg ->
                if(!TextUtils.isEmpty(msg)) {
                    Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                        .show()
                    callback(null, this)
                } else if (ds != null && ds.tasks.isLegalList()) {
                    data.union(ds.tasks) {
                        it.taskId
                    }
                    callback.invoke(ds, this)
                } else {
                    callback.invoke(null, this)
                }
            }
        }
    }

}