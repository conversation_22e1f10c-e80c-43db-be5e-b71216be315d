package com.jd.oa.joywork.detail

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.webkit.JavascriptInterface
import com.jd.me.web2.webview.JMEWebview
import com.jd.oa.BaseActivity
import com.jd.oa.fragment.js.hybrid.JsEvent
import com.jd.oa.fragment.js.hybrid.JsIm
import com.jd.oa.fragment.js.hybrid.JsUser
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.ui.JsPopup
import com.jd.oa.joywork.detail.ui.WebViewStatistics
import com.jd.oa.joywork.detail.ui.WebViewStatistics.end
import com.jd.oa.joywork.initJoyWork
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.utils.JoyWorkNetConfig
import com.tencent.smtt.export.external.interfaces.WebResourceError
import com.tencent.smtt.export.external.interfaces.WebResourceRequest
import com.tencent.smtt.export.external.interfaces.WebResourceResponse
import com.tencent.smtt.sdk.WebSettings
import com.tencent.smtt.sdk.WebViewClient
import wendu.dsbridge.CompletionHandler

class JoyWorkHistoryActivity : BaseActivity() {
    private lateinit var mWebView: JMEWebview

    companion object {
        fun start(activity: Activity, taskId: String) {
            val i = Intent(activity, JoyWorkHistoryActivity::class.java)
            i.putExtra("taskId", taskId)
            activity.startActivity(i)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        kotlin.runCatching {
            actionBar?.hide()
        }
        kotlin.runCatching {
            supportActionBar?.hide()
        }
        setContentView(R.layout.joywork_history)
        mWebView = findViewById(R.id.comment)
        initWebView(getInputTaskId(savedInstanceState))
        findViewById<View>(R.id.back_button).setOnClickListener {
            finish()
        }
    }

    private fun getInputTaskId(savedInstanceState: Bundle?): String {
        var ans = intent.getStringExtra("taskId")
        if (!ans.isLegalString() && savedInstanceState != null) {
            ans = savedInstanceState.getString("taskId")
        }
        return ans?:""
    }

    private fun initWebView(taskId: String) {
        mWebView.initJoyWork(JoyWorkNetConfig.getWorkHistory(taskId, this))
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val taskId = intent.getStringExtra("taskId")
        if (taskId.isLegalString()) {
            outState.putString("taskId", taskId)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mWebView.loadUrl("about:blank")
        mWebView.destroy() // 销毁web view
    }
}