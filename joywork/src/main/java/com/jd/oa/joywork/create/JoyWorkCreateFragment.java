package com.jd.oa.joywork.create;

import static android.app.Activity.RESULT_OK;
import static com.jd.oa.joywork.detail.ui.TaskDetailActivity.KEY_TASK_ID;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcelable;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleCoroutineScope;
import androidx.lifecycle.LifecycleOwnerKt;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.upload.UploadTask;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.utils.FileType;
import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkAttachmentChoose;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.joywork.JoyWorkHandler;
import com.jd.oa.joywork.JoyWorkLevel;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.ObjectLocal;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork.bean.KR;
import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.joywork.collaborator.OneTimeDataRepo;
import com.jd.oa.joywork.create.param.DeeplinkCreateParam;
import com.jd.oa.joywork.detail.DialogManager;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.model.service.im.dd.entity.Members;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity;
import com.jd.oa.joywork.detail.ui.des.JoyWorkDesActivity;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter;
import com.jd.oa.joywork.repo.JoyWorkCreateParams;
import com.jd.oa.joywork.repo.JoyWorkRepo;
import com.jd.oa.joywork.shortcut.ChatType;
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDialog;
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDraft;
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData;
import com.jd.oa.joywork.team.SelectProjectTaskGroupActivity;
import com.jd.oa.joywork.team.bean.GrayInfo;
import com.jd.oa.joywork.team.bean.Group;
import com.jd.oa.joywork.team.kpi.KpiRepo;
import com.jd.oa.joywork.team.kpi.SelectGoalActivity;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.ImageCompressUtils;
import com.jd.oa.utils.EventTrackerKt;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;
import com.yu.bundles.album.MaeAlbum;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function3;

/**
 * 会将各个 item 拆分到不同的 factory 中。主要是为了业务上可自动增删
 */
public class JoyWorkCreateFragment extends BaseFragment implements CreateDetailFragmentItf {

    private static final int REQUEST_CODE_CAPTURE = 200;
    private static final int REQUEST_CODE_GALLERY = 300;
    static final int RELATION_REQ = 400;
    static final int OWNER_REQ = 500;
    private final int REQ_SEL_ORDER = 600;
    private final int REQ_SEL_TARGET = 700;
    private UploadTask uploadTask;

    private View mCreateView;

    private Value mValue;

    View mInputText;

    // 拍照的图片地址
    private String mCapturePath;
    private Toast mToast;

    final CreateArrayList mItemFactories = new CreateArrayList();

    public static JoyWorkCreateFragment forTask(@NonNull String taskId) {
        JoyWorkCreateFragment fragment = new JoyWorkCreateFragment();
        Bundle args = new Bundle();
        args.putString(KEY_TASK_ID, taskId);
        fragment.setArguments(args);
        return fragment;
    }

    String getInTitle() {
        if (getArguments() != null && getArguments().containsKey("title")) {
            return getArguments().getString("title");
        } else if (getActivity() instanceof JoyWorkCreateActivity) {
            return ((JoyWorkCreateActivity) getActivity()).getTitleStr();
        } else {
            return "";
        }
    }

    private Long getInStartTime() {
        if (getArguments() != null && getArguments().containsKey("startTime")) {
            return getArguments().getLong("startTime");
        } else if (getActivity() instanceof JoyWorkCreateActivity) {
            return ((JoyWorkCreateActivity) getActivity()).getStartTime();
        } else {
            return null;
        }
    }

    private Long getInEndTime() {
        if (getArguments() != null && getArguments().containsKey("endTime")) {
            return getArguments().getLong("endTime");
        } else if (getActivity() instanceof JoyWorkCreateActivity) {
            return ((JoyWorkCreateActivity) getActivity()).getEndTime();
        } else {
            return null;
        }
    }

    boolean isProject() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return ((JoyWorkCreateActivity) getActivity()).isProject();
        } else {
            return false;
        }
    }

    private boolean getSendChecked() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.mSendDDChecked;
        } else {
            return false;
        }
    }

    public ShortcutDialogTmpData getTmpData() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.tmpData;
        } else {
            return new ShortcutDialogTmpData();
        }
    }

    private List<JoyWorkUser> getInOwners() {
        if (isSubJoywork()) {
            return null;
        }
        if (getActivity() instanceof JoyWorkCreateActivity) {
            if (JoyWorkCreateActivity.owners == null) {
                JoyWorkCreateActivity.owners = new ArrayList<>();
            }
            return JoyWorkCreateActivity.owners;
        } else {
            return null;
        }
    }

    String getParentId() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.parentId;
        } else {
            return null;
        }
    }

    private boolean isSubJoywork() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.isSubJoywork;
        } else {
            return false;
        }
    }

    String getInProjectId() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.projectId;
        } else {
            return null;
        }
    }

    String getInSelId() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.selId;
        } else {
            return null;
        }
    }

    private Set<AlertType> getAlertTypes() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.mTs;
        } else {
            return null;
        }
    }

    private MEChattingLabel getMEChattingLabel() {
        if (getActivity() instanceof JoyWorkCreateActivity && JoyWorkCreateActivity.tmpData != null) {
            return JoyWorkCreateActivity.tmpData.getMeChattingLabel();
        } else {
            return null;
        }
    }

    private ArrayList<Group> getInGroups() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.groups;
        } else {
            return null;
        }
    }

    private String getBizCode() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return ((JoyWorkCreateActivity) getActivity()).getBizCode();
        } else {
            return null;
        }
    }

    @Override
    public String getSessionId() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            if (getTmpData() != null && getTmpData().getChatType().equals(ChatType.GROUP.getValue())) {
                return ((JoyWorkCreateActivity) getActivity()).getSessionId();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    private Function3<String, Value, JoyWorkShortcutDialog, Unit> getSuccessCallback() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.successCallback;
        } else {
            return null;
        }
    }

    private DeeplinkCreateParam getDeeplinkCreateParam() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.deeplinkCreateParam;
        } else {
            return null;
        }
    }

    private DuplicateEnum getDuplicateEnum() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return ((JoyWorkCreateActivity) getActivity()).getDuplicateEnum();
        } else {
            return null;
        }
    }

    private List<Documents> getShimoList() {
        if (getActivity() instanceof JoyWorkCreateActivity) {
            return JoyWorkCreateActivity.shimo;
        } else {
            return null;
        }
    }

    CreateViewModel mCreateViewModel;

    private View mAlarmIcon;
    private ImageView mSendIcon;
    private View mSendContainer;
    private TextView mSendText;
    private boolean mSendIconChecked;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        // 默认只有这五个
        mItemFactories.append(new TitleCreateFactory());
        mItemFactories.append(new DescCreateFactory());
        mItemFactories.append(new OrderCreateFactory());
        mItemFactories.append(new ShimoCreateFactory());
        mItemFactories.append(new AttachmentCreateFactory());
        if (!(getViewStrategy() instanceof SubJoyworkStrategy)) {
            mItemFactories.append(new RelationCreateFactory());
        }
        mItemFactories.append(new DeadlineCreateFactory());
        if (getDuplicateEnum() != null) {
            mItemFactories.add(new DuplicateCreateFactory());
        }
//        if (getTmpData() != null && ObjExKt.isLegalList(getTmpData().getKpiTarget())) {
//            mItemFactories.add(new TargetCreateFactory());
//        }
        if (ObjExKt.isLegalSet(getAlertTypes())) {
            mItemFactories.add(new AlarmCreateFactory());
        }
        mItemFactories.add(new PriorityCreateFactory());
        return inflater.inflate(R.layout.jdme_joywork_create_fragment, container, false);
    }

    private void initViewModels(View view) {
        view.post(new Runnable() {
            @Override
            public void run() {
                LifecycleCoroutineScope scope = LifecycleOwnerKt.getLifecycleScope(getViewLifecycleOwner());
                mCreateViewModel.initObserver(scope, project -> {
                    onActivityResult(REQ_SEL_ORDER, Activity.RESULT_OK, new Intent().putExtra("project", project));
                    return null;
                });
            }
        });
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mCreateViewModel = ViewModelProviders.of(this).get(CreateViewModel.class);
        MEChattingLabel label = getMEChattingLabel();
        if (label != null && ObjExKt.isLegalList(label.labels)) {
            mCreateViewModel.getDefaultProject(label, view.getContext());
        }
        initViewModels(view);
        // 更新传入的默认参数
        mValue = new Value(this);
        mValue.projectId = getInProjectId();
        mValue.selId = getInSelId();
        mValue.updateDup(getDuplicateEnum());
        mValue.title = getInTitle();
        mValue.updateTime(getInStartTime(), getInEndTime());
        mValue.updatePriority(JoyWorkLevel.NO.getValue());
        mValue.updateMembers(new ArrayList<Members>());
        mValue.replaceAlertType(getAlertTypes());
        if (getTmpData() != null) {
            mValue.extra = getTmpData().getExtra();
            mValue.addTargets(getTmpData().getKpiTarget());
        }
//        mValue.updateAlertTime(null);
        // mValue.replaceAlertType(getTmpData().);
        mValue.updateShimo(getShimoList());
        mValue.setNormalOwners(getInOwners());
        mValue.addSubJoyworkOwners(null);
//        mCreateViewModel.getProjectChangeLivedata().observe(getActivity(), new Observer<Boolean>() {
//            @Override
//            public void onChanged(Boolean aBoolean) {
//                if (mValue.projects.isEmpty()) {
//                    mItemFactories.removeByClass(OrderCreateFactory.class);
//                }
//            }
//        });
        mCreateViewModel.getGrayInfoChangeLivedata().observe(getActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                GrayInfo info = mValue.safeGrayInfo();
                if (!info.hasGoalPermission() || (getViewStrategy() instanceof SubJoyworkStrategy)) {
                    mTargetItem.setVisibility(View.GONE);
                } else {
//                    mTargetItem.setVisibility(View.VISIBLE);
                    mTargetItem.setVisibility(View.GONE);
                }
            }
        });
        mCreateViewModel.getTargetChangeLivedata().observe(getActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (mValue.mergeTargetsAndKrs().isEmpty()) {
                    mItemFactories.removeByClass(TargetCreateFactory.class);
                } else {
                    addTargetUI();
                }
            }
        });
        mCreateViewModel.getKrChangeLivedata().observe(getActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (mValue.mergeTargetsAndKrs().isEmpty()) {
                    mItemFactories.removeByClass(TargetCreateFactory.class);
                } else {
                    addTargetUI();
                }
            }
        });
        getViewStrategy().init(view, this);

        if (getViewStrategy() instanceof ProjectStrategy) {
            mValue.setGroups(getInGroups());
            mValue.updateGroupUI(getInSelId());
        }
        if (getDeeplinkCreateParam() != null) {
            getDeeplinkCreateParam().initCreateValue(mValue);
        }

        mAlarmIcon = view.findViewById(R.id.alarm_icon);

        view.findViewById(R.id.close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                requireActivity().finish();
                EventTrackerKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_CREATE_TASK_ABANDON_CREATION);
            }
        });

        // 导入父待办相关
        mInputText = view.findViewById(R.id.mInputText);
        if (isSubJoywork()) {
            mInputText.setOnClickListener(this);
        }

        mSendContainer = view.findViewById(R.id.mSendDD);
        mSendIcon = view.findViewById(R.id.mSendIcon);
        mSendText = view.findViewById(R.id.mSendText);

        initUI(view);

        mCreateView = view.findViewById(R.id.new_task);
        mCreateView.setOnClickListener(this);
        mCreateViewModel.getTitleChangeLivedata().observe(requireActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                handleCreateView();
            }
        });
        mCreateViewModel.getOwnersChangeLivedata().observe(requireActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                handleCreateView();
                hideGroupsIfNecessary();
                updateSendIcon(mSendIconChecked);
            }
        });
        mCreateViewModel.getNormalOwnersChangeLivedata().observe(requireActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                hideGroupsIfNecessary();
                updateSendIcon(mSendIconChecked);
            }
        });
        mCreateViewModel.getTimeChangeLivedata().observe(requireActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (ObjExKt.isLegalTimestamp(mValue.endTime)) {
                    // 处理提醒。此时情况一般是用户在设置截止时间时统一设置了提醒、重复
                    if (!mItemFactories.has(AlarmCreateFactory.class)
                            && ObjExKt.isLegalSet(mValue.mAlertType) && !AlertType.containNO(mValue.mAlertType)) {
                        // 没有设置过提醒且本次时间选择时设置了提醒，且提醒不是"不提醒"
                        AlarmCreateFactory factory = new AlarmCreateFactory();
                        mItemFactories.add(factory);
                        factory.buildView(getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
                    }
                    // 处理重复
                    if (!mItemFactories.has(DuplicateCreateFactory.class)
                            && mValue.duplicateEnum != null && mValue.duplicateEnum != DuplicateEnum.NO) {
                        // 没有设置过重复且设置了截止时间，且设置了重复，且重复不是"不重复"
                        DuplicateCreateFactory factory = new DuplicateCreateFactory();
                        mItemFactories.add(factory);
                        factory.buildView(getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
                    }
                } else {
                    mItemFactories.removeByClass(AlarmCreateFactory.class);
                    mItemFactories.removeByClass(DuplicateCreateFactory.class);
                }
            }
        });
    }

    ViewStrategy getViewStrategy() {
        return ViewStrategyKt.getViewStrategy(isSubJoywork(), isProject());
    }

    private View mTargetItem;

    private void initUI(View view) {
        // 填充 view
        for (CreateItemFactory factory : mItemFactories) {
            factory.buildView((ViewGroup) view.findViewById(R.id.containerParent), mValue, this, mCreateViewModel);
        }
        view.findViewById(R.id.dup_icon).setOnClickListener(this);
        mTargetItem = view.findViewById(R.id.target_item);
        mTargetItem.setVisibility(View.GONE);
//        mTargetItem.setOnClickListener(this);
        view.findViewById(R.id.order_item).setOnClickListener(this);
        view.findViewById(R.id.alarm_icon).setOnClickListener(this);
        view.findViewById(R.id.attachment_icon).setOnClickListener(this);
        view.findViewById(R.id.priority_icon).setOnClickListener(this);
        // 只有从咚咚触发的创建待办才会开放发送至会话的开关
        if (getTmpData() != null && getTmpData().getFromDD()) {
            mSendContainer.setVisibility(View.VISIBLE);
            mSendText.setText(R.string.joywork_send_dd);
            // updateSendIcon(getSendChecked());
            mSendContainer.setOnClickListener(v -> updateSendIcon(!mSendIconChecked));
        } else {
            mSendContainer.setVisibility(View.GONE);
        }
        KpiRepo.INSTANCE.grayInfo(new Function1<GrayInfo, Unit>() {
            @Override
            public Unit invoke(GrayInfo grayInfo) {
                mValue.setGrayInfo(grayInfo);
                return null;
            }
        });
        mValue.setGrayInfo(new GrayInfo());
    }

    private void updateSendIcon(Boolean checked) {
        mSendIcon.setSelected(checked);
        mSendIconChecked = checked;
        EventTrackerKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_CREATE_TASK_SEND2_CHAT);
    }

    private void hideGroupsIfNecessary() {
        if (!shouldHideGroup()) {
            if (!mItemFactories.has(GroupCreateFactory.class)) {
                GroupCreateFactory factory = new GroupCreateFactory();
                mItemFactories.add(factory);
                factory.buildView(getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
            }
        } else {
            mItemFactories.removeByClass(GroupCreateFactory.class);
        }
    }

    private void handleCreateView() {
        if (mValue.title == null || mValue.title.trim().isEmpty()) {
            // 如果没有标题，不能创建
            mCreateView.setEnabled(false);
        } else {
            if (getViewStrategy().needOwner()) {
                // 有标题，需要判断有无负责人
                mCreateView.setEnabled(!getViewStrategy().getOwners(mValue).isEmpty());
            } else {
                mCreateView.setEnabled(true);
            }
        }
    }

    @Override
    public void onClick(final View v) {
        if (v.getId() == R.id.new_task) {
            // 新建待办
            v.setEnabled(false);
            JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_NEW_MORE_CREATE, JoyWorkConstant.JOYWORK_NEW_MORE_CREATE);

            if (isSubJoywork()) {
                JoyWorkRepo.INSTANCE.batchCreateChildTasks(new Function1<String, Unit>() {
                    @Override
                    public Unit invoke(String errorMsg) {
                        if (ObjExKt.isLegalString(errorMsg)) {
                            // 失败后恢复按钮状态
                            v.setEnabled(true);
                            ToastUtils.showInfoToast(errorMsg);
                        } else {
                            afterCreateSuccess("", null);
                        }
                        return null;
                    }
                }, handleNetParams());
            } else {
                JoyWorkRepo.INSTANCE.create(new Function3<JoyWorkDetail, String, String, Unit>() {
                    @Override
                    public Unit invoke(JoyWorkDetail joyWorkDetail, String errorMsg, String result) {
                        if (joyWorkDetail != null && ObjExKt.isLegalString(result)) {
                            afterCreateSuccess(result, joyWorkDetail);
                        } else {
                            // 失败后恢复按钮状态
                            v.setEnabled(true);
                            ToastUtils.showInfoToast(errorMsg);
                        }
                        return null;
                    }
                }, handleNetParams());
            }


        } else if (mInputText.getId() == v.getId()) {
            // 导入
            Object o = ObjectLocal.INSTANCE.getObj();
            JDMAUtils.onEventClick(
                    JoyWorkConstant.JOYWORK_BATCH_IMPORT,
                    JoyWorkConstant.JOYWORK_BATCH_IMPORT
            );
            if (o instanceof TaskDetailEntity) {
                JoyWorkDetail detail = ((TaskDetailEntity) o).getJoyWorkDetail();
                if (detail != null) {
//                    mValue.setSubJoyworkOwners(null);  // 保留所有选择的执行人
                    if (ObjExKt.isLegalString(detail.getTitle())) {
                        mValue.title = detail.getTitle();
                    } else {
                        mValue.title = "";// 没有标题，导入时清空所有已填内容
                    }
                    mValue.updateTime(detail.getStartTime(), detail.getEndTime());
                    mValue.replaceAlertType(detail.getAlertTypes());
//                    mValue.replaceAlertType();
                    if (mValue.alertTime != null && !mItemFactories.has(AlarmCreateFactory.class)) {
                        // 提醒时间没有，就需要加载显示
                        final AlarmCreateFactory factory = new AlarmCreateFactory();
                        mItemFactories.add(factory);
                        factory.buildView((ViewGroup) getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
                    }
                    // 有优先级
                    if (detail.getPriorityType() != JoyWorkLevel.NO.getValue()) {
                        mValue.updatePriority(detail.getPriorityType());
                        // 优先级如果没有，就需要加载
                        if (mValue.priority != null && !mItemFactories.has(PriorityCreateFactory.class)) {
                            final PriorityCreateFactory factory = new PriorityCreateFactory();
                            mItemFactories.add(factory);
                            factory.buildView((ViewGroup) getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
                        }
                    }
                    mValue.updateDes(detail.getRemark());
                    mValue.updateShimo(detail.getDocuments());
                    mValue.replaceAtt(detail.getResources());
                    for (CreateItemFactory factory : mItemFactories) {
                        factory.updateValue();
                    }
//                    mValue.updateMembers(detail.getExecutors());
                }
            }
        } else if (v.getId() == R.id.dup_icon) {
            // 重复
            if (!ObjExKt.isLegalTimestamp(mValue.endTime)) {
                if (mToast != null)
                    mToast.cancel();
                mToast = Toast.makeText(v.getContext(), R.string.joywork_create_dup_tips, Toast.LENGTH_SHORT);
                mToast.show();
                return;
            }
            if (mItemFactories.has(DuplicateCreateFactory.class)) {
                for (CreateItemFactory factory : mItemFactories) {
                    if (factory instanceof DuplicateCreateFactory) {
                        ((DuplicateCreateFactory) factory).showDupDialog(v.getContext(), new Function1<DuplicateEnum, Unit>() {
                            @Override
                            public Unit invoke(DuplicateEnum duplicateEnum) {
                                mValue.updateDup(duplicateEnum);
                                return null;
                            }
                        });
                        return;
                    }
                }
                return;
            }
            final DuplicateCreateFactory factory = new DuplicateCreateFactory();
            factory.showDupDialog(v.getContext(), new Function1<DuplicateEnum, Unit>() {
                @Override
                public Unit invoke(DuplicateEnum duplicateEnum) {
                    if (duplicateEnum != null) {
                        mValue.updateDup(duplicateEnum);
                        mItemFactories.add(factory);
                        factory.buildView((ViewGroup) getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
                    }
                    return null;
                }
            });
            EventTrackerKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_MORE_REPEAT);
        } else if (v.getId() == R.id.alarm_icon) {
            if (!ObjExKt.isLegalTimestamp(mValue.endTime)) {
                if (mToast != null)
                    mToast.cancel();
                mToast = Toast.makeText(v.getContext(), R.string.joywork_alert_deadline, Toast.LENGTH_SHORT);
                mToast.show();
                return;
            }
            if (mItemFactories.has(AlarmCreateFactory.class)) {
                for (CreateItemFactory factory : mItemFactories) {
                    if (factory instanceof AlarmCreateFactory) {
                        ((AlarmCreateFactory) factory).showAlertDialog(v.getContext());
                        return;
                    }
                }
                return;
            }
            final AlarmCreateFactory factory = new AlarmCreateFactory();
            factory.showAlertDialog(v.getContext(), new JoyWorkCallback<Set<AlertType>>() {
                @Override
                public void invoke(Set<AlertType> alertTypes) {
                    mItemFactories.add(factory);
                    mValue.replaceAlertType(alertTypes);
                    factory.buildView((ViewGroup) getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
                }
            });
        } else if (v.getId() == R.id.priority_icon) {
            if (mItemFactories.has(PriorityCreateFactory.class)) {
                for (CreateItemFactory factory : mItemFactories) {
                    if (factory instanceof PriorityCreateFactory) {
                        ((PriorityCreateFactory) factory).showLevelDialog(v.getContext(), new Function1<Integer, Unit>() {
                            @Override
                            public Unit invoke(Integer integer) {
                                mValue.updatePriority(integer);
                                return null;
                            }
                        });
                        return;
                    }
                }
                return;
            }
            final PriorityCreateFactory factory = new PriorityCreateFactory();
            factory.showLevelDialog(v.getContext(), new Function1<Integer, Unit>() {
                @Override
                public Unit invoke(Integer integer) {
                    if (integer != null) {
                        mValue.updatePriority(integer);
                        mItemFactories.add(factory);
                        factory.buildView((ViewGroup) getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
                    }
                    return null;
                }
            });
        } else if (v.getId() == R.id.attachment_icon) {
            // 附件
            DialogManager.INSTANCE.showFileChoose(v.getContext(), new Function1<JoyWorkAttachmentChoose, Unit>() {
                @Override
                public Unit invoke(JoyWorkAttachmentChoose joyWorkAttachmentChoose) {
                    final Activity activity = getActivity();
                    if (activity == null) {
                        return null;
                    }
                    switch (joyWorkAttachmentChoose) {
                        case TAKE_PHOTO:
                            takePhoto(activity);
                            break;
                        case ALBUM:
                            PermissionHelper.requestPermissions(activity, getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),
                                    getResources().getString(com.jme.common.R.string.me_request_permission_read_storage_gallery),
                                    new RequestPermissionCallback() {
                                        @Override
                                        public void allGranted() {
                                            JdmeRounter.getProvider(GalleryProvider.class).openGallery(requireActivity(), 30, REQUEST_CODE_GALLERY);
                                        }

                                        @Override
                                        public void denied(List<String> deniedList) {
                                            ToastUtils.showToast(R.string.task_operate_fail);
                                        }
                                    }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                            break;
                        case JOY_SPACE:
                            selectShimo(activity);
                            break;
                    }
                    return null;
                }
            });
        } else if (v.getId() == R.id.order_item) {
            selectGroup();
            EventTrackerKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_CREATE_TASK_RELATE_CHECKLIST_BUTTON);
        } else if (v.getId() == R.id.target_item) {
//            selectTarget();
        }
    }

    private void afterCreateSuccess(String result, JoyWorkDetail joyWorkDetail) {
        // 成功后关闭界面，不恢复按钮。防止成功后因来不及关界面导致多次点击
        JoyWorkShortcutDraft.Companion.removeDraft();
        JoyWorkHandler.getInstance().onCreateFinish();
        JoyWorkHandler.getInstance().notifyCallback(mValue.subOwners.size());
//                        successCallback.invoke(result, this @JoyWorkShortcutDialog)
        if (requireActivity() instanceof JoyWorkCreateActivity) {
            ((JoyWorkCreateActivity) requireActivity()).superFinish();
            if (JoyWorkCreateActivity.successRunnable != null) {
                JoyWorkCreateActivity.successRunnable.show(result);
            }
            if (getSuccessCallback() != null) {
                getSuccessCallback().invoke(result, mValue, null);
            }
//            if (mSendIconChecked && ViewUtilsKt.isVisible(mSendContainer) && mSendIcon.isEnabled()) {
//                if (ObjExKt.isLegalString(getSessionId())) {
//                    JoyWorkContentExKt.shareJoyWorkWhenCreate(joyWorkDetail, getTmpData().getTo(), getTmpData().getToApp(), getSessionId());
//                } else {
//                    ShortcutDialogCommon.INSTANCE.sendByChat(mValue.normalOwners, joyWorkDetail);
//                }
//            }
        } else {
            requireActivity().finish();
        }
    }

    private Function1<JoyWorkCreateParams, Unit> handleNetParams() {
        return new Function1<JoyWorkCreateParams, Unit>() {
            @Override
            public Unit invoke(JoyWorkCreateParams joyWorkCreateParams) {
                joyWorkCreateParams.setTitle(mValue.title == null ? "" : mValue.title);
                joyWorkCreateParams.setEndTime(mValue.endTime);
                joyWorkCreateParams.setStartTime(mValue.startTime);
                if (!isSubJoywork()) {
                    joyWorkCreateParams.setImMessage(mSendIconChecked);
                }
                if (mValue.priority != null && mValue.priority != JoyWorkLevel.NO.getValue()) {
                    // 只有紧急时才需要传优化级
                    joyWorkCreateParams.setPriorityType(mValue.priority);
                }
                joyWorkCreateParams.setDes(mValue.des == null ? "" : mValue.des);
                joyWorkCreateParams.setShimo(mValue.shimo);
                joyWorkCreateParams.setResources(mValue.mAtt);
                joyWorkCreateParams.setAlertTypes(mValue.mAlertType);
                joyWorkCreateParams.setBizCode(getBizCode());
                joyWorkCreateParams.setContent(mValue.extra);
                if (getTmpData() != null) {
                    joyWorkCreateParams.setContentOnly(getTmpData().getContent());
                    joyWorkCreateParams.setMobileContent(getTmpData().getMobileContent());
                    if (mItemFactories.has(GroupCreateFactory.class)) {
                        // 个人待办，如果有自己需要传分组 id。值可能没有，网络层会过滤
                        joyWorkCreateParams.setProjectId(getInProjectId());
                        joyWorkCreateParams.setGroupId(mValue.groupId);
                    }
                }
                if(mValue.selId != null){
                    joyWorkCreateParams.setGroupId(mValue.selId);
                }
                if(mValue.projectId != null){
                    joyWorkCreateParams.setProjectId(mValue.projectId);
                }
                if (getDeeplinkCreateParam() != null) {
                    joyWorkCreateParams.setFrom(getDeeplinkCreateParam().toCreateMap());
                }
                joyWorkCreateParams.setDupEnum(mValue.duplicateEnum);
                getViewStrategy().handleNetParams(joyWorkCreateParams, mValue, JoyWorkCreateFragment.this);
                return null;
            }
        };
    }

    /**
     * 是否隐藏分组选择
     */
    boolean shouldHideGroup() {
        if (!isProject()) {
            return true;
        }
        ShortcutDialogTmpData data = getTmpData() == null ? new ShortcutDialogTmpData() : getTmpData();
        return data.getHideGroups() && !mValue.hasSelf();
    }

    void selectGroup() {
        Intent i = new Intent(requireActivity(), SelectProjectTaskGroupActivity.class);
        ArrayList<JoyWorkDetail.Project> ps = new ArrayList<>();
        if (mValue.projects != null) {
            ps.addAll(mValue.projects);
        }
        i.putExtra("linkedProjects", ps);
        startActivityForResult(i, REQ_SEL_ORDER);
    }

    void selectTarget() {
        SelectGoalActivity.Companion.start(requireActivity(), REQ_SEL_TARGET);
    }

    void selectShimo(Activity activity) {
        TaskDetailPresenter.openDocumentChoose(activity);
    }

    private void takePhoto(Activity activity) {
        PermissionHelper.requestPermissions(activity, getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),
                getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                        File file = new File(FileCache.getInstance().getImageCacheFile(), "capture_rn" + System.currentTimeMillis() + ".jpg");
                        Uri mCaptureUri = CategoriesKt.getFileUri(AppBase.getAppContext(), file);
                        mCapturePath = file.getAbsolutePath();
                        intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUri);
                        startActivityForResult(intent, REQUEST_CODE_CAPTURE);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        ToastUtils.showToast(R.string.task_operate_fail);
                    }
                }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 100 && resultCode == Activity.RESULT_OK && data != null) {
            mValue.updateDes(data.getStringExtra(JoyWorkDesActivity.KEY));
        } else if (requestCode == REQUEST_CODE_GALLERY) {
            // 选择相册
            if (resultCode == RESULT_OK) {
                final List<String> pList = MaeAlbum.obtainPathResult(data);
                saveImageFileList(requireActivity(), pList);
            }
        } else if (requestCode == REQUEST_CODE_CAPTURE) {
            // 拍照
            if (resultCode == RESULT_OK) {
                if (mCapturePath == null) {
                    return;
                }
                List<String> pList = new ArrayList<>();
                pList.add(mCapturePath);
                saveImageFileList(requireActivity(), pList);
            }
        } else if (requestCode == RELATION_REQ && resultCode == RESULT_OK) {
            try {
                List<Members> members = (List<Members>) OneTimeDataRepo.INSTANCE.getData();
                mValue.updateMembers(members);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (requestCode == OWNER_REQ && resultCode == RESULT_OK) {
            try {
                ArrayList<JoyWorkUser> members = (ArrayList<JoyWorkUser>) OneTimeDataRepo.INSTANCE.getData();
                mValue.setSubJoyworkOwners(members);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (requestCode == REQ_SEL_ORDER && resultCode == RESULT_OK) {
            JoyWorkDetail.Project p = data.getParcelableExtra("project");
            if (mItemFactories.has(OrderCreateFactory.class)) {
                mValue.addProject(p);
                return;
            }
            OrderCreateFactory factory = new OrderCreateFactory();
            mValue.addProject(p);
            mItemFactories.add(factory);
            factory.buildView((ViewGroup) getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
        } else if (requestCode == REQ_SEL_TARGET && resultCode == RESULT_OK) {
            Parcelable p = SelectGoalActivity.Companion.getResultData(data);
            if (!(p instanceof KR) && !(p instanceof KpiTarget)) {
                return;
            }
            mValue.addGoalOrKr(p);
            if (!mItemFactories.has(TargetCreateFactory.class)) {
                addTargetUI();
            }
        }
    }

    private void addTargetUI() {
//        if (mItemFactories.has(TargetCreateFactory.class)) {
//            return;
//        }
//        TargetCreateFactory factory = new TargetCreateFactory();
//        mItemFactories.add(factory);
//        factory.buildView((ViewGroup) getView().findViewById(R.id.containerParent), mValue, JoyWorkCreateFragment.this, mCreateViewModel);
    }

    private void saveImageFileList(@NonNull final Activity activity, final List<String> pList) {
        if (pList != null && pList.size() > 0) {
            File parent = new File(activity.getExternalCacheDir() + "/rn");
            DialogUtils.showLoadDialog((FragmentActivity) activity, activity.getResources().getString(R.string.me_uploading), new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    if (uploadTask != null) {
                        uploadTask.cancel();
                        ToastUtils.showToast(activity.getResources().getString(R.string.me_cancel_ok));
                    }
                }
            });
            int i = 0;
            final ImageCompressUtils mGallery = ImageCompressUtils.INSTANCE;
            for (String p : pList) {
                final int index = i++;
                final Resources resources = new Resources();
                mGallery.compressAsync(p, new File(parent, System.currentTimeMillis() + ".jpg").getAbsolutePath(), new Function1<String, Unit>() {
                    @Override
                    public Unit invoke(final String s) {
                        Task.Callback<UploadResult> callback = new Task.Callback<UploadResult>() {
                            @Override
                            public void onStart() {
                            }

                            @Override
                            public void onProgressChange(Task.Progress progress) {
                            }

                            @Override
                            public void onPause() {
                            }

                            @Override
                            public void onComplete(UploadResult result) {
                                uploadTask = null;
                                DialogUtils.removeLoadDialog((FragmentActivity) activity);
                                resources.setUrl(result.getFileDownloadUrl());
                                mValue.updateAtt(resources);
                            }

                            @Override
                            public void onFailure(Exception exception) {
                                if (index >= pList.size() - 1) {
                                    DialogUtils.removeLoadDialog((FragmentActivity) activity);
                                    mGallery.reset();
                                    ToastUtils.showToast(R.string.joywork_upload_failure);
                                }
                            }
                        };
                        File file = new File(s);
                        if (file.exists()) {
                            resources.setBizType(1);
                            resources.setName(file.getName());
                            resources.setSize(file.length());
                            Random random = new Random();
                            resources.setBizId(String.valueOf(random.nextLong()));
                            resources.setFileType(FileType.getMimeType(file.getName()));
                            resources.setResourceType(1);
                            uploadTask = FileUploadManager.getDefault(activity)
                                    .create(s)
                                    .setAppKey(ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.JOYWORK))
                                    .setNeedAuthN(1)
                                    .setCallback(callback)
                                    .start();
                        }
                        return null;
                    }
                });
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        for (CreateItemFactory factory : mItemFactories) {
            factory.removeFromUI();
            factory.release();
        }
        if (mToast != null) {
            mToast.cancel();
        }
    }

    // 从网页上选择完文件后
    void chooseFileFromJS(final Map<String, Object> params) {
        Gson gson = new Gson();
        String paramsJson = gson.toJson(params.get("fileList"));
        List<Documents> list = gson.fromJson(paramsJson, new TypeToken<List<Documents>>() {
        }.getType());
        mValue.updateShimo(list);
    }

    @NonNull
    @Override
    public CreateItemFactoryList getMItemFactories() {
        return mItemFactories;
    }

    @NonNull
    @Override
    public Fragment getFragment() {
        return this;
    }

    @Override
    public boolean moreOwners() {
        return getViewStrategy().isSubOwners();
    }

}