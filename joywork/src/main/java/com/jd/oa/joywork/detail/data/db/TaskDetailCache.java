package com.jd.oa.joywork.detail.data.db;

import androidx.lifecycle.MutableLiveData;

import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class TaskDetailCache {
    private static final Map<String, MutableLiveData<TaskDetailEntity>> cacheMap = new ConcurrentHashMap<>();

    public static MutableLiveData<TaskDetailEntity> getDetail(String taskId) {
        return cacheMap.get(taskId);
    }

    public static void putDetail(String taskId, MutableLiveData<TaskDetailEntity> taskDetailEntity) {
        cacheMap.put(taskId, taskDetailEntity);
    }
}
