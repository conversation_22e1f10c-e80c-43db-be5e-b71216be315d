package com.jd.oa.joywork.detail.viewmodel;

import static com.jd.oa.fragment.utils.FileType.TYPE_PDF;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.databinding.BindingAdapter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.fragment.utils.FileType;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.FinishAction;
import com.jd.oa.joywork.JoyWorkDetailParam;
import com.jd.oa.joywork.JoyWorkExKt;
import com.jd.oa.joywork.JoyWorkLevel;
import com.jd.oa.joywork.JoyWorkMediator;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.RiskEnum;
import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork.create.CreateItemFactoryList;
import com.jd.oa.joywork.detail.JoyWorkContentExKt;
import com.jd.oa.joywork.detail.SubJoyWorkEx;
import com.jd.oa.joywork.detail.TaskDetailFinishHelperKt;
import com.jd.oa.joywork.detail.data.db.UpdateReporter;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.detail.data.entity.Owner;
import com.jd.oa.joywork.detail.data.entity.ParentTask;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity;
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup;
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem;
import com.jd.oa.joywork.detail.data.entity.custom.EmptyCustomFieldItem;
import com.jd.oa.joywork.detail.ui.DetailAlarmFactory;
import com.jd.oa.joywork.detail.ui.DetailDeadlineCreateFactory;
import com.jd.oa.joywork.detail.ui.DetailDupCreateFactory;
import com.jd.oa.joywork.detail.ui.DetailLaunchCreateFactory;
import com.jd.oa.joywork.detail.ui.DetailMoreFactory;
import com.jd.oa.joywork.detail.ui.DetailOrderCreateFactory;
import com.jd.oa.joywork.detail.ui.DetailOwnerFactory;
import com.jd.oa.joywork.detail.ui.DetailPriorityCreateFactory;
import com.jd.oa.joywork.detail.ui.DetailRelationFactory;
import com.jd.oa.joywork.detail.ui.DetailSysOrderCreateFactory;
import com.jd.oa.joywork.detail.ui.DetailTargetCreateFactory;
import com.jd.oa.joywork.detail.ui.NotifyLinearLayout;
import com.jd.oa.joywork.detail.ui.TaskDetailFragment;
import com.jd.oa.joywork.detail.ui.WebViewStatistics;
import com.jd.oa.joywork.filter.JoyWorkViewItem;
import com.jd.oa.joywork.utils.HyperLinkHelperKt;
import com.jd.oa.joywork.utils.JoyWorkNetConfig;
import com.jd.oa.joywork.view.ExpandLinearLayout;
import com.jd.oa.joywork2.backend.JoyWorkSource;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DrawableEx;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.TextHelper;
import com.jd.oa.utils.ViewUtilsKt;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import kotlin.Unit;
import kotlin.jvm.functions.Function2;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class TaskDetailBindingAdapter {
    private static final String API_JOYSPACE_ICONS_PRE = "https://apijoyspace-pre2.jd.com";
    private static final String API_JOYSPACE_ICONS_OFFICIAL = "https://apijoyspace.jd.com";
    private static final String API_JOY_SPACE_JD_ICONS = "/v1/common/icons";

    @BindingAdapter({"imageUrl", "error"})
    public static void loadImage(ImageView view, String url, Drawable error) {
        RequestOptions options = RequestOptions.bitmapTransform(new CircleCrop()).error(error).placeholder(error);
        Glide.with(view).load(url).apply(options).into(view);
    }

    @BindingAdapter({"loadImageUrl"})
    public static void loadImage(ImageView view, String url) {
        Glide.with(view).load(url).into(view);
    }

    @BindingAdapter({"docIcon"})
    public static void loadDocIcon(ImageView view, Documents documents) {
        RequestOptions options = RequestOptions.centerInsideTransform().error(null).placeholder(null);
        boolean isPre = GatewayNetEnvironment.getCurrentEnv().isPre();
        String prefix = isPre ? API_JOYSPACE_ICONS_PRE : API_JOYSPACE_ICONS_OFFICIAL;
        Glide.with(view).load(prefix + API_JOY_SPACE_JD_ICONS +
                "?title=" + documents.getTitle() + "&" +
                "pageType=" + documents.getPageType() + "&type=&client=web").apply(options).into(view);
    }

    @BindingAdapter({"resIcon"})
    public static void loadResIcon(final ImageView view, final Resources resources) {
        view.post(new Runnable() {
            @Override
            public void run() {
                int type = FileType.getTypeInt(resources.getName());
                if (FileType.isImage(type) || FileType.isGif(type) || FileType.isFlash(type) || FileType.isJpg(type)) {
                    view.setImageResource(R.drawable.file_imgs);
                } else if (FileType.isAudio(type) || FileType.isPlayList(type)) {
                    view.setImageResource(R.drawable.file_musics);
                } else if (FileType.isPowerPoint(type)) {
                    view.setImageResource(R.drawable.file_ppt);
                } else if (FileType.isExcel(type)) {
                    view.setImageResource(R.drawable.file_excel);
                } else if (FileType.isWord(type) || FileType.isTxt(type)) {
                    view.setImageResource(R.drawable.file_word);
                } else if (type == TYPE_PDF) {
                    view.setImageResource(R.drawable.file_pdf);
                } else if (FileType.isVideo(type)) {
                    view.setImageResource(R.drawable.file_videos);
                } else if (FileType.isZip(type)) {
                    view.setImageResource(R.drawable.file_compression);
                } else if (type == 0) {
                    view.setImageResource(R.drawable.file_unknown);
                } else {
                    view.setImageResource(R.drawable.file_files);
                }
            }
        });
    }

    @BindingAdapter({"launchTimeVisible"})
    public static void launchTimeVisible(final LinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment fragment = getOwnerFragment(view);
                if (fragment == null) {
                    return;
                }
                CreateItemFactoryList factories = fragment.getMItemFactories();
                if (entity == null || entity.getJoyWorkDetail() == null
                        || !JoyWorkContentExKt.selfIsOwner(entity.getJoyWorkDetail())
                        || !ObjExKt.isLegalTimestamp(entity.getJoyWorkDetail().getPlanTime())) {
                    view.removeAllViews();
                    view.setVisibility(View.GONE);
                    factories.removeByClass(DetailLaunchCreateFactory.class);
                } else {
                    view.setVisibility(View.VISIBLE);
                    if (factories.has(DetailLaunchCreateFactory.class)) {
                        fragment.mValue.updateLaunchTime(entity.getJoyWorkDetail().getPlanTime());
                    } else {
                        view.removeAllViews();
                        DetailLaunchCreateFactory launchCreateFactory = new DetailLaunchCreateFactory(fragment);
                        fragment.mValue.updateLaunchTime(entity.getJoyWorkDetail().getPlanTime());
                        launchCreateFactory.buildView(view, fragment.mValue, fragment, fragment.mValue.mCreateViewModel);
                        factories.add(launchCreateFactory);
                    }
                }
            }
        });
    }

    @BindingAdapter({"linkOrder"})
    public static void linkOrder(final LinearLayout view, final JoyWorkDetail entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment fragment = getOwnerFragment(view);
                if (fragment == null) {
                    return;
                }
                CreateItemFactoryList factories = fragment.getMItemFactories();
                view.setVisibility(View.VISIBLE);

                List<JoyWorkDetail.Project> projects;
                if (entity == null || entity.getProjects() == null) {
                    projects = new ArrayList<>();
                } else {
                    projects = entity.getProjects();
                }
                if (factories.has(DetailOrderCreateFactory.class)) {
                    fragment.mValue.replaceProjects(projects);
                } else {
                    view.removeAllViews();
                    DetailOrderCreateFactory launchCreateFactory = new DetailOrderCreateFactory(fragment);
                    fragment.mValue.replaceProjects(projects);
                    launchCreateFactory.buildView(view, fragment.mValue, fragment, fragment.mValue.mCreateViewModel);
                    factories.add(launchCreateFactory);
                }
            }
        });
    }


    @BindingAdapter({"linkSysOrder"})
    public static void linkSysOrder(final LinearLayout view, final JoyWorkDetail entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment fragment = getOwnerFragment(view);
                if (fragment == null) {
                    return;
                }
                CreateItemFactoryList factories = fragment.getMItemFactories();
                if (entity == null || entity.getSysSetProjects() == null || entity.getSysSetProjects().isEmpty()) {
                    view.setVisibility(View.GONE);
                    view.removeAllViews();
                    factories.removeByClass(DetailSysOrderCreateFactory.class);
                } else {
                    view.setVisibility(View.VISIBLE);
                    if (factories.has(DetailSysOrderCreateFactory.class)) {
                        fragment.mValue.replaceSysProjects(entity.getSysSetProjects());
                    } else {
                        view.removeAllViews();
                        DetailSysOrderCreateFactory launchCreateFactory = new DetailSysOrderCreateFactory(fragment);
                        fragment.mValue.replaceSysProjects(entity.getSysSetProjects());
                        fragment.mValue.sysProjectIcon = entity.sysProjectIcon;
                        launchCreateFactory.buildView(view, fragment.mValue, fragment, fragment.mValue.mCreateViewModel);
                        factories.add(launchCreateFactory);
                    }
                }
            }
        });
    }

    @BindingAdapter({"linkTarget"})
    public static void linkTarget(final LinearLayout view, final JoyWorkDetail entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                view.setVisibility(View.GONE);
            }
        });
    }

    @BindingAdapter({"linkTargetOld"})
    public static void linkTargetOld(final LinearLayout view, final JoyWorkDetail entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment fragment = getOwnerFragment(view);
                if (fragment == null) {
                    return;
                }
                CreateItemFactoryList factories = fragment.getMItemFactories();
                if (entity == null || (!ObjExKt.isLegalList(entity.goals) && !ObjExKt.isLegalList(entity.krs))) {
                    view.setVisibility(View.GONE);
                    view.removeAllViews();
                    factories.removeByClass(DetailTargetCreateFactory.class);
                } else {
                    view.setVisibility(View.VISIBLE);
                    if (factories.has(DetailTargetCreateFactory.class)) {
                        fragment.mValue.replaceTargets(entity.goals);
                        fragment.mValue.replaceKrs(entity.krs);
                    } else {
                        view.removeAllViews();
                        DetailTargetCreateFactory launchCreateFactory = new DetailTargetCreateFactory(fragment);
                        fragment.mValue.replaceTargets(entity.goals);
                        fragment.mValue.replaceKrs(entity.krs);
                        launchCreateFactory.buildView(view, fragment.mValue, fragment, fragment.mValue.mCreateViewModel);
                        factories.add(launchCreateFactory);
                    }
                }
            }
        });
    }

    @BindingAdapter({"relationList"})
    public static void relationList(final LinearLayout view, final JoyWorkDetail entity) {
        if (entity == null)
            return;
        view.post(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment fragment = getOwnerFragment(view);
                if (fragment == null)
                    return;
                CreateItemFactoryList factories = fragment.getMItemFactories();
                view.setVisibility(View.VISIBLE);
                if (factories.has(DetailRelationFactory.class)) {
                    fragment.mValue.updateMembers(entity.getExecutors());
                } else {
                    view.removeAllViews();
                    DetailRelationFactory launchCreateFactory = new DetailRelationFactory(fragment);
                    fragment.mValue.updateMembers(entity.getExecutors());
                    launchCreateFactory.buildView(view, fragment.mValue, fragment, fragment.mValue.mCreateViewModel);
                    factories.add(launchCreateFactory);
                }
            }
        });
    }

    @BindingAdapter({"edit"})
    public static void editSwitch(final EditText view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                boolean edit = false;
                if (entity != null && entity.getJoyWorkDetail() != null) {
                    ViewUtilsKt.strikethrough(view, TaskStatusEnum.FINISH.isFinish(entity.getJoyWorkDetail().getTaskStatus()));
                    edit = entity.getTitleEdit();
                } else {
                    ViewUtilsKt.strikethrough(view, false);
                }

                if (edit) {
                    view.setFocusable(true);
                    view.setFocusableInTouchMode(true);
//                    view.setInputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE);
                    view.requestFocus();
                } else {
                    view.setFocusable(false);
                    view.setFocusableInTouchMode(false);
//                    view.setInputType(InputType.TYPE_NULL);
                    view.clearFocus();
                }
            }
        });
    }

    @BindingAdapter({"priorityType"})
    public static void priorityType(final LinearLayout view, final Integer pt) {
        final TaskDetailFragment taskDetailFragment = getOwnerFragment(view);
        if (taskDetailFragment == null || pt == null)
            return;
        view.post(new Runnable() {
            @Override
            public void run() {
                JoyWorkLevel level = JoyWorkLevel.NO.getLevel(pt);
                // 【getLevel】 may modify the value of pt
                int priorityType = level.getValue();
                CreateItemFactoryList factories = taskDetailFragment.getMItemFactories();
//                if (level == JoyWorkLevel.NO) {
//                    view.setVisibility(View.GONE);
//                    view.removeAllViews();
//                    factories.removeByClass(DetailPriorityCreateFactory.class);
//                } else {
                view.setVisibility(View.VISIBLE);
                if (factories.has(DetailPriorityCreateFactory.class)) {
                    taskDetailFragment.mValue.updatePriority(priorityType);
                } else {
                    view.removeAllViews();
                    DetailPriorityCreateFactory factory = new DetailPriorityCreateFactory();
                    taskDetailFragment.mValue.updatePriority(priorityType);
                    factory.buildView(view, taskDetailFragment.mValue, taskDetailFragment, taskDetailFragment.mValue.mCreateViewModel);
                    factories.add(factory);
                }
//                }
            }
        });
    }

    @BindingAdapter({"remindMeVisible"})
    public static void remindMeVisible(final LinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                final TaskDetailFragment taskDetailFragment = getOwnerFragment(view);
                if (taskDetailFragment == null || entity == null || entity.getJoyWorkDetail() == null)
                    return;
                JoyWorkDetail detail = entity.getJoyWorkDetail();
                if (!JoyWorkExKt.hasLegalAlertType(detail.getAlertTypes())) {
                    view.setVisibility(View.GONE);
                    view.removeAllViews();
                    taskDetailFragment.getMItemFactories().removeByClass(DetailAlarmFactory.class);
                } else {
                    // 无权限，但别人设置过截止前提醒，可看不可操作
                    view.setVisibility(View.VISIBLE);
                    CreateItemFactoryList factories = taskDetailFragment.getMItemFactories();
                    if (factories.has(DetailAlarmFactory.class)) {
                        taskDetailFragment.mValue.replaceAlertType(detail.getAlertTypes());
//                        taskDetailFragment.mValue.updateAlertTime(remindTime);
                    } else {
                        view.removeAllViews();
                        DetailAlarmFactory factory = new DetailAlarmFactory(taskDetailFragment);
                        taskDetailFragment.mValue.replaceAlertType(detail.getAlertTypes());
                        factory.buildView(view, taskDetailFragment.mValue, taskDetailFragment, taskDetailFragment.mValue.mCreateViewModel);
                        factories.add(factory);
                    }
                }
            }
        });
    }

    @BindingAdapter({"canUpdateFiles"})
    public static void canUpdateFiles(final View view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (!JoyWorkContentExKt.canUpdateFiles(entity == null ? null : entity.getJoyWorkDetail())) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    @BindingAdapter({"joyworkDes"})
    public static void joyworkDes(final TextView view, final String joyworkDes) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (ObjExKt.isLegalString(joyworkDes)) {
                    TaskDetailFragment fragment = getOwnerFragment(view);
                    if (fragment == null) {
                        return;
                    }
                    HyperLinkHelperKt.setUrlLinkText(view, joyworkDes, fragment.getHyperLinkClick());
//                    view.setText(joyworkDes);
                    ObjExKt.setTextSizeRes(view, R.dimen.joywork_icon_mid);
                } else {
                    view.setText("");
                    ObjExKt.setTextSizeRes(view, R.dimen.joywork_title_sub);
                }
            }
        });
    }

    @BindingAdapter({"risk"})
    public static void risk(final LinearLayout view, final Integer riskStatus) {
        view.setVisibility(View.GONE);
//        final TaskDetailFragment taskDetailFragment = getOwnerFragment(view);
//        if (taskDetailFragment == null)
//            return;
//        view.post(new Runnable() {
//            @Override
//            public void run() {
//                if (taskDetailFragment.hasItemFactory(DetailRiskItemFactory.class)) {
//                    taskDetailFragment.mValue.updateRisk(riskStatus);
//                } else {
//                    view.removeAllViews();
//                    DetailRiskItemFactory itemFactory = new DetailRiskItemFactory();
//                    taskDetailFragment.mValue.riskStatus = riskStatus;
//                    View v = itemFactory.buildView(view, taskDetailFragment.mValue, taskDetailFragment, taskDetailFragment.mValue.mCreateViewModel);
//                    taskDetailFragment.itemFactories.add(itemFactory);
//                }
//            }
//        });
    }

    @BindingAdapter({"endTime"})
    public static void endTime(final LinearLayout view, final JoyWorkDetail content) {
        final TaskDetailFragment taskDetailFragment = getOwnerFragment(view);
        if (taskDetailFragment == null || content == null)
            return;
        view.post(new Runnable() {
            @Override
            public void run() {
                // 点击事件在 adapter 中实现，由它调用接口如果更新成功后会回调到该方法
                // 此时 if 判断成立，然后由 value 更新时间，再影响到 DetailDeadlineCreateFactory 中的 UI
                CreateItemFactoryList factories = taskDetailFragment.getMItemFactories();
                if (factories.has(DetailDeadlineCreateFactory.class)) {
                    taskDetailFragment.mValue.updateTime(content.getStartTime(), content.getEndTime());
                } else {
                    view.removeAllViews();
                    DetailDeadlineCreateFactory factory = new DetailDeadlineCreateFactory(taskDetailFragment);
                    taskDetailFragment.mValue.updateTime(content.getStartTime(), content.getEndTime());
                    factory.buildView(view, taskDetailFragment.mValue, taskDetailFragment, taskDetailFragment.mValue.mCreateViewModel);
                    factories.add(factory);
                }
            }
        });
    }

    public static boolean isEllipsize(TextView tv, String text, int w) {
        float width = TextHelper.getSingleLineTextWidth(text, (int) tv.getTextSize());
        return width > w;
    }

    @BindingAdapter({"moreVisible"})
    public static void moreVisible(final IconFontView view, final TaskDetailEntity entity) {
        //2022年05月26日：当前版本因为操作里可查看历史记录，不需要判断判断，所以肯定会有选项，就肯定不需要隐藏
        view.setVisibility(View.VISIBLE);
    }

    @BindingAdapter({"urgeVisible"})
    public static void urgeVisible(final IconFontView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                boolean b = entity != null && JoyWorkContentExKt.isRemindable(entity.getJoyWorkDetail());
                if (b) {
                    view.setVisibility(View.VISIBLE);
                } else {
                    view.setVisibility(View.GONE);
                }
            }
        });
    }

    @BindingAdapter({"focusVisible"})
    public static void focusVisible(final IconFontView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                boolean b = JoyWorkContentExKt.selfIsExecutor(entity == null ? null : entity.getJoyWorkDetail());
                if (b) {
                    view.setTextColor(Color.parseColor("#FFCF33"));
                    view.setText(R.string.icon_padding_followhear);
                } else {
                    view.setTextColor(Color.parseColor("#333333"));
                    view.setText(R.string.icon_padding_followheart);
                }
            }
        });
    }

    @BindingAdapter({"sourceName"})
    public static void sourceName(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (entity == null || entity.getJoyWorkDetail() == null) {
                    view.setVisibility(View.GONE);
                    return;
                }
                JoyWorkDetail detail = entity.getJoyWorkDetail();

                String title = null;
                String sourceName = null;
                // 咚咚与石墨文档的待办，单独处理
                if (!TextUtils.isEmpty(detail.tripartiteName) && (
                        Objects.equals(detail.getBizCode(), JoyWorkSource.HUIJI.getBizCode()) ||
                                Objects.equals(detail.getBizCode(), JoyWorkSource.IM.getBizCode()) ||
                                Objects.equals(detail.getBizCode(), JoyWorkSource.JOYSPACE.getBizCode()) ||
                                Objects.equals(detail.getBizCode(), JoyWorkSource.MEETING.getBizCode()) ||
                                Objects.equals(detail.getBizCode(), JoyWorkSource.MEETING2.getBizCode()))
                ) {
                    sourceName = detail.subTripartiteName;
                    // 稍后处理自动转为的待办，来源标题需要展示一级标题（稍后处理的来源属于 IM）
                    if (!ObjExKt.isLegalString(sourceName) ||
                            Objects.equals(detail.getBizCode(), JoyWorkSource.IM.getBizCode())) {
                        sourceName = detail.tripartiteName;
                    }
                    if (!ObjExKt.isLegalString(sourceName)) {
                        view.setVisibility(View.GONE);
                        return;
                    }
                    view.setVisibility(View.VISIBLE);
                    if (Objects.equals(detail.getBizCode(), JoyWorkSource.IM.getBizCode())) {
                        title = view.getContext().getString(R.string.joywork_from_chat, sourceName);
                    } else if (Objects.equals(detail.getBizCode(), JoyWorkSource.HUIJI.getBizCode())) {
                        title = view.getContext().getString(R.string.joywork_from_huiji, sourceName);
                    } else if (Objects.equals(detail.getBizCode(), JoyWorkSource.JOYSPACE.getBizCode())) {
                        title = view.getContext().getString(R.string.joywork_from_shimo, sourceName);
                    } else if (Objects.equals(detail.getBizCode(), JoyWorkSource.MEETING.getBizCode()) || Objects.equals(detail.getBizCode(), JoyWorkSource.MEETING2.getBizCode())) {
                        title = view.getContext().getString(R.string.joywork_from_meeting, sourceName);
                    }
                } else {
                    sourceName = detail.sourceDesc;
                    if (!ObjExKt.isLegalString(sourceName)) {
                        sourceName = detail.getSourceName();
                    }
                    view.setVisibility(View.VISIBLE);
                    title = view.getContext().getString(R.string.me_joywork_from, sourceName);
                }
                if (TextUtils.isEmpty(title) || TextUtils.isEmpty(sourceName)) {
                    view.setVisibility(View.GONE);
                    return;
                }
                SpannableString spannableString = new SpannableString(title);
                spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#4C7CFF")), title.length() - sourceName.length(), spannableString.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                view.setText(spannableString);
                view.setTag(entity);
                view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        jumpToSource(v.getContext(), (TaskDetailEntity) v.getTag());
                    }
                });
            }
        });
    }

    public static void jumpToSource(Context context, TaskDetailEntity entity) {
        if (ObjExKt.isLegalString(entity.getJoyWorkDetail().getMobileContent())) {
            if (JoyWorkSource.IM.getBizCode().equals(entity.getJoyWorkDetail().getBizCode())) {
                // 跳咚咚
                try {
                    JSONObject jsonObject = new JSONObject(entity.getJoyWorkDetail().getMobileContent());
                    String sessionKey = jsonObject.getString("sessionId");
                    String to = jsonObject.optString("to");
                    int sessionType = jsonObject.optInt("sessionType");
                    String msgId = jsonObject.optString("msgId");
                    long mid = (long) jsonObject.optDouble("mid");
                    long timestamp = (long) jsonObject.optDouble("timestamp");
                    String content = null;
                    if (jsonObject.has("content")) {
                        content = jsonObject.getJSONObject("content").optString("content");
                    }
                    String checkExist = jsonObject.optString("checkExist");
                    boolean check = "1".equals(checkExist);
                    String toApp = jsonObject.optString("toApp", "");
                    ImDdService ddService = AppJoint.service(ImDdService.class);
                    if (ddService != null) {
                        String appId = ddService.safeAppId(sessionType, toApp);
                        ddService.goChatActivity(context, sessionKey, to, appId, msgId, mid, content, timestamp, sessionType, check);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else {// 打开 deeplink
                String deepLink;
                if (!TextUtils.isEmpty(entity.getJoyWorkDetail().getSubMobileContent())) {
                    deepLink = entity.getJoyWorkDetail().getSubMobileContent();
                } else {
                    deepLink = entity.getJoyWorkDetail().getMobileContent();
                }
                ObjExKt.openDeeplink(deepLink);
            }
        } else {
            if (JoyWorkSource.IM.getBizCode().equals(entity.getJoyWorkDetail().getBizCode())) {
                // empty
            } else {
                JoyWorkViewItem.INSTANCE.showThirdDialog(entity.getJoyWorkDetail().getMobileContent(), entity.getJoyWorkDetail().getSourceName(), context, null);
            }
        }
    }

    public static void jumpToSource(Context context, String mobileContent, String bizCode, String sourceName) {
        if (ObjExKt.isLegalString(mobileContent)) {
            if (JoyWorkSource.IM.getBizCode().equals(bizCode)) {
                // 跳咚咚
                try {
                    JSONObject jsonObject = new JSONObject(mobileContent);
                    String sessionKey = jsonObject.getString("sessionId");
                    String to = jsonObject.optString("to");
                    int sessionType = jsonObject.optInt("sessionType");
                    String msgId = jsonObject.optString("msgId");
                    long mid = (long) jsonObject.optDouble("mid");
                    long timestamp = (long) jsonObject.optDouble("timestamp");
                    String content = null;
                    if (jsonObject.has("content")) {
                        content = jsonObject.getJSONObject("content").optString("content");
                    }
                    String checkExist = jsonObject.optString("checkExist");
                    boolean check = "1".equals(checkExist);
                    String toApp = jsonObject.optString("toApp", "");
                    ImDdService ddService = AppJoint.service(ImDdService.class);
                    if (ddService != null) {
                        String appId = ddService.safeAppId(sessionType, toApp);
                        ddService.goChatActivity(context, sessionKey, to, appId, msgId, mid, content, timestamp, sessionType, check);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else {// 打开 deeplink
                ObjExKt.openDeeplink(mobileContent);
            }
        } else {
            if (JoyWorkSource.IM.getBizCode().equals(bizCode)) {
                // empty
            } else {
                JoyWorkViewItem.INSTANCE.showThirdDialog(mobileContent, sourceName, context, null);
            }
        }
    }

    @BindingAdapter({"taskId"})
    public static void loadComment(final JMEWebview webView, final String taskId) {
        if (taskId == null) {
            return;
        }
        boolean isPre = GatewayNetEnvironment.getCurrentEnv().isPre();
        final String url = JoyWorkNetConfig.INSTANCE.getJoyWorkHost() + "/operation-record?appName=jme&taskId=" + taskId
                + "&lang=" + LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext());
        if (!url.equals(webView.getUrl()) || webView.getVisibility() != View.VISIBLE) {
            webView.post(new Runnable() {
                @Override
                public void run() {
                    WebViewStatistics.INSTANCE.start();
                    webView.loadUrl(url);
                }
            });
        }
    }

    @BindingAdapter({"parentTask"})
    public static void parentTask(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().getParentTask() == null || !ObjExKt.isLegalString(entity.getJoyWorkDetail().getParentTask().getTitle())) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    String sourceName = entity.getJoyWorkDetail().getParentTask().getTitle();
                    String title = view.getResources().getString(R.string.joywork_parent_title, sourceName);
                    SpannableString spannableString = new SpannableString(title);
                    spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#4C7CFF")), title.length() - sourceName.length(), spannableString.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                    view.setText(spannableString);
                    view.setBackground(DrawableEx.INSTANCE.roundSolidRect(Color.parseColor("#FFEDF1FF"), CommonUtils.dp2FloatPx(4)));
                    view.setTag(entity);
                    view.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            TaskDetailEntity entity = (TaskDetailEntity) v.getTag();
                            ParentTask parentTask = entity.getJoyWorkDetail().getParentTask();
                            JoyWorkDetailParam param = new JoyWorkDetailParam(parentTask.getTitle(), parentTask.getTaskId(), parentTask.getProjectId());
                            param.setReqCode(TaskDetailPresenter.PARENT_TASK_REQ);
                            param.setObj(UpdateReporter.INSTANCE.getParcel());
                            JoyWorkMediator.Companion.goDetail(view.getContext(), param);
                        }
                    });
                }
            }
        });
    }

    @BindingAdapter({"mergeTask"})
    public static void mergeTask(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().mergeInfo == null || !ObjExKt.isLegalString(entity.getJoyWorkDetail().mergeInfo.title)) {
//                    view.setVisibility(View.GONE);
                } else {
                    view.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
                    view.setText(entity.getJoyWorkDetail().mergeInfo.title);
                }
            }
        });
    }

    @BindingAdapter({"riskMsg"})
    public static void riskMsg(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (shouldHideRiskHeader(entity)) {
                    view.setVisibility(View.GONE);
                } else {
                    RiskEnum riskEnum = RiskEnum.Companion.valueByCode(entity.getJoyWorkDetail().riskStatus);
                    String str = entity.getJoyWorkDetail().riskContent;
                    view.setVisibility(View.VISIBLE);
                    if (riskEnum == RiskEnum.RISK) {
                        str = view.getResources().getString(R.string.jowork_risk_detail_prefix, str);
                    } else if (riskEnum == RiskEnum.PROBLEM) {
                        str = view.getResources().getString(R.string.jowork_problem_detail_prefix, str);
                    }
                    view.setText(str);
                }
            }
        });
    }

    @BindingAdapter({"riskMsgIcon"})
    public static void riskMsgIcon(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (shouldHideRiskHeader(entity)) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    RiskEnum riskEnum = RiskEnum.Companion.valueByCode(entity.getJoyWorkDetail().riskStatus);
                    view.setTextColor(riskEnum.tipsIconColor());
                }
            }
        });
    }

    @BindingAdapter({"riskMsgDiscuss"})
    public static void riskMsgDiscuss(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (shouldHideRiskHeader(entity) || !JoyWorkContentExKt.canComments(entity.getJoyWorkDetail())) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    RiskEnum riskEnum = RiskEnum.Companion.valueByCode(entity.getJoyWorkDetail().riskStatus);
                    view.setBackground(DrawableEx.INSTANCE.roundStrokeRect(riskEnum.tipsIconColor(), CommonUtils.dp2px(1), CommonUtils.dp2px(4)));
                    view.setTextColor(riskEnum.tipsIconColor());
                    maxWidth(view);
                }
            }
        });
    }

    @BindingAdapter({"riskMsgUpdate"})
    public static void riskMsgUpdate(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (shouldHideRiskHeader(entity) || !JoyWorkContentExKt.canUpdateRisk(entity.getJoyWorkDetail())) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    RiskEnum riskEnum = RiskEnum.Companion.valueByCode(entity.getJoyWorkDetail().riskStatus);
                    view.setBackground(DrawableEx.INSTANCE.roundStrokeRect(riskEnum.tipsIconColor(), CommonUtils.dp2px(1), CommonUtils.dp2px(4)));
                    view.setTextColor(riskEnum.tipsIconColor());
                    maxWidth(view);
                }
            }
        });
    }

    public static void maxWidth(View view) {
        if (view.getVisibility() != View.VISIBLE) {
            return;
        }
        if (view.getParent() == null || !(view.getParent() instanceof View)) {
            return;
        }
        View parent = (View) view.getParent();
        int width = parent.getWidth();
        if (width <= 0)
            return;
        ViewGroup.LayoutParams params = view.getLayoutParams();
        if (params == null)
            return;
        width = width - CommonUtils.dp2px(13) * 2;
        params.width = Math.min(width / 3, CommonUtils.dp2px(96));
        Log.e("TAG", " width = " + params.width);
        view.setLayoutParams(params);
    }

    @BindingAdapter({"riskMsgSolve"})
    public static void riskMsgSolve(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (shouldHideRiskHeader(entity) || !JoyWorkContentExKt.canUpdateRisk(entity.getJoyWorkDetail())) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    RiskEnum riskEnum = RiskEnum.Companion.valueByCode(entity.getJoyWorkDetail().riskStatus);
                    view.setBackground(DrawableEx.INSTANCE.roundStrokeRect(riskEnum.tipsIconColor(), CommonUtils.dp2px(1), CommonUtils.dp2px(4)));
                    view.setTextColor(riskEnum.tipsIconColor());
                    maxWidth(view);
                }
            }
        });
    }

    @BindingAdapter({"riskMsgParent"})
    public static void riskMsgParent(final ExpandLinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (shouldHideRiskHeader(entity)) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    view.reInit();
                    view.setHandleAction(new Function2<View, Boolean, Unit>() {
                        @Override
                        public Unit invoke(View view, Boolean isExpand) {
                            if (view instanceof IconFontView) {
                                ((IconFontView) view).setText(isExpand ? R.string.icon_direction_up : R.string.icon_direction_down);
                            }
                            return null;
                        }
                    });
                }
            }
        });
    }

    @BindingAdapter({"riskMsgContainer"})
    public static void riskMsgContainer(final LinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (shouldHideRiskHeader(entity)) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    RiskEnum riskEnum = RiskEnum.Companion.valueByCode(entity.getJoyWorkDetail().riskStatus);
                    view.setBackgroundColor(riskEnum.tipsBgColor());
                }
            }
        });
    }

    private static boolean shouldHideRiskHeader(final TaskDetailEntity entity) {
        return entity == null || entity.getJoyWorkDetail() == null
                || entity.getJoyWorkDetail().riskStatus == null
                || entity.getJoyWorkDetail().riskStatus == RiskEnum.NO_SET.getCode()
                || entity.getJoyWorkDetail().riskStatus == RiskEnum.NORMAL.getCode()
                || !ObjExKt.isLegalString(entity.getJoyWorkDetail().riskContent);
    }

    @BindingAdapter({"mergeTaskParent"})
    public static void mergeTaskParent(final View view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().mergeInfo == null || !ObjExKt.isLegalString(entity.getJoyWorkDetail().mergeInfo.title)) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    @BindingAdapter({"ownerUI"})
    public static void ownerUI(final LinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment fragment = getOwnerFragment(view);
                if (fragment != null) {
                    CreateItemFactoryList factories = fragment.getMItemFactories();
                    if (factories.has(DetailOwnerFactory.class)) {
                        ArrayList<JoyWorkUser> us = JoyWorkContentExKt.owners2Users(entity == null ? null : entity.getJoyWorkDetail());
                        fragment.mValue.setNormalOwners(us);
                    } else {
                        DetailOwnerFactory ownerFactory = new DetailOwnerFactory(fragment);
                        ArrayList<JoyWorkUser> us = JoyWorkContentExKt.owners2Users(entity == null ? null : entity.getJoyWorkDetail());
                        fragment.mValue.setNormalOwners(us);
                        ownerFactory.buildView(view, fragment.mValue, fragment, fragment.mValue.mCreateViewModel);
                        factories.add(ownerFactory);
                    }
                }
            }
        });
    }


    @BindingAdapter({"addDocVisible"})
    public static void addDocVisible(final View view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (JoyWorkContentExKt.canUpdateFiles(entity == null ? null : entity.getJoyWorkDetail())) {
//                    view.setVisibility(View.VISIBLE);
                    // 将 xml 中逻辑移动到代码中
                    if (entity != null && entity.getJoyWorkDetail() != null && entity.getJoyWorkDetail().getDocuments() != null && entity.getJoyWorkDetail().getDocuments().isEmpty()) {
                        view.setVisibility(View.VISIBLE);
                    } else {
                        view.setVisibility(View.GONE);
                    }
                } else {
                    //没有上传权限就隐藏
                    view.setVisibility(View.GONE);
                }
            }
        });
    }

    @BindingAdapter({"addDocVisible3"})
    public static void addDocVisible3(final View view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (JoyWorkContentExKt.canUpdateFiles(entity == null ? null : entity.getJoyWorkDetail())) {
                    // 将 xml 中逻辑移动到代码中
                    // 有权限，并且文档列表不为空时显示
                    if (entity != null && entity.getJoyWorkDetail() != null && entity.getJoyWorkDetail().getDocuments() != null && !entity.getJoyWorkDetail().getDocuments().isEmpty()) {
                        view.setVisibility(View.VISIBLE);
                    } else {
                        view.setVisibility(View.GONE);
                    }
                } else {
                    //没有上传权限就隐藏
                    view.setVisibility(View.GONE);
                }
            }
        });
    }

    @BindingAdapter({"addDocVisible2"})
    public static void addDocVisible2(final View view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (JoyWorkContentExKt.canUpdateFiles(entity == null ? null : entity.getJoyWorkDetail())) {
//                    view.setVisibility(View.VISIBLE);
                    // 将 xml 中逻辑移动到代码中
                    // 有权限且没有附件时才显示
                    if (entity != null && entity.getJoyWorkDetail() != null && entity.getJoyWorkDetail().getResources() != null && entity.getJoyWorkDetail().getResources().isEmpty()) {
                        view.setVisibility(View.VISIBLE);
                    } else {
                        view.setVisibility(View.GONE);
                    }
                } else {
                    //没有上传权限就隐藏
                    view.setVisibility(View.GONE);
                }
            }
        });
    }

    @BindingAdapter({"addDocVisible4"})
    public static void addDocVisible4(final View view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (JoyWorkContentExKt.canUpdateFiles(entity == null ? null : entity.getJoyWorkDetail())) {
//                    view.setVisibility(View.VISIBLE);
                    // 将 xml 中逻辑移动到代码中
                    // 有权限且有附件时显示
                    if (entity != null && entity.getJoyWorkDetail() != null && entity.getJoyWorkDetail().getResources() != null && !entity.getJoyWorkDetail().getResources().isEmpty()) {
                        view.setVisibility(View.VISIBLE);
                    } else {
                        view.setVisibility(View.GONE);
                    }
                } else {
                    //没有上传权限就隐藏
                    view.setVisibility(View.GONE);
                }
            }
        });
    }

    @BindingAdapter({"transferArrowVisible"})
    public static void transferArrowVisible(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (JoyWorkContentExKt.canTransfer(entity == null ? null : entity.getJoyWorkDetail())) {
                    view.setVisibility(View.VISIBLE);
                } else {
                    view.setVisibility(View.GONE);
                }
            }
        });
    }

    @BindingAdapter({"subTitle"})
    public static void subTitle(final TextView view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (JoyWorkContentExKt.hasSubJoyWork(entity == null ? null : entity.getJoyWorkDetail(), true)) {
                    if (entity == null) {
                        view.setText(R.string.joywork_sub_title);
                    } else {
                        view.setText(view.getContext().getString(R.string.joywork_subjoywork_title, entity.countSubJoyWorkFinish()));
                    }
                    view.setEnabled(false);
                    view.setTextColor(view.getResources().getColor(R.color.icon_gray));
                    view.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                } else {
                    view.setText(R.string.joy_work_child_works);
                    view.setEnabled(true);
                    view.setTextColor(view.getResources().getColor(R.color.title_sub));
                    view.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                }
            }
        });
    }

    @BindingAdapter({"subJoyworkUrge"})
    public static void subTitle(final LinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                // 没有子待办或者子待办已全部完成，就不显示
                if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().getChildWorks() == null || entity.getJoyWorkDetail().getChildWorks().isEmpty()) {
                    view.setVisibility(View.GONE);
                } else if (entity.getJoyWorkDetail().getChildWorks().size() == entity.getFinishSubJoyWorkSize()) { // 所有的都完成
                    view.setVisibility(View.GONE);
                } else if (!JoyWorkContentExKt.canUrgeChildWorks(entity.getJoyWorkDetail())) { // 没有相应权限
                    view.setVisibility(View.GONE);
                } else if (entity.getSubJoyWorkOwners() == entity.getJoyWorkDetail().getChildWorks().size()) { // 全部没有负责人
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.GONE);
                }
            }
        });
    }

    @BindingAdapter({"subTitleContainer"})
    public static void subTitleContainer(final View view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (entity == null || entity.getJoyWorkDetail() == null) {
                    view.setVisibility(View.GONE);
                    return;
                }
//                if (!JoyWorkContentExKt.canUpdateChildWorks(entity.getJoyWorkDetail()) && !(JoyWorkContentExKt.hasSubJoyWork(entity.getJoyWorkDetail(), false))) {
                if (!JoyWorkContentExKt.hasSubJoyWork(entity.getJoyWorkDetail(), false)) {
                    // 且没有子待办，隐藏
                    view.setVisibility(View.GONE);
                    return;
                }
                view.setVisibility(View.VISIBLE);
            }
        });
    }

    @BindingAdapter({"subListContainer"})
    public static void subListContainer(final NotifyLinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (entity == null || entity.getJoyWorkDetail() == null || !(JoyWorkContentExKt.hasSubJoyWork(entity.getJoyWorkDetail(), false))) {
                    view.removeAllViews();
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    view.removeAllViews();
                    SubJoyWorkEx.INSTANCE.getRv(view, entity.getJoyWorkDetail());
                }
            }
        });
    }

    @BindingAdapter({"moreContainer"})
    public static void moreContainer(final LinearLayout view, final TaskDetailEntity entity) {
        view.postDelayed(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment taskDetailFragment = getOwnerFragment(view);
                if (taskDetailFragment == null)
                    return;
                if (!taskDetailFragment.hasRemainUI()) {
                    view.setVisibility(View.GONE);
                    view.removeAllViews();
                    taskDetailFragment.itemFactories.removeByClass(DetailMoreFactory.class);
                } else {
                    view.setVisibility(View.VISIBLE);
                    if (!taskDetailFragment.hasItemFactory(DetailMoreFactory.class)) {
                        view.removeAllViews();
                        DetailMoreFactory itemFactory = new DetailMoreFactory();
                        View v = itemFactory.buildView(view, taskDetailFragment.mValue, taskDetailFragment, taskDetailFragment.mValue.mCreateViewModel);
                        taskDetailFragment.itemFactories.add(itemFactory);
                    }
                }
            }
        }, 32);
    }

    @BindingAdapter({"finishAllBtn"})
    public static void finishAllBtn(final View view, final TaskDetailEntity detail) {
        if (detail == null || detail.getJoyWorkDetail() == null) {
            return;
        }
        JoyWorkDetail joyWorkDetail = detail.getJoyWorkDetail();
        TextView tv = view.findViewById(R.id.finish_tips);
        TextView icon = view.findViewById(R.id.finish_icon);
        if (JoyWorkContentExKt.isThird(joyWorkDetail)) {
            view.setVisibility(View.VISIBLE);
            icon.setVisibility(View.GONE);
            if (joyWorkDetail.getTaskStatus() == TaskStatusEnum.FINISH.getCode()) {
                tv.setText(R.string.joywork_list_go_look);
            } else {
                tv.setText(R.string.joywork_list_go_third);
            }
            tv.setTextColor(view.getResources().getColor(R.color.icon_blue));
            view.setBackgroundResource(R.drawable.joywork_blue_stroke_round);
        } else if (TaskDetailFinishHelperKt.isFinishAll(joyWorkDetail)) {
            // 完成全部
            view.setVisibility(View.VISIBLE);
            int type = TaskDetailFinishHelperKt.getDialogType(joyWorkDetail);
            if (TaskDetailFinishHelperKt.isRestore(type)) {
                // 已完成
                icon.setVisibility(View.GONE);
                tv.setTextColor(Color.parseColor("#333333"));
                tv.setText(R.string.joywork_restore_all);
                view.setBackgroundResource(R.drawable.joywork_gray_stroke_round);
            } else {
                icon.setVisibility(View.GONE);
                tv.setText(R.string.joywork_finish_all);
                tv.setTextColor(Color.parseColor("#4C7CFF"));
                icon.setTextColor(Color.parseColor("#4C7CFF"));
                view.setBackgroundResource(R.drawable.joywork_blue_stroke_round);
            }
        } else {
            view.setVisibility(View.GONE);
        }
    }

    @BindingAdapter({"finishTaskBtn"})
    public static void finishTaskBtn(final View view, final TaskDetailEntity detail) {
        if (detail == null || detail.getJoyWorkDetail() == null) {
            return;
        }
        JoyWorkDetail joyWorkDetail = detail.getJoyWorkDetail();
        TextView tv = view.findViewById(R.id.finish_task_tips);
        TextView icon = view.findViewById(R.id.finish_task_icon);
        icon.setVisibility(View.GONE);
        boolean isFinish;
        if (FinishAction.FINISH_TASK_CREATOR.canHandle(joyWorkDetail)) {
            // 可以以创建人身份完成待办
            view.setVisibility(View.VISIBLE);
            isFinish = joyWorkDetail.getTaskStatus() == TaskStatusEnum.FINISH.getCode();
        } else if (FinishAction.FINISH_TASK_OWNER.canHandle(joyWorkDetail)) {
            // 可以以执行人身份完成待办
            Owner self = JoyWorkContentExKt.getSelfOwner(joyWorkDetail);
            if (self == null) {
                view.setVisibility(View.GONE);
                return;
            }
            view.setVisibility(View.VISIBLE);
            isFinish = self.taskStatus == TaskStatusEnum.FINISH.getCode();
        } else {
            view.setVisibility(View.GONE);
            return;
        }
        if (isFinish) {
            // 已完成
            tv.setTextColor(Color.parseColor("#333333"));
            tv.setText(R.string.joywork_restore_task);
            view.setBackgroundResource(R.drawable.joywork_gray_stroke_round);
        } else {
            tv.setText(R.string.joywork_finish_task);
            tv.setTextColor(Color.parseColor("#4C7CFF"));
            view.setBackgroundResource(R.drawable.joywork_blue_stroke_round);
        }
    }

    @BindingAdapter({"finishOnlyMe"})
    public static void finishOnlyMe(final TextView view, final TaskDetailEntity detail) {
        if (detail == null || detail.getJoyWorkDetail() == null) {
            return;
        }
        JoyWorkDetail joyWorkDetail = detail.getJoyWorkDetail();
        if (TaskDetailFinishHelperKt.hasFinishOnlyMe(joyWorkDetail)) {
            Owner self = JoyWorkContentExKt.getSelfOwner(joyWorkDetail);
            if (self == null) {
                view.setVisibility(View.GONE);
                return;
            }
            // 仅我完成
            view.setVisibility(View.VISIBLE);
            if (self.taskStatus == TaskStatusEnum.FINISH.getCode()) {
                // 已完成
                view.setText(R.string.joywork_restore_only_me);
            } else {
                view.setText(R.string.joywork_finish_only_me);
            }
        } else {
            view.setVisibility(View.GONE);
        }
    }

    @BindingAdapter({"markContainer"})
    public static void markContainer(final NotifyLinearLayout view, final TaskDetailEntity entity) {
        view.post(new Runnable() {
            @Override
            public void run() {
                if (entity == null || !ObjExKt.isLegalList(entity.getCustomField())) {
                    view.removeAllViews();
                    view.setVisibility(View.GONE);
                } else {
                    // 自定义字段，有值
                    Map<String, List<String>> safeCustomFields = entity.getJoyWorkDetail().safeCustomFields();
                    view.setVisibility(View.VISIBLE);
                    view.removeAllViews();
                    Map<String, CustomFieldGroup> works = entity.getMapCustomField();
                    boolean isFirst = true;
                    // 每一个自定义字段都需要有一个 view 与之相应
                    for (Map.Entry<String, CustomFieldGroup> work : works.entrySet()) {
                        // 该自定义字段可能并没有设置，但也要显示。所以这里补一个默认的
                        CustomFieldItem item = EmptyCustomFieldItem.getInstance();
                        // 当前待办设置过且有值，替换掉 item
                        if (safeCustomFields.containsKey(work.getKey())) {
                            List<String> strings = safeCustomFields.get(work.getKey());
                            if (ObjExKt.isLegalList(strings)) {
                                // 可能设置多个，这里取第一个
                                String valueId = strings.get(0);
                                // 拿到设置的值，然后从字典中比对，拿到真正的 item
                                List<CustomFieldItem> items = work.getValue().details;
                                if (ObjExKt.isLegalList(items)) {
                                    for (CustomFieldItem fieldItem : items) {
                                        if (fieldItem.detailId != null && fieldItem.detailId.equals(valueId)) {
                                            item = fieldItem;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        SubJoyWorkEx.INSTANCE.buildMarkView(isFirst, view, item, work.getValue(), JoyWorkContentExKt.canUpdateMark(entity.getJoyWorkDetail()));
                        isFirst = false;
                    }
                }
            }
        });
    }

    @BindingAdapter({"duplicateContainer"})
    public static void duplicateContainer(final LinearLayout view, final JoyWorkDetail content) {
        final TaskDetailFragment taskDetailFragment = getOwnerFragment(view);
        if (taskDetailFragment == null)
            return;
        view.post(new Runnable() {
            @Override
            public void run() {
                DuplicateEnum value;
                if (content == null || content.cycle == null) {
                    value = DuplicateEnum.NO;
                } else {
                    value = DuplicateEnum.Companion.getByValue(content.cycle);
                }
                if (!ObjExKt.isLegalLong(content == null ? null : content.getEndTime()) || value == DuplicateEnum.NO) {
                    view.removeAllViews();
                    view.setVisibility(View.GONE);
                    taskDetailFragment.itemFactories.removeByClass(DetailDupCreateFactory.class);
                } else {
                    view.setVisibility(View.VISIBLE);
                    if (taskDetailFragment.hasItemFactory(DetailDupCreateFactory.class)) {
                        taskDetailFragment.mValue.updateDup(value);
                    } else {
                        DetailDupCreateFactory itemFactory = new DetailDupCreateFactory();
                        view.removeAllViews();
                        taskDetailFragment.mValue.updateDup(value);
                        View v = itemFactory.buildView(view, taskDetailFragment.mValue, taskDetailFragment, taskDetailFragment.mValue.mCreateViewModel);
                        taskDetailFragment.itemFactories.add(itemFactory);
                    }
                }
            }
        });
    }

    public static TaskDetailFragment getOwnerFragment(View view) {
        if (view.getContext() instanceof Activity) {
            Object tag = ((Activity) view.getContext()).getWindow().getDecorView().getTag();
            if (tag instanceof TaskDetailFragment) {
                return (TaskDetailFragment) tag;
            }
        }
        return null;
    }
}