package com.jd.oa.joywork.create.param;

import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.joywork.create.Value;
import com.jd.oa.joywork.team.kpi.KpiRepo;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * 从文档新建时 deeplink 参数封装
 * deeplink example：
 * jdme://jm/biz/joywork/quickCreate?mparam={“appName”:”jme”,”pageId”:”1223”,”goalId”:”234234”,”bizCode”:”sdkfjsklfjlsdjflsdjfk”,”content”:”adasd”,”mobileContent”:”sdfsf”}
 */
public class SpaceCreateParam extends DeeplinkCreateParam {
    public static final String APP_NAME = "joyspace";

    public String pageId;
    public String goalId;

    @Override
    public void onParseParam(JSONObject object) throws JSONException {
        pageId = object.getString("pageId");
        goalId = object.getString("goalId");
    }

    @Override
    public void initCreateValue(final Value value) {
        KpiRepo.INSTANCE.getGoalDetail(goalId, new Function1<KpiTarget, Unit>() {
            @Override
            public Unit invoke(KpiTarget target) {
                if (target != null) {
                    value.addTarget(target);
                }
                return null;
            }
        });
    }

    @Override
    protected void toMap(HashMap<String, Object> map) {
        map.put("sourceId", pageId);
    }

    @Override
    public String getAppName() {
        return APP_NAME;
    }
}
