package com.jd.oa.joywork.team.kpi

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager

object KpiMsgCenter {

    fun removeReceiver(context: Context,vararg receivers: BroadcastReceiver){
        receivers.forEach {
            LocalBroadcastManager.getInstance(context).unregisterReceiver(it)
        }
    }

    // follow
    private val _cancelFollow = "kpi.cancel.follow"
    private val _follow = "kpi.follow"

    fun registerFollow(context: Context, receiver: BroadcastReceiver) {
        val intent = IntentFilter(_follow)
        LocalBroadcastManager.getInstance(context).registerReceiver(receiver, intent)
    }

    fun registerCancelFollow(context: Context, receiver: BroadcastReceiver) {
        val intent = IntentFilter(_cancelFollow)
        LocalBroadcastManager.getInstance(context).registerReceiver(receiver, intent)
    }

    fun sendFollow(context: Context, ddAppId: String, emplAccount: String) {
        val intent = Intent(_follow)
        intent.putExtra("ddAppId", ddAppId)
        intent.putExtra("emplAccount", emplAccount)
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
    }

    fun sendCancelFollow(context: Context, ddAppId: String, emplAccount: String) {
        val intent = Intent(_cancelFollow)
        intent.putExtra("ddAppId", ddAppId)
        intent.putExtra("emplAccount", emplAccount)
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
    }
    // follow end
}