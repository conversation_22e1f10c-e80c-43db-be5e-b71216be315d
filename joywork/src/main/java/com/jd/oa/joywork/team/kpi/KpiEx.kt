package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Context
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.*
import com.jd.oa.joywork.JoyWorkEx.getAppIds
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.shortcut.SuccessSnackBar
import com.jd.oa.joywork.snackbar.JoyWorkSnackBar
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.utils.string
import java.util.*
import kotlin.collections.ArrayList

/**
 * If its value is true, we will use [SuccessSnackBar] instead of toast when share is successful, otherwise use Toast
 * @param snackBar: If its value is true, we will use [SuccessSnackBar] rather than toast when share success, otherwise use toast
 * @param barClick: [SuccessSnackBar] click listener
 */
fun Activity.shareResult(
    r: Boolean,
    msg: String?,
    snackBar: Boolean = false,
    barClick: (() -> Unit)? = null
) {
    if (r) {
        if (snackBar && barClick != null) {
            val bar = JoyWorkSnackBar(this, "", null, null, JoyWorkConstant.BIZ_DETAIL_FROM_LIST)
            bar.goDetail = barClick
            bar.content = string(R.string.joywork_share_success)
            bar.buttonText = string(R.string.joywork_go_chat)
            bar.show()
        } else {
            Toast.makeText(this, R.string.joywork_share_success, Toast.LENGTH_SHORT)
                .show()
        }
    } else {
        Toast.makeText(
            this,
            JoyWorkEx.filterErrorMsg(
                msg,
                string(R.string.joywork_share_failure)
            ),
            Toast.LENGTH_SHORT
        ).show()
    }
}

/**
 * 选择聊天记录
 */
fun Activity.selectChat(callback: (users: List<Members>, groupIds: List<String>, firstIsGroup: Boolean) -> Unit) {
    val max = 500
    val imDdService = AppJoint.service(ImDdService::class.java)
    val appIds = mutableListOf<String?>()
    getAppIds(appIds)
    val entity = MemberListEntityJd()
    entity.setMaxNum(max)
        .setSpecifyAppId(appIds)
        .setFrom(UIHelperConstantJd.TYPE_SHARE)
        .setShowConstantFilter(true)
        .setSelectMode(MemberListEntityJd.SELECT_MODE_MULTI)
        .setExternalDataEnable(false)
        .setShowSelf(true)
    imDdService.gotoMemberList(
        this,
        1,
        entity,
        object : Callback<ArrayList<MemberEntityJd?>?> {
            override fun onFail() {
            }

            override fun onSuccess(entities: ArrayList<MemberEntityJd?>?) {
                if (!entities.isLegalList())
                    return
                val f = entities!!.first()!!.type == MemberEntityJd.TYPE_GROUP
                val users = mutableListOf<Members>()
                val groupIds = mutableListOf<String>()
                entities.forEach {
                    if (it != null) {
                        val isGroup = it.type == MemberEntityJd.TYPE_GROUP
                        if (isGroup) {
                            groupIds.add(it.id)
                        } else if (it.type == MemberEntityJd.TYPE_CONTACT) {
                            val m =
                                Members()
                            m.fromDD(it)
                            users.add(m)
                        }
                    }
                }
                callback(users, groupIds, f)
            }
        })
}

/**
 * KpiTarget 只使用了 goalId
 */
fun KpiTarget.shareToDD(activity: Activity) {
    activity.selectChat { users, groupIds, _ ->
        KpiRepo.shareTarget(
            goalId,
            groupIds,
            users
        ) { s, msg ->
            activity.shareResult(s, msg)
        }
    }
}

/**
 * 创建每周总结
 */
fun KpiTarget.goDetail(activity: Activity) {
    goDetail(activity, GoalListActivity.req_target_detail)
}

fun KpiTarget.goDetail(activity: Activity, reqCode: Int) {
    GoalDetailActivity.start(
        activity,
        goalId,
        canEdit,
        canDel,
        canShare,
        reqCode
    )
}

/**
 * 更新目标
 */
fun KpiTarget.updateSelf(context: Activity) {
    GoalCreateActivity.updateTarget(
        context,
        GoalListActivity.req_target_edit,
        goalId,
        itemName ?: ""
    )
}

fun KpiTarget.withWeight(tv: TextView) {
    tv.text = titleWeightStr(tv.context)
}

/**
 * 拼接标题和权重的字符串
 */
fun KpiTarget.titleWeightStr(context: Context): String {
    val weightS = if (!itemWeight.isLegalString()) "" else context.resources.getString(
        R.string.joywork_weight,
        "${itemWeight}%"
    )
    return listOf(itemName.trim(), weightS.trim()).joinLegalString(" - ")
}

/**
 * 将额外标签、标题、权重拼接成一个字符串
 * @return 第一个元素为拼接而成的字符串；第二个元素为标签长度
 */
fun KpiTarget.titleWeightExtraStr(context: Context): Pair<String, Int> {
    val extra = if (getSectionName(context).isLegalString()) {
        getSectionName(context)
    } else {
        "1" // 占位
    }.trim()
    val titleS = titleWeightStr(context)
    return Pair("$extra$titleS", extra.length)
}

fun Int?.statusRes(): Int {
    return if (this == 1) {
        R.drawable.joywork_status_blue
    } else if (this == 2) {
        R.drawable.joywork_status_yellow
    } else {
        R.drawable.joywork_status_red
    }
}

fun List<String>?.krDeletable(): Boolean {
    return this?.has {
        Objects.equals("delete", it)
    } ?: false
}

fun List<String>?.krEditable(): Boolean {
    return this?.has {
        Objects.equals("edit", it)
    } ?: false
}