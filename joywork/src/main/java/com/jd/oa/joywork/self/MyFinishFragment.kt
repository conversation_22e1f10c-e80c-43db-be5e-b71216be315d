package com.jd.oa.joywork.self

import android.content.res.Resources
import android.graphics.Color
import android.view.View
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.repo.CacheStrategy
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.repo.LoadingViewStrategy
import com.jd.oa.joywork.self.base.NetType
import com.jd.oa.joywork.self.base.SelfBaseBlockFragment
import com.jd.oa.joywork.self.base.SelfPageEnum
import com.jd.oa.joywork.self.base.adapter.SelfBaseFragmentAdapter
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.joywork.utils.isLastWeek
import com.jd.oa.joywork.utils.isThisWeek
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.gone
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap
import kotlin.collections.LinkedHashMap

/**
 * 个人待办 已完成 列表
 */
class MyFinishFragment : SelfBaseBlockFragment() {
    private lateinit var mPullUpLoadHelper: PullUpLoadHelper
    private lateinit var mRvAdapter: SelfBaseFragmentAdapter

    override fun onRefreshData(
        loadingViewStrategy: LoadingViewStrategy,
        strategy: CacheStrategy,
        netType: NetType
    ) {
        if (isSwipeRefreshLayoutInited && loadingViewStrategy.show()) {
            mSwipeRefreshLayout.isRefreshing = true
        }
        JoyWorkRepo.listFinish(
            0,
            getUserRole(),
            getDefaultStatus(),
            null,
            netCallback(netType)
        )
    }

    private fun netCallback(netType: NetType): JoyWorkVMCallback {
        return object : JoyWorkVMCallback() {
            override fun call(wrapper: JoyWorkWrapper) {
                if (isSwipeRefreshLayoutInited) {
                    mSwipeRefreshLayout.isRefreshing = false
                }
                val tmpGroups = handleResult(wrapper,resources)
                adapterGroups.clear()
                adapterGroups.addAll(tmpGroups)
                if (adapterGroups.isLegalList()) {
                    if (this@MyFinishFragment::mRvAdapter.isInitialized) {
                        mRvAdapter.replaceAll(adapterGroups)
                    } else {
                        mRvAdapter = setAdapter(adapterGroups)
                        setupLoadMore()
                        loadMoreOnceFinish(wrapper.countJoyWork() < 20)
                    }
                } else {
                    rv.adapter = getEmptyAdapter()
                }
            }

            override fun onError(msg: String) {
                if (isSwipeRefreshLayoutInited) {
                    mSwipeRefreshLayout.isRefreshing = false
                }
                if (rv.adapter != null) {
                    if (netType == NetType.REFRESH) {
                        ToastUtils.showInfoToast(msg)
                    } else if (netType == NetType.INIT) {
                        rv.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg = msg)
                    }
                } else {
                    rv.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg = msg)
                }
            }
        }
    }

    companion object{
        fun handleResult(
            wrapper: JoyWorkWrapper,
            resources: Resources,
            time: (JoyWork) -> Long? = { it ->
                it.completeTime
            }
        ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
            val map = LinkedHashMap<String, ExpandableGroup<JoyWorkTitle, JoyWork>>()
            wrapper.clientTasks?.forEach {
                it.tasks?.forEach { work ->
                    work.sortOwners()
                    val ct = time(work)
                    val key: String
                    when {
                        ct == null -> {
                            key = resources.getString(R.string.me_web_item_other)
                        }
                        ct.isLastWeek() -> {
                            key = resources.getString(R.string.joywork_last_week)
                        }
                        ct.isThisWeek() -> {
                            key = resources.getString(R.string.joywork_this_week)
                        }
                        DateUtils.isSameYear(ct, System.currentTimeMillis()) -> {
                            val c = Calendar.getInstance()
                            c.timeInMillis = ct
                            val month = c.get(Calendar.MONTH)
                            key = resources.getStringArray(R.array.joywork_month)[month]
                        }
                        else -> { // 不同年
                            val c = Calendar.getInstance()
                            c.timeInMillis = ct
                            val month =
                                resources.getStringArray(R.array.joywork_month_short)[c.get(Calendar.MONTH)]
                            val year = SimpleDateFormat("yyyy", Locale.getDefault()).format(ct)
                            key = resources.getString(R.string.joywork_year_month, year, month)
                        }
                    }
                    val group = map.getOrNew(key) {
                        ExpandableGroup(
                            JoyWorkTitle(
                                //  旧版本需要该字段，新版本没用。2022年04月08日
                                R.string.icon_banner_placeholder,
                                key,
                                0,
                                Color.TRANSPARENT
                            ),
                            ArrayList(),
                            key,
                            true
                        )
                    }
                    group.realItems.add(work)
                    group.notifyItemChange()
                }
            }

            val tmpGroups = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
            map.mapKeys {
                tmpGroups.add(it.value)
            }

            tmpGroups.removeAll {
                !it.realItems.isLegalList()
            }
            tmpGroups.forEach { outer ->
                outer.title.count = 0
                outer.expand = true
            }
            return tmpGroups
        }
    }

    private fun setupLoadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(rv) {
            loadMore()
        }
    }

    /**
     * 上拉加载更多的操作
     */
    private fun loadMore() {
        var offset = 0
        adapterGroups.forEach {
            offset += JoyWorkEx.countJoyWork(it.realItems)
        }
        JoyWorkRepo.listFinish(
            offset,
            getUserRole(),
            getDefaultStatus(),
            null,
            object : JoyWorkVMCallback() {
                override fun call(wrapper: JoyWorkWrapper) {
                    if (isSwipeRefreshLayoutInited) {
                        mSwipeRefreshLayout.isRefreshing = false
                    }
                    val tmpGroups = handleResult(wrapper, resources)
                    if (tmpGroups.isLegalList() && this@MyFinishFragment::mRvAdapter.isInitialized) {
                        val map = HashMap<String, ExpandableGroup<JoyWorkTitle, JoyWork>>()
                        adapterGroups.forEach {
                            map[it.id] = it
                        }
                        tmpGroups.forEach {
                            val i = map[it.id]
                            if (i != null) {
                                val list = i.realItems ?: ArrayList()
                                list.addAll(it.realItems)
                                it.notifyItemChange()
                            } else {
                                adapterGroups.add(it)
                            }
                        }
                        mRvAdapter.replaceAll(adapterGroups)
                    }
                    loadMoreOnceFinish(wrapper.countJoyWork() < 20)
                }

                override fun onError(msg: String) {
                    if (isSwipeRefreshLayoutInited) {
                        mSwipeRefreshLayout.isRefreshing = false
                    }
                    loadMoreOnceFinish(false)
                }
            }
        )
    }

    private fun loadMoreOnceFinish(finish: Boolean) {
        if (::mPullUpLoadHelper.isInitialized) {
            if (finish) {
                mPullUpLoadHelper.setComplete()
            } else {
                mPullUpLoadHelper.setLoaded()
            }
        }
    }

    override fun onLoadMore(blockType: String, callback: JoyWorkVMCallback, offset: Int) {
        // 用不上这个
    }

    override fun getUserRole(): TaskUserRole {
        return TaskUserRole.ALL
    }

    override fun getDefaultStatus(): TaskStatusEnum {
        return TaskStatusEnum.FINISH
    }

    override fun getPageId(): SelfPageEnum {
        return SelfPageEnum.MY_FINISH
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return TaskListTypeEnum.FINISH
    }

    override fun getTitleType(joyWorkTitle: JoyWorkTitle): Int {
        return 1
    }

    override fun onConfigGroupTitle(indicator: View) {
        indicator.gone()
    }

    override fun expandable(): Boolean {
        return false
    }

    override fun getTimeType(): Int {
        return 2
    }

    override fun showAvatar(): Boolean {
        return true
    }

    override fun finishAction(): List<String> {
        return mutableListOf(ProjectConstant.FINISH_ACTION)
    }
}