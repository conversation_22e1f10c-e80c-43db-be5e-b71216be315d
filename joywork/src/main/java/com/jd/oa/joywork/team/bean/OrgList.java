package com.jd.oa.joywork.team.bean;

import com.jd.oa.joywork.bean.JoyWorkUser;

import java.util.List;

public class OrgList {
    public static final int SUPER = 1;
    public static final int SUB = 2;
    public static final int ALL = 3;
    public static final int FOLLOWED = 4;

    public List<JoyWorkUser> sups; // 直属上级
    public List<JoyWorkUser> subs; // 直属下级
    public Integer subNums; //直属下级总数

    public List<JoyWorkUser> members;// 关注人员列表
}
