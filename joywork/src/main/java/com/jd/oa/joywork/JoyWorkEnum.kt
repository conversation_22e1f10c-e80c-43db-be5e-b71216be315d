package com.jd.oa.joywork

import android.content.Context
import android.graphics.Color
import com.jd.oa.joywork.detail.canComplete
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.joywork.detail.hasForceComplete

/**
 * 优先级
 */
enum class JoyWorkLevel(val value: Int) {
    NO(0) {
        override fun getColor() = Color.parseColor("#FF666666")
        override fun getResId(): Int {
            return R.string.joywork_project_sort_no
        }

        override fun getBgColor(): Int {
            return Color.parseColor("#1A666666")
        }

        override fun getTitle(): Int {
            return R.string.joywork_priority_None
        }
    },

    P1(4) {
        override fun getColor() = Color.parseColor("#FFFE3B30")
        override fun getResId(): Int {
            return R.string.joywork_priority_p1
        }

        override fun getBgColor(): Int {
            return Color.parseColor("#1AFE3B30")
        }

        override fun getCardTypeBgColor(): Int {
            return Color.parseColor("#F63218")
        }

        override fun getTitle(): Int {
            return R.string.joywork_priority_High
        }
    },

    P2(3) {
        override fun getColor() = Color.parseColor("#FFFFA600")
        override fun getResId(): Int {
            return R.string.joywork_priority_p2
        }

        override fun getBgColor(): Int {
            return Color.parseColor("#1AFFA600")
        }

        override fun getCardTypeBgColor(): Int {
            return Color.parseColor("#FBB731")
        }

        override fun getTitle(): Int {
            return R.string.joywork_priority_Medium
        }
    },
    P3(2) {
        override fun getColor() = Color.parseColor("#FF4C7CFF")
        override fun getResId(): Int {
            return R.string.joywork_priority_p3
        }

        override fun getBgColor(): Int {
            return Color.parseColor("#1A4C7CFF")
        }

        override fun getCardTypeBgColor(): Int {
            return Color.parseColor("#1869F5")
        }


        override fun getTitle(): Int {
            return R.string.joywork_priority_Low
        }
    },
    P4(1) {
        override fun getColor() = Color.parseColor("#FF00A558")
        override fun getResId(): Int {
            return R.string.joywork_priority_p4
        }

        override fun getBgColor(): Int {
            return Color.parseColor("#1A00A558")
        }
    };

    abstract fun getColor(): Int
    abstract fun getResId(): Int
    abstract fun getBgColor(): Int
    open fun getCardTypeBgColor(): Int = Color.TRANSPARENT
    open fun getTitle(): Int = -1

    /**
     * We have removed the p4 level, but there is still p4 in the old version
     * So in new version, we will fix the p4 to NO
     */
    fun fixIfNecessary(oldV: Int?): Int? {
        if (oldV == P4.value) {
            return NO.value
        }
        return oldV
    }

    fun getLevel(p: Int): JoyWorkLevel {
        var ans = values().firstOrNull {
            it.value == p
        } ?: NO
        if (ans == P4) {
            // 将旧版本的 p4 兼容为 NO
            ans = NO
        }
        return ans
    }
}

enum class AlertType(val value: Int, val resId: Int) {
    NO(0, R.string.joywork_alert_no),
    MINS_15(1, R.string.joywork_alert_15m),
    MINS_30(2, R.string.joywork_alert_30m),
    HOURS_1(3, R.string.joywork_alert_1h),
    HOURS_2(4, R.string.joywork_alert_2h),
    DAYS_1(5, R.string.joywork_alert_1d),
    DAYS_2(6, R.string.joywork_alert_2d),
    WEEKS_1(7, R.string.joywork_alert_1w);


    companion object {
        fun getByValue(value: Int?): AlertType {
            if (value == null) {
                return NO;
            }
            return values().firstOrNull {
                it.value == value
            } ?: NO
        }

        fun valueToString(ts: Set<AlertType>): String {
            return ts.joinToString(separator = ",") {
                "${it.value}"
            }
        }

        fun stringToValue(s: String): Set<AlertType> {
            return s.split(",")
                .mapNotNull { value -> AlertType.values().find { it.value == value.toInt() } }
                .toSet()
        }


        fun equals(set1: Set<AlertType>, set2: Set<AlertType>): Boolean {
            if (set1 == set2) {
                return true
            }
            if (set1.size != set2.size) {
                return false
            }
            var ans = true
            set1.forEach {
                if (!set2.contains(it)) {
                    ans = false
                }
            }
            return ans
        }

        fun createFromString(str: String?): HashSet<AlertType> {
            val ts = HashSet<AlertType>()
            val map = HashMap<String, AlertType>()
            values().forEach {
                map["${it.value}"] = it
            }
            if (str.isLegalString()) {
                str!!.split(",").forEach {
                    if (map.containsKey(it.trim())) {
                        ts.add(map[it.trim()]!!)
                    }
                }
            }
            if (containNO(ts)) {
                ts.clear()
                ts.add(NO)
            }
            return ts
        }

        fun toString(ts: HashSet<AlertType>, context: Context): String {
            if (!ts.isLegalSet()) {
                ts.add(NO)
            }
            if (ts.size == 1) {
                val s = context.getString(ts.first().resId)
                if (ts.first() == NO) {
                    return s;
                }
                return context.getString(R.string.joywork_alert_s2, s)
            }
            return context.getString(
                R.string.joywork_alert_s,
                ts.size
            )
        }

        @JvmStatic
        fun containNO(ts: Set<AlertType>): Boolean {
            return ts.contains(NO)
        }
    }
}

enum class DuplicateEnum(val resId: Int, val value: Int) {
    NO(R.string.joywork_duplicate_no, 0),
    WORK_TIME(R.string.joywork_duplicate_worktime, 1),
    DAY(R.string.joywork_duplicate_everyday, 2),
    WEEK(R.string.joywork_duplicate_everyweek, 3),
    MONTH(R.string.joywork_duplicate_every_month, 5),
    YEAR(R.string.joywork_duplicate_every_year, 6), ;

    companion object {
        fun getByValue(value: Int?): DuplicateEnum {
            if (value == null) {
                return NO;
            }
            return values().firstOrNull {
                it.value == value
            } ?: NO
        }
    }
}

/**
 * 定义 joywork 一些常用操作，并不一定用于某个具体界面
 */
enum class JoyWorkActionEnum {
    MORE {
        override fun getStringId(): Int {
            return R.string.joywork_detail_more
        }

        override fun getIconId(): Int {
            return R.string.icon_tabbar_more
        }
    },

    TITLE { // 标题，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.me_title_item
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    DESC {
        // 描述，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.joy_work_description
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    SHIMO { // 文档，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.joy_work_doc
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    ATTACHMENT { // 自己的附件，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.joy_work_res
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    OWNER { // 单负责人，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.joywork_create_owner_hint
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },


    OWNERS { // 多负责人，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.joywork_create_owner_hint
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    DEAD_LINE { // 截止时间，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.joywork_shortcut_deadline_tips
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    RELATION { // 关注人，这里只是使用，只是内部方法的返回值没有用
        override fun getStringId(): Int {
            return R.string.joywork_create_relation_hint
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    /**
     * 撤回
     */
    REVERSE {
        override fun getStringId(): Int {
            return R.string.joywork_alert_reverse
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    /**
     * 移除
     */
    REMOVE {
        override fun getStringId(): Int {
            return R.string.joywork_alert_remove
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_copy
        }
    },

    /**
     * 退出协作人
     */
    EXIT {
        override fun getStringId(): Int {
            return R.string.joywork_detail_exit
        }

        override fun getIconId(): Int {
            return R.string.icon_direction_logout
        }
    },

    /**
     * 还原待办
     */
    REVERSE_TASK {
        override fun getStringId(): Int {
            return R.string.joywork_detail_restore
        }

        override fun getIconId(): Int {
            return R.string.icon_direction_revoke
        }
    },

    // 合并待办
    MERGE {
        override fun getStringId(): Int {
            return R.string.joywork_merge
        }

        override fun getIconId(): Int {
            return R.string.icon_general_merge
        }

    },

    // 取消合并待办
    MERGE_CANCEL {
        override fun getStringId(): Int {
            return R.string.joywork_merge_cancel
        }

        override fun getIconId(): Int {
            return R.string.icon_general_merge
        }

    },

    // 变更父待办
    CHANGE_PARENT {
        override fun getStringId(): Int {
            return R.string.joywork_parent
        }

        override fun getIconId(): Int {
            return R.string.icon_treelist
        }

    },


    // 转为子待办
    CHILD {
        override fun getStringId(): Int {
            return R.string.joywork_sub
        }

        override fun getIconId(): Int {
            return R.string.icon_treelist
        }

    },

    /**
     * 删除待办
     */
    DEL_TASK {
        override fun getStringId(): Int {
            return R.string.joywork_detail_del
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_delete
        }
    },

    /**
     * 查看历史
     */
    HISTORY {
        override fun getStringId(): Int {
            return R.string.joywork_detail_history
        }

        override fun getIconId(): Int {
            return R.string.icon_edit_historyleft
        }
    },


    /**
     * 设置重复
     */
    DUP {
        override fun getStringId(): Int {
            return R.string.joywork_duplicate_text
        }

        override fun getIconId(): Int {
            return R.string.icon_general_repeat
        }
    },

    /**
     * 提醒
     */
    REMIND {
        override fun getStringId(): Int {
            return R.string.joy_work_remind
        }

        override fun getIconId(): Int {
            return R.string.icon_general_bell
        }
    },

    /**
     * 优先级
     */
    PRIORITY {
        override fun getStringId(): Int {
            return R.string.joywork_detail_priority
        }

        override fun getIconId(): Int {
            return R.string.icon_general_priority
        }
    },

    /**
     * 启动时间
     */
    LAUNCH_TIME {
        override fun getStringId(): Int {
            return R.string.joywork_detail_launch
        }

        override fun getIconId(): Int {
            return R.string.icon_padding_start
        }
    },

    /**
     * 上报问题风险
     */
    RISK {
        override fun getStringId(): Int {
            return R.string.joywork_risk_hint
        }

        override fun getIconId(): Int {
            return R.string.icon_prompt_exclamationcircle
        }
    },


    /**
     * 更多
     */
    ADD_MORE {
        override fun getStringId(): Int {
            return R.string.joywork_risk_hint
        }

        override fun getIconId(): Int {
            return R.string.icon_prompt_exclamationcircle
        }
    },

    /**
     * 添加子待办
     */
    ADD_CHILD {
        override fun getStringId(): Int {
            return R.string.joy_work_child_works
        }

        override fun getIconId(): Int {
            return R.string.icon_general_subtask
        }
    },

    /**
     * 关联清单
     */
    GROUP {
        override fun getStringId(): Int {
            return R.string.joy_work_link_task_groups
        }

        override fun getIconId(): Int {
            return R.string.icon_padding_listing
        }
    },


    /**
     * 关联系统预置清单
     */
    SYS_GROUP {
        override fun getStringId(): Int {
            return R.string.joy_work_link_task_groups
        }

        override fun getIconId(): Int {
            return R.string.icon_padding_listing
        }
    },

    TARGET {
        override fun getStringId(): Int {
            return R.string.joywork_link_target
        }

        override fun getIconId(): Int {
            return R.string.icon_padding_target
        }
    },

    /**
     * 无意义，只是为了方便处理 UI
     */
    NO {
        override fun getStringId(): Int {
            return R.string.joy_work_link_task_groups
        }

        override fun getIconId(): Int {
            return R.string.icon_padding_listing
        }
    };

    abstract fun getStringId(): Int
    abstract fun getIconId(): Int
}

/**
 * 附件方式
 */
enum class JoyWorkAttachmentChoose {
    TAKE_PHOTO,
    ALBUM,
    JOY_SPACE
}


/**
 * 我的待办 风险问题
 */
enum class IssueRegionTypeEnum(
    val code: String,
    val label: String,
    val strId: Int
) {
    // 逾期
    PROBLEM("1", "问题", R.string.joywork_risk_problem),
    ISSUE("2", "风险", R.string.joywork_risk_risk),
    OVER_DUE("3", "已逾期", R.string.joywork_self_overdue);

    fun getIconId(): Int {
        return R.string.icon_banner_placeholder
    }

    fun getColor(): Int {
        return Color.parseColor("#26A7FD")
    }

    companion object {

        fun getObj(code: String): IssueRegionTypeEnum {
            return values().firstOrNull {
                it.code == code
            } ?: PROBLEM
        }
    }
}

/**
 * 我的待办 列表页分组枚举
 */
enum class RegionTypeEnum(
    val code: String,
    val label: String,
    val strId: Int
) {
    // 逾期
    OVERDUE("0", "逾期", R.string.joywork_self_overdue),
    TODAY("1", "今天", R.string.joywork_today),
    NEXT_7DAYS("6", "未来七天", R.string.joywork_next_7d),
    FUTURE("5", "以后", R.string.joywork_self_future),
    INBOX("4", "未安排", R.string.joywork_self_no_schedule),
    FINISH("3", "已完成", R.string.joywork_screen_finish);

    fun addParams(params: HashMap<String, Any>) {
        params["regionType"] = code.toInt()
    }

    fun getIconId(): Int {
        return R.string.icon_banner_placeholder
    }

    fun getColor(): Int {
        return Color.parseColor("#26A7FD")
    }

    companion object {


        fun getObj(code: String): RegionTypeEnum {
            return values().firstOrNull {
                it.code == code
            } ?: OVERDUE
        }
    }
}

/**
 * 列表页各块枚举
 * 来源于后台，直接使用
 */
enum class BlockTypeEnum(
    public val code: String,
    public val label: String,
    val icon: Int,
    val iconColor: String
) {
    // 也叫待安排
    INBOX("0", "收件箱", R.string.icon_padding_backlog, "#26A7FD") {
        override fun getClickId(): String {
            return JoyWorkConstant.ASSIGN_INBOX
        }
    },
    TODAY("1", "今天", R.string.icon_padding_today, "#00C166") {
        override fun getClickId(): String {
            return JoyWorkConstant.ASSIGN_TODAY
        }
    },
    IN_PLAN("2", "计划", R.string.icon_padding_calendar, "#FAAB0C") {
        override fun getClickId(): String {
            return JoyWorkConstant.ASSIGN_PLAN
        }
    },
    OTHER_DAY("3", "某一天", R.string.icon_padding_schedule, "#8F959E") {
        override fun getClickId(): String {
            return JoyWorkConstant.ASSIGN_OTHER
        }
    };

    fun addParams(params: HashMap<String, Any>) {
        params["blockType"] = code
    }

    fun isPlan(code: String): Boolean {
        return code == IN_PLAN.code
    }

    abstract fun getClickId(): String

    fun getIconId(): Int {
        return when (this) {
            INBOX -> R.string.icon_padding_backlog
            TODAY -> R.string.icon_padding_today
            IN_PLAN -> R.string.icon_padding_calendar
            OTHER_DAY -> R.string.icon_padding_schedule
            else -> throw RuntimeException("${this.code} not support")
        }
    }

    fun getTitleId(): Int = when (this) {
        INBOX -> R.string.joywork_task_todo
        TODAY -> R.string.joywork_today
        IN_PLAN -> R.string.joywork_task_plan
        OTHER_DAY -> R.string.joywork_task_someday
        else -> throw RuntimeException("${this.code} not support")
    }

    fun getColor(): Int {
        return when (this) {
            INBOX -> Color.parseColor("#26A7FD")
            TODAY -> Color.parseColor("#00C166")
            IN_PLAN -> Color.parseColor("#FAAB0C")
            OTHER_DAY -> Color.parseColor("#8F959E")
            else -> throw RuntimeException("${this.code} not support")
        }
    }

    companion object {
        fun getObj(code: String): BlockTypeEnum {
            return when (code) {
                INBOX.code -> INBOX
                TODAY.code -> TODAY
                IN_PLAN.code -> IN_PLAN
                OTHER_DAY.code -> OTHER_DAY
                else -> INBOX
            }
        }
    }
}

/**
 * joywork 状态
 * 来源于后台，直接使用
 */
enum class TaskStatusEnum(public val code: Int, public val label: String) {
    DELETED(0, "已删除") {
        override fun getStringId(): Int {
            return R.string.joywork_screen_deleted
        }
    },
    UN_FINISH(1, "未完成") {
        override fun getStringId(): Int {
            return R.string.joywork_screen_unfinish
        }
    },
    FINISH(2, "已完成") {
        override fun getStringId(): Int {
            return R.string.joywork_screen_finish
        }
    },

    // 这其实不算一种 status，只不过为了处理方便，与后台一致，所以也定义到枚举中
    // 每一个 joywork 其 taskStatus 理论上不会是该值
    ALL(3, "全部") {
        override fun getStringId(): Int {
            return R.string.joywork_screen_all
        }
    },

    // 同上
    RISK(4, "风险问题") {
        override fun getStringId(): Int {
            return R.string.joywork_tabbar_title_issue
        }
    };

    fun addParams(params: HashMap<String, Any>) {
        params["taskStatus"] = code
    }

    fun isFinish(code: Int?): Boolean {
        return code == FINISH.code
    }

    abstract fun getStringId(): Int

    companion object {

        fun getInstance(code: Int): TaskStatusEnum {
            return values().firstOrNull {
                it.code == code
            } ?: UN_FINISH
        }

        fun isFinish(code: Int): Boolean {
            return code == FINISH.code
        }

        fun isUnFinish(code: Int): Boolean {
            return code == UN_FINISH.code
        }
    }
}

/**
 * Joywork 人员
 * 来源于后台，直接使用
 */
enum class TaskUserRole(val code: Int, val label: String) {
    /** 这里只是为了接口传参统一，实现使用时不应该使用该字段 */
    ALL(0, "全部身份"),
    OWNER(1, "执行人"),
    EXECUTOR(2, "关注人"),
    FOLLOWERS(3, "没用"),
    ASSIGN(4, "我指派的"),
    CREATOR(5, "创建人");

    fun addParams(params: HashMap<String, Any>) {
        params["userRole"] = code
    }

    fun addParams(params: HashMap<String, Any>, key: String) {
        params[key] = code
    }
}

/**
 * 团队待办成员状态
 */
enum class TeamUserRole(val permission: Int, val label: String, val desc: String) {
    CREATOR(1, "创建人", ""),
    EDITOR(2, "可编辑", "可以编辑清单中的待办"),
    VIEWER(3, "仅查看", "仅能查看清单中的待办，不支持编辑、评论待办"),
}

/**
 * 各操作对应的 string。
 * string 由后台返回
 */
enum class JoyWorkOp(val action: String) {
    UPDATE("update"),// 更新title、开始时间、截止时间、优先级、附件
    EXECUTOR("executor"),// 变更关注人，包括添加和删除
    FORCE_COMPLETE("force_complete"), // 标记别的执行人
    OWNER("owner"), // 操作负责人权限

    //    FOLLOWER("follower"),// 变更关注人，包括添加和删除
    CHILD_TASK("childTask"), // 创建子待办，包括完成，删除，添加
    COMMENT("comment"),// 发表评论
    COMPLETE("complete"),// 完成和取消完成待办
    DELETE("delete"),// 删除或还原待办
    URGE("urge"), // 催办权限
    REMIND("remind"),// 设置提醒时间
    MERGE("merge"), // 合并
    ADD_PARENT("addParent"), // 挂载父待办
    ONLY_READ("onlyRead"), // 只读权限
    ASSIGN("assign"),// 转移负责人
    END_REMIND("endRemind"), // 截止前提醒
    ARCHIVE("archive") // 归档权限
}

/**
 * 已读未读状态
 */
enum class JoyWorkReadStatus(val v: Int) {
    HIDE(0),
    UNREAD(1),
    READ(2);

    companion object {
        fun isRead(code: Int?): Boolean {
            return READ.v == code
        }

        fun unRead(code: Int?): Boolean {
            return UNREAD.v == code
        }
    }
}

/**
 * 待办风险枚举
 */
enum class RiskEnum(val code: Int, val resId: Int) {
    /**没有设置。注意第二个参数不能使用*/
    NO_SET(0, R.string.joywork_time_no_set) {
        override fun textColor(): Int {
            return Color.parseColor("#8F959E")
        }

        override fun bgColor(): Int {
            return Color.TRANSPARENT
        }

        override fun tipsBgColor(): Int {
            return Color.TRANSPARENT
        }

        override fun tipsIconColor(): Int {
            return Color.TRANSPARENT
        }

        /**
         * 不能使用
         */
        override fun msgTitle(): Int {
            return 0
        }

        /**
         * 不能使用
         */
        override fun msgHint(): Int {
            return 0
        }

        /**
         * 不能使用
         */
        override fun getClickId(): String {
            return ""
        }
    },

    /**正常*/
    NORMAL(3, R.string.joywork_risk_normal) {
        override fun textColor(): Int {
            return Color.WHITE
        }

        override fun bgColor(): Int {
            return Color.parseColor("#00C166")
        }

        override fun tipsBgColor(): Int {
            return Color.TRANSPARENT
        }

        override fun tipsIconColor(): Int {
            return Color.TRANSPARENT
        }

        override fun msgTitle(): Int {
            return R.string.joywork_risk_msg_normal_title
        }

        override fun msgHint(): Int {
            return R.string.joywork_risk_msg_normal_tips
        }

        override fun getClickId(): String {
            return JoyWorkConstant.DETAIL_RISK_NORMAL
        }
    },

    /**风险*/
    RISK(2, R.string.joywork_risk_risk) {
        override fun textColor(): Int {
            return Color.parseColor("#232930")
        }

        override fun bgColor(): Int {
            return Color.parseColor("#FFB416")
        }

        override fun tipsBgColor(): Int {
            return Color.parseColor("#FFF2E1")
        }

        override fun tipsIconColor(): Int {
            return Color.parseColor("#FFA333")
        }

        override fun msgTitle(): Int {
            return R.string.joywork_risk_msg_risk_title
        }

        override fun msgHint(): Int {
            return R.string.joywork_risk_msg_risk_tips
        }

        override fun getClickId(): String {
            return JoyWorkConstant.DETAIL_RISK_RISK
        }
    },

    /**问题*/
    PROBLEM(1, R.string.joywork_risk_problem) {
        override fun textColor(): Int {
            return Color.WHITE
        }

        override fun bgColor(): Int {
            return Color.parseColor("#FE514C")
        }

        override fun tipsBgColor(): Int {
            return Color.parseColor("#FFF2F0")
        }

        override fun tipsIconColor(): Int {
            return Color.parseColor("#FE3E33")
        }

        override fun msgTitle(): Int {
            return R.string.joywork_risk_msg_problem_title
        }

        override fun msgHint(): Int {
            return R.string.joywork_risk_msg_problem_tips
        }

        override fun getClickId(): String {
            return JoyWorkConstant.DETAIL_RISK_PROBLEM
        }
    };

    /**
     * 方案显示颜色
     */
    abstract fun textColor(): Int

    /**
     * 背景颜色
     */
    abstract fun bgColor(): Int

    /**
     * 显示在界面上方方案背景色
     */
    abstract fun tipsBgColor(): Int

    /**
     * 显示在界面上方方案icon 与下方按钮颜色
     */
    abstract fun tipsIconColor(): Int

    /**
     * 输入描述时文字标题
     */
    abstract fun msgTitle(): Int

    /**
     * 输入描述时默认提示文字
     */
    abstract fun msgHint(): Int

    abstract fun getClickId(): String

    companion object {
        fun valueByCode(code: Int?): RiskEnum {
            return when (code) {
                NORMAL.code -> {
                    NORMAL
                }

                PROBLEM.code -> {
                    PROBLEM
                }

                RISK.code -> {
                    RISK
                }

                else -> {
                    NO_SET
                }
            }
        }
    }
}

enum class EntranceType(val code: String) {
    MY_HANDLE("myHandle"), // 我处理/执行的的
    MY_ASSIGN("myAssign"),// 我指派的
    MY_COOPERATE("myCooperate"),// 我协作/关注的
    PROJECT_LIST("projectList"),// 团队待办
    RISK("risk"),//风险问题
    CUSTOM("custom"),// 其余。在侧边菜单中，除上述几项外，其余自定义视图等都算
    PENDING_FEEDBACK("imPendingFeedBack"),
    LATER_HANDLE("imHandleLater"),
    MY_CREATE("myCreate"),
    ASSIGN_TO_ME("assignToMe"),
    MEETING("meeting"),
    JD_PEOPLE("renshiguanli");

    companion object {
        fun valueByCode(s: String): EntranceType {
            return values().firstOrNull { it.code == s } ?: MY_HANDLE
        }
    }
}

/**
 * 详情页待办完成功能。
 * 主要产品逻辑链接：https://apijoyspace.jd.com/v1/files/AE4SMkmZIUpDclw6PwRn/link
 */
enum class FinishAction {

    // 完成待办有两种逻辑，一种是按创建人身份完成待办，一种按执行人身份完成待办
    // 虽然文案有时候一样，但调用的接口可能不一样，所以区分显示

    /**
     * 以创建人身份完成待办
     */
    FINISH_TASK_CREATOR {
        override fun canHandle(detail: JoyWorkDetail?): Boolean {
            val ans = detail.hasForceComplete()
            if (!ans)
                return false
            // 此处逻辑：
            // 如果自己是执行人+仅自己是执行人
            // 如果自己不是执行人+最多只有一个执行人
            // 所以两个逻辑合在一起，只需要判断执行人个数
            return (detail?.owners?.size ?: 0) <= 1
        }
    },

    /**
     * 以非创建人（执行人）身份完成待办
     */
    FINISH_TASK_OWNER {
        override fun canHandle(detail: JoyWorkDetail?): Boolean {
            // 非创建人+执行人
            return !detail.hasForceComplete() && detail.canComplete()
        }
    };

    abstract fun canHandle(detail: JoyWorkDetail?): Boolean
}

/**
 * 列表页待办完成
 */
enum class ListFinishAction(val code: Int, label: String) {
    NOT_ALLOW(1, "不允许点击"),
    NORMAL_NO_BOX(2, "普通完成，不弹窗"),
    NORMAL_DESC_BOX(3, "普通完成，完成信息输入框"),
    FORCE_NO_BOX(4, "强制完成，不弹窗"),
    FORCE_ALL_BOX(5, "强制完成，完成全部确认框"),
    FORCE_TASK_BOX(6, "强制完成，完成待办确认框");

    companion object {
        fun getValueByCode(code: Int?): ListFinishAction {
            return values().firstOrNull {
                it.code == code
            } ?: NOT_ALLOW
        }
    }
}

enum class TaskListTypeEnum(val code: Int, label: String) {
    HANDLE(1, "待我处理"),
    ASSIGN(2, "我指派的"),
    FOLLOW(3, "我关注的"),
    PROJECT(4, "团队清单待办列表"),
    QUESTION(5, "风险问题列表"),
    GROUP(6, "群待办列表"),
    CHILD(7, "子待办列表"),
    FINISH(8, "已完成待办列表"),
    ARCHIVE_PROJECT(9, "已归档清单"),
    APPROVAL(10, "待我审批")
}