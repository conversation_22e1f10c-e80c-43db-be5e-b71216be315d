package com.jd.oa.joywork.search;

import android.content.Context;
import android.content.res.Resources;

import com.jd.oa.joywork.JoyWorkExKt;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.team.dialog.CreateDialog;

import java.util.ArrayList;
import java.util.List;

public class SearchResult {
    public static final String sHighlightTag = "em";
    public static final String sStartTag = "<em>";
    public static final String sEndTag = "</em>";

    public String taskId;

    public Integer status; // 待办状态
    public Integer taskStatus;// 待办完成状态
    public String title; // 待办标题
    public String remark; // 待办描述
    public Long endTime;// 截止时间
    public List<JoyWorkDetail.Project> projects; // 挂靠团队清单信息
    public List<JoyWorkUser> owners; // 多执行人列表
    public JoyWorkUser creator; // 创建人
    // 执行人高亮显示的文字
    private String mOwnerHighlightText;
    private String mRemarkHighlightText;
    private String mTitleHighlightText;

    public String getProjectsString(Context context) {
        if (projects == null) {
            return "";
        }
        List<String> s = new ArrayList<>(projects.size());
        for (JoyWorkDetail.Project project : projects) {
            if (ObjExKt.isLegalString(project.getTitle())) {
                s.add(project.getTitle());
            }
        }
        return ObjExKt.joinLegalString(s, context.getString(R.string.joywork_comma));
    }

    public String getTitleHighlightText() {
        if (!ObjExKt.isLegalString(title)) {
            return "";
        }
        if (ObjExKt.isLegalString(mTitleHighlightText)) {
            return mTitleHighlightText;
        }
        mTitleHighlightText = split(title);
        return mTitleHighlightText;
    }

    public String getRemarkHighlightText() {
        if (!ObjExKt.isLegalString(remark)) {
            return "";
        }
        if (ObjExKt.isLegalString(mRemarkHighlightText)) {
            return mRemarkHighlightText;
        }
        mRemarkHighlightText = split(remark);
        return mRemarkHighlightText;
    }

    public String getOwnerHighlightText(Context context) {
        if (ObjExKt.isLegalString(mOwnerHighlightText)) {
            return mOwnerHighlightText;
        }
        List<String> list = new ArrayList<>();
        list.add(getCreatorString(context));
        list.add(getOwnersString(context));
        String src = ObjExKt.joinLegalString(list, context.getString(R.string.joywork_comma));
        if (!ObjExKt.isLegalString(src)) {
            return "";
        }
        mOwnerHighlightText = split(src);
        return mOwnerHighlightText;
    }

    private String split(String text) {
        // What we should do if the text matches <em>\s*</em>
        // Simple is best, ignore it !!!
        int startIndex = text.indexOf(sStartTag);
        if (startIndex <= 0) return text;
        int endIndex = text.indexOf(sEndTag, startIndex);
        if (endIndex == -1) {
            // 1. The text contains only <em>
            // 2. The text matches【</em>xx<em>】
            return text;
        }
        // NOT text.replaceAll("</?" + sHighlightTag + ">", ""); first and then substring
        // this will cause startIndex to be wrong
        String preText = text.substring(0, startIndex).replaceAll(sEndTag, "");
        if (preText.length() <= 5) {
            // Keep 5 letters before the keyword/sStartTag
            // So in this case, we can return immediately
            return preText + text.substring(startIndex);
        }
        String suffixText = text.substring(startIndex).replaceAll("</?" + sHighlightTag + ">", "");
        // The length of the remaining string must be greater than 15
        int preLen = Math.max(15 - suffixText.length(), 5);
        int preIndex = Math.max(0, preText.length() - preLen);
        String ellipsis = "";
        if (preIndex != 0) {
            preIndex++;
            ellipsis = "…";
        }
        return ellipsis + preText.substring(preIndex) + text.substring(startIndex);
    }

    private String getCreatorString(Context context) {
        if (creator == null || !ObjExKt.isLegalString(creator.realName)) {
            return "";
        }
        return creator.realName + context.getResources().getString(R.string.joywork_create);
    }

    private String getOwnersString(Context context) {
        if (owners == null) {
            return "";
        }
        List<String> s = new ArrayList<>(owners.size());
        JoyWorkUser owner = null;
        for (JoyWorkUser user : owners) {
            if (JoyWorkExKt.isChief(user.chief) && owner == null) {
                owner = user;
            } else {
                s.add(user.realName);
            }
        }
        String es = ObjExKt.joinLegalString(s, context.getString(R.string.joywork_dunhao));
        es = context.getResources().getString(R.string.joywork_create_owner, es);
        if (owner == null || !ObjExKt.isLegalString(owner.realName)) {
            return es;
        } else {
            String sb = owner.realName +
                    context.getString(R.string.joywork_owner2);
            s.clear();
            s.add(sb);
            s.add(es);
            return ObjExKt.joinLegalString(s, context.getString(R.string.joywork_comma));
        }
    }

    public String getEndTimeString(Resources resources) {
        return CreateDialog.Companion.getTimeStr2(null, endTime, resources, "");
    }
}
