package com.jd.oa.joywork.common

import android.content.Context
import android.content.res.Resources
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.create.JoyWorkCreateActivity
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.joywork.detail.shareJoyWorkWhenCreate
import com.jd.oa.joywork.repo.JoyWorkCreateParams
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDraft
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork.team.dialog.CreateDialog.Companion.getTimeStr2
import com.jd.oa.joywork.team.dialog.DialogSupporter
import com.jd.oa.joywork.view.ExecutorCallback
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.*
import kotlin.concurrent.thread

/**
 * [JoyWorkShortcutDialog] 与 [CreateDialog] 的公共部分
 * 因为两者 UI 有一些区别，但逻辑又有相同部分，所以进行拆分
 */
object ShortcutDialogCommon {
    // No use, but must be remained, in case you need it later
    fun sendByChat(users: List<JoyWorkUser>, joyWorkDetail: JoyWorkDetail) {
        thread {
            users.forEach {
                if (!it.isSelf()) {
                    joyWorkDetail.shareJoyWorkWhenCreate(it.emplAccount, it.ddAppId, null)
                }
            }
        }
    }

    fun setupSendToChat(itf: ItfShortcutDialogCommon) {
        itf.mSendDD?.run {
            if (isSubJoywork(itf)) {
                itf.mSendDD?.gone()
                return
            }
            val tmpData = itf.tmpData()
            itf.mSendText?.setText(R.string.joywork_send_dd)
            itf.mSendDD?.isVisible = tmpData?.fromDD == true
            itf.mSendDD?.setOnClickListener {
                JDMAUtils.onEventClick(
                    JoyWorkConstant.CREATE_SEND,
                    JoyWorkConstant.CREATE_SEND
                )
                updateSendIcon(!itf.mSendIconChecked, itf)
            }
            /*updateSendIcon(
                tmpData?.fromType in listOf(
                    ShortcutFromType.GROUP_TASK,
                    ShortcutFromType.EXTEND
                ), itf
            )*/
        }
    }

    /**
     * 是否是新建子待办
     */
    private fun isSubJoywork(itf: ItfShortcutDialogCommon): Boolean {
        val tmpData = itf.tmpData()
        return tmpData?.parentId.isLegalString()
    }

    private fun updateSendIcon(checked: Boolean, itf: ItfShortcutDialogCommon) {
        itf.mSendDD?.run {
            itf.mSendIcon?.isSelected = checked
            itf.mSendIconChecked = checked
        }
    }

    /**
     * 设置清除时间按钮点击事件
     */
    fun setTimeClearClick(itf: ItfShortcutDialogCommon) {
        itf.mTimeClear.setOnClickListener {
            val draft = itf.getValue()
            draft.startTime = null
            draft.endTime = null
            draft.duplicateEnum = null
            draft.mAlertType = HashSet()
            JoyWorkShortcutDraft.updateDraftEndTime(draft.endTime)
            JoyWorkShortcutDraft.updateDraftStartTime(draft.startTime)
            afterTimeSelected(itf)
            showQuickSelectTime(itf)
        }
    }

    fun setDupClickListener(supporter: DialogSupporter, itf: ItfShortcutDialogCommon) {
        itf.dupContainer.setOnClickListener {
            val draft = itf.getValue()
            supporter.listDup(draft.duplicateEnum ?: DuplicateEnum.NO) {
                openKeyboard(itf.mTitleView, itf.mTitleView.context)
                if(it != null){
                    draft.duplicateEnum = it
                    handleDupView(itf)
                }
            }

        }
    }

    fun setTimeContainerClickListener(itf: ItfShortcutDialogCommon) {
        itf.mTimeContent.setOnClickListener {
            JDMAUtils.onEventClick(
                JoyWorkConstant.SHORTCUT_DEADLINE,
                JoyWorkConstant.SHORTCUT_DEADLINE
            )
            val draft = itf.getValue()
            draft.type = Value.TYPE_DEADLINE
            if (!draft.hasTime && itf.mTodayContainer.isGone()) {
                showQuickSelectTime(itf)
            } else {
                // 已选择了时间就需要去修改
                itf.getShortcutManager().selectTime(draft) {
                    afterTimeSelected(itf)
                    if (draft.isClean) {
                        showQuickSelectTime(itf)
                    }
                }
            }
        }
    }

    fun setTimeIndicatorClickListener(
        viewIndicator: View,
        itf: ItfShortcutDialogCommon,
    ) {
        val draft = itf.getValue()
        if (draft.hasTime) {
            viewIndicator.gone()
            itf.mTimeContent.visible()
            afterTimeSelected(itf)
        }

        viewIndicator.setOnClickListener {
            JDMAUtils.onEventClick(
                JoyWorkConstant.SHORTCUT_DEADLINE,
                JoyWorkConstant.SHORTCUT_DEADLINE
            )
            val draft = itf.getValue()
            if (!draft.hasTime && itf.mTodayContainer.isGone()) {
                showQuickSelectTime(itf)
            }
            viewIndicator.gone()
            itf.mTimeContent.visible()
        }
        itf.mTimeContent.setOnClickListener {
            // 已选择了时间就需要去修改
            draft.type = Value.TYPE_DEADLINE
            itf.getShortcutManager().selectTime(draft) {
                afterTimeSelected(itf)
                if (draft.isClean) {
                    showQuickSelectTime(itf)
                }
            }
        }
    }

    /**
     * 显示快捷选择今天、明天
     */
    private fun showQuickSelectTime(itf: ItfShortcutDialogCommon) {
        itf.mTodayContainer.visible()
        itf.mTomorrowContainer.visible()
//        itf.mTomorrowContainer.post {
//            itf.mHorScrollView.smoothScrollTo(Int.MAX_VALUE, 0)
//        }
        itf.mTimeTips.setText(R.string.joywork_shortcut_other)
    }

    fun afterTimeSelected(itf: ItfShortcutDialogCommon) {
        handleAlertView(itf)
        handleTimeView(itf)
//        handleTimeIcon(itf)
        handleTimeOtherIcon(itf)
        handleDupView(itf)
        if (itf.showKeyBoardAfterChangePage()) {
            val ctx = itf.mTitleView.context
            openKeyboard(itf.mTitleView, ctx)
        }
    }

    fun setAlertClearClick(supporter: DialogSupporter, itf: ItfShortcutDialogCommon) {

        itf.mAlertContainer.setOnClickListener { view ->
            supporter.listAlerts(itf.getValue().mAlertType, true) {
                if(it != null){
                    itf.getValue().replaceAlertType(it)
                    handleAlertView(itf)
                }
                if (itf.showKeyBoardAfterChangePage()) {
                    openKeyboard(itf.mTitleView, view.context)
                }
            }

        }
    }

    private fun handleAlertView(itf: ItfShortcutDialogCommon) {
        val draft = itf.getValue()
        val context = itf.mAlertContainer.context
        if (!draft.mAlertType.isLegalSet()) {
            itf.mAlertContainer.gone()
        } else if (AlertType.containNO(draft.mAlertType)) {
            itf.mAlertContainer.gone()
        } else {
            itf.mAlertContainer.visible()
            itf.mAlertText.text = AlertType.toString(draft.mAlertType, context)
        }
    }

    // 关闭键盘
    public fun closeKeyboard(et: EditText, activity: Context) {
        val imm = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(et.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        et.clearFocus()
    }

    // 打开键盘
    public fun openKeyboard(et: EditText, ctx: Context) {
        et.postDelayed({
            et.isFocusable = true
            et.isFocusableInTouchMode = true
            et.requestFocus()
            val imm = ctx.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.showSoftInput(et, 0)
//            imm.toggleSoftInput(
//                0,
//                InputMethodManager.HIDE_NOT_ALWAYS
//            )
        }, 200)
    }

    private fun handleDupView(itf: ItfShortcutDialogCommon) {
        val draft = itf.getValue()
        if (!draft.endTime.isLegalTimestamp() || draft.duplicateEnum == null || draft.duplicateEnum == DuplicateEnum.NO) {
            itf.iconDuplicate.gone()
            itf.textDuplicate.gone()
            itf.dupContainer.gone()
        } else {
            itf.dupContainer.visible()
            itf.iconDuplicate.visible()
            itf.textDuplicate.visible()
            itf.textDuplicate.text =
                itf.textDuplicate.context.resources.getString(draft.duplicateEnum.resId)
        }
    }

    fun handleTimeView(itf: ItfShortcutDialogCommon) {
        val draft = itf.getValue()
        val context = itf.mTimeContent.context
        itf.mTimeTips.text = getTimeStr(draft.startTime, draft.endTime, context.resources)
        if (draft.hasTime) {
            itf.mTimeClear.visible()
            itf.mTomorrowContainer.gone()
            itf.mTodayContainer.gone()
        } else {
            itf.mTimeClear.gone()
        }
    }

//    private fun handleTimeIcon(itf: ItfShortcutDialogCommon) {
//        val draft = itf.getValue()
//        if (draft.hasTime) {
//            itf.mTimeIcon.gone()
//        } else {
//            itf.mTimeIcon.visible()
//            itf.mTimeIcon.setText(R.string.icon_padding_othertime)
//        }
//    }

    /**
     * 处理跟截止时间一起的几个 icon
     */
    private fun handleTimeOtherIcon(itf: ItfShortcutDialogCommon) {

        val draft = itf.getValue()
        if (draft.hasTime) {
            itf.mTimeClear.visible()
        } else {
            itf.mTimeClear.gone()
        }
        // 处理重复标签
        if (draft.duplicateEnum == null) {
            itf.mDupIcon.gone()
        } else {
            itf.mDupIcon.visible()
        }
    }

    private fun getTimeStr(
        startTime: Long?,
        endTime: Long?,
        resources: Resources,
    ): String {
        return getTimeStr2(
            startTime,
            endTime,
            resources,
            resources.getString(R.string.joywork_shortcut_deadline_tips)
        ) ?: resources.getString(R.string.joywork_shortcut_deadline_tips)
    }

    fun handleAvatarView(itf: ItfShortcutDialogCommon, context: Context) {
        val draft = itf.getValue()
        val owners = draft.normalOwners
        // 将真负责人位置调整到第一个
        val f = owners.firstOrNull {
            it.chief.isChief()
        }
        if (f != null) {
            owners.remove(f)
            owners.add(0, f)
        }
        itf.mAvatarView.mCallback = object : ExecutorCallback(owners.map {
            it.realName
        }, context) {
            override fun isChief(position: Int): Boolean {
                return draft.normalOwners.safeGet(position)?.chief.isChief()
            }

            override fun getHintText2(
                urls: List<String>,
                context: Context,
                type: Int
            ): Pair<String?, String?>? {
                return if (urls.size == 1) {
                    Pair<String?, String?>(
                        getName(), null
                    )
                } else {
                    null
                }
            }

            override fun getImageAltText(
                urls: List<String>,
                context: Context,
                position: Int,
                type: Int
            ): Pair<String?, String?>? {
                return null
            }
        }
        itf.mAvatarView.setUrls(owners.map {
            it.headImg
        })
        if (owners.isLegalList()) {
            itf.mExecutorArrow?.visible()
        } else {
            itf.mExecutorArrow?.gone()
        }
        updateSendIcon(itf.mSendIconChecked, itf)
        if (itf.showKeyBoardAfterChangePage()) {
            openKeyboard(itf.mTitleView, itf.mTitleView.context)
        }
        handleAssignContainer(itf)
    }

    fun handleAssignContainer(itf: ItfShortcutDialogCommon) {
        val draft = itf.getValue()
        if (!itf.showGroup()) {
            itf.mAssignContainer.gone()
            itf.mAssignContainerDivider?.gone()
            return
        }
        // 自己不是执行人时且配置需要清除时，隐藏入口
        if (!draft.hasSelf() && itf.tmpData()?.hideGroups.isTrue()) {
            itf.mAssignContainer.gone()
            itf.mAssignContainerDivider?.gone()
        } else {
            itf.mAssignContainer.visible()
            itf.mAssignContainerDivider?.visible()
        }
    }

    fun addCommonNetParams(itf: ItfShortcutDialogCommon, params: JoyWorkCreateParams) {
        val mValue = itf.getValue()
        params.owners = mValue.normalOwners
        params.alertTypes = itf.getValue().mAlertType
        params.title = mValue.title
        params.dupEnum = mValue.duplicateEnum
        params.startTime = mValue.startTime
        params.endTime = mValue.endTime

        params.content = mValue.extra
        params.bizCode = itf.tmpData()?.bizCode
        params.inventory = itf.tmpData()?.sessionId
        params.tripartiteName = itf.tmpData()?.tripartiteName
//        params.contentType = mValue.extraType
    }

    fun inflateCreateActivity(itf: ItfShortcutDialogCommon) {
        val mValue = itf.getValue()
        JoyWorkCreateActivity.owners = mValue.normalOwners
        JoyWorkCreateActivity.duplicateEnum = mValue.duplicateEnum
        JoyWorkCreateActivity.mTs = HashSet<AlertType>()
        JoyWorkCreateActivity.mTs.addAll(mValue.mAlertType)
    }

    fun initValue(itf: ItfShortcutDialogCommon, draftInput: JoyWorkShortcutDraft?) {
        val tmpData = itf.tmpData()
        val mValue = itf.getValue()

        if (tmpData?.owners.isLegalList()) {
            mValue.normalOwners.addAll(tmpData?.owners!!)
        }
        if (tmpData?.extra.isLegalString()) {
            mValue.extra = tmpData?.extra!!
        }
        val draft = draftInput ?: return
        if (draft.title.isLegalString()) {
            mValue.updateTitle(draft.title)
        }
        if (draft.startTime.isLegalTimestamp()) {
            mValue.startTime = draft.startTime
        }
        if (draft.endTime.isLegalTimestamp()) {
            mValue.endTime = draft.endTime
        }
    }
}

interface ItfShortcutDialogCommon {

    fun getValue(): Value

    //ShortcutCreator和CreateDialog融合时添加，后续缕清这块逻辑
    fun showGroup(): Boolean

    fun getShortcutManager(): ShortcutManager

    fun tmpData(): ShortcutDialogTmpData?

    /**
     * 在选择完截止时间、执行人后是否要自动弹出键盘
     */
    fun showKeyBoardAfterChangePage(): Boolean

    var mSendIconChecked: Boolean

    val mTodayContainer: View
    val mTomorrowContainer: View
    val mTimeTips: TextView
    val mTimeContent: View
    val mTimeClear: IconFontView
    val mAlarmTime: IconFontView?

    // 2022年05月09日  本次迭代重复标记不在这里
    val mDupIcon: IconFontView?
    val mTimeIcon: TextView
    val mHorScrollView: HorizontalScrollView

    val iconDuplicate: IconFontView
    val textDuplicate: TextView
    val dupContainer: View

    val mExecutorArrow: View?
    val mAvatarView: JoyWorkAvatarView

    val mSendIcon: ImageView?
    val mSendDD: View?
    val mSendText: TextView?

    val mTitleView: EditText

    val mAlertContainer: ViewGroup
    val mAlertIcon: TextView
    val mAlertText: TextView

    val mAssignContainer: View?
    val mAssignContainerDivider: View?


}