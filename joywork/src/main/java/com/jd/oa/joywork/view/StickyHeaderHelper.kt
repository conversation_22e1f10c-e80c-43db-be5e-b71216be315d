package com.jd.oa.joywork.view

import android.content.Context
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

internal class StickyHeaderHelper(
    val recyclerView: RecyclerView?,
    private val headContainerCreator: HeaderContainerCreator? = null
) {

    companion object {
        private const val INVALID_POSITION: Int = -1
        const val NO_ELEVATION: Int = -1
        private const val DEFAULT_ELEVATION: Int = 5
    }

    private var currentViewHolder: RecyclerView.ViewHolder? = null
    private var currentHeader: View? = null

    private var checkMargins = false

    private var mHeaderPositions: List<Int>? = null

    private var orientation = 0
    private var dirty = false

    private var lastBoundPosition = INVALID_POSITION
    private var headerElevation = NO_ELEVATION.toFloat()
    private var cachedElevation = NO_ELEVATION

    private val viewFactory = ViewHolderFactory()

    private val visibilityObserver = OnGlobalLayoutListener {
        recyclerView?.run {
            if (currentHeader != null) {
                currentHeader!!.visibility = visibility
            }
        }
    }

    init {
        checkMargins = recyclerViewHasPadding()
    }

    fun setHeaderPositions(headerPositions: List<Int>?) {
        this.mHeaderPositions = headerPositions
    }

    /**
     * @param firstVisiblePosition 第一个可见item
     * @param visibleHeaders       当前可见的所有header位置
     * @param viewFactory          header视图构造器
     * @param atTop                第0个item完全可见
     */
    fun updateHeaderState(
        firstVisiblePosition: Int,
        visibleHeaders: Map<Int, View>,
        atTop: Boolean
    ) {
        val headerPositionToShow = if (atTop) INVALID_POSITION else getHeaderPositionToShow(
            firstVisiblePosition,
            visibleHeaders[firstVisiblePosition]
        )
        val headerToCopy = visibleHeaders[headerPositionToShow]
        if (headerPositionToShow != lastBoundPosition) {
            if (headerPositionToShow == INVALID_POSITION || (checkMargins && headerAwayFromEdge(
                    headerToCopy
                ))
            ) {
                // 如果header刚好贴边，就无需加入
                dirty = true
                safeDetachHeader()
                lastBoundPosition = INVALID_POSITION
            } else {
                // 否则就创建一个header视图
                lastBoundPosition = headerPositionToShow
                val viewHolder: RecyclerView.ViewHolder? =
                    viewFactory.getViewHolderForPosition(recyclerView, headerPositionToShow)
                if (viewHolder != null) {
                    attachHeader(viewHolder, headerPositionToShow)
                }
            }
        } else if (checkMargins && headerAwayFromEdge(headerToCopy)) {
            detachHeader()
            lastBoundPosition = INVALID_POSITION
        }
        checkHeaderPositions(visibleHeaders)
        recyclerView?.post { checkElevation() }
    }

    private fun checkHeaderPositions(visibleHeaders: Map<Int, View>) {
        if (currentHeader == null) return
        if (currentHeader!!.height == 0) {
            waitForLayoutAndRetry(visibleHeaders)
            return
        }
        var reset = true
        for ((key, nextHeader) in visibleHeaders) {
            if (key <= lastBoundPosition) {
                continue
            }
            reset = offsetHeader(nextHeader) == -1f
            break
        }
        if (reset) {
            resetTranslation()
        }
        currentHeader!!.visibility = View.VISIBLE
    }

    fun setElevateHeaders(dpElevation: Int) {
        if (dpElevation != NO_ELEVATION) {
            cachedElevation = dpElevation
        } else {
            headerElevation = NO_ELEVATION.toFloat()
            cachedElevation = NO_ELEVATION
        }
    }

    fun reset(orientation: Int) {
        this.orientation = orientation
        lastBoundPosition = INVALID_POSITION
        dirty = true
        safeDetachHeader()
    }

    fun clearHeader() {
        detachHeader()
    }


    private fun offsetHeader(nextHeader: View): Float {
        val shouldOffsetHeader = shouldOffsetHeader(nextHeader)
        var offset = -1f
        if (shouldOffsetHeader) {
            if (orientation == LinearLayoutManager.VERTICAL) {
                offset = -(currentHeader!!.height - nextHeader.y)
                currentHeader!!.translationY = offset
            } else {
                offset = -(currentHeader!!.width - nextHeader.x)
                currentHeader!!.translationX = offset
            }
        }
        return offset
    }

    private fun shouldOffsetHeader(nextHeader: View): Boolean {
        return if (orientation == LinearLayoutManager.VERTICAL) {
            nextHeader.y < currentHeader!!.height
        } else {
            nextHeader.x < currentHeader!!.width
        }
    }

    private fun resetTranslation() {
        if (orientation == LinearLayoutManager.VERTICAL) {
            currentHeader!!.translationY = 0f
        } else {
            currentHeader!!.translationX = 0f
        }
    }

    private fun getHeaderPositionToShow(
        firstVisiblePosition: Int, headerForPosition: View?
    ): Int {
        var headerPositionToShow = INVALID_POSITION
        if (headerIsOffset(headerForPosition)) {
            val offsetHeaderIndex = mHeaderPositions!!.indexOf(firstVisiblePosition)
            if (offsetHeaderIndex > 0) {
                return mHeaderPositions!![offsetHeaderIndex - 1]
            }
        }
        for (headerPosition in mHeaderPositions!!) {
            if (headerPosition <= firstVisiblePosition) {
                // 寻找第一个可见的 item 所关联的 header 的位置
                headerPositionToShow = headerPosition
            } else {
                break
            }
        }
        return headerPositionToShow
    }

    private fun headerIsOffset(headerForPosition: View?): Boolean {
        return headerForPosition != null
                && (if (orientation == LinearLayoutManager.VERTICAL)
            headerForPosition.y > 0
        else
            headerForPosition.x > 0)
    }

    private fun attachHeader(viewHolder: RecyclerView.ViewHolder, headerPosition: Int) {
        if (currentViewHolder === viewHolder) {
            recyclerView?.adapter?.onBindViewHolder(currentViewHolder!!, headerPosition)
            currentViewHolder!!.itemView.requestLayout()
            checkTranslation()
            dirty = false
            return
        }
        detachHeader()
        this.currentViewHolder = viewHolder
        recyclerView?.adapter?.onBindViewHolder(currentViewHolder!!, headerPosition)
        this.currentHeader = currentViewHolder!!.itemView
        resolveElevationSettings(currentHeader!!.context)
        currentHeader!!.visibility = View.INVISIBLE
        recyclerView?.getViewTreeObserver()?.addOnGlobalLayoutListener(visibilityObserver)
        getHeaderContainer()?.addView(currentHeader)
        if (checkMargins) {
            updateLayoutParams(currentHeader)
        }
        dirty = false
    }

    private fun currentDimension(): Int {
        if (currentHeader == null) {
            return 0
        }
        return if (orientation == LinearLayoutManager.VERTICAL) {
            currentHeader!!.height
        } else {
            currentHeader!!.width
        }
    }

    private fun headerHasTranslation(): Boolean {
        if (currentHeader == null) {
            return false
        }
        return if (orientation == LinearLayoutManager.VERTICAL) {
            currentHeader!!.translationY < 0
        } else {
            currentHeader!!.translationX < 0
        }
    }

    private fun updateTranslation(diff: Int) {
        if (currentHeader == null) {
            return
        }
        if (orientation == LinearLayoutManager.VERTICAL) {
            currentHeader!!.translationY = currentHeader!!.translationY + diff
        } else {
            currentHeader!!.translationX = currentHeader!!.translationX + diff
        }
    }

    private fun checkTranslation() {
        val view = currentHeader ?: return
        view.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            var previous: Int = currentDimension()

            override fun onGlobalLayout() {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                    view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                } else {
                    view.viewTreeObserver.removeGlobalOnLayoutListener(this)
                }
                if (currentHeader == null) return

                val newDimen = currentDimension()
                if (headerHasTranslation() && previous != newDimen) {
                    updateTranslation(previous - newDimen)
                }
            }
        })
    }

    private fun checkElevation() {
        if (headerElevation != NO_ELEVATION.toFloat() && currentHeader != null) {
            if (orientation == LinearLayoutManager.VERTICAL && currentHeader!!.translationY == 0f
                || orientation == LinearLayoutManager.HORIZONTAL && currentHeader!!.translationX == 0f
            ) {
                elevateHeader()
            } else {
                settleHeader()
            }
        }
    }

    private fun elevateHeader() {
        if (currentHeader!!.tag != null) {
            return
        }
        currentHeader!!.tag = true
        currentHeader!!.animate().z(headerElevation)
    }

    private fun settleHeader() {
        if (currentHeader!!.tag != null) {
            currentHeader!!.tag = null
            currentHeader!!.animate().z(0f)
        }
    }

    private fun detachHeader() {
        if (currentHeader != null) {
            getHeaderContainer()?.removeView(currentHeader)
            clearVisibilityObserver()
            currentHeader = null
            currentViewHolder = null
        }
    }

    fun clearVisibilityObserver() {
        recyclerView?.getViewTreeObserver()?.removeOnGlobalLayoutListener(visibilityObserver)
    }

    private fun updateLayoutParams(currentHeader: View?) {
        val params = currentHeader!!.layoutParams as MarginLayoutParams
        val leftMargin =
            if (orientation == LinearLayoutManager.VERTICAL) (recyclerView?.paddingLeft
                ?: 0) else 0
        val topMargin =
            if (orientation == LinearLayoutManager.VERTICAL) 0 else (recyclerView?.paddingTop ?: 0)
        val rightMargin =
            if (orientation == LinearLayoutManager.VERTICAL) (recyclerView?.paddingRight
                ?: 0) else 0
        params.setMargins(leftMargin, topMargin, rightMargin, 0)
    }

    private fun headerAwayFromEdge(headerToCopy: View?): Boolean {
        return headerToCopy != null && (if (orientation == LinearLayoutManager.VERTICAL)
            headerToCopy.y > 0 else headerToCopy.x > 0)
    }

    private fun recyclerViewHasPadding(): Boolean {
        return recyclerView?.run {
            getPaddingLeft() > 0 || getPaddingRight() > 0 || paddingTop > 0
        } ?: false
    }

    private fun getHeaderContainer(): ViewGroup? {
        return headContainerCreator?.invoke().takeIf {
            it != null
        } ?: recyclerView?.parent as ViewGroup?
    }


    private fun waitForLayoutAndRetry(visibleHeaders: Map<Int, View>) {
        val view = currentHeader ?: return
        view.viewTreeObserver.addOnGlobalLayoutListener(
            object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (currentHeader == null) return
                    getHeaderContainer()?.requestLayout()
                    checkHeaderPositions(visibleHeaders)
                }
            })
    }

    private fun safeDetachHeader() {
        getHeaderContainer()?.post {
            if (dirty) {
                detachHeader()
            }
        }
    }

    private fun resolveElevationSettings(context: Context) {
        if (cachedElevation != NO_ELEVATION && headerElevation == NO_ELEVATION.toFloat()) {
            headerElevation = dp2px(context, cachedElevation)
        }
    }

    private fun dp2px(context: Context, dp: Int): Float {
        val scale = context.resources.displayMetrics.density
        return dp * scale
    }

}

internal class ViewHolderFactory {

    private var currentViewHolder: RecyclerView.ViewHolder? = null
    private var currentViewType: Int = -1


    fun getViewHolderForPosition(
        recyclerView: RecyclerView?,
        position: Int
    ): RecyclerView.ViewHolder? {
        recyclerView?.adapter?.run {
            if (currentViewType != getItemViewType(position)) {
                currentViewType = getItemViewType(position)
                currentViewHolder =
                    createViewHolder(recyclerView.parent as ViewGroup, currentViewType)
            }
        }
        return currentViewHolder
    }
}