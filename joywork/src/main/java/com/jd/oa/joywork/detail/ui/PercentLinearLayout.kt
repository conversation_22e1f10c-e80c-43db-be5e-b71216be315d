package com.jd.oa.joywork.detail.ui

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout

/**
 * 设置第一个 View 最大百分比水平布局
 * 它的宽度不能设置成 wrap_content，否则会出现错误。能解决这个问题，但不想处理判断逻辑，that's all！
 * <AUTHOR>
 */
class PercentLinearLayout(context: Context, attributeSet: AttributeSet) : LinearLayout(context, attributeSet) {
    var updateFunction: ((data: Any) -> Unit)? = null

    private val maxPercent = 0.5f
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val first = getChildAt(0)
        val second = getChildAt(1)
        if (first.measuredWidth > measuredWidth * maxPercent) {
            val maxW = (measuredWidth * maxPercent).toInt()
            val newSpec = MeasureSpec.makeMeasureSpec(maxW, MeasureSpec.EXACTLY)
            first.measure(newSpec, MeasureSpec.makeMeasureSpec(first.measuredHeight, MeasureSpec.EXACTLY))
            second.measure(MeasureSpec.makeMeasureSpec(measuredWidth - maxW, MeasureSpec.AT_MOST), MeasureSpec.makeMeasureSpec(second.measuredHeight, MeasureSpec.EXACTLY))
        }
    }
}