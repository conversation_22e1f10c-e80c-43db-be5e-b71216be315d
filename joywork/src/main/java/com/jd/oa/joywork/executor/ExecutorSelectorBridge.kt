package com.jd.oa.joywork.executor

import android.app.Activity
import android.content.Context
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.key
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.TabletUtil

class ExecutorSelectorBridge(
    private val activity: Activity,
    val selected: ArrayList<MemberEntityJd>
) : SetOwnerActivityCallback {
    private val selectedIds = HashSet<String>()

    init {
        selected.forEach {
            selectedIds.add(it.key())
        }
    }

    private var callbackHashCode: Int? = null

    var onSuccess: ((selected: ArrayList<MemberEntityJd?>?) -> Unit)? = null
    var onFailure: (() -> Unit)? = null

    val callback = object : Callback<ArrayList<MemberEntityJd?>?> {
        override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
            val s = selected ?: return
            val ss = ArrayList<MemberEntityJd>()
            val key = owner?.key() ?: "-"
            s.forEach {
                if (!(it == null || selectedIds.contains(it.key()))) {
                    ss.add(it)
                    if (it.key() == key) {
                        it.chief = 1
                    } else {
                        it.chief = 0
                    }
                }
            }
            SetOwnerActivity.select = ss
            SetOwnerActivity.callback = this@ExecutorSelectorBridge
            if (TabletUtil.isSplitMode(activity)) {
                val ctx = context
                if (ctx is Activity) {
                    SetOwnerActivity.start(ctx)
                    context = null
                } else {
                    SetOwnerActivity.start(activity)
                }
            } else {
                SetOwnerActivity.start(activity)
            }
        }

        override fun onFail() {
            onFailure?.invoke()
        }
    }
    val hashCodeCallback = object : Callback<Int> {
        override fun onSuccess(hashCode: Int?) {
            <EMAIL> = hashCode
        }

        override fun onFail() {

        }
    }

    private var context: Context? = null

    val contextCallback = object : Callback<Context> {
        override fun onSuccess(bean: Context?) {
            context = bean
        }

        override fun onFail() {
        }

    }

    // called from 【SetOwnerActivity】start
    override fun finish(activity: SetOwnerActivity) {
        SetOwnerActivity.callback = null
        SetOwnerActivity.select = null
        activity.finish()
    }

    override fun add(activity: SetOwnerActivity) {
        SetOwnerActivity.callback = null
        SetOwnerActivity.select = null
        activity.finish()
    }

    override fun sure(
        members: ArrayList<MemberEntityJd>?,
        activity: SetOwnerActivity,
        owner: JoyWorkUser?
    ) {
        SetOwnerActivity.callback = null
        SetOwnerActivity.select = null
        activity.finish()
        val ms = ArrayList<MemberEntityJd?>()
        val key = owner?.key() ?: "-"
        members?.forEach {
            ms.add(it)
            if (it.key() == key) {
                it.chief = 1
            } else {
                it.chief = 0
            }
        }
        onSuccess?.invoke(ms)
        val service = AppJoint.service(ImDdService::class.java)
        service.closeSelector()
        service.unregisterCallback(callbackHashCode)
    }

    private var owner: JoyWorkUser? = null

    override fun changeOwner(owner: JoyWorkUser?) {
        this.owner = owner
    }

    override fun remove(owner: JoyWorkUser) {

    }

    override fun addUser(joyWorkUser: JoyWorkUser) {

    }
    // called from 【SetOwnerActivity】end
}