package com.jd.oa.joywork.filter

import android.content.Context
import com.jd.oa.joywork.*
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.utils.DateShowUtils
import java.util.*
import kotlin.collections.ArrayList

object FilterValueEx {

    fun getUIString(
        filterValue: FilterValue,
        context: Context,
        newLine: Boolean = false,
        model: TeamDetailViewModel
    ): String {
        val array = ArrayList<String>()
        getUIString(filterValue, context, newLine, model, array)
        return array.joinLegalString(": ")
    }

    fun getUIString(
        filterValue: FilterValue,
        context: Context,
        newLine: Boolean = false,
        model: TeamDetailViewModel,
        result: ArrayList<String>
    ) {
        val enum = filterValue.enum ?: ProjectFilterEnum.SCREEN_NULL
        if (enum == ProjectFilterEnum.SCREEN_NULL) {
            result.add(context.getString(R.string.joywork_project_setting_filter))
        } else if (enum == ProjectFilterEnum.SCREEN_CUSTOM) {
            // 按清单自定义字段筛选
            val filterExt = filterValue.value
            // 此时理论上应该有扩展字段，然而以为万一
            if (filterExt == null || !filterExt.columnId.isLegalString() || !filterExt.detailId.isLegalString()) {
                model.updateFilter(FilterValue.getNullInstance())
                result.add(context.getString(R.string.joywork_project_setting_filter))
            } else {
                // ci 指按哪一个自定义字段筛选
                val ci = filterExt.columnId!!
                // di 指按自定义字段中的哪一项筛选
                val di = filterExt.detailId!!
                val field = model.extLiveData.value?.customFields?.firstOrNull {
                    Objects.equals(it.columnId, ci)
                }
                if (field == null || !field.details.isLegalList()) {
                    // 没有当前设置的自定义字段或者选项
                    model.updateFilter(FilterValue.getNullInstance())
                    result.add(context.getString(R.string.joywork_project_setting_filter))
                } else {
                    val d = field.details.firstOrNull {
                        Objects.equals(it.detailId, di)
                    }
                    if (d == null) {
                        // 没有当前设置的自定义字段或者选项
                        model.updateFilter(FilterValue.getNullInstance())
                        result.add(context.getString(R.string.joywork_project_setting_filter))
                    } else {
                        result.add(field.title)
                        result.add(d.itemContent.content)
                    }
                }
            }
        } else if (enum == ProjectFilterEnum.SCREEN_PRIORITY) {
            val p = filterValue.value?.priority
            if (p == null) {
                // 没有配置优先级选项
                model.updateFilter(FilterValue.getNullInstance())
                result.add(context.getString(R.string.joywork_project_setting_filter))
            } else {
                val level = JoyWorkLevel.NO.getLevel(p)
                result.add(context.getString(enum.stringId()))
                result.add(context.getString(level.getResId()))
            }
        } else if (enum == ProjectFilterEnum.SCREEN_EXECUTOR) {
            val appId = filterValue.value?.ddAppId
            val erp = filterValue.value?.emplAccount
            if (!appId.isLegalString() || !erp.isLegalString()) {
                // 按执行人搜，但没有给相应人员信息
                model.updateFilter(FilterValue.getNullInstance())
                result.add(context.getString(R.string.joywork_project_setting_filter))
            } else {
                val user = filterValue.value?.realName ?: ""
                result.add(context.getString(enum.stringId()))
                result.add(user)
            }
        } else if (enum == ProjectFilterEnum.SCREEN_EDN_TIME) {
            val endTime = filterValue.value?.endTime
            val startTime = filterValue.value?.startTime
            if (endTime.isLegalTimestamp()) {
                if (startTime.isLegalTimestamp()) {
                    val endS = DateShowUtils.getSimpleTimeShowTextWithHourMinute(endTime!!)
                    val startS = DateShowUtils.getSimpleTimeShowTextWithHourMinute(startTime!!)
                    val d = if (newLine) " -\n" else " - "
                    result.add(context.getString(enum.stringId()))
                    result.add("$startS$d$endS")
                } else {
                    val endS = DateShowUtils.getSimpleTimeShowTextWithHourMinute(endTime!!)
                    result.add(context.getString(enum.stringId()))
                    result.add(endS)
                }
            } else {
                model.updateFilter(FilterValue.getNullInstance())
                result.add(context.getString(R.string.joywork_project_setting_filter))
            }
        } else if (enum == ProjectFilterEnum.SCREEN_PROJECT) {
            val title = filterValue.value?.title
            result.add(context.getString(enum.stringId()))
            if (title.isLegalString()) {
                result.add(title!!)
            }
        } else {
            result.add(context.getString(enum.stringId()))
        }
    }
}