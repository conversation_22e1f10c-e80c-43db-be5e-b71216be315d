package com.jd.oa.joywork

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.StringRes
import com.jd.oa.AppBase
import com.jd.oa.ui.dialog.bottomsheet.BaseDialog
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog
import com.jd.oa.ui.dialog.bottomsheet.DialogConfig
import com.jd.oa.ui.dialog.bottomsheet.BottomAction
import com.jd.oa.joywork.detail.DialogManager.corner
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.joywork.dialog.thirdparty.*
import com.jd.oa.utils.*
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.ui.dialog.IShitTitleDecorator

object JoyWorkDialog {
    fun showAlertDialog(context: Context, msgId: Int, okResId: Int, okClick: (Dialog) -> Unit) {
        showAlertDialog(context, context.string(msgId), context.string(okResId), okClick)
    }

    fun showAlertDialog(context: Context, msgId: Int, okClick: (Dialog) -> Unit) {
        showAlertDialog(context, msgId, R.string.me_ok, okClick)
    }

    fun showAlertDialog(msg: String, okClick: (Dialog) -> Unit) {
        val activity = AppBase.getTopActivity()
        if (activity == null || activity.isFinishing || activity.isDestroyed) {
            return
        }
        showAlertDialog(activity, msg, okClick)
    }

    fun showAlertDialog(context: Context, msg: String, okClick: (Dialog) -> Unit) {
        showAlertDialog(context, msg, context.string(R.string.me_ok), okClick)
    }

    fun showAlertDialog(context: Context, msg: String, okRes: String, okClick: (Dialog) -> Unit) {
        val dialog = ShitAlertDialog(context, ShitAlertDialogConfig<ShitAlertDialog>().apply {
            this.okShitClick = okClick
            msgStr = msg
            okStr = okRes
        })
        dialog.show()
    }

    fun showAlertDialog(
        context: Context,
        title: String,
        subMessage: String?,
        @StringRes okRes: Int,
        @StringRes cancelResource: Int,
        okClick: (Dialog) -> Unit,
        cancelClick: (View, Dialog) -> Unit
    ) {
        val dialog = ShitAlertDialog(context, ShitAlertDialogConfig<ShitAlertDialog>().apply {
            this.okShitClick = okClick
            this.cancelShitClick = cancelClick
            msgStr = title
            subMsg = subMessage
            okStr = context.string(okRes)
            cancelRes = cancelResource
        })
        dialog.show()
    }

    fun showAlertDialog(
        context: Context,
        msg: String,
        @StringRes okRes: Int,
        @StringRes cancelResource: Int,
        okClick: (Dialog) -> Unit,
        cancelClick: (View, Dialog) -> Unit
    ) {
        val dialog = ShitAlertDialog(context, ShitAlertDialogConfig<ShitAlertDialog>().apply {
            this.okShitClick = okClick
            this.cancelShitClick = cancelClick
            msgStr = msg
            okStr = context.string(okRes)
            cancelRes = cancelResource
        })
        dialog.show()
    }

    fun showAlertDialog(
        msg: Int,
        okRes: Int,
        cancelRes: Int,
        okClick: (Dialog) -> Unit,
        cancelClick: (View, Dialog) -> Unit
    ) {
        val activity = AppBase.getTopActivity()
        if (activity == null || activity.isFinishing || activity.isDestroyed) {
            return
        }
        showAlertDialog(activity, activity.string(msg), okRes, cancelRes, okClick, cancelClick)
    }


    /**
     * 重复待办选择框
     */
    fun showDuplicateDialog(
        context: Context,
        level: DuplicateEnum,
        click: (DuplicateEnum) -> Unit
    ): ListShitDialog {
        val data = DuplicateEnum.values().toList()
        val dialog = ListShitDialog(
            context,
            object : ListShitDialogAdapter<DuplicateEnum>(data) {
                override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                    val view = context.layoutInflater.inflate(
                        R.layout.joywork_dup_item,
                        parent,
                        false
                    )
                    val tv: TextView = view.findViewById(R.id.content)
                    val d: DuplicateEnum = items[position]
                    tv.setText(d.resId)
                    view.tag = d
                    view.setOnClickListener {
                        dialog?.dismiss()
                        val tagData = it.tag as DuplicateEnum
                        click.invoke(tagData)
                    }
                    val check = view.findViewById<TextView>(R.id.check)
                    if (level == d) {
                        check.visibility = View.VISIBLE
                        tv.bold()
                    } else {
                        check.visibility = View.GONE
                        tv.normal()
                    }
                    return view
                }
            },
            ListShitDialogConfig().apply {
                contentBackground = DrawableEx.solidRect(Color.WHITE)
            })
        dialog.titleController = TextShitTitleController(object : IShitTitleDecorator<TextView> {
            override fun decorate(t: TextView) {
                t.gravity = Gravity.CENTER
                t.setTextColor(Color.parseColor("#FF232930"))
                t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16.0f)
                t.setText(R.string.joywork_duplicate_text)
                t.background =
                    DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
            }
        })
        dialog.actionController = DividerShitActionController
        dialog.show()
        return dialog
    }

    /**
     * 所属清单
     */
    fun showTaskProjectsDialog(
        context: Context,
        ps: List<JoyWorkDetail.Project>,
        dd: () -> Unit, // 关联清单回调
        remove: (JoyWorkDetail.Project) -> Unit
    ): BaseDialog {
        val data = mutableListOf<JoyWorkDetail.Project>()
        data.addAll(ps)
        val list =
            data.map {
                BottomAction(
                    iconStyle = R.style.BottomSheetDialogIcon_Project,
                    title = (it.title + "-" + it.groupTitle.ifEmpty { context.getString(R.string.joywork_task_link_untitled_group) }).trim(),
                    checkClick = true,
                    data = it
                )
            }.toMutableList()
        val config = DialogConfig().apply {
            height = DensityUtil.dp2px(context, 406f)
        }
        var sheetDialog: JoyBottomSheetDialog<JoyWorkDetail.Project>? = null
        val builder = JoyBottomSheetDialog.Builder<JoyWorkDetail.Project>(context).apply {
            setActions(list)
            setTitle(context.resources.getString(R.string.joywork_self_order))
            setConfig(config)
            setCheckStyle(R.style.BottomSheetDialogCheck_Close)
            setPositiveButton(R.string.joywork2_add) { dd.invoke() }
            setSubTitle(
                context.resources.getString(
                    R.string.joywork2_associated_project_number,
                    list.size
                )
            )
            setOnItemClickListener(false) { action, position ->
                remove.invoke(data[position])
                list.removeIf { action == it }
                sheetDialog?.refreshSubTitle(
                    context.resources.getString(
                        R.string.joywork2_associated_project_number,
                        list.size
                    )
                )
                if (list.isEmpty()) {
                    sheetDialog?.dismiss()
                }
            }
        }
        sheetDialog = JoyBottomSheetDialog(context, builder)
        sheetDialog.show()
        return sheetDialog
    }
}