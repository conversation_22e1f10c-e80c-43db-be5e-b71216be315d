package com.jd.oa.joywork.team.kpi

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.*
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

sealed class VisibleSettingSplinter(
    val context: Context,
    val visibleUserLiveData: VisibleUserLiveData?
) {
    protected var mPartArrow: View? = null
    protected var mRv: RecyclerView? = null

    open fun dataChange() {

    }

    fun buildView(parent: ViewGroup) {
        val subView =
            context.inflater.inflate(R.layout.joywork_visible_setting_splinter, parent, false)
        mPartArrow = subView.findViewById(R.id.mPartArrow)
        val title = subView.findViewById<TextView>(R.id.mSubTitle)
        val titleS = getSubTitle()
        if (titleS.isLegalString()) {
            title.visible()
            title.text = titleS
        } else {
            title.gone()
        }
        val rv = subView.findViewById<RecyclerView>(R.id.mSubRv)
        mRv = rv
        rv.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        parent.addView(subView)
        onBuildView(subView)
        onBuildRv(rv)
    }

    abstract fun getSubTitle(): String

    open fun onBuildView(view: View) {

    }

    open fun onBuildRv(rv: RecyclerView) {

    }
}

interface DepartmentSplinterCallback {
    fun getDeptData(): List<DeptInfo>?
    fun afterSelectedDept(ds: List<DeptInfo>)
}

class DepartmentSplinter(
    private val activity: AppCompatActivity,
    private val callback: DepartmentSplinterCallback,
    visibleUserLiveData: VisibleUserLiveData?
) :
    VisibleSettingSplinter(activity, visibleUserLiveData) {

    private val itemClick = View.OnClickListener {
        if (mPartArrow?.isVisible().isTrue()) {
            VisibleDepartmentsListActivity.start(VISIBLE_USER_LIVE_DATA_TOKEN, activity)
        }
    }

    override fun getSubTitle(): String {
        return activity.getString(R.string.joywork_visible_department)
    }

    override fun onBuildView(view: View) {
        view.setOnClickListener(itemClick)
    }

    override fun dataChange() {
        showPartSuccessUI()
    }

    private fun showPartSuccessUI() {
        if (callback.getDeptData()?.isNotEmpty().isTrue()) {
            mPartArrow.visible()
        } else {
            mPartArrow.gone()
        }
        (mRv?.adapter as? DepartmentAdapter)?.replaceDepts(callback.getDeptData())
        mRv?.requestLayout()
    }

    override fun onBuildRv(rv: RecyclerView) {
        rv.adapter =
            DepartmentAdapter(context, callback.getDeptData() ?: mutableListOf(), ::addUser)
    }

    private fun addUser(view: View) {
        val jds = ArrayList<DeptInfo>()
        callback.getDeptData()?.forEach {
            jds.add(it)
        }
        DepartmentSearchActivity.start(activity, VISIBLE_USER_LIVE_DATA_TOKEN)
    }

    private class DepartmentAdapter(
        private val context: Context,
        us: List<DeptInfo>,
        private val addClick: View.OnClickListener
    ) :
        RecyclerView.Adapter<RecyclerView.ViewHolder>() {
        // 2022年11月04日：only two elements
        private val data = mutableListOf<String>()

        init {
            updateDepts(us)
        }

        fun replaceDepts(depts: List<DeptInfo>?) {
            if (depts.isLegalList()) {
                updateDepts(depts!!)
            } else {
                data.clear()
                data.add("-")
            }
            notifyDataSetChanged()
        }

        private fun updateDepts(depts: List<DeptInfo>) {
            data.clear()
            data.add("-")
            if (depts.isLegalList()) {
                val s = depts.map {
                    it.deptName
                }.joinLegalString(" , ")
                data.add(s)
            }
        }

        class VH(view: View) : RecyclerView.ViewHolder(view) {
            val mTitle = itemView.findViewById<TextView>(R.id.mTitle)
            val add = itemView.findViewById<TextView>(R.id.add)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return VH(
                context.inflater.inflate(
                    R.layout.joywork_visible_setting_dept,
                    parent,
                    false
                )
            )
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is VH) {
                holder.add.setOnClickListener(addClick)
                if(data.size >= 2){
                    holder.mTitle.visible()
                    holder.mTitle.text = data[1]
                }else{
                    holder.mTitle.gone()
                }
            }
        }

        override fun getItemCount() = 1
    }
}

interface UserSplinterCallback {
    fun getData(): List<JoyWorkUser>?
    fun afterSelectedUser(users: List<JoyWorkUser>)
}

class UserSplinter(
    private val activity: AppCompatActivity,
    private val callback: UserSplinterCallback,
    visibleUserLiveData: VisibleUserLiveData?
) : VisibleSettingSplinter(activity, visibleUserLiveData) {

    private val itemClick = View.OnClickListener {
        if (mPartArrow?.isVisible().isTrue()) {
            VisibleUserListActivity.start(VISIBLE_USER_LIVE_DATA_TOKEN, activity)
        }
    }

    override fun onBuildView(view: View) {
        view.setOnClickListener(itemClick)
    }

    override fun dataChange() {
        showPartSuccessUI()
    }

    override fun getSubTitle(): String {
        return activity.getString(R.string.joywork_visible_user_title)
    }

    private fun showPartSuccessUI() {
        if (callback.getData()?.isNotEmpty().isTrue()) {
            mPartArrow.visible()
        } else {
            mPartArrow.gone()
        }
        (mRv?.adapter as? VisibleUserAdapter)?.replaceUsers(callback.getData())
    }

    override fun onBuildRv(rv: RecyclerView) {
        rv.adapter = VisibleUserAdapter(context, callback.getData() ?: mutableListOf(), ::addUser)
    }

    private fun addUser(view: View) {
        val jds = ArrayList<MemberEntityJd>()
        callback.getData()?.forEach {
            val jd = MemberEntityJd()
            jd.fromUser(it)
            jds.add(jd)
        }
        ExecutorUtils.selectVisibleUsers(activity, jds, { selected: ArrayList<MemberEntityJd?>? ->
            val newSelected = mutableListOf<JoyWorkUser>()
            selected?.forEach {
                if (it != null) {
                    val user = JoyWorkUser()
                    user.fromDD(it)
                    newSelected.add(user)
                }
            }
            callback.afterSelectedUser(newSelected)
        }) {

        }
    }

    private class VisibleUserAdapter(
        private val context: Context,
        us: List<JoyWorkUser>,
        private val addClick: View.OnClickListener
    ) :
        RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        private val data = mutableListOf<Any>()

        init {
            updateUsers(us)
        }

        fun replaceUsers(us: List<JoyWorkUser>?) {
            if (us.isLegalList()) {
                updateUsers(us!!)
            } else {
                data.clear()
                data.add("-")
            }
            notifyDataSetChanged()
        }

        private fun updateUsers(us: List<JoyWorkUser>) {
            data.clear()
            data.add("-")
            if (us.isLegalList()) {
                if (us.size > 5) {
                    data.addAll(us.subList(0, 5))
                    if (us.size - 5 >= 100) {
                        data.add("99+")
                    } else {
                        data.add("+${us.size - 5}")
                    }
                } else {
                    data.addAll(us)
                }
            }
        }

        class VH(view: View) : RecyclerView.ViewHolder(view) {
            val mIcon = itemView.findViewById<ImageView>(R.id.mIcon)
        }

        class VH2(view: View) : RecyclerView.ViewHolder(view) {
            val add = itemView.findViewById<TextView>(R.id.add)
        }

        class VH3(view: View) : RecyclerView.ViewHolder(view) {
            val add = itemView.findViewById<TextView>(R.id.add)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return if (viewType == 1) {
                VH(
                    context.inflater.inflate(
                        R.layout.joywork_visible_setting_item,
                        parent,
                        false
                    )
                )
            } else if (viewType == 2) {
                VH2(
                    context.inflater.inflate(
                        R.layout.joywork_visible_setting_item2,
                        parent,
                        false
                    )
                )
            } else {
                VH3(
                    context.inflater.inflate(
                        R.layout.joywork_visible_setting_item2,
                        parent,
                        false
                    )
                )
            }
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is VH) {
                val user = data[position] as JoyWorkUser
                JoyWorkViewItem.avatar(holder.mIcon, user.headImg)
            } else if (holder is VH2) {
                holder.add.setBackgroundResource(R.drawable.joywork_line_circle_dashed)
                holder.add.setText(R.string.icon_prompt_add)
                holder.add.argb("#666666")
                holder.add.setOnClickListener(addClick)
            } else if (holder is VH3) {
                val str = data[position] as String
                holder.add.setBackgroundResource(R.drawable.joywork_all_round_100_e6e6e6)
                holder.add.setText(str)
                holder.add.argb("#333333")
            }
        }

        override fun getItemViewType(position: Int): Int {
            val t = data[position]
            if (t is JoyWorkUser) {
                return 1
            }
            if (position == 0) {
                return 2
            }
            return 3
        }

        override fun getItemCount() = data.size
    }
}