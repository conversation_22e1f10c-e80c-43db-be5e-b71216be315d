package com.jd.oa.joywork.team.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.appcompat.app.AppCompatDialog
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.KpiQ
import com.jd.oa.joywork.team.kpi.KpiQWheelView
import com.jd.oa.utils.inflater

class SelectQDialog(context: Context, private val targets: List<KpiQ>, private val selectPos: Int) :
    AppCompatDialog(context, R.style.JoyWorkDialogStyle) {

    var click = { q: KpiQ ->

    }

    init {
        window?.apply {
            decorView.setBackgroundColor(Color.TRANSPARENT)
            requestFeature(Window.FEATURE_NO_TITLE)
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.BOTTOM
            attributes = layoutParams
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val mContentView: View = context.inflater.inflate(R.layout.joywork_dialog_selectq, null)
        setContentView(mContentView)
        val wheelView = mContentView.findViewById<KpiQWheelView>(R.id.mWheelView)
        wheelView.setData(targets, if (selectPos < 0) 0 else selectPos)
        mContentView.findViewById<View>(R.id.cancel).setOnClickListener {
            dismiss()
        }
        mContentView.findViewById<View>(R.id.ok).setOnClickListener {
            dismiss()
            click.invoke(targets[wheelView.currentItem])
        }
    }
}