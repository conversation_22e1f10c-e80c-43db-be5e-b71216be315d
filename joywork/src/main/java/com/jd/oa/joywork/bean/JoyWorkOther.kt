package com.jd.oa.joywork.bean

import com.jd.oa.joywork.BlockTypeEnum
import com.jd.oa.utils.DateUtils

class JoyWorkLoadMore2(val data: Any) : LoadMoreJoyWork(-1)

/**
 * 显示加载更多
 */
public open class LoadMoreJoyWork(val blockType: Int) : JoyWork() {
    public var isLoading = false
}

public class ProjectLoadMore(val projectId: String, val groupId: String) : LoadMoreJoyWork(
    BlockTypeEnum.TODAY.code.toInt()
) {

}

/**
 * 第二种加载更多。有排序条件且不是分组内排序时使用
 */
class ProjectLoadMore2() : LoadMoreJoyWork(BlockTypeEnum.TODAY.code.toInt()) {

}

/**
 * 显示时间
 */
class TimeTitleJoyWork(val time: String, timeL: Long) : JoyWork() {
    val titleTime = DateUtils.remainDay(timeL)
}


class CardBottomMultiPurpose<T>(val payload: T) : JoyWork() {
    var showNewTask = false
    var showMore = false
    var isLoading = false
}
