package com.jd.oa.joywork.view

import android.content.Context
import android.graphics.PorterDuff
import com.jd.oa.around.widget.refreshlistview.DefaultPullLoadFooter
import com.jd.oa.joywork.R
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

class ProjectListLoadMoreFooter(context: Context) : DefaultPullLoadFooter(context) {
    override fun setComplete() {
        super.setComplete()
        gone()
    }

    override fun setEmpty() {
        super.setEmpty()
        gone()
    }

    override fun setLoaded() {
        visible()
        super.setLoaded()
        mTvText.setText(R.string.joywork2_pull_load_more)
    }

    override fun setLoading() {
        visible()
        super.setLoading()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mPbLoading.indeterminateDrawable.setColorFilter(
            resources.getColor(R.color.joywork_red),
            PorterDuff.Mode.SRC_IN
        );
    }
}