package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.webkit.WebView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.airbnb.lottie.LottieAnimationView
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.fragment.js.hybrid.JsIm
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightObserver
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.common.ParcelableInteractionResult
import com.jd.oa.joywork.common.ParcelableStub
import com.jd.oa.joywork.common.ShortcutDialogCommon
import com.jd.oa.joywork.create.param.GoalCreateParams
import com.jd.oa.joywork.create.param.KRCreateParams
import com.jd.oa.joywork.detail.ui.JsTargetInterface
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.title.CenterTitleFragment
import com.jd.oa.joywork.utils.getParamsKey
import com.jd.oa.joywork.utils.getParamsStr
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import kotlinx.android.synthetic.main.joywork_kpi_target_detail.*
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * 绩效、目标详情
 * There are two deeplinks attached to this activity, the first one is used to open goal detail, other is used to open kr detail.
 * We use the 【type】 parameter carried in 【mparam】 to distinguish usage, not the deeplink
 */
@Route(DeepLink.JOY_WORK_GOAL_DETAIL, DeepLink.JOY_WORK_KR_DETAIL)
class GoalDetailActivity : BaseActivity(), KeyboardHeightObserver {

    companion object {
        val goal_action = "goal.detail.receiver.action"

        private const val KEY_TARGET_ID = "goalId"
        private const val KEY_KR_ID = "krId"
        private const val KEY_EDITABLE = "canEdit"
        private const val KEY_DELETABLE = "canDel"
        private const val KEY_SHARE = "canShare"

        /**
         * 打开目录详情
         */
        fun start(
            activity: Activity,
            targetId: String,
            canEdit: Boolean,
            canDel: Boolean,
            canShare: Boolean,
            requestCode: Int
        ) {
            val i = Intent(activity, GoalDetailActivity::class.java)
            i.putExtra(KEY_TARGET_ID, targetId)
            i.putExtra(KEY_EDITABLE, canEdit)
            i.putExtra(KEY_DELETABLE, canDel)
            i.putExtra(KEY_SHARE, canShare)
            i.putExtra("type", "goal")
            activity.startActivityForResult(i, requestCode)
        }

        /**
         * 打开 kr 详情
         */
        fun kr(
            activity: Activity,
            krId: String,
            canEdit: Boolean,
            canDel: Boolean,
            canShare: Boolean,
            requestCode: Int
        ) {
            val i = Intent(activity, GoalDetailActivity::class.java)
            i.putExtra(KEY_KR_ID, krId)
            i.putExtra(KEY_EDITABLE, canEdit)
            i.putExtra(KEY_DELETABLE, canDel)
            i.putExtra(KEY_SHARE, canShare)
            i.putExtra("type", "kr")
            activity.startActivityForResult(i, requestCode)
        }

        fun GoalDetailActivity.parseDeeplink() {
            // {"goalId":"${goalId}","type":2}
            val params = intent.getParamsStr()
            try {
                val obj = JSONObject(params!!)
                val goalId = obj.getString("goalId")
                var type = 1
                if (obj.has("type")) {
                    type = obj.getInt("type")
                }
                if (type == 1) { // goal
                    intent.putExtra(KEY_TARGET_ID, goalId)
                    intent.putExtra(KEY_EDITABLE, false)
                    intent.putExtra(KEY_DELETABLE, false)
                    intent.putExtra(KEY_SHARE, false)
                    intent.putExtra("type", "goal")
                } else if (type == 2) { // kr
                    intent.putExtra(KEY_KR_ID, goalId)
                    intent.putExtra(KEY_EDITABLE, false)
                    intent.putExtra(KEY_DELETABLE, false)
                    intent.putExtra(KEY_SHARE, false)
                    intent.putExtra("type", "kr")
                }
            } catch (e: Throwable) {
                // pass
            }
        }

        fun registerReturnCallback(context: Context, receiver: BroadcastReceiver) {
            val filter = IntentFilter("return.from.goal.kr.detail")
            LocalBroadcastManager.getInstance(context).registerReceiver(receiver, filter)
        }

        fun registerGoalCallback(context: Context, receiver: BroadcastReceiver) {
            val filter = IntentFilter(goal_action)
            LocalBroadcastManager.getInstance(context).registerReceiver(receiver, filter)
        }

        private fun GoalDetailActivity.isKr(): Boolean {
            return intent.getStringExtra("type") == "kr"
        }

        private fun GoalDetailActivity.restore(bundle: Bundle) {
            if (!intent.hasExtra(KEY_TARGET_ID) && bundle.containsKey(KEY_TARGET_ID)) {
                intent.putExtra(KEY_TARGET_ID, bundle.getString(KEY_TARGET_ID))
            }
        }

        fun GoalDetailActivity.getId(): String {
            return if (isKr()) {
                getIdByKey(KEY_KR_ID)
            } else {
                getIdByKey(KEY_TARGET_ID)
            }
        }

        private fun GoalDetailActivity.getIdByKey(key: String): String {
            var id = intent.getStringExtra(key)
            if (!id.isLegalString()) {
                id = intent.getParamsKey(key, "")
            }
            return id ?: ""
        }

        fun GoalDetailActivity.deletable(): Boolean {
            return intent.getBooleanExtra(KEY_DELETABLE, false)
        }

        fun GoalDetailActivity.editable(): Boolean {
            return intent.getBooleanExtra(KEY_EDITABLE, false)
        }

        fun GoalDetailActivity.sharable(): Boolean {
            return false
//            return intent.getBooleanExtra(KEY_SHARE, false)
        }

        fun parseResultIntent(intent: Intent): ParcelableInteractionResult {
            return ParcelableStub.readFromIntent(intent)
        }
    }

    private var runnable: Runnable? = null
    private var keyBoardShowHeight = 0
    private var lastKeyBoardHeight: Int = 0

    private val handler = Handler()

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null)
                return
            when (intent.action) {
                JsTargetInterface.OPEN_JOYWORK_DETAIL -> {
                    val obj = intent.getSerializableExtra("data") as? HashMap<String, Any>
                        ?: return
                    val id = obj["taskId"]
                    if (id.isLegalString()) {
                        val name = obj["title"] as? String
                        val params = JoyWorkDetailParam(
                            taskId = id as String,
                            taskName = name,
                            projectId = null
                        )
                        params.from = JoyWorkConstant.BIZ_DETAIL_FROM_KR_GOAL
                        params.reqCode = 1000
                        JoyWorkMediator.goDetail(this@GoalDetailActivity, params)
                    }
                }
                JsTargetInterface.CREATE_TASK -> {
                    val obj =
                        intent.getSerializableExtra("data") as? HashMap<String, Any>
                            ?: return
                    val tmpData = ShortcutDialogTmpData()
                    if (obj.contains("krId")) {
                        JDMAUtils.clickEvent("", JoyWorkConstant.KR_DETAIL_CREATE_TASK, null)
                        tmpData.krId = obj["krId"] as String
//                        KRCreateParams.genDeeplink(obj["krId"] as String, obj).openDeeplink(this@GoalDetailActivity)
                    } else if (obj.contains("goalId")) {
                        JDMAUtils.clickEvent("", JoyWorkConstant.TARGET_DETAIL_CREATE_TASK, null)
                        tmpData.targetId = obj["goalId"] as String
//                        GoalCreateParams.genDeeplink(obj["goalId"] as String).openDeeplink(this@GoalDetailActivity)
                    }
                    val dialogO =
                        JoyWorkShortcutCreator.getShortcutDialog(
                            this@GoalDetailActivity,
                            tmpData = tmpData
                        )
                    dialogO.showSnackBar = true
                    dialogO.successCallback = { _, _, dialog ->
                        dialog?.dismiss()
                        refresh()
                    }
                    dialogO.show()

//                    JoyWorkHandler.getInstance().addCreateObserver(object : Runnable {
//                        override fun run() {
//                            JoyWorkHandler.getInstance().removeCreateObserver(this)
//                        }
//                    })
                }
                JsTargetInterface.CLOSE_PAGE -> {
                    superFinish()
                }
                JsTargetInterface.LIKE_LIST -> {
                    val obj =
                        intent.getSerializableExtra("data") as? HashMap<String, String>
                            ?: return
                    val progressId = obj["progressId"] ?: return
                    // 跳转进展点赞列表
                    LikeListActivity.start(progressId, this@GoalDetailActivity)
                }
                JsTargetInterface.SYNC_DETAIL -> {
                    mLottieAnimationView?.gone()
                    mLottieAnimationViewContainer?.gone()
                    mSplinter?.syncData(intent)
                }
                JsTargetInterface.UPDATE_GOAL -> {
                    mSplinter?.updateFromWeb(intent)
                }
                JsTargetInterface.HANDLE_TITLE -> {
                    handler.clear()
                    val obj = intent.getSerializableExtra("data") as? HashMap<String, Boolean>
                    var r = true
                    if (obj != null) {
                        r = obj["show"] ?: true
                    }
                    if (r) {
                        mTitleContainer?.visible()
                    } else {
                        mTitleContainer?.gone()
                    }
                }
            }
        }
    }

    private var keyboardHeightProvider: KeyboardHeightProvider? = null
    private var mLottieAnimationView: LottieAnimationView? = null
    private var mLottieAnimationViewContainer: View? = null
    var mCenterTitleFragment: CenterTitleFragment? = null
    private var mSplinter: DetailSplinter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parseDeeplink()
        if (savedInstanceState != null) {
            restore(savedInstanceState)
        }
        hideAction()
        setContentView(R.layout.joywork_kpi_target_detail)
        mCenterTitleFragment = CenterTitleFragment().left(R.string.icon_direction_left, R.id.back) {
            superFinish()
        }.into(this, R.id.mTitleContainer)
        mSplinter = if (isKr()) {
            KrDetailSplinter(this)
        } else {
            GoalDetailSplinter(this)
        }
        mSplinter?.initTitleBar()
        mSplinter?.setTitleStyle(R.id.back)
        initWebView()
        val filter = IntentFilter()
        filter.addAction(JsTargetInterface.HANDLE_TITLE)
        filter.addAction(JsTargetInterface.CLOSE_PAGE)
        filter.addAction(JsTargetInterface.SYNC_DETAIL)
        filter.addAction(JsTargetInterface.LIKE_LIST)
        filter.addAction(JsTargetInterface.UPDATE_GOAL)
        filter.addAction(JsTargetInterface.CREATE_TASK)
        filter.addAction(JsTargetInterface.OPEN_JOYWORK_DETAIL)
        LocalBroadcastManager.getInstance(this).registerReceiver(receiver, filter)
        keyboardHeightProvider = KeyboardHeightProvider(this)
        mWebView.post {
            keyboardHeightProvider?.start()
        }
        mLottieAnimationViewContainer = findViewById(R.id.mLoadingViewContainer)
        mSplinter?.onCreate()
    }

    private var mWebViewHandler: CompletionHandler<Any>? = null
    private var mJsSdkKit: JsSdkKit? = null

    private fun initWebView() {
        val url = mSplinter?.getDetailUrl() ?: return
        WebView.setWebContentsDebuggingEnabled(true)
        val jsSdkKit = mWebView.initJoyWork(url)
        mWebView.bindJsBrowser(jsSdkKit) {
            mWebViewHandler = it
        }
        mJsSdkKit = jsSdkKit
        mWebView.setOnLongClickListener {
            return@setOnLongClickListener false
        }
    }

    override fun onDestroy() {
        mWebView.loadUrl("about:blank")
        mWebView.destroy() // 销毁web view
        handler.clear()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(receiver)
        mSplinter?.onDestroy()
        super.onDestroy()
    }

    override fun finish() {
//        super.finish()
        // 将事件交给网页处理，因为网页会弹出一些二级界面
        JsTargetInterface.sendToWeb(mWebView, JsTargetInterface.GO_BACK)

        // 如果网页加载出错，手机端进行一定补偿
        handler.postDelayed({
            superFinish()
        }, 1000)
    }

    fun refresh() {
        JsTargetInterface.sendToWeb(mWebView, JsTargetInterface.NOTIFY_REFRESH_GOAL)
    }

    fun superFinish() {
        handler.clear()
        val intent = Intent("return.from.goal.kr.detail")
        intent.putExtra("id", getId())
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
        super.finish()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val key = if (isKr()) KEY_KR_ID else KEY_TARGET_ID
        if (getId().isLegalString()) {
            outState.putString(key, getId())
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (mSplinter?.onActivityResult(requestCode, resultCode, data).isTrue()) {
            return
        }
        if (requestCode == 1000 && resultCode == RESULT_OK) {
            // from task detail
            refresh()
            return
        }
        if (requestCode == JsIm.REQUEST_CODE_SELECT_CONTACT) { //web评论的at功能会回调这里
            // 从选择联系人界面返回时，首先执行到这里，然后才会将跳界面导致的键盘收起事件发给网页
            // 因此会导致网页导致即使拿到联系人数据，也会因为 hide 事件退出评论状态
            runnable = Runnable {
                //这里防止键盘收起的时候onKeyboardHeightChanged退出评论状态
            }
            if (mJsSdkKit != null) {
                mJsSdkKit!!.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    override fun onResume() {
        keyboardHeightProvider?.setKeyboardHeightObserver(this)
        super.onResume()
    }

    override fun onPause() {
        keyboardHeightProvider?.setKeyboardHeightObserver(null)
        super.onPause()
    }

    override fun onKeyboardHeightChanged(height: Int, orientation: Int) {
        if (height == lastKeyBoardHeight) {
            return
        } else {
            lastKeyBoardHeight = height
        }
        if (height > keyBoardShowHeight) {
            keyBoardShowHeight = height
        }
        handler.post {
            if (height <= 0 && runnable == null) {
                JsTargetInterface.sendToWeb(mWebView, "NATIVE_EVENT_KEYBOARD_WILL_HIDE")
            }
            try {
                val jsonObject = JSONObject()
                val density = CommonUtils.getScreenDensity(this)
                jsonObject.put("height", height / density)
                mWebViewHandler?.setProgressData(jsonObject)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            runnable = null
        }
    }
}