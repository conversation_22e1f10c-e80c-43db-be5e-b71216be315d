package com.jd.oa.joywork.executor

import android.app.Activity
import android.widget.Toast
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.joywork.group.SelectGroupRosterActivity
import com.jd.oa.joywork.isLegalString
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.utils.string

object ExecutorUtils {
    val MAX_VALUE = 500

    // 抄送者最大人数
    val CC_MAX_VALUE = 500

    fun toastFull(activity: Activity, maxValue: Int = MAX_VALUE) {
        Toast.makeText(
            activity, activity.resources.getString(
                R.string.joywork_executor_full,
                maxValue
            ), Toast.LENGTH_SHORT
        ).show()
    }

    /**
     * @param sessionId: 当前群组。如果有值，会首先跳转至群成员列表
     */
    fun selectExecutors(
        activity: Activity,
        selected: ArrayList<MemberEntityJd>,
        sessionId: String? = null,
        needSetOwner: Boolean = false,
        onSuccess: (selected: ArrayList<MemberEntityJd?>?) -> Unit,
        onFailure: (() -> Unit)? = null
    ) {
        if (selected.size >= MAX_VALUE) {
            toastFull(activity, MAX_VALUE)
            return
        }
        if (sessionId.isLegalString()) {
            SelectGroupRosterActivity.start(activity, s = selected, sessionId!!, needSetOwner) {
                val r = ArrayList<MemberEntityJd?>()
                it?.forEach { jd ->
                    r.add(jd)
                }
                onSuccess(r)
            }
            return
        }
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .addExcludeAppId("robot.dd")
            .setConstantFilter(selected)
            .setShowSelf(true).setOptionalFilter(null).setShowOptionalFilter(false)
            .setMaxNum(MAX_VALUE - selected.size)

        if (needSetOwner) {
            val selector = ExecutorSelectorBridge(activity, selected)
            selector.onSuccess = onSuccess
            selector.onFailure = onFailure

            service.joyworkSelectExecutor(
                activity,
                activity.string(R.string.joywork_select_executor_title),
                activity.string(R.string.joywork_next),
                entity,
                selector.callback, selector.hashCodeCallback, selector.contextCallback
            )
        } else {
            service.gotoMemberList(
                activity,
                100,
                entity,
                object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                    override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                        onSuccess(selected)
                    }

                    override fun onFail() {
                        onFailure?.invoke()
                    }
                })
        }
    }

    /**
     * 选择要转派的人
     */
    fun selectTransfer(
        activity: Activity,
        selected: ArrayList<MemberEntityJd>,
        onSuccess: (selected: ArrayList<MemberEntityJd?>?) -> Unit,
        onFailure: (() -> Unit)? = null
    ) {
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setShowSelf(false).setOptionalFilter(null).setShowOptionalFilter(false)
            .setConstantFilter(selected)
            .setMaxNum(1)
        service.gotoMemberList(
            activity,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    onSuccess(selected)
                }

                override fun onFail() {
                    onFailure?.invoke()
                }
            })
    }

    /**
     * 选择抄送者
     */
    fun selectCC(
        activity: Activity,
        selected: ArrayList<MemberEntityJd>,
        onSuccess: (selected: ArrayList<MemberEntityJd?>?) -> Unit,
        onFailure: (() -> Unit)? = null
    ) {
        if (selected.size >= CC_MAX_VALUE) {
            Toast.makeText(
                activity, activity.resources.getString(
                    R.string.joywork_executor_full,
                    CC_MAX_VALUE
                ), Toast.LENGTH_SHORT
            ).show()
            return
        }
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setShowSelf(false).setOptionalFilter(null).setShowOptionalFilter(false)
            .setConstantFilter(selected)
            .setMaxNum(CC_MAX_VALUE - selected.size)
        service.gotoMemberList(
            activity,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    onSuccess(selected)
                }

                override fun onFail() {
                    onFailure?.invoke()
                }
            })
    }

    /**
     * 关注人最大人数
     */
    const val RELATION_MAX_VALUE = 500

    fun selectRelation(
        activity: Activity,
        selected: ArrayList<MemberEntityJd>,
        sessionId: String? = null,
        onSuccess: (selected: ArrayList<MemberEntityJd?>?) -> Unit,
        onFailure: (() -> Unit)? = null
    ) {
        if (selected.size >= RELATION_MAX_VALUE) {
            toastFull(activity, RELATION_MAX_VALUE)
            return
        }
        if (sessionId.isLegalString()) {
            SelectGroupRosterActivity.start(activity, s = selected, sessionId!!, false) {
                val r = ArrayList<MemberEntityJd?>()
                it?.forEach { jd ->
                    r.add(jd)
                }
                onSuccess(r)
            }
            return
        }
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setConstantFilter(selected)
            .setShowSelf(true).setOptionalFilter(null).setShowOptionalFilter(false)
            .setMaxNum(RELATION_MAX_VALUE - selected.size)
        service.gotoMemberList(
            activity,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    onSuccess(selected)
                }

                override fun onFail() {
                    onFailure?.invoke()
                }
            })
    }

    private const val max_visible_user = 500

    fun selectVisibleUsers(
        activity: Activity,
        selected: ArrayList<MemberEntityJd>,
        onSuccess: (selected: ArrayList<MemberEntityJd?>?) -> Unit,
        onFailure: (() -> Unit)? = null
    ) {
        if (selected.size >= max_visible_user) {
            Toast.makeText(
                activity, activity.resources.getString(
                    R.string.joywork_max_user,
                    max_visible_user
                ), Toast.LENGTH_SHORT
            ).show()
            return
        }
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setShowSelf(false).setOptionalFilter(null).setShowOptionalFilter(false)
            .setConstantFilter(selected)
            .setMaxNum(max_visible_user - selected.size)
        service.gotoMemberList(
            activity,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    onSuccess(selected)
                }

                override fun onFail() {
                    onFailure?.invoke()
                }
            })
    }

    fun searchOther(
        activity: Activity,
        onSuccess: (selected: ArrayList<MemberEntityJd?>?) -> Unit,
        onFailure: (() -> Unit)? = null
    ) {
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setShowSelf(false).setOptionalFilter(null).setShowOptionalFilter(false)
            .setMaxNum(1)
        service.gotoMemberList(
            activity,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    onSuccess(selected)
                }

                override fun onFail() {
                    onFailure?.invoke()
                }
            })
    }
}