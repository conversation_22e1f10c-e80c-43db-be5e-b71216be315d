package com.jd.oa.joywork.detail

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.data.entity.ChildWorks
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.joywork.detail.ui.NotifyLinearLayout
import com.jd.oa.joywork.indexAtWithNull
import com.jd.oa.joywork.move
import com.jd.oa.joywork.swapSubJoyWork
import com.jd.oa.utils.ToastUtils
import kotlinx.coroutines.launch

/**
 * 详情页子待办 adapter
 */
class SubAdapter(val detail: JoyWorkDetail, val nl: NotifyLinearLayout) :
    RecyclerView.Adapter<VH>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return if (viewType == 2) {
            val view =
                (parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).inflate(
                    R.layout.joywork_sub_add,
                    parent,
                    false
                )
            VH2(view, nl, detail, this)
        } else {
            val view =
                (parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).inflate(
                    R.layout.joywork_sub_item,
                    parent,
                    false
                )
            VH(view, nl, detail, this)
        }
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        if (holder is VH2) {
            holder.bind(detail, null)
        } else {
            holder.bind(detail, detail.childWorks[position])
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position >= detail.childWorks.size) {
            2
        } else 1
    }

    override fun getItemCount(): Int {
        val offset = if (detail.canUpdateChildWorks()) {
            1
        } else 0
        return detail.childWorks.size + offset
    }

    fun swap(viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder) {
        detail.childWorks.move(viewHolder.adapterPosition, target.adapterPosition)
        notifyItemMoved(viewHolder.adapterPosition, target.adapterPosition)
    }
}

open class VH(
    itemView: View,
    val nl: NotifyLinearLayout,
    val detail: JoyWorkDetail,
    val adapter: SubAdapter
) :
    RecyclerView.ViewHolder(itemView) {
    open fun bind(joyWorkDetail: JoyWorkDetail?, childWorks: ChildWorks?) {
        SubJoyWorkEx.buildView(joyWorkDetail!!, childWorks!!, itemView)
        itemView.setTag(R.id.tag_key_data, childWorks)
    }

    private val backup = ArrayList<ChildWorks>()

    fun onBeginDrag() {
        backup.clear()
        backup.addAll(detail.childWorks)
        itemView.setBackgroundColor(Color.parseColor("#F6F6F6"))
    }

    fun onEndDrag() {
        // 比对，传参
        itemView.setBackgroundColor(Color.TRANSPARENT)
        val work = itemView.getTag(R.id.tag_key_data) as ChildWorks
        val index = detail.childWorks.indexOf(work)
        var frontId: String? = null
        var afterId: String? = null
        if (index >= 0) {
            frontId = detail.childWorks.indexAtWithNull(index - 1)?.taskId
            afterId = detail.childWorks.indexAtWithNull(index + 1)?.taskId
        }
        nl.scope?.launch {
            val r = swapSubJoyWork(work.taskId, frontId, afterId)
            r.isSuccess({ _, msg, _ ->
                restore()
                ToastUtils.showToast(msg)
            }) {
                backup.clear()
            }
        }
    }

    private fun restore() {
        detail.childWorks.clear()
        detail.childWorks.addAll(backup)
        adapter.notifyDataSetChanged()
    }
}

// 新建更多
class VH2(itemView: View, nl: NotifyLinearLayout, detail: JoyWorkDetail, adapter: SubAdapter) :
    VH(itemView, nl, detail, adapter) {
    override fun bind(joyWorkDetail: JoyWorkDetail?, childWorks: ChildWorks?) {
        itemView.findViewById<View>(R.id.add_container).setOnClickListener(nl.childCall)
    }
}

class SubDrag(private val rv: RecyclerView) : ItemTouchHelper.Callback() {
    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        if (viewHolder is VH2) {
            return makeMovementFlags(0, 0)
        }
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        return makeMovementFlags(dragFlags, 0)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        (rv.adapter as SubAdapter).swap(viewHolder, target)
        return true
    }

    override fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        return target !is VH2
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        // 只有 item 可拖动
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && viewHolder != null && viewHolder::class.java == VH::class.java) {
            (viewHolder as? VH)?.onBeginDrag()
            return;
        }
        super.onSelectedChanged(viewHolder, actionState)
    }


    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        (viewHolder as? VH)?.onEndDrag()
        super.clearView(recyclerView, viewHolder)
    }
}