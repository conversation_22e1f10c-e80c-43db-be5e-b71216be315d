package com.jd.oa.joywork.detail.data.db

import com.jd.oa.joywork.JoyWorkMsgCenter
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.model.WorkInfo

object UpdateReporter {
    var parcel: DetailReturnParcel? = null

    var workInfo: WorkInfo? = null

    fun reportUpdate() {
        parcel?.update = true
    }

    @JvmStatic
    fun updateWorkInfo(workInfo: WorkInfo?) {
        if (parcel != null && workInfo != null) {
            this.workInfo = workInfo
            reportUpdate()
            JoyWorkMsgCenter.notifyDetailUpdate(workInfo)
        }
    }
}