package com.jd.oa.joywork.detail.data;

import android.app.Activity;
import android.app.Dialog;
import android.text.TextUtils;
import android.view.View;

import androidx.lifecycle.MutableLiveData;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.jd.oa.AppBase;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkDialog;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.RiskEnum;
import com.jd.oa.joywork.detail.data.db.TaskDetailCache;
import com.jd.oa.joywork.detail.data.entity.DelMemberSend;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.model.service.im.dd.entity.Members;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity;
import com.jd.oa.joywork.detail.data.entity.UpdateMemberSend;
import com.jd.oa.joywork.detail.data.entity.attachment.AttachmentOuter;
import com.jd.oa.joywork.detail.data.entity.praise.PraiseOuter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.Utils2Network;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;


public class TaskDetailRepository {

    private static TaskDetailRepository sInstance;

    public static TaskDetailRepository getInstance() {
        if (sInstance == null) {
            synchronized (TaskDetailRepository.class) {
                if (sInstance == null) {
                    sInstance = new TaskDetailRepository();
                }
            }
        }
        return sInstance;
    }

    public MutableLiveData<TaskDetailEntity> getTaskDetail(final String taskId) {
        MutableLiveData<TaskDetailEntity> liveData = TaskDetailCache.getDetail(taskId);
        if (liveData == null) {
            liveData = new MutableLiveData<>();
            TaskDetailCache.putDetail(taskId, liveData);
        }
        updateFromWeb(taskId);
        return liveData;
    }

    public void updateFromWeb(final String taskId) {
        TaskDetailWebservice.getTaskDetail(taskId, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MutableLiveData<TaskDetailEntity> liveData = TaskDetailCache.getDetail(taskId);
                if (liveData == null || liveData.getValue() == null) {
                    JoyWorkDialog.INSTANCE.showAlertDialog(R.string.retrieve, R.string.joy_work_retry, R.string.me_cancel, new Function1<Dialog, Unit>() {
                        @Override
                        public Unit invoke(Dialog dialog) {
                            dialog.dismiss();
                            DialogUtils.showLoadDialog(AppBase.getTopActivity(), R.string.me_waiting);
                            updateFromWeb(taskId);
                            return null;
                        }
                    }, new Function2<View, Dialog, Unit>() {
                        @Override
                        public Unit invoke(View view, Dialog dialog) {
                            dialog.dismiss();
                            final Activity activity = AppBase.getTopActivity();
                            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                                return null;
                            }
                            activity.finish();
                            return null;
                        }
                    });
                }
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (hasError) return;
                Gson gson = new GsonBuilder().create();
                final TaskDetailEntity detail = gson.fromJson(info.result, TaskDetailEntity.class);
                detail.getJoyWorkDetail().splitWebGoals();
                updatePraiseList(taskId, detail);
            }
        });
    }

    public void updateDetailEntity(String taskId, TaskDetailEntity taskDetailEntity) {
        MutableLiveData<TaskDetailEntity> mTaskDetail = TaskDetailCache.getDetail(taskId);
        if (mTaskDetail == null) {
            mTaskDetail = new MutableLiveData<>();
            TaskDetailCache.putDetail(taskId, mTaskDetail);
        }
        if (taskDetailEntity != null) {
            mTaskDetail.postValue(taskDetailEntity);
        }
    }

    public void updateMember(final UpdateMemberSend updateMemberSend) {
        DialogUtils.showLoadDialog(AppBase.getTopActivity(), R.string.me_waiting);
        TaskDetailWebservice.updateTaskMembers(updateMemberSend, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (hasError) return;
                updateFromWeb(updateMemberSend.getTaskId());
            }
        });
    }

    public void deleteTask(String taskId, String actionSource, TaskDetailWebservice.TaskCallback callback) {
        TaskDetailWebservice.deleteTask(taskId, actionSource, callback);
    }

    public void revertTask(String taskId, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.revertTask(taskId, taskCallback);
    }

    public void copyTask(UpdateMemberSend send, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.copyTask(send, taskCallback);
    }

    public void delTaskMembers(DelMemberSend delMemberSend) {
        TaskDetailWebservice.delTaskMembers(delMemberSend, new TaskDetailWebservice.TaskCallback());
    }

    public void delTaskMembers(DelMemberSend delMemberSend, TaskDetailWebservice.TaskCallback callback) {
        TaskDetailWebservice.delTaskMembers(delMemberSend, callback);
    }

    public void taskRemindTime(String taskId, String remind, String actionSource, TaskDetailWebservice.TaskCallback callback) {
        Map<String, Object> map = new HashMap<>();
        map.put("remindStr", remind);
        TaskDetailWebservice.postTaskUpdate(taskId, map, actionSource, callback);
//        TaskDetailWebservice.taskRemindTime(taskId, remind, new TaskDetailWebservice.TaskCallback());
    }

    public void taskDuplicate(String taskId, DuplicateEnum duplicateEnum, TaskDetailWebservice.TaskCallback callback) {
        TaskDetailWebservice.taskDuplicate(taskId, duplicateEnum, callback);
    }

    public void updateTitle(String taskId, String title, String actionSource, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.updateTitle(taskId, title, actionSource, taskCallback);
    }

    public void updateTarget(String taskId, String goalId, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.updateTarget(taskId, goalId, taskCallback);
    }

    public void updateKr(String taskId, String krId, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.updateKr(taskId, krId, taskCallback);
    }

    public void delTarget(String taskId, String goalId, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.delTarget(taskId, goalId, taskCallback);
    }

    public void delKr(String taskId, String krId, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.delKr(taskId, krId, taskCallback);
    }

    public void updateRisk(String taskId, RiskEnum riskEnum, String riskContent, String actionSource, TaskDetailWebservice.TaskCallback taskCallback) {
        TaskDetailWebservice.updateRisk(taskId, riskEnum, riskContent, actionSource, taskCallback);
    }

    public void updateEndTime(String taskId, long startTime, long endTime, String actionSource, TaskDetailWebservice.TaskCallback callback) {
        TaskDetailWebservice.updateEndTime(taskId, startTime, endTime, actionSource, callback);
    }

    /**
     * 同时更新截止时间、重复、提醒
     */
    public void updateEndTimeCycleAlert(String taskId, long startTime, long endTime, Integer cycle, String remindStr, String actionSource, TaskDetailWebservice.TaskCallback callback) {
        TaskDetailWebservice.updateEndTimeCycleAlert(taskId, startTime, endTime, cycle, remindStr, actionSource, callback);
    }

    public void updatePriority(String taskId, int joyWorkLevel, String actionSource, TaskDetailWebservice.TaskCallback callback) {
        TaskDetailWebservice.updatePriority(taskId, joyWorkLevel, actionSource, callback);
    }

    public void updateRemark(String taskId, String remark, String actionSource) {
        TaskDetailWebservice.updateRemark(taskId, remark, actionSource, new TaskDetailWebservice.TaskCallback());
    }

    public void deleteJoySpace(String taskId, String documentId) {
        TaskDetailWebservice.deleteJoySpace(taskId, documentId, new TaskDetailWebservice.TaskCallback());
    }

    public void saveTaskResources(final String taskId, Resources resources, final Utils2Network.Callback callback) {
        TaskDetailWebservice.saveTaskResources(taskId, resources, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.call(false);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (hasError) {
                    callback.call(false);
                    return;
                }
                callback.call(true);
                Gson gson = new GsonBuilder().create();
                AttachmentOuter detail = gson.fromJson(info.result, AttachmentOuter.class);
                if (detail.content == null || !ObjExKt.hasContent(detail.content.resources)) {
                    return;
                }
                TaskDetailEntity taskDetailEntity = TaskDetailCache.getDetail(taskId).getValue();
                if (taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null) {
                    return;
                }
                if (taskDetailEntity.getJoyWorkDetail().getResources() == null) {
                    taskDetailEntity.getJoyWorkDetail().setResources(new ArrayList<Resources>());
                }
                taskDetailEntity.getJoyWorkDetail().getResources().addAll(detail.content.resources);
                updateDetailEntity(taskId, taskDetailEntity);
            }
        });
    }


    public void linkTaskGroup(final String taskId, final JoyWorkDetail.Project project, final Utils2Network.Callback callback) {
        TaskDetailWebservice.taskAddProject(taskId, project.getProjectId(), project.getGroupId(), new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.call(false);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (hasError) {
                    callback.call(false);
                    return;
                }
                callback.call(true);
                TaskDetailEntity taskDetailEntity = TaskDetailCache.getDetail(taskId).getValue();
                if (taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null || taskDetailEntity.getJoyWorkDetail().getProjects() == null
                        || taskDetailEntity.getJoyWorkDetail().getProjects().size() <= 0) {
                    return;
                }
                ArrayList<JoyWorkDetail.Project> projects = taskDetailEntity.getJoyWorkDetail().getProjects();
//                for (Content.Project project1 : taskDetailEntity.getContent().getProjects()) {
//                    if (project.getProjectId().equals(project1.getProjectId())){
//                        project1.setGroupId(project.getGroupId());
//                    taskDetailEntity.getContent().getProjects().remove(project1)
//                    }
//                }
                if (projects != null) {
                    Iterator<JoyWorkDetail.Project> iterator = projects.iterator();
                    while (iterator.hasNext()) {
                        JoyWorkDetail.Project item = iterator.next();
                        if (TextUtils.equals(item.getProjectId(), project.getProjectId())) {
                            iterator.remove();
                            projects.add(project);
                            break;
                        }
                    }
                }
////                Content.Project project = new Content.Project(projectId ,groupId);
//                if (!taskDetailEntity.getContent().getProjects().contains(project)) {
//                    taskDetailEntity.getContent().getProjects().add(project);
//                }
                updateDetailEntity(taskId, taskDetailEntity);
            }
        });
    }

    public void delTaskResources(String taskId, List<String> resourcesList) {
        TaskDetailWebservice.delTaskResources(taskId, resourcesList, new TaskDetailWebservice.TaskCallback());
    }

    public void taskDeleteProject(String taskId, String projectId, String actionSource, TaskDetailWebservice.TaskCallback callback) {
        TaskDetailWebservice.taskDeleteProject(taskId, projectId, actionSource, callback);
    }

    public void addJoySpace(String taskId, List<Documents> documentsList) {
        TaskDetailWebservice.addJoySpace(taskId, documentsList, new TaskDetailWebservice.TaskCallback());
    }

    public void taskPraise(final String taskId) {
        DialogUtils.showLoadDialog(AppBase.getTopActivity(), R.string.me_waiting);
        TaskDetailWebservice.taskPraise(taskId, new TaskDetailWebservice.TaskCallback() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                updatePraiseList(taskId);
            }
        });
    }

    public void cancelTaskPraise(final String taskId) {
        DialogUtils.showLoadDialog(AppBase.getTopActivity(), R.string.me_waiting);
        TaskDetailWebservice.cancelTaskPraise(taskId, new TaskDetailWebservice.TaskCallback() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                updatePraiseList(taskId);
            }
        });
    }

    private void updatePraiseList(String taskId) {
        MutableLiveData<TaskDetailEntity> liveData = TaskDetailCache.getDetail(taskId);
        if (liveData == null) return;
        updatePraiseList(taskId, liveData.getValue());
    }

    public void taskUpdateStatus(String taskId, final int taskStatus, String content, boolean force, IActionFinishListener actionFinishListener) {
        MutableLiveData<TaskDetailEntity> liveData = TaskDetailCache.getDetail(taskId);
        if (liveData == null) return;
        DialogUtils.showLoadDialog(AppBase.getTopActivity(), R.string.me_waiting);
        final TaskDetailEntity detail = liveData.getValue();
        TaskDetailWebservice.taskUpdateStatus(detail.getJoyWorkDetail().getTaskId(), taskStatus, content, force, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                detail.getJoyWorkDetail().setStatus(taskStatus);
                updateFromWeb(detail.getJoyWorkDetail().getTaskId());
                if (actionFinishListener != null) {
                    actionFinishListener.onFinished(true);
                }
            }
        });
    }

    /**
     * 仅有完成/恢复
     */
    public void onlyMeFinish(String taskId, final int taskStatus, String content, IActionFinishListener actionFinishListener) {
        MutableLiveData<TaskDetailEntity> liveData = TaskDetailCache.getDetail(taskId);
        if (liveData == null) return;
        DialogUtils.showLoadDialog(AppBase.getTopActivity(), R.string.me_waiting);
        final TaskDetailEntity detail = liveData.getValue();
        TaskDetailWebservice.taskUpdateStatus(detail.getJoyWorkDetail().getTaskId(), taskStatus, content, false, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                detail.getJoyWorkDetail().setStatus(taskStatus);
                updateFromWeb(detail.getJoyWorkDetail().getTaskId());
                if (actionFinishListener != null) {
                    actionFinishListener.onFinished(true);
                }
            }
        });
    }

    private void updatePraiseList(final String taskId, final TaskDetailEntity detail) {
        TaskDetailWebservice.findTaskPraises(taskId, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (hasError) return;
                Gson gson = new GsonBuilder().create();
                PraiseOuter praiseOuter = gson.fromJson(info.result, PraiseOuter.class);
                if (praiseOuter != null && praiseOuter.content != null) {
                    detail.getJoyWorkDetail().setTaskPraises(praiseOuter.content.taskPraises == null ? new ArrayList<Members>() : praiseOuter.content.taskPraises);
                    detail.getJoyWorkDetail().setTotal(praiseOuter.content.total == null ? 0 : praiseOuter.content.total);
                }
//                Log.e("666666", "updatePraiseList: ");
                updateDetailEntity(taskId, detail);
                DialogUtils.removeLoadDialog(AppBase.getTopActivity());
            }
        });
    }

    public interface IActionFinishListener {
        void onFinished(boolean success);
    }

}

