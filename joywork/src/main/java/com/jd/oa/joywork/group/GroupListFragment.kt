package com.jd.oa.joywork.group

import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.group.strategy.GroupMainNormalStrategy
import com.jd.oa.joywork.group.strategy.GroupMainStrategy
import com.jd.oa.joywork.group.strategy.GroupMainTabStrategy
import com.jd.oa.joywork.group.viewmodel.MainViewModel
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDialog
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDraft
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.shortcut.ShortcutFromType
import com.jd.oa.joywork2.backend.JoyWorkSource
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.ThemeUtils
import com.jd.oa.utils.ToastUtils
import kotlinx.android.synthetic.main.jdme_joywork_group_list_fragment.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect
import org.json.JSONObject

enum class UIStatus {
    IDLE, // 没有请求
    INIT, // 初始化请求接口，切换待完成、未完成请求接口
    REFRESH, // 下拉需要
    LOAD // 上拉加载
}

abstract class GroupListFragment : BaseFragment() {

    private var mRvAdapter: GroupListAdapter? = null

    private var mStrategy: GroupMainStrategy = GroupMainNormalStrategy

    companion object {
        fun getInstance(isTab: Boolean, isMine: Boolean): GroupListFragment {
            val ret = if (isMine) GroupMineFragment() else GroupOtherFragment()
            val bundle = Bundle()
            bundle.putBoolean("isTab", isTab)
            ret.arguments = bundle
            return ret
        }

        private fun GroupListFragment.isTab(): Boolean {
            return arguments?.getBoolean("isTab") ?: false
        }
    }

    private lateinit var mPullUpLoadHelper: PullUpLoadHelper
    private lateinit var activityViewModel: MainViewModel
    private val scope = MainScope()
    private var mUIStatus = UIStatus.IDLE

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        activityViewModel = ViewModelProviders.of(requireActivity()).get(MainViewModel::class.java)
        val result = kotlin.runCatching {
            val jsonObject = JSONObject(requireArguments().getString("mparam") as String)
            mStrategy =
                if (jsonObject.get("isTab") == "1") GroupMainTabStrategy else GroupMainNormalStrategy
        }
        if (result.isFailure) {
            mStrategy = GroupMainNormalStrategy
        }
        return inflater.inflate(R.layout.jdme_joywork_group_list_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        rv.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        mRefreshView.setOnRefreshListener {
            mUIStatus = UIStatus.REFRESH
            refresh(activityViewModel.taskStatusFlow.value)
        }
        mRefreshView.setColorSchemeResources(
            ThemeUtils.getAttrsIdValueFromTheme(
                activity,
                R.attr.me_theme_major_color,
                R.color.skin_color_default
            )
        )
        addView.apply {
            setOnClickListener(this@GroupListFragment)
            val lp = (layoutParams as? ViewGroup.MarginLayoutParams)
                ?: ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            lp.apply {
                rightMargin =
                    requireActivity().resources.getDimensionPixelSize(mStrategy.getAddViewRightMargin())
                bottomMargin =
                    requireActivity().resources.getDimensionPixelSize(mStrategy.getAddViewBottomMargin())
            }
            layoutParams = lp
        }
        scope.launch {
            activityViewModel.taskStatusFlow.collect {
                mUIStatus = UIStatus.INIT
                mRefreshView.isRefreshing = true
                refresh(it)
            }
        }
        activityViewModel.refreshLiveData.observe(viewLifecycleOwner) {
            if (it) {
                mHandler.postDelayed(Runnable {
                    mUIStatus = UIStatus.REFRESH
                    refresh(activityViewModel.taskStatusFlow.value)
                }, 1000)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
        mHandler.removeMessages(0)
    }

    private fun refresh(taskStatus: TaskStatusEnum) {
        if (scope.isActive) {
            scope.launch {
                var offset = mRvAdapter?.itemCount ?: 0
                if (mUIStatus == UIStatus.REFRESH || mUIStatus == UIStatus.INIT) {
                    offset = 0
                }
                withContext(Dispatchers.IO) {
                    val r = getGroupTasks(
                        taskStatus,
                        offset,
                        activityViewModel.parmas?.sessionId ?: "",
                        getUserRole(),
                    )
                    updateUI(r)
                }
            }
        }
    }

    private suspend fun updateUI(r: CoroutineResult<JoyWorkWrapper>) {
        withContext(Dispatchers.Main) {
            when (mUIStatus) {
                UIStatus.LOAD -> {
                    r.isSuccess(::loadMoreNetworkFailure) {
                        val tasks: List<JoyWork>? = it?.tasks
                        if (tasks.isLegalList()) {
                            mRvAdapter?.loadMore(tasks!!)
                        }
                        setLoadMoreStatus((mRvAdapter?.itemCount ?: 0) >= (it?.total ?: 0))
                    }
                }

                UIStatus.INIT -> {
                    mRefreshView.isRefreshing = false
                    r.isSuccess(::initNetworkFailure) {
                        val tasks: List<JoyWork>? = it?.tasks
                        if (tasks.isLegalList()) {
                            mRvAdapter =
                                GroupListAdapter(tasks!!, requireContext(), activityViewModel)
                            rv.adapter = mRvAdapter
                            mPullUpLoadHelper = PullUpLoadHelper(rv) {
                                loadMore()
                            }
                            setLoadMoreStatus((mRvAdapter?.itemCount ?: 0) >= it.total)
                        } else {
                            // 没有结果，或者 joywork 为空
                            showEmpty()
                        }
                        updateFun(activityViewModel)(it?.total ?: 0)
                    }
                }

                UIStatus.REFRESH -> {
                    mRefreshView.isRefreshing = false
                    r.ifSuccessToast {
                        val tasks: List<JoyWork>? = it?.tasks
                        if (tasks.isLegalList()) {
                            mRvAdapter?.updateAll(tasks!!) ?: run {
                                mRvAdapter =
                                    GroupListAdapter(tasks!!, requireActivity(), activityViewModel)
                                rv.adapter = mRvAdapter
                                mPullUpLoadHelper = PullUpLoadHelper(rv) {
                                    loadMore()
                                }
                            }
                            setLoadMoreStatus((mRvAdapter?.itemCount ?: 0) >= (it?.total ?: 0))
                        } else {
                            showEmpty()
                        }
                        updateFun(activityViewModel)(it?.total ?: 0)
                    }
                }

                UIStatus.IDLE -> {
                }
            }
        }
    }

    private fun loadMore() {
        mUIStatus = UIStatus.LOAD
        refresh(activityViewModel.taskStatusFlow.value)
    }

    private fun loadMoreNetworkFailure(t: JoyWorkWrapper?, msg: String, errorCode: Int?) {
        ToastUtils.showInfoToast(msg)
        setLoadMoreStatus(false)
    }

    private fun setLoadMoreStatus(finish: Boolean) {
        if (::mPullUpLoadHelper.isInitialized) {
            if (finish) {
                mPullUpLoadHelper.setComplete()
            } else {
                mPullUpLoadHelper.setLoaded()
            }
        }
    }

    private fun initNetworkFailure(t: JoyWorkWrapper?, msg: String, errorCode: Int?) {
        ToastUtils.showInfoToast(msg)
        rv.adapter = JoyWorkEmptyAdapter.error(requireContext(), msg)
    }

    private fun showEmpty() {
        rv.adapter = JoyWorkEmptyAdapter.empty(requireContext())
    }

    override fun onClick(v: View?) {
        when (v?.id) { // 新建
            R.id.addView -> {
                val tmpData = ShortcutDialogTmpData()
                tmpData.sessionId = activityViewModel.parmas?.sessionId
                tmpData.tripartiteName = activityViewModel.parmas?.sessionName
                tmpData.extra = JsonUtils.getGson().toJson(activityViewModel.parmas)
                tmpData.bizCode = JoyWorkSource.IM.bizCode
                tmpData.fromDD = true
                tmpData.fromType = ShortcutFromType.GROUP_TASK
                tmpData.taskListTypeEnum = TaskListTypeEnum.GROUP
                val dialogO = JoyWorkShortcutDialog(
                    requireActivity(),
                    JoyWorkShortcutDraft.useDraftWhenHad(),
                    tmpData = tmpData
                )
                dialogO.showSnackBar = true
                dialogO.successCallback = { _, _, dialog ->
                    dialog?.dismiss()
                    activityViewModel.refresh()
                }
                dialogO.show()
            }
        }
    }

    abstract fun updateFun(activityViewModel: MainViewModel): (Int) -> Unit

    abstract fun getUserRole(): TaskUserRole
}