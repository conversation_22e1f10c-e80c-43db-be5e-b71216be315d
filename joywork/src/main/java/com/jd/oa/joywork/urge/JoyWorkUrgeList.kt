package com.jd.oa.joywork.urge

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.joywork.detail.data.entity.ChildWorks
import com.jd.oa.joywork.urge.JoyWorkUrgeActivity
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.jdme_joywork_urge.mBackView
import kotlinx.android.synthetic.main.jdme_joywork_urge.mTitle
import kotlinx.android.synthetic.main.jdme_joywork_urge_list.*
import com.jd.oa.joywork.detail.data.entity.Owner
import com.jd.oa.joywork.detail.fromOwner
import com.jd.oa.joywork.detail.isFinish

class JoyWorkUrgeList : BaseActivity() {

    companion object {

        val REQ_CODE = 100

        // 选择催办人，下一步跳转到真正的催办
        private const val TYPE_ONE = 1

        // 催办多个负责人，下一步跳回到催办界面
        private const val TYPE_MORE = 2

        /**
         * 批量催办子待办，先选择催办人
         */
        fun urgeSubjoywork(activity: Activity, childWorks: ArrayList<ChildWorks>?) {
            if (childWorks.isLegalList()) {
                val intent = Intent(activity, JoyWorkUrgeList::class.java)
                intent.putExtra("type", TYPE_ONE)

                val owners = ArrayList<JoyWorkUser>()
                childWorks!!.asSequence().filter {
                    it.owners.isLegalList() && !TaskStatusEnum.FINISH.isFinish(it.uiTaskStatus)
                }.filter {
                    it.owners.isLegalList()
                }.map {work->
                    work.owners.forEach {
                        it.taskId = work.taskId
                    }
                    work.owners
                }.flatten().forEach {
                    val user = JoyWorkUser()
                    user.fromOwner(it)
                    user.taskId = it.taskId
                    owners.add(user)
                }
                val key = "selectOwners"
                intent.putExtra("key", key)
                OneTimeDataRepo.dataCache[key] = owners

                val keyAll = "allOwners"
                intent.putExtra("keyAll", keyAll)
                OneTimeDataRepo.dataCache[keyAll] = owners.map {
                    it
                }
                activity.startActivity(intent)
            }
        }

        /**
         * 批量催办多负责人待办，先选择催办人
         */
        fun urgeJoywork(activity: Activity, owners: List<Owner>, taskId: String) {
            val intent = Intent(activity, JoyWorkUrgeList::class.java)
            intent.putExtra("type", TYPE_ONE)

            val par = ArrayList<JoyWorkUser>()
            owners.forEach {
                val user = JoyWorkUser()
                user.fromOwner(it)
                user.taskId = taskId
                par.add(user)
            }
            val key = "selectOwners"
            intent.putExtra("key", key)
            OneTimeDataRepo.dataCache[key] = par

            val keyAll = "allOwners"
            intent.putExtra("keyAll", keyAll)
            OneTimeDataRepo.dataCache[keyAll] = par.map {
                it
            }
            activity.startActivity(intent)
        }

        /**
         * @param [owners] 当前选中的人
         * @param [allOwners] 所有人
         */
        fun start(activity: Activity, owners: List<JoyWorkUser>, allOwners: List<JoyWorkUser>) {
            val intent = Intent(activity, JoyWorkUrgeList::class.java)
            intent.putExtra("type", TYPE_MORE)
            val key = "selectOwners"
            intent.putExtra("key", key)
            OneTimeDataRepo.dataCache[key] = ArrayList<JoyWorkUser>(owners)

            val keyAll = "allOwners"
            intent.putExtra("keyAll", keyAll)
            OneTimeDataRepo.dataCache[keyAll] = ArrayList<JoyWorkUser>(allOwners)

            activity.startActivityForResult(intent, REQ_CODE)
        }

        private fun JoyWorkUrgeList.getAllOwners(): List<JoyWorkUser> {
            val key = intent.getStringExtra("keyAll") ?: return listOf()
            return (OneTimeDataRepo.dataCache.remove(key) as? List<JoyWorkUser>) ?: listOf()
        }

        private fun JoyWorkUrgeList.getSelectOwners(): ArrayList<JoyWorkUser> {
            val key = intent.getStringExtra("key") ?: return ArrayList()
            return (OneTimeDataRepo.dataCache.remove(key) as? ArrayList<JoyWorkUser>) ?: ArrayList()
        }

        private fun JoyWorkUrgeList.getType(): Int {
            return intent.getIntExtra("type", TYPE_MORE)
        }
    }

    private val allOwners by lazy { getAllOwners() }
    private val selectedOwners by lazy { getSelectOwners() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_joywork_urge_list)
        hideAction()
        mBackView.setOnClickListener {
            finish()
        }
        mTitle.text = "${resources.getString(R.string.me_joywork_unfinish)} ${allOwners.size}"

        mRecyclerView.apply {
            layoutManager =
                LinearLayoutManager(this@JoyWorkUrgeList, LinearLayoutManager.VERTICAL, false)
            adapter = UrgeListAdapter(this@JoyWorkUrgeList, allOwners, { user ->
                selectedOwners.contains(user)
            }, { user ->
                val r = selectedOwners.remove(user)
                if (!r) {
                    selectedOwners.add(user)
                }
                mRecyclerView.adapter?.notifyDataSetChanged()
                handleSelCb()
            })
        }
        handleSelCb()
        mSelCb.setOnClickListener(allClick)
        if (getType() == TYPE_ONE) {
            mSure.setText(R.string.joywork_next)
            mSure.setOnClickListener(nextClick)
            mTitleView.setText(R.string.joywork_urge_select)
        } else {
            mSure.setOnClickListener(sureCallback)
            mTitleView.setText(R.string.joywork_more_owner)
        }

        mSelAll.setOnClickListener(allClick)
    }

    private val sureCallback = View.OnClickListener {
        val key = "resultSelect"
        OneTimeDataRepo.dataCache[key] = ArrayList<JoyWorkUser>(selectedOwners)
        setResult(RESULT_OK, Intent().apply {
            putExtra("key", key)
        })
        finish()
    }

    private val nextClick = View.OnClickListener {
        it.unable()
        JoyWorkUrgeActivity.urgeSubjoywork(this, selectedOwners, allOwners)
        finish()
    }

    private val allClick = View.OnClickListener {
        selectedOwners.clear()
        if (mSelCb.string() == getString(R.string.icon_padding_checkcircle)) {
            // 当前是全选，清空已选列表
            // empty
        } else {
            selectedOwners.clear()
            selectedOwners.addAll(allOwners)
        }
        handleSelCb()
        mRecyclerView.adapter?.notifyDataSetChanged()
    }

    private fun handleSelCb() {
        if (selectedOwners.size == allOwners.size) { // 全选
            mSelCb.setText(R.string.icon_padding_checkcircle)
            mSelCb.argb("#FE3B30")
        } else {
            mSelCb.setText(R.string.icon_prompt_circle)
            mSelCb.argb("#8F959E")
        }
        handleSureBtn()
    }

    private fun handleSureBtn() {
        if (selectedOwners.isNotEmpty()) {
            mSure.background =
                DrawableEx.roundSolidRect(Color.parseColor("#FE3B30"), CommonUtils.dp2FloatPx(4))
            mSure.isEnabled = true
        } else {
            mSure.background =
                DrawableEx.roundSolidRect(Color.parseColor("#1AFE3B30"), CommonUtils.dp2FloatPx(4))
            mSure.isEnabled = false
        }
    }
}