package com.jd.oa.joywork.group

import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.util.ArrayMap
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.joywork.group.strategy.GroupMainNormalStrategy
import com.jd.oa.joywork.group.strategy.GroupMainStrategy
import com.jd.oa.joywork.group.strategy.GroupMainTabStrategy
import com.jd.oa.joywork.group.viewmodel.CountUIState
import com.jd.oa.joywork.group.viewmodel.MainViewModel
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.dialog.biz.JoyWorkFilterDialog
import com.jd.oa.joywork.dialog.biz.JoyWorkFilterDialogBuilder
import com.jd.oa.joywork.dialog.biz.TaskSectionItemFactory
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.jdme_joywork_group_fragment.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import org.json.JSONObject
import com.jd.oa.joywork.R
import kotlinx.android.synthetic.main.joywork_dialog_list_item_simple.view.*

/**
 * 群组待办主页
 */
class GroupMainFragment : BaseFragment(), View.OnClickListener {
    private lateinit var mAdapter: PagerAdapter
    private var mLastPos = 0
    private val filterMap = ArrayMap<Int, JoyWorkFilterDialog.Item>()

    private val scope = MainScope()
    private var mFilterDialog: JoyWorkFilterDialog? = null

    // 所有的 fragment
    private val fs = ArrayList<Fragment>()
    private var mStrategy: GroupMainStrategy = GroupMainNormalStrategy
    private lateinit var model: MainViewModel
    private lateinit var ts: MutableList<String>

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        kotlin.runCatching {
            (requireActivity() as? FunctionActivity)?.setBarHide()
        }
        val result = kotlin.runCatching {
            val jsonObject = JSONObject(requireArguments().getString("mparam") as String)
            mStrategy =
                if (jsonObject.get("isTab") == "1") GroupMainTabStrategy else GroupMainNormalStrategy
        }
        if (result.isFailure) {
            mStrategy = GroupMainNormalStrategy
        }

        fs.add(GroupListFragment.getInstance(mStrategy == GroupMainTabStrategy, true))
        fs.add(GroupListFragment.getInstance(mStrategy == GroupMainTabStrategy, false))
        return inflater.inflate(R.layout.jdme_joywork_group_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        ts = mutableListOf(
            string(R.string.joywork_group_title_mine),
            string(R.string.joywork_group_title_other)
        )
        mTabLayout?.apply {
            layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = TabAdapter(requireContext(), ts, 0) {
                mViewPager.currentItem = it
            }
        }

//        val clickIds = listOf(JoyWorkConstant.JOYWORK_SELF, JoyWorkConstant.JOYWORK_TEAM)

        mAdapter = GroupSunAppFragmentAdapter(childFragmentManager, fs, ts)
        mViewPager.offscreenPageLimit = fs.size
        mViewPager.adapter = mAdapter
        mViewPager.currentItem = mLastPos
        mViewPager.addOnPageChangeListener(object : ViewPager.SimpleOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
//                JDMAUtils.onEventClick(clickIds[position], clickIds[position])
                if (mLastPos != position) {
                    filterMap.remove(mLastPos)
                    mLastPos = position
                }
            }
        })

        mStrategy.handleTitle(titleContainer, requireActivity())

        model = ViewModelProviders.of(requireActivity()).get(MainViewModel::class.java)
        registerCountFlow()
        registerFilterFlow()

        // 展开筛选项
        mFilterContainer.setOnClickListener {
            if (mFilterDialog != null) {
                mFilterDialog?.dismiss()
                return@setOnClickListener
            }
            val defItem = TaskSectionItemFactory.getItemByStatus(model.taskStatusFlow.value)
            mFilterDialog = JoyWorkFilterDialogBuilder.build(mTabContainer, requireActivity()) {
                selectedItem = defItem
                onItemSelectCallback = object : JoyWorkFilterDialog.OnItemSelectCallback {
                    override fun onSelectAction(item: JoyWorkFilterDialog.Item) {
                        model.updateFilter(item.taskStatus)
                    }
                }
                showAll = false
                showDel = false
                topMargin = mViewPager.locationInDecorView()[1]
                showCallback = {
                    setupFilterView(true)
                }
            }

            mFilterDialog?.dismissCallback = {
                setupFilterView(false)
                mFilterDialog = null
            }
        }
    }

    private fun registerCountFlow() {
        scope.launch {
            model.countFlow.collect { state: CountUIState ->
                val tmp = mutableListOf<String>()
                for (i in 0 until ts.size) {
                    tmp.add("${ts[i]} ${state[i]}")
                }
                (mTabLayout?.adapter as? TabAdapter)?.replace(tmp)
            }
        }
    }

    private fun registerFilterFlow() {
        scope.launch {
            model.taskStatusFlow.collect {
                val item = TaskSectionItemFactory.getItemByStatus(model.taskStatusFlow.value)
                mFilterName.setText(item.labelId)
            }
        }
    }

    private fun setupFilterView(open: Boolean) {
        if (open) {
            mFilterIcon.setText(R.string.icon_padding_careup)
            mFilterIcon.argbId(R.color.joywork_red)
            mFilterName.argbId(R.color.joywork_red)
        } else {
            mFilterIcon.setText(R.string.icon_padding_caredown)
            mFilterIcon.argbId(R.color.joywork_black_FFBFC1C)
            mFilterName.argbId(R.color.joywork_black)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }
}

class TabAdapter(
    private val context: Context,
    items: MutableList<String>,
    val selectedIndex: Int,
    itemClick: (Int) -> Unit
) : RecyclerView.Adapter<TabAdapter.VH>() {
    private val mItems = mutableListOf<String>()
    private var mIndex = selectedIndex

    init {
        items.forEach {
            mItems.add(it)
        }
    }

    private val itemClick = View.OnClickListener {
        val i = it.tag as Int
        if (i != mIndex) {
            mIndex = i
            notifyDataSetChanged()
            itemClick(i)
        }
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val tabView: TextView = itemView.findViewById(R.id.tab)
        val indicator: ImageView = itemView.findViewById(R.id.indicator)
    }

    fun replace(newItems: List<String>) {
        mItems.clear()
        mItems.addAll(newItems)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(
            context.inflater.inflate(
                R.layout.joywork_tablayout_item3,
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        holder.tabView.text = mItems[position]
        if (position == mIndex) {
            holder.tabView.bold()
            holder.indicator.visible()
        } else {
            holder.tabView.normal()
            holder.indicator.gone()
        }
        holder.itemView.tag = position
        holder.itemView.setOnClickListener(itemClick)
    }

    override fun getItemCount() = mItems.size
}

class GroupSunAppFragmentAdapter(
    fm: FragmentManager,
    private val mFragments: List<Fragment>,
    private val mTitles: List<String>
) : FragmentPagerAdapter(fm) {
    override fun getItem(position: Int): Fragment {
        return mFragments[position]
    }

    override fun getCount(): Int {
        return mFragments.size
    }

    override fun getPageTitle(position: Int): CharSequence {
        return mTitles[position]
    }
}