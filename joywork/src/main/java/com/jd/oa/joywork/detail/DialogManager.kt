package com.jd.oa.joywork.detail

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import com.jd.me.datetime.picker.DatePickerView
import com.jd.me.datetime.picker.DatetimePickerDialog
import com.jd.oa.ui.dialog.bottomsheet.BaseDialog
import com.jd.oa.ui.dialog.bottomsheet.JoyBottomSheetDialog
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem
import com.jd.oa.joywork.detail.data.entity.custom.EmptyCustomFieldItem
import com.jd.oa.joywork.detail.ui.TaskDetailFragment
import com.jd.oa.joywork.dialog.thirdparty.*
import com.jd.oa.joywork.shortcut.TimeSelectHelper
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.timezone.holidayFetcher
import com.jd.oa.utils.*
import java.util.*
import androidx.core.graphics.toColorInt
import com.jd.oa.ui.dialog.GoneShitTitleController
import com.jd.oa.ui.dialog.IShitTitleDecorator
import com.jd.oa.ui.dialog.bottomsheet.BottomAction

object DialogManager {

    val Context.layoutInflater: LayoutInflater
        get() = getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

    val Context.corner: Float
        get() = resources.getDimension(R.dimen.joywork_corner)

    fun showRemove(
        context: Context,
        click: ((Pair<String, JoyWorkActionEnum>) -> Unit)?
    ): ListShitDialog {
        val dialog = ListShitDialog(
            context,
            SimpleListShitDialogAdapter(
                false,
                context,
                listOf(
                    Pair(
                        context.string(R.string.joywork_alert_remove),
                        JoyWorkActionEnum.REMOVE
                    )
                ),
                -1,
                click
            ),
            ListShitDialogConfig().apply {
                contentBackground =
                    DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
            })
        dialog.titleController = GoneShitTitleController
        dialog.actionController = CancelShitActionController
        dialog.show()
        return dialog
    }

    fun showReverse(
        context: Context,
        click: ((Pair<String, JoyWorkActionEnum>) -> Unit)?
    ): ListShitDialog {
        val dialog = ListShitDialog(
            context,
            SimpleListShitDialogAdapter(
                false,
                context,
                listOf(
                    Pair(
                        context.string(R.string.joywork_alert_reverse),
                        JoyWorkActionEnum.REVERSE
                    )
                ),
                -1,
                click
            ),
            ListShitDialogConfig().apply {
                contentBackground =
                    DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
            })
        dialog.titleController = GoneShitTitleController
        dialog.actionController = CancelShitActionController
        dialog.show()
        return dialog
    }


    /**
     * 显示启动时间 dialog
     */
    fun showLaunchTimeDialog(
        launchTime: Long?,
        endTime: Long?,
        context: Context,
        click: (Long) -> Unit
    ): ListShitDialog {
        val data = ArrayList<ValueUnion>()

        val timeL = DateUtils.getSpecialHour(System.currentTimeMillis(), 9) // 今天九点
        // [0] 表示最左边图标 [1] 表示文字  [2] 表示最右边方案  [3] 表示选择当前 Item 后对应的毫秒值  [4] 表示是否展开时间选择器
        // [5] 表示 item 点击事件 id
        data.add(ValueUnion().apply {
            this[0] = context.string(R.string.icon_edit_historyright)
            this[1] = context.string(R.string.joywork_plan_alert_today)
            this[2] = "9:00"
            this[3] = timeL
            this[4] = false
            this[5] = JoyWorkConstant.DETAIL_LAUNCH_TIME_TODAY_CLICK
        })
        data.add(ValueUnion().apply {
            this[0] = context.string(R.string.icon_direction_rightcircle)
            this[1] = context.string(R.string.joywork_tomorrow)
            this[2] = "9:00"
            // 明天早晨 9 点
            this[3] = DateUtils.getSpecialHour(System.currentTimeMillis() + 24 * 60 * 60 * 1000L, 9)
            this[4] = false
            this[5] = JoyWorkConstant.DETAIL_LAUNCH_TIME_TOM_CLICK
        })
        data.add(ValueUnion().apply {
            this[0] = context.string(R.string.icon_direction_rightdoublearrow)
            this[1] = context.string(R.string.joywork_detail_risk_next_week)
            this[2] = "9:00"
            // 下周一 9 点
            this[3] =
                DateUtils.getSpecialHour(DateUtils.getNextWeekMonday(System.currentTimeMillis()), 9)
            this[4] = false
            this[5] = JoyWorkConstant.DETAIL_LAUNCH_TIME_NEXT_CLICK
        })
        data.add(ValueUnion().apply {
            this[0] = context.string(R.string.icon_general_calendarl)
            this[1] = context.string(R.string.joywork_alert_select_tips)
            this[2] = context.string(R.string.icon_direction_right)
            // 这个需要选择，为跟上面统一，所以给个默认值
            this[3] = Long.MAX_VALUE
            this[4] = true
            this[5] = JoyWorkConstant.DETAIL_LAUNCH_TIME_OTHER
        })

        val dialog = ListShitDialog(context, object : ListShitDialogAdapter<ValueUnion>(data) {
            override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                val view =
                    context.layoutInflater.inflate(R.layout.joywork_dialog_list_item, parent, false)
                val p = data[position]

                val icon = view.findViewById<TextView>(R.id.icon)
                icon.text = p[0] as String

                val tv: TextView = view.findViewById(R.id.content)
                tv.text = p[1] as String

                val detail: TextView = view.findViewById(R.id.detail)
                detail.text = p[2] as String

                view.tag = p
                view.setOnClickListener {
                    val valueUnion = it.tag as ValueUnion
                    val showTime = valueUnion[3] as Long
                    val needSelect = valueUnion[4] as Boolean
                    if (needSelect) {
                        selectLaunchTime(dialog, click, context, launchTime, endTime)
                    } else {
                        dialog?.dismiss()
                        click.invoke(showTime)
                    }
                }
                return view
            }
        }, ListShitDialogConfig().apply {
            contentBackground = DrawableEx.solidRect(Color.WHITE)
        })
        dialog.titleController = TextShitTitleController(object : IShitTitleDecorator<TextView> {
            override fun decorate(t: TextView) {
                t.gravity = Gravity.CENTER
                t.setTextColor(Color.parseColor("#232930"))
                t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16.0f)
                t.setText(R.string.joywork_detail_launch)
                t.background =
                    DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
            }
        })
        dialog.actionController = DividerShitActionController
        dialog.show()
        return dialog
    }

    /**
     * 选择提醒时间
     */
    private fun selectLaunchTime(
        dialog: ListShitDialog?,
        click: (Long) -> Unit,
        context: Context,
        alertTime: Long?,
        endTime: Long?
    ) {
        context.apply {
            dialog?.dismiss()

            val datetimePickerDialog = DatetimePickerDialog(this);
            datetimePickerDialog.setSelectMode(DatePickerView.SelectMode.SINGLE);

            val onCancelListener = DialogInterface.OnCancelListener {
                dialog?.show()
            }

            datetimePickerDialog.setOnCancelListener(onCancelListener)

            datetimePickerDialog.setOnConfirmListener { result ->
                result?.apply {
                    datetime?.run {
                        val selectTime = TimeSelectHelper.getTime(datetime, true)
                        selectTime?.apply {
                            click(this)
                        } ?: onCancelListener.onCancel(datetimePickerDialog)
                    }
                } ?: onCancelListener.onCancel(datetimePickerDialog)
            }

            val date = Date(
                alertTime
                    ?: DateUtils.getSpecialHour(
                        System.currentTimeMillis() + 24 * 60 * 60 * 1000L,
                        9
                    )
            )
            datetimePickerDialog.setSelectedDate(date)
            datetimePickerDialog.setSelectedTime(date)
            datetimePickerDialog.holidayFetcher()
            datetimePickerDialog.show()
        }
    }


    /**
     * 设置优先级
     */
    fun showLevelDialog(context: Context, level: Int?, click: (Int) -> Unit): BaseDialog {
        val list = mutableListOf(
            generateLevel(JoyWorkLevel.P1, context, level),
            generateLevel(JoyWorkLevel.P2, context, level),
            generateLevel(JoyWorkLevel.P3, context, level)
        )
        val builder = JoyBottomSheetDialog.Builder<JoyWorkLevel>(context).apply {
            setTitle(context.resources.getString(R.string.joywork_priority_title))
            setActions(list, listOf(generateLevel(JoyWorkLevel.NO, context, level)))
            setCheckStyle(R.style.BottomSheetDialogCheck_Tick)
            setOnItemClickListener { action, _ ->
                click.invoke(action.data.value)
            }
        }
        return JoyBottomSheetDialog(context, builder)
    }

    private fun generateLevel(
        workLevel: JoyWorkLevel,
        context: Context,
        level: Int?
    ): BottomAction<JoyWorkLevel> {
        return BottomAction(
            headView = TextView(context).apply {
                gravity = Gravity.CENTER
                text = context.resources.getString(workLevel.getResId())
                setTextColor(workLevel.getColor())
                padding(
                    DensityUtil.dp2px(context, 12f),
                    DensityUtil.dp2px(context, 6f),
                    DensityUtil.dp2px(context, 12f),
                    DensityUtil.dp2px(context, 6f)
                )
                background =
                    DrawableEx.roundSolidRect(workLevel.getBgColor(), CommonUtils.dp2FloatPx(4))
            },
            title = context.string(workLevel.getTitle()),
            isSelect = workLevel.value == level,
            data = workLevel
        )
    }

    /**
     * 附件选择框
     */
    fun showFileChoose(context: Context, click: (JoyWorkAttachmentChoose) -> Unit): ListShitDialog {
        val data = listOf(
            Pair(context.string(R.string.joy_work_doc), JoyWorkAttachmentChoose.JOY_SPACE),
            Pair(context.string(R.string.me_web_item_gallery), JoyWorkAttachmentChoose.ALBUM),
            Pair(context.string(R.string.joywork_take_photo), JoyWorkAttachmentChoose.TAKE_PHOTO)

        )
        val dialog = ListShitDialog(
            context,
            SimpleListShitDialogAdapter(
                false,
                context,
                data,
                R.style.JoyWorkAttachmentDialogItemStyle
            ) {
                click.invoke(it.second)
            },
            ListShitDialogConfig().apply {
                contentBackground = DrawableEx.solidRect(Color.WHITE)
            })
        dialog.titleController = TextShitTitleController(object : IShitTitleDecorator<TextView> {
            override fun decorate(t: TextView) {
                t.gravity = Gravity.CENTER
                t.setTextColor(Color.parseColor("#8F959E"))
                t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16.0f)
                t.setText(R.string.joywork_attachment_title)
                t.background =
                    DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
            }
        })
        dialog.actionController = CancelShitActionController
        dialog.show()
        return dialog
    }

    private val clickL = View.OnClickListener {
        val tag = it.getTag()
        when (tag) {
            JoyWorkActionEnum.MERGE_CANCEL -> {
                showMergeTips(R.string.joywork_cancel_merge_tips, it.context)
                return@OnClickListener
            }
            JoyWorkActionEnum.MERGE -> {
                showMergeTips(R.string.joywork_merge_tips, it.context)
                return@OnClickListener
            }
        }
    }

    private fun showMergeTips(id: Int, context: Context) {
        val dialog = ShitAlertDialog(context, ShitAlertDialogConfig<ShitAlertDialog>().apply {
            this.okShitClick = {
                it.dismiss()
            }
            msgRes = id
            okRes = R.string.me_ok
            showCancel = false
        })
        dialog.show()
    }

    /**
     * 显示更多按钮
     * @param fromTitle: 是否是点击右上角三个点
     */
    fun showMoreAction(
        fromTitle: Boolean = false,
        entity: TaskDetailEntity,
        context: Context,
        fragment: TaskDetailFragment,
        click: (JoyWorkActionEnum) -> Unit
    ): ListShitDialog {
        val data = mutableListOf<JoyWorkActionEnum>()
        val verData = getMoreActionVer(entity, fragment)
        data.addAll(verData)
        if (fromTitle) {
            val horData = getMoreActionHor(entity)
            if (horData.isLegalList()) {
                if (data.isLegalList()) {
                    // 上面没有内容，不显示分隔线
                    data.add(JoyWorkActionEnum.NO)
                }
                data.addAll(horData)
            }
        }
        val dialog =
            ListShitDialog(
                context,
                object : ListShitDialogAdapter<JoyWorkActionEnum>(data) {
                    override fun getView(
                        position: Int,
                        convertView: View?,
                        parent: ViewGroup?
                    ): View {
                        val p = items[position]
                        if (p == JoyWorkActionEnum.NO) {
                            val gap = View(context)
                            gap.layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                CommonUtils.dp2px(10.0f)
                            )
                            gap.setBackgroundColor(Color.parseColor("#FFF5F5F5"))
                            return gap
                        }
                        val view = context.layoutInflater.inflate(
                            R.layout.joywork_dialog_list_item,
                            parent,
                            false
                        )

                        val icon = view.findViewById<TextView>(R.id.icon)
                        icon.setText(p.getIconId())

                        val tv: TextView = view.findViewById(R.id.content)
                        tv.setText(p.getStringId())
                        if (p in listOf(
                                JoyWorkActionEnum.DEL_TASK
                            )
                        ) {
                            icon.argb("#FFFE3B30")
                            tv.argb("#FFFE3B30")
                        } else {
                            icon.argb("#FF232930")
                            tv.argb("#FF232930")
                        }

                        val detail: TextView = view.findViewById(R.id.detail)
                        if (p == JoyWorkActionEnum.MERGE || p == JoyWorkActionEnum.MERGE_CANCEL) {
                            detail.visible()
                            detail.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f)
                            detail.setText(R.string.icon_prompt_questioncircle)
                            detail.argb("#BFC1C4")
                            detail.tag = p
                            detail.setOnClickListener(clickL)
                        } else {
                            detail.gone()
                        }

                        view.tag = p
                        view.setOnClickListener {
                            dialog?.dismiss()
                            click.invoke(it.tag as JoyWorkActionEnum)
                        }
                        return view
                    }
                },
                ListShitDialogConfig().apply {
                    contentBackground = DrawableEx.solidRect(Color.WHITE)
                })

        dialog.titleController = CenterTextTitleController(R.string.joywork_shortcut_more)
        dialog.actionController = CancelShitActionController
        dialog.show()
        return dialog
    }

    /**
     * 点击详情页更多操作按钮时，水平滑动的按钮
     */
    private fun getMoreActionHor(entity: TaskDetailEntity?): List<JoyWorkActionEnum> {
        val tmp = mutableListOf<JoyWorkActionEnum>()
        if (entity == null) {
            return tmp
        }
//        if (entity.joyWorkDetail.isMergeable()) {
//            // 有 merge 权限
//            if (entity.joyWorkDetail.mergeInfo == null) {
//                // 合并
//                tmp.add(JoyWorkActionEnum.MERGE)
//            } else {
//                tmp.add(JoyWorkActionEnum.MERGE_CANCEL)
//            }
//        }

        if (entity.joyWorkDetail.isAddParent()) {
            if (entity.joyWorkDetail.parentTaskId.isLegalString()) { // 子待办
                tmp.add(JoyWorkActionEnum.CHANGE_PARENT)
            } else {
                tmp.add(JoyWorkActionEnum.CHILD)
            }
        }
        tmp.add(JoyWorkActionEnum.HISTORY)
        if (entity.joyWorkDetail.isDeletable()) {
            tmp.add(JoyWorkActionEnum.DEL_TASK)
        }
        if (entity.joyWorkDetail.isRevertible()) {
            tmp.add(JoyWorkActionEnum.REVERSE_TASK)
        }
        return tmp
    }

    private fun getMoreActionVer(
        entity: TaskDetailEntity?,
        fragment: TaskDetailFragment
    ): List<JoyWorkActionEnum> {
        val tmp = mutableListOf<JoyWorkActionEnum>()
        if (entity == null) {
            return tmp
        }
        val detail = entity.joyWorkDetail ?: return tmp
//        if (detail.canUpdateTarget() && fragment.filterAction(JoyWorkActionEnum.TARGET)) {
//            tmp.add(JoyWorkActionEnum.TARGET)
//        }

        if (detail.canUpdateGroup() && fragment.filterAction(JoyWorkActionEnum.GROUP)) {
            tmp.add(JoyWorkActionEnum.GROUP)
        }
        if (detail.canUpdateRemind() && fragment.filterAction(JoyWorkActionEnum.REMIND)) {
            tmp.add(JoyWorkActionEnum.REMIND)
        }
        if (detail.canUpdateDuplicate() && fragment.filterAction(JoyWorkActionEnum.DUP)) {
            tmp.add(JoyWorkActionEnum.DUP)
        }
        if (detail.canUpdateLevel() && fragment.filterAction(JoyWorkActionEnum.PRIORITY)) {
            tmp.add(JoyWorkActionEnum.PRIORITY)
        }
        if (detail.canUpdateChildWorks() && fragment.filterAction(JoyWorkActionEnum.ADD_CHILD)) {
            tmp.add(JoyWorkActionEnum.ADD_CHILD)
        }
        // 处理启动时间
        if (entity.joyWorkDetail.canUpdateLaunchTime() && fragment.filterAction(JoyWorkActionEnum.LAUNCH_TIME)) {
            if (tmp.isLegalList()) {
                tmp.add(JoyWorkActionEnum.NO)
            }
            tmp.add(JoyWorkActionEnum.LAUNCH_TIME)
        }
        return tmp
    }


    // 自定义字段选择
    private fun showMarkDialog2(
        context: Context,
        curItem: CustomFieldItem,
        group: CustomFieldGroup,
        taskId: String,
        clickListener: (item: CustomFieldItem?) -> Unit
    ) {
        val dataAll = mutableListOf<CustomFieldItem>()
        if (group.details.isLegalList()) {
            dataAll.addAll(group.details)
        }
        val list = dataAll.map {
            BottomAction(
                headView = TextView(context).apply {
                    gravity = Gravity.CENTER
                    text = it.itemContent.content
                    minWidth = DensityUtil.dp2px(context, 55f)
                    setTextColor(it.itemContent.color.toColorInt())
                    padding(
                        DensityUtil.dp2px(context, 12f),
                        DensityUtil.dp2px(context, 6f),
                        DensityUtil.dp2px(context, 12f),
                        DensityUtil.dp2px(context, 6f)
                    )
                    background =
                        DrawableEx.roundSolidRect(
                            it.itemContent.background.toColorInt(),
                            CommonUtils.dp2FloatPx(4)
                        )
                },
                isSelect = it.detailId == curItem.detailId,
                data = it
            )
        }.toMutableList()
        val emptyItem: CustomFieldItem = EmptyCustomFieldItem.getInstance()
        val bottomAction = BottomAction(
            headView = TextView(context).apply {
                gravity = Gravity.CENTER
                text = emptyItem.itemContent.content
                minWidth = DensityUtil.dp2px(context, 55f)
                setTextColor(emptyItem.itemContent.color.toColorInt())
                padding(
                    DensityUtil.dp2px(context, 12f),
                    DensityUtil.dp2px(context, 6f),
                    DensityUtil.dp2px(context, 12f),
                    DensityUtil.dp2px(context, 6f)
                )
                background =
                    DrawableEx.roundSolidRect(
                        emptyItem.itemContent.background.toColorInt(),
                        CommonUtils.dp2FloatPx(4)
                    )
            },
            isSelect = emptyItem.detailId == curItem.detailId,
            data = emptyItem
        )
        val builder = JoyBottomSheetDialog.Builder<CustomFieldItem>(context).apply {
            setTitle(context.resources.getString(R.string.joywork_mark_dialog_title, group.title))
            setActions(list, listOf(bottomAction))
            setCheckStyle(R.style.BottomSheetDialogCheck_Tick)
            setOnItemClickListener { label, _ ->
                clickListener.invoke(label.data)
            }
        }
        val dialog = JoyBottomSheetDialog(context, builder)
        dialog.setOnDismissListener {
            clickListener.invoke(null)
        }
        dialog.show()
    }

    // 自定义字段选择
    fun showMarkDialog(
        context: Context,
        curItem: CustomFieldItem,
        group: CustomFieldGroup,
        taskId: String,
        callback: (item: CustomFieldItem?) -> Unit
    ) {
        showMarkDialog2(context, curItem, group, taskId) { item ->
            if (item == null) {
                callback.invoke(null)
                return@showMarkDialog2
            }
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_MARK_DIALOG_CLICK,
                "${group.columnId},${group.title}"
            )
            if (item == EmptyCustomFieldItem.getInstance()) {
                delMark(context, item, group, taskId, callback)
            } else {
                updateMarkNet(context, item, group, taskId, callback)
            }
        }
    }

    // 自定义字段选择
    fun showMarkDialog(
        context: Context,
        curItem: CustomFieldItem,
        group: CustomFieldGroup,
        taskId: String,
        callback: (isDel:Boolean, item: CustomFieldItem?) -> Unit
    ) {
        showMarkDialog2(context, curItem, group, taskId) { item ->
            if (item == null) {
                callback.invoke(false, null)
                return@showMarkDialog2
            }
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_MARK_DIALOG_CLICK,
                "${group.columnId},${group.title}"
            )
            if (item == EmptyCustomFieldItem.getInstance()) {
                delMark(context, item, group, taskId){
                    callback.invoke(true, it)
                }
            } else {
                updateMarkNet(context, item, group, taskId){
                    callback.invoke(false, it)
                }
            }
        }
    }

    /**
     * 弹出选择待办风险 dialog
     */
    fun showRiskDialog(
        context: Context,
        curItem: RiskEnum,
        clickListener: (item: RiskEnum?) -> Unit
    ) {
        val dataAll = RiskEnum.values().toMutableList()
        dataAll.removeAll {
            it == RiskEnum.NO_SET
        }
        val dialog = ListShitDialog(
            context,
            object : ListShitDialogAdapter<RiskEnum>(dataAll) {
                override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                    val view = context.layoutInflater.inflate(
                        R.layout.task_detail_page_mark_item_dialog,
                        parent,
                        false
                    )
                    val tv: TextView = view.findViewById(R.id.jdme_id_myapply_dropdown_item)
                    val tvVH = view.findViewById<View>(R.id.mark_value_vh)
                    val tvContainer = view.findViewById<View>(R.id.mark_container)
                    val data = items[position]
                    tvContainer.background = DrawableEx.roundSolidRect(
                        if (data == RiskEnum.NO_SET) Color.parseColor("#F6F6F6") else data.bgColor(),
                        CommonUtils.dp2FloatPx(4)
                    )
                    if (data == RiskEnum.NO_SET) {
                        tv.invisible()
                        tvVH.visible()
                    } else {
                        tvVH.gone()
                        tv.visible()
                        tv.setText(data.resId)
                        tv.setTextColor(data.textColor())
                    }

                    view.tag = data
                    view.setOnClickListener {
                        dialog?.window?.decorView?.tag = data
                        dialog?.dismiss()
                    }

                    val flag = view.findViewById<TextView>(R.id.jdme_id_myapply_dropdown_icon)
                    if (isSelected(curItem, data)) {
                        flag.visible()
                    } else {
                        flag.gone()
                    }
                    val divider = view.findViewById<View>(R.id.divider)
                    if (position == items.size - 1) {
                        divider.gone()
                    } else {
                        divider.visible()
                    }
                    return view
                }

                private fun isSelected(position: RiskEnum, item: RiskEnum): Boolean {
                    return position == item
                }
            },
            ListShitDialogConfig().apply {
                contentBackground = DrawableEx.solidRect(Color.WHITE)
            },
        )
        dialog.setOnDismissListener {
            val item = dialog.window?.decorView?.tag as? RiskEnum
            clickListener.invoke(item)
        }
        dialog.titleController = TextShitTitleController(object : IShitTitleDecorator<TextView> {
            override fun decorate(t: TextView) {
                t.gravity = Gravity.START
                t.setTextColor(Color.parseColor("#232930"))
                t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16.0f)
                t.setText(R.string.joywork_risk_hint)
                t.background = DrawableEx.roundSolidDirRect(
                    Color.WHITE,
                    t.context.resources.getDimension(R.dimen.joywork_corner),
                    DrawableEx.DIR_TOP
                )
            }
        })
        dialog.actionController = CancelShitActionController
        dialog.show()
    }

    fun updateMarkNet(
        context: Context,
        item: CustomFieldItem,
        groupInner: CustomFieldGroup,
        taskId: String,
        callback: (item: CustomFieldItem?) -> Unit,
    ) {
        ProjectRepo.updateMark(
            groupInner.columnId,
            item.detailId,
            taskId,
            item.detailId
        ) { s, msg ->
            if (s) {
                callback(item)
            } else {
                callback(null)
                Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
            }
        }
    }

    fun delMark(
        context: Context,
        item: CustomFieldItem,
        groupInner: CustomFieldGroup,
        taskId: String,
        callback: (item: CustomFieldItem?) -> Unit,
    ) {
        ProjectRepo.delMark(groupInner.columnId, taskId) { s, msg ->
            if (s) {
                callback(item)
            } else {
                callback(null)
                Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
            }
        }
    }


    fun showAction(
        context: Context,
        data: List<Pair<String, Int>>,
        callback: (Pair<String, Int>) -> Unit,
        colorF: (Int) -> Int
    ): Dialog {
        val dialog = ListShitDialog(
            context,
            object : SimpleListShitDialogAdapter<Int>(
                false,
                context,
                data,
                -1,
                callback
            ) {
                override fun text(position: Int, tv: TextView) {
                    tv.setTextColor(colorF(data[position].second))
                }
            },
            ListShitDialogConfig().apply {
                contentBackground =
                    DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
            })
        dialog.titleController = GoneShitTitleController
        dialog.actionController = CancelShitActionController
        dialog.show()
        return dialog
    }
}