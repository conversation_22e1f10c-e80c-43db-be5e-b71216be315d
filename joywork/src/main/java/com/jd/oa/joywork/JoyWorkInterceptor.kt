package com.jd.oa.joywork

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import com.chenenyu.router.RouteInterceptor
import com.chenenyu.router.RouteResponse
import com.chenenyu.router.RouteStatus
import com.jd.oa.joywork.create.JoyWorkCreateActivity
import com.jd.oa.joywork.shortcut.SuccessSnackBar
import com.jd.oa.router.DeepLink

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/28 13:47
 */
class JoyWorkInterceptor : RouteInterceptor {

    override fun intercept(chain: RouteInterceptor.Chain?): RouteResponse {
        val uri = chain?.request?.uri ?: return next(chain)
        val request = chain.request
        val lastSegment = uri.lastPathSegment
        if (Uri.parse(DeepLink.JOY_PAGE_WORK_CREATE).lastPathSegment.equals(lastSegment, true)) {
            val context = chain.context
            val options: Bundle = request.activityOptionsBundle ?: Bundle()
            val intent = Intent(context, JoyWorkCreateActivity::class.java).apply {
                val mParam = uri.getQueryParameter(DeepLink.DEEPLINK_PARAM)
                putExtra(DeepLink.DEEPLINK_PARAM, mParam)
                putExtras(options)
            }
            if (context is Activity) {
                JoyWorkCreateActivity.successRunnable = SuccessSnackBar(context)
                context.startActivityForResult(
                    intent,
                    request.requestCode,
                    options
                )
            } else {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent, options)
            }
            return chain.intercept()
        }
        return next(chain)
    }

    private fun next(chain: RouteInterceptor.Chain?): RouteResponse {
        return chain?.process() ?: RouteResponse.assemble(
            RouteStatus.FAILED,
            null
        )
    }

}