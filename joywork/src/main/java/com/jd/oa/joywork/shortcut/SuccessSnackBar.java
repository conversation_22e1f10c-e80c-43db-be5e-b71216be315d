package com.jd.oa.joywork.shortcut;

import android.app.Activity;
import android.os.Handler;

import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.joywork.bean.JoyWork;
import com.jd.oa.joywork.snackbar.JoyWorkSnackBar;
import com.jd.oa.utils.Constants;
import com.jd.oa.utils.RemoteConfigExtKt;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class SuccessSnackBar {
    private WeakReference<Activity> outContext;

   public SuccessSnackBar(Activity activity) {
        this.outContext = new WeakReference<>(activity);
    }

    private JoyWorkSnackBar snackBar = null;

    public void show(final String result) {
        try {
            new Handler().postDelayed(() -> {
                JSONObject o;
                try {
                    o = new JSONObject(result).getJSONObject("content");
                    String id = o.getString("taskId");
                    String title = o.getString("title");
                    String projectId = o.optString("projectId");
                    showSnackBar(id, title, projectId);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }, 500);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void show(final JSONObject dataObject) {
        try {
            new Handler().postDelayed(() -> {
                try {
                    String id = dataObject.getString("taskId");
                    String title = dataObject.getString("title");
                    String projectId = dataObject.optString("projectId");
                    showSnackBar(id, title, projectId);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }, 500);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void show(final JoyWork work) {
        try {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    showSnackBar(work.taskId, work.title, work.projectId);
                }
            }, 500);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showSnackBar(String id, String title, String projectId) {
        try {
            if (snackBar != null) {
                snackBar.replace(id, title, projectId);
            } else {
                if (outContext.get() == null) {
                    return;
                }
                snackBar =
                        new JoyWorkSnackBar(outContext.get(), id, projectId, title, JoyWorkConstant.BIZ_DETAIL_FROM_LIST);
                snackBar.setOnDismissListener(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        snackBar = null;
                        return null;
                    }
                });
                snackBar.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    void destroy() {
        this.outContext = null;
    }
}
