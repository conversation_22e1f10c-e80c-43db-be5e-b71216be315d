package com.jd.oa.joywork.team.kpi

import android.content.Context
import android.graphics.Color
import com.jd.oa.joywork.JoyWorkActionEnum
import com.jd.oa.joywork.JoyWorkDialog
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.detail.DialogManager.corner
import com.jd.oa.joywork.dialog.thirdparty.*
import com.jd.oa.joywork.isTrue
import com.jd.oa.ui.dialog.GoneShitTitleController
import com.jd.oa.utils.*

val type_summary = 1
val type_share = 2
val type_update = 3
val type_remove = 4
val type_visible = 5

fun KpiTarget.actions(context: Context): List<Pair<String, Int>> {
    val items = mutableListOf<Pair<String, Int>>()
    if (canEdit.isTrue()) {
        items.add(
            Pair(
                context.string(R.string.joywork_update),
                type_update
            )
        )
    }
//    if (canShare.isTrue()) {
//        items.add(
//            Pair(
//                context.string(R.string.joywork_share),
//                type_share
//            )
//        )
//    }
    if (canUpdateVisible.isTrue()) {
        items.add(
            Pair(
                context.string(R.string.joywork_set_visible),
                type_visible
            )
        )
    }
    if (canDel.isTrue()) {
        items.add(
            Pair(
                context.string(R.string.joywork_del),
                type_remove
            )
        )
    }
    return items
}

fun showKpiDialog(
    title: KpiTarget,
    context: Context,
    click: (Int, KpiTarget) -> Unit
): ListShitDialog {
    val items = title.actions(context)
    val dialog = ListShitDialog(
        context,
        SimpleListShitDialogAdapter2(
            false,
            context,
            items,
            {
                if (it.second != type_remove) -1 else R.style.JoyWorkDialogItemHighlightStyle
            },
            {
                click(it.second, title)
            }
        ),
        ListShitDialogConfig().apply {
            contentBackground =
                DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
        })
    dialog.titleController = GoneShitTitleController
    dialog.actionController = CancelShitActionController
    dialog.show()
    return dialog
}


fun showKrDialog(
    kr: KR,
    context: Context,
    click: (Int, KR) -> Unit
): ListShitDialog {
    val items = mutableListOf<Pair<String, Int>>()

    if (kr.permissions.krEditable().isTrue()) {
        items.add(
            Pair(
                context.string(R.string.joywork_update),
                type_update
            )
        )
    }
    if (kr.permissions.krEditable().isTrue()) {
        items.add(
            Pair(
                context.string(R.string.joywork_del),
                type_remove
            )
        )
    }
    val dialog = ListShitDialog(
        context,
        SimpleListShitDialogAdapter2(
            false,
            context,
            items,
            { if (it.second != type_remove) -1 else R.style.JoyWorkDialogItemHighlightStyle },
            { click(it.second, kr) }
        ),
        ListShitDialogConfig().apply {
            contentBackground =
                DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
        })
    dialog.titleController = GoneShitTitleController
    dialog.actionController = CancelShitActionController
    dialog.show()
    return dialog
}

fun showRemoveDialog(context: Context, click: () -> Unit) {
    JoyWorkDialog.showAlertDialog(
        context,
        context.getString(R.string.joywork_del_target_title),
        context.getString(R.string.joywork_del_target_msg),
        R.string.me_ok,
        R.string.cancel,
        {
            it.dismiss()
            click.invoke()
        },
        { _, it -> it.dismiss() }
    )
}

fun showRemoveKRDialog(context: Context, click: () -> Unit) {
    JoyWorkDialog.showAlertDialog(
        context,
        context.getString(R.string.joywork_del_kr_title),
        context.getString(R.string.joywork_del_kr_msg),
        R.string.me_ok,
        R.string.cancel,
        {
            it.dismiss()
            click.invoke()
        },
        { _, it -> it.dismiss() }
    )
}

fun showQuitVisibleSettingDialog(context: Context, click: () -> Unit) {
    JoyWorkDialog.showAlertDialog(
        context,
        context.getString(R.string.joywork_quit_setting_title),
        context.getString(R.string.joywork_quit_setting_msg),
        R.string.me_ok,
        R.string.cancel,
        {
            it.dismiss()
            click.invoke()
        },
        { _, it -> it.dismiss() }
    )
}

fun showCancelFollowDialog(context: Context, click: () -> Unit) {
    JoyWorkDialog.showAlertDialog(
        context,
        context.getString(R.string.joywork_unfouce_title),
        context.getString(R.string.joywork_followed_cancel_msg),
        R.string.me_ok,
        R.string.cancel,
        {
            it.dismiss()
            click.invoke()
        },
        { _, it -> it.dismiss() }
    )
}