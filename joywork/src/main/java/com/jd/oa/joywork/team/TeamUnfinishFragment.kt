package com.jd.oa.joywork.team

import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.team.bean.Group

class TeamUnfinishFragment : TeamBaseFragment(), IBridge, IInsertJoyWork {
    private var finishRunnable: Runnable? = null
    override fun getDefaultStatus(): TaskStatusEnum {
        return TaskStatusEnum.UN_FINISH
    }

    override fun getPageStatus(): TaskStatusEnum {
        return TaskStatusEnum.UN_FINISH
    }

    override fun onNetworkFinish(success: Boolean) {
        finishRunnable?.run()
    }

    override fun addNetworkFinishObserver(runnable: Runnable?) {
        finishRunnable = runnable
    }

    override fun lazyInit(): <PERSON><PERSON>an {
        return false
    }

    override fun getListenerAction(): ArrayList<String> {
        val ret = super.getListenerAction()
        ret.add(ProjectConstant.UNFINISH_ACTION)
        ret.add(ProjectConstant.FINISH_ACTION)
        return ret
    }
}