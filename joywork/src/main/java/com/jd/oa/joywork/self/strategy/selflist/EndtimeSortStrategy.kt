package com.jd.oa.joywork.self.strategy.selflist

import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.self.base.SelfListBaseAdapter
import com.jd.oa.joywork.self.base.SelfListViewModel
import com.jd.oa.joywork.self.group.JoyWorkGroupEx
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.view.realAdapter

class EndtimeSortStrategy(
    next: StrategyBase?,
    mItf: StrategyBaseItf,
    context: FragmentActivity,
    mSortFilterStatusViewModel: TeamDetailViewModel,
    mViewModel: SelfListViewModel
) : StrategyBase(next, mItf, context, mSortFilterStatusViewModel, mViewModel) {
    override fun canHandle(): Boolean {
        return true
    }

    override fun needLoadMore(): Boolean {
        return false
    }

    override fun onGetList(userRole: TaskUserRole) {
        val status = mSortFilterStatusViewModel.mUIStatus
        return mViewModel.refresh(
            status.mStatus,
            userRole,
            status.mFilter,
            status.mSort,
            null,
        )
    }

    override fun onHandleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? JoyWorkWrapper ?: return ArrayList()
        // 截止时间排序，返回的列表中已经分好组
        val tmpGroups = JoyWorkGroupEx.getGroups(context)
        tmpGroups.forEach {
            it.title.count = 0
            it.ensureRealItems()
            it.realItems.clear()
        }
        wrapper.clientTasks.forEach {
            try {
                tmpGroups.firstOrNull { g ->
                    g.id == "${it.blockType}"
                }?.apply {
                    title.count = it.total
                    realItems.clear()
                    realItems.addAll(it.tasks)
                    val size = JoyWorkEx.countJoyWork(realItems)
                    if (size >= 20) {
                        realItems.add(LoadMoreJoyWork(it.blockType))
                    }
                    notifyItemChange()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        tmpGroups.removeAll {
            !it.realItems.isLegalList()
        }
        tmpGroups.forEach { outer ->
            val adapter = mItf.getRv().realAdapter()
            if (adapter is SelfListBaseAdapter) {
                outer.expand =
                    adapter.groups.firstOrNull { outer.id == it.id }?.expand ?: true
            } else {
                outer.expand = true
            }
        }
        return tmpGroups
    }
}