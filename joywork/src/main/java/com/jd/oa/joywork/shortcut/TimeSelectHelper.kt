package com.jd.oa.joywork.shortcut

import com.jd.me.datetime.picker.SelectDatetime
import java.util.Calendar

object TimeSelectHelper {
    fun getTime(result: SelectDatetime, start: Boolean): Long? {
        if (result.date == null) { // 没有日期，时间是无效的，所以不需要处理
            return null
        }
        val c = Calendar.getInstance()
        c.set(Calendar.MILLISECOND, 0)
        c.set(Calendar.SECOND, 0)

        val tmp = Calendar.getInstance()
        tmp.timeInMillis = result.date.time

        c.set(Calendar.YEAR, tmp.get(Calendar.YEAR))
        c.set(Calendar.MONTH, tmp.get(Calendar.MONTH))
        c.set(Calendar.DAY_OF_MONTH, tmp.get(Calendar.DAY_OF_MONTH))


        var minute = 0
        var hour = if (start) 9 else 18
        if (result.isTimeIsOpen && result.time != null) {
            val time = Calendar.getInstance()
            time.timeInMillis = result.time.time
            hour = time.get(Calendar.HOUR_OF_DAY)
            minute = time.get(Calendar.MINUTE)
        }
        c.set(Calendar.HOUR_OF_DAY, hour)
        c.set(Calendar.MINUTE, minute)
        return c.timeInMillis
    }

}