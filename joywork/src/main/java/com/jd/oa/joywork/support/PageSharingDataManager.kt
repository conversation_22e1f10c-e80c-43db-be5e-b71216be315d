package com.jd.oa.joywork.support

object PageSharingDataManager {

    private val map: HashMap<Int, Any> = HashMap()

    fun addData(data: Any): Int {
        val hashCode = System.identityHashCode(data)
        if (!map.containsKey(hashCode)) {
            map.put(hashCode, data)
        }
        return hashCode
    }

    fun removeData(data: Any) {
        map.remove(System.identityHashCode(data))
    }

    fun remove(key: Int) {
        map.remove(key)
    }

    fun getData(key: Int): Any? {
        return map[key]
    }
}