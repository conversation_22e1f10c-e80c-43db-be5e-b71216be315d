package com.jd.oa.joywork.group

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.ViewModelProviders
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.group.viewmodel.MainViewModel
import com.jd.oa.joywork.group.viewmodel.Params
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.detail.ui.TaskDetailActivity
import com.jd.oa.joywork.utils.getParamsKey
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.FragmentUtils
import com.jme.common.R
import org.json.JSONObject
import java.net.URLDecoder

@Route(DeepLink.JOY_WORK_GROUP)
class GroupMainActivity : BaseActivity() {
    private lateinit var model: MainViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_function)
        ActionBarHelper.getActionBar(this)?.hide()
        FragmentUtils.replaceWithCommit(
            this, GroupMainFragment::class.java,
            R.id.me_fragment_content, false, intent.extras,
            false
        )
        model = ViewModelProviders.of(this).get(MainViewModel::class.java)
        try {
            val mparam = intent.getStringExtra(DeepLink.DEEPLINK_PARAM) ?: intent.getStringExtra(
                WebFragment2.BIZ_PARAM
            )
            val decodedParam = URLDecoder.decode(mparam, "utf-8")
            val jsonObject = JSONObject(decodedParam)
            model.parmas = Params(
                jsonObject.optString("sessionId"),
                jsonObject.optString("sessionName"),
                jsonObject.optInt("sessionType", 0),
                jsonObject.optString("gid")
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && data != null && data.hasExtra(TaskDetailActivity.KEY_BIZ_OBJ)) {
            val parcel: DetailReturnParcel =
                data.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as DetailReturnParcel
            // 没有更新
            if (!parcel.update) {
                return
            }
            when (data.getStringExtra(TaskDetailActivity.KEY_BIZ_FROM)) {
                JoyWorkConstant.BIZ_DETAIL_FROM_LIST -> {
                    model.refresh()
                }
            }
        }
    }
}