package com.jd.oa.joywork.common

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.JavascriptInterface
import androidx.activity.OnBackPressedDispatcher
import com.jd.me.datetime.picker.DatetimePickerDialog
import com.jd.me.datetime.picker.SwitchView
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.dynamic.listener.DynamicCallback
import com.jd.oa.dynamic.listener.DynamicOperatorListener
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.fragment.js.hybrid.JsBrowser
import com.jd.oa.fragment.js.hybrid.JsEvent
import com.jd.oa.fragment.js.hybrid.JsPicker
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.R
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.ui.JsTargetInterface
import com.jd.oa.joywork.shortcut.ShortcutManager.AlertClickListener
import com.jd.oa.joywork.shortcut.ShortcutManager.DupClickListener
import com.jd.oa.utils.ToastUtils
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import org.json.JSONException
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/9
 * @Description: 收集H5 sendEvent消息
 */
open class EventWebFragment : WebFragment2(), DynamicOperatorListener, IWebContainer {
    private val callbackMapping: MutableMap<Int, DynamicCallback> = HashMap()
    private lateinit var draft: Value

    private val dialogHandler = object : JsPicker.DateTimePickDialogHandler() {
        override fun onDialogCreate(args: Any, dialog: DatetimePickerDialog) {
            super.onDialogCreate(args, dialog)
            val jsonObject = args as JSONObject
            draft = jsonObject.let {
                val startTime = it.optString("start")
                val endTime = it.optString("end")
                val remindStr = it.optString("remindStr")
                val cycle = it.optInt("cycle")

                Value().apply {
                    updateTime(startTime.toLongOrNull() ?: 0, endTime.toLongOrNull() ?: 0)
                    replaceAlertType(AlertType.stringToValue(remindStr))
                    updateDup(DuplicateEnum.getByValue(cycle))
                }
            }
            addExtraView(dialog, draft)
            dialog.apply {
                setResultChecker { result ->
                    val startEmpty = result.start.date == null
                    val endEmpty = result.end.date == null
                    if (!startEmpty && endEmpty) {
                        ToastUtils.showToast(R.string.joywork2__choose_end_date_tips)
                        return@setResultChecker false
                    }
                    // 截止时间为空时，提醒和重复不能被设置
                    if (endEmpty) {
                        draft.mAlertType.clear()
                        draft.duplicateEnum = DuplicateEnum.NO
                    }
                    return@setResultChecker true
                }
                setDefaultEndTimeInHour(16)
                clearStartWhenEndCleared(true)
            }
        }

        override fun onDialogDismiss(confirm: Boolean, returnObj: JSONObject) {
            super.onDialogDismiss(confirm, returnObj)
            if (confirm) {
                returnObj.apply {
                    put("remindStr", AlertType.valueToString(draft.mAlertType))
                    put("cycle", draft.duplicateEnum.value)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setWebContainer(this)
    }

    // 沉浸式
    override fun configLayout(root: View?, immersive: String, theme: String) {
        super<IWebContainer>.configLayout(root, immersive, theme)
        QMUIStatusBarHelper.translucent(activity)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initValue()
        val jsPicker = findJavascriptObject<JsPicker>(JsPicker.DOMAIN)
        // 待办需要启用时间选择器扩展功能，key 为 1 时表示开启
        jsPicker.setDateTimePickDialog(1, dialogHandler)
        setKeyboardForce(true)
    }

    override fun registerJSNativeKit() {
        super.registerJSNativeKit()
        addJavascriptObject(jsEvent, JsEvent.DOMAIN)
        jsBrowser = EventBrowser(
            webView,
            jsSdkKit,
            this,
            requireActivity(),
            this
        )
        addJavascriptObject(jsBrowser, JsBrowser.DOMAIN)
    }

    override fun getSoftInputMode(): Int {
        return WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING
    }

    open fun initValue() {}

    open fun getCreatePageInfo(handler: CompletionHandler<Any>) {}

    open fun createComplete(jsonObject: JSONObject) {}

    open fun detailPropertyChanged(jsonObject: JSONObject) {}

    open fun urge(jsonObject: JSONObject, handler: CompletionHandler<Any>) {}

    open fun notifyTaskSource(jsonObject: JSONObject) {}

    open fun notifyTaskDetailRisk() {}

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        sendToWebNew(requestCode, resultCode, data)
    }

    private val jsEvent = object : JsEvent(webView) {
        @JavascriptInterface
        override fun sendEvent(args: Any, handler: CompletionHandler<Any>) {
            if (activity == null || activity!!.isFinishing || activity!!.isDestroyed) {
                return
            }
            val jsonObject = args as JSONObject
            MELogUtil.localI(MELogUtil.TAG_JS, "JsEvent sendEvent args: $jsonObject")
            try {
                val action = jsonObject.getString("eventId")
                when (action) {
                    JsTargetInterface.TASK_GET_CREATE_INFO -> {
                        activity?.runOnUiThread { getCreatePageInfo(handler) }
                    }

                    JsTargetInterface.TASK_CREATE_COMPLETE -> {
                        activity?.runOnUiThread { createComplete(jsonObject) }
                    }

                    JsTargetInterface.TASK_URGE -> {
                        activity?.runOnUiThread { urge(jsonObject, handler) }
                    }

                    JsTargetInterface.DETAIL_PROPERTY_CHANGED -> {
                        activity?.runOnUiThread { detailPropertyChanged(jsonObject) }
                    }

                    JsTargetInterface.TASK_SOURCE_INFO -> {
                        activity?.runOnUiThread { notifyTaskSource(jsonObject) }
                    }

                    JsTargetInterface.TASK_DETAIL_RESOLVE_RISK -> {
                        notifyTaskDetailRisk()
                    }
                }
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
    }

    private fun sendToWebNew(requestCode: Int, resultCode: Int, data: Intent?) {
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping[requestCode]?.call(data, resultCode)
        }
    }

    private fun addExtraView(
        dialog: DatetimePickerDialog,
        draft: Value
    ) {
        val extraView = LayoutInflater.from(dialog.context)
            .inflate(R.layout.joywork_datetime_picker_extra, null, false)

        val alertContainer = extraView.findViewById<ViewGroup>(R.id.mAlertContainer)
        val alertClickListener = AlertClickListener(dialog, alertContainer, draft).init()

        val dupContainer = extraView.findViewById<ViewGroup>(R.id.mDupContainer)
        val dupClickListener = DupClickListener(dialog, dupContainer, draft).init()
        dialog.extraView = extraView
        dialog.setOnDateClearListener { side ->
            if (side == SwitchView.Side.RIGHT) {
                draft.mAlertType.clear()
                draft.mAlertType.add(AlertType.NO)
                alertClickListener.handleView()
                draft.duplicateEnum = DuplicateEnum.NO
                dupClickListener.handleView()
            }
        }
    }

    override fun operator(param: MutableMap<String, Any>?) {

    }

    override fun registerCallback(requestCode: Int, callBack: DynamicCallback) {
        callbackMapping[requestCode] = callBack
    }

    override fun onBackPressDispatcher(): OnBackPressedDispatcher? =
        activity?.onBackPressedDispatcher

    override fun close() {
        kotlin.runCatching {
            activity?.finish()
        }
    }
}