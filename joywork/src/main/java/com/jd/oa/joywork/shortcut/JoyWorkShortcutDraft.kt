package com.jd.oa.joywork.shortcut

/**
 * [JoyWorkShortcutDialog] 中数据存储类。
 */
class JoyWorkShortcutDraft private constructor() {

    companion object {
        private var mDraft: JoyWorkShortcutDraft? = null

        // 新建草稿对象，同时清空旧有
        fun newDraft(): JoyWorkShortcutDraft {
            val draft = JoyWorkShortcutDraft()
            mDraft = draft
            return draft
        }

        /**
         * 有草稿时使用草稿，没有草稿时新建草稿
         */
        fun useDraftWhenHad(): JoyWorkShortcutDraft {
            val d = mDraft
            if (d != null) {
                return d
            }
            return newDraft()
        }

        fun removeDraft() {
            mDraft = null;
        }

        fun hasDraft(): Boolean {
            return mDraft != null
        }

        fun updateDraftTitle(title: String?) {
            mDraft?.title = title
        }

        fun updateDraftStartTime(startTime: Long?) {
            mDraft?.startTime = startTime
        }

        fun updateDraftEndTime(endTime: Long?) {
            mDraft?.endTime = endTime
        }
    }

    var startTime: Long? = null
    var endTime: Long? = null
    var title: String? = null
}
