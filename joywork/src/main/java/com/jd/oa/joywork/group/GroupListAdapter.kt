package com.jd.oa.joywork.group

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.text.PrecomputedTextCompat
import androidx.core.widget.TextViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.group.viewmodel.MainViewModel
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.utils.ToastUtils
import kotlinx.android.synthetic.main.jdme_joywork_group_list_item.view.*

class GroupListAdapter(data: List<JoyWork>, val ctx: Context, val model: MainViewModel) :
    RecyclerView.Adapter<GroupListAdapter.VH>() {

    private val itemClick = View.OnClickListener {
        val joyWork = it.tag as JoyWork
        JoyWorkMediator.goDetail(
            ctx,
            JoyWorkDetailParam(
                taskId = joyWork.taskId,
                taskName = joyWork.title,
                projectId = null
            ).apply {
                from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                reqCode = 10
                obj = DetailReturnParcel()
            })
    }

    private val mutableData = ArrayList<JoyWork>(data)

    private fun View.inflate(id: Int, parent: ViewGroup) =
        (context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).inflate(
            id,
            parent,
            false
        )

    class VH(itemView: View) : RecyclerView.ViewHolder(itemView) {

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(parent.inflate(R.layout.jdme_joywork_group_list_item, parent))
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val joywork: JoyWork = mutableData[position]
        holder.itemView.title.text = joywork.title

        JoyWorkViewItem.deadline(joywork, holder.itemView.deadline)
        JoyWorkViewItem.duplicateIcon(joywork, holder.itemView.dupIcon)

        JoyWorkViewItem.checkboxNew(
            joywork,
            holder.itemView.cb_task,
            holder.itemView.cb_task2,
            holder.itemView.cb_task_container
        ) { joyWork, cb ->
            JoyWorkViewItem.finishAction(joyWork, cb.context, object : JoyWorkUpdateCallback {
                override fun result(success: Boolean, errorMsg: String) {
                    joyWork.isFinishing = false
                    if (success) {
                        try {
                            joyWork.toggleFinishStatus()
                            if (joyWork.isUIFinish && joyWork.isDup) {
                                ToastUtils.showInfoToast(R.string.joywork_dup_work_finish_tips)
                            }
                            model.refresh()
                        } catch (e: Exception) {
                            e.printStackTrace()
                            result(false, JoyWorkEx.filterErrorMsg(""))
                        }
                    } else {
                        ToastUtils.showInfoToast(errorMsg)
                        notifyDataSetChanged()
                    }
                }

                override fun onStart() {
                    joyWork.isFinishing = true
                    notifyDataSetChanged()
                }
            })
        }

        holder.itemView.tag = joywork
        holder.itemView.setOnClickListener(itemClick)
    }

    override fun getItemCount(): Int {
        return mutableData.size
    }

    fun loadMore(tasks: List<JoyWork>) {
        mutableData.addAll(tasks)
        notifyDataSetChanged()
    }

    fun updateAll(tasks: List<JoyWork>) {
        mutableData.clear()
        mutableData.addAll(tasks)
        notifyDataSetChanged()
    }

    fun AppCompatTextView.setTextFuture(charSequence: CharSequence) {
        this.setTextFuture(
            PrecomputedTextCompat.getTextFuture(
                charSequence,
                TextViewCompat.getTextMetricsParams(this),
                null
            )
        )
    }
}