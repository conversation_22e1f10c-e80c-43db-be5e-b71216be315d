package com.jd.oa.joywork.self.base

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.*
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail.Project
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.UpdateMemberSend
import com.jd.oa.joywork.detail.ui.TaskUiUtils
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork.team.*
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.bean.TeamGroupExtend
import com.jd.oa.joywork.team.view.DividerLinearLayout
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.*
import java.util.*

interface Refreshable {
    fun refresh()
}

interface SelfListItemVHItf : Refreshable {

    companion object {
        val TYPE_DL = 1
        val TYPE_FINISH = 2
    }

    fun getOwnerFragment(): Fragment

    fun refreshData()

    fun getData(): List<Any>

    fun itemTimeType(): Int

    fun projectClick(
        joyWork: JoyWork,
        callback: (Project?, isDel: Boolean) -> Unit
    )
}

/**
 * 团队待办中 item 对应的 vh
 */
class SelfListItemVH(
    private val context: Activity,
    parent: ViewGroup,
    private val itf: SelfListItemVHItf,
    private val adapter: RecyclerView.Adapter<*>
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork_project_item,
            parent,
            false
        )
    ), Exchangeable {

    override var exchangeable = true

    private val title by lazy { itemView.findViewById<TextView>(R.id.title) }
    private val cb = itemView.findViewById<TextView>(R.id.cb_task)
    private val cb2 = itemView.findViewById<ImageView>(R.id.cb_task2)
    private val cbContainer by lazy { itemView.findViewById<View>(R.id.cb_task_container) }
    private val cover by lazy { itemView.findViewById<View>(R.id.cover) }

    private val ownerContainer by lazy { itemView.findViewById<View>(R.id.owner_container) }

    private val titleContainer by lazy { itemView.findViewById<LinearLayout>(R.id.ll_title_container) }
    private val mAvatarView: JoyWorkAvatarView by lazy { itemView.findViewById(R.id.mAvatarView) }

    private val deadline by lazy { itemView.findViewById<TextView>(R.id.deadline) }
    private val dupIcon by lazy { itemView.findViewById<IconFontView>(R.id.dupIcon) }
    private val deadlineDupContainer by lazy { itemView.findViewById<ViewGroup>(R.id.deadline_dup) }
    private val deadlineContainer by lazy { itemView.findViewById<View>(R.id.deadline_container) }
    private val itemContainer by lazy { itemView.findViewById<DividerLinearLayout>(R.id.item_container) }
    private val deadlineVH by lazy { itemView.findViewById<View>(R.id.mark_value_vh) }
    private val risk = itemView.findViewById<TextView>(R.id.risk)

    // 优先级
    private val priority_container = itemView.findViewById<View>(R.id.priority_container)
    private val priority = itemView.findViewById<TextView>(R.id.priority)
    private val priorityVH = itemView.findViewById<View>(R.id.priority_value_vh)

    private val exContainer by lazy { itemView.findViewById<LinearLayout>(R.id.ex_container) }

    private val itemClick = View.OnClickListener {
        val task = it.getTag(R.id.jdme_tag_id) as JoyWork
        JDMAUtils.onEventClick(JoyWorkConstant.ITEM_CLICK, JoyWorkConstant.ITEM_CLICK)
        JoyWorkMediator.goDetail(
            itf.getOwnerFragment(),
            JoyWorkDetailParam(
                taskId = task.taskId,
                projectId = task.projectId,
                taskName = task.title
            ).apply {
                from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                reqCode = 10
                obj = DetailReturnParcel()
            })
    }

    private fun enableView(b: Boolean, view: View) {
        if (b) {
            view.setBackgroundColor(Color.TRANSPARENT)
        } else {
            view.setBackgroundColor(Color.parseColor("#F0F3F3"))
        }
        view.isEnabled = b
    }

    private val projectClickListener = View.OnClickListener {
        if (it.tag == null) {
            return@OnClickListener
        }
        val joywork = it.tag as JoyWork
        itf.projectClick(joywork) { p, isDel ->
            if (p == null) {
                return@projectClick
            }
            if (isDel) {
                TaskDetailWebservice.taskDeleteProject(
                    joywork.taskId,
                    p.projectId,
                    null,
                    object : TaskDetailWebservice.TaskCallback() {
                        override fun onFailure(exception: HttpException?, info: String?) {
                            Toast.makeText(
                                context,
                                JoyWorkEx.filterErrorMsg(info),
                                Toast.LENGTH_SHORT
                            ).show()
                        }

                        override fun onSuccess(info: ResponseInfo<String>?) {
                            super.onSuccess(info)
                            if (hasError) {
                                onFailure(null, "")
                            } else {
                                joywork.projects?.removeAll {
                                    Objects.equals(it.projectId, p.projectId)
                                }
                                updateProjects(joywork)
                            }
                        }
                    })

            } else {
                TaskDetailWebservice.taskAddProject(
                    joywork.taskId,
                    p.projectId,
                    p.groupId,
                    object : TaskDetailWebservice.TaskCallback() {
                        override fun onFailure(exception: HttpException?, info: String?) {
                            Toast.makeText(
                                context,
                                JoyWorkEx.filterErrorMsg(info),
                                Toast.LENGTH_SHORT
                            ).show()
                        }

                        override fun onSuccess(info: ResponseInfo<String>?) {
                            super.onSuccess(info)
                            if (hasError) {
                                onFailure(null, "")
                            } else {
                                val ps = joywork.projects ?: ArrayList()
                                ps.add(p)
                                joywork.projects = ps
                                updateProjects(joywork)
                            }
                        }
                    })
            }
        }
    }

    private fun updateProjects(joywork: JoyWork) {
        val value = itemView.findViewById<TextView>(R.id.mark_value)
        val ps = joywork.projects?.map {
            it.title
        }?.joinLegalString(",") ?: ""
        value.text = ps
    }

    private val ownerClickListener = View.OnClickListener {
        if (it.tag == null) {
            return@OnClickListener
        }
        val joywork = it.tag as JoyWork
        enableView(false, it)
        TaskUiUtils.selectContacts(
            context,
            1,
            object : Callback<ArrayList<MemberEntityJd?>> {
                override fun onSuccess(bean: ArrayList<MemberEntityJd?>) {
                    val memberSend = UpdateMemberSend()
                    memberSend.taskId = joywork.taskId
                    val members: MutableList<Members> = ArrayList()
                    for (memberEntityJd in bean) {
                        val member = Members()
                        member.fromDD(memberEntityJd!!)
                        members.add(member)
                    }
                    memberSend.members = members
                    TaskDetailWebservice.updateTaskMembers2(
                        memberSend,
                        object : TaskDetailWebservice.TaskCallback() {
                            override fun onSuccess(info: ResponseInfo<String>?) {
                                super.onSuccess(info)
                                enableView(true, it)
                                if (!hasError) {
                                    itf.refresh()
                                }
                            }

                            override fun onFailure(exception: HttpException?, info: String?) {
                                super.onFailure(exception, info)
                                enableView(true, it)
                            }
                        })
                }

                override fun onFail() {
                    enableView(true, it)
                    //ToastUtils.showToast(activity.getString(R.string.task_operate_fail))
                }
            })
    }

    private val priorityClick = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        enableView(false, view)
        JDMAUtils.clickEvent("", JoyWorkConstant.TEAM_LIST_SET_REMINDER, null)
        val dialog = DialogManager.showLevelDialog(context, joywork.priorityType) {
            TaskDetailWebservice.updatePriority(
                joywork.taskId,
                it,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }

                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            // 刷新整个界面
                            itf.refresh()
                        }
                    }
                })
        }
        dialog.setOnDismissListener {
            enableView(true, view)
        }
        dialog.show()
    }

    private val deadlineListener = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        enableView(false, view)
        val detail = Value()
        detail.startTime = joywork.startTime
        detail.endTime = joywork.endTime
        detail.replaceAlertType(AlertType.createFromString(joywork.remindStr))
        detail.duplicateEnum = DuplicateEnum.getByValue(joywork.cycle)
        detail.type = Value.TYPE_DEADLINE
        ShortcutManager(null, view.context).selectTime(detail) {
            val params = HashMap<String, Any>()
            if (!Objects.equals(detail.startTime, joywork.startTime)) {
                params["startTime"] =
                    if (detail.startTime.isLegalTimestamp()) detail.startTime else -1
            }
            if (!Objects.equals(detail.endTime, joywork.endTime)) {
                notifyRiskUpdateWhenEndTimeUpdate(view.context, detail.endTime, joywork.endTime)
                params["endTime"] =
                    if (detail.endTime.isLegalTimestamp()) detail.endTime else -1
            }
            if (detail.mAlertType.isEmpty()) {
                detail.mAlertType.add(AlertType.NO)
            }
            if (!AlertType.equals(
                    AlertType.createFromString(joywork.remindStr),
                    detail.mAlertType
                )
            ) {
                params["remindStr"] = AlertType.valueToString(detail.mAlertType)
            }
            val c = (detail.duplicateEnum ?: DuplicateEnum.NO).value
            if (!Objects.equals(c, joywork.cycle)) {
                params["cycle"] = c
            }
            if (!params.isLegalMap()) {
                enableView(true, view)
                return@selectTime
            }
            TaskDetailWebservice.postTaskUpdate(
                joywork.taskId,
                params,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            // 刷新整个界面
                            itf.refresh()
                        }
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }
                })
        }
    }

    fun bind(joyWork: JoyWork, data: ArrayList<Any>) {
        if (data.indexOf(joyWork) == 0 || data.indexAtWithNull(data.indexOf(joyWork) - 1)
                ?.isJoyWork() == true
        ) {
            itemContainer.setShowTop(false)
        } else {
            itemContainer.setShowTop(true)
        }

        itemView.setTag(R.id.jdme_tag_id, joyWork)

        itemView.setTag(R.id.jdme_tag_id, joyWork)
        itemView.setOnClickListener(itemClick)

        if (showCover(joyWork)) {
            cover.visible()
        } else {
            cover.gone()
        }
        // 整个屏幕的 80%
        titleContainer.apply {
            val w = CommonUtils.getScreentWidth(context) * 4 / 5
            this.layoutParams.width = w
        }

        JoyWorkViewItem.title(joyWork, title)
        joyWork.sortOwners()
        JoyWorkViewItem.risk(joyWork, risk).avatarView(joyWork, mAvatarView, null) { joywork ->
            if (!joywork.owners.isLegalList() || joywork.owners.size >= 2) {
                ""
            } else {
                joywork.owners.firstOrNull()?.realName ?: ""
            }
        }
        when (itf.itemTimeType()) {
            SelfListItemVHItf.TYPE_FINISH -> {
                JoyWorkViewItem.finishTime(joyWork, deadline, deadlineVH)
            }
            else -> {
                JoyWorkViewItem.deadline(joyWork, deadline, deadlineVH)
            }
        }
        JoyWorkViewItem.priority(joyWork, priority, priorityVH)
        JoyWorkViewItem.duplicateIcon(joyWork, dupIcon)
        JoyWorkViewItem.deadlineDupView(joyWork, deadlineDupContainer, deadlineVH)

        if (joyWork.canUpdateDeadline()) {
            deadlineContainer.tag = joyWork
            deadlineContainer.click(deadlineListener)
        } else {
            deadlineContainer.tag = null
            deadlineContainer.isEnabled = false
            deadlineContainer.isClickable = false
        }

        if (joyWork.canUpdatePriority()) {
            priority_container.tag = joyWork
            priority_container.click(priorityClick)
        } else {
            priority_container.tag = null
            priority_container.isEnabled = false
            priority_container.isClickable = false
        }

        if (joyWork.canTransfer()) {
            ownerContainer.tag = joyWork
            ownerContainer.click(ownerClickListener)
        } else {
            ownerContainer.tag = null
            ownerContainer.isEnabled = false
            ownerContainer.isClickable = false
        }

        addProjectView(joyWork)

        cb.setTag(R.id.jdme_tag_id, joyWork)
        cbContainer.setTag(R.id.jdme_tag_id, joyWork)
        JoyWorkViewItem.checkboxNew(
            joyWork,
            cb,
            cb2,
            cbContainer
        ) { work: JoyWork, cbView: View ->
            JDMAUtils.onEventClick(
                JoyWorkConstant.INCOMPLETE_FINISH,
                JoyWorkConstant.INCOMPLETE_FINISH
            )
            JoyWorkViewItem.finishAction(work, context, object : JoyWorkUpdateCallback {
                override fun result(success: Boolean, errorMsg: String) {
                    work.isFinishing = false
                    if (success) {
                        try {
                            val tmp = cbView.tag as JoyWork
                            if (tmp.isFinish) {
                                ProjectConstant.sendBroadcast(
                                    cb.context,
                                    ProjectConstant.UNFINISH_ACTION
                                )
                                work.uiTaskStatus = TaskStatusEnum.UN_FINISH.code;
                            } else {
                                ProjectConstant.sendBroadcast(
                                    cb.context,
                                    ProjectConstant.FINISH_ACTION
                                )
                                work.uiTaskStatus = TaskStatusEnum.FINISH.code
                                if (work.isFinish && work.isDup) {
                                    ToastUtils.showInfoToast(R.string.joywork_dup_work_finish_tips)
                                }
                            }
                            checkboxSuccess(tmp)
                            try {
                                // 修改标题中的数量
                                (tmp.expandableGroup.title as JoyWorkTitle).count--
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            result(false, JoyWorkEx.filterErrorMsg(""))
                        }
                    } else {
                        adapter.notifyItemChanged(adapterPosition)
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }

                override fun onStart() {
                    work.isFinishing = true
                    adapter.notifyItemChanged(adapterPosition)
                }
            })
        } // 选框
    }

    private fun addProjectView(joyWork: JoyWork) {
        exContainer.removeAllViews()
        val view = context.layoutInflater.inflate(
            R.layout.jdme_joywork_project_item_ex,
            exContainer,
            false
        )
        exContainer.addView(view)
        view.findViewById<View>(R.id.mark_value_vh).gone()
        val value = view.findViewById<TextView>(R.id.mark_value)
        val ps = joyWork.projects?.map {
            it.title
        }?.joinLegalString(",") ?: ""
        value.text = ps

        view.tag = joyWork
        view.setOnClickListener(projectClickListener)
    }

    private fun showCover(joyWork: JoyWork): Boolean {
        return joyWork.isDeleted
    }

    private fun checkboxSuccess(tmp: JoyWork) {
        tmp.expandableGroup.realItems.remove(tmp)
        itf.refreshData()
//        adapter.processor.refreshData()
        tmp.expandableGroup.notifyItemChange()
        if (adapterPosition >= 0 && adapterPosition < itf.getData().size) {
            adapter.notifyItemRemoved(adapterPosition)
        } else {
            adapter.notifyDataSetChanged()
        }
    }

    fun onBeginDrag() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.JOYWORK_START_DRAG,
            JoyWorkConstant.JOYWORK_START_DRAG
        )
//        adapter.processor.backup(itemView.getTag(R.id.jdme_tag_id) as JoyWork)
        itemView.setBackgroundColor(Color.parseColor("#F6F6F6"))
    }

    fun onEndDrag() {
//        adapter.clearAllMountItems()
        val item = itemView.getTag(R.id.jdme_tag_id) as JoyWork
        itemView.setBackgroundColor(Color.TRANSPARENT)
        val a = adapter as? SelfListCustomGroupAdapter
        val swaped = a?.swaped
        // 前后没挪位置
        if (swaped.isNotTrue()) {
            return
        }
        a?.swaped = false
        a?.sortJoyWork2(item)
    }
}

interface SelfListTitleItf {

    fun itemClick(title: JoyWorkTitle)
}


class SelfListTextImageVH(
    context: Context,
    parent: ViewGroup,
) : RecyclerView.ViewHolder(
    context.inflater.inflate(
        R.layout.jdme_joywork_project_title2,
        parent,
        false
    )
), Exchangeable {
    private val title = itemView.findViewById<TextView>(R.id.title)
    private val avatar = itemView.findViewById<ImageView>(R.id.avatar)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_joywork_group_small).toInt()

    override var exchangeable: Boolean = false


    private val JoyWorkTitle.teamGroup: TeamGroupExtend
        get() {
            return extra as TeamGroupExtend
        }

    fun bind(joyWorkTitle: JoyWorkTitle, first: Boolean) {
        itemView.setTag(R.id.jdme_tag_id, joyWorkTitle)

        if (first) {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingSmall,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        } else {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingBig,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        }

        title.text = joyWorkTitle.teamGroup.safeTitle(itemView.context)

        if (joyWorkTitle.teamGroup.user == null) {
            avatar.gone()
        } else {
            avatar.visible()
            JoyWorkViewItem.avatar(avatar, joyWorkTitle.teamGroup.user.headImg)
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "ProjectGroupTextImageVH【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class SelfListTitleVH(context: Context, parent: ViewGroup, private val itf: SelfListTitleItf) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_project_title,
            parent,
            false
        )
    ), Exchangeable {
    private val icon = itemView.findViewById<TextView>(R.id.icon)
    private val title = itemView.findViewById<TextView>(R.id.title)
    private val indicator = itemView.findViewById<TextView>(R.id.indicator)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_joywork_group_small).toInt()

    override var exchangeable: Boolean = false

    private val itemClick = View.OnClickListener {
        itf.itemClick(it.getTag(R.id.jdme_tag_id) as JoyWorkTitle)
    }

    fun bind(
        joyWorkTitle: JoyWorkTitle,
        expanded: Boolean,
        first: Boolean,
    ) {
        itemView.setTag(R.id.jdme_tag_id, joyWorkTitle)

        if (first) {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingSmall,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        } else {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingBig,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        }

        icon.gone()
        title.text = joyWorkTitle.title
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }

        itemView.setTag(R.id.jdme_tag_id, joyWorkTitle)
        itemView.setOnClickListener(itemClick)
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}


interface SelfListLoadMoreVHItf {
    fun loadMore(loadMoreJoyWork: LoadMoreJoyWork, type: Int, resultCallback: Runnable)
}

/**
 * 团队待办详情中所有 viewHolder
 */
class SelfListLoadMoreVH(
    context: Context,
    parent: ViewGroup,
    private val itf: SelfListLoadMoreVHItf,
    private val adapter: RecyclerView.Adapter<*>
) : RecyclerView.ViewHolder(
    context.inflater.inflate(
        R.layout.jdme_joywork_list_item_loadmore,
        parent,
        false
    )
) {
    private val loadMore = itemView.findViewById<LinearLayout>(R.id.loadMore)
    private val loadMoreText = itemView.findViewById<TextView>(R.id.loadMoreText)
    private val loadMoreIcon = itemView.findViewById<View>(R.id.loadMoreIcon)
    private val pb = itemView.findViewById<View>(R.id.pb)
    private val LoadMoreJoyWork.group: Group
        get() {
            return expandableGroup.title.extra as Group
        }

    private fun updateUI(joyWork: JoyWork) {
        val loadMoreJoyTask = joyWork as LoadMoreJoyWork
        if (loadMoreJoyTask.isLoading) {
            pb.visible()
            loadMoreText.setText(R.string.libui_loading)
            loadMoreIcon.gone()
        } else {
            pb.gone()
            loadMoreText.setText(R.string.me_load_more_data)
            loadMoreIcon.visible()
        }
    }

    fun bind(joyWork: LoadMoreJoyWork) {
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        updateUI(joyWork)
        loadMore.tag = joyWork
        loadMore.setOnClickListener {
            val loadMoreJoyWork = it.tag as LoadMoreJoyWork
            if (loadMoreJoyWork.isLoading) {
                return@setOnClickListener
            }
            loadMoreJoyWork.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            itf.loadMore(loadMoreJoyWork, loadMoreJoyWork.blockType) {
                loadMoreJoyWork.isLoading = false
                adapter.notifyItemChanged(adapterPosition)
            }
        }
    }
}

interface SelfListGroupTitleVHItf {
    fun edit(title: String, group: Group)
    fun del(remainTasks: Boolean, group: Group)
    fun newGroup(title: String, before: Boolean, group: Group?)
    fun expandGroup(title: JoyWorkTitle)
}

class SelfListGroupTitleVH(
    context: Context,
    parent: ViewGroup,
    private val adapter: SelfListGroupTitleVHItf
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_project_title,
            parent,
            false
        )
    ), Exchangeable {
    private val icon = itemView.findViewById<TextView>(R.id.icon)
    private val title = itemView.findViewById<TextView>(R.id.title)
    private val indicator = itemView.findViewById<TextView>(R.id.indicator)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_joywork_group_small).toInt()

    override var exchangeable: Boolean = false

    private val iconClick = View.OnClickListener {
        showAction(it.tag as Group)
    }

    companion object {
        // 是否初始化过
        fun JoyWorkTitle.isInit(): Boolean {
            return (extra as Group).isInit
        }

        val JoyWorkTitle.group: Group
            get() {
                return extra as Group
            }
    }

    fun bind(
        joyWorkTitle: JoyWorkTitle,
        expanded: Boolean,
        first: Boolean
    ) {
        itemView.setTag(R.id.jdme_tag_id, joyWorkTitle)
        if (first) {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingSmall,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        } else {
            itemView.setPadding(
                itemView.paddingLeft,
                paddingBig,
                itemView.paddingRight,
                itemView.paddingBottom
            )
        }
        val params: ViewGroup.LayoutParams = itemView.layoutParams
        params.height = CommonUtils.dp2px(21.0f) + itemView.paddingTop + itemView.paddingBottom
        itemView.layoutParams = params

        /**
         * 没权限
         */
        if (!joyWorkTitle.group.safePermissions.contains(ProjectPermissionEnum.GROUP.code)) {
            icon.gone()
        } else {
            icon.visible()
            icon.tag = joyWorkTitle.group
            icon.setOnClickListener(iconClick)
        }
        title.text = joyWorkTitle.group.safeTitle(itemView.context)
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }
    }

    private fun showAction(group: Group) {
        val actions = getGroupAction(group)
        showGroupAction(itemView.context, actions) {
            when (it) {
                is ProjectRename -> {
                    showGroupNameEditDialog(itemView.context, group.title) {
                        adapter.edit(it, group)
                    }
                }
                is ProjectNewAfter -> {
                    newGroup(false, group)
                }
                is ProjectNewBefore -> {
                    newGroup(true, group)
                }
                is ProjectDelete -> {
                    showGroupDelAlertDialog(itemView.context, group) {
                        adapter.del(it, group)
                    }
                }
            }
        }
    }

    private fun newGroup(before: Boolean, group: Group) {
        showNewGroupDialog(itemView.context, group) {
            adapter.newGroup(it, before, group)
        }
    }

    private fun getGroupAction(group: Group): List<JoyWorkAction> {
        val r = java.util.ArrayList<JoyWorkAction>()
        // 默认分组不能前插
        if (!group.isDefaultGroup) {
            // 默认分组不能改名
            r.add(ProjectRename(itemView.context))
            r.add(ProjectNewBefore(itemView.context))
        }
        r.add(ProjectNewAfter(itemView.context))
        // 默认分组不能删除
        if (!group.isDefaultGroup) {
            r.add(ProjectDelete(itemView.context))
        }
        return r
    }

    fun getJoyWorkTitle(): JoyWorkTitle {
        return itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
    }

    /**
     * 用于更新标题的数量
     */
    fun updateCount() {
        val joyWorkTitle = itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
//        count.text = "${Math.max(joyWorkTitle.count, 0)}" // 保证不会显示成负数
    }

    fun expandIfNecessary() {
        val title = itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
        if (!title.expandableGroup!!.expand) {
            adapter.expandGroup(title)
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

interface SelfListNewGroupVHItf {
    fun newGroup(title: String, before: Boolean, group: Group?)
    fun getAllGroups(): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>
}

class SelfListNewGroupVH(
    context: Context,
    parent: ViewGroup,
    private val itf: SelfListNewGroupVHItf
) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_project_new_group,
            parent,
            false
        )
    ) {

    private val root = itemView.findViewById<View>(R.id.root)

    fun bind(joyWorkTitle: ProjectAddTitle) {
        root.setTag(R.id.jdme_tag_id, joyWorkTitle)
        root.setOnClickListener {
            val group = getJoyWorkTitle(it).expandableGroup
            val groups = itf.getAllGroups()
            (groups.indexAtWithNull(groups.indexOf(group) - 1)?.title?.extra as? Group)?.apply {
                newGroup(this)
            }
        }
    }

    private fun newGroup(group: Group) {
        showNewGroupDialog(itemView.context, group) {
            itf.newGroup(it, false, group)
        }
    }

    private fun getJoyWorkTitle(view: View): JoyWorkTitle {
        return view.getTag(R.id.jdme_tag_id) as JoyWorkTitle
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

interface SelfListCustomGroupLoadMoreVHItf {
    fun customGroupLoadMore(groupId: String, count: Int, callback: (Group?, Boolean) -> Unit)
    fun refreshData()
}

class SelfListCustomGroupLoadMoreVH(
    itemView: View,
    private val adapter: RecyclerView.Adapter<*>,
    private val itf: SelfListCustomGroupLoadMoreVHItf
) : RecyclerView.ViewHolder(itemView) {
    private val loadMore = itemView.findViewById<LinearLayout>(R.id.loadMore)
    private val loadMoreText = itemView.findViewById<TextView>(R.id.loadMoreText)
    private val loadMoreIcon = itemView.findViewById<View>(R.id.loadMoreIcon)
    private val pb = itemView.findViewById<View>(R.id.pb)
    private val LoadMoreJoyWork.group: Group
        get() {
            return expandableGroup.title.extra as Group
        }

    private fun updateUI(joyWork: JoyWork) {
        val loadMoreJoyTask = joyWork as LoadMoreJoyWork
        if (loadMoreJoyTask.isLoading) {
            pb.visible()
            loadMoreText.setText(R.string.libui_loading)
            loadMoreIcon.gone()
        } else {
            pb.gone()
            loadMoreText.setText(R.string.me_load_more_data)
            loadMoreIcon.visible()
        }
    }

    fun bind(joyWork: LoadMoreJoyWork) {
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        updateUI(joyWork)
        loadMore.tag = joyWork
        loadMore.setOnClickListener {
            val loadMoreJoyWork = it.tag as LoadMoreJoyWork
            if (loadMoreJoyWork.isLoading) {
                return@setOnClickListener
            }
            loadMoreJoyWork.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            itf.customGroupLoadMore(
                loadMoreJoyWork.group.groupId,
                JoyWorkEx.countJoyWork(loadMoreJoyWork.expandableGroup.realItems)
            ) { data: Group?, success ->
                if (success && data != null) {
                    val items =
                        loadMoreJoyWork.expandableGroup.realItems as java.util.ArrayList<JoyWork>
                    loadMoreJoyWork.isLoading = false
                    if (data.groupId == (loadMoreJoyWork.expandableGroup.title.extra as Group).groupId) {
                        items.remove(loadMoreJoyWork)
                        items.addAll(data.tasks)
                        val allLoaded = JoyWorkEx.countJoyWork(data.tasks) < ProjectRepo.PAGE_LIMIT
                        if (!allLoaded) {
                            items.add(loadMoreJoyWork)
                        }
                        loadMoreJoyWork.expandableGroup.notifyItemChange()
                    }
                    itf.refreshData()
                    adapter.notifyDataSetChanged()
                } else {
                    loadMoreJoyWork.isLoading = false
                    adapter.notifyItemChanged(adapterPosition)
                }
            }
        }
    }
}