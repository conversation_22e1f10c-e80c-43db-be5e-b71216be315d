package com.jd.oa.joywork.detail.ui

import android.content.Intent
import com.google.gson.reflect.TypeToken
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.common.EventWebFragment
import com.jd.oa.joywork.detail.data.db.UpdateReporter.reportUpdate
import com.jd.oa.joywork.detail.data.db.UpdateReporter.updateWorkInfo
import com.jd.oa.joywork.detail.viewmodel.TaskDetailBindingAdapter
import com.jd.oa.joywork.urge.JoyWorkUrgeActivity
import com.jd.oa.joywork.urge.JoyWorkUrgeActivity.Companion.TYPE_JOYWORK
import com.jd.oa.joywork.urge.JoyWorkUrgeActivity.Companion.dataCache
import com.jd.oa.joywork.urge.JoyWorkUrgeActivity.Companion.sendResultCallback
import com.jd.oa.model.ExecutorInfo
import com.jd.oa.model.WorkInfo
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.JsonUtils
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/9
 * @Description:
 */
class TaskDetailH5Fragment : EventWebFragment() {
    private var taskId: String? = null

    override fun initValue() {
        super.initValue()
        taskId = arguments?.getString(TaskDetailActivity.KEY_TASK_ID)
    }

    // 催办
    override fun urge(jsonObject: JSONObject, handler: CompletionHandler<Any>) {
        super.urge(jsonObject, handler)
        val optString = jsonObject.optString("data")
        val dataObject = JSONObject(optString)
        val ownersString = dataObject.getString("owners")
        val title = dataObject.getString("title")
        val owners = JsonUtils.getGson().fromJson<List<JoyWorkUser>>(
            ownersString,
            object : TypeToken<List<JoyWorkUser?>?>() {}.type
        )
        JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_URGE, JoyWorkConstant.DETAIL_URGE)
        owners.forEach { it.taskId = taskId }
        val intent = Intent(activity, JoyWorkUrgeActivity::class.java).apply {
            putExtra("taskId", taskId)
            putExtra("title", title)
            putExtra("type", TYPE_JOYWORK)
            val key = "${System.currentTimeMillis()}"
            putExtra("key", key)
            dataCache[key] = ArrayList(owners)
        }
        sendResultCallback = object : SendResultCallback {
            override fun onResult(state: Boolean) {
                val result = JSONObject().apply {
                    put("opts", JSONObject().apply {
                        put("state", state)
                    })
                }
                handler.setProgressData(result)
            }
        }
        activity?.startActivity(intent)
    }

    override fun detailPropertyChanged(jsonObject: JSONObject) {
        super.detailPropertyChanged(jsonObject)
        val dataObject = JSONObject(jsonObject.optString("data"))
        val workInfo = WorkInfo(dataObject.optString("taskId")).apply {
            title = dataObject.optString("title")
            finishAction = dataObject.optInt("finishAction")
            isThirdParty = dataObject.optBoolean("thirdProcess")
            mobileContent = dataObject.optString("mobileContent")
            startTime = dataObject.optLong("startTime")
            endTime = dataObject.optLong("endTime")
            status = dataObject.optInt("status")
            uiTaskStatus = dataObject.optInt("selfTaskStatus", TaskStatusEnum.UN_FINISH.code)
            executors = JsonUtils.getGson().fromJson(
                dataObject.optString("owners"),
                object : TypeToken<List<ExecutorInfo?>?>() {}.type
            )
        }
        updateWorkInfo(workInfo)
    }

    override fun notifyTaskSource(jsonObject: JSONObject) {
        super.notifyTaskSource(jsonObject)
        val optString = jsonObject.optString("data")
        val optObject = JSONObject(optString)
        val bizCode = optObject.optString("bizCode")
        val sourceName = optObject.optString("sourceName")
        val mobileContent = optObject.optString("mobileContent")
        TaskDetailBindingAdapter.jumpToSource(context, mobileContent, bizCode, sourceName)
    }

    override fun notifyTaskDetailRisk() {
        super.notifyTaskDetailRisk()
        reportUpdate()
    }
}