package com.jd.oa.joywork

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.Log
import android.webkit.JavascriptInterface
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.jd.me.web2.webview.JMEWebview
import com.jd.oa.AppBase
import com.jd.oa.configuration.TenantConfigManager
import com.jd.oa.fragment.js.hybrid.JsBrowser
import com.jd.oa.fragment.js.hybrid.JsDeviceInfo
import com.jd.oa.fragment.js.hybrid.JsEvent
import com.jd.oa.fragment.js.hybrid.JsIm
import com.jd.oa.fragment.js.hybrid.JsUser
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.detail.data.entity.Creator
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.Owner
import com.jd.oa.joywork.detail.fromOwner
import com.jd.oa.joywork.detail.ui.JsPopup
import com.jd.oa.joywork.detail.ui.WebViewStatistics
import com.jd.oa.joywork.search.SearchResult
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.DateShowUtils
import com.jd.oa.utils.HighlightText
import com.tencent.smtt.export.external.interfaces.WebResourceError
import com.tencent.smtt.export.external.interfaces.WebResourceRequest
import com.tencent.smtt.export.external.interfaces.WebResourceResponse
import com.tencent.smtt.sdk.WebSettings
import com.tencent.smtt.sdk.WebViewClient
import wendu.dsbridge.CompletionHandler
import java.util.Calendar


object JoyWorkEx {

    fun showCover(status: TaskStatusEnum, joyWork: JoyWork): Boolean {
        return when (status) {
            TaskStatusEnum.ALL -> {
                joyWork.isFinishing || joyWork.isDeleted
            }

            TaskStatusEnum.DELETED -> {
                joyWork.isDeleted
            }

            else -> {
                false
            }
        }
    }

    fun filterErrorMsg(s: String?): String {
        var len = 50
        kotlin.runCatching {
            len = AppBase.getTopActivity()?.resources?.getString(R.string.joywork_error_msg_length)
                ?.toInt()
                ?: 50
        }
        if (TextUtils.isEmpty(s) || s!!.trim().length > len || "未知错误" == s || s.contains("Connection timed out")) {
            return AppBase.getTopActivity()?.resources?.getString(R.string.joywork_cmn_error)
                ?: "获取数据失败，返回重试"
        }
        return s;
    }

    fun filterErrorMsg(s: String?, dv: String): String {
        var len = 50
        kotlin.runCatching {
            len = AppBase.getTopActivity()?.resources?.getString(R.string.joywork_error_msg_length)
                ?.toInt()
                ?: 50
        }
        if (TextUtils.isEmpty(s) || s!!.trim().length > len || "未知错误" == s) {
            return dv
        }
        return s;
    }

    /**
     * 判断 [target] 是否是 [data] 中最后一个 [JoyWork] 实例
     */
    fun isLastJoyWork(data: List<JoyWork>, target: JoyWork): Boolean {
        val last = data.last {
            it.isJoyWork()
        }
        return target == last
    }

    fun isFirstJoyWork(data: List<JoyWork>, target: JoyWork): Boolean {
        val first = data.first {
            it.isJoyWork()
        }
        return target == first
    }

    /**
     * 最后一个满足条件下标，没有返回 -1
     */
    fun lastIndex(data: List<JoyWork>, target: JoyWork, predicate: (JoyWork) -> Boolean): Int {
        var result = -1
        data.forEachIndexed { index, joyWork ->
            if (predicate(joyWork)) {
                result = index
            }
        }
        return result
    }

    fun countJoyWork(data: List<JoyWork>?): Int {
        return data?.count {
            it.isJoyWork()
        } ?: 0
    }

    /**
     * 用于获取通过时间分隔时的显示文字
     */
    fun getTimeTitleString(time: Long, context: Context): String {
        return DateShowUtils.getTimeShowText(time)
    }

    /**
     * 获取配置中的所有 appIds
     */
    fun getAppIds(appIds: MutableList<String?>) {
        val imDdService = AppJoint.service(ImDdService::class.java)
        val ret = kotlin.runCatching {
            var config =
                TenantConfigManager.getConfigStringByKey(TenantConfigManager.KEY_COLLABORATIVELYAPPS)
            config = config.replace("\\", "")
            val gson = GsonBuilder()
                .enableComplexMapKeySerialization()
                .setPrettyPrinting()
                .create()
            val ids = gson.fromJson<List<String?>?>(
                config,
                (object : TypeToken<List<String?>?>() {}).type
            )
            ids?.forEach {
                if (it.isLegalString()) {
                    appIds.add(it as String)
                }
            }
        }
        // 解析失败，也保证传一个进去。解析成功了，这里面会有，不需要传
        if (ret.isFailure) {
            appIds.add(imDdService.appID)
        }
    }

    fun selectContactEntity(maxNum: Int = 1, showSelf: Boolean = true): MemberListEntityJd {
        val appIds: MutableList<String?> = ArrayList()
        getAppIds(appIds)
        val entity = MemberListEntityJd()
        entity.setMaxNum(maxNum).setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setShowSelf(showSelf)
        return entity
    }
}

fun TextView?.emHighline(text: String) {
    if (this == null) {
        return
    }
    HighlightText.with(text).startTag(SearchResult.sStartTag).endTag(SearchResult.sEndTag)
        .color(Color.parseColor("#4C7CFF")).into(this)
}

fun Set<AlertType>?.hasLegalAlertType(): Boolean {
    if (this == null || !isLegalSet()) {
        return false
    } else if (size == 1 && first() == AlertType.NO) {
        return false
    }
    return true
}

/**
 * 是否是 JoyWork 对象，不包括子类
 */
fun Any.isJoyWork(): Boolean {
    return this::class.java == JoyWork::class.java
}

fun JoyWork.selectPlanTime(fg: FragmentManager, callback: (Long) -> Unit) {
    val dialog =
        com.wdullaer.materialdatetimepicker.date.DatePickerDialog.newInstance { view, year, monthOfYear, dayOfMonth ->
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = 0L
            calendar.set(Calendar.YEAR, year)
            calendar.set(Calendar.MONTH, monthOfYear)
            calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)
            view.dismiss()
            callback.invoke(calendar.timeInMillis)
        }
    dialog.setOnCancelListener {
        callback.invoke(-1)
    }
    val cur = Calendar.getInstance()
    cur.add(Calendar.DAY_OF_MONTH, 1)
    dialog.minDate = cur
    dialog.show(fg, "date_pick")
}

fun hasSpecialPermission(
    op: JoyWorkOp,
    enablePermission: Boolean?,
    permission: List<String>?
): Boolean {
    if (enablePermission != null && enablePermission) {
        if (!permission.isNullOrEmpty()) {
            for (s in permission) {
                if (TextUtils.equals(s, op.action)) {
                    return true
                }
            }
        }
        return false
    }
    return false
}

/**
 * @return true 表示修改较大，需要整体刷新；否则只刷新当前 item 即可
 */
fun JoyWork.updateFromContent(joyWorkDetail: JoyWorkDetail): Boolean {
    if (taskStatus != joyWorkDetail.taskStatus) {// 待办状态变化，需要整体刷新，因为会导致权限变化
        return true
    }
    // 子待办再补，由详情页返回是否有子待办变化，这里不好判断
    title = joyWorkDetail.title
    joyWorkDetail.endTime?.apply {
        <EMAIL> = joyWorkDetail.endTime
    }
    priorityType = joyWorkDetail.priorityType
    readStatus = joyWorkDetail.readStatus
    return false
}

fun JoyWorkUser?.isSelf(): Boolean {
    return this?.run {
        emplAccount == PreferenceManager.UserInfo.getUserName() && ddAppId == PreferenceManager.UserInfo.getTimlineAppID()
    } ?: false
}

fun Owner?.isSelf(): Boolean {
    return this?.run {
        emplAccount == PreferenceManager.UserInfo.getUserName() && ddAppId == PreferenceManager.UserInfo.getTimlineAppID()
    } ?: false
}

fun Members?.isSelf(): Boolean {
    return this?.run {
        emplAccount == PreferenceManager.UserInfo.getUserName() && ddAppId == PreferenceManager.UserInfo.getTimlineAppID()
    } ?: false
}

/**
 * 当前登录人是不是待办的创建者
 */
fun Creator?.selfIsCreator(): Boolean {
    return this?.run {
        emplAccount == PreferenceManager.UserInfo.getUserName() && ddAppId == PreferenceManager.UserInfo.getTimlineAppID()
    } ?: false
}

fun JoyWork?.selfIsCreator(): Boolean {
    /**
     * 后台没返回 assigner 的时候就认为自己是创建人 2021年12月15日
     */
    return this?.run {
        if (assigner == null) {
            true
        } else {
            assigner?.emplAccount == PreferenceManager.UserInfo.getUserName() && assigner?.ddAppId == PreferenceManager.UserInfo.getTimlineAppID()
        }
    } ?: false
}

/**
 * 按负责人、自己顺序排列所有执行人
 */
fun JoyWork?.sortOwners() {
    val os = this?.owners
    if (!os.isLegalList()) {
        return
    } else if (os!!.size == 1) {
        return
    } else {
        os.sortBy {
            // 负责人排前面，自己其次，其余的一样
            when {
                it.chief.isChief() -> {
                    1
                }

                it.isSelf() -> {
                    50
                }

                else -> {
                    100
                }
            }
        }
    }
}

fun List<JoyWork>?.countJoyWork(): Int {
    return this?.count {
        it.isJoyWork()
    } ?: 0
}

fun JoyWorkWrapper?.countJoyWork(): Int {
    var ans = 0
    this?.clientTasks?.forEach {
        ans += JoyWorkEx.countJoyWork(it.tasks)
    }
    return ans
}

fun List<JoyWork>?.multiPurposeItem(): CardBottomMultiPurpose<*>? {
    return this?.firstOrNull {
        it is CardBottomMultiPurpose<*>
    } as? CardBottomMultiPurpose<*>?
}

/**
 * 是否是真负责人。由于历史原因，现在代码中的 Owner 为执行人
 * 真正的负责为叫 chief。虽然它也是 Owner 对象，但实际上不同类
 * chief：真负责人、首席
 */
fun Int?.isChief(): Boolean {
    return (this ?: 0) == 1
}

fun JoyWorkDetail?.toJoyWork(): JoyWork? {
    if (this == null)
        return null
    val joyWork = JoyWork()
    joyWork.taskId = taskId
    joyWork.owners = ArrayList()
    owners?.forEach {
        if (!TaskStatusEnum.isFinish(it.taskStatus)) {
            val user = JoyWorkUser()
            user.fromOwner(it)
            joyWork.owners.add(user)
        }
    }
    joyWork.title = title
    return joyWork
}

fun JMEWebview.bindJsBrowser(jsSdkKit: JsSdkKit, callback: (CompletionHandler<Any>?) -> Unit) {
    addJavascriptObject(object : JsBrowser(this, JsSdkKit(), null, null, null, null) {
        @JavascriptInterface
        override fun onKeyboardHeightChange(args: Any?, handler: CompletionHandler<Any>?) {
            callback(handler)
        }
    }, JsBrowser.DOMAIN)
}

fun JMEWebview.initJoyWork(url: String): JsSdkKit {
    val webView = this
    webView.setOnLongClickListener { true }
    val jsSdkKit = JsSdkKit();
    webView.addJavascriptObject(JsDeviceInfo(jsSdkKit), JsDeviceInfo.DOMAIN)
    webView.addJavascriptObject(object : JsEvent(webView) {
        @JavascriptInterface
        override fun sendEvent(args: Any, handler: CompletionHandler<Any>) {
            super.sendEvent(args, handler)
            WebViewStatistics.end()
        }
    }, JsEvent.DOMAIN)
    webView.addJavascriptObject(JsIm(jsSdkKit, null), JsIm.DOMAIN)
    webView.addJavascriptObject(JsPopup(), JsPopup.DOMAIN)
    webView.addJavascriptObject(JsUser(webView, null), JsUser.DOMAIN)
    webView.settings.cacheMode = WebSettings.LOAD_DEFAULT
    webView.settings.textZoom = 100
    val start = System.currentTimeMillis()
    webView.webViewClient = object : WebViewClient() {

        override fun onReceivedError(
            webView: com.tencent.smtt.sdk.WebView,
            webResourceRequest: WebResourceRequest,
            webResourceError: WebResourceError
        ) {
            super.onReceivedError(webView, webResourceRequest, webResourceError)
        }

        private var lastUrl: String? = null
        private var lastStart = start
        override fun shouldInterceptRequest(
            webView: com.tencent.smtt.sdk.WebView,
            webResourceRequest: WebResourceRequest
        ): WebResourceResponse? {
            if (lastUrl != null) {
                Log.e(
                    WebViewStatistics.TAG,
                    "shouldInterceptRequest lastUrl = " + lastUrl + ", " + (System.currentTimeMillis() - lastStart)
                )
            }
            if (webResourceRequest.url != null) {
                lastUrl = webResourceRequest.url.toString()
                lastStart = System.currentTimeMillis()
            }
            return super.shouldInterceptRequest(webView, webResourceRequest)
        }

        override fun shouldOverrideUrlLoading(
            view: com.tencent.smtt.sdk.WebView,
            url: String
        ): Boolean {
            view.loadUrl(url)
            return true
        }
    }
    WebViewStatistics.start()
    loadUrl(url)
    return jsSdkKit
}
