package com.jd.oa.joywork.self.base

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.*
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.repo.LoadingViewStrategy
import com.jd.oa.joywork.self.base.adapter.DataProcessor
import com.jd.oa.joywork.self.base.adapter.vh.GroupVH
import com.jd.oa.joywork.self.group.SingleViewType
import com.jd.oa.joywork.team.*
import com.jd.oa.joywork.team.TeamBaseFragment.Companion.getProjectId
import com.jd.oa.joywork.team.TeamTitleVH.Companion.group
import com.jd.oa.joywork.team.TeamTitleVH.Companion.isInit
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.bean.ProjectGroupTypeEnum
import com.jd.oa.joywork.team.bean.TeamGroupExtend
import com.jd.oa.joywork.team.kpi.PlaceHolderVH
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.joywork.team.view.ItemContainer
import com.jd.oa.utils.*
import java.util.*
import kotlin.collections.ArrayList

interface SelfListBaseAdapterItf : Refreshable {
    fun loadMoreWithGroup(
        type: Int,
        count: Int,
        callback: (data: List<JoyWork>, type: Int) -> Unit
    )

    fun customGroupLoadMore(groupId: String, count: Int, callback: (Group?, Boolean) -> Unit)

    fun projectClick(
        joyWork: JoyWork,
        callback: (JoyWorkDetail.Project?, isDel: Boolean) -> Unit
    )

    /**
     * 使用分组排序时，返回当前的 projectId
     */
    fun getProjectId(): String

    fun getProjectType(): Int
}

/**
 * 我的待办 adapter。里面分为不同的块
 */
open class SelfListBaseAdapter(
    val fragment: Fragment,
    private val itf: SelfListBaseAdapterItf,
    groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    private val recyclerView: HRecyclerView?,
    val context: FragmentActivity
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(), SelfListItemVHItf, SelfListTitleItf,
    SelfListLoadMoreVHItf, SelfListGroupTitleVHItf, SelfListNewGroupVHItf,
    SelfListCustomGroupLoadMoreVHItf {

    val processor: DataProcessor = DataProcessor(groups)

    val groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>
        get() {
            return processor.groups
        }

    fun replaceAll(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        processor.updateAll(groups)
        notifyDataSetChanged()
    }

    // 将待办插入至某些分组中
    fun insert(joyWork: JoyWork, groupIds: List<String>): Boolean {
        if (!groupIds.isLegalList()) {
            return false
        }
        var change = false
        processor.groups.forEach {
            if (groupIds.contains((it.getTitle().extra as? Group)?.groupId)) {
                val clone = joyWork.selfClone()
                clone.expandableGroup = it
                it.getRealItems().add(0, clone)
                change = true
            }
        }
        if (change) {
            processor.refreshData()
            notifyDataSetChanged()
            return true
        }
        return false
    }

    // 新增
    fun append(tmpGroups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        processor.appendGroups(tmpGroups)
        notifyDataSetChanged()
    }

    val fg: FragmentManager = context.supportFragmentManager

    val groupTitleSingleViewType = SingleViewType<GroupVH>(groups, 3525)

    fun restore(joyWork: JoyWork) {
        processor.restore(joyWork)
        kotlin.runCatching {
            notifyDataSetChanged()
        }
    }

    override fun getItemCount(): Int {
        return processor.getData().size
    }

    open fun sortJoyWork(joyWork: JoyWork) {
        val param = processor.getLocationInfo(joyWork)
        JoyWorkRepo.sortJoyWork(joyWork.taskId, param, object : JoyWorkUpdateCallback {
            override fun onStart() {
                PromptUtils.showLoadDialog(
                    context,
                    context.string(R.string.me_feedback_tab_processing),
                    false
                )
            }

            override fun result(success: Boolean, errorMsg: String) {
                PromptUtils.removeLoadDialog(context)
                if (success) {
                    // 刷新整个列表。里面可能涉及到多个地方的数据修改。比如时间头的移除/添加，组中数量的修改等
                    processor.filterAndSyncData(context)
                    notifyDataSetChanged()
                } else {
                    // 恢复原样
                    restore(joyWork)
                    notifyDataSetChanged()
                    ToastUtils.showInfoToast(errorMsg)
                }
            }
        })
    }

    fun expandAll() {
        groups.forEach {
            processor.expandGroup(it.title)
        }
        notifyDataSetChanged()
    }

    private val titleType = 100 // 普通标题
    private val titleTypeTextImage = titleType + 1 // 执行人头像+姓名标题
    private val titleTypePriority = titleType + 2 // 优先级
    private val titleTypeGroup = titleType + 3 // 分组标题
    private val loadMoreType = 200
    private val customGroupLoadMore = loadMoreType + 1 // 自定义分组时加载更多
    private val newGroupItem = 300
    private val itemType = 400
    private val otherType = 1000 // 可能未考虑到的异常情况

    override fun getItemViewType(position: Int): Int {
        return when (val data = processor.getData()[position]) {
            is ProjectAddTitle -> { // 新建分组
                newGroupItem
            }
            is JoyWorkTitle -> {
                val extra = data.extra
                if (extra is TeamGroupExtend) {
                    if (extra.user == null) {
                        titleTypePriority
                    } else {
                        titleTypeTextImage
                    }
                } else if (extra is Group) {
                    titleTypeGroup
                } else {
                    titleType // 普通的标题
                }
            }
            is ProjectLoadMore -> {
                customGroupLoadMore
            }
            is LoadMoreJoyWork -> {
                loadMoreType
            }
            is JoyWork -> {
                itemType
            }
            else -> {
                otherType
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            customGroupLoadMore -> {
                SelfListCustomGroupLoadMoreVH(
                    context.inflater.inflate(
                        R.layout.jdme_joywork_list_item_loadmore,
                        parent,
                        false
                    ), this, this
                )
            }
            newGroupItem -> {
                SelfListNewGroupVH(context, parent, this)
            }
            titleType -> {
                SelfListTitleVH(context, parent, this)
            }
            titleTypeGroup -> {
                SelfListGroupTitleVH(context, parent, this)
            }
            titleTypePriority -> {
                SelfListTextImageVH(context, parent)
            }
            titleTypeTextImage -> {
                SelfListTextImageVH(context, parent)
            }
            loadMoreType -> {
                SelfListLoadMoreVH(context, parent, this, this)
            }
            itemType -> {
                return SelfListItemVH(context, parent, this, this)
            }
            else -> {
                PlaceHolderVH(context)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        holder.itemView.visible()
        val data = processor.getData()[position]
        when (holder) {
            is SelfListCustomGroupLoadMoreVH -> {
                holder.bind(data as ProjectLoadMore)
            }
            is SelfListLoadMoreVH -> {
                holder.bind(data as LoadMoreJoyWork)
            }
            is SelfListNewGroupVH -> {
                holder.bind(data as ProjectAddTitle)
            }
//            is TeamLoadMoreVH2 -> {
//                holder.bind(data as ProjectLoadMore2)
//            }
            is SelfListTextImageVH -> {
                holder.bind(data as JoyWorkTitle, position == 0)
            }
            is SelfListGroupTitleVH -> {
                holder.bind(data as JoyWorkTitle, data.expandableGroup!!.expand, position == 0)
                holder.itemView.setOnClickListener(groupClick)
            }
            is SelfListTitleVH -> {
                holder.bind(
                    data as JoyWorkTitle,
                    data.expandableGroup!!.expand,
                    position == 0
                )
            }
            is SelfListItemVH -> {
                holder.bind(data as JoyWork, processor.getData())
            }
            else -> {
                holder.itemView.gone()
            }
        }
    }

    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        super.onViewAttachedToWindow(holder)
        val itemView = holder.itemView
        if (itemView is ItemContainer && !itemView.fixPos()) {
            itemView.scrollTo(recyclerView?.totalDx ?: 0, 0)
        } else {
            holder.itemView.scrollTo(0, 0)
        }
    }

    // begin SelfListItemVHItf
    override fun getOwnerFragment(): Fragment {
        return fragment
    }

    override fun itemTimeType(): Int {
        return SelfListItemVHItf.TYPE_DL
    }

    override fun projectClick(
        joyWork: JoyWork,
        callback: (JoyWorkDetail.Project?, isDel: Boolean) -> Unit
    ) {
        itf.projectClick(joyWork, callback)
    }

    override fun getData(): List<Any> {
        return processor.getData()
    }

    override fun refresh() {
        itf.refresh()
    }

    override fun refreshData() {
        processor.refreshData()
    }
    // end SelfListItemVHItf

    // SelfListTitleItf begin

    override fun itemClick(title: JoyWorkTitle) {
        processor.toggleGroup(title)
        notifyDataSetChanged()
    }

    // end SelfListTitleItf

    // begin SelfListLoadMoreVHItf
    override fun loadMore(
        loadMoreJoyWork: LoadMoreJoyWork,
        type: Int,
        resultCallback: Runnable
    ) {
        itf.loadMoreWithGroup(
            type,
            JoyWorkEx.countJoyWork(loadMoreJoyWork.expandableGroup.realItems)
        ) { it, id ->
            if (!Objects.equals(id, type)) {
                return@loadMoreWithGroup
            }
            if (!it.isLegalList()) {
                return@loadMoreWithGroup
            }
            val items = loadMoreJoyWork.expandableGroup.realItems as ArrayList<JoyWork>
            items.remove(loadMoreJoyWork)
            items.addAll(it)
            val allLoaded = JoyWorkEx.countJoyWork(it) < ProjectRepo.PAGE_LIMIT
            if (!allLoaded) {
                items.add(loadMoreJoyWork)
            }
            loadMoreJoyWork.expandableGroup.notifyItemChange()
            processor.refreshData()
            notifyDataSetChanged()
            resultCallback.run()
        }
    }

    // end SelfListLoadMoreVHItf

    // begin SelfListGroupTitleVHItf
    private val groupClick = View.OnClickListener {
        val title = it.getTag(R.id.jdme_tag_id) as JoyWorkTitle
        toggleGroup(title)
    }

    protected fun toggleGroup(title: JoyWorkTitle) {
        if (title.isInit()) {
            processor.toggleGroup(title)
            notifyDataSetChanged()
        } else {
            //加载更多，然后再 toggleGroup 一次
            itf.customGroupLoadMore(
                title.group.groupId,
                0
            ) { data: Group?, success ->
                if (success && data != null) {
                    title.group.initialize = true
                    if (data.groupId == title.group.groupId) {
                        title.expandableGroup.ensureRealItems()
                        val items = title.expandableGroup.realItems
                        items.clear()
                        items.addAll(data.tasks)
                        val allLoaded = JoyWorkEx.countJoyWork(data.tasks) < ProjectRepo.PAGE_LIMIT
                        if (!allLoaded) {
                            items.add(ProjectLoadMore(itf.getProjectId(), title.group.groupId))
                        }
                        title.expandableGroup.notifyItemChange()
                    }
                    processor.refreshData()
                    toggleGroup(title)// 再次打开
                }
            }
        }
    }

    override fun edit(title: String, group: Group) {
        ProjectRepo.updateGroup(group.groupId, title) { success: Boolean, msg: String? ->
            if (success) {
                group.title = title
                group.type = ProjectGroupTypeEnum.COMMON.code
                updateGroupTitle(group)
            } else {
                Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                    .show()
            }
        }
    }

    private fun updateGroupTitle(group: Group) {
        val data = processor.getData()
        data.forEach {
            if (it is JoyWorkTitle && it.extra == group) {
                it.title = group.safeTitle(context)
            }
        }
        notifyDataSetChanged()
    }

    override fun del(remainTasks: Boolean, group: Group) {
        ProjectRepo.delGroup(itf.getProjectId(), group.groupId, remainTasks) { it, msg ->
            if (it) {
                refresh()
            } else {
                ToastUtils.showInfoToast(msg ?: "")
            }
        }
    }

    override fun newGroup(title: String, before: Boolean, group: Group?) {
        ProjectRepo.createGroup(
            itf.getProjectId(),
            title,
            if (before) {
                null
            } else group?.groupId,
            if (before) group?.groupId else null,
        ) { success: Boolean, resultGroup: Group?, msg: String? ->
            if (success && resultGroup != null) {
                resultGroup.initialize = true // 新建分组，初始化过
                val newTitle = JoyWorkTitle(
                    R.string.icon_edit_delete,
                    resultGroup.safeTitle(context),
                    resultGroup.safeTotal,
                    Color.TRANSPARENT
                )
                newTitle.extra = resultGroup
                resultGroup.permissions = group?.permissions

                val newGroup =
                    ExpandableGroup(newTitle, ArrayList<JoyWork>(), resultGroup.groupId, true)
                var groupIndex = processor.groups.indexOfFirst {
                    (it.getTitle()?.extra as? Group)?.groupId == group?.groupId
                }
                groupIndex = Math.max(groupIndex, 0)
                // 前插
                if (before) {
                    processor.addGroup(newGroup, groupIndex)
                } else {// 后插
                    processor.addGroup(newGroup, groupIndex + 1)
                }
                notifyDataSetChanged()
            } else {
                Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun expandGroup(title: JoyWorkTitle) {
        if (title.expandableGroup?.expand != false) {
            return
        }
        val deltaSize = processor.toggleGroup(title)
        notifyItemRangeInserted(processor.getData().indexOf(title) + 1, Math.abs(deltaSize))
        notifyItemChanged(processor.getData().indexOf(title))
    }
    // end SelfListGroupTitleVHItf

    // begin SelfListNewGroupVHItf
    override fun getAllGroups(): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return groups
    }
    // end SelfListNewGroupVHItf

    // begin SelfListCustomGroupLoadMoreVHItf
    override fun customGroupLoadMore(
        groupId: String,
        count: Int,
        callback: (Group?, Boolean) -> Unit
    ) {
        itf.customGroupLoadMore(groupId, count, callback)
    }
    // end SelfListCustomGroupLoadMoreVHItf

}