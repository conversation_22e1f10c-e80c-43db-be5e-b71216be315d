package com.jd.oa.joywork.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.Menu;
import android.view.View;

import com.jd.me.web2.webview.JMEWebview;

public class JoyWorkWebView extends JMEWebview {
    public JoyWorkWebView(Context arg0, AttributeSet arg1) {
        super(arg0, arg1);
    }

    public JoyWorkWebView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public ActionMode startActionModeForChild(View originalView, ActionMode.Callback callback, int type) {
        ActionMode mode = super.startActionModeForChild(originalView, callback, type);
        Menu menu = mode.getMenu();
        menu.clear();
        return mode;
    }
}
