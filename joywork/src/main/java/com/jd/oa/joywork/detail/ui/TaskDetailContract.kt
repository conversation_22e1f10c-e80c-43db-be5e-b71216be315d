package com.jd.oa.joywork.detail.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.result.contract.ActivityResultContract
import com.jd.oa.joywork.JoyWorkDetailParam
import com.jd.oa.joywork.JoyWorkMediator
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.model.service.contract.JdActivityResultContract

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/1 15:02
 */

class JoyWorkTaskDetailContract(
    override val input: JoyWorkDetailParam,
    val callback: IServiceCallback<DetailReturnParcel?>
) :
    JdActivityResultContract<JoyWorkDetailParam, DetailReturnParcel?>(callback) {
    companion object {
        const val CONTRACT_TAG = "fromContract"
    }

    override val contact: ActivityResultContract<JoyWorkDetailParam, DetailReturnParcel?>
        get() = object : ActivityResultContract<JoyWorkDetailParam, DetailReturnParcel?>() {


            override fun createIntent(context: Context, input: JoyWorkDetailParam): Intent {
                return Intent(context, TaskDetailActivity::class.java).apply {
                    JoyWorkMediator.putParam(this, input)
                    //使用以前的修改数据回调流程
                    putExtra(TaskDetailActivity.KEY_BIZ_OBJ, DetailReturnParcel())
                    putExtra(CONTRACT_TAG, true)
                }
            }

            override fun parseResult(resultCode: Int, intent: Intent?): DetailReturnParcel? {
                if (resultCode != Activity.RESULT_OK) {
                    return null
                }
                return intent?.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as? DetailReturnParcel
            }
        }
}

