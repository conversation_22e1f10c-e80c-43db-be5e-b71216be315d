package com.jd.oa.joywork.group

import com.jd.oa.joywork.CoroutineResult
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.bean.JoyWorkWrapper
import kotlinx.coroutines.suspendCancellableCoroutine

suspend fun getGroupTasks(
    status: TaskStatusEnum,
    offset: Int,
    sessionId: String,
    userRole: TaskUserRole,
): CoroutineResult<JoyWorkWrapper> {
    return suspendCancellableCoroutine { c ->
        JoyWorkRepo.groupList(object : JoyWorkVMCallback() {
            override fun onError(msg: String) {
                c.resumeWith(Result.success(CoroutineResult<JoyWorkWrapper>().failure(msg)))
            }

            override fun call(wrapper: JoyWorkWrapper) {
                c.resumeWith(Result.success(CoroutineResult<JoyWorkWrapper>().success(wrapper)))
            }
        }, status, sessionId, offset, userRole, 30)
    }
}