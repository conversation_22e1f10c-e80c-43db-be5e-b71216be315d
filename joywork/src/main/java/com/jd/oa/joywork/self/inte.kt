package com.jd.oa.joywork.self

import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.dialog.biz.JoyWorkFilterDialog

interface Exchangeable {
    var exchangeable: Boolean
}

interface SelfBaseItf {
    /**
     * 当从 viewpager 中被切走时
     */
    fun onPageUnselected()

    /**
     * 当从 viewpager 中显示时
     */
    fun onPageSelected()

    fun onShowAtTabbar()

    /**
     * 获取列表类型
     */
    fun getTaskListType(): TaskListTypeEnum
}

/**
 * 个人待办支持筛选操作的接口
 */
interface SelfFilterItf {
    /**
     * 筛选中是否显示全部
     */
    fun showAll(): Boolean

    /**
     * 返回筛选选项中每一个 item 的点击事件 id
     */
    fun getFilterItemClickIds(): Map<TaskStatusEnum, String>

    /**
     * 重置当前筛选项
     */
    fun resetFilterItem()

    /**
     * 保存当前筛选项
     */
    fun saveFilterItem(item: JoyWorkFilterDialog.Item)

    /**
     * 获取当前筛选项
     */
    fun getFilterItem(): JoyWorkFilterDialog.Item

    fun getDefaultFilterItem(): JoyWorkFilterDialog.Item

    /**
     * 显示筛选结果界面
     */
    fun showFilterView()
}