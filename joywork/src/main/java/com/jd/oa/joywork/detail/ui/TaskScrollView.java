package com.jd.oa.joywork.detail.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class TaskScrollView extends NestedScrollView {
    private OnTaskScrollChangedListener mOnTaskScrollChangedListener;

    public TaskScrollView(@NonNull Context context) {
        super(context);
    }

    public TaskScrollView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public TaskScrollView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public interface OnTaskScrollChangedListener {
        void onScrollChanged();
    }

    @Override
    protected void onScrollChanged(int x, int y, int oldX, int oldY) {
        super.onScrollChanged(x, y, oldX, oldY);
        if (mOnTaskScrollChangedListener != null) {
            if (flagY == 0) {
                flagY = -2;
                int add = Math.abs(y - oldY);
                if (add > 5) {
                    mOnTaskScrollChangedListener.onScrollChanged();
                }
            }
        }
    }

    public void setOnTaskScrollChangedListener(OnTaskScrollChangedListener listener) {
        mOnTaskScrollChangedListener = listener;
    }

    private int flagY = -1;

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_UP) {
            flagY = -1;
        } else if (ev.getAction() == MotionEvent.ACTION_MOVE) {
            if (flagY == -1) {
                flagY = 0;
            }
        }

        return super.onTouchEvent(ev);
    }
}
