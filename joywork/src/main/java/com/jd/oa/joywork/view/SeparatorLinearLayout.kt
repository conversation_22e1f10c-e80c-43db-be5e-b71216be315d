package com.jd.oa.joywork.view

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import com.jd.oa.joywork.R

/**
 * 水平方向可设置间隔的线性布局
 */
class SeparatorLinearLayout(context: Context, attributeSet: AttributeSet) :
    LinearLayout(context, attributeSet) {
    private val mSeparator: Int

    init {
        val a = context.obtainStyledAttributes(attributeSet, R.styleable.SeparatorLinearLayout)
        mSeparator = a.getDimensionPixelOffset(R.styleable.SeparatorLinearLayout_separator, -1)
        a.recycle()
    }

    override fun onLayout(changed: <PERSON><PERSON><PERSON>, l: Int, t: Int, r: Int, b: Int) {
        if (mSeparator == -1 || orientation != HORIZONTAL) {
            super.onLayout(changed, l, t, r, b)
        } else {

        }
    }
}