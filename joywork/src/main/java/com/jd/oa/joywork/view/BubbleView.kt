package com.jd.oa.joywork.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.LayerDrawable
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.jd.oa.joywork.shortcut.LT2BubbleViewCallback
import com.jd.oa.joywork.R
import com.jd.oa.utils.DrawableEx

/**
 * 气泡 View。
 * 1. width = max(height, width)
 * 2. 只支持一个子 view
 * 3. 布局中设置的 width 无效，会被忽略
 * 4. 圆形时，会自动调用子 view 位置，使其居中
 * 5. 非圆形时，view 位置与普通 FrameLayout 一样，只不过水平方向上额外添加配置的 extraPadding
 * 6. 当处理子 view 高度超过当前 view 高度时，不处理。自己的垃圾代码自己负责
 *
 * 如果包含的子 view 是 TextView。在 textView 的文字长度小于 2 时会显示成圆形
 */
class BubbleView(context: Context, attributeSet: AttributeSet) :
    FrameLayout(context, attributeSet) {
    var mCallback: BubbleViewCallback = LT2BubbleViewCallback

    private val mBgPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val mBorderPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private val mBubbleViewConfig: BubbleViewConfig

    init {
        val a = context.obtainStyledAttributes(attributeSet, R.styleable.BubbleView)
        mBubbleViewConfig = mCallback.resetConfig(
            BubbleViewConfig(
                mBorderColor = a.getColor(
                    R.styleable.BubbleView_numViewBorderColor,
                    Color.TRANSPARENT
                ),
                mBorderWidth = a.getDimensionPixelSize(
                    R.styleable.BubbleView_numViewBorderWidth,
                    0
                ),
                mBgColor = a.getColor(R.styleable.BubbleView_numViewBgColor, Color.TRANSPARENT),
                mExtraPaddingH = a.getDimensionPixelSize(R.styleable.BubbleView_extraPaddingH, 0),
                mExtraPaddingPercent = a.getFloat(R.styleable.BubbleView_extraPaddingPercent, 0.0f)
                    .clamp(0.0f, 1.0f)
            )
        )
        mBorderPaint.color = mBubbleViewConfig.mBorderColor
        mBorderPaint.strokeWidth = mBubbleViewConfig.mBorderWidth.toFloat()
        mBgPaint.color = mBubbleViewConfig.mBgColor
        a.recycle()
        setWillNotDraw(false)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(
            MeasureSpec.makeMeasureSpec(
                MeasureSpec.getSize(widthMeasureSpec),
                MeasureSpec.UNSPECIFIED
            ), heightMeasureSpec
        )
        // 如果需要显示成圆
        if (mCallback.shouldCircular(getChildAt(0))) {
            if (measuredWidth == measuredHeight) {
                return
            }
            // 理论上这里不需要再次测量子 view，可以直接调用 setMeasuredDimension 更新当前 view 的 measureW 与 measureH
            super.onMeasure(
                MeasureSpec.makeMeasureSpec(
                    MeasureSpec.getSize(measuredHeight),
                    MeasureSpec.EXACTLY
                ),
                MeasureSpec.makeMeasureSpec(
                    MeasureSpec.getSize(measuredHeight),
                    MeasureSpec.EXACTLY
                )
            )
        } else {
            super.onMeasure(
                MeasureSpec.makeMeasureSpec(
                    measuredWidth + 2 * getExtraH(measuredHeight),
                    MeasureSpec.EXACTLY
                ),
                MeasureSpec.makeMeasureSpec(
                    MeasureSpec.getSize(measuredHeight),
                    MeasureSpec.EXACTLY
                )
            )
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        val child = getChildAt(0)
        if (mCallback.shouldCircular(child)) {
            val l = (measuredWidth - child.measuredWidth) / 2
            val t = (measuredHeight - child.measuredHeight) / 2
            child.layout(l, t, l + child.measuredWidth, t + child.measuredHeight)
        } else {
            val l = (measuredWidth - child.measuredWidth) / 2
            val t = (measuredHeight - child.measuredHeight) / 2
            child.layout(l, t, l + child.measuredWidth, t + child.measuredHeight)
        }
    }


    override fun addView(child: View?) {
        check(childCount <= 0) { "NumView can host only one direct child" }
        super.addView(child)
    }

    override fun addView(child: View?, index: Int) {
        check(childCount <= 0) { "NumView can host only one direct child" }
        super.addView(child, index)
    }

    override fun addView(child: View?, params: ViewGroup.LayoutParams?) {
        check(childCount <= 0) { "NumView can host only one direct child" }
        super.addView(child, params)
    }

    override fun addView(child: View?, index: Int, params: ViewGroup.LayoutParams?) {
        check(childCount <= 0) { "NumView can host only one direct child" }
        super.addView(child, index, params)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (mCallback.shouldCircular(getChildAt(0))) {
            drawCircleBg(canvas)
        } else {
            drawStadiumBg(canvas)
        }
    }

    private fun drawStadiumBg(canvas: Canvas) {
        if (mBubbleViewConfig.mBorderColor == Color.TRANSPARENT || mBubbleViewConfig.mBorderWidth <= 0) {
            //边框是透明色或者没有边框，显示的是背景色
            if (mBubbleViewConfig.mBgColor != Color.TRANSPARENT) {
                DrawableEx.roundSolidRect(
                    mBubbleViewConfig.mBorderColor,
                    height / 2.0f
                ).apply {
                    setBounds(0, 0, width, height)
                    draw(canvas)
                }
            }
        } else {
            // 边框有色，且有宽度
            if (mBubbleViewConfig.mBgColor != Color.TRANSPARENT) {
                // 如果有背景色
                val layers = arrayOf(
                    DrawableEx.roundSolidRect(mBubbleViewConfig.mBorderColor, height / 2.0f),
                    LayerDrawable(
                        arrayOf(
                            DrawableEx.roundSolidRect(
                                mBubbleViewConfig.mBgColor,
                                height / 2.0f
                            )
                        )
                    ).apply {
                        setLayerInset(
                            0,
                            mBubbleViewConfig.mBorderWidth,
                            mBubbleViewConfig.mBorderWidth,
                            mBubbleViewConfig.mBorderWidth,
                            mBubbleViewConfig.mBorderWidth
                        )
                    }
                )
                val ld = LayerDrawable(layers)
                ld.setBounds(0, 0, width, height)
                ld.draw(canvas)
            } else {
                // 没有背景色使用边框颜色绘制
                DrawableEx.roundSolidRect(
                    mBubbleViewConfig.mBorderColor,
                    height / 2.0f
                ).apply {
                    setBounds(0, 0, width, height)
                    draw(canvas)
                }
            }
        }
    }

    private fun drawCircleBg(canvas: Canvas) {
        if (mBubbleViewConfig.mBorderColor == Color.TRANSPARENT || mBubbleViewConfig.mBorderWidth <= 0) {
            //边框是透明色，忽略掉边框宽度
            if (mBubbleViewConfig.mBgColor != Color.TRANSPARENT) {
                canvas.drawCircle(width / 2.0f, height / 2.0f, width / 2.0f, mBgPaint)
            }
        } else {
            canvas.drawCircle(width / 2.0f, height / 2.0f, width / 2.0f, mBorderPaint)
            if (mBubbleViewConfig.mBgColor != Color.TRANSPARENT) {
                canvas.drawCircle(
                    width / 2.0f,
                    height / 2.0f,
                    width / 2.0f - mBubbleViewConfig.mBorderWidth,
                    mBgPaint
                )
            }
        }
    }

    private fun Float.clamp(s: Float, end: Float): Float {
        if (this <= s) {
            return s
        } else if (this > end) {
            return end
        }
        return this
    }

    private fun getExtraH(base: Int): Int {
        return if (mBubbleViewConfig.mExtraPaddingH <= 0) (base * mBubbleViewConfig.mExtraPaddingPercent).toInt() else mBubbleViewConfig.mExtraPaddingH
    }
}

data class BubbleViewConfig(
    var mBorderColor: Int,
    var mBgColor: Int,
    var mBorderWidth: Int,
    var mExtraPaddingH: Int,
    var mExtraPaddingPercent: Float
)

abstract class BubbleViewCallback {
    /**
     * 是否要显示成圆形
     */
    abstract fun shouldCircular(child: View): Boolean

    /**
     * 修改布局文件中配置的相关属性，只会在构造函数时调用一次
     */
    fun resetConfig(config: BubbleViewConfig): BubbleViewConfig {
        return config
    }
}