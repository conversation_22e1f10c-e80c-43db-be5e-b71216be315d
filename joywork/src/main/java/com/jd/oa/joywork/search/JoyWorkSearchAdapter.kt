package com.jd.oa.joywork.search

import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.dialog.CreateDialog
import com.jd.oa.joywork.union
import com.jd.oa.utils.HighlightText
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

class JoyWorkSearchAdapter(
    private val context: Context,
    dataOuter: List<SearchResult>
) : RecyclerView.Adapter<JoyWorkSearchAdapter.VH>() {
    private val mHighlightColor = Color.parseColor("#4C7CFF")

    var itemClick: ((SearchResult) -> Unit)? = null

    private val data = mutableListOf<SearchResult>()

    init {
        data.clear()
        data.addAll(dataOuter)
    }

    fun replace(new: List<SearchResult>) {
        data.clear()
        data.addAll(new)
        notifyDataSetChanged()
    }

    fun append(depts: List<SearchResult>) {
        data.union(depts) {
            it.taskId
        }
        notifyDataSetChanged()
    }

    private val itemClickListener = View.OnClickListener {
        val deptInfo = it.tag as SearchResult
        itemClick?.invoke(deptInfo)
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mTitle = itemView.findViewById<TextView>(R.id.mTitle)
        val mStatus = itemView.findViewById<TextView>(R.id.mStatus)
        val desc = itemView.findViewById<TextView>(R.id.desc)

        val mOwners = itemView.findViewById<TextView>(R.id.mOwners)
        val mOwnerContainer = itemView.findViewById<View>(R.id.mOwnerContainer)

        val mEndTime = itemView.findViewById<TextView>(R.id.tv_end_time)
        val mEndTimeContainer = itemView.findViewById<View>(R.id.deadline_container)

        val mProjects = itemView.findViewById<TextView>(R.id.mProjects)
        val mProjectContainer = itemView.findViewById<View>(R.id.mProjectContainer)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(
            context.inflater.inflate(
                R.layout.joywork_search_frg_item,
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val deptInfo = data[position]
        holder.mTitle.highlight(deptInfo.titleHighlightText ?: "")

        when (deptInfo.taskStatus) {
            TaskStatusEnum.UN_FINISH.code -> {
                holder.mStatus.visible()
                holder.mStatus.setText(R.string.joywork_screen_unfinish)
                holder.mStatus.setBackgroundResource(R.drawable.solid_all_2_f5f5f5)
            }
            TaskStatusEnum.FINISH.code -> {
                holder.mStatus.visible()
                holder.mStatus.setText(R.string.joywork_screen_finish)
                holder.mStatus.setBackgroundResource(R.drawable.stroke_all_2_e6e6e6)
            }
            else -> {
                holder.mStatus.gone()
            }
        }

        val r = deptInfo.remarkHighlightText
        if (r.isLegalString()) {
            holder.desc.visible()
            holder.desc.highlight(r)
        } else {
            holder.desc.gone()
        }

        val os = deptInfo.getOwnerHighlightText(context)
        if (os.isLegalString()) {
//            holder.mOwners.text = os
            holder.mOwners.highlight(os)
            holder.mOwnerContainer.visible()
        } else {
            holder.mOwnerContainer.gone()
        }

        val endTime = deptInfo.getEndTimeString(holder.mEndTime.resources)
        if (endTime.isLegalString()) {
            holder.mEndTimeContainer.visible()
            holder.mEndTime.text = endTime
        } else {
            holder.mEndTimeContainer.gone()
        }

        val proStr = deptInfo.getProjectsString(context)
        if (proStr.isLegalString()) {
            holder.mProjectContainer.visible()
            holder.mProjects.text = proStr
        } else {
            holder.mProjectContainer.gone()
        }

        holder.itemView.tag = deptInfo
        holder.itemView.setOnClickListener(itemClickListener)
    }

    override fun getItemCount() = data.size

    private fun TextView.highlight(text: String) {
        HighlightText.with(text).startTag(SearchResult.sStartTag).endTag(SearchResult.sEndTag)
            .color(mHighlightColor).into(this)
    }
}