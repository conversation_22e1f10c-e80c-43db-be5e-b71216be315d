package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.joywork.R
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.ui.IconFontView
import com.jd.oa.ui.dialog.IShitTitleController
import com.jd.oa.ui.dialog.IShitTitleDecorator
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.gone

abstract class CenterTextTitleDecorator : IShitTitleDecorator<TextView> {
    override fun decorate(t: TextView) {
        t.gravity = Gravity.CENTER
        t.setTextColor(Color.parseColor("#232930"))
        t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16.0f)
        t.setText(getTitleId())
        t.background = DrawableEx.roundSolidDirRect(
            Color.WHITE,
            t.context.resources.getDimension(getCornerRadiusId()),
            DrawableEx.DIR_TOP
        )
    }

    abstract fun getTitleId(): Int

    fun getCornerRadiusId(): Int {
        return R.dimen.joywork_corner
    }
}

/**
 * 文件标题
 */
open class TextShitTitleController(private val decorator: IShitTitleDecorator<TextView>) :
    IShitTitleController {
    override fun inflate(parent: FrameLayout): View {
        val inflater =
            parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        return (inflater.inflate(
            R.layout.joywork_dialog_text_title,
            parent,
            false
        ) as LinearLayout).apply {
            val textView = findViewById<TextView>(R.id.title)
            decorator.decorate(textView)
        }
    }
}

class CenterTextTitleController(decorator: CenterTextTitleDecorator) :
    TextShitTitleController(decorator) {
    constructor(id: Int) : this(object : CenterTextTitleDecorator() {
        override fun getTitleId(): Int {
            return id
        }

    })
}

abstract class StartTextTitleDecorator : IShitTitleDecorator<TextView> {
    override fun decorate(t: TextView) {
        t.gravity = Gravity.START
        t.setTextColor(Color.parseColor("#232930"))
        t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16.0f)
        t.setText(getTitleId())
        getTitleString()?.let {
            getTitleString().isLegalString().let { t.text = getTitleString() }
        }
        t.background = DrawableEx.roundSolidDirRect(
            Color.WHITE,
            t.context.resources.getDimension(getCornerRadiusId()),
            DrawableEx.DIR_TOP
        )
    }

    abstract fun getTitleId(): Int

    /**
     * id和string二选一，string传null时用ResId，string不为空时优先取string
     */
    abstract fun getTitleString(): String?

//    abstract fun canGoBack(): Boolean

    fun getCornerRadiusId(): Int {
        return R.dimen.joywork_corner
    }
}

class StartTextTitleController(decorator: StartTextTitleDecorator) :
    TextShitTitleController(decorator)

/**
 * @param data： 要显示的数据。first 为图标；second 为要显示的文字
 * @param click: item 点击事件
 * [T]：点击事件触发时，会原样返回。使用者可使用该字段区分点击位置
 */
class RvShitTitleController<T>(
    val data: List<Triple<Int, Int, T>>,
    val click: (T) -> Unit
) : IShitTitleController {

    private val clickLis = View.OnClickListener {
        val t = it.tag as T
        click(t)
    }

    override fun inflate(parent: FrameLayout): View {
        if (!data.isLegalList()) {
            parent.gone()
            return parent
        }
        val rv = parent.context.layoutInflater.inflate(
            R.layout.joywork_detail_more_hor,
            parent,
            false
        ) as RecyclerView
        rv.background = DrawableEx.roundSolidDirRect(
            Color.WHITE,
            parent.context.resources.getDimension(R.dimen.joywork_corner),
            DrawableEx.DIR_TOP
        )
        rv.layoutManager =
            LinearLayoutManager(parent.context, LinearLayoutManager.HORIZONTAL, false)
        rv.adapter = object : RecyclerView.Adapter<RvShitTitleControllerVH>() {
            override fun onCreateViewHolder(
                parent: ViewGroup,
                viewType: Int
            ): RvShitTitleControllerVH {
                return RvShitTitleControllerVH(
                    parent.context.layoutInflater.inflate(
                        R.layout.joywork_detail_more_hor_item,
                        parent,
                        false
                    )
                )
            }

            override fun onBindViewHolder(holder: RvShitTitleControllerVH, position: Int) {
                val pair = data[position]
                val view = holder.itemView
                view.tag = pair.third
                view.setOnClickListener(clickLis)
                holder.icon.setText(pair.first)
                holder.name.setText(pair.second)
            }

            override fun getItemCount(): Int {
                return data.size
            }

        }
        return rv
    }
}

private class RvShitTitleControllerVH(itemView: View) : RecyclerView.ViewHolder(itemView) {
    val icon: IconFontView = itemView.findViewById(R.id.icon)
    val name: TextView = itemView.findViewById(R.id.name)
}