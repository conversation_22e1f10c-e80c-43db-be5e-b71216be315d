package com.jd.oa.joywork.common

import android.content.Intent
import android.os.Parcelable
import java.io.Serializable

// 跨界面交互协议

private val activity_interaction_stub = "stub"

val activity_interaction_create = "c"
val activity_interaction_update = "u"
val activity_interaction_delete = "d"

interface InteractionResult {
    fun readFromIntent(intent: Intent): InteractionResult
    fun writeToIntent(intent: Intent)
}

val ParcelableStub = ParcelableInteractionResult(activity_interaction_stub, null)

/**
 * @param action: 执行的操作。取值 activity_interaction_create/activity_interaction_update/activity_interaction_delete
 * @param value: 交互后需要通知对方的参数
 */
class ParcelableInteractionResult(val action: String, val value: ArrayList<out Parcelable>?) :
    InteractionResult {

    override fun writeToIntent(intent: Intent) {
        intent.putExtra("action", action)
        if (value != null) {
            intent.putParcelableArrayListExtra("value", value)
        }
    }

    override fun readFromIntent(intent: Intent): ParcelableInteractionResult {
        val a = intent.getStringExtra("action")!!
        var v: ArrayList<Parcelable>? = null
        if (intent.hasExtra("value")) {
            v = intent.getParcelableArrayListExtra("value")
        }
        return ParcelableInteractionResult(a, v)
    }
}

val SerializableStub = SerializableInteractionResult(activity_interaction_stub, null)

/**
 * @param action: 执行的操作。取值 activity_interaction_create/activity_interaction_update/activity_interaction_delete
 * @param value: 交互后需要通知对方的参数
 */
class SerializableInteractionResult(
    val action: String,
    val value: ArrayList<out Serializable>?
) : InteractionResult {

    override fun writeToIntent(intent: Intent) {
        intent.putExtra("action", action)
        if (value != null) {
            intent.putExtra("size", value.size)
            value.forEachIndexed { index, t ->
                intent.putExtra("value$index", t)
            }
        }
    }

    override fun readFromIntent(intent: Intent): SerializableInteractionResult {
        val a = intent.getStringExtra("action")!!
        var v: ArrayList<Serializable>? = null
        if (intent.hasExtra("size")) {
            val len = intent.getIntExtra("size", 0)
            v = ArrayList(len)
            for (i in 0 until len) {
                v.add(intent.getSerializableExtra("value$i")!!)
            }
        }
        return SerializableInteractionResult(a, v)
    }
}

/**
 * @param action: 执行的操作。取值 activity_interaction_create/activity_interaction_update/activity_interaction_delete
 */
class EmptyInteractionResult(val action: String) :
    InteractionResult {

    override fun writeToIntent(intent: Intent) {
        intent.putExtra("action", action)
    }

    override fun readFromIntent(intent: Intent): EmptyInteractionResult {
        val a = intent.getStringExtra("action")!!
        return EmptyInteractionResult(a)
    }
}