package com.jd.oa.joywork.team.kpi

import com.jd.oa.joywork.JoyWorkNetConstant
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

class JoyWorkMockInterceptor : Interceptor {
    val apis = listOf(
        JoyWorkNetConstant.LIST_TARGET_CUR,
        "work.task.getKpiTasks.v3",
        JoyWorkNetConstant.LIST_TARGET_TASK
    )

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val action = request.header("x-client-action")
        if (!apis.contains(action)) {
            return chain.proceed(request)
        }
        try {
            val r = request.newBuilder().url("http://192.168.137.97:8484/").build()
            return chain.proceed(r)
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return chain.proceed(request)
    }
}