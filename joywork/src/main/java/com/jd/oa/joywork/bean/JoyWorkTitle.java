package com.jd.oa.joywork.bean;

import com.jd.oa.joywork.expandable.ExpandableGroup;
import com.jd.oa.joywork.expandable.Gradeable;
import com.jd.oa.joywork.expandable.Groupable;

/**
 * 每一个分组的标题
 */
public class JoyWorkTitle implements Gradeable, Groupable<JoyWorkTitle, JoyWork> {

    public int iconId;
    public String title;
    public int count;
    public int color;
    public boolean noGroupLoadMore;
    /**
     * 扩展字段。不同的界面标题对应的 bean 不一样，这些 bean 会放到 extra 中，不需要额外的自定义
     */
    public Object extra;

    public JoyWorkTitle(int iconId, String title, int count, int color) {
        this.iconId = iconId;
        this.title = title;
        this.count = count;
        this.color = color;
    }

    @Override
    public Grade getGrade() {
        return Gradeable.Grade.TITLE;
    }

    private ExpandableGroup<JoyWorkTitle, JoyWork> expandableGroup;

    @Override
    public ExpandableGroup<JoyWorkTitle, JoyWork> getExpandableGroup() {
        return expandableGroup;
    }

    @Override
    public void setExpandableGroup(ExpandableGroup<JoyWorkTitle, JoyWork> group) {
        this.expandableGroup = group;
    }
}
