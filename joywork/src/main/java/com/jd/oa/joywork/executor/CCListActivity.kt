package com.jd.oa.joywork.executor

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromMembers
import com.jd.oa.joywork.detail.key
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.string
import com.jd.oa.utils.visible
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.*

/**
 * 抄送人列表
 */
class CCListActivity : AppCompatActivity() {
    private lateinit var rv: RecyclerView
    private val scope = MainScope()

    companion object {

        val repo = HashMap<String, Any>()

        fun inflateIntent(
            intent: Intent,
            executors: List<Members>,
            sessionId: String?,
            resultCallback: (ms: ArrayList<Members>) -> Unit
        ) {
            OneTimeDataRepo.data = executors
            val key = "${System.currentTimeMillis()}"
            repo[key] = resultCallback
            intent.putExtra("repoId", key)
            if (sessionId.isLegalString()) {
                intent.putExtra("sessionId", sessionId)
            }
        }

        fun CCListActivity.getSessionId(): String? {
            return try {
                intent!!.getStringExtra("sessionId")
            } catch (e: Throwable) {
                null
            }
        }

        fun CCListActivity.getResultCallback(): ((ArrayList<Members>) -> Unit)? {
            return try {
                val key = intent!!.getStringExtra("repoId")
                repo[key!!] as? (ArrayList<Members>) -> Unit
            } catch (e: Throwable) {
                null
            }
        }

        fun CCListActivity.clearCallback() {
            try {
                val key = intent!!.getStringExtra("repoId")
                repo.remove(key)
            } catch (e: Throwable) {
            }
        }

        private fun getExecutors(): ArrayList<Members> {
            return (OneTimeDataRepo.data as? List<*>)?.run {
                return if (isEmpty()) {
                    ArrayList()
                } else {
                    if (first() is Members) {
                        ArrayList(this as List<Members>)
                    } else {
                        ArrayList()
                    }
                }
            } ?: ArrayList()
        }

    }

    private val members: ArrayList<Members> by lazy {
        getExecutors()
    }

    /**
     * 最终返回给调用者的用户集合
     */
    private val result: ArrayList<Members> = ArrayList()
    private val titleView: TextView by lazy {
        findViewById(R.id.title)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        result.addAll(members)
        hideAction()
        setContentView(R.layout.joywork_cc_activity)
        updateTitle()
        findViewById<View>(R.id.back).setOnClickListener { finish() }
        findViewById<View>(R.id.add).setOnClickListener {
            add()
        }
        rv = findViewById<RecyclerView>(R.id.rv).apply {
            layoutManager = LinearLayoutManager(
                this@CCListActivity,
                LinearLayoutManager.VERTICAL,
                false
            )

            adapter = if (members.isEmpty()) {
                JoyWorkEmptyAdapter.empty(
                    this@CCListActivity,
                    R.string.joywork_owner_empty,
                    R.drawable.joywork_executor_empty
                )
            } else {
                ExecutorsListAdapter(this, this@CCListActivity, members)
            }
            getInfo()
        }
    }

    private fun updateTitle() {
        titleView.text = string(R.string.joywork_cc_title) + "(" + result.size + ")"
    }

    private fun add() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.CREATE_OWNER_LIST,
            JoyWorkConstant.CREATE_OWNER_LIST
        )
        val selectedOuter = ArrayList<MemberEntityJd>()
        if (result.isNotEmpty()) {
            result.forEach { m ->
                m.emplAccount?.apply {
                    val jd = MemberEntityJd()
                    jd.fromMembers(m)
                    selectedOuter.add(jd)
                }
            }
        }
        ExecutorUtils.selectExecutors(
            this,
            selectedOuter,
            getSessionId(),
            false,
            { selected: ArrayList<MemberEntityJd?>? ->
                if (selected.isLegalList()) {
                    val membersNet: ArrayList<Members> = ArrayList()
                    for (memberEntityJd in selected!!) {
                        memberEntityJd?.apply {
                            val member = Members()
                            member.fromDD(memberEntityJd)
                            member.userRole = TaskUserRole.EXECUTOR.code
                            member.teamId = JoyWorkUser.DEFAULT_TEAM_ID
                            membersNet.add(member)
                        }
                    }
                    if (membersNet.isEmpty()) {
                        return@selectExecutors
                    }
                    addIfAbsent(result, membersNet)
                    (rv.adapter as? ExecutorsListAdapter)?.apply {
                        updateCollaborator(membersNet)
                        updateTitle()
                    } ?: showContent(result)
                    getInfo()
                }
            },
            {

            })
    }

    private fun getInfo() {
        // 过滤掉已有部门信息的人员
        val needNet = result.filter {
            listOf(it.deptInfo?.deptName, it.titleName).hasNull()
        }
        if (!needNet.isLegalList()) {
            return
        }
        scope.launch {
            val r = getBatchInfo(needNet)
            r.ifSuccessSilence {
                (rv.adapter as? ExecutorsListAdapter)?.apply {
                    updateInfo(it?.users ?: ArrayList<Members>())
                }
            }
        }
    }

    /**
     * 将 [src] 中去重复制到 [dst] 中。
     * 同时，会使用 [dst] 中的部分信息填充 [src]
     */
    private fun addIfAbsent(dst: ArrayList<Members>, src: List<Members>?) {
        val chiefKey = src?.firstOrNull {
            it.chief.isChief()
        }?.key()

        src?.forEach { outer ->
            val first = dst.firstOrNull { inner ->
                inner.emplAccount == outer.emplAccount
            }
            if (first == null) {
                dst.add(outer)
            } else {
                if (first.deptInfo.deptName.isLegalString() && !outer.deptInfo.deptName.isLegalString()) {
                    outer.deptInfo = first.deptInfo
                }
            }
        }
        dst.forEach {
            if (Objects.equals(it.key(), chiefKey)) {
                it.setToChief()
            } else {
                it.cancelChief()
            }
        }
    }

    private fun showContent(all: ArrayList<Members>) {
        updateTitle()
        rv.adapter = ExecutorsListAdapter(rv, this@CCListActivity, all)
    }

    // 移除协作人
    private fun removeNet(member: Members) {
        val key = member.key()
        result.removeAll {
            Objects.equals(it.key(), key)
        }
        (rv.adapter as? ExecutorsListAdapter)?.removeCollaborator(member)
        updateTitle()
    }

    override fun finish() {
        scope.cancel()
        OneTimeDataRepo.data = result
        setResult(Activity.RESULT_OK)
        getResultCallback()?.invoke(result)
        clearCallback()
        super.finish()
    }

    fun showEmpty() {
        rv.adapter = JoyWorkEmptyAdapter.empty(
            this@CCListActivity,
            R.string.joywork_owner_empty,
            R.drawable.joywork_executor_empty
        )
    }

    inner class ExecutorsListAdapter(
        val recyclerView: RecyclerView,
        val context: Context,
        dataC: ArrayList<Members>
    ) :
        RecyclerView.Adapter<VH>() {

        private val data = ArrayList<Members>(dataC)

        private val listener = View.OnClickListener {
            val c = it.tag as Members
            removeNet(c)
        }

        private val mSetListener = View.OnClickListener {
            exChief(it.tag as Members)
        }

        private fun exChief(member: Members) {
            val isChief = member.chief.isChief()
            data.forEach {
                it.cancelChief()
            }
            if (!isChief) {
                member.setToChief()
            }
            notifyDataSetChanged()
        }

        fun updateInfo(members: List<Members>) {
            members.forEach { ms ->
                data.firstOrNull {
                    it.emplAccount == ms.emplAccount
                }?.apply {
                    deptInfo = ms.deptInfo
                    titleName = ms.titleInfo?.titleName
                }
            }
            notifyDataSetChanged()
        }

        fun updateCollaborator(members: List<Members>) {
            val oldSize = data.size
            addIfAbsent(data, members)
            val newSize = data.size
            notifyDataSetChanged()
            if (oldSize != newSize) {
                recyclerView.smoothScrollToPosition(oldSize + 1)
            }
        }

        fun removeCollaborator(c: Members) {
            val index = data.indexOfFirst {
                it.isSamePerson(c)
            }
            if (index >= 0) {
                data.removeAt(index)
                if (data.isEmpty()) {
                    showEmpty()
                } else {
                    notifyItemRemoved(index)
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
            val inflate: View = LayoutInflater.from(context)
                .inflate(R.layout.set_owner_item, parent, false)
            return VH(inflate)
        }

        override fun getItemCount() = data.size

        override fun onBindViewHolder(holder: VH, position: Int) {
            val c = data[position]
            holder.name.text = c.realName ?: ""
            holder.department.text = listOf(c.deptInfo?.deptName, c.titleName).joinLegalString()
            holder.action.tag = c
            holder.action.setOnClickListener(listener)
            JoyWorkViewItem.avatar(holder.avatar, c.headImg)
            holder.avatar.tag = c
            holder.avatar.setOnClickListener {
                val m = it.tag as Members
                <EMAIL>(m.ddAppId, m.emplAccount)
            }

            if (c.chief.isChief()) {
                holder.mSet.setBackgroundResource(R.drawable.joywork_all_round_2_e6e6e6)
                holder.mSet.setText(R.string.joywork_set_owner_cancel)
                holder.hat.visible()
            } else {
                holder.mSet.setText(R.string.joywork_set_owner)
                holder.mSet.setBackgroundResource(R.drawable.joywork_stroke_round_2_cdcdcd)
                holder.hat.gone()
            }
            holder.mSet.gone()
        }

        fun clean() {
            data.clear()
        }
    }

    public class VH(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView = view.findViewById(R.id.image)
        val name: TextView = view.findViewById(R.id.name)
        val action: TextView = view.findViewById(R.id.action)
        val department: TextView = view.findViewById(R.id.department)
        val hat: ImageView = view.findViewById(R.id.hat)
        val mSet: TextView = view.findViewById(R.id.mSet)
    }
}