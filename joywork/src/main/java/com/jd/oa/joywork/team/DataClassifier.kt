package com.jd.oa.joywork.team

import android.content.Context
import android.graphics.Color
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.ProjectLoadMore
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.team.TeamBaseFragment.Companion.getProjectId
import com.jd.oa.joywork.team.TeamBaseFragment.Companion.isNormal
import com.jd.oa.joywork.team.bean.*
import com.jd.oa.utils.DateShowUtils
import com.jd.oa.utils.string

object DataClassifier {

    operator fun <K, V> Map<K, V>.component1(): K {
        return keys.first()
    }

    operator fun <K, V> Map<K, V>.component2(): V {
        return values.first()
    }

    // 随便取一个值
    val no_id = "DataClassifier#no_id@${this.hashCode()}"

    fun title(context: Context, title: String?): JoyWorkTitle {
        // 参数无用，随便写
        val t = JoyWorkTitle(R.string.icon_edit_delete, "", 0, Color.TRANSPARENT)
        t.extra =
            TeamGroupExtend()
        (t.extra as TeamGroupExtend).title = title
            ?: context.resources.getString(R.string.joywork_project_sort_no)
        return t
    }

    fun filterTitle(title: String?, context: Context): String {
        return title ?: context.resources.getString(R.string.joywork_project_sort_no)
    }

    fun flatProjectGroups(
        t: ProjectGroups,
        context: Context
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val groups = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val gs = ArrayList<Group>()
        gs.addAll(t.safeGroups)
        t.safeGroups.forEach {
            if (it.safeStatus != ProjectStatusEnum.DELETED.code) {
                if (it.type == ProjectGroupTypeEnum.INITIAL.code && !it.title.isLegalString()) {
                    it.title = context.string(R.string.joywork_self_default_group)
                }
                it.permissions = t.permissions
                //  参数在本界面中无用，随便写
                val title = JoyWorkTitle(
                    R.string.icon_padding_backlog,
                    it.title,
                    it.safeTotal,
                    Color.TRANSPARENT
                )
                title.extra = it
                val items = java.util.ArrayList<JoyWork>()
                items.addAll(it.safeTasks)
                if (it.safeTasks.size < it.safeTotal) {
                    items.add(ProjectLoadMore(it.projectId, it.groupId))
                }
                val expandableGroup =
                    ExpandableGroup(title, items, it.groupId, it.isInit)
                groups.add(expandableGroup)
            }
        }
        val tmpTitle =
            ProjectAddTitle(context.string(R.string.joywork_group_new_title))
        val expandableGroup = ExpandableGroup(
            tmpTitle,
            ArrayList<JoyWork>(),
            ProjectConstant.NEW_GROUP_TITLE_ID,
            true
        )
        groups.add(expandableGroup)
        return groups
    }

    fun clazz(
        group: CustomFieldGroupOuter,
        context: Context,
        sortValue: SortValue,
        result: List<JoyWork>
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val (idGenerator: ((JoyWork) -> String), titleGen: ((JoyWork) -> JoyWorkTitle)) = when (sortValue.code) {
            ProjectSortEnum.PRIORITY.code -> {
                mapOf({ w: JoyWork ->
                    if (w.priorityType != null) {
                        "${w.priorityType}"
                    } else {
                        no_id
                    }
                } to { w: JoyWork ->
                    val t =
                        if (w.priorityType == null) null else context.getString(
                            JoyWorkLevel.NO.getLevel(
                                w.priorityType
                            ).getResId()
                        )
                    title(context, t)
                })
            }

            ProjectSortEnum.SORT_END_TIME.code -> {
                mapOf({ w: JoyWork ->
                    if (w.endTime != null) {
                        DateShowUtils.getTimeShowText(w.endTime)
                    } else {
                        no_id
                    }
                } to { w: JoyWork ->
                    val t =
                        if (w.endTime == null) null else DateShowUtils.getTimeShowText(w.endTime)
                    title(context, t)
                })
            }

            ProjectSortEnum.PROJECT.code -> {
                mapOf({ w: JoyWork -> w.project?.projectId ?: no_id } to { w: JoyWork ->
                    val r = title(context, w.project?.title)
                    (r.extra as TeamGroupExtend).project = w.project
                    r
                })
            }

            ProjectSortEnum.SORT_BY_OWNER.code -> {
                mapOf({ w: JoyWork -> w.owner?.userId ?: no_id } to { w: JoyWork ->
                    val r = title(context, w.owner?.realName)
                    (r.extra as TeamGroupExtend).user = w.owner
                    r
                })
            }

            ProjectSortEnum.FINISH_TIME.code -> {
                mapOf({ w: JoyWork ->
                    if (w.completeTime != null) {
                        DateShowUtils.getTimeShowText(w.completeTime)
                    } else {
                        no_id
                    }
                } to { w: JoyWork ->
                    val t =
                        if (w.completeTime == null) null else DateShowUtils.getTimeShowText(w.completeTime)
                    title(context, t)
                })
            }

            ProjectSortEnum.SORT_CUSTOM.code -> {
                mapOf({ w: JoyWork ->
                    // 自定义字段排序，使用 cid 与所选值做为分组 id
                    val cid = sortValue.value.columnId
                    val did = w.customFields?.get(cid)?.firstOrNull() ?: no_id
                    "$cid-$did"
                } to { w: JoyWork ->
                    val r = title(context, null)
                    if (w.customFields?.get(sortValue.value.columnId).isLegalList()) {
                        val detailId = w.customFields[sortValue.value.columnId]?.firstOrNull()
                        if (detailId != null) {
                            (r.extra as TeamGroupExtend).title = group.getDetailItem(
                                sortValue.value.columnId,
                                detailId
                            )?.itemContent?.content
                                ?: context.resources.getString(R.string.joywork_project_sort_no)
                        }
                    }
                    r
                })
            }

            else -> {
                mapOf({ w: JoyWork -> no_id } to { w: JoyWork -> title(context, null) })
            }
        }

        val ret = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val idMap = HashMap<String, ExpandableGroup<JoyWorkTitle, JoyWork>>()
        result.forEach {
            it.priorityType = JoyWorkLevel.P4.fixIfNecessary(it.priorityType)
            val id = idGenerator(it)
            if (!idMap.containsKey(id)) {
                idMap[id] = ExpandableGroup(titleGen(it), ArrayList<JoyWork>(), id, true)
                ret.add(idMap[id]!!)
            }
            idMap[id]!!.realItems.add(it)
        }
        return ret
    }

    fun clazz2(
        context: Context,
        sortValue: ProjectSortEnum,
        result: List<JoyWork>
    ): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val (idGenerator: ((JoyWork) -> String), titleGen: ((JoyWork) -> JoyWorkTitle)) = when (sortValue) {
            ProjectSortEnum.PRIORITY -> {
                mapOf({ w: JoyWork ->
                    if (w.priorityType != null) {
                        "${w.priorityType}"
                    } else {
                        no_id
                    }
                } to { w: JoyWork ->
                    val t =
                        if (w.priorityType == null) null else context.getString(
                            JoyWorkLevel.NO.getLevel(
                                w.priorityType
                            ).getResId()
                        )
                    title(context, t)
                })
            }

            ProjectSortEnum.PROJECT -> {
                mapOf({ w: JoyWork -> w.project?.projectId ?: no_id } to { w: JoyWork ->
                    val r = title(context, w.project?.title)
                    r.extra = w.project
                    r
                })
            }

            ProjectSortEnum.SORT_BY_OWNER -> {
                mapOf({ w: JoyWork -> w.owner?.userId ?: no_id } to { w: JoyWork ->
                    val r = title(context, w.owner?.realName)
                    r.extra = w.owner
                    r
                })
            }

            ProjectSortEnum.FINISH_TIME -> {
                mapOf({ w: JoyWork ->
                    if (w.completeTime != null) {
                        DateShowUtils.getTimeShowText(w.completeTime)
                    } else {
                        no_id
                    }
                } to { w: JoyWork ->
                    val t =
                        if (w.completeTime == null) null else DateShowUtils.getTimeShowText(w.completeTime)
                    title(context, t)
                })
            }

            else -> {
                mapOf({ w: JoyWork -> no_id } to { w: JoyWork -> title(context, null) })
            }
        }

        val ret = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        val idMap = HashMap<String, ExpandableGroup<JoyWorkTitle, JoyWork>>()
        result.forEach {
            it.priorityType = JoyWorkLevel.P4.fixIfNecessary(it.priorityType)
            val id = idGenerator(it)
            if (!idMap.containsKey(id)) {
                idMap[id] = ExpandableGroup(titleGen(it), ArrayList<JoyWork>(), id, true)
                ret.add(idMap[id]!!)
            }
            idMap[id]!!.realItems.add(it)
        }
        return ret
    }
}
