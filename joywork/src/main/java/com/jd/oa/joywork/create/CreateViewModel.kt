package com.jd.oa.joywork.create

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.ProjectDetail
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail.Project
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.AbsRepoCallback
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.chat.ProjectSelectorDeeplinkUtils
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

class CreateViewModel : ViewModel() {

    //  描述
    private val _desChangeLivedata = MutableLiveData<Boolean>()

    val desChangeLivedata: LiveData<Boolean> = _desChangeLivedata

    fun updateDes() {
        _desChangeLivedata.value = true
    }

    // 文档

    private val _shimoChangeLivedata = MutableLiveData<Boolean>()

    val shimoChangeLivedata: LiveData<Boolean> = _shimoChangeLivedata

    fun updateShimo() {
        _shimoChangeLivedata.value = true
    }

    // 附件
    private val _attachmentChangeLivedata = MutableLiveData<Boolean>()

    val attachmentChangeLivedata: LiveData<Boolean> = _attachmentChangeLivedata

    fun updateAttachment() {
        _attachmentChangeLivedata.value = true
    }

    // 多负责人，子待办
    private val _ownersChangeLivedata = MutableLiveData<Boolean>()

    val ownersChangeLivedata: LiveData<Boolean> = _ownersChangeLivedata

    fun updateOwners() {
        _ownersChangeLivedata.value = true
    }

    // 多负责人，普通待办
    private val _normalOwnersChangeLivedata = MutableLiveData<Boolean>()

    val normalOwnersChangeLivedata: LiveData<Boolean> = _normalOwnersChangeLivedata

    fun updateNormalOwners() {
        _normalOwnersChangeLivedata.value = true
    }

    // 关注人
    private val _relationChangeLivedata = MutableLiveData<Boolean>()

    val relationChangeLivedata: LiveData<Boolean> = _relationChangeLivedata

    fun updateRelation() {
        _relationChangeLivedata.value = true
    }

    // 更新截止时间与开始时间

    private val _timeChangeLivedata = MutableLiveData<Boolean>()

    val timeChangeLivedata: LiveData<Boolean> = _timeChangeLivedata

    fun updateTime() {
        _timeChangeLivedata.value = true
    }
    // 重复

    private val _dupChangeLivedata = MutableLiveData<Boolean>()

    val dupChangeLivedata: LiveData<Boolean> = _dupChangeLivedata
    fun updateDup() {
        _dupChangeLivedata.value = true
    }

    // 优先级
    private val _priorityChangeLivedata = MutableLiveData<Boolean>()

    val priorityChangeLivedata: LiveData<Boolean> = _priorityChangeLivedata
    fun updatePriority() {
        _priorityChangeLivedata.value = true
    }

    // 提醒时间
    private val _alarmChangeLivedata = MutableLiveData<Boolean>()

    val alarmChangeLivedata: LiveData<Boolean> = _alarmChangeLivedata
    fun updateAlertTime() {
        _alarmChangeLivedata.value = true
    }

    // 提醒设置
    private val _alertChangeLiveData = MutableLiveData<Boolean>()

    val alertChangeLiveData: LiveData<Boolean> = _alertChangeLiveData
    fun updateAlertTypes() {
        _alertChangeLiveData.value = true
    }

    // 更新标题
    private val _titleChangeLivedata = MutableLiveData<Boolean>()

    val titleChangeLivedata: LiveData<Boolean> = _titleChangeLivedata
    fun updateTitle() {
        _titleChangeLivedata.value = true
    }

    // 团队待办时更新分组
    private val _groupChangeLivedata = MutableLiveData<Boolean>()

    val groupChangeLivedata: LiveData<Boolean> = _groupChangeLivedata
    fun updateGroup() {
        _groupChangeLivedata.value = true
    }

    // 详情中风险问题
    private val _riskChangeLivedata = MutableLiveData<Boolean>()

    val riskChangeLivedata: LiveData<Boolean> = _riskChangeLivedata
    fun updateRisk() {
        _riskChangeLivedata.value = true
    }

    // 启动时间
    private val _launchTimeChangeLivedata = MutableLiveData<Boolean>()

    val launchTimeChangeLivedata: LiveData<Boolean> = _launchTimeChangeLivedata
    fun updateLaunchTime() {
        _launchTimeChangeLivedata.value = true
    }

    // 关联清单
    private val _projectChangeLivedata = MutableLiveData<Boolean>()

    val projectChangeLivedata: LiveData<Boolean> = _projectChangeLivedata
    fun updateProject() {
        _projectChangeLivedata.value = true
    }

    // 关联预置清单
    private val _sysProjectChangeLivedata = MutableLiveData<Boolean>()

    val sysProjectChangeLivedata: LiveData<Boolean> = _sysProjectChangeLivedata
    fun updateSysProject() {
        _sysProjectChangeLivedata.value = true
    }

    // 关联目标
    private val _targetChangeLivedata = MutableLiveData<Boolean>()

    val targetChangeLivedata: LiveData<Boolean> = _targetChangeLivedata
    fun updateTarget() {
        _targetChangeLivedata.value = true
    }

    // 关联 kr
    private val _krChangeLivedata = MutableLiveData<Boolean>()

    val krChangeLivedata: LiveData<Boolean> = _krChangeLivedata
    fun updateKr() {
        _krChangeLivedata.value = true
    }

    // 灰度信息
    private val _grayInfoChangeLivedata = MutableLiveData<Boolean>()

    val grayInfoChangeLivedata: LiveData<Boolean> = _grayInfoChangeLivedata
    fun updateGrayInfo() {
        _grayInfoChangeLivedata.value = true
    }

    // java 用 flow 太他么麻烦了，直接提供个方法
    fun initObserver(scope: CoroutineScope, callback: (project: Project) -> Unit) {
        scope.launch {
            defaultProjectChangeFlow.collect {
                callback(it)
            }
        }
    }

    // 默认项目
    private val defaultProjectChangeFlow =
        MutableSharedFlow<Project>(100, 100, BufferOverflow.SUSPEND)

    fun getDefaultProject(labelOuter: MEChattingLabel, context: Context) {
        if (!labelOuter.hasJoyWorkLabel()) {
            return
        }
        labelOuter.labels.forEach { label ->
            viewModelScope.safeLaunch {
                coroutineScope {
                    val projectId =
                        ProjectSelectorDeeplinkUtils.getProjectId(label.linkUrl.mobile)
                    if (projectId.isLegalString()) {
                        try {
                            val gs =
                                suspendCancellableCoroutine<List<JoyWorkProjectList.ListDTO.GroupsDTO>> { continuation ->
                                    ProjectRepo.getProjectDetail(
                                        projectId,
                                        object : AbsRepoCallback<ProjectDetail>() {
                                            override fun result(
                                                t: ProjectDetail?,
                                                errorMsg: String?,
                                                success: Boolean,
                                                errorCode: Int?
                                            ) {
                                                if (success && t != null) {
                                                    continuation.resumeWith(Result.success(t.groups))
                                                }
                                            }
                                        })
                                }
                            if (gs.isLegalList() && gs.isNotEmpty()) {
                                val group = gs.first()
                                // String groupId, String groupTitle, String title, String projectId, String icon
                                val p = Project(
                                    group.groupId,
                                    group.safeTitle(context),
                                    ProjectSelectorDeeplinkUtils.getProjectTitle(label.linkUrl.mobile),
                                    group.projectId,
                                    ""
                                )
                                defaultProjectChangeFlow.emit(p)
                            }
                        } catch (e: Throwable) {
                            // empty
                        }
                    }
                }
            }
        }
    }
}