package com.jd.oa.joywork.self.base

import androidx.lifecycle.*

class LiveDataObserverWrapper<T>(
    val owner: LifecycleOwner,
    val liveData: LiveData<T>,
    val observer: Observer<T>,
)

open class JoyWorkStoppableViewModel : ViewModel() {

    private val _mObservers = HashSet<LiveDataObserverWrapper<*>>()

    protected fun <T> observeLiveData(
        owner: LifecycleOwner,
        liveData: LiveData<T>,
        observer: Observer<T>
    ) {
        val o = LiveDataObserverWrapper(owner, liveData, observer)
        _mObservers.add(o)
        liveData.observe(owner, observer)
    }

    fun onFragmentVisibleChange(isVisible: Boolean) {
        _mObservers.forEach {
            kotlin.runCatching {
                if (isVisible) {
                    it.liveData.observe(it.owner, it.observer as Observer<Any?>)
                } else {
                    it.liveData.removeObserver(it.observer as Observer<Any?>)
                }
            }
        }
    }
}

class JoyWorkSelfListViewModel : JoyWorkStoppableViewModel() {
    private val _refreshLiveData = MutableLiveData<Boolean>()
    fun observeRefresh(owner: LifecycleOwner, observer: Observer<Boolean>) {
        observeLiveData(owner, _refreshLiveData, observer)
    }

    fun updateRefresh() {
        _refreshLiveData.value = true
    }
}