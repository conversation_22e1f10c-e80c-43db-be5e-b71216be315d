package com.jd.oa.joywork.detail.data.entity;

import com.jd.oa.model.service.im.dd.entity.Members;

import java.util.List;

/**
 * Auto-generated: 2021-07-12 20:43:39
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class UpdateMemberSend {

    private String taskId;
    private List<Members> members;
    private String follow;

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setMembers(List<Members> members) {
        this.members = members;
    }

    public List<Members> getMembers() {
        return members;
    }

    public void setFollow(String follow) {
        this.follow = follow;
    }

    public String getFollow() {
        return follow;
    }

}