package com.jd.oa.joywork

import com.jd.oa.model.service.im.dd.entity.BatchUserInfo
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.ToastUtils
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.collections.ArrayList

class CoroutineResult<T> {
    var success: Boolean = false
    var errorMsg: String? = null
    var errorCode: Int? = null
    var data: T? = null

    fun success(t: T): CoroutineResult<T> {
        this.success = true
        this.data = t
        this.errorCode = null
        this.errorMsg = null
        return this
    }

    fun failure(errorMsg: String, errorCode: Int? = null, t: T? = null): CoroutineResult<T> {
        this.success = false
        this.data = t
        this.errorMsg = errorMsg
        this.errorCode = errorCode
        return this
    }

    /**
     * 失败时，静默处理
     */
    fun ifSuccessSilence(onSuccess: (t: T?) -> Unit) {
        isSuccess(::silenceFailure, onSuccess)
    }

    /**
     * 失败时，toast 提示
     */
    fun ifSuccessToast(onSuccess: (t: T?) -> Unit) {
        isSuccess(::toastFailure, onSuccess)
    }

    fun isSuccess(
        onFailure: (t: T?, errorMsg: String, errorCode: Int?) -> Unit,
        onSuccess: (t: T?) -> Unit
    ) {
        if (success) {
            onSuccess(data)
        } else {
            onFailure(data, JoyWorkEx.filterErrorMsg(errorMsg), errorCode)
        }
    }

    fun silenceFailure(t: T?, errorMsg: String, errorCode: Int?) {

    }

    fun toastFailure(t: T?, errorMsg: String, errorCode: Int?) {
        ToastUtils.showToast(errorMsg)
    }
}

suspend fun getSingleInfo(emplAccount: String, ddAppId: String): CoroutineResult<JoyWorkUser> {
    return suspendCancellableCoroutine { c ->
        JoyWorkRepo.singleInfo(emplAccount, ddAppId) { s, w, msg ->
            if (s && w != null) {
                c.resumeWith(Result.success(CoroutineResult<JoyWorkUser>().success(w)))
            } else {
                c.resumeWith(
                    Result.success(
                        CoroutineResult<JoyWorkUser>().failure(
                            JoyWorkEx.filterErrorMsg(
                                msg
                            )
                        )
                    )
                )
            }
        }
    }
}

// 获取咚咚群组成员
suspend fun getGroupsRoster(sessionId: String): CoroutineResult<ArrayList<MemberEntityJd>?> {
    return suspendCancellableCoroutine { c ->
        val service = AppJoint.service(ImDdService::class.java)
        service.getGroupRoster(
            sessionId,
            false,
            object : LoadDataCallback<ArrayList<MemberEntityJd>> {
                override fun onDataLoaded(jds: ArrayList<MemberEntityJd>?) {
                    c.resumeWith(
                        Result.success(
                            CoroutineResult<ArrayList<MemberEntityJd>?>().success(jds)
                        )
                    )
                }

                override fun onDataNotAvailable(p0: String?, p1: Int) {
                    c.resumeWith(
                        Result.success(
                            CoroutineResult<ArrayList<MemberEntityJd>?>().failure("")
                        )
                    )
                }
            })
    }
}

/**
 * 批量获取用户信息
 */
suspend fun getBatchInfo(members: List<Members>): CoroutineResult<BatchUserInfo> {
    return suspendCancellableCoroutine { c ->
        JoyWorkRepo.batchInfo(members) { s, w, msg ->
            if (s && w != null) {
                c.resumeWith(Result.success(CoroutineResult<BatchUserInfo>().success(w)))
            } else {
                c.resumeWith(
                    Result.success(
                        CoroutineResult<BatchUserInfo>().failure(
                            JoyWorkEx.filterErrorMsg(
                                msg
                            )
                        )
                    )
                )
            }
        }
    }
}

suspend fun swapSubJoyWork(
    taskId: String,
    before: String?,
    after: String?
): CoroutineResult<String> {
    return suspendCancellableCoroutine { c ->
        JoyWorkRepo.sortSubJoyWork(taskId, after, before) { s, msg ->
            if (s) {
                c.resumeWith(Result.success(CoroutineResult<String>().success("")))
            } else {
                c.resumeWith(
                    Result.success(
                        CoroutineResult<String>().failure(
                            JoyWorkEx.filterErrorMsg(
                                msg
                            )
                        )
                    )
                )
            }
        }
    }
}


/**
 * 批量获取用户信息
 */
suspend fun finishOrUnfinish(
    taskId: String,
    taskStatus: Int,
    member: Members
): CoroutineResult<String> {
    return suspendCancellableCoroutine { c ->
        JoyWorkRepo.finishOrUnfinishTask(taskId, taskStatus, member) { msg, success ->
            if (success) {
                c.resumeWith(Result.success(CoroutineResult<String>().success(msg)))
            } else {
                c.resumeWith(
                    Result.success(
                        CoroutineResult<String>().failure(msg)
                    )
                )
            }
        }
    }
}