package com.jd.oa.joywork.team

import com.jd.oa.joywork.TaskStatusEnum

class TeamDeletedFragment : TeamBaseFragment() {
    override fun getDefaultStatus(): TaskStatusEnum {
        return TaskStatusEnum.DELETED
    }

    override fun getPageStatus(): TaskStatusEnum {
        return TaskStatusEnum.DELETED
    }

    override fun showAddButton(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun canDrag(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun settingCanFilter(): <PERSON><PERSON><PERSON> {
        return false
    }
}