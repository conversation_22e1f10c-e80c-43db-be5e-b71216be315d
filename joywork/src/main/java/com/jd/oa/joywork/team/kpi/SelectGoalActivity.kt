package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Parcelable
import android.text.SpannableString
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.R.string
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiQ
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.team.dialog.SelectQDialog
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.joywork_link_target.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.*

/**
 * 选择绩效目标
 */
class SelectGoalActivity : BaseActivity(), DialogInterface.OnDismissListener {

    companion object {
        private const val result_key = "target"
        fun start(activity: Activity, requestCode: Int) {
            activity.startActivityForResult(
                Intent(activity, SelectGoalActivity::class.java),
                requestCode
            )
        }

        fun getResultData(intent: Intent?): Parcelable? {
            return intent?.getParcelableExtra(result_key)
        }
    }

    private val qs = mutableListOf<KpiQ>()
    private val scope = MainScope()
    private var mCurQ = KpiQ.PLACEHOLDER
    private var mSelectQDialog: SelectQDialog? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.joywork_link_target)
        hideAction()
        mBack.setOnClickListener { finish() }
        mSelectQ.setOnClickListener {
            if (qs.isLegalList()) {
                mSelectQDialog = SelectQDialog(this, qs, qs.indexOf(mCurQ))
                mSelectQDialog?.click = {
                    switchQ(it)
                }
                mSelectQDialog?.show()
                mSelectQDialog?.setOnDismissListener(this@SelectGoalActivity)
            }
        }
        mRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        mRecyclerView.adapter = TargetLinkAdapter(this, mutableListOf()) {
            val intent = Intent()
            if (it is KpiTarget) {
                intent.putExtra(result_key, it)
            } else if (it is KR) {
                intent.putExtra(result_key, it as Parcelable)
            }
            setResult(RESULT_OK, intent)
            finish()
        }
        getData()
    }

    private fun getData() {
        scope.launch {
            val result = KpiRepo.listQAndKr()
            result.isSuccess({ _, msg, _ ->
                showError(msg)
            }) {
                if (it == null || !it.periodList.isLegalList()) { // no q
                    showError(JoyWorkEx.filterErrorMsg(""))
                } else {
                    mSelectQ.visible()
                    qs.clear()
                    qs.addAll(it.periodList!!)
                    initQ(qs.first())
                    if (it.safeKRList.isLegalList()) {
                        showContent(it.safeKRList)
                    } else {
                        showEmpty()
                    }
                }
            }
        }
    }

    private fun initQ(q: KpiQ) {
        if (q == mCurQ) {
            return
        }
        mCurQ = q
        mQTitle.text = mCurQ.assessPeriodDesc
    }

    private fun switchQ(q: KpiQ) {
        if (q == mCurQ)
            return
        initQ(q)
        scope.launch {
            val result = KpiRepo.listGoalKrByQ(q.assessPeriod)
            result.isSuccess({ _, msg, _ ->
                showError(JoyWorkEx.filterErrorMsg(msg))
            }) {
                val list = it?.safeKRList
                if (list.isLegalList()) {
                    showContent(list!!)
                } else {
                    showEmpty()
                }
            }
        }
    }

    private fun showError(msg: String) {
        mSelectQ.gone()
        mRecyclerView.adapter = JoyWorkEmptyAdapter.error(this, msg)
    }

    private fun showEmpty() {
        mRecyclerView.adapter = JoyWorkEmptyAdapter.empty(
            this,
            R.string.joywork_kpi_target_empty,
            R.drawable.joywork_target_empty
        )
    }

    private fun showContent(items: MutableList<KpiTarget>) {
        val adapter = mRecyclerView.adapter
        if (adapter is TargetLinkAdapter) {
            adapter.replace(items)
        } else {
            mRecyclerView.adapter = TargetLinkAdapter(this, items) {
                val intent = Intent()
                if (it is KpiTarget) {
                    intent.putExtra(result_key, it)
                } else if (it is KR) {
                    intent.putExtra(result_key, it as Parcelable)
                }
                setResult(RESULT_OK, intent)
                finish()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }

    override fun onDismiss(dialog: DialogInterface?) {
        mSelectQDialog = null
    }
}

class TargetLinkAdapter(
    private val context: Context,
    items: MutableList<KpiTarget>,
    itemClick: (Any) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val margin = CommonUtils.dp2px(16.0f)
    private val data = mutableListOf<Any>()

    init {
        data.addAll(map(items))
    }

    private val itemClick = View.OnClickListener {
        val target = it.tag
        itemClick.invoke(target)
    }

    private fun map(items: List<KpiTarget>): List<Any> {
        val a = mutableListOf<Any>()
        items.forEach {
            a.add(it)
            if (it.krList.isLegalList()) {
                a.addAll(it.krList)
            }
        }
        return a
    }

    fun replace(newItems: List<KpiTarget>) {
        data.clear()
        data.addAll(map(newItems))
        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int): Int {
        val d = data[position]
        return when (d) {
            is KpiTarget -> {
                1
            }
            is KR -> {
                2
            }
            else -> {
                3
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType != 3) {
            VH(
                context.inflater.inflate(
                    R.layout.joywork_link_target_item,
                    parent,
                    false
                )
            )
        } else {
            PlaceHolderVH(context)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder !is VH) {
            return
        }
        val target = data[position]
        if (target !is KR && target !is KpiTarget) {
            return
        }
        holder.itemView.tag = target
        holder.itemView.setOnClickListener(itemClick)
        val id = if (target is KpiTarget) {
            target.target(holder.mTitle)
            holder.mTitle.argb("#232930")
            target.goalId
        } else {
            val kr = target as KR
            kr.kr(holder.mTitle)
            holder.mTitle.argb("#666666")
            kr.goalId
        }
        val next = data.safeGet(position + 1)
        val nextId = (next as? KR)?.goalId ?: (next as? KpiTarget)?.goalId
        if (Objects.equals(id, nextId)) {
            holder.mDivider.marginStart(margin)
        } else {
            holder.mDivider.marginStart(0)
        }
    }

    override fun getItemCount() = data.size

    private fun KpiTarget.target(tv: TextView) {
        if (!getSectionName(tv.context).isLegalString()) {
            tv.text = titleWeightStr(context)
            return
        }
        val pair = titleWeightExtraStr(tv.context)
        val ss = SpannableString(pair.first)

        val view = tv.context.inflater.inflate(R.layout.joywork_kpi_item_title_tag2, null)
        val tag = view.findViewById<TextView>(R.id.mTag)
        tag.text = getSectionName(tv.context)
        tag.argb("#FFFFFF")
        tag.setBackgroundResource(R.drawable.joywork_all_round_100_4c7cff)
        ss.setSpan(ViewSpan(view, tv, -1), 0, pair.second, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
        tv.text = ss
        tv.requestLayout()
    }

    private fun KR.kr(tv: TextView) {
        val tagText = context.resources.getString(R.string.joywork_kr_tag)
        val ss = SpannableString(tagText + content)

        val view = tv.context.inflater.inflate(R.layout.joywork_kpi_item_title_tag2, null)
        val tag = view.findViewById<TextView>(R.id.mTag)
        tag.text = tagText
        tag.argb("#4C7CFF")
        tag.setBackgroundResource(R.drawable.joywork_all_round_100_1a4c7cff)
        ss.setSpan(
            ViewSpan(view, tv, -CommonUtils.dp2px(1f)),
            0,
            tagText.length,
            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tv.text = ss
        tv.requestLayout()
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mTitle: TextView = itemView.findViewById(R.id.mTitle)
        val mDivider: View = itemView.findViewById(R.id.mDivider)
    }
}