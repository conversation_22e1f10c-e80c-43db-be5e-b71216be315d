package com.jd.oa.joywork.team.kpi

import android.graphics.Canvas
import android.graphics.Paint
import android.text.style.ReplacementSpan
import android.view.View
import android.widget.TextView
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.TextHelper

class ViewSpan(private val view: View, private val tv: TextView, private val offset: Int = 0) :
    ReplacementSpan() {
    override fun getSize(
        paint: Paint,
        text: CharSequence?,
        start: Int,
        end: Int,
        fm: Paint.FontMetricsInt?
    ): Int {
        view.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        view.layout(0, 0, view.measuredWidth, view.measuredHeight)
//        if (fm != null) {
//            val height = view.measuredHeight;
//            fm.ascent = -height / 2
//            fm.top = -height / 2
//            fm.descent = height / 2
//            fm.bottom = height / 2
//        }
        return view.measuredWidth
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence?,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        canvas.save()
        val height =
            TextHelper.getSingleLineTextHeight(tv.textSize, tv.context.resources.displayMetrics)
        val transY = Math.max(((height - view.height) / 2), 0.0f) - offset
        canvas.translate(x, transY)
        view.draw(canvas)
        canvas.restore()
    }

}