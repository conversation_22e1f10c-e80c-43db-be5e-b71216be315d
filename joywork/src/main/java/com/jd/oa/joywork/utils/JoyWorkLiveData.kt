package com.jd.oa.joywork.utils

object JoyWorkLiveDataRepo {
    private val joyWorkLiveData = mutableMapOf<String, JoyWorkLiveData>()

    fun remove(data: JoyWorkLiveData) {
        joyWorkLiveData.remove(data.token)
    }

    fun get(token: String): JoyWorkLiveData? {
        return joyWorkLiveData[token]
    }

    fun <T : JoyWorkLiveData> getOrCreate(
        token: String,
        user: Any,
        factory: ((String) -> T)? = null
    ): T? {
        val liveData = joyWorkLiveData[token] ?: factory?.invoke(token) ?: return null
        liveData.addUser(user)
        joyWorkLiveData[token] = liveData
        return liveData as T
    }
}

/**
 * We enhanced the [LiveData]'s ability, now they can share between activities
 */
open class JoyWorkLiveData(val token: String) {
    val users = mutableListOf<Any>()
    public fun addUser(user: Any) {
        users.add(user)
    }

    fun removeUser(user: Any) {
        users.remove(user)
        if (users.isEmpty()) {
            JoyWorkLiveDataRepo.remove(this)
        }
    }
}