package com.jd.oa.joywork;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.joywork.utils.JoyWorkNetConfig;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.LocaleUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class JoyWorkTaskSelector {
    public interface TaskSelectCallback {
        void onTaskSelected(ArrayList tasks);
    }

    private static final List<BroadcastReceiver> sReceivers = new ArrayList<>();

    /**
     * JS待办选择器
     *
     * @param single   是否是单选
     * @param editOnly 是否值选择可编辑的待办
     * @param callback callback
     */
    public static void selectTask(boolean single, boolean editOnly, List<String> taskIds, final TaskSelectCallback callback) {
        final LocalBroadcastManager mLocalBroadcastManager = LocalBroadcastManager.getInstance(AppBase.getAppContext());
        final IntentFilter filter = new IntentFilter();
        filter.addAction(Constant.JSSKD_EVENT_SELECT_DATA);
        final BroadcastReceiver mReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                unregister();
                if (Constant.JSSKD_EVENT_SELECT_DATA.equals(intent.getAction())) {
                    HashMap data = (HashMap) intent.getSerializableExtra("data");
                    if (data != null) {
                        if (data.containsKey("from")) {
                            String from = (String) data.get("from");
                            if ("joywork".equals(from)) {
                                callback.onTaskSelected((ArrayList) data.get("tasks"));
                            }
                        }
                    }
                }
            }
        };
        unregister();
        // 注册
        mLocalBroadcastManager.registerReceiver(mReceiver, filter);
        sReceivers.add(mReceiver);
        String scheme = JoyWorkNetConfig.INSTANCE.getJoyWorkHost();
        PreferenceManager.UserInfo.getNetEnvironment();
        StringBuilder sb = new StringBuilder();
        for (String id : taskIds) {
            sb.append(id);
            sb.append(",");
        }
        String s = sb.toString();
        if (s.endsWith(",")) {
            s = s.substring(0, s.length() - 1);
        }
        String url = scheme + "/task-select?single=" + single + "&editOnly=" + editOnly + "&lang=" + LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()) + "&disableList=" + s;
        Router.build(DeepLink.webUrl(url, 0)).go(AppBase.getAppContext());
    }

    public static void unregister() {
        final LocalBroadcastManager mLocalBroadcastManager = LocalBroadcastManager.getInstance(AppBase.getAppContext());
        for (BroadcastReceiver receiver : sReceivers) {
            try {
                mLocalBroadcastManager.unregisterReceiver(receiver);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        sReceivers.clear();
    }
}
