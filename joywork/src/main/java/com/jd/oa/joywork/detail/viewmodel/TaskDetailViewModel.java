package com.jd.oa.joywork.detail.viewmodel;

import android.app.Activity;
import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.BlockTypeEnum;
import com.jd.oa.joywork.JoyWorkEx;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.RiskEnum;
import com.jd.oa.joywork.bean.KR;
import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.joywork.detail.JoyWorkContentExKt;
import com.jd.oa.joywork.detail.data.TaskDetailRepository;
import com.jd.oa.joywork.detail.data.TaskDetailWebservice;
import com.jd.oa.joywork.detail.data.db.UpdateReporter;
import com.jd.oa.joywork.detail.data.entity.ChildWorks;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.model.service.im.dd.entity.Members;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity;
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup;
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem;
import com.jd.oa.joywork.detail.data.entity.mark.JoyWorkContent2;
import com.jd.oa.joywork.repo.JoyWorkAssignCallback;
import com.jd.oa.joywork.repo.JoyWorkRepo;
import com.jd.oa.joywork.team.ProjectRepo;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2Network;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import kotlin.Unit;
import kotlin.jvm.functions.Function3;

public class TaskDetailViewModel extends AndroidViewModel {
    final TaskDetailRepository mRepository;
    private final MutableLiveData<TaskDetailEntity> mTaskDetail;
    private final String mTaskId;
    private final String actionSource;

    public TaskDetailViewModel(Application application, TaskDetailRepository repository, final String taskId, String actionSource) {
        super(application);
        mRepository = repository;
        mTaskDetail = repository.getTaskDetail(taskId);
        mTaskId = taskId;
        this.actionSource = actionSource;
    }

    public static class Factory extends ViewModelProvider.NewInstanceFactory {

        private final String mTaskId;
        private final String actionSource;
        private final Application mApplication;

        private final TaskDetailRepository mRepository;

        public Factory(Application application, String taskId, String actionSource) {
            mApplication = application;
            mTaskId = taskId;
            this.actionSource = actionSource;
            mRepository = TaskDetailRepository.getInstance();
        }

        @SuppressWarnings("unchecked")
        @Override
        @NonNull
        public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
            return (T) new TaskDetailViewModel(mApplication, mRepository, mTaskId, actionSource);
        }
    }

    public void updateTaskDetailFromWeb() {
        mRepository.updateFromWeb(mTaskId);
    }

    public LiveData<TaskDetailEntity> getTaskDetail() {
        return mTaskDetail;
    }

    public void updateTaskDetail(TaskDetailEntity taskDetailEntity) {
        mRepository.updateDetailEntity(mTaskId, taskDetailEntity);
    }

    public void updateRisk(final RiskEnum riskEnum, final String riskContent, boolean needNet, final Runnable runnable) {
        if (!needNet) {
            TaskDetailEntity taskDetailEntity = mTaskDetail.getValue();
            if (taskDetailEntity != null) {
                taskDetailEntity.getJoyWorkDetail().riskStatus = riskEnum.getCode();
                taskDetailEntity.getJoyWorkDetail().riskContent = riskContent;
                updateTaskDetail(taskDetailEntity);
                runnable.run();
            }
            return;
        }
        mRepository.updateRisk(mTaskId, riskEnum, riskContent, actionSource, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (!hasError) {
                    updateRisk(riskEnum, riskContent, false, runnable);
                }
            }
        });
    }

    public void updateRemark(String remark, boolean needNet) {
        TaskDetailEntity taskDetailEntity = mTaskDetail.getValue();
        if (taskDetailEntity != null) {
            taskDetailEntity.getJoyWorkDetail().setRemark(remark);
        }
        updateTaskDetail(taskDetailEntity);
        if (needNet)
            mRepository.updateRemark(mTaskId, remark, actionSource);
    }

    public void updateTitle(Activity activity, String title, final Runnable success) {
        TaskDetailEntity taskDetailEntity = mTaskDetail.getValue();
        if (taskDetailEntity != null) {
            taskDetailEntity.getJoyWorkDetail().setTitle(title);
        }
        mRepository.updateTitle(mTaskId, title, actionSource, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                DialogUtils.removeLoadDialog(activity);
                try {
                    success.run();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
            }
        });
    }

    public void updateTargets(KpiTarget target, final Runnable success) {
        mRepository.updateTarget(mTaskId, target.goalId, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    success.run();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
            }
        });
    }

    public void updateKr(KR kr, final Runnable success) {
        mRepository.updateKr(mTaskId, kr.krId, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    success.run();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
            }
        });
    }

    public void taskDeleteTarget(final KpiTarget target, final Runnable success) {
        mRepository.delTarget(mTaskId, target.goalId, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    success.run();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
            }
        });
    }

    public void taskDeleteKr(final KR target, final Runnable success) {
        mRepository.delKr(mTaskId, target.krId, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    success.run();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
            }
        });
    }

    public void cancelTime(String mTaskId, final TaskDetailEntity detail, UpdateRegionVM updateRegionVM) {
        if (detail != null) {
            if (!JoyWorkContentExKt.canUpdateDeadline(detail.getJoyWorkDetail())) {
                return;
            }
            JoyWorkDetail joyWorkDetail = detail.getJoyWorkDetail();
            final Long oldStart = joyWorkDetail.getStartTime();
            final Long oldEnd = joyWorkDetail.getEndTime();
            final Integer oldDup = joyWorkDetail.cycle;
            final String oldRemindStr = joyWorkDetail.remindStr;

            joyWorkDetail.setEndTime(-1L);
            joyWorkDetail.setStartTime(-1L);
            joyWorkDetail.remindStr = AlertType.NO.getValue() + "";
            joyWorkDetail.cycle = 0;

            updateTaskDetail(detail);
            mRepository.updateEndTimeCycleAlert(mTaskId, -1, -1, 0, "0", actionSource, new TaskDetailWebservice.TaskCallback() {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    if (!hasError && updateRegionVM != null) {
                        if (!Objects.equals(oldDup, joyWorkDetail.cycle)) {
                            updateRegionVM.updateDup();
                        }
                        if (!Objects.equals(oldStart, joyWorkDetail.getStartTime())) {
                            updateRegionVM.updateStartTime();
                        }
                        if (!Objects.equals(oldEnd, joyWorkDetail.getEndTime())) {
                            updateRegionVM.updateDeadline();
                        }
                        if (!Objects.equals(oldRemindStr, joyWorkDetail.remindStr)) {
                            updateRegionVM.updateRemind();
                        }
                    }
                }
            });
        }
    }

    public void cancelLaunchTime(String mTaskId) {
        TaskDetailEntity taskDetailEntity = mTaskDetail.getValue();
        if (!JoyWorkContentExKt.canUpdateLaunchTime(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        JoyWorkRepo.INSTANCE.assign(mTaskId, BlockTypeEnum.INBOX, new JoyWorkAssignCallback() {
            @Override
            public void result(boolean success, @NonNull String errorMsg, @Nullable BlockTypeEnum target) {
                if (success) {
                    UpdateReporter.INSTANCE.reportUpdate();
                    TaskDetailEntity detail = mTaskDetail.getValue();
                    if (detail != null) {
                        detail.getJoyWorkDetail().setPlanTime(-1L);
                    }
                    updateTaskDetail(detail);
                } else {
                    ToastUtils.showToast(JoyWorkEx.INSTANCE.filterErrorMsg(errorMsg));
                }
            }

            @Override
            public void onStart() {

            }
        }, -1);
    }

    public void cancelRemain(String mTaskId, UpdateRegionVM updateRegionVM) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (!JoyWorkContentExKt.canUpdateRemind(detail == null ? null : detail.getJoyWorkDetail())) {
            return;
        }
        if (detail != null) {
            String old = detail.getJoyWorkDetail().remindStr;
            String newStr = AlertType.NO.getValue() + "";
            if (Objects.equals(old, newStr)) {
                return;
            }
            detail.getJoyWorkDetail().remindStr = newStr;
            updateTaskDetail(detail);
            mRepository.taskRemindTime(mTaskId, AlertType.NO.getValue() + "", actionSource, new TaskDetailWebservice.TaskCallback() {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    if (!hasError && updateRegionVM != null) {
                        updateRegionVM.updateRemind();
                    }
                }
            });
        }
    }

    public void setTitleEdit(boolean edit) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null) {
            detail.setTitleEdit(edit);
        }
        updateTaskDetail(detail);
    }

    public boolean getTitleEdit() {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null) {
            return detail.getTitleEdit();
        }
        return false;
    }

    public void delJouSpace(Documents document) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null) {
            List<Documents> list = detail.getJoyWorkDetail().getDocuments();
            if (list != null) {
                Iterator<Documents> iterator = list.iterator();
                while (iterator.hasNext()) {
                    Documents item = iterator.next();
                    if (item.getDocumentId().equals(document.getDocumentId())) {
                        iterator.remove();
                        break;
                    }
                }
            }
        }
        updateTaskDetail(detail);
        mRepository.deleteJoySpace(mTaskId, document.getDocumentId());
    }

    public void addJoySpace(List<Documents> documentsList) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null) {
            List<Documents> list = detail.getJoyWorkDetail().getDocuments();
            //做个去重 不添加重复的云文档
            Iterator<Documents> iterator = documentsList.iterator();
            while (iterator.hasNext()) {
                Documents documents = iterator.next();
                if (list != null && list.contains(documents)) {
                    iterator.remove();
                }
            }
            if (documentsList != null && documentsList.size() == 0) {
                //去重后列表为空时 不做请求
                return;
            }
            if (list == null) {
                detail.getJoyWorkDetail().setDocuments(documentsList);
            } else {
                list.addAll(documentsList);
                LinkedHashSet<Documents> hashSet = new LinkedHashSet<>(list);
                detail.getJoyWorkDetail().setDocuments(new ArrayList<>(hashSet));
            }
        }
        updateTaskDetail(detail);
        mRepository.addJoySpace(mTaskId, documentsList);
    }


    public void delResource(Resources resources) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null) {
            List<Resources> list = detail.getJoyWorkDetail().getResources();
            if (list != null) {
                Iterator<Resources> iterator = list.iterator();
                while (iterator.hasNext()) {
                    Resources item = iterator.next();
                    if (TextUtils.equals(item.getBizId(), resources.getBizId())) {
                        iterator.remove();
                        break;
                    }
                }
            }
        }
        updateTaskDetail(detail);
        List<String> ids = new ArrayList<>();
        ids.add(resources.getResourceId());
        mRepository.delTaskResources(mTaskId, ids);
    }

    public void taskChangeProject(final JoyWorkDetail.Project pro) {
        mRepository.linkTaskGroup(mTaskId, pro, new Utils2Network.Callback() {
            @Override
            public void call(boolean isSuccess) {
                if (isSuccess) {
                    updateMarkInfo(true);
                }
            }
        });
    }

    public void taskDeleteProject(final JoyWorkDetail.Project pro) {
        mRepository.taskDeleteProject(mTaskId, pro.getProjectId(), actionSource, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (!hasError) {
                    TaskDetailEntity detail = mTaskDetail.getValue();
                    if (detail != null) {
                        List<JoyWorkDetail.Project> list = detail.getJoyWorkDetail().getProjects();
                        if (ObjExKt.hasContent(list)) {
                            Iterator<JoyWorkDetail.Project> iterator = list.iterator();
                            while (iterator.hasNext()) {
                                JoyWorkDetail.Project item = iterator.next();
                                if (TextUtils.equals(item.getProjectId(), pro.getProjectId())) {
                                    iterator.remove();
                                    updateTaskDetail(detail);
                                    break;
                                }
                            }
                        }
                    }
                    updateMarkInfo(true);
                }
            }
        });
    }

    public void updateMarkInfo(final boolean forceRefresh) {
        // updateTaskDetail(detail);
        ProjectRepo.INSTANCE.refreshMark(mTaskId, new Function3<JoyWorkContent2, String, Boolean, Unit>() {
            @Override
            public Unit invoke(JoyWorkContent2 taskDetailEntity2, String s, Boolean aBoolean) {
                TaskDetailEntity detail = mTaskDetail.getValue();
                if (detail != null) {
                    // 更新
                    try {
                        detail.getJoyWorkDetail().customFields = taskDetailEntity2.customFields;
                        detail.getContent().customFieldDic = taskDetailEntity2.customFieldDic;
                    } catch (Exception e) {
                        // 有可能出现空指针异常
                        e.printStackTrace();
                    }
                    if (forceRefresh || (aBoolean != null && aBoolean)) {
                        updateTaskDetail(detail);
                    }
                }
                return null;
            }
        });
    }

    public void addResource(Resources resource, final Utils2Network.Callback callback) {
        mRepository.saveTaskResources(mTaskId, resource, new Utils2Network.Callback() {
            @Override
            public void call(boolean isSuccess) {
                if (callback != null) callback.call(isSuccess);
            }
        });
    }

    public void taskPraise() {
        mRepository.taskPraise(mTaskId);
    }


    public void cancelTaskPraise() {
        mRepository.cancelTaskPraise(mTaskId);
    }


    public void addSubTask(List<ChildWorks> works) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null && detail.getJoyWorkDetail() != null) {
            ArrayList<ChildWorks> ws = detail.getJoyWorkDetail().getChildWorks() == null ? new ArrayList<ChildWorks>() : detail.getJoyWorkDetail().getChildWorks();
            ws.addAll(works);
            detail.getJoyWorkDetail().setChildWorks(ws);
        }
        updateTaskDetail(detail);
    }

    public void updateMark(CustomFieldItem item, CustomFieldGroup group) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null) {
            Map<String, List<String>> map = detail.getJoyWorkDetail().safeCustomFields();
            if (map.get(group.columnId) == null) {
                map.put(group.columnId, new ArrayList<String>());
            }
            map.get(group.columnId).clear();
            map.get(group.columnId).add(item.detailId);
            updateTaskDetail(detail);
        }
    }

    public void changeRelation(List<Members> members) {
        TaskDetailEntity detail = mTaskDetail.getValue();
        if (detail != null && detail.getJoyWorkDetail() != null) {
            detail.getJoyWorkDetail().setExecutors(members);
        }
        updateTaskDetail(detail);
    }

    public String getParentId() {
        return mTaskId;
    }
}