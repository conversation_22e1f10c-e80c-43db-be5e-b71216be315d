package com.jd.oa.joywork.team

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.View.OnClickListener
import android.view.View.VISIBLE
import android.view.View.inflate
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.StringRes
import com.jd.me.datetime.picker.DatetimePickerDialog
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkLevel
import com.jd.oa.joywork.ProjectFilter
import com.jd.oa.joywork.ProjectPriority
import com.jd.oa.joywork.ProjectSetting
import com.jd.oa.joywork.ProjectSort
import com.jd.oa.joywork.ProjectSortAction
import com.jd.oa.joywork.ProjectSortCustom
import com.jd.oa.joywork.ProjectSortNo
import com.jd.oa.joywork.ProjectSortOwner
import com.jd.oa.joywork.ProjectSortTime
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.detail.DialogManager
import com.jd.oa.joywork.detail.DialogManager.corner
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem
import com.jd.oa.joywork.detail.data.entity.custom.ProjectFilterExt
import com.jd.oa.joywork.detail.ui.TaskUiUtils
import com.jd.oa.joywork.dialog.thirdparty.AlertChoiceConfig
import com.jd.oa.joywork.dialog.thirdparty.CancelShitActionController
import com.jd.oa.joywork.dialog.thirdparty.CenterTextTitleController
import com.jd.oa.joywork.dialog.thirdparty.CenterTextTitleDecorator
import com.jd.oa.joywork.dialog.thirdparty.DividerShitActionController
import com.jd.oa.joywork.dialog.thirdparty.EditAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.EditConfig
import com.jd.oa.joywork.dialog.thirdparty.GoneShitActionController
import com.jd.oa.joywork.dialog.thirdparty.GroupActionDialog
import com.jd.oa.joywork.dialog.thirdparty.ListShitDialog
import com.jd.oa.joywork.dialog.thirdparty.ListShitDialogAdapter
import com.jd.oa.joywork.dialog.thirdparty.ListShitDialogConfig
import com.jd.oa.joywork.dialog.thirdparty.SingleChoiceAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.SingleChoiceConfig
import com.jd.oa.joywork.dialog.thirdparty.SortGroupDialog
import com.jd.oa.joywork.dialog.thirdparty.StartTextTitleController
import com.jd.oa.joywork.dialog.thirdparty.StartTextTitleDecorator
import com.jd.oa.joywork.dialog.thirdparty.TextShitTitleController
import com.jd.oa.joywork.dialog.thirdparty.TitleAlertDialog
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.FilterValueEx
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.safeGet
import com.jd.oa.joywork.shortcut.TimeSelectHelper
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.timezone.holidayFetcher
import com.jd.oa.ui.ClearEditText
import com.jd.oa.ui.IconFontView
import com.jd.oa.ui.dialog.IShitTitleDecorator
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DateShowUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.bold
import com.jd.oa.utils.color
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.maxLenEx
import com.jd.oa.utils.normal
import com.jd.oa.utils.string
import com.jd.oa.utils.visible
import java.util.Calendar


fun showGroupAction(
    context: Context,
    actions: List<JoyWorkAction>,
    bottomAction: JoyWorkAction?,
    onClick: (JoyWorkAction) -> Unit,
) {
    val dialog = GroupActionDialog(context, actions, bottomAction, onClick)
    dialog.show()
}

fun showGroupAction(
    context: Context,
    actions: List<JoyWorkAction>,
    click: (JoyWorkAction) -> Unit
): ListShitDialog {

    val dialog = ListShitDialog(context, object : ListShitDialogAdapter<JoyWorkAction>(actions) {
        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            val view = context.inflater.inflate(R.layout.joywork_dialog_list_item, parent, false)
            val p = items[position]

            val icon = view.findViewById<TextView>(R.id.icon)
            icon.setText(p.iconId())
            icon.setTextColor(p.color())

            val tv: TextView = view.findViewById(R.id.content)
            tv.text = p.action
            tv.setTextColor(p.color())

            val detail: TextView = view.findViewById(R.id.detail)
            detail.gone()

            view.tag = p
            view.setOnClickListener {
                dialog?.dismiss()
                click.invoke(it.tag as JoyWorkAction)
            }
            return view
        }
    }, ListShitDialogConfig().apply {
        contentBackground = DrawableEx.solidRect(Color.WHITE)
    })

    dialog.titleController = CenterTextTitleController(object : CenterTextTitleDecorator() {
        override fun getTitleId(): Int {
            return R.string.joywork_project_action_title
        }
    })
    dialog.actionController = GoneShitActionController
    dialog.show()
    return dialog
}

fun showGroupDelAlertDialog(context: Context, group: Group, hard: (all: Boolean) -> Unit) {
// 2021年11月22日  修改产品文案，直接弹提示，不需要判断
//    if (group.safeTotal == 0) {
//        hard.invoke(true)
//        return
//    }
    SingleChoiceAlertDialog(context, SingleChoiceConfig().apply {
        actions = ArrayList<String>().apply {
            add(context.string(R.string.joywork_project_del_save))
            add(context.string(R.string.joywork_project_del_hard))
        }
        selectIndex = 0
        msgStr = context.string(R.string.joywork_project_del_title)
        okRes = R.string.me_ok
        okShitClick = {
            val index = it.getSelectIndex()
            if (index >= 0) {
                hard.invoke(index == 1)
            }
        }
    }).show()
}

fun showGroupDelAlertDialog(context: Context, hard: (all: Boolean) -> Unit) {
// 2021年11月22日  修改产品文案，直接弹提示，不需要判断
//    if (group.safeTotal == 0) {
//        hard.invoke(true)
//        return
//    }
    SingleChoiceAlertDialog(context, SingleChoiceConfig().apply {
        actions = ArrayList<String>().apply {
            add(context.string(R.string.joywork_project_del_save))
            add(context.string(R.string.joywork_project_del_hard))
        }
        selectIndex = 0
        msgStr = context.string(R.string.joywork_project_del_title)
        okRes = R.string.me_ok
        okShitClick = {
            val index = it.getSelectIndex()
            if (index >= 0) {
                hard.invoke(index == 1)
            }
        }
    }).show()
}

fun showGroupNameEditDialog(context: Context, name: String, groupName: (name: String) -> Unit) {
    EditAlertDialog(context, EditConfig().apply {
        defaultContent = name
        msgRes = R.string.joywork_group_name
        okRes = R.string.me_ok
        inputHintRes = R.string.joywork_please_input
        okShitClick = {
            groupName.invoke(it.getEditContent())
        }
    }).show()
}

fun showRejectTaskEditDialog(context: Context, message: (msg: String) -> Unit) {
    EditAlertDialog(context, EditConfig().apply {
        msgRes = R.string.joywork_reject_task
        okRes = R.string.joywork_confirm_reject
        inputHintRes = R.string.joywork_please_input_reject_reason
        okForceEnable = true
        editLayout = R.layout.jdme_joywork_edit_long_text
        okShitClick = {
            message.invoke(it.getEditContent())
        }
    }).show()
}

/**
 * 取消关注
 */
fun showUnfouceDialog(context: Context, hard: () -> Unit) {

    TitleAlertDialog(context, AlertChoiceConfig().apply {
        actions = ArrayList<String>().apply {
            add(context.string(R.string.joywork_unfouce_msg))
        }
        msgStr = context.string(R.string.joywork_unfouce_title)
        okRes = R.string.me_ok
        okShitClick = {
            hard()
        }
    }).show()
}

/**
 * @param callback: 参数为 null 表示取消，为空表示未输入内容，否则表示输入的内容
 */
fun showFinishAlertDialog(
    context: Context,
    isFinish: Boolean,
    isAll: Boolean,
    callback: () -> Unit
) {
    val titleRes = if (isFinish) {
        if (isAll) {
            R.string.joywork_finish_all
        } else {
            R.string.joywork_finish_task
        }
    } else {
        if (isAll) {
            R.string.joywork_restore_all
        } else {
            R.string.joywork_restore_task
        }
    }

    val actionRes = if (isFinish) {
        if (isAll) {
            R.string.joywork_finish_all_msg
        } else {
            R.string.joywork_finish_task_msg
        }
    } else {
        if (isAll) {
            R.string.joywork_restore_all_msg
        } else {
            R.string.joywork_restore_task_msg
        }
    }

    TitleAlertDialog(context, AlertChoiceConfig().apply {
        actions = ArrayList<String>().apply {
            add(context.string(actionRes))
        }
        msgStr = context.string(titleRes)
        okRes = R.string.me_ok
        okShitClick = {
            callback()
        }
    }).show()
}

/**
 * 完成时，输入信息 dialog
 * @param isAll: 是完成全部还是完成待办
 * @param bottomTipsResource: 在输入框下面可以显示额外的提示信息，传 -1 表示不显示
 */
fun showFinishEditDialog(
    context: Context,
    isAll: Boolean,
    @StringRes bottomTipsResource: Int = -1,
    groupName: (name: String?) -> Unit
): Dialog {
    val dialog = EditAlertDialog(context, EditConfig().apply {
        msgRes = if (isAll) R.string.joywork_finish_all else R.string.joy_work_finish
        okRes = R.string.joywork_finish_sure
        cancelRes = R.string.joywork_finish_cancel
        inputHintRes = R.string.joywork_finish_hint
        bottomTipRes = bottomTipsResource
        okForceEnable = true
        okShitClick = {
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_FINISH_DIALOG_OK,
                JoyWorkConstant.JOYWORK_FINISH_DIALOG_OK
            )
            groupName.invoke(it.getEditContent())
        }
        cancelShitClick = { v, dialog ->
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_FINISH_DIALOG_CANCEL,
                JoyWorkConstant.JOYWORK_FINISH_DIALOG_CANCEL
            )
            dialog.cancel()
            groupName.invoke(null)
        }
        titleLayout = R.layout.jdme_dialog_joywork_edit_input_title2
        // 初始化内容区域
        contentInit = {
            it.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14.0f)
            it.minLines = 3
            it.maxLines = 3
            it.maxLenEx = 500
            (it as? ClearEditText)?.setShowIcon(false)
            it.gravity = Gravity.START or Gravity.TOP
            it.background = DrawableEx.roundStrokeRect(
                context.color(R.color.color_dee0e3), CommonUtils.dp2px(0.5f),
                context.resources.getDimension(R.dimen.joywork_corner_small)
            )
        }
    })
    dialog.show()
    return dialog
}

fun showNewGroupDialog(context: Context, group: Group, groupName: (name: String) -> Unit) {
    EditAlertDialog(context, EditConfig().apply {
//        defaultContent = group.title
        msgRes = R.string.joywork_group_new_title
        okRes = R.string.me_ok
        inputHintRes = R.string.joywork_please_input_name
        okShitClick = {
            groupName.invoke(it.getEditContent())
        }
    }).show()
}

fun showNewGroupDialog(context: Context, finish: (name: String) -> Unit) {
    EditAlertDialog(context, EditConfig().apply {
//        defaultContent = group.title
        msgRes = R.string.joywork_group_new_title
        okRes = R.string.me_ok
        inputHintRes = R.string.joywork_please_input_name
        okShitClick = {
            finish(it.getEditContent())
        }
    }).show()
}

fun showDetailSettingDialog(
    context: Context,
    filterable: Boolean,
    click: (JoyWorkAction) -> Unit
): Dialog {
//    val actions = listOf(ProjectFilter(context), ProjectSort(context), ProjectSetting(context))
    val actions = mutableListOf<JoyWorkAction>()
    if (filterable) {
        actions.add(ProjectFilter(context))
        actions.add(ProjectSort(context))
    }
    actions.add(ProjectSetting(context))
    val dialog = ListShitDialog(context, object : ListShitDialogAdapter<JoyWorkAction>(actions) {
        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            val view =
                context.inflater.inflate(R.layout.jdme_dialog_project_option_item, parent, false)
            val p = items[position]

            val icon = view.findViewById<TextView>(R.id.icon)
            icon.setText(p.iconId())
            icon.setTextColor(p.color())

            val tv: TextView = view.findViewById(R.id.title)
            tv.text = p.action
            tv.setTextColor(p.color())

            val arrow: TextView = view.findViewById(R.id.arrow)
            if (p is ProjectSetting) {
                arrow.gone()
            } else {
                arrow.visible()
            }

            view.tag = p
            view.setOnClickListener {
                dialog?.dismiss()
                click.invoke(it.tag as JoyWorkAction)
            }
            return view
        }
    }, ListShitDialogConfig().apply {
        contentBackground = DrawableEx.solidRect(Color.WHITE)
    })

    dialog.titleController = CenterTextTitleController(object : CenterTextTitleDecorator() {
        override fun getTitleId(): Int {
            return R.string.joywork_project_setting_title
        }
    })
    dialog.actionController = CancelShitActionController
    dialog.show()
    return dialog
}

/**
 * 团队待办筛选弹框
 */
fun showFilterDialog(
    filterValue: FilterValue,
    model: TeamDetailViewModel,
    context: Context,
    isSelect: (ProjectFilterEnum) -> Boolean,
    click: (ProjectFilterEnum) -> Unit
): Dialog {
    val actions = listOf(
        ProjectFilterEnum.SCREEN_NULL,
        ProjectFilterEnum.SCREEN_ONLY_MINE,
        ProjectFilterEnum.SCREEN_THIS_WEEK,
        ProjectFilterEnum.SCREEN_NEXT_WEEK,
        ProjectFilterEnum.SCREEN_CUSTOMS
    )
    return showFilterDialog(context, actions, filterValue, model, isSelect, click)
}

fun showFilterDialog(
    context: Context,
    actions: List<ProjectFilterEnum>,
    filterValue: FilterValue? = null,
    model: TeamDetailViewModel,
    isSelect: (ProjectFilterEnum) -> Boolean,
    click: (ProjectFilterEnum) -> Unit
): Dialog {
    val dialog =
        ListShitDialog(context, object : ListShitDialogAdapter<ProjectFilterEnum>(actions) {
            override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                val p = items[position]
                val map = if (p == ProjectFilterEnum.SCREEN_CUSTOMS && isSelect(p)) {
                    p.bindItem2(parent!!) {
                        dialog?.dismiss()
                        click.invoke(it.tag as ProjectFilterEnum)
                    }
                } else {
                    p.bindItem(parent!!) {
                        dialog?.dismiss()
                        click.invoke(it.tag as ProjectFilterEnum)
                    }
                }
                val subTitle = map[R.id.mSubTitle] as? TextView
                if (subTitle != null && filterValue != null) {
                    val subContent = map[R.id.mSubContent] as? TextView
                    val array = ArrayList<String>()
                    FilterValueEx.getUIString(
                        filterValue,
                        context,
                        newLine = true,
                        model,
                        array
                    )
                    subTitle.text = "${array.safeGet(0)}: " ?: ""
                    subContent?.text = array.safeGet(1) ?: ""
                    if (filterValue.enum.code == ProjectFilterEnum.SCREEN_EDN_TIME.code) {
                        subTitle.maxLines = 2
                    } else {
                        subTitle.maxLines = 1
                    }
                    map[R.id.mSubTitleContainer].visible()
                } else {
                    map[R.id.mSubTitleContainer].gone()
                }
                if (!isSelect(p)) {
                    map[R.id.icon]!!.gone()
                    (map[R.id.title] as TextView).normal()
                } else {
                    map[R.id.icon]!!.visible()
                    (map[R.id.title] as TextView).bold()
                }
                return map[R.id.key] as View
            }
        }, ListShitDialogConfig().apply {
            contentBackground = DrawableEx.solidRect(Color.WHITE)
        })

    dialog.titleController = CenterTextTitleController(object : CenterTextTitleDecorator() {
        override fun getTitleId(): Int {
            return R.string.joywork_project_setting_filter
        }
    })
    dialog.actionController = CancelShitActionController
    dialog.show()
    return dialog
}

/**
 * 自定义筛选弹窗
 */
fun showCustomFilterDialog(
    context: Context,
    uiStatus: UIStatus,
    customFieldGroupOuter: CustomFieldGroupOuter?,
    extra: List<ProjectFilterEnum>? = null,
    click: (FilterValue) -> Unit
): Dialog {
    val actions = mutableListOf(
        ProjectFilterEnum.SCREEN_EXECUTOR,
        ProjectFilterEnum.SCREEN_EDN_TIME,
        ProjectFilterEnum.SCREEN_PRIORITY,
    )
    if (extra.isLegalList()) {
        actions.addAll(extra!!)
    }
    val num = actions.size
    customFieldGroupOuter?.let {
        val customFields = customFieldGroupOuter.customFields
        for (cus in customFields) {
            actions.add(ProjectFilterEnum.SCREEN_CUSTOM)
        }
    }
    val dialog =
        ListShitDialog(context, object : ListShitDialogAdapter<ProjectFilterEnum>(actions) {
            @SuppressLint("ViewHolder")
            override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                val view: View =
                    inflate(context, R.layout.joywork_dialog_list_item_custom_filter, null)
                val title: TextView = view.findViewById(R.id.tv_title)
                val content: TextView = view.findViewById(R.id.tv_content)
                content.maxLines = 1
                if (actions[position] == ProjectFilterEnum.SCREEN_CUSTOM)
                    title.text =
                        customFieldGroupOuter?.customFields?.get(position - num)?.title
                            ?: context.getString(R.string.joywork_project_setting_filter_custom)
                else
                    title.text = context.getString(actions[position].stringId())
                if (uiStatus.mFilter.enum.code == actions[position].code && uiStatus.mFilter.value != null) {//是当前选中的item
                    when (actions[position]) {
                        ProjectFilterEnum.SCREEN_EDN_TIME -> {
                            content.maxLines = 2
                            val startTime =
                                uiStatus.mFilter.value?.startTime?.let {
                                    DateShowUtils.getTimeShowTextWithHourMinute(
                                        it
                                    )
                                } ?: ""
                            val endTime =
                                DateShowUtils.getTimeShowTextWithHourMinute(uiStatus.mFilter.value!!.endTime)
                            content.text = if (startTime.isLegalString()) {
                                startTime.plus("-\n$endTime")
                            } else {
                                endTime
                            }
                        }

                        ProjectFilterEnum.SCREEN_PRIORITY -> {
                            val level = JoyWorkLevel.NO.getLevel(
                                uiStatus.mFilter.value?.priority ?: JoyWorkLevel.NO.value
                            )
                            content.text = context.getString(level.getResId())
                        }

                        ProjectFilterEnum.SCREEN_PROJECT -> {
                            content.text = uiStatus.mFilter.value?.title ?: ""
                        }

                        ProjectFilterEnum.SCREEN_EXECUTOR -> content.text =
                            uiStatus.mFilter.value?.realName ?: ""

                        ProjectFilterEnum.SCREEN_CUSTOM ->
                            if (uiStatus.mFilter.value!!.columnId == customFieldGroupOuter!!.customFields[position - num].columnId)
                                content.text = customFieldGroupOuter.getDetailItem(
                                    uiStatus.mFilter.value?.columnId,
                                    uiStatus.mFilter.value?.detailId
                                )?.itemContent?.content ?: ""

                        else -> {}
                    }
                } else {
                    content.text = ""
                }
                view.setOnClickListener(OnClickListener
                {
                    when (actions[position]) {
                        ProjectFilterEnum.SCREEN_EDN_TIME -> {//截止时间 默认是明天18:00
                            val datetimePickerDialog = DatetimePickerDialog(context)
                            val filterValue = uiStatus.mFilter.value

                            val endDefault = Calendar.getInstance()
                            if (filterValue?.endTime.isLegalTimestamp()) {
                                endDefault.timeInMillis = filterValue!!.endTime
                            } else {
                                endDefault[Calendar.HOUR_OF_DAY] = 18
                                endDefault[Calendar.MINUTE] = 0
                                endDefault.add(Calendar.DATE, 1)
                            }
                            datetimePickerDialog.setEndDate(endDefault.time)
                            datetimePickerDialog.setEndTime(endDefault.time)

                            if (filterValue?.startTime.isLegalTimestamp()) {
                                val startDefault = Calendar.getInstance()
                                startDefault.timeInMillis = filterValue!!.startTime
                                datetimePickerDialog.setStartTime(startDefault.time)
                                datetimePickerDialog.setStartDate(startDefault.time)
                            }
                            datetimePickerDialog.setOnConfirmListener(DatetimePickerDialog.OnConfirmListener { result ->
                                val start = result.start?.run {
                                    TimeSelectHelper.getTime(this, true)
                                }
                                val end = result.end?.run {
                                    TimeSelectHelper.getTime(this, false)
                                }
                                val newFilterValue = FilterValue().apply {
                                    type = ProjectFilterEnum.SCREEN_EDN_TIME.code
                                    value = ProjectFilterExt().apply {
                                        projectFilterEnum = ProjectFilterEnum.SCREEN_EDN_TIME
                                        startTime = start
                                        endTime = end
                                    }
                                }
                                click.invoke(newFilterValue)
                            })
                            datetimePickerDialog.holidayFetcher()
                            datetimePickerDialog.show()
                        }

                        ProjectFilterEnum.SCREEN_CUSTOM -> //自定义扩展字段
                        {
                            val curDid: String?
                            (uiStatus.mFilter.enum == ProjectFilterEnum.SCREEN_CUSTOM).let {
                                curDid = uiStatus.mFilter.value?.detailId//当前选中的item
                            }
                            customFieldGroupOuter?.let {
                                showCustomDialog(
                                    context,
                                    curDid,
                                    customFieldGroupOuter.customFields[position - num],
                                ) {
                                    val filterValue = FilterValue().apply {
                                        type = ProjectFilterEnum.SCREEN_CUSTOM.code
                                        value = ProjectFilterExt().apply {
                                            projectFilterEnum = ProjectFilterEnum.SCREEN_CUSTOM
                                            columnId =
                                                customFieldGroupOuter.customFields[position - num].columnId
                                            detailId = it.detailId
                                        }
                                    }
                                    click.invoke(filterValue)
                                }
                            }
                        }

                        ProjectFilterEnum.SCREEN_EXECUTOR ->//执行人
                            TaskUiUtils.selectContacts(
                                context as Activity,
                                1,
                                object : Callback<java.util.ArrayList<MemberEntityJd>> {
                                    override fun onSuccess(bean: java.util.ArrayList<MemberEntityJd>?) {
                                        val filterValue = FilterValue().apply {
                                            type = ProjectFilterEnum.SCREEN_EXECUTOR.code
                                            value = ProjectFilterExt().apply {
                                                projectFilterEnum =
                                                    ProjectFilterEnum.SCREEN_EXECUTOR
                                                ddAppId = bean?.get(0)?.app.toString()
                                                emplAccount = bean?.get(0)?.id
                                                realName = bean?.get(0)?.name ?: ""
                                            }
                                        }
                                        click.invoke(filterValue)
                                    }

                                    override fun onFail() {
                                    }
                                })

                        ProjectFilterEnum.SCREEN_PRIORITY ->//优先级
                            showLevDialog(
                                context,
                                uiStatus.mFilter.value?.priority
                            ) {
                                val filterValue = FilterValue().apply {
                                    type = ProjectFilterEnum.SCREEN_PRIORITY.code
                                    value = ProjectFilterExt().apply {
                                        projectFilterEnum = ProjectFilterEnum.SCREEN_PRIORITY
                                        priority = it
                                    }
                                }
                                click.invoke(filterValue)
                            }

                        ProjectFilterEnum.SCREEN_PROJECT -> {
                            // 所属清单
                            val filterValue = FilterValue().apply {
                                type = ProjectFilterEnum.SCREEN_PROJECT.code
                                value = ProjectFilterExt().apply {
                                    projectFilterEnum = ProjectFilterEnum.SCREEN_PROJECT
                                    projectId = ""
                                    this.title = ""
                                }
                            }
                            click.invoke(filterValue)
                        }

                        else -> {}
                    }
                    dialog?.dismiss()
                })
                return view
            }
        }, ListShitDialogConfig().apply {
            contentBackground = DrawableEx.solidRect(Color.WHITE)
        })
    dialog.titleController = StartTextTitleController(object : StartTextTitleDecorator() {
        override fun getTitleId(): Int {
            return R.string.joywork_project_setting_filter_custom
        }

        override fun getTitleString(): String? {
            return null
        }
    })
    dialog.actionController = CancelShitActionController
    dialog.show()
    return dialog
}

/**
 * 团队待办排序弹框
 * @param custom: 自定义的内容。偶数位是要显示的内容，奇数位是要返回的内容
 */
fun showSortDialog2(
    extra: List<ProjectSortAction>,
    context: Context,
    curA: SortValue,
    selected: Boolean,
    custom: ArrayList<String>?,
    click: (ProjectSortAction, Boolean, Boolean) -> Unit
): Dialog {
    val actions = mutableListOf(
//        ProjectSortTitle(context),
        ProjectSortNo(context),
        ProjectSortTime(context),
        ProjectSortOwner(context),
        ProjectPriority(context)
    )
    actions.addAll(extra)
    var i = 0
    while (custom != null && i + 1 < custom.size) {
        actions.add(ProjectSortCustom(custom[i], custom[i + 1]))
        i += 2
    }
    return showSortDialog3(actions, context, curA, selected, click)
}

fun showSortDialog3(
    actions: List<ProjectSortAction>,
    context: Context,
    curA: SortValue,
    selected: Boolean,
    click: (ProjectSortAction, Boolean, Boolean) -> Unit
): Dialog {
    val adapter = ProjectDialogHelper(actions, context, selected, curA) { i1, i2, i3 ->
        click.invoke(i1, i2, i3)
        ""
    }

    val dialog = ListShitDialog(context, adapter, ListShitDialogConfig().apply {
        contentBackground = DrawableEx.solidRect(Color.WHITE)
    })

    dialog.setOnCancelListener {
        dialog.setOnDismissListener(null)
    }

    dialog.titleController = CenterTextTitleController(object : CenterTextTitleDecorator() {
        override fun getTitleId(): Int {
            return R.string.joywork_project_setting_sort
        }
    })
    dialog.actionController = CancelShitActionController
    dialog.show()
    return dialog
}

fun showStatusDialog(
    context: Context,
    currentStatus: TaskStatusEnum,
    showDel: Boolean,
    click: (TaskStatusEnum) -> Unit
): ListShitDialog {
    val data = mutableListOf<TaskStatusEnum>()
    data.add(TaskStatusEnum.RISK)
    data.add(TaskStatusEnum.UN_FINISH)
    data.add(TaskStatusEnum.FINISH)
    data.add(TaskStatusEnum.ALL)
//    if (showDel) {
//        data.add(TaskStatusEnum.DELETED)
//    }
    data.add(TaskStatusEnum.DELETED)
    return showStatusDialog(context, currentStatus, data, click)
}

fun showStatusDialog(
    context: Context,
    currentStatus: TaskStatusEnum,
    data: List<TaskStatusEnum>,
    click: (TaskStatusEnum) -> Unit
): ListShitDialog {
    val dialog = ListShitDialog(
        context,
        object : ListShitDialogAdapter<TaskStatusEnum>(data) {
            override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                val view = context.layoutInflater.inflate(
                    R.layout.joywork_dup_item,
                    parent,
                    false
                )
                val tv: TextView = view.findViewById(R.id.content)
                val d: TaskStatusEnum = items[position]
                tv.setText(d.getStringId())
                view.tag = d
                view.setOnClickListener {
                    dialog?.dismiss()
                    val tagData = it.tag as TaskStatusEnum
                    click.invoke(tagData)
                }
                val check = view.findViewById<TextView>(R.id.check)
                if (currentStatus == d) {
                    check.visibility = View.VISIBLE
                    tv.bold()
                } else {
                    check.visibility = View.GONE
                    tv.normal()
                }
                return view
            }
        },
        ListShitDialogConfig().apply {
            contentBackground = DrawableEx.solidRect(Color.WHITE)
        })
    dialog.titleController = TextShitTitleController(object : IShitTitleDecorator<TextView> {
        override fun decorate(t: TextView) {
            t.gravity = Gravity.CENTER
            t.setTextColor(Color.parseColor("#FF232930"))
            t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16.0f)
            t.setText(R.string.joywork_task_tabbar_title)
            t.background =
                DrawableEx.roundSolidDirRect(Color.WHITE, context.corner, DrawableEx.DIR_TOP)
        }
    })
    dialog.actionController = DividerShitActionController
    dialog.show()
    return dialog
}


fun showCustomDialog(
    context: Context,
    curDid: String?,
    customFieldGroup: CustomFieldGroup,
    click: (CustomFieldItem) -> Unit
): ListShitDialog {
    val details = customFieldGroup.details
    val dialog =
        ListShitDialog(context, object : ListShitDialogAdapter<CustomFieldItem>(details) {
            @SuppressLint("ViewHolder")
            override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
                val view = inflate(context, R.layout.joywork_dialog_list_item_custom_status, null)
                val tvTitle = view.findViewById<TextView>(R.id.tv_title)
                val tvCheck = view.findViewById<IconFontView>(R.id.tv_check)
                val itemContent = details[position].itemContent
                if (curDid.equals(details[position].detailId)) tvCheck.visibility = VISIBLE
                tvTitle.text = itemContent.content
                tvTitle.setTextColor(Color.parseColor(itemContent.color))
                tvTitle.backgroundTintList =
                    ColorStateList.valueOf(Color.parseColor(itemContent.background))
                view.setOnClickListener(OnClickListener {
                    click.invoke(details[position])
                    dialog?.dismiss()
                })
                return view
            }
        }, ListShitDialogConfig().apply {
            contentBackground = DrawableEx.solidRect(Color.WHITE)
        })

    dialog.titleController = StartTextTitleController(object : StartTextTitleDecorator() {
        override fun getTitleId(): Int {
            return R.string.joywork_project_setting_filter_custom
        }

        override fun getTitleString(): String {
            return customFieldGroup.title
        }
    })
    dialog.actionController = CancelShitActionController
    dialog.show()
    return dialog
}

/**
 * 选择优先级
 */
fun showLevDialog(context: Context, priorityType: Int?, click: (Int) -> Unit) {
    val dialog = DialogManager.showLevelDialog(context, priorityType ?: 0) {
        click.invoke(it)
    }
    dialog.show()
}

fun showGroupSortDialog(
    context: Context,
    projectId: String,
    groups: MutableList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    callback: (ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) -> Unit
) {
    SortGroupDialog(context, projectId, groups, callback).show()
}