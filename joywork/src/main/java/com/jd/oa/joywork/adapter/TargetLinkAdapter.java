package com.jd.oa.joywork.adapter;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.bean.KR;
import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.joywork.team.kpi.KpiExKt;
import com.jd.oa.joywork.team.kpi.PlaceHolderVH;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DrawableEx;

import java.util.ArrayList;
import java.util.List;

public class TargetLinkAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private final TargetCallback callback;
    private final boolean canUpdate;
    public List<Object> data;

    public interface TargetCallback {
        void onClick(Object project);

        void onDelClick(Object project);
    }

    public TargetLinkAdapter(boolean canUpdate, TargetCallback callback, List<Object> targets) {
        this.canUpdate = canUpdate;
        this.callback = callback;
        data = new ArrayList<>(targets);
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (viewType == 2) {
            return new PlaceHolderVH(viewGroup.getContext());
        }
        LayoutInflater inflater = LayoutInflater.from(viewGroup.getContext());
        return new ViewHolder(inflater.inflate(R.layout.joywork_target_item, viewGroup, false));
    }

    private final View.OnClickListener mNameClick = new View.OnClickListener() {

        @Override
        public void onClick(View v) {
            Object target = v.getTag();
            callback.onClick(target);
        }
    };
    private final View.OnClickListener mDelClick = new View.OnClickListener() {

        @Override
        public void onClick(View v) {
            Object target = v.getTag();
            callback.onDelClick(target);
        }
    };

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {
        if (holder instanceof PlaceHolderVH) {
            return;
        }
        ViewHolder viewHolder = (ViewHolder) holder;
        Object object = data.get(i);
        viewHolder.tvClose.setVisibility(canUpdate ? View.VISIBLE : View.GONE);
        viewHolder.rl1.setBackground(DrawableEx.INSTANCE.roundSolidRect(Color.parseColor("#F5F5F5"),
                CommonUtils.dp2px(4f)));
        if (object instanceof KpiTarget) {
            KpiExKt.withWeight((KpiTarget) object, viewHolder.mTargetNameView);
        } else if (object instanceof KR) {
            viewHolder.mTargetNameView.setText(((KR) object).content);
        }
        viewHolder.mTargetNameView.setTag(object);
        viewHolder.mTargetNameView.setOnClickListener(mNameClick);

        viewHolder.tvClose.setTag(object);
        viewHolder.tvClose.setOnClickListener(mDelClick);

        if (i == 0) {
            viewHolder.space.setVisibility(View.GONE);
        } else {
            viewHolder.space.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return data.size();
    }

    @Override
    public int getItemViewType(int position) {
        Object o = data.get(position);
        if (o instanceof KpiTarget || o instanceof KR) {
            return 1;
        } else {
            return 2;
        }
    }

    public void submitList(List<Object> ps) {
        ps = ps == null ? new ArrayList<>() : ps;
        data.clear();
        data.addAll(ps);
        notifyDataSetChanged();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        public View rl1;
        public TextView mTargetNameView;
        public View space;
        public View tvClose;

        public ViewHolder(View itemView) {
            super(itemView);
            rl1 = itemView.findViewById(R.id.rl_1);
            mTargetNameView = itemView.findViewById(R.id.tv_group_name);
            space = itemView.findViewById(R.id.space);
            tvClose = itemView.findViewById(R.id.tv_close);
        }
    }
}
