package com.jd.oa.joywork

import android.content.Context
import com.jd.me.datetime.picker.DatetimePickerDialog
import com.jd.oa.utils.DateShowUtils
import com.jd.oa.utils.DateUtils
import java.text.SimpleDateFormat
import java.util.*

/**
 * joywork 专用时间显示方法
 */

/**
 * 中文：年/月/日
 * 英文：月/日/年
 */
fun yearMonthDay(context: Context, long: Long): String {
    if (DateUtils.isSameYear(long, System.currentTimeMillis())) {
        val format = SimpleDateFormat(context.resources.getString(R.string.joywork_time_format1), Locale.CHINESE)
        return format.format(long)
    } else {
        val format = SimpleDateFormat("yyyy/M/d", Locale.CHINESE)
        return format.format(long)
    }
}

object TimeSelectTransfer : DatetimePickerDialog.TimeTransfer {
    override fun transfer(date: Date?): String? {
        return date?.run {
            if (DateUtils.isSameYear(time, System.currentTimeMillis())) {
                DateShowUtils.getTimeShowText(this.time)
            } else {
                DateShowUtils.getSimpleTimeShowText(time)
            }
        }
    }
}