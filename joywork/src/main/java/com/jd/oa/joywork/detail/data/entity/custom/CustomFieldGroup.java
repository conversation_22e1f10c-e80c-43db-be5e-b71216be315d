package com.jd.oa.joywork.detail.data.entity.custom;

import java.util.List;

public class CustomFieldGroup {
    public String columnId;
    public String title;
    public Integer type;
    public Integer contentType;
    public String desc;
    public Integer status;
    // 每一个自定义字段的选项
    public List<CustomFieldItem> details;

    // {
    //                "columnId": "361208658547281920",
    //                "details": [
    //                    {
    //                        "detailId": "361208658673111040",
    //                        "sort": 105000000,
    //                        "content": "{\"background\":\"#00C166\",\"content\":\"正常\",\"color\":\"#FFFFFF\"}",
    //                        "status": 1
    //                    },
    //                    {
    //                        "detailId": "361208658715054080",
    //                        "sort": 110000000,
    //                        "content": "{\"background\":\"#FFB416\",\"content\":\"风险\",\"color\":\"#232930\"}",
    //                        "status": 1
    //                    },
    //                    {
    //                        "detailId": "361208658756997120",
    //                        "sort": 115000000,
    //                        "content": "{\"background\":\"#FE514C\",\"content\":\"问题\",\"color\":\"#FFFFFF\"}",
    //                        "status": 1
    //                    }
    //                ],
    //                "title": "待办状态",
    //                "type": 1,
    //                "contentType": 1,
    //                "desc": "可以选择待办状态：问题、风险、正常",
    //                "status": 1
    //            }
}
