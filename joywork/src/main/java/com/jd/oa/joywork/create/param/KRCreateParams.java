package com.jd.oa.joywork.create.param;

import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.bean.KR;
import com.jd.oa.joywork.create.Value;
import com.jd.oa.joywork.team.kpi.KpiRepo;
import com.jd.oa.router.DeepLink;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * 【NOTICE】：The deeplink only works in android.
 * mparam={"krId":"xx","appName":"goal"}
 */
public class KRCreateParams extends DeeplinkCreateParam {
    public static final String APP_NAME = "kr";

    private String krId;
    private String goalId;
    private String content;

    public static String genDeeplink(String krId, HashMap<String, Object> map) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("appName", APP_NAME);
            jsonObject.put("krId", krId);
            if (map != null && map.containsKey("content")) {
                jsonObject.put("content", krId);
            }
            if (map != null && map.containsKey("goalId")) {
                jsonObject.put("goalId", krId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return DeepLink.JOY_WORK_CREATE + "?mparam=" + jsonObject;
    }

    public static String genParamString(String krId, HashMap<String, Object> map) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("appName", APP_NAME);
            jsonObject.put("krId", krId);
            if (map != null && map.containsKey("content")) {
                jsonObject.put("content", krId);
            }
            if (map != null && map.containsKey("goalId")) {
                jsonObject.put("goalId", krId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }

    @Override
    public void onParseParam(JSONObject object) throws JSONException {
        krId = object.getString("krId");
        if (object.has("goalId")) {
            goalId = object.getString("goalId");
        }
        if (object.has("content")) {
            content = object.getString("content");
        }
    }

    @Override
    public void initCreateValue(final Value value) {
        if (ObjExKt.isLegalString(content)) {
            // Deeplink had carried kr's info. Therefore, kr needs to be displayed at the beginning
            KR kr = new KR();
            kr.krId = krId;
            kr.goalId = goalId;
            kr.content = content;
            value.addKr(kr);
        }
        KpiRepo.INSTANCE.getKrDetail(krId, new Function1<KR, Unit>() {
            @Override
            public Unit invoke(KR kr) {
                if (kr != null) {
                    value.addKr(kr);
                }
                return null;
            }
        });
    }

    @Override
    public String getAppName() {
        return APP_NAME;
    }
}
