package com.jd.oa.joywork.utils

import java.util.*

/**
 * 是否与当前时间处于同一周
 */
fun Long?.isThisWeek(): <PERSON><PERSON><PERSON> {
    if (this == null)
        return false

    val c = Calendar.getInstance()
    c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)

    val c2 = Calendar.getInstance()
    c2.timeInMillis = this
    c2.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
    return c.get(Calendar.YEAR) == c2.get(Calendar.YEAR)
            && c.get(Calendar.DAY_OF_YEAR) == c2.get(Calendar.DAY_OF_YEAR)
            && c.get(Calendar.MONTH) == c2.get(Calendar.MONTH)
}


/**
 * 是否是上一周
 */
fun Long?.isLastWeek(): Bo<PERSON>an {
    if (this == null)
        return false
    val l = this + 7 * 24 * 60 * 60 * 1000L
    return l.isThisWeek()
}