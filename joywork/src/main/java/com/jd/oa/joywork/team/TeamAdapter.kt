package com.jd.oa.joywork.team

import android.graphics.Color
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.*
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.group.JoyWorkGroupAdapter
import com.jd.oa.joywork.team.TeamTitleVH.Companion.group
import com.jd.oa.joywork.team.TeamTitleVH.Companion.isInit
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.bean.TeamGroupExtend
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.joywork.team.view.ItemContainer
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

/**
 * 团队详情对应的 adapter
 */
class TeamAdapter(
    val projectId: String,
    private val ownerFragment: TeamBaseFragment,
    groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    context: FragmentActivity,
    val pageStatus: TaskStatusEnum,
    private val recyclerView: HRecyclerView,
    group: CustomFieldGroupOuter
) : JoyWorkGroupAdapter(ownerFragment, groups, context) {

    var swaped = false

    var titleGroup = group

    private val titleType = 100
    private val titleTypeTextImage = titleType + 1 // 分组标题，但只显示文字(可能会显示头像)，且不支持收起操作
    private val loadMoreType = 200
    private val loadMoreType2 = loadMoreType + 1
    private val newGroupItem = 300
    private val itemType = 400

    var delCallback: ((Boolean, Group) -> Unit)? = null

    var groupLoadMoreCallback: ((String, Int, (Group?, Boolean) -> Unit) -> Unit)? = null

    var editCallback: ((String, Group) -> Unit)? = null

    val newCallback = { title: String, before: Boolean, group: Group? ->
        ProjectRepo.createGroup(
            projectId,
            title,
            if (before) {
                null
            } else group?.groupId,
            if (before) group?.groupId else null
        ) { success: Boolean, resultGroup: Group?, msg: String? ->
            if (success && resultGroup != null) {
                resultGroup.initialize = true // 新建分组，初始化过
                val newTitle = JoyWorkTitle(
                    R.string.icon_edit_delete,
                    resultGroup.safeTitle(context),
                    resultGroup.safeTotal,
                    Color.TRANSPARENT
                )
                newTitle.extra = resultGroup
                resultGroup.permissions = group?.permissions

                val newGroup =
                    ExpandableGroup(newTitle, ArrayList<JoyWork>(), resultGroup.groupId, true)
                var groupIndex = processor.groups.indexOfFirst {
                    (it.getTitle()?.extra as? Group)?.groupId == group?.groupId
                }
                groupIndex = Math.max(groupIndex, 0)
                // 前插
                if (before) {
                    processor.addGroup(newGroup, groupIndex)
                    // 需要通知 IDataKeeper 添加新的分组
                    ownerFragment.addNewGroup(resultGroup, groupIndex)
                } else {// 后插
                    processor.addGroup(newGroup, groupIndex + 1)
                    ownerFragment.addNewGroup(resultGroup, groupIndex + 1)
                }
                notifyDataSetChanged()
            } else {
                Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT).show()
            }
        }
    }

    fun sortJoyWork2(joyWork: JoyWork) {
        val param = processor.getLocationInfo(joyWork)
        JoyWorkRepo.sortProjectJoyWork(
            joyWork.taskId,
            param.blockType,
            projectId,
            param,
            object : JoyWorkUpdateCallback {
                override fun onStart() {
                }

                override fun result(success: Boolean, errorMsg: String) {
//                    PromptUtils.removeLoadDialog(context)
                    if (success) {
//                        ProjectConstant.sendBroadcast(context, ProjectConstant.JOYWORK_SORT)
                        // 刷新整个列表。里面可能涉及到多个地方的数据修改。比如时间头的移除/添加，组中数量的修改等
                        processor.filterAndSyncData(context)
                        notifyDataSetChanged()
                        initGroupIfNecessary(joyWork)
                    } else {
                        // 恢复原样
                        restore(joyWork)
                        notifyDataSetChanged()
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }
            })
    }

    private fun initGroupIfNecessary(joyWork: JoyWork) {
        (joyWork.expandableGroup.title.extra as? Group)?.apply {
            if (isInit) {
                expandGroup(joyWork.expandableGroup.title)
            } else {
                toggleGroup(title = joyWork.expandableGroup.title)
            }
        }
    }

    fun swap(
        fromVH: RecyclerView.ViewHolder,
        toVH: RecyclerView.ViewHolder,
        delta: Boolean,
        expand: Boolean
    ) {
        if (fromVH.adapterPosition == toVH.adapterPosition)
            return

        if (fromVH.adapterPosition > toVH.adapterPosition && toVH is TeamTitleVH) {
            // 上滑。只有 LoadMoreVH + GroupVH 情况时才不可交换
            if (toVH.adapterPosition == 0) {
                return
            }
            val item = processor.getData()[toVH.adapterPosition - 1]
            if (item is LoadMoreJoyWork)
                return
        }
        swaped = true
        Log.e("TAG", "from = ${fromVH.adapterPosition},to = ${toVH.adapterPosition}")

        val fromItem = fromVH.itemView.getTag(R.id.jdme_tag_id) as JoyWork

        if (toVH is TeamJoyWorkVH || toVH is TeamLoadMoreVH) { // 两个 item 交换
            val toItem = toVH.itemView.getTag(R.id.jdme_tag_id) as JoyWork

            var toIndex = toItem.expandableGroup.realItems.indexOf(toItem)
            val fromIndex = fromItem.expandableGroup.realItems.indexOf(fromItem)
            if (fromItem.expandableGroup == toItem.expandableGroup) {// 同组
                Log.e("TAG", "00 =$fromIndex, $toIndex")
                fromItem.expandableGroup.realItems.move(fromIndex, toIndex)
                Log.e(
                    "TAG",
                    "00 =${fromItem.expandableGroup.realItems.indexOf(fromItem)}, ${
                        toItem.expandableGroup.realItems.indexOf(toItem)
                    }"
                )
//                Collections.swap(fromItem.expandableGroup.realItems, fromIndex, toIndex)
            } else {
                if (delta) toIndex++
                Log.e("TAG", "01 = $fromIndex, $toIndex")
                fromItem.expandableGroup.realItems.remove(fromItem)
                fromItem.expandableGroup.title.count--
                toItem.expandableGroup.realItems.add(toIndex, fromItem)
                toItem.expandableGroup.title.count++
            }
            toItem.expandableGroup.notifyItemChange()
        } else { // 分组标题。要么拖入，要么拖出
            val toItem = toVH.itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle

            // 从旧组中删除
            fromItem.expandableGroup.realItems.remove(fromItem)
            fromItem.expandableGroup.clearTmp()
            fromItem.expandableGroup.title.count--

            if (fromItem.expandableGroup.id == toItem.expandableGroup.id) {
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                // 从组内拖到标题，理论上应该只是拖出。但鬼知道会不会有考虑不到的情况，弄个 if 判断吧
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，要拖出当前分组，添加到上一个分组
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    if(!toGroup.expand){
                        toGroup.saveTmp(fromItem)
                    }
                    Log.e("TAG", "11 = 0")
                    if (toGroup.expand) {
                        toGroup.realItems.add(fromItem)
                    } else {
                        toGroup.realItems.add(0, fromItem)
                    }
                    toGroup.title.count++
                } else {
                    Log.e("TAG", "11 = 1")
                    // 理论上这里执行不到
                    // 往下拖，拖入当前分组
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    if(!toGroup.expand){
                        toGroup.saveTmp(fromItem)
                    }
                    toGroup.realItems.add(0, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            } else {// 不同组，理论上应该是拖入
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，拖出。理论上这里执行不到
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    if(!toGroup.expand){
                        toGroup.saveTmp(fromItem)
                    }
                    Log.e(
                        "TAG",
                        "2 fromIndex = ${toGroup.realItems.size}, group = ${toGroup.title.title}"
                    )
                    toGroup.realItems.add(toGroup.realItems.size, fromItem)
                    toGroup.title.count++
                } else {// 正常的拖入
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    if(!toGroup.expand){
                        toGroup.saveTmp(fromItem)
                    }
                    var insertIndex = 0
                    val r = kotlin.runCatching {
                        insertIndex = processor.getData().indexOf(toItem) - processor.getData()
                            .indexOf(toGroup.title)
                    }
                    r.exceptionOrNull()?.printStackTrace()
                    Log.e("TAG", "2 down = $insertIndex")
                    toGroup.realItems.add(insertIndex, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            }
        }
        processor.refreshData()

        notifyItemMoved(fromVH.adapterPosition, toVH.adapterPosition)
    }

    val sortLoadMore = { lm: ProjectLoadMore2, callback: (Boolean) -> Unit ->
        ownerFragment.loadMore(processor.getData().count {
            it.isJoyWork()
        }) { group: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>?, success: Boolean, finish: Boolean ->
            if (success && group != null) {
                processor.groups
                if (group.isLegalList()) {
                    processor.groups.lastOrNull()?.getRealItems()?.remove(lm)
                    if (processor.groups.lastOrNull()?.getId() == group.firstOrNull()?.id) {
                        // 有同组，需要合并同组
                        processor.groups.lastOrNull()?.getRealItems()?.addAll(
                            group.firstOrNull()?.realItems
                                ?: ArrayList<JoyWork>()
                        )
                        group.removeFirstOrNull()

                    }
                    processor.groups.lastOrNull()?.notifyItemChange()
                    processor.appendGroups(group)
                    if (!finish) {
                        processor.groups.lastOrNull()?.getRealItems()?.add(lm)
                        processor.groups.lastOrNull()?.notifyItemChange()
                    }
                    processor.refreshData()
                    notifyDataSetChanged()
                }
                callback(true)
            } else {
                callback(false)
            }
        }
    }

    private val groupClick = View.OnClickListener {
        val title = it.getTag(R.id.jdme_tag_id) as JoyWorkTitle
        toggleGroup(title)
    }

    override fun getItemViewType(position: Int): Int {
        val data = processor.getData()[position]
        return when (data) {
            is ProjectAddTitle -> {
                newGroupItem
            }
            is JoyWorkTitle -> {
                if (data.extra is TeamGroupExtend) {
                    titleTypeTextImage
                } else {
                    titleType
                }
            }
            is ProjectLoadMore2 -> {
                loadMoreType2
            }
            is ProjectLoadMore -> {
                loadMoreType
            }
            is JoyWork -> {
                itemType
            }
            else -> {
                super.getItemViewType(position)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            newGroupItem -> {
                TeamNewGroupVH(context, parent, this)
            }
            titleTypeTextImage -> {
                TeamGroupTextImageVH(context, parent, this)
            }
            titleType -> {
                TeamTitleVH(context, parent, this)
            }
            loadMoreType2 -> {
                TeamLoadMoreVH2(
                    context.inflater.inflate(
                        R.layout.jdme_joywork_list_item_loadmore,
                        parent,
                        false
                    ), this
                )
            }
            loadMoreType -> {
                TeamLoadMoreVH(
                    context.inflater.inflate(
                        R.layout.jdme_joywork_list_item_loadmore,
                        parent,
                        false
                    ), this, groupLoadMoreCallback
                )
            }
            else -> {
                return TeamJoyWorkVH(context, parent, this)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        holder.itemView.visible()
        val data = processor.getData()[position]
        when (holder) {
            is TeamLoadMoreVH -> {
                holder.bind(data as LoadMoreJoyWork)
            }
            is TeamLoadMoreVH2 -> {
                holder.bind(data as ProjectLoadMore2)
            }
            is TeamGroupTextImageVH -> {
                holder.bind(data as JoyWorkTitle)
            }
            is TeamNewGroupVH -> {
                holder.bind(data as ProjectAddTitle)
            }
            is TeamTitleVH -> {
                holder.bind(
                    data as JoyWorkTitle,
                    data.expandableGroup!!.expand
                )
                holder.itemView.setOnClickListener(groupClick)
            }
            is TeamJoyWorkVH -> {
                holder.bind(data as JoyWork)
            }
            else -> {
                holder.itemView.gone()
            }
        }
    }

    private fun toggleGroup(title: JoyWorkTitle) {
        if (title.isInit()) {
            processor.toggleGroup(title)
            notifyDataSetChanged()
        } else {
            //加载更多，然后再 toggleGroup 一次
            groupLoadMoreCallback?.invoke(
                title.group.groupId,
                0
            ) { data: Group?, success ->
                if (success && data != null) {
                    title.group.initialize = true
                    if (data.groupId == title.group.groupId) {
                        title.expandableGroup.ensureRealItems()
                        val items = title.expandableGroup.realItems
                        items.clear()
                        items.addAll(data.tasks)
                        val allLoaded = JoyWorkEx.countJoyWork(data.tasks) < ProjectRepo.PAGE_LIMIT
                        if (!allLoaded) {
                            items.add(ProjectLoadMore(projectId, title.group.groupId))
                        }
                        title.expandableGroup.notifyItemChange()
                    }
                    processor.refreshData()
                    toggleGroup(title)// 再次打开
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return processor.getData().size
    }

    fun updateGroup(group: Group) {
        val data = processor.getData()
        data.forEach {
            if (it is JoyWorkTitle && it.extra == group) {
                it.title = group.safeTitle(context)
            }
        }
        notifyDataSetChanged()
    }

    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        super.onViewAttachedToWindow(holder)
        val itemView = holder.itemView
        if (itemView is ItemContainer && !itemView.fixPos()) {
            itemView.scrollTo(recyclerView.totalDx, 0)
        } else {
            holder.itemView.scrollTo(0, 0)
        }
    }

    fun insert(joyWork: JoyWork, groupIds: List<String>): Boolean {
        if (!groupIds.isLegalList()) {
            return false
        }
        var change = false
        processor.groups.forEach {
            if (groupIds.contains((it.getTitle().extra as? Group)?.groupId)) {
                val clone = joyWork.selfClone()
                clone.expandableGroup = it
                it.getRealItems().add(0, clone)
                change = true
            }
        }
        if (change) {
            processor.refreshData()
            notifyDataSetChanged()
            return true
        }
        return false
    }

    fun refresh() {
        // 刷新整个界面
        ownerFragment.refresh()
    }

    fun moveToGroup(target: Group, joyWork: JoyWork) {
        joyWork.expandableGroup.realItems.remove(joyWork)
        val refreshed = insert(joyWork, listOf(target.groupId))
        if (!refreshed) {
            processor.refreshData()
            notifyDataSetChanged()
        }
    }

    /**
     * 有排序项时加载更多
     */
    fun addSortLoadMore(groups: java.util.ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {

    }

    fun clearAllMountItems() {
        groups.forEach { it ->
            it.clearTmp()
        }
    }
}