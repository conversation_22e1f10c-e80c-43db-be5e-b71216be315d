package com.jd.oa.joywork

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Parcelable
import android.util.TypedValue
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.abilities.api.OpennessApi
import com.jd.oa.business.index.AppUtils
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.Utils2App
import java.io.File
import java.io.Serializable
import java.util.*

/**
 * 一次新建多少子待办
 */
val SUB_JOYWORK_ONETIME = 100

fun File?.isLegalDir(): Boolean {
    return this?.isDirectory.isTrue()
}

fun File?.isLegalFile(): Boolean {
    return this?.isFile.isTrue()
}

/**
 * 当有当调用者为字符串且不为 null，且 trim() 之后不 empty 时返回 true
 */
fun Any?.isLegalString(): Boolean = (this as? String)?.trim()?.isNotEmpty() ?: false

fun Int?.strNum():String{
    if (this == null) {
        return "0"
    }
    return if (this >= 100) "99+" else "$this"
}

fun <K, V> MutableMap<K, V>.getOrNew(key: K, factory: () -> V): V {
    return if (containsKey(key)) {
        this[key]!!
    } else {
        val v = factory()
        this[key] = v
        v
    }
}

fun <T> HashMap<String, ArrayList<T>>.putSafe(key: String, value: T) {
    if (containsKey(key) && this[key] != null) {
        this[key]!!.add(value)
    } else {
        this[key] = ArrayList()
        this[key]!!.add(value)
    }
}

infix fun List<*>?.gt(size: Int): Boolean {
    return (this?.size ?: 0) > size
}

infix fun List<*>?.lt(size: Int): Boolean {
    return (this?.size ?: 0) < size
}

fun <T> List<T>?.safeGet(pos: Int): T? {
    if (this == null)
        return null
    if (pos < 0 || pos >= size) {
        return null
    } else {
        return get(pos)
    }
}

fun <T> MutableList<T>?.addIf(dst: List<T>, predicate: (T) -> Boolean) {
    if (this == null)
        return
    dst.forEach {
        if (predicate(it)) {
            add(it)
        }
    }
}

fun <T> MutableList<T>?.safeAdd(pos: Int, t: T) {
    if (this == null)
        return
    if (pos < 0) {
        add(0, t)
    } else if (pos >= size) {
        add(t)
    } else {
        add(pos, t)
    }
}

fun <T> List<T>.has(filter: (T) -> Boolean): Boolean {
    forEach {
        if (filter(it)) {
            return true
        }
    }
    return false
}

fun <T> List<T>?.sizeOf(filter: (T) -> Boolean): Int {
    var ans = 0
    this?.forEach {
        if (filter(it)) {
            ans++
        }
    }
    return ans
}

fun <T> MutableList<T>.removeAllIf(condition: (T) -> Boolean): ArrayList<T> {
    val ans = ArrayList<T>()
    val i = iterator()
    while (i.hasNext()) {
        val it = i.next()
        if (condition(it)) {
            i.remove()
            ans.add(it)
        }
    }
    return ans
}


/**
 * 是否是合法的时间戳
 */
fun Any?.isLegalTimestamp(): Boolean {
    val t = this as? Long
    return t?.run {
        this > 0
    } ?: false
}

/**
 * @return 当有当调用者为 List 集合，且不为 null，且不为空时返回 true，其余返回 false
 */
fun Any?.isLegalList(): Boolean = (this as? List<*>)?.isNotEmpty() ?: false

fun <A> List<A>.containIf(target: A, compare: (a: A, b: A) -> Boolean): Boolean {
    forEach {
        if (compare(it, target)) {
            return true
        }
    }
    return false
}

fun Any?.isLegalSet(): Boolean = (this as? Set<*>)?.isNotEmpty() ?: false

fun Any?.isLegalLong(): Boolean {
    return try {
        (toString() + "").toLong()
        true
    } catch (e: Exception) {
        false
    }
}

fun List<*>?.hasContent(): Boolean = this?.isNotEmpty() ?: false

/**
 * 将 [src] 合并到调用者中，同时会根据 [id] 进行去重
 * @return true: 调用者发生变化；否则未发生变化
 */
fun <T> MutableList<T>.union(src: List<out T>?, id: (T) -> String): Boolean {
    if (!src.isLegalList()) {
        return false
    }
    val sets = HashSet<String>()
    forEach {
        sets.add(id(it))
    }
    var ans = false
    src!!.forEach {
        if (!sets.contains(id(it))) {
            add(it)
            ans = true
        }
    }
    return ans
}

fun Any?.isLegalMap(): Boolean = (this as? Map<*, *>)?.isNotEmpty() ?: false

fun TextView.string() = text.toString().trim()

fun <T> List<T>.indexAtWithNull(index: Int): T? {
    return if (index >= size || index < 0) {
        null
    } else {
        this[index]
    }
}

inline fun <T> List<T>.indexAtWithNull(index: Int, verify: (T) -> Boolean): T? {
    val ret = indexAtWithNull(index)
    return if (ret == null) ret else if (verify(ret)) ret else null
}

fun String?.defStr(def: String): String {
    return if (isLegalString()) this!! else def
}

/**
 * 按 n 位对齐
 */
fun Int.alignUnsafe(n: Int): Int {
    val t = n - 1
    return ((this + t) and (t.inv()))
}

fun String?.openDeeplink(context: Context) {
    if (!isLegalString())
        return
    var isNeedToken = false
    val uri = Uri.parse(this)
    if (uri.host != null) {
        isNeedToken = uri.host == "auth"
    }
    val appId = uri.getQueryParameter("appId")
    if (isNeedToken && appId.isLegalString()) {
        AppUtils.gainTokenAndGoPlugin(this, appId)
    } else if (this?.isLegalString() == true && this.startsWith(DeepLink.JDME)) {
        Router.build(this).go(context)
    } else {
        // https
        OpennessApi.openUrl(this, false)
    }
}

fun String?.openDeeplink() {
    openDeeplink(Utils2App.getApp())
}

fun TextView.setTextSizeRes(dimenId: Int) {
    setTextSize(TypedValue.COMPLEX_UNIT_PX, context.resources.getDimension(dimenId))
}

fun Context.toSelfInfo(appId: String, erp: String?) {
    erp?.apply {
        AppBase.iAppBase.showContactDetailInfo(this@toSelfInfo, appId, this)
    }
}

fun <E> ArrayList<E>.move(from: Int, to: Int) {
    if (from == to) {
        return
    }
    if (from > to) {
        val obj = removeAt(from)
        add(to, obj)
    } else {// from < to
        val obj = this[from]
        add(to + 1, obj)
        removeAt(from)
    }
}

fun <T> MutableList<T>.removeLast(): T? {
    if (!isLegalList()) {
        return null
    } else {
        return removeAt(size - 1)
    }
}

fun List<String?>.joinLegalString(separator: String = "-"): String {
    return filter {
        it.isLegalString()
    }.joinToString(separator)
}

/**
 * 元素中至少有一个为空(null 或者 "") 时返回 true
 */
fun List<String?>.hasNull(): Boolean {
    forEach {
        if (!it.isLegalString()) {
            return true
        }
    }
    return false
}

fun Context.isZh(): Boolean {
    return resources.getBoolean(R.bool.zh)
}

/**
 * 按需求根据当前时间取今天不同的毫秒值
 */
fun todayTime(): Long {
    val calendar = Calendar.getInstance()[Calendar.HOUR_OF_DAY]
    return when {
        calendar < 16 -> {
            DateUtils.getSpecialHour(System.currentTimeMillis(), 16)
        }

        calendar < 20 -> {
            DateUtils.getSpecialHour(System.currentTimeMillis(), 20)
        }

        else -> {
            DateUtils.getSpecialHour(System.currentTimeMillis(), 23) + 59 * 60 * 1000L
        }
    }
}

fun Int?.safeValue(dv: Int = 0): Int {
    return this ?: dv
}

fun Int?.clamp99(): String {
    return if (this == null || this <= 0) {
        "0"
    } else if (this >= 100) {
        "99+"
    } else {
        "$this"
    }
}

fun Boolean?.isTrue(): Boolean {
    return this == true
}

/**
 * 如果当前值为 Null 或 false 时，返回 true.
 * [NOTE]: The difference between it and [isFalse] is: when the caller is NULL, this function will return true but [isFalse] return false
 */
fun Boolean?.isNotTrue(): Boolean {
    return this == null || this == false
}

fun Boolean?.isFalse(): Boolean {
    return this == false
}

fun AppCompatActivity.hideAction() {
    kotlin.runCatching {
        actionBar?.hide()
    }
    kotlin.runCatching {
        supportActionBar?.hide()
    }
}

fun Handler.clear() {
    removeCallbacksAndMessages(null)
}

fun Activity.getIntentStr(key: String): String? {
    return intent.getStringExtra(key)
}

fun Activity.restoreFrom(
    bundle: Bundle?,
    auxiliary: ((String, ArrayList<*>, Bundle) -> Unit)? = null
) {
    if (bundle == null)
        return
    val i = intent ?: return

    bundle.keySet().forEach {
        when (val v = bundle.get(it)) {
            is Int -> {
                i.putExtra(it, v)
            }

            is Float -> {
                i.putExtra(it, v)
            }

            is Double -> {
                i.putExtra(it, v)
            }

            is String -> {
                i.putExtra(it, v)
            }

            is Boolean -> {
                i.putExtra(it, v)
            }

            is ArrayList<*> -> {
                if (v.isLegalList()) {
                    when (v.first()) {
                        is Parcelable -> {
                            i.putExtra(
                                it,
                                v as ArrayList<out Parcelable>
                            )
                        }

                        is Int -> {
                            i.putExtra(
                                it,
                                v as ArrayList<Int>
                            )
                        }

                        is String -> {
                            i.putExtra(
                                it,
                                v as ArrayList<String>
                            )
                        }

                        is CharSequence -> {
                            i.putExtra(
                                it,
                                v as ArrayList<CharSequence>
                            )
                        }
                    }
                } else {
                    auxiliary?.invoke(it, v, bundle)
                }
            }

            is Serializable -> {
                i.putExtra(it, v)
            }

            is CharSequence -> {
                i.putExtra(it, v)
            }
        }
    }
}

/**
 * 将 Intent 中的所有 extra 都保存到 bundle 中
 */
fun Activity.saveTo(bundle: Bundle, auxiliary: ((String, ArrayList<*>, Bundle) -> Unit)? = null) {
    val i = intent.extras ?: return

    i.keySet().forEach {
        when (val v = i.get(it)) {
            is Int -> {
                bundle.putInt(it, v)
            }

            is Float -> {
                bundle.putFloat(it, v)
            }

            is Double -> {
                bundle.putDouble(it, v)
            }

            is String -> {
                bundle.putString(it, v)
            }

            is Boolean -> {
                bundle.putBoolean(it, v)
            }

            is ArrayList<*> -> {
                if (v.isLegalList()) {
                    when (v.first()) {
                        is Parcelable -> {
                            bundle.putParcelableArrayList(
                                it,
                                v as ArrayList<out Parcelable>
                            )
                        }

                        is Int -> {
                            bundle.putIntegerArrayList(
                                it,
                                v as ArrayList<Int>
                            )
                        }

                        is String -> {
                            bundle.putStringArrayList(
                                it,
                                v as ArrayList<String>
                            )
                        }

                        is CharSequence -> {
                            bundle.putCharSequenceArrayList(
                                it,
                                v as ArrayList<CharSequence>
                            )
                        }
                    }
                } else {
                    auxiliary?.invoke(it, v, bundle)
                }
            }

            is Serializable -> {
                bundle.putSerializable(it, v)
            }

            is CharSequence -> {
                bundle.putCharSequence(it, v)
            }
        }
    }
}
