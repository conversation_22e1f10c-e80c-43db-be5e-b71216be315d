package com.jd.oa.joywork.team

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.abilities.api.OpennessApi
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.ProjectDetail
import com.jd.oa.joywork.bean.ProjectUserList
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.ui.des.JoyWorkDesActivity
import com.jd.oa.joywork.detail.ui.des.JoyWorkDesActivity.Companion.getDescContent
import com.jd.oa.joywork.dialog.thirdparty.CustomAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.CustomConfig
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.support.PageSharingDataManager
import com.jd.oa.joywork.support.ProjectSettingAction
import com.jd.oa.joywork.support.ProjectSettingPageData
import com.jd.oa.joywork.team.bean.ProjectErrorCode
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.utils.HyperLinkHelper
import com.jd.oa.joywork.utils.setUrlLinkText
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener
import com.jd.oa.utils.*
import java.util.*

/**
 * 清单设置
 */
class ProjectSettingActivity : BaseActivity(), View.OnClickListener {

    private var projectId: String? = null
    private var originTitleStr: String? = null
    private var originDescStr: String? = null
    private var permissions: ArrayList<String>? = null
    private var showHeadNumber: Int = 9
    private var mHyperClicked = false

    companion object {
        const val RELATION_REQ = 100
        const val DESC_REQ = 200

        fun getIntent(context: Context, projectId: String, key: Int): Intent {
            val intent = Intent(context, ProjectSettingActivity::class.java)
            val bundle = Bundle()
            bundle.putString("projectId", projectId)
            bundle.putInt("pageDataKey", key)
            intent.putExtras(bundle)
            return intent
        }

        val ProjectSettingActivity.pageData: ProjectSettingPageData?
            get() = if (intent.hasExtra("pageDataKey")) PageSharingDataManager.getData(
                intent.getIntExtra(
                    "pageDataKey",
                    -1
                )
            ) as? ProjectSettingPageData
            else null

    }

    lateinit var tvTitle: TextView
    lateinit var tvNew: TextView
    private val et_project_name: EditText by lazy {
        findViewById(R.id.et_project_name)
    }
    private val ll_members: ViewGroup by lazy {
        findViewById(R.id.ll_members)
    }

    private val recycler_relation: RecyclerView by lazy {
        findViewById(R.id.recycler_relation)
    }
    private val tv_transfer_creator: View by lazy {
        findViewById(R.id.tv_transfer_creator)
    }

    private val tv_exit_team: View by lazy {
        findViewById(R.id.tv_exit_team)
    }
    private val tv_relation_number: TextView by lazy {
        findViewById(R.id.tv_relation_number)
    }
    private val tv_delete: View by lazy {
        findViewById(R.id.tv_delete)
    }
    private val jumpIcon: TextView by lazy {
        findViewById(R.id.icon2)
    }
    private var mDescV: TextView? = null
    lateinit var mAdapter: HeadAdapter
    var memberList = mutableListOf<Members>()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)
        setContentView(R.layout.jdme_activity_project_setting)
        hideAction()
        initView()
        initData()
    }

    private fun initData() {
        getMembers()
        getProjectDetail()
    }

    private fun initView() {
        handleExampleButton()
        mDescV = findViewById(R.id.mDescV)
        mDescV?.setOnClickListener {
            if (mHyperClicked) {
                mHyperClicked = false
                return@setOnClickListener
            }
            if (!hasUpdatePermission()) {
                return@setOnClickListener
            }
            val intent = Intent(it.context, JoyWorkDesActivity::class.java)
            intent.putExtra(JoyWorkDesActivity.KEY, mDescV.pureStr())
            JoyWorkDesActivity.inflateIntent(
                intent,
                R.string.joywork_project_des_tip,
                R.string.joywork_project_des_hint
            )
            startActivityForResult(intent, DESC_REQ)
        }
        showHeadNumber = if (LocaleUtils.getUserSetLocaleStr(this) == "zh_CN") 8 else 5
        if (CommonUtils.getScreentWidth(this) <= 1080) {
            showHeadNumber--
        }
        initActionbar()
        getData()
        et_project_name.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                val it = s?.trim().toString().isNotEmpty()
                tvNew.isEnabled = it
                tvNew.setTextColor(
                    ContextCompat.getColor(
                        this@ProjectSettingActivity,
                        if (it) R.color.red_warn else R.color.color_4bf0250f
                    )
                )
            }
        })
        ll_members.setOnClickListener {
            addRelation()
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CHECKLIST_INFO_MEMBER
                )
            }
        }
        recycler_relation.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        mAdapter = HeadAdapter(this, mutableListOf())
        mAdapter.setOnItemClickListener(object : RecyclerViewItemOnClickListener<Members> {
            override fun onItemClick(view: View?, position: Int, item: Members?) {
                addRelation()
            }

            override fun onItemLongClick(view: View?, position: Int, item: Members?) {
            }
        })
        recycler_relation.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                if (parent.getChildPosition(view) != (memberList.size - 1)) {
                    outRect.right = -32
                }
            }
        }, 0)
        recycler_relation.adapter = mAdapter
        tv_transfer_creator.setOnClickListener(this)
        tv_exit_team.setOnClickListener(this)
        tv_delete.setOnClickListener(this)
        SoftKeyBoardListener.setListener(
            this,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                }

                override fun keyBoardHide(height: Int) {
                    if (!isFinishing) {
                        //如果标题&副标题没有变化则不进行网络请求
                        if (Objects.equals(
                                et_project_name.string(),
                                originTitleStr
                            ) && Objects.equals(mDescV.pureStr(), originDescStr)
                        ) {
                            return
                        }
                        if (originTitleStr?.isEmpty() == true) {
                            return
                        }
                        //如果用户清空清单名称 收起输入法时不更新项目 并设置项目名称为最后一次保存的名字
                        if (et_project_name.text.toString().trim().isEmpty()) {
                            et_project_name.setText(originTitleStr)
                            et_project_name.setCursorEnd()
                        } else {
                            updateProject()
                        }
                    }
                }
            })
    }

    private fun getData() {
        val bundle = intent.extras
        bundle?.apply {
            projectId = getString("projectId").toString()
        }
    }

    private fun setPermission() {
        if (permissions?.contains(ProjectPermissionEnum.ASSIGN.code) == true) {//非创建人
            tv_transfer_creator.visible()
        } else {
            tv_transfer_creator.gone()
        }
        if (permissions?.contains(ProjectPermissionEnum.DELETE.code) == true) {
            tv_delete.visible()
            tv_exit_team.gone()
        } else {
            tv_delete.gone()
            tv_exit_team.visible()
        }
        if (!hasUpdatePermission()) {//无 update 权限不可编辑
            et_project_name.isEnabled = false
            if (originDescStr?.isEmpty() == true) {
                mDescV?.setHint(R.string.joywork_project_des_hint)
            }
        }
        handleExampleButton()
    }

    private fun handleExampleButton() {
        val more = findViewById<View>(R.id.mMore)
        if (!hasUpdatePermission()) {//无 update 权限不可编辑
            more.gone()
        } else {
            more.visible()
            more.setOnClickListener {
                JoyWorkConstant.LIST_EXAMPLE_DEEPLINK.openDeeplink()
            }
        }
    }

    private fun hasUpdatePermission(): Boolean {
        return permissions?.contains(ProjectPermissionEnum.UPDATE.code).isTrue()
    }

    /**
     * 联网获取团队成员列表
     */
    private fun getMembers() {
        ProjectRepo.getProjectUsers(projectId.toString(), object : RepoCallback<ProjectUserList> {
            override fun result(t: ProjectUserList?, errorMsg: String?, success: Boolean) {
                if (success) {
                    if (t != null) {
                        memberList.clear()
                        memberList.addAll(t.users!!)
                        notifyMembersChanged()
                    }
                } else {
                    ToastUtils.showToast(JoyWorkEx.filterErrorMsg(errorMsg))
                }
            }
        })
    }

    /**
     * 获取项目详情
     */
    private fun getProjectDetail() {
        ProjectRepo.getProjectDetail(
            projectId.toString(),
            object : AbsRepoCallback<ProjectDetail>() {

                override fun result(
                    t: ProjectDetail?,
                    errorMsg: String?,
                    success: Boolean,
                    errorCode: Int?
                ) {
                    if (t == null) {
                        ToastUtils.showInfoToast(errorMsg)
                        if (ProjectErrorCode.values()
                                .firstOrNull { it.code == "$errorCode" } != null
                        ) {
                            finish()
                        }
                    } else {
                        t.apply {
                            if (permissions != null) {
                                <EMAIL> =
                                    t.permissions as ArrayList<String>
                                setPermission()
                            }
                            <EMAIL> = title
                            et_project_name.setText(title)
                            et_project_name.setCursorEnd()
                            <EMAIL> = desc
                            setDescV(desc)
                            jumpIcon.isInvisible = t.projectStatus != 1
                            ll_members.isEnabled = t.projectStatus == 1
                        }
                    }
                }
            })
    }

    private val hyperClick = object : HyperLinkHelper.OnClickListener {
        override fun onUrlLinkClick(widget: View, url: String) {
            mHyperClicked = true
            OpennessApi.openUrl(url, false)
        }
    }

    private fun setDescV(ds: String) {
        mDescV?.setUrlLinkText(ds, hyperClick)
    }

    /**
     * 初始化actionBar
     */
    private fun initActionbar() {
        tvTitle = findViewById(R.id.tv_title)
        tvTitle.setText(R.string.joywork_list_setting)
        tvNew = findViewById(R.id.tv_confirm)
        tvNew.gone()
        val close = findViewById<ImageView>(R.id.iv_close)
        close.setImageResource(R.drawable.joywork_leftarrow)
        close.setOnClickListener {
            finish()
        }
    }

    /**
     * 更新清单(项目)
     */
    private fun updateProject(isFinish: Boolean = false) {
        ProjectRepo.updateProject(
            projectId.toString(),
            et_project_name.pureStr(),
            mDescV.pureStr()
        ) { it, msg ->
            if (it) {
                originTitleStr = et_project_name.text.toString().trim()
                pageData?.newTitle = originTitleStr
                originDescStr = mDescV.pureStr()
                pageData?.newDesc = originDescStr
            } else {
                ToastUtils.showInfoToast(msg ?: "")
            }
            if (isFinish) {
                setResult(RESULT_OK)
                super.finish()
            }
        }
    }

    /**
     * 成员列表
     */
    fun addRelation() {
        val activity = this
        if (activity.isFinishing || activity.isDestroyed || memberList.isEmpty() || permissions == null) {
            return
        }
        val intent = Intent(activity, TeamMemberListActivity::class.java)
        OneTimeDataRepo.data = memberList
        intent.putExtra("projectId", projectId)
        intent.putStringArrayListExtra("permissions", permissions)
        activity.startActivityForResult(intent, RELATION_REQ)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == RELATION_REQ && resultCode == RESULT_OK) {
            try {
                (OneTimeDataRepo.data as? ArrayList<Members>)?.apply {
                    <EMAIL> = this
                    notifyMembersChanged()
                }
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        } else if (requestCode == DESC_REQ && resultCode == RESULT_OK && data != null) {
            setDescV(data.getDescContent() ?: "")
            updateProject()
        }
    }

    private fun notifyMembersChanged() {
        var tempList = memberList
        if (memberList.size > showHeadNumber) {
            tempList = memberList.subList(0, showHeadNumber)
        }
        tv_relation_number.text =
            if (LocaleUtils.getUserSetLocaleStr(this) == "zh_CN") "${memberList.size}人" else memberList.size.toString()
        mAdapter.replaceData(tempList)
    }

    inner class HeadAdapter(context: Context, datas: List<Members>) :
        BaseRecyclerViewAdapter<Members>(context, datas) {
        override fun getItemViewType(position: Int): Int {
            return if (memberList.size > showHeadNumber && position == showHeadNumber - 1) 1 else 0
        }

        override fun getItemLayoutId(viewType: Int): Int {
            return if (viewType == 0) R.layout.jdme_head_item else R.layout.jdme_view_head_more
        }

        override fun onConvert(holder: BaseRecyclerViewHolder?, item: Members?, position: Int) {
            if (holder?.itemViewType == 0) {
                JoyWorkViewItem.avatar(holder.getView(R.id.iv_head), item?.headImg)
            }
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            tv_transfer_creator -> transfer()//转让创建人
            tv_exit_team -> quite()//退出
            tv_delete -> delete()//删除
        }
    }

    /**
     * 删除清单
     */
    private fun delete() {
        CustomAlertDialog(this, CustomConfig().apply {
            msgRes = R.string.joywork_project_delete_tip
            okRes = R.string.me_ok
            okShitClick = {
                ProjectRepo.deleteProject(projectId.toString()) { success, msg ->
                    if (success) {
                        pageData?.actions?.add(ProjectSettingAction.EXIT)
                        finish()
                    } else {
                        ToastUtils.showToast(msg)
                    }
                }
            }
        }).show()
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CHECKLIST_INFO_DELETE
            )
        }
    }

    /**
     * 退出清单
     */
    private fun quite() {
        CustomAlertDialog(this, CustomConfig().apply {
            msgRes = R.string.joywork_project_quit_tip
            okRes = R.string.me_ok
            okShitClick = {
                ProjectRepo.quitProject(projectId.toString()) { it, msg ->
                    if (it) {
                        pageData?.actions?.add(ProjectSettingAction.EXIT)
                        finish()
                    } else {
                        ToastUtils.showToast(msg)
                    }
                }
            }
        }).show()
    }

    /**
     * 转让创建人
     */
    private fun transfer() {
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setShowSelf(true).setOptionalFilter(null).setShowOptionalFilter(false).setMaxNum(1)
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds)
        entity.setSpecifyAppId(appIds)
        service.gotoMemberList(
            this,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    if (selected != null) {
                        val membersNet: ArrayList<Members> = ArrayList()
                        for (memberEntityJd in selected) {
                            memberEntityJd?.apply {
                                val member = Members()
                                member.fromDD(memberEntityJd)
                                member.permission = TeamUserRole.EDITOR.permission
//                            member.teamId = JoyWorkUser.DEFAULT_TEAM_ID
                                membersNet.add(member)
                            }
                        }
                        if (membersNet.isEmpty()) {
                            return
                        }
                        CustomAlertDialog(this@ProjectSettingActivity, CustomConfig().apply {
                            msgRes = R.string.joywork_project_transfer_dec
                            okRes = R.string.me_ok
                            okShitClick = {
                                ProjectRepo.transferProject(
                                    projectId.toString(),
                                    membersNet[0]
                                ) { it, msg ->
                                    if (it) {
                                        getProjectDetail()
                                        pageData?.actions?.add(ProjectSettingAction.TRANSFER)
                                        tv_transfer_creator.gone()
                                    } else {
                                        ToastUtils.showInfoToast(msg ?: "")
                                    }
                                }
                            }
                        }).show()
                    }
                }

                override fun onFail() {

                }
            })
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CHECKLIST_INFO_TRANS_OWNER
            )
        }

    }

    override fun finish() {
        if (et_project_name.isFocused && et_project_name.text.toString().trim().isNotEmpty()) {
            updateProject(true)
        } else {
            setResult(Activity.RESULT_OK)
            super.finish()
        }
    }
}