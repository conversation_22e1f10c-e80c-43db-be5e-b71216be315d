package com.jd.oa.joywork.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

@Keep
public class JoyWorkComment implements Cloneable {
    private String commentId;
    private Long updateTime;

    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @NonNull
    @Override
    protected Object clone() throws CloneNotSupportedException {
        JoyWorkComment comment = new JoyWorkComment();
        comment.commentId = this.commentId;
        comment.updateTime = this.updateTime;
        return comment;
    }
}