package com.jd.oa.joywork.detail.data.entity;

/**
 * Auto-generated: 2021-07-08 11:47:43
 */
public class ParentTask {

    private String sourceId;
    private long gmtModified;
    private String bizCode;
    private String parentTaskId;
    private String fromMQ;
    private String remark;
    private String title;
    private String content;
    private String mobileContent;
    private String startTime;
    private int taskStatus;
    private String blockType;
    private long completeTime;
    private int sort;
    private long gmtCreate;
    private int priorityType;
    private String extend;
    private String planTime;
    private long endTime;
    private String sourceName;
    private String projectId;
    private String taskId;

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setGmtModified(long gmtModified) {
        this.gmtModified = gmtModified;
    }

    public long getGmtModified() {
        return gmtModified;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public String getParentTaskId() {
        return parentTaskId;
    }

    public void setFromMQ(String fromMQ) {
        this.fromMQ = fromMQ;
    }

    public String getFromMQ() {
        return fromMQ;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setMobileContent(String mobileContent) {
        this.mobileContent = mobileContent;
    }

    public String getMobileContent() {
        return mobileContent;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setTaskStatus(int taskStatus) {
        this.taskStatus = taskStatus;
    }

    public int getTaskStatus() {
        return taskStatus;
    }

    public void setBlockType(String blockType) {
        this.blockType = blockType;
    }

    public String getBlockType() {
        return blockType;
    }

    public void setCompleteTime(long completeTime) {
        this.completeTime = completeTime;
    }

    public long getCompleteTime() {
        return completeTime;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getSort() {
        return sort;
    }

    public void setGmtCreate(long gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public long getGmtCreate() {
        return gmtCreate;
    }

    public void setPriorityType(int priorityType) {
        this.priorityType = priorityType;
    }

    public int getPriorityType() {
        return priorityType;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public String getExtend() {
        return extend;
    }

    public void setPlanTime(String planTime) {
        this.planTime = planTime;
    }

    public String getPlanTime() {
        return planTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

}