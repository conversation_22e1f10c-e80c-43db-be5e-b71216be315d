package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.joywork.common.UserListFragment
import com.jd.oa.joywork.isLegalList
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * 点赞列表
 */
class LikeListActivity : BaseActivity() {
    companion object {
        fun start(progressId: String, activity: Activity) {
            val i = Intent(activity, LikeListActivity::class.java)
            i.putExtra("progressId", progressId)
            activity.startActivity(i)
        }
    }

    val scope = MainScope()

    private fun getProgressId(): String {
        return intent.getStringExtra("progressId") ?: ""
    }

    private var mFragment: UserListFragment? = null

    private var mTitle: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        kotlin.runCatching {
            actionBar?.hide()
        }
        kotlin.runCatching {
            supportActionBar?.hide()
        }
        setContentView(R.layout.joywork_like_list)
        findViewById<View>(R.id.mBack).setOnClickListener { finish() }
        mTitle = findViewById(R.id.title)
        mFragment = UserListFragment.getJoyWorkUserInstance()
        supportFragmentManager.beginTransaction()
            .add(R.id.mContentView, mFragment!!).commit()
        getLike()
    }

    private fun getLike() {
        scope.launch {
            val i = KpiRepo.listLike(getProgressId())
            i.isSuccess({ _, msg, _ ->
                mFragment?.showEmpty(emptyStr = msg, emptyDrawable = R.drawable.joywork_empty_like)
                mTitle?.text = getString(R.string.joywork_like, 0)
            }) {
                if (it == null || !it.taskPraises.isLegalList()) {
                    mFragment?.showEmpty(emptyStr = R.string.me_no_data, emptyDrawable = R.drawable.joywork_empty_like)
                } else {
                    mFragment?.replaceData(it.taskPraises)
                }
                if (it?.total == null) {
                    mTitle?.text = getString(R.string.joywork_like, 0)
                } else {
                    mTitle?.text = getString(R.string.joywork_like, it.total)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }
}