package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.*
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.fragment.model.WebBean
import com.jd.oa.fragment.web.WebConfig
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.*
import com.jd.oa.joywork.detail.ui.JsTargetInterface
import com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.team.dialog.SelectQDialog
import com.jd.oa.network.httpmanager.GatewayNetEnvironment
import com.jd.oa.network.token.TokenManager
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.joda.time.LocalDateTime
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.HashMap

/**
 * 每周总结
 */
class WeeklySummaryActivity : BaseActivity(), DialogInterface.OnDismissListener {
    companion object {
        fun start(activity: Activity, q: String, qDesc: String) {
            val i = Intent(activity, WeeklySummaryActivity::class.java)
            i.putExtra("q", q)
            i.putExtra("qDesc", qDesc)
            activity.startActivity(i)
        }

        fun openSelDir(context: Context, title: String, gid: String) {
            val prefix = if (GatewayNetEnvironment.getCurrentEnv().isPre) {
                TaskDetailPresenter.JOYSPACE_PRE
            } else {
                TaskDetailPresenter.JOYSPACE_OFFICIAL
            }
            val url =
                    prefix + "/selectors/jdme-folder?appName=jme&x-tenant-code=${PreferenceManager.UserInfo.getTenantCode()}&x-token=${TokenManager.getInstance().accessToken}&lang=${
                        LocaleUtils.getUserSetLocaleStr(
                                context
                        )
                    }&x-client=ANDROID" + if(TextUtils.isEmpty(gid)) "" else "&gid=".plus(gid) + if(TextUtils.isEmpty(title)) "" else "&title=".plus(title)
            val webBean = WebBean()
            webBean.url = url
            webBean.showNav = WebConfig.H5_NATIVE_HEAD_HIDE
            val intent = Intent(context, FunctionActivity::class.java)
            intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean)
            intent.putExtra(
                    FunctionActivity.FLAG_FUNCTION, WebFragment2::class.java.name
            )
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        }
    }

    private val space = JoyWorkSpace()
    private var sp: JoyWorkPreference? = null

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null)
                return
            when (intent.action) {
                JsTargetInterface.SEL_DIR -> {
                    val obj =
                        intent.getSerializableExtra("data") as? HashMap<String, String>
                            ?: return
                    space.name = obj["name"]
                    space.teamId = obj["teamId"]
                    space.folderId = obj["folderId"]
                    setDirName()
                    sp?.put(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_SELECTED, true)
                }
            }
        }
    }

    private val weeks = mutableListOf<Penta<Long, Long, KpiQ, Int, Int>>()

    // 第一位：开始时间；第二位：结束时间；第三位，显示内容
    private var mCurWeek: Penta<Long, Long, KpiQ, Int, Int>? = null

    private fun getQ(): String {
        return intent.getStringExtra("q") ?: ""
    }

    private fun getQDesc(): String {
        return intent.getStringExtra("qDesc") ?: ""
    }

    private var mProgressBar: View? = null
    private var mRv: RecyclerView? = null
    private var mSelDir: TextView? = null
    private var mArrow: TextView? = null
    private var mSure: TextView? = null
    private var mQTitle: TextView? = null
    private var mSelLocation: TextView? = null
    private var mSelectQDialog: SelectQDialog? = null
    private var fromMonday = true
    private val scope = MainScope()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        restoreFrom(savedInstanceState)
        hideAction()
        sp = JoyWorkPreference(this)
        initDir()
        calcWeeks()
        setContentView(R.layout.joywork_weekly_summary)
        findViewById<View>(R.id.mQTitle).setOnClickListener {
            val qs = weeks.map {
                it.third
            }
            mSelectQDialog =
                SelectQDialog(this, qs, qs.indexOf(mCurWeek?.third ?: weeks.first().third))
            mSelectQDialog?.click = { q ->
                mCurWeek = weeks.firstOrNull { triple ->
                    triple.third == q
                } ?: weeks.first()
                setWeekText()
            }
            mSelectQDialog?.show()
            mSelectQDialog?.setOnDismissListener(this@WeeklySummaryActivity)
        }
        findViewById<View>(R.id.mBack).setOnClickListener {
            finish()
        }
        mProgressBar = findViewById(R.id.mProgressBar)
        mSelDir = findViewById(R.id.mSelDir)
        mArrow = findViewById(R.id.mArrow)
        mSure = findViewById(R.id.mSure)
        mSelLocation = findViewById(R.id.mSelLocation)
        mQTitle = findViewById(R.id.mQTitle)
        mRv = findViewById(R.id.mRecyclerView)
        mRv?.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        mRv?.adapter = Adapter(this, mutableListOf()) {
            mSure?.isEnabled = it.isNotEmpty()
        }
        mSelLocation?.setOnClickListener {
            openSelDir(this, "", "")
        }
        mSelDir?.setOnClickListener {
            openSelDir(this, "", "")
        }
        val filter = IntentFilter()
        filter.addAction(JsTargetInterface.SEL_DIR)
        LocalBroadcastManager.getInstance(this).registerReceiver(receiver, filter)
        mSure?.setOnClickListener {
            createSummary()
        }
        refresh()
        setWeekText()
        setDirName()
        checkDirExist()
    }

    /**
     * 验证所选文件是否存在
     */
    private fun checkDirExist() {
        if (!space.folderId.isLegalString() || Objects.equals(
                JoyWorkSpace.PRIVATE_DIR_ID,
                space.folderId
            )
        ) {
            checkSuccess()
            return
        }
        mSelLocation?.setText(R.string.joywork_check_space)
        mSelDir.gone()
        mArrow.gone()
        mProgressBar.visible()
        scope.launch {
            val r = KpiRepo.checkDir(space.folderId, space.teamId)
            r.isSuccess({ _, _, _ ->
                sp?.remove(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_NAME)
                sp?.remove(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_ID)
                sp?.remove(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_TEAM)
                initDir()
            }) {
                // 用返回值更新当前记录
                space.name = it!!.getString("name")
            }
            setDirName()
            checkSuccess()
        }
    }

    private fun checkSuccess() {
        mSelLocation?.setText(R.string.joywork_weekly_location)
        mSelDir.visible()
        mArrow.visible()
        mProgressBar.gone()
    }

    private fun createSummary() {
        if (mProgressBar?.isVisible().isTrue())
            return
        val week = mCurWeek ?: return
        val adapter = mRv?.adapter as? Adapter ?: return
        val gs = mutableListOf<WeeklyReportGoal>()
        adapter.selected.values.forEach {
            gs.add(it)
        }
        if (!gs.isLegalList()) {
            return
        }
        JDMAUtils.clickEvent("", JoyWorkConstant.CREATE_WEEKLY_SUMMARY_SURE, null)
        scope.launch {
            mSure?.isEnabled = false
            val result = KpiRepo.createSummary(space, week, gs, week.fourth, getQ(), getQDesc())
            result.isSuccess({ p1, p2, p3 ->
                result.toastFailure(p1, p2, p3)
                mSure?.isEnabled = true
            }) {
                if (it?.deepLink.isLegalString()) {
                    it?.deepLink.openDeeplink()
                    finish()
                } else {
                    mSure?.isEnabled = true
                }
            }
        }
    }

    private fun initDir() {
        val folderId = sp?.get(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_ID)
        val name = sp?.get(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_NAME)
        val teamId = sp?.get(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_TEAM)
        space.teamId = teamId
        space.folderId = folderId
        space.name = if (name.isLegalString()) name else getString(R.string.joywork_private_dir)
        if (sp?.get(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_SELECTED).isFalse()) {
            space.name = getString(R.string.joywork_private_dir)
        }
    }

    private fun setDirName() {
        mSelDir?.text = space.name
    }

    private fun setWeekText() {
        if (mCurWeek != null) {
            mQTitle?.text = mCurWeek!!.third.assessPeriodDesc
        }
    }

    private fun refresh() {
        scope.launch {
            val result = KpiRepo.listGoalKrByQ(getQ())
            result.isSuccess({ _, msg, _ ->
                showError(JoyWorkEx.filterErrorMsg(msg))
            }) {
                val list = it?.safeKRList
                if (list.isLegalList()) {
                    (mRv?.adapter as? Adapter)?.refresh(list!!)
                } else {
                    showEmpty()
                }
            }
        }
    }


    private fun showError(msg: String) {
        mRv?.adapter = JoyWorkEmptyAdapter.error(this, msg)
    }

    private fun showEmpty() {
        mRv?.adapter = JoyWorkEmptyAdapter.empty(
            this,
            R.string.joywork_kpi_target_empty,
            R.drawable.joywork_target_empty
        )
    }

    private fun calcWeeks() {
        weeks.clear()
        val weekMillis = 7 * 24 * 3600 * 1000L
        val format = SimpleDateFormat(resources.getString(R.string.joywork_plan_time))
        (0..3).forEach {
            val time = System.currentTimeMillis() - it * weekMillis
            val start = getWeekStartTimestamp(time, fromMonday)
            val end = getWeekEndTimestamp(time, fromMonday)
            val ss = format.format(start)
            val es = format.format(end)
            val wc = getWeekNumber(time, fromMonday)
            val q = KpiQ()
            q.assessPeriodDesc = "${wc.first} (${ss} - ${es})"
            q.assessPeriod = q.assessPeriodDesc
            weeks.add(Penta(start, end, q, wc.second, wc.third))
        }
        mCurWeek = weeks.first()
    }

    private fun getWeekStartTimestamp(time: Long, fromMonday: Boolean): Long {
        var date = LocalDateTime(time).withTime(0, 0, 0, 0)
        val delta = if (fromMonday) {
            date.dayOfWeek - 1
        } else {
            if (date.dayOfWeek == 7) 0 else date.dayOfWeek
        }
        date = date.minusDays(delta)
        return date.toDate().time
    }

    private fun getWeekEndTimestamp(time: Long, fromMonday: Boolean): Long {
        var date = LocalDateTime(time).withTime(23, 59, 59, 999)
        val delta = if (fromMonday) {
            7 - date.dayOfWeek
        } else {
            if (date.dayOfWeek == 7) 6 else 6 - date.dayOfWeek
        }
        date = date.plusDays(delta)
        return date.toDate().time
    }

    /**
     * 以周四所在第一周为一年第一周
     * @return first：ui text; second: week of week year; third：week year
     */
    private fun getWeekNumber(time: Long, fromMonday: Boolean): Triple<String, Int, Int> {
        var date = LocalDateTime(time).withTime(23, 59, 59, 999)
        val delta = if (fromMonday) {
            4 - date.dayOfWeek
        } else {
            if (date.dayOfWeek == 7) 4 else 4 - date.dayOfWeek
        }
        date = date.plusDays(delta)
        val localDate = date.toLocalDate()
        return Triple(
            resources.getString(
                R.string.joywork_weekly,
                localDate.weekyear,
                localDate.weekOfWeekyear
            ), localDate.weekOfWeekyear, localDate.weekyear
        )
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        saveTo(outState)
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(receiver)
        scope.cancel()
        // 保存已选位置
        sp?.put(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_TEAM, space.teamId)
        sp?.put(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_ID, space.folderId)
        sp?.put(JoyWorkPreference.KV_ENTITY_WEEKLY_SUMMARY_FOLDER_NAME, space.name)
    }

    override fun onDismiss(dialog: DialogInterface?) {
        mSelectQDialog = null
    }

    /**
     * @param callback: when item's state of selecting had changed, the callback will be called
     */
    class Adapter(
        private val context: Context,
        title: List<KpiTarget>,
        val callback: (HashMap<String, WeeklyReportGoal>) -> Unit
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        val data = ArrayList<Any>()
        val selected = HashMap<String, WeeklyReportGoal>()
        private val padding = CommonUtils.dp2px(context, 42)

        init {
            data.addAll(flatTarget(title))
        }

        private fun flatTarget(ts: List<KpiTarget>): List<Any> {
            val list = mutableListOf<Any>()
            ts.forEach {
                list.add(it)
                if (it.safeKrs.isLegalList()) {
                    list.addAll(it.safeKrs)
                }
            }
            return list
        }

        fun refresh(title: List<KpiTarget>) {
            data.clear()
            selected.clear()
            callback(selected)
            data.addAll(flatTarget(title))
            notifyDataSetChanged()
        }

        class VH(view: View) : RecyclerView.ViewHolder(view) {
            val cb = itemView.findViewById<ImageView>(R.id.cb)
            val mStatus = itemView.findViewById<ImageView>(R.id.mStatus)
            val mLabel = itemView.findViewById<TextView>(R.id.mLabel)
            val mTitle = itemView.findViewById<TextView>(R.id.mTitle)
        }

        class VHKr(view: View) : RecyclerView.ViewHolder(view) {
            val cb = itemView.findViewById<View>(R.id.cb)
            val mStatus = itemView.findViewById<ImageView>(R.id.mStatus)
            val mLabel = itemView.findViewById<TextView>(R.id.mLabel)
            val mTitle = itemView.findViewById<TextView>(R.id.mTitle)
        }

        private val krClick = View.OnClickListener {
            val kr = it.tag as KR
            val goalId = kr.goalId
            // need to distinguish checked and unchecked
            if (selected.containsKey(goalId)) {
                val weeklyReportGoal = selected[goalId]!!
                if (weeklyReportGoal.krIds.contains(kr.krId)) {
                    weeklyReportGoal.krIds.remove(kr.krId)
                } else {
                    weeklyReportGoal.putIfAbsent(kr.krId)
                }
                if (weeklyReportGoal.krIds.isEmpty()) {
                    // If none of goal's kr is checked, then goal should not be checked either
                    selected.remove(goalId)
                }
            } else {
                // When kr is checked, the goal to which it belongs needs to be checked as well
                val g = WeeklyReportGoal(goalId)
                g.putIfAbsent(kr.krId)
                selected[goalId] = g
            }
            callback(selected)
            notifyDataSetChanged()
        }

        private val click = View.OnClickListener {
            val target = it.tag as KpiTarget
            val goalId = target.goalId
            if (selected.containsKey(goalId)) {
                // Is the goal half-checked or fully-checked?
                val weeklyReportGoal = selected[goalId]!!
                if (weeklyReportGoal.krIds.size == target.safeKrs.size) {
                    // fully-checked. We remove this goal and its krs
                    selected.remove(goalId)
                } else {
                    // half-checked. so check all krs of it
                    target.safeKrs.forEach {
                        weeklyReportGoal.putIfAbsent(it.krId)
                    }
                }
            } else {
                val g = WeeklyReportGoal(goalId)
                target.safeKrs.forEach { kr ->
                    g.putIfAbsent(kr.krId)
                }
                selected[goalId] = g
            }
            callback(selected)
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return when (viewType) {
                1 -> {
                    VH(
                        context.inflater.inflate(
                            R.layout.joywork_weekly_summary_item,
                            parent,
                            false
                        )
                    )
                }
                2 -> {
                    VHKr(
                        context.inflater.inflate(
                            R.layout.joywork_weekly_summary_kr_item,
                            parent,
                            false
                        )
                    )
                }
                else -> {
                    PlaceHolderVH(context)
                }
            }
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is VH) {
                val target = data[position] as KpiTarget
                if (selected.contains(target.goalId)) {
                    val weeklyReportGoal = selected[target.goalId]!!
                    if (weeklyReportGoal.krIds.size == target.safeKrs.size) {
                        // fully-checked
                        holder.cb.setImageResource(R.drawable.joywork_cb_selected_normal)
                    } else {// half-checked
                        holder.cb.setImageResource(R.drawable.joywork_cb_part_selected)
                    }
                } else {
                    // no-checked
                    holder.cb.setImageResource(R.drawable.joywork_cb_normal)
                }
                holder.itemView.tag = target
                holder.itemView.setOnClickListener(click)
                holder.mStatus.setImageResource(target.statusRes())
                if (target.getSectionName(holder.itemView.context).isLegalString()) {
                    holder.mLabel.text = target.getSectionName(holder.itemView.context)
                    holder.mLabel.visible()
                } else {
                    holder.mLabel.gone()
                }
                target.withWeight(holder.mTitle)
            } else if (holder is VHKr) {
                val kr = data[position] as KR
                holder.cb.isEnabled = true
                holder.cb.isSelected = selected[kr.goalId]?.krIds?.contains(kr.krId).isTrue()
                holder.itemView.tag = kr
                holder.itemView.setOnClickListener(krClick)
                holder.mStatus.setImageResource(kr.condition.statusRes())
                holder.mLabel.text = context.string(R.string.joywork_kr_tag)
                holder.mTitle.text = kr.content ?: ""
            }
        }

        override fun getItemViewType(position: Int): Int {
            return when (data[position]) {
                is KpiTarget -> {
                    1
                }
                is KR -> {
                    2
                }
                else -> {
                    3
                }
            }
        }

        override fun getItemCount() = data.size
    }
}

/**
 * used to transmission to the server
 */
class WeeklyReportGoal(val goalId: String) {
    val krIds = mutableListOf<String>()

    fun putIfAbsent(krId: String) {
        if (!krIds.contains(krId)) {
            krIds.add(krId)
        }
    }
}