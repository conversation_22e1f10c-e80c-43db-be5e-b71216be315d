package com.jd.oa.joywork.self

import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.utils.*
import com.jd.oa.joywork.R

/**
 * 个人待办 tabs 卡片的
 */
class SelfMainTabAdapter(
    private val ts: ArrayList<Triple<String, String, Boolean>>,
    private val context: Context,
    private val defaultIndex: Int = 0,
    private val selectCallback: (Int) -> Unit
) : RecyclerView.Adapter<SelfMainTabAdapter.VH>() {
    private var selectedIndex: Int = defaultIndex

    private val itemClick = View.OnClickListener {
        val pos = it.getTag(R.id.jdme_tag_id) as Int
        if (pos != selectedIndex) {
            selectCallback.invoke(pos)
            selectedIndex = pos
            notifyDataSetChanged()
        }
    }

    fun replace(newData: List<Triple<String, String, Boolean>>) {
        ts.clear()
        ts.addAll(newData)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(context.inflater.inflate(R.layout.jdme_joywork_self_tab_item, parent, false))
    }

    override fun getItemCount() = ts.size

    override fun onBindViewHolder(holder: VH, position: Int) {
        holder.title.text = ts[position].first
        holder.title.setTag(R.id.jdme_tag_id, position)
        holder.title.setOnClickListener(itemClick)

        if (selectedIndex == position) {
            holder.select()
        } else {
            holder.unselect()
        }
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    inner class VH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val title: TextView = itemView.findViewById(R.id.title)

        fun select() {
            title.setTextColor(Color.parseColor("#FE3E33"))
        }

        fun unselect() {
            title.setTextColor(Color.parseColor("#333333"))
        }
    }
}