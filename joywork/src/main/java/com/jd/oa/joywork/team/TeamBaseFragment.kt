package com.jd.oa.joywork.team

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.ViewModelProviders
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.*
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.detail.ui.TaskDetailActivity
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.repo.LoadingViewStrategy
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.team.bean.*
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.utils.*
import java.util.*

abstract class TeamBaseFragment : BaseFragment(), ICustomField {

    companion object {
        fun getInstance(status: TaskStatusEnum, projectId: String, type: Int): TeamBaseFragment {
            val ret = when (status) {
                TaskStatusEnum.RISK -> TeamRiskFragment()
                TaskStatusEnum.FINISH -> TeamFinishFragment()
                TaskStatusEnum.UN_FINISH -> TeamUnfinishFragment()
                TaskStatusEnum.ALL -> TeamAllFragment()
                else -> TeamDeletedFragment()
            }
            val bundle = Bundle()
            bundle.putString("projectId", projectId)
            bundle.putInt("type", type)
            ret.arguments = bundle
            return ret
        }

        fun TeamBaseFragment.getProjectId(): String {
            return arguments!!.getString("projectId")!!
        }

        fun TeamBaseFragment.getType(): Int {
            return arguments!!.getInt(
                "type",
                JoyWorkProjectList.TYPE_NORMAL
            )
        }

        fun TeamBaseFragment.isNormal(): Boolean {
            return getType() == JoyWorkProjectList.TYPE_NORMAL
        }
    }

    private lateinit var rvAdapter: TeamAdapter
    private lateinit var rv: HRecyclerView
    private lateinit var exContainer: LinearLayout
    private lateinit var mSwipeRefreshLayout: SwipeRefreshLayout

    //    private lateinit var title: String
//    private lateinit var desc: String
    private var initRunnable: Runnable? = null

    private lateinit var teamDetailDrag: TeamDetailDrag

    // 来自于 flutter 的更新
    private val flutterRunnable = Runnable { refreshData(LoadingViewStrategy.HIDE) }

    private var refreshRunnable: Runnable? = null

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (!Objects.equals(intent?.action, ProjectConstant.JOYWORK_SORT)) {
                activity?.setResult(Activity.RESULT_OK)
            }
            refreshRunnable = refreshRunnable ?: Runnable {
                refreshData(LoadingViewStrategy.SHOW)
            }
            if (userVisibleHint) {
                refreshRunnable?.run()
            }
        }
    }

    private lateinit var mModel: TeamDetailViewModel
    private val mFilterObserver = { it: FilterValue ->
        refreshWithNewStatus()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        JoyWorkHandler.getInstance().addCreateObserver(flutterRunnable)
        val view: View = inflater.inflate(R.layout.jdme_joywork_project_detail, container, false)
        mModel = ViewModelProviders.of(requireActivity()).get(TeamDetailViewModel::class.java)
        mSwipeRefreshLayout = view.findViewById(R.id.refresh)
        mSwipeRefreshLayout.setColorSchemeResources(
            ThemeUtils.getAttrsIdValueFromTheme(
                activity,
                R.attr.me_theme_major_color,
                R.color.skin_color_default
            )
        )
        mSwipeRefreshLayout.setOnRefreshListener {
            // 下拉刷新，只写不读
            refreshData(LoadingViewStrategy.SHOW)
        }
        exContainer = view.findViewById(R.id.ex_container)
        rv = view.findViewById<HRecyclerView>(R.id.rv).apply {
            layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        }
        // 设置拖动
        teamDetailDrag = TeamDetailDrag(rv)
        ItemTouchHelper(teamDetailDrag).attachToRecyclerView(rv)

        updateLen(view)
        view.findViewById<View>(R.id.title_container).apply {
            isEnabled = false
        }
        rv.brotherView.add(view.findViewById(R.id.title_container_child))

//        if (lazyInit()) {
//            initRunnable = Runnable {
//                initRunnable = null
//                refreshData(LoadingViewStrategy.SHOW)
//            }
//        } else {
//
//        }
        val filter = IntentFilter()
        getListenerAction().forEach {
            filter.addAction(it)
        }
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(receiver, filter)
        if (getPageStatus() != TaskStatusEnum.DELETED) {
            mModel.filterValueLiveData.observe(this.viewLifecycleOwner, mFilterObserver)
            mModel.sortValueLiveData.observe(this.viewLifecycleOwner) {
                refreshWithNewStatus()
            }
        }
        mModel.uiStatusLiveData.observe(viewLifecycleOwner) {
            // 1. init
            // 2. remove view setting
            if (it.isInited()) {
                refreshData(LoadingViewStrategy.SHOW)
            }
        }
        return view
    }

    private fun refreshWithNewStatus(strategy: LoadingViewStrategy = LoadingViewStrategy.SHOW) {
        val filter = mModel.mUIStatus.mFilter
        refreshData(
            filter,
            mModel.mUIStatus.mSort,
            mModel.mUIStatus.mGroupInnerSort,
            strategy
        )
    }

    fun refresh() {
        refreshData(LoadingViewStrategy.HIDE)
    }

    override fun onResume() {
        super.onResume()
        JDMAUtils.eventPV(
            JoyWorkConstant.PAGE_MAIN,
            null
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        JoyWorkHandler.getInstance().removeCreateObserver(flutterRunnable)
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(receiver)
    }

    private fun refreshData(loadingViewStrategy: LoadingViewStrategy) {
        refreshWithNewStatus(loadingViewStrategy)
    }

    private fun refreshData(
        filter: FilterValue,
        sortValue: SortValue,
        sortType: ProjectSortTypeEnum,
        loadingViewStrategy: LoadingViewStrategy
    ) {
        if (::mSwipeRefreshLayout.isInitialized && loadingViewStrategy.show()) {
            mSwipeRefreshLayout.isRefreshing = true
        }
        refreshContent(filter, sortValue, sortType)
//        ProjectRepo.getProjectExt(getProjectId()) { msg, obj ->
//            if (obj == null) {
//                // 失败
//                if (::mSwipeRefreshLayout.isInitialized) {
//                    mSwipeRefreshLayout.isRefreshing = false
//                }
//                refreshRunnable = null
//                ToastUtils.showInfoToast(msg ?: "")
//            } else {
//
//            }
//        }
    }

    /**
     * 刷新列表
     */
    private fun refreshContent(
        filter: FilterValue,
        sortValue: SortValue,
        sortTypeParam: ProjectSortTypeEnum,
    ) {
        // 当前界面未来得及隐藏，受到别的界面影响
        if (getDefaultStatus() != mModel.mUIStatus.mStatus) {
            return
        }
        val group = mModel.extLiveData.value ?: CustomFieldGroupOuter()
        val sortType = ProjectSortTypeEnum.SORT_OTHER
        if (sortType == ProjectSortTypeEnum.SORT_NO || sortType == ProjectSortTypeEnum.SORT_IN_GROUP || sortValue.code == ProjectSortEnum.SORT_NULL.code) {
            // 没有设置过排序，或者在组内排序，或者排序条件选择无。则按分组显示
            // 不同的排序调用不同的接口
            ProjectRepo.listProjectWork(
                getProjectId(),
                getDefaultStatus().code,
                filter,
                sortValue,
                getType(),
                object : AbsRepoCallback<ProjectGroups>() {
                    override fun result(
                        t: ProjectGroups?,
                        errorMsg: String?,
                        success: Boolean,
                        errorCode: Int?
                    ) {
                        mModel.updateDesc(t?.desc)
                        mModel.updateTitle(t?.title)
                        refreshRunnable = null
                        if (::mSwipeRefreshLayout.isInitialized) {
                            mSwipeRefreshLayout.isRefreshing = false
                        }
                        if (success && t != null) {
                            val groups = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
                            mModel.updatePermissions(t.permissions ?: ArrayList(1))
                            val gs = ArrayList<Group>()
                            gs.addAll(t.safeGroups)
                            mModel.updateGroups(gs)

//                        title = t.title
//                        desc = t.desc
                            t.safeGroups.forEach {
                                if (it.safeStatus != ProjectStatusEnum.DELETED.code) {
                                    it.permissions = t.permissions
                                    //  参数在本界面中无用，随便写
                                    val title = JoyWorkTitle(
                                        R.string.icon_padding_backlog,
                                        it.title,
                                        it.safeTotal,
                                        Color.TRANSPARENT
                                    )
                                    title.extra = it
                                    val items = ArrayList<JoyWork>()
                                    items.addAll(it.safeTasks)
                                    if (it.safeTasks.size < it.safeTotal) {
                                        items.add(ProjectLoadMore(it.projectId, it.groupId))
                                    }
                                    val expandableGroup =
                                        ExpandableGroup(title, items, it.groupId, it.isInit)
                                    groups.add(expandableGroup)
                                }
                            }
                            // 有新建分组权限
                            if ((t.permissions
                                    ?: ArrayList(1)).contains(ProjectPermissionEnum.GROUP.code) && getDefaultStatus() != TaskStatusEnum.DELETED
                            ) {
                                val tmpTitle =
                                    ProjectAddTitle(requireActivity().string(R.string.joywork_group_new_title))
                                val expandableGroup = ExpandableGroup(
                                    tmpTitle,
                                    ArrayList<JoyWork>(),
                                    ProjectConstant.NEW_GROUP_TITLE_ID,
                                    true
                                )
                                groups.add(expandableGroup)
                            }
                            addColTitle(group)
                            when {
                                onlyDefaultGroup(groups) -> {
                                    rv.adapter = JoyWorkEmptyAdapter.empty(requireContext())
                                }
                                ::rvAdapter.isInitialized -> {
                                    rvAdapter.replaceAllGroups(groups)
                                    rvAdapter.titleGroup = group
                                    if (rv.adapter != rvAdapter) {
                                        rv.adapter = rvAdapter
                                    }
                                }
                                else -> {
                                    teamDetailDrag.canDrag = canDrag() && isNormal()
                                    rvAdapter = TeamAdapter(
                                        getProjectId(),
                                        this@TeamBaseFragment,
                                        groups,
                                        requireActivity(),
                                        getPageStatus(),
                                        rv,
                                        group
                                    )
                                    rvAdapter.groupLoadMoreCallback = ::loadMore
                                    rvAdapter.delCallback = ::delGroup
                                    rvAdapter.editCallback = ::editGroup
                                    rv.adapter = rvAdapter
                                }
                            }
                        } else if (!success && ProjectErrorCode.DATA_NOT_AUTH.code == "$errorCode") {
                            ToastUtils.showInfoToast(errorMsg ?: "")
                            onNoPermission()
                        } else {
                            ToastUtils.showInfoToast(errorMsg ?: "")
                            // 访问出错。如果有数据，继续保留
                            if (::rvAdapter.isInitialized) {
                                return
                            }
                            rv.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg ?: "")
                        }
                        onNetworkFinish(success)
                    }
                })
        } else if (sortType == ProjectSortTypeEnum.SORT_OTHER) {
            ProjectRepo.listProjectWorkWithoutGroup(
                0,
                getProjectId(),
                getDefaultStatus().code,
                filter,
                sortValue,
                null,
                object : AbsRepoCallback<ResultWithoutGroup>() {
                    override fun result(
                        t: ResultWithoutGroup?,
                        errorMsg: String?,
                        success: Boolean,
                        errorCode: Int?
                    ) {
                        refreshRunnable = null
                        if (::mSwipeRefreshLayout.isInitialized) {
                            mSwipeRefreshLayout.isRefreshing = false
                        }
                        if (success && t != null) {
                            mModel.updatePermissions(t.permissions ?: ArrayList(1))
                            val groups = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
                            val classifier =
                                DataClassifier.clazz(group, requireContext(), sortValue, t.safeTasks())
                            if (t.safeTasks().size < t.safeTotal()) {
                                classifier.lastOrNull()?.realItems?.add(ProjectLoadMore2())
                            }

                            groups.addAll(classifier)
                            groups.forEach {
                                it.notifyItemChange()
                            }
                            addColTitle(group)
                            when {
                                onlyDefaultGroup(groups) -> {
                                    rv.adapter = JoyWorkEmptyAdapter.empty(requireContext())
                                }
                                ::rvAdapter.isInitialized -> {
                                    rvAdapter.titleGroup = group
                                    if (rv.adapter != rvAdapter) {
                                        rv.adapter = rvAdapter
                                    }
                                    rvAdapter.replaceAllGroups(groups)
                                }
                                else -> {
                                    rvAdapter = TeamAdapter(
                                        getProjectId(),
                                        this@TeamBaseFragment,
                                        groups,
                                        requireActivity(),
                                        getPageStatus(),
                                        rv,
                                        group
                                    )
                                    rvAdapter.groupLoadMoreCallback = ::loadMore
                                    rvAdapter.delCallback = ::delGroup
                                    rvAdapter.editCallback = ::editGroup
                                    rv.adapter = rvAdapter
                                }
                            }
                        } else if (!success && ProjectErrorCode.DATA_NOT_AUTH.code == "$errorCode") {
                            ToastUtils.showInfoToast(errorMsg ?: "")
                            onNoPermission()
                        } else {
                            ToastUtils.showInfoToast(errorMsg ?: "")
                            // 访问出错。如果有数据，继续保留
                            if (::rvAdapter.isInitialized) {
                                return
                            }
                            rv.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg ?: "")
                        }
                        onNetworkFinish(success)
                    }
                })
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (::rvAdapter.isInitialized) {
            rvAdapter.notifyDataSetChanged()
            addColTitle(rvAdapter.titleGroup)
            updateLen(view)
        }
    }

    private fun updateLen(view: View?) {
        view?.findViewById<View>(R.id.name)?.apply {
            layoutParams.width = CommonUtils.getScreentWidth(requireActivity()) * 4 / 5 + CommonUtils.dp2px(26.0f)
        }
    }

    private lateinit var customGroup: CustomFieldGroupOuter

    private fun addColTitle(group: CustomFieldGroupOuter) {
        customGroup = group
        exContainer.removeAllViews()
        if (group.customFields.hasContent()) {
            exContainer.visible()
            group.customFields.forEach {
                val item = requireActivity().layoutInflater.inflate(
                    R.layout.jdme_joywork_project_detail_title_item,
                    exContainer,
                    false
                )
                val nameV = item.findViewById<TextView>(R.id.col_name)
                nameV.text = it.title ?: ""
                exContainer.addView(item)
            }
        } else {
            exContainer.gone()
        }
    }

    override fun getCustomField(): List<CustomFieldGroup>? {
        return if (::customGroup.isInitialized) {
            customGroup.customFields
        } else {
            null
        }
    }

    protected open fun onNetworkFinish(success: Boolean) {

    }

    private fun onNoPermission() {
        activity?.finish()
    }

    private fun onlyDefaultGroup(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>): Boolean {
        if (groups.isEmpty())
            return true
        if (groups.size == 1) {
            val first = groups.first()
            if (first.title is ProjectAddTitle) {
                return false
            } else if (first.title is JoyWorkTitle) {
                return ((first.title.extra as? Group)?.isDefaultGroup
                    ?: true) && !first.realItems.isLegalList()
            }
        }
        return false
    }

    fun delGroup(hard: Boolean, group: Group) {
        ProjectRepo.delGroup(getProjectId(), group.groupId, hard) { it, msg ->
            if (it) {
                refreshData(LoadingViewStrategy.SHOW)
            } else {
                ToastUtils.showInfoToast(msg ?: "")
            }
        }
    }

    fun loadMore(groupId: String, count: Int, callback: (Group?, Boolean) -> Unit) {
        if (!::rvAdapter.isInitialized) {
            return
        }

        val tmpS = mModel.mUIStatus.mSort
        ProjectRepo.getGroupTasks(
            getProjectId(),
            groupId,
            getDefaultStatus().code,
            count,
            getType(),
            getFilter(),
            tmpS,
            object : RepoCallback<Group> {
                override fun result(t: Group?, errorMsg: String?, success: Boolean) {
                    if (success) {
                        callback.invoke(t!!, true)
                    } else {
                        callback.invoke(null, false)
                    }
                }
            })
    }

    fun loadMore(
        offset: Int,
        callback: (group: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>?, success: Boolean, finish: Boolean) -> Unit
    ) {
        if (!::rvAdapter.isInitialized) {
            return
        }
        val tmpS = mModel.mUIStatus.mSort
        ProjectRepo.listProjectWorkWithoutGroup(
            offset,
            getProjectId(),
            getDefaultStatus().code,
            getFilter(),
            tmpS,
            null,
            object : AbsRepoCallback<ResultWithoutGroup>() {
                override fun result(
                    t: ResultWithoutGroup?,
                    errorMsg: String?,
                    success: Boolean,
                    errorCode: Int?
                ) {
                    if (success && t != null) {
                        val groups = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
                        val group = mModel.extLiveData.value ?: CustomFieldGroupOuter()
                        val classifier =
                            DataClassifier.clazz(group, requireContext(), tmpS, t.safeTasks())
                        groups.addAll(classifier)
                        callback.invoke(
                            groups,
                            true,
                            JoyWorkEx.countJoyWork(t.safeTasks()) < ProjectRepo.PAGE_LIMIT
                        )
                    } else if (!success && ProjectErrorCode.DATA_NOT_AUTH.code == "$errorCode") {
                        callback.invoke(null, false, false)
                        onNoPermission()
                    } else {
                        callback.invoke(null, false, false)
                    }
                }
            })
    }

    private fun getFilter(): FilterValue {
        return mModel.mUIStatus.mFilter
    }

    fun editGroup(title: String, group: Group) {
        if (!::rvAdapter.isInitialized) {
            return
        }
        ProjectRepo.updateGroup(group.groupId, title) { success: Boolean, msg: String? ->
            if (success) {
                group.title = title
                group.type = ProjectGroupTypeEnum.COMMON.code
                rvAdapter.updateGroup(group)
            } else {
                Toast.makeText(requireActivity(), JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                    .show()
            }
        }
    }


    fun insert(joyWork: JoyWork, groupIds: List<String>) {
        if (!::rvAdapter.isInitialized) {
            return
        }
        rvAdapter.insert(joyWork, groupIds)
    }

    override fun onClick(v: View) {
    }

    /**
     * The return value will be used as a parameter of the network
     */
    abstract fun getDefaultStatus(): TaskStatusEnum

    /**
     * Identify the page
     */
    abstract fun getPageStatus(): TaskStatusEnum

    open fun lazyInit(): Boolean {
        return false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && data != null && data.hasExtra(TaskDetailActivity.KEY_BIZ_OBJ)) {
            val parcel: DetailReturnParcel =
                data.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as DetailReturnParcel
            // 没有更新
            if (!parcel.update) {
                return
            }
            when (data.getStringExtra(TaskDetailActivity.KEY_BIZ_FROM)) {
                JoyWorkConstant.BIZ_DETAIL_FROM_LIST -> {
                    ProjectConstant.sendBroadcast(requireActivity(), ProjectConstant.REFRESH_ACTION)
                }
            }
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        initRunnable?.apply {
            run()
        } ?: refreshRunnable?.run()
    }

    /**
     * 切换 tab 时调用该方法进行刷新界面
     */
    fun onPageSelected() {
        refreshRunnable = Runnable {
            refreshData(LoadingViewStrategy.HIDE)
        }
        refreshRunnable?.run()
    }

    open fun getListenerAction(): ArrayList<String> {
        return arrayListOf(
            ProjectConstant.DEL_ACTION,
            ProjectConstant.REVERSE_ACTION,
            ProjectConstant.REFRESH_ACTION,
            ProjectConstant.JOYWORK_SORT
        )
    }

    /**
     * 是否可拖动
     */
    open fun canDrag(): Boolean {
        return true
    }

    /**
     * 是否显示右下角的加号
     */
    open fun showAddButton(): Boolean {
        return true
    }

    /**
     * 右上角是否可出现筛选
     */
    open fun settingCanFilter(): Boolean {
        return false
    }

    open fun getExtraSortAction(): List<ProjectSortAction> {
        return emptyList()
    }

    // 新建
    fun addNewGroup(resultGroup: Group, index: Int) {
        mModel?.insertNewGroup(resultGroup, index)
    }
}