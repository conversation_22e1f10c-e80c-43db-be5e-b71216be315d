package com.jd.oa.joywork.team.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.jd.oa.joywork.R;

public class DividerLinearLayout extends LinearLayout {

    private boolean mShowTop;
    private boolean mShowBottom;
    private int mAnchorId;

    public DividerLinearLayout(Context context) {
        super(context);
    }

    public DividerLinearLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DividerLinearLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.DividerLinearLayout);
        mShowBottom = array.getBoolean(R.styleable.DividerLinearLayout_dividerBottom, true);
        mShowTop = array.getBoolean(R.styleable.DividerLinearLayout_dividerTop, false);
        mAnchorId = array.getResourceId(R.styleable.DividerLinearLayout_dividerStartId, R.id.title);
        array.recycle();
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        super.dispatchDraw(canvas);
        if (!showBottom) {
            return;
        }
        View child2 = findViewById(mAnchorId);
        int save = canvas.save();
        if (mShowBottom) {
            canvas.clipRect(new Rect(totalLeft(child2), getHeight() - getResources().getDimensionPixelOffset(R.dimen.joywork_border), getWidth(), getHeight()));
            canvas.drawColor(getContext().getResources().getColor(R.color.joywork_divider));
        }
        canvas.restoreToCount(save);
        save = canvas.save();
        if (mShowTop) {
            canvas.clipRect(new Rect(totalLeft(child2), 0, getWidth(), getResources().getDimensionPixelOffset(R.dimen.joywork_border)));
            canvas.drawColor(getContext().getResources().getColor(R.color.joywork_divider));
        }
        canvas.restoreToCount(save);
    }

    private int totalLeft(View child) {
        int total = child.getLeft();
        if (child == this)
            return total;
        ViewGroup parent = (ViewGroup) child.getParent();
        while (parent != this) {
            total += parent.getLeft();
            parent = (ViewGroup) parent.getParent();
        }
        return total;
    }

    public void setShowTop(boolean showTop) {
        if (mShowTop != showTop) {
            mShowTop = showTop;
            invalidate();
        }
    }

    private boolean showBottom = true;

    public void showBottom(boolean show) {
        if (show != showBottom) {
            showBottom = show;
            invalidate();
        }
    }

    public void setShowBottom(boolean showBottom) {
        if (mShowBottom != showBottom) {
            mShowBottom = showBottom;
            invalidate();
        }
    }
}
