package com.jd.oa.joywork.self

import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.FilterPageIdFactory
import com.jd.oa.joywork.self.base.SelfListBaseAdapter
import com.jd.oa.joywork.self.base.SelfListBaseFragment
import com.jd.oa.joywork.self.base.SelfListCreateAdapter
import com.jd.oa.joywork.team.bean.SelfListSortType
import com.jd.oa.joywork.team.bean.SelfListViewType
import com.jd.oa.utils.JDMAUtils

class MyCreateFragment : SelfListBaseFragment(), SelfBaseItf {

    override fun getPagePVName(): String? {
        return null
    }

    override fun getUserRole(): TaskUserRole {
        return TaskUserRole.CREATOR
    }

    override fun onPageUnselected() {
    }

    override fun onPageSelected() {
    }

    override fun onShowAtTabbar() {
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return TaskListTypeEnum.ASSIGN
    }

    override fun handleSortActions(list: MutableList<ProjectSortAction>) {
        list.add(0, ProjectSortOwner(requireContext()))
    }

    override fun getPageId(): String {
        return "MyCreate"
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isRealVisible()) {
            getFilterSortStatusViewModel().notifyUnableFilterSort(true)
        }
    }

    override fun getViewType(): Int {
        return SelfListViewType.MY_CREATE.code
    }
    override fun getSortType(): Int {
        return SelfListSortType.MY_CREATE.code
    }

    override fun isFilterSortUnable(): Boolean {
        return true
    }
}