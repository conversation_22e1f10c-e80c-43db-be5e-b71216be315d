package com.jd.oa.joywork.team.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;

public class ProjectList {

    @SerializedName("errorCode")
    private String errorCode;
    @SerializedName("content")
    private Content content;
    @SerializedName("errorMsg")
    private String errorMsg;

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Content getContent() {
        return content;
    }

    public void setContent(Content content) {
        this.content = content;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public static class Content {
        @SerializedName("pageSize")
        private int pageSize;
        @SerializedName("total")
        private int total;
        @SerializedName("page")
        private int page;
        @SerializedName("list")
        private java.util.List<List> list;

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public java.util.List<List> getList() {
            return list;
        }

        public void setList(java.util.List<List> list) {
            this.list = list;
        }

        public static class List {
            @SerializedName("permissions")
            private java.util.List<String> permissions;
            @SerializedName("groups")
            private ArrayList<Groups> groups;
            @SerializedName("title")
            private String title;
            @SerializedName("projectId")
            private String projectId;
            @SerializedName("desc")
            private String desc;
            @SerializedName("status")
            private int status;

            public java.util.List<String> getPermissions() {
                return permissions;
            }

            public void setPermissions(java.util.List<String> permissions) {
                this.permissions = permissions;
            }

            public ArrayList<Groups> getGroups() {
                return groups;
            }

            public void setGroups(ArrayList<Groups> groups) {
                this.groups = groups;
            }

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getProjectId() {
                return projectId;
            }

            public void setProjectId(String projectId) {
                this.projectId = projectId;
            }

            public String getDesc() {
                return desc;
            }

            public void setDesc(String desc) {
                this.desc = desc;
            }

            public int getStatus() {
                return status;
            }

            public void setStatus(int status) {
                this.status = status;
            }

            public static class Groups implements Parcelable {
                @SerializedName("groupId")
                private String groupId;
                @SerializedName("title")
                private String title;
                @SerializedName("type")
                private int type;
                @SerializedName("projectId")
                private String projectId;
                @SerializedName("status")
                private int status;

                protected Groups(Parcel in) {
                    groupId = in.readString();
                    title = in.readString();
                    type = in.readInt();
                    projectId = in.readString();
                    status = in.readInt();
                }

                public static final Creator<Groups> CREATOR = new Creator<Groups>() {
                    @Override
                    public Groups createFromParcel(Parcel in) {
                        return new Groups(in);
                    }

                    @Override
                    public Groups[] newArray(int size) {
                        return new Groups[size];
                    }
                };

                public String getGroupId() {
                    return groupId;
                }

                public void setGroupId(String groupId) {
                    this.groupId = groupId;
                }

                public String getTitle() {
                    return title;
                }

                public void setTitle(String title) {
                    this.title = title;
                }

                public int getType() {
                    return type;
                }

                public void setType(int type) {
                    this.type = type;
                }

                public String getProjectId() {
                    return projectId;
                }

                public void setProjectId(String projectId) {
                    this.projectId = projectId;
                }

                public int getStatus() {
                    return status;
                }

                public void setStatus(int status) {
                    this.status = status;
                }

                @Override
                public int describeContents() {
                    return 0;
                }

                @Override
                public void writeToParcel(Parcel dest, int flags) {
                    dest.writeString(groupId);
                    dest.writeString(title);
                    dest.writeInt(type);
                    dest.writeString(projectId);
                    dest.writeInt(status);
                }
            }
        }
    }
}
