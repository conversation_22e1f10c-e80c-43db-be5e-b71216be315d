package com.jd.oa.joywork.detail.data.entity.custom;

import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.team.bean.ProjectFilterEnum;

import java.util.HashMap;
import java.util.Objects;

public class ProjectFilterExt {
    public ProjectFilterEnum projectFilterEnum;

    // 清单自定义字段时字段 SCREEN_CUSTOM
    public String columnId;
    public String detailId;
    public String label;
    // 优先级时 SCREEN_PRIORITY
    public Integer priority;
    // 截止时间 SCREEN_EDN_TIME
    public Long endTime;
    public Long startTime;
    // 执行人时 SCREEN_EXECUTOR
    public String ddAppId;
    public String emplAccount;
    public String realName;
    // 所属清单
    public String title;
    public String projectId;

    public boolean isEquals(ProjectFilterExt other) {
        if (this == other) {
            return true;
        }
        if (this.projectFilterEnum != other.projectFilterEnum) {
            return false;
        }
        if (this.projectFilterEnum == null) {
            return true;
        }
        switch (projectFilterEnum) {
            case SCREEN_PRIORITY:
                return Objects.equals(this.priority, other.priority);
            case SCREEN_EDN_TIME:
                return Objects.equals(this.startTime, other.startTime) && Objects.equals(this.endTime, other.endTime);
            case SCREEN_CUSTOM:
                return Objects.equals(this.columnId, other.columnId) && Objects.equals(this.detailId, other.detailId);
            case SCREEN_EXECUTOR:
                return Objects.equals(this.ddAppId, other.ddAppId) && Objects.equals(this.emplAccount, other.emplAccount);
            case SCREEN_PROJECT:
                return Objects.equals(this.projectId, other.projectId);
            default:
                return true;
        }
    }

    public void toMap(HashMap<String, Object> map) {
        if (this.projectFilterEnum == null) {
            return;
        }
        switch (projectFilterEnum) {
            case SCREEN_PRIORITY:
                map.put("priority", priority);
            case SCREEN_EDN_TIME:
                if (ObjExKt.isLegalTimestamp(startTime)) {
                    map.put("startTime", startTime);
                }
                if (ObjExKt.isLegalTimestamp(endTime)) {
                    map.put("endTime", endTime);
                }
            case SCREEN_CUSTOM:
                map.put("columnId", columnId);
                map.put("detailId", detailId);
                map.put("label", label);
            case SCREEN_EXECUTOR:
                map.put("realName", realName);
                map.put("ddAppId", ddAppId);
                map.put("emplAccount", emplAccount);
            case SCREEN_PROJECT:
                map.put("title", title == null ? "" : title);
                map.put("projectId", projectId);
        }
    }
}
