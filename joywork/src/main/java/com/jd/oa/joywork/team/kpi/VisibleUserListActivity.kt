package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.common.IUserListItemCallback
import com.jd.oa.joywork.common.UserListFragment
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.utils.JoyWorkLiveDataRepo
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.argb
import com.jd.oa.utils.visible

class VisibleUserListActivity : BaseActivity() {
    companion object {
        fun start(token: String, activity: Activity) {
            val i = Intent(activity, VisibleUserListActivity::class.java)
            i.putExtra("token", token)
            activity.startActivity(i)
        }
    }

    private fun getViewModelToken(): String {
        return intent.getStringExtra("token") ?: ""
    }

    private var mFragment: UserListFragment? = null
    private var liveData: VisibleUserLiveData? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        setContentView(R.layout.joywork_visible_user_list)
        findViewById<View>(R.id.mBack).setOnClickListener { finish() }
        mFragment = UserListFragment.getJoyWorkUserInstance()
        mFragment?.mItemCallback = object : IUserListItemCallback() {
            override fun bindItemAction(tv: TextView) {
                tv.visible()
                tv.setText(R.string.icon_prompt_close)
                tv.argb("#FF999999")
                tv.setOnClickListener {
                    delJoyWorkUser(it.tag as JoyWorkUser)
                }
            }
        }
        supportFragmentManager.beginTransaction()
            .add(R.id.mContentView, mFragment!!).commit()
        liveData = JoyWorkLiveDataRepo.get(getViewModelToken()) as? VisibleUserLiveData
        liveData?.addUser(this)
        liveData?.visibleUserLiveData?.observe(this) {
            if (!it.isLegalList()) {
                mFragment?.showEmpty(
                    emptyStr = R.string.me_no_data,
                    emptyDrawable = R.drawable.joywork_empty_like
                )
            } else {
                mFragment?.replaceData(it)
            }
        }
        findViewById<View>(R.id.mAdd).setOnClickListener {
            val jds = ArrayList<MemberEntityJd>()
            liveData?.visibleUserLiveData?.value?.forEach { user ->
                val jd = MemberEntityJd()
                jd.fromUser(user)
                jds.add(jd)
            }
            ExecutorUtils.selectVisibleUsers(this, jds, { selected: ArrayList<MemberEntityJd?>? ->
                val newSelected = mutableListOf<JoyWorkUser>()
                selected?.forEach {
                    if (it != null) {
                        val user = JoyWorkUser()
                        user.fromDD(it)
                        newSelected.add(user)
                    }
                }
                liveData?.frontUsers(newSelected)
                mFragment?.replaceData(liveData?.visibleUserLiveData?.value ?: mutableListOf())
            }) {

            }
        }
    }

    private fun delJoyWorkUser(joyWorkUser: JoyWorkUser) {
        liveData?.removeUser(joyWorkUser)
    }

    override fun onDestroy() {
        super.onDestroy()
        liveData?.removeUser(this)
    }
}