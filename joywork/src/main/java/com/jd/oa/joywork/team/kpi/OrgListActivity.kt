package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.team.bean.OrgList
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.string
import com.jd.oa.utils.visible
import kotlinx.android.synthetic.main.jdme_joywork_org_list.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * 上下级、关注列表
 */
class OrgListActivity : BaseActivity() {

    companion object {
        fun start(context: Activity) {
            val bundle = Intent(context, OrgListActivity::class.java)
            context.startActivity(bundle)
        }
    }

    private val items = ArrayList<JoyWorkTabItem<Pair<String, Int>, OrgListFragment>>()
    private val scope = MainScope()
    private var mInitIndex = 0
    private var mViewModel: OrgListViewModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        setContentView(R.layout.jdme_joywork_org_list)
        mViewModel = ViewModelProviders.of(this).get(OrgListViewModel::class.java)
        getData()
        back.setOnClickListener {
            finish()
        }
        mSearch.setOnClickListener {
            ExecutorUtils.searchOther(this, { result ->
                if (result.isLegalList() && result!!.size >= 1) {
                    val user = JoyWorkUser()
                    user.fromDD(result[0]!!)
                    if (!user.isSelf()) {
                        GoalListActivity.startOther(this, user)
                    } else {
                        finish()
                    }
                }
            }) {}
        }
    }

    private fun getData() {
        scope.launch {
            mLoadingViewContainer.visible()
            // 这里只拉取考勤周期，每个周期中的待办由各 frg 自己负责拉取
            val result = KpiRepo.listOrg(OrgList.ALL)
            mLoadingViewContainer.gone()
            result.isSuccess({ _, msg, _ ->
                showError(msg)
            }) {
                if (it == null || (!it.sups.isLegalList() && !it.subs.isLegalList())) {
                    showError(string(R.string.me_no_data))
                } else {
                    mTabsRecyclerView.visible()
                    items.clear()
                    if (it.sups.isLegalList()) {
                        val i = JoyWorkTabItem(
                            Pair(string(R.string.joywork_org_list_super), OrgList.SUPER),
                            clickId = null,
                            OrgListFragment.getInstance(OrgList.SUPER)
                        )
                        mViewModel?.appendSup(it.sups)
                        items.add(i)
                    }
                    if (it.subs.isLegalList()) {
                        val i = JoyWorkTabItem(
                            Pair(string(R.string.joywork_org_list_sub), OrgList.SUB),
                            clickId = null,
                            OrgListFragment.getInstance(OrgList.SUB)
                        )
                        mViewModel?.appendSub(it.subs)
                        items.add(i)
                    }
                    val i = JoyWorkTabItem(
                        Pair(string(R.string.joywork_task_cooperator), OrgList.FOLLOWED),
                        clickId = null,
                        OrgListFragment.getInstance(OrgList.FOLLOWED)
                    )
                    items.add(i)
                    handleTabs()
                    handleViewPager()
                }
            }
        }
    }

    private fun showError(msg: String) {
        mTabsRecyclerView.gone()
        viewpager.gone()
        val errorView = inflater.inflate(R.layout.me_joywork_empty, mRoot, true)
        errorView.findViewById<TextView>(R.id.empty_tips).text = msg
        errorView.findViewById<ImageView>(R.id.icon)
            .setImageResource(R.drawable.joywork_target_empty)
    }

    private fun handleViewPager() {
        viewpager.visible()
        viewpager.adapter = KpiAppFragmentAdapter(supportFragmentManager, items)
        viewpager.offscreenPageLimit = items.size
    }

    private fun handleTabs() {
        mTabsRecyclerView.visible()
        mTabsRecyclerView.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        val map: List<Pair<String, Int>> = items.map {
            it.content
        }
        mTabsRecyclerView.adapter = TabsAdapter(this, mInitIndex, map) { it, _ ->
            viewpager.currentItem = it
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }
}