package com.jd.oa.joywork.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.Tag
import com.jd.oa.joywork2.list.calendar.view.setLabelTextViewParam
import com.jd.oa.ui.recycler.UIContants

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/26
 * @Description:
 */
class TagFloatMenuAdapter(
    private val context: Context,
    private val items: MutableList<Tag>
) : RecyclerView.Adapter<TagFloatMenuAdapter.TagMenuVH>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TagMenuVH {
        val view: View =
            LayoutInflater.from(context).inflate(
                R.layout.joywork_tag_float_menu_item,
                parent, false
            )
        return TagMenuVH(view)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: TagMenuVH, position: Int) {
        val item = items[position]
        holder.labelName.setLabelTextViewParam(item, true)
    }

    override fun getItemViewType(position: Int): Int {
        // 保证在使用 DivideItem 时 footer item 下方不会添加分割线
        if (position == items.size - 1) {
            return UIContants.ITEM_TYPE_VIEW_FOOTER
        }
        return UIContants.ITEM_TYPE_NORMAL.toInt()
    }

    inner class TagMenuVH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var labelName: TextView = itemView.findViewById(R.id.label_name)
    }
}





