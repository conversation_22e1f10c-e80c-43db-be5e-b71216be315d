package com.jd.oa.joywork.team;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.appcompat.widget.SwitchCompat;

import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.joywork.ProjectSortAction;
import com.jd.oa.joywork.ProjectSortCustom;
import com.jd.oa.joywork.ProjectSortCustomGroup;
import com.jd.oa.joywork.ProjectSortTitle;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.detail.data.entity.SortValue;
import com.jd.oa.joywork.dialog.thirdparty.ListShitDialogAdapter;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ViewUtilsKt;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

import kotlin.jvm.functions.Function3;

public class ProjectDialogHelper extends ListShitDialogAdapter<ProjectSortAction> implements DialogInterface.OnDismissListener {
    private final Context context;

    private ProjectSortAction sortAction;
    private boolean selected;
    private final SortValue curA;
    private final Function3<ProjectSortAction, Boolean, Boolean, String> function1;

    // 记录用户是否手动修改过内容
    private boolean hasChanged = false;

    public ProjectDialogHelper(@NotNull List<ProjectSortAction> items, Context context, boolean selected, SortValue curA, Function3<ProjectSortAction, Boolean, Boolean, String> function1) {
        super(items);
        this.context = context;
        sortAction = new ProjectSortCustomGroup(context);
        this.selected = selected;
        this.curA = curA;
        this.function1 = function1;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        function1.invoke(sortAction, selected, hasChanged);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (getDialog() != null) {
            getDialog().setOnDismissListener(this);
        }
        ProjectSortAction p = getItems().get(position);
        View view;
        if (p instanceof ProjectSortTitle) {
            view = ((LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.jdme_dialog_project_option_sort_item, parent, false);
            SwitchCompat mSwitchCompat = view.findViewById(R.id.switch_in_group);
            mSwitchCompat.setChecked(selected);
            mSwitchCompat.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    hasChanged = true;
                    JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_SORT_GROUP, JoyWorkConstant.JOYWORK_SORT_GROUP);
                    selected = isChecked;
                    function1.invoke(sortAction, selected, hasChanged);
                }
            });
        } else {
            Map<Integer, View> map = ProjectDialogItemKt.bindItem(p, parent, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    sortAction = (ProjectSortAction) v.getTag();
                    hasChanged = true;
                    if (getDialog() != null) {
                        getDialog().dismiss();
                    }
                }
            });
            if (curA.isSameAction(p)) {
                sortAction = p;
                if (p instanceof ProjectSortCustom && !((ProjectSortCustom) p).getValue().equals(curA.getValue() == null ? null : curA.getValue().columnId)) {
                    // 自定义字段，但不是同一个自定义字段
                    map.get(R.id.icon).setVisibility(View.GONE);
                    ViewUtilsKt.normal((TextView) map.get(R.id.title));
                } else {
                    map.get(R.id.icon).setVisibility(View.VISIBLE);
                    ViewUtilsKt.bold((TextView) map.get(R.id.title));
                }
            } else {
                map.get(R.id.icon).setVisibility(View.GONE);
                ViewUtilsKt.normal((TextView) map.get(R.id.title));
            }
            view = map.get(R.id.key);
        }
        return view;
    }
}
