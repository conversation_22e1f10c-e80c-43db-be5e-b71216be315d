package com.jd.oa.joywork.notification

import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.joywork.CoroutineResult
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.isLegalString
import com.jd.oa.network.SimpleReqCallbackAdapter
import com.jd.oa.network.httpmanager.HttpManager
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject

object NotifyRepo {
    val limit = 50

    // pull to refresh
    // init
    suspend fun listNotification(): CoroutineResult<JoyWorkNotificationOuter> {
        return listNotificationInner(limit = limit, offset = 0)
    }

    // pull up to load more
    suspend fun loadMoreDownNotification(
        lastTime: Long,
        dyId: String,
        id: Long?,
    ): CoroutineResult<JoyWorkNotificationOuter> {
        return listNotificationInner(lastTime = lastTime, dyId = dyId, direction = "DOWN", id = id)
    }

    // pull down to load more
    suspend fun loadMoreUpNotification(
        lastTime: Long,
        dyId: String,
        id: Long?,
    ): CoroutineResult<JoyWorkNotificationOuter> {
        return listNotificationInner(lastTime = lastTime, dyId = dyId, direction = "UP", id = id)
    }

    private suspend fun listNotificationInner(
        limit: Int? = null,
        offset: Int? = null,
        direction: String? = null,
        dyId: String? = null,
        lastTime: Long? = null,
        id: Long? = null,
    ): CoroutineResult<JoyWorkNotificationOuter> {
        return suspendCancellableCoroutine { c ->
            val params = hashMapOf<String, Any>()
            if (limit != null) {
                params["limit"] = limit
            } else {
                params["limit"] = 50
            }
            if (offset != null) {
                params["offset"] = offset
            }
            if (direction != null) {
                params["direction"] = direction
            }
            if (dyId != null) {
                params["dyId"] = dyId
            }
            if (lastTime != null) {
                params["lastTime"] = lastTime
            }
            if (id != null) {
                params["id"] = id
            }
            HttpManager.post(
                null,
                params,
                SimpleReqCallbackAdapter(object :
                    AbsReqCallback<JoyWorkNotificationOuter>(JoyWorkNotificationOuter::class.java) {
                    override fun onSuccess(
                        jsonObject: JoyWorkNotificationOuter?,
                        tArray: MutableList<JoyWorkNotificationOuter>?,
                        rawData: String?
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<JoyWorkNotificationOuter>().success(
                                        this
                                    )
                                )
                            )
                        } ?: onFailure("")
                    }

                    override fun onFailure(errorMsg: String?) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JoyWorkNotificationOuter>().failure(
                                    JoyWorkEx.filterErrorMsg(errorMsg)
                                )
                            )
                        )
                    }
                }),
                "work.task.dynamics.v1"
            )
        }
    }

    suspend fun getNotificationNum(): CoroutineResult<Nums> {
        return suspendCancellableCoroutine { c ->

            HttpManager.post(
                null,
                hashMapOf<String, Any>(),
                SimpleReqCallbackAdapter(object :
                    AbsReqCallback<Nums>(Nums::class.java) {
                    override fun onSuccess(
                        jsonObject: Nums?,
                        tArray: MutableList<Nums>?,
                        rawData: String?
                    ) {
                        jsonObject?.apply {
                            c.resumeWith(
                                Result.success(
                                    CoroutineResult<Nums>().success(this)
                                )
                            )
                        } ?: onFailure("")
                    }

                    override fun onFailure(errorMsg: String?) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<Nums>().success(Nums.getInstance())
                            )
                        )
                    }
                }),
                "work.task.dyNums.v1"
            )
        }
    }

    fun markRead(dyId: String, callback: (Boolean) -> Unit) {
        if (!dyId.isLegalString()) {
            return
        }
        val map = hashMapOf<String, Any>()
        map["dyId"] = dyId;
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JSONObject>(JSONObject::class.java) {
                override fun onSuccess(
                    jsonObject: JSONObject?,
                    tArray: MutableList<JSONObject>?,
                    rawData: String?
                ) {
                    callback(true)
                }

                override fun onFailure(errorMsg: String?) {
                    callback(false)
                }
            }),
            "work.task.dyReadById.v1"
        )
    }

    suspend fun markAllRead(): CoroutineResult<JSONObject> {
        return suspendCancellableCoroutine { c ->
            HttpManager.post(
                null,
                hashMapOf<String, Any>(),
                SimpleReqCallbackAdapter(object :
                    AbsReqCallback<JSONObject>(JSONObject::class.java) {
                    override fun onSuccess(
                        jsonObject: JSONObject?,
                        tArray: MutableList<JSONObject>?,
                        rawData: String?
                    ) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().success(JSONObject())
                            )
                        )
                    }

                    override fun onFailure(errorMsg: String?) {
                        c.resumeWith(
                            Result.success(
                                CoroutineResult<JSONObject>().failure(
                                    JoyWorkEx.filterErrorMsg(
                                        errorMsg
                                    )
                                )
                            )
                        )
                    }
                }),
                "work.task.dyAllRead.v1"
            )
        }
    }

}