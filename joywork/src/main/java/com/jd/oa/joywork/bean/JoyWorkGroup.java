package com.jd.oa.joywork.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JoyWorkGroup {
    private int total;

    @SerializedName("regionType")
    private int blockType;
    private int pageSize;
    private int page;
    private List<JoyWork> tasks;
    private int unPlanCount = 0;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getBlockType() {
        return blockType;
    }

    public void setBlockType(int blockType) {
        this.blockType = blockType;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public List<JoyWork> getTasks() {
        return tasks;
    }

    public void setTasks(List<JoyWork> tasks) {
        this.tasks = tasks;
    }

    public int getUnPlanCount() {
        return unPlanCount;
    }

    public void setUnPlanCount(int unPlanCount) {
        this.unPlanCount = unPlanCount;
    }


    @Override
    public String toString() {
        return "JoyWorkGroup{" +
                "total=" + total +
                ", blockType=" + blockType +
                ", pageSize=" + pageSize +
                ", page=" + page +
                ", tasks=" + tasks +
                ", unPlanCount=" + unPlanCount +
                '}';
    }
}
