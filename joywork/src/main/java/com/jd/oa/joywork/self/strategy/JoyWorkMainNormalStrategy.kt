package com.jd.oa.joywork.self.strategy

import android.app.Activity
import android.view.View
import android.widget.FrameLayout
import com.jd.oa.joywork.R
import com.jd.oa.joywork.openDeeplink
import com.jd.oa.joywork.search.JoyWorkSearchActivity
import com.jd.oa.utils.inflater

/**
 * [JoyWorkMainFragment] 的策略实现类：用于二级界面
 */
object JoyWorkMainNormalStrategy : JoyWorkMainStrategy {

    override fun handleTitle(parent: FrameLayout, activity: Activity) {
        parent.context?.apply {
            inflater.inflate(R.layout.jdme_joywork_fragment_normal_title, parent, true)
            parent.findViewById<View>(R.id.back).setOnClickListener {
                activity.finish()
            }
            parent.findViewById<View>(R.id.more_button).setOnClickListener {
                JoyWorkMainStrategy.DEEP_LINK.openDeeplink()
//                activity.startActivity(Intent(activity, GroupMainActivity::class.java))
            }
            parent.findViewById<View>(R.id.search).setOnClickListener {
                JoyWorkSearchActivity.start(activity)
            }
        }
    }

    override fun getAddViewBottomMargin(): Int = R.dimen.joywork_fab_margin
    override fun onThemeDataChange(parent: View?) {
    }

    override fun getAddViewRightMargin(): Int = R.dimen.joywork_fab_margin
}