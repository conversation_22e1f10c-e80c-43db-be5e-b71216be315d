package com.jd.oa.joywork.self.strategy.selflist

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.self.MyFinishFragment
import com.jd.oa.joywork.self.base.SelfListBaseAdapter
import com.jd.oa.joywork.self.base.SelfListBaseAdapterItf
import com.jd.oa.joywork.self.base.SelfListCreateAdapter
import com.jd.oa.joywork.self.base.SelfListViewModel
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.team.view.HRecyclerView

class MyCreateStrategy(
    next: StrategyBase?,
    itf: StrategyBaseItf,
    context: FragmentActivity,
    mSortFilterStatusViewModel: TeamDetailViewModel,
    mViewModel: SelfListViewModel
) : StrategyBase(next, itf, context, mSortFilterStatusViewModel, mViewModel) {
    override fun canHandle(): Boolean {
        return mItf.getUserRole() == TaskUserRole.CREATOR && mSortFilterStatusViewModel.mUIStatus.mStatus != TaskStatusEnum.FINISH
    }

    override fun needLoadMore(): Boolean {
        return true
    }

    override fun onGetList(userRole: TaskUserRole) {
        mViewModel.listMyCreate(0, mSortFilterStatusViewModel.mUIStatus.mStatus)
    }

    override fun onHandleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? JoyWorkWrapper ?: return ArrayList()
        return MyFinishFragment.handleResult(wrapper, context.resources){
            it.gmtCreate
        }
    }

    override fun onLoadMore(offset: Int, userRole: TaskUserRole) {
        mViewModel.listMyCreate(
            offset,
            mSortFilterStatusViewModel.mUIStatus.mStatus
        )
    }

    override fun onCreateAdapter(
        data: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        itf: SelfListBaseAdapterItf,
        fragment: Fragment,
        recyclerView: HRecyclerView?
    ): SelfListBaseAdapter {
        return SelfListCreateAdapter(
            fragment,
            itf,
            data,
            recyclerView,
            context
        )
    }
}