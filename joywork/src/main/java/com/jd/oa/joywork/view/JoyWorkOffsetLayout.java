package com.jd.oa.joywork.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;

import com.jd.oa.joywork.R;

public class JoyWorkOffsetLayout extends FrameLayout {

    private int mOffset;
    private final boolean mReverseDrawingOrder;

    public JoyWorkOffsetLayout(Context context) {
        this(context, null);
    }

    public JoyWorkOffsetLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public JoyWorkOffsetLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public JoyWorkOffsetLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.JoyWorkOffsetLayout);
        mOffset = typedArray.getDimensionPixelOffset(R.styleable.OffsetLayout_offset, 0);
        mReverseDrawingOrder = typedArray.getBoolean(R.styleable.OffsetLayout_reverseDrawingOrder, true);
        typedArray.recycle();

        setChildrenDrawingOrderEnabled(true);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int count = getChildCount();
        int height = 0;
        int width = 0;
        for (int i = 0; i < count; i++) {
            final View child = getChildAt(i);
            measureChildWithMargins(child, widthMeasureSpec, 0, heightMeasureSpec, 0);
            final FrameLayout.LayoutParams lp = (LayoutParams) child.getLayoutParams();
            height = Math.max(height, child.getMeasuredHeight() + lp.topMargin + lp.bottomMargin);
            width += child.getMeasuredWidth() + lp.leftMargin + lp.rightMargin;
            if (i != 0) {
                width -= mOffset;
            }
        }
        setMeasuredDimension(width, height);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        int right = getMeasuredWidth();
        for (int i = getChildCount() - 1; i >= 0; i--) {
            View child = getChildAt(i);
            int left = right - child.getMeasuredWidth();
            child.layout(left, child.getTop(), right, child.getBottom());
            right = left + mOffset;
        }
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int drawingPosition) {
        return mReverseDrawingOrder ? (childCount - 1) - drawingPosition : drawingPosition;
    }

    @Nullable
    @Override
    protected Parcelable onSaveInstanceState() {
        Parcelable superState = super.onSaveInstanceState();
        JoyWorkOffsetLayout.OffsetSavedState savedState = new JoyWorkOffsetLayout.OffsetSavedState(superState);
        savedState.offset = mOffset;
        return savedState;
    }

    @Override
    protected void onRestoreInstanceState(Parcelable state) {
        if (!(state instanceof JoyWorkOffsetLayout.OffsetSavedState)) {
            super.onRestoreInstanceState(state);
            return;
        }

        JoyWorkOffsetLayout.OffsetSavedState savedState = (JoyWorkOffsetLayout.OffsetSavedState) state;
        super.onRestoreInstanceState(savedState.getSuperState());

        this.mOffset = savedState.offset;
    }

    private static class OffsetSavedState extends BaseSavedState {

        private int offset;

        public OffsetSavedState(Parcel source) {
            super(source);
            offset = source.readInt();
        }

        public OffsetSavedState(Parcelable superState) {
            super(superState);
        }

        @Override
        public void writeToParcel(Parcel out, int flags) {
            super.writeToParcel(out, flags);
            out.writeInt(offset);
        }

        public static final Parcelable.Creator<JoyWorkOffsetLayout.OffsetSavedState> CREATOR = new Parcelable.Creator<JoyWorkOffsetLayout.OffsetSavedState>() {
            @Override
            public JoyWorkOffsetLayout.OffsetSavedState createFromParcel(Parcel source) {
                return new JoyWorkOffsetLayout.OffsetSavedState(source);
            }

            @Override
            public JoyWorkOffsetLayout.OffsetSavedState[] newArray(int size) {
                return new JoyWorkOffsetLayout.OffsetSavedState[size];
            }
        };
    }
}
