package com.jd.oa.joywork.create.param;

import com.jd.oa.joywork.create.Value;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

import javax.annotation.Nullable;

// 第三方应用通过 deeplink 新建时所带参数
public class DeeplinkCreateParam {
    // 直接透传至新建接口
    public String bizCode;
    public String content;
    public String mobileContent;
    public String inventory;
    public String tripartiteName;

    @Nullable
    public static DeeplinkCreateParam createFromParam(String param) {
        try {
            DeeplinkCreateParam result;
            String keyAppName = "appName";
            JSONObject obj = new JSONObject(param);
            String appName = null;
            if (obj.has(keyAppName)) {
                appName = obj.getString(keyAppName);
            } else {
                String keyAppFrom = "from";
                if (obj.has(keyAppFrom)) {
                    appName = obj.getString(keyAppFrom);
                }
            }
            if (SpaceCreateParam.APP_NAME.equals(appName)) {
                result = new SpaceCreateParam();
                result.bizCode = obj.getString("bizCode");
                result.content = obj.getString("content");
                result.mobileContent = obj.getString("mobileContent");
            } else if (KRCreateParams.APP_NAME.equals(appName)) {
                result = new KRCreateParams();
            } else if (GoalCreateParams.APP_NAME.equals(appName)) {
                result = new GoalCreateParams();
            } else if (EmailCreateParams.APP_NAME.equals(appName)) {
                EmailCreateParams params = new EmailCreateParams();
                if (obj.has("title")) {
                    params.setTitle(obj.getString("title"));
                }
                if (obj.has("desc")) {
                    params.setDesc(obj.getString("desc"));
                }
                params.content = obj.optString("content", null);
                params.mobileContent = obj.optString("mobileContent", null);
                params.bizCode = obj.optString("bizCode", null);
                params.inventory = obj.optString("inventory", null);
                params.tripartiteName = obj.optString("tripartiteName", null);
                result = params;
            } else {
                return null;
            }
            result.onParseParam(obj);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void initCreateValue(Value value) {

    }

    public void onParseParam(JSONObject object) throws JSONException {
    }

    public String getAppName() {
        return "";
    }

    public final HashMap<String, Object> toCreateMap() {
        HashMap<String, Object> r = new HashMap<>();
        r.put("source", getAppName());
        toMap(r);
        return r;
    }

    protected void toMap(HashMap<String, Object> map) {
    }
}
