package com.jd.oa.joywork.detail.data.entity.mark;

import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup;

import java.util.List;

/**
 * work.task.getTaskExtInfo.v2 返回实体类，与 TaskDetailEntity 有一些区别
 */
public class TaskDetailEntity2 {

    private String errorCode;
    private JoyWorkContent2 content;
    private String errorMsg;

    private boolean titleEdit;

    public boolean getTitleEdit() {
        return titleEdit;
    }

    public void setTitleEdit(boolean titleEdit) {
        this.titleEdit = titleEdit;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setContent(JoyWorkContent2 content) {
        this.content = content;
    }

    public JoyWorkContent2 getContent() {
        return content;
    }

    public List<CustomFieldGroup> getCustomField() {
        return content == null ? null : content.customFieldDic;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

}