package com.jd.oa.joywork.self.strategy.selflist

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.self.base.*
import com.jd.oa.joywork.team.DataClassifier
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.team.bean.ProjectSortEnum
import com.jd.oa.joywork.team.view.HRecyclerView

/**
 * 按执行人排序
 */
class OwnerSortStrategy(
    next: StrategyBase?,
    mItf: StrategyBaseItf,
    context: FragmentActivity,
    mSortFilterStatusViewModel: TeamDetailViewModel,
    private val itf: SelfListSortItf,
    mViewModel: SelfListViewModel
) : StrategyBase(next, mItf, context, mSortFilterStatusViewModel, mViewModel) {
    override fun canHandle(): Boolean {
        return mSortFilterStatusViewModel.mUIStatus.mSort.code == ProjectSortEnum.SORT_BY_OWNER.code
    }

    override fun needLoadMore(): Boolean {
        return true
    }

    override fun onLoadMore(offset: Int, userRole: TaskUserRole) {
        val uiStatus = mSortFilterStatusViewModel.mUIStatus
        mViewModel.listPriority(
            itf.getSortType(),
            itf.getProjectId(),
            uiStatus.mFilter,
            uiStatus.mSort,
            offset,
            uiStatus.mStatus,
        )
    }

    override fun onGetList(userRole: TaskUserRole) {
        val uiStatus = mSortFilterStatusViewModel.mUIStatus
        mViewModel.listPriority(
            itf.getSortType(),
            itf.getProjectId(),
            uiStatus.mFilter,
            uiStatus.mSort,
            0,
            uiStatus.mStatus,
        )
    }

    override fun onCreateAdapter(
        data: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        itf: SelfListBaseAdapterItf,
        fragment: Fragment,
        recyclerView: HRecyclerView?
    ): SelfListBaseAdapter {
        return SelfListOwnerAdapter(
            fragment,
            itf,
            data,
            recyclerView,
            context
        )
    }

    override fun onHandleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? JoyWorkWrapper ?: return ArrayList()
        return DataClassifier.clazz(
            CustomFieldGroupOuter(),
            context,
            mSortFilterStatusViewModel.mUIStatus.mSort,
            wrapper.clientTasks.first().tasks
        )
    }
}