package com.jd.oa.joywork.bean

import java.io.Serializable

/**
 * When comparing, only use first as the comparison value
 */
public data class Quadra<A : Comparable<A>, B, C, D>(
        public val first: A,
        public val second: B,
        public val third: C,
        public val fourth: D
) : Serializable, Comparable<Quadra<A, B, C, D>> {
    public override fun toString(): String = "($first, $second, $third, $fourth)"
    override fun compareTo(other: Quadra<A, B, C, D>): Int {
        return first.compareTo(other.first)
    }
}

/**
 * When comparing, only use first as the comparison value
 */
public data class Penta<A : Comparable<A>, B, C, D, E>(
        public val first: A,
        public val second: B,
        public val third: C,
        public val fourth: D,
        public val forth: E
) : Serializable, Comparable<Penta<A, B, C, D, E>> {
    public override fun toString(): String = "($first, $second, $third, $fourth, $forth)"
    override fun compareTo(other: Penta<A, B, C, D, E>): Int {
        return first.compareTo(other.first)
    }
}
