package com.jd.oa.joywork.team.dialog

import android.app.Activity
import android.content.res.Resources
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.EditText
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatDialog
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.common.ItfShortcutDialogCommon
import com.jd.oa.joywork.common.ShortcutDialogCommon
import com.jd.oa.joywork.create.JoyWorkCreateActivity
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.shortcut.ChatType
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork.shortcut.SuccessSnackBar
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.todayTime
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.model.service.WaterService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.ColorEx
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DateShowUtils
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.InputMethodUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.TextWatcherAdapter
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.color
import com.jd.oa.utils.inflater
import com.jd.oa.utils.string

class CreateDialog(
    val ctx: Activity,
    groups: List<Group>,
    private val projectId: String,
    private val config: ShortcutDialogTmpData? = null
) :
    AppCompatDialog(ctx, R.style.JoyWorkDialogStyle), ItfShortcutDialogCommon {

    private val innerGroups = if (groups.isLegalList()) ArrayList<Group>(groups) else ArrayList()

    private var selectGroupId: String? = config?.selectGroupId ?: innerGroups.firstOrNull()?.groupId

    private val safeTitle: String
        get() = if (selectGroupId.isLegalString()) { // 有分组
            val first = innerGroups.first { it.groupId == selectGroupId }
            first.safeTitle(ctx)
        } else {
            ctx.string(R.string.joywork_select_group_list_title)
        }


    private val manager = ShortcutManager(this)
    private val support = DialogSupporter(this, ctx)
    private val mValue = Value()

    private val transparentGrayColor = Color.parseColor("#7FDEE0E3")

    private val mContentView: View
    private val mOwnerContainer: View

    private var mAssignTips: TextView

    private val mCreateView: TextView

    init {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        mContentView = context.inflater.inflate(R.layout.joywork_project_create_dialog, null)
        setContentView(mContentView)

        ShortcutDialogCommon.initValue(this, null)
        ShortcutDialogCommon.setDupClickListener(support, this)
        ShortcutDialogCommon.setupSendToChat(this)
        ShortcutDialogCommon.handleTimeView(this)
        ShortcutDialogCommon.setAlertClearClick(support, this)

        mTodayContainer.background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
        mTodayContainer.setOnClickListener {
            // 截止时间设置为今天
            mValue.endTime = todayTime()
            ShortcutDialogCommon.afterTimeSelected(this@CreateDialog)
        }
        mTomorrowContainer.background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
        mTomorrowContainer.setOnClickListener {
            // 截止时间设置为明天
            mValue.endTime =
                DateUtils.getSpecialHour(System.currentTimeMillis() + 24 * 60 * 60 * 1000, 16)
            ShortcutDialogCommon.afterTimeSelected(this@CreateDialog)
        }

        if (context.applicationInfo.targetSdkVersion >= Build.VERSION_CODES.P) {
            InputMethodUtils.showSoftInputFromWindow(context, mTitleView)
        }

        mContentView.findViewById<View>(R.id.detail).setOnClickListener {
            val tmpConfig = config ?: ShortcutDialogTmpData()
            JoyWorkCreateActivity.projectId = projectId;
            JoyWorkCreateActivity.groups = innerGroups
            JoyWorkCreateActivity.selId = selectGroupId
            JoyWorkCreateActivity.successRunnable = SuccessSnackBar(ctx)
            JoyWorkCreateActivity.tmpData = tmpConfig
            JoyWorkCreateActivity.tmpData.taskListTypeEnum = TaskListTypeEnum.PROJECT
            ShortcutDialogCommon.inflateCreateActivity(this)
            JoyWorkCreateActivity.go(
                ctx,
                mTitleView.string(),
                mValue.startTime,
                mValue.endTime,
                true // 当前 dialog 可能来自于个人待办按自定义分组时，
            )
            <EMAIL>()
        }

        val tv = mContentView.findViewById<View>(R.id.detail)
        tv.background = DrawableEx.roundStrokeRect(
            Color.parseColor("#E6E6E6"),
            CommonUtils.dp2px(0.5f),
            CommonUtils.dp2FloatPx(6)
        )

        mCreateView = mContentView.findViewById(R.id.create)

        mOwnerContainer = mContentView.findViewById(R.id.owner_container)
        mOwnerContainer.setOnClickListener {
            JDMAUtils.onEventClick(JoyWorkConstant.SHORTCUT_OWNER, JoyWorkConstant.SHORTCUT_OWNER)
            manager.selectContact(context, mValue, null, ChatType.NO_CHAT.value) { success ->
                ShortcutDialogCommon.openKeyboard(mTitleView, context)
                if (success) {
                    handleOwnerView()
                }
            }
        }
        mOwnerContainer.background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))

        mTimeContent.background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
        ShortcutDialogCommon.setTimeContainerClickListener(this)

        ShortcutDialogCommon.setTimeClearClick(this)

        mAssignTips = mContentView.findViewById(R.id.assign_text)
        handleAssignView()
        ShortcutDialogCommon.handleAssignContainer(this)
        mContentView.findViewById<View>(R.id.assign_container).setOnClickListener {
            JDMAUtils.onEventClick(JoyWorkConstant.SHORTCUT_ASSIGN, JoyWorkConstant.SHORTCUT_ASSIGN)
            manager.closeKeyboard(mTitleView, context)
            support.listGroupOld(projectId, innerGroups, selectGroupId) {
                if (it != null) {
                    selectGroupId = it.groupId
                    handleAssignView()
                    handleCreateView()
                }
            }
        }

        window?.apply {
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN or WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE or WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
            clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
            val layoutParams = attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.BOTTOM
            attributes = layoutParams
        }

        setCanceledOnTouchOutside(true)

        setOnDismissListener {
            manager.closeKeyboard(mTitleView, context)
        }
        manager.initAt(mTitleView) { init ->
            manager.selectContact(context, 1) { b, bean ->
                if (b.isTrue() && bean.isLegalList()) {
                    mValue.addNormalOwners(bean);
                    ShortcutDialogCommon.handleAvatarView(this, context)
                    bean?.forEach {
                        init.appendAt(it)
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppJoint.service(WaterService::class.java)?.addWaterView(this, true)
        setCanceledOnTouchOutside(true)
        // 创建按钮颜色
        mCreateView.setTextColor(ColorEx.addItem(ColorEx.Item().apply {
            enable = true
            color = context.color(R.color.joywork_red)
        }, ColorEx.Item().apply {
            enable = false
            color = context.color(R.color.joywork_red_transparent)
        }, ColorEx.Item().apply {
            color = context.color(R.color.joywork_red)
        }))
        mCreateView.setOnClickListener {
            it.isEnabled = false
            if (!selectGroupId.isLegalString()) {
                Toast.makeText(context, R.string.joywork_select_group_tips, Toast.LENGTH_SHORT)
                    .show()
                return@setOnClickListener
            }

            ProjectRepo.create({ data, success, msg ->
                if (success && data != null) {
                    successCallback.invoke(data, this)
//                    if (mSendIconChecked && mSendDD.isVisible()) {
//                        val detail = JoyWorkDetail()
//                        detail.taskId = data.taskId
//                        detail.title = data.title
//                        detail.endTime = data.endTime
//                        detail.remark = data.remark
//                        ShortcutDialogCommon.sendByChat(mValue.normalOwners, detail)
//                    }
                }

            }) {
                if (mValue.hasSelf() || config?.isProject.isTrue()) {
                    groupId = selectGroupId
                    projectId = <EMAIL>
                }
                imMessage = mSendIconChecked
                taskListType = TaskListTypeEnum.PROJECT
                ShortcutDialogCommon.addCommonNetParams(this@CreateDialog, this)
            }
        }

        mTitleView.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                mValue.title = mTitleView.string().trim()
                handleCreateView()
            }
        })
        handleCreateView()
        handleOwnerView()
    }

    private fun handleAssignView() {
        if (selectGroupId.isLegalString()) {
            // 选择了分组。ellipsize 无效，此处手动修改
            mAssignTips.text =
                if (safeTitle.length > 6) "${safeTitle.subSequence(0, 6)}..." else safeTitle
        } else {
            mAssignTips.text = safeTitle
        }
        (mAssignTips.parent as ViewGroup).background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
        ShortcutDialogCommon.openKeyboard(mTitleView, mTitleView.context)
    }

    private fun handleOwnerView() {
        ShortcutDialogCommon.handleAvatarView(this, context)
    }

    private fun handleCreateView() {
        mCreateView.isEnabled = mValue.title.isLegalString() && selectGroupId.isLegalString()
    }

    override fun getValue(): Value {
        return mValue
    }

    override fun showGroup(): Boolean = projectId.isNotEmpty()


    override fun getShortcutManager(): ShortcutManager = manager

    override fun tmpData(): ShortcutDialogTmpData? = config

    override fun showKeyBoardAfterChangePage(): Boolean {
        return true
    }

    override var mSendIconChecked: Boolean = false

    override val mTodayContainer: View
        get() = mContentView.findViewById(R.id.mTodayContainer)

    override val mTomorrowContainer: View
        get() = mContentView.findViewById(R.id.mTomorrowContainer)

    override val mTimeTips: TextView
        get() = mContentView.findViewById(R.id.time_text)
    override val mTimeContent: View
        get() = mContentView.findViewById(R.id.time_container)
    override val mTimeClear: IconFontView
        get() = mContentView.findViewById(R.id.clear_time)
    override val mAlarmTime: IconFontView?
        get() = null

    //        get() = mContentView.findViewById(R.id.alarm_time)
    override val mDupIcon: IconFontView?
        get() = null

    //        get() = mContentView.findViewById(R.id.dup_icon)
    override val mTimeIcon: TextView
        get() = mContentView.findViewById(R.id.time_icon)
    override val mHorScrollView: HorizontalScrollView
        get() = mContentView.findViewById(R.id.mHorScrollView)

    override val iconDuplicate: IconFontView
        get() = mContentView.findViewById(R.id.iconDuplicate)
    override val textDuplicate: TextView
        get() = mContentView.findViewById(R.id.textDuplicate)
    override val dupContainer: View
        get() = mContentView.findViewById(R.id.dup_container)
    override val mExecutorArrow: View
        get() = mContentView.findViewById(R.id.mExecutorArrow)
    override val mAvatarView: JoyWorkAvatarView
        get() = mContentView.findViewById(R.id.mAvatarView)
    override val mSendDD: View
        get() = mContentView.findViewById(R.id.mSendDD)
    override val mSendIcon: ImageView
        get() = mContentView.findViewById(R.id.mSendIcon)
    override val mSendText: TextView
        get() = mContentView.findViewById(R.id.mSendText)
    override val mTitleView: EditText
        get() = mContentView.findViewById(R.id.title)

    override val mAlertContainer: ViewGroup
        get() = mContentView.findViewById(R.id.mAlertContainer)
    override val mAlertIcon: TextView
        get() = mContentView.findViewById(R.id.mAlertIcon)
    override val mAlertText: TextView
        get() = mContentView.findViewById(R.id.mAlertText)

    override val mAssignContainer: View?
        get() = mContentView.findViewById(R.id.assign_container)
    override val mAssignContainerDivider: View?
        get() = mContentView.findViewById(R.id.mGroupDivider)

    var successCallback: (JoyWork, CreateDialog) -> Unit = { _, dialog ->
        ToastUtils.showInfoToast(R.string.joywork_create_success)
        dialog.dismiss()
    }

    companion object {
        // 2022年01月11日 修改时间显示样式
        // 返回 xx 开始-xx 截止
        public fun getTimeStr2(
            startTime: Long?,
            endTime: Long?,
            resources: Resources,
            def: String?
        ): String? {
            return if (endTime != null && startTime != null) { // 开始，截止时间都有
                val start = DateShowUtils.getTimeShowTextWithHourMinute(startTime)
                val end = DateShowUtils.getTimeShowTextWithHourMinute(endTime)
                val ss = resources.getString(R.string.me_joywork_start_normal, start)
                val es = resources.getString(R.string.me_joywork_deadline_normal, end)
                "$ss-$es"
            } else {
                endTime?.run {
                    val end = DateShowUtils.getTimeShowTextWithHourMinute(this)
                    resources.getString(R.string.me_joywork_deadline_normal, end)
                } ?: startTime?.run {
                    val start = DateShowUtils.getTimeShowTextWithHourMinute(this)
                    resources.getString(R.string.me_joywork_start_normal, start)
                } ?: def
            }
        }
    }
}