package com.jd.oa.joywork.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.detail.ui.LinkCallback;
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum;

import java.util.ArrayList;
import java.util.List;

public class TaskLinkAdapter extends RecyclerView.Adapter<TaskLinkAdapter.ViewHolder> {
    private final LinkCallback callback;
    private boolean canUpdate;
    public List<JoyWorkDetail.Project> data;

    public TaskLinkAdapter(boolean canUpdate, LinkCallback callback, List<JoyWorkDetail.Project> projects) {
        this.canUpdate = canUpdate;
        this.callback = callback;
        data = new ArrayList<>(projects);
    }

    public void setCanUpdate(boolean canUpdate) {
        this.canUpdate = canUpdate;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        LayoutInflater inflater = LayoutInflater.from(viewGroup.getContext());
        return new ViewHolder(inflater.inflate(R.layout.joywork_order_item, viewGroup, false));
    }

    private final View.OnClickListener mClose = new View.OnClickListener() {

        @Override
        public void onClick(View v) {
            try {
                JoyWorkDetail.Project project = (JoyWorkDetail.Project) v.getTag();
                callback.onDelClick(project);
            } catch (Exception e) {
                // empty
            }
        }
    };
    private final View.OnClickListener mGroupClick = new View.OnClickListener() {

        @Override
        public void onClick(View v) {
            try {
                JoyWorkDetail.Project project = (JoyWorkDetail.Project) v.getTag();
                callback.onClick(project);
            } catch (Exception e) {
                // empty
            }
        }
    };

    private final View.OnClickListener mArrowClick = new View.OnClickListener() {

        @Override
        public void onClick(View v) {
            try {
                if (!canUpdate) {
                    return;
                }
                JoyWorkDetail.Project project = (JoyWorkDetail.Project) v.getTag();
                callback.changeGroup(project);
            } catch (Exception e) {
                // empty
            }
        }
    };

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        JoyWorkDetail.Project project = data.get(i);
        if (canUpdate && callback != null) {
            viewHolder.tvClose.setVisibility(View.VISIBLE);
            viewHolder.tvClose.setTag(project);
            viewHolder.tvClose.setOnClickListener(mClose);
        } else {
            viewHolder.tvClose.setVisibility(View.GONE);
        }
        boolean projectPermission = project.permissions != null && project.permissions.contains(ProjectPermissionEnum.TASK.getCode());
        if (canUpdate && callback != null && projectPermission) {
            viewHolder.mDownArrow.setVisibility(View.VISIBLE);
            viewHolder.mDownArrowContainer.setTag(project);
            viewHolder.mDownArrowContainer.setOnClickListener(mArrowClick);
        } else {
            viewHolder.mDownArrow.setVisibility(View.GONE);
        }

        if (TextUtils.isEmpty(project.getGroupTitle())) {
            viewHolder.tvGroupName.setText(R.string.joywork_task_link_untitled_group);
        } else {
            viewHolder.tvGroupName.setText(project.getGroupTitle());
        }
        viewHolder.tvGroupName.requestLayout();

        if (TextUtils.isEmpty(project.getTitle())) {
            viewHolder.tvProjectName.setText(R.string.joywork_task_link_untitled_project);
        } else {
            viewHolder.tvProjectName.setText(project.getTitle());
        }
        viewHolder.tvProjectName.setTag(project);
        viewHolder.tvProjectName.setOnClickListener(mGroupClick);

        if (i == 0) {
            viewHolder.space.setVisibility(View.GONE);
        } else {
            viewHolder.space.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        return data.size();
    }

    public void submitList(List<JoyWorkDetail.Project> ps) {
        ps = ps == null ? new ArrayList<JoyWorkDetail.Project>() : ps;
        data.clear();
        data.addAll(ps);
        notifyDataSetChanged();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        View space;
        TextView tvProjectName;
        TextView tvGroupName;
        View tvClose;
        View mDownArrow;
        View mDownArrowContainer;

        public ViewHolder(View view) {
            super(view);
            space = view.findViewById(R.id.space);
            tvProjectName = view.findViewById(R.id.tv_project_name);
            tvGroupName = view.findViewById(R.id.tv_group_name);
            tvClose = view.findViewById(R.id.tv_close);
            mDownArrow = view.findViewById(R.id.mDownArrow);
            mDownArrowContainer = view.findViewById(R.id.mDownArrowContainer);
        }
    }
}
