package com.jd.oa.joywork.search

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.isLegalString
import java.util.*

class JoyWorkSearchViewModel : ViewModel() {
    private val _statusLiveData = MutableLiveData<Int>()
    val statusLiveData: LiveData<Int> = _statusLiveData

    fun updateStatus(v: Int) {
        if (Objects.equals(statusLiveData.value, v)) {
            return
        }
        _statusLiveData.value = v
    }

    private val _contentLiveData = MutableLiveData<String>()
    val contentLiveData: LiveData<String> = _contentLiveData
    fun updateContent(v: String) {
        if (Objects.equals(contentLiveData.value, v)) {
            return
        }
        _contentLiveData.value = v
    }


    fun key(): String? {
        if (statusLiveData.value == null) {
            return null
        }
        if (!contentLiveData.value.isLegalString()) {
            return null
        }
        return "${contentLiveData.value}_${statusLiveData.value}"
    }
}
