package com.jd.oa.joywork.title

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.Quadra
import com.jd.oa.utils.inflater
import java.util.*

/**
 * TitleBar。the title is horizontally centered
 */
class CenterTitleFragment : BaseFragment() {
    private val ps = mutableListOf<Runnable>()
    private val rs = mutableListOf<Runnable>()

    private val actions = PriorityQueue<Quadra<Int, Int, Int, View.OnClickListener?>>()

    fun into(activity: FragmentActivity, id: Int): CenterTitleFragment {
        activity.supportFragmentManager.beginTransaction().add(id, this).commit()
        return this
    }

    /**
     * used to obtain the views owned by the fragment
     */
    fun post(r: Runnable): CenterTitleFragment {
        if (view != null) {
            r.run()
            return this
        }
        ps.add(r)
        return this
    }

    /**
     * Specifies the element of the toolbar's left view
     * @param id: view id。You can get the left view using findViewById(id) in post() method
     */
    fun left(resId: Int, id: Int = -1, c: View.OnClickListener?): CenterTitleFragment {
        val r = Runnable {
            val v = view?.findViewById<TextView>(R.id.close) ?: return@Runnable
            v.setText(resId)
            if (c != null) {
                v.setOnClickListener(c)
            }
            if (id > 0) {
                v.id = id
            }
        }
        if (view != null) {
            r.run()
        } else {
            rs.add(r)
        }
        return this
    }

    fun finish(): CenterTitleFragment {
        return left(R.string.icon_direction_left) {
            activity?.finish()
        }
    }

    fun title(id: Int): CenterTitleFragment {
        val r = Runnable {
            val v = view?.findViewById<TextView>(R.id.title) ?: return@Runnable
            v.setText(id)
        }
        if (view != null) {
            r.run()
        } else {
            rs.add(r)
        }
        return this
    }

    fun action(
        resId: Int,
        id: Int = -1,
        index: Int,
        c: View.OnClickListener?
    ): CenterTitleFragment {
        val customView = view
        val q = Quadra(index, resId, id, c)
        if (customView == null) {
            actions.add(q)
            return this
        }
        addAction(q)
        return this
    }

    private fun addAction(quadra: Quadra<Int, Int, Int, View.OnClickListener?>) {
        val customView = view ?: return
        val v = customView.findViewById<ViewGroup>(R.id.action_container)
        val item =
            v.context.inflater.inflate(R.layout.joywork_center_title_action, v, false) as TextView
        item.setText(quadra.second)
        if (quadra.fourth != null) {
            item.setOnClickListener(quadra.fourth)
        }
        if (quadra.third > 0) {
            item.id = quadra.third
        }
        v.addView(item)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        return inflater.inflate(R.layout.joywork_center_title, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        actions.forEach {
            addAction(it)
        }
        actions.clear()
        rs.forEach {
            it.run()
        }
        rs.clear()
        ps.forEach {
            it.run()
        }
        ps.clear()
    }
}