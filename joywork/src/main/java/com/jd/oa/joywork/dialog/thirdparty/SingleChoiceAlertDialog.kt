package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import com.jd.oa.utils.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.isLegalList
import java.lang.IllegalArgumentException


class SingleChoiceAlertDialog(context: Context, private val config: SingleChoiceConfig) :
    AppCompatDialog(context, R.style.JoyWorkAlertDialogStyle) {

    private lateinit var mContentView: View
    private lateinit var mMsgView: TextView
    private lateinit var mOkTextView: TextView

    private val itemView = ArrayList<View>()
    private val itemTv = ArrayList<TextView>()
    private val itemIcon = ArrayList<TextView>()

    private var selectedItemIndex = -1

    init {
        window?.apply {
            decorView.setBackgroundColor(Color.TRANSPARENT)
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = CommonUtils.getScreentWidth(context) * 62 / 75
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            attributes = layoutParams
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mContentView =
            context.inflater.inflate(R.layout.jdme_dialog_joywork_alert_single_choice, null)
        setContentView(mContentView)
        window?.decorView?.setBackgroundColor(Color.TRANSPARENT)
        mContentView.findViewById<ViewGroup>(R.id.root)?.apply {
            background =
                DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(config.bgCorner))
        }
        mMsgView = mContentView.findViewById(R.id.message)
        mMsgView.text = getStr(config.msgRes, config.msgStr)

        mOkTextView = mContentView.findViewById(R.id.ok)
        mOkTextView.text = getStr(config.okRes, config.okStr)

        getOkClick()?.apply {
            mOkTextView.setOnClickListener {
                this.invoke(this@SingleChoiceAlertDialog)
            }
        }

        mContentView.findViewById<TextView>(R.id.cancel).setOnClickListener {
            cancel()
        }

        selectedItemIndex = config.selectIndex
        val choiceContainer = mContentView.findViewById<LinearLayout>(R.id.choice_container)
        if (config.actions.isLegalList()) {
            choiceContainer.visible()
            addChoices(choiceContainer)
        } else {
            choiceContainer.gone()
        }
    }

    private fun addChoices(parent: ViewGroup) {
        config.actions?.forEachIndexed { index, it ->
            val item = context.inflater.inflate(
                R.layout.jdme_dialog_joywork_alert_single_choice_item,
                parent,
                false
            )
            itemView.add(item)
            val tv = item.findViewById<TextView>(R.id.content)
            itemTv.add(tv)
            tv.text = it
            val icon = item.findViewById<TextView>(R.id.flag)
            itemIcon.add(icon)

            if (selectedItemIndex == index) {
                icon.argbId(R.color.joywork_red)
                icon.setText(R.string.icon_padding_checkcircle)
            } else {
                icon.argbId(R.color.joywork_black_FFBFC1C)
                icon.setText(R.string.icon_prompt_circle)
            }

            item.setOnClickListener {
                val tmpIndex = itemView.indexOf(it)
                if (tmpIndex >= 0 && tmpIndex != selectedItemIndex) {
                    unselect(selectedItemIndex)
                    selectedItemIndex = tmpIndex
                    select(selectedItemIndex)
                }
            }
            parent.addView(item)
        } ?: parent.gone()
    }

    private fun unselect(index: Int) {
        if (index >= 0 && index < itemIcon.size) {
            val icon = itemIcon[index]
            icon.argbId(R.color.joywork_black_FFBFC1C)
            icon.setText(R.string.icon_prompt_circle)
        }
    }

    private fun select(index: Int) {
        if (index >= 0 && index < itemIcon.size) {
            val icon = itemIcon[index]
            icon.argbId(R.color.joywork_red)
            icon.setText(R.string.icon_padding_checkcircle)
        }
    }

    fun getSelectIndex() = selectedItemIndex

    private fun getStr(res: Int, str: String?): String {
        if (res <= 0 && str == null) {
            throw IllegalArgumentException()
        }
        return str ?: context.string(res)
    }

    private fun getOkClick(): ((SingleChoiceAlertDialog) -> Unit)? {
        return if (config.autoDismiss) { dialog ->
            dismiss()
            config.okShitClick?.invoke(dialog)
        } else config.okShitClick
    }
}

class SingleChoiceConfig : ShitAlertDialogConfig<SingleChoiceAlertDialog>() {
    var actions: List<String>? = null
    var selectIndex = -1
}