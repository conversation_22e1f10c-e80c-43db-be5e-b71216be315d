package com.jd.oa.joywork.repo

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.BlockTypeEnum
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.JoyWorkMsgCenter
import com.jd.oa.joywork.RegionTypeEnum
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.model.service.im.dd.entity.BatchUserInfo
import com.jd.oa.joywork.bean.JoyWorkNum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.bean.TransferResult
import com.jd.oa.joywork.detail.data.entity.DelMemberSend
import com.jd.oa.joywork.detail.data.entity.Documents
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.Resources
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.toNetMap
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalSet
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.team.ProjectRepo
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.joywork.team.bean.ProjectSortEnum
import com.jd.oa.joywork.urge.UrgeUserBase
import com.jd.oa.joywork2.backend.JoyWorkSource
import com.jd.oa.joywork2.menu.MenuBean
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.SimpleReqCallbackAdapter
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.storage.entity.KvEntity
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.JsonUtils
import org.json.JSONObject

/**
 * 加载时，进度条显示状态
 */
enum class LoadingViewStrategy {
    SHOW,
    HIDE;

    fun show(): Boolean {
        return this == SHOW
    }
}

enum class CacheStrategy {
    READ, // 只读
    WRITE, // 只写
    ALL, // 即读又写
    NONE; // 不使用缓存

    /**
     * 是否能读取缓存
     */
    fun canRead(): Boolean {
        return this in listOf(READ, ALL)
    }

    fun canWrite(): Boolean {
        return this in listOf(WRITE, ALL)
    }
}

object JoyWorkRepo {

    fun hasCache(sp: JoyWorkPreference?, key: KvEntity<String>): Boolean {
        return sp?.get(key).isLegalString()
    }

    /**
     * 用于获取列表。由于在可能在各个地方调用，所以不用观察者了，直接传递回调
     */
    fun listWithCache(
        strategy: CacheStrategy,
        sp: JoyWorkPreference?,
        cacheKey: KvEntity<String>?,
        callback: JoyWorkVMCallback,
        paramsInit: JoyWorkParamsBuilder.() -> Unit
    ) {
        // 有缓存，且需要读
        if (cacheKey != null && strategy.canRead() && sp?.get(cacheKey)
                .isLegalString()
        ) {
            sp?.get(cacheKey)?.apply {
                val obj = JSONObject(this)
                val wrapper = Gson().fromJson(obj.getString("content"), JoyWorkWrapper::class.java)
                callback.call(wrapper)
            }
        }

        val realCallback = if (strategy.canWrite()) object : JoyWorkVMCallback() {
            override fun call(wrapper: JoyWorkWrapper, rawData: String) {
                if (cacheKey != null) {
                    sp?.put(cacheKey, rawData)
                }
//                sp?.edit()?.putString(CACHE_KEY, rawData)?.commit()
                callback.call(wrapper)
            }

            override fun onError(msg: String) {
                callback.onError(JoyWorkEx.filterErrorMsg(msg))
            }
        } else callback

        list(realCallback, null, null, paramsInit)
    }

    /**
     * 用于获取列表。由于在可能在各个地方调用，所以不用观察者了，直接传递回调
     */
    fun list(
        callback: JoyWorkVMCallback,
        filterValue: FilterValue?,
        sortValue: SortValue?,
        paramsInit: JoyWorkParamsBuilder.() -> Unit
    ) {
        val params: HashMap<String, Any> = HashMap()
        JoyWorkParamsBuilder.clear()
        JoyWorkParamsBuilder.init()
        JoyWorkParamsBuilder.paramsInit()
        JoyWorkParamsBuilder.types?.apply {
            val tmp = ArrayList<HashMap<String, Any>>(size)
            params["clientFindTaskReq"] = tmp
            params["queryVersion"] = 2
            forEachIndexed { index, blockTypeEnum ->
                val hashMap = HashMap<String, Any>()
                tmp.add(hashMap)

                hashMap.putAll(JoyWorkParamsBuilder.tmp)
                blockTypeEnum.addParams(hashMap)

                if (JoyWorkParamsBuilder.limits != null) {
                    hashMap["limit"] = JoyWorkParamsBuilder.limits!![index]
                }
            }
        }
        if (filterValue != null && filterValue.type != ProjectFilterEnum.SCREEN_NULL.code) {
            val map = HashMap<String, Any>()
            filterValue.toMap(map)
            params["filter"] = map
        }
        if (sortValue != null && sortValue.code != ProjectSortEnum.SORT_NULL.code) {
            val map = HashMap<String, Any>()
            map["type"] = sortValue.code
            if (sortValue.value != null) {
                map["value"] = sortValue.value
            }
            params["sort"] = map
        }

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            }),
            "work.task.findMyTasks.v4"
        )
    }

    fun listFinish(
        offset: Int,
        userRole: TaskUserRole,
        taskStatus: TaskStatusEnum,
        regionType: RegionTypeEnum?,
        callback: JoyWorkVMCallback
    ) {
        val params: HashMap<String, Any> = HashMap()
        params["limit"] = ProjectRepo.PAGE_LIMIT
        params["offset"] = offset
        params["queryVersion"] = 2
        regionType?.addParams(params)
        userRole.addParams(params)
        taskStatus.addParams(params)

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            }),
            "work.task.findMyTasks.v4"
        )
    }

    /**
     * 返回参数是一个列表，需要前端聚类显示
     */
    fun listWithoutRegion(
        offset: Int,
        userRole: TaskUserRole,
        taskStatus: TaskStatusEnum,
        regionType: RegionTypeEnum?,
        filterValue: FilterValue?,
        sortValue: SortValue?,
        callback: JoyWorkVMCallback
    ) {
        val params: HashMap<String, Any> = HashMap()
        params["limit"] = ProjectRepo.PAGE_LIMIT
        params["offset"] = offset
        params["queryVersion"] = 2
        if (filterValue != null && filterValue.type != ProjectFilterEnum.SCREEN_NULL.code) {
            val map = HashMap<String, Any>()
            filterValue.toMap(map)
            params["filter"] = map
        }
        if (sortValue != null && sortValue.code != ProjectSortEnum.SORT_NULL.code) {
            val map = HashMap<String, Any>()
            map["type"] = sortValue.code
            if (sortValue.value != null) {
                map["value"] = sortValue.value
            }
            params["sort"] = map
        }
        regionType?.addParams(params)
        userRole.addParams(params)
        taskStatus.addParams(params)

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            }),
            "work.task.findMyTasks.v4"
        )
    }

    /**
     * 返回我创建的
     */
    fun listMyCreate(
        offset: Int,
        userRole: TaskUserRole,
        taskStatus: TaskStatusEnum,
        callback: JoyWorkVMCallback
    ) {
        val params: HashMap<String, Any> = HashMap()
        params["limit"] = ProjectRepo.PAGE_LIMIT
        params["offset"] = offset
        params["queryVersion"] = 2
        userRole.addParams(params)
        taskStatus.addParams(params)

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            }),
            "work.task.findMyTasks.v4"
        )
    }

    /**
     * 获取风险问题列表
     */
    fun listIssue(
        offset: Int,
        limit: Int = 20,
        regionTypes: List<Int>? = null,
        callback: JoyWorkVMCallback
    ) {
        val params: HashMap<String, Any> = HashMap()
        params["limit"] = limit
        params["offset"] = offset
        if (regionTypes.isLegalList()) {
            params["regionTypes"] = regionTypes!!
        }

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            }),
            "work.task.findMyQuestionTasks.v3"
        )
    }

    /**
     * 安排
     * [planTime] 在计划块时必须得传
     */
    fun assign(
        taskId: String,
        target: BlockTypeEnum?,
        callback: JoyWorkAssignCallback,
        planTime: Long = -1
    ) {
        val params: HashMap<String, Any> = HashMap()
        params["planTime"] = planTime
        params["taskId"] = taskId
        if (target != null) {
            params["blockType"] = target.code
        }
        callback.onStart()
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.result(false, JoyWorkEx.filterErrorMsg(errorMsg), null)
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    JoyWorkMsgCenter.notifySectionUpdate()
                    callback.result(true, "", target)
                }
            }),
            "work.task.tasksSortUpdate.v2"
        )
    }

    /**
     * 更改 Joywork 状态
     */
    fun updateTaskStatusNew(
        taskId: String,
        targetStatus: TaskStatusEnum,
        info: String?,
        force: Boolean,
        callback: JoyWorkUpdateCallback,
    ) {
        val params: HashMap<String, Any> = HashMap()
        params["taskId"] = taskId
        params["taskStatus"] = targetStatus.code
        if (info.isLegalString()) {
            params["content"] = info!!
        }
        params["force"] = force
        callback.onStart()
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.result(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    JoyWorkMsgCenter.notifySectionUpdate()
                    callback.result(true, "")
                }
            }),
            "work.task.taskUpdateStatus.v2"
        )
    }

    /**
     * 还原
     */
    fun revertTask(taskId: String, callback: JoyWorkReverseCallback) {
        val params: HashMap<String, Any> = HashMap()
        params["taskId"] = taskId
        params["type"] = 2 // 递归还原
        callback.onStart()
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.result(false, null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    JoyWorkMsgCenter.notifySectionUpdate()
                    callback.result(true, rawData, "")
                }
            }),
            "work.task.revertTask.v2"
        )
    }

    /**
     * 快捷新建
     */
    fun create(
        callback: (JoyWorkDetail?, String?, String?) -> Unit,
        config: JoyWorkCreateParams.() -> Unit
    ) {
        val p = JoyWorkCreateParams()
        p.config()
        if (!p.check()) {
            return
        }
        p.postHandle()
        val map = HashMap(p.params)
        p.clear()
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JoyWorkDetail>(JoyWorkDetail::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(null, JoyWorkEx.filterErrorMsg(errorMsg), null)
                }

                override fun onSuccess(
                    jsonObject: JoyWorkDetail?,
                    tArray: List<JoyWorkDetail?>?,
                    rawData: String
                ) {
                    JoyWorkMsgCenter.notifySectionUpdate()
                    if (jsonObject == null) {
                        onFailure("")
                    } else {
                        callback.invoke(jsonObject, null, rawData)
                    }
                }
            }),
            p.url ?: "work.task.clientTaskSave.v2"
        )
    }

    /**
     * 批量新建子待办
     */
    fun batchCreateChildTasks(
        callback: (errorMsg: String?) -> Unit,
        config: JoyWorkCreateParams.() -> Unit
    ) {
        val p = JoyWorkCreateParams()
        p.config()
        if (!p.check()) {
            return
        }
        p.postHandle()
        val map = HashMap(p.params)
        p.clear()
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JoyWorkDetail>(JoyWorkDetail::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkDetail?,
                    tArray: List<JoyWorkDetail?>?,
                    rawData: String
                ) {
                    callback.invoke(null)
                }
            }),
            "work.task.batchCreateChildTasks.v2"
        )
    }

    /**
     * 新建催办
     */
    fun newUrge(callback: JoyWorkUpdateCallback, config: JoyWorkUrgeParam.() -> Unit) {
        urge(callback, config, true)
    }

    /**
     * 取消催办
     */
    fun cancelUrge(callback: JoyWorkUpdateCallback, config: JoyWorkUrgeParam.() -> Unit) {
        urge(callback, config, false)
    }

    /**
     * 催办
     */
    private fun urge(
        callback: JoyWorkUpdateCallback,
        config: JoyWorkUrgeParam.() -> Unit,
        new: Boolean
    ) {
        val param = JoyWorkUrgeParam()
        param.config()
        if (new) param.newUrge() else param.cancelUrge()
        if (!param.check()) {
            return
        }
        param.postHandle()
        val map = HashMap(param.params)
        param.params.clear()
        callback.onStart()
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.result(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    callback.result(true, "")
                }
            }),
            if (new) "work.task.batchTaskUrge.v2" else "work.task.taskUrge.v2"
        )
    }

    fun notifyUpdate(
        taskId: String,
        mapValue: Map<String, Any>,
        callback: (errorMsg: String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        map["params"] = mapValue
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<String>(String::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: String?,
                    tArray: List<String?>?,
                    rawData: String
                ) {
                    callback(null)
                }
            }),
            "work.task.updateNotice.v3"
        )
    }

    //配合服务端透传deepLink中的参数回服务端
    private fun appendActionSource(param: HashMap<String, Any>, actionSource: String?) {
        kotlin.runCatching {
            if (actionSource.isNullOrEmpty()) {
                return@runCatching
            }
            param["actionSource"] = Gson().fromJson<Map<String, Any>>(
                actionSource,
                object : TypeToken<Map<String?, Any?>?>() {
                }.type
            )
        }
    }

    /**
     * 添加协作人
     */
    fun addRelation(
        list: List<Members>,
        taskId: String,
        actionSource: String?,
        onSuccess: (List<Members>) -> Unit,
        onFailure: (String) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        map["members"] = ArrayList<Map<String, Any>>().apply {
            list.forEach {
                val tmp = HashMap<String, Any>()
                val ret = kotlin.runCatching {
                    tmp.putAll(it.toNetMap())
                }
                if (ret.isSuccess) {
                    tmp["userRole"] = TaskUserRole.OWNER.code
                    tmp["headImg"] = it.headImg ?: ""
                    tmp["realName"] = it.realName ?: ""
                    tmp["chief"] = it.chief ?: 0
                    tmp["depInfo"] = it.deptInfo
                    add(tmp)
                }
            }
        }
        appendActionSource(map, actionSource)
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<DelMemberSend>(DelMemberSend::class.java) {
                override fun onFailure(errorMsg: String?) {
                    onFailure(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: DelMemberSend?,
                    tArray: List<DelMemberSend?>?,
                    rawData: String
                ) {
                    onSuccess(jsonObject?.members ?: list)
                }
            }),
            "work.task.addTaskMembers.v2"
        )
    }

    fun updatePermission(taskId: String?, callback: (List<String?>?) -> Unit) {
        if (!taskId.isLegalString()) {
            return
        }
        val map = HashMap<String, Any>()
        map["taskId"] = taskId!!
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<String>(String::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback(null)
                }

                override fun onSuccess(
                    jsonObject: String?,
                    tArray: List<String?>?,
                    rawData: String
                ) {
                    callback(tArray)
                }
            }),
            "work.task.getUserPermission.v2"
        )
    }

    fun transfer(
        taskId: String,
        members: Members,
        callback: (success: Boolean, result: TransferResult?, errorMsg: String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        map["member"] = members.toNetMap()
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<TransferResult>(TransferResult::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback(false, null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: TransferResult?,
                    tArray: List<TransferResult?>?,
                    rawData: String
                ) {
                    if (jsonObject == null) {
                        onFailure("")
                    } else {
                        callback(true, jsonObject, null)
                    }
                }
            }),
            "work.task.transferTask.v3"
        )
    }

    fun reject(taskId: String, message: String, callback: (msg: String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        map["text"] = message
        HttpManager.color()
            .post(map, null, "joywork.rejectTask", object : SimpleRequestCallback<String>() {
                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    callback(info?.result)
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    callback(null)
                }
            })
    }

    fun handleChief(
        taskId: String,
        members: Members,
        isCancel: Boolean,
        callback: (success: Boolean, errorMsg: String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        val m = members.toNetMap()
        m["chief"] = if (isCancel) "0" else "1"
        map["user"] = m
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JSONObject>(JSONObject::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JSONObject?,
                    tArray: List<JSONObject?>?,
                    rawData: String
                ) {
                    callback(true, null)
                }
            }),
            "work.task.setChiefOwner.v3"
        )
    }

    fun sortJoyWork(taskId: String, param: JoyWorkLocationParam, callback: JoyWorkUpdateCallback) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        if (param.after != null) {
            map["after"] = param.after!!
        }
        if (param.front != null) {
            map["front"] = param.front!!
        }
        map["planTime"] = param.planTime ?: "-1"
        map["blockType"] = param.blockType
        callback.onStart()
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ResultWrapper>(ResultWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.result(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: ResultWrapper?,
                    tArray: List<ResultWrapper?>?,
                    rawData: String
                ) {
                    callback.result(true, rawData ?: "")
                }
            }),
            "work.task.tasksSortUpdate.v2"
        )
    }

    fun sortProjectJoyWork(
        taskId: String,
        groupId: String,
        projectId: String,
        param: JoyWorkLocationParam,
        callback: JoyWorkUpdateCallback
    ) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        if (param.after != null) {
            map["after"] = param.after!!
        }
        if (param.front != null) {
            map["front"] = param.front!!
        }
        map["groupId"] = groupId
        map["projectId"] = projectId
        callback.onStart()
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ResultWrapper>(ResultWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.result(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: ResultWrapper?,
                    tArray: List<ResultWrapper?>?,
                    rawData: String
                ) {
                    callback.result(true, rawData ?: "")
                }
            }),
            "work.task.projectTaskSortUpdate.v2"
        )
    }

    fun sortSubJoyWork(
        taskId: String,
        after: String?,
        front: String?,
        callback: (Boolean, String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        if (after != null) {
            map["after"] = after
        }
        if (front != null) {
            map["front"] = front
        }
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ResultWrapper>(ResultWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: ResultWrapper?,
                    tArray: List<ResultWrapper?>?,
                    rawData: String
                ) {
                    callback.invoke(true, rawData ?: "")
                }
            }),
            "work.task.childTasksSortUpdate.v2"
        )
    }

    fun merge(from: String, to: String, callback: (Boolean, ResultWrapper?) -> Unit) {
        val map = HashMap<String, Any>()
        map["from"] = from
        map["to"] = to
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ResultWrapper>(ResultWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    val wrapper = ResultWrapper()
                    wrapper.errorMsg = JoyWorkEx.filterErrorMsg(errorMsg)
                    callback.invoke(false, wrapper)
                }

                override fun onSuccess(responseInfo: ResponseInfo<String>?) {
                    try {
                        val wrapper = Gson().fromJson(responseInfo?.result, clazz)
                        if (wrapper.isOk) {
                            callback.invoke(true, null)
                        } else {
                            wrapper.errorMsg = JoyWorkEx.filterErrorMsg("")
                            callback.invoke(false, wrapper)
                        }
                    } catch (e: Exception) {
                        onFailure("")
                    }
                }
            }),
            "work.task.mergeTask.v2"
        )
    }

    /**
     * 添加父待办、变更父待办
     */
    fun addParentTask(
        parentId: String,
        taskId: String,
        callback: (Boolean, ResultWrapper?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["parentId"] = parentId
        map["taskId"] = taskId
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ResultWrapper>(ResultWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    val wrapper = ResultWrapper()
                    wrapper.errorMsg = JoyWorkEx.filterErrorMsg(errorMsg)
                    callback.invoke(false, wrapper)
                }

                override fun onSuccess(responseInfo: ResponseInfo<String>?) {
//                super.onSuccess(responseInfo)
                    try {
                        val wrapper = Gson().fromJson(responseInfo?.result, clazz)
                        if (wrapper.isOk) {
                            callback.invoke(true, null)
                        } else {
                            wrapper.errorMsg = JoyWorkEx.filterErrorMsg("")
                            callback.invoke(false, wrapper)
                        }
                    } catch (e: Exception) {
                        onFailure("")
                    }
                }
            }),
            "work.task.addParentTask.v2"
        )
    }

    fun cancelMerge(taskId: String, callback: (Boolean, ResultWrapper?) -> Unit) {
        val map = HashMap<String, Any>()
        map["taskId"] = taskId
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ResultWrapper>(ResultWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    val wrapper = ResultWrapper()
                    wrapper.errorMsg = JoyWorkEx.filterErrorMsg(errorMsg)
                    callback.invoke(false, wrapper)
                }

                override fun onSuccess(
                    jsonObject: ResultWrapper?,
                    tArray: List<ResultWrapper?>?,
                    rawData: String
                ) {
                    callback.invoke(true, null)
                }
            }),
            "work.task.cancelMerge.v2"
        )
    }

    /**
     * 获取个人信息
     */
    fun singleInfo(
        emplAccount: String,
        ddAppId: String,
        callback: (Boolean, JoyWorkUser?, msg: String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["emplAccount"] = emplAccount
        map["ddAppId"] = ddAppId
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object : AbsReqCallback<JoyWorkUser>(JoyWorkUser::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    user: JoyWorkUser?,
                    tArray: List<JoyWorkUser?>?,
                    rawData: String
                ) {
                    if (user != null) {
                        callback.invoke(true, user, null)
                    } else {
                        onFailure(null)
                    }
                }
            }),
            "work.getUserInfo"
        )
    }

    /**
     * 批量获取个人信息
     */
    fun batchInfo(
        members: List<Members>,
        callback: (Boolean, BatchUserInfo?, msg: String?) -> Unit
    ) {
        val listMap: List<Map<String, String>> = members.map {
            it.toNetMap()
        }
        val map = HashMap<String, Any>()
        map["users"] = listMap
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<BatchUserInfo>(
                    BatchUserInfo::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    user: BatchUserInfo?,
                    tArray: List<BatchUserInfo>?,
                    rawData: String
                ) {
                    if (user != null) {
                        callback.invoke(true, user, null)
                    } else {
                        onFailure(null)
                    }
                }
            }),
            "work.task.getUserInfoBatch.v2"
        )
    }

    /**
     * 筛选
     */
    fun groupList(
        callback: JoyWorkVMCallback,
        status: TaskStatusEnum,
        sessionId: String,
        offset: Int,
        userRole: TaskUserRole,
        limit: Int = 20
    ) {
        val params: HashMap<String, Any> = HashMap()
        status.addParams(params)
        userRole.addParams(params, "mode")
        params["offset"] = offset
        params["inventory"] = sessionId
        params["bizCode"] = JoyWorkSource.IM.bizCode
        params["limit"] = if (limit < 0) 20 else limit

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkWrapper>(object :
                AbsReqCallback<JoyWorkWrapper>(JoyWorkWrapper::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkWrapper?,
                    tArray: List<JoyWorkWrapper?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.call(jsonObject)
                    } ?: onFailure("")
                }
            }),
            "work.task.getTripGroupTasks.v2"
        )
    }

    fun getNums(
        callback: (num: JoyWorkNum?) -> Unit,
    ) {
        val params: HashMap<String, Any> = HashMap()

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkNum>(object :
                AbsReqCallback<JoyWorkNum>(JoyWorkNum::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback(null)
                }

                override fun onSuccess(
                    jsonObject: JoyWorkNum?,
                    tArray: List<JoyWorkNum?>?,
                    rawData: String
                ) {
                    callback(jsonObject)
                }
            }),
            "work.task.findMyTaskNums.v4"
        )
    }

    fun getNums2(
        callback: (taskNumList: Map<String, Int>?) -> Unit,
    ) {
        val params: HashMap<String, Any> = HashMap()

        HttpManager.color().post(
            params,
            HashMap(),
            "joywork.getTodoNums.v2",
            object : SimpleRequestCallback<String>() {
                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    val response = ApiResponse.parse<Map<String, String>>(
                        info?.result,
                        object : TypeToken<Map<String, String>>() {}.type
                    )
                    val taskNumListString = response.data.getOrDefault("taskNums", null)
                    val mapType = object : TypeToken<Map<String, Int>>() {}.type
                    val map: Map<String, Int> = JsonUtils.getGson().fromJson(taskNumListString, mapType)
                    callback(map)
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)

                }
            },
        )
    }

    fun getMyHandle(callback: (list: List<MenuBean>?) -> Unit) {
        HttpManager.color().post(
            HashMap(),
            HashMap(),
            "joywork.myHandleItems",
            object : SimpleRequestCallback<String>() {
                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    val response = ApiResponse.parse<Map<String, String>>(
                        info?.result,
                        object : TypeToken<Map<String, String>>() {}.type
                    )
                    val itemsString = response.data.getOrDefault("items", null)
                    val listType = object : TypeToken<List<MenuBean>>() {}.type
                    val list: List<MenuBean>? =
                        JsonUtils.getGson().fromJson(itemsString, listType)
                    callback(list)
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    callback(null)
                }
            },
        )
    }

    /**
     * 将指定人的指定待办完成
     */
    fun finishOrUnfinishTask(
        taskId: String,
        taskStatus: Int,
        members: Members?,
        callback: (msg: String, success: Boolean) -> Unit
    ) {
        val params: HashMap<String, Any> = HashMap()
        params["taskId"] = taskId
        params["taskStatus"] = taskStatus
        if (members != null) {
            params["member"] = members.toNetMap()
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<String>(object :
                AbsReqCallback<String>(String::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback(JoyWorkEx.filterErrorMsg(errorMsg), false)
                }

                override fun onSuccess(
                    jsonObject: String?,
                    tArray: List<String?>?,
                    rawData: String
                ) {
                    callback(jsonObject ?: "", true)
                }
            }),
            "work.task.taskUpdateStatus.v2"
        )
    }

    /**
     * 主要关注或取消关注待办
     */
    fun focusOrUnForce(
        taskId: String,
        isForce: Boolean,
        callback: (msg: String, success: Boolean) -> Unit
    ) {
        val key = if (isForce) JoyWorkConstant.DETAIL_FOCUS else JoyWorkConstant.DETAIL_UNFOCUS
        JDMAUtils.onEventClick(key, key)
        val params: HashMap<String, Any> = HashMap()
        params["taskId"] = taskId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JSONObject>(JSONObject::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback(JoyWorkEx.filterErrorMsg(errorMsg), false)
                }

                override fun onSuccess(
                    jsonObject: JSONObject?,
                    tArray: List<JSONObject?>?,
                    rawData: String
                ) {
                    callback("", true)
                }
            }),
            if (isForce) "work.task.followTask.v3" else "work.task.noFollowTask.v3"
        )
    }
}

class JoyWorkLocationParam {
    var after: String? = null
    var front: String? = null
    var blockType: String = BlockTypeEnum.INBOX.code
    var planTime: String? = "-1"
}

abstract class JoyWorkVMCallback {
    open fun call(wrapper: JoyWorkWrapper) {

    }

    open fun call(wrapper: JoyWorkWrapper, rawData: String) {

    }

    abstract fun onError(msg: String)
}

interface JoyWorkAssignCallback {
    fun result(success: Boolean, errorMsg: String, target: BlockTypeEnum?)
    fun onStart()
}

interface JoyWorkUpdateCallback {
    fun result(success: Boolean, errorMsg: String)
    fun onStart()
}

interface JoyWorkReverseCallback {
    fun result(success: Boolean, result: String?, errorMsg: String)
    fun onStart()
}

object JoyWorkParamsBuilder {

    val tmp: HashMap<String, Any> = HashMap()
    var types: Array<RegionTypeEnum>? = null

    /**
     * 为每一种 BlockTypeEnum 指定加载的数量
     */
    var limits: Array<Int>? = null

    /**
     * 初始化一次性参数
     */
    fun init() {
        tmp["limit"] = 20
        tmp["offset"] = 0
    }

    /**
     * 设置加载的块，以及每一块加载数量。如果每一块的数量不同，可使用 [setBlockType] 以及 [setLimit] 配合
     */
    fun setBlockTypeAndLimit(
        types: Array<RegionTypeEnum>,
        limitFunction: (RegionTypeEnum) -> Int = { _ -> 20 }
    ) {
        JoyWorkParamsBuilder.types = types
        limits = Array(types.size) {
            limitFunction(types[it])
        }
    }

    /**
     * 设置加载的块
     */
    fun setBlockType(types: Array<RegionTypeEnum>) {
        JoyWorkParamsBuilder.types = types
    }

    /**
     * 设置每一块要加载的数量
     */
    fun setLimit(limit: Int = 20) {
        tmp["limit"] = limit
    }

    fun setTaskStatus(status: TaskStatusEnum) {
        status.addParams(tmp)
    }

    fun setRole(role: TaskUserRole) {
        role.addParams(tmp)
    }

    fun setOffset(offset: Int) {
        tmp["offset"] = offset
    }

    fun setUnPlanCount(isUnPlanCount: Boolean) {
        tmp["unPlanCount"] = isUnPlanCount
    }

    fun clear() {
        tmp.clear()
        types = null
        limits = null
    }
}


class JoyWorkCreateParams {
    var url: String? = null

    val params: HashMap<String, Any> = HashMap()

    var dupEnum: DuplicateEnum? = null
        set(value) {
            value?.apply {
                params["cycle"] = this.value
            }
            field = value
        }

    var title: String? = null
        set(value) {
            value?.apply {
                params["title"] = this
            }
            field = value
        }

    var endTime: Long? = null
        set(value) {
            field = value
            value?.apply {
                params["endTime"] = "$this"
            }
        }
    var startTime: Long? = null
        set(value) {
            field = value
            value?.apply {
                params["startTime"] = "$this"
            }
        }
    var remindTime: Long? = null
        set(value) {
//            field = value
//            value?.apply {
//                params["remindTime"] = "$this"
//            }
        }

    var taskListType: TaskListTypeEnum? = null
        set(value) {
            field = value
            if (value != null) {
                params["taskListType"] = value.code
            }
        }

    var tripartiteName: String? = null
        set(value) {
            if (value.isLegalString()) {
                params["tripartiteName"] = value!!
            }
            field = value
        }

    var owners: List<JoyWorkUser>? = null
        set(value) {
            field = value
            if (value.isLegalList()) {
                val list = ArrayList<Map<String, Any>>()
                value?.forEach {
                    val m = it.toSimpleMap().apply {
//                        this["taskUserRole"] = TaskUserRole.OWNER.code
//                        this["userRole"] = TaskUserRole.OWNER.code
                    }
                    list.add(m)
                }
                params["owners"] = list
            }
        }


    var from: HashMap<String, Any>? = null
        set(value) {
            field = value
            value?.apply {
                params["from"] = value
            }
        }
    var content: String? = null
        set(value) {
            field = value
            value?.apply {
                params["content"] = value
                params["mobileContent"] = value
            }
        }
    var contentOnly: String? = null
        set(value) {
            field = value
            value?.apply {
                params["content"] = value
            }
        }

    var mobileContent: String? = null
        set(value) {
            field = value
            value?.apply {
                params["mobileContent"] = value
            }
        }

    var groupId: String? = null
        set(value) {
            field = value
            value?.apply {
                params["groupId"] = value
            }
        }

    var groupTitle: String? = null
        set(value) {
            field = value
            value?.apply {
                params["groupTitle"] = value
            }
        }

    var projectId: String? = null
        set(value) {
            field = value
            value?.apply {
                params["projectId"] = value
            }
        }

    var parentId: String? = null
        set(value) {
            field = value
            value?.apply {
                params["parentTaskId"] = this
            }
        }

    var bizCode: String? = null
        set(value) {
            field = value
            value?.apply {
                params["bizCode"] = this
            }
        }

    // 咚咚群号
    var inventory: String? = null
        set(value) {
            field = value
            value?.apply {
                params["inventory"] = this
            }
        }

    var des: String? = null
        set(value) {
            field = value
            value?.apply {
                params["remark"] = this
            }
        }

    var shimo: List<Documents>? = null
        set(value) {
            field = value
            value?.apply {
                params["documents"] = this
            }
        }

    var resources: List<Resources>? = null
        set(value) {
            field = value
            value?.apply {
                params["resources"] = this
            }
        }

    var executors: List<Members>? = null
        set(value) {
            field = value
            val list = ArrayList<Map<String, Any>>()
            value?.forEach {
                val m = it.toSimpleMap()
                list.add(m)
            }
            params["executors"] = list
//
//            value?.apply {
//                params["executors"] = this
//            }
        }

    var priorityType: Int? = null
        set(value) {
            field = value
            value?.apply {
                params["priorityType"] = this
            }
        }

    var projects: List<JoyWorkDetail.Project>? = null
        set(value) {
            field = value
            value?.apply {
                val list = mutableListOf<Map<String, String>>()
                forEach {
                    val m = HashMap<String, String>()
                    m["projectId"] = it.projectId
                    m["groupId"] = it.groupId
                    list.add(m)
                }
                params["projects"] = list
            }
        }

    var childOwners: List<UrgeUserBase>? = null
        set(value) {
            field = value
            value?.apply {
                params["childOwners"] = this
            }
        }

    var imMessage: Boolean? = null
        set(value) {
            if (value != null) {
                // For compatibility with older versions, 【imMessage】 must always be true.
                // In the older version, if 【imMessage】 is true, the app should send the message cards by itself.
                // otherwise, the server will send notifications to users.
                // However starting from 2022/10/08, all message cards and notifications will be sent by the server.
                // So we need a param which is named 【interactiveCard】 to diff the user had checked the checkbox or not
                params["imMessage"] = true
                params["interactiveCard"] = value
            }
            field = value
        }

    var goalsAndKrs: List<Any>? = null
        set(value) {
            if (value.isLegalList()) {
                val r = value?.filter {
                    it is KpiTarget || it is KR
                }?.map {
                    val m = mutableMapOf<String, String>()
                    m["goalId"] = if (it is KpiTarget) it.goalId else (it as KR).krId
                    m["type"] = if (it is KpiTarget) "1" else "2"
                    m
                }
                params["goals"] = r!!
                field = value
            }
        }

    var alertTypes: Set<AlertType>? = null
        set(value) {
            if (value.isLegalSet()) {
                params["remindStr"] = AlertType.valueToString(value!!)
                field = value
            }
        }

    var groupDefaultProjectId: String? = null
        set(value) {
            field = value
            value?.apply {
                params["groupDefaultProjectId"] = this
            }
        }

    var interactiveCard: Boolean? = null
        set(value) {
            field = value
            value?.apply {
                params["interactiveCard"] = this
            }
        }

    fun postHandle() {
        // 创建子待办
        if (params.containsKey("parentTaskId")) {
            params["isChild"] = true
        }
        if (!params.containsKey("imMessage")) {
            params["imMessage"] = false
        }
    }

    fun clear() {
        params.clear()
        // 其余属性不清理也可，也不使用
    }

    fun check(): Boolean {
        if (!params.containsKey("blockType")) { // 负责人是别人时，不会传入 blockType。但接口需要，所以这里补一下
            params["blockType"] = 0
        }
        return params.containsKey("title")
    }
}

class JoyWorkUrgeParam {

    private val NEW_URGE = 1
    private val CANCEL_URGE = 2

    val params: HashMap<String, Any?> = HashMap()

    fun newUrge() {
        params["type"] = NEW_URGE
    }

    fun cancelUrge() {
        params["type"] = CANCEL_URGE
    }

    // 催办 id
    var urgeId: String? = null
        set(value) {
            params["urgeId"] = value
            field = value
        }

    // JoyWork 的 id
    var taskId: String? = null
        set(value) {
            params["taskId"] = value
            field = value
        }

    // 催办内容
    var urgeContent: String? = null
        set(value) {
            params["urgeContent"] = value
            field = value
        }

    var urgePerson: List<JoyWorkUser>? = null
        set(value) {
            params["urgePerson"] = value
            field = value
        }


    var urgeUsers: List<UrgeUserBase>? = null
        set(value) {
            params["urgeUsers"] = value
            field = value
        }

    var cc: List<UrgeUserBase>? = null
        set(value) {
            if (value.isLegalList()) {
                params["cc"] = value
            }
            field = value
        }

    fun clear() {
        params.clear()
    }

    fun postHandle() {
        params.remove("type")
    }

    fun check(): Boolean {
        // 先检查不同操作时的独有参数
        when (params["type"]) {
            NEW_URGE -> {
                if (!params["urgeContent"].isLegalString()) throw IllegalArgumentException("新建时 urgeContent 不能为空")

            }

            CANCEL_URGE -> {
                if (!params["urgeId"].isLegalString()) throw IllegalArgumentException("取消时 urgeId 不能为空")
            }

            else -> {
                throw IllegalArgumentException("不支持 ${params["type"]} 操作，值要么是 $NEW_URGE 要么是 $CANCEL_URGE")
            }
        }
//        if (!params["taskId"].isLegalString()) {
//            throw IllegalArgumentException("taskId 不能为空")
//        }
//        if (!params["urgePerson"].isLegalList()) {
//            throw IllegalArgumentException("urgePerson 不能为空")
//        }
        return true
    }
}