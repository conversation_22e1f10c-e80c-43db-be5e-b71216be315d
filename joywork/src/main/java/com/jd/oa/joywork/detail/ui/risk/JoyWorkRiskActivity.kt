package com.jd.oa.joywork.detail.ui.risk

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.TextView
import com.jd.oa.BaseActivity
import com.jd.oa.annotation.Navigation
import com.jd.oa.joywork.*
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.TaskDetailWebservice.TaskCallback
import com.jd.oa.joywork.string
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.utils.*
import com.jd.oa.joywork.R

@Navigation(hidden = true)
class JoyWorkRiskActivity : BaseActivity(), View.OnClickListener {
    companion object {
        const val KEY = "riskContent"
        const val ID = "id"
        const val TYPE = "type"
        const val IS_RESOLVE = "resolve"
        fun getKey(): String {
            return KEY;
        }

        fun getTypeKey(): String = TYPE

        fun JoyWorkRiskActivity.getData(): String? {
            return intent.getStringExtra(KEY)
        }

        fun JoyWorkRiskActivity.getType(): RiskEnum {
            return RiskEnum.valueByCode(intent.getIntExtra(TYPE, RiskEnum.NORMAL.code))
        }

        fun JoyWorkRiskActivity.getId(): String? {
            return intent.getStringExtra(ID)
        }

        /**
         * 是否是解决
         */
        fun JoyWorkRiskActivity.isResolve(): Boolean {
            return intent.getBooleanExtra(IS_RESOLVE, false)
        }
    }

    private var mSendView: TextView? = null
    private var mBackView: View? = null
    private lateinit var riskEnum: RiskEnum
    private lateinit var mContentView: EditText

    private fun getTitleMsg(): Int {
        if (riskEnum == RiskEnum.NORMAL && isResolve()) {
            return R.string.joywork_resolve_title
        }
        return riskEnum.msgTitle()
    }

    private fun getHintMsg(): Int {
        if (riskEnum == RiskEnum.NORMAL && isResolve()) {
            return R.string.joywork_resolve_hint
        }
        return riskEnum.msgHint()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.joywork_activity_des)
        ActionBarHelper.init(this)
        riskEnum = getType()
        CmnTitleHelper.handleTitle(
            this,
            getTitleMsg(),
            R.string.me_save,
            findViewById(R.id.title)
        ) { back, _, action ->
            mSendView = action
            mSendView!!.setTextColor(ColorEx.addItem(ColorEx.Item().apply {
                enable = true
                color = <EMAIL>(R.color.joywork_red)
            }, ColorEx.Item().apply {
                enable = false
                color = Color.parseColor("#80FE3B30")
            }))
            mSendView!!.setOnClickListener(this@JoyWorkRiskActivity)
            mBackView = back
            back.setOnClickListener(this@JoyWorkRiskActivity)
        }
        mContentView = findViewById(R.id.content)
        mContentView.setHint(getHintMsg())
        mContentView.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                mSendView?.isEnabled = s?.run {
                    mContentView.text.toString().isLegalString()
                } ?: false
            }
        })
        mContentView.setText(getData() ?: "")
        mContentView.setCursorEnd()
        mSendView?.isEnabled = getData().isLegalString()
    }

    override fun onClick(v: View?) {
        when (v) {
            mBackView -> {
                exit()
            }
            mSendView -> {
                // 保存
                val des = mContentView.text.toString().trim()
                if (des.isLegalString()) {
                    if (getId().isLegalString()) {
                        // 有 id，需要将描述存储到待办中
                        TaskDetailWebservice.updateRisk(
                            getId(),
                            getType(),
                            des,
                            null,
                            object : TaskCallback() {
                                override fun onSuccess(info: ResponseInfo<String>?) {
                                    super.onSuccess(info)
                                    if (!hasError) {
                                        ToastUtils.showInfoToast(R.string.me_save_success)
                                        saveSuccess()
                                    }
                                }
                            })
                    } else {
                        // 直接返回
                        saveSuccess()
                    }
                }
            }
        }
    }

    private fun saveSuccess() {
        setResult(Activity.RESULT_OK, Intent().apply {
            putExtra(KEY, mContentView.text.toString().trim())
            putExtra(TYPE, riskEnum.code)
        })
        finish()
    }

    override fun onBackPressed() {
        if (mContentView.string().isLegalString()) {
            JoyWorkDialog.showAlertDialog(
                this,
                R.string.joywork_project_create_cancle_tip,
                R.string.joywork_urge_tips_ok
            ) {
                super.onBackPressed()
            }
        } else {
            super.onBackPressed()
        }
    }

    private fun exit() {
        if (mContentView.string().isLegalString()) {
            JoyWorkDialog.showAlertDialog(
                this,
                R.string.joywork_project_create_cancle_tip,
                R.string.joywork_urge_tips_ok
            ) {
                super.finish()
            }
        } else {
            super.finish()
        }
    }
}