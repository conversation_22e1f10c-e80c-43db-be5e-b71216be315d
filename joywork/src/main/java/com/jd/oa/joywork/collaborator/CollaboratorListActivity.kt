package com.jd.oa.joywork.collaborator

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.detail.DialogManager
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.data.TaskDetailRepository
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.entity.DelMemberSend
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.string
import com.jd.oa.utils.visible
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class CollaboratorListActivity : AppCompatActivity() {
    private lateinit var rv: RecyclerView

    private val scope = MainScope()

    companion object {

        fun inflateIntent(intent: Intent, taskId: String, canUpdate: Boolean) {
            intent.putExtra("taskId", taskId)
            intent.putExtra("canUpdate", canUpdate)
        }

        fun isCanUpdate(intent: Intent, canUpdate: Boolean) {
            intent.putExtra("canUpdate", canUpdate)
        }

        fun setSessionId(intent: Intent, sessionId: String?) {
            if (sessionId.isLegalString()) {
                intent.putExtra("sessionId", sessionId)
            }
        }

        fun CollaboratorListActivity.canUpdate(): Boolean {
            return intent!!.getBooleanExtra("canUpdate", false)
        }

        private fun CollaboratorListActivity.getJoyWorkId(): String? {
            return intent!!.getStringExtra("taskId")
        }

        private fun CollaboratorListActivity.getSessionId(): String? {
            return intent!!.getStringExtra("sessionId")
        }

        private fun getExecutors(): ArrayList<Members> {
            return (OneTimeDataRepo.data as? List<*>)?.run {
                return if (isEmpty()) {
                    ArrayList()
                } else {
                    if (first() is Members) {
                        ArrayList(this as List<Members>)
                    } else {
                        ArrayList()
                    }
                }
            } ?: ArrayList()
        }
    }

    private val members: ArrayList<Members> by lazy {
        getExecutors()
    }

    /**
     * 最终返回给调用者的用户集合
     */
    private val result: ArrayList<Members> = ArrayList()
    private val titleView: TextView by lazy {
        findViewById(R.id.title)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        result.addAll(members)
        kotlin.runCatching {
            actionBar?.hide()
        }
        kotlin.runCatching {
            supportActionBar?.hide()
        }
        setContentView(R.layout.joywork_collaborator_activity)
        updateTitle()
        findViewById<View>(R.id.back).setOnClickListener { finish() }
        if (canUpdate()) {
            findViewById<View>(R.id.add).setOnClickListener {
                add()
            }
        } else {
            findViewById<View>(R.id.add).gone()
        }
        rv = findViewById<RecyclerView>(R.id.rv).apply {
            layoutManager = LinearLayoutManager(
                this@CollaboratorListActivity,
                LinearLayoutManager.VERTICAL,
                false
            )

            adapter = if (members.isEmpty()) {
                JoyWorkEmptyAdapter.empty(
                    this@CollaboratorListActivity,
                    R.string.joywork_relation_empty,
                    R.drawable.joywork_executor_empty
                )
            } else {
                CollaboratorListAdapter(this@CollaboratorListActivity, members)
            }
            getInfo()
        }
    }

    private fun updateTitle() {
        titleView.text = string(R.string.joywork_relation) + "(" + result.size + ")"
    }

    private fun add() {
        val selected = ArrayList<MemberEntityJd>()
        if (result.isNotEmpty()) {
            result.forEach {
                it.emplAccount?.apply {
                    val jd = MemberEntityJd()
                    jd.mApp = it.app
                    jd.mId = it.emplAccount
                    selected.add(jd)
                }
            }
        }
        ExecutorUtils.selectRelation(this, selected, getSessionId(), { jds ->
            if (jds.isLegalList()) {
                val membersNet: ArrayList<Members> = ArrayList()
                for (memberEntityJd in jds!!) {
                    memberEntityJd?.apply {
                        val member = Members()
                        member.fromDD(memberEntityJd)
                        member.userRole = TaskUserRole.EXECUTOR.code
                        member.teamId = JoyWorkUser.DEFAULT_TEAM_ID
                        membersNet.add(member)
                    }
                }
                if (membersNet.isEmpty()) {
                    return@selectRelation
                }
                if (getJoyWorkId() != null) {
                    JoyWorkRepo.addRelation(membersNet, getJoyWorkId()!!, null, {
                        // 添加协作人后，更新本地所有数据
                        addIfAbsent(result, it)
                        (rv.adapter as? CollaboratorListAdapter)?.apply {
                            updateCollaborator(it)
                            updateTitle()
                        } ?: showContent(result)
                    }, { s ->
                        ToastUtils.showInfoToast(s)
                    })
                } else {
                    addIfAbsent(result, membersNet)
                    (rv.adapter as? CollaboratorListAdapter)?.apply {
                        updateCollaborator(membersNet)
                        updateTitle()
                    } ?: showContent(result)
                    getInfo()
                }
            }
        }, {

        })
    }

    private fun getInfo() {
        // 过滤掉已有部门信息的人员
        val needNet = result.filter {
            listOf(it.deptInfo?.deptName, it.titleName).hasNull()
        }
        if (!needNet.isLegalList()) {
            return
        }
        scope.launch {
            val r = getBatchInfo(needNet)
            r.ifSuccessSilence {
                (rv.adapter as? CollaboratorListAdapter)?.apply {
                    updateInfo(it?.users ?: ArrayList<Members>())
                }
            }
        }
    }

    /**
     * 将 [src] 中去重复制到 [dst] 中。
     * 同时，会使用 [dst] 中的部分信息填充 [src]
     */
    private fun addIfAbsent(dst: ArrayList<Members>, src: List<Members>?) {
        src?.forEach { outer ->
            val first = dst.firstOrNull { inner ->
                inner.emplAccount == outer.emplAccount
            }
            if (first == null) {
                dst.add(outer)
            } else {
                if (first.deptInfo.deptName.isLegalString() && !outer.deptInfo.deptName.isLegalString()) {
                    outer.deptInfo = first.deptInfo
                }
            }
        }
    }

    private fun showContent(all: ArrayList<Members>) {
        updateTitle()
        rv.adapter = CollaboratorListAdapter(this@CollaboratorListActivity, all)
    }

    private fun showRemoveDialog(member: Members) {
        DialogManager.showRemove(this@CollaboratorListActivity) {
            removeNet(member)
        }
    }

    // 移除协作人
    private fun removeNet(member: Members) {
        if (getJoyWorkId() != null) {
            val send = DelMemberSend()
            send.taskId = getJoyWorkId()
            send.members = listOf(member)
            TaskDetailRepository.getInstance()
                .delTaskMembers(send, object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        if (!hasError) {
                            result.removeAll {
                                it.emplAccount == member.emplAccount
                            }
                            (rv.adapter as CollaboratorListAdapter).removeCollaborator(member)
                            updateTitle()
                        }
                    }
                })
        } else {
            result.removeAll {
                it.emplAccount == member.emplAccount
            }
            (rv.adapter as? CollaboratorListAdapter)?.removeCollaborator(member)
            updateTitle()
        }
    }

    override fun finish() {
        scope.cancel()
        OneTimeDataRepo.data = result
        setResult(Activity.RESULT_OK)
        super.finish()
    }

    fun showEmpty() {
        rv.adapter = JoyWorkEmptyAdapter.empty(
            this@CollaboratorListActivity,
            R.string.joywork_relation_empty,
            R.drawable.joywork_executor_empty
        )
    }

    inner class CollaboratorListAdapter(val context: Context, dataC: ArrayList<Members>) :
        RecyclerView.Adapter<VH>() {

        private val data = ArrayList<Members>(dataC)

        private val listener = View.OnClickListener {
            val c = it.tag as Members
            showRemoveDialog(c)
        }

        fun updateInfo(members: List<Members>) {
            members.forEach { ms ->
                data.firstOrNull {
                    it.emplAccount == ms.emplAccount
                }?.apply {
                    deptInfo = ms.deptInfo
                    titleName = ms.titleInfo?.titleName
                }
            }
            notifyDataSetChanged()
        }

        fun updateCollaborator(members: List<Members>) {
            addIfAbsent(data, members)
            notifyDataSetChanged()
        }

        fun removeCollaborator(c: Members) {
            val index = data.indexOfFirst {
                it.isSamePerson(c)
            }
            if (index >= 0) {
                data.removeAt(index)
                if (data.isEmpty()) {
                    showEmpty()
                } else {
                    notifyItemRemoved(index)
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
            val inflate: View = LayoutInflater.from(context)
                .inflate(R.layout.joywork_collaborator_activity_item, parent, false)
            return VH(inflate)
        }

        override fun getItemCount() = data.size

        override fun onBindViewHolder(holder: VH, position: Int) {
            val c = data[position]
            holder.name.text = c.realName ?: ""
            holder.department.text = listOf(c.deptInfo?.deptName, c.titleName).joinLegalString()
            holder.divider.visibility = if (position == data.size - 1) View.GONE else View.VISIBLE
            if (c.userRole == TaskUserRole.EXECUTOR.code && canUpdate()) {
                holder.action.visible()
                holder.action.tag = c
                holder.action.setOnClickListener(listener)
            } else {
                holder.action.gone()
            }
            JoyWorkViewItem.avatar(holder.avatar, c.headImg)
            holder.avatar.tag = c
            holder.avatar.setOnClickListener {
                val m = it.tag as Members
                <EMAIL>(m.ddAppId, m.emplAccount)
            }
        }

        fun clean() {
            data.clear()
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView by lazy { view.findViewById<ImageView>(R.id.image) }
        val name: TextView by lazy { view.findViewById<TextView>(R.id.name) }
        val action: TextView by lazy { view.findViewById<TextView>(R.id.action) }
        val department: TextView by lazy { view.findViewById<TextView>(R.id.department) }
        val divider: View by lazy { view.findViewById<View>(R.id.divider) }
    }
}

