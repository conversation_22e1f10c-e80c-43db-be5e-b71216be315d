package com.jd.oa.joywork.self.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.repo.CacheStrategy
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.repo.LoadingViewStrategy
import com.jd.oa.joywork.self.base.adapter.SelfBaseFragmentAdapter
import com.jd.oa.joywork.self.base.adapter.vh.GroupVHItf
import com.jd.oa.joywork.self.base.adapter.vh.ItemVHItf
import com.jd.oa.joywork.self.group.JoyWorkGroupEx
import com.jd.oa.joywork.view.swipe.SwipeMenuParent
import com.jd.oa.utils.ToastUtils

/**
 * 2022年04月09日：个人待办 我负责的、我指派的、我协作的父类。界面内容会分为几大块。
 * 每一块都有单独的加载更多，所以此界面不包含加载更多逻辑
 */
abstract class SelfBaseBlockFragment : SelfBaseFragment(), GroupVHItf, ItemVHItf {

    // 移动到另一个文件。如果放在当前界面，编译时会很卡。
    protected lateinit var adapterGroups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>

    private var mSwipeParent: SwipeMenuParent? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        adapterGroups = getGroups()
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onRefreshData(
        loadingViewStrategy: LoadingViewStrategy,
        strategy: CacheStrategy,
        netType: NetType
    ) {
        if (isSwipeRefreshLayoutInited && loadingViewStrategy.show()) {
            mSwipeRefreshLayout.isRefreshing = true
        }
        val map = countGroupLength()
        JoyWorkRepo.listWithCache(
            strategy,
            if (activity == null) null else sp,
            cacheKey = if (activity == null) null else getCacheKey(),
            getNetCallback(netType)
        ) {
            setBlockTypeAndLimit(RegionTypeEnum.values()) {
                map[it] ?: 20
            }
            setTaskStatus(getDefaultStatus())
            setRole(getUserRole())
        }
    }

    protected fun getNetCallback(netType: NetType): JoyWorkVMCallback {
        return object : JoyWorkVMCallback() {
            override fun call(wrapper: JoyWorkWrapper) {
                if (isSwipeRefreshLayoutInited) {
                    mSwipeRefreshLayout.isRefreshing = false
                }
                val tmpGroups = getGroups()
                tmpGroups.forEach {
                    it.title.count = 0
                    it.ensureRealItems()
                    it.realItems.clear()
                }
                wrapper.clientTasks?.forEach {
                    try {
                        tmpGroups.firstOrNull { g ->
                            g.id == "${it.blockType}"
                        }?.apply {
                            title.count = it.total
                            realItems.clear()
                            realItems.addAll(it.tasks)
                            val size = JoyWorkEx.countJoyWork(realItems)
                            if (size >= 20) {
                                realItems.add(LoadMoreJoyWork(it.blockType))
                            }
                            notifyItemChange()
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                tmpGroups.removeAll {
                    !it.realItems.isLegalList()
                }
                tmpGroups.forEach { outer ->
                    if (netType == NetType.INIT) {
                        outer.expand = expand(outer.id)
                    } else if (netType == NetType.REFRESH) { // 下拉刷新
                        if (rv.adapter is SelfBaseFragmentAdapter) {
                            outer.expand =
                                adapterGroups.firstOrNull { outer.id == it.id }?.expand ?: false
                        } else {
                            outer.expand = true
                        }
                    }
                }
                adapterGroups.clear()
                adapterGroups.addAll(tmpGroups)
                if (adapterGroups.isLegalList()) {
                    (rv.adapter as? SelfBaseFragmentAdapter)?.replaceAll(adapterGroups)
                        ?: setAdapter(adapterGroups)
                } else {
                    rv.adapter = getEmptyAdapter()
                }
            }

            override fun onError(msg: String) {
                if (isSwipeRefreshLayoutInited) {
                    mSwipeRefreshLayout.isRefreshing = false
                }
                if (rv.adapter != null) {
                    if (netType == NetType.REFRESH) {
                        ToastUtils.showInfoToast(msg)
                    } else if (netType == NetType.INIT) {
                        rv.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg = msg)
                    }
                } else {
                    rv.adapter = JoyWorkEmptyAdapter.error(requireContext(), errorMsg = msg)
                }
            }
        }
    }

    protected open fun getEmptyAdapter(): JoyWorkEmptyAdapter {
        return JoyWorkEmptyAdapter.empty(requireContext())
    }

    protected open fun getGroups(): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return JoyWorkGroupEx.getGroups(requireActivity())
    }

    protected open fun expand(id: String): Boolean {
        return true
    }

    protected fun setAdapter(g: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>): SelfBaseFragmentAdapter {
        val rvAdapter =
            SelfBaseFragmentAdapter(
                this@SelfBaseBlockFragment,
                this,
                this,
                g,
                requireActivity()
            )
        rvAdapter.loadMoreCallback = ::loadMore
        rv.adapter = rvAdapter
        return rvAdapter
    }

    private fun countGroupLength(): HashMap<RegionTypeEnum, Int> {
        return HashMap<RegionTypeEnum, Int>().apply {
            RegionTypeEnum.values().forEach {
                this[it] = 20
            }
        }
    }

    private fun loadMore(blockType: String, callback: JoyWorkVMCallback) {
        val list = adapterGroups.firstOrNull {
            it.id == blockType
        }?.realItems ?: ArrayList(0)
        val count = JoyWorkEx.countJoyWork(list)
        onLoadMore(blockType, callback, count)
    }

    protected open fun onLoadMore(blockType: String, callback: JoyWorkVMCallback, offset: Int) {
        JoyWorkRepo.list(callback, null, null) {
            setBlockType(arrayOf(RegionTypeEnum.getObj(blockType)))
            setTaskStatus(getDefaultStatus())
            setRole(getUserRole())
            setOffset(offset)
        }
    }

    /**
     * 获取当前界面用户角度，用于区分是 我负责的、我指派的、我协作的
     */
    abstract fun getUserRole(): TaskUserRole

    /**
     * 获取待办状态。
     */
    abstract fun getDefaultStatus(): TaskStatusEnum

    override fun onPageSelected() {
        super.onPageSelected()
        // onRefreshData(LoadingViewStrategy.HIDE, CacheStrategy.WRITE, NetType.REFRESH)
    }

    override fun getTitleType(joyWorkTitle: JoyWorkTitle): Int {
        if (joyWorkTitle.expandableGroup.id == RegionTypeEnum.OVERDUE.code) {
            return 3
        } else if (joyWorkTitle.expandableGroup.id == RegionTypeEnum.FINISH.code) {
            return 1
        } else {
            return 2
        }
    }

    override fun onConfigGroupTitle(indicator: View) {

    }

    override fun expandable(): Boolean {
        return true
    }

    override fun getTimeType(): Int {
        return 1
    }

    override fun showAvatar(): Boolean {
        return false
    }

    override fun getMarginH(): Int {
        return resources.getDimensionPixelOffset(R.dimen.joywork_8dp)
    }

    override fun remove(joyWork: JoyWork): Boolean {
        return false
    }

    override fun showFooterDivider(): Boolean {
        return true
    }
}