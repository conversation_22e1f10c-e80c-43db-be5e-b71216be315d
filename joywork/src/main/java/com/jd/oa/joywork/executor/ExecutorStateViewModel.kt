package com.jd.oa.joywork.executor

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.*
import com.jd.oa.joywork.detail.data.entity.Owner
import com.jd.oa.joywork.detail.isSamePerson
import com.jd.oa.joywork.detail.key
import java.util.*
import kotlin.collections.ArrayList

class ExecutorStateViewModel : ViewModel() {
    var taskId: String? = null

    private val _permissionChangeLiveData = MutableLiveData<Boolean>()
    val permissionChangeLiveData: LiveData<Boolean> = _permissionChangeLiveData

    var permission: Int = 0

    fun canUrge(): Boolean {
        return permission and ExecutorStateListActivity.permission_urgeable != 0
    }

    fun canRemove(): Boolean {
        return permission and ExecutorStateListActivity.permission_deletable != 0
    }

    fun canAdd(): Boolean {
        return permission and ExecutorStateListActivity.permission_addable != 0
    }

    fun canFinish(): Boolean {
        return permission and ExecutorStateListActivity.permission_finishable != 0
    }

    fun canUnfinish(): Boolean {
        return permission and ExecutorStateListActivity.permission_unfinishable != 0
    }

    fun canTransfer(): Boolean {
        return permission and ExecutorStateListActivity.permission_transfer != 0
    }

    fun canExChief(): Boolean {
        return permission and ExecutorStateListActivity.permission_ex_chief != 0
    }

    fun updatePermission(ps: List<String>) {
        permission = 0
        if (hasSpecialPermission(JoyWorkOp.URGE, true, ps)) {
            permission = permission or ExecutorStateListActivity.permission_urgeable
        }
        if (hasSpecialPermission(JoyWorkOp.ASSIGN, true, ps)) {
            permission = permission or ExecutorStateListActivity.permission_addable
        }
        if (hasSpecialPermission(JoyWorkOp.ASSIGN, true, ps)) {
            permission = permission or ExecutorStateListActivity.permission_deletable
        }
        if (hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, true, ps)) {
            permission = permission or ExecutorStateListActivity.permission_finishable
        }
        if (hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, true, ps)) {
            permission = permission or ExecutorStateListActivity.permission_unfinishable
        }
        if (hasSpecialPermission(JoyWorkOp.ASSIGN, true, ps)) {
            permission = permission or ExecutorStateListActivity.permission_transfer
        }
        if (hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, true, ps)) {
            permission = permission or ExecutorStateListActivity.permission_ex_chief
        }
        _permissionChangeLiveData.value = true
    }

    val finishOwners: ArrayList<Owner> = ArrayList()
    val unfinishOwners: ArrayList<Owner> = ArrayList()

    fun replaceFinish(fs: List<Owner>) {
        finishOwners.clear()
        finishOwners.addAll(fs)
        finishOwnerChange()
    }

    fun addFinish(owners: List<Owner>) {
        val ans = finishOwners.union(owners) {
            "${it.emplAccount},${it.ddAppId}"
        }
        if (ans) {
            finishOwnerChange()
        }
    }

    fun removeFinish(owner: Owner) {
        val ans = finishOwners.removeAll {
            it.isSamePerson(owner)
        }
        if (ans) {
            finishOwnerChange()
        }
    }

    fun replaceUnFinish(fs: List<Owner>) {
        unfinishOwners.clear()
        unfinishOwners.addAll(fs)
        unfinishOwnerChange()
    }

    fun addUnFinish(owners: List<Owner>) {
        val key = owners.firstOrNull {
            it.chief.isChief()
        }?.key()
        val ans = unfinishOwners.union(owners) {
            "${it.emplAccount},${it.ddAppId}"
        }
        if (key.isLegalString()) {
            unfinishOwners.forEach {
                if (Objects.equals(it.key(), key)) {
                    it.setToChief()
                } else {
                    it.cancelChief()
                }
            }
        }
        if (ans) {
            unfinishOwnerChange()
        }
    }

    fun removeUnFinish(owner: Owner) {
        val ans = unfinishOwners.removeAll {
            it.isSamePerson(owner)
        }
        if (ans) {
            unfinishOwnerChange()
        }
    }

    fun getAll(): List<Owner> {
        val ans = ArrayList<Owner>()
        ans.addAll(finishOwners)
        ans.addAll(unfinishOwners)
        return ans
    }

    // 已完成执行人列表
    private val _finishOwnerChange = MutableLiveData<Boolean>()
    var finishOwnerChange = _finishOwnerChange

    fun finishOwnerChange() {
        _finishOwnerChange.value = true
    }

    // 未完成执行人列表
    private val _unfinishOwnerChange = MutableLiveData<Boolean>()
    var unfinishOwnerChange = _unfinishOwnerChange

    fun unfinishOwnerChange() {
        _unfinishOwnerChange.value = true
    }
}