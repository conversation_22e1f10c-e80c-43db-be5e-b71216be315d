package com.jd.oa.joywork.team

import com.jd.oa.joywork.ProjectSortAction
import com.jd.oa.joywork.ProjectSortFinish
import com.jd.oa.joywork.SortAction
import com.jd.oa.joywork.TaskStatusEnum

class TeamFinishFragment : TeamBaseFragment() {
    override fun getDefaultStatus(): TaskStatusEnum {
        return TaskStatusEnum.FINISH
    }

    override fun getPageStatus(): TaskStatusEnum {
        return TaskStatusEnum.FINISH
    }

    override fun getListenerAction(): ArrayList<String> {
        val ret = super.getListenerAction()
        ret.add(ProjectConstant.FINISH_ACTION)
        ret.add(ProjectConstant.UNFINISH_ACTION)
        return ret
    }

    override fun getExtraSortAction(): List<ProjectSortAction> {
        return listOf(ProjectSortFinish(this.requireContext()))
    }
}