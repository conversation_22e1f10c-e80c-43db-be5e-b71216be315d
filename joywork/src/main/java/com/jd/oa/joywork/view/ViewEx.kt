package com.jd.oa.joywork.view

import android.app.Activity
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.around.base.HeaderFooterRecyclerAdapterWrapper
import com.jd.oa.joywork.R
import com.jd.oa.joywork.self.base.SelfListBaseAdapter
import com.jd.oa.joywork.self.base.SelfListBaseAdapterWrapper
import com.jd.oa.utils.ThemeUtils
import com.jd.oa.utils.forEachChild
import com.jd.oa.utils.gone
import com.jd.oa.utils.isVisible

val RecyclerView?.realAdapter: RecyclerView.Adapter<*>?
    get() = realAdapter()


fun RecyclerView?.realAdapter(): RecyclerView.Adapter<*>? {
    if (this == null) {
        return null
    }
    val a = adapter
    if (a is SelfListBaseAdapterWrapper) {
        return a.mRealAdapter
    }
    return (adapter as? HeaderFooterRecyclerAdapterWrapper)?.targetAdapter ?: adapter
}

fun SwipeRefreshLayout?.red(activity: Activity) {
    this?.setColorSchemeResources(
        ThemeUtils.getAttrsIdValueFromTheme(
            activity,
            R.attr.me_theme_major_color,
            R.color.skin_color_default
        )
    )
}

fun ViewGroup.goneWhenChildrenGone() {
    forEachChild {
        if (it.isVisible()) {
            return@goneWhenChildrenGone
        }
    }
    gone()
}