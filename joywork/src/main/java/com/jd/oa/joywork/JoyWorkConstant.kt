package com.jd.oa.joywork

import com.jd.oa.AppBase
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink.JDME
import com.jingdong.jdma.JDMA
import com.jingdong.jdma.minterface.CustomInterfaceParam

object JoyWorkConstant {
    /**
     * 有些列表刷新时需要添加延迟
     */
    const val REFRESH_INTERVAL = 1000L
    const val DRAGANDDROP_KR = "Draganddrop_KR"

    /**
     * 创建KR点击事件
     */
    const val CREATE_KR_CLICK = "mobile_JoyWork_performancegoals_addKeyResult_button_click"

    /**
     * 在KR页面创建待办
     */
    const val KR_DETAIL_CREATE_TASK =
        "mobile_JoyWork_performancegoals_keyResultview_addtask_button_click"

    /**
     * 创建目标点击事件
     */
    const val CREATE_TARGET_CLICK = "mobile_JoyWork_performancegoals_addObjective_button_click"

    /**
     * 在目标页面创建待办
     */
    const val TARGET_DETAIL_CREATE_TASK =
        "mobile_JoyWork_performancegoals_ObjectiveView_addtask_button_click"

    // 点击新建待办
    const val INCOMPLETE_CLICK1 = "mobile_Joywork_mytasks_incomplete_click1"

    /**
     * 我负责的页面（默认页面待完成）- 点击完成待办
     */
    const val INCOMPLETE_FINISH = "mobile_Joywork_mytasks_incomplete_click3"

    // 我负责的页面（已完成）- 取消完成
    const val INCOMPLETE_UNFINISH = "mobile_Joywork_mytasks_completed_click1"

    // 我配合的页面（已完成）- 点击取消完成
    const val CO_UNFINISH = "mobile_Joywork_cooperated_completed_click1"

    // 我配合的页面（待完成）- 点击完成
    const val CO_FINISH = "mobile_Joywork_cooperated_incomplete_click3"

    // 我配合的页面（默认页面全部） 点击取消完成
    const val CO_ALL_UNFINISH = "mobile_Joywork_cooperated_all_click1"

    // 我配合的页面（默认页面全部） 点击完成
    const val CO_ALL_FINISH = "mobile_Joywork_cooperated_all_click4"

    // 我指派的页面（待完成） 点击完成
    const val ASSIGN_FINISH = "mobile_Joywork_assigned_incomplete_click1"

    // 我指派的页面（已完成） 点击取消完成
    const val ASSIGN_UNFINISH = "mobile_Joywork_assigned_completed_click1"

    /**
     * 我指派的页面（默认页面全部） 点击完成
     */
    const val ASSIGN_ALL_FINISH = "mobile_Joywork_assigned_all_click1"

    /**
     * 我指派的页面 侧滑详情
     */
    const val ASSIGN_ACTION_DETAIL = "mobile_Joywork_assigned_all_click2"

    /**
     * 我指派的页面  点击卡片进入详情
     */
    const val ASSIGN_ITEM_DETAIL = "mobile_Joywork_assigned_all_click3"

    /**
     * 我指派的页面（默认页面全部） 点击取消完成
     */
    const val ASSIGN_ALL_UNFINISH = "mobile_Joywork_assigned_all_click4"

    // 点击卡片
    const val ITEM_CLICK = "mobile_Joywork_mytasks_incomplete_click24"

    // 安排-今天
    const val ASSIGN_TODAY = "mobile_Joywork_mytasks_incomplete_click12"

    // 安排-待安排
    const val ASSIGN_INBOX = "mobile_Joywork_mytasks_incomplete_click13"

    // 安排-计划
    const val ASSIGN_PLAN = "mobile_Joywork_mytasks_incomplete_click14"

    // 安排-某一天
    const val ASSIGN_OTHER = "mobile_Joywork_mytasks_incomplete_click15"

    // 点击筛选
    const val FILTER = "mobile_Joywork_mytasks_incomplete_click5"

    // 点击筛选-待完成
    const val FILTER_UNFINISH = "mobile_Joywork_mytasks_incomplete_click6"

    // 点击筛选-已删除
    const val FILTER_DELED = "mobile_Joywork_mytasks_incomplete_click7"

    // 点击筛选-已完成
    const val FILTER_FINISH = "mobile_Joywork_mytasks_incomplete_click8"

    // 点击我负责的
    const val RESPONSE_TAB_CLICK = "mobile_Joywork_mytasks_tab_mytasks_click"

    // 点击我指派的
    const val ASSIGN_TAB_CLICK = "mobile_Joywork_mytasks_tab_IAssigned_click"

    // 点击我配合的
    const val COOPERATE_TAB_CLICK = "mobile_Joywork_mytasks_tab_ICollaborated_click"

    //    点击我的待办-已完成
    const val FINISH_TAB_CLICK = "mobile_Joywork_mytasks_tab_Completed_click"

    // 我负责的页面（已删除）- 还原待办
    const val OWNER_REVERSE = "mobile_Joywork_mytasks_deleted_click1"

    // 我指派的页面（已删除）- 点击还原待办
    const val ASSIGN_REVERSE = "mobile_Joywork_assigned_deleted_click1"

    // 我指派的页面（默认页面全部）- 点击还原
    const val ASSIGN_ALL_REVERSE = "mobile_Joywork_assigned_all_click5"

    // 我配合的页面（已删除）- 点击还原待办
    const val CO_REVERSE = "mobile_Joywork_cooperated_deleted_click1"

    // 我负责的页面（默认页面待完成）
    const val PAGE_MAIN = "mobile_Joywork_mytasks_incomplete"

    // 我负责的页面（已完成）
    const val PAGE_MAIN_FINISH = "mobile_Joywork_mytasks_completed"

    // 我负责的页面（已删除）
    const val PAGE_MAIN_DELETED = "mobile_Joywork_mytasks_deleted"

    // 我指派的页面（默认页面全部）
    const val PAGE_ASSIGN_ALL = "mobile_Joywork_assigned_all"

    // 我指派的页面（待完成）
    const val PAGE_ASSIGN_UNFINISH = "mobile_Joywork_assigned_incomplete"

    // 我指派的页面（已完成）
    const val PAGE_ASSIGN_FINISH = "mobile_Joywork_assigned_completed"

    // 我指派的页面（已删除）
    const val PAGE_ASSIGN_DELETED = "mobile_Joywork_assigned_deleted"

    // 我配合的页面（默认页面全部）
    const val PAGE_CO_ALL = "mobile_Joywork_cooperated_all"

    // 我配合的页面（待完成）
    const val PAGE_CO_UNFINISH = "mobile_Joywork_cooperated_incomplete"

    // 我配合的页面（已完成）
    const val PAGE_CO_FINISH = "mobile_Joywork_cooperated_completed"

    // 我配合的页面（已删除）
    const val PAGE_CO_DELETED = "mobile_Joywork_cooperated_deleted"


    // 新建页面点击待安排
    const val SHORTCUT_ASSIGN = "mobile_Joywork_create_inbox_click"

    // 新建页面点击我负责
    const val SHORTCUT_OWNER = "mobile_Joywork_create_assigneetome_click"

    // 新建页面点击截止时间
    const val SHORTCUT_DEADLINE = "mobile_Joywork_create_duedate_click"

    // 长按消息点击待办
    const val JOYWORK_DD_ITEM = "mobile_Joywork_immessage_task_click"

    // IM +号中点击待办
    const val JOYWORK_DD_EX = "mobile_Joywork_im+_task_click"

    // 点击转让负责人
    const val JOYWORK_DETAIL_TRANSFORM = "mobile_Joywork_details_assignedto_click"

    // 点击切换截止时间
    const val JOYWORK_DETAIL_DEADTIME = "mobile_Joywork_details_duedate_click"

    // 点击添加描述
    const val JOYWORK_DETAIL_DES = "mobile_Joywork_details_description_click"

    // 点击创建子待办
    const val JOYWORK_DETAIL_SUBTASK = "mobile_Joywork_details_addsubtasks_click"

    // 点击评论
    const val JOYWORK_DETAIL_COMMENT = "mobile_Joywork_details_comment_click"

    // @人
    const val JOYWORK_DETAIL_AT = "mobile_Joywork_details_@mention_click"

    // @添加附件（joyspace文档）
    const val JOYWORK_DETAIL_JOYSPACE = "mobile_Joywork_details_attachment_joyspace_click"

    // 添加附件（图片）
    const val JOYWORK_DETAIL_IMAGE = "mobile_Joywork_details_attachment_image_click"

    // 添加附件（拍照）
    const val JOYWORK_DETAIL_TAKE_PHOTO = "mobile_Joywork_details_attachment_photo_click"

    // 强提醒 我知道了
    const val JOYWORK_TIPS_IGOTIT = "mobile_Joyday_remindview_Igotit_click"

    // 强提醒 查看详情
    const val JOYWORK_TIPS_VIEW = "mobile_Joyday_remindview_details_click"

    // 点击我的待办（个人待办）
    const val JOYWORK_SELF = "mobile_Joywork_mytasks_tab_click"

    // 点击团队待办
    const val JOYWORK_TEAM = "mobile_Joywork_teamtasks_tab_click"

    // 团队待办点击新建清单按钮
    const val JOYWORK_NEW_TEAM = "mobile_Joywork_teamtasks_newlist_button_click"

    // 团队待办点击清单名称
    const val JOYWORK_TEAM_ITEM_CLICK = "mobile_Joywork_teamtasks_listtitle_click"

    // 待办详情页面点击添加关联清单
    const val JOYWORK_DETAIL_LINK_TEAM = "mobile_Joywork_task_details_addteamlist_button_click"

    // 待办详情页面点击清单进入清单详情
    const val JOYWORK_DETAIL_GO_TEAM_DETAIL = "mobile_Joywork_task_details_teamlist_button_click"

    // 待办详情页面点击分享按钮
    const val JOYWORK_DETAIL_SHARE = "mobile_Joywork_task_details_share_button_click"

    // 我负责的待办拖拽待办卡片
    const val JOYWORK_START_DRAG = "mobile_Joywork_mytasks_taskcard_drag"

    // 我的待办页面（个人待办）
    const val JOYWORK_PAGE_SELF = "mobile_Joywork_personaltasks_view"

    // 团队待办页面
    const val JOYWORK_PAGE_TEAM = "mobile_Joywork_teamtasks_view"

    // 待办详情页面
    const val JOYWORK_PAGE_DETAIL = "mobile_Joywork_task_details_view"

    // 团队待办，点击全部
    const val JOYWORK_PROJECT_ALL = "mobile_Joywork_teamtask_tasklist_all_click"

    // 团队待办，点击移动
    const val JOYWORK_PROJECT_MOVE = "mobile_Joywork_teamtask_tasklist_move_click"

    // 自定义字段，详情页点击
    const val JOYWORK_MARK_DETAIL_CLICK = "mobile_Joywork_teamtask_taskdetail_customizefield_click"

    // 自定义字段，选择点击
    const val JOYWORK_MARK_DIALOG_CLICK = "mobile_Joywork_teamtask_taskdetail_customizefield_select"

    // snackbar点击查看详情
    const val JOYWORK_SNACKBAR_DETAIL = "mobile_JoyWork_createtask_snackbar_viewdetails_click";

    // 点击筛选
    const val JOYWORK_FILTER_ITEM = "mobile_JoyWork_teamtask_filter_click"

    // 点击仅我的待办，仅我的待办
    const val JOYWORK_FILTER_MY = "mobile_JoyWork_teamtask_filter_justmytask_click"

    // 点击筛选, 点击本周截止
    const val JOYWORK_FILTER_CUR = "mobile_JoyWork_teamtask_filter_duethisweek_click"

    // 点击筛选, 点击下周截止
    const val JOYWORK_FILTER_NEXT = "mobile_JoyWork_teamtask_filter_duenextweek_click"

    //点击筛选, 点击自定义筛选   这个不是用来上传子午线的
    const val JOYWORK_FILTER_CUSTOM = "mobile_JoyWork_teamtask_filter_custom_click"

    // 点击排序
    const val JOYWORK_SORT_ITEM = "mobile_JoyWork_teamtask_sort_click"

    // 点击截止时间
    const val JOYWORK_SORT_END_TIME = "mobile_JoyWork_teamtask_sort_duedate_click"

    // 点击指派对象
    const val JOYWORK_SORT_OWNER = "mobile_JoyWork_teamtask_sort_assignee_click"

    // 点击完成时间
    const val JOYWORK_SORT_FINISH = "mobile_JoyWork_teamtask_sort_completiondate_click"

    // 点击在分组内排序
    const val JOYWORK_SORT_GROUP = "mobile_JoyWork_teamtask_sort_sortwithsections_click"

    // 点击确认
    const val JOYWORK_FINISH_DIALOG_OK =
        "mobile_JoyWork_completetask_completedescription_confirm_click"

    // 点击无需说明
    const val JOYWORK_FINISH_DIALOG_CANCEL =
        "mobile_JoyWork_completetask_completedescription_nodescription_click"

    // 在更多页面点击新建
    const val JOYWORK_NEW_MORE_CREATE = "mobile_JoyWork_newtaskdetails_more_createbutton_click"

    // 点击更多按钮
    const val JOYWORK_NEW_MORE_TO = "mobile_JoyWork_newtaskdetails_morebutton_click"

    // 点击合并按钮
    const val JOYWORK_MERGE = "mobile_JoyWork_details_morebutton_mergeduplicate_click"

    // 点击还原合并按钮
    const val JOYWORK_MERGE_CANCEL = "mobile_JoyWork_details_morebutton_unmarkduplicate_click"

    // 点击转为子待办按钮
    const val JOYWORK_SUB = "mobile_JoyWork_details_morebutton_marksubtask_click"

    // 点击更换父待办按钮
    const val JOYWORK_CHANGE_PARENT = "mobile_JoyWork_details_morebutton_changeparenttask_click"

    //在全局页设置中点击关闭/开启消息
    const val JOYWORK_MESETTINGS_TURNOFFREMINDER = "mobile_mesettings_JoyWork_turnoffreminder_click"

    // 批量新建子待办按钮
    const val JOYWORK_BATCH_CREATE =
        "mobile_JoyWork_taskdetails_SubtasksBatchesCreate_button_click";

    // 导入按钮
    const val JOYWORK_BATCH_IMPORT =
        "mobile_JoyWork_taskdetails_SubtasksBatchesCreate_DetailsViews_import_button_click";

    // 催办按钮
    const val JOYWORK_URGE =
        "mobile_JoyWork_taskdetails_urge_button_click";

    // 取消催办按钮（催办选择人员页面左上角的X）
    const val JOYWORK_URGE_CANCEL =
        "mobile_JoyWork_taskdetails_urgeViews_close_button_click";

    // 点击风险问题tab
    const val JOYWORK_ISSUE_CLICK = "mobile_Joywork_risk&issue_tab_click"

    // 点击上报风险
    const val DETAIL_ISSUE_CLICK = "mobile_Joywork_taskdetail_ReportRisk_button_click"

    // 选择风险
    const val DETAIL_RISK_RISK = "mobile_Joywork_taskdetail_ReportRisk_risk_click"

    // 选择正常
    const val DETAIL_RISK_NORMAL = "mobile_Joywork_taskdetail_ReportRisk_normal_click"

    // 选择问题
    const val DETAIL_RISK_PROBLEM = "mobile_Joywork_taskdetail_ReportRisk_issue_click"

    // 点击风险卡片讨论
    const val DETAIL_RISK_DETAIL_DISCUSS = "mobile_Joywork_taskdetail_Riskcard_comment_click"

    // 点击风险卡片解决
    const val DETAIL_RISK_DETAIL_RESOLVE = "mobile_Joywork_taskdetail_Riskcard_resolve_click"

    // 点击问题卡片讨论
    const val DETAIL_PROBLEM_DETAIL_DISCUSS = "mobile_Joywork_taskdetail_Issuecard_comment_click"

    // 点击问题卡片解决
    const val DETAIL_PROBLEM_DETAIL_RESOLVE = "mobile_Joywork_taskdetail_Issuecard_resolve_click"

    // 点击风险卡片解决
    const val DETAIL_RISK_DETAIL_UPDATE = "mobile_Joywork_taskdetail_Riskcard_update_click"

    // 点击问题卡片解决
    const val DETAIL_PROBLEM_DETAIL_UPDATE = "mobile_Joywork_taskdetail_Issuecard_update_click"

    // 添加启动时间
    const val DETAIL_LAUNCH_TIME_CLICK = "mobile_Joywork_taskdetail_AddScheduleTime_button_click"

    // 点击今天
    const val DETAIL_LAUNCH_TIME_TODAY_CLICK =
        "mobile_Joywork_taskdetail_AddScheduleTime_today_click"

    // 点击明天
    const val DETAIL_LAUNCH_TIME_TOM_CLICK =
        "mobile_Joywork_taskdetail_AddScheduleTime_tomorrow_click"

    // 点击下周一
    const val DETAIL_LAUNCH_TIME_NEXT_CLICK =
        "mobile_Joywork_taskdetail_AddScheduleTime_nextweek_click"

    // 选择其他时间
    const val DETAIL_LAUNCH_TIME_OTHER = "mobile_Joywork_taskdetail_AddScheduleTime_othertime_click"

    // 新建页面点击添加击执行人文案按钮
    const val CREATE_OWNER = "mobile_JoyWork_createtaskview_addassignees_button_click"

    // 移动端执行人列表，点击右上角添加执行人
    const val CREATE_OWNER_LIST =
        "mobile_JoyWork_createtaskview_assigneeslist_addassignee_button_click"

    // 移动端点击执行人以后显示执行人列表页面，点击右上角添加执行人
    const val DETAIL_OWNER_LIST =
        "mobile_JoyWork_taskdetails_assigneeslist_addassignee_button_click"

    // 移动端详情页面，选择执行人执行人后面的+号，点击+号添加执行人
    const val DETAIL_OWNER = "mobile_JoyWork_taskdetails_addassignee+_button_click"

    // 完成待办
    const val FINISH_JOYWORK = "mobile_JoyWork_taskdetails_completedtask_button_click"

    // 恢复待办
    const val RESTORE_JOYWORK = "mobile_JoyWork_taskdetails_reopentask_button_click"

    // 仅我完成
    const val FINISH_ME = "mobile_JoyWork_taskdetails_completemyself_button_click"

    // 恢复自己
    const val RESTORE_ME = "mobile_JoyWork_taskdetails_reopenmyself_button_click"

    // 完成全部
    const val FINISH_ALL = "mobile_JoyWork_taskdetails_completedall_button_click"

    // 恢复全部
    const val RESTORE_ALL = "mobile_JoyWork_taskdetails_reopenall_button_click"

    // 未完成执行人列表点击发起催办
    const val OWNER_LIST_URGE = "mobile_JoyWork_taskdetails_assigneeslist_urge_button_click"

    // 右上角快捷催办
    const val DETAIL_URGE = "mobile_JoyWork_taskdetails_topquickurge_button_click"

    // 更多新建页面，添加关注人（点击添加文案）
    const val CREATE_RELATION = "mobile_JoyWork_morecreatetaskview_addfollower_button_click"

    // 更多新建页面，添加关注人（点击+号）
    const val CREATE_RELATION_ADD = "mobile_JoyWork_morecreatetaskview_addfollower+_button_click"

    // 详情页面-添加关注人按钮
    const val DETAIL_RELATION = "mobile_JoyWork_taskdetails_addfollower_button_click"

    // 详情页面添加关注人以后，点击+号的按钮
    const val DETAIL_RELATION_ADD = "mobile_JoyWork_taskdetails_addfollower+_button_click"

    // 详情页面-关注待办（点击右上角icon）
    const val DETAIL_FOCUS = "mobile_JoyWork_taskdetails_topfollow_button_click"

    // 详情页面-取消关注待办（点击右上角icon）
    const val DETAIL_UNFOCUS = "mobile_JoyWork_taskdetails_topnotfollow_button_click"

    // 创建时设置负责人
    const val CREATE_CHIEF = "timline_JoyWork_creatnewtaskview_assigneelist_markasower_button_click"

    // 详情页面设置负责人
    const val DETAIL_CHIEF = "timline_JoyWork_taskdetails_assigneelist_markasower_button_click"

    // 创建时勾选发送到会话
    const val CREATE_SEND = "timline_JoyWork_creatnewtaskview_sendtochat_button_click"

    // 点击绿条
    const val DETAIL_NOTIFY = "timline_JoyWork_taskdetails_topupdatemessage_click"

    // 点击绿条上的发送通知
    const val DETAIL_NOTIFY_SEND_BTN =
        "timline_JoyWork_taskdetails_topupdatemessage_sendmessage_button_click"

    // 点击弹窗上的按钮：发送，
    const val DETAIL_DIALOG_SEND =
        "timline_JoyWork_taskdetails_sendupdatemessagepopup_send_button_click"

    // 点击弹窗上的按钮：不发送
    const val DETAIL_DIALOG_NO_SEND =
        "timline_JoyWork_taskdetails_sendupdatemessagepopup_notsend_button_click"

    // 在群场景中全选按钮
    const val SELECT_GROUP_ROSTER_ALL =
        "mobile_JoyWork_Contactselector_currentgroupmembers_allmembers_button_click"

    // 在群场景中外部成员
    const val SELECT_GROUP_ROSTER_OUTER =
        "mobile_JoyWork_Contactselector_Out-of-groupgroupmembers_button_click"

    // 点击生成周报按钮
    const val CREATE_WEEKLY_SUMMARY =
        "mobile_JoyWork_performancegoals_Generateweeklyreport_button_click"

    // 点击确认按钮
    const val CREATE_WEEKLY_SUMMARY_SURE =
        "mobile_JoyWork_performancegoals_Generateweeklyreport_Choosegoalview_confirm_button_click"

    // 在时间选择器点击设置提醒时间
    const val TIME_DIALOG_SET_REMINDER = "mobile_JoyWork_TimeSelector_SetReminder_button_click"

    // 待办详情页面点击更多-设置提醒时间
    const val DETAIL_SET_REMINDER = "mobile_JoyWork_taskdetails_more_SetReminder_button_click"

    // 详情页面点击设置优先级
    const val DETAIL_SET_PRIORITY = "mobile_JoyWork_taskdetails_SetPriority_button_click"

    // 列表中点击设置优先级
    const val TEAM_LIST_SET_REMINDER = "mobile_JoyWork_tasklist_SetPriority_button_click"

    // 新建待办更多页面点击-设置提醒时间
    const val ALL_CREATE_SET_REMINDER =
        "mobile_JoyWork_CreatetaskView_more_SetReminder_button_click"

    // 新建待办更多点击设置优先级
    const val ALL_CREATE_SET_PRIORITY =
        "mobile_JoyWork_CreatetaskView_more_SetPriority_button_click"

    // 点击评论按钮
    const val GOAL_COMMENT_CLICK = "mobile_JoyWork_Goals_comment_button_click"

    // 点击目标分享
    const val GOAL_SHARE_CLICK = "mobile_JoyWork_Goals_sharetochat_button_click"

    // 点击京东人事
    const val GOAL_JDHR_CLICK = "mobile_JoyWork_JDHRTab_click"

    // 点击目标管理
    const val GOAL_JDGOALS_CLICK = "mobile_JoyWork_GoalsTab_click"
    // 业务使用

    const val BIZ_DETAIL_FROM_FILTER = "filter"
    const val BIZ_DETAIL_FROM_LIST = "list"

    //
    const val BIZ_DETAIL_FROM_KR_GOAL = "kr_goal"
    const val BIZ_DETAIL_FROM_SECTION = "section"

    // 石墨文档的 bizCode
    const val BIZ_CODE_SHIMO = "47b90c70859a4e5e829347536eefc923"

    // 咚咚的 bizCode
    const val BIZ_CODE_DD = "96a4458d4b19415ea5dd4d9fe9ceb973"

    const val LIST_EXAMPLE_DEEPLINK =
        JDME + "rn/201909020601?routeTag=new_document_edit&rnStandalone=2&page_id=VlaclfW7blz19bRuww74"

    // 移动端-待办-待完成
    const val JOYWORK_MYEXECUTION_COMPLETE = "Mobile_event_joywork_Myexecution_Complete"
    // 移动端-待办-排序
    const val JOYWORK_MYEXECUTION_RANK = "Mobile_event_joywork_Myexecution_rank"
    // 移动端-待办-分组
    const val JOYWORK_MYEXECUTION_GOUP = "Mobile_event_joywork_Myexecution_Goup"
    // 移动端-待办-我执行的
    const val JOYWORK_MYEXECUTION = "Mobile_event_joywork_Myexecution"
    // 移动端-待办-待我审批
    const val JOYWORK_MYAPPROVAL = "Mobile_event_Task_Home_MyApproval|1"
    // 移动端-待办-我关注的
    const val JOYWORK_MYFOLLOWED = "Mobile_event_joywork_Myfollowed"
    // 移动端-待办-我指派的
    const val JOYWORK_MYCREATION = "Mobile_event_joywork_MyCreation"
    // 移动端-待办-京东人事
    const val JOYWORK_ORG = "Mobile_event_joywork_Org"
    // 移动端-待办-会议待办
    const val JOYWORK_MEETINGTASK = "Mobile_event_joywork_meetingtask"
    // 移动端-待办-清单
    const val JOYWORK_LIST = "Mobile_event_joywork_list"
    // 移动端-待办-待办标题
    const val JOYWORK_TASKTITLE = "Mobile_event_joywork_tasktitle"


    const val MOBILE_EVENT_TASK_MYEXECUTION_GROUP_MORE_DELETE = "Mobile_event_Task_Myexecution_Group_More_Delete"
    const val MOBILE_EVENT_TASK_MYEXECUTION_GROUP_MORE_RENAME = "Mobile_event_Task_Myexecution_Group_More_Rename"
    const val MOBILE_EVENT_TASK_MYEXECUTION_GROUP_INSERTGROUPLEFT = "Mobile_event_Task_Myexecution_Group_Insertgroupleft"
    const val MOBILE_EVENT_TASK_MYEXECUTION_GROUP_INSERTGROUPRIGHT = "Mobile_event_Task_Myexecution_Group_Insertgroupright"


    const val MOBILE_EVENT_TASK_LIST_GROUP_MORE_DELETE = "Mobile_event_Task_List_Group_More_Delete"
    const val MOBILE_EVENT_TASK_LIST_GROUP_MORE_RENAME = "Mobile_event_Task_List_Group_More_Rename"

    const val MOBILE_EVENT_TASK_LIST_GROUP_INSERTGROUPLEFT = "Mobile_event_Task_List_Group_Insertgroupleft"
    const val MOBILE_EVENT_TASK_LIST_GROUP_INSERTGROUPRIGHT = "Mobile_event_Task_List_Group_Insertgroupright"


    const val MOBILE_EVENT_TASK_MYEXECUTION_CARD_VIEWER = "Mobile_event_Task_Myexecution_Card"
    const val MOBILE_EVENT_TASK_MYEXECUTION_LISTVIEWER = "Mobile_event_Task_Myexecution_ListViewer"

    const val MOBILE_EVENT_TASK_LIST_CARD = "Mobile_event_Task_List_Card"
    const val MOBILE_EVENT_TASK_LIST_LISTVIEWER = "Mobile_event_Task_List_ListViewer"

    const val MOBILE_EVENT_TASK_KANBAN_VIEWMORE = "Mobile_event_Task_Card_ViewMore"

    const val MOBILE_EVENT_TASK_LIST_LISTMANAGEMENT = "Mobile_event_Task_List_Listmanagement"

    const val MOBILE_EVENT_TASK_MY_CALENDAR = "Mobile_event_Task_My_Calendar"
    const val MOBILE_EVENT_TASK_LIST_CALENDAR = "Mobile_event_Task_List_Calendar"

    //add 24.09.30
    const val MOBILE_EVENT_TASK_CREATE_TASK_RELATE_CHECKLIST = "Mobile_event_Task_CreateTask_relateChecklist"
    const val MOBILE_EVENT_TASK_CREATE_TASK_RELATE_CHECKLIST_BUTTON = "Mobile_event_Task_CreateTask_relateChecklist_button"
    const val MOBILE_EVENT_TASK_CREATE_TASK_ASSIGNEES_CANCEL_RESPONSIBLE = "Mobile_event_Task_CreateTask_assignees_cancelResponsible"
    const val PC_TASK_EVENT_CREATE_TASK_ASSIGNEES_SET_RESPONSIBLE = "PC_Task_event_CreateTask_assignees_setResponsible"
    const val MOBILE_EVENT_TASK_CREATE_TASK_ASSIGNEES_ADD_ASSIGNEES = "Mobile_event_Task_CreateTask_assignees_addAssignees"
    const val MOBILE_EVENT_TASK_CREATE_TASK_MORE_VIEW = "Mobile_event_Task_CreateTask_moreView"
    const val MOBILE_EVENT_TASK_CREATE_TASK_SEND2_CHAT = "Mobile_event_Task_CreateTask_send2Chat"
    const val PC_TASK_EVENT_CREATE_TASK_ASSIGNEES_REMOVE = "PC_Task_event_CreateTask_assignees_Remove"
    const val MOBILE_EVENT_TASK_CREATE_TASK_ABANDON_CREATION = "Mobile_event_Task_CreateTask_abandonCreation"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_MORE_REPEAT = "Mobile_event_Task_TaskDetails_more_repeat"
    const val MOBILE_EVENT_TASK_HOME_GROUP_NEW_TASK = "Mobile_event_Task_Home_Group_newTask"
    const val MOBILE_EVENT_TASK_CHECK_LIST_GROUP_NEW_TASK = "Mobile_event_Task_checklist_Group_newTask"
    const val MOBILE_EVENT_TASK_HOME_GROUP_MORE_RANK_GROUP = "Mobile_event_Task_Home_Group_more_rankGroup"
    const val MOBILE_EVENT_TASK_HOME_SEARCH_BUTTON = "Mobile_event_Task_Home_Search_button"
    const val MOBILE_EVENT_TASK_HOME_HELP_CENTER_BUTTON = "Mobile_event_Task_Home_helpcenter_button"
    const val MOBILE_EVENT_TASK_NAV_TAB = "Mobile_event_Task_navtab"
    const val MOBILE_EVENT_TASK_CHECKLIST_INFO_MEMBER = "Mobile_event_Task_checklist_info_member"
    const val MOBILE_EVENT_TASK_CHECKLIST_INFO_MEMBER_CHOOSE_MEMBER = "Mobile_event_Task_checklist_info_member_choosemember"
    const val MOBILE_EVENT_TASK_CHECKLIST_INFO_TRANS_OWNER = "Mobile_event_Task_checklist_info_transOwner"
    const val MOBILE_EVENT_TASK_CHECKLIST_INFO_DELETE = "Mobile_event_Task_checklist_info_delete"
    const val MOBILE_EVENT_TASK_NAV_TAB_NEW_CHECKLIST = "Mobile_event_Task_navtab_newChecklist"



    const val MOBILE_EVENT_TASK_LIST_VIEWER_TASK_ASSIGNEES = "Mobile_event_Task_ListViewer_Task_assignees"
    const val MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_ASSIGNEES = "Mobile_event_Task_KanbanViewer_TaskCard_assignees"
    const val MOBILE_EVENT_TASK_LIST_VIEWER_TASK_DDL = "Mobile_event_Task_ListViewer_Task_ddl"
    const val MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_DDL = "Mobile_event_Task_KanbanViewer_TaskCard_ddl"
    const val MOBILE_EVENT_TASK_LIST_VIEWER_TASK_COMPLETE_MYSELF = "Mobile_event_Task_ListViewer_Task_completeMyself"
    const val MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_COMPLETE_MYSELF = "Mobile_event_Task_KanbanViewer_TaskCard_completeMyself"
    const val MOBILE_EVENT_TASK_LIST_VIEWER_TASK_STATUS = "Mobile_event_Task_ListViewer_Task_status"
    const val MOBILE_EVENT_TASK_LIST_VIEWER_TASK_CHECKLIST = "Mobile_event_Task_ListViewer_Task_checklist"
    const val MOBILE_EVENT_TASK_LIST_VIEWER_TASK_CLICK_ROW = "Mobile_event_Task_ListViewer_Task_clickrow"
    const val MOBILE_EVENT_TASK_KANBAN_VIEWER_TASK_CARD_CLICK = "Mobile_event_Task_KanbanViewer_TaskCard_click"

    const val MOBILE_EVENT_TASK_TASK_DETAILS_MORE_OPLOG = "Mobile_event_Task_TaskDetails_more_opLog"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_MORE_MERGE_TASKS = "Mobile_event_Task_TaskDetails_more_MergeTasks"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_MORE_DELETE_TASK = "Mobile_event_Task_TaskDetails_more_deleteTask"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_RELATED_CHECKLIST = "Mobile_event_Task_TaskDetails_relatedChecklist"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_RELATE_CHECKLIST_CHOOSE_GROUP = "Mobile_event_Task_TaskDetails_relateChecklist_chooseGroup"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_RELATE_CHECKLIST_REMOVE = "Mobile_event_Task_TaskDetails_relateChecklist_remove"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_ASSIGNEES_TASK_TRANSFER_BUTTON = "Mobile_event_Task_TaskDetails_assignees_TaskTransfer_button"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED = "Mobile_event_Task_TaskDetails_tabUnfinished"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED = "Mobile_event_Task_TaskDetails_tabFinished"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED_MORE_MARK_UNFINISHED = "Mobile_event_Task_TaskDetails_tabFinished_more_markUnfinished"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MARK_FINISHED = "Mobile_event_Task_TaskDetails_tabUnfinished_markFinished"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MORE_SET_RESPONSIBLE = "Mobile_event_Task_TaskDetails_tabUnfinished_more_setResponible"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MORE_CANCEL_RESPONSIBLE = "Mobile_event_Task_TaskDetails_tabUnfinished_more_cancelResponsible"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED_MORE_SET_RESPONSIBLE = "Mobile_event_Task_TaskDetails_tabFinished_more_setResponsible"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED_MORE_CANCEL_RESPONSIBLE = "Mobile_event_Task_TaskDetails_tabFinished_more_cancelResponible"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MORE_REMOVE_ASSIGNEES = "Mobile_event_Task_TaskDetails_tabUnfinished_more_removeAssignees"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED_MORE_REMOVE_ASSIGNEES = "Mobile_event_Task_TaskDetails_tabFinished_more_removeAssignees"
    const val MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MORE_TRANS_TASK = "Mobile_event_Task_TaskDetails_tabUnfinished_more_TransTask"
    const val MOBILE_EVENT_TASK_LINK_RECOGNITION = "Mobile_event_Task_LinkRecognition"
    // 真诚点赞
    const val MOBILE_EVENT_TASK_TASKDETAILS_LIKES = "Mobile_event_Task_TaskDetails_Likes"
    // 评论-更新进展
    const val MOBILE_EVENT_TASK_TASKDETAILS_COMMENT = "Mobile_event_Task_TaskDetails_comment"
    // 待办-我的-分组-插入分组（卡片视图）
    const val MOBILE_EVENT_TASK_HOME_GROUP_NEWGROUP = "Mobile_event_Task_Home_Group_newGroup"
    // 待办-刷新（卡片视图）
    const val MOBILE_EVENT_TASK_FRESH = "Mobile_event_Task_fresh"
    // 待办-清单-分组-新建分组（卡片视图）
    const val MOBILE_EVENT_TASK_CHECKLIST_GROUP_NEWGROUP = "Mobile_event_Task_checklist_Group_newGroup"
    // 工作台-我的待办-我执行的
    const val MOBILE_EVENT_WORKBENCH_TASK_MY_MYEXECUTION = "Mobile_event_workbench_task_My_myexecution"
    // 工作台-我的待办-我关注的
    const val MOBILE_EVENT_WORKBENCH_TASK_MY_MYFOLLOW = "Mobile_event_workbench_task_My_myFollow"
    // 工作台-我的清单-查看更多清单
    const val MOBILE_EVENT_WORKBENCH_TASK_LIST_CHECKMORE = "Mobile_event_workbench_task_list_checkMore"
    // 「移动到」按钮
    const val MOBILE_EVENT_CARD_MOVE_TO = "Mobile_event_Card_MoveTo"
    // 「转派」按钮
    const val MOBILE_EVENT_CARD_TRANSFER = "Mobile_event_Card_Transfer"
    // 「删除」按钮
    const val MOBILE_EVENT_CARD_DELETE = "Mobile_event_Card_Delete"
    // 「拒绝」按钮
    const val MOBILE_EVENT_CARD_REFUSE = "Mobile_event_Card_Refuse"
    // 筛选-风险问题
    const val MOBILE_EVENT_TASKS_VIEW_FILTER_RISK = "Mobile_event_Tasks_ViewFilter_Risk|1"
    // 京ME设置-待办-需反馈设置
    const val MOBILE_EVENT_SETTINGS_TASK_NEED_ME = "Mobile_event_settings_Task_NeedME|1"
}

object JoyWorkNetConstant {
    const val PROJECT_LIST = "work.task.getProjectList.v3"

    /**
     * 已归档清单
     */
    const val ARCHIVE_LIST = "joywork.getArchiveProjectList"

    /**
     * 列出某个 q 下所有目标
     */
    const val LIST_TARGET_Q = "work.task.getGoalWithKrByPeriod.v1"

    /**
     * 1. 返回所有考勤周期
     * 2. 返回某个
     */
    const val LIST_TARGET_CUR = "work.task.getPerf.v2"

    /**
     * 根据目标 id 获取待办
     */
    const val LIST_TARGET_TASK = "work.task.getTasksByGoalId.v1"

    /**
     * 各个待办关联绩效目标
     */
    const val TASK_LINK_TARGET = "work.task.addPerfGoal.v1"

    /**
     * 删除待办关联的目标
     */
    const val TASK_DEL_TARGET = "work.task.delPerfGoal.v1"

    /**
     * 删除目标
     */
    const val DEL_TARGET = "work.task.deleteGoal.v1"

}

object JoyWorkJDMA {

    /**
     * @param eid 自定义事件ID
     */
    fun sendCustomData(eid: String) {
        val param = CustomInterfaceParam()
        param.pin = PreferenceManager.UserInfo.getUserName()
        param.eid = eid
        param.ela = "mobile_joywork" // 自定义事件对应模块ID
        JDMA.sendCustomData(AppBase.getAppContext(), param)
    }
}