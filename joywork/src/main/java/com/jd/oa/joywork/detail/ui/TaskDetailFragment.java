package com.jd.oa.joywork.detail.ui;

import static com.jd.oa.fragment.js.hybrid.JsIm.REQUEST_CODE_SELECT_CONTACT;
import static com.jd.oa.joywork.detail.ui.TaskDetailActivity.KEY_ACTION_SOURCE;
import static com.jd.oa.joywork.detail.ui.TaskDetailActivity.KEY_COMMENT_ID;
import static com.jd.oa.joywork.detail.ui.TaskDetailActivity.KEY_TASK_ID;
import static com.jd.oa.utils.FileUtils.getExtension;
import static com.jd.oa.utils.OpenFileUtil.APP_SOURCE_TASK;
import static com.jd.oa.utils.OpenFileUtil.openFileByX5;
import static com.jd.oa.utils.OpenFileUtil.sendFileClickEvent;

import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.webkit.JavascriptInterface;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.arch.core.util.Function;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.fragment.js.hybrid.JsAlbum;
import com.jd.oa.fragment.js.hybrid.JsDeviceInfo;
import com.jd.oa.fragment.js.hybrid.JsEvent;
import com.jd.oa.fragment.js.hybrid.JsFile;
import com.jd.oa.fragment.js.hybrid.JsIm;
import com.jd.oa.fragment.js.hybrid.JsUser;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightObserver;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider;
import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkActionEnum;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.joywork.JoyWorkDialog;
import com.jd.oa.joywork.JoyWorkExKt;
import com.jd.oa.joywork.JoyWorkHandler;
import com.jd.oa.joywork.JoyWorkMsgCenter;
import com.jd.oa.joywork.JoyWorkTaskSelector;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.ObjectLocalKt;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.RiskEnum;
import com.jd.oa.joywork.TaskListTypeEnum;
import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork.TaskUserRole;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork.bean.TransferResult;
import com.jd.oa.joywork.create.CreateDetailFragmentItf;
import com.jd.oa.joywork.create.CreateItemFactory;
import com.jd.oa.joywork.create.CreateItemFactoryList;
import com.jd.oa.joywork.create.CreateViewModel;
import com.jd.oa.joywork.create.DetailArrayList;
import com.jd.oa.joywork.create.Value;
import com.jd.oa.joywork.databinding.TaskDetailFragmentBinding;
import com.jd.oa.joywork.detail.DialogManager;
import com.jd.oa.joywork.detail.JoyWorkContentExKt;
import com.jd.oa.joywork.detail.data.db.UpdateReporter;
import com.jd.oa.joywork.detail.data.entity.ChildWorks;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.model.service.im.dd.entity.Members;
import com.jd.oa.joywork.detail.data.entity.Owner;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity;
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup;
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailBindingAdapter;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailViewModel;
import com.jd.oa.joywork.detail.viewmodel.UpdateRegionVM;
import com.jd.oa.joywork.executor.ExecutorStateListActivity;
import com.jd.oa.joywork.executor.ExecutorUtils;
import com.jd.oa.joywork.notification.NotifyRepo;
import com.jd.oa.joywork.repo.JoyWorkRepo;
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator;
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDialog;
import com.jd.oa.joywork.team.bean.GrayInfo;
import com.jd.oa.joywork.team.kpi.KpiRepo;
import com.jd.oa.joywork.utils.HyperLinkHelper;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.EventTrackerKt;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.BuildConfig;
import com.tencent.smtt.export.external.interfaces.WebResourceError;
import com.tencent.smtt.export.external.interfaces.WebResourceRequest;
import com.tencent.smtt.export.external.interfaces.WebResourceResponse;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.functions.Function3;
import kotlinx.coroutines.CoroutineScope;
import kotlinx.coroutines.CoroutineScopeKt;
import wendu.dsbridge.CompletionHandler;
import wendu.dsbridge.OnReturnValue;

public class TaskDetailFragment extends Fragment implements KeyboardHeightObserver, CreateDetailFragmentItf, DynamicOperatorListener {
    public static final int MAX_SHOW_LIKE = 4;

    private TaskDetailFragmentBinding mBinding;
    private int keyBoardShowHeight, lastKeyBoardHeight;
    private int webToTop, webNeedToY;
    private String titleOld;
    String mTaskId;
    private String actionSource;
    private JsSdkKit jsSdkKit;
    private Runnable runnable;
    public TaskDetailPresenter taskDetailPresenter;
    private DocumentsAdapter documentsAdapter;
    private ResourceAdapter resourceAdapter;
    private MembersAdapter likeAdapter;
    public TaskDetailViewModel model;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private KeyboardHeightProvider keyboardHeightProvider;
    private UpdateRegionVM mUpdateRegionVM;
    private final CoroutineScope mScope = CoroutineScopeKt.MainScope();
    private final BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            UpdateReporter.INSTANCE.reportUpdate();
        }
    };

    private String mCommentId;
    private boolean mScrolledToComment;

    public static TaskDetailFragment forTask(@NonNull String taskId, String actionSource) {
        TaskDetailFragment fragment = new TaskDetailFragment();
        Bundle args = new Bundle();
        args.putString(KEY_TASK_ID, taskId);
        if (actionSource != null) {
            args.putString(KEY_ACTION_SOURCE, actionSource);
        }
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        mBinding = TaskDetailFragmentBinding.inflate(inflater, container, false);
        IntentFilter filter = new IntentFilter();
        filter.addAction(JsTargetInterface.TASK_DETAIL_RESOLVE_RISK);
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(receiver, filter);
        return mBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mUpdateRegionVM = ViewModelProviders.of(this).get(UpdateRegionVM.class);
        mUpdateRegionVM.init();
        mValue = new Value(ViewModelProviders.of(this).get(CreateViewModel.class));
        mTaskId = requireArguments().getString(KEY_TASK_ID);
        mCommentId = requireArguments().getString(KEY_COMMENT_ID);
        actionSource = requireArguments().getString(KEY_ACTION_SOURCE);
        TaskDetailViewModel.Factory factory = new TaskDetailViewModel.Factory(requireActivity().getApplication(), mTaskId, actionSource);
        model = new ViewModelProvider(this, factory).get(TaskDetailViewModel.class);
        mBinding.setLifecycleOwner(getViewLifecycleOwner());
        mBinding.setTaskDetailViewModel(this.model);
        mBinding.taskDetailHeader.moreButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TaskDetailEntity entity = mBinding.getTaskDetailViewModel().getTaskDetail().getValue();
                taskDetailPresenter.more(TaskDetailFragment.this, entity, true);
            }
        });
        mBinding.taskDetailPage.moreContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TaskDetailEntity entity = mBinding.getTaskDetailViewModel().getTaskDetail().getValue();
                taskDetailPresenter.more(TaskDetailFragment.this, entity, false);
            }
        });
        taskDetailPresenter = new TaskDetailPresenter(this.model, actionSource);
        taskDetailPresenter.mUpdateRegionVM = mUpdateRegionVM;
        mBinding.setTaskDetailPresenter(taskDetailPresenter);
        mBinding.taskDetailPage.title.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TaskDetailEntity entity = mBinding.getTaskDetailViewModel().getTaskDetail().getValue();
                if (!JoyWorkContentExKt.canUpdateTitle(entity == null ? null : entity.getJoyWorkDetail())) {
                    return;
                }
                if (!TaskDetailFragment.this.model.getTitleEdit()) {//框架中涉及到UI属性改变的最好放到handler中执行
                    titleOld = mBinding.taskDetailPage.title.getText().toString();
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            TaskDetailFragment.this.model.setTitleEdit(true);
                            TaskUiUtils.showKeyboard(mBinding.taskDetailPage.title);
                        }
                    });
                }
            }
        });
        mBinding.taskDetailBottomFlow.commentBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EventTrackerKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_TASKDETAILS_COMMENT);
                TaskDetailEntity entity = mBinding.getTaskDetailViewModel().getTaskDetail().getValue();
                if (!JoyWorkContentExKt.canComments(entity == null ? null : entity.getJoyWorkDetail())) {
                    ToastUtils.showToast(getString(R.string.joy_work_no_athority));
                    return;
                }
                JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_COMMENT, JoyWorkConstant.JOYWORK_DETAIL_COMMENT);
                if (mBinding.taskDetailPage.comment.getVisibility() != View.VISIBLE) {
                    ToastUtils.showToast(getString(R.string.joy_work_waiting));
                    return;
                }//评论逻辑：1、js回调scrollWebToReplyY。2、滚动到Y然后返回js输入框显示坐标
                sendEventToWeb("NATIVE_EVENT_COMMENT_CLICKED");

                UpdateReporter.INSTANCE.reportUpdate();
            }
        });
        mBinding.taskDetailPage.riskDiscuss.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TaskDetailEntity entity = mBinding.getTaskDetailViewModel().getTaskDetail().getValue();
                if (!JoyWorkContentExKt.canComments(entity == null ? null : entity.getJoyWorkDetail())) {
                    ToastUtils.showToast(getString(R.string.joy_work_no_athority));
                    return;
                }
                if (entity != null && entity.getJoyWorkDetail() != null && entity.getJoyWorkDetail().riskStatus != null) {
                    if (entity.getJoyWorkDetail().riskStatus == RiskEnum.PROBLEM.getCode()) {
                        JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_PROBLEM_DETAIL_DISCUSS, JoyWorkConstant.DETAIL_PROBLEM_DETAIL_DISCUSS);
                    } else {
                        JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_RISK_DETAIL_DISCUSS, JoyWorkConstant.DETAIL_RISK_DETAIL_DISCUSS);
                    }
                }
                JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_COMMENT, JoyWorkConstant.JOYWORK_DETAIL_COMMENT);
                if (mBinding.taskDetailPage.comment.getVisibility() != View.VISIBLE) {
                    ToastUtils.showToast(getString(R.string.joy_work_waiting));
                    return;
                }//评论逻辑：1、js回调scrollWebToReplyY。2、滚动到Y然后返回js输入框显示坐标
                sendEventToWeb("NATIVE_EVENT_COMMENT_CLICKED");
            }
        });
        getActivity().getWindow().getDecorView().setTag(this);
        mBinding.taskDetailPage.scrollPage.setOnTaskScrollChangedListener(new TaskScrollView.OnTaskScrollChangedListener() {
            @Override
            public void onScrollChanged() {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (lastKeyBoardHeight > 0) {//任何滚动都结束编辑状态，触发onKeyboardHeightChanged
                            editTitleFinish();
                            if (runnable == null) {
                                TaskUiUtils.hideKeyboard();
                            }
                        }
                    }
                });
            }
        });
        documentsAdapter = new DocumentsAdapter(this.model, new DocumentClickCallback() {
            @Override
            public void onClick(Documents documents) {
                Uri uri = Uri.parse(documents.getUrl());
                String deepLink = uri.getQueryParameter("jdme_router");
                Router.build(deepLink).go(AppBase.getTopActivity());
            }

            @Override
            public void onDelClick(Documents documents) {
                TaskDetailFragment.this.model.delJouSpace(documents);
            }
        });
        mBinding.taskDetailPage.docList.setAdapter(documentsAdapter);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, true);
        likeAdapter = new MembersAdapter(this.model, taskDetailPresenter, MAX_SHOW_LIKE);
        mBinding.taskDetailPage.likeList.setAdapter(likeAdapter);
        mBinding.taskDetailPage.likeList.setLayoutManager(linearLayoutManager);

        resourceAdapter = new ResourceAdapter(this.model, new ResourceClickCallback() {
            @Override
            public void onClick(Resources resources) {
                sendFileClickEvent(APP_SOURCE_TASK, getExtension(resources == null ? "" : resources.getName()), resources == null ? "" : resources.getUrl());
                openFileByX5(getActivity(), "JoyWorkAttachment", resources.getUrl(), resources.getName(), resources.getResourceId(), false, true);
            }

            @Override
            public void onDelClick(Resources resources) {
                TaskDetailFragment.this.model.delResource(resources);
            }
        });
        mBinding.taskDetailPage.resList.setAdapter(resourceAdapter);

        mBinding.taskDetailPage.subContainer.setScope(mScope);
        mBinding.taskDetailPage.subContainer.setChildCall(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_SUBTASK, JoyWorkConstant.JOYWORK_DETAIL_SUBTASK);
                addSubTask();
            }
        });
        mBinding.taskDetailPage.subContainer.setEmptyCall(new Runnable() {
            @Override
            public void run() {
                TaskDetailFragment.this.model.updateTaskDetail(TaskDetailFragment.this.model.getTaskDetail().getValue());
            }
        });
        mBinding.taskDetailPage.subContainer.setChildFinishCall(new Runnable() {
            @Override
            public void run() {
                // 子待办完成/取消完成时会回调该方法
                TaskDetailFragment.this.model.updateTaskDetail(TaskDetailFragment.this.model.getTaskDetail().getValue()); // 使用本地数据修改界面
                mUpdateRegionVM.updateUiStatus();
            }
        });
        mBinding.taskDetailPage.child.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_SUBTASK, JoyWorkConstant.JOYWORK_DETAIL_SUBTASK);
                addSubTask();
            }
        });

        NotifyLinearLayout markContainer = view.findViewById(R.id.mark_container);
        markContainer.setChildCall(new View.OnClickListener() {
            @Override
            public void onClick(final View v) {
                final CustomFieldItem curItem = (CustomFieldItem) v.getTag(R.id.tab_1);
                final CustomFieldGroup group = (CustomFieldGroup) v.getTag(R.id.tab_2);
                JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_MARK_DETAIL_CLICK, group.columnId + "," + group.title);
                DialogManager.INSTANCE.showMarkDialog(requireContext(), curItem, group, mTaskId, new Function1<CustomFieldItem, Unit>() {
                    @Override
                    public Unit invoke(CustomFieldItem customFieldItem) {
                        if (customFieldItem == null) {
                            return null;
                        }
                        if (Objects.equals(customFieldItem.detailId, curItem.detailId)) {
                            return null;
                        }
                        UpdateReporter.INSTANCE.reportUpdate();
                        TaskDetailFragment.this.model.updateMark(customFieldItem, group);
                        return null;
                    }
                });
            }
        });

        mBinding.taskDetailHeader.backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean canBack = canBack();
                if (canBack) {
                    getActivity().finish();
                }
            }
        });
        keyboardHeightProvider = new KeyboardHeightProvider(getActivity());
        view.post(new Runnable() {
            @Override
            public void run() {
                keyboardHeightProvider.start();
            }
        });

        mUpdateRegionVM.getValueLiveData().observe(requireActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean integers) {
                if (integers == null || !ObjExKt.isLegalList(mUpdateRegionVM.getUpdateType())) {
                    mBinding.taskDetailPage.mUpdateTipsContainer.setVisibility(View.GONE);
                    mBinding.taskDetailPage.mUpdateTips.setText("");
                    return;
                }
                TaskDetailEntity entity = TaskDetailFragment.this.model.getTaskDetail().getValue();
                if (entity == null || entity.getJoyWorkDetail() == null) {
                    mUpdateRegionVM.clear();
                    return;
                }
                boolean hasOther = ObjExKt.has(entity.getJoyWorkDetail().owners, new Function1<Owner, Boolean>() {
                    @Override
                    public Boolean invoke(Owner owner) {
                        return !JoyWorkExKt.isSelf(owner);
                    }
                });
                hasOther = hasOther || !JoyWorkExKt.selfIsCreator(entity.getJoyWorkDetail().getCreator());
                if (!hasOther) {
                    // 没有其它人，清单所有记录。最终还会回调到该方法，只不过第一个 if 判断成立，会隐藏掉提示条
                    mUpdateRegionVM.clear();
                    return;
                }
                mBinding.taskDetailPage.mUpdateTipsContainer.setVisibility(View.VISIBLE);
                mBinding.taskDetailPage.mUpdateTips.setText(getResources().getString(R.string.joywork_update_text, concatUpdateRegionText(mUpdateRegionVM.getUpdateType())));
            }
        });

        mUpdateRegionVM.getOuterTipLiveData().observe(requireActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean == null || !ObjExKt.isLegalList(mUpdateRegionVM.getOuterTipType())) {
                    return;
                }
                TaskDetailEntity entity = TaskDetailFragment.this.model.getTaskDetail().getValue();
                if (entity == null || entity.getJoyWorkDetail() == null) {
                    return;
                }
                UpdateReporter.updateWorkInfo(JoyWorkDetail.toWorkInfo(entity.getJoyWorkDetail()));
                mUpdateRegionVM.clear();
            }
        });

        mBinding.taskDetailPage.mUpdateTipsContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_NOTIFY, JoyWorkConstant.DETAIL_NOTIFY);
                notifyUpdate(false);
            }
        });
        mBinding.taskDetailPage.mUpdateTipsBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_NOTIFY_SEND_BTN, JoyWorkConstant.DETAIL_NOTIFY_SEND_BTN);
                notifyUpdate(false);
            }
        });

        initWebView(mBinding);
        subscribeToModel(this.model);
        mValue.setGrayInfo(new GrayInfo());
        KpiRepo.INSTANCE.grayInfo(new Function1<GrayInfo, Unit>() {
            @Override
            public Unit invoke(GrayInfo grayInfo) {
                mValue.setGrayInfo(grayInfo);
                return null;
            }
        });

        mValue.mCreateViewModel.getTargetChangeLivedata().observe(getActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (mValue.mergeTargetsAndKrs().isEmpty()) {
                    itemFactories.removeByClass(DetailTargetCreateFactory.class);
                }
            }
        });
        mValue.mCreateViewModel.getKrChangeLivedata().observe(getActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (mValue.mergeTargetsAndKrs().isEmpty()) {
                    itemFactories.removeByClass(DetailTargetCreateFactory.class);
                }
            }
        });
        markDyRead();
    }

    /**
     * 拼接修改部分文案
     */
    private String concatUpdateRegionText(List<Integer> integers) {
        List<String> items = new ArrayList<>();
        for (Integer pos : integers) {
            switch (pos) {
                case UpdateRegionVM.type_title:
                    items.add(getResources().getString(R.string.joywork_update_title));
                    break;
                case UpdateRegionVM.type_deadline:
                    items.add(getResources().getString(R.string.joywork_update_deadline));
                    break;
                case UpdateRegionVM.type_desc:
                    items.add(getResources().getString(R.string.joywork_update_desc));
                    break;
                case UpdateRegionVM.type_priority:
                    items.add(getResources().getString(R.string.joywork_update_priority));
                    break;
                case UpdateRegionVM.type_dup:
                    items.add(getResources().getString(R.string.joywork_update_dup));
                    break;
                case UpdateRegionVM.type_start_time:
                    items.add(getResources().getString(R.string.joywork_update_start));
                    break;
                case UpdateRegionVM.type_remind:
                    items.add(getResources().getString(R.string.joy_work_remind));
                    break;
            }
        }
        return ObjExKt.joinLegalString(items, ",");
    }

    private final Runnable addSubJoyworkRunnable = new Runnable() {
        @Override
        public void run() {
            model.updateTaskDetailFromWeb();
        }
    };

    private final Function<Integer, Void> subjoyworkSuccess = new Function<Integer, Void>() {
        @Override
        public Void apply(Integer input) {
            Toast.makeText(requireContext(), requireContext().getString(R.string.joywork_create_subjoywork_success, "" + input), Toast.LENGTH_SHORT).show();
            return null;
        }
    };

    /**
     * 添加子待办
     */
    private void addSubTask() {
        if (model.getTaskDetail() != null) {
            ObjectLocalKt.putLocal(this, model.getTaskDetail().getValue());
        }
        JoyWorkHandler.getInstance().addCreateObserver(addSubJoyworkRunnable);
        JoyWorkHandler.getInstance().addCreateCallback(subjoyworkSuccess);
        JoyWorkShortcutDialog showShortcutDialog = JoyWorkShortcutCreator.INSTANCE.showShortcutDialog(requireActivity(), model.getParentId(), TaskListTypeEnum.CHILD);
        showShortcutDialog.setShowSnackBar(false);
        showShortcutDialog.setSuccessCallback(new Function3<String, Value, JoyWorkShortcutDialog, Unit>() {
            @Override
            public Unit invoke(String s, Value value, JoyWorkShortcutDialog joyWorkShortcutDialog) {
                JoyWorkHandler.getInstance().removeCreateObserver(addSubJoyworkRunnable);
                try {
                    JSONObject object = new JSONObject(s);
                    if ("0".equals(object.getString("errorCode"))) {
                        String content = object.getJSONObject("content").toString();
                        Gson gson = new GsonBuilder().serializeNulls().create();
                        ChildWorks entity = gson.fromJson(content, ChildWorks.class);
                        ArrayList<ChildWorks> works = new ArrayList<>();
                        works.add(entity);
                        // 不判断了，空指针时到 catch
                        model.addSubTask(works);
                    } else {
                        model.updateTaskDetailFromWeb();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    model.updateTaskDetailFromWeb();
                }
                if (joyWorkShortcutDialog != null) {
                    joyWorkShortcutDialog.dismiss();
                }
                return null;
            }
        });
    }

    public void sendEventToWeb(String event) {
        if (mBinding == null) {
            return;
        }
        String param = requireActivity().getIntent().getStringExtra("mparam");
        if ("NATIVE_EVENT_PAGE_ANCHOR".equals(event)) {
            try {
                JSONObject jsonObject;
                if (param != null) {
                    jsonObject = new JSONObject(param);
                } else {
                    jsonObject = new JSONObject();
                }
                if (!mScrolledToComment && !TextUtils.isEmpty(mCommentId) && !jsonObject.has("commentId")) {
                    jsonObject.put("commentId", mCommentId);
                    mScrolledToComment = true;
                }
                mBinding.taskDetailPage.comment.callHandler(event, new Object[]{jsonObject}, new OnReturnValue<Integer>() {
                    @Override
                    public void onValue(Integer retValue) {
                    }
                });
                requireActivity().getIntent().removeExtra("mparam");
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else {
            mBinding.taskDetailPage.comment.callHandler(event, new OnReturnValue<Integer>() {
                @Override
                public void onValue(Integer retValue) {
                }
            });
        }
    }

    private void markDyRead() {
        String param = requireActivity().getIntent().getStringExtra("mparam");
        try {
            JSONObject object = new JSONObject(param);
            String id = object.getString("dyId");
            NotifyRepo.INSTANCE.markRead(id, new Function1<Boolean, Unit>() {
                @Override
                public Unit invoke(Boolean aBoolean) {
                    if (aBoolean) {
                        JoyWorkMsgCenter.INSTANCE.notifyNotificationRead(id);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            // empty
        }
    }

    private void initWebView(@NonNull final TaskDetailFragmentBinding mBinding) {
        final JMEWebview webView = mBinding.taskDetailPage.comment;
        webView.setOnLongClickListener(new View.OnLongClickListener() {

            @Override
            public boolean onLongClick(View v) {
                return true;
            }
        });
        jsSdkKit = new JsSdkKit();
        webView.addJavascriptObject(new JsDeviceInfo(jsSdkKit), JsDeviceInfo.DOMAIN);
        webView.addJavascriptObject(new JsViewBrowser(jsSdkKit, this, webView), JsViewBrowser.DOMAIN);
        webView.addJavascriptObject(new JsEvent(webView) {
            @Override
            @JavascriptInterface
            public void sendEvent(Object args, CompletionHandler<Object> handler) {
                super.sendEvent(args, handler);
                WebViewStatistics.INSTANCE.end();
            }
        }, JsEvent.DOMAIN);
        webView.addJavascriptObject(new JsIm(jsSdkKit, null), JsIm.DOMAIN);
        webView.addJavascriptObject(new JsPopup(), JsPopup.DOMAIN);
        webView.addJavascriptObject(new JsUser(webView, null), JsUser.DOMAIN);
        webView.addJavascriptObject(new JsAlbum(jsSdkKit), JsAlbum.DOMAIN);
        webView.addJavascriptObject(new JsFile(jsSdkKit, null), JsFile.DOMAIN);
        webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
        webView.getSettings().setTextZoom(100);
        JMEWebview.setDebugMode(BuildConfig.DEBUG);
        if (AppBase.DEBUG) {
            JMEWebview.setWebContentsDebuggingEnabled(true);
        }
        final long start = System.currentTimeMillis();
        webView.setWebViewClient(new WebViewClient() {

            @Override
            public void onPageFinished(WebView webView, String s) {
                super.onPageFinished(webView, s);
                if (lastUrl != null) {
                    Log.e(WebViewStatistics.INSTANCE.getTAG(), "lastUrl = " + lastUrl + ", " + (System.currentTimeMillis() - lastStart));
                }
            }

            @Override
            public void onReceivedError(WebView webView, WebResourceRequest webResourceRequest, WebResourceError webResourceError) {
                super.onReceivedError(webView, webResourceRequest, webResourceError);
            }

            private String lastUrl;
            private long lastStart = start;

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest webResourceRequest) {
                if (lastUrl != null) {
                    Log.e(WebViewStatistics.INSTANCE.getTAG(), "lastUrl = " + lastUrl + ", " + (System.currentTimeMillis() - lastStart));
                }
                lastUrl = webResourceRequest.getUrl().toString();
                lastStart = System.currentTimeMillis();
                return super.shouldInterceptRequest(webView, webResourceRequest);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }
        });
    }

    private void subscribeToModel(final TaskDetailViewModel model) {
        model.getTaskDetail().observe(getViewLifecycleOwner(), new Observer<TaskDetailEntity>() {
            @Override
            public void onChanged(TaskDetailEntity taskDetailEntity) {
                updateHeight();
                if (taskDetailEntity != null && taskDetailEntity.getJoyWorkDetail() != null) {
                    documentsAdapter.submitList(taskDetailEntity.getJoyWorkDetail().getDocuments());
                    documentsAdapter.notifyDataSetChanged();
                    resourceAdapter.submitList(taskDetailEntity.getJoyWorkDetail().getResources());
                    resourceAdapter.notifyDataSetChanged();
                    likeAdapter.submitList(taskDetailEntity.getJoyWorkDetail().getTaskPraises());
                    likeAdapter.notifyDataSetChanged();
                }
            }
        });
    }

    private void updateHeight() {//只要键盘、或live data有变化都保存各种控件高度，便于web都滚动计算
        webToTop = mBinding.taskDetailPage.scrollPage.getChildAt(0).getHeight() - mBinding.taskDetailPage.keyboardBlank.getHeight() - mBinding.taskDetailPage.comment.getHeight();
    }

    void onJsResult(final int requestCode, final int resultCode, final Intent data) {
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_AT, JoyWorkConstant.JOYWORK_DETAIL_AT);
        if (requestCode == REQUEST_CODE_SELECT_CONTACT) {//web评论的at功能会回调这里
            runnable = new Runnable() {
                @Override
                public void run() {//这里防止键盘收起的时候onKeyboardHeightChanged退出评论状态
                }
            };
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (jsSdkKit != null) {
                        CompletionHandler<Object> handler = jsSdkKit.getHandler(requestCode);
                        if (handler != null) {
                            jsSdkKit.onActivityResult(requestCode, resultCode, data);
                        } else {
                            sendToWebNew(requestCode, resultCode, data);
                        }
                    } else {
                        sendToWebNew(requestCode, resultCode, data);
                    }
                }
            }, 0);
        }
    }

    private void sendToWebNew(final int requestCode, final int resultCode, final Intent data) {
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping.get(requestCode).call(data, resultCode);
        }
    }

    @Override
    public void onResume() {
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(this);
        }
        JDMAUtils.onEventPagePV(requireContext(), JoyWorkConstant.JOYWORK_PAGE_DETAIL, JoyWorkConstant.JOYWORK_PAGE_DETAIL);
        super.onResume();
    }

    @Override
    public void onPause() {
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(null);
        }
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        if (mBinding != null) {
            mBinding.taskDetailPage.comment.loadUrl("about:blank");
            mBinding.taskDetailPage.comment.destroy();  // 销毁web view
        }
        try {
            handler.removeCallbacksAndMessages(null);
        } catch (Throwable throwable) {

        }
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(receiver);
        documentsAdapter = null;
        resourceAdapter = null;
        likeAdapter = null;
        mBinding = null;
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.close();
        }
        JoyWorkTaskSelector.unregister();
        for (CreateItemFactory factory : getMItemFactories()) {
            factory.removeFromUI();
            factory.release();
        }
        super.onDestroyView();
    }

    void focus() {
        mBinding.taskDetailBottomFlow.bottomBtn.setVisibility(View.INVISIBLE);
        mBinding.taskDetailPage.comment.requestFocus();
    }

    private void editTitleFinish() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (model.getTitleEdit() && mBinding != null) {
                    model.setTitleEdit(false);
                    String title = mBinding.taskDetailPage.title.getText().toString();
                    TaskUiUtils.hideKeyboard();
                    if (title != null && title.length() != 0 && !title.equals(titleOld)) {
                        titleOld = null;
                        model.updateTitle(getActivity(), title, () -> mUpdateRegionVM.updateTitle());
                    } else {
                        DialogUtils.removeLoadDialog(getActivity());
                    }
                }
            }
        }, 0);
    }

    private void scrollDelayed(final NestedScrollView scrollView) {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (runnable != null) {
                    runnable.run();
                }
            }
        }, 150);//后面定时多次重试，防止有的手机滚动停不下来
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (scrollView.getScrollY() == webNeedToY) {
                    runnable = null;
                } else {
                    if (runnable != null) {
                        runnable.run();
                        runnable = null;
                    }
                }
            }
        }, 500);
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (scrollView.getScrollY() == webNeedToY) {
                    runnable = null;
                } else {
                    if (runnable != null) {
                        runnable.run();
                        runnable = null;
                    }
                }
            }
        }, 1000);
    }

    void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode,
                          @Nullable Intent data) {
        if (taskDetailPresenter != null) {
            taskDetailPresenter.onActivityResult(activity, requestCode, resultCode, data);
        }
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping.get(requestCode).call(data, resultCode);
        }
    }

    public void chooseFileFromJS(final Map<String, Object> params) {
        Gson gson = new Gson();
        String paramsJson = gson.toJson(params.get("fileList"));
        List<Documents> list = gson.fromJson(paramsJson, new TypeToken<List<Documents>>() {
        }.getType());
        model.addJoySpace(list);
    }

    void scrollWebToReplyY(final boolean showKeyboard, final int y, final ScrollCallback scrollCallback) {
        runnable = new Runnable() {
            @Override
            public void run() {
                if (mBinding == null) {
                    return;
                }
                int inputToWebBottom = mBinding.taskDetailPage.comment.getHeight() - y - mBinding.blankView.getHeight() + keyBoardShowHeight;
                if (inputToWebBottom < 0) {
                    inputToWebBottom = 0;
                }
                scrollCallback.finish(inputToWebBottom);
                webNeedToY = webToTop + y;
                mBinding.taskDetailPage.scrollPage.smoothScrollTo(0, webNeedToY, 500);
            }
        };//此方法是js的回调，在评论的时候触发。runnable用来记录键盘弹出来以后的操作
        if (!showKeyboard) {
            mBinding.taskDetailPage.scrollPage.post(runnable);
//            runnable.run();
            return;
        }
        handler.post(new Runnable() {
            @Override
            public void run() {//有时候输入框会自动弹出来，就暂停300，然后判断无输入框再主动弹出
                focus();
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (lastKeyBoardHeight <= 0 && mBinding != null) {
                            TaskUiUtils.showKeyboard(mBinding.taskDetailPage.comment);
                        }
                    }
                }, 300);
            }
        });
    }

    @Override
    public void onKeyboardHeightChanged(final int height, int orientation) {
        if (height == lastKeyBoardHeight) {
            return;
        } else {
            lastKeyBoardHeight = height;
            updateHeight();
        }
        if (height > keyBoardShowHeight) {
            keyBoardShowHeight = height;
        }//监控键盘弹出来以后，才执行事先准备的runnable，如果没有，则收起键盘的同时通知js退出评论状态
        handler.post(new Runnable() {
            @Override
            public void run() {
                final LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mBinding.taskDetailPage.keyboardBlank.getLayoutParams();
                layoutParams.height = height;
                mBinding.taskDetailPage.keyboardBlank.setLayoutParams(layoutParams);
                updateHeight();
                if (height <= 0) {
                    editTitleFinish();
                    if (runnable == null) {
                        if (mBinding.taskDetailBottomFlow.bottomBtn.getVisibility() != View.VISIBLE) {
                            mBinding.taskDetailBottomFlow.bottomBtn.setVisibility(View.VISIBLE);
                            sendEventToWeb("NATIVE_EVENT_KEYBOARD_WILL_HIDE");
                        }
                    }
                } else {
                    if (runnable != null) {
                        scrollDelayed(mBinding.taskDetailPage.scrollPage);
                    }
                }
            }
        });
    }

    boolean canBack() {
        if (model == null || mBinding == null) {
            return true;
        }
        if (ObjExKt.isLegalList(mUpdateRegionVM.getUpdateType())) {
            notifyUpdate(true);
            return false;
        } else {
            return true;
        }
    }

    private void notifyUpdate(final boolean isFinish) {
        if (!ObjExKt.isLegalList(mUpdateRegionVM.getUpdateType())) {
            return;
        }
        if (isFinish) {
            requireActivity().finish();
            mUpdateRegionVM.clear();
            return;
        }
        String text = concatUpdateRegionText(mUpdateRegionVM.getUpdateType());
        String s = getResources().getString(R.string.joywork_update_dialog, text);
        JoyWorkDialog.INSTANCE.showAlertDialog(requireContext(), getResources().getString(R.string.joywork_update_dialog_title), s, R.string.joywork_urge_send_btn, R.string.joywork_no_send, new Function1<Dialog, Unit>() {
            @Override
            public Unit invoke(Dialog dialog) {
                dialog.dismiss();
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_DIALOG_SEND, JoyWorkConstant.DETAIL_DIALOG_SEND);
                if (model.getTaskDetail().getValue() == null) {
                    return null;
                }
                if (model.getTaskDetail().getValue().getJoyWorkDetail() == null) {
                    return null;
                }
                HashMap<String, Object> map = new HashMap<>();
//                    "cycle" : 3,
//                            "startTime" : 1657414800000,
//                            "endTime" : 1658397600000,
//                            "priorityType" : 3,
//                            "remark" : "rrr"
                JoyWorkDetail entity = model.getTaskDetail().getValue().getJoyWorkDetail();
                for (Integer integer : mUpdateRegionVM.getUpdateType()) {
                    switch (integer) {
                        case UpdateRegionVM.type_deadline:
                            map.put("endTime", entity.getEndTime() == null ? -1 : entity.getEndTime());
                            break;
                        case UpdateRegionVM.type_start_time:
                            map.put("startTime", entity.getStartTime() == null ? -1 : entity.getStartTime());
                            break;
                        case UpdateRegionVM.type_remind:
                            map.put("remindStr", entity.remindStr == null ? AlertType.NO + "" : entity.remindStr);
                            break;
                        case UpdateRegionVM.type_desc:
                            map.put("remark", entity.getRemark() == null ? "" : entity.getRemark());
                            break;
                        case UpdateRegionVM.type_dup:
                            map.put("cycle", entity.cycle == null ? DuplicateEnum.NO.getValue() : entity.cycle);
                            break;
                        case UpdateRegionVM.type_title:
                            map.put("title", entity.getTitle() == null ? "" : entity.getTitle());
                            break;
                        case UpdateRegionVM.type_priority:
                            map.put("priorityType", entity.getPriorityType());
                            break;
                    }
                }

                if (!ObjExKt.isLegalMap(map)) {
                    return null;
                }
                JoyWorkRepo.INSTANCE.notifyUpdate(mTaskId, map, new Function1<String, Unit>() {
                    @Override
                    public Unit invoke(String s) {
                        if (ObjExKt.isLegalString(s)) {
                            // 失败时提示
                            ToastUtils.showToast(s);
                        } else {
                            mUpdateRegionVM.clear();
                        }
                        return null;
                    }
                });
                return null;
            }
        }, new Function2<View, Dialog, Unit>() {
            @Override
            public Unit invoke(View view, Dialog dialog) {
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_DIALOG_NO_SEND, JoyWorkConstant.DETAIL_DIALOG_NO_SEND);
                dialog.dismiss();
                return null;
            }
        });
    }

    public void onFinish() {

    }

    @Override
    public void onStop() {
        super.onStop();
        editTitleFinish();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CoroutineScopeKt.cancel(mScope, null);
        ObjectLocalKt.removeLocal(this);
        JoyWorkHandler.getInstance().removeCreateObserver(addSubJoyworkRunnable);
        JoyWorkHandler.getInstance().removeCallback(subjoyworkSuccess);
    }

    public void updateWebView() {
        sendEventToWeb("NATIVE_EVENT_COMMENT_REFRESH");
    }

    private ViewTreeObserver.OnDrawListener listener = new ViewTreeObserver.OnDrawListener() {

        @Override
        public void onDraw() {
            mBinding.taskDetailPage.updateDes.post(new Runnable() {
                @Override
                public void run() {
                    TaskDetailBindingAdapter.maxWidth(mBinding.taskDetailPage.updateDes);
                    TaskDetailBindingAdapter.maxWidth(mBinding.taskDetailPage.riskDiscuss);
                    TaskDetailBindingAdapter.maxWidth(mBinding.taskDetailPage.riskResolve);
                    mBinding.taskDetailPage.updateDes.getViewTreeObserver().removeOnDrawListener(listener);
                }
            });
        }
    };

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        try {
            mBinding.taskDetailPage.updateDes.getViewTreeObserver().removeOnDrawListener(listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            mBinding.taskDetailPage.updateDes.getViewTreeObserver().addOnDrawListener(listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 给 TaskDetailBindingAdatper 使用
    public DetailArrayList itemFactories = new DetailArrayList();
    public Value mValue;

    public boolean hasItemFactory(Class<? extends CreateItemFactory> clazz) {
        for (CreateItemFactory factory : itemFactories) {
            if (factory.getClass() == clazz) {
                return true;
            }
        }
        return false;
    }

    @NonNull
    @Override
    public CreateItemFactoryList getMItemFactories() {
        return itemFactories;
    }

    @NonNull
    @Override
    public Fragment getFragment() {
        return this;
    }

    @Override
    public boolean moreOwners() {
        return false;
    }


    void cancelDeadlineTime() {
        mBinding.getTaskDetailViewModel().cancelTime(mTaskId, model.getTaskDetail().getValue(), mUpdateRegionVM);
//        mBinding.getTaskDetailViewModel().cancelTime(mTaskId);
    }

    void cancelLaunchTime() {
        mBinding.getTaskDetailViewModel().cancelLaunchTime(mTaskId);
    }

    public void updateDeadline(Value value) {
        Value v = value == null ? mValue : value;
        if (v == null)
            return;
        taskDetailPresenter.updateDeadlineNet(v, model.getTaskDetail().getValue());
    }

    /**
     * 是否可更新清单
     */
    public boolean canUpdaterOrder() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        return JoyWorkContentExKt.canUpdateGroup(entity == null ? null : entity.getJoyWorkDetail());
    }

    /**
     * 是否可更新目标
     */
    public boolean canUpdaterTarget() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        return JoyWorkContentExKt.canUpdateTarget(entity == null ? null : entity.getJoyWorkDetail());
    }

    /**
     * 是否可更新执行人
     */
    public boolean canUpdateOwner() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        return JoyWorkContentExKt.canTransfer(entity == null ? null : entity.getJoyWorkDetail());
    }

    /**
     * 是否可转派
     */
    public boolean canTransfer() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().owners == null || entity.getJoyWorkDetail().owners.isEmpty()) {
            return false;
        }
        boolean hadSelf = false;
        for (Owner owner : entity.getJoyWorkDetail().owners) {
            if (!hadSelf) {
                hadSelf = JoyWorkExKt.isSelf(owner);
            }
        }
        return JoyWorkContentExKt.canTransfer(entity.getJoyWorkDetail()) && hadSelf;
    }

    /**
     * 是否可修改负责人，非执行人
     */
    public boolean canUpdateChief() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        return JoyWorkContentExKt.canUpdateChief(entity == null ? null : entity.getJoyWorkDetail());
    }

    /**
     * 能否更新截止时间
     */
    public boolean canUpdateDeadline() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        return JoyWorkContentExKt.canUpdateDeadline(entity == null ? null : entity.getJoyWorkDetail());
    }

    /**
     * 是否可更新关注人
     */
    public boolean canUpdateRelation() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        return JoyWorkContentExKt.canUpdateRelations(entity == null ? null : entity.getJoyWorkDetail());
    }

    public void selectTime() {
        taskDetailPresenter.deadline(getActivity(), model.getTaskDetail().getValue());
    }

    /**
     * 跳转至关注人列表
     */
    public void toRelationList() {
        taskDetailPresenter.addRelation(getActivity(), model.getTaskDetail().getValue(), canUpdateRelation());
    }

    public HyperLinkHelper.OnClickListener getHyperLinkClick() {
        return new HyperLinkHelper.OnClickListener() {
            @Override
            public void onUrlLinkClick(@NonNull View widget, @NonNull String url) {
                taskDetailPresenter.onHyperLinkClick(url);
            }
        };
    }

    public void toRelationList(final ArrayList<Members> members) {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        List<Members> executors = entity.getJoyWorkDetail().getExecutors();
        ObjExKt.union(members, executors, new Function1<Members, String>() {
            @Override
            public String invoke(Members members) {
                return members.getDdAppId() + "," + members.getEmplAccount();
            }
        });
        changeRelation(members);
    }

    /**
     * 调用接口，修改所有关注人
     */
    public void changeRelation(final List<Members> members) {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        for (Members member : members) {
            member.setUserRole(TaskUserRole.EXECUTOR.getCode());
            member.setTeamId(JoyWorkUser.DEFAULT_TEAM_ID);
        }
        JoyWorkRepo.INSTANCE.addRelation(members, entity.getJoyWorkDetail().getTaskId(), actionSource, new Function1<List<? extends Members>, Unit>() {
            @Override
            public Unit invoke(List<? extends Members> m) {
                model.changeRelation(members);
                return null;
            }
        }, new Function1<String, Unit>() {
            @Override
            public Unit invoke(String s) {
                return null;
            }
        });
    }

    /**
     * 更新提醒时间
     */
    public void updateAlarm() {
        JDMAUtils.clickEvent("", JoyWorkConstant.DETAIL_SET_REMINDER, null);
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        taskDetailPresenter.alert(getActivity(), entity);
    }

    public void cancelAlarm() {
        getMItemFactories().removeByClass(DetailAlarmFactory.class);
        mBinding.getTaskDetailViewModel().cancelRemain(mTaskId, mUpdateRegionVM);
    }

    /**
     * 是否还有剩余的项没有显示
     */
    public boolean hasRemainUI() {
        if (filterAction(JoyWorkActionEnum.GROUP)) {
            return true;
        }
        if (filterAction(JoyWorkActionEnum.DUP)) {
            return true;
        }
        if (filterAction(JoyWorkActionEnum.REMIND)) {
            return true;
        }
        if (filterAction(JoyWorkActionEnum.PRIORITY)) {
            return true;
        }
        if (filterAction(JoyWorkActionEnum.ADD_CHILD)) {
            return true;
        }
        if (filterAction(JoyWorkActionEnum.LAUNCH_TIME)) {
            return true;
        }
        return false;
    }

    /**
     * 对更多菜单里面的菜单项进行过滤
     *
     * @return true 表示要显示，否则不显示
     */
    public final boolean filterAction(JoyWorkActionEnum actionEnum) {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        if (entity == null || entity.getJoyWorkDetail() == null || mBinding == null) {
            return false;
        }
        JoyWorkDetail detail = entity.getJoyWorkDetail();
        // 如果界面上显示有该内容，则菜单项里不需要显示
        if (actionEnum == JoyWorkActionEnum.DUP) {
            // 重复
            return mBinding.taskDetailPage.dupContainer.getVisibility() != View.VISIBLE && JoyWorkContentExKt.canUpdateDuplicate(detail);
        } else if (actionEnum == JoyWorkActionEnum.GROUP) {
            // 关联清单
            return mBinding.taskDetailPage.orderContainer.getVisibility() != View.VISIBLE && JoyWorkContentExKt.canUpdateGroup(detail);
        } else if (actionEnum == JoyWorkActionEnum.TARGET) {
            if (!mValue.safeGrayInfo().hasGoalPermission()) {
                return false;
            }
            // 关联目标
            return mBinding.taskDetailPage.targetContainer.getVisibility() != View.VISIBLE && JoyWorkContentExKt.canUpdateTarget(detail);
        } else if (actionEnum == JoyWorkActionEnum.REMIND) {
            return mBinding.taskDetailPage.remindContainer.getVisibility() != View.VISIBLE && JoyWorkContentExKt.canUpdateRemind(detail);
        } else if (actionEnum == JoyWorkActionEnum.PRIORITY) {
            // 优先级
            return mBinding.taskDetailPage.priorityContainer.getVisibility() != View.VISIBLE && JoyWorkContentExKt.canUpdateLevel(detail);
        } else if (actionEnum == JoyWorkActionEnum.ADD_CHILD) {
            // 添加子待办
            return mBinding.taskDetailPage.subTaskTitle.getVisibility() != View.VISIBLE && JoyWorkContentExKt.canUpdateChildWorks(detail);
        } else if (actionEnum == JoyWorkActionEnum.LAUNCH_TIME) {
            // 启动时间
            boolean isOwner = JoyWorkContentExKt.selfIsOwner(entity.getJoyWorkDetail());
            return mBinding.taskDetailPage.launchContainer.getVisibility() != View.VISIBLE && isOwner && JoyWorkContentExKt.canUpdateLaunchTime(detail);
        }
        return false;
    }

    public final void handleMoreAction(JoyWorkActionEnum actionEnum) {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        if (entity == null || entity.getJoyWorkDetail() == null) {
            return;
        }
        if (actionEnum == JoyWorkActionEnum.DUP) {
            // 重复
            if (ObjExKt.isLegalTimestamp(entity.getJoyWorkDetail().getEndTime())) {
                taskDetailPresenter.duplicate(mBinding.taskDetailPage.dupContainer, entity);
            } else {
                // 没有设置截止时间，不能选择重复
                Toast.makeText(getActivity(), R.string.joywork_create_dup_tips, Toast.LENGTH_SHORT).show();
            }
        } else if (actionEnum == JoyWorkActionEnum.GROUP) {
            // 关联清单
            TaskDetailPresenter.selectGroup(getActivity(), entity);
        } else if (actionEnum == JoyWorkActionEnum.REMIND) {
            if (!ObjExKt.isLegalTimestamp(entity.getJoyWorkDetail().getEndTime())) {
                Toast.makeText(requireContext(), R.string.joywork_alert_deadline, Toast.LENGTH_SHORT).show();
                return;
            }
            // 提醒
            taskDetailPresenter.alert(mBinding.taskDetailPage.remindContainer, entity);
        } else if (actionEnum == JoyWorkActionEnum.PRIORITY) {
            // 优先级
            taskDetailPresenter.priority(mBinding.taskDetailPage.remindContainer, entity);
        } else if (actionEnum == JoyWorkActionEnum.ADD_CHILD) {
            // 添加子待办
            addSubTask();
        } else if (actionEnum == JoyWorkActionEnum.LAUNCH_TIME) {
            // 启动时间
            taskDetailPresenter.launchTime(mBinding.taskDetailPage.remindContainer, entity);
        } else if (actionEnum == JoyWorkActionEnum.TARGET) {
            TaskDetailPresenter.selectTarget(
                    requireActivity(),
                    entity
            );
        }
    }

    public void transferOwner() {
        ExecutorUtils.INSTANCE.selectTransfer(requireActivity(), new ArrayList<>(), new Function1<ArrayList<MemberEntityJd>, Unit>() {
            @Override
            public Unit invoke(ArrayList<MemberEntityJd> memberEntityJds) {
                if (memberEntityJds == null || memberEntityJds.isEmpty()) {
                    return null;
                }
                MemberEntityJd jd = memberEntityJds.get(0);
                Members m = new Members();
                JoyWorkContentExKt.fromDD(m, jd);
                JoyWorkRepo.INSTANCE.transfer(mTaskId, m, new Function3<Boolean, TransferResult, String, Unit>() {
                    @Override
                    public Unit invoke(Boolean success, TransferResult result, String s) {
                        if (success && result != null) {
                            model.updateTaskDetailFromWeb();
                            ToastUtils.showToast(R.string.joywork_transfer_success);
                            mUpdateRegionVM.updateAssignees();
                        } else {
                            ToastUtils.showInfoToast(s);
                        }
                        return null;
                    }
                });
                return null;
            }
        }, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                return null;
            }
        });
    }

    /**
     * 添加执行人，选择联系人添加成执行人
     */
    public void addOwner() {
        final TaskDetailEntity entity = model.getTaskDetail().getValue();
        if (entity == null || entity.getJoyWorkDetail() == null)
            return;
        ArrayList<MemberEntityJd> selected = new ArrayList<>();
        if (entity.getJoyWorkDetail().owners != null) {
            for (Owner owner : entity.getJoyWorkDetail().owners) {
                MemberEntityJd jd = new MemberEntityJd();
                jd.mApp = owner.getDdAppId();
                jd.mId = owner.getEmplAccount();
                jd.mType = MemberEntityJd.TYPE_CONTACT;
                selected.add(jd);
            }
        }
        ExecutorUtils.INSTANCE.selectExecutors(requireActivity(), selected, null, canUpdateChief(), new Function1<ArrayList<MemberEntityJd>, Unit>() {
            @Override
            public Unit invoke(ArrayList<MemberEntityJd> memberEntityJds) {
                if (memberEntityJds != null && !memberEntityJds.isEmpty()) {
                    List<Members> members = new ArrayList<>();
                    for (MemberEntityJd jd : memberEntityJds) {
                        Members m = new Members();
                        JoyWorkContentExKt.fromDD(m, jd);
                        members.add(m);
                    }
                    for (Members member : members) {
                        member.setUserRole(TaskUserRole.OWNER.getCode());
                        member.setTeamId(JoyWorkUser.DEFAULT_TEAM_ID);
                    }
                    JoyWorkRepo.INSTANCE.addRelation(members, entity.getJoyWorkDetail().getTaskId(), actionSource, new Function1<List<? extends Members>, Unit>() {
                        @Override
                        public Unit invoke(List<? extends Members> m) {
                            UpdateReporter.INSTANCE.reportUpdate();
                            model.updateTaskDetailFromWeb();
                            mUpdateRegionVM.updateAssignees();
                            return null;
                        }
                    }, new Function1<String, Unit>() {
                        @Override
                        public Unit invoke(String s) {
                            return null;
                        }
                    });
                }
                return null;
            }
        }, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                return null;
            }
        });
    }

    /**
     * 去执行人列表
     */
    public void toOwnerList() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        if (entity == null || entity.getJoyWorkDetail() == null) {
            return;
        }
        JoyWorkDetail detail = entity.getJoyWorkDetail();
        if (!ObjExKt.isLegalList(detail.owners)) {
            // 没有负责人
            return;
        }
        Intent i = new Intent(requireContext(), ExecutorStateListActivity.class);
        int p = 0;
        if (JoyWorkContentExKt.isRemindable(detail)) {
            // 可催办
            p = p | ExecutorStateListActivity.Companion.getPermission_urgeable();
        }
        if (canUpdateOwner()) {
            // 可添加执行人
            p = p | ExecutorStateListActivity.Companion.getPermission_addable();
        }
        if (canUpdateOwner()) {
            // 可删除执行人
            p = p | ExecutorStateListActivity.Companion.getPermission_deletable();
        }
        if (JoyWorkContentExKt.canMarkOtherFinish(detail)) {
            // 可标记为完成
            p = p | ExecutorStateListActivity.Companion.getPermission_finishable();
        }
        if (JoyWorkContentExKt.canMarkOtherUnfinish(detail)) {
            // 可标记为未完成
            p = p | ExecutorStateListActivity.Companion.getPermission_unfinishable();
        }
        if (JoyWorkContentExKt.canTransfer(detail)) {
            // 可转派
            p = p | ExecutorStateListActivity.Companion.getPermission_transfer();
        }
        if (JoyWorkContentExKt.canUpdateChief(detail)) {
            // 可更换首席
            p = p | ExecutorStateListActivity.Companion.getPermission_ex_chief();
        }
        ExecutorStateListActivity.Companion.inflateIntent(i, detail.getTaskId(), detail.owners, p, owners -> {
            model.updateTaskDetailFromWeb();
            UpdateReporter.INSTANCE.reportUpdate();
            mUpdateRegionVM.updateAssignees();
            return null;
        });
        startActivity(i);
    }

    /**
     * 统计有多少个已完成的执行人
     */
    public int countFinishOwner() {
        TaskDetailEntity entity = model.getTaskDetail().getValue();
        if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().owners == null || entity.getJoyWorkDetail().owners.isEmpty()) {
            return 0;
        }
        int ans = 0;
        for (Owner owner : entity.getJoyWorkDetail().owners) {
            if (TaskStatusEnum.FINISH.isFinish(owner.taskStatus)) {
                ans++;
            }
        }
        return ans;
    }

    @Nullable
    @Override
    public String getSessionId() {
        return null;
    }

    @Override
    public void operator(Map<String, Object> param) {

    }

    private Map<Integer, DynamicCallback> callbackMapping = new HashMap<>();

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        callbackMapping.put(requestCode, callBack);
    }
}