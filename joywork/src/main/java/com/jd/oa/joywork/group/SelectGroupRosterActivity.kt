package com.jd.oa.joywork.group

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.detail.isRobot
import com.jd.oa.joywork.detail.key
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.executor.SetOwnerActivity
import com.jd.oa.joywork.executor.SetOwnerActivityCallback
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.JDMAUtils
import kotlinx.android.synthetic.main.joywork_select_group_roster.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CombineArrayList {
    var data = ArrayList<MemberEntityJd>()
    private val hash = HashSet<String>()

    var countChangeListener: () -> Unit = {

    }

    fun set(list: List<MemberEntityJd>) {
        data.clear()
        hash.clear()
        addIfAbsent(list)
    }

    fun addAll(list: List<MemberEntityJd>) {
        addIfAbsent(list)
    }

    fun add(jd: MemberEntityJd) {
        if (!contain(jd)) {
            data.add(jd)
            hash.add(jd.key())
            countChangeListener()
        }
    }

    fun remove(jd: MemberEntityJd) {
        if (contain(jd)) {
            data.remove(jd)
            hash.remove(jd.key())
            countChangeListener()
        }
    }

    fun removeAll(jds: List<MemberEntityJd>) {
        jds.forEach { jd ->
            if (contain(jd)) {
                data.remove(jd)
                hash.remove(jd.key())
            }
        }
        countChangeListener()
    }

    fun clear() {
        data.clear()
        hash.clear()
        countChangeListener()
    }

    fun contain(jd: MemberEntityJd): Boolean {
        return hash.contains(jd.key())
    }

    private fun addIfAbsent(list: List<MemberEntityJd>) {
        list.forEach {
            val key = it.key()
            if (!hash.contains(key)) {
                hash.add(key)
                data.add(it)
            }
        }
        countChangeListener()
    }

    operator fun minus(list: CombineArrayList): CombineArrayList {
        val r = CombineArrayList()
        data.forEach {
            if (!list.contain(it)) {
                r.add(it)
            }
        }
        return r
    }

    fun removeExcept(list: CombineArrayList) {
        clear()
        addAll(list.data)
    }
}

/**
 * 选择群组成员列表.
 * When the user click 【select outer members】, we will open contact picker,
 */
class SelectGroupRosterActivity : BaseActivity() {
    companion object {
        private var selected: ArrayList<MemberEntityJd> = ArrayList()
        private var resultCallback: ((selected: ArrayList<MemberEntityJd>?) -> Unit)? =
            null

        /**
         * @param onSuccess: 成功，会将所有已选择成员都返回
         */
        fun start(
            activity: Activity,
            s: ArrayList<MemberEntityJd>?,
            sessionId: String,
            needNext: Boolean,
            result: (selected: ArrayList<MemberEntityJd>?) -> Unit
        ) {
            this.selected = ArrayList()
            s?.forEach {
                selected.add(it)
            }
            resultCallback = result
            val i = Intent(activity, SelectGroupRosterActivity::class.java)
            i.putExtra("sessionId", sessionId)
            i.putExtra("needNext", needNext)
            activity.startActivity(i)
        }

        fun SelectGroupRosterActivity.needNext(): Boolean {
            return intent.getBooleanExtra("needNext", false)
        }

        fun SelectGroupRosterActivity.getSessionId(): String {
            return intent.getStringExtra("sessionId")!!
        }

        private fun init(list: CombineArrayList, list2: CombineArrayList) {
            list2.addAll(this.selected)
            list.addAll(this.selected)
            this.selected.clear()
        }
    }

    // 当前所有选中的人。包括当前群组中的，非群组中的以及初始进来时传入的值
    private val mSelectedList = CombineArrayList()

    /**
     * 进入界面时已添加的执行人
     */
    private val mOriginSelectedList = CombineArrayList()

    // 当前群组联系人
    private val mAllOwners = ArrayList<MemberEntityJd>()

    // 群成员里已被选择的成员
    private val mGroupSelected = CombineArrayList()
    val scope = MainScope()

    private var mDialog: SelectGroupRosterDialog? = null

    /**
     * 需要请求网络拉取用户信息
     */
    private val needGetInfo = ArrayList<MemberEntityJd>()

    private val mSetOwnerActivityCallback = object : SetOwnerActivityCallback {
        override fun finish(activity: SetOwnerActivity) {
            SetOwnerActivity.callback = null
            SetOwnerActivity.select = null
            activity.finish()
        }

        override fun add(activity: SetOwnerActivity) {
            SetOwnerActivity.callback = null
            SetOwnerActivity.select = null
            activity.finish()
        }

        override fun sure(
            members: ArrayList<MemberEntityJd>?,
            activity: SetOwnerActivity,
            owner: JoyWorkUser?
        ) {
            SetOwnerActivity.callback = null
            SetOwnerActivity.select = null
            activity.finish()
            val key = owner?.key() ?: "-"
            mSelectedList.data.forEach {
                if (it.key() == key) {
                    it.setToChief()
                } else {
                    it.cancelChief()
                }
            }
            finish()
        }

        override fun changeOwner(owner: JoyWorkUser?) {
            val key = owner?.key() ?: "-"
            mSelectedList.data.forEach {
                if (it.key() == key) {
                    it.setToChief()
                } else {
                    it.cancelChief()
                }
            }
        }

        override fun remove(owner: JoyWorkUser) {
            val key = owner.key()
            val m = mSelectedList.data.firstOrNull {
                it.key() == key
            } ?: return
            remove(m)
        }

        override fun addUser(joyWorkUser: JoyWorkUser) {
            val first = mAllOwners.firstOrNull {
                it.key() == joyWorkUser.key()
            }
            if (first != null) {
                mGroupSelected.add(first)
                mSelectedList.add(first)
                needGetInfo.add(first)
            } else {
                val jd = MemberEntityJd()
                jd.fromUser(joyWorkUser)
                mSelectedList.add(jd)
                needGetInfo.add(jd)
            }
            mRv.adapter?.notifyDataSetChanged()
            getInfo()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        setContentView(R.layout.joywork_select_group_roster)
        mSelectedList.countChangeListener = {
            mCount.text = resources.getString(
                R.string.joywork_select_group_count,
                (mSelectedList - mOriginSelectedList).data.size,
                ExecutorUtils.MAX_VALUE - mOriginSelectedList.data.size,
            )
            handleSureBtn()
        }
        Companion.init(mSelectedList, mOriginSelectedList)
        mBack.setOnClickListener { finish() }
        mRv.apply {
            layoutManager =
                LinearLayoutManager(
                    this@SelectGroupRosterActivity,
                    LinearLayoutManager.VERTICAL,
                    false
                )
            adapter = GroupRosterAdapter(this@SelectGroupRosterActivity, mAllOwners, { jd, attach ->
                if (attach) {
                    needGetInfo.add(jd)
                } else {
                    needGetInfo.remove(jd)
                }
            }, { user ->
                when {
                    mOriginSelectedList.contain(user) -> {
                        2
                    }
                    mSelectedList.contain(user) -> {
                        0
                    }
                    else -> {
                        1
                    }
                }
            }, { user: MemberEntityJd ->
                if (mSelectedList.contain(user)) {
                    mSelectedList.remove(user)
                } else {
                    if (mSelectedList.data.size >= ExecutorUtils.MAX_VALUE) {
                        ExecutorUtils.toastFull(this@SelectGroupRosterActivity)
                        return@GroupRosterAdapter
                    }
                    mSelectedList.add(user)
                }

                if (mGroupSelected.contain(user)) {
                    mGroupSelected.remove(user)
                } else {
                    mGroupSelected.add(user)
                }
                mRv.adapter?.notifyDataSetChanged()
            })
        }

        if (needNext()) {
            mSure.setText(R.string.joywork_next)
        } else {
            mSure.setText(R.string.joywork_plan_alert_done)
        }

        mSure.setOnClickListener {
            if(needNext()){
                val list = ArrayList<MemberEntityJd>()
                list.addAll(mSelectedList.data)
                list.removeAll {
                    mOriginSelectedList.data.contains(it)
                }
                SetOwnerActivity.select = list
                SetOwnerActivity.callback = mSetOwnerActivityCallback
                SetOwnerActivity.start(this)
            }else{
                finish()
            }
        }

        scope.launch {
            var changed = false
            // 获取咚咚联系人
            withContext(Dispatchers.Default) {
                val groupsRoster = getGroupsRoster(getSessionId())
                groupsRoster.ifSuccessToast {
                    val tmp = it?.filter { jd ->
                        !jd.isRobot()
                    }
                    if (tmp.isLegalList()) {
                        changed = mAllOwners.union(tmp) { jd ->
                            jd.key()
                        }
                    }
                }
            }
            if (changed) {
                // 根据已选中人员列表判断当前群成员有哪些已选中
                mAllOwners.forEach {
                    if (mSelectedList.contain(it)) {
                        mGroupSelected.add(it)
                    }
                }
                mRv.adapter?.notifyDataSetChanged()
            }
        }
        mArrow.setOnClickListener {
            if (mSelectedList.data.isLegalList()) {
                mDialog = SelectGroupRosterDialog(this, (mSelectedList - mOriginSelectedList).data)
                mDialog?.setOnDismissListener {
                    mDialog = null
                }
                mDialog?.show()
            }
        }
        mCount.setOnClickListener {
            if (mSelectedList.data.isLegalList()) {
                mDialog = SelectGroupRosterDialog(this, (mSelectedList - mOriginSelectedList).data)
                mDialog?.setOnDismissListener {
                    mDialog = null
                }
                mDialog?.show()
            }
        }
    }

    private fun getInfo() {
        val tmp = ArrayList(needGetInfo)
        needGetInfo.clear()
        if (tmp.isLegalList()) {
            val net = ArrayList<Members>()
            tmp.forEach {
                if (listOf(it.directDepartment, it.titleName).hasNull()) {
                    val m = Members()
                    m.fromDD(it)
                    net.add(m)
                }
            }
            if (net.isLegalList()) {
                scope.launch {
                    val r = getBatchInfo(net)
                    r.ifSuccessSilence {
                        val infos = HashMap<String, Members>()
                        it?.users?.forEach {
                            infos[it.key()] = it
                        }
                        mSelectedList.data.forEach { jd ->
                            jd.titleName = infos[jd.key()]?.titleInfo?.titleName
                            jd.department = infos[jd.key()]?.deptInfo?.deptName
                        }
                        (mRv.adapter as? GroupRosterAdapter)?.apply {
                            notifyDataSetChanged()
                        }
                    }
                }
            }
        }
    }

    fun clear() {
        mGroupSelected.removeExcept(mOriginSelectedList)
        mSelectedList.removeExcept(mOriginSelectedList)
        mRv.adapter?.notifyDataSetChanged()
        mDialog?.dataChange()
        mDialog?.dismiss()
    }

    fun remove(jd: MemberEntityJd) {
        mGroupSelected.remove(jd)
        mSelectedList.remove(jd)
        mDialog?.dataChange()
        mRv.adapter?.notifyDataSetChanged()
    }

    fun selectAll() {
        JDMAUtils.clickEvent("", JoyWorkConstant.SELECT_GROUP_ROSTER_ALL, null)
        if (mGroupSelected.data.size != mAllOwners.size) {
            // 全选时，判断全部选中后是否会超出最大限制人数
            if (mSelectedList.data.size + mAllOwners.size - mGroupSelected.data.size >= ExecutorUtils.MAX_VALUE) {
                ExecutorUtils.toastFull(this)
                return
            }
            // 全选
            mSelectedList.addAll(mAllOwners)
            mGroupSelected.addAll(mAllOwners)
        } else {
            // 取消全选
            mSelectedList.removeAll(mAllOwners)
            mGroupSelected.clear()
        }
        mRv.adapter?.notifyDataSetChanged()
    }

    fun outerClick() {
        JDMAUtils.clickEvent("", JoyWorkConstant.SELECT_GROUP_ROSTER_OUTER, null)
        ExecutorUtils.selectExecutors(this, mSelectedList.data, null, false, {
            val list = ArrayList<MemberEntityJd>()
            it?.forEach {
                if (it != null) {
                    list.add(it)
                }
            }
            var changed = false
            if (list.isLegalList()) {
                needGetInfo.addAll(list)
                mSelectedList.addAll(list)
                mAllOwners.forEach { jd ->
                    if (mSelectedList.contain(jd)) {
                        mGroupSelected.add(jd)
                        changed = true
                    }
                }
            }
            getInfo()
            if (changed) {
                mRv.adapter?.notifyDataSetChanged()
            }
        }, {

        })
    }

    fun isAll(): Boolean {
        return mGroupSelected.data.size == mAllOwners.size
    }

    private fun handleSureBtn() {
        val list = ArrayList<MemberEntityJd>()
        list.addAll(mSelectedList.data)
        list.removeAll {
            mOriginSelectedList.contain(it)
        }
        if (list.isLegalList()) {
            mSure.setBackgroundResource(R.drawable.solid_all_100_fe3e33)
            mSure.isEnabled = true
        } else {
            mSure.setBackgroundResource(R.drawable.solid_all_100_1afe3e33)
            mSure.isEnabled = false
        }
    }

    override fun onBackPressed() {
        if (mDialog != null) {
            mDialog?.dismiss()
            return
        }
        super.onBackPressed()
    }

    override fun finish() {
        resultCallback?.invoke(mSelectedList.data)
        resultCallback = null
        super.finish()
    }
}