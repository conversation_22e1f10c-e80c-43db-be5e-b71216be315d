package com.jd.oa.joywork.bean;

import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkOp;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.TaskExtend;
import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.expandable.Children;
import com.jd.oa.joywork.expandable.ExpandableGroup;
import com.jd.oa.joywork.expandable.Groupable;
import com.jd.oa.joywork.utils.JoyWorkUtilsKt;
import com.jd.oa.model.ExecutorInfo;
import com.jd.oa.model.WorkInfo;
import com.jd.oa.utils.ExceptionExKt;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Keep
public class JoyWork implements Children<JoyWorkTitle>, Groupable<JoyWorkTitle, JoyWork>, Serializable, Cloneable {
    /**
     * 所有基础数据类型都使用其包装类，因为后台可能会返回 null
     */
    public String bizCode;
    public Boolean thirdProcess;
    // 第三方跳转的 deeplink。当从咚咚来时，该值为一个 json，为兼容旧版本
    public String mobileContent;
    // 第三方系统的名字
    public String sourceName;
    // 列表页“一级分类” 中区分不同分组的 id
    public String inventory;
    // 列表页“一级分类” 中不同分组的标题
    public String tripartiteName;

    public String subInventory;
    public String subTripartiteName;
    public String subMobileContent;

    public String title;
    public Boolean enablePermission;
    public List<String> permission;
    public Integer taskStatus; // 表示待办状态
    public String remindStr;// 提醒时间

    // 列表的完成状态跟执行人自己是否完成待办以及待办是否完成相关，这部分逻辑由后台处理
    // 前端使用 uiTaskStatus 统一表示是否完成
    public Integer uiTaskStatus;// 多执行人时完成状态
    // 执行人、创建人在完成时，涉及到的逻辑不同，此处由后台统一处理，返回 finishAction 表示不同的操作
    public Integer finishAction;// 多执行人时完成操作

    public Integer status; // 表示待办是否被删除。0 表示已删除
    public JoyWorkUser owner;
    // 多负责人
    public List<JoyWorkUser> owners;
    public JoyWorkUser assigner; // 来源人
    public Integer priorityType;
    // 字符串数组，使用的时候需要转换成List
    public String tags;
    // 截止时间
    public Long endTime;
    // 开始时间
    public Long startTime;
    // 计划时间
    public Long planTime;
    public Long gmtCreate;
    public Long completeTime;
    public String taskId;
    public String projectId;
    // 子待办
    public List<JoyWork> childWorks;
    // 是否正在调用完成接口
    public boolean isFinishing = false;
    // 已读未读,0:不展示；1：未读；2：已读"
    public Integer readStatus;
    // 提醒时间
    public Long remindTime;
    // 是否被合并
    public Boolean merged;
    // 风险
    public Integer riskStatus;

    public String extend;
    private List<TaskExtend> extendObj;

    public Map<String, List<String>> customFields;

    /**
     * 对应 {@link DuplicateEnum}
     */
    public Integer cycle;

    public Map<String, List<String>> safeCustomFields() {
        if (customFields == null) {
            customFields = new HashMap<>();
        }
        return customFields;
    }

    // 所属的分组。2021年08月24日 目前只有项目下新建才有
    public List<JoyWorkDetail.Project> projects;
    // 个人待办，按所属清单排序时使用
    public JoyWorkDetail.Project project;

    public JoyWorkComment latestComment;

    public List<TaskExtend> getExtendObj() {
        if (extendObj == null) {
            try {
                /*
                 * 手动解析 extend。将 extend 转成相应对象
                 */
                if (extend != null && !TextUtils.isEmpty(extend)) {
                    JSONArray exArray = new JSONArray(extend);
                    setExtend(new ArrayList<TaskExtend>(exArray.length()));
                    for (int i = 0; i < exArray.length(); i++) {
                        JSONObject object = exArray.getJSONObject(i);
                        TaskExtend extendObj = new TaskExtend();
                        extendObj.setTips(JoyWorkUtilsKt.optStringOrNull(object, "tips", ""));
                        extendObj.setContent(JoyWorkUtilsKt.optStringOrNull(object, "content", ""));
                        extendObj.setType(JoyWorkUtilsKt.optStringOrNull(object, "type", ""));
                        getExtendObj().add(extendObj);
                    }
                }
            } catch (Exception e) {
                ExceptionExKt.logDebug(e);
            }
        }
        return extendObj;
    }

    /**
     * 判断截止时间是否正确
     */
    public boolean isEndDatetimeLegal() {
        return ObjExKt.isLegalTimestamp(endTime);
    }

    /**
     * 是否是重复待办
     */
    public boolean isDup() {
        return this.isEndDatetimeLegal() && cycle != null && DuplicateEnum.Companion.getByValue(cycle) != DuplicateEnum.NO;
    }

    public String getGroupTitle() {
        if (tripartiteName == null || TextUtils.isEmpty(tripartiteName.trim())) {
            return sourceName;
        }
        return tripartiteName;
    }


    /**
     * 返回当前用户是否能完成待办(来源于第三方的不能完成)
     */
    public boolean isCompletable() {
        return hasSpecialPermission(JoyWorkOp.COMPLETE);
    }

    /**
     * 是否有删除权限
     */
    public boolean isDeletable() {
        return hasSpecialPermission(JoyWorkOp.DELETE);
    }

    /**
     * 是否有催办权限
     */
    public boolean isRemindable() {
        return hasSpecialPermission(JoyWorkOp.URGE);
    }

    private boolean hasSpecialPermission(JoyWorkOp op) {
        if (enablePermission != null && enablePermission) {
            if (permission != null && !permission.isEmpty()) {
                for (String s : permission) {
                    if (TextUtils.equals(s, op.getAction())) {
                        return true;
                    }
                }
            }
            return false;
        }
        return false;
    }

    /**
     * 已删除
     */
    public boolean isDeleted() {
        return status != null && status == TaskStatusEnum.DELETED.getCode();
    }

    public boolean isFinish() {
        return TaskStatusEnum.FINISH.isFinish(taskStatus);
    }

    public boolean isUIFinish() {
        return TaskStatusEnum.FINISH.isFinish(uiTaskStatus);
    }

    /**
     * 是否来源于第三方
     */
    public boolean isThirdParty() {
        return thirdProcess != null && thirdProcess;
    }

    private void setExtend(List<TaskExtend> extend) {
        this.extendObj = extend;
    }

    /**
     * 由【未完成/完成】状态切换成【完成/未完成】状态。其余状态时不处理
     */
    public void toggleFinishStatus() {
        if (taskStatus == TaskStatusEnum.FINISH.getCode()) {
            taskStatus = TaskStatusEnum.UN_FINISH.getCode();
        } else if (taskStatus == TaskStatusEnum.UN_FINISH.getCode()) {
            taskStatus = TaskStatusEnum.FINISH.getCode();
        }
    }

    public JoyWork selfClone() throws CloneNotSupportedException {
        JoyWork joyWork = new JoyWork();
        joyWork.bizCode = bizCode;
        joyWork.thirdProcess = thirdProcess;
        joyWork.mobileContent = mobileContent;
        joyWork.sourceName = sourceName;
        joyWork.title = title;
        joyWork.enablePermission = enablePermission;
        if (permission == null || permission.isEmpty()) {
            joyWork.permission = permission;
        } else {
            joyWork.permission = new ArrayList<>(permission);
        }
        joyWork.taskStatus = taskStatus;
        joyWork.status = status;
        joyWork.owner = owner;
        joyWork.owners = owners;
        joyWork.assigner = assigner;
        joyWork.priorityType = priorityType;
        joyWork.endTime = endTime;
        joyWork.planTime = planTime;
        joyWork.gmtCreate = gmtCreate;
        joyWork.completeTime = completeTime;
        joyWork.taskId = taskId;
        joyWork.projectId = projectId;

        if (childWorks == null || childWorks.isEmpty()) {
            joyWork.childWorks = childWorks;
        } else {
            joyWork.childWorks = new ArrayList<>(childWorks.size());
            for (JoyWork c : childWorks) {
                joyWork.childWorks.add(c.selfClone());
            }
        }
        joyWork.isFinishing = isFinishing;
        joyWork.readStatus = readStatus;
        joyWork.remindTime = remindTime;
        joyWork.extend = extend;
        if (extendObj == null || extendObj.isEmpty()) {
            joyWork.extendObj = extendObj;
        } else {
            joyWork.extendObj = new ArrayList<>(extendObj);
        }
        if (projects == null || projects.isEmpty()) {
            joyWork.projects = projects;
        } else {
            joyWork.projects = new ArrayList<>(projects);
        }
        joyWork.subTripartiteName = subTripartiteName;
        joyWork.subMobileContent = subMobileContent;
        if (latestComment != null) {
            joyWork.latestComment = (JoyWorkComment) latestComment.clone();
        }

        return joyWork;
    }


    // 下面内容与分组展示相关

    private transient JoyWorkTitle joyWorkTitle;

    @Override
    public JoyWorkTitle getGroupParent() {
        return joyWorkTitle;
    }

    @Override
    public void setGroupParent(JoyWorkTitle joyWorkTitle) {
        this.joyWorkTitle = joyWorkTitle;
    }

    @Override
    public Grade getGrade() {
        return Grade.ITEM;
    }

    private transient ExpandableGroup<JoyWorkTitle, JoyWork> expandableGroup;

    @Override
    public void setExpandableGroup(ExpandableGroup<JoyWorkTitle, JoyWork> expandableGroup) {
        this.expandableGroup = expandableGroup;
    }

    @Override
    public ExpandableGroup<JoyWorkTitle, JoyWork> getExpandableGroup() {
        return expandableGroup;
    }

    @Override
    public String toString() {
        return "JoyWork{" +
                ", bizCode='" + bizCode + '\'' +
                ", thirdProcess=" + thirdProcess +
                ", mobileContent='" + mobileContent + '\'' +
                ", sourceName='" + sourceName + '\'' +
                ", title='" + title + '\'' +
                ", enablePermission=" + enablePermission +
                ", permission=" + permission +
                ", taskStatus=" + taskStatus +
                ", status=" + status +
                ", owner=" + owner +
                ", assigner=" + assigner +
                ", priorityType=" + priorityType +
                ", endTime=" + endTime +
                ", planTime=" + planTime +
                ", gmtCreate=" + gmtCreate +
                ", completeTime=" + completeTime +
                ", taskId='" + taskId + '\'' +
                ", projectId='" + projectId + '\'' +
                ", childWorks=" + childWorks +
                ", isFinishing=" + isFinishing +
                ", readStatus=" + readStatus +
                ", remindTime=" + remindTime +
                ", extend='" + extend + '\'' +
                ", extendObj=" + extendObj +
                ", projects=" + projects +
                ", joyWorkTitle=" + joyWorkTitle +
                ", expandableGroup=" + expandableGroup +
                '}';
    }

    @NonNull
    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public static WorkInfo toWorkInfo(JoyWork work) {
        WorkInfo workInfo = new WorkInfo(work.taskId);
        workInfo.setUiTaskStatus(work.uiTaskStatus);
        workInfo.setTitle(work.title);
        workInfo.setThirdParty(work.isThirdParty());
        workInfo.setFinishAction(work.finishAction);
        workInfo.setMobileContent(work.mobileContent);
        workInfo.setSourceName(work.sourceName);
        workInfo.setStartTime(work.startTime);
        workInfo.setEndTime(work.endTime);
        ArrayList<ExecutorInfo> executorInfos = new ArrayList<>();
        if (work.owners != null) {
            for (int i = 0; i < work.owners.size(); i++) {
                JoyWorkUser user = work.owners.get(i);
                ExecutorInfo executorInfo = new ExecutorInfo();
                executorInfo.setChief(user.chief == 1);
                executorInfo.setAvatar(user.headImg);
                executorInfo.setErp(user.emplAccount);
                executorInfos.add(executorInfo);
            }
        }
        workInfo.setExecutors(executorInfos);
        return workInfo;
    }

    public static JoyWork fromWorkInfo(WorkInfo workInfo) {
        JoyWork work = new JoyWork();
        work.taskId = workInfo.getTaskId();
        work.uiTaskStatus = workInfo.getUiTaskStatus();
        work.title = workInfo.getTitle();
        work.thirdProcess = workInfo.isThirdParty();
        work.finishAction = workInfo.getFinishAction();
        work.mobileContent = workInfo.getMobileContent();
        work.sourceName = workInfo.getSourceName();
        work.startTime = workInfo.getStartTime();
        work.endTime = workInfo.getEndTime();
        ArrayList<JoyWorkUser> owners = new ArrayList<>();
        List<ExecutorInfo> executorInfos = workInfo.getExecutors();
        if (executorInfos != null) {
            for (int i = 0; i < executorInfos.size(); i++) {
                ExecutorInfo executorInfo = executorInfos.get(i);
                JoyWorkUser joyWorkUser = new JoyWorkUser();
                joyWorkUser.chief = executorInfo.isChief() ? 1 : 0;
                joyWorkUser.headImg = executorInfo.getAvatar();
                joyWorkUser.emplAccount = executorInfo.getErp();
                owners.add(joyWorkUser);
            }
        }
        work.owners = owners;
        return work;
    }
}
