package com.jd.oa.joywork.detail.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import java.util.*

class UpdateRegionVM : ViewModel() {
    companion object {
        // 修改标题
        const val type_title = 1 shl 0

        // 修改背景/描述
        const val type_desc = 1 shl 1

        // 修改截止时间
        const val type_deadline = 1 shl 2

        // 修改优先级
        const val type_priority = 1 shl 3

        // 修改重复
        const val type_dup = 1 shl 4

        // 开始时间
        const val type_start_time = 1 shl 5

        // 提醒时间
        const val type_remind = 1 shl 6

        // 执行人，发通知时不包含
        const val type_assignees = 1 shl 7

        // 完成状态，发通知时不包含
        const val type_ui_status = 1 shl 8

        // 删除状态 0，已删除  1，未删除
        const val type_status = 1 shl 9

        //指定的给日历中的待办添加的通知类型，修改后要通知
        val designatedValues = mutableListOf(
            type_title,
            type_deadline,
            type_start_time,
            type_assignees,
            type_ui_status,
            type_status,
        )
    }

    private val value = mutableListOf<Int>()

    fun getUpdateType(): List<Int> {
        return value
    }

    private val _valueLiveData: MutableLiveData<Boolean> = MutableLiveData()
    val valueLiveData: LiveData<Boolean> = _valueLiveData

    //用于只通知外部，数据有调整，本页面顶部不展示
    val outerTipsValue = mutableListOf<Int>()

    fun getOuterTipType(): List<Int> {
        return outerTipsValue
    }

    private val _outerTipLiveData: MutableLiveData<Boolean> = MutableLiveData()
    val outerTipLiveData: LiveData<Boolean> = _outerTipLiveData


    fun updateTitle() {
        if (value.contains(type_title)) {
            return
        }
        value.add(type_title)
        _valueLiveData.value = true
        tryNotifyOuter(type_title)
    }

    fun updateRemind() {
        if (value.contains(type_remind)) {
            return
        }
        value.add(type_remind)
        _valueLiveData.value = true
        tryNotifyOuter(type_remind)
    }

    fun updateDesc() {
        if (value.contains(type_desc)) {
            return
        }
        value.add(type_desc)
        _valueLiveData.value = true
        tryNotifyOuter(type_desc)
    }

    fun updateDeadline() {
        if (value.contains(type_deadline)) {
            return
        }
        value.add(type_deadline)
        _valueLiveData.value = true
        tryNotifyOuter(type_deadline)
    }

    fun updatePriority() {
        if (value.contains(type_priority)) {
            return
        }
        value.add(type_priority)
        _valueLiveData.value = true
        tryNotifyOuter(type_priority)
    }

    fun updateDup() {
        if (value.contains(type_dup)) {
            return
        }
        value.add(type_dup)
        _valueLiveData.value = true
        tryNotifyOuter(type_dup)
    }

    fun updateStartTime() {
        if (value.contains(type_start_time)) {
            return
        }
        value.add(type_start_time)
        _valueLiveData.value = true
        tryNotifyOuter(type_start_time)
    }

    fun updateAssignees() {
        tryNotifyOuter(type_assignees)
    }

    fun updateUiStatus() {
        tryNotifyOuter(type_ui_status)
    }

    fun updateStatus() {
        tryNotifyOuter(type_status)
    }

    private fun tryNotifyOuter(type: Int){
        if(!designatedValues.contains(type)){
            return
        }
        if (outerTipsValue.contains(type)) {
            return
        }
        outerTipsValue.add(type)
        _outerTipLiveData.value = true
    }

    fun clear() {
        value.clear()
        outerTipsValue.clear()
        _valueLiveData.value = true
        _outerTipLiveData.value = true
    }

    fun init() {
        _valueLiveData.value = true
        _outerTipLiveData.value = true
    }


    fun notifyUpdate(
        oldStart: Long?,
        oldEnd: Long?,
        oldRemind: String?,
        oldDup: Int?,
        detail: JoyWorkDetail
    ) {
        if (!Objects.equals(oldStart, detail.startTime)) {
            updateStartTime()
        }
        if (!Objects.equals(oldEnd, detail.endTime)) {
            updateDeadline()
        }
        if (!Objects.equals(oldRemind, detail.remindStr)) {
            updateRemind()
        }
        if (!Objects.equals(oldDup, detail.cycle)) {
            updateDup()
        }
    }
}