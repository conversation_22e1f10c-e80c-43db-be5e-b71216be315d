package com.jd.oa.joywork.team.kpi;

import android.os.Parcel;
import android.os.Parcelable;

public class JoyWorkSpace implements Parcelable {
    public static final String PRIVATE_TEAM_ID = "root";
    public static final String PRIVATE_DIR_ID = "root";
    public String name;
    public String teamId;
    public String folderId;

    public JoyWorkSpace() {

    }

    protected JoyWorkSpace(Parcel in) {
        name = in.readString();
        teamId = in.readString();
        folderId = in.readString();
    }

    public static final Creator<JoyWorkSpace> CREATOR = new Creator<JoyWorkSpace>() {
        @Override
        public JoyWorkSpace createFromParcel(Parcel in) {
            return new JoyWorkSpace(in);
        }

        @Override
        public JoyWorkSpace[] newArray(int size) {
            return new JoyWorkSpace[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(name);
        dest.writeString(teamId);
        dest.writeString(folderId);
    }
}
