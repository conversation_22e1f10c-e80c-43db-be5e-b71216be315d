package com.jd.oa.joywork.self.base

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProviders
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.joywork.repo.CacheStrategy
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.LoadingViewStrategy
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkHandler
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.ui.TaskDetailActivity
import com.jd.oa.joywork.self.SelfBaseItf
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.ui.JoyWorkViewModel
import com.jd.oa.joywork.view.swipe.SwipeMenuParent
import com.jd.oa.storage.entity.KvEntity
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.ThemeUtils
import com.jd.oa.utils.gone
import java.util.*

/**
 * 个人待办列表页父类
 */
abstract class SelfBaseFragment : BaseFragment(), SelfBaseItf {
    protected lateinit var filterContainer: LinearLayout
    protected lateinit var rv: RecyclerView
    protected lateinit var mSwipeRefreshLayout: SwipeRefreshLayout
    protected val sp by lazy {
        JoyWorkPreference(requireActivity())
    }
    private lateinit var mViewModel: JoyWorkViewModel // 各 frg 通过 vm 通信

    // 新待办创建结束后回调
    private val createFinishRunnable =
        Runnable { refreshData(LoadingViewStrategy.HIDE, CacheStrategy.ALL, NetType.REFRESH) }

    private val finishRefreshRunnable = Runnable {
        onRefreshData(LoadingViewStrategy.HIDE, CacheStrategy.WRITE, NetType.REFRESH)
    }
    private val mHandler = Handler()

    private var needRefreshWhenSelect = false

    private var mSwipeParent: SwipeMenuParent? = null

    private val finishReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (Objects.equals(intent?.action, ProjectConstant.UNFINISH_ACTION)) {
                // 取消完成，刷新数据；
                mViewModel.refreshNum()
            }
            if (isShow) {
                // 切换列表时会刷新，所以加个判断
                mHandler.postDelayed(finishRefreshRunnable, JoyWorkConstant.REFRESH_INTERVAL)
            } else {
                needRefreshWhenSelect = true
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        mViewModel = ViewModelProviders.of(requireActivity()).get(JoyWorkViewModel::class.java)

        JoyWorkHandler.getInstance().addCreateObserver(createFinishRunnable)
        val view: View = inflater.inflate(R.layout.jdme_joywork_list, container, false)
        tips(view.findViewById(R.id.tipsView))
        mSwipeParent = view.findViewById(R.id.swipe_menu_parent)
        mSwipeRefreshLayout = view.findViewById(R.id.refresh)
        mSwipeRefreshLayout.setColorSchemeResources(
            ThemeUtils.getAttrsIdValueFromTheme(
                activity,
                R.attr.me_theme_major_color,
                R.color.skin_color_default
            )
        )
        mSwipeRefreshLayout.setOnRefreshListener {
            // 下拉刷新，只写不读
            refreshData(LoadingViewStrategy.SHOW, CacheStrategy.WRITE, NetType.REFRESH)
        }

        finishAction().forEach {
            LocalBroadcastManager.getInstance(requireContext()).registerReceiver(
                finishReceiver,
                IntentFilter(it)
            )
        }

        rv = view.findViewById<RecyclerView>(R.id.rv).apply {
            setPadding(
                paddingLeft,
                if (rvPaddingVertical() < 0) paddingTop else rvPaddingVertical(),
                paddingRight,
                if (rvPaddingVertical() < 0) paddingBottom else rvPaddingVertical(),
            )
            layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        }
//        ItemTouchHelper(SelfDragTouchCallback(rv)).attachToRecyclerView(rv)
        // 第一次请求，即写即读
        val loadingViewStrategy =
            if (cacheStrategy().canRead() && JoyWorkRepo.hasCache(
                    sp,
                    getCacheKey()!!
                )
            ) LoadingViewStrategy.HIDE else LoadingViewStrategy.SHOW
        refreshData(loadingViewStrategy, cacheStrategy(), NetType.INIT)
        filterContainer = view.findViewById(R.id.filterContainer)
        return view
    }

    override fun onResume() {
        super.onResume()
        getPagePVName()?.apply {
            JDMAUtils.onEventPagePV(
                requireActivity(),
                this,
                this
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        JoyWorkHandler.getInstance().removeCreateObserver(createFinishRunnable)
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(finishReceiver)
        mHandler.removeMessages(0)
    }

    /**
     * 请求数据，同时自己填充 ui
     */
    protected fun refreshData(
        loadingViewStrategy: LoadingViewStrategy,
        strategy: CacheStrategy,
        netType: NetType
    ) {
        if (netType == NetType.REFRESH) {
            mViewModel.refreshNum()
        }
        onRefreshData(loadingViewStrategy, strategy, netType)
    }

    abstract fun onRefreshData(
        loadingViewStrategy: LoadingViewStrategy,
        strategy: CacheStrategy,
        netType: NetType
    )

    override fun onShowAtTabbar() {
        if (!::mSwipeRefreshLayout.isInitialized) {
            return
        }
        val loadingViewStrategy =
            if (cacheStrategy().canRead() && JoyWorkRepo.hasCache(
                    sp,
                    getCacheKey()!!
                )
            ) LoadingViewStrategy.HIDE else LoadingViewStrategy.SHOW
        refreshData(loadingViewStrategy, CacheStrategy.WRITE, NetType.REFRESH)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && data != null && data.hasExtra(TaskDetailActivity.KEY_BIZ_OBJ)) {
            val parcel: DetailReturnParcel =
                data.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as DetailReturnParcel
            // 没有更新
            if (!parcel.update) {
                return
            }
            when (data.getStringExtra(TaskDetailActivity.KEY_BIZ_FROM)) {
                JoyWorkConstant.BIZ_DETAIL_FROM_FILTER -> {
                    refreshFromDetail(true)
                }
                JoyWorkConstant.BIZ_DETAIL_FROM_LIST -> {
                    refreshFromDetail(false)
                }
            }
        }
    }

    /**
     * [filter] 是否是筛选列表页进入的详情页
     */
    private fun refreshFromDetail(filter: Boolean) {
        onRefreshFromDetail()
    }

    private fun onRefreshFromDetail() {
        refreshData(LoadingViewStrategy.SHOW, CacheStrategy.ALL, NetType.REFRESH)
    }

    /**
     * 当使用缓存时，对应的缓存 key
     */
    open fun getCacheKey(): KvEntity<String>? {
        return null
    }

    /**
     * 是否使用缓存。
     * @return [CacheStrategy.NONE] 时不需要重写 [getCacheKey]，否则必须重写 [getCacheKey] 且不能返回 null
     */
    open fun cacheStrategy(): CacheStrategy {
        return CacheStrategy.NONE
    }

    /**
     * 返回埋点统计时，界面 pv
     */
    open fun getPagePVName(): String? {
        return null
    }

    var isShow = false

    /**
     * 当从 viewpager 中被切走时
     */
    override fun onPageUnselected() {
        isShow = false
    }

    /**
     * 当从 viewpager 中显示时
     */
    override fun onPageSelected() {
        isShow = true
        if (needRefreshWhenSelect) {
            finishRefreshRunnable.run()
            needRefreshWhenSelect = false
        }
    }

    /**
     * 返回列表页上下 padding。
     * @return 负值：保持原样不动；非负值：设置成返回值
     */
    open fun rvPaddingVertical(): Int {
        return -1
    }

    protected val isSwipeRefreshLayoutInited: Boolean
        get() {
            return ::mSwipeRefreshLayout.isInitialized
        }

    /**
     * 返回界面唯一标识，用于区分是 我负责的、我协作的、我指派的
     */
    abstract fun getPageId(): SelfPageEnum

    protected open fun tips(viewGroup: ViewGroup) {
        viewGroup.gone()
    }

    /**
     * 设置待办完成/未完成时接收的广播的 action
     */
    protected open fun finishAction(): List<String> {
        return mutableListOf(ProjectConstant.FINISH_ACTION, ProjectConstant.UNFINISH_ACTION)
    }
}

enum class SelfPageEnum {
    MY_OWNER,
    MY_ASSIGN,
    MY_COOPERATE,
    MY_FINISH,

    /**风险问题列表*/
    MY_ISSUE
}

enum class NetType {
    REFRESH, // 刷新
    PULL_DOWN_LOAD_MORE, // 下拉加载更多
    INIT, // 初始化
    LOADMORE // 上拉加载
}