package com.jd.oa.joywork.view.swipe;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 与 {@link SwipeMenuLayout} 配套使用，当 {@link SwipeMenuLayout} 处于打开状态时，该 View 会拦截一切 touch 事件
 * 首先对 {@link SwipeMenuLayout} 执行关闭操作。其余情况下跟系统默认一样
 */
public class SwipeMenuParent extends FrameLayout {
    private SwipeMenuLayout mOpenChild;
    private boolean inChild = false;

    public SwipeMenuParent(@NonNull Context context) {
        super(context);
    }

    public SwipeMenuParent(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SwipeMenuParent(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (mOpenChild != null) {
            if (ev.getAction() == MotionEvent.ACTION_DOWN) {
                final int actionIndex = ev.getActionIndex();
                final float x = ev.getX(actionIndex);
                final float y = ev.getY(actionIndex);
                inChild = mOpenChild.willHandle(ev);
            }
            // 如果点击在目标 View 范围内，事件不拦截，但同时关闭菜单
            if (inChild) {
                reset();
                return super.dispatchTouchEvent(ev);
            }
            SwipeMenuLayout tmp = mOpenChild;

            if (ev.getAction() == MotionEvent.ACTION_DOWN) {
                mOpenChild.smoothCloseMenu();
            }
            mOpenChild = tmp;
            if (ev.getAction() == MotionEvent.ACTION_CANCEL || ev.getAction() == MotionEvent.ACTION_UP) {
                if (mOpenChild != null) {
                    mOpenChild.smoothCloseMenu();
                }
                reset();
            }
            return true;
        }
        return super.dispatchTouchEvent(ev);
    }

    public void rememberChild(SwipeMenuLayout layout) {
        mOpenChild = layout;
    }

    public void reset() {
        mOpenChild = null;
        inChild = false;
    }

    /**
     * @return If there is a menu is opened, will return true and close it; otherwise，return false and do nothing
     */
    public boolean shouldCloseMenu() {
        if (mOpenChild == null) {
            return false;
        }
        mOpenChild.smoothCloseMenu();
        return true;
    }
}
