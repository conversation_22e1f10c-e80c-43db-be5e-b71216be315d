package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import com.jd.oa.utils.*
import com.jd.oa.joywork.R

class CustomAlertDialog(context: Context, private val config: CustomConfig) :
    AppCompatDialog(context, R.style.JoyWorkAlertDialogStyle) {

    private lateinit var mContentView: View
    private lateinit var mMsgView: TextView
    private lateinit var mOkTextView: TextView

    init {
        window?.apply {
            decorView.setBackgroundColor(Color.TRANSPARENT)
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = CommonUtils.getScreentWidth(context) * 62 / 75
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            attributes = layoutParams
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mContentView = context.inflater.inflate(R.layout.jdme_dialog_joywork_custom_alert, null)
        setContentView(mContentView)
        window?.decorView?.setBackgroundColor(Color.TRANSPARENT)
        mContentView.findViewById<ViewGroup>(R.id.root)?.apply {
            background =
                DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(config.bgCorner))
        }
        mMsgView = mContentView.findViewById(R.id.message)
        mMsgView.text = getStr(config.msgRes, config.msgStr)

        mOkTextView = mContentView.findViewById(R.id.ok)
        mOkTextView.text = getStr(config.okRes, config.okStr)

        getOkClick()?.apply {
            mOkTextView.setOnClickListener {
                this.invoke(this@CustomAlertDialog)
            }
        }
        val cancelView = mContentView.findViewById<View>(R.id.cancel)
        val divider = mContentView.findViewById<View>(R.id.mDivider)

        if (config.showCancel) {
            cancelView.visible()
            divider.visible()
            cancelView.setOnClickListener {
                cancel()
            }
        } else {
            cancelView.gone()
            divider.gone()
        }
    }

    private fun getStr(res: Int, str: String?): String {
        if (res <= 0 && str == null) {
            throw IllegalArgumentException()
        }
        return str ?: context.string(res)
    }

    private fun getOkClick(): ((CustomAlertDialog) -> Unit)? {
        return if (config.autoDismiss) { dialog ->
            dismiss()
            config.okShitClick?.invoke(dialog)
        } else config.okShitClick
    }
}

class CustomConfig : ShitAlertDialogConfig<CustomAlertDialog>() {
}