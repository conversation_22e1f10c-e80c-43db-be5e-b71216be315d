package com.jd.oa.joywork.self.drag

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.TimeTitleJoyWork
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.self.base.adapter.SelfBaseFragmentAdapter
import com.jd.oa.joywork.self.base.adapter.vh.ChildVH
import com.jd.oa.joywork.self.base.adapter.vh.GroupVH
import com.jd.oa.joywork.self.base.adapter.vh.LoadMoreVH
import com.jd.oa.joywork.self.base.adapter.vh.TimeVH

/**
 * 个人待办  我负责的 拖动
 */
class SelfDragTouchCallback(private val rv: RecyclerView) : ItemTouchHelper.Callback() {
    private var oldExpand = false
    private fun adapter(): SelfBaseFragmentAdapter {
        return rv.adapter as SelfBaseFragmentAdapter
    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        if (viewHolder !is ChildVH) {
            return makeMovementFlags(0, 0)
        }
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        return makeMovementFlags(dragFlags, 0)
    }

    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        MyOwnerAdapterSwapHelper.swap(
            viewHolder,
            target,
            viewHolder.adapterPosition < target.adapterPosition,
            adapter()
        )
        return true
    }

    // 返回值表示 current 与 target 是否可交换
    // 2021年08月11日 留：
    //  目前 VH 一共有四种类型：TimeVH 表示计划分组中时间间隔；ChildVH 表示普通的 Item；GroupVH 表示每一个他组的标题；LoadMoreVH 表示加载更多
    override fun canDropOver(recyclerView: RecyclerView, current: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        if (target !is Exchangeable) {
            return false
        }
        if (target is GroupVH && !target.getJoyWorkTitle().expandableGroup.expand) {
            oldExpand = true
            target.expandIfNecessary()
            return false
        }
        if (target.exchangeable) { // 普通的 Item，对应 ChildVH
            return true
        }
        // 不能超过边界
        val items = adapter().processor.getData()
        if (target.adapterPosition == 0) {
            return false
        }
        // 最后一个，可以拖动
        if (target.adapterPosition >= items.size - 1) {
            return true
        }
        // 到这里 target 只可能是 GroupVH，TimeVH 与 LoadMoreVH
        // 时间间隔分组。其上可能是 TimeVH, ChildVH,GroupVH；其下是 TimeVH，ChildVH,LoadMoreVH,GroupVH
        if (target is TimeVH) {
            return if (current.adapterPosition > target.adapterPosition) {
                // 上滑。只有 GroupVH + TimeVH 情况时才不可交换
                val item = items[target.adapterPosition - 1]
                item !is JoyWorkTitle
            } else {
                // 下滑，都可
                true
            }
        }
        // 加载更多。其上可能是 GroupVH,ChildVH,TimeVH; 其下只可能是 GroupVH
        if (target is LoadMoreVH) {
            // 只有上滑时才可与 LoadMoreVH 交换
            return current.adapterPosition > target.adapterPosition
        }

        // 分组标题。其上可能是 GroupVH,ChildVH,TimeVH,LoadMoreVH; 其下可能是 GroupVH,ChildVH,TimeVH,LoadMoreVH;
        if (target is GroupVH) {
            return if (current.adapterPosition > target.adapterPosition) {
                // 上滑。只有 LoadMoreVH + GroupVH 情况时才不可交换
//                val item = items[target.adapterPosition - 1]
//                item !is LoadMoreJoyWork
                true
            } else {
                // 下滑。只有 GroupVH+TimeVH 时不行
                val item = items[target.adapterPosition + 1]
                item !is TimeTitleJoyWork
            }
        }
        return false
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        // 只有 item 可拖动
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && viewHolder != null && viewHolder::class.java == ChildVH::class.java) {
            (viewHolder as? ChildVH)?.onBeginDrag()
            oldExpand = false
            return;
        }
        super.onSelectedChanged(viewHolder, actionState)
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        (viewHolder as? ChildVH)?.onEndDrag()
        oldExpand = true
        super.clearView(recyclerView, viewHolder)
    }

}