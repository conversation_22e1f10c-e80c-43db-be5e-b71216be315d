package com.jd.oa.joywork.shortcut

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.Window
import android.view.WindowManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkHandler
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.common.ItfShortcutDialogCommon
import com.jd.oa.joywork.common.ShortcutDialogCommon
import com.jd.oa.joywork.create.JoyWorkCreateActivity
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.data.entity.Documents
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.getBatchInfo
import com.jd.oa.joywork.hasNull
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.dialog.DialogSupporter
import com.jd.oa.joywork.todayTime
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork2.input.CopyOpMonitor
import com.jd.oa.model.service.WaterService
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.ColorEx
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.InputMethodUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.TextWatcherAdapter
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.color
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.setCursorEnd
import com.jd.oa.utils.string
import com.jd.oa.utils.visible
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

enum class ChatType(val value: String) {
    SINGLE("0"),
    FILE_HELPER("2"),
    GROUP("1"),
    NO_CHAT("-1") // 新建任务，非 IM 来源
}

/**
 * 快捷新建时临时数据，不参与草稿
 */
class ShortcutDialogTmpData {
    var bizCode: String? = null
    var sessionId: String? = null
    var to: String? = null
    var toApp: String? = null
    var parentId: String? = null
    var fromDD: Boolean = false // 是否是从咚咚创建（包含从聊天界面以及群待办）
    var chatType: String = ChatType.NO_CHAT.value // 是否是单聊

    var tripartiteName: String? = null // 三方文档或者会话的名称

    /**
     * 只有 [fromDD] 为 true 时才使用该字段
     * 1 表示长按消息
     * 2 表示加号
     * 3 表示群待办
     */
    var fromType: ShortcutFromType = ShortcutFromType.NULL

    /**
     * 额外指定的执行人
     */
    var owners: List<JoyWorkUser>? = null

    /**
     * 哪个列表创建的
     */
    var taskListTypeEnum: TaskListTypeEnum? = null

    /**
     * 关联哪个绩效目标
     */
    var kpiTarget: List<KpiTarget>? = null

    /**
     * the kr to which the task wants to attach
     */
    var kr: List<KR>? = null

    /**
     * 【targetId】 and 【kr】 are different from 【kpiTarget】and 【kr】
     *  The former needs to request the server to get detail info,  the latter does not
     */
    var targetId: String? = null
    var krId: String? = null

    var extra: String? = null

    // 可用 extra 表示两个以下两个字段，也可单独设置
    var content: String? = null
    var mobileContent: String? = null

    var meChattingLabel: MEChattingLabel? = null

    fun mergeTargetAndKr(): List<Any> {
        val ans = mutableListOf<Any>()
        if (kpiTarget.isLegalList()) {
            ans.addAll(kpiTarget!!)
        }
        if (kr.isLegalList()) {
            ans.addAll(kr!!)
        }
        if (krId.isLegalString()) {
            val kr = KR()
            kr.krId = krId
            ans.add(kr)
        }
        if (targetId.isLegalString()) {
            val kr = KpiTarget()
            kr.goalId = targetId
            ans.add(kr)
        }
        return ans
    }

    /**
     * 没有执行人时是否隐藏
     */
    var hideGroups: Boolean = false

    var isProject: Boolean = true
    var selectGroupId: String? = null

    var title: String? = null

    var desc: String? = null
}

enum class ShortcutFromType {
    // 默认状态，表示没有值
    NULL,

    // 长按消息
    MSG,

    // 点击加号
    EXTEND,

    // 群待办
    GROUP_TASK
}

class JoyWorkShortcutDialog(
    private val outContext: Activity,
    draft: JoyWorkShortcutDraft,
    private var tmpData: ShortcutDialogTmpData? = null,
    private val showGroup: Boolean = false, //是否显示分组
    private val projectId: String? = null,
    groups: List<Group>? = emptyList(),
    private val viewId: String? = null,
    private val viewType: String? = null,
) : AppCompatDialog(outContext, R.style.JoyWorkDialogStyle), ItfShortcutDialogCommon {

    private val innerGroups = if (groups?.isLegalList() == true) ArrayList<Group>(groups) else ArrayList()

    private var selectGroupId: String? = tmpData?.selectGroupId ?: innerGroups.firstOrNull()?.groupId

    private val safeTitle: String
        get() = if (selectGroupId.isLegalString()) { // 有分组
            val first = innerGroups.first { it.groupId == selectGroupId }
            first.safeTitle(outContext)
        } else {
            outContext.string(R.string.joywork_select_group_list_title)
        }

    private val mValue: Value = Value()

    private val manager = ShortcutManager(this)

    private val support = DialogSupporter(this, outContext)

    private val transparentGrayColor = Color.parseColor("#F8F8F9")

    private val mContentView: View

    //    private val mAssignContainer: View
    private val mOwnerContainer: View

    private val mCreateView: TextView

    private val mCreateContainer: FrameLayout
    private val mBatchContainer: LinearLayout

    var showSnackBar = true
    private var batchIcon: View
    private var batchText: View


    private val scope = MainScope()

    private val copyOpHelper = CopyOpMonitor(scope)
    private val recyclerView: RecyclerView

    init {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        mContentView = context.inflater.inflate(R.layout.joywork_shortcut_dialog, null)
        setContentView(mContentView)

        ShortcutDialogCommon.initValue(this, draft)

        ShortcutDialogCommon.setupSendToChat(this)
        ShortcutDialogCommon.handleTimeView(this)
        ShortcutDialogCommon.setDupClickListener(support, this)
        ShortcutDialogCommon.setAlertClearClick(support, this)

        mTodayContainer.background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
        mTodayContainer.setOnClickListener {
            // 截止时间设置为今天
            mValue.endTime = todayTime()
            JoyWorkShortcutDraft.updateDraftEndTime(mValue.endTime)
            ShortcutDialogCommon.afterTimeSelected(this@JoyWorkShortcutDialog)
        }
        mTomorrowContainer.background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
        mTomorrowContainer.setOnClickListener {
            // 截止时间设置为明天
            mValue.endTime =
                DateUtils.getSpecialHour(System.currentTimeMillis() + 24 * 60 * 60 * 1000, 16)
            JoyWorkShortcutDraft.updateDraftEndTime(mValue.endTime)
            ShortcutDialogCommon.afterTimeSelected(this@JoyWorkShortcutDialog)
        }

        if (context.applicationInfo.targetSdkVersion >= Build.VERSION_CODES.P) {
            InputMethodUtils.showSoftInputFromWindow(context, mTitleView)
        }


        mBatchContainer = mContentView.findViewById(R.id.batch_container)
        batchIcon = mContentView.findViewById(R.id.iconBatchSubJoywork)
        batchText = mContentView.findViewById(R.id.textBatchSubJoywork)

        mContentView.findViewById<View>(R.id.detail).setOnClickListener {
            goDetail()
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CREATE_TASK_MORE_VIEW
                )
            }
        }
        mCreateContainer = mContentView.findViewById(R.id.create_container)
        val colors = intArrayOf(Color.parseColor("#11FFFFFF"), Color.WHITE)
        mCreateContainer.background =
            GradientDrawable(GradientDrawable.Orientation.RIGHT_LEFT, colors)
        mCreateView = mContentView.findViewById(R.id.create)

        mOwnerContainer = mContentView.findViewById(R.id.owner_container)
        mOwnerContainer.setOnClickListener {
            JDMAUtils.onEventClick(JoyWorkConstant.SHORTCUT_OWNER, JoyWorkConstant.SHORTCUT_OWNER)
            manager.selectContact(
                context,
                mValue,
                sessionId = tmpData?.sessionId,
                chatType = tmpData?.chatType ?: ChatType.NO_CHAT.value
            ) { success ->
                ShortcutDialogCommon.openKeyboard(mTitleView, context)
                if (success) {
                    getInfo()
                    handleOwnerView()
                }
            }
        }

        mTimeContent.background =
            DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
        val timeIndicator = mContentView.findViewById<View>(R.id.time_container_indicator)
        ShortcutDialogCommon.setTimeIndicatorClickListener(timeIndicator, this)

        ShortcutDialogCommon.setTimeClearClick(this)

        handleAssignView()
        ShortcutDialogCommon.handleAssignContainer(this)
        mContentView.findViewById<View>(R.id.assign_container).setOnClickListener {
            JDMAUtils.onEventClick(JoyWorkConstant.SHORTCUT_ASSIGN, JoyWorkConstant.SHORTCUT_ASSIGN)
            manager.closeKeyboard(mTitleView, context)
            projectId?.run {
                support.listGroup(innerGroups, selectGroupId) {
//                    manager.openKeyboard(mTitleView, context)
                    if (it != null) {
                        selectGroupId = it.groupId
                        handleAssignView()
                        handleCreateView()
                    }
                }
            }
        }

        recyclerView = mContentView.findViewById(R.id.recycler)

        window?.apply {
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN or WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
            val layoutParams = attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.BOTTOM
            attributes = layoutParams
        }

        setCanceledOnTouchOutside(true)

        setOnDismissListener {
            manager.closeKeyboard(mTitleView, context)
        }
        getInfo()

        mContentView.getViewTreeObserver().addOnGlobalLayoutListener(
            object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    mContentView.getViewTreeObserver().removeOnGlobalLayoutListener(this)
                    val measuredHeight: Int = mContentView.height
                    AppJoint.service(WaterService::class.java)?.addWaterMark(
                        this@JoyWorkShortcutDialog,
                        true,
                        FrameLayout.LayoutParams.MATCH_PARENT,
                        measuredHeight
                    )
                }
            }
        )
    }

    private val docInitializer: DocInitializer = DocInitializer(recyclerView)


    private fun handleAssignView() {
        val assignIndicator = mContentView.findViewById<LinearLayout>(R.id.assign_indicator)
        val assignContent = mContentView.findViewById<LinearLayout>(R.id.assign_content)
        val assignTips = mContentView.findViewById<TextView>(R.id.assign_text)
        if (selectGroupId.isLegalString()) {
            assignIndicator.gone()
            assignContent.visible()
            mAssignContainer?.background =
                DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
            ShortcutDialogCommon.openKeyboard(mTitleView, mTitleView.context)
            // 选择了分组。ellipsize 无效，此处手动修改
            assignTips.text =
                if (safeTitle.length > 6) "${safeTitle.subSequence(0, 6)}..." else safeTitle
        } else {
            assignTips.text = safeTitle
            assignIndicator.visible()
            assignContent.gone()
        }

    }

    /**
     * 通过接口获取用户信息
     */
    private fun getInfo() {
        scope.launch {
            if (mValue.normalOwners.isLegalList()) {
                val ms = ArrayList<Members>()
                mValue.normalOwners.forEach {
                    if (listOf(it.headImg, it.realName).hasNull()) {
                        val m = Members()
                        m.fromJoyWorkUser(it)
                        ms.add(m)
                    }
                }
                if (!ms.isLegalList()) {
                    return@launch
                }
                val r = getBatchInfo(ms)
                r.ifSuccessSilence {
                    it?.apply {
                        val us = HashMap<String, Members>()
                        this.users.forEach {
                            us["${it.ddAppId},${it.userId}"] = it
                        }
                        // us 中存在的数据说明可以获取用户信息
                        // 没有的说明无法获取用户信息，过滤掉
                        val illegalUsers = ms.map {
                            "${it.ddAppId},${it.userId}"
                        }.filter {
                            !us.containsKey(it)
                        }
                        mValue.normalOwners.removeAll {
                            val key = "${it.ddAppId},${it.emplAccount}"
                            illegalUsers.contains(key)
                        }
                        mValue.normalOwners.forEach {
                            val key = "${it.ddAppId},${it.emplAccount}"
                            if (!it.headImg.isLegalString() && us.containsKey(key)) {
                                it.headImg = us[key]!!.headImg
                                it.teamId = us[key]!!.teamId
                                it.realName = us[key]!!.realName
                            }
                        }
                        handleOwnerView()
                    }
                }
            }
        }
    }

    private fun goDetail() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.JOYWORK_BATCH_CREATE,
            JoyWorkConstant.JOYWORK_BATCH_CREATE
        )
        JoyWorkCreateActivity.projectId = projectId
        JoyWorkCreateActivity.groups = innerGroups
        JoyWorkCreateActivity.selId = selectGroupId
        JoyWorkCreateActivity.successCallback = successCallback
        JoyWorkCreateActivity.tmpData = <EMAIL>
        JoyWorkCreateActivity.mSendDDChecked = mSendIconChecked
        JoyWorkCreateActivity.successRunnable = SuccessSnackBar(this.outContext)
        JoyWorkCreateActivity.viewId = viewId ?: ""
        JoyWorkCreateActivity.viewType = viewType ?: ""
        JoyWorkCreateActivity.shimo = mValue.shimo
        JoyWorkCreateActivity.showGroup = showGroup()
        ShortcutDialogCommon.inflateCreateActivity(this)
        if (isSubJoyWork()) {
            JoyWorkCreateActivity.parentId = tmpData?.parentId
            JoyWorkCreateActivity.isSubJoywork = true
        }
        JoyWorkCreateActivity.go(
            outContext,
            mTitleView.string(),
            mValue.startTime,
            mValue.endTime,
            false
        )
        <EMAIL>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setCanceledOnTouchOutside(true)
        if (isSubJoyWork()) {
            mContentView.findViewById<View>(R.id.detail).gone()
            mBatchContainer.visible()
            batchIcon.setOnClickListener {
                goDetail()
            }
            batchText.setOnClickListener {
                goDetail()
            }
        } else {
            val tv = mContentView.findViewById<View>(R.id.detail)
            tv.visible()
            mBatchContainer.gone()
        }
        // 创建按钮颜色
        mCreateView.setTextColor(ColorEx.addItem(ColorEx.Item().apply {
            enable = true
            color = context.color(R.color.joywork_red)
        }, ColorEx.Item().apply {
            enable = false
            color = context.color(R.color.joywork_red_transparent)
        }, ColorEx.Item().apply {
            color = context.color(R.color.joywork_red)
        }))
        mCreateView.setOnClickListener {
            it.isEnabled = false
            JoyWorkRepo.create({ detail, msg, result ->
                if (detail != null) {
                    if (showSnackBar) {
                        SuccessSnackBar(outContext).show(result)
                    }
                    successCallback.invoke(result, mValue, this@JoyWorkShortcutDialog)
                    JoyWorkShortcutDraft.removeDraft()
                    JoyWorkHandler.getInstance().onCreateFinish()
                } else {
                    ToastUtils.showInfoToast(msg)
                    it.isEnabled = true
                }
            }) {
                if (mValue.hasSelf() || tmpData?.isProject.isTrue()) {
                    groupId = selectGroupId
                    mValue.updateGroupUI(selectGroupId)
                    projectId = <EMAIL>
                }
                content = mValue.extra
                imMessage = mSendIconChecked
                parentId = <EMAIL>?.parentId
                bizCode = <EMAIL>?.bizCode
                inventory = <EMAIL>?.sessionId
                taskListType = <EMAIL>?.taskListTypeEnum
                tripartiteName = <EMAIL>?.tripartiteName
                goalsAndKrs = <EMAIL>?.mergeTargetAndKr()
                ShortcutDialogCommon.addCommonNetParams(this@JoyWorkShortcutDialog, this)
//                setTypeAndPlanTime(draft.type, draft.planTime)
            }
        }

        mTitleView.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                mValue.title = mTitleView.string().trim()
                JoyWorkShortcutDraft.updateDraftTitle(mValue.title)
                handleCreateView()
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                super.onTextChanged(s, start, before, count)
                copyOpHelper.onTextChanged(s, start, before, count) { page ->
                    page.runCatching {
                        val doc = Documents()
                        doc.apply {
                            pageType = page.pageType.toString()
                            documentId = page.bizId
                            permission = page.permissionType ?: 0
                            title = page.title
                            url = s?.toString()?.substring(start, start + count)
                        }
                        mValue.updateShimo(listOf(doc))
                        docInitializer.addDoc(this)
                    }
                }
            }
        })

        mValue.title?.trim()?.apply {
            mTitleView.setText(this)
            if (isNotEmpty()) {
                mTitleView.setCursorEnd()
            }
        } ?: handleCreateView()

        manager.initAt(mTitleView) { init ->
            manager.selectContact(context, 1) { b, bean ->
                if (b.isTrue() && bean.isLegalList() && !(outContext.isDestroyed || outContext.isFinishing)) {
                    mValue.addNormalOwners(bean)
                    ShortcutDialogCommon.handleAvatarView(this, context)
                    bean?.forEach {
                        init.appendAt(it)
                    }
                }
            }
        }

        handleOwnerView()
    }

    private fun handleOwnerView() {
        val ownerIcon = mOwnerContainer.findViewById<View>(R.id.owner_def_icon)
        if (mValue.normalOwners.isLegalList()) {
            mAvatarView.visible()
            ownerIcon.gone()
            mOwnerContainer.background =
                DrawableEx.roundSolidRect(transparentGrayColor, CommonUtils.dp2FloatPx(4))
            ShortcutDialogCommon.handleAvatarView(this, context)
        } else {
            mAvatarView.gone()
            ownerIcon.visible()
            mOwnerContainer.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    private fun handleCreateView() {
        mCreateView.isEnabled = mValue.title?.isNotEmpty() ?: false
    }

    override fun getShortcutManager(): ShortcutManager = manager

    override fun tmpData(): ShortcutDialogTmpData? = tmpData

    override fun showKeyBoardAfterChangePage(): Boolean {
        return true
    }

    override var mSendIconChecked: Boolean = false

    override val mTodayContainer: View
        get() = mContentView.findViewById(R.id.mTodayContainer)
    override val mTomorrowContainer: View
        get() = mContentView.findViewById(R.id.mTomorrowContainer)
    override val mTimeTips: TextView
        get() = mContentView.findViewById(R.id.time_text)
    override val mTimeContent: View
        get() = mContentView.findViewById(R.id.time_content)
    override val mTimeClear: IconFontView
        get() = mContentView.findViewById(R.id.clear_time)
    override val mAlarmTime: IconFontView?
        get() = null

    //        get() = mContentView.findViewById(R.id.alarm_time)
    override val mDupIcon: IconFontView?
        get() = null

    //        get() = mContentView.findViewById(R.id.dup_icon)  2022年05月09日 本次迭代重复标识不生效，返回 null
    override val mTimeIcon: TextView
        get() = mContentView.findViewById(R.id.time_icon)
    override val mHorScrollView: HorizontalScrollView
        get() = mContentView.findViewById(R.id.mHorScrollView)

    override val iconDuplicate: IconFontView
        get() = mContentView.findViewById(R.id.iconDuplicate)
    override val textDuplicate: TextView
        get() = mContentView.findViewById(R.id.textDuplicate)
    override val dupContainer: View
        get() = mContentView.findViewById(R.id.dup_container)
    override val mExecutorArrow: View?
        get() = null
    override val mAvatarView: JoyWorkAvatarView
        get() = mContentView.findViewById(R.id.mAvatarView)
    override val mSendDD: View
        get() = mContentView.findViewById(R.id.mSendDD)
    override val mSendIcon: ImageView
        get() = mContentView.findViewById(R.id.mSendIcon)
    override val mSendText: TextView
        get() = mContentView.findViewById(R.id.mSendText)
    override val mTitleView: EditText
        get() = mContentView.findViewById(R.id.title)

    override val mAlertContainer: ViewGroup
        get() = mContentView.findViewById(R.id.mAlertContainer)
    override val mAlertIcon: TextView
        get() = mContentView.findViewById(R.id.mAlertIcon)
    override val mAlertText: TextView
        get() = mContentView.findViewById(R.id.mAlertText)
    override val mAssignContainer: View?
        get() = mContentView.findViewById(R.id.assign_container)
    override val mAssignContainerDivider: View?
        get() = null

    var successCallback: (String?, Value, JoyWorkShortcutDialog?) -> Unit = { _, _, dialog ->
        dialog?.dismiss()
    }

    /**
     * 是否是新建子待办
     */
    private fun isSubJoyWork(): Boolean {
//        return tmpData?.parentId.isLegalString()
        return false
    }

    override fun dismiss() {
        super.dismiss()
        scope.cancel()
        JoyWorkShortcutDraft.removeDraft()
    }

    override fun getValue(): Value {
        return mValue
    }

    override fun showGroup(): Boolean = showGroup && !projectId.isNullOrEmpty()
}