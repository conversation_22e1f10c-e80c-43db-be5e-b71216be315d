package com.jd.oa.joywork.self

import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.R
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.JoyWorkMsgCenter
import com.jd.oa.joywork.ReverseMsg
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkReverseCallback
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.utils.PromptUtils
import com.jd.oa.utils.logDebug
import com.jd.oa.utils.string
import org.json.JSONObject
import java.lang.Exception

/**
 * 还原相关操作
 */
class JoyWorkReverseHelper {
    fun reverse(joyWork: JoyWork, fm: FragmentActivity) {
        JoyWorkRepo.revertTask(taskId = joyWork.taskId, callback = object : JoyWorkReverseCallback {
            override fun result(success: Boolean, result: String?, errorMsg: String) {
                try {
                    PromptUtils.removeLoadDialog(fm)
                } catch (e: Exception) {
                    e.logDebug()
                }
                try {
                    // {"errorCode":"0","content":{"taskIds":["315518638296006656"]},"errorMsg":"success"}
                    val obj = JSONObject(result ?: "")
                    if ("0" == obj.getString("errorCode")) {
                        val taskIds = obj.getJSONObject("content").getJSONArray("taskIds")
                        val ids = mutableListOf<String>()
                        for (i in 0 until taskIds.length()) {
                            ids.add(taskIds.getString(i))
                        }
                        ProjectConstant.sendBroadcast(fm, ProjectConstant.REVERSE_ACTION)
                        JoyWorkMsgCenter.notifyReverse(ReverseMsg(ids))
                    } else {
                        result(false, null, JoyWorkEx.filterErrorMsg(obj.getString("errorMsg")))
                    }
                } catch (e: Exception) {
                    e.logDebug()
                    result(false, null, JoyWorkEx.filterErrorMsg(""))
                }
            }

            override fun onStart() {
                PromptUtils.showLoadDialog(fm, fm.string(R.string.me_feedback_tab_processing), false)
            }
        })
    }
}