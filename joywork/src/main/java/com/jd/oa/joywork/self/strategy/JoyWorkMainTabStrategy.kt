package com.jd.oa.joywork.self.strategy

import android.app.Activity
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.openDeeplink
import com.jd.oa.joywork.search.JoyWorkSearchActivity
import com.jd.oa.theme.view.JoyWorkTheme
import com.jd.oa.theme.view.JoyWorkThemeView
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.StatusBarConfig
import com.jd.oa.utils.argb
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.inflater
import com.qmuiteam.qmui.util.QMUIStatusBarHelper

/**
 * [JoyWorkMainFragment]  的策略实现类：用于放置 tabbar 上
 */
object JoyWorkMainTabStrategy : JoyWorkMainStrategy {

    override fun handleTitle(parent: FrameLayout, activity: Activity) {
        parent.context?.apply {
            val view = inflater.inflate(R.layout.jdme_joywork_fragment_tab_title, parent, true)
            view.findViewById<View>(R.id.more_button).setOnClickListener {
                JoyWorkMainStrategy.DEEP_LINK.openDeeplink()
                clickEvent {
                    ClickEventParam(
                        eventId = JoyWorkConstant.MOBILE_EVENT_TASK_HOME_HELP_CENTER_BUTTON
                    )
                }

            }
            view.findViewById<View>(R.id.search).setOnClickListener {
                JoyWorkSearchActivity.start(activity)
            }
            val height = if (StatusBarConfig.enableImmersive()) {
                val titleContainer = view.findViewById<View>(R.id.mTitleContainer)
                titleContainer.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(this), 0, 0)
                QMUIStatusBarHelper.getStatusbarHeight(this) + resources.getDimensionPixelSize(R.dimen.joywork_list_tabbar_title_height)
            } else {
                resources.getDimensionPixelSize(R.dimen.joywork_list_tabbar_title_height)
            }
            view.layoutParams.height = height
        }
        onThemeDataChange(parent)
    }

    override fun onThemeDataChange(parent: View?) {
        val a = parent ?: return
        a.findViewById<JoyWorkThemeView>(R.id.mJoyWorkThemeBg)?.onThemeDataChange()
        val title = a.findViewById<TextView>(R.id.mTitle) ?: return
        val search = a.findViewById<TextView>(R.id.search) ?: return
        val more_button = a.findViewById<TextView>(R.id.more_button) ?: return
        val themeData = JoyWorkTheme.currentTheme
        if (themeData.isGlobal.isTrue() && themeData.isDarkTheme.isTrue()) {
            title.argb("#ffffff")
            search.argb("#ffffff")
            more_button.argb("#ffffff")
        } else {
            title.argb("#333333")
            search.argb("#333333")
            more_button.argb("#333333")
        }
    }

    override fun getAddViewBottomMargin() = R.dimen.joywork_add_view_bottom_margin

    //  不要问这些尺寸哪来的，是我通过 sketch 量 joyspace 得到的
    override fun getAddViewRightMargin() = R.dimen.joywork_add_view_right_margin
}