package com.jd.oa.joywork.snackbar

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.os.Handler
import android.os.Message
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.LinearLayout
import android.widget.TextView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.JDMAUtils

/**
 * 专为 joywork 使用的 snackbar
 *
 * @param from: 标识来源。要使用 JoyWorkConstant#BIZ_DETAIL_XXX 中的值
 */
class JoyWorkSnackBar(
    private val activity: Activity,
    private var id: String,
    private var projectId: String?,
    private var title: String?,
    private val from: String
) {
    private var duration = 3.5f // s
    private var contentView: View? = null

    var onDismissListener: (() -> Unit)? = null
    var goDetail: (() -> Unit)? = null
    var content: String? = null
    var buttonText: String? = null

    private val handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message) {
            if (contentView != null) {
                (activity.window?.decorView as? ViewGroup)?.removeView(contentView)
                contentView = null
                onDismissListener?.invoke()
            }
        }
    }

    constructor(
        activity: Activity,
        id: String,
        projectId: String,
        title: String,
        from: String,
        d: Float
    ) : this(activity, projectId, title, id, from) {
        this.duration = d
    }

    fun replace(id: String, projectId: String?, title: String?) {
        this.id = id;
        this.projectId = projectId;
        this.title = title
        handler.removeMessages(0)
        handler.sendEmptyMessageDelayed(0, (duration * 1000).toLong())
    }

    fun show() {
        if (contentView != null) {
            return
        }
        val decorView = activity.window?.decorView as? ViewGroup
        if (decorView == null) {
            Log.e("JoyWorkSnackBar", "JoyWorkSnackBar#show should be used after setContentView")
            return
        }
        contentView =
            activity.layoutInflater.inflate(R.layout.jdme_joywork_snackbar, decorView, false)

        val container = contentView!!.findViewById<View>(R.id.container)
        container.background = DrawableEx.roundSolidRect(
            Color.parseColor("#CC000000"),
            CommonUtils.dp2px(activity, 6).toFloat()
        )

        val offsetView = contentView!!.findViewById<View>(R.id.offset)
        val layoutParams = offsetView.layoutParams
            ?: LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0)
        layoutParams.height =
            decorView.height - (decorView.findViewById<View?>(Window.ID_ANDROID_CONTENT)?.bottom
                ?: decorView.height)

        contentView!!.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                handler.removeMessages(0)
                handler.sendEmptyMessage(1)
            }
            false
        }
        if (content.isLegalString()) {
            val cv = contentView!!.findViewById<TextView>(R.id.mContentView)
            cv.text = content
        }

        val button = contentView!!.findViewById<TextView>(R.id.to_detail)
        if (buttonText.isLegalString()) {
            button.text = buttonText
        }
        button.setOnClickListener {
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_SNACKBAR_DETAIL,
                JoyWorkConstant.JOYWORK_SNACKBAR_DETAIL
            )
            goDetail?.invoke()
                ?: JoyWorkMediator.goDetail(
                    activity,
                    JoyWorkDetailParam(title, id, projectId).apply {
                        from = <EMAIL>
                        reqCode = 10
                        obj = DetailReturnParcel()
                    })
            handler.removeMessages(0)
            handler.sendEmptyMessage(1)
        }
        handler.sendEmptyMessageDelayed(0, (duration * 1000).toLong())
        decorView.addView(contentView)
    }
}