package com.jd.oa.joywork.team;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

import androidx.appcompat.app.ActionBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.jd.oa.BaseActivity;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.detail.data.TaskDetailWebservice;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.team.bean.ProjectList;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;
import com.jd.oa.utils.ActionBarHelper;

import java.util.ArrayList;

public class SelectProjectActivity extends BaseActivity {
    private RecyclerView rvProjectList;
    private LinearLayout llEmpty;
    private ProjectListAdapter projectListAdapter;
    private RefreshRecyclerLayout refreshView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_select_project_task_group);
        initView();
        getProjectList(0);
    }

    private void initView() {
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            actionBar.hide();
        }
        findViewById(R.id.iv_me_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SelectProjectActivity.this.finish();
            }
        });
        rvProjectList = findViewById(R.id.recycleView);
        refreshView = findViewById(R.id.refreshView);
        llEmpty = findViewById(R.id.ll_list_empty);
        rvProjectList.setLayoutManager(new LinearLayoutManager(this));
        projectListAdapter = new ProjectListAdapter(this, projectList, false);
        rvProjectList.setAdapter(projectListAdapter);
        projectListAdapter.setOnItemClickListener(new ProjectListAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(ProjectList.Content.List list) {
                Intent data = new Intent();
                JoyWorkDetail.Project project = new JoyWorkDetail.Project(list.getProjectId(), list.getTitle());
                data.putExtra("project", project);
                setResult(RESULT_OK, data);
                finish();
            }
        });
    }


    private ArrayList<ProjectList.Content.List> projectList = new ArrayList();

    public void getProjectList(int pageSize) {
        TaskDetailWebservice.getProjectList(pageSize, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                Activity activity = (Activity) SelectProjectActivity.this;
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                super.onSuccess(info);
                Log.e(TAG, "onSuccess: " + info.result);
                Gson gson = new Gson();
                ProjectList list = gson.fromJson(info.result, ProjectList.class);
                if (list.getContent().getList() != null && list.getContent().getList().size() > 0) {
                    llEmpty.setVisibility(View.GONE);
                    rvProjectList.setVisibility(View.VISIBLE);
                    projectList.clear();
                    projectList.addAll(list.getContent().getList());
                    projectListAdapter.notifyDataSetChanged();
                } else {
                    llEmpty.setVisibility(View.VISIBLE);
                    rvProjectList.setVisibility(View.GONE);
                }
                if (refreshView.isRefreshing()) {
                    refreshView.setRefreshing(false);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                Activity activity = SelectProjectActivity.this;
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                if (refreshView.isRefreshing()) {
                    refreshView.setRefreshing(false);
                }
            }
        });
    }
}