package com.jd.oa.joywork.team.kpi

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.joywork.team.bean.VisibleSetting
import com.jd.oa.joywork.utils.JoyWorkLiveDataRepo
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.joywork_visible_setting.*
import kotlinx.coroutines.*

/**
 * Set the goal's visibility.
 */
class VisibleSettingActivity : BaseActivity(), UserSplinterCallback, DepartmentSplinterCallback {

    companion object {
        private val action = "joywork.goal.change.visible"
        fun start(goalId: String, context: Context) {
            val intent = Intent(context, VisibleSettingActivity::class.java)
            intent.putExtra("goalId", goalId)
            context.startActivity(intent)
        }

        fun register(receiver: BroadcastReceiver, context: Context) {
            val intentFilter = IntentFilter(action)
            LocalBroadcastManager.getInstance(context).registerReceiver(receiver, intentFilter)
        }

        private fun VisibleSettingActivity.goalId(): String {
            return intent.getStringExtra("goalId")!!
        }
    }

    // Raw data obtained from the server
    private var mServerSetting: VisibleSetting? = null

    // In the beginning, it is the same as [mServerSetting].
    // When the user changes the selected item, we will update [mLocalSetting].
    // Likewise, when the user modifies the visible users, we will also update [mLocalSetting].
    private var mLocalSetting: VisibleSetting? = null
    private val scope = MainScope()

    private val mSplinters = mutableListOf<VisibleSettingSplinter>()

    private var visibleUserLiveData: VisibleUserLiveData? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        visibleUserLiveData = JoyWorkLiveDataRepo.getOrCreate(VISIBLE_USER_LIVE_DATA_TOKEN, this) {
            VisibleUserLiveData()
        }
        setContentView(R.layout.joywork_visible_setting)
        findViewById<View>(R.id.mReturn).setOnClickListener {
            finish()
        }
        mSave.setOnClickListener {
            saveSetting()
        }
        mSupervisorContainer.setOnClickListener {
            val s = mLocalSetting ?: return@setOnClickListener
            if (!s.isOnlySupervisor) {
                selectItem(VisibleSetting.TYPE_ONLY_SUPERVISOR)
                updateSaveUI()
            }
        }
        mAllContainer.setOnClickListener {
            val s = mLocalSetting ?: return@setOnClickListener
            if (!s.isAll) {
                selectItem(VisibleSetting.TYPE_ALL)
                updateSaveUI()
            }
        }
        mPartContainer.setOnClickListener {
            val s = mLocalSetting ?: return@setOnClickListener
            if (!s.isPart) {
                selectItem(VisibleSetting.TYPE_PART)
                updateSaveUI()
            }
        }
        mPartErrorRetry.setOnClickListener {
            showVisibleUsers()
        }

        scope.launch {
            val setting = KpiRepo.getGoalVisible(goalId())
            setting.ifSuccessToast {
                mServerSetting = it!!
                mLocalSetting = it.copy()
                selectItem(it.visibleType, true)
                if (it.isPart) {
                    showVisibleUsers()
                }
            }
        }

        visibleUserLiveData?.visibleUserLiveData?.observe(this) {
            mLocalSetting?.replaceUsers(it)
            updateSaveUI()
            showPartSuccessUI()
            mSplinters.firstOrNull {
                it::class.java == UserSplinter::class.java
            }?.dataChange()
        }

        visibleUserLiveData?.deptLiveData?.observe(this) {
            mLocalSetting?.replaceDepts(it)
            updateSaveUI()
            showPartSuccessUI()
            mSplinters.firstOrNull {
                it::class.java == DepartmentSplinter::class.java
            }?.dataChange()
        }
    }

    override fun finish() {
        if (hadChanged()) {
            showQuitVisibleSettingDialog(this) {
                superFinish()
            }
        } else {
            superFinish()
        }
    }

    private fun superFinish() {
        super.finish()
    }

    private fun saveSetting() {
        scope.launch {
            val local = mLocalSetting ?: return@launch
            val type = local.visibleType ?: return@launch
            mSave.isEnabled = false
            val (addedList, deletedList) = diffUsers()
            val (addedDepts, deletedDepts) = diffDepts()
            val result =
                KpiRepo.saveVisibleSetting(
                    goalId(),
                    type,
                    addedList,
                    deletedList,
                    addedDepts,
                    deletedDepts
                )
            result.isSuccess({ t, msg, c ->
                result.toastFailure(t, msg, c)
                mSave.isEnabled = true
            }) {
                Toast.makeText(
                    this@VisibleSettingActivity,
                    getString(R.string.joywork_setting_success),
                    Toast.LENGTH_SHORT
                ).show()
                val intent = Intent(action)
                intent.putExtra("goalId",goalId())
                intent.putExtra("visibleType", type)
                LocalBroadcastManager.getInstance(this@VisibleSettingActivity).sendBroadcast(intent)
                superFinish()
            }
        }
    }

    private fun selectItem(type: Int, init: Boolean = false) {
        val localSetting = mLocalSetting ?: return
        if (localSetting.visibleType != type || init) {
            unselect(if (init) null else getIconByType(localSetting.visibleType))
            select(getIconByType(type))
            localSetting.visibleType = type

            if (mPartSubInfo.isVisible() && !localSetting.isPart) {
                mPartSubInfo.gone()
            } else if (localSetting.isPart) {
                showVisibleUsers()
            }
        }
    }

    private fun getIconByType(type: Int): TextView {
        return when (type) {
            VisibleSetting.TYPE_ONLY_SUPERVISOR -> mSupervisorIcon
            VisibleSetting.TYPE_ALL -> mAllIcon
            else -> mPartIcon
        }
    }

    private fun unselect(tv: TextView?) {
        tv?.setText(R.string.icon_prompt_circle)
        tv?.argb("#999999")
    }

    private fun select(tv: TextView) {
        tv.setText(R.string.icon_padding_checkcircle)
        tv.argb("#FE3E33")
    }

    private fun showVisibleUsers() {
        if (!mLocalSetting?.isPart.isTrue()) {
            return
        }
        if (mServerSetting?.visibleUsersObtained.isTrue()) {
            showPartSuccessUI()
            mSplinters.forEach { s ->
                s.dataChange()
            }
            return
        }
        showPartGettingUI()
        scope.launch {
            withContext(Dispatchers.IO) {
                val result = KpiRepo.getVisibleUsers(goalId())
                result.isSuccess({ _, _, _ ->
                    showPartErrorUI()
                }) {
                    mServerSetting?.replaceUsers(it?.members)
                    mServerSetting?.replaceDepts(it?.depts)
                    mLocalSetting?.replaceUsers(it?.members)
                    mLocalSetting?.replaceDepts(it?.depts)
                    mServerSetting?.visibleUsersObtained = true
                    visibleUserLiveData?.replaceVisibleUser(
                        mLocalSetting?.members ?: mutableListOf()
                    )
                    visibleUserLiveData?.replaceDepts(mLocalSetting?.depts ?: mutableListOf())
                }
            }
        }
    }

    private fun showPartSuccessUI() {
        mPartSubInfo.visible()
        mVisibleItemsContainer.visible()
        mGettingTipsContainer.gone()
        if (mSplinters.isEmpty()) {
            mSplinters.add(DepartmentSplinter(this, this, visibleUserLiveData))
            mSplinters.add(UserSplinter(this, this, visibleUserLiveData))
            mSplinters.forEach {
                it.buildView(mVisibleItemsContainer)
            }
        }
    }

    private fun showPartGettingUI() {
        mPartSubInfo.visible()
        mVisibleItemsContainer.gone()
        mGettingTipsContainer.visible()
        mPartErrorIcon.gone()
        mGettingTipsView.visible()
        mGettingTipsView.setText(R.string.joywork_visible_people_getting)
        mProgressBar.visible()
        mPartErrorRetry.gone()
    }

    private fun showPartErrorUI() {
        scope.launch(Dispatchers.Main) {
            mPartSubInfo.visible()
            mVisibleItemsContainer.gone()
            mGettingTipsContainer.visible()
            mPartErrorIcon.visible()
            mGettingTipsView.visible()
            mGettingTipsView.setText(R.string.joywork_get_date_error)
            mProgressBar.gone()
            mPartErrorRetry.visible()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
        visibleUserLiveData?.removeUser(this)
    }

    private fun updateSaveUI() {
        mSave.isEnabled = hadChanged()
    }

    private fun hadChanged(): Boolean {
        val server = mServerSetting
        val local = mLocalSetting
        if (server == local) return false
        if (server == null || local == null) {
            return true
        }
        if (server.visibleType != local.visibleType) {
            return true
        }
        val (addedList, deletedList) = diffUsers()
        val (addedDepts, deletedDepts) = diffDepts()
        return addedList.isLegalList() || deletedList.isLegalList() || addedDepts.isLegalList() || deletedDepts.isLegalList()
    }

    /**
     * Pair.first：the newly added users; Pair.second: the deleted users
     */
    private fun diffUsers(): Pair<List<JoyWorkUser>, List<JoyWorkUser>> {
        val server = mServerSetting
        val local = mLocalSetting
        val addedList = mutableListOf<JoyWorkUser>()
        val deletedList = mutableListOf<JoyWorkUser>()
        val result = Pair<List<JoyWorkUser>, List<JoyWorkUser>>(addedList, deletedList)
        if (server == null || !server.members.isLegalList()) {
            if (local != null && local.members.isLegalList()) {
                addedList.addAll(local.members!!)
            }
        } else {
            if (local == null || !local.members.isLegalList()) {
                // Fast results if the user had deleted all visible users
                deletedList.addAll(server.members!!)
            } else {
                val serverUsersMap = HashMap<String, JoyWorkUser>()
                server.members?.forEach {
                    serverUsersMap["${it.ddAppId}_${it.emplAccount}"] = it
                }
                val set = HashSet<String>()
                local.members?.forEach {
                    val key = "${it.ddAppId}_${it.emplAccount}"
                    if (!set.contains(key)) {
                        set.add(key)
                        if (!serverUsersMap.containsKey(key)) {
                            addedList.add(it)
                        } else {
                            serverUsersMap.remove(key)
                        }
                    }
                }
                serverUsersMap.entries.forEach {
                    deletedList.add(it.value)
                }
            }
        }
        return result
    }

    private fun diffDepts(): Pair<List<DeptInfo>, List<DeptInfo>> {
        val server = mServerSetting
        val local = mLocalSetting
        val addedList = mutableListOf<DeptInfo>()
        val deletedList = mutableListOf<DeptInfo>()
        val result = Pair<List<DeptInfo>, List<DeptInfo>>(addedList, deletedList)
        if (server == null || !server.depts.isLegalList()) {
            if (local != null && local.depts.isLegalList()) {
                addedList.addAll(local.depts!!)
            }
        } else {
            if (local == null || !local.depts.isLegalList()) {
                deletedList.addAll(server.depts!!)
            } else {
                val serverMap = HashMap<String, DeptInfo>()
                server.depts?.forEach {
                    serverMap[it.deptId] = it
                }
                val set = HashSet<String>()
                local.depts?.forEach {
                    val key = it.deptId
                    if (!set.contains(key)) {
                        set.add(key)
                        if (!serverMap.containsKey(key)) {
                            addedList.add(it)
                        } else {
                            serverMap.remove(key)
                        }
                    }
                }
                serverMap.entries.forEach {
                    deletedList.add(it.value)
                }
            }
        }
        return result
    }

    // called from [UserSplinter] start
    override fun getData(): List<JoyWorkUser>? {
        return mLocalSetting?.members
    }

    override fun afterSelectedUser(users: List<JoyWorkUser>) {
        mLocalSetting?.frontUsers(users)
        visibleUserLiveData?.replaceVisibleUser(mLocalSetting?.members ?: mutableListOf())
    }
    // called from [UserSplinter] end

    // called from [DepartmentSplinterCallback] start
    override fun getDeptData(): List<DeptInfo>? {
        return mLocalSetting?.depts
    }

    override fun afterSelectedDept(ds: List<DeptInfo>) {
        mLocalSetting?.appendDepts(ds)
        visibleUserLiveData?.replaceDepts(mLocalSetting?.depts ?: mutableListOf())
    }
    // called from [DepartmentSplinterCallback] end
}