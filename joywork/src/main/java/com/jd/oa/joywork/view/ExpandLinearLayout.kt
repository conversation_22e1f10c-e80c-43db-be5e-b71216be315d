package com.jd.oa.joywork.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.string
import com.jd.oa.utils.TextHelper

/**
 * 可对内部 textView 执行展开操作的线性布局
 */
class ExpandLinearLayout(context: Context, attrs: AttributeSet) : LinearLayout(context, attrs) {
    val TYPE_SHRINK = 0
    val TYPE_EXPAND = 1
    public var mShrinkLineCount = 2
    private var expandType = TYPE_EXPAND

    private val initRunnable = Runnable {
        val tv = findViewById<TextView>(R.id.textView)
        val handle = findViewById<View>(R.id.ll_handled)
        val lineCount = TextHelper.getLineCount(
            tv.string(),
            tv.textSize,
            tv.width.toFloat(),
            tv.context.resources.displayMetrics
        )
        if (lineCount > mShrinkLineCount) {
            tv.maxLines = mShrinkLineCount
            expandType = TYPE_SHRINK
            handle.visibility = View.VISIBLE
            handle.setOnClickListener {
                if (expandType == TYPE_EXPAND) {
                    tv.maxLines = mShrinkLineCount
                    expandType = TYPE_SHRINK
                } else {
                    tv.maxLines = lineCount + 1
                    expandType = TYPE_EXPAND
                }
                handleAction(it, expandType == TYPE_EXPAND)
            }
        } else {
            handle.visibility = View.GONE
        }
    }

    var handleAction: (handle: View, isExpand: Boolean) -> Unit = { v, b ->

    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        post {
            initRunnable
        }
    }

    fun reInit() {
        post {
            val tv = findViewById<TextView>(R.id.textView)
            tv.maxLines = Int.MAX_VALUE
            initRunnable.run()
        }
    }
}