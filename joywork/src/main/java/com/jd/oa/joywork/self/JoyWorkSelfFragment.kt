package com.jd.oa.joywork.self

import android.animation.ValueAnimator
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.R.string
import com.jd.oa.joywork.bean.JoyWorkNum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.filter.JoyWorkFilterSortFragment
import com.jd.oa.joywork.filter.JoyWorkFilterSortFragmentItf
import com.jd.oa.joywork.self.assign.MyAssignFragment
import com.jd.oa.joywork.self.base.SelfListBaseFragment
import com.jd.oa.joywork.self.cooperate.MyCooperateFragment
import com.jd.oa.joywork.self.owner.MyOwnerFragment
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.shortcut.JoyWorkShortcutCreator
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.team.UIStatus
import com.jd.oa.joywork.ui.JoyWorkViewModel
import com.jd.oa.listener.Refreshable
import com.jd.oa.utils.*

/**
 * Joywork 我的待办 列表页。包括 "我负责、我指派、我协作" 三个 tab，内部使用 ViewPager+Fragment 显示
 *
 * <AUTHOR>
 */
class JoyWorkSelfFragment : BaseFragment(), Refreshable, View.OnClickListener, SelfBaseItf,
    JoyWorkFilterSortFragmentItf {
    private lateinit var mViewModel: JoyWorkViewModel // 各 frg 通过 vm 通信
    private lateinit var mAdapter: PagerAdapter
    private lateinit var mViewPager: ViewPager
    private lateinit var mTabRV: RecyclerView
    private lateinit var mTabsIndicator: View
    private var mInitPos = 0
    private var mLastPos = mInitPos

    private val tabItems: ArrayList<JoyWorkTabItem<Triple<String, String, Boolean>, Fragment>> =
        ArrayList()

    companion object {
        fun getInstance(isTab: Boolean): JoyWorkSelfFragment {
            val ret = JoyWorkSelfFragment()
            val bundle = Bundle()
            bundle.putBoolean("isTab", isTab)
            ret.arguments = bundle
            return ret
        }

        private fun JoyWorkSelfFragment.isTab(): Boolean {
            return arguments?.getBoolean("isTab") ?: false
        }
    }

    private var mStrategy: JoyWorkMainStrategy = JoyWorkMainNormalStrategy

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        kotlin.runCatching {
            (requireActivity() as? FunctionActivity)?.setBarHide()
        }
        mStrategy = if (isTab()) JoyWorkMainTabStrategy else JoyWorkMainNormalStrategy
        val view: View = inflater.inflate(R.layout.jdme_joywork_fragment_mine, container, false)

        ViewModelProviders.of(requireActivity())
            .get(TeamDetailViewModel::class.java).itfLiveData.value = this
        val f = JoyWorkFilterSortFragment()
        f.mUnableFilterSortStatus.add(TaskStatusEnum.FINISH)
        f.mUnableFilterSortStatus.add(TaskStatusEnum.DELETED)
        childFragmentManager.beginTransaction()
            .add(R.id.mFilterContainer, f).commit()
        view.findViewById<View>(R.id.mShowFilter).setOnClickListener {
            val c = view.findViewById<View>(R.id.mFilterContainer)
            if (c.isVisible()) {
                c.gone()
                it.setBackgroundColor(Color.TRANSPARENT)
            } else {
                c.visible()
                it.setBackgroundResource(R.drawable.joywork_all_round_4_1afe3e33)
            }
        }
        mViewPager = view.findViewById<View>(R.id.viewpager) as ViewPager
        mTabsIndicator = view.findViewById(R.id.mTabsIndicator)
        initTabItems()

        mAdapter = MineSunAppFragmentAdapter(childFragmentManager, tabItems)
        mViewPager.offscreenPageLimit = tabItems.size
        mViewPager.adapter = mAdapter
        mViewPager.addOnPageChangeListener(object : ViewPager.SimpleOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
                JDMAUtils.onEventClick(tabItems[position].clickId, tabItems[position].clickId)
                if (mLastPos != position) {
                    translateTo(mLastPos, position)
                    (tabItems[mLastPos].fragment as? SelfBaseItf)?.onPageUnselected()

                    mLastPos = position
                    (tabItems[mLastPos].fragment as? SelfBaseItf)?.onPageSelected()
                }
            }
        })
        view.findViewById<View>(R.id.add).apply {
            setOnClickListener(this@JoyWorkSelfFragment)
            val lp = (layoutParams as? ViewGroup.MarginLayoutParams)
                ?: ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            lp.apply {
                rightMargin =
                    requireActivity().resources.getDimensionPixelSize(mStrategy.addViewRightMargin)
                bottomMargin =
                    requireActivity().resources.getDimensionPixelSize(mStrategy.addViewBottomMargin)
            }
            layoutParams = lp
        }
        initTabs(view.findViewById<RecyclerView>(R.id.tabs).also { mTabRV = it })
        return view
    }

    private fun initTabItems() {
        tabItems.add(
            JoyWorkTabItem(
                Triple(string(R.string.joywork_my_tasks), "0", false),
                JoyWorkConstant.RESPONSE_TAB_CLICK,
                MyOwnerFragment()
            )
        )
        tabItems.add(
            JoyWorkTabItem(
                Triple(string(R.string.joywork_task_assignor), "0", false),
                JoyWorkConstant.ASSIGN_TAB_CLICK,
                MyAssignFragment()
            )
        )
        tabItems.add(
            JoyWorkTabItem(
                Triple(string(R.string.joywork_task_cooperator), "0", false),
                JoyWorkConstant.COOPERATE_TAB_CLICK,
                MyCooperateFragment()
            )
        )
//        tabItems.add(
//            JoyWorkTabItem(
//                Triple(string(R.string.joywork_my_create), "0", false),
//                null,
//                MyCreateFragment()
//            )
//        )

        mViewModel = ViewModelProviders.of(requireActivity()).get(JoyWorkViewModel::class.java)
        mViewModel.countFlow.observe(requireActivity()) {
            if (<EMAIL> != null) {
                tabItems[0].content = Triple(
                    tabItems[0].content.first,
                    JoyWorkNum.getStrNum(it?.taskNums?.myHandle),
                    tabItems[0].content.third
                )
                tabItems[1].content = Triple(
                    tabItems[1].content.first,
                    JoyWorkNum.getStrNum(it?.taskNums?.myAssign),
                    tabItems[1].content.third
                )

                tabItems[2].content = Triple(
                    tabItems[2].content.first,
                    JoyWorkNum.getStrNum(it?.taskNums?.myCooperate),
                    tabItems[2].content.third
                )
                if (::mTabRV.isInitialized) {
                    (mTabRV.adapter as? SelfMainTabAdapter)?.replace(tabItems.map { item ->
                        item.content
                    })
                }
            }
        }
        mViewModel.uiFlow.observe(requireActivity()) {
            if (it == EntranceType.MY_HANDLE) {
                mInitPos = 0
                mLastPos = 0
            } else if (it == EntranceType.MY_COOPERATE) {
                mInitPos = 2
                mLastPos = 2
            } else if (it == EntranceType.MY_ASSIGN) {
                mInitPos = 1
                mLastPos = 1
            }
        }
    }

    private fun initTabs(rv: RecyclerView) {
        rv.layoutManager =
            LinearLayoutManager(requireActivity(), LinearLayoutManager.HORIZONTAL, false)
        val ts = ArrayList<Triple<String, String, Boolean>>()
        tabItems.forEach {
            ts.add(it.content)
        }
        rv.adapter = SelfMainTabAdapter(ts, requireActivity(), mInitPos) {
            mViewPager.currentItem = it
        }
    }

    // tabbar 打开当前界面时会调用该方法
    override fun refresh() {
        tabItems.forEach {
            (tabItems[mLastPos].fragment as? SelfBaseItf)?.onShowAtTabbar()
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) { // 新建
            R.id.add -> {
                val tag =
                    (mAdapter as? MineSunAppFragmentAdapter)?.getFragmentTag(mViewPager.currentItem)
                var f: Fragment? = null
                if (tag != null) {
                    f = childFragmentManager.findFragmentByTag(tag)
                }
//                f = tabItems[mViewPager.currentItem].fragment
                if ((f as? SelfListBaseFragment)?.onCreateJoyWork().isTrue()) {
                    return
                }
                var tmpData: ShortcutDialogTmpData? = null
                if (f is SelfBaseItf) {
                    tmpData = ShortcutDialogTmpData()
                    tmpData.taskListTypeEnum = f.getTaskListType()
                    if (tmpData.taskListTypeEnum == TaskListTypeEnum.HANDLE) {
                        val self = JoyWorkUser.getSelf();
                        self.setToChief()
                        tmpData.owners = listOf(self)
                    }
                }
                val dialogO =
                    JoyWorkShortcutCreator.getShortcutDialog(requireActivity(), tmpData = tmpData)
                dialogO.showSnackBar = true
                dialogO.successCallback = { _, _, dialog ->
                    dialog?.dismiss()
                }
                dialogO.show()
                JDMAUtils.onEventClick(
                    JoyWorkConstant.INCOMPLETE_CLICK1,
                    JoyWorkConstant.INCOMPLETE_CLICK1
                )
            }
        }
    }

    override fun onPageUnselected() {
        tabItems.forEach {
            (it.fragment as? SelfBaseItf)?.onPageUnselected()
        }
    }

    override fun onPageSelected() {
        if (::mViewPager.isInitialized) {
            (tabItems[mViewPager.currentItem].fragment as? SelfBaseItf)?.onPageSelected()
        }
    }

    override fun onShowAtTabbar() {
        if (::mViewPager.isInitialized) {
            (tabItems[mViewPager.currentItem].fragment as? SelfBaseItf)?.onShowAtTabbar()
        }
    }

    override fun getTaskListType(): TaskListTypeEnum {
        // 当前该方法返回结果没有用处
        return TaskListTypeEnum.HANDLE
    }

    private var mInited = false
    override fun onResume() {
        super.onResume()
        JDMAUtils.onEventPagePV(
            requireContext(),
            JoyWorkConstant.JOYWORK_PAGE_SELF,
            JoyWorkConstant.JOYWORK_PAGE_SELF
        )
        if (!mInited) {
            mViewPager.currentItem = mInitPos
            mInited = true
        }
    }

    // tab's width - mTabsIndicator.padHor / 2
    private val mTabW = CommonUtils.dp2FloatPx(72)
    private var mValueAnimator: ValueAnimator? = null
    private fun translateTo(from: Int, to: Int) {
        if (from == to || !::mTabsIndicator.isInitialized) {
            return
        }
        mValueAnimator?.cancel()
        val animation = ValueAnimator.ofFloat(from.toFloat(), to.toFloat())
        animation.setInterpolator(LinearInterpolator())
        animation.setDuration(200)
        animation.addUpdateListener {
            val value = it.animatedValue as Float
            mTabsIndicator.translationX = value * mTabW
        }
        animation.start()
        mValueAnimator = animation
    }

    // begin JoyWorkFilterSortFragmentItf
    override fun handleSortActions(list: MutableList<ProjectSortAction>) {
        (tabItems[mViewPager.currentItem].fragment as? SelfListBaseFragment)?.handleSortActions(list)
    }

    override fun getPageId(): String {
        return (tabItems[mViewPager.currentItem].fragment as? SelfListBaseFragment)?.getPageId()
            ?: ""
    }

    override fun getViewType(): Int {
        return (tabItems[mViewPager.currentItem].fragment as? SelfListBaseFragment)?.getViewType()
            ?: -1
    }

    override fun getProjectId(): String {
        return (tabItems[mViewPager.currentItem].fragment as? SelfListBaseFragment)?.getProjectId()
            ?: ""
    }

    override fun isFilterSortUnable(): Boolean {
        return (tabItems[mViewPager.currentItem].fragment as? SelfListBaseFragment)?.isFilterSortUnable()
            ?: true
    }

    override fun getDefaultUIState(): UIStatus {
        return (tabItems[mViewPager.currentItem].fragment as? SelfListBaseFragment)?.getDefaultUIState()
            ?: UIStatus().apply {
                mStatus = TaskStatusEnum.UN_FINISH
                mFilter = FilterValue.getNullInstance()
                mSort = SortValue.getDefaultSort(requireContext())
            }
    }
    // end JoyWorkFilterSortFragmentItf
}

private class MineSunAppFragmentAdapter(
    fm: FragmentManager,
    private val mFragments: List<JoyWorkTabItem<Triple<String, String, Boolean>, Fragment>>
) : FragmentPagerAdapter(fm) {
    private val tags = HashMap<Int, String>()
    override fun getItem(position: Int): Fragment {
        return mFragments[position].fragment
    }

    override fun getCount(): Int {
        return mFragments.size
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        if (!tags.containsKey(position)) {
            val itemId = getItemId(position)
            tags[position] = makeFragmentName(container.id, itemId)
        }
        return super.instantiateItem(container, position)
    }

    private fun makeFragmentName(viewId: Int, id: Long): String {
        return "android:switcher:$viewId:$id"
    }

    fun getFragmentTag(position: Int): String? {
        return tags[position]
    }
}