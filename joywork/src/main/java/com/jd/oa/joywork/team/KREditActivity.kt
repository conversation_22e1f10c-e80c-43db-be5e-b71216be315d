package com.jd.oa.joywork.team

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.common.SerializableInteractionResult
import com.jd.oa.joywork.common.SerializableStub
import com.jd.oa.joywork.common.activity_interaction_create
import com.jd.oa.joywork.common.activity_interaction_update
import com.jd.oa.joywork.team.kpi.GoalCreateActivity
import com.jd.oa.joywork.team.kpi.KpiRepo
import com.jd.oa.joywork.title.CenterTitleFragment
import com.jd.oa.utils.TextWatcherAdapter
import com.jd.oa.utils.setCursorEnd
import com.jd.oa.utils.string
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.io.Serializable
import java.util.*

/**
 * kr 新建、编辑界面。本界面新建、修改成功后会通过广播通知，不接受 startActivityForResult() 逻辑
 *
 * - action = kr_broadaction_action
 * - value = SerializableInteractionResult
 *
 */
class KREditActivity : BaseActivity() {
    companion object {
        val kr_broadaction_action = "kr_broadaction_action"

        fun registerCallback(context: Context, receiver: BroadcastReceiver): String {
            val filter = IntentFilter(kr_broadaction_action)
            LocalBroadcastManager.getInstance(context).registerReceiver(receiver, filter)
            return kr_broadaction_action
        }

        fun newKR(activity: Activity, goalId: String) {
            val i = Intent(activity, KREditActivity::class.java)
            i.putExtra("type", "new")
            i.putExtra("goalId", goalId)
            activity.startActivity(i)
        }

        fun updateKR(activity: Activity, krId: String, title: String?) {
            val i = Intent(activity, KREditActivity::class.java)
            i.putExtra("type", "update")
            i.putExtra("krId", krId)
            i.putExtra("title", title ?: "")
            activity.startActivity(i)
        }

        private fun KREditActivity.isNew(): Boolean {
            return intent.getStringExtra("type") == "new"
        }

        /**
         * 新建时，当前 kr 所属目标 id
         */
        private fun KREditActivity.goalId(): String {
            return intent.getStringExtra("goalId") ?: ""
        }

        /**
         * 当前 kr 自己的 id。新建时返回 ""
         */
        private fun KREditActivity.krId(): String {
            return intent.getStringExtra("krId") ?: ""
        }

        private fun KREditActivity.title(): String {
            return intent.getStringExtra("title") ?: ""
        }

        fun parseResultIntent(intent: Intent): SerializableInteractionResult {
            return SerializableStub.readFromIntent(intent)
        }
    }

    private lateinit var mCreate: TextView
    private lateinit var mNum: TextView
    private lateinit var mContentView: EditText

    private val scope = MainScope()

    private val backClick = View.OnClickListener {
        if (Objects.equals(title(), mContentView.string())) {
            finish()
            return@OnClickListener
        }
        JoyWorkDialog.showAlertDialog(
                context = this,
                msg = getString(R.string.joywork_project_create_cancle_tip),
                okRes = R.string.joywork_urge_tips_ok,
                cancelResource = R.string.joywork_continue_edit,
                okClick = {
                    finish()
                }
        ) { _, d ->
            d.dismiss()
        }
    }

    private val sureClick = View.OnClickListener {
        if (isNew()) {
            newKr()
        } else {
            updateTarget()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        restoreFrom(savedInstanceState)
        setContentView(R.layout.joywork_kr_activity)
        // 标题
        CenterTitleFragment().into(this, R.id.mTitleContainer)
                .left(R.string.icon_prompt_close, c = backClick)
                .title(if (isNew()) R.string.joywork_kr_create_title else R.string.joywork_kr_edit_title)
                .action(if (isNew()) R.string.joywork_new else R.string.confirm, id = R.id.mCreate, index = 0, sureClick)
                .post {
                    mCreate = findViewById(R.id.mCreate)
                    mContentView.addTextChangedListener(object : TextWatcherAdapter() {
                        override fun afterTextChanged(s: Editable?) {
                            super.afterTextChanged(s)
                            mCreate.isEnabled = mContentView.string().isNotEmpty()
                            mNum.text = "${mContentView.string().length}/200"
                        }
                    })
                    if (isNew()) {
                        newTargetUI()
                    } else {
                        updateTargetUI()
                    }
                }

        mContentView = findViewById(R.id.mContentView)
        mContentView.requestFocus()
        mNum = findViewById(R.id.mNum)
    }

    private fun newKr() {
        scope.launch {
            mCreate.isEnabled = false
            val r = KpiRepo.newKr(
                    mContentView.string(),
                    goalId()
            )
            mCreate.isEnabled = true
            r.ifSuccessToast {
                val i = Intent()
                i.action = kr_broadaction_action
                val value = ArrayList<Serializable>()
                value.add(goalId())
                if (it != KR.stub) {
                    value.add(it!!)
                }
                SerializableInteractionResult(activity_interaction_create, value).writeToIntent(i)
                LocalBroadcastManager.getInstance(this@KREditActivity).sendBroadcast(i)
                finish()
            }
        }
    }

    private fun updateTarget() {
        scope.launch {
            mCreate.isEnabled = false
            val r = KpiRepo.updateKr(
                    mContentView.string(),
                    krId()
            )
            mCreate.isEnabled = false
            r.ifSuccessToast {
                val i = Intent()
                i.action = kr_broadaction_action
                val value = ArrayList<String>()
                value.add(krId())
                value.add(mContentView.string())
                SerializableInteractionResult(activity_interaction_update, value).writeToIntent(i)
                LocalBroadcastManager.getInstance(this@KREditActivity).sendBroadcast(i)
                finish()
            }
        }
    }

    private fun newTargetUI() {
        mContentView.setText("")
    }

    private fun updateTargetUI() {
        mContentView.setText(title())
        mContentView.setCursorEnd()
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        saveTo(outState)
    }
}