package com.jd.oa.joywork.common

import android.app.Activity
import com.jd.me.web2.webview.CommonWebViewListener
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.fragment.js.hybrid.JsBrowser
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit
import com.jd.oa.fragment.web.IWebContainer

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/13
 * @Description:
 */
class EventBrowser(
    webView: CommonWebViewListener,
    jsSdkKit: JsSdkKit,
    webFragment2: WebFragment2,
    val activity: Activity?,
    webContainer: IWebContainer?
) : Js<PERSON>rowser(webView, jsSdkKit, webFragment2, activity, webFragment2, webContainer) {

    // 覆盖 JsBrowser close 方法
    override fun close(time: Long) {
        super.close(time)
        activity.finish()
    }
}