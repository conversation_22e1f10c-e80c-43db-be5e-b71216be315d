package com.jd.oa.joywork.team.chat

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.JoyWorkProjectList.DividerListDTO
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.utils.argb
import java.util.Objects

class ProjectSelectorAdapter(
    private val context: Context,
    private var mCheckedId: String = "",
    private var items: List<JoyWorkProjectList.ListDTO>,
    private val itemClick: (JoyWorkProjectList.ListDTO) -> Unit,
) :
    RecyclerView.Adapter<ProjectSelectorAdapter.VH>() {

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mName = view.findViewById<TextView>(R.id.mName)
        val mCheck = view.findViewById<TextView>(R.id.mCheck)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProjectSelectorAdapter.VH {
        val view =
            context.layoutInflater.inflate(
                R.layout.joywork_project_selector_frg_item,
                parent,
                false
            )
        return VH(view)
    }

    override fun getItemCount(): Int {
        return items.size
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val data = items[position]
        val item = data as JoyWorkProjectList.ListDTO
        holder.itemView.tag = item
        holder.itemView.setOnClickListener {
            itemClick(it.tag as JoyWorkProjectList.ListDTO)
        }
        holder.mName.text = item.title
        if (Objects.equals(mCheckedId, item.projectId)) {
            holder.mCheck.setText(R.string.icon_general_single)
            holder.mCheck.argb("#F63218")
        } else {
            holder.mCheck.setText(R.string.icon_prompt_circle)
            holder.mCheck.argb("#DCDEE0")
        }
    }

    fun updateCheckedItem(dto: JoyWorkProjectList.ListDTO) {
        mCheckedId = dto.projectId
        notifyDataSetChanged()
    }

    fun setData(newItems: List<JoyWorkProjectList.ListDTO>) {
        this.items = ArrayList(newItems)
        notifyDataSetChanged()
    }
}