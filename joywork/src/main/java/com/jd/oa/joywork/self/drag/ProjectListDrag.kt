package com.jd.oa.joywork.self.drag

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.around.base.HeaderFooterRecyclerAdapterWrapper

interface DragViewAdapter {
    fun swap(src: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder)
    fun onBeginDrag(holder: RecyclerView.ViewHolder)
    fun onEndDrag(holder: RecyclerView.ViewHolder)
    fun canDrag(holder: RecyclerView.ViewHolder): Boolean
    fun canDropOver(recyclerView: RecyclerView, current: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean
}

class ProjectListDrag(private val recyclerView: RecyclerView?) : ItemTouchHelper.Callback() {

    fun adapter(): DragViewAdapter? {
        var a = recyclerView?.adapter
        if (a is Header<PERSON>ooterRecyclerAdapterWrapper) {
            a = a.targetAdapter
        }
        return a as? DragViewAdapter
    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        if (adapter()?.canDrag(viewHolder) != true) {
            return makeMovementFlags(0, 0)
        }
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        return makeMovementFlags(dragFlags, 0)
    }

    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        adapter()?.swap(viewHolder, target)
        return true
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
    }

    override fun canDropOver(recyclerView: RecyclerView, current: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        return adapter()?.canDropOver(recyclerView, current, target) ?: false
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        // 只有 item 可拖动
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && viewHolder != null) {
            adapter()?.onBeginDrag(viewHolder)
            return;
        }
        super.onSelectedChanged(viewHolder, actionState)
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        adapter()?.onEndDrag(viewHolder)
        super.clearView(recyclerView, viewHolder)
    }
}