package com.jd.oa.joywork.detail.data.entity;

/**
 * Auto-generated: 2021-07-15 10:45:13
 */
public class Resources {
    @Override
    public String toString() {
        return "Resources{" +
                "resourceId='" + resourceId + '\'' +
                ", bizType=" + bizType +
                ", size=" + size +
                ", fromMQ='" + fromMQ + '\'' +
                ", bizId='" + bizId + '\'' +
                ", name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", fileType='" + fileType + '\'' +
                ", resourceType=" + resourceType +
                '}';
    }

    private String resourceId;
    private int bizType;
    private Long size;
    private String fromMQ;
    private String bizId;
    private String name;
    private String url;
    private String fileType;

    private int resourceType;

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setBizType(int bizType) {
        this.bizType = bizType;
    }

    public int getBizType() {
        return bizType;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public int getResourceType() {
        return resourceType;
    }

    public void setResourceType(int resourceType) {
        this.resourceType = resourceType;
    }

    public Long getSize() {
        return size;
    }

    public void setFromMQ(String fromMQ) {
        this.fromMQ = fromMQ;
    }

    public String getFromMQ() {
        return fromMQ;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getBizId() {
        return bizId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrl() {
        return url;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileType() {
        return fileType;
    }

}