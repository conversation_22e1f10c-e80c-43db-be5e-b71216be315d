package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.R
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitContentController
import com.jd.oa.ui.dialog.ShitDialogConfig
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.gone
import com.jd.oa.utils.setSafeIcon
import com.jd.oa.utils.visible


class GroupActionDialog(
    context: Context,
    actions: List<JoyWorkAction>,
    val bottomAction: JoyWorkAction?,
    val onClick: (JoyWorkAction) -> Unit,
) : BaseShitDialog(context, ShitDialogConfig()) {
    private val listController = object : IShitContentController {
        override fun inflate(parent: FrameLayout): View {
            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val content = inflater.inflate(R.layout.joywork_dialog_group_actions, parent, false)
            val bottomRoot: View = content.findViewById(R.id.root)
            bottomAction?.run {
                bottomRoot.visible()
                val icon: TextView = bottomRoot.findViewById(R.id.icon)
                val title: TextView = bottomRoot.findViewById(R.id.title)
                bottomRoot.findViewById<View>(R.id.tail).gone()
                icon.setSafeIcon(bottomAction.iconId())
                icon.setTextColor(bottomAction.color())
                title.text = bottomAction.action
                title.setTextColor(bottomAction.color())
                content.setOnClickListener {
                    dismiss()
                    onClick.invoke(bottomAction)
                }
            } ?: run {
                bottomRoot.gone()
            }
            parent.addView(content)
            return content
        }
    }

    override var contentController: IShitContentController
        get() = listController
        set(value) {}

    private val adapter = GroupActionAdapter(actions) {
        dismiss()
        onClick.invoke(it)
    }

    override fun setupCustomContentView(content: View) {
        content.background = DrawableEx.roundSolidDirRect(
            Color.parseColor("#F4F5F6"),
            context.resources.getDimension(R.dimen.joywork_joywork_group_small),
            DrawableEx.DIR_TOP
        )

        val recycler = content.findViewById<RecyclerView>(R.id.recycler)
        recycler.layoutManager = LinearLayoutManager(content.context)
        recycler.adapter = adapter
    }

    private class GroupActionAdapter(
        var dataList: List<JoyWorkAction>,
        val onClick: (JoyWorkAction) -> Unit
    ) : RecyclerView.Adapter<ActionHolder>() {


        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ActionHolder {
            return ActionHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.jdme_dialog_group_action_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: ActionHolder, position: Int) {
            val item = dataList[position]
            holder.icon.setSafeIcon(item.iconId())
            holder.icon.setTextColor(item.color())
            holder.title.text = item.action
            holder.title.setTextColor(item.color())
            holder.tail.gone()
            holder.itemView.setOnClickListener {
                onClick.invoke(item)
            }
        }

        override fun getItemCount(): Int {
            return dataList.size
        }
    }

    class ActionHolder(view: View) : RecyclerView.ViewHolder(view) {
        var icon: TextView = view.findViewById(R.id.icon)
        var title: TextView = view.findViewById(R.id.title)
        var tail: TextView = view.findViewById(R.id.tail)
    }

}






