package com.jd.oa.joywork.detail.ui.des

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.TextView
import com.jd.oa.BaseActivity
import com.jd.oa.annotation.Navigation
import com.jd.oa.joywork.CmnTitleHelper
import com.jd.oa.joywork.JoyWorkDialog
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.isLegalString
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.utils.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.common.ShortcutDialogCommon
import com.jd.oa.joywork.detail.ui.TaskUiUtils

@Navigation(hidden = true)
class JoyWorkDesActivity : BaseActivity(), View.OnClickListener {
    companion object {
        const val KEY = "data"
        const val ID = "id"
        const val TITLE = "title"
        private const val CONTENT_HINT = "content_hint"
        fun inflateIntent(
            intent: Intent,
            titleRes: Int?,
            contentRes: Int = R.string.joy_work_description
        ) {
            if (titleRes != null) {
                intent.putExtra(TITLE, titleRes)
            }
            intent.putExtra(CONTENT_HINT, contentRes)
        }

        private fun JoyWorkDesActivity.getContentHint(): Int {
            return intent.getIntExtra(CONTENT_HINT, R.string.joy_work_description)
        }

        private fun JoyWorkDesActivity.getTitleStr(): Int {
            return intent.getIntExtra(TITLE, R.string.joywork_des_title)
        }

        fun getKey(): String {
            return KEY;
        }

        private fun JoyWorkDesActivity.getData(): String? {
            return intent.getStringExtra(KEY)
        }

        private fun JoyWorkDesActivity.getId(): String? {
            return intent.getStringExtra(ID)
        }

        fun Intent.getDescContent(): String? {
            return getStringExtra(KEY)
        }
    }

    private var mSendView: TextView? = null
    private var mBackView: View? = null
    private lateinit var mContentView: EditText
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.joywork_activity_des)
        ActionBarHelper.init(this)
        CmnTitleHelper.handleTitle(
            this,
            getTitleStr(),
            R.string.me_save,
            findViewById(R.id.title)
        ) { back, _, action ->
            mSendView = action
            mSendView!!.setTextColor(ColorEx.addItem(ColorEx.Item().apply {
                enable = true
                color = <EMAIL>(R.color.joywork_red)
            }, ColorEx.Item().apply {
                enable = false
                color = Color.parseColor("#80FE3B30")
            }))
            mSendView!!.setOnClickListener(this@JoyWorkDesActivity)
            mBackView = back
            back.setOnClickListener(this@JoyWorkDesActivity)
        }
        mContentView = findViewById(R.id.content)
        mContentView.setHint(getContentHint())
//        mContentView.addTextChangedListener(object : TextWatcherAdapter() {
//            override fun afterTextChanged(s: Editable?) {
//                mSendView?.isEnabled = s?.run {
//                    mContentView.text.toString().isLegalString()
//                } ?: false
//            }
//        })
        mContentView.setText(getData() ?: "")
        mContentView.setCursorEnd()
        ShortcutDialogCommon.openKeyboard(mContentView, this)
    }

    override fun onClick(v: View?) {
        when (v) {
            mBackView -> {
                exit()
            }
            mSendView -> {
                // 保存
                val des = mContentView.text.toString().trim()
                if (getId().isLegalString()) {
                    // 有 id，需要将描述存储到待办中
                    TaskDetailWebservice.updateRemark(
                        getId(),
                        des,
                        null,
                        object : TaskDetailWebservice.TaskCallback() {
                            override fun onSuccess(info: ResponseInfo<String>?) {
                                super.onSuccess(info)
                                if (!hasError) {
                                    ToastUtils.showInfoToast(R.string.me_save_success)
                                    saveSuccess()
                                }
                            }
                        })
                } else {
                    // 直接返回
                    saveSuccess()
                }
            }
        }
    }

    private fun saveSuccess() {
        setResult(Activity.RESULT_OK, Intent().apply {
            putExtra(KEY, mContentView.text.toString().trim())
        })
        finish()
    }

    override fun onBackPressed() {
        if (mContentView.string().isLegalString()) {
            JoyWorkDialog.showAlertDialog(
                this,
                R.string.joywork_project_create_cancle_tip,
                R.string.joywork_urge_tips_ok
            ) {
                super.onBackPressed()
            }
        } else {
            super.onBackPressed()
        }
    }

    private fun exit() {
        if (mContentView.string().isLegalString()) {
            JoyWorkDialog.showAlertDialog(
                this,
                R.string.joywork_project_create_cancle_tip,
                R.string.joywork_urge_tips_ok
            ) {
                super.finish()
            }
        } else {
            super.finish()
        }
    }
}