package com.jd.oa.joywork.detail.data.entity;

import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class TaskDetailEntity {

    private String errorCode;
    private JoyWorkContent content;
    private String errorMsg;

    private boolean titleEdit;

    public boolean getTitleEdit() {
        return titleEdit;
    }

    public void setTitleEdit(boolean titleEdit) {
        this.titleEdit = titleEdit;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setContent(JoyWorkContent content) {
        this.content = content;
    }

    public JoyWorkContent getContent() {
        return content;
    }

    public JoyWorkDetail getJoyWorkDetail() {
        return content == null ? null : content.taskDetail;
    }

    public String countSubJoyWorkFinish() {
        JoyWorkDetail detail = getJoyWorkDetail();
        if (detail == null) {
            return "0/0";
        } else {
            ArrayList<ChildWorks> works = detail.getChildWorks();
            if (works == null || works.isEmpty()) {
                return "0/0";
            } else {
                int c = 0;
                for (ChildWorks w : works) {
                    if (TaskStatusEnum.FINISH.isFinish(w.getTaskStatus())) {
                        c++;
                    }
                }
                return c + "/" + works.size();
            }
        }
    }

    public ArrayList<ChildWorks> getUnfinishSubjoywork() {
        JoyWorkDetail detail = getJoyWorkDetail();
        ArrayList<ChildWorks> ans = new ArrayList<>();
        if (detail == null) {
            return ans;
        } else {
            ArrayList<ChildWorks> works = detail.getChildWorks();
            if (works == null || works.isEmpty()) {
                return ans;
            } else {
                for (ChildWorks w : works) {
                    if (!TaskStatusEnum.FINISH.isFinish(w.getTaskStatus())) {
                        ans.add(w);
                    }
                }
                return ans;
            }
        }
    }

    public int getFinishSubJoyWorkSize() {
        JoyWorkDetail detail = getJoyWorkDetail();
        if (detail == null) {
            return 0;
        } else {
            ArrayList<ChildWorks> works = detail.getChildWorks();
            if (works == null || works.isEmpty()) {
                return 0;
            } else {
                int c = 0;
                for (ChildWorks w : works) {
                    if (TaskStatusEnum.FINISH.isFinish(w.getTaskStatus())) {
                        c++;
                    }
                }
                return c;
            }
        }
    }

    public int getSubJoyWorkOwners() {
        JoyWorkDetail detail = getJoyWorkDetail();
        if (detail == null) {
            return 0;
        } else {
            ArrayList<ChildWorks> works = detail.getChildWorks();
            if (works == null || works.isEmpty()) {
                return 0;
            } else {
                int c = 0;
                for (ChildWorks w : works) {
                    if (w.owners == null || w.owners.isEmpty()){
                        c++;
                    }
                }
                return c;
            }
        }
    }

    public List<CustomFieldGroup> getCustomField() {
        return content == null ? null : content.customFieldDic;
    }

    public Map<String, CustomFieldGroup> getMapCustomField() {
        List<CustomFieldGroup> field = getCustomField();
        if (!ObjExKt.isLegalList(field)) {
            return new HashMap<>();
        }

        LinkedHashMap<String, CustomFieldGroup> ans = new LinkedHashMap<>();
        for (CustomFieldGroup f : field) {
            ans.put(f.columnId, f);
        }
        return ans;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

}