package com.jd.oa.joywork.view

import android.content.Context
import android.graphics.PorterDuff
import com.jd.oa.around.widget.refreshlistview.DefaultPullLoadFooter
import com.jd.oa.joywork.R
import com.jd.oa.utils.forEachChild
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

class JoyWorkLoadMoreFooter(context: Context) : DefaultPullLoadFooter(context) {
    override fun setComplete() {
        visible()
        super.setComplete()
    }

    override fun setEmpty() {
        visible()
        super.setEmpty()
    }

    override fun setLoaded() {
        visible()
        super.setLoaded()
        mTvText.setText(R.string.joywork2_pull_load_more)
    }

    override fun setLoading() {
        visible()
        super.setLoading()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mPbLoading.indeterminateDrawable.setColorFilter(
            resources.getColor(R.color.joywork_red),
            PorterDuff.Mode.SRC_IN
        );
    }

    fun goneChildren() {
        forEachChild {
            it.gone()
        }
    }
}