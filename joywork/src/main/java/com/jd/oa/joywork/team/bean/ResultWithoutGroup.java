package com.jd.oa.joywork.team.bean;

import com.jd.oa.joywork.bean.JoyWork;

import java.util.ArrayList;
import java.util.List;

// work.task.getProjectTasksWithoutGroup.v2 返回结果
public class ResultWithoutGroup {
    public Integer pageSize;
    public Integer total;
    public Integer page;
    public List<JoyWork> tasks;
    public List<String> permissions;

    public int safeTotal() {
        return total != null ? total : 0;
    }

    public List<JoyWork> safeTasks() {
        if (tasks == null) {
            return new ArrayList<>();
        } else {
            return tasks;
        }
    }

    public List<String> getSafePermissions() {
        if (permissions == null) {
            permissions = new ArrayList<>();
        }
        return permissions;
    }

    public Part createPart(String id) {
        Part part = new Part();
        part.permissions = getSafePermissions();
        part.id = id;
        return part;
    }


    public static class Part {
        private List<String> permissions;
        private String id;

        public List<String> getSafePermissions() {
            if (permissions == null) {
                permissions = new ArrayList<>();
            }
            return permissions;
        }

        public String getId() {
            return id;
        }
    }
}
