package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.jd.oa.joywork.R
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitActionController
import com.jd.oa.utils.inflater

/**
 * 隐藏
 */
object GoneShitActionController : IShitActionController {
    override fun inflate(parent: FrameLayout, dialog: BaseShitDialog): View {
        parent.visibility = View.GONE
        return parent
    }
}

/**
 * 留有 10dp 的间隔
 */
object DividerShitActionController : IShitActionController {
    override fun inflate(parent: FrameLayout, dialog: BaseShitDialog): View {
        return parent.context.inflater.inflate(R.layout.joywork_dialog_divider, parent, false)
    }
}

/**
 * 圆角取消按钮
 */
object CancelShitActionController : IShitActionController {
    override fun inflate(parent: FrameLayout, dialog: BaseShitDialog): View {
        val inflater = parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val view = inflater.inflate(R.layout.joywork_dialog_cancel, parent, false)
        val cancelView = view.findViewById<View>(R.id.cancel)
        cancelView.setOnClickListener {
            dialog.cancel()
        }
        cancelView.setBackgroundColor(Color.WHITE)
        return view
    }
}
