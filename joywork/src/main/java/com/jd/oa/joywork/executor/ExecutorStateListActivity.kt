package com.jd.oa.joywork.executor

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.ViewModelProviders
import androidx.lifecycle.lifecycleScope
import androidx.viewpager.widget.ViewPager
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.Owner
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.self.SelfBaseItf
import com.jd.oa.model.TenantCode
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.*
import kotlinx.coroutines.launch

/**
 * 分已完成未完成列表
 */
class ExecutorStateListActivity : AppCompatActivity() {
    private val tabItems = ArrayList<JoyWorkTabItem<Pair<String, TabType>, Fragment>>()
    private lateinit var mTabContainer: LinearLayout
    private lateinit var mViewPager: ViewPager
    private lateinit var mAdapter: FragmentPagerAdapter

    private var mLastPos: Int = -1

    private val pageChangeListener = object : ViewPager.SimpleOnPageChangeListener() {
        override fun onPageSelected(position: Int) {
            tabItems[position].clickId?.apply {
                JDMAUtils.onEventClick(this, this)
            }
            if (mLastPos != position) {
                if (mLastPos != -1) {
                    mTabContainer.getChildAt(mLastPos).findViewById<TextView>(R.id.tab_title)
                        .apply {
                            normal()
                            argb("#666666")
                        }
                    mTabContainer.getChildAt(mLastPos).findViewById<View>(R.id.indicator)
                        .invisible()
                    (tabItems[mLastPos].fragment as? SelfBaseItf)?.onPageUnselected()
                }
                (tabItems[mViewPager.currentItem].fragment as? SelfBaseItf)?.onPageSelected()
                mLastPos = position

                mTabContainer.getChildAt(mLastPos).findViewById<TextView>(R.id.tab_title)
                    .apply {
                        bold()
                        argb("#333333")
                    }
                mTabContainer.getChildAt(mLastPos).findViewById<View>(R.id.indicator)
                    .visible()
            }
        }
    };

    private val onTabClick = View.OnClickListener {
        mViewPager.currentItem = it.tag as Int
    }

    companion object {
        /**
         * 可催办
         */
        val permission_urgeable = 1 shl 0

        /**
         * 可添加执行人
         */
        val permission_addable = 1 shl 1

        /**
         * 可删除执行人
         */
        val permission_deletable = 1 shl 2

        /**
         * 可将执行人标记为完成
         */
        val permission_finishable = 1 shl 3

        /**
         * 可将执行人标记为未完成
         */
        val permission_unfinishable = 1 shl 4

        /**
         * 可转派
         */
        val permission_transfer = 1 shl 5

        /**
         * 是否可设置/取消设置首席
         */
        val permission_ex_chief = 1 shl 6

        val repo = HashMap<String, Any>()

        fun inflateIntent(
            intent: Intent,
            taskId: String,
            executors: List<Owner>,
            permission: Int,
            resultCallback: (ms: ArrayList<Owner>) -> Unit
        ) {
            OneTimeDataRepo.data = executors
            val key = "${System.currentTimeMillis()}"
            repo[key] = resultCallback
            intent.putExtra("repoId", key)
            intent.putExtra("taskId", taskId)
            intent.putExtra("permission", permission)
        }

        private fun ExecutorStateListActivity.getResultCallback(): ((ArrayList<Owner>) -> Unit)? {
            return try {
                val key = intent!!.getStringExtra("repoId")
                repo[key!!] as? (ArrayList<Owner>) -> Unit
            } catch (e: Throwable) {
                null
            }
        }

        private fun ExecutorStateListActivity.getPermission(): Int {
            return try {
                intent!!.getIntExtra("permission", 0)
            } catch (e: Throwable) {
                0
            }
        }

        private fun ExecutorStateListActivity.clearCallback() {
            try {
                val key = intent!!.getStringExtra("repoId")
                repo.remove(key)
            } catch (e: Throwable) {
            }
        }

        private fun ExecutorStateListActivity.getJoyWorkId(): String? {
            return intent!!.getStringExtra("taskId")
        }

        private fun getExecutors(): ArrayList<Owner> {
            return (OneTimeDataRepo.data as? List<*>)?.run {
                return if (isEmpty()) {
                    ArrayList()
                } else {
                    if (first() is Owner) {
                        ArrayList(this as List<Owner>)
                    } else {
                        ArrayList()
                    }
                }
            } ?: ArrayList()
        }
    }

    private lateinit var mViewModel: ExecutorStateViewModel
    private lateinit var mAddView: View
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        setContentView(R.layout.joywork_executor_state_activity)

        findViewById<View>(R.id.back).setOnClickListener {
            finish()
        }

        mViewModel = ViewModelProviders.of(this).get(ExecutorStateViewModel::class.java)
        mViewModel.taskId = getJoyWorkId()
        mViewModel.permission = getPermission()

        val f = ArrayList<Owner>()
        val u = ArrayList<Owner>()
        getExecutors().forEach {
            if (TaskStatusEnum.isFinish(it.taskStatus)) {
                f.add(it)
            } else if (TaskStatusEnum.isUnFinish(it.taskStatus)) {
                u.add(it)
            }
        }
        mViewModel.replaceFinish(f)
        mViewModel.replaceUnFinish(u)

        mViewModel.finishOwnerChange.observe(this) {
            updateNum()
        }
        mViewModel.unfinishOwnerChange.observe(this) {
            updateNum()
        }
        mAddView = findViewById<View>(R.id.add)
        updateAddView()
        mViewPager = findViewById(R.id.viewpager)
        mViewPager.currentItem = 0
        mLastPos = mViewPager.currentItem
        initTabs()
        mTabContainer = findViewById<LinearLayout>(R.id.self_tabs_ll).also {
            tabItems.forEachIndexed { index, joyWorkTabItem ->
                val tabView = <EMAIL>(
                    R.layout.joywork_executor_tablayout_item,
                    it,
                    false
                )
                val title = tabView.findViewById<TextView>(R.id.tab_title)
                title.tag = joyWorkTabItem.content.second as TabType
                title.text = joyWorkTabItem.content.first
                if (index == mViewPager.currentItem) {
                    title.bold()
                    tabView.findViewById<View>(R.id.indicator).visible()
                } else {
                    title.normal()
                    tabView.findViewById<View>(R.id.indicator).invisible()
                }
                tabView.tag = index
                tabView.setOnClickListener(onTabClick)
                it.addView(tabView)
            }
        }
        mAdapter = object : FragmentPagerAdapter(supportFragmentManager) {
            override fun getItem(position: Int): Fragment {
                return tabItems[position].fragment
            }

            override fun getCount(): Int {
                return tabItems.size
            }
        }
        mViewPager.offscreenPageLimit = tabItems.size
        mViewPager.adapter = mAdapter
        mViewPager.addOnPageChangeListener(pageChangeListener)

        mViewModel.permissionChangeLiveData.observe(this) {
            updateAddView()
        }
    }

    private fun updateAddView() {
        if (mViewModel.canAdd()) {
            mAddView.visible()
            mAddView.setOnClickListener {
                add()
            }
        } else {
            mAddView.gone()
        }
    }

    private fun updateNum() {
        mTabContainer.forEachChild {
            val title = it.findViewById<TextView>(R.id.tab_title)
            var str = ""
            var num = 0
            when (title.tag as TabType) {
                TabType.FINISH -> {
                    str = getString(R.string.joywork_screen_finish)
                    num = mViewModel.finishOwners.size
                }
                TabType.UN_FINISH -> {
                    str = getString(R.string.me_joywork_unfinish)
                    num = mViewModel.unfinishOwners.size
                }
            }
            title.text = "$str $num"
        }
    }

    private fun add() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.DETAIL_OWNER_LIST,
            JoyWorkConstant.DETAIL_OWNER_LIST
        )
        val s = ArrayList<MemberEntityJd>()
        val result = mViewModel.getAll()
        if (result.isNotEmpty()) {
            result.forEach {
                it.emplAccount?.apply {
                    val jd = MemberEntityJd()
                    jd.mApp = it.app
                    jd.mId = it.emplAccount
                    jd.type = MemberEntityJd.TYPE_CONTACT
                    s.add(jd)
                }
            }
        }

        ExecutorUtils.selectExecutors(this, s, null, false, { selected ->
            if (selected != null && mViewModel.taskId.isLegalString()) {
                val membersNet: ArrayList<Owner> = ArrayList()
                val members = ArrayList<Members>()
                for (memberEntityJd in selected) {
                    memberEntityJd?.apply {
                        val member = Owner()
                        member.fromDD(this)
                        member.userRole = TaskUserRole.OWNER.code.toString()
                        member.teamId = JoyWorkUser.DEFAULT_TEAM_ID
                        membersNet.add(member)

                        val m = Members()
                        m.fromDD(this)
                        members.add(m)
                    }
                }
                if (membersNet.isLegalList()) {
                    <EMAIL> {
                        // 人员选择成功后，还需要查询一遍用户信息userId，在addTaskMembers时需要使用
                        getBatchInfo(members).ifSuccessSilence {
                            JoyWorkRepo.addRelation(
                                it?.users ?: emptyList(),
                                mViewModel.taskId!!,
                                null,
                                { mList ->
                                    mList.forEach { ms ->
                                        membersNet.firstOrNull { mn ->
                                            if (TenantCode.getByDDAppId(mn.ddAppId) != null) {
                                                mn.emplAccount == ms.emplAccount && mn.ddAppId == ms.ddAppId
                                            } else {
                                                mn.userId == ms.userId && mn.ddAppId == ms.ddAppId
                                            }
                                        }?.apply {
                                            departmentInfo = ms.deptInfo
                                            titleName = ms.titleName ?: ""
                                        }
                                    }
                                    mViewModel.addUnFinish(membersNet)
                                },
                                { msg ->
                                    ToastUtils.showInfoToast(msg)
                                })
                        }
                    }
                }
            }
        })
    }

    private fun initTabs() {
        tabItems.add(
            JoyWorkTabItem(
                Pair(string(R.string.me_joywork_unfinish), TabType.UN_FINISH),
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED,
                ExecutorStateListFragment.getInstance(ExecutorStateListFragment.TYPE_UNFINISH)
            )
        )
        tabItems.add(
            JoyWorkTabItem(
                Pair(string(R.string.joywork_screen_finish), TabType.FINISH),
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED,
                ExecutorStateListFragment.getInstance(ExecutorStateListFragment.TYPE_FINISH)
            )
        )
    }

    override fun finish() {
        val result = ArrayList<Owner>()
        result.addAll(mViewModel.finishOwners)
        result.addAll(mViewModel.unfinishOwners)
        getResultCallback()?.invoke(result)
        super.finish()
    }

    override fun onDestroy() {
        clearCallback()
        super.onDestroy()
    }

    private enum class TabType {
        UN_FINISH,
        FINISH
    }
}