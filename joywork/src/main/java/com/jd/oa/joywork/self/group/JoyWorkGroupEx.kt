package com.jd.oa.joywork.self.group

import android.content.Context
import android.graphics.Color
import com.jd.oa.joywork.IssueRegionTypeEnum
import com.jd.oa.joywork.R
import com.jd.oa.joywork.RegionTypeEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork2.backend.JoyWork2RegionType
import com.jd.oa.utils.string

object JoyWorkGroupEx {

    fun getGroups(context: Context): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        RegionTypeEnum.values().forEach {
            ans.add(
                ExpandableGroup(
                    JoyWorkTitle(
                        //  旧版本需要该字段，新版本没用。2022年04月08日
                        it.getIconId(),
                        context.string(it.strId),
                        0,
                        it.getColor() // 随便写的，没用: 2022年04月08日
                    ),
                    ArrayList<JoyWork>(),
                    it.code,
                    true
                )
            )
        }
        // 2022年07月20日   不显示已完成分组
        ans.removeAll {
            it.id == RegionTypeEnum.FINISH.code
        }
        return ans
    }

    fun getIssueGroups(context: Context): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val ans = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        IssueRegionTypeEnum.values().forEach {
            ans.add(
                ExpandableGroup(
                    JoyWorkTitle(
                        //  旧版本需要该字段，新版本没用。2022年04月08日
                        it.getIconId(),
                        context.string(it.strId),
                        0,
                        it.getColor() // 随便写的，没用: 2022年04月08日
                    ),
                    ArrayList<JoyWork>(),
                    it.code,
                    true
                )
            )
        }
        return ans
    }
}