package com.jd.oa.joywork.filter

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.string

class JoyWorkBlankAdapter(private val context: Context) :
    RecyclerView.Adapter<JoyWorkBlankAdapter.VH>() {

    inner class VH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val iconView: ImageView = itemView.findViewById(R.id.icon)
        val tipsView: TextView = itemView.findViewById(R.id.empty_tips)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): JoyWorkBlankAdapter.VH {
        return VH(context.inflater.inflate(R.layout.me_joywork_empty, parent, false))
    }

    override fun getItemCount() = 1

    override fun onBindViewHolder(holder: VH, position: Int) {
        holder.iconView.gone()
        holder.tipsView.gone()
    }
}