package com.jd.oa.joywork.detail

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewParent
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.detail.data.db.UpdateReporter
import com.jd.oa.joywork.detail.data.db.UpdateReporter.reportUpdate
import com.jd.oa.joywork.detail.data.entity.ChildWorks
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem
import com.jd.oa.joywork.detail.data.entity.custom.EmptyCustomFieldItem
import com.jd.oa.joywork.detail.ui.NotifyLinearLayout
import com.jd.oa.joywork.detail.ui.PercentLinearLayout
import com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork.view.JoyWorkAvatarViewCallback
import com.jd.oa.utils.*

/**
 * 子待办处理
 */
object SubJoyWorkEx {

    fun buildView(
        joyWorkDetail: JoyWorkDetail,
        childWorks: ChildWorks,
        view: View
    ): View {
        view.findViewById<TextView>(R.id.title).text = childWorks.title ?: ""
        val imageView = view.findViewById<JoyWorkAvatarView>(R.id.avatar)
        if (!childWorks.owners.isLegalList()) {
            imageView.gone()
        } else {
            imageView.visible()
            val urls = ArrayList<String>()
            childWorks.owners?.forEach {
                urls.add(it.headImg ?: "")
            }
            imageView.mCallback = object : JoyWorkAvatarViewCallback() {
                override fun needHat(
                    url: String,
                    position: Int,
                    itemView: ImageView,
                    parent: ViewGroup
                ): Boolean {
                    return position == 0 && childWorks.owners.firstOrNull()?.chief.isChief()
                }
            }
            imageView.setUrls(urls)
            view.requestLayout()
//            JoyWorkViewItem.avatar(imageView, childWorks.owner?.headImg)
        }

        val cb = view.findViewById<TextView>(R.id.cb_task)
        val cb2 = view.findViewById<ImageView>(R.id.cb_task2)
        cbUI(joyWorkDetail, cb, cb2, childWorks)

        val time = view.findViewById<TextView>(R.id.time)
        val timeIcon = view.findViewById<TextView>(R.id.timeIcon)
        if (childWorks.endTime != null) {
            if (childWorks.endTime >= System.currentTimeMillis() || childWorks.isFinish()) {
                time.argb("#666666")
                timeIcon.argb("#666666")
            } else {
                time.argbId(R.color.joywork_red);
                timeIcon.argbId(R.color.joywork_red);
            }
            time.text = DateShowUtils.getSimpleTimeShowTextWithHourMinute(childWorks.endTime)
//            time.text = yearMonthDay(view.context, childWorks.endTime)
        } else {
            time.gone()
            timeIcon.gone()
        }
        view.setOnClickListener {
            // 跳详情
            JoyWorkMediator.goDetail(
                it.context,
                JoyWorkDetailParam(
                    childWorks.title,
                    childWorks.taskId,
                    childWorks.projectId
                ).apply {
                    reqCode = TaskDetailPresenter.CHILD_TASK_REQ
                    obj = UpdateReporter.parcel
                })
        }
        val mBottomTips = view.findViewById<View>(R.id.mBottomTips)
        if (time.isGone() && imageView.isGone()) {
            mBottomTips.gone()
        } else {
            mBottomTips.visible()
        }
        return view
    }

    private fun cbUI(
        joyWorkDetail: JoyWorkDetail,
        cbText: TextView,
        cb: ImageView,
        childWorks: ChildWorks
    ) {
        if (childWorks.merged == true) {
            cbText.setText(R.string.icon_general_merge)
            cbText.visible()
            cb.gone()
            cbText.setBackgroundColor(Color.TRANSPARENT)
            cbText.setTextColor(Color.parseColor("#BFC1C4"))
            cbText.isEnabled = false
            return;
        }
        cb.setBackgroundColor(Color.TRANSPARENT)
        if (TaskStatusEnum.FINISH.isFinish(childWorks.uiTaskStatus)) { // 已完成
            // 已完成
            cb.isSelected = true
            if (!joyWorkDetail.canUpdateChildWorks() || childWorks.finishAction == ListFinishAction.NOT_ALLOW.code) {// 没有修改子待办权限
                cb.isEnabled = false
                cb.isClickable = false
            } else {
                cb.isEnabled = true
                cb.isClickable = true
                cb.tag = childWorks
                cb.setOnClickListener {
                    // 取消完成
                    finishOrUnfinish(joyWorkDetail, childWorks, cbText, cb)
                }
            }
        } else {
            // 未完成
            cb.isSelected = false
            if (!joyWorkDetail.canUpdateChildWorks() || childWorks.finishAction == ListFinishAction.NOT_ALLOW.code) {// 没有修改子待办权限
                JoyWorkViewItem.cbUnavailable(cb)
                cb.isClickable = false
            } else {
                cb.isEnabled = true
                cb.isClickable = true
                cb.tag = childWorks
                cb.setOnClickListener {
                    finishOrUnfinish(joyWorkDetail, childWorks, cbText, cb)
                }
            }
        }
    }

    private fun finishOrUnfinish(
        joyWorkDetail: JoyWorkDetail,
        work: ChildWorks,
        cb: TextView,
        cb2: ImageView,
    ) {
        val joywork = JoyWork()
        joywork.finishAction = work.finishAction
        joywork.taskId = work.taskId
        joywork.uiTaskStatus = work.uiTaskStatus
        JoyWorkViewItem.finishAction(joywork, cb.context, object : JoyWorkUpdateCallback {
            override fun result(success: Boolean, errorMsg: String) {
                if (success) {
                    reportUpdate()
                    work.toggleFinishStatus()
                } else {
                    ToastUtils.showInfoToast(errorMsg)
                }
                notifyParent(cb)
                cbUI(joyWorkDetail, cb, cb2, work)
            }

            override fun onStart() {

            }
        })
    }

    private fun notifyParent(view: View) {
        try {
            var parent: ViewParent? = view.parent
            while (parent != null && parent !is NotifyLinearLayout) {
                parent = parent.parent
            }
            (parent as? NotifyLinearLayout)?.apply {
                this.childFinishCall?.run()
            }
        } catch (t: Throwable) {
            t.printStackTrace()
        }
    }

    fun buildMarkView(
        isFirst: Boolean,
        parent: NotifyLinearLayout,
        item: CustomFieldItem,
        group: CustomFieldGroup,
        updatable: Boolean
    ) {
        val view =
            (parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).inflate(
                R.layout.task_detail_page_mark_item,
                parent,
                false
            ) as PercentLinearLayout
        parent.addView(view)

        val icon = view.findViewById<View>(R.id.mIcon)
        if (isFirst) {
            icon.visible()
        } else {
            icon.invisible()
        }

        val value = view.findViewById<TextView>(R.id.mark_value)
        val valueContainer = view.findViewById<ViewGroup>(R.id.mark_container)
        val valueVH = view.findViewById<View>(R.id.mark_value_vh)

        view.updateFunction = { it ->
            val newItem = it as CustomFieldItem
            view.setTag(R.id.tab_1, newItem)

            // 鬼知道后台会返回啥样的数据，先 catch 为敬
            kotlin.runCatching {
                valueContainer.background = DrawableEx.roundSolidRect(
                    Color.parseColor(newItem.itemContent.background),
                    CommonUtils.dp2FloatPx(4)
                )
            }
            kotlin.runCatching {
                if (newItem != EmptyCustomFieldItem.getInstance()) {
                    value.text = "${group.title}: ${newItem.itemContent.content}"
                    valueVH.gone()
                } else {
                    value.text = "${group.title}: "
                    valueVH.visible()
                }
                value.setTextColor(Color.parseColor(newItem.itemContent.color))
//                value.text = newItem.itemContent.content
            }
        }
        view.updateFunction?.invoke(item)

        view.setTag(R.id.tab_2, group)
        if (parent.childCall != null && updatable) {
            //  具体实现在 TaskDetailFragment 中
            view.setOnClickListener(parent.childCall)
        }
    }

    fun getRv(parent: NotifyLinearLayout, detail: JoyWorkDetail) {
        val rv = RecyclerView(parent.context)
        rv.layoutManager = LinearLayoutManager(parent.context, LinearLayoutManager.VERTICAL, false)
        rv.adapter = SubAdapter(detail, parent)
        parent.addView(rv)
        if (detail.canUpdateChildWorks()) {
            ItemTouchHelper(SubDrag(rv)).attachToRecyclerView(rv)
        }
    }
}