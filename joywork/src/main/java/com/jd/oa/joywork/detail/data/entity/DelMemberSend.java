package com.jd.oa.joywork.detail.data.entity;

import com.jd.oa.model.service.im.dd.entity.Members;

import java.util.List;

/**
 * Auto-generated: 2021-07-13 14:50:47
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class DelMemberSend {

    private String taskId;
    private List<Members> members;

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setMembers(List<Members> members) {
        this.members = members;
    }

    public List<Members> getMembers() {
        return members;
    }

}