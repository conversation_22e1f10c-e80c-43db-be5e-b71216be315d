package com.jd.oa.joywork.detail.data.entity.custom;

public class EmptyCustomFieldItem extends CustomFieldItem {
    private static EmptyCustomFieldItem contentS;

    public static EmptyCustomFieldItem getInstance() {
        if (contentS == null) {
            contentS = new EmptyCustomFieldItem();
        }
        return contentS;
    }

    @Override
    public ItemContent getItemContent() {
        ItemContent content = new ItemContent();
        content.background = "#F6F6F6";
        content.content = "—";
        content.color = "#8F959E";
        return content;
    }
}
