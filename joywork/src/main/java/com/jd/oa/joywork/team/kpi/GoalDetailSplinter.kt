package com.jd.oa.joywork.team.kpi

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.common.ParcelableInteractionResult
import com.jd.oa.joywork.common.SerializableInteractionResult
import com.jd.oa.joywork.common.activity_interaction_delete
import com.jd.oa.joywork.common.activity_interaction_update
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.setTextSizeRes
import com.jd.oa.joywork.team.KREditActivity
import com.jd.oa.joywork.team.kpi.GoalDetailActivity.Companion.deletable
import com.jd.oa.joywork.team.kpi.GoalDetailActivity.Companion.editable
import com.jd.oa.joywork.team.kpi.GoalDetailActivity.Companion.getId
import com.jd.oa.joywork.team.kpi.GoalDetailActivity.Companion.sharable
import com.jd.oa.joywork.utils.JoyWorkNetConfig
import com.jd.oa.utils.argbId
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import java.io.Serializable

sealed class DetailSplinter(protected val activity: GoalDetailActivity) {

    open fun onCreate() {

    }

    open fun onDestroy() {

    }

    open fun initTitleBar() {

    }

    abstract fun getDetailUrl(): String

    open fun syncData(intent: Intent) {

    }

    open fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    open fun updateFromWeb(intent: Intent) {

    }

    protected fun addRightAction(
        e: Boolean,
        id: Int,
        click: View.OnClickListener,
        index: Int,
        resId: Int
    ) {
        val view = activity.findViewById<View>(id)
        if (e) {
            if (view == null) {
                activity.mCenterTitleFragment?.action(resId, id, index, click)
            } else {
                view.visible()
                view.setOnClickListener(click)
            }
            setTitleStyle(id)
        } else {
            view?.gone()
        }
    }

    fun setTitleStyle(vararg ids: Int) {
        activity.mCenterTitleFragment?.post {
            ids.forEach {
                val view = activity.findViewById<TextView>(it)
                view.argbId(R.color.icon_black)
                view.setTextSizeRes(R.dimen.joywork_icon_big_size)
            }
        }
    }
}

class GoalDetailSplinter(activity: GoalDetailActivity) : DetailSplinter(activity) {
    private val reqGoal = 100

    private val mEditClick = View.OnClickListener {
        val target = mTarget ?: return@OnClickListener
        GoalCreateActivity.updateTarget(
            activity,
            reqGoal,
            activity.getId(),
            target.itemName
        )
    }
    private val mDelClick = View.OnClickListener {
        showRemoveDialog(activity) {
            KpiRepo.delTarget(activity.getId()) {
                if (it.isLegalString()) {
                    Toast.makeText(activity, it, Toast.LENGTH_SHORT).show()
                } else {
                    delGoal()
                }
            }
        }
    }

    private val mShareClick = View.OnClickListener {
        val target = KpiTarget()
        target.goalId = activity.getId()
        target.shareToDD(activity)
    }

    private var mTarget: KpiTarget? = null

    override fun initTitleBar() {
//        addRightAction(
//            activity.sharable(),
//            R.id.mShare,
//            mShareClick,
//            0,
//            R.string.icon_general_share1
//        )
        addRightAction(activity.editable(), R.id.mEdit, mEditClick, 1, R.string.icon_general_edit)
        addRightAction(activity.deletable(), R.id.mDel, mDelClick, 2, R.string.icon_edit_delete)
    }

    override fun getDetailUrl(): String {
        return JoyWorkNetConfig.getGoalDetailUrl(activity.getId(), activity)
    }

    override fun updateFromWeb(intent: Intent) {
        val obj = intent.getSerializableExtra("data") as? HashMap<String, Any>
        val detail = obj?.get("detail") as? Map<String, Any> ?: return
        val target = mTarget ?: KpiTarget()
        target.fromMap(detail)
        mTarget = target
        sendUpdateBroadcast()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        if (requestCode == reqGoal && resultCode == BaseActivity.RESULT_OK && data != null) { // 更新目标
            activity.refresh()
            return true
        }
        return false
    }

    override fun syncData(intent: Intent) {
        val obj = intent.getSerializableExtra("data") as? HashMap<String, Any>
        val detail = obj?.get("detail") as? Map<String, Any> ?: return
        val target = mTarget ?: KpiTarget()
        target.fromMap(detail)
        mTarget = target
//        addRightAction(
//            mTarget?.canShare.isTrue(),
//            R.id.mShare,
//            mShareClick,
//            0,
//            R.string.icon_general_share1
//        )
        addRightAction(
            mTarget?.canEdit.isTrue(),
            R.id.mEdit,
            mEditClick,
            1,
            R.string.icon_general_edit
        )
        addRightAction(mTarget?.canDel.isTrue(), R.id.mDel, mDelClick, 2, R.string.icon_edit_delete)
        sendUpdateBroadcast()
    }

    private fun delGoal() {
        val intent = Intent()
        intent.action = GoalDetailActivity.goal_action
        val target = KpiTarget()
        target.goalId = activity.getId()
        val array = ArrayList<KpiTarget>()
        array.add(target)
        val result = ParcelableInteractionResult(activity_interaction_delete, array)
        result.writeToIntent(intent)
        LocalBroadcastManager.getInstance(activity).sendBroadcast(intent)

        activity.superFinish()
    }

    private fun sendUpdateBroadcast() {
        val curTarget = mTarget ?: return
        val intent = Intent()
        intent.action = GoalDetailActivity.goal_action
        val target = KpiTarget()
        target.goalId = activity.getId()
        target.permissions = curTarget.permissions
        target.itemName = curTarget.itemName
        target.setSectionName(curTarget.getSectionName(activity))
        target.condition = curTarget.condition
        val array = ArrayList<KpiTarget>()
        array.add(target)
        val result = ParcelableInteractionResult(activity_interaction_update, array)
        result.writeToIntent(intent)
        LocalBroadcastManager.getInstance(activity).sendBroadcast(intent)
    }

    private fun KpiTarget.fromMap(detail: Map<String, Any>) {
        permissions = detail["permissions"] as? ArrayList<String>
        setSectionName((detail["sectionName"] as? String) ?: "")
        itemName = (detail["itemName"] as? String) ?: ""
        assessPeriod = (detail["assessPeriod"] as? String) ?: ""
        condition = (detail["condition"] as? Double)?.toInt() ?: 1
        if (detail.containsKey("progressNums")) {
            progressNums = (detail["progressNums"] as? Double)?.toInt() ?: 0
        }
    }
}

class KrDetailSplinter(activity: GoalDetailActivity) : DetailSplinter(activity) {
    private val mEditClick = View.OnClickListener {
        val kr = mKr ?: return@OnClickListener
        KREditActivity.updateKR(activity, kr.krId, kr.content)
    }

    private val mDelClick = View.OnClickListener {
        val kr = mKr ?: return@OnClickListener
        showRemoveKRDialog(activity) { delKr(kr) }
    }

    private var mKr: KR? = null

    private val r = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            activity.refresh()
        }
    }

    override fun onCreate() {
        KREditActivity.registerCallback(activity, r)
    }

    override fun onDestroy() {
        LocalBroadcastManager.getInstance(activity).unregisterReceiver(r)
    }

    override fun syncData(intent: Intent) {
        val obj = intent.getSerializableExtra("data") as? HashMap<String, Any>
        val detail = obj?.get("detail") as? Map<String, Any> ?: return
        mKr = mKr ?: KR()
        mKr?.fromMap(detail)
        addRightAction(
            mKr?.permissions?.krEditable().isTrue(),
            R.id.mEdit,
            mEditClick,
            0,
            R.string.icon_general_edit
        )
        addRightAction(
            mKr?.permissions?.krDeletable().isTrue(),
            R.id.mDel,
            mDelClick,
            1,
            R.string.icon_edit_delete
        )
    }

    override fun initTitleBar() {
        addRightAction(activity.editable(), R.id.mEdit, mEditClick, 0, R.string.icon_general_edit)
        addRightAction(activity.deletable(), R.id.mDel, mDelClick, 1, R.string.icon_edit_delete)
    }

    override fun getDetailUrl(): String {
        return JoyWorkNetConfig.getKrDetailUrl(activity.getId(), activity)
    }

    private fun KR.fromMap(detail: Map<String, Any>) {
        permissions = detail["permissions"] as? ArrayList<String>
        condition = (detail["condition"] as? Double)?.toInt() ?: 1
        krId = (detail["krId"] as? String) ?: ""
        goalId = (detail["goalId"] as? String) ?: ""
        content = (detail["content"] as? String) ?: ""
    }

    private fun delKr(kr: KR) {
        KpiRepo.delKr(kr.krId) {
            if (it.isLegalString()) {
                // 失败
                Toast.makeText(activity, it!!, Toast.LENGTH_SHORT).show()
            } else {
                // Firstly remove the receiver which will refresh the webview in the activity,
                // otherwise it will be called
                LocalBroadcastManager.getInstance(activity).unregisterReceiver(r)
                val intent = Intent()
                intent.action = KREditActivity.kr_broadaction_action
                val array = ArrayList<Serializable>()
                array.add(activity.getId())
                val target = KR()
                target.krId = activity.getId()
                array.add(target)
                val result = SerializableInteractionResult(
                    activity_interaction_delete,
                    array
                )
                result.writeToIntent(intent)
                LocalBroadcastManager.getInstance(activity).sendBroadcast(intent)
                activity.superFinish()
            }
        }
    }
}