package com.jd.oa.joywork.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

class UntouchableViewPager(context: Context, attributeSet: AttributeSet) : ViewPager(context, attributeSet) {
    override fun onInterceptTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        return false
//        return super.onInterceptTouchEvent(ev)
    }

    override fun onTouchEvent(ev: MotionEvent?): Bo<PERSON>an {
        return false
//        return super.onTouchEvent(ev)
    }
}