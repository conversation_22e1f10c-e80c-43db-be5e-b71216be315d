package com.jd.oa.joywork.executor

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.common.IUserListItemCallback
import com.jd.oa.joywork.common.UserListFragment
import com.jd.oa.joywork.detail.key
import com.jd.oa.joywork.isChief
import com.jd.oa.joywork.isLegalSet
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.argb
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

interface SetOwnerFragmentCallback {
    /**
     * get all the [JoyWorkUser] need to list
     */
    fun getUsers(): List<JoyWorkUser>

    /**
     * cancel check the specific [joyWorkUser]
     */
    fun remove(joyWorkUser: JoyWorkUser)

    /**
     * check the specific [joyWorkUser]
     */
    fun add(joyWorkUser: JoyWorkUser)

    /**
     * @param [joyWorkUser]: If it is null, it indicates the current user wants to unset the owner; otherwise, it is set [joyWorkUser] as owner
     */
    fun changeOwner(joyWorkUser: JoyWorkUser?)
}

class SetOwnerFragment : UserListFragment() {
    companion object {
        fun getInstance(): SetOwnerFragment {
            val r = SetOwnerFragment()
            val b = Bundle()
            b.putString("type", "user")
            r.arguments = b
            return r
        }
    }

    private val mSelectedUsers = HashSet<String>()

    private val mActionClick = View.OnClickListener {
        val joyWorkUser = it.tag as JoyWorkUser
        val key = joyWorkUser.key()
        val contains = mSelectedUsers.contains(key)
        if (contains) {
            mSelectedUsers.remove(key)
            getCallback().remove(joyWorkUser)
        } else {
            mSelectedUsers.add(key)
            getCallback().add(joyWorkUser)
        }
        refreshUI()
        if(contains){
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.PC_TASK_EVENT_CREATE_TASK_ASSIGNEES_REMOVE
                )
            }
        }
    }

    private val mSetClick = View.OnClickListener {
        val joyWorkUser = it.tag as JoyWorkUser
        val isChief = joyWorkUser.chief.isChief()
        forEachItem {
            (it as? JoyWorkUser)?.cancelChief()
        }
        if (!isChief) {
            joyWorkUser.setToChief()
            mSelectedUsers.add(joyWorkUser.key())
            getCallback().add(joyWorkUser)
        }
        getCallback().changeOwner(if (joyWorkUser.chief.isChief()) joyWorkUser else null)
        refreshUI()
        clickEvent {
            ClickEventParam(
                eventId = if (isChief)
                    JoyWorkConstant.MOBILE_EVENT_TASK_CREATE_TASK_ASSIGNEES_CANCEL_RESPONSIBLE
                else
                    JoyWorkConstant.PC_TASK_EVENT_CREATE_TASK_ASSIGNEES_SET_RESPONSIBLE
            )
        }
    }

    private val itemCallback = object : IUserListItemCallback() {
        override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            return VH(requireContext(), parent)
        }

        override fun onBindViewHolder(
            holder: RecyclerView.ViewHolder,
            position: Int,
            data: Any
        ): Boolean {
            val joyWorkUser = data as JoyWorkUser
            val vh = holder as VH
            if (joyWorkUser.chief.isChief()) {
                vh.mSet.setBackgroundResource(R.drawable.joywork_all_round_100_e6e6e6)
                vh.mSet.setText(R.string.joywork_set_owner_cancel)
                vh.hat.visible()
            } else {
                vh.mSet.setText(R.string.joywork_set_owner)
                vh.mSet.setBackgroundResource(R.drawable.joywork_stroke_round_100_cdcdcd)
                vh.hat.gone()
            }
            vh.mSet.tag = joyWorkUser
            vh.mSet.setOnClickListener(mSetClick)

            vh.name.text = mStrategy.getTitleName(data)
            vh.department.text = mStrategy.getSubTitleText(data)

            holder.itemView.tag = joyWorkUser
            holder.itemView.setOnClickListener(mActionClick)
            val key = joyWorkUser.key()
            if (mSelectedUsers.contains(key)) {
                holder.action.setText(R.string.icon_padding_checkcircle)
                holder.action.argb("#FE3B30")
            } else {
                holder.action.argb("#CDCDCD")
                holder.action.setText(R.string.icon_prompt_circle)
            }

            holder.avatar.setTag(R.id.tag_key_data, joyWorkUser)
            mStrategy.bindAvatar(holder.avatar)
            return true
        }
    }

    private fun getCallback(): SetOwnerFragmentCallback {
        return activity as SetOwnerFragmentCallback
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        mItemCallback = itemCallback
        super.onViewCreated(view, savedInstanceState)
        val us = getCallback().getUsers()
        val owner = us.firstOrNull {
            it.chief.isChief()
        }
        us.forEach {
            mSelectedUsers.add(it.key())
        }
        appendData(us)
        getCallback().changeOwner(owner)
    }

    fun appendAndSelect(data: List<JoyWorkUser>, keys: HashSet<String>) {
        data.forEach {
            mSelectedUsers.add(it.key())
        }
        if (keys.isLegalSet()) {
            mSelectedUsers.addAll(keys)
        }
        appendData(data)
    }

    fun changeSelAll(selected: Boolean) {
        mSelectedUsers.clear()
        if (selected) {
            getCallback().getUsers().forEach {
                mSelectedUsers.add(it.key())
                getCallback().add(it)
            }
            forEachItem {
                if (it is JoyWorkUser) {
                    if (it.chief.isChief()) {
                        getCallback().changeOwner(it)
                    }
                    getCallback().add(it)
                }
            }
        } else {
            forEachItem {
                if (it is JoyWorkUser) {
                    getCallback().remove(it)
                }
            }
        }
        refreshUI()
    }

    private class VH(context: Context, parent: ViewGroup) :
        RecyclerView.ViewHolder(context.inflater.inflate(R.layout.set_owner_item2, parent, false)) {
        val avatar: ImageView = itemView.findViewById(R.id.image)
        val name: TextView = itemView.findViewById(R.id.name)
        val action: TextView = itemView.findViewById(R.id.action)
        val department: TextView = itemView.findViewById(R.id.department)
        val hat: ImageView = itemView.findViewById(R.id.hat)
        val mSet: TextView = itemView.findViewById(R.id.mSet)
    }
}