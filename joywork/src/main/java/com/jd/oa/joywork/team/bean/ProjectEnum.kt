package com.jd.oa.joywork.team.bean

import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.hasContent

/**
 * hint 只用于描述，不能作为信息使用 F
 */
enum class ProjectGroupTypeEnum(val code: Int, hint: String) {
    COMMON(1, "普通分组"),
    INITIAL(2, "默认分组")
}

/**
 * hint 只用于描述，不能作为信息使用 F
 */
enum class ProjectStatusEnum(val code: Int, hint: String) {
    DELETED(0, "删除"),
    NORMAL(1, "正常状态")
}

enum class GroupDeleteEnum(val code: Int, hint: String) {
    NO_KEEP_TASK(1, "不保留待办"),
    KEEP_TASK(2, "保留待办"),
}

/**
 * 用户团队待办相关权限标识
 * hint 只用于描述,
 */
enum class ProjectPermissionEnum(val code: String, hint: String) {
    ASSIGN("assign", "转让项目"),
    UPDATE("update", "修改项目信息（描述、名称）"),
    DELETE("delete", "删除项目"),
    MEMBER("member", "共享项目（添加成员、删除成员）"),
    GROUP("group", "编辑待办分组（新建、删除、重命名、移动顺序）"),
    View("view", "编辑保存视图"),
    TASK("task", "编辑待办（新建、编辑、删除、评论待办、移动顺序）");

    fun containBy(p: List<String>?): Boolean {
        if (p.hasContent()) {
            return p!!.contains(this.code)
        }
        return false
    }
}

enum class ProjectErrorCode(val code: String, val hint: String) {
    DATA_NOT_AUTH("1104007", "无权限"),
    DATA_NOT_EXIST("1104931", "清单不存在"),
}

enum class ProjectFilterEnum(val code: Int, val hint: String) {
    SCREEN_NULL(0, "无") {
        override fun getClickId(): String {
            return ""
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_project_sort_no
        }
    },
    SCREEN_THIS_WEEK(1, "本周截止") {
        override fun getClickId(): String {
            return JoyWorkConstant.JOYWORK_FILTER_CUR
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_project_setting_filter_cur
        }
    },
    SCREEN_NEXT_WEEK(2, "下周截止") {
        override fun getClickId(): String {
            return JoyWorkConstant.JOYWORK_FILTER_NEXT
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_project_setting_filter_last
        }
    },
    SCREEN_ONLY_MINE(3, "仅我的待办") {
        override fun getClickId(): String {
            return JoyWorkConstant.JOYWORK_FILTER_MY
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_project_setting_filter_only
        }
    },
    SCREEN_EDN_TIME(4, "截止时间") {
        override fun getClickId(): String {
            return ""
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_project_setting_sort_time
        }
    },
    // 该 code 不能使用，具体的筛选 type 是自定义筛选内部的不同 item
    SCREEN_CUSTOMS(-1, "自定义筛选") {

        // 其所有方法返回值均不使用，忽略
        override fun getClickId(): String {
            return JoyWorkConstant.JOYWORK_FILTER_CUSTOM
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_project_setting_filter_custom
        }
    },
    SCREEN_CUSTOM(5, "清单自定义的扩展字段") {

        // 其所有方法返回值均不使用，忽略
        override fun getClickId(): String {
            return ""
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_project_setting_filter_custom
        }
    },
    SCREEN_EXECUTOR(6, "执行人") {
        override fun getClickId(): String {
            return ""
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_executor
        }
    },
    SCREEN_PROJECT(8,"所属清单"){
        override fun getClickId(): String {
            return ""
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_self_order
        }

    },
    SCREEN_PRIORITY(7, "优先级") {

        override fun getClickId(): String {
            return ""
        }

        override fun iconId(): Int {
            return R.string.icon_general_set
        }

        override fun stringId(): Int {
            return R.string.joywork_update_priority
        }
    };

    abstract fun getClickId(): String
    abstract fun iconId(): Int
    abstract fun stringId(): Int

    companion object {
        fun getInstance(code: Int): ProjectFilterEnum {
            return values().firstOrNull {
                it.code == code
            } ?: SCREEN_NULL
        }
    }
}

enum class ProjectSortUIEnum(val hint: String) {
    OLD("按旧版本样式显示"),
    NEW("按新版本样式显示，即截止时间等为分组标题")
}

/**
 * 排序类型
 */
enum class ProjectSortTypeEnum(val hint: String) {
    SORT_NO("分组内排序，默认(没有打开过排序 dialog)") {
        override fun isOpen(): Boolean {
            return false
        }

        override fun getUIType(): ProjectSortUIEnum {
            return ProjectSortUIEnum.OLD
        }
    },
    SORT_IN_GROUP("分组内排序打开") {
        override fun isOpen(): Boolean {
            return true
        }

        override fun getUIType(): ProjectSortUIEnum {
            return ProjectSortUIEnum.OLD
        }
    },
    SORT_OTHER("分组内排序关闭") {
        override fun isOpen(): Boolean {
            return false
        }

        override fun getUIType(): ProjectSortUIEnum {
            return ProjectSortUIEnum.NEW
        }
    };

    // 处于该状态时，分组内排序的按钮是否打开
    abstract fun isOpen(): Boolean

    /**
     * 返回 1 表示按旧版本分组
     * 返回 2 表示按截止时间等分组
     */
    abstract fun getUIType(): ProjectSortUIEnum
}

enum class ProjectSortEnum(val code: Int, val hint: String) {
    SORT_NULL(-1, "无") {
        override fun stringId(): Int {
            return R.string.joywork_project_sort_no
        }
    },
    SORT_CUSTOM_GROUP(9, "自定义分组") {
        override fun stringId(): Int {
            return R.string.joywork_sort_custom_group
        }
    },
    SORT_END_TIME(1, "截止时间") {
        override fun stringId(): Int {
            return R.string.joywork_update_deadline
        }
    },
    FINISH_TIME(2, "完成时间") {
        override fun stringId(): Int {
            return R.string.joywork_project_setting_sort_finish
        }
    },
    SORT_BY_OWNER(3, "指派对象(负责人、执行人)") {
        override fun stringId(): Int {
            return R.string.joywork_executor
        }
    },
    SORT_CUSTOM(4, "自定义字段") {
        override fun stringId(): Int {
            // 该返回值无用
            return R.string.joywork_tabbar_title_team
        }
    },
    PRIORITY(5, "优先级") {
        override fun stringId(): Int {
            return R.string.joywork_update_priority
        }
    },
    UPDATE_TIME(6, "更新时间") {
        override fun stringId(): Int {
            // 该返回值无用
            return R.string.joywork_tabbar_title_team
        }
    },
    SOURCE(7, "待办来源") {
        override fun stringId(): Int {
            // 该返回值无用
            return R.string.joywork_tabbar_title_team
        }
    },
    PROJECT(8, "我的清单") {
        override fun stringId(): Int {
            return R.string.joywork_self_order
        }
    };

    abstract fun stringId(): Int;

    companion object {
        fun getInstance(code: Int): ProjectSortEnum {
            return values().firstOrNull {
                it.code == code
            } ?: SORT_NULL
        }
    }
}

enum class SelfListViewType(val code: Int) {
    MY_OWNER(3),
    MY_ASSIGN(4),
    MY_COOPERATE(5),
    MY_CREATE(6),
}

enum class SelfListSortType(val code: Int){
    MY_OWNER(5),
    MY_ASSIGN(6),
    MY_COOPERATE(7),
    MY_CREATE(-1),
}