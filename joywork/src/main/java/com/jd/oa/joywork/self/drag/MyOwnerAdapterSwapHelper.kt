package com.jd.oa.joywork.self.drag

import android.util.Log
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.move
import com.jd.oa.joywork.self.base.adapter.SelfBaseFragmentAdapter
import com.jd.oa.joywork.self.base.adapter.vh.ChildVH
import com.jd.oa.joywork.self.base.adapter.vh.GroupVH
import com.jd.oa.joywork.self.base.adapter.vh.LoadMoreVH
import com.jd.oa.joywork.self.base.adapter.vh.TimeVH

/**
 * 我的待办  我负责的  拖动时处理逻辑
 */
object MyOwnerAdapterSwapHelper {
    fun swap(
        fromVH: RecyclerView.ViewHolder,
        toVH: RecyclerView.ViewHolder,
        delta: Boolean,
        adapter: SelfBaseFragmentAdapter
    ) {
        if (fromVH.adapterPosition == toVH.adapterPosition)
            return

        if (fromVH.adapterPosition > toVH.adapterPosition && toVH is GroupVH) {
            // 上滑。只有 LoadMoreVH + GroupVH 情况时才不可交换
            val item = adapter.processor.getData()[toVH.adapterPosition - 1]
            if (item is LoadMoreJoyWork)
                return
        }
        val groups = adapter.groups
        adapter.swaped = true
        Log.e("TAG", "from = ${fromVH.adapterPosition},to = ${toVH.adapterPosition}")

        val fromItem = fromVH.itemView.getTag(R.id.jdme_tag_id) as JoyWork

        if (toVH is ChildVH || toVH is LoadMoreVH || toVH is TimeVH) { // 两个 item 交换
            val toItem = toVH.itemView.getTag(R.id.jdme_tag_id) as JoyWork
            var toIndex = toItem.expandableGroup.realItems.indexOf(toItem)
            val fromIndex = fromItem.expandableGroup.realItems.indexOf(fromItem)
            if (fromItem.expandableGroup == toItem.expandableGroup) {// 同组
                Log.e("TAG", "00 =$fromIndex, $toIndex")
                fromItem.expandableGroup.realItems.move(fromIndex, toIndex)
                Log.e(
                    "TAG",
                    "00 =${fromItem.expandableGroup.realItems.indexOf(fromItem)}, ${
                        toItem.expandableGroup.realItems.indexOf(toItem)
                    }"
                )
            } else {
//                if (toVH is TimeVH) toIndex++
                if (delta) toIndex++
                Log.e("TAG", "01 = $toIndex")
                fromItem.expandableGroup.realItems.remove(fromItem)
                fromItem.expandableGroup.title.count--
                if (toIndex > toItem.expandableGroup.realItems.size) {
                    toItem.expandableGroup.realItems.add(fromItem)
                } else {
                    toItem.expandableGroup.realItems.add(toIndex, fromItem)
                }
//                toItem.expandableGroup.realItems.add(toIndex, fromItem)
                toItem.expandableGroup.title.count++
            }
            toItem.expandableGroup.notifyItemChange()
        } else { // 分组标题。要么拖入，要么拖出
            val toItem = toVH.itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
            // 从旧组中删除
            fromItem.expandableGroup.realItems.remove(fromItem)
            fromItem.expandableGroup.title.count--

            if (fromItem.expandableGroup == toItem.expandableGroup) {
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                // 从组内拖到标题，理论上应该只是拖出。但鬼知道会不会有考虑不到的情况，弄个 if 判断吧
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，要拖出当前分组，添加到上一个分组
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    Log.e("TAG", "11 = 0")
                    toGroup.getRealItems().add(fromItem)
                    toGroup.getTitle().count++
                } else {
                    Log.e("TAG", "11 = 1")
                    // 理论上这里执行不到
                    // 往下拖，拖入当前分组
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    toGroup.realItems.add(0, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            } else {// 不同组，理论上应该是拖入
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，拖出。理论上这里执行不到
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    Log.e(
                        "TAG",
                        "2 fromIndex = ${toGroup.getRealItems().size}, group = ${toGroup.getTitle().title}"
                    )
                    toGroup.getRealItems().add(toGroup.getRealItems().size, fromItem)
                    toGroup.getTitle().count++
                } else {// 正常的拖入与展开后拖入
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    var insertIndex = 0
                    val r = kotlin.runCatching {
                        insertIndex = adapter.processor.getData()
                            .indexOf(toItem) - adapter.processor.getData()
                            .indexOf(toGroup.title)
                    }
                    r.exceptionOrNull()?.printStackTrace()
                    Log.e("TAG", "2 down = $insertIndex")
                    toGroup.realItems.add(insertIndex, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            }
        }
        adapter.processor.refreshData()
        adapter.notifyItemMoved(fromVH.adapterPosition, toVH.adapterPosition)
    }
}