package com.jd.oa.joywork;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.load.model.LazyHeaders;
import com.jd.oa.BaseActivity;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.PreferenceManager;
import com.yu.bundles.album.photoview.PhotoView;

import java.util.ArrayList;

public class JoyWorkImageActivity extends BaseActivity {
    public static final String EXTRA_IMAGE_INFO_LIST = "ImageInfoList";
    public static final String EXTRA_IMAGE_POS = "image_pos";
    private ArrayList<String> mImages;
    private int mCurrPos;

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.joywork_image);
        if (getActionBar() != null) {
            getActionBar().hide();
        }
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        this.mImages = this.getIntent().getStringArrayListExtra(EXTRA_IMAGE_INFO_LIST);
        if (this.mImages == null) {
            this.finish();
            return;
        }
        this.mCurrPos = this.getIntent().getIntExtra(EXTRA_IMAGE_POS, 0);
        this.initView();
    }

    private void initView() {
        ViewPager mPreviewViewPager = (ViewPager) this.findViewById(R.id.gallery_viewpager);
        JoyWorkImageActivity.PreviewPagerAdapter mPreviewPagerAdapter = new JoyWorkImageActivity.PreviewPagerAdapter();
        mPreviewViewPager.setAdapter(mPreviewPagerAdapter);
        mPreviewViewPager.setCurrentItem(this.mCurrPos);
        JoyWorkImageActivity.PreviewChangeListener mPreviewChangeListener = new JoyWorkImageActivity.PreviewChangeListener();
        mPreviewViewPager.addOnPageChangeListener(mPreviewChangeListener);
    }

    private class PreviewChangeListener implements ViewPager.OnPageChangeListener {
        private PreviewChangeListener() {
        }

        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        }

        public void onPageSelected(int position) {
            JoyWorkImageActivity.this.mCurrPos = position;
        }

        public void onPageScrollStateChanged(int state) {
        }
    }

    private class PreviewPagerAdapter extends PagerAdapter {
        private PreviewPagerAdapter() {
        }

        public int getCount() {
            return JoyWorkImageActivity.this.mImages == null ? 0 : JoyWorkImageActivity.this.mImages.size();
        }

        public boolean isViewFromObject(View view, Object object) {
            ImageView galleryPhotoView = (ImageView) view.findViewById(R.id.iv_show_image);
            if (galleryPhotoView instanceof PhotoView) {
                ((PhotoView) galleryPhotoView).setScale(1.0F);
            }
            return view == object;
        }

        public Object instantiateItem(ViewGroup container, final int position) {
            View galleryItemView = View.inflate(JoyWorkImageActivity.this.getApplicationContext(), R.layout.joywork_image_item, (ViewGroup) null);
            PhotoView galleryPhotoView = (PhotoView) galleryItemView.findViewById(R.id.iv_show_image);
            final View progressBar = galleryItemView.findViewById(R.id.progressbar);
            progressBar.setVisibility(View.GONE);
            final String imageInfo = JoyWorkImageActivity.this.mImages.get(position);
            GlideUrl glideUrl = new GlideUrl(imageInfo, new LazyHeaders.Builder()
                    .addHeader("x-token", TokenManager.getInstance().getAccessToken())
                    .addHeader("x-team-id", PreferenceManager.UserInfo.getTeamId())
                    .build());
            Glide.with(container.getContext()).asBitmap().load(glideUrl).into(galleryPhotoView);
            container.addView(galleryItemView);
            return galleryItemView;
        }

        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }
    }
}
