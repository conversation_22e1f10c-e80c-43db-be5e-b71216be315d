package com.jd.oa.joywork.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.jd.oa.joywork.R;

import java.util.ArrayList;
import java.util.List;

public class HorizontalPercentLinearLayout extends LinearLayout {
    private final List<Integer> mChildrenIndex = new ArrayList<>();

    public HorizontalPercentLinearLayout(Context context) {
        super(context);
        setOrientation(HORIZONTAL);
    }

    public HorizontalPercentLinearLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setOrientation(HORIZONTAL);
        final TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.HorizontalPercentLinearLayout);
        String s = a.getString(R.styleable.HorizontalPercentLinearLayout_childrenIndex);
        if (s != null && !s.isEmpty()) {
            String[] split = s.split(",");
            for (String s1 : split) {
                mChildrenIndex.add(Integer.parseInt(s1));
            }
        }
        a.recycle();
    }

    public HorizontalPercentLinearLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setOrientation(HORIZONTAL);
    }

    public HorizontalPercentLinearLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        setOrientation(HORIZONTAL);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int total = 0;
        for (int i = 0; i < getChildCount(); i++) {
            if (!mChildrenIndex.contains(i)) {
                View child = getChildAt(i);
                total += child.getMeasuredWidth();
            }
        }
        int max = (getMeasuredWidth() - total) / 2;
        for (Integer index : mChildrenIndex) {
            View child = getChildAt(index);
            child.measure(0, heightMeasureSpec);
            child.measure(MeasureSpec.makeMeasureSpec(max, MeasureSpec.AT_MOST), heightMeasureSpec);
        }
    }
}
