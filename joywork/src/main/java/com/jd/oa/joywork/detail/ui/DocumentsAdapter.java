package com.jd.oa.joywork.detail.ui;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.AsyncDifferConfig;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.databinding.TaskDocItemBinding;
import com.jd.oa.joywork.detail.data.entity.Documents;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailViewModel;

class DocumentsAdapter extends ListAdapter<Documents, DocumentsAdapter.DocumentsViewHolder> {

    @Nullable
    private final DocumentClickCallback documentClickCallback;
    private final TaskDetailViewModel taskDetailViewModel;

    DocumentsAdapter(TaskDetailViewModel taskDetailViewModel, @Nullable DocumentClickCallback documentClickCallback) {
        super(new AsyncDifferConfig.Builder<>(new DiffUtil.ItemCallback<Documents>() {
            @Override
            public boolean areItemsTheSame(@NonNull Documents old,
                                           @NonNull Documents comment) {
                return old.getDocumentId().equals(comment.getDocumentId());
            }

            @Override
            public boolean areContentsTheSame(@NonNull Documents old,
                                              @NonNull Documents comment) {
                return old.getDocumentId().equals(comment.getDocumentId());
            }
        }).build());
        this.documentClickCallback = documentClickCallback;
        this.taskDetailViewModel = taskDetailViewModel;
    }

    @Override
    @NonNull
    public DocumentsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TaskDocItemBinding binding = DataBindingUtil
                .inflate(LayoutInflater.from(parent.getContext()), R.layout.task_doc_item,
                        parent, false);
        binding.setCallback(documentClickCallback);
        return new DocumentsViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull DocumentsViewHolder holder, int position) {
        holder.binding.setDocuments(getItem(position));
        holder.binding.executePendingBindings();
        holder.binding.setDetail(taskDetailViewModel.getTaskDetail().getValue());
    }

    static class DocumentsViewHolder extends RecyclerView.ViewHolder {

        final TaskDocItemBinding binding;

        DocumentsViewHolder(TaskDocItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
