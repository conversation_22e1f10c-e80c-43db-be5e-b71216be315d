package com.jd.oa.joywork.bean;

import android.content.Context;
import android.widget.ImageView;

import androidx.annotation.StringRes;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.filter.JoyWorkViewItem;
import com.jd.oa.joywork.team.bean.ProjectGroupTypeEnum;

import java.io.Serializable;
import java.util.List;

public class JoyWorkProjectList implements Serializable {
    public int page;
    public int pageSize;
    public int total;
    public List<ListDTO> list;
    public List<ListDTO> preSetList;
    public List<ListDTO> sysSetList;

    public int totalSize() {
        int ans = 0;
        if (list != null) {
            ans += list.size();
        }
        if (preSetList != null) {
            ans += preSetList.size();
        }
        return ans;
    }

    public static final int TYPE_DIVIDER = -1;
    public static final int TYPE_EMPTY = -2; // 自定义清单为空时空界面
    public static final int TYPE_NORMAL = 1; // 正常清单
    public static final int TYPE_KPI = 2; // 绩效目标
    public static final int TYPE_OTHER = 3; // 其它

    public static class ListDTO implements Serializable {
        public static final String KPI_PROJECT_ID = "kpi_projectd_id";
        public String title;
        public List<String> permissions;
        public String projectId;
        public int status;
        public String desc;
        public List<GroupsDTO> groups;
        public Integer riskNums; // 风险待办数
        public Integer type = TYPE_NORMAL;
        public Integer unFinishedNums; // 待完成数量
        public String icon; // 图标

        public String getIconUrl() {
            return icon;
        }

        public boolean canDrag = true;

        public void icon(ImageView iv) {
            JoyWorkProjectList.icon(iv, type, icon);
        }

        public static class GroupsDTO implements Serializable {
            public String groupId;
            public String projectId;
            public String title;
            public int type;

            public String safeTitle(Context context) {
                if (title != null && !title.trim().isEmpty()) {
                    return title;
                }
                if (type == ProjectGroupTypeEnum.INITIAL.getCode()) {
                    return context.getResources().getString(R.string.joywork_task_link_untitled_group);
                }
                return context.getResources().getString(R.string.joywork_task_link_untitled_group);
            }
        }

    }

    public static void icon(ImageView iv, Integer type, String iconUrl) {
        if (type == JoyWorkProjectList.TYPE_NORMAL) {
            iv.setImageResource(R.drawable.joywork_project_icon);
        } else {
            JoyWorkViewItem.INSTANCE.img(
                    iv,
                    iconUrl,
                    R.drawable.joywork_project_icon
            );
        }
    }

    public static class DividerListDTO extends ListDTO {
        public int resId;

        public DividerListDTO(@StringRes int resId) {
            canDrag = false;
            type = TYPE_DIVIDER;
            this.resId = resId;
        }
    }

    public static class EmptyListDTO extends ListDTO {
        public int resId;

        public EmptyListDTO() {
            canDrag = false;
            type = TYPE_EMPTY;
        }
    }
}
