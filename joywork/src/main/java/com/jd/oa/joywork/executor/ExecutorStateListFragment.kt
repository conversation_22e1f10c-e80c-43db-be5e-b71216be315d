package com.jd.oa.joywork.executor

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.TransferResult
import com.jd.oa.joywork.detail.DialogManager
import com.jd.oa.joywork.detail.data.TaskDetailRepository
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.entity.DelMemberSend
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.Owner
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.detail.fromOwner
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.urge.JoyWorkUrgeList
import com.jd.oa.model.TenantCode
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.joywork_executor_state_frg.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class ExecutorStateListFragment : BaseFragment() {
    val action_finish = 1
    val action_remove = 2
    val action_tranfer = 3
    val action_remove_chief = 4
    val action_set_chief = 5

    private val scope = MainScope()

    companion object {
        val TYPE_FINISH = 1
        val TYPE_UNFINISH = 2
        fun getInstance(type: Int): ExecutorStateListFragment {
            val i = ExecutorStateListFragment()
            i.arguments = Bundle()
            i.requireArguments().putInt("type", type)
            return i
        }

        private fun ExecutorStateListFragment.isFinish(): Boolean {
            return arguments!!.getInt("type") == TYPE_FINISH
        }
    }

    private lateinit var mViewModel: ExecutorStateViewModel

    private val mUrgeClick = View.OnClickListener {
        JDMAUtils.onEventClick(JoyWorkConstant.OWNER_LIST_URGE, JoyWorkConstant.OWNER_LIST_URGE)
        JoyWorkUrgeList.urgeJoywork(
            requireActivity(),
            mViewModel.unfinishOwners,
            mViewModel.taskId ?: ""
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mViewModel =
            ViewModelProviders.of(requireActivity()).get(ExecutorStateViewModel::class.java)
        return inflater.inflate(R.layout.joywork_executor_state_frg, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        updateUrge()
        rv.apply {
            layoutManager = LinearLayoutManager(
                requireContext(),
                LinearLayoutManager.VERTICAL,
                false
            )
            val members =
                if (<EMAIL>()) mViewModel.finishOwners else mViewModel.unfinishOwners
            adapter = if (members.isEmpty()) {
                JoyWorkEmptyAdapter.empty(
                    requireContext(),
                    R.string.joywork_owner_empty,
                    R.drawable.joywork_executor_empty
                )
            } else {
                ExecutorsListAdapter(requireContext(), members)
            }
            getInfo()
        }

        if (isFinish()) {
            mViewModel.finishOwnerChange.observe(requireActivity()) {
                // 完成待办的执行人数量变化，只需要完成列表界面更新
                if (isFinish()) {
                    updateUI()
                }
            }
        } else {
            mViewModel.unfinishOwnerChange.observe(requireActivity()) {
                if (!isFinish()) {
                    updateUI()
                }
            }
        }

        mViewModel.permissionChangeLiveData.observe(requireActivity()) {
            if (!isFinish() && mViewModel.unfinishOwners.isLegalList() && mViewModel.canUrge()) {
                mUrgy.visible()
            }
            (rv.adapter as? ExecutorsListAdapter)?.notifyDataSetChanged()
        }
    }

    private fun updateUI() {
        updateUrge()
        val data = if (isFinish()) mViewModel.finishOwners else mViewModel.unfinishOwners
        if (rv.adapter is ExecutorsListAdapter) {
            // 原来显示的列表
            if (data.isLegalList()) {
                (rv.adapter as ExecutorsListAdapter).change(data)
            } else {
                showEmpty()
            }
        } else {
            // 原来显示的是空白界面
            if (data.isLegalList()) {
                showContent(data)
            }
        }
    }

    private fun updateUrge() {
        if (isFinish()) {
            // 已完成界面不需要催办
            mUrgy.gone()
        } else {
            if (mViewModel.unfinishOwners.isLegalList() && mViewModel.canUrge()) {
                mUrgy.visible()
            } else {
                mUrgy.gone()
            }
            mUrgy.setOnClickListener(mUrgeClick)
        }
    }

    private fun getInfo() {
        // 过滤掉已有部门信息的人员
        val result = if (isFinish()) mViewModel.finishOwners else mViewModel.unfinishOwners
        val needNet = ArrayList<Members>()
        result.forEach {
            if (listOf(it.departmentInfo?.deptName, it.titleName).hasNull()) {
                val m = Members()
                m.fromOwner(it)
                needNet.add(m)
            }
        }
        if (!needNet.isLegalList()) {
            return
        }
        scope.launch {
            val r = getBatchInfo(needNet)
            r.ifSuccessSilence {
                (rv?.adapter as? ExecutorsListAdapter)?.apply {
                    updateInfo(it?.users ?: ArrayList())
                }
            }
        }
    }

    private fun showContent(all: ArrayList<Owner>) {
        rv.adapter = ExecutorsListAdapter(requireContext(), all)
    }

    private fun getAction(member: Owner): List<Pair<String, Int>> {
        val list = mutableListOf<Pair<String, Int>>()
        if (mViewModel.canExChief()) {
            if (member.chief.isChief()) {
                // 取消负责人
                list.add(
                    Pair(
                        resources.getString(R.string.joywork_cancel_chief),
                        action_remove_chief
                    )
                )
            } else {
                // 设置负责人
                list.add(Pair(resources.getString(R.string.joywork_set_chief), action_set_chief))
            }
        }
        if (isFinish()) {
            if (mViewModel.canUnfinish()) {
                list.add(Pair(resources.getString(R.string.joywork_mark_unfinish), action_finish))
            }
        } else {
            if (mViewModel.canFinish()) {
                list.add(Pair(resources.getString(R.string.joywork_mark_finish), action_finish))
            }
        }
        if (member.isSelf() && mViewModel.canTransfer() && !isFinish()) {
            list.add(Pair(resources.getString(R.string.joywork_transfer), action_tranfer))
        }
        if (mViewModel.canRemove()) {
            if (member.chief.isChief()) {
                if (mViewModel.canExChief()) {
                    list.add(
                        Pair(
                            resources.getString(R.string.joywork_remove_owner),
                            action_remove
                        )
                    )
                }
                // [owner] is chief, but the user don't have permission to remove the chief, so do nothing
            } else {
                // only executor
                list.add(Pair(resources.getString(R.string.joywork_remove_owner), action_remove))
            }
        }
        return list
    }

    private fun showRemoveDialog(member: Owner) {
        val list = getAction(member)
        if (list.isLegalList()) {
            DialogManager.showAction(requireContext(), list, { i ->
                when (i.second) {
                    action_finish -> finishOrFinish(member, !isFinish())
                    action_remove -> removeNet(member)
                    action_tranfer -> transfer(member)
                    action_set_chief -> {
                        JDMAUtils.onEventClick(
                            JoyWorkConstant.DETAIL_CHIEF,
                            JoyWorkConstant.DETAIL_CHIEF
                        )
                        handleChief(member, false)
                    }

                    action_remove_chief -> handleChief(member, true)
                }
            }, { value ->
                if (value == action_remove) {
                    Color.parseColor("#FE3E33")
                } else {
                    Color.parseColor("#333333")
                }
            })
        }
    }

    private fun handleChief(member: Owner, isCancel: Boolean) {
        if (isCancel) {
            JoyWorkDialog.showAlertDialog(requireContext(), R.string.joywork_cancel_chief_tips) {
                it.dismiss()
                handleChiefNet(member, isCancel)
            }
        } else {
            val a = mViewModel.finishOwners.firstOrNull {
                it.chief.isChief()
            } ?: mViewModel.unfinishOwners.firstOrNull {
                it.chief.isChief()
            }
            if (a == null) {
                handleChiefNet(member, isCancel)
            } else {
                val msg = resources.getString(R.string.joywork_set_chief_tips, a.realName)
                JoyWorkDialog.showAlertDialog(requireContext(), msg) {
                    it.dismiss()
                    handleChiefNet(member, isCancel)
                }
            }
        }
        val clickId = if (isFinish()) {
            if(isCancel){
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED_MORE_CANCEL_RESPONSIBLE
            } else {
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED_MORE_SET_RESPONSIBLE
            }
        } else {
            if(isCancel){
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MORE_CANCEL_RESPONSIBLE
            } else {
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MORE_SET_RESPONSIBLE
            }
        }
        clickEvent {
            ClickEventParam(
                eventId = clickId
            )
        }
    }

    private fun handleChiefNet(member: Owner, isCancel: Boolean) {
        val m = Members()
        m.fromOwner(member)
        JoyWorkRepo.handleChief(
            mViewModel.taskId!!,
            m,
            isCancel
        ) { success: Boolean, errorMsg: String? ->
            if (success) {
                // 需要挪到位置，同时将别的人的负责人给取消
                (rv.adapter as? ExecutorsListAdapter)?.exChief(member, isCancel)
                if (member.isSelf()) {
                    updatePermission()
                }
            } else {
                ToastUtils.showInfoToast(errorMsg)
            }
        }
    }

    /**
     * 转派
     */
    private fun transfer(member: Owner) {
        val ls = ArrayList<MemberEntityJd>()
        mViewModel.unfinishOwners.forEach {
            val jd = MemberEntityJd()
            jd.mApp = it.app
            jd.mId = it.emplAccount
            ls.add(jd)
        }
        ExecutorUtils.selectTransfer(requireActivity(), ls, {
            if (mViewModel.taskId.isLegalString() && it.isLegalList()) {
                // 有 taskId
                val m = Members()
                m.fromDD(it!!.first()!!)
                JoyWorkRepo.transfer(
                    mViewModel.taskId!!,
                    m
                ) { success: Boolean, result: TransferResult?, errorMsg: String? ->
                    if (success && result != null) {
                        if (isFinish()) {
                            mViewModel.removeFinish(member)
                        } else {
                            mViewModel.removeUnFinish(member)
                        }
                        val o = Owner()
                        o.fromJoyWorkUser(result.member)
                        mViewModel.addUnFinish(listOf(o))
                        ToastUtils.showToast(R.string.joywork_transfer_success)
                    } else {
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }
            }
        }, {
            // failure
        })
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CHECKLIST_INFO_TRANS_OWNER
            )
        }
    }

    /**
     * @param finish: true 表示将未完成变成都完成; false 表示将完成变成未完成
     */
    private fun finishOrFinish(member: Owner, finish: Boolean) {
        if (!mViewModel.taskId.isLegalString()) {
            return
        }
        scope.launch {
            val status = if (finish) TaskStatusEnum.FINISH.code else TaskStatusEnum.UN_FINISH.code
            val m = Members()
            m.emplAccount = member.emplAccount
            m.ddAppId = member.ddAppId
            val result: CoroutineResult<String> = finishOrUnfinish(mViewModel.taskId!!, status, m)
            result.ifSuccessSilence {
                if (finish) {
                    mViewModel.removeUnFinish(member)
                    mViewModel.addFinish(listOf(member))
                } else {
                    mViewModel.removeFinish(member)
                    mViewModel.addUnFinish(listOf(member))
                }
                if (member.isSelf()) {
                    updatePermission()
                }
            }
        }
        val clickId =
            if (finish)
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MARK_FINISHED
            else
                JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_FINISHED_MORE_MARK_UNFINISHED
        clickEvent {
            ClickEventParam(
                eventId = clickId
            )
        }
    }

    // 移除协作人
    private fun removeNet(member: Owner) {
        if (mViewModel.taskId.isLegalString()) {
            val send = DelMemberSend()
            send.taskId = mViewModel.taskId
            val m = Members()
            if (TenantCode.getByDDAppId(member.ddAppId) != null) {
                m.ddAppId = member.ddAppId
                m.emplAccount = member.emplAccount
            } else {
                m.teamId = member.ddAppId
                m.userId = member.userId
            }
            m.userRole = TaskUserRole.OWNER.code
            send.members = listOf(m)
            TaskDetailRepository.getInstance()
                .delTaskMembers(send, object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        if (!hasError) {
                            if (isFinish()) {
                                mViewModel.removeFinish(member)
                            } else {
                                mViewModel.removeUnFinish(member)
                            }
                            if (member.isSelf()) {
                                // 删除自己，需要更新权限
                                updatePermission()
                            }
                        }
                    }
                })
        } else {
            if (isFinish()) {
                mViewModel.removeFinish(member)
            } else {
                mViewModel.removeUnFinish(member)
            }
        }

        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_TAB_UNFINISHED_MORE_TRANS_TASK
            )
        }
    }

    private fun updatePermission() {
        JoyWorkRepo.updatePermission(mViewModel.taskId) {
            var ps = if (it.isLegalList()) it!! else ArrayList<String>()
            ps = ps.filterNotNull()
            mViewModel.updatePermission(ps)
        }
    }

    fun showEmpty() {
        rv.adapter = JoyWorkEmptyAdapter.empty(
            requireContext(),
            R.string.joywork_relation_empty,
            R.drawable.joywork_executor_empty
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }

    private inner class ExecutorsListAdapter(val context: Context, dataC: ArrayList<Owner>) :
        RecyclerView.Adapter<VH>() {

        private val data = ArrayList<Owner>(dataC)

        private val listener = View.OnClickListener {
            val c = it.tag as Owner
            showRemoveDialog(c)
        }

        fun change(newData: List<Owner>) {
            data.clear()
            data.addAll(newData)
            notifyDataSetChanged()
        }

        fun exChief(member: Owner, isCancel: Boolean) {
            data.forEach {
                it.chief = 0
            }
            if (!isCancel) {
                member.chief = 1
            }
            notifyDataSetChanged()
        }

        fun updateInfo(members: List<Members>) {
            var ans = false
            members.forEach { ms ->
                data.firstOrNull {
                    it.emplAccount == ms.emplAccount && it.ddAppId == ms.ddAppId
                }?.apply {
                    departmentInfo = ms.deptInfo
                    titleName = ms.titleInfo?.titleName ?: ""
                    ans = true
                }
            }
            if (ans) {
                notifyDataSetChanged()
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
            val inflate: View = LayoutInflater.from(context)
                .inflate(R.layout.joywork_executor_state_frg_item, parent, false)
            return VH(inflate)
        }

        override fun getItemCount() = data.size

        override fun onBindViewHolder(holder: VH, position: Int) {
            val c = data[position]
            holder.name.text = c.realName ?: ""
            holder.department.text =
                listOf(c.departmentInfo?.deptName, c.titleName).joinLegalString()
            holder.divider.visibility = if (position == data.size - 1) View.GONE else View.VISIBLE
            holder.action.tag = c
            val action = getAction(c)
            if (action.isLegalList()) {
                holder.action.visible()
                holder.action.setOnClickListener(listener)
            } else {
                holder.action.gone()
            }
            JoyWorkViewItem.avatar(holder.avatar, c.headImg)
            holder.avatar.setTag(R.id.tag_key_data, c)
            holder.avatar.setOnClickListener {
                val m = it.getTag(R.id.tag_key_data) as Owner
                context.toSelfInfo(
                    m.ddAppId,
                    if (TenantCode.getByDDAppId(m.ddAppId) != null) {
                        m.emplAccount
                    } else {
                        m.userId
                    }
                )
            }
            if (c.chief.isChief()) {
                holder.hat.visible()
            } else {
                holder.hat.gone()
            }
            when {
                JoyWorkReadStatus.isRead(c.readStatus) -> {
                    holder.readStatus.visible()
                    holder.readStatus.setText(R.string.joywork_read)
                    holder.readStatus.argb("##666666")
                    holder.readStatus.background = DrawableEx.roundStrokeRect(
                        Color.parseColor("#CDCDCD"),
                        CommonUtils.dp2px(0.5f),
                        CommonUtils.dp2FloatPx(2)
                    )
                }

                JoyWorkReadStatus.unRead(c.readStatus) -> {
                    holder.readStatus.visible()
                    holder.readStatus.setText(R.string.joywork_unread)
                    holder.readStatus.argb("#4C7CFF")
                    holder.readStatus.background = DrawableEx.roundStrokeRect(
                        Color.parseColor("#4C7CFF"),
                        CommonUtils.dp2px(0.5f),
                        CommonUtils.dp2FloatPx(2)
                    )
                }

                else -> {
                    holder.readStatus.gone()
                }
            }
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView = view.findViewById(R.id.image)
        val name: TextView = view.findViewById(R.id.name)
        val action: TextView = view.findViewById(R.id.action)
        val department: TextView = view.findViewById(R.id.department)
        val divider: View = view.findViewById(R.id.divider)
        val readStatus: TextView = view.findViewById(R.id.readStatus)
        val hat: ImageView = view.findViewById(R.id.hat)
    }
}