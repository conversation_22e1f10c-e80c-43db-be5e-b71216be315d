package com.jd.oa.joywork.detail.ui

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import kotlinx.coroutines.CoroutineScope

class NotifyLinearLayout(context: Context, attrs: AttributeSet) : LinearLayout(context, attrs) {
    var childCall: OnClickListener? = null

    var childFinishCall: Runnable? = null

    var emptyCall: Runnable? = null

    var scope: CoroutineScope? = null
}