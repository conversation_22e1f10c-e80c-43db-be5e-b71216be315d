package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.FrameLayout
import android.widget.ListAdapter
import android.widget.ListView
import com.jd.oa.joywork.R
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitContentController
import com.jd.oa.ui.dialog.ShitDialogConfig
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.inflater

class LoadingListShitDialog(private val ctx: Context, private val listConfig: LoadingListShitDialogConfig) : BaseShitDialog(ctx, listConfig) {

    var itemClick: ((View, Int) -> Unit)? = null

    private val listController = object : IShitContentController {
        override fun inflate(parent: FrameLayout): View {
            val inflater = ctx.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val rv = inflater.inflate(R.layout.joywork_dialog_list_content, parent, false) as ListView
            rv.divider = null
            parent.addView(rv)
            return rv
        }
    }

    private var listView: ListView? = null

    override var contentController: IShitContentController
        get() = listController
        set(value) {}

    override fun setupCustomContentView(content: View) {
        (content as? ListView)?.apply {
            listView = content
            fillContent()
        }
        content.background = listConfig.contentBackground
    }

    var showContent: Boolean = false
        set(value) {
            if (value == field) {
                return
            }
            field = value
            fillContent()
        }

    private fun fillContent() {
        listView?.apply {
            if (showContent && listConfig.adapterCreator != null) {
                adapter = listConfig.adapterCreator!!.invoke(ctx)
                (adapter as? LoadingListShitDialogAdapter<*>)?.apply {
                    dialog = this@LoadingListShitDialog
                }
                setOnItemClickListener { parent, view, position, id ->
                    itemClick?.invoke(view, position)
                    dismiss()
                }
            } else {
                adapter = LoadingAdapter(ctx)
            }
        }
    }
}

class LoadingListShitDialogConfig : ShitDialogConfig() {
    var contentBackground = DrawableEx.solidRect(Color.WHITE)
    var showBottomDivider: Boolean = false

    var adapterCreator: ((Context) -> ListAdapter)? = null
}

private class LoadingAdapter(private val context: Context) : BaseAdapter() {
    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        return context.inflater.inflate(R.layout.joywork_dialog_list_item_loading, parent, false)
    }

    override fun getItem(position: Int): Any {
        return 1
    }

    override fun getItemId(position: Int): Long {
        return 1L
    }

    override fun getCount(): Int {
        return 1
    }
}

abstract class LoadingListShitDialogAdapter<T : Any>(protected val items: List<T>) : BaseAdapter() {

    var dialog: LoadingListShitDialog? = null

    override fun getItem(position: Int): Any {
        return items[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getCount() = items.size
}