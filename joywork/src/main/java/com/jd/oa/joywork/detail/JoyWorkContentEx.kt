package com.jd.oa.joywork.detail

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.net.Uri
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.data.entity.*
import com.jd.oa.joywork.team.dialog.CreateDialog
import com.jd.oa.joywork.team.kpi.KpiRepo
import com.jd.oa.joywork.team.kpi.selectChat
import com.jd.oa.joywork.team.kpi.shareResult
import com.jd.oa.model.TenantCode
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.JoyWorkShareBean
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.DateShowUtils

fun JoyWorkDetail.share(context: Activity) {
//    val judge = Objects.equals(ConfigurationManager.get().getEntry("work.task.share", "false"), "true")
    val judge = true // 本迭代删除 ducc 配置，默认全部执行新逻辑
    if (judge) {
        context.selectChat { users, groupIds, isGroup ->
            KpiRepo.shareTask(taskId, groupIds, users) { s, msg ->
                context.shareResult(s, msg, true) {
                    kotlin.runCatching {
                        val id = if (isGroup) {
                            groupIds.first()
                        } else {
                            users.first().emplAccount
                        }
                        context.openChat(id, isGroup)
                    }
                }
            }
        }
    } else {
        shareOld(context)
    }
}

fun Context.openChat(id: String, isGroup: Boolean) {
    val ddService = AppJoint.service(ImDdService::class.java)
    kotlin.runCatching {
        if (isGroup) {
            ddService.showGroupChattingActivity(this, id)
        } else {
            ddService.showChattingActivity(this, id)
        }
    }
}

private fun JoyWorkDetail.shareOld(activity: Activity) {
    activity.shareJoyWork(title, endTime, remark, taskId)
}

/**
 * 新建时将待办发送至咚咚会话
 */
fun JoyWorkDetail?.shareJoyWorkWhenCreate(
    to: String?,
    toApp: String?,
    gid: String?
) {
    if (this == null)
        return
    val imDdService = AppJoint.service(ImDdService::class.java)
    val bean = JoyWorkShareBean()
    val deeplink = Uri.parse(DeepLink.TASK_DETAIL_PAGE).buildUpon()
        .appendQueryParameter(
            DeepLink.DEEPLINK_PARAM,
            "{\"taskId\":\"$taskId\"}"
        )
        .build()
    val url = deeplink.toString()
    bean.taskTargetUrl = url

    bean.taskTitle = if (!title.isLegalString()) "" else title

    bean.taskDeadLine = endTime
    bean.taskDes = remark
    imDdService.sendJoyWorkCard(bean, to, toApp, gid)
}

fun Activity.shareJoyWork(title: String?, end: Long?, des: String?, taskId: String) {
    val imDdService = AppJoint.service(ImDdService::class.java)
    val bean = JoyWorkShareBean()
    val deeplink = Uri.parse(DeepLink.TASK_DETAIL_PAGE).buildUpon()
        .appendQueryParameter(
            DeepLink.DEEPLINK_PARAM,
            "{\"taskId\":\"$taskId\"}"
        )
        .build()
    val url = deeplink.toString()
    bean.taskTargetUrl = url

    bean.taskTitle = if (!title.isLegalString()) "" else title

    bean.taskDeadLine = end
    bean.taskDes = des
//    if (content.getOwner() != null) {
//        bean.taskManager = content.getOwner().getEmplAccount()
//        bean.taskManagerApp = content.getOwner().getDdAppId()
//        bean.taskManagerNickName = content.getOwner().getRealName()
//    }
    imDdService.shareJoyWork(this, bean)
}

/**
 * 是否可退出协作人
 */
fun JoyWorkDetail?.isExitable(): Boolean {
    return this?.run {
        !selfIsOwner() && selfIsExecutor() && !isDeleted()
    } ?: false
}

fun JoyWorkDetail?.isDup(): Boolean {
    return this?.run {
        endTime.isLegalLong() && cycle != null && DuplicateEnum.getByValue(cycle) != DuplicateEnum.NO
    } ?: false
}

/**
 * 是否可复制
 */
fun JoyWorkDetail?.isCopyable(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有合并权限
 */
fun JoyWorkDetail?.isMergeable(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.MERGE, enablePermission, permission)
    } ?: false
}

fun JoyWorkDetail?.isAddParent(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.ADD_PARENT, enablePermission, permission)
    } ?: false
}

/**
 * 是否已被删除
 */
fun JoyWorkDetail.isDeleted(): Boolean {
    return status != null && status == TaskStatusEnum.DELETED.code
}

/**
 * 是否有删除权限
 */
fun JoyWorkDetail.hasDeletable(): Boolean {
    return hasSpecialPermission(JoyWorkOp.DELETE, enablePermission, permission)
}

fun JoyWorkDetail?.isDeletable(): Boolean {
    return this != null && hasDeletable() && !isDeleted()
}

/**
 * 是否可还原待办
 */
fun JoyWorkDetail?.isRevertible(): Boolean {
    return this != null && hasDeletable() && isDeleted()
}

fun JoyWorkDetail?.isRemindable(): Boolean {
    if (this == null) return false
    return hasSpecialPermission(JoyWorkOp.URGE, enablePermission, permission)
}

/**
 * 返回起止时间对应的字符串
 */
fun JoyWorkDetail.startEndTime(resources: Resources): String {
    return CreateDialog.getTimeStr2(startTime, endTime, resources, null) ?: ""
}

fun Long?.launchTime(resources: Resources): String {
    return if (isLegalTimestamp()) {
        val ps = DateShowUtils.getTimeShowTextWithHourMinute(this!!) ?: ""
        resources.getString(R.string.me_joywork_plantime_normal, ps)
    } else {
        ""
    }
}

/**
 * 只有自己是执行人
 */
fun JoyWorkDetail?.onlySelfIsOwner(): Boolean {
    return this?.owners?.size == 1 && selfIsOwner()
}

/**
 * 返回当前登录用户是不是负责人(执行人)
 */
fun JoyWorkDetail?.selfIsOwner(): Boolean {
    return this?.run {
        owners.forEach {
            if (it.isSelf()) {
                return@run true
            }
        }
        false
    } ?: false
}

/**
 * 以执行人角色，判断是否完成了待办
 */
fun JoyWorkDetail?.selfIsOwnerFinish(): Boolean {
    val o = getSelfOwner()
    if (o == null) {
        return false
    } else {
        return TaskStatusEnum.isFinish(o.taskStatus)
    }
}

/**
 * 当前人对应的获取执行人
 */
fun JoyWorkDetail?.getSelfOwner(): Owner? {
    return this?.owners?.firstOrNull {
        it.isSelf()
    }
}

/**
 * 返回当前登录用户是不是创建者
 */
fun JoyWorkDetail?.selfIsCreator(): Boolean {
    return this?.run {
        creator?.emplAccount == PreferenceManager.UserInfo.getUserName()
    } ?: false
}

/**
 * 是否有修改优先级的权限
 */
fun JoyWorkDetail?.canUpdateLevel(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}


/**
 * 是否有修改截止时间权限
 */
fun JoyWorkDetail?.canUpdateDeadline(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改负责人权限
 */
fun JoyWorkDetail?.canUpdateChief(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.OWNER, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改负责人权限
 */
fun JoyWork?.canUpdateChief(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.OWNER, enablePermission, permission)
    } ?: false
}

/**
 * 是否可标记别的执行人完成
 */
fun JoyWorkDetail?.canMarkOtherFinish(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可标记别的执行人完成
 */
fun JoyWork?.canMarkOtherFinish(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, enablePermission, permission)
    } ?: false
}


/**
 * 是否可标记别的执行人未完成
 */
fun JoyWorkDetail?.canMarkOtherUnfinish(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可标记别的执行人未完成
 */
fun JoyWork?.canMarkOtherUnFinish(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改启动时间权限
 */
fun JoyWorkDetail?.canUpdateLaunchTime(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改重复待办权限
 */
fun JoyWorkDetail?.canUpdateDuplicate(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

fun JoyWorkDetail?.canUpdateRisk(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改截止时间权限
 */
fun JoyWork?.canUpdateDeadline(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改优先级的权限
 */
fun JoyWork?.canUpdatePriority(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可更新自定义字段
 */
fun JoyWorkDetail?.canUpdateMark(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可更新自定义字段
 */
fun JoyWork?.canUpdateMark(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改描述权限
 */
fun JoyWorkDetail?.canUpdateDes(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改附件权限
 */
fun JoyWorkDetail?.canUpdateFiles(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可修改关联清单
 */
fun JoyWorkDetail?.canUpdateGroup(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可以更新关联目标
 */
fun JoyWorkDetail?.canUpdateTarget(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否有评论权限
 */
fun JoyWorkDetail?.canComments(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.COMMENT, enablePermission, permission)
    } ?: false
}

/**
 * 是否有改变协同人权限
 */
fun JoyWorkDetail?.canUpdateRelations(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.EXECUTOR, enablePermission, permission)
    } ?: false
}

/**
 * 是否有修改标题权限
 */
fun JoyWorkDetail?.canUpdateTitle(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.UPDATE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可完成待办
 */
fun JoyWorkDetail?.canComplete(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.COMPLETE, enablePermission, permission)
    } ?: false
}

/**
 * 是否可修改提醒
 */
fun JoyWorkDetail?.canUpdateRemind(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.END_REMIND, enablePermission, permission)
    } ?: false
}

/**
 * 是否有转派权限
 */
fun JoyWorkDetail?.canTransfer(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.ASSIGN, enablePermission, permission)
    } ?: false
}

/**
 * 是否有转派权限
 * 有编辑权限，且待办未完成
 */
fun JoyWork?.canTransfer(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.ASSIGN, enablePermission, permission)
    } ?: false
}


/**
 * 是否已完成
 */
fun JoyWorkDetail?.isFinish(): Boolean {
    return this?.run {
        TaskStatusEnum.FINISH.isFinish(taskStatus);
    } ?: true
}

/**
 * 返回当前登录用户是否是协作人
 */
fun JoyWorkDetail?.selfIsExecutor(): Boolean {
    if (this == null)
        return false
    return (executors?.indexOfFirst {
        it.isSelf()
    } ?: -1) >= 0
}

/**
 * 是否已过期
 */
fun JoyWorkDetail?.outdate(): Boolean {
    return this?.run {
        endTime != null && endTime <= System.currentTimeMillis()
    } ?: false
}

/**
 * 没有起止时间
 */
fun JoyWorkDetail?.noStartEndTime(): Boolean {
    return this?.run {
        (startTime == null || startTime <= 0) && (endTime == null || endTime <= 0)
    } ?: true
}

/**
 * 没有启动时间
 */
fun JoyWorkDetail?.noLaunchTime(): Boolean {
    return this?.run {
        planTime == null || planTime < 0
    } ?: true
}

/**
 * 是否有子待办
 */
fun JoyWorkDetail?.hasSubJoyWork(defaultV: Boolean): Boolean {
    return this?.run {
        childWorks?.isNotEmpty()
    } ?: defaultV
}

/**
 * 是否可修改子待办。包括完成，删除，添加
 */
fun JoyWorkDetail?.canUpdateChildWorks(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.CHILD_TASK, enablePermission, permission)
    } ?: false
}

/**
 * 是否可催办子待办
 */
fun JoyWorkDetail?.canUrgeChildWorks(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.CHILD_TASK, enablePermission, permission)
    } ?: false
}

/**
 * 是否是第三方
 */
fun JoyWorkDetail?.isThird(): Boolean {
    return this?.thirdProcess ?: false
}

/**
 * 是否有 force_complete 权限
 */
fun JoyWorkDetail?.hasForceComplete(): Boolean {
    return this?.run {
        hasSpecialPermission(JoyWorkOp.FORCE_COMPLETE, enablePermission, permission)
    } ?: false
}

fun Members.fromDD(memberEntityJd: MemberEntityJd) {
    userId = memberEntityJd.mId
    emplAccount = memberEntityJd.mId
    teamId = JoyWorkUser.DEFAULT_TEAM_ID
    app = memberEntityJd.mApp
    ddAppId = memberEntityJd.mApp
    userRole = TaskUserRole.OWNER.code
    deptInfo = DeptInfo().apply {
        fullName = memberEntityJd.department
    }
    headImg = memberEntityJd.mAvatar
    position = memberEntityJd.position
    realName = memberEntityJd.mName
    chief = memberEntityJd.chief
    type = if (memberEntityJd.mType == 0) MemberEntityJd.TYPE_CONTACT else memberEntityJd.mType
}

fun MemberEntityJd.fromMembers(memberEntityJd: Members) {
    mId = memberEntityJd.emplAccount ?: memberEntityJd.userId
    mApp = memberEntityJd.ddAppId ?: memberEntityJd.app
    department = memberEntityJd.deptInfo?.fullName
    mAvatar = memberEntityJd.headImg
    position = memberEntityJd.position
    mName = memberEntityJd.realName
    titleName = memberEntityJd.titleName
    chief = memberEntityJd.chief
}

fun Members.toNetMap(): HashMap<String, String> {
    val tmp = HashMap<String, String>()
    if (TenantCode.getByDDAppId(ddAppId) == null && TenantCode.getByDDAppId(app) == null) {
        tmp["userId"] = userId
        tmp["teamId"] = ddAppId ?: JoyWorkUser.DEFAULT_TEAM_ID
    } else {
        if (emplAccount.isLegalString()) {
            tmp["emplAccount"] = emplAccount
            tmp["ddAppId"] = ddAppId ?: MultiAppConstant.APPID
        } else if (userId.isLegalString()) {
            tmp["userId"] = userId
            tmp["teamId"] = teamId
            tmp["app"] = app ?: MultiAppConstant.APPID
        }
    }
    return tmp
}

fun MemberEntityJd.isRobot(): Boolean {
    return "robot.dd" == app
}

fun JoyWorkUser.fromDD(memberEntityJd: MemberEntityJd) {
    userId = memberEntityJd.mId
    emplAccount = memberEntityJd.mId
    teamId = JoyWorkUser.DEFAULT_TEAM_ID
    app = memberEntityJd.mApp
    ddAppId = memberEntityJd.mApp
    deptInfo = DeptInfo().apply {
        fullName = memberEntityJd.department
    }
    headImg = memberEntityJd.mAvatar
    realName = memberEntityJd.mName
    titleName = memberEntityJd.titleName
    chief = memberEntityJd.chief
}

fun MemberEntityJd.fromUser(user: JoyWorkUser) {
    mId = user.emplAccount ?: user.userId
    mApp = user.ddAppId ?: user.app
    department = user.deptInfo?.fullName
    mAvatar = user.headImg
    mName = user.realName
    titleName = user.titleName
    chief = user.chief
    mType = MemberEntityJd.TYPE_CONTACT
}

fun Owner.fromDD(memberEntityJd: MemberEntityJd) {
    userId = memberEntityJd.mId
    emplAccount =memberEntityJd.mId
    chief = memberEntityJd.chief
    teamId = JoyWorkUser.DEFAULT_TEAM_ID
    app = memberEntityJd.mApp
    ddAppId = memberEntityJd.mApp
    departmentInfo = DeptInfo().apply {
        fullName = memberEntityJd.department
    }
    headImg = memberEntityJd.mAvatar
    realName = memberEntityJd.mName
}

fun JoyWorkUser.fromOwner(memberEntityJd: Owner) {
    userId = memberEntityJd.userId
    emplAccount = memberEntityJd.emplAccount
    teamId = JoyWorkUser.DEFAULT_TEAM_ID
    app = memberEntityJd.app
    ddAppId = memberEntityJd.ddAppId
    deptInfo = memberEntityJd.departmentInfo
    headImg = memberEntityJd.headImg
    realName = memberEntityJd.realName
    titleName = memberEntityJd.titleName
    taskId = memberEntityJd.taskId
    taskStatus = memberEntityJd.taskStatus
    chief = memberEntityJd.chief
}

fun Owner.fromJoyWorkUser(memberEntityJd: JoyWorkUser) {
    userId = memberEntityJd.userId
    emplAccount = memberEntityJd.emplAccount
    teamId = JoyWorkUser.DEFAULT_TEAM_ID
    app = memberEntityJd.app
    ddAppId = memberEntityJd.ddAppId
    departmentInfo = memberEntityJd.deptInfo
    headImg = memberEntityJd.headImg
    realName = memberEntityJd.realName
    titleName = memberEntityJd.titleName
    taskId = memberEntityJd.taskId
    taskStatus = memberEntityJd.taskStatus ?: TaskStatusEnum.UN_FINISH.code
    chief = memberEntityJd.chief
}

fun JoyWorkUser.key(): String {
    return "$emplAccount ${ddAppId ?: MultiAppConstant.APPID}"
}

fun MemberEntityJd.key(): String {
    return "$mId ${app ?: MultiAppConstant.APPID}"
}

fun Members.key(): String {
    return "$emplAccount ${ddAppId ?: MultiAppConstant.APPID}"
}

fun Owner.key(): String {
    return "$emplAccount ${ddAppId ?: MultiAppConstant.APPID}"
}

fun Members.fromJoyWorkUser(user: JoyWorkUser) {
    userId = user.userId
    emplAccount = user.emplAccount
    teamId = user.teamId
    app = user.app
    ddAppId = user.ddAppId
    if (user.deptInfo != null) {
        deptInfo = DeptInfo().apply {
            fullName = user.deptInfo.fullName
            deptName = user.deptInfo.deptName
        }
    }
    headImg = user.headImg
    realName = user.realName
    titleName = user.titleName
    chief = user.chief
}

fun Members.fromOwner(user: Owner) {
    userId = user.userId
    emplAccount = user.emplAccount
    teamId = user.teamId
    app = user.app
    ddAppId = user.ddAppId
    if (user.departmentInfo != null) {
        deptInfo = user.departmentInfo
    }
    headImg = user.headImg
    realName = user.realName
    chief = user.chief
}

fun JoyWorkUser.fromMembers(user: Members) {
    userId = user.userId
    emplAccount = user.emplAccount
    teamId = user.teamId
    app = user.app
    ddAppId = user.ddAppId
    if (user.deptInfo != null) {
        deptInfo = DeptInfo().apply {
            fullName = user.deptInfo.fullName
            deptName = user.deptInfo.deptName
        }
    }
    headImg = user.headImg
    realName = user.realName
    titleName = user.titleName
    chief = user.chief
}

fun JoyWorkDetail?.owners2Users(): ArrayList<JoyWorkUser> {
    val ans = ArrayList<JoyWorkUser>()
    if (this == null)
        return ans
    if (owners.isLegalList()) {
        owners.forEach { it ->
            val u = JoyWorkUser()
            u.fromOwner(it)
            ans.add(u)
        }
    }
    return ans
}


/**
 * 子待办是否完成
 */
fun ChildWorks.isFinish(): Boolean {
    return TaskStatusEnum.FINISH.isFinish(taskStatus)
}

fun Owner.isSamePerson(owner: Owner): Boolean {
    return owner.emplAccount == emplAccount && owner.ddAppId == ddAppId
}
