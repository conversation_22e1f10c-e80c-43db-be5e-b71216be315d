package com.jd.oa.joywork.team.kpi

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.around.base.HeaderFooterRecyclerAdapterWrapper
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.team.bean.DepartmentContent
import com.jd.oa.joywork.utils.JoyWorkLiveDataRepo
import com.jd.oa.utils.*
import java.util.*
import kotlin.collections.ArrayList

class DepartmentSearchActivity : BaseActivity() {
    companion object {
        fun start(context: Context, token: String) {
            val starter = Intent(context, DepartmentSearchActivity::class.java)
            starter.putExtra("token", token)
            context.startActivity(starter)
        }
    }

    private fun getViewModelToken(): String {
        return intent.getStringExtra("token") ?: ""
    }

    private val mHandler = Handler()
    private val mGetInfoRunnable = Runnable {
        search()
    }

    private var mRv: RecyclerView? = null
    private var mEt: EditText? = null
    private var mClear: View? = null
    private var liveData: VisibleUserLiveData? = null
    private val mHistories = HashMap<String, SearchInfo>()
    private var mPullUpLoadHelper: PullUpLoadHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.joywork_department_search)
        hideAction()
        liveData = JoyWorkLiveDataRepo.get(getViewModelToken()) as? VisibleUserLiveData
        liveData?.addUser(this)
        mRv = findViewById(R.id.rv_search)
        mRv?.vertical()
        showInitUI()
        findViewById<View>(R.id.tv_cancel).setOnClickListener {
            finish()
        }
        mClear = findViewById(R.id.mClear)
        mEt = findViewById(R.id.et_search)
        mEt?.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                val str = mEt?.string()
                mHandler.clear()
                if (!str.isLegalString()) {
                    showInitUI()
                    mClear?.gone()
                    return
                }
                mClear?.visible()
                mHandler.postDelayed(mGetInfoRunnable, 200)
            }
        })
        mClear?.setOnClickListener {
            mEt?.setText("")
        }
    }

    private fun showEmpty() {
        mRv?.adapter = JoyWorkEmptyAdapter.empty(
            this,
            R.string.joywork_department_empty,
            R.drawable.joywork_department_empty
        )
    }

    private fun showInitUI() {
        mRv?.adapter = JoyWorkEmptyAdapter.empty(
            this,
            R.string.joywork_department_init,
            R.drawable.joywork_department_empty
        )
    }

    private fun search() {
        val infoOuter = getCurrentInfo() ?: return
        infoOuter.firstSearch net@{ it, info ->
            // If the result can't match the current text, should ignore it!
            if (!Objects.equals(info.key, mEt?.string() ?: "")) {
                return@net
            }
            val new = it.depts ?: mutableListOf()
            if (new.isLegalList()) {
                val adapter = getRvAdapter()
                if (adapter != null) {
                    adapter.replace(new)
                } else {
                    mRv?.adapter = DepartmentSearchAdapter(this, new, { deptInfo ->
                        liveData?.deptLiveData?.value?.containIf(deptInfo) { a, b ->
                            Objects.equals(a.deptId, b.deptId)
                        } ?: false
                    }) {
                        false
                    }
                    getRvAdapter()?.itemClick = { itemInfo ->
                        val selected = liveData?.deptLiveData?.value?.containIf(itemInfo) { a, b ->
                            Objects.equals(a.deptId, b.deptId)
                        } ?: false
                        if (!selected) {
                            liveData?.appendDepts(mutableListOf(itemInfo))
                            finish()
                        }
                    }
//                   loadMore()
                }
                it.finishLoadMore()
            } else {
                showEmpty()
            }
        }
    }

    private fun getCurrentInfo(): SearchInfo? {
        val str = mEt?.string() ?: return null
        val info = mHistories[str] ?: SearchInfo(this, str)
        mHistories[str] = info
        return info
    }

    private fun loadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(mRv) {
            val info = getCurrentInfo() ?: return@PullUpLoadHelper
            info.loadMore { departmentContent, searchInfo ->
                // If the result can't match the current text, should ignore it!
                if (!Objects.equals(searchInfo.key, mEt?.string() ?: "")) {
                    return@loadMore
                }
                if (departmentContent == null) {
                    mPullUpLoadHelper?.setLoaded()
                } else {
                    departmentContent.finishLoadMore()
                    // If the result can't match the current text, should ignore it!
                    if (!Objects.equals(info.key, mEt?.string() ?: "")) {
                        return@loadMore
                    }
                    getRvAdapter()?.append(departmentContent.depts)
                }
            }
        }
    }

    private fun getRvAdapter(): DepartmentSearchAdapter? {
        val adapter = mRv?.adapter ?: return null
        if (adapter is HeaderFooterRecyclerAdapterWrapper) {
            return adapter.targetAdapter as? DepartmentSearchAdapter
        } else if (adapter is DepartmentSearchAdapter) {
            return adapter
        } else {
            return null
        }
    }

    private fun DepartmentContent.finishLoadMore() {
        if ((depts?.size ?: 0) >= 20) {
            mPullUpLoadHelper?.setLoaded()
        } else {
            mPullUpLoadHelper?.setComplete()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.clear()
    }

    private class SearchInfo(private val context: Context, val key: String) {

        val data = ArrayList<DeptInfo>()

        fun firstSearch(callback: (DepartmentContent, SearchInfo) -> Unit) {
            if (data.isLegalList()) {
                // already had cache, so go back immediately
                val cache = ArrayList(data)
                val ans = DepartmentContent()
                ans.depts = cache
                callback.invoke(ans, this)
            } else {
                KpiRepo.searchDepartments(key, 0) { ds, msg ->
                    if (ds != null) {
                        // cache
                        val ans = ds.depts ?: ArrayList()
                        ds.depts = ans
                        data.union(ds.depts) {
                            it.deptId
                        }
                        callback.invoke(ds, this)
                    } else {
                        Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                            .show()
                    }
                }
            }
        }

        fun loadMore(callback: (DepartmentContent?, SearchInfo) -> Unit) {
            KpiRepo.searchDepartments(key, data.size) { ds, msg ->
                if (ds != null && ds.depts.isLegalList()) {
                    data.union(ds.depts) {
                        it.deptId
                    }
                    callback.invoke(ds, this)
                } else {
                    Toast.makeText(context, JoyWorkEx.filterErrorMsg(msg), Toast.LENGTH_SHORT)
                        .show()
                    callback(null, this)
                }
            }
        }
    }
}