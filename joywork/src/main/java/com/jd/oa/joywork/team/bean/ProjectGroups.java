package com.jd.oa.joywork.team.bean;

import java.util.ArrayList;
import java.util.List;

public class ProjectGroups {
    public List<Group> groups;

    public List<Group> getSafeGroups() {
        if (groups == null) {
            groups = new ArrayList<>();
        }
        return groups;
    }

    public String title;
    public String desc;
    /**
     * assign-转让创建人
     * update-可编辑 标题&副标题
     * delete-可删除清单
     * member-可添加成员
     * group-可编辑分组
     * task-可在分组下操作待办
     */
    public ArrayList<String> permissions;// assign-转让创建人  update-可编辑 标题&副标题

    public List<String> getPermissions() {
        if (permissions == null) {
            groups = new ArrayList<>();
        }
        return permissions;
    }
}
