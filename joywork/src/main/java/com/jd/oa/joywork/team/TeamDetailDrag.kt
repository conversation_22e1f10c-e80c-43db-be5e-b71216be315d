package com.jd.oa.joywork.team

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.self.base.adapter.vh.LoadMoreVH
import com.jd.oa.joywork.team.TeamTitleVH.Companion.isInit

/**
 * 团队详情拖动
 */
class TeamDetailDrag(private val rv: RecyclerView) : ItemTouchHelper.Callback() {

    var canDrag = false

    private fun adapter(): TeamAdapter {
        return rv.adapter as TeamAdapter
    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        if (!canDrag) {
            return makeMovementFlags(0, 0)
        }
        if (viewHolder !is TeamJoyWorkVH) {
            return makeMovementFlags(0, 0)
        }
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        return makeMovementFlags(dragFlags, 0)
    }

    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        adapter().swap(viewHolder, target, viewHolder.adapterPosition < target.adapterPosition, false)
        return true
    }

    // 返回值表示 current 与 target 是否可交换
    // 2021年08月11日 留：
    //  目前 VH 一共有四种类型：ChildVH 表示普通的 Item；GroupVH 表示每一个他组的标题；LoadMoreVH 表示加载更多
    override fun canDropOver(recyclerView: RecyclerView, current: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        if (target !is Exchangeable) {
            return false
        }
        if (target is TeamTitleVH && !target.getJoyWorkTitle().expandableGroup.expand && target.getJoyWorkTitle().isInit()) {
            target.expandIfNecessary()
            return true
        }
        if (target.exchangeable) { // 普通的 Item，对应 ChildVH
            return true
        }
        // 不能超过边界
        val items = adapter().processor.getData()
        if (target.adapterPosition == 0) {
            return false
        }
        // 最后一个，可以交换
        if (target.adapterPosition >= items.size - 1) {
            return true
        }
        // 到这里 target 只可能是 GroupVH 与 LoadMoreVH

        // 加载更多。其上可能是 GroupVH,ChildVH; 其下只可能是 GroupVH
        if (target is LoadMoreVH) {
            // 只有上滑时才可与 LoadMoreVH 交换
            return current.adapterPosition > target.adapterPosition
        }

        // 分组标题。其上可能是 GroupVH,ChildVH,LoadMoreVH; 其下可能是 GroupVH,ChildVH,LoadMoreVH;
        if (target is TeamTitleVH) {
            return if (current.adapterPosition > target.adapterPosition) {
                // 上滑。只有 LoadMoreVH + GroupVH 情况时才不可交换。这一部分挪到 swap() 中判断
//                val item = items[target.adapterPosition - 1]
//                item !is LoadMoreJoyWork
                true
            } else {
                // 下滑，永远可以交换
                true
            }
        }
        return false
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        // 只有 item 可拖动
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && viewHolder != null && viewHolder::class.java == TeamJoyWorkVH::class.java) {
            (viewHolder as? TeamJoyWorkVH)?.onBeginDrag()
            return;
        }
        super.onSelectedChanged(viewHolder, actionState)
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        (viewHolder as? TeamJoyWorkVH)?.onEndDrag()
        super.clearView(recyclerView, viewHolder)
    }
}