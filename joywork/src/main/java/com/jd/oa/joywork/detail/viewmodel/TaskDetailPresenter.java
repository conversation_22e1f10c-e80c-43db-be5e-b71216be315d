package com.jd.oa.joywork.detail.viewmodel;

import static android.app.Activity.RESULT_OK;
import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.upload.UploadTask;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.js.hybrid.JSStoragePreference;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.utils.FileType;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.BlockTypeEnum;
import com.jd.oa.joywork.DuplicateEnum;
import com.jd.oa.joywork.JoyWorkActionEnum;
import com.jd.oa.joywork.JoyWorkAttachmentChoose;
import com.jd.oa.joywork.JoyWorkCommonConstant;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.joywork.JoyWorkDetailParam;
import com.jd.oa.joywork.JoyWorkDialog;
import com.jd.oa.joywork.JoyWorkEx;
import com.jd.oa.joywork.JoyWorkExKt;
import com.jd.oa.joywork.JoyWorkMediator;
import com.jd.oa.joywork.JoyWorkTaskSelector;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.RiskEnum;
import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork.bean.JoyWork;
import com.jd.oa.joywork.bean.KR;
import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.joywork.collaborator.CollaboratorListActivity;
import com.jd.oa.joywork.collaborator.OneTimeDataRepo;
import com.jd.oa.joywork.create.Value;
import com.jd.oa.joywork.detail.DialogManager;
import com.jd.oa.joywork.detail.JoyWorkContentExKt;
import com.jd.oa.joywork.detail.JoyWorkHistoryActivity;
import com.jd.oa.joywork.detail.TaskDetailFinishHelperKt;
import com.jd.oa.joywork.detail.data.TaskDetailRepository;
import com.jd.oa.joywork.detail.data.TaskDetailWebservice;
import com.jd.oa.joywork.detail.data.db.UpdateReporter;
import com.jd.oa.joywork.detail.data.entity.DelMemberSend;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.model.service.im.dd.entity.Members;
import com.jd.oa.joywork.detail.data.entity.MergeInfo;
import com.jd.oa.joywork.detail.data.entity.Owner;
import com.jd.oa.joywork.detail.data.entity.Resources;
import com.jd.oa.joywork.detail.data.entity.TaskDetailEntity;
import com.jd.oa.joywork.detail.data.entity.UpdateMemberSend;
import com.jd.oa.joywork.detail.ui.TaskDetailFragment;
import com.jd.oa.joywork.detail.ui.TaskUiUtils;
import com.jd.oa.joywork.detail.ui.des.JoyWorkDesActivity;
import com.jd.oa.joywork.detail.ui.risk.JoyWorkRiskActivity;
import com.jd.oa.joywork.dialog.JoyWorkAlertDialog;
import com.jd.oa.joywork.repo.JoyWorkAssignCallback;
import com.jd.oa.joywork.repo.JoyWorkRepo;
import com.jd.oa.joywork.repo.ResultWrapper;
import com.jd.oa.joywork.shortcut.ShortcutManager;
import com.jd.oa.joywork.team.ProjectConstant;
import com.jd.oa.joywork.team.ProjectDialogKt;
import com.jd.oa.joywork.team.SelectProjectTaskGroupActivity;
import com.jd.oa.joywork.team.kpi.SelectGoalActivity;
import com.jd.oa.joywork.urge.JoyWorkUrgeActivity;
import com.jd.oa.joywork.urge.JoyWorkUrgeList;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.ImageCompressUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.UtilKt;
import com.jd.oa.utils.Utils2Network;
import com.yu.bundles.album.MaeAlbum;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;

public class TaskDetailPresenter {
    //    public static boolean forTask;//这个用来兼容旧的逻辑，和flutter的分开JDF
    private static final int DEFAULT_MAX_NUM = 30;
    public static final String JOYSPACE_PRE = "https://joyspace-pre2.jd.com";
    public static final String JOYSPACE_TEST = "https://joyspace-test.jd.com";
    public static final String JOYSPACE_OFFICIAL = "https://joyspace.jd.com";
    private static final String JOY_SPACE_SELECTION = "/jdme/newplatform/doc-selection?primaryColor=0xffF0250F&max-num=" + DEFAULT_MAX_NUM + "&source=joyday";
    private static final int REQUEST_CODE_GALLERY = 1;
    private static final int REQUEST_CODE_CAPTURE = 2;
    private static final int DES_REQ = 100;
    private static final int RISK_REQ = 101;
    private static final int RELATION_REQ = 200;
    private static final int LINK_TASK_GROUP_REQ = 500;
    private static final int LINK_TASK_TARGET_REQ = 600;
    public static final int CHILD_TASK_REQ = 300;
    public static final int PARENT_TASK_REQ = 400;
    private String mCapturePath;
    private String actionSource;
    // 有可能为 null
    private final TaskDetailViewModel taskDetailViewModel;
    private final TaskDetailRepository mRepository;
    public UpdateRegionVM mUpdateRegionVM;
//    private Activity activity;

    public TaskDetailPresenter(TaskDetailViewModel taskDetailViewModel, String actionSource) {
        this.taskDetailViewModel = taskDetailViewModel;
        this.actionSource = actionSource;
        if (this.taskDetailViewModel != null) {
            mRepository = taskDetailViewModel.mRepository;
        } else {
            mRepository = null;
        }
    }

    /**
     * <b>仅提供给 ProjectChildVH 使用，其余使用者需要自己保证不会出问题</b>
     */
    public TaskDetailPresenter() {
        this(null, null);
    }

    public int canUpdateRelations(final TaskDetailEntity entity) {
        if (!JoyWorkContentExKt.canUpdateRelations(entity == null ? null : entity.getJoyWorkDetail())) {
            return View.GONE;
        } else {
            return View.VISIBLE;
        }
    }

    public void changeOwner(View view, final TaskDetailEntity taskDetailEntity) {
        final Activity activity = (Activity) view.getContext();
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || taskDetailEntity == null) {
            return;
        }
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_TRANSFORM, JoyWorkConstant.JOYWORK_DETAIL_TRANSFORM);
        TaskUiUtils.selectContacts(activity, 1, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> bean) {
                final UpdateMemberSend memberSend = new UpdateMemberSend();
                memberSend.setTaskId(taskDetailEntity.getJoyWorkDetail().getTaskId());
                List<Members> members = new ArrayList<>();
                for (MemberEntityJd memberEntityJd : bean) {
                    Members member = new Members();
                    JoyWorkContentExKt.fromDD(member, memberEntityJd);
                    members.add(member);
                }
                memberSend.setMembers(members);
                Handler handler = new Handler(Looper.getMainLooper());
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mRepository.updateMember(memberSend);
                    }
                }, 200);
            }

            @Override
            public void onFail() {
                ToastUtils.showToast(activity.getString(R.string.task_operate_fail));
            }
        });
    }

    @SuppressWarnings({"unused", "RedundantSuppression"})
    public void changeAllStatus(View view, final TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null) {
            return;
        }
        TaskDetailRepository.IActionFinishListener actionFinishListener = new TaskDetailRepository.IActionFinishListener() {
            @Override
            public void onFinished(boolean success) {
                if (success) {
                    mUpdateRegionVM.updateUiStatus();
                }
            }
        };
        if (JoyWorkContentExKt.isThird(taskDetailEntity.getJoyWorkDetail())) {
            TaskDetailBindingAdapter.jumpToSource(view.getContext(), taskDetailEntity);
        } else {
            if (!JoyWorkContentExKt.canComplete(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
                ToastUtils.showToast(R.string.joy_work_no_athority);
                return;
            }
            int type = TaskDetailFinishHelperKt.getDialogType(taskDetailEntity.getJoyWorkDetail());
            switch (type) {
                case TaskDetailFinishHelperKt.type_finish_all_alert:
                    ProjectDialogKt.showFinishAlertDialog(view.getContext(), true, true, new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            mRepository.taskUpdateStatus(taskDetailEntity.getJoyWorkDetail().getTaskId(), TaskStatusEnum.FINISH.getCode(), null, true, actionFinishListener);
                            return null;
                        }
                    });
                    break;
                case TaskDetailFinishHelperKt.type_finish_all_edit:
                    // 完成时提示
                    ProjectDialogKt.showFinishAlertDialog(view.getContext(), true, true, new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            mRepository.taskUpdateStatus(taskDetailEntity.getJoyWorkDetail().getTaskId(), TaskStatusEnum.FINISH.getCode(), null, true, actionFinishListener);
                            return null;
                        }
                    });
                    break;
                case TaskDetailFinishHelperKt.type_restore_all_alert:
                    ProjectDialogKt.showFinishAlertDialog(view.getContext(), false, true, new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            mRepository.taskUpdateStatus(taskDetailEntity.getJoyWorkDetail().getTaskId(), TaskStatusEnum.UN_FINISH.getCode(), null, true, actionFinishListener);
                            return null;
                        }
                    });
                    break;
            }

            final int taskStatus = taskDetailEntity.getJoyWorkDetail().getTaskStatus();
            if (taskStatus == TaskStatusEnum.FINISH.getCode()) {
                JDMAUtils.onEventClick(JoyWorkConstant.RESTORE_ALL, JoyWorkConstant.RESTORE_ALL);
                LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
            } else if (taskStatus == TaskStatusEnum.UN_FINISH.getCode()) {
                JDMAUtils.onEventClick(JoyWorkConstant.FINISH_ALL, JoyWorkConstant.FINISH_ALL);
                // 这里为了兼容工作台卡片刷新逻辑
                LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
            } else {
                ToastUtils.showToast(R.string.task_operate_fail);
            }
        }
    }

    @SuppressWarnings({"unused", "RedundantSuppression"})
    public void changeTaskStatus(View view, final TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null) {
            return;
        }
        final JoyWorkDetail detail = taskDetailEntity.getJoyWorkDetail();
        if (!JoyWorkContentExKt.canComplete(detail)) {
            ToastUtils.showToast(R.string.joy_work_no_athority);
            return;
        }
        int taskStatus = detail.getTaskStatus();
        if (!JoyWorkContentExKt.hasForceComplete(detail)) {
            // 不是创建者，只能是执行人。执行人完成/恢复待办时，只以自己的状态为准，不能取待办状态
            Owner self = JoyWorkContentExKt.getSelfOwner(detail);
            if (self != null) {
                taskStatus = self.taskStatus;
            }
        }
        if (taskStatus == TaskStatusEnum.FINISH.getCode()) {
            JDMAUtils.onEventClick(JoyWorkConstant.RESTORE_JOYWORK, JoyWorkConstant.RESTORE_JOYWORK);
            LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
        } else if (taskStatus == TaskStatusEnum.UN_FINISH.getCode()) {
            JDMAUtils.onEventClick(JoyWorkConstant.FINISH_JOYWORK, JoyWorkConstant.FINISH_JOYWORK);
            // 这里为了兼容工作台卡片刷新逻辑
            LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
        } else {
            ToastUtils.showToast(R.string.task_operate_fail);
            return;
        }
        TaskDetailRepository.IActionFinishListener actionFinishListener = new TaskDetailRepository.IActionFinishListener() {
            @Override
            public void onFinished(boolean success) {
                if (success) {
                    mUpdateRegionVM.updateUiStatus();
                }
            }
        };
        int type = TaskDetailFinishHelperKt.getDialogType(detail);
        switch (type) {
            case TaskDetailFinishHelperKt.type_finish_task_edit_no:
                // 完成时提示
                mRepository.taskUpdateStatus(taskDetailEntity.getJoyWorkDetail().getTaskId(), TaskStatusEnum.FINISH.getCode(), null, JoyWorkContentExKt.hasForceComplete(detail), actionFinishListener);
                break;
            case TaskDetailFinishHelperKt.type_finish_task_alert:
                ProjectDialogKt.showFinishAlertDialog(view.getContext(), true, false, new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        mRepository.taskUpdateStatus(detail.getTaskId(), TaskStatusEnum.FINISH.getCode(), null, JoyWorkContentExKt.hasForceComplete(detail), actionFinishListener);
                        return null;
                    }
                });
                break;
            case TaskDetailFinishHelperKt.type_finish_none:
                mRepository.taskUpdateStatus(detail.getTaskId(), TaskStatusEnum.FINISH.getCode(), null, JoyWorkContentExKt.hasForceComplete(detail), actionFinishListener);
                break;
            case TaskDetailFinishHelperKt.type_restore_task_alert:
                ProjectDialogKt.showFinishAlertDialog(view.getContext(), false, false, new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        mRepository.taskUpdateStatus(detail.getTaskId(), TaskStatusEnum.UN_FINISH.getCode(), null, true, actionFinishListener);
                        return null;
                    }
                });
                break;
            case TaskDetailFinishHelperKt.type_restore_none:
                mRepository.taskUpdateStatus(detail.getTaskId(), TaskStatusEnum.UN_FINISH.getCode(), null, JoyWorkContentExKt.hasForceComplete(detail), actionFinishListener);
                break;
        }
//
//        if (JoyWorkContentExKt.hasForceComplete(detail)) {
//            if (detail.owners == null || detail.owners.isEmpty()) {
//                // 创建人，且没有执行人时，不需要弹窗
//                mRepository.taskUpdateStatus(detail.getTaskId(), newStatus, null, true);
//            } else if (detail.owners.size() == 1 && JoyWorkContentExKt.selfIsOwner(detail)) {
//                // 仅自己是执行人也不需要弹窗
//                mRepository.taskUpdateStatus(detail.getTaskId(), newStatus, null, true);
//            } else {
//                // 创建人，有执行人时，需要弹窗
//                // 完成时提示
//                ProjectDialogKt.showTaskFinishDialog(view.getContext(), newStatus == TaskStatusEnum.FINISH.getCode(), new Function0<Unit>() {
//                    @Override
//                    public Unit invoke() {
//                        mRepository.taskUpdateStatus(detail.getTaskId(), newStatus, null, true);
//                        return null;
//                    }
//                });
//            }
//        } else {
//            // 不是创建人，只是以执行人身份完成
//            if (newStatus == TaskStatusEnum.FINISH.getCode() && !JoyWorkExKt.selfIsCreator(taskDetailEntity.getJoyWorkDetail().getCreator())) {
//                // 完成时提示
//                ProjectDialogKt.showFinishInfoDialog(view.getContext(), new Function1<String, Unit>() {
//                    @Override
//                    public Unit invoke(String s) {
//                        mRepository.taskUpdateStatus(taskDetailEntity.getJoyWorkDetail().getTaskId(), newStatus, s, false);
//                        return null;
//                    }
//                });
//            } else {
//                mRepository.taskUpdateStatus(taskDetailEntity.getJoyWorkDetail().getTaskId(), newStatus, null, false);
//            }
//        }
    }

    @SuppressWarnings({"unused", "RedundantSuppression"})
    public void onlyMe(final View view, final TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null) {
            return;
        }
        final JoyWorkDetail detail = taskDetailEntity.getJoyWorkDetail();
        Owner self = JoyWorkContentExKt.getSelfOwner(detail);
        if (self == null) {
            return;
        }
        final int taskStatus = self.taskStatus;
        final int newStatus;
        String content = null;
        TaskDetailRepository.IActionFinishListener actionFinishListener = new TaskDetailRepository.IActionFinishListener() {
            @Override
            public void onFinished(boolean success) {
                if (success) {
                    mUpdateRegionVM.updateUiStatus();
                }
            }
        };
        if (taskStatus == TaskStatusEnum.FINISH.getCode()) {
            JDMAUtils.onEventClick(JoyWorkConstant.RESTORE_ME, JoyWorkConstant.RESTORE_ME);
            newStatus = TaskStatusEnum.UN_FINISH.getCode();
            LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
        } else if (taskStatus == TaskStatusEnum.UN_FINISH.getCode()) {
            JDMAUtils.onEventClick(JoyWorkConstant.FINISH_ME, JoyWorkConstant.FINISH_ME);
            // 完成时提示
            // 这里为了兼容工作台卡片刷新逻辑
            LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
            mRepository.onlyMeFinish(detail.getTaskId(), TaskStatusEnum.FINISH.getCode(), null, actionFinishListener);
            return;
        } else {
            ToastUtils.showToast(R.string.task_operate_fail);
            return;
        }
        mRepository.onlyMeFinish(detail.getTaskId(), newStatus, content, actionFinishListener);
    }

    public void deadline(View view, final TaskDetailEntity taskDetailEntity) {
        deadline(view.getContext(), taskDetailEntity);
    }

    public void deadline(Context context, final TaskDetailEntity taskDetailEntity) {
        // 无权限
        if (!JoyWorkContentExKt.canUpdateDeadline(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        JoyWorkDetail detail = taskDetailEntity.getJoyWorkDetail();
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_DEADTIME, JoyWorkConstant.JOYWORK_DETAIL_DEADTIME);

        Value value = new Value();
        value.startTime = detail.getStartTime();
        value.endTime = detail.getEndTime();
        value.duplicateEnum = DuplicateEnum.Companion.getByValue(detail.cycle);
        value.mAlertType = detail.getAlertTypes();
        value.type = Value.TYPE_DEADLINE;
        new ShortcutManager(null, context).selectTime(value, new Function1<Boolean, Unit>() {
            @Override
            public Unit invoke(Boolean isCancelled) {
                updateDeadlineNet(value, taskDetailEntity);
                return null;
            }
        });
    }

    public void updateDeadlineNet(Value value, final TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null) return;
        JoyWorkDetail detail = taskDetailEntity.getJoyWorkDetail();

        Long oldStart = detail.getStartTime();
        Long oldEnd = detail.getEndTime();
        String oldRemind = detail.remindStr;
        Integer oldDup = detail.cycle;

        Long aLong = value.startTime;
        Long aLong2 = value.endTime;
        detail.setStartTime(aLong);
        detail.setEndTime(aLong2);
        if (aLong2 == null) {
            detail.cycle = null;
            detail.remindStr = AlertType.NO.getValue() + "";// 没有截止时间时，取消提醒设置
        } else {
            detail.remindStr = AlertType.Companion.valueToString(value.mAlertType);
            detail.cycle = value.duplicateEnum == null ? DuplicateEnum.NO.getValue() : value.duplicateEnum.getValue();
        }
        if (taskDetailViewModel != null) {
            taskDetailViewModel.updateTaskDetail(taskDetailEntity);
        }
        if (mRepository != null) {
            mRepository.updateEndTimeCycleAlert(detail.getTaskId(), aLong == null ? -1 : aLong, aLong2 == null ? -1 : aLong2, detail.cycle, detail.remindStr, actionSource, new TaskDetailWebservice.TaskCallback() {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    if (!hasError && mUpdateRegionVM != null) {
                        mUpdateRegionVM.notifyUpdate(oldStart, oldEnd, oldRemind, oldDup, detail);
                    }
                }
            });
        }
    }


    public void launchTime(View view, final TaskDetailEntity taskDetailEntity) {
        // 无权限
        if (!JoyWorkContentExKt.canUpdateLaunchTime(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_LAUNCH_TIME_CLICK, JoyWorkConstant.DETAIL_LAUNCH_TIME_CLICK);
        DialogManager.INSTANCE.showLaunchTimeDialog(taskDetailEntity.getJoyWorkDetail().getPlanTime(), taskDetailEntity.getJoyWorkDetail().getEndTime(), view.getContext(), new Function1<Long, Unit>() {
            @Override
            public Unit invoke(final Long aLong) {

                JoyWorkRepo.INSTANCE.assign(taskDetailEntity.getJoyWorkDetail().getTaskId(), android.text.format.DateUtils.isToday(aLong) ? BlockTypeEnum.TODAY : BlockTypeEnum.IN_PLAN, new JoyWorkAssignCallback() {
                    @Override
                    public void result(boolean success, @NonNull String errorMsg, @Nullable BlockTypeEnum target) {
                        if (success) {
                            taskDetailEntity.getJoyWorkDetail().setPlanTime(aLong);
                            taskDetailViewModel.updateTaskDetail(taskDetailEntity);
                            UpdateReporter.INSTANCE.reportUpdate();
                        } else {
                            ToastUtils.showToast(JoyWorkEx.INSTANCE.filterErrorMsg(errorMsg));
                        }
                    }

                    @Override
                    public void onStart() {

                    }
                }, aLong);
                return null;
            }
        });
    }

    public void risk(final View view, final TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null || !JoyWorkContentExKt.canUpdateRisk(taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_ISSUE_CLICK, JoyWorkConstant.DETAIL_ISSUE_CLICK);
        RiskEnum riskEnum = RiskEnum.Companion.valueByCode(taskDetailEntity.getJoyWorkDetail().riskStatus);
        DialogManager.INSTANCE.showRiskDialog(view.getContext(), riskEnum, new Function1<RiskEnum, Unit>() {
            @Override
            public Unit invoke(RiskEnum riskEnum) {
                if (riskEnum == null) {
                    return null;
                }
                if (Objects.equals(riskEnum.getCode(), taskDetailEntity.getJoyWorkDetail().riskStatus)) {
                    return null;
                }
                if (!riskEnum.getClickId().isEmpty()) {
                    JDMAUtils.onEventClick(riskEnum.getClickId(), riskEnum.getClickId());
                }
                UpdateReporter.INSTANCE.reportUpdate();
                if (riskEnum == RiskEnum.NO_SET) {
                    // 不需要输入描述
                    if (taskDetailViewModel != null) {
                        taskDetailViewModel.updateRisk(riskEnum, null, true, new Runnable() {
                            @Override
                            public void run() {
                                LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
                            }
                        });
                    }
                } else {
                    try {
                        Activity activity = (Activity) view.getContext();
                        Intent intent = new Intent(view.getContext(), JoyWorkRiskActivity.class);
                        intent.putExtra(JoyWorkRiskActivity.TYPE, riskEnum.getCode());
                        intent.putExtra(JoyWorkRiskActivity.ID, taskDetailEntity.getJoyWorkDetail().getTaskId());
                        activity.startActivityForResult(intent, RISK_REQ);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                return null;
            }
        });
    }

    public void duplicate(View view, final TaskDetailEntity taskDetailEntity) {
        // 无权限
        if (!JoyWorkContentExKt.canUpdateDuplicate(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        DuplicateEnum oldEnum = taskDetailEntity.getJoyWorkDetail().cycle == null ? null : DuplicateEnum.Companion.getByValue(taskDetailEntity.getJoyWorkDetail().cycle);
        JoyWorkDialog.INSTANCE.showDuplicateDialog(view.getContext(), oldEnum, new Function1<DuplicateEnum, Unit>() {
            @Override
            public Unit invoke(final DuplicateEnum duplicateEnum) {
                if (mRepository != null) {
                    mRepository.taskDuplicate(taskDetailEntity.getJoyWorkDetail().getTaskId(), duplicateEnum, new TaskDetailWebservice.TaskCallback() {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            if (!hasError) {
                                taskDetailEntity.getJoyWorkDetail().cycle = duplicateEnum.getValue();
                                if (taskDetailViewModel != null) {
                                    taskDetailViewModel.updateTaskDetail(taskDetailEntity);
                                }
                                if (mUpdateRegionVM != null) {
                                    mUpdateRegionVM.updateDup();
                                }
                            }
                        }
                    });
                }
                return null;
            }
        });
    }

    public void alert(View view, final TaskDetailEntity taskDetailEntity) {
        alert(view.getContext(), taskDetailEntity);
    }

    public void alert(Context context, final TaskDetailEntity taskDetailEntity) {
        boolean canUpdate = JoyWorkContentExKt.canUpdateRemind(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail());
        JoyWorkAlertDialog dialog = new JoyWorkAlertDialog(true, context, taskDetailEntity.getJoyWorkDetail().getAlertTypes(), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Set<AlertType> ts = (Set<AlertType>) v.getTag();
                final String oldRemind = taskDetailEntity.getJoyWorkDetail().remindStr;
                String newStr = AlertType.Companion.valueToString(ts);
                if (Objects.equals(oldRemind, newStr)) {
                    return;
                }
                taskDetailEntity.getJoyWorkDetail().remindStr = newStr;
                taskDetailViewModel.updateTaskDetail(taskDetailEntity);
                if (mRepository != null) {
                    mRepository.taskRemindTime(taskDetailEntity.getJoyWorkDetail().getTaskId(), taskDetailEntity.getJoyWorkDetail().remindStr, actionSource, new TaskDetailWebservice.TaskCallback() {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            if (!hasError) {
                                mUpdateRegionVM.updateRemind();
                            }
                        }
                    });
                }
            }
        });
        dialog.canUpdate = canUpdate;
        dialog.show();
//        DialogManager.INSTANCE.showAlertDialog(taskDetailEntity.getJoyWorkDetail().getRemindTime(), context, new Function1<Long, Unit>() {
//            @Override
//            public Unit invoke(Long aLong) {
//
//                return null;
//            }
//        });
    }

    public void priority(View view, final TaskDetailEntity taskDetailEntity) {
        // 没有修改优先级的权限，点击啥操作也不干
        if (!JoyWorkContentExKt.canUpdateLevel(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        JDMAUtils.clickEvent("", JoyWorkConstant.DETAIL_SET_PRIORITY, null);
        DialogManager.INSTANCE.showLevelDialog(view.getContext(), taskDetailEntity.getJoyWorkDetail().getPriorityType(), new Function1<Integer, Unit>() {
            @Override
            public Unit invoke(Integer integer) {
                taskDetailEntity.getJoyWorkDetail().setPriorityType(integer);
                taskDetailViewModel.updateTaskDetail(taskDetailEntity);
                mRepository.updatePriority(taskDetailEntity.getJoyWorkDetail().getTaskId(), integer, actionSource, new TaskDetailWebservice.TaskCallback() {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        if (!hasError && mUpdateRegionVM != null) {
                            mUpdateRegionVM.updatePriority();
                        }
                    }
                });
                return null;
            }
        }).show();
    }

    public void close(View view) {
        ((Activity) view.getContext()).finish();
    }

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    /**
     * 分享点击事件
     */
    public void share(View view, final TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null || taskDetailEntity.getContent() == null)
            return;
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_SHARE, JoyWorkConstant.JOYWORK_DETAIL_SHARE);
        final Activity activity = (Activity) view.getContext();
        final JoyWorkDetail content = taskDetailEntity.getJoyWorkDetail();
        JoyWorkContentExKt.share(content, activity);
    }

    public void selectJoySpace(View view, final TaskDetailEntity taskDetailEntity) {
        if (!JoyWorkContentExKt.canUpdateFiles(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        final Activity activity = (Activity) view.getContext();
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_JOYSPACE, JoyWorkConstant.JOYWORK_DETAIL_JOYSPACE);
//                        forTask = true;
        openDocumentChoose(activity);
    }


    public void more(final TaskDetailFragment fragment, final TaskDetailEntity taskDetailEntity, boolean fromTitle) {
        final Activity activity = fragment.getActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()
                || taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null) {
            return;
        }
        DialogManager.INSTANCE.showMoreAction(fromTitle, taskDetailEntity, activity, fragment, new Function1<JoyWorkActionEnum, Unit>() {
            @Override
            public Unit invoke(JoyWorkActionEnum joyWorkActionEnum) {
                boolean action = handleMoreAction(activity, joyWorkActionEnum, taskDetailEntity);
                if (!action) {
                    // 处理跟界面相关逻辑：添加界面元素
                    fragment.handleMoreAction(joyWorkActionEnum);
                }
                if(joyWorkActionEnum == JoyWorkActionEnum.HISTORY){
                    UtilKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_MORE_OPLOG);
                } else if(joyWorkActionEnum == JoyWorkActionEnum.DEL_TASK){
                    UtilKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_MORE_DELETE_TASK);
                } else if(joyWorkActionEnum == JoyWorkActionEnum.MERGE){
                    UtilKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_MORE_MERGE_TASKS);
                }
                return null;
            }
        });
    }

    public boolean handleMoreAction(final Activity activity, JoyWorkActionEnum joyWorkActionEnum, TaskDetailEntity taskDetailEntity) {
        // 两种处理逻辑，一种是跟 UI 相关，一种只单纯处理操作
        if (joyWorkActionEnum == JoyWorkActionEnum.DEL_TASK) {
            ProjectConstant.INSTANCE.sendBroadcast(activity, ProjectConstant.INSTANCE.getDEL_ACTION());
            mRepository.deleteTask(taskDetailEntity.getJoyWorkDetail().getTaskId(), actionSource, new TaskDetailWebservice.TaskCallback() {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    if (!hasError) {
                        JoyWorkDetail detail = taskDetailEntity.getJoyWorkDetail();
                        if (detail != null) {
                            detail.setStatus(TaskStatusEnum.DELETED.getCode());
                        }
                        mUpdateRegionVM.updateStatus();
                        activity.finish();
                    }
                }
            });
            return true;
        } else if (joyWorkActionEnum == JoyWorkActionEnum.EXIT) {
            String erp = PreferenceManager.UserInfo.getUserName();
            DelMemberSend delMemberSend = new DelMemberSend();
            delMemberSend.setTaskId(taskDetailEntity.getJoyWorkDetail().getTaskId());
            for (Members executor : taskDetailEntity.getJoyWorkDetail().getExecutors()) {
                if (erp.equals(executor.getEmplAccount())) {
                    List<Members> members = new ArrayList<>();
                    members.add(executor);
                    delMemberSend.setMembers(members);
                    mRepository.delTaskMembers(delMemberSend);
                    break;
                }
            }
            activity.finish();
            return true;
        } else if (joyWorkActionEnum == JoyWorkActionEnum.REVERSE_TASK) {
            DialogUtils.showLoadDialog(R.string.me_waiting);
            ProjectConstant.INSTANCE.sendBroadcast(activity, ProjectConstant.INSTANCE.getREVERSE_ACTION());
            mRepository.revertTask(taskDetailEntity.getJoyWorkDetail().getTaskId(), new TaskDetailWebservice.TaskCallback() {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    taskDetailViewModel.updateTaskDetailFromWeb();
                }
            });
            return true;
        } else if (joyWorkActionEnum == JoyWorkActionEnum.MERGE) {
            JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_MERGE, JoyWorkConstant.JOYWORK_MERGE);
            twoWorkAction(joyWorkActionEnum, taskDetailEntity.getJoyWorkDetail().getTaskId());
            return true;
        } else if (joyWorkActionEnum == JoyWorkActionEnum.MERGE_CANCEL) {
            JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_MERGE_CANCEL, JoyWorkConstant.JOYWORK_MERGE_CANCEL);
            twoWorkAction(joyWorkActionEnum, taskDetailEntity.getJoyWorkDetail().getTaskId());
            return true;
        } else if (joyWorkActionEnum == JoyWorkActionEnum.CHANGE_PARENT) {
            JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_CHANGE_PARENT, JoyWorkConstant.JOYWORK_CHANGE_PARENT);
            twoWorkAction(joyWorkActionEnum, taskDetailEntity.getJoyWorkDetail().getTaskId());
            return true;
        } else if (joyWorkActionEnum == JoyWorkActionEnum.CHILD) {
            JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_SUB, JoyWorkConstant.JOYWORK_SUB);
            twoWorkAction(joyWorkActionEnum, taskDetailEntity.getJoyWorkDetail().getTaskId());
            return true;
        } else if (joyWorkActionEnum == JoyWorkActionEnum.HISTORY) {
            JoyWorkHistoryActivity.Companion.start(activity, taskDetailEntity.getJoyWorkDetail().getTaskId());
            return true;
        }
        return false;
    }

    /**
     * 两个待办的操作：合并、取消合并、更改父待办、转为子待办
     */
    private void twoWorkAction(final JoyWorkActionEnum joyWorkActionEnum, final String from) {
        if (joyWorkActionEnum == JoyWorkActionEnum.MERGE_CANCEL) {
            // 取消合并不需要选择待办
            twoWorkActionNet(null, joyWorkActionEnum, from);
        } else {
            ArrayList<String> ids = new ArrayList<>();
            ids.add(from);
            JoyWorkTaskSelector.selectTask(true, true, ids, new JoyWorkTaskSelector.TaskSelectCallback() {
                @Override
                public void onTaskSelected(ArrayList tasks) {
                    // 2021年12月21日：此时返回的是一个 Map 对象
                    if (ObjExKt.isLegalList(tasks) && (tasks.get(0) instanceof Map)) {
                        Map o = (Map) tasks.get(0);
                        try {
                            twoWorkActionNet((String) o.get("taskId"), joyWorkActionEnum, from);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
        }
    }

    private void twoWorkActionNet(String to, JoyWorkActionEnum joyWorkActionEnum, final String from) {
        Function2<Boolean, ResultWrapper, Unit> callback = new Function2<Boolean, ResultWrapper, Unit>() {
            @Override
            public Unit invoke(Boolean aBoolean, ResultWrapper s) {
                if (aBoolean != null && aBoolean) {
                    UpdateReporter.INSTANCE.reportUpdate();
                    mRepository.updateFromWeb(from);
                } else {
                    // 2021年12月23日 由张鹏程(写 html 的) 定实现方案：提示文字由前端写死
                    // 证据：https://apijoyspace.jd.com/v1/files/c0BSsp7JR5bLLlCpWEpV/link
                    if ("1104933".equals(s.errorCode)) {
                        ToastUtils.showInfoToast(R.string.joywork_merge_special_tips);
                    } else {
                        ToastUtils.showInfoToast(s.errorMsg);
                    }
                }
                return null;
            }
        };
        if (joyWorkActionEnum == JoyWorkActionEnum.MERGE) {
            JoyWorkRepo.INSTANCE.merge(from, to, callback);
        } else if (joyWorkActionEnum == JoyWorkActionEnum.MERGE_CANCEL) {
            JoyWorkRepo.INSTANCE.cancelMerge(from, callback);
        } else if (joyWorkActionEnum == JoyWorkActionEnum.CHILD) {
            JoyWorkRepo.INSTANCE.addParentTask(to, from, callback);
        } else if (joyWorkActionEnum == JoyWorkActionEnum.CHANGE_PARENT) {
            JoyWorkRepo.INSTANCE.addParentTask(to, from, callback);
        }
    }

    public void urge(View view, TaskDetailEntity taskDetailEntity) {
        Activity activity = (Activity) view.getContext();
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || taskDetailEntity == null) {
            return;
        }
        JoyWork work = JoyWorkExKt.toJoyWork(taskDetailEntity.getJoyWorkDetail());
        if (work == null) {
            return;
        }
        JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_URGE, JoyWorkConstant.DETAIL_URGE);
        JoyWorkUrgeActivity.Companion.start(activity, work);
    }

    public void focus(View view, TaskDetailEntity taskDetailEntity) {
        Activity activity = (Activity) view.getContext();
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || taskDetailEntity == null) {
            return;
        }
        final JoyWorkDetail detail = taskDetailEntity.getJoyWorkDetail();
        if (detail == null)
            return;
        if (JoyWorkContentExKt.selfIsExecutor(detail)) {
            // 取消关注时，需弹窗提示
            ProjectDialogKt.showUnfouceDialog(activity, new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    JoyWorkRepo.INSTANCE.focusOrUnForce(detail.getTaskId(), !JoyWorkContentExKt.selfIsExecutor(detail), new Function2<String, Boolean, Unit>() {
                        @Override
                        public Unit invoke(String s, Boolean aBoolean) {
                            if (aBoolean != null && aBoolean) {
                                UpdateReporter.INSTANCE.reportUpdate();
                                mRepository.updateFromWeb(detail.getTaskId());
                            } else {
                                ToastUtils.showInfoToast(s);
                            }
                            return null;
                        }
                    });
                    return null;
                }
            });
        } else {
            // 关注或者取消关注
            JoyWorkRepo.INSTANCE.focusOrUnForce(detail.getTaskId(), !JoyWorkContentExKt.selfIsExecutor(detail), new Function2<String, Boolean, Unit>() {
                @Override
                public Unit invoke(String s, Boolean aBoolean) {
                    if (aBoolean != null && aBoolean) {
                        UpdateReporter.INSTANCE.reportUpdate();
                        mRepository.updateFromWeb(detail.getTaskId());
                        ToastUtils.showInfoToast(R.string.joywork_focus);
                    } else {
                        ToastUtils.showInfoToast(s);
                    }
                    return null;
                }
            });
        }
    }

    public void urgeSubjoywork(View view, TaskDetailEntity taskDetailEntity) {
        Activity activity = (Activity) view.getContext();
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || taskDetailEntity == null) {
            return;
        }
        JoyWorkUrgeList.Companion.urgeSubjoywork(activity, taskDetailEntity.getUnfinishSubjoywork());
//        JoyWorkUrgeActivity.Companion.urgeSubjoywork(activity, taskDetailEntity.getUnfinishSubjoywork());
    }

    /**
     * 添加协作人
     */
    public void addRelation(Activity activity, TaskDetailEntity taskDetailEntity, boolean canUpdate) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || taskDetailEntity == null) {
            return;
        }
        Intent intent = new Intent(activity, CollaboratorListActivity.class);
        CollaboratorListActivity.Companion.inflateIntent(intent, taskDetailEntity.getJoyWorkDetail().getTaskId(), canUpdate);
        List<Members> data = taskDetailEntity.getJoyWorkDetail().getExecutors();
        if (taskDetailEntity.getJoyWorkDetail().getExecutors() == null) {
            data = new ArrayList<>();
        }
        OneTimeDataRepo.INSTANCE.setData(data);
        activity.startActivityForResult(intent, RELATION_REQ);
    }

    public void addLinkTaskGroup(View view, TaskDetailEntity taskDetailEntity) {
        if (view.getContext() instanceof Activity) {
            selectGroup((Activity) view.getContext(), taskDetailEntity);
        }
    }

    public static void selectGroup(Activity activity, TaskDetailEntity taskDetailEntity) {
        if (!JoyWorkContentExKt.canUpdateFiles(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || taskDetailEntity == null) {
            return;
        }
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_LINK_TEAM, JoyWorkConstant.JOYWORK_DETAIL_LINK_TEAM);
        Intent intent = new Intent(activity, SelectProjectTaskGroupActivity.class);
        intent.putExtra("taskId", taskDetailEntity.getJoyWorkDetail().getTaskId());
        intent.putExtra("linkedProjects", taskDetailEntity.getJoyWorkDetail().getProjects());
//        intent.putExtra("taskDetailEntity", taskDetailEntity);
        activity.startActivityForResult(intent, LINK_TASK_GROUP_REQ);
    }

    public static void selectTarget(Activity activity, TaskDetailEntity taskDetailEntity) {
        if (!JoyWorkContentExKt.canUpdateTarget(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || taskDetailEntity == null) {
            return;
        }
        SelectGoalActivity.Companion.start(activity, LINK_TASK_TARGET_REQ);
    }

    public void showUserInfo(View view, String erp) {
        if (erp == null) {
            return;
        }
        AppBase.iAppBase.showContactDetailInfo(view.getContext(), erp);
    }

    /**
     * 合并待办
     */
    public void mergeInfo(View iv, TaskDetailEntity entity) {
        if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().mergeInfo == null || entity.getJoyWorkDetail().mergeInfo.taskId == null) {
            return;
        }
        MergeInfo parentTask = entity.getJoyWorkDetail().mergeInfo;
        JoyWorkDetailParam param = new JoyWorkDetailParam(parentTask.title, parentTask.taskId, null);
        param.setReqCode(TaskDetailPresenter.PARENT_TASK_REQ);
        param.setObj(UpdateReporter.INSTANCE.getParcel());
        JoyWorkMediator.Companion.goDetail(iv.getContext(), param);
    }

    /**
     * 解决风险
     */
    public void solveRisk(View view, TaskDetailEntity entity) {
        if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().riskStatus == null
                || entity.getJoyWorkDetail().riskContent == null || entity.getJoyWorkDetail().riskContent.isEmpty()) {
            return;
        }
        try {
            if (entity.getJoyWorkDetail().riskStatus == RiskEnum.RISK.getCode()) {
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_RISK_DETAIL_RESOLVE, JoyWorkConstant.DETAIL_RISK_DETAIL_RESOLVE);
            } else if (entity.getJoyWorkDetail().riskStatus == RiskEnum.PROBLEM.getCode()) {
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_PROBLEM_DETAIL_RESOLVE, JoyWorkConstant.DETAIL_PROBLEM_DETAIL_RESOLVE);
            }

            Activity activity = (Activity) view.getContext();
            Intent intent = new Intent(view.getContext(), JoyWorkRiskActivity.class);
            // 解决风险就是将风险设置为正常
            intent.putExtra(JoyWorkRiskActivity.TYPE, RiskEnum.NORMAL.getCode());
            intent.putExtra(JoyWorkRiskActivity.IS_RESOLVE, true); // 是否是解决问题
            intent.putExtra(JoyWorkRiskActivity.ID, entity.getJoyWorkDetail().getTaskId());
            activity.startActivityForResult(intent, RISK_REQ);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新描述
     */
    public void updateRisk(View view, TaskDetailEntity entity) {
        if (entity == null || entity.getJoyWorkDetail() == null || entity.getJoyWorkDetail().riskStatus == null
                || entity.getJoyWorkDetail().riskContent == null || entity.getJoyWorkDetail().riskContent.isEmpty()) {
            return;
        }
        try {
            if (entity.getJoyWorkDetail().riskStatus == RiskEnum.RISK.getCode()) {
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_RISK_DETAIL_UPDATE, JoyWorkConstant.DETAIL_RISK_DETAIL_UPDATE);
            } else if (entity.getJoyWorkDetail().riskStatus == RiskEnum.PROBLEM.getCode()) {
                JDMAUtils.onEventClick(JoyWorkConstant.DETAIL_PROBLEM_DETAIL_UPDATE, JoyWorkConstant.DETAIL_PROBLEM_DETAIL_UPDATE);
            }
            Activity activity = (Activity) view.getContext();
            Intent intent = new Intent(view.getContext(), JoyWorkRiskActivity.class);
            // 解决风险就是将风险设置为正常
            intent.putExtra(JoyWorkRiskActivity.TYPE, entity.getJoyWorkDetail().riskStatus);
            intent.putExtra(JoyWorkRiskActivity.IS_RESOLVE, false); // 是否是解决问题
            intent.putExtra(JoyWorkRiskActivity.KEY, entity.getJoyWorkDetail().riskContent);
            intent.putExtra(JoyWorkRiskActivity.ID, entity.getJoyWorkDetail().getTaskId());
            activity.startActivityForResult(intent, RISK_REQ);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addOwner(final Activity activity, final TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null)
            return;
        TaskUiUtils.selectContacts(activity, 1, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> bean) {
                UpdateMemberSend memberSend = new UpdateMemberSend();
                memberSend.setTaskId(taskDetailEntity.getJoyWorkDetail().getTaskId());
                List<Members> members = new ArrayList<>();
                for (MemberEntityJd memberEntityJd : bean) {
                    Members member = new Members();
                    JoyWorkContentExKt.fromDD(member, memberEntityJd);
                    members.add(member);
                }
                memberSend.setMembers(members);
                mRepository.updateMember(memberSend);
            }

            @Override
            public void onFail() {
            }
        });
    }

    public void upload(View view) {
        final Activity activity = (Activity) view.getContext();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        DialogManager.INSTANCE.showFileChoose(view.getContext(), new Function1<JoyWorkAttachmentChoose, Unit>() {
            @Override
            public Unit invoke(JoyWorkAttachmentChoose joyWorkAttachmentChoose) {
                switch (joyWorkAttachmentChoose) {
                    case TAKE_PHOTO:
                        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_TAKE_PHOTO, JoyWorkConstant.JOYWORK_DETAIL_TAKE_PHOTO);
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                PermissionHelper.requestPermissions(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal), activity.getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal),
                                        new RequestPermissionCallback() {
                                            @Override
                                            public void allGranted() {
                                                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                                                File file = new File(FileCache.getInstance().getImageCacheFile(), "capture_rn" + System.currentTimeMillis() + ".jpg");
                                                Uri mCaptureUri = CategoriesKt.getFileUri(AppBase.getAppContext(), file);
                                                mCapturePath = file.getAbsolutePath();
                                                intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUri);
                                                activity.startActivityForResult(intent, REQUEST_CODE_CAPTURE);
                                            }

                                            @Override
                                            public void denied(List<String> deniedList) {
                                                ToastUtils.showToast(R.string.task_operate_fail);
                                            }
                                        }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                            }
                        });
                        break;
                    case ALBUM:
                        PermissionHelper.requestPermissions(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),
                                activity.getResources().getString(com.jme.common.R.string.me_request_permission_read_storage_gallery),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_IMAGE, JoyWorkConstant.JOYWORK_DETAIL_IMAGE);
                                        JdmeRounter.getProvider(GalleryProvider.class).openGallery(activity, DEFAULT_MAX_NUM, REQUEST_CODE_GALLERY);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                        ToastUtils.showToast(R.string.task_operate_fail);
                                    }
                                }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                        break;
                    case JOY_SPACE:
                        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_JOYSPACE, JoyWorkConstant.JOYWORK_DETAIL_JOYSPACE);
//                        forTask = true;
                        openDocumentChoose(activity);
                        break;
                }
                return null;
            }
        });
    }

    public void onActivityResult(@NonNull final Activity activity, int requestCode, int resultCode, @Nullable Intent data) {
        if (requestCode == DES_REQ && resultCode == RESULT_OK && data != null) {
            String extra = data.getStringExtra(JoyWorkDesActivity.Companion.getKey());
            taskDetailViewModel.updateRemark(extra, false);
            if (mUpdateRegionVM != null) {
                mUpdateRegionVM.updateDesc();
            }
            return;
        }
        if (requestCode == RISK_REQ && resultCode == RESULT_OK && data != null) {
            String extra = data.getStringExtra(JoyWorkRiskActivity.Companion.getKey());
            int code = data.getIntExtra(JoyWorkRiskActivity.Companion.getTypeKey(), RiskEnum.NORMAL.getCode());
            RiskEnum riskEnum = RiskEnum.Companion.valueByCode(code);
            taskDetailViewModel.updateRisk(riskEnum, extra, false, new Runnable() {
                @Override
                public void run() {
                    LocalBroadcastManager.getInstance(activity).sendBroadcast(new Intent(JoyWorkCommonConstant.REFRESH_UPDATE_RISK));
                }
            });
            return;
        }
        if (requestCode == RELATION_REQ && resultCode == RESULT_OK) {
            try {
                List<Members> members = (List<Members>) OneTimeDataRepo.INSTANCE.getData();
                taskDetailViewModel.changeRelation(members);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }
        if (requestCode == CHILD_TASK_REQ && resultCode == RESULT_OK) {
            // 从子待办来。父待办打开子待办，然后子待办返回
            UpdateReporter.INSTANCE.reportUpdate();
            taskDetailViewModel.updateTaskDetailFromWeb();
            return;
        }
        if (requestCode == PARENT_TASK_REQ && resultCode == RESULT_OK) {
            // 子待办打开父待办，由父待办返回
            UpdateReporter.INSTANCE.reportUpdate();
            taskDetailViewModel.updateTaskDetailFromWeb();
            return;
        }
        if (requestCode == REQUEST_CODE_GALLERY) {
            UpdateReporter.INSTANCE.reportUpdate();
            if (resultCode == RESULT_OK) {
                final List<String> pList = MaeAlbum.obtainPathResult(data);
                saveImageFileList(activity, pList);
            }
        } else if (requestCode == REQUEST_CODE_CAPTURE) {
            UpdateReporter.INSTANCE.reportUpdate();
            if (resultCode == RESULT_OK) {
                if (mCapturePath == null) {
                    return;
                }
                List<String> pList = new ArrayList<>();
                pList.add(mCapturePath);
                saveImageFileList(activity, pList);

            }
        } else if (requestCode == LINK_TASK_GROUP_REQ && resultCode == 200) {
            //            关联成功 刷新页面
            UpdateReporter.INSTANCE.reportUpdate();
            taskDetailViewModel.updateTaskDetailFromWeb();
        } else if (requestCode == LINK_TASK_TARGET_REQ && resultCode == RESULT_OK) {
            Parcelable target = SelectGoalActivity.Companion.getResultData(data);
            String id = null;
            if (target instanceof KpiTarget) {
                id = ((KpiTarget) target).goalId;
            } else if (target instanceof KR) {
                id = ((KR) target).krId;
            }
            if (id != null) {
                // 待办详情中处理关联目标与 kr
                TaskDetailEntity value = taskDetailViewModel.getTaskDetail().getValue();
                boolean had = false;
                if (value != null && value.getJoyWorkDetail() != null) {
                    List<KpiTarget> t = value.getJoyWorkDetail().goals == null ? new ArrayList<KpiTarget>() : value.getJoyWorkDetail().goals;
                    for (KpiTarget goal : t) {
                        if (Objects.equals(goal.goalId, id)) {
                            had = true;
                            break;
                        }
                    }
                    List<KR> krs = value.getJoyWorkDetail().krs == null ? new ArrayList<KR>() : value.getJoyWorkDetail().krs;
                    for (KR goal : krs) {
                        if (Objects.equals(goal.krId, id)) {
                            had = true;
                            break;
                        }
                    }
                }
                if (had) {
                    return;
                }
                if (target instanceof KpiTarget) {
                    taskDetailViewModel.updateTargets((KpiTarget) target, new Runnable() {
                        @Override
                        public void run() {
                            UpdateReporter.INSTANCE.reportUpdate();
                            taskDetailViewModel.updateTaskDetailFromWeb();
                        }
                    });
                } else {
                    taskDetailViewModel.updateKr((KR) target, new Runnable() {
                        @Override
                        public void run() {
                            UpdateReporter.INSTANCE.reportUpdate();
                            taskDetailViewModel.updateTaskDetailFromWeb();
                        }
                    });
                }
            } else {
                ToastUtils.showToast(activity.getString(R.string.joywork_task_link_error));
            }
        }
    }

    private UploadTask uploadTask;

    private void saveImageFileList(@NonNull final Activity activity, final List<String> pList) {
        if (pList != null && pList.size() > 0) {
            File parent = new File(activity.getExternalCacheDir() + "/rn");
            DialogUtils.showLoadDialog((FragmentActivity) activity, activity.getResources().getString(R.string.me_uploading), new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    if (uploadTask != null) {
                        uploadTask.cancel();
                        ToastUtils.showToast(activity.getResources().getString(R.string.me_cancel_ok));
                    }
                }
            });
            int i = 0;
            final ImageCompressUtils mGallery = ImageCompressUtils.INSTANCE;
            for (String p : pList) {
                final int index = i++;
                final Resources resources = new Resources();
                mGallery.compressAsync(p, new File(parent, System.currentTimeMillis() + ".jpg").getAbsolutePath(), new Function1<String, Unit>() {
                    @Override
                    public Unit invoke(final String s) {
                        Task.Callback<UploadResult> callback = new Task.Callback<UploadResult>() {
                            @Override
                            public void onStart() {
//                                Log.d("TAG123", "onStart,thread: " + Thread.currentThread().getName());
                            }

                            @Override
                            public void onProgressChange(Task.Progress progress) {
//                                Log.d("TAG123", "onProgressChange,thread: " + Thread.currentThread().getName() + ",progress: " + progress.getPercent());
//                                        mPbProgress.setProgress(progress.getPercent());
                            }

                            @Override
                            public void onPause() {
//                                Log.d("TAG123", "onPause, thread: " + Thread.currentThread().getName());
                            }

                            @Override
                            public void onComplete(UploadResult result) {
                                resources.setUrl(result.getFileDownloadUrl());
                                taskDetailViewModel.addResource(resources, new Utils2Network.Callback() {
                                    @Override
                                    public void call(boolean isSuccess) {
                                        if (index >= pList.size() - 1) {
                                            uploadTask = null;
                                            DialogUtils.removeLoadDialog((FragmentActivity) activity);
                                        }
                                    }
                                });
                            }

                            @Override
                            public void onFailure(Exception exception) {
//                                Log.e("TAG123", "onFailure,thread: " + Thread.currentThread().getName(), exception);
                                if (index >= pList.size() - 1) {
                                    DialogUtils.removeLoadDialog((FragmentActivity) activity);
                                    mGallery.reset();
                                    ToastUtils.showToast(R.string.joywork_upload_failure);
                                }
                            }
                        };
                        File file = new File(s);
                        if (file.exists()) {
                            resources.setBizType(1);
                            resources.setName(file.getName());
                            resources.setSize(file.length());
                            Random random = new Random();
                            resources.setBizId(String.valueOf(random.nextLong()));
                            resources.setFileType(FileType.getMimeType(file.getName()));
                            resources.setResourceType(1);
                            uploadTask = FileUploadManager.getDefault(activity)
                                    .create(s)
                                    .setAppKey(ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.JOYWORK))
                                    .setNeedAuthN(1)
                                    .setCallback(callback)
                                    .start();
                        }
                        return null;
                    }
                });
            }
        }
    }

    public int getLikeIcon(TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null) {
            return R.string.icon_general_like;
        }
        List<Members> praises = taskDetailEntity.getJoyWorkDetail().getTaskPraises();
        if (praises != null) {
            String erp = PreferenceManager.UserInfo.getUserName();
            for (Members members : praises) {
                if (erp.equals(members.getEmplAccount())) {
                    return R.string.icon_padding_like;
                }
            }
        }
        return R.string.icon_general_like;
    }

    public String getLikeTip(TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null) {
            return AppBase.getAppContext().getString(R.string.joy_work_like);
        }
        int total = taskDetailEntity.getJoyWorkDetail().getTotal();
        if (total > 4) {
            return AppBase.getAppContext().getString(R.string.joy_work_like_2, total);
        } else if (total == 0) {
            UtilKt.clickEvent(JoyWorkConstant.MOBILE_EVENT_TASK_TASKDETAILS_LIKES);
            return AppBase.getAppContext().getString(R.string.joy_work_like);
        }
        List<Members> praises = taskDetailEntity.getJoyWorkDetail().getTaskPraises();
        if (praises != null) {
            String erp = PreferenceManager.UserInfo.getUserName();
            for (Members members : praises) {
                if (erp.equals(members.getEmplAccount())) {
                    if (total > 1 && total <= 4) {
                        return AppBase.getAppContext().getString(R.string.joy_work_like_4);
                    } else if (total == 1) {
                        return AppBase.getAppContext().getString(R.string.joy_work_like_3);
                    }
                }
            }
        }
        return AppBase.getAppContext().getString(R.string.joy_work_like_2, total);
    }

    @SuppressWarnings({"unused", "RedundantSuppression"})
    public void switchLike(@NonNull View view, TaskDetailEntity taskDetailEntity) {
        if (taskDetailEntity == null || taskDetailEntity.getJoyWorkDetail() == null) {
            return;
        }
        List<Members> praises = taskDetailEntity.getJoyWorkDetail().getTaskPraises();
        if (praises == null) {
            praises = new ArrayList<>();
        }
        int index = 0;
        int find = -1;
        String erp = PreferenceManager.UserInfo.getUserName();
        for (Members members : praises) {
            if (erp.equals(members.getEmplAccount())) {
                find = index;
                break;
            }
            index++;
        }
        if (find >= 0) {
            taskDetailViewModel.cancelTaskPraise();
        } else {
            taskDetailViewModel.taskPraise();
        }
    }

    //打开joy space文档选择器
    public static void openDocumentChoose(@NonNull Activity activity) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        Map<String, String> param = new HashMap<>();
        param.put("x-token", TokenManager.getInstance().getAccessToken());
        param.put("x-team-id", PreferenceManager.UserInfo.getTenantCode());
        param.put("x-client", "ANDROID");
        param.put("x-app", imDdService.getAppID());
        Gson gson = new Gson();
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        String jsonStr = gson.toJson(param, type);
        String val = "{\"obj\":" + jsonStr + "}";
        JSStoragePreference.getInstance().put("joySpaceValue", val);
        WebBean webBean = new WebBean();
        if (GatewayNetEnvironment.getCurrentEnv().isPre()) {
            webBean.setUrl(JOYSPACE_PRE + JOY_SPACE_SELECTION);
        } else {
            webBean.setUrl(JOYSPACE_OFFICIAL + JOY_SPACE_SELECTION);
        }
        webBean.setShowNav(WebConfig.H5_NATIVE_HEAD_HIDE);
        Intent intent = new Intent(activity, FunctionActivity.class);
        intent.putExtra(EXTRA_WEB_BEAN, webBean);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
        activity.startActivity(intent);
    }

    private boolean mHyperClicked = false;

    public void joyworkDes(View view, TaskDetailEntity taskDetailEntity) {
        // 没有修改优先级的权限，点击啥操作也不干
        if (!JoyWorkContentExKt.canUpdateDes(taskDetailEntity == null ? null : taskDetailEntity.getJoyWorkDetail())) {
            return;
        }
        if (mHyperClicked) {
            mHyperClicked = false;
            return;
        }
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_DES, JoyWorkConstant.JOYWORK_DETAIL_DES);
        try {
            Activity activity = (Activity) view.getContext();
            Intent intent = new Intent(view.getContext(), JoyWorkDesActivity.class);
            intent.putExtra(JoyWorkDesActivity.KEY, taskDetailEntity.getJoyWorkDetail().getRemark());
            intent.putExtra(JoyWorkDesActivity.ID, taskDetailEntity.getJoyWorkDetail().getTaskId());
            activity.startActivityForResult(intent, DES_REQ);
//            taskDetailViewModel.updateTaskDetail(taskDetailEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onHyperLinkClick(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        mHyperClicked = true;
        OpennessApi.openUrl(url, false);
    }
}
