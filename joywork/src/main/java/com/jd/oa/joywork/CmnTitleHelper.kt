package com.jd.oa.joywork

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.jd.oa.BaseActivity

object CmnTitleHelper {
    fun handleTitle(activity: BaseActivity, titleRes: Int, actionRes: Int, parent: ViewGroup, callback: ((View, TextView, TextView) -> Unit)? = null) {
        val title = parent.findViewById<TextView>(R.id.header_title).apply {
            setText(titleRes)
        }
        val save = parent.findViewById<TextView>(R.id.header_save).apply {
            setText(actionRes)
        }
        val backView = parent.findViewById<View>(R.id.header_back)
        backView.setOnClickListener {
            activity.finish()
        }
        callback?.invoke(backView, title, save)
    }
}