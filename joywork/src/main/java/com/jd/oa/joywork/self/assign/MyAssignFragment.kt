package com.jd.oa.joywork.self.assign

import com.jd.oa.joywork.*
import com.jd.oa.joywork.self.base.SelfListBaseFragment
import com.jd.oa.joywork.self.base.SelfPageEnum
import com.jd.oa.joywork.filter.FilterPageIdFactory
import com.jd.oa.joywork.self.SelfBaseItf
import com.jd.oa.joywork.team.bean.SelfListSortType
import com.jd.oa.joywork.team.bean.SelfListViewType
import com.jd.oa.utils.JDMAUtils

/**
 * 我指派的
 */
class MyAssignFragment : SelfListBaseFragment(), SelfBaseItf {

    override fun getPagePVName(): String? {
        return FilterPageIdFactory.getPageId(getUserRole(), TaskStatusEnum.UN_FINISH)?.apply {
            JDMAUtils.onEventPagePV(requireActivity(), this, this)
        }
    }

    override fun getUserRole(): TaskUserRole {
        return TaskUserRole.ASSIGN
    }

    override fun onPageUnselected() {
    }

    override fun onPageSelected() {
    }

    override fun onShowAtTabbar() {
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return TaskListTypeEnum.ASSIGN
    }

    override fun handleSortActions(list: MutableList<ProjectSortAction>) {
        list.add(0, ProjectSortOwner(requireContext()))
    }

    override fun getPageId(): String {
        return "MyAssign"
    }

    override fun getViewType(): Int {
        return SelfListViewType.MY_ASSIGN.code
    }

    override fun getSortType(): Int {
        return SelfListSortType.MY_ASSIGN.code
    }
}