package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.R
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.utils.JoyWorkLiveDataRepo
import com.jd.oa.utils.vertical

class VisibleDepartmentsListActivity : BaseActivity() {
    companion object {
        fun start(token: String, activity: Activity) {
            val i = Intent(activity, VisibleDepartmentsListActivity::class.java)
            i.putExtra("token", token)
            activity.startActivity(i)
        }
    }

    private fun getViewModelToken(): String {
        return intent.getStringExtra("token") ?: ""
    }

    private var liveData: VisibleUserLiveData? = null
    private var mRv: RecyclerView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        setContentView(R.layout.joywork_visible_departments_list)
        mRv = findViewById(R.id.mContentView)
        mRv?.vertical()
        findViewById<View>(R.id.mBack).setOnClickListener { finish() }
        liveData = JoyWorkLiveDataRepo.get(getViewModelToken()) as? VisibleUserLiveData
        liveData?.addUser(this)
        liveData?.deptLiveData?.observe(this) {
            if (!it.isLegalList()) {
                showEmpty()
            } else {
                val adapter = mRv?.adapter as? DepartmentSearchAdapter
                if (adapter != null) {
                    adapter.replace(it)
                } else {
                    setAdapter(it)
                }
            }
        }
        findViewById<View>(R.id.mAdd).setOnClickListener {
            DepartmentSearchActivity.start(this, getViewModelToken())
        }
        val data = liveData?.deptLiveData?.value ?: mutableListOf()
        if (data.isLegalList()) {
            setAdapter(data)
        } else {
            showEmpty()
        }
    }

    private fun setAdapter(data: List<DeptInfo>) {
        val adapter = DepartmentSearchAdapter(this, data, {
            false
        }) {
            true
        }
        adapter.deleteCallback = {
            delDept(it)
        }
        mRv?.adapter = adapter
    }

    private fun delDept(info: DeptInfo) {
        liveData?.removeDept(info)
    }

    private fun showEmpty() {
        mRv?.adapter = JoyWorkEmptyAdapter.empty(
            this,
            R.string.joywork_department_empty,
            R.drawable.joywork_department_empty
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        liveData?.removeUser(this)
    }
}