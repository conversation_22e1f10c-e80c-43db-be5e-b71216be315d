package com.jd.oa.joywork.self.strategy.selflist

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.self.base.SelfListBaseAdapter
import com.jd.oa.joywork.self.base.SelfListBaseAdapterItf
import com.jd.oa.joywork.self.base.SelfListViewModel
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.team.view.HRecyclerView

interface StrategyBaseItf {
    fun getUserRole(): TaskUserRole

    fun getRv(): HRecyclerView?
}

/**
 * We can use coroutines to separate data processing from the UI layer
 */
abstract class StrategyBase(
    val next: StrategyBase?,
    protected val mItf: StrategyBaseItf,
    protected val context: FragmentActivity,
    protected val mSortFilterStatusViewModel: TeamDetailViewModel,
    protected val mViewModel: SelfListViewModel
) {
    abstract fun canHandle(): Boolean

    /**
     * If you override this method and it returns true, you must also override [onLoadMore]
     */
    abstract fun needLoadMore(): Boolean

    fun loadMore(offset: Int, userRole: TaskUserRole) {
        if (canHandle()) {
            onLoadMore(offset, userRole)
        } else {
            next?.loadMore(offset, userRole)
        }
    }

    protected open fun onLoadMore(offset: Int, userRole: TaskUserRole) {
    }

    fun getList(userRole: TaskUserRole) {
        if (canHandle()) {
            onGetList(userRole)
        } else {
            next?.getList(userRole)
        }
    }

    protected abstract fun onGetList(userRole: TaskUserRole)

    fun handleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return if (canHandle()) {
            onHandleData(any)
        } else {
            next?.handleData(any) ?: ArrayList()
        }
    }

    protected abstract fun onHandleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>

    fun createAdapter(
        data: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        itf: SelfListBaseAdapterItf,
        fragment: Fragment,
        recyclerView: HRecyclerView?
    ): SelfListBaseAdapter {
        return if (canHandle()) {
            onCreateAdapter(data, itf, fragment, recyclerView)
        } else {
            next?.createAdapter(data, itf, fragment, recyclerView) ?: onCreateAdapter(
                data,
                itf,
                fragment,
                recyclerView
            )
        }
    }

    protected open fun onCreateAdapter(
        data: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        itf: SelfListBaseAdapterItf,
        fragment: Fragment,
        recyclerView: HRecyclerView?
    ): SelfListBaseAdapter {
        return SelfListBaseAdapter(
            fragment,
            itf,
            data,
            recyclerView,
            context
        )
    }
}