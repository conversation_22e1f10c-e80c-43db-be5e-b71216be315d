package com.jd.oa.joywork.detail.data.entity.custom;

import java.util.List;

public class CustomFieldGroupOuter {
    // 表示所有自定义字段
    public List<CustomFieldGroup> customFields;

    public CustomFieldItem getDetailItem(String cid, String did) {
        if (customFields == null || customFields.isEmpty()) {
            return null;
        }
        for (CustomFieldGroup group : customFields) {
            if (group.columnId.equals(cid)) {
                if (group.details == null || group.details.isEmpty()) {
                    return null;
                }
                for (CustomFieldItem item : group.details) {
                    if (item.detailId.equals(did)) {
                        return item;
                    }
                }
                return null;
            }
        }
        return null;
    }
}
