package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.text.SpannableString
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Space
import android.widget.TextView
import android.widget.Toast
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.DelAction
import com.jd.oa.joywork.EditAction
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkJDMA
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.self.base.adapter.vh.ItemVHItf
import com.jd.oa.joywork.team.KREditActivity
import com.jd.oa.joywork.team.bean.VisibleSetting
import com.jd.oa.joywork.team.view.DividerLinearLayout
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.argb
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.isVisible
import com.jd.oa.utils.visible
import java.util.Objects

class KpiTitleVH(
    private val context: Activity, parent: ViewGroup,
    private val adapter: GoalListAdapter
) : RecyclerView.ViewHolder(
    context.inflater.inflate(
        R.layout.joywork_kpi_item_title,
        parent,
        false
    )
), Exchangeable {
    override var exchangeable: Boolean = false

    //    private val mTagContainer = itemView.findViewById<LinearLayout>(R.id.mTagContainer)
    private val joyworkCount = itemView.findViewById<TextView>(R.id.joyworkCount)
    private val summaryCount = itemView.findViewById<TextView>(R.id.summaryCount)
    private val commentCount = itemView.findViewById<TextView>(R.id.commentCount)
    private val mIcon = itemView.findViewById<TextView>(R.id.mIcon)
    private val mIconContainer = itemView.findViewById<View>(R.id.mIconContainer)
    private val mTitle = itemView.findViewById<TextView>(R.id.mTitle)
    private val mTitleContainer = itemView.findViewById<ViewGroup>(R.id.mTitleContainer)
    private val mActionContainer = itemView.findViewById<ViewGroup>(R.id.mActionContainer)

    //    private val mHandle = itemView.findViewById<TextView>(R.id.mHandle)
    private val mMore = itemView.findViewById<TextView>(R.id.mMore)
    private val mDivider = itemView.findViewById<View>(R.id.mDivider)

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            unregisterVisibleSetting()
            val i = intent ?: return
            val visibleType = i.getIntExtra("visibleType", -1)
            if (visibleType == -1) {
                return
            }
            val goalId = i.getStringExtra("goalId")
            if (!goalId.isLegalString()) {
                return
            }
            adapter.updateVisibleType(goalId!!, visibleType)
        }
    }

    private fun registerVisibleSetting() {
        VisibleSettingActivity.register(receiver, context)
    }

    private fun unregisterVisibleSetting() {
        LocalBroadcastManager.getInstance(context).unregisterReceiver(receiver)
    }

    private val moreClick = View.OnClickListener {
        val title = it.tag as JoyWorkTitle
        showKpiDialog(title.kpiTarget, it.context) { type, target ->
            when (type) {
                type_visible -> {
                    registerVisibleSetting()
                    VisibleSettingActivity.start(target.goalId, context)
                }

                type_remove -> {
                    showRemoveDialog(context) {
                        KpiRepo.delTarget(target.goalId) {
                            if (it.isLegalString()) {
                                // error
                                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
                            } else {
                                adapter.removeGroup(title)
                            }
                        }
                    }
                }

                type_share -> {
                    target.shareToDD(context)
                }

                type_update -> {
                    target.updateSelf(context)
                }
            }
        }
    }

    private val JoyWorkTitle.kpiTarget
        get() = extra as KpiTarget

    private val mIconClick = View.OnClickListener {
        val title = it.tag as JoyWorkTitle
        if (title.kpiTarget.safeInit()) {
            adapter.toggleGroup(title)
        } else {
            val size = title.expandableGroup.realItems?.count { work ->
                work is KR
            } ?: 0
            // 没有初始化，需要加载
            adapter.kpiLoadMore?.invoke(
                title.expandableGroup.id,
                size
            ) {
                title.kpiTarget.initialize = true
                adapter.toggleGroup(title)
            }
        }
    }

    private val mTitleContainerClick = View.OnClickListener {
        val title = it.tag as JoyWorkTitle
        val target = title.kpiTarget
        target.goDetail(context)
    }

    private val mCommentClick = View.OnClickListener {
        val title = it.tag as KpiTarget
        JDMAUtils.clickEvent("", JoyWorkConstant.GOAL_COMMENT_CLICK, null)
        KRCommentActivity.startGoalComment(it.context, title) { id ->
            KpiRepo.getGoalDetail(id) { target ->
                val t = target ?: return@getGoalDetail
                title.commentNums = t.commentNums ?: 0
                adapter.notifyDataSetChanged()
            }
        }
    }

    fun bind(title: JoyWorkTitle, next: Any?) {
        val kpiTitle = title.extra as KpiTarget
        mTitleContainer.tag = title
        mTitleContainer.setOnClickListener(mTitleContainerClick)
        mActionContainer.tag = title
        mActionContainer.setOnClickListener(mTitleContainerClick)

        joyworkCount.text =
            context.resources.getString(R.string.joywork_task_count, kpiTitle.taskNums ?: 0)
        summaryCount.text =
            context.resources.getString(R.string.joywork_summary_count2, kpiTitle.progressNums ?: 0)

        commentCount.tag = kpiTitle
        commentCount.setOnClickListener(mCommentClick)
        commentCount.text =
            context.resources.getString(R.string.joywork_comment2, kpiTitle.commentNums ?: 0)
//        joyworkCount.tag = title
//        joyworkCount.setOnClickListener(mTitleContainerClick)
//        summaryCount.tag = title
//        summaryCount.setOnClickListener(mTitleContainerClick)

        if (title.expandableGroup.expand) {
            mIcon.setText(R.string.icon_padding_caredown)
            itemView.setBackgroundResource(R.drawable.joywork_top_round_10)
            mDivider.setBackgroundColor(Color.parseColor("#E6E6E6"))
        } else {
            mIcon.setText(R.string.icon_padding_right)
            itemView.setBackgroundResource(R.drawable.joywork_all_round_10_ffffff)
            mDivider.setBackgroundColor(Color.TRANSPARENT)
        }
        if (next == null || next is JoyWorkTitle) {
            itemView.setBackgroundResource(R.drawable.joywork_all_round_10_ffffff)
            mDivider.setBackgroundColor(Color.TRANSPARENT)
        }
        mIconContainer.tag = title
        mIconContainer.setOnClickListener(mIconClick)

        val view = context.inflater.inflate(R.layout.joywork_kpi_item_title_tag3, null)
        view.findViewById<ImageView>(R.id.mStatus).setImageResource(kpiTitle.statusRes())
        val mImage = view.findViewById<ImageView>(R.id.mImage)
        val tag = view.findViewById<TextView>(R.id.mTag)
        val mTabContainer = view.findViewById<ViewGroup>(R.id.mTabContainer)
        tag.setBackgroundResource(R.drawable.joywork_all_round_100_4c7cff)
        tag.argb("#FFFFFF")
        if (kpiTitle.getSectionName(context).isLegalString()) {
            tag.visible()
            mTabContainer.visible()
            tag.text = kpiTitle.getSectionName(context)
        } else {
            tag.gone()
            mTabContainer.gone()
        }
        if (kpiTitle.visibleType == null) {
            mImage.gone()
        } else if (kpiTitle.visibleType == VisibleSetting.TYPE_ONLY_SUPERVISOR) {
            mImage.visible()
            mImage.setImageResource(R.drawable.joywork_lock)
        } else {
            mImage.visible()
            mImage.setImageResource(R.drawable.joywork_lock_half)
        }
        val pair = kpiTitle.titleWeightExtraStr(mTitle.context)
        val ss = SpannableString(pair.first)
        val offset = -1
        ss.setSpan(
            ViewSpan(view, mTitle, offset),
            0,
            pair.second,
            SpannableString.SPAN_INCLUSIVE_EXCLUSIVE
        )
        mTitle.text = ss
        val items = kpiTitle.actions(mMore.context)
        if (items.isLegalList()) {
            mMore.visible()
            mMore.tag = title
            mMore.setOnClickListener(moreClick)
        } else {
            mMore.gone()
        }
    }
}

class KpiAddJoyWorkVH(
    context: Context, parent: ViewGroup,
    private val adapter: GoalListAdapter
) : RecyclerView.ViewHolder(
    context.inflater.inflate(
        R.layout.joywork_kpi_item_add,
        parent,
        false
    )
) {
    private val mLoadMore = itemView.findViewById<LinearLayout>(R.id.mLoadMore)
    private val mDivider = itemView.findViewById<View>(R.id.mDivider)
    private val mDividerVertical = itemView.findViewById<View>(R.id.mDividerVertical)
    private val mAdd = itemView.findViewById<LinearLayout>(R.id.mAdd)

    private val mArrow = itemView.findViewById<View>(R.id.mArrow)
    private val mProgressBar = itemView.findViewById<View>(R.id.mProgressBar)

    private val mAddClick = View.OnClickListener {
        JDMAUtils.clickEvent("", JoyWorkConstant.CREATE_KR_CLICK, null)
        val j = it.tag as KpiAddJoyWork
        val t = j.expandableGroup.title.extra as KpiTarget
        adapter.kpiNewTask?.invoke(t, j.expandableGroup.title)
    }
    private val mMoreClick = View.OnClickListener {
        if (mProgressBar.isVisible())
            return@OnClickListener
        mProgressBar.visible()
        mArrow.gone()
        val j = it.tag as KpiAddJoyWork
        val size = j.expandableGroup.realItems?.count { work ->
            work is KR
        } ?: 0
        adapter.kpiLoadMore?.invoke(j.expandableGroup.id, size) {
            mArrow.visible()
            mProgressBar.gone()
        }
    }

    fun bind(addJoyWork: KpiAddJoyWork, pre: Any?) {
        if (pre is JoyWorkTitle) {
            mDivider.gone()
        } else {
            mDivider.visible()
        }
        if (addJoyWork.loadMore) {
            mLoadMore.visible()
        } else {
            mLoadMore.gone()
        }
        if (addJoyWork.editKr) {
            mAdd.visible()
        } else {
            mAdd.gone()
        }
        if (mLoadMore.isVisible() && mAdd.isVisible()) {
            mDividerVertical.visible()
        } else {
            mDividerVertical.gone()
        }

        mAdd.tag = addJoyWork
        mAdd.setOnClickListener(mAddClick)

        mLoadMore.tag = addJoyWork
        mLoadMore.setOnClickListener(mMoreClick)

        mProgressBar.gone()
    }
}

class KpiKRVH(
    private val activity: Activity,
    parent: ViewGroup,
    val adapter: GoalListAdapter,
) : RecyclerView.ViewHolder(
    activity.inflater.inflate(
        R.layout.joywork_kpi_item_kr,
        parent,
        false
    )
), Exchangeable {

    private val joyworkCount = itemView.findViewById<TextView>(R.id.joyworkCount)
    private val summaryCount = itemView.findViewById<TextView>(R.id.summaryCount)
    private val commentCount = itemView.findViewById<TextView>(R.id.commentCount)
    private val mTitle = itemView.findViewById<TextView>(R.id.mTitle)
    private val content = itemView.findViewById<DividerLinearLayout>(R.id.content)
    private val actionContainer = itemView.findViewById<LinearLayout>(R.id.swipe_right)
    private val itemClick = View.OnClickListener {
        val kr = it.tag as KR
        GoalDetailActivity.kr(
            activity,
            kr.krId,
            canEdit = kr.permissions.krEditable(),
            canDel = kr.permissions.krDeletable(),
            canShare = false,
            100
        )
    }
    private val mCommentClick = View.OnClickListener {
        val krTag = it.tag as KR
        KRCommentActivity.startKRComment(it.context, krTag) { id ->
            KpiRepo.getKrDetail(id) { kr ->
                val k = kr ?: return@getKrDetail
                krTag.commentNums = k.commentNums ?: 0
                adapter.notifyDataSetChanged()
            }
        }
    }

    override var exchangeable: Boolean
        get() = true
        set(value) {}

    fun bind(kr: KR, next: Any?, itemVHItf: ItemVHItf) {
        itemView.tag = kr
        joyworkCount.text =
            activity.resources.getString(R.string.joywork_task_count, kr.taskNums ?: 0)
        summaryCount.text =
            activity.resources.getString(R.string.joywork_summary_count2, kr.progressNums ?: 0)

        commentCount.tag = kr
        commentCount.setOnClickListener(mCommentClick)
        commentCount.text =
            activity.resources.getString(R.string.joywork_comment2, kr.commentNums ?: 0)
        val view = activity.inflater.inflate(R.layout.joywork_kpi_item_title_tag, null)
        view.findViewById<ImageView>(R.id.mStatus).setImageResource(kr.condition.statusRes())
        val tag = view.findViewById<TextView>(R.id.mTag)
        val tagText = activity.resources.getString(R.string.joywork_kr_tag)
        tag.text = tagText
        val ss = SpannableString(tagText + kr.content)
        ss.setSpan(
            ViewSpan(view, mTitle, CommonUtils.dp2px(1.0f) + 1),
            0,
            tagText.length,
            SpannableString.SPAN_INCLUSIVE_EXCLUSIVE
        )
        mTitle.text = ss
        JoyWorkViewItem.action(kr, actionContainer, getActions(kr), ::actionClick)
        itemView.tag = kr
        itemView.setOnClickListener(itemClick)
        if (next is JoyWorkTitle || next == null) {
            itemView.setBackgroundResource(R.drawable.joywork_bottom_round_10_ffffff)
            content.showBottom(false)
        } else {
            itemView.setBackgroundColor(Color.WHITE)
            content.showBottom(true)
        }
    }

    private fun actionClick(action: JoyWorkAction, joywork: JoyWork) {
        val kr = joywork as KR
        when (action) {
            is DelAction -> {
                del(kr)
            }

            is EditAction -> {
                edit(kr)
            }
        }
    }

    private fun del(kr: KR) {
        showRemoveKRDialog(activity) {
            adapter.delKR?.invoke(kr)
        }
    }

    private fun edit(kr: KR) {
        KREditActivity.updateKR(activity, kr.krId, kr.content)
    }

    private fun getActions(kr: KR): List<JoyWorkAction> {
        val r = mutableListOf<JoyWorkAction>()
        if (kr.permissions.krEditable()) {
            r.add(EditAction(activity))
        }
        if (kr.permissions.krDeletable()) {
            r.add(DelAction(activity))
        }
        return r
    }

    fun onBeginDrag(holder: RecyclerView.ViewHolder) {
        JoyWorkJDMA.sendCustomData(JoyWorkConstant.DRAGANDDROP_KR)
    }

    fun onEndDrag(holder: RecyclerView.ViewHolder) {

    }

    fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        if (target !is KpiKRVH) {
            return false
        }
        val srcKr = itemView.tag as KR
        val dstKr = target.itemView.tag as KR
        return Objects.equals(srcKr.goalId, dstKr.goalId)
    }
}

class PlaceHolderVH(context: Context) : RecyclerView.ViewHolder(Space(context))

open class PlaceHolderVH2(context: Context, height: Int = 0) :
    RecyclerView.ViewHolder(Space(context).apply {
        layoutParams = RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height)
    })

/**
 * 空组占位，保证能够移动到空组
 */
class MovePlaceHolder(context: Context,): PlaceHolderVH2(context,1)