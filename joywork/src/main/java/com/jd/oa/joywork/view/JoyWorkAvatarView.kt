package com.jd.oa.joywork.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.jd.oa.joywork.R
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.isLegalString
import com.jd.oa.ui.CircleImageView
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.goneExcept
import com.jd.oa.utils.isVisible
import com.jd.oa.utils.removeLast
import com.jd.oa.utils.visible
import com.jd.oa.utils.visibleExcept

/**
 * 头像有两种显示模式：
 * 1. 使用 maxAvatar 指定最大头像个数：大于 maxAvatar 时追加气泡，同时显示 maxAvatar 个头像；小于等于 maxAvatar 时会显示所有头像，不显示气泡
 * 2. 使用 maxChildCount 指定最大子 item 个数：头像小于等于 maxChildCount 时，会显示所有头像，不显示气泡；大于 maxChildCount 时，会显示 maxAvatar 个头像，同时显示气泡
 */
class JoyWorkAvatarView(context: Context, attrs: AttributeSet) : LinearLayout(context, attrs) {
    var mCallback = JoyWorkAvatarViewCallback()

    companion object {
        // 当前应该显示短文字
        val TYPE_SHORT = 1
        val TYPE_LONG = 2
    }

    private var mCurType = TYPE_LONG

    private val mEmptyLayout: Int
    private val mMaxAvatar: Int
    private val mHintTextThreshold: Int

    private val mOffsetLayout: ViewGroup
    private val mOffsetLayout2: ViewGroup
    private val mEmptyContainer: FrameLayout
    private val mUrls = ArrayList<String>()
    private val mAvatarSize: Int
    private val mEllipsizeTextSize: Int
    private val mMaxChildCount: Int
    private val mHatPaddingExtra: Int
    private val mHatSize: Int
    private var mVertical: Boolean = false

    private val mIconSize: Int
    private val mIconRes: Drawable?
    private val mHintTextApperance: Int
    var mShowable: Boolean = true

    private var showMore = true
    private var showBorder = true

    val urls: List<String>
        get() {
            return ArrayList<String>(mUrls)
        }

    init {
        val a = context.obtainStyledAttributes(attrs, R.styleable.JoyWorkAvatarView)

        mIconSize = a.getDimensionPixelOffset(R.styleable.JoyWorkAvatarView_iconSize, -1)
        mIconRes = a.getDrawable(R.styleable.JoyWorkAvatarView_iconRes)

        mHatPaddingExtra = a.getDimensionPixelOffset(
            R.styleable.JoyWorkAvatarView_hatPaddingExtra,
            CommonUtils.dp2px(1.0f)
        )
        mHatSize =
            a.getDimensionPixelSize(R.styleable.JoyWorkAvatarView_hatSize, CommonUtils.dp2px(10.0f))
        mEmptyLayout = a.getResourceId(R.styleable.JoyWorkAvatarView_emptyLayout, -1)
        mMaxAvatar = a.getInt(R.styleable.JoyWorkAvatarView_maxAvatar, 3)
        mMaxChildCount = a.getInt(R.styleable.JoyWorkAvatarView_maxChildCount, Int.MAX_VALUE)
        mAvatarSize = a.getDimensionPixelOffset(R.styleable.JoyWorkAvatarView_avatarSize, -1)
        mEllipsizeTextSize =
            a.getDimensionPixelSize(R.styleable.JoyWorkAvatarView_ellipsizeTextSize, -1)

        mHintTextThreshold =
            if (a.getBoolean(R.styleable.JoyWorkAvatarView_alwaysShowHintText, false)) {
                Int.MAX_VALUE
            } else {
                a.getInt(R.styleable.JoyWorkAvatarView_hintTextThreshold, -1)
            }
        val overlap = a.getBoolean(R.styleable.JoyWorkAvatarView_avatarOverlap, true)
        showBorder = a.getBoolean(R.styleable.JoyWorkAvatarView_showBorder, true)
        showMore = a.getBoolean(R.styleable.JoyWorkAvatarView_showMore, true)
        val view = if (overlap) addView(
            R.layout.avator_layout,
            this,
            attachToRoot = true
        ) else addView(R.layout.avator_layout2, this, attachToRoot = true)
        mOffsetLayout = view.findViewById(R.id.offset)
        mOffsetLayout2 = view.findViewById(R.id.offset2)
        mOffsetLayout2.gone()
        mHintTextApperance = a.getResourceId(R.styleable.JoyWorkAvatarView_hintTextAppearance, 0)

        mEmptyContainer = view.findViewById(R.id.emptyContainer)
        a.recycle()
        orientation = HORIZONTAL
        updateUI()
    }

    fun setUrls(us: List<String?>) {
        val tmp = us.map {
            it ?: ""
        }
        mUrls.clear()
        mUrls.addAll(tmp)
        updateUI()
    }

    public fun switchTo(type: Int) {
        if (mCurType != type) {
            this.mCurType = type
            updateUI()
        }
    }

    private fun updateUI() {
        if (mUrls.isEmpty()) {
            if (mEmptyLayout <= 0) {
                gone()
            } else {
                mEmptyContainer.visible()
                mEmptyContainer.removeAllViews()
                addView(mEmptyLayout, mEmptyContainer)
                goneExcept(mEmptyContainer)
            }
        } else {
            if (mShowable) {
                visible()
            }
            visibleExcept(mEmptyContainer)
            mOffsetLayout.removeAllViews()
            mOffsetLayout2.removeAllViews()
            if (mVertical) {
                mOffsetLayout2.visible()
            } else {
                mOffsetLayout2.gone()
            }
            var used = 0 // 一共显示了多少个头像
            var currentParent: ViewGroup = mOffsetLayout
            for (i in 0 until mUrls.size) {
                val url = mUrls[i]
                currentParent = getCurrentParent(used)
                val itemView = addView(R.layout.avator_layout_item, currentParent)
                val iv = itemView.findViewById<CircleImageView>(R.id.avatar)
                val hat = itemView.findViewById<View>(R.id.hat)
                val mImageAltTextContainer =
                    itemView.findViewById<ViewGroup>(R.id.mImageAltTextContainer)
                val mImageAltText = itemView.findViewById<TextView>(R.id.mImageAltText)
                val mImageAltText2 = itemView.findViewById<TextView>(R.id.mImageAltText2)
                val mDivider = itemView.findViewById<View>(R.id.mDivider)
                if (mAvatarSize > 0) {
                    iv.layoutParams.width = mAvatarSize
                    iv.layoutParams.height = mAvatarSize
                    val cl = hat.layoutParams as ConstraintLayout.LayoutParams
                    cl.circleRadius = mAvatarSize / 2 + mHatPaddingExtra
                }
                if (!showBorder) iv.borderWidth = 0
                iv.borderColor = Color.TRANSPARENT
                if (mHatSize > 0) {
                    hat.layoutParams.width = mHatSize
                    hat.layoutParams.height = mHatSize
                }
                JoyWorkViewItem.avatar(iv, url)

                val mIcon = itemView.findViewById<ImageView>(R.id.mIcon)
                if (mIconSize <= 0 || mIconRes == null || !mCallback.needIcon(
                        url,
                        i,
                        mIcon,
                        currentParent
                    )
                ) {
                    mIcon.gone()
                } else {
                    mIcon.visible()
                    mIcon.setImageDrawable(mIconRes)
                    mIcon.layoutParams.width = mIconSize
                    mIcon.layoutParams.height = mIconSize
                }

                used++
                if (mCallback.needHat(url, i, iv, currentParent)) {
                    hat.visible()
                    val alt = mCallback.getImageAltText(mUrls, currentParent.context, i, mCurType)
                    if (alt != null) {
                        if (alt.first.isLegalString()) {
                            mImageAltText.visible()
                            mImageAltText.text = alt.first!!
                        } else {
                            mImageAltText.gone()
                        }
                        if (alt.second.isLegalString()) {
                            mImageAltText2.visible()
                            mImageAltText2.text = alt.second!!
                        } else {
                            mImageAltText2.gone()
                        }
                        if (mImageAltText2.isVisible() || mImageAltText.isVisible()) {
                            mImageAltTextContainer.visible()
                        } else {
                            mImageAltTextContainer.gone()
                        }
                        if (mUrls.size <= 1) {
                            // only one
                            mDivider.gone()
                        } else if (currentParent != getCurrentParent(used + 1)) {
                            mDivider.gone()
                        } else {
                            mDivider.visible()
                        }
                    } else {
                        mDivider.gone()
                        mImageAltText.gone()
                        mImageAltText2.gone()
                        mImageAltTextContainer.gone()
                    }
                } else {
                    hat.gone()
                    mImageAltText.gone()
                    mImageAltText2.gone()
                    mImageAltTextContainer.gone()
                }
                if (i + 1 >= mMaxAvatar || getAddedChildrenCount() > mMaxChildCount) {
                    break
                }
            }
            // 如果还有头像未显示，应该显示气泡
            if (used < mUrls.size) {
                if (getAddedChildrenCount() >= mMaxChildCount) {
                    // 没有足够的空间用于显示气泡，所以删除一个头像
                    currentParent.removeLast()
                }
                getBubble(currentParent, mUrls.size - used)
            }

            if (mUrls.size <= mHintTextThreshold) {
                addHintText(currentParent)
            }
        }
    }

    private fun addHintText(p: ViewGroup) {
        val result = addView(
            R.layout.avator_layout3,
            p
        )
        val mHintTextContainer = result.findViewById<ViewGroup>(R.id.hintTextContainer)
        val mHintText1 = result.findViewById<TextView>(R.id.hintText1)
        val mHintText = result.findViewById<TextView>(R.id.hintText)

        if (mHintTextApperance > 0) {
            mHintText1.setTextAppearance(context, mHintTextApperance)
            mHintText.setTextAppearance(context, mHintTextApperance)
        }

        val pair = mCallback.getHintText2(mUrls, context, mCurType)
        if (pair != null) {
            var text = pair.first
            if (text.isLegalString()) {
                mHintText1.text = text
                mHintText1.visible()
            } else {
                mHintText1.gone()
            }
            text = pair.second
            if (text.isLegalString()) {
                mHintText.visible()
                mHintText.text = text
            } else {
                mHintText.gone()
            }
            if (mHintText1.isVisible() || mHintText.isVisible()) {
                mHintTextContainer.visible()
            } else {
                mHintTextContainer.gone()
            }
        } else {
            mHintText1.gone()
            mHintText.gone()
            mHintTextContainer.gone()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun getBubble(p: ViewGroup, leftCount: Int): View {
        val result = addView(
            R.layout.avator_layout_item_bubble,
            p
        )
        val v: TextView = result.findViewById(R.id.bubbleText)
        if(showBorder)
            v.setBackgroundResource(R.drawable.joywork_circle_bg)
        else
            v.setBackgroundResource(R.drawable.joywork_circle_whtiout_border_bg)
        v.visible()
        val lp = v.layoutParams ?: ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        if (mAvatarSize > 0) {
            lp.width = mAvatarSize
            lp.height = mAvatarSize
        }
        if (mEllipsizeTextSize > 0) {
            v.setTextSize(TypedValue.COMPLEX_UNIT_PX, mEllipsizeTextSize.toFloat())
        }
        v.layoutParams = lp
        if (!showMore) { //不展示更多，意味着展示数字
            var reduceFontSize = true
            val content = if (leftCount > 99) {
                "+99"
            } else if (leftCount > 9) {
                "+$leftCount"
            } else {
                reduceFontSize = false
                "+$leftCount"
            }
            v.takeIf { reduceFontSize }?.run {
                v.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    textSize - DisplayUtils.dip2px(2f)
                )
            }
            v.setTextColor(Color.parseColor("#6A6A6A"))
            v.text = content
        }
        return result
    }

    private fun addView(resId: Int, parent: ViewGroup?, attachToRoot: Boolean = false): View {
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        if (parent == null) {
            return inflater.inflate(resId, null)
        } else {
            val view = inflater.inflate(resId, parent, attachToRoot)
            if (!attachToRoot) {
                parent.addView(view)
            }
            return view
        }
    }

    private fun getCurrentParent(used: Int): ViewGroup {
        return if (used < mCallback.getFirstLineChildrenCount() || !mVertical) {
            mOffsetLayout
        } else {
            mOffsetLayout2
        }
    }

    private fun getAddedChildrenCount(): Int {
        var ans = mOffsetLayout.childCount
        if (mOffsetLayout2.isVisible() && mVertical) {
            ans += mOffsetLayout2.childCount
        }
        return ans
    }

    fun setVertical(v: Boolean) {
        if (mVertical != v) {
            mVertical = v
            requestLayout()
            updateUI()
        }
    }
}

open class JoyWorkAvatarViewCallback {
    /**
     * 如果分多行显示时，第一行显示的数量
     */
    open fun getFirstLineChildrenCount(): Int {
        return 1
    }

    /**
     * hintText 由两部分组成。first 为 null 第一部分不显示，second 为 null 第二部分不显示.
     * 第一部分有最大长度限制，第二部分没有
     */
    open fun getHintText2(
        urls: List<String>,
        context: Context,
        type: Int
    ): Pair<String?, String?>? {
        return null
    }

    /**
     * 在 【needHat】返回 true 的情况下，用于返回每一个头像后面跟随的文字。该文字由两部分组成
     * @return 返回 null 即不显示文字; first 为 null，第一部分不显示；second 为 null，第二部分不显示
     */
    open fun getImageAltText(
        urls: List<String>,
        context: Context,
        position: Int,
        type: Int
    ): Pair<String?, String?>? {
        return null
    }

    /**
     * 返回是否显示头像右下角的蓝色三角标志
     */
    open fun needIcon(
        url: String,
        position: Int,
        itemView: ImageView,
        parent: ViewGroup
    ): Boolean {
        return false
    }


    /**
     * @return 是否需要戴帽子
     */
    open fun needHat(
        url: String,
        position: Int,
        itemView: ImageView,
        parent: ViewGroup
    ): Boolean {
        return false
    }
}

