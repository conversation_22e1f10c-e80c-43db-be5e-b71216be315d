package com.jd.oa.joywork.bean;

import android.content.Context;
import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.R;

import java.util.ArrayList;
import java.util.List;

/**
 * joywork 中跟绩效相关的目标
 */
public class KpiTarget implements Parcelable {

    private static final String P_SHARE = "share";
    private static final String P_EDIT = "edit";
    private static final String P_DEL = "delete";
    private static final String P_KR = "kr";
    private static final String P_PROGRESS = "progress";
    private static final String P_VISIBLE = "visible";

    public KpiTarget() {

    }

    /**
     * 权限
     */
    public List<String> permissions;

    // 所属的 q id。对应 KpiQ 类
    public String assessPeriod;

    protected KpiTarget(Parcel in) {
        permissions = in.createStringArrayList();
        assessPeriod = in.readString();
        goalId = in.readString();
        itemWeight = in.readString();
        itemName = in.readString();
        sectionName = in.readString();
        if (in.readByte() == 0) {
            condition = null;
        } else {
            condition = in.readInt();
        }
        if (in.readByte() == 0) {
            progressNums = null;
        } else {
            progressNums = in.readInt();
        }
        byte tmpInitialize = in.readByte();
        initialize = tmpInitialize == 0 ? null : tmpInitialize == 1;
    }

    public static final Creator<KpiTarget> CREATOR = new Creator<KpiTarget>() {
        @Override
        public KpiTarget createFromParcel(Parcel in) {
            return new KpiTarget(in);
        }

        @Override
        public KpiTarget[] newArray(int size) {
            return new KpiTarget[size];
        }
    };

    public boolean getCanDel() {
        return ObjExKt.isLegalList(permissions) && permissions.contains(P_DEL);
    }

    public boolean getCanUpdateVisible(){
        return ObjExKt.isLegalList(permissions) && permissions.contains(P_VISIBLE);
    }

    public boolean getCanKr(){
        return ObjExKt.isLegalList(permissions) && permissions.contains(P_KR);
    }

    public boolean getCanEdit() {
        return ObjExKt.isLegalList(permissions) && permissions.contains(P_EDIT);
    }

    public boolean getCanShare() {
        return ObjExKt.isLegalList(permissions) && permissions.contains(P_SHARE);
    }

    // 目标 id。看成 work 生成的目标唯一标识，用这个可以唯一标识一个目标
    public String goalId;

    /**
     * 权重
     */
    public String itemWeight;
    // 标题
    public String itemName;
    // 额外的标签
    private String sectionName;
    public Integer condition; // 红黄绿灯
    /**
     * 每周总结数
     */
    public Integer progressNums;
    public Integer taskNums;
    public Integer commentNums;
    /**
     * 该目标下的所有 kr
     */
    public List<KR> krList;

    public Boolean initialize;

    public Integer visibleType;// 可见类型，对应 VisibleSetting 中

    public String uiContent;
    public Integer type; // 1 关联目标， 2 关联kr

    public boolean safeInit() {
        return initialize != null && initialize;
    }

    public List<KR> getSafeKrs() {
        if (krList == null) {
            krList = new ArrayList<>();
        }
        return krList;
    }

    public String getSectionName(Context context) {
        if (ObjExKt.isLegalString(sectionName)) {
            return sectionName;
        }else{
            return context.getResources().getString(R.string.joywork_objective);
        }
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }

    public int getSafeSummary() {
        return progressNums == null ? 0 : progressNums;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeStringList(permissions);
        dest.writeString(assessPeriod);
        dest.writeString(goalId);
        dest.writeString(itemWeight);
        dest.writeString(itemName);
        dest.writeString(sectionName);
        if (condition == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(condition);
        }
        if (progressNums == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(progressNums);
        }
        dest.writeByte((byte) (initialize == null ? 0 : initialize ? 1 : 2));
    }

    public int statusRes() {
        if (condition == 1) {
            return R.drawable.joywork_status_blue;
        } else if (condition == 2) {
            return R.drawable.joywork_status_yellow;
        } else {
            return R.drawable.joywork_status_red;
        }
    }
}
