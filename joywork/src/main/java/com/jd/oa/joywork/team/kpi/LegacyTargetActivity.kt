package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.me.web2.webview.JMEWebview
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.R
import com.jd.oa.joywork.clear
import com.jd.oa.joywork.detail.ui.JsTargetInterface
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.initJoyWork
import com.jd.oa.joywork.utils.JoyWorkNetConfig
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

/**
 * 目标新建、更新界面
 */
class LegacyTargetActivity : BaseActivity() {
    companion object {
        /**
         * 新建目标
         */
        fun newTarget(activity: Activity, code: Int, assessPeriod: String) {
            val i = Intent(activity, LegacyTargetActivity::class.java)
            i.putExtra("type", "new")
            i.putExtra("assessPeriod", assessPeriod)
            activity.startActivityForResult(i, code)
        }

        /**
         * 更新目标
         */
        fun updateTarget(activity: Activity, code: Int, goalId: String) {
            val i = Intent(activity, LegacyTargetActivity::class.java)
            i.putExtra("type", "update")
            i.putExtra("goalId", goalId)
            activity.startActivityForResult(i, code)
        }
    }

    private val handler = Handler()

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null)
                return
            when (intent.action) {
                JsTargetInterface.B_GOAL_SUCCESS -> {
                    val obj = intent.getSerializableExtra("data") as? HashMap<String, Boolean>
                    var r = false
                    if (obj != null) {
                        r = obj["refresh"] ?: false
                    }
                    if (r) {
                        val i = Intent()
                        i.putExtra(
                            "assessPeriod",
                            <EMAIL>("assessPeriod") ?: ""
                        )
                        setResult(RESULT_OK, i)
                    }
                    superFinish()
                }
                JsTargetInterface.HANDLE_TITLE -> {
                    val obj = intent.getSerializableExtra("data") as? HashMap<String, Boolean>
                    var r = true
                    if (obj != null) {
                        r = obj["show"] ?: true
                    }
                    if (r) {
                        mTitleContainer?.visible()
                    } else {
                        mTitleContainer?.gone()
                    }
                }
                JsTargetInterface.CLOSE_PAGE -> {
                    superFinish()
                }
            }
        }
    }

    private var mTitleContainer: View? = null
    private var mWebView: JMEWebview? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        setContentView(R.layout.joywork_target_web)
        mWebView = findViewById(R.id.webview)
        mWebView?.initJoyWork(getUrl())
        val filter = IntentFilter()
        filter.addAction(JsTargetInterface.B_GOAL_SUCCESS)
        filter.addAction(JsTargetInterface.HANDLE_TITLE)
        filter.addAction(JsTargetInterface.CLOSE_PAGE)
        LocalBroadcastManager.getInstance(this).registerReceiver(receiver, filter)

        findViewById<View>(R.id.mBack).setOnClickListener { superFinish() }
        mTitleContainer = findViewById(R.id.mTitleContainer)
        val title: TextView = findViewById(R.id.title)
        title.setOnClickListener {
            // 向网页发送保存事件
            JsTargetInterface.sendToWeb(mWebView, JsTargetInterface.B_GOAL_SAVE)
        }
    }

    override fun finish() {
        // 事件交由网页自己处理
        JsTargetInterface.sendToWeb(mWebView, JsTargetInterface.GO_BACK)
        handler.postDelayed({
            superFinish()
        },1000)
//        super.finish()
    }

    private fun superFinish() {
        super.finish()
    }

    override fun onDestroy() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(receiver)
        mWebView?.destroy()
        mWebView?.loadUrl("about:blank")
        handler.clear()
        super.onDestroy()
    }

    private fun getUrl(): String {
        val type = intent.getStringExtra("type")
        var url =
            JoyWorkNetConfig.getJoyWorkHost() + "/mobile-goal-edit-page?appName=jme" + "&lang=" + LocaleUtils.getUserSetLocaleStr(
                this
            ) + "&type=" + type
        url += when (type) {
            "new" -> {
                "&assessPeriod=" + intent.getStringExtra("assessPeriod")
            }
            else -> {
                "&goalId=" + intent.getStringExtra("goalId")
            }
        }
        return url
    }
}