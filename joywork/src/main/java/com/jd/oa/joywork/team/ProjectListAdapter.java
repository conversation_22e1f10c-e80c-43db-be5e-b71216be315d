package com.jd.oa.joywork.team;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;


import com.jd.oa.joywork.R;
import com.jd.oa.joywork.team.bean.ProjectList;

import java.util.List;

public class ProjectListAdapter extends RecyclerView.Adapter<ProjectListAdapter.ViewHolder> {
    private final List<ProjectList.Content.List> mList;
    private final Context mContext;
    private final boolean showArrow;

    public ProjectListAdapter(Context context, List<ProjectList.Content.List> list) {
        mList = list;
        mContext = context;
        this.showArrow = true;
    }

    public ProjectListAdapter(Context context, List<ProjectList.Content.List> list, boolean showArrow) {
        mList = list;
        mContext = context;
        this.showArrow = showArrow;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = View.inflate(mContext, R.layout.jdme_layout_project_item_link, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        final ProjectList.Content.List list = mList.get(i);
        viewHolder.tvProjectName.setText(list.getTitle());
        if (showArrow) {
            viewHolder.iv.setVisibility(View.VISIBLE);
        } else {
            viewHolder.iv.setVisibility(View.GONE);
        }
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(list);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvProjectName;
        View iv;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvProjectName = itemView.findViewById(R.id.tv_project);
            iv = itemView.findViewById(R.id.iv);
        }
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(ProjectList.Content.List list);
    }
}
