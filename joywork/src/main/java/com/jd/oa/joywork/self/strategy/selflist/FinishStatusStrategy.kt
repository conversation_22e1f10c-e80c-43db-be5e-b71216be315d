package com.jd.oa.joywork.self.strategy.selflist

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.JoyWorkWrapper
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.self.MyCreateFragment
import com.jd.oa.joywork.self.MyFinishFragment
import com.jd.oa.joywork.self.base.SelfListBaseAdapter
import com.jd.oa.joywork.self.base.SelfListBaseAdapterItf
import com.jd.oa.joywork.self.base.SelfListFinishAdapter
import com.jd.oa.joywork.self.base.SelfListViewModel
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.team.view.HRecyclerView

class FinishStatusStrategy(
    next: StrategyBase?,
    itf: StrategyBaseItf,
    context: FragmentActivity,
    sortFilterStatusViewModel: TeamDetailViewModel,
    viewModel: SelfListViewModel
) : StrategyBase(next, itf, context, sortFilterStatusViewModel, viewModel) {
    override fun canHandle(): Boolean {
        return mSortFilterStatusViewModel.mUIStatus.mStatus == TaskStatusEnum.FINISH
    }

    override fun needLoadMore(): Boolean {
        return true
    }

    override fun onGetList(userRole: TaskUserRole) {
        mViewModel.listFinishTasks(userRole, 0)
    }

    override fun onLoadMore(offset: Int, userRole: TaskUserRole) {
        mViewModel.listFinishTasks(userRole, offset)
    }

    override fun onCreateAdapter(
        data: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        itf: SelfListBaseAdapterItf,
        fragment: Fragment,
        recyclerView: HRecyclerView?
    ): SelfListBaseAdapter {
        return SelfListFinishAdapter(
            fragment,
            itf,
            data,
            recyclerView,
            context = context
        )
    }

    override fun onHandleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val wrapper = any as? JoyWorkWrapper ?: return ArrayList()
        return MyFinishFragment.handleResult(wrapper, context.resources)
    }
}