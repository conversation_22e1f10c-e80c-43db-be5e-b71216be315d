package com.jd.oa.joywork.team

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.ProjectSortCustom
import com.jd.oa.joywork.SortAction
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.detail.data.entity.custom.ProjectViewOuter
import com.jd.oa.joywork.filter.JoyWorkFilterSortFragmentItf
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.joywork.team.bean.ProjectSortTypeEnum
import java.util.*
import kotlin.collections.ArrayList

class UIStatus() {
    // 组内排序
    var mGroupInnerSort: ProjectSortTypeEnum = ProjectSortTypeEnum.SORT_NO

    // 筛选
    var mFilter: FilterValue = FilterValue.getNullInstance()

    // 描述
    var mDesc: String = ""

    // 状态
    var mStatus: TaskStatusEnum = TaskStatusEnum.UN_FINISH

    // 排序
    lateinit var mSort: SortValue

    fun isInited(): Boolean {
        return ::mSort.isInitialized
    }
}

/**
 * There are two mechanisms:
 * 1. You can observe [uiStatusLiveData] directly to get [UIStatus], it contains all information you maybe need
 * 2. You can observe [filterValueLiveData],[sortValueLiveData] etc, it will only give you information about filter,sort and so on.
 */
class TeamDetailViewModel : ViewModel() {
    val onlyUILiveData: MutableLiveData<String> = MutableLiveData()
    fun notifyUpdateUI() {
        onlyUILiveData.value = ""
    }

    val unableFilterSortLiveData = MutableLiveData<Boolean>()
    fun notifyUnableFilterSort(b: Boolean) {
        unableFilterSortLiveData.value = b
    }

    private val _mUIStatusLiveData: MutableLiveData<UIStatus> =
        MutableLiveData(UIStatus())

    val uiStatusLiveData: LiveData<UIStatus> = _mUIStatusLiveData

    val mUIStatus: UIStatus
        get() {
            return _mUIStatusLiveData.value!!
        }

    /**
     * set the [UIStatus]'s initial value
     */
    fun init(initF: (UIStatus) -> Unit) {
        initF(mUIStatus)
    }

    fun updateWithoutNotify(updateF: (UIStatus) -> Unit) {
        updateF.invoke(mUIStatus)
    }

    fun update(updateF: (UIStatus) -> Unit) {
        updateF(mUIStatus)
        notifyUpdate()
    }

    fun notifyUpdate() {
        if (!mUIStatus.isInited()) {
            return
        }
        _mUIStatusLiveData.value = _mUIStatusLiveData.value
    }

    // 组内排序
    private val _groupInnerSortLivedata = MutableLiveData<ProjectSortTypeEnum>()
    val groupInnerSortLivedata: LiveData<ProjectSortTypeEnum> = _groupInnerSortLivedata
    fun updateGroupInnerSort(newV: ProjectSortTypeEnum) {
        val oldValue = mUIStatus.mGroupInnerSort
        if (newV == oldValue) {
            return
        }
        mUIStatus.mGroupInnerSort = newV
        _groupInnerSortLivedata.value = newV
    }

    val groupInnerSort: ProjectSortTypeEnum
        get() {
            return mUIStatus.mGroupInnerSort
        }

    // 排序
    private val _sortValueLiveData = MutableLiveData<SortValue>()
    val sortValueLiveData: LiveData<SortValue> = _sortValueLiveData
    fun updateSort(sortAction: SortAction) {
        val oldValue = mUIStatus.mSort
        if (oldValue.isSameAction(sortAction)) {
            return
        }
        updateEditModel(true)
        val sortValue = if (sortAction is ProjectSortCustom) { // 自定义排序项
            val obj = SortValue.SortObj()
            obj.columnId = sortAction.value
            SortValue.getSortInstance(sortAction.enum().code, obj, sortAction.action)
        } else {
            SortValue.getSortInstance(sortAction.enum().code, null, sortAction.action)
        }
        mUIStatus.mSort = sortValue
        _sortValueLiveData.value = sortValue
    }

    fun updateSort(sortValue: SortValue) {
        mUIStatus.mSort = sortValue
        _sortValueLiveData.value = sortValue
    }

    // 筛选
    private val _filterValueLiveData = MutableLiveData<FilterValue>()
    val filterValueLiveData: LiveData<FilterValue> = _filterValueLiveData
    fun updateFilter(filterValue: FilterValue) {
        val oldValue: FilterValue = mUIStatus.mFilter
        if (filterValue.isEquals(oldValue)) {
            return
        }
        updateEditModel(true)
        mUIStatus.mFilter = filterValue
        _filterValueLiveData.value = filterValue
    }

    // 描述
    private val _descLiveData = MutableLiveData<String>()
    val descLiveData: LiveData<String> = _descLiveData
    fun updateDesc(desc: String?) {
        val d = desc ?: ""
        if (!Objects.equals(d, mUIStatus.mDesc)) {
            mUIStatus.mDesc = d
            _descLiveData.value = d
        }
    }

    // 待办状态
    private val _statusLiveData = MutableLiveData<TaskStatusEnum>()
    val statusLiveData: LiveData<TaskStatusEnum> = _statusLiveData
    fun updateStatus(item: TaskStatusEnum) {
        if (mUIStatus.mStatus == item) {
            return
        }
        mUIStatus.mStatus = item
        _statusLiveData.value = item
        updateEditModel(true)
    }

    // 通过 viewmodel 给 JoyWorkFilterSortFragment 填充依赖的接口
    val itfLiveData = MutableLiveData<JoyWorkFilterSortFragmentItf>()

    // 权限
    private val _permissionsLiveData = MutableLiveData<List<String>>()
    val permissionsLiveData: LiveData<List<String>> = _permissionsLiveData

    fun updatePermissions(ls: List<String>) {
        _permissionsLiveData.value = ls
    }

    // 标题
    private val _titleLiveData = MutableLiveData<String?>()
    val titleLiveData: LiveData<String?> = _titleLiveData

    fun updateTitle(title: String?) {
        _titleLiveData.value = title
    }

    // 待办分组
    private val _groupsLiveData = MutableLiveData<ArrayList<Group>>()
    val groupsLiveData: LiveData<ArrayList<Group>> = _groupsLiveData

    fun updateGroups(ls: ArrayList<Group>) {
        _groupsLiveData.value = ls
    }

    fun insertNewGroup(group: Group, index: Int) {
        val gs = _groupsLiveData.value ?: ArrayList(10)
        gs.apply {
            when {
                index < 0 -> {
                    add(0, group.copy())
                }

                index >= size -> {
                    add(group)
                }

                else -> {
                    add(index, group)
                }
            }
        }
        _groupsLiveData.value = gs
    }

    // 当前清单额外配置的字段
    private val _extLiveData = MutableLiveData<CustomFieldGroupOuter>()
    val extLiveData: LiveData<CustomFieldGroupOuter> = _extLiveData
    fun updateExt(outer: CustomFieldGroupOuter) {
        _extLiveData.value = outer;
    }

    //     自定义视图
    private val _customViewLiveData = MutableLiveData<String?>()
    val customViewLiveData: LiveData<String?> = _customViewLiveData
    fun updateProjectView(outer: String?) {
        _customViewLiveData.value = outer
    }

    // 视图创建人
    private val _saveUserLiveData = MutableLiveData<JoyWorkUser?>(null)
    val saveUserLiveData: LiveData<JoyWorkUser?> = _saveUserLiveData
    fun updateSaveUser(user: JoyWorkUser?) {
        _saveUserLiveData.value = user
    }

    // 是否显示保存设置
    private val _editModelLiveData = MutableLiveData(false)
    val editModelLiveData: LiveData<Boolean> = _editModelLiveData
    fun updateEditModel(b: Boolean) {
        _editModelLiveData.value = b
    }

    private val _deeplinkParamsLiveData = MutableLiveData<String?>(null)
    val deeplinkParamsLiveData: LiveData<String?> = _deeplinkParamsLiveData
    fun updateDeeplinkParams(content: String?) {
        _deeplinkParamsLiveData.value = content
    }
}