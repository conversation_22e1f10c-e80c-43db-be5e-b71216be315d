package com.jd.oa.joywork.expandable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import kotlin.jvm.functions.Function1;

/**
 * 每一个分组的聚合类
 *
 * @param <G> 每一分组的标题 bean
 * @param <T> 每一个具体的 item
 */
public class ExpandableGroup<G extends Gradeable & Groupable<G, T>, T extends Children<G> & Groupable<G, T>> {
    private ArrayList<T> realItems;
    private final G title;
    private final String id;
    private boolean expand;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExpandableGroup<?, ?> that = (ExpandableGroup<?, ?>) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public ExpandableGroup(G title, ArrayList<T> items, String id, boolean expand) {
        this.realItems = items;
        this.id = id;
        this.expand = expand;
        title.setExpandableGroup(this);
        this.title = title;
        if (items != null) {
            for (T t : items) {
                t.setGroupParent(title);
                t.setExpandableGroup(this);
            }
        }
    }

    public String getId() {
        return id;
    }

    public G getTitle() {
        return title;
    }

    public ArrayList<T> getRealItems() {
        return realItems;
    }

    public void ensureRealItems() {
        if (realItems == null) {
            realItems = new ArrayList<>();
        }
    }

    public void notifyItemChange() {
        if (realItems != null) {
            for (T t : realItems) {
                t.setGroupParent(title);
                t.setExpandableGroup(this);
            }
        }
        title.setExpandableGroup(this);
    }

    /**
     * 保存临时状态：在拖动时，如果当前分组是关闭的，也需要将 Item 临时挂到当前分组下
     * 后续有可能需要拖出去，所以先保存旧数据
     */
    private final List<T> mountItems = new ArrayList<>();

    public List<T> getMountItems() {
        return mountItems;
    }

    public void saveTmp(T t) {
        if (realItems != null) {
            mountItems.add(t);
        }
    }

    public void clearTmp() {
        mountItems.clear();
    }

    public int getItemCount() {
        return realItems == null ? 0 : realItems.size();
    }

    public void setExpand(boolean expand) {
        this.expand = expand;
    }

    public boolean getExpand() {
        return expand;
    }

    @Override
    public String toString() {
        return "ExpandableGroup{" +
                "title='" + title + '\'' +
                ", id=" + id +
                '}';
    }

    public int countRealItem(Function1<T, Boolean> filter) {
        int count = 0;
        if (realItems != null) {
            for (int i = 0; i < realItems.size(); i++) {
                if (filter.invoke(realItems.get(i))) {
                    count += 1;
                }
            }
        }
        return count;
    }
}
