package com.jd.oa.joywork.team.kpi

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class GoalListViewModel : ViewModel() {
    private val _followLiveData = MutableLiveData<Boolean>()

    val followLiveData: LiveData<Boolean> = _followLiveData

    fun isFollow(followed: <PERSON>olean) {
        _followLiveData.value = followed
    }

    fun changeFollowStatus() {
        val value = _followLiveData.value ?: return
        _followLiveData.value = !value
    }
}