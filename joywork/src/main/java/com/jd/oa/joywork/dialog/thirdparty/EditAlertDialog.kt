package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatDialog
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.content.ContextCompat
import com.jd.oa.joywork.R
import com.jd.oa.joywork.common.ShortcutDialogCommon
import com.jd.oa.joywork.dialog.DialogHelper
import com.jd.oa.joywork.isLegalString
import com.jd.oa.utils.ColorEx
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.TextWatcherAdapter
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.setCursorEnd
import com.jd.oa.utils.string
import com.jd.oa.utils.visible


class EditAlertDialog(context: Context, private val config: EditConfig) :
    AppCompatDialog(context, R.style.JoyWorkEditDialogStyle) {

    private lateinit var mContentView: View
    private lateinit var mMsgView: TextView
    private lateinit var mOkTextView: TextView
    private lateinit var mContent: AppCompatEditText

    init {
        window?.apply {
            decorView.setBackgroundColor(Color.TRANSPARENT)
            setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN
                        or WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE
                        or WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE
            )
            val layoutParams = attributes
            layoutParams.width = CommonUtils.getScreentWidth(context) * 62 / 75
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            attributes = layoutParams
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    private var mDialogHelper: DialogHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mDialogHelper = DialogHelper(this)
        mDialogHelper?.register()
        mContentView = context.inflater.inflate(R.layout.jdme_dialog_joywork_alert_edit_input, null)
        setContentView(mContentView)
        window?.decorView?.setBackgroundColor(Color.TRANSPARENT)
        mContentView.findViewById<ViewGroup>(R.id.root)?.apply {
            background =
                DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(config.bgCorner))
        }

        val titleView = context.inflater.inflate(
            config.titleLayout,
            mContentView.findViewById(R.id.title_container),
            true
        )
        config.titleInit(mContentView.findViewById(R.id.title_container))

        mMsgView = titleView.findViewById(R.id.message)
        mMsgView.text = getStr(config.msgRes, config.msgStr)
        titleView.findViewById<View>(R.id.icon)?.setOnClickListener {
            dismiss()
        }

        mOkTextView = mContentView.findViewById(R.id.ok)
        mOkTextView.text = getStr(config.okRes, config.okStr)

        val bottomTips = mContentView.findViewById<TextView>(R.id.mBottomTips)
        var bs = config.bottomTips
        if (config.bottomTipRes > 0) {
            bs = context.resources.getString(config.bottomTipRes)
        }
        if (bs.isLegalString()) {
            bottomTips.visible()
            bottomTips.text = bs
        } else {
            bottomTips.gone()
        }

        val cancelView = mContentView.findViewById<TextView>(R.id.cancel)
        cancelView.text = getStr(config.cancelRes, config.cancelStr)
        cancelView.setOnClickListener {
            config.cancelShitClick?.invoke(it, this) ?: cancel()
        }

        getOkClick()?.apply {
            mOkTextView.setOnClickListener {
                this.invoke(this@EditAlertDialog)
            }
        }

        context.inflater.inflate(
            config.editLayout,
            mContentView.findViewById(R.id.edit_content),
            true
        ).apply {
            mContent = findViewById(R.id.et_content)
        }

        if (config.showKeyBoard) {
            ShortcutDialogCommon.openKeyboard(mContent, context)
        }
        config.contentInit.invoke(mContent)

        if (config.inputHintRes == -1) {
            mContent.setHint(R.string.joywork_please_input)
        } else {
            val ret = kotlin.runCatching {
                mContent.setHint(config.inputHintRes)
            }
            if (ret.isFailure) {
                mContent.setHint(R.string.joywork_please_input)
            }
        }

        val color = ColorEx.addItem(
            ColorEx.Item().apply {
                enable = true
                color = ContextCompat.getColor(context, R.color.color_fe3b30)
            },
            ColorEx.Item().apply {
                enable = false
                color = ContextCompat.getColor(context, R.color.color_4bf0250f)
            },
        )
        mOkTextView.setTextColor(color)
        mOkTextView.isEnabled = config.okForceEnable

        mContent.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                super.afterTextChanged(s)
                val okClickable = s?.length!! > 0
                mOkTextView.isEnabled = okClickable or config.okForceEnable
            }
        })
        if (config.defaultContent.isLegalString()) {
            mContent.setText(config.defaultContent)
            mContent.setCursorEnd()
        }
    }

    fun getEditContent(): String = mContent.text.toString().trim()

    private fun getStr(res: Int, str: String?): String {
        if (res <= 0 && str == null) {
            throw IllegalArgumentException()
        }
        return str ?: context.string(res)
    }

    private fun getOkClick(): ((EditAlertDialog) -> Unit)? {
        return if (config.autoDismiss) { dialog ->
            dismiss()
            config.okShitClick?.invoke(dialog)
        } else config.okShitClick
    }

    override fun dismiss() {
        super.dismiss()
        mDialogHelper?.unregister()
    }
}

class EditConfig : ShitAlertDialogConfig<EditAlertDialog>() {
    var contentInit = { _: EditText -> }
    var titleInit = { _: LinearLayout -> }

    var defaultContent: String? = null
    var inputHintRes: Int = -1

    var titleLayout = R.layout.jdme_dialog_joywork_edit_input_title

    /**
     * 在显示时是否自动显示键盘
     */
    var showKeyBoard: Boolean = true

    var okForceEnable: Boolean = false

    var bottomTips: String? = null

    @StringRes
    var bottomTipRes: Int = -1

    @LayoutRes
    var editLayout = R.layout.jdme_joywork_edit_one_line_text
}