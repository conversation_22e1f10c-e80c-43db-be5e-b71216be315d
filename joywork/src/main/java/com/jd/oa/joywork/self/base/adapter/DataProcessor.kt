package com.jd.oa.joywork.self.base.adapter

import android.content.Context
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.CardBottomMultiPurpose
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.bean.TimeTitleJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.repo.JoyWorkLocationParam
import com.jd.oa.joywork2.bean.title.JoyWork2MovePlaceholderItem
import com.jd.oa.joywork2.list.grouper.CardBottomMultiPurposeVH
import com.jd.oa.utils.DateUtils

class DataProcessor(expandableGroups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
    private val data = ArrayList<Any>()
    var groups = expandableGroups

    init {
        refreshData()
    }

    fun getData(): ArrayList<Any> {
        return data
    }

    /**
     * 返回本次操作后与操作前 Item 数量差。正数表示展开，负数表示收起
     */
    fun toggleGroup(title: JoyWorkTitle): Int {
        val oldSize = data.size
        val group = title.expandableGroup!!
        val oldStatus = group.expand
        if (oldStatus) {// 本次要收起分组
            data.removeAll {
                when (it) {
                    // 普通的 item，时间标题，加载更多标题
                    is JoyWork -> it.expandableGroup == group
                    else -> false
                }
            }
        } else {
            val index = data.indexOf(title) + 1
            data.addAll(index, group.realItems)
        }
        group.expand = !oldStatus
        return data.size - oldSize
    }

    fun expandGroup(title: JoyWorkTitle) {
        val group = title.expandableGroup!!
        if (group.expand) {
            return
        }
        group.expand = true
        val index = data.indexOf(title) + 1
        data.addAll(index, group.realItems)
    }

    fun updateAll(expandableGroups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        this.groups = expandableGroups
        refreshData()
    }

    private var oldPos: Int = -1
    private var oldGroup: ExpandableGroup<JoyWorkTitle, JoyWork>? = null
    fun backup(item: Any) {
        groups.forEach {
            if (it.realItems.contains(item)) {
                oldGroup = it
                oldPos = it.realItems.indexOf(item)
            }
        }
    }

    fun restore(item: JoyWork) {
        var newPos: Int = -1
        var newGroup: ExpandableGroup<JoyWorkTitle, JoyWork>? = null
        groups.forEach {
            if (it.realItems.contains(item)) {
                newGroup = it
                newPos = it.realItems.indexOf(item)
            }
        }
        if (oldPos == newPos && newGroup?.id == oldGroup?.id) {
            return
        }

        if (newGroup?.id != oldGroup?.id) {
            newGroup?.realItems?.remove(item)
            newGroup?.notifyItemChange()
            oldGroup?.realItems?.add(oldPos, item)
            oldGroup?.notifyItemChange()
        } else {
            newGroup?.realItems?.move(newPos, oldPos)
        }
        refreshData()
    }

    /**
     * 添加到指定分组的某个位置
     */
    fun addGroup(group: ExpandableGroup<JoyWorkTitle, JoyWork>, any: JoyWork, index: Int) {
        group.realItems.add(index, any)
        group.notifyItemChange()
        refreshData()
    }

    fun addGroup(group: ExpandableGroup<JoyWorkTitle, JoyWork>, index: Int) {
        if (index < 0) {
            groups.add(0, group)
        } else if (index >= groups.size) {
            groups.add(group)
        } else {
            groups.add(index, group)
        }
        refreshData()
    }

    fun removeGroup(group: ExpandableGroup<JoyWorkTitle, JoyWork>){
        groups.remove(group)
        refreshData()
    }


    fun appendGroups(gs: List<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        groups.addAll(gs)
        refreshData()
    }

    /**
     * 根据 group 重新生成 data
     */
    fun refreshData() {
        data.clear()
        groups.forEach {
            data.add(it.title)
            if (it.expand) {
                data.addAll(it.realItems)
            }
            data.addAll(it.mountItems)
        }
    }

    fun getLocationInfo(joyWork: JoyWork): JoyWorkLocationParam {
        val ret = JoyWorkLocationParam()
        if ((joyWork.planTime ?: -1) >= 0)
            ret.planTime = "${joyWork.planTime}"
        ret.blockType = joyWork.expandableGroup.id
        val groupData = joyWork.expandableGroup.realItems
        val index = groupData.indexOf(joyWork)
        if (index > 0) {
            val front = groupData.indexAtWithNull(index - 1)
            if (front?.isJoyWork() == true) {
                ret.front = front.taskId
            }
            val after = groupData.indexAtWithNull(index + 1)
            if (after?.isJoyWork() == true) {
                ret.after = after.taskId
            }
        }
        return ret
    }

    /**
     * 纠正 item 的 planTime
     */
    fun correctPlanTime(item: JoyWork) {
        if (item.expandableGroup.id != BlockTypeEnum.IN_PLAN.code) {
            item.planTime = null
            return
        }
        val index = data.indexOf(item)
        val anchor = data.indexAtWithNull(index - 1) {
            it.isJoyWork() || it is TimeTitleJoyWork
        } ?: data.indexAtWithNull(index + 1) {
            it.isJoyWork()
        }
        item.planTime = if (anchor is TimeTitleJoyWork) anchor.titleTime else (anchor as? JoyWork)?.planTime
    }

    /**
     * 过滤掉不合理的数据，然后同步 groups
     */
    fun filterAndSyncData(context: Context) {
        val tmp = ArrayList<Any>(data.size)
        data.forEach {
            when {
                it is JoyWorkTitle -> {
                    // 换分组了，旧的没有内容的时间头需要删除
                    trimTimeTitleJoyWork(tmp)
                    tmp.add(it)
                }
                it is LoadMoreJoyWork -> {
                    // 处理加载更多了，移除当前分组中不必要的时间头
                    trimTimeTitleJoyWork(tmp)
                    tmp.add(it)
                }
                it is CardBottomMultiPurpose<*> -> {
                    tmp.add(it)
                }
                it is JoyWork2MovePlaceholderItem -> {
                    tmp.add(it)
                }
                it.isJoyWork() -> {
                    val joyWork = it as JoyWork
                    if (joyWork.expandableGroup.id != BlockTypeEnum.IN_PLAN.code) {
                        tmp.add(it)
                    } else {
                        if (joyWork.planTime != null && joyWork.planTime > 0) {
                            var last = tmp.lastOrNull()
                            // last 只可能是 JoyWorkTitle，JoyWork, TimeTitleJoyWork
                            while (last is TimeTitleJoyWork && !DateUtils.isSameDay(last.titleTime, it.planTime)) {
                                tmp.removeLast()
                                last = tmp.lastOrNull()
                            }
                            if (last is JoyWorkTitle) { // 分组标题
                                tmp.add(TimeTitleJoyWork(JoyWorkEx.getTimeTitleString(it.planTime, context), it.planTime))
                            }
                            // 与上一条待办不是同一天计划内
                            if ((last?.isJoyWork() == true) && !DateUtils.isSameDay((last as JoyWork).planTime, it.planTime)) {
                                tmp.add(TimeTitleJoyWork(JoyWorkEx.getTimeTitleString(it.planTime, context), it.planTime))
                            }
                            tmp.add(it)
                        }
                    }
                }
                it is TimeTitleJoyWork -> {
                    // 逆序遍历，移除重复的时间头
                    trimTimeTitleJoyWork(tmp)
                    tmp.add(it)
                }
            }
        }
        data.clear()
        data.addAll(tmp)
        syncData()
    }

    /**
     * 拖动交换位置后，使用该方法保证数据同步
     */
    fun syncData() {
        var lastGroup: ExpandableGroup<JoyWorkTitle, JoyWork>? = null
        data.forEach {
            if (it is JoyWorkTitle) {
                lastGroup = it.expandableGroup
                if (lastGroup?.expand == true) {
                    lastGroup?.realItems?.clear()
                }
            } else if (it is JoyWork) {
                if (lastGroup?.expand == true) {
                    lastGroup?.realItems?.add(it)
                    it.expandableGroup = lastGroup
                }
            }
        }
    }

    private fun trimTimeTitleJoyWork(tmp: ArrayList<Any>) {
        // 逆序遍历，移除重复的时间头
        while (tmp.lastOrNull() is TimeTitleJoyWork) {
            tmp.removeLast()
        }
    }

    fun firstTitle(joyWorkTitle: JoyWorkTitle): Boolean {
        return data.firstOrNull {
            it is JoyWorkTitle
        } == joyWorkTitle
    }
}