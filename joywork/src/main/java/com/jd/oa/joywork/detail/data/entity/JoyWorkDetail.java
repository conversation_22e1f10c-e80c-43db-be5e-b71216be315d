package com.jd.oa.joywork.detail.data.entity;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.joywork.AlertType;
import com.jd.oa.joywork.ObjExKt;
import com.jd.oa.joywork.RiskEnum;
import com.jd.oa.joywork.bean.KR;
import com.jd.oa.joywork.bean.KpiTarget;
import com.jd.oa.model.ExecutorInfo;
import com.jd.oa.model.WorkInfo;
import com.jd.oa.model.service.im.dd.entity.Members;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * Auto-generated: 2021-07-06 18:26:51
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class JoyWorkDetail {

    private String sourceId;
    private long gmtModified;
    private String bizCode;
    private String parentTaskId;
    private String fromMQ;
    private String remark;
    private String title;
    private String content;
    private String inventory;
    private Long remindTime;
    /**
     * 提醒类型
     */
    public String remindStr;
    private ArrayList<ChildWorks> childWorks;
    private String mobileContent;

    private String subMobileContent;

    private boolean enablePermission;
    private int taskStatus;
    // 多人待办负责人
    public List<Owner> owners;
    private Creator creator;
    private ParentTask parentTask;
    private Integer blockType;
    private List<Resources> resources;
    private List<Documents> documents;
    private Long completeTime;
    private List<String> permission;
    private int sort;
    private Long gmtCreate;
    private int priorityType;
    private String extend;
    private Boolean thirdProcess;
    // 旧版本是计划时间，新版本是启动时间  2022年04月14日
    private Long planTime;
    private List<Members> executors;
    private List<Members> taskPraises;
    private Long endTime;
    private Long startTime;
    private String sourceName;
    public String sourceDesc; // 用于详情页"来自"后的文案显示
    // 三方文档或者会话的名称
    public String tripartiteName;

    public String subTripartiteName;
    private String userRole;
    private String projectId;
    private String taskId;
    private Integer status;
    private Integer readStatus;
    private int total;
    private ArrayList<Project> projects;
    private ArrayList<Project> sysProjects;
    public String sysProjectIcon; // 系统预置清单图标
    // from 2022年09月28日, In the task details page, goals means both goal and kr
    public ArrayList<KpiTarget> goals;
    public ArrayList<KR> krs;
    public MergeInfo mergeInfo;
    /**
     * 风险状态。对应 {@link RiskEnum}
     */
    public Integer riskStatus;
    /**
     * 风险描述
     */
    public String riskContent;
    /**
     * 对应 {@DuplicateEnum}。重复待办周期
     */
    public Integer cycle;

    public Map<String, List<String>> customFields;

    public Map<String, List<String>> safeCustomFields() {
        if (customFields == null) {
            customFields = new HashMap<>();
        }
        return customFields;
    }

    public ArrayList<Project> getProjects() {
        return projects;
    }

    public void setProjects(ArrayList<Project> projects) {
        this.projects = projects;
    }

    public ArrayList<Project> getSysSetProjects() {
        return sysProjects;
    }

    public void setSysSetProjects(ArrayList<Project> sysSetProjects) {
        this.sysProjects = sysSetProjects;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<Documents> getDocuments() {
        return documents;
    }

    public void setDocuments(List<Documents> documents) {
        this.documents = documents;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setGmtModified(long gmtModified) {
        this.gmtModified = gmtModified;
    }

    public long getGmtModified() {
        return gmtModified;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public String getParentTaskId() {
        return parentTaskId;
    }

    public void setFromMQ(String fromMQ) {
        this.fromMQ = fromMQ;
    }

    public String getFromMQ() {
        return fromMQ;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setInventory(String inventory) {
        this.inventory = inventory;
    }

    public String getInventory() {
        return inventory;
    }

    public void setChildWorks(ArrayList<ChildWorks> childWorks) {
        this.childWorks = childWorks;
    }

    public ArrayList<ChildWorks> getChildWorks() {
        return childWorks;
    }

    public void setMobileContent(String mobileContent) {
        this.mobileContent = mobileContent;
    }

    public String getMobileContent() {
        return mobileContent;
    }

    public String getSubMobileContent() {
        return subMobileContent;
    }

    public void setSubMobileContent(String subMobileContent) {
        this.subMobileContent = subMobileContent;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getStartTime() {
        if (startTime == null || startTime <= 0) {
            return null;
        }
        return startTime;
    }

    public void setEnablePermission(boolean enablePermission) {
        this.enablePermission = enablePermission;
    }

    public boolean getEnablePermission() {
        return enablePermission;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public int getTaskStatus() {
        return taskStatus;
    }

    public void setCreator(Creator creator) {
        this.creator = creator;
    }

    public Creator getCreator() {
        return creator;
    }

    public void setParentTask(ParentTask parentTask) {
        this.parentTask = parentTask;
    }

    public ParentTask getParentTask() {
        return parentTask;
    }

    public void setBlockType(int blockType) {
        this.blockType = blockType;
    }

    public Integer getBlockType() {
        return blockType;
    }

    public void setResources(List<Resources> resources) {
        this.resources = resources;
    }

    public List<Resources> getResources() {
        return resources;
    }

    public void setCompleteTime(Long completeTime) {
        this.completeTime = completeTime;
    }

    public Long getCompleteTime() {
        return completeTime;
    }

    public void setPermission(List<String> permission) {
        this.permission = permission;
    }

    public List<String> getPermission() {
        return permission;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getSort() {
        return sort;
    }

    public void setGmtCreate(Long gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Long getGmtCreate() {
        return gmtCreate;
    }

    public void setPriorityType(int priorityType) {
        this.priorityType = priorityType;
    }

    public int getPriorityType() {
        return priorityType;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public String getExtend() {
        return extend;
    }

    public void setThirdProcess(Boolean thirdProcess) {
        this.thirdProcess = thirdProcess;
    }

    public Boolean getThirdProcess() {
        return thirdProcess;
    }

    public void setPlanTime(Long planTime) {
        this.planTime = planTime;
    }

    public Long getPlanTime() {
        return planTime;
    }

    public void setExecutors(List<Members> executors) {
        this.executors = executors;
    }

    public List<Members> getExecutors() {
        return executors;
    }


    public List<Members> getTaskPraises() {
        return taskPraises;
    }

    public void setTaskPraises(List<Members> taskPraises) {
        this.taskPraises = taskPraises;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Long getEndTime() {
        if (endTime == null || endTime <= 0) {
            return null;
        }
        return endTime;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setReadStatus(Integer status) {
        this.readStatus = status;
    }

    public Integer getReadStatus() {
        return readStatus;
    }

    public HashSet<AlertType> getAlertTypes() {
        return AlertType.Companion.createFromString(remindStr);
    }

    public static class Project implements Parcelable {
        public List<String> permissions;
        @SerializedName("groupId")
        private String groupId; // 当前分组 id
        @SerializedName("groupTitle")
        private String groupTitle; // 当前分组名
        @SerializedName("title")
        private String title; // 当前分组所属清单名
        @SerializedName("projectId")
        private String projectId; // 当前分组所属清单 id

        public String icon; // 当前清单的图标

        protected Project(Parcel in) {
            groupId = in.readString();
            groupTitle = in.readString();
            title = in.readString();
            projectId = in.readString();
            icon = in.readString();
        }

        public Project(String projectId, String projectTitle) {
            this(null, null, projectTitle, projectId, null);
        }

        public Project() {

        }

        public Project(String groupId, String groupTitle, String title, String projectId, String icon) {
            this.groupId = groupId;
            this.groupTitle = groupTitle;
            this.title = title;
            this.projectId = projectId;
            this.icon = icon;
        }

        public static final Creator<Project> CREATOR = new Creator<Project>() {
            @Override
            public Project createFromParcel(Parcel in) {
                return new Project(in);
            }

            @Override
            public Project[] newArray(int size) {
                return new Project[size];
            }
        };

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public String getGroupTitle() {
            return groupTitle;
        }

        public void setGroupTitle(String groupTitle) {
            this.groupTitle = groupTitle;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getProjectId() {
            return projectId;
        }

        public void setProjectId(String projectId) {
            this.projectId = projectId;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(groupId);
            dest.writeString(groupTitle);
            dest.writeString(title);
            dest.writeString(projectId);
            if (ObjExKt.isLegalString(icon)) {
                dest.writeString(icon);
            } else {
                dest.writeString("");
            }
        }
    }

    @Override
    public String toString() {
        return "Content{" +
                "sourceId='" + sourceId + '\'' +
                ", gmtModified=" + gmtModified +
                ", bizCode='" + bizCode + '\'' +
                ", parentTaskId='" + parentTaskId + '\'' +
                ", fromMQ='" + fromMQ + '\'' +
                ", remark='" + remark + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", inventory='" + inventory + '\'' +
                ", remindTime=" + remindTime +
                ", childWorks=" + childWorks +
                ", mobileContent='" + mobileContent + '\'' +
                ", enablePermission=" + enablePermission +
                ", taskStatus=" + taskStatus +
                ", creator=" + creator +
                ", parentTask=" + parentTask +
                ", blockType=" + blockType +
                ", resources=" + resources +
                ", documents=" + documents +
                ", completeTime=" + completeTime +
                ", permission=" + permission +
                ", sort=" + sort +
                ", gmtCreate=" + gmtCreate +
                ", priorityType=" + priorityType +
                ", extend='" + extend + '\'' +
                ", thirdProcess='" + thirdProcess + '\'' +
                ", planTime='" + planTime + '\'' +
                ", executors=" + executors +
                ", taskPraises=" + taskPraises +
                ", endTime=" + endTime +
                ", startTime=" + startTime +
                ", sourceName='" + sourceName + '\'' +
                ", userRole='" + userRole + '\'' +
                ", projectId='" + projectId + '\'' +
                ", taskId='" + taskId + '\'' +
                ", status=" + status +
                ", readStatus=" + readStatus +
                ", total=" + total +
                '}';
    }

    /**
     * distinguish kr from goals
     */
    public void splitWebGoals() {
        if (goals == null || goals.isEmpty()) {
            return;
        }
        ArrayList<KpiTarget> gs = new ArrayList<>();
        ArrayList<KR> krs = new ArrayList<>();
        for (KpiTarget goal : goals) {
            if (goal.type == 1) {
                goal.itemName = goal.uiContent;
                gs.add(goal);
            } else if (goal.type == 2) {
                KR kr = new KR();
                kr.content = goal.uiContent;
                kr.krId = goal.goalId;
                krs.add(kr);
            }
        }
        goals = gs;
        this.krs = krs;
    }

    @Nullable
    public static WorkInfo toWorkInfo(JoyWorkDetail detail) {
        if (detail == null) {
            return null;
        }
        WorkInfo workInfo = new WorkInfo(detail.taskId);
        workInfo.setUiTaskStatus(detail.status);
        workInfo.setStatus(detail.status);
        workInfo.setTitle(detail.title);
        workInfo.setThirdParty(detail.getThirdProcess() != null);
        // JoyWorkDetail没有finishAction
//        workInfo.setFinishAction(detail.finishAction);
        workInfo.setMobileContent(detail.mobileContent);
        workInfo.setSourceName(detail.sourceName);
        workInfo.setStartTime(detail.startTime == null ? 0L : detail.startTime);
        workInfo.setEndTime(detail.endTime == null ? 0L : detail.endTime);
        ArrayList<ExecutorInfo> executorInfos = getExecutorInfos(detail);
        workInfo.setExecutors(executorInfos);
        return workInfo;
    }

    private static @NonNull ArrayList<ExecutorInfo> getExecutorInfos(JoyWorkDetail detail) {
        ArrayList<ExecutorInfo> executorInfos = new ArrayList<>();
        if (detail.owners != null) {
            for (int i = 0; i < detail.owners.size(); i++) {
                Owner user = detail.owners.get(i);
                ExecutorInfo executorInfo = new ExecutorInfo();
                executorInfo.setChief(user.chief == 1);
                executorInfo.setAvatar(user.getHeadImg());
                executorInfo.setErp(user.getEmplAccount());
                executorInfos.add(executorInfo);
            }
        }
        return executorInfos;
    }
}