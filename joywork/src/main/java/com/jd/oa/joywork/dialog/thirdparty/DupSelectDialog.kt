package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.R
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitContentController
import com.jd.oa.ui.dialog.ShitDialogConfig
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible


class DupSelectDialog(
    private val ctx: Context,
    level: DuplicateEnum,
    callback: (DuplicateEnum) -> Unit
) : BaseShitDialog(ctx, ShitDialogConfig()) {
    private val listController = object : IShitContentController {
        override fun inflate(parent: FrameLayout): View {
            val inflater = ctx.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val content =
                inflater.inflate(R.layout.joywork_dialog_title_list_base_content, parent, false)
            parent.addView(content)
            return content
        }
    }

    override var contentController: IShitContentController
        get() = listController
        set(_) {}

    private val groupAdapter = DupSelectAdapter(level) {
        dismiss()
        callback.invoke(it)
    }

    override fun setupCustomContentView(content: View) {
        content.background = DrawableEx.roundSolidDirRect(
            Color.parseColor("#F4F5F6"),
            context.resources.getDimension(R.dimen.joywork_corner_big),
            DrawableEx.DIR_TOP
        )
        content.findViewById<TextView>(R.id.title).setText(R.string.joywork_duplicate_text)
        val recycler = content.findViewById<RecyclerView>(R.id.recycler)
        recycler.layoutManager = LinearLayoutManager(content.context)
        recycler.adapter = groupAdapter
        content.findViewById<TextView>(R.id.cancel).setOnClickListener { dismiss() }
    }


    private class DupSelectAdapter(
        val level: DuplicateEnum,
        val callback: (DuplicateEnum) -> Unit
    ) : RecyclerView.Adapter<DupViewHolder>() {
        val dataList = DuplicateEnum.values().toList()
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DupViewHolder {
            return DupViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.joywork_dup_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: DupViewHolder, position: Int) {
            val data = dataList[position]
            if (level == data) holder.icon.visible() else holder.icon.gone()
            holder.title.setText(data.resId)
            holder.divider.gone()
            holder.itemView.setOnClickListener {
                callback.invoke(data)
            }
        }

        override fun getItemCount(): Int {
            return dataList.size
        }
    }

    class DupViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var title: TextView = view.findViewById(R.id.content)
        var icon: View = view.findViewById(R.id.check)
        var divider: View = view.findViewById(R.id.divider)
    }

}






