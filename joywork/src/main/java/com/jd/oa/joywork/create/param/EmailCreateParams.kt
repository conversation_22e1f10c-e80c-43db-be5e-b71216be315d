package com.jd.oa.joywork.create.param

import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.create.Value

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/19 13:28
 */
class EmailCreateParams : DeeplinkCreateParam() {

    var title: String? = null
    var desc: String? = null

    companion object {
        const val APP_NAME: String = "email"
    }

    override fun getAppName(): String {
        return APP_NAME
    }

    override fun initCreateValue(value: Value?) {
        super.initCreateValue(value)
        value?.run {
            <EMAIL>?.apply {
                value.title = this
            }
            <EMAIL>?.apply {
                value.updateDes(this)
            }
            setNormalOwners(mutableListOf(JoyWorkUser.getSelf()))
        }
    }

}