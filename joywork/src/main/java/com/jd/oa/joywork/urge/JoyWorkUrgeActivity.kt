package com.jd.oa.joywork.urge

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.view.ViewGroup
import android.widget.ImageView
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.joywork.detail.data.db.UpdateReporter
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.detail.fromMembers
import com.jd.oa.joywork.detail.ui.SendResultCallback
import com.jd.oa.joywork.executor.CCListActivity
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.view.ExecutorCallback
import com.jd.oa.joywork.view.JoyWorkAvatarViewCallback
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.jdme_joywork_urge.*

/**
 * JoyWork 催办界面
 */
class JoyWorkUrgeActivity : BaseActivity() {

    companion object {
        val dataCache = HashMap<String, Any>()
        var sendResultCallback: SendResultCallback? = null

        // 催办待办
        const val TYPE_JOYWORK = 1

        // 催办子待办
        private const val TYPE_SUB_JOYWORK = 2

        fun start(activity: Activity, joyWork: JoyWork) {
            val intent = Intent(activity, JoyWorkUrgeActivity::class.java)
            val selfClone = joyWork.selfClone()
            selfClone?.owners?.forEach {
                it.taskId = selfClone.taskId
            }
            intent.putExtra("taskId", selfClone.taskId)
            intent.putExtra("title", selfClone.title ?: "")
            intent.putExtra("type", TYPE_JOYWORK)
            val key = "${System.currentTimeMillis()}"
            intent.putExtra("key", key)
            dataCache[key] = ArrayList(selfClone.owners)
            activity.startActivity(intent)
        }

        fun urgeSubjoywork(
            activity: Activity,
            owners: List<JoyWorkUser>,
            allOwners: List<JoyWorkUser>
        ) {
            val intent = Intent(activity, JoyWorkUrgeActivity::class.java)
            intent.putExtra("type", TYPE_SUB_JOYWORK)
            val key = "${System.currentTimeMillis()}"
            intent.putExtra("key", key)
            intent.putExtra("selectOwners", "selectOwners")
            dataCache[key] = ArrayList<JoyWorkUser>(allOwners)
            dataCache["selectOwners"] = ArrayList<JoyWorkUser>(owners)
            activity.startActivity(intent)
        }

        private fun JoyWorkUrgeActivity.getJoyWorkId(): String? {
            return intent.getStringExtra("taskId")
        }

        private fun JoyWorkUrgeActivity.getJoyWorkTitle(): String? {
            return intent.getStringExtra("title")
        }

        private fun JoyWorkUrgeActivity.getOwners(): List<JoyWorkUser> {
            val key = intent.getStringExtra("key") ?: return listOf()
            return (dataCache[key] as? List<JoyWorkUser>) ?: listOf()
        }

        private fun JoyWorkUrgeActivity.getSelectedOwners(): List<JoyWorkUser>? {
            return if (intent.hasExtra("selectOwners")) {
                (dataCache["selectOwners"] as? List<JoyWorkUser>) ?: listOf()
            } else {
                null
            }
        }

        /**
         * 是否是子待办催办
         */
        private fun JoyWorkUrgeActivity.isSubJoyWork(): Boolean {
            return intent.getIntExtra("type", TYPE_JOYWORK) == TYPE_SUB_JOYWORK
        }
    }

    private lateinit var allOwners: List<JoyWorkUser>
    private var selectOwners: ArrayList<JoyWorkUser> = ArrayList()
    private var mSuccess: Boolean = false

    /**
     * 抄送的人员
     */
    private val mCCUsers = ArrayList<JoyWorkUser>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_joywork_urge)
        hideAction()

        mBackView.setOnClickListener {
            JDMAUtils.onEventClick(
                JoyWorkConstant.JOYWORK_URGE_CANCEL,
                JoyWorkConstant.JOYWORK_URGE_CANCEL
            )
            exit()
        }
        allOwners = getOwners()
        selectOwners.addAll(getSelectedOwners() ?: allOwners)
        sort()
        mSend.setTextColor(ColorEx.addItem(ColorEx.Item().apply {
            enable = true
            color = <EMAIL>(R.color.joywork_red)
        }, ColorEx.Item().apply {
            enable = false
            color = Color.parseColor("#80FE3B30")
        }))
        mSend.isEnabled = false
        mSend.setOnClickListener {
            send()
        }

        mOwnerContainer.background =
            DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(4))
        mOwnerContainer.setOnClickListener {
            JoyWorkUrgeList.start(this, selectOwners, allOwners)
        }
        mContentView.apply {
            background = DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(4))
            addTextChangedListener(object : TextWatcherAdapter() {
                override fun afterTextChanged(s: Editable?) {
                    mSend.isEnabled = s?.run {
                        text.toString().isLegalString()
                    } ?: false
                }
            })
            setText(R.string.joywork_urge_def_text)
            setCursorEnd()
        }

        mTitle.apply {
            if (isSubJoyWork()) {
                gone()
            } else {
                text = getJoyWorkTitle() ?: ""
                background = DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(4))
            }
        }
        mAvatarView.mCallback = object : JoyWorkAvatarViewCallback() {
            override fun needHat(
                url: String,
                position: Int,
                itemView: ImageView,
                parent: ViewGroup
            ): Boolean {
                return selectOwners.safeGet(position)?.chief.isChief()
            }

            override fun getHintText2(
                urls: List<String>,
                context: Context,
                type:Int
            ): Pair<String?, String?> {
                val ans = if (urls.size == 1) {
                    selectOwners.firstOrNull()?.realName ?: context.resources.getString(
                        R.string.joywork_create_owner_text,
                        urls.size
                    )
                } else {
                    context.resources.getString(R.string.joywork_create_owner_text, urls.size)
                }
                return Pair(null,ans)
            }
        }
        mAvatarView.setUrls(selectOwners.map { it.headImg })
        handleCCUI()
        mCCAvatarView.mCallback =
            object : ExecutorCallback(selectOwners.map { it.realName }, this) {
                override fun getHintText2(
                    urls: List<String>,
                    context: Context,
                    type:Int
                ): Pair<String?, String?>? {
                    if (!mCCUsers.isLegalList()) {
                        return null
                    }
                    val text = if (mCCUsers.size == 1) {
                        mCCUsers.first().realName
                    } else {
                        resources.getString(R.string.joywork_cc_num, mCCUsers.size)
                    }
                    return Pair(null, text)
                }

                override fun getImageAltText(
                    urls: List<String>,
                    context: Context,
                    position: Int,
                    type:Int
                ): Pair<String?, String?>? {
                    return null
                }
            }
        mCCAddView.setOnClickListener {
            val s = ArrayList<MemberEntityJd>()
            mCCUsers.forEach {
                val jd = MemberEntityJd()
                jd.mApp = it.app
                jd.mId = it.emplAccount
                s.add(jd)
            }
            ExecutorUtils.selectCC(this, s, { jds ->
                jds?.forEach { jd ->
                    if (jd != null) {
                        val u = JoyWorkUser()
                        u.fromDD(jd)
                        mCCUsers.add(u)
                    }
                }
                handleCCUI()
            }, {

            })
        }
        mCCAvatarView.setOnClickListener {
            // 跳转至抄送人列表
            val i = Intent(this, CCListActivity::class.java)
            val ms = mCCUsers.map {
                val m = Members()
                m.fromJoyWorkUser(it)
                m
            }
            CCListActivity.inflateIntent(i, ms, null) {
                val us = it.map { m ->
                    val u = JoyWorkUser()
                    u.fromMembers(m)
                    u
                }
                mCCUsers.clear()
                mCCUsers.addAll(us)
                handleCCUI()
            }
            startActivity(i)
        }
    }

    override fun onBackPressed() {
        if (mContentView.string().isLegalString()) {
            JoyWorkDialog.showAlertDialog(
                this,
                R.string.joywork_project_create_cancle_tip,
                R.string.joywork_urge_tips_ok
            ) {
                finish()
            }
        } else {
            super.onBackPressed()
        }
    }

    private fun sort() {
        val c = selectOwners.removeAllIf {
            it.chief.isChief()
        }
        if (c.isLegalList()) {
            selectOwners.addAll(0, c)
        }
    }

    private fun exit() {
        if (mContentView.string().isLegalString()) {
            JoyWorkDialog.showAlertDialog(
                this,
                R.string.joywork_project_create_cancle_tip,
                R.string.joywork_urge_tips_ok
            ) {
                super.finish()
            }
        } else {
            super.finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dataCache.clear()
        sendResultCallback?.onResult(mSuccess)
        sendResultCallback = null
    }

    /**
     * 处理抄送者 UI
     */
    private fun handleCCUI() {
        if (mCCUsers.isLegalList()) {
            mCCAvatarView.visible()
            mCCArrow.visible()
            mCCAddView.gone()
            val urls = mCCUsers.map {
                it.headImg
            }
            mCCAvatarView.setUrls(urls)
        } else {
            mCCAvatarView.gone()
            mCCArrow.gone()
            mCCAddView.visible()
        }
    }

    private fun send() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.JOYWORK_URGE,
            JoyWorkConstant.JOYWORK_URGE
        )
        JoyWorkRepo.newUrge(object : JoyWorkUpdateCallback {
            override fun result(success: Boolean, errorMsg: String) {
                mSuccess = success
                if (success) {
                    UpdateReporter.reportUpdate()
                    ToastUtils.showInfoToast(R.string.joywork_urge_success)
                    finish()
                } else {
                    ToastUtils.showInfoToast(errorMsg)
                }
            }

            override fun onStart() {
            }

        }) {
            urgeContent = mContentView.text.toString().trim()
            if (getJoyWorkId().isLegalString()) {
                this.taskId = getJoyWorkId()
            }
            urgeUsers = selectOwners.map { user ->
                UrgeUserBase().apply {
                    taskId = user.taskId // 催办时可以不传，但取消催办需要。所以统一加上
                    emplAccount = user.emplAccount
                    ddAppId = user.ddAppId
                    app = user.app
                }
            }
            cc = mCCUsers.map { user ->
                UrgeUserBase().apply {
                    taskId = user.taskId // 催办时可以不传，但取消催办需要。所以统一加上
                    emplAccount = user.emplAccount
                    ddAppId = user.ddAppId
                    app = user.app
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == JoyWorkUrgeList.REQ_CODE && resultCode == RESULT_OK && (data?.hasExtra("key") == true)) {
            val key = data.getStringExtra("key")
            val r = (OneTimeDataRepo.dataCache.remove(key) as? List<JoyWorkUser>) ?: listOf()
            selectOwners.clear()
            selectOwners.addAll(r)
            sort()
            mAvatarView.setUrls(selectOwners.map { it.headImg })
        }
    }
}