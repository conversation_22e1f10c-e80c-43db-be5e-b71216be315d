package com.jd.oa.joywork.self.base

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.team.view.HRecyclerView

class SelfListProjectAdapter(
    fragment: Fragment,
    itf: SelfListBaseAdapterItf,
    groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    recyclerView: HRecyclerView?,
    context: FragmentActivity
) : SelfListBaseAdapter(fragment, itf, groups, recyclerView, context) {
}