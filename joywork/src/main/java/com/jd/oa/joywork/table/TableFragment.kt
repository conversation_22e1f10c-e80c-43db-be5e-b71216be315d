package com.jd.oa.joywork.table

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.R
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.joywork.view.red
import com.jd.oa.utils.*

abstract class TableFragment : BaseFragment() {
    private lateinit var mTableColumnConfig: TableColumnConfig
    fun getTableColumnConfig(): TableColumnConfig {
        if (!::mTableColumnConfig.isInitialized) {
            mTableColumnConfig = onCreateTableConfig()
        }
        return mTableColumnConfig
    }

    abstract fun onCreateTableConfig(): TableColumnConfig

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.joywork_table_frg, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val titleGroup = view.findViewById<ViewGroup>(R.id.title_container_child)

        val title = getTableColumnConfig().getTableTitleConfig().getMainTableTitle()
        title.forEach {
            addTitleItem(it, true, titleGroup)
        }
        val subTableTitle = getTableColumnConfig().getTableTitleConfig().getSubTableTitle()
        subTableTitle.forEach {
            addTitleItem(it, false, titleGroup)
        }

        val rv = view.findViewById<HRecyclerView>(R.id.rv)
        rv.vertical()
        rv.brotherView.add(view.findViewById(R.id.title_container))
        val refresh = view.findViewById<SwipeRefreshLayout>(R.id.refresh)
        refresh.red(requireActivity())
        onSetupView(rv, refresh)
    }

    private fun addTitleItem(title: TableTitle, isMain: Boolean, parent: ViewGroup) {
        val titleView = layoutInflater.inflate(R.layout.joywork_table_frg_title, parent, false)

        val name = titleView.findViewById<TextView>(R.id.name)
        if (isMain) {
            val w = CommonUtils.getScreentWidth(requireContext()) * 4 / 5 + CommonUtils.dp2px(26.0f)
            titleView.findViewById<View>(R.id.mRoot).layoutParams.width = w
        }
        name.setText(title.titleRes)

        val divider = titleView.findViewById<View>(R.id.mDivider)
        if (parent.childCount == 0) {
            divider.gone()
        } else {
            divider.visible()
        }

        parent.addView(titleView)
    }

    abstract fun onSetupView(rv: HRecyclerView, refreshLayout: SwipeRefreshLayout)
}