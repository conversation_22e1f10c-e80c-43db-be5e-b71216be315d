package com.jd.oa.joywork.bean;

/**
 * 待办数量
 */
public class JoyWorkNum {
    private static JoyWorkNum DEFAULT_VALUE;

    public static JoyWorkNum getDefault() {
        if (DEFAULT_VALUE == null) {
            DEFAULT_VALUE = new JoyWorkNum();
            DEFAULT_VALUE.taskNums = new JoyWorkNumInner();
            DEFAULT_VALUE.taskNums.finished = 0;
            DEFAULT_VALUE.taskNums.risk = 0;
            DEFAULT_VALUE.taskNums.myAssign = 0;
            DEFAULT_VALUE.taskNums.myCooperate = 0;
            DEFAULT_VALUE.taskNums.myHandle = 0;
        }
        return DEFAULT_VALUE;
    }

    public JoyWorkNumInner taskNums;

    public JoyWorkNumInner getSafeNum(){
        if(taskNums == null){
            taskNums = new JoyWorkNumInner();
            taskNums.finished = 0;
            taskNums.risk = 0;
            taskNums.myAssign = 0;
            taskNums.myCooperate = 0;
            taskNums.myHandle = 0;
        }
        return taskNums;
    }

    public void setSafeNum(JoyWorkNumInner num){
        if(num != null){
            this.taskNums = num;
        }
    }

    public static String getStrNum(Integer num) {
        if (num == null || num == 0) {
            return "";
        }
        return num >=100 ? "99+" : "" + num;
    }

    public static class JoyWorkNumInner {
        public Integer finished; // 已完成列表
        public Integer risk;// 风险
        public Integer myAssign;// 我指派的
        public Integer myCooperate;// 我协作的
        public Integer myHandle;// 我处理的

    }
}
