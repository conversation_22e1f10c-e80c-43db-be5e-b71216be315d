package com.jd.oa.joywork.team

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.jd.oa.BaseActivity
import com.jd.oa.abilities.api.OpennessApi
import com.jd.oa.joywork.*
import com.jd.oa.joywork.title.CenterTitleFragment
import com.jd.oa.joywork.utils.HyperLinkHelper
import com.jd.oa.joywork.utils.setUrlLinkText
import com.jd.oa.utils.argbId

/**
 * 清单设置
 */
class ProjectSettingActivity2 : BaseActivity(), View.OnClickListener {

    var projectId: String? = null
    var title: String? = null
    var desc: String? = null

    companion object {

        fun getIntent(context: Context, projectId: String, title: String, desc: String): Intent {
            val intent = Intent(context, ProjectSettingActivity2::class.java)
            val bundle = Bundle()
            bundle.putString("projectId", projectId)
            bundle.putString("title", title)
            bundle.putString("desc", desc)
            intent.putExtras(bundle)
            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_project_setting2)
        val bundle = intent.extras
        bundle?.apply {
            projectId = getString("projectId") ?: ""
            title = getString("title") ?: ""
            desc = getString("desc")
        }
        hideAction()
        CenterTitleFragment().left(R.string.icon_direction_left, R.id.back) {
            finish()
        }.into(this, R.id.mTitleContainer).title(R.string.joywork_list_setting).post {
            val view = findViewById<TextView>(R.id.back)
            view.argbId(R.color.icon_black)
            view.setTextSizeRes(R.dimen.joywork_icon_big_size)
        }
        val nameV = findViewById<TextView>(R.id.et_project_name)
        nameV.text = title
        val desV = findViewById<TextView>(R.id.et_project_des)
        desV.setUrlLinkText(desc, object : HyperLinkHelper.OnClickListener {
            override fun onUrlLinkClick(widget: View, url: String) {
                OpennessApi.openUrl(url, false)
            }
        })
        findViewById<View>(R.id.mMore).setOnClickListener {
            JoyWorkConstant.LIST_EXAMPLE_DEEPLINK.openDeeplink()
        }
    }

    override fun onClick(v: View?) {
    }
}