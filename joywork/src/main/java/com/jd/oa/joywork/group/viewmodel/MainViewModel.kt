package com.jd.oa.joywork.group.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.TaskStatusEnum
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

data class CountUIState(val mineCount: Int = 0, val otherCount: Int = 0) {
    operator fun get(index: Int): Int {
        return when (index) {
            0 -> mineCount
            else -> otherCount
        }
    }
}

data class Params(val sessionId: String, val sessionName: String, val sessionType: Int, val to: String)

class MainViewModel : ViewModel() {
    // 更新数量
    private val _countFlow: MutableStateFlow<CountUIState> = MutableStateFlow(CountUIState(0, 0))
    val countFlow: StateFlow<CountUIState> = _countFlow.asStateFlow()

    fun updateMineCount(mineCount: Int) {
        _countFlow.value = CountUIState(mineCount, _countFlow.value.otherCount)
    }

    fun updateOtherCount(otherCount: Int) {
        _countFlow.value = CountUIState(_countFlow.value.mineCount, otherCount)
    }

    // 筛选条件
    private val _taskStatusFlow = MutableStateFlow(TaskStatusEnum.UN_FINISH)
    val taskStatusFlow: StateFlow<TaskStatusEnum> = _taskStatusFlow

    fun updateFilter(nv: TaskStatusEnum) {
        _taskStatusFlow.value = nv
    }

    // 跨 frg/act 传参
    var parmas: Params? = null

    // 刷新列表
    private val _refreshLiveData = MutableLiveData<Boolean>()
    val refreshLiveData = _refreshLiveData as LiveData<Boolean>
    fun refresh() {
        _refreshLiveData.value = true
    }
}