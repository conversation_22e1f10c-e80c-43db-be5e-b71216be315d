package com.jd.oa.joywork.self.group

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.self.base.adapter.DataProcessor

/**
 * 处理二级列表。只是将分组 flat 成列表，不处理待办 UI 相关逻辑
 */
abstract class JoyWorkGroupAdapter(
    val fragment: Fragment,
    groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    val context: FragmentActivity
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    val processor: DataProcessor = DataProcessor(groups)
    val fg: FragmentManager = context.supportFragmentManager

    val groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>
        get() {
            return processor.groups
        }

    /**
     * 替换所有内容
     */
    fun replaceAllGroups(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        processor.updateAll(groups)
        notifyDataSetChanged()
    }

    /**
     * 回退某个 joywork 到原本位置
     */
    fun restore(joyWork: JoyWork) {
        processor.restore(joyWork)
        kotlin.runCatching {
            notifyDataSetChanged()
        }
    }

    /**
     * 切换分组状态，展开到关闭，关闭到展开
     */
    private fun toggleGroup(title: JoyWorkTitle) {
        processor.toggleGroup(title)
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return processor.getData().size
    }

    /**
     * 展开某一分组
     */
    fun expandGroup(title: JoyWorkTitle) {
        if (title.expandableGroup?.expand != false) {
            return
        }
        val deltaSize = processor.toggleGroup(title)
        notifyItemRangeInserted(processor.getData().indexOf(title) + 1, Math.abs(deltaSize))
        notifyItemChanged(processor.getData().indexOf(title))
    }

    /**
     * 展开所有
     */
    fun expandAll() {
        groups.forEach {
            processor.expandGroup(it.title)
        }
        notifyDataSetChanged()
    }
}