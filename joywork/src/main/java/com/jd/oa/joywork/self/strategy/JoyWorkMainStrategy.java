package com.jd.oa.joywork.self.strategy;

import static com.jd.oa.router.DeepLink.JDME;

import android.app.Activity;
import android.view.View;
import android.widget.FrameLayout;

import com.jd.oa.joywork.self.owner.MyOwnerFragment;
import com.jd.oa.router.DeepLink;

/**
 * {@link MyOwnerFragment} 的策略类
 */
public interface JoyWorkMainStrategy {
    String DEEP_LINK = DeepLink.rnOld("201909020601","routeTag=document_edit&rnStandalone=2&page_id=VAu639QHZHbKI8GsPLlL");;

    void handleTitle(FrameLayout parent, Activity activity);

    int getAddViewRightMargin();

    int getAddViewBottomMargin();

    void onThemeDataChange(View parent);
}
