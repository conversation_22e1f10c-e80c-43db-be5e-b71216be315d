package com.jd.oa.joywork.team

import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.bean.ProjectLoadMore2
import com.jd.oa.joywork.create.Value
import com.jd.oa.joywork.detail.*
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.UpdateMemberSend
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldItem
import com.jd.oa.joywork.detail.data.entity.custom.EmptyCustomFieldItem
import com.jd.oa.joywork.detail.ui.TaskUiUtils
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectAddTitle
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.bean.TeamGroupExtend
import com.jd.oa.joywork.team.view.DividerLinearLayout
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.*
import java.util.*

/**
 * 团队待办详情中所有 viewHolder
 */
class TeamLoadMoreVH(
    itemView: View,
    private val adapter: TeamAdapter,
    private val loadMoreCallback: ((groupId: String, count: Int, callback: (Group?, Boolean) -> Unit) -> Unit)? = null
) : RecyclerView.ViewHolder(itemView) {
    private val loadMore = itemView.findViewById<LinearLayout>(R.id.loadMore)
    private val loadMoreText = itemView.findViewById<TextView>(R.id.loadMoreText)
    private val loadMoreIcon = itemView.findViewById<View>(R.id.loadMoreIcon)
    private val pb = itemView.findViewById<View>(R.id.pb)
    private val LoadMoreJoyWork.group: Group
        get() {
            return expandableGroup.title.extra as Group
        }

    private fun updateUI(joyWork: JoyWork) {
        val loadMoreJoyTask = joyWork as LoadMoreJoyWork
        if (loadMoreJoyTask.isLoading) {
            pb.visible()
            loadMoreText.setText(R.string.libui_loading)
            loadMoreIcon.gone()
        } else {
            pb.gone()
            loadMoreText.setText(R.string.me_load_more_data)
            loadMoreIcon.visible()
        }
    }

    fun bind(joyWork: LoadMoreJoyWork) {
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        updateUI(joyWork)
        loadMore.tag = joyWork
        loadMore.setOnClickListener {
            val loadMoreJoyWork = it.tag as LoadMoreJoyWork
            if (loadMoreJoyWork.isLoading) {
                return@setOnClickListener
            }
            loadMoreJoyWork.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            loadMoreCallback?.invoke(
                loadMoreJoyWork.group.groupId,
                JoyWorkEx.countJoyWork(loadMoreJoyWork.expandableGroup.realItems)
            ) { data: Group?, success ->
                if (success && data != null) {
                    val items = loadMoreJoyWork.expandableGroup.realItems as ArrayList<JoyWork>
                    loadMoreJoyWork.isLoading = false
                    if (data.groupId == (loadMoreJoyWork.expandableGroup.title.extra as Group).groupId) {
                        items.remove(loadMoreJoyWork)
                        items.addAll(data.tasks)
                        val allLoaded = JoyWorkEx.countJoyWork(data.tasks) < ProjectRepo.PAGE_LIMIT
                        if (!allLoaded) {
                            items.add(loadMoreJoyWork)
                        }
                        loadMoreJoyWork.expandableGroup.notifyItemChange()
                    }
                    adapter.processor.refreshData()
                    adapter.notifyDataSetChanged()
                } else {
                    loadMoreJoyWork.isLoading = false
                    adapter.notifyItemChanged(adapterPosition)
                }
            }
        }
    }
}

class TeamLoadMoreVH2(itemView: View, private val adapter: TeamAdapter) :
    RecyclerView.ViewHolder(itemView) {
    private val loadMore = itemView.findViewById<LinearLayout>(R.id.loadMore)
    private val loadMoreText = itemView.findViewById<TextView>(R.id.loadMoreText)
    private val loadMoreIcon = itemView.findViewById<View>(R.id.loadMoreIcon)
    private val pb = itemView.findViewById<View>(R.id.pb)

    private fun updateUI(joyWork: JoyWork) {
        val loadMoreJoyTask = joyWork as LoadMoreJoyWork
        if (loadMoreJoyTask.isLoading) {
            pb.visible()
            loadMoreText.setText(R.string.libui_loading)
            loadMoreIcon.gone()
        } else {
            pb.gone()
            loadMoreText.setText(R.string.me_load_more_data)
            loadMoreIcon.visible()
        }
    }

    fun bind(loadMoreBean: ProjectLoadMore2) {
        itemView.setTag(R.id.jdme_tag_id, loadMoreBean)
        updateUI(loadMoreBean)
        loadMore.tag = loadMoreBean
        loadMore.setOnClickListener {
            val loadMoreJoyWork = it.tag as ProjectLoadMore2
            if (loadMoreJoyWork.isLoading) {
                return@setOnClickListener
            }
            loadMoreJoyWork.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            adapter.sortLoadMore.invoke(loadMoreBean) { s: Boolean ->
                loadMoreJoyWork.isLoading = false
                adapter.notifyItemChanged(adapterPosition)
            }
        }
    }
}

class TeamGroupTextImageVH(
    context: Context,
    parent: ViewGroup,
    private val adapter: TeamAdapter
) : RecyclerView.ViewHolder(
    context.inflater.inflate(
        R.layout.jdme_joywork_project_title2,
        parent,
        false
    )
), Exchangeable {
    private val title = itemView.findViewById<TextView>(R.id.title)
    private val avatar = itemView.findViewById<ImageView>(R.id.avatar)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_big).toInt()

    override var exchangeable: Boolean = false


    private val JoyWorkTitle.teamGroup: TeamGroupExtend
        get() {
            return extra as TeamGroupExtend
        }

    fun bind(joyWorkTitle: JoyWorkTitle) {
        itemView.setTag(R.id.jdme_tag_id, joyWorkTitle)

        title.text = joyWorkTitle.teamGroup.safeTitle(itemView.context)

        if (joyWorkTitle.teamGroup.user == null) {
            avatar.gone()
        } else {
            avatar.visible()
            JoyWorkViewItem.avatar(avatar, joyWorkTitle.teamGroup.user.headImg)
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "ProjectGroupTextImageVH【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}


class TeamTitleVH(context: Context, parent: ViewGroup, private val adapter: TeamAdapter) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_project_title,
            parent,
            false
        )
    ), Exchangeable {
    private val icon = itemView.findViewById<TextView>(R.id.icon)
    private val title = itemView.findViewById<TextView>(R.id.title)
    private val indicator = itemView.findViewById<TextView>(R.id.indicator)
    private val paddingSmall =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_small).toInt()
    private val paddingBig =
        itemView.context.resources.getDimension(R.dimen.joywork_project_group_big).toInt()

    override var exchangeable: Boolean = false

    private val iconClick = View.OnClickListener {
        showAction(it.tag as Group)
    }

    companion object {
        // 是否初始化过
        fun JoyWorkTitle.isInit(): Boolean {
            return (extra as Group).isInit
        }

        val JoyWorkTitle.group: Group
            get() {
                return extra as Group
            }
    }

    fun bind(
        joyWorkTitle: JoyWorkTitle,
        expanded: Boolean
    ) {
        itemView.setTag(R.id.jdme_tag_id, joyWorkTitle)

        if (joyWorkTitle.group.isDefaultGroup) {
            val params: ViewGroup.LayoutParams = itemView.layoutParams
            params.height = 1
            itemView.layoutParams = params
            return
        }

//        if (first) {
//            itemView.setPadding(itemView.paddingLeft, paddingSmall, itemView.paddingRight, itemView.paddingBottom)
//        } else {
//        itemView.setPadding(itemView.paddingLeft, paddingBig, itemView.paddingRight, itemView.paddingBottom)
//        }
        val params: ViewGroup.LayoutParams = itemView.layoutParams
        params.height = CommonUtils.dp2px(21.0f) + itemView.paddingTop + itemView.paddingBottom
        itemView.layoutParams = params

        /**
         * 没权限
         */
        if (!joyWorkTitle.group.safePermissions.contains(ProjectPermissionEnum.GROUP.code)) {
            icon.gone()
        } else {
            icon.visible()
            icon.tag = joyWorkTitle.group
            icon.setOnClickListener(iconClick)
        }
        title.text = joyWorkTitle.group.safeTitle(itemView.context)
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }
    }

    private fun showAction(group: Group) {
        val actions = getGroupAction(group)
        showGroupAction(itemView.context, actions) {
            when (it) {
                is ProjectRename -> {
                    showGroupNameEditDialog(itemView.context, group.title) {
                        adapter.editCallback?.invoke(it, group)
                    }
                }
                is ProjectNewAfter -> {
                    newGroup(false, group)
                }
                is ProjectNewBefore -> {
                    newGroup(true, group)
                }
                is ProjectDelete -> {
                    showGroupDelAlertDialog(itemView.context, group) {
                        adapter.delCallback?.invoke(it, group)
                    }
                }
            }
        }
    }

    private fun newGroup(before: Boolean, group: Group) {
        showNewGroupDialog(itemView.context, group) {
            adapter.newCallback.invoke(it, before, group)
        }
    }

    private fun getGroupAction(group: Group): List<JoyWorkAction> {
        val r = ArrayList<JoyWorkAction>()
        r.add(ProjectRename(itemView.context))
        // 默认分组不能前插
        if (!group.isDefaultGroup) {
            r.add(ProjectNewBefore(itemView.context))
        }
        r.add(ProjectNewAfter(itemView.context))
        // 默认分组不能删除
        if (!group.isDefaultGroup) {
            r.add(ProjectDelete(itemView.context))
        }
        return r
    }

    fun getJoyWorkTitle(): JoyWorkTitle {
        return itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
    }

    /**
     * 用于更新标题的数量
     */
    fun updateCount() {
        val joyWorkTitle = itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
//        count.text = "${Math.max(joyWorkTitle.count, 0)}" // 保证不会显示成负数
    }

    fun expandIfNecessary() {
        val title = itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
        if (!title.expandableGroup!!.expand) {
            adapter.expandGroup(title)
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class TeamNewGroupVH(context: Context, parent: ViewGroup, private val adapter: TeamAdapter) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_project_new_group,
            parent,
            false
        )
    ) {

    private val root = itemView.findViewById<View>(R.id.root)

    fun bind(joyWorkTitle: ProjectAddTitle) {
        root.setTag(R.id.jdme_tag_id, joyWorkTitle)
        root.setOnClickListener {
            val group = getJoyWorkTitle(it).expandableGroup
            (adapter.groups.indexAtWithNull(adapter.groups.indexOf(group) - 1)?.title?.extra as? Group)?.apply {
                newGroup(this)
            }
        }
    }

    private fun newGroup(group: Group) {
        showNewGroupDialog(itemView.context, group) {
            adapter.newCallback.invoke(it, false, group)
        }
    }

    private fun getJoyWorkTitle(view: View): JoyWorkTitle {
        return view.getTag(R.id.jdme_tag_id) as JoyWorkTitle
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

/**
 * 团队待办中 item 对应的 vh
 */
class TeamJoyWorkVH(context: Context, parent: ViewGroup, private val adapter: TeamAdapter) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.joywork_project_item,
            parent,
            false
        )
    ), Exchangeable {

    override var exchangeable = true

    private val title by lazy { itemView.findViewById<TextView>(R.id.title) }
    private val cb = itemView.findViewById<TextView>(R.id.cb_task)
    private val cb2 = itemView.findViewById<ImageView>(R.id.cb_task2)
    private val cbContainer by lazy { itemView.findViewById<View>(R.id.cb_task_container) }
    private val cover by lazy { itemView.findViewById<View>(R.id.cover) }

    private val ownerContainer by lazy { itemView.findViewById<View>(R.id.owner_container) }

    private val titleContainer by lazy { itemView.findViewById<LinearLayout>(R.id.ll_title_container) }
    private val mAvatarView: JoyWorkAvatarView by lazy { itemView.findViewById(R.id.mAvatarView) }

    private val exContainer by lazy { itemView.findViewById<LinearLayout>(R.id.ex_container) }
    private val deadline by lazy { itemView.findViewById<TextView>(R.id.deadline) }
    private val dupIcon by lazy { itemView.findViewById<IconFontView>(R.id.dupIcon) }
    private val deadlineDupContainer by lazy { itemView.findViewById<ViewGroup>(R.id.deadline_dup) }
    private val deadlineContainer by lazy { itemView.findViewById<View>(R.id.deadline_container) }
    private val itemContainer by lazy { itemView.findViewById<DividerLinearLayout>(R.id.item_container) }
    private val deadlineVH by lazy { itemView.findViewById<View>(R.id.mark_value_vh) }
    private val risk = itemView.findViewById<TextView>(R.id.risk)

    // 优先级
    private val priority_container = itemView.findViewById<View>(R.id.priority_container)
    private val priority = itemView.findViewById<TextView>(R.id.priority)
    private val priorityVH = itemView.findViewById<View>(R.id.priority_value_vh)

    private val itemClick = View.OnClickListener {
        val task = it.getTag(R.id.jdme_tag_id) as JoyWork
        JDMAUtils.onEventClick(JoyWorkConstant.ITEM_CLICK, JoyWorkConstant.ITEM_CLICK)
        JoyWorkMediator.goDetail(
            adapter.fragment,
            JoyWorkDetailParam(
                taskId = task.taskId,
                projectId = task.projectId,
                taskName = task.title
            ).apply {
                from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                reqCode = 10
                obj = DetailReturnParcel()
            })
    }

    private fun enableView(b: Boolean, view: View) {
        if (b) {
            view.setBackgroundColor(Color.TRANSPARENT)
        } else {
            view.setBackgroundColor(Color.parseColor("#F0F3F3"))
        }
        view.isEnabled = b
    }

    private val ownerClickListener = View.OnClickListener {
        if (it.tag == null) {
            return@OnClickListener
        }
        val joywork = it.tag as JoyWork
        enableView(false, it)
        TaskUiUtils.selectContacts(
            adapter.context,
            1,
            object : Callback<java.util.ArrayList<MemberEntityJd?>> {
                override fun onSuccess(bean: java.util.ArrayList<MemberEntityJd?>) {
                    val memberSend = UpdateMemberSend()
                    memberSend.taskId = joywork.taskId
                    val members: MutableList<Members> = java.util.ArrayList()
                    for (memberEntityJd in bean) {
                        val member = Members()
                        member.fromDD(memberEntityJd!!)
                        members.add(member)
                    }
                    memberSend.members = members
                    TaskDetailWebservice.updateTaskMembers2(
                        memberSend,
                        object : TaskDetailWebservice.TaskCallback() {
                            override fun onSuccess(info: ResponseInfo<String>?) {
                                super.onSuccess(info)
                                enableView(true, it)
                                if (!hasError) {
                                    adapter.refresh()
                                }
                            }

                            override fun onFailure(exception: HttpException?, info: String?) {
                                super.onFailure(exception, info)
                                enableView(true, it)
                            }
                        })
                }

                override fun onFail() {
                    enableView(true, it)
                    //ToastUtils.showToast(activity.getString(R.string.task_operate_fail))
                }
            })
    }

    private val priorityClick = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        enableView(false, view)
        JDMAUtils.clickEvent("", JoyWorkConstant.TEAM_LIST_SET_REMINDER, null)
        val dialog = DialogManager.showLevelDialog(context, joywork.priorityType) {
            TaskDetailWebservice.updatePriority(
                joywork.taskId,
                it,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }

                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            // 刷新整个界面
                            adapter.refresh()
                        }
                    }
                })
        }
        dialog.setOnDismissListener {
            enableView(true, view)
        }
        dialog.show()
    }

    private val deadlineListener = View.OnClickListener { view ->
        if (view.tag == null) {
            return@OnClickListener
        }
        val joywork = view.tag as JoyWork
        enableView(false, view)
        val detail = Value()
        detail.startTime = joywork.startTime
        detail.endTime = joywork.endTime
        detail.replaceAlertType(AlertType.createFromString(joywork.remindStr))
        detail.duplicateEnum = DuplicateEnum.getByValue(joywork.cycle)
        detail.type = Value.TYPE_DEADLINE
        ShortcutManager(null, view.context).selectTime(detail) {
            val params = HashMap<String, Any>()
            if (!Objects.equals(detail.startTime, joywork.startTime)) {
                params["startTime"] =
                    if (detail.startTime.isLegalTimestamp()) detail.startTime else -1
            }
            if (!Objects.equals(detail.endTime, joywork.endTime)) {
                notifyRiskUpdateWhenEndTimeUpdate(view.context, detail.endTime, joywork.endTime)
                params["endTime"] =
                    if (detail.endTime.isLegalTimestamp()) detail.endTime else -1
            }
            if (detail.mAlertType.isEmpty()) {
                detail.mAlertType.add(AlertType.NO)
            }
            if (!AlertType.equals(
                    AlertType.createFromString(joywork.remindStr),
                    detail.mAlertType
                )
            ) {
                params["remindStr"] = AlertType.valueToString(detail.mAlertType)
            }
            val c = (detail.duplicateEnum ?: DuplicateEnum.NO).value
            if (!Objects.equals(c, joywork.cycle)) {
                params["cycle"] = c
            }
            if (!params.isLegalMap()) {
                enableView(true, view)
                return@selectTime
            }
            TaskDetailWebservice.postTaskUpdate(
                joywork.taskId,
                params,
                null,
                object : TaskDetailWebservice.TaskCallback() {
                    override fun onSuccess(info: ResponseInfo<String>?) {
                        super.onSuccess(info)
                        enableView(true, view)
                        if (!hasError) {
                            // 刷新整个界面
                            adapter.refresh()
                        }
                    }

                    override fun onFailure(exception: HttpException?, info: String?) {
                        super.onFailure(exception, info)
                        enableView(true, view)
                    }
                })
        }
    }

    fun bind(joyWork: JoyWork) {
        val data = adapter.processor.getData()
        if (data.indexOf(joyWork) == 0 || data.indexAtWithNull(data.indexOf(joyWork) - 1)
                ?.isJoyWork() == true
        ) {
            itemContainer.setShowTop(false)
        } else {
            itemContainer.setShowTop(true)
        }

        itemView.setTag(R.id.jdme_tag_id, joyWork)

        itemView.setTag(R.id.jdme_tag_id, joyWork)
        itemView.setOnClickListener(itemClick)

        if (showCover(joyWork)) {
            cover.visible()
        } else {
            cover.gone()
        }
        // 整个屏幕的 80%
        titleContainer.apply {
            val w = CommonUtils.getScreentWidth(adapter.context) * 4 / 5
            this.layoutParams.width = w
        }

        JoyWorkViewItem.title(joyWork, title)
        joyWork.sortOwners()
        JoyWorkViewItem.risk(joyWork, risk).avatarView(joyWork, mAvatarView, null) { joywork ->
            if (!joywork.owners.isLegalList() || joywork.owners.size >= 2) {
                ""
            } else {
                joywork.owners.firstOrNull()?.realName ?: ""
            }
        }

        JoyWorkViewItem.deadline(joyWork, deadline, deadlineVH)
        JoyWorkViewItem.priority(joyWork, priority, priorityVH)
        JoyWorkViewItem.duplicateIcon(joyWork, dupIcon)
        JoyWorkViewItem.deadlineDupView(joyWork, deadlineDupContainer, deadlineVH)

        if (joyWork.canUpdateDeadline()) {
            deadlineContainer.tag = joyWork
            deadlineContainer.click(deadlineListener)
        } else {
            deadlineContainer.tag = null
            deadlineContainer.isEnabled = false
            deadlineContainer.isClickable = false
        }

        if (joyWork.canUpdatePriority()) {
            priority_container.tag = joyWork
            priority_container.click(priorityClick)
        } else {
            priority_container.tag = null
            priority_container.isEnabled = false
            priority_container.isClickable = false
        }

        if (joyWork.canTransfer()) {
            ownerContainer.tag = joyWork
            ownerContainer.click(ownerClickListener)
        } else {
            // 2022年06月16日 不能点击头像
            ownerContainer.tag = null
            ownerContainer.isEnabled = false
            ownerContainer.isClickable = false
        }


        cb.setTag(R.id.jdme_tag_id, joyWork)
        cbContainer.setTag(R.id.jdme_tag_id, joyWork)
        addColTitle(joyWork)
        JoyWorkViewItem.checkboxNew(
            joyWork,
            cb,
            cb2,
            cbContainer
        ) { work: JoyWork, cbView: View ->
            JDMAUtils.onEventClick(
                JoyWorkConstant.INCOMPLETE_FINISH,
                JoyWorkConstant.INCOMPLETE_FINISH
            )
            JoyWorkViewItem.finishAction(work, adapter.context, object : JoyWorkUpdateCallback {
                override fun result(success: Boolean, errorMsg: String) {
                    work.isFinishing = false
                    if (success) {
                        try {
                            val tmp = cbView.tag as JoyWork
                            if (tmp.isFinish) {
                                ProjectConstant.sendBroadcast(
                                    cb.context,
                                    ProjectConstant.UNFINISH_ACTION
                                )
                                work.uiTaskStatus = TaskStatusEnum.UN_FINISH.code;
                            } else {
                                ProjectConstant.sendBroadcast(
                                    cb.context,
                                    ProjectConstant.FINISH_ACTION
                                )
                                work.uiTaskStatus = TaskStatusEnum.FINISH.code
                                if (work.isFinish && work.isDup) {
                                    ToastUtils.showInfoToast(R.string.joywork_dup_work_finish_tips)
                                }
                            }
                            checkboxSuccess(tmp)
                            try {
                                // 修改标题中的数量
                                (tmp.expandableGroup.title as JoyWorkTitle).count--
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            result(false, JoyWorkEx.filterErrorMsg(""))
                        }
                    } else {
                        adapter.notifyItemChanged(adapterPosition)
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }

                override fun onStart() {
                    work.isFinishing = true
                    adapter.notifyItemChanged(adapterPosition)
                }
            })
        } // 选框
    }

    private fun addColTitle(joyWork: JoyWork) {
        val group = adapter.titleGroup.customFields
        exContainer.removeAllViews()
        if (group.hasContent()) {
            exContainer.visible()
            // group 指一共有多少组自定义字段，每一个字段要加一个显示，所以 for
            group.forEach { cfg: CustomFieldGroup ->
                // fields 指当前 joywork 有多少自定义字段。其 key 为 cfg 的 colId，value 是选项 id 的集合
                val fields = joyWork.safeCustomFields()
                var fieldItem: CustomFieldItem = EmptyCustomFieldItem.getInstance()
                if (fields.isNotEmpty()) {
                    val detailIds = fields[cfg.columnId]
                    val detailId = detailIds?.firstOrNull()
                    cfg.details?.firstOrNull { fi ->
                        if (detailId != null && fi.detailId == detailId) {
                            fieldItem = fi
                            true
                        } else {
                            false
                        }
                    }
                }

                val view = adapter.context.layoutInflater.inflate(
                    R.layout.jdme_joywork_project_item_ex,
                    exContainer,
                    false
                )
                exContainer.addView(view)

                if (joyWork.canUpdateMark()) {
                    view.tag = fieldItem
                    view.setTag(R.id.tag_key_data, cfg)
                    view.setTag(R.id.tag_key_data_1, joyWork)
                    view.click {
                        enableView(false, it)
                        if (it.tag == null) {
                            return@click
                        }
                        val item = it.tag as CustomFieldItem
                        val clickGroup = it.getTag(R.id.tag_key_data) as CustomFieldGroup
                        val clickWork = it.getTag(R.id.tag_key_data_1) as JoyWork
                        DialogManager.showMarkDialog(
                            it.context,
                            item,
                            clickGroup,
                            clickWork.taskId
                        ) { item2 ->
                            enableView(true, it)
                            if (item2 != null) {
                                adapter.refresh()
                            }
                        }
                    }
                } else {
                    view.tag = null
                    view.isEnabled = false
                    view.isClickable = false
                }

                val valueContainer = view.findViewById<ViewGroup>(R.id.mark_container)
                kotlin.runCatching {
                    if (fieldItem == EmptyCustomFieldItem.getInstance()) {
                        valueContainer.background =
                            DrawableEx.roundSolidRect(Color.TRANSPARENT, CommonUtils.dp2FloatPx(4))
                    } else {
                        valueContainer.background = DrawableEx.roundSolidRect(
                            Color.parseColor(fieldItem.itemContent.background),
                            CommonUtils.dp2FloatPx(4)
                        )
                    }
                }
                val value = view.findViewById<TextView>(R.id.mark_value)
                val valueVH = view.findViewById<View>(R.id.mark_value_vh)

                if (fieldItem == EmptyCustomFieldItem.getInstance()) {
                    valueVH.visible()
                    value.invisible()
                } else {
                    valueVH.gone()
                    value.visible()
                    // 鬼知道后台会返回啥样的数据，先 catch 为敬
                    kotlin.runCatching {
                        value.text = fieldItem.itemContent.content
                    }
                    kotlin.runCatching {
                        value.setTextColor(Color.parseColor(fieldItem.itemContent.color))
                    }
                }
            }
        } else {
            exContainer.gone()
        }
    }

    // 是否是全部列表
    private fun isAllPage(): Boolean {
        return adapter.pageStatus == TaskStatusEnum.ALL
    }

    // 是否是删除列表
    private fun isDelPage(): Boolean {
        return adapter.pageStatus == TaskStatusEnum.DELETED
    }

    private fun showCover(joyWork: JoyWork): Boolean {
        return joyWork.isDeleted
    }

    private fun checkboxSuccess(tmp: JoyWork) {
        // 全部的时候，不移除
        if (isAllPage()) {
            tmp.expandableGroup.notifyItemChange()
            adapter.notifyDataSetChanged()
        } else {
            tmp.expandableGroup.realItems.remove(tmp)
            adapter.processor.refreshData()
            tmp.expandableGroup.notifyItemChange()
            if (adapterPosition >= 0 && adapterPosition < adapter.processor.getData().size) {
                adapter.notifyItemRemoved(adapterPosition)
            } else {
                adapter.notifyDataSetChanged()
            }
        }
    }

    fun onBeginDrag() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.JOYWORK_START_DRAG,
            JoyWorkConstant.JOYWORK_START_DRAG
        )
        adapter.processor.backup(itemView.getTag(R.id.jdme_tag_id) as JoyWork)
        itemView.setBackgroundColor(Color.parseColor("#F6F6F6"))
    }

    fun onEndDrag() {
        adapter.clearAllMountItems()
        val item = itemView.getTag(R.id.jdme_tag_id) as JoyWork
        itemView.setBackgroundColor(Color.TRANSPARENT)
        // 前后没挪位置
        if (!adapter.swaped) {
            return
        }
        adapter.swaped = false
        if (item.expandableGroup.id != BlockTypeEnum.IN_PLAN.code) {
            adapter.sortJoyWork2(item)
        } else {
            // 计划组看是否要选择计划时间
            adapter.processor.correctPlanTime(item)
            if ((item.planTime ?: -1) < 0) {
                // 先选时间
                item.selectPlanTime(adapter.fg) {
                    if (it == -1L) { // 没选择时间
                        adapter.restore(item)
                    } else {
                        item.planTime = it
                        adapter.sortJoyWork2(item)
                    }
                }
            } else {
                adapter.sortJoyWork2(item)
            }
        }
    }
}