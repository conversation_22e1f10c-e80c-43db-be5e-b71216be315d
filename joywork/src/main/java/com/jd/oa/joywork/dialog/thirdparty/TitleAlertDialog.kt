package com.jd.oa.joywork.dialog.thirdparty

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.utils.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.dialog.DialogHelper
import com.jd.oa.joywork.isLegalList
import java.lang.IllegalArgumentException

class TitleAlertDialog(context: Context, private val config: AlertChoiceConfig) :
    AppCompatDialog(context, R.style.JoyWorkAlertDialogStyle) {

    private lateinit var mContentView: View
    private lateinit var mMsgView: TextView
    private lateinit var mOkTextView: TextView

    init {
        window?.apply {
            decorView.setBackgroundColor(Color.TRANSPARENT)
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = CommonUtils.getScreentWidth(context) * 62 / 75
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            attributes = layoutParams
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    private var mDialogHelper: DialogHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mDialogHelper = DialogHelper(this)
        mDialogHelper?.register()
        mContentView =
            context.inflater.inflate(R.layout.joywork_title_alert, null)
        setContentView(mContentView)
        window?.decorView?.setBackgroundColor(Color.TRANSPARENT)
        mContentView.findViewById<ViewGroup>(R.id.root)?.apply {
            background =
                DrawableEx.roundSolidRect(Color.WHITE, CommonUtils.dp2FloatPx(config.bgCorner))
        }
        mMsgView = mContentView.findViewById(R.id.message)
        mMsgView.text = getStr(config.msgRes, config.msgStr)

        mOkTextView = mContentView.findViewById(R.id.ok)
        mOkTextView.text = getStr(config.okRes, config.okStr)

        getOkClick()?.apply {
            mOkTextView.setOnClickListener {
                this.invoke(this@TitleAlertDialog)
            }
        }

        mContentView.findViewById<TextView>(R.id.cancel).setOnClickListener {
            cancel()
        }
        val choiceContainer = mContentView.findViewById<LinearLayout>(R.id.choice_container)
        if (config.actions.isLegalList()) {
            choiceContainer.visible()
            addChoices(choiceContainer)
        } else {
            choiceContainer.gone()
        }
    }

    private fun addChoices(parent: ViewGroup) {
        config.actions?.forEachIndexed { index, it ->
            val item = context.inflater.inflate(
                R.layout.joywork_alert_content,
                parent,
                false
            )
            val tv = item.findViewById<TextView>(R.id.choice_item)
            tv.text = it
            parent.addView(item)
        } ?: parent.gone()
    }

    private fun getStr(res: Int, str: String?): String {
        if (res <= 0 && str == null) {
            throw IllegalArgumentException()
        }
        return str ?: context.string(res)
    }

    private fun getOkClick(): ((TitleAlertDialog) -> Unit)? {
        return if (config.autoDismiss) { dialog ->
            dismiss()
            config.okShitClick?.invoke(dialog)
        } else config.okShitClick
    }

    override fun dismiss() {
        super.dismiss()
        mDialogHelper?.unregister()
    }
}

class AlertChoiceConfig : ShitAlertDialogConfig<TitleAlertDialog>() {
    var actions: List<String>? = null
}