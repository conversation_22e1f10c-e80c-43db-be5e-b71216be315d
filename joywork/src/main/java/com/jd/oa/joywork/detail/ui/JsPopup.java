package com.jd.oa.joywork.detail.ui;

import android.app.Activity;
import android.app.Dialog;
import android.os.Handler;
import android.os.Looper;
import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.joywork.JoyWorkDialog;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import org.json.JSONObject;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsPopup {
    public static final String DOMAIN = "popup";
    private final Handler main = new Handler(Looper.getMainLooper());

    @JavascriptInterface
    public void alert(final Object args, final CompletionHandler<Object> handler) {
        showDialog((JSONObject) args, handler);
    }

    @JavascriptInterface
    public void confirm(final Object args, final CompletionHandler<Object> handler) {
        showDialog((JSONObject) args, handler);
    }

    @JavascriptInterface
    public void toast(final Object args, final CompletionHandler<Object> handler) {
        main.post(new Runnable() {
            @Override
            public void run() {
                try {
                    String message = ((JSONObject) args).optString("message");
                    ToastUtils.showToast(message);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void showDialog(final JSONObject args, final CompletionHandler<Object> handler) {
        main.post(new Runnable() {
            @Override
            public void run() {
                try {
                    String title = args.optString("title");
//                    String message = args.optString("message");
//                    String cancel = args.optString("cancel");
//                    String confirm = args.optString("confirm");
                    if (title == null || title.length() == 0) {
                        title = AppBase.getAppContext().getString(R.string.me_info_title);
                    }
//                    if (cancel == null || cancel.length() == 0) {
//                        cancel = AppBase.getAppContext().getString(R.string.me_cancel);
//                    }
//                    if (cancel == null || cancel.length() == 0) {
//                        cancel = AppBase.getAppContext().getString(R.string.me_cancel);
//                    }
//                    if (confirm == null || confirm.length() == 0) {
//                        confirm = AppBase.getAppContext().getString(R.string.me_ok);
//                    }
                    final Activity activity = AppBase.getTopActivity();
                    if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                        return;
                    }
                    JoyWorkDialog.INSTANCE.showAlertDialog(title, new Function1<Dialog, Unit>() {
                        @Override
                        public Unit invoke(Dialog dialog) {
                            handler.complete("true");
                            return null;
                        }
                    });
//                    DialogUtils.showAlertDialog(title, message, confirm, cancel, new DialogInterface.OnClickListener() {
//                        @Override
//                        public void onClick(DialogInterface dialog, int which) {
//                            handler.complete("true");
//                        }
//                    }, null);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

}
