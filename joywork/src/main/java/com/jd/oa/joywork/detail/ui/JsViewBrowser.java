package com.jd.oa.joywork.detail.ui;

import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.fragment.js.hybrid.JsBrowser;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.utils.CommonUtils;

import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "RedundantSuppression"})
class JsViewBrowser extends JsBrowser {
    //这个接口是ScrollView嵌套WebView滚动专用，给待办详情页面使用的
    private final TaskDetailFragment taskDetailFragment;

    public JsViewBrowser(JsSdkKit jsSdkKit, TaskDetailFragment taskDetailFragment, JMEWebview jmeWebview) {
        super(jmeWebview, jsSdkKit, null, null, null, null);
        this.taskDetailFragment = taskDetailFragment;
    }

    @JavascriptInterface
    @Override
    public void onContentHeightChange(Object args, final CompletionHandler<Object> handler) {
        try {
            if (webView == null || !webView.isAttachedToWindow()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            int height = jsonObject.optInt("height");
            if (height > 0) {
                final ViewGroup.LayoutParams linearParams2 = webView.getLayoutParams();
                linearParams2.height = CommonUtils.dp2px(height);
                webView.post(new Runnable() {
                    @Override
                    public void run() {
                        webView.setLayoutParams(linearParams2);
                    }
                });
            }
            taskDetailFragment.sendEventToWeb("NATIVE_EVENT_PAGE_ANCHOR");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void jumpAnchor(Object args, final CompletionHandler<Object> handler) {
        try {
            if (webView == null || !webView.isAttachedToWindow() || args == null) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            JSONObject position = jsonObject.optJSONObject("position");
            boolean showKeyboard = jsonObject.optBoolean("showKeyboard");
            if (position == null) {
                return;
            }
            final int y = position.optInt("y");
            if (y > 0) {
                taskDetailFragment.scrollWebToReplyY(showKeyboard, CommonUtils.dp2px(y), new ScrollCallback() {
                    @Override
                    public void finish(int y) {
                        JSONObject jsonObject = new JSONObject();
                        try {
                            jsonObject.put("height", CommonUtils.px2dp(y));
                            handler.complete(jsonObject);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
