package com.jd.oa.joywork.team;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.jd.oa.BaseActivity;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.detail.data.TaskDetailWebservice;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.team.bean.ProjectList;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;
import com.jd.oa.utils.ActionBarHelper;

import java.util.ArrayList;

public class SelectProjectTaskGroupActivity extends BaseActivity {
    private RecyclerView rvProjectList;
    private LinearLayout llEmpty;
    private ProjectListAdapter projectListAdapter;
    private RefreshRecyclerLayout refreshView;
    private String taskId;
    private ArrayList<JoyWorkDetail.Project> linkedProjects;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_select_project_task_group);
        taskId = getIntent().getStringExtra("taskId");
        linkedProjects = getIntent().getParcelableArrayListExtra("linkedProjects");
        initView();
        getProjectList(0);
    }

    private void initView() {
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            actionBar.hide();
        }
        findViewById(R.id.iv_me_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SelectProjectTaskGroupActivity.this.finish();
            }
        });
        rvProjectList = findViewById(R.id.recycleView);
        refreshView = findViewById(R.id.refreshView);
        llEmpty = findViewById(R.id.ll_list_empty);
        rvProjectList.setLayoutManager(new LinearLayoutManager(this));
        projectListAdapter = new ProjectListAdapter(this, projectList);
        rvProjectList.setAdapter(projectListAdapter);
        projectListAdapter.setOnItemClickListener(new ProjectListAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(ProjectList.Content.List list) {
                Intent intent = new Intent(SelectProjectTaskGroupActivity.this, SelectTaskGroupActivity.class);
                intent.putExtra("taskId", taskId);
                intent.putExtra("projectId", list.getProjectId());
                intent.putExtra("projectName", list.getTitle());
                intent.putParcelableArrayListExtra("groups", list.getGroups());
                intent.putParcelableArrayListExtra("linkedProjects", linkedProjects);
                startActivityForResult(intent, 1001);
            }


        });
    }


    private ArrayList<ProjectList.Content.List> projectList = new ArrayList();

    public void getProjectList(int pageSize) {
        TaskDetailWebservice.getProjectList(pageSize, new TaskDetailWebservice.TaskCallback() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                Activity activity = (Activity) SelectProjectTaskGroupActivity.this;
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                super.onSuccess(info);
                Log.e(TAG, "onSuccess: " + info.result);
                Gson gson = new Gson();
                ProjectList list = gson.fromJson(info.result, ProjectList.class);
                if (list.getContent().getList() != null && list.getContent().getList().size() > 0) {
                    llEmpty.setVisibility(View.GONE);
                    rvProjectList.setVisibility(View.VISIBLE);
                    projectList.clear();
                    projectList.addAll(list.getContent().getList());
                    projectListAdapter.notifyDataSetChanged();
                } else {
                    llEmpty.setVisibility(View.VISIBLE);
                    rvProjectList.setVisibility(View.GONE);
                }
                if (refreshView.isRefreshing()) {
                    refreshView.setRefreshing(false);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                Activity activity = SelectProjectTaskGroupActivity.this;
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                if (refreshView.isRefreshing()) {
                    refreshView.setRefreshing(false);
                }
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case 1001:
                if (resultCode == 200) {
                    if (data != null && data.hasExtra("project")) {
                        setResult(RESULT_OK, data);
                        finish();
                        return;
                    }
                    setResult(200, getIntent());
                    finish();
                }
                break;
        }
    }
}