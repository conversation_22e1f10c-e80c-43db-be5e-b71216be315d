package com.jd.oa.joywork.group

import android.graphics.Color
import android.os.Bundle
import android.view.*
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.executor.ExecutorListActivity
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.joinLegalString
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import kotlinx.android.synthetic.main.joywork_select_group_roster_dialog.*

class SelectGroupRosterDialog(
    private val activity: SelectGroupRosterActivity,
    private val data: ArrayList<MemberEntityJd>
) :
    AppCompatDialog(activity, R.style.JoyWorkDialogStyle) {
    private lateinit var mContentView: View

    init {
        window?.apply {
            decorView.setBackgroundColor(Color.TRANSPARENT)
            requestFeature(Window.FEATURE_NO_TITLE)
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = CommonUtils.dp2px(428.0f)
            layoutParams.gravity = Gravity.BOTTOM
            attributes = layoutParams
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mContentView = context.inflater.inflate(R.layout.joywork_select_group_roster_dialog, null)
        setContentView(mContentView)
        mTitle.text = activity.resources.getString(R.string.joywork_select_group_title, data.size)
        mClear.setOnClickListener { activity.clear() }
        mRecyclerView.layoutManager =
            LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        mRecyclerView.adapter = Adapter()
    }

    private val itemRemove = View.OnClickListener {
        val d = it.tag as MemberEntityJd
        activity.remove(d)
    }

    fun dataChange(){
        mRecyclerView.adapter?.notifyDataSetChanged()
        mTitle.text = activity.resources.getString(R.string.joywork_select_group_title, data.size)
    }

    inner class Adapter() : RecyclerView.Adapter<VH>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
            val inflate: View = LayoutInflater.from(context)
                .inflate(R.layout.joywork_collaborator_activity_item, parent, false)
            return VH(inflate)
        }

        override fun getItemCount() = data.size

        override fun onBindViewHolder(holder: VH, position: Int) {
            val c = data[position]
            holder.name.text = c.name ?: c.mId ?: ""
            holder.department.text = listOf(c.directDepartment, c.titleName).joinLegalString()
            holder.divider.visibility = if (position == data.size - 1) View.GONE else View.VISIBLE
            holder.action.tag = c
            holder.action.setText(R.string.icon_prompt_close)
            holder.action.setOnClickListener(itemRemove)
            JoyWorkViewItem.avatar(holder.avatar, c.avatar)
            holder.avatar.tag = c
            holder.avatar.setOnClickListener {
            }
            holder.hat.gone()
        }
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView = view.findViewById(R.id.image)
        val name: TextView = view.findViewById(R.id.name)
        val action: TextView = view.findViewById(R.id.action)
        val department: TextView = view.findViewById(R.id.department)
        val divider: View = view.findViewById(R.id.divider)
        val hat: ImageView = view.findViewById(R.id.hat)
    }
}