package com.jd.oa.joywork.team

import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroup
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.team.bean.Group

interface IInsertJoyWork {
    /**
     * 该功能已在 ProjectBase 中实现了，子类实现该接口只表示拥有该功能
     */
    fun insert(joyWork: JoyWork, groupIds: List<String>)
}

interface IBridge {
    fun addNetworkFinishObserver(runnable: Runnable?)
}

interface ICustomField {
    fun getCustomField(): List<CustomFieldGroup>?
}