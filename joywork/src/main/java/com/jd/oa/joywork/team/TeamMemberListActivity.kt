package com.jd.oa.joywork.team

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.bean.ProjectMembersList
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.joywork.detail.DialogManager
import com.jd.oa.joywork.detail.data.TaskDetailRepository
import com.jd.oa.joywork.detail.data.TaskDetailWebservice
import com.jd.oa.joywork.detail.data.entity.DelMemberSend
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.dialog.biz.JoyWorkTeamMemberActionDialog
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.toSelfInfo
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.TeamUserRole
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.utils.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.model.TenantCode

/**
 * 共享成员列表
 */
class TeamMemberListActivity : AppCompatActivity() {
    private lateinit var rv: RecyclerView
    var clickPosition: Int = -1
    var projectId: String? = null
    var isSetting: Boolean = false
    var permissions: List<String>? = null

    companion object {

        fun inflateIntent(intent: Intent, projectId: String, permissions: List<String>) {
            intent.putExtra("projectId", projectId)
            intent.putStringArrayListExtra(
                "permissions",
                permissions as java.util.ArrayList<String>
            )
        }

        private fun TeamMemberListActivity.getTeamId(): String {
            return intent!!.getStringExtra("projectId")!!
        }
    }

    private fun getExecutors(): ArrayList<Members> {
        return (OneTimeDataRepo.data as? List<*>)?.run {
            return if (isEmpty()) {
                ArrayList()
            } else {
                if (first() is Members) {
                    ArrayList(this as List<Members>)
                } else {
                    ArrayList()
                }
            }
        } ?: ArrayList()
    }

    private val members: ArrayList<Members> by lazy {
        getExecutors()
    }

    private val result: ArrayList<Members> = ArrayList()
    private val titleView: TextView by lazy {
        findViewById<TextView>(R.id.title)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        projectId = intent.getStringExtra("projectId")
        permissions = intent.getStringArrayListExtra("permissions")
        isSetting = !projectId.isBlankOrNull()
        result.addAll(members)
        kotlin.runCatching {
            actionBar?.hide()
        }
        kotlin.runCatching {
            supportActionBar?.hide()
        }
        setContentView(R.layout.joywork_collaborator_activity)
        updateTitle()
        findViewById<View>(R.id.back).setOnClickListener { finish() }
        findViewById<View>(R.id.add).setOnClickListener {
            add()
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CHECKLIST_INFO_MEMBER_CHOOSE_MEMBER
                )
            }

        }
        if (permissions != null && (!permissions!!.contains(ProjectPermissionEnum.MEMBER.code))) {
            findViewById<View>(R.id.add).invisible()
        }
        rv = findViewById<RecyclerView>(R.id.rv).apply {
            layoutManager = LinearLayoutManager(
                this@TeamMemberListActivity,
                LinearLayoutManager.VERTICAL,
                false
            )

            adapter = if (members.isEmpty()) {
                JoyWorkEmptyAdapter.empty(
                    this@TeamMemberListActivity,
                    R.string.joywork_relation_empty,
                    R.drawable.joywork_executor_empty
                )
            } else {
                TeamMemberListAdapter(this@TeamMemberListActivity, members)
            }
        }
    }

    private fun updateTitle() {
        titleView.text = string(R.string.joywork_team_member_list) + "(" + result.size + ")"
    }

    private fun add() {
        val selected = ArrayList<MemberEntityJd>()
        if (result.isNotEmpty()) {
            result.forEach {
                it.emplAccount?.apply {
                    val jd = MemberEntityJd()
                    jd.mApp = it.app
                    jd.mId = it.emplAccount
                    selected.add(jd)
                }
            }
        }
        val service = AppJoint.service(ImDdService::class.java)
        val entity = MemberListEntityJd()
        val appIds = ArrayList<String?>()
        JoyWorkEx.getAppIds(appIds = appIds)
        entity.setSpecifyAppId(appIds)
        entity.setFrom(UIHelperConstantJd.TYPE_ADD_MEMBER).setShowConstantFilter(true)
            .setConstantFilter(selected)
            .setShowSelf(true).setOptionalFilter(null).setShowOptionalFilter(false)
            .setMaxNum(Int.MAX_VALUE)
        service.gotoMemberList(
            this,
            100,
            entity,
            object : Callback<java.util.ArrayList<MemberEntityJd?>?> {
                override fun onSuccess(selected: ArrayList<MemberEntityJd?>?) {
                    if (selected != null) {
                        val membersNet: ArrayList<Members> = ArrayList()
                        for (memberEntityJd in selected) {
                            memberEntityJd?.apply {
                                val member = Members()
                                member.fromDD(memberEntityJd)
                                member.permission = TeamUserRole.EDITOR.permission
                                membersNet.add(member)
                            }
                        }
                        if (membersNet.isEmpty()) {
                            return
                        }
                        if (isSetting) {
                            ProjectRepo.addProjectMember(projectId.toString(), membersNet, object :
                                RepoCallback<ProjectMembersList> {
                                override fun result(
                                    t: ProjectMembersList?,
                                    errorMsg: String?,
                                    success: Boolean
                                ) {
                                    if (success) {
                                        (rv.adapter as? TeamMemberListAdapter)?.apply {
                                            updateCollaborator(t?.members!!)
                                            updateTitle()
                                        } ?: showContent(result)
                                        updateTitle()
                                    } else {
                                        ToastUtils.showInfoToast(errorMsg)
                                    }
                                }
                            })
                        } else {
                            (rv.adapter as? TeamMemberListAdapter)?.apply {
                                updateCollaborator(membersNet)
                                updateTitle()
                            } ?: showContent(result)
                            updateTitle()
                        }
                    }
                }

                override fun onFail() {

                }
            })
    }

    private fun addIfAbsent(dst: ArrayList<Members>, src: List<Members>?) {
        src?.forEach { outer ->
            val first = dst.firstOrNull { inner ->
                inner.emplAccount == outer.emplAccount
            }
            if (first == null) {
                dst.add(outer)
                result.add(outer)
            }
        }
    }

    private fun showContent(all: ArrayList<Members>) {
        updateTitle()
        rv.adapter = TeamMemberListAdapter(this@TeamMemberListActivity, all)
    }

    private fun showRemoveDialog(member: Members) {
        DialogManager.showRemove(this@TeamMemberListActivity) {
            removeNet(member)
        }
    }

    // 移除协作人
    private fun removeNet(member: Members) {
        val send = DelMemberSend()
        send.taskId = getTeamId()
        send.members = listOf(member)
        TaskDetailRepository.getInstance()
            .delTaskMembers(send, object : TaskDetailWebservice.TaskCallback() {
                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    if (!hasError) {
                        result.removeAll {
                            it.emplAccount == member.emplAccount
                        }
                        (rv.adapter as TeamMemberListAdapter).removeCollaborator(member)
                        updateTitle()
                    }
                }
            })
    }

    override fun finish() {
        OneTimeDataRepo.data = result
        setResult(Activity.RESULT_OK)
        super.finish()
    }

    fun showEmpty() {
        rv.adapter = JoyWorkEmptyAdapter.empty(
            this@TeamMemberListActivity,
            R.string.joywork_relation_empty,
            R.drawable.joywork_executor_empty
        )
    }

    /**
     * 判断当前人是否已是协作人
     */
    private fun duplicate(uId: String): Boolean {
        return result.firstOrNull {
            it.emplAccount == uId
        } != null
    }

    inner class TeamMemberListAdapter(val context: Context, dataC: ArrayList<Members>) :
        RecyclerView.Adapter<VH>() {

        private val data = ArrayList<Members>(dataC)

        private val listener = View.OnClickListener {

            val c = it.tag as Members
            clickPosition = result.indexOf(c)
//            showRemoveDialog(c)
            JoyWorkTeamMemberActionDialog.show(
                this@TeamMemberListActivity,
                object : JoyWorkTeamMemberActionDialog.OnItemSelectCallback {
                    override fun onSelectAction(permission: TeamUserRole) {
                        if (isSetting) {
                            val member = result[clickPosition]
                            member.permission = permission.permission
                            ProjectRepo.changeMemberPermission(projectId!!, member) { it, msg ->
                                if (it) {
                                    result[clickPosition].permission = permission.permission
                                    showContent(result)
                                } else {
                                    ToastUtils.showInfoToast(msg)
                                }
                            }
                        } else {
                            result[clickPosition].permission = permission.permission
                            showContent(result)
                        }
                    }

                    override fun onRemove() {
                        if (isSetting) {
                            val list: ArrayList<Members> = ArrayList()
                            list.add(result[clickPosition])
                            ProjectRepo.delProjectMember(projectId!!, list) { it, msg ->
                                if (it) {
                                    result.removeAt(clickPosition)
                                    showContent(result)
                                } else {
                                    ToastUtils.showInfoToast(msg)
                                }
                            }
                        } else {
                            result.removeAt(clickPosition)
                            showContent(result)
                        }
                    }
                },
                c
            )
        }

        fun updateCollaborator(members: List<Members>) {
            addIfAbsent(data, members)
            notifyDataSetChanged()
        }

        fun removeCollaborator(c: Members) {
            val index = data.indexOfFirst {
                it.isSamePerson(c)
            }
            if (index >= 0) {
                data.removeAt(index)
                if (data.isEmpty()) {
                    showEmpty()
                } else {
                    notifyItemRemoved(index)
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
            val inflate: View = LayoutInflater.from(context)
                .inflate(R.layout.joywork_activity_team_member_list_item, parent, false)
            return VH(inflate)
        }

        override fun getItemCount() = data.size

        override fun onBindViewHolder(holder: VH, position: Int) {
            val c = data[position]
            holder.name.text = c.realName ?: ""
            holder.department.text = c.deptInfo?.fullName ?: ""
            holder.divider.visibility == if (position == data.size - 1) View.GONE else View.VISIBLE
            if (c.permission != TeamUserRole.CREATOR.permission) {
                holder.action.isClickable = true
                holder.action.tag = c
                holder.action.setOnClickListener(listener)
                holder.arrowDown.visible()
                holder.situation.text =
                    if (c.permission == TeamUserRole.EDITOR.permission) context.getString(R.string.joywork_can_edit) else context.getString(
                        R.string.joywork_team_viewer
                    )
            } else {
                holder.situation.text = context.getString(R.string.creator)
                holder.arrowDown.invisible()
                holder.action.isClickable = false
            }
            JoyWorkViewItem.avatar(holder.avatar, c.headImg)
            holder.avatar.tag = c
            holder.avatar.setOnClickListener {
                val m = it.tag as Members
                //群不跳转
                if (m.type == 2) {
                    return@setOnClickListener
                }
                <EMAIL>(
                    m.ddAppId,
                    if (TenantCode.getByDDAppId(m.ddAppId) != null) {
                        m.emplAccount
                    } else {
                        m.userId
                    }
                )
            }
            if (isSetting) {
                holder.action.isClickable = c.canEdit
                holder.arrowDown.visibility =
                    if (c.canEdit && c.permission != 1) View.VISIBLE else View.INVISIBLE
            }
        }

        fun clean() {
            data.clear()
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val avatar: ImageView by lazy { view.findViewById<ImageView>(R.id.image) }
        val name: TextView by lazy { view.findViewById<TextView>(R.id.name) }
        val action: LinearLayout by lazy { view.findViewById<LinearLayout>(R.id.action) }
        val department: TextView by lazy { view.findViewById<TextView>(R.id.department) }
        val divider: View by lazy { view.findViewById<View>(R.id.divider) }
        val arrowDown: View by lazy { view.findViewById<View>(R.id.iv_arrow_down) }
        val situation: TextView by lazy { view.findViewById<TextView>(R.id.tv_situation) }
    }
}