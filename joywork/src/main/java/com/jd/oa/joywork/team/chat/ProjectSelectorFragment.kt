package com.jd.oa.joywork.team.chat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.R
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.view.JoyWorkLoadMoreFooter
import com.jd.oa.joywork.view.realAdapter
import com.jd.oa.joywork.view.red
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.vertical

class ProjectSelectorFragment : BaseFragment() {
    private val mViewModel: ProjectSelectorViewModel by activityViewModels()
    private val mRv: RecyclerView by lazy {
        requireView().findViewById(R.id.mRecyclerView)
    }
    private val mSwipe: SwipeRefreshLayout by lazy {
        requireView().findViewById(R.id.mSwipe)
    }
    private var mPullUpLoadHelper: PullUpLoadHelper? = null
    private var mFooterView: JoyWorkLoadMoreFooter? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.joywork_project_selector_frg, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        mRv.vertical()
        mSwipe.red(requireActivity())
        mSwipe.setOnRefreshListener {
            mSwipe.isRefreshing = true
            mViewModel.initList()
        }
        mViewModel.initList()
        initViewModels()
    }

    private fun initViewModels() {
        mViewModel.mProjectLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is ProjectSelectorViewModel.DataState.DataChanged -> {
                    val adapter = mRv.realAdapter()
                    if (adapter is ProjectSelectorAdapter) {//成功后下拉刷新，只更新数据
                        adapter.setData(it.data)
                    } else { // 失败后下拉刷新
                        mRv.adapter = ProjectSelectorAdapter(requireContext(), "", it.data) {
                            mViewModel.updateCheckedItem(it)
                        }
                        setupLoadMore()
                    }
                }

                is ProjectSelectorViewModel.DataState.LoadMoreFailure -> {
                    ToastUtils.showToast(it.msg)
                }

                is ProjectSelectorViewModel.DataState.RefreshFailure -> {
                    val adapter = mRv.realAdapter()
                    if (adapter is ProjectSelectorAdapter) {
                        ToastUtils.showToast(it.msg)
                    } else {
                        mRv.adapter = JoyWorkEmptyAdapter.error(requireContext(), it.msg)
                    }
                }
            }
        }
        mViewModel.mEffectLiveData.observe(viewLifecycleOwner) {
            if (it is ProjectSelectorViewModel.Effect.LoadMoreFinish) {
                loadMoreOnceFinish(it.completed)
            } else if (it is ProjectSelectorViewModel.Effect.RefreshEffect) {
                mSwipe.isRefreshing = it.isRefreshing
            }
        }
        mViewModel.mCheckedProjectLiveData.observe(viewLifecycleOwner) {
            (mRv.realAdapter() as? ProjectSelectorAdapter)?.updateCheckedItem(it)
        }
    }

    private fun setupLoadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(mRv) {
            mViewModel.loadMore()
        }
        mFooterView = JoyWorkLoadMoreFooter(requireContext())
        mPullUpLoadHelper?.setLoadFooter(mFooterView)
    }

    private fun loadMoreOnceFinish(finish: Boolean) {
        val helper = mPullUpLoadHelper ?: return
        if (finish) {
            helper.setComplete()
        } else {
            helper.setLoaded()
        }
    }
}