package com.jd.oa.joywork.issue

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.repo.CacheStrategy
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.repo.LoadingViewStrategy
import com.jd.oa.joywork.self.base.NetType
import com.jd.oa.joywork.self.base.SelfBaseBlockFragment
import com.jd.oa.joywork.self.base.SelfPageEnum
import com.jd.oa.joywork.self.group.JoyWorkGroupEx
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

/**
 * joywork： 风险问题列表
 * 2022年06月14日：
 * 理论上它不应该直接继承 SelfBaseBlockFragment，应该在 SelfBaseBlockFragment 上面抽取出另一层做为 SelfBaseBlockFragment 与 MyIssueFragment 的父类
 * 后面有时间再说
 */
class MyIssueFragment : SelfBaseBlockFragment() {

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            refreshData(LoadingViewStrategy.SHOW, CacheStrategy.WRITE, NetType.REFRESH)
        }
    }

    private val mRiskUpdateObserver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            refreshData(LoadingViewStrategy.HIDE, CacheStrategy.WRITE, NetType.REFRESH)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(receiver, IntentFilter(JoyWorkCommonConstant.REFRESH_UPDATE_RISK))
        mRiskUpdateObserver observeRiskUpdate requireContext()
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun tips(viewGroup: ViewGroup) {
        if (sp.get(JoyWorkPreference.KV_ENTITY_ISSUE_TIPS)) {
            viewGroup.gone()
        } else {
            viewGroup.visible()
            val view =
                requireContext().inflater.inflate(R.layout.jowork_issue_tips, viewGroup, true)
            view.findViewById<View>(R.id.close).setOnClickListener {
                viewGroup.gone()
                sp.put(JoyWorkPreference.KV_ENTITY_ISSUE_TIPS, true)
            }
        }
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return TaskListTypeEnum.QUESTION
    }

    override fun onRefreshData(
        loadingViewStrategy: LoadingViewStrategy,
        strategy: CacheStrategy,
        netType: NetType
    ) {
        if (isSwipeRefreshLayoutInited && loadingViewStrategy.show()) {
            mSwipeRefreshLayout.isRefreshing = true
        }
        JoyWorkRepo.listIssue(
            0,
            callback = getNetCallback(netType)
        )
    }

    override fun getEmptyAdapter(): JoyWorkEmptyAdapter {
        return JoyWorkEmptyAdapter.empty(
            requireContext(),
            R.string.joywork_issue_empty,
            R.drawable.joywork_issue_empty
        )
    }

    override fun onLoadMore(blockType: String, callback: JoyWorkVMCallback, offset: Int) {
        JoyWorkRepo.listIssue(
            offset,
            callback = callback,
            regionTypes = listOf(blockType.toInt())
        )
    }

    override fun getGroups(): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        return JoyWorkGroupEx.getIssueGroups(requireContext())
    }

    override fun getUserRole(): TaskUserRole {
        return TaskUserRole.OWNER
    }

    override fun getDefaultStatus(): TaskStatusEnum {
        return TaskStatusEnum.ALL
    }

    override fun onDestroyView() {
        super.onDestroyView()
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(receiver)
        mRiskUpdateObserver stopObserveRiskUpdate requireContext()
    }

    override fun getPageId(): SelfPageEnum {
        return SelfPageEnum.MY_ISSUE
    }

    override fun getTitleType(joyWorkTitle: JoyWorkTitle): Int {
        return 3
    }

    override fun finishAction(): List<String> {
        return mutableListOf(ProjectConstant.UNFINISH_ACTION)
    }
}