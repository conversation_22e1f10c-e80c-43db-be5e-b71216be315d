package com.jd.oa.joywork.team.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewParent;
import android.widget.FrameLayout;
import android.widget.OverScroller;

/*
可以换成线性布局。留待优化
 */
public class ItemContainer extends FrameLayout {

    private final int mScaledTouchSlop;
    private int mLastX;
    private int mDownX;
    private int mDownY;
    private boolean mDragging;
    private final OverScroller mScroller;
    private VelocityTracker mVelocityTracker;
    private final int mScaledMaximumFlingVelocity;

    private int mActivePointerId = INVALID_POINTER;
    private final int mMinimumVelocity;
    private static final int INVALID_POINTER = -1;

    public ItemContainer(Context context) {
        this(context, null);
    }

    public ItemContainer(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ItemContainer(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        setClickable(true);
        ViewConfiguration configuration = ViewConfiguration.get(getContext());
        mScaledTouchSlop = configuration.getScaledTouchSlop();
        mScaledMaximumFlingVelocity = configuration.getScaledMaximumFlingVelocity();
        mMinimumVelocity = configuration.getScaledMinimumFlingVelocity();
        mScroller = new OverScroller(getContext());
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        View child = getChildAt(0);
        child.measure(MeasureSpec.makeMeasureSpec(MeasureSpec.getSize(widthMeasureSpec), MeasureSpec.UNSPECIFIED), heightMeasureSpec);
        setMeasuredDimension(MeasureSpec.makeMeasureSpec(child.getMeasuredWidth(), MeasureSpec.EXACTLY), getMeasuredHeight());
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (fixPos()) {
            return super.onInterceptTouchEvent(ev);
        }
        boolean isIntercepted = super.onInterceptTouchEvent(ev);

        int action = ev.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN: {
                mActivePointerId = ev.getPointerId(0);
                if (!mScroller.isFinished()) mScroller.abortAnimation();
                mDownX = mLastX = (int) ev.getX();
                mDownY = (int) ev.getY();
                return false;
            }
            case MotionEvent.ACTION_MOVE: {
                final int activePointerIndex = ev.findPointerIndex(mActivePointerId);
                if (activePointerIndex == -1) {
                    break;
                }
                int disX = (int) (ev.getX(activePointerIndex) - mDownX);
                int disY = (int) (ev.getY(activePointerIndex) - mDownY);
                boolean r = Math.abs(disX) > mScaledTouchSlop && Math.abs(disX) > Math.abs(disY);
                if (r || mDragging) {
                    requestDisallowInterceptTouchEvent(true);
//                    final long now = SystemClock.uptimeMillis();
//                    super.onInterceptTouchEvent(MotionEvent.obtain(now, now,
//                            MotionEvent.ACTION_CANCEL, 0.0f, 0.0f, 0));
                    return true;
                }
            }
            case MotionEvent.ACTION_UP: {
                return isIntercepted;
            }
            case MotionEvent.ACTION_CANCEL: {
                if (!mScroller.isFinished()) mScroller.abortAnimation();
                return false;
            }
        }
        return isIntercepted;
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (fixPos()) {
            return super.onTouchEvent(ev);
        }
        if (mVelocityTracker == null) mVelocityTracker = VelocityTracker.obtain();
        mVelocityTracker.addMovement(ev);
        int action = ev.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN: {
                mDownX = mLastX = (int) ev.getX();
                mDownY = (int) ev.getY();
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                final int activePointerIndex = ev.findPointerIndex(mActivePointerId);
                if (activePointerIndex == -1) {
                    break;
                }
                int disX = (int) (mDownX - ev.getX(activePointerIndex));
                int disY = (int) (mDownY - ev.getY(activePointerIndex));
                int frameDeltaX = (int) (mLastX - ev.getX());
                mLastX = (int) ev.getX();
                if (!mDragging && Math.abs(disX) > Math.abs(disY) && Math.abs(disX) > mScaledTouchSlop) {
                    requestDisallowInterceptTouchEvent(true);
                    mDragging = true;
                }
                if (mDragging) {
                    scrollTo2(getScrollX() + frameDeltaX, false);
                    super.onTouchEvent(ev);
                    return true;
                }
                break;
            }
            case MotionEvent.ACTION_UP: {
                if (!mDragging) {
                    super.onTouchEvent(ev);
                }
                mDragging = false;
                mVelocityTracker.computeCurrentVelocity(1000, mScaledMaximumFlingVelocity);
                int initialVelocity = (int) mVelocityTracker.getXVelocity(mActivePointerId);

                if (getChildCount() > 0) {
                    if ((Math.abs(initialVelocity) > mMinimumVelocity)) {
                        fling(-initialVelocity);
                    }
                }
                mActivePointerId = INVALID_POINTER;
                mVelocityTracker.clear();
                mVelocityTracker.recycle();
                mVelocityTracker = null;
                requestDisallowInterceptTouchEvent(false);
                return true;
            }
            case MotionEvent.ACTION_CANCEL: {
                mDragging = false;
                requestDisallowInterceptTouchEvent(false);
                break;
            }
        }
        return super.onTouchEvent(ev);
    }

    public void fling(int velocityX) {
        if (getChildCount() > 0) {
            mScroller.fling(getScrollX(), 0, velocityX, 0, 0,
                    scrollYRange(), 0, 0);
        }
    }

    private void scrollTo2(int x, boolean fling) {
        if (getChildCount() > 0) {
            x = clamp(x);
            notifyParentSync(x, fling);
            postInvalidate();
        }
    }

    private int clamp(int dst) {
        if (dst < 0) {
            return 0;
        }
        return Math.min(dst, scrollYRange());
    }

    @Override
    public void computeScroll() {
        if (mScroller.computeScrollOffset()) {
            int x = mScroller.getCurrX();
            scrollTo2(x, true);
        }
    }

    /**
     * 水平滑动的最大距离
     */
    private int scrollYRange() {
        return Math.max(0, getWidth() - ((View) getParent()).getWidth());
    }

    private void notifyParentSync(int dx, boolean fling) {
        ViewParent parent = getParent();
        if (parent instanceof HRecyclerView) {
            ((HRecyclerView) parent).syncChildren(this, dx);
        }
    }

    private boolean mFixPos = false;

    public void setFixPos(boolean fixPos) {
        this.mFixPos = fixPos;
    }

    public boolean fixPos() {
        return mFixPos;
    }
}
