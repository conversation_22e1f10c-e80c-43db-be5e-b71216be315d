package com.jd.oa.joywork.dialog.biz

import android.app.Activity
import android.os.Bundle
import android.view.*
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TeamUserRole
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.dialog.DialogHelper
import com.jd.oa.utils.DeviceUtil
import com.jd.oa.utils.visible

/**
 * 团队成员权限弹窗
 */
class JoyWorkTeamMemberActionDialog(
    context: Activity,
    private val onItemSelectCallback: OnItemSelectCallback,
    val member: Members
) : AppCompatDialog(context, R.style.BottomDialogStyle), View.OnClickListener {

    lateinit var editor: LinearLayout
    lateinit var viewer: LinearLayout
    lateinit var remove: TextView
    lateinit var calcel: TextView

    companion object {
        fun show(
            activity: androidx.fragment.app.FragmentActivity,
            onItemSelectCallback: OnItemSelectCallback,
            member: Members
        ): JoyWorkTeamMemberActionDialog {
            val taskScreenDialog =
                JoyWorkTeamMemberActionDialog(activity, onItemSelectCallback, member)
            taskScreenDialog.show()
            return taskScreenDialog
        }
    }

    init {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        window?.apply {
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.BOTTOM
            attributes = layoutParams
        }
    }

    private var mDialogHelper: DialogHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mDialogHelper = DialogHelper(this)
        mDialogHelper?.register()
        val view =
            LayoutInflater.from(context).inflate(R.layout.joywork_team_member_action_dialog, null)
        setContentView(view)
        initView(view)
    }

    private fun initView(view: View) {
        val parent = view.parent as ViewGroup
        parent.setBackgroundResource(android.R.color.transparent)
        val window = window
        if (window != null) {
            val lp = window.attributes
            lp.width = DeviceUtil.getScreenWidth(context) // 宽度
            window.attributes = lp
        }
        view.apply {
            editor = findViewById(R.id.editor)
            viewer = findViewById(R.id.viewer)
            remove = findViewById(R.id.tv_remove)
            calcel = findViewById(R.id.tv_cancel)
        }
        if (member.permission == TeamUserRole.EDITOR.permission) {
            view.findViewById<View>(R.id.team_member_editor_select).visible()
        } else if (member.permission == TeamUserRole.VIEWER.permission) {
            view.findViewById<View>(R.id.jdme_id_myapply_dropdown_icon).visible()
        }
        editor.setOnClickListener(this)
        viewer.setOnClickListener(this)
        remove.setOnClickListener(this)
        calcel.setOnClickListener(this)
    }

    interface OnItemSelectCallback {
        fun onSelectAction(permission: TeamUserRole)
        fun onRemove()
    }

    override fun onClick(v: View?) {
        when (v) {
            editor -> {//可编辑
                onItemSelectCallback.onSelectAction(TeamUserRole.EDITOR)
                dismiss()
            }
            viewer -> {//仅查看
                onItemSelectCallback.onSelectAction(TeamUserRole.VIEWER)
                dismiss()
            }
            remove -> {//移除
                onItemSelectCallback.onRemove()
                dismiss()
            }
            this.calcel -> this.dismiss()
        }
    }

    override fun dismiss() {
        super.dismiss()
        mDialogHelper?.unregister()
    }
}