package com.jd.oa.joywork.notification

import android.content.Context
import android.graphics.Color

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.lib.un.basewidget.widget.simple.utils.DpiUtils
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.view.ExpandLinearLayout
import com.jd.oa.utils.*
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashSet

class NotificationAdapter(
    private val context: Context,
    data: List<JoyWorkNotification>,
    itemClick: (JoyWorkNotification) -> Unit
) : RecyclerView.Adapter<NotificationAdapter.VH>() {
    private val mDataInner = arrayListOf<JoyWorkNotification>()
    private val mCustomEventWidth =
        CommonUtils.getScreentWidth(context) - CommonUtils.dp2FloatPx(48)

    private val mItemClick = View.OnClickListener {
        itemClick.invoke(it.tag as JoyWorkNotification)
    }

    init {
        mDataInner.addAll(data)
    }

    private val mInflater = context.inflater

    fun markAllRead() {
        mDataInner.forEach {
            it.readStatus = JoyWorkNotification.READ
        }
        notifyDataSetChanged()
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mReadStatus = itemView.findViewById<View>(R.id.mReadStatus)
        val mLabel = itemView.findViewById<TextView>(R.id.mLabel)
        val mHeader = itemView.findViewById<ImageView>(R.id.mHeader)
        val mName = itemView.findViewById<TextView>(R.id.mName)
        val mTime = itemView.findViewById<TextView>(R.id.mTime)
        val mTaskName = itemView.findViewById<TextView>(R.id.mTaskName)
        val mCustomEventContainer = itemView.findViewById<ViewGroup>(R.id.mCustomEventContainer)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(
            mInflater.inflate(
                R.layout.joywork_notification_center_item,
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val notification = mDataInner[position]

        holder.itemView.tag = notification
        holder.itemView.setOnClickListener(mItemClick)

        if (notification.isRead) {
            holder.mReadStatus.invisible()
        } else {
            holder.mReadStatus.visible()
        }
        if (notification.label.isLegalString()) {
            holder.mLabel.visible()
            holder.mLabel.text = notification.label
        } else {
            holder.mLabel.gone()
        }
        if (notification.title.isLegalString()) {
            holder.mTaskName.visible()
            holder.mTaskName.text = notification.title
        } else {
            holder.mTaskName.gone()
        }

        JoyWorkViewItem.avatar(holder.mHeader, notification.operator?.headImg)

        val text = listOf(notification.operator?.realName, notification.event).joinLegalString(" ")
        if (text.isLegalString()) {
            holder.mName.visible()
            holder.mName.text = text
        } else {
            holder.mName.gone()
        }

        addCustomEvent(holder.mCustomEventContainer, notification.eventDetails)

        if (notification.createTime.isLegalTimestamp()) {
            holder.mTime.visible()
            holder.mTime.text =
                DateShowUtils.getTimeShowTextWithHourMinute(notification.createTime)
        } else {
            holder.mTime.gone()
        }
    }

    override fun getItemCount() = mDataInner.size
    fun refresh(dynamics: List<JoyWorkNotification>) {
        mDataInner.clear()
        mDataInner.addAll(dynamics)
        notifyDataSetChanged()
    }

    fun markRead(dyId: String) {
        mDataInner.firstOrNull {
            Objects.equals(it.dyId, dyId)
        }?.apply {
            markRead()
            <EMAIL>()
        }
    }

    fun mergeDown(dynamics: List<JoyWorkNotification>) {
        mergeInner(dynamics, false)
    }

    fun mergeUp(dynamics: List<JoyWorkNotification>) {
        mergeInner(dynamics, true)
    }

    private val mClick = { v: View ->
        val notificationEvent = v.tag as JoyWorkNotificationEvent
        notificationEvent.isExpand = !notificationEvent.isExpand
        notifyDataSetChanged()
    }

    private fun addCustomEvent(parent: ViewGroup, es: List<JoyWorkNotificationEvent>) {
        val events = es.filter { it.eContent.isLegalString() }
        if (events.isLegalList()) {
            parent.visible()
            parent.removeAllViews()
            events.forEachIndexed { index, event ->
                val view = mInflater.inflate(
                    R.layout.joywork_notification_center_item_custom,
                    parent,
                    false
                ) as LinearLayout

                val mDivider = view.findViewById<View>(R.id.mDivider)
                if (index == 0) {
                    mDivider.gone()
                } else {
                    mDivider.visible()
                }

                val tv = view.findViewById<TextView>(R.id.textView)
                val str = listOf(event.eTitle, event.eContent).joinLegalString(": ")

                if (event.lineCount <= 0) {
                    event.lineCount = TextHelper.getLineCount(
                        str,
                        tv.textSize,
                        mCustomEventWidth,
                        context.resources.displayMetrics
                    )
                }

                if (event.eTitle.isLegalString()) {
                    TextHelper.setTextViewColor(
                        Color.parseColor("#999999"),
                        tv,
                        str,
                        0,
                        event.eTitle.length
                    )
                }else{
                    tv.text = str
                }

                val handle = view.findViewById<TextView>(R.id.ll_handled)
                if (event.lineCount <= 4) {
                    handle.gone()
                    tv.maxLines = event.lineCount + 1 // 保证可以显示完整
                } else {

                    if (event.isExpand) {
                        tv.maxLines = event.lineCount + 1
                    } else {
                        tv.maxLines = 4
                    }

                    handle.visible()
                    handle.tag = event
                    if (event.isExpand) {
                        handle.setText(R.string.joywork_shrink)
                    } else {
                        handle.setText(R.string.joywork_expand)
                    }
                    handle.setOnClickListener(mClick)
                }

                parent.addView(view)
            }
        } else {
            parent.gone()
        }
    }

    private fun mergeInner(dynamics: List<JoyWorkNotification>, isUp: Boolean) {
        if (!dynamics.isLegalList()) {
            return
        }
        val ids = HashSet<String>()
        if (isUp) {
            val createTime = dynamics.last().createTime
            mDataInner.forEach {
                if (Objects.equals(it.createTime, createTime)) {
                    ids.add(it.dyId)
                } else {
                    return@forEach
                }
            }
            val ans = removeDup(ids, dynamics)
            mDataInner.addAll(0, ans)
        } else {
            val createTime = dynamics.first().createTime
            val size = mDataInner.size
            for (index in size - 1 downTo 0) {
                val d = mDataInner[index]
                if (Objects.equals(d.createTime, createTime)) {
                    ids.add(d.dyId)
                } else {
                    break
                }
            }
            val ans = removeDup(ids, dynamics)
            mDataInner.addAll(ans)
        }
        notifyDataSetChanged()
    }

    private fun removeDup(
        ids: Set<String>,
        dynamics: List<JoyWorkNotification>
    ): ArrayList<JoyWorkNotification> {
        val result = ArrayList<JoyWorkNotification>(dynamics)
        if (!ids.isLegalSet()) {
            return result
        }
        result.removeAll {
            ids.contains(it.dyId)
        }
        return result
    }

    fun lastItem(): JoyWorkNotification? {
        return mDataInner.lastOrNull()
    }

    fun firstItem(): JoyWorkNotification? {
        return mDataInner.firstOrNull()
    }
}