package com.jd.oa.joywork.filter

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.utils.inflater
import com.jd.oa.utils.string

class JoyWorkEmptyAdapter private constructor(
    val mContext: Context,
    private val iconRes: Int,
    private val text: String
) : RecyclerView.Adapter<JoyWorkEmptyAdapter.VH>() {

    companion object {
        fun custom(
            context: Context,
            msg: String,
            @DrawableRes drawableRes: Int
        ): JoyWorkEmptyAdapter {
            return JoyWorkEmptyAdapter(
                context,
                drawableRes,
                msg
            )
        }

        fun empty(context: Context): JoyWorkEmptyAdapter {
            return JoyWorkEmptyAdapter(
                context,
                R.drawable.joywork_empty_img,
                context.string(R.string.joywork_empty)
            )
        }

        fun empty(context: Context, msg: String, @DrawableRes iconRes: Int): JoyWorkEmptyAdapter {
            return JoyWorkEmptyAdapter(context, iconRes, msg)
        }

        fun empty(
            context: Context,
            @StringRes msgId: Int,
            @DrawableRes iconRes: Int
        ): JoyWorkEmptyAdapter {
            return JoyWorkEmptyAdapter(context, iconRes, context.string(msgId))
        }

        fun error(context: Context, errorMsg: String): JoyWorkEmptyAdapter {
            return JoyWorkEmptyAdapter(
                context,
                R.drawable.joywork_error_img,
                JoyWorkEx.filterErrorMsg(errorMsg)
            )
        }
    }

    inner class VH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val iconView: ImageView = itemView.findViewById(R.id.icon)
        val tipsView: TextView = itemView.findViewById(R.id.empty_tips)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(mContext.inflater.inflate(R.layout.me_joywork_empty, parent, false))
    }

    override fun getItemCount() = 1

    override fun onBindViewHolder(holder: VH, position: Int) {
        holder.iconView.setImageResource(iconRes)
        holder.tipsView.text = text
    }
}