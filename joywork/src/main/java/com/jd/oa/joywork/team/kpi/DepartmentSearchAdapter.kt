package com.jd.oa.joywork.team.kpi

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.joywork.union
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.visible

class DepartmentSearchAdapter(
    private val context: Context,
    dataOuter: List<DeptInfo>,
    private val selected: (DeptInfo) -> Boolean,
    private val deletable: (DeptInfo) -> Boolean
) : RecyclerView.Adapter<DepartmentSearchAdapter.VH>() {

    var deleteCallback: ((DeptInfo) -> Unit)? = null
    var itemClick: ((DeptInfo) -> Unit)? = null

    private val data = mutableListOf<DeptInfo>()

    init {
        data.clear()
        data.addAll(dataOuter)
    }

    fun replace(new: List<DeptInfo>) {
        data.clear()
        data.addAll(new)
        notifyDataSetChanged()
    }

    fun append(depts: List<DeptInfo>) {
        data.union(depts) {
            it.deptId
        }
        notifyDataSetChanged()
    }

    private val itemDeleteListener = View.OnClickListener {
        val deptInfo = it.tag as DeptInfo
        deleteCallback?.invoke(deptInfo)
    }
    private val itemClickListener = View.OnClickListener {
        val deptInfo = it.tag as DeptInfo
        itemClick?.invoke(deptInfo)
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mTitle = itemView.findViewById<TextView>(R.id.mTitle)
        val mAdd = itemView.findViewById<TextView>(R.id.mAdd)
        val mSubTitle = itemView.findViewById<TextView>(R.id.mSubTitle)
        val mDel = itemView.findViewById<TextView>(R.id.mDel)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(
            context.inflater.inflate(
                R.layout.joywork_department_search_item,
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        val deptInfo = data[position]
        if (deletable.invoke(deptInfo)) {
            holder.mDel.visible()
            holder.mDel.tag = deptInfo
            holder.mDel.setOnClickListener(itemDeleteListener)
        } else {
            holder.mDel.gone()
        }
        if (selected.invoke(deptInfo)) {
            holder.mAdd.visible()
        } else {
            holder.mAdd.gone()
        }
        holder.mTitle.text = deptInfo.deptName ?: ""
        holder.mSubTitle.text = deptInfo.fullName ?: ""

        holder.itemView.tag = deptInfo
        holder.itemView.setOnClickListener(itemClickListener)
    }

    override fun getItemCount() = data.size
}