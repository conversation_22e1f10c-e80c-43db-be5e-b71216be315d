package com.jd.oa.joywork.view

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.jd.oa.joywork.R

class AutoSizeTextView(context: Context, attributeSet: AttributeSet) :
    AppCompatTextView(context, attributeSet) {
    var hadBackup = false
    var mBackupTextSize = -1.0f

    init {
        val a = context.obtainStyledAttributes(attributeSet, R.styleable.AutoSizeTextView)
        mBackupTextSize = a.getDimension(R.styleable.AutoSizeTextView_backupTextSize, -1.0f)
        if (mBackupTextSize == -1.0f) {
            hadBackup = true
        }
        a.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (!hadBackup) {
            val oldW = measuredWidth
            hadBackup = true
            super.onMeasure(
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                heightMeasureSpec
            )
            if (measuredWidth > oldW) {
                // 不够显示
                setTextSize(TypedValue.COMPLEX_UNIT_PX, mBackupTextSize)
                super.onMeasure(widthMeasureSpec, heightMeasureSpec)
            }
        }
    }
}