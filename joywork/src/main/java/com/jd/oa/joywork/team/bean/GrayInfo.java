package com.jd.oa.joywork.team.bean;

public class GrayInfo {
    public GrayInfoItem grayInfo;

    /**
     * 是否有关联目标权限
     * 没有返回表示有权限，返回 0 表示没权限，返回其余值表示有权限
     */
    public boolean hasGoalPermission() {
        if (grayInfo == null)
            return false;
        // 没有返回表示有权限
        if (grayInfo.performance == null) {
            return true;
        }
        return grayInfo.performance != 0;
    }
}
