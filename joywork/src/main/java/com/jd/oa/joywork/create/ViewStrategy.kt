package com.jd.oa.joywork.create

import android.view.View
import com.jd.oa.joywork.repo.JoyWorkCreateParams
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.isSelf
import com.jd.oa.joywork.urge.UrgeUserBase
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

fun getViewStrategy(sub: <PERSON>ole<PERSON>, project: Boolean): ViewStrategy {
    return when {
        sub -> {
            SubJoyworkStrategy
        }
        project -> {
            ProjectStrategy
        }
        else -> {
            NormalViewStrategy
        }
    }
}

sealed class ViewStrategy {
    abstract fun init(view: View, fragment: JoyWorkCreateFragment)

    open fun handleNetParams(
        params: JoyWorkCreateParams,
        mValue: Value,
        fragment: JoyWorkCreateFragment
    ) {
        params.taskListType = fragment.tmpData?.taskListTypeEnum
        params.tripartiteName = fragment.tmpData?.tripartiteName
    }

    /**
     * 新建时是否需要负责人
     */
    abstract fun needOwner(): Boolean

    abstract fun getOwners(value: Value): List<JoyWorkUser>

    open fun isSubOwners(): Boolean {
        return false
    }
}

object NormalViewStrategy : ViewStrategy() {
    override fun init(view: View, fragment: JoyWorkCreateFragment) {
        // 个人待办时分组
        view.findViewById<View>(R.id.mInputContainer).gone()
        fragment.mItemFactories.add(OwnerCreateFactory())
        view.findViewById<View>(R.id.order_item).visible()
        view.findViewById<View>(R.id.target_item).visible()
    }

    override fun handleNetParams(
        params: JoyWorkCreateParams,
        mValue: Value,
        fragment: JoyWorkCreateFragment
    ) {
        super.handleNetParams(params, mValue, fragment)
        if (mValue.hasSelf()) { // 只有有自己时才需要提醒时间，分组
            params.remindTime = mValue.alertTime
//            params.setTypeAndPlanTime(mValue.type, mValue.planTime)
        }
        params.goalsAndKrs = mValue.mergeTargetsAndKrs()
        params.owners = mValue.normalOwners
        params.inventory = fragment.getSessionId()
        params.executors = mValue.members
        params.projects = mValue.projects
    }

    override fun needOwner(): Boolean {
        return false
    }

    override fun getOwners(value: Value): List<JoyWorkUser> {
        return value.normalOwners
    }
}

object SubJoyworkStrategy : ViewStrategy() {
    override fun init(view: View, fragment: JoyWorkCreateFragment) {
        view.findViewById<View>(R.id.mInputContainer).visible()
        view.findViewById<View>(R.id.order_item).gone()
        view.findViewById<View>(R.id.target_item).gone()
        fragment.mItemFactories.add(SubOwnersCreateFactory())
    }

    override fun handleNetParams(
        params: JoyWorkCreateParams,
        mValue: Value,
        fragment: JoyWorkCreateFragment
    ) {
        super.handleNetParams(params, mValue, fragment)
        var hasSelf = false
        params.parentId = fragment.parentId
        params.url = "work.task.batchCreateChildTasks.v2"
        params.childOwners = mValue.subOwners?.map { user ->
            hasSelf = user.isSelf()
            UrgeUserBase().apply {
                taskId = user.taskId // 催办时可以不传，但取消催办需要。所以统一加上
                emplAccount = user.emplAccount
                ddAppId = user.ddAppId
                app = user.app
            }
        }
        // 只有自己时才传提醒时间
        if (hasSelf) {
            params.remindTime = mValue.alertTime
        }
    }

    override fun needOwner(): Boolean {
        return true
    }

    override fun getOwners(value: Value): List<JoyWorkUser> {
        return value.subOwners
    }

    override fun isSubOwners(): Boolean {
        return true
    }
}

object ProjectStrategy : ViewStrategy() {
    override fun init(view: View, fragment: JoyWorkCreateFragment) {
        view.findViewById<View>(R.id.mInputContainer).gone()
        view.findViewById<View>(R.id.order_item).gone()
        view.findViewById<View>(R.id.target_item).visible()
        fragment.mItemFactories.add(OwnerCreateFactory())
        if (!fragment.shouldHideGroup()) {
            fragment.mItemFactories.add(GroupCreateFactory())
        }
    }

    override fun handleNetParams(
        params: JoyWorkCreateParams,
        mValue: Value,
        fragment: JoyWorkCreateFragment
    ) {
        super.handleNetParams(params, mValue, fragment)
        if (mValue.hasSelf()) { // 只有自己时才需要提醒时间
            params.remindTime = mValue.alertTime
        }
        params.owners = mValue.normalOwners
        params.goalsAndKrs = mValue.mergeTargetsAndKrs()
        if (!fragment.shouldHideGroup()) {
            params.projectId = fragment.inProjectId
            params.groupId = mValue.groupId
        }
        params.executors = mValue.members
    }

    override fun needOwner(): Boolean {
        return false
    }

    override fun getOwners(value: Value): List<JoyWorkUser> {
        return value.normalOwners
    }
}