package com.jd.oa.joywork.filter

import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.TaskUserRole

object FilterPageIdFactory {
    fun getPageId(role: TaskUserRole, status: TaskStatusEnum): String? {
        return when (role) {
            TaskUserRole.OWNER -> OwnerStatePageId().getPageId(status)
            TaskUserRole.CREATOR -> CreatorStatePageId().getPageId(status)
            TaskUserRole.EXECUTOR -> ExecutorStatePageId().getPageId(status)
            else -> null
        }
    }

    /**
     * 获取列表页中复选框点击事件 id。该 id 与待办状态(通过 [status] 判断)，所处界面(通过 [role] 判断),以及当前界面加载的是哪种状态(通过 [pageStatus] 判断)的 joywork 有关
     */
    fun getFinishClickId(
        role: TaskUserRole,
        status: TaskStatusEnum,
        pageStatus: TaskStatusEnum?
    ): String? {
        return when (role) {
            TaskUserRole.OWNER -> OwnerStatePageId().getFinishClickId(status, pageStatus)
            TaskUserRole.CREATOR -> CreatorStatePageId().getFinishClickId(status, pageStatus)
            TaskUserRole.EXECUTOR -> ExecutorStatePageId().getFinishClickId(status, pageStatus)
            else -> null
        }
    }

    /**
     * 获取还原的事件 id
     */
    fun getReverseClickId(
        role: TaskUserRole,
        status: TaskStatusEnum,
        pageStatus: TaskStatusEnum?
    ): String? {
        return when (role) {
            TaskUserRole.OWNER -> OwnerStatePageId().getReverseClickId(status, pageStatus)
            TaskUserRole.CREATOR -> CreatorStatePageId().getReverseClickId(status, pageStatus)
            TaskUserRole.EXECUTOR -> ExecutorStatePageId().getReverseClickId(status, pageStatus)
            else -> null
        }
    }

    fun getDetailClickId(
        isItem: Boolean,
        role: TaskUserRole,
        status: TaskStatusEnum,
        pageStatus: TaskStatusEnum?
    ): String? {
        return when (role) {
            TaskUserRole.OWNER -> OwnerStatePageId().getDetailId(isItem, status, pageStatus)
            TaskUserRole.CREATOR -> CreatorStatePageId().getDetailId(isItem, status, pageStatus)
            TaskUserRole.EXECUTOR -> ExecutorStatePageId().getDetailId(isItem, status, pageStatus)
            else -> null
        }
    }

}

private interface StatusPageId {
    fun getPageId(status: TaskStatusEnum): String?
    fun getFinishClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String?
    fun getReverseClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String?
    fun getDetailId(isItem: Boolean, status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String?
}

/**
 * [TaskUserRole.OWNER] 时。我负责的
 */
private class OwnerStatePageId : StatusPageId {
    override fun getPageId(status: TaskStatusEnum): String? {
        return when (status) {
            TaskStatusEnum.FINISH -> JoyWorkConstant.PAGE_MAIN_FINISH
            TaskStatusEnum.DELETED -> JoyWorkConstant.PAGE_MAIN_DELETED
            else -> null
        }
    }

    override fun getFinishClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String? {
        return when (pageStatus) {
            TaskStatusEnum.FINISH -> JoyWorkConstant.INCOMPLETE_UNFINISH // 取消完成
            TaskStatusEnum.UN_FINISH -> JoyWorkConstant.INCOMPLETE_FINISH // 点击完成
            else -> null
        }
    }

    override fun getReverseClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String? {
        return JoyWorkConstant.OWNER_REVERSE
    }

    override fun getDetailId(
        isItem: Boolean,
        status: TaskStatusEnum,
        pageStatus: TaskStatusEnum?
    ): String? {
        return null
    }
}

/**
 * [TaskUserRole.CREATOR] 时。我指派的
 */
private class CreatorStatePageId : StatusPageId {
    override fun getPageId(status: TaskStatusEnum): String? {
        return when (status) {
            TaskStatusEnum.ALL -> JoyWorkConstant.PAGE_ASSIGN_ALL
            TaskStatusEnum.UN_FINISH -> JoyWorkConstant.PAGE_ASSIGN_UNFINISH
            TaskStatusEnum.FINISH -> JoyWorkConstant.PAGE_ASSIGN_FINISH
            TaskStatusEnum.DELETED -> JoyWorkConstant.PAGE_ASSIGN_DELETED
            else -> null
        }
    }

    override fun getFinishClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String? {
        return when (pageStatus) {
            TaskStatusEnum.ALL -> {
                // 加载的是全部，此时有可能是完成，也有可能是取消
                return when (status) {
                    TaskStatusEnum.UN_FINISH -> JoyWorkConstant.ASSIGN_ALL_FINISH
                    TaskStatusEnum.FINISH -> JoyWorkConstant.ASSIGN_ALL_UNFINISH
                    else -> null
                }
            }
            TaskStatusEnum.UN_FINISH -> JoyWorkConstant.ASSIGN_FINISH
            TaskStatusEnum.FINISH -> JoyWorkConstant.ASSIGN_UNFINISH
            else -> null
        }
    }

    override fun getReverseClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String? {
        return when (pageStatus) {
            TaskStatusEnum.ALL -> JoyWorkConstant.ASSIGN_ALL_REVERSE // 全部 筛选条件下的还原
            TaskStatusEnum.DELETED -> JoyWorkConstant.ASSIGN_REVERSE // 已删除 筛选条件下的还原
            else -> null
        }
    }

    override fun getDetailId(
        isItem: Boolean,
        status: TaskStatusEnum,
        pageStatus: TaskStatusEnum?
    ): String {
        return if (isItem) {
            JoyWorkConstant.ASSIGN_ITEM_DETAIL
        } else {
            JoyWorkConstant.ASSIGN_ACTION_DETAIL
        }
    }
}

/**
 * [TaskUserRole.EXECUTOR] 时。我配合的
 */
private class ExecutorStatePageId : StatusPageId {
    override fun getPageId(status: TaskStatusEnum): String? {
        return when (status) {
            TaskStatusEnum.ALL -> JoyWorkConstant.PAGE_CO_ALL
            TaskStatusEnum.UN_FINISH -> JoyWorkConstant.PAGE_CO_UNFINISH
            TaskStatusEnum.FINISH -> JoyWorkConstant.PAGE_CO_FINISH
            TaskStatusEnum.DELETED -> JoyWorkConstant.PAGE_CO_DELETED
            else -> null
        }
    }

    override fun getFinishClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String? {
        return when (pageStatus) {
            TaskStatusEnum.ALL -> {
                // 加载的是全部，此时有可能是完成，也有可能是取消
                return when (status) {
                    TaskStatusEnum.UN_FINISH -> JoyWorkConstant.CO_ALL_FINISH
                    TaskStatusEnum.FINISH -> JoyWorkConstant.CO_ALL_UNFINISH
                    else -> null
                }
            }
            TaskStatusEnum.FINISH -> JoyWorkConstant.CO_UNFINISH
            TaskStatusEnum.UN_FINISH -> JoyWorkConstant.CO_FINISH
            else -> null
        }
    }

    override fun getReverseClickId(status: TaskStatusEnum, pageStatus: TaskStatusEnum?): String? {
        return when (pageStatus) {
            TaskStatusEnum.DELETED -> JoyWorkConstant.CO_REVERSE
            else -> null
        }
    }

    override fun getDetailId(
        isItem: Boolean,
        status: TaskStatusEnum,
        pageStatus: TaskStatusEnum?
    ): String? {
        return null
    }
}