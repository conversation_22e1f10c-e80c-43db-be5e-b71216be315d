package com.jd.oa.joywork.detail.data.entity.custom;

import android.content.Context;

import com.google.gson.Gson;
import com.jd.oa.joywork.TaskStatusEnum;
import com.jd.oa.joywork.bean.JoyWorkUser;
import com.jd.oa.joywork.detail.data.entity.FilterValue;
import com.jd.oa.joywork.detail.data.entity.SortValue;
import com.jd.oa.joywork.team.bean.ProjectFilterEnum;
import com.jd.oa.joywork.team.bean.ProjectSortEnum;

import org.json.JSONObject;

import javax.annotation.Nullable;

public class ProjectView {
    public JoyWorkUser creator;
    public String viewId;
    public Integer type;
    public String projectId;
    public String content;

    public TaskStatusEnum getTaskStatus() {
        try {
            JSONObject object = new JSONObject(content);
            int taskStatus = object.getInt("taskStatus");
            return TaskStatusEnum.Companion.getInstance(taskStatus);
        } catch (Exception e) {
            return TaskStatusEnum.UN_FINISH;
        }
    }

    public SortValue getSortValue(Context context) {
        return getSortValueWithDefault(context, SortValue.getDefaultSort(context));
    }

    public SortValue getSortValueWithDefault(Context context, SortValue defaultValue) {
        try {
            JSONObject object = new JSONObject(content).getJSONObject("sort");
            int type = object.getInt("type");
            // 理论上这里只会出现 截止时间、执行人、优先级以及清单自定义的字段
            ProjectSortEnum sortEnum = ProjectSortEnum.Companion.getInstance(type);
            SortValue.SortObj obj = sortEnum == ProjectSortEnum.SORT_CUSTOM ? new SortValue.SortObj() : null;
            if (obj != null) {
                try {
                    obj.columnId = object.getJSONObject("value").getString("columnId");
                } catch (Exception e) {
                    return defaultValue;
                }
            }
            return SortValue.getSortInstance(sortEnum.getCode(), obj, context.getResources().getString(sortEnum.stringId()));
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public FilterValue getFilter() {
        try {
            JSONObject object = new JSONObject(content).getJSONObject("filter");
            FilterValue value = new Gson().fromJson(object.toString(), FilterValue.class);
            if (value.value != null) {
                value.value.projectFilterEnum = value.getEnum();
            }
            return value;
        } catch (Exception e) {
            return FilterValue.getNullInstance();
        }
    }

    @Nullable
    public ProjectFilterExt getFilterExt() {
        return getFilter().value;
    }

    /**
     * 是否是自定义筛选
     */
    public boolean isCustomFilter() {
        ProjectFilterEnum filter = getFilter().getEnum();
        return filter == ProjectFilterEnum.SCREEN_CUSTOM
                || filter == ProjectFilterEnum.SCREEN_EXECUTOR
                || filter == ProjectFilterEnum.SCREEN_PRIORITY
                || filter == ProjectFilterEnum.SCREEN_EDN_TIME;
    }

    public void setFilterToNull() {
        try {
            JSONObject object = new JSONObject(content);
            object.remove("filter");
            content = object.toString();
        } catch (Exception e) {
        }
    }

    public void setSortToNull() {
        try {
            JSONObject object = new JSONObject(content);
            object.remove("sort");
            content = object.toString();
        } catch (Exception e) {
        }
    }
}

//"view": {
//        "creator": {
//        "ddAppId": "ee",
//        "deptName": "企业效率组",
//        "realName": "胡锋",
//        "emplAccount": "hufeng24",
//        "titleName": "",
//        "headImg": "https://m.360buyimg.com/jmeside/jfs/t1/184528/40/16268/104313/61010d24E950c0c06/24cf672780d5d2e7.png"
//        },
//        "viewId": "535107589669650432",
//        "type": 1,
//        "projectId": "454325475157082112",
//            "content": "{\"taskStatus\":1,\"filter\":{\"type\":5,\"value\":{\"columnId\":\"511497571508785152\",\"detailId\":\"511497571651391488\",\"label\":\"待审核\"}}}"
//        }