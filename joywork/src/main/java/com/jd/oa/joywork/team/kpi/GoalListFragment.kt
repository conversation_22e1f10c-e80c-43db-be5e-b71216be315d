package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.common.SerializableStub
import com.jd.oa.joywork.common.activity_interaction_create
import com.jd.oa.joywork.common.activity_interaction_delete
import com.jd.oa.joywork.common.activity_interaction_update
import com.jd.oa.joywork.detail.ui.TaskDetailActivity
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.filter.JoyWorkEmptyAdapter
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.self.base.NetType
import com.jd.oa.joywork.self.base.adapter.vh.GoalItemItf
import com.jd.oa.joywork.self.base.adapter.vh.GroupVHItf
import com.jd.oa.joywork.self.base.adapter.vh.ItemVHItf
import com.jd.oa.joywork.self.drag.ProjectListDrag
import com.jd.oa.joywork.team.KREditActivity
import com.jd.oa.joywork.team.bean.VisibleSetting
import com.jd.oa.joywork.view.red
import com.jd.oa.joywork.view.swipe.SwipeMenuParent
import kotlinx.android.synthetic.main.jdme_joywork_kpi_detail.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

fun GoalListFragment.inflate(
    qId: String,
    qDesc: String,
    isSelf: Boolean,
    ddAppId: String?,
    emplAccount: String?
): GoalListFragment {
    arguments = Bundle()
    arguments?.putString("qId", qId)
    arguments?.putString("qDesc", qDesc)
    arguments?.putBoolean("isSelf", isSelf)
    if (ddAppId.isLegalString()) {
        arguments?.putString("ddAppId", ddAppId)
    }
    if (emplAccount.isLegalString()) {
        arguments?.putString("emplAccount", emplAccount)
    }
    return this
}

fun GoalListFragment.ddAppId(): String {
    return arguments?.getString("ddAppId") ?: ""
}

fun GoalListFragment.emplAccount(): String {
    return arguments?.getString("emplAccount") ?: ""
}

fun GoalListFragment.isSelf(): Boolean {
    return arguments?.getBoolean("isSelf") ?: false
}

fun GoalListFragment.getQID(): String {
    return arguments?.getString("qId") ?: ""
}

fun GoalListFragment.getQDesc(): String {
    return arguments?.getString("qDesc") ?: ""
}

class GoalListFragment : BaseFragment(), GroupVHItf, GoalItemItf {

    private var mRootView: View? = null
    private var mRecyclerView: RecyclerView? = null
    private var mSwipe: SwipeRefreshLayout? = null

    private val scope = MainScope()

    /**
     * It will be triggered by GoalDetailActivity
     */
    private val detailReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val r = GoalDetailActivity.parseResultIntent(intent)
            val target = r.value?.firstOrNull() as? KpiTarget
            if (target == null) {
                refresh()
                return
            }
            when (r.action) {
                activity_interaction_delete -> {// delete
                    if (target.goalId.isLegalString()) {
                        // at this case, we know which target should be deleted
                        delGoal(target.goalId!!)
                    } else {
                        // we don't know which item should be deleted, so we can only refresh all data
                        refresh()
                    }
                }
                activity_interaction_update -> {
                    // It's better to just refresh when resume , but I don't want to deal with that now
                    updateGoal(target)
                }
            }
        }
    }

    private val detailReturnReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            // need to refresh taskNums and progressNum
            val id = intent.getStringExtra("id")
            KpiRepo.refreshNums(id) {
                if (it != null) {
                    val adapter = mRecyclerView?.adapter as? GoalListAdapter ?: return@refreshNums
                    adapter.refreshNums(id, it)
                }
            }
        }
    }

    private val krReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val r = SerializableStub.readFromIntent(intent)
            val list = r.value
            if (!list.isLegalList()) {
                refresh()
                return
            }
            val id = r.value?.first() as? String
            if (!id.isLegalString()) {
                refresh()
                return
            }
            val adapter = mRecyclerView?.adapter as? GoalListAdapter ?: return
            when (r.action) {
                activity_interaction_delete -> {
                    if (list.isLegalList()) {
                        adapter.delAllById(id!!)
                    } else {
                        refresh()
                    }
                }
                activity_interaction_create -> {
                    if (list!!.size < 2 || list[1] !is KR) {
                        refresh()
                    } else {
                        adapter.afterLoadMore(id!!, listOf(list[1] as KR))
                    }
                }
                activity_interaction_update -> {
                    if (list!!.size < 2 || !list[1].isLegalString()) {
                        refresh()
                    } else {
                        adapter.refresh(id!!, list[1] as String)
                    }
                }
            }
        }
    }

    private var mSwipeParent: SwipeMenuParent? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (mRootView != null) {
            initView(mRootView!!)
            return mRootView
        }
        val view = inflater.inflate(R.layout.joywork_kpi_detail_fragment, container, false)
        if (mRootView == null) {
            mRootView = view
            initView(view)
        }
        KREditActivity.registerCallback(requireContext(), krReceiver)
        GoalDetailActivity.registerReturnCallback(requireContext(), detailReturnReceiver)
        GoalDetailActivity.registerGoalCallback(requireContext(), detailReceiver)
        return mRootView
    }

    private fun initView(view: View) {
        mSwipeParent = view.findViewById(R.id.swipe_menu_parent)
        mRecyclerView = view.findViewById(R.id.mRecyclerView)
        mRecyclerView?.layoutManager =
            object : LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false) {
                override fun onAdapterChanged(
                    oldAdapter: RecyclerView.Adapter<*>?,
                    newAdapter: RecyclerView.Adapter<*>?
                ) {
                    if (newAdapter is GoalListAdapter) {
                        newAdapter.kpiLoadMore = ::loadMore
                        newAdapter.kpiNewTask = ::newKr
                        newAdapter.delKR = ::delKr
                    }
                }
            }
        ItemTouchHelper(ProjectListDrag(mRecyclerView)).attachToRecyclerView(mRecyclerView)
        mSwipe = view.findViewById(R.id.mSwipe)
        mSwipe?.setOnRefreshListener {
            list(NetType.REFRESH, 0)
        }
        mSwipe.red(requireActivity())
        list(NetType.INIT, 0)
    }

    fun refresh() {
        list(NetType.REFRESH, 0)
    }

    private var shouldInterceptor = false
    fun needInterceptTouchEvent(ev: MotionEvent): Boolean {
        val action = ev.action
        if (action == MotionEvent.ACTION_DOWN) {
            shouldInterceptor = mSwipeParent?.shouldCloseMenu() ?: false
        }
        if (shouldInterceptor && (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL)) {
            shouldInterceptor = false
            return true
        }
        return shouldInterceptor
    }

    /**
     * 内部延迟 1s 再刷新，防止后台缓存来不及更新导致请求数据错误
     */
    fun refreshDelay() {
        mHandler.postDelayed({
            list(NetType.REFRESH, 0)
        }, 1000)
    }

    fun delGoal(goalId: String) {
        (mRecyclerView?.adapter as? GoalListAdapter)?.removeGroup(goalId)
    }

    fun updateGoal(goalId: String, newTitle: String) {
        (mRecyclerView?.adapter as? GoalListAdapter)?.updateGoal(goalId, newTitle)
    }

    private fun updateGoal(goal: KpiTarget) {
        (mRecyclerView?.adapter as? GoalListAdapter)?.updateGoal(goal)
    }

    private fun list(type: NetType, offset: Int) {
        scope.launch {
            val result = if (isSelf()) KpiRepo.listQTargets(
                getQID(),
                true
            ) else KpiRepo.listOtherQTargets(getQID(), true, emplAccount(), ddAppId())
            mSwipe?.isRefreshing = false
            result.isSuccess({ _, msg, _ ->
                onFailure(type, JoyWorkEx.filterErrorMsg(msg))
            }) {
                if (it == null) {
                    onFailure(type, JoyWorkEx.filterErrorMsg(""))
                } else {
                    onSuccess(type, it)
                }
            }
        }
    }

    private fun onSuccess(type: NetType, t: KpiGroups) {
        when (type) {
            NetType.INIT -> {
                if (t.safeKRList.isLegalList()) {
                    mRecyclerView?.adapter =
                        GoalListAdapter(this, requireActivity(), this, this, handleResult(t))
                } else {
                    mRecyclerView?.adapter = JoyWorkEmptyAdapter.empty(
                        requireContext(),
                        if (isSelf()) R.string.joywork_kpi_target_empty else R.string.joywork_other_target_empty,
                        if (isSelf()) R.drawable.joywork_target_empty else R.drawable.joywork_other_target_empty
                    )
                }
            }
            NetType.REFRESH -> {
                if (t.safeKRList.isLegalList()) {
                    if (mRecyclerView?.adapter is GoalListAdapter) {
                        (mRecyclerView?.adapter as GoalListAdapter).replaceAll(handleResult(t))
                    } else {
                        mRecyclerView?.adapter =
                            GoalListAdapter(this, requireActivity(), this, this, handleResult(t))
                    }
                } else if (!isSelf()) {
                    // Other person's goal. If there are no goals, should show empty UI
                    // Maybe due to the person has turned off the visibility of his/her goals
                    mRecyclerView?.adapter = JoyWorkEmptyAdapter.empty(
                        requireContext(),
                        R.string.joywork_other_target_empty,
                        R.drawable.joywork_other_target_empty
                    )
                }
            }
            NetType.LOADMORE -> {

            }
        }
    }

    private fun handleResult(groups: KpiGroups): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val items = ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>()
        groups.safeKRList.forEach {
            val title = JoyWorkTitle(
                R.string.icon_banner_placeholder, "", 0,
                Color.TRANSPARENT
            )
            title.extra = it
            val group = ExpandableGroup(title, ArrayList(), it.goalId, it.safeInit())
            group.ensureRealItems()
            if (it.safeKrs.isLegalList()) {
                group.realItems.addAll(it.safeKrs)
            }
            if (it.safeKrs.size >= KpiRepo.page_size || it.canKr) {
                val add = KpiAddJoyWork()
                add.loadMore = it.safeKrs.size >= KpiRepo.page_size
                add.editKr = it.canKr
                group.realItems.add(add)
            }
            group.notifyItemChange()
            it.safeKrs.clear()
            items.add(group)
        }
        return items
    }

    private fun onFailure(type: NetType, msg: String) {
        when (type) {
            NetType.INIT -> {
                mRecyclerView?.adapter = JoyWorkEmptyAdapter.error(
                    requireContext(),
                    msg
                )
            }
            NetType.REFRESH -> {

            }
            NetType.LOADMORE -> {

            }
        }
    }

    private fun loadMore(targetId: String, offset: Int, runnable: Runnable) {
        // 加载更多
        scope.launch {
            val r = KpiRepo.listTargetKr(targetId, offset)
            runnable.run()
            r.ifSuccessSilence { newTarget ->
                val tasks = newTarget?.safeList
                if (tasks != null) {
                    (mRecyclerView?.adapter as? GoalListAdapter)?.afterLoadMore(targetId, tasks)
                }
            }
        }
    }

    private fun delKr(kr: KR) {
        KpiRepo.delKr(kr.krId) {
            if (it.isLegalString()) {
                // 失败
                Toast.makeText(requireContext(), it!!, Toast.LENGTH_SHORT).show()
            } else {
                (mRecyclerView?.adapter as? GoalListAdapter)?.delAll(kr)
            }
        }
    }

    private fun newKr(target: KpiTarget, title: JoyWorkTitle) {
        KREditActivity.newKR(requireActivity(), target.goalId)
    }

    override fun getTitleType(joyWorkTitle: JoyWorkTitle): Int {
        return 1
    }

    override fun onConfigGroupTitle(indicator: View) {
    }

    override fun expandable(): Boolean {
        return true
    }

    override fun showLock(kpiTitle: KpiTarget): Boolean {
        return isSelf() && kpiTitle.visibleType != null && kpiTitle.visibleType == VisibleSetting.TYPE_ONLY_SUPERVISOR
    }

    override fun getTimeType(): Int {
        return 3
    }

    override fun showAvatar(): Boolean {
        return false
    }

    override fun getMarginH(): Int {
        return 0
    }

    override fun remove(joyWork: JoyWork): Boolean {
        return true
    }

    override fun showFooterDivider(): Boolean {
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
        mHandler.clear()
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(krReceiver)
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(detailReturnReceiver)
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(detailReceiver)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && data != null && data.hasExtra(TaskDetailActivity.KEY_BIZ_OBJ)) {
            val parcel: DetailReturnParcel =
                data.getSerializableExtra(TaskDetailActivity.KEY_BIZ_OBJ) as DetailReturnParcel
            // 没有更新
            if (!parcel.update) {
                return
            }
            when (data.getStringExtra(TaskDetailActivity.KEY_BIZ_FROM)) {
                JoyWorkConstant.BIZ_DETAIL_FROM_FILTER -> {
                    refreshDelay()
                }
                JoyWorkConstant.BIZ_DETAIL_FROM_LIST -> {
                    refreshDelay()
                }
            }
        }
    }
}