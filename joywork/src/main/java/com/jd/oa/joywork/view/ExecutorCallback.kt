package com.jd.oa.joywork.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.detail.DialogManager.layoutInflater
import com.jd.oa.utils.string

/**
 * 如果 names 是动态变化，可通过重写 getName() 返回实时姓名
 */
open class ExecutorCallback(val names: List<String>?, context: Context) :
    JoyWorkAvatarViewCallback() {

    override fun getHintText2(
        urls: List<String>,
        context: Context,
        type: Int
    ): Pair<String?, String?>? {
        return if (urls.size == 1) {
            if (isChief(0)) {
                Pair<String?, String?>(
                    getName(), null
                )
            } else {
                Pair<String?, String?>(
                    getName(),
                    context.resources.getString(R.string.joywork_create_owner2)
                )
            }
        } else {
            if (type == JoyWorkAvatarView.TYPE_LONG) {
                Pair<String?, String?>(
                    null,
                    context.resources.getString(R.string.joywork_create_owner_text2, urls.size)
                )
            }else{
                Pair<String?, String?>(
                    null,
                    context.resources.getString(R.string.joywork_create_owner_text_short, urls.size)
                )
            }
        }
    }

    protected open fun getName(): String {
        return names?.firstOrNull() ?: ""
    }

    override fun needHat(
        url: String,
        position: Int,
        itemView: ImageView,
        parent: ViewGroup
    ): Boolean {
        return position == 0 && isChief(position)
    }

    override fun getImageAltText(
        urls: List<String>,
        context: Context,
        position: Int,
        type: Int
    ): Pair<String?, String?>? {
        val name = names?.firstOrNull() ?: return null
        return Pair(name, context.string(R.string.joywork_owner2))
    }

    protected open fun isChief(position: Int): Boolean {
        return false
    }
}