package com.jd.oa.joywork.dialog.thirdparty

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.indexAtWithNull
import com.jd.oa.joywork2.bean.title.JoyWork2ExtraTitle
import com.jd.oa.network.post
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitContentController
import com.jd.oa.ui.dialog.ShitDialogConfig
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.safeLaunch
import java.util.Collections


class SortGroupDialog(
    private val ctx: Context,
    val projectId: String,
    groups: MutableList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    private val callback: (ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) -> Unit
) : BaseShitDialog(ctx, ShitDialogConfig()) {
    private val listController = object : IShitContentController {
        override fun inflate(parent: FrameLayout): View {
            val inflater = ctx.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val content = inflater.inflate(R.layout.joywork_dialog_group_sort, parent, false)
            parent.addView(content)
            return content
        }
    }

    override var contentController: IShitContentController
        get() = listController
        set(value) {}

    private val groupAdapter = GroupSortAdapter(groups) {
        reportSort(it)
    }

    override fun setupCustomContentView(content: View) {
        content.background = DrawableEx.roundSolidDirRect(
            Color.parseColor("#F4F5F6"),
            context.resources.getDimension(R.dimen.joywork_joywork_group_small),
            DrawableEx.DIR_TOP
        )

        content.findViewById<View>(R.id.cancel).setOnClickListener {
            dismiss()
        }
        content.findViewById<View>(R.id.confirm).setOnClickListener {
            dismiss()
        }
        val recycler = content.findViewById<RecyclerView>(R.id.recycler)
        recycler.layoutManager = LinearLayoutManager(content.context)
        recycler.adapter = groupAdapter
        val helper = ItemTouchHelper(ItemTouchCallback(groupAdapter))
        helper.attachToRecyclerView(recycler)

        setOnDismissListener {
            if (groupAdapter.sorted) {
                callback.invoke(ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>().also {
                    it.addAll(groupAdapter.dataList)
                })
            }
        }
    }

    private class ItemTouchCallback(val groupSortAdapter: GroupSortAdapter) :
        ItemTouchHelper.Callback() {

        override fun getMovementFlags(
            recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder
        ): Int {
            return makeMovementFlags(ItemTouchHelper.UP or ItemTouchHelper.DOWN, 0)
        }


        override fun onMove(
            recyclerView: RecyclerView,
            viewHolder: RecyclerView.ViewHolder,
            target: RecyclerView.ViewHolder
        ): Boolean {
            groupSortAdapter.onMove(viewHolder.adapterPosition, target.adapterPosition)
            return true
        }

        override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

        }

        override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
            if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
                val itemViewHolder = viewHolder as? SortViewHolder
                itemViewHolder?.run {
                    onItemSelected()
                    groupSortAdapter.onStartDrag(this)
                }

            }
            super.onSelectedChanged(viewHolder, actionState)
        }

        override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
            super.clearView(recyclerView, viewHolder)
            val itemViewHolder = viewHolder as? SortViewHolder
            itemViewHolder?.run {
                onItemClear()
                groupSortAdapter.onStopDrag(this)
            }
        }

    }

    private fun reportSort(position: Int) {
        if (ctx is LifecycleOwner) {
            ctx.lifecycleScope.safeLaunch {
                val response = post<String>(
                    action = "work.task.projectGroupSortUpdate.v2"
                ) {
                    val groups = groupAdapter.dataList
                    val group = groups[position]
                    val map = mutableMapOf(
                        Pair("projectId", projectId),
                        Pair("groupId", group.id),
                    )
                    groups.indexAtWithNull(position - 1)?.run {
                        map += Pair("front", id)
                    }
                    groups.indexAtWithNull(position + 1)?.run {
                        map += Pair("after", id)
                    }
                    map
                }
                if (!response.isSuccessful) {
                    groupAdapter.restore()
                } else {
                    if (!groupAdapter.sorted) groupAdapter.sorted = true
                }
            }
        }
    }


    private class GroupSortAdapter(
        var dataList: MutableList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        val sortCallback: (Int) -> Unit
    ) : RecyclerView.Adapter<SortViewHolder>() {

        var backup: MutableList<ExpandableGroup<JoyWorkTitle, JoyWork>>? = null

        var sorted = false


        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SortViewHolder {
            return SortViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.joywork_dialog_group_sort_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: SortViewHolder, position: Int) {
            val item = dataList[position]
            val gg = (item.title as JoyWork2ExtraTitle).extraGroup
            holder.title.text = gg?.title ?: ""
        }

        override fun getItemCount(): Int {
            return dataList.size
        }

        fun onMove(fromPosition: Int, toPosition: Int) {
            Collections.swap(dataList, fromPosition, toPosition)
            notifyItemMoved(fromPosition, toPosition)
            sorted = true
        }

        fun onStartDrag(viewHolder: SortViewHolder) {
            backup = dataList
        }

        fun onStopDrag(viewHolder: SortViewHolder) {
            sortCallback.invoke(viewHolder.getBindingAdapterPosition())
        }

        @SuppressLint("NotifyDataSetChanged")
        fun restore() {
            dataList = backup ?: mutableListOf()
            notifyDataSetChanged()
        }
    }

    class SortViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var title: TextView = view.findViewById(R.id.title)

        fun onItemSelected() {
//            itemView.setAlpha(0.5f)
            itemView.setBackgroundColor(Color.parseColor("#F6F6F6"))
        }

        fun onItemClear() {
//            itemView.setAlpha(1.0f)
            itemView.setBackgroundColor(Color.TRANSPARENT)
        }
    }

}






