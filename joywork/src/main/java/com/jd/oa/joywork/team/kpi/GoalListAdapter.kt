package com.jd.oa.joywork.team.kpi

import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.indexAtWithNull
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.safeGet
import com.jd.oa.joywork.self.base.adapter.SelfBaseFragmentAdapter
import com.jd.oa.joywork.self.base.adapter.vh.GoalItemItf
import com.jd.oa.joywork.self.base.adapter.vh.GroupVHItf
import com.jd.oa.joywork.self.base.adapter.vh.ItemVHItf
import com.jd.oa.joywork.self.drag.DragViewAdapter
import com.jd.oa.joywork.team.bean.Numbers
import com.jd.oa.joywork.team.bean.NumbersList
import java.util.*

class GoalListAdapter(
    fragment: Fragment,
    context: FragmentActivity,
    private val groupVHItf: GroupVHItf,
    val itemVHItf: GoalItemItf,
    groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>
) : SelfBaseFragmentAdapter(
    groups = groups,
    context = context,
    groupVHItf = groupVHItf,
    itemVHItf = itemVHItf,
    fragment = fragment
), DragViewAdapter {
    var kpiLoadMore: ((blockType: String, offset: Int, runnable: Runnable) -> Unit)? = null
    var kpiNewTask: ((kpiTarget: KpiTarget, title: JoyWorkTitle) -> Unit)? = null
    var delKR: ((KR) -> Unit)? = null
    private val type_other = -1
    private val type_add_joywork = 100
    private val type_title = 200

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            itemType -> {
                KpiKRVH(context, parent, this)
            }
            type_add_joywork -> {
                KpiAddJoyWorkVH(context, parent, this)
            }
            type_title -> {
                KpiTitleVH(context, parent, this)
            }
            else -> {
                PlaceHolderVH(context)
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (processor.getData()[position]) {
            is JoyWorkTitle -> {
                type_title
            }
            is KpiAddJoyWork -> {
                type_add_joywork
            }
            is KR -> {
                itemType
            }
            else -> {
                type_other
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is KpiKRVH -> {
                holder.bind(
                    processor.getData()[position] as KR,
                    processor.getData().indexAtWithNull(position + 1),
                    itemVHItf
                )
            }
            is KpiTitleVH -> {
                val kpiTitle = processor.getData()[position]
                holder.bind(
                    kpiTitle as JoyWorkTitle,
                    processor.getData().indexAtWithNull(position + 1)
                )
            }
            is KpiAddJoyWorkVH -> {
                holder.bind(
                    processor.getData()[position] as KpiAddJoyWork,
                    processor.getData().safeGet(position - 1)
                )
            }
        }
    }

    override fun getItemCount() = processor.getData().size

    fun refresh(krId: String, content: String) {
        var changed = false
        processor.getData().forEach {
            if ((it is KR) && Objects.equals(it.krId, krId) && !Objects.equals(
                    it.content,
                    content
                )
            ) {
                it.content = content
                changed = true
            }
        }
        if (changed) {
            notifyDataSetChanged()
        }
    }

    /**
     * the method will be called in two case:
     * 1. load more
     * 2. after creating kr
     */
    fun afterLoadMore(targetId: String, tasks: List<KR>?) {
        if (!tasks.isLegalList()) {
            return
        }
        val title = processor.getData().firstOrNull {
            (it is JoyWorkTitle) && (it.expandableGroup.id == targetId)
        }
        val group = (title as? JoyWorkTitle)?.expandableGroup ?: return
        group.ensureRealItems()
        val last = group.realItems.lastOrNull()
        if (last is KpiAddJoyWork) {
            group.realItems.addAll(group.realItems.size - 1, tasks!!)
            if (last.editKr) {
                // the current user can create kr，so the last item will exist all time
                (group.realItems.last() as? KpiAddJoyWork)?.loadMore =
                    tasks.size >= KpiRepo.page_size
            } else {
                // only load more. Need to determine whether LoadMore is still needed
                val l = tasks.size >= KpiRepo.page_size
                if (l) {
                    (group.realItems.last() as? KpiAddJoyWork)?.loadMore = true
                } else {
                    group.realItems.remove(last)
                }
            }
        } else {
            group.realItems.addAll(tasks!!)
        }
        group.notifyItemChange()
        processor.refreshData()
        notifyDataSetChanged()
    }

    fun removeGroup(groupId: String) {
        val title = processor.getData().firstOrNull {
            it is JoyWorkTitle && it.expandableGroup.id == groupId
        }
        if (title != null && title is JoyWorkTitle) {
            removeGroup(title)
        }
    }

    fun updateGoal(groupId: String, newTitle: String) {
        val title = processor.getData().firstOrNull {
            it is JoyWorkTitle && it.expandableGroup.id == groupId
        }
        if (title != null && title is JoyWorkTitle) {
            (title.extra as? KpiTarget)?.itemName = newTitle
            notifyDataSetChanged()
        }
    }

    fun updateGoal(goal: KpiTarget) {
        val title = processor.getData().firstOrNull {
            it is JoyWorkTitle && it.expandableGroup.id == goal.goalId
        }
        if (title != null && title is JoyWorkTitle) {
            val target = title.extra as? KpiTarget
            target?.itemName = goal.itemName
            target?.condition = goal.condition
            notifyDataSetChanged()
        }
    }

    fun removeGroup(title: JoyWorkTitle) {
        processor.removeGroup(title.expandableGroup)
        notifyDataSetChanged()
    }

    fun delAll(kr: KR) {
        processor.groups.forEach {
            it.realItems.removeAll { work ->
                (work is KR) && Objects.equals(kr.krId, work.krId)
            }
        }
        processor.refreshData()
        notifyDataSetChanged()
    }

    fun delAllById(id: String) {
        processor.groups.forEach {
            it.realItems.removeAll { work ->
                (work is KR) && Objects.equals(id, work.krId)
            }
        }
        processor.refreshData()
        notifyDataSetChanged()
    }

    override fun swap(src: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder) {
        if (src is KpiKRVH) {
            if (src.adapterPosition == target.adapterPosition) {
                return
            }
            Collections.swap(processor.getData(), src.adapterPosition, target.adapterPosition)
            notifyItemMoved(src.adapterPosition, target.adapterPosition)
        }
    }

    // 拖动排序时备份
    private var backup: ArrayList<Any>? = null

    override fun onBeginDrag(holder: RecyclerView.ViewHolder) {
        if (holder is KpiKRVH) {
            holder.onBeginDrag(holder)
            backup = ArrayList(processor.getData())
        }
    }

    override fun onEndDrag(holder: RecyclerView.ViewHolder) {
        if (holder is KpiKRVH) {
            holder.onEndDrag(holder)
            val kr = holder.itemView.tag as KR
            val index = processor.getData().indexOf(kr)
            val front = (processor.getData().safeGet(index - 1) as? KR)?.krId
            val after = (processor.getData().safeGet(index + 1) as? KR)?.krId
            KpiRepo.swapKr(kr.krId, front, after) {
                if (it.isLegalString()) {
                    // 失败
                    Toast.makeText(context, it!!, Toast.LENGTH_SHORT).show()
                    if (backup.isLegalList()) {
                        processor.getData().clear()
                        processor.getData().addAll(backup!!)
                        backup = null
                        notifyDataSetChanged()
                    }
                } else {
                    // 成功
                }
                processor.syncData()
            }
        }
    }

    override fun canDrag(holder: RecyclerView.ViewHolder): Boolean {
        return holder is KpiKRVH
    }

    override fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        return if (current is KpiKRVH) {
            current.canDropOver(recyclerView, current, target)
        } else {
            false
        }
    }

    fun refreshNums(id: String?, numbers: NumbersList) {
        if (!numbers.nums.isLegalList()) {
            return
        }
        val map = mutableMapOf<String, Numbers>()
        numbers.nums.forEach {
            map.put(it.goalId, it)
        }
        processor.getData().forEach {
            if (it is KR && map.contains(it.krId)) {
                it.taskNums = map[it.krId]?.taskNums
                it.progressNums = map[it.krId]?.progressNums
            } else if (it is JoyWorkTitle) {
                val target = it.extra as? KpiTarget
                if (target != null && map.contains(target.goalId)) {
                    target.taskNums = map[target.goalId]?.taskNums
                    target.progressNums = map[target.goalId]?.progressNums
                }
            }
        }
        notifyDataSetChanged()
    }

    fun updateVisibleType(goalId: String, type: Int) {
        processor.getData().forEach {
            if (it is JoyWorkTitle) {
                val target = it.extra as? KpiTarget
                if (target != null && Objects.equals(target.goalId, goalId)) {
                    target.visibleType = type
                }
            }
        }
        notifyDataSetChanged()
    }
}