package com.jd.oa.joywork.shortcut

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.jd.oa.business.joyspace.JoySpacePage
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.clickEvent


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/17 10:28
 */
class DocInitializer(val recyclerView: RecyclerView) {

    val context: Context = recyclerView.context

    init {
        val layoutManager = LinearLayoutManager(recyclerView.context)
        layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        recyclerView.layoutManager = layoutManager
        val adapter = DocAdapter()
        recyclerView.adapter = adapter
        recyclerView.addItemDecoration(object : ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val itemPosition = parent.getChildAdapterPosition(view);
                if (itemPosition == RecyclerView.NO_POSITION) {
                    return
                }
//                val itemCount = state.itemCount;
//                if (itemPosition == 0) {
//                    outRect.left = CommonUtils.dp2px(16f)
//                } else if (itemCount > 0 && itemPosition == itemCount - 1) {
//                    outRect.left = CommonUtils.dp2px(8f)
//                    outRect.right = CommonUtils.dp2px(16f)
//                } else {
//                    outRect.left = CommonUtils.dp2px(8f)
//                }

                outRect.left = CommonUtils.dp2px(4f)
                outRect.right = CommonUtils.dp2px(4f)
            }
        })
    }

    fun addDoc(page: JoySpacePage) {
        val adapter = recyclerView.adapter as DocAdapter
        val size = adapter.itemCount
        adapter.pages.add(page)
//        if (size > 0) {
//            //改变上一个的outRect.right
//            adapter.notifyItemChanged(size - 1)
//        }
        adapter.notifyItemInserted(size)
        recyclerView.scrollToPosition(size)
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_LINK_RECOGNITION
            )
        }
    }

    private class DocAdapter : RecyclerView.Adapter<DocHolder>() {

        val pages = mutableListOf<JoySpacePage>()

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DocHolder {
            return DocHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.joywork_dialog_doc_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: DocHolder, position: Int) {
            val item = pages[position]
            item.mobileIcon?.runCatching {
                val options = RequestOptions.centerInsideTransform()
                    .error(R.drawable.file_unknown)
                    .placeholder(R.drawable.file_unknown)
                Glide.with(holder.icon).load(item.mobileIcon).apply(options).into(holder.icon)
            }
            holder.title.text = item.title
            holder.clear.setOnClickListener {
                removeItem(item)
            }
        }

        fun removeItem(item: JoySpacePage) {
            runCatching {
                val index = pages.indexOf(item)
                pages.removeAt(index)
                notifyItemRemoved(index)
            }
        }

        override fun getItemCount(): Int {
            return pages.size
        }
    }

    class DocHolder(view: View) : RecyclerView.ViewHolder(view) {
        init {
            view.background =
                DrawableEx.roundStrokeRect(
                    Color.parseColor("#EBECEE"),
                    3,
                    CommonUtils.dp2FloatPx(4)
                )
        }

        var icon: ImageView = view.findViewById(R.id.icon)
        var title: TextView = view.findViewById(R.id.title)
        var clear: TextView = view.findViewById(R.id.clear)
    }
}