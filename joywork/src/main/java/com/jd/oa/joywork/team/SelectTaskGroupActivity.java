package com.jd.oa.joywork.team;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.ActionBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.BaseActivity;
import com.jd.oa.joywork.R;
import com.jd.oa.joywork.detail.data.TaskDetailRepository;
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail;
import com.jd.oa.joywork.team.bean.ProjectList;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2Network;

import java.util.ArrayList;

public class SelectTaskGroupActivity extends BaseActivity {

    private Button btnOk;
    private RecyclerView rvGroup;
    private ArrayList<ProjectList.Content.List.Groups> groups;
    private String projectId;
    private String projectName;
    private String taskId;
    private ArrayList<JoyWorkDetail.Project> linkedProjects;
    private GroupListAdapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_select_task_group);
        taskId = getIntent().getStringExtra("taskId");
        projectId = getIntent().getStringExtra("projectId");
        projectName = getIntent().getStringExtra("projectName");
        groups = getIntent().getParcelableArrayListExtra("groups");
        linkedProjects = getIntent().getParcelableArrayListExtra("linkedProjects");
        initView();
    }

    private void initView() {
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            actionBar.hide();
        }
        findViewById(R.id.iv_me_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        btnOk = findViewById(R.id.btn_ok);
        btnOk.setEnabled(true);
        rvGroup = findViewById(R.id.rv_group);
        rvGroup.setLayoutManager(new LinearLayoutManager(this));
        adapter = new GroupListAdapter(this, groups, projectId, linkedProjects, projectName);
        rvGroup.setAdapter(adapter);
        btnOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                linkTaskGroup();
            }
        });
    }


    public void linkTaskGroup() {
        JoyWorkDetail.Project project = adapter.getmCurrentSelectProject();
        if (project == null) {
            return;
        }
        if (taskId == null || taskId.isEmpty()) {
            Intent i = new Intent();
            i.putExtra("project", project);
            setResult(200, i);
            finish();
            return;
        }
        TaskDetailRepository.getInstance().linkTaskGroup(taskId, project, new Utils2Network.Callback() {
            @Override
            public void call(boolean isSuccess) {
                if (isSuccess) {
                    ToastUtils.showToast(R.string.joy_work_link_task_groups_success);
                    if (!SelectTaskGroupActivity.this.isFinishing()) {
                        setResult(200, getIntent());
                        finish();
                    }
                }
            }
        });
    }
}