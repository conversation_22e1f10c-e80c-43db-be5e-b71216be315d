package com.jd.oa.joywork.expandable;


public interface Groupable<TITLE extends Gradeable & Groupable<TITLE, ITEM>, ITEM extends Children<TITLE> & Groupable<TITLE, ITEM>> {
    /**
     * 用于获取所属的组。可通过 {@link ExpandableGroup} 中相关的属性 title 与 item 相互操作
     */
    ExpandableGroup<TITLE, ITEM> getExpandableGroup();

    /**
     * 设置所属的组。可通过 {@link ExpandableGroup} 中相关的属性 title 与 item 相互操作
     */
    void setExpandableGroup(ExpandableGroup<TITLE, ITEM> group);
}
