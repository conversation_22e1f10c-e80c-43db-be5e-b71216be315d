package com.jd.oa.joywork.team.kpi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.ViewModelProviders
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.KpiQ
import com.jd.oa.joywork.common.activity_interaction_create
import com.jd.oa.joywork.common.activity_interaction_update
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromMembers
import com.jd.oa.joywork.detail.openChat
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.utils.getParamsStr
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.jdme_joywork_kpi_detail.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.*
import kotlin.collections.ArrayList


/**
 * 目标列表
 */
@Route(DeepLink.JOY_GOAL_LIST)
class GoalListActivity : BaseActivity() {

    companion object {
        const val req_target_detail = 200

        const val req_target_edit = 100

        fun inflateIntent(
            context: Activity,
            title: String = "",
            isTab: Boolean
        ): Intent {
            val bundle = Intent(context, GoalListActivity::class.java)
            bundle.putExtra("title", title)
            bundle.putExtra("isTab", isTab)
            bundle.putExtra("type", "self")
            bundle.putExtra("ddAppId", PreferenceManager.UserInfo.getTimlineAppID())
            bundle.putExtra("emplAccount", PreferenceManager.UserInfo.getUserName())
            return bundle
        }

        fun startOther(context: Activity, user: JoyWorkUser) {
            val intent = Intent(context, GoalListActivity::class.java)
            intent.fromJoyWorkUser(user)
            context.startActivity(intent)
        }

        private fun Intent.fromJoyWorkUser(user: JoyWorkUser) {
            val type = if (user.isSelf()) "self" else "other"
            putExtra("type", type)
            putExtra("ddAppId", user.ddAppId)
            putExtra("emplAccount", user.emplAccount)
            putExtra("headImg", user.headImg)
            putExtra("isTab", false)
            putExtra("title", user.realName)
        }

        private fun GoalListActivity.fromDeeplink() {
            // jdme://jm/biz/joywork/goalList?mparam={"erp":"qianyang3","ddappid":"ee","realname":"千阳",
            //                "headimg":"https://storage.jd.com/jd.jme.avatar/5374E384BF494DCE66AC2FAB44257CA0.png","assessPeriod":"xx"}
            val params = intent.getParamsStr()
            try {
                val user = JoyWorkUser()
                val obj = JSONObject(params)
                if (obj.has("title")) {
                    intent.putExtra("title", obj.getString("title"))
                }
                val erp = obj.optString("erp")
                if (!erp.isLegalString()) {
                    return
                }
                user.emplAccount = obj.getString("erp")
                user.ddAppId = obj.getString("ddappid")
                if (obj.has("headimg")) {
                    user.headImg = obj.getString("headimg")
                } else {
                    user.headImg = ""
                }
                if (obj.has("realname")) {
                    user.realName = obj.getString("realname")
                } else {
                    user.realName = ""
                }
                intent.fromJoyWorkUser(user)
                if (obj.has("assessPeriod")) {
                    intent.putExtra("assessPeriod", obj.getString("assessPeriod"))
                }
            } catch (e: Exception) {

            }
        }

        private fun GoalListActivity.getAssessPeriod(): String? {
            if (intent.hasExtra("assessPeriod")) {
                return intent.getStringExtra("assessPeriod")
            }
            return null
        }

        private fun GoalListActivity.ddAppId(): String {
            return intent.getStringExtra("ddAppId") ?: ""
        }

        private fun GoalListActivity.emplAccount(): String {
            return intent.getStringExtra("emplAccount") ?: ""
        }

        private fun GoalListActivity.headImg(): String {
            return intent.getStringExtra("headImg") ?: ""
        }

        private fun GoalListActivity.isSelf(): Boolean {
            return intent.getStringExtra("type") != "other"
        }

        private fun GoalListActivity.restore(bundle: Bundle?) {
            if (bundle == null)
                return
            if (bundle.containsKey("curIndex")) {
                mInitIndex = bundle.getInt("curIndex")
            }
        }

        private fun GoalListActivity.isTab(): Boolean {
            return intent?.getBooleanExtra("isTab", false) ?: false
        }

        private fun GoalListActivity.getTitleStr(): String {
            return intent?.getStringExtra("title") ?: ""
        }
    }

    private var mStrategy: JoyWorkMainStrategy = JoyWorkMainNormalStrategy

    private val items = ArrayList<JoyWorkTabItem<Pair<String, KpiQ>, GoalListFragment>>()
    private val scope = MainScope()
    private var mInitIndex = 0
    private var mGoalListViewModel: GoalListViewModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideAction()
        setContentView(R.layout.jdme_joywork_kpi_detail)
        fromDeeplink()
        mGoalListViewModel = ViewModelProviders.of(this).get(GoalListViewModel::class.java)
        restore(bundle = savedInstanceState)
        mStrategy = if (isTab()) JoyWorkMainTabStrategy else JoyWorkMainNormalStrategy
        getData()
        handleAddTab()
        mTitleView.text = getTitleStr()
        back.setOnClickListener {
            finish()
        }
        if (isSelf()) {
            mAvatar.gone()
            mIcon.visible()
            mFocus.gone()
            mWeeklySummary.setOnClickListener {
                if (items.isLegalList()) {
                    JDMAUtils.clickEvent("", JoyWorkConstant.CREATE_WEEKLY_SUMMARY, null)
                    WeeklySummaryActivity.start(
                        this,
                        items[viewpager.currentItem].fragment.getQID(),
                        items[viewpager.currentItem].fragment.getQDesc()
                    )
                }
            }
            mOrganizationList.setOnClickListener {
                OrgListActivity.start(this)
            }
            mSearch.setOnClickListener {
                ExecutorUtils.searchOther(this, { result ->
                    if (result.isLegalList() && result!!.size >= 1) {
                        val user = JoyWorkUser()
                        user.fromDD(result[0]!!)
                        if (!user.isSelf()) {
                            startOther(this, user)
                        }
                    }
                }) {}
            }
        } else {
            mIcon.gone()
            mAvatar.visible()
            mAvatar.setOnClickListener(::toOtherInfo)
            mTitleView.setOnClickListener(::toOtherInfo)
            JoyWorkViewItem.avatar(mAvatar, headImg())
            mSearch.gone()
            mWeeklySummary.gone()
            mOrganizationList.gone()
            mFocus.visible()
            mFocus.setOnClickListener {
                changeFocusStatus()
            }
            if (!headImg().isLegalString() || !getTitleStr().isLegalString()) {
                scope.launch {
                    val m = Members()
                    m.ddAppId = ddAppId()
                    m.emplAccount = emplAccount()
                    val r = getBatchInfo(listOf(m))
                    r.ifSuccessSilence {
                        val first = it?.users?.firstOrNull() ?: return@ifSuccessSilence
                        val user = JoyWorkUser()
                        user.fromMembers(first)
                        intent.fromJoyWorkUser(user)
                        // update UI
                        JoyWorkViewItem.avatar(mAvatar, headImg())
                        mTitleView.text = getTitleStr()
                    }
                }
            }
        }
        mGoalListViewModel?.followLiveData?.observe(this) {
            if (it) {
                mFocus.setText(R.string.icon_padding_followhear)
                mFocus.argb("#FFCF33")
            } else {
                mFocus.setText(R.string.icon_padding_followheart)
                mFocus.argb("#666666")
            }
        }
        mShare.setOnClickListener {
            if (items.isLegalList()) {
                JDMAUtils.clickEvent("", JoyWorkConstant.GOAL_SHARE_CLICK, null)
                selectChat { users, groupIds, isGroup ->
                    val user = JoyWorkUser()
                    user.emplAccount = emplAccount()
                    user.ddAppId = ddAppId()
                    KpiRepo.sharePeriod(
                        items[viewpager.currentItem].fragment.getQID(),
                        items[viewpager.currentItem].fragment.getQDesc(),
                        user,
                        groupIds,
                        users
                    ) { r, msg ->
                        shareResult(r, msg, true) {
                            kotlin.runCatching {
                                val id = if (isGroup) {
                                    groupIds.first()
                                } else {
                                    users.first().emplAccount
                                }
                                openChat(id, isGroup)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun changeFocusStatus() {
        val followed = mGoalListViewModel?.followLiveData?.value ?: return
        if (followed) {
            showCancelFollowDialog(this) {
                changeFocusStatusNet(followed)
            }
        } else {
            changeFocusStatusNet(followed)
        }
    }

    private fun changeFocusStatusNet(followed: Boolean) {
        KpiRepo.followOrCancelSomeone(emplAccount(), ddAppId(), followed) {
            if (it.isLegalString()) {
                // failure
                Toast.makeText(this@GoalListActivity, it!!, Toast.LENGTH_SHORT).show()
            } else {
                val str = if (followed) {
                    KpiMsgCenter.sendCancelFollow(
                        this,
                        emplAccount = emplAccount(),
                        ddAppId = ddAppId()
                    )
                    getString(R.string.joywork_followed_cancel)
                } else {
                    KpiMsgCenter.sendFollow(this, emplAccount = emplAccount(), ddAppId = ddAppId())
                    getString(R.string.joywork_followed)
                }
                Toast.makeText(
                    this@GoalListActivity,
                    str,
                    Toast.LENGTH_SHORT
                ).show()
                mGoalListViewModel?.changeFollowStatus()
            }
        }
    }

    private fun toOtherInfo(view: View) {
        val emplAccount = emplAccount()
        if (isSelf() || !emplAccount.isLegalString()) {
            return
        }
        toSelfInfo(ddAppId(), emplAccount)
    }

    private fun getData() {
        scope.launch {
            // 这里只拉取考勤周期，每个周期中的待办由各 frg 自己负责拉取
            val result =
                if (isSelf()) KpiRepo.listQ() else KpiRepo.listOtherQ(emplAccount(), ddAppId())
            result.isSuccess({ _, msg, _ ->
                showError(msg)
            }) {
                if (isSelf()) {
                    mFocus.gone()
                } else {
                    mFocus.visible()
                    mGoalListViewModel?.isFollow(it?.follow.isTrue())
                }

                if (it == null || !it.periodList.isLegalList()) {
                    showError(if (isSelf()) string(R.string.joywork_kpi_target_empty) else string(R.string.joywork_other_target_empty))
                } else {
                    mTabsRecyclerView.visible()
                    if (isSelf()) mAddView.visible() else mAddView.gone()
                    items.clear()
                    val ap = getAssessPeriod()
                    it.periodList.forEachIndexed { index, q ->
                        if (Objects.equals(q.assessPeriod, ap)) {
                            mInitIndex = index
                        }
                        val i = JoyWorkTabItem<Pair<String, KpiQ>, GoalListFragment>(
                            Pair(q.assessPeriodDesc, q),
                            clickId = null,
                            GoalListFragment().inflate(
                                q.assessPeriod,
                                q.assessPeriodDesc,
                                isSelf(),
                                ddAppId(),
                                emplAccount()
                            )
                        )
                        items.add(i)
                    }
                    handleTabs()
                    handleViewPager()
                }
            }
        }
    }

    private fun showError(msg: String) {
        mTabsRecyclerView.gone()
        mAddView.gone()
        viewpager.gone()
        // 拉取考勤周期失败，应该有一个错误界面
        val errorView = inflater.inflate(R.layout.me_joywork_empty, mRoot, true)
        errorView.findViewById<TextView>(R.id.empty_tips).text = msg
        errorView.findViewById<ImageView>(R.id.icon)
            .setImageResource(if (isSelf()) R.drawable.joywork_target_empty else R.drawable.joywork_other_target_empty)
    }

    private fun handleViewPager() {
        viewpager.visible()
        viewpager.adapter = KpiAppFragmentAdapter(supportFragmentManager, items)
        viewpager.currentItem = mInitIndex
    }

    private fun handleTabs() {
        mTabsRecyclerView.visible()
        mTabsRecyclerView.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        val map: List<Pair<String, KpiQ>> = items.map {
            it.content
        }
        mTabsRecyclerView.adapter = TabsAdapter(this, mInitIndex, map) { it, _ ->
            viewpager.currentItem = it
        }
        if (mInitIndex != 0) {
            mTabsRecyclerView.smoothScrollToPosition(mInitIndex)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt("curIndex", viewpager.currentItem)
    }

    private fun handleAddTab() {
        if (!isSelf()) {
            mAddView.gone()
            return
        }
        mAddView.apply {
            setOnClickListener {
                JDMAUtils.clickEvent("", JoyWorkConstant.CREATE_TARGET_CLICK, null)
                GoalCreateActivity.newTarget(
                    this@GoalListActivity,
                    100,
                    items[viewpager.currentItem].fragment.getQID()
                )
            }

            val lp = (layoutParams as? ViewGroup.MarginLayoutParams)
                ?: ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            lp.apply {
                rightMargin =
                    resources.getDimensionPixelSize(mStrategy.addViewRightMargin)
                bottomMargin =
                    resources.getDimensionPixelSize(mStrategy.addViewBottomMargin)
            }
            layoutParams = lp
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        scope.cancel()
    }

    private fun updateGoalTitle(goalId: String?, newTitle: String?) {
        if (!goalId.isLegalString() || !newTitle.isLegalString()) {
            // 不知道具体目标或者目标新标题未知，只能刷新
            items[viewpager.currentItem].fragment.refresh()
        } else {
            // 否则直接更新，不需要调接口
            items[viewpager.currentItem].fragment.updateGoal(goalId!!, newTitle!!)
        }
    }

    override fun finish() {
        // 关闭目标列表时，通知清单列表界面刷新
        setResult(RESULT_OK)
        LocalBroadcastManager.getInstance(this)
            .sendBroadcast(Intent("ACTION_EXP_HOMEPAGE_UPDATE_OKR_CARD"))
        super.finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == req_target_edit && resultCode == RESULT_OK && data != null) { // 新建目标、更新目标
            val r = GoalCreateActivity.parseResultIntent(data)
            when (r.action) {
                activity_interaction_create -> {
                    // 新建后，直接刷新。这里不能本地更新，因为不知道新目标应该在的位置
                    items.forEach {
                        if (r.value?.firstOrNull() == it.fragment.getQID()) {
                            it.fragment.refreshDelay()
                        }
                    }
                }
                activity_interaction_update -> {
                    val goalId = r.value?.firstOrNull() as? String
                    val newTitle = r.value?.safeGet(1) as? String
                    updateGoalTitle(goalId, newTitle)
                }
            }
        }
    }

//    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
//        if (ev == null) {
//            return super.dispatchTouchEvent(ev)
//        }
//        items[viewpager.currentItem].fragment.needInterceptTouchEvent(ev)
//        return super.dispatchTouchEvent(ev)
//    }
}

/**
 * @param items key 为要显示的内容；value 为通过点击事件回传的 Item 唯一标识
 */
class TabsAdapter(
    private val context: Activity,
    initIndex: Int = 0,
    private val items: List<Pair<String, Any>>,
    private val itemClick: (Int, Any) -> Unit
) : RecyclerView.Adapter<TabsAdapter.VH>() {

    private var selectedIndex = initIndex

    private val click = View.OnClickListener {
        selectedIndex = it.getTag(R.id.tag_key_data) as Int
        itemClick(selectedIndex, it.tag)
        notifyDataSetChanged()
    }

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val indicator = itemView.findViewById<View>(R.id.indicator)
        val title = itemView.findViewById<TextView>(R.id.tab_title)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VH {
        return VH(
            context.inflater.inflate(
                R.layout.joywork_tablayout_item2,
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: VH, position: Int) {
        if (position == selectedIndex) {
            holder.indicator.visible()
            holder.title.argb("#333333")
        } else {
            holder.title.argb("#666666")
            holder.indicator.gone()
        }
        holder.title.text = items[position].first
        holder.itemView.tag = items[position].second
        holder.itemView.setTag(R.id.tag_key_data, position)
        holder.itemView.setOnClickListener(click)
    }

    override fun getItemCount() = items.size
}

public class KpiAppFragmentAdapter<T : Fragment, A>(
    fm: FragmentManager,
    private val tabItems: ArrayList<JoyWorkTabItem<Pair<String, A>, T>>
) : FragmentPagerAdapter(fm) {
    override fun getItem(position: Int): Fragment {
        return tabItems[position].fragment
    }

    override fun getCount(): Int {
        return tabItems.size
    }
}