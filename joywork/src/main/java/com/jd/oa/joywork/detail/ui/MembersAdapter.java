package com.jd.oa.joywork.detail.ui;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.AsyncDifferConfig;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.databinding.TaskMemberItemBinding;
import com.jd.oa.model.service.im.dd.entity.Members;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter;
import com.jd.oa.joywork.detail.viewmodel.TaskDetailViewModel;

class MembersAdapter extends ListAdapter<Members, MembersAdapter.MembersViewHolder> {

    @Nullable
    private final TaskDetailPresenter taskDetailPresenter;
    private final TaskDetailViewModel taskDetailViewModel;
    private final int maxCount;

    MembersAdapter(TaskDetailViewModel taskDetailViewModel, @Nullable TaskDetailPresenter taskDetailPresenter, int maxCount) {
        super(new AsyncDifferConfig.Builder<>(new DiffUtil.ItemCallback<Members>() {
            @Override
            public boolean areItemsTheSame(@NonNull Members old,
                                           @NonNull Members comment) {
                return old.isSamePerson(comment);
            }

            @Override
            public boolean areContentsTheSame(@NonNull Members old,
                                              @NonNull Members comment) {
                return old.isSamePerson(comment);
            }
        }).build());
        this.taskDetailPresenter = taskDetailPresenter;
        this.taskDetailViewModel = taskDetailViewModel;
        this.maxCount = maxCount;
    }

    @Override
    @NonNull
    public MembersViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TaskMemberItemBinding binding = DataBindingUtil
                .inflate(LayoutInflater.from(parent.getContext()), R.layout.task_member_item,
                        parent, false);
        binding.setPresenter(taskDetailPresenter);
        return new MembersViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull MembersViewHolder holder, int position) {
        holder.binding.setMember(getItem(position));
        holder.binding.executePendingBindings();
        holder.binding.memberAvatar.setTag(R.id.tag_key_data, getItem(position));
        holder.binding.memberAvatar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Members members = (Members) v.getTag(R.id.tag_key_data);
                taskDetailPresenter.showUserInfo(v, members.getEmplAccount());
            }
        });
    }

    static class MembersViewHolder extends RecyclerView.ViewHolder {

        final TaskMemberItemBinding binding;

        MembersViewHolder(TaskMemberItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @Override
    public int getItemCount() {
        int count = super.getItemCount();
        return Math.min(count, maxCount);
    }
}
