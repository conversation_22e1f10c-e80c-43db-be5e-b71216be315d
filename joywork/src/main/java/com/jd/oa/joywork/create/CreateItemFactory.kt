package com.jd.oa.joywork.create

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Rect
import android.net.Uri
import android.text.Editable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.JoyWorkActionEnum
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkDialog.showDuplicateDialog
import com.jd.oa.joywork.JoyWorkImageActivity
import com.jd.oa.joywork.JoyWorkLevel
import com.jd.oa.joywork.R
import com.jd.oa.joywork.RiskEnum
import com.jd.oa.joywork.RiskEnum.Companion.valueByCode
import com.jd.oa.joywork.SUB_JOYWORK_ONETIME
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.adapter.TargetLinkAdapter
import com.jd.oa.joywork.adapter.TaskLinkAdapter
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.collaborator.CollaboratorListActivity
import com.jd.oa.joywork.collaborator.OneTimeDataRepo
import com.jd.oa.joywork.detail.DialogManager.showLevelDialog
import com.jd.oa.joywork.detail.data.entity.Documents
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.Resources
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.detail.fromMembers
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.detail.launchTime
import com.jd.oa.joywork.detail.ui.LinkCallback
import com.jd.oa.joywork.detail.ui.des.JoyWorkDesActivity
import com.jd.oa.joywork.detail.viewmodel.TaskDetailBindingAdapter
import com.jd.oa.joywork.dialog.JoyWorkAlertDialog
import com.jd.oa.joywork.executor.ExecutorListActivity
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.isChief
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalLong
import com.jd.oa.joywork.isLegalSet
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.isNotTrue
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.safeAdd
import com.jd.oa.joywork.shortcut.JoyWorkShortcutDraft
import com.jd.oa.joywork.shortcut.ShortcutManager
import com.jd.oa.joywork.team.TeamDetailActivity
import com.jd.oa.joywork.team.bean.GrayInfo
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.dialog.CreateDialog.Companion.getTimeStr2
import com.jd.oa.joywork.team.dialog.DialogSupporter
import com.jd.oa.joywork.team.kpi.GoalDetailActivity
import com.jd.oa.joywork.team.kpi.goDetail
import com.jd.oa.joywork.team.kpi.krDeletable
import com.jd.oa.joywork.team.kpi.krEditable
import com.jd.oa.joywork.todayTime
import com.jd.oa.joywork.view.ExecutorCallback
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork.view.JoyWorkAvatarViewCallback
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.DensityUtil
import com.jd.oa.utils.DrawableEx.roundSolidRect
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.TabletUtil
import com.jd.oa.utils.TextWatcherAdapter
import com.jd.oa.utils.argb
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.invisible
import com.jd.oa.utils.isVisible
import com.jd.oa.utils.padHor
import com.jd.oa.utils.padVertical
import com.jd.oa.utils.setCursorEnd
import com.jd.oa.utils.string
import com.jd.oa.utils.visible

abstract class CreateItemFactoryList(size: Int) : ArrayList<CreateItemFactory>(size) {
    fun has(clazz: Class<*>): Boolean {
        return firstOrNull {
            it::class.java == clazz
        } != null
    }

    fun <T : CreateItemFactory> removeByClass(clazz: Class<T>) {
        removeAll {
            val ans = it::class.java == clazz
            if (ans) {
                it.removeFromUI()
            }
            ans
        }
    }

    fun <T : CreateItemFactory> getByClass(clazz: Class<T>): T? {
        return firstOrNull {
            it::class.java == clazz
        } as? T
    }

    /**
     * 会自动排序
     */
    override fun add(element: CreateItemFactory): Boolean {
        return if (isEmpty()) {
            super.add(element)
        } else {
            val eleIndex = getOrderList().indexOf(element.getEnum())
            // 按定义好的枚举排序。因为数量少，直接插入排序
            val index = indexOfFirst {
                getOrderList().indexOf(it.getEnum()) > eleIndex
            }
            if (index >= size || index < 0) {
                super.add(element)
            } else {
                super.add(index, element)
                true
            }
        }
    }

    /**
     * 如果自己能保证顺序，可直接添加
     */
    fun addFirst(element: CreateItemFactory) {
        add(0, element)
    }

    /**
     * 追加。向末尾添加元素
     */
    fun append(element: CreateItemFactory) {
        super.add(element)
    }

    /**
     * 返回排序列表
     */
    protected abstract fun getOrderList(): List<JoyWorkActionEnum>
}

/**
 * 根据 JoyWorkActionEnum 进行排序
 */
internal class CreateArrayList : CreateItemFactoryList(8) {
    override fun getOrderList(): List<JoyWorkActionEnum> {
        return listOf(
            JoyWorkActionEnum.TITLE,
            JoyWorkActionEnum.DESC,
            JoyWorkActionEnum.SHIMO,
            JoyWorkActionEnum.ATTACHMENT,
//            JoyWorkActionEnum.TARGET,
            JoyWorkActionEnum.GROUP,
            JoyWorkActionEnum.OWNER,
            JoyWorkActionEnum.OWNERS,
            JoyWorkActionEnum.RELATION,
            JoyWorkActionEnum.DEAD_LINE,
            JoyWorkActionEnum.REMIND,
            JoyWorkActionEnum.DUP,
            JoyWorkActionEnum.PRIORITY
        )
    }
}

internal class DetailArrayList : CreateItemFactoryList(16) {
    override fun getOrderList(): List<JoyWorkActionEnum> {
        return listOf(
            // 这里其实没啥用，主要排序还是在布局文件中
        )
    }
}


interface CreateDetailFragmentItf {
    val mItemFactories: CreateItemFactoryList
    val fragment: Fragment

    fun moreOwners(): Boolean

    fun getSessionId(): String?
}

abstract class CreateItemFactory {

    protected var mValue: Value? = null
    protected var mFragmentItf: CreateDetailFragmentItf? = null
    protected var mFragment: Fragment? = null
    protected var mCreateViewModel: CreateViewModel? = null

    fun buildView(
        parent: ViewGroup,
        value: Value,
        fragment: CreateDetailFragmentItf,
        viewModel: CreateViewModel
    ): View {
        mValue = value
        mFragmentItf = fragment
        mFragment = fragment.fragment
        mCreateViewModel = viewModel
        return buildView(parent)
    }

    private fun buildView(parent: ViewGroup): View {
        val layoutInflater =
            parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val view = onBuildView(customParent(parent), layoutInflater)
        val index = mFragmentItf?.mItemFactories?.indexOfFirst {
            it.getEnum() == getEnum()
        } ?: -1
        if (index < 0) {
            parent.addView(view)
        } else {
            try {
                parent.addView(view, index)
            } catch (e: Exception) {
                parent.addView(view)
            }
        }
        onInit()
        return view
    }

    open fun onInit() {}

    open fun updateValue() {}

    open fun customParent(parent: ViewGroup): ViewGroup {
        return parent
    }

    protected abstract fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View

    abstract fun getEnum(): JoyWorkActionEnum

    /**
     * 界面退出时回调
     */
    open fun release() {

    }

    /**
     * 当被移除时
     */
    open fun removeFromUI() {

    }
}

/**
 * 新建页标题
 */
class TitleCreateFactory : CreateItemFactory() {
    private var mTitleView: EditText? = null
    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view =
            layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_title, parent, false)
        mTitleView = view.findViewById(R.id.title)
        mTitleView!!.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable) {
                super.afterTextChanged(s)
                mValue?.updateTitle(mTitleView?.string() ?: "")
            }
        })
        mTitleView?.setText(mValue?.title ?: "")
        mTitleView.setCursorEnd()
        view.postDelayed({
            val manager =
                mTitleView?.context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            manager?.showSoftInput(mTitleView, 0)
        }, 500)
        return view
    }

    override fun updateValue() {
        mTitleView?.setText(mValue?.title ?: "")
        mTitleView?.setCursorEnd()
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.TITLE
    }
}

/**
 * 普通待办多负责人
 */
open class OwnerCreateFactory : CreateItemFactory() {
    private val addClick = View.OnClickListener {
        addOwner()
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CREATE_TASK_ASSIGNEES_ADD_ASSIGNEES
            )
        }
    }

    private fun addOwner() {
        val a = mFragmentItf?.fragment?.activity ?: return
        val selected = ArrayList<MemberEntityJd>()
        mValue?.normalOwners?.forEach {
            val jd = MemberEntityJd()
            jd.fromUser(it)
            selected.add(jd)
        }
        ExecutorUtils.selectExecutors(a, selected, mFragmentItf?.getSessionId(), true, { bean ->
            val us = ArrayList<JoyWorkUser>()
            bean?.forEach { entityJd ->
                if (entityJd != null) {
                    val u = JoyWorkUser()
                    u.fromDD(entityJd)
                    if (u.chief.isChief()) {
                        us.safeAdd(0, u)
                    } else {
                        us.add(u)
                    }
                }
            }
            if (us.isLegalList()) {
                mValue?.addNormalOwners(us)
            }
        }, {

        })
    }

    private val itemClick = View.OnClickListener {
        JDMAUtils.onEventClick(
            JoyWorkConstant.CREATE_OWNER,
            JoyWorkConstant.CREATE_OWNER
        )
        if (mValue?.normalOwners.isLegalList()) {
            // 跳转执行人列表
            val i = Intent(it.context, ExecutorListActivity::class.java)
            val ms = mValue!!.normalOwners.map {
                val m = Members()
                m.fromJoyWorkUser(it)
                m
            }
            ExecutorListActivity.inflateIntent(i, ms, mFragmentItf?.getSessionId()) {
                mValue!!.setNormalOwners(it)
            }
            mFragment?.startActivity(i)
        } else {
            addOwner()
        }
    }

    private var mOwnerContainer: LinearLayout? = null
    private var mOwnerAvatar: JoyWorkAvatarView? = null
    private var mOwnerAvatar2: JoyWorkAvatarView? = null
    private var mOwnerName: TextView? = null
    private var mOwnerArrow: View? = null
    private var mAdd: View? = null
    private var mTransfer: View? = null
    private var judged = false
    private val mReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            judged = false
            updateUI()
        }
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view =
            layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_owner, parent, false)
        mOwnerAvatar = view.findViewById(R.id.owner_avatar)
        mOwnerAvatar?.mCallback = getAvatarCallback(parent.context)
        mOwnerAvatar2 = view.findViewById(R.id.owner_avatar2)
        mOwnerAvatar2?.mCallback = getAvatarCallback(parent.context)
        mOwnerAvatar2?.mShowable = false
        mOwnerName = view.findViewById(R.id.owner_name)
        mOwnerContainer = view.findViewById(R.id.owner_container)
        mOwnerArrow = view.findViewById(R.id.owner_arrow)
        mAdd = view.findViewById(R.id.relation_add)
        mTransfer = view.findViewById(R.id.relation_transfer)
        mOwnerContainer?.setOnClickListener(itemClick())
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.normalOwnersChangeLivedata?.observe(a) {
            judged = false
            updateUI()
        }
        // 折叠屏展开收起时广播
        val filter = IntentFilter(TabletUtil.ACTION_SPLIT_MODE_CHANGE)
        LocalBroadcastManager.getInstance(a).registerReceiver(mReceiver, filter)
    }

    private fun updateUI() {
        val owner = mValue?.normalOwners
        if (!owner.isLegalList()) {
            mOwnerArrow.gone()
            mOwnerAvatar.gone()
            mOwnerName.visible()
            mOwnerContainer.padHor(CommonUtils.dp2px(0f))
            mOwnerContainer?.setBackgroundColor(Color.TRANSPARENT)
            mAdd.gone()
            mTransfer.gone()
            return
        }
        if (addVisible() && !mShouldRemoveAdd) {
            mAdd.visible()
            mAdd?.setOnClickListener(addClick())
        } else {
            mAdd.gone()
        }
        if (transferVisible()) {
            mTransfer.visible()
            mTransfer?.setOnClickListener(transferClick())
        } else {
            mTransfer.gone()
        }
        mOwnerName.gone()
        mOwnerAvatar.visible()
        mOwnerArrow.visible()
        mOwnerContainer?.apply {
            mOwnerContainer.padHor(CommonUtils.dp2px(8.0f))
            background =
                roundSolidRect(
                    Color.parseColor("#F5F5F5"),
                    resources.getDimension(R.dimen.joywork_corner_small2)
                )
        }
        mOwnerAvatar?.run {
            val us = mValue?.normalOwners?.map {
                it.headImg
            } ?: ArrayList<String>()
            setUrls(us)
            mOwnerAvatar2?.setUrls(us)
        }
        if (!judged) {
            mOwnerContainer?.post {
                judged = true
                // 计算宽度
                val ms = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                mOwnerContainer!!.measure(ms, ms)
                mOwnerAvatar2!!.measure(ms, ms)
                mOwnerAvatar!!.measure(ms, ms)
                val w =
                    mOwnerContainer!!.measuredWidth - mOwnerAvatar!!.measuredWidth + mOwnerAvatar2!!.measuredWidth
                val ctx = mOwnerContainer!!.context
                var dp = 60
                if (mAdd?.isVisible().isTrue()) {
                    dp += 30
                }
                if (mTransfer?.isVisible().isTrue()) {
                    dp += 30
                }
                val remainW = CommonUtils.getScreentWidth(ctx) - CommonUtils.dp2FloatPx(dp)
                if (w >= remainW) {
                    // 压显
                    mOwnerAvatar?.setVertical(true)
                    mOwnerContainer?.padVertical(CommonUtils.dp2px(6.0f))
                    if (mOwnerArrow?.isVisible().isTrue()) {
                        val l = mOwnerArrow?.layoutParams ?: ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        l.height = CommonUtils.dp2px(20.0f)
                        mOwnerArrow?.layoutParams = l
                    }
//                    mOwnerAvatar?.switchTo(JoyWorkAvatarView.TYPE_SHORT)
//                    judgeWithGoneAdd()
                } else {
                    mOwnerContainer?.padVertical(CommonUtils.dp2px(0f))
                    mOwnerAvatar?.setVertical(false)
                    if (mOwnerArrow?.isVisible().isTrue()) {
                        val l = mOwnerArrow?.layoutParams ?: ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        l.height = ViewGroup.LayoutParams.MATCH_PARENT
                        mOwnerArrow?.layoutParams = l
                    }
                }
            }
        }
    }

    private var mShouldRemoveAdd = false

    private fun judgeWithGoneAdd() {
        mOwnerAvatar?.post {
            // 计算宽度
            val w = mOwnerContainer!!.width
            val ctx = mOwnerContainer!!.context
            var dp = 60
            if (mAdd?.isVisible().isTrue()) {
                dp += 30
            }
            val remainW = CommonUtils.getScreentWidth(ctx) - CommonUtils.dp2FloatPx(dp)
            if (w >= remainW) {
                mAdd.gone()
                mShouldRemoveAdd = true
            }
        }
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.OWNER
    }

    protected open fun getAvatarCallback(context: Context): JoyWorkAvatarViewCallback {
        return object : ExecutorCallback(null, context) {
            override fun getName(): String {
                return mValue?.normalOwners?.firstOrNull()?.realName ?: ""
            }

            override fun getImageAltText(
                urls: List<String>,
                context: Context,
                position: Int,
                type: Int
            ): Pair<String?, String?>? {
                val name = mValue?.normalOwners?.firstOrNull()?.realName ?: return null
                return Pair(name, context.string(R.string.joywork_owner2))
            }

            override fun isChief(position: Int): Boolean {
                return mValue?.normalOwners?.firstOrNull()?.chief.isChief()
            }
        }
    }

    override fun removeFromUI() {
        val a = mFragment?.activity ?: return
        LocalBroadcastManager.getInstance(a).unregisterReceiver(mReceiver)
    }

    open fun itemClick() = itemClick

    open fun addClick() = addClick

    open fun transferClick() = View.OnClickListener {

    }

    // 后面的添加按钮是否可见
    open fun addVisible(): Boolean = true

    open fun transferVisible(): Boolean = false
}

/**
 * 多负责人时。子待办创建
 */
class SubOwnersCreateFactory : CreateItemFactory() {
    private val click = View.OnClickListener {
        val f = mFragment ?: return@OnClickListener
        if (mValue?.subOwners.isLegalList()) {
            val intent = Intent(
                it.context,
                CreateOwnerListActivity::class.java
            )
            val data: List<JoyWorkUser> = mValue!!.subOwners
            OneTimeDataRepo.data = data
            f.startActivityForResult(intent, JoyWorkCreateFragment.OWNER_REQ)
        } else {
            val v = mAvatarAdd ?: return@OnClickListener
            mAvatarAddClick.onClick(v)
        }
    }

    private val mAvatarAddClick = View.OnClickListener {
        val value = mValue ?: return@OnClickListener
        val max =
            if (SUB_JOYWORK_ONETIME <= value.subOwners.size) SUB_JOYWORK_ONETIME else SUB_JOYWORK_ONETIME - value.subOwners.size
        ShortcutManager(null, it.context).selectContact(
            it.context,
            max
        ) { aBoolean: Boolean?, joyWorkUsers: ArrayList<JoyWorkUser>? ->
            if (aBoolean != null && aBoolean && joyWorkUsers.isLegalList()) {
                value.addSubJoyworkOwners(joyWorkUsers)
            }
        }
    }

    private val mObserver = Observer<Boolean> {
        updateUI()
    }

    private fun updateUI() {
        if (mValue?.subOwners.isLegalList()) {
            mContainer.visible()
            mEmptyTips.gone()
            val m = mValue?.subOwners?.map {
                val member = Members()
                member.fromJoyWorkUser(it)
                member
            }
            mTv?.run {
                hint = ""
                val arg = m!!.size
                text = if (arg == 1) {
                    try {
                        resources.getString(R.string.joywork_create_owner, m.first().realName)
                    } catch (e: Exception) { // 加个 try-catch，防止上面出现异常
                        resources.getString(R.string.joywork_create_owner_text, arg)
                    }
                } else {
                    resources.getString(R.string.joywork_create_owner_text, arg)
                }
            }
            if (mMembersAdapter == null) {
                mMembersAdapter = CreateMembersAdapter(2)
                mRv?.adapter = mMembersAdapter
            } else {
                mMembersAdapter?.updateAll(m)
            }
        } else {
            mContainer.gone()
            mEmptyTips.visible()
        }
    }

    private var mAvatarAdd: View? = null
    private var mRv: RecyclerView? = null
    private var mTv: TextView? = null
    private var mEmptyTips: View? = null
    private var mMembersAdapter: CreateMembersAdapter? = null
    private var mContainer: View? = null

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view =
            layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_owners, parent, false)
        view.setOnClickListener(click)
        mRv = view.findViewById(R.id.owners_list)
        mRv?.layoutManager = LinearLayoutManager(
            parent.context,
            LinearLayoutManager.HORIZONTAL,
            false
        )
        mMembersAdapter = CreateMembersAdapter(2)
        mRv?.adapter = mMembersAdapter
        mRv?.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                val a = mRv?.adapter ?: return
                if (parent.getChildPosition(view) != a.itemCount - 1) {
                    outRect.right = -32
                }
            }
        }, 0)
        mTv = view.findViewById(R.id.relation_text)
        mAvatarAdd = view.findViewById(R.id.mAvatarAdd)
        mAvatarAdd?.setOnClickListener(mAvatarAddClick)
        mEmptyTips = view.findViewById(R.id.empty_tips)
        mContainer = view.findViewById(R.id.container)
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.ownersChangeLivedata?.observe(a, mObserver)
    }

    override fun removeFromUI() {
        release()
    }

    override fun release() {
        mCreateViewModel?.ownersChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.OWNERS
    }
}

/**
 * 截止时间
 */
open class DeadlineCreateFactory : CreateItemFactory() {
    private val click = View.OnClickListener {
        if (!timeClickable()) {
            return@OnClickListener
        }
        // 截止时间
        val draft = Value()
        draft.type = Value.TYPE_DEADLINE
        draft.startTime = mValue!!.startTime
        draft.endTime = mValue!!.endTime
        draft.mAlertType = HashSet()
        draft.mAlertType.addAll(mValue!!.mAlertType)
        draft.duplicateEnum = mValue!!.duplicateEnum

        ShortcutManager(null, it.context).selectTime(draft) {
            if (draft.endTime.isLegalTimestamp()) {
                mValue?.replaceAlertType(draft.mAlertType)
                mValue?.updateDup(draft.duplicateEnum)
            } else {
                mValue?.replaceAlertType(HashSet<AlertType>())
                mValue?.updateDup(null)
            }
            mValue?.updateTime(draft.startTime, draft.endTime)
            JoyWorkShortcutDraft.updateDraftEndTime(draft.endTime)
            JoyWorkShortcutDraft.updateDraftStartTime(draft.startTime)
            afterSetTime()
        }
    }

    private val todayClick = View.OnClickListener {
        if (todayClickable()) {
            mValue?.updateTime(null, todayTime())
            afterSetTime()
        }
    }

    private val tomorrowClick = View.OnClickListener {
        if (!tomorrowClickable()) {
            return@OnClickListener
        }
        // 截止时间设置为明天
        mValue?.updateTime(
            null,
            DateUtils.getSpecialHour(System.currentTimeMillis() + 24 * 60 * 60 * 1000, 16)
        )
        afterSetTime()
    }

    private var clearListener = View.OnClickListener {
        if (!clearClickable()) {
            return@OnClickListener
        }
        mValue?.updateTime(null, null)
    }

    private var mTodayContainer: View? = null
    private var mTomorrowContainer: View? = null
    private var mTimeText: TextView? = null
    private var mClearView: View? = null
    private var mTimeIcon: View? = null

    private val mObserver = Observer<Boolean> {
        updateUI()
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view =
            layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_deadline, parent, false)
        mTomorrowContainer = view.findViewById(R.id.mTomorrowContainer)
        mTimeIcon = view.findViewById(R.id.time_icon)
        mTodayContainer = view.findViewById(R.id.mTodayContainer)
        if (todayClickable()) {
            mTodayContainer?.setOnClickListener(todayClick)
        }
        if (tomorrowClickable()) {
            mTomorrowContainer?.setOnClickListener(tomorrowClick)
        }
        if (timeClickable()) {
            view.findViewById<View>(R.id.time_container).setOnClickListener(timeContainerClick())
        }
        mTimeText = view.findViewById(R.id.time_text)
        mClearView = view.findViewById(R.id.clear_time)
        if (clearClickable()) {
            mClearView.visible()
            mClearView?.setOnClickListener(clearClick())
        } else {
            mClearView.gone()
        }
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.timeChangeLivedata?.observe(a, mObserver)
    }

    private fun updateUI() {
        val view = mTimeText ?: return
        val startTime = mValue?.startTime
        val endTime = mValue?.endTime
        if (!startTime.isLegalTimestamp() && !endTime.isLegalLong()) {
            view.argb("#333333")
            view.setText(R.string.joywork_shortcut_other)
            mTodayContainer.visible()
            mTomorrowContainer.visible()
            mTimeIcon.visible()
            mClearView.gone()
        } else {
            mTodayContainer.gone()
            mTomorrowContainer.gone()
            if (clearClickable()) {
                mClearView.visible()
            } else {
                mClearView.gone()
            }
            mTimeIcon.gone()
            var text = getTimeStr2(startTime, endTime, view.resources, null)
            if (text == null) {
                text = ""
            }
            if (TaskDetailBindingAdapter.isEllipsize(
                    view,
                    text,
                    view.width - CommonUtils.dp2px(24f)
                )
            ) {
                view.maxLines = 2
                val index = text.indexOf("-")
                if (index > 0) {
                    var nextT = text.substring(0, index)
                    nextT += " - "
                    nextT += "\n"
                    nextT += text.substring(index + 1)
                    view.text = nextT
                } else {
                    view.text = text
                }
            } else {
                view.text = text
            }
            if (endTime.isLegalTimestamp() && endTime!! < System.currentTimeMillis() && timeShouldHighlight()) {
                view.argb("#FE3E33")
            } else {
                view.argb("#333333")
            }
        }
    }

    override fun removeFromUI() {
        mCreateViewModel?.timeChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.DEAD_LINE
    }

    open fun clearClick() = clearListener

    open fun afterSetTime() {

    }

    open fun timeContainerClick() = click

    open fun todayClickable() = true
    open fun tomorrowClickable() = true
    open fun timeClickable() = true

    /**
     * 清空截止时间按钮是否可点击
     */
    open fun clearClickable() = true

    open fun timeShouldHighlight() = false
}

/**
 * 重复
 */
open class DuplicateCreateFactory : CreateItemFactory() {
    private var mDupContainer: LinearLayout? = null
    private var mDupText: TextView? = null
    private var mRootView: View? = null

    private fun no() {
        mValue?.updateDup(null)
        mFragmentItf?.mItemFactories?.removeByClass(DuplicateCreateFactory::class.java)
    }

    private val mLiveDataObserver = Observer<Boolean> {
        updateUI()
    }

    // 没有设置截止时间时，不显示重复
    private val mDeadlineObserver = Observer<Boolean> {
        if (!(mValue?.endTime.isLegalTimestamp())) {
            mFragmentItf?.mItemFactories?.removeByClass(DuplicateCreateFactory::class.java)
            mValue?.updateDup(null)
        }
    }

    fun showDupDialog(
        ctx: Context,
        click: (DuplicateEnum) -> Unit
    ) {
        showDuplicateDialog(
            ctx,
            if (mValue?.duplicateEnum == null) DuplicateEnum.NO else mValue!!.duplicateEnum,
            click
        )
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_duplicate,
            parent,
            false
        )
        mRootView = view
        view.tag = this
        mDupContainer = view.findViewById(R.id.dup_container)
        if (itemClickable()) {
            mDupContainer!!.setOnClickListener {
                showDupDialog(parent.context) { duplicateEnum: DuplicateEnum? ->
                    if (duplicateEnum == DuplicateEnum.NO) {
                        // 不重复
                        no()
                    } else {
                        mValue?.updateDup(duplicateEnum)
                    }
                }
            }
        }
        mDupText = view.findViewById(R.id.dupText)

        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.dupChangeLivedata?.observe(a, mLiveDataObserver)
        mCreateViewModel?.timeChangeLivedata?.observe(a, mDeadlineObserver)
    }

    private fun updateUI() {
        if (mValue?.duplicateEnum == null || mValue?.duplicateEnum == DuplicateEnum.NO) {
            mRootView.gone()
            return
        }
        mRootView.visible()
        mDupContainer?.visible()
        mDupText?.visible()
        if (mValue?.duplicateEnum == null) {
            mDupText?.setHint(R.string.joywork_duplicate_text)
            mDupText.padHor(CommonUtils.dp2px(0f))
            mDupText?.setBackgroundColor(Color.TRANSPARENT)
            mDupText?.text = ""
        } else {
            mDupText?.setBackgroundResource(R.drawable.joywork_item_bg)
            mDupText.padHor(CommonUtils.dp2px(8.0f))
            mDupText?.hint = ""
            if (mValue?.duplicateEnum == DuplicateEnum.NO) {
                mDupText?.text = mFragment?.getString(mValue!!.duplicateEnum.resId) ?: ""
            } else {
                val a = mFragment?.getString(mValue!!.duplicateEnum.resId) ?: ""
                mDupText?.text = mFragment?.getString(R.string.joywork_repeat_tips, a) ?: ""
            }
        }
//        }
    }

    override fun removeFromUI() {
        (mDupContainer?.parent as? ViewGroup)?.removeView(mDupContainer)
        release()
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.DUP
    }

    override fun release() {
        mDupContainer = null
        mDupText = null
        mCreateViewModel?.dupChangeLivedata?.removeObserver(mLiveDataObserver)
        mCreateViewModel?.timeChangeLivedata?.removeObserver(mDeadlineObserver)
    }

    open fun itemClickable(): Boolean = true
}

/**
 *  团队待办分组
 */
class GroupCreateFactory : CreateItemFactory() {
    private val click = View.OnClickListener {
        val value = mValue ?: return@OnClickListener
        if (value.projectId.isLegalString()) {
            // 选择分组
            DialogSupporter(null, it.context).listGroupOld(
                value.projectId,
                value.groups,
                value.selId
            ) { group: Group? ->
                if (group != null) {
                    value.updateGroupUI(group.groupId)
                }
            }
        }
    }

    private val mObserver = Observer<Boolean> {
        updateUI()
    }

    private var mGroupTitle: TextView? = null
    private var mContainer: View? = null

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_group,
            parent,
            false
        )
        view.setOnClickListener(click)
        mGroupTitle = view.findViewById(R.id.group)
        mContainer = view.findViewById(R.id.group_container)
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.groupChangeLivedata?.observe(a, mObserver)
    }

    private fun updateUI() {
        val id = mValue?.groupId ?: return
        val context = mGroupTitle?.context ?: return
        val groups = mValue?.groups ?: return
        if (id.isLegalString()) {
            for (group in groups) {
                if (id == group.groupId) {
                    mGroupTitle?.hint = ""
                    mGroupTitle?.text = group.safeTitle(context)
                }
            }
        } else {
            mGroupTitle?.text = ""
        }
    }

    override fun release() {
        mCreateViewModel?.groupChangeLivedata?.removeObserver(mObserver)
    }

    override fun removeFromUI() {
        (mContainer?.parent as? ViewGroup)?.removeView(mContainer)
        release()
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.GROUP
    }
}

interface JoyWorkCallback<T> {
    fun invoke(t: T)
}

/**
 *  提醒我
 */
open class AlarmCreateFactory : CreateItemFactory() {
    private var mAlertContainer: View? = null
    private var mAlertView: TextView? = null
//    private var mCloseView: View? = null

    // 截止时间
    private val mDeadlineObserver = Observer<Boolean> {
        if (mValue?.endTime.isLegalTimestamp().isNotTrue()) {
            mFragmentItf?.mItemFactories?.removeByClass(AlarmCreateFactory::class.java)
            mValue?.cleanAlertTypes()
        }
    }

    // 修改提醒时间后
    private val mAlarmObserver = Observer<Boolean> {
        updateUI()
    }

    private val close = View.OnClickListener {
        mFragmentItf?.mItemFactories?.removeByClass(AlarmCreateFactory::class.java)
        mValue?.cleanAlertTypes()
    }

    private val itemClick = View.OnClickListener {
        JDMAUtils.clickEvent("", JoyWorkConstant.ALL_CREATE_SET_REMINDER, null)
        showAlertDialog(it.context)
    }

    fun showAlertDialog(context: Context) {
        showAlertDialog(context, object : JoyWorkCallback<Set<AlertType>> {
            override fun invoke(t: Set<AlertType>) {
                mValue?.replaceAlertType(t)
            }
        })
    }

    fun showAlertDialog(context: Context, callback: JoyWorkCallback<Set<AlertType>>) {
        JoyWorkAlertDialog(true, context, mValue?.mAlertType) {
            callback.invoke(it.tag as Set<AlertType>)
        }.show()
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_alarm,
            parent,
            false
        )
        mAlertContainer = view.findViewById(R.id.alert_container)
        mAlertContainer?.setOnClickListener(itemClick())
        mAlertView = view.findViewById(R.id.alert)
//        mCloseView = view.findViewById(R.id.close)
//        mCloseView?.setOnClickListener(clearClick())
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity
        if (a != null) {
            mCreateViewModel?.timeChangeLivedata?.observe(a, mDeadlineObserver)
            mCreateViewModel?.alertChangeLiveData?.observe(a, mAlarmObserver)
        }
    }

    override fun removeFromUI() {
        (mAlertContainer?.parent as? ViewGroup)?.removeView(mAlertContainer)
        release()
    }

    private fun updateUI() {
        val a = mFragment?.activity ?: return
        val mAlertType = mValue?.mAlertType
        if (!mAlertType.isLegalSet() || AlertType.containNO(mAlertType!!)) {
            removeFromUI()
        } else {
            mAlertView?.text = AlertType.toString(mAlertType, a)
        }
    }

    override fun release() {
//        mCreateViewModel?.normalOwnersChangeLivedata?.removeObserver(mOwnerObserver)
//        mCreateViewModel?.ownersChangeLivedata?.removeObserver(mOwnersObserver)
        mCreateViewModel?.alertChangeLiveData?.removeObserver(mAlarmObserver)
        mCreateViewModel?.timeChangeLivedata?.removeObserver(mDeadlineObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.REMIND
    }

    open fun itemClick() = itemClick

    open fun clearClick() = close
}

/**
 *  优先级
 */
open class PriorityCreateFactory : CreateItemFactory() {
    private var mPriorityView: TextView? = null
    private var mPriorityHintView: View? = null
    private var mContainer: View? = null

    fun showLevelDialog(ctx: Context, click: (Int) -> Unit) {
        showLevelDialog(ctx, mValue?.priority, click).show()
    }

    private val click = View.OnClickListener {
        JDMAUtils.clickEvent("", JoyWorkConstant.ALL_CREATE_SET_PRIORITY, null)
        showLevelDialog(it.context) { integer: Int? ->
            if (integer != null) {
                mValue?.updatePriority(integer)
            }
        }
    }

    private val close = View.OnClickListener {
        mFragmentItf?.mItemFactories?.removeByClass(PriorityCreateFactory::class.java)
        mValue?.updatePriority(null)
    }

    private val mLiveDataObserver = Observer<Boolean> { updateUI() }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_priority,
            parent,
            false
        )
        if (itemClickable()) {
            view.setOnClickListener(click)
        }
        mContainer = view.findViewById(R.id.priority_container)
        // 优先级
        mPriorityView = view.findViewById(R.id.priority)
        mPriorityHintView = view.findViewById(R.id.mPriorityHint)
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.priorityChangeLivedata?.observe(a, mLiveDataObserver)
    }

    override fun removeFromUI() {
        (mContainer?.parent as? ViewGroup)?.removeView(mContainer)
        release()
    }

    private fun updateUI() {
        mPriorityView?.run {
            val integer = mValue?.priority ?: JoyWorkLevel.NO.value
            val level = JoyWorkLevel.NO.getLevel(integer)
            if (level == JoyWorkLevel.NO) {
                mPriorityHintView.visible()
                mPriorityView.gone()
            } else {
                mPriorityView.visible()
                mPriorityHintView.gone()
                background = roundSolidRect(
                    level.getBgColor(),
                    CommonUtils.dp2FloatPx(4)
                )
                mPriorityView?.setTextColor(level.getColor())
                mPriorityView?.setText(level.getResId())
            }
        }
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.PRIORITY
    }

    override fun release() {
        mCreateViewModel?.priorityChangeLivedata?.removeObserver(mLiveDataObserver)
    }

    open fun itemClickable() = true
}

/**
 *  描述
 */
class DescCreateFactory : CreateItemFactory() {

    private var mDesView: TextView? = null


    private val mObserver = Observer<Boolean> {
        mDesView?.text = mValue?.des ?: ""
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_desc,
            parent,
            false
        )
        view.setOnClickListener {
            val intent = Intent(it.context, JoyWorkDesActivity::class.java)
            intent.putExtra(JoyWorkDesActivity.KEY, mValue!!.des)
            mFragment?.startActivityForResult(intent, 100)
        }
        mDesView = view.findViewById(R.id.des)
        return view
    }

    override fun onInit() {
        val fragment = mFragment?.activity
        fragment?.apply {
            mCreateViewModel?.desChangeLivedata?.observe(this, mObserver)
        }

    }

    override fun removeFromUI() {
        mCreateViewModel?.desChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.DESC
    }
}

/**
 * 文档
 */
class ShimoCreateFactory : CreateItemFactory() {
    private var mContainer: ViewGroup? = null

    private val mObserver = Observer<Boolean> {
        // 更新文档显示
        mContainer?.removeAllViews()
        if (mValue?.shimo.isLegalList()) {
            mContainer.visible()
            mValue?.shimo?.forEach {
                addSubView(it)
            }
        } else {
            mContainer.gone()
        }
    }

    private val shimoDelClick = View.OnClickListener {
        val d = it.tag as Documents
        mValue?.shimo?.remove(d)
        mContainer?.findViewWithTag<View>(d)?.apply {
            (parent as? ViewGroup)?.removeView(this)
        }
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_shimo,
            parent,
            false
        )
        mContainer = view.findViewById(R.id.shimo_container)
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.shimoChangeLivedata?.observe(a, mObserver)
    }

    private fun addSubView(d: Documents) {
        val fragment = mFragment
        val parent = mContainer
        if (fragment == null || parent == null) {
            return
        }
        val view: View = fragment.requireActivity().layoutInflater
            .inflate(R.layout.task_doc_item_create, parent, false)
        val titleView = view.findViewById<TextView>(R.id.doc_title)
        titleView.text = d.title

        val header = view.findViewById<ImageView>(R.id.doc_type)
        TaskDetailBindingAdapter.loadDocIcon(header, d)

        view.findViewById<View>(R.id.doc_del_icon).tag = d
        view.findViewById<View>(R.id.doc_del_icon).setOnClickListener(shimoDelClick)

        view.tag = d
        view.setOnClickListener { v -> toShimo(v.tag as Documents) }
        parent.addView(view)
    }

    private fun toShimo(documents: Documents) {
        val uri = Uri.parse(documents.url)
        val deepLink = uri.getQueryParameter("jdme_router")
        Router.build(deepLink).go(AppBase.getTopActivity())
    }

    override fun removeFromUI() {
        mCreateViewModel?.shimoChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.SHIMO
    }
}

/**
 * 文档
 */
class AttachmentCreateFactory : CreateItemFactory() {
    private var mContainer: ViewGroup? = null

    private val mObserver = Observer<Boolean> {
        // 更新文档显示
        mContainer?.removeAllViews()
        if (mValue?.mAtt.isLegalList()) {
            mContainer.visible()
            mValue?.mAtt?.forEach {
                addSubView(it)
            }
        } else {
            mContainer.gone()
        }
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_shimo,
            parent,
            false
        )
        mContainer = view.findViewById(R.id.shimo_container)
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.attachmentChangeLivedata?.observe(a, mObserver)
    }

    private val attDelClick = View.OnClickListener {
        val d = it.tag as Resources
        mValue?.mAtt?.remove(d)
        mContainer?.findViewWithTag<View>(d)?.apply {
            (parent as? ViewGroup)?.removeView(this)
        }
    }

    override fun removeFromUI() {
        mCreateViewModel?.attachmentChangeLivedata?.removeObserver(mObserver)
    }

    private fun addSubView(d: Resources) {
        val fragment = mFragment
        val parent = mContainer
        if (fragment == null || parent == null) {
            return
        }
        val view = fragment.requireActivity().layoutInflater.inflate(
            R.layout.task_res_item_create,
            parent,
            false
        )
        val titleView = view.findViewById<TextView>(R.id.res_title)
        titleView.text = d.name

        val header = view.findViewById<ImageView>(R.id.res_type)
        TaskDetailBindingAdapter.loadResIcon(header, d)

        view.findViewById<View>(R.id.res_del_icon).tag = d
        view.findViewById<View>(R.id.res_del_icon).setOnClickListener(attDelClick)

        view.tag = d
        view.setOnClickListener {
            val resources = it.tag as Resources
            val a = mFragment?.activity ?: return@setOnClickListener
            // 目前只有图片
            val i = Intent(a, JoyWorkImageActivity::class.java)
            val urls = ArrayList<String>()
            var pos = 0
            if (mValue?.mAtt?.contains(resources) == true) {
                mValue?.mAtt?.forEach {
                    urls.add(it.url)
                }
                pos = mValue?.mAtt?.indexOf(resources) ?: 0
            } else {
                urls.add(resources.url)
            }
            i.putStringArrayListExtra(JoyWorkImageActivity.EXTRA_IMAGE_INFO_LIST, urls)
            i.putExtra(JoyWorkImageActivity.EXTRA_IMAGE_POS, pos)
            a.startActivity(i)
//            openFileByX5(a, "JoyWorkAttachment", resources.url, resources.name, resources.resourceId, false, true);
        }

        parent.addView(view)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.ATTACHMENT
    }
}

/**
 * 协作人
 */
open class RelationCreateFactory : CreateItemFactory() {
    private var mRelationAdd: View? = null
    private var mRelationList: JoyWorkAvatarView? = null
    private var mRelationText: TextView? = null
    private var mArrowView: View? = null
    private var mRelationContainer: View? = null

    private val mObserver = Observer<Boolean> {
        updateMembersUI()
    }

    private val mAddClick = View.OnClickListener {
        selectContact(it.context, true)
        JDMAUtils.onEventClick(
            JoyWorkConstant.CREATE_RELATION_ADD,
            JoyWorkConstant.CREATE_RELATION_ADD
        )
    }

    fun selectContact(context: Context, isAdd: Boolean) {
        val value = mValue ?: return
        val activity = mFragment?.activity ?: return
        val selected = ArrayList<MemberEntityJd>()
        value.members.forEach {
            val m = MemberEntityJd()
            m.fromMembers(it)
            selected.add(m)
        }
        ExecutorUtils.selectRelation(activity, selected, mFragmentItf?.getSessionId(), { jds ->
            if (jds.isLegalList()) {
                val ms = ArrayList<Members>()
                for (user in jds!!) {
                    if (user != null) {
                        val m = Members()
                        m.fromDD(user)
                        m.userRole = TaskUserRole.EXECUTOR.code
                        ms.add(m)
                    }
                }
                value.addMembers(ms)
                afterSelect(ms, isAdd)
            }
        }, {

        })
    }

    open fun afterSelect(ms: ArrayList<Members>, isAdd: Boolean) {

    }

    private var mMoreClick = View.OnClickListener {
        JDMAUtils.onEventClick(JoyWorkConstant.CREATE_RELATION, JoyWorkConstant.CREATE_RELATION)
        if (mValue?.members.isLegalList()) {
            mFragment?.activity?.apply {
                val intent = Intent(
                    this,
                    CollaboratorListActivity::class.java
                )
                CollaboratorListActivity.isCanUpdate(intent, true)
                CollaboratorListActivity.setSessionId(intent, mFragmentItf?.getSessionId())
                mValue!!.members.forEach {
                    it.userRole = TaskUserRole.EXECUTOR.code
                }
                OneTimeDataRepo.data = mValue!!.members
                startActivityForResult(intent, JoyWorkCreateFragment.RELATION_REQ)
            }
        } else {
            selectContact(it.context, true)
        }
    }

    private val avatarCallback = object : JoyWorkAvatarViewCallback() {
        override fun getHintText2(
            urls: List<String>,
            context: Context,
            type: Int
        ): Pair<String?, String?> {
            val m = mValue?.members
            val arg = "${m!!.size}"
            val ans = if (m.size <= 1) {
                try {
                    context.resources.getString(
                        R.string.joywork_create_relation_text2,
                        m.first().realName
                    )
                } catch (e: Exception) {
                    context.resources.getString(R.string.joywork_create_relation_text, arg)
                }
            } else {
                context.resources.getString(R.string.joywork_create_relation_text, arg)
            }
            return Pair(null, ans)
        }
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_relation,
            parent,
            false
        )

        mRelationText = view.findViewById(R.id.relation_text)
        mRelationList = view.findViewById(R.id.relation_list)
        mRelationList?.mCallback = avatarCallback
        mRelationContainer = view.findViewById(R.id.owner_container)
        mRelationContainer?.setOnClickListener(containerClick())
        mArrowView = view.findViewById(R.id.arrow)
        mRelationAdd = view.findViewById(R.id.relation_add)
        mRelationAdd?.setOnClickListener(addClick())
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.relationChangeLivedata?.observe(a, mObserver)
    }

    private fun updateMembersUI() {
        val m = mValue?.members
        if (m.isLegalList()) {
            mRelationText.gone()
            mRelationList.visible()

            if (addVisible()) {
                mRelationAdd.visible()
            } else {
                mRelationAdd.gone()
            }
            mArrowView.visible()
            mRelationList?.run {
                val us = mValue?.members?.map {
                    it.headImg
                } ?: ArrayList<String>()
                setUrls(us)
            }

            mRelationContainer?.run {
                background = roundSolidRect(
                    Color.parseColor("#F5F5F5"),
                    resources.getDimension(R.dimen.joywork_corner_small2)
                )
                padHor(CommonUtils.dp2px(8.0f))
            }
        } else {
            mRelationList.gone()
            mRelationAdd.gone()
            mArrowView.gone()
            mRelationText.visible()
            mRelationText?.setHint(R.string.joywork_create_relation_hint)
            mRelationText?.text = ""
            mRelationContainer?.run {
                setBackgroundColor(Color.TRANSPARENT)
                padHor(CommonUtils.dp2px(0f))
            }
        }
    }

    override fun removeFromUI() {
        mCreateViewModel?.relationChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.RELATION
    }

    protected open fun addClick() = mAddClick

    protected open fun containerClick() = mMoreClick

    protected open fun addVisible() = true
}

/**
 * 风险问题
 */
class DetailRiskItemFactory : CreateItemFactory() {
    private var mPriorityView: TextView? = null
    private var mIcon: IconFontView? = null
    private var mView: View? = null

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(
            R.layout.jdme_joywork_create_fragment_risk,
            parent,
            false
        )
        // 优先级
        mPriorityView = view.findViewById(R.id.priority)
        mIcon = view.findViewById(R.id.priority_icon)
        mIcon?.setText(getEnum().getIconId())
        mView = view
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.riskChangeLivedata?.observe(a) {
            updateUI()
        }
    }

    private fun updateUI() {
        val riskEnum = valueByCode(mValue?.riskStatus)
        val priorityView = mPriorityView ?: return

        if (riskEnum !== RiskEnum.NO_SET) {
            priorityView.setText(riskEnum.resId)
            priorityView.setTextColor(riskEnum.textColor())
            priorityView.setPadding(
                CommonUtils.dp2px(10.0f),
                priorityView.paddingTop,
                CommonUtils.dp2px(10.0f),
                priorityView.paddingBottom
            )
            priorityView.background = roundSolidRect(
                riskEnum.bgColor(),
                CommonUtils.dp2FloatPx(4)
            )

        } else {
            priorityView.setText(R.string.joywork_risk_hint)
            priorityView.setTextColor(riskEnum.textColor())
            priorityView.setBackgroundColor(Color.TRANSPARENT)
            priorityView.setPadding(0, priorityView.paddingTop, 0, priorityView.paddingBottom)
        }
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.RISK
    }
}


/**
 * 启动时间
 */
open class LaunchCreateFactory : CreateItemFactory() {

    private var clearClick = View.OnClickListener {
        mValue?.updateLaunchTime(null)
    }

    private var mTimeText: TextView? = null
    private var mClearView: View? = null
    private var mContainer: View? = null

    private val mObserver = Observer<Boolean> {
        updateUI()
    }

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view =
            layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_launch, parent, false)
        mTimeText = view.findViewById(R.id.time_text)
        mClearView = view.findViewById(R.id.clear_time)
        mClearView?.setOnClickListener(clearClick())
        mContainer = view.findViewById(R.id.time_container)
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.launchTimeChangeLivedata?.observe(a, mObserver)
    }

    private fun updateUI() {
        val view = mTimeText ?: return
        val launchTime = mValue?.launchTime
        if (!launchTime.isLegalTimestamp()) {
            view.setText(R.string.joywork_detail_launch)
            mClearView.gone()
            mContainer?.setBackgroundColor(Color.TRANSPARENT)
        } else {
            mClearView.visible()
            val text = launchTime.launchTime(view.resources)
            mContainer?.setBackgroundResource(R.drawable.joywork_item_bg)
            view.text = text
        }
    }

    override fun removeFromUI() {
        mCreateViewModel?.launchTimeChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.LAUNCH_TIME
    }

    open fun clearClick() = clearClick
}

/**
 * 关联的清单
 */
open class OrderCreateFactory() : CreateItemFactory() {

    private val mObserver = Observer<Boolean> {
        updateUI()
    }

    private var mRv: RecyclerView? = null
    private var mRootView: View? = null
    private var mAdd: View? = null

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view = layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_order, parent, false)
        mRootView = view
        mRv = view.findViewById(R.id.rv_link_project_group)
        mRv?.layoutManager = LinearLayoutManager(parent.context, LinearLayoutManager.VERTICAL, false)
        mRv?.adapter = TaskLinkAdapter(
            canUpdate(),
            object : LinkCallback {
                override fun onClick(project: JoyWorkDetail.Project) {
                    JDMAUtils.onEventClick(
                        JoyWorkConstant.JOYWORK_DETAIL_GO_TEAM_DETAIL,
                        JoyWorkConstant.JOYWORK_DETAIL_GO_TEAM_DETAIL
                    )
                    val intent = TeamDetailActivity.inflateIntent(
                        parent.context,
                        project.title,
                        project.projectId,
                        JoyWorkProjectList.TYPE_NORMAL,
                        "",
                        false
                    )
                    (parent.context as? Activity)?.startActivity(intent)
                }

                override fun onDelClick(project: JoyWorkDetail.Project) {
                    delProject(project)
                    clickEvent {
                        ClickEventParam(
                            eventId = JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_RELATE_CHECKLIST_REMOVE
                        )
                    }

                }

                override fun changeGroup(project: JoyWorkDetail.Project) {
                    onChangeGroup(project)
                    clickEvent {
                        ClickEventParam(
                            eventId = JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_RELATE_CHECKLIST_CHOOSE_GROUP
                        )
                    }
                }
            },
            ArrayList<JoyWorkDetail.Project>()
        )
        mAdd = view.findViewById<View>(R.id.add)
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.projectChangeLivedata?.observe(a, mObserver)
        updateUI()
    }

    private fun updateUI() {
        val projects = mValue?.projects ?: ArrayList()
        if (!canUpdate() && projects.isEmpty()) {
            mRootView.gone()
        } else {
            mRootView.visible()
            if (canUpdate()) {
                mAdd?.visible()
                mAdd?.setOnClickListener(addClick())
            } else {
                mAdd?.gone()
            }
        }

        ( mRv?.adapter as? TaskLinkAdapter?)?.run {
            setCanUpdate(canUpdate())
            submitList(projects)
        }

        val layoutParams = mRv?.layoutParams as? MarginLayoutParams
        layoutParams?.let {
            it.bottomMargin = if (projects.isEmpty()) 0 else DensityUtil.dp2px(mRv?.context, 4f)
            mRv?.layoutParams = it
        }
    }

    override fun removeFromUI() {
        val rootView = mRootView
        if (rootView != null) {
            (rootView.parent as? ViewGroup)?.removeView(rootView)
        }
        mCreateViewModel?.projectChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.GROUP
    }

    private fun delProject(project: JoyWorkDetail.Project) {
        mValue?.delProject(project)
        afterProjectDel(project)
    }

    protected open fun afterProjectDel(project: JoyWorkDetail.Project) {

    }

    /**
     * 是否可对清单内容进行更新
     */
    open fun canUpdate() = true

    open fun addClick(): View.OnClickListener = View.OnClickListener {
        val f = (mFragment as? JoyWorkCreateFragment) ?: return@OnClickListener
        f.selectGroup()
        clickEvent {
            ClickEventParam(
                eventId = JoyWorkConstant.MOBILE_EVENT_TASK_CREATE_TASK_RELATE_CHECKLIST
            )
        }
    }

    private fun onChangeGroup(project: JoyWorkDetail.Project) {
        val ctx = mFragmentItf?.fragment?.activity ?: return
        DialogSupporter(null, ctx).listGroupOld(
            project.projectId,
            ArrayList(),
            project.groupId
        ) { group: Group? ->
            if (group != null) {
                project.groupId = group.groupId
                project.groupTitle = group.title
                mValue?.addProject(project)
                afterChangeGroup(project)
            }
        }
    }

    open fun afterChangeGroup(project: JoyWorkDetail.Project) {

    }
}


/**
 * 关联系统预置清单（如：京东人事）
 */
open class SysOrderCreateFactory : CreateItemFactory() {

    private val mObserver = Observer<Boolean> {
        updateUI()
    }

    private var mRv: RecyclerView? = null
    private var mRootView: View? = null
    private var link_icon: ImageView? = null
    private var mAdd: View? = null

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view =
            layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_sysorder, parent, false)
        mRootView = view
        link_icon = view.findViewById(R.id.link_icon)
        mRv = view.findViewById(R.id.rv_link_project_group)
        mRv?.layoutManager =
            LinearLayoutManager(parent.context, LinearLayoutManager.VERTICAL, false)
        mRv?.adapter = TaskLinkAdapter(
            canUpdate(),
            object : LinkCallback {
                override fun onClick(project: JoyWorkDetail.Project) {
                    val intent = TeamDetailActivity.inflateIntent(
                        parent.context,
                        project.title,
                        project.projectId,
                        JoyWorkProjectList.TYPE_OTHER,
                        mValue?.sysProjectIcon ?: "",
                        false
                    )
                    (parent.context as? Activity)?.startActivity(intent)
                }

                override fun onDelClick(project: JoyWorkDetail.Project) {
                    delProject(project)
                }

                override fun changeGroup(project: JoyWorkDetail.Project?) {

                }
            },
            ArrayList<JoyWorkDetail.Project>()
        )
        mAdd = view.findViewById<View>(R.id.add)
        if (canUpdate()) {
            mAdd?.visible()
            mAdd?.setOnClickListener(addClick())
        } else {
            mAdd?.invisible()
        }
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.sysProjectChangeLivedata?.observe(a, mObserver)
    }

    private fun updateUI() {
        val projects = mValue?.sysProjects ?: ArrayList()
        (mRv?.adapter as? TaskLinkAdapter)?.submitList(projects)
        val iv = link_icon ?: return
        JoyWorkViewItem.img(iv, mValue?.sysProjectIcon ?: "", R.drawable.joywork_project_icon)
    }

    override fun removeFromUI() {
        val rootView = mRootView
        if (rootView != null) {
            (rootView.parent as? ViewGroup)?.removeView(rootView)
        }
        mCreateViewModel?.sysProjectChangeLivedata?.removeObserver(mObserver)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.SYS_GROUP
    }

    private fun delProject(project: JoyWorkDetail.Project) {
        mValue?.delSysProject(project)
        afterProjectDel(project)
    }

    protected open fun afterProjectDel(project: JoyWorkDetail.Project) {

    }

    /**
     * 是否可对清单内容进行更新
     */
    open fun canUpdate() = false

    open fun addClick(): View.OnClickListener = View.OnClickListener {
//        val f = (mFragment as? JoyWorkCreateFragment) ?: return@OnClickListener
//        f.selectTarget()
    }
}

/**
 * 关联的目标
 */
open class TargetCreateFactory() : CreateItemFactory() {

    private val mObserver = Observer<Boolean> {
        updateUI()
    }


    private val mKrObserver = Observer<Boolean> {
        updateUI()
    }

    private val mObserver2 = Observer<Boolean> {
        val info = mValue?.safeGrayInfo() ?: GrayInfo()
        if (!info.hasGoalPermission() || !canUpdate()) {
            mAdd?.gone()
        } else {
            mAdd?.visible()
        }
    }

    private var mRv: RecyclerView? = null
    private var mRootView: View? = null
    private var mIcon: TextView? = null
    private var mAdd: View? = null

    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        val view =
            layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_target, parent, false)
        mRootView = view
        mIcon = view.findViewById(R.id.link_icon)
        mIcon?.setText(R.string.icon_padding_target)
        mRv = view.findViewById(R.id.rv_link_project_group)
        mRv?.layoutManager =
            LinearLayoutManager(parent.context, LinearLayoutManager.VERTICAL, false)
        // need merge KR with KpiTarget
        mRv?.adapter = TargetLinkAdapter(
            canUpdate(),
            object : TargetLinkAdapter.TargetCallback {
                override fun onClick(project: Any) {
                    val a = mFragmentItf?.fragment?.activity ?: return
                    if (project is KpiTarget) {
                        project.goDetail(a, 100)
                    } else if (project is KR) {
                        GoalDetailActivity.kr(
                            a,
                            project.krId,
                            project.permissions.krEditable(),
                            project.permissions.krDeletable(),
                            false,
                            100
                        )
                    }
                }

                override fun onDelClick(project: Any) {
                    if (project is KpiTarget) {
                        delProject(project)
                    } else if (project is KR) {
                        delKr(project)
                    }
                }
            },
            ArrayList<Any>()
        )
        mAdd = view.findViewById<View>(R.id.add)
        if (canUpdate()) {
            mAdd?.visible()
            mAdd?.setOnClickListener(addClick())
        } else {
            mAdd?.gone()
        }
        return view
    }

    override fun onInit() {
        val a = mFragment?.activity ?: return
        mCreateViewModel?.targetChangeLivedata?.observe(a, mObserver)
        mCreateViewModel?.krChangeLivedata?.observe(a, mKrObserver)
        mCreateViewModel?.grayInfoChangeLivedata?.observe(a, mObserver2)
    }

    private fun updateUI() {
        val list = mutableListOf<Any>()
        if (mValue?.targets.isLegalList()) {
            list.addAll(mValue!!.targets!!)
        }
        if (mValue?.krs.isLegalList()) {
            list.addAll(mValue!!.krs!!)
        }
        (mRv?.adapter as? TargetLinkAdapter)?.submitList(list)
    }

    override fun removeFromUI() {
        val rootView = mRootView
        if (rootView != null) {
            (rootView.parent as? ViewGroup)?.removeView(rootView)
        }
        mCreateViewModel?.targetChangeLivedata?.removeObserver(mObserver)
        mCreateViewModel?.krChangeLivedata?.removeObserver(mKrObserver)
        mCreateViewModel?.grayInfoChangeLivedata?.removeObserver(mObserver2)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.TARGET
    }

    private fun delProject(target: KpiTarget) {
        mValue?.delTarget(target)
        afterTargetDel(target)
    }

    private fun delKr(kr: KR) {
        mValue?.delKr(kr)
        afterKrDel(kr)
    }

    protected open fun afterTargetDel(target: KpiTarget) {

    }

    protected open fun afterKrDel(kr: KR) {

    }

    /**
     * 是否可对清单内容进行更新
     */
    open fun canUpdate() = true

    open fun addClick(): View.OnClickListener = View.OnClickListener {
        val f = (mFragment as? JoyWorkCreateFragment) ?: return@OnClickListener
        f.selectTarget()
    }
}

