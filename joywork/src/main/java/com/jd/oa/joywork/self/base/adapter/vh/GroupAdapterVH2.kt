package com.jd.oa.joywork.self.base.adapter.vh

import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.DimenRes
import androidx.recyclerview.widget.RecyclerView
import com.huawei.hmf.tasks.Tasks
import com.jd.oa.joywork.self.DetailReturnParcel
import com.jd.oa.joywork.*
import com.jd.oa.joywork.repo.JoyWorkAssignCallback
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.joywork.urge.JoyWorkUrgeActivity
import com.jd.oa.joywork.view.BubbleView
import com.jd.oa.joywork.bean.*
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.self.Exchangeable
import com.jd.oa.joywork.self.JoyWorkReverseHelper
import com.jd.oa.joywork.self.base.adapter.SelfBaseFragmentAdapter
import com.jd.oa.joywork.team.ProjectConstant
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.team.view.DividerLinearLayout
import com.jd.oa.joywork.utils.getTaskCompleteTimeString
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork.view.goneWhenChildrenGone

/**
 * 时间 VH
 */
class TimeVH(itemView: View) : RecyclerView.ViewHolder(itemView), Exchangeable {
    private val time by lazy { itemView.findViewById<TextView>(R.id.time) }

    override var exchangeable = false

    fun bindTime(timeTask: TimeTitleJoyWork) {
        itemView.setTag(R.id.jdme_tag_id, timeTask)
        exchangeable = false
        time.text = timeTask.time
    }
}


/**
 * 加载更多的 VH
 */
class LoadMoreVH(
    itemView: View,
    private val loadMoreCallback: ((blockType: String, callback: JoyWorkVMCallback) -> Unit)? = null,
    private val adapter: SelfBaseFragmentAdapter
) : RecyclerView.ViewHolder(itemView), Exchangeable {
    private val loadMore = itemView.findViewById<LinearLayout>(R.id.loadMore)
    private val loadMoreText = itemView.findViewById<TextView>(R.id.loadMoreText)
    private val loadMoreIcon = itemView.findViewById<View>(R.id.loadMoreIcon)
    private val pb = itemView.findViewById<View>(R.id.pb)

    override var exchangeable = false

    private fun updateUI(joyWork: JoyWork, allLoaded: Boolean) {
        if (allLoaded) {
            itemView.layoutParams.height = 0
        } else {
            itemView.layoutParams.height = CommonUtils.dp2px(itemView.context, 44)
        }
        val loadMoreJoyTask = joyWork as LoadMoreJoyWork
        if (loadMoreJoyTask.isLoading) {
            pb.visible()
            loadMoreText.setText(R.string.libui_loading)
            loadMoreIcon.gone()
        } else {
            pb.gone()
            loadMoreText.setText(R.string.me_load_more_data)
            loadMoreIcon.visible()
        }
    }

    fun bind(joyWork: LoadMoreJoyWork, next: Any?) {
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        updateUI(
            joyWork,
            joyWork.expandableGroup.title.count <= JoyWorkEx.countJoyWork(joyWork.expandableGroup.realItems)
        )
        loadMore.tag = joyWork
        loadMore.setOnClickListener {
            val loadMoreJoyWork = it.tag as LoadMoreJoyWork
            if (loadMoreJoyWork.isLoading) {
                return@setOnClickListener
            }
            loadMoreJoyWork.isLoading = true
            adapter.notifyItemChanged(adapterPosition)
            loadMoreCallback?.invoke("${loadMoreJoyWork.blockType}", object : JoyWorkVMCallback() {
                override fun onError(msg: String) {
                    loadMoreJoyWork.isLoading = false
                    updateUI(
                        loadMoreJoyWork,
                        joyWork.expandableGroup.title.count <= JoyWorkEx.countJoyWork(joyWork.expandableGroup.realItems)
                    )
                }

                override fun call(wrapper: JoyWorkWrapper) {
                    val items = loadMoreJoyWork.expandableGroup.realItems as ArrayList<JoyWork>
                    var allLoaded = false
                    wrapper.clientTasks?.forEach { eachGroup ->
                        if (loadMoreJoyWork.blockType == eachGroup.blockType && eachGroup.tasks != null && eachGroup.tasks.isNotEmpty()) {
                            items.remove(loadMoreJoyWork)
                            items.addAll(eachGroup.tasks)
                            allLoaded = JoyWorkEx.countJoyWork(eachGroup.tasks) < 20
                            if (!allLoaded) {
                                items.add(loadMoreJoyWork)
                            }
                            loadMoreJoyWork.expandableGroup.notifyItemChange()
                            adapter.processor.refreshData()
                            adapter.notifyDataSetChanged()
                        }
                    }
                    loadMoreJoyWork.isLoading = false
                }
            })
        }
        itemView.setBackgroundDrawable(
            DrawableEx.roundSolidDirRect(
                Color.WHITE,
                CommonUtils.dp2FloatPx(12),
                DrawableEx.DIR_BOTTOM
            )
        )
    }
}

interface GroupVHItf {
    /**
     * @return 1 不显示数字
     *  2 显示黑色数字
     *  其它 显示气泡数字
     */
    fun getTitleType(joyWorkTitle: JoyWorkTitle): Int

    /**
     * 处理分组标题
     *
     * @param indicator: 标题前面的箭头
     */
    fun onConfigGroupTitle(indicator: View)

    /**
     * 是否支持展开收起
     */
    fun expandable(): Boolean
}

interface ItemVHItf {
    /**
     * item 中显示的时间是哪种：
     * 1. 截止时间
     * 2. 完成时间
     * 3. 截止时间，超过当前时间后会显示成红色
     * 4. 其它. 不显示时间
     */
    fun getTimeType(): Int

    /**
     * 是否显示头像
     */
    fun showAvatar(): Boolean

    /**
     * 返回 item 在水平方向上的 margin
     */
    fun getMarginH(): Int

    /**
     * 从列表中删除指定的待办
     * @return true 表示不需要额外处理；false 表示需要额外处理
     */
    fun remove(joyWork: JoyWork): Boolean

    /**
     * 最后一个 item 时是否显示分隔线
     */
    fun showFooterDivider(): Boolean
}

interface GoalItemItf : ItemVHItf {
    fun showLock(kpiTitle: KpiTarget): Boolean
}

/**
 * 分组标题 VH
 */
class GroupVH(context: Context, parent: ViewGroup, private val adapter: SelfBaseFragmentAdapter) :
    RecyclerView.ViewHolder(
        context.inflater.inflate(
            R.layout.jdme_joywork_list_title,
            parent,
            false
        )
    ), Exchangeable {
    private val title = itemView.findViewById<TextView>(R.id.title)
    private val count = itemView.findViewById<TextView>(R.id.count)
    private val count2 = itemView.findViewById<TextView>(R.id.count2)
    private val indicator = itemView.findViewById<TextView>(R.id.indicator)
    private val countContainer = itemView.findViewById<BubbleView>(R.id.countContainer)

    //        private val divider = itemView.findViewById<View>(R.id.divider)
    override var exchangeable: Boolean = false

    fun bind(joyWorkTitle: JoyWorkTitle, expanded: Boolean, next: Any?, itf: GroupVHItf) {
        when (itf.getTitleType(joyWorkTitle)) {
            1 -> {
                countContainer.gone()
                count2.gone()
            }
            2 -> {
                countContainer.gone()
                count2.visible()
            }
            else -> {
                countContainer.visible()
                count2.gone()
            }
        }

        itemView.setTag(R.id.jdme_tag_id, joyWorkTitle)
        title.text = joyWorkTitle.title
        val c = Math.max(joyWorkTitle.count, 0)// 保证不会显示成负数
        count.text = JoyWorkNum.getStrNum(c)
        count2.text = JoyWorkNum.getStrNum(c)
        if (expanded) {
            indicator.setText(R.string.icon_padding_caredown)
        } else {
            indicator.setText(R.string.icon_padding_right)
        }
        itf.onConfigGroupTitle(indicator)
        if (next == null || next is JoyWorkTitle) {
            itemView.setBackgroundDrawable(
                DrawableEx.roundSolidDirRect(
                    Color.WHITE,
                    CommonUtils.dp2FloatPx(12),
                    DrawableEx.DIR_ALL
                )
            )
        } else {
            itemView.setBackgroundDrawable(
                DrawableEx.roundSolidDirRect(
                    Color.WHITE,
                    CommonUtils.dp2FloatPx(12),
                    DrawableEx.DIR_TOP
                )
            )
        }
    }

    fun getJoyWorkTitle(): JoyWorkTitle {
        return itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
    }

    /**
     * 用于更新标题的数量
     */
    fun updateCount() {
        val joyWorkTitle = itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
        count.text = JoyWorkNum.getStrNum(Math.max(joyWorkTitle.count, 0)) // 保证不会显示成负数
    }

    fun expandIfNecessary() {
        val title = itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle
        if (!title.expandableGroup!!.expand) {
            adapter.expandGroup(title)
        }
    }

    override fun toString(): String {
        val parentString = super.toString()
        return "【parentString = $parentString, adapterPos = $adapterPosition 】"
    }
}

class ChildVH(
    itemView: View,
    val adapter: SelfBaseFragmentAdapter,
) : RecyclerView.ViewHolder(itemView), Exchangeable {
    // 正常 item
    private val normalContainer = itemView.findViewById<View>(R.id.normal)
    private val mItemContainer = itemView.findViewById<DividerLinearLayout>(R.id.mItemContainer)

    private val title = itemView.findViewById<TextView>(R.id.title)
    private val deadline = itemView.findViewById<TextView>(R.id.deadline)
    private val cb = itemView.findViewById<TextView>(R.id.cb_task)
    private val cb2 = itemView.findViewById<ImageView>(R.id.cb_task2)
    private val cover = itemView.findViewById<View>(R.id.cover)
    private val deadlineIcon = itemView.findViewById<IconFontView>(R.id.deadlineIcon)
    private val deadlineContainer = itemView.findViewById<ViewGroup>(R.id.deadlineContainer)
    private val projectIcon = itemView.findViewById<IconFontView>(R.id.projectIcon)
    private val projectText = itemView.findViewById<TextView>(R.id.project)
    private val projectContainer = itemView.findViewById<ViewGroup>(R.id.projectContainer)
    private val risk = itemView.findViewById<TextView>(R.id.risk)
    private val cbContainer = itemView.findViewById<View>(R.id.cb_task_container)
    private val mAvatarView = itemView.findViewById<JoyWorkAvatarView>(R.id.mAvatarView)

    private val bottomContainer = itemView.findViewById<ViewGroup>(R.id.bottomContainer)

    // 侧滑菜单
    private val actionContainer = itemView.findViewById<LinearLayout>(R.id.swipe_right)

    override var exchangeable: Boolean = false

    fun bind(joyWork: JoyWork, next: Any?, itemVHItf: ItemVHItf) {
        val marginH = itemVHItf.getMarginH()
        (itemView.layoutParams as? ViewGroup.MarginLayoutParams)?.leftMargin = marginH
        (itemView.layoutParams as? ViewGroup.MarginLayoutParams)?.rightMargin = marginH
        itemView.setTag(R.id.jdme_tag_id, joyWork)
        bindItem(joyWork, itemVHItf)
        if (next == null || next is JoyWorkTitle) {
            itemView.setBackgroundDrawable(
                DrawableEx.roundSolidDirRect(
                    Color.WHITE,
                    CommonUtils.dp2FloatPx(12),
                    DrawableEx.DIR_BOTTOM
                )
            )
        } else {
            itemView.setBackgroundColor(Color.WHITE)
        }
        if ((next == null || !next.isJoyWork()) && !itemVHItf.showFooterDivider()) {
            mItemContainer.showBottom(false)
        } else {
            mItemContainer.showBottom(true)
        }
    }

    private fun bindItem(task: JoyWork, itemVHItf: ItemVHItf) {
        exchangeable = true

        normalContainer.setOnClickListener {
            JDMAUtils.onEventClick(JoyWorkConstant.ITEM_CLICK, JoyWorkConstant.ITEM_CLICK)
            JoyWorkMediator.goDetail(
                adapter.fragment,
                JoyWorkDetailParam(
                    taskId = task.taskId,
                    projectId = task.projectId,
                    taskName = task.title
                ).apply {
                    from = JoyWorkConstant.BIZ_DETAIL_FROM_LIST
                    reqCode = 10
                    obj = DetailReturnParcel()
                })
        }
        actionContainer.gone()
        val joyworkViewItem = JoyWorkViewItem
            .title(task, title) // 标题
            .risk(task, risk)
            .avatarView(task, mAvatarView, childItf = itemVHItf, { joyWork: JoyWork, pos: Int ->
                TaskStatusEnum.isFinish(
                    joyWork.owners.safeGet(pos)?.taskStatus ?: TaskStatusEnum.UN_FINISH.code
                )
            }) { joyWork ->
                if (!joyWork.owners.isLegalList()) {
                    ""
                } else {
                    val c = joyWork.owners.count {
                        TaskStatusEnum.isFinish(it.taskStatus)
                    }
                    mAvatarView.context.getString(
                        R.string.joywork_detail_owners,
                        "$c/${joyWork.owners.size}"
                    )
                }
            }
            .cover(task, cover, showCover(task))
        when (itemVHItf.getTimeType()) {
            1 -> {
                if (task.expandableGroup.id == RegionTypeEnum.OVERDUE.code || task.expandableGroup.id == RegionTypeEnum.FINISH.code) {
                    joyworkViewItem.deadlineByEndTime(task, deadline)// 截止时间
                        .deadlineIcon(task, deadlineIcon)
                } else if (task.expandableGroup.id == RegionTypeEnum.TODAY.code) { // 今天
                    // 按启动时间、开始时间、截止时间依次显示，有哪个显示哪个。且颜色不变红
                    joyworkViewItem.deadline3(task, deadline, deadlineIcon)
                } else if (task.expandableGroup.id == RegionTypeEnum.FUTURE.code || task.expandableGroup.id == RegionTypeEnum.INBOX.code) {
                    joyworkViewItem.deadline4(task, deadline, deadlineIcon)
                } else {
                    // 未安排没有任何时间，不显示
                    deadline.gone()
                    deadlineIcon.gone()
                }
            }
            2 -> {
                if (task.completeTime.isLegalTimestamp()) {
                    deadline.argb("#666666")
                    deadlineIcon.argb("#666666")
                    deadline.visible()
                    deadlineIcon.visible()
                    deadline.text = getTaskCompleteTimeString(
                        task.completeTime,
                        deadline.resources
                    )
                } else {
                    deadline.gone()
                    deadlineIcon.gone()
                }
            }
            3 -> {
                joyworkViewItem.deadlineByEndTime(task, deadline)// 截止时间
                    .deadlineIcon(task, deadlineIcon)
            }
            else -> {
                deadline.gone()
                deadlineIcon.gone()
            }
        }
        joyworkViewItem.projectText(task, projectText, projectIcon)
        if (deadline.isVisible()) {
            deadlineContainer.visible()
        } else {
            deadlineContainer.gone()
        }
        if (projectText.isVisible()) {
            projectContainer.visible()
        } else {
            projectContainer.gone()
        }
        if (bottomContainer.allChildrenGone()) {
            bottomContainer.gone()
        } else {
            bottomContainer.visible()
        }

        cb.setTag(R.id.jdme_tag_id, task)
        joyworkViewItem.checkboxNew(task, cb, cb2, cbContainer) { work: JoyWork, cbView: View ->
            if (work.isFinishing) {
                return@checkboxNew
            }
            JDMAUtils.onEventClick(
                JoyWorkConstant.INCOMPLETE_FINISH,
                JoyWorkConstant.INCOMPLETE_FINISH
            )
            JoyWorkViewItem.finishAction(work, adapter.context, object : JoyWorkUpdateCallback {
                override fun result(success: Boolean, errorMsg: String) {
                    Log.e("TAG", "result 1")
                    if (success) {
                        try {
                            Log.e("TAG", "result 2")
                            val tmp = cbView.tag as JoyWork
                            if (tmp.isFinish) {
                                ProjectConstant.sendBroadcast(
                                    cb.context,
                                    ProjectConstant.UNFINISH_ACTION
                                )
                                work.uiTaskStatus = TaskStatusEnum.UN_FINISH.code;
                            } else {
                                ProjectConstant.sendBroadcast(
                                    cb.context,
                                    ProjectConstant.FINISH_ACTION
                                )
                                work.uiTaskStatus = TaskStatusEnum.FINISH.code
                                if (work.isDup) {
                                    ToastUtils.showInfoToast(R.string.joywork_dup_work_finish_tips)
                                }
                            }
                            Log.e("TAG", "result 3")
                            checkboxSuccess(tmp, itemVHItf)
                            Log.e("TAG", "result 4")
                            try {
                                // 修改标题中的数量
                                (tmp.expandableGroup.title as JoyWorkTitle).count--
                                adapter.groupTitleSingleViewType.vhInvoke(
                                    tmp.expandableGroup,
                                    GroupVH::updateCount
                                )
                            } catch (e: Exception) {
                                Log.e("TAG", "result 5")
                                e.printStackTrace()
                            }
                        } catch (e: Exception) {
                            Log.e("TAG", "result 6")
                            result(false, JoyWorkEx.filterErrorMsg(""))
                        }
                    } else {
                        work.isFinishing = false
                        Log.e("TAG", "result 7")
                        adapter.notifyItemChanged(adapterPosition)
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }

                override fun onStart() {
                    work.isFinishing = true
                    adapter.notifyItemChanged(adapterPosition)
//                                    PromptUtils.showLoadDialog(context, context.string(R.string.me_feedback_tab_processing), false)
                }
            })
        } // 选框
    }

    private fun showCover(joyWork: JoyWork): Boolean {
        return joyWork.isFinishing
    }

    private fun checkboxSuccess(tmp: JoyWork, itemVHItf: ItemVHItf) {
        if (itemVHItf.remove(tmp)) {
            return
        }
        Log.e("TAG", "result = ${tmp.expandableGroup.realItems.size}  ${tmp.title}")
        val r = tmp.expandableGroup.realItems.removeAllIf {
            it.isJoyWork() && it.taskId == tmp.taskId
        }
        Log.e("TAG", "result = ${r.size} ${tmp.expandableGroup.realItems.size}  ${tmp.title}")
        tmp.expandableGroup.notifyItemChange()
        if (!tmp.expandableGroup.realItems.isLegalList()) {
            // 删除分组
            adapter.processor.removeGroup(tmp.expandableGroup)
        }
        adapter.processor.refreshData()
        adapter.notifyDataSetChanged()
    }

    fun getActions(joyWork: JoyWork): List<JoyWorkAction> {
        val actions = ArrayList<JoyWorkAction>()
        actions.add(DetailAction(adapter.context))
        actions.add(AssignAction(adapter.context))
        if (joyWork.isDeletable && joyWork.isDeleted) {
            actions.add(ReverseAction(adapter.context))
        }
        if (joyWork.isRemindable) {
            actions.add(UrgeAction(adapter.context))
        }
        return actions
    }

    fun onBeginDrag() {
        JDMAUtils.onEventClick(
            JoyWorkConstant.JOYWORK_START_DRAG,
            JoyWorkConstant.JOYWORK_START_DRAG
        )
        adapter.processor.backup(itemView.getTag(R.id.jdme_tag_id) as JoyWork)
        itemView.setBackgroundColor(Color.parseColor("#F6F6F6"))
    }

    fun onEndDrag() {
        val item = itemView.getTag(R.id.jdme_tag_id) as JoyWork
        itemView.setBackgroundColor(Color.TRANSPARENT)
        // 前后没挪位置
        if (!adapter.swaped) {
            return
        }
        adapter.swaped = false
        adapter.sortJoyWork(item)
    }
}

