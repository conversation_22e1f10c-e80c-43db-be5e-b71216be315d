package com.jd.oa.joywork.self.base.adapter

import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.self.group.SingleViewType
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.bean.TimeTitleJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.indexAtWithNull
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.repo.JoyWorkVMCallback
import com.jd.oa.utils.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.self.base.adapter.vh.*

/**
 * 我的待办 adapter。里面分为不同的块
 */
open class SelfBaseFragmentAdapter(
    val fragment: Fragment,
    private val groupVHItf: GroupVHItf,
    private val itemVHItf: ItemVHItf,
    groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    val context: FragmentActivity
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    val processor: DataProcessor = DataProcessor(groups)

    val groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>
        get() {
            return processor.groups
        }

    fun replaceAll(groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        processor.updateAll(groups)
        notifyDataSetChanged()
    }

    // 新增
    fun append(tmpGroups: java.util.ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>) {
        processor.appendGroups(tmpGroups)
        notifyDataSetChanged()
    }

    val fg: FragmentManager = context.supportFragmentManager

    // 普通的 item 显示的 viewType
    val itemType = 1

    // 时间间隔
    private val timeType = 2

    // 外界设置的加载更多回调
    var loadMoreCallback: ((blockType: String, callback: JoyWorkVMCallback) -> Unit)? = null

    // 加载更多与标题设置不同的 ViewType，不复用。减少各种 UI 变化时的奇葩问题
    private val loadMoreSingleViewType = SingleViewType<LoadMoreVH>(groups, 343252)

    val groupTitleSingleViewType = SingleViewType<GroupVH>(groups, 3525)
    var swaped = false
    private val groupClick = View.OnClickListener {
        val title = it.getTag(R.id.jdme_tag_id) as JoyWorkTitle
        toggleGroup(title)
    }


    fun restore(joyWork: JoyWork) {
        processor.restore(joyWork)
        kotlin.runCatching {
            notifyDataSetChanged()
        }
    }

    override fun getItemViewType(position: Int): Int {
        val data = processor.getData()[position]
        return if (data is JoyWorkTitle) {
            groupTitleSingleViewType.getViewType(data.expandableGroup!!)
        } else {
            when (val item = data as JoyWork) {
                is LoadMoreJoyWork -> { // 加载更多
                    loadMoreSingleViewType.getViewType(item.expandableGroup!!)
                }
                is TimeTitleJoyWork -> { // 时间间隔
                    timeType
                }
                else -> { // 普通 View
                    itemType
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        when {
            viewType == itemType -> {
                val view: View =
                    context.inflater.inflate(R.layout.jdme_joywork_list_item, parent, false)
                return ChildVH(view, this)
            }
            viewType == timeType -> {
                return TimeVH(
                    context.inflater.inflate(
                        R.layout.jdme_joywork_list_time,
                        parent,
                        false
                    )
                )
            }
            loadMoreSingleViewType.contain(viewType) -> {// 加载更多
                return loadMoreSingleViewType.getVH(viewType) {
                    val v = context.inflater.inflate(
                        R.layout.jdme_joywork_self_item_loadmore,
                        parent,
                        false
                    )
                    LoadMoreVH(v, loadMoreCallback, this)
                }
            }
            else -> {// 分组标题
                return groupTitleSingleViewType.getVH(viewType) {
                    GroupVH(context, parent, this)
                }
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        holder.itemView.visible()
        val data = processor.getData()[position]
        when (holder) {
            is TimeVH -> {
                holder.bindTime(data as TimeTitleJoyWork)
            }
            is LoadMoreVH -> {
                holder.bind(
                    data as LoadMoreJoyWork,
                    processor.getData().indexAtWithNull(position + 1)
                )
            }
            is GroupVH -> {
                holder.bind(
                    data as JoyWorkTitle,
                    data.expandableGroup!!.expand,
                    processor.getData().indexAtWithNull(position + 1),
                    groupVHItf
                )
                if(groupVHItf.expandable()){
                    holder.itemView.setOnClickListener(groupClick)
                }
            }
            is ChildVH -> {
                holder.bind(
                    data as JoyWork,
                    processor.getData().indexAtWithNull(position + 1),
                    itemVHItf
                )
            }
            else -> {
                holder.itemView.gone()
            }
        }
    }

    /**
     * 更新当前组的数量
     */
    fun updateTitleCount(targetGroup: ExpandableGroup<JoyWorkTitle, JoyWork>?) {
        groupTitleSingleViewType.vhInvoke(targetGroup, GroupVH::updateCount)
    }

    fun expandGroup(title: JoyWorkTitle) {
        if (title.expandableGroup?.expand != false) {
            return
        }
        val deltaSize = processor.toggleGroup(title)
        notifyItemRangeInserted(processor.getData().indexOf(title) + 1, Math.abs(deltaSize))
        notifyItemChanged(processor.getData().indexOf(title))
    }

    public fun toggleGroup(title: JoyWorkTitle) {
        val size = processor.toggleGroup(title)
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return processor.getData().size
    }

    open fun sortJoyWork(joyWork: JoyWork) {
        val param = processor.getLocationInfo(joyWork)
        JoyWorkRepo.sortJoyWork(joyWork.taskId, param, object : JoyWorkUpdateCallback {
            override fun onStart() {
                PromptUtils.showLoadDialog(
                    context,
                    context.string(R.string.me_feedback_tab_processing),
                    false
                )
            }

            override fun result(success: Boolean, errorMsg: String) {
                PromptUtils.removeLoadDialog(context)
                if (success) {
                    // 刷新整个列表。里面可能涉及到多个地方的数据修改。比如时间头的移除/添加，组中数量的修改等
                    processor.filterAndSyncData(context)
                    notifyDataSetChanged()
                } else {
                    // 恢复原样
                    restore(joyWork)
                    notifyDataSetChanged()
                    ToastUtils.showInfoToast(errorMsg)
                }
            }
        })
    }

    fun expandAll() {
        groups.forEach {
            processor.expandGroup(it.title)
        }
        notifyDataSetChanged()
    }
}