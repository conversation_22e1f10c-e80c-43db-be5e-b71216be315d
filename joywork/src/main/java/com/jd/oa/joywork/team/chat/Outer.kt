package com.jd.oa.joywork.team.chat

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.google.gson.GsonBuilder
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.ext.searchParams
import com.jd.oa.im.listener.Callback
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.create.TeamCreateActivity
import com.jd.oa.joywork.utils.JoyWorkNetConfig
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import org.json.JSONObject
import java.net.URLDecoder
import java.net.URLEncoder
import java.util.UUID

class ProjectSelectorTeamCreateHelper : TeamCreateActivity.Companion.TeamCreateHelper() {
    override fun addMember(intent: Intent, callback: (List<Members>) -> Unit) {
        val toPin = intent.getStringExtra("toPin")
        val toApp = intent.getStringExtra("toApp")
        if (toPin.isLegalString() && toApp.isLegalString()) {
            AppJoint.service(ImDdService::class.java)
                .getContactInfo(toApp, toPin, object : Callback<MemberEntityJd> {
                    override fun onSuccess(bean: MemberEntityJd?) {
                        if (bean == null) {
                            callback(listOf())
                            return
                        }
                        val m = Members()
                        m.fromDD(bean)
                        callback(listOf(m))
                    }

                    override fun onFail() {
                        callback(listOf())
                    }
                })
            return
        }
        callback(listOf())
    }

    override fun memberListClickable(intent: Intent): Boolean {
        return false
    }
}


object ProjectSelectorDeeplinkUtils {

    enum class SessionType(val value: Int) {
        // 群里面绑定清单时，IM 传递的 sessionType 是该枚举，这是 IM 自己使用的真实 sessionType
        // 在会话界面长按消息、加号新建任务时，那里的 sessionType 由于 IM 历史原因无法与此处 SessionType 一一对应
        // 但后者历史比较久，已有线上数据，所以只能在绑定时将此处 SessionType 转成旧数据
        // 1：单聊  2：群聊  6：单密聊 7：群密聊
        SingleChat(1),
        GroupChat(2),
        SingleSecretChat(6),
        GroupSecretChat(7)
    }

    private fun getLegacySessionType(sessionType: Int): Int {
        return if (sessionType == SessionType.SingleChat.value || sessionType == SessionType.SingleSecretChat.value) {
            0
        } else {
            1
        }
    }

    fun getProjectId(deeplink: String?): String {
        return try {
            val url = URLDecoder.decode(deeplink, "utf-8")
            val uri = Uri.parse(url)
            val json = JSONObject(uri.getQueryParameter("mparam"))
            json.getString("projectId")
        } catch (e: Exception) {
            ""
        }
    }

    fun getProjectTitle(deeplink: String): String {
        return try {
            val url = URLDecoder.decode(deeplink, "utf-8")
            val uri = Uri.parse(url)
            val json = JSONObject(uri.getQueryParameter("mparam"))
            json.getString("title")
        } catch (e: Exception) {
            ""
        }
    }

    fun buildExtraInfo(json: String?, callback: (String?, String?, String?) -> Unit) {
        // {"gid":"10204375416","sessionType":2,"sessionId":"10204375416","title":"鱼鱼鱼","projectId":"701114881718587392"}
        // {"toApp":"ee","sessionType":1,"sessionId":"hufeng24:ee:xn_taas_016:ee","toPin":"hufeng24"}
        try {
            val jsonObj = JSONObject(json)
            val sessionType = jsonObj.getInt("sessionType")
            val sessionId = jsonObj.getString("sessionId")

            val ans = JSONObject()
            ans.put("sessionId", sessionId)
            if (sessionType == SessionType.SingleChat.value || sessionType == SessionType.SingleSecretChat.value) {
                ans.put("sessionType", 0)
            } else {
                ans.put("sessionType", 1)
            }
//            ans.put("sessionType", sessionType)
            if (sessionType == 2) { // 群聊
                val gid = if (jsonObj.has("gid")) {
                    jsonObj.getString("gid")
                } else {
                    sessionId
                }

                val info = AppJoint.service(ImDdService::class.java).getGroupInfoLocal(gid)
                ans.put("to", sessionId)
                ans.put("sessionName", info?.name ?: "")
                callback(ans.toString(), sessionId, info?.name ?: "")
            } else {
                val toApp = jsonObj.getString("toApp")
                val toPin = jsonObj.getString("toPin")
                ans.put("to", toPin)
                ans.put("toApp", toApp)
                AppJoint.service(ImDdService::class.java)
                    .getContactInfo(toApp, toPin, object : Callback<MemberEntityJd> {
                        override fun onSuccess(bean: MemberEntityJd?) {
                            if (bean == null) {
                                onFail()
                                return
                            }
                            runCatching {
                                ans.put("sessionName", bean.name ?: "")
                                callback(ans.toString(), sessionId, bean.name ?: "")
                            }.onFailure {
                                onFail()
                            }
                        }

                        override fun onFail() {
                            callback(null, null, null)
                        }
                    })
            }
        } catch (e: Throwable) {
            callback(null, null, null)
        }
    }

    fun buildDeeplink(
        intent: Intent,
        context: Context,
        mobile: Boolean,
        id: String,
        title: String
    ): String {
        val map2 = hashMapOf<String, Any>()
        val sessionType = intent.searchParams("sessionType")
        if (sessionType.isLegalString()) {
            val r = runCatching {
//                map2["sessionType"] = getLegacySessionType(sessionType.toInt())
                map2["sessionType"] = sessionType.toInt()
            }
            if (r.isFailure) { // 理论上此处不会执行到
                val msg =
                    "the sessionType is illegal, sessionType = $sessionType, clz = ${sessionType::class.java.canonicalName}"
                MELogUtil.onlineE("ProjectSelector", msg)
                MELogUtil.localE("ProjectSelector", msg)
                map2["sessionType"] = sessionType
            }
        }
        val gid = intent.searchParams("gid")
        if (gid.isLegalString()) {
            map2["gid"] = gid
        }

        val sessionId = intent.searchParams("sessionId")
        if (sessionId.isLegalString()) {
            map2["sessionId"] = sessionId
        }

        val toPin = intent.searchParams("toPin")
        if (toPin.isLegalString()) {
            map2["toPin"] = toPin
        }

        val toApp = intent.searchParams("toApp")
        if (toApp.isLegalString()) {
            map2["app"] = toApp
        }
        if (mobile) {
            map2["projectId"] = id
            map2["title"] = title
        } else {
            map2["name"] = UUID.randomUUID().toString()
            map2["url"] = JoyWorkNetConfig.getProjectDetailUrl(id)
            val list = ArrayList<Map<String, String>>()
            list.add(mapOf("type" to "tab"))
            map2["containers"] = list
        }
        val gson = GsonBuilder().disableHtmlEscaping().create()
//        val params = URLEncoder.encode(gson.toJson(map2), "UTF-8")
        val params = gson.toJson(map2)
        return if (mobile) {
            val builder = Uri.parse(DeepLink.JOY_WORK_PROJECT_DETAIL).buildUpon()
            builder.appendQueryParameter(DeepLink.DEEPLINK_PARAM, params)
            builder.build().toString()
        } else {
            val encodedParams = URLEncoder.encode(params, "utf-8")
            val outputParam = "${encodedParams}&appId=joywork&paramType=1"
            DeepLink.JDME + "jm/biz/im/session/openContainer?params=${outputParam}"
        }
    }
}