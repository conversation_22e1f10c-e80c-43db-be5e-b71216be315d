package com.jd.oa.joywork.common

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.detail.key
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.utils.gone
import java.io.Serializable

abstract class IUserListItemCallback : Serializable {
    /**
     * 处理 item 中的更多
     * 具体数据已添加到 tv.tag 中
     */
    open fun bindItemAction(tv: TextView) {
        tv.gone()
    }

    open fun bindItem(itemView: View) {

    }

    open fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder? {
        return null
    }

    open fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, data: Any): Boolean {
        return false
    }
}

abstract class IUserListStrategy : IUserListItemCallback() {

    @StringRes
    abstract fun getEmptyStr(): Int

    @DrawableRes
    abstract fun getEmptyDrawable(): Int

    /**
     * 处理 item 中的更多
     * 具体数据已添加到 iv.tag 中
     */
    fun bindAvatar(avatar: ImageView) {
        val tag = avatar.tag ?: avatar.getTag(R.id.tag_key_data)
        JoyWorkViewItem.avatar(avatar, getAvatarUrl(tag))
        avatar.setOnClickListener {
            if (tag != null) {
                avatar.context.toSelfInfo(getAppId(tag), getUserId(tag))
            }
        }
    }

    abstract fun getAvatarUrl(any: Any?): String?
    abstract fun getUserId(any: Any): String
    abstract fun getAppId(any: Any): String

    /**
     * 获取 item 上半部分显示内容
     */
    abstract fun getTitleName(any: Any): String
    abstract fun getSubTitleText(any: Any): String

    /**
     * 返回所有需要通过接口获取各人信息的人
     */
    abstract fun getNeedInfoUsers(data: ArrayList<Any>): List<Members>

    /**
     * 将 members 中的参数得到到 data 中。
     * @return 如果 data 数据有变化，返回 true；否则返回 false
     */
    abstract fun merge(data: ArrayList<Any>, members: List<Members>): Boolean

    open fun remove(data: ArrayList<Any>, any: Any): Boolean {
        return false
    }
}

class JoyWorkUserListStrategy : IUserListStrategy() {

    override fun getEmptyStr(): Int {
        return R.string.joywork_owner_empty
    }

    override fun getEmptyDrawable(): Int {
        return R.drawable.joywork_executor_empty
    }

    override fun getAvatarUrl(any: Any?): String? {
        return (any as? JoyWorkUser)?.headImg
    }

    override fun getUserId(any: Any): String {
        return (any as? JoyWorkUser)?.emplAccount ?: ""
    }

    override fun getAppId(any: Any): String {
        return (any as? JoyWorkUser)?.ddAppId ?: ""
    }

    override fun getTitleName(any: Any): String {
        return (any as? JoyWorkUser)?.realName ?: ""
    }

    override fun getSubTitleText(any: Any): String {
        val c = (any as? JoyWorkUser) ?: return ""
        val title = if (c.titleName.isLegalString()) c.titleName else c.titleInfo?.titleName
        return listOf(c.deptInfo?.deptName, title).joinLegalString()
    }

    override fun getNeedInfoUsers(data: ArrayList<Any>): List<Members> {
        val r = mutableListOf<Members>()
        data.forEach {
            if (it is JoyWorkUser && listOf(
                    it.deptInfo?.deptName,
                    it.titleName,
                    it.realName,
                    it.headImg
                ).hasNull()
            ) {
                val m = Members()
                m.fromJoyWorkUser(it)
                r.add(m)
            }
        }
        return r
    }

    override fun merge(data: ArrayList<Any>, members: List<Members>): Boolean {
        if (!members.isLegalList())
            return false
        var result = false
        val map = mutableMapOf<String, Members>()
        members.forEach {
            map[it.key()] = it
        }
        data.forEach {
            if (it is JoyWorkUser && map[it.key()] != null) {
                val m = map[it.key()]!!
                it.deptInfo = m.deptInfo
                it.titleName = m.titleInfo?.titleName
                it.realName = m.realName
                it.headImg = m.headImg
                result = true
            }
        }
        return result
    }

    override fun remove(data: ArrayList<Any>, any: Any): Boolean {
        if (!data.isLegalList() || any !is JoyWorkUser)
            return false
        var result = false
        val key = any.key()
        data.removeAll {
            val a = it is JoyWorkUser && it.key() == key
            if (a) {
                result = true
            }
            a
        }
        return result
    }
}


class MemberListStrategy : IUserListStrategy() {
    override fun getEmptyStr(): Int {
        return R.string.joywork_owner_empty
    }

    override fun getEmptyDrawable(): Int {
        return R.drawable.joywork_executor_empty
    }

    override fun getAvatarUrl(any: Any?): String? {
        return (any as? Members)?.headImg
    }

    override fun getUserId(any: Any): String {
        return (any as? Members)?.emplAccount ?: ""
    }

    override fun getAppId(any: Any): String {
        return (any as? Members)?.ddAppId ?: ""
    }

    override fun getTitleName(any: Any): String {
        return (any as? Members)?.realName ?: ""
    }

    override fun getSubTitleText(any: Any): String {
        val c = (any as? Members) ?: return ""
        return listOf(c.deptInfo?.deptName, c.titleName).joinLegalString()
    }

    override fun getNeedInfoUsers(data: ArrayList<Any>): List<Members> {
        val r = mutableListOf<Members>()
        data.forEach {
            if (it is Members && listOf(it.deptInfo?.deptName, it.titleName).hasNull()) {
                r.add(it)
            }
        }
        return r
    }

    override fun remove(data: ArrayList<Any>, any: Any): Boolean {
        if (!data.isLegalList() || any !is Members)
            return false
        var result = false
        val key = any.key()
        data.removeAll {
            val a = it is Members && it.key() == key
            if (a) {
                result = true
            }
            a
        }
        return result
    }

    override fun merge(data: ArrayList<Any>, members: List<Members>): Boolean {
        if (!members.isLegalList())
            return false
        var result = false
        val map = mutableMapOf<String, Members>()
        members.forEach {
            map[it.key()] = it
        }
        data.forEach {
            if (it is Members && map[it.key()] != null) {
                val m = map[it.key()]!!
                it.deptInfo = m.deptInfo
                it.titleName = m.titleInfo?.titleName
                result = true
            }
        }
        return result
    }
}