package com.jd.oa.joywork.shortcut

import android.app.Activity
import com.jd.oa.joywork.TaskListTypeEnum
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.create.JoyWorkCreateActivity
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork2.backend.JoyWorkSource
import com.jd.oa.model.TenantCode
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel
import com.jd.oa.preference.PreferenceManager
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

object JoyWorkShortcutCreator {

    /**
     * 使用旧有草稿的创建子待办
     */
    fun showShortcutDialog(
        context: Activity,
        parentId: String,
        taskListTypeEnum: TaskListTypeEnum? = null
    ): JoyWorkShortcutDialog {
        val tmpData = ShortcutDialogTmpData()
        tmpData.parentId = parentId
        if (taskListTypeEnum != null) {
            tmpData.taskListTypeEnum = taskListTypeEnum
        }
        return JoyWorkShortcutDialog(
            context,
            JoyWorkShortcutDraft.useDraftWhenHad(),
            tmpData = tmpData
        ).apply {
            show()
        }
    }

    /**
     * 有草稿时，使用旧草稿；没草稿时，使用 [builder] 生成新的 [JoyWorkShortcutDraft] 创建
     */
    fun getShortcutDialog(
        context: Activity,
        tmpData: ShortcutDialogTmpData? = null,
        showGroup: Boolean = false, //是否显示分组
        projectId: String? = null,
        groups: List<Group>? = emptyList(),
        viewId: String? = null,
        viewType: String? = null,
    ): JoyWorkShortcutDialog {
        val tmp = tmpData ?: ShortcutDialogTmpData()
        return JoyWorkShortcutDialog(
            context,
            JoyWorkShortcutDraft.useDraftWhenHad(),
            tmpData = tmp,
            showGroup,
            projectId,
            groups,
            viewId,
            viewType
        )
    }

    /**
     * 有草稿时，使用旧草稿；没草稿时，使用 [builder] 生成新的 [JoyWorkShortcutDraft] 创建
     */
    fun getShortcutDialog(
        context: Activity,
        tmpData: ShortcutDialogTmpData? = null,
    ): JoyWorkShortcutDialog {
        val tmp = tmpData ?: ShortcutDialogTmpData()
        return JoyWorkShortcutDialog(
            context,
            JoyWorkShortcutDraft.useDraftWhenHad(),
            tmpData = tmp
        )
    }

    fun goCompleteCreate(
        activity: Activity,
        content: String?,
        info: String?,
        atUsers: String?,
        isExtend: Boolean,
        label: MEChattingLabel
    ) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.meChattingLabel = label
        tmpData.bizCode = JoyWorkUser.BIZ_CODE
        tmpData.fromDD = true
        tmpData.fromType = if (isExtend) ShortcutFromType.EXTEND else ShortcutFromType.MSG

        var user: JoyWorkUser? = null
        var isSingleChat = false
        val extraUser: MutableList<JoyWorkUser> = mutableListOf()
        val r = kotlin.runCatching {
            // 单聊
            // {"chatType":0,"mid":0,"sessionId":"hufeng24:ee:zhaoyu57:ee","sessionType":0,"timestamp":0,"to":"zhaoyu57","toApp":"ee"}
            // chatType：普通聊天、密聊
            // sessionType：0表示单聊 1表示群聊 2表示文件助手
            val obj = JSONObject(info)
            val sessionType = obj.getString("sessionType")
            val atUsersArray = atUsers?.let { JSONArray(atUsers) }
            val fileHelper = Objects.equals("2", sessionType)
            isSingleChat = Objects.equals("0", sessionType) || fileHelper
            if (obj.has("sessionName")) {
                tmpData.tripartiteName = obj.getString("sessionName")
            } else if (obj.has("to")) {
                tmpData.tripartiteName = obj.getString("to")
            }
            if (isSingleChat) {
                if (!fileHelper) {
                    user = JoyWorkUser().apply {
                        ddAppId = obj.getString("toApp")
                        val to = obj.getString("to")
                        tmpData.to = if (TenantCode.getByDDAppId(ddAppId) != null) {
                            emplAccount = to
                            to
                        } else {
                            userId = to
                            to
                        }
                        realName = obj.getString("sessionName")
                        headImg = obj.optJSONObject("sender")?.optString("avatar") ?: ""
                    }
                    tmpData.toApp = user!!.ddAppId
                    tmpData.sessionId = obj.getString("sessionId")
                }
            } else {
                tmpData.sessionId = obj.getString("sessionId")
            }
            // 遍历 at 人，组装 owners
            if (atUsersArray != null && Objects.equals("1", sessionType)) {
                for (i in 0 until atUsersArray.length()) {
                    val jsonObject: JSONObject = atUsersArray.getJSONObject(i)
                    val pin = jsonObject.optString("pin")
                    val app = jsonObject.optString("app")
                    // 过滤At机器人、全体成员
                    if (app != "robot.dd" && pin != "all") {
                        extraUser.add(
                            JoyWorkUser().apply {
                                this.ddAppId = app
                                if (TenantCode.getByDDAppId(app) != null) {
                                    this.emplAccount = jsonObject.optString("pin")
                                } else {
                                    this.userId = jsonObject.optString("pin")
                                }
                                this.realName = jsonObject.optString("nickname")
                            })
                    }
                }
            }
            tmpData.chatType = sessionType
        }
        val cur = JoyWorkUser().apply {
            headImg = PreferenceManager.UserInfo.getUserCover()
            realName = PreferenceManager.UserInfo.getUserRealName()
            app = PreferenceManager.UserInfo.getTimlineAppID()
            ddAppId = PreferenceManager.UserInfo.getTimlineAppID()
            if (TenantCode.getByDDAppId(ddAppId) != null) {
                emplAccount = PreferenceManager.UserInfo.getUserName()
            } else {
                userId = PreferenceManager.UserInfo.getUserId()
            }
        }

        JoyWorkCreateActivity.tmpData = tmpData
        JoyWorkCreateActivity.successRunnable = SuccessSnackBar(activity)
        if (isExtend) { // 来自聊天界面的 + 号
            if (r.isFailure) {
                // 解析失败了，走原来逻辑
                isSingleChat = false
            }
            if (isSingleChat && user != null) {
                // 单聊点击加号以对方
                tmpData.owners = arrayListOf(user!!)
            }
            JoyWorkCreateActivity.owners = tmpData.owners ?: arrayListOf()
            tmpData.extra = info
            JoyWorkCreateActivity.go(activity, "", null, null, false)
        } else {
            tmpData.owners = if (extraUser.isEmpty()) arrayListOf(cur) else extraUser
            tmpData.extra = info
            JoyWorkCreateActivity.owners = tmpData.owners
            JoyWorkCreateActivity.go(activity, content, null, null, false)
        }
    }

    fun showShortcutDialog(context: Activity, content: String?, info: String?, isExtend: Boolean) {
        val tmpData = ShortcutDialogTmpData()
        tmpData.bizCode = JoyWorkSource.IM.bizCode
        tmpData.fromDD = true
        tmpData.fromType = if (isExtend) ShortcutFromType.EXTEND else ShortcutFromType.MSG

        var user: JoyWorkUser? = null
        var isSingleChat = false
        val r = kotlin.runCatching {
            // 单聊
            // {"chatType":0,"mid":0,"sessionId":"hufeng24:ee:zhaoyu57:ee","sessionType":0,"timestamp":0,"to":"zhaoyu57","toApp":"ee"}
            // chatType：普通聊天、密聊
            // sessionType：0表示单聊 1表示群聊 2表示文件助手
            val obj = JSONObject(info)
            val sessionType = obj.getString("sessionType")
            val fileHelper = Objects.equals("2", sessionType)
            isSingleChat = Objects.equals("0", sessionType) || fileHelper
            if (obj.has("sessionName")) {
                tmpData.tripartiteName = obj.getString("sessionName")
            } else if (obj.has("to")) {
                tmpData.tripartiteName = obj.getString("to")
            }
            if (isSingleChat) {
                if (!fileHelper) {
                    user = JoyWorkUser().apply {
                        ddAppId = obj.getString("toApp")
                        val to = obj.getString("to")
                        tmpData.to = if (TenantCode.getByDDAppId(ddAppId) != null) {
                            emplAccount = to
                            to
                        } else {
                            userId = to
                            to
                        }
                        realName = obj.getString("sessionName")
                        headImg = obj.optJSONObject("sender")?.optString("avatar") ?: ""
                    }
                    tmpData.toApp = user?.ddAppId
                    tmpData.sessionId = obj.getString("sessionId")
                }
            } else {
                tmpData.sessionId = obj.getString("sessionId")
            }
            tmpData.chatType = sessionType
        }
        val cur = JoyWorkUser().apply {
            headImg = PreferenceManager.UserInfo.getUserCover()
            realName = PreferenceManager.UserInfo.getUserRealName()
            app = PreferenceManager.UserInfo.getTimlineAppID()
            ddAppId = PreferenceManager.UserInfo.getTimlineAppID()
            if (TenantCode.getByDDAppId(ddAppId) != null) {
                emplAccount = PreferenceManager.UserInfo.getUserName()
            } else {
                userId = PreferenceManager.UserInfo.getUserId()
            }
        }
        if (r.isFailure) {
            // 解析失败了，走原来逻辑
            isSingleChat = false
        }

        if (isExtend) { // 来自聊天界面的 + 号

            if (isSingleChat && user != null) {
                // 单聊点击加号以对方
                tmpData.owners = arrayListOf(user!!)
            }
            tmpData.extra = info
            JoyWorkShortcutDialog(
                context,
                JoyWorkShortcutDraft.useDraftWhenHad(),
                tmpData = tmpData
            ).show()
        } else { // 来自长按消息，不需要旧草稿
            tmpData.owners = arrayListOf(cur) // 长按消息，都以自己
            tmpData.extra = info
            val draft = JoyWorkShortcutDraft.newDraft()
            draft.title = content
            JoyWorkShortcutDialog(context, draft, tmpData = tmpData).show()
        }
    }
}