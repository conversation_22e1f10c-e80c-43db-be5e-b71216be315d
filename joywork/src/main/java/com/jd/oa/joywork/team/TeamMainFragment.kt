package com.jd.oa.joywork.team

import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.*
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkNum
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.filter.JoyWorkViewItem
import com.jd.oa.joywork.self.SelfBaseItf
import com.jd.oa.joywork.self.base.NetType
import com.jd.oa.joywork.self.drag.DragViewAdapter
import com.jd.oa.joywork.self.drag.ProjectListDrag
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.team.create.TeamCreateActivity
import com.jd.oa.joywork.team.kpi.GoalListActivity
import com.jd.oa.joywork.view.JoyWorkLoadMoreFooter
import com.jd.oa.joywork.view.red
import com.jd.oa.utils.*
import java.util.*
import kotlin.collections.ArrayList

/**
 * 团队待办列表
 */
class TeamMainFragment : BaseFragment(), SelfBaseItf {
    var recyclerView: RecyclerView? = null
    var llListEmpty: LinearLayout? = null
    var mContext: Context? = null
    var refreshView: SwipeRefreshLayout? = null
    var offset: Int = 0
    var mAdapter: MyAdapter? = null
    var fragment: TeamMainFragment? = null
    private val mRiskUpdateObserver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            getProjectList(offset = 0, NetType.REFRESH)
        }
    }

    companion object {
        const val FRG_TAG = "TeamMainFragment"
        const val CREATE_TEAM_CODE = 100
        const val SETTING_TEAM_CODE = 200
        fun getInstance(isTab: Boolean): TeamMainFragment {
            val ret = TeamMainFragment()
            val bundle = Bundle()
            bundle.putBoolean("isTab", isTab)
            ret.arguments = bundle
            return ret
        }

        private fun TeamMainFragment.isTab(): Boolean {
            return arguments?.getBoolean("isTab") ?: false
        }
    }

    private var mStrategy: JoyWorkMainStrategy = JoyWorkMainNormalStrategy
    private var mAdd: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mStrategy = if (isTab()) JoyWorkMainTabStrategy else JoyWorkMainNormalStrategy
        val view = inflater.inflate(R.layout.jdme_fragment_project_list, container, false)
        mContext = requireContext()
        fragment = this
        initView(view)
        getProjectList(offset, NetType.INIT)
        mRiskUpdateObserver observeRiskUpdate requireContext()
        return view
    }

    private fun initView(view: View) {
        view.apply {
            refreshView = findViewById(R.id.refreshView)
            refreshView.red(requireActivity())
            recyclerView = findViewById(R.id.recycleView)
            recyclerView?.layoutManager = LinearLayoutManager(context)
            mAdapter = MyAdapter(
                requireActivity(),
                mutableListOf(),
                mutableListOf()
            )
            recyclerView?.adapter = mAdapter
            ItemTouchHelper(ProjectListDrag(recyclerView)).attachToRecyclerView(recyclerView)
            setupLoadMore()
            loadMoreOnceFinish(true, false)
            llListEmpty = findViewById(R.id.ll_list_empty)
            refreshView?.setOnRefreshListener {
                getProjectList(offset = 0, NetType.REFRESH)
            }
            findViewById<View>(R.id.tv_create_project).setOnClickListener {
                goCreate()
            }
            findViewById<View>(R.id.add).apply {
                mAdd = this
                setOnClickListener {
                    goCreate()
                }
                val lp = (layoutParams as? ViewGroup.MarginLayoutParams)
                    ?: ViewGroup.MarginLayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                lp.apply {
                    rightMargin =
                        requireActivity().resources.getDimensionPixelSize(mStrategy.addViewRightMargin)
                    bottomMargin =
                        requireActivity().resources.getDimensionPixelSize(mStrategy.addViewBottomMargin)
                }
                layoutParams = lp
            }
        }
    }

    private fun getProjectList(offset: Int, type: NetType) {
        if (type != NetType.LOADMORE) {
            refreshView?.isRefreshing = true
        }
        ProjectRepo.projectList(
            object : JoyWorkProjectCallback() {
                override fun call(wrapper: JoyWorkProjectList, rawData: String) {
                    refreshView?.isRefreshing = false
                    wrapper.preSetList =
                        wrapper.preSetList ?: ArrayList<JoyWorkProjectList.ListDTO>()
                    //
//                    if (wrapper.sysSetList.isLegalList()) {
//                        wrapper.preSetList.addAll(wrapper.sysSetList)
//                    }
                    wrapper.preSetList?.forEach {
                        it.canDrag = false
                    }
                    when (type) {
                        NetType.INIT -> {
                            if (!wrapper.preSetList.isLegalList() && !wrapper.list.isLegalList()) {
                                llListEmpty?.visibility = View.VISIBLE
                                mAdapter?.clearData()
                            } else {
                                llListEmpty?.visibility = View.GONE
                                mAdapter?.refreshData(wrapper.list, wrapper.preSetList)
                            }
                            loadMoreOnceFinish(wrapper.total < 100, wrapper.list.isLegalList())
                        }
                        NetType.REFRESH -> {
                            if (wrapper.preSetList.isLegalList() || wrapper.list.isLegalList()) {
                                llListEmpty?.visibility = View.GONE
                                mAdapter?.refreshData(wrapper.list, wrapper.preSetList)
                            }
                            loadMoreOnceFinish(wrapper.total < 100, wrapper.list.isLegalList())
                        }
                        NetType.LOADMORE -> {
                            mAdapter?.append(wrapper.list, wrapper.preSetList)
                            loadMoreOnceFinish(wrapper.total < 100, true)
                        }
                    }
                }

                override fun onError(msg: String) {
                    ToastUtils.showToast(msg)
                    refreshView?.isRefreshing = false
                    when (type) {
                        NetType.LOADMORE -> {
                            loadMoreOnceFinish(false, true)
                        }
                        NetType.INIT -> {
                            llListEmpty?.visibility = View.VISIBLE
                        }
                        NetType.REFRESH -> {
                        }
                    }
                }
            }, offset
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            getProjectList(offset = 0, NetType.REFRESH)
            if (requestCode == CREATE_TEAM_CODE && data != null) {
                goDetail(
                    data.getStringExtra("projectId")!!,
                    data.getStringExtra("title")!!,
                    JoyWorkProjectList.TYPE_NORMAL,
                    null,
                    arrayListOf()
                )
            }
        }
    }

    private fun goCreate() {
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_NEW_TEAM, JoyWorkConstant.JOYWORK_NEW_TEAM)
        activity?.startActivityFromFragment(
            this@TeamMainFragment,
            Intent(context, TeamCreateActivity::class.java),
            CREATE_TEAM_CODE
        )
    }

    private fun goDetail(
        projectId: String,
        title: String,
        type: Int?,
        iconUrl: String?,
        gs: List<JoyWorkProjectList.ListDTO.GroupsDTO>,
        ps: List<String> = arrayListOf<String>()
    ) {
        when (type) {
            JoyWorkProjectList.TYPE_OTHER,
            JoyWorkProjectList.TYPE_NORMAL -> {
                if (type == JoyWorkProjectList.TYPE_OTHER) {
                    JDMAUtils.clickEvent("", JoyWorkConstant.GOAL_JDGOALS_CLICK, null)
                }
                val intent = TeamDetailActivity.inflateIntent(
                    context = requireActivity(),
                    title = title,
                    id = projectId,
                    isTab = isTab(),
                    type = type,
                    iconUrl = iconUrl,
                    gsp = gs,
                    psp = ps
                )
                activity?.startActivityFromFragment(
                    this@TeamMainFragment,
                    intent,
                    SETTING_TEAM_CODE
                )
            }
            JoyWorkProjectList.TYPE_KPI -> {
                // 跳转绩效管理界面
                JDMAUtils.clickEvent("", JoyWorkConstant.GOAL_JDHR_CLICK, null)
                startActivityForResult(
                    GoalListActivity.inflateIntent(
                        requireActivity(),
                        title,
                        isTab()
                    ), 100
                )
            }
        }
    }


    inner class MyAdapter(
        val context: Activity,
        list: List<JoyWorkProjectList.ListDTO>,
        kpiList: List<JoyWorkProjectList.ListDTO>
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>(), DragViewAdapter {

        private val mCreateClick = View.OnClickListener {
            goCreate()
        }

        inner class VH2(view: View) : RecyclerView.ViewHolder(view) {
            val mCreate = itemView.findViewById<View>(R.id.mCreate)
        }

        inner class VH3(view: View) : RecyclerView.ViewHolder(view) {
            val mNameView = itemView.findViewById<TextView>(R.id.tv_project)
        }

        inner class VH(view: View) : RecyclerView.ViewHolder(view) {
            val mDefault = itemView.findViewById<View>(R.id.mDefault)

            val unfinishTitle = itemView.findViewById<TextView>(R.id.unfinishTitle)
            val unfinishNums = itemView.findViewById<TextView>(R.id.unfinishNums)

            val mNameView = itemView.findViewById<TextView>(R.id.tv_project)
            val riskTitle = itemView.findViewById<TextView>(R.id.riskTitle)
            val riskCount = itemView.findViewById<TextView>(R.id.riskCount)
            val divider = itemView.findViewById<View>(R.id.divider)
            val itv_project_icon = itemView.findViewById<ImageView>(R.id.itv_project_icon)
            val itv_project_font = itemView.findViewById<TextView>(R.id.itv_project_font)
        }

        private var data = mutableListOf<JoyWorkProjectList.ListDTO>()
        private var backup: List<JoyWorkProjectList.ListDTO>? = null

        init {
            refreshData(list, kpiList, true)
        }

        fun clearData() {
            data.clear()
            notifyDataSetChanged()
        }

        fun refreshData(
            list: List<JoyWorkProjectList.ListDTO>?,
            kpiList: List<JoyWorkProjectList.ListDTO>?,
            init: Boolean = false
        ) {
            data.clear()
            if (kpiList.isLegalList()) {
//                data.add(JoyWorkProjectList.DividerListDTO(R.string.joywork_project_default))
                data.addAll(kpiList!!)
            }
            if (!init) {
//                data.add(JoyWorkProjectList.DividerListDTO(R.string.joywork_project_custom))
                if (list.isLegalList()) {
                    data.addAll(list!!)
                } else {
                    data.add(JoyWorkProjectList.EmptyListDTO())
                }
            }
            handleAddButton()
            notifyDataSetChanged()
        }

        fun append(
            list: List<JoyWorkProjectList.ListDTO>?,
            kpiList: List<JoyWorkProjectList.ListDTO>?
        ) {
            var kpiLastIndex = -1
            var normalFirstIndex = -1
            var hadEmpty = false
            data.forEachIndexed { index, listDTO ->
                if (listDTO.type == JoyWorkProjectList.TYPE_KPI) {
                    kpiLastIndex = index
                } else if (listDTO.type == JoyWorkProjectList.TYPE_NORMAL && normalFirstIndex == -1) {
                    normalFirstIndex = index
                } else if (listDTO is JoyWorkProjectList.EmptyListDTO) {
                    hadEmpty = true
                }
            }
            if (kpiList.isLegalList()) {
                if (kpiLastIndex < 0) {
                    // 原来没有默认清单
//                    data.add(0, JoyWorkProjectList.DividerListDTO(R.string.joywork_project_default))
                    kpiLastIndex = 0
                }
                data.addAll(kpiLastIndex + 1, kpiList!!)
            }
            if (list.isLegalList()) {
                if (hadEmpty) {
                    data.removeAll {
                        it is JoyWorkProjectList.EmptyListDTO
                    }
                }
                data.addAll(list!!)
            }
            handleAddButton()
            notifyDataSetChanged()
        }

        private fun handleAddButton() {
            val empty = data.has {
                it is JoyWorkProjectList.EmptyListDTO
            }
            if (empty) {
                mAdd?.gone()
            } else {
                mAdd?.visible()
            }
        }

        override fun canDrag(holder: RecyclerView.ViewHolder): Boolean {
            return (holder.itemView.getTag(R.id.jdme_tag_id) as? JoyWorkProjectList.ListDTO)?.canDrag
                ?: false
        }

        override fun canDropOver(
            recyclerView: RecyclerView,
            current: RecyclerView.ViewHolder,
            target: RecyclerView.ViewHolder
        ): Boolean {
            return (target.itemView.getTag(R.id.jdme_tag_id) as? JoyWorkProjectList.ListDTO)?.canDrag
                ?: false
        }

        private var swaped = false

        override fun swap(src: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder) {
            if (src.adapterPosition == target.adapterPosition) {
                return
            }
            swaped = true
            Collections.swap(data, src.adapterPosition, target.adapterPosition)
            notifyItemMoved(src.adapterPosition, target.adapterPosition)
        }

        override fun onBeginDrag(holder: RecyclerView.ViewHolder) {
            backup = ArrayList(data)
            val localHolder = holder as VH
            localHolder.divider.invisible()
            holder.itemView.setBackgroundColor(Color.parseColor("#F6F6F6"))
        }

        override fun onEndDrag(holder: RecyclerView.ViewHolder) {
            val localHolder = holder as VH
            localHolder.divider.visible()
            holder.itemView.setBackgroundColor(Color.WHITE)
            // 前后没挪位置
            if (!swaped) {
                return
            }
            val item = holder.itemView.getTag(R.id.jdme_tag_id) as JoyWorkProjectList.ListDTO
            swaped = false
            val index = data.indexOf(item)
            val front: String? = data.indexAtWithNull(index - 1)?.projectId
            val after: String? = data.indexAtWithNull(index + 1)?.projectId
            ProjectRepo.sortGroup(item.projectId, front, after) { success, msg ->
                if (success) {
                    // empty
                } else {
                    if (backup.isLegalList()) {
                        data.clear()
                        data.addAll(backup!!)
                        backup = null
                        notifyDataSetChanged()
                    }
                    Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            if (viewType == type_divider) {
                val view =
                    requireContext().inflater.inflate(
                        R.layout.jdme_fragment_project_list2,
                        parent,
                        false
                    )
                return VH3(view)
            } else if (viewType == type_empty) {
                val view =
                    requireContext().inflater.inflate(
                        R.layout.jdme_fragment_project_list3,
                        parent,
                        false
                    )
                return VH2(view)
            }
            val view =
                requireContext().inflater.inflate(R.layout.jdme_layout_project_item, parent, false)
            return VH(view)
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is VH) {
                val item = data[position]
                holder.itemView.setTag(R.id.jdme_tag_id, item)
                holder.mNameView.text = item.title
                holder.itemView.setOnClickListener {
                    val i = it.getTag(R.id.jdme_tag_id) as JoyWorkProjectList.ListDTO
                    goDetail(
                        i.projectId,
                        i.title,
                        i.type,
                        i.iconUrl,
                        i.groups ?: arrayListOf<JoyWorkProjectList.ListDTO.GroupsDTO>(),
                        i.permissions ?: arrayListOf<String>()
                    )
                }
                when (item.type) {
                    JoyWorkProjectList.TYPE_OTHER -> {
                        holder.mDefault.visible()
                        val value = item.riskNums.safeValue()
                        holder.riskTitle.visible()
                        holder.riskCount.visible()
                        holder.riskCount.text = item.riskNums.clamp99()
                        if (value > 0) {
                            holder.riskTitle.argb("#FE3E33")
                            holder.riskCount.argb("#FE3E33")
                        } else {
                            holder.riskTitle.argb("#666666")
                            holder.riskCount.argb("#666666")
                        }

                        holder.unfinishTitle.visible()
                        holder.unfinishNums.visible()
                        holder.unfinishNums.text = item.unFinishedNums.clamp99()
                    }
                    JoyWorkProjectList.TYPE_NORMAL -> {
                        val value = item.riskNums.safeValue()
                        holder.riskTitle.visible()
                        holder.mDefault.gone()
                        holder.riskCount.visible()
                        holder.riskCount.text = value.clamp99()
                        if (value > 0) {
                            holder.riskTitle.argb("#FE3E33")
                            holder.riskCount.argb("#FE3E33")
                        } else {
                            holder.riskTitle.argb("#666666")
                            holder.riskCount.argb("#666666")
                        }

                        holder.unfinishNums.visible()
                        holder.unfinishTitle.visible()
                        holder.unfinishNums.text = item.unFinishedNums.clamp99()
                    }
                    else -> {
                        holder.mDefault.gone()
                        holder.riskTitle.gone()
                        holder.riskCount.gone()
                        holder.unfinishNums.gone()
                        holder.unfinishTitle.gone()
                    }
                }

                if (item.type == JoyWorkProjectList.TYPE_KPI) {
                    holder.itv_project_icon.gone()
                    holder.itv_project_font.visible()
                } else {
                    holder.itv_project_font.gone()
                    holder.itv_project_icon.visible()
                    item.icon(holder.itv_project_icon)
                }
                val next = data.safeGet(position + 1)
                if (next is JoyWorkProjectList.DividerListDTO) {
                    holder.divider.gone()
                } else {
                    holder.divider.visible()
                }
            } else if (holder is VH2) {
                holder.mCreate.setOnClickListener(mCreateClick)
            } else if (holder is VH3) {
                val item = data[position]
                if (item is JoyWorkProjectList.DividerListDTO) {
                    holder.mNameView.setText(item.resId)
                }
            }
        }

        override fun getItemCount() = data.size

        val type_divider = 1
        val type_empty = 3
        val type_item = 2

        override fun getItemViewType(position: Int): Int {
            val d = data[position]
            if (d is JoyWorkProjectList.DividerListDTO) {
                return type_divider
            } else if (d is JoyWorkProjectList.EmptyListDTO) {
                return type_empty
            }
            return type_item
        }
    }

    override fun onResume() {
        super.onResume()
        JDMAUtils.onEventPagePV(
            requireContext(),
            JoyWorkConstant.JOYWORK_PAGE_TEAM,
            JoyWorkConstant.JOYWORK_PAGE_TEAM
        )
    }

    override fun onPageUnselected() {

    }

    override fun onPageSelected() {
//        getProjectList(offset = 0, NetType.INIT)
    }

    override fun onShowAtTabbar() {

    }

    override fun onDestroyView() {
        mRiskUpdateObserver stopObserveRiskUpdate requireContext()
        super.onDestroyView()
    }

    override fun getTaskListType(): TaskListTypeEnum {
        // 2022年06月27日  返回值无用
        return TaskListTypeEnum.PROJECT
    }

    private lateinit var mPullUpLoadHelper: PullUpLoadHelper
    private var mFooterView: JoyWorkLoadMoreFooter? = null

    private fun setupLoadMore() {
        mPullUpLoadHelper = PullUpLoadHelper(recyclerView) {
            getProjectList(offset, NetType.LOADMORE)
        }
        mFooterView = JoyWorkLoadMoreFooter(requireContext())
        mPullUpLoadHelper.setLoadFooter(mFooterView)
    }

    private fun loadMoreOnceFinish(finish: Boolean, visible: Boolean) {
        if (::mPullUpLoadHelper.isInitialized && visible) {
            if (finish) {
                mPullUpLoadHelper.setComplete()
            } else {
                mPullUpLoadHelper.setLoaded()
            }
        } else {
            mFooterView.gone()
        }
    }
}
