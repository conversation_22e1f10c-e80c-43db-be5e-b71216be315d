package com.jd.oa.joywork.notification;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.joywork.bean.JoyWorkUser;

import java.util.List;

public class JoyWorkNotification {
    public static final int UN_READ = 1;
    public static final int READ = 2;

    @SerializedName("operatorInfo")
    public JoyWorkUser operator;
    public String title;
    public Long endTime;
    public String taskId;
    public Integer readStatus;// 已读未读状态，1未读，2已读
    public String label; // 通知的标签分类
    public String deepLink;
    public String dyId;
    public Long id;// 表主键
    // 用于上拉、下拉加载更多时当作参数
    public Long createTime;

    public String event;
    public List<JoyWorkNotificationEvent> eventDetails;

    public boolean isRead() {
        return readStatus != null && readStatus == READ;
    }

    public void markRead() {
        readStatus = READ;
    }
}
