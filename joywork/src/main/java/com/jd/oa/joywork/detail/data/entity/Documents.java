package com.jd.oa.joywork.detail.data.entity;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

/**
 * Auto-generated: 2021-07-17 15:27:58
 */
public class Documents {

    private String pageType;
    @SerializedName(value = "documentId", alternate = {"id"})
    private String documentId;
    @SerializedName(value = "permission", alternate = {"permissionType"})
    private int permission;
    private String title;
    @SerializedName(value = "url", alternate = {"deepLink"})
    private String url;

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    public String getPageType() {
        return pageType;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setPermission(int permission) {
        this.permission = permission;
    }

    public int getPermission() {
        return permission;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrl() {
        return url;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj instanceof Documents) {
            return documentId.hashCode() == obj.hashCode();
        }
        return false;
    }

    @Override
    public int hashCode() {
        if (documentId == null) {
            return 0;
        }
        return documentId.hashCode();
    }


    @Override
    public String toString() {
        return "Documents{" +
                "pageType='" + pageType + '\'' +
                ", documentId='" + documentId + '\'' +
                ", permission=" + permission +
                ", title='" + title + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}