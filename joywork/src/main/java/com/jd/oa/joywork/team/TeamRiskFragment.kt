package com.jd.oa.joywork.team

import com.jd.oa.joywork.TaskStatusEnum

class TeamRiskFragment : TeamBaseFragment() {
    override fun getDefaultStatus(): TaskStatusEnum {
        return TaskStatusEnum.RISK
    }

    override fun getPageStatus(): TaskStatusEnum {
        return TaskStatusEnum.RISK
    }

    override fun getListenerAction(): ArrayList<String> {
        val ret = super.getListenerAction()
        ret.add(ProjectConstant.FINISH_ACTION)
        ret.add(ProjectConstant.UNFINISH_ACTION)
        return ret
    }
}