package com.jd.oa.joywork.team.kpi;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.bean.KpiQ;
import com.jd.oa.ui.wheel.adapter.AbstractWheelTextAdapter;
import com.jd.oa.ui.wheel.views.WheelView;

import java.util.ArrayList;
import java.util.List;

public class KpiQWheelView extends WheelView {

    public KpiQWheelView(Context context) {
        this(context, (AttributeSet) null);
    }

    public KpiQWheelView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public KpiQWheelView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setData(List<KpiQ> qList, int selPos) {
        qList = qList == null ? new ArrayList<>() : qList;
        setCenterDrawable(new ColorDrawable(Color.TRANSPARENT));
        setViewAdapter(new KpiQWheelViewAdapter(qList, getContext(), selPos));
        setCurrentItem(selPos);
    }

    private static class KpiQWheelViewAdapter extends AbstractWheelTextAdapter {

        private final List<KpiQ> qList;

        protected KpiQWheelViewAdapter(List<KpiQ> qList, Context context, int selPos) {
            super(context, R.layout.joywork_wheelview_item, R.id.mTitle, selPos, 18, 18);
            this.qList = qList;
        }

        @Override
        protected CharSequence getItemText(int index) {
            KpiQ item = qList.get(index);
            if (item == null || item.assessPeriodDesc == null) {
                return "";
            }
            return item.assessPeriodDesc;
        }

        @Override
        public int getItemsCount() {
            return qList.size();
        }
    }
}
