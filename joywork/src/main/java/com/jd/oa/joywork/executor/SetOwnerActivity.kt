package com.jd.oa.joywork.executor

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromUser
import com.jd.oa.joywork.detail.key
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalSet
import com.jd.oa.joywork.title.CenterTitleFragment
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.argb
import kotlinx.android.synthetic.main.joywork_select_group_roster.*

interface SetOwnerActivityCallback {
    fun finish(activity: SetOwnerActivity)
    fun add(activity: SetOwnerActivity)
    fun sure(members: ArrayList<MemberEntityJd>?, activity: SetOwnerActivity, owner: JoyWorkUser?)

    fun changeOwner(owner: JoyWorkUser?)
    fun remove(owner: JoyWorkUser)
    fun addUser(joyWorkUser: JoyWorkUser)
}

class SetOwnerActivity : BaseActivity(), SetOwnerFragmentCallback {
    companion object {
        var select: ArrayList<MemberEntityJd>? = null
        var callback: SetOwnerActivityCallback? = null
        fun start(activity: Activity) {
            activity.startActivity(Intent(activity, SetOwnerActivity::class.java))
        }
    }

    private var mSetOwnerFragment: SetOwnerFragment? = null

    private val mSelAll = View.OnClickListener {
        if (mSelectedUsers.size == select?.size) {
            mSetOwnerFragment?.changeSelAll(false)
        } else {
            mSetOwnerFragment?.changeSelAll(true)
        }
    }

    private val mSelectedUsers = HashSet<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.set_owner)
        hideAction()
        CenterTitleFragment().title(R.string.joywork_set_roler)
            .left(R.string.icon_direction_left, id = R.id.finish) {
                callback?.finish(this)
            }.into(this, R.id.mTitleContainer).post {
//            val add = findViewById<TextView>(R.id.add)
//            add.setTextSize(TypedValue.COMPLEX_UNIT_SP, 20.0f)
//            add.argb("#000000")

            val finishIcon = findViewById<TextView>(R.id.finish)
            finishIcon.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 20.0f)
            finishIcon.argb("#000000")
        }
        mSetOwnerFragment = SetOwnerFragment.getInstance()
        supportFragmentManager.beginTransaction()
            .add(R.id.mFilterContainer, mSetOwnerFragment!!)
            .commit()
        findViewById<View>(R.id.mSave).setOnClickListener {
            val ans = ArrayList<MemberEntityJd>()
            select?.forEach {
                if (mSelectedUsers.contains(it.key())) {
                    ans.add(it)
                }
            }
            callback?.sure(ans, this, owner)
        }
        select?.forEach {
            mSelectedUsers.add(it.key())
        }
        handleSureBtn()
        findViewById<View>(R.id.mSelCb).setOnClickListener(mSelAll)
        findViewById<View>(R.id.mSelAll).setOnClickListener(mSelAll)
    }

    // called from 【SetOwnerFragment】start
    override fun getUsers(): List<JoyWorkUser> {
        val ans = mutableListOf<JoyWorkUser>()
        select?.forEach {
            val u = JoyWorkUser()
            u.fromDD(it)
            ans.add(u)
        }
        return ans
    }

    override fun remove(joyWorkUser: JoyWorkUser) {
        val key = joyWorkUser.key()
        mSelectedUsers.remove(key)
        handleSureBtn()
        handleSelAll()
        callback?.remove(joyWorkUser)
        val service = AppJoint.service(ImDdService::class.java)
        val jd = MemberEntityJd()
        jd.fromUser(joyWorkUser)
        service.deleteMemberToSelector(jd)
    }

    override fun add(joyWorkUser: JoyWorkUser) {
        val key = joyWorkUser.key()
        if (mSelectedUsers.contains(key)) {
            return
        }
        mSelectedUsers.add(key)
        handleSureBtn()
        handleSelAll()
        callback?.addUser(joyWorkUser)
        // notify dd to add
        val service = AppJoint.service(ImDdService::class.java)
        val jd = MemberEntityJd()
        jd.fromUser(joyWorkUser)
        service.addMemberToSelector(jd)
    }

    private var owner: JoyWorkUser? = null

    override fun changeOwner(joyWorkUser: JoyWorkUser?) {
        this.owner = joyWorkUser
        callback?.changeOwner(joyWorkUser)
    }
    // called from 【SetOwnerFragment】over

    private fun handleSelAll() {
        val tv = findViewById<TextView>(R.id.mSelCb)

        if (mSelectedUsers.size == select?.size) {
            tv.setText(R.string.icon_padding_checkcircle)
            tv.argb("#FE3B30")
        } else {
            tv.argb("#CDCDCD")
            tv.setText(R.string.icon_prompt_circle)
        }
    }

    private fun handleSureBtn() {
        val mSave = findViewById<TextView>(R.id.mSave)
        if (mSelectedUsers.isLegalSet()) {
            mSave.setBackgroundResource(R.drawable.solid_all_100_fe3e33)
            mSave.isEnabled = true
        } else {
            mSave.setBackgroundResource(R.drawable.solid_all_100_1afe3e33)
            mSave.isEnabled = false
        }
    }
}