package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.R
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitContentController
import com.jd.oa.ui.dialog.ShitDialogConfig
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible


class AlertTypeSelectDialog(
    private val ctx: Context,
    private val canUpdate: Boolean,
    private val selectTypes: Set<AlertType>,
    val callback: (Set<AlertType>) -> Unit
) : BaseShitDialog(ctx, ShitDialogConfig()) {

    private var typeNoContainer: FrameLayout? = null

    private val listController = object : IShitContentController {
        override fun inflate(parent: FrameLayout): View {
            val inflater = ctx.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val content =
                inflater.inflate(R.layout.joywork_dialog_alert_type, parent, false)
            parent.addView(content)
            return content
        }
    }

    override var contentController: IShitContentController
        get() = listController
        set(_) {}

    private val alertTypeAdapter = AlertTypeAdapter(selectTypes, canUpdate){
        typeNoContainer?.run {
            val typeNoCb = findViewById<CheckBox>(R.id.cb)
            typeNoCb?.isChecked = it
        }

    }

    override fun setupCustomContentView(content: View) {
        content.background = DrawableEx.roundSolidDirRect(
            Color.parseColor("#F4F5F6"),
            context.resources.getDimension(R.dimen.joywork_corner_big),
            DrawableEx.DIR_TOP
        )
        content.findViewById<TextView>(R.id.close).setOnClickListener {
            dismiss()
        }
        content.findViewById<TextView>(R.id.title).setText(R.string.joywork_alert)
        val confirm = content.findViewById<TextView>(R.id.confirm)
        val secondaryTitle = content.findViewById<TextView>(R.id.secondary_title)
        typeNoContainer = content.findViewById(R.id.type_no_container)
        val typeNoTitle = typeNoContainer!!.findViewById<TextView>(R.id.title)
        typeNoTitle.setText(AlertType.NO.resId)
        val typeNoCb = typeNoContainer!!.findViewById<CheckBox>(R.id.cb)
        typeNoCb.isChecked = checkNoAlert(selectTypes)
        typeNoCb.isEnabled = false
        typeNoCb.isClickable = false
        if (canUpdate) {
            confirm.visible()
            confirm.setOnClickListener {
                onConfirm()
            }
            secondaryTitle.setText(R.string.joywork_alert_tips)
            typeNoContainer!!.setOnClickListener {
                alertTypeAdapter.clearSelect()
                typeNoCb.isChecked = true
            }
        } else {
            confirm.gone()
            secondaryTitle.setText(R.string.joywork_alert_tips2)
            typeNoCb.isEnabled = false
        }
        val recycler = content.findViewById<RecyclerView>(R.id.recycler)
        recycler.layoutManager = LinearLayoutManager(content.context)
        recycler.adapter = alertTypeAdapter
    }

    private fun checkNoAlert(types: Set<AlertType>?): Boolean {
        if (types.isNullOrEmpty()) return true
        if (types.size == 1 && types.contains(AlertType.NO)) return true
        return false
    }

    private fun onConfirm() {
        alertTypeAdapter.mSelected.remove(AlertType.NO)
        callback.invoke(alertTypeAdapter.mSelected)
        dismiss()
    }

    private class AlertTypeAdapter(
        selectTypes: Set<AlertType>,
        private val canUpdate: Boolean,
        private val onSetChanged: (Boolean) -> Unit,
    ) : RecyclerView.Adapter<AlertTypeHolder>() {

        val mSelected = selectTypes.toMutableSet()

        val allTypes = AlertType.values().filterNot {
            it == AlertType.NO
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AlertTypeHolder {
            return AlertTypeHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.jdme_dialog_alert_type_mul_select, parent, false)
            )
        }

        override fun onBindViewHolder(holder: AlertTypeHolder, position: Int) {
            val type = allTypes[position]
            val isSelected = mSelected.contains(type)
            holder.cb.isChecked = isSelected
            holder.cb.isEnabled = false
            holder.cb.isClickable = false
            holder.title.setText(type.resId)
            if (canUpdate) {
                holder.itemView.setOnClickListener {
                    if (mSelected.contains(type)) {
                        mSelected.remove(type)
                    } else {
                        mSelected.add(type)
                    }
                    onSetChanged.invoke(mSelected.isEmpty())
                    notifyItemChanged(position)
                }
            }
        }

        override fun getItemCount(): Int = allTypes.size

        fun clearSelect() {
            mSelected.clear()
            notifyDataSetChanged()
            onSetChanged.invoke(true)
        }

    }

    class AlertTypeHolder(view: View) : RecyclerView.ViewHolder(view) {
        var title: TextView = view.findViewById(R.id.title)
        var cb: CheckBox = view.findViewById(R.id.cb)
    }

}






