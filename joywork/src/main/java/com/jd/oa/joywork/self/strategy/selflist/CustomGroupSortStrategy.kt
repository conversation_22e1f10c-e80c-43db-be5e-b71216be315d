package com.jd.oa.joywork.self.strategy.selflist

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.jd.oa.joywork.TaskUserRole
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.self.base.SelfListBaseAdapter
import com.jd.oa.joywork.self.base.SelfListBaseAdapterItf
import com.jd.oa.joywork.self.base.SelfListCustomGroupAdapter
import com.jd.oa.joywork.self.base.SelfListViewModel
import com.jd.oa.joywork.team.DataClassifier
import com.jd.oa.joywork.team.TeamDetailViewModel
import com.jd.oa.joywork.team.bean.ProjectGroups
import com.jd.oa.joywork.team.bean.ProjectSortEnum
import com.jd.oa.joywork.team.view.HRecyclerView

interface CustomGroupSortStrategyItf {
    fun getCustomGroupType(): Int
    fun getProjectId(): String
}

class CustomGroupSortStrategy(
    next: StrategyBase?,
    private val mCustomGroupSortStrategyItf: CustomGroupSortStrategyItf,
    mItf: StrategyBaseItf,
    context: FragmentActivity,
    mSortFilterStatusViewModel: TeamDetailViewModel,
    mViewModel: SelfListViewModel
) : StrategyBase(next, mItf, context, mSortFilterStatusViewModel, mViewModel) {
    override fun canHandle(): Boolean {
        return mSortFilterStatusViewModel.mUIStatus.mSort.code == ProjectSortEnum.SORT_CUSTOM_GROUP.code
    }

    override fun needLoadMore(): Boolean {
        return false
    }

    override fun onGetList(userRole: TaskUserRole) {
        val status = mSortFilterStatusViewModel.mUIStatus
        mViewModel.listCustomGroup(
            status.mFilter,
            status.mSort,
            mCustomGroupSortStrategyItf.getCustomGroupType(),
            mCustomGroupSortStrategyItf.getProjectId(),
            status.mStatus
        )
    }

    override fun onCreateAdapter(
        data: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
        itf: SelfListBaseAdapterItf,
        fragment: Fragment,
        recyclerView: HRecyclerView?
    ): SelfListBaseAdapter {
        return SelfListCustomGroupAdapter(
            fragment,
            itf,
            data,
            recyclerView,
            context,
            itf.getProjectId()
        )
    }

    override fun onHandleData(any: Any): ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>> {
        val groups = any as? ProjectGroups ?: return ArrayList()
        return DataClassifier.flatProjectGroups(groups, context)
    }
}