package com.jd.oa.joywork.self.base

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.around.base.HeaderFooterRecyclerAdapterWrapper
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.*
import com.jd.oa.joywork.team.*
import com.jd.oa.utils.*
import java.util.*


/**
 * 我的待办 adapter。里面分为不同的块
 */
class SelfListBaseAdapterWrapper(
    val mRealAdapter: RecyclerView.Adapter<RecyclerView.ViewHolder>,
    val mLoadMoreAdapter: HeaderFooterRecyclerAdapterWrapper
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val mDataObserver = object : RecyclerView.AdapterDataObserver() {
        override fun onChanged() {
            <EMAIL>()
        }

        override fun onItemRangeChanged(positionStart: Int, itemCount: Int) {
            <EMAIL>(positionStart, itemCount)
        }

        override fun onItemRangeChanged(positionStart: Int, itemCount: Int, payload: Any?) {
            <EMAIL>(
                positionStart,
                itemCount,
                payload
            )
        }

        override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
            <EMAIL>(positionStart, itemCount)
        }

        override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
            <EMAIL>(positionStart, itemCount)
        }

        override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
            <EMAIL>(fromPosition, toPosition)
        }
    }

    init {
        mLoadMoreAdapter.registerAdapterDataObserver(mDataObserver)
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): RecyclerView.ViewHolder {
        return mLoadMoreAdapter.onCreateViewHolder(p0, p1)
    }

    override fun onBindViewHolder(p0: RecyclerView.ViewHolder, p1: Int) {
        mLoadMoreAdapter.onBindViewHolder(p0, p1)
    }

    override fun getItemCount(): Int {
        return mLoadMoreAdapter.itemCount
    }

    override fun getItemViewType(position: Int): Int {
        return mLoadMoreAdapter.getItemViewType(position)
    }

    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        mLoadMoreAdapter.onViewAttachedToWindow(holder)
        mRealAdapter.onViewAttachedToWindow(holder)
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        mLoadMoreAdapter.onAttachedToRecyclerView(recyclerView)
    }
}