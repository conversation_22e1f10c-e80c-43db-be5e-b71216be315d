package com.jd.oa.joywork.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork2.list.TodoShowType
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

class FloatMenuAdapter(
    private val context: Context,
    private val curViewType: TodoShowType,
    private val items: List<TodoShowType>,
    private val onItemClickListener: (TodoShowType) -> Unit
) : RecyclerView.Adapter<MenuVH>() {

//    val items = TodoShowType.values().toList().filterNot {
//        it.value == "kanban"
//    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuVH {
        val view: View =
            LayoutInflater.from(context).inflate(
                R.layout.joywork_float_menu_item,
                parent, false
            )
        return MenuVH(view)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: MenuVH, position: Int) {
        val item = items[position]
        val textColor = if (curViewType == item) {
            holder.checked.visible()
            Color.parseColor("#F63218")
        } else {
            holder.checked.gone()
            Color.parseColor("#1B1B1B")
        }
        holder.icon.apply {
            setText(item.resId)
//            setTextColor(textColor)
        }
        holder.title.apply {
            setText(item.titleId)
//            setTextColor(textColor)
        }
        holder.itemView.setOnClickListener {
            onItemClickListener.invoke(item)
        }
    }

}

class MenuVH(itemView: View) : RecyclerView.ViewHolder(itemView) {
    var icon: TextView = itemView.findViewById(R.id.menu_icon)
    var title: TextView = itemView.findViewById(R.id.menu_text)
    var checked: TextView = itemView.findViewById(R.id.checked)
}





