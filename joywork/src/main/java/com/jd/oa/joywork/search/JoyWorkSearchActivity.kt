package com.jd.oa.joywork.search

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.widget.EditText
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.common.ShortcutDialogCommon
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.team.kpi.TabsAdapter
import com.jd.oa.utils.*

class JoyWorkSearchActivity : BaseActivity() {
    companion object {
        fun start(context: Context) {
            val starter = Intent(context, JoyWorkSearchActivity::class.java)
            context.startActivity(starter)
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_HOME_SEARCH_BUTTON
                )
            }

        }
    }

    private var mEt: EditText? = null
    private var mClear: View? = null

    private var mJoyWorkSearchViewModel: JoyWorkSearchViewModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.joywork_search)
        hideAction()
        mJoyWorkSearchViewModel =
            ViewModelProviders.of(this).get(JoyWorkSearchViewModel::class.java)
        findViewById<View>(R.id.tv_cancel).setOnClickListener {
            finish()
        }
        mClear = findViewById(R.id.mClear)
        mEt = findViewById(R.id.et_search)
        mEt?.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                val str = mEt?.string()
                if (!str.isLegalString()) {
                    mJoyWorkSearchViewModel?.updateContent("")
                    mClear?.gone()
                    return
                }
                mJoyWorkSearchViewModel?.updateContent(str!!)
                mClear?.visible()
            }
        })
        ShortcutDialogCommon.openKeyboard(mEt!!, this)
        mClear?.setOnClickListener {
            mEt?.setText("")
        }

        val rv = findViewById<RecyclerView>(R.id.rv_search)
        rv.hor()
        val initIndex = 0
        val items = ArrayList<Pair<String, TaskStatusEnum>>()
        items.add(Pair(string(R.string.joywork_screen_all), TaskStatusEnum.ALL))
        items.add(Pair(string(R.string.joywork_screen_unfinish), TaskStatusEnum.UN_FINISH))
        items.add(Pair(string(R.string.joywork_screen_finish), TaskStatusEnum.FINISH))
        mJoyWorkSearchViewModel?.updateStatus(items[initIndex].second.code)
        rv.adapter = TabsAdapter(this, initIndex, items) { _, s ->
            val et = mEt
            if (et != null) {
                ShortcutDialogCommon.closeKeyboard(et, this)
            }
            mJoyWorkSearchViewModel?.updateStatus((s as TaskStatusEnum).code)
        }
        supportFragmentManager.beginTransaction()
            .add(R.id.me_fragment_container, JoyWorkSearchFragment()).commit()
    }
}