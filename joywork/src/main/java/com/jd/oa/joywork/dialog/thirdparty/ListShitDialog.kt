package com.jd.oa.joywork.dialog.thirdparty

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import com.jd.oa.joywork.R
import com.jd.oa.ui.dialog.BaseShitDialog
import com.jd.oa.ui.dialog.IShitContentController
import com.jd.oa.ui.dialog.ShitDialogConfig
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

class ListShitDialog(
    private val ctx: Context,
    private val listAdapter: ListAdapter,
    private val listConfig: ListShitDialogConfig
) : BaseShitDialog(ctx, listConfig) {
    private val listController = object : IShitContentController {
        override fun inflate(parent: FrameLayout): View {
            val inflater = ctx.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val rv =
                inflater.inflate(R.layout.joywork_dialog_list_content, parent, false) as ListView
            rv.divider = null
            parent.addView(rv)
            return rv
        }
    }

    override var contentController: IShitContentController
        get() = listController
        set(value) {}

    override fun setupCustomContentView(content: View) {
        if (listAdapter.count == 0) {
            content.gone()
            return
        }
        content.visible()
        (content as? ListView)?.apply {
            adapter = listAdapter
            (listAdapter as? ListShitDialogAdapter<*>)?.apply {
                dialog = this@ListShitDialog
            }
        }
        content.background = listConfig.contentBackground
    }
}

class ListShitDialogConfig : ShitDialogConfig() {
    var contentBackground = DrawableEx.solidRect(Color.WHITE)
    var showBottomDivider: Boolean = false
}

abstract class ListShitDialogAdapter<T : Any>(protected val items: List<T>) : BaseAdapter() {

    var dialog: ListShitDialog? = null

    override fun getItem(position: Int): Any {
        return items[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getCount() = items.size
}


open class SimpleListShitDialogAdapter<V : Any>(
    private val showBottomDivider: Boolean,
    private val context: Context,
    data: List<Pair<String, V>>,
    private val styleRes: Int = -1,
    private val click: ((Pair<String, V>) -> Unit)?
) : ListShitDialogAdapter<Pair<String, V>>(data) {
    private val layoutInflater =
        context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = layoutInflater.inflate(R.layout.joywork_dialog_list_item_simple, parent, false)
        val tv = view.findViewById<TextView>(R.id.text) as TextView
        if (styleRes > 0) {
            tv.setTextAppearance(context, styleRes)
        }
        val data = items[position]
        tv.text = data.first
        tv.tag = data
        text(position, tv)
        tv.setOnClickListener {
            dialog?.dismiss()
            click?.invoke(it.tag as Pair<String, V>)
        }
        val divider = view.findViewById<View>(R.id.divider)
        if (position == count - 1 && !showBottomDivider) {
            divider.gone()
        } else {
            divider.visible()
        }
        return view
    }

    protected open fun text(position: Int, tv: TextView) {

    }
}

class SimpleListShitDialogAdapter2<V : Any>(
    private val showBottomDivider: Boolean,
    private val context: Context,
    data: List<Pair<String, V>>,
    val styleResFactory: (Pair<String, V>) -> Int?,
    private val click: ((Pair<String, V>) -> Unit)?
) : ListShitDialogAdapter<Pair<String, V>>(data) {
    private val layoutInflater =
        context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = layoutInflater.inflate(R.layout.joywork_dialog_list_item_simple, parent, false)
        val tv = view.findViewById(R.id.text) as TextView
        val data = items[position]
        tv.text = data.first
        tv.tag = data
        tv.setOnClickListener {
            dialog?.dismiss()
            click?.invoke(it.tag as Pair<String, V>)
        }
        val divider = view.findViewById<View>(R.id.divider)
        if (position == count - 1 && !showBottomDivider) {
            divider.gone()
        } else {
            divider.visible()
        }
        val styleRes = styleResFactory(data) ?: 0
        if (styleRes > 0) {
            tv.setTextAppearance(context, styleRes)
        }
        return view
    }
}