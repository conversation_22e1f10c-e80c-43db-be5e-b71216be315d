package com.jd.oa.joywork.bean

import android.content.Context
import com.jd.oa.joywork.R

/**
 * @Author: hepiao3
 * @CreateTime: 2024/12/6
 * @Description:
 */
sealed class OperateButton {
    abstract fun title(context: Context): String
    // 是否是主按钮
    abstract val isMain: Boolean

    object MoveToButton : OperateButton() {
        override fun title(context: Context) =
            context.getString(R.string.joywork_project_button_move_to)

        override val isMain = true
    }

    object TransferButton : OperateButton() {
        override fun title(context: Context) =
            context.getString(R.string.joywork_project_button_transfer)

        override val isMain = false
    }

    object DeleteButton : OperateButton() {
        override fun title(context: Context) =
            context.getString(R.string.joywork_project_button_delete)

        override val isMain = false
    }
}
