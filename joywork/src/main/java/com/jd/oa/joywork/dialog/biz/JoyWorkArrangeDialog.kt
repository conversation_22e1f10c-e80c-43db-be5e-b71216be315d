package com.jd.oa.joywork.dialog.biz

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.util.Log
import android.view.*
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.com.libsharesdk.util.DeviceUtil
import com.jd.oa.AppBase
import com.jd.oa.joywork.BlockTypeEnum
import com.jd.oa.joywork.R
import com.jd.oa.joywork.dialog.DialogHelper
import com.jd.oa.ui.IconFontView
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.JDMAUtils

/**
 * 安排待办弹窗
 * Created by gzf on 2021/5/15
 */
class JoyWorkArrangeDialog(
    context: Context,
    from: Item?,
    onItemSelectCallback: OnItemSelectCallback,
    private val autoJDMA: Boolean = true
) : AppCompatDialog(context, R.style.JoyWorkDialogStyle) {
    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: MyAdapter
    private var callback: OnItemSelectCallback = onItemSelectCallback

    private var temp: Item? = from

    companion object {
        /**
         * @param autoJDMA: 是否自动在选择后添加埋点。此处为兼容旧处理逻辑，新加入的参数，默认为 true
         */
        fun show(
            activity: Context,
            from: Item?,
            onItemSelectCallback: OnItemSelectCallback,
            autoJDMA: Boolean = true
        ): JoyWorkArrangeDialog {
            val taskScreenDialog =
                JoyWorkArrangeDialog(activity, from, onItemSelectCallback, autoJDMA)
            taskScreenDialog.show()
            return taskScreenDialog
        }
    }

    init {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        window?.apply {
            setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            val layoutParams = attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.BOTTOM
            attributes = layoutParams
        }
    }

    private var mDialogHelper: DialogHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mDialogHelper = DialogHelper(this)
        mDialogHelper?.register()
        val view = LayoutInflater.from(context).inflate(R.layout.joywork_dialog_arrange, null)
        setContentView(view)
        initView(view)
    }

    private fun initView(view: View) {
        val results = listOf(
            Item(BlockTypeEnum.INBOX, context.getString(R.string.joywork_task_todo)),
            Item(BlockTypeEnum.TODAY, context.getString(R.string.joywork_plan_alert_today)),
            Item(BlockTypeEnum.IN_PLAN, context.getString(R.string.joywork_plan_alert_plan)),
            Item(BlockTypeEnum.OTHER_DAY, context.getString(R.string.joywork_plan_alert_one_day))
        )

        adapter = MyAdapter(results)
        val parent = view.parent as ViewGroup
        parent.setBackgroundResource(android.R.color.transparent)
        val window = window
        if (window != null) {
            val lp = window.attributes
            lp.width = DeviceUtil.getScreenWidth(context) // 宽度
            window.attributes = lp
        }
        view.apply {
            recyclerView = findViewById(R.id.recyclerView)
        }
        recyclerView.layoutManager = LinearLayoutManager(context)
        adapter.setOnItemClickListener(object : RecyclerViewItemOnClickListener<Item> {
            override fun onItemClick(view: View?, position: Int, item: Item) {
                if (autoJDMA) {
                    JDMAUtils.onEventClick(item.block.getClickId(), item.block.getClickId())
                }
                callback.onSelectAction(item)
            }

            override fun onItemLongClick(view: View?, position: Int, item: Item) {

            }
        })
        recyclerView.adapter = adapter
    }

    inner class MyAdapter(val dataList: List<Item>) :
        BaseRecyclerViewAdapter<Item>(AppBase.getTopActivity(), dataList) {
        override fun getItemLayoutId(viewType: Int): Int = R.layout.joywork_arrange_item

        override fun onConvert(holder: BaseRecyclerViewHolder?, item: Item, position: Int) {
            holder?.apply {
                setVisible(
                    R.id.jdme_id_myapply_dropdown_icon,
                    if (temp?.block?.code == item.block.code) View.VISIBLE else View.INVISIBLE
                )
                val text = getView<TextView>(R.id.jdme_id_myapply_dropdown_item)
                text.typeface =
                    Typeface.defaultFromStyle(if (temp?.block?.code == item.block.code) Typeface.BOLD else Typeface.NORMAL)
                text.text = item.text
                setVisible(R.id.iv_arrow_right, if (position == 2) View.VISIBLE else View.INVISIBLE)
                val icon = getView<IconFontView>(R.id.tv_icon)
                icon.setText(item.block.icon)
                icon.setTextColor(Color.parseColor(item.block.iconColor))
            }
        }
    }

    interface OnItemSelectCallback {
        fun onSelectAction(item: Item)
    }

    override fun dismiss() {
        super.dismiss()
        mDialogHelper?.unregister()
    }

    class Item(val block: BlockTypeEnum, val text: String)
}