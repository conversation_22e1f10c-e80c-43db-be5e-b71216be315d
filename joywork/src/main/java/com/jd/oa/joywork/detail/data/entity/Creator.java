package com.jd.oa.joywork.detail.data.entity;

import java.io.Serializable;

/**
 * Auto-generated: 2021-07-06 18:26:51
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class Creator implements Serializable {

    private String app;
    private String realName;
    private String emplAccount;
    private String headImg;
    private String teamId;
    private String sex;
    private String enName;
    private String userRole;
//    private DeptInfo deptInfo;
    private String userId;
    private String taskId;
    public String ddAppId;

    public void setApp(String app) {
        this.app = app;
    }

    public String getApp() {
        return app;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRealName() {
        return realName;
    }

    public void setEmplAccount(String emplAccount) {
        this.emplAccount = emplAccount;
    }

    public String getEmplAccount() {
        return emplAccount;
    }

    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }

    public String getHeadImg() {
        return headImg;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getEnName() {
        return enName;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getUserRole() {
        return userRole;
    }

//    public void setDeptInfo(DeptInfo deptInfo) {
//        this.deptInfo = deptInfo;
//    }
//
//    public DeptInfo getDeptInfo() {
//        return deptInfo;
//    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

}