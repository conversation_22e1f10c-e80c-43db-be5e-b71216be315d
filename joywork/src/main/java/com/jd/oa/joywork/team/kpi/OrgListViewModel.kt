package com.jd.oa.joywork.team.kpi

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.jd.oa.joywork.bean.JoyWorkUser

class OrgListViewModel : ViewModel() {
    private val _subLiveData = MutableLiveData<MutableList<JoyWorkUser>>()
    val subLiveData: LiveData<MutableList<JoyWorkUser>> = _subLiveData

    fun appendSub(list: List<JoyWorkUser>) {
        val result = mutableListOf<JoyWorkUser>()
        result.addAll(list)
        _subLiveData.value = result
    }

    private val _subRefreshLiveData = MutableLiveData<MutableList<JoyWorkUser>>()
    val subRefreshLiveData: LiveData<MutableList<JoyWorkUser>> = _subRefreshLiveData

    fun replaceSub(list: List<JoyWorkUser>) {
        val result = mutableListOf<JoyWorkUser>()
        result.addAll(list)
        _subRefreshLiveData.value = result
    }



    private val _supLiveData = MutableLiveData<MutableList<JoyWorkUser>>()
    val supLiveData: LiveData<MutableList<JoyWorkUser>> = _supLiveData

    fun appendSup(list: List<JoyWorkUser>) {
        val result = mutableListOf<JoyWorkUser>()
        result.addAll(list)
        _supLiveData.value = result
    }

    private val _followedLiveData = MutableLiveData<MutableList<JoyWorkUser>>()
    val followedLiveData: LiveData<MutableList<JoyWorkUser>> = _followedLiveData

    fun appendFollowed(list: List<JoyWorkUser>) {
        val result = mutableListOf<JoyWorkUser>()
        result.addAll(list)
        _followedLiveData.value = result
    }

    private val _followedRefreshLiveData = MutableLiveData<MutableList<JoyWorkUser>>()
    val followedRefreshLiveData: LiveData<MutableList<JoyWorkUser>> = _followedRefreshLiveData

    fun replaceFollowed(list: List<JoyWorkUser>) {
        val result = mutableListOf<JoyWorkUser>()
        result.addAll(list)
        _followedRefreshLiveData.value = result
    }
}