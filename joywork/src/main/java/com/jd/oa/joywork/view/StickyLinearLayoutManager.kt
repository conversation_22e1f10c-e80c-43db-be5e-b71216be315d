package com.jd.oa.joywork.view

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork2.list.JoyWork2GroperAdapter
import kotlin.math.abs

interface StickyHeader

typealias HeaderContainerCreator = () -> ViewGroup?

class StickyLinearLayoutManager(
    private val recyclerView: RecyclerView,
    private val headContainerCreator: HeaderContainerCreator? = null
) :
    LinearLayoutManager(recyclerView.context) {

    private var mHeaderHandler: StickyHeaderHelper? = null
    private val mHeaderPositions: MutableList<Int> = ArrayList()
    private var headerElevation: Int = StickyHeaderHelper.NO_ELEVATION

    fun elevateHeaders(dpElevation: Int) {
        this.headerElevation = if (dpElevation > 0) dpElevation else StickyHeaderHelper.NO_ELEVATION
        if (mHeaderHandler != null) {
            mHeaderHandler?.setElevateHeaders(headerElevation)
        }
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
        super.onLayoutChildren(recycler, state)
        cacheHeaderPositions()
        if (mHeaderHandler != null) {
            resetHeaderHandler()
        }
    }

    override fun scrollToPosition(position: Int) {
        super.scrollToPositionWithOffset(position, 0)
    }

    override fun scrollVerticallyBy(
        dy: Int, recycler: RecyclerView.Recycler?, state: RecyclerView.State?
    ): Int {
        val scroll: Int = super.scrollVerticallyBy(dy, recycler, state)
        if (abs(scroll.toDouble()) > 0) {
            mHeaderHandler?.updateHeaderState(
                findFirstVisibleItemPosition(),
                getVisibleHeaders(),
                findFirstCompletelyVisibleItemPosition() == 0
            )
        }
        return scroll
    }

    override fun scrollHorizontallyBy(
        dx: Int, recycler: RecyclerView.Recycler?, state: RecyclerView.State?
    ): Int {
        val scroll: Int = super.scrollHorizontallyBy(dx, recycler, state)
        if (abs(scroll.toDouble()) > 0) {
            mHeaderHandler?.updateHeaderState(
                findFirstVisibleItemPosition(),
                getVisibleHeaders(),
                findFirstCompletelyVisibleItemPosition() == 0
            )
        }
        return scroll
    }

    override fun removeAndRecycleAllViews(recycler: RecyclerView.Recycler) {
        super.removeAndRecycleAllViews(recycler)
        mHeaderHandler?.clearHeader()
    }

    override fun onAttachedToWindow(view: RecyclerView?) {
//        mHeaderHandler = StickyHeaderHelper(view!!, headContainerCreator).also {
//            it.setElevateHeaders(headerElevation)
//            if (mHeaderPositions.isNotEmpty()) {
//                it.setHeaderPositions(mHeaderPositions)
//                resetHeaderHandler()
//            }
//        }
        super.onAttachedToWindow(view)
    }

    override fun onDetachedFromWindow(view: RecyclerView?, recycler: RecyclerView.Recycler?) {
        mHeaderHandler?.clearVisibilityObserver()
        super.onDetachedFromWindow(view, recycler)
    }

    private fun resetHeaderHandler() {
        mHeaderHandler?.reset(orientation)
        mHeaderHandler?.updateHeaderState(
            findFirstVisibleItemPosition(),
            getVisibleHeaders(),
            findFirstCompletelyVisibleItemPosition() == 0
        )
    }

    private fun getVisibleHeaders(): Map<Int, View> {
        val visibleHeaders: MutableMap<Int, View> = LinkedHashMap()
        for (i in 0 until childCount) {
            val view: View = getChildAt(i) ?: continue
            val dataPosition: Int = getPosition(view)
            if (mHeaderPositions.contains(dataPosition)) {
                visibleHeaders[dataPosition] = view
            }
        }
        return visibleHeaders
    }

    private fun cacheHeaderPositions() {
        mHeaderPositions.clear()
        val adapter = recyclerView.realAdapter() as? JoyWork2GroperAdapter ?: return
        val adapterData: List<Any> = adapter.getData()
        for (i in adapterData.indices) {
            if (adapterData[i] is StickyHeader) {
                mHeaderPositions.add(i)
            }
        }
        mHeaderHandler?.setHeaderPositions(mHeaderPositions)
    }
}