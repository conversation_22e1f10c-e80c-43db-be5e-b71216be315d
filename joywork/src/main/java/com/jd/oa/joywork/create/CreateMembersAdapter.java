package com.jd.oa.joywork.create;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.joywork.R;
import com.jd.oa.joywork.databinding.TaskMemberItemBinding;
import com.jd.oa.model.service.im.dd.entity.Members;

import java.util.ArrayList;
import java.util.List;

class CreateMembersAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final int maxCount;
    private final ArrayList<Members> members = new ArrayList<>();

    CreateMembersAdapter(int maxCount) {
        this.maxCount = maxCount;
    }

    void updateAll(List<Members> newList) {
        members.clear();
        members.addAll(newList);
        notifyDataSetChanged();
    }

    @Override
    @NonNull
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_IMAGE) {
            TaskMemberItemBinding binding = DataBindingUtil
                    .inflate(LayoutInflater.from(parent.getContext()), R.layout.task_member_item,
                            parent, false);
            return new MembersViewHolder(binding);
        } else {
            return new MembersTextViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.task_member_item_text,
                    parent, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder h, int position) {
        if (h instanceof MembersViewHolder) {
            MembersViewHolder holder = (MembersViewHolder) h;
            Members m = members.get(position);
            holder.binding.setMember(m);
            holder.itemView.setEnabled(false);
            holder.itemView.setClickable(false);
            holder.binding.executePendingBindings();
//            holder.itemView.setTag(m);
//            holder.itemView.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    Members m = (Members) v.getTag();
//                    if (ObjExKt.isLegalString(m.getEmplAccount())) {
//                        AppBase.iAppBase.showContactDetailInfo(v.getContext(), m.getEmplAccount());
//                    }
//                }
//            });
        }
    }

    static class MembersViewHolder extends RecyclerView.ViewHolder {

        final TaskMemberItemBinding binding;

        MembersViewHolder(TaskMemberItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    static class MembersTextViewHolder extends RecyclerView.ViewHolder {

        MembersTextViewHolder(View view) {
            super(view);
        }
    }

    private static final int TYPE_IMAGE = 1;
    private static final int TYPE_COUNT = 2;

    @Override
    public int getItemViewType(int position) {
        if (position < maxCount) {
            return TYPE_IMAGE;
        } else {
            return TYPE_COUNT;
        }
    }

    @Override
    public int getItemCount() {
        int count = members.size();
        if (count <= maxCount) {
            return count;
        } else {
            return maxCount + 1;
        }
    }
}
