package com.jd.oa.joywork.detail.ui;

import com.jd.me.web2.webview.JMEWebview;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsTargetInterface {

    // 网页通知原生目标保存成功
    public static final String B_GOAL_SUCCESS = "JSSKD_EVENT_HANDLE_GOAL";
    // 原生通知网页保存目标
    public static final String B_GOAL_SAVE = "NATIVE_EVENT_SAVE_GOAL";

    public static void sendToWeb(JMEWebview webview, String event) {
        if (webview == null)
            return;
        webview.callHandler(event, null, null);
    }

    public static final String GO_BACK = "NATIVE_EVENT_GO_BACK";

    // 网页通知原生隐藏/显示 actionBar
    public static final String HANDLE_TITLE = "JSSDK_EVENT_HANDLE_TITLEBAR";
    // 原生通知网页刷新目标详情
    public static final String NOTIFY_REFRESH_GOAL = "NATIVE_EVENT_REFRESH_GOAL";
    // 网页请求接口成功后，通过该事件将后台返回值同步至原生
    // 一进入详情页时，或者修改后主动刷新时
    public static final String SYNC_DETAIL = "JSSDK_EVENT_SYNC_GOAL_DETAIL";
    // 跳转进展点赞列表
    public static final String LIKE_LIST = "JSSDK_EVENT_PROGRESS_LIKE_LIST";
    // 网页端修改目标详情后，给原生回调
    public static final String UPDATE_GOAL = "JSSDK_EVENT_UPDATE_GOAL_DETAIL";
    // 选择目录后，网页发送事件
    public static final String SEL_DIR = "JS_SDK_SELECT_DIRECTORY";

    // 原生关闭当前界面
    public static final String CLOSE_PAGE = "JSSDK_EVENT_CLOSE_PAGE";
    // 网页加载完成后给原生的回调
    public static final String JSSDK_EVENT_PAGE_FINISH = "JSSDK_EVENT_PAGE_FINISH";
    // 创建待办
    public static final String CREATE_TASK = "JSSDK_EVENT_GOAL_CREATE_TASK";
    // open task detail
    public static final String OPEN_JOYWORK_DETAIL = "JSSDK_EVENT_OPEN_TASK_DETAIL";
    // 待办详情页解决风险问题
    public static final String TASK_DETAIL_RESOLVE_RISK = "NATIVE_EVENT_TASK_LIST_REFRESH";
    // 待办子任务
    public static final String TASK_GET_CREATE_INFO = "JSSDK_EVENT_GET_CREATE_TASKINFO";
    // 待办创建完成
    public static final String TASK_CREATE_COMPLETE = "JSSDK_EVENT_CREATE_TASK_COMPLETED";
    // 待办详情拉起原生催办
    public static final String TASK_URGE = "JSSDK_EVENT_TASK_URGE";
    // 详情页信息修改
    public static final String DETAIL_PROPERTY_CHANGED = "JSSDK_EVENT_TASK_PROPERTY_CHANGED";
    // 点击待办来源跳转
    public static final String TASK_SOURCE_INFO = "JSSDK_EVENT_OPEN_TASK_SOURCE";
}
