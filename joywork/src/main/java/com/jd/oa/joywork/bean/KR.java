package com.jd.oa.joywork.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class KR extends JoyWork implements Parcelable, Serializable {

    private static final long serialVersionUID = 3283324252412431L;

    public static final KR stub = new KR();

    public String goalId;
    public String content;
    public String krId;
    public Integer condition;
    public List<String> permissions;
    public Integer taskNums;
    public Integer progressNums;
    public Integer commentNums;

    public KR() {

    }

    public List<String> safePermissions() {
        return permissions == null ? new ArrayList<String>() : permissions;
    }

    protected KR(Parcel in) {
        goalId = in.readString();
        content = in.readString();
        krId = in.readString();
        if (in.readByte() == 0) {
            condition = null;
        } else {
            condition = in.readInt();
        }
        permissions = in.createStringArrayList();
    }

    public static final Creator<KR> CREATOR = new Creator<KR>() {
        @Override
        public KR createFromParcel(Parcel in) {
            return new KR(in);
        }

        @Override
        public KR[] newArray(int size) {
            return new KR[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(goalId);
        dest.writeString(content);
        dest.writeString(krId);
        if (condition == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(condition);
        }
        dest.writeStringList(permissions);
    }
}
