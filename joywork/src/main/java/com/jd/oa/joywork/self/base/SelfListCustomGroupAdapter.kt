package com.jd.oa.joywork.self.base

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkTitle
import com.jd.oa.joywork.bean.LoadMoreJoyWork
import com.jd.oa.joywork.expandable.ExpandableGroup
import com.jd.oa.joywork.move
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.team.TeamLoadMoreVH
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.view.HRecyclerView
import com.jd.oa.utils.ToastUtils

class SelfListCustomGroupAdapter(
    fragment: Fragment,
    itf: SelfListBaseAdapterItf,
    groups: ArrayList<ExpandableGroup<JoyWorkTitle, JoyWork>>,
    recyclerView: HRecyclerView?,
    context: FragmentActivity,
    private val projectId: String,
) : SelfListBaseAdapter(fragment, itf, groups, recyclerView, context) {
    var swaped = false

    fun swap(
        fromVH: RecyclerView.ViewHolder,
        toVH: RecyclerView.ViewHolder,
        delta: Boolean
    ) {
        if (fromVH.adapterPosition == toVH.adapterPosition)
            return

        if (fromVH.adapterPosition > toVH.adapterPosition && toVH is SelfListGroupTitleVH) {
            // 上滑。只有 LoadMoreVH + GroupVH 情况时才不可交换
            if (toVH.adapterPosition == 0) {
                return
            }
            val item = processor.getData()[toVH.adapterPosition - 1]
            if (item is LoadMoreJoyWork)
                return
        }
        swaped = true
        Log.e("TAG", "from = ${fromVH.adapterPosition},to = ${toVH.adapterPosition}")

        val fromItem = fromVH.itemView.getTag(R.id.jdme_tag_id) as JoyWork

        if (toVH is SelfListItemVH || toVH is TeamLoadMoreVH) { // 两个 item 交换
            val toItem = toVH.itemView.getTag(R.id.jdme_tag_id) as JoyWork

            var toIndex = toItem.expandableGroup.realItems.indexOf(toItem)
            val fromIndex = fromItem.expandableGroup.realItems.indexOf(fromItem)
            if (fromItem.expandableGroup == toItem.expandableGroup) {// 同组
                Log.e("TAG", "00 =$fromIndex, $toIndex")
                fromItem.expandableGroup.realItems.move(fromIndex, toIndex)
                Log.e(
                    "TAG",
                    "00 =${fromItem.expandableGroup.realItems.indexOf(fromItem)}, ${
                        toItem.expandableGroup.realItems.indexOf(toItem)
                    }"
                )
//                Collections.swap(fromItem.expandableGroup.realItems, fromIndex, toIndex)
            } else {
                if (delta) toIndex++
                Log.e("TAG", "01 = $fromIndex, $toIndex")
                fromItem.expandableGroup.realItems.remove(fromItem)
                fromItem.expandableGroup.title.count--
                toItem.expandableGroup.realItems.add(toIndex, fromItem)
                toItem.expandableGroup.title.count++
            }
            toItem.expandableGroup.notifyItemChange()
        } else { // 分组标题。要么拖入，要么拖出
            val toItem = toVH.itemView.getTag(R.id.jdme_tag_id) as JoyWorkTitle

            // 从旧组中删除
            fromItem.expandableGroup.realItems.remove(fromItem)
            fromItem.expandableGroup.clearTmp()
            fromItem.expandableGroup.title.count--

            if (fromItem.expandableGroup.id == toItem.expandableGroup.id) {
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                // 从组内拖到标题，理论上应该只是拖出。但鬼知道会不会有考虑不到的情况，弄个 if 判断吧
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，要拖出当前分组，添加到上一个分组
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    Log.e("TAG", "11 = 0")
                    if (toGroup.expand) {
                        toGroup.realItems.add(fromItem)
                    } else {
                        toGroup.realItems.add(0, fromItem)
                    }
                    toGroup.title.count++
                } else {
                    Log.e("TAG", "11 = 1")
                    // 理论上这里执行不到
                    // 往下拖，拖入当前分组
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    toGroup.realItems.add(0, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            } else {// 不同组，理论上应该是拖入
                val toGroup: ExpandableGroup<JoyWorkTitle, JoyWork>
                if (fromVH.adapterPosition > toVH.adapterPosition) {
                    // 往上拖，拖出。理论上这里执行不到
                    toGroup = groups[groups.indexOf(toItem.expandableGroup) - 1]
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    Log.e(
                        "TAG",
                        "2 fromIndex = ${toGroup.realItems.size}, group = ${toGroup.title.title}"
                    )
                    toGroup.realItems.add(toGroup.realItems.size, fromItem)
                    toGroup.title.count++
                } else {// 正常的拖入
                    toGroup = toItem.expandableGroup as ExpandableGroup<JoyWorkTitle, JoyWork>
                    if (!toGroup.expand) {
                        toGroup.saveTmp(fromItem)
                    }
                    var insertIndex = 0
                    val r = kotlin.runCatching {
                        insertIndex = processor.getData().indexOf(toItem) - processor.getData()
                            .indexOf(toGroup.title)
                    }
                    r.exceptionOrNull()?.printStackTrace()
                    Log.e("TAG", "2 down = $insertIndex")
                    toGroup.realItems.add(insertIndex, fromItem)
                    toGroup.title.count++
                }
                fromItem.expandableGroup = toGroup
                toGroup.notifyItemChange()
            }
        }
        processor.refreshData()

        notifyItemMoved(fromVH.adapterPosition, toVH.adapterPosition)
    }

    fun sortJoyWork2(joyWork: JoyWork) {
        val param = processor.getLocationInfo(joyWork)
        JoyWorkRepo.sortProjectJoyWork(
            joyWork.taskId,
            param.blockType,
            projectId,
            param,
            object : JoyWorkUpdateCallback {
                override fun onStart() {
                }

                override fun result(success: Boolean, errorMsg: String) {
//                    PromptUtils.removeLoadDialog(context)
                    if (success) {
//                        ProjectConstant.sendBroadcast(context, ProjectConstant.JOYWORK_SORT)
                        // 刷新整个列表。里面可能涉及到多个地方的数据修改。比如时间头的移除/添加，组中数量的修改等
                        processor.filterAndSyncData(context)
                        notifyDataSetChanged()
                        initGroupIfNecessary(joyWork)
                    } else {
                        // 恢复原样
                        restore(joyWork)
                        notifyDataSetChanged()
                        ToastUtils.showInfoToast(errorMsg)
                    }
                }
            })
    }

    private fun initGroupIfNecessary(joyWork: JoyWork) {
        (joyWork.expandableGroup.title.extra as? Group)?.apply {
            if (isInit) {
                expandGroup(joyWork.expandableGroup.title)
            } else {
                toggleGroup(title = joyWork.expandableGroup.title)
            }
        }
    }
}