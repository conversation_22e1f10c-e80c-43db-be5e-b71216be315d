package com.jd.oa.joywork.detail.ui

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.KR
import com.jd.oa.joywork.bean.KpiTarget
import com.jd.oa.joywork.create.*
import com.jd.oa.joywork.detail.data.db.UpdateReporter.reportUpdate
import com.jd.oa.joywork.detail.data.entity.JoyWorkDetail
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.viewmodel.TaskDetailPresenter
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork.view.JoyWorkAvatarViewCallback
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.string

/**
 * 详情页使用的截止时间
 */
open class DetailDeadlineCreateFactory(val fragment: TaskDetailFragment) : DeadlineCreateFactory() {

    override fun timeShouldHighlight(): Boolean {
        return true
    }

    override fun timeContainerClick(): View.OnClickListener {
        return View.OnClickListener {
            fragment.selectTime()
        }
    }

    override fun clearClick() = View.OnClickListener {
        fragment.cancelDeadlineTime()
    }

    override fun afterSetTime() {
        fragment.updateDeadline(mValue);
    }

    override fun todayClickable(): Boolean {
        return fragment.canUpdateDeadline()
    }

    override fun tomorrowClickable(): Boolean {
        return fragment.canUpdateDeadline()
    }

    override fun timeClickable(): Boolean {
        return fragment.canUpdateDeadline()
    }

    override fun clearClickable(): Boolean {
        return fragment.canUpdateDeadline()
    }
}

class DetailLaunchCreateFactory(val fragment: TaskDetailFragment) : LaunchCreateFactory() {
    override fun clearClick() = View.OnClickListener {
        fragment.mItemFactories.removeByClass(DetailLaunchCreateFactory::class.java)
        fragment.cancelLaunchTime()
    }
}

class DetailOrderCreateFactory(val fragment: TaskDetailFragment) : OrderCreateFactory() {
    override fun canUpdate(): Boolean {
        return fragment.canUpdaterOrder()
    }

    override fun addClick(): View.OnClickListener {
        return View.OnClickListener {
            TaskDetailPresenter.selectGroup(
                fragment.activity,
                fragment.model.taskDetail.value
            )
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_RELATED_CHECKLIST
                )
            }
        }
    }

    override fun afterProjectDel(project: JoyWorkDetail.Project) {
        fragment.model.taskDeleteProject(project)
    }

    override fun afterChangeGroup(project: JoyWorkDetail.Project) {
        fragment.model.taskChangeProject(project)
    }
}

class DetailSysOrderCreateFactory(val fragment: TaskDetailFragment) : SysOrderCreateFactory() {
    override fun canUpdate(): Boolean {
        return false
    }
}

class DetailTargetCreateFactory(val fragment: TaskDetailFragment) : TargetCreateFactory() {
    override fun canUpdate(): Boolean {
        return fragment.canUpdaterTarget()
    }

    override fun addClick(): View.OnClickListener {
        return View.OnClickListener {
            TaskDetailPresenter.selectTarget(
                fragment.activity,
                fragment.model.taskDetail.value
            )
        }
    }

    override fun afterTargetDel(target: KpiTarget) {
        fragment.model.taskDeleteTarget(target) {
            reportUpdate()
            fragment.model.updateTaskDetailFromWeb()
        }
    }

    override fun afterKrDel(kr: KR) {
        fragment.model.taskDeleteKr(kr) {
            reportUpdate()
            fragment.model.updateTaskDetailFromWeb()
        }
    }
}

class DetailRelationFactory(val fragment: TaskDetailFragment) : RelationCreateFactory() {
    override fun addClick(): View.OnClickListener {
        return View.OnClickListener {
            JDMAUtils.onEventClick(
                JoyWorkConstant.DETAIL_RELATION_ADD,
                JoyWorkConstant.DETAIL_RELATION_ADD
            )
            if (!fragment.canUpdateRelation()) {
                return@OnClickListener
            }
            selectContact(it.context, true)
        }
    }

    override fun containerClick(): View.OnClickListener {
        return View.OnClickListener {
            if (fragment.mValue.members.isLegalList()) {
                fragment.toRelationList()
            } else {
                if (!fragment.canUpdateRelation()) {
                    return@OnClickListener
                }
                selectContact(it.context, false)
            }
        }
    }

    override fun afterSelect(ms: ArrayList<Members>, isAdd: Boolean) {
        if (isAdd) {
            fragment.toRelationList(ms)
        } else {
            fragment.changeRelation(ms)
        }
    }

    override fun addVisible(): Boolean {
        return fragment.canUpdateRelation()
    }
}

class DetailPriorityCreateFactory() : PriorityCreateFactory() {
    override fun itemClickable(): Boolean {
        return false
    }
}

class DetailAlarmFactory(val fragment: TaskDetailFragment) : AlarmCreateFactory() {
    override fun itemClick(): View.OnClickListener {
        return View.OnClickListener {
            fragment.updateAlarm();
        }
    }

    override fun clearClick(): View.OnClickListener {
        return View.OnClickListener {
            fragment.cancelAlarm()
        }
    }
}

class DetailOwnerFactory(val fragment: TaskDetailFragment) : OwnerCreateFactory() {
    override fun addClick(): View.OnClickListener {
        return View.OnClickListener {
            JDMAUtils.onEventClick(
                JoyWorkConstant.DETAIL_OWNER,
                JoyWorkConstant.DETAIL_OWNER
            )
            if (!fragment.canUpdateOwner()) {
                return@OnClickListener
            }
            fragment.addOwner()
        }
    }

    override fun itemClick(): View.OnClickListener {
        return View.OnClickListener {
            if (fragment.mValue.normalOwners.isLegalList()) {
                fragment.toOwnerList()
            } else {
                if (!fragment.canUpdateOwner()) {
                    return@OnClickListener
                }
                fragment.addOwner()
            }
        }
    }

    override fun addVisible(): Boolean {
        return fragment.canTransfer()
    }

    override fun transferVisible(): Boolean {
        return fragment.canTransfer()
    }

    override fun transferClick(): View.OnClickListener {
        return View.OnClickListener {
            fragment.transferOwner()
            clickEvent {
                ClickEventParam(
                    eventId = JoyWorkConstant.MOBILE_EVENT_TASK_TASK_DETAILS_ASSIGNEES_TASK_TRANSFER_BUTTON
                )
            }
        }
    }

    override fun getAvatarCallback(context: Context): JoyWorkAvatarViewCallback {
        return object : JoyWorkAvatarViewCallback() {
            override fun getHintText2(
                urls: List<String>,
                context: Context,
                type: Int
            ): Pair<String?, String?>? {
                return if (fragment.mValue.normalOwners.size == 1) {
                    val user = fragment.mValue.normalOwners.first()
                    if (user.chief.isChief()) {
                        null
                    } else {
                        val name = fragment.mValue.normalOwners.firstOrNull()?.realName ?: ""
                        Pair(
                            name,
                            context.resources.getString(R.string.joywork_create_owner2)
                        )
                    }
                } else {
                    if (type == JoyWorkAvatarView.TYPE_LONG) {
                        Pair(
                            null, fragment.getString(
                                R.string.joywork_detail_owners,
                                "${fragment.countFinishOwner()}/${urls.size}"
                            )
                        )
                    } else {
                        Pair(
                            null, fragment.getString(
                                R.string.joywork_detail_owners_short,
                                "${fragment.countFinishOwner()}/${urls.size}"
                            )
                        )
                    }
                }

            }

            override fun getImageAltText(
                urls: List<String>,
                context: Context,
                position: Int,
                type: Int
            ): Pair<String?, String?>? {
                val name = fragment.mValue?.normalOwners?.firstOrNull()?.realName ?: return null
                return Pair(name, context.string(R.string.joywork_owner2))
            }

            override fun needHat(
                url: String,
                position: Int,
                itemView: ImageView,
                parent: ViewGroup
            ): Boolean {
                return position == 0 && fragment.mValue.normalOwners.firstOrNull()?.chief.isChief()
            }
        }
    }
}


/**
 * 详情页重复行
 */
class DetailDupCreateFactory : DuplicateCreateFactory() {
    override fun itemClickable(): Boolean {
        return false
    }
}

class DetailMoreFactory : CreateItemFactory() {
    override fun onBuildView(parent: ViewGroup, layoutInflater: LayoutInflater): View {
        return layoutInflater.inflate(R.layout.jdme_joywork_create_fragment_more, parent, false)
    }

    override fun getEnum(): JoyWorkActionEnum {
        return JoyWorkActionEnum.MORE
    }

}
