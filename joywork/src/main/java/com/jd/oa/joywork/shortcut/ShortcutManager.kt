package com.jd.oa.joywork.shortcut

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import com.jd.me.datetime.picker.DatePickerView
import com.jd.me.datetime.picker.DatetimePickerDialog
import com.jd.me.datetime.picker.SelectDatetime
import com.jd.me.datetime.picker.SwitchView
import com.jd.oa.AppBase
import com.jd.oa.im.listener.Callback
import com.jd.oa.joywork.AlertType
import com.jd.oa.joywork.DuplicateEnum
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkEx
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TimeSelectTransfer
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.create.Value
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.fromDD
import com.jd.oa.joywork.detail.fromJoyWorkUser
import com.jd.oa.joywork.executor.ExecutorListActivity
import com.jd.oa.joywork.executor.ExecutorUtils
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.team.dialog.DialogSupporter
import com.jd.oa.joywork2.input.AtInit
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.timezone.holidayFetcher
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.ToastUtils
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.Objects

/**
 * 各种 dialog 会来回出现。此处统一处理
 */
class ShortcutManager(
    private val shortcutDialog: AppCompatDialog?,
    private val context: Context? = null
) {

    private lateinit var mAtInit: AtInit

    fun initAt(et: EditText, atRunnable: (AtInit) -> Unit) {
        if (!::mAtInit.isInitialized) {
            mAtInit = AtInit(et, atRunnable)
        }
    }

    /**
     * 选择开始、截止时间
     */
    fun selectTime(draft: Value, finishCallback: (isCancelled: Boolean) -> Unit) {
        val ctx = ((shortcutDialog?.context ?: context) as? Activity) ?: AppBase.getTopActivity()
        ctx?.apply {
            draft.isClean = false
            shortcutDialog?.dismiss()
            val datetimePickerDialog = DatetimePickerDialog(this)
            datetimePickerDialog.setTransfer(TimeSelectTransfer)
            datetimePickerDialog.setSelectMode(DatePickerView.SelectMode.RANGE)
            val onCancelListener = DialogInterface.OnCancelListener {
                shortcutDialog?.show()
                finishCallback(true)
            }

            datetimePickerDialog.setSelectSide(
                if (draft.type == Value.TYPE_DEADLINE)
                    SwitchView.Side.RIGHT
                else
                    SwitchView.Side.LEFT
            )

            datetimePickerDialog.setOnCancelListener(onCancelListener)
            datetimePickerDialog.setOnConfirmListener { result ->
                result?.apply {
                    shortcutDialog?.show()
                    draft.startTime = start?.run {
                        getTime(start, true)
                    }
                    draft.endTime = end?.run {
                        getTime(end, false)
                    }
                    JoyWorkShortcutDraft.updateDraftStartTime(draft.startTime)
                    JoyWorkShortcutDraft.updateDraftEndTime(draft.endTime)
                    finishCallback(false)
                } ?: onCancelListener.onCancel(datetimePickerDialog)
            }
            // 开始日期。如果已有，就传已有值，否则不传
            draft.startTime?.apply {
                val start = Date(this)
                datetimePickerDialog.setStartDate(start)
                datetimePickerDialog.setStartTime(start)
            }

            // 截止日期。如果已选，则传已选值，否则传明天
            val endMill = draft.endTime ?: (System.currentTimeMillis() + 24 * 60 * 60 * 1000L)
            datetimePickerDialog.setEndDate(Date(endMill))

            SimpleDateFormat("hh:mm", Locale.getDefault()).apply {
                // 截止时间。如果已选，则传已选值，否则传 18 点
                val endTime = if (draft.endTime == null) parse("16:00") else Date(endMill)
                datetimePickerDialog.setEndTime(endTime)
            }
            addExtraView(datetimePickerDialog, draft)
            datetimePickerDialog.setResultChecker { result ->
                val startEmpty = result.start.date == null
                val endEmpty = result.end.date == null
                if (!startEmpty && endEmpty) {
                    ToastUtils.showToast(R.string.joywork2__choose_end_date_tips)
                    return@setResultChecker false
                }
                // 截止时间为空时，提醒和重复不能被设置
                if (endEmpty) {
                    draft.mAlertType.clear()
                    draft.duplicateEnum = DuplicateEnum.NO
                }
                return@setResultChecker true
            }
            datetimePickerDialog.holidayFetcher()
            datetimePickerDialog.setDefaultEndTimeInHour(16)
            datetimePickerDialog.clearStartWhenEndCleared(true)
            datetimePickerDialog.show()
        }
    }

    private fun addExtraView(
        dialog: DatetimePickerDialog,
        draft: Value
    ) {
        val extraView = LayoutInflater.from(dialog.context)
            .inflate(R.layout.joywork_datetime_picker_extra, null, false)

        val alertContainer = extraView.findViewById<ViewGroup>(R.id.mAlertContainer)
        val alertClickListener = AlertClickListener(dialog, alertContainer, draft).init()

        val dupContainer = extraView.findViewById<ViewGroup>(R.id.mDupContainer)
        val dupClickListener = DupClickListener(dialog, dupContainer, draft).init()
        dialog.extraView = extraView
        dialog.setOnDateClearListener { side ->
            if (side == SwitchView.Side.RIGHT) {
                draft.mAlertType.clear()
                draft.mAlertType.add(AlertType.NO)
                alertClickListener.handleView()
                draft.duplicateEnum = DuplicateEnum.NO
                dupClickListener.handleView()
            }
        }
    }

    private fun getTime(result: SelectDatetime, start: Boolean): Long? {
        if (result.date == null) { // 没有日期，时间是无效的，所以不需要处理
            return null
        }
        val c = Calendar.getInstance()
        c.set(Calendar.MILLISECOND, 0)
        c.set(Calendar.SECOND, 0)

        val tmp = Calendar.getInstance()
        tmp.timeInMillis = result.date.time

        c.set(Calendar.YEAR, tmp.get(Calendar.YEAR))
        c.set(Calendar.MONTH, tmp.get(Calendar.MONTH))
        c.set(Calendar.DAY_OF_MONTH, tmp.get(Calendar.DAY_OF_MONTH))


        var minute = 0
        var hour = if (start) 9 else 18
        if (result.time != null) {
            val time = Calendar.getInstance()
            time.timeInMillis = result.time.time
            hour = time.get(Calendar.HOUR_OF_DAY)
            minute = time.get(Calendar.MINUTE)
        }
        c.set(Calendar.HOUR_OF_DAY, hour)
        c.set(Calendar.MINUTE, minute)
        return c.timeInMillis
    }

    /**
     * 选择负责人
     */
    fun selectContact(
        context: Context,
        draft: Value,
        sessionId: String? = null,
        chatType: String,
        finishCallback: (success: Boolean) -> Unit
    ) {
        ((context as? Activity) ?: AppBase.getTopActivity())?.apply {
            shortcutDialog?.dismiss()

            // 不是群聊，将 sessionId 置空
            val id = if (Objects.equals(chatType, ChatType.GROUP.value)) {
                sessionId
            } else {
                ""
            }
            if (draft.normalOwners.isLegalList()) {
                val i = Intent(this, ExecutorListActivity::class.java)
                val ms = draft.normalOwners.map {
                    val m = Members()
                    m.fromJoyWorkUser(it)
                    m
                }
                ExecutorListActivity.inflateIntent(i, ms, id) {
                    shortcutDialog?.show()
                    draft.setNormalOwners(it)
                    finishCallback(true)
                }
                startActivity(i)
            } else {
                val onFail = {
                    shortcutDialog?.show()
                    finishCallback(false)
                }
                ExecutorUtils.selectExecutors(this, ArrayList(), id, true, { bean ->
                    shortcutDialog?.show()
                    bean?.apply {
                        if (isNotEmpty()) {
                            val us = ArrayList<JoyWorkUser>()
                            bean.forEach { entityJd ->
                                if (entityJd != null) {
                                    val u = JoyWorkUser()
                                    u.fromDD(entityJd)
                                    us.add(u)
                                }
                            }
                            draft.setNormalOwners(us)
                            finishCallback(true)
                        } else {
                            onFail()
                        }
                    } ?: onFail()
                }, onFail)
            }
        }
    }

    /**
     * 选择负责人
     */
    fun selectContact(
        context: Context,
        max: Int,
        finishCallback: (success: Boolean, us: ArrayList<JoyWorkUser>?) -> Unit
    ) {
        ((context as? Activity) ?: AppBase.getTopActivity())?.apply {
            shortcutDialog?.dismiss()
            val imDdService = AppJoint.service(ImDdService::class.java)
            val entity = JoyWorkEx.selectContactEntity(maxNum = max)
            imDdService.gotoMemberList(
                this,
                1,
                entity,
                object : Callback<ArrayList<MemberEntityJd>> {
                    override fun onSuccess(bean: ArrayList<MemberEntityJd>?) {
                        shortcutDialog?.show()
                        bean?.apply {
                            if (isNotEmpty()) {
                                val list = ArrayList<JoyWorkUser>()
                                bean.forEach { entityJd ->
                                    val user = JoyWorkUser().apply {
                                        app = entityJd.mApp
                                        ddAppId = entityJd.mApp
                                        headImg = entityJd.mAvatar
                                        emplAccount = entityJd.mId
                                        realName = entityJd.mName
                                    }
                                    list.add(user)
                                }
                                finishCallback(true, list)
                            } else {
                                onFail()
                            }
                        } ?: onFail()
                    }

                    override fun onFail() {
                        shortcutDialog?.show()
                        finishCallback(false, null)
//                    ToastUtils.showInfoToast(R.string.joywork_select_contact)
                    }
                })
        }
    }

    // 关闭键盘
    fun closeKeyboard(et: EditText, activity: Context) {
        val imm = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(et.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        et.clearFocus()
    }

    // 打开键盘
    fun openKeyboard(et: EditText, ctx: Context) {
        et.isFocusable = true
        et.isFocusableInTouchMode = true
        et.requestFocus()
        val imm = ctx.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.toggleSoftInput(
            0,
            InputMethodManager.HIDE_NOT_ALWAYS
        )
    }

    class DupClickListener(
        private val dialog: DatetimePickerDialog,
        private val parent: ViewGroup,
        private val value: Value
    ) : View.OnClickListener {
        private val mDupText = parent.findViewById<TextView>(R.id.mDupText)

        fun init(): DupClickListener {
            parent.setOnClickListener(this)
            handleView()
            return this;
        }

        override fun onClick(v: View) {
            DialogSupporter(null, v.context).listDup(value.duplicateEnum ?: DuplicateEnum.NO) {
                if (it != null) {
                    value.duplicateEnum = it
                    handleView()
                }
            }

        }

        fun handleView() {
            mDupText.setText((value.duplicateEnum ?: DuplicateEnum.NO).resId)
        }
    }

    class AlertClickListener(
        private val dialog: DatetimePickerDialog,
        private val parent: ViewGroup,
        private val value: Value
    ) : View.OnClickListener {
        private val mAlertText = parent.findViewById<TextView>(R.id.mAlertText)

        fun init(): AlertClickListener {
            parent.setOnClickListener(this)
            handleView()
            return this
        }

        override fun onClick(v: View) {
            DialogSupporter(null, v.context).listAlerts(value.mAlertType, true) {
                if (it != null) {
                    JDMAUtils.clickEvent("", JoyWorkConstant.TIME_DIALOG_SET_REMINDER, null)
                    value.replaceAlertType(it)
                    handleView()
                }
            }
        }

        fun handleView() {
            val text = AlertType.toString(value.mAlertType, parent.context)
            mAlertText.text = text
        }
    }
}