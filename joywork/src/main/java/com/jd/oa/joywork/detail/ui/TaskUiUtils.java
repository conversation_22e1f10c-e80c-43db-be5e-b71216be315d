package com.jd.oa.joywork.detail.ui;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.os.LocaleList;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.jd.oa.AppBase;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.joywork.JoyWorkEx;
import com.jd.oa.joywork.R;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;
import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_SHARE;

public class TaskUiUtils {
    private static final int REQUEST_CODE_SELECT = 1;

    public static void selectContacts(Activity activity, int max, Callback<ArrayList<MemberEntityJd>> callback) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        MemberListEntityJd entityJd = JoyWorkEx.INSTANCE.selectContactEntity(max, true);
        imDdService.gotoMemberList(activity, REQUEST_CODE_SELECT, entityJd, callback);
    }

    public static void selectContactsWithErps(Activity activity, int max, ArrayList<MemberEntityJd> selected, Callback<ArrayList<MemberEntityJd>> callback) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        MemberListEntityJd entityJd = JoyWorkEx.INSTANCE.selectContactEntity(max, true);
        entityJd.setConstantFilter(selected);
        entityJd.setShowConstantFilter(true).setConstantFilter(selected).setOptionalFilter(null).setShowOptionalFilter(false);
        imDdService.gotoMemberList(activity, REQUEST_CODE_SELECT, entityJd, callback);
    }

    public static void selectContactsForShare(Activity activity, int max, com.jd.oa.im.listener.Callback<ArrayList<MemberEntityJd>> callback) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);

        List<String> appIds = new ArrayList<>();
        JoyWorkEx.INSTANCE.getAppIds(appIds);
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setMaxNum(max)
                .setSpecifyAppId(appIds)
                .setFrom(TYPE_SHARE)
                .setShowConstantFilter(true)
                .setShowSelf(true);
        imDdService.gotoMemberList(activity, REQUEST_CODE_SELECT, entity, callback);
    }


    public static void selectContacts(Activity activity, int max, Callback<ArrayList<MemberEntityJd>> callback, List<String> erps) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setMaxNum(max);
        entity.setFrom(TYPE_ADD_MEMBER).setShowConstantFilter(true).setShowSelf(true).setSpecifyAppId(erps);
        imDdService.gotoMemberList(activity, REQUEST_CODE_SELECT, entity, callback);
    }

    public static void hideKeyboard() {
        final Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        final InputMethodManager manager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (manager.isActive()) {
            manager.hideSoftInputFromWindow(activity.getWindow().getDecorView().getWindowToken(), 0);
        }
    }

    public static void showKeyboard(View view) {
        final Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.showSoftInput(view, InputMethodManager.SHOW_FORCED);
        inputMethodManager.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
        inputMethodManager.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
    }

    public static boolean isZh() {
        try {
            if (AppBase.getTopActivity() != null) {
                return AppBase.getTopActivity().getResources().getBoolean(R.bool.zh);
            }
            Locale locale;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                locale = LocaleList.getDefault().get(0);
            } else locale = Locale.getDefault();
            return "zh".equals(locale.getLanguage().trim().toLowerCase());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
