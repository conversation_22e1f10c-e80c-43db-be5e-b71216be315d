package com.jd.oa.joywork.team

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.ViewModelProviders
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.joywork.JoyWorkLevel
import com.jd.oa.joywork.ProjectFilter
import com.jd.oa.joywork.ProjectSetting
import com.jd.oa.joywork.ProjectSort
import com.jd.oa.joywork.ProjectSortAction
import com.jd.oa.joywork.R
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.JoyWorkProjectList
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.dialog.thirdparty.CustomAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.CustomConfig
import com.jd.oa.joywork.dialog.thirdparty.ShitAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.ShitAlertDialogConfig
import com.jd.oa.joywork.hideAction
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.isTrue
import com.jd.oa.joywork.self.strategy.JoyWorkMainNormalStrategy
import com.jd.oa.joywork.self.strategy.JoyWorkMainTabStrategy
import com.jd.oa.joywork.shortcut.ShortcutDialogTmpData
import com.jd.oa.joywork.shortcut.SuccessSnackBar
import com.jd.oa.joywork.string
import com.jd.oa.joywork.support.PageSharingDataManager
import com.jd.oa.joywork.support.ProjectSettingPageData
import com.jd.oa.joywork.team.bean.Group
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.joywork.team.bean.ProjectPermissionEnum
import com.jd.oa.joywork.team.bean.ProjectSortEnum
import com.jd.oa.joywork.team.bean.ProjectSortTypeEnum
import com.jd.oa.joywork.team.bean.*
import com.jd.oa.joywork.team.chat.ProjectSelectorDeeplinkUtils
import com.jd.oa.joywork.team.dialog.CreateDialog
import com.jd.oa.utils.DateShowUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.argb
import com.jd.oa.utils.bindClick
import com.jd.oa.utils.gone
import com.jd.oa.utils.string
import com.jd.oa.utils.visible
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Objects
import com.jd.oa.joywork2.backend.JoyWorkSource
import com.jd.oa.utils.*
import kotlinx.coroutines.*
import org.json.JSONObject
import java.util.*

/**
 * Joywork 项目详情页。包括 "待完成、已完成、已删除" 三个 tab，内部使用 ViewPager+Fragment 显示
 * <AUTHOR>
 */
class TeamDetailFragment : BaseFragment(), View.OnClickListener {
    private var mAddView: View? = null
    private var mInitPos = 1

    private lateinit var filterSortItems: ArrayList<SortValue>

    private lateinit var pageData: ProjectSettingPageData

    private val items = mutableListOf<TaskStatusEnum>()
    val clickIds = mutableListOf<String>()
    private val mScope = MainScope()

    private lateinit var fs: List<TeamBaseFragment>

    companion object {
        var mGs: ArrayList<Group>? = null
        var mPs: ArrayList<String>? = null
        fun getInstance(
            isTab: Boolean,
            title: String = "",
            id: String,
            type: Int,
            iconUrl: String?,
            gs: ArrayList<Group>,
            ps: ArrayList<String>,
        ): TeamDetailFragment {
            val ret = TeamDetailFragment()
            val bundle = Bundle()
            bundle.putBoolean("isTab", isTab)
            bundle.putString("title", title)
            bundle.putString("id", id)
            bundle.putString("iconUrl", iconUrl ?: "")
            bundle.putInt("type", type)
            mGs = gs
            mPs = ps
            ret.arguments = bundle
            return ret
        }

        private fun TeamDetailFragment.isTab(): Boolean {
            return arguments?.getBoolean("isTab") ?: false
        }

        private fun TeamDetailFragment.getTitle(): String {
            return arguments?.getString("title") ?: ""
        }

        private fun TeamDetailFragment.getProjectId(): String {
            return arguments?.getString("id") ?: ""
        }

        private fun TeamDetailFragment.getType(): Int {
            return arguments?.getInt("type", JoyWorkProjectList.TYPE_NORMAL)
                ?: JoyWorkProjectList.TYPE_NORMAL
        }

        private fun TeamDetailFragment.getIconUrl(): String {
            return arguments?.getString("iconUrl") ?: ""
        }
    }

    private val mStatusObserver = observe@{ it: TaskStatusEnum ->
        val f = fs.firstOrNull { frg ->
            frg.getPageStatus() == it
        } ?: return@observe
        mStatusText?.setText(it.getStringId())
        handleAddButton()
        if (it == TaskStatusEnum.DELETED) {
            mModel.updateFilter(FilterValue.getNullInstance())
            mModel.updateSort(SortValue.getDefaultSort(requireContext()))
            mFilterText?.argb("#CDCDCD")
            mFilterIcon.argb("#CDCDCD")
            mSortText?.argb("#CDCDCD")
            mSortIcon.argb("#CDCDCD")
        } else {
            handleFilterSortView(
                mFilterText,
                mFilterIcon,
                mFilterContainer,
                mFilterText?.string() ?: "",
                mModel.mUIStatus.mFilter.enum != ProjectFilterEnum.SCREEN_NULL
            )
            if (mModel.mUIStatus.isInited()) {
                handleFilterSortView(
                    mSortText,
                    mSortIcon,
                    mSortContainer,
                    mSortText?.string() ?: "",
                    mModel.mUIStatus.mSort.code != ProjectSortEnum.SORT_NULL.code
                )
            }
        }
        childFragmentManager.beginTransaction().replace(R.id.mContentView, f).commit()
    }

    private val mSortObserver = observe@{ it: SortValue ->
        val txt = if (it.code == ProjectSortEnum.SORT_NULL.code) {
            getString(R.string.joywork_project_setting_sort)
        } else if (it.code == ProjectSortEnum.SORT_CUSTOM.code) {
            mModel.extLiveData.value?.customFields?.firstOrNull { g ->
                Objects.equals(g.columnId, it.value.columnId)
            }?.title ?: getString(R.string.joywork_project_setting_sort)
        } else {
            it.title ?: ""
        }
        handleFilterSortView(
            mSortText,
            mSortIcon,
            mSortContainer,
            txt,
            it.code != ProjectSortEnum.SORT_NULL.code
        )
    }

    private val mFilterObserver = observe@{ it: FilterValue ->
        val filterValue = mModel.mUIStatus.mFilter
        val enum = filterValue.enum ?: ProjectFilterEnum.SCREEN_NULL
        val txt = if (enum == ProjectFilterEnum.SCREEN_NULL) {
            getString(R.string.joywork_project_setting_filter)
        } else if (enum == ProjectFilterEnum.SCREEN_CUSTOM) {
            // 按清单自定义字段筛选
            val filterExt = filterValue.value
            // 此时理论上应该有扩展字段，然而以为万一
            if (filterExt == null || !filterExt.columnId.isLegalString() || !filterExt.detailId.isLegalString()) {
                mModel.updateFilter(FilterValue.getNullInstance())
                getString(R.string.joywork_project_setting_filter)
            } else {
                // ci 指按哪一个自定义字段筛选
                val ci = filterExt.columnId!!
                // di 指按自定义字段中的哪一项筛选
                val di = filterExt.detailId!!
                val field = mModel.extLiveData.value?.customFields?.firstOrNull {
                    Objects.equals(it.columnId, ci)
                }
                if (field == null || !field.details.isLegalList()) {
                    // 没有当前设置的自定义字段或者选项
                    mModel.updateFilter(FilterValue.getNullInstance())
                    getString(R.string.joywork_project_setting_filter)
                } else {
                    val d = field.details.firstOrNull {
                        Objects.equals(it.detailId, di)
                    }
                    if (d == null) {
                        // 没有当前设置的自定义字段或者选项
                        mModel.updateFilter(FilterValue.getNullInstance())
                        getString(R.string.joywork_project_setting_filter)
                    } else {
                        "${field.title}: ${d.itemContent.content}"
                    }
                }
            }
        } else if (enum == ProjectFilterEnum.SCREEN_PRIORITY) {
            val p = filterValue.value?.priority
            if (p == null) {
                // 没有配置优先级选项
                mModel.updateFilter(FilterValue.getNullInstance())
                getString(R.string.joywork_project_setting_filter)
            } else {
                val level = JoyWorkLevel.NO.getLevel(p)
                "${getString(enum.stringId())}: ${getString(level.getResId())}"
            }
        } else if (enum == ProjectFilterEnum.SCREEN_EXECUTOR) {
            val appId = filterValue.value?.ddAppId
            val erp = filterValue.value?.emplAccount
            if (!appId.isLegalString() || !erp.isLegalString()) {
                // 按执行人搜，但没有给相应人员信息
                mModel.updateFilter(FilterValue.getNullInstance())
                getString(R.string.joywork_project_setting_filter)
            } else {
                val user = filterValue.value?.realName ?: ""
                "${getString(enum.stringId())}: $user"
            }
        } else if (enum == ProjectFilterEnum.SCREEN_EDN_TIME) {
            val endTime = filterValue.value?.endTime
            val startTime = filterValue.value?.startTime
            if (endTime.isLegalTimestamp()) {
                if (startTime.isLegalTimestamp()) {
                    val endS = DateShowUtils.getSimpleTimeShowTextWithHourMinute(endTime!!)
                    val startS = DateShowUtils.getSimpleTimeShowTextWithHourMinute(startTime!!)
                    "${getString(enum.stringId())}: $startS - $endS"
                } else {
                    val endS = DateShowUtils.getSimpleTimeShowTextWithHourMinute(endTime!!)
                    "${getString(enum.stringId())}: $endS"
                }
            } else {
                mModel.updateFilter(FilterValue.getNullInstance())
                getString(R.string.joywork_project_setting_filter)
            }
        } else {
            getString(enum.stringId())
        }
        handleFilterSortView(
            mFilterText,
            mFilterIcon,
            mFilterContainer,
            txt,
            enum != ProjectFilterEnum.SCREEN_NULL
        )
    }

    private lateinit var mTitleView: TextView

    private var mDesc: TextView? = null
    private var mDescContainer: View? = null
    private var mDivider: View? = null
    private val mModel: TeamDetailViewModel by activityViewModels()

    private val mHelper by lazy {
        try {
            val params = mModel.deeplinkParamsLiveData.value
            val obj = JSONObject(params)
            // 判断是否来自会话
            if (obj.has("sessionType") && obj.has("sessionId")) {
                FromIMHelper()
            } else {
                TeamDetailHelperImpl()
            }
        } catch (e: Throwable) {
            TeamDetailHelperImpl()
        }
    }

    private var mStatusContainer: View? = null
    private var mStatusText: TextView? = null

    private lateinit var mFilterContainer: View
    private var mFilterText: TextView? = null
    private lateinit var mFilterIcon: TextView

    private lateinit var mSortContainer: View
    private var mSortText: TextView? = null
    private lateinit var mSortIcon: TextView

    private var mHadSaved: View? = null // 已保存
    private var mSaveView: View? = null // 保存设置
    private var mClear: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        kotlin.runCatching {
            (requireActivity() as? FunctionActivity)?.hideAction()
        }
        initUIConfig()
        val view = initViews(inflater, container!!)
        initObserver()
        return view
    }

    private fun initObserver() {
        mModel.descLiveData.observe(viewLifecycleOwner) {
            if (it.isLegalString()) {
                mDesc?.text = it
                mDescContainer.visible()
            } else {
                mDescContainer.gone()
            }
        }
        mModel.titleLiveData.observe(viewLifecycleOwner) {
            mTitleView.text = it ?: getTitle()
        }
        mModel.statusLiveData.observe(viewLifecycleOwner, mStatusObserver)
        mModel.permissionsLiveData.observe(viewLifecycleOwner) {
            handleAddButton()
            handleClearView()
            handleSaveView()
        }
        mModel.uiStatusLiveData.observe(viewLifecycleOwner) {
            if (it.isInited()) {
                mFilterObserver.invoke(it.mFilter)
                mSortObserver.invoke(it.mSort)
                mStatusObserver.invoke(it.mStatus)
            }
        }
        if (isNormal()) {
            mModel.filterValueLiveData.observe(viewLifecycleOwner, mFilterObserver)
            mModel.sortValueLiveData.observe(viewLifecycleOwner, mSortObserver)
            mModel.saveUserLiveData.observe(viewLifecycleOwner) {
                if (it == null) {
                    mHadSaved?.gone()
                } else {
                    mHadSaved?.visible()
                }
                handleClearView()
            }

            mModel.editModelLiveData.observe(viewLifecycleOwner) {
                handleSaveView()
            }
        }
    }

    private fun handleFilterSortView(
        tv: TextView?,
        icon: TextView?,
        group: View,
        text: String,
        selected: Boolean
    ) {
        if (!selected) {
            group.setBackgroundResource(R.drawable.solid_all_4_f5f5f5)
            tv?.text = text
            tv?.argb("#232930")
            icon?.argb("#232930")
        } else {
            group.setBackgroundResource(R.drawable.solid_all_4_0afe3b30)
            tv?.text = text
            tv?.argb("#FE3E33")
            icon?.argb("#FE3E33")
        }
    }

    private fun initUIConfig() {
        filterSortItems = ArrayList()
        // 默认排序
//        currentSort = SortValue.getDefaultSort(<EMAIL>())

        items.add(TaskStatusEnum.RISK)
        items.add(TaskStatusEnum.UN_FINISH)
        items.add(TaskStatusEnum.FINISH)
        items.add(TaskStatusEnum.ALL)
//        if (isNormal()) {
//            items.add(TaskStatusEnum.DELETED)
//        }
        items.add(TaskStatusEnum.DELETED)
        fs = items.map {
            if (it == TaskStatusEnum.DELETED) {
                clickIds.add(JoyWorkConstant.JOYWORK_PROJECT_ALL)
            } else {
                clickIds.add("")
            }
            TeamBaseFragment.getInstance(it, getProjectId(), getType())
        }
        mModel.updateGroups(mGs ?: ArrayList())
        mModel.updatePermissions(mPs ?: ArrayList())
        mGs = null
        mPs = null
        getConfig()
    }

    private fun getConfig() {
        if (isNormal()) {
            mScope.launch {
                val customFieldGroupOuterD = async {
                    getProjectExt(getProjectId())
                }
                val projectViewOuterD = async {
                    getProjectSetting(getProjectId())
                }
                val projectViewOuter = projectViewOuterD.await()
                val customFieldGroupOuter = customFieldGroupOuterD.await()
                mModel.updateExt(customFieldGroupOuter)
                val view = projectViewOuter.view
                mModel.updateSaveUser(view?.creator)
                mModel.updateProjectView(view?.viewId)
                if (view == null) { // 未保存过自定义视图
                    mModel.init { status ->
                        status.mStatus = TaskStatusEnum.UN_FINISH
                        status.mFilter = FilterValue.getNullInstance()
                        status.mSort = SortValue.getDefaultSort(context)
                    }
                    mModel.notifyUpdate()
                    return@launch
                }
                val customFields = customFieldGroupOuter.customFields ?: ArrayList()
                var filter = view.filter
                var hadFilter = filter.enum != ProjectFilterEnum.SCREEN_CUSTOM
                var sortValue = view.getSortValue(context)
                var hadSort = sortValue.code != ProjectSortEnum.SORT_CUSTOM.code
                val filterExt = filter.value
                customFields.forEach {
                    if (!hadFilter) {
                        hadFilter = Objects.equals(it.columnId, filterExt?.columnId)
                    }
                    if (!hadSort) {
                        hadSort = Objects.equals(it.columnId, sortValue?.value?.columnId)
                    }
                }
                if (!hadFilter) {
                    view.setFilterToNull()
                    filter = view.filter
                }
                if (!hadSort) {
                    view.setSortToNull()
                    sortValue = view.getSortValue(context)
                }
                mModel.init { status ->
                    status.mStatus = view.taskStatus
                    status.mFilter = filter
                    status.mSort = sortValue
                }
                mModel.notifyUpdate()
            }
        } else {
            mScope.launch {
                val customFieldGroupOuter = withContext(Dispatchers.Default) {
                    getProjectExt(getProjectId())
                }
                mModel.updateExt(customFieldGroupOuter)
                mModel.init { status ->
                    status.mSort = SortValue.getDefaultSort(context)
                }
                mModel.notifyUpdate()
            }
        }
    }

    private fun initViews(inflater: LayoutInflater, container: ViewGroup): View {
        val view =
            inflater.inflate(R.layout.jdme_joywork_fragment_project_detail, container, false)
        mDivider = view.findViewById(R.id.mDivider)

        mStatusContainer = view.findViewById(R.id.mStatusContainer)
        mStatusContainer?.setOnClickListener(this)
        mStatusText = view.findViewById(R.id.mStatusText)

        mFilterContainer = view.findViewById(R.id.mFilterContainer)
        mFilterContainer.setOnClickListener(this)
        mFilterText = view.findViewById(R.id.mFilterText)
        mFilterIcon = view.findViewById(R.id.mFilterIcon)

        mSortContainer = view.findViewById(R.id.mSortContainer)
        mSortContainer.setOnClickListener(this)
        mSortText = view.findViewById(R.id.mSortText)
        mSortIcon = view.findViewById(R.id.mSortIcon)
        view.findViewById<View>(R.id.back).setOnClickListener(this)
        view.findViewById<View>(R.id.setting).setOnClickListener(this)
        mTitleView = view.findViewById(R.id.title)
        mTitleView.text = getTitle()

        mSaveView = view.bindClick(R.id.mSaveView) {
            ShitAlertDialog(requireContext(), ShitAlertDialogConfig<ShitAlertDialog>().apply {
                msgRes = R.string.joywork_save_view_title
                subMsg = requireActivity().string(R.string.joywork_save_view_content)
                okRes = R.string.me_ok
                okShitClick = {
                    it.dismiss()
                    saveView()
                }
            }).show()
        }

        mHadSaved = view.bindClick(R.id.mSave) {
            val realName = mModel.saveUserLiveData.value?.realName ?: return@bindClick
            CustomAlertDialog(requireContext(), CustomConfig().apply {
                msgStr = requireActivity().getString(R.string.joywork_view_save_tips, realName)
                okRes = R.string.me_ok
                showCancel = false
            }).show()
        }

        mClear = view.bindClick(R.id.mClear) {
            ShitAlertDialog(requireContext(), ShitAlertDialogConfig<ShitAlertDialog>().apply {
                msgRes = R.string.joywork_view_clear_title
                subMsg = requireActivity().string(R.string.joywork_view_clear_tips)
                okRes = R.string.me_ok
                okShitClick = {
                    it.dismiss()
                    clearView()
                }
            }).show()
        }

        if (isNormal()) {
            mFilterContainer.visible()
            mSortContainer.visible()
            mHadSaved.visible()
            mDivider.visible()
        } else {
            mFilterContainer.gone()
            mSortContainer.gone()
            mHadSaved.gone()
            mDivider.gone()
        }

        view.findViewById<View>(R.id.add).apply {
            mAddView = this
            mAddView?.gone()
            setOnClickListener(this@TeamDetailFragment)
            val lp = (layoutParams as? ViewGroup.MarginLayoutParams)
                ?: ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            val strategy = if (isTab()) JoyWorkMainTabStrategy else JoyWorkMainNormalStrategy
            lp.apply {
                rightMargin =
                    requireActivity().resources.getDimensionPixelSize(strategy.addViewRightMargin)
                bottomMargin =
                    requireActivity().resources.getDimensionPixelSize(strategy.addViewBottomMargin)
            }
            layoutParams = lp
        }
        return view
    }

    private fun saveView() {
        mScope.launch {
            val view = saveProjectExt(
                getProjectId(),
                mModel.mUIStatus.mFilter,
                mModel.mUIStatus.mSort,
                mModel.mUIStatus.mStatus
            )
            mModel.updateProjectView(view.view?.viewId)
            mModel.updateSaveUser(view.view?.creator)
            mModel.updateEditModel(false)
        }
    }

    private fun clearView() {
        mScope.launch {
            val id = mModel.customViewLiveData.value
            if (id.isLegalString()) {
                clearProjectExt(getProjectId(), id!!)
                mModel.updateProjectView(null)
                mModel.mUIStatus.mStatus = TaskStatusEnum.UN_FINISH
                mModel.mUIStatus.mSort = SortValue.getDefaultSort(requireContext())
                mModel.mUIStatus.mFilter = FilterValue.getNullInstance()
                mModel.updateSaveUser(null)
                mModel.notifyUpdate()
            }
        }
    }

    private fun handleAddButton() {
        val permissions = mModel.permissionsLiveData.value ?: ArrayList()
        if (permissions.contains(ProjectPermissionEnum.TASK.code) && getCurrentFrg().showAddButton()) {
            mAddView.visible()
        } else {
            mAddView.gone()
        }
    }

    private fun handleClearView() {
        if (!haveViewPermission() || mModel.saveUserLiveData.value == null) {
            mClear.gone()
        } else {
            mClear.visible()
        }
    }

    private fun handleSaveView() {
        if (haveViewPermission() && mModel.editModelLiveData.value.isTrue()) {
            mSaveView.visible()
            mHadSaved.gone()
        } else {
            mSaveView.gone()
        }
    }

    private fun haveViewPermission(): Boolean {
        val permissions = mModel.permissionsLiveData.value ?: ArrayList()
        return permissions.contains(ProjectPermissionEnum.View.code)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.mSortContainer -> {
                if (mModel.mUIStatus.mStatus == TaskStatusEnum.DELETED) {
                    return
                }
                showSortDialog()
            }

            R.id.mFilterContainer -> {
                if (mModel.mUIStatus.mStatus == TaskStatusEnum.DELETED) {
                    return
                }
                showFilterDialog()
            }

            R.id.mStatusContainer -> {
                showStatusDialog(
                    requireContext(),
                    mModel.mUIStatus.mStatus,
                    isNormal(),
                ) {
                    mModel.updateStatus(it)
                }
            }

            R.id.add -> {
                val dg = mModel.groupsLiveData.value ?: ArrayList()
                val config = ShortcutDialogTmpData()
                config.isProject = true
                // 拼一下 json 传递至新建
                ProjectSelectorDeeplinkUtils.buildExtraInfo(mModel.deeplinkParamsLiveData.value) { extra, inventory, name ->
                    if (extra.isLegalString()) {
                        config.extra = extra
                        config.bizCode = JoyWorkUser.BIZ_CODE
                        config.sessionId = inventory
                        config.tripartiteName = name
                    }
                    CreateDialog(requireActivity(), dg, getProjectId(), config).apply {
                        successCallback = ::createSuccess
                    }.show()
                    JDMAUtils.clickEvent("", JoyWorkConstant.INCOMPLETE_CLICK1, null)
                }
            }

            R.id.back -> {
                requireActivity().finish()
            }

            R.id.setting -> {
                goSetting()
            }
        }
    }

    private fun getCurrentFrg(): TeamBaseFragment {
        return fs.firstOrNull {
            it.getPageStatus() == mModel.mUIStatus.mStatus
        }!!
    }

    private fun goSetting() {
        if (!isNormal()) {
            goSetting2()
            return
        }
        pageData = ProjectSettingPageData()
        val key = PageSharingDataManager.addData(pageData)
        val intent = ProjectSettingActivity.getIntent(
            requireContext(),
            getProjectId(),
            key
        )
        startActivityForResult(intent, 100)
    }

    private fun goSetting2() {
        val intent = ProjectSettingActivity2.getIntent(
            requireContext(),
            getProjectId(),
            getTitle(),
            mModel.mUIStatus.mDesc
        )
        startActivity(intent)
    }

    private fun createSuccess(joywork: JoyWork, dialog: CreateDialog) {
        dialog.dismiss()
        if (mModel.mUIStatus.mSort.code == ProjectSortEnum.SORT_NULL.code) {
            // 没有排序，插入
            if (joywork.projects == null) {
                return
            }
            if (!::fs.isInitialized) {
                return
            }
            if (getCurrentFrg() is IInsertJoyWork) {
                val target = joywork.projects.filter {
                    it.projectId == getProjectId()
                }.map {
                    it.groupId
                }
                getCurrentFrg().insert(joywork, target)
            }
        } else {
            mModel.notifyUpdate()
        }
        SuccessSnackBar(requireActivity()).show(joywork)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::pageData.isInitialized) {
            PageSharingDataManager.removeData(pageData)
        }
        if (!::fs.isInitialized) {
            return
        }
        fs.forEach {
            (it as? IBridge)?.addNetworkFinishObserver(null)
        }
        mScope.cancel()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 100 && resultCode == Activity.RESULT_OK && ::pageData.isInitialized) {
            activity?.setResult(Activity.RESULT_OK)
            pageData.getAction().apply {
                if (this and 1 != 0) {// 关闭界面
                    activity?.finish()
                } else if (this and (1 shl 1) != 0) { // 修改标题
                    if (::mTitleView.isInitialized) {
                        if (pageData.newTitle.isLegalString()) {
                            mTitleView.text = pageData.newTitle
                        }
                        mModel.updateDesc(pageData.newDesc)
                    }
                } else if (this and (1 shl 2) != 0) {// 刷新界面
                    mModel.notifyUpdate()
                }
            }
        }
    }

    private fun showFilterDialog() {
        val c = mModel.mUIStatus.mFilter.enum.code
        showFilterDialog(mModel.mUIStatus.mFilter, mModel, requireContext(), {
            if (it == ProjectFilterEnum.SCREEN_CUSTOMS) {
                mutableListOf(
                    ProjectFilterEnum.SCREEN_EXECUTOR.code,
                    ProjectFilterEnum.SCREEN_EDN_TIME.code,
                    ProjectFilterEnum.SCREEN_PRIORITY.code,
                    ProjectFilterEnum.SCREEN_CUSTOM.code,
                ).contains(c)
            } else {
                it.code == c
            }
        }) {
            onFilterSelect(it)
        }
    }

    private fun onFilterSelect(s: ProjectFilterEnum) {
        if (s.getClickId().isLegalString()) {
            JDMAUtils.onEventClick(s.getClickId(), s.getClickId())
        }
        if (s.getClickId() == JoyWorkConstant.JOYWORK_FILTER_CUSTOM) {
            //点击自定义筛选时
            showCustomFilterDialog(requireContext(), mModel.mUIStatus, mModel.extLiveData.value) {
                mModel.updateFilter(it)
            }
        } else
            mModel.updateFilter(FilterValue.getInstance(s))
    }

    private fun showSortDialog() {
        val customField = getCurrentFrg().getCustomField()
        var custom: ArrayList<String>? = null
        if (customField.isLegalList()) {
            custom = ArrayList()
            for (it in customField!!) {
                custom.add(it.title)
                custom.add(it.columnId)
            }
        }
        showSortDialog2(
            getCurrentFrg().getExtraSortAction(), requireContext(),
            mModel.mUIStatus.mSort, mModel.groupInnerSort.isOpen(), custom
        ) { sortAction: ProjectSortAction, selected, hadChanged ->
            if (!hadChanged) {
                return@showSortDialog2
            }
            if (sortAction.getClickId().isLegalString()) {
                JDMAUtils.onEventClick(sortAction.getClickId(), sortAction.getClickId())
            }
            val tmpType =
                if (selected) ProjectSortTypeEnum.SORT_IN_GROUP else ProjectSortTypeEnum.SORT_OTHER
            mModel.updateGroupInnerSort(tmpType)
            mModel.updateSort(sortAction)
        }
    }

    private fun isNormal(): Boolean {
        val a = arguments ?: return false
        if (!a.containsKey("type")) {
            return false
        }
        return a.getInt("type", JoyWorkProjectList.TYPE_NORMAL) == JoyWorkProjectList.TYPE_NORMAL
    }

    inner class TeamDetailHelperImpl : TeamDetailHelper {
        override fun handleTitle(container: View) {
            container.setOnClickListener {
                goSetting()
            }
        }
    }

    inner class FromIMHelper : TeamDetailHelper {
        override fun handleTitle(container: View) {
            container.findViewById<View>(R.id.mArrow).gone()
        }
    }
}

fun interface TeamDetailHelper {
    fun handleTitle(container: View)
}