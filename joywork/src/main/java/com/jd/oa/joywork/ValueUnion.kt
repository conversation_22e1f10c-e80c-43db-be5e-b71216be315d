package com.jd.oa.joywork

class ValueUnion {
    var p0: Any? = null
    var p1: Any? = null
    var p2: Any? = null
    var p3: Any? = null
    var p4: Any? = null
    var p5: Any? = null
    var p6: Any? = null

    operator fun get(i: Int): Any? {
        return when (i) {
            0 -> {
                p0
            }
            1 -> {
                p1
            }
            2 -> {
                p2
            }
            3 -> {
                p3
            }
            4 -> {
                p4
            }
            5 -> {
                p5
            }
            6 -> {
                p6
            }
            else -> null
        }
    }

    operator fun set(i: Int, v: Any?) {
        when (i) {
            0 -> {
                p0 = v
            }
            1 -> {
                p1 = v
            }
            2 -> {
                p2 = v
            }
            3 -> {
                p3 = v
            }
            4 -> {
                p4 = v
            }
            5 -> {
                p5 = v
            }
            6 -> {
                p6 = v
            }
            else -> null
        }
    }
}