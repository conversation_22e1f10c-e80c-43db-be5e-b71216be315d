package com.jd.oa.joywork.team.kpi

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.joywork.R
import com.jd.oa.joywork.bean.JoyWorkUser
import com.jd.oa.joywork.common.IUserListItemCallback
import com.jd.oa.joywork.common.UserListFragment
import com.jd.oa.model.service.im.dd.entity.DeptInfo
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalString
import com.jd.oa.joywork.isSelf
import com.jd.oa.joywork.team.bean.OrgList
import com.jd.oa.utils.argb
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class OrgListFragment : UserListFragment() {
    companion object {
        fun getInstance(type: Int): OrgListFragment {
            val r = OrgListFragment()
            val b = Bundle()
            b.putInt("classType", type)
            b.putString("type", "user")
            r.arguments = b
            return r
        }
    }

    private val itemCallback = object : IUserListItemCallback() {
        override fun bindItem(itemView: View) {
            itemView.setOnClickListener {
                val user = it.tag as JoyWorkUser
                if (user.isSelf()) {
                    <EMAIL>?.finish()
                } else {
                    GoalListActivity.startOther(<EMAIL>(), user)
                }
            }
        }

        override fun bindItemAction(tv: TextView) {
            val type = arguments?.getInt("classType") ?: OrgList.SUPER
            if (type == OrgList.FOLLOWED) {
                tv.visible()
                tv.setText(R.string.icon_padding_followhear)
                tv.argb("#FFCF33")
                tv.setOnClickListener {
                    cancelFollow(it.tag as JoyWorkUser)
                }
            } else {
                tv.gone()
            }
        }
    }

    private val mObserver = object : Observer<List<JoyWorkUser>> {
        override fun onChanged(it: List<JoyWorkUser>) {
            it.forEach {
                if ((it.deptInfo == null || !it.deptInfo.deptName.isLegalString()) && it.deptName.isLegalString()) {
                    it.deptInfo = DeptInfo()
                    it.deptInfo.deptName = it.deptName
                }
            }
            appendData(it)
            val upLoadHelper = mPullUpLoadHelper ?: return
            if (it.size >= KpiRepo.SUB_PAGE_SIZE) {
                upLoadHelper.setLoaded()
            } else {
                upLoadHelper.setComplete()
            }
        }
    }

    private val mRefreshObserver = object : Observer<List<JoyWorkUser>> {
        override fun onChanged(it: List<JoyWorkUser>) {
            if (!it.isLegalList()) {
                showOrgListEmpty()
                return
            }

            it.forEach {
                if ((it.deptInfo == null || !it.deptInfo.deptName.isLegalString()) && it.deptName.isLegalString()) {
                    it.deptInfo = DeptInfo()
                    it.deptInfo.deptName = it.deptName
                }
            }
            replaceData(it)
            val upLoadHelper = mPullUpLoadHelper ?: return
            if (it.size >= KpiRepo.SUB_PAGE_SIZE) {
                upLoadHelper.setLoaded()
            } else {
                upLoadHelper.setComplete()
            }
        }
    }

    private val cancelFollowReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val i = intent ?: return
            val user = i.parseFromIntent()
            remove(user)
            if (getItemCount() <= 0) {
                showOrgListEmpty()
            }
        }
    }

    private val followReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val i = intent ?: return
            val user = i.parseFromIntent()
            appendData(mutableListOf(user))
        }
    }

    private fun Intent.parseFromIntent(): JoyWorkUser {
        val user = JoyWorkUser()
        user.ddAppId = getStringExtra("ddAppId")
        user.emplAccount = getStringExtra("emplAccount")
        return user
    }

    private var mViewModel: OrgListViewModel? = null
    private var mPullUpLoadHelper: PullUpLoadHelper? = null

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        mViewModel = ViewModelProviders.of(requireActivity()).get(OrgListViewModel::class.java)
        mItemCallback = itemCallback
        return view
    }

    private var scope: CoroutineScope? = null

    override fun setupRv(recyclerView: RecyclerView) {
        val type = arguments?.getInt("classType") ?: OrgList.SUPER
        if (type == OrgList.SUB || type == OrgList.FOLLOWED) {
            scope = MainScope()
            mPullUpLoadHelper = PullUpLoadHelper(recyclerView) {
                loadMore()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val type = arguments?.getInt("classType") ?: OrgList.SUPER
        when (type) {
            OrgList.SUPER -> {
                mViewModel?.supLiveData?.observe(requireActivity(), mObserver)
            }
            OrgList.SUB -> {
                mViewModel?.subLiveData?.observe(requireActivity(), mObserver)
                mViewModel?.subRefreshLiveData?.observe(requireActivity(), mRefreshObserver)
            }
            OrgList.FOLLOWED -> {
                KpiMsgCenter.registerCancelFollow(requireContext(), cancelFollowReceiver)
                KpiMsgCenter.registerFollow(requireContext(), followReceiver)
                mViewModel?.followedLiveData?.observe(requireActivity(), mObserver)
                mViewModel?.followedRefreshLiveData?.observe(requireActivity(), mRefreshObserver)
                scope?.launch {
                    val followed = KpiRepo.listFollowed(0)
                    followed.isSuccess({ _, msg, _ ->
                        showOrgListEmpty(msg)
                    }) { org ->
                        val it = org?.members ?: ArrayList<JoyWorkUser>()
                        if (it.isLegalList()) {
                            mViewModel?.appendFollowed(it)
                        } else {
                            showOrgListEmpty()
                        }
                    }
                }
            }
        }
    }

    private fun loadMore() {
        val type = arguments?.getInt("classType") ?: OrgList.SUPER
        if (type == OrgList.SUB) {
            scope?.launch {
                val result = KpiRepo.listSub(getItemCount())
                result.isSuccess({ _, _, _ ->
                    mPullUpLoadHelper?.setLoaded()
                }) {
                    mViewModel?.appendSub(it?.subs ?: mutableListOf())
                }
            }
        } else if (type == OrgList.FOLLOWED) {
            scope?.launch {
                val result = KpiRepo.listFollowed(getItemCount())
                result.isSuccess({ _, _, _ ->
                    mPullUpLoadHelper?.setLoaded()
                }) { org ->
                    val it = org?.members ?: ArrayList<JoyWorkUser>()
                    mViewModel?.appendFollowed(it)
                }
            }
        }
    }

    override fun refreshable(): Boolean {
        val type = arguments?.getInt("classType") ?: OrgList.SUPER
        return type == OrgList.FOLLOWED || type == OrgList.SUB
    }

    override fun onRefresh() {
        val type = arguments?.getInt("classType") ?: OrgList.SUPER
        if (type == OrgList.SUB) {
            scope?.launch {
                val result = KpiRepo.listSub(0)
                mSwipeRefreshLayout?.isRefreshing = false
                result.ifSuccessToast {
                    mViewModel?.replaceSub(it?.subs ?: mutableListOf())
                }
            }
        } else if (type == OrgList.FOLLOWED) {
            scope?.launch {
                val result = KpiRepo.listFollowed(0)
                mSwipeRefreshLayout?.isRefreshing = false
                result.ifSuccessToast { org ->
                    val it = org?.members ?: ArrayList<JoyWorkUser>()
                    mViewModel?.replaceFollowed(it)
                }
            }
        }
    }

    private fun cancelFollow(joyWorkUser: JoyWorkUser) {
        showCancelFollowDialog(requireContext()) {
            KpiRepo.followOrCancelSomeone(joyWorkUser.emplAccount, joyWorkUser.ddAppId, true) {
                if (it.isLegalString()) {
                    Toast.makeText(requireContext(), it!!, Toast.LENGTH_SHORT).show()
                } else {
                    remove(joyWorkUser)
                    KpiMsgCenter.sendCancelFollow(
                        requireContext(),
                        joyWorkUser.ddAppId,
                        joyWorkUser.emplAccount
                    )
                }
            }
        }
    }

    private fun showOrgListEmpty(msg: String? = null) {
        showEmpty(msg ?: getString(R.string.joywork_followed_list_empty), emptyDrawable = R.drawable.joywork_empty_followed)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        scope?.cancel()
        KpiMsgCenter.removeReceiver(requireContext(), cancelFollowReceiver, followReceiver)
    }
}