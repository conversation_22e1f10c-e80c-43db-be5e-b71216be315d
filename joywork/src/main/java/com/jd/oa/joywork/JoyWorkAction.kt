package com.jd.oa.joywork

import android.content.Context
import android.graphics.Color
import com.jd.oa.joywork.team.bean.ProjectFilterEnum
import com.jd.oa.joywork.team.bean.ProjectSortEnum
import com.jd.oa.utils.string

/**
 * @param action 要显示在界面上的文字
 */
open class JoyWorkAction(val action: String) {
    open fun bgColor(): Int {
        return Color.TRANSPARENT
    }

    open fun iconId(): Int {
        return R.string.icon_general_calendarl
    }

    open fun color(): Int {
        return Color.TRANSPARENT
    }
}

/**
 * 编辑
 */
class EditAction(context: Context) : JoyWorkAction(context.string(R.string.me_edit)) {
    override fun bgColor(): Int {
        return Color.parseColor("#B7BCC6")
    }
}

/**
 * 删除
 */
class DelAction(context: Context) : JoyWorkAction(context.string(R.string.me_delete)) {
    override fun bgColor(): Int {
        return Color.parseColor("#FE3E33")
    }
}

/**
 * 撤回
 */
class ReverseAction(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_action_restore)) {
    override fun bgColor(): Int {
        return Color.parseColor("#FF4C7CFF")
    }
}

class DetailAction(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_action_detail)) {
    override fun bgColor(): Int {
        return Color.parseColor("#FF62656D")
    }
}

class UrgeAction(context: Context) : JoyWorkAction(context.string(R.string.joywork_urge)) {
    override fun bgColor(): Int {
        return Color.parseColor("#FFFFB416")
    }
}

class AssignAction(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_action_assgin)) {
    override fun bgColor(): Int {
        return Color.parseColor("#FFFFB416")
    }
}

open class OperateAction(action: String): JoyWorkAction(action) {
    override fun iconId(): Int {
        return -1
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

// 移动
class MoveAction(context: Context) :
    OperateAction(context.string(R.string.joywork_project_button_move_to))

// 转派
class TransferAction(context: Context) :
    OperateAction(context.string(R.string.joywork_project_button_transfer))

// 拒绝
class RejectAction(context: Context) :
    OperateAction(context.string(R.string.joywork_project_button_reject))

// 删除
class DeleteAction(context: Context) :
    OperateAction(context.string(R.string.joywork_project_button_delete)) {
    override fun color(): Int {
        return Color.parseColor("#FFF63218")
    }
}

/**
 * 项目分组重命名
 */
class ProjectRename(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_project_rename)) {
    override fun iconId(): Int {
        return R.string.icon_general_edit
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

/**
 * 项目分组 新增上方分组
 */
class ProjectNewBefore(context: Context, val vertical: Boolean = true) :
    JoyWorkAction(
        context.string(
            if (vertical)
                R.string.joywork_project_new_before
            else
                R.string.joywork_project_new_left
        )
    ) {
    override fun iconId(): Int {
        return if (vertical)
            R.string.icon_direction_arrowup
        else
            R.string.icon_direction_arrowleft
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

/**
 * 项目分组 新增下方分组
 */
class ProjectNewAfter(context: Context, val vertical: Boolean = true) :
    JoyWorkAction(
        context.string(
            if (vertical)
                R.string.joywork_project_new_after
            else
                R.string.joywork_project_new_right
        )
    ) {
    override fun iconId(): Int {
        return if (vertical)
            R.string.icon_direction_arrowdown
        else
            R.string.icon_direction_arrowright
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

/**
 * 项目分组 新增下方分组
 */
class ProjectGroupSort(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_project_group_sort)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

/**
 * 项目分组 删除分组
 */
class ProjectDelete(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_project_delete)) {
    override fun iconId(): Int {
        return R.string.icon_edit_delete
    }

    override fun color(): Int {
        return Color.parseColor("#FFF63218")
    }
}

/**
 * 项目详情，右上角 筛选
 */
class ProjectFilter(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_project_setting_filter)) {
    override fun iconId(): Int {
        return R.string.icon_general_screening
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

/**
 * 项目详情，右上角 排序
 */
class ProjectSort(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_project_setting_sort)) {
    override fun iconId(): Int {
        return R.string.icon_workplacesortascending
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

/**
 * 项目详情，右上角 设置
 */
class ProjectSetting(context: Context) :
    JoyWorkAction(context.string(R.string.joywork_project_setting_setting)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

abstract class FilterAction(action: String) : JoyWorkAction(action) {
    abstract fun enum(): ProjectFilterEnum

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }
}

/**
 * 封装 dialog 中每一个 item
 * @param action: 要显示在界面上的文字
 */
abstract class SortAction(action: String) : JoyWorkAction(action) {
    abstract fun enum(): ProjectSortEnum
    fun javaEnum(): ProjectSortEnum {
        return enum()
    }

    override fun color(): Int {
        return Color.parseColor("#FF1B1B1B")
    }

    override fun equals(other: Any?): Boolean {
        return ((other as? SortAction)?.enum()?.code ?: -1) == enum().code
    }

    abstract fun getClickId(): String;
}

/**
 * 项目详情，排序，标题。用于区分普通样式与特殊样式，里面的值无用
 */
class ProjectSortTitle(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_project_sort_no)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.SORT_NULL
    }

    override fun getClickId(): String {
        return ""
    }
}

sealed class ProjectSortAction(title: String) : SortAction(title) {
    open fun showNewLabel() = false
}

class ProjectSortProject(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_self_order)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.PROJECT
    }

    override fun getClickId(): String {
        return ""
    }
}

/**
 * 项目详情，排序，无
 */
class ProjectSortNo(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_project_sort_no)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.SORT_NULL
    }

    override fun getClickId(): String {
        return ""
    }
}

/**
 * 项目详情，排序，自定义分组
 */
class ProjectSortCustomGroup(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_sort_custom_group)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.SORT_CUSTOM_GROUP
    }

    override fun getClickId(): String {
        return ""
    }

    override fun showNewLabel(): Boolean {
        return true
    }
}

/**
 * 项目详情，排序，截止时间
 */
class ProjectSortTime(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_project_setting_sort_time)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.SORT_END_TIME
    }

    override fun getClickId(): String {
        return JoyWorkConstant.JOYWORK_SORT_END_TIME
    }
}

class ProjectPriority(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_update_priority)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.PRIORITY
    }

    override fun getClickId(): String {
        return ""
    }
}

/**
 * 项目详情，排序，指派对象
 */
class ProjectSortOwner(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_project_setting_sort_owner)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.SORT_BY_OWNER
    }

    override fun getClickId(): String {
        return JoyWorkConstant.JOYWORK_SORT_OWNER
    }
}

/**
 * 项目详情，排序，完成时间
 */
class ProjectSortFinish(context: Context) :
    ProjectSortAction(context.string(R.string.joywork_project_setting_sort_finish)) {
    override fun iconId(): Int {
        return R.string.icon_general_set
    }

    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.FINISH_TIME
    }

    override fun getClickId(): String {
        return JoyWorkConstant.JOYWORK_SORT_FINISH
    }
}

/**
 * @param action: 显示内容
 * @param value：当前选项的 id
 */
class ProjectSortCustom(action: String, val value: String) : ProjectSortAction(action) {
    override fun enum(): ProjectSortEnum {
        return ProjectSortEnum.SORT_CUSTOM
    }

    override fun equals(other: Any?): Boolean {
        return super.equals(other) && ((other as? ProjectSortCustom)?.value == value)
    }

    override fun getClickId(): String {
        return ""
    }
}