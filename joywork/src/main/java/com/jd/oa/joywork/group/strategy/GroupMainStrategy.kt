package com.jd.oa.joywork.group.strategy

import android.app.Activity
import android.graphics.Color
import android.view.View
import android.widget.FrameLayout
import com.jd.oa.joywork.R
import com.jd.oa.joywork.openDeeplink
import com.jd.oa.router.DeepLink
import com.jd.oa.router.DeepLink.JDME
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.inflater

abstract class GroupMainStrategy {
    private val DEEP_LINK = DeepLink.rnOld("201909020601","routeTag=document_edit&rnStandalone=2&page_id=xJ00KRpZWpl6JRc4kCXq")

    fun handleTitle(parent: FrameLayout, activity: Activity) {
        parent.context?.apply {
            inflater.inflate(R.layout.jdme_joywork_group_normal_title, parent, true)
            parent.findViewById<View>(R.id.back).setOnClickListener {
                activity.finish()
            }
            val robot = parent.findViewById<View>(R.id.robot)
            robot.background = DrawableEx.roundStrokeRect(
                Color.parseColor("#CDCDCD"),
                CommonUtils.dp2px(0.5f),
                CommonUtils.dp2FloatPx(4)
            )
            parent.findViewById<View>(R.id.more_button).setOnClickListener {
                DEEP_LINK.openDeeplink()
            }
        }
    }

    abstract fun getAddViewBottomMargin(): Int

    abstract fun getAddViewRightMargin(): Int
}

object GroupMainNormalStrategy : GroupMainStrategy() {

    override fun getAddViewBottomMargin(): Int = R.dimen.joywork_fab_margin

    override fun getAddViewRightMargin(): Int = R.dimen.joywork_fab_margin
}

object GroupMainTabStrategy : GroupMainStrategy() {

    override fun getAddViewBottomMargin() = R.dimen.joywork_add_view_bottom_margin

    //  不要问这些尺寸哪来的，是我通过 sketch 量 joyspace 得到的
    override fun getAddViewRightMargin() = R.dimen.joywork_add_view_right_margin
}