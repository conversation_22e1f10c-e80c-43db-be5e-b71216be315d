package com.jd.oa.joywork.self.owner

import android.view.View
import com.jd.oa.joywork.*
import com.jd.oa.joywork.repo.CacheStrategy
import com.jd.oa.joywork.self.SelfBaseItf
import com.jd.oa.joywork.self.base.SelfListBaseFragment
import com.jd.oa.joywork.self.base.SelfListCacheReader
import com.jd.oa.joywork.self.base.SelfListCacheWriter
import com.jd.oa.joywork.self.base.SelfPageEnum
import com.jd.oa.joywork.sp.JoyWorkPreference
import com.jd.oa.joywork.team.bean.SelfListSortType
import com.jd.oa.joywork.team.bean.SelfListViewType
import com.jd.oa.storage.entity.KvEntity

/**
 * 个人待办，我负责的
 */
class MyOwnerFragment : SelfListBaseFragment(), SelfBaseItf {
    private val sp by lazy {
        JoyWorkPreference(requireActivity())
    }

    override fun getUserRole(): TaskUserRole {
        return TaskUserRole.OWNER
    }

    override fun onPageUnselected() {
    }

    override fun onPageSelected() {
    }

    override fun onShowAtTabbar() {
    }

    override fun getTaskListType(): TaskListTypeEnum {
        return TaskListTypeEnum.HANDLE
    }


    override fun getPagePVName(): String {
        return JoyWorkConstant.PAGE_MAIN
    }

    override fun getSelfListCacheReader(): SelfListCacheReader {
        return object : SelfListCacheReader {
            override fun read(): String? {
                return sp.get(JoyWorkPreference.KV_ENTITY_PERSON_LIST)
            }

            override fun interceptNetwork(): Boolean {
                return false
            }
        }
    }

    override fun getSelfListCacheWriter(): SelfListCacheWriter {
        return object : SelfListCacheWriter {
            override fun write(data: String) {
                sp.put(JoyWorkPreference.KV_ENTITY_PERSON_LIST, data)
            }
        }
    }

    override fun getCustomGroupType(): Int {
        return 4
    }

    override fun handleSortActions(list: MutableList<ProjectSortAction>) {
        list.add(0, ProjectSortOwner(requireContext()))
        list.add(0, ProjectSortCustomGroup(requireContext()))
    }

    override fun getPageId(): String {
        return "MyOwner"
    }

    override fun getViewType(): Int {
        return SelfListViewType.MY_OWNER.code
    }

    override fun getSortType(): Int {
        return SelfListSortType.MY_OWNER.code
    }
}