package com.jd.oa.joywork.utils

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.URLSpan
import android.text.util.Linkify
import android.view.View
import android.widget.TextView
import androidx.annotation.ColorInt
import java.util.regex.Pattern

object HyperLinkHelper {

    private const val REGEX =
        "((http[s]?|ftp)://[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$,%^&*+?:_/=<>]*)?)|(www.[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$,%^&*+?:_/=<>]*)?)"


    fun findLinks(content: String): List<String> = runCatching {
        val regex = Regex(REGEX)
        val matchResult = regex.findAll(content)
        matchResult.map {
            it.value
        }.toList()
    }.getOrNull() ?: emptyList()


    /**
     * 转换成链接。
     */
    fun transUrlSpan(text: CharSequence): Spannable {
        val ss = SpannableString.valueOf(text)
        Linkify.addLinks(ss, Pattern.compile(REGEX), "")
        val urlSpans = ss.getSpans(
            0, ss.length,
            URLSpan::class.java
        ) ?: return ss
        for (sp in urlSpans) {
            val start = ss.getSpanStart(sp)
            val end = ss.getSpanEnd(sp)
            ss.removeSpan(sp)
            ss.setSpan(
                UrlLinkSpan(sp.url.formatUrl(), Color.parseColor("#4C7CFF")),
                start,
                end,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        return ss
    }

    /**
     * 设置点击事件。
     */
    fun setClickListener(spanned: Spanned, listener: OnClickListener) {
        spanned.getSpans(0, spanned.length, UrlLinkSpan::class.java)
            .forEach { it.listener = listener }
    }

    private fun String.formatUrl(): String {
        return if (indexOf("http") != 0
            && indexOf("ftp") != 0
            && indexOf("file") != 0
        ) {
            "http://$this"
        } else {
            this
        }
    }

    // 并把URLSpan替换成样式需要的Span。
    class UrlLinkSpan(val url: String, @ColorInt val highColor: Int) : ClickableSpan() {

        var listener: OnClickListener? = null

        override fun onClick(widget: View) {
            listener?.onUrlLinkClick(widget, url)
        }

        override fun updateDrawState(ds: TextPaint) {
            super.updateDrawState(ds)
            ds.color = highColor
            ds.bgColor = Color.TRANSPARENT
            ds.isUnderlineText = false
        }
    }

    interface OnClickListener {
        fun onUrlLinkClick(widget: View, url: String)
    }
}

fun TextView.setUrlLinkText(
    text: CharSequence?,
    listener: HyperLinkHelper.OnClickListener
) {
    highlightColor = Color.TRANSPARENT
    if (this.text?.toString() != text) {
        this.text = if (text != null) {
            HyperLinkHelper.transUrlSpan(text).apply {
                HyperLinkHelper.setClickListener(this, listener)
                movementMethod = LinkMovementMethod()
            }
        } else {
            ""
        }
    }
}