package com.jd.oa.joywork.team

import com.alibaba.fastjson.JSONObject
import com.google.gson.Gson
import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.joywork.*
import com.jd.oa.joywork.bean.*
import com.jd.oa.joywork.detail.data.entity.FilterValue
import com.jd.oa.model.service.im.dd.entity.Members
import com.jd.oa.joywork.detail.data.entity.SortValue
import com.jd.oa.joywork.detail.data.entity.custom.CustomFieldGroupOuter
import com.jd.oa.joywork.detail.data.entity.custom.ProjectViewOuter
import com.jd.oa.joywork.detail.data.entity.mark.JoyWorkContent2
import com.jd.oa.joywork.repo.JoyWorkCreateParams
import com.jd.oa.joywork.team.bean.*
import com.jd.oa.model.TenantCode
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.network.SimpleReqCallbackAdapter
import com.jd.oa.network.httpmanager.HttpManager
import com.tencent.mm.opensdk.modelbase.BaseResp
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.jvm.isAccessible

object ProjectRepo {
    // 加载待办时，每一个页数量
    val PAGE_LIMIT = 20
    val PROJECT_PAGE_LIMIT = 100

    /**
     * 清单列表
     */
    fun projectList(callback: JoyWorkProjectCallback, offset: Int) {
        val params: HashMap<String, Any> = HashMap()
        params["offset"] = offset
        params["limit"] = PROJECT_PAGE_LIMIT
        params["returnGroup"] = true
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JoyWorkProjectList>(JoyWorkProjectList::class.java) {
                override fun onFailure(errorMsg: String) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkProjectList?,
                    tArray: List<JoyWorkProjectList?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        preSetList?.clear()
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            }),
            JoyWorkNetConstant.PROJECT_LIST
        )
    }

    fun getArchiveList(callback: JoyWorkProjectCallback) {
        val params: HashMap<String, Any> = HashMap()
        params["returnGroup"] = false
        HttpManager.color().post(
            params,
            null,
            JoyWorkNetConstant.ARCHIVE_LIST,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JoyWorkProjectList>(JoyWorkProjectList::class.java) {
                override fun onFailure(errorMsg: String) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkProjectList?,
                    tArray: List<JoyWorkProjectList?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        preSetList?.clear()
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            })
        )
    }

    enum class ProjectRequestPermission(val value: Int) {
        CREATOR(1),
        EDITABLE(2),
        READONLY(3);
    }

    fun listEditableProject(callback: JoyWorkProjectCallback, offset: Int) {
        val params: HashMap<String, Any> = HashMap()
        params["offset"] = offset
        params["limit"] = 100
        params["returnGroup"] = true
        val a = intArrayOf(
            ProjectRequestPermission.CREATOR.value,
            ProjectRequestPermission.EDITABLE.value
        )
        params["permissions"] = a
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<JoyWorkProjectList>(JoyWorkProjectList::class.java) {
                override fun onFailure(errorMsg: String) {
                    callback.onError(JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    jsonObject: JoyWorkProjectList?,
                    tArray: List<JoyWorkProjectList?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        preSetList?.clear()
                        callback.call(jsonObject)
                        callback.call(jsonObject, rawData)
                    } ?: onFailure("")
                }
            }),
            JoyWorkNetConstant.PROJECT_LIST
        )
    }

    /**
     * 新建清单
     */
    fun createProject(
        list: List<Members>,
        title: String,
        desc: String,
        callback: RepoCallback<ProjectDetail>
    ) {
        val params = hashMapOf<String, Any>()
        params["title"] = title //项目名称
        params["desc"] = desc //项目描述
        params["members"] = ArrayList<Map<String, Any>>().apply {
            val r = list.map {
                val tmp = HashMap<String, Any>()
                tmp["userId"] = it.userId ?: ""
                tmp["teamId"] = it.app ?: JoyWorkUser.DEFAULT_TEAM_ID
                if (TenantCode.getByDDAppId(it.app) != null) {
                    tmp["emplAccount"] = it.emplAccount ?: ""
                    tmp["app"] = it.app ?: ImDdService.APP_ID_JDME_CHINA
                    tmp["ddAppId"] = it.app ?: ImDdService.APP_ID_JDME_CHINA
                }
                tmp["userRole"] = it.userRole
                tmp["headImg"] = it.headImg ?: ""
                tmp["realName"] = it.realName ?: ""
                tmp["permission"] = it.permission
                tmp
            }
            addAll(r)
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ProjectDetail>(ProjectDetail::class.java) {
                override fun onSuccess(
                    jsonObject: ProjectDetail?,
                    tArray: MutableList<ProjectDetail>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.result(jsonObject, null, true)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?) {
                    callback.result(null, JoyWorkEx.filterErrorMsg(errorMsg), false)
                }
            }),
            "work.task.createProject.v2"
        )
    }

    fun listProjectWork(
        projectId: String,
        status: Int,
        filterValue: FilterValue,
        sortValue: SortValue,
        projectType: Int, //1普通清单；3系统清单
        callback: AbsRepoCallback<ProjectGroups>
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["taskStatus"] = status
        params["limit"] = PAGE_LIMIT
        params["projectType"] = projectType
        params["need"] = 50
        if (filterValue.type != ProjectFilterEnum.SCREEN_NULL.code) {
            val map = HashMap<String, Any>()
            filterValue.toMap(map)
            params["filter"] = map
        }
        if (sortValue.code != ProjectSortEnum.SORT_NULL.code && sortValue.code != ProjectSortEnum.SORT_CUSTOM_GROUP.code) {
//            params["sort"] = JSONObject.toJSON(sortValue)
            val map = HashMap<String, Any>()
            map["type"] = sortValue.code
            if (sortValue.value != null) {
                map["value"] = sortValue.value
            }
            params["sort"] = map
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<ProjectGroups>(object :
                JoyWorkReqCallback<ProjectGroups>(ProjectGroups::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.result(null, JoyWorkEx.filterErrorMsg(errorMsg), false, code)
                }

                override fun onSuccess(
                    jsonObject: ProjectGroups?,
                    tArray: List<ProjectGroups?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.result(jsonObject, null, true, null)
                    } ?: onFailure("")
                }
            }),
            "work.task.getProjectTasks.v3"
        )
    }


    fun listProjectWorkWithoutGroup(
        offset: Int,
        projectId: String,
        status: Int,
        filterValue: FilterValue?,
        sortValue: SortValue?,
        type: Int? = null,
        callback: AbsRepoCallback<ResultWithoutGroup>
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["offset"] = offset
        params["taskStatus"] = status
        params["limit"] = PAGE_LIMIT
        if (type != null) {
            params["projectType"] = type;
        }
        if (filterValue != null && filterValue.type != ProjectFilterEnum.SCREEN_NULL.code) {
            val map = HashMap<String, Any>()
            filterValue.toMap(map)
            params["filter"] = map
        }
        if (sortValue != null && sortValue.code != ProjectSortEnum.SORT_NULL.code) {
//            params["sort"] = JSONObject.toJSON(sortValue)
            val map = HashMap<String, Any>()
            map["type"] = sortValue.code
            if (sortValue.value != null) {
                map["value"] = sortValue.value
            }
            params["sort"] = map

        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<ResultWithoutGroup>(object :
                JoyWorkReqCallback<ResultWithoutGroup>(ResultWithoutGroup::class.java) {
                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.result(null, JoyWorkEx.filterErrorMsg(errorMsg), false, code)
                }

                override fun onSuccess(
                    jsonObject: ResultWithoutGroup?,
                    tArray: List<ResultWithoutGroup?>?,
                    rawData: String
                ) {
                    jsonObject?.apply {
                        callback.result(jsonObject, null, true, null)
                    } ?: onFailure("")
                }
            }),
            "work.task.getProjectTasksWithoutGroup.v2"
        )
    }

    fun getGroupTasks(
        projectId: String,
        groupId: String,
        status: Int,
        offset: Int,
        projectType: Int, //1普通清单；3系统清单
        filterValue: FilterValue,
        sortValue: SortValue?,
        callback: RepoCallback<Group>
    ) {
        val params = hashMapOf<String, Any>()
        params["offset"] = offset
        params["limit"] = ProjectRepo.PAGE_LIMIT
        params["projectType"] = projectType
        params["projectId"] = projectId
        params["taskStatus"] = status

        params["groupId"] = groupId

        if (filterValue.type != ProjectFilterEnum.SCREEN_NULL.code) {
            val map = HashMap<String, Any>()
            filterValue.toMap(map)
            params["filter"] = map
        }
        if (sortValue != null && sortValue.code != ProjectSortEnum.SORT_NULL.code) {
            val map = HashMap<String, Any>()
            map["type"] = sortValue.code
            if (sortValue.value != null) {
                map["value"] = sortValue.value
            }
            params["sort"] = map

        }

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<Group>(object : AbsReqCallback<Group>(Group::class.java) {
                override fun onFailure(errorMsg: String) {
                    callback.result(null, JoyWorkEx.filterErrorMsg(errorMsg), false)
                }

                override fun onSuccess(jsonObject: Group?, tArray: List<Group?>?, rawData: String) {
                    jsonObject?.apply {
                        jsonObject.groupId = groupId
                        jsonObject.projectId = projectId
                        callback.result(jsonObject, null, true)
                    } ?: onFailure("")
                }
            }),
            "work.task.getGroupTasks.v2"
        )
    }

    fun delGroup(
        projectId: String,
        groupId: String,
        hard: Boolean,
        callback: (Boolean, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()

        params["projectId"] = projectId
        params["type"] =
            if (hard) GroupDeleteEnum.NO_KEEP_TASK.code else GroupDeleteEnum.KEEP_TASK.code
        params["groupId"] = groupId

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<Group>(object : AbsReqCallback<Group>(Group::class.java) {
                override fun onFailure(errorMsg: String) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(jsonObject: Group?, tArray: List<Group?>?, rawData: String) {
                    callback(true, null)
                }
            }),
            "work.task.deleteGroup.v2"
        )
    }

    fun createGroup(
        projectId: String,
        title: String,
        front: String?,
        after: String?,
        type: Int = 1,
        callback: (Boolean, Group?, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["type"] = type
        params["title"] = title
        front?.apply {
            params["front"] = this
        }
        after?.apply {
            params["after"] = this
        }

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<Group>(Group::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(t: Group?, tArray: MutableList<Group>?, rawData: String?) {
                    callback.invoke(true, t, "")
                }
            }),
            "work.task.createProjectGroup.v2"
        )
    }

    fun updateGroup(groupId: String, title: String, callback: (Boolean, String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["groupId"] = groupId
        params["title"] = title

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<Group>(Group::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, errorMsg)
                }

                override fun onSuccess(t: Group?, tArray: MutableList<Group>?, rawData: String?) {
                    callback.invoke(true, null)
                }
            }),
            "work.task.updateGroup.v2"
        )
    }

    suspend fun listGroupSuspend(projectId: String) =
        suspendCancellableCoroutine<List<Group>> { continuation ->
            listGroup(projectId) { result, gs, msg ->
                if (gs.isLegalList()) {
                    continuation.resumeWith(Result.success(gs!!))
                } else {
                    continuation.resumeWithException(RuntimeException(JoyWorkEx.filterErrorMsg(msg)))
                }
            }
        }

    fun listGroup(projectId: String, callback: (Boolean, List<Group>?, msg: String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ProjectGroups>(ProjectGroups::class.java) {
                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, null, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onSuccess(
                    t: ProjectGroups?,
                    tArray: MutableList<ProjectGroups>?,
                    rawData: String?
                ) {
                    t?.apply {
                        callback.invoke(true, groups, null)
                    } ?: onFailure("")
                }
            }),
            "work.task.getGroupList.v2"
        )
    }

    /**
     * 更新项目
     */
    fun updateProject(
        projectId: String,
        title: String,
        desc: String,
        callback: (Boolean, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["title"] = title
        params["desc"] = desc

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<BaseResp>(BaseResp::class.java) {
                override fun onSuccess(
                    t: BaseResp?,
                    tArray: MutableList<BaseResp>?,
                    rawData: String?
                ) {
                    callback(true, null)
                }

                override fun onFailure(errorMsg: String?) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))

                }
            }),
            "work.task.updateProject.v2"
        )
    }

    /**
     * 转让项目负责人
     */
    fun transferProject(projectId: String, member: Members, callback: (Boolean, String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["member"] = run {
            member.beanToMap().toMutableMap().apply {
                if (TenantCode.getByDDAppId(member.ddAppId) == null) {
                    remove("app")
                    remove("ddAppId")
                    remove("emplAccount")
                }
            }
        }

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<BaseResp>(BaseResp::class.java) {
                override fun onSuccess(
                    t: BaseResp?,
                    tArray: MutableList<BaseResp>?,
                    rawData: String?
                ) {
                    callback(true, null)
                }

                override fun onFailure(errorMsg: String?) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))

                }
            }),
            "work.task.transferProject.v2"
        )
    }

    /**
     * 获取项目成员列表
     */
    fun getProjectUsers(projectId: String, callback: RepoCallback<ProjectUserList>) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<ProjectUserList>(object :
                AbsReqCallback<ProjectUserList>(ProjectUserList::class.java) {
                override fun onSuccess(
                    jsonObject: ProjectUserList?,
                    tArray: MutableList<ProjectUserList>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.result(jsonObject, null, true)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?) {
                    callback.result(null, JoyWorkEx.filterErrorMsg(errorMsg), false)
                }
            }),
            "work.task.getProjectUsers.v2"
        )
    }

    /**
     * 修改成员权限
     */
    fun changeMemberPermission(
        projectId: String,
        member: Members,
        callback: (Boolean, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["member"] = JSONObject.toJSON(member)

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<BaseResp>(BaseResp::class.java) {
                override fun onSuccess(
                    t: BaseResp?,
                    tArray: MutableList<BaseResp>?,
                    rawData: String?
                ) {
                    callback(true, null)
                }

                override fun onFailure(errorMsg: String?) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))

                }
            }),
            "work.task.changeMemberPermission.v2"
        )
    }

    /**
     * 退出项目
     */
    fun quitProject(projectId: String, callback: (Boolean, String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<BaseResp>(BaseResp::class.java) {
                override fun onSuccess(
                    t: BaseResp?,
                    tArray: MutableList<BaseResp>?,
                    rawData: String?
                ) {
                    callback(true, null)
                }

                override fun onFailure(errorMsg: String?) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))

                }
            }),
            "work.task.quitProject.v2"
        )
    }

    /**
     * 删除项目
     */
    fun deleteProject(projectId: String, callback: (Boolean, String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<BaseResp>(BaseResp::class.java) {
                override fun onSuccess(
                    t: BaseResp?,
                    tArray: MutableList<BaseResp>?,
                    rawData: String?
                ) {
                    callback(true, null)
                }

                override fun onFailure(errorMsg: String?) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))

                }
            }),
            "work.task.deleteProject.v2"
        )
    }

    /**
     * 删除项目成员
     */
    fun delProjectMember(
        projectId: String,
        list: List<Members>,
        callback: (Boolean, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["members"] = ArrayList<Map<String, Any>>().apply {
            val r = list.map {
                val tmp = HashMap<String, Any>()
                tmp["userId"] = it.userId ?: ""
                tmp["teamId"] = it.teamId ?: JoyWorkUser.DEFAULT_TEAM_ID
                if (TenantCode.getByTeamId(it.teamId) != null) {
                    tmp["emplAccount"] = it.emplAccount ?: ""
                    tmp["app"] = it.app ?: ImDdService.APP_ID_JDME_CHINA
                    tmp["ddAppId"] = it.app ?: ImDdService.APP_ID_JDME_CHINA
                }
                tmp["userRole"] = it.userRole
                tmp["headImg"] = it.headImg ?: ""
                tmp["realName"] = it.realName ?: ""
                tmp["permission"] = it.permission
                tmp
            }
            addAll(r)
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object : AbsReqCallback<BaseResp>(BaseResp::class.java) {
                override fun onSuccess(
                    t: BaseResp?,
                    tArray: MutableList<BaseResp>?,
                    rawData: String?
                ) {
                    callback(true, null)
                }

                override fun onFailure(errorMsg: String?) {
                    callback(false, JoyWorkEx.filterErrorMsg(errorMsg))

                }
            }),
            "work.task.deleteProjectMember.v2"
        )
    }


    /**
     * 新建
     */
    fun create(
        callback: (JoyWork?, Boolean, String?) -> Unit,
        config: JoyWorkCreateParams.() -> Unit
    ) {
        val p = JoyWorkCreateParams()
        p.config()
        if (!p.check()) {
            return
        }
        p.postHandle()
        val map = HashMap(p.params)
        p.clear()
        HttpManager.post(
            null,
            map,
            SimpleReqCallbackAdapter<JoyWork>(object :
                AbsReqCallback<JoyWork>(JoyWork::class.java) {
                override fun onFailure(errorMsg: String) {
                    callback.invoke(null, false, JoyWorkEx.filterErrorMsg(errorMsg))
                }

                override fun onFailure(errorMsg: String?, code: Int) {

                }

                override fun onSuccess(
                    jsonObject: JoyWork?,
                    tArray: List<JoyWork?>?,
                    rawData: String
                ) {
                    callback.invoke(jsonObject, jsonObject != null, JoyWorkEx.filterErrorMsg(""))
                }
            }),
            "work.task.clientTaskSave.v2"
        )
    }

    /**
     * 添加项目成员
     */
    fun addProjectMember(
        projectId: String,
        list: List<Members>,
        callback: RepoCallback<ProjectMembersList>
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["members"] = ArrayList<Map<String, Any>>().apply {
            val r = list.map {
                val tmp = HashMap<String, Any>()
                tmp["userId"] = it.userId ?: ""
                tmp["teamId"] = it.app ?: JoyWorkUser.DEFAULT_TEAM_ID
                if (TenantCode.getByDDAppId(it.app) != null) {
                    tmp["emplAccount"] = it.emplAccount ?: ""
                    tmp["app"] = it.app ?: ImDdService.APP_ID_JDME_CHINA
                    tmp["ddAppId"] = it.app ?: ImDdService.APP_ID_JDME_CHINA
                }
                tmp["userRole"] = it.userRole
                tmp["headImg"] = it.headImg ?: ""
                tmp["realName"] = it.realName ?: ""
                tmp["permission"] = it.permission
                tmp
            }
            addAll(r)
        }

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<ProjectMembersList>(object :
                AbsReqCallback<ProjectMembersList>(ProjectMembersList::class.java) {
                override fun onSuccess(
                    jsonObject: ProjectMembersList?,
                    tArray: MutableList<ProjectMembersList>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.result(jsonObject, null, true)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?) {
                    callback.result(null, JoyWorkEx.filterErrorMsg(errorMsg), false)
                }
            }),
            "work.task.addProjectMember.v2"
        )
    }

    /**
     * 获取项目详情
     */
    fun getProjectDetail(projectId: String, callback: AbsRepoCallback<ProjectDetail>) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<ProjectDetail>(object :
                AbsReqCallback<ProjectDetail>(ProjectDetail::class.java) {
                override fun onSuccess(
                    jsonObject: ProjectDetail?,
                    tArray: MutableList<ProjectDetail>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.result(jsonObject, null, true, 1)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?, code: Int) {
                    callback.result(null, JoyWorkEx.filterErrorMsg(errorMsg), false ,code)
                }

            }),
            "work.task.getProjectDetail.v2"
        )
    }

    fun sortGroup(
        projectId: String,
        front: String?,
        after: String?,
        callback: (Boolean, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        front?.apply {
            params["front"] = this
        }

        after?.apply {
            params["after"] = this
        }

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<String>(object : AbsReqCallback<String>(String::class.java) {
                override fun onSuccess(
                    jsonObject: String?,
                    tArray: MutableList<String>?,
                    rawData: String?
                ) {
                    // {"errorCode":"0","content":null,"errorMsg":"success"}
                    val ret = kotlin.runCatching {
                        val objectS = org.json.JSONObject(rawData)
                        if ("0" == objectS.getString("errorCode")) {
                            callback.invoke(true, null)
                        } else {
                            onFailure("")
                        }
                    }
                    if (ret.isFailure) {
                        onFailure("")
                    }
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }
            }),
            "work.task.projectSortUpdate.v2"
        )
    }

    fun refreshMark(taskId: String, callback: (JoyWorkContent2?, String?, Boolean) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["taskId"] = taskId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<JoyWorkContent2>(object :
                AbsReqCallback<JoyWorkContent2>(JoyWorkContent2::class.java) {
                override fun onSuccess(
                    jsonObject: JoyWorkContent2?,
                    tArray: MutableList<JoyWorkContent2>?,
                    rawData: String?
                ) {
                    // {"errorCode":"0","content":null,"errorMsg":"success"}
                    val ret = kotlin.runCatching {
                        val objectS = org.json.JSONObject(rawData)
                        if ("0" == objectS.getString("errorCode") && jsonObject != null) {
                            callback.invoke(jsonObject, null, true)
                        } else {
                            onFailure("")
                        }
                    }
                    if (ret.isFailure) {
                        onFailure("")
                    }
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(null, JoyWorkEx.filterErrorMsg(errorMsg), false)
                }
            }),
            "work.task.getTaskExtInfo.v2"
        )
    }

    /**
     * 更新自定义字段
     */
    fun updateMark(
        columnId: String,
        detailId: String,
        taskId: String,
        content: String,
        callback: (Boolean, String?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["columnId"] = columnId
        params["detailId"] = detailId
        params["taskId"] = taskId
        params["content"] = content
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<String>(object : AbsReqCallback<String>(String::class.java) {
                override fun onSuccess(
                    jsonObject: String?,
                    tArray: MutableList<String>?,
                    rawData: String?
                ) {
                    // {"errorCode":"0","content":null,"errorMsg":"success"}
                    val ret = kotlin.runCatching {
                        val objectS = org.json.JSONObject(rawData)
                        if ("0" == objectS.getString("errorCode")) {
                            callback.invoke(true, null)
                        } else {
                            onFailure("")
                        }
                    }
                    if (ret.isFailure) {
                        onFailure("")
                    }
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }
            }),
            "work.task.setProjectTaskExt.v2"
        )
    }

    fun getProjectExt(projectId: String, callback: (String?, CustomFieldGroupOuter?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<CustomFieldGroupOuter>(object :
                AbsReqCallback<CustomFieldGroupOuter>(CustomFieldGroupOuter::class.java) {
                override fun onSuccess(
                    jsonObject: CustomFieldGroupOuter?,
                    tArray: MutableList<CustomFieldGroupOuter>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.invoke(null, this)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg), null)
                }
            }),
            "work.task.getProjectExtList.v2"
        )
    }

    fun clearProjectExt(
        projectId: String,
        viewId: String,
        type: Int? = null,
        callback: (String?, ProjectViewOuter?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        params["viewId"] = viewId
        if (type != null) {
            params["type"] = type
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ProjectViewOuter>(ProjectViewOuter::class.java) {
                override fun onSuccess(
                    jsonObject: ProjectViewOuter?,
                    tArray: MutableList<ProjectViewOuter>?,
                    rawData: String?
                ) {
                    callback.invoke(null, jsonObject ?: ProjectViewOuter())
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg), null)
                }
            }),
            "work.task.delProjectView.v1"
        )
    }

    fun saveProjectExt(
        projectId: String,
        filterValue: FilterValue?,
        sortValue: SortValue?,
        status: TaskStatusEnum,
        type: Int? = null,
        callback: (String?, ProjectViewOuter?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        val content = hashMapOf<String, Any>()
        if (type != null) {
            params["type"] = type
        }
        content["taskStatus"] = status.code
        if (filterValue != null && filterValue.enum != ProjectFilterEnum.SCREEN_NULL) {
            val map = HashMap<String, Any>()
            filterValue.toMap(map)
            content["filter"] = map
        }
        if (sortValue != null && sortValue.code != ProjectSortEnum.SORT_NULL.code) {
            val map = HashMap<String, Any>()
            map["type"] = sortValue.code
            if (sortValue.value != null) {
                map["value"] = sortValue.value
            }
            content["sort"] = map
        }
        params["content"] = Gson().toJson(content)

        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ProjectViewOuter>(ProjectViewOuter::class.java) {
                override fun onSuccess(
                    jsonObject: ProjectViewOuter?,
                    tArray: MutableList<ProjectViewOuter>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.invoke(null, this)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg), null)
                }
            }),
            "work.task.saveProjectView.v1"
        )
    }

    fun getProjectView(
        projectId: String,
        type: Int?,
        callback: (String?, ProjectViewOuter?) -> Unit
    ) {
        val params = hashMapOf<String, Any>()
        params["projectId"] = projectId
        if (type != null) {
            params["type"] = type
        }
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter(object :
                AbsReqCallback<ProjectViewOuter>(ProjectViewOuter::class.java) {
                override fun onSuccess(
                    jsonObject: ProjectViewOuter?,
                    tArray: MutableList<ProjectViewOuter>?,
                    rawData: String?
                ) {
                    jsonObject?.apply {
                        callback.invoke(null, this)
                    } ?: onFailure("")
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(JoyWorkEx.filterErrorMsg(errorMsg), null)
                }
            }),
            "work.task.getProjectView.v1"
        )
    }

    /**
     * 删除自定义字段
     */
    fun delMark(columnId: String, taskId: String, callback: (Boolean, String?) -> Unit) {
        val params = hashMapOf<String, Any>()
        params["columnId"] = columnId
        params["taskId"] = taskId
        HttpManager.post(
            null,
            params,
            SimpleReqCallbackAdapter<String>(object : AbsReqCallback<String>(String::class.java) {
                override fun onSuccess(
                    jsonObject: String?,
                    tArray: MutableList<String>?,
                    rawData: String?
                ) {
                    // {"errorCode":"0","content":null,"errorMsg":"success"}
                    val ret = kotlin.runCatching {
                        val objectS = org.json.JSONObject(rawData)
                        if ("0" == objectS.getString("errorCode")) {
                            callback.invoke(true, null)
                        } else {
                            onFailure("")
                        }
                    }
                    if (ret.isFailure) {
                        onFailure("")
                    }
                }

                override fun onFailure(errorMsg: String?) {
                    callback.invoke(false, JoyWorkEx.filterErrorMsg(errorMsg))
                }
            }),
            "work.task.deleteProjectTaskExt.v2"
        )
    }
}

fun Any.beanToMap(): Map<String, Any?> {
    // 仅当前类的属性，不包括继承类的属性
    return this::class.declaredMemberProperties.associate { prop ->
        // 允许访问私有属性
        prop.isAccessible = true
        prop.name to prop.getter.call(this)
    }
}

/**
 * 获取清单配置
 */
suspend fun getProjectExt(projectId: String): CustomFieldGroupOuter {
    return suspendCancellableCoroutine { c ->
        ProjectRepo.getProjectExt(projectId) { msg: String?, outer: CustomFieldGroupOuter? ->
            val result = outer ?: CustomFieldGroupOuter()
            c.resumeWith(Result.success(result))
        }
    }
}

/**
 * 获取清单配置
 */
suspend fun clearProjectExt(projectId: String, viewId: String): ProjectViewOuter {
    return suspendCancellableCoroutine { c ->
        ProjectRepo.clearProjectExt(
            projectId,
            viewId
        ) { msg: String?, outer: ProjectViewOuter? ->
            val result = outer ?: ProjectViewOuter()
            c.resumeWith(Result.success(result))
        }
    }
}

/**
 * 获取清单配置
 */
suspend fun saveProjectExt(
    projectId: String,
    filterValue: FilterValue,
    sortValue: SortValue,
    status: TaskStatusEnum
): ProjectViewOuter {
    return suspendCancellableCoroutine { c ->
        ProjectRepo.saveProjectExt(
            projectId,
            filterValue,
            sortValue,
            status
        ) { msg: String?, outer: ProjectViewOuter? ->
            val result = outer ?: ProjectViewOuter()
            c.resumeWith(Result.success(result))
        }
    }
}


/**
 * 获取清单保存的设置
 */
suspend fun getProjectSetting(projectId: String, type: Int? = null): ProjectViewOuter {
    return suspendCancellableCoroutine { c ->
        ProjectRepo.getProjectView(projectId, type) { msg: String?, outer: ProjectViewOuter? ->
            val result = outer ?: ProjectViewOuter()
            c.resumeWith(Result.success(result))
        }
    }
}

interface RepoCallback<T> {
    /**
     * [success] 为 true 是 [t] 一般不为 null，但尽量判断下
     * [success] 为 false 时, [errorMsg] 一般不为 null，但尽量判断下
     */
    fun result(t: T?, errorMsg: String?, success: Boolean)
}

open class AbsRepoCallback<T> : RepoCallback<T> {
    final override fun result(t: T?, errorMsg: String?, success: Boolean) {

    }

    /**
     * [success] 为 true 是 [t] 一般不为 null，但尽量判断下
     * [success] 为 false 时, [errorMsg] 与 [errorCode] 一般不为 null，但尽量判断下
     */
    open fun result(t: T?, errorMsg: String?, success: Boolean, errorCode: Int?) {
        result(t, errorMsg, success)
    }
}

abstract class JoyWorkProjectCallback {
    open fun call(wrapper: JoyWorkProjectList) {

    }

    open fun call(wrapper: JoyWorkProjectList, rawData: String) {

    }

    abstract fun onError(msg: String)
}
