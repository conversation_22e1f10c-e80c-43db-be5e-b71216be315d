package com.jd.oa.joywork.utils.starter

import android.content.Intent
import java.lang.ref.WeakReference
import java.util.UUID

/**
 * 背景：A 启动 B，B 会根据 A 的参数进行业务逻辑。随着业务的扩展，B 的入口会越来越多，要处理的逻辑会越来越复杂。
 *      因此 B 将需要根据参数处理的逻辑抽取成接口，A 启动 B 时，将接口实例传递给 B。
 * 问题：
 *      如果接口实例比较大通过 Intent 传输时会出异常。由于本身 A，B 都在同一进程，所以此处可以使用本地直接读取方式
 */
object LocalStarterDataStore {

    private val mData = HashMap<String, WeakReference<Any>>()
    fun store(any: Any): String {
        // 通过 UUID 生成 key，存储至 mData 中，并返回
        val key = UUID.randomUUID().toString()
        mData.put(key, WeakReference(any))
        return key
    }

    fun remove(key: String): Any? {
        return mData.remove(key)?.get()
    }
}

object LocalStarter {
    fun wrap(any: Any, intent: Intent): Intent {
        val key = LocalStarterDataStore.store(any)
        intent.putExtra("LocalStarter::default_key_${System.identityHashCode(intent)}", key)
        return intent
    }

    fun <T> get(intent: Intent): T? {
        val key =
            intent.getStringExtra("LocalStarter::default_key_${System.identityHashCode(intent)}")
                ?: return null
        return (LocalStarterDataStore.remove(key) as? T)
    }
}
