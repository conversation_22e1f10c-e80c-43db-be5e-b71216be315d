package com.jd.oa.joywork.filter

import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.text.Html
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.chenenyu.router.Router
import com.google.gson.reflect.TypeToken
import com.jd.oa.AppBase
import com.jd.oa.basic.ImBasic
import com.jd.oa.joywork.ExtendFactory
import com.jd.oa.joywork.JoyWorkAction
import com.jd.oa.joywork.JoyWorkLevel
import com.jd.oa.joywork.JoyWorkReadStatus
import com.jd.oa.joywork.ListFinishAction
import com.jd.oa.joywork.R
import com.jd.oa.joywork.RiskEnum
import com.jd.oa.joywork.TaskStatusEnum
import com.jd.oa.joywork.bean.JoyWork
import com.jd.oa.joywork.bean.Tag
import com.jd.oa.joywork.dialog.thirdparty.ShitAlertDialog
import com.jd.oa.joywork.dialog.thirdparty.ShitAlertDialogConfig
import com.jd.oa.joywork.isChief
import com.jd.oa.joywork.isLegalList
import com.jd.oa.joywork.isLegalTimestamp
import com.jd.oa.joywork.isSelf
import com.jd.oa.joywork.joinLegalString
import com.jd.oa.joywork.repo.JoyWorkRepo
import com.jd.oa.joywork.repo.JoyWorkUpdateCallback
import com.jd.oa.joywork.self.base.adapter.vh.ItemVHItf
import com.jd.oa.joywork.team.showFinishAlertDialog
import com.jd.oa.joywork.utils.getTaskCompleteTimeString
import com.jd.oa.joywork.utils.getTaskDeadlineString
import com.jd.oa.joywork.utils.getTaskDeadlineStringWithoutDue
import com.jd.oa.joywork.utils.getTaskPlanTimeString
import com.jd.oa.joywork.utils.getTaskStartTimeString
import com.jd.oa.joywork.utils.getTimeStringWithoutSuffix
import com.jd.oa.joywork.view.JoyWorkAvatarView
import com.jd.oa.joywork.view.JoyWorkAvatarViewCallback
import com.jd.oa.joywork.view.swipe.SwipeMenuLayout
import com.jd.oa.joywork2.list.calendar.view.HorizontalLabelLayout
import com.jd.oa.ui.IconFontView
import com.jd.oa.utils.CommonUtils
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.DrawableEx
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.allChildrenGone
import com.jd.oa.utils.color
import com.jd.oa.utils.gone
import com.jd.oa.utils.inflater
import com.jd.oa.utils.invisible
import com.jd.oa.utils.strikethrough
import com.jd.oa.utils.visible
import java.util.Locale




/**
 * 根据 [JoyWork] 对象处理要显示的内容
 */
object JoyWorkViewItem {

    fun avatarView(
        joyWork: JoyWork,
        avatarView: JoyWorkAvatarView,
        childItf: ItemVHItf?,
        iconController: ((JoyWork, Int) -> Boolean)? = null,
        nameGen: (JoyWork) -> String
    ): JoyWorkViewItem {
        val show = childItf?.showAvatar() ?: true
        if (!show || !joyWork.owners.isLegalList()) {
            avatarView.gone()
        } else {
            avatarView.visible()
            avatarView.mCallback = object : JoyWorkAvatarViewCallback() {
                override fun getHintText2(
                    urls: List<String>,
                    context: Context,
                    type: Int
                ): Pair<String?, String?> {
                    return Pair(nameGen(joyWork), null)
                }

                override fun needHat(
                    url: String,
                    position: Int,
                    itemView: ImageView,
                    parent: ViewGroup
                ): Boolean {
                    return position == 0 && joyWork.owners.firstOrNull()?.chief.isChief()
                }

                override fun needIcon(
                    url: String,
                    position: Int,
                    itemView: ImageView,
                    parent: ViewGroup
                ): Boolean {
                    return iconController?.invoke(joyWork, position) ?: false
                }
            }
            avatarView.setUrls(joyWork.owners.map {
                it.headImg
            })
        }
        return this
    }

    fun creatorAvatarView(
        joyWork: JoyWork,
        avatarView: JoyWorkAvatarView,
        childItf: ItemVHItf?,
        iconController: ((JoyWork, Int) -> Boolean)? = null,
        nameGen: (JoyWork) -> String
    ): JoyWorkViewItem {
        val show = childItf?.showAvatar() ?: true
        if (!show || joyWork.assigner == null || joyWork.assigner.headImg.isNullOrEmpty()) {
            avatarView.gone()
        } else {
            avatarView.visible()
            avatarView.mCallback = object : JoyWorkAvatarViewCallback() {
                override fun getHintText2(
                    urls: List<String>,
                    context: Context,
                    type: Int
                ): Pair<String?, String?> {
                    return Pair(nameGen(joyWork), null)
                }

                override fun needHat(
                    url: String,
                    position: Int,
                    itemView: ImageView,
                    parent: ViewGroup
                ): Boolean {
                    return false
                }

                override fun needIcon(
                    url: String,
                    position: Int,
                    itemView: ImageView,
                    parent: ViewGroup
                ): Boolean {
                    return iconController?.invoke(joyWork, position) ?: false
                }
            }
            avatarView.setUrls(listOf(joyWork.assigner.headImg))
            avatarView.setOnClickListener {
                ImBasic.openContactInfo(
                    avatarView.context,
                    joyWork.assigner.ddAppId ?: "ee",
                    joyWork.assigner.emplAccount
                )
            }
        }
        return this
    }

    fun from(joyWork: JoyWork, fromView: TextView): JoyWorkViewItem {
        if (joyWork.sourceName.isNullOrEmpty()) {
            fromView.gone()
        } else {
            fromView.visible()
            fromView.text = joyWork.sourceName
        }
        return this
    }

    // 处理优先级
    fun level(task: JoyWork, levelView: View): JoyWorkViewItem {
        // 优先级
        levelView.background =
            DrawableEx.roundSolidRect(Color.parseColor("#1FFE3B30"), CommonUtils.dp2FloatPx(2))
        if (3 == (task.priorityType ?: 0)) {
            levelView.visible()
        } else {
            levelView.gone()
        }
        return this
    }

    /**
     * 当【完成】/【删除】时，是否显示上面的一层蒙版
     */
    fun cover(joyWork: JoyWork, coverView: View, show: Boolean): JoyWorkViewItem {
        if (show) {
            coverView.visible()
        } else {
            coverView.gone()
        }
        return this
    }

    /**
     * 处理扩展字段
     */
    fun extend(task: JoyWork, lv: LinearLayout): JoyWorkViewItem {
        lv.removeAllViews()
        task.extendObj?.forEachIndexed { index, it ->
            val v = ExtendFactory.createView(it, lv.context)
            if (v != null) {
                val params = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                if (index != 0) {
                    params.leftMargin = CommonUtils.dp2px(14.0f);
                }
                lv.addView(v, lv.childCount, params);
            }
        }
        if (lv.childCount == 0) {
            lv.gone()
        } else {
            lv.visible()
        }
        return this;
    }

    /**
     * 标题
     */
    fun title(task: JoyWork, title: TextView): JoyWorkViewItem {
        title.text = task.title ?: ""
        title.strikethrough(task.isUIFinish)
        return this
    }

    /**
     * 子待办
     */
    fun childWorks(
        task: JoyWork,
        progress: TextView,
        progressIcon: View,
        container: ViewGroup
    ): JoyWorkViewItem {
        if (task.childWorks == null || task.childWorks.isEmpty()) {
            progress.gone()
            progressIcon.gone()
            container.gone()
        } else {
            val count = task.childWorks.count {
                it.isFinish
            }
            container.visible()
            progress.visible()
            progressIcon.visible()
            progress.text = "$count/${task.childWorks.size}"
        }
        return this
    }

    /**
     * 只按截止时间显示截止时间。后面带《截止》二字
     */
    fun deadlineByEndTime(task: JoyWork, deadline: TextView): JoyWorkViewItem {
        if (task.isEndDatetimeLegal) {
            deadline.visible()
            val time = task.endTime
            if (task.isUIFinish || time > System.currentTimeMillis()) { // 已完成的 或者 还未到截止时间的
                deadline.setTextColor(Color.parseColor("#999999"));
            } else {
                deadline.setTextColor(deadline.context.color(R.color.joywork_red));
            }
            deadline.text = getTaskDeadlineString(time, deadline.context.resources);
        } else {
            deadline.gone()
        }
        return this
    }

    /**
     * 按启动时间、开始时间、截止时间依次寻找
     */
    fun deadline3(task: JoyWork, deadline: TextView, deadlineIcon: TextView): JoyWorkViewItem {
        val time = DateUtils.getSpecialHour(
            System.currentTimeMillis() + 24 * 60 * 60 * 1000,
            0
        )
        if (task.planTime.isLegalTimestamp() && task.planTime < time) {
            // 如果启动时间在今天内，按启动时间显示
            setDeadlineView(
                deadline,
                deadlineIcon,
                getTaskPlanTimeString(task.planTime, deadline.context.resources)
            )
            return this
        } else if (task.startTime.isLegalTimestamp() && task.startTime < time) {
            // 如果开始时间在今天内，按启动时间显示
            setDeadlineView(
                deadline,
                deadlineIcon,
                getTaskStartTimeString(task.startTime, deadline.context.resources)
            )
            return this
        }
        deadlineByEndTime(task, deadline)
        if (deadline.visibility == View.VISIBLE) {
            deadline.setTextColor(Color.parseColor("#999999"))
            deadlineIcon.visible()
            deadlineIcon.setTextColor(Color.parseColor("#999999"))
        } else {
            deadlineIcon.gone()
        }
        return this
    }

    /**
     * 按启动时间、开始时间、截止时间顺序查找，有哪个显示哪个
     */
    fun deadline4(task: JoyWork, deadline: TextView, deadlineIcon: TextView): JoyWorkViewItem {
        if (task.planTime.isLegalTimestamp()) {
            setDeadlineView(
                deadline,
                deadlineIcon,
                getTaskPlanTimeString(task.planTime, deadline.context.resources)
            )
            return this
        } else if (task.startTime.isLegalTimestamp()) {
            setDeadlineView(
                deadline,
                deadlineIcon,
                getTaskStartTimeString(task.startTime, deadline.context.resources)
            )
            return this
        }
        deadlineByEndTime(task, deadline)
        if (deadline.visibility == View.VISIBLE) {
            deadline.setTextColor(Color.parseColor("#999999"))
            deadlineIcon.visible()
            deadlineIcon.setTextColor(Color.parseColor("#999999"))
        } else {
            deadlineIcon.gone()
        }
        return this
    }

    private fun setDeadlineView(deadline: TextView, deadlineIcon: TextView, text: String) {
        deadlineIcon.visible()
        deadlineIcon.setTextColor(Color.parseColor("#999999"))
        deadline.visible()
        deadline.text = text
        deadline.setTextColor(Color.parseColor("#999999"))
    }

    /**
     * 截止时间。
     */
    fun deadline(task: JoyWork, deadline: TextView): JoyWorkViewItem {
        if (task.isEndDatetimeLegal) {
            deadline.visible()
            val time = task.endTime
            if (task.isUIFinish || time > System.currentTimeMillis()) { // 已完成的 或者 还未到截止时间的
                deadline.setTextColor(Color.parseColor("#999999"));
            } else {
                deadline.setTextColor(deadline.context.color(R.color.joywork_red));
            }
            deadline.text = getTaskDeadlineString(time, deadline.context.resources);
        } else {
            deadline.gone()
        }
        return this
    }

    /**
     * 截止时间的图标
     */
    fun deadlineIcon(task: JoyWork, deadline: IconFontView): JoyWorkViewItem {
        if (task.isEndDatetimeLegal) {
            deadline.visible()
            val time = task.endTime
            if (task.isUIFinish || time > System.currentTimeMillis()) { // 已完成的 或者 还未到截止时间的
                deadline.setTextColor(Color.parseColor("#999999"));
            } else {
                deadline.setTextColor(deadline.context.color(R.color.joywork_red));
            }
        } else {
            deadline.gone()
        }
        return this
    }

    fun finishTime(task: JoyWork, deadline: TextView, defaultV: View): JoyWorkViewItem {
        if (task.completeTime.isLegalTimestamp()) {
            deadline.visible()
            defaultV.gone()
            val time = task.completeTime
            deadline.setTextColor(Color.parseColor("#8F959E"));
            deadline.text = getTaskCompleteTimeString(time, deadline.context.resources);
        } else {
            deadline.gone()
            defaultV.visible()
        }
        return this
    }

    fun startTime(task: JoyWork, start: TextView, defaultV: View): JoyWorkViewItem {
        if (task.startTime.isLegalTimestamp()) {
            start.visible()
            defaultV.gone()
            val time = task.startTime
            start.setTextColor(Color.parseColor("#8F959E"));
            start.text = getTaskStartTimeString(time, start.context.resources);
        } else {
            start.gone()
            defaultV.visible()
        }
        return this
    }


    fun startTimeWithoutSuffix(task: JoyWork, start: TextView, defaultV: View): JoyWorkViewItem {
        if (task.startTime.isLegalTimestamp()) {
            start.visible()
            defaultV.gone()
            val time = task.startTime
            start.setTextColor(Color.parseColor("#8F959E"));
            start.text = getTimeStringWithoutSuffix(time, start.context.resources);
        } else {
            start.gone()
            defaultV.visible()
        }
        return this
    }

    fun createTimeWithoutSuffix(task: JoyWork, create: TextView, defaultV: View): JoyWorkViewItem {
        if (task.gmtCreate.isLegalTimestamp()) {
            create.visible()
            defaultV.gone()
            val time = task.gmtCreate
            create.setTextColor(Color.parseColor("#8F959E"))
            create.text = getTimeStringWithoutSuffix(time, create.context.resources);
        } else {
            create.gone()
            defaultV.visible()
        }
        return this
    }

    fun projectNames(joyWork: JoyWork, textView: TextView): JoyWorkViewItem {
        val ps = joyWork.projects?.map {
            it.title
        }?.joinLegalString(",") ?: ""
        textView.text = ps
        return this
    }

    /**
     * 截止时间
     */
    fun deadline(task: JoyWork, deadline: TextView, defaultV: View): JoyWorkViewItem {
        if (task.isEndDatetimeLegal && task.endTime.isLegalTimestamp()) {
            deadline.visible()
            defaultV.gone()
            val time = task.endTime
            if (task.isUIFinish || time > System.currentTimeMillis()) { // 已完成的 或者 还未到截止时间的
                deadline.setTextColor(Color.parseColor("#8F959E"));
            } else {
                deadline.setTextColor(deadline.context.color(R.color.joywork_red));
            }
            deadline.text = getTaskDeadlineStringWithoutDue(time, deadline.context.resources);
        } else {
            deadline.gone()
            defaultV.visible()
        }
        return this
    }

    fun priority(task: JoyWork, deadline: TextView, view: View?) {
        val level = JoyWorkLevel.NO.getLevel(task.priorityType ?: JoyWorkLevel.NO.value)
        if (level == JoyWorkLevel.NO && view != null) {
            deadline.gone()
            view.visible()
        } else {
            deadline.visible()
            view?.gone()
            deadline.setTextColor(level.getColor())
            deadline.setText(level.getResId())
            deadline.background = DrawableEx.roundSolidRect(
                level.getBgColor(),
                CommonUtils.dp2FloatPx(4)
            )
        }
    }

    /**
     * 清单文字
     */
    fun projectText(task: JoyWork, projectText: TextView, projectIcon: View): JoyWorkViewItem {
        if (task.projects.isLegalList()) {
            projectText.visible()
            projectIcon.visible()
            projectText.text = projectText.context.getString(
                R.string.joywork_self_item_project,
                task.projects.first().title
            )
        } else {
            projectText.gone()
            projectIcon.gone()
        }
        return this
    }


    fun risk(task: JoyWork, risk: TextView): JoyWorkViewItem {
        when (val riskEnum = RiskEnum.valueByCode(task.riskStatus)) {
            RiskEnum.NORMAL,
            RiskEnum.NO_SET -> {
                risk.gone()
            }

            else -> {
                risk.visible()
                risk.setText(riskEnum.resId)
                risk.setTextColor(riskEnum.textColor())
                risk.background =
                    DrawableEx.roundSolidRect(riskEnum.bgColor(), CommonUtils.dp2FloatPx(2))
            }
        }
        return this
    }

    /**
     * 重复图标
     */
    fun duplicateIcon(task: JoyWork, depView: IconFontView): JoyWorkViewItem {
        if (task.isDup) {
            depView.visible()
            val time = task.endTime
            if (time < System.currentTimeMillis()) {
                depView.setTextColor(depView.context.color(R.color.joywork_red));
            } else {
                depView.setTextColor(Color.parseColor("#62656D"));
            }
        } else {
            depView.gone()
        }
        return this
    }

    fun deadlineDupView(task: JoyWork, container: ViewGroup, vh: View?): JoyWorkViewItem {
        // 2022年03月01日：当前产品逻辑为：没有截止时间就不会有重复待办
        if (container.allChildrenGone()) {
            container.gone()
            vh?.visible()
        } else {
            container.visible()
            vh?.gone()
        }
        return this
    }

    /**
     * 侧滑操作
     */
    fun action(
        task: JoyWork,
        parent: LinearLayout,
        actions: List<JoyWorkAction>,
        click: (JoyWorkAction, JoyWork) -> Unit
    ): JoyWorkViewItem {
        parent.removeAllViews()
        if (actions.isEmpty()) {
            parent.gone()
            parent.requestLayout()
        } else {
            parent.visible()
            actions.forEach { a ->
                val view =
                    parent.context.inflater.inflate(R.layout.joywork_list_action, parent, false)
                val tv = view.findViewById<TextView>(R.id.action_text)
                tv.text = a.action
                tv.setBackgroundColor(a.bgColor())
                tv.tag = a
                tv.setOnClickListener(SwipeMenuLayout.AutoCloseClickListener(View.OnClickListener {
                    val tmp = view.tag as JoyWorkAction
                    click.invoke(tmp, task)
                }))
                parent.addView(view)
            }
        }
        return this
    }

    /**
     * @param [showOwner] 是否显示负责人头像。false 时会显示来源人头像
     *
     * **产品逻辑**：我负责的显示来源人头像，如果来源人是自己，则不显示头像。我指派的、我配合的显示负责人头像。筛选结果页与当前所处卡片一致
     */
    fun avatar(task: JoyWork, avatar: ImageView, showOwner: Boolean): JoyWorkViewItem {
        if (showOwner) {
            avatar.visible()
            val url = task.owner?.headImg
            val options = RequestOptions().placeholder(R.drawable.joywork_default_avatar)
                .error(R.drawable.joywork_default_avatar)
            Glide.with(avatar).applyDefaultRequestOptions(options).load(url).into(avatar)
        } else {
            if (task.assigner == null || task.assigner.isSelf()) {
                avatar.gone()
            } else {
                avatar.visible()
                val url = task.assigner?.headImg
                val options = RequestOptions().placeholder(R.drawable.joywork_default_avatar)
                    .error(R.drawable.joywork_default_avatar)
                Glide.with(avatar).applyDefaultRequestOptions(options).load(url).into(avatar)
            }
        }
        return this
    }

    fun checkboxNew(
        task: JoyWork,
        cb: TextView,
        cb2: ImageView,
        click: View? = null,
        clickListener: ((joyWork: JoyWork, cb: View) -> Unit)? = null
    ): JoyWorkViewItem {
        val clickView = click ?: if (task.merged == true || task.isThirdParty) cb else cb2
        clickView.tag = task
        if (clickListener != null) {
            clickView.setOnClickListener {
                if (it.isEnabled) {
                    clickListener((it.tag as JoyWork), it)
                }
            }
        }
        if (task.merged == true) {
            // 被合并的待办
            cb2.gone()
            cb.visible()
            cb.setText(R.string.icon_general_merge)
            cb.setTextColor(Color.parseColor("#BFC1C4"))
            return this
        }
        if (task.isThirdParty) {
            // 第三方
            cb2.gone()
            cb.visible()
            cb.setText(R.string.icon_general_control)
            cb.setTextColor(Color.parseColor("#BFC1C4"))
            return this
        }
        cb.gone()
        cb2.visible()
        if (task.isUIFinish) {
            cb2.isSelected = true
            cb2.isEnabled = task.finishAction != ListFinishAction.NOT_ALLOW.code
            return this
        }
        // 剩余表示未完成的待办，需要判断是否能完成又分为可完成、不可完成
        cb2.isSelected = false
        if (task.isCompletable && task.finishAction != ListFinishAction.NOT_ALLOW.code) {
            cb2.isEnabled = !task.isFinishing
            return this
        }
        cbUnavailable(cb2)
        return this
    }

    fun cbUnavailable(cb: ImageView) {
        // 不可完成
        cb.isEnabled = false
    }

    fun finishAction(work: JoyWork, context: Context, callback: JoyWorkUpdateCallback) {
        if (work.isThirdParty) {
            showThirdDialog(work, context) {
                callback.result(false, "")
            }
            return
        }
        when (ListFinishAction.getValueByCode(work.finishAction)) {
            ListFinishAction.NOT_ALLOW -> return
            ListFinishAction.NORMAL_NO_BOX -> {
                finishNet(work, null, false, callback)
            }

            ListFinishAction.NORMAL_DESC_BOX -> {
                // 普通完成，完成信息输入框。只是由未完成变成已完成时才有需要
                finishNet(work, null, false, callback)
            }

            ListFinishAction.FORCE_NO_BOX -> {
                // 强制完成，不弹窗
                finishNet(work, null, true, callback)
            }

            ListFinishAction.FORCE_ALL_BOX -> {
                if (!work.assigner.isSelf() && !work.isUIFinish) {
                    showFinishAlertDialog(context, isFinish = true, isAll = true) {
                        finishNet(work, null, true, callback)
                    }
                } else {
                    showFinishAlertDialog(context, !work.isUIFinish, true) {
                        finishNet(work, null, true, callback)
                    }
                }
            }

            ListFinishAction.FORCE_TASK_BOX -> {
                if (!work.assigner.isSelf() && !work.isUIFinish) {
                    finishNet(work, null, true, callback)
                } else {
                    showFinishAlertDialog(context, !work.isUIFinish, false) {
                        finishNet(work, null, true, callback)
                    }
                }
            }
        }
    }

    private fun finishNet(
        work: JoyWork,
        info: String?,
        force: Boolean,
        callback: JoyWorkUpdateCallback
    ) {
        JoyWorkRepo.updateTaskStatusNew(
            work.taskId,
            if (work.isUIFinish) TaskStatusEnum.UN_FINISH else TaskStatusEnum.FINISH,
            info,
            force,
            callback
        )
    }

    fun divider(view: View, show: Boolean): JoyWorkViewItem {
        if (show) {
            view.visible()
        } else {
            view.invisible()
        }
        return this
    }

    /**
     * 提醒按钮
     */
    fun alertIcon(view: View, task: JoyWork): JoyWorkViewItem {
        task.remindTime?.run {
            view.visible()
        } ?: view.gone()
        return this
    }

    /**
     * 阅读状态
     */
    fun readStatus(view: TextView, task: Int?): JoyWorkViewItem {
        when (task) {
            JoyWorkReadStatus.UNREAD.v -> {
                view.visible()
                view.setText(R.string.joywork_unread)
                view.setTextColor(view.resources.getColor(R.color.title_sub))
                view.setTextColor(Color.parseColor("#FF4C7CFF"))
            }

            JoyWorkReadStatus.READ.v -> {
                view.visible()
                view.setText(R.string.joywork_read)
                view.setTextColor(view.resources.getColor(R.color.title_sub))
            }

            else -> {
                view.gone()
            }
        }
        return this
    }


    /**
     * 该方法在所有方法之后调用。如果其父 View 中除 view 外所有 View 都 gone，那么 view 也会 gone
     * 同时父 View 也会 gone
     */
    fun placeholder(view: View) {
        val ret = countLayoutView(view.parent as ViewGroup, view)
        if (ret == 0) {
            view.gone()
            (view.parent as ViewGroup).gone()
        } else {
            (view.parent as ViewGroup).visible()
            view.invisible()
        }
    }

    private fun countLayoutView(parent: ViewGroup, view: View): Int {
        var ret = 0
        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            if (child != view) {
                ret += if (child is ViewGroup) {
                    if (child.visibility == View.GONE) {
                        0
                    } else {
                        countLayoutView(child, view)
                    }
                } else {
                    if (child.visibility == View.GONE) 0 else 1
                }
            }
        }
        return ret
    }

    /**
     * 当来自第三方时，弹窗
     */
    private fun showThirdDialog(
        joyWork: JoyWork,
        context: Context,
        callback: ((Boolean) -> Unit)?
    ) {
        showThirdDialog(joyWork.mobileContent, joyWork.sourceName, context, callback)
    }

    fun showThirdDialog(
        deeplink: String?,
        sourceName: String?,
        context: Context,
        callback: ((Boolean) -> Unit)?
    ) {
        if (!TextUtils.isEmpty(deeplink)) {
            val msg = context.resources.getString(
                R.string.joywork_list_from2, sourceName
                    ?: ""
            )
            // 有 deeplink
            val dialog = ShitAlertDialog(context, ShitAlertDialogConfig<ShitAlertDialog>().apply {
                this.okShitClick = {
                    Router.build(deeplink).go(AppBase.getTopActivity())
                    it.dismiss()
                    callback?.invoke(true)
                }
                this.cancelShitClick = { _, _ ->
                    callback?.invoke(false)
                }
                msgSpanner = Html.fromHtml(msg)
                okRes = R.string.joywork_list_go_third
                showCancel = true
            })
            dialog.show()
        } else {
            val msg = context.resources.getString(
                R.string.joywork_list_from, sourceName
                    ?: ""
            )
            val dialog = ShitAlertDialog(context, ShitAlertDialogConfig<ShitAlertDialog>().apply {
                this.okShitClick = {
                    it.dismiss()
                    callback?.invoke(false)
                }
                msgSpanner = Html.fromHtml(msg)
                okRes = R.string.joywork_dialog_ok
                showCancel = false
            })
            dialog.show()
        }
    }

    /**
     * 显示头像
     */
    fun avatar(avatar: ImageView, url: String?) {
        img(avatar, url, R.drawable.joywork_default_avatar)
    }

    /**
     * 显示头像
     */
    fun img(avatar: ImageView, url: String?, dd: Int) {
        val options = RequestOptions().placeholder(dd)
            .error(dd)
        Glide.with(avatar).applyDefaultRequestOptions(options).load(url).into(avatar)
    }

    /**
     * 加载待办标签【风险、问题、稍后处理等】
     */
    fun loadLabel(task: JoyWork, labelContainer: HorizontalLabelLayout): JoyWorkViewItem {
        val allTags: MutableList<Tag> = mutableListOf()
        val riskEnum = RiskEnum.valueByCode(task.riskStatus)
        if (riskEnum == RiskEnum.RISK || riskEnum == RiskEnum.PROBLEM) {
            val riskTag = Tag(
                "#FFF63218",
                getStringByLocale(labelContainer.context, riskEnum.resId, Locale.ENGLISH),
                getStringByLocale(labelContainer.context, riskEnum.resId, Locale.CHINESE)
            )
            allTags.add(riskTag)
        }
        if (task.tags != null) {
            val type = TypeToken.getParameterized(List::class.java, Tag::class.java).type
            val tags = JsonUtils.getGson().fromJson<List<Tag>>(task.tags, type)
            allTags.addAll(tags)
        }
        labelContainer.setLabels(allTags)
        labelContainer.isVisible = allTags.isNotEmpty()
        return this
    }

    /**
     * 根据当前语言环境获取 stringId 对应的文本
     */
    private fun getStringByLocale(context: Context, resId: Int, locale: Locale?): String {
        val resources = context.resources
        val config = Configuration(resources.configuration)
        config.setLocale(locale)
        return context.createConfigurationContext(config).getText(resId).toString()
    }

}